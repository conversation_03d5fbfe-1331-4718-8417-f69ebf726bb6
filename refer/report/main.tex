\documentclass[11pt]{article}
\usepackage{geometry}
\usepackage{amsmath}
\usepackage{amssymb}
\usepackage{enumitem}
\usepackage[dvipsnames]{xcolor}
\usepackage{fancyhdr}
\usepackage{tikz}
\usepackage[style=phys,articletitle=true,biblabel=brackets]{biblatex}
\addbibresource{cite.bib} % 指定参考文献文件名
\usepackage{subcaption} % 用于子图标注

\geometry{a4paper, top=1.5cm, bottom=1.5cm, left=1.5cm, right=1.5cm}
\setlength{\parskip}{0.5em}

\definecolor{myblue}{RGB}{70,130,180}
\definecolor{myred}{RGB}{178,34,34}

\pagestyle{fancy}
\fancyhf{}
\fancyhead[L]{Shastry-Sutherland Model Research}
\fancyhead[R]{Yu Shenlong}
\fancyfoot[C]{\thepage}
\renewcommand{\headrulewidth}{0.4pt}

\title{\textbf{\textcolor{myblue}{Student Progress Report}}\\Quantum Phase Transitions in the Shastry-Sutherland Model with four-spin interactions}
\author{<PERSON>\\Nanyang Technological University}
\date{}

\begin{document}

\maketitle

\section{\textcolor{myblue}{Overview}}

I'm investigating quantum phase transitions in the Shastry-Sutherland model with four-spin interactions, focusing on the potential deconfined quantum criticality. This phenomenon challenges the Landau paradigm of phase transitions by allowing a direct continuous transition between two ordered phases breaking different symmetries.

Using neural network quantum states (NQS) as computational tools, I have performed large-scale quantum many-body simulations to analyze correlation functions, structure factors, and critical exponents. 

This work can contribute to our understanding of exotic quantum phase transitions in frustrated magnetic systems and connects to experimental realizations in materials like SrCu$_2$(BO$_3$)$_2$.

\section{\textcolor{myblue}{Introduction}}

The Shastry-Sutherland model is a paradigmatic quantum spin system characterized by a distinctive pattern of exchange interactions:
\begin{align}
\hat{H} = J \sum_{\langle r,r' \rangle} \vec{S}_r \cdot \vec{S}_{r'} + J' \sum_{\langle\langle r,r' \rangle\rangle} \vec{S}_r \cdot \vec{S}_{r'}
\end{align}
Here, $J$ represents nearest-neighbor couplings (solid lines in Figure \ref{fig:lattice}), while $J'$ represents diagonal couplings forming orthogonal dimers (dashed lines). 

\begin{figure}[h]
    \centering
    \includegraphics[width=0.2\linewidth]{lattice.png}
    \caption{Shastry-Sutherland Lattice \cite{article}.}
    \label{fig:lattice}
\end{figure}



\subsection{Phase Diagram of Shastry-Sutherland model}

The Shastry-Sutherland model has been extensively studied, and its phase distribution is shown in Figure \ref{fig:phase}, where there are some disagreements regarding the specific values of the parameters \cite{viteritti2024transformer}.

\begin{figure}
    \centering
    \includegraphics[width=0.5\linewidth]{phase.png}
    \caption{Phase diagram of Shastry-Sutherland model \cite{corboz2025quantum}.}
    \label{fig:phase}
\end{figure}

% [INSERT FIGURE 2: Phase diagram of the Shastry-Sutherland model showing the different phases as a function of J/J'. The phases should be labeled as Dimer, Plaquette VBS, Spin Liquid (highlighted), and Néel AFM.]


\newpage
\subsection{Deconfined Quantum Criticality}

Deconfined quantum criticality (DQC), proposed by Senthil in \cite{senthil2004deconfined}, challenges conventional phase transition theory by suggesting that:

\begin{itemize}
    \item A direct continuous transition can occur between phases breaking different symmetries.
    \item The critical point features fractionalized degrees of freedom (spinons) coupled to an emergent gauge field.
    \item These spinons are "deconfined" only at the critical point but confined in the ordered phases.
\end{itemize}


Testing these predictions requires careful numerical analysis of order parameters, correlation functions, and critical scaling behavior.

\subsection{Proposed phase diagram}
My research extends Shastry-Sutherland model by incorporating four-spin interactions:
\begin{align}
\hat{H}_Q = -Q \sum_{\langle ijkl \rangle} \left( S_i \cdot S_j - \frac{1}{4} \right) \left( S_k \cdot S_l - \frac{1}{4} \right)
\end{align}
The four-spin interactions describes four-spin cyclic exchange interactions on plaquettes where spins $i,j,k,l$ form the corners. This term is applied across the entire lattice. This four-spin interaction serves multiple critical functions in accessing deconfined quantum criticality:
\begin{itemize}
    \item It introduces non-frustrating quantum fluctuations that can destabilize the Néel order.
    \item It favors the formation of plaquette or columnar valence-bond patterns.
    \item It effectively enhances the competition between magnetic and valence-bond orders.
\end{itemize}

We hope that the above four-spin interactions can drive the Shastry-Sutherland model to a DQC point and propose the following phase diagram in Figure \ref{fig:prophase}:
\begin{figure}[h]
    \centering
    \includegraphics[width=0.4\linewidth]{prophase.jpeg}
    \caption{Our proposed phase diagram}
    \label{fig:prophase}
\end{figure}

The case where $\frac{J'}{J'+Q} = 0$ on the left side , i.e., pure J-Q model, has been well studied in \cite{sandvik2007evidencea}. The case where $\frac{J'}{J'+Q} = 1$ on the right side is exactly the pure Shastry-Sutherland model we discussed earlier.





\newpage
\section{\textcolor{myblue}{Current Results}}
\subsection{Machine learning model training}

I use the variational Monte Carlo method with Neural network quantum state Ansatz to study the ground state of the system, where the neural network structure is graph convolutional neural network (GCNN). The ground state energy of $\frac{J'}{J'+Q} = 0.05$ for system size of 4x4x4 and 5x5x4 after stabilization is shown in Figure \ref{fig:energy}.

\begin{figure}[h]
    \centering
    % 第一张图
    \begin{subfigure}[b]{0.5\textwidth}
        \centering
        \includegraphics[width=\textwidth]{energy1.png}
    \end{subfigure}
    \hfill
    % 第二张图
    \begin{subfigure}[b]{0.5\textwidth}
        \centering
        \includegraphics[width=\textwidth]{energy2.png}
    \end{subfigure}
    \caption{Ground state energy at $\frac{J'}{J'+Q} = 0.05$.}
    \label{fig:energy}
\end{figure}


\subsection{Correlation Ratios}
For each ratio, we compute:
\[R = 1 - \frac{F(\mathbf{k}_{\text{peak}} + \delta\mathbf{k})}{F(\mathbf{k}_{\text{peak}})}
\]
where $F$ is a structure factor, $\mathbf{k}_{\text{peak}}$ is the ordering wavevector, and $||\delta k|| = 2\pi/L
$.

\begin{enumerate}
    \item \textbf{Magnetic Correlation Ratio} ($R_{\text{Néel}}$):
    \begin{itemize}
        \item Structure factor: $S(\mathbf{k}) = \frac{1}{N}\sum_{r,r'} e^{i\mathbf{k}\cdot(\mathbf{r}-\mathbf{r'})} \langle \mathbf{S}_r \cdot \mathbf{S}_{r'} \rangle$.
    \end{itemize}

    \item \textbf{Plaquette Correlation Ratio} ($R_{\text{plaq}}$):
    \begin{itemize}
        \item Structure factor: $C(\mathbf{k}) = \frac{1}{N}\sum_{r,r'} e^{i\mathbf{k}\cdot(\mathbf{r}-\mathbf{r'})} \langle(\hat{P}_r + \hat{P}^{-1}_r)(\hat{P}_{r'} + \hat{P}^{-1}_{r'})\rangle$.
        \item $\hat{P}_r$ is plaquette cyclic permutation operator
    \end{itemize}

    \item \textbf{Dimer Correlation Ratio} ($R_{\text{dimer}}$):
    \begin{itemize}
        \item Structure factor: $D(\mathbf{k}) = \frac{1}{N}\sum_{r,r'} e^{i\mathbf{k}\cdot(\mathbf{r}-\mathbf{r'})} \langle(\mathbf{S}_r \cdot \mathbf{S}_{r+\hat{x}})(\mathbf{S}_{r'} \cdot \mathbf{S}_{r'+\hat{x}})\rangle$.
    \end{itemize}
\end{enumerate}

  \begin{figure}
        \centering
        % 第一张图
        \begin{subfigure}[b]{\textwidth}
            \centering
            \includegraphics[width=0.35\textwidth]{image1.png}
        \end{subfigure}
        
        
        % 第二张图
        \begin{subfigure}[b]{\textwidth}
            \centering
            \includegraphics[width=0.35\textwidth]{image2.png}
        \end{subfigure}
        
        
        % 第三张图
        \begin{subfigure}[b]{\textwidth}
            \centering
            \includegraphics[width=0.35\textwidth]{image3.png}
        \end{subfigure}
        \caption{Correlation ratios at $\frac{J'}{J'+Q} = 0.05$.}
        \label{fig:ratio}
    \end{figure}
From Figure \ref{fig:ratio}, it can be seen that as $\frac{J}{J'+Q} = 0.05$ increases, the Dimer correlation ratio remains almost unchanged, which means we need to further calculate the diagonal dimers to determine the specific dimer configuration. And we are experiencing an increase in the Neel phase and a decrease in the Plaquette phase, with two peaks indicating the possible emergence of an intermediate phase, which requires further investigation (note that the error bars have been drawn, but they are smaller than the symbol size).

\section{\textcolor{myblue}{Neural Network Quantum States Models}}
I also investigated the limitations of the machine learning models we use to achieve a larger system size, as well as possible improvement methods.

\begin{itemize}
    \item GCNN Limitations:
    \begin{itemize}
    \item Local information exchange may restrict the ability to capture long-range correlations.
    \item Exponentially growing parameter requirements with depth.
    \end{itemize}
    \item I also explored Vision Transformer (ViT) approaches for quantum states, but encountered:
    \begin{itemize}
    \item Enormous parameter count: $\approx 10^5$ parameters for modest 3x3x4 system size.
    \item Memory intensive computation.
    \end{itemize}
\end{itemize}

These limitations motivated the development of more efficient and physically motivated architectures.

\subsection{Novel Neural Network Architectures for Quantum States}

To overcome possible limitations, I investigated two architectures:



\begin{itemize}
\item \textbf{Convolutional Transformer Wave Function (CTWF):}
\begin{itemize}
    \item ConvEmbedding: Maps 2D spin configurations to embedded patches.
    \item ConvUnit: Extracts local features while maintaining spatial relationships.
    \item CT\_MHSA: Multi-head self-attention with relative positional encoding.
    \item IRFFN: Inverted residual network for efficient non-linear processing.
\end{itemize}
The CTWF combines convolutional operations with transformer attention mechanisms, can reach 0.2\% error with $\approx 10^4$ parameters (10× parameter reduction).

\item \textbf{GCNN with Vision Transformer Structure:}
\begin{itemize}
    \item Maintains symmetry-preserving properties through equivariant layers.
    \item Incorporates attention mechanisms for long-range correlations.
    \item Uses real-valued feature extraction with complex-valued output projection.
\end{itemize}
This hybrid architecture merges the symmetry properties of GCNN with transformer attention, achieving comparable accuracy ($0.2\%$ error) to GCNNs while enhancing scaling capabilities, albeit with a slight increase in the number of parameters.
\end{itemize}

We hope that these investigations can help us scale up to larger system sizes, thereby obtaining the accurate results needed for reliable finite size scaling analysis.

\section{\textcolor{myblue}{Future Research}}


\begin{itemize}
\item \textbf{1. Physics Investigations:}
\begin{itemize}
    \item Analyze the correlation ratio intersections between the 4x4x4 and 5x5x4 systems to identify phase transitions. Despite the limited system size, these intersections can still potentially detect phase boundaries, as the correlation ratio curves of different sized systems intersect at critical points, reflecting scale invariance.
    \item Calculate Binder cumulants across the parameter space to provide additional verification of phase transitions and critical behavior.
    \item Scan the parameter plane to determine the deconfined quantum critical points parameter window.
    \item Scale up to larger system sizes to perform a finite size scaling analysis.
\end{itemize}

\item \textbf{2. Methodological Explorations:}
\begin{itemize}
    \item Extend methodology to compute low-lying excited states across the phase diagram.
    \item Investigate the architecture to strike a balance between scalability and the number of parameters.
\end{itemize}
\end{itemize}







\printbibliography

\end{document}

