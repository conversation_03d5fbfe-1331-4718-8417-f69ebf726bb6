\documentclass{beamer}
\usetheme{Madrid}
\usecolortheme{dolphin}
\usepackage{amsmath,amssymb,graphicx,physics,bm}
\usepackage{tikz}
\usetikzlibrary{shapes,arrows,positioning}

\title{Shastry-Sutherland Model\\and Network Quantum States Models}
\author{Yu Shenlong}
\date{\today}

\begin{document}

\begin{frame}
\titlepage
\end{frame}

\begin{frame}{Outline}
\tableofcontents
\end{frame}

\section{Physical Model}

\begin{frame}{The Shastry-Sutherland Model}
\begin{columns}
\column{0.6\textwidth}
\begin{itemize}
\item Hamiltonian:
\begin{align}
\hat{H} = J \sum_{\langle r,r' \rangle} \vec{S}_r \cdot \vec{S}_{r'} + J' \sum_{\langle\langle r,r' \rangle\rangle} \vec{S}_r \cdot \vec{S}_{r'}
\end{align}
\item Four-spin interaction ($Q$-term):
\begin{align}
\hat{H}_Q = -Q \sum_{\langle ijkl \rangle} \left(S_i \cdot S_j - \frac{1}{4}\right)\left(S_k \cdot S_l - \frac{1}{4}\right)
\end{align}
\end{itemize}

\column{0.4\textwidth}
% Insert Shastry-Sutherland lattice figure here
\includegraphics[width=\textwidth]{shastry_sutherland_lattice.png}
\end{columns}
\end{frame}

\begin{frame}{Phase Diagram of Shastry-Sutherland Model}
\begin{columns}
\column{0.55\textwidth}
\begin{itemize}
\item Known phases:
\begin{itemize}
\item Dimer phase: $J \ll J'$
\item Plaquette phase: $0.675 \lesssim J/J' \lesssim 0.765$
\item Néel antiferromagnetic phase: $J/J' \gtrsim 0.82$
\item Spin liquid: $0.77 \lesssim J/J' \lesssim 0.82$ (debated)
\end{itemize}
\item With $Q$-term: modifies phase boundaries
\item Our focus: $J/(J'+Q)$ vs. $J'/(J'+Q)$ plane
\end{itemize}

\column{0.45\textwidth}
% Insert the hand-drawn phase diagram from the uploaded image
\includegraphics[width=\textwidth]{phase_diagram_sketch.png}
\caption{Proposed phase diagram with Q-term}
\end{columns}
\end{frame}

\begin{frame}{Results for J'/J = 0.05}
\begin{columns}
\column{0.5\textwidth}
\begin{itemize}
\item Order parameters along $J'/J = 0.05$
\item Analysis of magnetic and plaquette ordering
\item Correlation ratio crossing analysis
\item System sizes $L = 6$ to $L = 14$
\item Evidence for phase transition at $J/(J'+Q) \approx 0.04$
\end{itemize}

\column{0.5\textwidth}
% Insert order parameter results here
\includegraphics[width=\textwidth]{order_parameters.png}
\caption{Order parameters}

\vspace{0.5cm}
% Insert correlation ratio results here
\includegraphics[width=\textwidth]{correlation_ratios.png}
\caption{Correlation ratios}
\end{columns}
\end{frame}

\section{Machine Learning Methods}

\begin{frame}{Limitations of Graph Convolutional Neural Networks (GCNN)}
\begin{itemize}
\item GCNN preserves symmetries inherently through equivariance
\item Limitations:
    \begin{itemize}
    \item Local information exchange only - difficulty capturing long-range correlations
    \item Exponentially growing parameter requirements with depth
    \item Inherent inductive bias may not match quantum ground states
    \item Limited expressivity for highly entangled states
    \item Challenges in representing phase transitions
    \end{itemize}
\item Need for architectures that can effectively capture non-local quantum correlations
\end{itemize}
\end{frame}

\begin{frame}{Vision Transformers (ViT) Approach by Becca et al.}
\begin{columns}
\column{0.6\textwidth}
\begin{itemize}
\item Viteritti, Rende, and Becca (2023): Transformers for frustrated quantum systems
\item Advantages:
    \begin{itemize}
    \item Captures non-local correlations via attention mechanism
    \item State-of-the-art accuracy on quantum systems
    \end{itemize}
\item Limitations:
    \begin{itemize}
    \item Enormous parameter count: $\approx 10^6$ parameters
    \item Memory intensive computation
    \item Complex-valued training challenges
    \item Limited system sizes ($L \leq 18$)
    \end{itemize}
\end{itemize}

\column{0.4\textwidth}
% Insert ViT architecture figure here
\includegraphics[width=\textwidth]{vit_architecture.png}
\caption{ViT architecture from Viteritti et al.}
\end{columns}
\end{frame}

\begin{frame}{Our Approach: Convolutional Transformer Wave Function (CTWF)}
\begin{columns}
\column{0.55\textwidth}
\begin{tikzpicture}[
    node distance=1.2cm,
    box/.style={rectangle, draw, minimum width=2.5cm, minimum height=0.7cm, text centered, rounded corners}
]
\node[box] (input) {Spin Configuration};
\node[box, below of=input] (patching) {Convolutional Embedding};
\node[box, below of=patching] (convunit) {ConvUnit};
\node[box, below of=convunit] (mhsa) {CT-MHSA};
\node[box, below of=mhsa] (irffn) {IRFFN};
\node[box, below of=irffn] (output) {OutputHead};

\draw[->] (input) -- (patching);
\draw[->] (patching) -- (convunit);
\draw[->] (convunit) -- (mhsa);
\draw[->] (mhsa) -- (irffn);
\draw[->] (irffn) -- (output);
\end{tikzpicture}

\column{0.45\textwidth}
\begin{itemize}
\item Key innovations:
    \begin{itemize}
    \item Local feature extraction via ConvUnit
    \item Relative positional encoding in CT-MHSA
    \item Inverted Residual FFN for efficiency
    \item Parameter-efficient design
    \item Only $\approx 10^4$ parameters
    \end{itemize}
\item Combines benefits of CNNs and Transformers
\end{itemize}
\end{columns}
\end{frame}

\begin{frame}{GCNN with ViT Architecture}
\begin{columns}
\column{0.55\textwidth}
\begin{itemize}
\item Hybrid architecture combining:
    \begin{itemize}
    \item Group-equivariant convolutional layers (GCNN)
    \item Self-attention mechanisms from Vision Transformers
    \end{itemize}
\item Structure:
    \begin{itemize}
    \item Initial symmetrization layer
    \item Multiple GCNN layers for local features
    \item Attention layers for long-range correlations
    \item Residual connections and layer normalization
    \end{itemize}
\item Parameter count: $\approx 2 \times 10^4$ parameters
\end{itemize}

\column{0.45\textwidth}
\begin{tikzpicture}[
    node distance=1cm,
    box/.style={rectangle, draw, minimum width=2.5cm, minimum height=0.6cm, text centered, rounded corners}
]
\node[box] (input) {Spin Configuration};
\node[box, below of=input] (symm) {Symmetrization Layer};
\node[box, below of=symm] (gcnn1) {GCNN Layer 1};
\node[box, below of=gcnn1] (gcnn2) {GCNN Layer 2};
\node[box, below of=gcnn2] (attn1) {Attention Layer 1};
\node[box, below of=attn1] (attn2) {Attention Layer 2};
\node[box, below of=attn2] (output) {Output Projection};

\draw[->] (input) -- (symm);
\draw[->] (symm) -- (gcnn1);
\draw[->] (gcnn1) -- (gcnn2);
\draw[->] (gcnn2) -- (attn1);
\draw[->] (attn1) -- (attn2);
\draw[->] (attn2) -- (output);
\end{tikzpicture}
\end{columns}
\end{frame}

\begin{frame}{Performance Comparison}
\begin{columns}
\column{0.5\textwidth}
\begin{itemize}
\item Energy convergence (L=6, J/J'=0.8):
    \begin{itemize}
    \item ViT: $\Delta E \approx 10^{-5}$
    \item CTWF: $\Delta E \approx 5 \times 10^{-4}$
    \item GCNN+ViT: $\Delta E \approx 10^{-4}$
    \end{itemize}
\item Scaling with system size:
    \begin{itemize}
    \item ViT: up to L=18
    \item CTWF: up to L=24
    \item GCNN+ViT: up to L=20
    \end{itemize}
\item Training time reduction: 3-5x faster than ViT
\end{itemize}

\column{0.5\textwidth}
% Insert energy convergence plot
\includegraphics[width=\textwidth]{energy_convergence.png}
\caption{Energy convergence comparison}

\vspace{0.5cm}
% Insert system size scaling plot
\includegraphics[width=\textwidth]{system_size_scaling.png}
\caption{System size scaling}
\end{columns}
\end{frame}

\begin{frame}{Conclusions and Future Work}
\begin{itemize}
\item Key findings:
    \begin{itemize}
    \item Confirmed phase transitions in Shastry-Sutherland model with Q-term
    \item Developed efficient architectures (CTWF and GCNN+ViT)
    \item Reduced parameter count while maintaining accuracy
    \end{itemize}
\item Ongoing work:
    \begin{itemize}
    \item Complete phase diagram of J-J'-Q model
    \item Investigation of spin-liquid properties
    \item Extending architectures to larger system sizes (L > 24)
    \item Hybridization with specialized quantum state representations
    \end{itemize}
\item Next steps: Investigate triplet excitations with momentum resolution
\end{itemize}
\end{frame}

\begin{frame}{Thank You!}
\begin{center}
\Large Questions?
\end{center}
\end{frame}

\end{document}
