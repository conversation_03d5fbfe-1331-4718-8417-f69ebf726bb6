This is pdfTeX, Version 3.141592653-2.6-1.40.26 (TeX Live 2024) (preloaded format=pdflatex 2024.4.7)  1 MAY 2025 23:46
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**/Users/<USER>/Documents/Academic/Projects/Project_report/slides.tex
(/Users/<USER>/Documents/Academic/Projects/Project_report/slides.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamer.cls
Document Class: beamer 2024/01/06 v3.71 A class for typesetting presentations
(/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasemodes.sty (/usr/local/texlive/2024/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count188
)
\beamer@tempbox=\box51
\beamer@tempcount=\count189
\c@beamerpauses=\count190
 (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasedecode.sty
\beamer@slideinframe=\count191
\beamer@minimum=\count192
\beamer@decode@box=\box52
)
\beamer@commentbox=\box53
\beamer@modecount=\count193
) (/usr/local/texlive/2024/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
)
\headdp=\dimen140
\footheight=\dimen141
\sidebarheight=\dimen142
\beamer@tempdim=\dimen143
\beamer@finalheight=\dimen144
\beamer@animht=\dimen145
\beamer@animdp=\dimen146
\beamer@animwd=\dimen147
\beamer@leftmargin=\dimen148
\beamer@rightmargin=\dimen149
\beamer@leftsidebar=\dimen150
\beamer@rightsidebar=\dimen151
\beamer@boxsize=\dimen152
\beamer@vboxoffset=\dimen153
\beamer@descdefault=\dimen154
\beamer@descriptionwidth=\dimen155
\beamer@lastskip=\skip48
\beamer@areabox=\box54
\beamer@animcurrent=\box55
\beamer@animshowbox=\box56
\beamer@sectionbox=\box57
\beamer@logobox=\box58
\beamer@linebox=\box59
\beamer@sectioncount=\count194
\beamer@subsubsectionmax=\count195
\beamer@subsectionmax=\count196
\beamer@sectionmax=\count197
\beamer@totalheads=\count198
\beamer@headcounter=\count199
\beamer@partstartpage=\count266
\beamer@sectionstartpage=\count267
\beamer@subsectionstartpage=\count268
\beamer@animationtempa=\count269
\beamer@animationtempb=\count270
\beamer@xpos=\count271
\beamer@ypos=\count272
\beamer@ypos@offset=\count273
\beamer@showpartnumber=\count274
\beamer@currentsubsection=\count275
\beamer@coveringdepth=\count276
\beamer@sectionadjust=\count277
\beamer@toclastsection=\count278
\beamer@tocsectionnumber=\count279
 (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbaseoptions.sty (/usr/local/texlive/2024/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
))
\beamer@paperwidth=\skip49
\beamer@paperheight=\skip50
 (/usr/local/texlive/2024/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (/usr/local/texlive/2024/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
)
\Gm@cnth=\count280
\Gm@cntv=\count281
\c@Gm@tempcnt=\count282
\Gm@bindingoffset=\dimen156
\Gm@wd@mp=\dimen157
\Gm@odd@mp=\dimen158
\Gm@even@mp=\dimen159
\Gm@layoutwidth=\dimen160
\Gm@layoutheight=\dimen161
\Gm@layouthoffset=\dimen162
\Gm@layoutvoffset=\dimen163
\Gm@dimlist=\toks18
) (/usr/local/texlive/2024/texmf-dist/tex/latex/pgf/math/pgfmath.sty (/usr/local/texlive/2024/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks19
\pgfutil@tempdima=\dimen164
\pgfutil@tempdimb=\dimen165
) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box60
) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/local/texlive/2024/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks20
\pgfkeys@temptoks=\toks21
 (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfiltered.code.tex
\pgfkeys@tmptoks=\toks22
))) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex
\pgf@x=\dimen166
\pgf@xa=\dimen167
\pgf@xb=\dimen168
\pgf@xc=\dimen169
\pgf@y=\dimen170
\pgf@ya=\dimen171
\pgf@yb=\dimen172
\pgf@yc=\dimen173
\c@pgf@counta=\count283
\c@pgf@countb=\count284
\c@pgf@countc=\count285
\c@pgf@countd=\count286
) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen174
\pgfmath@count=\count287
\pgfmath@box=\box61
\pgfmath@toks=\toks23
\pgfmath@stack@operand=\toks24
\pgfmath@stack@operation=\toks25
) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.tex) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric.code.tex) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.tex) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.code.tex) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.tex) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithmetics.code.tex) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count288
))) (/usr/local/texlive/2024/texmf-dist/tex/latex/base/size11.clo
File: size11.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
) (/usr/local/texlive/2024/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty (/usr/local/texlive/2024/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2024/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (/usr/local/texlive/2024/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (/usr/local/texlive/2024/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.
 (/usr/local/texlive/2024/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2022/09/22 v1.2b Graphics/color driver for pdftex
))
\Gin@req@height=\dimen175
\Gin@req@width=\dimen176
) (/usr/local/texlive/2024/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)
\pgf@x=\dimen177
\pgf@y=\dimen178
\pgf@xa=\dimen179
\pgf@ya=\dimen180
\pgf@xb=\dimen181
\pgf@yb=\dimen182
\pgf@xc=\dimen183
\pgf@yc=\dimen184
\pgf@xd=\dimen185
\pgf@yd=\dimen186
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count289
\c@pgf@countb=\count290
\c@pgf@countc=\count291
\c@pgf@countd=\count292
\t@pgf@toka=\toks26
\t@pgf@tokb=\toks27
\t@pgf@tokc=\toks28
\pgf@sys@id@count=\count293
 (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def
 (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-pdftex.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)
 (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count294
\pgfsyssoftpath@bigbuffer@items=\count295
) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/local/texlive/2024/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)
 (/usr/local/texlive/2024/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
 (/usr/local/texlive/2024/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)
 (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/math/pgfint.code.tex) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen187
\pgf@picmaxx=\dimen188
\pgf@picminy=\dimen189
\pgf@picmaxy=\dimen190
\pgf@pathminx=\dimen191
\pgf@pathmaxx=\dimen192
\pgf@pathminy=\dimen193
\pgf@pathmaxy=\dimen194
\pgf@xx=\dimen195
\pgf@xy=\dimen196
\pgf@yx=\dimen197
\pgf@yy=\dimen198
\pgf@zx=\dimen199
\pgf@zy=\dimen256
) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen257
\pgf@path@lasty=\dimen258
) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen259
\pgf@shorten@start@additional=\dimen260
) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box62
\pgf@hbox=\box63
\pgf@layerbox@main=\box64
\pgf@picture@serial@count=\count296
) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen261
) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen262
\pgf@pt@y=\dimen263
\pgf@pt@temp=\dimen264
) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen265
) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen266
\pgf@sys@shading@range@num=\count297
\pgf@shadingcount=\count298
) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box65
) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/local/texlive/2024/texmf-dist/tex/latex/pgf/utilities/xxcolor.sty
Package: xxcolor 2003/10/24 ver 0.1
\XC@nummixins=\count299
\XC@countmixins=\count300
) (/usr/local/texlive/2024/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
) (/usr/local/texlive/2024/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-01-20 v7.01h Hypertext links for LaTeX
 (/usr/local/texlive/2024/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
) (/usr/local/texlive/2024/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (/usr/local/texlive/2024/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (/usr/local/texlive/2024/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (/usr/local/texlive/2024/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (/usr/local/texlive/2024/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)) (/usr/local/texlive/2024/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (/usr/local/texlive/2024/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
) (/usr/local/texlive/2024/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (/usr/local/texlive/2024/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (/usr/local/texlive/2024/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (/usr/local/texlive/2024/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count301
)
\@linkdim=\dimen267
\Hy@linkcounter=\count302
\Hy@pagecounter=\count303
 (/usr/local/texlive/2024/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-01-20 v7.01h Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
) (/usr/local/texlive/2024/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count304
 (/usr/local/texlive/2024/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-01-20 v7.01h Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Option `bookmarks' set `true' on input line 4062.
Package hyperref Info: Option `bookmarksopen' set `true' on input line 4062.
Package hyperref Info: Option `implicit' set `false' on input line 4062.
Package hyperref Info: Hyper figures OFF on input line 4179.
Package hyperref Info: Link nesting OFF on input line 4184.
Package hyperref Info: Hyper index ON on input line 4187.
Package hyperref Info: Plain pages OFF on input line 4194.
Package hyperref Info: Backreferencing OFF on input line 4199.
Package hyperref Info: Implicit mode OFF; no redefinition of LaTeX internals.
Package hyperref Info: Bookmarks ON on input line 4446.
\c@Hy@tempcnt=\count305
 (/usr/local/texlive/2024/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip16
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4784.
\XeTeXLinkMargin=\dimen268
 (/usr/local/texlive/2024/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (/usr/local/texlive/2024/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count306
\Field@Width=\dimen269
\Fld@charsize=\dimen270
Package hyperref Info: Hyper figures OFF on input line 6063.
Package hyperref Info: Link nesting OFF on input line 6068.
Package hyperref Info: Hyper index ON on input line 6071.
Package hyperref Info: backreferencing OFF on input line 6078.
Package hyperref Info: Link coloring OFF on input line 6083.
Package hyperref Info: Link coloring with OCG OFF on input line 6088.
Package hyperref Info: PDF/A mode OFF on input line 6093.
\Hy@abspage=\count307


Package hyperref Message: Stopped early.

)
Package hyperref Info: Driver (autodetected): hpdftex.
 (/usr/local/texlive/2024/texmf-dist/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2024-01-20 v7.01h Hyperref driver for pdfTeX
 (/usr/local/texlive/2024/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
)
\Fld@listcount=\count308
\c@bookmark@seq@number=\count309
 (/usr/local/texlive/2024/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
 (/usr/local/texlive/2024/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 285.
)) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbaserequires.sty (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasecompatibility.sty) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasefont.sty (/usr/local/texlive/2024/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (/usr/local/texlive/2024/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\@emptytoks=\toks29
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (/usr/local/texlive/2024/texmf-dist/tex/latex/sansmathaccent/sansmathaccent.sty
Package: sansmathaccent 2020/01/31
 (/usr/local/texlive/2024/texmf-dist/tex/latex/koma-script/scrlfile.sty
Package: scrlfile 2023/07/07 v3.41 KOMA-Script package (file load hooks)
 (/usr/local/texlive/2024/texmf-dist/tex/latex/koma-script/scrlfile-hook.sty
Package: scrlfile-hook 2023/07/07 v3.41 KOMA-Script package (using LaTeX hooks)
 (/usr/local/texlive/2024/texmf-dist/tex/latex/koma-script/scrlogo.sty
Package: scrlogo 2023/07/07 v3.41 KOMA-Script package (logo)
))))) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasetranslator.sty (/usr/local/texlive/2024/texmf-dist/tex/latex/translator/translator.sty
Package: translator 2021-05-31 v1.12d Easy translation of strings in LaTeX
)) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasemisc.sty) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasetwoscreens.sty) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbaseoverlay.sty
\beamer@argscount=\count310
\beamer@lastskipcover=\skip51
\beamer@trivlistdepth=\count311
) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasetitle.sty) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasesection.sty
\c@lecture=\count312
\c@part=\count313
\c@section=\count314
\c@subsection=\count315
\c@subsubsection=\count316
) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbaseframe.sty
\beamer@framebox=\box66
\beamer@frametitlebox=\box67
\beamer@zoombox=\box68
\beamer@zoomcount=\count317
\beamer@zoomframecount=\count318
\beamer@frametextheight=\dimen271
\c@subsectionslide=\count319
\beamer@frametopskip=\skip52
\beamer@framebottomskip=\skip53
\beamer@frametopskipautobreak=\skip54
\beamer@framebottomskipautobreak=\skip55
\beamer@envbody=\toks30
\framewidth=\dimen272
\c@framenumber=\count320
) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbaseverbatim.sty
\beamer@verbatimfileout=\write4
) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbaseframesize.sty
\beamer@splitbox=\box69
\beamer@autobreakcount=\count321
\beamer@autobreaklastheight=\dimen273
\beamer@frametitletoks=\toks31
\beamer@framesubtitletoks=\toks32
) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbaseframecomponents.sty
\beamer@footins=\box70
) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasecolor.sty) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasenotes.sty
\beamer@frameboxcopy=\box71
) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasetoc.sty) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasetemplates.sty
\beamer@sbttoks=\toks33
 (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbaseauxtemplates.sty (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbaseboxes.sty
\bmb@box=\box72
\bmb@colorbox=\box73
\bmb@boxwidth=\dimen274
\bmb@boxheight=\dimen275
\bmb@prevheight=\dimen276
\bmb@temp=\dimen277
\bmb@dima=\dimen278
\bmb@dimb=\dimen279
\bmb@prevheight=\dimen280
)
\beamer@blockheadheight=\dimen281
)) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbaselocalstructure.sty (/usr/local/texlive/2024/texmf-dist/tex/latex/tools/enumerate.sty
Package: enumerate 2023/07/04 v3.00 enumerate extensions (DPC)
\@enLab=\toks34
)
\beamer@bibiconwidth=\skip56
\c@figure=\count322
\c@table=\count323
\abovecaptionskip=\skip57
\belowcaptionskip=\skip58
) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasenavigation.sty (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasenavigationsymbols.tex)
\beamer@section@min@dim=\dimen282
) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasetheorems.sty (/usr/local/texlive/2024/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip59

For additional information on amsmath, use the `?' option.
(/usr/local/texlive/2024/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (/usr/local/texlive/2024/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks35
\ex@=\dimen283
)) (/usr/local/texlive/2024/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen284
) (/usr/local/texlive/2024/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count324
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count325
\leftroot@=\count326
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count327
\DOTSCASE@=\count328
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box74
\strutbox@=\box75
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen285
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count329
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count330
\dotsspace@=\muskip17
\c@parentequation=\count331
\dspbrk@lvl=\count332
\tag@help=\toks36
\row@=\count333
\column@=\count334
\maxfields@=\count335
\andhelp@=\toks37
\eqnshift@=\dimen286
\alignsep@=\dimen287
\tagshift@=\dimen288
\tagwidth@=\dimen289
\totwidth@=\dimen290
\lineht@=\dimen291
\@envbody=\toks38
\multlinegap=\skip60
\multlinetaggap=\skip61
\mathdisplay@stack=\toks39
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (/usr/local/texlive/2024/texmf-dist/tex/latex/amscls/amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks40
\thm@bodyfont=\toks41
\thm@headfont=\toks42
\thm@notefont=\toks43
\thm@headpunct=\toks44
\thm@preskip=\skip62
\thm@postskip=\skip63
\thm@headsep=\skip64
\dth@everypar=\toks45
)
\c@theorem=\count336
) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerbasethemes.sty)) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerthemedefault.sty (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerfontthemedefault.sty) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamercolorthemedefault.sty) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerinnerthemedefault.sty
\beamer@dima=\dimen292
\beamer@dimb=\dimen293
) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerouterthemedefault.sty))) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerthemeMadrid.sty (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamercolorthemewhale.sty) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamercolorthemeorchid.sty) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerinnerthemerounded.sty) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamerouterthemeinfolines.sty)) (/usr/local/texlive/2024/texmf-dist/tex/latex/beamer/beamercolorthemedolphin.sty) (/usr/local/texlive/2024/texmf-dist/tex/latex/physics/physics.sty
Package: physics 
 (/usr/local/texlive/2024/texmf-dist/tex/latex/l3packages/xparse/xparse.sty (/usr/local/texlive/2024/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2024-02-20 L3 programming layer (loader) 
 (/usr/local/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-02-20 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count337
\l__pdf_internal_box=\box76
))
Package: xparse 2024-02-18 L3 Experimental document command parser
))
\sympureletters=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `pureletters' in version `bold'
(Font)                  OT1/mathkerncmss/m/sl --> OT1/mathkerncmss/bx/sl on input line 5.
 (/usr/local/texlive/2024/texmf-dist/tex/latex/tools/bm.sty
Package: bm 2023/07/08 v1.2f Bold Symbol Support (DPC/FMi)
\symboldoperators=\mathgroup7
\symboldletters=\mathgroup8
\symboldsymbols=\mathgroup9
Package bm Info: No bold for \OMX/cmex/m/n, using \pmb.
Package bm Info: No bold for \U/msa/m/n, using \pmb.
Package bm Info: No bold for \U/msb/m/n, using \pmb.
\symboldpureletters=\mathgroup10
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 149.
) (/usr/local/texlive/2024/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty (/usr/local/texlive/2024/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty
Package: pgf 2023-01-15 v3.1.10 (3.1.10)
 (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box77
) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/local/texlive/2024/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen294
\pgf@nodesepend=\dimen295
) (/usr/local/texlive/2024/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/local/texlive/2024/texmf-dist/tex/latex/pgf/utilities/pgffor.sty (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen296
\pgffor@skip=\dimen297
\pgffor@stack=\toks46
\pgffor@toks=\toks47
)) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)
 (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count338
\pgfplotmarksize=\dimen298
)
\tikz@lastx=\dimen299
\tikz@lasty=\dimen300
\tikz@lastxsaved=\dimen301
\tikz@lastysaved=\dimen302
\tikz@lastmovetox=\dimen303
\tikz@lastmovetoy=\dimen304
\tikzleveldistance=\dimen305
\tikzsiblingdistance=\dimen306
\tikz@figbox=\box78
\tikz@figbox@bg=\box79
\tikz@tempbox=\box80
\tikz@tempbox@bg=\box81
\tikztreelevel=\count339
\tikznumberofchildren=\count340
\tikznumberofcurrentchild=\count341
\tikz@fig@count=\count342
 (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count343
\pgfmatrixcurrentcolumn=\count344
\pgf@matrix@numberofcolumns=\count345
)
\tikz@expandcount=\count346
 (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.code.tex
File: tikzlibraryshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.geometric.code.tex
File: tikzlibraryshapes.geometric.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.geometric.code.tex
File: pgflibraryshapes.geometric.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.misc.code.tex
File: tikzlibraryshapes.misc.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.misc.code.tex
File: pgflibraryshapes.misc.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.symbols.code.tex
File: tikzlibraryshapes.symbols.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.symbols.code.tex
File: pgflibraryshapes.symbols.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.arrows.code.tex
File: tikzlibraryshapes.arrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.arrows.code.tex
File: pgflibraryshapes.arrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.callouts.code.tex (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.callouts.code.tex)) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryshapes.multipart.code.tex
File: tikzlibraryshapes.multipart.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.multipart.code.tex
File: pgflibraryshapes.multipart.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodepartlowerbox=\box82
\pgfnodeparttwobox=\box83
\pgfnodepartthreebox=\box84
\pgfnodepartfourbox=\box85
\pgfnodeparttwentybox=\box86
\pgfnodepartnineteenbox=\box87
\pgfnodeparteighteenbox=\box88
\pgfnodepartseventeenbox=\box89
\pgfnodepartsixteenbox=\box90
\pgfnodepartfifteenbox=\box91
\pgfnodepartfourteenbox=\box92
\pgfnodepartthirteenbox=\box93
\pgfnodeparttwelvebox=\box94
\pgfnodepartelevenbox=\box95
\pgfnodeparttenbox=\box96
\pgfnodepartninebox=\box97
\pgfnodeparteightbox=\box98
\pgfnodepartsevenbox=\box99
\pgfnodepartsixbox=\box100
\pgfnodepartfivebox=\box101
))) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryarrows.code.tex
File: tikzlibraryarrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
 (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/libraries/pgflibraryarrows.code.tex
File: pgflibraryarrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\arrowsize=\dimen307
)) (/usr/local/texlive/2024/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarypositioning.code.tex
File: tikzlibrarypositioning.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
No file slides.aux.
\openout1 = `slides.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: custom
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: includehead includefoot 
* h-part:(L,W,R)=(10.95003pt, 342.2953pt, 10.95003pt)
* v-part:(T,H,B)=(0.0pt, 273.14662pt, 0.0pt)
* \paperwidth=364.19536pt
* \paperheight=273.14662pt
* \textwidth=342.2953pt
* \textheight=244.6939pt
* \oddsidemargin=-61.31996pt
* \evensidemargin=-61.31996pt
* \topmargin=-72.26999pt
* \headheight=14.22636pt
* \headsep=0.0pt
* \topskip=11.0pt
* \footskip=14.22636pt
* \marginparwidth=4.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

(/usr/local/texlive/2024/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count347
\scratchdimen=\dimen308
\scratchbox=\box102
\nofMPsegments=\count348
\nofMParguments=\count349
\everyMPshowfont=\toks48
\MPscratchCnt=\count350
\MPscratchDim=\dimen309
\MPnumerator=\count351
\makeMPintoPDFobject=\count352
\everyMPtoPDFconversion=\toks49
) (/usr/local/texlive/2024/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (/usr/local/texlive/2024/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
Package hyperref Info: Link coloring OFF on input line 12.
\@outlinefile=\write5
\openout5 = `slides.out'.

LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/cmss/m/n on input line 12.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/cmss/b/n on input line 12.
\symnumbers=\mathgroup11
LaTeX Font Info:    Redeclaring symbol font `pureletters' on input line 12.
LaTeX Font Info:    Overwriting symbol font `pureletters' in version `normal'
(Font)                  OT1/mathkerncmss/m/sl --> OT1/cmss/m/it on input line 12.
LaTeX Font Info:    Overwriting symbol font `pureletters' in version `bold'
(Font)                  OT1/mathkerncmss/bx/sl --> OT1/cmss/m/it on input line 12.
LaTeX Font Info:    Overwriting math alphabet `\mathrm' in version `normal'
(Font)                  OT1/cmss/m/n --> OT1/cmr/m/n on input line 12.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 12.
LaTeX Font Info:    Redeclaring math alphabet \mathsf on input line 12.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> OT1/cmss/m/n on input line 12.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> OT1/cmss/m/n on input line 12.
LaTeX Font Info:    Redeclaring math alphabet \mathit on input line 12.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> OT1/cmss/m/it on input line 12.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> OT1/cmss/m/it on input line 12.
LaTeX Font Info:    Redeclaring math alphabet \mathtt on input line 12.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> OT1/cmtt/m/n on input line 12.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> OT1/cmtt/m/n on input line 12.
LaTeX Font Info:    Overwriting symbol font `numbers' in version `bold'
(Font)                  OT1/cmss/m/n --> OT1/cmss/b/n on input line 12.
LaTeX Font Info:    Overwriting symbol font `pureletters' in version `bold'
(Font)                  OT1/cmss/m/it --> OT1/cmss/b/it on input line 12.
LaTeX Font Info:    Overwriting math alphabet `\mathrm' in version `bold'
(Font)                  OT1/cmss/b/n --> OT1/cmr/b/n on input line 12.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmss/b/n --> OT1/cmss/b/n on input line 12.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/m/n --> OT1/cmss/b/n on input line 12.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmss/m/it --> OT1/cmss/b/it on input line 12.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> OT1/cmtt/b/n on input line 12.
LaTeX Font Info:    Redeclaring symbol font `pureletters' on input line 12.
LaTeX Font Info:    Overwriting symbol font `pureletters' in version `normal'
(Font)                  OT1/cmss/m/it --> OT1/mathkerncmss/m/sl on input line 12.
LaTeX Font Info:    Overwriting symbol font `pureletters' in version `bold'
(Font)                  OT1/cmss/b/it --> OT1/mathkerncmss/m/sl on input line 12.
LaTeX Font Info:    Overwriting symbol font `pureletters' in version `bold'
(Font)                  OT1/mathkerncmss/m/sl --> OT1/mathkerncmss/bx/sl on input line 12.
 (/usr/local/texlive/2024/texmf-dist/tex/latex/translator/translator-basic-dictionary-English.dict
Dictionary: translator-basic-dictionary, Language: English 
) (/usr/local/texlive/2024/texmf-dist/tex/latex/translator/translator-bibliography-dictionary-English.dict
Dictionary: translator-bibliography-dictionary, Language: English 
) (/usr/local/texlive/2024/texmf-dist/tex/latex/translator/translator-environment-dictionary-English.dict
Dictionary: translator-environment-dictionary, Language: English 
) (/usr/local/texlive/2024/texmf-dist/tex/latex/translator/translator-months-dictionary-English.dict
Dictionary: translator-months-dictionary, Language: English 
) (/usr/local/texlive/2024/texmf-dist/tex/latex/translator/translator-numbers-dictionary-English.dict
Dictionary: translator-numbers-dictionary, Language: English 
) (/usr/local/texlive/2024/texmf-dist/tex/latex/translator/translator-theorem-dictionary-English.dict
Dictionary: translator-theorem-dictionary, Language: English 
) (./slides.nav)
Overfull \hbox (48.38454pt too wide) in paragraph at lines 12--12
 [][][][]  
 []


Overfull \hbox (48.38454pt too wide) in paragraph at lines 16--16
 [][][][]  
 []


Overfull \hbox (48.38454pt too wide) has occurred while \output is active
 [][][][]  
 []

[1

{/usr/local/texlive/2024/texmf-var/fonts/map/pdftex/updmap/pdftex.map}]
Overfull \hbox (48.38454pt too wide) in paragraph at lines 20--20
 [][][][]  
 []

No file slides.toc.

Overfull \hbox (48.38454pt too wide) has occurred while \output is active
 [][][][]  
 []

[2

]
Overfull \hbox (48.38454pt too wide) in paragraph at lines 42--42
 [][][][]  
 []

LaTeX Font Info:    Trying to load font information for U+msa on input line 42.
(/usr/local/texlive/2024/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 42.
 (/usr/local/texlive/2024/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
LaTeX Font Info:    Trying to load font information for OT1+mathkerncmss on input line 42.
 (/usr/local/texlive/2024/texmf-dist/tex/latex/sansmathaccent/ot1mathkerncmss.fd
File: ot1mathkerncmss.fd 2020/01/31 Fontinst v1.933 font definitions for OT1/mathkerncmss.
)
Overfull \hbox (17.27557pt too wide) detected at line 42
[]
 []


LaTeX Warning: File `shastry_sutherland_lattice.png' not found on input line 42.


/Users/<USER>/Documents/Academic/Projects/Project_report/slides.tex:42: Package pdftex.def Error: File `shastry_sutherland_lattice.png' not found: using draft setting.

See the pdftex.def package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.42 \end{frame}
                
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


Overfull \hbox (48.38454pt too wide) has occurred while \output is active
 [][][][]  
 []

[3

]
Overfull \hbox (48.38454pt too wide) in paragraph at lines 64--64
 [][][][]  
 []


LaTeX Warning: File `phase_diagram_sketch.png' not found on input line 64.


/Users/<USER>/Documents/Academic/Projects/Project_report/slides.tex:64: Package pdftex.def Error: File `phase_diagram_sketch.png' not found: using draft setting.

See the pdftex.def package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.64 \end{frame}
                
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


/Users/<USER>/Documents/Academic/Projects/Project_report/slides.tex:64: LaTeX Error: \caption outside figure or table.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.64 \end{frame}
                
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


Overfull \hbox (48.38454pt too wide) has occurred while \output is active
 [][][][]  
 []

[4

]
Overfull \hbox (48.38454pt too wide) in paragraph at lines 87--87
 [][][][]  
 []


LaTeX Warning: File `order_parameters.png' not found on input line 87.


/Users/<USER>/Documents/Academic/Projects/Project_report/slides.tex:87: Package pdftex.def Error: File `order_parameters.png' not found: using draft setting.

See the pdftex.def package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.87 \end{frame}
                
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


/Users/<USER>/Documents/Academic/Projects/Project_report/slides.tex:87: LaTeX Error: \caption outside figure or table.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.87 \end{frame}
                
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


LaTeX Warning: File `correlation_ratios.png' not found on input line 87.


/Users/<USER>/Documents/Academic/Projects/Project_report/slides.tex:87: Package pdftex.def Error: File `correlation_ratios.png' not found: using draft setting.

See the pdftex.def package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.87 \end{frame}
                
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


/Users/<USER>/Documents/Academic/Projects/Project_report/slides.tex:87: LaTeX Error: \caption outside figure or table.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.87 \end{frame}
                
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


Overfull \vbox (150.26326pt too high) detected at line 87
 []


Overfull \hbox (48.38454pt too wide) has occurred while \output is active
 [][][][]  
 []

[5

]
Overfull \hbox (48.38454pt too wide) in paragraph at lines 104--104
 [][][][]  
 []


Overfull \hbox (48.38454pt too wide) has occurred while \output is active
 [][][][]  
 []

[6

]
Overfull \hbox (48.38454pt too wide) in paragraph at lines 130--130
 [][][][]  
 []


LaTeX Warning: File `vit_architecture.png' not found on input line 130.


/Users/<USER>/Documents/Academic/Projects/Project_report/slides.tex:130: Package pdftex.def Error: File `vit_architecture.png' not found: using draft setting.

See the pdftex.def package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.130 \end{frame}
                 
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


/Users/<USER>/Documents/Academic/Projects/Project_report/slides.tex:130: LaTeX Error: \caption outside figure or table.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.130 \end{frame}
                 
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


Overfull \hbox (48.38454pt too wide) has occurred while \output is active
 [][][][]  
 []

[7

]
Overfull \hbox (48.38454pt too wide) in paragraph at lines 166--166
 [][][][]  
 []


Overfull \hbox (48.38454pt too wide) has occurred while \output is active
 [][][][]  
 []

[8

]
Overfull \hbox (48.38454pt too wide) in paragraph at lines 208--208
 [][][][]  
 []


Overfull \hbox (48.38454pt too wide) has occurred while \output is active
 [][][][]  
 []

[9

]
Overfull \hbox (48.38454pt too wide) in paragraph at lines 239--239
 [][][][]  
 []


LaTeX Warning: File `energy_convergence.png' not found on input line 239.


/Users/<USER>/Documents/Academic/Projects/Project_report/slides.tex:239: Package pdftex.def Error: File `energy_convergence.png' not found: using draft setting.

See the pdftex.def package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.239 \end{frame}
                 
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


/Users/<USER>/Documents/Academic/Projects/Project_report/slides.tex:239: LaTeX Error: \caption outside figure or table.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.239 \end{frame}
                 
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


LaTeX Warning: File `system_size_scaling.png' not found on input line 239.


/Users/<USER>/Documents/Academic/Projects/Project_report/slides.tex:239: Package pdftex.def Error: File `system_size_scaling.png' not found: using draft setting.

See the pdftex.def package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.239 \end{frame}
                 
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


/Users/<USER>/Documents/Academic/Projects/Project_report/slides.tex:239: LaTeX Error: \caption outside figure or table.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.239 \end{frame}
                 
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.


Overfull \vbox (152.39243pt too high) detected at line 239
 []


Overfull \hbox (48.38454pt too wide) has occurred while \output is active
 [][][][]  
 []

[10

]
Overfull \hbox (48.38454pt too wide) in paragraph at lines 258--258
 [][][][]  
 []


Overfull \hbox (48.38454pt too wide) has occurred while \output is active
 [][][][]  
 []

[11

]
Overfull \hbox (48.38454pt too wide) in paragraph at lines 264--264
 [][][][]  
 []


Overfull \hbox (48.38454pt too wide) has occurred while \output is active
 [][][][]  
 []

[12

]
\tf@nav=\write6
\openout6 = `slides.nav'.

\tf@toc=\write7
\openout7 = `slides.toc'.

\tf@snm=\write8
\openout8 = `slides.snm'.

 (./slides.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
 ***********


Package rerunfilecheck Warning: File `slides.out' has changed.
(rerunfilecheck)                Rerun to get outlines right
(rerunfilecheck)                or use package `bookmark'.

Package rerunfilecheck Info: Checksums for `slides.out':
(rerunfilecheck)             Before: <no file>
(rerunfilecheck)             After:  1F9C49C60CE124FCABD9275B6B01D321;285.
 ) 
Here is how much of TeX's memory you used:
 28149 strings out of 474116
 580107 string characters out of 5743683
 1944187 words of memory out of 5000000
 49915 multiletter control sequences out of 15000+600000
 578634 words of font info for 99 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 128i,15n,123p,454b,962s stack positions out of 10000i,1000n,20000p,200000b,200000s
</usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi8.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmss10.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmss12.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmss8.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmssi10.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmssi8.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy6.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy7.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy8.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmtt10.pfb></usr/local/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/symbols/msam10.pfb>
Output written on slides.pdf (12 pages, 169930 bytes).
PDF statistics:
 429 PDF objects out of 1000 (max. 8388607)
 362 compressed objects within 4 object streams
 27 named destinations out of 1000 (max. 500000)
 55 words of extra memory for PDF output out of 10000 (max. 10000000)

