{"cells": [{"cell_type": "markdown", "id": "5eb27e05", "metadata": {}, "source": ["## 1. <PERSON> Methods and the Sign Problem\n"]}, {"cell_type": "markdown", "id": "98f2be25", "metadata": {}, "source": ["Quantum many-body systems are a fundamental area of modern physics research, with implications for condensed matter physics, quantum computing, and materials science. However, these systems are notoriously difficult to solve exactly due to the exponential growth of the Hilbert space with the number of particles. This project aims to explore and implement Monte <PERSON> methods for simulating quantum many-body systems.\n", "\n", "The project will proceed incrementally, starting from basic Monte Carlo methods, introducing variational Monte Carlo techniques, employing neural networks as wave function representations, and finally implementing complex variational models based on Vision Transformers. This progressive approach will help us understand the strengths and limitations of various techniques and their potential for solving quantum many-body problems.\n", "\n", "We will use [NetKet](https://www.netket.org/) to simplify our code. NetKet is an open-source library specifically designed for quantum many-body simulations, particularly neural-network-based quantum state representations."]}, {"cell_type": "markdown", "id": "0a1a1c08", "metadata": {}, "source": ["Quantum many-body systems are a fundamental area of modern physics research, with implications for condensed matter physics, quantum computing, and materials science. However, these systems are notoriously difficult to solve exactly due to the exponential growth of the Hilbert space with the number of particles. This project aims to explore and implement Monte <PERSON> methods for simulating quantum many-body systems.\n", "\n", "The project will proceed incrementally, starting from basic Monte Carlo methods, introducing variational Monte Carlo techniques, employing neural networks as wave function representations, and finally implementing complex variational models based on Vision Transformers. This progressive approach will help us understand the strengths and limitations of various techniques and their potential for solving quantum many-body problems.\n", "\n", "We will use [NetKet](https://www.netket.org/) to simplify our code. NetKet is an open-source library specifically designed for quantum many-body simulations, particularly neural-network-based quantum state representations."]}, {"cell_type": "markdown", "id": "2fc6bdd7", "metadata": {}, "source": ["### 1.1 Introduction to <PERSON>"]}, {"cell_type": "markdown", "id": "a3379be0", "metadata": {}, "source": ["\n", "Quantum Monte Carlo (QMC) methods are powerful numerical techniques for solving quantum systems through random sampling. These methods are based on statistical sampling of the possible configurations of a system to calculate expectation values of physical observables. For a quantum many-body system, the ground state energy can be calculated using the variational principle:\n", "\n", "$$E_0 \\leq E[\\Psi_T] = \\frac{\\langle\\Psi_T|H|\\Psi_T\\rangle}{\\langle\\Psi_T|\\Psi_T\\rangle}$$\n", "\n", "where $\\Psi_T$ is a trial wave function. For a system of $N$ spin-1/2 particles, we can represent this wave function in the computational basis $\\{|S\\rangle\\}$, where $|S\\rangle = |S_1, S_2, \\ldots, S_N\\rangle$ and $S_i = \\pm 1$ represents spin up or down. The energy expectation value can be rewritten as:\n", "\n", "$$E[\\Psi_T] = \\frac{\\sum_S \\Psi_T^*(S) \\langle S|H|\\Psi_T\\rangle}{\\sum_S |\\Psi_T(S)|^2} = \\sum_S P(S) E_L(S)$$\n", "\n", "where $P(S) = \\frac{|\\Psi_T(S)|^2}{\\sum_{S'} |\\Psi_T(S')|^2}$ is a probability distribution and $E_L(S) = \\frac{\\langle S|H|\\Psi_T\\rangle}{\\Psi_T(S)}$ is the local energy.\n", "\n", "When $\\Psi_T(S)$ is real and non-negative, we can use Monte Carlo methods for importance sampling:\n", "1. Generate configuration samples $\\{S_1, S_2, ..., S_M\\}$ according to the probability distribution $P(S)$\n", "2. Calculate the local energies $E_L(S_i)$ for these samples\n", "3. Estimate the energy expectation value: $E[\\Psi_T] \\approx \\frac{1}{M}\\sum_{i=1}^M E_L(S_i)$\n"]}, {"cell_type": "code", "execution_count": 1, "id": "24e14d74", "metadata": {}, "outputs": [], "source": ["\n", "import numpy as np\n", "import netket as nk\n", "from netket.operator.spin import sigmax, sigmay, sigmaz\n", "from scipy.sparse.linalg import eigsh\n", "import warnings\n", "warnings.filterwarnings(\"ignore\", category=nk.errors.HolomorphicUndeclaredWarning)\n", "\n", "\n", "\n", "# Utility Functions\n", "def compute_ground_state(H, hi):\n", "    \"\"\"\n", "    Compute ground state energy and wave function for a given Hamiltonian\n", "    \n", "    Args:\n", "        H: The Hamiltonian operator\n", "        hi: <PERSON><PERSON> space\n", "        \n", "    Returns:\n", "        ground_state_energy: Lowest eigenvalue\n", "        ground_state: Corresponding eigenvector\n", "    \"\"\"\n", "    sp_h = H.to_sparse()\n", "    eig_vals, eig_vecs = eigsh(sp_h, k=1, which=\"SA\")\n", "    ground_state_energy = eig_vals[0]\n", "    ground_state = eig_vecs[:, 0]\n", "    return ground_state_energy, ground_state\n", "\n", "def qmc_energy_estimate(ground_state, L, n_samples, diag_multiplier, offdiag_multiplier):\n", "    \"\"\"\n", "    Estimate energy using Monte Carlo sampling\n", "    \n", "    - First samples from Hilbert space according to |ψ|²\n", "    - For each sampled configuration, calculates the local energy:\n", "         Local energy = (diagonal part) + (off-diagonal part)\n", "    - Diagonal part: For each nearest-neighbor pair i, contributes diag_multiplier * config[i]*config[(i+1)%L]\n", "    - Off-diagonal part: For each spin i in flipped configuration, contributes offdiag_multiplier * (ψ(flipped)/ψ(original))\n", "    \n", "    Args:\n", "        ground_state: Ground state wave function\n", "        L: System size\n", "        n_samples: Number of Monte Carlo samples\n", "        diag_multiplier: Coefficient for diagonal terms\n", "        offdiag_multiplier: Coefficient for off-diagonal terms\n", "        \n", "    Returns:\n", "        qmc_energy: Mean energy from Monte Carlo sampling\n", "        qmc_error: Standard error of the estimate\n", "    \"\"\"\n", "    dim = 2**L\n", "    prob_weights = np.abs(ground_state)**2\n", "    prob_weights /= np.sum(prob_weights)\n", "    sample_indices = np.random.choice(dim, size=n_samples, p=prob_weights)\n", "    \n", "    energy_samples = []\n", "    for idx in sample_indices:\n", "        bin_repr = format(idx, f\"0{L}b\")\n", "        config = np.array([1 - 2 * int(bit) for bit in bin_repr])\n", "        local_energy = 0\n", "        \n", "        # Diagonal part (e.g., nearest-neighbor interaction terms)\n", "        for i in range(L):\n", "            local_energy += diag_multiplier * config[i] * config[(i + 1) % L]\n", "        \n", "        # Off-diagonal part (e.g., spin-flip terms)\n", "        for i in range(L):\n", "            flipped_config = config.copy()\n", "            flipped_config[i] *= -1\n", "            # Generate binary string for flipped configuration, then convert to integer index\n", "            flipped_idx_str = ''.join(['1' if spin == -1 else '0' for spin in flipped_config])\n", "            flipped_idx_int = int(flipped_idx_str, 2)\n", "            psi_ratio = ground_state[flipped_idx_int] / ground_state[idx]\n", "            local_energy += offdiag_multiplier * psi_ratio\n", "\n", "        \n", "        energy_samples.append(local_energy)\n", "    \n", "    qmc_energy = np.mean(energy_samples)\n", "    qmc_error = np.std(energy_samples) / np.sqrt(n_samples)\n", "    return qmc_energy, qmc_error"]}, {"cell_type": "markdown", "id": "023d7da2", "metadata": {}, "source": ["\n", "A typical example without sign problems is the one-dimensional transverse field Ising model:\n", "\n", "$$H_{\\text{TFIM}} = -J\\sum_{i=1}^N \\sigma_i^z \\sigma_{i+1}^z - h\\sum_{i=1}^N \\sigma_i^x$$\n", "\n", "In the weak transverse field regime where $h \\ll J$, this model serves as a good example without sign problems in the $σ^z$ basis.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "efd88f89", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["====== Transverse Field Ising Model Simulation ======\n", "Exact diagonalization energy: -10.25166179\n", "QMC estimated energy: -10.25166179 ± 0.00000000\n", "Relative error: 0.00%\n", "\n"]}], "source": ["def tfim_model_sign(L=8, J=1.0, h=1.0, n_samples=100000):\n", "    \"\"\"\n", "    QMC simulation of 1D transverse field Ising model, demonstrating sign problem detection\n", "    Hamiltonian: H = J ∑ σ_i^z σ_{i+1}^z + h ∑ σ_i^x\n", "\n", "    Args:\n", "        L: System size (chain length)\n", "        J: Nearest-neighbor coupling (positive for antiferromagnetic, negative for ferromagnetic)\n", "        h: Transverse field strength\n", "        n_samples: Number of Monte Carlo samples\n", "        \n", "    Returns:\n", "        exact_energy: Energy from exact diagonalization\n", "        qmc_energy: Energy from QMC estimation\n", "        qmc_error: Error in QMC estimation\n", "    \"\"\"\n", "    # Define the <PERSON>lbert space (spin 1/2)\n", "    hi = nk.hilbert.Spin(s=1/2, N=L)\n", "    \n", "    # Construct transverse field Ising model Hamiltonian\n", "    H = sum([J * sigmaz(hi, i) * sigmaz(hi, (i + 1) % L) for i in range(L)])\n", "    H += sum([h * sigmax(hi, i) for i in range(L)])\n", "    \n", "    # Obtain ground state (exact diagonalization)\n", "    exact_energy, ground_state = compute_ground_state(H, hi)\n", "    \n", "    # Calculate energy using QMC sampling\n", "    qmc_energy, qmc_error = qmc_energy_estimate(ground_state, L, n_samples,\n", "                                                diag_multiplier=J,\n", "                                                offdiag_multiplier=h)\n", "    \n", "    return exact_energy, qmc_energy, qmc_error\n", "\n", "if __name__ == '__main__':\n", "    np.random.seed(42)\n", "    \n", "    print(\"====== Transverse Field Ising Model Simulation ======\")\n", "    L = 8        # System size\n", "    J = 1.0      # Coupling\n", "    h = 1.0      # Transverse field strength\n", "    n_samples = 100000\n", "    exact_energy, qmc_energy, qmc_error = tfim_model_sign(L, J, h, n_samples)\n", "    print(f\"Exact diagonalization energy: {exact_energy:.8f}\")\n", "    print(f\"QMC estimated energy: {qmc_energy:.8f} ± {qmc_error:.8f}\")\n", "    print(f\"Relative error: {abs(qmc_energy - exact_energy) / abs(exact_energy) * 100:.2f}%\\n\")\n"]}, {"cell_type": "markdown", "id": "49684509", "metadata": {}, "source": ["\n", "### 1.2 The Sign Problem\n"]}, {"cell_type": "markdown", "id": "10cae6a4", "metadata": {}, "source": ["\n", "The sign problem is one of the core challenges in quantum Monte Carlo methods. When the wave function $\\Psi_T(S)$ takes complex or negative values for certain configurations, standard Monte Carlo methods cannot be applied directly.\n", "\n", "Mathematically, the sign problem can be understood as follows. For a wave function with phase structure, we can decompose it as:\n", "\n", "$$\\Psi_T(S) = |\\Psi_T(S)| \\cdot e^{i\\theta(S)}$$\n", "\n", "where $e^{i\\theta(S)}$ is the phase factor containing phase information of the wave function. The local energy becomes:\n", "\n", "$$\\tilde{E}_L(S) = \\frac{e^{-i\\theta(S)} \\langle S|H|\\Psi_T\\rangle}{|\\Psi_T(S)|}$$\n", "\n", "If we still use the same sampling approach, then \n", "$$E[\\Psi_T] = \\sum_S e^{-i\\theta(S)} P(S) \\cdot E_L(S)=\\sum_S \\tilde{P}(S) \\cdot E_L(S)$$\n", "\n", "The sampling probability contains phase information, and it may even be negative! This leads to critical issues:\n", "\n", "1. When the wave function has different phases for different configurations, sampled configurations will cancel each other out in terms of phase, resulting in a reduced effective sample size\n", "2. An exponential increase in the number of samples is required to achieve acceptable precision\n"]}, {"cell_type": "markdown", "id": "183a7b86", "metadata": {}, "source": ["\n", "The antiferromagnetic Heisenberg model Hamiltonian can be expressed as:\n", "\n", "$$H_{\\text{<PERSON><PERSON><PERSON>}} = J \\sum_{\\langle i,j \\rangle} \\vec{\\sigma}_i \\cdot \\vec{\\sigma}_j = J \\sum_{\\langle i,j \\rangle} (\\sigma^x_i \\sigma^x_j + \\sigma^y_i \\sigma^y_j + \\sigma^z_i \\sigma^z_j)$$\n", "\n", "This model consists of:\n", "- $J > 0$ represents the antiferromagnetic coupling strength\n", "- The summation is over nearest-neighbor lattice site pairs $\\langle i,j \\rangle$\n", "- It includes spin-spin interactions in all three directions\n", "\n", "When considered in the $\\sigma^z$ basis, the Hamiltonian can be divided into two parts:\n", "- The $\\sigma^z_i \\sigma^z_j$ terms are diagonal terms, similar to the classical Ising model, where antiferromagnetic coupling makes adjacent spins tend to align antiparallel\n", "- The $\\sigma^x_i \\sigma^x_j + \\sigma^y_i \\sigma^y_j$ terms can be rewritten as $\\frac{1}{2}(\\sigma^+_i \\sigma^-_j + \\sigma^-_i \\sigma^+_j)$, which are off-diagonal terms representing spin exchange processes\n", "\n", "These spin exchange terms are off-diagonal in the $\\sigma^z$ representation and cause adjacent spins to flip simultaneously:\n", "\n", "$$\\sigma^+_i \\sigma^-_j |\\uparrow_i \\downarrow_j\\rangle = |\\downarrow_i \\uparrow_j\\rangle$$\n", "\n", "This spin exchange process establishes complex phase relationships between different spin configurations. Particularly in fermion systems, particle exchanges introduce negative signs, making the wave function positive for some configurations and negative for others.\n", "\n", "Unlike the transverse field Ising model, the antiferromagnetic Heisenberg model has a sign problem at any non-zero temperature, with no simple parameter adjustment to eliminate it. This makes quantum Monte Carlo simulations of large-scale antiferromagnetic Heisenberg systems extremely challenging, especially on non-bipartite lattices in two or higher dimensions where the sign problem is particularly severe.\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "id": "08d9bb2a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["====== Antiferromagnetic Heisenberg Model Simulation ======\n", "Exact diagonalization energy: -14.60437364\n", "QMC estimated energy: -4.87400000-0.00000000j ± 0.00770577\n", "Relative error: 66.63%\n"]}], "source": ["\n", "def heisenberg_model_signproblem(L=8, J=1.0, n_samples=100000):\n", "    \"\"\"\n", "    QMC simulation of antiferromagnetic Heisenberg model, demonstrating the sign problem\n", "    Hamiltonian: H = J ∑ (σ_i^xσ_{i+1}^x + σ_i^yσ_{i+1}^y + σ_i^zσ_{i+1}^z)\n", "\n", "    Args:\n", "        L: System size\n", "        J: Spin coupling strength (J > 0 for antiferromagnetic, prone to sign problems)\n", "        n_samples: Number of Monte Carlo samples\n", "        \n", "    Returns:\n", "        exact_energy: Energy from exact diagonalization\n", "        qmc_energy: Energy from QMC estimation\n", "        qmc_error: Error in QMC estimation\n", "    \"\"\"\n", "    # Define the <PERSON>lbert space (spin 1/2)\n", "    hi = nk.hilbert.Spin(s=1/2, N=L)\n", "    \n", "    # Construct the Heisenberg Hamiltonian\n", "    H = 0\n", "    for i in range(L):\n", "        H += J * (sigmax(hi, i) * sigmax(hi, (i + 1) % L) +\n", "                  sigmay(hi, i) * sigmay(hi, (i + 1) % L) +\n", "                  sigmaz(hi, i) * sigmaz(hi, (i + 1) % L))\n", "    \n", "    # Obtain ground state (exact diagonalization), note: ground state may contain both positive and negative coefficients\n", "    exact_energy, ground_state = compute_ground_state(H, hi)\n", "    \n", "    # Calculate energy using QMC sampling\n", "    # For the Heisenberg model, off-diagonal term coefficients are the same as J\n", "    qmc_energy, qmc_error = qmc_energy_estimate(ground_state, L, n_samples,\n", "                                                diag_multiplier=J,\n", "                                                offdiag_multiplier=J)\n", "    \n", "    return exact_energy, qmc_energy, qmc_error\n", "\n", "if __name__ == '__main__':\n", "    np.random.seed(42)\n", "\n", "    print(\"====== Antiferromagnetic Heisenberg Model Simulation ======\")\n", "    exact_energy, qmc_energy, qmc_error = heisenberg_model_signproblem(L, J, n_samples)\n", "    print(f\"Exact diagonalization energy: {exact_energy:.8f}\")\n", "    print(f\"QMC estimated energy: {qmc_energy:.8f} ± {qmc_error:.8f}\")\n", "    print(f\"Relative error: {abs(qmc_energy - exact_energy) / abs(exact_energy) * 100:.2f}%\")\n"]}, {"cell_type": "markdown", "id": "4d9b33ca", "metadata": {}, "source": ["\n", "We can observe that even using the same sampling method, models without sign problems can yield accurate results, while models with sign problems lead to sampling failures.\n"]}, {"cell_type": "markdown", "id": "b597ce4e", "metadata": {}, "source": ["\n", "### 1.3 Solutions to the Sign Problem"]}, {"cell_type": "markdown", "id": "d3cc292a", "metadata": {}, "source": ["\n", "To address the sign problem, researchers have proposed various methods, including:\n", "\n", "1. **Fixed-Node Approximation**: In fermion systems, avoiding the sign problem by fixing the nodal structure of the wave function.\n", "2. **Auxiliary Field Quantum Monte Carlo**: Transforming many-body problems into path integrals of single-body problems by introducing auxiliary fields, which can alleviate the sign problem in certain cases. For specific models (such as half-filled Hubbard models), this can completely eliminate the sign problem.\n", "3. **Variational Monte Carlo (VMC)**: Using parameterized trial wave functions and optimizing parameters through the variational principle, bypassing the direct handling of the sign problem.\n", "\n", "In the following sections, we will focus on exploring the variational Monte Carlo method, which can effectively circumvent sign problems.\n"]}, {"cell_type": "markdown", "id": "618cdd3f", "metadata": {}, "source": ["\n", "## 2. Variational Monte Carlo\n"]}, {"cell_type": "markdown", "id": "7636348c", "metadata": {}, "source": ["\n", "### 2.1 Principles of Variational Monte Carlo\n"]}, {"cell_type": "markdown", "id": "efb3f26b", "metadata": {}, "source": ["\n", "The variational Monte Carlo (VMC) method is based on the variational principle in quantum mechanics, which states that the energy expectation value of any trial wave function is never lower than the true ground state energy of the system. The core idea of VMC is:\n", "\n", "1. Select a trial wave function $|\\Psi_{\\boldsymbol{\\theta}}\\rangle$ dependent on variational parameters $\\boldsymbol{\\theta}$\n", "2. Calculate the energy expectation value $E(\\boldsymbol{\\theta}) = \\frac{\\langle\\Psi_{\\boldsymbol{\\theta}}|H|\\Psi_{\\boldsymbol{\\theta}}\\rangle}{\\langle\\Psi_{\\boldsymbol{\\theta}}|\\Psi_{\\boldsymbol{\\theta}}\\rangle}$\n", "3. Use optimization algorithms to find parameters $\\boldsymbol{\\theta}_{\\text{opt}}$ that minimize the energy\n", "\n", "The energy expectation value can be calculated using <PERSON> methods:\n", "\n", "$$E(\\boldsymbol{\\theta}) = \\frac{\\sum_S |\\Psi_{\\boldsymbol{\\theta}}(S)|^2 \\frac{\\langle S|H|\\Psi_{\\boldsymbol{\\theta}}\\rangle}{\\Psi_{\\boldsymbol{\\theta}}(S)}}{\\sum_S |\\Psi_{\\boldsymbol{\\theta}}(S)|^2} = \\sum_S P(S) E_L(S) = \\left\\langle E_L(S) \\right\\rangle_{|\\Psi_{\\boldsymbol{\\theta}}|^2}$$\n", "\n", "where $E_L(S)$ is the local energy that absorbs phase factors through the parameterized trial wave function. $\\langle \\cdot \\rangle_{|\\Psi_{\\boldsymbol{\\theta}}|^2}$ denotes expectation values with respect to the probability distribution $|\\Psi_{\\boldsymbol{\\theta}}(S)|^2$.\n", "\n", "The energy gradient can be expressed as:\n", "\n", "$$\\nabla_{\\boldsymbol{\\theta}} E(\\boldsymbol{\\theta}) = 2\\text{Re}\\left\\langle E_L(S) \\frac{\\nabla_{\\boldsymbol{\\theta}} \\Psi_{\\boldsymbol{\\theta}}(S)}{\\Psi_{\\boldsymbol{\\theta}}(S)} \\right\\rangle_{|\\Psi_{\\boldsymbol{\\theta}}|^2} - 2\\text{Re}\\left\\langle E_L(S) \\right\\rangle_{|\\Psi_{\\boldsymbol{\\theta}}|^2} \\left\\langle \\frac{\\nabla_{\\boldsymbol{\\theta}} \\Psi_{\\boldsymbol{\\theta}}(S)}{\\Psi_{\\boldsymbol{\\theta}}(S)} \\right\\rangle_{|\\Psi_{\\boldsymbol{\\theta}}|^2}$$\n"]}, {"cell_type": "markdown", "id": "ec2e8f6d", "metadata": {}, "source": ["\n", "### 2.2 1D <PERSON>: Comparing Jastrow and RBM Models\n"]}, {"cell_type": "markdown", "id": "547ce8f6", "metadata": {}, "source": ["\n", "Below we construct a one-dimensional periodic Heisenberg model and solve for the ground state energy using both Jastrow and RBM trial wave functions.\n", "$$H_{\\text{<PERSON><PERSON><PERSON>}} = J \\sum_{\\langle i,j \\rangle} \\vec{\\sigma}_i \\cdot \\vec{\\sigma}_j = J \\sum_{\\langle i,j \\rangle} (\\sigma^x_i \\sigma^x_j + \\sigma^y_i \\sigma^y_j + \\sigma^z_i \\sigma^z_j)$$\n"]}, {"cell_type": "markdown", "id": "704e0532", "metadata": {}, "source": ["\n", "First, let's define the one-dimensional Heisenberg chain:\n"]}, {"cell_type": "code", "execution_count": 4, "id": "6242ce28", "metadata": {}, "outputs": [], "source": ["\n", "# Set system parameters\n", "L = 16\n", "\n", "# Create a one-dimensional periodic chain (single-dimension lattice)\n", "graph_chain = nk.graph.Hypercube(length=L, n_dim=1, pbc=True)\n", "hilbert_chain = nk.hilbert.Spin(s=1/2, total_sz=0, N=graph_chain.n_nodes)\n", "\n", "# Heisenberg model: nearest-neighbor interactions with coupling constant J=1.0\n", "ham_heisenberg = nk.operator.Heisenberg(hilbert=hilbert_chain, graph=graph_chain)\n", "\n", "# Create sampler\n", "sampler = nk.sampler.MetropolisExchange(hilbert=hilbert_chain, graph=graph_chain)\n", "\n", "# Define optimizer and stochastic reconfiguration (SR) parameters\n", "optimizer = nk.optimizer.Sgd(learning_rate=0.01)\n", "sr = nk.optimizer.SR(diag_shift=0.1)\n", "n_iter = 600\n", "n_samples = 3200\n"]}, {"cell_type": "markdown", "id": "36c89e91", "metadata": {}, "source": ["\n", "Let's define our optimization function:\n"]}, {"cell_type": "code", "execution_count": 5, "id": "edc271f9", "metadata": {}, "outputs": [], "source": ["def run_vmc_simulation(hamiltonian, hilbert, sampler, model, optimizer, sr, n_iter, n_samples, out_label):\n", "    \"\"\"\n", "    Encapsulates VMC simulation process, returns final energy and relative error\n", "  \n", "    Args:\n", "        hamiltonian: NetKet defined Hamiltonian\n", "        hilbert: Hilbert space\n", "        sampler: Sampler object\n", "        model: Trial wave function model (e.g., Jastrow, RBM, or custom ViT model)\n", "        optimizer: Optimizer object\n", "        sr: Stochastic Reconfiguration preprocessor\n", "        n_iter: Number of iterations\n", "        n_samples: Number of Monte Carlo samples\n", "        out_label: Log file save label\n", "        \n", "    Returns:\n", "        final_energy: Mean energy after VMC optimization\n", "        relative_error: Relative error (compared to exact energy)\n", "        exact_energy: Energy from exact diagonalization (Lanczos ED)\n", "    \"\"\"\n", "    # Create variational state (MCState)\n", "    vs = nk.vqs.MCState(sampler, model, n_samples=n_samples)\n", "    # Construct VMC object\n", "    vmc_driver = nk.VMC(\n", "        hamiltonian=hamiltonian,\n", "        optimizer=optimizer,\n", "        preconditioner=sr,\n", "        variational_state=vs\n", "    )\n", "    # Calculate exact energy (<PERSON><PERSON><PERSON><PERSON> diagonalization, only suitable for small system sizes)\n", "    evals = nk.exact.lanczos_ed(hamiltonian, compute_eigenvectors=False)\n", "    exact_energy = evals[0]\n", "    \n", "    # Iterative optimization\n", "    vmc_driver.run(n_iter, out=out_label)\n", "    \n", "    final_energy = vs.expect(hamiltonian).mean\n", "    relative_error = abs(final_energy - exact_energy) / abs(exact_energy)\n", "    \n", "    return final_energy, relative_error, exact_energy"]}, {"cell_type": "markdown", "id": "2af8d393", "metadata": {}, "source": ["\n", "#### 2.2.1 Simple Jastrow Trial Wave Function\n"]}, {"cell_type": "markdown", "id": "273f569b", "metadata": {}, "source": ["\n", "We first implement a simple VMC model using a <PERSON><PERSON>row factor as the trial wave function:\n", "\n", "$$\\Psi_{\\boldsymbol{\\theta}}(S) = \\exp\\left(\\sum_{i,j} J_{ij} S_i S_j+ \\sum_{i} a_i S_i\\right) $$\n", "\n", "where $\\alpha_{ij}$ are variational parameters to be optimized and $S_i$ are spin variables. This trial wave function can capture two-body correlations between spins.\n"]}, {"cell_type": "code", "execution_count": 6, "id": "9293aa10", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6bbb1c09a5e14046a67eeb133fa1f03c", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/600 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["【Heisenberg Model - <PERSON><PERSON><PERSON>】\n", "Exact energy: -28.56918544\n", "VMC final energy: -28.55141857+0.00000000j\n", "Relative error: 0.06%\n", "\n"]}], "source": ["# Solve using <PERSON><PERSON><PERSON> model\n", "model_jastrow = nk.models.Jastrow()\n", "final_energy_jastrow, rel_err_jastrow, exact_energy_heis = run_vmc_simulation(\n", "    ham_heisenberg, hilbert_chain, sampler, model_jastrow,\n", "    optimizer, sr, n_iter, n_samples, out_label=\"Jastrow\"\n", ")\n", "print(\"【Heisenberg Model - Jastrow】\")\n", "print(f\"Exact energy: {exact_energy_heis:.8f}\")\n", "print(f\"VMC final energy: {final_energy_jastrow:.8f}\")\n", "print(f\"Relative error: {rel_err_jastrow*100:.2f}%\\n\")"]}, {"cell_type": "markdown", "id": "e4a1e688", "metadata": {}, "source": ["\n", "#### 2.2.2 Neural Quantum States (NQS)\n", "\n", "Neural Quantum States (NQS) are a method for representing quantum many-body wave functions using artificial neural networks. This approach, proposed by <PERSON><PERSON> and <PERSON> in 2017, has been successfully applied to the study of various quantum systems. The core idea of NQS is to utilize the powerful representation capabilities of neural networks to approximate complex wave functions, especially those difficult to handle by traditional methods.\n", "\n", "In NQS, the wave function is represented as:\n", "\n", "$$\\Psi_{\\boldsymbol{\\theta}}(S) = \\langle S|\\Psi_{\\boldsymbol{\\theta}}\\rangle$$\n", "\n", "where $|\\Psi_{\\boldsymbol{\\theta}}\\rangle$ is a quantum state determined by neural network parameters $\\boldsymbol{\\theta}$, and $S$ is the system configuration.\n", "\n", "Common NQS implementations use Restricted Boltzmann Machines (RBM) as the neural network structure:\n", "\n", "$$\\Psi_{\\text{RBM}}(S) = e^{\\sum_i c_i S_i} \\prod_{j=1}^M 2\\cosh\\left(b_j + \\sum_i W_{ij}S_i\\right)$$\n", "\n", "where:\n", "- $S_i$ are visible neurons (corresponding to physical spins)\n", "- $c_i$ are visible biases\n", "- $b_j$ are hidden biases\n", "- $W_{ij}$ are connection weights\n", "- $M$ is the number of hidden neurons\n", "\n", "The advantage of RBM is that it can represent arbitrarily complex wave functions, including those with negative values or complex phases, thereby effectively solving the sign problem.\n", "\n", "<img src=\"RBM.png\" alt=\"RBM\" width=\"300\"/>"]}, {"cell_type": "code", "execution_count": 7, "id": "c39fe877", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4c64fb722d7846049e5c28e6c731a0ca", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/600 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["【Heisenberg Model - RBM】\n", "Exact energy: -28.56918544\n", "VMC final energy: -28.55454150\n", "Relative error: 0.05%\n", "\n"]}], "source": ["\n", "# Solve using RBM model (alpha=1)\n", "model_rbm = nk.models.RBM(alpha=1)\n", "final_energy_rbm, rel_err_rbm, _ = run_vmc_simulation(\n", "    ham_heisenberg, hilbert_chain, sampler, model_rbm,\n", "    optimizer, sr, n_iter, n_samples, out_label=\"RBM\"\n", ")\n", "print(\"【Heisenberg Model - RBM】\")\n", "print(f\"Exact energy: {exact_energy_heis:.8f}\")\n", "print(f\"VMC final energy: {final_energy_rbm:.8f}\")\n", "print(f\"Relative error: {rel_err_rbm*100:.2f}%\\n\")"]}, {"cell_type": "markdown", "id": "bfdd6f80", "metadata": {}, "source": ["\n", "Comparing relative errors reveals that the RBM wave function has greater expressivity than the <PERSON><PERSON><PERSON> wave function. Let's plot energy vs. iterations to visually demonstrate the training process:\n"]}, {"cell_type": "code", "execution_count": 8, "id": "113bf932", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import json\n", "import matplotlib.pyplot as plt\n", "\n", "# Read Jastrow log data\n", "with open(\"Jastrow.log\", \"r\") as f:\n", "    data_Jastrow = json.load(f)\n", "iters_Jastrow = data_Jastrow[\"Energy\"][\"iters\"]\n", "# If energy is in complex format, take its real part\n", "energy_Jastrow = data_Jastrow[\"Energy\"][\"Mean\"][\"real\"]\n", "\n", "# Read RBM log data\n", "with open(\"RBM.log\", \"r\") as f:\n", "    data_RBM = json.load(f)\n", "iters_RBM = data_RBM[\"Energy\"][\"iters\"]\n", "energy_RBM = data_RBM[\"Energy\"][\"Mean\"]\n", "\n", "# Plot figure\n", "plt.figure(figsize=(8, 6))\n", "plt.plot(iters_<PERSON><PERSON><PERSON>, energy_Jastrow, label=\"Jastrow\")\n", "plt.plot(iters_RBM, energy_RBM, label=\"RBM\")\n", "plt.axhline(y=exact_energy_heis, xmin=0,\n", "                xmax=iters_J<PERSON>row[-1], linewidth=2, color='k', label='Exact')\n", "plt.xlabel(\"Iteration\")\n", "plt.ylabel(\"Energy\")\n", "plt.title(\"Energy vs Iterations\")\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "0fdd6664", "metadata": {}, "source": ["\n", "## 3. Advanced Variational Monte Carlo Model Example: Vision Transformer Model\n"]}, {"cell_type": "markdown", "id": "e3da4798", "metadata": {}, "source": ["\n", "### 3.1 Introduction to Vision Transformer Model\n", "\n", "Vision Transformer (ViT) is a neural network architecture based on self-attention mechanisms, originally designed for image processing tasks. We can apply this architecture to quantum many-body systems, utilizing self-attention mechanisms to capture long-range quantum correlations.\n", "\n", "The basic idea of applying ViT to quantum systems is:\n", "\n", "1. View spin configurations as \"images\"\n", "2. Divide configurations into multiple \"patches\"\n", "3. Use self-attention mechanisms to process correlations between patches\n", "4. Map to wave function values through fully connected layers at the end\n", "\n", "The advantage of ViT is its ability to effectively capture spin correlations at arbitrary distances, not limited by architectures such as RBM.\n", "\n", "<img src=\"ViT.png\" alt=\"ViT\" width=\"300\"/>"]}, {"cell_type": "markdown", "id": "1c7b6c10", "metadata": {}, "source": ["\n", "### 3.2 Building a ViT Quantum Model with Flax\n", "\n", "We use JAX and Flax libraries to build a ViT model for quantum systems:\n"]}, {"cell_type": "code", "execution_count": 9, "id": "ad68543b", "metadata": {}, "outputs": [], "source": ["import jax\n", "import jax.numpy as jnp\n", "from flax import linen as nn\n", "from typing import Sequence\n", "import netket as nk\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import time\n", "import json\n", "\n", "# Define core modules for Vision Transformer (ViT) model\n", "def custom_uniform(scale=1e-2, dtype=jnp.float_):\n", "    \"\"\"\n", "    Custom weight initialization function for neural network parameters\n", "    \"\"\"\n", "    def init(key, shape, dtype=dtype):\n", "        dtype = jax.dtypes.canonicalize_dtype(dtype)\n", "        return jax.random.uniform(key, shape, dtype, minval=-scale, maxval=scale)\n", "    return init\n", "\n", "@jax.jit\n", "def log_cosh(x):\n", "    \"\"\"\n", "    Log-cosh activation function, a smooth approximation of ReLU\n", "    that works well for quantum wave function representation\n", "    \"\"\"\n", "    sgn_x = -2 * jnp.signbit(x.real) + 1\n", "    x = x * sgn_x\n", "    return x + jnp.log1p(jnp.exp(-2.0 * x)) - jnp.log(2.0)\n", "\n", "@jax.jit\n", "def attention(J, values, shift):\n", "    \"\"\"\n", "    Custom attention mechanism for quantum systems\n", "    \"\"\"\n", "    # Cyclic shift values, then weighted sum by J\n", "    values = jnp.roll(values, shift, axis=0)\n", "    return jnp.sum(J * values, axis=0)\n", "\n", "class EncoderBlock(nn.Module):\n", "    \"\"\"\n", "    Transformer encoder block with custom attention for quantum systems\n", "    \"\"\"\n", "    d_model: int  # Model dimension\n", "    h: int        # Number of attention heads\n", "    L: int        # Number of sequence blocks\n", "    b: int        # Size per block\n", "\n", "    def setup(self):\n", "        scale = (3.0 * 0.7 / self.L) ** 0.5\n", "        # Value projections (real and imaginary parts)\n", "        self.v_projR = nn.<PERSON>(self.d_model, param_dtype=jnp.float64,\n", "                                kernel_init=jax.nn.initializers.variance_scaling(0.3, \"fan_in\", \"uniform\"),\n", "                                bias_init=nn.initializers.zeros)\n", "        self.v_projI = nn.<PERSON>(self.d_model, param_dtype=jnp.float64,\n", "                                kernel_init=jax.nn.initializers.variance_scaling(0.3, \"fan_in\", \"uniform\"),\n", "                                bias_init=nn.initializers.zeros)\n", "        # Attention weights (complex)\n", "        self.JR = self.param(\"JR\", custom_uniform(scale=scale), (self.L, self.h, 1), jnp.float64)\n", "        self.JI = self.param(\"JI\", custom_uniform(scale=scale), (self.L, self.h, 1), jnp.float64)\n", "        # Output projection\n", "        self.W0R = nn.<PERSON><PERSON>(self.d_model, param_dtype=jnp.float64,\n", "                            kernel_init=jax.nn.initializers.variance_scaling(0.065, \"fan_in\", \"uniform\"),\n", "                            bias_init=nn.initializers.zeros)\n", "        self.W0I = nn.<PERSON><PERSON>(self.d_model, param_dtype=jnp.float64,\n", "                            kernel_init=jax.nn.initializers.variance_scaling(0.065, \"fan_in\", \"uniform\"),\n", "                            bias_init=nn.initializers.zeros)\n", "\n", "    def __call__(self, x):\n", "        # Construct complex attention weights\n", "        J = self.JR + 1j * self.JI\n", "        # Project values and convert to complex form, reshape to (L, h, dim)\n", "        x = self.v_projR(x).reshape(self.L, self.h, -1) + 1j * self.v_projI(x).reshape(self.L, self.h, -1)\n", "        # Apply attention for each position\n", "        x = jax.vmap(attention, (None, None, 0))(J, x, jnp.arange(self.L))\n", "        x = x.reshape(self.L, -1)\n", "        # Output projection\n", "        x = self.W0R(x) + 1j * self.W0I(x)\n", "        return log_cosh(x)\n", "\n", "class TransformerEnc(nn.Module):\n", "    \"\"\"\n", "    Full Transformer encoder for quantum wave function representation\n", "    \"\"\"\n", "    d_model: int  # Model dimension\n", "    h: int        # Number of attention heads\n", "    L: int        # Number of sequence blocks\n", "    b: int        # Size per block\n", "\n", "    def setup(self):\n", "        self.encoder = EncoderBlock(self.d_model, self.h, self.L, self.b)\n", "\n", "    def __call__(self, x):\n", "        # Reshape input into blocks: [num_blocks, block_size, batch_size]\n", "        x = x.reshape(x.shape[0], -1, self.b)\n", "        x = jax.vmap(self.encoder)(x)\n", "        # Pool features from all blocks (sum)\n", "        return jnp.sum(x, axis=(1, 2))"]}, {"cell_type": "markdown", "id": "27ad19de", "metadata": {}, "source": ["\n", "### 3.3 Comparing RBM and ViT Models on the 1D J1-J2 Model\n"]}, {"cell_type": "markdown", "id": "cb05c55a", "metadata": {}, "source": ["\n", "#### 3.3.1 Introduction to the 1D J1-J2 Model\n", "\n", "The one-dimensional J1-J2 model is a quantum many-body system with competing interactions, described by the Hamiltonian:\n", "$$H = J_1 \\sum_{i} \\vec{\\sigma}_i \\cdot \\vec{\\sigma}_{i+1} + J_2 \\sum_{i} \\vec{\\sigma}_i \\cdot \\vec{\\sigma}_{i+2}$$\n", "where $J_1$ and $J_2$ represent coupling constants for nearest-neighbor and next-nearest-neighbor interactions, respectively. \n", "\n", "\n", "\n", "\n", "##### Frustration Characteristics\n", "\n", "A core feature of the J1-J2 model is quantum frustration. When $J_1$ and $J_2$ have specific relationships, situations arise where not all interactions can be simultaneously satisfied:\n", "\n", "- When $J_2/J_1 > 0.241167$, the system transitions from a Néel state to a dimerized state\n", "- Frustration stems from competition between different interactions, making it impossible for the system to minimize all energy terms simultaneously\n", "- This competition lead to highly degenerate ground states or complex quantum phase structures\n", "\n", "\n", "Frustration characteristics significantly affect the \"sign problem\" in quantum simulations, when frustration is strong in antiferromagnetic $J_1$ cases, the sign problem becomes extremely severe\n", "\n"]}, {"cell_type": "code", "execution_count": 10, "id": "36252b5c", "metadata": {}, "outputs": [], "source": ["# Create a one-dimensional chain lattice (allowing next-nearest-neighbor interactions)\n", "lattice_j1j2 = nk.graph.Chain(length=L, pbc=True, max_neighbor_order=2)\n", "hilbert_j1j2 = nk.hilbert.Spin(s=1/2, N=lattice_j1j2.n_nodes, total_sz=0)\n", "\n", "# Define J1-J2 Heisenberg model: J1=1.0, J2=0.5\n", "ham_j1j2 = nk.operator.Heisenberg(hilbert=hilbert_j1j2, graph=lattice_j1j2, J=[1.0, 0.5])\n", "sampler_j1j2 = nk.sampler.MetropolisExchange(hilbert=hilbert_j1j2, graph=lattice_j1j2)"]}, {"cell_type": "markdown", "id": "08493f91", "metadata": {}, "source": ["\n", "#### 3.3.2 RBM Training\n", "\n", "Let's first check the accuracy of the RBM model:\n"]}, {"cell_type": "code", "execution_count": 11, "id": "0c42493d", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "320db727a2a94300a4b2437f464b6d2d", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/600 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["【J1-J2 Model - RBM】\n", "Exact energy: -24.00000000\n", "VMC final energy: -6.50000000\n", "Relative error: 72.92%\n", "\n"]}], "source": ["\n", "# Solve the J1-J2 model using the RBM model\n", "final_energy_rbm_j1j2, rel_err_rbm_j1j2, exact_energy_j1j2 = run_vmc_simulation(\n", "    ham_j1j2, hilbert_j1j2, sampler_j1j2, model_rbm,\n", "    optimizer, sr, n_iter, n_samples, out_label=\"RBM_J1J2\"\n", ")\n", "print(\"【J1-J2 Model - RBM】\")\n", "print(f\"Exact energy: {exact_energy_j1j2:.8f}\")\n", "print(f\"VMC final energy: {final_energy_rbm_j1j2:.8f}\")\n", "print(f\"Relative error: {rel_err_rbm_j1j2*100:.2f}%\\n\")"]}, {"cell_type": "markdown", "id": "521351ec", "metadata": {}, "source": ["\n", "#### 3.3.3 ViT Training\n", "\n", "Now let's check the accuracy of the ViT model:\n"]}, {"cell_type": "code", "execution_count": 12, "id": "7f0c8bd7", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d169f5df3cf043e7a435d5524cd09ff3", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/600 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["【J1-J2 Model - ViT】\n", "Exact energy: -24.00000000\n", "VMC final energy: -24.00085138-0.00084533j\n", "Relative error: 0.00%\n", "\n"]}], "source": ["# ViT model parameters\n", "heads = 8\n", "feature_factor = 1\n", "batch_size = 4\n", "d_model = feature_factor * heads\n", "\n", "# Build ViT model instance, note input size should match Hilbert space dimensions\n", "vit_model = TransformerEnc(d_model=d_model, h=heads, L=L // batch_size, b=batch_size)\n", "\n", "# For ViT, we need to use custom samplers and initialize parameters\n", "seed = 0\n", "sampler_j1j2_vit = nk.sampler.MetropolisExchange(hilbert=hilbert_j1j2, graph=lattice_j1j2)\n", "key = jax.random.<PERSON><PERSON><PERSON><PERSON>(seed)\n", "params = vit_model.init(key, jnp.zeros((1, hilbert_j1j2.size)))\n", "vs_vit = nk.vqs.MCState(sampler_j1j2_vit, vit_model, n_samples=n_samples, variables=params)\n", "\n", "# Construct VMC optimizer object\n", "vmc_driver_vit = nk.VMC(\n", "    hamiltonian=ham_j1j2,\n", "    optimizer=optimizer,\n", "    preconditioner=sr,\n", "    variational_state=vs_vit\n", ")\n", "\n", "# Run ViT model optimization\n", "vmc_driver_vit.run(n_iter, out=\"ViT_J1J2\")\n", "final_energy_vit = vs_vit.expect(ham_j1j2).mean\n", "evals_vit = nk.exact.lanczos_ed(ham_j1j2, compute_eigenvectors=False)\n", "exact_energy_j1j2_vit = evals_vit[0]\n", "rel_err_vit = abs(final_energy_vit - exact_energy_j1j2_vit) / abs(exact_energy_j1j2_vit)\n", "\n", "print(\"【J1-J2 Model - ViT】\")\n", "print(f\"Exact energy: {exact_energy_j1j2_vit:.8f}\")\n", "print(f\"VMC final energy: {final_energy_vit:.8f}\")\n", "print(f\"Relative error: {rel_err_vit*100:.2f}%\\n\")"]}, {"cell_type": "markdown", "id": "1a2af260", "metadata": {}, "source": ["By comparing the relative errors in the training process, we found that the ViT wave function has a greater expressive power than the RBM wave function, the latter of which cannot even approach the ground state energy. The ViT model can more effectively capture long-range correlations and complex quantum states than the RBM model.\n", "\n", "Let's plot energy vs. iterations to visually demonstrate the training process:"]}, {"cell_type": "code", "execution_count": 13, "id": "926a6aa7", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import json\n", "import matplotlib.pyplot as plt\n", "\n", "# Read RBM log data\n", "with open(\"RBM_J1J2.log\", \"r\") as f:\n", "    data_RBM = json.load(f)\n", "iters_RBM = data_RBM[\"Energy\"][\"iters\"]\n", "# If energy is in complex format, take its real part\n", "energy_RBM = data_RBM[\"Energy\"][\"Mean\"]\n", "\n", "# Read ViT log data\n", "with open(\"ViT_J1J2.log\", \"r\") as f:\n", "    data_ViT = json.load(f)\n", "iters_ViT = data_ViT[\"Energy\"][\"iters\"]\n", "energy_ViT = data_ViT[\"Energy\"][\"Mean\"]\n", "\n", "if isinstance(energy_ViT, dict) and \"real\" in energy_ViT:\n", "    energy_ViT = energy_ViT[\"real\"]\n", "\n", "# Plot figure\n", "plt.figure(figsize=(8, 6))\n", "plt.plot(iters_RBM, energy_RBM, label=\"RBM\")\n", "plt.plot(iters_ViT, energy_ViT, label=\"ViT\")\n", "plt.xlabel(\"Iteration\")\n", "plt.ylabel(\"Energy\")\n", "plt.axhline(y=exact_energy_j1j2_vit, xmin=0,\n", "                xmax=iters_J<PERSON>row[-1], linewidth=2, color='k', label='Exact')\n", "plt.title(\"Energy vs Iterations\")\n", "plt.legend()\n", "plt.grid(True)\n", "plt.ylim(top=10, bottom=-25)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "4915b55e", "metadata": {}, "source": ["\n", "## Conclusion\n"]}, {"cell_type": "markdown", "id": "a1a18294", "metadata": {}, "source": ["\n", "In this project, we explored methods for solving quantum many-body problems using neural networks, particularly focusing on how to solve or bypass the sign problem in traditional quantum Monte Carlo methods. We implemented a range of strategies from simple variational Monte Carlo to complex Vision Transformer-based variational methods.\n", "\n", "Key findings:\n", "\n", "1. Traditional quantum Monte Carlo methods face severe challenges when dealing with systems that have sign problems.\n", "\n", "2. Variational Monte Carlo methods combined with parameterized trial wave functions can effectively bypass the sign problem.\n", "\n", "3. Neural Quantum States (NQS) provide a powerful method for representing wave functions of complex quantum systems, with RBM architectures performing well in one-dimensional and two-dimensional systems.\n", "\n", "\n", "Potential future directions include:\n", "\n", "1. Applying these methods to more complex systems, such as frustrated magnets, topological systems, or strongly correlated electron systems.\n", "\n", "2. Exploring more advanced neural network structures, such as deep neural networks, graph neural networks, etc.\n", "\n", "3. Researching how to more efficiently train neural network representations of large-scale quantum systems.\n", "\n", "4. Combining these methods with quantum computing algorithms to provide a foundation for future hybrid quantum-classical computing.\n", "\n", "In summary, neural network methods offer a promising new direction for research on quantum many-body problems. They can overcome some key limitations of traditional methods, particularly in handling systems with sign problems. With further development in computational power and machine learning techniques, these methods are expected to play an increasingly important role in future quantum physics research."]}], "metadata": {"kernelspec": {"display_name": "netket", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}