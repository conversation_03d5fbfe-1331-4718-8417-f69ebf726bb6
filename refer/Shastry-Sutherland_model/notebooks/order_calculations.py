import numpy as np
import os
import matplotlib.pyplot as plt

# 定义日志记录函数
def log_message(log_file, message):
    print(message) # 在脚本执行时直接打印
    if log_file:
        with open(log_file, 'a') as f:
            f.write(message + '\n')

def calculate_af_order_parameter(k_points_tuple, spin_sf, L, save_dir, log_file=None):
    """反铁磁序参量：直接取S(π,π)"""
    k_points_x, k_points_y = k_points_tuple
    pi_idx_x = np.argmin(np.abs(k_points_x - np.pi))
    pi_idx_y = np.argmin(np.abs(k_points_y - np.pi))
    return spin_sf[pi_idx_y, pi_idx_x].real  # 取实部

def calculate_dimer_order_parameter(k_points_tuple, dimer_sf, L, save_dir, log_file=None):
    """二聚体序参量：取S(π,0)和S(0,π)的平均"""
    k_points_x, k_points_y = k_points_tuple
    pi_idx_x = np.argmin(np.abs(k_points_x - np.pi))
    pi_idx_y = np.argmin(np.abs(k_points_y - np.pi))
    zero_idx_x = np.argmin(np.abs(k_points_x - 0.0))
    zero_idx_y = np.argmin(np.abs(k_points_y - 0.0))
    
    S_pi0 = dimer_sf[zero_idx_y, pi_idx_x].real
    S_0pi = dimer_sf[pi_idx_y, zero_idx_x].real
    return 0.5 * (S_pi0 + S_0pi)

def calculate_diag_dimer_order_parameter(k_points_tuple, diag_dimer_sf, L, save_dir, log_file=None):
    """对角二聚体序参量：取S(π,0)和S(0,π)的平均"""
    k_points_x, k_points_y = k_points_tuple
    pi_idx_x = np.argmin(np.abs(k_points_x - np.pi))
    pi_idx_y = np.argmin(np.abs(k_points_y - np.pi))
    zero_idx_x = np.argmin(np.abs(k_points_x - 0.0))
    zero_idx_y = np.argmin(np.abs(k_points_y - 0.0))
    
    S_pi0 = diag_dimer_sf[zero_idx_y, pi_idx_x].real
    S_0pi = diag_dimer_sf[pi_idx_y, zero_idx_x].real
    return 0.5 * (S_pi0 + S_0pi)

def calculate_plaquette_order_parameter(k_points_tuple, plaq_sf, L, save_dir, log_file=None):
    """简盘序参量：取S(π,0)和S(0,π)的平均"""
    k_points_x, k_points_y = k_points_tuple
    pi_idx_x = np.argmin(np.abs(k_points_x - np.pi))
    pi_idx_y = np.argmin(np.abs(k_points_y - np.pi))
    zero_idx_x = np.argmin(np.abs(k_points_x - 0.0))
    zero_idx_y = np.argmin(np.abs(k_points_y - 0.0))

    S_pi0 = plaq_sf[zero_idx_y, pi_idx_x].real
    S_0pi = plaq_sf[pi_idx_y, zero_idx_x].real
    return 0.5 * (S_pi0 + S_0pi)



def calculate_correlation_ratios(k_points_tuple, structure_factor, save_dir, type_name, log_file=None):
    """
    根据不同类型计算物理上有意义的相关比率：
    - spin: 检测(π,π)位置，计算Néel反铁磁序的相关比率，取一个相邻格点
    - dimer: 检测(0,π)和(π,0)位置，计算条纹型二聚体序的相关比率，每个位置取一个相邻格点
    - diag_dimer: 检测(0,π)和(π,0)位置，计算对角二聚体序的相关比率，每个位置取一个相邻格点，返回平均值
    - plaquette: 检测(0,π)和(π,0)位置，计算简盘序的相关比率

    计算公式：Ratio = 1 - S(相邻格点)/S(峰位置)

    参数:
    - k_points_tuple: 包含k_points_x和k_points_y的元组
    - structure_factor: 结构因子数据
    - save_dir: 保存目录
    - type_name: 结构因子类型名称 ('spin', 'dimer', 'diag_dimer', 'plaquette')
    - log_file: 日志文件
    """
    # 解包k点
    k_points_x, k_points_y = k_points_tuple

    log_message(log_file, "-"*80)
    log_message(log_file, f"计算{type_name}相关比率...")

    # 找到关键k点的索引
    pi_idx_x = np.argmin(np.abs(k_points_x - np.pi))
    pi_idx_y = np.argmin(np.abs(k_points_y - np.pi))
    zero_idx_x = np.argmin(np.abs(k_points_x - 0.0))
    zero_idx_y = np.argmin(np.abs(k_points_y - 0.0))
    
    # 获取实际的k点值
    k_pi_x = k_points_x[pi_idx_x]
    k_pi_y = k_points_y[pi_idx_y]
    k_zero_x = k_points_x[zero_idx_x]
    k_zero_y = k_points_y[zero_idx_y]

    if type_name.lower() == 'spin':
        # 对于自旋：检测(π,π)位置的Néel反铁磁序
        S_main = structure_factor[pi_idx_y, pi_idx_x].real
        main_pos = (k_pi_x, k_pi_y)
        
        # 按照用户要求：只取一个相邻格点，选择x方向的相邻点
        idx_x_adj = pi_idx_x + 1 if pi_idx_x < len(k_points_x) - 1 else pi_idx_x - 1
        S_adj = structure_factor[pi_idx_y, idx_x_adj].real
        
        # 获取相邻点的实际k坐标
        k_adj_x = k_points_x[idx_x_adj]
        
        # 计算ratio，不取绝对值
        ratio = 1.0 - S_adj / S_main if abs(S_main) > 1e-10 else 0.0
        
        log_message(log_file, f"主峰位置(π,π): ({main_pos[0]:.4f}, {main_pos[1]:.4f}), S = {S_main:.6f}")
        log_message(log_file, f"相邻点: ({k_adj_x:.4f}, {main_pos[1]:.4f}), S = {S_adj:.6f}")
        log_message(log_file, f"Neel关联比: {ratio:.6f}")
        
        return ratio, (main_pos, S_main, S_adj)
        
    elif type_name.lower() == 'dimer':
        # 对于二聚体：分别检测(0,π)和(π,0)位置
        S_0pi = structure_factor[pi_idx_y, zero_idx_x].real
        S_pi0 = structure_factor[zero_idx_y, pi_idx_x].real
        
        # 按照用户要求：只取一个相邻格点
        # 对于(0,π)位置，取y方向的相邻点
        idx_y_adj_0pi = pi_idx_y + 1 if pi_idx_y < len(k_points_y) - 1 else pi_idx_y - 1
        S_adj_0pi = structure_factor[idx_y_adj_0pi, zero_idx_x].real
        
        # 对于(π,0)位置，取x方向的相邻点
        idx_x_adj_pi0 = pi_idx_x + 1 if pi_idx_x < len(k_points_x) - 1 else pi_idx_x - 1
        S_adj_pi0 = structure_factor[zero_idx_y, idx_x_adj_pi0].real
        
        # 获取相邻点的实际k坐标
        k_y_adj_0pi = k_points_y[idx_y_adj_0pi]
        k_x_adj_pi0 = k_points_x[idx_x_adj_pi0]
        
        # 分别计算两个位置的ratio，不取绝对值
        ratio_0pi = 1.0 - S_adj_0pi / S_0pi if abs(S_0pi) > 1e-10 else 0.0
        ratio_pi0 = 1.0 - S_adj_pi0 / S_pi0 if abs(S_pi0) > 1e-10 else 0.0
        
        # 详细的日志记录
        log_message(log_file, f"主峰位置(0,π): ({k_zero_x:.4f}, {k_pi_y:.4f}), S = {S_0pi:.6f}")
        log_message(log_file, f"  相邻点: ({k_zero_x:.4f}, {k_y_adj_0pi:.4f}), S = {S_adj_0pi:.6f}")
        log_message(log_file, f"  关联比(0,π): {ratio_0pi:.6f}")
        
        log_message(log_file, f"主峰位置(π,0): ({k_pi_x:.4f}, {k_zero_y:.4f}), S = {S_pi0:.6f}")
        log_message(log_file, f"  相邻点: ({k_x_adj_pi0:.4f}, {k_zero_y:.4f}), S = {S_adj_pi0:.6f}")
        log_message(log_file, f"  关联比(π,0): {ratio_pi0:.6f}")
        
        # 返回详细信息，包括两个不同位置的ratio
        return {
            'ratio_0pi': ratio_0pi,
            'ratio_pi0': ratio_pi0,
            'S_0pi': S_0pi,
            'S_pi0': S_pi0,
            'S_adj_0pi': S_adj_0pi,
            'S_adj_pi0': S_adj_pi0
        }
        
    elif type_name.lower() == 'diag_dimer':
        # 对于对角二聚体：按照用户要求检测(0,π)和(π,0)位置
        S_0pi = structure_factor[pi_idx_y, zero_idx_x].real
        S_pi0 = structure_factor[zero_idx_y, pi_idx_x].real
        
        # 按照用户要求：只取一个相邻格点
        # 对于(0,π)位置，取y方向的相邻点
        idx_y_adj_0pi = pi_idx_y + 1 if pi_idx_y < len(k_points_y) - 1 else pi_idx_y - 1
        S_adj_0pi = structure_factor[idx_y_adj_0pi, zero_idx_x].real
        
        # 对于(π,0)位置，取x方向的相邻点
        idx_x_adj_pi0 = pi_idx_x + 1 if pi_idx_x < len(k_points_x) - 1 else pi_idx_x - 1
        S_adj_pi0 = structure_factor[zero_idx_y, idx_x_adj_pi0].real
        
        # 获取相邻点的实际k坐标
        k_y_adj_0pi = k_points_y[idx_y_adj_0pi]
        k_x_adj_pi0 = k_points_x[idx_x_adj_pi0]
        
        # 分别计算两个位置的ratio，不取绝对值
        ratio_0pi = 1.0 - S_adj_0pi / S_0pi if abs(S_0pi) > 1e-10 else 0.0
        ratio_pi0 = 1.0 - S_adj_pi0 / S_pi0 if abs(S_pi0) > 1e-10 else 0.0
        
        # 计算平均ratio
        ratio_avg = 0.5 * (ratio_0pi + ratio_pi0)
        
        # 详细的日志记录
        log_message(log_file, f"对角二聚体主峰位置(0,π): ({k_zero_x:.4f}, {k_pi_y:.4f}), S = {S_0pi:.6f}")
        log_message(log_file, f"  相邻点: ({k_zero_x:.4f}, {k_y_adj_0pi:.4f}), S = {S_adj_0pi:.6f}")
        log_message(log_file, f"  关联比(0,π): {ratio_0pi:.6f}")
        
        log_message(log_file, f"对角二聚体主峰位置(π,0): ({k_pi_x:.4f}, {k_zero_y:.4f}), S = {S_pi0:.6f}")
        log_message(log_file, f"  相邻点: ({k_x_adj_pi0:.4f}, {k_zero_y:.4f}), S = {S_adj_pi0:.6f}")
        log_message(log_file, f"  关联比(π,0): {ratio_pi0:.6f}")
        
        log_message(log_file, f"对角二聚体平均关联比: {ratio_avg:.6f}")
        
        # 返回详细信息
        return {
            'ratio_0pi': ratio_0pi,
            'ratio_pi0': ratio_pi0,
            'ratio_avg': ratio_avg,  # 添加平均值
            'S_0pi': S_0pi,
            'S_pi0': S_pi0,
            'S_adj_0pi': S_adj_0pi,
            'S_adj_pi0': S_adj_pi0
        }
        
    elif type_name.lower() == 'plaquette':
        # 对于简盘：检测(0,π)和(π,0)位置
        S_0pi = structure_factor[pi_idx_y, zero_idx_x].real
        S_pi0 = structure_factor[zero_idx_y, pi_idx_x].real
        S_main = S_0pi + S_pi0
        
        # 计算相邻点
        idx_x_plus = zero_idx_x + 1 if zero_idx_x < len(k_points_x) - 1 else zero_idx_x - 1
        idx_y_plus = pi_idx_y + 1 if pi_idx_y < len(k_points_y) - 1 else pi_idx_y - 1
        
        S_adj_0pi = structure_factor[idx_y_plus, zero_idx_x].real
        S_adj_pi0 = structure_factor[zero_idx_y, idx_x_plus].real
        S_adj = S_adj_0pi + S_adj_pi0
        
        main_pos = f"(0,π)+(π,0)"
        
        log_message(log_file, f"S(0,π) = {S_0pi:.6f}, S(π,0) = {S_pi0:.6f}")
        log_message(log_file, f"主峰位置{main_pos}: S_total = {S_main:.6f}")
        log_message(log_file, f"相邻点总和: S_adj = {S_adj:.6f}")
        
    else:
        # 默认行为：使用全局最大值
        log_message(log_file, f"警告: 未知的类型 '{type_name}'，使用默认的全局最大值方法")
        max_idx = np.unravel_index(np.argmax(structure_factor.real), structure_factor.shape)
        S_main = structure_factor[max_idx].real
        
        # 简单的相邻点计算
        max_idx_y, max_idx_x = max_idx
        idx_x_plus = max_idx_x + 1 if max_idx_x < len(k_points_x) - 1 else max_idx_x - 1
        idx_y_plus = max_idx_y + 1 if max_idx_y < len(k_points_y) - 1 else max_idx_y - 1
        
        S_adj_x = structure_factor[max_idx_y, idx_x_plus].real
        S_adj_y = structure_factor[idx_y_plus, max_idx_x].real
        S_adj = 0.5 * (S_adj_x + S_adj_y)
        
        main_pos = (k_points_x[max_idx_x], k_points_y[max_idx_y])

    # 计算相关比率，避免除零
    if abs(S_main) > 1e-10:
        ratio = 1.0 - S_adj / S_main
    else:
        ratio = 0.0
        log_message(log_file, f"警告: 主峰值太小 (S_main = {S_main:.6f})，设置比率为0")

    log_message(log_file, f"{type_name.capitalize()} 相关比率: {ratio:.4f}")

    return ratio, (main_pos, S_main, S_adj)

# 绘图函数
def plot_structure_factor_with_max(k_points_tuple, structure_factor, L, J2, J1, type_name, save_dir=None):
    """绘制结构因子并标记最大值位置"""
    k_points_x, k_points_y = k_points_tuple
    
    # 创建网格
    kx, ky = np.meshgrid(k_points_x, k_points_y)
    
    # 找到最大值位置
    max_idx = np.unravel_index(np.argmax(structure_factor), structure_factor.shape)
    k_max_x = k_points_x[max_idx[1]]
    k_max_y = k_points_y[max_idx[0]]
    S_max = structure_factor[max_idx]
    
    # 绘制
    plt.figure(figsize=(8, 6))
    cp = plt.contourf(kx, ky, structure_factor, 50, cmap='viridis')
    plt.colorbar(cp, label=f'{type_name} Structure Factor')
    plt.xlabel('$k_x$')
    plt.ylabel('$k_y$')
    plt.title(f'{type_name} Structure Factor (L={L}, J2={J2:.2f}, J1={J1:.2f})')
    
    # 标记最大值
    plt.plot(k_max_x, k_max_y, 'r*', markersize=10, label=f'Max: S({k_max_x:.2f}, {k_max_y:.2f}) = {S_max:.4f}')
    
    # 标记 (π,π) 点 - 对于Néel反铁磁顺序很重要
    pi_idx_x = np.argmin(np.abs(k_points_x - np.pi))
    pi_idx_y = np.argmin(np.abs(k_points_y - np.pi))
    k_pi_x = k_points_x[pi_idx_x]
    k_pi_y = k_points_y[pi_idx_y]
    S_pi_pi = structure_factor[pi_idx_y, pi_idx_x]
    plt.plot(k_pi_x, k_pi_y, 'go', markersize=8, label=f'S(π,π) = {S_pi_pi:.4f}')
    
    plt.legend()
    plt.grid(alpha=0.3)
    
    if save_dir:
        plt.savefig(os.path.join(save_dir, f"{type_name.lower()}_sf_L{L}_J2{J2:.2f}_J1{J1:.2f}.png"), dpi=300, bbox_inches='tight')
    
    plt.show()
    
def plot_order_parameters_vs_J1(J1_values, order_values, system_size, J2, type_name):
    """绘制序参量随J1变化的曲线"""
    plt.figure(figsize=(8, 6))
    plt.plot(J1_values, order_values, 'o-', markersize=8, linewidth=2)
    
    plt.xlabel('$J_1$')
    plt.ylabel(f'{type_name} Order Parameter')
    plt.title(f'{type_name} Order vs $J_1$ (L={system_size}, $J_2$={J2:.2f})')
    
    plt.grid(alpha=0.3)
    plt.show()
    
def plot_correlation_ratios_vs_J1(J1_values, ratio_values, system_size, J2, type_name):
    """绘制相关比率随J1变化的曲线"""
    plt.figure(figsize=(8, 6))
    plt.plot(J1_values, ratio_values, 'o-', markersize=8, linewidth=2)
    
    plt.xlabel('$J_1$')
    plt.ylabel(f'{type_name} Correlation Ratio')
    plt.title(f'{type_name} Correlation Ratio vs $J_1$ (L={system_size}, $J_2$={J2:.2f})')
    
    plt.grid(alpha=0.3)
    plt.show()
    
def plot_all_order_parameters(J1_values, data_dict, system_size, J2):
    """在一张图上绘制所有序参量随J1变化的曲线"""
    plt.figure(figsize=(10, 7))
    
    if 'af_order' in data_dict:
        af_data = data_dict['af_order']
        plt.plot(J1_values, af_data, 'o-', label='AF Order', linewidth=2)
    
    if 'dimer_order' in data_dict:
        dimer_data = data_dict['dimer_order']
        plt.plot(J1_values, dimer_data, 's-', label='Dimer Order', linewidth=2)
    
    if 'plaq_order' in data_dict:
        plaq_data = data_dict['plaq_order']
        plt.plot(J1_values, plaq_data, '^-', label='Plaquette Order', linewidth=2)
    
    plt.xlabel('$J_1/J_2$', fontsize=14)
    plt.ylabel('Order Parameter', fontsize=14)
    plt.title(f'Order Parameters vs $J_1/J_2$ (L={system_size}, $J_2$={J2:.2f})', fontsize=16)
    
    plt.grid(alpha=0.3)
    plt.legend(fontsize=12)
    plt.tight_layout()
    plt.show()
    
def plot_all_correlation_ratios(J1_values, data_dict, system_size, J2):
    """在一张图上绘制所有相关比率随J1变化的曲线"""
    plt.figure(figsize=(10, 7))
    
    if 'neel_ratio' in data_dict:
        neel_data = data_dict['neel_ratio']
        plt.plot(J1_values, neel_data, 'o-', label='Neel Ratio', linewidth=2)
    
    if 'dimer_ratio' in data_dict:
        dimer_data = data_dict['dimer_ratio']
        plt.plot(J1_values, dimer_data, 's-', label='Dimer Ratio', linewidth=2)
    
    if 'plaq_ratio' in data_dict:
        plaq_data = data_dict['plaq_ratio']
        plt.plot(J1_values, plaq_data, '^-', label='Plaquette Ratio', linewidth=2)
    
    plt.xlabel('$J_1/J_2$', fontsize=14)
    plt.ylabel('Correlation Ratio', fontsize=14)
    plt.title(f'Correlation Ratios vs $J_1/J_2$ (L={system_size}, $J_2$={J2:.2f})', fontsize=16)
    
    plt.grid(alpha=0.3)
    plt.legend(fontsize=12)
    plt.tight_layout()
    plt.show() 