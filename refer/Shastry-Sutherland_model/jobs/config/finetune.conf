# ==================== 链式微调任务配置文件 ====================
# 此文件包含链式微调任务的所有参数配置

# ==================== 系统参数配置 ====================
# 晶格尺寸
L_VALUES="5 6"

# J2耦合强度
J2_VALUES="1.00"

# ==================== 链式微调起始和边界参数 ====================
FINETUNE_START_J1="0.80"
FINETUNE_J1_LEFT_BOUND="0.76"
FINETUNE_J1_RIGHT_BOUND="0.84"
FINETUNE_J1_STEP="0.01"

# ==================== 微调超参数 ====================
# 通常比训练用更少的样本和周期
FINETUNE_LEARNING_RATE=0.015
FINETUNE_N_SAMPLES=4096
FINETUNE_CHUNK_SIZE=2048
FINETUNE_N_CYCLES=3
FINETUNE_INITIAL_PERIOD=150
FINETUNE_PERIOD_MULT=2.0
FINETUNE_MAX_TEMPERATURE=1.0
FINETUNE_MIN_TEMPERATURE=0.0

# ==================== 模型参数 ====================
FINETUNE_NUM_FEATURES=4
FINETUNE_NUM_LAYERS=4
FINETUNE_DIAG_SHIFT=0.20
FINETUNE_GRAD_CLIP=1.0

# ==================== Checkpoint配置 ====================
FINETUNE_ENABLE_CHECKPOINT=true
FINETUNE_CHECKPOINT_INTERVAL=105
FINETUNE_KEEP_CHECKPOINT_HISTORY=true

