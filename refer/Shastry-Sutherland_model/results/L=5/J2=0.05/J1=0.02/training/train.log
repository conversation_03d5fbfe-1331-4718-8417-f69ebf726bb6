[2025-09-10 07:04:53] ✓ 从checkpoint恢复: results/L=5/J2=0.05/J1=0.03/training/checkpoints/final_GCNN.pkl
[2025-09-10 07:04:53]   - 迭代次数: final
[2025-09-10 07:04:53]   - 能量: -79.724329+0.001132j ± 0.104764
[2025-09-10 07:04:53]   - 时间戳: 2025-09-10T07:03:43.482607+08:00
[2025-09-10 07:05:23] ✓ 变分状态参数已从checkpoint恢复
[2025-09-10 07:05:23] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-10 07:05:23] ==================================================
[2025-09-10 07:05:23] GCNN for Shastry-Sutherland Model
[2025-09-10 07:05:23] ==================================================
[2025-09-10 07:05:23] System parameters:
[2025-09-10 07:05:23]   - System size: L=5, N=100
[2025-09-10 07:05:23]   - System parameters: J1=0.02, J2=0.05, Q=0.95
[2025-09-10 07:05:23] --------------------------------------------------
[2025-09-10 07:05:23] Model parameters:
[2025-09-10 07:05:23]   - Number of layers = 4
[2025-09-10 07:05:23]   - Number of features = 4
[2025-09-10 07:05:23]   - Total parameters = 19628
[2025-09-10 07:05:23] --------------------------------------------------
[2025-09-10 07:05:23] Training parameters:
[2025-09-10 07:05:23]   - Learning rate: 0.015
[2025-09-10 07:05:23]   - Total iterations: 1050
[2025-09-10 07:05:23]   - Annealing cycles: 3
[2025-09-10 07:05:23]   - Initial period: 150
[2025-09-10 07:05:23]   - Period multiplier: 2.0
[2025-09-10 07:05:23]   - Temperature range: 0.0-1.0
[2025-09-10 07:05:23]   - Samples: 4096
[2025-09-10 07:05:23]   - Discarded samples: 0
[2025-09-10 07:05:23]   - Chunk size: 2048
[2025-09-10 07:05:23]   - Diagonal shift: 0.2
[2025-09-10 07:05:23]   - Gradient clipping: 1.0
[2025-09-10 07:05:23]   - Checkpoint enabled: interval=105
[2025-09-10 07:05:23]   - Checkpoint directory: results/L=5/J2=0.05/J1=0.02/training/checkpoints
[2025-09-10 07:05:23] --------------------------------------------------
[2025-09-10 07:05:23] Device status:
[2025-09-10 07:05:23]   - Devices model: NVIDIA H200 NVL
[2025-09-10 07:05:23]   - Number of devices: 1
[2025-09-10 07:05:23]   - Sharding: True
[2025-09-10 07:05:23] ============================================================
[2025-09-10 07:07:49] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -79.355735-0.009910j
[2025-09-10 07:09:24] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -79.351252+0.003690j
[2025-09-10 07:09:59] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -79.342049-0.007500j
[2025-09-10 07:10:33] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -79.307519-0.005431j
[2025-09-10 07:11:07] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -79.373017-0.009355j
[2025-09-10 07:11:42] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -79.345841-0.003056j
[2025-09-10 07:12:16] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -79.204854-0.007662j
[2025-09-10 07:12:51] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -79.368842-0.005080j
[2025-09-10 07:13:25] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -79.303037-0.004199j
[2025-09-10 07:14:00] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -79.411131-0.004427j
[2025-09-10 07:14:34] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -79.254199-0.001664j
[2025-09-10 07:15:08] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -79.432563-0.001655j
[2025-09-10 07:15:43] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -79.591554-0.000315j
[2025-09-10 07:16:18] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -79.582753-0.001422j
[2025-09-10 07:16:52] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -79.554829-0.001910j
[2025-09-10 07:17:27] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -79.556347-0.000999j
[2025-09-10 07:18:01] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -79.367816-0.008421j
[2025-09-10 07:18:36] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -79.383883-0.004730j
[2025-09-10 07:19:10] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -79.287590+0.000736j
[2025-09-10 07:19:44] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -79.394312-0.010571j
[2025-09-10 07:20:19] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -79.342050-0.002942j
[2025-09-10 07:20:53] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -79.459267+0.001356j
[2025-09-10 07:21:28] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -79.416161-0.001561j
[2025-09-10 07:22:02] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -79.469146-0.005942j
[2025-09-10 07:22:37] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -79.392499-0.001248j
[2025-09-10 07:23:11] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -79.349149-0.000248j
[2025-09-10 07:23:46] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -79.472656-0.002403j
[2025-09-10 07:24:20] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -79.414504-0.008217j
[2025-09-10 07:24:55] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -79.271759-0.005084j
[2025-09-10 07:25:29] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -79.287972+0.000632j
[2025-09-10 07:26:04] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -79.274783+0.003277j
[2025-09-10 07:26:38] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -79.341571+0.001145j
[2025-09-10 07:27:13] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -79.207929-0.000666j
[2025-09-10 07:27:47] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -79.212141-0.008211j
[2025-09-10 07:28:22] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -79.282018-0.002978j
[2025-09-10 07:28:57] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -79.202200-0.000629j
[2025-09-10 07:29:32] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -79.286078+0.004383j
[2025-09-10 07:30:06] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -79.158497-0.007615j
[2025-09-10 07:30:41] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -79.192819-0.001457j
[2025-09-10 07:31:16] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -79.266424+0.002574j
[2025-09-10 07:31:51] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -79.250036-0.000370j
[2025-09-10 07:32:25] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -79.322769+0.001469j
[2025-09-10 07:33:00] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -79.457097+0.001816j
[2025-09-10 07:33:34] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -79.377018-0.002017j
[2025-09-10 07:34:09] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -79.385429-0.005771j
[2025-09-10 07:34:43] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -79.283040+0.001845j
[2025-09-10 07:35:18] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -79.328851-0.003189j
[2025-09-10 07:35:52] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -79.462267-0.000322j
[2025-09-10 07:36:27] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -79.371550+0.000848j
[2025-09-10 07:37:01] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -79.174660+0.005957j
[2025-09-10 07:37:36] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -79.258533+0.000393j
[2025-09-10 07:38:10] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -79.354759+0.000801j
[2025-09-10 07:38:45] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -79.331428-0.000707j
[2025-09-10 07:39:19] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -79.240461-0.000013j
[2025-09-10 07:39:54] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -79.336873+0.006182j
[2025-09-10 07:40:28] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -79.281170+0.000149j
[2025-09-10 07:41:03] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -79.494849-0.000546j
[2025-09-10 07:41:37] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -79.314464-0.003516j
[2025-09-10 07:42:12] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -79.418688+0.000409j
[2025-09-10 07:42:46] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -79.294537-0.000159j
[2025-09-10 07:43:21] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -79.310452+0.001235j
[2025-09-10 07:43:55] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -79.420942+0.003737j
[2025-09-10 07:44:30] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -79.412802+0.003120j
[2025-09-10 07:45:04] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -79.463098+0.009351j
[2025-09-10 07:45:39] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -79.625710+0.002407j
[2025-09-10 07:46:13] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -79.422012-0.002830j
[2025-09-10 07:46:48] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -79.412494-0.003710j
[2025-09-10 07:47:23] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -79.492771+0.001818j
[2025-09-10 07:47:57] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -79.311519+0.004769j
[2025-09-10 07:48:32] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -79.475958+0.001831j
[2025-09-10 07:49:07] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -79.324673+0.005310j
[2025-09-10 07:49:42] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -79.237916-0.003194j
[2025-09-10 07:50:16] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -79.190550-0.002426j
[2025-09-10 07:50:51] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -79.165825-0.000495j
[2025-09-10 07:51:26] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -79.083490-0.001101j
[2025-09-10 07:52:01] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -79.237464+0.003037j
[2025-09-10 07:52:36] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -79.390879-0.001770j
[2025-09-10 07:53:10] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -79.272066+0.003099j
[2025-09-10 07:53:45] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -79.329826+0.002474j
[2025-09-10 07:54:20] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -79.396942+0.000310j
[2025-09-10 07:54:55] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -79.530621-0.000487j
[2025-09-10 07:55:30] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -79.309932+0.008181j
[2025-09-10 07:56:04] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -79.229740+0.000767j
[2025-09-10 07:56:39] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -79.277612-0.005523j
[2025-09-10 07:57:14] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -79.208922-0.003189j
[2025-09-10 07:57:49] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -79.157368-0.005422j
[2025-09-10 07:58:23] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -79.260651+0.002715j
[2025-09-10 07:58:58] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -79.335310-0.002121j
[2025-09-10 07:59:33] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -79.307757-0.001423j
[2025-09-10 08:00:08] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -79.163319-0.003420j
[2025-09-10 08:00:42] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -79.251910+0.000769j
[2025-09-10 08:01:17] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -79.139365+0.001023j
[2025-09-10 08:01:51] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -79.129201+0.004699j
[2025-09-10 08:02:26] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -79.048710+0.002435j
[2025-09-10 08:03:01] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -79.183590+0.000858j
[2025-09-10 08:03:35] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -79.183223+0.001456j
[2025-09-10 08:04:10] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -79.207925+0.004513j
[2025-09-10 08:04:45] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -79.131071+0.000600j
[2025-09-10 08:05:20] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -79.209397-0.001889j
[2025-09-10 08:05:54] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -79.340412+0.003347j
[2025-09-10 08:06:29] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -79.369243+0.000481j
[2025-09-10 08:07:04] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -79.348797+0.000169j
[2025-09-10 08:07:38] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -79.273985+0.000077j
[2025-09-10 08:08:13] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -79.295495+0.001012j
[2025-09-10 08:08:48] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -79.228917-0.001540j
[2025-09-10 08:08:48] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-10 08:09:23] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -79.166258-0.001034j
[2025-09-10 08:09:57] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -79.272624+0.003777j
[2025-09-10 08:10:32] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -79.233969+0.000031j
[2025-09-10 08:11:07] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -79.406323+0.007760j
[2025-09-10 08:11:41] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -79.265007-0.002324j
[2025-09-10 08:12:16] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -79.200090+0.002194j
[2025-09-10 08:12:51] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -79.324783-0.002336j
[2025-09-10 08:13:26] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -79.378544-0.001996j
[2025-09-10 08:14:00] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -79.465721+0.000307j
[2025-09-10 08:14:35] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -79.368642+0.001476j
[2025-09-10 08:15:10] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -79.317019+0.001866j
[2025-09-10 08:15:45] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -79.444167+0.003707j
[2025-09-10 08:16:20] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -79.468743+0.003309j
[2025-09-10 08:16:55] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -79.481002+0.000103j
[2025-09-10 08:17:29] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -79.396105-0.002093j
[2025-09-10 08:18:04] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -79.310353+0.001096j
[2025-09-10 08:18:39] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -79.326022+0.002452j
[2025-09-10 08:19:14] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -79.495156-0.001316j
[2025-09-10 08:19:48] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -79.423210+0.014424j
[2025-09-10 08:20:23] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -79.406865+0.001913j
[2025-09-10 08:20:58] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -79.434086+0.002662j
[2025-09-10 08:21:33] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -79.310869+0.000159j
[2025-09-10 08:22:07] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -79.541184+0.001861j
[2025-09-10 08:22:42] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -79.317357+0.001311j
[2025-09-10 08:23:17] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -79.311759-0.002152j
[2025-09-10 08:23:52] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -79.193845-0.001960j
[2025-09-10 08:24:26] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -79.289494+0.001018j
[2025-09-10 08:25:01] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -79.368141-0.000003j
[2025-09-10 08:25:36] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -79.435291+0.001195j
[2025-09-10 08:26:11] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -79.262294+0.001402j
[2025-09-10 08:26:45] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -79.334572-0.000421j
[2025-09-10 08:27:20] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -79.409427-0.002327j
[2025-09-10 08:27:55] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -79.411625-0.005940j
[2025-09-10 08:28:30] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -79.422312+0.001074j
[2025-09-10 08:29:04] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -79.466431-0.000291j
[2025-09-10 08:29:39] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -79.531686-0.001056j
[2025-09-10 08:30:14] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -79.535030-0.007575j
[2025-09-10 08:30:48] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -79.530325-0.002009j
[2025-09-10 08:31:23] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -79.584386-0.000450j
[2025-09-10 08:31:58] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -79.548217-0.000481j
[2025-09-10 08:32:33] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -79.410180-0.003375j
[2025-09-10 08:33:08] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -79.279659-0.002371j
[2025-09-10 08:33:42] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -79.320525-0.005198j
[2025-09-10 08:34:17] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -79.223147-0.000079j
[2025-09-10 08:34:52] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -79.217482+0.011469j
[2025-09-10 08:34:52] RESTART #1 | Period: 300
[2025-09-10 08:35:27] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -79.207209-0.005307j
[2025-09-10 08:36:01] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -79.128518+0.002446j
[2025-09-10 08:36:36] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -79.226567-0.001142j
[2025-09-10 08:37:11] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -79.167239+0.003603j
[2025-09-10 08:37:45] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -79.160383-0.000830j
[2025-09-10 08:38:20] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -79.196384+0.004055j
[2025-09-10 08:38:54] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -79.329115-0.004020j
[2025-09-10 08:39:29] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -79.225667-0.001265j
[2025-09-10 08:40:03] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -79.280213-0.001387j
[2025-09-10 08:40:38] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -79.378168-0.002115j
[2025-09-10 08:41:12] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -79.395794-0.002444j
[2025-09-10 08:41:46] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -79.500643+0.001977j
[2025-09-10 08:42:21] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -79.337649-0.007008j
[2025-09-10 08:42:55] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -79.215956-0.001575j
[2025-09-10 08:43:30] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -79.237326-0.006682j
[2025-09-10 08:44:04] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -79.291894+0.001435j
[2025-09-10 08:44:39] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -79.308177+0.002959j
[2025-09-10 08:45:13] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -79.331383-0.007494j
[2025-09-10 08:45:47] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -79.316070+0.000018j
[2025-09-10 08:46:22] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -79.326650+0.003762j
[2025-09-10 08:46:56] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -79.243776-0.000884j
[2025-09-10 08:47:31] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -79.174339+0.002035j
[2025-09-10 08:48:05] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -79.184981+0.002481j
[2025-09-10 08:48:40] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -79.027986-0.002756j
[2025-09-10 08:49:14] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -79.189833+0.003079j
[2025-09-10 08:49:49] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -79.388919-0.003242j
[2025-09-10 08:50:23] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -79.315994+0.010966j
[2025-09-10 08:50:58] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -79.294388+0.000158j
[2025-09-10 08:51:33] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -79.356908+0.007318j
[2025-09-10 08:52:07] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -79.295785-0.000088j
[2025-09-10 08:52:42] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -79.333032+0.003290j
[2025-09-10 08:53:16] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -79.409503+0.007312j
[2025-09-10 08:53:51] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -79.287537+0.001167j
[2025-09-10 08:54:25] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -79.398070+0.001534j
[2025-09-10 08:55:00] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -79.327884-0.004201j
[2025-09-10 08:55:34] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -79.260170+0.001585j
[2025-09-10 08:56:09] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -79.286994+0.005170j
[2025-09-10 08:56:43] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -79.198093+0.002276j
[2025-09-10 08:57:18] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -79.162022-0.002505j
[2025-09-10 08:57:52] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -79.186345+0.002837j
[2025-09-10 08:58:27] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -79.206038+0.000051j
[2025-09-10 08:59:01] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -79.133964+0.006637j
[2025-09-10 08:59:36] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -79.162181+0.001161j
[2025-09-10 09:00:10] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -79.186506-0.006026j
[2025-09-10 09:00:45] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -79.102696+0.004854j
[2025-09-10 09:01:19] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -78.974035+0.003744j
[2025-09-10 09:01:54] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -79.010299+0.009612j
[2025-09-10 09:02:29] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -78.866477+0.004433j
[2025-09-10 09:03:03] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -79.121362+0.000693j
[2025-09-10 09:03:37] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -79.119799+0.001356j
[2025-09-10 09:04:12] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -79.163627+0.002087j
[2025-09-10 09:04:47] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -79.113760+0.006148j
[2025-09-10 09:05:22] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -79.135404+0.002940j
[2025-09-10 09:05:56] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -79.080158-0.000305j
[2025-09-10 09:06:31] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -79.123377+0.005665j
[2025-09-10 09:07:06] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -79.268035-0.002850j
[2025-09-10 09:07:41] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -79.393422+0.000798j
[2025-09-10 09:08:15] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -79.436261-0.001439j
[2025-09-10 09:08:50] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -79.426847-0.001807j
[2025-09-10 09:09:25] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -79.368528-0.001098j
[2025-09-10 09:09:25] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-10 09:10:00] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -79.347477+0.001144j
[2025-09-10 09:10:34] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -79.468265+0.002257j
[2025-09-10 09:11:09] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -79.382188+0.001979j
[2025-09-10 09:11:44] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -79.283753+0.000844j
[2025-09-10 09:12:19] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -79.316577+0.003375j
[2025-09-10 09:12:53] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -79.359348+0.002047j
[2025-09-10 09:13:28] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -79.280442+0.000088j
[2025-09-10 09:14:03] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -79.321423+0.004257j
[2025-09-10 09:14:38] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -79.430960+0.000247j
[2025-09-10 09:15:12] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -79.423928-0.002961j
[2025-09-10 09:15:47] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -79.269677+0.003015j
[2025-09-10 09:16:22] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -79.368528-0.007518j
[2025-09-10 09:16:57] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -79.191791+0.006905j
[2025-09-10 09:17:31] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -79.168544-0.001110j
[2025-09-10 09:18:06] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -79.240062+0.005708j
[2025-09-10 09:18:41] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -79.191328+0.000709j
[2025-09-10 09:19:16] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -79.259643-0.000804j
[2025-09-10 09:19:50] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -79.374907-0.000764j
[2025-09-10 09:20:25] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -79.348148-0.001050j
[2025-09-10 09:20:59] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -79.315246-0.000534j
[2025-09-10 09:21:34] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -79.378303-0.001002j
[2025-09-10 09:22:09] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -79.327793+0.004098j
[2025-09-10 09:22:44] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -79.469134+0.004970j
[2025-09-10 09:23:19] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -79.504340-0.000605j
[2025-09-10 09:23:54] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -79.309276+0.003432j
[2025-09-10 09:24:28] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -79.366797+0.003736j
[2025-09-10 09:25:03] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -79.481564+0.001029j
[2025-09-10 09:25:38] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -79.361413-0.001442j
[2025-09-10 09:26:13] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -79.358581+0.000411j
[2025-09-10 09:26:47] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -79.362151+0.001168j
[2025-09-10 09:27:22] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -79.389759-0.001743j
[2025-09-10 09:27:57] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -79.360569-0.001980j
[2025-09-10 09:28:31] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -79.222375+0.003239j
[2025-09-10 09:29:06] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -79.302082-0.003849j
[2025-09-10 09:29:41] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -79.334979+0.000946j
[2025-09-10 09:30:16] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -79.286538+0.000067j
[2025-09-10 09:30:50] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -79.294868+0.003192j
[2025-09-10 09:31:25] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -79.347056-0.001657j
[2025-09-10 09:32:00] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -79.345986-0.005269j
[2025-09-10 09:32:35] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -79.302377+0.001288j
[2025-09-10 09:33:09] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -79.331922-0.004013j
[2025-09-10 09:33:44] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -79.365193-0.003076j
[2025-09-10 09:34:18] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -79.332653-0.001232j
[2025-09-10 09:34:53] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -79.412857+0.000550j
[2025-09-10 09:35:27] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -79.262711-0.004435j
[2025-09-10 09:36:02] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -79.262388+0.000321j
[2025-09-10 09:36:36] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -79.249856+0.004455j
[2025-09-10 09:37:11] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -79.182500+0.010441j
[2025-09-10 09:37:45] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -79.284311+0.001977j
[2025-09-10 09:38:20] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -79.229624-0.005961j
[2025-09-10 09:38:54] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -79.126350+0.007503j
[2025-09-10 09:39:29] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -79.131040+0.000575j
[2025-09-10 09:40:03] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -79.185413+0.002125j
[2025-09-10 09:40:38] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -79.138037-0.003051j
[2025-09-10 09:41:12] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -79.212868-0.007261j
[2025-09-10 09:41:47] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -79.152276-0.002583j
[2025-09-10 09:42:21] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -79.030362+0.000977j
[2025-09-10 09:42:56] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -79.064021-0.000644j
[2025-09-10 09:43:30] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -79.217485+0.000132j
[2025-09-10 09:44:05] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -79.201976+0.002855j
[2025-09-10 09:44:39] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -79.233077-0.003012j
[2025-09-10 09:45:14] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -79.357673-0.003176j
[2025-09-10 09:45:48] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -79.353173+0.002522j
[2025-09-10 09:46:23] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -79.346549-0.001904j
[2025-09-10 09:46:58] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -79.390749+0.001158j
[2025-09-10 09:47:32] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -79.263025+0.001545j
[2025-09-10 09:48:07] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -79.388299-0.006381j
[2025-09-10 09:48:42] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -79.288035+0.006009j
[2025-09-10 09:49:17] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -79.394738+0.006150j
[2025-09-10 09:49:51] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -79.258210+0.001712j
[2025-09-10 09:50:26] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -79.350670-0.000286j
[2025-09-10 09:51:01] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -79.361577+0.000541j
[2025-09-10 09:51:36] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -79.443871+0.000574j
[2025-09-10 09:52:11] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -79.437115+0.003552j
[2025-09-10 09:52:45] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -79.416556+0.000299j
[2025-09-10 09:53:20] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -79.369232-0.005121j
[2025-09-10 09:53:55] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -79.293853-0.000382j
[2025-09-10 09:54:30] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -79.343535+0.001818j
[2025-09-10 09:55:05] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -79.294540+0.001781j
[2025-09-10 09:55:39] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -79.162482+0.001331j
[2025-09-10 09:56:14] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -79.344942+0.000181j
[2025-09-10 09:56:49] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -79.198945-0.005720j
[2025-09-10 09:57:24] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -79.289389+0.001267j
[2025-09-10 09:57:58] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -79.287600+0.002574j
[2025-09-10 09:58:33] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -79.320383-0.001028j
[2025-09-10 09:59:08] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -79.149218-0.000154j
[2025-09-10 09:59:42] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -79.127981+0.004090j
[2025-09-10 10:00:16] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -79.243611+0.002397j
[2025-09-10 10:00:51] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -79.304820-0.002735j
[2025-09-10 10:01:25] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -79.303949-0.002911j
[2025-09-10 10:02:00] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -79.278382+0.002062j
[2025-09-10 10:02:34] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -79.437918-0.000701j
[2025-09-10 10:03:09] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -79.392714+0.002670j
[2025-09-10 10:03:43] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -79.363311-0.001843j
[2025-09-10 10:04:18] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -79.413111+0.002548j
[2025-09-10 10:04:52] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -79.296015+0.000893j
[2025-09-10 10:05:26] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -79.426044-0.002590j
[2025-09-10 10:06:01] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -79.311878-0.001902j
[2025-09-10 10:06:35] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -79.361785-0.001243j
[2025-09-10 10:07:10] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -79.230804+0.000451j
[2025-09-10 10:07:44] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -79.263027-0.002068j
[2025-09-10 10:08:19] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -79.381086-0.003673j
[2025-09-10 10:08:53] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -79.416908+0.001231j
[2025-09-10 10:09:28] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -79.245741+0.001295j
[2025-09-10 10:10:02] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -79.341473-0.000494j
[2025-09-10 10:10:02] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-10 10:10:37] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -79.364286+0.002499j
[2025-09-10 10:11:11] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -79.184663-0.002051j
[2025-09-10 10:11:46] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -79.344259-0.000288j
[2025-09-10 10:12:20] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -79.274735+0.004455j
[2025-09-10 10:12:55] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -79.258109+0.000423j
[2025-09-10 10:13:29] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -79.349925+0.002700j
[2025-09-10 10:14:04] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -79.189176+0.002503j
[2025-09-10 10:14:38] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -79.060458+0.003625j
[2025-09-10 10:15:13] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -79.239201-0.000446j
[2025-09-10 10:15:47] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -79.179221+0.002747j
[2025-09-10 10:16:22] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -79.148155-0.004624j
[2025-09-10 10:16:56] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -79.254498+0.004016j
[2025-09-10 10:17:31] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -79.288820+0.002262j
[2025-09-10 10:18:05] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -79.144239-0.000514j
[2025-09-10 10:18:40] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -79.169185+0.003451j
[2025-09-10 10:19:14] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -79.270413-0.000245j
[2025-09-10 10:19:49] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -79.487686+0.003168j
[2025-09-10 10:20:23] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -79.509365+0.001043j
[2025-09-10 10:20:57] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -79.430821-0.001372j
[2025-09-10 10:21:32] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -79.397276-0.000270j
[2025-09-10 10:22:07] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -79.350018-0.000548j
[2025-09-10 10:22:41] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -79.353322+0.001314j
[2025-09-10 10:23:16] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -79.299628-0.000649j
[2025-09-10 10:23:51] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -79.247213-0.000422j
[2025-09-10 10:24:26] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -79.271599+0.001277j
[2025-09-10 10:25:00] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -79.427510+0.009004j
[2025-09-10 10:25:34] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -79.468830-0.002100j
[2025-09-10 10:26:09] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -79.305124+0.000339j
[2025-09-10 10:26:43] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -79.291048-0.003186j
[2025-09-10 10:27:18] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -79.298616-0.002274j
[2025-09-10 10:27:52] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -79.308491-0.001201j
[2025-09-10 10:28:27] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -79.577138-0.002080j
[2025-09-10 10:29:01] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -79.450751-0.009617j
[2025-09-10 10:29:36] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -79.355651+0.000100j
[2025-09-10 10:30:10] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -79.325638+0.001115j
[2025-09-10 10:30:45] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -79.419652-0.004248j
[2025-09-10 10:31:19] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -79.281936+0.002155j
[2025-09-10 10:31:53] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -79.254264-0.001251j
[2025-09-10 10:32:28] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -79.394443-0.002460j
[2025-09-10 10:33:02] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -79.287322+0.005344j
[2025-09-10 10:33:37] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -79.150356+0.001400j
[2025-09-10 10:34:12] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -79.156784-0.000440j
[2025-09-10 10:34:46] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -79.324288-0.000600j
[2025-09-10 10:35:20] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -79.346817+0.000061j
[2025-09-10 10:35:55] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -79.377421-0.002791j
[2025-09-10 10:36:29] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -79.287601+0.002098j
[2025-09-10 10:37:04] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -79.338010+0.001185j
[2025-09-10 10:37:38] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -79.392188+0.002431j
[2025-09-10 10:38:12] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -79.565822-0.000705j
[2025-09-10 10:38:47] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -79.410043-0.000960j
[2025-09-10 10:39:21] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -79.451356+0.001819j
[2025-09-10 10:39:56] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -79.415861-0.002651j
[2025-09-10 10:40:30] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -79.326954-0.002697j
[2025-09-10 10:41:05] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -79.317570-0.000784j
[2025-09-10 10:41:40] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -79.303474-0.004244j
[2025-09-10 10:42:15] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -79.190395-0.001588j
[2025-09-10 10:42:49] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -79.324591-0.002826j
[2025-09-10 10:43:24] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -79.292977-0.001388j
[2025-09-10 10:43:59] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -79.326122-0.000353j
[2025-09-10 10:44:34] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -79.284306+0.001426j
[2025-09-10 10:45:08] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -79.193964-0.001878j
[2025-09-10 10:45:43] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -79.207307+0.003070j
[2025-09-10 10:46:18] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -79.344763-0.004716j
[2025-09-10 10:46:52] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -79.350750+0.002433j
[2025-09-10 10:47:27] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -79.344901+0.000686j
[2025-09-10 10:48:02] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -79.380394+0.004503j
[2025-09-10 10:48:37] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -79.344567-0.002148j
[2025-09-10 10:49:11] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -79.483614+0.000297j
[2025-09-10 10:49:46] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -79.421039-0.001502j
[2025-09-10 10:50:21] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -79.413009-0.000569j
[2025-09-10 10:50:56] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -79.457111+0.000116j
[2025-09-10 10:51:31] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -79.411076+0.005352j
[2025-09-10 10:52:05] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -79.321466+0.007195j
[2025-09-10 10:52:40] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -79.443312+0.004639j
[2025-09-10 10:53:15] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -79.414113-0.000332j
[2025-09-10 10:53:49] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -79.361337+0.000946j
[2025-09-10 10:54:23] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -79.530459-0.001088j
[2025-09-10 10:54:58] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -79.348712-0.002666j
[2025-09-10 10:55:33] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -79.312765-0.000813j
[2025-09-10 10:56:07] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -79.340294+0.004465j
[2025-09-10 10:56:41] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -79.311687-0.001273j
[2025-09-10 10:57:16] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -79.261108+0.000091j
[2025-09-10 10:57:50] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -79.221309-0.004729j
[2025-09-10 10:58:25] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -79.210345+0.000138j
[2025-09-10 10:58:59] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -79.125154+0.003473j
[2025-09-10 10:59:34] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -79.165450+0.008415j
[2025-09-10 11:00:08] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -79.128488-0.000734j
[2025-09-10 11:00:43] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -79.144937+0.000473j
[2025-09-10 11:01:17] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -79.233606-0.000579j
[2025-09-10 11:01:52] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -79.315893+0.001820j
[2025-09-10 11:02:26] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -79.187091+0.001546j
[2025-09-10 11:03:00] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -79.105493-0.002591j
[2025-09-10 11:03:35] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -79.251160+0.003156j
[2025-09-10 11:04:10] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -79.263311+0.002979j
[2025-09-10 11:04:45] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -79.347167-0.006202j
[2025-09-10 11:05:19] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -79.387432-0.000142j
[2025-09-10 11:05:54] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -79.292393+0.001566j
[2025-09-10 11:06:29] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -79.315384+0.001853j
[2025-09-10 11:07:04] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -79.328034-0.002672j
[2025-09-10 11:07:38] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -79.202424-0.000205j
[2025-09-10 11:08:13] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -79.285174-0.001258j
[2025-09-10 11:08:48] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -79.286018+0.000586j
[2025-09-10 11:09:23] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -79.308206-0.002441j
[2025-09-10 11:09:57] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -79.440248+0.002334j
[2025-09-10 11:10:32] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -79.261803-0.003451j
[2025-09-10 11:10:32] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-10 11:11:07] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -79.279134+0.003238j
[2025-09-10 11:11:42] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -79.115591+0.000809j
[2025-09-10 11:12:16] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -79.190244-0.003553j
[2025-09-10 11:12:51] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -79.110037-0.001525j
[2025-09-10 11:13:26] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -79.238181-0.000690j
[2025-09-10 11:14:01] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -79.267425-0.003934j
[2025-09-10 11:14:36] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -79.244894+0.000414j
[2025-09-10 11:15:10] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -79.236734+0.001286j
[2025-09-10 11:15:45] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -79.311417+0.000494j
[2025-09-10 11:16:20] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -79.231798-0.004521j
[2025-09-10 11:16:55] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -79.269135-0.006728j
[2025-09-10 11:17:29] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -79.189364-0.001489j
[2025-09-10 11:18:04] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -79.219670-0.002554j
[2025-09-10 11:18:39] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -79.434587+0.001873j
[2025-09-10 11:19:14] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -79.279779-0.002117j
[2025-09-10 11:19:48] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -79.296047+0.004673j
[2025-09-10 11:20:23] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -79.325671-0.001423j
[2025-09-10 11:20:57] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -79.366952+0.001750j
[2025-09-10 11:21:32] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -79.335486-0.000776j
[2025-09-10 11:22:07] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -79.342563-0.002063j
[2025-09-10 11:22:41] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -79.524388-0.001433j
[2025-09-10 11:23:15] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -79.449276+0.002718j
[2025-09-10 11:23:50] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -79.352104-0.001885j
[2025-09-10 11:24:24] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -79.355725+0.002388j
[2025-09-10 11:24:59] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -79.321822+0.001617j
[2025-09-10 11:25:33] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -79.337467+0.007051j
[2025-09-10 11:26:08] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -79.275672+0.001736j
[2025-09-10 11:26:42] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -79.464246+0.005240j
[2025-09-10 11:27:17] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -79.401319-0.000277j
[2025-09-10 11:27:51] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -79.485441+0.001033j
[2025-09-10 11:27:51] RESTART #2 | Period: 600
[2025-09-10 11:28:26] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -79.392308+0.000885j
[2025-09-10 11:29:00] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -79.293986+0.000998j
[2025-09-10 11:29:35] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -79.341837+0.003402j
[2025-09-10 11:30:10] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -79.304034-0.000181j
[2025-09-10 11:30:44] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -79.268811+0.001603j
[2025-09-10 11:31:19] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -79.296589-0.004467j
[2025-09-10 11:31:54] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -79.311796+0.000625j
[2025-09-10 11:32:29] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -79.226118+0.000967j
[2025-09-10 11:33:04] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -79.308641+0.003194j
[2025-09-10 11:33:38] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -79.181955+0.003640j
[2025-09-10 11:34:13] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -79.438234-0.001146j
[2025-09-10 11:34:48] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -79.222699-0.001608j
[2025-09-10 11:35:21] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -79.133470-0.002025j
[2025-09-10 11:35:56] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -79.153737-0.002112j
[2025-09-10 11:36:31] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -79.145477+0.003736j
[2025-09-10 11:37:06] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -79.088182+0.004092j
[2025-09-10 11:37:40] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -79.256235+0.005460j
[2025-09-10 11:38:15] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -79.209760+0.003215j
[2025-09-10 11:38:50] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -79.207352-0.000541j
[2025-09-10 11:39:25] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -79.307254+0.003665j
[2025-09-10 11:39:59] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -79.353975+0.003783j
[2025-09-10 11:40:34] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -79.538939+0.001947j
[2025-09-10 11:41:09] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -79.351617-0.000343j
[2025-09-10 11:41:44] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -79.357229+0.003230j
[2025-09-10 11:42:18] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -79.240039+0.008248j
[2025-09-10 11:42:53] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -79.266257-0.001220j
[2025-09-10 11:43:28] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -79.238510-0.000173j
[2025-09-10 11:44:02] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -79.205663-0.006301j
[2025-09-10 11:44:37] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -79.325069-0.003952j
[2025-09-10 11:45:11] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -79.326717+0.000433j
[2025-09-10 11:45:46] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -79.295318+0.000431j
[2025-09-10 11:46:20] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -79.454949-0.000006j
[2025-09-10 11:46:55] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -79.428877+0.001709j
[2025-09-10 11:47:29] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -79.553742-0.002521j
[2025-09-10 11:48:04] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -79.592701+0.000697j
[2025-09-10 11:48:38] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -79.604714+0.001800j
[2025-09-10 11:49:13] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -79.491865+0.006619j
[2025-09-10 11:49:47] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -79.416375-0.003058j
[2025-09-10 11:50:22] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -79.439207-0.007527j
[2025-09-10 11:50:56] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -79.327761+0.000272j
[2025-09-10 11:51:31] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -79.567173+0.002053j
[2025-09-10 11:52:05] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -79.501892-0.001014j
[2025-09-10 11:52:40] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -79.391261+0.001127j
[2025-09-10 11:53:14] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -79.298537-0.005045j
[2025-09-10 11:53:48] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -79.349281+0.007874j
[2025-09-10 11:54:23] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -79.437889-0.001627j
[2025-09-10 11:54:57] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -79.487888-0.006904j
[2025-09-10 11:55:32] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -79.463492-0.003263j
[2025-09-10 11:56:06] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -79.419004+0.001445j
[2025-09-10 11:56:41] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -79.412524+0.008976j
[2025-09-10 11:57:15] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -79.443946-0.003133j
[2025-09-10 11:57:50] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -79.429233+0.004692j
[2025-09-10 11:58:24] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -79.555755-0.002289j
[2025-09-10 11:58:59] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -79.595146+0.002172j
[2025-09-10 11:59:33] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -79.518298+0.000563j
[2025-09-10 12:00:07] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -79.444494+0.001483j
[2025-09-10 12:00:42] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -79.441195-0.000140j
[2025-09-10 12:01:16] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -79.361349+0.003321j
[2025-09-10 12:01:51] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -79.421538+0.004277j
[2025-09-10 12:02:25] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -79.313416-0.000459j
[2025-09-10 12:03:00] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -79.339678+0.004173j
[2025-09-10 12:03:34] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -79.298970+0.001609j
[2025-09-10 12:04:09] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -79.221351+0.002338j
[2025-09-10 12:04:44] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -79.364370+0.004940j
[2025-09-10 12:05:18] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -79.305094+0.002876j
[2025-09-10 12:05:53] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -79.286482+0.004331j
[2025-09-10 12:06:28] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -79.473866-0.000561j
[2025-09-10 12:07:03] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -79.414150-0.002222j
[2025-09-10 12:07:37] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -79.285772+0.000545j
[2025-09-10 12:08:12] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -79.376636+0.000319j
[2025-09-10 12:08:46] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -79.248391-0.001232j
[2025-09-10 12:09:21] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -79.363108-0.001555j
[2025-09-10 12:09:55] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -79.249899+0.006043j
[2025-09-10 12:10:30] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -79.184906-0.002096j
[2025-09-10 12:11:04] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -79.251885-0.005870j
[2025-09-10 12:11:04] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-10 12:11:38] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -79.245521-0.000728j
[2025-09-10 12:12:13] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -79.254181-0.001538j
[2025-09-10 12:12:48] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -79.272178-0.003242j
[2025-09-10 12:13:22] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -79.221293+0.003669j
[2025-09-10 12:13:57] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -79.222926-0.003437j
[2025-09-10 12:14:32] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -79.137015+0.001638j
[2025-09-10 12:15:07] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -79.382476+0.002486j
[2025-09-10 12:15:42] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -79.374174+0.002720j
[2025-09-10 12:16:16] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -79.478371-0.000107j
[2025-09-10 12:16:51] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -79.471115+0.000676j
[2025-09-10 12:17:26] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -79.384885+0.001262j
[2025-09-10 12:18:01] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -79.383866-0.003633j
[2025-09-10 12:18:35] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -79.280434+0.005932j
[2025-09-10 12:19:10] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -79.428737-0.000981j
[2025-09-10 12:19:45] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -79.443627-0.001298j
[2025-09-10 12:20:20] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -79.492917-0.002829j
[2025-09-10 12:20:55] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -79.466502+0.004157j
[2025-09-10 12:21:29] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -79.472589-0.001096j
[2025-09-10 12:22:04] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -79.352730+0.002018j
[2025-09-10 12:22:38] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -79.382340-0.001283j
[2025-09-10 12:23:13] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -79.381489-0.001067j
[2025-09-10 12:23:47] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -79.323269-0.002148j
[2025-09-10 12:24:22] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -79.343392-0.003270j
[2025-09-10 12:24:56] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -79.425237-0.001149j
[2025-09-10 12:25:31] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -79.370342+0.000951j
[2025-09-10 12:26:05] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -79.397189+0.001367j
[2025-09-10 12:26:40] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -79.366227-0.001974j
[2025-09-10 12:27:14] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -79.223770-0.005024j
[2025-09-10 12:27:48] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -79.226580+0.001039j
[2025-09-10 12:28:23] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -79.127537+0.003029j
[2025-09-10 12:28:57] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -79.198607+0.002417j
[2025-09-10 12:29:32] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -79.266439-0.001220j
[2025-09-10 12:30:06] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -79.262949+0.002732j
[2025-09-10 12:30:41] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -79.314259-0.002223j
[2025-09-10 12:31:15] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -79.252226+0.004633j
[2025-09-10 12:31:50] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -79.335668+0.000204j
[2025-09-10 12:32:24] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -79.503796-0.003083j
[2025-09-10 12:32:59] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -79.560419-0.005926j
[2025-09-10 12:33:34] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -79.435829-0.003421j
[2025-09-10 12:34:09] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -79.041207-0.002407j
[2025-09-10 12:34:43] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -79.082635-0.002532j
[2025-09-10 12:35:18] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -79.256487-0.000549j
[2025-09-10 12:35:53] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -79.117908+0.001348j
[2025-09-10 12:36:28] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -79.240102-0.003128j
[2025-09-10 12:37:02] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -79.165471+0.001523j
[2025-09-10 12:37:37] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -79.396402+0.004245j
[2025-09-10 12:38:11] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -79.399601+0.001415j
[2025-09-10 12:38:46] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -79.415656-0.004340j
[2025-09-10 12:39:21] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -79.328354-0.002312j
[2025-09-10 12:39:56] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -79.361853-0.002051j
[2025-09-10 12:40:30] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -79.335995+0.002670j
[2025-09-10 12:41:05] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -79.496812-0.001081j
[2025-09-10 12:41:40] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -79.364534+0.001013j
[2025-09-10 12:42:15] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -79.308230-0.003085j
[2025-09-10 12:42:49] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -79.311424+0.000442j
[2025-09-10 12:43:24] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -79.268027-0.002267j
[2025-09-10 12:43:59] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -79.389299+0.003577j
[2025-09-10 12:44:34] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -79.264855+0.001454j
[2025-09-10 12:45:09] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -79.289557-0.003876j
[2025-09-10 12:45:43] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -79.205539-0.002308j
[2025-09-10 12:46:18] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -79.272507-0.002372j
[2025-09-10 12:46:53] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -79.322323+0.001023j
[2025-09-10 12:47:28] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -79.276220-0.002662j
[2025-09-10 12:48:02] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -79.303030-0.001422j
[2025-09-10 12:48:37] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -79.444805+0.002092j
[2025-09-10 12:49:12] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -79.239318-0.006528j
[2025-09-10 12:49:47] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -79.347482-0.005902j
[2025-09-10 12:50:21] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -79.444359+0.001079j
[2025-09-10 12:50:56] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -79.338359-0.004091j
[2025-09-10 12:51:31] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -79.281421-0.001839j
[2025-09-10 12:52:05] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -79.327424+0.000157j
[2025-09-10 12:52:40] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -79.390142-0.001304j
[2025-09-10 12:53:15] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -79.306374+0.001912j
[2025-09-10 12:53:50] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -79.335030-0.000693j
[2025-09-10 12:54:24] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -79.247738-0.003090j
[2025-09-10 12:54:59] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -79.290386+0.002082j
[2025-09-10 12:55:34] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -79.307169-0.001786j
[2025-09-10 12:56:08] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -79.372957+0.003282j
[2025-09-10 12:56:43] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -79.314663+0.001062j
[2025-09-10 12:57:18] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -79.404445+0.001717j
[2025-09-10 12:57:53] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -79.405796-0.000126j
[2025-09-10 12:58:27] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -79.432090-0.000731j
[2025-09-10 12:59:02] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -79.395644+0.002032j
[2025-09-10 12:59:36] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -79.483630-0.001697j
[2025-09-10 13:00:10] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -79.387315+0.002116j
[2025-09-10 13:00:45] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -79.546690+0.000258j
[2025-09-10 13:01:19] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -79.518622-0.003352j
[2025-09-10 13:01:54] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -79.363886+0.001763j
[2025-09-10 13:02:28] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -79.373627-0.005183j
[2025-09-10 13:03:03] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -79.482011+0.005667j
[2025-09-10 13:03:37] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -79.469102+0.002965j
[2025-09-10 13:04:12] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -79.414445-0.003912j
[2025-09-10 13:04:46] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -79.446975+0.004381j
[2025-09-10 13:05:21] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -79.427570-0.001426j
[2025-09-10 13:05:55] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -79.342070-0.000670j
[2025-09-10 13:06:29] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -79.349265+0.003536j
[2025-09-10 13:07:04] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -79.378691-0.000310j
[2025-09-10 13:07:38] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -79.367450+0.004787j
[2025-09-10 13:08:13] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -79.495384-0.000071j
[2025-09-10 13:08:47] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -79.382527+0.001254j
[2025-09-10 13:09:22] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -79.467457+0.000901j
[2025-09-10 13:09:56] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -79.486031-0.001447j
[2025-09-10 13:10:31] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -79.532181-0.002542j
[2025-09-10 13:11:05] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -79.537208+0.000792j
[2025-09-10 13:11:39] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -79.450704-0.000178j
[2025-09-10 13:11:39] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-10 13:12:13] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -79.389955+0.001703j
[2025-09-10 13:12:48] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -79.448034+0.003897j
[2025-09-10 13:13:23] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -79.340946-0.000295j
[2025-09-10 13:13:57] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -79.567147-0.000338j
[2025-09-10 13:14:32] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -79.371886-0.000173j
[2025-09-10 13:15:06] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -79.283821+0.002780j
[2025-09-10 13:15:41] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -79.458455+0.003985j
[2025-09-10 13:16:15] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -79.362517-0.000138j
[2025-09-10 13:16:50] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -79.418058+0.001884j
[2025-09-10 13:17:25] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -79.291749+0.010450j
[2025-09-10 13:17:59] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -79.258321+0.001971j
[2025-09-10 13:18:34] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -79.354123+0.002670j
[2025-09-10 13:19:09] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -79.338197+0.004604j
[2025-09-10 13:19:44] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -79.443665+0.011173j
[2025-09-10 13:20:18] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -79.274405+0.002756j
[2025-09-10 13:20:53] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -79.166240+0.006046j
[2025-09-10 13:21:28] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -79.236250+0.000986j
[2025-09-10 13:22:03] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -79.332222-0.006219j
[2025-09-10 13:22:37] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -79.174339-0.002667j
[2025-09-10 13:23:12] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -79.180973+0.003720j
[2025-09-10 13:23:47] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -79.402055+0.001779j
[2025-09-10 13:24:22] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -79.466579+0.005304j
[2025-09-10 13:24:56] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -79.322367-0.000808j
[2025-09-10 13:25:31] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -79.297712+0.003208j
[2025-09-10 13:26:06] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -79.370353-0.000096j
[2025-09-10 13:26:41] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -79.410341+0.000567j
[2025-09-10 13:27:15] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -79.354978-0.000832j
[2025-09-10 13:27:50] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -79.250738+0.002427j
[2025-09-10 13:28:25] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -79.348194-0.002673j
[2025-09-10 13:29:00] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -79.392626+0.006254j
[2025-09-10 13:29:34] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -79.289841+0.004898j
[2025-09-10 13:30:09] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -79.424114-0.001237j
[2025-09-10 13:30:44] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -79.348093+0.004760j
[2025-09-10 13:31:19] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -79.454675+0.005283j
[2025-09-10 13:31:54] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -79.386127+0.002284j
[2025-09-10 13:32:29] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -79.313509-0.003603j
[2025-09-10 13:33:03] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -79.328883-0.000336j
[2025-09-10 13:33:38] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -79.323190+0.001403j
[2025-09-10 13:34:13] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -79.483723-0.000291j
[2025-09-10 13:34:47] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -79.231259+0.002086j
[2025-09-10 13:35:22] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -79.202086-0.003586j
[2025-09-10 13:35:57] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -79.302933+0.001305j
[2025-09-10 13:36:32] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -79.420466-0.005815j
[2025-09-10 13:37:06] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -79.414145-0.002332j
[2025-09-10 13:37:41] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -79.529109+0.000897j
[2025-09-10 13:38:16] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -79.419789+0.001106j
[2025-09-10 13:38:51] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -79.296374-0.001646j
[2025-09-10 13:39:26] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -79.267468-0.001408j
[2025-09-10 13:40:00] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -79.337368-0.000152j
[2025-09-10 13:40:35] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -79.251136+0.001712j
[2025-09-10 13:41:10] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -79.242826-0.003725j
[2025-09-10 13:41:44] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -79.258846+0.003928j
[2025-09-10 13:42:19] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -79.293219-0.002785j
[2025-09-10 13:42:54] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -79.136008+0.000009j
[2025-09-10 13:43:29] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -79.146634-0.003601j
[2025-09-10 13:44:03] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -79.153566+0.007672j
[2025-09-10 13:44:38] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -79.263484-0.002305j
[2025-09-10 13:45:13] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -79.267099-0.001453j
[2025-09-10 13:45:48] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -79.380629+0.003845j
[2025-09-10 13:46:22] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -79.295705-0.001464j
[2025-09-10 13:46:57] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -79.207140+0.005575j
[2025-09-10 13:47:32] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -79.202385-0.002514j
[2025-09-10 13:48:07] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -79.236421+0.002328j
[2025-09-10 13:48:41] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -79.371036-0.001690j
[2025-09-10 13:49:16] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -79.447988-0.000864j
[2025-09-10 13:49:51] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -79.520214+0.002236j
[2025-09-10 13:50:26] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -79.372712+0.000315j
[2025-09-10 13:51:00] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -79.351302+0.003255j
[2025-09-10 13:51:35] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -79.335292+0.003828j
[2025-09-10 13:52:10] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -79.244932-0.001463j
[2025-09-10 13:52:45] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -79.390578+0.000573j
[2025-09-10 13:53:20] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -79.471912+0.002031j
[2025-09-10 13:53:54] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -79.549190-0.002212j
[2025-09-10 13:54:29] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -79.423025+0.002714j
[2025-09-10 13:55:04] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -79.436795+0.001543j
[2025-09-10 13:55:38] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -79.463351-0.000690j
[2025-09-10 13:56:13] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -79.388034+0.000560j
[2025-09-10 13:56:48] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -79.420202-0.002969j
[2025-09-10 13:57:23] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -79.294246+0.001571j
[2025-09-10 13:57:57] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -79.230618-0.000124j
[2025-09-10 13:58:32] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -79.394770-0.004293j
[2025-09-10 13:59:06] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -79.366211+0.001155j
[2025-09-10 13:59:41] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -79.416673-0.004397j
[2025-09-10 14:00:15] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -79.421047+0.003251j
[2025-09-10 14:00:50] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -79.381619+0.003179j
[2025-09-10 14:01:25] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -79.520903-0.000525j
[2025-09-10 14:02:00] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -79.523489-0.000453j
[2025-09-10 14:02:34] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -79.389003+0.002822j
[2025-09-10 14:03:09] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -79.375704-0.000922j
[2025-09-10 14:03:44] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -79.313798+0.004214j
[2025-09-10 14:04:19] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -79.426877+0.002424j
[2025-09-10 14:04:53] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -79.315732+0.000630j
[2025-09-10 14:05:28] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -79.374484+0.000563j
[2025-09-10 14:06:03] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -79.331149-0.003816j
[2025-09-10 14:06:38] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -79.280928-0.002461j
[2025-09-10 14:07:13] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -79.355745-0.005055j
[2025-09-10 14:07:48] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -79.415893-0.000775j
[2025-09-10 14:08:22] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -79.242789-0.002575j
[2025-09-10 14:08:57] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -79.239717+0.000923j
[2025-09-10 14:09:32] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -79.401204-0.001706j
[2025-09-10 14:10:07] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -79.214387-0.001967j
[2025-09-10 14:10:41] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -79.275641+0.005329j
[2025-09-10 14:11:16] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -79.297071-0.001806j
[2025-09-10 14:11:50] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -79.310813-0.003567j
[2025-09-10 14:12:24] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -79.246016-0.002244j
[2025-09-10 14:12:24] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-10 14:12:59] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -79.258616+0.001566j
[2025-09-10 14:13:34] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -79.469205-0.000198j
[2025-09-10 14:14:08] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -79.402714+0.002748j
[2025-09-10 14:14:43] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -79.206126+0.000349j
[2025-09-10 14:15:18] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -79.313960-0.004510j
[2025-09-10 14:15:52] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -79.072743-0.001391j
[2025-09-10 14:16:27] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -79.292977-0.001703j
[2025-09-10 14:17:02] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -79.196496+0.004813j
[2025-09-10 14:17:37] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -79.251870-0.000165j
[2025-09-10 14:18:12] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -79.140915+0.005001j
[2025-09-10 14:18:47] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -79.138178-0.002472j
[2025-09-10 14:19:21] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -79.023521-0.001059j
[2025-09-10 14:19:56] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -79.073830-0.000510j
[2025-09-10 14:20:31] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -79.268454+0.002249j
[2025-09-10 14:21:05] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -79.210215-0.003610j
[2025-09-10 14:21:40] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -79.250066-0.002020j
[2025-09-10 14:22:14] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -79.064094-0.001529j
[2025-09-10 14:22:48] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -79.159837+0.003591j
[2025-09-10 14:23:23] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -79.094653+0.002619j
[2025-09-10 14:23:57] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -79.228128+0.004205j
[2025-09-10 14:24:32] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -79.292369+0.000626j
[2025-09-10 14:25:06] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -79.255937+0.006706j
[2025-09-10 14:25:41] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -79.262447-0.001572j
[2025-09-10 14:26:15] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -79.247221-0.002764j
[2025-09-10 14:26:50] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -79.393776+0.003067j
[2025-09-10 14:27:24] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -79.425710-0.004294j
[2025-09-10 14:27:59] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -79.357998+0.000059j
[2025-09-10 14:28:34] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -79.291976-0.002839j
[2025-09-10 14:29:08] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -79.238517-0.000035j
[2025-09-10 14:29:43] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -79.204363+0.001438j
[2025-09-10 14:30:17] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -79.259898+0.001241j
[2025-09-10 14:30:52] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -79.213736-0.002830j
[2025-09-10 14:31:27] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -79.310490-0.000744j
[2025-09-10 14:32:01] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -79.314147+0.000152j
[2025-09-10 14:32:36] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -79.312820-0.002022j
[2025-09-10 14:33:11] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -79.313815+0.001886j
[2025-09-10 14:33:45] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -79.263259-0.001229j
[2025-09-10 14:34:20] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -79.280371+0.001178j
[2025-09-10 14:34:55] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -79.412266-0.000020j
[2025-09-10 14:35:29] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -79.460990+0.006849j
[2025-09-10 14:36:04] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -79.484177-0.004128j
[2025-09-10 14:36:39] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -79.595854-0.000941j
[2025-09-10 14:37:14] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -79.568300-0.001466j
[2025-09-10 14:37:48] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -79.366026-0.000051j
[2025-09-10 14:38:23] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -79.293529-0.002122j
[2025-09-10 14:38:58] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -79.270290+0.000390j
[2025-09-10 14:39:33] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -79.316281+0.000361j
[2025-09-10 14:40:08] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -79.183705-0.000500j
[2025-09-10 14:40:42] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -79.250657-0.000138j
[2025-09-10 14:41:17] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -79.326594-0.000598j
[2025-09-10 14:41:52] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -79.355419-0.002287j
[2025-09-10 14:42:26] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -79.218649+0.005114j
[2025-09-10 14:43:01] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -79.235849+0.000047j
[2025-09-10 14:43:36] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -79.292970-0.002322j
[2025-09-10 14:44:11] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -79.430116+0.000803j
[2025-09-10 14:44:45] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -79.418234-0.001678j
[2025-09-10 14:45:20] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -79.287111-0.002813j
[2025-09-10 14:45:55] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -79.280874-0.004674j
[2025-09-10 14:46:30] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -79.377138-0.001750j
[2025-09-10 14:47:04] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -79.306645-0.000912j
[2025-09-10 14:47:39] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -79.237239-0.005771j
[2025-09-10 14:48:14] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -79.143639-0.007361j
[2025-09-10 14:48:48] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -79.255175-0.000081j
[2025-09-10 14:49:23] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -79.304027-0.000465j
[2025-09-10 14:49:58] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -79.258478+0.002293j
[2025-09-10 14:50:33] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -79.098536+0.001171j
[2025-09-10 14:51:07] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -79.230172+0.001886j
[2025-09-10 14:51:42] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -79.257656+0.002140j
[2025-09-10 14:52:17] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -79.425681-0.006436j
[2025-09-10 14:52:52] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -79.307888-0.000192j
[2025-09-10 14:53:27] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -79.293151+0.000978j
[2025-09-10 14:54:01] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -79.236391+0.002036j
[2025-09-10 14:54:36] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -79.304611+0.002339j
[2025-09-10 14:55:11] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -79.248589+0.002834j
[2025-09-10 14:55:46] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -79.321841+0.000588j
[2025-09-10 14:56:20] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -79.209544+0.000252j
[2025-09-10 14:56:55] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -79.193674-0.001451j
[2025-09-10 14:57:30] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -79.126495+0.001214j
[2025-09-10 14:58:05] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -79.236027-0.003124j
[2025-09-10 14:58:39] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -79.447254-0.000268j
[2025-09-10 14:59:14] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -79.364707+0.005717j
[2025-09-10 14:59:49] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -79.265099+0.002851j
[2025-09-10 15:00:24] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -79.241225+0.002532j
[2025-09-10 15:00:58] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -79.141532-0.001523j
[2025-09-10 15:01:33] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -79.232249-0.000317j
[2025-09-10 15:02:08] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -79.248001+0.002394j
[2025-09-10 15:02:43] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -79.253390+0.002003j
[2025-09-10 15:03:18] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -79.379575+0.003826j
[2025-09-10 15:03:52] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -79.362367-0.002035j
[2025-09-10 15:04:27] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -79.305658+0.002933j
[2025-09-10 15:05:02] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -79.302240+0.001408j
[2025-09-10 15:05:37] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -79.304096-0.002455j
[2025-09-10 15:06:11] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -79.281667-0.000421j
[2025-09-10 15:06:46] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -79.228862-0.004378j
[2025-09-10 15:07:21] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -79.362240+0.004448j
[2025-09-10 15:07:56] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -79.302058-0.000442j
[2025-09-10 15:08:30] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -79.255618-0.004200j
[2025-09-10 15:09:05] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -79.086491+0.003388j
[2025-09-10 15:09:40] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -79.220508-0.000710j
[2025-09-10 15:10:15] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -79.266429+0.003961j
[2025-09-10 15:10:49] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -79.209570-0.001413j
[2025-09-10 15:11:24] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -79.350073-0.000582j
[2025-09-10 15:11:59] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -79.280064-0.000212j
[2025-09-10 15:12:34] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -79.144715-0.002274j
[2025-09-10 15:13:08] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -79.255775-0.002350j
[2025-09-10 15:13:08] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-10 15:13:43] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -79.332722-0.004231j
[2025-09-10 15:14:18] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -79.221078+0.001276j
[2025-09-10 15:14:53] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -79.388560+0.000797j
[2025-09-10 15:15:28] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -79.310216+0.001167j
[2025-09-10 15:16:02] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -79.377579-0.005154j
[2025-09-10 15:16:37] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -79.348784+0.005741j
[2025-09-10 15:17:12] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -79.485020+0.000504j
[2025-09-10 15:17:47] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -79.506698+0.000844j
[2025-09-10 15:18:21] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -79.539402-0.000290j
[2025-09-10 15:18:56] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -79.385145-0.002048j
[2025-09-10 15:19:31] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -79.467647-0.001186j
[2025-09-10 15:20:06] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -79.533352+0.001304j
[2025-09-10 15:20:40] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -79.470798+0.007331j
[2025-09-10 15:21:15] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -79.460336-0.000480j
[2025-09-10 15:21:50] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -79.500208-0.001746j
[2025-09-10 15:22:25] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -79.501291+0.001414j
[2025-09-10 15:22:59] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -79.449173+0.000987j
[2025-09-10 15:23:34] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -79.342222+0.005549j
[2025-09-10 15:24:09] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -79.453231-0.001904j
[2025-09-10 15:24:44] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -79.402540-0.002524j
[2025-09-10 15:25:18] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -79.365218+0.002795j
[2025-09-10 15:25:53] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -79.179168-0.000214j
[2025-09-10 15:26:27] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -79.279080-0.001427j
[2025-09-10 15:27:02] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -79.305098-0.005724j
[2025-09-10 15:27:36] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -79.244121+0.008398j
[2025-09-10 15:28:11] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -79.293236-0.001908j
[2025-09-10 15:28:45] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -79.330648+0.000854j
[2025-09-10 15:29:19] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -79.176769+0.002209j
[2025-09-10 15:29:54] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -79.153498+0.007098j
[2025-09-10 15:30:28] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -79.061203-0.006212j
[2025-09-10 15:31:03] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -79.159392+0.000160j
[2025-09-10 15:31:37] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -79.177878-0.004283j
[2025-09-10 15:32:12] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -79.257249-0.000014j
[2025-09-10 15:32:46] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -79.150988+0.004270j
[2025-09-10 15:33:21] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -79.204010-0.000540j
[2025-09-10 15:33:55] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -79.369792+0.000148j
[2025-09-10 15:34:29] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -79.252447-0.000885j
[2025-09-10 15:35:04] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -79.170908-0.003879j
[2025-09-10 15:35:38] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -79.305845-0.001714j
[2025-09-10 15:36:13] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -79.317005+0.002927j
[2025-09-10 15:36:47] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -79.352748+0.002775j
[2025-09-10 15:37:22] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -79.327674-0.001385j
[2025-09-10 15:37:57] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -79.305188+0.005766j
[2025-09-10 15:38:31] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -79.355728-0.003540j
[2025-09-10 15:39:05] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -79.349172+0.002610j
[2025-09-10 15:39:40] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -79.385467-0.001482j
[2025-09-10 15:40:14] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -79.454863+0.005010j
[2025-09-10 15:40:49] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -79.328122-0.001401j
[2025-09-10 15:41:23] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -79.361477+0.001188j
[2025-09-10 15:41:57] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -79.483436-0.003718j
[2025-09-10 15:42:32] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -79.466737-0.005362j
[2025-09-10 15:43:06] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -79.445480+0.002411j
[2025-09-10 15:43:41] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -79.338759-0.000496j
[2025-09-10 15:44:15] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -79.295826-0.006231j
[2025-09-10 15:44:50] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -79.292475-0.001870j
[2025-09-10 15:45:24] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -79.379187-0.001948j
[2025-09-10 15:45:58] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -79.208746-0.000634j
[2025-09-10 15:46:33] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -79.244239-0.000970j
[2025-09-10 15:47:07] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -79.319963+0.001421j
[2025-09-10 15:47:42] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -79.150343-0.004740j
[2025-09-10 15:48:16] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -79.196229-0.000159j
[2025-09-10 15:48:51] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -79.257744+0.001128j
[2025-09-10 15:49:25] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -79.174571-0.004414j
[2025-09-10 15:50:00] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -79.195147+0.002764j
[2025-09-10 15:50:34] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -79.207089-0.001655j
[2025-09-10 15:51:09] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -79.210255+0.000985j
[2025-09-10 15:51:44] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -79.237365-0.001012j
[2025-09-10 15:52:18] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -79.179110+0.006999j
[2025-09-10 15:52:53] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -79.258216-0.000616j
[2025-09-10 15:53:28] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -79.361311+0.000563j
[2025-09-10 15:54:02] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -79.413631+0.000480j
[2025-09-10 15:54:37] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -79.231338-0.003010j
[2025-09-10 15:55:11] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -79.200493+0.001890j
[2025-09-10 15:55:46] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -79.340496-0.000567j
[2025-09-10 15:56:20] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -79.370679+0.000146j
[2025-09-10 15:56:55] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -79.388767+0.004991j
[2025-09-10 15:57:29] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -79.311918+0.000783j
[2025-09-10 15:58:04] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -79.324297-0.002205j
[2025-09-10 15:58:38] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -79.241384+0.001990j
[2025-09-10 15:59:12] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -79.140975+0.001240j
[2025-09-10 15:59:47] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -79.295107+0.004129j
[2025-09-10 16:00:22] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -79.223092+0.004456j
[2025-09-10 16:00:56] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -79.196844+0.002682j
[2025-09-10 16:01:30] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -79.222874+0.003600j
[2025-09-10 16:02:05] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -79.254973-0.001862j
[2025-09-10 16:02:39] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -79.351678+0.002004j
[2025-09-10 16:03:14] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -79.397288+0.001104j
[2025-09-10 16:03:48] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -79.282028-0.000261j
[2025-09-10 16:04:23] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -79.356770-0.002153j
[2025-09-10 16:04:57] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -79.357428+0.001629j
[2025-09-10 16:05:32] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -79.414908-0.002219j
[2025-09-10 16:06:06] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -79.545104-0.003539j
[2025-09-10 16:06:40] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -79.380985+0.000446j
[2025-09-10 16:07:15] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -79.405717+0.006365j
[2025-09-10 16:07:49] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -79.463112-0.002911j
[2025-09-10 16:08:24] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -79.432297-0.001214j
[2025-09-10 16:08:58] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -79.500077-0.004992j
[2025-09-10 16:09:33] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -79.380724-0.000037j
[2025-09-10 16:10:07] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -79.286977-0.004675j
[2025-09-10 16:10:42] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -79.353915+0.001420j
[2025-09-10 16:11:16] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -79.294445+0.001346j
[2025-09-10 16:11:51] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -79.343422+0.002717j
[2025-09-10 16:12:25] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -79.389397-0.000137j
[2025-09-10 16:13:00] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -79.393225+0.000862j
[2025-09-10 16:13:34] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -79.415726-0.004067j
[2025-09-10 16:13:34] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-10 16:14:08] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -79.231777-0.000451j
[2025-09-10 16:14:43] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -79.309187+0.002182j
[2025-09-10 16:15:18] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -79.337454-0.000825j
[2025-09-10 16:15:52] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -79.152718+0.000934j
[2025-09-10 16:16:27] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -79.225432+0.005738j
[2025-09-10 16:17:01] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -79.060392-0.002051j
[2025-09-10 16:17:35] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -79.134386+0.002927j
[2025-09-10 16:18:10] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -79.234621-0.000427j
[2025-09-10 16:18:44] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -79.213097+0.001342j
[2025-09-10 16:19:19] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -79.266237-0.000397j
[2025-09-10 16:19:53] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -79.254995-0.002606j
[2025-09-10 16:20:28] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -79.173239+0.001391j
[2025-09-10 16:21:03] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -79.129425-0.001264j
[2025-09-10 16:21:38] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -79.158644+0.003081j
[2025-09-10 16:22:12] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -79.292029+0.004256j
[2025-09-10 16:22:47] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -79.153496-0.000294j
[2025-09-10 16:23:22] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -79.302625-0.000025j
[2025-09-10 16:23:57] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -79.140983-0.002535j
[2025-09-10 16:24:31] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -79.092033+0.001673j
[2025-09-10 16:25:06] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -79.115586-0.002442j
[2025-09-10 16:25:40] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -79.110929-0.001758j
[2025-09-10 16:26:15] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -79.060497-0.005312j
[2025-09-10 16:26:49] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -79.153538-0.003056j
[2025-09-10 16:27:24] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -79.221370-0.001675j
[2025-09-10 16:27:58] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -79.261111-0.004141j
[2025-09-10 16:28:33] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -79.256617+0.000902j
[2025-09-10 16:29:07] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -79.233713+0.004558j
[2025-09-10 16:29:41] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -79.310084-0.000624j
[2025-09-10 16:30:16] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -79.409838-0.002457j
[2025-09-10 16:30:50] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -79.424454+0.004207j
[2025-09-10 16:31:25] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -79.379108+0.001018j
[2025-09-10 16:31:59] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -79.109636+0.003084j
[2025-09-10 16:32:34] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -79.185687+0.002538j
[2025-09-10 16:33:08] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -79.240882+0.005918j
[2025-09-10 16:33:43] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -79.229447+0.000181j
[2025-09-10 16:34:17] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -79.155648+0.000899j
[2025-09-10 16:34:52] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -79.018330+0.001606j
[2025-09-10 16:35:26] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -79.226166-0.003106j
[2025-09-10 16:36:01] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -79.088023+0.003523j
[2025-09-10 16:36:35] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -79.054444+0.002181j
[2025-09-10 16:37:10] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -79.162333-0.004457j
[2025-09-10 16:37:44] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -79.259830+0.000942j
[2025-09-10 16:38:18] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -79.162013+0.000358j
[2025-09-10 16:38:53] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -79.288768-0.000098j
[2025-09-10 16:39:28] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -79.327400+0.001467j
[2025-09-10 16:40:03] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -79.265356-0.001088j
[2025-09-10 16:40:37] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -79.336987-0.000568j
[2025-09-10 16:41:12] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -79.354920+0.002491j
[2025-09-10 16:41:47] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -79.374325+0.000251j
[2025-09-10 16:42:22] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -79.261336-0.002458j
[2025-09-10 16:42:56] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -79.314209+0.004637j
[2025-09-10 16:43:31] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -79.295300-0.000369j
[2025-09-10 16:44:06] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -79.187474+0.002844j
[2025-09-10 16:44:41] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -79.299488-0.000112j
[2025-09-10 16:45:16] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -79.213368+0.002988j
[2025-09-10 16:45:50] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -79.119789-0.000045j
[2025-09-10 16:46:25] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -79.046476-0.001672j
[2025-09-10 16:47:00] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -79.211998-0.000465j
[2025-09-10 16:47:34] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -79.264059-0.000613j
[2025-09-10 16:48:09] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -79.400177-0.000989j
[2025-09-10 16:48:44] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -79.291910+0.000182j
[2025-09-10 16:49:19] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -79.372019-0.002932j
[2025-09-10 16:49:53] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -79.397451-0.000748j
[2025-09-10 16:50:28] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -79.422720+0.007354j
[2025-09-10 16:51:03] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -79.382385+0.002278j
[2025-09-10 16:51:38] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -79.302755-0.003706j
[2025-09-10 16:52:12] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -79.123661-0.001339j
[2025-09-10 16:52:47] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -79.170330-0.002656j
[2025-09-10 16:53:22] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -79.256759+0.002774j
[2025-09-10 16:53:57] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -79.049098+0.000614j
[2025-09-10 16:54:31] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -79.263878+0.002631j
[2025-09-10 16:55:06] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -79.366101-0.001817j
[2025-09-10 16:55:41] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -79.345412-0.001946j
[2025-09-10 16:56:16] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -79.310973-0.000869j
[2025-09-10 16:56:50] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -79.384348-0.001021j
[2025-09-10 16:57:25] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -79.270655-0.002355j
[2025-09-10 16:58:00] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -79.329523-0.001211j
[2025-09-10 16:58:35] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -79.268345-0.002889j
[2025-09-10 16:59:10] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -79.296831+0.003795j
[2025-09-10 16:59:44] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -79.343602+0.001071j
[2025-09-10 17:00:19] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -79.288691-0.001090j
[2025-09-10 17:00:53] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -79.324996-0.004520j
[2025-09-10 17:01:27] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -79.306803+0.000704j
[2025-09-10 17:02:02] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -79.247243+0.001810j
[2025-09-10 17:02:36] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -79.279359+0.001437j
[2025-09-10 17:03:11] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -79.329618-0.000857j
[2025-09-10 17:03:45] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -79.299736-0.002785j
[2025-09-10 17:04:20] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -79.440460+0.004584j
[2025-09-10 17:04:54] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -79.454844+0.006183j
[2025-09-10 17:05:28] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -79.433214-0.001570j
[2025-09-10 17:06:03] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -79.440820-0.002960j
[2025-09-10 17:06:37] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -79.377123+0.001466j
[2025-09-10 17:07:12] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -79.415022-0.000563j
[2025-09-10 17:07:46] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -79.465240+0.000302j
[2025-09-10 17:08:21] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -79.576853+0.002314j
[2025-09-10 17:08:55] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -79.488484-0.001840j
[2025-09-10 17:09:30] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -79.476158+0.002124j
[2025-09-10 17:10:04] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -79.603209-0.000304j
[2025-09-10 17:10:39] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -79.356182-0.001737j
[2025-09-10 17:11:13] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -79.434046-0.004346j
[2025-09-10 17:11:48] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -79.357126+0.010283j
[2025-09-10 17:12:22] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -79.397020+0.000365j
[2025-09-10 17:12:57] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -79.403183+0.003276j
[2025-09-10 17:13:31] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -79.323024-0.003505j
[2025-09-10 17:14:05] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -79.322765+0.003366j
[2025-09-10 17:14:05] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-10 17:14:05] ✅ Training completed | Restarts: 2
[2025-09-10 17:14:05] ============================================================
[2025-09-10 17:14:05] Training completed | Runtime: 36522.8s
[2025-09-10 17:14:20] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-10 17:14:20] ============================================================
