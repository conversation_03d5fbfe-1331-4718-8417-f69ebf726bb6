[2025-09-12 10:31:52] 使用checkpoint文件: results/L=5/J2=0.05/J1=0.02/training/checkpoints/checkpoint_iter_000525.pkl
[2025-09-12 10:32:26] ✓ 从checkpoint加载参数: 525
[2025-09-12 10:32:26]   - 能量: -79.251885-0.005870j ± 0.113966
[2025-09-12 10:32:26] ================================================================================
[2025-09-12 10:32:26] 加载量子态: L=5, J2=0.05, J1=0.02, checkpoint=checkpoint_iter_000525
[2025-09-12 10:32:26] 使用采样数目: 1048576
[2025-09-12 10:32:26] 设置样本数为: 1048576
[2025-09-12 10:32:26] 开始生成共享样本集...
[2025-09-12 10:42:04] 样本生成完成,耗时: 577.738 秒
[2025-09-12 10:42:04] ================================================================================
[2025-09-12 10:42:04] 开始计算自旋结构因子...
[2025-09-12 10:42:04] 初始化操作符缓存...
[2025-09-12 10:42:04] 预构建所有自旋相关操作符...
[2025-09-12 10:42:04] 开始计算自旋相关函数...
[2025-09-12 10:42:30] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 25.464s
[2025-09-12 10:43:02] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 32.257s
[2025-09-12 10:43:22] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 20.054s
[2025-09-12 10:43:42] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 20.054s
[2025-09-12 10:44:02] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 20.200s
[2025-09-12 10:44:22] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 20.083s
[2025-09-12 10:44:42] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 20.120s
[2025-09-12 10:45:03] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 20.163s
[2025-09-12 10:45:23] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 20.145s
[2025-09-12 10:45:43] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 20.176s
[2025-09-12 10:46:03] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 20.190s
[2025-09-12 10:46:23] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 20.084s
[2025-09-12 10:46:43] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 20.186s
[2025-09-12 10:47:03] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 20.039s
[2025-09-12 10:47:24] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 20.119s
[2025-09-12 10:47:44] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 20.179s
[2025-09-12 10:48:04] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 20.122s
[2025-09-12 10:48:24] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 20.070s
[2025-09-12 10:48:44] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 20.096s
[2025-09-12 10:49:04] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 20.115s
[2025-09-12 10:49:24] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 20.219s
[2025-09-12 10:49:45] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 20.019s
[2025-09-12 10:50:05] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 20.180s
[2025-09-12 10:50:25] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 20.175s
[2025-09-12 10:50:45] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 20.137s
[2025-09-12 10:51:05] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 20.090s
[2025-09-12 10:51:25] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 20.109s
[2025-09-12 10:51:45] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 20.083s
[2025-09-12 10:52:06] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 20.230s
[2025-09-12 10:52:26] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 20.062s
[2025-09-12 10:52:46] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 20.131s
[2025-09-12 10:53:06] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 20.124s
[2025-09-12 10:53:26] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 20.128s
[2025-09-12 10:53:46] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 20.144s
[2025-09-12 10:54:06] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 20.119s
[2025-09-12 10:54:27] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 20.109s
[2025-09-12 10:54:47] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 20.203s
[2025-09-12 10:55:07] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 20.130s
[2025-09-12 10:55:27] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 20.159s
[2025-09-12 10:55:47] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 20.179s
[2025-09-12 10:56:07] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 20.103s
[2025-09-12 10:56:27] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 20.040s
[2025-09-12 10:56:48] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 20.100s
[2025-09-12 10:57:08] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 20.100s
[2025-09-12 10:57:28] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 20.157s
[2025-09-12 10:57:48] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 20.078s
[2025-09-12 10:58:08] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 20.122s
[2025-09-12 10:58:28] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 20.177s
[2025-09-12 10:58:48] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 20.096s
[2025-09-12 10:59:09] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 20.110s
[2025-09-12 10:59:29] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 20.250s
[2025-09-12 10:59:49] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 20.203s
[2025-09-12 11:00:09] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 20.135s
[2025-09-12 11:00:29] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 20.071s
[2025-09-12 11:00:49] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 20.123s
[2025-09-12 11:01:10] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 20.208s
[2025-09-12 11:01:30] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 20.136s
[2025-09-12 11:01:50] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 20.079s
[2025-09-12 11:02:10] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 20.112s
[2025-09-12 11:02:30] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 20.099s
[2025-09-12 11:02:50] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 20.080s
[2025-09-12 11:03:10] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 20.143s
[2025-09-12 11:03:30] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 20.102s
[2025-09-12 11:03:51] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 20.195s
[2025-09-12 11:04:11] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 20.169s
[2025-09-12 11:04:31] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 20.173s
[2025-09-12 11:04:51] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 20.222s
[2025-09-12 11:05:11] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 20.137s
[2025-09-12 11:05:31] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 20.080s
[2025-09-12 11:05:52] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 20.135s
[2025-09-12 11:06:12] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 20.080s
[2025-09-12 11:06:32] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 20.137s
[2025-09-12 11:06:52] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 20.130s
[2025-09-12 11:07:12] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 20.030s
[2025-09-12 11:07:32] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 20.173s
[2025-09-12 11:07:52] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 20.136s
[2025-09-12 11:08:13] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 20.090s
[2025-09-12 11:08:33] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 20.141s
[2025-09-12 11:08:53] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 20.100s
[2025-09-12 11:09:13] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 20.039s
[2025-09-12 11:09:33] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 20.089s
[2025-09-12 11:09:53] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 20.089s
[2025-09-12 11:10:13] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 20.223s
[2025-09-12 11:10:33] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 20.060s
[2025-09-12 11:10:54] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 20.087s
[2025-09-12 11:11:14] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 20.178s
[2025-09-12 11:11:34] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 20.124s
[2025-09-12 11:11:54] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 20.018s
[2025-09-12 11:12:14] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 20.026s
[2025-09-12 11:12:34] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 20.138s
[2025-09-12 11:12:54] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 20.091s
[2025-09-12 11:13:14] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 20.138s
[2025-09-12 11:13:34] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 20.131s
[2025-09-12 11:13:55] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 20.264s
[2025-09-12 11:14:15] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 20.169s
[2025-09-12 11:14:35] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 20.079s
[2025-09-12 11:14:55] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 20.099s
[2025-09-12 11:15:15] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 20.038s
[2025-09-12 11:15:35] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 20.205s
[2025-09-12 11:15:56] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 20.123s
[2025-09-12 11:15:56] 自旋相关函数计算完成,总耗时 2031.54 秒
[2025-09-12 11:16:01] 计算傅里叶变换...
[2025-09-12 11:16:08] 自旋结构因子计算完成
[2025-09-12 11:16:10] 自旋相关函数平均误差: 0.000808
