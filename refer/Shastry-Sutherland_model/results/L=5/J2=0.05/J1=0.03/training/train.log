[2025-09-09 20:51:38] ✓ 从checkpoint恢复: results/L=5/J2=0.05/J1=0.04/training/checkpoints/final_GCNN.pkl
[2025-09-09 20:51:38]   - 迭代次数: final
[2025-09-09 20:51:39]   - 能量: -80.499382+0.003559j ± 0.111916
[2025-09-09 20:51:39]   - 时间戳: 2025-09-09T20:51:16.342192+08:00
[2025-09-09 20:52:07] ✓ 变分状态参数已从checkpoint恢复
[2025-09-09 20:52:07] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-09 20:52:07] ==================================================
[2025-09-09 20:52:07] GCNN for Shastry-Sutherland Model
[2025-09-09 20:52:08] ==================================================
[2025-09-09 20:52:08] System parameters:
[2025-09-09 20:52:08]   - System size: L=5, N=100
[2025-09-09 20:52:08]   - System parameters: J1=0.03, J2=0.05, Q=0.95
[2025-09-09 20:52:08] --------------------------------------------------
[2025-09-09 20:52:08] Model parameters:
[2025-09-09 20:52:08]   - Number of layers = 4
[2025-09-09 20:52:08]   - Number of features = 4
[2025-09-09 20:52:08]   - Total parameters = 19628
[2025-09-09 20:52:08] --------------------------------------------------
[2025-09-09 20:52:08] Training parameters:
[2025-09-09 20:52:08]   - Learning rate: 0.015
[2025-09-09 20:52:08]   - Total iterations: 1050
[2025-09-09 20:52:08]   - Annealing cycles: 3
[2025-09-09 20:52:08]   - Initial period: 150
[2025-09-09 20:52:08]   - Period multiplier: 2.0
[2025-09-09 20:52:08]   - Temperature range: 0.0-1.0
[2025-09-09 20:52:08]   - Samples: 4096
[2025-09-09 20:52:08]   - Discarded samples: 0
[2025-09-09 20:52:08]   - Chunk size: 2048
[2025-09-09 20:52:08]   - Diagonal shift: 0.2
[2025-09-09 20:52:08]   - Gradient clipping: 1.0
[2025-09-09 20:52:08]   - Checkpoint enabled: interval=105
[2025-09-09 20:52:08]   - Checkpoint directory: results/L=5/J2=0.05/J1=0.03/training/checkpoints
[2025-09-09 20:52:08] --------------------------------------------------
[2025-09-09 20:52:08] Device status:
[2025-09-09 20:52:08]   - Devices model: NVIDIA H200 NVL
[2025-09-09 20:52:08]   - Number of devices: 1
[2025-09-09 20:52:08]   - Sharding: True
[2025-09-09 20:52:08] ============================================================
[2025-09-09 20:54:34] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -80.047401+0.006507j
[2025-09-09 20:56:09] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -79.925835-0.002228j
[2025-09-09 20:56:44] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -79.947785+0.000966j
[2025-09-09 20:57:18] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -80.055254+0.005742j
[2025-09-09 20:57:53] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -79.998744+0.001411j
[2025-09-09 20:58:28] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -79.964041-0.000663j
[2025-09-09 20:59:02] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -79.939187+0.001147j
[2025-09-09 20:59:37] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -80.039236-0.005456j
[2025-09-09 21:00:12] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -80.088768-0.012423j
[2025-09-09 21:00:46] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -80.096604-0.003830j
[2025-09-09 21:01:21] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -79.990375-0.008912j
[2025-09-09 21:01:56] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -79.938799-0.009544j
[2025-09-09 21:02:30] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -79.925200-0.000189j
[2025-09-09 21:03:05] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -79.995156-0.004134j
[2025-09-09 21:03:40] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -80.027463-0.006695j
[2025-09-09 21:04:14] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -80.129131+0.001046j
[2025-09-09 21:04:49] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -80.064748-0.002821j
[2025-09-09 21:05:24] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -79.948364-0.003391j
[2025-09-09 21:05:59] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -79.859761-0.004077j
[2025-09-09 21:06:33] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -79.923921-0.000029j
[2025-09-09 21:07:08] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -80.094717-0.006596j
[2025-09-09 21:07:42] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -80.086863+0.000728j
[2025-09-09 21:08:17] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -79.991215-0.000087j
[2025-09-09 21:08:52] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -80.076709-0.001763j
[2025-09-09 21:09:27] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -79.926836+0.006323j
[2025-09-09 21:10:01] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -80.031065+0.001704j
[2025-09-09 21:10:36] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -80.072256-0.003198j
[2025-09-09 21:11:11] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -79.972221-0.001731j
[2025-09-09 21:11:45] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -80.054301+0.001248j
[2025-09-09 21:12:20] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -80.097769+0.005498j
[2025-09-09 21:12:55] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -80.057907+0.005190j
[2025-09-09 21:13:30] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -79.784159+0.002724j
[2025-09-09 21:14:05] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -79.968755+0.003687j
[2025-09-09 21:14:39] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -79.904490+0.000967j
[2025-09-09 21:15:14] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -80.004808+0.000082j
[2025-09-09 21:15:48] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -79.974189+0.006991j
[2025-09-09 21:16:23] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -80.047535+0.002056j
[2025-09-09 21:16:58] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -79.839840-0.005921j
[2025-09-09 21:17:32] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -79.952822+0.003838j
[2025-09-09 21:18:07] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -79.963625-0.004451j
[2025-09-09 21:18:42] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -80.087965+0.004321j
[2025-09-09 21:19:17] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -79.943317-0.002721j
[2025-09-09 21:19:51] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -79.862084-0.003958j
[2025-09-09 21:20:26] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -79.752129-0.001719j
[2025-09-09 21:21:01] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -79.917949-0.005654j
[2025-09-09 21:21:35] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -80.012681+0.009285j
[2025-09-09 21:22:10] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -80.008510-0.002045j
[2025-09-09 21:22:45] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -80.118663+0.001433j
[2025-09-09 21:23:20] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -79.995148-0.002658j
[2025-09-09 21:23:54] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -80.041459+0.002357j
[2025-09-09 21:24:29] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -79.925738+0.003582j
[2025-09-09 21:25:04] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -79.847910+0.001787j
[2025-09-09 21:25:39] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -79.956414+0.000788j
[2025-09-09 21:26:13] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -79.976963-0.000071j
[2025-09-09 21:26:48] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -79.831942+0.002964j
[2025-09-09 21:27:23] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -79.727871-0.008640j
[2025-09-09 21:27:57] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -79.975806+0.001611j
[2025-09-09 21:28:32] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -79.876296-0.001422j
[2025-09-09 21:29:07] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -79.967893-0.002817j
[2025-09-09 21:29:41] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -79.821147-0.000369j
[2025-09-09 21:30:16] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -79.979602-0.000266j
[2025-09-09 21:30:50] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -79.866867-0.005332j
[2025-09-09 21:31:25] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -80.012222-0.004497j
[2025-09-09 21:32:00] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -79.872096-0.000376j
[2025-09-09 21:32:34] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -79.885683-0.003315j
[2025-09-09 21:33:09] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -79.939674+0.003389j
[2025-09-09 21:33:44] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -79.912373+0.000026j
[2025-09-09 21:34:19] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -79.914329+0.002784j
[2025-09-09 21:34:53] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -79.987290+0.002747j
[2025-09-09 21:35:28] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -80.036509-0.000977j
[2025-09-09 21:36:03] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -79.868167-0.006980j
[2025-09-09 21:36:37] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -79.889145+0.004336j
[2025-09-09 21:37:12] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -79.845274-0.001203j
[2025-09-09 21:37:47] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -79.893415-0.002258j
[2025-09-09 21:38:22] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -79.925382-0.002897j
[2025-09-09 21:38:56] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -79.959106+0.002584j
[2025-09-09 21:39:31] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -79.792237-0.000745j
[2025-09-09 21:40:06] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -79.982608-0.001755j
[2025-09-09 21:40:41] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -80.039125-0.003357j
[2025-09-09 21:41:16] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -80.066829-0.003841j
[2025-09-09 21:41:50] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -79.951116+0.002275j
[2025-09-09 21:42:24] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -80.016298+0.000106j
[2025-09-09 21:42:59] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -79.944716-0.003302j
[2025-09-09 21:43:34] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -80.046266+0.000364j
[2025-09-09 21:44:09] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -79.963903-0.000666j
[2025-09-09 21:44:44] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -79.954884-0.000476j
[2025-09-09 21:45:18] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -79.875414-0.003750j
[2025-09-09 21:45:53] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -79.947777-0.005295j
[2025-09-09 21:46:28] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -79.936660+0.003672j
[2025-09-09 21:47:03] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -79.806242+0.003665j
[2025-09-09 21:47:37] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -79.741015+0.012716j
[2025-09-09 21:48:12] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -79.669405-0.000063j
[2025-09-09 21:48:46] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -79.771983+0.002718j
[2025-09-09 21:49:21] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -79.757793+0.003213j
[2025-09-09 21:49:56] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -79.861254+0.007628j
[2025-09-09 21:50:31] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -79.854420+0.000346j
[2025-09-09 21:51:05] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -79.886757+0.002810j
[2025-09-09 21:51:40] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -79.693432+0.000110j
[2025-09-09 21:52:15] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -79.788780-0.000158j
[2025-09-09 21:52:50] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -79.837587+0.003267j
[2025-09-09 21:53:24] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -79.699765+0.002516j
[2025-09-09 21:53:59] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -79.820464-0.005480j
[2025-09-09 21:54:34] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -79.783219+0.002753j
[2025-09-09 21:55:09] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -79.852027+0.003869j
[2025-09-09 21:55:44] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -79.796618-0.000847j
[2025-09-09 21:55:44] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-09 21:56:18] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -79.783873+0.002136j
[2025-09-09 21:56:53] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -79.702787+0.001838j
[2025-09-09 21:57:28] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -79.753139-0.001477j
[2025-09-09 21:58:03] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -79.767190-0.004335j
[2025-09-09 21:58:38] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -79.807464+0.002960j
[2025-09-09 21:59:12] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -79.769997+0.001552j
[2025-09-09 21:59:47] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -79.951423-0.002399j
[2025-09-09 22:00:22] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -79.863549-0.003725j
[2025-09-09 22:00:57] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -79.819227+0.002909j
[2025-09-09 22:01:32] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -79.904574-0.001617j
[2025-09-09 22:02:07] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -79.915516+0.001240j
[2025-09-09 22:02:41] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -79.870425+0.002844j
[2025-09-09 22:03:16] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -79.799055-0.001589j
[2025-09-09 22:03:51] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -79.800771+0.002314j
[2025-09-09 22:04:26] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -79.813348-0.004493j
[2025-09-09 22:05:01] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -79.917809+0.002279j
[2025-09-09 22:05:35] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -79.989102+0.000289j
[2025-09-09 22:06:10] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -79.988431-0.003496j
[2025-09-09 22:06:45] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -80.131121+0.005640j
[2025-09-09 22:07:20] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -79.932501-0.002968j
[2025-09-09 22:07:55] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -80.038675-0.001865j
[2025-09-09 22:08:29] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -80.014351+0.001983j
[2025-09-09 22:09:04] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -79.988224-0.000330j
[2025-09-09 22:09:39] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -79.914897+0.001567j
[2025-09-09 22:10:14] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -79.922413+0.003828j
[2025-09-09 22:10:48] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -80.011331+0.002247j
[2025-09-09 22:11:23] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -79.937458+0.002557j
[2025-09-09 22:11:58] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -79.926031+0.003493j
[2025-09-09 22:12:33] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -79.922500+0.006388j
[2025-09-09 22:13:08] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -79.805321+0.000246j
[2025-09-09 22:13:42] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -79.939285-0.004574j
[2025-09-09 22:14:17] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -79.989864-0.002305j
[2025-09-09 22:14:52] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -79.911108-0.001821j
[2025-09-09 22:15:27] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -79.894701-0.000103j
[2025-09-09 22:16:02] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -79.890753-0.004858j
[2025-09-09 22:16:36] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -79.856294+0.002706j
[2025-09-09 22:17:11] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -80.000979+0.005719j
[2025-09-09 22:17:46] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -79.986817-0.003371j
[2025-09-09 22:18:21] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -80.030016-0.001143j
[2025-09-09 22:18:55] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -79.929657+0.001703j
[2025-09-09 22:19:30] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -79.909792-0.000714j
[2025-09-09 22:20:05] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -79.822211+0.000612j
[2025-09-09 22:20:39] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -79.900340-0.002050j
[2025-09-09 22:21:14] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -79.923651+0.005316j
[2025-09-09 22:21:49] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -79.872703+0.000254j
[2025-09-09 22:21:49] RESTART #1 | Period: 300
[2025-09-09 22:22:24] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -79.920466+0.002862j
[2025-09-09 22:22:58] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -79.879333+0.000636j
[2025-09-09 22:23:33] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -79.873099-0.001911j
[2025-09-09 22:24:08] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -79.926137+0.001587j
[2025-09-09 22:24:43] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -80.012822+0.005263j
[2025-09-09 22:25:17] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -79.975917-0.000026j
[2025-09-09 22:25:52] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -79.893112+0.003167j
[2025-09-09 22:26:27] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -79.896277+0.003708j
[2025-09-09 22:27:02] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -79.866668+0.004450j
[2025-09-09 22:27:36] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -79.898713+0.000056j
[2025-09-09 22:28:11] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -79.935315+0.004240j
[2025-09-09 22:28:46] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -79.994199-0.003107j
[2025-09-09 22:29:21] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -79.902147+0.000734j
[2025-09-09 22:29:56] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -79.958839-0.003648j
[2025-09-09 22:30:31] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -80.143970-0.001032j
[2025-09-09 22:31:05] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -79.997143+0.004510j
[2025-09-09 22:31:40] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -79.847898+0.002767j
[2025-09-09 22:32:15] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -79.875812+0.004774j
[2025-09-09 22:32:50] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -80.012615-0.001153j
[2025-09-09 22:33:25] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -79.858689-0.000482j
[2025-09-09 22:34:00] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -79.943671+0.000467j
[2025-09-09 22:34:34] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -79.984100-0.002218j
[2025-09-09 22:35:09] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -80.076474+0.003471j
[2025-09-09 22:35:44] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -79.941987+0.003041j
[2025-09-09 22:36:19] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -79.908833-0.001542j
[2025-09-09 22:36:54] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -80.025198+0.002108j
[2025-09-09 22:37:28] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -79.867467+0.003698j
[2025-09-09 22:38:03] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -79.899666-0.000571j
[2025-09-09 22:38:38] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -79.797188+0.001429j
[2025-09-09 22:39:13] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -79.925370+0.003063j
[2025-09-09 22:39:48] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -80.010463+0.000540j
[2025-09-09 22:40:22] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -79.915346-0.001250j
[2025-09-09 22:40:57] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -79.918458-0.002659j
[2025-09-09 22:41:32] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -79.969095-0.006930j
[2025-09-09 22:42:07] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -79.997550+0.005537j
[2025-09-09 22:42:42] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -79.870293-0.001067j
[2025-09-09 22:43:17] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -79.913563-0.003544j
[2025-09-09 22:43:51] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -79.957818-0.004979j
[2025-09-09 22:44:26] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -79.930286+0.002883j
[2025-09-09 22:45:01] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -80.016913+0.002941j
[2025-09-09 22:45:36] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -79.917067+0.000373j
[2025-09-09 22:46:11] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -80.034542+0.001033j
[2025-09-09 22:46:45] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -79.937852-0.000761j
[2025-09-09 22:47:20] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -79.917776-0.004040j
[2025-09-09 22:47:55] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -80.012923-0.003700j
[2025-09-09 22:48:30] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -79.951312-0.003319j
[2025-09-09 22:49:05] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -79.964239-0.000095j
[2025-09-09 22:49:40] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -79.907396+0.001499j
[2025-09-09 22:50:14] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -79.943276+0.000630j
[2025-09-09 22:50:49] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -79.957709+0.002462j
[2025-09-09 22:51:24] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -80.001355-0.003365j
[2025-09-09 22:51:59] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -79.823934+0.003517j
[2025-09-09 22:52:34] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -79.820731-0.006383j
[2025-09-09 22:53:08] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -79.840184+0.000799j
[2025-09-09 22:53:43] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -79.819518+0.003707j
[2025-09-09 22:54:18] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -79.826283-0.000589j
[2025-09-09 22:54:53] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -79.951582-0.004258j
[2025-09-09 22:55:28] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -80.002131+0.003024j
[2025-09-09 22:56:03] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -80.080762+0.001365j
[2025-09-09 22:56:37] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -80.052740-0.000781j
[2025-09-09 22:56:37] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-09 22:57:12] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -80.058391+0.000191j
[2025-09-09 22:57:47] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -79.912900+0.003074j
[2025-09-09 22:58:22] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -79.825328-0.002431j
[2025-09-09 22:58:57] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -79.925909-0.004584j
[2025-09-09 22:59:31] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -80.079891+0.002945j
[2025-09-09 23:00:06] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -79.884850+0.002004j
[2025-09-09 23:00:41] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -80.065820+0.005454j
[2025-09-09 23:01:16] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -79.964104+0.000277j
[2025-09-09 23:01:51] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -80.042438+0.002129j
[2025-09-09 23:02:26] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -79.903306-0.000187j
[2025-09-09 23:03:00] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -79.822737+0.002461j
[2025-09-09 23:03:35] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -79.662804-0.001308j
[2025-09-09 23:04:10] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -79.805561+0.000805j
[2025-09-09 23:04:45] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -79.951754+0.001104j
[2025-09-09 23:05:20] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -79.866915+0.003774j
[2025-09-09 23:05:54] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -79.935935-0.002448j
[2025-09-09 23:06:29] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -80.000317-0.001317j
[2025-09-09 23:07:04] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -79.973460-0.003600j
[2025-09-09 23:07:39] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -80.014807+0.002703j
[2025-09-09 23:08:13] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -79.970371+0.002497j
[2025-09-09 23:08:48] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -79.930947+0.001389j
[2025-09-09 23:09:23] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -80.002055+0.005200j
[2025-09-09 23:09:58] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -79.989006-0.004353j
[2025-09-09 23:10:32] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -80.020672-0.004810j
[2025-09-09 23:11:07] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -79.960163-0.001606j
[2025-09-09 23:11:42] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -79.986239-0.002779j
[2025-09-09 23:12:17] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -79.880963-0.002817j
[2025-09-09 23:12:51] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -79.873655+0.003208j
[2025-09-09 23:13:26] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -79.811420-0.002308j
[2025-09-09 23:14:01] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -79.858340-0.001990j
[2025-09-09 23:14:36] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -79.712836+0.001061j
[2025-09-09 23:15:11] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -79.814320-0.003795j
[2025-09-09 23:15:45] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -79.774971-0.007340j
[2025-09-09 23:16:20] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -79.675071+0.002964j
[2025-09-09 23:16:55] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -79.823428+0.002052j
[2025-09-09 23:17:30] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -79.706967+0.000459j
[2025-09-09 23:18:04] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -79.734776+0.001276j
[2025-09-09 23:18:39] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -79.806251+0.002091j
[2025-09-09 23:19:14] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -79.788146+0.007594j
[2025-09-09 23:19:49] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -79.740543+0.003868j
[2025-09-09 23:20:23] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -79.856589-0.000840j
[2025-09-09 23:20:58] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -79.908660+0.000143j
[2025-09-09 23:21:33] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -79.848200+0.005188j
[2025-09-09 23:22:08] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -79.865579+0.004318j
[2025-09-09 23:22:43] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -79.838029+0.002274j
[2025-09-09 23:23:17] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -79.822771-0.003025j
[2025-09-09 23:23:52] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -80.082069-0.003967j
[2025-09-09 23:24:27] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -79.970547-0.003018j
[2025-09-09 23:25:02] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -80.095269+0.001812j
[2025-09-09 23:25:37] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -80.009376+0.001895j
[2025-09-09 23:26:12] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -80.017209+0.001155j
[2025-09-09 23:26:47] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -79.927226+0.004081j
[2025-09-09 23:27:21] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -79.977077-0.002744j
[2025-09-09 23:27:56] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -80.044582-0.001469j
[2025-09-09 23:28:31] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -79.936986-0.000510j
[2025-09-09 23:29:06] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -79.942050-0.001918j
[2025-09-09 23:29:41] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -79.860345-0.006870j
[2025-09-09 23:30:15] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -80.050489+0.003565j
[2025-09-09 23:30:50] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -79.945769+0.003540j
[2025-09-09 23:31:25] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -80.016241-0.001977j
[2025-09-09 23:32:00] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -79.994682+0.004312j
[2025-09-09 23:32:34] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -80.016846+0.001462j
[2025-09-09 23:33:09] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -79.992244+0.006574j
[2025-09-09 23:33:44] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -80.108590+0.001631j
[2025-09-09 23:34:19] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -80.027583-0.001776j
[2025-09-09 23:34:54] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -80.086020+0.003515j
[2025-09-09 23:35:29] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -79.992200-0.001757j
[2025-09-09 23:36:03] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -80.069027-0.000909j
[2025-09-09 23:36:38] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -79.986489+0.005554j
[2025-09-09 23:37:13] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -79.937608+0.002712j
[2025-09-09 23:37:48] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -80.023794+0.003700j
[2025-09-09 23:38:23] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -79.901397+0.016990j
[2025-09-09 23:38:57] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -79.855512-0.006858j
[2025-09-09 23:39:32] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -79.816265-0.001250j
[2025-09-09 23:40:07] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -79.893439-0.001608j
[2025-09-09 23:40:42] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -79.955165+0.003432j
[2025-09-09 23:41:17] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -79.963624-0.001287j
[2025-09-09 23:41:51] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -79.813123-0.004527j
[2025-09-09 23:42:26] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -79.825066-0.002086j
[2025-09-09 23:43:01] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -79.857350-0.000314j
[2025-09-09 23:43:36] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -79.962955-0.001692j
[2025-09-09 23:44:11] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -79.942477+0.002032j
[2025-09-09 23:44:45] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -79.894835+0.006351j
[2025-09-09 23:45:20] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -79.873255-0.003734j
[2025-09-09 23:45:55] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -79.971127+0.002197j
[2025-09-09 23:46:30] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -80.000824-0.000634j
[2025-09-09 23:47:05] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -79.867006-0.000635j
[2025-09-09 23:47:39] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -79.874598+0.005169j
[2025-09-09 23:48:14] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -79.810762+0.006027j
[2025-09-09 23:48:49] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -79.791882+0.002303j
[2025-09-09 23:49:24] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -79.823864+0.000912j
[2025-09-09 23:49:59] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -79.866628-0.003270j
[2025-09-09 23:50:33] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -79.794725+0.003346j
[2025-09-09 23:51:08] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -79.882311-0.003645j
[2025-09-09 23:51:43] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -79.972469-0.001083j
[2025-09-09 23:52:18] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -80.158143+0.002727j
[2025-09-09 23:52:53] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -80.131483+0.000997j
[2025-09-09 23:53:27] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -79.992940-0.000138j
[2025-09-09 23:54:02] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -80.126183-0.002157j
[2025-09-09 23:54:37] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -80.134979+0.002307j
[2025-09-09 23:55:12] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -80.143458+0.002552j
[2025-09-09 23:55:47] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -80.244440+0.006236j
[2025-09-09 23:56:21] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -80.061185+0.001150j
[2025-09-09 23:56:56] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -79.952677-0.002466j
[2025-09-09 23:57:31] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -79.999578-0.001626j
[2025-09-09 23:57:31] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-09 23:58:06] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -79.945557-0.000636j
[2025-09-09 23:58:41] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -79.936646+0.002057j
[2025-09-09 23:59:15] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -80.051331+0.000989j
[2025-09-09 23:59:50] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -79.865998-0.004301j
[2025-09-10 00:00:25] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -79.992403-0.006940j
[2025-09-10 00:01:00] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -79.913919-0.000297j
[2025-09-10 00:01:35] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -80.039370+0.000426j
[2025-09-10 00:02:10] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -80.039894+0.004109j
[2025-09-10 00:02:45] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -80.084386+0.000873j
[2025-09-10 00:03:19] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -80.077201+0.000328j
[2025-09-10 00:03:54] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -80.020698+0.002032j
[2025-09-10 00:04:29] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -80.103743-0.002628j
[2025-09-10 00:05:04] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -80.037967+0.003278j
[2025-09-10 00:05:39] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -80.003470+0.005012j
[2025-09-10 00:06:13] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -79.996630-0.002281j
[2025-09-10 00:06:50] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -80.127741+0.002618j
[2025-09-10 00:07:24] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -80.045710-0.000634j
[2025-09-10 00:07:58] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -80.046879-0.005153j
[2025-09-10 00:08:33] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -80.095921+0.005678j
[2025-09-10 00:09:08] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -80.230670-0.000828j
[2025-09-10 00:09:42] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -80.042181+0.007463j
[2025-09-10 00:10:17] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -80.167819+0.002272j
[2025-09-10 00:10:52] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -80.045660-0.004406j
[2025-09-10 00:11:26] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -80.053535+0.002103j
[2025-09-10 00:12:01] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -79.954835-0.000729j
[2025-09-10 00:12:36] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -79.913728+0.000805j
[2025-09-10 00:13:11] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -79.866843+0.004926j
[2025-09-10 00:13:45] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -79.808419-0.006755j
[2025-09-10 00:14:20] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -80.019034-0.000844j
[2025-09-10 00:14:55] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -79.859031-0.003027j
[2025-09-10 00:15:30] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -79.812699+0.003907j
[2025-09-10 00:16:04] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -79.875556-0.001033j
[2025-09-10 00:16:39] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -79.870188-0.000627j
[2025-09-10 00:17:14] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -80.038021+0.001233j
[2025-09-10 00:17:49] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -79.873090-0.003726j
[2025-09-10 00:18:24] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -79.836351-0.002713j
[2025-09-10 00:18:59] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -80.009631+0.000631j
[2025-09-10 00:19:33] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -79.977786-0.002956j
[2025-09-10 00:20:08] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -80.009501-0.002896j
[2025-09-10 00:20:43] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -80.054679+0.005022j
[2025-09-10 00:21:18] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -80.149516-0.000149j
[2025-09-10 00:21:53] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -80.110575+0.004143j
[2025-09-10 00:22:27] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -80.109634+0.002261j
[2025-09-10 00:23:02] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -79.922680+0.000605j
[2025-09-10 00:23:37] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -80.036037+0.002409j
[2025-09-10 00:24:12] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -80.182618-0.002850j
[2025-09-10 00:24:46] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -80.304575+0.000297j
[2025-09-10 00:25:21] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -80.138608+0.003211j
[2025-09-10 00:25:56] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -80.107183-0.001853j
[2025-09-10 00:26:31] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -79.974326+0.001050j
[2025-09-10 00:27:06] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -80.057333+0.003878j
[2025-09-10 00:27:40] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -80.076397+0.005641j
[2025-09-10 00:28:15] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -80.032920-0.006862j
[2025-09-10 00:28:50] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -79.978265+0.001479j
[2025-09-10 00:29:25] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -79.865875+0.000075j
[2025-09-10 00:30:00] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -79.931253+0.000688j
[2025-09-10 00:30:34] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -79.899919+0.002269j
[2025-09-10 00:31:09] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -79.862440+0.003651j
[2025-09-10 00:31:44] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -79.952925-0.003387j
[2025-09-10 00:32:19] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -79.856760-0.008294j
[2025-09-10 00:32:53] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -79.798522-0.005200j
[2025-09-10 00:33:28] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -79.876260+0.001779j
[2025-09-10 00:34:03] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -79.913514+0.005064j
[2025-09-10 00:34:38] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -79.929574-0.004922j
[2025-09-10 00:35:13] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -79.792284+0.002283j
[2025-09-10 00:35:47] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -79.762608+0.003911j
[2025-09-10 00:36:22] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -79.905165+0.002218j
[2025-09-10 00:36:57] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -79.986808+0.002051j
[2025-09-10 00:37:32] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -79.937950-0.000522j
[2025-09-10 00:38:07] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -79.906315-0.002912j
[2025-09-10 00:38:41] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -79.917465+0.004514j
[2025-09-10 00:39:16] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -79.913730+0.001341j
[2025-09-10 00:39:51] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -80.047080+0.001403j
[2025-09-10 00:40:26] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -80.038888+0.000039j
[2025-09-10 00:41:01] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -79.929061-0.003145j
[2025-09-10 00:41:36] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -79.943464-0.004727j
[2025-09-10 00:42:10] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -79.900904-0.004670j
[2025-09-10 00:42:45] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -79.900317+0.001017j
[2025-09-10 00:43:20] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -79.867936+0.001696j
[2025-09-10 00:43:54] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -79.849338-0.005767j
[2025-09-10 00:44:29] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -79.733701-0.004409j
[2025-09-10 00:45:03] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -79.793101-0.004382j
[2025-09-10 00:45:37] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -79.786892+0.002137j
[2025-09-10 00:46:13] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -79.826398-0.002151j
[2025-09-10 00:46:48] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -79.846252+0.001133j
[2025-09-10 00:47:22] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -79.742475+0.000599j
[2025-09-10 00:47:57] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -79.859315-0.001068j
[2025-09-10 00:48:32] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -79.920383+0.003496j
[2025-09-10 00:49:07] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -79.949345+0.002315j
[2025-09-10 00:49:42] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -79.862404-0.001927j
[2025-09-10 00:50:16] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -80.027732-0.002584j
[2025-09-10 00:50:51] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -80.060147-0.002438j
[2025-09-10 00:51:26] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -80.011924-0.004639j
[2025-09-10 00:52:01] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -80.137463+0.000876j
[2025-09-10 00:52:36] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -80.043487-0.000469j
[2025-09-10 00:53:10] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -80.009983+0.002951j
[2025-09-10 00:53:45] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -79.944579+0.003332j
[2025-09-10 00:54:20] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -79.979545+0.002200j
[2025-09-10 00:54:55] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -80.061807+0.002061j
[2025-09-10 00:55:30] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -79.939883+0.002184j
[2025-09-10 00:56:04] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -79.986254+0.001251j
[2025-09-10 00:56:39] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -80.176973-0.000544j
[2025-09-10 00:57:14] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -79.949449-0.000688j
[2025-09-10 00:57:49] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -80.024022+0.003582j
[2025-09-10 00:58:23] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -79.885011+0.002685j
[2025-09-10 00:58:23] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-10 00:58:58] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -80.036255+0.002170j
[2025-09-10 00:59:33] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -80.065874+0.000294j
[2025-09-10 01:00:08] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -79.911781-0.006329j
[2025-09-10 01:00:42] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -80.091319-0.005536j
[2025-09-10 01:01:17] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -80.070149+0.000474j
[2025-09-10 01:01:52] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -79.941924+0.002398j
[2025-09-10 01:02:27] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -79.931368+0.000561j
[2025-09-10 01:03:02] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -80.057487+0.011160j
[2025-09-10 01:03:37] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -79.966636-0.000595j
[2025-09-10 01:04:11] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -80.050979+0.001685j
[2025-09-10 01:04:46] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -80.137342+0.000304j
[2025-09-10 01:05:21] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -79.979414-0.000222j
[2025-09-10 01:05:56] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -80.040285-0.005177j
[2025-09-10 01:06:30] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -79.999582-0.004905j
[2025-09-10 01:07:05] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -79.875175-0.004123j
[2025-09-10 01:07:40] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -79.886807+0.002272j
[2025-09-10 01:08:15] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -79.853054+0.001252j
[2025-09-10 01:08:49] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -80.028254-0.002788j
[2025-09-10 01:09:24] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -79.972682+0.005117j
[2025-09-10 01:09:59] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -79.992119-0.004752j
[2025-09-10 01:10:34] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -79.996730+0.001068j
[2025-09-10 01:11:09] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -79.882724-0.000328j
[2025-09-10 01:11:44] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -80.049975-0.000522j
[2025-09-10 01:12:18] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -79.933669+0.005354j
[2025-09-10 01:12:53] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -79.850164+0.002059j
[2025-09-10 01:13:28] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -79.955528+0.001929j
[2025-09-10 01:14:03] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -79.903173+0.002183j
[2025-09-10 01:14:38] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -79.839696-0.004131j
[2025-09-10 01:15:12] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -79.886714-0.002001j
[2025-09-10 01:15:47] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -79.887961-0.002276j
[2025-09-10 01:15:47] RESTART #2 | Period: 600
[2025-09-10 01:16:22] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -79.870662-0.005033j
[2025-09-10 01:16:57] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -79.803355+0.001047j
[2025-09-10 01:17:31] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -79.672545+0.000745j
[2025-09-10 01:18:06] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -79.807894+0.000753j
[2025-09-10 01:18:41] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -79.861640+0.001343j
[2025-09-10 01:19:16] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -79.896219-0.004052j
[2025-09-10 01:19:51] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -79.930010+0.000748j
[2025-09-10 01:20:26] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -79.910794+0.004757j
[2025-09-10 01:21:00] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -80.031416-0.008750j
[2025-09-10 01:21:35] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -79.871265-0.002127j
[2025-09-10 01:22:10] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -79.996716+0.001142j
[2025-09-10 01:22:45] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -80.121446-0.003316j
[2025-09-10 01:23:19] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -79.995247-0.004004j
[2025-09-10 01:23:54] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -79.895962+0.001236j
[2025-09-10 01:24:29] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -80.007877-0.004493j
[2025-09-10 01:25:04] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -79.955565-0.004160j
[2025-09-10 01:25:39] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -79.999985-0.001036j
[2025-09-10 01:26:13] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -80.039291-0.003095j
[2025-09-10 01:26:48] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -79.837039-0.004594j
[2025-09-10 01:27:23] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -79.872423+0.000808j
[2025-09-10 01:27:58] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -79.866118+0.000010j
[2025-09-10 01:28:33] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -79.805257-0.002377j
[2025-09-10 01:29:07] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -79.883528-0.001205j
[2025-09-10 01:29:42] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -79.832399-0.001127j
[2025-09-10 01:30:17] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -79.985355-0.003397j
[2025-09-10 01:30:52] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -79.940293-0.036441j
[2025-09-10 01:31:26] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -79.916081-0.005636j
[2025-09-10 01:32:01] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -79.909761+0.002177j
[2025-09-10 01:32:36] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -79.788640+0.007598j
[2025-09-10 01:33:11] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -79.870847-0.002643j
[2025-09-10 01:33:46] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -79.976732-0.004277j
[2025-09-10 01:34:20] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -80.038040+0.003491j
[2025-09-10 01:34:55] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -79.954281-0.003488j
[2025-09-10 01:35:30] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -80.163789+0.002403j
[2025-09-10 01:36:05] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -80.038988-0.000159j
[2025-09-10 01:36:40] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -80.205242+0.000744j
[2025-09-10 01:37:15] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -80.101936-0.000200j
[2025-09-10 01:37:49] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -80.065644-0.002379j
[2025-09-10 01:38:24] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -80.012181-0.000046j
[2025-09-10 01:38:59] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -79.810498+0.001509j
[2025-09-10 01:39:34] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -79.882982-0.000328j
[2025-09-10 01:40:09] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -79.995922+0.000449j
[2025-09-10 01:40:43] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -80.133275-0.003943j
[2025-09-10 01:41:18] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -80.057594-0.000407j
[2025-09-10 01:41:53] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -80.012514+0.002490j
[2025-09-10 01:42:27] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -80.001918-0.000398j
[2025-09-10 01:43:02] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -79.877298+0.003018j
[2025-09-10 01:43:37] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -79.812376-0.007262j
[2025-09-10 01:44:12] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -79.988001+0.005859j
[2025-09-10 01:44:46] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -80.127663+0.000801j
[2025-09-10 01:45:21] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -80.045559+0.001494j
[2025-09-10 01:45:56] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -79.942681-0.002866j
[2025-09-10 01:46:31] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -79.867334-0.000696j
[2025-09-10 01:47:05] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -79.842918-0.000037j
[2025-09-10 01:47:40] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -79.723451-0.005439j
[2025-09-10 01:48:15] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -79.768145-0.001521j
[2025-09-10 01:48:50] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -79.725072+0.002001j
[2025-09-10 01:49:25] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -79.732048-0.002081j
[2025-09-10 01:50:00] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -79.701607-0.001289j
[2025-09-10 01:50:34] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -79.783649-0.006279j
[2025-09-10 01:51:09] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -79.700186-0.004689j
[2025-09-10 01:51:44] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -79.842222+0.001433j
[2025-09-10 01:52:19] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -79.778336-0.004389j
[2025-09-10 01:52:54] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -79.931002-0.004954j
[2025-09-10 01:53:28] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -79.784202+0.001533j
[2025-09-10 01:54:03] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -79.667251+0.002769j
[2025-09-10 01:54:38] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -79.857455-0.001930j
[2025-09-10 01:55:13] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -79.970749-0.003849j
[2025-09-10 01:55:48] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -79.991242-0.004718j
[2025-09-10 01:56:22] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -79.981877-0.005206j
[2025-09-10 01:56:57] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -79.857888-0.005232j
[2025-09-10 01:57:32] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -79.807650-0.006699j
[2025-09-10 01:58:07] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -79.900431+0.000725j
[2025-09-10 01:58:42] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -79.845102-0.002187j
[2025-09-10 01:59:16] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -79.956714+0.003347j
[2025-09-10 01:59:17] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-10 01:59:51] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -79.982982-0.002289j
[2025-09-10 02:00:26] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -79.889006-0.001799j
[2025-09-10 02:01:01] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -79.989527+0.000105j
[2025-09-10 02:01:36] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -79.991544+0.001754j
[2025-09-10 02:02:10] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -79.861674-0.003495j
[2025-09-10 02:02:45] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -79.884916+0.004354j
[2025-09-10 02:03:20] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -79.891972-0.005821j
[2025-09-10 02:03:55] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -79.956589-0.005248j
[2025-09-10 02:04:29] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -79.920019-0.000513j
[2025-09-10 02:05:04] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -80.073908-0.000828j
[2025-09-10 02:05:39] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -80.098207-0.000928j
[2025-09-10 02:06:14] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -79.989842+0.002759j
[2025-09-10 02:06:49] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -80.102245-0.002135j
[2025-09-10 02:07:24] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -79.974520+0.006771j
[2025-09-10 02:07:57] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -79.993438+0.001054j
[2025-09-10 02:08:32] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -79.954093-0.002449j
[2025-09-10 02:09:06] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -79.953593+0.007516j
[2025-09-10 02:09:42] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -79.824729-0.001142j
[2025-09-10 02:10:17] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -79.828514-0.001917j
[2025-09-10 02:10:51] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -79.816083+0.002222j
[2025-09-10 02:11:26] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -79.829120+0.001726j
[2025-09-10 02:12:01] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -79.883251-0.000161j
[2025-09-10 02:12:35] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -79.824005-0.000399j
[2025-09-10 02:13:10] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -79.834674+0.002543j
[2025-09-10 02:13:45] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -79.913923-0.004633j
[2025-09-10 02:14:20] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -80.007129-0.001741j
[2025-09-10 02:14:54] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -79.698323+0.001878j
[2025-09-10 02:15:29] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -79.787695+0.006323j
[2025-09-10 02:16:04] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -79.852040+0.003516j
[2025-09-10 02:16:38] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -79.884987+0.001742j
[2025-09-10 02:17:13] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -79.818885+0.004025j
[2025-09-10 02:17:48] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -79.697412-0.004224j
[2025-09-10 02:18:22] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -79.814846+0.000120j
[2025-09-10 02:18:57] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -79.752167+0.001505j
[2025-09-10 02:19:32] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -79.902031-0.000540j
[2025-09-10 02:20:06] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -79.904893-0.004012j
[2025-09-10 02:20:41] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -79.927342+0.002053j
[2025-09-10 02:21:16] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -79.894830-0.000364j
[2025-09-10 02:21:50] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -79.840546-0.003411j
[2025-09-10 02:22:25] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -79.906189+0.000179j
[2025-09-10 02:23:00] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -80.055060+0.000562j
[2025-09-10 02:23:34] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -79.910931+0.003004j
[2025-09-10 02:24:09] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -80.018457-0.000288j
[2025-09-10 02:24:44] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -79.918473-0.002854j
[2025-09-10 02:25:19] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -79.919941-0.005366j
[2025-09-10 02:25:53] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -80.046049+0.001621j
[2025-09-10 02:26:28] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -79.911808+0.000737j
[2025-09-10 02:27:03] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -79.813632-0.001731j
[2025-09-10 02:27:37] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -79.795383+0.001308j
[2025-09-10 02:28:12] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -79.913975+0.000777j
[2025-09-10 02:28:47] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -79.863360-0.005109j
[2025-09-10 02:29:22] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -79.895148-0.001913j
[2025-09-10 02:29:56] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -79.823256-0.002344j
[2025-09-10 02:30:31] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -79.749305+0.003278j
[2025-09-10 02:31:05] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -79.768704+0.003777j
[2025-09-10 02:31:40] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -79.768141+0.003192j
[2025-09-10 02:32:15] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -79.936718+0.004387j
[2025-09-10 02:32:50] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -79.974159-0.002899j
[2025-09-10 02:33:24] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -79.743668-0.000959j
[2025-09-10 02:33:59] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -79.861302+0.006014j
[2025-09-10 02:34:33] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -79.916643-0.000234j
[2025-09-10 02:35:08] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -79.851266-0.002708j
[2025-09-10 02:35:43] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -79.782233-0.001137j
[2025-09-10 02:36:17] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -79.817508-0.000724j
[2025-09-10 02:36:52] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -80.062232-0.012614j
[2025-09-10 02:37:27] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -80.018935+0.001896j
[2025-09-10 02:38:02] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -79.949125+0.001126j
[2025-09-10 02:38:36] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -79.837412+0.000547j
[2025-09-10 02:39:11] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -79.838991+0.002750j
[2025-09-10 02:39:46] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -79.903531-0.000869j
[2025-09-10 02:40:20] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -79.768250+0.001452j
[2025-09-10 02:40:55] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -79.703963-0.003479j
[2025-09-10 02:41:30] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -79.761676+0.000995j
[2025-09-10 02:42:04] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -79.757815-0.002978j
[2025-09-10 02:42:39] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -79.783423-0.003110j
[2025-09-10 02:43:14] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -79.787447+0.002356j
[2025-09-10 02:43:48] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -79.899479-0.001278j
[2025-09-10 02:44:23] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -79.793465+0.007521j
[2025-09-10 02:44:58] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -80.010404-0.002833j
[2025-09-10 02:45:32] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -80.020242-0.002868j
[2025-09-10 02:46:07] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -79.829936-0.000384j
[2025-09-10 02:46:42] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -79.754153+0.001549j
[2025-09-10 02:47:16] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -79.773699-0.002894j
[2025-09-10 02:47:51] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -79.734284-0.001385j
[2025-09-10 02:48:26] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -79.957341+0.003247j
[2025-09-10 02:49:01] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -79.910049+0.000932j
[2025-09-10 02:49:36] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -79.900236+0.000626j
[2025-09-10 02:50:10] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -79.894995+0.001018j
[2025-09-10 02:50:45] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -79.966228-0.003530j
[2025-09-10 02:51:20] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -79.851839-0.008313j
[2025-09-10 02:51:54] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -79.727102+0.000941j
[2025-09-10 02:52:29] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -79.902131+0.006953j
[2025-09-10 02:53:04] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -79.988263+0.006222j
[2025-09-10 02:53:39] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -79.931412+0.000627j
[2025-09-10 02:54:13] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -79.799529+0.002620j
[2025-09-10 02:54:48] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -79.871895+0.001862j
[2025-09-10 02:55:23] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -79.815742-0.000959j
[2025-09-10 02:55:58] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -79.722343+0.004249j
[2025-09-10 02:56:33] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -79.862967+0.000708j
[2025-09-10 02:57:07] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -79.847637+0.001141j
[2025-09-10 02:57:42] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -79.813404+0.004324j
[2025-09-10 02:58:17] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -79.743833-0.000145j
[2025-09-10 02:58:52] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -79.733362+0.008964j
[2025-09-10 02:59:27] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -79.732037+0.004518j
[2025-09-10 03:00:02] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -79.822593+0.006154j
[2025-09-10 03:00:02] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-10 03:00:36] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -79.958318-0.002194j
[2025-09-10 03:01:11] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -79.983527+0.003682j
[2025-09-10 03:01:46] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -79.803878+0.004862j
[2025-09-10 03:02:21] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -79.874117+0.001029j
[2025-09-10 03:02:56] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -79.843687-0.000398j
[2025-09-10 03:03:30] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -79.949215+0.001402j
[2025-09-10 03:04:05] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -79.936542-0.004202j
[2025-09-10 03:04:40] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -79.803121+0.000896j
[2025-09-10 03:05:15] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -79.894967-0.001474j
[2025-09-10 03:05:50] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -79.944924-0.005419j
[2025-09-10 03:06:25] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -79.895335-0.004429j
[2025-09-10 03:06:59] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -79.826979+0.002317j
[2025-09-10 03:07:34] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -79.847247+0.005123j
[2025-09-10 03:08:09] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -79.902631-0.000882j
[2025-09-10 03:08:44] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -79.769076-0.000540j
[2025-09-10 03:09:18] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -79.812651+0.000983j
[2025-09-10 03:09:53] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -79.761126-0.001720j
[2025-09-10 03:10:28] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -79.899254+0.002409j
[2025-09-10 03:11:03] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -80.081889-0.000403j
[2025-09-10 03:11:37] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -79.873353+0.000908j
[2025-09-10 03:12:12] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -79.936531+0.000976j
[2025-09-10 03:12:47] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -80.237577-0.000502j
[2025-09-10 03:13:22] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -80.103133-0.004032j
[2025-09-10 03:13:56] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -79.963966+0.001916j
[2025-09-10 03:14:31] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -79.981669+0.002898j
[2025-09-10 03:15:06] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -79.951983+0.003307j
[2025-09-10 03:15:41] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -79.988296-0.003494j
[2025-09-10 03:16:15] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -79.906972-0.002236j
[2025-09-10 03:16:50] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -79.893674-0.007543j
[2025-09-10 03:17:25] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -79.879628+0.001046j
[2025-09-10 03:18:00] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -80.021845+0.003719j
[2025-09-10 03:18:35] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -79.964122+0.001246j
[2025-09-10 03:19:09] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -80.107267+0.002230j
[2025-09-10 03:19:44] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -80.072683+0.004191j
[2025-09-10 03:20:19] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -80.094588+0.004687j
[2025-09-10 03:20:54] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -80.078646-0.001048j
[2025-09-10 03:21:29] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -80.007413-0.002477j
[2025-09-10 03:22:03] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -80.054901-0.007541j
[2025-09-10 03:22:38] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -79.994806-0.002317j
[2025-09-10 03:23:13] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -79.948121+0.006682j
[2025-09-10 03:23:48] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -79.979917-0.001980j
[2025-09-10 03:24:23] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -79.985306+0.002520j
[2025-09-10 03:24:57] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -79.881264+0.005256j
[2025-09-10 03:25:32] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -79.891760+0.003021j
[2025-09-10 03:26:07] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -79.931740-0.005913j
[2025-09-10 03:26:42] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -79.824704-0.005770j
[2025-09-10 03:27:17] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -79.694410+0.000854j
[2025-09-10 03:27:51] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -79.644751-0.000261j
[2025-09-10 03:28:26] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -79.739711-0.001885j
[2025-09-10 03:29:01] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -79.773984+0.001746j
[2025-09-10 03:29:36] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -79.862917-0.005367j
[2025-09-10 03:30:10] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -79.952313+0.003038j
[2025-09-10 03:30:45] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -79.899497+0.003776j
[2025-09-10 03:31:20] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -80.014226-0.008806j
[2025-09-10 03:31:54] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -79.744848-0.002382j
[2025-09-10 03:32:29] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -79.763507-0.000030j
[2025-09-10 03:33:04] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -79.889764-0.003088j
[2025-09-10 03:33:39] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -79.931362-0.001345j
[2025-09-10 03:34:13] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -80.053222+0.004069j
[2025-09-10 03:34:48] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -79.954571+0.003632j
[2025-09-10 03:35:23] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -80.040012+0.002371j
[2025-09-10 03:35:58] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -80.036162+0.004731j
[2025-09-10 03:36:32] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -80.024031+0.000437j
[2025-09-10 03:37:07] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -79.753649-0.001384j
[2025-09-10 03:37:42] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -79.824134+0.003439j
[2025-09-10 03:38:16] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -79.687192+0.001033j
[2025-09-10 03:38:51] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -79.740152+0.000087j
[2025-09-10 03:39:26] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -79.670514+0.000583j
[2025-09-10 03:40:01] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -79.864146+0.001086j
[2025-09-10 03:40:36] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -79.853773-0.002681j
[2025-09-10 03:41:10] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -79.826302-0.003377j
[2025-09-10 03:41:45] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -79.912032-0.002857j
[2025-09-10 03:42:20] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -79.865465+0.000007j
[2025-09-10 03:42:55] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -79.819218+0.004810j
[2025-09-10 03:43:30] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -79.939975-0.002316j
[2025-09-10 03:44:04] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -79.925993+0.005520j
[2025-09-10 03:44:39] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -79.926725-0.002132j
[2025-09-10 03:45:14] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -80.135275+0.009067j
[2025-09-10 03:45:49] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -80.127775+0.003035j
[2025-09-10 03:46:23] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -79.937175+0.000543j
[2025-09-10 03:46:58] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -79.845442-0.000740j
[2025-09-10 03:47:33] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -79.826054+0.001431j
[2025-09-10 03:48:08] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -79.948253+0.000772j
[2025-09-10 03:48:42] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -79.850437-0.003179j
[2025-09-10 03:49:17] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -79.819692+0.003016j
[2025-09-10 03:49:52] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -79.858325+0.000300j
[2025-09-10 03:50:27] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -79.871999+0.002022j
[2025-09-10 03:51:01] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -79.815237-0.003777j
[2025-09-10 03:51:36] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -79.945201+0.002353j
[2025-09-10 03:52:11] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -79.855558-0.003887j
[2025-09-10 03:52:46] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -79.917436+0.005811j
[2025-09-10 03:53:21] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -79.902278+0.006043j
[2025-09-10 03:53:55] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -79.824107+0.003039j
[2025-09-10 03:54:30] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -79.804445+0.006368j
[2025-09-10 03:55:05] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -79.866676+0.014232j
[2025-09-10 03:55:40] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -80.005428-0.003597j
[2025-09-10 03:56:15] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -80.032504+0.000314j
[2025-09-10 03:56:49] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -79.980558+0.000359j
[2025-09-10 03:57:24] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -80.066125+0.000138j
[2025-09-10 03:57:59] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -80.065639+0.001256j
[2025-09-10 03:58:34] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -79.931599-0.006029j
[2025-09-10 03:59:09] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -79.914878+0.001872j
[2025-09-10 03:59:43] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -79.832104-0.000537j
[2025-09-10 04:00:18] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -80.035047+0.002585j
[2025-09-10 04:00:53] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -80.043684-0.000773j
[2025-09-10 04:00:53] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-10 04:01:28] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -80.028729-0.002164j
[2025-09-10 04:02:03] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -79.865873-0.001630j
[2025-09-10 04:02:37] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -79.979553+0.001194j
[2025-09-10 04:03:12] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -80.079287-0.007391j
[2025-09-10 04:03:47] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -80.080721-0.004009j
[2025-09-10 04:04:22] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -80.015930+0.002015j
[2025-09-10 04:04:57] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -80.129742-0.000384j
[2025-09-10 04:05:31] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -79.858407-0.000511j
[2025-09-10 04:06:06] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -80.098067-0.002872j
[2025-09-10 04:06:41] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -79.854103+0.000798j
[2025-09-10 04:07:16] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -80.074793+0.003027j
[2025-09-10 04:07:51] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -80.020942+0.001719j
[2025-09-10 04:08:25] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -79.953695+0.005033j
[2025-09-10 04:09:00] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -79.964117-0.000781j
[2025-09-10 04:09:35] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -79.977645+0.002682j
[2025-09-10 04:10:10] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -80.046040+0.000281j
[2025-09-10 04:10:44] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -80.044382+0.003863j
[2025-09-10 04:11:19] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -80.090237-0.014520j
[2025-09-10 04:11:54] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -80.035330-0.006239j
[2025-09-10 04:12:29] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -80.086397-0.002503j
[2025-09-10 04:13:04] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -79.976198+0.000816j
[2025-09-10 04:13:38] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -79.963276-0.002518j
[2025-09-10 04:14:13] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -79.892972+0.002283j
[2025-09-10 04:14:48] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -79.893558+0.007040j
[2025-09-10 04:15:23] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -79.969231+0.001426j
[2025-09-10 04:15:58] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -80.004777-0.000519j
[2025-09-10 04:16:32] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -80.041337+0.002306j
[2025-09-10 04:17:07] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -79.849254-0.004420j
[2025-09-10 04:17:42] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -79.860659+0.006859j
[2025-09-10 04:18:17] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -79.763321+0.003328j
[2025-09-10 04:18:52] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -79.691669-0.000030j
[2025-09-10 04:19:26] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -79.724170-0.000972j
[2025-09-10 04:20:01] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -79.831334+0.001816j
[2025-09-10 04:20:36] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -79.955406+0.000328j
[2025-09-10 04:21:11] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -79.845941+0.005485j
[2025-09-10 04:21:46] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -79.849732-0.003188j
[2025-09-10 04:22:20] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -79.946703+0.002772j
[2025-09-10 04:22:55] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -80.005206-0.001202j
[2025-09-10 04:23:30] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -79.991505+0.000477j
[2025-09-10 04:24:05] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -80.042581+0.000981j
[2025-09-10 04:24:40] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -80.154276+0.003477j
[2025-09-10 04:25:15] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -80.112431-0.000179j
[2025-09-10 04:25:49] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -80.111701-0.001497j
[2025-09-10 04:26:24] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -80.146493-0.002850j
[2025-09-10 04:26:59] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -79.895639+0.000076j
[2025-09-10 04:27:34] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -80.054755-0.000733j
[2025-09-10 04:28:08] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -79.968962-0.005263j
[2025-09-10 04:28:43] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -79.850538-0.003409j
[2025-09-10 04:29:18] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -79.804147-0.000014j
[2025-09-10 04:29:53] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -79.706218+0.005445j
[2025-09-10 04:30:28] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -79.693096+0.002317j
[2025-09-10 04:31:02] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -79.865602+0.000302j
[2025-09-10 04:31:37] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -79.900146-0.000144j
[2025-09-10 04:32:12] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -79.984205-0.005486j
[2025-09-10 04:32:47] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -79.979398+0.002809j
[2025-09-10 04:33:22] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -79.840118-0.002016j
[2025-09-10 04:33:56] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -79.798965-0.005080j
[2025-09-10 04:34:31] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -79.788024-0.000288j
[2025-09-10 04:35:06] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -79.715318-0.002695j
[2025-09-10 04:35:41] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -79.825662+0.002346j
[2025-09-10 04:36:15] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -79.948381+0.000034j
[2025-09-10 04:36:50] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -79.898582-0.003666j
[2025-09-10 04:37:25] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -79.958267+0.002088j
[2025-09-10 04:38:00] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -79.899997-0.002572j
[2025-09-10 04:38:34] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -80.060038+0.001539j
[2025-09-10 04:39:09] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -79.900674+0.002953j
[2025-09-10 04:39:44] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -80.057800-0.000581j
[2025-09-10 04:40:19] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -79.970851-0.002783j
[2025-09-10 04:40:53] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -80.000047+0.001978j
[2025-09-10 04:41:28] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -79.900505-0.004877j
[2025-09-10 04:42:03] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -79.894093+0.007117j
[2025-09-10 04:42:38] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -79.940960-0.002711j
[2025-09-10 04:43:13] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -80.015555+0.000989j
[2025-09-10 04:43:47] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -79.940550+0.001609j
[2025-09-10 04:44:22] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -80.048810-0.000516j
[2025-09-10 04:44:57] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -79.960540-0.000260j
[2025-09-10 04:45:32] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -80.071516-0.002715j
[2025-09-10 04:46:07] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -80.052943+0.006375j
[2025-09-10 04:46:41] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -80.099784-0.001070j
[2025-09-10 04:47:16] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -80.026387-0.007155j
[2025-09-10 04:47:51] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -79.890025-0.001311j
[2025-09-10 04:48:26] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -79.957761-0.000588j
[2025-09-10 04:49:01] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -79.937629+0.003112j
[2025-09-10 04:49:35] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -79.993012-0.003747j
[2025-09-10 04:50:10] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -80.024503+0.002876j
[2025-09-10 04:50:45] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -79.949019-0.001501j
[2025-09-10 04:51:20] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -80.006330-0.002712j
[2025-09-10 04:51:55] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -79.911657+0.001126j
[2025-09-10 04:52:30] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -79.844827+0.000158j
[2025-09-10 04:53:04] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -79.850635-0.003488j
[2025-09-10 04:53:39] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -79.863931-0.001859j
[2025-09-10 04:54:14] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -79.885186+0.000163j
[2025-09-10 04:54:49] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -79.947605+0.001222j
[2025-09-10 04:55:24] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -79.819496-0.000437j
[2025-09-10 04:55:58] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -79.707125+0.002703j
[2025-09-10 04:56:33] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -79.915272-0.002296j
[2025-09-10 04:57:08] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -79.972075+0.005105j
[2025-09-10 04:57:43] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -79.991121+0.000166j
[2025-09-10 04:58:18] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -79.998913-0.001959j
[2025-09-10 04:58:52] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -80.012770-0.001139j
[2025-09-10 04:59:27] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -79.935298+0.000146j
[2025-09-10 05:00:02] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -79.910526-0.002412j
[2025-09-10 05:00:37] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -80.002690-0.000199j
[2025-09-10 05:01:11] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -80.013326-0.006270j
[2025-09-10 05:01:46] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -79.975759-0.000745j
[2025-09-10 05:01:46] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-10 05:02:21] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -79.992443+0.000461j
[2025-09-10 05:02:56] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -79.901400-0.000946j
[2025-09-10 05:03:30] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -79.870529+0.001345j
[2025-09-10 05:04:05] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -80.004308-0.000796j
[2025-09-10 05:04:40] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -79.931699+0.002653j
[2025-09-10 05:05:15] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -79.848363-0.003093j
[2025-09-10 05:05:50] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -79.887370-0.003722j
[2025-09-10 05:06:24] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -79.886514+0.002571j
[2025-09-10 05:06:59] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -79.928426-0.001382j
[2025-09-10 05:07:34] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -79.958581+0.005099j
[2025-09-10 05:08:09] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -79.935881+0.005358j
[2025-09-10 05:08:44] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -80.064052+0.002406j
[2025-09-10 05:09:18] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -80.024499-0.002192j
[2025-09-10 05:09:53] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -79.871899+0.002917j
[2025-09-10 05:10:28] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -79.861726-0.001509j
[2025-09-10 05:11:03] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -79.741480-0.004885j
[2025-09-10 05:11:38] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -79.667280-0.003155j
[2025-09-10 05:12:12] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -79.740425+0.001147j
[2025-09-10 05:12:47] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -79.833542-0.005768j
[2025-09-10 05:13:22] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -79.822676-0.000781j
[2025-09-10 05:13:57] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -79.922661+0.001627j
[2025-09-10 05:14:31] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -79.867220-0.000851j
[2025-09-10 05:15:06] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -79.894281+0.001172j
[2025-09-10 05:15:41] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -79.818589+0.000351j
[2025-09-10 05:16:16] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -79.790248+0.000842j
[2025-09-10 05:16:51] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -79.805015+0.000280j
[2025-09-10 05:17:25] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -79.840714+0.003124j
[2025-09-10 05:18:00] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -79.809532-0.001448j
[2025-09-10 05:18:35] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -79.829563-0.000373j
[2025-09-10 05:19:08] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -79.827811+0.000376j
[2025-09-10 05:19:42] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -79.939850+0.002131j
[2025-09-10 05:20:17] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -80.021352+0.006046j
[2025-09-10 05:20:52] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -79.986178+0.005663j
[2025-09-10 05:21:26] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -79.932405+0.000851j
[2025-09-10 05:22:01] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -79.926005+0.006549j
[2025-09-10 05:22:36] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -79.940949-0.006590j
[2025-09-10 05:23:11] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -79.979130+0.000955j
[2025-09-10 05:23:46] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -80.073686-0.001300j
[2025-09-10 05:24:20] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -80.068058+0.003443j
[2025-09-10 05:24:55] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -80.073551-0.000976j
[2025-09-10 05:25:30] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -80.015083+0.003271j
[2025-09-10 05:26:05] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -80.016953-0.000629j
[2025-09-10 05:26:40] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -80.077164+0.001807j
[2025-09-10 05:27:14] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -80.055967-0.000536j
[2025-09-10 05:27:49] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -79.989101+0.003430j
[2025-09-10 05:28:24] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -79.852043+0.003826j
[2025-09-10 05:28:59] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -79.822053+0.002850j
[2025-09-10 05:29:34] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -79.787502+0.004187j
[2025-09-10 05:30:08] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -79.921535-0.000061j
[2025-09-10 05:30:43] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -79.998765+0.001850j
[2025-09-10 05:31:18] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -80.124255-0.001154j
[2025-09-10 05:31:53] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -80.230536-0.001079j
[2025-09-10 05:32:27] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -80.116634+0.003482j
[2025-09-10 05:33:02] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -80.053805-0.002699j
[2025-09-10 05:33:37] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -80.147425-0.001335j
[2025-09-10 05:34:12] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -79.962702+0.000567j
[2025-09-10 05:34:47] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -80.001837+0.001766j
[2025-09-10 05:35:21] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -80.022031-0.002597j
[2025-09-10 05:35:56] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -79.944719-0.002030j
[2025-09-10 05:36:31] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -79.977260+0.001614j
[2025-09-10 05:37:06] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -80.074947+0.002484j
[2025-09-10 05:37:40] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -80.005073-0.004880j
[2025-09-10 05:38:15] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -80.036975+0.006168j
[2025-09-10 05:38:50] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -79.971078+0.005677j
[2025-09-10 05:39:25] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -79.966731-0.002454j
[2025-09-10 05:39:59] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -79.957937-0.004160j
[2025-09-10 05:40:34] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -79.882169-0.002750j
[2025-09-10 05:41:09] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -79.883972-0.001275j
[2025-09-10 05:41:44] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -79.857467+0.000104j
[2025-09-10 05:42:18] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -79.905763+0.005215j
[2025-09-10 05:42:53] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -79.894489+0.000983j
[2025-09-10 05:43:28] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -79.803168-0.003476j
[2025-09-10 05:44:03] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -79.888742-0.003267j
[2025-09-10 05:44:38] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -79.961517-0.000693j
[2025-09-10 05:45:12] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -79.795215+0.004165j
[2025-09-10 05:45:47] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -79.938470-0.002423j
[2025-09-10 05:46:22] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -79.952249+0.000112j
[2025-09-10 05:46:57] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -80.065527-0.000132j
[2025-09-10 05:47:32] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -80.007787-0.000057j
[2025-09-10 05:48:07] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -79.883886-0.004278j
[2025-09-10 05:48:41] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -79.887565+0.000866j
[2025-09-10 05:49:16] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -79.970555-0.002619j
[2025-09-10 05:49:51] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -79.983507+0.002685j
[2025-09-10 05:50:26] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -79.895389+0.003297j
[2025-09-10 05:51:01] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -80.094153-0.000223j
[2025-09-10 05:51:35] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -80.035035-0.003946j
[2025-09-10 05:52:10] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -80.059188-0.002454j
[2025-09-10 05:52:45] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -80.019183-0.002583j
[2025-09-10 05:53:20] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -79.906956-0.000394j
[2025-09-10 05:53:55] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -79.999628-0.003479j
[2025-09-10 05:54:30] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -79.914590-0.003021j
[2025-09-10 05:55:04] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -79.900793-0.004997j
[2025-09-10 05:55:39] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -79.986118-0.007129j
[2025-09-10 05:56:14] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -79.994552-0.002830j
[2025-09-10 05:56:49] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -79.940078-0.000898j
[2025-09-10 05:57:23] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -79.900811+0.003720j
[2025-09-10 05:57:58] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -79.840734-0.001173j
[2025-09-10 05:58:33] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -79.861263+0.002113j
[2025-09-10 05:59:08] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -80.028887-0.003891j
[2025-09-10 05:59:42] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -79.834021-0.002691j
[2025-09-10 06:00:17] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -79.847464-0.005304j
[2025-09-10 06:00:52] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -79.931720-0.001770j
[2025-09-10 06:01:27] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -79.908516-0.000013j
[2025-09-10 06:02:02] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -79.868146+0.005320j
[2025-09-10 06:02:36] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -80.000423+0.005240j
[2025-09-10 06:02:36] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-10 06:03:11] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -79.884278-0.004526j
[2025-09-10 06:03:46] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -79.788093+0.001708j
[2025-09-10 06:04:21] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -79.730328+0.000899j
[2025-09-10 06:04:56] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -79.858835+0.000800j
[2025-09-10 06:05:31] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -79.886218-0.001018j
[2025-09-10 06:06:05] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -79.904300+0.002962j
[2025-09-10 06:06:40] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -79.993361+0.001831j
[2025-09-10 06:07:15] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -79.943257-0.006726j
[2025-09-10 06:07:50] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -79.965202-0.000204j
[2025-09-10 06:08:25] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -80.021682-0.003221j
[2025-09-10 06:08:59] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -80.151727-0.001862j
[2025-09-10 06:09:34] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -80.031094-0.000250j
[2025-09-10 06:10:09] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -80.035168-0.004156j
[2025-09-10 06:10:44] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -80.011560-0.000641j
[2025-09-10 06:11:18] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -79.917676+0.005947j
[2025-09-10 06:11:53] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -79.990928-0.000418j
[2025-09-10 06:12:28] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -79.874082-0.002299j
[2025-09-10 06:13:03] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -79.825952+0.002280j
[2025-09-10 06:13:38] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -79.763617+0.000017j
[2025-09-10 06:14:12] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -79.846763+0.002170j
[2025-09-10 06:14:47] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -79.879910-0.002311j
[2025-09-10 06:15:22] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -80.003226+0.002203j
[2025-09-10 06:15:57] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -79.917343-0.002588j
[2025-09-10 06:16:32] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -79.957678-0.000109j
[2025-09-10 06:17:07] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -79.888956-0.001974j
[2025-09-10 06:17:41] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -79.963060-0.001971j
[2025-09-10 06:18:16] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -80.102234+0.003295j
[2025-09-10 06:18:51] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -79.971086+0.004900j
[2025-09-10 06:19:26] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -79.980496-0.007741j
[2025-09-10 06:20:01] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -79.921493-0.000879j
[2025-09-10 06:20:35] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -79.939165-0.001551j
[2025-09-10 06:21:10] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -79.864374-0.000416j
[2025-09-10 06:21:45] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -79.948088+0.000316j
[2025-09-10 06:22:20] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -80.009076-0.000146j
[2025-09-10 06:22:54] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -79.882480-0.007322j
[2025-09-10 06:23:29] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -79.831225-0.000213j
[2025-09-10 06:24:04] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -79.851085-0.002302j
[2025-09-10 06:24:39] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -79.931566-0.001539j
[2025-09-10 06:25:14] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -79.867667-0.007778j
[2025-09-10 06:25:49] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -79.815413-0.008282j
[2025-09-10 06:26:23] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -79.904364-0.000861j
[2025-09-10 06:26:58] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -79.851571-0.001754j
[2025-09-10 06:27:33] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -79.978651-0.002454j
[2025-09-10 06:28:08] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -79.962789-0.003335j
[2025-09-10 06:28:42] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -80.091959+0.003855j
[2025-09-10 06:29:17] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -79.979887-0.000656j
[2025-09-10 06:29:52] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -79.827198-0.001474j
[2025-09-10 06:30:27] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -79.872828+0.006436j
[2025-09-10 06:31:02] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -80.025327-0.000646j
[2025-09-10 06:31:36] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -79.931677+0.001205j
[2025-09-10 06:32:11] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -79.914970+0.001249j
[2025-09-10 06:32:46] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -79.850977+0.003398j
[2025-09-10 06:33:21] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -79.769187-0.000236j
[2025-09-10 06:33:55] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -79.798572-0.000540j
[2025-09-10 06:34:30] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -79.842451+0.005146j
[2025-09-10 06:35:05] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -79.992006+0.001661j
[2025-09-10 06:35:39] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -79.845612-0.001841j
[2025-09-10 06:36:14] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -79.850139+0.002651j
[2025-09-10 06:36:49] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -79.849619-0.004135j
[2025-09-10 06:37:24] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -80.037747+0.003005j
[2025-09-10 06:37:59] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -80.135986+0.001149j
[2025-09-10 06:38:33] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -79.902032+0.003994j
[2025-09-10 06:39:08] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -80.033222-0.006590j
[2025-09-10 06:39:43] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -79.790037+0.001715j
[2025-09-10 06:40:18] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -79.856800-0.000194j
[2025-09-10 06:40:53] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -79.949047-0.001237j
[2025-09-10 06:41:28] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -79.828916+0.005741j
[2025-09-10 06:42:02] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -79.845510-0.004872j
[2025-09-10 06:42:37] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -79.730243-0.001926j
[2025-09-10 06:43:12] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -79.879156-0.003116j
[2025-09-10 06:43:47] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -79.735732-0.003414j
[2025-09-10 06:44:21] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -79.819692-0.000603j
[2025-09-10 06:44:55] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -79.790368-0.002002j
[2025-09-10 06:45:31] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -79.762083+0.000729j
[2025-09-10 06:46:06] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -79.702341-0.000386j
[2025-09-10 06:46:41] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -79.890887+0.002058j
[2025-09-10 06:47:15] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -79.896017-0.003156j
[2025-09-10 06:47:50] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -79.808231+0.001448j
[2025-09-10 06:48:25] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -79.885288-0.001868j
[2025-09-10 06:49:00] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -79.944243-0.000141j
[2025-09-10 06:49:34] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -79.923895+0.000578j
[2025-09-10 06:50:09] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -79.772595-0.000734j
[2025-09-10 06:50:44] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -79.702756+0.002428j
[2025-09-10 06:51:19] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -79.826998+0.003779j
[2025-09-10 06:51:54] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -79.716967+0.002310j
[2025-09-10 06:52:28] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -79.841697+0.000106j
[2025-09-10 06:53:03] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -79.880068+0.003842j
[2025-09-10 06:53:38] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -79.997538-0.000244j
[2025-09-10 06:54:13] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -79.793009+0.000104j
[2025-09-10 06:54:48] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -80.116053+0.003992j
[2025-09-10 06:55:22] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -79.842509+0.002714j
[2025-09-10 06:55:57] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -79.784769-0.003547j
[2025-09-10 06:56:32] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -79.849022+0.001044j
[2025-09-10 06:57:07] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -79.927104-0.000635j
[2025-09-10 06:57:41] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -80.011459+0.008808j
[2025-09-10 06:58:16] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -79.948989+0.002736j
[2025-09-10 06:58:51] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -79.997324-0.002770j
[2025-09-10 06:59:26] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -80.063225-0.000735j
[2025-09-10 07:00:00] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -79.889945+0.002468j
[2025-09-10 07:00:35] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -80.009920-0.001583j
[2025-09-10 07:01:10] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -79.832602-0.006613j
[2025-09-10 07:01:45] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -79.776662-0.001606j
[2025-09-10 07:02:19] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -79.825420-0.003643j
[2025-09-10 07:02:54] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -79.652239+0.000109j
[2025-09-10 07:03:29] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -79.724329+0.001132j
[2025-09-10 07:03:29] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-10 07:03:29] ✅ Training completed | Restarts: 2
[2025-09-10 07:03:29] ============================================================
[2025-09-10 07:03:29] Training completed | Runtime: 36681.0s
[2025-09-10 07:03:43] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-10 07:03:43] ============================================================
