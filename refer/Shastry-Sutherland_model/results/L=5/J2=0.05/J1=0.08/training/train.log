[2025-09-10 07:04:53] ✓ 从checkpoint恢复: results/L=5/J2=0.05/J1=0.07/training/checkpoints/final_GCNN.pkl
[2025-09-10 07:04:53]   - 迭代次数: final
[2025-09-10 07:04:53]   - 能量: -82.477201-0.009097j ± 0.109976
[2025-09-10 07:04:53]   - 时间戳: 2025-09-10T07:04:31.052864+08:00
[2025-09-10 07:05:22] ✓ 变分状态参数已从checkpoint恢复
[2025-09-10 07:05:22] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-10 07:05:22] ==================================================
[2025-09-10 07:05:22] GCNN for Shastry-Sutherland Model
[2025-09-10 07:05:22] ==================================================
[2025-09-10 07:05:22] System parameters:
[2025-09-10 07:05:22]   - System size: L=5, N=100
[2025-09-10 07:05:22]   - System parameters: J1=0.08, J2=0.05, Q=0.95
[2025-09-10 07:05:22] --------------------------------------------------
[2025-09-10 07:05:22] Model parameters:
[2025-09-10 07:05:22]   - Number of layers = 4
[2025-09-10 07:05:22]   - Number of features = 4
[2025-09-10 07:05:22]   - Total parameters = 19628
[2025-09-10 07:05:22] --------------------------------------------------
[2025-09-10 07:05:22] Training parameters:
[2025-09-10 07:05:22]   - Learning rate: 0.015
[2025-09-10 07:05:22]   - Total iterations: 1050
[2025-09-10 07:05:22]   - Annealing cycles: 3
[2025-09-10 07:05:22]   - Initial period: 150
[2025-09-10 07:05:22]   - Period multiplier: 2.0
[2025-09-10 07:05:22]   - Temperature range: 0.0-1.0
[2025-09-10 07:05:22]   - Samples: 4096
[2025-09-10 07:05:22]   - Discarded samples: 0
[2025-09-10 07:05:22]   - Chunk size: 2048
[2025-09-10 07:05:22]   - Diagonal shift: 0.2
[2025-09-10 07:05:22]   - Gradient clipping: 1.0
[2025-09-10 07:05:22]   - Checkpoint enabled: interval=105
[2025-09-10 07:05:22]   - Checkpoint directory: results/L=5/J2=0.05/J1=0.08/training/checkpoints
[2025-09-10 07:05:22] --------------------------------------------------
[2025-09-10 07:05:22] Device status:
[2025-09-10 07:05:22]   - Devices model: NVIDIA H200 NVL
[2025-09-10 07:05:22]   - Number of devices: 1
[2025-09-10 07:05:22]   - Sharding: True
[2025-09-10 07:05:22] ============================================================
[2025-09-10 07:07:49] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -83.077919-0.012335j
[2025-09-10 07:09:25] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -83.099011-0.007747j
[2025-09-10 07:10:00] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -82.976079+0.003818j
[2025-09-10 07:10:35] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -83.186170-0.001495j
[2025-09-10 07:11:10] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -83.003027+0.002297j
[2025-09-10 07:11:46] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -83.021206+0.004963j
[2025-09-10 07:12:21] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -83.001556-0.010814j
[2025-09-10 07:12:56] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -83.101064+0.006232j
[2025-09-10 07:13:31] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -83.092572+0.003298j
[2025-09-10 07:14:06] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -82.989398+0.007310j
[2025-09-10 07:14:41] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -83.031683+0.002924j
[2025-09-10 07:15:16] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -83.164398-0.000811j
[2025-09-10 07:15:52] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -83.031251+0.005254j
[2025-09-10 07:16:27] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -82.943425-0.000371j
[2025-09-10 07:17:02] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -82.862001+0.003417j
[2025-09-10 07:17:37] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -82.879454+0.001264j
[2025-09-10 07:18:12] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -82.890778+0.002087j
[2025-09-10 07:18:47] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -82.888031-0.001544j
[2025-09-10 07:19:22] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -83.099897-0.000861j
[2025-09-10 07:19:58] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -83.072047-0.001441j
[2025-09-10 07:20:33] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -83.040876+0.002789j
[2025-09-10 07:21:08] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -82.906672+0.002994j
[2025-09-10 07:21:43] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -83.098090-0.000809j
[2025-09-10 07:22:18] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -83.030070-0.003884j
[2025-09-10 07:22:53] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -83.199277+0.002440j
[2025-09-10 07:23:28] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -82.937891-0.002019j
[2025-09-10 07:24:04] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -83.212142-0.000568j
[2025-09-10 07:24:39] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -83.205711-0.000007j
[2025-09-10 07:25:14] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -83.185349+0.002597j
[2025-09-10 07:25:49] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -83.041511+0.000283j
[2025-09-10 07:26:24] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -83.196580+0.001987j
[2025-09-10 07:26:59] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -83.115179-0.000214j
[2025-09-10 07:27:35] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -83.153144-0.001786j
[2025-09-10 07:28:10] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -83.030175+0.000346j
[2025-09-10 07:28:45] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -83.162880-0.001412j
[2025-09-10 07:29:20] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -82.980979+0.002702j
[2025-09-10 07:29:55] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -83.053844+0.024661j
[2025-09-10 07:30:30] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -83.128681+0.002015j
[2025-09-10 07:31:06] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -83.209286-0.002690j
[2025-09-10 07:31:41] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -83.136095+0.000067j
[2025-09-10 07:32:16] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -83.205504-0.000142j
[2025-09-10 07:32:51] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -83.175109+0.000012j
[2025-09-10 07:33:26] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -82.935640+0.002773j
[2025-09-10 07:34:01] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -83.059598+0.005131j
[2025-09-10 07:34:36] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -83.177303-0.001009j
[2025-09-10 07:35:11] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -82.987327+0.003008j
[2025-09-10 07:35:46] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -82.884656-0.001182j
[2025-09-10 07:36:22] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -82.870595-0.000960j
[2025-09-10 07:36:57] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -82.875931+0.000231j
[2025-09-10 07:37:32] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -83.095058+0.000483j
[2025-09-10 07:38:07] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -83.060818+0.004587j
[2025-09-10 07:38:42] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -83.119372+0.003842j
[2025-09-10 07:39:17] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -83.094533-0.003088j
[2025-09-10 07:39:52] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -82.975828-0.000026j
[2025-09-10 07:40:27] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -82.974579-0.000743j
[2025-09-10 07:41:02] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -82.852423+0.003153j
[2025-09-10 07:41:37] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -82.815192-0.001209j
[2025-09-10 07:42:13] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -82.966866-0.003285j
[2025-09-10 07:42:48] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -82.883683-0.000332j
[2025-09-10 07:43:23] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -83.006351+0.002538j
[2025-09-10 07:43:58] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -83.062312-0.000022j
[2025-09-10 07:44:33] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -83.130049-0.002818j
[2025-09-10 07:45:08] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -83.067913+0.002552j
[2025-09-10 07:45:43] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -83.147398-0.000612j
[2025-09-10 07:46:18] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -83.114032-0.000751j
[2025-09-10 07:46:54] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -82.974669+0.000047j
[2025-09-10 07:47:29] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -83.113989-0.002825j
[2025-09-10 07:48:04] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -83.044742+0.003933j
[2025-09-10 07:48:39] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -83.054013+0.000162j
[2025-09-10 07:49:14] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -82.939292+0.001533j
[2025-09-10 07:49:50] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -83.118045+0.001250j
[2025-09-10 07:50:25] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -82.990699+0.002221j
[2025-09-10 07:51:00] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -82.921414+0.000570j
[2025-09-10 07:51:35] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -82.927212-0.000910j
[2025-09-10 07:52:10] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -82.944895-0.001497j
[2025-09-10 07:52:46] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -82.893623+0.000851j
[2025-09-10 07:53:21] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -82.937324+0.000609j
[2025-09-10 07:53:56] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -83.150090-0.001018j
[2025-09-10 07:54:31] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -82.981533+0.000765j
[2025-09-10 07:55:06] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -83.047264-0.001984j
[2025-09-10 07:55:42] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -82.932622+0.000523j
[2025-09-10 07:56:17] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -83.016959+0.000852j
[2025-09-10 07:56:52] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -82.955712-0.000868j
[2025-09-10 07:57:27] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -83.113736-0.000099j
[2025-09-10 07:58:02] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -83.117631-0.000328j
[2025-09-10 07:58:38] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -82.952083-0.000299j
[2025-09-10 07:59:13] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -82.971932-0.003443j
[2025-09-10 07:59:48] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -83.077547-0.001541j
[2025-09-10 08:00:23] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -83.085193-0.000200j
[2025-09-10 08:00:58] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -82.966888-0.001193j
[2025-09-10 08:01:33] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -83.206788+0.000915j
[2025-09-10 08:02:09] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -83.081254+0.003087j
[2025-09-10 08:02:44] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -82.982926-0.001191j
[2025-09-10 08:03:19] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -83.100373+0.002976j
[2025-09-10 08:03:54] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -83.187788-0.001552j
[2025-09-10 08:04:29] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -83.062260-0.002213j
[2025-09-10 08:05:05] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -83.160295-0.003542j
[2025-09-10 08:05:40] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -83.093168-0.002818j
[2025-09-10 08:06:15] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -82.997508-0.002045j
[2025-09-10 08:06:50] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -82.977790+0.004447j
[2025-09-10 08:07:25] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -82.815986+0.001097j
[2025-09-10 08:08:00] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -83.023543-0.002089j
[2025-09-10 08:08:36] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -83.020800+0.002006j
[2025-09-10 08:09:11] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -82.918545-0.000219j
[2025-09-10 08:09:46] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -83.102361+0.000158j
[2025-09-10 08:09:46] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-10 08:10:21] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -82.984489+0.001631j
[2025-09-10 08:10:56] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -82.902751-0.003338j
[2025-09-10 08:11:31] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -82.957629-0.002381j
[2025-09-10 08:12:07] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -82.778442+0.001964j
[2025-09-10 08:12:42] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -82.940394-0.001011j
[2025-09-10 08:13:17] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -82.898410-0.001146j
[2025-09-10 08:13:52] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -82.887595+0.001187j
[2025-09-10 08:14:27] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -82.906711-0.001229j
[2025-09-10 08:15:03] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -83.039367+0.000500j
[2025-09-10 08:15:38] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -82.943114-0.003815j
[2025-09-10 08:16:13] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -83.070156-0.000669j
[2025-09-10 08:16:48] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -83.053199-0.001155j
[2025-09-10 08:17:23] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -83.103664+0.000054j
[2025-09-10 08:17:59] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -82.911448+0.006078j
[2025-09-10 08:18:34] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -83.054479+0.003823j
[2025-09-10 08:19:09] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -83.064660-0.001139j
[2025-09-10 08:19:44] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -82.971939+0.004107j
[2025-09-10 08:20:19] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -82.967878-0.001468j
[2025-09-10 08:20:55] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -82.887746-0.001823j
[2025-09-10 08:21:30] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -82.948202-0.000899j
[2025-09-10 08:22:05] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -82.910207+0.002085j
[2025-09-10 08:22:40] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -83.003261+0.000389j
[2025-09-10 08:23:15] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -82.933971+0.010985j
[2025-09-10 08:23:51] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -82.985949+0.003403j
[2025-09-10 08:24:26] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -83.072079-0.003256j
[2025-09-10 08:25:01] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -83.008124+0.001821j
[2025-09-10 08:25:36] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -82.910592+0.003677j
[2025-09-10 08:26:11] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -82.939220+0.002354j
[2025-09-10 08:26:46] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -83.027471+0.006358j
[2025-09-10 08:27:22] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -83.003889+0.001908j
[2025-09-10 08:27:57] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -82.928697-0.005038j
[2025-09-10 08:28:32] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -83.039826-0.001237j
[2025-09-10 08:29:07] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -82.975456-0.001168j
[2025-09-10 08:29:42] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -82.923753+0.000649j
[2025-09-10 08:30:18] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -83.036757-0.000041j
[2025-09-10 08:30:53] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -83.162221+0.001556j
[2025-09-10 08:31:28] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -83.175120-0.002384j
[2025-09-10 08:32:03] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -83.115652-0.000793j
[2025-09-10 08:32:38] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -82.991977+0.003065j
[2025-09-10 08:33:13] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -82.960776+0.003610j
[2025-09-10 08:33:48] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -83.179033+0.004254j
[2025-09-10 08:34:24] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -83.112782+0.002571j
[2025-09-10 08:34:59] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -83.076749-0.001043j
[2025-09-10 08:35:34] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -82.941641+0.002258j
[2025-09-10 08:36:09] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -82.960912-0.001092j
[2025-09-10 08:36:09] RESTART #1 | Period: 300
[2025-09-10 08:36:44] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -83.036042+0.000545j
[2025-09-10 08:37:20] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -83.160460+0.002139j
[2025-09-10 08:37:55] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -83.155362+0.000603j
[2025-09-10 08:38:30] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -83.025788-0.000210j
[2025-09-10 08:39:05] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -82.957953+0.002398j
[2025-09-10 08:39:40] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -82.958818-0.001737j
[2025-09-10 08:40:15] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -82.937024+0.001055j
[2025-09-10 08:40:50] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -82.840143+0.001512j
[2025-09-10 08:41:25] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -83.095213-0.001422j
[2025-09-10 08:42:01] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -82.961398-0.000586j
[2025-09-10 08:42:36] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -82.853123+0.001121j
[2025-09-10 08:43:11] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -82.877973-0.003503j
[2025-09-10 08:43:46] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -82.821868-0.001806j
[2025-09-10 08:44:21] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -82.880584-0.006871j
[2025-09-10 08:44:56] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -82.976794+0.004765j
[2025-09-10 08:45:31] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -82.828678-0.000071j
[2025-09-10 08:46:06] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -82.810058-0.000815j
[2025-09-10 08:46:41] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -82.802676-0.000649j
[2025-09-10 08:47:16] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -82.858951-0.021117j
[2025-09-10 08:47:52] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -82.907675+0.000492j
[2025-09-10 08:48:27] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -82.966107+0.001978j
[2025-09-10 08:49:02] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -82.990310+0.001266j
[2025-09-10 08:49:37] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -82.905774-0.002858j
[2025-09-10 08:50:12] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -82.807972+0.005686j
[2025-09-10 08:50:47] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -82.819925+0.002465j
[2025-09-10 08:51:22] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -82.940893-0.000436j
[2025-09-10 08:51:57] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -83.100323-0.003178j
[2025-09-10 08:52:33] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -82.961050+0.000233j
[2025-09-10 08:53:08] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -82.946070-0.003181j
[2025-09-10 08:53:43] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -83.139908+0.001027j
[2025-09-10 08:54:18] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -82.986237+0.001097j
[2025-09-10 08:54:53] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -83.037727+0.001530j
[2025-09-10 08:55:28] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -83.103868+0.001339j
[2025-09-10 08:56:03] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -83.054945+0.000407j
[2025-09-10 08:56:38] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -83.120983+0.003362j
[2025-09-10 08:57:13] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -83.112997-0.002473j
[2025-09-10 08:57:48] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -83.223440+0.002352j
[2025-09-10 08:58:24] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -83.193260-0.001671j
[2025-09-10 08:58:59] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -83.097894-0.000020j
[2025-09-10 08:59:34] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -83.120215-0.000200j
[2025-09-10 09:00:09] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -83.116238-0.003530j
[2025-09-10 09:00:44] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -83.165223+0.000198j
[2025-09-10 09:01:19] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -83.013553+0.001778j
[2025-09-10 09:01:54] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -83.094929-0.000408j
[2025-09-10 09:02:29] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -83.016248+0.002704j
[2025-09-10 09:03:04] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -82.817903+0.002178j
[2025-09-10 09:03:39] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -83.039988-0.004857j
[2025-09-10 09:04:14] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -83.105246-0.001880j
[2025-09-10 09:04:50] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -83.158771-0.000232j
[2025-09-10 09:05:25] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -83.098924-0.003811j
[2025-09-10 09:06:00] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -83.145022-0.001812j
[2025-09-10 09:06:35] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -83.062910+0.001396j
[2025-09-10 09:07:10] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -82.975957+0.000829j
[2025-09-10 09:07:46] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -83.084488+0.001171j
[2025-09-10 09:08:21] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -83.010064-0.001127j
[2025-09-10 09:08:56] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -83.005105+0.001302j
[2025-09-10 09:09:31] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -83.050659+0.000752j
[2025-09-10 09:10:06] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -83.073012-0.001355j
[2025-09-10 09:10:41] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -82.991897-0.000984j
[2025-09-10 09:11:17] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -82.918652+0.000712j
[2025-09-10 09:11:17] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-10 09:11:52] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -82.883869+0.000604j
[2025-09-10 09:12:27] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -82.961654+0.000264j
[2025-09-10 09:13:02] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -83.004189+0.000504j
[2025-09-10 09:13:37] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -82.853293-0.002805j
[2025-09-10 09:14:12] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -82.949307-0.000517j
[2025-09-10 09:14:47] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -82.949157+0.006148j
[2025-09-10 09:15:23] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -82.902583-0.000046j
[2025-09-10 09:15:58] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -82.940047+0.000171j
[2025-09-10 09:16:33] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -83.006187-0.001621j
[2025-09-10 09:17:08] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -83.126648+0.001417j
[2025-09-10 09:17:44] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -83.136269-0.002511j
[2025-09-10 09:18:19] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -83.034377+0.000980j
[2025-09-10 09:18:54] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -83.029683-0.004950j
[2025-09-10 09:19:29] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -83.169504+0.002196j
[2025-09-10 09:20:04] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -82.987890+0.000718j
[2025-09-10 09:20:39] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -82.911948+0.000907j
[2025-09-10 09:21:14] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -82.831824+0.000498j
[2025-09-10 09:21:49] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -82.907805-0.002481j
[2025-09-10 09:22:25] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -83.006549-0.000771j
[2025-09-10 09:23:00] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -83.106279+0.001044j
[2025-09-10 09:23:35] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -82.943202+0.000912j
[2025-09-10 09:24:10] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -82.929402+0.005269j
[2025-09-10 09:24:45] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -83.014453+0.001442j
[2025-09-10 09:25:21] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -83.015011+0.000147j
[2025-09-10 09:25:56] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -83.003437+0.002162j
[2025-09-10 09:26:31] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -83.092269-0.000364j
[2025-09-10 09:27:06] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -83.050049+0.000279j
[2025-09-10 09:27:42] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -83.043498+0.000752j
[2025-09-10 09:28:17] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -82.973212-0.004991j
[2025-09-10 09:28:52] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -82.968024+0.000495j
[2025-09-10 09:29:27] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -82.916013+0.003081j
[2025-09-10 09:30:02] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -82.826484+0.004408j
[2025-09-10 09:30:37] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -82.769125-0.001699j
[2025-09-10 09:31:13] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -82.870800+0.000444j
[2025-09-10 09:31:48] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -82.819149+0.000673j
[2025-09-10 09:32:23] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -82.810091+0.003191j
[2025-09-10 09:32:58] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -82.778804-0.003619j
[2025-09-10 09:33:33] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -82.914546-0.001559j
[2025-09-10 09:34:08] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -83.015390-0.002489j
[2025-09-10 09:34:43] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -82.982704+0.001267j
[2025-09-10 09:35:18] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -82.837313+0.003926j
[2025-09-10 09:35:53] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -82.992885-0.000972j
[2025-09-10 09:36:29] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -83.066377+0.000553j
[2025-09-10 09:37:04] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -83.096174-0.001328j
[2025-09-10 09:37:39] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -83.095729-0.003188j
[2025-09-10 09:38:14] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -82.960602+0.001585j
[2025-09-10 09:38:49] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -83.088807-0.000331j
[2025-09-10 09:39:24] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -83.021254+0.003430j
[2025-09-10 09:39:59] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -82.920011-0.000429j
[2025-09-10 09:40:34] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -82.881982-0.001261j
[2025-09-10 09:41:09] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -82.818023+0.004340j
[2025-09-10 09:41:44] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -83.045806-0.003109j
[2025-09-10 09:42:20] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -83.002060+0.000843j
[2025-09-10 09:42:55] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -83.017323-0.003983j
[2025-09-10 09:43:30] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -83.076803-0.001170j
[2025-09-10 09:44:05] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -83.189274+0.000798j
[2025-09-10 09:44:40] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -83.136814-0.002074j
[2025-09-10 09:45:15] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -83.019781-0.002401j
[2025-09-10 09:45:51] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -82.952179+0.001911j
[2025-09-10 09:46:26] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -82.931863+0.003719j
[2025-09-10 09:47:01] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -82.816655-0.001996j
[2025-09-10 09:47:36] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -82.867669+0.001289j
[2025-09-10 09:48:11] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -82.854964+0.001686j
[2025-09-10 09:48:47] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -82.765314-0.002342j
[2025-09-10 09:49:22] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -82.872685+0.001846j
[2025-09-10 09:49:57] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -82.861083-0.005017j
[2025-09-10 09:50:32] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -82.965839+0.001544j
[2025-09-10 09:51:07] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -82.898838+0.004577j
[2025-09-10 09:51:42] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -82.914302-0.000889j
[2025-09-10 09:52:18] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -82.895793+0.005527j
[2025-09-10 09:52:53] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -82.916300+0.000434j
[2025-09-10 09:53:28] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -82.986848+0.002564j
[2025-09-10 09:54:03] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -82.958400+0.004168j
[2025-09-10 09:54:38] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -83.085302+0.000542j
[2025-09-10 09:55:13] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -83.001815+0.000475j
[2025-09-10 09:55:49] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -83.135048+0.002726j
[2025-09-10 09:56:24] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -83.044997+0.001370j
[2025-09-10 09:56:59] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -83.161585+0.000541j
[2025-09-10 09:57:34] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -83.069089-0.001648j
[2025-09-10 09:58:09] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -83.034372+0.002268j
[2025-09-10 09:58:44] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -82.971790+0.001396j
[2025-09-10 09:59:19] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -83.060863+0.000042j
[2025-09-10 09:59:55] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -82.973508+0.004000j
[2025-09-10 10:00:30] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -83.060719+0.002688j
[2025-09-10 10:01:05] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -83.126993+0.003244j
[2025-09-10 10:01:40] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -83.054761+0.005540j
[2025-09-10 10:02:15] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -83.054459-0.001781j
[2025-09-10 10:02:50] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -83.109104-0.003456j
[2025-09-10 10:03:26] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -83.041835+0.003301j
[2025-09-10 10:04:01] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -82.862057-0.002111j
[2025-09-10 10:04:36] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -83.029379+0.000705j
[2025-09-10 10:05:11] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -83.098263+0.004372j
[2025-09-10 10:05:46] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -83.066143-0.000001j
[2025-09-10 10:06:21] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -82.933872-0.001395j
[2025-09-10 10:06:56] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -83.047315-0.001084j
[2025-09-10 10:07:31] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -83.007074-0.001025j
[2025-09-10 10:08:07] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -82.882615+0.000098j
[2025-09-10 10:08:42] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -82.942141-0.001272j
[2025-09-10 10:09:17] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -82.958958+0.001066j
[2025-09-10 10:09:52] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -83.082386-0.002939j
[2025-09-10 10:10:27] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -83.109028+0.001645j
[2025-09-10 10:11:02] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -82.871779+0.002097j
[2025-09-10 10:11:37] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -82.984647-0.002340j
[2025-09-10 10:12:12] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -83.059165+0.001049j
[2025-09-10 10:12:48] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -82.943309-0.000496j
[2025-09-10 10:12:48] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-10 10:13:23] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -83.069678+0.001858j
[2025-09-10 10:13:58] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -83.052103-0.001356j
[2025-09-10 10:14:33] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -82.961356-0.000580j
[2025-09-10 10:15:08] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -83.148450+0.000232j
[2025-09-10 10:15:43] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -83.133179+0.003093j
[2025-09-10 10:16:18] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -83.109137+0.002441j
[2025-09-10 10:16:54] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -82.979744+0.004328j
[2025-09-10 10:17:29] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -82.984527-0.001285j
[2025-09-10 10:18:04] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -82.872129+0.000213j
[2025-09-10 10:18:39] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -82.921641-0.000553j
[2025-09-10 10:19:14] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -82.911425-0.000093j
[2025-09-10 10:19:49] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -83.096173+0.000017j
[2025-09-10 10:20:24] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -83.070252-0.000283j
[2025-09-10 10:20:59] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -83.024221-0.003885j
[2025-09-10 10:21:34] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -83.150252-0.001874j
[2025-09-10 10:22:09] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -83.190033+0.003018j
[2025-09-10 10:22:45] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -83.137867-0.002102j
[2025-09-10 10:23:20] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -83.111151-0.005068j
[2025-09-10 10:23:55] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -83.174479+0.002596j
[2025-09-10 10:24:30] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -83.229210+0.000814j
[2025-09-10 10:25:05] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -83.127838-0.002537j
[2025-09-10 10:25:40] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -83.177898-0.000351j
[2025-09-10 10:26:15] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -83.266534+0.003414j
[2025-09-10 10:26:51] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -83.141774-0.001203j
[2025-09-10 10:27:26] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -83.140584-0.004662j
[2025-09-10 10:28:01] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -83.162896+0.002744j
[2025-09-10 10:28:36] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -83.175640-0.002882j
[2025-09-10 10:29:11] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -83.278936-0.001775j
[2025-09-10 10:29:46] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -83.074031-0.001044j
[2025-09-10 10:30:21] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -83.026355+0.000981j
[2025-09-10 10:30:56] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -82.951822-0.006664j
[2025-09-10 10:31:32] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -82.899054+0.000498j
[2025-09-10 10:32:07] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -82.955352+0.002187j
[2025-09-10 10:32:42] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -82.949882+0.004361j
[2025-09-10 10:33:17] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -83.039322-0.003181j
[2025-09-10 10:33:52] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -83.171492+0.000894j
[2025-09-10 10:34:27] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -83.128634+0.001893j
[2025-09-10 10:35:02] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -83.040474+0.002552j
[2025-09-10 10:35:37] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -82.907724+0.000125j
[2025-09-10 10:36:13] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -83.063187+0.001448j
[2025-09-10 10:36:48] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -83.087128+0.002731j
[2025-09-10 10:37:23] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -82.997717-0.002511j
[2025-09-10 10:37:58] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -83.195418-0.002595j
[2025-09-10 10:38:33] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -83.157183-0.004756j
[2025-09-10 10:39:08] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -83.101443+0.001252j
[2025-09-10 10:39:43] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -82.990792-0.000596j
[2025-09-10 10:40:18] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -83.050720+0.000712j
[2025-09-10 10:40:54] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -83.058865-0.003992j
[2025-09-10 10:41:29] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -82.985270+0.000915j
[2025-09-10 10:42:04] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -82.966878-0.002713j
[2025-09-10 10:42:39] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -82.792149-0.001065j
[2025-09-10 10:43:15] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -82.826357-0.001335j
[2025-09-10 10:43:50] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -82.806598-0.003394j
[2025-09-10 10:44:25] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -82.835261+0.002399j
[2025-09-10 10:45:00] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -82.856267+0.001852j
[2025-09-10 10:45:35] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -83.063757-0.002726j
[2025-09-10 10:46:10] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -83.008844-0.000115j
[2025-09-10 10:46:46] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -82.970327-0.002765j
[2025-09-10 10:47:21] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -83.002999-0.003940j
[2025-09-10 10:47:56] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -82.900957+0.002183j
[2025-09-10 10:48:31] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -83.025351-0.006714j
[2025-09-10 10:49:06] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -83.057651+0.000419j
[2025-09-10 10:49:42] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -82.917741+0.001043j
[2025-09-10 10:50:17] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -83.060792-0.002096j
[2025-09-10 10:50:52] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -83.098671+0.001972j
[2025-09-10 10:51:27] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -83.053131+0.001381j
[2025-09-10 10:52:02] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -83.008797+0.000293j
[2025-09-10 10:52:37] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -82.929105-0.000931j
[2025-09-10 10:53:12] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -82.853400-0.000153j
[2025-09-10 10:53:48] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -82.952034+0.004908j
[2025-09-10 10:54:23] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -83.056626+0.001507j
[2025-09-10 10:54:58] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -82.906351+0.002935j
[2025-09-10 10:55:33] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -83.030177+0.000422j
[2025-09-10 10:56:08] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -83.075821-0.001051j
[2025-09-10 10:56:43] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -83.033267+0.003624j
[2025-09-10 10:57:18] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -83.193084-0.001536j
[2025-09-10 10:57:54] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -83.034505-0.004867j
[2025-09-10 10:58:29] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -83.148220-0.000302j
[2025-09-10 10:59:04] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -83.040834+0.003425j
[2025-09-10 10:59:39] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -83.094098+0.003533j
[2025-09-10 11:00:14] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -82.972351+0.002920j
[2025-09-10 11:00:49] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -83.056905+0.002324j
[2025-09-10 11:01:25] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -82.921017-0.001005j
[2025-09-10 11:02:00] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -82.872549+0.000946j
[2025-09-10 11:02:35] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -82.792132-0.002367j
[2025-09-10 11:03:10] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -82.840357-0.005291j
[2025-09-10 11:03:46] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -82.930411+0.000616j
[2025-09-10 11:04:21] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -83.015433-0.000914j
[2025-09-10 11:04:56] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -83.086134-0.002249j
[2025-09-10 11:05:31] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -83.134116-0.006145j
[2025-09-10 11:06:06] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -83.147664-0.003830j
[2025-09-10 11:06:41] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -83.162069+0.003816j
[2025-09-10 11:07:17] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -83.065796+0.004577j
[2025-09-10 11:07:52] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -83.093939+0.004767j
[2025-09-10 11:08:27] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -83.087687-0.001056j
[2025-09-10 11:09:02] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -83.095511+0.000957j
[2025-09-10 11:09:37] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -83.054916-0.000585j
[2025-09-10 11:10:13] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -83.044374-0.002188j
[2025-09-10 11:10:48] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -83.045632+0.000715j
[2025-09-10 11:11:23] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -83.031390-0.001445j
[2025-09-10 11:11:58] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -83.015344+0.003605j
[2025-09-10 11:12:33] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -83.095611-0.003606j
[2025-09-10 11:13:09] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -82.939774+0.001198j
[2025-09-10 11:13:44] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -82.990690-0.001725j
[2025-09-10 11:14:19] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -83.038148+0.000458j
[2025-09-10 11:14:19] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-10 11:14:54] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -83.118054-0.001473j
[2025-09-10 11:15:29] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -83.250006+0.007139j
[2025-09-10 11:16:05] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -83.181340-0.001653j
[2025-09-10 11:16:40] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -82.915921+0.005025j
[2025-09-10 11:17:15] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -82.907559+0.003592j
[2025-09-10 11:17:50] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -83.149675+0.004652j
[2025-09-10 11:18:25] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -82.940525-0.000240j
[2025-09-10 11:19:00] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -82.937678+0.001845j
[2025-09-10 11:19:36] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -83.099354+0.001638j
[2025-09-10 11:20:11] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -83.107114-0.000346j
[2025-09-10 11:20:46] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -83.061086-0.001795j
[2025-09-10 11:21:21] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -82.880543-0.003586j
[2025-09-10 11:21:56] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -82.905640+0.001925j
[2025-09-10 11:22:31] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -82.846208+0.000716j
[2025-09-10 11:23:07] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -82.981702+0.004780j
[2025-09-10 11:23:42] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -83.136221-0.001419j
[2025-09-10 11:24:17] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -82.948085-0.001765j
[2025-09-10 11:24:52] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -82.957966-0.001778j
[2025-09-10 11:25:27] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -83.087432+0.001324j
[2025-09-10 11:26:02] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -83.112142-0.001003j
[2025-09-10 11:26:37] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -82.955258+0.000470j
[2025-09-10 11:27:12] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -83.037494+0.000646j
[2025-09-10 11:27:47] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -83.015254-0.003720j
[2025-09-10 11:28:23] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -83.018216+0.002093j
[2025-09-10 11:28:58] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -83.062526+0.004513j
[2025-09-10 11:29:33] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -82.867789+0.004284j
[2025-09-10 11:30:08] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -82.900464-0.000242j
[2025-09-10 11:30:43] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -82.840870-0.000326j
[2025-09-10 11:31:18] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -82.836513+0.001230j
[2025-09-10 11:31:54] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -82.894143+0.000775j
[2025-09-10 11:31:54] RESTART #2 | Period: 600
[2025-09-10 11:32:29] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -82.984954-0.002021j
[2025-09-10 11:33:04] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -83.014916-0.003234j
[2025-09-10 11:33:39] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -82.942267-0.001194j
[2025-09-10 11:34:14] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -83.003318-0.001447j
[2025-09-10 11:34:49] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -82.954365+0.000626j
[2025-09-10 11:35:24] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -83.006006-0.003557j
[2025-09-10 11:35:59] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -82.892359+0.002678j
[2025-09-10 11:36:34] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -83.091554-0.005268j
[2025-09-10 11:37:09] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -83.127432+0.000885j
[2025-09-10 11:37:44] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -83.076846-0.003051j
[2025-09-10 11:38:19] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -83.140338+0.003816j
[2025-09-10 11:38:55] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -83.100478+0.002778j
[2025-09-10 11:39:30] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -83.087802-0.000694j
[2025-09-10 11:40:05] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -83.154816-0.007718j
[2025-09-10 11:40:41] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -82.988482-0.000665j
[2025-09-10 11:41:16] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -82.901931+0.000823j
[2025-09-10 11:41:51] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -82.941973+0.000242j
[2025-09-10 11:42:26] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -82.954953-0.001167j
[2025-09-10 11:43:01] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -82.901719+0.000892j
[2025-09-10 11:43:36] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -83.009256-0.002788j
[2025-09-10 11:44:12] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -82.943750-0.005637j
[2025-09-10 11:44:47] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -83.030015-0.000148j
[2025-09-10 11:45:22] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -82.925644-0.001231j
[2025-09-10 11:45:57] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -83.082578-0.003222j
[2025-09-10 11:46:32] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -83.050658-0.002658j
[2025-09-10 11:47:07] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -82.956159+0.002073j
[2025-09-10 11:47:42] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -83.096191-0.003664j
[2025-09-10 11:48:17] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -83.003547+0.000538j
[2025-09-10 11:48:52] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -83.133520-0.002269j
[2025-09-10 11:49:27] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -83.105348-0.001274j
[2025-09-10 11:50:03] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -83.071827-0.001411j
[2025-09-10 11:50:38] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -82.918865+0.002925j
[2025-09-10 11:51:13] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -82.947652-0.000424j
[2025-09-10 11:51:48] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -82.974947-0.002690j
[2025-09-10 11:52:23] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -82.994575-0.001810j
[2025-09-10 11:52:58] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -83.002777-0.001298j
[2025-09-10 11:53:33] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -83.009275+0.001512j
[2025-09-10 11:54:08] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -83.026839-0.000005j
[2025-09-10 11:54:43] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -83.012624-0.003643j
[2025-09-10 11:55:18] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -83.061469+0.001233j
[2025-09-10 11:55:54] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -83.078749-0.001348j
[2025-09-10 11:56:29] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -82.815805+0.002895j
[2025-09-10 11:57:04] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -82.813175+0.027798j
[2025-09-10 11:57:39] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -82.992114-0.002900j
[2025-09-10 11:58:14] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -82.906508-0.000518j
[2025-09-10 11:58:50] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -82.959659+0.002147j
[2025-09-10 11:59:25] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -83.064389+0.001705j
[2025-09-10 12:00:00] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -83.043809-0.002987j
[2025-09-10 12:00:35] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -83.065794-0.000069j
[2025-09-10 12:01:10] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -83.212841-0.001534j
[2025-09-10 12:01:45] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -83.165350+0.000274j
[2025-09-10 12:02:20] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -83.101727-0.000579j
[2025-09-10 12:02:55] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -83.052805+0.002021j
[2025-09-10 12:03:30] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -83.098527-0.003156j
[2025-09-10 12:04:06] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -83.054936-0.001781j
[2025-09-10 12:04:41] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -82.994199-0.000951j
[2025-09-10 12:05:16] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -83.036635+0.003868j
[2025-09-10 12:05:51] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -82.980131-0.003781j
[2025-09-10 12:06:26] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -83.040770-0.002451j
[2025-09-10 12:07:01] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -83.017815+0.001620j
[2025-09-10 12:07:37] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -82.985936+0.001932j
[2025-09-10 12:08:12] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -82.830556+0.001926j
[2025-09-10 12:08:47] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -82.922535+0.000884j
[2025-09-10 12:09:22] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -82.980012+0.004667j
[2025-09-10 12:09:57] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -83.061800+0.002846j
[2025-09-10 12:10:32] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -83.030113-0.004188j
[2025-09-10 12:11:07] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -83.060356+0.000607j
[2025-09-10 12:11:42] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -82.993123+0.001794j
[2025-09-10 12:12:17] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -82.935001-0.003109j
[2025-09-10 12:12:52] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -82.919665-0.000247j
[2025-09-10 12:13:28] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -82.956485-0.001185j
[2025-09-10 12:14:03] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -83.062627+0.001274j
[2025-09-10 12:14:38] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -83.084106-0.002089j
[2025-09-10 12:15:13] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -83.249730-0.000466j
[2025-09-10 12:15:48] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -82.957680+0.001327j
[2025-09-10 12:15:48] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-10 12:16:24] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -83.056032-0.000499j
[2025-09-10 12:16:59] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -82.917648+0.000836j
[2025-09-10 12:17:34] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -82.946823+0.000731j
[2025-09-10 12:18:09] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -82.966971-0.002105j
[2025-09-10 12:18:44] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -83.057569+0.003379j
[2025-09-10 12:19:20] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -82.988849+0.001933j
[2025-09-10 12:19:55] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -82.957401-0.001882j
[2025-09-10 12:20:30] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -82.881612-0.004033j
[2025-09-10 12:21:05] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -82.951912+0.003187j
[2025-09-10 12:21:40] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -82.907630+0.003878j
[2025-09-10 12:22:15] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -82.891797-0.001927j
[2025-09-10 12:22:50] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -82.959674+0.002082j
[2025-09-10 12:23:26] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -82.946163+0.002908j
[2025-09-10 12:24:01] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -82.893858-0.000541j
[2025-09-10 12:24:36] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -82.983549+0.003546j
[2025-09-10 12:25:11] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -82.914157+0.004488j
[2025-09-10 12:25:46] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -82.901804+0.006327j
[2025-09-10 12:26:21] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -82.921079+0.000891j
[2025-09-10 12:26:56] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -82.857395+0.003172j
[2025-09-10 12:27:32] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -82.912094+0.003074j
[2025-09-10 12:28:07] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -82.986640-0.000203j
[2025-09-10 12:28:42] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -82.971243-0.000175j
[2025-09-10 12:29:17] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -82.950572+0.002260j
[2025-09-10 12:29:52] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -83.065354+0.003243j
[2025-09-10 12:30:27] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -83.108908+0.001859j
[2025-09-10 12:31:02] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -83.076998+0.004482j
[2025-09-10 12:31:37] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -83.053735-0.001830j
[2025-09-10 12:32:13] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -83.080029-0.001028j
[2025-09-10 12:32:48] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -83.041986-0.005176j
[2025-09-10 12:33:23] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -83.053396+0.000169j
[2025-09-10 12:33:58] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -83.140431+0.002538j
[2025-09-10 12:34:33] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -82.890487+0.000453j
[2025-09-10 12:35:09] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -82.957575+0.000382j
[2025-09-10 12:35:44] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -82.968315+0.002475j
[2025-09-10 12:36:19] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -82.997240+0.002999j
[2025-09-10 12:36:54] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -83.061120+0.001979j
[2025-09-10 12:37:29] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -83.021178+0.001484j
[2025-09-10 12:38:05] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -83.025432+0.001081j
[2025-09-10 12:38:40] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -82.905038-0.000401j
[2025-09-10 12:39:15] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -82.828023+0.000989j
[2025-09-10 12:39:50] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -82.775707-0.001010j
[2025-09-10 12:40:25] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -82.999240+0.001975j
[2025-09-10 12:41:01] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -82.876347+0.002457j
[2025-09-10 12:41:36] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -83.094815-0.003056j
[2025-09-10 12:42:11] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -83.058305+0.000706j
[2025-09-10 12:42:46] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -83.033585-0.003036j
[2025-09-10 12:43:21] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -82.905752-0.001483j
[2025-09-10 12:43:57] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -82.959396-0.002301j
[2025-09-10 12:44:32] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -82.956444-0.001096j
[2025-09-10 12:45:07] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -82.985677+0.000821j
[2025-09-10 12:45:42] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -83.056102-0.003694j
[2025-09-10 12:46:17] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -83.153100+0.000235j
[2025-09-10 12:46:52] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -83.118169-0.000772j
[2025-09-10 12:47:28] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -83.087735-0.001844j
[2025-09-10 12:48:03] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -83.172046+0.001516j
[2025-09-10 12:48:38] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -83.195570-0.001419j
[2025-09-10 12:49:13] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -83.178399-0.002856j
[2025-09-10 12:49:48] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -82.960734-0.002429j
[2025-09-10 12:50:24] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -82.942490-0.003825j
[2025-09-10 12:50:59] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -83.022411-0.001616j
[2025-09-10 12:51:34] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -82.980263+0.001893j
[2025-09-10 12:52:09] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -82.964495+0.002392j
[2025-09-10 12:52:45] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -82.992585+0.001790j
[2025-09-10 12:53:20] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -83.013713-0.000928j
[2025-09-10 12:53:55] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -82.978957+0.003652j
[2025-09-10 12:54:30] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -82.977327+0.001776j
[2025-09-10 12:55:05] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -83.012007+0.002455j
[2025-09-10 12:55:41] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -83.075401+0.003259j
[2025-09-10 12:56:16] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -82.956692+0.002133j
[2025-09-10 12:56:51] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -82.831201+0.002713j
[2025-09-10 12:57:27] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -82.897441+0.000095j
[2025-09-10 12:58:02] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -82.922674+0.006849j
[2025-09-10 12:58:37] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -82.889405+0.000846j
[2025-09-10 12:59:12] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -83.010106+0.000485j
[2025-09-10 12:59:47] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -83.065262+0.001996j
[2025-09-10 13:00:22] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -83.127120+0.000618j
[2025-09-10 13:00:58] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -83.072884-0.000192j
[2025-09-10 13:01:33] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -83.071340-0.000990j
[2025-09-10 13:02:08] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -83.048908+0.002373j
[2025-09-10 13:02:43] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -82.835416+0.002912j
[2025-09-10 13:03:18] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -82.863543+0.002966j
[2025-09-10 13:03:53] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -82.885325-0.003589j
[2025-09-10 13:04:28] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -83.018945-0.000157j
[2025-09-10 13:05:04] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -83.041001+0.001123j
[2025-09-10 13:05:39] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -82.975055-0.000657j
[2025-09-10 13:06:14] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -82.901977-0.003860j
[2025-09-10 13:06:49] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -82.876754-0.000860j
[2025-09-10 13:07:24] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -82.917595-0.001819j
[2025-09-10 13:07:59] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -82.995735+0.003113j
[2025-09-10 13:08:35] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -82.987483+0.002485j
[2025-09-10 13:09:10] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -83.148794+0.001029j
[2025-09-10 13:09:45] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -83.090719+0.001837j
[2025-09-10 13:10:20] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -83.011670+0.002396j
[2025-09-10 13:10:55] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -83.108621+0.005846j
[2025-09-10 13:11:31] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -83.187581+0.003599j
[2025-09-10 13:12:06] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -83.033540-0.002348j
[2025-09-10 13:12:41] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -83.079344-0.002089j
[2025-09-10 13:13:16] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -83.104352+0.002103j
[2025-09-10 13:13:51] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -83.209675-0.000657j
[2025-09-10 13:14:26] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -83.165191-0.004384j
[2025-09-10 13:15:01] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -82.867099-0.002101j
[2025-09-10 13:15:37] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -83.016258+0.001662j
[2025-09-10 13:16:12] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -82.925790-0.005359j
[2025-09-10 13:16:47] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -83.047899-0.004937j
[2025-09-10 13:17:22] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -82.975304+0.001844j
[2025-09-10 13:17:22] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-10 13:17:57] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -83.002895+0.002861j
[2025-09-10 13:18:33] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -83.166772-0.004542j
[2025-09-10 13:19:08] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -83.087978+0.000585j
[2025-09-10 13:19:43] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -83.064975-0.003445j
[2025-09-10 13:20:18] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -83.135320+0.002832j
[2025-09-10 13:20:53] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -83.060861-0.000897j
[2025-09-10 13:21:29] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -83.047311-0.000994j
[2025-09-10 13:22:04] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -82.940436+0.002242j
[2025-09-10 13:22:39] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -82.955024-0.001038j
[2025-09-10 13:23:14] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -82.954861+0.000957j
[2025-09-10 13:23:49] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -82.893573+0.006078j
[2025-09-10 13:24:24] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -82.803728-0.001021j
[2025-09-10 13:25:00] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -82.748113+0.000303j
[2025-09-10 13:25:35] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -82.896298-0.000852j
[2025-09-10 13:26:10] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -83.021579-0.003698j
[2025-09-10 13:26:45] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -82.985215+0.004883j
[2025-09-10 13:27:20] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -83.061234+0.001521j
[2025-09-10 13:27:56] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -82.993249-0.000384j
[2025-09-10 13:28:31] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -82.907436-0.002365j
[2025-09-10 13:29:06] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -82.889917+0.002635j
[2025-09-10 13:29:41] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -82.850257+0.002388j
[2025-09-10 13:30:16] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -83.052022-0.001717j
[2025-09-10 13:30:51] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -83.006044-0.001592j
[2025-09-10 13:31:27] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -83.123564-0.000651j
[2025-09-10 13:32:02] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -83.019365+0.004374j
[2025-09-10 13:32:37] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -83.028001+0.000802j
[2025-09-10 13:33:12] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -83.132519+0.001034j
[2025-09-10 13:33:48] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -83.130031+0.002281j
[2025-09-10 13:34:23] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -82.973260-0.001067j
[2025-09-10 13:34:58] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -83.154018-0.004049j
[2025-09-10 13:35:33] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -83.165161+0.002225j
[2025-09-10 13:36:09] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -82.947429-0.001402j
[2025-09-10 13:36:44] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -82.951050+0.002533j
[2025-09-10 13:37:19] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -82.909802+0.004849j
[2025-09-10 13:37:54] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -82.933508-0.003462j
[2025-09-10 13:38:29] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -82.876459+0.002708j
[2025-09-10 13:39:04] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -82.961204-0.003178j
[2025-09-10 13:39:40] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -82.993328-0.001434j
[2025-09-10 13:40:15] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -83.030720+0.001934j
[2025-09-10 13:40:50] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -82.965900-0.001095j
[2025-09-10 13:41:25] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -83.027252+0.000362j
[2025-09-10 13:42:01] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -82.983348+0.000053j
[2025-09-10 13:42:36] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -82.956877-0.000869j
[2025-09-10 13:43:11] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -82.928140-0.002208j
[2025-09-10 13:43:46] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -82.910467-0.002282j
[2025-09-10 13:44:21] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -82.922118+0.000709j
[2025-09-10 13:44:56] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -82.832326+0.002577j
[2025-09-10 13:45:32] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -82.971339-0.000452j
[2025-09-10 13:46:07] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -82.906194-0.003293j
[2025-09-10 13:46:42] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -83.034453-0.000026j
[2025-09-10 13:47:17] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -83.143522-0.002598j
[2025-09-10 13:47:52] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -83.192177+0.001378j
[2025-09-10 13:48:27] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -83.072454+0.001013j
[2025-09-10 13:49:03] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -83.106673+0.000521j
[2025-09-10 13:49:38] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -83.129277-0.001079j
[2025-09-10 13:50:13] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -83.018779-0.003122j
[2025-09-10 13:50:48] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -83.017114+0.003003j
[2025-09-10 13:51:23] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -83.149727-0.003152j
[2025-09-10 13:51:59] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -83.169750-0.002077j
[2025-09-10 13:52:34] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -83.047752-0.000274j
[2025-09-10 13:53:09] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -83.101535-0.001894j
[2025-09-10 13:53:44] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -83.060804-0.000439j
[2025-09-10 13:54:19] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -83.000976-0.001915j
[2025-09-10 13:54:54] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -83.041736-0.003616j
[2025-09-10 13:55:30] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -83.057422-0.003198j
[2025-09-10 13:56:05] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -82.946428+0.003302j
[2025-09-10 13:56:40] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -82.862688-0.001267j
[2025-09-10 13:57:15] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -82.806703-0.001886j
[2025-09-10 13:57:51] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -82.884096-0.000397j
[2025-09-10 13:58:26] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -82.831387+0.002867j
[2025-09-10 13:59:01] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -82.884288-0.000508j
[2025-09-10 13:59:36] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -82.859448-0.000559j
[2025-09-10 14:00:11] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -82.888857-0.000571j
[2025-09-10 14:00:47] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -82.765574+0.005325j
[2025-09-10 14:01:22] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -82.906640+0.001983j
[2025-09-10 14:01:57] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -82.796093+0.002621j
[2025-09-10 14:02:32] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -82.846427-0.002933j
[2025-09-10 14:03:07] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -82.975591-0.008084j
[2025-09-10 14:03:43] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -82.966040-0.000312j
[2025-09-10 14:04:18] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -83.012508+0.000044j
[2025-09-10 14:04:53] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -82.887336-0.002714j
[2025-09-10 14:05:28] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -82.817404-0.000243j
[2025-09-10 14:06:03] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -82.816423-0.001772j
[2025-09-10 14:06:39] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -82.941605+0.003736j
[2025-09-10 14:07:14] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -82.874108-0.001519j
[2025-09-10 14:07:49] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -83.051063-0.002666j
[2025-09-10 14:08:24] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -83.037585+0.001335j
[2025-09-10 14:08:59] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -83.047623-0.001191j
[2025-09-10 14:09:35] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -83.114600-0.001984j
[2025-09-10 14:10:10] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -83.144441+0.002198j
[2025-09-10 14:10:45] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -83.079134+0.001020j
[2025-09-10 14:11:20] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -83.099621-0.002903j
[2025-09-10 14:11:55] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -83.168742+0.001571j
[2025-09-10 14:12:30] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -83.204842+0.001272j
[2025-09-10 14:13:06] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -83.111252-0.001588j
[2025-09-10 14:13:41] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -83.108049-0.000753j
[2025-09-10 14:14:16] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -83.038781-0.003591j
[2025-09-10 14:14:51] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -82.925924-0.002783j
[2025-09-10 14:15:26] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -82.903342-0.000518j
[2025-09-10 14:16:01] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -82.954189-0.003017j
[2025-09-10 14:16:36] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -82.995087+0.003123j
[2025-09-10 14:17:11] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -83.022154+0.000607j
[2025-09-10 14:17:47] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -83.111768-0.000232j
[2025-09-10 14:18:22] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -83.009707+0.002561j
[2025-09-10 14:18:57] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -82.942617+0.002218j
[2025-09-10 14:18:57] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-10 14:19:32] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -82.995955+0.001369j
[2025-09-10 14:20:07] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -83.002447+0.004350j
[2025-09-10 14:20:42] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -82.871759-0.000666j
[2025-09-10 14:21:17] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -82.919401-0.000458j
[2025-09-10 14:21:52] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -82.784834+0.003140j
[2025-09-10 14:22:28] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -83.008073-0.004393j
[2025-09-10 14:23:03] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -83.076912-0.001782j
[2025-09-10 14:23:38] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -82.910800-0.013240j
[2025-09-10 14:24:13] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -82.873583-0.000461j
[2025-09-10 14:24:48] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -82.999139+0.002466j
[2025-09-10 14:25:23] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -83.030157-0.000481j
[2025-09-10 14:25:59] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -82.993424+0.002568j
[2025-09-10 14:26:34] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -82.894828-0.000442j
[2025-09-10 14:27:09] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -82.944398+0.001487j
[2025-09-10 14:27:44] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -83.124937+0.000193j
[2025-09-10 14:28:20] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -83.245540+0.001669j
[2025-09-10 14:28:55] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -83.104417+0.001352j
[2025-09-10 14:29:30] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -82.969440+0.002852j
[2025-09-10 14:30:05] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -82.990671+0.002523j
[2025-09-10 14:30:40] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -83.122890-0.003105j
[2025-09-10 14:31:15] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -83.124311-0.000235j
[2025-09-10 14:31:51] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -83.101336+0.000035j
[2025-09-10 14:32:26] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -82.960159-0.001432j
[2025-09-10 14:33:01] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -83.052504-0.001940j
[2025-09-10 14:33:36] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -83.056459-0.003262j
[2025-09-10 14:34:11] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -83.056071-0.004662j
[2025-09-10 14:34:46] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -83.153963+0.003754j
[2025-09-10 14:35:22] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -83.040072+0.000333j
[2025-09-10 14:35:57] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -83.258828-0.002909j
[2025-09-10 14:36:32] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -83.152726+0.000181j
[2025-09-10 14:37:07] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -83.130807-0.005159j
[2025-09-10 14:37:42] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -83.146310-0.000384j
[2025-09-10 14:38:18] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -83.287482+0.003363j
[2025-09-10 14:38:53] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -83.041704-0.000497j
[2025-09-10 14:39:28] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -83.026328+0.000457j
[2025-09-10 14:40:03] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -83.004317+0.000340j
[2025-09-10 14:40:38] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -83.108682+0.000021j
[2025-09-10 14:41:14] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -82.993294-0.004158j
[2025-09-10 14:41:49] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -82.999641+0.000768j
[2025-09-10 14:42:24] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -82.976939+0.001308j
[2025-09-10 14:42:59] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -82.976045-0.000557j
[2025-09-10 14:43:34] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -82.990322-0.001257j
[2025-09-10 14:44:09] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -83.112162-0.002362j
[2025-09-10 14:44:45] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -83.070492+0.002405j
[2025-09-10 14:45:20] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -83.048887-0.003120j
[2025-09-10 14:45:55] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -83.011600-0.001559j
[2025-09-10 14:46:30] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -82.856447+0.002915j
[2025-09-10 14:47:05] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -82.979356-0.001157j
[2025-09-10 14:47:41] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -83.071680-0.000051j
[2025-09-10 14:48:16] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -83.025190+0.000230j
[2025-09-10 14:48:51] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -82.934699-0.002381j
[2025-09-10 14:49:26] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -83.029134+0.001669j
[2025-09-10 14:50:01] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -83.073163+0.002150j
[2025-09-10 14:50:37] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -83.142127-0.003872j
[2025-09-10 14:51:12] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -83.161655-0.002201j
[2025-09-10 14:51:47] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -83.126275-0.002485j
[2025-09-10 14:52:22] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -83.028551+0.024412j
[2025-09-10 14:52:58] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -83.067163+0.000274j
[2025-09-10 14:53:33] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -83.070877+0.003093j
[2025-09-10 14:54:08] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -83.158899+0.006441j
[2025-09-10 14:54:43] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -83.124863+0.002495j
[2025-09-10 14:55:19] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -83.139120-0.004814j
[2025-09-10 14:55:54] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -83.105157-0.002459j
[2025-09-10 14:56:29] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -83.074406+0.003429j
[2025-09-10 14:57:04] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -83.001793-0.000080j
[2025-09-10 14:57:39] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -83.149154+0.001492j
[2025-09-10 14:58:15] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -83.129036-0.003441j
[2025-09-10 14:58:50] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -83.244281-0.003577j
[2025-09-10 14:59:25] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -83.142046-0.003424j
[2025-09-10 15:00:00] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -83.223744-0.001011j
[2025-09-10 15:00:35] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -83.141549+0.003763j
[2025-09-10 15:01:11] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -83.159864+0.001487j
[2025-09-10 15:01:46] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -83.184332+0.000619j
[2025-09-10 15:02:21] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -82.955697-0.000799j
[2025-09-10 15:02:56] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -83.041460-0.000994j
[2025-09-10 15:03:31] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -83.092043-0.002874j
[2025-09-10 15:04:07] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -82.994140+0.000683j
[2025-09-10 15:04:42] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -83.030585-0.000064j
[2025-09-10 15:05:17] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -82.966339+0.000020j
[2025-09-10 15:05:52] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -82.976747+0.001571j
[2025-09-10 15:06:28] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -83.013783-0.000486j
[2025-09-10 15:07:03] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -83.017725-0.000029j
[2025-09-10 15:07:38] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -82.963147-0.000132j
[2025-09-10 15:08:13] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -83.060514+0.000558j
[2025-09-10 15:08:48] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -83.063163+0.002051j
[2025-09-10 15:09:23] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -83.027412-0.000955j
[2025-09-10 15:09:59] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -83.105183-0.003187j
[2025-09-10 15:10:34] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -83.100491-0.001680j
[2025-09-10 15:11:09] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -83.109251+0.001439j
[2025-09-10 15:11:44] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -83.025349-0.002278j
[2025-09-10 15:12:20] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -83.023802+0.003520j
[2025-09-10 15:12:55] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -83.102609-0.001183j
[2025-09-10 15:13:30] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -83.101290-0.004776j
[2025-09-10 15:14:05] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -83.010237+0.000494j
[2025-09-10 15:14:40] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -82.964572+0.000176j
[2025-09-10 15:15:15] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -82.916351-0.000232j
[2025-09-10 15:15:51] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -83.075916-0.000717j
[2025-09-10 15:16:26] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -82.960009-0.001461j
[2025-09-10 15:17:01] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -82.981923+0.001593j
[2025-09-10 15:17:36] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -82.937281+0.002415j
[2025-09-10 15:18:11] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -83.149480+0.001968j
[2025-09-10 15:18:47] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -83.091587+0.002920j
[2025-09-10 15:19:22] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -82.997880-0.000751j
[2025-09-10 15:19:57] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -83.198215+0.001969j
[2025-09-10 15:20:32] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -83.285948-0.000268j
[2025-09-10 15:20:32] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-10 15:21:08] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -83.214156+0.000280j
[2025-09-10 15:21:43] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -83.064594+0.002347j
[2025-09-10 15:22:18] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -83.048696-0.000777j
[2025-09-10 15:22:53] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -83.101430+0.005361j
[2025-09-10 15:23:28] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -83.170145+0.003662j
[2025-09-10 15:24:04] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -83.286082+0.005849j
[2025-09-10 15:24:39] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -83.001426+0.005179j
[2025-09-10 15:25:14] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -83.015807+0.000190j
[2025-09-10 15:25:49] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -83.061004-0.002944j
[2025-09-10 15:26:24] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -83.097034-0.000016j
[2025-09-10 15:26:59] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -82.973481-0.002902j
[2025-09-10 15:27:35] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -82.890690-0.000251j
[2025-09-10 15:28:10] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -82.804534-0.002728j
[2025-09-10 15:28:45] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -82.772191+0.003703j
[2025-09-10 15:29:20] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -82.817065+0.002325j
[2025-09-10 15:29:55] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -82.985436-0.000251j
[2025-09-10 15:30:30] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -82.869194-0.001562j
[2025-09-10 15:31:05] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -83.065232+0.002989j
[2025-09-10 15:31:41] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -83.014193-0.005488j
[2025-09-10 15:32:16] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -82.966428-0.003582j
[2025-09-10 15:32:51] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -82.949864-0.001688j
[2025-09-10 15:33:26] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -83.041253+0.003694j
[2025-09-10 15:34:01] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -83.046684+0.001454j
[2025-09-10 15:34:37] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -82.958890-0.003632j
[2025-09-10 15:35:12] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -83.141043+0.000735j
[2025-09-10 15:35:47] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -83.057809+0.001934j
[2025-09-10 15:36:22] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -83.041610-0.002011j
[2025-09-10 15:36:57] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -82.939841-0.001618j
[2025-09-10 15:37:32] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -82.931402+0.000613j
[2025-09-10 15:38:07] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -83.087210-0.001752j
[2025-09-10 15:38:43] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -83.111669-0.000913j
[2025-09-10 15:39:18] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -83.129374-0.000860j
[2025-09-10 15:39:53] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -82.969655-0.000060j
[2025-09-10 15:40:28] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -82.959286-0.002168j
[2025-09-10 15:41:03] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -82.915777-0.002276j
[2025-09-10 15:41:38] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -83.035997+0.005463j
[2025-09-10 15:42:13] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -83.062272+0.002215j
[2025-09-10 15:42:48] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -83.055393-0.007180j
[2025-09-10 15:43:24] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -83.098508-0.003263j
[2025-09-10 15:43:59] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -83.073811+0.000207j
[2025-09-10 15:44:34] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -83.000690+0.005068j
[2025-09-10 15:45:09] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -82.959823+0.000393j
[2025-09-10 15:45:44] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -83.018058-0.000482j
[2025-09-10 15:46:19] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -82.932680+0.001988j
[2025-09-10 15:46:54] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -82.978889+0.000478j
[2025-09-10 15:47:30] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -82.895855+0.000433j
[2025-09-10 15:48:05] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -82.956038-0.001605j
[2025-09-10 15:48:40] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -83.022960+0.000245j
[2025-09-10 15:49:15] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -82.848374-0.003129j
[2025-09-10 15:49:50] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -82.782027+0.002740j
[2025-09-10 15:50:25] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -83.078740-0.002555j
[2025-09-10 15:51:00] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -83.057493+0.001898j
[2025-09-10 15:51:36] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -82.808239+0.006163j
[2025-09-10 15:52:11] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -82.866355-0.001008j
[2025-09-10 15:52:46] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -82.877924+0.004606j
[2025-09-10 15:53:21] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -82.995220-0.000612j
[2025-09-10 15:53:56] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -83.059767+0.001448j
[2025-09-10 15:54:31] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -83.078608+0.002195j
[2025-09-10 15:55:07] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -83.100480-0.001319j
[2025-09-10 15:55:42] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -82.951043-0.000442j
[2025-09-10 15:56:17] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -82.883392-0.002376j
[2025-09-10 15:56:52] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -82.975876-0.001086j
[2025-09-10 15:57:27] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -83.040503-0.001241j
[2025-09-10 15:58:02] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -82.983608-0.000316j
[2025-09-10 15:58:37] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -83.109863-0.003268j
[2025-09-10 15:59:12] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -83.121351-0.001231j
[2025-09-10 15:59:47] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -82.996175+0.001806j
[2025-09-10 16:00:23] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -82.881537-0.005714j
[2025-09-10 16:00:58] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -83.061785+0.000271j
[2025-09-10 16:01:33] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -83.145824+0.000728j
[2025-09-10 16:02:08] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -83.114809+0.002387j
[2025-09-10 16:02:44] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -83.123871+0.000586j
[2025-09-10 16:03:19] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -82.932744+0.002482j
[2025-09-10 16:03:54] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -82.972160+0.003036j
[2025-09-10 16:04:29] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -83.002888-0.003741j
[2025-09-10 16:05:04] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -82.982713+0.001500j
[2025-09-10 16:05:39] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -82.958773+0.005171j
[2025-09-10 16:06:15] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -83.092277+0.000626j
[2025-09-10 16:06:50] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -83.011057-0.005194j
[2025-09-10 16:07:25] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -82.966956-0.005443j
[2025-09-10 16:08:00] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -83.054622-0.000106j
[2025-09-10 16:08:35] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -83.007498-0.000884j
[2025-09-10 16:09:10] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -82.975901+0.001485j
[2025-09-10 16:09:45] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -83.004152+0.002238j
[2025-09-10 16:10:21] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -83.042650+0.000152j
[2025-09-10 16:10:56] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -82.946909+0.004166j
[2025-09-10 16:11:31] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -83.009212-0.001488j
[2025-09-10 16:12:06] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -82.935692-0.001259j
[2025-09-10 16:12:41] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -82.769198+0.005341j
[2025-09-10 16:13:16] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -82.847401+0.001787j
[2025-09-10 16:13:51] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -82.806539+0.001289j
[2025-09-10 16:14:26] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -82.753249-0.002974j
[2025-09-10 16:15:01] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -82.778673-0.002326j
[2025-09-10 16:15:36] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -82.868074-0.000033j
[2025-09-10 16:16:12] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -82.983358-0.000796j
[2025-09-10 16:16:47] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -82.976990+0.000364j
[2025-09-10 16:17:22] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -82.923533+0.003276j
[2025-09-10 16:17:57] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -82.844980-0.000194j
[2025-09-10 16:18:32] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -82.936280-0.003378j
[2025-09-10 16:19:07] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -82.923652-0.000646j
[2025-09-10 16:19:42] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -83.002880+0.000259j
[2025-09-10 16:20:18] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -82.959722-0.001450j
[2025-09-10 16:20:53] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -82.939412-0.002246j
[2025-09-10 16:21:28] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -82.925071+0.001587j
[2025-09-10 16:22:03] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -82.992173-0.000283j
[2025-09-10 16:22:03] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-10 16:22:38] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -82.943550-0.000449j
[2025-09-10 16:23:14] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -82.979372-0.005595j
[2025-09-10 16:23:49] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -82.924201-0.003084j
[2025-09-10 16:24:24] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -83.229668-0.002393j
[2025-09-10 16:24:59] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -83.076974-0.002825j
[2025-09-10 16:25:34] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -83.051049+0.000683j
[2025-09-10 16:26:09] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -82.955992+0.002495j
[2025-09-10 16:26:44] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -83.090536+0.002323j
[2025-09-10 16:27:19] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -83.003493+0.003481j
[2025-09-10 16:27:54] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -83.012563+0.001825j
[2025-09-10 16:28:29] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -82.984132-0.007076j
[2025-09-10 16:29:04] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -83.044761+0.001581j
[2025-09-10 16:29:40] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -83.138113+0.000117j
[2025-09-10 16:30:15] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -83.038639+0.001420j
[2025-09-10 16:30:50] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -83.058889+0.000739j
[2025-09-10 16:31:25] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -82.965379+0.002921j
[2025-09-10 16:32:00] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -83.081042-0.000222j
[2025-09-10 16:32:35] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -83.041522-0.001604j
[2025-09-10 16:33:10] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -83.033374-0.002622j
[2025-09-10 16:33:45] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -82.965730+0.001021j
[2025-09-10 16:34:20] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -82.991775-0.004068j
[2025-09-10 16:34:55] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -83.149199-0.000801j
[2025-09-10 16:35:31] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -83.058982-0.000448j
[2025-09-10 16:36:06] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -83.228817-0.002347j
[2025-09-10 16:36:41] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -83.190681+0.003615j
[2025-09-10 16:37:16] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -82.984693+0.006834j
[2025-09-10 16:37:51] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -83.118227+0.000822j
[2025-09-10 16:38:26] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -83.144872-0.000264j
[2025-09-10 16:39:01] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -83.096290-0.000501j
[2025-09-10 16:39:36] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -83.138404+0.001953j
[2025-09-10 16:40:11] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -83.168202-0.000140j
[2025-09-10 16:40:47] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -83.168197+0.000903j
[2025-09-10 16:41:22] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -83.188162-0.001402j
[2025-09-10 16:41:57] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -83.243252-0.001355j
[2025-09-10 16:42:32] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -83.067184+0.000731j
[2025-09-10 16:43:07] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -83.051519+0.002835j
[2025-09-10 16:43:43] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -82.959445+0.008323j
[2025-09-10 16:44:18] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -83.127904+0.000685j
[2025-09-10 16:44:53] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -82.989747+0.005320j
[2025-09-10 16:45:28] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -83.092015+0.002446j
[2025-09-10 16:46:03] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -83.038598+0.001295j
[2025-09-10 16:46:38] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -83.145100+0.000818j
[2025-09-10 16:47:14] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -83.075973-0.003323j
[2025-09-10 16:47:49] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -83.067355-0.002041j
[2025-09-10 16:48:24] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -83.089398-0.001425j
[2025-09-10 16:48:59] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -83.054030+0.001937j
[2025-09-10 16:49:34] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -82.935401+0.004599j
[2025-09-10 16:50:10] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -82.833182+0.000515j
[2025-09-10 16:50:45] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -82.994305+0.000918j
[2025-09-10 16:51:20] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -82.949534-0.000693j
[2025-09-10 16:51:55] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -82.974114-0.004805j
[2025-09-10 16:52:30] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -82.888771+0.001044j
[2025-09-10 16:53:05] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -83.069806-0.002004j
[2025-09-10 16:53:41] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -82.881158-0.002572j
[2025-09-10 16:54:16] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -82.934773+0.001686j
[2025-09-10 16:54:51] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -82.904950-0.000542j
[2025-09-10 16:55:26] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -83.060100+0.000401j
[2025-09-10 16:56:01] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -83.097296-0.002398j
[2025-09-10 16:56:36] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -83.019352-0.002094j
[2025-09-10 16:57:12] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -83.119291-0.002841j
[2025-09-10 16:57:47] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -83.092000-0.000882j
[2025-09-10 16:58:22] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -83.186109-0.003120j
[2025-09-10 16:58:57] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -83.046197-0.002946j
[2025-09-10 16:59:32] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -82.936542-0.001558j
[2025-09-10 17:00:07] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -83.036205-0.002519j
[2025-09-10 17:00:42] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -83.090001-0.000562j
[2025-09-10 17:01:18] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -83.102044-0.004591j
[2025-09-10 17:01:53] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -83.067231+0.001418j
[2025-09-10 17:02:28] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -82.862947+0.001957j
[2025-09-10 17:03:03] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -83.099685+0.005307j
[2025-09-10 17:03:38] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -83.044377+0.000709j
[2025-09-10 17:04:13] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -83.040344-0.001421j
[2025-09-10 17:04:48] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -83.137915-0.004436j
[2025-09-10 17:05:23] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -83.195697-0.000352j
[2025-09-10 17:05:58] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -83.254126+0.002281j
[2025-09-10 17:06:33] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -83.057631+0.005073j
[2025-09-10 17:07:08] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -82.976496+0.002279j
[2025-09-10 17:07:43] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -82.971952+0.000118j
[2025-09-10 17:08:19] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -82.932731+0.004473j
[2025-09-10 17:08:54] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -82.938265+0.001852j
[2025-09-10 17:09:29] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -82.830432-0.001957j
[2025-09-10 17:10:04] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -82.938005+0.002881j
[2025-09-10 17:10:39] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -82.896306-0.002225j
[2025-09-10 17:11:14] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -83.007271+0.002484j
[2025-09-10 17:11:49] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -82.889176-0.005766j
[2025-09-10 17:12:24] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -83.121561+0.000067j
[2025-09-10 17:12:59] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -83.105516-0.000429j
[2025-09-10 17:13:34] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -83.123520+0.002970j
[2025-09-10 17:14:09] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -83.052172+0.000339j
[2025-09-10 17:14:36] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -83.133805-0.003732j
[2025-09-10 17:15:00] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -83.005533+0.001059j
[2025-09-10 17:15:23] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -82.981191-0.001899j
[2025-09-10 17:15:47] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -83.042865+0.000632j
[2025-09-10 17:16:10] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -83.168258+0.000899j
[2025-09-10 17:16:33] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -83.186362+0.001361j
[2025-09-10 17:16:57] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -83.169204+0.002449j
[2025-09-10 17:17:20] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -83.047655-0.002569j
[2025-09-10 17:17:44] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -83.116282+0.006240j
[2025-09-10 17:18:07] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -83.030935-0.003037j
[2025-09-10 17:18:31] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -82.995173-0.001143j
[2025-09-10 17:18:54] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -82.967620-0.000089j
[2025-09-10 17:19:18] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -82.970008+0.004785j
[2025-09-10 17:19:41] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -82.842163-0.003639j
[2025-09-10 17:20:05] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -82.834319+0.000447j
[2025-09-10 17:20:28] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -82.733107-0.000499j
[2025-09-10 17:20:28] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-10 17:20:28] ✅ Training completed | Restarts: 2
[2025-09-10 17:20:28] ============================================================
[2025-09-10 17:20:28] Training completed | Runtime: 36905.9s
[2025-09-10 17:20:38] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-10 17:20:38] ============================================================
