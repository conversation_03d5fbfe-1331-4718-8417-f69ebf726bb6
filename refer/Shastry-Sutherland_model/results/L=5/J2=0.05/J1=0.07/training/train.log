[2025-09-09 20:51:38] ✓ 从checkpoint恢复: results/L=5/J2=0.05/J1=0.06/training/checkpoints/final_GCNN.pkl
[2025-09-09 20:51:38]   - 迭代次数: final
[2025-09-09 20:51:39]   - 能量: -81.613190-0.001125j ± 0.110476
[2025-09-09 20:51:39]   - 时间戳: 2025-09-09T20:46:55.084009+08:00
[2025-09-09 20:52:08] ✓ 变分状态参数已从checkpoint恢复
[2025-09-09 20:52:08] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-09 20:52:08] ==================================================
[2025-09-09 20:52:08] GCNN for Shastry-Sutherland Model
[2025-09-09 20:52:08] ==================================================
[2025-09-09 20:52:08] System parameters:
[2025-09-09 20:52:08]   - System size: L=5, N=100
[2025-09-09 20:52:08]   - System parameters: J1=0.07, J2=0.05, Q=0.95
[2025-09-09 20:52:08] --------------------------------------------------
[2025-09-09 20:52:08] Model parameters:
[2025-09-09 20:52:08]   - Number of layers = 4
[2025-09-09 20:52:08]   - Number of features = 4
[2025-09-09 20:52:08]   - Total parameters = 19628
[2025-09-09 20:52:08] --------------------------------------------------
[2025-09-09 20:52:08] Training parameters:
[2025-09-09 20:52:08]   - Learning rate: 0.015
[2025-09-09 20:52:08]   - Total iterations: 1050
[2025-09-09 20:52:08]   - Annealing cycles: 3
[2025-09-09 20:52:08]   - Initial period: 150
[2025-09-09 20:52:08]   - Period multiplier: 2.0
[2025-09-09 20:52:08]   - Temperature range: 0.0-1.0
[2025-09-09 20:52:08]   - Samples: 4096
[2025-09-09 20:52:08]   - Discarded samples: 0
[2025-09-09 20:52:08]   - Chunk size: 2048
[2025-09-09 20:52:08]   - Diagonal shift: 0.2
[2025-09-09 20:52:08]   - Gradient clipping: 1.0
[2025-09-09 20:52:08]   - Checkpoint enabled: interval=105
[2025-09-09 20:52:08]   - Checkpoint directory: results/L=5/J2=0.05/J1=0.07/training/checkpoints
[2025-09-09 20:52:08] --------------------------------------------------
[2025-09-09 20:52:08] Device status:
[2025-09-09 20:52:08]   - Devices model: NVIDIA H200 NVL
[2025-09-09 20:52:08]   - Number of devices: 1
[2025-09-09 20:52:08]   - Sharding: True
[2025-09-09 20:52:08] ============================================================
[2025-09-09 20:54:34] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -82.512278+0.017114j
[2025-09-09 20:56:09] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -82.624934+0.005583j
[2025-09-09 20:56:44] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -82.498292+0.002802j
[2025-09-09 20:57:19] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -82.522129+0.002477j
[2025-09-09 20:57:54] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -82.646433+0.008360j
[2025-09-09 20:58:29] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -82.524444-0.001985j
[2025-09-09 20:59:04] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -82.453881-0.004071j
[2025-09-09 20:59:39] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -82.617215-0.001400j
[2025-09-09 21:00:14] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -82.632191+0.002363j
[2025-09-09 21:00:49] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -82.500134-0.002294j
[2025-09-09 21:01:23] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -82.370065+0.004166j
[2025-09-09 21:01:58] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -82.255198+0.005715j
[2025-09-09 21:02:33] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -82.361635+0.001475j
[2025-09-09 21:03:08] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -82.322985-0.006319j
[2025-09-09 21:03:43] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -82.497735-0.000801j
[2025-09-09 21:04:18] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -82.400782+0.001738j
[2025-09-09 21:04:53] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -82.475582+0.000604j
[2025-09-09 21:05:28] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -82.424797-0.004217j
[2025-09-09 21:06:03] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -82.515841-0.001043j
[2025-09-09 21:06:38] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -82.401667+0.000141j
[2025-09-09 21:07:13] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -82.475678+0.006654j
[2025-09-09 21:07:47] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -82.525732+0.003548j
[2025-09-09 21:08:22] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -82.510705+0.000256j
[2025-09-09 21:08:57] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -82.603152-0.000946j
[2025-09-09 21:09:32] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -82.521483+0.002298j
[2025-09-09 21:10:07] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -82.631204-0.006357j
[2025-09-09 21:10:42] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -82.631836+0.000562j
[2025-09-09 21:11:17] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -82.586505+0.002145j
[2025-09-09 21:11:52] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -82.593381+0.002887j
[2025-09-09 21:12:27] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -82.420835-0.006168j
[2025-09-09 21:13:02] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -82.437772-0.007592j
[2025-09-09 21:13:37] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -82.463128+0.001114j
[2025-09-09 21:14:11] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -82.497706+0.003125j
[2025-09-09 21:14:46] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -82.378555-0.000706j
[2025-09-09 21:15:21] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -82.241822+0.001873j
[2025-09-09 21:15:56] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -82.325063-0.004116j
[2025-09-09 21:16:31] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -82.509812-0.006953j
[2025-09-09 21:17:06] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -82.377116+0.000881j
[2025-09-09 21:17:41] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -82.429835+0.004857j
[2025-09-09 21:18:16] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -82.439855-0.004972j
[2025-09-09 21:18:51] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -82.442348+0.004513j
[2025-09-09 21:19:26] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -82.292159-0.004946j
[2025-09-09 21:20:01] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -82.350743-0.001451j
[2025-09-09 21:20:36] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -82.313222-0.001719j
[2025-09-09 21:21:10] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -82.240788-0.006321j
[2025-09-09 21:21:45] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -82.225654+0.004444j
[2025-09-09 21:22:20] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -82.357348-0.002150j
[2025-09-09 21:22:55] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -82.293879+0.000534j
[2025-09-09 21:23:30] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -82.276157-0.003298j
[2025-09-09 21:24:05] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -82.200910+0.005514j
[2025-09-09 21:24:40] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -82.272272+0.001742j
[2025-09-09 21:25:15] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -82.430202-0.002942j
[2025-09-09 21:25:50] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -82.360016+0.001923j
[2025-09-09 21:26:25] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -82.255901-0.000088j
[2025-09-09 21:27:00] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -82.253205-0.004212j
[2025-09-09 21:27:35] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -82.163969-0.003147j
[2025-09-09 21:28:10] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -82.271836-0.006573j
[2025-09-09 21:28:45] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -82.335338+0.000058j
[2025-09-09 21:29:19] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -82.254012+0.005675j
[2025-09-09 21:29:54] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -82.252957-0.000343j
[2025-09-09 21:30:29] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -82.282081-0.001124j
[2025-09-09 21:31:04] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -82.369918+0.002143j
[2025-09-09 21:31:39] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -82.288571+0.004790j
[2025-09-09 21:32:14] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -82.244546+0.000515j
[2025-09-09 21:32:49] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -82.400430-0.002341j
[2025-09-09 21:33:24] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -82.330207+0.000418j
[2025-09-09 21:33:59] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -82.387493+0.003485j
[2025-09-09 21:34:34] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -82.209682+0.004264j
[2025-09-09 21:35:09] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -82.312882-0.007396j
[2025-09-09 21:35:43] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -82.248561-0.002096j
[2025-09-09 21:36:18] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -82.483525+0.003517j
[2025-09-09 21:36:53] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -82.213053+0.006069j
[2025-09-09 21:37:28] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -82.446261+0.005935j
[2025-09-09 21:38:03] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -82.295910-0.004497j
[2025-09-09 21:38:37] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -82.273474+0.005124j
[2025-09-09 21:39:12] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -82.343162+0.000120j
[2025-09-09 21:39:47] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -82.399815+0.000046j
[2025-09-09 21:40:22] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -82.460788+0.004481j
[2025-09-09 21:40:57] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -82.436518-0.003017j
[2025-09-09 21:41:32] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -82.342804-0.001456j
[2025-09-09 21:42:07] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -82.449209-0.000856j
[2025-09-09 21:42:42] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -82.449781-0.000485j
[2025-09-09 21:43:17] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -82.559904-0.002110j
[2025-09-09 21:43:52] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -82.398538-0.003098j
[2025-09-09 21:44:27] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -82.377146+0.005129j
[2025-09-09 21:45:02] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -82.499412+0.000061j
[2025-09-09 21:45:37] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -82.540761-0.004447j
[2025-09-09 21:46:12] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -82.498220-0.000030j
[2025-09-09 21:46:46] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -82.435205+0.001349j
[2025-09-09 21:47:22] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -82.215237-0.004072j
[2025-09-09 21:47:56] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -82.386844+0.000486j
[2025-09-09 21:48:31] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -82.334838-0.007161j
[2025-09-09 21:49:06] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -82.372514-0.003419j
[2025-09-09 21:49:41] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -82.422602-0.004942j
[2025-09-09 21:50:16] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -82.334595-0.000724j
[2025-09-09 21:50:51] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -82.445133+0.002349j
[2025-09-09 21:51:26] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -82.444950+0.004053j
[2025-09-09 21:52:01] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -82.478101-0.001081j
[2025-09-09 21:52:35] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -82.258273+0.003307j
[2025-09-09 21:53:10] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -82.263728-0.000199j
[2025-09-09 21:53:45] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -82.452135+0.002362j
[2025-09-09 21:54:20] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -82.425755+0.001264j
[2025-09-09 21:54:55] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -82.250190-0.000670j
[2025-09-09 21:55:30] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -82.269640-0.003105j
[2025-09-09 21:56:04] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -82.234194+0.000743j
[2025-09-09 21:56:04] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-09 21:56:39] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -82.310226-0.001495j
[2025-09-09 21:57:14] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -82.243703-0.003466j
[2025-09-09 21:57:49] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -82.358963-0.002307j
[2025-09-09 21:58:24] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -82.455610+0.001988j
[2025-09-09 21:58:59] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -82.539321-0.007346j
[2025-09-09 21:59:33] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -82.450118-0.001458j
[2025-09-09 22:00:08] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -82.387953-0.003320j
[2025-09-09 22:00:43] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -82.464655+0.002614j
[2025-09-09 22:01:18] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -82.344600-0.000763j
[2025-09-09 22:01:53] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -82.276851-0.000028j
[2025-09-09 22:02:28] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -82.184244-0.000950j
[2025-09-09 22:03:02] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -82.227054-0.001030j
[2025-09-09 22:03:37] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -82.332224-0.000588j
[2025-09-09 22:04:12] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -82.188557-0.000908j
[2025-09-09 22:04:47] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -82.233409-0.004762j
[2025-09-09 22:05:22] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -82.282421+0.005638j
[2025-09-09 22:05:56] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -82.359343+0.001469j
[2025-09-09 22:06:31] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -82.249961+0.001606j
[2025-09-09 22:07:06] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -82.340572+0.003076j
[2025-09-09 22:07:41] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -82.319920-0.000111j
[2025-09-09 22:08:16] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -82.282482-0.000119j
[2025-09-09 22:08:50] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -82.496429+0.003107j
[2025-09-09 22:09:25] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -82.470108+0.003721j
[2025-09-09 22:10:00] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -82.387159+0.001199j
[2025-09-09 22:10:35] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -82.395873+0.002278j
[2025-09-09 22:11:10] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -82.396079-0.000241j
[2025-09-09 22:11:45] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -82.383135-0.001299j
[2025-09-09 22:12:19] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -82.251221+0.002948j
[2025-09-09 22:12:54] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -82.389405-0.000065j
[2025-09-09 22:13:28] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -82.417203-0.005865j
[2025-09-09 22:14:03] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -82.329761+0.000278j
[2025-09-09 22:14:37] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -82.294873-0.000685j
[2025-09-09 22:15:12] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -82.383548-0.003290j
[2025-09-09 22:15:47] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -82.325688+0.001954j
[2025-09-09 22:16:22] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -82.317350+0.003591j
[2025-09-09 22:16:57] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -82.340834+0.002322j
[2025-09-09 22:17:31] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -82.453830+0.002709j
[2025-09-09 22:18:06] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -82.371052-0.007683j
[2025-09-09 22:18:41] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -82.451215-0.002905j
[2025-09-09 22:19:16] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -82.316128-0.000046j
[2025-09-09 22:19:51] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -82.427160-0.001363j
[2025-09-09 22:20:26] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -82.501371-0.001515j
[2025-09-09 22:21:01] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -82.506694-0.001768j
[2025-09-09 22:21:36] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -82.449136-0.001442j
[2025-09-09 22:22:11] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -82.685468-0.001486j
[2025-09-09 22:22:11] RESTART #1 | Period: 300
[2025-09-09 22:22:46] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -82.502163-0.002761j
[2025-09-09 22:23:20] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -82.539975-0.002201j
[2025-09-09 22:23:55] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -82.543177-0.004021j
[2025-09-09 22:24:29] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -82.401361-0.004391j
[2025-09-09 22:25:04] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -82.506301-0.002750j
[2025-09-09 22:25:38] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -82.476216-0.004065j
[2025-09-09 22:26:13] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -82.504794-0.002900j
[2025-09-09 22:26:48] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -82.295142+0.000129j
[2025-09-09 22:27:23] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -82.414488-0.001552j
[2025-09-09 22:27:58] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -82.451796+0.001723j
[2025-09-09 22:28:32] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -82.454686-0.000454j
[2025-09-09 22:29:07] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -82.439224-0.003919j
[2025-09-09 22:29:42] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -82.474762+0.002052j
[2025-09-09 22:30:17] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -82.361838+0.001725j
[2025-09-09 22:30:51] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -82.453502-0.001156j
[2025-09-09 22:31:26] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -82.421282+0.005739j
[2025-09-09 22:32:01] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -82.496371+0.004096j
[2025-09-09 22:32:36] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -82.473592-0.002004j
[2025-09-09 22:33:11] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -82.462550-0.002256j
[2025-09-09 22:33:46] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -82.259124+0.002504j
[2025-09-09 22:34:21] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -82.273784+0.000409j
[2025-09-09 22:34:55] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -82.386307+0.001729j
[2025-09-09 22:35:30] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -82.427381+0.000940j
[2025-09-09 22:36:05] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -82.292716-0.000616j
[2025-09-09 22:36:40] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -82.387174+0.003335j
[2025-09-09 22:37:15] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -82.423348-0.002865j
[2025-09-09 22:37:49] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -82.369459+0.001229j
[2025-09-09 22:38:24] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -82.361346+0.005903j
[2025-09-09 22:38:59] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -82.466665+0.000445j
[2025-09-09 22:39:34] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -82.566333-0.002839j
[2025-09-09 22:40:09] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -82.417846-0.005298j
[2025-09-09 22:40:44] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -82.332611+0.001226j
[2025-09-09 22:41:18] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -82.314426-0.001964j
[2025-09-09 22:41:53] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -82.385354-0.002987j
[2025-09-09 22:42:28] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -82.484256-0.001630j
[2025-09-09 22:43:03] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -82.364292-0.004473j
[2025-09-09 22:43:38] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -82.453798+0.002647j
[2025-09-09 22:44:13] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -82.322108-0.000829j
[2025-09-09 22:44:48] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -82.329755-0.002471j
[2025-09-09 22:45:22] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -82.274975-0.003372j
[2025-09-09 22:45:57] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -82.234680-0.002166j
[2025-09-09 22:46:32] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -82.248144+0.007180j
[2025-09-09 22:47:07] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -82.210983+0.000836j
[2025-09-09 22:47:42] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -82.143202+0.003622j
[2025-09-09 22:48:17] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -82.240268+0.000353j
[2025-09-09 22:48:51] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -82.241552+0.000425j
[2025-09-09 22:49:26] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -82.176978-0.002081j
[2025-09-09 22:50:01] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -82.263615-0.004625j
[2025-09-09 22:50:36] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -82.359866-0.000543j
[2025-09-09 22:51:11] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -82.421287-0.003284j
[2025-09-09 22:51:46] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -82.318969-0.007336j
[2025-09-09 22:52:21] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -82.367497+0.002632j
[2025-09-09 22:52:55] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -82.243117-0.004482j
[2025-09-09 22:53:30] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -82.374141-0.005533j
[2025-09-09 22:54:05] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -82.305422-0.002880j
[2025-09-09 22:54:40] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -82.401426+0.001411j
[2025-09-09 22:55:15] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -82.514092+0.000370j
[2025-09-09 22:55:50] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -82.570656-0.001613j
[2025-09-09 22:56:25] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -82.465493-0.000458j
[2025-09-09 22:56:59] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -82.478872-0.002097j
[2025-09-09 22:56:59] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-09 22:57:34] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -82.684446-0.003717j
[2025-09-09 22:58:09] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -82.645238+0.000034j
[2025-09-09 22:58:44] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -82.456796+0.000042j
[2025-09-09 22:59:19] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -82.554614-0.004410j
[2025-09-09 22:59:53] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -82.462038-0.004152j
[2025-09-09 23:00:28] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -82.551760-0.000379j
[2025-09-09 23:01:03] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -82.391543-0.004262j
[2025-09-09 23:01:38] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -82.504731+0.000571j
[2025-09-09 23:02:13] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -82.237133+0.003225j
[2025-09-09 23:02:47] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -82.348495+0.000649j
[2025-09-09 23:03:22] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -82.357930+0.002674j
[2025-09-09 23:03:57] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -82.296284+0.001338j
[2025-09-09 23:04:32] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -82.225961-0.001336j
[2025-09-09 23:05:07] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -82.409745+0.000513j
[2025-09-09 23:05:42] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -82.365205+0.003780j
[2025-09-09 23:06:17] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -82.356170+0.003044j
[2025-09-09 23:06:51] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -82.410465-0.000865j
[2025-09-09 23:07:26] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -82.440277+0.000897j
[2025-09-09 23:08:01] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -82.421372+0.002967j
[2025-09-09 23:08:36] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -82.384530+0.000618j
[2025-09-09 23:09:11] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -82.354251-0.004301j
[2025-09-09 23:09:45] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -82.327766+0.000598j
[2025-09-09 23:10:20] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -82.342963-0.002147j
[2025-09-09 23:10:55] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -82.426613-0.008540j
[2025-09-09 23:11:29] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -82.463078+0.001041j
[2025-09-09 23:12:04] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -82.303251+0.000633j
[2025-09-09 23:12:39] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -82.219206+0.002667j
[2025-09-09 23:13:14] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -82.373288+0.002789j
[2025-09-09 23:13:49] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -82.444165+0.007835j
[2025-09-09 23:14:24] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -82.414435+0.004948j
[2025-09-09 23:14:58] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -82.532875+0.006174j
[2025-09-09 23:15:33] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -82.417522-0.000322j
[2025-09-09 23:16:08] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -82.586390+0.002145j
[2025-09-09 23:16:43] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -82.503879-0.000493j
[2025-09-09 23:17:18] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -82.559702-0.000141j
[2025-09-09 23:17:52] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -82.555938-0.005767j
[2025-09-09 23:18:27] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -82.457963-0.001862j
[2025-09-09 23:19:02] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -82.528564+0.005047j
[2025-09-09 23:19:37] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -82.513173-0.000326j
[2025-09-09 23:20:12] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -82.481198+0.000702j
[2025-09-09 23:20:46] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -82.525636+0.001692j
[2025-09-09 23:21:21] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -82.475967+0.002225j
[2025-09-09 23:21:56] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -82.526320+0.002141j
[2025-09-09 23:22:31] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -82.427471-0.000614j
[2025-09-09 23:23:06] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -82.421068-0.001448j
[2025-09-09 23:23:41] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -82.358877-0.000598j
[2025-09-09 23:24:15] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -82.277146+0.001787j
[2025-09-09 23:24:50] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -82.342745-0.000711j
[2025-09-09 23:25:25] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -82.299077+0.000602j
[2025-09-09 23:26:00] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -82.271917-0.002795j
[2025-09-09 23:26:35] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -82.209782+0.002650j
[2025-09-09 23:27:09] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -82.345704-0.007597j
[2025-09-09 23:27:44] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -82.477825-0.006807j
[2025-09-09 23:28:19] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -82.506541-0.001941j
[2025-09-09 23:28:54] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -82.474203+0.005516j
[2025-09-09 23:29:29] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -82.406368+0.007472j
[2025-09-09 23:30:03] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -82.644904-0.002820j
[2025-09-09 23:30:38] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -82.358908+0.000624j
[2025-09-09 23:31:13] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -82.337273+0.003765j
[2025-09-09 23:31:48] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -82.271612-0.001266j
[2025-09-09 23:32:22] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -82.332264+0.003922j
[2025-09-09 23:32:57] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -82.316670-0.002467j
[2025-09-09 23:33:32] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -82.358695+0.005936j
[2025-09-09 23:34:07] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -82.335190+0.003559j
[2025-09-09 23:34:42] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -82.377709+0.005179j
[2025-09-09 23:35:17] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -82.351791+0.003495j
[2025-09-09 23:35:52] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -82.289374+0.003048j
[2025-09-09 23:36:26] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -82.261440+0.000885j
[2025-09-09 23:37:01] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -82.250357-0.003511j
[2025-09-09 23:37:36] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -82.300011+0.001531j
[2025-09-09 23:38:11] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -82.312022-0.003973j
[2025-09-09 23:38:46] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -82.146996+0.001245j
[2025-09-09 23:39:21] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -82.341813+0.006577j
[2025-09-09 23:39:55] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -82.208887+0.003474j
[2025-09-09 23:40:30] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -82.198547-0.004274j
[2025-09-09 23:41:05] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -82.080776-0.001426j
[2025-09-09 23:41:40] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -82.328731-0.002704j
[2025-09-09 23:42:14] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -82.414667+0.001824j
[2025-09-09 23:42:49] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -82.450042+0.003917j
[2025-09-09 23:43:24] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -82.304866+0.001432j
[2025-09-09 23:43:59] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -82.204252+0.002702j
[2025-09-09 23:44:34] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -82.148366+0.006481j
[2025-09-09 23:45:08] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -82.096358+0.001199j
[2025-09-09 23:45:43] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -82.405992-0.002118j
[2025-09-09 23:46:18] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -82.480997+0.000123j
[2025-09-09 23:46:53] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -82.349648+0.003057j
[2025-09-09 23:47:28] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -82.256511-0.006145j
[2025-09-09 23:48:03] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -82.342739-0.002828j
[2025-09-09 23:48:37] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -82.368820+0.003235j
[2025-09-09 23:49:12] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -82.478268-0.000091j
[2025-09-09 23:49:47] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -82.324524+0.002479j
[2025-09-09 23:50:22] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -82.277138+0.000833j
[2025-09-09 23:50:57] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -82.284222-0.003507j
[2025-09-09 23:51:32] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -82.317181+0.003030j
[2025-09-09 23:52:06] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -82.331224-0.000616j
[2025-09-09 23:52:41] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -82.478857-0.000172j
[2025-09-09 23:53:16] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -82.310397+0.000438j
[2025-09-09 23:53:51] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -82.311809+0.003160j
[2025-09-09 23:54:26] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -82.315129+0.000985j
[2025-09-09 23:55:00] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -82.334004-0.004089j
[2025-09-09 23:55:35] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -82.314267-0.000037j
[2025-09-09 23:56:10] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -82.555274+0.001031j
[2025-09-09 23:56:45] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -82.462760+0.001307j
[2025-09-09 23:57:20] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -82.438729-0.002036j
[2025-09-09 23:57:54] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -82.443078+0.008221j
[2025-09-09 23:57:54] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-09 23:58:29] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -82.444144+0.000943j
[2025-09-09 23:59:04] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -82.385742+0.005471j
[2025-09-09 23:59:39] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -82.465962-0.002808j
[2025-09-10 00:00:14] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -82.556571+0.006013j
[2025-09-10 00:00:48] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -82.516980+0.001528j
[2025-09-10 00:01:23] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -82.548176+0.000292j
[2025-09-10 00:01:58] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -82.499756+0.004801j
[2025-09-10 00:02:33] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -82.642724+0.005043j
[2025-09-10 00:03:08] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -82.693807+0.002595j
[2025-09-10 00:03:43] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -82.706154-0.000280j
[2025-09-10 00:04:18] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -82.523860+0.001118j
[2025-09-10 00:04:53] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -82.473585+0.000374j
[2025-09-10 00:05:27] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -82.398827+0.003965j
[2025-09-10 00:06:02] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -82.327481+0.000902j
[2025-09-10 00:06:36] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -82.235042+0.002475j
[2025-09-10 00:07:12] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -82.354954+0.000749j
[2025-09-10 00:07:45] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -82.466977-0.002351j
[2025-09-10 00:08:23] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -82.286725-0.004652j
[2025-09-10 00:08:59] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -82.146996+0.000691j
[2025-09-10 00:09:34] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -82.238175-0.000031j
[2025-09-10 00:10:09] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -82.184321-0.002772j
[2025-09-10 00:10:43] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -82.342977-0.004146j
[2025-09-10 00:11:18] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -82.409136-0.003864j
[2025-09-10 00:11:53] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -82.378294-0.000805j
[2025-09-10 00:12:28] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -82.417901-0.003458j
[2025-09-10 00:13:03] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -82.507713+0.003951j
[2025-09-10 00:13:38] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -82.579136+0.000871j
[2025-09-10 00:14:13] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -82.491648+0.001717j
[2025-09-10 00:14:47] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -82.417839-0.000826j
[2025-09-10 00:15:22] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -82.398896-0.000806j
[2025-09-10 00:15:57] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -82.504160-0.004410j
[2025-09-10 00:16:31] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -82.394998+0.003228j
[2025-09-10 00:17:06] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -82.524658+0.001322j
[2025-09-10 00:17:41] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -82.373689+0.002113j
[2025-09-10 00:18:16] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -82.397409+0.002023j
[2025-09-10 00:18:51] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -82.418417+0.000578j
[2025-09-10 00:19:25] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -82.363045-0.005638j
[2025-09-10 00:20:00] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -82.449265+0.003574j
[2025-09-10 00:20:35] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -82.261603+0.003815j
[2025-09-10 00:21:10] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -82.136008+0.014224j
[2025-09-10 00:21:45] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -82.242785-0.000298j
[2025-09-10 00:22:19] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -82.373261-0.004633j
[2025-09-10 00:22:54] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -82.510506+0.000581j
[2025-09-10 00:23:29] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -82.257910-0.000687j
[2025-09-10 00:24:04] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -82.434174-0.008965j
[2025-09-10 00:24:39] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -82.505260+0.000777j
[2025-09-10 00:25:14] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -82.622591-0.003881j
[2025-09-10 00:25:48] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -82.401549+0.003301j
[2025-09-10 00:26:23] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -82.482645-0.002484j
[2025-09-10 00:26:58] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -82.543633+0.002628j
[2025-09-10 00:27:33] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -82.384805+0.001794j
[2025-09-10 00:28:08] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -82.581410-0.000559j
[2025-09-10 00:28:43] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -82.564302+0.002678j
[2025-09-10 00:29:17] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -82.647026-0.000662j
[2025-09-10 00:29:52] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -82.446154-0.002202j
[2025-09-10 00:30:27] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -82.479424+0.006969j
[2025-09-10 00:31:02] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -82.534867+0.001441j
[2025-09-10 00:31:37] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -82.475821-0.003500j
[2025-09-10 00:32:11] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -82.383887+0.004286j
[2025-09-10 00:32:46] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -82.214757-0.002885j
[2025-09-10 00:33:21] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -82.350386+0.001085j
[2025-09-10 00:33:56] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -82.514802+0.000646j
[2025-09-10 00:34:31] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -82.421170+0.000084j
[2025-09-10 00:35:05] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -82.545469+0.000451j
[2025-09-10 00:35:40] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -82.629332-0.000382j
[2025-09-10 00:36:15] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -82.506997-0.002916j
[2025-09-10 00:36:50] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -82.541007+0.001711j
[2025-09-10 00:37:25] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -82.317472+0.004675j
[2025-09-10 00:37:59] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -82.256101+0.000840j
[2025-09-10 00:38:34] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -82.442836+0.000680j
[2025-09-10 00:39:09] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -82.285064-0.000501j
[2025-09-10 00:39:44] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -82.562137-0.000663j
[2025-09-10 00:40:18] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -82.410739+0.005265j
[2025-09-10 00:40:53] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -82.364422+0.001639j
[2025-09-10 00:41:28] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -82.493001-0.004595j
[2025-09-10 00:42:03] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -82.515286+0.003746j
[2025-09-10 00:42:38] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -82.474422-0.003537j
[2025-09-10 00:43:13] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -82.334997+0.001204j
[2025-09-10 00:43:48] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -82.434877-0.003205j
[2025-09-10 00:44:22] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -82.383265-0.000878j
[2025-09-10 00:44:56] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -82.365058-0.004680j
[2025-09-10 00:45:31] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -82.473882-0.002020j
[2025-09-10 00:46:06] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -82.335645-0.003510j
[2025-09-10 00:46:40] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -82.306168-0.000177j
[2025-09-10 00:47:15] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -82.209974-0.001020j
[2025-09-10 00:47:50] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -82.241962-0.001254j
[2025-09-10 00:48:25] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -82.203271+0.003238j
[2025-09-10 00:49:00] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -82.150922-0.002180j
[2025-09-10 00:49:35] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -82.202427+0.003997j
[2025-09-10 00:50:10] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -82.310761-0.000193j
[2025-09-10 00:50:45] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -82.330634+0.005633j
[2025-09-10 00:51:19] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -82.332513-0.003774j
[2025-09-10 00:51:54] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -82.290623+0.002882j
[2025-09-10 00:52:29] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -82.326925-0.004104j
[2025-09-10 00:53:04] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -82.353312-0.000009j
[2025-09-10 00:53:39] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -82.427991-0.000646j
[2025-09-10 00:54:13] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -82.249563+0.001668j
[2025-09-10 00:54:48] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -82.386950-0.001828j
[2025-09-10 00:55:23] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -82.563998+0.002778j
[2025-09-10 00:55:58] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -82.573622+0.002042j
[2025-09-10 00:56:33] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -82.582452+0.002120j
[2025-09-10 00:57:08] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -82.532110-0.003953j
[2025-09-10 00:57:42] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -82.512436+0.000977j
[2025-09-10 00:58:17] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -82.323356+0.000639j
[2025-09-10 00:58:51] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -82.362483+0.004957j
[2025-09-10 00:58:51] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-10 00:59:26] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -82.293173-0.005726j
[2025-09-10 01:00:00] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -82.379564-0.001623j
[2025-09-10 01:00:35] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -82.368303+0.003251j
[2025-09-10 01:01:10] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -82.500015+0.004035j
[2025-09-10 01:01:45] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -82.436580+0.005110j
[2025-09-10 01:02:20] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -82.380051+0.002411j
[2025-09-10 01:02:55] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -82.421499-0.004125j
[2025-09-10 01:03:30] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -82.433220+0.001368j
[2025-09-10 01:04:04] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -82.320505+0.001097j
[2025-09-10 01:04:39] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -82.388303-0.001112j
[2025-09-10 01:05:14] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -82.411715-0.002945j
[2025-09-10 01:05:49] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -82.315982+0.002375j
[2025-09-10 01:06:24] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -82.258207-0.001592j
[2025-09-10 01:06:59] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -82.296516+0.006960j
[2025-09-10 01:07:33] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -82.271046+0.002791j
[2025-09-10 01:08:08] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -82.257435+0.000691j
[2025-09-10 01:08:43] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -82.444029+0.002136j
[2025-09-10 01:09:18] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -82.389424+0.005057j
[2025-09-10 01:09:53] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -82.357047-0.000540j
[2025-09-10 01:10:28] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -82.280446-0.002254j
[2025-09-10 01:11:02] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -82.489633-0.001429j
[2025-09-10 01:11:37] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -82.534969+0.002937j
[2025-09-10 01:12:12] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -82.695996-0.001710j
[2025-09-10 01:12:47] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -82.496587-0.001932j
[2025-09-10 01:13:22] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -82.519378-0.004843j
[2025-09-10 01:13:57] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -82.590440-0.001593j
[2025-09-10 01:14:31] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -82.565912+0.002577j
[2025-09-10 01:15:06] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -82.413683+0.000671j
[2025-09-10 01:15:41] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -82.242145-0.002929j
[2025-09-10 01:16:16] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -82.487571-0.002797j
[2025-09-10 01:16:16] RESTART #2 | Period: 600
[2025-09-10 01:16:51] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -82.335424-0.001298j
[2025-09-10 01:17:25] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -82.291005+0.002590j
[2025-09-10 01:18:00] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -82.479571+0.000485j
[2025-09-10 01:18:35] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -82.308394-0.001559j
[2025-09-10 01:19:10] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -82.268849-0.000301j
[2025-09-10 01:19:45] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -82.186995-0.005168j
[2025-09-10 01:20:20] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -82.256304-0.002401j
[2025-09-10 01:20:54] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -82.166968+0.001840j
[2025-09-10 01:21:29] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -82.229168+0.003057j
[2025-09-10 01:22:04] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -82.279960-0.001435j
[2025-09-10 01:22:39] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -82.285045+0.000421j
[2025-09-10 01:23:14] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -82.229965-0.004325j
[2025-09-10 01:23:48] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -82.304350-0.001615j
[2025-09-10 01:24:23] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -82.279613+0.000735j
[2025-09-10 01:24:58] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -82.338772+0.001557j
[2025-09-10 01:25:33] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -82.199416-0.001600j
[2025-09-10 01:26:08] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -82.327018+0.000248j
[2025-09-10 01:26:43] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -82.186002+0.003833j
[2025-09-10 01:27:17] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -82.186949-0.000438j
[2025-09-10 01:27:52] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -82.317148-0.000915j
[2025-09-10 01:28:27] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -82.282191-0.003429j
[2025-09-10 01:29:02] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -82.391452-0.002840j
[2025-09-10 01:29:37] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -82.159684-0.002126j
[2025-09-10 01:30:11] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -82.220390-0.001079j
[2025-09-10 01:30:46] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -82.252590+0.001771j
[2025-09-10 01:31:21] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -82.213124-0.002471j
[2025-09-10 01:31:56] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -82.213473+0.000881j
[2025-09-10 01:32:31] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -82.252380-0.000076j
[2025-09-10 01:33:06] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -82.343830-0.002547j
[2025-09-10 01:33:40] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -82.407100-0.003077j
[2025-09-10 01:34:15] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -82.410325+0.000914j
[2025-09-10 01:34:50] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -82.481615+0.000168j
[2025-09-10 01:35:25] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -82.296856-0.001037j
[2025-09-10 01:36:00] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -82.210257-0.000569j
[2025-09-10 01:36:35] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -82.340888-0.003716j
[2025-09-10 01:37:09] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -82.449305-0.000390j
[2025-09-10 01:37:44] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -82.327176+0.001364j
[2025-09-10 01:38:19] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -82.339854-0.002081j
[2025-09-10 01:38:54] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -82.272505-0.001646j
[2025-09-10 01:39:29] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -82.268973-0.001075j
[2025-09-10 01:40:04] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -82.308252-0.000845j
[2025-09-10 01:40:38] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -82.353573+0.002404j
[2025-09-10 01:41:13] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -82.409124+0.001608j
[2025-09-10 01:41:47] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -82.529372+0.003975j
[2025-09-10 01:42:22] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -82.350869+0.000192j
[2025-09-10 01:42:56] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -82.443629-0.000084j
[2025-09-10 01:43:31] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -82.352274+0.004768j
[2025-09-10 01:44:06] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -82.488122+0.001183j
[2025-09-10 01:44:41] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -82.496740+0.001150j
[2025-09-10 01:45:16] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -82.511707+0.003466j
[2025-09-10 01:45:51] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -82.504564-0.002239j
[2025-09-10 01:46:25] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -82.481828+0.000829j
[2025-09-10 01:47:00] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -82.373476+0.000144j
[2025-09-10 01:47:35] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -82.476315+0.002041j
[2025-09-10 01:48:10] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -82.539411+0.006371j
[2025-09-10 01:48:45] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -82.591485-0.000705j
[2025-09-10 01:49:20] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -82.611741+0.003778j
[2025-09-10 01:49:54] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -82.715561-0.001111j
[2025-09-10 01:50:29] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -82.523612+0.001075j
[2025-09-10 01:51:04] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -82.446141-0.000433j
[2025-09-10 01:51:39] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -82.564850-0.001368j
[2025-09-10 01:52:14] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -82.437014+0.001175j
[2025-09-10 01:52:49] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -82.387362-0.003263j
[2025-09-10 01:53:23] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -82.353881-0.002341j
[2025-09-10 01:53:58] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -82.279972-0.007016j
[2025-09-10 01:54:33] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -82.359974-0.000226j
[2025-09-10 01:55:08] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -82.478600+0.000418j
[2025-09-10 01:55:43] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -82.378653+0.000962j
[2025-09-10 01:56:17] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -82.334858+0.005951j
[2025-09-10 01:56:52] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -82.356149+0.001660j
[2025-09-10 01:57:27] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -82.388387+0.000572j
[2025-09-10 01:58:02] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -82.353519+0.003698j
[2025-09-10 01:58:37] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -82.393157+0.006149j
[2025-09-10 01:59:11] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -82.411337-0.003074j
[2025-09-10 01:59:46] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -82.408433+0.000647j
[2025-09-10 01:59:46] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-10 02:00:21] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -82.215041-0.001063j
[2025-09-10 02:00:56] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -82.330594-0.000436j
[2025-09-10 02:01:31] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -82.458428-0.003262j
[2025-09-10 02:02:06] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -82.404314-0.001664j
[2025-09-10 02:02:40] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -82.532406+0.003164j
[2025-09-10 02:03:15] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -82.377049+0.000903j
[2025-09-10 02:03:50] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -82.375992+0.002279j
[2025-09-10 02:04:25] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -82.320705-0.005302j
[2025-09-10 02:05:00] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -82.223734-0.001755j
[2025-09-10 02:05:35] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -82.242132-0.002925j
[2025-09-10 02:06:10] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -82.290775-0.000904j
[2025-09-10 02:06:44] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -82.276091-0.003657j
[2025-09-10 02:07:19] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -82.374120+0.004395j
[2025-09-10 02:07:54] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -82.366486+0.000561j
[2025-09-10 02:08:30] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -82.326047+0.002326j
[2025-09-10 02:09:06] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -82.296654+0.002527j
[2025-09-10 02:09:42] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -82.430763-0.005209j
[2025-09-10 02:10:17] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -82.381818+0.002200j
[2025-09-10 02:10:52] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -82.450550-0.003059j
[2025-09-10 02:11:27] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -82.364397-0.004560j
[2025-09-10 02:12:02] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -82.314029+0.000764j
[2025-09-10 02:12:36] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -82.372018+0.003978j
[2025-09-10 02:13:11] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -82.358764-0.004737j
[2025-09-10 02:13:46] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -82.289185-0.001916j
[2025-09-10 02:14:21] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -82.273680+0.000746j
[2025-09-10 02:14:56] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -82.253787+0.007853j
[2025-09-10 02:15:31] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -82.397610-0.001634j
[2025-09-10 02:16:06] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -82.449374-0.001199j
[2025-09-10 02:16:41] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -82.411127-0.001674j
[2025-09-10 02:17:16] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -82.235721-0.003162j
[2025-09-10 02:17:51] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -82.232175-0.004783j
[2025-09-10 02:18:26] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -82.334491+0.000184j
[2025-09-10 02:19:01] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -82.261643+0.001682j
[2025-09-10 02:19:36] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -82.379482+0.003550j
[2025-09-10 02:20:11] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -82.454902-0.004625j
[2025-09-10 02:20:45] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -82.422185+0.002452j
[2025-09-10 02:21:21] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -82.411711-0.000194j
[2025-09-10 02:21:55] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -82.432267+0.001935j
[2025-09-10 02:22:30] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -82.457705-0.000711j
[2025-09-10 02:23:05] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -82.446709+0.000699j
[2025-09-10 02:23:40] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -82.505802+0.000393j
[2025-09-10 02:24:15] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -82.465524-0.000723j
[2025-09-10 02:24:50] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -82.409785+0.000821j
[2025-09-10 02:25:25] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -82.504439+0.000837j
[2025-09-10 02:26:00] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -82.411521+0.000492j
[2025-09-10 02:26:35] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -82.389986+0.002273j
[2025-09-10 02:27:10] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -82.331998-0.005462j
[2025-09-10 02:27:44] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -82.306858+0.000403j
[2025-09-10 02:28:19] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -82.389061-0.003597j
[2025-09-10 02:28:54] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -82.390690+0.004430j
[2025-09-10 02:29:29] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -82.348114+0.000439j
[2025-09-10 02:30:04] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -82.329980+0.004503j
[2025-09-10 02:30:39] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -82.407245+0.002592j
[2025-09-10 02:31:14] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -82.327385+0.003659j
[2025-09-10 02:31:49] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -82.286441-0.000707j
[2025-09-10 02:32:24] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -82.369828+0.000024j
[2025-09-10 02:32:59] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -82.472428-0.001440j
[2025-09-10 02:33:34] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -82.395456-0.005459j
[2025-09-10 02:34:08] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -82.305993-0.000041j
[2025-09-10 02:34:43] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -82.325470-0.000603j
[2025-09-10 02:35:18] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -82.423691+0.000620j
[2025-09-10 02:35:53] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -82.398356+0.005645j
[2025-09-10 02:36:28] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -82.343283+0.001850j
[2025-09-10 02:37:03] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -82.405379+0.003517j
[2025-09-10 02:37:38] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -82.449327-0.004398j
[2025-09-10 02:38:13] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -82.445980-0.005094j
[2025-09-10 02:38:48] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -82.363186+0.002289j
[2025-09-10 02:39:23] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -82.411693-0.004732j
[2025-09-10 02:39:57] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -82.280243+0.001811j
[2025-09-10 02:40:32] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -82.246148-0.003422j
[2025-09-10 02:41:07] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -82.419806+0.001677j
[2025-09-10 02:41:42] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -82.390659-0.001565j
[2025-09-10 02:42:17] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -82.288644-0.001355j
[2025-09-10 02:42:52] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -82.233542-0.000258j
[2025-09-10 02:43:27] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -82.048078+0.002384j
[2025-09-10 02:44:02] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -82.067403+0.002005j
[2025-09-10 02:44:37] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -82.302885-0.005530j
[2025-09-10 02:45:12] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -82.334902+0.001089j
[2025-09-10 02:45:47] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -82.439549+0.000606j
[2025-09-10 02:46:21] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -82.365593-0.004660j
[2025-09-10 02:46:56] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -82.374772-0.000562j
[2025-09-10 02:47:31] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -82.285210+0.008848j
[2025-09-10 02:48:06] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -82.348433+0.000550j
[2025-09-10 02:48:41] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -82.377826+0.004702j
[2025-09-10 02:49:16] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -82.380124+0.002264j
[2025-09-10 02:49:51] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -82.289752+0.003126j
[2025-09-10 02:50:25] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -82.308476-0.001060j
[2025-09-10 02:51:00] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -82.309415+0.002773j
[2025-09-10 02:51:35] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -82.397127+0.000602j
[2025-09-10 02:52:10] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -82.387714-0.002712j
[2025-09-10 02:52:45] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -82.419821-0.000178j
[2025-09-10 02:53:20] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -82.336178-0.003894j
[2025-09-10 02:53:54] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -82.372485-0.001029j
[2025-09-10 02:54:29] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -82.465976+0.004554j
[2025-09-10 02:55:04] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -82.325852+0.000548j
[2025-09-10 02:55:39] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -82.342136+0.001682j
[2025-09-10 02:56:14] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -82.288387+0.000200j
[2025-09-10 02:56:48] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -82.342866-0.002310j
[2025-09-10 02:57:23] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -82.273263+0.007221j
[2025-09-10 02:57:58] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -82.285581+0.002812j
[2025-09-10 02:58:33] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -82.300722-0.000347j
[2025-09-10 02:59:08] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -82.345117+0.003590j
[2025-09-10 02:59:42] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -82.230615+0.002410j
[2025-09-10 03:00:17] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -82.381773+0.002829j
[2025-09-10 03:00:52] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -82.251134+0.000638j
[2025-09-10 03:00:52] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-10 03:01:27] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -82.367067-0.000376j
[2025-09-10 03:02:01] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -82.293212+0.005056j
[2025-09-10 03:02:36] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -82.442413+0.006575j
[2025-09-10 03:03:11] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -82.291156-0.004363j
[2025-09-10 03:03:46] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -82.319383+0.003995j
[2025-09-10 03:04:21] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -82.291539+0.000989j
[2025-09-10 03:04:55] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -82.248309-0.003428j
[2025-09-10 03:05:30] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -82.430640+0.000810j
[2025-09-10 03:06:05] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -82.440550+0.001263j
[2025-09-10 03:06:40] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -82.393749+0.000734j
[2025-09-10 03:07:15] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -82.406877+0.000459j
[2025-09-10 03:07:50] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -82.600787-0.009391j
[2025-09-10 03:08:25] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -82.487819-0.002750j
[2025-09-10 03:08:59] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -82.579571-0.002820j
[2025-09-10 03:09:34] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -82.479924+0.001252j
[2025-09-10 03:10:09] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -82.600079-0.005309j
[2025-09-10 03:10:44] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -82.441162-0.002308j
[2025-09-10 03:11:18] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -82.420816+0.001160j
[2025-09-10 03:11:53] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -82.558825-0.001145j
[2025-09-10 03:12:28] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -82.470386-0.000020j
[2025-09-10 03:13:03] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -82.547277+0.003766j
[2025-09-10 03:13:37] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -82.516121-0.008781j
[2025-09-10 03:14:12] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -82.552054-0.002275j
[2025-09-10 03:14:46] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -82.462819-0.006319j
[2025-09-10 03:15:21] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -82.376771+0.004403j
[2025-09-10 03:15:56] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -82.525586+0.002774j
[2025-09-10 03:16:31] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -82.423964+0.003890j
[2025-09-10 03:17:05] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -82.411710-0.001970j
[2025-09-10 03:17:40] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -82.331847-0.005133j
[2025-09-10 03:18:15] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -82.351997+0.004589j
[2025-09-10 03:18:50] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -82.383509+0.000443j
[2025-09-10 03:19:25] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -82.357731+0.003887j
[2025-09-10 03:20:00] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -82.494652+0.004182j
[2025-09-10 03:20:34] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -82.517803+0.007782j
[2025-09-10 03:21:09] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -82.628604+0.004650j
[2025-09-10 03:21:44] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -82.488998-0.001733j
[2025-09-10 03:22:19] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -82.461625+0.001119j
[2025-09-10 03:22:54] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -82.523560-0.000067j
[2025-09-10 03:23:29] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -82.591769-0.001698j
[2025-09-10 03:24:03] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -82.458300-0.005355j
[2025-09-10 03:24:38] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -82.497111+0.002129j
[2025-09-10 03:25:13] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -82.542512+0.005759j
[2025-09-10 03:25:48] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -82.547641-0.004481j
[2025-09-10 03:26:23] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -82.568632-0.000078j
[2025-09-10 03:26:58] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -82.550161-0.000704j
[2025-09-10 03:27:32] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -82.441079+0.001599j
[2025-09-10 03:28:07] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -82.487858-0.000740j
[2025-09-10 03:28:42] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -82.555724-0.001215j
[2025-09-10 03:29:17] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -82.458907-0.001770j
[2025-09-10 03:29:51] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -82.471895+0.001034j
[2025-09-10 03:30:26] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -82.465896-0.001885j
[2025-09-10 03:31:01] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -82.504004-0.006015j
[2025-09-10 03:31:36] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -82.437040-0.000313j
[2025-09-10 03:32:11] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -82.339185-0.003236j
[2025-09-10 03:32:45] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -82.518316+0.000654j
[2025-09-10 03:33:20] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -82.386288+0.001871j
[2025-09-10 03:33:55] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -82.402361+0.000079j
[2025-09-10 03:34:30] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -82.322736+0.001092j
[2025-09-10 03:35:05] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -82.485775-0.000253j
[2025-09-10 03:35:39] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -82.387199-0.000503j
[2025-09-10 03:36:14] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -82.510325+0.010577j
[2025-09-10 03:36:48] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -82.618738+0.001455j
[2025-09-10 03:37:23] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -82.515282+0.000140j
[2025-09-10 03:37:58] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -82.412859-0.002280j
[2025-09-10 03:38:33] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -82.437894-0.000920j
[2025-09-10 03:39:07] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -82.310675+0.008069j
[2025-09-10 03:39:42] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -82.241813-0.004474j
[2025-09-10 03:40:17] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -82.259761+0.001681j
[2025-09-10 03:40:52] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -82.323149+0.001033j
[2025-09-10 03:41:25] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -82.443439+0.002672j
[2025-09-10 03:42:05] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -82.489955-0.003215j
[2025-09-10 03:42:40] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -82.399401+0.003657j
[2025-09-10 03:43:15] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -82.603921+0.003806j
[2025-09-10 03:43:49] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -82.529517-0.002909j
[2025-09-10 03:44:24] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -82.554949+0.003283j
[2025-09-10 03:44:59] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -82.441461-0.004204j
[2025-09-10 03:45:34] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -82.480054-0.001507j
[2025-09-10 03:46:09] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -82.479630+0.001921j
[2025-09-10 03:46:44] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -82.473677-0.000712j
[2025-09-10 03:47:18] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -82.552367+0.002260j
[2025-09-10 03:47:53] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -82.559928-0.005642j
[2025-09-10 03:48:28] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -82.542496-0.003332j
[2025-09-10 03:49:03] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -82.457227-0.001764j
[2025-09-10 03:49:38] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -82.460530+0.003279j
[2025-09-10 03:50:13] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -82.491967-0.000794j
[2025-09-10 03:50:47] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -82.404654-0.001756j
[2025-09-10 03:51:22] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -82.436384-0.005247j
[2025-09-10 03:51:57] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -82.513506+0.006824j
[2025-09-10 03:52:32] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -82.268099-0.002615j
[2025-09-10 03:53:07] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -82.313618+0.001403j
[2025-09-10 03:53:42] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -82.419428-0.001593j
[2025-09-10 03:54:16] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -82.432549+0.005928j
[2025-09-10 03:54:51] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -82.424845-0.000747j
[2025-09-10 03:55:26] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -82.497084+0.001246j
[2025-09-10 03:56:01] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -82.518939-0.001093j
[2025-09-10 03:56:36] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -82.500833-0.006167j
[2025-09-10 03:57:11] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -82.523661+0.000607j
[2025-09-10 03:57:46] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -82.478078-0.001344j
[2025-09-10 03:58:20] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -82.486154-0.001403j
[2025-09-10 03:58:55] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -82.352265+0.000094j
[2025-09-10 03:59:30] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -82.474261-0.001340j
[2025-09-10 04:00:05] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -82.415822-0.000701j
[2025-09-10 04:00:40] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -82.415611-0.005794j
[2025-09-10 04:01:14] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -82.367686+0.000683j
[2025-09-10 04:01:49] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -82.421281+0.000200j
[2025-09-10 04:01:49] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-10 04:02:24] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -82.368015+0.002765j
[2025-09-10 04:02:59] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -82.452317-0.000847j
[2025-09-10 04:03:34] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -82.544957+0.001653j
[2025-09-10 04:04:08] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -82.636526-0.000568j
[2025-09-10 04:04:43] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -82.540388-0.005560j
[2025-09-10 04:05:18] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -82.571314-0.004783j
[2025-09-10 04:05:53] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -82.512502+0.004590j
[2025-09-10 04:06:28] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -82.343625+0.000738j
[2025-09-10 04:07:03] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -82.439747-0.001609j
[2025-09-10 04:07:37] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -82.410672+0.003533j
[2025-09-10 04:08:12] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -82.269696+0.002764j
[2025-09-10 04:08:47] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -82.279073-0.000352j
[2025-09-10 04:09:22] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -82.217791+0.003230j
[2025-09-10 04:09:57] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -82.382703+0.001795j
[2025-09-10 04:10:32] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -82.274052-0.006517j
[2025-09-10 04:11:06] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -82.317813-0.002053j
[2025-09-10 04:11:41] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -82.280107+0.001779j
[2025-09-10 04:12:16] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -82.362808+0.001282j
[2025-09-10 04:12:51] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -82.261292+0.000226j
[2025-09-10 04:13:26] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -82.259010-0.001434j
[2025-09-10 04:14:01] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -82.196518+0.001422j
[2025-09-10 04:14:36] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -82.265065-0.000066j
[2025-09-10 04:15:10] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -82.190309-0.006252j
[2025-09-10 04:15:45] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -82.251155-0.001757j
[2025-09-10 04:16:20] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -82.143394+0.001675j
[2025-09-10 04:16:55] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -82.256227-0.003177j
[2025-09-10 04:17:30] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -82.391532-0.003974j
[2025-09-10 04:18:05] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -82.330888+0.000656j
[2025-09-10 04:18:39] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -82.497652+0.002872j
[2025-09-10 04:19:14] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -82.501564+0.001723j
[2025-09-10 04:19:49] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -82.427205+0.003498j
[2025-09-10 04:20:24] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -82.470199+0.001155j
[2025-09-10 04:20:59] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -82.559025-0.000369j
[2025-09-10 04:21:34] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -82.503242-0.003319j
[2025-09-10 04:22:08] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -82.539294+0.004396j
[2025-09-10 04:22:43] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -82.527643-0.003629j
[2025-09-10 04:23:18] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -82.544051+0.000599j
[2025-09-10 04:23:53] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -82.482619+0.001764j
[2025-09-10 04:24:28] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -82.607718+0.001357j
[2025-09-10 04:25:02] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -82.371421+0.004580j
[2025-09-10 04:25:37] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -82.445416+0.003568j
[2025-09-10 04:26:12] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -82.440465-0.003892j
[2025-09-10 04:26:47] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -82.607379-0.002146j
[2025-09-10 04:27:22] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -82.460592+0.001487j
[2025-09-10 04:27:57] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -82.488089+0.000084j
[2025-09-10 04:28:31] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -82.400978+0.000133j
[2025-09-10 04:29:06] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -82.328781-0.007511j
[2025-09-10 04:29:41] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -82.233651-0.001766j
[2025-09-10 04:30:16] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -82.341369-0.000461j
[2025-09-10 04:30:51] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -82.483957+0.000940j
[2025-09-10 04:31:26] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -82.364491+0.002387j
[2025-09-10 04:32:00] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -82.443479-0.002936j
[2025-09-10 04:32:35] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -82.284049-0.002487j
[2025-09-10 04:33:10] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -82.557505+0.000760j
[2025-09-10 04:33:45] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -82.486632-0.004099j
[2025-09-10 04:34:20] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -82.447053+0.002825j
[2025-09-10 04:34:55] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -82.406064-0.000854j
[2025-09-10 04:35:29] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -82.349456+0.003675j
[2025-09-10 04:36:04] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -82.487431-0.003494j
[2025-09-10 04:36:39] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -82.581287+0.000191j
[2025-09-10 04:37:14] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -82.410410+0.001478j
[2025-09-10 04:37:49] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -82.461852+0.001133j
[2025-09-10 04:38:23] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -82.378035+0.001334j
[2025-09-10 04:38:58] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -82.457099+0.003349j
[2025-09-10 04:39:33] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -82.405642+0.003807j
[2025-09-10 04:40:07] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -82.413319+0.001818j
[2025-09-10 04:40:42] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -82.408742-0.001033j
[2025-09-10 04:41:16] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -82.456461+0.005148j
[2025-09-10 04:41:51] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -82.407124+0.002324j
[2025-09-10 04:42:26] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -82.550716+0.004218j
[2025-09-10 04:43:01] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -82.410674+0.002083j
[2025-09-10 04:43:36] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -82.429350+0.001499j
[2025-09-10 04:44:10] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -82.495462+0.000064j
[2025-09-10 04:44:45] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -82.579794-0.005050j
[2025-09-10 04:45:20] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -82.425241-0.004230j
[2025-09-10 04:45:55] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -82.484572+0.000205j
[2025-09-10 04:46:30] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -82.401859-0.002386j
[2025-09-10 04:47:05] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -82.514742+0.001710j
[2025-09-10 04:47:39] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -82.332742-0.004633j
[2025-09-10 04:48:14] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -82.324893+0.004085j
[2025-09-10 04:48:49] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -82.347350-0.005310j
[2025-09-10 04:49:24] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -82.203074-0.001464j
[2025-09-10 04:49:59] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -82.303254+0.002262j
[2025-09-10 04:50:34] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -82.289217-0.004069j
[2025-09-10 04:51:08] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -82.381791+0.000949j
[2025-09-10 04:51:43] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -82.285515+0.002205j
[2025-09-10 04:52:18] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -82.382922-0.001600j
[2025-09-10 04:52:53] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -82.292284+0.000099j
[2025-09-10 04:53:28] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -82.489922-0.002419j
[2025-09-10 04:54:03] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -82.404831+0.001083j
[2025-09-10 04:54:37] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -82.496311-0.000956j
[2025-09-10 04:55:12] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -82.445492-0.000630j
[2025-09-10 04:55:47] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -82.430956-0.002765j
[2025-09-10 04:56:22] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -82.483000+0.002214j
[2025-09-10 04:56:57] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -82.427447+0.000130j
[2025-09-10 04:57:32] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -82.412584+0.003700j
[2025-09-10 04:58:06] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -82.451395-0.002580j
[2025-09-10 04:58:41] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -82.296729-0.003535j
[2025-09-10 04:59:16] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -82.490291+0.003935j
[2025-09-10 04:59:51] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -82.500476+0.001989j
[2025-09-10 05:00:26] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -82.530208-0.000205j
[2025-09-10 05:01:00] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -82.551189+0.001391j
[2025-09-10 05:01:35] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -82.468151-0.001750j
[2025-09-10 05:02:10] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -82.426221+0.003599j
[2025-09-10 05:02:44] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -82.287615-0.000808j
[2025-09-10 05:02:44] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-10 05:03:19] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -82.453538+0.001871j
[2025-09-10 05:03:53] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -82.378253-0.003416j
[2025-09-10 05:04:28] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -82.400267+0.004813j
[2025-09-10 05:05:03] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -82.444944-0.007214j
[2025-09-10 05:05:37] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -82.442758+0.001414j
[2025-09-10 05:06:12] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -82.556679+0.000472j
[2025-09-10 05:06:47] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -82.368885+0.000368j
[2025-09-10 05:07:22] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -82.501380-0.000409j
[2025-09-10 05:07:57] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -82.388492-0.000343j
[2025-09-10 05:08:31] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -82.214727+0.003747j
[2025-09-10 05:09:06] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -82.156804-0.003821j
[2025-09-10 05:09:41] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -82.358675-0.003563j
[2025-09-10 05:10:16] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -82.370857-0.002287j
[2025-09-10 05:10:51] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -82.551512-0.001209j
[2025-09-10 05:11:26] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -82.523139-0.004430j
[2025-09-10 05:12:01] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -82.462243+0.000080j
[2025-09-10 05:12:36] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -82.621423-0.000288j
[2025-09-10 05:13:10] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -82.537873+0.000769j
[2025-09-10 05:13:45] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -82.411335-0.002252j
[2025-09-10 05:14:20] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -82.475552+0.002783j
[2025-09-10 05:14:55] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -82.365382-0.000265j
[2025-09-10 05:15:30] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -82.380245-0.002179j
[2025-09-10 05:16:04] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -82.400401+0.001810j
[2025-09-10 05:16:39] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -82.206040-0.002510j
[2025-09-10 05:17:14] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -82.190676-0.001566j
[2025-09-10 05:17:49] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -82.374168-0.002008j
[2025-09-10 05:18:24] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -82.453967-0.000816j
[2025-09-10 05:18:59] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -82.565205-0.002854j
[2025-09-10 05:19:37] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -82.451886+0.001578j
[2025-09-10 05:20:13] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -82.563300+0.000772j
[2025-09-10 05:20:48] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -82.370730+0.001799j
[2025-09-10 05:21:23] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -82.516483-0.001513j
[2025-09-10 05:21:58] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -82.536419-0.001151j
[2025-09-10 05:22:33] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -82.561890+0.003305j
[2025-09-10 05:23:08] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -82.602217+0.003910j
[2025-09-10 05:23:43] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -82.423208+0.007812j
[2025-09-10 05:24:17] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -82.425200+0.001146j
[2025-09-10 05:24:52] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -82.203120-0.000351j
[2025-09-10 05:25:27] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -82.183565+0.000035j
[2025-09-10 05:26:02] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -82.257321+0.001257j
[2025-09-10 05:26:37] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -82.347138+0.000968j
[2025-09-10 05:27:12] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -82.324276-0.000381j
[2025-09-10 05:27:47] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -82.491852+0.001391j
[2025-09-10 05:28:21] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -82.505336+0.000087j
[2025-09-10 05:28:56] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -82.463818+0.005197j
[2025-09-10 05:29:31] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -82.428957+0.001822j
[2025-09-10 05:30:06] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -82.480886-0.001506j
[2025-09-10 05:30:41] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -82.394631-0.003408j
[2025-09-10 05:31:15] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -82.438485+0.001803j
[2025-09-10 05:31:50] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -82.444339+0.000932j
[2025-09-10 05:32:24] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -82.276702-0.000345j
[2025-09-10 05:32:59] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -82.431555+0.004734j
[2025-09-10 05:33:33] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -82.482513+0.004052j
[2025-09-10 05:34:08] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -82.360184+0.000814j
[2025-09-10 05:34:43] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -82.452233-0.002318j
[2025-09-10 05:35:18] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -82.461958-0.002745j
[2025-09-10 05:35:52] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -82.497407-0.000091j
[2025-09-10 05:36:27] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -82.522499+0.002274j
[2025-09-10 05:37:01] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -82.612568+0.000060j
[2025-09-10 05:37:36] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -82.677601-0.001832j
[2025-09-10 05:38:11] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -82.603564+0.001566j
[2025-09-10 05:38:45] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -82.507054+0.000394j
[2025-09-10 05:39:20] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -82.447980-0.000467j
[2025-09-10 05:39:55] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -82.406430+0.000351j
[2025-09-10 05:40:30] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -82.400368+0.001351j
[2025-09-10 05:41:05] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -82.423525+0.004932j
[2025-09-10 05:41:40] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -82.455575-0.004153j
[2025-09-10 05:42:14] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -82.439546+0.003783j
[2025-09-10 05:42:49] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -82.534700+0.000173j
[2025-09-10 05:43:24] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -82.481126+0.003047j
[2025-09-10 05:43:59] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -82.322302-0.000490j
[2025-09-10 05:44:34] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -82.396728+0.001102j
[2025-09-10 05:45:09] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -82.311309-0.025307j
[2025-09-10 05:45:43] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -82.318393-0.002353j
[2025-09-10 05:46:18] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -82.206828-0.002567j
[2025-09-10 05:46:53] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -82.260118+0.002885j
[2025-09-10 05:47:28] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -82.262955+0.003129j
[2025-09-10 05:48:03] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -82.236661-0.000779j
[2025-09-10 05:48:37] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -82.291667-0.000598j
[2025-09-10 05:49:12] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -82.260656+0.002208j
[2025-09-10 05:49:47] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -82.321040-0.000294j
[2025-09-10 05:50:22] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -82.278737-0.002829j
[2025-09-10 05:50:57] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -82.313600+0.000420j
[2025-09-10 05:51:32] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -82.461538-0.000629j
[2025-09-10 05:52:06] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -82.405565+0.003143j
[2025-09-10 05:52:41] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -82.343971+0.000290j
[2025-09-10 05:53:16] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -82.405415-0.000386j
[2025-09-10 05:53:51] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -82.390234-0.003849j
[2025-09-10 05:54:26] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -82.407303-0.002103j
[2025-09-10 05:55:00] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -82.295091-0.000851j
[2025-09-10 05:55:35] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -82.422141+0.005190j
[2025-09-10 05:56:10] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -82.310720-0.000332j
[2025-09-10 05:56:45] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -82.209344+0.000330j
[2025-09-10 05:57:20] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -82.395261+0.002836j
[2025-09-10 05:57:55] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -82.368549+0.001573j
[2025-09-10 05:58:30] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -82.293615+0.001196j
[2025-09-10 05:59:04] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -82.240862+0.000838j
[2025-09-10 05:59:39] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -82.356256-0.001359j
[2025-09-10 06:00:14] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -82.324342+0.002601j
[2025-09-10 06:00:49] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -82.447927-0.000274j
[2025-09-10 06:01:24] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -82.261768-0.007755j
[2025-09-10 06:01:59] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -82.320093+0.001691j
[2025-09-10 06:02:33] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -82.438843-0.000160j
[2025-09-10 06:03:08] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -82.375974+0.002725j
[2025-09-10 06:03:43] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -82.379670+0.002336j
[2025-09-10 06:03:43] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-10 06:04:18] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -82.441087-0.001583j
[2025-09-10 06:04:53] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -82.456421+0.001422j
[2025-09-10 06:05:28] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -82.304127+0.005132j
[2025-09-10 06:06:02] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -82.292661-0.001596j
[2025-09-10 06:06:37] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -82.492661-0.003626j
[2025-09-10 06:07:12] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -82.435444-0.003677j
[2025-09-10 06:07:47] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -82.387075-0.003622j
[2025-09-10 06:08:22] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -82.374794-0.000346j
[2025-09-10 06:08:56] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -82.340996-0.002318j
[2025-09-10 06:09:31] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -82.297936-0.005371j
[2025-09-10 06:10:06] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -82.260948+0.000603j
[2025-09-10 06:10:41] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -82.341811+0.002789j
[2025-09-10 06:11:16] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -82.288385+0.000651j
[2025-09-10 06:11:51] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -82.336931+0.002769j
[2025-09-10 06:12:25] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -82.427081+0.000974j
[2025-09-10 06:13:00] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -82.416070+0.000944j
[2025-09-10 06:13:35] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -82.533307+0.000217j
[2025-09-10 06:14:10] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -82.432631+0.002579j
[2025-09-10 06:14:45] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -82.433803+0.006614j
[2025-09-10 06:15:19] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -82.478166-0.004908j
[2025-09-10 06:15:54] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -82.632866-0.003831j
[2025-09-10 06:16:29] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -82.548638-0.002919j
[2025-09-10 06:17:04] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -82.464830-0.005126j
[2025-09-10 06:17:39] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -82.471927-0.003946j
[2025-09-10 06:18:13] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -82.518596-0.000711j
[2025-09-10 06:18:48] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -82.370684+0.000682j
[2025-09-10 06:19:23] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -82.471283+0.000732j
[2025-09-10 06:19:58] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -82.305032-0.001680j
[2025-09-10 06:20:33] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -82.252045-0.000820j
[2025-09-10 06:21:07] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -82.334060-0.000527j
[2025-09-10 06:21:42] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -82.279236+0.005838j
[2025-09-10 06:22:17] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -82.310726+0.002316j
[2025-09-10 06:22:52] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -82.276822-0.002490j
[2025-09-10 06:23:27] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -82.302520+0.003690j
[2025-09-10 06:24:01] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -82.230357-0.003437j
[2025-09-10 06:24:36] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -82.307360-0.000023j
[2025-09-10 06:25:11] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -82.271262-0.006260j
[2025-09-10 06:25:46] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -82.331427-0.001177j
[2025-09-10 06:26:21] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -82.252814-0.000798j
[2025-09-10 06:26:56] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -82.336766+0.000681j
[2025-09-10 06:27:30] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -82.388109+0.001160j
[2025-09-10 06:28:05] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -82.524708-0.000671j
[2025-09-10 06:28:40] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -82.411819+0.000255j
[2025-09-10 06:29:15] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -82.260016-0.001697j
[2025-09-10 06:29:50] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -82.296657-0.002759j
[2025-09-10 06:30:24] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -82.400442+0.001905j
[2025-09-10 06:30:59] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -82.454180+0.000178j
[2025-09-10 06:31:34] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -82.422614-0.000758j
[2025-09-10 06:32:09] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -82.433078-0.000329j
[2025-09-10 06:32:44] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -82.450261-0.000270j
[2025-09-10 06:33:18] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -82.424606-0.004864j
[2025-09-10 06:33:53] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -82.291554+0.001289j
[2025-09-10 06:34:28] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -82.429047+0.002602j
[2025-09-10 06:35:03] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -82.412769+0.006084j
[2025-09-10 06:35:38] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -82.424688+0.003785j
[2025-09-10 06:36:13] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -82.380109-0.002788j
[2025-09-10 06:36:47] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -82.447995+0.000732j
[2025-09-10 06:37:22] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -82.384742+0.001136j
[2025-09-10 06:37:57] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -82.523518+0.000548j
[2025-09-10 06:38:32] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -82.435563-0.001071j
[2025-09-10 06:39:07] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -82.327935-0.002509j
[2025-09-10 06:39:41] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -82.232945+0.000035j
[2025-09-10 06:40:16] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -82.338608+0.002086j
[2025-09-10 06:40:51] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -82.288996+0.003618j
[2025-09-10 06:41:26] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -82.200169+0.002873j
[2025-09-10 06:42:01] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -82.403178-0.000002j
[2025-09-10 06:42:35] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -82.500509-0.003640j
[2025-09-10 06:43:10] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -82.415733-0.007294j
[2025-09-10 06:43:45] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -82.474387-0.000479j
[2025-09-10 06:44:19] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -82.507267+0.000197j
[2025-09-10 06:44:54] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -82.433850-0.001242j
[2025-09-10 06:45:30] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -82.440977+0.001840j
[2025-09-10 06:46:04] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -82.352411+0.001814j
[2025-09-10 06:46:39] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -82.357739+0.000128j
[2025-09-10 06:47:14] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -82.432834-0.002014j
[2025-09-10 06:47:49] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -82.421726+0.000953j
[2025-09-10 06:48:24] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -82.250981+0.005035j
[2025-09-10 06:48:59] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -82.377114-0.003565j
[2025-09-10 06:49:34] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -82.544642-0.005108j
[2025-09-10 06:50:08] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -82.557480+0.000794j
[2025-09-10 06:50:43] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -82.503532+0.001527j
[2025-09-10 06:51:18] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -82.433670-0.001438j
[2025-09-10 06:51:53] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -82.578319-0.003686j
[2025-09-10 06:52:28] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -82.529672-0.000979j
[2025-09-10 06:53:02] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -82.448923-0.003967j
[2025-09-10 06:53:37] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -82.509875+0.001873j
[2025-09-10 06:54:12] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -82.454745+0.000056j
[2025-09-10 06:54:47] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -82.458559-0.000088j
[2025-09-10 06:55:22] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -82.481347+0.003537j
[2025-09-10 06:55:56] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -82.354939-0.002711j
[2025-09-10 06:56:32] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -82.416152-0.002104j
[2025-09-10 06:57:06] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -82.431963+0.002528j
[2025-09-10 06:57:41] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -82.394418-0.000557j
[2025-09-10 06:58:16] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -82.450145-0.001444j
[2025-09-10 06:58:51] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -82.403592-0.000546j
[2025-09-10 06:59:26] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -82.408775+0.000240j
[2025-09-10 07:00:01] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -82.386433-0.002427j
[2025-09-10 07:00:36] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -82.478457+0.000021j
[2025-09-10 07:01:11] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -82.406861+0.000109j
[2025-09-10 07:01:45] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -82.477391-0.002632j
[2025-09-10 07:02:20] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -82.433050+0.004007j
[2025-09-10 07:02:55] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -82.448515-0.002849j
[2025-09-10 07:03:30] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -82.441240-0.000610j
[2025-09-10 07:03:58] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -82.359417+0.004840j
[2025-09-10 07:04:21] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -82.477201-0.009097j
[2025-09-10 07:04:21] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-10 07:04:21] ✅ Training completed | Restarts: 2
[2025-09-10 07:04:21] ============================================================
[2025-09-10 07:04:21] Training completed | Runtime: 36733.2s
[2025-09-10 07:04:31] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-10 07:04:31] ============================================================
