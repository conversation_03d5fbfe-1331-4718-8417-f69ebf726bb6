[2025-09-12 17:02:10] 使用checkpoint文件: results/L=5/J2=0.05/J1=0.04/training/checkpoints/checkpoint_iter_000315.pkl
[2025-09-12 17:02:30] ✓ 从checkpoint加载参数: 315
[2025-09-12 17:02:30]   - 能量: -80.396192-0.003003j ± 0.110610
[2025-09-12 17:02:30] ================================================================================
[2025-09-12 17:02:30] 加载量子态: L=5, J2=0.05, J1=0.04, checkpoint=checkpoint_iter_000315
[2025-09-12 17:02:30] 使用采样数目: 1048576
[2025-09-12 17:02:30] 设置样本数为: 1048576
[2025-09-12 17:02:30] 开始生成共享样本集...
[2025-09-12 17:05:26] 样本生成完成,耗时: 175.315 秒
[2025-09-12 17:05:26] ================================================================================
[2025-09-12 17:05:26] 开始计算自旋结构因子...
[2025-09-12 17:05:26] 初始化操作符缓存...
[2025-09-12 17:05:26] 预构建所有自旋相关操作符...
[2025-09-12 17:05:26] 开始计算自旋相关函数...
[2025-09-12 17:05:34] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 8.632s
[2025-09-12 17:05:45] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 10.256s
[2025-09-12 17:05:51] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.077s
[2025-09-12 17:05:57] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.089s
[2025-09-12 17:06:03] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.093s
[2025-09-12 17:06:09] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.080s
[2025-09-12 17:06:15] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.093s
[2025-09-12 17:06:21] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.111s
[2025-09-12 17:06:27] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.098s
[2025-09-12 17:06:33] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.104s
[2025-09-12 17:06:39] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.094s
[2025-09-12 17:06:46] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.093s
[2025-09-12 17:06:52] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.095s
[2025-09-12 17:06:58] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.082s
[2025-09-12 17:07:04] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.083s
[2025-09-12 17:07:10] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.096s
[2025-09-12 17:07:16] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.101s
[2025-09-12 17:07:22] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.081s
[2025-09-12 17:07:28] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.096s
[2025-09-12 17:07:34] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.102s
[2025-09-12 17:07:40] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.096s
[2025-09-12 17:07:47] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.103s
[2025-09-12 17:07:53] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.222s
[2025-09-12 17:07:59] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.104s
[2025-09-12 17:08:05] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.106s
[2025-09-12 17:08:11] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.104s
[2025-09-12 17:08:17] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.101s
[2025-09-12 17:08:23] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.111s
[2025-09-12 17:08:29] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.106s
[2025-09-12 17:08:36] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.105s
[2025-09-12 17:08:42] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.107s
[2025-09-12 17:08:48] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.095s
[2025-09-12 17:08:54] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.083s
[2025-09-12 17:09:00] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.103s
[2025-09-12 17:09:06] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.097s
[2025-09-12 17:09:12] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.083s
[2025-09-12 17:09:18] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.096s
[2025-09-12 17:09:24] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.105s
[2025-09-12 17:09:30] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.095s
[2025-09-12 17:09:37] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.094s
[2025-09-12 17:09:43] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.094s
[2025-09-12 17:09:49] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.095s
[2025-09-12 17:09:55] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.081s
[2025-09-12 17:10:01] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.097s
[2025-09-12 17:10:07] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.096s
[2025-09-12 17:10:13] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.093s
[2025-09-12 17:10:19] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.085s
[2025-09-12 17:10:25] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.080s
[2025-09-12 17:10:31] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.095s
[2025-09-12 17:10:38] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.095s
[2025-09-12 17:10:44] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.084s
[2025-09-12 17:10:50] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.097s
[2025-09-12 17:10:56] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.095s
[2025-09-12 17:11:02] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.084s
[2025-09-12 17:11:08] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.097s
[2025-09-12 17:11:14] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.085s
[2025-09-12 17:11:20] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.095s
[2025-09-12 17:11:26] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.094s
[2025-09-12 17:11:32] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.102s
[2025-09-12 17:11:38] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.084s
[2025-09-12 17:11:45] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.101s
[2025-09-12 17:11:51] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.086s
[2025-09-12 17:11:57] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.110s
[2025-09-12 17:12:03] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.084s
[2025-09-12 17:12:09] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.096s
[2025-09-12 17:12:15] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.083s
[2025-09-12 17:12:21] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.097s
[2025-09-12 17:12:27] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.087s
[2025-09-12 17:12:33] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.082s
[2025-09-12 17:12:39] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.097s
[2025-09-12 17:12:46] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.085s
[2025-09-12 17:12:52] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.097s
[2025-09-12 17:12:58] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.103s
[2025-09-12 17:13:04] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.087s
[2025-09-12 17:13:10] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.112s
[2025-09-12 17:13:16] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.104s
[2025-09-12 17:13:22] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.096s
[2025-09-12 17:13:28] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.081s
[2025-09-12 17:13:34] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.102s
[2025-09-12 17:13:40] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.083s
[2025-09-12 17:13:47] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.084s
[2025-09-12 17:13:53] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.083s
[2025-09-12 17:13:59] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.102s
[2025-09-12 17:14:05] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.095s
[2025-09-12 17:14:11] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.083s
[2025-09-12 17:14:17] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.100s
[2025-09-12 17:14:23] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.095s
[2025-09-12 17:14:29] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.086s
[2025-09-12 17:14:35] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.085s
[2025-09-12 17:14:41] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.104s
[2025-09-12 17:14:48] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.098s
[2025-09-12 17:14:54] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.094s
[2025-09-12 17:15:00] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.100s
[2025-09-12 17:15:06] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.095s
[2025-09-12 17:15:12] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.095s
[2025-09-12 17:15:18] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.096s
[2025-09-12 17:15:24] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.107s
[2025-09-12 17:15:30] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.095s
[2025-09-12 17:15:36] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.098s
[2025-09-12 17:15:42] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.086s
[2025-09-12 17:15:42] 自旋相关函数计算完成,总耗时 616.71 秒
[2025-09-12 17:15:43] 计算傅里叶变换...
[2025-09-12 17:15:52] 自旋结构因子计算完成
[2025-09-12 17:15:53] 自旋相关函数平均误差: 0.000794
