[2025-09-09 10:36:42] ✓ 从checkpoint恢复: results/L=5/J2=0.05/J1=0.05/training/checkpoints/final_GCNN.pkl
[2025-09-09 10:36:42]   - 迭代次数: final
[2025-09-09 10:36:42]   - 能量: -81.152107+0.000003j ± 0.054953
[2025-09-09 10:36:42]   - 时间戳: 2025-09-09T04:43:31.695992+08:00
[2025-09-09 10:37:10] ✓ 变分状态参数已从checkpoint恢复
[2025-09-09 10:37:10] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-09 10:37:10] ==================================================
[2025-09-09 10:37:10] GCNN for Shastry-Sutherland Model
[2025-09-09 10:37:10] ==================================================
[2025-09-09 10:37:10] System parameters:
[2025-09-09 10:37:10]   - System size: L=5, N=100
[2025-09-09 10:37:10]   - System parameters: J1=0.04, J2=0.05, Q=0.95
[2025-09-09 10:37:10] --------------------------------------------------
[2025-09-09 10:37:10] Model parameters:
[2025-09-09 10:37:10]   - Number of layers = 4
[2025-09-09 10:37:10]   - Number of features = 4
[2025-09-09 10:37:10]   - Total parameters = 19628
[2025-09-09 10:37:10] --------------------------------------------------
[2025-09-09 10:37:10] Training parameters:
[2025-09-09 10:37:10]   - Learning rate: 0.015
[2025-09-09 10:37:10]   - Total iterations: 1050
[2025-09-09 10:37:10]   - Annealing cycles: 3
[2025-09-09 10:37:10]   - Initial period: 150
[2025-09-09 10:37:10]   - Period multiplier: 2.0
[2025-09-09 10:37:10]   - Temperature range: 0.0-1.0
[2025-09-09 10:37:10]   - Samples: 4096
[2025-09-09 10:37:10]   - Discarded samples: 0
[2025-09-09 10:37:10]   - Chunk size: 2048
[2025-09-09 10:37:10]   - Diagonal shift: 0.2
[2025-09-09 10:37:10]   - Gradient clipping: 1.0
[2025-09-09 10:37:10]   - Checkpoint enabled: interval=105
[2025-09-09 10:37:10]   - Checkpoint directory: results/L=5/J2=0.05/J1=0.04/training/checkpoints
[2025-09-09 10:37:10] --------------------------------------------------
[2025-09-09 10:37:10] Device status:
[2025-09-09 10:37:10]   - Devices model: NVIDIA H200 NVL
[2025-09-09 10:37:10]   - Number of devices: 1
[2025-09-09 10:37:10]   - Sharding: True
[2025-09-09 10:37:10] ============================================================
[2025-09-09 10:39:22] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -80.594156-0.019920j
[2025-09-09 10:40:52] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -80.643408-0.021616j
[2025-09-09 10:41:27] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -80.553896-0.014027j
[2025-09-09 10:42:02] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -80.637693-0.011097j
[2025-09-09 10:42:37] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -80.614622-0.013240j
[2025-09-09 10:43:13] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -80.582431-0.013297j
[2025-09-09 10:43:48] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -80.447139-0.003716j
[2025-09-09 10:44:22] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -80.447728-0.001179j
[2025-09-09 10:44:58] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -80.382603-0.013808j
[2025-09-09 10:45:33] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -80.367865-0.002096j
[2025-09-09 10:46:08] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -80.570467-0.004188j
[2025-09-09 10:46:43] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -80.547138+0.000972j
[2025-09-09 10:47:18] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -80.628864+0.003247j
[2025-09-09 10:47:53] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -80.685070-0.003635j
[2025-09-09 10:48:28] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -80.831101-0.004375j
[2025-09-09 10:49:03] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -80.803311-0.005497j
[2025-09-09 10:49:38] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -80.646284+0.000786j
[2025-09-09 10:50:13] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -80.770549-0.006130j
[2025-09-09 10:50:48] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -80.694679+0.000102j
[2025-09-09 10:51:23] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -80.681643-0.004585j
[2025-09-09 10:51:58] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -80.642152-0.003038j
[2025-09-09 10:52:33] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -80.639073-0.010376j
[2025-09-09 10:53:08] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -80.752526-0.001831j
[2025-09-09 10:53:43] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -80.660110-0.008272j
[2025-09-09 10:54:18] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -80.620617-0.005114j
[2025-09-09 10:54:53] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -80.554628+0.001490j
[2025-09-09 10:55:29] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -80.543407+0.002278j
[2025-09-09 10:56:04] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -80.519974+0.008015j
[2025-09-09 10:56:39] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -80.465559-0.008499j
[2025-09-09 10:57:14] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -80.552264-0.006894j
[2025-09-09 10:57:49] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -80.456177+0.003303j
[2025-09-09 10:58:24] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -80.458913-0.000865j
[2025-09-09 10:58:59] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -80.429504-0.000996j
[2025-09-09 10:59:34] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -80.461245+0.006044j
[2025-09-09 11:00:09] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -80.541500+0.003423j
[2025-09-09 11:00:44] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -80.625538+0.000942j
[2025-09-09 11:01:19] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -80.567674-0.002652j
[2025-09-09 11:01:54] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -80.589846-0.002130j
[2025-09-09 11:02:29] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -80.555556+0.001064j
[2025-09-09 11:03:04] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -80.660751+0.001186j
[2025-09-09 11:03:39] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -80.733142-0.002314j
[2025-09-09 11:04:14] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -80.524755-0.001155j
[2025-09-09 11:04:49] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -80.591032+0.003118j
[2025-09-09 11:05:24] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -80.743059+0.009576j
[2025-09-09 11:05:59] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -80.544092+0.001824j
[2025-09-09 11:06:34] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -80.555356-0.008381j
[2025-09-09 11:07:09] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -80.603425-0.006108j
[2025-09-09 11:07:44] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -80.525304-0.000754j
[2025-09-09 11:08:19] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -80.539769-0.002408j
[2025-09-09 11:08:54] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -80.561457+0.001185j
[2025-09-09 11:09:29] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -80.604958-0.001524j
[2025-09-09 11:10:04] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -80.668344-0.006323j
[2025-09-09 11:10:40] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -80.500275+0.001473j
[2025-09-09 11:11:15] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -80.703050+0.005828j
[2025-09-09 11:11:50] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -80.639401+0.002025j
[2025-09-09 11:12:25] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -80.605343+0.001422j
[2025-09-09 11:13:00] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -80.685028-0.007051j
[2025-09-09 11:13:35] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -80.693815+0.006157j
[2025-09-09 11:14:10] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -80.735364-0.003420j
[2025-09-09 11:14:45] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -80.563066-0.010331j
[2025-09-09 11:15:20] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -80.478494+0.001483j
[2025-09-09 11:15:55] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -80.582086-0.007253j
[2025-09-09 11:16:30] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -80.583495-0.005169j
[2025-09-09 11:17:03] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -80.556304-0.001755j
[2025-09-09 11:17:39] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -80.526151-0.007501j
[2025-09-09 11:18:14] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -80.450222-0.000762j
[2025-09-09 11:18:49] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -80.410446+0.001973j
[2025-09-09 11:19:24] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -80.595319-0.001448j
[2025-09-09 11:19:59] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -80.544662+0.000890j
[2025-09-09 11:20:34] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -80.566577-0.000346j
[2025-09-09 11:21:09] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -80.644613-0.003040j
[2025-09-09 11:21:44] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -80.687543-0.000756j
[2025-09-09 11:22:19] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -80.571069-0.003613j
[2025-09-09 11:22:54] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -80.723148+0.003488j
[2025-09-09 11:23:29] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -80.576273+0.001157j
[2025-09-09 11:24:04] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -80.557593-0.007147j
[2025-09-09 11:24:39] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -80.693514-0.001546j
[2025-09-09 11:25:14] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -80.711602-0.003826j
[2025-09-09 11:25:50] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -80.604740-0.008998j
[2025-09-09 11:26:25] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -80.640122-0.002912j
[2025-09-09 11:27:00] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -80.470983+0.003700j
[2025-09-09 11:27:35] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -80.526376-0.003367j
[2025-09-09 11:28:10] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -80.569192-0.004815j
[2025-09-09 11:28:45] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -80.645799+0.001095j
[2025-09-09 11:29:20] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -80.553877-0.002949j
[2025-09-09 11:29:55] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -80.541063-0.002954j
[2025-09-09 11:30:30] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -80.612298+0.005119j
[2025-09-09 11:31:05] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -80.605714+0.003673j
[2025-09-09 11:31:40] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -80.437318-0.000635j
[2025-09-09 11:32:15] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -80.296800-0.000173j
[2025-09-09 11:32:50] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -80.287157+0.000520j
[2025-09-09 11:33:25] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -80.437639+0.005741j
[2025-09-09 11:34:00] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -80.400352+0.000162j
[2025-09-09 11:34:35] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -80.448398+0.000254j
[2025-09-09 11:35:10] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -80.329614+0.005212j
[2025-09-09 11:35:45] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -80.304535-0.001592j
[2025-09-09 11:36:20] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -80.400128+0.003245j
[2025-09-09 11:36:55] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -80.448552+0.008125j
[2025-09-09 11:37:30] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -80.416024+0.002639j
[2025-09-09 11:38:05] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -80.582389+0.007291j
[2025-09-09 11:38:40] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -80.563727+0.001280j
[2025-09-09 11:39:15] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -80.476741-0.005809j
[2025-09-09 11:39:50] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -80.385827+0.006704j
[2025-09-09 11:40:25] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -80.553386+0.002739j
[2025-09-09 11:41:00] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -80.585348+0.005877j
[2025-09-09 11:41:00] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-09 11:41:35] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -80.603344-0.000794j
[2025-09-09 11:42:10] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -80.529232+0.000099j
[2025-09-09 11:42:45] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -80.478601-0.004422j
[2025-09-09 11:43:20] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -80.625115+0.001751j
[2025-09-09 11:43:55] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -80.814464-0.001884j
[2025-09-09 11:44:30] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -80.773447+0.001249j
[2025-09-09 11:45:05] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -80.673568+0.005552j
[2025-09-09 11:45:40] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -80.740424-0.001251j
[2025-09-09 11:46:15] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -80.710636-0.003571j
[2025-09-09 11:46:50] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -80.605150+0.003652j
[2025-09-09 11:47:25] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -80.737759+0.000564j
[2025-09-09 11:48:00] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -80.545507+0.001545j
[2025-09-09 11:48:35] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -80.584270+0.003499j
[2025-09-09 11:49:11] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -80.585415+0.005609j
[2025-09-09 11:49:46] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -80.665889-0.000449j
[2025-09-09 11:50:21] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -80.784310+0.002869j
[2025-09-09 11:50:56] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -80.810208-0.003500j
[2025-09-09 11:51:31] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -80.644294-0.006489j
[2025-09-09 11:52:06] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -80.570400-0.002211j
[2025-09-09 11:52:41] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -80.627510+0.003430j
[2025-09-09 11:53:15] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -80.719763-0.004753j
[2025-09-09 11:53:51] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -80.752833-0.002982j
[2025-09-09 11:54:26] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -80.781501+0.000085j
[2025-09-09 11:55:01] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -80.482328-0.003827j
[2025-09-09 11:55:36] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -80.642498-0.002660j
[2025-09-09 11:56:11] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -80.498324+0.003726j
[2025-09-09 11:56:46] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -80.421896-0.000759j
[2025-09-09 11:57:21] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -80.544599-0.002424j
[2025-09-09 11:57:56] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -80.453736-0.002733j
[2025-09-09 11:58:31] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -80.408273-0.008079j
[2025-09-09 11:59:06] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -80.492262-0.000523j
[2025-09-09 11:59:41] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -80.507947+0.007640j
[2025-09-09 12:00:16] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -80.479053-0.004040j
[2025-09-09 12:00:51] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -80.461315+0.000787j
[2025-09-09 12:01:26] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -80.441696+0.002285j
[2025-09-09 12:02:02] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -80.479853-0.003423j
[2025-09-09 12:02:37] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -80.245271+0.001210j
[2025-09-09 12:03:12] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -80.288661+0.003314j
[2025-09-09 12:03:47] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -80.505200+0.007004j
[2025-09-09 12:04:22] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -80.470331-0.005909j
[2025-09-09 12:04:57] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -80.355021-0.001373j
[2025-09-09 12:05:32] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -80.454204-0.000604j
[2025-09-09 12:06:07] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -80.573312-0.006858j
[2025-09-09 12:06:42] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -80.603896+0.000713j
[2025-09-09 12:07:17] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -80.515060+0.006634j
[2025-09-09 12:07:17] RESTART #1 | Period: 300
[2025-09-09 12:07:52] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -80.657447+0.001434j
[2025-09-09 12:08:27] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -80.462201-0.000289j
[2025-09-09 12:09:02] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -80.394510-0.002595j
[2025-09-09 12:09:38] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -80.473706-0.002969j
[2025-09-09 12:10:13] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -80.457951+0.004273j
[2025-09-09 12:10:48] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -80.540093-0.003251j
[2025-09-09 12:11:23] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -80.484611+0.003518j
[2025-09-09 12:11:58] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -80.492055+0.002502j
[2025-09-09 12:12:33] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -80.475888-0.000233j
[2025-09-09 12:13:08] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -80.327821-0.001015j
[2025-09-09 12:13:43] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -80.427374+0.005517j
[2025-09-09 12:14:18] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -80.446257-0.003072j
[2025-09-09 12:14:53] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -80.420710+0.000006j
[2025-09-09 12:15:28] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -80.491040-0.000800j
[2025-09-09 12:16:03] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -80.465993-0.003697j
[2025-09-09 12:16:38] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -80.550313+0.000911j
[2025-09-09 12:17:13] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -80.476272+0.002787j
[2025-09-09 12:17:48] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -80.472173+0.001055j
[2025-09-09 12:18:23] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -80.567967-0.000549j
[2025-09-09 12:18:58] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -80.424452-0.002211j
[2025-09-09 12:19:33] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -80.351330+0.003045j
[2025-09-09 12:20:08] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -80.405503+0.004626j
[2025-09-09 12:20:43] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -80.537434-0.000145j
[2025-09-09 12:21:18] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -80.416995-0.004766j
[2025-09-09 12:21:53] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -80.536135-0.005067j
[2025-09-09 12:22:28] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -80.576435+0.003066j
[2025-09-09 12:23:03] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -80.669156-0.000504j
[2025-09-09 12:23:38] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -80.746696+0.004135j
[2025-09-09 12:24:13] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -80.594042+0.004904j
[2025-09-09 12:24:48] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -80.468072-0.003277j
[2025-09-09 12:25:23] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -80.399700-0.001383j
[2025-09-09 12:25:58] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -80.366794-0.002380j
[2025-09-09 12:26:33] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -80.407913+0.000631j
[2025-09-09 12:27:08] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -80.482789+0.000696j
[2025-09-09 12:27:43] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -80.449442+0.001101j
[2025-09-09 12:28:18] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -80.434092-0.005919j
[2025-09-09 12:28:53] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -80.437597-0.000285j
[2025-09-09 12:29:28] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -80.437448-0.004665j
[2025-09-09 12:30:04] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -80.509076+0.000439j
[2025-09-09 12:30:39] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -80.433142+0.006719j
[2025-09-09 12:31:13] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -80.487103+0.002020j
[2025-09-09 12:31:49] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -80.465234-0.001487j
[2025-09-09 12:32:24] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -80.576042+0.000625j
[2025-09-09 12:32:58] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -80.542520-0.000158j
[2025-09-09 12:33:33] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -80.456786+0.005063j
[2025-09-09 12:34:09] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -80.759809-0.001349j
[2025-09-09 12:34:44] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -80.572587+0.001163j
[2025-09-09 12:35:19] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -80.640814-0.001520j
[2025-09-09 12:35:54] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -80.510601-0.000062j
[2025-09-09 12:36:29] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -80.641689-0.003933j
[2025-09-09 12:37:04] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -80.641830+0.001075j
[2025-09-09 12:37:39] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -80.510858-0.001639j
[2025-09-09 12:38:14] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -80.661843-0.003320j
[2025-09-09 12:38:49] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -80.686634+0.002224j
[2025-09-09 12:39:24] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -80.487413+0.000167j
[2025-09-09 12:39:59] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -80.372956-0.007992j
[2025-09-09 12:40:34] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -80.396963-0.001045j
[2025-09-09 12:41:09] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -80.378847-0.005239j
[2025-09-09 12:41:44] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -80.517717-0.006197j
[2025-09-09 12:42:19] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -80.395603+0.003040j
[2025-09-09 12:42:19] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-09 12:42:54] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -80.459891+0.000449j
[2025-09-09 12:43:29] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -80.508686+0.002901j
[2025-09-09 12:44:04] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -80.490081+0.002636j
[2025-09-09 12:44:40] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -80.532066-0.000197j
[2025-09-09 12:45:15] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -80.439092-0.003160j
[2025-09-09 12:45:50] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -80.303928-0.008057j
[2025-09-09 12:46:25] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -80.354583+0.000575j
[2025-09-09 12:47:00] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -80.397943-0.007221j
[2025-09-09 12:47:35] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -80.396232-0.003126j
[2025-09-09 12:48:10] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -80.406014-0.000780j
[2025-09-09 12:48:45] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -80.520892-0.000663j
[2025-09-09 12:49:20] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -80.454286-0.006682j
[2025-09-09 12:49:55] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -80.431111+0.004037j
[2025-09-09 12:50:30] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -80.460559-0.002569j
[2025-09-09 12:51:05] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -80.594445-0.005873j
[2025-09-09 12:51:40] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -80.678865+0.002799j
[2025-09-09 12:52:15] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -80.498443-0.004484j
[2025-09-09 12:52:50] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -80.537839+0.003916j
[2025-09-09 12:53:25] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -80.495916-0.007362j
[2025-09-09 12:54:00] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -80.609510-0.005593j
[2025-09-09 12:54:35] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -80.457931-0.000950j
[2025-09-09 12:55:11] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -80.447490+0.001681j
[2025-09-09 12:55:46] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -80.430121-0.007148j
[2025-09-09 12:56:21] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -80.450914-0.005289j
[2025-09-09 12:56:56] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -80.404975+0.005561j
[2025-09-09 12:57:31] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -80.450281-0.002098j
[2025-09-09 12:58:06] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -80.412260+0.006163j
[2025-09-09 12:58:41] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -80.473077-0.003646j
[2025-09-09 12:59:16] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -80.510613+0.001377j
[2025-09-09 12:59:51] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -80.525485+0.003027j
[2025-09-09 13:00:26] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -80.516497+0.007060j
[2025-09-09 13:01:01] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -80.400571-0.006632j
[2025-09-09 13:01:36] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -80.458819-0.004326j
[2025-09-09 13:02:11] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -80.366803-0.001994j
[2025-09-09 13:02:46] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -80.351097+0.001640j
[2025-09-09 13:03:21] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -80.411406+0.002255j
[2025-09-09 13:03:56] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -80.354552+0.001656j
[2025-09-09 13:04:31] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -80.575420-0.001376j
[2025-09-09 13:05:06] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -80.499803-0.009002j
[2025-09-09 13:05:42] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -80.399832-0.000559j
[2025-09-09 13:06:17] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -80.335640+0.003388j
[2025-09-09 13:06:52] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -80.396704+0.004984j
[2025-09-09 13:07:27] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -80.435797-0.004724j
[2025-09-09 13:08:02] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -80.394662+0.001087j
[2025-09-09 13:08:37] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -80.506975+0.001128j
[2025-09-09 13:09:12] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -80.600669-0.003294j
[2025-09-09 13:09:47] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -80.523740-0.001415j
[2025-09-09 13:10:22] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -80.501299-0.001814j
[2025-09-09 13:10:57] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -80.661525-0.004403j
[2025-09-09 13:11:32] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -80.635774-0.005121j
[2025-09-09 13:12:06] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -80.593984-0.001327j
[2025-09-09 13:12:41] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -80.599098-0.005460j
[2025-09-09 13:13:17] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -80.499740-0.005183j
[2025-09-09 13:13:52] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -80.403035+0.006232j
[2025-09-09 13:14:26] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -80.514428-0.002966j
[2025-09-09 13:15:01] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -80.578463-0.001126j
[2025-09-09 13:15:37] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -80.394374-0.002606j
[2025-09-09 13:16:12] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -80.422302+0.002019j
[2025-09-09 13:16:47] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -80.579328-0.004639j
[2025-09-09 13:17:22] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -80.563048-0.000768j
[2025-09-09 13:17:57] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -80.515998+0.004189j
[2025-09-09 13:18:32] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -80.608356-0.001648j
[2025-09-09 13:19:07] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -80.556918-0.001705j
[2025-09-09 13:19:42] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -80.455128-0.004714j
[2025-09-09 13:20:17] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -80.631173-0.001598j
[2025-09-09 13:20:52] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -80.595259-0.001140j
[2025-09-09 13:21:27] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -80.633213-0.004044j
[2025-09-09 13:22:02] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -80.625354-0.001058j
[2025-09-09 13:22:37] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -80.640291-0.000473j
[2025-09-09 13:23:12] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -80.608217+0.001168j
[2025-09-09 13:23:47] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -80.528022-0.004536j
[2025-09-09 13:24:22] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -80.443156-0.001119j
[2025-09-09 13:24:57] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -80.719027-0.000199j
[2025-09-09 13:25:32] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -80.583673-0.000006j
[2025-09-09 13:26:07] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -80.675686+0.000654j
[2025-09-09 13:26:42] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -80.597750-0.005593j
[2025-09-09 13:27:18] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -80.633756-0.001275j
[2025-09-09 13:27:53] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -80.611487-0.006177j
[2025-09-09 13:28:28] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -80.625357-0.003361j
[2025-09-09 13:29:03] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -80.648624+0.002706j
[2025-09-09 13:29:38] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -80.553087+0.004207j
[2025-09-09 13:30:13] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -80.658663+0.000036j
[2025-09-09 13:30:48] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -80.579692-0.008751j
[2025-09-09 13:31:23] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -80.616551+0.002519j
[2025-09-09 13:31:58] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -80.676979-0.002039j
[2025-09-09 13:32:33] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -80.685943+0.001546j
[2025-09-09 13:33:08] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -80.658709-0.002148j
[2025-09-09 13:33:43] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -80.701766-0.000170j
[2025-09-09 13:34:18] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -80.712876-0.002075j
[2025-09-09 13:34:53] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -80.666970+0.001663j
[2025-09-09 13:35:28] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -80.673281-0.006242j
[2025-09-09 13:36:03] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -80.732641+0.001767j
[2025-09-09 13:36:38] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -80.557204-0.003047j
[2025-09-09 13:37:13] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -80.526268-0.002490j
[2025-09-09 13:37:48] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -80.644939-0.000689j
[2025-09-09 13:38:23] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -80.597409-0.000088j
[2025-09-09 13:38:58] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -80.668206+0.000703j
[2025-09-09 13:39:33] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -80.610780+0.000533j
[2025-09-09 13:40:08] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -80.476749-0.001006j
[2025-09-09 13:40:43] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -80.463171-0.003563j
[2025-09-09 13:41:18] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -80.495735+0.001314j
[2025-09-09 13:41:53] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -80.434229+0.005348j
[2025-09-09 13:42:28] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -80.463570+0.003622j
[2025-09-09 13:43:03] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -80.502466+0.004864j
[2025-09-09 13:43:39] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -80.396192-0.003003j
[2025-09-09 13:43:39] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-09 13:44:14] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -80.504584+0.000070j
[2025-09-09 13:44:49] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -80.560075+0.003735j
[2025-09-09 13:45:24] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -80.686283-0.003000j
[2025-09-09 13:45:59] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -80.588573-0.004304j
[2025-09-09 13:46:34] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -80.701820-0.000695j
[2025-09-09 13:47:09] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -80.673843+0.003139j
[2025-09-09 13:47:44] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -80.553875-0.005059j
[2025-09-09 13:48:19] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -80.493442+0.004952j
[2025-09-09 13:48:54] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -80.522521+0.003388j
[2025-09-09 13:49:29] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -80.495649-0.001309j
[2025-09-09 13:50:04] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -80.407802-0.001920j
[2025-09-09 13:50:39] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -80.325711+0.000041j
[2025-09-09 13:51:14] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -80.469012-0.002534j
[2025-09-09 13:51:49] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -80.481847-0.002958j
[2025-09-09 13:52:24] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -80.487811+0.005241j
[2025-09-09 13:53:00] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -80.570590+0.001673j
[2025-09-09 13:53:35] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -80.470859+0.005998j
[2025-09-09 13:54:10] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -80.646681+0.000759j
[2025-09-09 13:54:45] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -80.432064-0.000411j
[2025-09-09 13:55:20] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -80.632790+0.001543j
[2025-09-09 13:55:55] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -80.617413+0.003082j
[2025-09-09 13:56:30] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -80.676284+0.003129j
[2025-09-09 13:57:05] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -80.567115-0.004245j
[2025-09-09 13:57:40] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -80.504477-0.003370j
[2025-09-09 13:58:15] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -80.688809-0.003507j
[2025-09-09 13:58:50] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -80.516036-0.001480j
[2025-09-09 13:59:25] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -80.454769+0.003146j
[2025-09-09 14:00:00] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -80.487211-0.002062j
[2025-09-09 14:00:35] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -80.322647-0.001415j
[2025-09-09 14:01:10] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -80.502098-0.000054j
[2025-09-09 14:01:46] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -80.552243+0.001045j
[2025-09-09 14:02:21] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -80.459449-0.002312j
[2025-09-09 14:02:56] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -80.453464+0.000164j
[2025-09-09 14:03:31] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -80.525540+0.001097j
[2025-09-09 14:04:06] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -80.491245+0.003382j
[2025-09-09 14:04:41] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -80.623624+0.000230j
[2025-09-09 14:05:15] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -80.579581-0.006316j
[2025-09-09 14:05:51] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -80.685405+0.004404j
[2025-09-09 14:06:26] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -80.458014-0.000738j
[2025-09-09 14:07:01] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -80.508098+0.003605j
[2025-09-09 14:07:36] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -80.425518-0.001770j
[2025-09-09 14:08:11] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -80.461221-0.003166j
[2025-09-09 14:08:46] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -80.409023-0.002846j
[2025-09-09 14:09:21] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -80.633054+0.001746j
[2025-09-09 14:09:56] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -80.574671+0.000831j
[2025-09-09 14:10:31] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -80.617866-0.010340j
[2025-09-09 14:11:06] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -80.391985-0.001266j
[2025-09-09 14:11:41] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -80.380189-0.003917j
[2025-09-09 14:12:16] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -80.362069-0.000599j
[2025-09-09 14:12:51] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -80.235269-0.005444j
[2025-09-09 14:13:26] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -80.466450-0.003562j
[2025-09-09 14:14:01] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -80.386047-0.001853j
[2025-09-09 14:14:36] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -80.291388-0.001408j
[2025-09-09 14:15:12] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -80.272314-0.018487j
[2025-09-09 14:15:47] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -80.383084-0.000290j
[2025-09-09 14:16:22] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -80.481464+0.000366j
[2025-09-09 14:16:57] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -80.522813-0.006230j
[2025-09-09 14:17:32] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -80.609319-0.003242j
[2025-09-09 14:18:07] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -80.626666+0.002150j
[2025-09-09 14:18:42] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -80.528573+0.003396j
[2025-09-09 14:19:17] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -80.415739-0.001978j
[2025-09-09 14:19:52] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -80.581631-0.001150j
[2025-09-09 14:20:27] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -80.497190+0.006883j
[2025-09-09 14:21:02] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -80.445427+0.007436j
[2025-09-09 14:21:37] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -80.504688+0.000426j
[2025-09-09 14:22:12] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -80.330152-0.001197j
[2025-09-09 14:22:47] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -80.518459-0.000781j
[2025-09-09 14:23:23] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -80.787265-0.006316j
[2025-09-09 14:23:58] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -80.604774-0.000580j
[2025-09-09 14:24:33] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -80.562726+0.001212j
[2025-09-09 14:25:08] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -80.627156+0.001881j
[2025-09-09 14:25:43] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -80.654464-0.004782j
[2025-09-09 14:26:18] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -80.689448-0.004377j
[2025-09-09 14:26:53] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -80.531493-0.006466j
[2025-09-09 14:27:28] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -80.813361+0.004290j
[2025-09-09 14:28:03] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -80.809117+0.001880j
[2025-09-09 14:28:38] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -80.669154+0.006875j
[2025-09-09 14:29:13] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -80.742916+0.000954j
[2025-09-09 14:29:48] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -80.666250-0.001225j
[2025-09-09 14:30:23] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -80.675787+0.004129j
[2025-09-09 14:30:57] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -80.583738-0.003806j
[2025-09-09 14:31:34] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -80.614871-0.002333j
[2025-09-09 14:32:08] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -80.556977-0.002926j
[2025-09-09 14:32:46] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -80.491905-0.000380j
[2025-09-09 14:33:21] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -80.605113+0.002687j
[2025-09-09 14:33:56] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -80.593512-0.007545j
[2025-09-09 14:34:31] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -80.579388+0.002052j
[2025-09-09 14:35:06] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -80.610537-0.001930j
[2025-09-09 14:35:41] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -80.650978+0.000881j
[2025-09-09 14:36:16] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -80.533362+0.000147j
[2025-09-09 14:36:51] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -80.575090-0.001984j
[2025-09-09 14:37:27] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -80.479410+0.000329j
[2025-09-09 14:38:02] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -80.497629-0.003785j
[2025-09-09 14:38:37] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -80.510276-0.002683j
[2025-09-09 14:39:12] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -80.496521+0.002497j
[2025-09-09 14:39:47] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -80.407671+0.004745j
[2025-09-09 14:40:22] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -80.586356+0.001519j
[2025-09-09 14:40:57] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -80.566062+0.008662j
[2025-09-09 14:41:32] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -80.710485-0.003617j
[2025-09-09 14:42:07] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -80.623653+0.000205j
[2025-09-09 14:42:42] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -80.626701+0.001198j
[2025-09-09 14:43:17] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -80.606802+0.005175j
[2025-09-09 14:43:52] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -80.506775-0.000017j
[2025-09-09 14:44:27] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -80.466523+0.000639j
[2025-09-09 14:45:02] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -80.552270-0.008237j
[2025-09-09 14:45:02] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-09 14:45:37] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -80.437731+0.004577j
[2025-09-09 14:46:12] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -80.612600-0.004005j
[2025-09-09 14:46:47] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -80.574768-0.007214j
[2025-09-09 14:47:22] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -80.540165+0.005577j
[2025-09-09 14:47:57] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -80.358315-0.000062j
[2025-09-09 14:48:32] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -80.476152-0.002834j
[2025-09-09 14:49:07] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -80.395386-0.003407j
[2025-09-09 14:49:42] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -80.483284-0.003680j
[2025-09-09 14:50:17] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -80.502270+0.006054j
[2025-09-09 14:50:52] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -80.483588-0.009089j
[2025-09-09 14:51:27] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -80.449039-0.000422j
[2025-09-09 14:52:02] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -80.431339-0.002795j
[2025-09-09 14:52:37] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -80.427652-0.000046j
[2025-09-09 14:53:12] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -80.507088+0.001146j
[2025-09-09 14:53:47] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -80.310122+0.002827j
[2025-09-09 14:54:22] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -80.425871-0.001733j
[2025-09-09 14:54:58] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -80.315714-0.008813j
[2025-09-09 14:55:32] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -80.470395-0.003750j
[2025-09-09 14:56:07] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -80.456485-0.007596j
[2025-09-09 14:56:42] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -80.536511-0.000269j
[2025-09-09 14:57:17] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -80.545518+0.002770j
[2025-09-09 14:57:52] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -80.447507-0.000822j
[2025-09-09 14:58:27] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -80.571926+0.002215j
[2025-09-09 14:59:02] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -80.683972+0.003005j
[2025-09-09 14:59:37] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -80.783621+0.004012j
[2025-09-09 15:00:12] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -80.733340+0.004854j
[2025-09-09 15:00:47] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -80.607215+0.003538j
[2025-09-09 15:01:22] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -80.582373+0.004032j
[2025-09-09 15:01:57] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -80.627815+0.002205j
[2025-09-09 15:02:32] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -80.536275+0.002183j
[2025-09-09 15:02:32] RESTART #2 | Period: 600
[2025-09-09 15:03:07] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -80.611238+0.004663j
[2025-09-09 15:03:42] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -80.581179+0.000876j
[2025-09-09 15:04:17] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -80.582008+0.000383j
[2025-09-09 15:04:52] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -80.543666-0.005008j
[2025-09-09 15:05:28] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -80.585704+0.000726j
[2025-09-09 15:06:03] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -80.556862-0.001086j
[2025-09-09 15:06:38] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -80.637942-0.002878j
[2025-09-09 15:07:13] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -80.651464+0.018579j
[2025-09-09 15:07:48] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -80.581018+0.005357j
[2025-09-09 15:08:23] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -80.715638-0.000921j
[2025-09-09 15:08:58] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -80.637885+0.005248j
[2025-09-09 15:09:33] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -80.489916+0.003055j
[2025-09-09 15:10:08] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -80.556882+0.002353j
[2025-09-09 15:10:43] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -80.519658+0.001874j
[2025-09-09 15:11:18] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -80.483187+0.003612j
[2025-09-09 15:11:53] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -80.527766+0.001162j
[2025-09-09 15:12:28] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -80.539629-0.002316j
[2025-09-09 15:13:03] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -80.608190-0.003843j
[2025-09-09 15:13:38] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -80.739913+0.005277j
[2025-09-09 15:14:13] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -80.586535+0.003672j
[2025-09-09 15:14:48] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -80.813110+0.000969j
[2025-09-09 15:15:23] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -80.660991+0.008594j
[2025-09-09 15:15:58] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -80.669659+0.000918j
[2025-09-09 15:16:33] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -80.589317-0.001907j
[2025-09-09 15:17:09] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -80.508944+0.000487j
[2025-09-09 15:17:44] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -80.608709+0.002191j
[2025-09-09 15:18:19] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -80.648658+0.003225j
[2025-09-09 15:18:54] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -80.537535+0.005121j
[2025-09-09 15:19:29] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -80.509702+0.004435j
[2025-09-09 15:20:04] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -80.347158-0.000220j
[2025-09-09 15:20:39] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -80.434636+0.000600j
[2025-09-09 15:21:14] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -80.405058+0.000069j
[2025-09-09 15:21:49] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -80.564071+0.006681j
[2025-09-09 15:22:24] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -80.546593-0.002476j
[2025-09-09 15:22:59] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -80.466089-0.002593j
[2025-09-09 15:23:35] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -80.427569-0.001023j
[2025-09-09 15:24:10] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -80.538654+0.000434j
[2025-09-09 15:24:44] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -80.419419+0.004916j
[2025-09-09 15:25:19] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -80.395171-0.004389j
[2025-09-09 15:25:55] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -80.549365+0.001206j
[2025-09-09 15:26:30] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -80.450944-0.002320j
[2025-09-09 15:27:05] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -80.574142-0.002670j
[2025-09-09 15:27:40] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -80.393399+0.001532j
[2025-09-09 15:28:15] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -80.647174-0.001743j
[2025-09-09 15:28:50] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -80.609733+0.000603j
[2025-09-09 15:29:25] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -80.658960+0.002547j
[2025-09-09 15:30:00] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -80.700056-0.001903j
[2025-09-09 15:30:35] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -80.571504+0.000242j
[2025-09-09 15:31:10] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -80.464566-0.002721j
[2025-09-09 15:31:45] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -80.561849+0.004252j
[2025-09-09 15:32:20] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -80.514185+0.005683j
[2025-09-09 15:32:55] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -80.597906+0.002197j
[2025-09-09 15:33:30] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -80.675423+0.006936j
[2025-09-09 15:34:05] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -80.609797+0.001567j
[2025-09-09 15:34:40] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -80.619255-0.002781j
[2025-09-09 15:35:16] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -80.428879-0.000618j
[2025-09-09 15:35:51] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -80.449696+0.003152j
[2025-09-09 15:36:26] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -80.573100-0.003903j
[2025-09-09 15:37:01] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -80.544611+0.004073j
[2025-09-09 15:37:36] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -80.599058-0.001900j
[2025-09-09 15:38:11] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -80.628318-0.000155j
[2025-09-09 15:38:46] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -80.645828-0.005084j
[2025-09-09 15:39:21] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -80.605864+0.001185j
[2025-09-09 15:39:56] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -80.419565+0.000461j
[2025-09-09 15:40:31] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -80.434843+0.001513j
[2025-09-09 15:41:06] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -80.526852-0.005045j
[2025-09-09 15:41:40] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -80.402319-0.007079j
[2025-09-09 15:42:15] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -80.379597+0.000603j
[2025-09-09 15:42:50] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -80.425553-0.008572j
[2025-09-09 15:43:25] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -80.517017-0.002777j
[2025-09-09 15:44:00] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -80.515650-0.000466j
[2025-09-09 15:44:35] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -80.588430-0.000311j
[2025-09-09 15:45:10] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -80.539443-0.007742j
[2025-09-09 15:45:45] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -80.431810-0.000359j
[2025-09-09 15:46:20] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -80.452155+0.001832j
[2025-09-09 15:46:21] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-09 15:46:55] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -80.463409+0.000837j
[2025-09-09 15:47:31] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -80.438564-0.000882j
[2025-09-09 15:48:06] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -80.494660+0.000717j
[2025-09-09 15:48:41] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -80.484401-0.001065j
[2025-09-09 15:49:16] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -80.585561-0.000760j
[2025-09-09 15:49:51] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -80.517348+0.000142j
[2025-09-09 15:50:26] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -80.642370-0.001423j
[2025-09-09 15:51:01] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -80.568480-0.002217j
[2025-09-09 15:51:36] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -80.715407-0.003481j
[2025-09-09 15:52:11] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -80.691518-0.001887j
[2025-09-09 15:52:46] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -80.580437+0.004035j
[2025-09-09 15:53:21] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -80.613525+0.002490j
[2025-09-09 15:53:56] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -80.556132-0.000997j
[2025-09-09 15:54:31] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -80.690873-0.006072j
[2025-09-09 15:55:06] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -80.562195-0.001318j
[2025-09-09 15:55:41] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -80.619113+0.000316j
[2025-09-09 15:56:16] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -80.606900+0.002389j
[2025-09-09 15:56:51] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -80.589286-0.002953j
[2025-09-09 15:57:26] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -80.644914-0.003530j
[2025-09-09 15:58:01] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -80.552620+0.000551j
[2025-09-09 15:58:36] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -80.479102+0.000116j
[2025-09-09 15:59:11] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -80.602395+0.003866j
[2025-09-09 15:59:46] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -80.683608-0.009343j
[2025-09-09 16:00:21] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -80.497712+0.000403j
[2025-09-09 16:00:56] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -80.651000+0.009494j
[2025-09-09 16:01:31] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -80.443252+0.001364j
[2025-09-09 16:02:06] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -80.406004+0.001690j
[2025-09-09 16:02:41] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -80.519891+0.001300j
[2025-09-09 16:03:16] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -80.487891+0.002253j
[2025-09-09 16:03:52] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -80.670785-0.000161j
[2025-09-09 16:04:27] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -80.659499-0.000380j
[2025-09-09 16:05:02] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -80.508584+0.003355j
[2025-09-09 16:05:36] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -80.609812-0.000175j
[2025-09-09 16:06:11] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -80.583599-0.002920j
[2025-09-09 16:06:47] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -80.452861+0.002145j
[2025-09-09 16:07:22] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -80.517918-0.003901j
[2025-09-09 16:07:57] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -80.329034-0.002255j
[2025-09-09 16:08:32] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -80.330219-0.000907j
[2025-09-09 16:09:07] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -80.344227-0.005125j
[2025-09-09 16:09:42] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -80.251585-0.006027j
[2025-09-09 16:10:17] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -80.512563+0.002162j
[2025-09-09 16:10:52] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -80.461562+0.004425j
[2025-09-09 16:11:27] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -80.385840+0.000028j
[2025-09-09 16:12:02] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -80.375575+0.002276j
[2025-09-09 16:12:37] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -80.434495-0.000256j
[2025-09-09 16:13:12] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -80.476913+0.000355j
[2025-09-09 16:13:47] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -80.637923-0.001837j
[2025-09-09 16:14:22] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -80.508805-0.010864j
[2025-09-09 16:14:57] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -80.371914+0.000543j
[2025-09-09 16:15:32] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -80.374477-0.002061j
[2025-09-09 16:16:07] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -80.499001+0.000058j
[2025-09-09 16:16:42] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -80.564814-0.002044j
[2025-09-09 16:17:17] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -80.679746+0.010469j
[2025-09-09 16:17:52] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -80.723251-0.001192j
[2025-09-09 16:18:27] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -80.601084-0.003440j
[2025-09-09 16:19:02] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -80.565545-0.002697j
[2025-09-09 16:19:37] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -80.643351-0.000228j
[2025-09-09 16:20:12] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -80.589188-0.002394j
[2025-09-09 16:20:47] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -80.617095-0.001914j
[2025-09-09 16:21:22] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -80.692569+0.003216j
[2025-09-09 16:21:58] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -80.658622+0.000866j
[2025-09-09 16:22:33] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -80.696884+0.007081j
[2025-09-09 16:23:07] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -80.693228+0.007678j
[2025-09-09 16:23:42] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -80.666730-0.000431j
[2025-09-09 16:24:17] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -80.635618+0.003380j
[2025-09-09 16:24:52] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -80.525800+0.005183j
[2025-09-09 16:25:27] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -80.440976+0.002688j
[2025-09-09 16:26:03] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -80.317907-0.007102j
[2025-09-09 16:26:38] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -80.445915+0.003584j
[2025-09-09 16:27:13] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -80.518653+0.005632j
[2025-09-09 16:27:48] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -80.369912+0.001152j
[2025-09-09 16:28:23] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -80.374016+0.002198j
[2025-09-09 16:28:58] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -80.549759+0.004488j
[2025-09-09 16:29:33] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -80.428118-0.005129j
[2025-09-09 16:30:08] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -80.443307+0.003073j
[2025-09-09 16:30:43] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -80.545178-0.000149j
[2025-09-09 16:31:18] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -80.480291-0.001741j
[2025-09-09 16:31:53] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -80.525425-0.001410j
[2025-09-09 16:32:28] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -80.493160+0.001937j
[2025-09-09 16:33:03] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -80.633444-0.013129j
[2025-09-09 16:33:38] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -80.618484-0.001078j
[2025-09-09 16:34:13] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -80.631191+0.003000j
[2025-09-09 16:34:49] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -80.476542-0.003719j
[2025-09-09 16:35:24] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -80.588673-0.003387j
[2025-09-09 16:35:59] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -80.447395+0.000513j
[2025-09-09 16:36:34] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -80.417225+0.005324j
[2025-09-09 16:37:09] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -80.625407-0.002414j
[2025-09-09 16:37:44] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -80.623368+0.004178j
[2025-09-09 16:38:19] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -80.577825-0.003485j
[2025-09-09 16:38:54] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -80.567695+0.000488j
[2025-09-09 16:39:29] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -80.474079+0.004189j
[2025-09-09 16:40:04] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -80.431316+0.000147j
[2025-09-09 16:40:39] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -80.386316+0.000815j
[2025-09-09 16:41:14] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -80.504662-0.003424j
[2025-09-09 16:41:49] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -80.606171+0.000932j
[2025-09-09 16:42:25] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -80.658185-0.004464j
[2025-09-09 16:43:00] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -80.517037-0.000449j
[2025-09-09 16:43:35] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -80.415949-0.002573j
[2025-09-09 16:44:10] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -80.384463+0.001091j
[2025-09-09 16:44:45] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -80.405477+0.006255j
[2025-09-09 16:45:20] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -80.509887-0.002361j
[2025-09-09 16:45:55] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -80.523746+0.000979j
[2025-09-09 16:46:30] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -80.597173+0.004667j
[2025-09-09 16:47:05] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -80.552471-0.002558j
[2025-09-09 16:47:40] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -80.586362-0.000624j
[2025-09-09 16:47:40] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-09 16:48:15] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -80.529421-0.001043j
[2025-09-09 16:48:50] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -80.643998+0.000228j
[2025-09-09 16:49:25] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -80.574156-0.001915j
[2025-09-09 16:50:00] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -80.645521-0.002570j
[2025-09-09 16:50:35] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -80.563365+0.003984j
[2025-09-09 16:51:10] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -80.593203-0.000463j
[2025-09-09 16:51:46] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -80.530574+0.000212j
[2025-09-09 16:52:21] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -80.603410+0.003794j
[2025-09-09 16:52:56] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -80.477323-0.000495j
[2025-09-09 16:53:31] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -80.484088+0.000185j
[2025-09-09 16:54:06] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -80.354542-0.001603j
[2025-09-09 16:54:41] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -80.336065-0.000080j
[2025-09-09 16:55:16] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -80.423173+0.004183j
[2025-09-09 16:55:51] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -80.439775+0.001106j
[2025-09-09 16:56:26] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -80.382804+0.001204j
[2025-09-09 16:57:01] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -80.486393+0.001544j
[2025-09-09 16:57:36] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -80.469192+0.001393j
[2025-09-09 16:58:11] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -80.474312-0.000963j
[2025-09-09 16:58:46] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -80.493407+0.001743j
[2025-09-09 16:59:21] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -80.620528-0.000968j
[2025-09-09 16:59:56] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -80.418791+0.001006j
[2025-09-09 17:00:31] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -80.462973-0.001000j
[2025-09-09 17:01:07] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -80.684458+0.002035j
[2025-09-09 17:01:42] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -80.606692-0.002083j
[2025-09-09 17:02:17] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -80.495040+0.002797j
[2025-09-09 17:02:52] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -80.461205-0.000397j
[2025-09-09 17:03:27] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -80.516014-0.002671j
[2025-09-09 17:04:02] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -80.523138+0.005947j
[2025-09-09 17:04:37] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -80.683006+0.003204j
[2025-09-09 17:05:12] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -80.737381+0.003617j
[2025-09-09 17:05:47] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -80.658428-0.000035j
[2025-09-09 17:06:22] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -80.772659-0.005316j
[2025-09-09 17:06:57] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -80.556342-0.003258j
[2025-09-09 17:07:32] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -80.589372+0.015549j
[2025-09-09 17:08:07] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -80.750229-0.000875j
[2025-09-09 17:08:42] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -80.492979-0.003958j
[2025-09-09 17:09:18] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -80.490202-0.008095j
[2025-09-09 17:09:53] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -80.494054+0.006565j
[2025-09-09 17:10:28] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -80.587589-0.000116j
[2025-09-09 17:11:03] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -80.505969+0.001046j
[2025-09-09 17:11:38] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -80.608150-0.002522j
[2025-09-09 17:12:13] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -80.583239-0.001934j
[2025-09-09 17:12:48] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -80.563761+0.002420j
[2025-09-09 17:13:23] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -80.535069-0.000006j
[2025-09-09 17:13:58] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -80.513072-0.002717j
[2025-09-09 17:14:33] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -80.306348+0.000724j
[2025-09-09 17:15:08] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -80.376691-0.006424j
[2025-09-09 17:15:43] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -80.340288-0.011389j
[2025-09-09 17:16:18] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -80.440527+0.000714j
[2025-09-09 17:16:53] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -80.381403+0.008566j
[2025-09-09 17:17:28] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -80.621564+0.007453j
[2025-09-09 17:18:03] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -80.593761+0.005072j
[2025-09-09 17:18:38] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -80.629394-0.001230j
[2025-09-09 17:19:13] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -80.524745-0.002170j
[2025-09-09 17:19:48] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -80.729974-0.001443j
[2025-09-09 17:20:23] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -80.582565+0.005848j
[2025-09-09 17:20:58] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -80.561680-0.003684j
[2025-09-09 17:21:34] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -80.645056-0.003115j
[2025-09-09 17:22:09] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -80.609028-0.001216j
[2025-09-09 17:22:44] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -80.548774+0.002896j
[2025-09-09 17:23:19] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -80.588046-0.003992j
[2025-09-09 17:23:54] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -80.697533-0.001957j
[2025-09-09 17:24:29] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -80.626054-0.004308j
[2025-09-09 17:25:04] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -80.682097-0.000144j
[2025-09-09 17:25:39] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -80.681852+0.001305j
[2025-09-09 17:26:14] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -80.562528+0.001183j
[2025-09-09 17:26:49] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -80.566422-0.004402j
[2025-09-09 17:27:24] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -80.726716-0.005220j
[2025-09-09 17:27:59] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -80.590784-0.003408j
[2025-09-09 17:28:35] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -80.743168-0.000753j
[2025-09-09 17:29:10] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -80.600486+0.001241j
[2025-09-09 17:29:45] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -80.480148+0.001801j
[2025-09-09 17:30:20] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -80.442929-0.000122j
[2025-09-09 17:30:55] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -80.654155-0.000656j
[2025-09-09 17:31:30] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -80.575925+0.001718j
[2025-09-09 17:32:05] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -80.478306+0.001646j
[2025-09-09 17:32:40] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -80.579970-0.002536j
[2025-09-09 17:33:15] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -80.599481-0.001558j
[2025-09-09 17:33:50] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -80.580149+0.003130j
[2025-09-09 17:34:25] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -80.647431+0.001084j
[2025-09-09 17:35:06] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -80.716895-0.000976j
[2025-09-09 17:35:41] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -80.672194+0.004793j
[2025-09-09 17:36:16] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -80.792952+0.004279j
[2025-09-09 17:36:51] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -80.713831-0.002616j
[2025-09-09 17:37:26] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -80.698451-0.002422j
[2025-09-09 17:38:01] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -80.563009-0.003285j
[2025-09-09 17:38:36] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -80.626178-0.002252j
[2025-09-09 17:39:11] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -80.523654+0.000650j
[2025-09-09 17:39:46] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -80.562788-0.004517j
[2025-09-09 17:40:22] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -80.346415-0.006305j
[2025-09-09 17:40:57] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -80.280011+0.001576j
[2025-09-09 17:41:32] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -80.434127+0.003282j
[2025-09-09 17:42:07] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -80.445771+0.006398j
[2025-09-09 17:42:42] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -80.599299+0.000270j
[2025-09-09 17:43:17] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -80.567229+0.004161j
[2025-09-09 17:43:52] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -80.542572-0.005859j
[2025-09-09 17:44:27] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -80.569158+0.002167j
[2025-09-09 17:45:02] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -80.464001-0.002765j
[2025-09-09 17:45:37] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -80.454145-0.006050j
[2025-09-09 17:46:12] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -80.440888+0.002701j
[2025-09-09 17:46:47] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -80.386977+0.001706j
[2025-09-09 17:47:22] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -80.496826-0.003234j
[2025-09-09 17:47:58] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -80.537162-0.002796j
[2025-09-09 17:48:33] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -80.386752-0.011411j
[2025-09-09 17:49:08] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -80.441940-0.000401j
[2025-09-09 17:49:08] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-09 17:49:43] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -80.516037+0.001911j
[2025-09-09 17:50:18] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -80.509657+0.002331j
[2025-09-09 17:50:53] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -80.485032-0.006578j
[2025-09-09 17:51:28] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -80.618652-0.000378j
[2025-09-09 17:52:03] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -80.547193+0.001796j
[2025-09-09 17:52:38] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -80.503784-0.002335j
[2025-09-09 17:53:13] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -80.355117+0.004909j
[2025-09-09 17:53:48] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -80.334605-0.003787j
[2025-09-09 17:54:23] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -80.457737+0.003217j
[2025-09-09 17:54:58] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -80.418293+0.006056j
[2025-09-09 17:55:33] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -80.501538+0.001117j
[2025-09-09 17:56:08] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -80.463196+0.001691j
[2025-09-09 17:56:43] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -80.301173-0.004794j
[2025-09-09 17:57:18] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -80.463369-0.002207j
[2025-09-09 17:57:53] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -80.265758+0.001564j
[2025-09-09 17:58:29] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -80.382608+0.000361j
[2025-09-09 17:59:04] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -80.475833+0.004841j
[2025-09-09 17:59:39] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -80.581366-0.001657j
[2025-09-09 18:00:14] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -80.463283-0.002306j
[2025-09-09 18:00:49] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -80.637773+0.000052j
[2025-09-09 18:01:24] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -80.464371-0.002548j
[2025-09-09 18:01:59] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -80.670016-0.000967j
[2025-09-09 18:02:34] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -80.527951-0.001559j
[2025-09-09 18:03:09] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -80.548893-0.001067j
[2025-09-09 18:03:44] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -80.386602+0.000579j
[2025-09-09 18:04:19] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -80.468983-0.000648j
[2025-09-09 18:04:54] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -80.513174+0.002152j
[2025-09-09 18:05:29] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -80.550251+0.006330j
[2025-09-09 18:06:04] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -80.474927-0.001121j
[2025-09-09 18:06:39] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -80.422068+0.004318j
[2025-09-09 18:07:14] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -80.521826+0.001273j
[2025-09-09 18:07:49] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -80.567030+0.002689j
[2025-09-09 18:08:24] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -80.541896-0.001434j
[2025-09-09 18:08:59] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -80.390842-0.005252j
[2025-09-09 18:09:35] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -80.644059-0.001292j
[2025-09-09 18:10:10] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -80.484750-0.000660j
[2025-09-09 18:10:45] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -80.451067-0.006051j
[2025-09-09 18:11:20] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -80.516119-0.003123j
[2025-09-09 18:11:55] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -80.471155+0.005146j
[2025-09-09 18:12:30] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -80.503117+0.000365j
[2025-09-09 18:13:05] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -80.453921+0.000071j
[2025-09-09 18:13:40] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -80.459010-0.003108j
[2025-09-09 18:14:15] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -80.592924+0.000254j
[2025-09-09 18:14:50] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -80.548476+0.002951j
[2025-09-09 18:15:25] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -80.606156-0.002699j
[2025-09-09 18:16:00] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -80.768147-0.000188j
[2025-09-09 18:16:35] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -80.683354-0.022892j
[2025-09-09 18:17:10] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -80.441825+0.006626j
[2025-09-09 18:17:45] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -80.700918-0.002524j
[2025-09-09 18:18:20] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -80.618625+0.004638j
[2025-09-09 18:18:55] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -80.456717+0.000495j
[2025-09-09 18:19:31] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -80.474941+0.000403j
[2025-09-09 18:20:06] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -80.606243-0.000413j
[2025-09-09 18:20:41] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -80.411865+0.002781j
[2025-09-09 18:21:16] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -80.491818+0.002171j
[2025-09-09 18:21:51] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -80.705738+0.005865j
[2025-09-09 18:22:26] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -80.498525-0.004462j
[2025-09-09 18:23:01] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -80.443740-0.002244j
[2025-09-09 18:23:36] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -80.452752+0.002480j
[2025-09-09 18:24:11] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -80.530333-0.001164j
[2025-09-09 18:24:46] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -80.383558-0.001271j
[2025-09-09 18:25:21] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -80.629820-0.003203j
[2025-09-09 18:25:56] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -80.606346-0.001265j
[2025-09-09 18:26:31] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -80.559571-0.002933j
[2025-09-09 18:27:06] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -80.584920+0.000445j
[2025-09-09 18:27:41] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -80.652243-0.003629j
[2025-09-09 18:28:16] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -80.591460+0.001762j
[2025-09-09 18:28:51] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -80.505535+0.009281j
[2025-09-09 18:29:26] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -80.498941-0.001263j
[2025-09-09 18:30:01] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -80.515674-0.004199j
[2025-09-09 18:30:37] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -80.499345-0.001505j
[2025-09-09 18:31:12] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -80.425256+0.002268j
[2025-09-09 18:31:46] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -80.452981+0.001051j
[2025-09-09 18:32:21] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -80.577212+0.004470j
[2025-09-09 18:32:57] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -80.477853-0.005724j
[2025-09-09 18:33:32] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -80.532902+0.000717j
[2025-09-09 18:34:07] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -80.481566+0.005013j
[2025-09-09 18:34:42] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -80.473647+0.001420j
[2025-09-09 18:35:17] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -80.522158+0.002601j
[2025-09-09 18:35:52] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -80.299373+0.009228j
[2025-09-09 18:36:27] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -80.413971+0.001028j
[2025-09-09 18:37:02] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -80.348169-0.004533j
[2025-09-09 18:37:37] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -80.433779-0.002863j
[2025-09-09 18:38:12] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -80.612112-0.003181j
[2025-09-09 18:38:47] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -80.403818-0.002550j
[2025-09-09 18:39:22] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -80.302241+0.002545j
[2025-09-09 18:39:57] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -80.300705-0.000462j
[2025-09-09 18:40:32] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -80.245440-0.001740j
[2025-09-09 18:41:07] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -80.199702-0.004605j
[2025-09-09 18:41:42] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -80.299215+0.001985j
[2025-09-09 18:42:17] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -80.360187+0.000782j
[2025-09-09 18:42:52] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -80.417185+0.001249j
[2025-09-09 18:43:27] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -80.265118+0.002120j
[2025-09-09 18:44:02] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -80.413985+0.002259j
[2025-09-09 18:44:38] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -80.423275+0.002013j
[2025-09-09 18:45:13] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -80.566048+0.001198j
[2025-09-09 18:45:48] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -80.599882+0.000276j
[2025-09-09 18:46:23] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -80.513440-0.000827j
[2025-09-09 18:46:57] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -80.542534-0.000989j
[2025-09-09 18:47:33] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -80.534523-0.003371j
[2025-09-09 18:48:07] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -80.620206-0.000911j
[2025-09-09 18:48:43] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -80.640756-0.000538j
[2025-09-09 18:49:18] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -80.582044-0.002665j
[2025-09-09 18:49:53] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -80.594538+0.001851j
[2025-09-09 18:50:28] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -80.624005+0.004783j
[2025-09-09 18:50:28] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-09 18:51:03] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -80.624155+0.003300j
[2025-09-09 18:51:38] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -80.464511+0.004199j
[2025-09-09 18:52:13] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -80.443823+0.000910j
[2025-09-09 18:52:48] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -80.627995-0.001007j
[2025-09-09 18:53:23] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -80.625796+0.000821j
[2025-09-09 18:53:58] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -80.547340-0.004725j
[2025-09-09 18:54:33] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -80.551707-0.002105j
[2025-09-09 18:55:08] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -80.514757-0.001070j
[2025-09-09 18:55:43] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -80.449369-0.001470j
[2025-09-09 18:56:18] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -80.568185+0.001652j
[2025-09-09 18:56:53] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -80.404022-0.003404j
[2025-09-09 18:57:28] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -80.397549+0.000864j
[2025-09-09 18:58:04] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -80.309094+0.004309j
[2025-09-09 18:58:39] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -80.468551-0.001417j
[2025-09-09 18:59:14] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -80.534759-0.002622j
[2025-09-09 18:59:49] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -80.635673-0.002776j
[2025-09-09 19:00:24] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -80.650237-0.000211j
[2025-09-09 19:00:59] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -80.723428+0.003196j
[2025-09-09 19:01:34] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -80.691104-0.002009j
[2025-09-09 19:02:09] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -80.582121-0.003031j
[2025-09-09 19:02:44] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -80.590734-0.006559j
[2025-09-09 19:03:19] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -80.635615-0.009925j
[2025-09-09 19:03:54] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -80.643744-0.003301j
[2025-09-09 19:04:29] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -80.693664-0.001594j
[2025-09-09 19:05:04] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -80.641097-0.001026j
[2025-09-09 19:05:39] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -80.782056-0.000010j
[2025-09-09 19:06:14] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -80.584593-0.002948j
[2025-09-09 19:06:49] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -80.476649+0.000923j
[2025-09-09 19:07:25] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -80.629313-0.004901j
[2025-09-09 19:08:00] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -80.695333+0.001976j
[2025-09-09 19:08:34] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -80.523448-0.002331j
[2025-09-09 19:09:09] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -80.561303-0.003188j
[2025-09-09 19:09:44] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -80.594741+0.000515j
[2025-09-09 19:10:20] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -80.561667-0.006001j
[2025-09-09 19:10:55] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -80.480657-0.000722j
[2025-09-09 19:11:30] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -80.539763+0.001948j
[2025-09-09 19:12:05] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -80.548068-0.002944j
[2025-09-09 19:12:40] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -80.546005+0.001754j
[2025-09-09 19:13:15] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -80.666733-0.000474j
[2025-09-09 19:13:50] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -80.675346+0.000279j
[2025-09-09 19:14:25] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -80.636154+0.001124j
[2025-09-09 19:15:00] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -80.732140-0.003330j
[2025-09-09 19:15:35] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -80.577586-0.004547j
[2025-09-09 19:16:10] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -80.533047-0.000117j
[2025-09-09 19:16:45] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -80.518098+0.003256j
[2025-09-09 19:17:20] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -80.572430-0.002400j
[2025-09-09 19:17:55] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -80.599011-0.000401j
[2025-09-09 19:18:30] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -80.447119-0.000610j
[2025-09-09 19:19:05] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -80.464708+0.004193j
[2025-09-09 19:19:40] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -80.433185+0.002332j
[2025-09-09 19:20:15] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -80.608978+0.004089j
[2025-09-09 19:20:50] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -80.746699+0.005289j
[2025-09-09 19:21:25] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -80.693998+0.001567j
[2025-09-09 19:22:00] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -80.658549-0.001135j
[2025-09-09 19:22:35] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -80.652044-0.000879j
[2025-09-09 19:23:10] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -80.696971-0.000799j
[2025-09-09 19:23:45] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -80.703250-0.002289j
[2025-09-09 19:24:20] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -80.685633+0.000388j
[2025-09-09 19:24:55] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -80.598891-0.002215j
[2025-09-09 19:25:30] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -80.540750+0.002921j
[2025-09-09 19:26:05] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -80.559628-0.002722j
[2025-09-09 19:26:40] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -80.452649-0.004490j
[2025-09-09 19:27:15] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -80.663516-0.000580j
[2025-09-09 19:27:50] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -80.626459+0.006090j
[2025-09-09 19:28:25] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -80.648515+0.010800j
[2025-09-09 19:29:00] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -80.534338-0.000880j
[2025-09-09 19:29:35] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -80.427839-0.002508j
[2025-09-09 19:30:10] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -80.547099+0.002267j
[2025-09-09 19:30:46] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -80.551468-0.001326j
[2025-09-09 19:31:21] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -80.389744-0.006468j
[2025-09-09 19:31:56] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -80.394404+0.003413j
[2025-09-09 19:32:31] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -80.472000+0.001152j
[2025-09-09 19:33:06] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -80.535265-0.000459j
[2025-09-09 19:33:41] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -80.469110-0.002562j
[2025-09-09 19:34:16] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -80.579673-0.010390j
[2025-09-09 19:34:51] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -80.503833-0.004429j
[2025-09-09 19:35:26] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -80.503894-0.000331j
[2025-09-09 19:36:01] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -80.523209-0.000355j
[2025-09-09 19:36:36] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -80.605650+0.001395j
[2025-09-09 19:37:11] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -80.625272+0.001993j
[2025-09-09 19:37:46] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -80.691200+0.003487j
[2025-09-09 19:38:21] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -80.576763+0.002614j
[2025-09-09 19:38:56] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -80.549390+0.002158j
[2025-09-09 19:39:31] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -80.514805-0.002157j
[2025-09-09 19:40:06] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -80.600506-0.004835j
[2025-09-09 19:40:41] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -80.413256-0.000031j
[2025-09-09 19:41:16] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -80.382048-0.006099j
[2025-09-09 19:41:51] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -80.583752+0.003354j
[2025-09-09 19:42:27] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -80.451598-0.002093j
[2025-09-09 19:43:02] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -80.433926-0.003950j
[2025-09-09 19:43:37] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -80.433445+0.002444j
[2025-09-09 19:44:12] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -80.524316-0.000989j
[2025-09-09 19:44:47] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -80.542880+0.002915j
[2025-09-09 19:45:22] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -80.549282+0.002571j
[2025-09-09 19:45:57] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -80.583659-0.002428j
[2025-09-09 19:46:32] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -80.586315-0.003934j
[2025-09-09 19:47:07] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -80.562771-0.016541j
[2025-09-09 19:47:42] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -80.454567-0.000136j
[2025-09-09 19:48:17] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -80.451909-0.006331j
[2025-09-09 19:48:52] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -80.566706+0.001237j
[2025-09-09 19:49:27] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -80.435115-0.002420j
[2025-09-09 19:50:02] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -80.463663-0.000938j
[2025-09-09 19:50:37] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -80.486079-0.002757j
[2025-09-09 19:51:12] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -80.387264-0.002653j
[2025-09-09 19:51:47] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -80.435224-0.002629j
[2025-09-09 19:51:47] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-09 19:52:22] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -80.383043-0.000534j
[2025-09-09 19:52:57] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -80.457988-0.002653j
[2025-09-09 19:53:32] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -80.423289-0.003807j
[2025-09-09 19:54:07] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -80.446049-0.001683j
[2025-09-09 19:54:42] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -80.421362+0.005276j
[2025-09-09 19:55:17] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -80.414560-0.002997j
[2025-09-09 19:55:52] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -80.336884+0.001511j
[2025-09-09 19:56:27] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -80.335878-0.001351j
[2025-09-09 19:57:03] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -80.318950-0.001288j
[2025-09-09 19:57:38] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -80.393861+0.000996j
[2025-09-09 19:58:13] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -80.500714-0.003404j
[2025-09-09 19:58:48] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -80.469845-0.000302j
[2025-09-09 19:59:23] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -80.431304-0.000538j
[2025-09-09 19:59:58] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -80.586776+0.001841j
[2025-09-09 20:00:33] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -80.551369+0.000767j
[2025-09-09 20:01:08] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -80.551798+0.000440j
[2025-09-09 20:01:43] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -80.586106+0.007784j
[2025-09-09 20:02:18] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -80.516984+0.004166j
[2025-09-09 20:02:54] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -80.546724+0.007012j
[2025-09-09 20:03:29] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -80.386663+0.000756j
[2025-09-09 20:04:04] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -80.561296-0.001883j
[2025-09-09 20:04:39] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -80.538653-0.001999j
[2025-09-09 20:05:14] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -80.532982+0.001635j
[2025-09-09 20:05:49] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -80.536737+0.002625j
[2025-09-09 20:06:24] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -80.500989-0.003027j
[2025-09-09 20:06:59] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -80.567192+0.001445j
[2025-09-09 20:07:38] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -80.570901+0.006112j
[2025-09-09 20:08:13] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -80.414030-0.000666j
[2025-09-09 20:08:47] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -80.441707-0.003102j
[2025-09-09 20:09:22] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -80.389301+0.001383j
[2025-09-09 20:09:58] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -80.457498+0.007202j
[2025-09-09 20:10:33] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -80.605753+0.003730j
[2025-09-09 20:11:07] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -80.579995-0.002762j
[2025-09-09 20:11:43] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -80.642196-0.000801j
[2025-09-09 20:12:18] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -80.513031+0.006425j
[2025-09-09 20:12:53] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -80.382601-0.002588j
[2025-09-09 20:13:28] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -80.556578+0.002640j
[2025-09-09 20:14:03] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -80.563188+0.000845j
[2025-09-09 20:14:38] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -80.614390-0.004388j
[2025-09-09 20:15:13] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -80.709256-0.001901j
[2025-09-09 20:15:48] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -80.705285-0.002346j
[2025-09-09 20:16:23] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -80.721078-0.004929j
[2025-09-09 20:16:58] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -80.708466+0.002725j
[2025-09-09 20:17:33] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -80.632495-0.000293j
[2025-09-09 20:18:08] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -80.651938+0.006840j
[2025-09-09 20:18:43] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -80.661399+0.006738j
[2025-09-09 20:19:18] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -80.484799+0.000276j
[2025-09-09 20:19:53] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -80.446927+0.000379j
[2025-09-09 20:20:28] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -80.479514-0.000061j
[2025-09-09 20:21:03] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -80.469911-0.004054j
[2025-09-09 20:21:38] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -80.357070+0.004569j
[2025-09-09 20:22:13] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -80.445282-0.000729j
[2025-09-09 20:22:48] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -80.274384-0.006969j
[2025-09-09 20:23:23] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -80.339480+0.001740j
[2025-09-09 20:23:59] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -80.493665-0.003488j
[2025-09-09 20:24:34] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -80.471172+0.002056j
[2025-09-09 20:25:09] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -80.571578+0.001668j
[2025-09-09 20:25:44] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -80.587110-0.000150j
[2025-09-09 20:26:19] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -80.531701-0.003684j
[2025-09-09 20:26:54] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -80.543546+0.001829j
[2025-09-09 20:27:29] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -80.570022+0.003358j
[2025-09-09 20:28:04] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -80.557071+0.002992j
[2025-09-09 20:28:39] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -80.572192-0.005364j
[2025-09-09 20:29:14] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -80.584723-0.000201j
[2025-09-09 20:29:49] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -80.337824-0.002577j
[2025-09-09 20:30:24] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -80.408735-0.000680j
[2025-09-09 20:30:59] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -80.342292+0.005532j
[2025-09-09 20:31:35] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -80.441610+0.000355j
[2025-09-09 20:32:10] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -80.514566+0.002993j
[2025-09-09 20:32:45] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -80.468444+0.005918j
[2025-09-09 20:33:20] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -80.387428+0.002000j
[2025-09-09 20:33:55] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -80.580459+0.003543j
[2025-09-09 20:34:30] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -80.529971-0.000746j
[2025-09-09 20:35:05] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -80.546315-0.000072j
[2025-09-09 20:35:40] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -80.479235+0.002539j
[2025-09-09 20:36:15] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -80.500460+0.001137j
[2025-09-09 20:36:50] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -80.714170+0.003442j
[2025-09-09 20:37:25] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -80.478436-0.001672j
[2025-09-09 20:38:00] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -80.348334+0.005218j
[2025-09-09 20:38:35] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -80.317282+0.002663j
[2025-09-09 20:39:10] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -80.464334+0.006294j
[2025-09-09 20:39:45] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -80.466399+0.003735j
[2025-09-09 20:40:20] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -80.351359-0.000334j
[2025-09-09 20:40:55] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -80.334199+0.000594j
[2025-09-09 20:41:30] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -80.509952-0.006721j
[2025-09-09 20:42:05] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -80.414968-0.004297j
[2025-09-09 20:42:40] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -80.456799+0.000157j
[2025-09-09 20:43:15] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -80.450424+0.002649j
[2025-09-09 20:43:50] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -80.501131-0.001405j
[2025-09-09 20:44:25] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -80.564444+0.003687j
[2025-09-09 20:45:01] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -80.464373-0.001513j
[2025-09-09 20:45:36] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -80.516154-0.000804j
[2025-09-09 20:46:11] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -80.483554+0.000002j
[2025-09-09 20:46:46] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -80.614982+0.003826j
[2025-09-09 20:47:12] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -80.610133-0.005901j
[2025-09-09 20:47:36] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -80.539521+0.002921j
[2025-09-09 20:47:59] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -80.593118+0.003487j
[2025-09-09 20:48:22] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -80.431746-0.004888j
[2025-09-09 20:48:46] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -80.486032+0.004909j
[2025-09-09 20:49:09] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -80.605658-0.001185j
[2025-09-09 20:49:32] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -80.550554+0.000591j
[2025-09-09 20:49:56] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -80.421720-0.002845j
[2025-09-09 20:50:19] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -80.558839-0.002345j
[2025-09-09 20:50:43] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -80.517947+0.003807j
[2025-09-09 20:51:06] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -80.499382+0.003559j
[2025-09-09 20:51:06] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-09 20:51:06] ✅ Training completed | Restarts: 2
[2025-09-09 20:51:06] ============================================================
[2025-09-09 20:51:06] Training completed | Runtime: 36836.2s
[2025-09-09 20:51:16] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-09 20:51:16] ============================================================
