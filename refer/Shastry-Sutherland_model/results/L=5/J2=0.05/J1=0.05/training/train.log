[2025-09-08 00:15:05] ==================================================
[2025-09-08 00:15:05] GCNN for Shastry-Sutherland Model
[2025-09-08 00:15:05] ==================================================
[2025-09-08 00:15:05] System parameters:
[2025-09-08 00:15:05]   - System size: L=5, N=100
[2025-09-08 00:15:05]   - System parameters: J1=0.05, J2=0.05, Q=0.95
[2025-09-08 00:15:05] --------------------------------------------------
[2025-09-08 00:15:05] Model parameters:
[2025-09-08 00:15:05]   - Number of layers = 4
[2025-09-08 00:15:05]   - Number of features = 4
[2025-09-08 00:15:05]   - Total parameters = 19628
[2025-09-08 00:15:05] --------------------------------------------------
[2025-09-08 00:15:05] Training parameters:
[2025-09-08 00:15:05]   - Learning rate: 0.015
[2025-09-08 00:15:05]   - Total iterations: 2250
[2025-09-08 00:15:05]   - Annealing cycles: 4
[2025-09-08 00:15:05]   - Initial period: 150
[2025-09-08 00:15:05]   - Period multiplier: 2.0
[2025-09-08 00:15:05]   - Temperature range: 0.0-1.0
[2025-09-08 00:15:05]   - Samples: 16384
[2025-09-08 00:15:05]   - Discarded samples: 0
[2025-09-08 00:15:05]   - Chunk size: 2048
[2025-09-08 00:15:05]   - Diagonal shift: 0.2
[2025-09-08 00:15:05]   - Gradient clipping: 1.0
[2025-09-08 00:15:05]   - Checkpoint enabled: interval=250
[2025-09-08 00:15:05]   - Checkpoint directory: results/L=5/J2=0.05/J1=0.05/training/checkpoints
[2025-09-08 00:15:05] --------------------------------------------------
[2025-09-08 00:15:05] Device status:
[2025-09-08 00:15:05]   - Devices model: NVIDIA H200 NVL
[2025-09-08 00:15:05]   - Number of devices: 1
[2025-09-08 00:15:05]   - Sharding: True
[2025-09-08 00:15:05] ============================================================
[2025-09-08 00:16:29] [Iter 1/2250] R0[0/150], Temp: 1.0000, Energy: 3.124315-0.000158j
[2025-09-08 00:17:15] [Iter 2/2250] R0[1/150], Temp: 0.9999, Energy: 3.124566-0.000085j
[2025-09-08 00:18:00] [Iter 3/2250] R0[2/150], Temp: 0.9996, Energy: 3.124803-0.000234j
[2025-09-08 00:18:46] [Iter 4/2250] R0[3/150], Temp: 0.9990, Energy: 3.124488+0.000277j
[2025-09-08 00:19:31] [Iter 5/2250] R0[4/150], Temp: 0.9982, Energy: 3.124597+0.000107j
[2025-09-08 00:20:17] [Iter 6/2250] R0[5/150], Temp: 0.9973, Energy: 3.124600+0.000002j
[2025-09-08 00:21:02] [Iter 7/2250] R0[6/150], Temp: 0.9961, Energy: 3.124373-0.000005j
[2025-09-08 00:21:48] [Iter 8/2250] R0[7/150], Temp: 0.9946, Energy: 3.124568+0.000122j
[2025-09-08 00:22:34] [Iter 9/2250] R0[8/150], Temp: 0.9930, Energy: 3.124859-0.000325j
[2025-09-08 00:23:19] [Iter 10/2250] R0[9/150], Temp: 0.9911, Energy: 3.124793-0.000179j
[2025-09-08 00:24:04] [Iter 11/2250] R0[10/150], Temp: 0.9891, Energy: 3.124527-0.000315j
[2025-09-08 00:24:50] [Iter 12/2250] R0[11/150], Temp: 0.9868, Energy: 3.124516+0.000093j
[2025-09-08 00:25:36] [Iter 13/2250] R0[12/150], Temp: 0.9843, Energy: 3.124520+0.000074j
[2025-09-08 00:26:21] [Iter 14/2250] R0[13/150], Temp: 0.9816, Energy: 3.124664-0.000259j
[2025-09-08 00:27:07] [Iter 15/2250] R0[14/150], Temp: 0.9787, Energy: 3.124644-0.000195j
[2025-09-08 00:27:52] [Iter 16/2250] R0[15/150], Temp: 0.9755, Energy: 3.124627-0.000129j
[2025-09-08 00:28:38] [Iter 17/2250] R0[16/150], Temp: 0.9722, Energy: 3.124494-0.000725j
[2025-09-08 00:29:23] [Iter 18/2250] R0[17/150], Temp: 0.9686, Energy: 3.124451+0.000141j
[2025-09-08 00:30:09] [Iter 19/2250] R0[18/150], Temp: 0.9649, Energy: 3.124405-0.000412j
[2025-09-08 00:30:55] [Iter 20/2250] R0[19/150], Temp: 0.9609, Energy: 3.124635-0.000186j
[2025-09-08 00:31:40] [Iter 21/2250] R0[20/150], Temp: 0.9568, Energy: 3.124476+0.000095j
[2025-09-08 00:32:25] [Iter 22/2250] R0[21/150], Temp: 0.9524, Energy: 3.124373-0.000698j
[2025-09-08 00:33:11] [Iter 23/2250] R0[22/150], Temp: 0.9479, Energy: 3.124418+0.000144j
[2025-09-08 00:33:56] [Iter 24/2250] R0[23/150], Temp: 0.9431, Energy: 3.124378+0.000159j
[2025-09-08 00:34:42] [Iter 25/2250] R0[24/150], Temp: 0.9382, Energy: 3.124442-0.000099j
[2025-09-08 00:35:27] [Iter 26/2250] R0[25/150], Temp: 0.9330, Energy: 3.124336-0.000447j
[2025-09-08 00:36:13] [Iter 27/2250] R0[26/150], Temp: 0.9277, Energy: 3.124354-0.000200j
[2025-09-08 00:36:59] [Iter 28/2250] R0[27/150], Temp: 0.9222, Energy: 3.124455-0.000073j
[2025-09-08 00:37:44] [Iter 29/2250] R0[28/150], Temp: 0.9165, Energy: 3.124367-0.000338j
[2025-09-08 00:38:30] [Iter 30/2250] R0[29/150], Temp: 0.9106, Energy: 3.124566-0.000355j
[2025-09-08 00:39:15] [Iter 31/2250] R0[30/150], Temp: 0.9045, Energy: 3.124072+0.000295j
[2025-09-08 00:40:01] [Iter 32/2250] R0[31/150], Temp: 0.8983, Energy: 3.124650-0.000736j
[2025-09-08 00:40:46] [Iter 33/2250] R0[32/150], Temp: 0.8918, Energy: 3.124199+0.000148j
[2025-09-08 00:41:32] [Iter 34/2250] R0[33/150], Temp: 0.8853, Energy: 3.124597-0.000424j
[2025-09-08 00:42:18] [Iter 35/2250] R0[34/150], Temp: 0.8785, Energy: 3.124308+0.000238j
[2025-09-08 00:43:03] [Iter 36/2250] R0[35/150], Temp: 0.8716, Energy: 3.124001-0.000313j
[2025-09-08 00:43:49] [Iter 37/2250] R0[36/150], Temp: 0.8645, Energy: 3.123808+0.000119j
[2025-09-08 00:44:34] [Iter 38/2250] R0[37/150], Temp: 0.8572, Energy: 3.123942+0.000656j
[2025-09-08 00:45:20] [Iter 39/2250] R0[38/150], Temp: 0.8498, Energy: 3.124023-0.000004j
[2025-09-08 00:46:05] [Iter 40/2250] R0[39/150], Temp: 0.8423, Energy: 3.124102-0.000190j
[2025-09-08 00:46:51] [Iter 41/2250] R0[40/150], Temp: 0.8346, Energy: 3.123862-0.000339j
[2025-09-08 00:47:36] [Iter 42/2250] R0[41/150], Temp: 0.8267, Energy: 3.124284-0.000138j
[2025-09-08 00:48:22] [Iter 43/2250] R0[42/150], Temp: 0.8187, Energy: 3.124118-0.000308j
[2025-09-08 00:49:07] [Iter 44/2250] R0[43/150], Temp: 0.8106, Energy: 3.123697+0.000202j
[2025-09-08 00:49:53] [Iter 45/2250] R0[44/150], Temp: 0.8023, Energy: 3.124156-0.000599j
[2025-09-08 00:50:39] [Iter 46/2250] R0[45/150], Temp: 0.7939, Energy: 3.123457+0.000384j
[2025-09-08 00:51:24] [Iter 47/2250] R0[46/150], Temp: 0.7854, Energy: 3.123901-0.000322j
[2025-09-08 00:52:10] [Iter 48/2250] R0[47/150], Temp: 0.7767, Energy: 3.123858-0.000454j
[2025-09-08 00:52:55] [Iter 49/2250] R0[48/150], Temp: 0.7679, Energy: 3.123301+0.000732j
[2025-09-08 00:53:41] [Iter 50/2250] R0[49/150], Temp: 0.7590, Energy: 3.124018-0.000164j
[2025-09-08 00:54:26] [Iter 51/2250] R0[50/150], Temp: 0.7500, Energy: 3.123712-0.000178j
[2025-09-08 00:55:12] [Iter 52/2250] R0[51/150], Temp: 0.7409, Energy: 3.123473+0.000501j
[2025-09-08 00:55:57] [Iter 53/2250] R0[52/150], Temp: 0.7316, Energy: 3.123699-0.000104j
[2025-09-08 00:56:43] [Iter 54/2250] R0[53/150], Temp: 0.7223, Energy: 3.123719-0.000513j
[2025-09-08 00:57:28] [Iter 55/2250] R0[54/150], Temp: 0.7129, Energy: 3.123207+0.000754j
[2025-09-08 00:58:14] [Iter 56/2250] R0[55/150], Temp: 0.7034, Energy: 3.123596-0.000172j
[2025-09-08 00:58:59] [Iter 57/2250] R0[56/150], Temp: 0.6938, Energy: 3.123398-0.000113j
[2025-09-08 00:59:45] [Iter 58/2250] R0[57/150], Temp: 0.6841, Energy: 3.123160+0.000083j
[2025-09-08 01:00:30] [Iter 59/2250] R0[58/150], Temp: 0.6743, Energy: 3.122890+0.000563j
[2025-09-08 01:01:16] [Iter 60/2250] R0[59/150], Temp: 0.6644, Energy: 3.123320-0.000315j
[2025-09-08 01:02:01] [Iter 61/2250] R0[60/150], Temp: 0.6545, Energy: 3.123593-0.000736j
[2025-09-08 01:02:47] [Iter 62/2250] R0[61/150], Temp: 0.6445, Energy: 3.123092-0.000000j
[2025-09-08 01:03:33] [Iter 63/2250] R0[62/150], Temp: 0.6345, Energy: 3.123174-0.000453j
[2025-09-08 01:04:18] [Iter 64/2250] R0[63/150], Temp: 0.6243, Energy: 3.123374-0.000984j
[2025-09-08 01:05:04] [Iter 65/2250] R0[64/150], Temp: 0.6142, Energy: 3.122847+0.000435j
[2025-09-08 01:05:49] [Iter 66/2250] R0[65/150], Temp: 0.6040, Energy: 3.122129+0.000304j
[2025-09-08 01:06:35] [Iter 67/2250] R0[66/150], Temp: 0.5937, Energy: 3.122115+0.000030j
[2025-09-08 01:07:20] [Iter 68/2250] R0[67/150], Temp: 0.5834, Energy: 3.122772-0.000993j
[2025-09-08 01:08:06] [Iter 69/2250] R0[68/150], Temp: 0.5730, Energy: 3.122486+0.000098j
[2025-09-08 01:08:51] [Iter 70/2250] R0[69/150], Temp: 0.5627, Energy: 3.122078-0.000083j
[2025-09-08 01:09:37] [Iter 71/2250] R0[70/150], Temp: 0.5523, Energy: 3.122412-0.000815j
[2025-09-08 01:10:22] [Iter 72/2250] R0[71/150], Temp: 0.5418, Energy: 3.121209+0.001126j
[2025-09-08 01:11:08] [Iter 73/2250] R0[72/150], Temp: 0.5314, Energy: 3.121579+0.000269j
[2025-09-08 01:11:53] [Iter 74/2250] R0[73/150], Temp: 0.5209, Energy: 3.121274+0.000525j
[2025-09-08 01:12:39] [Iter 75/2250] R0[74/150], Temp: 0.5105, Energy: 3.121061-0.000064j
[2025-09-08 01:13:24] [Iter 76/2250] R0[75/150], Temp: 0.5000, Energy: 3.121233-0.000373j
[2025-09-08 01:14:10] [Iter 77/2250] R0[76/150], Temp: 0.4895, Energy: 3.120572+0.000542j
[2025-09-08 01:14:55] [Iter 78/2250] R0[77/150], Temp: 0.4791, Energy: 3.121164-0.000686j
[2025-09-08 01:15:41] [Iter 79/2250] R0[78/150], Temp: 0.4686, Energy: 3.120833-0.000484j
[2025-09-08 01:16:27] [Iter 80/2250] R0[79/150], Temp: 0.4582, Energy: 3.120452+0.000116j
[2025-09-08 01:17:12] [Iter 81/2250] R0[80/150], Temp: 0.4477, Energy: 3.120325-0.000719j
[2025-09-08 01:17:58] [Iter 82/2250] R0[81/150], Temp: 0.4373, Energy: 3.118542+0.001716j
[2025-09-08 01:18:43] [Iter 83/2250] R0[82/150], Temp: 0.4270, Energy: 3.118523+0.000548j
[2025-09-08 01:19:29] [Iter 84/2250] R0[83/150], Temp: 0.4166, Energy: 3.119568-0.001817j
[2025-09-08 01:20:14] [Iter 85/2250] R0[84/150], Temp: 0.4063, Energy: 3.117890-0.000298j
[2025-09-08 01:21:00] [Iter 86/2250] R0[85/150], Temp: 0.3960, Energy: 3.118202+0.000134j
[2025-09-08 01:21:46] [Iter 87/2250] R0[86/150], Temp: 0.3858, Energy: 3.116189+0.002379j
[2025-09-08 01:22:31] [Iter 88/2250] R0[87/150], Temp: 0.3757, Energy: 3.117074-0.000052j
[2025-09-08 01:23:17] [Iter 89/2250] R0[88/150], Temp: 0.3655, Energy: 3.116749-0.000383j
[2025-09-08 01:24:02] [Iter 90/2250] R0[89/150], Temp: 0.3555, Energy: 3.116135+0.000290j
[2025-09-08 01:24:48] [Iter 91/2250] R0[90/150], Temp: 0.3455, Energy: 3.114654+0.001812j
[2025-09-08 01:25:33] [Iter 92/2250] R0[91/150], Temp: 0.3356, Energy: 3.115759-0.002183j
[2025-09-08 01:26:19] [Iter 93/2250] R0[92/150], Temp: 0.3257, Energy: 3.112793+0.002205j
[2025-09-08 01:27:04] [Iter 94/2250] R0[93/150], Temp: 0.3159, Energy: 3.111879+0.002763j
[2025-09-08 01:27:50] [Iter 95/2250] R0[94/150], Temp: 0.3062, Energy: 3.112543+0.000763j
[2025-09-08 01:28:35] [Iter 96/2250] R0[95/150], Temp: 0.2966, Energy: 3.110877+0.001086j
[2025-09-08 01:29:21] [Iter 97/2250] R0[96/150], Temp: 0.2871, Energy: 3.108897+0.000750j
[2025-09-08 01:30:06] [Iter 98/2250] R0[97/150], Temp: 0.2777, Energy: 3.110416-0.001957j
[2025-09-08 01:30:52] [Iter 99/2250] R0[98/150], Temp: 0.2684, Energy: 3.107187+0.001984j
[2025-09-08 01:31:37] [Iter 100/2250] R0[99/150], Temp: 0.2591, Energy: 3.105081+0.001351j
[2025-09-08 01:32:23] [Iter 101/2250] R0[100/150], Temp: 0.2500, Energy: 3.104400+0.001316j
[2025-09-08 01:33:08] [Iter 102/2250] R0[101/150], Temp: 0.2410, Energy: 3.104234-0.002446j
[2025-09-08 01:33:54] [Iter 103/2250] R0[102/150], Temp: 0.2321, Energy: 3.099747+0.001338j
[2025-09-08 01:34:39] [Iter 104/2250] R0[103/150], Temp: 0.2233, Energy: 3.101032-0.002018j
[2025-09-08 01:35:25] [Iter 105/2250] R0[104/150], Temp: 0.2146, Energy: 3.098661-0.001676j
[2025-09-08 01:36:10] [Iter 106/2250] R0[105/150], Temp: 0.2061, Energy: 3.093760+0.002113j
[2025-09-08 01:36:56] [Iter 107/2250] R0[106/150], Temp: 0.1977, Energy: 3.092029-0.001124j
[2025-09-08 01:37:42] [Iter 108/2250] R0[107/150], Temp: 0.1894, Energy: 3.089145+0.000310j
[2025-09-08 01:38:27] [Iter 109/2250] R0[108/150], Temp: 0.1813, Energy: 3.085641-0.000570j
[2025-09-08 01:39:13] [Iter 110/2250] R0[109/150], Temp: 0.1733, Energy: 3.080047+0.002674j
[2025-09-08 01:39:58] [Iter 111/2250] R0[110/150], Temp: 0.1654, Energy: 3.079439-0.004021j
[2025-09-08 01:40:44] [Iter 112/2250] R0[111/150], Temp: 0.1577, Energy: 3.069212+0.002830j
[2025-09-08 01:41:29] [Iter 113/2250] R0[112/150], Temp: 0.1502, Energy: 3.066640-0.002456j
[2025-09-08 01:42:15] [Iter 114/2250] R0[113/150], Temp: 0.1428, Energy: 3.061028-0.002032j
[2025-09-08 01:43:00] [Iter 115/2250] R0[114/150], Temp: 0.1355, Energy: 3.055430-0.002668j
[2025-09-08 01:43:46] [Iter 116/2250] R0[115/150], Temp: 0.1284, Energy: 3.045458+0.001002j
[2025-09-08 01:44:31] [Iter 117/2250] R0[116/150], Temp: 0.1215, Energy: 3.036717+0.001992j
[2025-09-08 01:45:17] [Iter 118/2250] R0[117/150], Temp: 0.1147, Energy: 3.027500+0.000705j
[2025-09-08 01:46:02] [Iter 119/2250] R0[118/150], Temp: 0.1082, Energy: 3.012773+0.011253j
[2025-09-08 01:46:48] [Iter 120/2250] R0[119/150], Temp: 0.1017, Energy: 3.004389+0.000662j
[2025-09-08 01:47:33] [Iter 121/2250] R0[120/150], Temp: 0.0955, Energy: 2.991890-0.003057j
[2025-09-08 01:48:19] [Iter 122/2250] R0[121/150], Temp: 0.0894, Energy: 2.974247+0.000021j
[2025-09-08 01:49:05] [Iter 123/2250] R0[122/150], Temp: 0.0835, Energy: 2.962475-0.007006j
[2025-09-08 01:49:50] [Iter 124/2250] R0[123/150], Temp: 0.0778, Energy: 2.939635-0.003275j
[2025-09-08 01:50:35] [Iter 125/2250] R0[124/150], Temp: 0.0723, Energy: 2.914616+0.001257j
[2025-09-08 01:51:21] [Iter 126/2250] R0[125/150], Temp: 0.0670, Energy: 2.888858+0.006824j
[2025-09-08 01:52:07] [Iter 127/2250] R0[126/150], Temp: 0.0618, Energy: 2.865336-0.000421j
[2025-09-08 01:52:52] [Iter 128/2250] R0[127/150], Temp: 0.0569, Energy: 2.838326-0.017690j
[2025-09-08 01:53:38] [Iter 129/2250] R0[128/150], Temp: 0.0521, Energy: 2.786766+0.015176j
[2025-09-08 01:54:23] [Iter 130/2250] R0[129/150], Temp: 0.0476, Energy: 2.755676-0.005681j
[2025-09-08 01:55:08] [Iter 131/2250] R0[130/150], Temp: 0.0432, Energy: 2.701526+0.013392j
[2025-09-08 01:55:54] [Iter 132/2250] R0[131/150], Temp: 0.0391, Energy: 2.651540+0.001585j
[2025-09-08 01:56:40] [Iter 133/2250] R0[132/150], Temp: 0.0351, Energy: 2.592359-0.003781j
[2025-09-08 01:57:25] [Iter 134/2250] R0[133/150], Temp: 0.0314, Energy: 2.528695-0.001440j
[2025-09-08 01:58:11] [Iter 135/2250] R0[134/150], Temp: 0.0278, Energy: 2.447032+0.012564j
[2025-09-08 01:58:56] [Iter 136/2250] R0[135/150], Temp: 0.0245, Energy: 2.372465-0.005034j
[2025-09-08 01:59:42] [Iter 137/2250] R0[136/150], Temp: 0.0213, Energy: 2.273263-0.021741j
[2025-09-08 02:00:27] [Iter 138/2250] R0[137/150], Temp: 0.0184, Energy: 2.155503+0.019165j
[2025-09-08 02:01:13] [Iter 139/2250] R0[138/150], Temp: 0.0157, Energy: 2.039095+0.008770j
[2025-09-08 02:01:58] [Iter 140/2250] R0[139/150], Temp: 0.0132, Energy: 1.912043-0.023867j
[2025-09-08 02:02:44] [Iter 141/2250] R0[140/150], Temp: 0.0109, Energy: 1.755221+0.005254j
[2025-09-08 02:03:29] [Iter 142/2250] R0[141/150], Temp: 0.0089, Energy: 1.586828+0.006515j
[2025-09-08 02:04:15] [Iter 143/2250] R0[142/150], Temp: 0.0070, Energy: 1.372370+0.060771j
[2025-09-08 02:05:00] [Iter 144/2250] R0[143/150], Temp: 0.0054, Energy: 1.170080+0.024270j
[2025-09-08 02:05:46] [Iter 145/2250] R0[144/150], Temp: 0.0039, Energy: 0.934824+0.025926j
[2025-09-08 02:06:31] [Iter 146/2250] R0[145/150], Temp: 0.0027, Energy: 0.684482-0.001711j
[2025-09-08 02:07:17] [Iter 147/2250] R0[146/150], Temp: 0.0018, Energy: 0.387104+0.033564j
[2025-09-08 02:08:02] [Iter 148/2250] R0[147/150], Temp: 0.0010, Energy: 0.083697+0.014685j
[2025-09-08 02:08:48] [Iter 149/2250] R0[148/150], Temp: 0.0004, Energy: -0.276988+0.000056j
[2025-09-08 02:09:33] [Iter 150/2250] R0[149/150], Temp: 0.0001, Energy: -0.666784+0.053970j
[2025-09-08 02:09:33] RESTART #1 | Period: 300
[2025-09-08 02:10:19] [Iter 151/2250] R1[0/300], Temp: 1.0000, Energy: -1.082777+0.021731j
[2025-09-08 02:11:04] [Iter 152/2250] R1[1/300], Temp: 1.0000, Energy: -1.500322-0.028114j
[2025-09-08 02:11:50] [Iter 153/2250] R1[2/300], Temp: 0.9999, Energy: -1.988239+0.010722j
[2025-09-08 02:12:35] [Iter 154/2250] R1[3/300], Temp: 0.9998, Energy: -2.515039-0.036343j
[2025-09-08 02:13:21] [Iter 155/2250] R1[4/300], Temp: 0.9996, Energy: -3.076385+0.000873j
[2025-09-08 02:14:06] [Iter 156/2250] R1[5/300], Temp: 0.9993, Energy: -3.584787-0.010683j
[2025-09-08 02:14:52] [Iter 157/2250] R1[6/300], Temp: 0.9990, Energy: -4.232779-0.042454j
[2025-09-08 02:15:37] [Iter 158/2250] R1[7/300], Temp: 0.9987, Energy: -4.928673-0.039387j
[2025-09-08 02:16:23] [Iter 159/2250] R1[8/300], Temp: 0.9982, Energy: -5.571326-0.044006j
[2025-09-08 02:17:08] [Iter 160/2250] R1[9/300], Temp: 0.9978, Energy: -6.176014-0.028217j
[2025-09-08 02:17:54] [Iter 161/2250] R1[10/300], Temp: 0.9973, Energy: -6.949677-0.054566j
[2025-09-08 02:18:40] [Iter 162/2250] R1[11/300], Temp: 0.9967, Energy: -7.594345+0.060755j
[2025-09-08 02:19:25] [Iter 163/2250] R1[12/300], Temp: 0.9961, Energy: -8.325181-0.046801j
[2025-09-08 02:20:11] [Iter 164/2250] R1[13/300], Temp: 0.9954, Energy: -8.921857+0.005030j
[2025-09-08 02:20:56] [Iter 165/2250] R1[14/300], Temp: 0.9946, Energy: -9.645052-0.016677j
[2025-09-08 02:21:42] [Iter 166/2250] R1[15/300], Temp: 0.9938, Energy: -10.375379+0.030289j
[2025-09-08 02:22:27] [Iter 167/2250] R1[16/300], Temp: 0.9930, Energy: -11.080634-0.041587j
[2025-09-08 02:23:12] [Iter 168/2250] R1[17/300], Temp: 0.9921, Energy: -11.801189+0.012089j
[2025-09-08 02:23:58] [Iter 169/2250] R1[18/300], Temp: 0.9911, Energy: -12.483345+0.030816j
[2025-09-08 02:24:44] [Iter 170/2250] R1[19/300], Temp: 0.9901, Energy: -13.302738+0.029307j
[2025-09-08 02:25:29] [Iter 171/2250] R1[20/300], Temp: 0.9891, Energy: -14.010643-0.066361j
[2025-09-08 02:26:14] [Iter 172/2250] R1[21/300], Temp: 0.9880, Energy: -14.803547+0.049634j
[2025-09-08 02:27:00] [Iter 173/2250] R1[22/300], Temp: 0.9868, Energy: -15.573322+0.099438j
[2025-09-08 02:27:46] [Iter 174/2250] R1[23/300], Temp: 0.9856, Energy: -16.472607-0.009567j
[2025-09-08 02:28:31] [Iter 175/2250] R1[24/300], Temp: 0.9843, Energy: -17.424846-0.005227j
[2025-09-08 02:29:17] [Iter 176/2250] R1[25/300], Temp: 0.9830, Energy: -18.362104+0.058175j
[2025-09-08 02:30:02] [Iter 177/2250] R1[26/300], Temp: 0.9816, Energy: -19.275749+0.059953j
[2025-09-08 02:30:48] [Iter 178/2250] R1[27/300], Temp: 0.9801, Energy: -20.367409+0.042855j
[2025-09-08 02:31:33] [Iter 179/2250] R1[28/300], Temp: 0.9787, Energy: -21.617584+0.002087j
[2025-09-08 02:32:19] [Iter 180/2250] R1[29/300], Temp: 0.9771, Energy: -22.828145+0.051620j
[2025-09-08 02:33:04] [Iter 181/2250] R1[30/300], Temp: 0.9755, Energy: -24.279550-0.041712j
[2025-09-08 02:33:50] [Iter 182/2250] R1[31/300], Temp: 0.9739, Energy: -25.947460+0.080728j
[2025-09-08 02:34:35] [Iter 183/2250] R1[32/300], Temp: 0.9722, Energy: -27.775913+0.022707j
[2025-09-08 02:35:20] [Iter 184/2250] R1[33/300], Temp: 0.9704, Energy: -29.608018+0.068751j
[2025-09-08 02:36:06] [Iter 185/2250] R1[34/300], Temp: 0.9686, Energy: -31.783119+0.069327j
[2025-09-08 02:36:52] [Iter 186/2250] R1[35/300], Temp: 0.9668, Energy: -34.166518+0.114294j
[2025-09-08 02:37:37] [Iter 187/2250] R1[36/300], Temp: 0.9649, Energy: -36.684575+0.039953j
[2025-09-08 02:38:23] [Iter 188/2250] R1[37/300], Temp: 0.9629, Energy: -39.372725+0.059552j
[2025-09-08 02:39:08] [Iter 189/2250] R1[38/300], Temp: 0.9609, Energy: -42.292427+0.102939j
[2025-09-08 02:39:54] [Iter 190/2250] R1[39/300], Temp: 0.9589, Energy: -45.212492+0.184145j
[2025-09-08 02:40:39] [Iter 191/2250] R1[40/300], Temp: 0.9568, Energy: -48.359392+0.092950j
[2025-09-08 02:41:25] [Iter 192/2250] R1[41/300], Temp: 0.9546, Energy: -51.377814+0.087505j
[2025-09-08 02:42:10] [Iter 193/2250] R1[42/300], Temp: 0.9524, Energy: -54.207702+0.114762j
[2025-09-08 02:42:56] [Iter 194/2250] R1[43/300], Temp: 0.9502, Energy: -56.757335+0.061460j
[2025-09-08 02:43:41] [Iter 195/2250] R1[44/300], Temp: 0.9479, Energy: -58.996438+0.098618j
[2025-09-08 02:44:27] [Iter 196/2250] R1[45/300], Temp: 0.9455, Energy: -61.212748+0.068723j
[2025-09-08 02:45:12] [Iter 197/2250] R1[46/300], Temp: 0.9431, Energy: -63.014082+0.045708j
[2025-09-08 02:45:58] [Iter 198/2250] R1[47/300], Temp: 0.9407, Energy: -64.667896+0.029982j
[2025-09-08 02:46:44] [Iter 199/2250] R1[48/300], Temp: 0.9382, Energy: -66.080427+0.052479j
[2025-09-08 02:47:29] [Iter 200/2250] R1[49/300], Temp: 0.9356, Energy: -67.259449-0.000683j
[2025-09-08 02:48:15] [Iter 201/2250] R1[50/300], Temp: 0.9330, Energy: -68.441013+0.034464j
[2025-09-08 02:49:00] [Iter 202/2250] R1[51/300], Temp: 0.9304, Energy: -69.419300+0.014298j
[2025-09-08 02:49:46] [Iter 203/2250] R1[52/300], Temp: 0.9277, Energy: -70.234834+0.046382j
[2025-09-08 02:50:31] [Iter 204/2250] R1[53/300], Temp: 0.9249, Energy: -71.111634+0.062336j
[2025-09-08 02:51:17] [Iter 205/2250] R1[54/300], Temp: 0.9222, Energy: -71.933141+0.039893j
[2025-09-08 02:52:03] [Iter 206/2250] R1[55/300], Temp: 0.9193, Energy: -72.557350+0.044055j
[2025-09-08 02:52:48] [Iter 207/2250] R1[56/300], Temp: 0.9165, Energy: -73.303977+0.012414j
[2025-09-08 02:53:34] [Iter 208/2250] R1[57/300], Temp: 0.9135, Energy: -73.961660-0.004752j
[2025-09-08 02:54:19] [Iter 209/2250] R1[58/300], Temp: 0.9106, Energy: -74.441863-0.012060j
[2025-09-08 02:55:05] [Iter 210/2250] R1[59/300], Temp: 0.9076, Energy: -74.995265+0.024742j
[2025-09-08 02:55:50] [Iter 211/2250] R1[60/300], Temp: 0.9045, Energy: -75.429400+0.015022j
[2025-09-08 02:56:36] [Iter 212/2250] R1[61/300], Temp: 0.9014, Energy: -75.748738-0.014691j
[2025-09-08 02:57:21] [Iter 213/2250] R1[62/300], Temp: 0.8983, Energy: -76.234461+0.070569j
[2025-09-08 02:58:07] [Iter 214/2250] R1[63/300], Temp: 0.8951, Energy: -76.483200-0.008680j
[2025-09-08 02:58:52] [Iter 215/2250] R1[64/300], Temp: 0.8918, Energy: -76.739415+0.005090j
[2025-09-08 02:59:38] [Iter 216/2250] R1[65/300], Temp: 0.8886, Energy: -77.031402+0.013391j
[2025-09-08 03:00:23] [Iter 217/2250] R1[66/300], Temp: 0.8853, Energy: -77.127354-0.000251j
[2025-09-08 03:01:09] [Iter 218/2250] R1[67/300], Temp: 0.8819, Energy: -77.533351-0.000785j
[2025-09-08 03:01:54] [Iter 219/2250] R1[68/300], Temp: 0.8785, Energy: -77.764296+0.022352j
[2025-09-08 03:02:40] [Iter 220/2250] R1[69/300], Temp: 0.8751, Energy: -77.825349+0.003172j
[2025-09-08 03:03:25] [Iter 221/2250] R1[70/300], Temp: 0.8716, Energy: -78.002463+0.020501j
[2025-09-08 03:04:11] [Iter 222/2250] R1[71/300], Temp: 0.8680, Energy: -78.021977+0.011999j
[2025-09-08 03:04:56] [Iter 223/2250] R1[72/300], Temp: 0.8645, Energy: -78.268403+0.001423j
[2025-09-08 03:05:42] [Iter 224/2250] R1[73/300], Temp: 0.8609, Energy: -78.353969-0.010117j
[2025-09-08 03:06:28] [Iter 225/2250] R1[74/300], Temp: 0.8572, Energy: -78.396846+0.012010j
[2025-09-08 03:07:13] [Iter 226/2250] R1[75/300], Temp: 0.8536, Energy: -78.466939-0.005637j
[2025-09-08 03:07:59] [Iter 227/2250] R1[76/300], Temp: 0.8498, Energy: -78.561847+0.008401j
[2025-09-08 03:08:44] [Iter 228/2250] R1[77/300], Temp: 0.8461, Energy: -78.674079+0.001384j
[2025-09-08 03:09:30] [Iter 229/2250] R1[78/300], Temp: 0.8423, Energy: -78.761698-0.005241j
[2025-09-08 03:10:15] [Iter 230/2250] R1[79/300], Temp: 0.8384, Energy: -78.813588-0.004465j
[2025-09-08 03:11:01] [Iter 231/2250] R1[80/300], Temp: 0.8346, Energy: -78.896526+0.017597j
[2025-09-08 03:11:46] [Iter 232/2250] R1[81/300], Temp: 0.8307, Energy: -78.913237-0.006044j
[2025-09-08 03:12:32] [Iter 233/2250] R1[82/300], Temp: 0.8267, Energy: -79.013951+0.005309j
[2025-09-08 03:13:17] [Iter 234/2250] R1[83/300], Temp: 0.8227, Energy: -79.017730-0.003461j
[2025-09-08 03:14:03] [Iter 235/2250] R1[84/300], Temp: 0.8187, Energy: -79.105919+0.009529j
[2025-09-08 03:14:48] [Iter 236/2250] R1[85/300], Temp: 0.8147, Energy: -79.137854-0.003857j
[2025-09-08 03:15:34] [Iter 237/2250] R1[86/300], Temp: 0.8106, Energy: -79.211245-0.004121j
[2025-09-08 03:16:19] [Iter 238/2250] R1[87/300], Temp: 0.8065, Energy: -79.329487+0.000760j
[2025-09-08 03:17:05] [Iter 239/2250] R1[88/300], Temp: 0.8023, Energy: -79.235474-0.012232j
[2025-09-08 03:17:50] [Iter 240/2250] R1[89/300], Temp: 0.7981, Energy: -79.334228-0.004730j
[2025-09-08 03:18:36] [Iter 241/2250] R1[90/300], Temp: 0.7939, Energy: -79.317588-0.005048j
[2025-09-08 03:19:21] [Iter 242/2250] R1[91/300], Temp: 0.7896, Energy: -79.381951-0.005884j
[2025-09-08 03:20:07] [Iter 243/2250] R1[92/300], Temp: 0.7854, Energy: -79.397324-0.002993j
[2025-09-08 03:20:52] [Iter 244/2250] R1[93/300], Temp: 0.7810, Energy: -79.443482+0.004942j
[2025-09-08 03:21:38] [Iter 245/2250] R1[94/300], Temp: 0.7767, Energy: -79.483118+0.007120j
[2025-09-08 03:22:24] [Iter 246/2250] R1[95/300], Temp: 0.7723, Energy: -79.462508-0.008416j
[2025-09-08 03:23:09] [Iter 247/2250] R1[96/300], Temp: 0.7679, Energy: -79.505759+0.003958j
[2025-09-08 03:23:55] [Iter 248/2250] R1[97/300], Temp: 0.7635, Energy: -79.439721-0.004520j
[2025-09-08 03:24:40] [Iter 249/2250] R1[98/300], Temp: 0.7590, Energy: -79.550807+0.010151j
[2025-09-08 03:25:26] [Iter 250/2250] R1[99/300], Temp: 0.7545, Energy: -79.603797-0.000317j
[2025-09-08 03:25:26] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-09-08 03:26:11] [Iter 251/2250] R1[100/300], Temp: 0.7500, Energy: -79.573897-0.004171j
[2025-09-08 03:26:57] [Iter 252/2250] R1[101/300], Temp: 0.7455, Energy: -79.687358-0.011643j
[2025-09-08 03:27:42] [Iter 253/2250] R1[102/300], Temp: 0.7409, Energy: -79.752136-0.001126j
[2025-09-08 03:28:28] [Iter 254/2250] R1[103/300], Temp: 0.7363, Energy: -79.806408-0.015881j
[2025-09-08 03:29:13] [Iter 255/2250] R1[104/300], Temp: 0.7316, Energy: -79.751224+0.015134j
[2025-09-08 03:29:59] [Iter 256/2250] R1[105/300], Temp: 0.7270, Energy: -79.823089-0.004328j
[2025-09-08 03:30:44] [Iter 257/2250] R1[106/300], Temp: 0.7223, Energy: -79.749875+0.002428j
[2025-09-08 03:31:30] [Iter 258/2250] R1[107/300], Temp: 0.7176, Energy: -79.726981+0.027412j
[2025-09-08 03:32:15] [Iter 259/2250] R1[108/300], Temp: 0.7129, Energy: -79.760555+0.011060j
[2025-09-08 03:33:01] [Iter 260/2250] R1[109/300], Temp: 0.7081, Energy: -79.740930+0.007087j
[2025-09-08 03:33:46] [Iter 261/2250] R1[110/300], Temp: 0.7034, Energy: -79.895591-0.004687j
[2025-09-08 03:34:32] [Iter 262/2250] R1[111/300], Temp: 0.6986, Energy: -79.846025-0.009629j
[2025-09-08 03:35:17] [Iter 263/2250] R1[112/300], Temp: 0.6938, Energy: -79.806843-0.001180j
[2025-09-08 03:36:03] [Iter 264/2250] R1[113/300], Temp: 0.6889, Energy: -79.872977-0.000829j
[2025-09-08 03:36:48] [Iter 265/2250] R1[114/300], Temp: 0.6841, Energy: -79.897827-0.010784j
[2025-09-08 03:37:34] [Iter 266/2250] R1[115/300], Temp: 0.6792, Energy: -79.907031-0.008238j
[2025-09-08 03:38:19] [Iter 267/2250] R1[116/300], Temp: 0.6743, Energy: -79.962967+0.005195j
[2025-09-08 03:39:05] [Iter 268/2250] R1[117/300], Temp: 0.6694, Energy: -79.962845-0.005662j
[2025-09-08 03:39:50] [Iter 269/2250] R1[118/300], Temp: 0.6644, Energy: -79.916897-0.016206j
[2025-09-08 03:40:36] [Iter 270/2250] R1[119/300], Temp: 0.6595, Energy: -79.987101-0.012034j
[2025-09-08 03:41:21] [Iter 271/2250] R1[120/300], Temp: 0.6545, Energy: -79.896313-0.005391j
[2025-09-08 03:42:07] [Iter 272/2250] R1[121/300], Temp: 0.6495, Energy: -79.955440+0.001435j
[2025-09-08 03:42:52] [Iter 273/2250] R1[122/300], Temp: 0.6445, Energy: -79.960828-0.016874j
[2025-09-08 03:43:38] [Iter 274/2250] R1[123/300], Temp: 0.6395, Energy: -79.972916+0.007152j
[2025-09-08 03:44:23] [Iter 275/2250] R1[124/300], Temp: 0.6345, Energy: -79.956613+0.011134j
[2025-09-08 03:45:09] [Iter 276/2250] R1[125/300], Temp: 0.6294, Energy: -80.058546+0.003244j
[2025-09-08 03:45:54] [Iter 277/2250] R1[126/300], Temp: 0.6243, Energy: -79.942705+0.002816j
[2025-09-08 03:46:40] [Iter 278/2250] R1[127/300], Temp: 0.6193, Energy: -80.006607+0.004843j
[2025-09-08 03:47:25] [Iter 279/2250] R1[128/300], Temp: 0.6142, Energy: -79.983894-0.008934j
[2025-09-08 03:48:11] [Iter 280/2250] R1[129/300], Temp: 0.6091, Energy: -80.024554-0.000162j
[2025-09-08 03:48:56] [Iter 281/2250] R1[130/300], Temp: 0.6040, Energy: -80.102131-0.002920j
[2025-09-08 03:49:42] [Iter 282/2250] R1[131/300], Temp: 0.5988, Energy: -80.122702-0.005633j
[2025-09-08 03:50:27] [Iter 283/2250] R1[132/300], Temp: 0.5937, Energy: -80.063024+0.002545j
[2025-09-08 03:51:13] [Iter 284/2250] R1[133/300], Temp: 0.5885, Energy: -80.163218-0.011405j
[2025-09-08 03:51:58] [Iter 285/2250] R1[134/300], Temp: 0.5834, Energy: -80.092782+0.023068j
[2025-09-08 03:52:44] [Iter 286/2250] R1[135/300], Temp: 0.5782, Energy: -80.103515-0.000176j
[2025-09-08 03:53:29] [Iter 287/2250] R1[136/300], Temp: 0.5730, Energy: -80.158172+0.010183j
[2025-09-08 03:54:15] [Iter 288/2250] R1[137/300], Temp: 0.5679, Energy: -80.212162-0.003187j
[2025-09-08 03:55:00] [Iter 289/2250] R1[138/300], Temp: 0.5627, Energy: -80.184313-0.007753j
[2025-09-08 03:55:46] [Iter 290/2250] R1[139/300], Temp: 0.5575, Energy: -80.157706-0.011424j
[2025-09-08 03:56:31] [Iter 291/2250] R1[140/300], Temp: 0.5523, Energy: -80.225832-0.004000j
[2025-09-08 03:57:17] [Iter 292/2250] R1[141/300], Temp: 0.5471, Energy: -80.193025-0.006064j
[2025-09-08 03:58:02] [Iter 293/2250] R1[142/300], Temp: 0.5418, Energy: -80.215300+0.005470j
[2025-09-08 03:58:48] [Iter 294/2250] R1[143/300], Temp: 0.5366, Energy: -80.200323-0.003417j
[2025-09-08 03:59:33] [Iter 295/2250] R1[144/300], Temp: 0.5314, Energy: -80.293931-0.023183j
[2025-09-08 04:00:19] [Iter 296/2250] R1[145/300], Temp: 0.5262, Energy: -80.297609+0.007909j
[2025-09-08 04:01:04] [Iter 297/2250] R1[146/300], Temp: 0.5209, Energy: -80.252307+0.012441j
[2025-09-08 04:01:50] [Iter 298/2250] R1[147/300], Temp: 0.5157, Energy: -80.341649+0.003543j
[2025-09-08 04:02:36] [Iter 299/2250] R1[148/300], Temp: 0.5105, Energy: -80.289131-0.004249j
[2025-09-08 04:03:21] [Iter 300/2250] R1[149/300], Temp: 0.5052, Energy: -80.396758+0.004394j
[2025-09-08 04:04:07] [Iter 301/2250] R1[150/300], Temp: 0.5000, Energy: -80.337076+0.008610j
[2025-09-08 04:04:52] [Iter 302/2250] R1[151/300], Temp: 0.4948, Energy: -80.336941-0.004578j
[2025-09-08 04:05:38] [Iter 303/2250] R1[152/300], Temp: 0.4895, Energy: -80.211000-0.002260j
[2025-09-08 04:06:23] [Iter 304/2250] R1[153/300], Temp: 0.4843, Energy: -80.271435+0.009599j
[2025-09-08 04:07:09] [Iter 305/2250] R1[154/300], Temp: 0.4791, Energy: -80.254572-0.009992j
[2025-09-08 04:07:54] [Iter 306/2250] R1[155/300], Temp: 0.4738, Energy: -80.276427-0.003125j
[2025-09-08 04:08:40] [Iter 307/2250] R1[156/300], Temp: 0.4686, Energy: -80.313883-0.001092j
[2025-09-08 04:09:26] [Iter 308/2250] R1[157/300], Temp: 0.4634, Energy: -80.349118-0.006689j
[2025-09-08 04:10:11] [Iter 309/2250] R1[158/300], Temp: 0.4582, Energy: -80.323845+0.007177j
[2025-09-08 04:10:57] [Iter 310/2250] R1[159/300], Temp: 0.4529, Energy: -80.344515-0.006789j
[2025-09-08 04:11:42] [Iter 311/2250] R1[160/300], Temp: 0.4477, Energy: -80.401749+0.010010j
[2025-09-08 04:12:28] [Iter 312/2250] R1[161/300], Temp: 0.4425, Energy: -80.315467+0.008369j
[2025-09-08 04:13:13] [Iter 313/2250] R1[162/300], Temp: 0.4373, Energy: -80.272771+0.000869j
[2025-09-08 04:13:59] [Iter 314/2250] R1[163/300], Temp: 0.4321, Energy: -80.276556-0.004061j
[2025-09-08 04:14:44] [Iter 315/2250] R1[164/300], Temp: 0.4270, Energy: -80.376115-0.000904j
[2025-09-08 04:15:30] [Iter 316/2250] R1[165/300], Temp: 0.4218, Energy: -80.305582-0.009563j
[2025-09-08 04:16:15] [Iter 317/2250] R1[166/300], Temp: 0.4166, Energy: -80.271875-0.010362j
[2025-09-08 04:17:01] [Iter 318/2250] R1[167/300], Temp: 0.4115, Energy: -80.305900+0.005617j
[2025-09-08 04:17:46] [Iter 319/2250] R1[168/300], Temp: 0.4063, Energy: -80.297196-0.008296j
[2025-09-08 04:18:32] [Iter 320/2250] R1[169/300], Temp: 0.4012, Energy: -80.404813+0.004766j
[2025-09-08 04:19:17] [Iter 321/2250] R1[170/300], Temp: 0.3960, Energy: -80.469846+0.003098j
[2025-09-08 04:20:03] [Iter 322/2250] R1[171/300], Temp: 0.3909, Energy: -80.427924+0.010152j
[2025-09-08 04:20:48] [Iter 323/2250] R1[172/300], Temp: 0.3858, Energy: -80.479781+0.002026j
[2025-09-08 04:21:34] [Iter 324/2250] R1[173/300], Temp: 0.3807, Energy: -80.465600-0.000295j
[2025-09-08 04:22:20] [Iter 325/2250] R1[174/300], Temp: 0.3757, Energy: -80.481751-0.008150j
[2025-09-08 04:23:05] [Iter 326/2250] R1[175/300], Temp: 0.3706, Energy: -80.439820-0.005246j
[2025-09-08 04:23:51] [Iter 327/2250] R1[176/300], Temp: 0.3655, Energy: -80.440263+0.008145j
[2025-09-08 04:24:36] [Iter 328/2250] R1[177/300], Temp: 0.3605, Energy: -80.449139+0.010116j
[2025-09-08 04:25:22] [Iter 329/2250] R1[178/300], Temp: 0.3555, Energy: -80.490375+0.006852j
[2025-09-08 04:26:07] [Iter 330/2250] R1[179/300], Temp: 0.3505, Energy: -80.476482-0.010333j
[2025-09-08 04:26:53] [Iter 331/2250] R1[180/300], Temp: 0.3455, Energy: -80.571238-0.013523j
[2025-09-08 04:27:38] [Iter 332/2250] R1[181/300], Temp: 0.3405, Energy: -80.558952-0.001312j
[2025-09-08 04:28:24] [Iter 333/2250] R1[182/300], Temp: 0.3356, Energy: -80.634917-0.019091j
[2025-09-08 04:29:09] [Iter 334/2250] R1[183/300], Temp: 0.3306, Energy: -80.631870+0.002830j
[2025-09-08 04:29:55] [Iter 335/2250] R1[184/300], Temp: 0.3257, Energy: -80.571424+0.008080j
[2025-09-08 04:30:40] [Iter 336/2250] R1[185/300], Temp: 0.3208, Energy: -80.624653-0.008474j
[2025-09-08 04:31:26] [Iter 337/2250] R1[186/300], Temp: 0.3159, Energy: -80.580957+0.003560j
[2025-09-08 04:32:12] [Iter 338/2250] R1[187/300], Temp: 0.3111, Energy: -80.629026-0.006421j
[2025-09-08 04:32:57] [Iter 339/2250] R1[188/300], Temp: 0.3062, Energy: -80.608638-0.007315j
[2025-09-08 04:33:43] [Iter 340/2250] R1[189/300], Temp: 0.3014, Energy: -80.600002+0.003676j
[2025-09-08 04:34:28] [Iter 341/2250] R1[190/300], Temp: 0.2966, Energy: -80.544221+0.005755j
[2025-09-08 04:35:14] [Iter 342/2250] R1[191/300], Temp: 0.2919, Energy: -80.620032+0.005027j
[2025-09-08 04:35:59] [Iter 343/2250] R1[192/300], Temp: 0.2871, Energy: -80.733919+0.007686j
[2025-09-08 04:36:45] [Iter 344/2250] R1[193/300], Temp: 0.2824, Energy: -80.654631-0.001961j
[2025-09-08 04:37:30] [Iter 345/2250] R1[194/300], Temp: 0.2777, Energy: -80.679483-0.018124j
[2025-09-08 04:38:16] [Iter 346/2250] R1[195/300], Temp: 0.2730, Energy: -80.776208+0.007323j
[2025-09-08 04:39:01] [Iter 347/2250] R1[196/300], Temp: 0.2684, Energy: -80.717275-0.008371j
[2025-09-08 04:39:47] [Iter 348/2250] R1[197/300], Temp: 0.2637, Energy: -80.746768-0.005504j
[2025-09-08 04:40:32] [Iter 349/2250] R1[198/300], Temp: 0.2591, Energy: -80.644976-0.000404j
[2025-09-08 04:41:18] [Iter 350/2250] R1[199/300], Temp: 0.2545, Energy: -80.658797-0.000905j
[2025-09-08 04:42:03] [Iter 351/2250] R1[200/300], Temp: 0.2500, Energy: -80.694483+0.002629j
[2025-09-08 04:42:49] [Iter 352/2250] R1[201/300], Temp: 0.2455, Energy: -80.661157-0.008238j
[2025-09-08 04:43:34] [Iter 353/2250] R1[202/300], Temp: 0.2410, Energy: -80.657092+0.001634j
[2025-09-08 04:44:20] [Iter 354/2250] R1[203/300], Temp: 0.2365, Energy: -80.677669+0.005699j
[2025-09-08 04:45:05] [Iter 355/2250] R1[204/300], Temp: 0.2321, Energy: -80.723214-0.007898j
[2025-09-08 04:45:51] [Iter 356/2250] R1[205/300], Temp: 0.2277, Energy: -80.756944-0.009688j
[2025-09-08 04:46:36] [Iter 357/2250] R1[206/300], Temp: 0.2233, Energy: -80.731946+0.004089j
[2025-09-08 04:47:22] [Iter 358/2250] R1[207/300], Temp: 0.2190, Energy: -80.708436+0.005933j
[2025-09-08 04:48:08] [Iter 359/2250] R1[208/300], Temp: 0.2146, Energy: -80.761132-0.009392j
[2025-09-08 04:48:53] [Iter 360/2250] R1[209/300], Temp: 0.2104, Energy: -80.716191+0.006001j
[2025-09-08 04:49:39] [Iter 361/2250] R1[210/300], Temp: 0.2061, Energy: -80.726436-0.016833j
[2025-09-08 04:50:24] [Iter 362/2250] R1[211/300], Temp: 0.2019, Energy: -80.789823-0.006528j
[2025-09-08 04:51:10] [Iter 363/2250] R1[212/300], Temp: 0.1977, Energy: -80.726020+0.016337j
[2025-09-08 04:51:55] [Iter 364/2250] R1[213/300], Temp: 0.1935, Energy: -80.690050+0.007347j
[2025-09-08 04:52:41] [Iter 365/2250] R1[214/300], Temp: 0.1894, Energy: -80.764829+0.003551j
[2025-09-08 04:53:26] [Iter 366/2250] R1[215/300], Temp: 0.1853, Energy: -80.686347+0.001835j
[2025-09-08 04:54:12] [Iter 367/2250] R1[216/300], Temp: 0.1813, Energy: -80.669493+0.001149j
[2025-09-08 04:54:57] [Iter 368/2250] R1[217/300], Temp: 0.1773, Energy: -80.778073+0.005156j
[2025-09-08 04:55:43] [Iter 369/2250] R1[218/300], Temp: 0.1733, Energy: -80.783648-0.004974j
[2025-09-08 04:56:28] [Iter 370/2250] R1[219/300], Temp: 0.1693, Energy: -80.802667+0.000151j
[2025-09-08 04:57:14] [Iter 371/2250] R1[220/300], Temp: 0.1654, Energy: -80.696145+0.000639j
[2025-09-08 04:57:59] [Iter 372/2250] R1[221/300], Temp: 0.1616, Energy: -80.718872+0.018780j
[2025-09-08 04:58:45] [Iter 373/2250] R1[222/300], Temp: 0.1577, Energy: -80.674401-0.011921j
[2025-09-08 04:59:31] [Iter 374/2250] R1[223/300], Temp: 0.1539, Energy: -80.711100-0.001651j
[2025-09-08 05:00:16] [Iter 375/2250] R1[224/300], Temp: 0.1502, Energy: -80.699178-0.002134j
[2025-09-08 05:01:02] [Iter 376/2250] R1[225/300], Temp: 0.1464, Energy: -80.645620+0.004001j
[2025-09-08 05:01:47] [Iter 377/2250] R1[226/300], Temp: 0.1428, Energy: -80.721917-0.001360j
[2025-09-08 05:02:33] [Iter 378/2250] R1[227/300], Temp: 0.1391, Energy: -80.731511+0.002017j
[2025-09-08 05:03:18] [Iter 379/2250] R1[228/300], Temp: 0.1355, Energy: -80.692588-0.013275j
[2025-09-08 05:04:04] [Iter 380/2250] R1[229/300], Temp: 0.1320, Energy: -80.745828+0.000846j
[2025-09-08 05:04:49] [Iter 381/2250] R1[230/300], Temp: 0.1284, Energy: -80.725894+0.006400j
[2025-09-08 05:05:35] [Iter 382/2250] R1[231/300], Temp: 0.1249, Energy: -80.809836+0.001630j
[2025-09-08 05:06:20] [Iter 383/2250] R1[232/300], Temp: 0.1215, Energy: -80.908245-0.011420j
[2025-09-08 05:07:06] [Iter 384/2250] R1[233/300], Temp: 0.1181, Energy: -80.796739+0.001423j
[2025-09-08 05:07:52] [Iter 385/2250] R1[234/300], Temp: 0.1147, Energy: -80.733449-0.014475j
[2025-09-08 05:08:37] [Iter 386/2250] R1[235/300], Temp: 0.1114, Energy: -80.805717-0.007458j
[2025-09-08 05:09:23] [Iter 387/2250] R1[236/300], Temp: 0.1082, Energy: -80.801439+0.004041j
[2025-09-08 05:10:08] [Iter 388/2250] R1[237/300], Temp: 0.1049, Energy: -80.834236-0.001527j
[2025-09-08 05:10:54] [Iter 389/2250] R1[238/300], Temp: 0.1017, Energy: -80.817154+0.006535j
[2025-09-08 05:11:39] [Iter 390/2250] R1[239/300], Temp: 0.0986, Energy: -80.840507-0.003293j
[2025-09-08 05:12:25] [Iter 391/2250] R1[240/300], Temp: 0.0955, Energy: -80.835772+0.008643j
[2025-09-08 05:13:10] [Iter 392/2250] R1[241/300], Temp: 0.0924, Energy: -80.854068-0.001362j
[2025-09-08 05:13:56] [Iter 393/2250] R1[242/300], Temp: 0.0894, Energy: -80.908352+0.017274j
[2025-09-08 05:14:41] [Iter 394/2250] R1[243/300], Temp: 0.0865, Energy: -80.886147+0.005936j
[2025-09-08 05:15:27] [Iter 395/2250] R1[244/300], Temp: 0.0835, Energy: -80.913170-0.005110j
[2025-09-08 05:16:12] [Iter 396/2250] R1[245/300], Temp: 0.0807, Energy: -80.824816-0.001069j
[2025-09-08 05:16:58] [Iter 397/2250] R1[246/300], Temp: 0.0778, Energy: -80.819287+0.001937j
[2025-09-08 05:17:43] [Iter 398/2250] R1[247/300], Temp: 0.0751, Energy: -80.777393+0.001527j
[2025-09-08 05:18:29] [Iter 399/2250] R1[248/300], Temp: 0.0723, Energy: -80.904874+0.005140j
[2025-09-08 05:19:14] [Iter 400/2250] R1[249/300], Temp: 0.0696, Energy: -80.895193-0.006808j
[2025-09-08 05:20:00] [Iter 401/2250] R1[250/300], Temp: 0.0670, Energy: -80.916786+0.010494j
[2025-09-08 05:20:45] [Iter 402/2250] R1[251/300], Temp: 0.0644, Energy: -80.894104-0.004798j
[2025-09-08 05:21:31] [Iter 403/2250] R1[252/300], Temp: 0.0618, Energy: -80.861272-0.002706j
[2025-09-08 05:22:16] [Iter 404/2250] R1[253/300], Temp: 0.0593, Energy: -80.946412-0.002050j
[2025-09-08 05:23:02] [Iter 405/2250] R1[254/300], Temp: 0.0569, Energy: -80.927075+0.001834j
[2025-09-08 05:23:47] [Iter 406/2250] R1[255/300], Temp: 0.0545, Energy: -80.867259+0.006813j
[2025-09-08 05:24:33] [Iter 407/2250] R1[256/300], Temp: 0.0521, Energy: -80.848222+0.004162j
[2025-09-08 05:25:19] [Iter 408/2250] R1[257/300], Temp: 0.0498, Energy: -80.795055-0.004537j
[2025-09-08 05:26:04] [Iter 409/2250] R1[258/300], Temp: 0.0476, Energy: -80.805470+0.014049j
[2025-09-08 05:26:50] [Iter 410/2250] R1[259/300], Temp: 0.0454, Energy: -80.809108-0.004338j
[2025-09-08 05:27:35] [Iter 411/2250] R1[260/300], Temp: 0.0432, Energy: -80.771700-0.003451j
[2025-09-08 05:28:21] [Iter 412/2250] R1[261/300], Temp: 0.0411, Energy: -80.877734-0.001108j
[2025-09-08 05:29:06] [Iter 413/2250] R1[262/300], Temp: 0.0391, Energy: -80.766897+0.002970j
[2025-09-08 05:29:51] [Iter 414/2250] R1[263/300], Temp: 0.0371, Energy: -80.793104+0.013038j
[2025-09-08 05:30:37] [Iter 415/2250] R1[264/300], Temp: 0.0351, Energy: -80.765660-0.000479j
[2025-09-08 05:31:22] [Iter 416/2250] R1[265/300], Temp: 0.0332, Energy: -80.827682-0.008568j
[2025-09-08 05:32:08] [Iter 417/2250] R1[266/300], Temp: 0.0314, Energy: -80.883348+0.000564j
[2025-09-08 05:32:54] [Iter 418/2250] R1[267/300], Temp: 0.0296, Energy: -80.863858-0.003472j
[2025-09-08 05:33:39] [Iter 419/2250] R1[268/300], Temp: 0.0278, Energy: -80.809897+0.000214j
[2025-09-08 05:34:25] [Iter 420/2250] R1[269/300], Temp: 0.0261, Energy: -80.787366+0.008690j
[2025-09-08 05:35:10] [Iter 421/2250] R1[270/300], Temp: 0.0245, Energy: -80.877802-0.008510j
[2025-09-08 05:35:56] [Iter 422/2250] R1[271/300], Temp: 0.0229, Energy: -80.859340-0.011028j
[2025-09-08 05:36:41] [Iter 423/2250] R1[272/300], Temp: 0.0213, Energy: -80.787946+0.008757j
[2025-09-08 05:37:27] [Iter 424/2250] R1[273/300], Temp: 0.0199, Energy: -80.882034+0.001203j
[2025-09-08 05:38:12] [Iter 425/2250] R1[274/300], Temp: 0.0184, Energy: -80.882934+0.006055j
[2025-09-08 05:38:58] [Iter 426/2250] R1[275/300], Temp: 0.0170, Energy: -80.808231-0.000867j
[2025-09-08 05:39:44] [Iter 427/2250] R1[276/300], Temp: 0.0157, Energy: -80.825188+0.010304j
[2025-09-08 05:40:29] [Iter 428/2250] R1[277/300], Temp: 0.0144, Energy: -80.836073+0.005731j
[2025-09-08 05:41:15] [Iter 429/2250] R1[278/300], Temp: 0.0132, Energy: -80.837829-0.007815j
[2025-09-08 05:42:00] [Iter 430/2250] R1[279/300], Temp: 0.0120, Energy: -80.909839+0.003845j
[2025-09-08 05:42:46] [Iter 431/2250] R1[280/300], Temp: 0.0109, Energy: -80.900351+0.007110j
[2025-09-08 05:43:31] [Iter 432/2250] R1[281/300], Temp: 0.0099, Energy: -80.946289+0.002784j
[2025-09-08 05:44:17] [Iter 433/2250] R1[282/300], Temp: 0.0089, Energy: -80.856001-0.001148j
[2025-09-08 05:45:02] [Iter 434/2250] R1[283/300], Temp: 0.0079, Energy: -80.812845+0.001746j
[2025-09-08 05:45:48] [Iter 435/2250] R1[284/300], Temp: 0.0070, Energy: -80.884887-0.004796j
[2025-09-08 05:46:33] [Iter 436/2250] R1[285/300], Temp: 0.0062, Energy: -80.887047+0.002601j
[2025-09-08 05:47:19] [Iter 437/2250] R1[286/300], Temp: 0.0054, Energy: -80.820948-0.009009j
[2025-09-08 05:48:05] [Iter 438/2250] R1[287/300], Temp: 0.0046, Energy: -80.892352+0.002138j
[2025-09-08 05:48:50] [Iter 439/2250] R1[288/300], Temp: 0.0039, Energy: -80.872509-0.000057j
[2025-09-08 05:49:36] [Iter 440/2250] R1[289/300], Temp: 0.0033, Energy: -80.884653-0.004592j
[2025-09-08 05:50:21] [Iter 441/2250] R1[290/300], Temp: 0.0027, Energy: -80.889296-0.007484j
[2025-09-08 05:51:07] [Iter 442/2250] R1[291/300], Temp: 0.0022, Energy: -80.950152+0.007002j
[2025-09-08 05:51:52] [Iter 443/2250] R1[292/300], Temp: 0.0018, Energy: -80.861009+0.001194j
[2025-09-08 05:52:38] [Iter 444/2250] R1[293/300], Temp: 0.0013, Energy: -80.874484+0.003136j
[2025-09-08 05:53:23] [Iter 445/2250] R1[294/300], Temp: 0.0010, Energy: -80.843930+0.003708j
[2025-09-08 05:54:09] [Iter 446/2250] R1[295/300], Temp: 0.0007, Energy: -80.880623-0.002406j
[2025-09-08 05:54:54] [Iter 447/2250] R1[296/300], Temp: 0.0004, Energy: -80.839303-0.000394j
[2025-09-08 05:55:40] [Iter 448/2250] R1[297/300], Temp: 0.0002, Energy: -80.890320+0.011186j
[2025-09-08 05:56:25] [Iter 449/2250] R1[298/300], Temp: 0.0001, Energy: -80.886640-0.005896j
[2025-09-08 05:57:11] [Iter 450/2250] R1[299/300], Temp: 0.0000, Energy: -80.924886-0.004963j
[2025-09-08 05:57:11] RESTART #2 | Period: 600
[2025-09-08 05:57:56] [Iter 451/2250] R2[0/600], Temp: 1.0000, Energy: -80.979915+0.000373j
[2025-09-08 05:58:42] [Iter 452/2250] R2[1/600], Temp: 1.0000, Energy: -80.956376-0.010875j
[2025-09-08 05:59:27] [Iter 453/2250] R2[2/600], Temp: 1.0000, Energy: -80.956761-0.004460j
[2025-09-08 06:00:13] [Iter 454/2250] R2[3/600], Temp: 0.9999, Energy: -80.946157-0.009036j
[2025-09-08 06:00:58] [Iter 455/2250] R2[4/600], Temp: 0.9999, Energy: -80.953329-0.000295j
[2025-09-08 06:01:44] [Iter 456/2250] R2[5/600], Temp: 0.9998, Energy: -80.955103-0.006760j
[2025-09-08 06:02:29] [Iter 457/2250] R2[6/600], Temp: 0.9998, Energy: -80.895205-0.000322j
[2025-09-08 06:03:15] [Iter 458/2250] R2[7/600], Temp: 0.9997, Energy: -80.953954+0.000593j
[2025-09-08 06:04:01] [Iter 459/2250] R2[8/600], Temp: 0.9996, Energy: -80.900748+0.007809j
[2025-09-08 06:04:46] [Iter 460/2250] R2[9/600], Temp: 0.9994, Energy: -80.941810+0.013046j
[2025-09-08 06:05:32] [Iter 461/2250] R2[10/600], Temp: 0.9993, Energy: -80.881554-0.000709j
[2025-09-08 06:06:17] [Iter 462/2250] R2[11/600], Temp: 0.9992, Energy: -80.885422+0.008807j
[2025-09-08 06:07:03] [Iter 463/2250] R2[12/600], Temp: 0.9990, Energy: -80.994393-0.001020j
[2025-09-08 06:07:48] [Iter 464/2250] R2[13/600], Temp: 0.9988, Energy: -80.930026-0.000807j
[2025-09-08 06:08:34] [Iter 465/2250] R2[14/600], Temp: 0.9987, Energy: -80.986418+0.003464j
[2025-09-08 06:09:19] [Iter 466/2250] R2[15/600], Temp: 0.9985, Energy: -80.969502+0.000713j
[2025-09-08 06:10:05] [Iter 467/2250] R2[16/600], Temp: 0.9982, Energy: -81.023766-0.001857j
[2025-09-08 06:10:50] [Iter 468/2250] R2[17/600], Temp: 0.9980, Energy: -81.013072-0.009331j
[2025-09-08 06:11:36] [Iter 469/2250] R2[18/600], Temp: 0.9978, Energy: -80.960091+0.007661j
[2025-09-08 06:12:22] [Iter 470/2250] R2[19/600], Temp: 0.9975, Energy: -81.024908+0.014672j
[2025-09-08 06:13:07] [Iter 471/2250] R2[20/600], Temp: 0.9973, Energy: -81.040656+0.002035j
[2025-09-08 06:13:53] [Iter 472/2250] R2[21/600], Temp: 0.9970, Energy: -81.028681+0.007005j
[2025-09-08 06:14:38] [Iter 473/2250] R2[22/600], Temp: 0.9967, Energy: -81.095379-0.002556j
[2025-09-08 06:15:24] [Iter 474/2250] R2[23/600], Temp: 0.9964, Energy: -80.978382+0.008416j
[2025-09-08 06:16:09] [Iter 475/2250] R2[24/600], Temp: 0.9961, Energy: -80.945990-0.000535j
[2025-09-08 06:16:55] [Iter 476/2250] R2[25/600], Temp: 0.9957, Energy: -81.038356+0.002355j
[2025-09-08 06:17:40] [Iter 477/2250] R2[26/600], Temp: 0.9954, Energy: -81.014356-0.005710j
[2025-09-08 06:18:26] [Iter 478/2250] R2[27/600], Temp: 0.9950, Energy: -81.010875+0.000377j
[2025-09-08 06:19:11] [Iter 479/2250] R2[28/600], Temp: 0.9946, Energy: -80.942479-0.006454j
[2025-09-08 06:19:57] [Iter 480/2250] R2[29/600], Temp: 0.9942, Energy: -80.845272-0.003210j
[2025-09-08 06:20:42] [Iter 481/2250] R2[30/600], Temp: 0.9938, Energy: -80.914458-0.006497j
[2025-09-08 06:21:28] [Iter 482/2250] R2[31/600], Temp: 0.9934, Energy: -80.926307+0.000290j
[2025-09-08 06:22:14] [Iter 483/2250] R2[32/600], Temp: 0.9930, Energy: -80.817035+0.001499j
[2025-09-08 06:22:59] [Iter 484/2250] R2[33/600], Temp: 0.9926, Energy: -80.813185-0.000704j
[2025-09-08 06:23:45] [Iter 485/2250] R2[34/600], Temp: 0.9921, Energy: -80.815416+0.001419j
[2025-09-08 06:24:30] [Iter 486/2250] R2[35/600], Temp: 0.9916, Energy: -80.852255+0.005457j
[2025-09-08 06:25:16] [Iter 487/2250] R2[36/600], Temp: 0.9911, Energy: -80.941932-0.003231j
[2025-09-08 06:26:01] [Iter 488/2250] R2[37/600], Temp: 0.9906, Energy: -80.874872-0.003709j
[2025-09-08 06:26:47] [Iter 489/2250] R2[38/600], Temp: 0.9901, Energy: -80.843621-0.001285j
[2025-09-08 06:27:32] [Iter 490/2250] R2[39/600], Temp: 0.9896, Energy: -80.876699-0.004056j
[2025-09-08 06:28:18] [Iter 491/2250] R2[40/600], Temp: 0.9891, Energy: -80.900674-0.005134j
[2025-09-08 06:29:03] [Iter 492/2250] R2[41/600], Temp: 0.9885, Energy: -80.924272+0.008467j
[2025-09-08 06:29:49] [Iter 493/2250] R2[42/600], Temp: 0.9880, Energy: -80.943998-0.003424j
[2025-09-08 06:30:35] [Iter 494/2250] R2[43/600], Temp: 0.9874, Energy: -80.891977-0.000692j
[2025-09-08 06:31:20] [Iter 495/2250] R2[44/600], Temp: 0.9868, Energy: -80.857470-0.003304j
[2025-09-08 06:32:06] [Iter 496/2250] R2[45/600], Temp: 0.9862, Energy: -80.871491+0.003208j
[2025-09-08 06:32:51] [Iter 497/2250] R2[46/600], Temp: 0.9856, Energy: -80.924400-0.001602j
[2025-09-08 06:33:37] [Iter 498/2250] R2[47/600], Temp: 0.9849, Energy: -81.045110+0.002520j
[2025-09-08 06:34:22] [Iter 499/2250] R2[48/600], Temp: 0.9843, Energy: -81.000158+0.005575j
[2025-09-08 06:35:08] [Iter 500/2250] R2[49/600], Temp: 0.9836, Energy: -80.969130+0.011677j
[2025-09-08 06:35:08] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-09-08 06:35:53] [Iter 501/2250] R2[50/600], Temp: 0.9830, Energy: -80.936543+0.001711j
[2025-09-08 06:36:39] [Iter 502/2250] R2[51/600], Temp: 0.9823, Energy: -81.009675+0.004026j
[2025-09-08 06:37:24] [Iter 503/2250] R2[52/600], Temp: 0.9816, Energy: -80.982921-0.008703j
[2025-09-08 06:38:10] [Iter 504/2250] R2[53/600], Temp: 0.9809, Energy: -81.005541-0.009208j
[2025-09-08 06:38:55] [Iter 505/2250] R2[54/600], Temp: 0.9801, Energy: -81.044414-0.004545j
[2025-09-08 06:39:41] [Iter 506/2250] R2[55/600], Temp: 0.9794, Energy: -81.047973-0.007704j
[2025-09-08 06:40:26] [Iter 507/2250] R2[56/600], Temp: 0.9787, Energy: -80.980043+0.002173j
[2025-09-08 06:41:12] [Iter 508/2250] R2[57/600], Temp: 0.9779, Energy: -80.987629+0.003950j
[2025-09-08 06:41:57] [Iter 509/2250] R2[58/600], Temp: 0.9771, Energy: -80.971897+0.001510j
[2025-09-08 06:42:43] [Iter 510/2250] R2[59/600], Temp: 0.9763, Energy: -80.976792+0.006572j
[2025-09-08 06:43:28] [Iter 511/2250] R2[60/600], Temp: 0.9755, Energy: -80.955802+0.001570j
[2025-09-08 06:44:14] [Iter 512/2250] R2[61/600], Temp: 0.9747, Energy: -81.001807+0.001059j
[2025-09-08 06:44:59] [Iter 513/2250] R2[62/600], Temp: 0.9739, Energy: -81.012845+0.003159j
[2025-09-08 06:45:45] [Iter 514/2250] R2[63/600], Temp: 0.9730, Energy: -80.970186+0.001680j
[2025-09-08 06:46:30] [Iter 515/2250] R2[64/600], Temp: 0.9722, Energy: -81.045302+0.002929j
[2025-09-08 06:47:16] [Iter 516/2250] R2[65/600], Temp: 0.9713, Energy: -81.003932+0.001511j
[2025-09-08 06:48:02] [Iter 517/2250] R2[66/600], Temp: 0.9704, Energy: -80.980710-0.002464j
[2025-09-08 06:48:47] [Iter 518/2250] R2[67/600], Temp: 0.9695, Energy: -80.973689+0.001537j
[2025-09-08 06:49:33] [Iter 519/2250] R2[68/600], Temp: 0.9686, Energy: -80.976181-0.005934j
[2025-09-08 06:50:18] [Iter 520/2250] R2[69/600], Temp: 0.9677, Energy: -80.885768-0.010301j
[2025-09-08 06:51:04] [Iter 521/2250] R2[70/600], Temp: 0.9668, Energy: -80.924075+0.001704j
[2025-09-08 06:51:49] [Iter 522/2250] R2[71/600], Temp: 0.9658, Energy: -80.968471-0.001560j
[2025-09-08 06:52:35] [Iter 523/2250] R2[72/600], Temp: 0.9649, Energy: -81.004492-0.005640j
[2025-09-08 06:53:20] [Iter 524/2250] R2[73/600], Temp: 0.9639, Energy: -80.984225+0.004787j
[2025-09-08 06:54:06] [Iter 525/2250] R2[74/600], Temp: 0.9629, Energy: -80.928420-0.006026j
[2025-09-08 06:54:51] [Iter 526/2250] R2[75/600], Temp: 0.9619, Energy: -81.006654+0.004845j
[2025-09-08 06:55:37] [Iter 527/2250] R2[76/600], Temp: 0.9609, Energy: -81.037082+0.008528j
[2025-09-08 06:56:22] [Iter 528/2250] R2[77/600], Temp: 0.9599, Energy: -81.033393-0.003519j
[2025-09-08 06:57:08] [Iter 529/2250] R2[78/600], Temp: 0.9589, Energy: -81.109602-0.002518j
[2025-09-08 06:57:54] [Iter 530/2250] R2[79/600], Temp: 0.9578, Energy: -81.072480-0.001108j
[2025-09-08 06:58:39] [Iter 531/2250] R2[80/600], Temp: 0.9568, Energy: -81.042872+0.003383j
[2025-09-08 06:59:25] [Iter 532/2250] R2[81/600], Temp: 0.9557, Energy: -80.979152+0.000323j
[2025-09-08 07:00:10] [Iter 533/2250] R2[82/600], Temp: 0.9546, Energy: -81.005632-0.002275j
[2025-09-08 07:00:56] [Iter 534/2250] R2[83/600], Temp: 0.9535, Energy: -81.031164+0.002956j
[2025-09-08 07:01:41] [Iter 535/2250] R2[84/600], Temp: 0.9524, Energy: -81.014383-0.005355j
[2025-09-08 07:02:27] [Iter 536/2250] R2[85/600], Temp: 0.9513, Energy: -81.000273-0.001892j
[2025-09-08 07:03:12] [Iter 537/2250] R2[86/600], Temp: 0.9502, Energy: -81.003146-0.003336j
[2025-09-08 07:03:58] [Iter 538/2250] R2[87/600], Temp: 0.9490, Energy: -81.016175-0.002114j
[2025-09-08 07:04:43] [Iter 539/2250] R2[88/600], Temp: 0.9479, Energy: -81.171763+0.004473j
[2025-09-08 07:05:29] [Iter 540/2250] R2[89/600], Temp: 0.9467, Energy: -81.034081-0.003203j
[2025-09-08 07:06:14] [Iter 541/2250] R2[90/600], Temp: 0.9455, Energy: -81.043374-0.004430j
[2025-09-08 07:07:00] [Iter 542/2250] R2[91/600], Temp: 0.9443, Energy: -81.064864-0.003231j
[2025-09-08 07:07:45] [Iter 543/2250] R2[92/600], Temp: 0.9431, Energy: -81.091834-0.001100j
[2025-09-08 07:08:31] [Iter 544/2250] R2[93/600], Temp: 0.9419, Energy: -81.005565-0.006407j
[2025-09-08 07:09:17] [Iter 545/2250] R2[94/600], Temp: 0.9407, Energy: -80.969495-0.002382j
[2025-09-08 07:10:02] [Iter 546/2250] R2[95/600], Temp: 0.9394, Energy: -81.000932+0.004350j
[2025-09-08 07:10:48] [Iter 547/2250] R2[96/600], Temp: 0.9382, Energy: -81.046341-0.007140j
[2025-09-08 07:11:33] [Iter 548/2250] R2[97/600], Temp: 0.9369, Energy: -80.987123+0.000713j
[2025-09-08 07:12:19] [Iter 549/2250] R2[98/600], Temp: 0.9356, Energy: -80.989513+0.002203j
[2025-09-08 07:13:04] [Iter 550/2250] R2[99/600], Temp: 0.9343, Energy: -80.998834-0.005569j
[2025-09-08 07:13:50] [Iter 551/2250] R2[100/600], Temp: 0.9330, Energy: -80.990652+0.003344j
[2025-09-08 07:14:35] [Iter 552/2250] R2[101/600], Temp: 0.9317, Energy: -81.021956-0.002223j
[2025-09-08 07:15:21] [Iter 553/2250] R2[102/600], Temp: 0.9304, Energy: -80.992821-0.005195j
[2025-09-08 07:16:06] [Iter 554/2250] R2[103/600], Temp: 0.9290, Energy: -81.027737+0.001418j
[2025-09-08 07:16:52] [Iter 555/2250] R2[104/600], Temp: 0.9277, Energy: -80.953005+0.007558j
[2025-09-08 07:17:37] [Iter 556/2250] R2[105/600], Temp: 0.9263, Energy: -81.014732+0.002720j
[2025-09-08 07:18:23] [Iter 557/2250] R2[106/600], Temp: 0.9249, Energy: -81.005109-0.006820j
[2025-09-08 07:19:08] [Iter 558/2250] R2[107/600], Temp: 0.9236, Energy: -81.032126+0.006685j
[2025-09-08 07:19:54] [Iter 559/2250] R2[108/600], Temp: 0.9222, Energy: -81.063738-0.000554j
[2025-09-08 07:20:39] [Iter 560/2250] R2[109/600], Temp: 0.9208, Energy: -80.956696+0.000256j
[2025-09-08 07:21:25] [Iter 561/2250] R2[110/600], Temp: 0.9193, Energy: -81.020929-0.003691j
[2025-09-08 07:22:10] [Iter 562/2250] R2[111/600], Temp: 0.9179, Energy: -81.076042-0.004394j
[2025-09-08 07:22:56] [Iter 563/2250] R2[112/600], Temp: 0.9165, Energy: -81.021294-0.004277j
[2025-09-08 07:23:41] [Iter 564/2250] R2[113/600], Temp: 0.9150, Energy: -81.062043-0.003441j
[2025-09-08 07:24:27] [Iter 565/2250] R2[114/600], Temp: 0.9135, Energy: -80.990423-0.004522j
[2025-09-08 07:25:12] [Iter 566/2250] R2[115/600], Temp: 0.9121, Energy: -80.986311-0.001082j
[2025-09-08 07:25:58] [Iter 567/2250] R2[116/600], Temp: 0.9106, Energy: -81.076277+0.002026j
[2025-09-08 07:26:43] [Iter 568/2250] R2[117/600], Temp: 0.9091, Energy: -81.036825+0.000088j
[2025-09-08 07:27:29] [Iter 569/2250] R2[118/600], Temp: 0.9076, Energy: -81.032585+0.006241j
[2025-09-08 07:28:15] [Iter 570/2250] R2[119/600], Temp: 0.9060, Energy: -80.990807+0.001741j
[2025-09-08 07:29:00] [Iter 571/2250] R2[120/600], Temp: 0.9045, Energy: -81.046479-0.000208j
[2025-09-08 07:29:46] [Iter 572/2250] R2[121/600], Temp: 0.9030, Energy: -80.956276-0.005352j
[2025-09-08 07:30:31] [Iter 573/2250] R2[122/600], Temp: 0.9014, Energy: -80.979454-0.003413j
[2025-09-08 07:31:17] [Iter 574/2250] R2[123/600], Temp: 0.8998, Energy: -80.959357-0.010167j
[2025-09-08 07:32:02] [Iter 575/2250] R2[124/600], Temp: 0.8983, Energy: -80.924318+0.006101j
[2025-09-08 07:32:48] [Iter 576/2250] R2[125/600], Temp: 0.8967, Energy: -80.933804-0.002315j
[2025-09-08 07:33:33] [Iter 577/2250] R2[126/600], Temp: 0.8951, Energy: -80.993856+0.002035j
[2025-09-08 07:34:19] [Iter 578/2250] R2[127/600], Temp: 0.8935, Energy: -80.994002-0.003681j
[2025-09-08 07:35:04] [Iter 579/2250] R2[128/600], Temp: 0.8918, Energy: -80.909812-0.004826j
[2025-09-08 07:35:50] [Iter 580/2250] R2[129/600], Temp: 0.8902, Energy: -81.027557-0.003543j
[2025-09-08 07:36:35] [Iter 581/2250] R2[130/600], Temp: 0.8886, Energy: -81.037683+0.002195j
[2025-09-08 07:37:21] [Iter 582/2250] R2[131/600], Temp: 0.8869, Energy: -81.051197+0.001056j
[2025-09-08 07:38:06] [Iter 583/2250] R2[132/600], Temp: 0.8853, Energy: -81.023748+0.003082j
[2025-09-08 07:38:52] [Iter 584/2250] R2[133/600], Temp: 0.8836, Energy: -80.994884-0.003025j
[2025-09-08 07:39:37] [Iter 585/2250] R2[134/600], Temp: 0.8819, Energy: -80.973903-0.000402j
[2025-09-08 07:40:23] [Iter 586/2250] R2[135/600], Temp: 0.8802, Energy: -80.973812-0.005105j
[2025-09-08 07:41:08] [Iter 587/2250] R2[136/600], Temp: 0.8785, Energy: -81.046631-0.009794j
[2025-09-08 07:41:54] [Iter 588/2250] R2[137/600], Temp: 0.8768, Energy: -81.046185+0.001030j
[2025-09-08 07:42:39] [Iter 589/2250] R2[138/600], Temp: 0.8751, Energy: -81.023851-0.004497j
[2025-09-08 07:43:25] [Iter 590/2250] R2[139/600], Temp: 0.8733, Energy: -81.041478-0.000430j
[2025-09-08 07:44:10] [Iter 591/2250] R2[140/600], Temp: 0.8716, Energy: -80.974543-0.002238j
[2025-09-08 07:44:56] [Iter 592/2250] R2[141/600], Temp: 0.8698, Energy: -81.010819-0.007724j
[2025-09-08 07:45:42] [Iter 593/2250] R2[142/600], Temp: 0.8680, Energy: -81.034017+0.002873j
[2025-09-08 07:46:27] [Iter 594/2250] R2[143/600], Temp: 0.8663, Energy: -80.992504+0.002328j
[2025-09-08 07:47:13] [Iter 595/2250] R2[144/600], Temp: 0.8645, Energy: -81.025621+0.002524j
[2025-09-08 07:47:58] [Iter 596/2250] R2[145/600], Temp: 0.8627, Energy: -81.009411+0.000768j
[2025-09-08 07:48:44] [Iter 597/2250] R2[146/600], Temp: 0.8609, Energy: -81.028979+0.007293j
[2025-09-08 07:49:29] [Iter 598/2250] R2[147/600], Temp: 0.8591, Energy: -81.113980+0.000495j
[2025-09-08 07:50:15] [Iter 599/2250] R2[148/600], Temp: 0.8572, Energy: -81.084836+0.001958j
[2025-09-08 07:51:00] [Iter 600/2250] R2[149/600], Temp: 0.8554, Energy: -81.021450+0.003062j
[2025-09-08 07:51:46] [Iter 601/2250] R2[150/600], Temp: 0.8536, Energy: -81.027021-0.000968j
[2025-09-08 07:52:31] [Iter 602/2250] R2[151/600], Temp: 0.8517, Energy: -81.015141+0.002005j
[2025-09-08 07:53:17] [Iter 603/2250] R2[152/600], Temp: 0.8498, Energy: -81.014835-0.001239j
[2025-09-08 07:54:02] [Iter 604/2250] R2[153/600], Temp: 0.8480, Energy: -80.977247+0.001345j
[2025-09-08 07:54:48] [Iter 605/2250] R2[154/600], Temp: 0.8461, Energy: -80.928315-0.006436j
[2025-09-08 07:55:33] [Iter 606/2250] R2[155/600], Temp: 0.8442, Energy: -80.911455-0.003762j
[2025-09-08 07:56:19] [Iter 607/2250] R2[156/600], Temp: 0.8423, Energy: -81.020255-0.002643j
[2025-09-08 07:57:04] [Iter 608/2250] R2[157/600], Temp: 0.8404, Energy: -81.064124-0.004178j
[2025-09-08 07:57:50] [Iter 609/2250] R2[158/600], Temp: 0.8384, Energy: -81.021524-0.010329j
[2025-09-08 07:58:35] [Iter 610/2250] R2[159/600], Temp: 0.8365, Energy: -81.025562+0.004488j
[2025-09-08 07:59:21] [Iter 611/2250] R2[160/600], Temp: 0.8346, Energy: -81.080078+0.003669j
[2025-09-08 08:00:06] [Iter 612/2250] R2[161/600], Temp: 0.8326, Energy: -81.087848+0.001916j
[2025-09-08 08:00:52] [Iter 613/2250] R2[162/600], Temp: 0.8307, Energy: -81.031353+0.000116j
[2025-09-08 08:01:38] [Iter 614/2250] R2[163/600], Temp: 0.8287, Energy: -81.012074-0.001478j
[2025-09-08 08:02:23] [Iter 615/2250] R2[164/600], Temp: 0.8267, Energy: -81.099816-0.002179j
[2025-09-08 08:03:09] [Iter 616/2250] R2[165/600], Temp: 0.8247, Energy: -81.119257+0.000136j
[2025-09-08 08:03:54] [Iter 617/2250] R2[166/600], Temp: 0.8227, Energy: -81.110287-0.002078j
[2025-09-08 08:04:40] [Iter 618/2250] R2[167/600], Temp: 0.8207, Energy: -81.073429-0.002354j
[2025-09-08 08:05:25] [Iter 619/2250] R2[168/600], Temp: 0.8187, Energy: -81.087604-0.008212j
[2025-09-08 08:06:11] [Iter 620/2250] R2[169/600], Temp: 0.8167, Energy: -81.102660-0.004873j
[2025-09-08 08:06:56] [Iter 621/2250] R2[170/600], Temp: 0.8147, Energy: -81.111812-0.000532j
[2025-09-08 08:07:42] [Iter 622/2250] R2[171/600], Temp: 0.8126, Energy: -81.073537-0.001232j
[2025-09-08 08:08:27] [Iter 623/2250] R2[172/600], Temp: 0.8106, Energy: -81.046287-0.002638j
[2025-09-08 08:09:13] [Iter 624/2250] R2[173/600], Temp: 0.8085, Energy: -81.001053-0.002705j
[2025-09-08 08:09:58] [Iter 625/2250] R2[174/600], Temp: 0.8065, Energy: -80.991677+0.002000j
[2025-09-08 08:10:44] [Iter 626/2250] R2[175/600], Temp: 0.8044, Energy: -81.054930+0.004503j
[2025-09-08 08:11:29] [Iter 627/2250] R2[176/600], Temp: 0.8023, Energy: -81.027809-0.003668j
[2025-09-08 08:12:15] [Iter 628/2250] R2[177/600], Temp: 0.8002, Energy: -81.101806-0.000803j
[2025-09-08 08:13:00] [Iter 629/2250] R2[178/600], Temp: 0.7981, Energy: -81.109626-0.004234j
[2025-09-08 08:13:46] [Iter 630/2250] R2[179/600], Temp: 0.7960, Energy: -81.094566+0.003238j
[2025-09-08 08:14:31] [Iter 631/2250] R2[180/600], Temp: 0.7939, Energy: -81.036311-0.004100j
[2025-09-08 08:15:17] [Iter 632/2250] R2[181/600], Temp: 0.7918, Energy: -81.048091-0.002630j
[2025-09-08 08:16:02] [Iter 633/2250] R2[182/600], Temp: 0.7896, Energy: -81.048590-0.003743j
[2025-09-08 08:16:48] [Iter 634/2250] R2[183/600], Temp: 0.7875, Energy: -81.064912-0.006425j
[2025-09-08 08:17:33] [Iter 635/2250] R2[184/600], Temp: 0.7854, Energy: -81.054224+0.000229j
[2025-09-08 08:18:19] [Iter 636/2250] R2[185/600], Temp: 0.7832, Energy: -81.071475+0.001541j
[2025-09-08 08:19:04] [Iter 637/2250] R2[186/600], Temp: 0.7810, Energy: -81.096500+0.000284j
[2025-09-08 08:19:50] [Iter 638/2250] R2[187/600], Temp: 0.7789, Energy: -81.105049+0.000803j
[2025-09-08 08:20:35] [Iter 639/2250] R2[188/600], Temp: 0.7767, Energy: -81.163584-0.000348j
[2025-09-08 08:21:21] [Iter 640/2250] R2[189/600], Temp: 0.7745, Energy: -81.116229+0.002198j
[2025-09-08 08:22:06] [Iter 641/2250] R2[190/600], Temp: 0.7723, Energy: -81.102054+0.003348j
[2025-09-08 08:22:52] [Iter 642/2250] R2[191/600], Temp: 0.7701, Energy: -81.096999-0.002430j
[2025-09-08 08:23:37] [Iter 643/2250] R2[192/600], Temp: 0.7679, Energy: -81.133737-0.000506j
[2025-09-08 08:24:23] [Iter 644/2250] R2[193/600], Temp: 0.7657, Energy: -81.098660-0.002704j
[2025-09-08 08:25:08] [Iter 645/2250] R2[194/600], Temp: 0.7635, Energy: -81.057690+0.007496j
[2025-09-08 08:25:54] [Iter 646/2250] R2[195/600], Temp: 0.7612, Energy: -81.094466+0.003198j
[2025-09-08 08:26:39] [Iter 647/2250] R2[196/600], Temp: 0.7590, Energy: -81.027097-0.002953j
[2025-09-08 08:27:25] [Iter 648/2250] R2[197/600], Temp: 0.7568, Energy: -81.076980+0.005056j
[2025-09-08 08:28:10] [Iter 649/2250] R2[198/600], Temp: 0.7545, Energy: -81.196620-0.000141j
[2025-09-08 08:28:56] [Iter 650/2250] R2[199/600], Temp: 0.7523, Energy: -81.204885+0.001092j
[2025-09-08 08:29:41] [Iter 651/2250] R2[200/600], Temp: 0.7500, Energy: -81.226593+0.005082j
[2025-09-08 08:30:27] [Iter 652/2250] R2[201/600], Temp: 0.7477, Energy: -81.180313+0.001994j
[2025-09-08 08:31:12] [Iter 653/2250] R2[202/600], Temp: 0.7455, Energy: -81.153571-0.000350j
[2025-09-08 08:31:58] [Iter 654/2250] R2[203/600], Temp: 0.7432, Energy: -81.190363+0.002412j
[2025-09-08 08:32:44] [Iter 655/2250] R2[204/600], Temp: 0.7409, Energy: -81.113648+0.001727j
[2025-09-08 08:33:29] [Iter 656/2250] R2[205/600], Temp: 0.7386, Energy: -81.092857-0.002763j
[2025-09-08 08:34:15] [Iter 657/2250] R2[206/600], Temp: 0.7363, Energy: -81.174790-0.005118j
[2025-09-08 08:35:00] [Iter 658/2250] R2[207/600], Temp: 0.7340, Energy: -81.139165-0.002124j
[2025-09-08 08:35:46] [Iter 659/2250] R2[208/600], Temp: 0.7316, Energy: -81.126717-0.010549j
[2025-09-08 08:36:31] [Iter 660/2250] R2[209/600], Temp: 0.7293, Energy: -81.076763-0.001312j
[2025-09-08 08:37:17] [Iter 661/2250] R2[210/600], Temp: 0.7270, Energy: -81.019478+0.001879j
[2025-09-08 08:38:02] [Iter 662/2250] R2[211/600], Temp: 0.7247, Energy: -81.070329-0.005947j
[2025-09-08 08:38:48] [Iter 663/2250] R2[212/600], Temp: 0.7223, Energy: -81.102162-0.004923j
[2025-09-08 08:39:33] [Iter 664/2250] R2[213/600], Temp: 0.7200, Energy: -81.068737+0.002707j
[2025-09-08 08:40:19] [Iter 665/2250] R2[214/600], Temp: 0.7176, Energy: -81.048839+0.000585j
[2025-09-08 08:41:04] [Iter 666/2250] R2[215/600], Temp: 0.7153, Energy: -81.042735+0.001484j
[2025-09-08 08:41:50] [Iter 667/2250] R2[216/600], Temp: 0.7129, Energy: -81.061269-0.000955j
[2025-09-08 08:42:35] [Iter 668/2250] R2[217/600], Temp: 0.7105, Energy: -81.008272-0.004882j
[2025-09-08 08:43:21] [Iter 669/2250] R2[218/600], Temp: 0.7081, Energy: -80.975033+0.002515j
[2025-09-08 08:44:06] [Iter 670/2250] R2[219/600], Temp: 0.7058, Energy: -81.012859+0.000742j
[2025-09-08 08:44:52] [Iter 671/2250] R2[220/600], Temp: 0.7034, Energy: -81.073495-0.001944j
[2025-09-08 08:45:37] [Iter 672/2250] R2[221/600], Temp: 0.7010, Energy: -81.039224-0.004819j
[2025-09-08 08:46:23] [Iter 673/2250] R2[222/600], Temp: 0.6986, Energy: -81.051371-0.005978j
[2025-09-08 08:47:08] [Iter 674/2250] R2[223/600], Temp: 0.6962, Energy: -81.007440+0.002230j
[2025-09-08 08:47:54] [Iter 675/2250] R2[224/600], Temp: 0.6938, Energy: -81.048782+0.003204j
[2025-09-08 08:48:39] [Iter 676/2250] R2[225/600], Temp: 0.6913, Energy: -81.056520+0.002792j
[2025-09-08 08:49:25] [Iter 677/2250] R2[226/600], Temp: 0.6889, Energy: -81.058435+0.001427j
[2025-09-08 08:50:10] [Iter 678/2250] R2[227/600], Temp: 0.6865, Energy: -80.995383+0.000170j
[2025-09-08 08:50:56] [Iter 679/2250] R2[228/600], Temp: 0.6841, Energy: -81.036632-0.005046j
[2025-09-08 08:51:41] [Iter 680/2250] R2[229/600], Temp: 0.6816, Energy: -81.114540+0.001714j
[2025-09-08 08:52:27] [Iter 681/2250] R2[230/600], Temp: 0.6792, Energy: -81.070264+0.006318j
[2025-09-08 08:53:12] [Iter 682/2250] R2[231/600], Temp: 0.6767, Energy: -81.090361-0.001990j
[2025-09-08 08:53:58] [Iter 683/2250] R2[232/600], Temp: 0.6743, Energy: -81.063070-0.001393j
[2025-09-08 08:54:43] [Iter 684/2250] R2[233/600], Temp: 0.6718, Energy: -81.000050-0.002742j
[2025-09-08 08:55:29] [Iter 685/2250] R2[234/600], Temp: 0.6694, Energy: -81.069998-0.002596j
[2025-09-08 08:56:14] [Iter 686/2250] R2[235/600], Temp: 0.6669, Energy: -81.022637-0.005589j
[2025-09-08 08:57:00] [Iter 687/2250] R2[236/600], Temp: 0.6644, Energy: -81.048437-0.001465j
[2025-09-08 08:57:45] [Iter 688/2250] R2[237/600], Temp: 0.6620, Energy: -81.073822+0.003477j
[2025-09-08 08:58:31] [Iter 689/2250] R2[238/600], Temp: 0.6595, Energy: -81.129023-0.004170j
[2025-09-08 08:59:16] [Iter 690/2250] R2[239/600], Temp: 0.6570, Energy: -81.109531-0.001299j
[2025-09-08 09:00:02] [Iter 691/2250] R2[240/600], Temp: 0.6545, Energy: -81.075512-0.001113j
[2025-09-08 09:00:48] [Iter 692/2250] R2[241/600], Temp: 0.6520, Energy: -81.079600-0.000315j
[2025-09-08 09:01:33] [Iter 693/2250] R2[242/600], Temp: 0.6495, Energy: -81.170366-0.002317j
[2025-09-08 09:02:18] [Iter 694/2250] R2[243/600], Temp: 0.6470, Energy: -81.138784-0.003234j
[2025-09-08 09:03:04] [Iter 695/2250] R2[244/600], Temp: 0.6445, Energy: -81.032613+0.002554j
[2025-09-08 09:03:49] [Iter 696/2250] R2[245/600], Temp: 0.6420, Energy: -81.090006-0.000489j
[2025-09-08 09:04:35] [Iter 697/2250] R2[246/600], Temp: 0.6395, Energy: -81.028292+0.007821j
[2025-09-08 09:05:20] [Iter 698/2250] R2[247/600], Temp: 0.6370, Energy: -81.056757-0.001876j
[2025-09-08 09:06:06] [Iter 699/2250] R2[248/600], Temp: 0.6345, Energy: -81.028233+0.003023j
[2025-09-08 09:06:52] [Iter 700/2250] R2[249/600], Temp: 0.6319, Energy: -81.095283-0.002438j
[2025-09-08 09:07:37] [Iter 701/2250] R2[250/600], Temp: 0.6294, Energy: -81.036338-0.007395j
[2025-09-08 09:08:22] [Iter 702/2250] R2[251/600], Temp: 0.6269, Energy: -81.036183+0.000214j
[2025-09-08 09:09:08] [Iter 703/2250] R2[252/600], Temp: 0.6243, Energy: -81.095091+0.001703j
[2025-09-08 09:09:53] [Iter 704/2250] R2[253/600], Temp: 0.6218, Energy: -81.038911-0.001311j
[2025-09-08 09:10:39] [Iter 705/2250] R2[254/600], Temp: 0.6193, Energy: -81.070158+0.002220j
[2025-09-08 09:11:24] [Iter 706/2250] R2[255/600], Temp: 0.6167, Energy: -81.117066-0.001573j
[2025-09-08 09:12:10] [Iter 707/2250] R2[256/600], Temp: 0.6142, Energy: -81.059052+0.004872j
[2025-09-08 09:12:55] [Iter 708/2250] R2[257/600], Temp: 0.6116, Energy: -81.098302+0.005264j
[2025-09-08 09:13:41] [Iter 709/2250] R2[258/600], Temp: 0.6091, Energy: -81.157155-0.002783j
[2025-09-08 09:14:26] [Iter 710/2250] R2[259/600], Temp: 0.6065, Energy: -81.139937-0.003524j
[2025-09-08 09:15:12] [Iter 711/2250] R2[260/600], Temp: 0.6040, Energy: -81.083531-0.000916j
[2025-09-08 09:15:58] [Iter 712/2250] R2[261/600], Temp: 0.6014, Energy: -81.074005+0.000173j
[2025-09-08 09:16:43] [Iter 713/2250] R2[262/600], Temp: 0.5988, Energy: -81.080179-0.002063j
[2025-09-08 09:17:29] [Iter 714/2250] R2[263/600], Temp: 0.5963, Energy: -81.066294-0.000622j
[2025-09-08 09:18:14] [Iter 715/2250] R2[264/600], Temp: 0.5937, Energy: -81.114494-0.008127j
[2025-09-08 09:19:00] [Iter 716/2250] R2[265/600], Temp: 0.5911, Energy: -81.133560-0.003352j
[2025-09-08 09:19:45] [Iter 717/2250] R2[266/600], Temp: 0.5885, Energy: -81.009244+0.006318j
[2025-09-08 09:20:31] [Iter 718/2250] R2[267/600], Temp: 0.5860, Energy: -81.028702-0.004597j
[2025-09-08 09:21:16] [Iter 719/2250] R2[268/600], Temp: 0.5834, Energy: -81.095027+0.010650j
[2025-09-08 09:22:02] [Iter 720/2250] R2[269/600], Temp: 0.5808, Energy: -81.029641-0.001957j
[2025-09-08 09:22:47] [Iter 721/2250] R2[270/600], Temp: 0.5782, Energy: -81.066747+0.002731j
[2025-09-08 09:23:33] [Iter 722/2250] R2[271/600], Temp: 0.5756, Energy: -81.050301-0.000044j
[2025-09-08 09:24:19] [Iter 723/2250] R2[272/600], Temp: 0.5730, Energy: -81.044847-0.000796j
[2025-09-08 09:25:04] [Iter 724/2250] R2[273/600], Temp: 0.5705, Energy: -81.089679+0.002509j
[2025-09-08 09:25:49] [Iter 725/2250] R2[274/600], Temp: 0.5679, Energy: -81.146176-0.004615j
[2025-09-08 09:26:35] [Iter 726/2250] R2[275/600], Temp: 0.5653, Energy: -81.115491+0.003467j
[2025-09-08 09:27:21] [Iter 727/2250] R2[276/600], Temp: 0.5627, Energy: -81.092767-0.004005j
[2025-09-08 09:28:06] [Iter 728/2250] R2[277/600], Temp: 0.5601, Energy: -81.122480-0.002075j
[2025-09-08 09:28:51] [Iter 729/2250] R2[278/600], Temp: 0.5575, Energy: -81.056706+0.001881j
[2025-09-08 09:29:37] [Iter 730/2250] R2[279/600], Temp: 0.5549, Energy: -81.098166-0.002706j
[2025-09-08 09:30:23] [Iter 731/2250] R2[280/600], Temp: 0.5523, Energy: -81.132199-0.001252j
[2025-09-08 09:31:08] [Iter 732/2250] R2[281/600], Temp: 0.5497, Energy: -81.165387+0.002024j
[2025-09-08 09:31:54] [Iter 733/2250] R2[282/600], Temp: 0.5471, Energy: -81.151248-0.001271j
[2025-09-08 09:32:39] [Iter 734/2250] R2[283/600], Temp: 0.5444, Energy: -81.147568+0.003087j
[2025-09-08 09:33:25] [Iter 735/2250] R2[284/600], Temp: 0.5418, Energy: -81.076215+0.001864j
[2025-09-08 09:34:10] [Iter 736/2250] R2[285/600], Temp: 0.5392, Energy: -81.090582+0.000360j
[2025-09-08 09:34:56] [Iter 737/2250] R2[286/600], Temp: 0.5366, Energy: -81.101625-0.004073j
[2025-09-08 09:35:41] [Iter 738/2250] R2[287/600], Temp: 0.5340, Energy: -81.114275+0.001451j
[2025-09-08 09:36:27] [Iter 739/2250] R2[288/600], Temp: 0.5314, Energy: -81.137801-0.003784j
[2025-09-08 09:37:12] [Iter 740/2250] R2[289/600], Temp: 0.5288, Energy: -81.092954+0.001887j
[2025-09-08 09:37:58] [Iter 741/2250] R2[290/600], Temp: 0.5262, Energy: -81.153708-0.001181j
[2025-09-08 09:38:43] [Iter 742/2250] R2[291/600], Temp: 0.5236, Energy: -81.110269-0.003078j
[2025-09-08 09:39:29] [Iter 743/2250] R2[292/600], Temp: 0.5209, Energy: -81.074368+0.001512j
[2025-09-08 09:40:15] [Iter 744/2250] R2[293/600], Temp: 0.5183, Energy: -81.075557-0.001666j
[2025-09-08 09:41:00] [Iter 745/2250] R2[294/600], Temp: 0.5157, Energy: -81.132095-0.006171j
[2025-09-08 09:41:45] [Iter 746/2250] R2[295/600], Temp: 0.5131, Energy: -81.074845-0.001788j
[2025-09-08 09:42:31] [Iter 747/2250] R2[296/600], Temp: 0.5105, Energy: -81.161496-0.003410j
[2025-09-08 09:43:16] [Iter 748/2250] R2[297/600], Temp: 0.5079, Energy: -81.118431-0.000352j
[2025-09-08 09:44:02] [Iter 749/2250] R2[298/600], Temp: 0.5052, Energy: -81.067675+0.001353j
[2025-09-08 09:44:47] [Iter 750/2250] R2[299/600], Temp: 0.5026, Energy: -81.064855-0.006376j
[2025-09-08 09:44:47] ✓ Checkpoint saved: checkpoint_iter_000750.pkl
[2025-09-08 09:45:33] [Iter 751/2250] R2[300/600], Temp: 0.5000, Energy: -81.071489+0.000237j
[2025-09-08 09:46:18] [Iter 752/2250] R2[301/600], Temp: 0.4974, Energy: -80.985212-0.002945j
[2025-09-08 09:47:04] [Iter 753/2250] R2[302/600], Temp: 0.4948, Energy: -81.099874+0.002699j
[2025-09-08 09:47:50] [Iter 754/2250] R2[303/600], Temp: 0.4921, Energy: -81.088879+0.000108j
[2025-09-08 09:48:35] [Iter 755/2250] R2[304/600], Temp: 0.4895, Energy: -81.094100+0.000430j
[2025-09-08 09:49:21] [Iter 756/2250] R2[305/600], Temp: 0.4869, Energy: -81.022969+0.002113j
[2025-09-08 09:50:06] [Iter 757/2250] R2[306/600], Temp: 0.4843, Energy: -81.010152+0.000128j
[2025-09-08 09:50:52] [Iter 758/2250] R2[307/600], Temp: 0.4817, Energy: -81.035651+0.001855j
[2025-09-08 09:51:37] [Iter 759/2250] R2[308/600], Temp: 0.4791, Energy: -81.021905-0.000480j
[2025-09-08 09:52:23] [Iter 760/2250] R2[309/600], Temp: 0.4764, Energy: -81.083653-0.002440j
[2025-09-08 09:53:08] [Iter 761/2250] R2[310/600], Temp: 0.4738, Energy: -81.025987-0.001223j
[2025-09-08 09:53:54] [Iter 762/2250] R2[311/600], Temp: 0.4712, Energy: -81.091245+0.001917j
[2025-09-08 09:54:39] [Iter 763/2250] R2[312/600], Temp: 0.4686, Energy: -81.080939-0.001990j
[2025-09-08 09:55:25] [Iter 764/2250] R2[313/600], Temp: 0.4660, Energy: -81.044264-0.000254j
[2025-09-08 09:56:10] [Iter 765/2250] R2[314/600], Temp: 0.4634, Energy: -81.101335-0.002273j
[2025-09-08 09:56:56] [Iter 766/2250] R2[315/600], Temp: 0.4608, Energy: -81.059278+0.000317j
[2025-09-08 09:57:41] [Iter 767/2250] R2[316/600], Temp: 0.4582, Energy: -81.061940-0.001781j
[2025-09-08 09:58:27] [Iter 768/2250] R2[317/600], Temp: 0.4556, Energy: -81.127429+0.002043j
[2025-09-08 09:59:12] [Iter 769/2250] R2[318/600], Temp: 0.4529, Energy: -81.046324+0.001614j
[2025-09-08 09:59:58] [Iter 770/2250] R2[319/600], Temp: 0.4503, Energy: -81.065583+0.004095j
[2025-09-08 10:00:43] [Iter 771/2250] R2[320/600], Temp: 0.4477, Energy: -81.045722-0.000122j
[2025-09-08 10:01:29] [Iter 772/2250] R2[321/600], Temp: 0.4451, Energy: -81.113032+0.000107j
[2025-09-08 10:02:14] [Iter 773/2250] R2[322/600], Temp: 0.4425, Energy: -81.137252-0.004815j
[2025-09-08 10:03:00] [Iter 774/2250] R2[323/600], Temp: 0.4399, Energy: -81.097102+0.002269j
[2025-09-08 10:03:45] [Iter 775/2250] R2[324/600], Temp: 0.4373, Energy: -81.072932-0.003319j
[2025-09-08 10:04:31] [Iter 776/2250] R2[325/600], Temp: 0.4347, Energy: -81.119527+0.000724j
[2025-09-08 10:05:17] [Iter 777/2250] R2[326/600], Temp: 0.4321, Energy: -81.112372+0.000658j
[2025-09-08 10:06:02] [Iter 778/2250] R2[327/600], Temp: 0.4295, Energy: -81.166269-0.000826j
[2025-09-08 10:06:48] [Iter 779/2250] R2[328/600], Temp: 0.4270, Energy: -81.116502-0.000455j
[2025-09-08 10:07:33] [Iter 780/2250] R2[329/600], Temp: 0.4244, Energy: -81.122117-0.001442j
[2025-09-08 10:08:19] [Iter 781/2250] R2[330/600], Temp: 0.4218, Energy: -81.195794-0.003799j
[2025-09-08 10:09:04] [Iter 782/2250] R2[331/600], Temp: 0.4192, Energy: -81.148001-0.003934j
[2025-09-08 10:09:50] [Iter 783/2250] R2[332/600], Temp: 0.4166, Energy: -81.184014-0.000231j
[2025-09-08 10:10:35] [Iter 784/2250] R2[333/600], Temp: 0.4140, Energy: -81.142888-0.003523j
[2025-09-08 10:11:21] [Iter 785/2250] R2[334/600], Temp: 0.4115, Energy: -81.126099-0.002949j
[2025-09-08 10:12:07] [Iter 786/2250] R2[335/600], Temp: 0.4089, Energy: -81.134389-0.000897j
[2025-09-08 10:12:52] [Iter 787/2250] R2[336/600], Temp: 0.4063, Energy: -81.156502+0.000782j
[2025-09-08 10:13:38] [Iter 788/2250] R2[337/600], Temp: 0.4037, Energy: -81.092490-0.001371j
[2025-09-08 10:14:23] [Iter 789/2250] R2[338/600], Temp: 0.4012, Energy: -81.141429-0.000439j
[2025-09-08 10:15:09] [Iter 790/2250] R2[339/600], Temp: 0.3986, Energy: -81.102866-0.000833j
[2025-09-08 10:15:54] [Iter 791/2250] R2[340/600], Temp: 0.3960, Energy: -81.201687+0.005396j
[2025-09-08 10:16:40] [Iter 792/2250] R2[341/600], Temp: 0.3935, Energy: -81.193600-0.000899j
[2025-09-08 10:17:25] [Iter 793/2250] R2[342/600], Temp: 0.3909, Energy: -81.166695-0.000601j
[2025-09-08 10:18:11] [Iter 794/2250] R2[343/600], Temp: 0.3884, Energy: -81.118380-0.000362j
[2025-09-08 10:18:56] [Iter 795/2250] R2[344/600], Temp: 0.3858, Energy: -81.121014+0.002953j
[2025-09-08 10:19:42] [Iter 796/2250] R2[345/600], Temp: 0.3833, Energy: -81.154456+0.001055j
[2025-09-08 10:20:27] [Iter 797/2250] R2[346/600], Temp: 0.3807, Energy: -81.131056-0.001401j
[2025-09-08 10:21:13] [Iter 798/2250] R2[347/600], Temp: 0.3782, Energy: -81.140953-0.000662j
[2025-09-08 10:21:58] [Iter 799/2250] R2[348/600], Temp: 0.3757, Energy: -81.142621-0.000258j
[2025-09-08 10:22:44] [Iter 800/2250] R2[349/600], Temp: 0.3731, Energy: -81.121934-0.001560j
[2025-09-08 10:23:30] [Iter 801/2250] R2[350/600], Temp: 0.3706, Energy: -81.082305+0.000647j
[2025-09-08 10:24:15] [Iter 802/2250] R2[351/600], Temp: 0.3681, Energy: -81.103288-0.000913j
[2025-09-08 10:25:00] [Iter 803/2250] R2[352/600], Temp: 0.3655, Energy: -81.203066-0.000068j
[2025-09-08 10:25:46] [Iter 804/2250] R2[353/600], Temp: 0.3630, Energy: -81.204381-0.001357j
[2025-09-08 10:26:31] [Iter 805/2250] R2[354/600], Temp: 0.3605, Energy: -81.204827-0.000045j
[2025-09-08 10:27:17] [Iter 806/2250] R2[355/600], Temp: 0.3580, Energy: -81.163222+0.003893j
[2025-09-08 10:28:02] [Iter 807/2250] R2[356/600], Temp: 0.3555, Energy: -81.219844+0.003148j
[2025-09-08 10:28:48] [Iter 808/2250] R2[357/600], Temp: 0.3530, Energy: -81.215434-0.000855j
[2025-09-08 10:29:33] [Iter 809/2250] R2[358/600], Temp: 0.3505, Energy: -81.207829-0.002962j
[2025-09-08 10:30:19] [Iter 810/2250] R2[359/600], Temp: 0.3480, Energy: -81.241317+0.004032j
[2025-09-08 10:31:04] [Iter 811/2250] R2[360/600], Temp: 0.3455, Energy: -81.220508+0.004161j
[2025-09-08 10:31:50] [Iter 812/2250] R2[361/600], Temp: 0.3430, Energy: -81.146155-0.000128j
[2025-09-08 10:32:35] [Iter 813/2250] R2[362/600], Temp: 0.3405, Energy: -81.155810+0.000855j
[2025-09-08 10:33:21] [Iter 814/2250] R2[363/600], Temp: 0.3380, Energy: -81.182247-0.000066j
[2025-09-08 10:34:06] [Iter 815/2250] R2[364/600], Temp: 0.3356, Energy: -81.187789-0.001318j
[2025-09-08 10:34:52] [Iter 816/2250] R2[365/600], Temp: 0.3331, Energy: -81.132990+0.001262j
[2025-09-08 10:35:38] [Iter 817/2250] R2[366/600], Temp: 0.3306, Energy: -81.140370+0.004561j
[2025-09-08 10:36:23] [Iter 818/2250] R2[367/600], Temp: 0.3282, Energy: -81.155965+0.000995j
[2025-09-08 10:37:09] [Iter 819/2250] R2[368/600], Temp: 0.3257, Energy: -81.178075-0.000823j
[2025-09-08 10:37:54] [Iter 820/2250] R2[369/600], Temp: 0.3233, Energy: -81.237988-0.002427j
[2025-09-08 10:38:40] [Iter 821/2250] R2[370/600], Temp: 0.3208, Energy: -81.148931-0.001408j
[2025-09-08 10:39:25] [Iter 822/2250] R2[371/600], Temp: 0.3184, Energy: -81.152308-0.000752j
[2025-09-08 10:40:11] [Iter 823/2250] R2[372/600], Temp: 0.3159, Energy: -81.151313-0.000426j
[2025-09-08 10:40:56] [Iter 824/2250] R2[373/600], Temp: 0.3135, Energy: -81.133772-0.003473j
[2025-09-08 10:41:42] [Iter 825/2250] R2[374/600], Temp: 0.3111, Energy: -81.071528-0.001089j
[2025-09-08 10:42:27] [Iter 826/2250] R2[375/600], Temp: 0.3087, Energy: -81.070799-0.002779j
[2025-09-08 10:43:13] [Iter 827/2250] R2[376/600], Temp: 0.3062, Energy: -81.120287-0.001400j
[2025-09-08 10:43:58] [Iter 828/2250] R2[377/600], Temp: 0.3038, Energy: -81.103243-0.001224j
[2025-09-08 10:44:44] [Iter 829/2250] R2[378/600], Temp: 0.3014, Energy: -81.153636-0.001178j
[2025-09-08 10:45:29] [Iter 830/2250] R2[379/600], Temp: 0.2990, Energy: -81.127641+0.006493j
[2025-09-08 10:46:15] [Iter 831/2250] R2[380/600], Temp: 0.2966, Energy: -81.107067-0.004210j
[2025-09-08 10:47:01] [Iter 832/2250] R2[381/600], Temp: 0.2942, Energy: -81.048151-0.000157j
[2025-09-08 10:47:46] [Iter 833/2250] R2[382/600], Temp: 0.2919, Energy: -81.060771+0.001898j
[2025-09-08 10:48:32] [Iter 834/2250] R2[383/600], Temp: 0.2895, Energy: -81.123220+0.004306j
[2025-09-08 10:49:17] [Iter 835/2250] R2[384/600], Temp: 0.2871, Energy: -81.065305+0.001484j
[2025-09-08 10:50:03] [Iter 836/2250] R2[385/600], Temp: 0.2847, Energy: -81.111820+0.001515j
[2025-09-08 10:50:48] [Iter 837/2250] R2[386/600], Temp: 0.2824, Energy: -81.098528+0.000533j
[2025-09-08 10:51:34] [Iter 838/2250] R2[387/600], Temp: 0.2800, Energy: -81.134946-0.001405j
[2025-09-08 10:52:19] [Iter 839/2250] R2[388/600], Temp: 0.2777, Energy: -81.051374+0.002324j
[2025-09-08 10:53:05] [Iter 840/2250] R2[389/600], Temp: 0.2753, Energy: -81.032293-0.005992j
[2025-09-08 10:53:50] [Iter 841/2250] R2[390/600], Temp: 0.2730, Energy: -81.076381-0.001814j
[2025-09-08 10:54:36] [Iter 842/2250] R2[391/600], Temp: 0.2707, Energy: -81.087031-0.000102j
[2025-09-08 10:55:21] [Iter 843/2250] R2[392/600], Temp: 0.2684, Energy: -81.094117-0.004133j
[2025-09-08 10:56:07] [Iter 844/2250] R2[393/600], Temp: 0.2660, Energy: -81.198743+0.002182j
[2025-09-08 10:56:52] [Iter 845/2250] R2[394/600], Temp: 0.2637, Energy: -81.164154-0.001360j
[2025-09-08 10:57:38] [Iter 846/2250] R2[395/600], Temp: 0.2614, Energy: -81.166915-0.000554j
[2025-09-08 10:58:24] [Iter 847/2250] R2[396/600], Temp: 0.2591, Energy: -81.135665-0.000603j
[2025-09-08 10:59:09] [Iter 848/2250] R2[397/600], Temp: 0.2568, Energy: -81.209601+0.000532j
[2025-09-08 10:59:55] [Iter 849/2250] R2[398/600], Temp: 0.2545, Energy: -81.218535+0.001145j
[2025-09-08 11:00:40] [Iter 850/2250] R2[399/600], Temp: 0.2523, Energy: -81.158043-0.000768j
[2025-09-08 11:01:26] [Iter 851/2250] R2[400/600], Temp: 0.2500, Energy: -81.180606+0.000205j
[2025-09-08 11:02:11] [Iter 852/2250] R2[401/600], Temp: 0.2477, Energy: -81.224962+0.004048j
[2025-09-08 11:02:57] [Iter 853/2250] R2[402/600], Temp: 0.2455, Energy: -81.205801-0.003587j
[2025-09-08 11:03:42] [Iter 854/2250] R2[403/600], Temp: 0.2432, Energy: -81.154560+0.000596j
[2025-09-08 11:04:28] [Iter 855/2250] R2[404/600], Temp: 0.2410, Energy: -81.144241-0.000895j
[2025-09-08 11:05:13] [Iter 856/2250] R2[405/600], Temp: 0.2388, Energy: -81.157698-0.002468j
[2025-09-08 11:05:59] [Iter 857/2250] R2[406/600], Temp: 0.2365, Energy: -81.185926+0.001064j
[2025-09-08 11:06:44] [Iter 858/2250] R2[407/600], Temp: 0.2343, Energy: -81.174719+0.002558j
[2025-09-08 11:07:30] [Iter 859/2250] R2[408/600], Temp: 0.2321, Energy: -81.188061-0.000902j
[2025-09-08 11:08:16] [Iter 860/2250] R2[409/600], Temp: 0.2299, Energy: -81.174460+0.001903j
[2025-09-08 11:09:01] [Iter 861/2250] R2[410/600], Temp: 0.2277, Energy: -81.210842+0.000393j
[2025-09-08 11:09:47] [Iter 862/2250] R2[411/600], Temp: 0.2255, Energy: -81.120596-0.001256j
[2025-09-08 11:10:32] [Iter 863/2250] R2[412/600], Temp: 0.2233, Energy: -81.140694+0.000364j
[2025-09-08 11:11:18] [Iter 864/2250] R2[413/600], Temp: 0.2211, Energy: -81.138804+0.001582j
[2025-09-08 11:12:03] [Iter 865/2250] R2[414/600], Temp: 0.2190, Energy: -81.158263+0.000224j
[2025-09-08 11:12:49] [Iter 866/2250] R2[415/600], Temp: 0.2168, Energy: -81.128809-0.000052j
[2025-09-08 11:13:34] [Iter 867/2250] R2[416/600], Temp: 0.2146, Energy: -81.175108+0.004070j
[2025-09-08 11:14:20] [Iter 868/2250] R2[417/600], Temp: 0.2125, Energy: -81.206144-0.000411j
[2025-09-08 11:15:06] [Iter 869/2250] R2[418/600], Temp: 0.2104, Energy: -81.179756-0.002872j
[2025-09-08 11:15:51] [Iter 870/2250] R2[419/600], Temp: 0.2082, Energy: -81.169043+0.006451j
[2025-09-08 11:16:37] [Iter 871/2250] R2[420/600], Temp: 0.2061, Energy: -81.216378+0.001096j
[2025-09-08 11:17:22] [Iter 872/2250] R2[421/600], Temp: 0.2040, Energy: -81.186603-0.004326j
[2025-09-08 11:18:08] [Iter 873/2250] R2[422/600], Temp: 0.2019, Energy: -81.226533-0.000880j
[2025-09-08 11:18:53] [Iter 874/2250] R2[423/600], Temp: 0.1998, Energy: -81.256115+0.003053j
[2025-09-08 11:19:39] [Iter 875/2250] R2[424/600], Temp: 0.1977, Energy: -81.172666+0.002070j
[2025-09-08 11:20:24] [Iter 876/2250] R2[425/600], Temp: 0.1956, Energy: -81.240298-0.003094j
[2025-09-08 11:21:10] [Iter 877/2250] R2[426/600], Temp: 0.1935, Energy: -81.139316-0.000739j
[2025-09-08 11:21:55] [Iter 878/2250] R2[427/600], Temp: 0.1915, Energy: -81.163771+0.001185j
[2025-09-08 11:22:41] [Iter 879/2250] R2[428/600], Temp: 0.1894, Energy: -81.159485+0.001444j
[2025-09-08 11:23:26] [Iter 880/2250] R2[429/600], Temp: 0.1874, Energy: -81.192695+0.001080j
[2025-09-08 11:24:12] [Iter 881/2250] R2[430/600], Temp: 0.1853, Energy: -81.163066+0.000793j
[2025-09-08 11:24:57] [Iter 882/2250] R2[431/600], Temp: 0.1833, Energy: -81.131162-0.002238j
[2025-09-08 11:25:43] [Iter 883/2250] R2[432/600], Temp: 0.1813, Energy: -81.120261-0.001243j
[2025-09-08 11:26:28] [Iter 884/2250] R2[433/600], Temp: 0.1793, Energy: -81.126244+0.004229j
[2025-09-08 11:27:14] [Iter 885/2250] R2[434/600], Temp: 0.1773, Energy: -81.114776-0.002725j
[2025-09-08 11:27:59] [Iter 886/2250] R2[435/600], Temp: 0.1753, Energy: -81.198502+0.004501j
[2025-09-08 11:28:45] [Iter 887/2250] R2[436/600], Temp: 0.1733, Energy: -81.246929-0.002401j
[2025-09-08 11:29:30] [Iter 888/2250] R2[437/600], Temp: 0.1713, Energy: -81.221700+0.001354j
[2025-09-08 11:30:16] [Iter 889/2250] R2[438/600], Temp: 0.1693, Energy: -81.251782+0.005138j
[2025-09-08 11:31:01] [Iter 890/2250] R2[439/600], Temp: 0.1674, Energy: -81.185040-0.001377j
[2025-09-08 11:31:47] [Iter 891/2250] R2[440/600], Temp: 0.1654, Energy: -81.117414-0.000764j
[2025-09-08 11:32:32] [Iter 892/2250] R2[441/600], Temp: 0.1635, Energy: -81.195292-0.000135j
[2025-09-08 11:33:18] [Iter 893/2250] R2[442/600], Temp: 0.1616, Energy: -81.194196-0.001596j
[2025-09-08 11:34:03] [Iter 894/2250] R2[443/600], Temp: 0.1596, Energy: -81.162577+0.001499j
[2025-09-08 11:34:49] [Iter 895/2250] R2[444/600], Temp: 0.1577, Energy: -81.117716+0.001695j
[2025-09-08 11:35:34] [Iter 896/2250] R2[445/600], Temp: 0.1558, Energy: -81.138492+0.000401j
[2025-09-08 11:36:20] [Iter 897/2250] R2[446/600], Temp: 0.1539, Energy: -81.104021-0.000525j
[2025-09-08 11:37:06] [Iter 898/2250] R2[447/600], Temp: 0.1520, Energy: -81.096794-0.004334j
[2025-09-08 11:37:51] [Iter 899/2250] R2[448/600], Temp: 0.1502, Energy: -81.183356-0.001963j
[2025-09-08 11:38:36] [Iter 900/2250] R2[449/600], Temp: 0.1483, Energy: -81.196877+0.005020j
[2025-09-08 11:39:22] [Iter 901/2250] R2[450/600], Temp: 0.1464, Energy: -81.187887-0.000366j
[2025-09-08 11:40:07] [Iter 902/2250] R2[451/600], Temp: 0.1446, Energy: -81.256476-0.001997j
[2025-09-08 11:40:53] [Iter 903/2250] R2[452/600], Temp: 0.1428, Energy: -81.209680+0.003328j
[2025-09-08 11:41:38] [Iter 904/2250] R2[453/600], Temp: 0.1409, Energy: -81.203552-0.002128j
[2025-09-08 11:42:24] [Iter 905/2250] R2[454/600], Temp: 0.1391, Energy: -81.226723-0.004463j
[2025-09-08 11:43:10] [Iter 906/2250] R2[455/600], Temp: 0.1373, Energy: -81.249812-0.003788j
[2025-09-08 11:43:55] [Iter 907/2250] R2[456/600], Temp: 0.1355, Energy: -81.151076-0.001513j
[2025-09-08 11:44:41] [Iter 908/2250] R2[457/600], Temp: 0.1337, Energy: -81.090418-0.000928j
[2025-09-08 11:45:26] [Iter 909/2250] R2[458/600], Temp: 0.1320, Energy: -81.113926+0.001498j
[2025-09-08 11:46:12] [Iter 910/2250] R2[459/600], Temp: 0.1302, Energy: -81.115371+0.000887j
[2025-09-08 11:46:57] [Iter 911/2250] R2[460/600], Temp: 0.1284, Energy: -81.118042+0.000653j
[2025-09-08 11:47:43] [Iter 912/2250] R2[461/600], Temp: 0.1267, Energy: -81.158099+0.000168j
[2025-09-08 11:48:28] [Iter 913/2250] R2[462/600], Temp: 0.1249, Energy: -81.129918-0.002104j
[2025-09-08 11:49:14] [Iter 914/2250] R2[463/600], Temp: 0.1232, Energy: -81.125639-0.003095j
[2025-09-08 11:50:00] [Iter 915/2250] R2[464/600], Temp: 0.1215, Energy: -81.178723+0.001068j
[2025-09-08 11:50:45] [Iter 916/2250] R2[465/600], Temp: 0.1198, Energy: -81.071028+0.001651j
[2025-09-08 11:51:31] [Iter 917/2250] R2[466/600], Temp: 0.1181, Energy: -81.061375-0.003991j
[2025-09-08 11:52:16] [Iter 918/2250] R2[467/600], Temp: 0.1164, Energy: -81.135608-0.000728j
[2025-09-08 11:53:02] [Iter 919/2250] R2[468/600], Temp: 0.1147, Energy: -81.125490+0.002291j
[2025-09-08 11:53:48] [Iter 920/2250] R2[469/600], Temp: 0.1131, Energy: -81.085325-0.001027j
[2025-09-08 11:54:33] [Iter 921/2250] R2[470/600], Temp: 0.1114, Energy: -81.084152+0.003422j
[2025-09-08 11:55:18] [Iter 922/2250] R2[471/600], Temp: 0.1098, Energy: -81.112931-0.000959j
[2025-09-08 11:56:04] [Iter 923/2250] R2[472/600], Temp: 0.1082, Energy: -81.214497+0.001935j
[2025-09-08 11:56:55] [Iter 924/2250] R2[473/600], Temp: 0.1065, Energy: -81.183060+0.003557j
[2025-09-08 11:57:40] [Iter 925/2250] R2[474/600], Temp: 0.1049, Energy: -81.175414-0.002586j
[2025-09-08 11:58:26] [Iter 926/2250] R2[475/600], Temp: 0.1033, Energy: -81.175292-0.000989j
[2025-09-08 11:59:11] [Iter 927/2250] R2[476/600], Temp: 0.1017, Energy: -81.221281+0.002313j
[2025-09-08 11:59:57] [Iter 928/2250] R2[477/600], Temp: 0.1002, Energy: -81.164327+0.000726j
[2025-09-08 12:00:42] [Iter 929/2250] R2[478/600], Temp: 0.0986, Energy: -81.265492-0.000330j
[2025-09-08 12:01:28] [Iter 930/2250] R2[479/600], Temp: 0.0970, Energy: -81.258574+0.001399j
[2025-09-08 12:02:13] [Iter 931/2250] R2[480/600], Temp: 0.0955, Energy: -81.190045-0.001286j
[2025-09-08 12:02:59] [Iter 932/2250] R2[481/600], Temp: 0.0940, Energy: -81.247193+0.001581j
[2025-09-08 12:03:45] [Iter 933/2250] R2[482/600], Temp: 0.0924, Energy: -81.249279-0.000290j
[2025-09-08 12:04:30] [Iter 934/2250] R2[483/600], Temp: 0.0909, Energy: -81.186996-0.001790j
[2025-09-08 12:05:16] [Iter 935/2250] R2[484/600], Temp: 0.0894, Energy: -81.114882+0.000075j
[2025-09-08 12:06:01] [Iter 936/2250] R2[485/600], Temp: 0.0879, Energy: -81.151014+0.001464j
[2025-09-08 12:06:47] [Iter 937/2250] R2[486/600], Temp: 0.0865, Energy: -81.167944+0.001942j
[2025-09-08 12:07:32] [Iter 938/2250] R2[487/600], Temp: 0.0850, Energy: -81.200416-0.000303j
[2025-09-08 12:08:18] [Iter 939/2250] R2[488/600], Temp: 0.0835, Energy: -81.119669-0.002927j
[2025-09-08 12:09:03] [Iter 940/2250] R2[489/600], Temp: 0.0821, Energy: -81.155317-0.000428j
[2025-09-08 12:09:49] [Iter 941/2250] R2[490/600], Temp: 0.0807, Energy: -81.159345-0.003223j
[2025-09-08 12:10:34] [Iter 942/2250] R2[491/600], Temp: 0.0792, Energy: -81.142505-0.003530j
[2025-09-08 12:11:20] [Iter 943/2250] R2[492/600], Temp: 0.0778, Energy: -81.105414+0.000528j
[2025-09-08 12:12:05] [Iter 944/2250] R2[493/600], Temp: 0.0764, Energy: -81.094724-0.001966j
[2025-09-08 12:12:51] [Iter 945/2250] R2[494/600], Temp: 0.0751, Energy: -81.193613+0.001460j
[2025-09-08 12:13:36] [Iter 946/2250] R2[495/600], Temp: 0.0737, Energy: -81.090345-0.003159j
[2025-09-08 12:14:22] [Iter 947/2250] R2[496/600], Temp: 0.0723, Energy: -81.139292+0.000917j
[2025-09-08 12:15:07] [Iter 948/2250] R2[497/600], Temp: 0.0710, Energy: -81.144046-0.002138j
[2025-09-08 12:15:53] [Iter 949/2250] R2[498/600], Temp: 0.0696, Energy: -81.162946-0.003164j
[2025-09-08 12:16:38] [Iter 950/2250] R2[499/600], Temp: 0.0683, Energy: -81.113918-0.000234j
[2025-09-08 12:17:24] [Iter 951/2250] R2[500/600], Temp: 0.0670, Energy: -81.153113+0.000885j
[2025-09-08 12:18:09] [Iter 952/2250] R2[501/600], Temp: 0.0657, Energy: -81.102715+0.003031j
[2025-09-08 12:18:55] [Iter 953/2250] R2[502/600], Temp: 0.0644, Energy: -81.054724+0.000003j
[2025-09-08 12:19:40] [Iter 954/2250] R2[503/600], Temp: 0.0631, Energy: -81.099840+0.000224j
[2025-09-08 12:20:26] [Iter 955/2250] R2[504/600], Temp: 0.0618, Energy: -81.046505+0.001270j
[2025-09-08 12:21:11] [Iter 956/2250] R2[505/600], Temp: 0.0606, Energy: -81.098752-0.001758j
[2025-09-08 12:21:57] [Iter 957/2250] R2[506/600], Temp: 0.0593, Energy: -81.119639-0.000194j
[2025-09-08 12:22:42] [Iter 958/2250] R2[507/600], Temp: 0.0581, Energy: -81.117191+0.004282j
[2025-09-08 12:23:28] [Iter 959/2250] R2[508/600], Temp: 0.0569, Energy: -81.155029+0.002299j
[2025-09-08 12:24:13] [Iter 960/2250] R2[509/600], Temp: 0.0557, Energy: -81.154245+0.000142j
[2025-09-08 12:24:59] [Iter 961/2250] R2[510/600], Temp: 0.0545, Energy: -81.143858-0.002798j
[2025-09-08 12:25:44] [Iter 962/2250] R2[511/600], Temp: 0.0533, Energy: -81.186343+0.001220j
[2025-09-08 12:26:30] [Iter 963/2250] R2[512/600], Temp: 0.0521, Energy: -81.190619-0.003392j
[2025-09-08 12:27:15] [Iter 964/2250] R2[513/600], Temp: 0.0510, Energy: -81.113537-0.002353j
[2025-09-08 12:28:01] [Iter 965/2250] R2[514/600], Temp: 0.0498, Energy: -81.133540-0.003809j
[2025-09-08 12:28:47] [Iter 966/2250] R2[515/600], Temp: 0.0487, Energy: -81.143882-0.003217j
[2025-09-08 12:29:32] [Iter 967/2250] R2[516/600], Temp: 0.0476, Energy: -81.107972+0.003213j
[2025-09-08 12:30:18] [Iter 968/2250] R2[517/600], Temp: 0.0465, Energy: -81.157843-0.002774j
[2025-09-08 12:31:03] [Iter 969/2250] R2[518/600], Temp: 0.0454, Energy: -81.134407-0.003698j
[2025-09-08 12:31:48] [Iter 970/2250] R2[519/600], Temp: 0.0443, Energy: -81.161414-0.003668j
[2025-09-08 12:32:34] [Iter 971/2250] R2[520/600], Temp: 0.0432, Energy: -81.201195+0.002359j
[2025-09-08 12:33:20] [Iter 972/2250] R2[521/600], Temp: 0.0422, Energy: -81.161317-0.001953j
[2025-09-08 12:34:05] [Iter 973/2250] R2[522/600], Temp: 0.0411, Energy: -81.175456-0.002489j
[2025-09-08 12:34:50] [Iter 974/2250] R2[523/600], Temp: 0.0401, Energy: -81.150298-0.002291j
[2025-09-08 12:35:36] [Iter 975/2250] R2[524/600], Temp: 0.0391, Energy: -81.149429+0.003184j
[2025-09-08 12:36:21] [Iter 976/2250] R2[525/600], Temp: 0.0381, Energy: -81.065459-0.000911j
[2025-09-08 12:37:07] [Iter 977/2250] R2[526/600], Temp: 0.0371, Energy: -81.120468-0.001034j
[2025-09-08 12:37:52] [Iter 978/2250] R2[527/600], Temp: 0.0361, Energy: -81.096843-0.001338j
[2025-09-08 12:38:38] [Iter 979/2250] R2[528/600], Temp: 0.0351, Energy: -81.079667-0.000073j
[2025-09-08 12:39:23] [Iter 980/2250] R2[529/600], Temp: 0.0342, Energy: -81.096817+0.002522j
[2025-09-08 12:40:09] [Iter 981/2250] R2[530/600], Temp: 0.0332, Energy: -81.096600+0.002166j
[2025-09-08 12:40:54] [Iter 982/2250] R2[531/600], Temp: 0.0323, Energy: -81.066650-0.002276j
[2025-09-08 12:41:40] [Iter 983/2250] R2[532/600], Temp: 0.0314, Energy: -81.153720+0.001639j
[2025-09-08 12:42:26] [Iter 984/2250] R2[533/600], Temp: 0.0305, Energy: -81.168223-0.004946j
[2025-09-08 12:43:11] [Iter 985/2250] R2[534/600], Temp: 0.0296, Energy: -81.108843-0.001312j
[2025-09-08 12:43:57] [Iter 986/2250] R2[535/600], Temp: 0.0287, Energy: -81.102246-0.002292j
[2025-09-08 12:44:42] [Iter 987/2250] R2[536/600], Temp: 0.0278, Energy: -81.110112+0.000705j
[2025-09-08 12:45:28] [Iter 988/2250] R2[537/600], Temp: 0.0270, Energy: -81.082224-0.001653j
[2025-09-08 12:46:13] [Iter 989/2250] R2[538/600], Temp: 0.0261, Energy: -81.104725-0.007268j
[2025-09-08 12:46:58] [Iter 990/2250] R2[539/600], Temp: 0.0253, Energy: -81.035430-0.003046j
[2025-09-08 12:47:44] [Iter 991/2250] R2[540/600], Temp: 0.0245, Energy: -81.073268-0.005439j
[2025-09-08 12:48:30] [Iter 992/2250] R2[541/600], Temp: 0.0237, Energy: -81.047085+0.000342j
[2025-09-08 12:49:15] [Iter 993/2250] R2[542/600], Temp: 0.0229, Energy: -81.031626+0.000676j
[2025-09-08 12:50:01] [Iter 994/2250] R2[543/600], Temp: 0.0221, Energy: -81.056673-0.002311j
[2025-09-08 12:50:46] [Iter 995/2250] R2[544/600], Temp: 0.0213, Energy: -81.082490-0.002005j
[2025-09-08 12:51:32] [Iter 996/2250] R2[545/600], Temp: 0.0206, Energy: -81.122815-0.001315j
[2025-09-08 12:52:17] [Iter 997/2250] R2[546/600], Temp: 0.0199, Energy: -81.147725-0.004075j
[2025-09-08 12:53:03] [Iter 998/2250] R2[547/600], Temp: 0.0191, Energy: -81.241745-0.001803j
[2025-09-08 12:53:48] [Iter 999/2250] R2[548/600], Temp: 0.0184, Energy: -81.170790+0.001031j
[2025-09-08 12:54:34] [Iter 1000/2250] R2[549/600], Temp: 0.0177, Energy: -81.194733+0.003953j
[2025-09-08 12:54:34] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-09-08 12:55:19] [Iter 1001/2250] R2[550/600], Temp: 0.0170, Energy: -81.193697+0.003172j
[2025-09-08 12:56:05] [Iter 1002/2250] R2[551/600], Temp: 0.0164, Energy: -81.119395-0.003792j
[2025-09-08 12:56:50] [Iter 1003/2250] R2[552/600], Temp: 0.0157, Energy: -81.066774+0.001364j
[2025-09-08 12:57:36] [Iter 1004/2250] R2[553/600], Temp: 0.0151, Energy: -81.139665-0.000014j
[2025-09-08 12:58:21] [Iter 1005/2250] R2[554/600], Temp: 0.0144, Energy: -81.144879-0.004709j
[2025-09-08 12:59:07] [Iter 1006/2250] R2[555/600], Temp: 0.0138, Energy: -81.096174-0.004236j
[2025-09-08 12:59:52] [Iter 1007/2250] R2[556/600], Temp: 0.0132, Energy: -81.066398-0.000064j
[2025-09-08 13:00:38] [Iter 1008/2250] R2[557/600], Temp: 0.0126, Energy: -81.127897-0.000638j
[2025-09-08 13:01:23] [Iter 1009/2250] R2[558/600], Temp: 0.0120, Energy: -81.097811+0.000674j
[2025-09-08 13:02:09] [Iter 1010/2250] R2[559/600], Temp: 0.0115, Energy: -81.138150-0.002674j
[2025-09-08 13:02:55] [Iter 1011/2250] R2[560/600], Temp: 0.0109, Energy: -81.122806-0.002378j
[2025-09-08 13:03:40] [Iter 1012/2250] R2[561/600], Temp: 0.0104, Energy: -81.043890+0.000718j
[2025-09-08 13:04:25] [Iter 1013/2250] R2[562/600], Temp: 0.0099, Energy: -81.056728-0.001724j
[2025-09-08 13:05:11] [Iter 1014/2250] R2[563/600], Temp: 0.0094, Energy: -81.036812-0.000346j
[2025-09-08 13:05:56] [Iter 1015/2250] R2[564/600], Temp: 0.0089, Energy: -81.008791+0.002044j
[2025-09-08 13:06:42] [Iter 1016/2250] R2[565/600], Temp: 0.0084, Energy: -81.127073+0.001581j
[2025-09-08 13:07:27] [Iter 1017/2250] R2[566/600], Temp: 0.0079, Energy: -81.064874+0.001309j
[2025-09-08 13:08:13] [Iter 1018/2250] R2[567/600], Temp: 0.0074, Energy: -81.100306-0.002004j
[2025-09-08 13:08:58] [Iter 1019/2250] R2[568/600], Temp: 0.0070, Energy: -81.080392-0.002308j
[2025-09-08 13:09:44] [Iter 1020/2250] R2[569/600], Temp: 0.0066, Energy: -81.116630-0.002590j
[2025-09-08 13:10:29] [Iter 1021/2250] R2[570/600], Temp: 0.0062, Energy: -81.139654+0.000504j
[2025-09-08 13:11:15] [Iter 1022/2250] R2[571/600], Temp: 0.0058, Energy: -81.127712-0.000586j
[2025-09-08 13:12:00] [Iter 1023/2250] R2[572/600], Temp: 0.0054, Energy: -81.147856+0.002107j
[2025-09-08 13:12:46] [Iter 1024/2250] R2[573/600], Temp: 0.0050, Energy: -81.144343-0.000446j
[2025-09-08 13:13:31] [Iter 1025/2250] R2[574/600], Temp: 0.0046, Energy: -81.153387+0.000310j
[2025-09-08 13:14:17] [Iter 1026/2250] R2[575/600], Temp: 0.0043, Energy: -81.158166-0.002046j
[2025-09-08 13:15:02] [Iter 1027/2250] R2[576/600], Temp: 0.0039, Energy: -81.217520+0.003810j
[2025-09-08 13:15:48] [Iter 1028/2250] R2[577/600], Temp: 0.0036, Energy: -81.244633-0.000469j
[2025-09-08 13:16:33] [Iter 1029/2250] R2[578/600], Temp: 0.0033, Energy: -81.165911+0.003014j
[2025-09-08 13:17:19] [Iter 1030/2250] R2[579/600], Temp: 0.0030, Energy: -81.182626+0.000567j
[2025-09-08 13:18:04] [Iter 1031/2250] R2[580/600], Temp: 0.0027, Energy: -81.267982+0.002746j
[2025-09-08 13:18:50] [Iter 1032/2250] R2[581/600], Temp: 0.0025, Energy: -81.160534-0.000504j
[2025-09-08 13:19:35] [Iter 1033/2250] R2[582/600], Temp: 0.0022, Energy: -81.119674-0.000278j
[2025-09-08 13:20:20] [Iter 1034/2250] R2[583/600], Temp: 0.0020, Energy: -81.123886+0.000038j
[2025-09-08 13:21:06] [Iter 1035/2250] R2[584/600], Temp: 0.0018, Energy: -81.082386+0.004329j
[2025-09-08 13:21:51] [Iter 1036/2250] R2[585/600], Temp: 0.0015, Energy: -81.057591+0.001153j
[2025-09-08 13:22:37] [Iter 1037/2250] R2[586/600], Temp: 0.0013, Energy: -81.017794+0.002103j
[2025-09-08 13:23:23] [Iter 1038/2250] R2[587/600], Temp: 0.0012, Energy: -81.100444+0.001130j
[2025-09-08 13:24:08] [Iter 1039/2250] R2[588/600], Temp: 0.0010, Energy: -81.118199+0.002055j
[2025-09-08 13:24:53] [Iter 1040/2250] R2[589/600], Temp: 0.0008, Energy: -81.103392+0.003090j
[2025-09-08 13:25:39] [Iter 1041/2250] R2[590/600], Temp: 0.0007, Energy: -81.163669+0.000130j
[2025-09-08 13:26:24] [Iter 1042/2250] R2[591/600], Temp: 0.0006, Energy: -81.145554+0.002885j
[2025-09-08 13:27:10] [Iter 1043/2250] R2[592/600], Temp: 0.0004, Energy: -81.151620-0.000962j
[2025-09-08 13:27:56] [Iter 1044/2250] R2[593/600], Temp: 0.0003, Energy: -81.111045-0.002538j
[2025-09-08 13:28:41] [Iter 1045/2250] R2[594/600], Temp: 0.0002, Energy: -81.181486+0.000615j
[2025-09-08 13:29:26] [Iter 1046/2250] R2[595/600], Temp: 0.0002, Energy: -81.155672+0.000197j
[2025-09-08 13:30:12] [Iter 1047/2250] R2[596/600], Temp: 0.0001, Energy: -81.059500-0.000151j
[2025-09-08 13:30:57] [Iter 1048/2250] R2[597/600], Temp: 0.0001, Energy: -81.186675-0.000941j
[2025-09-08 13:31:43] [Iter 1049/2250] R2[598/600], Temp: 0.0000, Energy: -81.220090-0.001583j
[2025-09-08 13:32:29] [Iter 1050/2250] R2[599/600], Temp: 0.0000, Energy: -81.224903-0.001028j
[2025-09-08 13:32:29] RESTART #3 | Period: 1200
[2025-09-08 13:33:14] [Iter 1051/2250] R3[0/1200], Temp: 1.0000, Energy: -81.236399-0.003018j
[2025-09-08 13:34:00] [Iter 1052/2250] R3[1/1200], Temp: 1.0000, Energy: -81.146370-0.003264j
[2025-09-08 13:34:45] [Iter 1053/2250] R3[2/1200], Temp: 1.0000, Energy: -81.112286+0.003437j
[2025-09-08 13:35:31] [Iter 1054/2250] R3[3/1200], Temp: 1.0000, Energy: -81.167432-0.000951j
[2025-09-08 13:36:16] [Iter 1055/2250] R3[4/1200], Temp: 1.0000, Energy: -81.155920-0.005956j
[2025-09-08 13:37:02] [Iter 1056/2250] R3[5/1200], Temp: 1.0000, Energy: -81.192643-0.001040j
[2025-09-08 13:37:47] [Iter 1057/2250] R3[6/1200], Temp: 0.9999, Energy: -81.087089-0.000072j
[2025-09-08 13:38:33] [Iter 1058/2250] R3[7/1200], Temp: 0.9999, Energy: -81.140003+0.005551j
[2025-09-08 13:39:18] [Iter 1059/2250] R3[8/1200], Temp: 0.9999, Energy: -81.210340+0.000477j
[2025-09-08 13:40:04] [Iter 1060/2250] R3[9/1200], Temp: 0.9999, Energy: -81.166830-0.001677j
[2025-09-08 13:40:49] [Iter 1061/2250] R3[10/1200], Temp: 0.9998, Energy: -81.139716+0.001687j
[2025-09-08 13:41:35] [Iter 1062/2250] R3[11/1200], Temp: 0.9998, Energy: -81.179507+0.001224j
[2025-09-08 13:42:20] [Iter 1063/2250] R3[12/1200], Temp: 0.9998, Energy: -81.138789+0.000923j
[2025-09-08 13:43:06] [Iter 1064/2250] R3[13/1200], Temp: 0.9997, Energy: -81.119133+0.000799j
[2025-09-08 13:43:51] [Iter 1065/2250] R3[14/1200], Temp: 0.9997, Energy: -81.141630+0.002544j
[2025-09-08 13:44:37] [Iter 1066/2250] R3[15/1200], Temp: 0.9996, Energy: -81.217011+0.000065j
[2025-09-08 13:45:22] [Iter 1067/2250] R3[16/1200], Temp: 0.9996, Energy: -81.193532+0.002229j
[2025-09-08 13:46:08] [Iter 1068/2250] R3[17/1200], Temp: 0.9995, Energy: -81.210090+0.001354j
[2025-09-08 13:46:53] [Iter 1069/2250] R3[18/1200], Temp: 0.9994, Energy: -81.195001-0.000413j
[2025-09-08 13:47:39] [Iter 1070/2250] R3[19/1200], Temp: 0.9994, Energy: -81.144196-0.001639j
[2025-09-08 13:48:24] [Iter 1071/2250] R3[20/1200], Temp: 0.9993, Energy: -81.160969-0.001176j
[2025-09-08 13:49:10] [Iter 1072/2250] R3[21/1200], Temp: 0.9992, Energy: -81.187998-0.001018j
[2025-09-08 13:49:55] [Iter 1073/2250] R3[22/1200], Temp: 0.9992, Energy: -81.165728+0.000382j
[2025-09-08 13:50:41] [Iter 1074/2250] R3[23/1200], Temp: 0.9991, Energy: -81.096136-0.005456j
[2025-09-08 13:51:26] [Iter 1075/2250] R3[24/1200], Temp: 0.9990, Energy: -81.093386+0.002905j
[2025-09-08 13:52:12] [Iter 1076/2250] R3[25/1200], Temp: 0.9989, Energy: -81.169576+0.002831j
[2025-09-08 13:52:58] [Iter 1077/2250] R3[26/1200], Temp: 0.9988, Energy: -81.140165+0.000350j
[2025-09-08 13:53:43] [Iter 1078/2250] R3[27/1200], Temp: 0.9988, Energy: -81.151535+0.001907j
[2025-09-08 13:54:28] [Iter 1079/2250] R3[28/1200], Temp: 0.9987, Energy: -81.233244+0.000268j
[2025-09-08 13:55:14] [Iter 1080/2250] R3[29/1200], Temp: 0.9986, Energy: -81.177494-0.000187j
[2025-09-08 13:55:59] [Iter 1081/2250] R3[30/1200], Temp: 0.9985, Energy: -81.156292-0.001277j
[2025-09-08 13:56:45] [Iter 1082/2250] R3[31/1200], Temp: 0.9984, Energy: -81.225496-0.002774j
[2025-09-08 13:57:30] [Iter 1083/2250] R3[32/1200], Temp: 0.9982, Energy: -81.155221+0.005140j
[2025-09-08 13:58:16] [Iter 1084/2250] R3[33/1200], Temp: 0.9981, Energy: -81.227610+0.001745j
[2025-09-08 13:59:02] [Iter 1085/2250] R3[34/1200], Temp: 0.9980, Energy: -81.261182-0.000857j
[2025-09-08 13:59:47] [Iter 1086/2250] R3[35/1200], Temp: 0.9979, Energy: -81.193333+0.000841j
[2025-09-08 14:00:33] [Iter 1087/2250] R3[36/1200], Temp: 0.9978, Energy: -81.291563-0.003279j
[2025-09-08 14:01:18] [Iter 1088/2250] R3[37/1200], Temp: 0.9977, Energy: -81.259414+0.001418j
[2025-09-08 14:02:04] [Iter 1089/2250] R3[38/1200], Temp: 0.9975, Energy: -81.214328-0.000168j
[2025-09-08 14:02:49] [Iter 1090/2250] R3[39/1200], Temp: 0.9974, Energy: -81.210978-0.003520j
[2025-09-08 14:03:35] [Iter 1091/2250] R3[40/1200], Temp: 0.9973, Energy: -81.245999-0.001506j
[2025-09-08 14:04:20] [Iter 1092/2250] R3[41/1200], Temp: 0.9971, Energy: -81.199304-0.000287j
[2025-09-08 14:05:06] [Iter 1093/2250] R3[42/1200], Temp: 0.9970, Energy: -81.277374-0.002139j
[2025-09-08 14:05:51] [Iter 1094/2250] R3[43/1200], Temp: 0.9968, Energy: -81.188532+0.001699j
[2025-09-08 14:06:37] [Iter 1095/2250] R3[44/1200], Temp: 0.9967, Energy: -81.143282+0.003129j
[2025-09-08 14:07:22] [Iter 1096/2250] R3[45/1200], Temp: 0.9965, Energy: -81.172678+0.002836j
[2025-09-08 14:08:08] [Iter 1097/2250] R3[46/1200], Temp: 0.9964, Energy: -81.159529+0.001519j
[2025-09-08 14:08:53] [Iter 1098/2250] R3[47/1200], Temp: 0.9962, Energy: -81.193947-0.000073j
[2025-09-08 14:09:39] [Iter 1099/2250] R3[48/1200], Temp: 0.9961, Energy: -81.191863+0.002177j
[2025-09-08 14:10:25] [Iter 1100/2250] R3[49/1200], Temp: 0.9959, Energy: -81.206216-0.000064j
[2025-09-08 14:11:10] [Iter 1101/2250] R3[50/1200], Temp: 0.9957, Energy: -81.194520-0.000725j
[2025-09-08 14:11:56] [Iter 1102/2250] R3[51/1200], Temp: 0.9955, Energy: -81.232786+0.001261j
[2025-09-08 14:12:41] [Iter 1103/2250] R3[52/1200], Temp: 0.9954, Energy: -81.270231+0.001254j
[2025-09-08 14:13:27] [Iter 1104/2250] R3[53/1200], Temp: 0.9952, Energy: -81.204011-0.003665j
[2025-09-08 14:14:12] [Iter 1105/2250] R3[54/1200], Temp: 0.9950, Energy: -81.164077-0.000410j
[2025-09-08 14:14:57] [Iter 1106/2250] R3[55/1200], Temp: 0.9948, Energy: -81.137407-0.001110j
[2025-09-08 14:15:43] [Iter 1107/2250] R3[56/1200], Temp: 0.9946, Energy: -81.215414-0.000485j
[2025-09-08 14:16:28] [Iter 1108/2250] R3[57/1200], Temp: 0.9944, Energy: -81.218295+0.000922j
[2025-09-08 14:17:14] [Iter 1109/2250] R3[58/1200], Temp: 0.9942, Energy: -81.171004-0.001875j
[2025-09-08 14:17:59] [Iter 1110/2250] R3[59/1200], Temp: 0.9940, Energy: -81.233450+0.002076j
[2025-09-08 14:18:45] [Iter 1111/2250] R3[60/1200], Temp: 0.9938, Energy: -81.167592-0.000763j
[2025-09-08 14:19:30] [Iter 1112/2250] R3[61/1200], Temp: 0.9936, Energy: -81.182961+0.001924j
[2025-09-08 14:20:16] [Iter 1113/2250] R3[62/1200], Temp: 0.9934, Energy: -81.227618-0.004917j
[2025-09-08 14:21:01] [Iter 1114/2250] R3[63/1200], Temp: 0.9932, Energy: -81.136564+0.001316j
[2025-09-08 14:21:47] [Iter 1115/2250] R3[64/1200], Temp: 0.9930, Energy: -81.202702-0.001851j
[2025-09-08 14:22:32] [Iter 1116/2250] R3[65/1200], Temp: 0.9928, Energy: -81.212068+0.001351j
[2025-09-08 14:23:18] [Iter 1117/2250] R3[66/1200], Temp: 0.9926, Energy: -81.224969+0.000944j
[2025-09-08 14:24:03] [Iter 1118/2250] R3[67/1200], Temp: 0.9923, Energy: -81.172203+0.003944j
[2025-09-08 14:24:49] [Iter 1119/2250] R3[68/1200], Temp: 0.9921, Energy: -81.150952-0.002823j
[2025-09-08 14:25:34] [Iter 1120/2250] R3[69/1200], Temp: 0.9919, Energy: -81.158493+0.003372j
[2025-09-08 14:26:20] [Iter 1121/2250] R3[70/1200], Temp: 0.9916, Energy: -81.135004+0.000501j
[2025-09-08 14:27:05] [Iter 1122/2250] R3[71/1200], Temp: 0.9914, Energy: -81.136929-0.002562j
[2025-09-08 14:27:51] [Iter 1123/2250] R3[72/1200], Temp: 0.9911, Energy: -81.157516-0.003644j
[2025-09-08 14:28:37] [Iter 1124/2250] R3[73/1200], Temp: 0.9909, Energy: -81.166008-0.000734j
[2025-09-08 14:29:22] [Iter 1125/2250] R3[74/1200], Temp: 0.9906, Energy: -81.144526+0.002430j
[2025-09-08 14:30:08] [Iter 1126/2250] R3[75/1200], Temp: 0.9904, Energy: -81.249942-0.000693j
[2025-09-08 14:30:53] [Iter 1127/2250] R3[76/1200], Temp: 0.9901, Energy: -81.186744+0.000399j
[2025-09-08 14:31:39] [Iter 1128/2250] R3[77/1200], Temp: 0.9899, Energy: -81.151171+0.001144j
[2025-09-08 14:32:24] [Iter 1129/2250] R3[78/1200], Temp: 0.9896, Energy: -81.166673-0.000111j
[2025-09-08 14:33:10] [Iter 1130/2250] R3[79/1200], Temp: 0.9893, Energy: -81.221437+0.000782j
[2025-09-08 14:33:55] [Iter 1131/2250] R3[80/1200], Temp: 0.9891, Energy: -81.168539-0.002314j
[2025-09-08 14:34:41] [Iter 1132/2250] R3[81/1200], Temp: 0.9888, Energy: -81.133218+0.001417j
[2025-09-08 14:35:27] [Iter 1133/2250] R3[82/1200], Temp: 0.9885, Energy: -81.218553+0.000086j
[2025-09-08 14:36:12] [Iter 1134/2250] R3[83/1200], Temp: 0.9882, Energy: -81.260396+0.002748j
[2025-09-08 14:36:58] [Iter 1135/2250] R3[84/1200], Temp: 0.9880, Energy: -81.190765-0.003568j
[2025-09-08 14:37:43] [Iter 1136/2250] R3[85/1200], Temp: 0.9877, Energy: -81.169342+0.003184j
[2025-09-08 14:38:29] [Iter 1137/2250] R3[86/1200], Temp: 0.9874, Energy: -81.133369+0.000820j
[2025-09-08 14:39:14] [Iter 1138/2250] R3[87/1200], Temp: 0.9871, Energy: -81.233475-0.001072j
[2025-09-08 14:40:00] [Iter 1139/2250] R3[88/1200], Temp: 0.9868, Energy: -81.172699-0.006948j
[2025-09-08 14:40:45] [Iter 1140/2250] R3[89/1200], Temp: 0.9865, Energy: -81.141470-0.004256j
[2025-09-08 14:41:31] [Iter 1141/2250] R3[90/1200], Temp: 0.9862, Energy: -81.114731+0.002390j
[2025-09-08 14:42:17] [Iter 1142/2250] R3[91/1200], Temp: 0.9859, Energy: -81.098607-0.001834j
[2025-09-08 14:43:02] [Iter 1143/2250] R3[92/1200], Temp: 0.9856, Energy: -81.109805+0.004969j
[2025-09-08 14:43:48] [Iter 1144/2250] R3[93/1200], Temp: 0.9853, Energy: -81.167977-0.001807j
[2025-09-08 14:44:33] [Iter 1145/2250] R3[94/1200], Temp: 0.9849, Energy: -81.136618+0.000626j
[2025-09-08 14:45:19] [Iter 1146/2250] R3[95/1200], Temp: 0.9846, Energy: -81.179638-0.001572j
[2025-09-08 14:46:04] [Iter 1147/2250] R3[96/1200], Temp: 0.9843, Energy: -81.221861-0.002368j
[2025-09-08 14:46:50] [Iter 1148/2250] R3[97/1200], Temp: 0.9840, Energy: -81.224548+0.000179j
[2025-09-08 14:47:35] [Iter 1149/2250] R3[98/1200], Temp: 0.9836, Energy: -81.241765-0.004130j
[2025-09-08 14:48:21] [Iter 1150/2250] R3[99/1200], Temp: 0.9833, Energy: -81.207675+0.000973j
[2025-09-08 14:49:07] [Iter 1151/2250] R3[100/1200], Temp: 0.9830, Energy: -81.152043-0.001296j
[2025-09-08 14:49:52] [Iter 1152/2250] R3[101/1200], Temp: 0.9826, Energy: -81.146725+0.003490j
[2025-09-08 14:50:39] [Iter 1153/2250] R3[102/1200], Temp: 0.9823, Energy: -81.145579+0.003380j
[2025-09-08 14:51:25] [Iter 1154/2250] R3[103/1200], Temp: 0.9819, Energy: -81.143713+0.000300j
[2025-09-08 14:52:11] [Iter 1155/2250] R3[104/1200], Temp: 0.9816, Energy: -81.175312-0.000969j
[2025-09-08 14:52:57] [Iter 1156/2250] R3[105/1200], Temp: 0.9812, Energy: -81.219799-0.000405j
[2025-09-08 14:53:42] [Iter 1157/2250] R3[106/1200], Temp: 0.9809, Energy: -81.235090-0.006089j
[2025-09-08 14:54:28] [Iter 1158/2250] R3[107/1200], Temp: 0.9805, Energy: -81.236003+0.001401j
[2025-09-08 14:55:13] [Iter 1159/2250] R3[108/1200], Temp: 0.9801, Energy: -81.244972+0.001361j
[2025-09-08 14:55:59] [Iter 1160/2250] R3[109/1200], Temp: 0.9798, Energy: -81.182310-0.000631j
[2025-09-08 14:56:44] [Iter 1161/2250] R3[110/1200], Temp: 0.9794, Energy: -81.190287-0.000981j
[2025-09-08 14:57:30] [Iter 1162/2250] R3[111/1200], Temp: 0.9790, Energy: -81.182150-0.002020j
[2025-09-08 14:58:15] [Iter 1163/2250] R3[112/1200], Temp: 0.9787, Energy: -81.147857+0.002689j
[2025-09-08 14:59:01] [Iter 1164/2250] R3[113/1200], Temp: 0.9783, Energy: -81.229860-0.000397j
[2025-09-08 14:59:46] [Iter 1165/2250] R3[114/1200], Temp: 0.9779, Energy: -81.202899+0.002559j
[2025-09-08 15:00:32] [Iter 1166/2250] R3[115/1200], Temp: 0.9775, Energy: -81.190133-0.002833j
[2025-09-08 15:01:17] [Iter 1167/2250] R3[116/1200], Temp: 0.9771, Energy: -81.177857-0.001662j
[2025-09-08 15:02:03] [Iter 1168/2250] R3[117/1200], Temp: 0.9767, Energy: -81.182763+0.001196j
[2025-09-08 15:02:48] [Iter 1169/2250] R3[118/1200], Temp: 0.9763, Energy: -81.193916+0.001231j
[2025-09-08 15:03:34] [Iter 1170/2250] R3[119/1200], Temp: 0.9759, Energy: -81.204067-0.000368j
[2025-09-08 15:04:20] [Iter 1171/2250] R3[120/1200], Temp: 0.9755, Energy: -81.248296+0.000727j
[2025-09-08 15:05:05] [Iter 1172/2250] R3[121/1200], Temp: 0.9751, Energy: -81.202089+0.002677j
[2025-09-08 15:05:51] [Iter 1173/2250] R3[122/1200], Temp: 0.9747, Energy: -81.173623-0.002862j
[2025-09-08 15:06:36] [Iter 1174/2250] R3[123/1200], Temp: 0.9743, Energy: -81.216927+0.003446j
[2025-09-08 15:07:22] [Iter 1175/2250] R3[124/1200], Temp: 0.9739, Energy: -81.177481-0.001019j
[2025-09-08 15:08:07] [Iter 1176/2250] R3[125/1200], Temp: 0.9735, Energy: -81.175444+0.001460j
[2025-09-08 15:08:53] [Iter 1177/2250] R3[126/1200], Temp: 0.9730, Energy: -81.189282-0.001060j
[2025-09-08 15:09:38] [Iter 1178/2250] R3[127/1200], Temp: 0.9726, Energy: -81.290270+0.001712j
[2025-09-08 15:10:23] [Iter 1179/2250] R3[128/1200], Temp: 0.9722, Energy: -81.180408+0.000957j
[2025-09-08 15:11:09] [Iter 1180/2250] R3[129/1200], Temp: 0.9718, Energy: -81.154593+0.003146j
[2025-09-08 15:11:54] [Iter 1181/2250] R3[130/1200], Temp: 0.9713, Energy: -81.216982-0.000162j
[2025-09-08 15:12:40] [Iter 1182/2250] R3[131/1200], Temp: 0.9709, Energy: -81.180070+0.001222j
[2025-09-08 15:13:25] [Iter 1183/2250] R3[132/1200], Temp: 0.9704, Energy: -81.175374-0.001194j
[2025-09-08 15:14:11] [Iter 1184/2250] R3[133/1200], Temp: 0.9700, Energy: -81.222477+0.001641j
[2025-09-08 15:14:56] [Iter 1185/2250] R3[134/1200], Temp: 0.9695, Energy: -81.204062+0.002371j
[2025-09-08 15:15:42] [Iter 1186/2250] R3[135/1200], Temp: 0.9691, Energy: -81.179344-0.003043j
[2025-09-08 15:16:27] [Iter 1187/2250] R3[136/1200], Temp: 0.9686, Energy: -81.198777+0.000533j
[2025-09-08 15:17:13] [Iter 1188/2250] R3[137/1200], Temp: 0.9682, Energy: -81.256510-0.001517j
[2025-09-08 15:17:59] [Iter 1189/2250] R3[138/1200], Temp: 0.9677, Energy: -81.203467-0.001181j
[2025-09-08 15:18:44] [Iter 1190/2250] R3[139/1200], Temp: 0.9673, Energy: -81.252237+0.001260j
[2025-09-08 15:19:30] [Iter 1191/2250] R3[140/1200], Temp: 0.9668, Energy: -81.286701-0.001147j
[2025-09-08 15:20:15] [Iter 1192/2250] R3[141/1200], Temp: 0.9663, Energy: -81.215326+0.001277j
[2025-09-08 15:21:01] [Iter 1193/2250] R3[142/1200], Temp: 0.9658, Energy: -81.246291-0.000071j
[2025-09-08 15:21:46] [Iter 1194/2250] R3[143/1200], Temp: 0.9654, Energy: -81.158887-0.000658j
[2025-09-08 15:22:32] [Iter 1195/2250] R3[144/1200], Temp: 0.9649, Energy: -81.227959+0.000673j
[2025-09-08 15:23:17] [Iter 1196/2250] R3[145/1200], Temp: 0.9644, Energy: -81.199004+0.001100j
[2025-09-08 15:24:03] [Iter 1197/2250] R3[146/1200], Temp: 0.9639, Energy: -81.183992-0.000496j
[2025-09-08 15:24:48] [Iter 1198/2250] R3[147/1200], Temp: 0.9634, Energy: -81.185168+0.001938j
[2025-09-08 15:25:34] [Iter 1199/2250] R3[148/1200], Temp: 0.9629, Energy: -81.186230-0.000609j
[2025-09-08 15:26:19] [Iter 1200/2250] R3[149/1200], Temp: 0.9624, Energy: -81.200006+0.001688j
[2025-09-08 15:27:05] [Iter 1201/2250] R3[150/1200], Temp: 0.9619, Energy: -81.166500+0.000336j
[2025-09-08 15:27:50] [Iter 1202/2250] R3[151/1200], Temp: 0.9614, Energy: -81.206793+0.000029j
[2025-09-08 15:28:36] [Iter 1203/2250] R3[152/1200], Temp: 0.9609, Energy: -81.176178-0.002325j
[2025-09-08 15:29:22] [Iter 1204/2250] R3[153/1200], Temp: 0.9604, Energy: -81.176000-0.002045j
[2025-09-08 15:30:07] [Iter 1205/2250] R3[154/1200], Temp: 0.9599, Energy: -81.178432-0.003401j
[2025-09-08 15:30:53] [Iter 1206/2250] R3[155/1200], Temp: 0.9594, Energy: -81.178442-0.004282j
[2025-09-08 15:31:38] [Iter 1207/2250] R3[156/1200], Temp: 0.9589, Energy: -81.183568-0.002879j
[2025-09-08 15:32:24] [Iter 1208/2250] R3[157/1200], Temp: 0.9584, Energy: -81.204004-0.002772j
[2025-09-08 15:33:10] [Iter 1209/2250] R3[158/1200], Temp: 0.9578, Energy: -81.213567+0.006601j
[2025-09-08 15:33:55] [Iter 1210/2250] R3[159/1200], Temp: 0.9573, Energy: -81.184669+0.004792j
[2025-09-08 15:34:41] [Iter 1211/2250] R3[160/1200], Temp: 0.9568, Energy: -81.199152-0.000123j
[2025-09-08 15:35:26] [Iter 1212/2250] R3[161/1200], Temp: 0.9562, Energy: -81.148475-0.004560j
[2025-09-08 15:36:12] [Iter 1213/2250] R3[162/1200], Temp: 0.9557, Energy: -81.196040+0.001943j
[2025-09-08 15:36:57] [Iter 1214/2250] R3[163/1200], Temp: 0.9552, Energy: -81.143993+0.003561j
[2025-09-08 15:37:43] [Iter 1215/2250] R3[164/1200], Temp: 0.9546, Energy: -81.170612-0.000530j
[2025-09-08 15:38:28] [Iter 1216/2250] R3[165/1200], Temp: 0.9541, Energy: -81.169610+0.003419j
[2025-09-08 15:39:14] [Iter 1217/2250] R3[166/1200], Temp: 0.9535, Energy: -81.186512-0.000152j
[2025-09-08 15:39:59] [Iter 1218/2250] R3[167/1200], Temp: 0.9530, Energy: -81.217400+0.001306j
[2025-09-08 15:40:45] [Iter 1219/2250] R3[168/1200], Temp: 0.9524, Energy: -81.183994-0.004379j
[2025-09-08 15:41:30] [Iter 1220/2250] R3[169/1200], Temp: 0.9519, Energy: -81.223069+0.000830j
[2025-09-08 15:42:16] [Iter 1221/2250] R3[170/1200], Temp: 0.9513, Energy: -81.151548+0.000516j
[2025-09-08 15:43:01] [Iter 1222/2250] R3[171/1200], Temp: 0.9507, Energy: -81.230001-0.000293j
[2025-09-08 15:43:47] [Iter 1223/2250] R3[172/1200], Temp: 0.9502, Energy: -81.152885+0.001441j
[2025-09-08 15:44:32] [Iter 1224/2250] R3[173/1200], Temp: 0.9496, Energy: -81.191684+0.000489j
[2025-09-08 15:45:18] [Iter 1225/2250] R3[174/1200], Temp: 0.9490, Energy: -81.159904-0.001031j
[2025-09-08 15:46:03] [Iter 1226/2250] R3[175/1200], Temp: 0.9484, Energy: -81.244970-0.000061j
[2025-09-08 15:46:49] [Iter 1227/2250] R3[176/1200], Temp: 0.9479, Energy: -81.185618-0.001716j
[2025-09-08 15:47:35] [Iter 1228/2250] R3[177/1200], Temp: 0.9473, Energy: -81.125500-0.001065j
[2025-09-08 15:48:20] [Iter 1229/2250] R3[178/1200], Temp: 0.9467, Energy: -81.147152-0.002277j
[2025-09-08 15:49:06] [Iter 1230/2250] R3[179/1200], Temp: 0.9461, Energy: -81.128013-0.000483j
[2025-09-08 15:49:51] [Iter 1231/2250] R3[180/1200], Temp: 0.9455, Energy: -81.129387-0.004225j
[2025-09-08 15:50:37] [Iter 1232/2250] R3[181/1200], Temp: 0.9449, Energy: -81.162272-0.000298j
[2025-09-08 15:51:22] [Iter 1233/2250] R3[182/1200], Temp: 0.9443, Energy: -81.178078-0.002550j
[2025-09-08 15:52:08] [Iter 1234/2250] R3[183/1200], Temp: 0.9437, Energy: -81.275099-0.002015j
[2025-09-08 15:52:53] [Iter 1235/2250] R3[184/1200], Temp: 0.9431, Energy: -81.253995+0.001413j
[2025-09-08 15:53:39] [Iter 1236/2250] R3[185/1200], Temp: 0.9425, Energy: -81.208738+0.000300j
[2025-09-08 15:54:25] [Iter 1237/2250] R3[186/1200], Temp: 0.9419, Energy: -81.235079-0.001057j
[2025-09-08 15:55:10] [Iter 1238/2250] R3[187/1200], Temp: 0.9413, Energy: -81.207545+0.000527j
[2025-09-08 15:55:56] [Iter 1239/2250] R3[188/1200], Temp: 0.9407, Energy: -81.206996+0.000216j
[2025-09-08 15:56:41] [Iter 1240/2250] R3[189/1200], Temp: 0.9400, Energy: -81.179733+0.000566j
[2025-09-08 15:57:27] [Iter 1241/2250] R3[190/1200], Temp: 0.9394, Energy: -81.138013+0.000190j
[2025-09-08 15:58:12] [Iter 1242/2250] R3[191/1200], Temp: 0.9388, Energy: -81.171642-0.002094j
[2025-09-08 15:58:58] [Iter 1243/2250] R3[192/1200], Temp: 0.9382, Energy: -81.209233-0.000122j
[2025-09-08 15:59:43] [Iter 1244/2250] R3[193/1200], Temp: 0.9375, Energy: -81.130949+0.004005j
[2025-09-08 16:00:29] [Iter 1245/2250] R3[194/1200], Temp: 0.9369, Energy: -81.157587-0.001089j
[2025-09-08 16:01:14] [Iter 1246/2250] R3[195/1200], Temp: 0.9362, Energy: -81.190187-0.001411j
[2025-09-08 16:02:00] [Iter 1247/2250] R3[196/1200], Temp: 0.9356, Energy: -81.169835+0.000208j
[2025-09-08 16:02:46] [Iter 1248/2250] R3[197/1200], Temp: 0.9350, Energy: -81.154772+0.003563j
[2025-09-08 16:03:31] [Iter 1249/2250] R3[198/1200], Temp: 0.9343, Energy: -81.181157-0.000313j
[2025-09-08 16:04:17] [Iter 1250/2250] R3[199/1200], Temp: 0.9337, Energy: -81.162571+0.001758j
[2025-09-08 16:04:17] ✓ Checkpoint saved: checkpoint_iter_001250.pkl
[2025-09-08 16:05:02] [Iter 1251/2250] R3[200/1200], Temp: 0.9330, Energy: -81.172809-0.004345j
[2025-09-08 16:05:48] [Iter 1252/2250] R3[201/1200], Temp: 0.9324, Energy: -81.243911-0.001126j
[2025-09-08 16:06:33] [Iter 1253/2250] R3[202/1200], Temp: 0.9317, Energy: -81.191577+0.001287j
[2025-09-08 16:07:19] [Iter 1254/2250] R3[203/1200], Temp: 0.9310, Energy: -81.282455-0.002949j
[2025-09-08 16:08:04] [Iter 1255/2250] R3[204/1200], Temp: 0.9304, Energy: -81.229166-0.002414j
[2025-09-08 16:08:50] [Iter 1256/2250] R3[205/1200], Temp: 0.9297, Energy: -81.189780+0.004050j
[2025-09-08 16:09:36] [Iter 1257/2250] R3[206/1200], Temp: 0.9290, Energy: -81.243898+0.000847j
[2025-09-08 16:10:21] [Iter 1258/2250] R3[207/1200], Temp: 0.9284, Energy: -81.123893-0.002783j
[2025-09-08 16:11:07] [Iter 1259/2250] R3[208/1200], Temp: 0.9277, Energy: -81.193730+0.000654j
[2025-09-08 16:11:52] [Iter 1260/2250] R3[209/1200], Temp: 0.9270, Energy: -81.149748-0.000894j
[2025-09-08 16:12:38] [Iter 1261/2250] R3[210/1200], Temp: 0.9263, Energy: -81.149773-0.000024j
[2025-09-08 16:13:23] [Iter 1262/2250] R3[211/1200], Temp: 0.9256, Energy: -81.082969+0.002507j
[2025-09-08 16:14:09] [Iter 1263/2250] R3[212/1200], Temp: 0.9249, Energy: -81.165933-0.000506j
[2025-09-08 16:14:54] [Iter 1264/2250] R3[213/1200], Temp: 0.9243, Energy: -81.120489-0.000687j
[2025-09-08 16:15:40] [Iter 1265/2250] R3[214/1200], Temp: 0.9236, Energy: -81.081925+0.000096j
[2025-09-08 16:16:26] [Iter 1266/2250] R3[215/1200], Temp: 0.9229, Energy: -81.085016-0.000181j
[2025-09-08 16:17:11] [Iter 1267/2250] R3[216/1200], Temp: 0.9222, Energy: -81.188280-0.000988j
[2025-09-08 16:17:57] [Iter 1268/2250] R3[217/1200], Temp: 0.9215, Energy: -81.170332+0.000103j
[2025-09-08 16:18:42] [Iter 1269/2250] R3[218/1200], Temp: 0.9208, Energy: -81.187071-0.001194j
[2025-09-08 16:19:28] [Iter 1270/2250] R3[219/1200], Temp: 0.9200, Energy: -81.215217+0.003040j
[2025-09-08 16:20:13] [Iter 1271/2250] R3[220/1200], Temp: 0.9193, Energy: -81.198408-0.000728j
[2025-09-08 16:20:59] [Iter 1272/2250] R3[221/1200], Temp: 0.9186, Energy: -81.148740-0.001765j
[2025-09-08 16:21:44] [Iter 1273/2250] R3[222/1200], Temp: 0.9179, Energy: -81.146386-0.000612j
[2025-09-08 16:22:30] [Iter 1274/2250] R3[223/1200], Temp: 0.9172, Energy: -81.134565-0.001102j
[2025-09-08 16:23:15] [Iter 1275/2250] R3[224/1200], Temp: 0.9165, Energy: -81.109027+0.001396j
[2025-09-08 16:24:01] [Iter 1276/2250] R3[225/1200], Temp: 0.9157, Energy: -81.173486-0.000329j
[2025-09-08 16:24:46] [Iter 1277/2250] R3[226/1200], Temp: 0.9150, Energy: -81.249035-0.001288j
[2025-09-08 16:25:32] [Iter 1278/2250] R3[227/1200], Temp: 0.9143, Energy: -81.209754-0.001161j
[2025-09-08 16:26:17] [Iter 1279/2250] R3[228/1200], Temp: 0.9135, Energy: -81.161900-0.000820j
[2025-09-08 16:27:03] [Iter 1280/2250] R3[229/1200], Temp: 0.9128, Energy: -81.129101+0.005010j
[2025-09-08 16:27:48] [Iter 1281/2250] R3[230/1200], Temp: 0.9121, Energy: -81.109813-0.002073j
[2025-09-08 16:28:34] [Iter 1282/2250] R3[231/1200], Temp: 0.9113, Energy: -81.199925-0.000836j
[2025-09-08 16:29:19] [Iter 1283/2250] R3[232/1200], Temp: 0.9106, Energy: -81.190265-0.002317j
[2025-09-08 16:30:05] [Iter 1284/2250] R3[233/1200], Temp: 0.9098, Energy: -81.225958-0.000960j
[2025-09-08 16:30:50] [Iter 1285/2250] R3[234/1200], Temp: 0.9091, Energy: -81.226370+0.001482j
[2025-09-08 16:31:36] [Iter 1286/2250] R3[235/1200], Temp: 0.9083, Energy: -81.220418+0.003333j
[2025-09-08 16:32:22] [Iter 1287/2250] R3[236/1200], Temp: 0.9076, Energy: -81.205815+0.000287j
[2025-09-08 16:33:07] [Iter 1288/2250] R3[237/1200], Temp: 0.9068, Energy: -81.181909+0.001898j
[2025-09-08 16:33:53] [Iter 1289/2250] R3[238/1200], Temp: 0.9060, Energy: -81.243157+0.002074j
[2025-09-08 16:34:38] [Iter 1290/2250] R3[239/1200], Temp: 0.9053, Energy: -81.193956-0.000746j
[2025-09-08 16:35:24] [Iter 1291/2250] R3[240/1200], Temp: 0.9045, Energy: -81.203433+0.002039j
[2025-09-08 16:36:09] [Iter 1292/2250] R3[241/1200], Temp: 0.9037, Energy: -81.190463-0.001039j
[2025-09-08 16:36:55] [Iter 1293/2250] R3[242/1200], Temp: 0.9030, Energy: -81.213571+0.000527j
[2025-09-08 16:37:40] [Iter 1294/2250] R3[243/1200], Temp: 0.9022, Energy: -81.156800+0.000234j
[2025-09-08 16:38:26] [Iter 1295/2250] R3[244/1200], Temp: 0.9014, Energy: -81.130857+0.001325j
[2025-09-08 16:39:11] [Iter 1296/2250] R3[245/1200], Temp: 0.9006, Energy: -81.164053-0.003374j
[2025-09-08 16:39:57] [Iter 1297/2250] R3[246/1200], Temp: 0.8998, Energy: -81.138497-0.001770j
[2025-09-08 16:40:42] [Iter 1298/2250] R3[247/1200], Temp: 0.8991, Energy: -81.214136+0.000184j
[2025-09-08 16:41:28] [Iter 1299/2250] R3[248/1200], Temp: 0.8983, Energy: -81.223562-0.002602j
[2025-09-08 16:42:13] [Iter 1300/2250] R3[249/1200], Temp: 0.8975, Energy: -81.171652-0.002387j
[2025-09-08 16:42:59] [Iter 1301/2250] R3[250/1200], Temp: 0.8967, Energy: -81.138547+0.000767j
[2025-09-08 16:43:45] [Iter 1302/2250] R3[251/1200], Temp: 0.8959, Energy: -81.121589-0.001333j
[2025-09-08 16:44:30] [Iter 1303/2250] R3[252/1200], Temp: 0.8951, Energy: -81.182372-0.002409j
[2025-09-08 16:45:16] [Iter 1304/2250] R3[253/1200], Temp: 0.8943, Energy: -81.070673-0.001899j
[2025-09-08 16:46:01] [Iter 1305/2250] R3[254/1200], Temp: 0.8935, Energy: -81.198590+0.000432j
[2025-09-08 16:46:47] [Iter 1306/2250] R3[255/1200], Temp: 0.8927, Energy: -81.034668-0.000851j
[2025-09-08 16:47:32] [Iter 1307/2250] R3[256/1200], Temp: 0.8918, Energy: -81.077690+0.000685j
[2025-09-08 16:48:18] [Iter 1308/2250] R3[257/1200], Temp: 0.8910, Energy: -81.102245-0.002416j
[2025-09-08 16:49:03] [Iter 1309/2250] R3[258/1200], Temp: 0.8902, Energy: -81.155431+0.001434j
[2025-09-08 16:49:49] [Iter 1310/2250] R3[259/1200], Temp: 0.8894, Energy: -81.117793+0.002000j
[2025-09-08 16:50:35] [Iter 1311/2250] R3[260/1200], Temp: 0.8886, Energy: -81.130093-0.000042j
[2025-09-08 16:51:20] [Iter 1312/2250] R3[261/1200], Temp: 0.8877, Energy: -81.134691+0.000937j
[2025-09-08 16:52:05] [Iter 1313/2250] R3[262/1200], Temp: 0.8869, Energy: -81.098691-0.001928j
[2025-09-08 16:52:51] [Iter 1314/2250] R3[263/1200], Temp: 0.8861, Energy: -81.179638+0.002407j
[2025-09-08 16:53:36] [Iter 1315/2250] R3[264/1200], Temp: 0.8853, Energy: -81.135412-0.001217j
[2025-09-08 16:54:22] [Iter 1316/2250] R3[265/1200], Temp: 0.8844, Energy: -81.178462-0.000326j
[2025-09-08 16:55:07] [Iter 1317/2250] R3[266/1200], Temp: 0.8836, Energy: -81.170736-0.000470j
[2025-09-08 16:55:53] [Iter 1318/2250] R3[267/1200], Temp: 0.8827, Energy: -81.205149-0.001189j
[2025-09-08 16:56:38] [Iter 1319/2250] R3[268/1200], Temp: 0.8819, Energy: -81.242420-0.005222j
[2025-09-08 16:57:24] [Iter 1320/2250] R3[269/1200], Temp: 0.8811, Energy: -81.177928-0.001841j
[2025-09-08 16:58:10] [Iter 1321/2250] R3[270/1200], Temp: 0.8802, Energy: -81.198659+0.002918j
[2025-09-08 16:58:55] [Iter 1322/2250] R3[271/1200], Temp: 0.8794, Energy: -81.193895-0.001253j
[2025-09-08 16:59:41] [Iter 1323/2250] R3[272/1200], Temp: 0.8785, Energy: -81.153287-0.000438j
[2025-09-08 17:00:26] [Iter 1324/2250] R3[273/1200], Temp: 0.8776, Energy: -81.138966-0.001312j
[2025-09-08 17:01:12] [Iter 1325/2250] R3[274/1200], Temp: 0.8768, Energy: -81.174521+0.001449j
[2025-09-08 17:01:57] [Iter 1326/2250] R3[275/1200], Temp: 0.8759, Energy: -81.211139-0.001407j
[2025-09-08 17:02:43] [Iter 1327/2250] R3[276/1200], Temp: 0.8751, Energy: -81.186995+0.001295j
[2025-09-08 17:03:28] [Iter 1328/2250] R3[277/1200], Temp: 0.8742, Energy: -81.224605-0.001017j
[2025-09-08 17:04:14] [Iter 1329/2250] R3[278/1200], Temp: 0.8733, Energy: -81.251030-0.000170j
[2025-09-08 17:04:59] [Iter 1330/2250] R3[279/1200], Temp: 0.8724, Energy: -81.209328+0.003649j
[2025-09-08 17:05:45] [Iter 1331/2250] R3[280/1200], Temp: 0.8716, Energy: -81.270822+0.003258j
[2025-09-08 17:06:30] [Iter 1332/2250] R3[281/1200], Temp: 0.8707, Energy: -81.198695+0.001259j
[2025-09-08 17:07:16] [Iter 1333/2250] R3[282/1200], Temp: 0.8698, Energy: -81.250105-0.000163j
[2025-09-08 17:08:01] [Iter 1334/2250] R3[283/1200], Temp: 0.8689, Energy: -81.258076-0.002500j
[2025-09-08 17:08:47] [Iter 1335/2250] R3[284/1200], Temp: 0.8680, Energy: -81.234090+0.000711j
[2025-09-08 17:09:32] [Iter 1336/2250] R3[285/1200], Temp: 0.8672, Energy: -81.174724+0.000247j
[2025-09-08 17:10:18] [Iter 1337/2250] R3[286/1200], Temp: 0.8663, Energy: -81.134120-0.001435j
[2025-09-08 17:11:03] [Iter 1338/2250] R3[287/1200], Temp: 0.8654, Energy: -81.117683+0.000332j
[2025-09-08 17:11:49] [Iter 1339/2250] R3[288/1200], Temp: 0.8645, Energy: -81.140320+0.001657j
[2025-09-08 17:12:34] [Iter 1340/2250] R3[289/1200], Temp: 0.8636, Energy: -81.169555-0.001197j
[2025-09-08 17:13:20] [Iter 1341/2250] R3[290/1200], Temp: 0.8627, Energy: -81.175259-0.000196j
[2025-09-08 17:14:06] [Iter 1342/2250] R3[291/1200], Temp: 0.8618, Energy: -81.191717+0.000103j
[2025-09-08 17:14:51] [Iter 1343/2250] R3[292/1200], Temp: 0.8609, Energy: -81.191805-0.005232j
[2025-09-08 17:15:37] [Iter 1344/2250] R3[293/1200], Temp: 0.8600, Energy: -81.180364+0.000805j
[2025-09-08 17:16:22] [Iter 1345/2250] R3[294/1200], Temp: 0.8591, Energy: -81.177743+0.000653j
[2025-09-08 17:17:08] [Iter 1346/2250] R3[295/1200], Temp: 0.8582, Energy: -81.226148+0.002176j
[2025-09-08 17:17:53] [Iter 1347/2250] R3[296/1200], Temp: 0.8572, Energy: -81.197200+0.000472j
[2025-09-08 17:18:39] [Iter 1348/2250] R3[297/1200], Temp: 0.8563, Energy: -81.214641-0.001905j
[2025-09-08 17:19:24] [Iter 1349/2250] R3[298/1200], Temp: 0.8554, Energy: -81.184765+0.001112j
[2025-09-08 17:20:10] [Iter 1350/2250] R3[299/1200], Temp: 0.8545, Energy: -81.133669-0.000767j
[2025-09-08 17:20:55] [Iter 1351/2250] R3[300/1200], Temp: 0.8536, Energy: -81.153819+0.001432j
[2025-09-08 17:21:41] [Iter 1352/2250] R3[301/1200], Temp: 0.8526, Energy: -81.128137+0.000480j
[2025-09-08 17:22:27] [Iter 1353/2250] R3[302/1200], Temp: 0.8517, Energy: -81.151330-0.001424j
[2025-09-08 17:23:12] [Iter 1354/2250] R3[303/1200], Temp: 0.8508, Energy: -81.152542-0.003654j
[2025-09-08 17:23:58] [Iter 1355/2250] R3[304/1200], Temp: 0.8498, Energy: -81.210131-0.001477j
[2025-09-08 17:24:43] [Iter 1356/2250] R3[305/1200], Temp: 0.8489, Energy: -81.260811+0.000022j
[2025-09-08 17:25:29] [Iter 1357/2250] R3[306/1200], Temp: 0.8480, Energy: -81.240627+0.003901j
[2025-09-08 17:26:14] [Iter 1358/2250] R3[307/1200], Temp: 0.8470, Energy: -81.256473+0.000206j
[2025-09-08 17:27:00] [Iter 1359/2250] R3[308/1200], Temp: 0.8461, Energy: -81.198526-0.004286j
[2025-09-08 17:27:45] [Iter 1360/2250] R3[309/1200], Temp: 0.8451, Energy: -81.212694+0.001483j
[2025-09-08 17:28:31] [Iter 1361/2250] R3[310/1200], Temp: 0.8442, Energy: -81.121969+0.001212j
[2025-09-08 17:29:16] [Iter 1362/2250] R3[311/1200], Temp: 0.8432, Energy: -81.133635+0.003253j
[2025-09-08 17:30:02] [Iter 1363/2250] R3[312/1200], Temp: 0.8423, Energy: -81.193408+0.002461j
[2025-09-08 17:30:48] [Iter 1364/2250] R3[313/1200], Temp: 0.8413, Energy: -81.213479+0.001369j
[2025-09-08 17:31:33] [Iter 1365/2250] R3[314/1200], Temp: 0.8404, Energy: -81.176449-0.000069j
[2025-09-08 17:32:19] [Iter 1366/2250] R3[315/1200], Temp: 0.8394, Energy: -81.191267-0.000341j
[2025-09-08 17:33:04] [Iter 1367/2250] R3[316/1200], Temp: 0.8384, Energy: -81.146007-0.002221j
[2025-09-08 17:33:50] [Iter 1368/2250] R3[317/1200], Temp: 0.8375, Energy: -81.252550-0.001337j
[2025-09-08 17:34:35] [Iter 1369/2250] R3[318/1200], Temp: 0.8365, Energy: -81.217599+0.002342j
[2025-09-08 17:35:21] [Iter 1370/2250] R3[319/1200], Temp: 0.8355, Energy: -81.275478-0.004710j
[2025-09-08 17:36:06] [Iter 1371/2250] R3[320/1200], Temp: 0.8346, Energy: -81.346148-0.001964j
[2025-09-08 17:36:52] [Iter 1372/2250] R3[321/1200], Temp: 0.8336, Energy: -81.336741-0.002539j
[2025-09-08 17:37:38] [Iter 1373/2250] R3[322/1200], Temp: 0.8326, Energy: -81.266436-0.001768j
[2025-09-08 17:38:23] [Iter 1374/2250] R3[323/1200], Temp: 0.8316, Energy: -81.246454-0.003372j
[2025-09-08 17:39:09] [Iter 1375/2250] R3[324/1200], Temp: 0.8307, Energy: -81.220710-0.000283j
[2025-09-08 17:39:54] [Iter 1376/2250] R3[325/1200], Temp: 0.8297, Energy: -81.093164-0.000092j
[2025-09-08 17:40:40] [Iter 1377/2250] R3[326/1200], Temp: 0.8287, Energy: -81.052409-0.002958j
[2025-09-08 17:41:25] [Iter 1378/2250] R3[327/1200], Temp: 0.8277, Energy: -81.108015-0.002392j
[2025-09-08 17:42:11] [Iter 1379/2250] R3[328/1200], Temp: 0.8267, Energy: -81.106081+0.000784j
[2025-09-08 17:42:56] [Iter 1380/2250] R3[329/1200], Temp: 0.8257, Energy: -81.183994-0.001072j
[2025-09-08 17:43:42] [Iter 1381/2250] R3[330/1200], Temp: 0.8247, Energy: -81.249077-0.000823j
[2025-09-08 17:44:27] [Iter 1382/2250] R3[331/1200], Temp: 0.8237, Energy: -81.192911+0.000206j
[2025-09-08 17:45:13] [Iter 1383/2250] R3[332/1200], Temp: 0.8227, Energy: -81.166853-0.003136j
[2025-09-08 17:45:58] [Iter 1384/2250] R3[333/1200], Temp: 0.8217, Energy: -81.183926-0.001475j
[2025-09-08 17:46:44] [Iter 1385/2250] R3[334/1200], Temp: 0.8207, Energy: -81.155090+0.002763j
[2025-09-08 17:47:29] [Iter 1386/2250] R3[335/1200], Temp: 0.8197, Energy: -81.213905-0.000450j
[2025-09-08 17:48:15] [Iter 1387/2250] R3[336/1200], Temp: 0.8187, Energy: -81.242738+0.000728j
[2025-09-08 17:49:00] [Iter 1388/2250] R3[337/1200], Temp: 0.8177, Energy: -81.260082+0.002521j
[2025-09-08 17:49:46] [Iter 1389/2250] R3[338/1200], Temp: 0.8167, Energy: -81.262504-0.000179j
[2025-09-08 17:50:32] [Iter 1390/2250] R3[339/1200], Temp: 0.8157, Energy: -81.190170+0.001660j
[2025-09-08 17:51:17] [Iter 1391/2250] R3[340/1200], Temp: 0.8147, Energy: -81.176503-0.001739j
[2025-09-08 17:52:03] [Iter 1392/2250] R3[341/1200], Temp: 0.8136, Energy: -81.208059-0.000069j
[2025-09-08 17:52:48] [Iter 1393/2250] R3[342/1200], Temp: 0.8126, Energy: -81.207633-0.002087j
[2025-09-08 17:53:34] [Iter 1394/2250] R3[343/1200], Temp: 0.8116, Energy: -81.213820-0.000684j
[2025-09-08 17:54:19] [Iter 1395/2250] R3[344/1200], Temp: 0.8106, Energy: -81.197970-0.000818j
[2025-09-08 17:55:05] [Iter 1396/2250] R3[345/1200], Temp: 0.8095, Energy: -81.142543+0.002073j
[2025-09-08 17:55:50] [Iter 1397/2250] R3[346/1200], Temp: 0.8085, Energy: -81.207929-0.001185j
[2025-09-08 17:56:36] [Iter 1398/2250] R3[347/1200], Temp: 0.8075, Energy: -81.136942+0.001773j
[2025-09-08 17:57:22] [Iter 1399/2250] R3[348/1200], Temp: 0.8065, Energy: -81.169472+0.003234j
[2025-09-08 17:58:07] [Iter 1400/2250] R3[349/1200], Temp: 0.8054, Energy: -81.176292-0.000526j
[2025-09-08 17:58:53] [Iter 1401/2250] R3[350/1200], Temp: 0.8044, Energy: -81.167917+0.001574j
[2025-09-08 17:59:38] [Iter 1402/2250] R3[351/1200], Temp: 0.8033, Energy: -81.149401+0.001123j
[2025-09-08 18:00:24] [Iter 1403/2250] R3[352/1200], Temp: 0.8023, Energy: -81.162686-0.000869j
[2025-09-08 18:01:09] [Iter 1404/2250] R3[353/1200], Temp: 0.8013, Energy: -81.175541+0.003380j
[2025-09-08 18:01:55] [Iter 1405/2250] R3[354/1200], Temp: 0.8002, Energy: -81.153639-0.005228j
[2025-09-08 18:02:40] [Iter 1406/2250] R3[355/1200], Temp: 0.7992, Energy: -81.206573+0.000019j
[2025-09-08 18:03:26] [Iter 1407/2250] R3[356/1200], Temp: 0.7981, Energy: -81.123496-0.001196j
[2025-09-08 18:04:11] [Iter 1408/2250] R3[357/1200], Temp: 0.7971, Energy: -81.007090-0.010741j
[2025-09-08 18:04:57] [Iter 1409/2250] R3[358/1200], Temp: 0.7960, Energy: -81.117955+0.001248j
[2025-09-08 18:05:42] [Iter 1410/2250] R3[359/1200], Temp: 0.7950, Energy: -81.083775-0.001570j
[2025-09-08 18:06:28] [Iter 1411/2250] R3[360/1200], Temp: 0.7939, Energy: -81.128615-0.003235j
[2025-09-08 18:07:16] [Iter 1412/2250] R3[361/1200], Temp: 0.7928, Energy: -81.156349+0.001705j
[2025-09-08 18:08:02] [Iter 1413/2250] R3[362/1200], Temp: 0.7918, Energy: -81.196232-0.002278j
[2025-09-08 18:08:47] [Iter 1414/2250] R3[363/1200], Temp: 0.7907, Energy: -81.137626+0.000444j
[2025-09-08 18:09:33] [Iter 1415/2250] R3[364/1200], Temp: 0.7896, Energy: -81.173146+0.004069j
[2025-09-08 18:10:18] [Iter 1416/2250] R3[365/1200], Temp: 0.7886, Energy: -81.157157-0.003199j
[2025-09-08 18:11:04] [Iter 1417/2250] R3[366/1200], Temp: 0.7875, Energy: -81.136648-0.002393j
[2025-09-08 18:11:49] [Iter 1418/2250] R3[367/1200], Temp: 0.7864, Energy: -81.143790+0.000940j
[2025-09-08 18:12:35] [Iter 1419/2250] R3[368/1200], Temp: 0.7854, Energy: -81.168283-0.002288j
[2025-09-08 18:13:21] [Iter 1420/2250] R3[369/1200], Temp: 0.7843, Energy: -81.250746-0.002250j
[2025-09-08 18:14:06] [Iter 1421/2250] R3[370/1200], Temp: 0.7832, Energy: -81.217809+0.000005j
[2025-09-08 18:14:52] [Iter 1422/2250] R3[371/1200], Temp: 0.7821, Energy: -81.304605-0.001061j
[2025-09-08 18:15:37] [Iter 1423/2250] R3[372/1200], Temp: 0.7810, Energy: -81.223377-0.008271j
[2025-09-08 18:16:23] [Iter 1424/2250] R3[373/1200], Temp: 0.7800, Energy: -81.235316+0.002106j
[2025-09-08 18:17:08] [Iter 1425/2250] R3[374/1200], Temp: 0.7789, Energy: -81.272739-0.000399j
[2025-09-08 18:17:54] [Iter 1426/2250] R3[375/1200], Temp: 0.7778, Energy: -81.247663-0.001470j
[2025-09-08 18:18:39] [Iter 1427/2250] R3[376/1200], Temp: 0.7767, Energy: -81.244734-0.001948j
[2025-09-08 18:19:25] [Iter 1428/2250] R3[377/1200], Temp: 0.7756, Energy: -81.154739+0.002269j
[2025-09-08 18:20:10] [Iter 1429/2250] R3[378/1200], Temp: 0.7745, Energy: -81.146613+0.000024j
[2025-09-08 18:20:56] [Iter 1430/2250] R3[379/1200], Temp: 0.7734, Energy: -81.142713-0.002334j
[2025-09-08 18:21:41] [Iter 1431/2250] R3[380/1200], Temp: 0.7723, Energy: -81.158861-0.000972j
[2025-09-08 18:22:27] [Iter 1432/2250] R3[381/1200], Temp: 0.7712, Energy: -81.154334+0.004363j
[2025-09-08 18:23:12] [Iter 1433/2250] R3[382/1200], Temp: 0.7701, Energy: -81.145834-0.002020j
[2025-09-08 18:23:58] [Iter 1434/2250] R3[383/1200], Temp: 0.7690, Energy: -81.193635+0.000524j
[2025-09-08 18:24:44] [Iter 1435/2250] R3[384/1200], Temp: 0.7679, Energy: -81.097048+0.002101j
[2025-09-08 18:25:29] [Iter 1436/2250] R3[385/1200], Temp: 0.7668, Energy: -81.148431-0.000269j
[2025-09-08 18:26:15] [Iter 1437/2250] R3[386/1200], Temp: 0.7657, Energy: -81.145279+0.000394j
[2025-09-08 18:27:00] [Iter 1438/2250] R3[387/1200], Temp: 0.7646, Energy: -81.190277+0.000545j
[2025-09-08 18:27:46] [Iter 1439/2250] R3[388/1200], Temp: 0.7635, Energy: -81.173821-0.003699j
[2025-09-08 18:28:31] [Iter 1440/2250] R3[389/1200], Temp: 0.7624, Energy: -81.144571-0.002570j
[2025-09-08 18:29:17] [Iter 1441/2250] R3[390/1200], Temp: 0.7612, Energy: -81.113535-0.000399j
[2025-09-08 18:30:02] [Iter 1442/2250] R3[391/1200], Temp: 0.7601, Energy: -81.208927-0.000843j
[2025-09-08 18:30:48] [Iter 1443/2250] R3[392/1200], Temp: 0.7590, Energy: -81.166826+0.000185j
[2025-09-08 18:31:33] [Iter 1444/2250] R3[393/1200], Temp: 0.7579, Energy: -81.220680+0.001073j
[2025-09-08 18:32:19] [Iter 1445/2250] R3[394/1200], Temp: 0.7568, Energy: -81.153085+0.000954j
[2025-09-08 18:33:04] [Iter 1446/2250] R3[395/1200], Temp: 0.7556, Energy: -81.142352-0.001166j
[2025-09-08 18:33:50] [Iter 1447/2250] R3[396/1200], Temp: 0.7545, Energy: -81.129744-0.004288j
[2025-09-08 18:34:35] [Iter 1448/2250] R3[397/1200], Temp: 0.7534, Energy: -81.107416-0.000909j
[2025-09-08 18:35:21] [Iter 1449/2250] R3[398/1200], Temp: 0.7523, Energy: -81.241204-0.000732j
[2025-09-08 18:36:07] [Iter 1450/2250] R3[399/1200], Temp: 0.7511, Energy: -81.181815+0.000424j
[2025-09-08 18:36:52] [Iter 1451/2250] R3[400/1200], Temp: 0.7500, Energy: -81.202302+0.000011j
[2025-09-08 18:37:38] [Iter 1452/2250] R3[401/1200], Temp: 0.7489, Energy: -81.144001+0.001084j
[2025-09-08 18:38:23] [Iter 1453/2250] R3[402/1200], Temp: 0.7477, Energy: -81.157996-0.000860j
[2025-09-08 18:39:09] [Iter 1454/2250] R3[403/1200], Temp: 0.7466, Energy: -81.070014-0.002406j
[2025-09-08 18:39:54] [Iter 1455/2250] R3[404/1200], Temp: 0.7455, Energy: -81.137296+0.000646j
[2025-09-08 18:40:40] [Iter 1456/2250] R3[405/1200], Temp: 0.7443, Energy: -81.153393+0.000468j
[2025-09-08 18:41:25] [Iter 1457/2250] R3[406/1200], Temp: 0.7432, Energy: -81.168511+0.001787j
[2025-09-08 18:42:11] [Iter 1458/2250] R3[407/1200], Temp: 0.7420, Energy: -81.159051-0.004305j
[2025-09-08 18:42:56] [Iter 1459/2250] R3[408/1200], Temp: 0.7409, Energy: -81.176267+0.001675j
[2025-09-08 18:43:42] [Iter 1460/2250] R3[409/1200], Temp: 0.7397, Energy: -81.204225-0.002893j
[2025-09-08 18:44:27] [Iter 1461/2250] R3[410/1200], Temp: 0.7386, Energy: -81.191198+0.000624j
[2025-09-08 18:45:13] [Iter 1462/2250] R3[411/1200], Temp: 0.7374, Energy: -81.167271+0.000225j
[2025-09-08 18:45:58] [Iter 1463/2250] R3[412/1200], Temp: 0.7363, Energy: -81.163344-0.001952j
[2025-09-08 18:46:44] [Iter 1464/2250] R3[413/1200], Temp: 0.7351, Energy: -81.164529-0.000790j
[2025-09-08 18:47:29] [Iter 1465/2250] R3[414/1200], Temp: 0.7340, Energy: -81.158252+0.000233j
[2025-09-08 18:48:15] [Iter 1466/2250] R3[415/1200], Temp: 0.7328, Energy: -81.132242+0.001146j
[2025-09-08 18:49:00] [Iter 1467/2250] R3[416/1200], Temp: 0.7316, Energy: -81.162292+0.003350j
[2025-09-08 18:49:46] [Iter 1468/2250] R3[417/1200], Temp: 0.7305, Energy: -81.144242-0.000184j
[2025-09-08 18:50:31] [Iter 1469/2250] R3[418/1200], Temp: 0.7293, Energy: -81.173487+0.001257j
[2025-09-08 18:51:17] [Iter 1470/2250] R3[419/1200], Temp: 0.7282, Energy: -81.226073-0.000233j
[2025-09-08 18:52:02] [Iter 1471/2250] R3[420/1200], Temp: 0.7270, Energy: -81.253447+0.002039j
[2025-09-08 18:52:48] [Iter 1472/2250] R3[421/1200], Temp: 0.7258, Energy: -81.289481+0.002640j
[2025-09-08 18:53:33] [Iter 1473/2250] R3[422/1200], Temp: 0.7247, Energy: -81.213542-0.002369j
[2025-09-08 18:54:20] [Iter 1474/2250] R3[423/1200], Temp: 0.7235, Energy: -81.121142+0.001885j
[2025-09-08 18:55:05] [Iter 1475/2250] R3[424/1200], Temp: 0.7223, Energy: -81.187488-0.001456j
[2025-09-08 18:55:51] [Iter 1476/2250] R3[425/1200], Temp: 0.7211, Energy: -81.159308-0.001892j
[2025-09-08 18:56:36] [Iter 1477/2250] R3[426/1200], Temp: 0.7200, Energy: -81.128894-0.000256j
[2025-09-08 18:57:22] [Iter 1478/2250] R3[427/1200], Temp: 0.7188, Energy: -81.175216-0.001690j
[2025-09-08 18:58:07] [Iter 1479/2250] R3[428/1200], Temp: 0.7176, Energy: -81.163676-0.002791j
[2025-09-08 18:58:53] [Iter 1480/2250] R3[429/1200], Temp: 0.7164, Energy: -81.153950+0.000216j
[2025-09-08 18:59:38] [Iter 1481/2250] R3[430/1200], Temp: 0.7153, Energy: -81.123622-0.000462j
[2025-09-08 19:00:24] [Iter 1482/2250] R3[431/1200], Temp: 0.7141, Energy: -81.144336-0.001130j
[2025-09-08 19:01:09] [Iter 1483/2250] R3[432/1200], Temp: 0.7129, Energy: -81.243657-0.000456j
[2025-09-08 19:01:55] [Iter 1484/2250] R3[433/1200], Temp: 0.7117, Energy: -81.231130+0.000820j
[2025-09-08 19:02:40] [Iter 1485/2250] R3[434/1200], Temp: 0.7105, Energy: -81.233378+0.002794j
[2025-09-08 19:03:26] [Iter 1486/2250] R3[435/1200], Temp: 0.7093, Energy: -81.186143+0.002029j
[2025-09-08 19:04:11] [Iter 1487/2250] R3[436/1200], Temp: 0.7081, Energy: -81.172606+0.001695j
[2025-09-08 19:04:57] [Iter 1488/2250] R3[437/1200], Temp: 0.7069, Energy: -81.268274+0.000849j
[2025-09-08 19:05:42] [Iter 1489/2250] R3[438/1200], Temp: 0.7058, Energy: -81.198958-0.002515j
[2025-09-08 19:06:28] [Iter 1490/2250] R3[439/1200], Temp: 0.7046, Energy: -81.166432+0.003379j
[2025-09-08 19:07:13] [Iter 1491/2250] R3[440/1200], Temp: 0.7034, Energy: -81.142930-0.001063j
[2025-09-08 19:07:59] [Iter 1492/2250] R3[441/1200], Temp: 0.7022, Energy: -81.105180+0.002205j
[2025-09-08 19:08:44] [Iter 1493/2250] R3[442/1200], Temp: 0.7010, Energy: -81.139857+0.000564j
[2025-09-08 19:09:30] [Iter 1494/2250] R3[443/1200], Temp: 0.6998, Energy: -81.193102-0.001640j
[2025-09-08 19:10:15] [Iter 1495/2250] R3[444/1200], Temp: 0.6986, Energy: -81.167181-0.000981j
[2025-09-08 19:11:01] [Iter 1496/2250] R3[445/1200], Temp: 0.6974, Energy: -81.178035-0.001037j
[2025-09-08 19:11:46] [Iter 1497/2250] R3[446/1200], Temp: 0.6962, Energy: -81.180386-0.001208j
[2025-09-08 19:12:32] [Iter 1498/2250] R3[447/1200], Temp: 0.6950, Energy: -81.144373-0.001570j
[2025-09-08 19:13:17] [Iter 1499/2250] R3[448/1200], Temp: 0.6938, Energy: -81.205096+0.000325j
[2025-09-08 19:14:03] [Iter 1500/2250] R3[449/1200], Temp: 0.6926, Energy: -81.191559-0.000551j
[2025-09-08 19:14:03] ✓ Checkpoint saved: checkpoint_iter_001500.pkl
[2025-09-08 19:14:49] [Iter 1501/2250] R3[450/1200], Temp: 0.6913, Energy: -81.109228+0.001714j
[2025-09-08 19:15:34] [Iter 1502/2250] R3[451/1200], Temp: 0.6901, Energy: -81.120453+0.001785j
[2025-09-08 19:16:19] [Iter 1503/2250] R3[452/1200], Temp: 0.6889, Energy: -81.143553+0.001924j
[2025-09-08 19:17:05] [Iter 1504/2250] R3[453/1200], Temp: 0.6877, Energy: -81.130748+0.000343j
[2025-09-08 19:17:50] [Iter 1505/2250] R3[454/1200], Temp: 0.6865, Energy: -81.223977+0.000824j
[2025-09-08 19:18:36] [Iter 1506/2250] R3[455/1200], Temp: 0.6853, Energy: -81.189006-0.004068j
[2025-09-08 19:19:21] [Iter 1507/2250] R3[456/1200], Temp: 0.6841, Energy: -81.254038+0.002636j
[2025-09-08 19:20:07] [Iter 1508/2250] R3[457/1200], Temp: 0.6828, Energy: -81.214803+0.000817j
[2025-09-08 19:20:53] [Iter 1509/2250] R3[458/1200], Temp: 0.6816, Energy: -81.255324+0.000051j
[2025-09-08 19:21:38] [Iter 1510/2250] R3[459/1200], Temp: 0.6804, Energy: -81.238095-0.000123j
[2025-09-08 19:22:24] [Iter 1511/2250] R3[460/1200], Temp: 0.6792, Energy: -81.219830+0.002299j
[2025-09-08 19:23:09] [Iter 1512/2250] R3[461/1200], Temp: 0.6780, Energy: -81.169043-0.000694j
[2025-09-08 19:23:55] [Iter 1513/2250] R3[462/1200], Temp: 0.6767, Energy: -81.074777+0.003049j
[2025-09-08 19:24:40] [Iter 1514/2250] R3[463/1200], Temp: 0.6755, Energy: -81.108900+0.001700j
[2025-09-08 19:25:26] [Iter 1515/2250] R3[464/1200], Temp: 0.6743, Energy: -81.114275+0.000794j
[2025-09-08 19:26:11] [Iter 1516/2250] R3[465/1200], Temp: 0.6731, Energy: -81.113099+0.000539j
[2025-09-08 19:26:57] [Iter 1517/2250] R3[466/1200], Temp: 0.6718, Energy: -81.156788-0.000454j
[2025-09-08 19:27:43] [Iter 1518/2250] R3[467/1200], Temp: 0.6706, Energy: -81.142261+0.000411j
[2025-09-08 19:28:28] [Iter 1519/2250] R3[468/1200], Temp: 0.6694, Energy: -81.208784+0.002108j
[2025-09-08 19:29:14] [Iter 1520/2250] R3[469/1200], Temp: 0.6681, Energy: -81.113368+0.001949j
[2025-09-08 19:29:59] [Iter 1521/2250] R3[470/1200], Temp: 0.6669, Energy: -81.118633+0.000808j
[2025-09-08 19:30:45] [Iter 1522/2250] R3[471/1200], Temp: 0.6657, Energy: -81.119404-0.001643j
[2025-09-08 19:31:30] [Iter 1523/2250] R3[472/1200], Temp: 0.6644, Energy: -81.083866+0.000522j
[2025-09-08 19:32:16] [Iter 1524/2250] R3[473/1200], Temp: 0.6632, Energy: -81.091380-0.000708j
[2025-09-08 19:33:01] [Iter 1525/2250] R3[474/1200], Temp: 0.6620, Energy: -81.174953+0.002221j
[2025-09-08 19:33:47] [Iter 1526/2250] R3[475/1200], Temp: 0.6607, Energy: -81.145865+0.002863j
[2025-09-08 19:34:32] [Iter 1527/2250] R3[476/1200], Temp: 0.6595, Energy: -81.172879-0.000359j
[2025-09-08 19:35:18] [Iter 1528/2250] R3[477/1200], Temp: 0.6582, Energy: -81.175182-0.001610j
[2025-09-08 19:36:03] [Iter 1529/2250] R3[478/1200], Temp: 0.6570, Energy: -81.141195+0.001009j
[2025-09-08 19:36:49] [Iter 1530/2250] R3[479/1200], Temp: 0.6558, Energy: -81.197838-0.002673j
[2025-09-08 19:37:34] [Iter 1531/2250] R3[480/1200], Temp: 0.6545, Energy: -81.206073-0.000339j
[2025-09-08 19:38:20] [Iter 1532/2250] R3[481/1200], Temp: 0.6533, Energy: -81.217250-0.001653j
[2025-09-08 19:39:05] [Iter 1533/2250] R3[482/1200], Temp: 0.6520, Energy: -81.194510-0.000257j
[2025-09-08 19:39:51] [Iter 1534/2250] R3[483/1200], Temp: 0.6508, Energy: -81.171415+0.001021j
[2025-09-08 19:40:36] [Iter 1535/2250] R3[484/1200], Temp: 0.6495, Energy: -81.154199-0.000332j
[2025-09-08 19:41:22] [Iter 1536/2250] R3[485/1200], Temp: 0.6483, Energy: -81.151667-0.001798j
[2025-09-08 19:42:07] [Iter 1537/2250] R3[486/1200], Temp: 0.6470, Energy: -81.197118+0.000522j
[2025-09-08 19:42:53] [Iter 1538/2250] R3[487/1200], Temp: 0.6458, Energy: -81.183246+0.001465j
[2025-09-08 19:43:38] [Iter 1539/2250] R3[488/1200], Temp: 0.6445, Energy: -81.207034+0.000051j
[2025-09-08 19:44:24] [Iter 1540/2250] R3[489/1200], Temp: 0.6433, Energy: -81.220214-0.003320j
[2025-09-08 19:45:09] [Iter 1541/2250] R3[490/1200], Temp: 0.6420, Energy: -81.119810-0.000591j
[2025-09-08 19:45:54] [Iter 1542/2250] R3[491/1200], Temp: 0.6408, Energy: -81.189084-0.001180j
[2025-09-08 19:46:40] [Iter 1543/2250] R3[492/1200], Temp: 0.6395, Energy: -81.162385-0.000274j
[2025-09-08 19:47:25] [Iter 1544/2250] R3[493/1200], Temp: 0.6382, Energy: -81.170079+0.002470j
[2025-09-08 19:48:11] [Iter 1545/2250] R3[494/1200], Temp: 0.6370, Energy: -81.110848-0.000456j
[2025-09-08 19:48:57] [Iter 1546/2250] R3[495/1200], Temp: 0.6357, Energy: -81.097577-0.002577j
[2025-09-08 19:49:42] [Iter 1547/2250] R3[496/1200], Temp: 0.6345, Energy: -81.142763+0.000677j
[2025-09-08 19:50:27] [Iter 1548/2250] R3[497/1200], Temp: 0.6332, Energy: -81.176812+0.000737j
[2025-09-08 19:51:13] [Iter 1549/2250] R3[498/1200], Temp: 0.6319, Energy: -81.184102+0.001722j
[2025-09-08 19:51:59] [Iter 1550/2250] R3[499/1200], Temp: 0.6307, Energy: -81.182184+0.000972j
[2025-09-08 19:52:44] [Iter 1551/2250] R3[500/1200], Temp: 0.6294, Energy: -81.175971+0.001867j
[2025-09-08 19:53:30] [Iter 1552/2250] R3[501/1200], Temp: 0.6281, Energy: -81.170750+0.000480j
[2025-09-08 19:54:15] [Iter 1553/2250] R3[502/1200], Temp: 0.6269, Energy: -81.238514+0.000719j
[2025-09-08 19:55:00] [Iter 1554/2250] R3[503/1200], Temp: 0.6256, Energy: -81.222931+0.000774j
[2025-09-08 19:55:46] [Iter 1555/2250] R3[504/1200], Temp: 0.6243, Energy: -81.188721+0.000278j
[2025-09-08 19:56:31] [Iter 1556/2250] R3[505/1200], Temp: 0.6231, Energy: -81.188774+0.000456j
[2025-09-08 19:57:17] [Iter 1557/2250] R3[506/1200], Temp: 0.6218, Energy: -81.127926-0.001976j
[2025-09-08 19:58:02] [Iter 1558/2250] R3[507/1200], Temp: 0.6205, Energy: -81.164237-0.002031j
[2025-09-08 19:58:48] [Iter 1559/2250] R3[508/1200], Temp: 0.6193, Energy: -81.192625+0.000751j
[2025-09-08 19:59:33] [Iter 1560/2250] R3[509/1200], Temp: 0.6180, Energy: -81.199956+0.002439j
[2025-09-08 20:00:19] [Iter 1561/2250] R3[510/1200], Temp: 0.6167, Energy: -81.234421-0.000475j
[2025-09-08 20:01:04] [Iter 1562/2250] R3[511/1200], Temp: 0.6154, Energy: -81.281376-0.000063j
[2025-09-08 20:01:50] [Iter 1563/2250] R3[512/1200], Temp: 0.6142, Energy: -81.237025+0.002642j
[2025-09-08 20:02:35] [Iter 1564/2250] R3[513/1200], Temp: 0.6129, Energy: -81.233459-0.002070j
[2025-09-08 20:03:21] [Iter 1565/2250] R3[514/1200], Temp: 0.6116, Energy: -81.158709+0.002280j
[2025-09-08 20:04:06] [Iter 1566/2250] R3[515/1200], Temp: 0.6103, Energy: -81.160276+0.000975j
[2025-09-08 20:04:52] [Iter 1567/2250] R3[516/1200], Temp: 0.6091, Energy: -81.230084-0.001831j
[2025-09-08 20:05:37] [Iter 1568/2250] R3[517/1200], Temp: 0.6078, Energy: -81.242080-0.001788j
[2025-09-08 20:06:23] [Iter 1569/2250] R3[518/1200], Temp: 0.6065, Energy: -81.235735-0.002702j
[2025-09-08 20:07:08] [Iter 1570/2250] R3[519/1200], Temp: 0.6052, Energy: -81.232438-0.001333j
[2025-09-08 20:07:54] [Iter 1571/2250] R3[520/1200], Temp: 0.6040, Energy: -81.166679-0.000280j
[2025-09-08 20:08:40] [Iter 1572/2250] R3[521/1200], Temp: 0.6027, Energy: -81.214216+0.000741j
[2025-09-08 20:09:25] [Iter 1573/2250] R3[522/1200], Temp: 0.6014, Energy: -81.248980-0.000555j
[2025-09-08 20:10:11] [Iter 1574/2250] R3[523/1200], Temp: 0.6001, Energy: -81.198326-0.000280j
[2025-09-08 20:10:56] [Iter 1575/2250] R3[524/1200], Temp: 0.5988, Energy: -81.197225-0.002442j
[2025-09-08 20:11:42] [Iter 1576/2250] R3[525/1200], Temp: 0.5975, Energy: -81.252106+0.001721j
[2025-09-08 20:12:27] [Iter 1577/2250] R3[526/1200], Temp: 0.5963, Energy: -81.264846-0.001369j
[2025-09-08 20:13:13] [Iter 1578/2250] R3[527/1200], Temp: 0.5950, Energy: -81.238578-0.000714j
[2025-09-08 20:13:58] [Iter 1579/2250] R3[528/1200], Temp: 0.5937, Energy: -81.323409-0.003794j
[2025-09-08 20:14:44] [Iter 1580/2250] R3[529/1200], Temp: 0.5924, Energy: -81.198279-0.000275j
[2025-09-08 20:15:29] [Iter 1581/2250] R3[530/1200], Temp: 0.5911, Energy: -81.206571+0.000209j
[2025-09-08 20:16:15] [Iter 1582/2250] R3[531/1200], Temp: 0.5898, Energy: -81.161591-0.003785j
[2025-09-08 20:17:00] [Iter 1583/2250] R3[532/1200], Temp: 0.5885, Energy: -81.153202+0.001901j
[2025-09-08 20:17:46] [Iter 1584/2250] R3[533/1200], Temp: 0.5873, Energy: -81.147352+0.000150j
[2025-09-08 20:18:31] [Iter 1585/2250] R3[534/1200], Temp: 0.5860, Energy: -81.248251-0.007762j
[2025-09-08 20:19:17] [Iter 1586/2250] R3[535/1200], Temp: 0.5847, Energy: -81.202839-0.003479j
[2025-09-08 20:20:02] [Iter 1587/2250] R3[536/1200], Temp: 0.5834, Energy: -81.174675-0.001121j
[2025-09-08 20:20:48] [Iter 1588/2250] R3[537/1200], Temp: 0.5821, Energy: -81.115958-0.000610j
[2025-09-08 20:21:33] [Iter 1589/2250] R3[538/1200], Temp: 0.5808, Energy: -81.168574+0.002977j
[2025-09-08 20:22:19] [Iter 1590/2250] R3[539/1200], Temp: 0.5795, Energy: -81.119426+0.002550j
[2025-09-08 20:23:04] [Iter 1591/2250] R3[540/1200], Temp: 0.5782, Energy: -81.175983+0.000166j
[2025-09-08 20:23:50] [Iter 1592/2250] R3[541/1200], Temp: 0.5769, Energy: -81.162524+0.002419j
[2025-09-08 20:24:35] [Iter 1593/2250] R3[542/1200], Temp: 0.5756, Energy: -81.268961+0.002392j
[2025-09-08 20:25:21] [Iter 1594/2250] R3[543/1200], Temp: 0.5743, Energy: -81.161470-0.000890j
[2025-09-08 20:26:06] [Iter 1595/2250] R3[544/1200], Temp: 0.5730, Energy: -81.181845-0.002351j
[2025-09-08 20:26:52] [Iter 1596/2250] R3[545/1200], Temp: 0.5717, Energy: -81.205488-0.000198j
[2025-09-08 20:27:37] [Iter 1597/2250] R3[546/1200], Temp: 0.5705, Energy: -81.175180+0.000451j
[2025-09-08 20:28:23] [Iter 1598/2250] R3[547/1200], Temp: 0.5692, Energy: -81.122704+0.002381j
[2025-09-08 20:29:08] [Iter 1599/2250] R3[548/1200], Temp: 0.5679, Energy: -81.181747-0.001555j
[2025-09-08 20:29:54] [Iter 1600/2250] R3[549/1200], Temp: 0.5666, Energy: -81.101057+0.000458j
[2025-09-08 20:30:39] [Iter 1601/2250] R3[550/1200], Temp: 0.5653, Energy: -81.098246+0.002500j
[2025-09-08 20:31:25] [Iter 1602/2250] R3[551/1200], Temp: 0.5640, Energy: -81.141374-0.000555j
[2025-09-08 20:32:10] [Iter 1603/2250] R3[552/1200], Temp: 0.5627, Energy: -81.130960+0.000045j
[2025-09-08 20:32:56] [Iter 1604/2250] R3[553/1200], Temp: 0.5614, Energy: -81.198047-0.000525j
[2025-09-08 20:33:41] [Iter 1605/2250] R3[554/1200], Temp: 0.5601, Energy: -81.149662+0.000411j
[2025-09-08 20:34:27] [Iter 1606/2250] R3[555/1200], Temp: 0.5588, Energy: -81.141016+0.001270j
[2025-09-08 20:35:12] [Iter 1607/2250] R3[556/1200], Temp: 0.5575, Energy: -81.194748+0.001125j
[2025-09-08 20:35:58] [Iter 1608/2250] R3[557/1200], Temp: 0.5562, Energy: -81.084750-0.000746j
[2025-09-08 20:36:43] [Iter 1609/2250] R3[558/1200], Temp: 0.5549, Energy: -81.197046+0.000990j
[2025-09-08 20:37:29] [Iter 1610/2250] R3[559/1200], Temp: 0.5536, Energy: -81.176678+0.001351j
[2025-09-08 20:38:14] [Iter 1611/2250] R3[560/1200], Temp: 0.5523, Energy: -81.126041-0.001921j
[2025-09-08 20:39:00] [Iter 1612/2250] R3[561/1200], Temp: 0.5510, Energy: -81.191825-0.001744j
[2025-09-08 20:39:45] [Iter 1613/2250] R3[562/1200], Temp: 0.5497, Energy: -81.171993-0.000375j
[2025-09-08 20:40:31] [Iter 1614/2250] R3[563/1200], Temp: 0.5484, Energy: -81.096050-0.000311j
[2025-09-08 20:41:16] [Iter 1615/2250] R3[564/1200], Temp: 0.5471, Energy: -81.107268-0.000313j
[2025-09-08 20:42:02] [Iter 1616/2250] R3[565/1200], Temp: 0.5458, Energy: -81.128821+0.001993j
[2025-09-08 20:42:47] [Iter 1617/2250] R3[566/1200], Temp: 0.5444, Energy: -81.181292-0.000219j
[2025-09-08 20:43:33] [Iter 1618/2250] R3[567/1200], Temp: 0.5431, Energy: -81.141912+0.001052j
[2025-09-08 20:44:18] [Iter 1619/2250] R3[568/1200], Temp: 0.5418, Energy: -81.198996+0.001227j
[2025-09-08 20:45:04] [Iter 1620/2250] R3[569/1200], Temp: 0.5405, Energy: -81.083836-0.000829j
[2025-09-08 20:45:49] [Iter 1621/2250] R3[570/1200], Temp: 0.5392, Energy: -81.099179+0.003491j
[2025-09-08 20:46:35] [Iter 1622/2250] R3[571/1200], Temp: 0.5379, Energy: -81.214212-0.002177j
[2025-09-08 20:47:20] [Iter 1623/2250] R3[572/1200], Temp: 0.5366, Energy: -81.185080+0.001875j
[2025-09-08 20:48:06] [Iter 1624/2250] R3[573/1200], Temp: 0.5353, Energy: -81.183407-0.001793j
[2025-09-08 20:48:51] [Iter 1625/2250] R3[574/1200], Temp: 0.5340, Energy: -81.279237+0.000073j
[2025-09-08 20:49:37] [Iter 1626/2250] R3[575/1200], Temp: 0.5327, Energy: -81.169946-0.000705j
[2025-09-08 20:50:22] [Iter 1627/2250] R3[576/1200], Temp: 0.5314, Energy: -81.231547-0.000319j
[2025-09-08 20:51:08] [Iter 1628/2250] R3[577/1200], Temp: 0.5301, Energy: -81.217130+0.001329j
[2025-09-08 20:51:53] [Iter 1629/2250] R3[578/1200], Temp: 0.5288, Energy: -81.230755+0.002703j
[2025-09-08 20:52:39] [Iter 1630/2250] R3[579/1200], Temp: 0.5275, Energy: -81.210109-0.000064j
[2025-09-08 20:53:24] [Iter 1631/2250] R3[580/1200], Temp: 0.5262, Energy: -81.289783+0.002564j
[2025-09-08 20:54:10] [Iter 1632/2250] R3[581/1200], Temp: 0.5249, Energy: -81.329923-0.000711j
[2025-09-08 20:54:56] [Iter 1633/2250] R3[582/1200], Temp: 0.5236, Energy: -81.218411+0.002056j
[2025-09-08 20:55:41] [Iter 1634/2250] R3[583/1200], Temp: 0.5222, Energy: -81.281579+0.002260j
[2025-09-08 20:56:26] [Iter 1635/2250] R3[584/1200], Temp: 0.5209, Energy: -81.221411+0.000726j
[2025-09-08 20:57:12] [Iter 1636/2250] R3[585/1200], Temp: 0.5196, Energy: -81.193889+0.001228j
[2025-09-08 20:57:57] [Iter 1637/2250] R3[586/1200], Temp: 0.5183, Energy: -81.168978-0.001567j
[2025-09-08 20:58:43] [Iter 1638/2250] R3[587/1200], Temp: 0.5170, Energy: -81.204067-0.001606j
[2025-09-08 20:59:28] [Iter 1639/2250] R3[588/1200], Temp: 0.5157, Energy: -81.183373+0.001028j
[2025-09-08 21:00:14] [Iter 1640/2250] R3[589/1200], Temp: 0.5144, Energy: -81.146549+0.000235j
[2025-09-08 21:00:59] [Iter 1641/2250] R3[590/1200], Temp: 0.5131, Energy: -81.074602-0.000284j
[2025-09-08 21:01:45] [Iter 1642/2250] R3[591/1200], Temp: 0.5118, Energy: -81.085483+0.001169j
[2025-09-08 21:02:30] [Iter 1643/2250] R3[592/1200], Temp: 0.5105, Energy: -81.091848-0.000385j
[2025-09-08 21:03:16] [Iter 1644/2250] R3[593/1200], Temp: 0.5092, Energy: -81.075140-0.000853j
[2025-09-08 21:04:02] [Iter 1645/2250] R3[594/1200], Temp: 0.5079, Energy: -81.143645-0.000759j
[2025-09-08 21:04:47] [Iter 1646/2250] R3[595/1200], Temp: 0.5065, Energy: -81.154196-0.001709j
[2025-09-08 21:05:33] [Iter 1647/2250] R3[596/1200], Temp: 0.5052, Energy: -81.231197-0.001078j
[2025-09-08 21:06:18] [Iter 1648/2250] R3[597/1200], Temp: 0.5039, Energy: -81.163715-0.000867j
[2025-09-08 21:07:04] [Iter 1649/2250] R3[598/1200], Temp: 0.5026, Energy: -81.176627+0.000617j
[2025-09-08 21:07:49] [Iter 1650/2250] R3[599/1200], Temp: 0.5013, Energy: -81.239856-0.000471j
[2025-09-08 21:08:34] [Iter 1651/2250] R3[600/1200], Temp: 0.5000, Energy: -81.202973-0.000593j
[2025-09-08 21:09:20] [Iter 1652/2250] R3[601/1200], Temp: 0.4987, Energy: -81.238028-0.000566j
[2025-09-08 21:10:06] [Iter 1653/2250] R3[602/1200], Temp: 0.4974, Energy: -81.175455-0.000697j
[2025-09-08 21:10:51] [Iter 1654/2250] R3[603/1200], Temp: 0.4961, Energy: -81.160261+0.002711j
[2025-09-08 21:11:37] [Iter 1655/2250] R3[604/1200], Temp: 0.4948, Energy: -81.104255+0.000634j
[2025-09-08 21:12:22] [Iter 1656/2250] R3[605/1200], Temp: 0.4935, Energy: -81.186333-0.000601j
[2025-09-08 21:13:11] [Iter 1657/2250] R3[606/1200], Temp: 0.4921, Energy: -81.181116+0.000545j
[2025-09-08 21:13:56] [Iter 1658/2250] R3[607/1200], Temp: 0.4908, Energy: -81.183487+0.006366j
[2025-09-08 21:14:42] [Iter 1659/2250] R3[608/1200], Temp: 0.4895, Energy: -81.205026+0.002182j
[2025-09-08 21:15:27] [Iter 1660/2250] R3[609/1200], Temp: 0.4882, Energy: -81.166043-0.001539j
[2025-09-08 21:16:13] [Iter 1661/2250] R3[610/1200], Temp: 0.4869, Energy: -81.137180-0.002153j
[2025-09-08 21:16:59] [Iter 1662/2250] R3[611/1200], Temp: 0.4856, Energy: -81.087905+0.001193j
[2025-09-08 21:17:44] [Iter 1663/2250] R3[612/1200], Temp: 0.4843, Energy: -81.075151+0.000891j
[2025-09-08 21:18:30] [Iter 1664/2250] R3[613/1200], Temp: 0.4830, Energy: -81.147186+0.000865j
[2025-09-08 21:19:15] [Iter 1665/2250] R3[614/1200], Temp: 0.4817, Energy: -81.111584-0.000637j
[2025-09-08 21:20:01] [Iter 1666/2250] R3[615/1200], Temp: 0.4804, Energy: -81.138365-0.001630j
[2025-09-08 21:20:46] [Iter 1667/2250] R3[616/1200], Temp: 0.4791, Energy: -81.160781-0.000317j
[2025-09-08 21:21:32] [Iter 1668/2250] R3[617/1200], Temp: 0.4778, Energy: -81.133288-0.002600j
[2025-09-08 21:22:17] [Iter 1669/2250] R3[618/1200], Temp: 0.4764, Energy: -81.046473-0.001610j
[2025-09-08 21:23:03] [Iter 1670/2250] R3[619/1200], Temp: 0.4751, Energy: -81.117994-0.003652j
[2025-09-08 21:23:48] [Iter 1671/2250] R3[620/1200], Temp: 0.4738, Energy: -81.083387-0.001494j
[2025-09-08 21:24:34] [Iter 1672/2250] R3[621/1200], Temp: 0.4725, Energy: -81.129155+0.001266j
[2025-09-08 21:25:19] [Iter 1673/2250] R3[622/1200], Temp: 0.4712, Energy: -81.129729-0.000710j
[2025-09-08 21:26:05] [Iter 1674/2250] R3[623/1200], Temp: 0.4699, Energy: -81.145540-0.001955j
[2025-09-08 21:26:50] [Iter 1675/2250] R3[624/1200], Temp: 0.4686, Energy: -81.113996-0.004732j
[2025-09-08 21:27:36] [Iter 1676/2250] R3[625/1200], Temp: 0.4673, Energy: -81.124468-0.001327j
[2025-09-08 21:28:21] [Iter 1677/2250] R3[626/1200], Temp: 0.4660, Energy: -81.190288+0.001345j
[2025-09-08 21:29:07] [Iter 1678/2250] R3[627/1200], Temp: 0.4647, Energy: -81.221900+0.000071j
[2025-09-08 21:29:52] [Iter 1679/2250] R3[628/1200], Temp: 0.4634, Energy: -81.173470-0.000280j
[2025-09-08 21:30:38] [Iter 1680/2250] R3[629/1200], Temp: 0.4621, Energy: -81.196789+0.000773j
[2025-09-08 21:31:23] [Iter 1681/2250] R3[630/1200], Temp: 0.4608, Energy: -81.296627-0.001097j
[2025-09-08 21:32:09] [Iter 1682/2250] R3[631/1200], Temp: 0.4595, Energy: -81.239992+0.001653j
[2025-09-08 21:32:54] [Iter 1683/2250] R3[632/1200], Temp: 0.4582, Energy: -81.252034-0.002714j
[2025-09-08 21:33:40] [Iter 1684/2250] R3[633/1200], Temp: 0.4569, Energy: -81.262288-0.001600j
[2025-09-08 21:34:25] [Iter 1685/2250] R3[634/1200], Temp: 0.4556, Energy: -81.287156+0.001647j
[2025-09-08 21:35:11] [Iter 1686/2250] R3[635/1200], Temp: 0.4542, Energy: -81.234100+0.003783j
[2025-09-08 21:35:56] [Iter 1687/2250] R3[636/1200], Temp: 0.4529, Energy: -81.227051-0.003206j
[2025-09-08 21:36:42] [Iter 1688/2250] R3[637/1200], Temp: 0.4516, Energy: -81.219767+0.000924j
[2025-09-08 21:37:27] [Iter 1689/2250] R3[638/1200], Temp: 0.4503, Energy: -81.176764+0.002194j
[2025-09-08 21:38:13] [Iter 1690/2250] R3[639/1200], Temp: 0.4490, Energy: -81.211639-0.001099j
[2025-09-08 21:38:58] [Iter 1691/2250] R3[640/1200], Temp: 0.4477, Energy: -81.252431+0.000195j
[2025-09-08 21:39:44] [Iter 1692/2250] R3[641/1200], Temp: 0.4464, Energy: -81.254978+0.003149j
[2025-09-08 21:40:29] [Iter 1693/2250] R3[642/1200], Temp: 0.4451, Energy: -81.228962+0.003298j
[2025-09-08 21:41:15] [Iter 1694/2250] R3[643/1200], Temp: 0.4438, Energy: -81.328452-0.001424j
[2025-09-08 21:42:00] [Iter 1695/2250] R3[644/1200], Temp: 0.4425, Energy: -81.227801+0.000342j
[2025-09-08 21:42:46] [Iter 1696/2250] R3[645/1200], Temp: 0.4412, Energy: -81.189295+0.000339j
[2025-09-08 21:43:31] [Iter 1697/2250] R3[646/1200], Temp: 0.4399, Energy: -81.176472-0.002040j
[2025-09-08 21:44:17] [Iter 1698/2250] R3[647/1200], Temp: 0.4386, Energy: -81.157023+0.000361j
[2025-09-08 21:45:02] [Iter 1699/2250] R3[648/1200], Temp: 0.4373, Energy: -81.204837+0.001399j
[2025-09-08 21:45:48] [Iter 1700/2250] R3[649/1200], Temp: 0.4360, Energy: -81.184082+0.001995j
[2025-09-08 21:46:34] [Iter 1701/2250] R3[650/1200], Temp: 0.4347, Energy: -81.178491-0.000885j
[2025-09-08 21:47:19] [Iter 1702/2250] R3[651/1200], Temp: 0.4334, Energy: -81.143056+0.000249j
[2025-09-08 21:48:05] [Iter 1703/2250] R3[652/1200], Temp: 0.4321, Energy: -81.147885-0.001335j
[2025-09-08 21:48:50] [Iter 1704/2250] R3[653/1200], Temp: 0.4308, Energy: -81.171842+0.000785j
[2025-09-08 21:49:36] [Iter 1705/2250] R3[654/1200], Temp: 0.4295, Energy: -81.142081-0.001345j
[2025-09-08 21:50:21] [Iter 1706/2250] R3[655/1200], Temp: 0.4283, Energy: -81.204726+0.001901j
[2025-09-08 21:51:07] [Iter 1707/2250] R3[656/1200], Temp: 0.4270, Energy: -81.158670-0.002076j
[2025-09-08 21:51:52] [Iter 1708/2250] R3[657/1200], Temp: 0.4257, Energy: -81.177791-0.000168j
[2025-09-08 21:52:38] [Iter 1709/2250] R3[658/1200], Temp: 0.4244, Energy: -81.171679-0.002445j
[2025-09-08 21:53:23] [Iter 1710/2250] R3[659/1200], Temp: 0.4231, Energy: -81.217710-0.000216j
[2025-09-08 21:54:09] [Iter 1711/2250] R3[660/1200], Temp: 0.4218, Energy: -81.244510-0.000088j
[2025-09-08 21:54:54] [Iter 1712/2250] R3[661/1200], Temp: 0.4205, Energy: -81.214620+0.004106j
[2025-09-08 21:55:40] [Iter 1713/2250] R3[662/1200], Temp: 0.4192, Energy: -81.211442+0.001429j
[2025-09-08 21:56:25] [Iter 1714/2250] R3[663/1200], Temp: 0.4179, Energy: -81.238181+0.001802j
[2025-09-08 21:57:11] [Iter 1715/2250] R3[664/1200], Temp: 0.4166, Energy: -81.337594+0.001108j
[2025-09-08 21:57:56] [Iter 1716/2250] R3[665/1200], Temp: 0.4153, Energy: -81.231416+0.000298j
[2025-09-08 21:58:42] [Iter 1717/2250] R3[666/1200], Temp: 0.4140, Energy: -81.283257+0.000116j
[2025-09-08 21:59:27] [Iter 1718/2250] R3[667/1200], Temp: 0.4127, Energy: -81.283819-0.000612j
[2025-09-08 22:00:13] [Iter 1719/2250] R3[668/1200], Temp: 0.4115, Energy: -81.269450-0.000341j
[2025-09-08 22:00:59] [Iter 1720/2250] R3[669/1200], Temp: 0.4102, Energy: -81.292689-0.000555j
[2025-09-08 22:01:44] [Iter 1721/2250] R3[670/1200], Temp: 0.4089, Energy: -81.228822+0.001113j
[2025-09-08 22:02:30] [Iter 1722/2250] R3[671/1200], Temp: 0.4076, Energy: -81.298796-0.000122j
[2025-09-08 22:03:15] [Iter 1723/2250] R3[672/1200], Temp: 0.4063, Energy: -81.229036+0.000499j
[2025-09-08 22:04:01] [Iter 1724/2250] R3[673/1200], Temp: 0.4050, Energy: -81.221942+0.001704j
[2025-09-08 22:04:46] [Iter 1725/2250] R3[674/1200], Temp: 0.4037, Energy: -81.208441-0.000514j
[2025-09-08 22:05:32] [Iter 1726/2250] R3[675/1200], Temp: 0.4025, Energy: -81.200614-0.000993j
[2025-09-08 22:06:17] [Iter 1727/2250] R3[676/1200], Temp: 0.4012, Energy: -81.219509+0.001295j
[2025-09-08 22:07:03] [Iter 1728/2250] R3[677/1200], Temp: 0.3999, Energy: -81.234269+0.000584j
[2025-09-08 22:07:48] [Iter 1729/2250] R3[678/1200], Temp: 0.3986, Energy: -81.264023+0.001527j
[2025-09-08 22:08:34] [Iter 1730/2250] R3[679/1200], Temp: 0.3973, Energy: -81.177281+0.001144j
[2025-09-08 22:09:20] [Iter 1731/2250] R3[680/1200], Temp: 0.3960, Energy: -81.239209+0.001503j
[2025-09-08 22:10:05] [Iter 1732/2250] R3[681/1200], Temp: 0.3948, Energy: -81.307690+0.005828j
[2025-09-08 22:10:51] [Iter 1733/2250] R3[682/1200], Temp: 0.3935, Energy: -81.208445-0.000712j
[2025-09-08 22:11:36] [Iter 1734/2250] R3[683/1200], Temp: 0.3922, Energy: -81.268517-0.001291j
[2025-09-08 22:12:22] [Iter 1735/2250] R3[684/1200], Temp: 0.3909, Energy: -81.246938+0.001728j
[2025-09-08 22:13:07] [Iter 1736/2250] R3[685/1200], Temp: 0.3897, Energy: -81.211682+0.001117j
[2025-09-08 22:13:53] [Iter 1737/2250] R3[686/1200], Temp: 0.3884, Energy: -81.184283-0.001100j
[2025-09-08 22:14:38] [Iter 1738/2250] R3[687/1200], Temp: 0.3871, Energy: -81.205785-0.001327j
[2025-09-08 22:15:24] [Iter 1739/2250] R3[688/1200], Temp: 0.3858, Energy: -81.145439+0.004327j
[2025-09-08 22:16:09] [Iter 1740/2250] R3[689/1200], Temp: 0.3846, Energy: -81.198841-0.002155j
[2025-09-08 22:16:55] [Iter 1741/2250] R3[690/1200], Temp: 0.3833, Energy: -81.233152-0.000043j
[2025-09-08 22:17:40] [Iter 1742/2250] R3[691/1200], Temp: 0.3820, Energy: -81.191334-0.001542j
[2025-09-08 22:18:26] [Iter 1743/2250] R3[692/1200], Temp: 0.3807, Energy: -81.205942-0.001505j
[2025-09-08 22:19:11] [Iter 1744/2250] R3[693/1200], Temp: 0.3795, Energy: -81.176955-0.001781j
[2025-09-08 22:19:57] [Iter 1745/2250] R3[694/1200], Temp: 0.3782, Energy: -81.205521+0.001303j
[2025-09-08 22:20:42] [Iter 1746/2250] R3[695/1200], Temp: 0.3769, Energy: -81.317980+0.002686j
[2025-09-08 22:21:28] [Iter 1747/2250] R3[696/1200], Temp: 0.3757, Energy: -81.244210+0.000225j
[2025-09-08 22:22:13] [Iter 1748/2250] R3[697/1200], Temp: 0.3744, Energy: -81.261013-0.001110j
[2025-09-08 22:22:59] [Iter 1749/2250] R3[698/1200], Temp: 0.3731, Energy: -81.278367-0.002023j
[2025-09-08 22:23:44] [Iter 1750/2250] R3[699/1200], Temp: 0.3719, Energy: -81.369483-0.006540j
[2025-09-08 22:23:44] ✓ Checkpoint saved: checkpoint_iter_001750.pkl
[2025-09-08 22:24:30] [Iter 1751/2250] R3[700/1200], Temp: 0.3706, Energy: -81.276673-0.001063j
[2025-09-08 22:25:15] [Iter 1752/2250] R3[701/1200], Temp: 0.3693, Energy: -81.284392+0.001612j
[2025-09-08 22:26:01] [Iter 1753/2250] R3[702/1200], Temp: 0.3681, Energy: -81.307532+0.000969j
[2025-09-08 22:26:47] [Iter 1754/2250] R3[703/1200], Temp: 0.3668, Energy: -81.186435+0.002031j
[2025-09-08 22:27:32] [Iter 1755/2250] R3[704/1200], Temp: 0.3655, Energy: -81.166597-0.002008j
[2025-09-08 22:28:18] [Iter 1756/2250] R3[705/1200], Temp: 0.3643, Energy: -81.080698+0.004076j
[2025-09-08 22:29:03] [Iter 1757/2250] R3[706/1200], Temp: 0.3630, Energy: -81.145480+0.001034j
[2025-09-08 22:29:48] [Iter 1758/2250] R3[707/1200], Temp: 0.3618, Energy: -81.120717+0.000491j
[2025-09-08 22:30:34] [Iter 1759/2250] R3[708/1200], Temp: 0.3605, Energy: -81.116779+0.003939j
[2025-09-08 22:31:19] [Iter 1760/2250] R3[709/1200], Temp: 0.3592, Energy: -81.171021-0.001543j
[2025-09-08 22:32:05] [Iter 1761/2250] R3[710/1200], Temp: 0.3580, Energy: -81.085298+0.002591j
[2025-09-08 22:32:50] [Iter 1762/2250] R3[711/1200], Temp: 0.3567, Energy: -81.079911-0.002183j
[2025-09-08 22:33:36] [Iter 1763/2250] R3[712/1200], Temp: 0.3555, Energy: -81.111601+0.001486j
[2025-09-08 22:34:22] [Iter 1764/2250] R3[713/1200], Temp: 0.3542, Energy: -81.184040-0.002700j
[2025-09-08 22:35:07] [Iter 1765/2250] R3[714/1200], Temp: 0.3530, Energy: -81.210786+0.000427j
[2025-09-08 22:35:53] [Iter 1766/2250] R3[715/1200], Temp: 0.3517, Energy: -81.152882+0.000365j
[2025-09-08 22:36:38] [Iter 1767/2250] R3[716/1200], Temp: 0.3505, Energy: -81.243967-0.000925j
[2025-09-08 22:37:24] [Iter 1768/2250] R3[717/1200], Temp: 0.3492, Energy: -81.180254+0.002011j
[2025-09-08 22:38:09] [Iter 1769/2250] R3[718/1200], Temp: 0.3480, Energy: -81.159805+0.000106j
[2025-09-08 22:38:55] [Iter 1770/2250] R3[719/1200], Temp: 0.3467, Energy: -81.181453-0.001810j
[2025-09-08 22:39:40] [Iter 1771/2250] R3[720/1200], Temp: 0.3455, Energy: -81.134260-0.003298j
[2025-09-08 22:40:26] [Iter 1772/2250] R3[721/1200], Temp: 0.3442, Energy: -81.198481+0.002694j
[2025-09-08 22:41:12] [Iter 1773/2250] R3[722/1200], Temp: 0.3430, Energy: -81.196814-0.000715j
[2025-09-08 22:41:57] [Iter 1774/2250] R3[723/1200], Temp: 0.3418, Energy: -81.201324-0.000215j
[2025-09-08 22:42:43] [Iter 1775/2250] R3[724/1200], Temp: 0.3405, Energy: -81.213788+0.000984j
[2025-09-08 22:43:28] [Iter 1776/2250] R3[725/1200], Temp: 0.3393, Energy: -81.205177-0.000038j
[2025-09-08 22:44:14] [Iter 1777/2250] R3[726/1200], Temp: 0.3380, Energy: -81.162281-0.002624j
[2025-09-08 22:44:59] [Iter 1778/2250] R3[727/1200], Temp: 0.3368, Energy: -81.200236+0.000880j
[2025-09-08 22:45:45] [Iter 1779/2250] R3[728/1200], Temp: 0.3356, Energy: -81.174678-0.000094j
[2025-09-08 22:46:30] [Iter 1780/2250] R3[729/1200], Temp: 0.3343, Energy: -81.205967-0.001100j
[2025-09-08 22:47:16] [Iter 1781/2250] R3[730/1200], Temp: 0.3331, Energy: -81.095708-0.000905j
[2025-09-08 22:48:01] [Iter 1782/2250] R3[731/1200], Temp: 0.3319, Energy: -81.066689+0.001728j
[2025-09-08 22:48:47] [Iter 1783/2250] R3[732/1200], Temp: 0.3306, Energy: -81.096017+0.000031j
[2025-09-08 22:49:32] [Iter 1784/2250] R3[733/1200], Temp: 0.3294, Energy: -81.117224-0.000596j
[2025-09-08 22:50:18] [Iter 1785/2250] R3[734/1200], Temp: 0.3282, Energy: -81.151115-0.000074j
[2025-09-08 22:51:03] [Iter 1786/2250] R3[735/1200], Temp: 0.3269, Energy: -81.133411-0.000064j
[2025-09-08 22:51:49] [Iter 1787/2250] R3[736/1200], Temp: 0.3257, Energy: -81.142871-0.001215j
[2025-09-08 22:52:34] [Iter 1788/2250] R3[737/1200], Temp: 0.3245, Energy: -81.134655+0.001462j
[2025-09-08 22:53:20] [Iter 1789/2250] R3[738/1200], Temp: 0.3233, Energy: -81.182297-0.001264j
[2025-09-08 22:54:05] [Iter 1790/2250] R3[739/1200], Temp: 0.3220, Energy: -81.139937-0.000365j
[2025-09-08 22:54:51] [Iter 1791/2250] R3[740/1200], Temp: 0.3208, Energy: -81.147062+0.000247j
[2025-09-08 22:55:36] [Iter 1792/2250] R3[741/1200], Temp: 0.3196, Energy: -81.230019-0.001988j
[2025-09-08 22:56:22] [Iter 1793/2250] R3[742/1200], Temp: 0.3184, Energy: -81.166356+0.000329j
[2025-09-08 22:57:07] [Iter 1794/2250] R3[743/1200], Temp: 0.3172, Energy: -81.242171-0.001382j
[2025-09-08 22:57:53] [Iter 1795/2250] R3[744/1200], Temp: 0.3159, Energy: -81.272252-0.000842j
[2025-09-08 22:58:38] [Iter 1796/2250] R3[745/1200], Temp: 0.3147, Energy: -81.259387-0.000553j
[2025-09-08 22:59:24] [Iter 1797/2250] R3[746/1200], Temp: 0.3135, Energy: -81.222040+0.002808j
[2025-09-08 23:00:09] [Iter 1798/2250] R3[747/1200], Temp: 0.3123, Energy: -81.246890-0.000343j
[2025-09-08 23:00:55] [Iter 1799/2250] R3[748/1200], Temp: 0.3111, Energy: -81.226820+0.000955j
[2025-09-08 23:01:40] [Iter 1800/2250] R3[749/1200], Temp: 0.3099, Energy: -81.262691-0.003242j
[2025-09-08 23:02:26] [Iter 1801/2250] R3[750/1200], Temp: 0.3087, Energy: -81.209166+0.002166j
[2025-09-08 23:03:11] [Iter 1802/2250] R3[751/1200], Temp: 0.3074, Energy: -81.236842-0.001573j
[2025-09-08 23:03:57] [Iter 1803/2250] R3[752/1200], Temp: 0.3062, Energy: -81.215606-0.003868j
[2025-09-08 23:04:43] [Iter 1804/2250] R3[753/1200], Temp: 0.3050, Energy: -81.222174+0.000639j
[2025-09-08 23:05:28] [Iter 1805/2250] R3[754/1200], Temp: 0.3038, Energy: -81.193525-0.000478j
[2025-09-08 23:06:13] [Iter 1806/2250] R3[755/1200], Temp: 0.3026, Energy: -81.158220-0.000046j
[2025-09-08 23:06:59] [Iter 1807/2250] R3[756/1200], Temp: 0.3014, Energy: -81.178325+0.000256j
[2025-09-08 23:07:45] [Iter 1808/2250] R3[757/1200], Temp: 0.3002, Energy: -81.118464+0.000663j
[2025-09-08 23:08:30] [Iter 1809/2250] R3[758/1200], Temp: 0.2990, Energy: -81.175097+0.001357j
[2025-09-08 23:09:16] [Iter 1810/2250] R3[759/1200], Temp: 0.2978, Energy: -81.176880-0.000714j
[2025-09-08 23:10:01] [Iter 1811/2250] R3[760/1200], Temp: 0.2966, Energy: -81.204978+0.000349j
[2025-09-08 23:10:47] [Iter 1812/2250] R3[761/1200], Temp: 0.2954, Energy: -81.291155+0.001412j
[2025-09-08 23:11:32] [Iter 1813/2250] R3[762/1200], Temp: 0.2942, Energy: -81.208529+0.001305j
[2025-09-08 23:12:18] [Iter 1814/2250] R3[763/1200], Temp: 0.2931, Energy: -81.190424+0.001569j
[2025-09-08 23:13:03] [Iter 1815/2250] R3[764/1200], Temp: 0.2919, Energy: -81.175373-0.002690j
[2025-09-08 23:13:49] [Iter 1816/2250] R3[765/1200], Temp: 0.2907, Energy: -81.184974+0.002767j
[2025-09-08 23:14:35] [Iter 1817/2250] R3[766/1200], Temp: 0.2895, Energy: -81.202152-0.000196j
[2025-09-08 23:15:20] [Iter 1818/2250] R3[767/1200], Temp: 0.2883, Energy: -81.172441+0.002386j
[2025-09-08 23:16:06] [Iter 1819/2250] R3[768/1200], Temp: 0.2871, Energy: -81.198773+0.001559j
[2025-09-08 23:16:51] [Iter 1820/2250] R3[769/1200], Temp: 0.2859, Energy: -81.204342-0.003024j
[2025-09-08 23:17:37] [Iter 1821/2250] R3[770/1200], Temp: 0.2847, Energy: -81.179990-0.000029j
[2025-09-08 23:18:22] [Iter 1822/2250] R3[771/1200], Temp: 0.2836, Energy: -81.167131-0.000383j
[2025-09-08 23:19:08] [Iter 1823/2250] R3[772/1200], Temp: 0.2824, Energy: -81.163313-0.001308j
[2025-09-08 23:19:53] [Iter 1824/2250] R3[773/1200], Temp: 0.2812, Energy: -81.194890+0.002303j
[2025-09-08 23:20:39] [Iter 1825/2250] R3[774/1200], Temp: 0.2800, Energy: -81.248439+0.001652j
[2025-09-08 23:21:24] [Iter 1826/2250] R3[775/1200], Temp: 0.2789, Energy: -81.239656-0.000127j
[2025-09-08 23:22:10] [Iter 1827/2250] R3[776/1200], Temp: 0.2777, Energy: -81.201912+0.000055j
[2025-09-08 23:22:55] [Iter 1828/2250] R3[777/1200], Temp: 0.2765, Energy: -81.191930-0.000859j
[2025-09-08 23:23:41] [Iter 1829/2250] R3[778/1200], Temp: 0.2753, Energy: -81.197909+0.000546j
[2025-09-08 23:24:26] [Iter 1830/2250] R3[779/1200], Temp: 0.2742, Energy: -81.237631-0.002079j
[2025-09-08 23:25:12] [Iter 1831/2250] R3[780/1200], Temp: 0.2730, Energy: -81.186452-0.002077j
[2025-09-08 23:25:57] [Iter 1832/2250] R3[781/1200], Temp: 0.2718, Energy: -81.130823+0.000206j
[2025-09-08 23:26:43] [Iter 1833/2250] R3[782/1200], Temp: 0.2707, Energy: -81.129254-0.001080j
[2025-09-08 23:27:28] [Iter 1834/2250] R3[783/1200], Temp: 0.2695, Energy: -81.223054+0.001038j
[2025-09-08 23:28:14] [Iter 1835/2250] R3[784/1200], Temp: 0.2684, Energy: -81.200812-0.002028j
[2025-09-08 23:28:59] [Iter 1836/2250] R3[785/1200], Temp: 0.2672, Energy: -81.108504+0.001873j
[2025-09-08 23:29:45] [Iter 1837/2250] R3[786/1200], Temp: 0.2660, Energy: -81.113720+0.000389j
[2025-09-08 23:30:30] [Iter 1838/2250] R3[787/1200], Temp: 0.2649, Energy: -81.134069-0.000153j
[2025-09-08 23:31:16] [Iter 1839/2250] R3[788/1200], Temp: 0.2637, Energy: -81.184119-0.001168j
[2025-09-08 23:32:01] [Iter 1840/2250] R3[789/1200], Temp: 0.2626, Energy: -81.130704+0.002992j
[2025-09-08 23:32:47] [Iter 1841/2250] R3[790/1200], Temp: 0.2614, Energy: -81.118981-0.001268j
[2025-09-08 23:33:32] [Iter 1842/2250] R3[791/1200], Temp: 0.2603, Energy: -81.188444+0.000556j
[2025-09-08 23:34:18] [Iter 1843/2250] R3[792/1200], Temp: 0.2591, Energy: -81.171188+0.000563j
[2025-09-08 23:35:03] [Iter 1844/2250] R3[793/1200], Temp: 0.2580, Energy: -81.153111+0.001019j
[2025-09-08 23:35:49] [Iter 1845/2250] R3[794/1200], Temp: 0.2568, Energy: -81.187267-0.002595j
[2025-09-08 23:36:35] [Iter 1846/2250] R3[795/1200], Temp: 0.2557, Energy: -81.157563-0.002117j
[2025-09-08 23:37:20] [Iter 1847/2250] R3[796/1200], Temp: 0.2545, Energy: -81.237603-0.001303j
[2025-09-08 23:38:05] [Iter 1848/2250] R3[797/1200], Temp: 0.2534, Energy: -81.204369-0.000860j
[2025-09-08 23:38:51] [Iter 1849/2250] R3[798/1200], Temp: 0.2523, Energy: -81.215673+0.003304j
[2025-09-08 23:39:37] [Iter 1850/2250] R3[799/1200], Temp: 0.2511, Energy: -81.288979+0.001417j
[2025-09-08 23:40:22] [Iter 1851/2250] R3[800/1200], Temp: 0.2500, Energy: -81.263152-0.001663j
[2025-09-08 23:41:07] [Iter 1852/2250] R3[801/1200], Temp: 0.2489, Energy: -81.301859-0.000949j
[2025-09-08 23:41:53] [Iter 1853/2250] R3[802/1200], Temp: 0.2477, Energy: -81.289935-0.001867j
[2025-09-08 23:42:39] [Iter 1854/2250] R3[803/1200], Temp: 0.2466, Energy: -81.219160-0.001986j
[2025-09-08 23:43:24] [Iter 1855/2250] R3[804/1200], Temp: 0.2455, Energy: -81.183425-0.000521j
[2025-09-08 23:44:10] [Iter 1856/2250] R3[805/1200], Temp: 0.2444, Energy: -81.276984-0.001631j
[2025-09-08 23:44:55] [Iter 1857/2250] R3[806/1200], Temp: 0.2432, Energy: -81.318796+0.000805j
[2025-09-08 23:45:41] [Iter 1858/2250] R3[807/1200], Temp: 0.2421, Energy: -81.193026-0.001600j
[2025-09-08 23:46:26] [Iter 1859/2250] R3[808/1200], Temp: 0.2410, Energy: -81.170794-0.001149j
[2025-09-08 23:47:12] [Iter 1860/2250] R3[809/1200], Temp: 0.2399, Energy: -81.279116+0.002048j
[2025-09-08 23:47:57] [Iter 1861/2250] R3[810/1200], Temp: 0.2388, Energy: -81.288394+0.001150j
[2025-09-08 23:48:43] [Iter 1862/2250] R3[811/1200], Temp: 0.2376, Energy: -81.197794+0.000239j
[2025-09-08 23:49:28] [Iter 1863/2250] R3[812/1200], Temp: 0.2365, Energy: -81.182174-0.000773j
[2025-09-08 23:50:14] [Iter 1864/2250] R3[813/1200], Temp: 0.2354, Energy: -81.233227+0.002985j
[2025-09-08 23:50:59] [Iter 1865/2250] R3[814/1200], Temp: 0.2343, Energy: -81.192082+0.001666j
[2025-09-08 23:51:45] [Iter 1866/2250] R3[815/1200], Temp: 0.2332, Energy: -81.214535-0.002861j
[2025-09-08 23:52:30] [Iter 1867/2250] R3[816/1200], Temp: 0.2321, Energy: -81.193840-0.001020j
[2025-09-08 23:53:16] [Iter 1868/2250] R3[817/1200], Temp: 0.2310, Energy: -81.214636+0.001345j
[2025-09-08 23:54:01] [Iter 1869/2250] R3[818/1200], Temp: 0.2299, Energy: -81.250295+0.001174j
[2025-09-08 23:54:47] [Iter 1870/2250] R3[819/1200], Temp: 0.2288, Energy: -81.209657-0.000050j
[2025-09-08 23:55:33] [Iter 1871/2250] R3[820/1200], Temp: 0.2277, Energy: -81.207441+0.000023j
[2025-09-08 23:56:18] [Iter 1872/2250] R3[821/1200], Temp: 0.2266, Energy: -81.268635+0.000512j
[2025-09-08 23:57:04] [Iter 1873/2250] R3[822/1200], Temp: 0.2255, Energy: -81.219490-0.003342j
[2025-09-08 23:57:49] [Iter 1874/2250] R3[823/1200], Temp: 0.2244, Energy: -81.185102+0.001330j
[2025-09-08 23:58:35] [Iter 1875/2250] R3[824/1200], Temp: 0.2233, Energy: -81.210806+0.001340j
[2025-09-08 23:59:20] [Iter 1876/2250] R3[825/1200], Temp: 0.2222, Energy: -81.178827+0.000806j
[2025-09-09 00:00:06] [Iter 1877/2250] R3[826/1200], Temp: 0.2211, Energy: -81.116811+0.006963j
[2025-09-09 00:00:51] [Iter 1878/2250] R3[827/1200], Temp: 0.2200, Energy: -81.079938-0.001379j
[2025-09-09 00:01:37] [Iter 1879/2250] R3[828/1200], Temp: 0.2190, Energy: -81.187948-0.003798j
[2025-09-09 00:02:22] [Iter 1880/2250] R3[829/1200], Temp: 0.2179, Energy: -81.100163-0.002033j
[2025-09-09 00:03:08] [Iter 1881/2250] R3[830/1200], Temp: 0.2168, Energy: -81.202084-0.002030j
[2025-09-09 00:03:53] [Iter 1882/2250] R3[831/1200], Temp: 0.2157, Energy: -81.174218+0.002040j
[2025-09-09 00:04:39] [Iter 1883/2250] R3[832/1200], Temp: 0.2146, Energy: -81.115563+0.002511j
[2025-09-09 00:05:24] [Iter 1884/2250] R3[833/1200], Temp: 0.2136, Energy: -81.113532-0.000780j
[2025-09-09 00:06:10] [Iter 1885/2250] R3[834/1200], Temp: 0.2125, Energy: -81.158500+0.002115j
[2025-09-09 00:06:55] [Iter 1886/2250] R3[835/1200], Temp: 0.2114, Energy: -81.098790-0.000357j
[2025-09-09 00:07:41] [Iter 1887/2250] R3[836/1200], Temp: 0.2104, Energy: -81.181116+0.000852j
[2025-09-09 00:08:26] [Iter 1888/2250] R3[837/1200], Temp: 0.2093, Energy: -81.128878+0.001365j
[2025-09-09 00:09:12] [Iter 1889/2250] R3[838/1200], Temp: 0.2082, Energy: -81.150833+0.004558j
[2025-09-09 00:09:57] [Iter 1890/2250] R3[839/1200], Temp: 0.2072, Energy: -81.184522+0.000927j
[2025-09-09 00:10:44] [Iter 1891/2250] R3[840/1200], Temp: 0.2061, Energy: -81.228451-0.001124j
[2025-09-09 00:11:29] [Iter 1892/2250] R3[841/1200], Temp: 0.2050, Energy: -81.134172-0.001026j
[2025-09-09 00:12:15] [Iter 1893/2250] R3[842/1200], Temp: 0.2040, Energy: -81.139457+0.003520j
[2025-09-09 00:13:00] [Iter 1894/2250] R3[843/1200], Temp: 0.2029, Energy: -81.092693-0.002437j
[2025-09-09 00:13:46] [Iter 1895/2250] R3[844/1200], Temp: 0.2019, Energy: -81.176263-0.001462j
[2025-09-09 00:14:31] [Iter 1896/2250] R3[845/1200], Temp: 0.2008, Energy: -81.209004+0.000102j
[2025-09-09 00:15:17] [Iter 1897/2250] R3[846/1200], Temp: 0.1998, Energy: -81.196085-0.001926j
[2025-09-09 00:16:02] [Iter 1898/2250] R3[847/1200], Temp: 0.1987, Energy: -81.214035+0.000728j
[2025-09-09 00:16:48] [Iter 1899/2250] R3[848/1200], Temp: 0.1977, Energy: -81.198104+0.002500j
[2025-09-09 00:17:33] [Iter 1900/2250] R3[849/1200], Temp: 0.1967, Energy: -81.214396-0.000049j
[2025-09-09 00:18:19] [Iter 1901/2250] R3[850/1200], Temp: 0.1956, Energy: -81.210747+0.003143j
[2025-09-09 00:19:05] [Iter 1902/2250] R3[851/1200], Temp: 0.1946, Energy: -81.157202-0.002510j
[2025-09-09 00:19:50] [Iter 1903/2250] R3[852/1200], Temp: 0.1935, Energy: -81.038667-0.000233j
[2025-09-09 00:20:36] [Iter 1904/2250] R3[853/1200], Temp: 0.1925, Energy: -81.130883-0.001536j
[2025-09-09 00:21:21] [Iter 1905/2250] R3[854/1200], Temp: 0.1915, Energy: -81.161790-0.001241j
[2025-09-09 00:22:07] [Iter 1906/2250] R3[855/1200], Temp: 0.1905, Energy: -81.090187+0.000726j
[2025-09-09 00:22:52] [Iter 1907/2250] R3[856/1200], Temp: 0.1894, Energy: -81.123896+0.001355j
[2025-09-09 00:23:38] [Iter 1908/2250] R3[857/1200], Temp: 0.1884, Energy: -81.107778-0.002306j
[2025-09-09 00:24:23] [Iter 1909/2250] R3[858/1200], Temp: 0.1874, Energy: -81.130908-0.000755j
[2025-09-09 00:25:09] [Iter 1910/2250] R3[859/1200], Temp: 0.1864, Energy: -81.131192+0.000818j
[2025-09-09 00:25:55] [Iter 1911/2250] R3[860/1200], Temp: 0.1853, Energy: -81.171886+0.001188j
[2025-09-09 00:26:40] [Iter 1912/2250] R3[861/1200], Temp: 0.1843, Energy: -81.111299+0.000298j
[2025-09-09 00:27:26] [Iter 1913/2250] R3[862/1200], Temp: 0.1833, Energy: -81.182819+0.001930j
[2025-09-09 00:28:11] [Iter 1914/2250] R3[863/1200], Temp: 0.1823, Energy: -81.169580+0.000723j
[2025-09-09 00:28:57] [Iter 1915/2250] R3[864/1200], Temp: 0.1813, Energy: -81.207008-0.000132j
[2025-09-09 00:29:42] [Iter 1916/2250] R3[865/1200], Temp: 0.1803, Energy: -81.183354+0.001138j
[2025-09-09 00:30:28] [Iter 1917/2250] R3[866/1200], Temp: 0.1793, Energy: -81.234167+0.002136j
[2025-09-09 00:31:13] [Iter 1918/2250] R3[867/1200], Temp: 0.1783, Energy: -81.132183-0.000003j
[2025-09-09 00:31:59] [Iter 1919/2250] R3[868/1200], Temp: 0.1773, Energy: -81.149193-0.000674j
[2025-09-09 00:32:45] [Iter 1920/2250] R3[869/1200], Temp: 0.1763, Energy: -81.160205+0.003135j
[2025-09-09 00:33:30] [Iter 1921/2250] R3[870/1200], Temp: 0.1753, Energy: -81.156703-0.001393j
[2025-09-09 00:34:16] [Iter 1922/2250] R3[871/1200], Temp: 0.1743, Energy: -81.159146+0.000837j
[2025-09-09 00:35:01] [Iter 1923/2250] R3[872/1200], Temp: 0.1733, Energy: -81.143370-0.000502j
[2025-09-09 00:35:47] [Iter 1924/2250] R3[873/1200], Temp: 0.1723, Energy: -81.230110-0.000284j
[2025-09-09 00:36:32] [Iter 1925/2250] R3[874/1200], Temp: 0.1713, Energy: -81.255653+0.001373j
[2025-09-09 00:37:18] [Iter 1926/2250] R3[875/1200], Temp: 0.1703, Energy: -81.212003-0.000127j
[2025-09-09 00:38:03] [Iter 1927/2250] R3[876/1200], Temp: 0.1693, Energy: -81.290954+0.002568j
[2025-09-09 00:38:49] [Iter 1928/2250] R3[877/1200], Temp: 0.1684, Energy: -81.277668+0.002444j
[2025-09-09 00:39:34] [Iter 1929/2250] R3[878/1200], Temp: 0.1674, Energy: -81.249470-0.001076j
[2025-09-09 00:40:20] [Iter 1930/2250] R3[879/1200], Temp: 0.1664, Energy: -81.145406+0.000508j
[2025-09-09 00:41:05] [Iter 1931/2250] R3[880/1200], Temp: 0.1654, Energy: -81.235497-0.000757j
[2025-09-09 00:41:51] [Iter 1932/2250] R3[881/1200], Temp: 0.1645, Energy: -81.241536+0.004688j
[2025-09-09 00:42:36] [Iter 1933/2250] R3[882/1200], Temp: 0.1635, Energy: -81.237400-0.000093j
[2025-09-09 00:43:22] [Iter 1934/2250] R3[883/1200], Temp: 0.1625, Energy: -81.283154-0.001642j
[2025-09-09 00:44:07] [Iter 1935/2250] R3[884/1200], Temp: 0.1616, Energy: -81.225640-0.001161j
[2025-09-09 00:44:53] [Iter 1936/2250] R3[885/1200], Temp: 0.1606, Energy: -81.182477-0.002520j
[2025-09-09 00:45:38] [Iter 1937/2250] R3[886/1200], Temp: 0.1596, Energy: -81.133322-0.001839j
[2025-09-09 00:46:24] [Iter 1938/2250] R3[887/1200], Temp: 0.1587, Energy: -81.123218-0.000435j
[2025-09-09 00:47:09] [Iter 1939/2250] R3[888/1200], Temp: 0.1577, Energy: -81.147439-0.001085j
[2025-09-09 00:47:55] [Iter 1940/2250] R3[889/1200], Temp: 0.1568, Energy: -81.117054+0.000778j
[2025-09-09 00:48:40] [Iter 1941/2250] R3[890/1200], Temp: 0.1558, Energy: -81.181552+0.000473j
[2025-09-09 00:49:26] [Iter 1942/2250] R3[891/1200], Temp: 0.1549, Energy: -81.120349+0.001897j
[2025-09-09 00:50:11] [Iter 1943/2250] R3[892/1200], Temp: 0.1539, Energy: -81.169696+0.000932j
[2025-09-09 00:50:57] [Iter 1944/2250] R3[893/1200], Temp: 0.1530, Energy: -81.166491-0.000039j
[2025-09-09 00:51:42] [Iter 1945/2250] R3[894/1200], Temp: 0.1520, Energy: -81.168672+0.000512j
[2025-09-09 00:52:28] [Iter 1946/2250] R3[895/1200], Temp: 0.1511, Energy: -81.054485+0.000097j
[2025-09-09 00:53:13] [Iter 1947/2250] R3[896/1200], Temp: 0.1502, Energy: -81.065461-0.000300j
[2025-09-09 00:53:59] [Iter 1948/2250] R3[897/1200], Temp: 0.1492, Energy: -81.089256-0.000326j
[2025-09-09 00:54:44] [Iter 1949/2250] R3[898/1200], Temp: 0.1483, Energy: -81.178477-0.001410j
[2025-09-09 00:55:30] [Iter 1950/2250] R3[899/1200], Temp: 0.1474, Energy: -81.182428-0.002655j
[2025-09-09 00:56:16] [Iter 1951/2250] R3[900/1200], Temp: 0.1464, Energy: -81.121913+0.000960j
[2025-09-09 00:57:01] [Iter 1952/2250] R3[901/1200], Temp: 0.1455, Energy: -81.073192+0.001028j
[2025-09-09 00:57:47] [Iter 1953/2250] R3[902/1200], Temp: 0.1446, Energy: -81.086097+0.001157j
[2025-09-09 00:58:32] [Iter 1954/2250] R3[903/1200], Temp: 0.1437, Energy: -81.096841-0.001285j
[2025-09-09 00:59:18] [Iter 1955/2250] R3[904/1200], Temp: 0.1428, Energy: -81.126635-0.000421j
[2025-09-09 01:00:03] [Iter 1956/2250] R3[905/1200], Temp: 0.1418, Energy: -81.087570-0.001087j
[2025-09-09 01:00:49] [Iter 1957/2250] R3[906/1200], Temp: 0.1409, Energy: -81.185794+0.001116j
[2025-09-09 01:01:35] [Iter 1958/2250] R3[907/1200], Temp: 0.1400, Energy: -81.151471+0.002661j
[2025-09-09 01:02:20] [Iter 1959/2250] R3[908/1200], Temp: 0.1391, Energy: -81.209690+0.001411j
[2025-09-09 01:03:06] [Iter 1960/2250] R3[909/1200], Temp: 0.1382, Energy: -81.183861-0.001237j
[2025-09-09 01:03:51] [Iter 1961/2250] R3[910/1200], Temp: 0.1373, Energy: -81.153889-0.001117j
[2025-09-09 01:04:37] [Iter 1962/2250] R3[911/1200], Temp: 0.1364, Energy: -81.172463+0.002071j
[2025-09-09 01:05:22] [Iter 1963/2250] R3[912/1200], Temp: 0.1355, Energy: -81.147607+0.002958j
[2025-09-09 01:06:08] [Iter 1964/2250] R3[913/1200], Temp: 0.1346, Energy: -81.235293+0.000488j
[2025-09-09 01:06:53] [Iter 1965/2250] R3[914/1200], Temp: 0.1337, Energy: -81.120389-0.001580j
[2025-09-09 01:07:39] [Iter 1966/2250] R3[915/1200], Temp: 0.1328, Energy: -81.218611+0.000535j
[2025-09-09 01:08:24] [Iter 1967/2250] R3[916/1200], Temp: 0.1320, Energy: -81.227142+0.000696j
[2025-09-09 01:09:10] [Iter 1968/2250] R3[917/1200], Temp: 0.1311, Energy: -81.167196-0.002904j
[2025-09-09 01:09:55] [Iter 1969/2250] R3[918/1200], Temp: 0.1302, Energy: -81.226103+0.000903j
[2025-09-09 01:10:41] [Iter 1970/2250] R3[919/1200], Temp: 0.1293, Energy: -81.222929+0.000356j
[2025-09-09 01:11:27] [Iter 1971/2250] R3[920/1200], Temp: 0.1284, Energy: -81.193081-0.001825j
[2025-09-09 01:12:12] [Iter 1972/2250] R3[921/1200], Temp: 0.1276, Energy: -81.157749-0.001085j
[2025-09-09 01:12:58] [Iter 1973/2250] R3[922/1200], Temp: 0.1267, Energy: -81.180087-0.002123j
[2025-09-09 01:13:43] [Iter 1974/2250] R3[923/1200], Temp: 0.1258, Energy: -81.226551+0.001407j
[2025-09-09 01:14:29] [Iter 1975/2250] R3[924/1200], Temp: 0.1249, Energy: -81.163219+0.001637j
[2025-09-09 01:15:14] [Iter 1976/2250] R3[925/1200], Temp: 0.1241, Energy: -81.271768+0.001059j
[2025-09-09 01:16:00] [Iter 1977/2250] R3[926/1200], Temp: 0.1232, Energy: -81.239004-0.000976j
[2025-09-09 01:16:45] [Iter 1978/2250] R3[927/1200], Temp: 0.1224, Energy: -81.193419-0.000786j
[2025-09-09 01:17:31] [Iter 1979/2250] R3[928/1200], Temp: 0.1215, Energy: -81.207569-0.000734j
[2025-09-09 01:18:16] [Iter 1980/2250] R3[929/1200], Temp: 0.1206, Energy: -81.199006-0.001231j
[2025-09-09 01:19:02] [Iter 1981/2250] R3[930/1200], Temp: 0.1198, Energy: -81.100260-0.000586j
[2025-09-09 01:19:48] [Iter 1982/2250] R3[931/1200], Temp: 0.1189, Energy: -81.222186+0.001405j
[2025-09-09 01:20:33] [Iter 1983/2250] R3[932/1200], Temp: 0.1181, Energy: -81.149849-0.002102j
[2025-09-09 01:21:19] [Iter 1984/2250] R3[933/1200], Temp: 0.1173, Energy: -81.190682-0.001415j
[2025-09-09 01:22:04] [Iter 1985/2250] R3[934/1200], Temp: 0.1164, Energy: -81.091549+0.000425j
[2025-09-09 01:22:50] [Iter 1986/2250] R3[935/1200], Temp: 0.1156, Energy: -81.139867-0.001896j
[2025-09-09 01:23:35] [Iter 1987/2250] R3[936/1200], Temp: 0.1147, Energy: -81.148157+0.000046j
[2025-09-09 01:24:21] [Iter 1988/2250] R3[937/1200], Temp: 0.1139, Energy: -81.113020+0.000647j
[2025-09-09 01:25:06] [Iter 1989/2250] R3[938/1200], Temp: 0.1131, Energy: -81.090785-0.001776j
[2025-09-09 01:25:52] [Iter 1990/2250] R3[939/1200], Temp: 0.1123, Energy: -81.116936-0.004828j
[2025-09-09 01:26:37] [Iter 1991/2250] R3[940/1200], Temp: 0.1114, Energy: -81.130070-0.000555j
[2025-09-09 01:27:23] [Iter 1992/2250] R3[941/1200], Temp: 0.1106, Energy: -81.139060+0.001683j
[2025-09-09 01:28:09] [Iter 1993/2250] R3[942/1200], Temp: 0.1098, Energy: -81.150086-0.002565j
[2025-09-09 01:28:54] [Iter 1994/2250] R3[943/1200], Temp: 0.1090, Energy: -81.146527-0.002840j
[2025-09-09 01:29:40] [Iter 1995/2250] R3[944/1200], Temp: 0.1082, Energy: -81.128145-0.001002j
[2025-09-09 01:30:25] [Iter 1996/2250] R3[945/1200], Temp: 0.1073, Energy: -81.136082-0.002898j
[2025-09-09 01:31:10] [Iter 1997/2250] R3[946/1200], Temp: 0.1065, Energy: -81.175701-0.001382j
[2025-09-09 01:31:56] [Iter 1998/2250] R3[947/1200], Temp: 0.1057, Energy: -81.136019-0.002177j
[2025-09-09 01:32:42] [Iter 1999/2250] R3[948/1200], Temp: 0.1049, Energy: -81.169729+0.000440j
[2025-09-09 01:33:29] [Iter 2000/2250] R3[949/1200], Temp: 0.1041, Energy: -81.163776+0.000621j
[2025-09-09 01:33:29] ✓ Checkpoint saved: checkpoint_iter_002000.pkl
[2025-09-09 01:34:15] [Iter 2001/2250] R3[950/1200], Temp: 0.1033, Energy: -81.187806+0.000133j
[2025-09-09 01:35:00] [Iter 2002/2250] R3[951/1200], Temp: 0.1025, Energy: -81.135712+0.002187j
[2025-09-09 01:35:46] [Iter 2003/2250] R3[952/1200], Temp: 0.1017, Energy: -81.209376-0.001936j
[2025-09-09 01:36:31] [Iter 2004/2250] R3[953/1200], Temp: 0.1009, Energy: -81.219414+0.000966j
[2025-09-09 01:37:17] [Iter 2005/2250] R3[954/1200], Temp: 0.1002, Energy: -81.223618-0.000377j
[2025-09-09 01:38:03] [Iter 2006/2250] R3[955/1200], Temp: 0.0994, Energy: -81.169151+0.000329j
[2025-09-09 01:38:48] [Iter 2007/2250] R3[956/1200], Temp: 0.0986, Energy: -81.188805+0.000213j
[2025-09-09 01:39:34] [Iter 2008/2250] R3[957/1200], Temp: 0.0978, Energy: -81.218524-0.000275j
[2025-09-09 01:40:19] [Iter 2009/2250] R3[958/1200], Temp: 0.0970, Energy: -81.223916+0.001057j
[2025-09-09 01:41:05] [Iter 2010/2250] R3[959/1200], Temp: 0.0963, Energy: -81.202761-0.001596j
[2025-09-09 01:41:50] [Iter 2011/2250] R3[960/1200], Temp: 0.0955, Energy: -81.156486+0.001823j
[2025-09-09 01:42:36] [Iter 2012/2250] R3[961/1200], Temp: 0.0947, Energy: -81.213784+0.000813j
[2025-09-09 01:43:21] [Iter 2013/2250] R3[962/1200], Temp: 0.0940, Energy: -81.187484-0.001440j
[2025-09-09 01:44:07] [Iter 2014/2250] R3[963/1200], Temp: 0.0932, Energy: -81.160876-0.001356j
[2025-09-09 01:44:52] [Iter 2015/2250] R3[964/1200], Temp: 0.0924, Energy: -81.224593+0.003631j
[2025-09-09 01:45:38] [Iter 2016/2250] R3[965/1200], Temp: 0.0917, Energy: -81.195621+0.001104j
[2025-09-09 01:46:23] [Iter 2017/2250] R3[966/1200], Temp: 0.0909, Energy: -81.207092-0.001353j
[2025-09-09 01:47:09] [Iter 2018/2250] R3[967/1200], Temp: 0.0902, Energy: -81.226459-0.000864j
[2025-09-09 01:47:54] [Iter 2019/2250] R3[968/1200], Temp: 0.0894, Energy: -81.211724+0.000169j
[2025-09-09 01:48:40] [Iter 2020/2250] R3[969/1200], Temp: 0.0887, Energy: -81.269277+0.000981j
[2025-09-09 01:49:26] [Iter 2021/2250] R3[970/1200], Temp: 0.0879, Energy: -81.203295-0.000508j
[2025-09-09 01:50:11] [Iter 2022/2250] R3[971/1200], Temp: 0.0872, Energy: -81.182272-0.000811j
[2025-09-09 01:50:57] [Iter 2023/2250] R3[972/1200], Temp: 0.0865, Energy: -81.165593+0.002724j
[2025-09-09 01:51:42] [Iter 2024/2250] R3[973/1200], Temp: 0.0857, Energy: -81.136172+0.004069j
[2025-09-09 01:52:28] [Iter 2025/2250] R3[974/1200], Temp: 0.0850, Energy: -81.199036-0.002390j
[2025-09-09 01:53:13] [Iter 2026/2250] R3[975/1200], Temp: 0.0843, Energy: -81.200806+0.000175j
[2025-09-09 01:53:59] [Iter 2027/2250] R3[976/1200], Temp: 0.0835, Energy: -81.174625+0.004509j
[2025-09-09 01:54:44] [Iter 2028/2250] R3[977/1200], Temp: 0.0828, Energy: -81.243396+0.001101j
[2025-09-09 01:55:30] [Iter 2029/2250] R3[978/1200], Temp: 0.0821, Energy: -81.200704-0.002351j
[2025-09-09 01:56:15] [Iter 2030/2250] R3[979/1200], Temp: 0.0814, Energy: -81.248291+0.000569j
[2025-09-09 01:57:01] [Iter 2031/2250] R3[980/1200], Temp: 0.0807, Energy: -81.230676-0.000522j
[2025-09-09 01:57:46] [Iter 2032/2250] R3[981/1200], Temp: 0.0800, Energy: -81.188643-0.000632j
[2025-09-09 01:58:32] [Iter 2033/2250] R3[982/1200], Temp: 0.0792, Energy: -81.121219+0.003036j
[2025-09-09 01:59:17] [Iter 2034/2250] R3[983/1200], Temp: 0.0785, Energy: -81.159674-0.001931j
[2025-09-09 02:00:03] [Iter 2035/2250] R3[984/1200], Temp: 0.0778, Energy: -81.119526-0.001113j
[2025-09-09 02:00:48] [Iter 2036/2250] R3[985/1200], Temp: 0.0771, Energy: -81.183508+0.000121j
[2025-09-09 02:01:34] [Iter 2037/2250] R3[986/1200], Temp: 0.0764, Energy: -81.186442-0.002322j
[2025-09-09 02:02:19] [Iter 2038/2250] R3[987/1200], Temp: 0.0757, Energy: -81.219627-0.000133j
[2025-09-09 02:03:05] [Iter 2039/2250] R3[988/1200], Temp: 0.0751, Energy: -81.249584-0.000610j
[2025-09-09 02:03:50] [Iter 2040/2250] R3[989/1200], Temp: 0.0744, Energy: -81.309438-0.001357j
[2025-09-09 02:04:36] [Iter 2041/2250] R3[990/1200], Temp: 0.0737, Energy: -81.268265+0.003875j
[2025-09-09 02:05:21] [Iter 2042/2250] R3[991/1200], Temp: 0.0730, Energy: -81.270327+0.000876j
[2025-09-09 02:06:07] [Iter 2043/2250] R3[992/1200], Temp: 0.0723, Energy: -81.305503+0.001123j
[2025-09-09 02:06:52] [Iter 2044/2250] R3[993/1200], Temp: 0.0716, Energy: -81.269184-0.000281j
[2025-09-09 02:07:38] [Iter 2045/2250] R3[994/1200], Temp: 0.0710, Energy: -81.234427+0.000154j
[2025-09-09 02:08:24] [Iter 2046/2250] R3[995/1200], Temp: 0.0703, Energy: -81.217029-0.001940j
[2025-09-09 02:09:09] [Iter 2047/2250] R3[996/1200], Temp: 0.0696, Energy: -81.214412+0.001441j
[2025-09-09 02:09:55] [Iter 2048/2250] R3[997/1200], Temp: 0.0690, Energy: -81.192097-0.003672j
[2025-09-09 02:10:40] [Iter 2049/2250] R3[998/1200], Temp: 0.0683, Energy: -81.267383+0.000564j
[2025-09-09 02:11:26] [Iter 2050/2250] R3[999/1200], Temp: 0.0676, Energy: -81.304138+0.000742j
[2025-09-09 02:12:11] [Iter 2051/2250] R3[1000/1200], Temp: 0.0670, Energy: -81.201743+0.001133j
[2025-09-09 02:12:57] [Iter 2052/2250] R3[1001/1200], Temp: 0.0663, Energy: -81.194993+0.005726j
[2025-09-09 02:13:42] [Iter 2053/2250] R3[1002/1200], Temp: 0.0657, Energy: -81.254557+0.000631j
[2025-09-09 02:14:28] [Iter 2054/2250] R3[1003/1200], Temp: 0.0650, Energy: -81.231771+0.002776j
[2025-09-09 02:15:13] [Iter 2055/2250] R3[1004/1200], Temp: 0.0644, Energy: -81.296231-0.004881j
[2025-09-09 02:15:59] [Iter 2056/2250] R3[1005/1200], Temp: 0.0638, Energy: -81.230652-0.000866j
[2025-09-09 02:16:44] [Iter 2057/2250] R3[1006/1200], Temp: 0.0631, Energy: -81.243032-0.000113j
[2025-09-09 02:17:30] [Iter 2058/2250] R3[1007/1200], Temp: 0.0625, Energy: -81.144142+0.000958j
[2025-09-09 02:18:15] [Iter 2059/2250] R3[1008/1200], Temp: 0.0618, Energy: -81.264007-0.002071j
[2025-09-09 02:19:01] [Iter 2060/2250] R3[1009/1200], Temp: 0.0612, Energy: -81.192176+0.000998j
[2025-09-09 02:19:46] [Iter 2061/2250] R3[1010/1200], Temp: 0.0606, Energy: -81.121124-0.003346j
[2025-09-09 02:20:32] [Iter 2062/2250] R3[1011/1200], Temp: 0.0600, Energy: -81.186254-0.000283j
[2025-09-09 02:21:17] [Iter 2063/2250] R3[1012/1200], Temp: 0.0593, Energy: -81.206037+0.001211j
[2025-09-09 02:22:03] [Iter 2064/2250] R3[1013/1200], Temp: 0.0587, Energy: -81.200125-0.001032j
[2025-09-09 02:22:48] [Iter 2065/2250] R3[1014/1200], Temp: 0.0581, Energy: -81.089715+0.004281j
[2025-09-09 02:23:34] [Iter 2066/2250] R3[1015/1200], Temp: 0.0575, Energy: -81.089143-0.000648j
[2025-09-09 02:24:19] [Iter 2067/2250] R3[1016/1200], Temp: 0.0569, Energy: -81.103719-0.001610j
[2025-09-09 02:25:05] [Iter 2068/2250] R3[1017/1200], Temp: 0.0563, Energy: -81.091655+0.000078j
[2025-09-09 02:25:50] [Iter 2069/2250] R3[1018/1200], Temp: 0.0557, Energy: -81.112582+0.001449j
[2025-09-09 02:26:36] [Iter 2070/2250] R3[1019/1200], Temp: 0.0551, Energy: -81.083727-0.000237j
[2025-09-09 02:27:21] [Iter 2071/2250] R3[1020/1200], Temp: 0.0545, Energy: -81.176045+0.002890j
[2025-09-09 02:28:07] [Iter 2072/2250] R3[1021/1200], Temp: 0.0539, Energy: -81.126440+0.000631j
[2025-09-09 02:28:52] [Iter 2073/2250] R3[1022/1200], Temp: 0.0533, Energy: -81.158429+0.000980j
[2025-09-09 02:29:38] [Iter 2074/2250] R3[1023/1200], Temp: 0.0527, Energy: -81.187447+0.000763j
[2025-09-09 02:30:24] [Iter 2075/2250] R3[1024/1200], Temp: 0.0521, Energy: -81.212187+0.000357j
[2025-09-09 02:31:09] [Iter 2076/2250] R3[1025/1200], Temp: 0.0516, Energy: -81.152969-0.000080j
[2025-09-09 02:31:54] [Iter 2077/2250] R3[1026/1200], Temp: 0.0510, Energy: -81.176173+0.000585j
[2025-09-09 02:32:40] [Iter 2078/2250] R3[1027/1200], Temp: 0.0504, Energy: -81.130702-0.001551j
[2025-09-09 02:33:26] [Iter 2079/2250] R3[1028/1200], Temp: 0.0498, Energy: -81.168048-0.001613j
[2025-09-09 02:34:11] [Iter 2080/2250] R3[1029/1200], Temp: 0.0493, Energy: -81.095547+0.001621j
[2025-09-09 02:34:57] [Iter 2081/2250] R3[1030/1200], Temp: 0.0487, Energy: -81.083464-0.002516j
[2025-09-09 02:35:42] [Iter 2082/2250] R3[1031/1200], Temp: 0.0481, Energy: -81.148616-0.002155j
[2025-09-09 02:36:28] [Iter 2083/2250] R3[1032/1200], Temp: 0.0476, Energy: -81.140944+0.001401j
[2025-09-09 02:37:13] [Iter 2084/2250] R3[1033/1200], Temp: 0.0470, Energy: -81.123329-0.001066j
[2025-09-09 02:37:59] [Iter 2085/2250] R3[1034/1200], Temp: 0.0465, Energy: -81.097663-0.000906j
[2025-09-09 02:38:44] [Iter 2086/2250] R3[1035/1200], Temp: 0.0459, Energy: -81.094414-0.000087j
[2025-09-09 02:39:30] [Iter 2087/2250] R3[1036/1200], Temp: 0.0454, Energy: -81.103840+0.001342j
[2025-09-09 02:40:16] [Iter 2088/2250] R3[1037/1200], Temp: 0.0448, Energy: -81.188276-0.001090j
[2025-09-09 02:41:01] [Iter 2089/2250] R3[1038/1200], Temp: 0.0443, Energy: -81.153443-0.001252j
[2025-09-09 02:41:47] [Iter 2090/2250] R3[1039/1200], Temp: 0.0438, Energy: -81.146763+0.001232j
[2025-09-09 02:42:32] [Iter 2091/2250] R3[1040/1200], Temp: 0.0432, Energy: -81.156697-0.000518j
[2025-09-09 02:43:18] [Iter 2092/2250] R3[1041/1200], Temp: 0.0427, Energy: -81.186422+0.000048j
[2025-09-09 02:44:03] [Iter 2093/2250] R3[1042/1200], Temp: 0.0422, Energy: -81.124518+0.001801j
[2025-09-09 02:44:49] [Iter 2094/2250] R3[1043/1200], Temp: 0.0416, Energy: -81.178002-0.000644j
[2025-09-09 02:45:35] [Iter 2095/2250] R3[1044/1200], Temp: 0.0411, Energy: -81.205850+0.000626j
[2025-09-09 02:46:20] [Iter 2096/2250] R3[1045/1200], Temp: 0.0406, Energy: -81.251428-0.002202j
[2025-09-09 02:47:06] [Iter 2097/2250] R3[1046/1200], Temp: 0.0401, Energy: -81.233951-0.000620j
[2025-09-09 02:47:51] [Iter 2098/2250] R3[1047/1200], Temp: 0.0396, Energy: -81.148655-0.000769j
[2025-09-09 02:48:37] [Iter 2099/2250] R3[1048/1200], Temp: 0.0391, Energy: -81.168934+0.000985j
[2025-09-09 02:49:22] [Iter 2100/2250] R3[1049/1200], Temp: 0.0386, Energy: -81.223822+0.001283j
[2025-09-09 02:50:08] [Iter 2101/2250] R3[1050/1200], Temp: 0.0381, Energy: -81.258196-0.000050j
[2025-09-09 02:50:53] [Iter 2102/2250] R3[1051/1200], Temp: 0.0376, Energy: -81.164190+0.000552j
[2025-09-09 02:51:39] [Iter 2103/2250] R3[1052/1200], Temp: 0.0371, Energy: -81.217173+0.000329j
[2025-09-09 02:52:26] [Iter 2104/2250] R3[1053/1200], Temp: 0.0366, Energy: -81.220766-0.003053j
[2025-09-09 02:53:12] [Iter 2105/2250] R3[1054/1200], Temp: 0.0361, Energy: -81.239761+0.001543j
[2025-09-09 02:53:58] [Iter 2106/2250] R3[1055/1200], Temp: 0.0356, Energy: -81.230171-0.002883j
[2025-09-09 02:54:43] [Iter 2107/2250] R3[1056/1200], Temp: 0.0351, Energy: -81.160047+0.001622j
[2025-09-09 02:55:29] [Iter 2108/2250] R3[1057/1200], Temp: 0.0346, Energy: -81.180364+0.000134j
[2025-09-09 02:56:14] [Iter 2109/2250] R3[1058/1200], Temp: 0.0342, Energy: -81.108776-0.000534j
[2025-09-09 02:57:00] [Iter 2110/2250] R3[1059/1200], Temp: 0.0337, Energy: -81.178629+0.000746j
[2025-09-09 02:57:45] [Iter 2111/2250] R3[1060/1200], Temp: 0.0332, Energy: -81.214923+0.001819j
[2025-09-09 02:58:31] [Iter 2112/2250] R3[1061/1200], Temp: 0.0327, Energy: -81.264948+0.000467j
[2025-09-09 02:59:16] [Iter 2113/2250] R3[1062/1200], Temp: 0.0323, Energy: -81.287242+0.001937j
[2025-09-09 03:00:02] [Iter 2114/2250] R3[1063/1200], Temp: 0.0318, Energy: -81.254357+0.001711j
[2025-09-09 03:00:47] [Iter 2115/2250] R3[1064/1200], Temp: 0.0314, Energy: -81.325859+0.001551j
[2025-09-09 03:01:34] [Iter 2116/2250] R3[1065/1200], Temp: 0.0309, Energy: -81.278756-0.001480j
[2025-09-09 03:02:19] [Iter 2117/2250] R3[1066/1200], Temp: 0.0305, Energy: -81.176178-0.001446j
[2025-09-09 03:03:05] [Iter 2118/2250] R3[1067/1200], Temp: 0.0300, Energy: -81.251592-0.001127j
[2025-09-09 03:03:50] [Iter 2119/2250] R3[1068/1200], Temp: 0.0296, Energy: -81.198999-0.000455j
[2025-09-09 03:04:36] [Iter 2120/2250] R3[1069/1200], Temp: 0.0291, Energy: -81.237614-0.000056j
[2025-09-09 03:05:22] [Iter 2121/2250] R3[1070/1200], Temp: 0.0287, Energy: -81.205483+0.000506j
[2025-09-09 03:06:07] [Iter 2122/2250] R3[1071/1200], Temp: 0.0282, Energy: -81.186280+0.001553j
[2025-09-09 03:06:53] [Iter 2123/2250] R3[1072/1200], Temp: 0.0278, Energy: -81.182810-0.000344j
[2025-09-09 03:07:38] [Iter 2124/2250] R3[1073/1200], Temp: 0.0274, Energy: -81.260867-0.000161j
[2025-09-09 03:08:24] [Iter 2125/2250] R3[1074/1200], Temp: 0.0270, Energy: -81.200143-0.001595j
[2025-09-09 03:09:09] [Iter 2126/2250] R3[1075/1200], Temp: 0.0265, Energy: -81.145432-0.001910j
[2025-09-09 03:09:55] [Iter 2127/2250] R3[1076/1200], Temp: 0.0261, Energy: -81.254550-0.000572j
[2025-09-09 03:10:40] [Iter 2128/2250] R3[1077/1200], Temp: 0.0257, Energy: -81.238538+0.000305j
[2025-09-09 03:11:26] [Iter 2129/2250] R3[1078/1200], Temp: 0.0253, Energy: -81.272332+0.000464j
[2025-09-09 03:12:11] [Iter 2130/2250] R3[1079/1200], Temp: 0.0249, Energy: -81.198627-0.001316j
[2025-09-09 03:12:57] [Iter 2131/2250] R3[1080/1200], Temp: 0.0245, Energy: -81.200217+0.000612j
[2025-09-09 03:13:42] [Iter 2132/2250] R3[1081/1200], Temp: 0.0241, Energy: -81.207103+0.001602j
[2025-09-09 03:14:28] [Iter 2133/2250] R3[1082/1200], Temp: 0.0237, Energy: -81.141080-0.001030j
[2025-09-09 03:15:13] [Iter 2134/2250] R3[1083/1200], Temp: 0.0233, Energy: -81.082680+0.001493j
[2025-09-09 03:15:59] [Iter 2135/2250] R3[1084/1200], Temp: 0.0229, Energy: -81.096650+0.001392j
[2025-09-09 03:16:45] [Iter 2136/2250] R3[1085/1200], Temp: 0.0225, Energy: -81.040413+0.000718j
[2025-09-09 03:17:30] [Iter 2137/2250] R3[1086/1200], Temp: 0.0221, Energy: -81.140162+0.002016j
[2025-09-09 03:18:16] [Iter 2138/2250] R3[1087/1200], Temp: 0.0217, Energy: -81.033675-0.002574j
[2025-09-09 03:19:01] [Iter 2139/2250] R3[1088/1200], Temp: 0.0213, Energy: -81.065357+0.001097j
[2025-09-09 03:19:47] [Iter 2140/2250] R3[1089/1200], Temp: 0.0210, Energy: -81.043414+0.000728j
[2025-09-09 03:20:32] [Iter 2141/2250] R3[1090/1200], Temp: 0.0206, Energy: -81.092486+0.000584j
[2025-09-09 03:21:18] [Iter 2142/2250] R3[1091/1200], Temp: 0.0202, Energy: -81.160840-0.001463j
[2025-09-09 03:22:03] [Iter 2143/2250] R3[1092/1200], Temp: 0.0199, Energy: -81.174288+0.001941j
[2025-09-09 03:22:49] [Iter 2144/2250] R3[1093/1200], Temp: 0.0195, Energy: -81.222082-0.003279j
[2025-09-09 03:23:34] [Iter 2145/2250] R3[1094/1200], Temp: 0.0191, Energy: -81.274947-0.000905j
[2025-09-09 03:24:20] [Iter 2146/2250] R3[1095/1200], Temp: 0.0188, Energy: -81.269398+0.000960j
[2025-09-09 03:25:05] [Iter 2147/2250] R3[1096/1200], Temp: 0.0184, Energy: -81.265023+0.002308j
[2025-09-09 03:25:51] [Iter 2148/2250] R3[1097/1200], Temp: 0.0181, Energy: -81.276143-0.000354j
[2025-09-09 03:26:36] [Iter 2149/2250] R3[1098/1200], Temp: 0.0177, Energy: -81.255236+0.000332j
[2025-09-09 03:27:22] [Iter 2150/2250] R3[1099/1200], Temp: 0.0174, Energy: -81.318908-0.001339j
[2025-09-09 03:28:07] [Iter 2151/2250] R3[1100/1200], Temp: 0.0170, Energy: -81.282664-0.001327j
[2025-09-09 03:28:53] [Iter 2152/2250] R3[1101/1200], Temp: 0.0167, Energy: -81.206175+0.002024j
[2025-09-09 03:29:38] [Iter 2153/2250] R3[1102/1200], Temp: 0.0164, Energy: -81.321750-0.001955j
[2025-09-09 03:30:24] [Iter 2154/2250] R3[1103/1200], Temp: 0.0160, Energy: -81.284124-0.002816j
[2025-09-09 03:31:10] [Iter 2155/2250] R3[1104/1200], Temp: 0.0157, Energy: -81.225094-0.002050j
[2025-09-09 03:31:55] [Iter 2156/2250] R3[1105/1200], Temp: 0.0154, Energy: -81.242591+0.001773j
[2025-09-09 03:32:41] [Iter 2157/2250] R3[1106/1200], Temp: 0.0151, Energy: -81.213657+0.001073j
[2025-09-09 03:33:26] [Iter 2158/2250] R3[1107/1200], Temp: 0.0147, Energy: -81.207914-0.000694j
[2025-09-09 03:34:12] [Iter 2159/2250] R3[1108/1200], Temp: 0.0144, Energy: -81.221143-0.000674j
[2025-09-09 03:34:57] [Iter 2160/2250] R3[1109/1200], Temp: 0.0141, Energy: -81.280139+0.003262j
[2025-09-09 03:35:43] [Iter 2161/2250] R3[1110/1200], Temp: 0.0138, Energy: -81.282818+0.001148j
[2025-09-09 03:36:28] [Iter 2162/2250] R3[1111/1200], Temp: 0.0135, Energy: -81.167877+0.000167j
[2025-09-09 03:37:14] [Iter 2163/2250] R3[1112/1200], Temp: 0.0132, Energy: -81.103941-0.000650j
[2025-09-09 03:37:59] [Iter 2164/2250] R3[1113/1200], Temp: 0.0129, Energy: -81.164721+0.000047j
[2025-09-09 03:38:45] [Iter 2165/2250] R3[1114/1200], Temp: 0.0126, Energy: -81.080557-0.000853j
[2025-09-09 03:39:30] [Iter 2166/2250] R3[1115/1200], Temp: 0.0123, Energy: -81.104914-0.002316j
[2025-09-09 03:40:16] [Iter 2167/2250] R3[1116/1200], Temp: 0.0120, Energy: -81.173299-0.001662j
[2025-09-09 03:41:01] [Iter 2168/2250] R3[1117/1200], Temp: 0.0118, Energy: -81.189240+0.000944j
[2025-09-09 03:41:47] [Iter 2169/2250] R3[1118/1200], Temp: 0.0115, Energy: -81.310936-0.001071j
[2025-09-09 03:42:32] [Iter 2170/2250] R3[1119/1200], Temp: 0.0112, Energy: -81.201591+0.000253j
[2025-09-09 03:43:18] [Iter 2171/2250] R3[1120/1200], Temp: 0.0109, Energy: -81.207775-0.000258j
[2025-09-09 03:44:03] [Iter 2172/2250] R3[1121/1200], Temp: 0.0107, Energy: -81.230081+0.002237j
[2025-09-09 03:44:49] [Iter 2173/2250] R3[1122/1200], Temp: 0.0104, Energy: -81.220122+0.000011j
[2025-09-09 03:45:34] [Iter 2174/2250] R3[1123/1200], Temp: 0.0101, Energy: -81.218687+0.002524j
[2025-09-09 03:46:20] [Iter 2175/2250] R3[1124/1200], Temp: 0.0099, Energy: -81.206990+0.000329j
[2025-09-09 03:47:05] [Iter 2176/2250] R3[1125/1200], Temp: 0.0096, Energy: -81.234403+0.001191j
[2025-09-09 03:47:51] [Iter 2177/2250] R3[1126/1200], Temp: 0.0094, Energy: -81.170464+0.000151j
[2025-09-09 03:48:36] [Iter 2178/2250] R3[1127/1200], Temp: 0.0091, Energy: -81.251343-0.000236j
[2025-09-09 03:49:22] [Iter 2179/2250] R3[1128/1200], Temp: 0.0089, Energy: -81.242526+0.001808j
[2025-09-09 03:50:07] [Iter 2180/2250] R3[1129/1200], Temp: 0.0086, Energy: -81.212283+0.000853j
[2025-09-09 03:50:53] [Iter 2181/2250] R3[1130/1200], Temp: 0.0084, Energy: -81.241121+0.000237j
[2025-09-09 03:51:38] [Iter 2182/2250] R3[1131/1200], Temp: 0.0081, Energy: -81.146845-0.002038j
[2025-09-09 03:52:24] [Iter 2183/2250] R3[1132/1200], Temp: 0.0079, Energy: -81.156020-0.000549j
[2025-09-09 03:53:09] [Iter 2184/2250] R3[1133/1200], Temp: 0.0077, Energy: -81.222464+0.002028j
[2025-09-09 03:53:55] [Iter 2185/2250] R3[1134/1200], Temp: 0.0074, Energy: -81.215594-0.000773j
[2025-09-09 03:54:40] [Iter 2186/2250] R3[1135/1200], Temp: 0.0072, Energy: -81.121225-0.002126j
[2025-09-09 03:55:26] [Iter 2187/2250] R3[1136/1200], Temp: 0.0070, Energy: -81.207219+0.001956j
[2025-09-09 03:56:11] [Iter 2188/2250] R3[1137/1200], Temp: 0.0068, Energy: -81.240178-0.000134j
[2025-09-09 03:56:57] [Iter 2189/2250] R3[1138/1200], Temp: 0.0066, Energy: -81.173769+0.002344j
[2025-09-09 03:57:42] [Iter 2190/2250] R3[1139/1200], Temp: 0.0064, Energy: -81.178385+0.000462j
[2025-09-09 03:58:28] [Iter 2191/2250] R3[1140/1200], Temp: 0.0062, Energy: -81.177832+0.000187j
[2025-09-09 03:59:14] [Iter 2192/2250] R3[1141/1200], Temp: 0.0060, Energy: -81.243033-0.003532j
[2025-09-09 03:59:59] [Iter 2193/2250] R3[1142/1200], Temp: 0.0058, Energy: -81.194284-0.002627j
[2025-09-09 04:00:45] [Iter 2194/2250] R3[1143/1200], Temp: 0.0056, Energy: -81.131774-0.004932j
[2025-09-09 04:01:30] [Iter 2195/2250] R3[1144/1200], Temp: 0.0054, Energy: -81.233340+0.000972j
[2025-09-09 04:02:16] [Iter 2196/2250] R3[1145/1200], Temp: 0.0052, Energy: -81.177859+0.000514j
[2025-09-09 04:03:01] [Iter 2197/2250] R3[1146/1200], Temp: 0.0050, Energy: -81.224194-0.001981j
[2025-09-09 04:03:47] [Iter 2198/2250] R3[1147/1200], Temp: 0.0048, Energy: -81.147690+0.000123j
[2025-09-09 04:04:32] [Iter 2199/2250] R3[1148/1200], Temp: 0.0046, Energy: -81.178463-0.000466j
[2025-09-09 04:05:18] [Iter 2200/2250] R3[1149/1200], Temp: 0.0045, Energy: -81.180285+0.000839j
[2025-09-09 04:06:04] [Iter 2201/2250] R3[1150/1200], Temp: 0.0043, Energy: -81.218069-0.000088j
[2025-09-09 04:06:49] [Iter 2202/2250] R3[1151/1200], Temp: 0.0041, Energy: -81.135245-0.000706j
[2025-09-09 04:07:35] [Iter 2203/2250] R3[1152/1200], Temp: 0.0039, Energy: -81.110484-0.000665j
[2025-09-09 04:08:20] [Iter 2204/2250] R3[1153/1200], Temp: 0.0038, Energy: -81.161228-0.000110j
[2025-09-09 04:09:06] [Iter 2205/2250] R3[1154/1200], Temp: 0.0036, Energy: -81.152868+0.000033j
[2025-09-09 04:09:51] [Iter 2206/2250] R3[1155/1200], Temp: 0.0035, Energy: -81.134695-0.000001j
[2025-09-09 04:10:37] [Iter 2207/2250] R3[1156/1200], Temp: 0.0033, Energy: -81.144987+0.000738j
[2025-09-09 04:11:22] [Iter 2208/2250] R3[1157/1200], Temp: 0.0032, Energy: -81.195309-0.000303j
[2025-09-09 04:12:08] [Iter 2209/2250] R3[1158/1200], Temp: 0.0030, Energy: -81.263224+0.000166j
[2025-09-09 04:12:53] [Iter 2210/2250] R3[1159/1200], Temp: 0.0029, Energy: -81.178161-0.001935j
[2025-09-09 04:13:39] [Iter 2211/2250] R3[1160/1200], Temp: 0.0027, Energy: -81.146707-0.000525j
[2025-09-09 04:14:24] [Iter 2212/2250] R3[1161/1200], Temp: 0.0026, Energy: -81.111428-0.000370j
[2025-09-09 04:15:10] [Iter 2213/2250] R3[1162/1200], Temp: 0.0025, Energy: -81.187334+0.002436j
[2025-09-09 04:15:55] [Iter 2214/2250] R3[1163/1200], Temp: 0.0023, Energy: -81.159357+0.000449j
[2025-09-09 04:16:41] [Iter 2215/2250] R3[1164/1200], Temp: 0.0022, Energy: -81.211383-0.002849j
[2025-09-09 04:17:27] [Iter 2216/2250] R3[1165/1200], Temp: 0.0021, Energy: -81.187241-0.000674j
[2025-09-09 04:18:12] [Iter 2217/2250] R3[1166/1200], Temp: 0.0020, Energy: -81.129565-0.000452j
[2025-09-09 04:18:58] [Iter 2218/2250] R3[1167/1200], Temp: 0.0019, Energy: -81.212047+0.000987j
[2025-09-09 04:19:43] [Iter 2219/2250] R3[1168/1200], Temp: 0.0018, Energy: -81.189364+0.002696j
[2025-09-09 04:20:29] [Iter 2220/2250] R3[1169/1200], Temp: 0.0016, Energy: -81.226306+0.002374j
[2025-09-09 04:21:14] [Iter 2221/2250] R3[1170/1200], Temp: 0.0015, Energy: -81.165201+0.000453j
[2025-09-09 04:22:00] [Iter 2222/2250] R3[1171/1200], Temp: 0.0014, Energy: -81.192893-0.000109j
[2025-09-09 04:22:45] [Iter 2223/2250] R3[1172/1200], Temp: 0.0013, Energy: -81.317985+0.003221j
[2025-09-09 04:23:31] [Iter 2224/2250] R3[1173/1200], Temp: 0.0012, Energy: -81.183143-0.000095j
[2025-09-09 04:24:16] [Iter 2225/2250] R3[1174/1200], Temp: 0.0012, Energy: -81.190446-0.000719j
[2025-09-09 04:25:02] [Iter 2226/2250] R3[1175/1200], Temp: 0.0011, Energy: -81.067056+0.001025j
[2025-09-09 04:25:47] [Iter 2227/2250] R3[1176/1200], Temp: 0.0010, Energy: -81.095663+0.000157j
[2025-09-09 04:26:33] [Iter 2228/2250] R3[1177/1200], Temp: 0.0009, Energy: -81.080916+0.002520j
[2025-09-09 04:27:18] [Iter 2229/2250] R3[1178/1200], Temp: 0.0008, Energy: -81.133059+0.003499j
[2025-09-09 04:28:04] [Iter 2230/2250] R3[1179/1200], Temp: 0.0008, Energy: -81.177814+0.000571j
[2025-09-09 04:28:49] [Iter 2231/2250] R3[1180/1200], Temp: 0.0007, Energy: -81.160591-0.000393j
[2025-09-09 04:29:35] [Iter 2232/2250] R3[1181/1200], Temp: 0.0006, Energy: -81.199414+0.000860j
[2025-09-09 04:30:20] [Iter 2233/2250] R3[1182/1200], Temp: 0.0006, Energy: -81.235516-0.001787j
[2025-09-09 04:31:06] [Iter 2234/2250] R3[1183/1200], Temp: 0.0005, Energy: -81.194284-0.001338j
[2025-09-09 04:31:51] [Iter 2235/2250] R3[1184/1200], Temp: 0.0004, Energy: -81.287931-0.000629j
[2025-09-09 04:32:37] [Iter 2236/2250] R3[1185/1200], Temp: 0.0004, Energy: -81.218062+0.001557j
[2025-09-09 04:33:22] [Iter 2237/2250] R3[1186/1200], Temp: 0.0003, Energy: -81.201826+0.001819j
[2025-09-09 04:34:08] [Iter 2238/2250] R3[1187/1200], Temp: 0.0003, Energy: -81.289645-0.001181j
[2025-09-09 04:34:53] [Iter 2239/2250] R3[1188/1200], Temp: 0.0002, Energy: -81.212616-0.000390j
[2025-09-09 04:35:39] [Iter 2240/2250] R3[1189/1200], Temp: 0.0002, Energy: -81.246478-0.000033j
[2025-09-09 04:36:24] [Iter 2241/2250] R3[1190/1200], Temp: 0.0002, Energy: -81.212372+0.000052j
[2025-09-09 04:37:10] [Iter 2242/2250] R3[1191/1200], Temp: 0.0001, Energy: -81.230504-0.002405j
[2025-09-09 04:37:55] [Iter 2243/2250] R3[1192/1200], Temp: 0.0001, Energy: -81.236303-0.001014j
[2025-09-09 04:38:41] [Iter 2244/2250] R3[1193/1200], Temp: 0.0001, Energy: -81.241374-0.000178j
[2025-09-09 04:39:26] [Iter 2245/2250] R3[1194/1200], Temp: 0.0001, Energy: -81.302890-0.001097j
[2025-09-09 04:40:12] [Iter 2246/2250] R3[1195/1200], Temp: 0.0000, Energy: -81.244708-0.000195j
[2025-09-09 04:40:57] [Iter 2247/2250] R3[1196/1200], Temp: 0.0000, Energy: -81.166730+0.002175j
[2025-09-09 04:41:43] [Iter 2248/2250] R3[1197/1200], Temp: 0.0000, Energy: -81.167905-0.000946j
[2025-09-09 04:42:28] [Iter 2249/2250] R3[1198/1200], Temp: 0.0000, Energy: -81.164281-0.001887j
[2025-09-09 04:43:14] [Iter 2250/2250] R3[1199/1200], Temp: 0.0000, Energy: -81.152107+0.000003j
[2025-09-09 04:43:14] ✓ Checkpoint saved: checkpoint_iter_002250.pkl
[2025-09-09 04:43:14] ✅ Training completed | Restarts: 3
[2025-09-09 04:43:14] ============================================================
[2025-09-09 04:43:14] Training completed | Runtime: 102488.4s
[2025-09-09 04:43:31] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-09 04:43:31] ============================================================
