[2025-09-11 02:21:36] ✓ 从checkpoint恢复: results/L=5/J2=0.05/J1=0.09/training/checkpoints/final_GCNN.pkl
[2025-09-11 02:21:36]   - 迭代次数: final
[2025-09-11 02:21:36]   - 能量: -83.676160+0.001053j ± 0.110385
[2025-09-11 02:21:36]   - 时间戳: 2025-09-11T02:21:16.098067+08:00
[2025-09-11 02:21:58] ✓ 变分状态参数已从checkpoint恢复
[2025-09-11 02:21:58] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-11 02:21:59] ==================================================
[2025-09-11 02:21:59] GCNN for Shastry-Sutherland Model
[2025-09-11 02:21:59] ==================================================
[2025-09-11 02:21:59] System parameters:
[2025-09-11 02:21:59]   - System size: L=5, N=100
[2025-09-11 02:21:59]   - System parameters: J1=0.1, J2=0.05, Q=0.95
[2025-09-11 02:21:59] --------------------------------------------------
[2025-09-11 02:21:59] Model parameters:
[2025-09-11 02:21:59]   - Number of layers = 4
[2025-09-11 02:21:59]   - Number of features = 4
[2025-09-11 02:21:59]   - Total parameters = 19628
[2025-09-11 02:21:59] --------------------------------------------------
[2025-09-11 02:21:59] Training parameters:
[2025-09-11 02:21:59]   - Learning rate: 0.015
[2025-09-11 02:21:59]   - Total iterations: 1050
[2025-09-11 02:21:59]   - Annealing cycles: 3
[2025-09-11 02:21:59]   - Initial period: 150
[2025-09-11 02:21:59]   - Period multiplier: 2.0
[2025-09-11 02:21:59]   - Temperature range: 0.0-1.0
[2025-09-11 02:21:59]   - Samples: 4096
[2025-09-11 02:21:59]   - Discarded samples: 0
[2025-09-11 02:21:59]   - Chunk size: 2048
[2025-09-11 02:21:59]   - Diagonal shift: 0.2
[2025-09-11 02:21:59]   - Gradient clipping: 1.0
[2025-09-11 02:21:59]   - Checkpoint enabled: interval=105
[2025-09-11 02:21:59]   - Checkpoint directory: results/L=5/J2=0.05/J1=0.10/training/checkpoints
[2025-09-11 02:21:59] --------------------------------------------------
[2025-09-11 02:21:59] Device status:
[2025-09-11 02:21:59]   - Devices model: NVIDIA H200 NVL
[2025-09-11 02:21:59]   - Number of devices: 1
[2025-09-11 02:21:59]   - Sharding: True
[2025-09-11 02:21:59] ============================================================
[2025-09-11 02:23:25] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -84.498572-0.017609j
[2025-09-11 02:24:22] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -84.282953-0.018935j
[2025-09-11 02:24:45] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -84.180727-0.012734j
[2025-09-11 02:25:08] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -84.217151-0.011463j
[2025-09-11 02:25:32] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -84.239190-0.006650j
[2025-09-11 02:25:55] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -84.212455-0.010670j
[2025-09-11 02:26:18] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -84.318381-0.000658j
[2025-09-11 02:26:42] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -84.263673-0.004644j
[2025-09-11 02:27:05] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -84.079654-0.006336j
[2025-09-11 02:27:28] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -84.185502-0.003977j
[2025-09-11 02:27:52] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -84.165654-0.002179j
[2025-09-11 02:28:15] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -84.061477-0.001466j
[2025-09-11 02:28:38] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -84.083447+0.001247j
[2025-09-11 02:29:02] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -84.002401-0.001186j
[2025-09-11 02:29:25] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -84.258692+0.001055j
[2025-09-11 02:29:48] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -84.271865+0.002569j
[2025-09-11 02:30:12] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -84.249012+0.000719j
[2025-09-11 02:30:35] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -84.302048-0.003385j
[2025-09-11 02:30:58] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -84.345611-0.001995j
[2025-09-11 02:31:22] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -84.227293-0.000912j
[2025-09-11 02:31:45] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -84.328394-0.000516j
[2025-09-11 02:32:08] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -84.228131-0.001214j
[2025-09-11 02:32:31] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -84.203253+0.001620j
[2025-09-11 02:32:55] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -84.225717-0.000303j
[2025-09-11 02:33:18] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -84.081174-0.002536j
[2025-09-11 02:33:41] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -84.153008+0.002183j
[2025-09-11 02:34:05] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -84.405606+0.000990j
[2025-09-11 02:34:28] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -84.421638-0.004879j
[2025-09-11 02:34:51] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -84.289381+0.003137j
[2025-09-11 02:35:15] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -84.434223+0.002837j
[2025-09-11 02:35:38] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -84.382840+0.008833j
[2025-09-11 02:36:01] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -84.315098+0.001223j
[2025-09-11 02:36:25] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -84.333921-0.001984j
[2025-09-11 02:36:48] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -84.371174-0.000747j
[2025-09-11 02:37:11] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -84.419402-0.000682j
[2025-09-11 02:37:35] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -84.346369-0.001020j
[2025-09-11 02:37:58] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -84.334621+0.001540j
[2025-09-11 02:38:21] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -84.425213-0.001046j
[2025-09-11 02:38:45] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -84.280668+0.002085j
[2025-09-11 02:39:08] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -84.311705+0.000358j
[2025-09-11 02:39:31] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -84.369761+0.001084j
[2025-09-11 02:39:55] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -84.342835+0.003542j
[2025-09-11 02:40:18] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -84.390645+0.003744j
[2025-09-11 02:40:41] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -84.308311+0.002705j
[2025-09-11 02:41:04] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -84.368249-0.000046j
[2025-09-11 02:41:28] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -84.352647+0.000954j
[2025-09-11 02:41:51] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -84.325167+0.004398j
[2025-09-11 02:42:14] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -84.328220-0.000330j
[2025-09-11 02:42:38] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -84.240799+0.001935j
[2025-09-11 02:43:01] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -84.229192+0.007407j
[2025-09-11 02:43:24] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -84.169835+0.001163j
[2025-09-11 02:43:48] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -84.322596+0.000114j
[2025-09-11 02:44:11] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -84.397356-0.001666j
[2025-09-11 02:44:34] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -84.378905-0.001882j
[2025-09-11 02:44:58] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -84.330039-0.000704j
[2025-09-11 02:45:21] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -84.153044-0.000677j
[2025-09-11 02:45:44] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -84.222875+0.002214j
[2025-09-11 02:46:08] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -84.197748+0.001450j
[2025-09-11 02:46:31] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -84.147634-0.000327j
[2025-09-11 02:46:54] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -84.245336+0.000714j
[2025-09-11 02:47:18] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -84.318486-0.007368j
[2025-09-11 02:47:41] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -84.196534-0.000261j
[2025-09-11 02:48:04] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -84.297452-0.002553j
[2025-09-11 02:48:27] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -84.267048+0.000849j
[2025-09-11 02:48:51] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -84.216056-0.002143j
[2025-09-11 02:49:14] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -84.286263-0.000788j
[2025-09-11 02:49:38] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -84.291037-0.003572j
[2025-09-11 02:50:01] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -84.261099-0.006031j
[2025-09-11 02:50:24] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -84.329828-0.003373j
[2025-09-11 02:50:47] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -84.307975-0.001630j
[2025-09-11 02:51:11] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -84.185899-0.003069j
[2025-09-11 02:51:34] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -84.316456+0.000298j
[2025-09-11 02:51:57] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -84.214917+0.002017j
[2025-09-11 02:52:21] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -84.356126+0.004891j
[2025-09-11 02:52:44] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -84.427601+0.000798j
[2025-09-11 02:53:07] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -84.221871-0.001026j
[2025-09-11 02:53:31] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -84.215915-0.001303j
[2025-09-11 02:53:54] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -84.336438-0.002800j
[2025-09-11 02:54:17] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -84.288488+0.002963j
[2025-09-11 02:54:41] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -84.357358-0.001086j
[2025-09-11 02:55:04] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -84.228933+0.001007j
[2025-09-11 02:55:27] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -84.351287+0.002267j
[2025-09-11 02:55:50] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -84.243413+0.002403j
[2025-09-11 02:56:14] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -84.138590-0.003819j
[2025-09-11 02:56:37] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -84.169747-0.003656j
[2025-09-11 02:57:01] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -84.246107+0.000435j
[2025-09-11 02:57:24] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -84.227034-0.005011j
[2025-09-11 02:57:47] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -84.231158+0.003833j
[2025-09-11 02:58:10] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -84.224546-0.000242j
[2025-09-11 02:58:34] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -84.292417+0.000469j
[2025-09-11 02:58:57] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -84.182775-0.003229j
[2025-09-11 02:59:20] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -84.238665+0.006730j
[2025-09-11 02:59:44] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -84.237230-0.001229j
[2025-09-11 03:00:07] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -84.336829+0.003714j
[2025-09-11 03:00:30] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -84.275276+0.001309j
[2025-09-11 03:00:54] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -84.251865-0.000152j
[2025-09-11 03:01:17] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -84.281942-0.000606j
[2025-09-11 03:01:40] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -84.288473-0.001864j
[2025-09-11 03:02:04] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -84.308563+0.001764j
[2025-09-11 03:02:27] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -84.394241-0.000864j
[2025-09-11 03:02:50] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -84.249830-0.003319j
[2025-09-11 03:03:13] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -84.220240+0.001788j
[2025-09-11 03:03:37] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -84.341529-0.002374j
[2025-09-11 03:04:00] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -84.270651-0.000665j
[2025-09-11 03:04:23] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -84.164124+0.004374j
[2025-09-11 03:04:23] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-11 03:04:47] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -84.110353+0.000497j
[2025-09-11 03:05:10] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -84.057451-0.000734j
[2025-09-11 03:05:34] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -84.187628-0.001875j
[2025-09-11 03:05:57] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -84.224834+0.001023j
[2025-09-11 03:06:20] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -84.100079+0.001224j
[2025-09-11 03:06:44] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -84.142217-0.001210j
[2025-09-11 03:07:07] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -84.264177+0.002427j
[2025-09-11 03:07:30] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -84.031086+0.001016j
[2025-09-11 03:07:53] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -83.991646+0.000006j
[2025-09-11 03:08:17] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -84.122683+0.000523j
[2025-09-11 03:08:40] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -84.105143-0.001281j
[2025-09-11 03:09:03] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -84.079895-0.000139j
[2025-09-11 03:09:27] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -84.150908+0.003588j
[2025-09-11 03:09:50] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -84.165240+0.003693j
[2025-09-11 03:10:13] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -84.138098+0.000639j
[2025-09-11 03:10:37] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -84.143913+0.001359j
[2025-09-11 03:11:00] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -84.173999-0.002588j
[2025-09-11 03:11:23] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -84.283772-0.002967j
[2025-09-11 03:11:47] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -84.220457-0.003804j
[2025-09-11 03:12:10] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -84.185433+0.000724j
[2025-09-11 03:12:33] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -84.348581+0.002532j
[2025-09-11 03:12:57] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -84.262221+0.005739j
[2025-09-11 03:13:20] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -84.257507-0.000469j
[2025-09-11 03:13:43] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -84.369799-0.000183j
[2025-09-11 03:14:07] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -84.213928+0.002610j
[2025-09-11 03:14:30] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -84.409932-0.001519j
[2025-09-11 03:14:53] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -84.259394-0.002780j
[2025-09-11 03:15:17] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -84.306953-0.000186j
[2025-09-11 03:15:40] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -84.261292-0.003042j
[2025-09-11 03:16:03] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -84.182003-0.000870j
[2025-09-11 03:16:27] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -84.220850+0.002280j
[2025-09-11 03:16:50] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -84.267503-0.003638j
[2025-09-11 03:17:13] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -84.409085-0.000568j
[2025-09-11 03:17:36] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -84.348666-0.000122j
[2025-09-11 03:18:00] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -84.274529-0.000590j
[2025-09-11 03:18:23] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -84.178760-0.003392j
[2025-09-11 03:18:46] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -84.259941+0.001452j
[2025-09-11 03:19:10] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -84.291561+0.002542j
[2025-09-11 03:19:33] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -84.367210-0.006204j
[2025-09-11 03:19:56] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -84.210397-0.000772j
[2025-09-11 03:20:20] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -84.210249+0.002042j
[2025-09-11 03:20:43] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -84.259637+0.001826j
[2025-09-11 03:21:06] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -84.387452+0.000323j
[2025-09-11 03:21:30] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -84.371382-0.002916j
[2025-09-11 03:21:53] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -84.267081-0.002178j
[2025-09-11 03:21:53] RESTART #1 | Period: 300
[2025-09-11 03:22:16] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -84.540097-0.003227j
[2025-09-11 03:22:40] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -84.428463+0.000928j
[2025-09-11 03:23:03] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -84.540446+0.000410j
[2025-09-11 03:23:26] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -84.501276+0.001457j
[2025-09-11 03:23:49] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -84.422675-0.007821j
[2025-09-11 03:24:13] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -84.220566-0.005566j
[2025-09-11 03:24:36] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -84.143079-0.000267j
[2025-09-11 03:25:00] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -84.365182-0.001413j
[2025-09-11 03:25:23] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -84.227143+0.003038j
[2025-09-11 03:25:46] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -84.217012-0.001403j
[2025-09-11 03:26:09] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -84.204187+0.003931j
[2025-09-11 03:26:33] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -84.190824-0.001159j
[2025-09-11 03:26:56] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -84.170606-0.002675j
[2025-09-11 03:27:19] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -84.110474+0.002833j
[2025-09-11 03:27:43] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -84.180463+0.002561j
[2025-09-11 03:28:06] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -84.182666+0.000494j
[2025-09-11 03:28:29] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -84.054429+0.005886j
[2025-09-11 03:28:53] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -84.184323+0.001966j
[2025-09-11 03:29:16] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -84.174550+0.001543j
[2025-09-11 03:29:39] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -84.142426-0.002374j
[2025-09-11 03:30:03] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -84.139196-0.002923j
[2025-09-11 03:30:26] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -84.128580+0.007044j
[2025-09-11 03:30:49] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -84.035493-0.001071j
[2025-09-11 03:31:13] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -84.162694+0.002001j
[2025-09-11 03:31:36] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -84.346496-0.004342j
[2025-09-11 03:31:59] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -84.319080+0.000401j
[2025-09-11 03:32:22] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -84.150222-0.001991j
[2025-09-11 03:32:46] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -84.224849+0.001739j
[2025-09-11 03:33:09] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -84.234769-0.000728j
[2025-09-11 03:33:32] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -84.157926-0.001031j
[2025-09-11 03:33:56] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -84.325510+0.000174j
[2025-09-11 03:34:19] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -84.243632+0.001200j
[2025-09-11 03:34:42] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -84.252479-0.001791j
[2025-09-11 03:35:06] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -84.101408+0.002244j
[2025-09-11 03:35:29] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -84.243014-0.000830j
[2025-09-11 03:35:52] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -84.296206-0.001248j
[2025-09-11 03:36:16] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -84.273756+0.004036j
[2025-09-11 03:36:39] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -84.366090-0.000970j
[2025-09-11 03:37:02] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -84.236249+0.000859j
[2025-09-11 03:37:26] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -84.328103-0.000102j
[2025-09-11 03:37:49] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -84.160300-0.000988j
[2025-09-11 03:38:12] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -84.212153-0.001888j
[2025-09-11 03:38:36] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -84.216802-0.003487j
[2025-09-11 03:38:59] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -84.301523+0.001150j
[2025-09-11 03:39:23] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -84.324664+0.003748j
[2025-09-11 03:39:46] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -84.412475+0.002451j
[2025-09-11 03:40:09] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -84.259591-0.003456j
[2025-09-11 03:40:33] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -84.292394-0.000566j
[2025-09-11 03:40:56] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -84.423133-0.000672j
[2025-09-11 03:41:19] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -84.471365+0.001156j
[2025-09-11 03:41:43] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -84.390916+0.000244j
[2025-09-11 03:42:06] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -84.395975+0.000187j
[2025-09-11 03:42:29] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -84.247987-0.000488j
[2025-09-11 03:42:53] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -84.344228+0.000948j
[2025-09-11 03:43:16] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -84.278534-0.002311j
[2025-09-11 03:43:39] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -84.340421-0.000741j
[2025-09-11 03:44:02] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -84.416136-0.000145j
[2025-09-11 03:44:26] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -84.406072-0.002531j
[2025-09-11 03:44:49] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -84.509094-0.005832j
[2025-09-11 03:45:12] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -84.380025-0.000625j
[2025-09-11 03:45:12] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-11 03:45:36] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -84.235287-0.000151j
[2025-09-11 03:45:59] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -84.245935-0.001081j
[2025-09-11 03:46:22] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -84.200991-0.000268j
[2025-09-11 03:46:46] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -84.247600-0.004653j
[2025-09-11 03:47:09] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -84.368597-0.002897j
[2025-09-11 03:47:32] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -84.359067-0.000947j
[2025-09-11 03:47:56] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -84.237149-0.000786j
[2025-09-11 03:48:19] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -84.393028-0.000757j
[2025-09-11 03:48:42] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -84.247852+0.002982j
[2025-09-11 03:49:06] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -84.315635-0.001142j
[2025-09-11 03:49:29] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -84.203950-0.005945j
[2025-09-11 03:49:52] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -84.383571-0.000203j
[2025-09-11 03:50:16] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -84.398181-0.001631j
[2025-09-11 03:50:39] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -84.364364-0.001110j
[2025-09-11 03:51:02] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -84.321556-0.000032j
[2025-09-11 03:51:26] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -84.220717+0.000144j
[2025-09-11 03:51:49] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -84.295570+0.000757j
[2025-09-11 03:52:12] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -84.246831+0.003230j
[2025-09-11 03:52:36] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -84.402659+0.004935j
[2025-09-11 03:52:59] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -84.374304-0.000082j
[2025-09-11 03:53:22] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -84.216903-0.002097j
[2025-09-11 03:53:46] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -84.186998-0.003346j
[2025-09-11 03:54:09] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -84.252560-0.000570j
[2025-09-11 03:54:32] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -84.171158+0.000044j
[2025-09-11 03:54:56] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -84.216893-0.005060j
[2025-09-11 03:55:19] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -84.256186+0.001194j
[2025-09-11 03:55:42] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -84.357878+0.000612j
[2025-09-11 03:56:06] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -84.206637+0.003120j
[2025-09-11 03:56:29] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -84.387673+0.002786j
[2025-09-11 03:56:52] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -84.318841+0.002475j
[2025-09-11 03:57:16] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -84.155856+0.001562j
[2025-09-11 03:57:39] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -84.097042-0.003355j
[2025-09-11 03:58:02] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -84.134696+0.002306j
[2025-09-11 03:58:26] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -84.203386+0.002498j
[2025-09-11 03:58:49] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -84.129506+0.003767j
[2025-09-11 03:59:12] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -84.286949-0.000512j
[2025-09-11 03:59:36] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -84.163697+0.000758j
[2025-09-11 03:59:59] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -84.213721+0.006322j
[2025-09-11 04:00:22] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -84.229066+0.005185j
[2025-09-11 04:00:46] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -84.245543-0.000129j
[2025-09-11 04:01:09] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -84.372308-0.002351j
[2025-09-11 04:01:32] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -84.390998-0.002526j
[2025-09-11 04:01:56] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -84.375479+0.002458j
[2025-09-11 04:02:19] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -84.298063-0.000449j
[2025-09-11 04:02:42] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -84.203782-0.001141j
[2025-09-11 04:03:06] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -84.040864+0.004620j
[2025-09-11 04:03:29] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -84.181837+0.000792j
[2025-09-11 04:03:52] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -84.171032+0.000728j
[2025-09-11 04:04:16] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -84.213472-0.001974j
[2025-09-11 04:04:39] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -84.174395+0.003225j
[2025-09-11 04:05:02] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -84.225562+0.000026j
[2025-09-11 04:05:26] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -84.166192+0.002104j
[2025-09-11 04:05:49] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -84.164467-0.000628j
[2025-09-11 04:06:12] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -84.265741+0.000648j
[2025-09-11 04:06:36] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -84.326797+0.002332j
[2025-09-11 04:06:59] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -84.162971+0.002994j
[2025-09-11 04:07:22] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -84.427279-0.001303j
[2025-09-11 04:07:46] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -84.328844+0.001989j
[2025-09-11 04:08:09] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -84.236838+0.002359j
[2025-09-11 04:08:32] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -84.272854-0.002165j
[2025-09-11 04:08:55] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -84.361456+0.001299j
[2025-09-11 04:09:19] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -84.446560+0.003214j
[2025-09-11 04:09:42] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -84.538801+0.000896j
[2025-09-11 04:10:05] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -84.478795-0.000735j
[2025-09-11 04:10:29] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -84.381495-0.004499j
[2025-09-11 04:10:52] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -84.403249-0.001281j
[2025-09-11 04:11:15] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -84.252564+0.004689j
[2025-09-11 04:11:38] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -84.120533-0.000613j
[2025-09-11 04:12:02] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -84.084148-0.001086j
[2025-09-11 04:12:25] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -84.266310+0.000819j
[2025-09-11 04:12:48] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -84.522787+0.000222j
[2025-09-11 04:13:12] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -84.373868+0.002717j
[2025-09-11 04:13:35] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -84.445746-0.002454j
[2025-09-11 04:13:58] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -84.342531+0.001345j
[2025-09-11 04:14:22] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -84.386041+0.001185j
[2025-09-11 04:14:45] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -84.191974+0.005801j
[2025-09-11 04:15:08] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -84.320873+0.002110j
[2025-09-11 04:15:32] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -84.162013+0.000411j
[2025-09-11 04:15:55] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -84.280820+0.000204j
[2025-09-11 04:16:18] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -84.250404-0.005297j
[2025-09-11 04:16:42] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -84.255172-0.004348j
[2025-09-11 04:17:05] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -84.312111-0.003955j
[2025-09-11 04:17:28] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -84.189578-0.003573j
[2025-09-11 04:17:52] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -84.170507+0.000759j
[2025-09-11 04:18:15] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -84.217015-0.000503j
[2025-09-11 04:18:39] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -84.206434-0.001914j
[2025-09-11 04:19:02] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -84.382881+0.003848j
[2025-09-11 04:19:25] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -84.353417-0.000673j
[2025-09-11 04:19:48] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -84.364392-0.002479j
[2025-09-11 04:20:12] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -84.364762-0.003385j
[2025-09-11 04:20:35] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -84.324772-0.000637j
[2025-09-11 04:20:58] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -84.419613-0.001298j
[2025-09-11 04:21:22] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -84.383169+0.001048j
[2025-09-11 04:21:45] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -84.287715-0.000913j
[2025-09-11 04:22:08] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -84.282787-0.003160j
[2025-09-11 04:22:32] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -84.284465+0.003872j
[2025-09-11 04:22:55] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -84.327745+0.003159j
[2025-09-11 04:23:18] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -84.133060-0.001395j
[2025-09-11 04:23:42] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -84.218378-0.001323j
[2025-09-11 04:24:05] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -84.240219-0.002482j
[2025-09-11 04:24:28] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -84.234105-0.001412j
[2025-09-11 04:24:52] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -84.089359-0.004954j
[2025-09-11 04:25:15] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -84.215707-0.001463j
[2025-09-11 04:25:38] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -84.286329-0.004753j
[2025-09-11 04:26:02] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -84.218887-0.002195j
[2025-09-11 04:26:02] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-11 04:26:25] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -84.134090+0.004284j
[2025-09-11 04:26:48] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -84.045775+0.003339j
[2025-09-11 04:27:12] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -83.914207-0.000197j
[2025-09-11 04:27:35] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -83.949607+0.001826j
[2025-09-11 04:27:58] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -84.108889+0.001301j
[2025-09-11 04:28:22] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -84.139428+0.002118j
[2025-09-11 04:28:45] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -84.265086+0.000073j
[2025-09-11 04:29:08] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -84.359010+0.000256j
[2025-09-11 04:29:32] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -84.298291-0.002913j
[2025-09-11 04:29:55] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -84.272422+0.004525j
[2025-09-11 04:30:18] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -84.339038-0.000774j
[2025-09-11 04:30:41] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -84.373079+0.002028j
[2025-09-11 04:31:05] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -84.279869+0.003736j
[2025-09-11 04:31:28] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -84.329390-0.000887j
[2025-09-11 04:31:51] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -84.315320+0.003288j
[2025-09-11 04:32:15] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -84.263185-0.000812j
[2025-09-11 04:32:38] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -84.272258+0.002455j
[2025-09-11 04:33:01] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -84.074647+0.002037j
[2025-09-11 04:33:25] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -84.107194-0.001158j
[2025-09-11 04:33:48] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -84.129222-0.002075j
[2025-09-11 04:34:11] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -84.167764+0.001983j
[2025-09-11 04:34:35] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -84.097581-0.001059j
[2025-09-11 04:34:58] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -84.186134+0.003300j
[2025-09-11 04:35:21] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -84.169445-0.002094j
[2025-09-11 04:35:45] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -84.219220-0.002550j
[2025-09-11 04:36:08] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -84.260673+0.000020j
[2025-09-11 04:36:31] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -84.302456-0.001325j
[2025-09-11 04:36:54] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -84.189349+0.003302j
[2025-09-11 04:37:18] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -84.217689+0.003929j
[2025-09-11 04:37:41] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -84.131441-0.001973j
[2025-09-11 04:38:04] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -84.144264+0.002128j
[2025-09-11 04:38:28] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -84.105336+0.003779j
[2025-09-11 04:38:51] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -84.125492+0.003536j
[2025-09-11 04:39:14] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -84.195097+0.002925j
[2025-09-11 04:39:38] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -84.135468-0.001668j
[2025-09-11 04:40:01] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -84.290751+0.002387j
[2025-09-11 04:40:24] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -84.239283+0.002000j
[2025-09-11 04:40:48] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -84.218390-0.004668j
[2025-09-11 04:41:11] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -84.198526+0.002553j
[2025-09-11 04:41:34] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -84.380898-0.000933j
[2025-09-11 04:41:57] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -84.512144+0.001451j
[2025-09-11 04:42:21] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -84.373276+0.000035j
[2025-09-11 04:42:44] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -84.569310-0.002724j
[2025-09-11 04:43:07] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -84.368867+0.000538j
[2025-09-11 04:43:31] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -84.527305+0.001873j
[2025-09-11 04:43:54] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -84.586322+0.000339j
[2025-09-11 04:44:17] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -84.348739-0.000893j
[2025-09-11 04:44:41] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -84.400882+0.000807j
[2025-09-11 04:45:04] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -84.270303+0.000527j
[2025-09-11 04:45:27] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -84.287440-0.000375j
[2025-09-11 04:45:50] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -84.351314+0.001935j
[2025-09-11 04:46:14] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -84.139062-0.001697j
[2025-09-11 04:46:37] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -84.201598+0.002943j
[2025-09-11 04:47:00] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -84.237214-0.001551j
[2025-09-11 04:47:24] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -84.218114-0.002647j
[2025-09-11 04:47:47] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -84.336461+0.002225j
[2025-09-11 04:48:10] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -84.294922-0.001640j
[2025-09-11 04:48:34] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -84.253664+0.004751j
[2025-09-11 04:48:57] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -84.211227-0.002102j
[2025-09-11 04:49:20] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -84.192257-0.005225j
[2025-09-11 04:49:44] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -84.188478-0.000058j
[2025-09-11 04:50:07] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -84.262919+0.001631j
[2025-09-11 04:50:30] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -84.403996-0.003879j
[2025-09-11 04:50:53] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -84.248548-0.001652j
[2025-09-11 04:51:17] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -84.384848-0.001812j
[2025-09-11 04:51:40] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -84.201808+0.003437j
[2025-09-11 04:52:03] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -84.138816-0.002697j
[2025-09-11 04:52:27] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -84.176199-0.002869j
[2025-09-11 04:52:50] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -84.297192+0.000433j
[2025-09-11 04:53:13] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -84.273055+0.000582j
[2025-09-11 04:53:37] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -84.223528-0.001804j
[2025-09-11 04:54:00] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -84.092060+0.003083j
[2025-09-11 04:54:23] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -84.251847+0.002426j
[2025-09-11 04:54:47] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -84.426095+0.001628j
[2025-09-11 04:55:10] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -84.232259-0.002610j
[2025-09-11 04:55:33] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -84.313375-0.000512j
[2025-09-11 04:55:57] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -84.365839+0.002662j
[2025-09-11 04:56:20] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -84.326990+0.001476j
[2025-09-11 04:56:43] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -84.434230-0.001006j
[2025-09-11 04:57:06] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -84.370132+0.000430j
[2025-09-11 04:57:30] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -84.244536+0.003312j
[2025-09-11 04:57:53] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -84.349426+0.002638j
[2025-09-11 04:58:16] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -84.456471+0.002155j
[2025-09-11 04:58:40] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -84.320227-0.000080j
[2025-09-11 04:59:03] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -84.271369+0.010617j
[2025-09-11 04:59:26] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -84.179242+0.002952j
[2025-09-11 04:59:50] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -84.156498+0.005815j
[2025-09-11 05:00:13] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -84.354188-0.002261j
[2025-09-11 05:00:36] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -84.343333-0.000495j
[2025-09-11 05:01:00] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -84.301275-0.001725j
[2025-09-11 05:01:23] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -84.413382-0.000689j
[2025-09-11 05:01:46] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -84.294373+0.003220j
[2025-09-11 05:02:09] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -84.271467+0.001484j
[2025-09-11 05:02:33] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -84.239309-0.002649j
[2025-09-11 05:02:56] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -84.305819-0.001398j
[2025-09-11 05:03:20] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -84.357060-0.000837j
[2025-09-11 05:03:43] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -84.334092-0.009138j
[2025-09-11 05:04:06] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -84.483526-0.001752j
[2025-09-11 05:04:29] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -84.520706-0.000550j
[2025-09-11 05:04:53] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -84.515556-0.001314j
[2025-09-11 05:05:16] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -84.256979+0.004643j
[2025-09-11 05:05:39] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -84.472479-0.001329j
[2025-09-11 05:06:02] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -84.418635-0.000911j
[2025-09-11 05:06:26] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -84.314757-0.000655j
[2025-09-11 05:06:49] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -84.288721+0.000632j
[2025-09-11 05:06:49] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-11 05:07:12] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -84.181478+0.005287j
[2025-09-11 05:07:36] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -84.135032+0.001378j
[2025-09-11 05:07:59] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -84.185929+0.005186j
[2025-09-11 05:08:22] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -84.348685+0.002083j
[2025-09-11 05:08:46] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -84.318658+0.003230j
[2025-09-11 05:09:09] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -84.360115+0.003272j
[2025-09-11 05:09:32] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -84.365151-0.003108j
[2025-09-11 05:09:56] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -84.414915-0.000695j
[2025-09-11 05:10:19] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -84.231797+0.001583j
[2025-09-11 05:10:42] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -84.375486-0.001825j
[2025-09-11 05:11:06] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -84.423302-0.001250j
[2025-09-11 05:11:29] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -84.448308+0.000601j
[2025-09-11 05:11:52] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -84.400451+0.002381j
[2025-09-11 05:12:15] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -84.472274-0.000467j
[2025-09-11 05:12:39] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -84.348489-0.016701j
[2025-09-11 05:13:02] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -84.388313+0.000594j
[2025-09-11 05:13:25] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -84.353130-0.007035j
[2025-09-11 05:13:49] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -84.281127+0.001133j
[2025-09-11 05:14:12] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -84.397434-0.002456j
[2025-09-11 05:14:35] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -84.294607+0.000627j
[2025-09-11 05:14:59] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -84.313168+0.004505j
[2025-09-11 05:15:22] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -84.359169+0.000774j
[2025-09-11 05:15:45] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -84.328918-0.003080j
[2025-09-11 05:16:09] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -84.282740-0.002470j
[2025-09-11 05:16:32] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -84.179791+0.000982j
[2025-09-11 05:16:55] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -84.176269-0.001543j
[2025-09-11 05:17:19] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -84.309936-0.003291j
[2025-09-11 05:17:42] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -84.223837+0.002154j
[2025-09-11 05:18:05] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -84.246358-0.000971j
[2025-09-11 05:18:29] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -84.220223+0.001649j
[2025-09-11 05:18:29] RESTART #2 | Period: 600
[2025-09-11 05:18:52] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -84.273363-0.000166j
[2025-09-11 05:19:15] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -84.273713+0.003174j
[2025-09-11 05:19:38] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -84.256430-0.000487j
[2025-09-11 05:20:02] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -84.224360-0.001761j
[2025-09-11 05:20:25] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -84.176888+0.001068j
[2025-09-11 05:20:48] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -84.229946-0.000112j
[2025-09-11 05:21:12] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -84.038102-0.001668j
[2025-09-11 05:21:35] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -84.062842+0.000216j
[2025-09-11 05:21:58] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -84.196098-0.002200j
[2025-09-11 05:22:22] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -84.161963-0.000699j
[2025-09-11 05:22:45] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -84.163633+0.002126j
[2025-09-11 05:23:08] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -84.245789-0.000587j
[2025-09-11 05:23:31] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -84.227961-0.006544j
[2025-09-11 05:23:55] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -84.272214-0.002900j
[2025-09-11 05:24:18] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -84.409574-0.003715j
[2025-09-11 05:24:41] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -84.229058-0.000211j
[2025-09-11 05:25:05] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -84.236261-0.001243j
[2025-09-11 05:25:28] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -84.360219-0.000273j
[2025-09-11 05:25:51] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -84.224561+0.001776j
[2025-09-11 05:26:15] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -84.433644-0.000290j
[2025-09-11 05:26:38] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -84.130992+0.001659j
[2025-09-11 05:27:01] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -84.196267-0.002160j
[2025-09-11 05:27:25] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -84.111610+0.000510j
[2025-09-11 05:27:48] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -84.180759-0.000881j
[2025-09-11 05:28:11] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -84.056469+0.000760j
[2025-09-11 05:28:35] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -84.256710-0.001769j
[2025-09-11 05:28:58] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -84.290830-0.001178j
[2025-09-11 05:29:21] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -84.348339+0.000728j
[2025-09-11 05:29:45] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -84.319520-0.000447j
[2025-09-11 05:30:08] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -84.381526-0.003637j
[2025-09-11 05:30:31] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -84.500688+0.001036j
[2025-09-11 05:30:54] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -84.292894+0.003673j
[2025-09-11 05:31:18] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -84.352221+0.000524j
[2025-09-11 05:31:41] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -84.234729+0.002443j
[2025-09-11 05:32:05] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -84.357180-0.001647j
[2025-09-11 05:32:28] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -84.313162-0.002641j
[2025-09-11 05:32:51] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -84.152156-0.001822j
[2025-09-11 05:33:15] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -84.136085-0.000582j
[2025-09-11 05:33:38] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -84.229172+0.000671j
[2025-09-11 05:34:01] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -84.262969-0.003050j
[2025-09-11 05:34:24] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -84.264454-0.000066j
[2025-09-11 05:34:48] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -84.337119+0.001541j
[2025-09-11 05:35:11] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -84.225538+0.003087j
[2025-09-11 05:35:34] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -84.332999+0.000061j
[2025-09-11 05:35:58] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -84.277230-0.003398j
[2025-09-11 05:36:21] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -84.331559-0.000289j
[2025-09-11 05:36:44] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -84.438160+0.000552j
[2025-09-11 05:37:08] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -84.103713-0.000815j
[2025-09-11 05:37:31] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -84.128864+0.001436j
[2025-09-11 05:37:54] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -84.363551-0.000284j
[2025-09-11 05:38:18] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -84.364641+0.004384j
[2025-09-11 05:38:41] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -84.252121-0.001064j
[2025-09-11 05:39:04] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -84.333429+0.000109j
[2025-09-11 05:39:28] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -84.357088-0.001948j
[2025-09-11 05:39:51] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -84.307076-0.005527j
[2025-09-11 05:40:14] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -84.260682-0.001943j
[2025-09-11 05:40:38] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -84.220915-0.000362j
[2025-09-11 05:41:01] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -84.148989+0.003006j
[2025-09-11 05:41:24] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -84.127500+0.004738j
[2025-09-11 05:41:48] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -84.047595-0.004877j
[2025-09-11 05:42:11] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -84.078866+0.003802j
[2025-09-11 05:42:34] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -84.135633-0.002009j
[2025-09-11 05:42:58] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -84.115111-0.003651j
[2025-09-11 05:43:21] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -84.191590+0.001281j
[2025-09-11 05:43:44] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -84.311072-0.005385j
[2025-09-11 05:44:07] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -84.245710+0.000944j
[2025-09-11 05:44:31] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -84.237180+0.000990j
[2025-09-11 05:44:54] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -84.226727-0.000846j
[2025-09-11 05:45:17] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -84.257458-0.002084j
[2025-09-11 05:45:40] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -84.021258+0.003165j
[2025-09-11 05:46:04] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -84.129047-0.003484j
[2025-09-11 05:46:27] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -84.128335+0.002409j
[2025-09-11 05:46:50] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -84.444203+0.004597j
[2025-09-11 05:47:14] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -84.356089-0.001378j
[2025-09-11 05:47:37] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -84.309941-0.001173j
[2025-09-11 05:47:37] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-11 05:48:00] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -84.139252+0.000300j
[2025-09-11 05:48:24] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -84.153046+0.001371j
[2025-09-11 05:48:47] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -84.188047-0.000028j
[2025-09-11 05:49:10] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -84.099557+0.001037j
[2025-09-11 05:49:34] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -84.292062-0.000506j
[2025-09-11 05:49:57] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -84.250884+0.001306j
[2025-09-11 05:50:20] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -84.174207+0.001979j
[2025-09-11 05:50:44] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -84.262096-0.002473j
[2025-09-11 05:51:07] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -83.992031-0.002407j
[2025-09-11 05:51:30] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -84.126871+0.001061j
[2025-09-11 05:51:54] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -84.184425+0.003600j
[2025-09-11 05:52:17] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -84.212114-0.003639j
[2025-09-11 05:52:40] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -84.284182-0.001238j
[2025-09-11 05:53:04] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -84.302269-0.001917j
[2025-09-11 05:53:27] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -84.293579-0.002937j
[2025-09-11 05:53:50] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -84.366854-0.003399j
[2025-09-11 05:54:13] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -84.212314+0.001440j
[2025-09-11 05:54:37] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -84.186495+0.001889j
[2025-09-11 05:55:00] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -84.254196+0.001054j
[2025-09-11 05:55:23] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -84.376485+0.000309j
[2025-09-11 05:55:47] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -84.363842-0.001130j
[2025-09-11 05:56:10] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -84.372675+0.001296j
[2025-09-11 05:56:33] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -84.354054-0.002348j
[2025-09-11 05:56:57] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -84.327787+0.000191j
[2025-09-11 05:57:20] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -84.292013-0.002007j
[2025-09-11 05:57:43] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -84.331367-0.000467j
[2025-09-11 05:58:07] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -84.258048+0.002641j
[2025-09-11 05:58:30] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -84.256032-0.001284j
[2025-09-11 05:58:53] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -84.202387+0.003211j
[2025-09-11 05:59:16] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -84.125191-0.000990j
[2025-09-11 05:59:40] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -84.230443+0.001129j
[2025-09-11 06:00:03] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -84.135395-0.000390j
[2025-09-11 06:00:26] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -84.122895+0.000901j
[2025-09-11 06:00:50] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -84.247390-0.001072j
[2025-09-11 06:01:13] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -84.322946-0.001781j
[2025-09-11 06:01:36] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -84.397879-0.002216j
[2025-09-11 06:02:00] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -84.290104-0.000047j
[2025-09-11 06:02:23] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -84.340599+0.001300j
[2025-09-11 06:02:46] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -84.261826-0.002319j
[2025-09-11 06:03:10] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -84.321825-0.003204j
[2025-09-11 06:03:33] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -84.284032-0.000623j
[2025-09-11 06:03:56] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -84.374410+0.001663j
[2025-09-11 06:04:19] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -84.298216+0.001599j
[2025-09-11 06:04:43] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -84.337879+0.000945j
[2025-09-11 06:05:06] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -84.322273+0.002664j
[2025-09-11 06:05:29] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -84.215464+0.000304j
[2025-09-11 06:05:53] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -84.286381+0.001131j
[2025-09-11 06:06:16] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -84.097186-0.000714j
[2025-09-11 06:06:39] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -84.131532+0.002363j
[2025-09-11 06:07:03] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -84.240534+0.000147j
[2025-09-11 06:07:26] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -84.395607+0.000541j
[2025-09-11 06:07:49] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -84.275804+0.001463j
[2025-09-11 06:08:13] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -84.143161-0.006569j
[2025-09-11 06:08:36] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -84.219758+0.001397j
[2025-09-11 06:08:59] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -84.237835-0.000699j
[2025-09-11 06:09:23] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -84.417406+0.000357j
[2025-09-11 06:09:46] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -84.297314-0.000561j
[2025-09-11 06:10:09] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -84.378956-0.000572j
[2025-09-11 06:10:32] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -84.458741-0.002972j
[2025-09-11 06:10:56] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -84.406429-0.000120j
[2025-09-11 06:11:19] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -84.188827+0.001952j
[2025-09-11 06:11:42] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -84.423675-0.000844j
[2025-09-11 06:12:06] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -84.426031-0.002161j
[2025-09-11 06:12:29] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -84.355463+0.001955j
[2025-09-11 06:12:52] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -84.195458+0.000740j
[2025-09-11 06:13:15] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -84.340782+0.000230j
[2025-09-11 06:13:39] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -84.259505-0.005096j
[2025-09-11 06:14:02] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -84.291175+0.003106j
[2025-09-11 06:14:25] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -84.368353+0.000949j
[2025-09-11 06:14:49] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -84.283434+0.000689j
[2025-09-11 06:15:12] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -84.264374-0.001351j
[2025-09-11 06:15:35] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -84.276318+0.002750j
[2025-09-11 06:15:59] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -84.416228-0.000777j
[2025-09-11 06:16:22] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -84.245811+0.000788j
[2025-09-11 06:16:45] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -84.323243+0.001171j
[2025-09-11 06:17:09] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -84.454328-0.001771j
[2025-09-11 06:17:32] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -84.360193+0.003108j
[2025-09-11 06:17:55] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -84.304566+0.002496j
[2025-09-11 06:18:19] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -84.305803-0.004376j
[2025-09-11 06:18:42] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -84.297116-0.000407j
[2025-09-11 06:19:05] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -84.367625+0.001454j
[2025-09-11 06:19:28] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -84.252970+0.002982j
[2025-09-11 06:19:52] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -84.451350-0.002603j
[2025-09-11 06:20:15] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -84.155871+0.001243j
[2025-09-11 06:20:38] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -84.314494-0.002770j
[2025-09-11 06:21:02] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -84.297034+0.000883j
[2025-09-11 06:21:25] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -84.389931-0.001223j
[2025-09-11 06:21:48] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -84.269659+0.002137j
[2025-09-11 06:22:12] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -84.325212+0.004935j
[2025-09-11 06:22:35] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -84.171049+0.001832j
[2025-09-11 06:22:58] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -84.292729-0.000619j
[2025-09-11 06:23:22] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -84.239649-0.001355j
[2025-09-11 06:23:45] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -84.312542-0.001041j
[2025-09-11 06:24:08] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -84.320799-0.003201j
[2025-09-11 06:24:31] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -84.352192-0.000533j
[2025-09-11 06:24:55] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -84.353095+0.000434j
[2025-09-11 06:25:18] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -84.280386-0.000609j
[2025-09-11 06:25:41] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -84.279462-0.001141j
[2025-09-11 06:26:05] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -84.273499-0.003202j
[2025-09-11 06:26:28] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -84.361036+0.004662j
[2025-09-11 06:26:51] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -84.238780-0.000714j
[2025-09-11 06:27:14] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -84.224279+0.001591j
[2025-09-11 06:27:38] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -84.400533+0.000406j
[2025-09-11 06:28:01] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -84.366177+0.003027j
[2025-09-11 06:28:24] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -84.458542+0.000350j
[2025-09-11 06:28:24] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-11 06:28:48] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -84.284557+0.001425j
[2025-09-11 06:29:11] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -84.241393+0.003650j
[2025-09-11 06:29:34] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -84.287594+0.003936j
[2025-09-11 06:29:58] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -84.184011+0.000230j
[2025-09-11 06:30:21] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -84.170067+0.001219j
[2025-09-11 06:30:44] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -84.229528+0.002215j
[2025-09-11 06:31:07] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -84.266119-0.001799j
[2025-09-11 06:31:31] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -84.319223-0.000738j
[2025-09-11 06:31:54] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -84.360431-0.002116j
[2025-09-11 06:32:17] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -84.341930+0.001520j
[2025-09-11 06:32:41] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -84.287083+0.002559j
[2025-09-11 06:33:04] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -84.241601-0.000448j
[2025-09-11 06:33:27] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -84.265410-0.004210j
[2025-09-11 06:33:51] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -84.335981-0.002178j
[2025-09-11 06:34:14] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -84.385591+0.000382j
[2025-09-11 06:34:37] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -84.253573-0.004470j
[2025-09-11 06:35:01] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -84.229171-0.002013j
[2025-09-11 06:35:24] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -84.214671+0.002504j
[2025-09-11 06:35:47] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -84.192934-0.000533j
[2025-09-11 06:36:11] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -84.132957+0.000963j
[2025-09-11 06:36:34] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -84.237287-0.001164j
[2025-09-11 06:36:57] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -84.145237-0.003937j
[2025-09-11 06:37:21] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -84.229315-0.003125j
[2025-09-11 06:37:44] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -84.296061+0.001074j
[2025-09-11 06:38:07] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -84.267417+0.003220j
[2025-09-11 06:38:30] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -84.216286+0.000803j
[2025-09-11 06:38:54] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -84.301533+0.000264j
[2025-09-11 06:39:17] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -84.340752+0.000417j
[2025-09-11 06:39:40] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -84.324106-0.002445j
[2025-09-11 06:40:04] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -84.317969-0.002527j
[2025-09-11 06:40:27] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -84.405747+0.003480j
[2025-09-11 06:40:50] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -84.245437+0.000432j
[2025-09-11 06:41:14] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -84.208028+0.001432j
[2025-09-11 06:41:37] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -84.347711+0.002770j
[2025-09-11 06:42:00] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -84.282692+0.000756j
[2025-09-11 06:42:24] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -84.282302+0.000842j
[2025-09-11 06:42:47] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -84.288467-0.000339j
[2025-09-11 06:43:10] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -84.231588-0.001287j
[2025-09-11 06:43:34] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -84.245787+0.000795j
[2025-09-11 06:43:57] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -84.310910+0.000068j
[2025-09-11 06:44:20] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -84.317615-0.000445j
[2025-09-11 06:44:43] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -84.174934-0.000077j
[2025-09-11 06:45:07] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -84.282570-0.005815j
[2025-09-11 06:45:30] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -84.458832+0.002126j
[2025-09-11 06:45:53] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -84.250380+0.002449j
[2025-09-11 06:46:17] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -84.173956-0.002229j
[2025-09-11 06:46:40] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -84.307045-0.000624j
[2025-09-11 06:47:03] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -84.143222+0.002757j
[2025-09-11 06:47:27] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -84.231859+0.000789j
[2025-09-11 06:47:50] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -84.017381-0.001598j
[2025-09-11 06:48:13] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -84.120819+0.001284j
[2025-09-11 06:48:37] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -84.156327-0.001246j
[2025-09-11 06:48:49] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -84.316046-0.003908j
[2025-09-11 06:48:59] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -84.462353-0.001149j
[2025-09-11 06:49:10] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -84.437937+0.000219j
[2025-09-11 06:49:20] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -84.481609+0.001934j
[2025-09-11 06:49:31] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -84.550497+0.001110j
[2025-09-11 06:49:41] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -84.441458-0.000195j
[2025-09-11 06:49:52] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -84.243995-0.000598j
[2025-09-11 06:50:02] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -84.227548-0.000720j
[2025-09-11 06:50:13] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -84.355721+0.000116j
[2025-09-11 06:50:24] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -84.296440+0.001444j
[2025-09-11 06:50:34] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -84.330821+0.003847j
[2025-09-11 06:50:45] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -84.139963+0.001249j
[2025-09-11 06:50:55] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -84.106588+0.001690j
[2025-09-11 06:51:06] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -84.246649-0.000604j
[2025-09-11 06:51:16] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -84.241595+0.000670j
[2025-09-11 06:51:27] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -84.363751-0.000069j
[2025-09-11 06:51:37] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -84.342136+0.002204j
[2025-09-11 06:51:48] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -84.208035+0.001268j
[2025-09-11 06:51:58] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -84.322628+0.001777j
[2025-09-11 06:52:09] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -84.397956+0.001813j
[2025-09-11 06:52:20] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -84.288666+0.000466j
[2025-09-11 06:52:30] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -84.336842-0.003962j
[2025-09-11 06:52:41] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -84.327705-0.001943j
[2025-09-11 06:52:51] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -84.396469-0.001239j
[2025-09-11 06:53:02] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -84.476252+0.001146j
[2025-09-11 06:53:12] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -84.432753+0.005448j
[2025-09-11 06:53:23] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -84.432479-0.000521j
[2025-09-11 06:53:33] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -84.357665+0.000219j
[2025-09-11 06:53:44] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -84.326813-0.001057j
[2025-09-11 06:53:54] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -84.277907+0.001424j
[2025-09-11 06:54:05] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -84.278180-0.002280j
[2025-09-11 06:54:15] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -84.320153-0.002504j
[2025-09-11 06:54:26] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -84.320283-0.000532j
[2025-09-11 06:54:36] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -84.330870+0.001712j
[2025-09-11 06:54:47] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -84.244301-0.005281j
[2025-09-11 06:54:58] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -84.246742-0.001013j
[2025-09-11 06:55:08] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -84.344393-0.001920j
[2025-09-11 06:55:19] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -84.318142-0.002077j
[2025-09-11 06:55:29] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -84.427892+0.000321j
[2025-09-11 06:55:40] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -84.379815-0.004008j
[2025-09-11 06:55:50] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -84.328104+0.001502j
[2025-09-11 06:56:01] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -84.414013+0.004076j
[2025-09-11 06:56:11] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -84.290204+0.001673j
[2025-09-11 06:56:22] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -84.297191-0.000830j
[2025-09-11 06:56:32] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -84.375537-0.001995j
[2025-09-11 06:56:43] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -84.316635+0.003450j
[2025-09-11 06:56:53] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -84.345922-0.000054j
[2025-09-11 06:57:04] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -84.319739-0.000672j
[2025-09-11 06:57:14] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -84.465648+0.002105j
[2025-09-11 06:57:25] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -84.422892+0.001535j
[2025-09-11 06:57:36] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -84.382155-0.003186j
[2025-09-11 06:57:46] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -84.456249-0.003049j
[2025-09-11 06:57:57] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -84.401236+0.001834j
[2025-09-11 06:57:57] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-11 06:58:07] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -84.489354+0.000958j
[2025-09-11 06:58:18] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -84.299561+0.002132j
[2025-09-11 06:58:28] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -84.284062-0.001925j
[2025-09-11 06:58:39] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -84.355141+0.002051j
[2025-09-11 06:58:49] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -84.145432-0.001749j
[2025-09-11 06:59:00] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -84.054823-0.003182j
[2025-09-11 06:59:10] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -84.087097+0.000839j
[2025-09-11 06:59:21] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -84.386540-0.000313j
[2025-09-11 06:59:32] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -84.255983+0.004199j
[2025-09-11 06:59:42] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -84.174441-0.001489j
[2025-09-11 06:59:53] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -84.076310+0.001123j
[2025-09-11 07:00:03] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -84.093729-0.000195j
[2025-09-11 07:00:14] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -84.091877-0.002952j
[2025-09-11 07:00:24] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -84.161843-0.001541j
[2025-09-11 07:00:35] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -84.077112-0.000011j
[2025-09-11 07:00:45] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -84.197107-0.001958j
[2025-09-11 07:00:56] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -84.251731+0.000065j
[2025-09-11 07:01:06] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -84.182526+0.002586j
[2025-09-11 07:01:17] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -84.201290-0.002510j
[2025-09-11 07:01:27] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -84.125012+0.002009j
[2025-09-11 07:01:38] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -84.272838+0.003167j
[2025-09-11 07:01:48] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -84.238477+0.005333j
[2025-09-11 07:01:59] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -84.318937-0.000579j
[2025-09-11 07:02:10] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -84.268847+0.001770j
[2025-09-11 07:02:20] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -84.374423-0.003765j
[2025-09-11 07:02:31] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -84.460471+0.003197j
[2025-09-11 07:02:41] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -84.381809+0.000023j
[2025-09-11 07:02:52] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -84.436824-0.000063j
[2025-09-11 07:03:02] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -84.387220-0.003126j
[2025-09-11 07:03:13] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -84.373995+0.002807j
[2025-09-11 07:03:23] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -84.334423-0.001499j
[2025-09-11 07:03:34] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -84.390331-0.004135j
[2025-09-11 07:03:44] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -84.418863-0.002238j
[2025-09-11 07:03:55] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -84.249433+0.000606j
[2025-09-11 07:04:05] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -84.394819+0.000454j
[2025-09-11 07:04:16] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -84.272173+0.004020j
[2025-09-11 07:04:27] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -84.308626-0.001250j
[2025-09-11 07:04:37] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -84.440377-0.000003j
[2025-09-11 07:04:48] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -84.212965+0.004765j
[2025-09-11 07:04:58] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -84.382226+0.003567j
[2025-09-11 07:05:09] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -84.197777-0.003896j
[2025-09-11 07:05:19] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -84.419991-0.003337j
[2025-09-11 07:05:30] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -84.320145+0.000584j
[2025-09-11 07:05:40] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -84.303206+0.001412j
[2025-09-11 07:05:51] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -84.429641-0.000821j
[2025-09-11 07:06:01] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -84.462348-0.001741j
[2025-09-11 07:06:12] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -84.544308+0.006243j
[2025-09-11 07:06:22] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -84.522120+0.001793j
[2025-09-11 07:06:33] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -84.559399+0.000784j
[2025-09-11 07:06:43] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -84.559116-0.000135j
[2025-09-11 07:06:54] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -84.383705+0.001627j
[2025-09-11 07:07:05] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -84.159367+0.002042j
[2025-09-11 07:07:15] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -84.384310-0.002449j
[2025-09-11 07:07:26] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -84.219828+0.005496j
[2025-09-11 07:07:36] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -84.296988+0.006235j
[2025-09-11 07:07:47] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -84.429370+0.000443j
[2025-09-11 07:07:57] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -84.426921-0.001447j
[2025-09-11 07:08:08] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -84.423788+0.002506j
[2025-09-11 07:08:18] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -84.409713-0.000934j
[2025-09-11 07:08:29] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -84.243505+0.002314j
[2025-09-11 07:08:39] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -84.322580+0.001212j
[2025-09-11 07:08:50] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -84.060361-0.004094j
[2025-09-11 07:09:01] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -84.201200+0.003333j
[2025-09-11 07:09:11] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -84.102112+0.001830j
[2025-09-11 07:09:22] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -84.179637-0.004014j
[2025-09-11 07:09:32] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -84.181119-0.004035j
[2025-09-11 07:09:43] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -84.255750+0.004708j
[2025-09-11 07:09:53] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -84.393257-0.000522j
[2025-09-11 07:10:04] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -84.331437+0.002363j
[2025-09-11 07:10:14] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -84.356646-0.001797j
[2025-09-11 07:10:25] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -84.343313-0.000440j
[2025-09-11 07:10:35] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -84.395269+0.002269j
[2025-09-11 07:10:46] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -84.386246+0.005088j
[2025-09-11 07:10:56] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -84.371360+0.000907j
[2025-09-11 07:11:07] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -84.310224-0.005039j
[2025-09-11 07:11:18] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -84.268572-0.000164j
[2025-09-11 07:11:28] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -84.286983-0.004024j
[2025-09-11 07:11:39] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -84.249405-0.003086j
[2025-09-11 07:11:49] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -84.207827-0.001353j
[2025-09-11 07:12:00] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -84.258137+0.002578j
[2025-09-11 07:12:10] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -84.298526+0.001960j
[2025-09-11 07:12:21] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -84.232718-0.001568j
[2025-09-11 07:12:31] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -84.239658+0.003063j
[2025-09-11 07:12:42] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -84.346257+0.001695j
[2025-09-11 07:12:52] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -84.369380-0.001431j
[2025-09-11 07:13:03] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -84.410559+0.005890j
[2025-09-11 07:13:13] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -84.469262+0.001335j
[2025-09-11 07:13:24] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -84.236862-0.001319j
[2025-09-11 07:13:34] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -84.352211-0.000318j
[2025-09-11 07:13:45] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -84.362188-0.000348j
[2025-09-11 07:13:56] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -84.170626-0.001321j
[2025-09-11 07:14:06] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -84.277798+0.000064j
[2025-09-11 07:14:17] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -84.169129+0.001585j
[2025-09-11 07:14:27] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -84.138430+0.003007j
[2025-09-11 07:14:38] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -84.146798-0.000758j
[2025-09-11 07:14:48] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -84.190384-0.000934j
[2025-09-11 07:14:59] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -84.182219-0.000534j
[2025-09-11 07:15:09] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -84.225559-0.000193j
[2025-09-11 07:15:20] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -84.215427-0.004043j
[2025-09-11 07:15:30] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -84.201793+0.002463j
[2025-09-11 07:15:41] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -84.115145+0.001026j
[2025-09-11 07:15:51] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -84.195051-0.005399j
[2025-09-11 07:16:02] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -84.348209-0.001136j
[2025-09-11 07:16:12] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -84.286857-0.003099j
[2025-09-11 07:16:23] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -84.211418-0.000221j
[2025-09-11 07:16:23] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-11 07:16:34] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -84.013302-0.003297j
[2025-09-11 07:16:44] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -84.117357-0.000617j
[2025-09-11 07:16:55] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -84.174965-0.000947j
[2025-09-11 07:17:05] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -84.184284-0.000396j
[2025-09-11 07:17:16] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -84.244264+0.003062j
[2025-09-11 07:17:26] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -84.222437-0.000103j
[2025-09-11 07:17:37] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -84.031499+0.004088j
[2025-09-11 07:17:47] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -84.227847+0.000630j
[2025-09-11 07:17:58] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -84.244887+0.001687j
[2025-09-11 07:18:08] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -84.160293-0.000865j
[2025-09-11 07:18:19] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -84.117447+0.000158j
[2025-09-11 07:18:29] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -84.325465+0.000773j
[2025-09-11 07:18:40] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -84.349779-0.002892j
[2025-09-11 07:18:51] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -84.338675+0.004074j
[2025-09-11 07:19:01] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -84.234935+0.003362j
[2025-09-11 07:19:12] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -84.178328+0.002272j
[2025-09-11 07:19:22] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -84.148121-0.002345j
[2025-09-11 07:19:33] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -84.112243-0.002223j
[2025-09-11 07:19:43] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -84.188930-0.001717j
[2025-09-11 07:19:54] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -84.275527-0.000264j
[2025-09-11 07:20:04] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -84.332645-0.002307j
[2025-09-11 07:20:15] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -84.397858+0.006454j
[2025-09-11 07:20:25] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -84.333042-0.006652j
[2025-09-11 07:20:36] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -84.310437-0.004152j
[2025-09-11 07:20:47] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -84.349761-0.001309j
[2025-09-11 07:20:57] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -84.157694-0.002341j
[2025-09-11 07:21:08] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -84.334532+0.004859j
[2025-09-11 07:21:18] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -84.400715-0.005363j
[2025-09-11 07:21:29] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -84.259177-0.005693j
[2025-09-11 07:21:39] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -84.361087-0.001353j
[2025-09-11 07:21:50] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -84.331145-0.005236j
[2025-09-11 07:22:00] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -84.396340-0.001458j
[2025-09-11 07:22:11] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -84.260591-0.005247j
[2025-09-11 07:22:21] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -84.277278+0.001981j
[2025-09-11 07:22:32] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -84.279639-0.002773j
[2025-09-11 07:22:42] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -84.310233+0.003678j
[2025-09-11 07:22:53] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -84.361013+0.004232j
[2025-09-11 07:23:03] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -84.516947-0.000572j
[2025-09-11 07:23:14] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -84.549683+0.000674j
[2025-09-11 07:23:25] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -84.408746-0.002432j
[2025-09-11 07:23:35] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -84.412028+0.000110j
[2025-09-11 07:23:46] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -84.364471+0.000643j
[2025-09-11 07:23:56] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -84.342298-0.003333j
[2025-09-11 07:24:07] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -84.461748-0.001290j
[2025-09-11 07:24:17] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -84.417244+0.001191j
[2025-09-11 07:24:28] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -84.446206+0.000581j
[2025-09-11 07:24:38] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -84.361778-0.001791j
[2025-09-11 07:24:49] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -84.364843+0.002191j
[2025-09-11 07:24:59] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -84.373250+0.003550j
[2025-09-11 07:25:10] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -84.274270+0.002425j
[2025-09-11 07:25:20] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -84.277997+0.000830j
[2025-09-11 07:25:31] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -84.244870+0.000413j
[2025-09-11 07:25:42] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -84.211141+0.001007j
[2025-09-11 07:25:52] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -84.405715-0.004137j
[2025-09-11 07:26:03] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -84.453312-0.001538j
[2025-09-11 07:26:13] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -84.330381+0.001284j
[2025-09-11 07:26:24] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -84.434339-0.000405j
[2025-09-11 07:26:34] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -84.306254+0.003445j
[2025-09-11 07:26:45] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -84.397593+0.000252j
[2025-09-11 07:26:55] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -84.398638-0.002561j
[2025-09-11 07:27:06] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -84.258269-0.001118j
[2025-09-11 07:27:16] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -84.311970-0.000660j
[2025-09-11 07:27:27] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -84.349719-0.001416j
[2025-09-11 07:27:38] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -84.243558-0.002847j
[2025-09-11 07:27:48] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -84.236005-0.003000j
[2025-09-11 07:27:59] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -84.325062+0.000184j
[2025-09-11 07:28:09] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -84.355393-0.000413j
[2025-09-11 07:28:20] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -84.496622-0.000485j
[2025-09-11 07:28:30] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -84.308593+0.000489j
[2025-09-11 07:28:41] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -84.311344-0.000122j
[2025-09-11 07:28:51] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -84.235229+0.006026j
[2025-09-11 07:29:02] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -84.147934-0.003125j
[2025-09-11 07:29:12] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -84.139286-0.002946j
[2025-09-11 07:29:23] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -84.246025-0.000316j
[2025-09-11 07:29:33] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -84.183615-0.000138j
[2025-09-11 07:29:44] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -84.146670+0.000163j
[2025-09-11 07:29:55] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -84.136550+0.003007j
[2025-09-11 07:30:05] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -84.185900-0.001609j
[2025-09-11 07:30:16] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -84.290736-0.003106j
[2025-09-11 07:30:26] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -84.392742-0.001316j
[2025-09-11 07:30:37] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -84.255692+0.002736j
[2025-09-11 07:30:47] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -84.353203+0.000248j
[2025-09-11 07:30:58] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -84.380318+0.000364j
[2025-09-11 07:31:08] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -84.363401+0.000947j
[2025-09-11 07:31:19] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -84.368654-0.001093j
[2025-09-11 07:31:29] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -84.420670-0.000101j
[2025-09-11 07:31:40] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -84.390451+0.000220j
[2025-09-11 07:31:50] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -84.252448-0.002678j
[2025-09-11 07:32:01] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -84.308558-0.002025j
[2025-09-11 07:32:12] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -84.278242-0.002472j
[2025-09-11 07:32:22] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -84.350993+0.000017j
[2025-09-11 07:32:33] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -84.111545+0.000768j
[2025-09-11 07:32:43] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -84.248186+0.003219j
[2025-09-11 07:32:54] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -84.132034+0.000927j
[2025-09-11 07:33:04] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -84.067393-0.000336j
[2025-09-11 07:33:15] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -84.156007-0.002031j
[2025-09-11 07:33:25] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -84.241961-0.001482j
[2025-09-11 07:33:36] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -84.286448-0.003824j
[2025-09-11 07:33:46] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -84.271695+0.000592j
[2025-09-11 07:33:57] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -84.257313-0.001998j
[2025-09-11 07:34:07] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -84.112564+0.003010j
[2025-09-11 07:34:18] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -84.125676-0.003892j
[2025-09-11 07:34:28] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -84.143491+0.000658j
[2025-09-11 07:34:39] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -84.199705-0.000223j
[2025-09-11 07:34:50] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -84.315022+0.000853j
[2025-09-11 07:34:50] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-11 07:35:00] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -84.278847+0.000623j
[2025-09-11 07:35:11] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -84.331908-0.000443j
[2025-09-11 07:35:21] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -84.185100+0.001079j
[2025-09-11 07:35:32] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -84.102798-0.001980j
[2025-09-11 07:35:42] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -84.218254-0.001998j
[2025-09-11 07:35:53] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -84.162256-0.002203j
[2025-09-11 07:36:03] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -84.193781+0.001693j
[2025-09-11 07:36:14] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -84.301237-0.002773j
[2025-09-11 07:36:24] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -84.209543-0.000242j
[2025-09-11 07:36:35] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -84.222036+0.003197j
[2025-09-11 07:36:46] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -84.339466+0.005306j
[2025-09-11 07:36:56] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -84.220725+0.002853j
[2025-09-11 07:37:07] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -84.193285+0.003342j
[2025-09-11 07:37:17] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -84.132290-0.000591j
[2025-09-11 07:37:28] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -84.162496-0.001552j
[2025-09-11 07:37:38] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -84.174491-0.002003j
[2025-09-11 07:37:49] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -84.190516+0.001993j
[2025-09-11 07:37:59] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -84.202185-0.001900j
[2025-09-11 07:38:10] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -84.083191+0.000044j
[2025-09-11 07:38:20] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -84.173293+0.002165j
[2025-09-11 07:38:31] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -84.168156+0.001609j
[2025-09-11 07:38:41] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -84.182494+0.001437j
[2025-09-11 07:38:52] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -84.334610+0.002200j
[2025-09-11 07:39:03] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -84.377548+0.001003j
[2025-09-11 07:39:13] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -84.354289+0.000197j
[2025-09-11 07:39:24] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -84.278739+0.001206j
[2025-09-11 07:39:34] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -84.045237+0.001296j
[2025-09-11 07:39:45] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -84.199616-0.000211j
[2025-09-11 07:39:55] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -84.153435+0.002106j
[2025-09-11 07:40:06] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -84.165382+0.000470j
[2025-09-11 07:40:16] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -84.149819+0.015061j
[2025-09-11 07:40:27] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -84.226622+0.000889j
[2025-09-11 07:40:37] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -84.263050-0.001782j
[2025-09-11 07:40:48] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -84.222058+0.000488j
[2025-09-11 07:40:58] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -84.240275+0.001917j
[2025-09-11 07:41:09] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -84.289106+0.000215j
[2025-09-11 07:41:20] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -84.331943+0.000619j
[2025-09-11 07:41:30] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -84.339949-0.000666j
[2025-09-11 07:41:41] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -84.198653+0.001894j
[2025-09-11 07:41:51] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -84.178516+0.002407j
[2025-09-11 07:42:02] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -84.339033-0.002254j
[2025-09-11 07:42:12] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -84.294659-0.001799j
[2025-09-11 07:42:23] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -84.380489-0.000279j
[2025-09-11 07:42:33] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -84.243928+0.001746j
[2025-09-11 07:42:44] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -84.347964-0.002971j
[2025-09-11 07:42:54] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -84.252179-0.000823j
[2025-09-11 07:43:05] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -84.178901-0.000199j
[2025-09-11 07:43:15] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -84.100989+0.001177j
[2025-09-11 07:43:26] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -84.199090+0.001546j
[2025-09-11 07:43:37] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -84.327503+0.001180j
[2025-09-11 07:43:47] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -84.155371-0.000023j
[2025-09-11 07:43:58] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -84.047531+0.002420j
[2025-09-11 07:44:08] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -83.993509+0.000230j
[2025-09-11 07:44:19] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -84.206521+0.001809j
[2025-09-11 07:44:29] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -84.175562+0.001527j
[2025-09-11 07:44:40] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -84.280891-0.003363j
[2025-09-11 07:44:50] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -84.221181+0.002915j
[2025-09-11 07:45:01] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -84.363299+0.000345j
[2025-09-11 07:45:11] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -84.384419-0.003656j
[2025-09-11 07:45:22] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -84.257551-0.001934j
[2025-09-11 07:45:32] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -84.395176-0.000320j
[2025-09-11 07:45:43] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -84.270894-0.002413j
[2025-09-11 07:45:53] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -84.407222-0.001654j
[2025-09-11 07:46:04] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -84.091532+0.001411j
[2025-09-11 07:46:15] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -84.029648-0.000411j
[2025-09-11 07:46:25] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -84.135345-0.000246j
[2025-09-11 07:46:36] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -84.382188+0.000478j
[2025-09-11 07:46:46] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -84.367258+0.001189j
[2025-09-11 07:46:57] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -84.365526+0.001592j
[2025-09-11 07:47:07] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -84.334648-0.000240j
[2025-09-11 07:47:18] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -84.245341+0.001859j
[2025-09-11 07:47:28] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -84.256648+0.001392j
[2025-09-11 07:47:39] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -84.422123+0.002124j
[2025-09-11 07:47:49] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -84.381458-0.002100j
[2025-09-11 07:48:00] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -84.538790-0.004768j
[2025-09-11 07:48:11] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -84.443340-0.001383j
[2025-09-11 07:48:21] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -84.309504+0.000683j
[2025-09-11 07:48:32] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -84.274033-0.000212j
[2025-09-11 07:48:42] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -84.301953+0.000282j
[2025-09-11 07:48:53] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -84.335366+0.002475j
[2025-09-11 07:49:03] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -84.250281+0.002568j
[2025-09-11 07:49:14] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -84.321245-0.001042j
[2025-09-11 07:49:24] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -84.286755+0.002497j
[2025-09-11 07:49:35] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -84.307674+0.000830j
[2025-09-11 07:49:45] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -84.270157-0.000805j
[2025-09-11 07:49:56] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -84.378554+0.004121j
[2025-09-11 07:50:06] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -84.347771+0.000116j
[2025-09-11 07:50:17] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -84.248660-0.001077j
[2025-09-11 07:50:28] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -84.145401+0.001903j
[2025-09-11 07:50:38] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -84.286352+0.000214j
[2025-09-11 07:50:49] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -84.221264+0.003107j
[2025-09-11 07:50:59] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -84.348590-0.001422j
[2025-09-11 07:51:10] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -84.384029+0.002833j
[2025-09-11 07:51:20] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -84.385272-0.001600j
[2025-09-11 07:51:31] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -84.361618+0.000607j
[2025-09-11 07:51:41] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -84.446957-0.000141j
[2025-09-11 07:51:52] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -84.349393-0.002849j
[2025-09-11 07:52:02] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -84.269553-0.000551j
[2025-09-11 07:52:13] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -84.216387-0.003529j
[2025-09-11 07:52:23] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -84.379008+0.000805j
[2025-09-11 07:52:34] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -84.358499-0.001244j
[2025-09-11 07:52:44] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -84.194859-0.002052j
[2025-09-11 07:52:55] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -84.271002+0.000790j
[2025-09-11 07:53:06] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -84.487011+0.000128j
[2025-09-11 07:53:16] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -84.402051-0.000061j
[2025-09-11 07:53:16] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-11 07:53:16] ✅ Training completed | Restarts: 2
[2025-09-11 07:53:16] ============================================================
[2025-09-11 07:53:16] Training completed | Runtime: 19877.2s
[2025-09-11 07:53:21] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-11 07:53:21] ============================================================
