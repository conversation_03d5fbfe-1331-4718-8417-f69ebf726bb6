[2025-09-09 10:36:42] ✓ 从checkpoint恢复: results/L=5/J2=0.05/J1=0.05/training/checkpoints/final_GCNN.pkl
[2025-09-09 10:36:42]   - 迭代次数: final
[2025-09-09 10:36:42]   - 能量: -81.152107+0.000003j ± 0.054953
[2025-09-09 10:36:42]   - 时间戳: 2025-09-09T04:43:31.695992+08:00
[2025-09-09 10:37:11] ✓ 变分状态参数已从checkpoint恢复
[2025-09-09 10:37:11] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-09 10:37:11] ==================================================
[2025-09-09 10:37:11] GCNN for Shastry-Sutherland Model
[2025-09-09 10:37:11] ==================================================
[2025-09-09 10:37:11] System parameters:
[2025-09-09 10:37:11]   - System size: L=5, N=100
[2025-09-09 10:37:12]   - System parameters: J1=0.06, J2=0.05, Q=0.95
[2025-09-09 10:37:12] --------------------------------------------------
[2025-09-09 10:37:12] Model parameters:
[2025-09-09 10:37:12]   - Number of layers = 4
[2025-09-09 10:37:12]   - Number of features = 4
[2025-09-09 10:37:12]   - Total parameters = 19628
[2025-09-09 10:37:12] --------------------------------------------------
[2025-09-09 10:37:12] Training parameters:
[2025-09-09 10:37:12]   - Learning rate: 0.015
[2025-09-09 10:37:12]   - Total iterations: 1050
[2025-09-09 10:37:12]   - Annealing cycles: 3
[2025-09-09 10:37:12]   - Initial period: 150
[2025-09-09 10:37:12]   - Period multiplier: 2.0
[2025-09-09 10:37:12]   - Temperature range: 0.0-1.0
[2025-09-09 10:37:12]   - Samples: 4096
[2025-09-09 10:37:12]   - Discarded samples: 0
[2025-09-09 10:37:12]   - Chunk size: 2048
[2025-09-09 10:37:12]   - Diagonal shift: 0.2
[2025-09-09 10:37:12]   - Gradient clipping: 1.0
[2025-09-09 10:37:12]   - Checkpoint enabled: interval=105
[2025-09-09 10:37:12]   - Checkpoint directory: results/L=5/J2=0.05/J1=0.06/training/checkpoints
[2025-09-09 10:37:12] --------------------------------------------------
[2025-09-09 10:37:12] Device status:
[2025-09-09 10:37:12]   - Devices model: NVIDIA H200 NVL
[2025-09-09 10:37:12]   - Number of devices: 1
[2025-09-09 10:37:12]   - Sharding: True
[2025-09-09 10:37:12] ============================================================
[2025-09-09 10:39:27] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -82.132367-0.006027j
[2025-09-09 10:40:59] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -81.943687+0.001508j
[2025-09-09 10:41:34] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -81.882584-0.002453j
[2025-09-09 10:42:08] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -82.028497+0.001313j
[2025-09-09 10:42:43] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -81.856807-0.001793j
[2025-09-09 10:43:18] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -82.016654-0.001588j
[2025-09-09 10:43:52] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -82.070456-0.002648j
[2025-09-09 10:44:27] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -81.980067-0.009440j
[2025-09-09 10:45:02] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -81.880095-0.003137j
[2025-09-09 10:45:36] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -81.931121+0.003856j
[2025-09-09 10:46:11] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -82.053228-0.000687j
[2025-09-09 10:46:46] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -82.076649-0.001578j
[2025-09-09 10:47:20] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -82.028345+0.001806j
[2025-09-09 10:47:55] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -82.078207-0.002881j
[2025-09-09 10:48:29] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -82.029591-0.004471j
[2025-09-09 10:49:04] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -82.026466-0.006432j
[2025-09-09 10:49:39] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -81.956052-0.001565j
[2025-09-09 10:50:14] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -81.904789-0.001776j
[2025-09-09 10:50:48] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -81.731095-0.004361j
[2025-09-09 10:51:23] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -81.828044-0.004323j
[2025-09-09 10:51:58] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -81.935656-0.002094j
[2025-09-09 10:52:32] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -81.851519-0.003105j
[2025-09-09 10:53:07] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -81.766966-0.000221j
[2025-09-09 10:53:42] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -81.731574-0.005114j
[2025-09-09 10:54:17] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -81.626274-0.010493j
[2025-09-09 10:54:51] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -81.740908-0.002796j
[2025-09-09 10:55:26] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -81.726030-0.002949j
[2025-09-09 10:56:01] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -81.697012-0.000590j
[2025-09-09 10:56:35] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -81.621276-0.000883j
[2025-09-09 10:57:10] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -81.591640-0.000590j
[2025-09-09 10:57:45] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -81.603783+0.001377j
[2025-09-09 10:58:19] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -81.536126-0.006815j
[2025-09-09 10:58:54] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -81.749638+0.002849j
[2025-09-09 10:59:29] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -81.660079+0.006079j
[2025-09-09 11:00:04] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -81.728853+0.000415j
[2025-09-09 11:00:39] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -81.775863+0.002107j
[2025-09-09 11:01:13] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -81.779484+0.001803j
[2025-09-09 11:01:48] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -81.911670-0.003837j
[2025-09-09 11:02:22] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -81.956613-0.007689j
[2025-09-09 11:02:57] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -81.836238+0.009523j
[2025-09-09 11:03:32] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -81.816990-0.002833j
[2025-09-09 11:04:06] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -81.849854+0.000558j
[2025-09-09 11:04:41] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -81.840529-0.005780j
[2025-09-09 11:05:16] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -81.819424+0.000590j
[2025-09-09 11:05:50] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -81.814467+0.000363j
[2025-09-09 11:06:25] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -81.835175-0.005551j
[2025-09-09 11:07:00] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -81.701352-0.001333j
[2025-09-09 11:07:34] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -81.675381-0.005010j
[2025-09-09 11:08:09] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -81.730648-0.000554j
[2025-09-09 11:08:44] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -81.644224-0.002584j
[2025-09-09 11:09:18] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -81.770431-0.003776j
[2025-09-09 11:09:53] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -81.612976+0.005814j
[2025-09-09 11:10:28] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -81.658502+0.000485j
[2025-09-09 11:11:02] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -81.708470+0.007068j
[2025-09-09 11:11:37] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -81.839345-0.003539j
[2025-09-09 11:12:12] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -81.888219-0.000342j
[2025-09-09 11:12:46] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -81.900501-0.006132j
[2025-09-09 11:13:21] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -81.822552+0.002788j
[2025-09-09 11:13:56] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -82.030888-0.008946j
[2025-09-09 11:14:30] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -81.812026+0.004003j
[2025-09-09 11:15:05] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -81.850979-0.001302j
[2025-09-09 11:15:40] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -81.889458+0.000703j
[2025-09-09 11:16:14] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -81.757676-0.006505j
[2025-09-09 11:16:47] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -81.757054-0.003662j
[2025-09-09 11:17:22] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -81.653814+0.000133j
[2025-09-09 11:17:57] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -81.732268-0.001610j
[2025-09-09 11:18:31] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -81.679970-0.005646j
[2025-09-09 11:19:06] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -81.669343+0.001422j
[2025-09-09 11:19:41] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -81.665987+0.003634j
[2025-09-09 11:20:15] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -81.530997+0.003498j
[2025-09-09 11:20:50] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -81.658011-0.003443j
[2025-09-09 11:21:25] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -81.711729+0.000781j
[2025-09-09 11:21:59] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -81.722638-0.000011j
[2025-09-09 11:22:34] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -81.794176-0.000570j
[2025-09-09 11:23:09] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -81.897854+0.007779j
[2025-09-09 11:23:43] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -81.857999-0.002544j
[2025-09-09 11:24:18] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -81.812111-0.001495j
[2025-09-09 11:24:53] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -81.710548-0.000254j
[2025-09-09 11:25:27] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -81.887095+0.000834j
[2025-09-09 11:26:02] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -81.904792-0.003349j
[2025-09-09 11:26:37] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -81.847036-0.004288j
[2025-09-09 11:27:12] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -81.617596+0.006631j
[2025-09-09 11:27:46] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -81.623682-0.002282j
[2025-09-09 11:28:21] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -81.652021+0.003140j
[2025-09-09 11:28:56] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -81.730808+0.005251j
[2025-09-09 11:29:30] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -81.751296-0.007593j
[2025-09-09 11:30:05] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -81.640956-0.001639j
[2025-09-09 11:30:40] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -81.630048-0.003658j
[2025-09-09 11:31:14] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -81.715271-0.002423j
[2025-09-09 11:31:49] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -81.769216-0.000191j
[2025-09-09 11:32:24] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -81.632027+0.002336j
[2025-09-09 11:32:58] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -81.764800-0.001623j
[2025-09-09 11:33:33] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -81.827682+0.004565j
[2025-09-09 11:34:08] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -81.785067+0.001691j
[2025-09-09 11:34:43] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -81.685218+0.000315j
[2025-09-09 11:35:17] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -81.677080+0.003550j
[2025-09-09 11:35:52] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -81.896425-0.001999j
[2025-09-09 11:36:27] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -82.057141-0.007193j
[2025-09-09 11:37:01] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -81.833891+0.000091j
[2025-09-09 11:37:36] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -81.693347-0.001401j
[2025-09-09 11:38:11] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -81.816299-0.004687j
[2025-09-09 11:38:46] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -81.830787-0.000972j
[2025-09-09 11:39:20] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -81.648854-0.004357j
[2025-09-09 11:39:55] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -81.703430-0.001388j
[2025-09-09 11:40:30] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -81.819658-0.001893j
[2025-09-09 11:40:30] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-09 11:41:04] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -81.697472+0.004924j
[2025-09-09 11:41:39] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -81.717190-0.002774j
[2025-09-09 11:42:14] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -81.720964-0.004585j
[2025-09-09 11:42:48] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -81.772976-0.004989j
[2025-09-09 11:43:23] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -81.696190+0.000281j
[2025-09-09 11:43:58] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -81.616270-0.005882j
[2025-09-09 11:44:32] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -81.723047+0.002445j
[2025-09-09 11:45:07] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -81.641254+0.000428j
[2025-09-09 11:45:42] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -81.792931-0.004990j
[2025-09-09 11:46:16] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -81.925475-0.004210j
[2025-09-09 11:46:51] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -81.746805+0.006118j
[2025-09-09 11:47:26] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -81.713561+0.007532j
[2025-09-09 11:48:00] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -81.911017+0.001305j
[2025-09-09 11:48:35] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -81.963441-0.003667j
[2025-09-09 11:49:10] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -81.782614+0.002717j
[2025-09-09 11:49:45] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -81.853233+0.004189j
[2025-09-09 11:50:19] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -81.887702+0.000821j
[2025-09-09 11:50:54] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -81.874712+0.009157j
[2025-09-09 11:51:28] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -81.883503+0.000138j
[2025-09-09 11:52:03] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -81.797686+0.002704j
[2025-09-09 11:52:38] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -81.979492+0.002160j
[2025-09-09 11:53:12] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -81.965424-0.003303j
[2025-09-09 11:53:47] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -81.926849+0.003705j
[2025-09-09 11:54:22] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -81.899099+0.001220j
[2025-09-09 11:54:57] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -81.983525-0.000539j
[2025-09-09 11:55:32] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -81.894640-0.000821j
[2025-09-09 11:56:06] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -81.794222+0.005132j
[2025-09-09 11:56:41] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -81.800918+0.007860j
[2025-09-09 11:57:16] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -81.776250+0.003175j
[2025-09-09 11:57:50] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -81.688235+0.000406j
[2025-09-09 11:58:25] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -81.766548-0.001947j
[2025-09-09 11:59:00] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -81.748476+0.001234j
[2025-09-09 11:59:34] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -81.655468+0.002278j
[2025-09-09 12:00:09] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -81.750911+0.002580j
[2025-09-09 12:00:44] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -81.636922-0.004510j
[2025-09-09 12:01:18] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -81.729170+0.000304j
[2025-09-09 12:01:53] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -81.912240+0.005102j
[2025-09-09 12:02:28] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -81.937441-0.005460j
[2025-09-09 12:03:02] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -81.953631+0.001907j
[2025-09-09 12:03:37] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -81.875433-0.002032j
[2025-09-09 12:04:12] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -81.908322-0.000424j
[2025-09-09 12:04:46] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -81.869030+0.000485j
[2025-09-09 12:05:21] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -81.925670-0.000725j
[2025-09-09 12:05:56] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -81.890887-0.005247j
[2025-09-09 12:06:31] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -81.897676-0.005692j
[2025-09-09 12:06:31] RESTART #1 | Period: 300
[2025-09-09 12:07:05] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -81.869799-0.002656j
[2025-09-09 12:07:40] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -81.814107-0.000321j
[2025-09-09 12:08:14] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -81.803999-0.003678j
[2025-09-09 12:08:49] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -81.687517-0.000637j
[2025-09-09 12:09:24] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -81.723584+0.001170j
[2025-09-09 12:09:58] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -81.713744-0.003252j
[2025-09-09 12:10:33] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -81.557188-0.001974j
[2025-09-09 12:11:08] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -81.720800+0.005617j
[2025-09-09 12:11:42] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -81.741781+0.005042j
[2025-09-09 12:12:17] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -81.683226-0.003421j
[2025-09-09 12:12:52] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -81.828146-0.001298j
[2025-09-09 12:13:26] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -81.722074-0.016350j
[2025-09-09 12:14:01] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -81.699028-0.001004j
[2025-09-09 12:14:36] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -81.827840-0.000437j
[2025-09-09 12:15:10] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -81.781905+0.005049j
[2025-09-09 12:15:45] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -81.783435+0.000869j
[2025-09-09 12:16:20] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -81.797380+0.003430j
[2025-09-09 12:16:54] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -81.787668+0.002174j
[2025-09-09 12:17:29] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -81.627245+0.000916j
[2025-09-09 12:18:04] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -81.683055+0.005985j
[2025-09-09 12:18:38] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -81.888799+0.001732j
[2025-09-09 12:19:13] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -81.818988+0.000974j
[2025-09-09 12:19:48] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -81.880638+0.001641j
[2025-09-09 12:20:23] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -81.850250-0.000864j
[2025-09-09 12:20:57] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -81.828524+0.005025j
[2025-09-09 12:21:32] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -81.748887-0.002171j
[2025-09-09 12:22:07] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -81.805379-0.001650j
[2025-09-09 12:22:41] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -81.819566+0.002421j
[2025-09-09 12:23:16] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -81.831333-0.004339j
[2025-09-09 12:23:51] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -81.926655-0.002900j
[2025-09-09 12:24:25] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -81.674475-0.003859j
[2025-09-09 12:25:00] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -81.675661+0.000864j
[2025-09-09 12:25:35] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -81.669754-0.000240j
[2025-09-09 12:26:10] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -81.855842-0.004927j
[2025-09-09 12:26:44] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -81.833816-0.005092j
[2025-09-09 12:27:19] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -81.715415-0.002401j
[2025-09-09 12:27:53] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -81.804364-0.000419j
[2025-09-09 12:28:28] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -81.759966+0.001898j
[2025-09-09 12:29:03] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -81.929706-0.002194j
[2025-09-09 12:29:37] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -81.776324+0.001400j
[2025-09-09 12:30:12] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -81.945125+0.000251j
[2025-09-09 12:30:47] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -81.725340-0.002282j
[2025-09-09 12:31:21] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -81.837834+0.004497j
[2025-09-09 12:31:56] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -81.762286-0.001998j
[2025-09-09 12:32:31] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -81.892986+0.004814j
[2025-09-09 12:33:05] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -81.708977-0.002470j
[2025-09-09 12:33:40] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -81.631044-0.006789j
[2025-09-09 12:34:15] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -81.659225+0.003673j
[2025-09-09 12:34:49] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -81.631537+0.001685j
[2025-09-09 12:35:24] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -81.484494-0.003952j
[2025-09-09 12:35:59] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -81.646274-0.000868j
[2025-09-09 12:36:33] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -81.739335+0.005129j
[2025-09-09 12:37:08] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -81.700042-0.005169j
[2025-09-09 12:37:43] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -81.601019+0.000526j
[2025-09-09 12:38:17] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -81.773701+0.006733j
[2025-09-09 12:38:52] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -81.679041-0.005334j
[2025-09-09 12:39:27] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -81.684259+0.000544j
[2025-09-09 12:40:02] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -81.694959+0.002079j
[2025-09-09 12:40:36] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -81.800134+0.006542j
[2025-09-09 12:41:11] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -81.641640-0.004456j
[2025-09-09 12:41:11] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-09 12:41:46] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -81.449242-0.001771j
[2025-09-09 12:42:20] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -81.594867+0.003646j
[2025-09-09 12:42:55] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -81.503714+0.009046j
[2025-09-09 12:43:30] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -81.781470-0.000620j
[2025-09-09 12:44:04] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -81.712304-0.002411j
[2025-09-09 12:44:39] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -81.771633+0.001355j
[2025-09-09 12:45:14] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -81.815875-0.000469j
[2025-09-09 12:45:48] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -81.746494-0.002721j
[2025-09-09 12:46:23] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -81.760631+0.002911j
[2025-09-09 12:46:58] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -81.840016-0.002074j
[2025-09-09 12:47:32] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -81.730222+0.002272j
[2025-09-09 12:48:07] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -81.630011-0.002308j
[2025-09-09 12:48:41] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -81.695835-0.002669j
[2025-09-09 12:49:16] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -81.634384-0.000716j
[2025-09-09 12:49:51] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -81.785145-0.002314j
[2025-09-09 12:50:26] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -81.730588-0.002272j
[2025-09-09 12:51:00] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -81.625833-0.000605j
[2025-09-09 12:51:35] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -81.707930+0.002996j
[2025-09-09 12:52:10] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -81.743040-0.002371j
[2025-09-09 12:52:44] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -81.721336+0.000574j
[2025-09-09 12:53:19] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -81.697477+0.001404j
[2025-09-09 12:53:53] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -81.865323-0.001306j
[2025-09-09 12:54:28] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -81.693528-0.000409j
[2025-09-09 12:55:03] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -81.826811+0.002019j
[2025-09-09 12:55:38] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -81.837522-0.004450j
[2025-09-09 12:56:12] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -81.869164-0.005468j
[2025-09-09 12:56:47] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -81.817503-0.005458j
[2025-09-09 12:57:22] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -81.685939-0.003347j
[2025-09-09 12:57:56] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -81.644307+0.002749j
[2025-09-09 12:58:31] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -81.600696-0.004491j
[2025-09-09 12:59:06] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -81.712765+0.001602j
[2025-09-09 12:59:40] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -81.635408+0.005227j
[2025-09-09 13:00:15] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -81.663321+0.001240j
[2025-09-09 13:00:50] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -81.681387-0.001902j
[2025-09-09 13:01:24] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -81.664038-0.004058j
[2025-09-09 13:01:59] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -81.556917-0.004779j
[2025-09-09 13:02:34] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -81.740746-0.000781j
[2025-09-09 13:03:08] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -81.700475-0.004947j
[2025-09-09 13:03:43] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -81.746686-0.000009j
[2025-09-09 13:04:18] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -81.916868-0.004160j
[2025-09-09 13:04:52] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -81.776510+0.004091j
[2025-09-09 13:05:27] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -81.715731+0.002557j
[2025-09-09 13:06:02] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -81.880106-0.000989j
[2025-09-09 13:06:36] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -81.717492-0.004699j
[2025-09-09 13:07:11] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -81.632484+0.003408j
[2025-09-09 13:07:46] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -81.718955-0.001367j
[2025-09-09 13:08:20] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -81.663995+0.000943j
[2025-09-09 13:08:55] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -81.600229-0.007880j
[2025-09-09 13:09:29] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -81.760848+0.002465j
[2025-09-09 13:10:04] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -81.576008-0.001624j
[2025-09-09 13:10:39] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -81.741286-0.003470j
[2025-09-09 13:11:13] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -81.692183-0.004397j
[2025-09-09 13:11:48] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -81.755884-0.003976j
[2025-09-09 13:12:23] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -81.735809-0.006321j
[2025-09-09 13:12:57] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -81.774454-0.000018j
[2025-09-09 13:13:32] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -81.640931-0.000814j
[2025-09-09 13:14:07] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -81.597054+0.003103j
[2025-09-09 13:14:41] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -81.802540-0.002431j
[2025-09-09 13:15:16] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -81.820503+0.001541j
[2025-09-09 13:15:51] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -81.785449-0.000714j
[2025-09-09 13:16:25] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -81.837775-0.003933j
[2025-09-09 13:17:00] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -81.747223-0.000409j
[2025-09-09 13:17:35] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -81.756282-0.012224j
[2025-09-09 13:18:09] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -81.769285+0.001580j
[2025-09-09 13:18:44] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -81.871695-0.000834j
[2025-09-09 13:19:19] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -81.693701+0.001615j
[2025-09-09 13:19:53] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -81.717859-0.003119j
[2025-09-09 13:20:28] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -81.595750-0.000282j
[2025-09-09 13:21:03] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -81.643947+0.001812j
[2025-09-09 13:21:38] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -81.619636-0.002108j
[2025-09-09 13:22:12] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -81.874882-0.001062j
[2025-09-09 13:22:47] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -81.899448+0.003059j
[2025-09-09 13:23:22] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -81.886405+0.000062j
[2025-09-09 13:23:57] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -81.810098-0.000181j
[2025-09-09 13:24:31] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -81.800894-0.000706j
[2025-09-09 13:25:06] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -81.749301+0.012157j
[2025-09-09 13:25:40] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -81.761127-0.005132j
[2025-09-09 13:26:15] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -81.842992+0.002837j
[2025-09-09 13:26:50] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -82.084598-0.003861j
[2025-09-09 13:27:25] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -81.849139+0.002535j
[2025-09-09 13:27:59] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -81.786941+0.000586j
[2025-09-09 13:28:34] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -81.817818-0.002506j
[2025-09-09 13:29:09] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -81.940262-0.005305j
[2025-09-09 13:29:44] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -81.744561+0.001877j
[2025-09-09 13:30:18] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -81.919991+0.000844j
[2025-09-09 13:30:53] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -81.797430-0.001244j
[2025-09-09 13:31:28] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -81.721827+0.000221j
[2025-09-09 13:32:02] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -81.694201-0.001971j
[2025-09-09 13:32:37] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -81.664825-0.003019j
[2025-09-09 13:33:12] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -81.755430+0.001247j
[2025-09-09 13:33:46] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -81.729293-0.000534j
[2025-09-09 13:34:21] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -81.689642+0.003523j
[2025-09-09 13:34:56] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -81.737969-0.000982j
[2025-09-09 13:35:30] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -81.764557+0.001886j
[2025-09-09 13:36:05] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -81.792773-0.000908j
[2025-09-09 13:36:40] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -81.642914-0.010754j
[2025-09-09 13:37:15] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -81.643701-0.006878j
[2025-09-09 13:37:49] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -81.673940-0.001648j
[2025-09-09 13:38:24] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -81.787199-0.004986j
[2025-09-09 13:38:59] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -81.893157+0.008411j
[2025-09-09 13:39:33] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -81.756098-0.000493j
[2025-09-09 13:40:08] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -81.922602+0.000430j
[2025-09-09 13:40:43] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -81.840170-0.000190j
[2025-09-09 13:41:17] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -81.868660+0.002582j
[2025-09-09 13:41:52] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -81.724908-0.000471j
[2025-09-09 13:41:52] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-09 13:42:27] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -81.789831+0.005158j
[2025-09-09 13:43:02] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -81.870538-0.002006j
[2025-09-09 13:43:36] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -81.943779-0.006638j
[2025-09-09 13:44:11] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -82.052658+0.000331j
[2025-09-09 13:44:45] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -81.982171-0.004739j
[2025-09-09 13:45:20] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -81.819780+0.000057j
[2025-09-09 13:45:55] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -81.879249-0.001183j
[2025-09-09 13:46:29] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -81.759743-0.010965j
[2025-09-09 13:47:04] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -81.755260+0.003302j
[2025-09-09 13:47:39] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -81.825395-0.003387j
[2025-09-09 13:48:13] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -81.762439-0.002742j
[2025-09-09 13:48:48] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -81.726569+0.002023j
[2025-09-09 13:49:23] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -81.707881+0.002557j
[2025-09-09 13:49:58] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -81.790037+0.003553j
[2025-09-09 13:50:32] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -81.730715-0.002272j
[2025-09-09 13:51:07] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -81.713598-0.002000j
[2025-09-09 13:51:41] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -81.731784+0.000364j
[2025-09-09 13:52:16] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -81.808708-0.002545j
[2025-09-09 13:52:51] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -81.797039-0.003555j
[2025-09-09 13:53:25] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -81.750759+0.004305j
[2025-09-09 13:54:00] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -81.907789-0.004841j
[2025-09-09 13:54:35] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -81.654656+0.000410j
[2025-09-09 13:55:10] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -81.726738-0.002605j
[2025-09-09 13:55:44] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -81.689523+0.003815j
[2025-09-09 13:56:19] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -81.772136-0.000849j
[2025-09-09 13:56:54] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -81.769205-0.008824j
[2025-09-09 13:57:29] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -81.823431-0.003363j
[2025-09-09 13:58:03] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -81.819345+0.000547j
[2025-09-09 13:58:38] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -81.838304-0.003509j
[2025-09-09 13:59:13] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -82.001819+0.002125j
[2025-09-09 13:59:47] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -81.885652+0.001186j
[2025-09-09 14:00:22] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -81.942084-0.002423j
[2025-09-09 14:00:56] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -81.870514-0.000166j
[2025-09-09 14:01:31] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -81.811596-0.000740j
[2025-09-09 14:02:06] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -81.860079-0.001967j
[2025-09-09 14:02:40] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -81.923217+0.002266j
[2025-09-09 14:03:15] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -81.853267+0.000127j
[2025-09-09 14:03:49] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -81.794878+0.002331j
[2025-09-09 14:04:24] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -81.821669-0.000450j
[2025-09-09 14:04:59] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -81.745651+0.000145j
[2025-09-09 14:05:33] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -81.772808-0.000330j
[2025-09-09 14:06:08] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -81.708253+0.002121j
[2025-09-09 14:06:43] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -81.779398+0.002273j
[2025-09-09 14:07:18] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -81.655360-0.007611j
[2025-09-09 14:07:52] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -81.639617+0.000153j
[2025-09-09 14:08:27] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -81.682980+0.003053j
[2025-09-09 14:09:02] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -81.683357+0.000466j
[2025-09-09 14:09:36] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -81.692849-0.006385j
[2025-09-09 14:10:11] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -81.576863+0.001707j
[2025-09-09 14:10:46] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -81.590834+0.001071j
[2025-09-09 14:11:20] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -81.749985+0.001861j
[2025-09-09 14:11:55] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -81.657055+0.001732j
[2025-09-09 14:12:30] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -81.792799+0.001844j
[2025-09-09 14:13:05] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -81.805447-0.001958j
[2025-09-09 14:13:39] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -81.720347+0.003856j
[2025-09-09 14:14:14] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -81.707341+0.001933j
[2025-09-09 14:14:49] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -81.763917-0.000761j
[2025-09-09 14:15:23] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -81.824640+0.003462j
[2025-09-09 14:15:58] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -81.864382-0.004196j
[2025-09-09 14:16:33] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -81.832563-0.004083j
[2025-09-09 14:17:07] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -81.699550-0.004242j
[2025-09-09 14:17:42] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -81.736100-0.000993j
[2025-09-09 14:18:17] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -81.651634-0.001956j
[2025-09-09 14:18:51] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -81.576216+0.002403j
[2025-09-09 14:19:26] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -81.691588+0.003761j
[2025-09-09 14:20:01] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -81.723718+0.003216j
[2025-09-09 14:20:35] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -81.903449+0.003482j
[2025-09-09 14:21:10] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -81.863829-0.001657j
[2025-09-09 14:21:45] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -81.775437+0.002330j
[2025-09-09 14:22:19] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -81.851186-0.007185j
[2025-09-09 14:22:54] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -81.750602-0.004516j
[2025-09-09 14:23:29] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -81.767366+0.000987j
[2025-09-09 14:24:03] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -81.777953+0.005433j
[2025-09-09 14:24:38] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -81.602100+0.004594j
[2025-09-09 14:25:13] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -81.694739+0.004215j
[2025-09-09 14:25:47] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -81.682854+0.003086j
[2025-09-09 14:26:22] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -81.789485-0.001250j
[2025-09-09 14:26:57] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -81.823149-0.001906j
[2025-09-09 14:27:31] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -81.859388+0.003220j
[2025-09-09 14:28:06] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -81.732219-0.000613j
[2025-09-09 14:28:41] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -81.651999+0.003358j
[2025-09-09 14:29:15] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -81.658969+0.000491j
[2025-09-09 14:29:50] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -81.661150+0.004852j
[2025-09-09 14:30:25] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -81.681689-0.000413j
[2025-09-09 14:30:57] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -81.713669+0.001606j
[2025-09-09 14:31:34] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -81.723880+0.001378j
[2025-09-09 14:32:09] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -81.819349-0.000342j
[2025-09-09 14:32:43] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -81.798050+0.002477j
[2025-09-09 14:33:18] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -81.916951+0.002409j
[2025-09-09 14:33:52] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -81.768027-0.007749j
[2025-09-09 14:34:27] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -81.624518+0.001805j
[2025-09-09 14:35:02] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -81.612764-0.000738j
[2025-09-09 14:35:36] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -81.643439-0.003490j
[2025-09-09 14:36:11] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -81.785984-0.005784j
[2025-09-09 14:36:45] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -81.747144+0.004141j
[2025-09-09 14:37:20] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -81.733663-0.000321j
[2025-09-09 14:37:55] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -81.777734-0.004102j
[2025-09-09 14:38:30] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -81.944586-0.002048j
[2025-09-09 14:39:04] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -81.882686-0.003730j
[2025-09-09 14:39:39] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -81.775491-0.000444j
[2025-09-09 14:40:14] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -81.757466-0.002409j
[2025-09-09 14:40:49] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -81.776127-0.002181j
[2025-09-09 14:41:23] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -81.930352-0.002140j
[2025-09-09 14:41:58] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -81.837219-0.002746j
[2025-09-09 14:42:33] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -81.705526-0.005703j
[2025-09-09 14:42:33] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-09 14:43:07] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -81.830097-0.004782j
[2025-09-09 14:43:42] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -81.813661-0.002890j
[2025-09-09 14:44:17] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -81.967616-0.003220j
[2025-09-09 14:44:51] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -81.974148-0.000178j
[2025-09-09 14:45:26] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -81.946678+0.000605j
[2025-09-09 14:46:01] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -81.883874-0.005150j
[2025-09-09 14:46:36] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -81.965754+0.002694j
[2025-09-09 14:47:10] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -81.866124+0.002619j
[2025-09-09 14:47:45] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -81.861737+0.003654j
[2025-09-09 14:48:20] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -81.822101+0.002560j
[2025-09-09 14:48:54] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -81.911309-0.000001j
[2025-09-09 14:49:29] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -81.903382+0.001202j
[2025-09-09 14:50:04] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -81.909861+0.001816j
[2025-09-09 14:50:38] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -81.837357-0.001852j
[2025-09-09 14:51:13] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -81.878985-0.001056j
[2025-09-09 14:51:48] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -81.790191-0.003786j
[2025-09-09 14:52:23] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -81.791956+0.003958j
[2025-09-09 14:52:57] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -81.757301+0.000063j
[2025-09-09 14:53:32] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -81.681069-0.007567j
[2025-09-09 14:54:07] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -81.640377+0.006430j
[2025-09-09 14:54:41] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -81.656333-0.002087j
[2025-09-09 14:55:16] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -81.755108-0.001688j
[2025-09-09 14:55:51] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -81.807338-0.002977j
[2025-09-09 14:56:25] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -81.774531-0.000341j
[2025-09-09 14:57:00] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -81.849662-0.003476j
[2025-09-09 14:57:35] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -81.807745-0.001394j
[2025-09-09 14:58:09] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -81.754407-0.004723j
[2025-09-09 14:58:44] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -81.723218-0.000687j
[2025-09-09 14:59:19] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -81.728323+0.004565j
[2025-09-09 14:59:53] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -81.763723+0.004010j
[2025-09-09 14:59:53] RESTART #2 | Period: 600
[2025-09-09 15:00:28] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -81.675600-0.001857j
[2025-09-09 15:01:03] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -81.705797+0.003227j
[2025-09-09 15:01:38] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -81.820810-0.001797j
[2025-09-09 15:02:12] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -81.657439-0.000054j
[2025-09-09 15:02:47] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -81.707272-0.002192j
[2025-09-09 15:03:22] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -81.656369-0.001629j
[2025-09-09 15:03:56] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -81.775052+0.001311j
[2025-09-09 15:04:31] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -81.704660+0.002034j
[2025-09-09 15:05:06] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -81.767936-0.000765j
[2025-09-09 15:05:40] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -81.817016+0.001288j
[2025-09-09 15:06:15] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -81.741126+0.002863j
[2025-09-09 15:06:50] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -81.884586-0.002230j
[2025-09-09 15:07:24] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -81.784175+0.004460j
[2025-09-09 15:07:59] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -81.815573-0.000868j
[2025-09-09 15:08:34] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -81.724919-0.001505j
[2025-09-09 15:09:09] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -81.669526-0.004671j
[2025-09-09 15:09:43] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -81.664197-0.006226j
[2025-09-09 15:10:18] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -81.758071-0.004583j
[2025-09-09 15:10:53] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -81.769299-0.002255j
[2025-09-09 15:11:27] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -81.644217+0.000692j
[2025-09-09 15:12:02] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -81.897362-0.001455j
[2025-09-09 15:12:37] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -81.743543-0.002796j
[2025-09-09 15:13:11] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -81.825680+0.005694j
[2025-09-09 15:13:46] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -81.937229-0.000122j
[2025-09-09 15:14:21] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -81.946437+0.003298j
[2025-09-09 15:14:55] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -81.952255+0.003189j
[2025-09-09 15:15:30] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -81.955710+0.003192j
[2025-09-09 15:16:05] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -81.867115-0.001423j
[2025-09-09 15:16:40] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -81.742763-0.003171j
[2025-09-09 15:17:14] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -81.809337+0.002378j
[2025-09-09 15:17:49] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -81.675416+0.003844j
[2025-09-09 15:18:24] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -81.752367+0.000043j
[2025-09-09 15:18:58] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -81.927587-0.002810j
[2025-09-09 15:19:33] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -81.777102+0.001459j
[2025-09-09 15:20:08] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -81.899674+0.000137j
[2025-09-09 15:20:42] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -81.764973+0.002861j
[2025-09-09 15:21:17] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -81.851407+0.006578j
[2025-09-09 15:21:52] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -81.891650+0.001526j
[2025-09-09 15:22:26] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -81.934070+0.000098j
[2025-09-09 15:23:01] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -81.767005-0.000511j
[2025-09-09 15:23:36] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -81.767548+0.000560j
[2025-09-09 15:24:10] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -81.944502+0.002230j
[2025-09-09 15:24:45] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -82.072781+0.001619j
[2025-09-09 15:25:20] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -81.862908-0.005047j
[2025-09-09 15:25:54] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -81.926639-0.006332j
[2025-09-09 15:26:29] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -81.844872-0.000707j
[2025-09-09 15:27:04] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -81.980365-0.003130j
[2025-09-09 15:27:38] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -81.764500+0.003310j
[2025-09-09 15:28:13] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -81.773620-0.002977j
[2025-09-09 15:28:48] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -81.878086-0.000468j
[2025-09-09 15:29:23] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -81.742908-0.005921j
[2025-09-09 15:29:57] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -81.831567-0.001076j
[2025-09-09 15:30:32] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -81.818517-0.000263j
[2025-09-09 15:31:07] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -82.041767-0.000694j
[2025-09-09 15:31:41] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -81.900379-0.003600j
[2025-09-09 15:32:16] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -81.873109+0.002948j
[2025-09-09 15:32:51] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -81.864984-0.004981j
[2025-09-09 15:33:25] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -82.000758+0.007154j
[2025-09-09 15:34:00] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -81.946827-0.002313j
[2025-09-09 15:34:35] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -81.855717+0.002901j
[2025-09-09 15:35:09] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -81.835384+0.000049j
[2025-09-09 15:35:44] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -81.752998-0.000507j
[2025-09-09 15:36:19] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -81.678200+0.000656j
[2025-09-09 15:36:53] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -81.674484+0.007340j
[2025-09-09 15:37:28] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -81.608902-0.007339j
[2025-09-09 15:38:03] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -81.595440-0.003238j
[2025-09-09 15:38:37] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -81.662261+0.003089j
[2025-09-09 15:39:12] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -81.627769-0.006523j
[2025-09-09 15:39:47] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -81.692973+0.004615j
[2025-09-09 15:40:21] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -81.554398+0.000792j
[2025-09-09 15:40:56] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -81.505164+0.000918j
[2025-09-09 15:41:29] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -81.622538+0.002117j
[2025-09-09 15:42:04] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -81.579929+0.001622j
[2025-09-09 15:42:39] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -81.630299-0.002420j
[2025-09-09 15:43:13] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -81.626177-0.003217j
[2025-09-09 15:43:13] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-09 15:43:48] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -81.608334+0.000357j
[2025-09-09 15:44:23] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -81.684368-0.000043j
[2025-09-09 15:44:58] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -81.770369-0.000820j
[2025-09-09 15:45:32] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -81.677084+0.000717j
[2025-09-09 15:46:07] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -81.688789+0.001569j
[2025-09-09 15:46:42] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -81.956764+0.000962j
[2025-09-09 15:47:16] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -81.741646+0.001372j
[2025-09-09 15:47:51] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -81.735737-0.002025j
[2025-09-09 15:48:26] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -81.699984-0.005334j
[2025-09-09 15:49:00] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -81.890047+0.000470j
[2025-09-09 15:49:35] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -81.763276+0.003471j
[2025-09-09 15:50:10] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -81.822154+0.000255j
[2025-09-09 15:50:44] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -81.835887+0.000499j
[2025-09-09 15:51:19] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -81.792685-0.004733j
[2025-09-09 15:51:54] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -81.804375-0.003492j
[2025-09-09 15:52:28] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -81.770817-0.005160j
[2025-09-09 15:53:03] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -81.875258+0.005518j
[2025-09-09 15:53:38] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -81.911188+0.001451j
[2025-09-09 15:54:12] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -81.872012-0.004658j
[2025-09-09 15:54:47] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -81.908936-0.004223j
[2025-09-09 15:55:22] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -81.930986-0.003131j
[2025-09-09 15:55:56] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -81.894457+0.004241j
[2025-09-09 15:56:31] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -81.886980-0.001340j
[2025-09-09 15:57:05] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -81.873746-0.001462j
[2025-09-09 15:57:40] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -81.848652+0.001075j
[2025-09-09 15:58:15] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -81.826951-0.012093j
[2025-09-09 15:58:50] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -81.712890-0.001463j
[2025-09-09 15:59:24] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -81.705973+0.005556j
[2025-09-09 15:59:59] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -81.639926-0.000738j
[2025-09-09 16:00:34] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -81.538941+0.004367j
[2025-09-09 16:01:08] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -81.762897-0.004584j
[2025-09-09 16:01:43] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -81.767758-0.007192j
[2025-09-09 16:02:18] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -81.847886+0.002055j
[2025-09-09 16:02:53] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -81.779399-0.003602j
[2025-09-09 16:03:27] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -81.573279+0.001483j
[2025-09-09 16:04:02] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -81.637127-0.003913j
[2025-09-09 16:04:37] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -81.600230-0.000434j
[2025-09-09 16:05:11] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -81.658796-0.004390j
[2025-09-09 16:05:46] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -81.788805-0.001831j
[2025-09-09 16:06:21] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -81.676869+0.001420j
[2025-09-09 16:06:56] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -81.622918-0.002127j
[2025-09-09 16:07:30] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -81.823894+0.005720j
[2025-09-09 16:08:05] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -81.760985-0.003005j
[2025-09-09 16:08:40] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -81.681629+0.005089j
[2025-09-09 16:09:14] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -81.781443-0.004156j
[2025-09-09 16:09:49] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -81.769719+0.001695j
[2025-09-09 16:10:24] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -81.842150-0.004116j
[2025-09-09 16:10:58] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -81.752448-0.002380j
[2025-09-09 16:11:33] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -81.865926-0.002853j
[2025-09-09 16:12:08] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -81.788690+0.000132j
[2025-09-09 16:12:42] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -81.774350+0.004733j
[2025-09-09 16:13:17] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -81.843934+0.001589j
[2025-09-09 16:13:52] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -81.874897-0.003710j
[2025-09-09 16:14:26] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -81.985721-0.001874j
[2025-09-09 16:15:01] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -81.870628-0.003247j
[2025-09-09 16:15:36] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -82.050542+0.005594j
[2025-09-09 16:16:10] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -81.832703+0.000510j
[2025-09-09 16:16:45] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -81.864330+0.001101j
[2025-09-09 16:17:20] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -81.804375+0.001076j
[2025-09-09 16:17:54] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -81.842075-0.001069j
[2025-09-09 16:18:29] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -81.908322+0.004763j
[2025-09-09 16:19:04] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -81.934344-0.001093j
[2025-09-09 16:19:38] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -81.889052+0.002858j
[2025-09-09 16:20:13] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -81.971934-0.003280j
[2025-09-09 16:20:48] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -81.931412-0.001290j
[2025-09-09 16:21:23] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -81.898857-0.000447j
[2025-09-09 16:21:57] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -81.955027+0.004051j
[2025-09-09 16:22:32] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -81.986736+0.001148j
[2025-09-09 16:23:07] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -81.980764+0.000587j
[2025-09-09 16:23:42] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -81.907296+0.000017j
[2025-09-09 16:24:16] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -81.939677-0.001149j
[2025-09-09 16:24:51] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -81.999499+0.003129j
[2025-09-09 16:25:26] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -81.849359+0.002007j
[2025-09-09 16:26:00] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -81.758191+0.002629j
[2025-09-09 16:26:35] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -81.881861-0.003386j
[2025-09-09 16:27:10] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -81.657935+0.002006j
[2025-09-09 16:27:44] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -81.796223-0.007548j
[2025-09-09 16:28:19] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -81.804373+0.001110j
[2025-09-09 16:28:54] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -81.816319-0.001357j
[2025-09-09 16:29:28] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -81.802231-0.003541j
[2025-09-09 16:30:03] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -81.860276-0.000845j
[2025-09-09 16:30:38] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -81.791184+0.003479j
[2025-09-09 16:31:12] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -81.828079+0.000566j
[2025-09-09 16:31:47] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -81.740360-0.001484j
[2025-09-09 16:32:22] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -81.778947+0.002168j
[2025-09-09 16:32:56] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -81.942522-0.003314j
[2025-09-09 16:33:31] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -81.873161+0.002835j
[2025-09-09 16:34:06] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -81.766406-0.017804j
[2025-09-09 16:34:40] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -81.742905-0.004445j
[2025-09-09 16:35:15] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -81.640306+0.003855j
[2025-09-09 16:35:50] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -81.726429-0.001068j
[2025-09-09 16:36:25] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -81.716571+0.003677j
[2025-09-09 16:36:59] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -81.640897+0.000283j
[2025-09-09 16:37:34] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -81.540853-0.001073j
[2025-09-09 16:38:09] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -81.515135+0.000109j
[2025-09-09 16:38:43] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -81.659157+0.000594j
[2025-09-09 16:39:18] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -81.455872+0.001806j
[2025-09-09 16:39:53] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -81.450053+0.002469j
[2025-09-09 16:40:28] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -81.465207-0.001122j
[2025-09-09 16:41:02] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -81.481358-0.002715j
[2025-09-09 16:41:37] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -81.714530-0.006686j
[2025-09-09 16:42:11] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -81.659974-0.005723j
[2025-09-09 16:42:46] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -81.824228-0.003546j
[2025-09-09 16:43:21] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -81.818409+0.003864j
[2025-09-09 16:43:55] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -81.833511-0.001040j
[2025-09-09 16:43:55] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-09 16:44:30] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -81.884978+0.004782j
[2025-09-09 16:45:05] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -81.888232+0.004494j
[2025-09-09 16:45:39] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -81.902551+0.004674j
[2025-09-09 16:46:14] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -81.832179-0.012620j
[2025-09-09 16:46:49] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -81.700141-0.005290j
[2025-09-09 16:47:24] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -81.643491-0.004822j
[2025-09-09 16:47:58] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -81.772560+0.001946j
[2025-09-09 16:48:33] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -81.671521+0.006109j
[2025-09-09 16:49:08] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -81.635083-0.000813j
[2025-09-09 16:49:43] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -81.567465+0.000033j
[2025-09-09 16:50:17] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -81.742152+0.001621j
[2025-09-09 16:50:52] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -81.597516-0.000364j
[2025-09-09 16:51:27] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -81.753255+0.001699j
[2025-09-09 16:52:01] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -81.772018+0.001403j
[2025-09-09 16:52:36] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -81.719750+0.002006j
[2025-09-09 16:53:11] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -81.549464+0.003068j
[2025-09-09 16:53:45] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -81.606135-0.003316j
[2025-09-09 16:54:20] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -81.631480-0.000581j
[2025-09-09 16:54:55] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -81.598085-0.001712j
[2025-09-09 16:55:29] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -81.675264-0.005189j
[2025-09-09 16:56:04] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -81.592306+0.004763j
[2025-09-09 16:56:39] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -81.773508+0.002680j
[2025-09-09 16:57:14] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -81.651384-0.001498j
[2025-09-09 16:57:48] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -81.752673+0.000188j
[2025-09-09 16:58:23] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -81.759949-0.004358j
[2025-09-09 16:58:58] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -81.729591+0.002432j
[2025-09-09 16:59:32] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -81.892621+0.002207j
[2025-09-09 17:00:07] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -81.817018-0.005580j
[2025-09-09 17:00:42] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -81.744624+0.004044j
[2025-09-09 17:01:16] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -81.780485-0.001296j
[2025-09-09 17:01:51] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -81.966533-0.004356j
[2025-09-09 17:02:25] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -81.795370-0.000443j
[2025-09-09 17:03:00] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -81.827135-0.003632j
[2025-09-09 17:03:35] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -81.977554+0.007524j
[2025-09-09 17:04:10] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -81.787808-0.000225j
[2025-09-09 17:04:44] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -81.626928-0.001202j
[2025-09-09 17:05:19] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -81.646979-0.000541j
[2025-09-09 17:05:54] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -81.674623-0.002559j
[2025-09-09 17:06:28] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -81.725965-0.001298j
[2025-09-09 17:07:03] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -81.634329-0.000473j
[2025-09-09 17:07:38] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -81.718322+0.008504j
[2025-09-09 17:08:13] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -81.850016+0.001675j
[2025-09-09 17:08:47] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -81.962710+0.001759j
[2025-09-09 17:09:22] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -81.824718-0.003224j
[2025-09-09 17:09:57] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -81.829133+0.000089j
[2025-09-09 17:10:31] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -81.931351+0.000276j
[2025-09-09 17:11:06] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -81.638545+0.003276j
[2025-09-09 17:11:41] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -81.842801-0.006221j
[2025-09-09 17:12:15] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -81.847854-0.000063j
[2025-09-09 17:12:50] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -81.714107-0.001982j
[2025-09-09 17:13:25] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -81.915819+0.002646j
[2025-09-09 17:13:59] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -82.003688-0.001165j
[2025-09-09 17:14:34] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -81.938645+0.000372j
[2025-09-09 17:15:09] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -82.006790-0.005566j
[2025-09-09 17:15:43] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -82.021006+0.003307j
[2025-09-09 17:16:18] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -82.116199-0.002698j
[2025-09-09 17:16:53] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -82.140683-0.001000j
[2025-09-09 17:17:28] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -81.858103-0.002094j
[2025-09-09 17:18:02] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -81.746866-0.000516j
[2025-09-09 17:18:37] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -81.716548-0.003376j
[2025-09-09 17:19:12] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -81.821316+0.001237j
[2025-09-09 17:19:46] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -81.862570-0.007607j
[2025-09-09 17:20:21] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -81.765166-0.003516j
[2025-09-09 17:20:56] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -81.976355-0.007731j
[2025-09-09 17:21:30] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -81.908450+0.006232j
[2025-09-09 17:22:05] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -81.883357-0.001121j
[2025-09-09 17:22:40] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -81.844744-0.003779j
[2025-09-09 17:23:15] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -81.849802+0.000877j
[2025-09-09 17:23:49] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -81.820923+0.002207j
[2025-09-09 17:24:24] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -81.897385+0.001682j
[2025-09-09 17:24:59] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -81.897213-0.000546j
[2025-09-09 17:25:33] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -81.951642-0.000072j
[2025-09-09 17:26:08] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -81.840992+0.009939j
[2025-09-09 17:26:43] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -81.772926+0.001235j
[2025-09-09 17:27:17] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -81.819287+0.008840j
[2025-09-09 17:27:52] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -81.889366+0.001316j
[2025-09-09 17:28:27] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -81.918488+0.000370j
[2025-09-09 17:29:01] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -81.812478+0.005421j
[2025-09-09 17:29:36] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -81.713129-0.004498j
[2025-09-09 17:30:11] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -81.762835-0.009056j
[2025-09-09 17:30:45] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -81.740254-0.001346j
[2025-09-09 17:31:20] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -81.577776+0.006964j
[2025-09-09 17:31:55] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -81.708664+0.002120j
[2025-09-09 17:32:29] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -81.809445+0.004334j
[2025-09-09 17:33:04] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -81.618487-0.000857j
[2025-09-09 17:33:39] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -81.805687+0.002626j
[2025-09-09 17:34:13] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -81.773240-0.007492j
[2025-09-09 17:34:45] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -81.861004+0.002362j
[2025-09-09 17:35:20] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -81.776155-0.002968j
[2025-09-09 17:35:56] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -81.728080+0.002909j
[2025-09-09 17:36:31] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -81.779540+0.004737j
[2025-09-09 17:37:05] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -81.779411-0.000208j
[2025-09-09 17:37:40] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -81.832310-0.000857j
[2025-09-09 17:38:15] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -81.729145-0.006803j
[2025-09-09 17:38:50] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -81.696918+0.000508j
[2025-09-09 17:39:24] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -81.699647+0.006876j
[2025-09-09 17:39:59] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -81.842741+0.000081j
[2025-09-09 17:40:34] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -81.769804-0.002835j
[2025-09-09 17:41:08] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -81.839443+0.001951j
[2025-09-09 17:41:43] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -81.940505+0.003029j
[2025-09-09 17:42:18] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -81.895458+0.001340j
[2025-09-09 17:42:52] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -81.817575+0.003658j
[2025-09-09 17:43:27] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -81.815556+0.003587j
[2025-09-09 17:44:02] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -81.767697-0.002892j
[2025-09-09 17:44:36] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -81.899447-0.002317j
[2025-09-09 17:44:36] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-09 17:45:11] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -81.666540-0.004485j
[2025-09-09 17:45:45] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -81.722648-0.001464j
[2025-09-09 17:46:20] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -81.695637-0.000595j
[2025-09-09 17:46:55] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -81.721559+0.000682j
[2025-09-09 17:47:30] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -81.642825-0.000508j
[2025-09-09 17:48:04] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -81.542013-0.005416j
[2025-09-09 17:48:39] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -81.596100-0.001496j
[2025-09-09 17:49:13] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -81.518999-0.005537j
[2025-09-09 17:49:48] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -81.603476+0.000540j
[2025-09-09 17:50:23] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -81.630219+0.005627j
[2025-09-09 17:50:57] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -81.582751+0.001364j
[2025-09-09 17:51:32] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -81.656031-0.003779j
[2025-09-09 17:52:07] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -81.583295+0.000633j
[2025-09-09 17:52:41] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -81.729862+0.001615j
[2025-09-09 17:53:16] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -81.651151+0.002324j
[2025-09-09 17:53:51] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -81.572608-0.000212j
[2025-09-09 17:54:26] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -81.574642-0.000104j
[2025-09-09 17:55:00] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -81.614524+0.002489j
[2025-09-09 17:55:35] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -81.653831+0.004346j
[2025-09-09 17:56:10] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -81.707947-0.003168j
[2025-09-09 17:56:44] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -81.614883-0.001320j
[2025-09-09 17:57:19] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -81.608904+0.000811j
[2025-09-09 17:57:53] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -81.614822+0.005650j
[2025-09-09 17:58:28] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -81.589040-0.002248j
[2025-09-09 17:59:03] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -81.573097-0.002049j
[2025-09-09 17:59:38] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -81.547902-0.006342j
[2025-09-09 18:00:12] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -81.531957+0.003216j
[2025-09-09 18:00:47] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -81.542591-0.002632j
[2025-09-09 18:01:22] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -81.581159-0.003954j
[2025-09-09 18:01:56] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -81.647540-0.003336j
[2025-09-09 18:02:31] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -81.702819-0.001292j
[2025-09-09 18:03:06] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -81.694219+0.004596j
[2025-09-09 18:03:40] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -81.695698-0.008185j
[2025-09-09 18:04:15] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -81.691355+0.005329j
[2025-09-09 18:04:50] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -81.610151-0.005479j
[2025-09-09 18:05:24] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -81.583319-0.000728j
[2025-09-09 18:05:59] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -81.878562-0.004474j
[2025-09-09 18:06:34] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -81.748496+0.001787j
[2025-09-09 18:07:08] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -81.802087+0.004012j
[2025-09-09 18:07:43] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -81.921230+0.006009j
[2025-09-09 18:08:18] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -81.859240-0.004074j
[2025-09-09 18:08:52] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -81.860914-0.002609j
[2025-09-09 18:09:27] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -81.857623-0.000131j
[2025-09-09 18:10:02] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -81.889167-0.001939j
[2025-09-09 18:10:36] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -81.944886+0.008808j
[2025-09-09 18:11:11] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -81.762587-0.001682j
[2025-09-09 18:11:46] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -81.705251-0.002829j
[2025-09-09 18:12:20] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -81.655204-0.003509j
[2025-09-09 18:12:55] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -81.857823+0.002409j
[2025-09-09 18:13:30] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -81.768325-0.001445j
[2025-09-09 18:14:04] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -81.689789+0.001111j
[2025-09-09 18:14:39] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -81.732762-0.004855j
[2025-09-09 18:15:14] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -81.747385-0.003708j
[2025-09-09 18:15:48] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -81.705022-0.003294j
[2025-09-09 18:16:23] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -81.666786-0.002874j
[2025-09-09 18:16:58] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -81.611083+0.001130j
[2025-09-09 18:17:33] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -81.744360+0.004362j
[2025-09-09 18:18:07] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -81.584912-0.001441j
[2025-09-09 18:18:42] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -81.742446-0.003378j
[2025-09-09 18:19:17] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -81.830476-0.000159j
[2025-09-09 18:19:51] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -81.820438+0.007101j
[2025-09-09 18:20:26] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -81.792656+0.006418j
[2025-09-09 18:21:01] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -81.851259-0.000643j
[2025-09-09 18:21:35] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -81.787568+0.001594j
[2025-09-09 18:22:10] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -81.856327+0.004498j
[2025-09-09 18:22:45] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -81.898627+0.001250j
[2025-09-09 18:23:19] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -81.866850+0.000982j
[2025-09-09 18:23:54] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -81.917159+0.000612j
[2025-09-09 18:24:29] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -81.844327-0.001778j
[2025-09-09 18:25:04] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -81.824301+0.004287j
[2025-09-09 18:25:38] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -81.757207+0.000379j
[2025-09-09 18:26:13] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -81.850861+0.003712j
[2025-09-09 18:26:48] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -81.877838+0.000012j
[2025-09-09 18:27:22] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -81.777801+0.003654j
[2025-09-09 18:27:57] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -81.672169-0.000764j
[2025-09-09 18:28:32] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -81.587432-0.005853j
[2025-09-09 18:29:06] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -81.562329-0.003230j
[2025-09-09 18:29:41] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -81.620444-0.002386j
[2025-09-09 18:30:16] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -81.684062-0.002032j
[2025-09-09 18:30:50] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -81.741665-0.003117j
[2025-09-09 18:31:25] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -81.761219+0.001796j
[2025-09-09 18:32:00] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -81.692534-0.000442j
[2025-09-09 18:32:35] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -81.569278+0.000168j
[2025-09-09 18:33:09] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -81.654153+0.001450j
[2025-09-09 18:33:44] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -81.754786+0.000748j
[2025-09-09 18:34:18] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -81.771513-0.000952j
[2025-09-09 18:34:53] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -81.847356-0.002741j
[2025-09-09 18:35:28] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -81.844753-0.002569j
[2025-09-09 18:36:03] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -81.584775+0.000224j
[2025-09-09 18:36:37] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -81.681253+0.000887j
[2025-09-09 18:37:12] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -81.689252+0.005358j
[2025-09-09 18:37:47] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -81.731991-0.003021j
[2025-09-09 18:38:21] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -81.822996-0.006603j
[2025-09-09 18:38:56] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -81.649625-0.007707j
[2025-09-09 18:39:31] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -81.757953-0.001778j
[2025-09-09 18:40:05] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -81.746510+0.002310j
[2025-09-09 18:40:40] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -81.861771-0.005262j
[2025-09-09 18:41:15] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -81.928466-0.001857j
[2025-09-09 18:41:50] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -81.900795+0.002852j
[2025-09-09 18:42:24] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -81.982738+0.001426j
[2025-09-09 18:42:59] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -81.951328+0.004924j
[2025-09-09 18:43:34] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -82.000569+0.001223j
[2025-09-09 18:44:08] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -81.937666-0.001410j
[2025-09-09 18:44:43] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -81.765429+0.000698j
[2025-09-09 18:45:18] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -81.803993+0.002366j
[2025-09-09 18:45:18] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-09 18:45:53] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -81.855277+0.003767j
[2025-09-09 18:46:27] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -81.903407-0.001116j
[2025-09-09 18:47:02] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -81.931251+0.002921j
[2025-09-09 18:47:37] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -82.011740-0.000228j
[2025-09-09 18:48:12] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -81.915495+0.002161j
[2025-09-09 18:48:46] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -81.651447+0.000148j
[2025-09-09 18:49:21] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -81.718338+0.001431j
[2025-09-09 18:49:55] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -81.755184+0.003910j
[2025-09-09 18:50:30] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -81.684667-0.000160j
[2025-09-09 18:51:05] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -81.745226+0.001891j
[2025-09-09 18:51:39] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -81.861853-0.004260j
[2025-09-09 18:52:14] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -81.646922-0.001926j
[2025-09-09 18:52:49] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -81.580916+0.001901j
[2025-09-09 18:53:23] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -81.865197-0.001064j
[2025-09-09 18:53:58] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -81.764329+0.002870j
[2025-09-09 18:54:33] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -81.868193-0.005386j
[2025-09-09 18:55:08] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -81.730778+0.001066j
[2025-09-09 18:55:42] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -81.674924+0.004099j
[2025-09-09 18:56:17] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -81.641801-0.000894j
[2025-09-09 18:56:52] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -81.626013+0.002583j
[2025-09-09 18:57:26] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -81.642450+0.003695j
[2025-09-09 18:58:01] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -81.526183-0.000553j
[2025-09-09 18:58:35] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -81.738451-0.003375j
[2025-09-09 18:59:10] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -81.803721+0.004377j
[2025-09-09 18:59:45] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -81.879652-0.002130j
[2025-09-09 19:00:20] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -81.629277-0.001795j
[2025-09-09 19:00:54] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -81.781107-0.001152j
[2025-09-09 19:01:29] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -81.711976-0.005339j
[2025-09-09 19:02:04] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -81.707758-0.002296j
[2025-09-09 19:02:38] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -81.671727-0.001877j
[2025-09-09 19:03:13] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -81.665660+0.004388j
[2025-09-09 19:03:47] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -81.631145-0.003506j
[2025-09-09 19:04:22] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -81.659619-0.002104j
[2025-09-09 19:04:57] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -81.691256-0.001949j
[2025-09-09 19:05:31] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -81.795793-0.002245j
[2025-09-09 19:06:06] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -81.839525-0.000219j
[2025-09-09 19:06:41] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -81.755022+0.001374j
[2025-09-09 19:07:15] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -81.792031+0.001770j
[2025-09-09 19:07:50] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -81.760293-0.003811j
[2025-09-09 19:08:25] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -81.815204+0.000180j
[2025-09-09 19:08:59] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -81.839949-0.007516j
[2025-09-09 19:09:34] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -81.817187+0.002895j
[2025-09-09 19:10:09] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -81.925586-0.002910j
[2025-09-09 19:10:43] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -81.896689+0.000931j
[2025-09-09 19:11:18] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -81.859695-0.002666j
[2025-09-09 19:11:53] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -81.926789-0.002275j
[2025-09-09 19:12:28] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -81.894350+0.000749j
[2025-09-09 19:13:02] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -81.793305-0.000289j
[2025-09-09 19:13:37] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -81.778513-0.000717j
[2025-09-09 19:14:12] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -81.595710+0.003986j
[2025-09-09 19:14:46] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -81.612666-0.002845j
[2025-09-09 19:15:21] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -81.598658+0.000887j
[2025-09-09 19:15:56] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -81.592066-0.001809j
[2025-09-09 19:16:30] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -81.672009-0.000958j
[2025-09-09 19:17:05] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -81.703518-0.001293j
[2025-09-09 19:17:40] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -81.601535-0.009629j
[2025-09-09 19:18:15] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -81.726733+0.000832j
[2025-09-09 19:18:49] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -81.794842-0.003807j
[2025-09-09 19:19:24] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -81.822866+0.005198j
[2025-09-09 19:19:59] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -81.684801-0.005722j
[2025-09-09 19:20:34] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -81.784619+0.000028j
[2025-09-09 19:21:08] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -81.602083+0.003353j
[2025-09-09 19:21:43] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -81.566919+0.000378j
[2025-09-09 19:22:18] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -81.738279-0.001398j
[2025-09-09 19:22:52] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -81.724169-0.002129j
[2025-09-09 19:23:27] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -81.668949-0.001686j
[2025-09-09 19:24:02] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -81.638034+0.003131j
[2025-09-09 19:24:36] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -81.624806+0.002492j
[2025-09-09 19:25:11] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -81.635494-0.004647j
[2025-09-09 19:25:46] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -81.831215+0.002668j
[2025-09-09 19:26:20] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -81.760445+0.013434j
[2025-09-09 19:26:55] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -81.688829+0.002951j
[2025-09-09 19:27:30] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -81.710706-0.003813j
[2025-09-09 19:28:04] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -81.784637-0.000682j
[2025-09-09 19:28:39] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -81.786178+0.003198j
[2025-09-09 19:29:14] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -81.700581-0.002608j
[2025-09-09 19:29:49] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -81.622956+0.000990j
[2025-09-09 19:30:23] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -81.696703+0.003433j
[2025-09-09 19:30:58] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -81.873870-0.007978j
[2025-09-09 19:31:33] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -81.926319+0.002734j
[2025-09-09 19:32:07] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -82.026384-0.002526j
[2025-09-09 19:32:42] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -81.880997-0.002267j
[2025-09-09 19:33:17] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -81.844064+0.002832j
[2025-09-09 19:33:51] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -81.627706+0.004301j
[2025-09-09 19:34:26] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -81.802313+0.002563j
[2025-09-09 19:35:01] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -81.787034+0.003413j
[2025-09-09 19:35:36] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -81.854948+0.000632j
[2025-09-09 19:36:10] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -81.643598-0.010341j
[2025-09-09 19:36:45] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -81.778340+0.003465j
[2025-09-09 19:37:20] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -81.815980+0.013308j
[2025-09-09 19:37:54] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -81.879288-0.002687j
[2025-09-09 19:38:29] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -81.762785+0.000875j
[2025-09-09 19:39:04] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -81.740942-0.001975j
[2025-09-09 19:39:38] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -81.708150-0.002457j
[2025-09-09 19:40:13] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -81.737612-0.000216j
[2025-09-09 19:40:48] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -81.838370-0.002722j
[2025-09-09 19:41:23] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -81.814171-0.000641j
[2025-09-09 19:41:57] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -81.756243+0.000897j
[2025-09-09 19:42:32] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -81.805404+0.001421j
[2025-09-09 19:43:07] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -81.926613+0.000629j
[2025-09-09 19:43:41] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -81.989380-0.003744j
[2025-09-09 19:44:16] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -81.967471-0.005105j
[2025-09-09 19:44:50] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -82.159864+0.002161j
[2025-09-09 19:45:25] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -81.988759+0.003490j
[2025-09-09 19:46:00] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -82.023248+0.003107j
[2025-09-09 19:46:00] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-09 19:46:35] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -81.999130-0.002739j
[2025-09-09 19:47:09] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -82.149040-0.002241j
[2025-09-09 19:47:44] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -81.925800+0.005259j
[2025-09-09 19:48:19] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -81.814019-0.002350j
[2025-09-09 19:48:53] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -81.925966+0.003069j
[2025-09-09 19:49:28] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -81.889693+0.001647j
[2025-09-09 19:50:03] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -81.913541+0.002424j
[2025-09-09 19:50:37] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -81.952147+0.001519j
[2025-09-09 19:51:12] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -81.860842+0.005207j
[2025-09-09 19:51:47] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -81.787133+0.000826j
[2025-09-09 19:52:21] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -81.784031+0.001469j
[2025-09-09 19:52:56] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -81.688761-0.002244j
[2025-09-09 19:53:31] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -81.796926+0.010304j
[2025-09-09 19:54:05] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -81.982136-0.002621j
[2025-09-09 19:54:40] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -81.992030+0.000504j
[2025-09-09 19:55:15] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -82.067382-0.000367j
[2025-09-09 19:55:49] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -81.947267-0.000482j
[2025-09-09 19:56:24] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -81.912929+0.005121j
[2025-09-09 19:56:59] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -81.947837+0.001038j
[2025-09-09 19:57:33] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -81.930884+0.000505j
[2025-09-09 19:58:08] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -82.030306-0.006390j
[2025-09-09 19:58:43] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -81.828761+0.000562j
[2025-09-09 19:59:17] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -81.841924+0.003132j
[2025-09-09 19:59:52] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -81.746982-0.000714j
[2025-09-09 20:00:27] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -81.799667+0.000711j
[2025-09-09 20:01:01] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -81.757644+0.003310j
[2025-09-09 20:01:36] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -81.723223+0.000716j
[2025-09-09 20:02:11] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -81.708868+0.002782j
[2025-09-09 20:02:45] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -81.726100+0.000048j
[2025-09-09 20:03:20] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -81.902204-0.001790j
[2025-09-09 20:03:55] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -81.884634+0.000848j
[2025-09-09 20:04:30] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -81.790185-0.003982j
[2025-09-09 20:05:04] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -81.908067-0.002139j
[2025-09-09 20:05:39] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -82.205315-0.001689j
[2025-09-09 20:06:14] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -82.114447-0.001181j
[2025-09-09 20:06:48] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -82.021646-0.001649j
[2025-09-09 20:07:20] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -81.955885-0.001050j
[2025-09-09 20:07:54] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -81.999692-0.001290j
[2025-09-09 20:08:30] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -82.025968+0.003669j
[2025-09-09 20:09:04] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -82.024214+0.001367j
[2025-09-09 20:09:40] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -82.009516-0.001245j
[2025-09-09 20:10:15] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -81.899113-0.003388j
[2025-09-09 20:10:49] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -81.730691-0.003533j
[2025-09-09 20:11:25] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -81.875918+0.004523j
[2025-09-09 20:12:00] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -81.693514+0.006431j
[2025-09-09 20:12:34] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -81.724262-0.004738j
[2025-09-09 20:13:09] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -81.835377-0.008795j
[2025-09-09 20:13:44] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -81.858934-0.004950j
[2025-09-09 20:14:19] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -81.788204-0.011481j
[2025-09-09 20:14:53] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -81.676513-0.000079j
[2025-09-09 20:15:28] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -81.792057+0.001832j
[2025-09-09 20:16:03] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -81.863778-0.001885j
[2025-09-09 20:16:37] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -81.828677+0.007309j
[2025-09-09 20:17:12] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -81.698467+0.003416j
[2025-09-09 20:17:47] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -81.747354+0.006538j
[2025-09-09 20:18:21] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -81.629305-0.003973j
[2025-09-09 20:18:56] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -81.640577-0.003908j
[2025-09-09 20:19:31] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -81.616716+0.001624j
[2025-09-09 20:20:05] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -81.541029+0.001478j
[2025-09-09 20:20:40] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -81.516330+0.001907j
[2025-09-09 20:21:15] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -81.655990+0.000611j
[2025-09-09 20:21:49] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -81.674413-0.002444j
[2025-09-09 20:22:24] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -81.540645+0.000550j
[2025-09-09 20:22:59] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -81.721609+0.002765j
[2025-09-09 20:23:33] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -81.728886-0.003848j
[2025-09-09 20:24:08] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -81.816380-0.001454j
[2025-09-09 20:24:43] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -81.898496+0.007921j
[2025-09-09 20:25:17] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -81.884391-0.002883j
[2025-09-09 20:25:52] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -81.818913-0.002982j
[2025-09-09 20:26:27] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -81.699576-0.004499j
[2025-09-09 20:27:01] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -81.866468+0.000906j
[2025-09-09 20:27:36] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -81.881555-0.002904j
[2025-09-09 20:28:11] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -81.807844+0.006170j
[2025-09-09 20:28:45] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -81.686381-0.001214j
[2025-09-09 20:29:20] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -81.543702-0.001896j
[2025-09-09 20:29:55] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -81.753579+0.001550j
[2025-09-09 20:30:29] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -81.644319-0.001984j
[2025-09-09 20:31:04] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -81.684671-0.001538j
[2025-09-09 20:31:39] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -81.814074-0.003425j
[2025-09-09 20:32:13] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -81.696941-0.001677j
[2025-09-09 20:32:48] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -81.646982+0.000380j
[2025-09-09 20:33:23] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -81.642236+0.003181j
[2025-09-09 20:33:57] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -81.680235-0.001272j
[2025-09-09 20:34:32] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -81.843085-0.000981j
[2025-09-09 20:35:07] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -81.778520-0.006135j
[2025-09-09 20:35:41] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -81.898638-0.000870j
[2025-09-09 20:36:16] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -82.035445-0.004984j
[2025-09-09 20:36:50] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -81.803206-0.002318j
[2025-09-09 20:37:25] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -81.852398+0.003034j
[2025-09-09 20:38:00] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -81.931694+0.004407j
[2025-09-09 20:38:34] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -81.858825-0.001208j
[2025-09-09 20:39:09] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -81.835374-0.005482j
[2025-09-09 20:39:44] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -81.942471-0.003811j
[2025-09-09 20:40:19] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -81.881778+0.001560j
[2025-09-09 20:40:53] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -81.845237-0.002159j
[2025-09-09 20:41:28] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -81.779476-0.004672j
[2025-09-09 20:42:03] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -81.830324-0.000373j
[2025-09-09 20:42:37] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -81.803828-0.004228j
[2025-09-09 20:43:12] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -81.880011+0.003991j
[2025-09-09 20:43:47] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -82.005749+0.004975j
[2025-09-09 20:44:21] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -81.795519-0.001354j
[2025-09-09 20:44:56] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -81.957216+0.002233j
[2025-09-09 20:45:31] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -81.915784-0.000732j
[2025-09-09 20:46:05] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -81.860067-0.002080j
[2025-09-09 20:46:40] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -81.613190-0.001125j
[2025-09-09 20:46:40] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-09 20:46:40] ✅ Training completed | Restarts: 2
[2025-09-09 20:46:40] ============================================================
[2025-09-09 20:46:40] Training completed | Runtime: 36568.4s
[2025-09-09 20:46:55] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-09 20:46:55] ============================================================
