[2025-09-11 02:21:36] ✓ 从checkpoint恢复: results/L=5/J2=0.05/J1=0.01/training/checkpoints/final_GCNN.pkl
[2025-09-11 02:21:36]   - 迭代次数: final
[2025-09-11 02:21:36]   - 能量: -78.574900+0.001096j ± 0.113798
[2025-09-11 02:21:36]   - 时间戳: 2025-09-11T02:21:06.626238+08:00
[2025-09-11 02:21:57] ✓ 变分状态参数已从checkpoint恢复
[2025-09-11 02:21:57] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-11 02:21:57] ==================================================
[2025-09-11 02:21:57] GCNN for Shastry-Sutherland Model
[2025-09-11 02:21:57] ==================================================
[2025-09-11 02:21:57] System parameters:
[2025-09-11 02:21:57]   - System size: L=5, N=100
[2025-09-11 02:21:57]   - System parameters: J1=-0.0, J2=0.05, Q=0.95
[2025-09-11 02:21:57] --------------------------------------------------
[2025-09-11 02:21:57] Model parameters:
[2025-09-11 02:21:57]   - Number of layers = 4
[2025-09-11 02:21:57]   - Number of features = 4
[2025-09-11 02:21:57]   - Total parameters = 19628
[2025-09-11 02:21:57] --------------------------------------------------
[2025-09-11 02:21:57] Training parameters:
[2025-09-11 02:21:57]   - Learning rate: 0.015
[2025-09-11 02:21:57]   - Total iterations: 1050
[2025-09-11 02:21:57]   - Annealing cycles: 3
[2025-09-11 02:21:57]   - Initial period: 150
[2025-09-11 02:21:57]   - Period multiplier: 2.0
[2025-09-11 02:21:57]   - Temperature range: 0.0-1.0
[2025-09-11 02:21:57]   - Samples: 4096
[2025-09-11 02:21:57]   - Discarded samples: 0
[2025-09-11 02:21:57]   - Chunk size: 2048
[2025-09-11 02:21:57]   - Diagonal shift: 0.2
[2025-09-11 02:21:57]   - Gradient clipping: 1.0
[2025-09-11 02:21:57]   - Checkpoint enabled: interval=105
[2025-09-11 02:21:57]   - Checkpoint directory: results/L=5/J2=0.05/J1=-0.00/training/checkpoints
[2025-09-11 02:21:57] --------------------------------------------------
[2025-09-11 02:21:57] Device status:
[2025-09-11 02:21:57]   - Devices model: NVIDIA H200 NVL
[2025-09-11 02:21:57]   - Number of devices: 1
[2025-09-11 02:21:57]   - Sharding: True
[2025-09-11 02:21:57] ============================================================
[2025-09-11 02:23:18] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -78.293679-0.016378j
[2025-09-11 02:24:06] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -78.118402-0.004229j
[2025-09-11 02:24:17] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -78.213055+0.001435j
[2025-09-11 02:24:32] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -78.133284+0.002395j
[2025-09-11 02:24:47] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -78.154729+0.000139j
[2025-09-11 02:25:02] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -78.056639+0.002574j
[2025-09-11 02:25:17] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -78.142226-0.000932j
[2025-09-11 02:25:32] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -77.917463-0.006504j
[2025-09-11 02:25:47] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -78.034822+0.000975j
[2025-09-11 02:26:02] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -78.188601+0.000799j
[2025-09-11 02:26:18] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -78.223576+0.001265j
[2025-09-11 02:26:33] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -78.144456-0.002646j
[2025-09-11 02:26:48] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -78.092154-0.001416j
[2025-09-11 02:27:03] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -77.973846-0.001228j
[2025-09-11 02:27:18] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -78.101991-0.001084j
[2025-09-11 02:27:33] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -78.234947-0.000949j
[2025-09-11 02:27:48] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -78.211989-0.002076j
[2025-09-11 02:28:04] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -78.342949-0.003266j
[2025-09-11 02:28:19] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -78.031679-0.000276j
[2025-09-11 02:28:34] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -78.133368-0.002124j
[2025-09-11 02:28:49] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -78.052890+0.001895j
[2025-09-11 02:29:04] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -78.081992+0.000685j
[2025-09-11 02:29:19] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -78.153756+0.002078j
[2025-09-11 02:29:34] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -78.206788-0.003212j
[2025-09-11 02:29:50] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -78.225192+0.002083j
[2025-09-11 02:30:05] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -78.182505+0.001182j
[2025-09-11 02:30:20] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -78.190244-0.003845j
[2025-09-11 02:30:35] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -78.269064-0.001075j
[2025-09-11 02:30:50] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -78.196177-0.004100j
[2025-09-11 02:31:05] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -78.222989+0.004062j
[2025-09-11 02:31:21] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -78.140403+0.002644j
[2025-09-11 02:31:36] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -78.229987-0.003543j
[2025-09-11 02:31:51] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -78.138867+0.004923j
[2025-09-11 02:32:06] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -78.196032+0.002527j
[2025-09-11 02:32:21] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -78.299313+0.001628j
[2025-09-11 02:32:36] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -78.268266-0.001080j
[2025-09-11 02:32:51] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -78.062378+0.000914j
[2025-09-11 02:33:07] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -78.086255+0.001140j
[2025-09-11 02:33:22] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -78.106734-0.001020j
[2025-09-11 02:33:37] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -77.997951-0.010688j
[2025-09-11 02:33:52] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -78.141447-0.003189j
[2025-09-11 02:34:07] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -78.199662-0.003149j
[2025-09-11 02:34:22] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -78.150946+0.000692j
[2025-09-11 02:34:38] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -78.075405-0.002674j
[2025-09-11 02:34:53] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -78.040240-0.002351j
[2025-09-11 02:35:08] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -77.814700+0.001783j
[2025-09-11 02:35:23] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -77.977158-0.001008j
[2025-09-11 02:35:38] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -78.000208-0.001461j
[2025-09-11 02:35:53] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -77.894171+0.006846j
[2025-09-11 02:36:08] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -77.918048-0.000413j
[2025-09-11 02:36:24] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -77.978809-0.001117j
[2025-09-11 02:36:39] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -78.089795-0.001481j
[2025-09-11 02:36:54] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -78.032875+0.002060j
[2025-09-11 02:37:09] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -78.057876-0.003340j
[2025-09-11 02:37:24] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -78.148059-0.004488j
[2025-09-11 02:37:39] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -78.095531-0.000150j
[2025-09-11 02:37:55] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -77.963928-0.001220j
[2025-09-11 02:38:10] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -77.996669-0.000396j
[2025-09-11 02:38:25] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -77.902497-0.001281j
[2025-09-11 02:38:40] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -78.137768-0.001802j
[2025-09-11 02:38:55] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -78.117132-0.001235j
[2025-09-11 02:39:10] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -78.082343-0.000955j
[2025-09-11 02:39:26] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -78.149576-0.001703j
[2025-09-11 02:39:41] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -78.028680+0.000895j
[2025-09-11 02:39:56] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -78.031390+0.001250j
[2025-09-11 02:40:11] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -78.251536+0.002617j
[2025-09-11 02:40:26] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -78.119533-0.001674j
[2025-09-11 02:40:41] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -78.209551+0.002926j
[2025-09-11 02:40:56] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -78.187438+0.003619j
[2025-09-11 02:41:12] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -78.223491-0.003131j
[2025-09-11 02:41:27] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -78.134316-0.002609j
[2025-09-11 02:41:42] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -78.135805+0.002437j
[2025-09-11 02:41:57] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -77.961757-0.006246j
[2025-09-11 02:42:12] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -77.870470-0.001313j
[2025-09-11 02:42:27] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -77.878368-0.000365j
[2025-09-11 02:42:42] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -78.043440+0.003654j
[2025-09-11 02:42:57] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -77.953262-0.001211j
[2025-09-11 02:43:13] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -78.014295+0.001593j
[2025-09-11 02:43:28] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -77.873347+0.002549j
[2025-09-11 02:43:43] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -78.158933-0.002284j
[2025-09-11 02:43:58] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -78.036255-0.002719j
[2025-09-11 02:44:13] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -78.169389+0.000193j
[2025-09-11 02:44:28] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -78.154238-0.000661j
[2025-09-11 02:44:43] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -78.109986-0.003872j
[2025-09-11 02:44:59] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -78.205243-0.001798j
[2025-09-11 02:45:14] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -78.231619-0.003711j
[2025-09-11 02:45:29] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -78.115926+0.001843j
[2025-09-11 02:45:44] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -77.991209-0.001367j
[2025-09-11 02:45:59] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -77.955156+0.001110j
[2025-09-11 02:46:14] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -77.946427-0.000006j
[2025-09-11 02:46:29] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -77.931817+0.001470j
[2025-09-11 02:46:45] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -77.961169+0.003302j
[2025-09-11 02:47:00] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -77.863053+0.002336j
[2025-09-11 02:47:15] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -78.090209-0.003191j
[2025-09-11 02:47:30] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -78.168977-0.002651j
[2025-09-11 02:47:45] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -78.136503+0.000673j
[2025-09-11 02:48:00] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -78.366512-0.001372j
[2025-09-11 02:48:16] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -78.181737-0.000989j
[2025-09-11 02:48:31] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -78.218246-0.000270j
[2025-09-11 02:48:46] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -78.146139-0.003623j
[2025-09-11 02:49:01] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -78.163871-0.001689j
[2025-09-11 02:49:16] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -78.217915-0.003203j
[2025-09-11 02:49:31] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -78.143293+0.000286j
[2025-09-11 02:49:46] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -78.187833+0.001116j
[2025-09-11 02:50:02] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -78.294026-0.001670j
[2025-09-11 02:50:02] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-11 02:50:17] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -78.328859-0.000578j
[2025-09-11 02:50:32] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -78.312131-0.001752j
[2025-09-11 02:50:47] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -78.157121-0.005375j
[2025-09-11 02:51:02] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -78.083692-0.001378j
[2025-09-11 02:51:17] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -78.101235-0.003435j
[2025-09-11 02:51:33] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -78.104839+0.002217j
[2025-09-11 02:51:48] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -78.245364+0.000614j
[2025-09-11 02:52:03] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -78.289740+0.000102j
[2025-09-11 02:52:18] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -78.160188+0.000754j
[2025-09-11 02:52:33] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -78.140888+0.001599j
[2025-09-11 02:52:48] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -78.219870+0.001570j
[2025-09-11 02:53:03] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -78.187372-0.002227j
[2025-09-11 02:53:18] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -78.175577+0.002835j
[2025-09-11 02:53:34] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -78.260192-0.002226j
[2025-09-11 02:53:49] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -78.262443-0.002024j
[2025-09-11 02:54:04] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -78.377145-0.003138j
[2025-09-11 02:54:19] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -78.222645-0.003876j
[2025-09-11 02:54:34] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -78.185048+0.005367j
[2025-09-11 02:54:49] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -78.343339+0.000438j
[2025-09-11 02:55:04] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -78.169016-0.004894j
[2025-09-11 02:55:19] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -78.233333+0.000452j
[2025-09-11 02:55:35] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -78.058689+0.001347j
[2025-09-11 02:55:50] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -77.997324+0.003376j
[2025-09-11 02:56:05] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -78.115385-0.002182j
[2025-09-11 02:56:20] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -78.042063+0.003459j
[2025-09-11 02:56:35] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -78.004613+0.003372j
[2025-09-11 02:56:50] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -78.155223-0.002269j
[2025-09-11 02:57:05] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -78.103372+0.003802j
[2025-09-11 02:57:21] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -78.205966-0.002858j
[2025-09-11 02:57:36] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -78.115331-0.009156j
[2025-09-11 02:57:51] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -78.189874-0.005898j
[2025-09-11 02:58:06] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -78.145550-0.004827j
[2025-09-11 02:58:21] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -78.135305-0.004905j
[2025-09-11 02:58:36] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -78.162815-0.000804j
[2025-09-11 02:58:51] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -78.188161+0.002652j
[2025-09-11 02:59:06] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -78.147931-0.003781j
[2025-09-11 02:59:22] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -78.179412+0.001798j
[2025-09-11 02:59:37] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -78.200449+0.001498j
[2025-09-11 02:59:52] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -78.267812+0.000635j
[2025-09-11 03:00:07] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -78.199393-0.001442j
[2025-09-11 03:00:22] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -78.168982-0.000743j
[2025-09-11 03:00:37] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -78.301431+0.001073j
[2025-09-11 03:00:53] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -78.064160-0.000746j
[2025-09-11 03:01:08] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -78.098222+0.002340j
[2025-09-11 03:01:23] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -78.297307-0.000151j
[2025-09-11 03:01:23] RESTART #1 | Period: 300
[2025-09-11 03:01:38] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -78.167947-0.000776j
[2025-09-11 03:01:53] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -78.084317-0.001119j
[2025-09-11 03:02:08] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -78.183532+0.001079j
[2025-09-11 03:02:23] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -78.077734+0.002330j
[2025-09-11 03:02:38] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -78.062768+0.002849j
[2025-09-11 03:02:54] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -78.218304-0.001227j
[2025-09-11 03:03:09] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -78.167774+0.000125j
[2025-09-11 03:03:24] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -78.036121+0.003144j
[2025-09-11 03:03:39] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -78.142603-0.001763j
[2025-09-11 03:03:54] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -78.025446-0.002713j
[2025-09-11 03:04:09] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -78.143264+0.001699j
[2025-09-11 03:04:24] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -78.111257-0.000921j
[2025-09-11 03:04:40] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -78.004006+0.001119j
[2025-09-11 03:04:55] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -77.978822-0.000079j
[2025-09-11 03:05:10] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -77.885056+0.003282j
[2025-09-11 03:05:25] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -77.881499+0.000534j
[2025-09-11 03:05:40] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -77.968144+0.003159j
[2025-09-11 03:05:55] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -78.035558+0.000592j
[2025-09-11 03:06:10] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -78.029787-0.004878j
[2025-09-11 03:06:26] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -78.037545-0.001300j
[2025-09-11 03:06:41] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -78.019737+0.000257j
[2025-09-11 03:06:56] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -77.995541-0.005796j
[2025-09-11 03:07:11] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -78.099946-0.001125j
[2025-09-11 03:07:26] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -78.156259+0.003430j
[2025-09-11 03:07:41] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -78.106091-0.003133j
[2025-09-11 03:07:57] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -78.190412+0.000369j
[2025-09-11 03:08:12] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -78.327562+0.000939j
[2025-09-11 03:08:27] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -78.153409-0.007135j
[2025-09-11 03:08:42] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -78.135095-0.000351j
[2025-09-11 03:08:57] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -78.078581+0.001435j
[2025-09-11 03:09:12] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -78.116919+0.001732j
[2025-09-11 03:09:27] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -78.146463-0.000101j
[2025-09-11 03:09:43] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -78.002392+0.000752j
[2025-09-11 03:09:58] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -77.889785-0.002505j
[2025-09-11 03:10:13] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -78.030733-0.002931j
[2025-09-11 03:10:28] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -78.127463-0.004691j
[2025-09-11 03:10:43] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -78.143899-0.002964j
[2025-09-11 03:10:58] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -78.156512+0.001377j
[2025-09-11 03:11:13] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -77.997093-0.000707j
[2025-09-11 03:11:28] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -78.175352-0.002163j
[2025-09-11 03:11:44] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -78.062790+0.002965j
[2025-09-11 03:11:59] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -77.986862-0.003046j
[2025-09-11 03:12:14] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -77.961685+0.001480j
[2025-09-11 03:12:29] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -78.073903-0.001172j
[2025-09-11 03:12:44] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -77.908348+0.001975j
[2025-09-11 03:12:59] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -78.089844+0.002641j
[2025-09-11 03:13:15] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -78.013208-0.001591j
[2025-09-11 03:13:30] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -77.956531-0.005659j
[2025-09-11 03:13:45] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -77.957073-0.000450j
[2025-09-11 03:14:00] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -78.110172+0.003628j
[2025-09-11 03:14:15] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -78.061609+0.001916j
[2025-09-11 03:14:30] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -78.147968+0.000996j
[2025-09-11 03:14:45] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -78.179881+0.001634j
[2025-09-11 03:15:01] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -78.185560+0.001327j
[2025-09-11 03:15:16] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -78.206944-0.003984j
[2025-09-11 03:15:31] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -78.152174+0.001575j
[2025-09-11 03:15:46] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -78.200312-0.000708j
[2025-09-11 03:16:01] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -78.272289-0.003071j
[2025-09-11 03:16:16] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -78.209091+0.001564j
[2025-09-11 03:16:31] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -78.179858-0.001342j
[2025-09-11 03:16:31] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-11 03:16:47] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -78.169204-0.000509j
[2025-09-11 03:17:02] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -78.149009-0.000235j
[2025-09-11 03:17:17] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -78.293249+0.000291j
[2025-09-11 03:17:32] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -78.239624+0.001185j
[2025-09-11 03:17:47] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -78.221635+0.000014j
[2025-09-11 03:18:02] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -78.286769+0.001822j
[2025-09-11 03:18:18] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -78.207126+0.000373j
[2025-09-11 03:18:33] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -78.304509+0.000316j
[2025-09-11 03:18:48] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -78.453175+0.001975j
[2025-09-11 03:19:03] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -78.394089+0.000578j
[2025-09-11 03:19:18] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -78.213113-0.001608j
[2025-09-11 03:19:33] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -78.254574-0.002291j
[2025-09-11 03:19:48] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -78.148602+0.005491j
[2025-09-11 03:20:03] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -78.258836+0.003622j
[2025-09-11 03:20:19] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -78.169685+0.002951j
[2025-09-11 03:20:34] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -78.160330-0.005069j
[2025-09-11 03:20:49] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -78.043400+0.002932j
[2025-09-11 03:21:04] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -78.113441+0.001932j
[2025-09-11 03:21:19] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -78.130604+0.002075j
[2025-09-11 03:21:34] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -78.142188+0.001230j
[2025-09-11 03:21:49] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -78.148451+0.002079j
[2025-09-11 03:22:05] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -78.150069-0.000985j
[2025-09-11 03:22:20] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -78.029210+0.003465j
[2025-09-11 03:22:35] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -78.100059-0.000231j
[2025-09-11 03:22:50] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -77.925603-0.001017j
[2025-09-11 03:23:05] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -78.074663-0.001796j
[2025-09-11 03:23:20] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -78.100786+0.005026j
[2025-09-11 03:23:35] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -78.027587-0.000619j
[2025-09-11 03:23:51] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -78.143824+0.002577j
[2025-09-11 03:24:06] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -78.050018-0.001308j
[2025-09-11 03:24:21] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -78.022666-0.000322j
[2025-09-11 03:24:36] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -78.009293-0.000236j
[2025-09-11 03:24:51] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -77.945821+0.002851j
[2025-09-11 03:25:06] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -78.022139+0.002658j
[2025-09-11 03:25:21] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -78.007351+0.002557j
[2025-09-11 03:25:36] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -78.147529+0.004278j
[2025-09-11 03:25:52] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -78.036708-0.001587j
[2025-09-11 03:26:07] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -77.912384+0.003876j
[2025-09-11 03:26:22] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -77.903825-0.000507j
[2025-09-11 03:26:37] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -77.906600+0.005939j
[2025-09-11 03:26:52] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -78.094497-0.005800j
[2025-09-11 03:27:07] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -78.103487-0.001130j
[2025-09-11 03:27:23] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -78.172242-0.002589j
[2025-09-11 03:27:38] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -78.091377+0.001025j
[2025-09-11 03:27:53] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -78.143675-0.000355j
[2025-09-11 03:28:08] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -78.235589-0.000465j
[2025-09-11 03:28:23] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -78.217970-0.004273j
[2025-09-11 03:28:38] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -78.137381-0.002305j
[2025-09-11 03:28:54] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -78.149152-0.004493j
[2025-09-11 03:29:09] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -78.144021+0.000073j
[2025-09-11 03:29:24] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -78.010224+0.003759j
[2025-09-11 03:29:39] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -78.055450+0.003787j
[2025-09-11 03:29:54] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -77.991145-0.001178j
[2025-09-11 03:30:09] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -78.127800-0.003304j
[2025-09-11 03:30:24] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -78.119590-0.004832j
[2025-09-11 03:30:40] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -77.989276+0.000921j
[2025-09-11 03:30:55] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -78.110111-0.003206j
[2025-09-11 03:31:10] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -78.065312+0.000018j
[2025-09-11 03:31:25] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -78.015674+0.000059j
[2025-09-11 03:31:40] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -77.955245+0.002755j
[2025-09-11 03:31:55] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -78.137889+0.005968j
[2025-09-11 03:32:10] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -78.323162+0.000017j
[2025-09-11 03:32:26] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -78.237181+0.001839j
[2025-09-11 03:32:41] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -78.108756+0.001730j
[2025-09-11 03:32:56] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -78.150408+0.002689j
[2025-09-11 03:33:11] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -78.142222+0.004296j
[2025-09-11 03:33:26] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -78.094192-0.003551j
[2025-09-11 03:33:41] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -77.954328+0.000521j
[2025-09-11 03:33:56] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -78.086915-0.002858j
[2025-09-11 03:34:12] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -77.966301-0.003326j
[2025-09-11 03:34:27] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -78.054846+0.003957j
[2025-09-11 03:34:42] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -78.083733-0.000272j
[2025-09-11 03:34:57] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -78.119430-0.001650j
[2025-09-11 03:35:12] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -78.140895+0.000445j
[2025-09-11 03:35:27] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -78.247069+0.003384j
[2025-09-11 03:35:42] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -78.144175+0.000452j
[2025-09-11 03:35:58] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -78.226688+0.001678j
[2025-09-11 03:36:13] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -78.254846+0.001511j
[2025-09-11 03:36:28] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -78.257091+0.001303j
[2025-09-11 03:36:43] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -78.062920-0.000858j
[2025-09-11 03:36:58] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -78.156138+0.001740j
[2025-09-11 03:37:13] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -78.174285+0.001906j
[2025-09-11 03:37:28] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -78.034000+0.001702j
[2025-09-11 03:37:44] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -78.074695-0.004340j
[2025-09-11 03:37:59] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -78.106830-0.002254j
[2025-09-11 03:38:14] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -78.005335-0.001190j
[2025-09-11 03:38:29] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -78.036387-0.000250j
[2025-09-11 03:38:44] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -78.019259-0.003479j
[2025-09-11 03:38:59] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -78.089086+0.005108j
[2025-09-11 03:39:14] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -78.140707-0.007123j
[2025-09-11 03:39:30] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -77.845036-0.000030j
[2025-09-11 03:39:45] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -77.966939-0.001439j
[2025-09-11 03:40:00] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -77.891926-0.001121j
[2025-09-11 03:40:15] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -77.935340-0.001113j
[2025-09-11 03:40:30] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -78.113830-0.000592j
[2025-09-11 03:40:45] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -78.037584+0.000641j
[2025-09-11 03:41:00] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -78.105905-0.000741j
[2025-09-11 03:41:16] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -77.943434+0.002839j
[2025-09-11 03:41:31] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -78.106554-0.004575j
[2025-09-11 03:41:46] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -78.110354-0.001789j
[2025-09-11 03:42:01] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -77.994727-0.002850j
[2025-09-11 03:42:16] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -78.170816+0.002911j
[2025-09-11 03:42:31] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -78.197254-0.000481j
[2025-09-11 03:42:46] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -78.070700+0.005300j
[2025-09-11 03:43:02] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -78.198334-0.005322j
[2025-09-11 03:43:02] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-11 03:43:17] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -78.367685-0.001840j
[2025-09-11 03:43:32] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -78.303671-0.001185j
[2025-09-11 03:43:47] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -78.148907-0.006355j
[2025-09-11 03:44:02] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -78.168491+0.000388j
[2025-09-11 03:44:17] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -78.174505-0.001806j
[2025-09-11 03:44:32] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -78.228684-0.003462j
[2025-09-11 03:44:48] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -78.154325-0.001581j
[2025-09-11 03:45:03] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -78.085063+0.000907j
[2025-09-11 03:45:18] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -77.880464-0.000744j
[2025-09-11 03:45:33] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -78.092916+0.002378j
[2025-09-11 03:45:48] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -78.129651-0.002591j
[2025-09-11 03:46:03] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -78.099492+0.004116j
[2025-09-11 03:46:18] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -77.934354-0.002208j
[2025-09-11 03:46:34] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -77.921107+0.000641j
[2025-09-11 03:46:49] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -77.932251+0.001235j
[2025-09-11 03:47:04] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -78.003673-0.003743j
[2025-09-11 03:47:19] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -78.074291+0.000192j
[2025-09-11 03:47:34] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -78.094949+0.001296j
[2025-09-11 03:47:49] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -78.032401+0.002031j
[2025-09-11 03:48:05] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -78.178027-0.000242j
[2025-09-11 03:48:20] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -78.123702+0.007102j
[2025-09-11 03:48:35] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -78.043220+0.001170j
[2025-09-11 03:48:50] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -78.306468+0.003306j
[2025-09-11 03:49:05] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -78.183255+0.001823j
[2025-09-11 03:49:20] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -77.942247-0.004645j
[2025-09-11 03:49:35] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -77.991916-0.000637j
[2025-09-11 03:49:50] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -77.927634+0.000427j
[2025-09-11 03:50:06] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -77.933340-0.001585j
[2025-09-11 03:50:21] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -78.012762+0.002200j
[2025-09-11 03:50:36] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -78.005718-0.007952j
[2025-09-11 03:50:51] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -77.964323-0.002801j
[2025-09-11 03:51:06] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -77.882720+0.000953j
[2025-09-11 03:51:21] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -78.041200-0.001576j
[2025-09-11 03:51:37] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -78.108789-0.004076j
[2025-09-11 03:51:52] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -78.004621+0.001597j
[2025-09-11 03:52:07] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -78.140258+0.001684j
[2025-09-11 03:52:22] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -78.356972+0.004863j
[2025-09-11 03:52:37] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -78.358506-0.001458j
[2025-09-11 03:52:52] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -78.258290-0.002768j
[2025-09-11 03:53:08] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -78.436844+0.001398j
[2025-09-11 03:53:23] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -78.368175+0.006315j
[2025-09-11 03:53:38] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -78.275423+0.003075j
[2025-09-11 03:53:53] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -78.233662-0.000295j
[2025-09-11 03:54:08] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -78.164671+0.001784j
[2025-09-11 03:54:23] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -78.152644+0.001459j
[2025-09-11 03:54:38] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -78.119735-0.002456j
[2025-09-11 03:54:53] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -78.153498-0.007009j
[2025-09-11 03:55:09] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -78.057240-0.000507j
[2025-09-11 03:55:24] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -77.948015+0.001050j
[2025-09-11 03:55:39] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -78.003067+0.000270j
[2025-09-11 03:55:54] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -78.102885-0.003436j
[2025-09-11 03:56:09] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -77.988352+0.001648j
[2025-09-11 03:56:24] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -78.061565-0.000147j
[2025-09-11 03:56:39] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -78.217402+0.002764j
[2025-09-11 03:56:55] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -78.026332-0.005364j
[2025-09-11 03:57:10] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -78.120291+0.006540j
[2025-09-11 03:57:25] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -78.103578-0.002449j
[2025-09-11 03:57:40] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -78.200391-0.000549j
[2025-09-11 03:57:55] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -78.099369-0.003582j
[2025-09-11 03:58:10] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -78.120668+0.000370j
[2025-09-11 03:58:25] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -78.072088+0.001945j
[2025-09-11 03:58:40] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -77.938513-0.001384j
[2025-09-11 03:58:56] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -78.052706+0.004805j
[2025-09-11 03:59:11] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -78.032659+0.000722j
[2025-09-11 03:59:26] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -78.002617+0.002349j
[2025-09-11 03:59:41] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -77.854013+0.000487j
[2025-09-11 03:59:56] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -77.936101+0.003494j
[2025-09-11 04:00:11] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -78.034100+0.001723j
[2025-09-11 04:00:26] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -78.073014+0.002000j
[2025-09-11 04:00:42] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -78.174157-0.003135j
[2025-09-11 04:00:57] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -78.163728-0.000756j
[2025-09-11 04:01:12] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -78.167469+0.000673j
[2025-09-11 04:01:27] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -78.198269-0.000787j
[2025-09-11 04:01:42] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -78.273407+0.004107j
[2025-09-11 04:01:57] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -78.191776-0.009463j
[2025-09-11 04:02:12] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -78.322025-0.006140j
[2025-09-11 04:02:28] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -78.203463+0.002696j
[2025-09-11 04:02:43] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -78.295629-0.000239j
[2025-09-11 04:02:58] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -78.351453-0.002027j
[2025-09-11 04:03:13] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -78.262742-0.001781j
[2025-09-11 04:03:28] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -78.167498-0.002419j
[2025-09-11 04:03:43] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -78.289262-0.003572j
[2025-09-11 04:03:58] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -78.220265-0.000260j
[2025-09-11 04:04:14] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -78.062205-0.007753j
[2025-09-11 04:04:29] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -78.219856-0.003619j
[2025-09-11 04:04:44] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -78.145596+0.004967j
[2025-09-11 04:04:59] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -78.183488+0.001300j
[2025-09-11 04:05:14] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -78.187251+0.005179j
[2025-09-11 04:05:29] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -78.122999+0.002021j
[2025-09-11 04:05:44] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -78.058317+0.001649j
[2025-09-11 04:06:00] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -78.076095-0.004599j
[2025-09-11 04:06:15] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -78.103599+0.000285j
[2025-09-11 04:06:30] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -77.973504-0.000289j
[2025-09-11 04:06:45] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -78.095797-0.001461j
[2025-09-11 04:07:00] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -78.078105-0.001899j
[2025-09-11 04:07:15] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -78.201798+0.001428j
[2025-09-11 04:07:30] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -78.195240-0.001355j
[2025-09-11 04:07:45] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -78.208356+0.001638j
[2025-09-11 04:08:01] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -78.265153+0.002062j
[2025-09-11 04:08:16] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -78.163519+0.002453j
[2025-09-11 04:08:31] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -78.208146+0.000970j
[2025-09-11 04:08:46] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -78.192949+0.002053j
[2025-09-11 04:09:01] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -78.231702+0.000030j
[2025-09-11 04:09:16] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -78.242436-0.002629j
[2025-09-11 04:09:31] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -78.301216-0.003198j
[2025-09-11 04:09:32] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-11 04:09:47] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -78.321553-0.000467j
[2025-09-11 04:10:02] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -78.268121+0.002689j
[2025-09-11 04:10:17] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -78.485520+0.002315j
[2025-09-11 04:10:32] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -78.365557-0.003295j
[2025-09-11 04:10:47] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -78.263479-0.003764j
[2025-09-11 04:11:03] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -78.211022+0.000922j
[2025-09-11 04:11:18] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -78.227922+0.001994j
[2025-09-11 04:11:33] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -78.210291+0.002492j
[2025-09-11 04:11:48] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -78.243638+0.002073j
[2025-09-11 04:12:03] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -78.106356-0.000699j
[2025-09-11 04:12:18] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -78.216655-0.000035j
[2025-09-11 04:12:33] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -78.151377-0.001650j
[2025-09-11 04:12:48] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -78.146008-0.001127j
[2025-09-11 04:13:04] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -78.194177-0.000997j
[2025-09-11 04:13:19] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -78.245675+0.000110j
[2025-09-11 04:13:34] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -78.150979-0.001412j
[2025-09-11 04:13:49] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -78.058242-0.001807j
[2025-09-11 04:14:04] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -78.216032+0.001175j
[2025-09-11 04:14:19] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -78.067575+0.002537j
[2025-09-11 04:14:34] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -78.173625-0.001146j
[2025-09-11 04:14:50] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -78.224947+0.005365j
[2025-09-11 04:15:05] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -78.124625-0.000954j
[2025-09-11 04:15:20] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -78.189703+0.000672j
[2025-09-11 04:15:35] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -78.192014+0.001388j
[2025-09-11 04:15:50] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -78.214680-0.001524j
[2025-09-11 04:16:05] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -78.271219+0.000859j
[2025-09-11 04:16:20] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -78.243104-0.003007j
[2025-09-11 04:16:35] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -78.257447-0.004179j
[2025-09-11 04:16:51] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -78.244211+0.003059j
[2025-09-11 04:17:06] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -78.318252-0.001418j
[2025-09-11 04:17:06] RESTART #2 | Period: 600
[2025-09-11 04:17:21] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -78.186900+0.011339j
[2025-09-11 04:17:36] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -78.049314+0.002500j
[2025-09-11 04:17:51] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -78.062422+0.001370j
[2025-09-11 04:18:06] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -78.006793-0.000872j
[2025-09-11 04:18:21] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -78.005830+0.002720j
[2025-09-11 04:18:37] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -77.966646-0.002760j
[2025-09-11 04:18:52] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -77.945191+0.000945j
[2025-09-11 04:19:07] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -77.935713-0.003457j
[2025-09-11 04:19:22] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -78.154381+0.001840j
[2025-09-11 04:19:37] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -77.969655+0.001409j
[2025-09-11 04:19:52] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -78.117179+0.001355j
[2025-09-11 04:20:07] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -78.216547+0.000687j
[2025-09-11 04:20:23] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -78.194381+0.003194j
[2025-09-11 04:20:38] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -78.201801-0.002517j
[2025-09-11 04:20:53] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -78.173007+0.004550j
[2025-09-11 04:21:08] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -78.030224+0.000702j
[2025-09-11 04:21:23] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -78.172891-0.001453j
[2025-09-11 04:21:38] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -78.277643+0.001961j
[2025-09-11 04:21:54] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -78.263478-0.003593j
[2025-09-11 04:22:09] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -78.198131-0.000633j
[2025-09-11 04:22:24] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -78.213712-0.002817j
[2025-09-11 04:22:39] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -78.090832+0.002997j
[2025-09-11 04:22:54] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -78.251458-0.001291j
[2025-09-11 04:23:09] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -78.204179+0.001620j
[2025-09-11 04:23:25] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -78.133828-0.002267j
[2025-09-11 04:23:40] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -78.189325-0.004756j
[2025-09-11 04:23:55] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -78.260009+0.000128j
[2025-09-11 04:24:10] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -78.328139+0.004920j
[2025-09-11 04:24:25] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -78.189333-0.002912j
[2025-09-11 04:24:40] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -78.085575-0.002802j
[2025-09-11 04:24:55] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -78.219513-0.002329j
[2025-09-11 04:25:11] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -78.154659-0.002658j
[2025-09-11 04:25:26] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -78.092109-0.004378j
[2025-09-11 04:25:41] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -78.129970+0.001779j
[2025-09-11 04:25:56] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -78.318274-0.001305j
[2025-09-11 04:26:11] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -78.141423+0.000999j
[2025-09-11 04:26:26] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -78.244675+0.000241j
[2025-09-11 04:26:41] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -78.247403+0.005302j
[2025-09-11 04:26:57] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -78.249079+0.003224j
[2025-09-11 04:27:12] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -78.269889+0.002751j
[2025-09-11 04:27:27] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -78.193482+0.001969j
[2025-09-11 04:27:42] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -78.383857-0.001955j
[2025-09-11 04:27:57] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -78.351176+0.003270j
[2025-09-11 04:28:12] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -78.314516+0.003069j
[2025-09-11 04:28:27] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -78.187309-0.005419j
[2025-09-11 04:28:43] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -78.194054+0.003583j
[2025-09-11 04:28:58] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -78.193242-0.003483j
[2025-09-11 04:29:13] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -78.283030-0.002348j
[2025-09-11 04:29:28] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -78.289691+0.001984j
[2025-09-11 04:29:43] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -78.336983+0.000028j
[2025-09-11 04:29:58] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -78.158364-0.000656j
[2025-09-11 04:30:14] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -78.080152-0.000331j
[2025-09-11 04:30:29] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -78.346781-0.006214j
[2025-09-11 04:30:44] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -78.300298+0.001142j
[2025-09-11 04:30:59] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -78.259120-0.003920j
[2025-09-11 04:31:14] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -78.233052+0.003015j
[2025-09-11 04:31:29] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -78.168153+0.001220j
[2025-09-11 04:31:44] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -78.157300-0.004743j
[2025-09-11 04:32:00] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -78.037902+0.003964j
[2025-09-11 04:32:15] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -78.023920+0.001104j
[2025-09-11 04:32:30] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -78.092027-0.002809j
[2025-09-11 04:32:45] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -78.350369-0.005215j
[2025-09-11 04:33:00] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -78.199674-0.005211j
[2025-09-11 04:33:15] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -78.058774+0.001913j
[2025-09-11 04:33:30] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -78.058465-0.000065j
[2025-09-11 04:33:46] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -78.109719+0.000832j
[2025-09-11 04:34:01] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -78.049175-0.003899j
[2025-09-11 04:34:16] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -78.053287-0.003368j
[2025-09-11 04:34:31] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -78.065117-0.000781j
[2025-09-11 04:34:46] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -77.972037+0.004859j
[2025-09-11 04:35:01] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -78.259814-0.000295j
[2025-09-11 04:35:16] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -78.253325+0.001136j
[2025-09-11 04:35:32] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -78.154878-0.006054j
[2025-09-11 04:35:47] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -78.290499-0.002591j
[2025-09-11 04:36:02] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -78.286978+0.006084j
[2025-09-11 04:36:02] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-11 04:36:17] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -78.301646+0.001889j
[2025-09-11 04:36:32] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -78.371004+0.000658j
[2025-09-11 04:36:47] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -78.267186+0.004322j
[2025-09-11 04:37:03] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -78.307970-0.005953j
[2025-09-11 04:37:18] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -78.291859+0.003634j
[2025-09-11 04:37:33] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -78.226448+0.001359j
[2025-09-11 04:37:48] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -78.249637+0.002997j
[2025-09-11 04:38:03] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -78.246050-0.003519j
[2025-09-11 04:38:18] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -78.237012+0.003440j
[2025-09-11 04:38:33] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -78.227779+0.005376j
[2025-09-11 04:38:49] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -78.142313+0.000316j
[2025-09-11 04:39:04] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -78.034461+0.004931j
[2025-09-11 04:39:19] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -78.204860-0.000854j
[2025-09-11 04:39:34] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -78.119335-0.000777j
[2025-09-11 04:39:49] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -78.205060-0.000511j
[2025-09-11 04:40:04] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -78.139631-0.005303j
[2025-09-11 04:40:20] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -78.229931+0.001055j
[2025-09-11 04:40:35] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -78.191848-0.003918j
[2025-09-11 04:40:50] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -77.915723+0.000496j
[2025-09-11 04:41:05] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -78.099085+0.004936j
[2025-09-11 04:41:20] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -78.103933+0.002675j
[2025-09-11 04:41:35] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -78.123582-0.000221j
[2025-09-11 04:41:50] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -77.914100-0.002532j
[2025-09-11 04:42:06] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -77.958309+0.007985j
[2025-09-11 04:42:21] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -78.083388-0.002673j
[2025-09-11 04:42:36] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -78.284467+0.000854j
[2025-09-11 04:42:51] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -78.132203+0.005052j
[2025-09-11 04:43:06] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -78.228421-0.001472j
[2025-09-11 04:43:21] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -78.318084+0.001050j
[2025-09-11 04:43:37] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -78.201704-0.007980j
[2025-09-11 04:43:52] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -78.033017+0.000565j
[2025-09-11 04:44:07] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -78.180952+0.000514j
[2025-09-11 04:44:22] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -78.096147+0.000895j
[2025-09-11 04:44:37] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -78.142839+0.006564j
[2025-09-11 04:44:52] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -77.984233+0.003539j
[2025-09-11 04:45:08] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -78.109794-0.003493j
[2025-09-11 04:45:23] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -78.333095-0.001153j
[2025-09-11 04:45:38] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -78.260236-0.000394j
[2025-09-11 04:45:53] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -78.078321-0.010180j
[2025-09-11 04:46:08] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -78.121768-0.002325j
[2025-09-11 04:46:23] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -78.219818+0.004134j
[2025-09-11 04:46:38] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -78.192042-0.000541j
[2025-09-11 04:46:54] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -78.157538-0.000929j
[2025-09-11 04:47:09] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -78.042917+0.003120j
[2025-09-11 04:47:24] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -78.037816+0.000449j
[2025-09-11 04:47:39] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -77.997872+0.000595j
[2025-09-11 04:47:54] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -77.942828-0.004569j
[2025-09-11 04:48:09] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -78.058335+0.003923j
[2025-09-11 04:48:24] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -78.033547+0.001150j
[2025-09-11 04:48:40] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -77.951830+0.004467j
[2025-09-11 04:48:55] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -78.065102+0.000206j
[2025-09-11 04:49:10] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -78.069735+0.001898j
[2025-09-11 04:49:25] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -78.028242+0.000610j
[2025-09-11 04:49:40] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -78.015649-0.004471j
[2025-09-11 04:49:55] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -78.054121+0.001479j
[2025-09-11 04:50:11] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -78.220723-0.002818j
[2025-09-11 04:50:26] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -78.106419+0.003195j
[2025-09-11 04:50:41] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -78.250720+0.000550j
[2025-09-11 04:50:56] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -78.121096-0.000424j
[2025-09-11 04:51:11] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -78.139574+0.000661j
[2025-09-11 04:51:26] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -78.249325+0.002754j
[2025-09-11 04:51:41] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -78.182315-0.000334j
[2025-09-11 04:51:56] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -78.119051-0.000312j
[2025-09-11 04:52:12] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -78.128443-0.001830j
[2025-09-11 04:52:27] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -78.046382-0.000910j
[2025-09-11 04:52:42] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -78.055137+0.001689j
[2025-09-11 04:52:57] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -78.170344-0.001395j
[2025-09-11 04:53:12] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -78.015075-0.001865j
[2025-09-11 04:53:27] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -78.168308-0.000973j
[2025-09-11 04:53:43] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -78.161267-0.002959j
[2025-09-11 04:53:58] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -78.067158+0.000437j
[2025-09-11 04:54:13] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -78.100531-0.000613j
[2025-09-11 04:54:28] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -78.091287-0.001935j
[2025-09-11 04:54:43] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -78.102505+0.006763j
[2025-09-11 04:54:58] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -78.193411+0.002905j
[2025-09-11 04:55:14] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -78.146361+0.003120j
[2025-09-11 04:55:29] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -78.259438-0.000452j
[2025-09-11 04:55:44] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -78.032709-0.002438j
[2025-09-11 04:55:59] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -78.030943-0.001120j
[2025-09-11 04:56:14] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -78.149010-0.002139j
[2025-09-11 04:56:29] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -78.186336+0.000081j
[2025-09-11 04:56:44] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -78.155647+0.001105j
[2025-09-11 04:56:59] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -78.137327+0.005848j
[2025-09-11 04:57:14] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -78.164123+0.005046j
[2025-09-11 04:57:30] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -78.266640-0.001356j
[2025-09-11 04:57:45] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -78.320229+0.000903j
[2025-09-11 04:58:00] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -78.229684-0.004497j
[2025-09-11 04:58:15] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -78.297736+0.001344j
[2025-09-11 04:58:30] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -78.305035+0.003912j
[2025-09-11 04:58:45] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -78.316644+0.000217j
[2025-09-11 04:59:00] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -78.024212+0.003604j
[2025-09-11 04:59:16] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -78.024997+0.002285j
[2025-09-11 04:59:31] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -78.044850-0.003396j
[2025-09-11 04:59:46] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -77.971951+0.001944j
[2025-09-11 05:00:01] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -78.075192+0.002846j
[2025-09-11 05:00:16] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -77.923315-0.001460j
[2025-09-11 05:00:32] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -78.102509-0.002987j
[2025-09-11 05:00:47] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -78.137160+0.000859j
[2025-09-11 05:01:02] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -78.160936+0.003100j
[2025-09-11 05:01:17] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -78.108711+0.000418j
[2025-09-11 05:01:32] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -78.195059-0.000891j
[2025-09-11 05:01:47] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -78.143273-0.006129j
[2025-09-11 05:02:02] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -78.077857-0.001506j
[2025-09-11 05:02:18] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -78.149959+0.000953j
[2025-09-11 05:02:33] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -78.194033-0.002526j
[2025-09-11 05:02:33] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-11 05:02:48] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -78.283467+0.004188j
[2025-09-11 05:03:03] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -78.104607-0.002638j
[2025-09-11 05:03:18] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -78.177801+0.001857j
[2025-09-11 05:03:33] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -78.068018+0.000131j
[2025-09-11 05:03:48] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -78.189811-0.000329j
[2025-09-11 05:04:03] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -78.202822-0.005800j
[2025-09-11 05:04:19] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -78.090738-0.002426j
[2025-09-11 05:04:34] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -78.066415-0.005613j
[2025-09-11 05:04:49] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -77.895009+0.000155j
[2025-09-11 05:05:04] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -77.983869-0.001358j
[2025-09-11 05:05:19] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -77.978812-0.003025j
[2025-09-11 05:05:34] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -77.959981-0.000683j
[2025-09-11 05:05:50] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -78.085116+0.002775j
[2025-09-11 05:06:05] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -78.154159-0.001513j
[2025-09-11 05:06:20] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -78.227603-0.001284j
[2025-09-11 05:06:35] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -78.233817+0.003808j
[2025-09-11 05:06:50] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -78.123790+0.002385j
[2025-09-11 05:07:05] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -78.196759-0.000441j
[2025-09-11 05:07:20] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -78.203365-0.001072j
[2025-09-11 05:07:35] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -78.148239-0.000572j
[2025-09-11 05:07:51] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -78.344827+0.001000j
[2025-09-11 05:08:06] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -78.181971-0.001730j
[2025-09-11 05:08:21] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -78.244763+0.000526j
[2025-09-11 05:08:36] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -78.304524-0.000896j
[2025-09-11 05:08:51] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -78.179732-0.002489j
[2025-09-11 05:09:06] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -78.227555+0.003928j
[2025-09-11 05:09:21] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -78.304184-0.002799j
[2025-09-11 05:09:37] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -78.356344+0.003264j
[2025-09-11 05:09:52] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -78.183513-0.001641j
[2025-09-11 05:10:07] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -78.167267-0.000449j
[2025-09-11 05:10:22] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -78.092302-0.002833j
[2025-09-11 05:10:37] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -78.108030-0.000173j
[2025-09-11 05:10:52] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -77.963892+0.001671j
[2025-09-11 05:11:08] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -77.983185+0.002197j
[2025-09-11 05:11:23] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -78.040071-0.001657j
[2025-09-11 05:11:38] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -77.974391-0.003522j
[2025-09-11 05:11:53] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -78.094075-0.000979j
[2025-09-11 05:12:08] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -78.075268-0.004107j
[2025-09-11 05:12:23] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -77.980594+0.001458j
[2025-09-11 05:12:38] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -77.967503+0.002485j
[2025-09-11 05:12:53] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -77.912609+0.003576j
[2025-09-11 05:13:09] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -77.878347+0.001609j
[2025-09-11 05:13:24] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -77.957792+0.002094j
[2025-09-11 05:13:39] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -77.945600-0.000040j
[2025-09-11 05:13:54] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -77.950826-0.001668j
[2025-09-11 05:14:09] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -77.890892-0.002855j
[2025-09-11 05:14:24] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -77.969774-0.001212j
[2025-09-11 05:14:39] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -77.883277-0.001917j
[2025-09-11 05:14:55] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -78.055687-0.002315j
[2025-09-11 05:15:10] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -78.075026+0.002647j
[2025-09-11 05:15:25] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -78.023691-0.001433j
[2025-09-11 05:15:40] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -77.947585-0.003430j
[2025-09-11 05:15:55] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -77.825784-0.000230j
[2025-09-11 05:16:10] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -78.092192+0.001434j
[2025-09-11 05:16:26] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -78.080087-0.000325j
[2025-09-11 05:16:41] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -78.142750-0.004461j
[2025-09-11 05:16:56] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -78.077646+0.001908j
[2025-09-11 05:17:11] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -77.903823+0.003893j
[2025-09-11 05:17:26] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -78.078978-0.000164j
[2025-09-11 05:17:41] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -78.170208+0.007815j
[2025-09-11 05:17:56] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -78.069480-0.005213j
[2025-09-11 05:18:11] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -78.124253-0.002802j
[2025-09-11 05:18:27] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -78.111546-0.001964j
[2025-09-11 05:18:42] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -78.176015-0.001002j
[2025-09-11 05:18:57] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -78.111144+0.000647j
[2025-09-11 05:19:12] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -78.129956+0.002405j
[2025-09-11 05:19:27] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -78.077258-0.002344j
[2025-09-11 05:19:42] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -78.183881-0.002601j
[2025-09-11 05:19:58] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -78.074612+0.004434j
[2025-09-11 05:20:13] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -78.064398-0.000810j
[2025-09-11 05:20:28] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -77.966470+0.001436j
[2025-09-11 05:20:43] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -77.972755+0.000398j
[2025-09-11 05:20:58] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -78.017918-0.001718j
[2025-09-11 05:21:13] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -78.093846+0.002534j
[2025-09-11 05:21:28] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -78.115997+0.001463j
[2025-09-11 05:21:44] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -78.202639-0.000320j
[2025-09-11 05:21:59] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -78.078821-0.002372j
[2025-09-11 05:22:14] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -78.002423+0.000995j
[2025-09-11 05:22:29] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -78.027134+0.002494j
[2025-09-11 05:22:44] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -78.149516+0.001941j
[2025-09-11 05:22:59] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -78.215810-0.000764j
[2025-09-11 05:23:14] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -78.162806-0.010071j
[2025-09-11 05:23:30] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -78.055157+0.000110j
[2025-09-11 05:23:45] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -78.230297+0.001764j
[2025-09-11 05:24:00] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -78.222995+0.003430j
[2025-09-11 05:24:15] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -78.252800+0.000655j
[2025-09-11 05:24:30] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -78.335739-0.002241j
[2025-09-11 05:24:45] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -78.197178-0.001100j
[2025-09-11 05:25:00] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -78.199488-0.004675j
[2025-09-11 05:25:16] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -78.264451+0.002943j
[2025-09-11 05:25:31] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -78.152575-0.001453j
[2025-09-11 05:25:46] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -78.382217-0.001009j
[2025-09-11 05:26:01] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -78.367177+0.004189j
[2025-09-11 05:26:16] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -78.240628-0.002633j
[2025-09-11 05:26:31] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -78.248042+0.003030j
[2025-09-11 05:26:46] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -78.279595+0.000079j
[2025-09-11 05:27:01] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -78.308085+0.004560j
[2025-09-11 05:27:17] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -78.250193+0.004447j
[2025-09-11 05:27:32] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -78.254271+0.002881j
[2025-09-11 05:27:47] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -78.399731-0.000317j
[2025-09-11 05:28:02] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -78.218224+0.001747j
[2025-09-11 05:28:17] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -78.118035+0.000994j
[2025-09-11 05:28:32] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -78.235228+0.005398j
[2025-09-11 05:28:48] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -78.106006+0.005132j
[2025-09-11 05:29:03] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -78.282754-0.004086j
[2025-09-11 05:29:03] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-11 05:29:18] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -78.232638+0.001333j
[2025-09-11 05:29:33] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -78.240448-0.001456j
[2025-09-11 05:29:48] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -78.280199-0.004671j
[2025-09-11 05:30:03] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -78.198725-0.000802j
[2025-09-11 05:30:19] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -78.078133+0.002485j
[2025-09-11 05:30:34] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -78.200644+0.002662j
[2025-09-11 05:30:49] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -78.053719+0.001515j
[2025-09-11 05:31:04] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -78.103903-0.002672j
[2025-09-11 05:31:19] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -77.912788+0.000708j
[2025-09-11 05:31:34] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -78.040308-0.001557j
[2025-09-11 05:31:50] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -78.155944+0.002339j
[2025-09-11 05:32:05] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -78.128760+0.002734j
[2025-09-11 05:32:20] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -78.196762+0.002317j
[2025-09-11 05:32:35] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -78.108359+0.002863j
[2025-09-11 05:32:50] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -78.170101-0.001103j
[2025-09-11 05:33:05] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -78.148396-0.002783j
[2025-09-11 05:33:20] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -78.206224-0.000687j
[2025-09-11 05:33:36] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -78.181629-0.001371j
[2025-09-11 05:33:51] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -78.103317-0.001491j
[2025-09-11 05:34:06] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -78.147513-0.000328j
[2025-09-11 05:34:21] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -78.100842-0.002954j
[2025-09-11 05:34:36] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -78.282268+0.000647j
[2025-09-11 05:34:51] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -78.149449+0.000902j
[2025-09-11 05:35:06] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -78.048353-0.003489j
[2025-09-11 05:35:21] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -78.235652+0.000012j
[2025-09-11 05:35:37] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -78.156221+0.002378j
[2025-09-11 05:35:52] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -78.208676+0.003047j
[2025-09-11 05:36:07] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -78.224167-0.001287j
[2025-09-11 05:36:22] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -78.120206+0.003944j
[2025-09-11 05:36:37] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -78.047244-0.005093j
[2025-09-11 05:36:52] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -78.140628-0.002340j
[2025-09-11 05:37:07] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -78.085814-0.000770j
[2025-09-11 05:37:23] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -77.974008+0.000490j
[2025-09-11 05:37:38] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -77.875817-0.005453j
[2025-09-11 05:37:53] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -77.816924-0.004375j
[2025-09-11 05:38:08] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -77.917477+0.001036j
[2025-09-11 05:38:23] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -77.976324+0.002869j
[2025-09-11 05:38:38] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -78.159744-0.001243j
[2025-09-11 05:38:53] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -77.927995-0.001060j
[2025-09-11 05:39:08] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -77.802421-0.001807j
[2025-09-11 05:39:24] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -77.786422+0.000508j
[2025-09-11 05:39:39] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -77.991603-0.000077j
[2025-09-11 05:39:54] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -78.035463+0.003090j
[2025-09-11 05:40:09] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -78.055033-0.000527j
[2025-09-11 05:40:24] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -77.971089-0.002693j
[2025-09-11 05:40:39] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -78.018184+0.001866j
[2025-09-11 05:40:54] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -78.082946+0.002644j
[2025-09-11 05:41:09] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -78.044054+0.000387j
[2025-09-11 05:41:25] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -78.188153+0.001112j
[2025-09-11 05:41:40] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -78.264799-0.003740j
[2025-09-11 05:41:55] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -78.023990-0.001065j
[2025-09-11 05:42:10] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -77.935602+0.001240j
[2025-09-11 05:42:25] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -78.132703+0.003507j
[2025-09-11 05:42:40] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -78.127375+0.001465j
[2025-09-11 05:42:55] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -78.079253-0.003899j
[2025-09-11 05:43:11] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -78.029520+0.001207j
[2025-09-11 05:43:26] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -78.010966-0.001577j
[2025-09-11 05:43:41] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -78.101962+0.002582j
[2025-09-11 05:43:56] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -78.088085+0.001679j
[2025-09-11 05:44:11] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -78.132716+0.003942j
[2025-09-11 05:44:27] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -78.123785+0.000105j
[2025-09-11 05:44:42] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -77.903303-0.005109j
[2025-09-11 05:44:57] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -78.111371-0.001064j
[2025-09-11 05:45:12] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -78.193191-0.001443j
[2025-09-11 05:45:27] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -78.240856+0.001538j
[2025-09-11 05:45:42] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -78.154292-0.001349j
[2025-09-11 05:45:57] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -78.131431+0.000536j
[2025-09-11 05:46:13] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -78.319998-0.000079j
[2025-09-11 05:46:28] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -78.364469-0.003149j
[2025-09-11 05:46:43] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -78.115568-0.001341j
[2025-09-11 05:46:58] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -78.074911-0.000790j
[2025-09-11 05:47:13] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -78.133008+0.001533j
[2025-09-11 05:47:28] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -78.127467+0.000746j
[2025-09-11 05:47:43] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -78.084091+0.001979j
[2025-09-11 05:47:58] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -78.062785-0.001498j
[2025-09-11 05:48:13] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -78.201131-0.002114j
[2025-09-11 05:48:28] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -78.086397-0.000224j
[2025-09-11 05:48:44] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -78.154426-0.001977j
[2025-09-11 05:48:59] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -77.995023-0.000468j
[2025-09-11 05:49:14] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -78.049537+0.002835j
[2025-09-11 05:49:29] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -78.189713-0.000508j
[2025-09-11 05:49:44] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -78.046624+0.005008j
[2025-09-11 05:49:59] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -78.048134+0.002349j
[2025-09-11 05:50:15] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -77.947770-0.003349j
[2025-09-11 05:50:30] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -78.054406+0.001983j
[2025-09-11 05:50:45] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -78.082996+0.001674j
[2025-09-11 05:51:00] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -78.078913+0.001115j
[2025-09-11 05:51:15] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -78.137802+0.001143j
[2025-09-11 05:51:30] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -78.215339+0.000421j
[2025-09-11 05:51:45] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -78.191748-0.002113j
[2025-09-11 05:52:00] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -78.225065+0.002688j
[2025-09-11 05:52:16] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -78.224225-0.001376j
[2025-09-11 05:52:31] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -78.126125+0.000289j
[2025-09-11 05:52:46] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -78.161085-0.002506j
[2025-09-11 05:53:01] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -78.170852+0.001260j
[2025-09-11 05:53:16] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -78.124625+0.005247j
[2025-09-11 05:53:31] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -78.109495-0.001354j
[2025-09-11 05:53:47] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -78.118050-0.001616j
[2025-09-11 05:54:02] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -78.322142-0.002036j
[2025-09-11 05:54:17] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -78.199079+0.001915j
[2025-09-11 05:54:32] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -78.106923+0.002657j
[2025-09-11 05:54:47] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -78.213578-0.000078j
[2025-09-11 05:55:02] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -78.115547+0.002802j
[2025-09-11 05:55:17] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -78.070993+0.002241j
[2025-09-11 05:55:33] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -77.924809-0.001005j
[2025-09-11 05:55:33] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-11 05:55:48] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -78.035605+0.001040j
[2025-09-11 05:56:03] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -78.141073+0.004794j
[2025-09-11 05:56:18] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -78.247303-0.001584j
[2025-09-11 05:56:33] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -78.011381+0.000378j
[2025-09-11 05:56:48] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -78.115130-0.002780j
[2025-09-11 05:57:03] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -78.154026+0.001597j
[2025-09-11 05:57:19] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -78.090145+0.000531j
[2025-09-11 05:57:34] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -78.069670+0.000325j
[2025-09-11 05:57:49] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -78.017107+0.001737j
[2025-09-11 05:58:04] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -78.018950-0.002402j
[2025-09-11 05:58:19] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -77.947981-0.023672j
[2025-09-11 05:58:34] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -77.911787-0.000184j
[2025-09-11 05:58:49] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -78.073991+0.001950j
[2025-09-11 05:59:05] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -78.106232-0.005284j
[2025-09-11 05:59:20] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -77.927416-0.004622j
[2025-09-11 05:59:35] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -77.911992-0.003102j
[2025-09-11 05:59:50] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -77.987445+0.000509j
[2025-09-11 06:00:05] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -78.051442+0.005552j
[2025-09-11 06:00:20] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -78.288431-0.001450j
[2025-09-11 06:00:36] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -78.095001-0.004456j
[2025-09-11 06:00:51] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -78.148534+0.002917j
[2025-09-11 06:01:06] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -78.060610-0.002261j
[2025-09-11 06:01:21] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -78.254722-0.005332j
[2025-09-11 06:01:36] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -78.338511+0.000333j
[2025-09-11 06:01:51] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -78.227881+0.001774j
[2025-09-11 06:02:07] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -78.247551+0.000823j
[2025-09-11 06:02:22] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -78.242605+0.004820j
[2025-09-11 06:02:37] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -78.240247-0.001670j
[2025-09-11 06:02:52] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -78.361897+0.000614j
[2025-09-11 06:03:07] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -78.289670-0.000629j
[2025-09-11 06:03:22] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -78.169196+0.002378j
[2025-09-11 06:03:37] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -78.159893-0.000922j
[2025-09-11 06:03:52] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -78.145498-0.003487j
[2025-09-11 06:04:08] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -78.127433-0.002808j
[2025-09-11 06:04:23] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -78.217532-0.000578j
[2025-09-11 06:04:38] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -78.066348+0.003052j
[2025-09-11 06:04:53] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -78.078417+0.001427j
[2025-09-11 06:05:08] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -78.182121+0.001208j
[2025-09-11 06:05:23] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -78.077984-0.001793j
[2025-09-11 06:05:38] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -78.063160+0.003353j
[2025-09-11 06:05:54] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -78.095108+0.001865j
[2025-09-11 06:06:09] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -77.986023-0.000864j
[2025-09-11 06:06:24] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -77.893320-0.000640j
[2025-09-11 06:06:39] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -78.182585+0.000988j
[2025-09-11 06:06:54] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -78.041683+0.000361j
[2025-09-11 06:07:09] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -78.115891-0.004108j
[2025-09-11 06:07:25] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -77.969313-0.001041j
[2025-09-11 06:07:40] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -77.931355+0.001203j
[2025-09-11 06:07:55] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -77.887249+0.001871j
[2025-09-11 06:08:10] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -78.015664+0.000495j
[2025-09-11 06:08:25] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -78.152653+0.002289j
[2025-09-11 06:08:40] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -78.188475-0.000269j
[2025-09-11 06:08:55] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -78.184419+0.003889j
[2025-09-11 06:09:11] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -78.150038-0.000196j
[2025-09-11 06:09:26] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -78.004235-0.004462j
[2025-09-11 06:09:41] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -78.047944+0.003988j
[2025-09-11 06:09:56] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -78.099670+0.004337j
[2025-09-11 06:10:11] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -78.191774-0.000410j
[2025-09-11 06:10:26] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -78.073692-0.002060j
[2025-09-11 06:10:42] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -77.849320+0.000284j
[2025-09-11 06:10:57] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -78.013736+0.001729j
[2025-09-11 06:11:12] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -78.069935+0.001936j
[2025-09-11 06:11:27] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -78.002827-0.002253j
[2025-09-11 06:11:42] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -77.908869+0.004253j
[2025-09-11 06:11:57] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -77.998017-0.000892j
[2025-09-11 06:12:12] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -78.248250-0.001704j
[2025-09-11 06:12:28] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -78.305420+0.000460j
[2025-09-11 06:12:43] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -78.325155-0.003185j
[2025-09-11 06:12:58] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -78.343703-0.001353j
[2025-09-11 06:13:13] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -78.394298+0.002553j
[2025-09-11 06:13:28] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -78.242145-0.002654j
[2025-09-11 06:13:43] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -78.127444-0.001275j
[2025-09-11 06:13:58] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -78.049920-0.003970j
[2025-09-11 06:14:14] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -78.122649-0.003279j
[2025-09-11 06:14:29] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -78.025792-0.003332j
[2025-09-11 06:14:44] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -78.054490+0.005341j
[2025-09-11 06:14:59] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -78.094333+0.001647j
[2025-09-11 06:15:14] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -78.195002+0.000260j
[2025-09-11 06:15:30] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -78.041316+0.003609j
[2025-09-11 06:15:45] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -78.007887+0.003515j
[2025-09-11 06:16:00] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -77.985887-0.004042j
[2025-09-11 06:16:15] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -78.071510-0.000677j
[2025-09-11 06:16:30] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -78.261821-0.000583j
[2025-09-11 06:16:45] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -78.150375-0.001905j
[2025-09-11 06:17:01] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -78.175902+0.000948j
[2025-09-11 06:17:16] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -77.983478-0.000707j
[2025-09-11 06:17:31] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -77.996301-0.001170j
[2025-09-11 06:17:46] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -77.861788-0.003544j
[2025-09-11 06:18:01] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -78.114779-0.002163j
[2025-09-11 06:18:16] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -78.102976+0.002558j
[2025-09-11 06:18:31] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -77.975858+0.001651j
[2025-09-11 06:18:47] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -78.031479-0.000311j
[2025-09-11 06:19:02] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -78.074953-0.002508j
[2025-09-11 06:19:17] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -78.271750+0.000403j
[2025-09-11 06:19:32] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -78.147478+0.002962j
[2025-09-11 06:19:47] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -78.112131+0.002477j
[2025-09-11 06:20:02] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -78.068555+0.000838j
[2025-09-11 06:20:18] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -77.999058+0.002941j
[2025-09-11 06:20:33] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -78.126691+0.004101j
[2025-09-11 06:20:48] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -78.207219+0.006529j
[2025-09-11 06:21:03] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -78.149139+0.004822j
[2025-09-11 06:21:18] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -78.227835+0.002388j
[2025-09-11 06:21:33] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -78.220548-0.002243j
[2025-09-11 06:21:48] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -78.128935+0.002139j
[2025-09-11 06:22:04] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -78.055289+0.000067j
[2025-09-11 06:22:04] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-11 06:22:19] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -78.030518+0.000817j
[2025-09-11 06:22:34] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -78.147283+0.002411j
[2025-09-11 06:22:49] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -78.267943-0.000057j
[2025-09-11 06:23:04] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -78.220327-0.005824j
[2025-09-11 06:23:19] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -78.122195+0.001392j
[2025-09-11 06:23:35] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -78.180952+0.000386j
[2025-09-11 06:23:50] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -78.029500-0.001948j
[2025-09-11 06:24:05] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -78.187488-0.004228j
[2025-09-11 06:24:20] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -78.090781+0.001695j
[2025-09-11 06:24:35] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -78.134690+0.001547j
[2025-09-11 06:24:50] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -78.156542-0.003442j
[2025-09-11 06:25:05] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -78.197393+0.005481j
[2025-09-11 06:25:21] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -78.166024-0.000909j
[2025-09-11 06:25:36] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -78.148079+0.002038j
[2025-09-11 06:25:51] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -78.039407+0.003223j
[2025-09-11 06:26:06] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -78.108162+0.000855j
[2025-09-11 06:26:21] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -78.107475-0.002005j
[2025-09-11 06:26:36] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -77.981651-0.001649j
[2025-09-11 06:26:52] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -78.062091+0.002648j
[2025-09-11 06:27:07] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -78.100910+0.001846j
[2025-09-11 06:27:22] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -78.084242-0.004280j
[2025-09-11 06:27:37] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -78.118657-0.000030j
[2025-09-11 06:27:52] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -78.176341+0.001275j
[2025-09-11 06:28:07] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -78.216834+0.000368j
[2025-09-11 06:28:22] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -77.982315-0.001955j
[2025-09-11 06:28:37] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -78.200467+0.004855j
[2025-09-11 06:28:53] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -78.192760+0.002274j
[2025-09-11 06:29:08] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -78.072262-0.004574j
[2025-09-11 06:29:23] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -78.222325-0.001626j
[2025-09-11 06:29:38] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -78.145506+0.001403j
[2025-09-11 06:29:53] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -78.005879-0.001113j
[2025-09-11 06:30:08] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -78.085912-0.004416j
[2025-09-11 06:30:24] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -78.074772+0.002368j
[2025-09-11 06:30:39] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -78.189931+0.000527j
[2025-09-11 06:30:54] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -77.986317+0.000564j
[2025-09-11 06:31:09] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -78.066099+0.000219j
[2025-09-11 06:31:24] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -78.047993-0.000052j
[2025-09-11 06:31:39] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -78.049009+0.000614j
[2025-09-11 06:31:54] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -77.959790-0.001737j
[2025-09-11 06:32:10] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -77.980648-0.006556j
[2025-09-11 06:32:25] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -77.928606+0.056560j
[2025-09-11 06:32:40] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -77.974402-0.001575j
[2025-09-11 06:32:55] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -78.102787-0.001496j
[2025-09-11 06:33:10] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -78.064426-0.001748j
[2025-09-11 06:33:25] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -78.125851-0.001370j
[2025-09-11 06:33:41] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -77.979174+0.004244j
[2025-09-11 06:33:56] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -78.046164+0.002534j
[2025-09-11 06:34:11] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -77.928823-0.001204j
[2025-09-11 06:34:26] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -77.943161+0.000469j
[2025-09-11 06:34:41] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -77.936360+0.004808j
[2025-09-11 06:34:56] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -77.969832+0.001234j
[2025-09-11 06:35:12] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -78.074733-0.000593j
[2025-09-11 06:35:27] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -77.934077-0.000576j
[2025-09-11 06:35:42] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -77.950202+0.005024j
[2025-09-11 06:35:57] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -77.928226+0.000547j
[2025-09-11 06:36:12] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -78.026257-0.005017j
[2025-09-11 06:36:27] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -78.007695-0.001017j
[2025-09-11 06:36:42] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -77.884649+0.000626j
[2025-09-11 06:36:57] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -77.776035-0.002362j
[2025-09-11 06:37:13] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -77.978077+0.001375j
[2025-09-11 06:37:28] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -77.995744-0.009825j
[2025-09-11 06:37:43] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -78.119916-0.000475j
[2025-09-11 06:37:58] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -78.052121+0.000973j
[2025-09-11 06:38:13] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -78.189421+0.002568j
[2025-09-11 06:38:28] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -78.110968-0.002835j
[2025-09-11 06:38:43] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -78.007452+0.006916j
[2025-09-11 06:38:58] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -78.175607+0.000316j
[2025-09-11 06:39:14] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -78.211737+0.001498j
[2025-09-11 06:39:29] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -78.362999-0.002249j
[2025-09-11 06:39:44] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -78.367468+0.003096j
[2025-09-11 06:39:59] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -78.403329-0.003459j
[2025-09-11 06:40:14] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -78.408638+0.000492j
[2025-09-11 06:40:29] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -78.262728-0.000104j
[2025-09-11 06:40:44] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -78.345931+0.000148j
[2025-09-11 06:41:00] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -78.259680-0.000372j
[2025-09-11 06:41:15] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -78.292137-0.000125j
[2025-09-11 06:41:30] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -78.260993-0.000382j
[2025-09-11 06:41:45] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -78.139086+0.001120j
[2025-09-11 06:42:00] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -78.192555+0.000269j
[2025-09-11 06:42:15] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -78.190235-0.002468j
[2025-09-11 06:42:31] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -78.178935-0.000659j
[2025-09-11 06:42:46] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -78.314664-0.001296j
[2025-09-11 06:43:01] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -78.202552-0.004651j
[2025-09-11 06:43:16] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -78.171568+0.003121j
[2025-09-11 06:43:31] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -78.193555-0.002473j
[2025-09-11 06:43:46] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -78.132271+0.001770j
[2025-09-11 06:44:01] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -78.159865+0.000023j
[2025-09-11 06:44:17] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -78.066732+0.005962j
[2025-09-11 06:44:32] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -77.991783-0.001705j
[2025-09-11 06:44:47] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -77.935790+0.005940j
[2025-09-11 06:45:02] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -77.931168+0.000081j
[2025-09-11 06:45:17] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -77.984842+0.003414j
[2025-09-11 06:45:32] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -78.131417-0.005754j
[2025-09-11 06:45:48] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -78.054041-0.006684j
[2025-09-11 06:46:03] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -78.065370+0.001746j
[2025-09-11 06:46:18] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -78.100963+0.002708j
[2025-09-11 06:46:33] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -78.143605-0.005505j
[2025-09-11 06:46:48] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -78.115964-0.004627j
[2025-09-11 06:47:03] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -78.080948+0.002669j
[2025-09-11 06:47:18] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -77.860180+0.000382j
[2025-09-11 06:47:33] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -78.050574-0.000477j
[2025-09-11 06:47:49] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -78.021316-0.005671j
[2025-09-11 06:48:04] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -78.115901+0.001352j
[2025-09-11 06:48:19] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -78.165175-0.001756j
[2025-09-11 06:48:34] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -78.084435+0.001965j
[2025-09-11 06:48:34] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-11 06:48:34] ✅ Training completed | Restarts: 2
[2025-09-11 06:48:34] ============================================================
[2025-09-11 06:48:34] Training completed | Runtime: 15996.9s
[2025-09-11 06:48:40] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-11 06:48:40] ============================================================
