[2025-09-10 17:21:00] ✓ 从checkpoint恢复: results/L=5/J2=0.05/J1=0.08/training/checkpoints/final_GCNN.pkl
[2025-09-10 17:21:00]   - 迭代次数: final
[2025-09-10 17:21:00]   - 能量: -82.733107-0.000499j ± 0.104462
[2025-09-10 17:21:00]   - 时间戳: 2025-09-10T17:20:38.554864+08:00
[2025-09-10 17:21:29] ✓ 变分状态参数已从checkpoint恢复
[2025-09-10 17:21:29] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-10 17:21:29] ==================================================
[2025-09-10 17:21:29] GCNN for Shastry-Sutherland Model
[2025-09-10 17:21:29] ==================================================
[2025-09-10 17:21:29] System parameters:
[2025-09-10 17:21:29]   - System size: L=5, N=100
[2025-09-10 17:21:29]   - System parameters: J1=0.09, J2=0.05, Q=0.95
[2025-09-10 17:21:29] --------------------------------------------------
[2025-09-10 17:21:29] Model parameters:
[2025-09-10 17:21:29]   - Number of layers = 4
[2025-09-10 17:21:29]   - Number of features = 4
[2025-09-10 17:21:29]   - Total parameters = 19628
[2025-09-10 17:21:29] --------------------------------------------------
[2025-09-10 17:21:29] Training parameters:
[2025-09-10 17:21:29]   - Learning rate: 0.015
[2025-09-10 17:21:29]   - Total iterations: 1050
[2025-09-10 17:21:29]   - Annealing cycles: 3
[2025-09-10 17:21:29]   - Initial period: 150
[2025-09-10 17:21:29]   - Period multiplier: 2.0
[2025-09-10 17:21:29]   - Temperature range: 0.0-1.0
[2025-09-10 17:21:29]   - Samples: 4096
[2025-09-10 17:21:29]   - Discarded samples: 0
[2025-09-10 17:21:29]   - Chunk size: 2048
[2025-09-10 17:21:29]   - Diagonal shift: 0.2
[2025-09-10 17:21:29]   - Gradient clipping: 1.0
[2025-09-10 17:21:29]   - Checkpoint enabled: interval=105
[2025-09-10 17:21:29]   - Checkpoint directory: results/L=5/J2=0.05/J1=0.09/training/checkpoints
[2025-09-10 17:21:29] --------------------------------------------------
[2025-09-10 17:21:29] Device status:
[2025-09-10 17:21:29]   - Devices model: NVIDIA H200 NVL
[2025-09-10 17:21:29]   - Number of devices: 1
[2025-09-10 17:21:29]   - Sharding: True
[2025-09-10 17:21:29] ============================================================
[2025-09-10 17:23:56] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -83.908463-0.023194j
[2025-09-10 17:25:32] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -83.802160-0.007823j
[2025-09-10 17:26:07] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -83.854543-0.014050j
[2025-09-10 17:26:42] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -83.667872-0.004679j
[2025-09-10 17:27:17] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -83.799566-0.005553j
[2025-09-10 17:27:52] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -83.868200-0.007386j
[2025-09-10 17:28:27] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -83.697457-0.007135j
[2025-09-10 17:29:02] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -83.801327-0.004285j
[2025-09-10 17:29:37] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -83.645379-0.005868j
[2025-09-10 17:30:12] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -83.652982-0.004132j
[2025-09-10 17:30:48] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -83.687640-0.003338j
[2025-09-10 17:31:23] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -83.609185+0.001675j
[2025-09-10 17:31:58] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -83.518328-0.000364j
[2025-09-10 17:32:33] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -83.611181-0.005007j
[2025-09-10 17:33:08] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -83.615305+0.004690j
[2025-09-10 17:33:43] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -83.640870-0.000540j
[2025-09-10 17:34:18] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -83.680048-0.005262j
[2025-09-10 17:34:53] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -83.504163-0.004679j
[2025-09-10 17:35:28] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -83.628636-0.002077j
[2025-09-10 17:36:04] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -83.587293+0.001174j
[2025-09-10 17:36:39] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -83.613867+0.000010j
[2025-09-10 17:37:14] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -83.718721-0.005029j
[2025-09-10 17:37:49] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -83.729978-0.003445j
[2025-09-10 17:38:24] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -83.731896-0.001318j
[2025-09-10 17:39:00] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -83.660413+0.001159j
[2025-09-10 17:39:35] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -83.607302-0.000952j
[2025-09-10 17:40:10] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -83.620084+0.002399j
[2025-09-10 17:40:45] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -83.627610-0.000951j
[2025-09-10 17:41:20] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -83.752819-0.001635j
[2025-09-10 17:41:55] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -83.630636-0.001572j
[2025-09-10 17:42:31] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -83.577412+0.003606j
[2025-09-10 17:43:06] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -83.617609+0.001934j
[2025-09-10 17:43:41] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -83.667964-0.004054j
[2025-09-10 17:44:16] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -83.719089-0.000661j
[2025-09-10 17:44:51] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -83.664870+0.000072j
[2025-09-10 17:45:26] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -83.575312+0.000339j
[2025-09-10 17:46:01] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -83.594125-0.002137j
[2025-09-10 17:46:36] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -83.682417-0.004038j
[2025-09-10 17:47:11] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -83.542305+0.001433j
[2025-09-10 17:47:46] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -83.489024-0.000153j
[2025-09-10 17:48:21] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -83.610956-0.003194j
[2025-09-10 17:48:56] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -83.546085-0.000572j
[2025-09-10 17:49:31] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -83.481062+0.001438j
[2025-09-10 17:50:06] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -83.477523+0.000923j
[2025-09-10 17:50:41] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -83.587114-0.003114j
[2025-09-10 17:51:16] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -83.740412+0.001762j
[2025-09-10 17:51:52] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -83.616585-0.003753j
[2025-09-10 17:52:27] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -83.640465+0.001243j
[2025-09-10 17:53:02] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -83.778851-0.004093j
[2025-09-10 17:53:37] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -83.726307-0.001583j
[2025-09-10 17:54:12] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -83.714434-0.003939j
[2025-09-10 17:54:47] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -83.767178+0.001656j
[2025-09-10 17:55:22] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -83.621808+0.004893j
[2025-09-10 17:55:57] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -83.690520+0.004509j
[2025-09-10 17:56:32] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -83.687059+0.001429j
[2025-09-10 17:57:07] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -83.856991-0.001725j
[2025-09-10 17:57:42] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -83.787552+0.004655j
[2025-09-10 17:58:17] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -83.863443+0.001816j
[2025-09-10 17:58:52] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -83.717257-0.002839j
[2025-09-10 17:59:28] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -83.719522-0.007064j
[2025-09-10 18:00:03] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -83.828789-0.002194j
[2025-09-10 18:00:38] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -83.700316-0.000382j
[2025-09-10 18:01:13] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -83.662314-0.004357j
[2025-09-10 18:01:48] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -83.743821+0.003081j
[2025-09-10 18:02:23] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -83.839420+0.001269j
[2025-09-10 18:02:59] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -83.721273-0.000984j
[2025-09-10 18:03:34] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -83.602131+0.001167j
[2025-09-10 18:04:09] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -83.766104+0.000971j
[2025-09-10 18:04:44] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -83.664782-0.002095j
[2025-09-10 18:05:19] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -83.643772+0.001562j
[2025-09-10 18:05:54] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -83.743610-0.004882j
[2025-09-10 18:06:29] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -83.645155+0.000229j
[2025-09-10 18:07:04] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -83.677235-0.003187j
[2025-09-10 18:07:40] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -83.751274+0.003583j
[2025-09-10 18:08:15] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -83.617406-0.000406j
[2025-09-10 18:08:50] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -83.559738-0.006326j
[2025-09-10 18:09:25] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -83.753391+0.001502j
[2025-09-10 18:10:00] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -83.699038+0.003308j
[2025-09-10 18:10:35] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -83.686221+0.004330j
[2025-09-10 18:11:10] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -83.627087-0.001634j
[2025-09-10 18:11:45] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -83.622767-0.000993j
[2025-09-10 18:12:20] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -83.718041+0.000690j
[2025-09-10 18:12:56] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -83.561572-0.003596j
[2025-09-10 18:13:31] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -83.555671+0.002037j
[2025-09-10 18:14:06] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -83.612549+0.002890j
[2025-09-10 18:14:41] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -83.634506+0.000803j
[2025-09-10 18:15:16] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -83.755993-0.002890j
[2025-09-10 18:15:51] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -83.766107+0.001222j
[2025-09-10 18:16:26] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -83.644345-0.001457j
[2025-09-10 18:17:01] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -83.651008+0.001908j
[2025-09-10 18:17:36] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -83.558855-0.001005j
[2025-09-10 18:18:11] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -83.344833+0.004129j
[2025-09-10 18:18:47] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -83.541536+0.001169j
[2025-09-10 18:19:22] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -83.503852-0.001755j
[2025-09-10 18:19:57] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -83.543597+0.001557j
[2025-09-10 18:20:32] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -83.608266-0.007549j
[2025-09-10 18:21:07] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -83.601772+0.002660j
[2025-09-10 18:21:42] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -83.660899+0.000060j
[2025-09-10 18:22:17] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -83.739997-0.000486j
[2025-09-10 18:22:52] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -83.733339+0.000696j
[2025-09-10 18:23:27] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -83.723392+0.001005j
[2025-09-10 18:24:02] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -83.752935-0.000253j
[2025-09-10 18:24:38] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -83.663056-0.001868j
[2025-09-10 18:25:13] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -83.645375-0.000167j
[2025-09-10 18:25:48] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -83.722470+0.001299j
[2025-09-10 18:25:48] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-10 18:26:23] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -83.661972-0.001007j
[2025-09-10 18:26:58] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -83.616327+0.003530j
[2025-09-10 18:27:33] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -83.677026-0.001197j
[2025-09-10 18:28:08] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -83.592766+0.003178j
[2025-09-10 18:28:43] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -83.580073+0.000265j
[2025-09-10 18:29:18] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -83.650968-0.001395j
[2025-09-10 18:29:54] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -83.786478-0.000445j
[2025-09-10 18:30:29] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -83.467495+0.000554j
[2025-09-10 18:31:04] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -83.510734-0.000002j
[2025-09-10 18:31:39] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -83.508328+0.003086j
[2025-09-10 18:32:14] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -83.689710-0.000796j
[2025-09-10 18:32:49] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -83.647459-0.001374j
[2025-09-10 18:33:24] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -83.742278+0.006000j
[2025-09-10 18:33:59] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -83.768791-0.001899j
[2025-09-10 18:34:34] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -83.732606-0.007918j
[2025-09-10 18:35:10] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -83.760086-0.002154j
[2025-09-10 18:35:45] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -83.616788+0.001690j
[2025-09-10 18:36:20] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -83.584392+0.004011j
[2025-09-10 18:36:55] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -83.621638-0.006255j
[2025-09-10 18:37:30] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -83.661499-0.000912j
[2025-09-10 18:38:05] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -83.675285+0.001387j
[2025-09-10 18:38:40] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -83.563974+0.000099j
[2025-09-10 18:39:15] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -83.611417-0.005419j
[2025-09-10 18:39:50] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -83.619109-0.003448j
[2025-09-10 18:40:25] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -83.585285-0.002445j
[2025-09-10 18:41:01] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -83.513018+0.001500j
[2025-09-10 18:41:36] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -83.538043-0.002256j
[2025-09-10 18:42:11] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -83.473930+0.001699j
[2025-09-10 18:42:46] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -83.752407+0.002399j
[2025-09-10 18:43:21] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -83.557165+0.000194j
[2025-09-10 18:43:56] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -83.511087+0.002219j
[2025-09-10 18:44:31] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -83.579680+0.003898j
[2025-09-10 18:45:06] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -83.619945-0.000922j
[2025-09-10 18:45:41] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -83.581378+0.003144j
[2025-09-10 18:46:16] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -83.551185-0.001438j
[2025-09-10 18:46:51] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -83.566411-0.000661j
[2025-09-10 18:47:26] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -83.604225-0.000699j
[2025-09-10 18:48:01] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -83.558180-0.004613j
[2025-09-10 18:48:36] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -83.638889+0.005225j
[2025-09-10 18:49:11] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -83.724191+0.003554j
[2025-09-10 18:49:46] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -83.821016-0.002718j
[2025-09-10 18:50:22] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -83.833745+0.000335j
[2025-09-10 18:50:57] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -83.778145-0.001308j
[2025-09-10 18:51:32] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -83.771465+0.002754j
[2025-09-10 18:52:07] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -83.784219+0.001234j
[2025-09-10 18:52:07] RESTART #1 | Period: 300
[2025-09-10 18:52:42] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -83.697305-0.004359j
[2025-09-10 18:53:17] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -83.754134+0.002861j
[2025-09-10 18:53:52] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -83.665762-0.001693j
[2025-09-10 18:54:27] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -83.741574+0.000533j
[2025-09-10 18:55:02] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -83.803109+0.002282j
[2025-09-10 18:55:37] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -83.856566-0.002981j
[2025-09-10 18:56:13] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -83.732433+0.005774j
[2025-09-10 18:56:48] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -83.658845-0.001618j
[2025-09-10 18:57:23] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -83.695446+0.000246j
[2025-09-10 18:57:58] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -83.728631+0.000066j
[2025-09-10 18:58:33] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -83.499519+0.003825j
[2025-09-10 18:59:08] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -83.652716+0.000121j
[2025-09-10 18:59:44] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -83.789060+0.003057j
[2025-09-10 19:00:19] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -83.654878+0.000581j
[2025-09-10 19:00:54] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -83.737683+0.003328j
[2025-09-10 19:01:29] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -83.654425-0.003850j
[2025-09-10 19:02:04] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -83.588535-0.000405j
[2025-09-10 19:02:39] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -83.769717+0.000401j
[2025-09-10 19:03:14] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -83.704692+0.002814j
[2025-09-10 19:03:49] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -83.740150+0.003714j
[2025-09-10 19:04:24] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -83.662090-0.000570j
[2025-09-10 19:04:59] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -83.773469+0.000121j
[2025-09-10 19:05:34] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -83.718338-0.004244j
[2025-09-10 19:06:10] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -83.572759+0.000725j
[2025-09-10 19:06:45] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -83.784062-0.000229j
[2025-09-10 19:07:20] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -83.767398-0.001565j
[2025-09-10 19:07:55] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -83.552731+0.008870j
[2025-09-10 19:08:30] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -83.660812-0.003140j
[2025-09-10 19:09:05] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -83.764975+0.000507j
[2025-09-10 19:09:41] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -83.759326-0.000690j
[2025-09-10 19:10:16] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -83.773084+0.001201j
[2025-09-10 19:10:51] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -83.723139-0.002345j
[2025-09-10 19:11:26] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -83.717302-0.001361j
[2025-09-10 19:12:01] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -83.710664+0.002745j
[2025-09-10 19:12:36] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -83.692011+0.002818j
[2025-09-10 19:13:11] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -83.741055-0.002211j
[2025-09-10 19:13:46] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -83.810657+0.002330j
[2025-09-10 19:14:21] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -83.888480-0.001252j
[2025-09-10 19:14:56] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -83.766859-0.003340j
[2025-09-10 19:15:32] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -83.632255+0.002124j
[2025-09-10 19:16:07] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -83.614488+0.000949j
[2025-09-10 19:16:42] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -83.668785+0.002685j
[2025-09-10 19:17:17] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -83.668850-0.000577j
[2025-09-10 19:17:52] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -83.522481+0.003563j
[2025-09-10 19:18:27] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -83.538331+0.000351j
[2025-09-10 19:19:02] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -83.657345-0.000741j
[2025-09-10 19:19:37] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -83.691267+0.000084j
[2025-09-10 19:20:12] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -83.771330+0.002567j
[2025-09-10 19:20:48] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -83.829136+0.001959j
[2025-09-10 19:21:23] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -83.868573-0.001028j
[2025-09-10 19:21:58] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -83.923682-0.001317j
[2025-09-10 19:22:33] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -83.869942-0.001546j
[2025-09-10 19:23:08] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -83.817913+0.002560j
[2025-09-10 19:23:43] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -83.823017-0.001383j
[2025-09-10 19:24:18] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -83.792085-0.003812j
[2025-09-10 19:24:53] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -83.870276-0.005019j
[2025-09-10 19:25:28] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -83.815417+0.000023j
[2025-09-10 19:26:03] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -83.788657+0.003656j
[2025-09-10 19:26:39] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -83.863993+0.000448j
[2025-09-10 19:27:14] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -83.875425+0.001232j
[2025-09-10 19:27:14] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-10 19:27:49] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -83.822693-0.001332j
[2025-09-10 19:28:24] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -83.731246+0.002807j
[2025-09-10 19:28:59] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -83.667151-0.003434j
[2025-09-10 19:29:34] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -83.636634+0.000621j
[2025-09-10 19:30:09] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -83.689931+0.000009j
[2025-09-10 19:30:44] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -83.642184-0.001393j
[2025-09-10 19:31:19] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -83.648639-0.001496j
[2025-09-10 19:31:54] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -83.673503-0.001896j
[2025-09-10 19:32:30] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -83.709620+0.000267j
[2025-09-10 19:33:05] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -83.761774-0.001281j
[2025-09-10 19:33:40] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -83.819389+0.000988j
[2025-09-10 19:34:15] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -83.830027-0.002192j
[2025-09-10 19:34:50] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -83.927999-0.003641j
[2025-09-10 19:35:25] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -83.873940-0.000368j
[2025-09-10 19:36:00] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -83.852365-0.000933j
[2025-09-10 19:36:35] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -83.771245+0.004449j
[2025-09-10 19:37:11] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -83.818908-0.000326j
[2025-09-10 19:37:46] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -83.817641+0.001950j
[2025-09-10 19:38:21] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -83.859202+0.003518j
[2025-09-10 19:38:56] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -83.695947+0.003385j
[2025-09-10 19:39:31] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -83.735891+0.002093j
[2025-09-10 19:40:06] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -83.727950-0.003566j
[2025-09-10 19:40:42] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -83.779668-0.002125j
[2025-09-10 19:41:17] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -83.729267+0.001171j
[2025-09-10 19:41:52] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -83.753563+0.001758j
[2025-09-10 19:42:27] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -83.710066-0.000649j
[2025-09-10 19:43:02] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -83.611859+0.001012j
[2025-09-10 19:43:37] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -83.620015+0.003131j
[2025-09-10 19:44:12] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -83.574558+0.003194j
[2025-09-10 19:44:47] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -83.567343-0.002770j
[2025-09-10 19:45:22] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -83.456904-0.001770j
[2025-09-10 19:45:57] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -83.629144-0.000767j
[2025-09-10 19:46:33] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -83.462514+0.000254j
[2025-09-10 19:47:08] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -83.427363-0.001976j
[2025-09-10 19:47:43] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -83.464239-0.002826j
[2025-09-10 19:48:18] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -83.589330+0.000360j
[2025-09-10 19:48:53] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -83.715437-0.000635j
[2025-09-10 19:49:28] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -83.533569-0.000604j
[2025-09-10 19:50:04] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -83.648158-0.000720j
[2025-09-10 19:50:39] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -83.637347-0.000243j
[2025-09-10 19:51:14] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -83.651187-0.002231j
[2025-09-10 19:51:49] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -83.731291+0.003959j
[2025-09-10 19:52:24] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -83.589660+0.003260j
[2025-09-10 19:52:59] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -83.616105+0.000117j
[2025-09-10 19:53:34] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -83.507142-0.002229j
[2025-09-10 19:54:09] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -83.563524-0.001030j
[2025-09-10 19:54:44] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -83.500213-0.002893j
[2025-09-10 19:55:19] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -83.586742+0.000104j
[2025-09-10 19:55:54] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -83.628981-0.000471j
[2025-09-10 19:56:30] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -83.661126+0.000888j
[2025-09-10 19:57:05] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -83.463248+0.000469j
[2025-09-10 19:57:40] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -83.533501-0.001665j
[2025-09-10 19:58:15] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -83.529167-0.003068j
[2025-09-10 19:58:50] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -83.494942+0.001178j
[2025-09-10 19:59:25] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -83.548437+0.001733j
[2025-09-10 20:00:00] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -83.418865-0.002737j
[2025-09-10 20:00:35] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -83.659052+0.001160j
[2025-09-10 20:01:10] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -83.454699+0.004089j
[2025-09-10 20:01:45] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -83.552648-0.001616j
[2025-09-10 20:02:20] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -83.612932-0.002302j
[2025-09-10 20:02:55] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -83.532898+0.000104j
[2025-09-10 20:03:30] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -83.623016-0.001952j
[2025-09-10 20:04:05] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -83.597716+0.002481j
[2025-09-10 20:04:40] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -83.668928-0.002602j
[2025-09-10 20:05:15] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -83.550407+0.005704j
[2025-09-10 20:05:51] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -83.605003-0.000585j
[2025-09-10 20:06:26] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -83.688303-0.001449j
[2025-09-10 20:07:01] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -83.623154+0.000244j
[2025-09-10 20:07:36] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -83.785806-0.002486j
[2025-09-10 20:08:11] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -83.691247+0.001788j
[2025-09-10 20:08:46] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -83.594637+0.001323j
[2025-09-10 20:09:21] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -83.510308-0.000970j
[2025-09-10 20:09:57] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -83.409370+0.002951j
[2025-09-10 20:10:32] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -83.605569+0.005902j
[2025-09-10 20:11:07] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -83.576866-0.002475j
[2025-09-10 20:11:42] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -83.620792-0.002509j
[2025-09-10 20:12:17] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -83.621108-0.000748j
[2025-09-10 20:12:52] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -83.555470-0.003323j
[2025-09-10 20:13:27] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -83.457773+0.002475j
[2025-09-10 20:14:02] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -83.425751+0.000903j
[2025-09-10 20:14:37] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -83.575266-0.000472j
[2025-09-10 20:15:13] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -83.708412+0.001212j
[2025-09-10 20:15:48] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -83.771027+0.000038j
[2025-09-10 20:16:23] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -83.818433+0.001108j
[2025-09-10 20:16:58] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -83.743666-0.003419j
[2025-09-10 20:17:33] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -83.653553-0.003358j
[2025-09-10 20:18:08] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -83.516394+0.002315j
[2025-09-10 20:18:43] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -83.519924-0.000764j
[2025-09-10 20:19:18] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -83.439611+0.002051j
[2025-09-10 20:19:53] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -83.501802+0.003430j
[2025-09-10 20:20:29] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -83.606278-0.000510j
[2025-09-10 20:21:04] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -83.716397+0.000536j
[2025-09-10 20:21:39] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -83.665639+0.001317j
[2025-09-10 20:22:14] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -83.680123+0.000425j
[2025-09-10 20:22:49] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -83.782940-0.000966j
[2025-09-10 20:23:24] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -83.801691+0.002553j
[2025-09-10 20:23:59] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -83.677766+0.003967j
[2025-09-10 20:24:34] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -83.637328-0.000823j
[2025-09-10 20:25:09] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -83.552939-0.002542j
[2025-09-10 20:25:44] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -83.608934-0.001217j
[2025-09-10 20:26:20] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -83.655236+0.000326j
[2025-09-10 20:26:55] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -83.713455+0.001915j
[2025-09-10 20:27:30] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -83.701279-0.002841j
[2025-09-10 20:28:05] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -83.709141-0.002051j
[2025-09-10 20:28:40] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -83.693880+0.000320j
[2025-09-10 20:28:40] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-10 20:29:15] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -83.510575+0.003796j
[2025-09-10 20:29:50] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -83.565802-0.002776j
[2025-09-10 20:30:25] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -83.582202+0.001004j
[2025-09-10 20:31:00] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -83.711491-0.000139j
[2025-09-10 20:31:35] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -83.532632+0.000411j
[2025-09-10 20:32:10] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -83.516907+0.002571j
[2025-09-10 20:32:45] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -83.653324+0.003535j
[2025-09-10 20:33:20] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -83.602491+0.000619j
[2025-09-10 20:33:55] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -83.717060-0.002755j
[2025-09-10 20:34:30] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -83.655812+0.004979j
[2025-09-10 20:35:05] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -83.717084+0.001740j
[2025-09-10 20:35:41] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -83.667682+0.000778j
[2025-09-10 20:36:16] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -83.645582+0.004406j
[2025-09-10 20:36:51] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -83.804880+0.000177j
[2025-09-10 20:37:26] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -83.721237-0.003101j
[2025-09-10 20:38:01] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -83.734483+0.001429j
[2025-09-10 20:38:36] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -83.710972+0.005526j
[2025-09-10 20:39:11] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -83.712855+0.000379j
[2025-09-10 20:39:47] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -83.571426+0.000496j
[2025-09-10 20:40:22] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -83.488479-0.001706j
[2025-09-10 20:40:57] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -83.535664+0.002559j
[2025-09-10 20:41:32] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -83.595325+0.001790j
[2025-09-10 20:42:07] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -83.696430+0.005268j
[2025-09-10 20:42:42] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -83.612331+0.003438j
[2025-09-10 20:43:17] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -83.696706+0.000188j
[2025-09-10 20:43:52] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -83.612900-0.002010j
[2025-09-10 20:44:27] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -83.576873-0.002309j
[2025-09-10 20:45:03] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -83.645776+0.000469j
[2025-09-10 20:45:38] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -83.693699-0.000232j
[2025-09-10 20:46:13] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -83.640804-0.006402j
[2025-09-10 20:46:48] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -83.492433-0.001054j
[2025-09-10 20:47:23] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -83.656477-0.001072j
[2025-09-10 20:47:58] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -83.626890-0.003528j
[2025-09-10 20:48:33] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -83.686012+0.003646j
[2025-09-10 20:49:09] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -83.521907+0.001160j
[2025-09-10 20:49:44] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -83.717831-0.001133j
[2025-09-10 20:50:19] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -83.611538-0.000191j
[2025-09-10 20:50:54] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -83.656635+0.000392j
[2025-09-10 20:51:29] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -83.561820-0.002957j
[2025-09-10 20:52:04] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -83.555405+0.002589j
[2025-09-10 20:52:39] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -83.546894-0.001353j
[2025-09-10 20:53:14] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -83.528188+0.000873j
[2025-09-10 20:53:49] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -83.653992+0.002492j
[2025-09-10 20:54:24] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -83.550758+0.003315j
[2025-09-10 20:54:59] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -83.479247+0.001135j
[2025-09-10 20:55:35] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -83.554668+0.004600j
[2025-09-10 20:56:10] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -83.633123-0.001294j
[2025-09-10 20:56:45] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -83.582089-0.001266j
[2025-09-10 20:57:20] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -83.527627+0.003463j
[2025-09-10 20:57:55] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -83.575342-0.002639j
[2025-09-10 20:58:30] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -83.589160+0.000221j
[2025-09-10 20:59:05] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -83.685374-0.000924j
[2025-09-10 20:59:40] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -83.552886+0.002598j
[2025-09-10 21:00:16] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -83.681954-0.000060j
[2025-09-10 21:00:51] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -83.635661+0.001122j
[2025-09-10 21:01:26] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -83.489212-0.002923j
[2025-09-10 21:02:01] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -83.418911+0.001284j
[2025-09-10 21:02:36] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -83.454612-0.000729j
[2025-09-10 21:03:11] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -83.521820-0.001196j
[2025-09-10 21:03:46] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -83.714340+0.002855j
[2025-09-10 21:04:21] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -83.659056-0.000664j
[2025-09-10 21:04:57] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -83.666150+0.000285j
[2025-09-10 21:05:32] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -83.735583+0.000378j
[2025-09-10 21:06:07] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -83.743257-0.000964j
[2025-09-10 21:06:42] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -83.654447-0.003370j
[2025-09-10 21:07:17] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -83.593817-0.000718j
[2025-09-10 21:07:52] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -83.619432-0.002475j
[2025-09-10 21:08:27] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -83.633612-0.000475j
[2025-09-10 21:09:02] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -83.710435-0.003872j
[2025-09-10 21:09:37] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -83.652460+0.001023j
[2025-09-10 21:10:13] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -83.545464+0.000565j
[2025-09-10 21:10:48] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -83.627554-0.001661j
[2025-09-10 21:11:23] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -83.554856-0.002776j
[2025-09-10 21:11:58] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -83.613900+0.005280j
[2025-09-10 21:12:33] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -83.637833+0.003216j
[2025-09-10 21:13:08] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -83.699979-0.006086j
[2025-09-10 21:13:43] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -83.537086+0.000690j
[2025-09-10 21:14:18] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -83.665684-0.003786j
[2025-09-10 21:14:53] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -83.593331+0.001663j
[2025-09-10 21:15:28] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -83.621209-0.000390j
[2025-09-10 21:16:03] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -83.571546+0.002367j
[2025-09-10 21:16:38] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -83.643840+0.004074j
[2025-09-10 21:17:13] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -83.610266+0.000594j
[2025-09-10 21:17:48] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -83.596249+0.002308j
[2025-09-10 21:18:23] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -83.549042-0.000624j
[2025-09-10 21:18:59] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -83.421696-0.002904j
[2025-09-10 21:19:33] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -83.515262-0.004218j
[2025-09-10 21:20:09] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -83.559576-0.001150j
[2025-09-10 21:20:44] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -83.630784+0.001863j
[2025-09-10 21:21:19] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -83.559774-0.004911j
[2025-09-10 21:21:54] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -83.661438+0.001525j
[2025-09-10 21:22:29] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -83.625374-0.001076j
[2025-09-10 21:23:04] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -83.605672+0.001632j
[2025-09-10 21:23:39] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -83.594956-0.000653j
[2025-09-10 21:24:14] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -83.391275-0.003023j
[2025-09-10 21:24:49] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -83.525086-0.001802j
[2025-09-10 21:25:25] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -83.481625+0.002643j
[2025-09-10 21:26:00] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -83.621870+0.001448j
[2025-09-10 21:26:35] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -83.671411-0.000005j
[2025-09-10 21:27:10] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -83.602977+0.006640j
[2025-09-10 21:27:45] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -83.550160-0.003622j
[2025-09-10 21:28:20] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -83.589612+0.002739j
[2025-09-10 21:28:56] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -83.480753-0.000943j
[2025-09-10 21:29:31] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -83.493214-0.002282j
[2025-09-10 21:30:06] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -83.624203+0.001264j
[2025-09-10 21:30:06] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-10 21:30:41] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -83.915958-0.000561j
[2025-09-10 21:31:16] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -83.803058-0.002733j
[2025-09-10 21:31:51] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -83.707719+0.001498j
[2025-09-10 21:32:26] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -83.886252-0.002150j
[2025-09-10 21:33:01] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -83.741475-0.001416j
[2025-09-10 21:33:36] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -83.630641+0.001222j
[2025-09-10 21:34:12] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -83.628900-0.000044j
[2025-09-10 21:34:47] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -83.786585+0.000577j
[2025-09-10 21:35:22] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -83.803741-0.004216j
[2025-09-10 21:35:57] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -83.731546+0.000294j
[2025-09-10 21:36:32] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -83.722168-0.003373j
[2025-09-10 21:37:07] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -83.665581-0.000778j
[2025-09-10 21:37:42] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -83.845045+0.000351j
[2025-09-10 21:38:17] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -83.751219+0.001815j
[2025-09-10 21:38:52] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -83.671594+0.001832j
[2025-09-10 21:39:27] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -83.591972-0.000686j
[2025-09-10 21:40:02] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -83.792344-0.004057j
[2025-09-10 21:40:37] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -83.792302-0.002706j
[2025-09-10 21:41:12] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -83.965083-0.003712j
[2025-09-10 21:41:47] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -83.987272+0.002287j
[2025-09-10 21:42:22] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -83.769990-0.003456j
[2025-09-10 21:42:57] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -83.825848+0.001833j
[2025-09-10 21:43:32] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -83.640637+0.000693j
[2025-09-10 21:44:07] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -83.514224+0.001979j
[2025-09-10 21:44:42] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -83.721055+0.000623j
[2025-09-10 21:45:18] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -83.451435-0.002450j
[2025-09-10 21:45:53] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -83.715213-0.002648j
[2025-09-10 21:46:28] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -83.613529-0.006817j
[2025-09-10 21:47:03] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -83.565145+0.003415j
[2025-09-10 21:47:38] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -83.789183+0.001207j
[2025-09-10 21:47:38] RESTART #2 | Period: 600
[2025-09-10 21:48:13] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -83.712390-0.004014j
[2025-09-10 21:48:48] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -83.616354+0.001599j
[2025-09-10 21:49:23] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -83.586893-0.001196j
[2025-09-10 21:49:58] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -83.668733+0.000355j
[2025-09-10 21:50:33] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -83.782837+0.003689j
[2025-09-10 21:51:09] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -83.713205+0.002347j
[2025-09-10 21:51:44] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -83.586101+0.006197j
[2025-09-10 21:52:19] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -83.497214+0.004071j
[2025-09-10 21:52:54] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -83.630903-0.000561j
[2025-09-10 21:53:29] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -83.636372-0.001429j
[2025-09-10 21:54:04] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -83.752471+0.001650j
[2025-09-10 21:54:39] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -83.721946-0.000221j
[2025-09-10 21:55:15] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -83.706761+0.003135j
[2025-09-10 21:55:50] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -83.636332+0.002692j
[2025-09-10 21:56:25] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -83.892170-0.001506j
[2025-09-10 21:57:00] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -83.653232+0.000678j
[2025-09-10 21:57:35] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -83.681171-0.001924j
[2025-09-10 21:58:10] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -83.764454+0.000690j
[2025-09-10 21:58:45] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -83.867270-0.000115j
[2025-09-10 21:59:20] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -83.742025-0.003879j
[2025-09-10 21:59:55] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -83.653696+0.000117j
[2025-09-10 22:00:31] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -83.838712+0.003904j
[2025-09-10 22:01:06] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -83.802828+0.003160j
[2025-09-10 22:01:41] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -83.689037+0.003411j
[2025-09-10 22:02:16] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -83.701955+0.000812j
[2025-09-10 22:02:51] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -83.805122-0.002327j
[2025-09-10 22:03:26] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -83.831583-0.002920j
[2025-09-10 22:04:01] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -83.781861+0.003090j
[2025-09-10 22:04:36] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -83.760812-0.001528j
[2025-09-10 22:05:11] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -83.592190-0.002011j
[2025-09-10 22:05:46] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -83.614891+0.002025j
[2025-09-10 22:06:22] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -83.636482-0.001314j
[2025-09-10 22:06:57] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -83.731302-0.002901j
[2025-09-10 22:07:32] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -83.793308-0.000468j
[2025-09-10 22:08:07] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -83.568039-0.001093j
[2025-09-10 22:08:42] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -83.692733+0.001182j
[2025-09-10 22:09:17] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -83.727616+0.000669j
[2025-09-10 22:09:52] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -83.657437-0.003210j
[2025-09-10 22:10:28] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -83.639521-0.000254j
[2025-09-10 22:11:03] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -83.628265+0.002308j
[2025-09-10 22:11:38] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -83.596828+0.005558j
[2025-09-10 22:12:13] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -83.752484-0.000527j
[2025-09-10 22:12:48] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -83.845664-0.004427j
[2025-09-10 22:13:23] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -83.727248-0.003691j
[2025-09-10 22:13:58] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -83.885344+0.004210j
[2025-09-10 22:14:33] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -83.766587+0.004204j
[2025-09-10 22:15:09] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -83.686795-0.001411j
[2025-09-10 22:15:44] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -83.739491+0.001645j
[2025-09-10 22:16:19] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -83.730202-0.001197j
[2025-09-10 22:16:54] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -83.676187+0.002148j
[2025-09-10 22:17:29] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -83.856088+0.000961j
[2025-09-10 22:18:04] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -83.849755-0.003876j
[2025-09-10 22:18:39] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -83.843005-0.003730j
[2025-09-10 22:19:14] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -83.792946+0.003273j
[2025-09-10 22:19:49] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -83.626511+0.002045j
[2025-09-10 22:20:24] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -83.555991+0.006034j
[2025-09-10 22:20:59] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -83.546807+0.002575j
[2025-09-10 22:21:34] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -83.620291+0.005459j
[2025-09-10 22:22:09] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -83.571047+0.000466j
[2025-09-10 22:22:44] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -83.525946-0.000707j
[2025-09-10 22:23:19] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -83.585443+0.000998j
[2025-09-10 22:23:54] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -83.458548+0.002218j
[2025-09-10 22:24:29] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -83.563106-0.000567j
[2025-09-10 22:25:05] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -83.375689-0.000054j
[2025-09-10 22:25:40] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -83.453595-0.002829j
[2025-09-10 22:26:15] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -83.466979+0.001234j
[2025-09-10 22:26:50] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -83.442920+0.002779j
[2025-09-10 22:27:25] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -83.569548-0.002096j
[2025-09-10 22:28:00] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -83.687998+0.002818j
[2025-09-10 22:28:35] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -83.722788-0.001216j
[2025-09-10 22:29:10] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -83.798896-0.003199j
[2025-09-10 22:29:46] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -83.804486-0.002484j
[2025-09-10 22:30:21] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -83.711895+0.002960j
[2025-09-10 22:30:56] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -83.568925-0.000305j
[2025-09-10 22:31:31] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -83.447248+0.002116j
[2025-09-10 22:31:31] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-10 22:32:06] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -83.540801-0.002738j
[2025-09-10 22:32:41] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -83.617222+0.000576j
[2025-09-10 22:33:17] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -83.598381-0.000965j
[2025-09-10 22:33:52] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -83.719782+0.000687j
[2025-09-10 22:34:27] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -83.749186-0.003949j
[2025-09-10 22:35:02] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -83.565862-0.000594j
[2025-09-10 22:35:37] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -83.687815-0.001860j
[2025-09-10 22:36:12] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -83.697180+0.000061j
[2025-09-10 22:36:47] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -83.671362+0.001323j
[2025-09-10 22:37:22] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -83.669846-0.003864j
[2025-09-10 22:37:57] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -83.622374-0.003044j
[2025-09-10 22:38:32] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -83.609190+0.000470j
[2025-09-10 22:39:07] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -83.659854+0.001841j
[2025-09-10 22:39:43] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -83.686497-0.002151j
[2025-09-10 22:40:18] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -83.680380-0.003012j
[2025-09-10 22:40:53] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -83.421865+0.000044j
[2025-09-10 22:41:28] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -83.652117-0.004214j
[2025-09-10 22:42:03] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -83.690682-0.003008j
[2025-09-10 22:42:38] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -83.731054-0.002875j
[2025-09-10 22:43:13] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -83.629918+0.004866j
[2025-09-10 22:43:48] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -83.624365-0.004593j
[2025-09-10 22:44:23] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -83.741091-0.003650j
[2025-09-10 22:44:58] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -83.771889-0.001146j
[2025-09-10 22:45:34] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -83.814284-0.004159j
[2025-09-10 22:46:09] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -83.762941-0.002042j
[2025-09-10 22:46:44] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -83.654733+0.002610j
[2025-09-10 22:47:19] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -83.881700+0.001819j
[2025-09-10 22:47:54] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -83.701338-0.001137j
[2025-09-10 22:48:29] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -83.659436+0.001276j
[2025-09-10 22:49:04] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -83.671453-0.002113j
[2025-09-10 22:49:39] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -83.740398-0.003751j
[2025-09-10 22:50:14] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -83.756844-0.001029j
[2025-09-10 22:50:49] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -83.838408+0.001442j
[2025-09-10 22:51:24] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -83.705566+0.004248j
[2025-09-10 22:51:59] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -83.723588+0.002989j
[2025-09-10 22:52:35] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -83.786998-0.002053j
[2025-09-10 22:53:10] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -83.730694-0.001180j
[2025-09-10 22:53:45] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -83.785073+0.003785j
[2025-09-10 22:54:20] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -83.783408-0.002864j
[2025-09-10 22:54:55] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -83.726984-0.002149j
[2025-09-10 22:55:30] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -83.710738-0.001506j
[2025-09-10 22:56:05] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -83.748737-0.001013j
[2025-09-10 22:56:40] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -83.727420+0.000493j
[2025-09-10 22:57:15] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -83.694955+0.001509j
[2025-09-10 22:57:51] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -83.681226-0.000685j
[2025-09-10 22:58:26] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -83.742053-0.003250j
[2025-09-10 22:59:01] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -83.652680-0.000973j
[2025-09-10 22:59:36] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -83.708177-0.001904j
[2025-09-10 23:00:11] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -83.550140+0.001866j
[2025-09-10 23:00:46] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -83.597761+0.002789j
[2025-09-10 23:01:21] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -83.644600+0.003425j
[2025-09-10 23:01:57] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -83.645410-0.003367j
[2025-09-10 23:02:32] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -83.496802-0.001868j
[2025-09-10 23:03:07] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -83.578823-0.006046j
[2025-09-10 23:03:42] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -83.492896+0.004705j
[2025-09-10 23:04:17] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -83.533944+0.002588j
[2025-09-10 23:04:52] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -83.638560+0.000727j
[2025-09-10 23:05:27] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -83.534806-0.001398j
[2025-09-10 23:06:02] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -83.456549+0.002911j
[2025-09-10 23:06:37] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -83.455047+0.000771j
[2025-09-10 23:07:13] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -83.325920-0.001669j
[2025-09-10 23:07:48] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -83.519469+0.001620j
[2025-09-10 23:08:23] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -83.406458-0.003404j
[2025-09-10 23:08:58] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -83.493496-0.000717j
[2025-09-10 23:09:33] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -83.623498-0.007340j
[2025-09-10 23:10:08] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -83.653652+0.001190j
[2025-09-10 23:10:43] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -83.585456-0.003266j
[2025-09-10 23:11:18] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -83.680013+0.001696j
[2025-09-10 23:11:53] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -83.660611-0.001075j
[2025-09-10 23:12:28] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -83.503040-0.002223j
[2025-09-10 23:13:04] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -83.559011-0.001219j
[2025-09-10 23:13:39] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -83.693354-0.001019j
[2025-09-10 23:14:14] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -83.640920-0.002951j
[2025-09-10 23:14:49] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -83.515859-0.004921j
[2025-09-10 23:15:24] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -83.579497-0.002757j
[2025-09-10 23:15:59] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -83.679250+0.000082j
[2025-09-10 23:16:34] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -83.608042+0.000573j
[2025-09-10 23:17:09] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -83.696961-0.005117j
[2025-09-10 23:17:44] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -83.594444-0.001316j
[2025-09-10 23:18:19] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -83.647739+0.000879j
[2025-09-10 23:18:55] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -83.642960+0.001179j
[2025-09-10 23:19:30] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -83.696516+0.000727j
[2025-09-10 23:20:05] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -83.838320-0.001339j
[2025-09-10 23:20:40] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -83.848052-0.003648j
[2025-09-10 23:21:15] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -83.822766-0.001202j
[2025-09-10 23:21:50] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -83.786620-0.000718j
[2025-09-10 23:22:25] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -83.755323-0.003728j
[2025-09-10 23:23:00] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -83.794842+0.000817j
[2025-09-10 23:23:35] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -83.670535-0.003434j
[2025-09-10 23:24:10] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -83.729065+0.000929j
[2025-09-10 23:24:45] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -83.670836+0.001540j
[2025-09-10 23:25:21] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -83.557483+0.000505j
[2025-09-10 23:25:56] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -83.620494-0.002151j
[2025-09-10 23:26:31] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -83.557590+0.006155j
[2025-09-10 23:27:06] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -83.625030+0.000687j
[2025-09-10 23:27:41] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -83.601130+0.001580j
[2025-09-10 23:28:16] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -83.637356+0.004296j
[2025-09-10 23:28:51] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -83.553609+0.000473j
[2025-09-10 23:29:27] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -83.541295-0.002180j
[2025-09-10 23:30:02] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -83.621716-0.001741j
[2025-09-10 23:30:37] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -83.452787-0.000249j
[2025-09-10 23:31:12] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -83.476594-0.000518j
[2025-09-10 23:31:47] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -83.553650-0.009296j
[2025-09-10 23:32:22] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -83.588990+0.001088j
[2025-09-10 23:32:57] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -83.705434+0.000261j
[2025-09-10 23:32:57] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-10 23:33:32] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -83.673405-0.000047j
[2025-09-10 23:34:08] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -83.652871-0.001199j
[2025-09-10 23:34:43] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -83.498682-0.004164j
[2025-09-10 23:35:18] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -83.439088+0.000941j
[2025-09-10 23:35:53] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -83.502234-0.006163j
[2025-09-10 23:36:28] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -83.581376-0.001064j
[2025-09-10 23:37:03] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -83.607420+0.000326j
[2025-09-10 23:37:39] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -83.672428-0.003174j
[2025-09-10 23:38:14] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -83.524084-0.000044j
[2025-09-10 23:38:49] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -83.511294-0.003849j
[2025-09-10 23:39:24] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -83.547608-0.003102j
[2025-09-10 23:39:59] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -83.594475+0.002412j
[2025-09-10 23:40:34] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -83.789743+0.001372j
[2025-09-10 23:41:09] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -83.743543+0.000202j
[2025-09-10 23:41:44] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -83.680290-0.001200j
[2025-09-10 23:42:19] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -83.603254-0.000306j
[2025-09-10 23:42:54] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -83.616482+0.002020j
[2025-09-10 23:43:30] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -83.761637+0.005388j
[2025-09-10 23:44:05] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -83.827335-0.001969j
[2025-09-10 23:44:40] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -83.820934+0.002022j
[2025-09-10 23:45:15] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -83.827328-0.001907j
[2025-09-10 23:45:50] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -83.821162-0.001208j
[2025-09-10 23:46:21] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -83.758145+0.002240j
[2025-09-10 23:46:44] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -83.933041+0.001691j
[2025-09-10 23:47:08] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -83.836593+0.003864j
[2025-09-10 23:47:31] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -83.668880+0.001759j
[2025-09-10 23:47:54] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -83.688884-0.001714j
[2025-09-10 23:48:18] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -83.716867-0.000311j
[2025-09-10 23:48:41] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -83.627370+0.001588j
[2025-09-10 23:49:05] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -83.660998-0.003852j
[2025-09-10 23:49:28] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -83.503939-0.001954j
[2025-09-10 23:49:52] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -83.632564+0.001557j
[2025-09-10 23:50:15] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -83.558699+0.002078j
[2025-09-10 23:50:38] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -83.675230-0.002341j
[2025-09-10 23:51:02] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -83.614581-0.001529j
[2025-09-10 23:51:25] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -83.613909-0.002804j
[2025-09-10 23:51:49] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -83.670062-0.001374j
[2025-09-10 23:52:12] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -83.662249-0.004018j
[2025-09-10 23:52:36] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -83.773073-0.001515j
[2025-09-10 23:52:59] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -83.737015+0.003477j
[2025-09-10 23:53:23] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -83.452090+0.003063j
[2025-09-10 23:53:46] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -83.578657-0.001062j
[2025-09-10 23:54:09] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -83.492481-0.002995j
[2025-09-10 23:54:33] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -83.496395+0.002803j
[2025-09-10 23:54:56] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -83.683564+0.004825j
[2025-09-10 23:55:20] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -83.688024+0.004597j
[2025-09-10 23:55:43] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -83.663107-0.003682j
[2025-09-10 23:56:06] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -83.862689-0.002068j
[2025-09-10 23:56:30] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -83.719238+0.002658j
[2025-09-10 23:56:53] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -83.726493-0.000080j
[2025-09-10 23:57:17] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -83.554150+0.001043j
[2025-09-10 23:57:40] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -83.415173+0.000316j
[2025-09-10 23:58:04] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -83.509929-0.003147j
[2025-09-10 23:58:27] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -83.562943+0.000463j
[2025-09-10 23:58:50] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -83.558447-0.001934j
[2025-09-10 23:59:14] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -83.711566-0.000540j
[2025-09-10 23:59:37] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -83.499341-0.001882j
[2025-09-11 00:00:01] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -83.654379-0.004828j
[2025-09-11 00:00:24] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -83.625748-0.000580j
[2025-09-11 00:00:48] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -83.668270+0.001516j
[2025-09-11 00:01:11] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -83.622183-0.001510j
[2025-09-11 00:01:35] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -83.637371+0.001439j
[2025-09-11 00:01:58] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -83.673904+0.001692j
[2025-09-11 00:02:21] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -83.726225+0.003131j
[2025-09-11 00:02:45] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -83.627629+0.000534j
[2025-09-11 00:03:08] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -83.681267+0.002642j
[2025-09-11 00:03:32] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -83.686847-0.001005j
[2025-09-11 00:03:55] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -83.624543+0.001385j
[2025-09-11 00:04:18] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -83.690135+0.003045j
[2025-09-11 00:04:42] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -83.559698-0.003707j
[2025-09-11 00:05:05] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -83.689659+0.003337j
[2025-09-11 00:05:29] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -83.720273-0.000222j
[2025-09-11 00:05:52] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -83.728488+0.030422j
[2025-09-11 00:06:16] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -83.668142+0.000275j
[2025-09-11 00:06:39] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -83.755477-0.002774j
[2025-09-11 00:07:02] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -83.478902-0.000974j
[2025-09-11 00:07:26] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -83.676331-0.001897j
[2025-09-11 00:07:49] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -83.524406-0.000797j
[2025-09-11 00:08:13] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -83.649787-0.000766j
[2025-09-11 00:08:36] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -83.750164+0.000885j
[2025-09-11 00:09:00] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -83.777091-0.002639j
[2025-09-11 00:09:23] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -83.655209+0.000102j
[2025-09-11 00:09:47] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -83.705755+0.001237j
[2025-09-11 00:10:10] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -83.710105+0.001472j
[2025-09-11 00:10:33] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -83.642938-0.004508j
[2025-09-11 00:10:57] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -83.695190-0.001280j
[2025-09-11 00:11:20] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -83.858843-0.000975j
[2025-09-11 00:11:44] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -83.843249+0.004072j
[2025-09-11 00:12:07] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -83.949274+0.000104j
[2025-09-11 00:12:31] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -83.802789+0.000609j
[2025-09-11 00:12:54] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -83.701501-0.003655j
[2025-09-11 00:13:17] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -83.780870+0.002445j
[2025-09-11 00:13:41] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -83.760026+0.002777j
[2025-09-11 00:14:04] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -83.737210-0.003081j
[2025-09-11 00:14:28] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -83.745990-0.001254j
[2025-09-11 00:14:51] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -83.777195+0.001234j
[2025-09-11 00:15:15] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -83.763430+0.003573j
[2025-09-11 00:15:38] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -83.791274-0.003023j
[2025-09-11 00:16:02] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -83.730189-0.000085j
[2025-09-11 00:16:25] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -83.730254+0.005481j
[2025-09-11 00:16:48] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -83.518650-0.001404j
[2025-09-11 00:17:12] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -83.660743+0.001775j
[2025-09-11 00:17:35] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -83.629405+0.001392j
[2025-09-11 00:17:59] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -83.847840-0.003470j
[2025-09-11 00:18:22] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -83.784145+0.000848j
[2025-09-11 00:18:22] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-11 00:18:45] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -83.698857-0.000599j
[2025-09-11 00:19:09] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -83.610874+0.003441j
[2025-09-11 00:19:32] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -83.643313-0.003311j
[2025-09-11 00:19:56] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -83.524170+0.001916j
[2025-09-11 00:20:19] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -83.657651+0.001425j
[2025-09-11 00:20:43] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -83.535248+0.002171j
[2025-09-11 00:21:06] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -83.651090-0.003527j
[2025-09-11 00:21:29] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -83.551953+0.000534j
[2025-09-11 00:21:53] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -83.499385+0.002087j
[2025-09-11 00:22:16] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -83.386449+0.000747j
[2025-09-11 00:22:40] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -83.507032-0.000075j
[2025-09-11 00:23:03] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -83.650121-0.000411j
[2025-09-11 00:23:27] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -83.655052-0.005888j
[2025-09-11 00:23:50] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -83.713407+0.005274j
[2025-09-11 00:24:14] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -83.706823-0.002498j
[2025-09-11 00:24:37] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -83.601370-0.005930j
[2025-09-11 00:25:00] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -83.625848-0.001500j
[2025-09-11 00:25:24] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -83.660701+0.006425j
[2025-09-11 00:25:47] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -83.574128-0.000234j
[2025-09-11 00:26:11] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -83.646289+0.006473j
[2025-09-11 00:26:34] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -83.652408-0.001155j
[2025-09-11 00:26:58] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -83.602859-0.002003j
[2025-09-11 00:27:21] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -83.831296+0.001221j
[2025-09-11 00:27:44] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -83.659858-0.003831j
[2025-09-11 00:28:08] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -83.812404+0.003732j
[2025-09-11 00:28:31] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -83.773861+0.003991j
[2025-09-11 00:28:55] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -83.834531-0.000208j
[2025-09-11 00:29:18] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -83.777284+0.002727j
[2025-09-11 00:29:42] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -83.725585+0.003315j
[2025-09-11 00:30:05] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -83.830099+0.001475j
[2025-09-11 00:30:28] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -83.690397+0.003922j
[2025-09-11 00:30:52] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -83.619078+0.003165j
[2025-09-11 00:31:15] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -83.708126-0.000093j
[2025-09-11 00:31:39] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -83.674364+0.001593j
[2025-09-11 00:32:02] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -83.774341+0.000778j
[2025-09-11 00:32:26] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -83.511504+0.001728j
[2025-09-11 00:32:49] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -83.647617+0.000014j
[2025-09-11 00:33:12] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -83.541667+0.002356j
[2025-09-11 00:33:36] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -83.798694+0.003222j
[2025-09-11 00:33:59] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -83.613512-0.003049j
[2025-09-11 00:34:23] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -83.755751+0.001110j
[2025-09-11 00:34:46] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -83.788761-0.005401j
[2025-09-11 00:35:10] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -83.672007+0.001091j
[2025-09-11 00:35:33] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -83.572704+0.000864j
[2025-09-11 00:35:56] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -83.611085+0.004817j
[2025-09-11 00:36:20] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -83.642975-0.004876j
[2025-09-11 00:36:43] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -83.667806+0.003986j
[2025-09-11 00:37:07] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -83.687889+0.000317j
[2025-09-11 00:37:30] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -83.629718+0.003223j
[2025-09-11 00:37:54] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -83.506472+0.004201j
[2025-09-11 00:38:17] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -83.471460-0.006091j
[2025-09-11 00:38:40] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -83.375410+0.001591j
[2025-09-11 00:39:04] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -83.528112+0.002913j
[2025-09-11 00:39:27] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -83.623452-0.005172j
[2025-09-11 00:39:51] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -83.508115+0.000511j
[2025-09-11 00:40:14] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -83.618448-0.001805j
[2025-09-11 00:40:38] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -83.705500+0.001900j
[2025-09-11 00:41:01] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -83.547217+0.001348j
[2025-09-11 00:41:24] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -83.548021+0.002144j
[2025-09-11 00:41:48] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -83.737894-0.000354j
[2025-09-11 00:42:11] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -83.751722-0.001897j
[2025-09-11 00:42:35] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -83.819804-0.000831j
[2025-09-11 00:42:58] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -83.594568-0.005140j
[2025-09-11 00:43:22] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -83.580196+0.000431j
[2025-09-11 00:43:45] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -83.521204+0.000247j
[2025-09-11 00:44:08] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -83.693917+0.000626j
[2025-09-11 00:44:32] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -83.688028+0.003111j
[2025-09-11 00:44:55] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -83.378023+0.002346j
[2025-09-11 00:45:19] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -83.558807+0.003506j
[2025-09-11 00:45:42] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -83.637737-0.000972j
[2025-09-11 00:46:06] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -83.659270-0.000915j
[2025-09-11 00:46:29] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -83.599386+0.000428j
[2025-09-11 00:46:52] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -83.588420-0.001385j
[2025-09-11 00:47:16] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -83.560121+0.004715j
[2025-09-11 00:47:39] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -83.605953+0.001292j
[2025-09-11 00:48:03] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -83.432570+0.000580j
[2025-09-11 00:48:26] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -83.688038-0.002584j
[2025-09-11 00:48:49] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -83.762317+0.001195j
[2025-09-11 00:49:13] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -83.806177+0.002486j
[2025-09-11 00:49:36] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -83.650158+0.002877j
[2025-09-11 00:50:00] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -83.616797-0.000821j
[2025-09-11 00:50:23] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -83.500464+0.003326j
[2025-09-11 00:50:46] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -83.406141-0.000032j
[2025-09-11 00:51:10] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -83.379800-0.002782j
[2025-09-11 00:51:33] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -83.404118+0.005243j
[2025-09-11 00:51:56] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -83.479673-0.002151j
[2025-09-11 00:52:20] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -83.485152+0.000067j
[2025-09-11 00:52:43] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -83.598257+0.000913j
[2025-09-11 00:53:07] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -83.649679-0.002716j
[2025-09-11 00:53:30] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -83.732779-0.002589j
[2025-09-11 00:53:54] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -83.655894+0.000689j
[2025-09-11 00:54:17] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -83.587453+0.000022j
[2025-09-11 00:54:40] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -83.551635-0.004268j
[2025-09-11 00:55:04] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -83.588753-0.002966j
[2025-09-11 00:55:27] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -83.627220+0.001111j
[2025-09-11 00:55:51] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -83.472129+0.000998j
[2025-09-11 00:56:14] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -83.422836-0.003253j
[2025-09-11 00:56:37] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -83.511874+0.003413j
[2025-09-11 00:57:01] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -83.632256-0.000300j
[2025-09-11 00:57:24] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -83.595303+0.003390j
[2025-09-11 00:57:48] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -83.726454+0.000126j
[2025-09-11 00:58:11] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -83.598929-0.002545j
[2025-09-11 00:58:35] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -83.704509-0.003433j
[2025-09-11 00:58:58] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -83.733361-0.000253j
[2025-09-11 00:59:21] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -83.812947+0.000411j
[2025-09-11 00:59:21] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-11 00:59:45] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -83.840655+0.000931j
[2025-09-11 01:00:08] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -83.868385-0.000893j
[2025-09-11 01:00:32] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -83.878540-0.002829j
[2025-09-11 01:00:55] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -83.648130+0.005806j
[2025-09-11 01:01:19] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -83.565080+0.003907j
[2025-09-11 01:01:42] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -83.549423+0.000005j
[2025-09-11 01:02:05] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -83.660907+0.001352j
[2025-09-11 01:02:29] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -83.682356+0.003126j
[2025-09-11 01:02:52] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -83.759124-0.000062j
[2025-09-11 01:03:16] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -83.534712+0.004869j
[2025-09-11 01:03:39] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -83.553348+0.000895j
[2025-09-11 01:04:02] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -83.507013-0.000415j
[2025-09-11 01:04:26] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -83.560535+0.002636j
[2025-09-11 01:04:49] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -83.526769-0.001656j
[2025-09-11 01:05:13] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -83.589205-0.000839j
[2025-09-11 01:05:36] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -83.597836-0.003281j
[2025-09-11 01:05:59] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -83.652996+0.001313j
[2025-09-11 01:06:23] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -83.647423+0.000695j
[2025-09-11 01:06:46] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -83.640960+0.002921j
[2025-09-11 01:07:10] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -83.640695-0.001664j
[2025-09-11 01:07:33] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -83.539741-0.004306j
[2025-09-11 01:07:56] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -83.653603-0.001954j
[2025-09-11 01:08:20] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -83.545882+0.000504j
[2025-09-11 01:08:43] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -83.468208+0.005313j
[2025-09-11 01:09:07] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -83.640981-0.000146j
[2025-09-11 01:09:30] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -83.528842+0.000597j
[2025-09-11 01:09:53] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -83.586078+0.001719j
[2025-09-11 01:10:17] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -83.614296+0.000728j
[2025-09-11 01:10:40] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -83.562185-0.000940j
[2025-09-11 01:11:04] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -83.542685+0.001003j
[2025-09-11 01:11:27] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -83.658862-0.000587j
[2025-09-11 01:11:50] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -83.536928-0.001948j
[2025-09-11 01:12:14] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -83.429398-0.001431j
[2025-09-11 01:12:37] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -83.622219-0.001098j
[2025-09-11 01:13:01] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -83.705427-0.000547j
[2025-09-11 01:13:24] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -83.672270-0.000132j
[2025-09-11 01:13:48] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -83.538379-0.000620j
[2025-09-11 01:14:11] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -83.615180-0.001183j
[2025-09-11 01:14:34] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -83.511857+0.000486j
[2025-09-11 01:14:58] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -83.520273-0.000348j
[2025-09-11 01:15:21] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -83.494106+0.001765j
[2025-09-11 01:15:45] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -83.600575+0.007047j
[2025-09-11 01:16:08] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -83.667579-0.001839j
[2025-09-11 01:16:31] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -83.809308-0.005390j
[2025-09-11 01:16:55] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -83.674098-0.002845j
[2025-09-11 01:17:18] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -83.767622+0.002411j
[2025-09-11 01:17:42] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -83.698492-0.000800j
[2025-09-11 01:18:05] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -83.786336-0.005947j
[2025-09-11 01:18:29] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -83.723346-0.004099j
[2025-09-11 01:18:52] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -83.786281-0.002065j
[2025-09-11 01:19:15] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -83.709094-0.001748j
[2025-09-11 01:19:39] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -83.721737+0.000015j
[2025-09-11 01:20:02] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -83.736875+0.000122j
[2025-09-11 01:20:26] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -83.800370+0.004317j
[2025-09-11 01:20:49] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -83.734150-0.001044j
[2025-09-11 01:21:13] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -83.783238-0.000978j
[2025-09-11 01:21:36] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -83.655555+0.004706j
[2025-09-11 01:21:59] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -83.775060-0.001289j
[2025-09-11 01:22:23] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -83.792891-0.001638j
[2025-09-11 01:22:46] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -83.623416+0.000293j
[2025-09-11 01:23:10] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -83.556030-0.000721j
[2025-09-11 01:23:33] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -83.580445+0.002483j
[2025-09-11 01:23:56] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -83.632569+0.001641j
[2025-09-11 01:24:20] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -83.482998-0.004157j
[2025-09-11 01:24:43] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -83.489202-0.002557j
[2025-09-11 01:25:07] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -83.585342+0.003949j
[2025-09-11 01:25:30] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -83.643624-0.003094j
[2025-09-11 01:25:54] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -83.854946-0.000644j
[2025-09-11 01:26:17] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -83.770226-0.003840j
[2025-09-11 01:26:41] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -83.784361-0.005546j
[2025-09-11 01:27:04] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -83.811165-0.001165j
[2025-09-11 01:27:27] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -83.662819-0.000287j
[2025-09-11 01:27:51] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -83.783072-0.003499j
[2025-09-11 01:28:14] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -83.864306-0.000156j
[2025-09-11 01:28:38] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -83.764885+0.001207j
[2025-09-11 01:29:01] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -83.795416+0.000225j
[2025-09-11 01:29:24] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -83.766537-0.000994j
[2025-09-11 01:29:48] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -83.879137-0.000486j
[2025-09-11 01:30:11] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -83.701809-0.004761j
[2025-09-11 01:30:35] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -83.676975-0.002848j
[2025-09-11 01:30:58] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -83.623756+0.002569j
[2025-09-11 01:31:22] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -83.607152+0.001752j
[2025-09-11 01:31:45] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -83.553577+0.001629j
[2025-09-11 01:32:08] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -83.736569-0.000643j
[2025-09-11 01:32:32] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -83.897850-0.001360j
[2025-09-11 01:32:55] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -83.824954+0.003223j
[2025-09-11 01:33:19] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -83.749218-0.002671j
[2025-09-11 01:33:42] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -83.777504-0.001574j
[2025-09-11 01:34:05] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -83.822355+0.004566j
[2025-09-11 01:34:29] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -83.749177-0.001230j
[2025-09-11 01:34:52] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -83.687813-0.000879j
[2025-09-11 01:35:15] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -83.765941-0.002348j
[2025-09-11 01:35:39] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -83.744962-0.004833j
[2025-09-11 01:36:02] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -83.696047+0.002766j
[2025-09-11 01:36:25] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -83.719302+0.002993j
[2025-09-11 01:36:49] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -83.780706+0.003523j
[2025-09-11 01:37:12] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -83.653299-0.004232j
[2025-09-11 01:37:36] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -83.727048-0.002045j
[2025-09-11 01:37:59] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -83.771727-0.001146j
[2025-09-11 01:38:23] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -83.751762-0.003524j
[2025-09-11 01:38:46] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -83.614110-0.001188j
[2025-09-11 01:39:09] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -83.809351+0.000347j
[2025-09-11 01:39:33] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -83.796176-0.000622j
[2025-09-11 01:39:56] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -83.760569+0.001344j
[2025-09-11 01:40:20] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -83.700342-0.001074j
[2025-09-11 01:40:20] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-11 01:40:43] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -83.784764-0.002507j
[2025-09-11 01:41:07] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -83.571986+0.002646j
[2025-09-11 01:41:30] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -83.645634+0.001133j
[2025-09-11 01:41:53] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -83.740131+0.001799j
[2025-09-11 01:42:17] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -83.784632+0.001089j
[2025-09-11 01:42:40] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -83.722765-0.001873j
[2025-09-11 01:43:04] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -83.658049+0.002537j
[2025-09-11 01:43:27] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -83.664400-0.002739j
[2025-09-11 01:43:50] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -83.527554-0.001712j
[2025-09-11 01:44:14] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -83.633281-0.003272j
[2025-09-11 01:44:37] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -83.440260-0.000762j
[2025-09-11 01:45:01] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -83.602910+0.003412j
[2025-09-11 01:45:24] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -83.605951+0.001177j
[2025-09-11 01:45:47] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -83.651774-0.000651j
[2025-09-11 01:46:11] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -83.807759-0.002103j
[2025-09-11 01:46:34] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -83.644739+0.002812j
[2025-09-11 01:46:58] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -83.750590+0.001687j
[2025-09-11 01:47:21] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -83.857943-0.001699j
[2025-09-11 01:47:45] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -83.711467+0.002049j
[2025-09-11 01:48:08] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -83.589296-0.005503j
[2025-09-11 01:48:31] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -83.624321-0.003539j
[2025-09-11 01:48:55] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -83.545573-0.001310j
[2025-09-11 01:49:18] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -83.464733-0.003059j
[2025-09-11 01:49:41] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -83.740702+0.001542j
[2025-09-11 01:50:05] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -83.759066-0.004287j
[2025-09-11 01:50:28] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -83.653525+0.003385j
[2025-09-11 01:50:52] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -83.587306+0.001487j
[2025-09-11 01:51:15] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -83.701611-0.002648j
[2025-09-11 01:51:39] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -83.769150-0.001197j
[2025-09-11 01:52:02] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -83.649563-0.002650j
[2025-09-11 01:52:25] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -83.748003-0.000863j
[2025-09-11 01:52:49] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -83.696684-0.001566j
[2025-09-11 01:53:12] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -83.666688+0.000177j
[2025-09-11 01:53:36] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -83.685091-0.000153j
[2025-09-11 01:53:59] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -83.654845+0.000678j
[2025-09-11 01:54:23] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -83.747210+0.001883j
[2025-09-11 01:54:46] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -83.685340-0.000281j
[2025-09-11 01:55:09] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -83.622048+0.004031j
[2025-09-11 01:55:33] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -83.611842+0.000904j
[2025-09-11 01:55:56] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -83.632881-0.004853j
[2025-09-11 01:56:20] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -83.596529+0.001122j
[2025-09-11 01:56:43] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -83.685642+0.002221j
[2025-09-11 01:57:06] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -83.600528+0.000511j
[2025-09-11 01:57:30] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -83.586009-0.000634j
[2025-09-11 01:57:53] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -83.592573+0.002136j
[2025-09-11 01:58:17] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -83.585393+0.002552j
[2025-09-11 01:58:40] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -83.619727+0.000131j
[2025-09-11 01:59:04] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -83.541488-0.004216j
[2025-09-11 01:59:27] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -83.680126-0.000471j
[2025-09-11 01:59:50] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -83.646146-0.001916j
[2025-09-11 02:00:14] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -83.683940-0.003620j
[2025-09-11 02:00:37] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -83.642039-0.003316j
[2025-09-11 02:01:01] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -83.742054-0.000420j
[2025-09-11 02:01:24] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -83.807292-0.002608j
[2025-09-11 02:01:48] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -83.807130+0.001031j
[2025-09-11 02:02:11] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -83.687928-0.001858j
[2025-09-11 02:02:34] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -83.662756-0.005073j
[2025-09-11 02:02:57] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -83.733307+0.003969j
[2025-09-11 02:03:21] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -83.640832+0.000450j
[2025-09-11 02:03:44] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -83.690784+0.001004j
[2025-09-11 02:04:08] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -83.523209+0.000326j
[2025-09-11 02:04:31] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -83.453991+0.005410j
[2025-09-11 02:04:54] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -83.517240-0.000893j
[2025-09-11 02:05:18] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -83.519759-0.004675j
[2025-09-11 02:05:41] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -83.531907+0.002747j
[2025-09-11 02:06:05] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -83.571373+0.002433j
[2025-09-11 02:06:28] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -83.647362+0.001334j
[2025-09-11 02:06:51] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -83.621039+0.003522j
[2025-09-11 02:07:15] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -83.737371-0.000944j
[2025-09-11 02:07:38] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -83.595739-0.000756j
[2025-09-11 02:08:02] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -83.754577-0.001468j
[2025-09-11 02:08:25] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -83.708023-0.002356j
[2025-09-11 02:08:49] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -83.490071-0.002726j
[2025-09-11 02:09:12] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -83.458303+0.006064j
[2025-09-11 02:09:35] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -83.503526+0.000506j
[2025-09-11 02:09:59] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -83.481598+0.004668j
[2025-09-11 02:10:22] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -83.431436-0.002597j
[2025-09-11 02:10:46] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -83.699339-0.000691j
[2025-09-11 02:11:09] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -83.724100-0.001036j
[2025-09-11 02:11:32] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -83.577353+0.003324j
[2025-09-11 02:11:56] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -83.653998-0.001379j
[2025-09-11 02:12:19] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -83.584107-0.000767j
[2025-09-11 02:12:43] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -83.578866-0.003720j
[2025-09-11 02:13:06] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -83.519711-0.001682j
[2025-09-11 02:13:29] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -83.697181+0.003789j
[2025-09-11 02:13:53] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -83.501717+0.004495j
[2025-09-11 02:14:16] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -83.509775+0.003631j
[2025-09-11 02:14:39] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -83.659100+0.002353j
[2025-09-11 02:15:03] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -83.647144-0.001879j
[2025-09-11 02:15:26] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -83.646811+0.003890j
[2025-09-11 02:15:50] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -83.782746+0.004351j
[2025-09-11 02:16:13] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -83.710197-0.002309j
[2025-09-11 02:16:36] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -83.814233+0.003072j
[2025-09-11 02:17:00] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -83.617322-0.000031j
[2025-09-11 02:17:23] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -83.655568-0.001220j
[2025-09-11 02:17:47] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -83.615574+0.001092j
[2025-09-11 02:18:10] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -83.711661+0.001303j
[2025-09-11 02:18:33] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -83.814402+0.001048j
[2025-09-11 02:18:57] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -83.611061+0.002823j
[2025-09-11 02:19:20] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -83.627894-0.002811j
[2025-09-11 02:19:44] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -83.778633-0.004256j
[2025-09-11 02:20:07] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -83.704913+0.003529j
[2025-09-11 02:20:31] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -83.613612+0.000792j
[2025-09-11 02:20:54] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -83.609511-0.000060j
[2025-09-11 02:21:11] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -83.676160+0.001053j
[2025-09-11 02:21:11] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-11 02:21:11] ✅ Training completed | Restarts: 2
[2025-09-11 02:21:11] ============================================================
[2025-09-11 02:21:11] Training completed | Runtime: 32382.0s
[2025-09-11 02:21:16] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-11 02:21:16] ============================================================
