[2025-09-12 06:10:02] 使用checkpoint文件: results/L=5/J2=0.05/J1=0.01/training/checkpoints/checkpoint_iter_001050.pkl
[2025-09-12 06:10:37] ✓ 从checkpoint加载参数: 1050
[2025-09-12 06:10:37]   - 能量: -78.574900+0.001096j ± 0.113798
[2025-09-12 06:10:37] ================================================================================
[2025-09-12 06:10:37] 加载量子态: L=5, J2=0.05, J1=0.01, checkpoint=checkpoint_iter_001050
[2025-09-12 06:10:37] 使用采样数目: 1048576
[2025-09-12 06:10:37] 设置样本数为: 1048576
[2025-09-12 06:10:37] 开始生成共享样本集...
[2025-09-12 06:20:20] 样本生成完成,耗时: 582.572 秒
[2025-09-12 06:20:20] ================================================================================
[2025-09-12 06:20:20] 开始计算自旋结构因子...
[2025-09-12 06:20:20] 初始化操作符缓存...
[2025-09-12 06:20:20] 预构建所有自旋相关操作符...
[2025-09-12 06:20:20] 开始计算自旋相关函数...
[2025-09-12 06:20:46] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 25.881s
[2025-09-12 06:21:19] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 32.885s
[2025-09-12 06:21:39] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 20.285s
[2025-09-12 06:21:59] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 20.425s
[2025-09-12 06:22:20] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 20.603s
[2025-09-12 06:22:40] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 20.313s
[2025-09-12 06:23:01] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 20.334s
[2025-09-12 06:23:21] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 20.488s
[2025-09-12 06:23:42] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 20.446s
[2025-09-12 06:24:02] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 20.361s
[2025-09-12 06:24:22] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 20.541s
[2025-09-12 06:24:43] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 20.424s
[2025-09-12 06:25:03] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 20.531s
[2025-09-12 06:25:24] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 20.530s
[2025-09-12 06:25:44] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 20.362s
[2025-09-12 06:26:05] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 20.360s
[2025-09-12 06:26:25] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 20.577s
[2025-09-12 06:26:46] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 20.417s
[2025-09-12 06:27:07] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 20.739s
[2025-09-12 06:27:27] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 20.600s
[2025-09-12 06:27:48] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 20.427s
[2025-09-12 06:28:08] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 20.512s
[2025-09-12 06:28:29] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 20.578s
[2025-09-12 06:28:49] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 20.557s
[2025-09-12 06:29:10] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 20.402s
[2025-09-12 06:29:30] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 20.514s
[2025-09-12 06:29:51] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 20.442s
[2025-09-12 06:30:11] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 20.614s
[2025-09-12 06:30:32] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 20.289s
[2025-09-12 06:30:52] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 20.630s
[2025-09-12 06:31:13] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 20.314s
[2025-09-12 06:31:33] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 20.354s
[2025-09-12 06:31:53] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 20.430s
[2025-09-12 06:32:14] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 20.440s
[2025-09-12 06:32:34] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 20.372s
[2025-09-12 06:32:55] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 20.361s
[2025-09-12 06:33:15] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 20.585s
[2025-09-12 06:33:36] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 20.316s
[2025-09-12 06:33:56] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 20.410s
[2025-09-12 06:34:17] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 20.598s
[2025-09-12 06:34:37] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 20.309s
[2025-09-12 06:34:57] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 20.555s
[2025-09-12 06:35:18] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 20.322s
[2025-09-12 06:35:38] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 20.411s
[2025-09-12 06:35:59] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 20.311s
[2025-09-12 06:36:19] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 20.482s
[2025-09-12 06:36:39] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 20.371s
[2025-09-12 06:37:00] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 20.236s
[2025-09-12 06:37:20] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 20.374s
[2025-09-12 06:37:41] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 20.471s
[2025-09-12 06:38:01] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 20.344s
[2025-09-12 06:38:21] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 20.352s
[2025-09-12 06:38:42] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 20.553s
[2025-09-12 06:39:02] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 20.298s
[2025-09-12 06:39:23] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 20.377s
[2025-09-12 06:39:43] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 20.333s
[2025-09-12 06:40:04] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 20.541s
[2025-09-12 06:40:24] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 20.422s
[2025-09-12 06:40:44] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 20.474s
[2025-09-12 06:41:05] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 20.370s
[2025-09-12 06:41:26] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 20.700s
[2025-09-12 06:41:46] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 20.419s
[2025-09-12 06:42:07] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 20.596s
[2025-09-12 06:42:27] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 20.394s
[2025-09-12 06:42:48] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 20.435s
[2025-09-12 06:43:08] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 20.415s
[2025-09-12 06:43:29] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 20.576s
[2025-09-12 06:43:49] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 20.419s
[2025-09-12 06:44:09] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 20.430s
[2025-09-12 06:44:30] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 20.543s
[2025-09-12 06:44:50] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 20.393s
[2025-09-12 06:45:11] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 20.564s
[2025-09-12 06:45:31] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 20.463s
[2025-09-12 06:45:52] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 20.450s
[2025-09-12 06:46:12] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 20.524s
[2025-09-12 06:46:33] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 20.347s
[2025-09-12 06:46:53] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 20.565s
[2025-09-12 06:47:14] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 20.303s
[2025-09-12 06:47:34] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 20.472s
[2025-09-12 06:47:55] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 20.372s
[2025-09-12 06:48:15] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 20.303s
[2025-09-12 06:48:35] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 20.398s
[2025-09-12 06:48:56] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 20.521s
[2025-09-12 06:49:16] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 20.366s
[2025-09-12 06:49:37] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 20.385s
[2025-09-12 06:49:57] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 20.514s
[2025-09-12 06:50:18] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 20.368s
[2025-09-12 06:50:38] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 20.390s
[2025-09-12 06:50:58] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 20.330s
[2025-09-12 06:51:19] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 20.434s
[2025-09-12 06:51:39] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 20.380s
[2025-09-12 06:52:00] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 20.521s
[2025-09-12 06:52:20] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 20.579s
[2025-09-12 06:52:41] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 20.329s
[2025-09-12 06:53:01] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 20.411s
[2025-09-12 06:53:21] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 20.380s
[2025-09-12 06:53:42] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 20.398s
[2025-09-12 06:54:02] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 20.344s
[2025-09-12 06:54:23] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 20.540s
[2025-09-12 06:54:43] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 20.423s
[2025-09-12 06:54:43] 自旋相关函数计算完成,总耗时 2063.37 秒
[2025-09-12 06:54:48] 计算傅里叶变换...
[2025-09-12 06:54:56] 自旋结构因子计算完成
[2025-09-12 06:54:58] 自旋相关函数平均误差: 0.000803
