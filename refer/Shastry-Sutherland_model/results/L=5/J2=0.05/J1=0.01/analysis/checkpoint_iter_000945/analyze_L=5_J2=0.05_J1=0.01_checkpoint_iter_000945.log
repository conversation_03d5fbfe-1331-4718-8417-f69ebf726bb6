[2025-09-12 05:24:58] 使用checkpoint文件: results/L=5/J2=0.05/J1=0.01/training/checkpoints/checkpoint_iter_000945.pkl
[2025-09-12 05:25:33] ✓ 从checkpoint加载参数: 945
[2025-09-12 05:25:33]   - 能量: -78.718214+0.002368j ± 0.108884
[2025-09-12 05:25:33] ================================================================================
[2025-09-12 05:25:33] 加载量子态: L=5, J2=0.05, J1=0.01, checkpoint=checkpoint_iter_000945
[2025-09-12 05:25:33] 使用采样数目: 1048576
[2025-09-12 05:25:33] 设置样本数为: 1048576
[2025-09-12 05:25:33] 开始生成共享样本集...
[2025-09-12 05:35:15] 样本生成完成,耗时: 582.571 秒
[2025-09-12 05:35:15] ================================================================================
[2025-09-12 05:35:15] 开始计算自旋结构因子...
[2025-09-12 05:35:15] 初始化操作符缓存...
[2025-09-12 05:35:15] 预构建所有自旋相关操作符...
[2025-09-12 05:35:16] 开始计算自旋相关函数...
[2025-09-12 05:35:41] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 25.963s
[2025-09-12 05:36:14] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 32.947s
[2025-09-12 05:36:35] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 20.282s
[2025-09-12 05:36:55] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 20.370s
[2025-09-12 05:37:16] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 20.679s
[2025-09-12 05:37:36] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 20.387s
[2025-09-12 05:37:57] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 20.335s
[2025-09-12 05:38:17] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 20.580s
[2025-09-12 05:38:38] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 20.519s
[2025-09-12 05:38:58] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 20.343s
[2025-09-12 05:39:19] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 20.579s
[2025-09-12 05:39:39] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 20.530s
[2025-09-12 05:40:00] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 20.433s
[2025-09-12 05:40:20] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 20.348s
[2025-09-12 05:40:41] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 20.604s
[2025-09-12 05:41:01] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 20.607s
[2025-09-12 05:41:22] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 20.458s
[2025-09-12 05:41:42] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 20.447s
[2025-09-12 05:42:03] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 20.258s
[2025-09-12 05:42:23] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 20.538s
[2025-09-12 05:42:44] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 20.658s
[2025-09-12 05:43:04] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 20.271s
[2025-09-12 05:43:24] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 20.401s
[2025-09-12 05:43:45] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 20.321s
[2025-09-12 05:44:05] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 20.371s
[2025-09-12 05:44:25] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 20.239s
[2025-09-12 05:44:46] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 20.460s
[2025-09-12 05:45:06] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 20.453s
[2025-09-12 05:45:27] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 20.536s
[2025-09-12 05:45:47] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 20.256s
[2025-09-12 05:46:08] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 20.525s
[2025-09-12 05:46:28] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 20.419s
[2025-09-12 05:46:49] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 20.406s
[2025-09-12 05:47:09] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 20.251s
[2025-09-12 05:47:30] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 20.653s
[2025-09-12 05:47:50] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 20.459s
[2025-09-12 05:48:10] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 20.307s
[2025-09-12 05:48:31] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 20.680s
[2025-09-12 05:48:51] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 20.293s
[2025-09-12 05:49:12] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 20.332s
[2025-09-12 05:49:32] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 20.644s
[2025-09-12 05:49:53] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 20.333s
[2025-09-12 05:50:13] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 20.584s
[2025-09-12 05:50:34] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 20.232s
[2025-09-12 05:50:54] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 20.364s
[2025-09-12 05:51:14] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 20.354s
[2025-09-12 05:51:35] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 20.569s
[2025-09-12 05:51:56] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 20.594s
[2025-09-12 05:52:16] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 20.629s
[2025-09-12 05:52:36] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 20.187s
[2025-09-12 05:52:57] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 20.552s
[2025-09-12 05:53:18] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 20.559s
[2025-09-12 05:53:38] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 20.311s
[2025-09-12 05:53:58] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 20.542s
[2025-09-12 05:54:19] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 20.605s
[2025-09-12 05:54:40] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 20.581s
[2025-09-12 05:55:00] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 20.406s
[2025-09-12 05:55:21] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 20.586s
[2025-09-12 05:55:41] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 20.407s
[2025-09-12 05:56:01] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 20.233s
[2025-09-12 05:56:22] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 20.515s
[2025-09-12 05:56:42] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 20.477s
[2025-09-12 05:57:03] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 20.649s
[2025-09-12 05:57:23] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 20.334s
[2025-09-12 05:57:44] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 20.427s
[2025-09-12 05:58:04] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 20.478s
[2025-09-12 05:58:25] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 20.640s
[2025-09-12 05:58:45] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 20.344s
[2025-09-12 05:59:05] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 20.257s
[2025-09-12 05:59:26] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 20.432s
[2025-09-12 05:59:46] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 20.335s
[2025-09-12 06:00:07] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 20.517s
[2025-09-12 06:00:27] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 20.540s
[2025-09-12 06:00:48] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 20.285s
[2025-09-12 06:01:08] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 20.580s
[2025-09-12 06:01:29] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 20.320s
[2025-09-12 06:01:49] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 20.622s
[2025-09-12 06:02:10] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 20.398s
[2025-09-12 06:02:30] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 20.570s
[2025-09-12 06:02:51] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 20.470s
[2025-09-12 06:03:11] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 20.328s
[2025-09-12 06:03:32] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 20.483s
[2025-09-12 06:03:52] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 20.490s
[2025-09-12 06:04:13] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 20.518s
[2025-09-12 06:04:33] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 20.450s
[2025-09-12 06:04:54] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 20.518s
[2025-09-12 06:05:14] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 20.366s
[2025-09-12 06:05:34] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 20.256s
[2025-09-12 06:05:55] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 20.347s
[2025-09-12 06:06:15] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 20.603s
[2025-09-12 06:06:36] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 20.262s
[2025-09-12 06:06:56] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 20.529s
[2025-09-12 06:07:17] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 20.557s
[2025-09-12 06:07:37] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 20.399s
[2025-09-12 06:07:58] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 20.505s
[2025-09-12 06:08:18] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 20.276s
[2025-09-12 06:08:38] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 20.473s
[2025-09-12 06:08:59] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 20.474s
[2025-09-12 06:09:19] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 20.476s
[2025-09-12 06:09:40] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 20.435s
[2025-09-12 06:09:40] 自旋相关函数计算完成,总耗时 2064.24 秒
[2025-09-12 06:09:45] 计算傅里叶变换...
[2025-09-12 06:09:52] 自旋结构因子计算完成
[2025-09-12 06:09:54] 自旋相关函数平均误差: 0.000804
