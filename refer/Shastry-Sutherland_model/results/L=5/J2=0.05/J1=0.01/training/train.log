[2025-09-10 17:21:00] ✓ 从checkpoint恢复: results/L=5/J2=0.05/J1=0.02/training/checkpoints/final_GCNN.pkl
[2025-09-10 17:21:00]   - 迭代次数: final
[2025-09-10 17:21:00]   - 能量: -79.322765+0.003366j ± 0.114730
[2025-09-10 17:21:00]   - 时间戳: 2025-09-10T17:14:20.320596+08:00
[2025-09-10 17:21:29] ✓ 变分状态参数已从checkpoint恢复
[2025-09-10 17:21:29] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-10 17:21:29] ==================================================
[2025-09-10 17:21:29] GCNN for Shastry-Sutherland Model
[2025-09-10 17:21:29] ==================================================
[2025-09-10 17:21:29] System parameters:
[2025-09-10 17:21:29]   - System size: L=5, N=100
[2025-09-10 17:21:29]   - System parameters: J1=0.01, J2=0.05, Q=0.95
[2025-09-10 17:21:29] --------------------------------------------------
[2025-09-10 17:21:29] Model parameters:
[2025-09-10 17:21:29]   - Number of layers = 4
[2025-09-10 17:21:29]   - Number of features = 4
[2025-09-10 17:21:29]   - Total parameters = 19628
[2025-09-10 17:21:29] --------------------------------------------------
[2025-09-10 17:21:29] Training parameters:
[2025-09-10 17:21:29]   - Learning rate: 0.015
[2025-09-10 17:21:29]   - Total iterations: 1050
[2025-09-10 17:21:29]   - Annealing cycles: 3
[2025-09-10 17:21:29]   - Initial period: 150
[2025-09-10 17:21:29]   - Period multiplier: 2.0
[2025-09-10 17:21:29]   - Temperature range: 0.0-1.0
[2025-09-10 17:21:29]   - Samples: 4096
[2025-09-10 17:21:29]   - Discarded samples: 0
[2025-09-10 17:21:29]   - Chunk size: 2048
[2025-09-10 17:21:29]   - Diagonal shift: 0.2
[2025-09-10 17:21:29]   - Gradient clipping: 1.0
[2025-09-10 17:21:29]   - Checkpoint enabled: interval=105
[2025-09-10 17:21:29]   - Checkpoint directory: results/L=5/J2=0.05/J1=0.01/training/checkpoints
[2025-09-10 17:21:29] --------------------------------------------------
[2025-09-10 17:21:29] Device status:
[2025-09-10 17:21:29]   - Devices model: NVIDIA H200 NVL
[2025-09-10 17:21:29]   - Number of devices: 1
[2025-09-10 17:21:29]   - Sharding: True
[2025-09-10 17:21:29] ============================================================
[2025-09-10 17:23:56] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -78.675278-0.020344j
[2025-09-10 17:25:32] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -78.705231-0.019228j
[2025-09-10 17:26:07] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -78.816774-0.015804j
[2025-09-10 17:26:42] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -78.699958-0.007829j
[2025-09-10 17:27:17] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -78.809954-0.009068j
[2025-09-10 17:27:52] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -78.886818-0.005732j
[2025-09-10 17:28:27] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -78.796404-0.000558j
[2025-09-10 17:29:02] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -78.839481-0.006200j
[2025-09-10 17:29:37] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -78.839967-0.000255j
[2025-09-10 17:30:12] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -78.779785-0.006703j
[2025-09-10 17:30:48] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -78.761588-0.005344j
[2025-09-10 17:31:23] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -78.788773-0.003067j
[2025-09-10 17:31:58] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -78.676437+0.001711j
[2025-09-10 17:32:33] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -78.808547-0.002406j
[2025-09-10 17:33:08] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -78.763593-0.002408j
[2025-09-10 17:33:43] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -78.692126-0.001368j
[2025-09-10 17:34:18] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -78.688877+0.005291j
[2025-09-10 17:34:53] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -78.812761+0.001120j
[2025-09-10 17:35:28] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -78.786531+0.000932j
[2025-09-10 17:36:03] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -78.737901+0.001007j
[2025-09-10 17:36:39] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -78.719322-0.002339j
[2025-09-10 17:37:14] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -78.765085+0.002617j
[2025-09-10 17:37:49] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -78.659833+0.000150j
[2025-09-10 17:38:24] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -78.835774-0.000303j
[2025-09-10 17:39:00] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -78.923578-0.000582j
[2025-09-10 17:39:35] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -78.806879-0.004698j
[2025-09-10 17:40:10] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -78.766563+0.003586j
[2025-09-10 17:40:45] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -78.780057-0.000837j
[2025-09-10 17:41:20] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -78.641360+0.001744j
[2025-09-10 17:41:56] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -78.674932+0.002869j
[2025-09-10 17:42:31] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -78.698810+0.002432j
[2025-09-10 17:43:06] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -78.832588-0.004150j
[2025-09-10 17:43:40] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -78.932878+0.001427j
[2025-09-10 17:44:15] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -78.785654-0.002308j
[2025-09-10 17:44:50] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -78.839735-0.004647j
[2025-09-10 17:45:25] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -78.725455+0.000565j
[2025-09-10 17:46:00] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -78.794787+0.001208j
[2025-09-10 17:46:35] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -78.868043-0.002540j
[2025-09-10 17:47:09] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -78.749114-0.001002j
[2025-09-10 17:47:44] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -78.758666-0.000545j
[2025-09-10 17:48:19] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -78.743678-0.001097j
[2025-09-10 17:48:54] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -78.794264+0.001681j
[2025-09-10 17:49:29] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -78.732091-0.000571j
[2025-09-10 17:50:04] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -78.784585-0.000019j
[2025-09-10 17:50:39] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -78.667006-0.001228j
[2025-09-10 17:51:14] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -78.746011+0.000684j
[2025-09-10 17:51:49] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -78.690839-0.000214j
[2025-09-10 17:52:24] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -78.780742-0.002348j
[2025-09-10 17:53:00] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -78.838733-0.001337j
[2025-09-10 17:53:34] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -78.793232-0.001440j
[2025-09-10 17:54:09] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -78.766603-0.006795j
[2025-09-10 17:54:44] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -78.651845-0.002527j
[2025-09-10 17:55:19] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -78.775842-0.000706j
[2025-09-10 17:55:54] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -78.724069-0.006348j
[2025-09-10 17:56:29] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -78.749373-0.000411j
[2025-09-10 17:57:04] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -78.789986-0.000352j
[2025-09-10 17:57:39] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -78.753751+0.004105j
[2025-09-10 17:58:13] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -78.537602-0.000772j
[2025-09-10 17:58:48] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -78.552006-0.001374j
[2025-09-10 17:59:23] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -78.598076+0.000457j
[2025-09-10 17:59:58] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -78.626691+0.002422j
[2025-09-10 18:00:34] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -78.624301-0.004481j
[2025-09-10 18:01:09] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -78.575613+0.001318j
[2025-09-10 18:01:44] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -78.630852-0.003393j
[2025-09-10 18:02:19] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -78.766719-0.000543j
[2025-09-10 18:02:54] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -78.778209-0.000771j
[2025-09-10 18:03:30] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -78.758630+0.004731j
[2025-09-10 18:04:05] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -78.830192+0.000852j
[2025-09-10 18:04:40] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -78.816058-0.000255j
[2025-09-10 18:05:15] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -78.899453+0.002463j
[2025-09-10 18:05:50] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -78.840584-0.002892j
[2025-09-10 18:06:24] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -78.802668-0.000501j
[2025-09-10 18:06:59] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -78.773092-0.003773j
[2025-09-10 18:07:34] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -78.863655-0.001705j
[2025-09-10 18:08:09] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -78.951309+0.003140j
[2025-09-10 18:08:44] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -78.880424-0.002176j
[2025-09-10 18:09:19] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -78.831990-0.002043j
[2025-09-10 18:09:54] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -78.783874+0.001779j
[2025-09-10 18:10:29] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -78.867551+0.000849j
[2025-09-10 18:11:03] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -78.788617+0.001993j
[2025-09-10 18:11:38] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -78.738792+0.001138j
[2025-09-10 18:12:13] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -78.702629-0.002289j
[2025-09-10 18:12:48] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -78.577830+0.001281j
[2025-09-10 18:13:23] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -78.632693+0.002446j
[2025-09-10 18:13:59] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -78.668494+0.001771j
[2025-09-10 18:14:34] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -78.800009+0.000494j
[2025-09-10 18:15:08] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -78.836867-0.002956j
[2025-09-10 18:15:43] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -78.900202+0.001793j
[2025-09-10 18:16:18] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -78.827205-0.003843j
[2025-09-10 18:16:53] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -78.776026-0.001997j
[2025-09-10 18:17:28] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -78.827009-0.000175j
[2025-09-10 18:18:03] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -78.868339-0.000152j
[2025-09-10 18:18:38] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -78.778220+0.005141j
[2025-09-10 18:19:12] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -78.791481+0.002904j
[2025-09-10 18:19:47] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -78.606369+0.000409j
[2025-09-10 18:20:22] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -78.672032-0.000713j
[2025-09-10 18:20:57] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -78.711688+0.001825j
[2025-09-10 18:21:32] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -78.654642+0.001219j
[2025-09-10 18:22:08] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -78.688343-0.000463j
[2025-09-10 18:22:43] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -78.542981+0.003801j
[2025-09-10 18:23:18] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -78.600350-0.001533j
[2025-09-10 18:23:53] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -78.620450-0.002892j
[2025-09-10 18:24:28] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -78.626430+0.001089j
[2025-09-10 18:25:04] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -78.702875+0.000246j
[2025-09-10 18:25:39] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -78.662433+0.001990j
[2025-09-10 18:25:39] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-10 18:26:14] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -78.574505+0.003901j
[2025-09-10 18:26:48] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -78.521048+0.003297j
[2025-09-10 18:27:23] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -78.606212+0.002516j
[2025-09-10 18:27:59] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -78.532764-0.002553j
[2025-09-10 18:28:34] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -78.605914+0.001749j
[2025-09-10 18:29:09] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -78.599587+0.000407j
[2025-09-10 18:29:44] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -78.559290+0.005730j
[2025-09-10 18:30:19] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -78.615340-0.000215j
[2025-09-10 18:30:54] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -78.657371+0.000511j
[2025-09-10 18:31:29] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -78.622162+0.000246j
[2025-09-10 18:32:04] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -78.683486+0.001333j
[2025-09-10 18:32:39] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -78.642897+0.001652j
[2025-09-10 18:33:13] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -78.656554-0.003676j
[2025-09-10 18:33:48] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -78.671339+0.001119j
[2025-09-10 18:34:24] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -78.664648-0.000560j
[2025-09-10 18:34:59] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -78.683536-0.000689j
[2025-09-10 18:35:34] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -78.812824+0.004258j
[2025-09-10 18:36:09] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -78.740014-0.002793j
[2025-09-10 18:36:44] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -78.786319-0.004953j
[2025-09-10 18:37:20] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -78.808856-0.000486j
[2025-09-10 18:37:55] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -78.727573-0.002283j
[2025-09-10 18:38:30] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -78.825610+0.003061j
[2025-09-10 18:39:05] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -78.823582+0.000786j
[2025-09-10 18:39:40] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -78.847144+0.002218j
[2025-09-10 18:40:15] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -78.854422+0.007821j
[2025-09-10 18:40:50] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -78.806467-0.000408j
[2025-09-10 18:41:25] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -78.802145-0.003643j
[2025-09-10 18:42:00] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -78.730066-0.000501j
[2025-09-10 18:42:35] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -78.804835-0.003950j
[2025-09-10 18:43:10] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -78.638069+0.002995j
[2025-09-10 18:43:45] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -78.826948-0.001977j
[2025-09-10 18:44:20] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -78.743001+0.000100j
[2025-09-10 18:44:55] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -78.631594+0.000810j
[2025-09-10 18:45:30] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -78.714080+0.006244j
[2025-09-10 18:46:05] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -78.631920-0.006155j
[2025-09-10 18:46:40] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -78.676922-0.003651j
[2025-09-10 18:47:15] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -78.765774+0.001092j
[2025-09-10 18:47:50] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -78.713922+0.004056j
[2025-09-10 18:48:25] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -78.605097-0.000146j
[2025-09-10 18:49:00] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -78.705802-0.001162j
[2025-09-10 18:49:35] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -78.672520+0.005532j
[2025-09-10 18:50:10] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -78.578385-0.003470j
[2025-09-10 18:50:45] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -78.597456+0.003728j
[2025-09-10 18:51:21] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -78.471297-0.001447j
[2025-09-10 18:51:56] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -78.569755+0.007088j
[2025-09-10 18:51:56] RESTART #1 | Period: 300
[2025-09-10 18:52:31] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -78.559429+0.002582j
[2025-09-10 18:53:06] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -78.632355+0.001609j
[2025-09-10 18:53:41] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -78.711733+0.001411j
[2025-09-10 18:54:16] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -78.798666-0.005633j
[2025-09-10 18:54:51] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -78.874200+0.001251j
[2025-09-10 18:55:26] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -78.812053+0.003532j
[2025-09-10 18:56:01] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -78.872795-0.002484j
[2025-09-10 18:56:36] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -78.872884-0.002659j
[2025-09-10 18:57:11] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -78.744661-0.000931j
[2025-09-10 18:57:47] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -78.728787-0.000261j
[2025-09-10 18:58:22] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -78.718247-0.000248j
[2025-09-10 18:58:57] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -78.957297-0.003334j
[2025-09-10 18:59:32] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -78.822276-0.000917j
[2025-09-10 19:00:07] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -78.597534-0.002455j
[2025-09-10 19:00:42] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -78.716192-0.005336j
[2025-09-10 19:01:17] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -78.750641+0.000875j
[2025-09-10 19:01:52] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -78.778224+0.000749j
[2025-09-10 19:02:27] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -78.785454-0.003764j
[2025-09-10 19:03:02] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -78.848566+0.000186j
[2025-09-10 19:03:37] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -78.724436-0.002525j
[2025-09-10 19:04:12] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -78.711325+0.002760j
[2025-09-10 19:04:48] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -78.692718-0.006264j
[2025-09-10 19:05:23] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -78.692732+0.017797j
[2025-09-10 19:05:58] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -78.607920-0.000790j
[2025-09-10 19:06:33] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -78.544673+0.003694j
[2025-09-10 19:07:08] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -78.498823+0.000689j
[2025-09-10 19:07:43] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -78.741695-0.002907j
[2025-09-10 19:08:19] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -78.688330-0.000216j
[2025-09-10 19:08:54] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -78.716392-0.002927j
[2025-09-10 19:09:29] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -78.611068-0.002535j
[2025-09-10 19:10:04] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -78.673514+0.002930j
[2025-09-10 19:10:39] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -78.667642-0.001808j
[2025-09-10 19:11:15] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -78.707140+0.003300j
[2025-09-10 19:11:50] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -78.689435+0.002775j
[2025-09-10 19:12:25] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -78.702767-0.006833j
[2025-09-10 19:13:00] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -78.774007+0.003731j
[2025-09-10 19:13:35] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -78.870365-0.002710j
[2025-09-10 19:14:10] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -78.705981-0.001185j
[2025-09-10 19:14:45] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -78.725882+0.001713j
[2025-09-10 19:15:20] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -78.807799+0.003266j
[2025-09-10 19:15:55] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -78.622858-0.004317j
[2025-09-10 19:16:30] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -78.631984+0.002479j
[2025-09-10 19:17:06] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -78.577590+0.002227j
[2025-09-10 19:17:41] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -78.684155-0.002907j
[2025-09-10 19:18:16] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -78.682316-0.001160j
[2025-09-10 19:18:51] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -78.641468+0.001606j
[2025-09-10 19:19:26] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -78.801347-0.000899j
[2025-09-10 19:20:01] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -78.762404-0.001275j
[2025-09-10 19:20:36] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -78.771982+0.000106j
[2025-09-10 19:21:11] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -78.671194-0.004208j
[2025-09-10 19:21:46] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -78.758327-0.000190j
[2025-09-10 19:22:21] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -78.676091+0.002579j
[2025-09-10 19:22:55] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -78.649290+0.007933j
[2025-09-10 19:23:30] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -78.626733-0.005821j
[2025-09-10 19:24:06] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -78.579529-0.002351j
[2025-09-10 19:24:41] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -78.424924-0.001363j
[2025-09-10 19:25:16] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -78.543983-0.002304j
[2025-09-10 19:25:51] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -78.387822-0.004204j
[2025-09-10 19:26:26] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -78.463583-0.002893j
[2025-09-10 19:27:02] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -78.614504-0.006518j
[2025-09-10 19:27:02] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-10 19:27:37] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -78.682048+0.002933j
[2025-09-10 19:28:12] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -78.732983-0.002898j
[2025-09-10 19:28:47] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -78.775766-0.013118j
[2025-09-10 19:29:23] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -78.688309-0.001452j
[2025-09-10 19:29:58] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -78.844700+0.001902j
[2025-09-10 19:30:33] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -78.801685-0.003055j
[2025-09-10 19:31:08] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -78.701293-0.003599j
[2025-09-10 19:31:44] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -78.805027-0.000631j
[2025-09-10 19:32:19] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -78.709817-0.001625j
[2025-09-10 19:32:54] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -78.747581+0.005507j
[2025-09-10 19:33:29] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -78.706801+0.000713j
[2025-09-10 19:34:04] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -78.699221-0.000691j
[2025-09-10 19:34:39] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -78.816506-0.004300j
[2025-09-10 19:35:14] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -78.743697-0.000055j
[2025-09-10 19:35:49] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -78.623893+0.001976j
[2025-09-10 19:36:24] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -78.692758-0.003718j
[2025-09-10 19:36:59] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -78.675214-0.002295j
[2025-09-10 19:37:34] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -78.591374-0.004905j
[2025-09-10 19:38:09] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -78.607066-0.000885j
[2025-09-10 19:38:44] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -78.580142-0.006859j
[2025-09-10 19:39:20] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -78.656397-0.001045j
[2025-09-10 19:39:55] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -78.497242-0.000912j
[2025-09-10 19:40:30] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -78.583709-0.000629j
[2025-09-10 19:41:05] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -78.642670-0.000193j
[2025-09-10 19:41:40] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -78.622554+0.007151j
[2025-09-10 19:42:15] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -78.763684+0.000221j
[2025-09-10 19:42:50] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -78.695751+0.003398j
[2025-09-10 19:43:25] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -78.560877+0.003259j
[2025-09-10 19:44:00] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -78.655774+0.001684j
[2025-09-10 19:44:35] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -78.755756-0.000187j
[2025-09-10 19:45:11] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -78.703796+0.002053j
[2025-09-10 19:45:46] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -78.831655-0.002652j
[2025-09-10 19:46:21] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -78.804888+0.001811j
[2025-09-10 19:46:56] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -78.747927-0.004294j
[2025-09-10 19:47:31] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -78.820941-0.000174j
[2025-09-10 19:48:06] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -78.871073-0.000806j
[2025-09-10 19:48:42] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -78.757996+0.005415j
[2025-09-10 19:49:17] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -78.786320+0.003568j
[2025-09-10 19:49:52] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -78.869052-0.000238j
[2025-09-10 19:50:27] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -78.830027+0.001074j
[2025-09-10 19:51:02] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -78.839778+0.001765j
[2025-09-10 19:51:37] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -78.666539+0.002805j
[2025-09-10 19:52:13] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -78.752639-0.000268j
[2025-09-10 19:52:48] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -78.620311+0.001747j
[2025-09-10 19:53:23] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -78.707442-0.003711j
[2025-09-10 19:53:58] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -78.763864-0.000712j
[2025-09-10 19:54:33] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -78.725153+0.002133j
[2025-09-10 19:55:08] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -78.705283-0.001170j
[2025-09-10 19:55:42] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -78.681736-0.000640j
[2025-09-10 19:56:18] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -78.602057+0.002374j
[2025-09-10 19:56:53] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -78.462012-0.000134j
[2025-09-10 19:57:28] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -78.524153-0.000666j
[2025-09-10 19:58:03] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -78.579597-0.003547j
[2025-09-10 19:58:38] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -78.623301-0.002293j
[2025-09-10 19:59:13] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -78.584274-0.000330j
[2025-09-10 19:59:48] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -78.637776+0.002867j
[2025-09-10 20:00:23] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -78.657936+0.000069j
[2025-09-10 20:00:58] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -78.809429+0.001315j
[2025-09-10 20:01:32] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -78.589632+0.003718j
[2025-09-10 20:02:07] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -78.625722-0.000045j
[2025-09-10 20:02:42] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -78.789480+0.000817j
[2025-09-10 20:03:17] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -78.881470+0.002526j
[2025-09-10 20:03:52] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -78.784434+0.000948j
[2025-09-10 20:04:27] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -78.724318+0.000933j
[2025-09-10 20:05:02] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -78.779770+0.002135j
[2025-09-10 20:05:37] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -78.788204+0.000196j
[2025-09-10 20:06:13] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -78.775902+0.000589j
[2025-09-10 20:06:48] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -78.733642-0.001740j
[2025-09-10 20:07:23] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -78.795308-0.001857j
[2025-09-10 20:07:58] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -78.711570+0.002547j
[2025-09-10 20:08:33] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -78.780048-0.000671j
[2025-09-10 20:09:08] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -78.810212+0.000668j
[2025-09-10 20:09:43] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -78.921717-0.004132j
[2025-09-10 20:10:18] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -78.854362-0.002499j
[2025-09-10 20:10:53] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -78.722427-0.001252j
[2025-09-10 20:11:28] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -78.666282-0.003052j
[2025-09-10 20:12:04] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -78.641847+0.001268j
[2025-09-10 20:12:39] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -78.697209+0.002108j
[2025-09-10 20:13:14] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -78.738323-0.000844j
[2025-09-10 20:13:49] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -78.562298+0.000334j
[2025-09-10 20:14:25] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -78.571796+0.002245j
[2025-09-10 20:15:00] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -78.479348-0.000707j
[2025-09-10 20:15:34] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -78.696634-0.001531j
[2025-09-10 20:16:09] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -78.740595-0.003088j
[2025-09-10 20:16:44] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -78.605400-0.001660j
[2025-09-10 20:17:19] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -78.681268+0.001292j
[2025-09-10 20:17:54] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -78.701312+0.005442j
[2025-09-10 20:18:30] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -78.756502+0.004076j
[2025-09-10 20:19:05] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -78.612568+0.003979j
[2025-09-10 20:19:40] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -78.539400+0.000393j
[2025-09-10 20:20:15] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -78.588006+0.001319j
[2025-09-10 20:20:50] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -78.707201+0.001409j
[2025-09-10 20:21:26] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -78.802928+0.003513j
[2025-09-10 20:22:00] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -78.831303-0.001403j
[2025-09-10 20:22:35] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -78.740549-0.003067j
[2025-09-10 20:23:10] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -78.811931+0.001425j
[2025-09-10 20:23:45] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -78.759337-0.001900j
[2025-09-10 20:24:20] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -78.858917-0.002975j
[2025-09-10 20:24:55] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -78.719662-0.000911j
[2025-09-10 20:25:31] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -78.782226-0.003314j
[2025-09-10 20:26:06] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -78.630364-0.005088j
[2025-09-10 20:26:41] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -78.692955+0.002739j
[2025-09-10 20:27:16] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -78.653087+0.000330j
[2025-09-10 20:27:51] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -78.854370+0.001139j
[2025-09-10 20:28:26] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -78.753446-0.003181j
[2025-09-10 20:28:26] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-10 20:29:01] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -78.757891+0.004475j
[2025-09-10 20:29:36] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -78.654682+0.004005j
[2025-09-10 20:30:11] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -78.678376+0.002173j
[2025-09-10 20:30:46] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -78.587764+0.001609j
[2025-09-10 20:31:21] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -78.751869+0.001011j
[2025-09-10 20:31:56] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -78.809514-0.003201j
[2025-09-10 20:32:30] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -78.788793-0.001673j
[2025-09-10 20:33:05] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -78.808306+0.002567j
[2025-09-10 20:33:40] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -78.615359-0.003601j
[2025-09-10 20:34:15] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -78.667020-0.005037j
[2025-09-10 20:34:50] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -78.643798-0.001821j
[2025-09-10 20:35:25] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -78.677684-0.003943j
[2025-09-10 20:36:00] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -78.744560+0.002955j
[2025-09-10 20:36:36] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -78.669961-0.011739j
[2025-09-10 20:37:11] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -78.616943-0.002152j
[2025-09-10 20:37:46] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -78.683791-0.006247j
[2025-09-10 20:38:21] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -78.785735+0.000015j
[2025-09-10 20:38:57] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -78.775538+0.000900j
[2025-09-10 20:39:32] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -78.587067+0.001654j
[2025-09-10 20:40:07] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -78.624905+0.002912j
[2025-09-10 20:40:42] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -78.598254+0.000154j
[2025-09-10 20:41:18] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -78.602370+0.002628j
[2025-09-10 20:41:53] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -78.660745+0.002622j
[2025-09-10 20:42:28] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -78.380293+0.005009j
[2025-09-10 20:43:03] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -78.599327+0.001479j
[2025-09-10 20:43:39] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -78.464481+0.000814j
[2025-09-10 20:44:14] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -78.680583+0.000789j
[2025-09-10 20:44:49] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -78.426217+0.000983j
[2025-09-10 20:45:24] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -78.510284-0.000624j
[2025-09-10 20:45:59] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -78.684339-0.003826j
[2025-09-10 20:46:34] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -78.578851-0.002719j
[2025-09-10 20:47:10] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -78.536176-0.002424j
[2025-09-10 20:47:45] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -78.622349+0.000260j
[2025-09-10 20:48:20] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -78.496914+0.002179j
[2025-09-10 20:48:55] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -78.467925-0.001881j
[2025-09-10 20:49:30] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -78.518332+0.006495j
[2025-09-10 20:50:05] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -78.633988-0.001332j
[2025-09-10 20:50:40] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -78.570617+0.003230j
[2025-09-10 20:51:15] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -78.710075-0.003276j
[2025-09-10 20:51:50] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -78.699841+0.000202j
[2025-09-10 20:52:25] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -78.749180+0.001363j
[2025-09-10 20:53:00] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -78.681305+0.002190j
[2025-09-10 20:53:34] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -78.727335+0.001146j
[2025-09-10 20:54:09] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -78.617824+0.000760j
[2025-09-10 20:54:45] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -78.695545-0.000674j
[2025-09-10 20:55:20] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -78.722311-0.000129j
[2025-09-10 20:55:55] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -78.596209-0.002689j
[2025-09-10 20:56:30] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -78.715119+0.000999j
[2025-09-10 20:57:05] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -78.703519+0.000536j
[2025-09-10 20:57:41] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -78.716020+0.003515j
[2025-09-10 20:58:16] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -78.752665-0.001044j
[2025-09-10 20:58:51] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -78.720570+0.002554j
[2025-09-10 20:59:26] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -78.826574-0.005448j
[2025-09-10 21:00:02] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -78.680486-0.001469j
[2025-09-10 21:00:37] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -78.524171+0.001121j
[2025-09-10 21:01:12] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -78.721819+0.000038j
[2025-09-10 21:01:47] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -78.871087+0.002028j
[2025-09-10 21:02:22] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -78.604262+0.003385j
[2025-09-10 21:02:57] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -78.685231-0.000313j
[2025-09-10 21:03:32] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -78.745461-0.005413j
[2025-09-10 21:04:07] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -78.594529-0.001030j
[2025-09-10 21:04:42] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -78.662591-0.001522j
[2025-09-10 21:05:17] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -78.545031+0.000537j
[2025-09-10 21:05:52] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -78.687050-0.003073j
[2025-09-10 21:06:27] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -78.595139+0.003566j
[2025-09-10 21:07:02] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -78.559683-0.002577j
[2025-09-10 21:07:37] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -78.543218+0.002559j
[2025-09-10 21:08:12] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -78.506312+0.000879j
[2025-09-10 21:08:48] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -78.519788-0.002028j
[2025-09-10 21:09:23] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -78.745711+0.000829j
[2025-09-10 21:09:58] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -78.634880-0.004430j
[2025-09-10 21:10:33] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -78.707101-0.000530j
[2025-09-10 21:11:08] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -78.630644+0.000592j
[2025-09-10 21:11:43] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -78.627180-0.000402j
[2025-09-10 21:12:18] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -78.577679+0.002720j
[2025-09-10 21:12:53] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -78.579699+0.001333j
[2025-09-10 21:13:28] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -78.636326+0.000387j
[2025-09-10 21:14:03] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -78.775581+0.003095j
[2025-09-10 21:14:38] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -78.587112-0.001742j
[2025-09-10 21:15:13] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -78.803635+0.003261j
[2025-09-10 21:15:48] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -78.725903-0.000496j
[2025-09-10 21:16:23] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -78.693643+0.000240j
[2025-09-10 21:16:58] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -78.654256+0.002605j
[2025-09-10 21:17:33] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -78.583104-0.003136j
[2025-09-10 21:18:08] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -78.681377-0.001890j
[2025-09-10 21:18:43] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -78.693550+0.005075j
[2025-09-10 21:19:18] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -78.845846-0.000725j
[2025-09-10 21:19:53] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -78.885638-0.004533j
[2025-09-10 21:20:28] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -78.692422+0.002176j
[2025-09-10 21:21:04] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -78.842399+0.003457j
[2025-09-10 21:21:39] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -78.886806+0.001027j
[2025-09-10 21:22:14] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -78.765092-0.001578j
[2025-09-10 21:22:49] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -78.890220+0.000571j
[2025-09-10 21:23:24] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -78.900836-0.001778j
[2025-09-10 21:24:00] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -78.816854-0.002584j
[2025-09-10 21:24:35] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -78.691761-0.003076j
[2025-09-10 21:25:10] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -78.821883+0.001577j
[2025-09-10 21:25:45] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -79.018332+0.003360j
[2025-09-10 21:26:21] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -78.956908+0.001426j
[2025-09-10 21:26:56] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -78.942670+0.001027j
[2025-09-10 21:27:31] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -78.966517+0.001324j
[2025-09-10 21:28:06] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -78.928628-0.001477j
[2025-09-10 21:28:41] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -78.865588+0.001013j
[2025-09-10 21:29:17] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -78.607907+0.000188j
[2025-09-10 21:29:52] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -78.612858+0.000942j
[2025-09-10 21:29:52] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-10 21:30:27] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -78.544237+0.003070j
[2025-09-10 21:31:02] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -78.637843+0.003135j
[2025-09-10 21:31:37] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -78.669745+0.002495j
[2025-09-10 21:32:12] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -78.759026-0.002202j
[2025-09-10 21:32:47] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -78.752645-0.008578j
[2025-09-10 21:33:22] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -78.582205+0.001144j
[2025-09-10 21:33:57] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -78.613605-0.000849j
[2025-09-10 21:34:32] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -78.565688-0.002848j
[2025-09-10 21:35:07] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -78.486557+0.000634j
[2025-09-10 21:35:42] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -78.540808-0.003355j
[2025-09-10 21:36:17] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -78.744354-0.002994j
[2025-09-10 21:36:52] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -78.738767-0.000757j
[2025-09-10 21:37:27] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -78.794132+0.000496j
[2025-09-10 21:38:02] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -78.815967+0.001543j
[2025-09-10 21:38:37] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -78.844549-0.000267j
[2025-09-10 21:39:12] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -78.714330+0.000693j
[2025-09-10 21:39:47] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -78.692777+0.002751j
[2025-09-10 21:40:21] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -78.805721-0.002424j
[2025-09-10 21:40:56] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -78.816648-0.004270j
[2025-09-10 21:41:31] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -78.822047+0.003823j
[2025-09-10 21:42:06] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -78.697739+0.003267j
[2025-09-10 21:42:41] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -78.762060+0.001295j
[2025-09-10 21:43:16] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -78.611804+0.001776j
[2025-09-10 21:43:51] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -78.712554-0.001240j
[2025-09-10 21:44:26] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -78.687925-0.001394j
[2025-09-10 21:45:01] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -78.727992-0.004733j
[2025-09-10 21:45:36] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -78.650050-0.001359j
[2025-09-10 21:46:11] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -78.684051+0.005372j
[2025-09-10 21:46:46] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -78.689428-0.001138j
[2025-09-10 21:47:21] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -78.895860-0.006283j
[2025-09-10 21:47:21] RESTART #2 | Period: 600
[2025-09-10 21:47:56] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -78.889132+0.006916j
[2025-09-10 21:48:32] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -78.812447+0.001134j
[2025-09-10 21:49:07] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -78.854194+0.000095j
[2025-09-10 21:49:42] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -78.816090+0.001334j
[2025-09-10 21:50:17] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -78.794340+0.002243j
[2025-09-10 21:50:53] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -78.910030+0.000395j
[2025-09-10 21:51:28] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -79.012725+0.000218j
[2025-09-10 21:52:03] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -78.837482-0.000844j
[2025-09-10 21:52:38] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -78.777019+0.000949j
[2025-09-10 21:53:13] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -78.804464-0.000622j
[2025-09-10 21:53:48] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -78.771755-0.000904j
[2025-09-10 21:54:23] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -78.758012-0.002364j
[2025-09-10 21:54:58] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -78.771910-0.003029j
[2025-09-10 21:55:33] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -78.804522-0.002774j
[2025-09-10 21:56:08] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -78.835292+0.000326j
[2025-09-10 21:56:44] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -78.789147+0.003845j
[2025-09-10 21:57:19] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -78.750693-0.000035j
[2025-09-10 21:57:54] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -78.710923+0.002371j
[2025-09-10 21:58:29] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -78.678604-0.002656j
[2025-09-10 21:59:04] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -78.790026+0.000307j
[2025-09-10 21:59:39] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -78.922545+0.001181j
[2025-09-10 22:00:14] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -78.846269+0.003651j
[2025-09-10 22:00:49] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -78.851997+0.007244j
[2025-09-10 22:01:24] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -78.653015+0.005276j
[2025-09-10 22:01:59] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -78.610665-0.000999j
[2025-09-10 22:02:35] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -78.766608-0.004347j
[2025-09-10 22:03:10] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -78.614476-0.001097j
[2025-09-10 22:03:45] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -78.573814-0.003334j
[2025-09-10 22:04:20] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -78.588339-0.002618j
[2025-09-10 22:04:54] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -78.606208-0.001595j
[2025-09-10 22:05:29] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -78.485112+0.001359j
[2025-09-10 22:06:04] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -78.711167-0.001424j
[2025-09-10 22:06:39] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -78.542623-0.000134j
[2025-09-10 22:07:14] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -78.685933-0.002060j
[2025-09-10 22:07:49] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -78.728048+0.000957j
[2025-09-10 22:08:25] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -78.737343+0.003790j
[2025-09-10 22:09:00] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -78.667735+0.002258j
[2025-09-10 22:09:35] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -78.664991+0.002797j
[2025-09-10 22:10:10] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -78.700744-0.001590j
[2025-09-10 22:10:46] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -78.772099+0.005725j
[2025-09-10 22:11:21] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -78.636292-0.000533j
[2025-09-10 22:11:56] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -78.691570+0.001169j
[2025-09-10 22:12:31] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -78.576219-0.001038j
[2025-09-10 22:13:07] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -78.775032-0.002411j
[2025-09-10 22:13:42] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -78.790418-0.001717j
[2025-09-10 22:14:17] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -78.870962+0.002129j
[2025-09-10 22:14:52] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -78.701370+0.003273j
[2025-09-10 22:15:28] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -78.622792+0.003104j
[2025-09-10 22:16:03] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -78.524332-0.002018j
[2025-09-10 22:16:38] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -78.748411-0.000938j
[2025-09-10 22:17:12] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -78.677994-0.001132j
[2025-09-10 22:17:47] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -78.680056-0.001960j
[2025-09-10 22:18:22] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -78.744358+0.004809j
[2025-09-10 22:18:57] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -78.794191+0.002141j
[2025-09-10 22:19:32] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -78.719367+0.001249j
[2025-09-10 22:20:07] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -78.741062+0.000208j
[2025-09-10 22:20:41] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -78.637986-0.005607j
[2025-09-10 22:21:16] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -78.658748+0.000873j
[2025-09-10 22:21:51] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -78.669205-0.002323j
[2025-09-10 22:22:26] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -78.577573+0.002898j
[2025-09-10 22:23:01] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -78.796098-0.001593j
[2025-09-10 22:23:36] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -78.756288+0.000336j
[2025-09-10 22:24:10] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -78.766372+0.001070j
[2025-09-10 22:24:45] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -78.774544-0.000224j
[2025-09-10 22:25:21] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -78.924223+0.004630j
[2025-09-10 22:25:56] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -78.765341+0.001233j
[2025-09-10 22:26:31] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -78.826800+0.011041j
[2025-09-10 22:27:06] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -78.759677-0.002842j
[2025-09-10 22:27:41] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -78.762285+0.005616j
[2025-09-10 22:28:16] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -78.875710-0.003936j
[2025-09-10 22:28:51] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -78.754975+0.002238j
[2025-09-10 22:29:25] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -78.759377-0.000346j
[2025-09-10 22:30:00] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -78.731574-0.005857j
[2025-09-10 22:30:35] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -78.660529-0.001789j
[2025-09-10 22:31:10] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -78.691055-0.000241j
[2025-09-10 22:31:10] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-10 22:31:45] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -78.706424+0.002484j
[2025-09-10 22:32:20] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -78.805765+0.002268j
[2025-09-10 22:32:56] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -78.732379-0.000005j
[2025-09-10 22:33:31] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -78.707039+0.001163j
[2025-09-10 22:34:06] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -78.777050-0.003316j
[2025-09-10 22:34:41] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -78.798198+0.001098j
[2025-09-10 22:35:16] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -78.684369-0.000609j
[2025-09-10 22:35:51] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -78.671896-0.002991j
[2025-09-10 22:36:26] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -78.695303-0.002073j
[2025-09-10 22:37:01] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -78.915560+0.001755j
[2025-09-10 22:37:36] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -78.920760-0.002990j
[2025-09-10 22:38:11] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -78.879525-0.002332j
[2025-09-10 22:38:46] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -78.764606+0.001339j
[2025-09-10 22:39:20] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -78.639201-0.000270j
[2025-09-10 22:39:55] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -78.683908+0.000065j
[2025-09-10 22:40:31] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -78.588256-0.002793j
[2025-09-10 22:41:06] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -78.623852+0.003288j
[2025-09-10 22:41:41] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -78.655751+0.001660j
[2025-09-10 22:42:16] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -78.672053+0.001311j
[2025-09-10 22:42:51] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -78.655040+0.003466j
[2025-09-10 22:43:26] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -78.623974+0.003585j
[2025-09-10 22:44:00] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -78.774062+0.002415j
[2025-09-10 22:44:36] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -78.807264-0.001404j
[2025-09-10 22:45:11] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -78.898685-0.004992j
[2025-09-10 22:45:46] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -78.868129-0.000780j
[2025-09-10 22:46:22] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -78.770248-0.006438j
[2025-09-10 22:46:57] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -78.687688+0.019039j
[2025-09-10 22:47:32] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -78.860249+0.000170j
[2025-09-10 22:48:06] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -78.786463-0.004537j
[2025-09-10 22:48:41] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -78.970223-0.000702j
[2025-09-10 22:49:16] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -78.913940-0.000352j
[2025-09-10 22:49:51] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -78.883931-0.000264j
[2025-09-10 22:50:26] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -78.808730-0.001522j
[2025-09-10 22:51:01] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -78.655259+0.000383j
[2025-09-10 22:51:37] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -78.649914+0.001005j
[2025-09-10 22:52:12] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -78.726706+0.001592j
[2025-09-10 22:52:47] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -78.776804+0.000530j
[2025-09-10 22:53:22] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -78.619914+0.007510j
[2025-09-10 22:53:57] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -78.781190+0.001685j
[2025-09-10 22:54:33] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -78.748957-0.000464j
[2025-09-10 22:55:08] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -78.806355-0.000243j
[2025-09-10 22:55:43] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -78.649762-0.000861j
[2025-09-10 22:56:18] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -78.688960+0.000337j
[2025-09-10 22:56:53] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -78.642835-0.006707j
[2025-09-10 22:57:28] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -78.642327-0.003341j
[2025-09-10 22:58:03] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -78.733920+0.003083j
[2025-09-10 22:58:38] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -78.698585-0.001536j
[2025-09-10 22:59:13] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -78.826812+0.001012j
[2025-09-10 22:59:48] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -78.780201-0.002334j
[2025-09-10 23:00:24] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -78.661530-0.000095j
[2025-09-10 23:00:59] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -78.754874-0.000958j
[2025-09-10 23:01:34] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -78.771483-0.007626j
[2025-09-10 23:02:09] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -78.820622+0.000011j
[2025-09-10 23:02:44] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -78.795960-0.001013j
[2025-09-10 23:03:18] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -78.763581+0.004784j
[2025-09-10 23:03:54] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -78.748827+0.002414j
[2025-09-10 23:04:29] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -78.788165+0.008864j
[2025-09-10 23:05:04] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -78.863751+0.002024j
[2025-09-10 23:05:39] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -78.814031+0.004485j
[2025-09-10 23:06:14] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -78.806760+0.001176j
[2025-09-10 23:06:50] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -78.799860+0.000414j
[2025-09-10 23:07:25] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -78.786395+0.002078j
[2025-09-10 23:08:00] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -78.599038+0.004150j
[2025-09-10 23:08:35] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -78.767385+0.004044j
[2025-09-10 23:09:11] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -78.847592+0.003271j
[2025-09-10 23:09:46] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -78.619442-0.000886j
[2025-09-10 23:10:21] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -78.651091-0.003487j
[2025-09-10 23:10:56] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -78.735277-0.002056j
[2025-09-10 23:11:31] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -78.798505-0.001597j
[2025-09-10 23:12:06] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -78.689686+0.004928j
[2025-09-10 23:12:41] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -78.636582+0.002693j
[2025-09-10 23:13:16] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -78.675764-0.004713j
[2025-09-10 23:13:50] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -78.642657+0.002829j
[2025-09-10 23:14:25] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -78.697651-0.002393j
[2025-09-10 23:15:00] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -78.804874+0.005535j
[2025-09-10 23:15:36] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -78.616165+0.001961j
[2025-09-10 23:16:11] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -78.564077-0.001920j
[2025-09-10 23:16:46] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -78.648633+0.000214j
[2025-09-10 23:17:21] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -78.586930-0.000044j
[2025-09-10 23:17:56] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -78.501456+0.000862j
[2025-09-10 23:18:32] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -78.714054+0.000728j
[2025-09-10 23:19:07] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -78.552064-0.001173j
[2025-09-10 23:19:42] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -78.645858-0.005125j
[2025-09-10 23:20:17] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -78.642170+0.001651j
[2025-09-10 23:20:52] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -78.537424+0.001407j
[2025-09-10 23:21:27] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -78.736812+0.000471j
[2025-09-10 23:22:02] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -78.724820+0.001317j
[2025-09-10 23:22:37] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -78.849453+0.002975j
[2025-09-10 23:23:12] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -78.758561+0.006876j
[2025-09-10 23:23:47] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -78.790725-0.002110j
[2025-09-10 23:24:22] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -78.745217-0.002251j
[2025-09-10 23:24:57] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -78.848385-0.003037j
[2025-09-10 23:25:32] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -78.796021-0.000058j
[2025-09-10 23:26:07] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -78.666872+0.000865j
[2025-09-10 23:26:42] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -78.567414-0.001087j
[2025-09-10 23:27:17] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -78.601332-0.001372j
[2025-09-10 23:27:52] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -78.551953+0.002962j
[2025-09-10 23:28:27] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -78.638018-0.000507j
[2025-09-10 23:29:02] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -78.594409-0.002811j
[2025-09-10 23:29:38] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -78.583075+0.000320j
[2025-09-10 23:30:12] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -78.656092-0.005488j
[2025-09-10 23:30:47] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -78.600964-0.000190j
[2025-09-10 23:31:22] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -78.661629+0.000506j
[2025-09-10 23:31:57] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -78.795033-0.001219j
[2025-09-10 23:32:32] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -78.684749-0.000886j
[2025-09-10 23:32:32] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-10 23:33:08] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -78.796078+0.002622j
[2025-09-10 23:33:43] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -78.837261-0.000473j
[2025-09-10 23:34:18] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -78.867627+0.001563j
[2025-09-10 23:34:53] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -78.795185+0.002050j
[2025-09-10 23:35:28] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -78.586767+0.002807j
[2025-09-10 23:36:04] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -78.606086-0.004564j
[2025-09-10 23:36:39] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -78.563523+0.004170j
[2025-09-10 23:37:14] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -78.642873+0.003424j
[2025-09-10 23:37:49] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -78.623353-0.000775j
[2025-09-10 23:38:24] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -78.607587+0.000516j
[2025-09-10 23:38:59] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -78.739107-0.001684j
[2025-09-10 23:39:34] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -78.632407+0.001531j
[2025-09-10 23:40:09] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -78.652959+0.000264j
[2025-09-10 23:40:44] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -78.792359+0.001014j
[2025-09-10 23:41:19] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -78.835156+0.009098j
[2025-09-10 23:41:54] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -78.934255+0.003126j
[2025-09-10 23:42:30] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -78.766793+0.007012j
[2025-09-10 23:43:05] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -78.837615+0.001874j
[2025-09-10 23:43:40] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -78.849315+0.003887j
[2025-09-10 23:44:15] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -78.812019+0.002418j
[2025-09-10 23:44:50] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -78.932798-0.002955j
[2025-09-10 23:45:25] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -78.871096-0.003109j
[2025-09-10 23:46:00] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -78.830282+0.001897j
[2025-09-10 23:46:28] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -78.934459-0.001261j
[2025-09-10 23:46:51] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -78.814071-0.000381j
[2025-09-10 23:47:14] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -78.784352+0.001382j
[2025-09-10 23:47:38] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -78.788975+0.002981j
[2025-09-10 23:48:01] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -78.769448-0.001348j
[2025-09-10 23:48:25] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -78.813025+0.001172j
[2025-09-10 23:48:48] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -78.791968-0.003101j
[2025-09-10 23:49:11] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -78.815836+0.000971j
[2025-09-10 23:49:35] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -78.705273+0.001927j
[2025-09-10 23:49:58] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -78.593586-0.000802j
[2025-09-10 23:50:22] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -78.748238+0.001118j
[2025-09-10 23:50:45] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -78.906329+0.002533j
[2025-09-10 23:51:08] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -78.881601+0.000094j
[2025-09-10 23:51:32] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -78.892834+0.000671j
[2025-09-10 23:51:55] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -78.928742+0.000445j
[2025-09-10 23:52:19] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -78.988473-0.001196j
[2025-09-10 23:52:42] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -78.861020+0.000140j
[2025-09-10 23:53:05] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -78.902938+0.001062j
[2025-09-10 23:53:29] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -78.932951+0.001629j
[2025-09-10 23:53:52] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -78.820971-0.004058j
[2025-09-10 23:54:15] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -78.718697-0.001738j
[2025-09-10 23:54:38] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -78.699922+0.000640j
[2025-09-10 23:55:02] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -78.725558-0.001629j
[2025-09-10 23:55:25] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -78.638088+0.001541j
[2025-09-10 23:55:48] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -78.868857+0.002119j
[2025-09-10 23:56:12] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -78.719930+0.002182j
[2025-09-10 23:56:35] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -78.726103-0.007501j
[2025-09-10 23:56:59] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -78.749580-0.002597j
[2025-09-10 23:57:22] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -78.723031+0.004225j
[2025-09-10 23:57:45] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -78.689374+0.003299j
[2025-09-10 23:58:09] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -78.695072-0.001588j
[2025-09-10 23:58:32] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -78.625288-0.001505j
[2025-09-10 23:58:56] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -78.690200-0.004095j
[2025-09-10 23:59:19] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -78.614690-0.000011j
[2025-09-10 23:59:42] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -78.580000-0.003601j
[2025-09-11 00:00:06] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -78.469109-0.000612j
[2025-09-11 00:00:29] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -78.585665+0.000908j
[2025-09-11 00:00:53] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -78.694394+0.000295j
[2025-09-11 00:01:16] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -78.670429+0.001114j
[2025-09-11 00:01:40] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -78.571486+0.001872j
[2025-09-11 00:02:03] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -78.523941-0.002467j
[2025-09-11 00:02:26] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -78.646078-0.002622j
[2025-09-11 00:02:50] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -78.517538+0.002291j
[2025-09-11 00:03:13] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -78.567605-0.000095j
[2025-09-11 00:03:37] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -78.537621+0.009101j
[2025-09-11 00:04:00] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -78.691913+0.004383j
[2025-09-11 00:04:23] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -78.641986-0.000248j
[2025-09-11 00:04:47] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -78.517250+0.000678j
[2025-09-11 00:05:10] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -78.588122+0.001004j
[2025-09-11 00:05:34] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -78.501785-0.002564j
[2025-09-11 00:05:57] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -78.585306+0.003899j
[2025-09-11 00:06:20] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -78.607004+0.002699j
[2025-09-11 00:06:44] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -78.612439+0.003369j
[2025-09-11 00:07:07] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -78.590441-0.000906j
[2025-09-11 00:07:31] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -78.572231-0.003483j
[2025-09-11 00:07:54] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -78.638241+0.002359j
[2025-09-11 00:08:18] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -78.687158-0.003177j
[2025-09-11 00:08:41] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -78.659432-0.000565j
[2025-09-11 00:09:04] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -78.657335-0.004541j
[2025-09-11 00:09:28] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -78.743490+0.004774j
[2025-09-11 00:09:51] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -78.685335-0.003125j
[2025-09-11 00:10:14] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -78.701594+0.001304j
[2025-09-11 00:10:38] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -78.625547-0.001483j
[2025-09-11 00:11:01] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -78.520659-0.002319j
[2025-09-11 00:11:25] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -78.522563-0.000303j
[2025-09-11 00:11:48] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -78.552828-0.003269j
[2025-09-11 00:12:12] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -78.535454+0.002475j
[2025-09-11 00:12:35] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -78.464127+0.004538j
[2025-09-11 00:12:58] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -78.448039+0.000962j
[2025-09-11 00:13:22] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -78.522162+0.002643j
[2025-09-11 00:13:45] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -78.587301-0.002623j
[2025-09-11 00:14:09] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -78.501507-0.001995j
[2025-09-11 00:14:32] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -78.518020-0.001047j
[2025-09-11 00:14:56] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -78.623942+0.004223j
[2025-09-11 00:15:19] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -78.704454-0.001471j
[2025-09-11 00:15:42] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -78.621991-0.002119j
[2025-09-11 00:16:06] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -78.760938-0.004136j
[2025-09-11 00:16:29] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -78.757710-0.001314j
[2025-09-11 00:16:53] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -78.806446+0.009197j
[2025-09-11 00:17:16] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -78.740359+0.004949j
[2025-09-11 00:17:39] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -78.696264-0.001212j
[2025-09-11 00:18:03] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -78.734650-0.001149j
[2025-09-11 00:18:03] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-11 00:18:26] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -78.719480-0.003940j
[2025-09-11 00:18:49] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -78.666899-0.001368j
[2025-09-11 00:19:13] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -78.665257+0.002484j
[2025-09-11 00:19:36] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -78.708139-0.004481j
[2025-09-11 00:20:00] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -78.374080-0.000207j
[2025-09-11 00:20:23] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -78.560968-0.000985j
[2025-09-11 00:20:47] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -78.617870-0.002244j
[2025-09-11 00:21:10] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -78.542056-0.000321j
[2025-09-11 00:21:33] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -78.532866-0.001037j
[2025-09-11 00:21:57] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -78.518406-0.003964j
[2025-09-11 00:22:20] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -78.563925+0.003481j
[2025-09-11 00:22:43] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -78.632400-0.001203j
[2025-09-11 00:23:07] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -78.679176+0.000842j
[2025-09-11 00:23:30] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -78.592500+0.002022j
[2025-09-11 00:23:54] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -78.792280+0.002611j
[2025-09-11 00:24:17] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -78.685861-0.001621j
[2025-09-11 00:24:41] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -78.735675+0.000992j
[2025-09-11 00:25:04] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -78.781616+0.003819j
[2025-09-11 00:25:27] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -78.726650-0.001257j
[2025-09-11 00:25:51] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -78.624278+0.003254j
[2025-09-11 00:26:14] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -78.605427-0.003485j
[2025-09-11 00:26:38] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -78.534668+0.001005j
[2025-09-11 00:27:01] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -78.513829-0.000661j
[2025-09-11 00:27:24] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -78.517748-0.003978j
[2025-09-11 00:27:48] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -78.657093+0.001596j
[2025-09-11 00:28:11] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -78.502845+0.000513j
[2025-09-11 00:28:35] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -78.554262-0.002742j
[2025-09-11 00:28:58] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -78.611320+0.004463j
[2025-09-11 00:29:21] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -78.519729-0.000006j
[2025-09-11 00:29:45] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -78.559864-0.002367j
[2025-09-11 00:30:08] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -78.625466+0.000640j
[2025-09-11 00:30:32] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -78.643600-0.000526j
[2025-09-11 00:30:55] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -78.615072+0.000245j
[2025-09-11 00:31:19] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -78.611784-0.000496j
[2025-09-11 00:31:42] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -78.595368-0.002893j
[2025-09-11 00:32:05] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -78.582859+0.003822j
[2025-09-11 00:32:29] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -78.758658-0.003687j
[2025-09-11 00:32:52] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -78.659267+0.003090j
[2025-09-11 00:33:16] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -78.629523-0.000475j
[2025-09-11 00:33:39] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -78.607453-0.002666j
[2025-09-11 00:34:02] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -78.765804-0.003503j
[2025-09-11 00:34:26] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -78.670441-0.003934j
[2025-09-11 00:34:49] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -78.558999+0.001851j
[2025-09-11 00:35:13] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -78.668212-0.003943j
[2025-09-11 00:35:36] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -78.675654+0.000447j
[2025-09-11 00:35:59] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -78.672395+0.000779j
[2025-09-11 00:36:23] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -78.689324+0.003269j
[2025-09-11 00:36:46] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -78.745247-0.001098j
[2025-09-11 00:37:10] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -78.919083-0.000844j
[2025-09-11 00:37:33] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -78.880496+0.003792j
[2025-09-11 00:37:56] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -78.771587-0.002941j
[2025-09-11 00:38:20] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -78.715229+0.001114j
[2025-09-11 00:38:43] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -78.678024-0.002185j
[2025-09-11 00:39:07] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -78.764129-0.002360j
[2025-09-11 00:39:30] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -78.808150-0.000085j
[2025-09-11 00:39:53] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -78.872576+0.004910j
[2025-09-11 00:40:17] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -78.791633+0.000384j
[2025-09-11 00:40:40] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -78.629740-0.002292j
[2025-09-11 00:41:04] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -78.659762-0.005628j
[2025-09-11 00:41:27] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -78.720774-0.004486j
[2025-09-11 00:41:51] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -78.751074-0.002930j
[2025-09-11 00:42:14] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -78.765276-0.000372j
[2025-09-11 00:42:37] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -78.789831+0.000005j
[2025-09-11 00:43:01] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -78.657529+0.000550j
[2025-09-11 00:43:24] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -78.567100-0.001520j
[2025-09-11 00:43:48] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -78.536893-0.000890j
[2025-09-11 00:44:11] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -78.485795+0.001708j
[2025-09-11 00:44:34] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -78.506544-0.000227j
[2025-09-11 00:44:58] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -78.559488+0.000099j
[2025-09-11 00:45:21] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -78.683721+0.001094j
[2025-09-11 00:45:45] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -78.754282+0.002496j
[2025-09-11 00:46:08] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -78.756645+0.000120j
[2025-09-11 00:46:31] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -78.819189+0.000517j
[2025-09-11 00:46:55] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -78.883858-0.000613j
[2025-09-11 00:47:18] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -78.849762-0.008638j
[2025-09-11 00:47:42] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -78.900788+0.003342j
[2025-09-11 00:48:05] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -78.911913+0.000773j
[2025-09-11 00:48:29] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -78.755343+0.000728j
[2025-09-11 00:48:52] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -78.880740-0.000637j
[2025-09-11 00:49:15] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -78.883115+0.003784j
[2025-09-11 00:49:39] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -78.803327+0.003587j
[2025-09-11 00:50:02] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -78.763735+0.004098j
[2025-09-11 00:50:25] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -78.748518+0.004125j
[2025-09-11 00:50:48] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -78.675058+0.008885j
[2025-09-11 00:51:11] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -78.730308-0.002946j
[2025-09-11 00:51:35] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -78.659026-0.001612j
[2025-09-11 00:51:58] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -78.721102-0.001906j
[2025-09-11 00:52:22] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -78.771284-0.001585j
[2025-09-11 00:52:45] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -78.866208+0.000271j
[2025-09-11 00:53:08] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -78.826745-0.002637j
[2025-09-11 00:53:32] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -78.794355-0.001600j
[2025-09-11 00:53:55] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -78.798075-0.003051j
[2025-09-11 00:54:19] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -78.647735+0.001264j
[2025-09-11 00:54:42] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -78.784103+0.003706j
[2025-09-11 00:55:06] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -78.682745+0.001941j
[2025-09-11 00:55:29] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -78.571916-0.001150j
[2025-09-11 00:55:53] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -78.633452+0.000457j
[2025-09-11 00:56:16] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -78.614236-0.000315j
[2025-09-11 00:56:39] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -78.697745-0.003137j
[2025-09-11 00:57:03] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -78.735991+0.006419j
[2025-09-11 00:57:26] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -78.593810+0.001216j
[2025-09-11 00:57:50] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -78.796624+0.001220j
[2025-09-11 00:58:13] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -78.867960-0.000862j
[2025-09-11 00:58:37] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -78.837250-0.003017j
[2025-09-11 00:59:00] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -78.698947+0.002894j
[2025-09-11 00:59:00] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-11 00:59:23] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -78.526902+0.000678j
[2025-09-11 00:59:47] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -78.493322-0.002545j
[2025-09-11 01:00:10] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -78.522599-0.003548j
[2025-09-11 01:00:34] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -78.559072-0.000151j
[2025-09-11 01:00:57] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -78.590629+0.002380j
[2025-09-11 01:01:21] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -78.584316+0.003925j
[2025-09-11 01:01:44] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -78.757434-0.005801j
[2025-09-11 01:02:07] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -78.678974+0.000036j
[2025-09-11 01:02:31] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -78.524993+0.001464j
[2025-09-11 01:02:54] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -78.702683+0.001729j
[2025-09-11 01:03:18] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -78.622153+0.004271j
[2025-09-11 01:03:41] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -78.607319-0.004116j
[2025-09-11 01:04:05] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -78.676864+0.000071j
[2025-09-11 01:04:28] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -78.813041-0.001218j
[2025-09-11 01:04:51] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -78.855244+0.007573j
[2025-09-11 01:05:15] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -78.798758-0.000348j
[2025-09-11 01:05:38] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -78.678215+0.000034j
[2025-09-11 01:06:02] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -78.630702+0.000261j
[2025-09-11 01:06:25] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -78.498729+0.001368j
[2025-09-11 01:06:48] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -78.384336+0.005038j
[2025-09-11 01:07:12] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -78.561176-0.000737j
[2025-09-11 01:07:35] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -78.625697-0.002718j
[2025-09-11 01:07:59] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -78.544551+0.001725j
[2025-09-11 01:08:22] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -78.642141+0.000046j
[2025-09-11 01:08:45] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -78.618837-0.001555j
[2025-09-11 01:09:09] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -78.669061+0.004078j
[2025-09-11 01:09:32] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -78.713742-0.000672j
[2025-09-11 01:09:56] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -78.651793+0.002241j
[2025-09-11 01:10:19] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -78.606172-0.000772j
[2025-09-11 01:10:43] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -78.745655+0.002128j
[2025-09-11 01:11:06] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -78.623779-0.003892j
[2025-09-11 01:11:29] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -78.620233-0.003578j
[2025-09-11 01:11:53] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -78.605460+0.002131j
[2025-09-11 01:12:16] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -78.739553+0.004008j
[2025-09-11 01:12:40] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -78.604366-0.000998j
[2025-09-11 01:13:03] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -78.769746-0.000157j
[2025-09-11 01:13:27] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -78.590512+0.001369j
[2025-09-11 01:13:50] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -78.734896+0.004076j
[2025-09-11 01:14:13] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -78.736021-0.001546j
[2025-09-11 01:14:37] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -78.721202+0.000786j
[2025-09-11 01:15:00] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -78.734715+0.000965j
[2025-09-11 01:15:24] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -78.715467+0.002048j
[2025-09-11 01:15:47] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -78.702372-0.003288j
[2025-09-11 01:16:10] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -78.793980-0.000769j
[2025-09-11 01:16:34] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -78.779707-0.002422j
[2025-09-11 01:16:57] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -78.694646+0.002465j
[2025-09-11 01:17:21] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -78.602783+0.004798j
[2025-09-11 01:17:44] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -78.743699-0.004957j
[2025-09-11 01:18:08] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -78.813944-0.004213j
[2025-09-11 01:18:31] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -78.983233-0.004259j
[2025-09-11 01:18:54] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -79.005938+0.001282j
[2025-09-11 01:19:18] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -78.926520-0.000956j
[2025-09-11 01:19:41] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -78.862939+0.000592j
[2025-09-11 01:20:05] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -78.862577-0.000234j
[2025-09-11 01:20:28] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -78.876403+0.003639j
[2025-09-11 01:20:51] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -78.787690-0.005175j
[2025-09-11 01:21:15] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -78.887643-0.004554j
[2025-09-11 01:21:38] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -78.730037-0.000847j
[2025-09-11 01:22:02] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -78.624411-0.002120j
[2025-09-11 01:22:25] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -78.779608+0.003070j
[2025-09-11 01:22:49] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -78.708673-0.000528j
[2025-09-11 01:23:12] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -78.693308-0.002901j
[2025-09-11 01:23:35] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -78.742411+0.000696j
[2025-09-11 01:23:59] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -78.537204-0.003216j
[2025-09-11 01:24:22] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -78.622263+0.001210j
[2025-09-11 01:24:46] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -78.653214+0.000200j
[2025-09-11 01:25:09] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -78.639551+0.000173j
[2025-09-11 01:25:33] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -78.781773+0.000585j
[2025-09-11 01:25:56] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -78.733501+0.000584j
[2025-09-11 01:26:19] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -78.500906-0.001742j
[2025-09-11 01:26:43] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -78.644411+0.003688j
[2025-09-11 01:27:06] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -78.580152+0.002308j
[2025-09-11 01:27:30] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -78.729696-0.002030j
[2025-09-11 01:27:53] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -78.741021-0.000432j
[2025-09-11 01:28:17] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -78.719878-0.000434j
[2025-09-11 01:28:40] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -78.810386+0.000817j
[2025-09-11 01:29:03] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -78.677701-0.001387j
[2025-09-11 01:29:27] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -78.721403-0.000841j
[2025-09-11 01:29:50] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -78.843242+0.000302j
[2025-09-11 01:30:14] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -78.748653+0.003391j
[2025-09-11 01:30:37] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -78.669375+0.000841j
[2025-09-11 01:31:00] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -78.659175-0.001503j
[2025-09-11 01:31:24] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -78.632869+0.000854j
[2025-09-11 01:31:47] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -78.574125-0.003001j
[2025-09-11 01:32:11] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -78.690685-0.002583j
[2025-09-11 01:32:34] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -78.660068-0.000950j
[2025-09-11 01:32:58] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -78.731288-0.003610j
[2025-09-11 01:33:21] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -78.712505-0.001613j
[2025-09-11 01:33:44] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -78.628550+0.003814j
[2025-09-11 01:34:08] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -78.592926-0.002981j
[2025-09-11 01:34:31] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -78.650248+0.000430j
[2025-09-11 01:34:54] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -78.656215-0.002903j
[2025-09-11 01:35:18] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -78.837908+0.001162j
[2025-09-11 01:35:41] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -78.630142+0.000708j
[2025-09-11 01:36:04] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -78.659028-0.002371j
[2025-09-11 01:36:28] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -78.649377-0.000722j
[2025-09-11 01:36:51] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -78.720212-0.001586j
[2025-09-11 01:37:14] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -78.851634-0.000370j
[2025-09-11 01:37:38] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -78.738016-0.000687j
[2025-09-11 01:38:01] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -78.582919+0.001009j
[2025-09-11 01:38:25] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -78.595960+0.004324j
[2025-09-11 01:38:48] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -78.686249+0.001303j
[2025-09-11 01:39:12] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -78.672038-0.000629j
[2025-09-11 01:39:35] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -78.642897-0.001885j
[2025-09-11 01:39:58] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -78.718214+0.002368j
[2025-09-11 01:39:58] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-11 01:40:22] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -78.642941-0.000124j
[2025-09-11 01:40:45] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -78.784133-0.000255j
[2025-09-11 01:41:09] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -78.681301-0.002434j
[2025-09-11 01:41:32] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -78.621977-0.003227j
[2025-09-11 01:41:56] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -78.559115-0.001769j
[2025-09-11 01:42:19] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -78.579181+0.001238j
[2025-09-11 01:42:42] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -78.645520-0.001241j
[2025-09-11 01:43:06] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -78.708633-0.004349j
[2025-09-11 01:43:29] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -78.679056-0.002648j
[2025-09-11 01:43:53] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -78.528162+0.000765j
[2025-09-11 01:44:16] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -78.710949-0.002522j
[2025-09-11 01:44:40] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -78.732611+0.003708j
[2025-09-11 01:45:03] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -78.683022-0.002457j
[2025-09-11 01:45:26] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -78.945804+0.001201j
[2025-09-11 01:45:50] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -78.846988+0.002647j
[2025-09-11 01:46:13] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -78.826820-0.000047j
[2025-09-11 01:46:37] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -78.763755+0.001719j
[2025-09-11 01:47:00] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -78.641027+0.003647j
[2025-09-11 01:47:24] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -78.624876+0.002372j
[2025-09-11 01:47:47] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -78.834125+0.000033j
[2025-09-11 01:48:11] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -78.633897+0.000174j
[2025-09-11 01:48:34] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -78.812588+0.004840j
[2025-09-11 01:48:57] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -78.808739-0.001345j
[2025-09-11 01:49:21] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -78.859276+0.004771j
[2025-09-11 01:49:44] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -78.827005+0.004420j
[2025-09-11 01:50:08] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -78.875972+0.003283j
[2025-09-11 01:50:31] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -78.857872-0.001438j
[2025-09-11 01:50:54] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -78.828626-0.000171j
[2025-09-11 01:51:18] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -78.861538-0.000952j
[2025-09-11 01:51:41] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -78.653196-0.003233j
[2025-09-11 01:52:05] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -78.835342-0.005082j
[2025-09-11 01:52:28] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -78.988302+0.002363j
[2025-09-11 01:52:51] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -78.864387-0.003423j
[2025-09-11 01:53:15] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -78.834547-0.001843j
[2025-09-11 01:53:38] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -78.834648+0.002566j
[2025-09-11 01:54:02] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -78.925575+0.000526j
[2025-09-11 01:54:25] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -78.784638-0.002478j
[2025-09-11 01:54:49] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -78.736412+0.003258j
[2025-09-11 01:55:12] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -78.662129-0.002450j
[2025-09-11 01:55:35] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -78.852008-0.000443j
[2025-09-11 01:55:59] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -78.783227+0.002138j
[2025-09-11 01:56:22] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -78.777499-0.001302j
[2025-09-11 01:56:46] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -78.738504+0.001819j
[2025-09-11 01:57:09] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -78.863611-0.006073j
[2025-09-11 01:57:32] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -78.758123-0.002576j
[2025-09-11 01:57:56] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -78.636694+0.002097j
[2025-09-11 01:58:19] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -78.580937-0.000750j
[2025-09-11 01:58:43] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -78.792283-0.002368j
[2025-09-11 01:59:06] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -78.679224-0.002790j
[2025-09-11 01:59:30] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -78.838061+0.000180j
[2025-09-11 01:59:53] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -78.701850+0.000722j
[2025-09-11 02:00:16] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -78.763086-0.000157j
[2025-09-11 02:00:40] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -78.810370+0.002559j
[2025-09-11 02:01:03] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -78.650907+0.006071j
[2025-09-11 02:01:27] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -78.773486-0.000096j
[2025-09-11 02:01:50] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -78.599416+0.002773j
[2025-09-11 02:02:13] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -78.733245-0.000172j
[2025-09-11 02:02:36] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -78.573317+0.001283j
[2025-09-11 02:03:00] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -78.530261-0.002684j
[2025-09-11 02:03:23] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -78.557410-0.002044j
[2025-09-11 02:03:46] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -78.600163+0.001912j
[2025-09-11 02:04:10] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -78.560333-0.000304j
[2025-09-11 02:04:33] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -78.633851-0.002326j
[2025-09-11 02:04:57] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -78.723038-0.001864j
[2025-09-11 02:05:20] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -78.638897-0.001986j
[2025-09-11 02:05:44] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -78.604506-0.001978j
[2025-09-11 02:06:07] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -78.539103+0.001930j
[2025-09-11 02:06:30] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -78.682069-0.003187j
[2025-09-11 02:06:54] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -78.640719-0.000717j
[2025-09-11 02:07:17] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -78.593996-0.003624j
[2025-09-11 02:07:41] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -78.578583+0.004317j
[2025-09-11 02:08:04] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -78.657283-0.000475j
[2025-09-11 02:08:27] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -78.625707+0.003664j
[2025-09-11 02:08:51] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -78.739181-0.000602j
[2025-09-11 02:09:14] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -78.674996-0.000357j
[2025-09-11 02:09:38] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -78.736829-0.000366j
[2025-09-11 02:10:01] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -78.714141+0.000075j
[2025-09-11 02:10:25] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -78.610972+0.002215j
[2025-09-11 02:10:48] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -78.758864-0.000019j
[2025-09-11 02:11:11] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -78.602061-0.003337j
[2025-09-11 02:11:35] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -78.812616+0.002498j
[2025-09-11 02:11:58] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -78.729721+0.003217j
[2025-09-11 02:12:21] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -78.671872+0.002574j
[2025-09-11 02:12:44] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -78.463839+0.001197j
[2025-09-11 02:13:08] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -78.530410+0.000272j
[2025-09-11 02:13:31] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -78.720777-0.000273j
[2025-09-11 02:13:54] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -78.760858-0.004217j
[2025-09-11 02:14:18] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -78.775705-0.001562j
[2025-09-11 02:14:41] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -78.621969+0.000177j
[2025-09-11 02:15:05] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -78.749552-0.000721j
[2025-09-11 02:15:28] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -78.815738-0.000219j
[2025-09-11 02:15:51] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -78.920397+0.000797j
[2025-09-11 02:16:15] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -78.790859-0.000209j
[2025-09-11 02:16:38] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -78.662598+0.003095j
[2025-09-11 02:17:02] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -78.708570+0.002761j
[2025-09-11 02:17:25] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -78.745200-0.002245j
[2025-09-11 02:17:49] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -78.777570-0.001375j
[2025-09-11 02:18:12] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -78.787357+0.001897j
[2025-09-11 02:18:36] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -78.669339-0.001234j
[2025-09-11 02:18:59] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -78.616928+0.003977j
[2025-09-11 02:19:22] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -78.680974-0.001271j
[2025-09-11 02:19:46] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -78.588569+0.001702j
[2025-09-11 02:20:09] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -78.661306+0.000137j
[2025-09-11 02:20:33] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -78.547151-0.000221j
[2025-09-11 02:20:56] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -78.574900+0.001096j
[2025-09-11 02:20:56] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-11 02:20:56] ✅ Training completed | Restarts: 2
[2025-09-11 02:20:56] ============================================================
[2025-09-11 02:20:56] Training completed | Runtime: 32367.3s
[2025-09-11 02:21:06] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-11 02:21:06] ============================================================
