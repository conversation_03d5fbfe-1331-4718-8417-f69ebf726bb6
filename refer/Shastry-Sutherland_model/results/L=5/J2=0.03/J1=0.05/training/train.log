[2025-09-12 16:41:54] ==================================================
[2025-09-12 16:41:54] GCNN for Shastry-Sutherland Model
[2025-09-12 16:41:54] ==================================================
[2025-09-12 16:41:54] System parameters:
[2025-09-12 16:41:54]   - System size: L=5, N=100
[2025-09-12 16:41:54]   - System parameters: J1=0.05, J2=0.03, Q=0.97
[2025-09-12 16:41:54] --------------------------------------------------
[2025-09-12 16:41:54] Model parameters:
[2025-09-12 16:41:54]   - Number of layers = 4
[2025-09-12 16:41:54]   - Number of features = 4
[2025-09-12 16:41:54]   - Total parameters = 19628
[2025-09-12 16:41:54] --------------------------------------------------
[2025-09-12 16:41:54] Training parameters:
[2025-09-12 16:41:54]   - Learning rate: 0.015
[2025-09-12 16:41:54]   - Total iterations: 2250
[2025-09-12 16:41:54]   - Annealing cycles: 4
[2025-09-12 16:41:54]   - Initial period: 150
[2025-09-12 16:41:54]   - Period multiplier: 2.0
[2025-09-12 16:41:54]   - Temperature range: 0.0-1.0
[2025-09-12 16:41:54]   - Samples: 16384
[2025-09-12 16:41:54]   - Discarded samples: 0
[2025-09-12 16:41:54]   - Chunk size: 2048
[2025-09-12 16:41:54]   - Diagonal shift: 0.2
[2025-09-12 16:41:54]   - Gradient clipping: 1.0
[2025-09-12 16:41:54]   - Checkpoint enabled: interval=250
[2025-09-12 16:41:54]   - Checkpoint directory: results/L=5/J2=0.03/J1=0.05/training/checkpoints
[2025-09-12 16:41:54] --------------------------------------------------
[2025-09-12 16:41:54] Device status:
[2025-09-12 16:41:54]   - Devices model: NVIDIA H200 NVL
[2025-09-12 16:41:54]   - Number of devices: 1
[2025-09-12 16:41:54]   - Sharding: True
[2025-09-12 16:41:54] ============================================================
[2025-09-12 16:44:36] [Iter 1/2250] R0[0/150], Temp: 1.0000, Energy: 2.874249+0.000120j
[2025-09-12 16:46:18] [Iter 2/2250] R0[1/150], Temp: 0.9999, Energy: 2.874476-0.000043j
[2025-09-12 16:48:00] [Iter 3/2250] R0[2/150], Temp: 0.9996, Energy: 2.874440-0.000095j
[2025-09-12 16:49:41] [Iter 4/2250] R0[3/150], Temp: 0.9990, Energy: 2.874217-0.000092j
[2025-09-12 16:51:23] [Iter 5/2250] R0[4/150], Temp: 0.9982, Energy: 2.874293-0.000330j
[2025-09-12 16:53:05] [Iter 6/2250] R0[5/150], Temp: 0.9973, Energy: 2.874070-0.000012j
[2025-09-12 16:54:23] [Iter 7/2250] R0[6/150], Temp: 0.9961, Energy: 2.874212+0.000172j
[2025-09-12 16:55:47] [Iter 8/2250] R0[7/150], Temp: 0.9946, Energy: 2.873965-0.000202j
[2025-09-12 16:57:30] [Iter 9/2250] R0[8/150], Temp: 0.9930, Energy: 2.874302-0.000074j
[2025-09-12 16:58:51] [Iter 10/2250] R0[9/150], Temp: 0.9911, Energy: 2.874250-0.000018j
[2025-09-12 17:00:32] [Iter 11/2250] R0[10/150], Temp: 0.9891, Energy: 2.874079+0.000162j
[2025-09-12 17:02:13] [Iter 12/2250] R0[11/150], Temp: 0.9868, Energy: 2.874351+0.000289j
[2025-09-12 17:03:55] [Iter 13/2250] R0[12/150], Temp: 0.9843, Energy: 2.874293-0.000195j
[2025-09-12 17:05:37] [Iter 14/2250] R0[13/150], Temp: 0.9816, Energy: 2.874000-0.000098j
[2025-09-12 17:07:18] [Iter 15/2250] R0[14/150], Temp: 0.9787, Energy: 2.873955-0.000472j
[2025-09-12 17:08:20] [Iter 16/2250] R0[15/150], Temp: 0.9755, Energy: 2.874313+0.000071j
[2025-09-12 17:09:53] [Iter 17/2250] R0[16/150], Temp: 0.9722, Energy: 2.873995+0.000134j
[2025-09-12 17:11:29] [Iter 18/2250] R0[17/150], Temp: 0.9686, Energy: 2.874202+0.000173j
[2025-09-12 17:12:56] [Iter 19/2250] R0[18/150], Temp: 0.9649, Energy: 2.874037-0.000168j
[2025-09-12 17:14:38] [Iter 20/2250] R0[19/150], Temp: 0.9609, Energy: 2.874130+0.000062j
[2025-09-12 17:16:19] [Iter 21/2250] R0[20/150], Temp: 0.9568, Energy: 2.874146-0.000148j
[2025-09-12 17:18:00] [Iter 22/2250] R0[21/150], Temp: 0.9524, Energy: 2.874145-0.000259j
[2025-09-12 17:19:41] [Iter 23/2250] R0[22/150], Temp: 0.9479, Energy: 2.873700-0.000131j
[2025-09-12 17:21:22] [Iter 24/2250] R0[23/150], Temp: 0.9431, Energy: 2.873493+0.000075j
[2025-09-12 17:22:20] [Iter 25/2250] R0[24/150], Temp: 0.9382, Energy: 2.874109+0.000145j
[2025-09-12 17:24:02] [Iter 26/2250] R0[25/150], Temp: 0.9330, Energy: 2.873539+0.000052j
[2025-09-12 17:25:28] [Iter 27/2250] R0[26/150], Temp: 0.9277, Energy: 2.873857-0.000055j
[2025-09-12 17:27:05] [Iter 28/2250] R0[27/150], Temp: 0.9222, Energy: 2.873594-0.000046j
[2025-09-12 17:28:47] [Iter 29/2250] R0[28/150], Temp: 0.9165, Energy: 2.874061-0.000085j
[2025-09-12 17:30:29] [Iter 30/2250] R0[29/150], Temp: 0.9106, Energy: 2.873859+0.000070j
[2025-09-12 17:32:11] [Iter 31/2250] R0[30/150], Temp: 0.9045, Energy: 2.873408-0.000276j
[2025-09-12 17:33:53] [Iter 32/2250] R0[31/150], Temp: 0.8983, Energy: 2.873679-0.000144j
[2025-09-12 17:35:29] [Iter 33/2250] R0[32/150], Temp: 0.8918, Energy: 2.873521-0.000125j
[2025-09-12 17:36:29] [Iter 34/2250] R0[33/150], Temp: 0.8853, Energy: 2.873630+0.000510j
[2025-09-12 17:38:12] [Iter 35/2250] R0[34/150], Temp: 0.8785, Energy: 2.873812+0.000767j
[2025-09-12 17:39:30] [Iter 36/2250] R0[35/150], Temp: 0.8716, Energy: 2.873192-0.000627j
[2025-09-12 17:41:11] [Iter 37/2250] R0[36/150], Temp: 0.8645, Energy: 2.873209-0.000406j
[2025-09-12 17:42:52] [Iter 38/2250] R0[37/150], Temp: 0.8572, Energy: 2.873391-0.000083j
[2025-09-12 17:44:34] [Iter 39/2250] R0[38/150], Temp: 0.8498, Energy: 2.873088-0.000666j
[2025-09-12 17:46:15] [Iter 40/2250] R0[39/150], Temp: 0.8423, Energy: 2.873044-0.000594j
[2025-09-12 17:47:56] [Iter 41/2250] R0[40/150], Temp: 0.8346, Energy: 2.872846-0.000610j
[2025-09-12 17:49:25] [Iter 42/2250] R0[41/150], Temp: 0.8267, Energy: 2.873437+0.000582j
[2025-09-12 17:50:37] [Iter 43/2250] R0[42/150], Temp: 0.8187, Energy: 2.872912-0.000133j
[2025-09-12 17:52:20] [Iter 44/2250] R0[43/150], Temp: 0.8106, Energy: 2.872399-0.000399j
[2025-09-12 17:53:42] [Iter 45/2250] R0[44/150], Temp: 0.8023, Energy: 2.873343-0.000057j
[2025-09-12 17:55:23] [Iter 46/2250] R0[45/150], Temp: 0.7939, Energy: 2.872788-0.000166j
[2025-09-12 17:57:05] [Iter 47/2250] R0[46/150], Temp: 0.7854, Energy: 2.872836-0.000405j
[2025-09-12 17:58:47] [Iter 48/2250] R0[47/150], Temp: 0.7767, Energy: 2.872614-0.000041j
[2025-09-12 18:00:29] [Iter 49/2250] R0[48/150], Temp: 0.7679, Energy: 2.872616-0.000194j
[2025-09-12 18:02:11] [Iter 50/2250] R0[49/150], Temp: 0.7590, Energy: 2.872538-0.000046j
[2025-09-12 18:03:26] [Iter 51/2250] R0[50/150], Temp: 0.7500, Energy: 2.872485+0.000550j
[2025-09-12 18:04:51] [Iter 52/2250] R0[51/150], Temp: 0.7409, Energy: 2.872418+0.000699j
[2025-09-12 18:06:33] [Iter 53/2250] R0[52/150], Temp: 0.7316, Energy: 2.872096-0.000588j
[2025-09-12 18:07:54] [Iter 54/2250] R0[53/150], Temp: 0.7223, Energy: 2.872137+0.000009j
[2025-09-12 18:09:35] [Iter 55/2250] R0[54/150], Temp: 0.7129, Energy: 2.871739-0.000245j
[2025-09-12 18:11:16] [Iter 56/2250] R0[55/150], Temp: 0.7034, Energy: 2.872035+0.000360j
[2025-09-12 18:12:58] [Iter 57/2250] R0[56/150], Temp: 0.6938, Energy: 2.870752-0.000633j
[2025-09-12 18:14:39] [Iter 58/2250] R0[57/150], Temp: 0.6841, Energy: 2.871194+0.000493j
[2025-09-12 18:16:20] [Iter 59/2250] R0[58/150], Temp: 0.6743, Energy: 2.870831-0.001373j
[2025-09-12 18:17:21] [Iter 60/2250] R0[59/150], Temp: 0.6644, Energy: 2.870919+0.000293j
[2025-09-12 18:18:55] [Iter 61/2250] R0[60/150], Temp: 0.6545, Energy: 2.870477-0.000359j
[2025-09-12 18:20:30] [Iter 62/2250] R0[61/150], Temp: 0.6445, Energy: 2.870156+0.000264j
[2025-09-12 18:21:59] [Iter 63/2250] R0[62/150], Temp: 0.6345, Energy: 2.869645-0.000613j
[2025-09-12 18:23:40] [Iter 64/2250] R0[63/150], Temp: 0.6243, Energy: 2.869243+0.000057j
[2025-09-12 18:25:21] [Iter 65/2250] R0[64/150], Temp: 0.6142, Energy: 2.870236+0.001893j
[2025-09-12 18:27:02] [Iter 66/2250] R0[65/150], Temp: 0.6040, Energy: 2.868988+0.000020j
[2025-09-12 18:28:43] [Iter 67/2250] R0[66/150], Temp: 0.5937, Energy: 2.869410+0.000425j
[2025-09-12 18:30:24] [Iter 68/2250] R0[67/150], Temp: 0.5834, Energy: 2.868114-0.000072j
[2025-09-12 18:31:21] [Iter 69/2250] R0[68/150], Temp: 0.5730, Energy: 2.866874-0.001931j
[2025-09-12 18:33:03] [Iter 70/2250] R0[69/150], Temp: 0.5627, Energy: 2.867359+0.000470j
[2025-09-12 18:34:28] [Iter 71/2250] R0[70/150], Temp: 0.5523, Energy: 2.866940+0.000529j
[2025-09-12 18:36:10] [Iter 72/2250] R0[71/150], Temp: 0.5418, Energy: 2.865389-0.000474j
[2025-09-12 18:37:51] [Iter 73/2250] R0[72/150], Temp: 0.5314, Energy: 2.865573+0.000054j
[2025-09-12 18:39:33] [Iter 74/2250] R0[73/150], Temp: 0.5209, Energy: 2.864056-0.000148j
[2025-09-12 18:41:14] [Iter 75/2250] R0[74/150], Temp: 0.5105, Energy: 2.863579-0.000546j
[2025-09-12 18:42:56] [Iter 76/2250] R0[75/150], Temp: 0.5000, Energy: 2.863011+0.000370j
[2025-09-12 18:44:28] [Iter 77/2250] R0[76/150], Temp: 0.4895, Energy: 2.861958-0.000337j
[2025-09-12 18:45:39] [Iter 78/2250] R0[77/150], Temp: 0.4791, Energy: 2.860851-0.000052j
[2025-09-12 18:47:21] [Iter 79/2250] R0[78/150], Temp: 0.4686, Energy: 2.859066-0.001059j
[2025-09-12 18:48:43] [Iter 80/2250] R0[79/150], Temp: 0.4582, Energy: 2.857221-0.001940j
[2025-09-12 18:50:25] [Iter 81/2250] R0[80/150], Temp: 0.4477, Energy: 2.856866-0.000450j
[2025-09-12 18:52:06] [Iter 82/2250] R0[81/150], Temp: 0.4373, Energy: 2.852933-0.004258j
[2025-09-12 18:53:47] [Iter 83/2250] R0[82/150], Temp: 0.4270, Energy: 2.854922+0.001664j
[2025-09-12 18:55:28] [Iter 84/2250] R0[83/150], Temp: 0.4166, Energy: 2.850803-0.001990j
[2025-09-12 18:57:09] [Iter 85/2250] R0[84/150], Temp: 0.4063, Energy: 2.851624+0.003963j
[2025-09-12 18:58:26] [Iter 86/2250] R0[85/150], Temp: 0.3960, Energy: 2.847027-0.000056j
[2025-09-12 18:59:50] [Iter 87/2250] R0[86/150], Temp: 0.3858, Energy: 2.842497-0.002163j
[2025-09-12 19:01:32] [Iter 88/2250] R0[87/150], Temp: 0.3757, Energy: 2.844182+0.005502j
[2025-09-12 19:02:53] [Iter 89/2250] R0[88/150], Temp: 0.3655, Energy: 2.837478-0.001052j
[2025-09-12 19:04:34] [Iter 90/2250] R0[89/150], Temp: 0.3555, Energy: 2.834707-0.001743j
[2025-09-12 19:06:16] [Iter 91/2250] R0[90/150], Temp: 0.3455, Energy: 2.830073+0.000590j
[2025-09-12 19:07:57] [Iter 92/2250] R0[91/150], Temp: 0.3356, Energy: 2.826100+0.000196j
[2025-09-12 19:09:39] [Iter 93/2250] R0[92/150], Temp: 0.3257, Energy: 2.817051-0.003668j
[2025-09-12 19:11:20] [Iter 94/2250] R0[93/150], Temp: 0.3159, Energy: 2.817266+0.005012j
[2025-09-12 19:12:22] [Iter 95/2250] R0[94/150], Temp: 0.3062, Energy: 2.807323-0.002128j
[2025-09-12 19:13:48] [Iter 96/2250] R0[95/150], Temp: 0.2966, Energy: 2.802416+0.000571j
[2025-09-12 19:15:31] [Iter 97/2250] R0[96/150], Temp: 0.2871, Energy: 2.794335+0.003973j
[2025-09-12 19:16:52] [Iter 98/2250] R0[97/150], Temp: 0.2777, Energy: 2.786695+0.005020j
[2025-09-12 19:18:33] [Iter 99/2250] R0[98/150], Temp: 0.2684, Energy: 2.777143+0.005362j
[2025-09-12 19:20:14] [Iter 100/2250] R0[99/150], Temp: 0.2591, Energy: 2.766072+0.004066j
[2025-09-12 19:21:55] [Iter 101/2250] R0[100/150], Temp: 0.2500, Energy: 2.750591+0.000089j
[2025-09-12 19:23:36] [Iter 102/2250] R0[101/150], Temp: 0.2410, Energy: 2.733266-0.004692j
[2025-09-12 19:25:17] [Iter 103/2250] R0[102/150], Temp: 0.2321, Energy: 2.724338+0.009064j
[2025-09-12 19:26:18] [Iter 104/2250] R0[103/150], Temp: 0.2233, Energy: 2.702888+0.001875j
[2025-09-12 19:27:54] [Iter 105/2250] R0[104/150], Temp: 0.2146, Energy: 2.678043-0.006051j
[2025-09-12 19:29:27] [Iter 106/2250] R0[105/150], Temp: 0.2061, Energy: 2.663268+0.008610j
[2025-09-12 19:30:58] [Iter 107/2250] R0[106/150], Temp: 0.1977, Energy: 2.639635+0.001097j
[2025-09-12 19:32:39] [Iter 108/2250] R0[107/150], Temp: 0.1894, Energy: 2.605917+0.001569j
[2025-09-12 19:34:20] [Iter 109/2250] R0[108/150], Temp: 0.1813, Energy: 2.577421+0.000155j
[2025-09-12 19:36:01] [Iter 110/2250] R0[109/150], Temp: 0.1733, Energy: 2.535579-0.004895j
[2025-09-12 19:37:42] [Iter 111/2250] R0[110/150], Temp: 0.1654, Energy: 2.492459-0.007990j
[2025-09-12 19:39:23] [Iter 112/2250] R0[111/150], Temp: 0.1577, Energy: 2.453585-0.003554j
[2025-09-12 19:40:25] [Iter 113/2250] R0[112/150], Temp: 0.1502, Energy: 2.401525-0.007021j
[2025-09-12 19:42:07] [Iter 114/2250] R0[113/150], Temp: 0.1428, Energy: 2.340171-0.015126j
[2025-09-12 19:43:27] [Iter 115/2250] R0[114/150], Temp: 0.1355, Energy: 2.282818-0.000754j
[2025-09-12 19:45:09] [Iter 116/2250] R0[115/150], Temp: 0.1284, Energy: 2.208445-0.002083j
[2025-09-12 19:46:51] [Iter 117/2250] R0[116/150], Temp: 0.1215, Energy: 2.131634-0.003370j
[2025-09-12 19:48:32] [Iter 118/2250] R0[117/150], Temp: 0.1147, Energy: 2.041452-0.006477j
[2025-09-12 19:50:13] [Iter 119/2250] R0[118/150], Temp: 0.1082, Energy: 1.945163+0.007214j
[2025-09-12 19:51:54] [Iter 120/2250] R0[119/150], Temp: 0.1017, Energy: 1.834514+0.019978j
[2025-09-12 19:53:26] [Iter 121/2250] R0[120/150], Temp: 0.0955, Energy: 1.699145+0.000253j
[2025-09-12 19:54:37] [Iter 122/2250] R0[121/150], Temp: 0.0894, Energy: 1.548550-0.005660j
[2025-09-12 19:56:20] [Iter 123/2250] R0[122/150], Temp: 0.0835, Energy: 1.395496+0.025712j
[2025-09-12 19:57:42] [Iter 124/2250] R0[123/150], Temp: 0.0778, Energy: 1.220279-0.001938j
[2025-09-12 19:59:23] [Iter 125/2250] R0[124/150], Temp: 0.0723, Energy: 0.987992-0.012730j
[2025-09-12 20:01:05] [Iter 126/2250] R0[125/150], Temp: 0.0670, Energy: 0.771308+0.018379j
[2025-09-12 20:02:46] [Iter 127/2250] R0[126/150], Temp: 0.0618, Energy: 0.465230-0.018974j
[2025-09-12 20:04:28] [Iter 128/2250] R0[127/150], Temp: 0.0569, Energy: 0.197113+0.013945j
[2025-09-12 20:06:10] [Iter 129/2250] R0[128/150], Temp: 0.0521, Energy: -0.184522-0.028802j
[2025-09-12 20:07:25] [Iter 130/2250] R0[129/150], Temp: 0.0476, Energy: -0.555026-0.019247j
[2025-09-12 20:08:51] [Iter 131/2250] R0[130/150], Temp: 0.0432, Energy: -1.027203-0.021315j
[2025-09-12 20:10:33] [Iter 132/2250] R0[131/150], Temp: 0.0391, Energy: -1.539103-0.020423j
[2025-09-12 20:11:54] [Iter 133/2250] R0[132/150], Temp: 0.0351, Energy: -2.060227-0.028187j
[2025-09-12 20:13:35] [Iter 134/2250] R0[133/150], Temp: 0.0314, Energy: -2.680489+0.038418j
[2025-09-12 20:15:16] [Iter 135/2250] R0[134/150], Temp: 0.0278, Energy: -3.382313+0.004589j
[2025-09-12 20:16:57] [Iter 136/2250] R0[135/150], Temp: 0.0245, Energy: -4.105720+0.016183j
[2025-09-12 20:18:38] [Iter 137/2250] R0[136/150], Temp: 0.0213, Energy: -4.907072-0.051064j
[2025-09-12 20:20:19] [Iter 138/2250] R0[137/150], Temp: 0.0184, Energy: -5.699027-0.002377j
[2025-09-12 20:21:20] [Iter 139/2250] R0[138/150], Temp: 0.0157, Energy: -6.675361-0.081906j
[2025-09-12 20:22:56] [Iter 140/2250] R0[139/150], Temp: 0.0132, Energy: -7.609455+0.013679j
[2025-09-12 20:24:29] [Iter 141/2250] R0[140/150], Temp: 0.0109, Energy: -8.543954+0.026421j
[2025-09-12 20:26:00] [Iter 142/2250] R0[141/150], Temp: 0.0089, Energy: -9.643665-0.035047j
[2025-09-12 20:27:42] [Iter 143/2250] R0[142/150], Temp: 0.0070, Energy: -10.717647+0.034106j
[2025-09-12 20:29:24] [Iter 144/2250] R0[143/150], Temp: 0.0054, Energy: -11.805890+0.027045j
[2025-09-12 20:31:06] [Iter 145/2250] R0[144/150], Temp: 0.0039, Energy: -12.991211+0.005321j
[2025-09-12 20:32:47] [Iter 146/2250] R0[145/150], Temp: 0.0027, Energy: -14.325626-0.020635j
[2025-09-12 20:34:30] [Iter 147/2250] R0[146/150], Temp: 0.0018, Energy: -15.757577-0.047068j
[2025-09-12 20:35:32] [Iter 148/2250] R0[147/150], Temp: 0.0010, Energy: -17.292448-0.033189j
[2025-09-12 20:37:14] [Iter 149/2250] R0[148/150], Temp: 0.0004, Energy: -19.021194+0.067069j
[2025-09-12 20:38:33] [Iter 150/2250] R0[149/150], Temp: 0.0001, Energy: -20.943390-0.000455j
[2025-09-12 20:38:33] RESTART #1 | Period: 300
[2025-09-12 20:40:14] [Iter 151/2250] R1[0/300], Temp: 1.0000, Energy: -23.295418-0.031624j
[2025-09-12 20:41:56] [Iter 152/2250] R1[1/300], Temp: 1.0000, Energy: -25.801447-0.028479j
[2025-09-12 20:43:37] [Iter 153/2250] R1[2/300], Temp: 0.9999, Energy: -28.903330-0.006100j
[2025-09-12 20:45:19] [Iter 154/2250] R1[3/300], Temp: 0.9998, Energy: -32.391080+0.021836j
[2025-09-12 20:47:00] [Iter 155/2250] R1[4/300], Temp: 0.9996, Energy: -36.442023+0.089187j
[2025-09-12 20:48:29] [Iter 156/2250] R1[5/300], Temp: 0.9993, Energy: -40.622002+0.038494j
[2025-09-12 20:49:43] [Iter 157/2250] R1[6/300], Temp: 0.9990, Energy: -44.955730-0.013176j
[2025-09-12 20:51:25] [Iter 158/2250] R1[7/300], Temp: 0.9987, Energy: -48.932937-0.049704j
[2025-09-12 20:52:47] [Iter 159/2250] R1[8/300], Temp: 0.9982, Energy: -52.750032-0.007291j
[2025-09-12 20:54:29] [Iter 160/2250] R1[9/300], Temp: 0.9978, Energy: -56.160438-0.009405j
[2025-09-12 20:56:10] [Iter 161/2250] R1[10/300], Temp: 0.9973, Energy: -59.047071+0.000249j
[2025-09-12 20:57:52] [Iter 162/2250] R1[11/300], Temp: 0.9967, Energy: -61.758929+0.013532j
[2025-09-12 20:59:34] [Iter 163/2250] R1[12/300], Temp: 0.9961, Energy: -64.178497-0.035602j
[2025-09-12 21:01:16] [Iter 164/2250] R1[13/300], Temp: 0.9954, Energy: -65.967099+0.024364j
[2025-09-12 21:02:29] [Iter 165/2250] R1[14/300], Temp: 0.9946, Energy: -67.652645+0.007426j
[2025-09-12 21:03:56] [Iter 166/2250] R1[15/300], Temp: 0.9938, Energy: -69.219906-0.018104j
[2025-09-12 21:05:37] [Iter 167/2250] R1[16/300], Temp: 0.9930, Energy: -70.540962+0.043496j
[2025-09-12 21:07:00] [Iter 168/2250] R1[17/300], Temp: 0.9921, Energy: -71.665202-0.007061j
[2025-09-12 21:08:41] [Iter 169/2250] R1[18/300], Temp: 0.9911, Energy: -72.700641+0.026697j
[2025-09-12 21:10:22] [Iter 170/2250] R1[19/300], Temp: 0.9901, Energy: -73.854734+0.024423j
[2025-09-12 21:12:03] [Iter 171/2250] R1[20/300], Temp: 0.9891, Energy: -74.619933+0.010789j
[2025-09-12 21:13:44] [Iter 172/2250] R1[21/300], Temp: 0.9880, Energy: -75.439308+0.031271j
[2025-09-12 21:15:25] [Iter 173/2250] R1[22/300], Temp: 0.9868, Energy: -76.141486-0.023579j
[2025-09-12 21:16:25] [Iter 174/2250] R1[23/300], Temp: 0.9856, Energy: -76.711819-0.013956j
[2025-09-12 21:18:02] [Iter 175/2250] R1[24/300], Temp: 0.9843, Energy: -77.231264-0.011565j
[2025-09-12 21:19:34] [Iter 176/2250] R1[25/300], Temp: 0.9830, Energy: -77.572459+0.005954j
[2025-09-12 21:21:05] [Iter 177/2250] R1[26/300], Temp: 0.9816, Energy: -78.051926+0.008300j
[2025-09-12 21:22:47] [Iter 178/2250] R1[27/300], Temp: 0.9801, Energy: -78.385266+0.014564j
[2025-09-12 21:24:29] [Iter 179/2250] R1[28/300], Temp: 0.9787, Energy: -78.592025+0.018269j
[2025-09-12 21:26:10] [Iter 180/2250] R1[29/300], Temp: 0.9771, Energy: -79.054497+0.009829j
[2025-09-12 21:27:52] [Iter 181/2250] R1[30/300], Temp: 0.9755, Energy: -79.354398-0.004232j
[2025-09-12 21:29:34] [Iter 182/2250] R1[31/300], Temp: 0.9739, Energy: -79.438977+0.006652j
[2025-09-12 21:30:36] [Iter 183/2250] R1[32/300], Temp: 0.9722, Energy: -79.625364-0.031574j
[2025-09-12 21:32:18] [Iter 184/2250] R1[33/300], Temp: 0.9704, Energy: -79.762831-0.002394j
[2025-09-12 21:33:36] [Iter 185/2250] R1[34/300], Temp: 0.9686, Energy: -79.965254-0.028813j
[2025-09-12 21:35:18] [Iter 186/2250] R1[35/300], Temp: 0.9668, Energy: -80.048321-0.007313j
[2025-09-12 21:36:59] [Iter 187/2250] R1[36/300], Temp: 0.9649, Energy: -80.207805+0.003353j
[2025-09-12 21:38:41] [Iter 188/2250] R1[37/300], Temp: 0.9629, Energy: -80.327382-0.000531j
[2025-09-12 21:40:23] [Iter 189/2250] R1[38/300], Temp: 0.9609, Energy: -80.412281+0.008073j
[2025-09-12 21:42:05] [Iter 190/2250] R1[39/300], Temp: 0.9589, Energy: -80.452869+0.006540j
[2025-09-12 21:43:34] [Iter 191/2250] R1[40/300], Temp: 0.9568, Energy: -80.629359+0.029910j
[2025-09-12 21:44:48] [Iter 192/2250] R1[41/300], Temp: 0.9546, Energy: -80.668135+0.006133j
[2025-09-12 21:46:30] [Iter 193/2250] R1[42/300], Temp: 0.9524, Energy: -80.725661-0.022915j
[2025-09-12 21:47:52] [Iter 194/2250] R1[43/300], Temp: 0.9502, Energy: -80.767260-0.028634j
[2025-09-12 21:49:33] [Iter 195/2250] R1[44/300], Temp: 0.9479, Energy: -80.891584-0.011146j
[2025-09-12 21:51:15] [Iter 196/2250] R1[45/300], Temp: 0.9455, Energy: -80.903125+0.016130j
[2025-09-12 21:52:56] [Iter 197/2250] R1[46/300], Temp: 0.9431, Energy: -80.985700-0.000187j
[2025-09-12 21:54:38] [Iter 198/2250] R1[47/300], Temp: 0.9407, Energy: -81.016585-0.011998j
[2025-09-12 21:56:20] [Iter 199/2250] R1[48/300], Temp: 0.9382, Energy: -81.005101+0.017525j
[2025-09-12 21:57:33] [Iter 200/2250] R1[49/300], Temp: 0.9356, Energy: -81.090890-0.009624j
[2025-09-12 21:59:01] [Iter 201/2250] R1[50/300], Temp: 0.9330, Energy: -81.056730+0.007793j
[2025-09-12 22:00:41] [Iter 202/2250] R1[51/300], Temp: 0.9304, Energy: -81.087430-0.000966j
[2025-09-12 22:02:05] [Iter 203/2250] R1[52/300], Temp: 0.9277, Energy: -81.302705-0.013145j
[2025-09-12 22:03:46] [Iter 204/2250] R1[53/300], Temp: 0.9249, Energy: -81.294110+0.018185j
[2025-09-12 22:05:28] [Iter 205/2250] R1[54/300], Temp: 0.9222, Energy: -81.342764+0.015590j
[2025-09-12 22:07:10] [Iter 206/2250] R1[55/300], Temp: 0.9193, Energy: -81.290718+0.017770j
[2025-09-12 22:08:51] [Iter 207/2250] R1[56/300], Temp: 0.9165, Energy: -81.333318-0.022834j
[2025-09-12 22:10:33] [Iter 208/2250] R1[57/300], Temp: 0.9135, Energy: -81.278275-0.002041j
[2025-09-12 22:11:31] [Iter 209/2250] R1[58/300], Temp: 0.9106, Energy: -81.363330+0.008152j
[2025-09-12 22:13:09] [Iter 210/2250] R1[59/300], Temp: 0.9076, Energy: -81.473512+0.014677j
[2025-09-12 22:14:41] [Iter 211/2250] R1[60/300], Temp: 0.9045, Energy: -81.447323+0.002305j
[2025-09-12 22:16:12] [Iter 212/2250] R1[61/300], Temp: 0.9014, Energy: -81.522611-0.007769j
[2025-09-12 22:17:53] [Iter 213/2250] R1[62/300], Temp: 0.8983, Energy: -81.421677-0.001302j
[2025-09-12 22:19:34] [Iter 214/2250] R1[63/300], Temp: 0.8951, Energy: -81.529121-0.000054j
[2025-09-12 22:21:16] [Iter 215/2250] R1[64/300], Temp: 0.8918, Energy: -81.573081-0.006512j
[2025-09-12 22:22:57] [Iter 216/2250] R1[65/300], Temp: 0.8886, Energy: -81.560259+0.000804j
[2025-09-12 22:24:39] [Iter 217/2250] R1[66/300], Temp: 0.8853, Energy: -81.581836-0.006470j
[2025-09-12 22:25:41] [Iter 218/2250] R1[67/300], Temp: 0.8819, Energy: -81.533624+0.020050j
[2025-09-12 22:27:23] [Iter 219/2250] R1[68/300], Temp: 0.8785, Energy: -81.632668+0.001292j
[2025-09-12 22:28:41] [Iter 220/2250] R1[69/300], Temp: 0.8751, Energy: -81.545196+0.006211j
[2025-09-12 22:30:23] [Iter 221/2250] R1[70/300], Temp: 0.8716, Energy: -81.560011+0.007258j
[2025-09-12 22:32:04] [Iter 222/2250] R1[71/300], Temp: 0.8680, Energy: -81.552489-0.001743j
[2025-09-12 22:33:45] [Iter 223/2250] R1[72/300], Temp: 0.8645, Energy: -81.579790-0.004709j
[2025-09-12 22:35:26] [Iter 224/2250] R1[73/300], Temp: 0.8609, Energy: -81.644267-0.002955j
[2025-09-12 22:37:07] [Iter 225/2250] R1[74/300], Temp: 0.8572, Energy: -81.681782-0.004535j
[2025-09-12 22:38:37] [Iter 226/2250] R1[75/300], Temp: 0.8536, Energy: -81.632814+0.002470j
[2025-09-12 22:39:51] [Iter 227/2250] R1[76/300], Temp: 0.8498, Energy: -81.675887-0.022694j
[2025-09-12 22:41:33] [Iter 228/2250] R1[77/300], Temp: 0.8461, Energy: -81.768036-0.008262j
[2025-09-12 22:42:55] [Iter 229/2250] R1[78/300], Temp: 0.8423, Energy: -81.759299+0.002844j
[2025-09-12 22:44:37] [Iter 230/2250] R1[79/300], Temp: 0.8384, Energy: -81.826648+0.010478j
[2025-09-12 22:46:19] [Iter 231/2250] R1[80/300], Temp: 0.8346, Energy: -81.794915-0.002915j
[2025-09-12 22:48:00] [Iter 232/2250] R1[81/300], Temp: 0.8307, Energy: -81.776834+0.010106j
[2025-09-12 22:49:42] [Iter 233/2250] R1[82/300], Temp: 0.8267, Energy: -81.759945-0.007232j
[2025-09-12 22:51:24] [Iter 234/2250] R1[83/300], Temp: 0.8227, Energy: -81.790595+0.014620j
[2025-09-12 22:52:38] [Iter 235/2250] R1[84/300], Temp: 0.8187, Energy: -81.774639-0.006137j
[2025-09-12 22:54:05] [Iter 236/2250] R1[85/300], Temp: 0.8147, Energy: -81.884511-0.004163j
[2025-09-12 22:55:46] [Iter 237/2250] R1[86/300], Temp: 0.8106, Energy: -81.915290+0.009645j
[2025-09-12 22:57:08] [Iter 238/2250] R1[87/300], Temp: 0.8065, Energy: -81.888350-0.004886j
[2025-09-12 22:58:49] [Iter 239/2250] R1[88/300], Temp: 0.8023, Energy: -81.855211+0.003845j
[2025-09-12 23:00:30] [Iter 240/2250] R1[89/300], Temp: 0.7981, Energy: -81.893253-0.004503j
[2025-09-12 23:02:12] [Iter 241/2250] R1[90/300], Temp: 0.7939, Energy: -81.894312-0.007222j
[2025-09-12 23:03:53] [Iter 242/2250] R1[91/300], Temp: 0.7896, Energy: -82.018047+0.003910j
[2025-09-12 23:05:33] [Iter 243/2250] R1[92/300], Temp: 0.7854, Energy: -81.911150+0.009534j
[2025-09-12 23:06:33] [Iter 244/2250] R1[93/300], Temp: 0.7810, Energy: -81.975560-0.000603j
[2025-09-12 23:08:11] [Iter 245/2250] R1[94/300], Temp: 0.7767, Energy: -81.928060+0.003225j
[2025-09-12 23:09:42] [Iter 246/2250] R1[95/300], Temp: 0.7723, Energy: -81.955372+0.004222j
[2025-09-12 23:11:14] [Iter 247/2250] R1[96/300], Temp: 0.7679, Energy: -81.963478-0.005426j
[2025-09-12 23:12:55] [Iter 248/2250] R1[97/300], Temp: 0.7635, Energy: -82.017959-0.001500j
[2025-09-12 23:14:37] [Iter 249/2250] R1[98/300], Temp: 0.7590, Energy: -81.905709+0.005226j
[2025-09-12 23:16:19] [Iter 250/2250] R1[99/300], Temp: 0.7545, Energy: -81.959117+0.014558j
[2025-09-12 23:16:19] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-09-12 23:18:00] [Iter 251/2250] R1[100/300], Temp: 0.7500, Energy: -82.000317-0.003993j
[2025-09-12 23:19:41] [Iter 252/2250] R1[101/300], Temp: 0.7455, Energy: -82.017831-0.004205j
[2025-09-12 23:20:46] [Iter 253/2250] R1[102/300], Temp: 0.7409, Energy: -82.008026+0.001619j
[2025-09-12 23:22:29] [Iter 254/2250] R1[103/300], Temp: 0.7363, Energy: -82.113055+0.001375j
[2025-09-12 23:23:48] [Iter 255/2250] R1[104/300], Temp: 0.7316, Energy: -82.033482-0.002892j
[2025-09-12 23:25:29] [Iter 256/2250] R1[105/300], Temp: 0.7270, Energy: -82.016550+0.005638j
[2025-09-12 23:27:11] [Iter 257/2250] R1[106/300], Temp: 0.7223, Energy: -82.078379+0.004564j
[2025-09-12 23:28:52] [Iter 258/2250] R1[107/300], Temp: 0.7176, Energy: -82.030018+0.021558j
[2025-09-12 23:30:33] [Iter 259/2250] R1[108/300], Temp: 0.7129, Energy: -81.990272-0.010724j
[2025-09-12 23:32:14] [Iter 260/2250] R1[109/300], Temp: 0.7081, Energy: -81.988138+0.014921j
[2025-09-12 23:33:41] [Iter 261/2250] R1[110/300], Temp: 0.7034, Energy: -82.005355-0.004335j
[2025-09-12 23:35:00] [Iter 262/2250] R1[111/300], Temp: 0.6986, Energy: -82.022047-0.001225j
[2025-09-12 23:36:42] [Iter 263/2250] R1[112/300], Temp: 0.6938, Energy: -82.159901+0.001258j
[2025-09-12 23:38:04] [Iter 264/2250] R1[113/300], Temp: 0.6889, Energy: -81.991644+0.008265j
[2025-09-12 23:39:45] [Iter 265/2250] R1[114/300], Temp: 0.6841, Energy: -82.097881-0.001778j
[2025-09-12 23:41:26] [Iter 266/2250] R1[115/300], Temp: 0.6792, Energy: -82.090594-0.005266j
[2025-09-12 23:43:07] [Iter 267/2250] R1[116/300], Temp: 0.6743, Energy: -82.101933-0.003127j
[2025-09-12 23:44:48] [Iter 268/2250] R1[117/300], Temp: 0.6694, Energy: -82.211588-0.004117j
[2025-09-12 23:46:29] [Iter 269/2250] R1[118/300], Temp: 0.6644, Energy: -82.174551+0.002205j
[2025-09-12 23:47:37] [Iter 270/2250] R1[119/300], Temp: 0.6595, Energy: -82.179728+0.012342j
[2025-09-12 23:49:12] [Iter 271/2250] R1[120/300], Temp: 0.6545, Energy: -82.137936+0.005054j
[2025-09-12 23:50:45] [Iter 272/2250] R1[121/300], Temp: 0.6495, Energy: -82.165537+0.011295j
[2025-09-12 23:52:15] [Iter 273/2250] R1[122/300], Temp: 0.6445, Energy: -82.113683+0.003837j
[2025-09-12 23:53:57] [Iter 274/2250] R1[123/300], Temp: 0.6395, Energy: -82.139959-0.012989j
[2025-09-12 23:55:38] [Iter 275/2250] R1[124/300], Temp: 0.6345, Energy: -82.308480-0.006884j
[2025-09-12 23:57:20] [Iter 276/2250] R1[125/300], Temp: 0.6294, Energy: -82.245728-0.009288j
[2025-09-12 23:59:01] [Iter 277/2250] R1[126/300], Temp: 0.6243, Energy: -82.311689+0.000314j
[2025-09-13 00:00:42] [Iter 278/2250] R1[127/300], Temp: 0.6193, Energy: -82.257665+0.004711j
[2025-09-13 00:01:46] [Iter 279/2250] R1[128/300], Temp: 0.6142, Energy: -82.266732+0.003268j
[2025-09-13 00:03:29] [Iter 280/2250] R1[129/300], Temp: 0.6091, Energy: -82.243432+0.005663j
[2025-09-13 00:04:49] [Iter 281/2250] R1[130/300], Temp: 0.6040, Energy: -82.233166-0.001217j
[2025-09-13 00:06:30] [Iter 282/2250] R1[131/300], Temp: 0.5988, Energy: -82.304251-0.010810j
[2025-09-13 00:08:11] [Iter 283/2250] R1[132/300], Temp: 0.5937, Energy: -82.259981+0.018331j
[2025-09-13 00:09:52] [Iter 284/2250] R1[133/300], Temp: 0.5885, Energy: -82.290031+0.000386j
[2025-09-13 00:11:33] [Iter 285/2250] R1[134/300], Temp: 0.5834, Energy: -82.223347-0.009938j
[2025-09-13 00:13:15] [Iter 286/2250] R1[135/300], Temp: 0.5782, Energy: -82.272498+0.010994j
[2025-09-13 00:14:42] [Iter 287/2250] R1[136/300], Temp: 0.5730, Energy: -82.159575-0.006000j
[2025-09-13 00:15:56] [Iter 288/2250] R1[137/300], Temp: 0.5679, Energy: -82.279040+0.006429j
[2025-09-13 00:17:38] [Iter 289/2250] R1[138/300], Temp: 0.5627, Energy: -82.224897+0.011355j
[2025-09-13 00:19:00] [Iter 290/2250] R1[139/300], Temp: 0.5575, Energy: -82.344222+0.006134j
[2025-09-13 00:20:41] [Iter 291/2250] R1[140/300], Temp: 0.5523, Energy: -82.322806+0.011425j
[2025-09-13 00:22:23] [Iter 292/2250] R1[141/300], Temp: 0.5471, Energy: -82.298058+0.007861j
[2025-09-13 00:24:04] [Iter 293/2250] R1[142/300], Temp: 0.5418, Energy: -82.292323+0.020085j
[2025-09-13 00:25:46] [Iter 294/2250] R1[143/300], Temp: 0.5366, Energy: -82.312642+0.018731j
[2025-09-13 00:27:27] [Iter 295/2250] R1[144/300], Temp: 0.5314, Energy: -82.289373-0.004673j
[2025-09-13 00:28:41] [Iter 296/2250] R1[145/300], Temp: 0.5262, Energy: -82.221101+0.000840j
[2025-09-13 00:30:07] [Iter 297/2250] R1[146/300], Temp: 0.5209, Energy: -82.205473+0.008317j
[2025-09-13 00:31:50] [Iter 298/2250] R1[147/300], Temp: 0.5157, Energy: -82.217536+0.012373j
[2025-09-13 00:33:11] [Iter 299/2250] R1[148/300], Temp: 0.5105, Energy: -82.279786+0.003893j
[2025-09-13 00:34:52] [Iter 300/2250] R1[149/300], Temp: 0.5052, Energy: -82.294206+0.009904j
[2025-09-13 00:36:33] [Iter 301/2250] R1[150/300], Temp: 0.5000, Energy: -82.212425+0.000168j
[2025-09-13 00:38:14] [Iter 302/2250] R1[151/300], Temp: 0.4948, Energy: -82.301979-0.002251j
[2025-09-13 00:39:55] [Iter 303/2250] R1[152/300], Temp: 0.4895, Energy: -82.320011+0.001405j
[2025-09-13 00:41:36] [Iter 304/2250] R1[153/300], Temp: 0.4843, Energy: -82.321367+0.010821j
[2025-09-13 00:42:37] [Iter 305/2250] R1[154/300], Temp: 0.4791, Energy: -82.268525+0.005329j
[2025-09-13 00:44:14] [Iter 306/2250] R1[155/300], Temp: 0.4738, Energy: -82.249193-0.019440j
[2025-09-13 00:45:45] [Iter 307/2250] R1[156/300], Temp: 0.4686, Energy: -82.318811+0.012845j
[2025-09-13 00:47:17] [Iter 308/2250] R1[157/300], Temp: 0.4634, Energy: -82.335954+0.006042j
[2025-09-13 00:48:58] [Iter 309/2250] R1[158/300], Temp: 0.4582, Energy: -82.297339-0.005161j
[2025-09-13 00:50:40] [Iter 310/2250] R1[159/300], Temp: 0.4529, Energy: -82.308844-0.011612j
[2025-09-13 00:52:21] [Iter 311/2250] R1[160/300], Temp: 0.4477, Energy: -82.343915-0.002098j
[2025-09-13 00:54:02] [Iter 312/2250] R1[161/300], Temp: 0.4425, Energy: -82.402120+0.003572j
[2025-09-13 00:55:43] [Iter 313/2250] R1[162/300], Temp: 0.4373, Energy: -82.246830+0.007315j
[2025-09-13 00:56:46] [Iter 314/2250] R1[163/300], Temp: 0.4321, Energy: -82.333164-0.001426j
[2025-09-13 00:58:28] [Iter 315/2250] R1[164/300], Temp: 0.4270, Energy: -82.416834+0.009176j
[2025-09-13 00:59:48] [Iter 316/2250] R1[165/300], Temp: 0.4218, Energy: -82.376575-0.002574j
[2025-09-13 01:01:29] [Iter 317/2250] R1[166/300], Temp: 0.4166, Energy: -82.393081-0.015028j
[2025-09-13 01:03:10] [Iter 318/2250] R1[167/300], Temp: 0.4115, Energy: -82.380562-0.000968j
[2025-09-13 01:04:51] [Iter 319/2250] R1[168/300], Temp: 0.4063, Energy: -82.335090-0.009862j
[2025-09-13 01:06:32] [Iter 320/2250] R1[169/300], Temp: 0.4012, Energy: -82.320544-0.005661j
[2025-09-13 01:08:14] [Iter 321/2250] R1[170/300], Temp: 0.3960, Energy: -82.358242+0.006058j
[2025-09-13 01:09:42] [Iter 322/2250] R1[171/300], Temp: 0.3909, Energy: -82.474147+0.007058j
[2025-09-13 01:10:56] [Iter 323/2250] R1[172/300], Temp: 0.3858, Energy: -82.432850+0.000123j
[2025-09-13 01:12:39] [Iter 324/2250] R1[173/300], Temp: 0.3807, Energy: -82.434959-0.001941j
[2025-09-13 01:14:01] [Iter 325/2250] R1[174/300], Temp: 0.3757, Energy: -82.478740+0.005836j
[2025-09-13 01:15:42] [Iter 326/2250] R1[175/300], Temp: 0.3706, Energy: -82.394557+0.002361j
[2025-09-13 01:17:24] [Iter 327/2250] R1[176/300], Temp: 0.3655, Energy: -82.445123+0.012445j
[2025-09-13 01:19:05] [Iter 328/2250] R1[177/300], Temp: 0.3605, Energy: -82.420297+0.008499j
[2025-09-13 01:20:46] [Iter 329/2250] R1[178/300], Temp: 0.3555, Energy: -82.420475-0.014967j
[2025-09-13 01:22:28] [Iter 330/2250] R1[179/300], Temp: 0.3505, Energy: -82.522329+0.011421j
[2025-09-13 01:23:41] [Iter 331/2250] R1[180/300], Temp: 0.3455, Energy: -82.411585+0.006088j
[2025-09-13 01:25:09] [Iter 332/2250] R1[181/300], Temp: 0.3405, Energy: -82.503775-0.000337j
[2025-09-13 01:26:49] [Iter 333/2250] R1[182/300], Temp: 0.3356, Energy: -82.443037+0.006253j
[2025-09-13 01:28:13] [Iter 334/2250] R1[183/300], Temp: 0.3306, Energy: -82.496311+0.003983j
[2025-09-13 01:29:55] [Iter 335/2250] R1[184/300], Temp: 0.3257, Energy: -82.515330+0.005942j
[2025-09-13 01:31:36] [Iter 336/2250] R1[185/300], Temp: 0.3208, Energy: -82.471304-0.007931j
[2025-09-13 01:33:18] [Iter 337/2250] R1[186/300], Temp: 0.3159, Energy: -82.550521+0.009926j
[2025-09-13 01:34:59] [Iter 338/2250] R1[187/300], Temp: 0.3111, Energy: -82.449384-0.000760j
[2025-09-13 01:36:41] [Iter 339/2250] R1[188/300], Temp: 0.3062, Energy: -82.483919+0.010002j
[2025-09-13 01:37:38] [Iter 340/2250] R1[189/300], Temp: 0.3014, Energy: -82.508821+0.004150j
[2025-09-13 01:39:18] [Iter 341/2250] R1[190/300], Temp: 0.2966, Energy: -82.517014+0.000744j
[2025-09-13 01:40:50] [Iter 342/2250] R1[191/300], Temp: 0.2919, Energy: -82.509716-0.009470j
[2025-09-13 01:42:22] [Iter 343/2250] R1[192/300], Temp: 0.2871, Energy: -82.371619+0.001029j
[2025-09-13 01:44:03] [Iter 344/2250] R1[193/300], Temp: 0.2824, Energy: -82.422393+0.004641j
[2025-09-13 01:45:44] [Iter 345/2250] R1[194/300], Temp: 0.2777, Energy: -82.553187+0.009871j
[2025-09-13 01:47:25] [Iter 346/2250] R1[195/300], Temp: 0.2730, Energy: -82.531850+0.007860j
[2025-09-13 01:49:07] [Iter 347/2250] R1[196/300], Temp: 0.2684, Energy: -82.312625+0.007373j
[2025-09-13 01:50:47] [Iter 348/2250] R1[197/300], Temp: 0.2637, Energy: -82.369498-0.001631j
[2025-09-13 01:51:51] [Iter 349/2250] R1[198/300], Temp: 0.2591, Energy: -82.262205-0.000842j
[2025-09-13 01:53:33] [Iter 350/2250] R1[199/300], Temp: 0.2545, Energy: -82.344193+0.016047j
[2025-09-13 01:54:54] [Iter 351/2250] R1[200/300], Temp: 0.2500, Energy: -82.439257+0.006445j
[2025-09-13 01:56:35] [Iter 352/2250] R1[201/300], Temp: 0.2455, Energy: -82.413002+0.002014j
[2025-09-13 01:58:16] [Iter 353/2250] R1[202/300], Temp: 0.2410, Energy: -82.571977+0.005050j
[2025-09-13 01:59:58] [Iter 354/2250] R1[203/300], Temp: 0.2365, Energy: -82.556643+0.001664j
[2025-09-13 02:01:39] [Iter 355/2250] R1[204/300], Temp: 0.2321, Energy: -82.583100-0.001933j
[2025-09-13 02:03:20] [Iter 356/2250] R1[205/300], Temp: 0.2277, Energy: -82.582781-0.003275j
[2025-09-13 02:04:44] [Iter 357/2250] R1[206/300], Temp: 0.2233, Energy: -82.519774-0.002726j
[2025-09-13 02:05:54] [Iter 358/2250] R1[207/300], Temp: 0.2190, Energy: -82.586167-0.002476j
[2025-09-13 02:07:37] [Iter 359/2250] R1[208/300], Temp: 0.2146, Energy: -82.521861-0.018663j
[2025-09-13 02:08:59] [Iter 360/2250] R1[209/300], Temp: 0.2104, Energy: -82.516209+0.001200j
[2025-09-13 02:10:41] [Iter 361/2250] R1[210/300], Temp: 0.2061, Energy: -82.570227-0.001094j
[2025-09-13 02:12:22] [Iter 362/2250] R1[211/300], Temp: 0.2019, Energy: -82.598145+0.000759j
[2025-09-13 02:14:04] [Iter 363/2250] R1[212/300], Temp: 0.1977, Energy: -82.542674+0.003284j
[2025-09-13 02:15:46] [Iter 364/2250] R1[213/300], Temp: 0.1935, Energy: -82.528482+0.004512j
[2025-09-13 02:17:27] [Iter 365/2250] R1[214/300], Temp: 0.1894, Energy: -82.571480+0.004248j
[2025-09-13 02:18:44] [Iter 366/2250] R1[215/300], Temp: 0.1853, Energy: -82.533108-0.004687j
[2025-09-13 02:20:10] [Iter 367/2250] R1[216/300], Temp: 0.1813, Energy: -82.627589+0.001307j
[2025-09-13 02:21:52] [Iter 368/2250] R1[217/300], Temp: 0.1773, Energy: -82.646229-0.002346j
[2025-09-13 02:23:13] [Iter 369/2250] R1[218/300], Temp: 0.1733, Energy: -82.545098-0.001726j
[2025-09-13 02:24:54] [Iter 370/2250] R1[219/300], Temp: 0.1693, Energy: -82.521677-0.003598j
[2025-09-13 02:26:35] [Iter 371/2250] R1[220/300], Temp: 0.1654, Energy: -82.545585-0.002492j
[2025-09-13 02:28:16] [Iter 372/2250] R1[221/300], Temp: 0.1616, Energy: -82.467161-0.006225j
[2025-09-13 02:29:57] [Iter 373/2250] R1[222/300], Temp: 0.1577, Energy: -82.575199-0.013319j
[2025-09-13 02:31:39] [Iter 374/2250] R1[223/300], Temp: 0.1539, Energy: -82.480742-0.006850j
[2025-09-13 02:32:39] [Iter 375/2250] R1[224/300], Temp: 0.1502, Energy: -82.595067+0.001237j
[2025-09-13 02:34:16] [Iter 376/2250] R1[225/300], Temp: 0.1464, Energy: -82.603394+0.007812j
[2025-09-13 02:35:48] [Iter 377/2250] R1[226/300], Temp: 0.1428, Energy: -82.628914-0.001743j
[2025-09-13 02:37:20] [Iter 378/2250] R1[227/300], Temp: 0.1391, Energy: -82.586287-0.001087j
[2025-09-13 02:39:02] [Iter 379/2250] R1[228/300], Temp: 0.1355, Energy: -82.691997-0.004752j
[2025-09-13 02:40:43] [Iter 380/2250] R1[229/300], Temp: 0.1320, Energy: -82.635630+0.000968j
[2025-09-13 02:42:24] [Iter 381/2250] R1[230/300], Temp: 0.1284, Energy: -82.676868+0.002864j
[2025-09-13 02:44:06] [Iter 382/2250] R1[231/300], Temp: 0.1249, Energy: -82.655869-0.005868j
[2025-09-13 02:45:48] [Iter 383/2250] R1[232/300], Temp: 0.1215, Energy: -82.733811+0.000136j
[2025-09-13 02:46:49] [Iter 384/2250] R1[233/300], Temp: 0.1181, Energy: -82.674337+0.006818j
[2025-09-13 02:48:32] [Iter 385/2250] R1[234/300], Temp: 0.1147, Energy: -82.599239-0.002616j
[2025-09-13 02:49:50] [Iter 386/2250] R1[235/300], Temp: 0.1114, Energy: -82.531496-0.003355j
[2025-09-13 02:51:32] [Iter 387/2250] R1[236/300], Temp: 0.1082, Energy: -82.675023+0.005738j
[2025-09-13 02:53:14] [Iter 388/2250] R1[237/300], Temp: 0.1049, Energy: -82.608070+0.008338j
[2025-09-13 02:54:56] [Iter 389/2250] R1[238/300], Temp: 0.1017, Energy: -82.636479-0.005994j
[2025-09-13 02:56:38] [Iter 390/2250] R1[239/300], Temp: 0.0986, Energy: -82.624006-0.009745j
[2025-09-13 02:58:19] [Iter 391/2250] R1[240/300], Temp: 0.0955, Energy: -82.697263+0.004287j
[2025-09-13 02:59:49] [Iter 392/2250] R1[241/300], Temp: 0.0924, Energy: -82.712845-0.000532j
[2025-09-13 03:01:02] [Iter 393/2250] R1[242/300], Temp: 0.0894, Energy: -82.685158+0.010887j
[2025-09-13 03:02:44] [Iter 394/2250] R1[243/300], Temp: 0.0865, Energy: -82.683113-0.000479j
[2025-09-13 03:04:07] [Iter 395/2250] R1[244/300], Temp: 0.0835, Energy: -82.652179+0.006616j
[2025-09-13 03:05:48] [Iter 396/2250] R1[245/300], Temp: 0.0807, Energy: -82.661568-0.000595j
[2025-09-13 03:07:29] [Iter 397/2250] R1[246/300], Temp: 0.0778, Energy: -82.715857-0.000254j
[2025-09-13 03:09:11] [Iter 398/2250] R1[247/300], Temp: 0.0751, Energy: -82.667593-0.004378j
[2025-09-13 03:10:53] [Iter 399/2250] R1[248/300], Temp: 0.0723, Energy: -82.735114+0.008674j
[2025-09-13 03:12:35] [Iter 400/2250] R1[249/300], Temp: 0.0696, Energy: -82.691622+0.003055j
[2025-09-13 03:13:48] [Iter 401/2250] R1[250/300], Temp: 0.0670, Energy: -82.825862-0.003650j
[2025-09-13 03:15:15] [Iter 402/2250] R1[251/300], Temp: 0.0644, Energy: -82.773288-0.003121j
[2025-09-13 03:16:56] [Iter 403/2250] R1[252/300], Temp: 0.0618, Energy: -82.744795+0.001449j
[2025-09-13 03:18:18] [Iter 404/2250] R1[253/300], Temp: 0.0593, Energy: -82.748550+0.001774j
[2025-09-13 03:19:59] [Iter 405/2250] R1[254/300], Temp: 0.0569, Energy: -82.691585+0.000075j
[2025-09-13 03:21:41] [Iter 406/2250] R1[255/300], Temp: 0.0545, Energy: -82.736738+0.001004j
[2025-09-13 03:23:22] [Iter 407/2250] R1[256/300], Temp: 0.0521, Energy: -82.693130-0.002329j
[2025-09-13 03:25:03] [Iter 408/2250] R1[257/300], Temp: 0.0498, Energy: -82.732132-0.010271j
[2025-09-13 03:26:44] [Iter 409/2250] R1[258/300], Temp: 0.0476, Energy: -82.713341-0.008272j
[2025-09-13 03:27:44] [Iter 410/2250] R1[259/300], Temp: 0.0454, Energy: -82.686819+0.004368j
[2025-09-13 03:29:22] [Iter 411/2250] R1[260/300], Temp: 0.0432, Energy: -82.804751+0.002685j
[2025-09-13 03:30:54] [Iter 412/2250] R1[261/300], Temp: 0.0411, Energy: -82.809821+0.007397j
[2025-09-13 03:32:26] [Iter 413/2250] R1[262/300], Temp: 0.0391, Energy: -82.803790+0.002454j
[2025-09-13 03:34:07] [Iter 414/2250] R1[263/300], Temp: 0.0371, Energy: -82.786852-0.002131j
[2025-09-13 03:35:48] [Iter 415/2250] R1[264/300], Temp: 0.0351, Energy: -82.744017+0.003816j
[2025-09-13 03:37:29] [Iter 416/2250] R1[265/300], Temp: 0.0332, Energy: -82.697564-0.000443j
[2025-09-13 03:39:10] [Iter 417/2250] R1[266/300], Temp: 0.0314, Energy: -82.747130-0.003108j
[2025-09-13 03:40:51] [Iter 418/2250] R1[267/300], Temp: 0.0296, Energy: -82.731668-0.001014j
[2025-09-13 03:41:53] [Iter 419/2250] R1[268/300], Temp: 0.0278, Energy: -82.716638+0.006772j
[2025-09-13 03:43:36] [Iter 420/2250] R1[269/300], Temp: 0.0261, Energy: -82.688099+0.009057j
[2025-09-13 03:44:53] [Iter 421/2250] R1[270/300], Temp: 0.0245, Energy: -82.687344+0.001135j
[2025-09-13 03:46:35] [Iter 422/2250] R1[271/300], Temp: 0.0229, Energy: -82.638589-0.002821j
[2025-09-13 03:48:16] [Iter 423/2250] R1[272/300], Temp: 0.0213, Energy: -82.631504+0.003517j
[2025-09-13 03:49:57] [Iter 424/2250] R1[273/300], Temp: 0.0199, Energy: -82.709386-0.001325j
[2025-09-13 03:51:39] [Iter 425/2250] R1[274/300], Temp: 0.0184, Energy: -82.802705-0.000938j
[2025-09-13 03:53:20] [Iter 426/2250] R1[275/300], Temp: 0.0170, Energy: -82.678223-0.000103j
[2025-09-13 03:54:49] [Iter 427/2250] R1[276/300], Temp: 0.0157, Energy: -82.658708-0.002661j
[2025-09-13 03:56:03] [Iter 428/2250] R1[277/300], Temp: 0.0144, Energy: -82.639669-0.004613j
[2025-09-13 03:57:45] [Iter 429/2250] R1[278/300], Temp: 0.0132, Energy: -82.618663-0.008360j
[2025-09-13 03:59:07] [Iter 430/2250] R1[279/300], Temp: 0.0120, Energy: -82.628160-0.005677j
[2025-09-13 04:00:49] [Iter 431/2250] R1[280/300], Temp: 0.0109, Energy: -82.692375+0.000941j
[2025-09-13 04:02:30] [Iter 432/2250] R1[281/300], Temp: 0.0099, Energy: -82.645905+0.000780j
[2025-09-13 04:04:12] [Iter 433/2250] R1[282/300], Temp: 0.0089, Energy: -82.606779-0.003342j
[2025-09-13 04:05:53] [Iter 434/2250] R1[283/300], Temp: 0.0079, Energy: -82.637794+0.001886j
[2025-09-13 04:07:34] [Iter 435/2250] R1[284/300], Temp: 0.0070, Energy: -82.605575+0.009964j
[2025-09-13 04:08:47] [Iter 436/2250] R1[285/300], Temp: 0.0062, Energy: -82.687556+0.001699j
[2025-09-13 04:10:07] [Iter 437/2250] R1[286/300], Temp: 0.0054, Energy: -82.711494+0.013081j
[2025-09-13 04:11:49] [Iter 438/2250] R1[287/300], Temp: 0.0046, Energy: -82.697560-0.000728j
[2025-09-13 04:13:10] [Iter 439/2250] R1[288/300], Temp: 0.0039, Energy: -82.678816+0.005086j
[2025-09-13 04:14:52] [Iter 440/2250] R1[289/300], Temp: 0.0033, Energy: -82.722598+0.003320j
[2025-09-13 04:16:33] [Iter 441/2250] R1[290/300], Temp: 0.0027, Energy: -82.808676-0.003155j
[2025-09-13 04:18:15] [Iter 442/2250] R1[291/300], Temp: 0.0022, Energy: -82.787782+0.001525j
[2025-09-13 04:19:57] [Iter 443/2250] R1[292/300], Temp: 0.0018, Energy: -82.760429+0.003862j
[2025-09-13 04:21:38] [Iter 444/2250] R1[293/300], Temp: 0.0013, Energy: -82.720863+0.005544j
[2025-09-13 04:22:45] [Iter 445/2250] R1[294/300], Temp: 0.0010, Energy: -82.636310+0.002401j
[2025-09-13 04:24:18] [Iter 446/2250] R1[295/300], Temp: 0.0007, Energy: -82.724696-0.002830j
[2025-09-13 04:25:54] [Iter 447/2250] R1[296/300], Temp: 0.0004, Energy: -82.653458+0.002107j
[2025-09-13 04:27:21] [Iter 448/2250] R1[297/300], Temp: 0.0002, Energy: -82.735811+0.008889j
[2025-09-13 04:29:02] [Iter 449/2250] R1[298/300], Temp: 0.0001, Energy: -82.748401+0.001623j
[2025-09-13 04:30:43] [Iter 450/2250] R1[299/300], Temp: 0.0000, Energy: -82.741222+0.009100j
[2025-09-13 04:30:43] RESTART #2 | Period: 600
[2025-09-13 04:32:25] [Iter 451/2250] R2[0/600], Temp: 1.0000, Energy: -82.812949+0.005160j
[2025-09-13 04:34:06] [Iter 452/2250] R2[1/600], Temp: 1.0000, Energy: -82.800656-0.001615j
[2025-09-13 04:35:47] [Iter 453/2250] R2[2/600], Temp: 1.0000, Energy: -82.872256-0.001106j
[2025-09-13 04:36:45] [Iter 454/2250] R2[3/600], Temp: 0.9999, Energy: -82.811579-0.000131j
[2025-09-13 04:38:27] [Iter 455/2250] R2[4/600], Temp: 0.9999, Energy: -82.781570-0.004973j
[2025-09-13 04:39:53] [Iter 456/2250] R2[5/600], Temp: 0.9998, Energy: -82.790194-0.002227j
[2025-09-13 04:41:31] [Iter 457/2250] R2[6/600], Temp: 0.9998, Energy: -82.826054+0.002060j
[2025-09-13 04:43:12] [Iter 458/2250] R2[7/600], Temp: 0.9997, Energy: -82.751878-0.003922j
[2025-09-13 04:44:54] [Iter 459/2250] R2[8/600], Temp: 0.9996, Energy: -82.841502+0.001550j
[2025-09-13 04:46:36] [Iter 460/2250] R2[9/600], Temp: 0.9994, Energy: -82.821818-0.001353j
[2025-09-13 04:48:18] [Iter 461/2250] R2[10/600], Temp: 0.9993, Energy: -82.788312-0.002551j
[2025-09-13 04:49:53] [Iter 462/2250] R2[11/600], Temp: 0.9992, Energy: -82.803550-0.005849j
[2025-09-13 04:51:01] [Iter 463/2250] R2[12/600], Temp: 0.9990, Energy: -82.723728+0.001250j
[2025-09-13 04:52:44] [Iter 464/2250] R2[13/600], Temp: 0.9988, Energy: -82.758352-0.003400j
[2025-09-13 04:54:06] [Iter 465/2250] R2[14/600], Temp: 0.9987, Energy: -82.728532-0.000682j
[2025-09-13 04:55:47] [Iter 466/2250] R2[15/600], Temp: 0.9985, Energy: -82.759700-0.002409j
[2025-09-13 04:57:28] [Iter 467/2250] R2[16/600], Temp: 0.9982, Energy: -82.892832+0.000129j
[2025-09-13 04:59:09] [Iter 468/2250] R2[17/600], Temp: 0.9980, Energy: -82.839005+0.001019j
[2025-09-13 05:00:50] [Iter 469/2250] R2[18/600], Temp: 0.9978, Energy: -82.772463+0.000698j
[2025-09-13 05:02:31] [Iter 470/2250] R2[19/600], Temp: 0.9975, Energy: -82.755028+0.003529j
[2025-09-13 05:03:50] [Iter 471/2250] R2[20/600], Temp: 0.9973, Energy: -82.858472+0.002046j
[2025-09-13 05:05:14] [Iter 472/2250] R2[21/600], Temp: 0.9970, Energy: -82.916267+0.001715j
[2025-09-13 05:06:56] [Iter 473/2250] R2[22/600], Temp: 0.9967, Energy: -82.866909+0.006719j
[2025-09-13 05:08:18] [Iter 474/2250] R2[23/600], Temp: 0.9964, Energy: -82.920877-0.001014j
[2025-09-13 05:10:00] [Iter 475/2250] R2[24/600], Temp: 0.9961, Energy: -82.810362-0.002536j
[2025-09-13 05:11:42] [Iter 476/2250] R2[25/600], Temp: 0.9957, Energy: -82.781996+0.008693j
[2025-09-13 05:13:23] [Iter 477/2250] R2[26/600], Temp: 0.9954, Energy: -82.783229-0.006281j
[2025-09-13 05:15:05] [Iter 478/2250] R2[27/600], Temp: 0.9950, Energy: -82.797745-0.000678j
[2025-09-13 05:16:47] [Iter 479/2250] R2[28/600], Temp: 0.9946, Energy: -82.814577+0.001138j
[2025-09-13 05:17:49] [Iter 480/2250] R2[29/600], Temp: 0.9942, Energy: -82.696950-0.001503j
[2025-09-13 05:19:22] [Iter 481/2250] R2[30/600], Temp: 0.9938, Energy: -82.754276+0.004330j
[2025-09-13 05:20:57] [Iter 482/2250] R2[31/600], Temp: 0.9934, Energy: -82.764650+0.003380j
[2025-09-13 05:22:27] [Iter 483/2250] R2[32/600], Temp: 0.9930, Energy: -82.751831-0.001528j
[2025-09-13 05:24:08] [Iter 484/2250] R2[33/600], Temp: 0.9926, Energy: -82.764329-0.005524j
[2025-09-13 05:25:50] [Iter 485/2250] R2[34/600], Temp: 0.9921, Energy: -82.771008+0.003467j
[2025-09-13 05:27:31] [Iter 486/2250] R2[35/600], Temp: 0.9916, Energy: -82.892103-0.001727j
[2025-09-13 05:29:13] [Iter 487/2250] R2[36/600], Temp: 0.9911, Energy: -82.826837-0.002627j
[2025-09-13 05:30:55] [Iter 488/2250] R2[37/600], Temp: 0.9906, Energy: -82.784424+0.000226j
[2025-09-13 05:31:51] [Iter 489/2250] R2[38/600], Temp: 0.9901, Energy: -82.804631+0.010488j
[2025-09-13 05:33:33] [Iter 490/2250] R2[39/600], Temp: 0.9896, Energy: -82.800449+0.009400j
[2025-09-13 05:34:59] [Iter 491/2250] R2[40/600], Temp: 0.9891, Energy: -82.868003-0.002387j
[2025-09-13 05:36:36] [Iter 492/2250] R2[41/600], Temp: 0.9885, Energy: -82.861173+0.002738j
[2025-09-13 05:38:17] [Iter 493/2250] R2[42/600], Temp: 0.9880, Energy: -82.871116-0.001155j
[2025-09-13 05:39:58] [Iter 494/2250] R2[43/600], Temp: 0.9874, Energy: -82.945246+0.001899j
[2025-09-13 05:41:39] [Iter 495/2250] R2[44/600], Temp: 0.9868, Energy: -82.945036-0.004368j
[2025-09-13 05:43:21] [Iter 496/2250] R2[45/600], Temp: 0.9862, Energy: -82.957570-0.007897j
[2025-09-13 05:44:56] [Iter 497/2250] R2[46/600], Temp: 0.9856, Energy: -82.911595+0.003802j
[2025-09-13 05:46:04] [Iter 498/2250] R2[47/600], Temp: 0.9849, Energy: -82.814885-0.000812j
[2025-09-13 05:47:46] [Iter 499/2250] R2[48/600], Temp: 0.9843, Energy: -82.860896+0.001571j
[2025-09-13 05:49:08] [Iter 500/2250] R2[49/600], Temp: 0.9836, Energy: -82.857555+0.000427j
[2025-09-13 05:49:08] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-09-13 05:50:49] [Iter 501/2250] R2[50/600], Temp: 0.9830, Energy: -82.727540-0.003770j
[2025-09-13 05:52:30] [Iter 502/2250] R2[51/600], Temp: 0.9823, Energy: -82.812327+0.006963j
[2025-09-13 05:54:12] [Iter 503/2250] R2[52/600], Temp: 0.9816, Energy: -82.923421+0.004696j
[2025-09-13 05:55:53] [Iter 504/2250] R2[53/600], Temp: 0.9809, Energy: -82.900161-0.003172j
[2025-09-13 05:57:34] [Iter 505/2250] R2[54/600], Temp: 0.9801, Energy: -82.800330-0.001482j
[2025-09-13 05:58:54] [Iter 506/2250] R2[55/600], Temp: 0.9794, Energy: -82.768174+0.007005j
[2025-09-13 06:00:18] [Iter 507/2250] R2[56/600], Temp: 0.9787, Energy: -82.754195+0.008087j
[2025-09-13 06:02:00] [Iter 508/2250] R2[57/600], Temp: 0.9779, Energy: -82.804621-0.000480j
[2025-09-13 06:03:22] [Iter 509/2250] R2[58/600], Temp: 0.9771, Energy: -82.810684-0.002956j
[2025-09-13 06:05:02] [Iter 510/2250] R2[59/600], Temp: 0.9763, Energy: -82.855698+0.001409j
[2025-09-13 06:06:44] [Iter 511/2250] R2[60/600], Temp: 0.9755, Energy: -82.804647+0.001815j
[2025-09-13 06:08:25] [Iter 512/2250] R2[61/600], Temp: 0.9747, Energy: -82.831347-0.008848j
[2025-09-13 06:10:06] [Iter 513/2250] R2[62/600], Temp: 0.9739, Energy: -82.840165-0.001039j
[2025-09-13 06:11:47] [Iter 514/2250] R2[63/600], Temp: 0.9730, Energy: -82.887222+0.000720j
[2025-09-13 06:12:49] [Iter 515/2250] R2[64/600], Temp: 0.9722, Energy: -82.842409-0.000810j
[2025-09-13 06:14:16] [Iter 516/2250] R2[65/600], Temp: 0.9713, Energy: -82.916072+0.001544j
[2025-09-13 06:15:58] [Iter 517/2250] R2[66/600], Temp: 0.9704, Energy: -82.878140+0.000306j
[2025-09-13 06:17:19] [Iter 518/2250] R2[67/600], Temp: 0.9695, Energy: -82.826200-0.003296j
[2025-09-13 06:19:00] [Iter 519/2250] R2[68/600], Temp: 0.9686, Energy: -82.848495-0.004305j
[2025-09-13 06:20:41] [Iter 520/2250] R2[69/600], Temp: 0.9677, Energy: -82.908224+0.000838j
[2025-09-13 06:22:23] [Iter 521/2250] R2[70/600], Temp: 0.9668, Energy: -82.832819-0.001228j
[2025-09-13 06:24:04] [Iter 522/2250] R2[71/600], Temp: 0.9658, Energy: -82.841305-0.003479j
[2025-09-13 06:25:45] [Iter 523/2250] R2[72/600], Temp: 0.9649, Energy: -82.840508+0.000859j
[2025-09-13 06:26:45] [Iter 524/2250] R2[73/600], Temp: 0.9639, Energy: -82.835058+0.000504j
[2025-09-13 06:28:22] [Iter 525/2250] R2[74/600], Temp: 0.9629, Energy: -82.848309-0.007202j
[2025-09-13 06:29:54] [Iter 526/2250] R2[75/600], Temp: 0.9619, Energy: -82.944612-0.004564j
[2025-09-13 06:31:25] [Iter 527/2250] R2[76/600], Temp: 0.9609, Energy: -82.892414-0.002133j
[2025-09-13 06:33:07] [Iter 528/2250] R2[77/600], Temp: 0.9599, Energy: -82.829725-0.007098j
[2025-09-13 06:34:49] [Iter 529/2250] R2[78/600], Temp: 0.9589, Energy: -82.850403-0.005051j
[2025-09-13 06:36:31] [Iter 530/2250] R2[79/600], Temp: 0.9578, Energy: -82.846491-0.006110j
[2025-09-13 06:38:13] [Iter 531/2250] R2[80/600], Temp: 0.9568, Energy: -82.833150-0.001763j
[2025-09-13 06:39:55] [Iter 532/2250] R2[81/600], Temp: 0.9557, Energy: -82.764569+0.006315j
[2025-09-13 06:40:57] [Iter 533/2250] R2[82/600], Temp: 0.9546, Energy: -82.822828-0.003454j
[2025-09-13 06:42:39] [Iter 534/2250] R2[83/600], Temp: 0.9535, Energy: -82.790454-0.002962j
[2025-09-13 06:43:58] [Iter 535/2250] R2[84/600], Temp: 0.9524, Energy: -82.809309-0.002423j
[2025-09-13 06:45:39] [Iter 536/2250] R2[85/600], Temp: 0.9513, Energy: -82.815173-0.001120j
[2025-09-13 06:47:21] [Iter 537/2250] R2[86/600], Temp: 0.9502, Energy: -82.927160+0.005963j
[2025-09-13 06:49:03] [Iter 538/2250] R2[87/600], Temp: 0.9490, Energy: -82.845945-0.003696j
[2025-09-13 06:50:45] [Iter 539/2250] R2[88/600], Temp: 0.9479, Energy: -82.857521+0.005244j
[2025-09-13 06:52:27] [Iter 540/2250] R2[89/600], Temp: 0.9467, Energy: -82.831864+0.008140j
[2025-09-13 06:53:56] [Iter 541/2250] R2[90/600], Temp: 0.9455, Energy: -82.817036+0.000211j
[2025-09-13 06:55:09] [Iter 542/2250] R2[91/600], Temp: 0.9443, Energy: -82.827872+0.004545j
[2025-09-13 06:56:52] [Iter 543/2250] R2[92/600], Temp: 0.9431, Energy: -82.821336-0.003987j
[2025-09-13 06:58:13] [Iter 544/2250] R2[93/600], Temp: 0.9419, Energy: -82.837288-0.002128j
[2025-09-13 06:59:55] [Iter 545/2250] R2[94/600], Temp: 0.9407, Energy: -82.826547-0.003177j
[2025-09-13 07:01:37] [Iter 546/2250] R2[95/600], Temp: 0.9394, Energy: -82.866908+0.005056j
[2025-09-13 07:03:19] [Iter 547/2250] R2[96/600], Temp: 0.9382, Energy: -82.855631+0.005966j
[2025-09-13 07:05:01] [Iter 548/2250] R2[97/600], Temp: 0.9369, Energy: -82.857906-0.001419j
[2025-09-13 07:06:43] [Iter 549/2250] R2[98/600], Temp: 0.9356, Energy: -82.827966-0.001171j
[2025-09-13 07:07:56] [Iter 550/2250] R2[99/600], Temp: 0.9343, Energy: -82.824343-0.001414j
[2025-09-13 07:09:23] [Iter 551/2250] R2[100/600], Temp: 0.9330, Energy: -82.800611-0.006120j
[2025-09-13 07:11:04] [Iter 552/2250] R2[101/600], Temp: 0.9317, Energy: -82.903113-0.000511j
[2025-09-13 07:12:27] [Iter 553/2250] R2[102/600], Temp: 0.9304, Energy: -82.840394+0.002881j
[2025-09-13 07:14:08] [Iter 554/2250] R2[103/600], Temp: 0.9290, Energy: -82.806327+0.001014j
[2025-09-13 07:15:49] [Iter 555/2250] R2[104/600], Temp: 0.9277, Energy: -82.851767-0.011765j
[2025-09-13 07:17:30] [Iter 556/2250] R2[105/600], Temp: 0.9263, Energy: -82.869848-0.004213j
[2025-09-13 07:19:12] [Iter 557/2250] R2[106/600], Temp: 0.9249, Energy: -82.788418-0.000445j
[2025-09-13 07:20:53] [Iter 558/2250] R2[107/600], Temp: 0.9236, Energy: -82.869015-0.000701j
[2025-09-13 07:21:51] [Iter 559/2250] R2[108/600], Temp: 0.9222, Energy: -82.912571-0.000873j
[2025-09-13 07:23:30] [Iter 560/2250] R2[109/600], Temp: 0.9208, Energy: -82.879086+0.000547j
[2025-09-13 07:25:02] [Iter 561/2250] R2[110/600], Temp: 0.9193, Energy: -82.836881-0.003406j
[2025-09-13 07:26:33] [Iter 562/2250] R2[111/600], Temp: 0.9179, Energy: -82.783004-0.005396j
[2025-09-13 07:28:15] [Iter 563/2250] R2[112/600], Temp: 0.9165, Energy: -82.841652+0.001301j
[2025-09-13 07:29:57] [Iter 564/2250] R2[113/600], Temp: 0.9150, Energy: -82.827232+0.002730j
[2025-09-13 07:31:38] [Iter 565/2250] R2[114/600], Temp: 0.9135, Energy: -82.839023-0.000866j
[2025-09-13 07:33:20] [Iter 566/2250] R2[115/600], Temp: 0.9121, Energy: -82.774387+0.005852j
[2025-09-13 07:35:01] [Iter 567/2250] R2[116/600], Temp: 0.9106, Energy: -82.796868-0.001906j
[2025-09-13 07:36:03] [Iter 568/2250] R2[117/600], Temp: 0.9091, Energy: -82.774793-0.007963j
[2025-09-13 07:37:46] [Iter 569/2250] R2[118/600], Temp: 0.9076, Energy: -82.814119+0.000732j
[2025-09-13 07:39:03] [Iter 570/2250] R2[119/600], Temp: 0.9060, Energy: -82.776389+0.003968j
[2025-09-13 07:40:45] [Iter 571/2250] R2[120/600], Temp: 0.9045, Energy: -82.792691+0.009578j
[2025-09-13 07:42:27] [Iter 572/2250] R2[121/600], Temp: 0.9030, Energy: -82.890005+0.001746j
[2025-09-13 07:44:08] [Iter 573/2250] R2[122/600], Temp: 0.9014, Energy: -82.943877+0.004402j
[2025-09-13 07:45:50] [Iter 574/2250] R2[123/600], Temp: 0.8998, Energy: -82.888423+0.001457j
[2025-09-13 07:47:32] [Iter 575/2250] R2[124/600], Temp: 0.8983, Energy: -82.890262+0.005799j
[2025-09-13 07:49:00] [Iter 576/2250] R2[125/600], Temp: 0.8967, Energy: -82.942096+0.005305j
[2025-09-13 07:50:14] [Iter 577/2250] R2[126/600], Temp: 0.8951, Energy: -82.930595+0.001782j
[2025-09-13 07:51:57] [Iter 578/2250] R2[127/600], Temp: 0.8935, Energy: -82.892491+0.003372j
[2025-09-13 07:53:18] [Iter 579/2250] R2[128/600], Temp: 0.8918, Energy: -82.891815+0.002458j
[2025-09-13 07:54:59] [Iter 580/2250] R2[129/600], Temp: 0.8902, Energy: -82.849516-0.000158j
[2025-09-13 07:56:40] [Iter 581/2250] R2[130/600], Temp: 0.8886, Energy: -82.871343+0.002018j
[2025-09-13 07:58:21] [Iter 582/2250] R2[131/600], Temp: 0.8869, Energy: -82.841222+0.001121j
[2025-09-13 08:00:02] [Iter 583/2250] R2[132/600], Temp: 0.8853, Energy: -82.839366+0.002095j
[2025-09-13 08:01:44] [Iter 584/2250] R2[133/600], Temp: 0.8836, Energy: -82.796622-0.000188j
[2025-09-13 08:02:57] [Iter 585/2250] R2[134/600], Temp: 0.8819, Energy: -82.818806-0.003952j
[2025-09-13 08:04:25] [Iter 586/2250] R2[135/600], Temp: 0.8802, Energy: -82.898205-0.006488j
[2025-09-13 08:06:06] [Iter 587/2250] R2[136/600], Temp: 0.8785, Energy: -82.839905-0.004274j
[2025-09-13 08:07:28] [Iter 588/2250] R2[137/600], Temp: 0.8768, Energy: -82.809667+0.000882j
[2025-09-13 08:09:10] [Iter 589/2250] R2[138/600], Temp: 0.8751, Energy: -82.755033-0.000754j
[2025-09-13 08:10:51] [Iter 590/2250] R2[139/600], Temp: 0.8733, Energy: -82.749738+0.003038j
[2025-09-13 08:12:32] [Iter 591/2250] R2[140/600], Temp: 0.8716, Energy: -82.818722-0.001921j
[2025-09-13 08:14:14] [Iter 592/2250] R2[141/600], Temp: 0.8698, Energy: -82.849548+0.003514j
[2025-09-13 08:15:55] [Iter 593/2250] R2[142/600], Temp: 0.8680, Energy: -82.824700+0.008244j
[2025-09-13 08:16:54] [Iter 594/2250] R2[143/600], Temp: 0.8663, Energy: -82.881540+0.001428j
[2025-09-13 08:18:23] [Iter 595/2250] R2[144/600], Temp: 0.8645, Energy: -82.895119+0.004109j
[2025-09-13 08:20:02] [Iter 596/2250] R2[145/600], Temp: 0.8627, Energy: -82.884089-0.000939j
[2025-09-13 08:21:27] [Iter 597/2250] R2[146/600], Temp: 0.8609, Energy: -82.897037-0.000432j
[2025-09-13 08:23:08] [Iter 598/2250] R2[147/600], Temp: 0.8591, Energy: -82.871068+0.004270j
[2025-09-13 08:24:49] [Iter 599/2250] R2[148/600], Temp: 0.8572, Energy: -82.815427+0.000479j
[2025-09-13 08:26:30] [Iter 600/2250] R2[149/600], Temp: 0.8554, Energy: -82.761514+0.005942j
[2025-09-13 08:28:11] [Iter 601/2250] R2[150/600], Temp: 0.8536, Energy: -82.775428+0.000935j
[2025-09-13 08:29:52] [Iter 602/2250] R2[151/600], Temp: 0.8517, Energy: -82.647254+0.002842j
[2025-09-13 08:30:49] [Iter 603/2250] R2[152/600], Temp: 0.8498, Energy: -82.733863+0.001299j
[2025-09-13 08:32:29] [Iter 604/2250] R2[153/600], Temp: 0.8480, Energy: -82.708424-0.003162j
[2025-09-13 08:34:00] [Iter 605/2250] R2[154/600], Temp: 0.8461, Energy: -82.688899+0.000452j
[2025-09-13 08:35:32] [Iter 606/2250] R2[155/600], Temp: 0.8442, Energy: -82.760853+0.000662j
[2025-09-13 08:37:14] [Iter 607/2250] R2[156/600], Temp: 0.8423, Energy: -82.769754-0.003295j
[2025-09-13 08:38:55] [Iter 608/2250] R2[157/600], Temp: 0.8404, Energy: -82.789092-0.005233j
[2025-09-13 08:40:36] [Iter 609/2250] R2[158/600], Temp: 0.8384, Energy: -82.885177+0.000397j
[2025-09-13 08:42:18] [Iter 610/2250] R2[159/600], Temp: 0.8365, Energy: -82.766894+0.002951j
[2025-09-13 08:43:59] [Iter 611/2250] R2[160/600], Temp: 0.8346, Energy: -82.821760+0.000134j
[2025-09-13 08:45:02] [Iter 612/2250] R2[161/600], Temp: 0.8326, Energy: -82.888506-0.003796j
[2025-09-13 08:46:44] [Iter 613/2250] R2[162/600], Temp: 0.8307, Energy: -82.825164-0.000814j
[2025-09-13 08:48:02] [Iter 614/2250] R2[163/600], Temp: 0.8287, Energy: -82.861361+0.009652j
[2025-09-13 08:49:43] [Iter 615/2250] R2[164/600], Temp: 0.8267, Energy: -82.861170+0.001323j
[2025-09-13 08:51:25] [Iter 616/2250] R2[165/600], Temp: 0.8247, Energy: -82.869616-0.002699j
[2025-09-13 08:53:06] [Iter 617/2250] R2[166/600], Temp: 0.8227, Energy: -82.862238+0.001238j
[2025-09-13 08:54:47] [Iter 618/2250] R2[167/600], Temp: 0.8207, Energy: -82.870674-0.000358j
[2025-09-13 08:56:28] [Iter 619/2250] R2[168/600], Temp: 0.8187, Energy: -82.774974+0.000105j
[2025-09-13 08:57:56] [Iter 620/2250] R2[169/600], Temp: 0.8167, Energy: -82.836529+0.001100j
[2025-09-13 08:59:11] [Iter 621/2250] R2[170/600], Temp: 0.8147, Energy: -82.828504+0.001578j
[2025-09-13 09:00:53] [Iter 622/2250] R2[171/600], Temp: 0.8126, Energy: -82.870698+0.002498j
[2025-09-13 09:02:15] [Iter 623/2250] R2[172/600], Temp: 0.8106, Energy: -82.825702-0.007667j
[2025-09-13 09:03:57] [Iter 624/2250] R2[173/600], Temp: 0.8085, Energy: -82.761289+0.004725j
[2025-09-13 09:05:39] [Iter 625/2250] R2[174/600], Temp: 0.8065, Energy: -82.837957-0.006622j
[2025-09-13 09:07:20] [Iter 626/2250] R2[175/600], Temp: 0.8044, Energy: -82.879419+0.000965j
[2025-09-13 09:09:02] [Iter 627/2250] R2[176/600], Temp: 0.8023, Energy: -82.922093+0.002092j
[2025-09-13 09:10:44] [Iter 628/2250] R2[177/600], Temp: 0.8002, Energy: -82.912446-0.001601j
[2025-09-13 09:11:56] [Iter 629/2250] R2[178/600], Temp: 0.7981, Energy: -82.890849-0.002008j
[2025-09-13 09:13:24] [Iter 630/2250] R2[179/600], Temp: 0.7960, Energy: -82.871404-0.000713j
[2025-09-13 09:15:04] [Iter 631/2250] R2[180/600], Temp: 0.7939, Energy: -82.922624+0.000859j
[2025-09-13 09:16:27] [Iter 632/2250] R2[181/600], Temp: 0.7918, Energy: -82.944170+0.001657j
[2025-09-13 09:18:08] [Iter 633/2250] R2[182/600], Temp: 0.7896, Energy: -82.939839+0.000435j
[2025-09-13 09:19:49] [Iter 634/2250] R2[183/600], Temp: 0.7875, Energy: -82.890057-0.003731j
[2025-09-13 09:21:31] [Iter 635/2250] R2[184/600], Temp: 0.7854, Energy: -82.880255+0.004552j
[2025-09-13 09:23:12] [Iter 636/2250] R2[185/600], Temp: 0.7832, Energy: -82.762546+0.004726j
[2025-09-13 09:24:53] [Iter 637/2250] R2[186/600], Temp: 0.7810, Energy: -82.870181+0.003499j
[2025-09-13 09:25:51] [Iter 638/2250] R2[187/600], Temp: 0.7789, Energy: -82.849290-0.002630j
[2025-09-13 09:27:30] [Iter 639/2250] R2[188/600], Temp: 0.7767, Energy: -82.890580+0.002142j
[2025-09-13 09:29:02] [Iter 640/2250] R2[189/600], Temp: 0.7745, Energy: -82.919066-0.002783j
[2025-09-13 09:30:33] [Iter 641/2250] R2[190/600], Temp: 0.7723, Energy: -82.913741-0.001083j
[2025-09-13 09:32:14] [Iter 642/2250] R2[191/600], Temp: 0.7701, Energy: -82.861445-0.005170j
[2025-09-13 09:33:56] [Iter 643/2250] R2[192/600], Temp: 0.7679, Energy: -82.891973-0.000450j
[2025-09-13 09:35:37] [Iter 644/2250] R2[193/600], Temp: 0.7657, Energy: -82.817410-0.004200j
[2025-09-13 09:37:18] [Iter 645/2250] R2[194/600], Temp: 0.7635, Energy: -82.824381+0.002883j
[2025-09-13 09:39:00] [Iter 646/2250] R2[195/600], Temp: 0.7612, Energy: -82.759127+0.001390j
[2025-09-13 09:40:01] [Iter 647/2250] R2[196/600], Temp: 0.7590, Energy: -82.825723+0.000822j
[2025-09-13 09:41:43] [Iter 648/2250] R2[197/600], Temp: 0.7568, Energy: -82.755164+0.001158j
[2025-09-13 09:43:01] [Iter 649/2250] R2[198/600], Temp: 0.7545, Energy: -82.849085+0.003630j
[2025-09-13 09:44:42] [Iter 650/2250] R2[199/600], Temp: 0.7523, Energy: -82.943081-0.001693j
[2025-09-13 09:46:24] [Iter 651/2250] R2[200/600], Temp: 0.7500, Energy: -82.945482+0.000132j
[2025-09-13 09:48:05] [Iter 652/2250] R2[201/600], Temp: 0.7477, Energy: -82.945291+0.004097j
[2025-09-13 09:49:46] [Iter 653/2250] R2[202/600], Temp: 0.7455, Energy: -82.917068-0.001779j
[2025-09-13 09:51:28] [Iter 654/2250] R2[203/600], Temp: 0.7432, Energy: -82.868419+0.003661j
[2025-09-13 09:52:57] [Iter 655/2250] R2[204/600], Temp: 0.7409, Energy: -82.816700+0.004968j
[2025-09-13 09:54:10] [Iter 656/2250] R2[205/600], Temp: 0.7386, Energy: -82.825123-0.000526j
[2025-09-13 09:55:52] [Iter 657/2250] R2[206/600], Temp: 0.7363, Energy: -82.840902-0.000822j
[2025-09-13 09:57:14] [Iter 658/2250] R2[207/600], Temp: 0.7340, Energy: -82.897098+0.003355j
[2025-09-13 09:58:55] [Iter 659/2250] R2[208/600], Temp: 0.7316, Energy: -82.937104-0.001989j
[2025-09-13 10:00:37] [Iter 660/2250] R2[209/600], Temp: 0.7293, Energy: -82.956570+0.000467j
[2025-09-13 10:02:18] [Iter 661/2250] R2[210/600], Temp: 0.7270, Energy: -82.890854-0.002727j
[2025-09-13 10:03:59] [Iter 662/2250] R2[211/600], Temp: 0.7247, Energy: -82.891276-0.003110j
[2025-09-13 10:05:40] [Iter 663/2250] R2[212/600], Temp: 0.7223, Energy: -82.982791+0.000786j
[2025-09-13 10:06:55] [Iter 664/2250] R2[213/600], Temp: 0.7200, Energy: -82.941935-0.003277j
[2025-09-13 10:08:21] [Iter 665/2250] R2[214/600], Temp: 0.7176, Energy: -82.929086+0.002530j
[2025-09-13 10:10:03] [Iter 666/2250] R2[215/600], Temp: 0.7153, Energy: -82.859306+0.002248j
[2025-09-13 10:11:25] [Iter 667/2250] R2[216/600], Temp: 0.7129, Energy: -82.842920+0.003395j
[2025-09-13 10:13:06] [Iter 668/2250] R2[217/600], Temp: 0.7105, Energy: -82.884670-0.005706j
[2025-09-13 10:14:47] [Iter 669/2250] R2[218/600], Temp: 0.7081, Energy: -82.882352-0.001623j
[2025-09-13 10:16:29] [Iter 670/2250] R2[219/600], Temp: 0.7058, Energy: -82.934229+0.002727j
[2025-09-13 10:18:10] [Iter 671/2250] R2[220/600], Temp: 0.7034, Energy: -82.892540+0.001234j
[2025-09-13 10:19:51] [Iter 672/2250] R2[221/600], Temp: 0.7010, Energy: -83.010580+0.000578j
[2025-09-13 10:20:50] [Iter 673/2250] R2[222/600], Temp: 0.6986, Energy: -82.893483-0.001955j
[2025-09-13 10:22:21] [Iter 674/2250] R2[223/600], Temp: 0.6962, Energy: -82.874184-0.000484j
[2025-09-13 10:23:59] [Iter 675/2250] R2[224/600], Temp: 0.6938, Energy: -82.923951-0.000055j
[2025-09-13 10:25:25] [Iter 676/2250] R2[225/600], Temp: 0.6913, Energy: -82.814379-0.000100j
[2025-09-13 10:27:06] [Iter 677/2250] R2[226/600], Temp: 0.6889, Energy: -82.811706-0.001247j
[2025-09-13 10:28:47] [Iter 678/2250] R2[227/600], Temp: 0.6865, Energy: -82.876954+0.000968j
[2025-09-13 10:30:28] [Iter 679/2250] R2[228/600], Temp: 0.6841, Energy: -82.902614+0.001682j
[2025-09-13 10:32:09] [Iter 680/2250] R2[229/600], Temp: 0.6816, Energy: -82.925748-0.002213j
[2025-09-13 10:33:51] [Iter 681/2250] R2[230/600], Temp: 0.6792, Energy: -82.813896+0.004601j
[2025-09-13 10:34:50] [Iter 682/2250] R2[231/600], Temp: 0.6767, Energy: -82.851683-0.002219j
[2025-09-13 10:36:32] [Iter 683/2250] R2[232/600], Temp: 0.6743, Energy: -82.847600-0.003639j
[2025-09-13 10:37:58] [Iter 684/2250] R2[233/600], Temp: 0.6718, Energy: -82.888685-0.000797j
[2025-09-13 10:39:35] [Iter 685/2250] R2[234/600], Temp: 0.6694, Energy: -82.880971-0.002207j
[2025-09-13 10:41:17] [Iter 686/2250] R2[235/600], Temp: 0.6669, Energy: -82.837299-0.001059j
[2025-09-13 10:42:59] [Iter 687/2250] R2[236/600], Temp: 0.6644, Energy: -82.817223+0.002307j
[2025-09-13 10:44:41] [Iter 688/2250] R2[237/600], Temp: 0.6620, Energy: -82.803732-0.001659j
[2025-09-13 10:46:22] [Iter 689/2250] R2[238/600], Temp: 0.6595, Energy: -82.830563-0.008390j
[2025-09-13 10:47:58] [Iter 690/2250] R2[239/600], Temp: 0.6570, Energy: -82.859270-0.006992j
[2025-09-13 10:49:06] [Iter 691/2250] R2[240/600], Temp: 0.6545, Energy: -82.865742+0.001723j
[2025-09-13 10:50:49] [Iter 692/2250] R2[241/600], Temp: 0.6520, Energy: -82.834972-0.001355j
[2025-09-13 10:52:10] [Iter 693/2250] R2[242/600], Temp: 0.6495, Energy: -82.858477-0.004331j
[2025-09-13 10:53:51] [Iter 694/2250] R2[243/600], Temp: 0.6470, Energy: -82.853230-0.001502j
[2025-09-13 10:55:33] [Iter 695/2250] R2[244/600], Temp: 0.6445, Energy: -82.844688-0.001740j
[2025-09-13 10:57:14] [Iter 696/2250] R2[245/600], Temp: 0.6420, Energy: -82.932307+0.001404j
[2025-09-13 10:58:55] [Iter 697/2250] R2[246/600], Temp: 0.6395, Energy: -82.938597-0.000840j
[2025-09-13 11:00:36] [Iter 698/2250] R2[247/600], Temp: 0.6370, Energy: -82.950910+0.001114j
[2025-09-13 11:01:55] [Iter 699/2250] R2[248/600], Temp: 0.6345, Energy: -82.905511-0.000461j
[2025-09-13 11:03:20] [Iter 700/2250] R2[249/600], Temp: 0.6319, Energy: -82.925536-0.001356j
[2025-09-13 11:05:02] [Iter 701/2250] R2[250/600], Temp: 0.6294, Energy: -82.916490+0.007025j
[2025-09-13 11:06:23] [Iter 702/2250] R2[251/600], Temp: 0.6269, Energy: -82.866821+0.003137j
[2025-09-13 11:08:04] [Iter 703/2250] R2[252/600], Temp: 0.6243, Energy: -82.837135-0.001680j
[2025-09-13 11:09:46] [Iter 704/2250] R2[253/600], Temp: 0.6218, Energy: -82.838345+0.000497j
[2025-09-13 11:11:27] [Iter 705/2250] R2[254/600], Temp: 0.6193, Energy: -82.813499-0.001225j
[2025-09-13 11:13:08] [Iter 706/2250] R2[255/600], Temp: 0.6167, Energy: -82.879375-0.006666j
[2025-09-13 11:14:50] [Iter 707/2250] R2[256/600], Temp: 0.6142, Energy: -82.843346+0.004241j
[2025-09-13 11:15:52] [Iter 708/2250] R2[257/600], Temp: 0.6116, Energy: -82.887500+0.000512j
[2025-09-13 11:17:27] [Iter 709/2250] R2[258/600], Temp: 0.6091, Energy: -82.897765+0.001914j
[2025-09-13 11:19:00] [Iter 710/2250] R2[259/600], Temp: 0.6065, Energy: -82.925097-0.001567j
[2025-09-13 11:20:32] [Iter 711/2250] R2[260/600], Temp: 0.6040, Energy: -82.928505-0.005215j
[2025-09-13 11:22:14] [Iter 712/2250] R2[261/600], Temp: 0.6014, Energy: -82.956993-0.001527j
[2025-09-13 11:23:55] [Iter 713/2250] R2[262/600], Temp: 0.5988, Energy: -82.937316+0.000731j
[2025-09-13 11:25:37] [Iter 714/2250] R2[263/600], Temp: 0.5963, Energy: -82.816630+0.001523j
[2025-09-13 11:27:19] [Iter 715/2250] R2[264/600], Temp: 0.5937, Energy: -82.863805-0.001530j
[2025-09-13 11:29:01] [Iter 716/2250] R2[265/600], Temp: 0.5911, Energy: -82.933338-0.002289j
[2025-09-13 11:30:03] [Iter 717/2250] R2[266/600], Temp: 0.5885, Energy: -82.925719+0.000332j
[2025-09-13 11:31:45] [Iter 718/2250] R2[267/600], Temp: 0.5860, Energy: -82.898952+0.003627j
[2025-09-13 11:33:04] [Iter 719/2250] R2[268/600], Temp: 0.5834, Energy: -82.910212+0.002224j
[2025-09-13 11:34:45] [Iter 720/2250] R2[269/600], Temp: 0.5808, Energy: -82.857907-0.003546j
[2025-09-13 11:36:26] [Iter 721/2250] R2[270/600], Temp: 0.5782, Energy: -82.798773+0.004461j
[2025-09-13 11:38:08] [Iter 722/2250] R2[271/600], Temp: 0.5756, Energy: -82.720295-0.001506j
[2025-09-13 11:39:49] [Iter 723/2250] R2[272/600], Temp: 0.5730, Energy: -82.832178+0.002989j
[2025-09-13 11:41:30] [Iter 724/2250] R2[273/600], Temp: 0.5705, Energy: -82.793649+0.002038j
[2025-09-13 11:43:00] [Iter 725/2250] R2[274/600], Temp: 0.5679, Energy: -82.856366+0.002364j
[2025-09-13 11:44:12] [Iter 726/2250] R2[275/600], Temp: 0.5653, Energy: -82.885119+0.002691j
[2025-09-13 11:45:54] [Iter 727/2250] R2[276/600], Temp: 0.5627, Energy: -82.916709+0.000090j
[2025-09-13 11:47:16] [Iter 728/2250] R2[277/600], Temp: 0.5601, Energy: -82.874455+0.001033j
[2025-09-13 11:48:58] [Iter 729/2250] R2[278/600], Temp: 0.5575, Energy: -82.924410-0.001318j
[2025-09-13 11:50:40] [Iter 730/2250] R2[279/600], Temp: 0.5549, Energy: -82.930956+0.000128j
[2025-09-13 11:52:21] [Iter 731/2250] R2[280/600], Temp: 0.5523, Energy: -82.892753-0.004442j
[2025-09-13 11:54:02] [Iter 732/2250] R2[281/600], Temp: 0.5497, Energy: -82.923846-0.005855j
[2025-09-13 11:55:43] [Iter 733/2250] R2[282/600], Temp: 0.5471, Energy: -82.888375+0.003823j
[2025-09-13 11:56:58] [Iter 734/2250] R2[283/600], Temp: 0.5444, Energy: -82.883741-0.000206j
[2025-09-13 11:58:24] [Iter 735/2250] R2[284/600], Temp: 0.5418, Energy: -82.903891+0.001382j
[2025-09-13 12:00:06] [Iter 736/2250] R2[285/600], Temp: 0.5392, Energy: -82.804683-0.000171j
[2025-09-13 12:01:28] [Iter 737/2250] R2[286/600], Temp: 0.5366, Energy: -82.842391+0.002497j
[2025-09-13 12:03:09] [Iter 738/2250] R2[287/600], Temp: 0.5340, Energy: -82.913846+0.000443j
[2025-09-13 12:04:51] [Iter 739/2250] R2[288/600], Temp: 0.5314, Energy: -82.851968-0.000783j
[2025-09-13 12:06:32] [Iter 740/2250] R2[289/600], Temp: 0.5288, Energy: -82.841314+0.001926j
[2025-09-13 12:08:14] [Iter 741/2250] R2[290/600], Temp: 0.5262, Energy: -82.837244+0.000112j
[2025-09-13 12:09:55] [Iter 742/2250] R2[291/600], Temp: 0.5236, Energy: -82.880126+0.000634j
[2025-09-13 12:10:55] [Iter 743/2250] R2[292/600], Temp: 0.5209, Energy: -82.881005+0.005856j
[2025-09-13 12:12:31] [Iter 744/2250] R2[293/600], Temp: 0.5183, Energy: -82.903529+0.000348j
[2025-09-13 12:14:03] [Iter 745/2250] R2[294/600], Temp: 0.5157, Energy: -82.919652-0.000166j
[2025-09-13 12:15:35] [Iter 746/2250] R2[295/600], Temp: 0.5131, Energy: -82.893503+0.001962j
[2025-09-13 12:17:16] [Iter 747/2250] R2[296/600], Temp: 0.5105, Energy: -82.875535+0.000927j
[2025-09-13 12:18:58] [Iter 748/2250] R2[297/600], Temp: 0.5079, Energy: -82.830035+0.003417j
[2025-09-13 12:20:39] [Iter 749/2250] R2[298/600], Temp: 0.5052, Energy: -82.902679+0.003501j
[2025-09-13 12:22:20] [Iter 750/2250] R2[299/600], Temp: 0.5026, Energy: -82.879420+0.001803j
[2025-09-13 12:22:20] ✓ Checkpoint saved: checkpoint_iter_000750.pkl
[2025-09-13 12:24:02] [Iter 751/2250] R2[300/600], Temp: 0.5000, Energy: -82.836390+0.000164j
[2025-09-13 12:24:51] [Iter 752/2250] R2[301/600], Temp: 0.4974, Energy: -82.874180-0.001608j
[2025-09-13 12:26:28] [Iter 753/2250] R2[302/600], Temp: 0.4948, Energy: -82.864975+0.000692j
[2025-09-13 12:27:59] [Iter 754/2250] R2[303/600], Temp: 0.4921, Energy: -82.811203-0.002678j
[2025-09-13 12:29:31] [Iter 755/2250] R2[304/600], Temp: 0.4895, Energy: -82.845301-0.001010j
[2025-09-13 12:31:13] [Iter 756/2250] R2[305/600], Temp: 0.4869, Energy: -82.876731+0.002488j
[2025-09-13 12:32:54] [Iter 757/2250] R2[306/600], Temp: 0.4843, Energy: -82.891679+0.002704j
[2025-09-13 12:34:36] [Iter 758/2250] R2[307/600], Temp: 0.4817, Energy: -82.943257-0.004212j
[2025-09-13 12:36:18] [Iter 759/2250] R2[308/600], Temp: 0.4791, Energy: -83.047621+0.002706j
[2025-09-13 12:37:59] [Iter 760/2250] R2[309/600], Temp: 0.4764, Energy: -82.973630-0.004069j
[2025-09-13 12:39:01] [Iter 761/2250] R2[310/600], Temp: 0.4738, Energy: -82.888864-0.003463j
[2025-09-13 12:40:43] [Iter 762/2250] R2[311/600], Temp: 0.4712, Energy: -82.925393+0.004029j
[2025-09-13 12:42:03] [Iter 763/2250] R2[312/600], Temp: 0.4686, Energy: -82.936808-0.000364j
[2025-09-13 12:43:44] [Iter 764/2250] R2[313/600], Temp: 0.4660, Energy: -82.993768-0.000076j
[2025-09-13 12:45:26] [Iter 765/2250] R2[314/600], Temp: 0.4634, Energy: -82.875319-0.000605j
[2025-09-13 12:47:08] [Iter 766/2250] R2[315/600], Temp: 0.4608, Energy: -82.979283-0.002154j
[2025-09-13 12:48:49] [Iter 767/2250] R2[316/600], Temp: 0.4582, Energy: -82.906977+0.001990j
[2025-09-13 12:50:31] [Iter 768/2250] R2[317/600], Temp: 0.4556, Energy: -82.979912+0.000810j
[2025-09-13 12:52:00] [Iter 769/2250] R2[318/600], Temp: 0.4529, Energy: -82.928793+0.001719j
[2025-09-13 12:53:12] [Iter 770/2250] R2[319/600], Temp: 0.4503, Energy: -82.906881-0.003200j
[2025-09-13 12:54:54] [Iter 771/2250] R2[320/600], Temp: 0.4477, Energy: -82.917255+0.001698j
[2025-09-13 12:56:16] [Iter 772/2250] R2[321/600], Temp: 0.4451, Energy: -82.892929-0.001173j
[2025-09-13 12:57:57] [Iter 773/2250] R2[322/600], Temp: 0.4425, Energy: -82.860465+0.001182j
[2025-09-13 12:59:39] [Iter 774/2250] R2[323/600], Temp: 0.4399, Energy: -82.883707+0.000048j
[2025-09-13 13:01:20] [Iter 775/2250] R2[324/600], Temp: 0.4373, Energy: -82.873482-0.000212j
[2025-09-13 13:03:01] [Iter 776/2250] R2[325/600], Temp: 0.4347, Energy: -82.895716+0.004691j
[2025-09-13 13:04:42] [Iter 777/2250] R2[326/600], Temp: 0.4321, Energy: -82.958172+0.000021j
[2025-09-13 13:05:58] [Iter 778/2250] R2[327/600], Temp: 0.4295, Energy: -82.961383+0.001756j
[2025-09-13 13:07:24] [Iter 779/2250] R2[328/600], Temp: 0.4270, Energy: -82.862922-0.001082j
[2025-09-13 13:09:06] [Iter 780/2250] R2[329/600], Temp: 0.4244, Energy: -82.827409-0.000912j
[2025-09-13 13:10:27] [Iter 781/2250] R2[330/600], Temp: 0.4218, Energy: -82.836476-0.001961j
[2025-09-13 13:12:08] [Iter 782/2250] R2[331/600], Temp: 0.4192, Energy: -82.905088+0.002044j
[2025-09-13 13:13:50] [Iter 783/2250] R2[332/600], Temp: 0.4166, Energy: -82.877805+0.004298j
[2025-09-13 13:15:31] [Iter 784/2250] R2[333/600], Temp: 0.4140, Energy: -82.960231+0.001105j
[2025-09-13 13:17:12] [Iter 785/2250] R2[334/600], Temp: 0.4115, Energy: -82.891103-0.003480j
[2025-09-13 13:18:54] [Iter 786/2250] R2[335/600], Temp: 0.4089, Energy: -82.900727-0.002088j
[2025-09-13 13:19:55] [Iter 787/2250] R2[336/600], Temp: 0.4063, Energy: -82.869507-0.001935j
[2025-09-13 13:21:30] [Iter 788/2250] R2[337/600], Temp: 0.4037, Energy: -82.856442+0.002788j
[2025-09-13 13:23:03] [Iter 789/2250] R2[338/600], Temp: 0.4012, Energy: -82.878914-0.002522j
[2025-09-13 13:24:34] [Iter 790/2250] R2[339/600], Temp: 0.3986, Energy: -82.939783-0.005051j
[2025-09-13 13:26:16] [Iter 791/2250] R2[340/600], Temp: 0.3960, Energy: -82.968141+0.001894j
[2025-09-13 13:27:58] [Iter 792/2250] R2[341/600], Temp: 0.3935, Energy: -82.958095-0.004021j
[2025-09-13 13:29:40] [Iter 793/2250] R2[342/600], Temp: 0.3909, Energy: -82.918129-0.002238j
[2025-09-13 13:31:21] [Iter 794/2250] R2[343/600], Temp: 0.3884, Energy: -82.888948+0.000891j
[2025-09-13 13:33:03] [Iter 795/2250] R2[344/600], Temp: 0.3858, Energy: -82.852511+0.001764j
[2025-09-13 13:34:04] [Iter 796/2250] R2[345/600], Temp: 0.3833, Energy: -82.878825+0.002555j
[2025-09-13 13:35:46] [Iter 797/2250] R2[346/600], Temp: 0.3807, Energy: -82.837398+0.000037j
[2025-09-13 13:37:07] [Iter 798/2250] R2[347/600], Temp: 0.3782, Energy: -82.866986-0.001711j
[2025-09-13 13:38:48] [Iter 799/2250] R2[348/600], Temp: 0.3757, Energy: -82.930732+0.001773j
[2025-09-13 13:40:30] [Iter 800/2250] R2[349/600], Temp: 0.3731, Energy: -82.925708+0.002096j
[2025-09-13 13:42:11] [Iter 801/2250] R2[350/600], Temp: 0.3706, Energy: -82.894576+0.000686j
[2025-09-13 13:43:53] [Iter 802/2250] R2[351/600], Temp: 0.3681, Energy: -82.963086+0.001125j
[2025-09-13 13:45:34] [Iter 803/2250] R2[352/600], Temp: 0.3655, Energy: -82.816907-0.002076j
[2025-09-13 13:47:04] [Iter 804/2250] R2[353/600], Temp: 0.3630, Energy: -82.878364-0.002575j
[2025-09-13 13:48:17] [Iter 805/2250] R2[354/600], Temp: 0.3605, Energy: -82.946501-0.003401j
[2025-09-13 13:49:59] [Iter 806/2250] R2[355/600], Temp: 0.3580, Energy: -82.903357+0.001397j
[2025-09-13 13:51:21] [Iter 807/2250] R2[356/600], Temp: 0.3555, Energy: -82.885992+0.000482j
[2025-09-13 13:53:02] [Iter 808/2250] R2[357/600], Temp: 0.3530, Energy: -82.911456-0.001287j
[2025-09-13 13:54:44] [Iter 809/2250] R2[358/600], Temp: 0.3505, Energy: -82.902297-0.002691j
[2025-09-13 13:56:25] [Iter 810/2250] R2[359/600], Temp: 0.3480, Energy: -82.854629-0.002073j
[2025-09-13 13:58:07] [Iter 811/2250] R2[360/600], Temp: 0.3455, Energy: -82.920370+0.001632j
[2025-09-13 13:59:48] [Iter 812/2250] R2[361/600], Temp: 0.3430, Energy: -82.936349+0.003506j
[2025-09-13 14:01:02] [Iter 813/2250] R2[362/600], Temp: 0.3405, Energy: -82.887237+0.002180j
[2025-09-13 14:02:29] [Iter 814/2250] R2[363/600], Temp: 0.3380, Energy: -82.833711-0.000723j
[2025-09-13 14:04:11] [Iter 815/2250] R2[364/600], Temp: 0.3356, Energy: -82.859028-0.002858j
[2025-09-13 14:05:33] [Iter 816/2250] R2[365/600], Temp: 0.3331, Energy: -82.869227+0.004984j
[2025-09-13 14:07:14] [Iter 817/2250] R2[366/600], Temp: 0.3306, Energy: -82.837573+0.000250j
[2025-09-13 14:08:56] [Iter 818/2250] R2[367/600], Temp: 0.3282, Energy: -82.821982+0.001893j
[2025-09-13 14:10:38] [Iter 819/2250] R2[368/600], Temp: 0.3257, Energy: -82.829717+0.000909j
[2025-09-13 14:12:19] [Iter 820/2250] R2[369/600], Temp: 0.3233, Energy: -82.888909-0.005061j
[2025-09-13 14:14:01] [Iter 821/2250] R2[370/600], Temp: 0.3208, Energy: -82.852453+0.008027j
[2025-09-13 14:15:01] [Iter 822/2250] R2[371/600], Temp: 0.3184, Energy: -82.827829+0.000531j
[2025-09-13 14:16:39] [Iter 823/2250] R2[372/600], Temp: 0.3159, Energy: -82.867012-0.000451j
[2025-09-13 14:18:11] [Iter 824/2250] R2[373/600], Temp: 0.3135, Energy: -82.799421-0.000693j
[2025-09-13 14:19:43] [Iter 825/2250] R2[374/600], Temp: 0.3111, Energy: -82.818373+0.001678j
[2025-09-13 14:21:24] [Iter 826/2250] R2[375/600], Temp: 0.3087, Energy: -82.822628+0.001277j
[2025-09-13 14:23:06] [Iter 827/2250] R2[376/600], Temp: 0.3062, Energy: -82.877785+0.001100j
[2025-09-13 14:24:48] [Iter 828/2250] R2[377/600], Temp: 0.3038, Energy: -82.928849+0.000348j
[2025-09-13 14:26:30] [Iter 829/2250] R2[378/600], Temp: 0.3014, Energy: -82.925903+0.005118j
[2025-09-13 14:28:12] [Iter 830/2250] R2[379/600], Temp: 0.2990, Energy: -83.024897-0.009697j
[2025-09-13 14:29:02] [Iter 831/2250] R2[380/600], Temp: 0.2966, Energy: -82.922899-0.003512j
[2025-09-13 14:30:45] [Iter 832/2250] R2[381/600], Temp: 0.2942, Energy: -82.950742-0.004504j
[2025-09-13 14:32:11] [Iter 833/2250] R2[382/600], Temp: 0.2919, Energy: -82.890040+0.000208j
[2025-09-13 14:33:48] [Iter 834/2250] R2[383/600], Temp: 0.2895, Energy: -82.800035-0.003240j
[2025-09-13 14:35:30] [Iter 835/2250] R2[384/600], Temp: 0.2871, Energy: -82.842863-0.001542j
[2025-09-13 14:37:11] [Iter 836/2250] R2[385/600], Temp: 0.2847, Energy: -82.858489-0.004843j
[2025-09-13 14:38:53] [Iter 837/2250] R2[386/600], Temp: 0.2824, Energy: -82.857546-0.001086j
[2025-09-13 14:40:35] [Iter 838/2250] R2[387/600], Temp: 0.2800, Energy: -82.927544-0.001209j
[2025-09-13 14:42:10] [Iter 839/2250] R2[388/600], Temp: 0.2777, Energy: -82.969320-0.000489j
[2025-09-13 14:43:19] [Iter 840/2250] R2[389/600], Temp: 0.2753, Energy: -82.988425+0.000112j
[2025-09-13 14:45:01] [Iter 841/2250] R2[390/600], Temp: 0.2730, Energy: -82.928738+0.000179j
[2025-09-13 14:46:22] [Iter 842/2250] R2[391/600], Temp: 0.2707, Energy: -82.853373+0.005333j
[2025-09-13 14:48:04] [Iter 843/2250] R2[392/600], Temp: 0.2684, Energy: -82.890144+0.003486j
[2025-09-13 14:49:46] [Iter 844/2250] R2[393/600], Temp: 0.2660, Energy: -82.959915+0.001778j
[2025-09-13 14:51:27] [Iter 845/2250] R2[394/600], Temp: 0.2637, Energy: -83.005708+0.000468j
[2025-09-13 14:53:09] [Iter 846/2250] R2[395/600], Temp: 0.2614, Energy: -82.968521+0.000547j
[2025-09-13 14:54:50] [Iter 847/2250] R2[396/600], Temp: 0.2591, Energy: -82.975325+0.001728j
[2025-09-13 14:56:09] [Iter 848/2250] R2[397/600], Temp: 0.2568, Energy: -82.971398+0.000672j
[2025-09-13 14:57:33] [Iter 849/2250] R2[398/600], Temp: 0.2545, Energy: -82.884416-0.002566j
[2025-09-13 14:59:15] [Iter 850/2250] R2[399/600], Temp: 0.2523, Energy: -82.865820-0.003361j
[2025-09-13 15:00:37] [Iter 851/2250] R2[400/600], Temp: 0.2500, Energy: -82.804158-0.001907j
[2025-09-13 15:02:18] [Iter 852/2250] R2[401/600], Temp: 0.2477, Energy: -82.803808+0.000755j
[2025-09-13 15:04:00] [Iter 853/2250] R2[402/600], Temp: 0.2455, Energy: -82.841631+0.000686j
[2025-09-13 15:05:42] [Iter 854/2250] R2[403/600], Temp: 0.2432, Energy: -82.853492+0.000074j
[2025-09-13 15:07:23] [Iter 855/2250] R2[404/600], Temp: 0.2410, Energy: -82.944523-0.001043j
[2025-09-13 15:09:05] [Iter 856/2250] R2[405/600], Temp: 0.2388, Energy: -82.944505+0.000038j
[2025-09-13 15:10:07] [Iter 857/2250] R2[406/600], Temp: 0.2365, Energy: -82.910558-0.001340j
[2025-09-13 15:11:43] [Iter 858/2250] R2[407/600], Temp: 0.2343, Energy: -82.953055+0.001396j
[2025-09-13 15:13:15] [Iter 859/2250] R2[408/600], Temp: 0.2321, Energy: -82.865640+0.002034j
[2025-09-13 15:14:47] [Iter 860/2250] R2[409/600], Temp: 0.2299, Energy: -82.961220+0.001339j
[2025-09-13 15:16:28] [Iter 861/2250] R2[410/600], Temp: 0.2277, Energy: -82.945049-0.000463j
[2025-09-13 15:18:10] [Iter 862/2250] R2[411/600], Temp: 0.2255, Energy: -82.919189-0.000914j
[2025-09-13 15:19:52] [Iter 863/2250] R2[412/600], Temp: 0.2233, Energy: -82.865179-0.001079j
[2025-09-13 15:21:34] [Iter 864/2250] R2[413/600], Temp: 0.2211, Energy: -82.905884-0.001689j
[2025-09-13 15:23:15] [Iter 865/2250] R2[414/600], Temp: 0.2190, Energy: -82.873413+0.000328j
[2025-09-13 15:24:16] [Iter 866/2250] R2[415/600], Temp: 0.2168, Energy: -82.958715-0.000443j
[2025-09-13 15:25:58] [Iter 867/2250] R2[416/600], Temp: 0.2146, Energy: -82.853594-0.001084j
[2025-09-13 15:27:19] [Iter 868/2250] R2[417/600], Temp: 0.2125, Energy: -82.905134-0.003922j
[2025-09-13 15:29:01] [Iter 869/2250] R2[418/600], Temp: 0.2104, Energy: -82.871507-0.000392j
[2025-09-13 15:30:42] [Iter 870/2250] R2[419/600], Temp: 0.2082, Energy: -82.888172-0.002315j
[2025-09-13 15:32:24] [Iter 871/2250] R2[420/600], Temp: 0.2061, Energy: -82.885349+0.002176j
[2025-09-13 15:34:05] [Iter 872/2250] R2[421/600], Temp: 0.2040, Energy: -82.924617-0.001411j
[2025-09-13 15:35:47] [Iter 873/2250] R2[422/600], Temp: 0.2019, Energy: -82.937687+0.001901j
[2025-09-13 15:37:16] [Iter 874/2250] R2[423/600], Temp: 0.1998, Energy: -82.916988-0.002135j
[2025-09-13 15:38:30] [Iter 875/2250] R2[424/600], Temp: 0.1977, Energy: -82.932005-0.004532j
[2025-09-13 15:40:12] [Iter 876/2250] R2[425/600], Temp: 0.1956, Energy: -82.919518+0.001801j
[2025-09-13 15:41:34] [Iter 877/2250] R2[426/600], Temp: 0.1935, Energy: -82.900219-0.001829j
[2025-09-13 15:43:16] [Iter 878/2250] R2[427/600], Temp: 0.1915, Energy: -82.894991+0.005461j
[2025-09-13 15:44:57] [Iter 879/2250] R2[428/600], Temp: 0.1894, Energy: -82.930108+0.001519j
[2025-09-13 15:46:38] [Iter 880/2250] R2[429/600], Temp: 0.1874, Energy: -82.939636+0.000204j
[2025-09-13 15:48:20] [Iter 881/2250] R2[430/600], Temp: 0.1853, Energy: -82.989458+0.001229j
[2025-09-13 15:50:01] [Iter 882/2250] R2[431/600], Temp: 0.1833, Energy: -83.003667-0.000567j
[2025-09-13 15:51:15] [Iter 883/2250] R2[432/600], Temp: 0.1813, Energy: -82.934329-0.000489j
[2025-09-13 15:52:41] [Iter 884/2250] R2[433/600], Temp: 0.1793, Energy: -82.918620+0.000653j
[2025-09-13 15:54:23] [Iter 885/2250] R2[434/600], Temp: 0.1773, Energy: -82.846882-0.002195j
[2025-09-13 15:55:45] [Iter 886/2250] R2[435/600], Temp: 0.1753, Energy: -82.858241+0.003756j
[2025-09-13 15:57:18] [Iter 887/2250] R2[436/600], Temp: 0.1733, Energy: -82.926029-0.000912j
[2025-09-13 15:58:58] [Iter 888/2250] R2[437/600], Temp: 0.1713, Energy: -82.835960+0.000564j
[2025-09-13 16:00:39] [Iter 889/2250] R2[438/600], Temp: 0.1693, Energy: -82.880269+0.001222j
[2025-09-13 16:02:20] [Iter 890/2250] R2[439/600], Temp: 0.1674, Energy: -82.858263+0.001302j
[2025-09-13 16:04:02] [Iter 891/2250] R2[440/600], Temp: 0.1654, Energy: -82.912750+0.001142j
[2025-09-13 16:05:12] [Iter 892/2250] R2[441/600], Temp: 0.1635, Energy: -82.903547+0.002752j
[2025-09-13 16:06:41] [Iter 893/2250] R2[442/600], Temp: 0.1616, Energy: -82.917928+0.002490j
[2025-09-13 16:08:20] [Iter 894/2250] R2[443/600], Temp: 0.1596, Energy: -82.881588-0.001468j
[2025-09-13 16:09:45] [Iter 895/2250] R2[444/600], Temp: 0.1577, Energy: -82.857661-0.003086j
[2025-09-13 16:11:27] [Iter 896/2250] R2[445/600], Temp: 0.1558, Energy: -82.918758-0.004065j
[2025-09-13 16:13:08] [Iter 897/2250] R2[446/600], Temp: 0.1539, Energy: -82.905475+0.002614j
[2025-09-13 16:14:50] [Iter 898/2250] R2[447/600], Temp: 0.1520, Energy: -82.947813-0.004404j
[2025-09-13 16:16:32] [Iter 899/2250] R2[448/600], Temp: 0.1502, Energy: -82.951287-0.003703j
[2025-09-13 16:18:14] [Iter 900/2250] R2[449/600], Temp: 0.1483, Energy: -82.989434+0.000928j
[2025-09-13 16:19:11] [Iter 901/2250] R2[450/600], Temp: 0.1464, Energy: -82.935431-0.001142j
[2025-09-13 16:20:51] [Iter 902/2250] R2[451/600], Temp: 0.1446, Energy: -82.903741+0.002787j
[2025-09-13 16:22:23] [Iter 903/2250] R2[452/600], Temp: 0.1428, Energy: -82.957425+0.000594j
[2025-09-13 16:23:54] [Iter 904/2250] R2[453/600], Temp: 0.1409, Energy: -82.990981+0.001539j
[2025-09-13 16:25:35] [Iter 905/2250] R2[454/600], Temp: 0.1391, Energy: -82.984416-0.001096j
[2025-09-13 16:27:16] [Iter 906/2250] R2[455/600], Temp: 0.1373, Energy: -82.924565+0.001654j
[2025-09-13 16:28:57] [Iter 907/2250] R2[456/600], Temp: 0.1355, Energy: -82.924642-0.002712j
[2025-09-13 16:30:39] [Iter 908/2250] R2[457/600], Temp: 0.1337, Energy: -82.907014-0.003327j
[2025-09-13 16:32:20] [Iter 909/2250] R2[458/600], Temp: 0.1320, Energy: -82.926697-0.000989j
[2025-09-13 16:33:10] [Iter 910/2250] R2[459/600], Temp: 0.1302, Energy: -82.932269+0.001483j
[2025-09-13 16:34:52] [Iter 911/2250] R2[460/600], Temp: 0.1284, Energy: -82.934862+0.000495j
[2025-09-13 16:36:19] [Iter 912/2250] R2[461/600], Temp: 0.1267, Energy: -82.881176-0.003911j
[2025-09-13 16:37:56] [Iter 913/2250] R2[462/600], Temp: 0.1249, Energy: -82.915880+0.000235j
[2025-09-13 16:39:38] [Iter 914/2250] R2[463/600], Temp: 0.1232, Energy: -82.896216-0.001331j
[2025-09-13 16:41:20] [Iter 915/2250] R2[464/600], Temp: 0.1215, Energy: -82.882876-0.001627j
[2025-09-13 16:43:02] [Iter 916/2250] R2[465/600], Temp: 0.1198, Energy: -82.939138-0.002042j
[2025-09-13 16:44:44] [Iter 917/2250] R2[466/600], Temp: 0.1181, Energy: -82.858643+0.002609j
[2025-09-13 16:46:21] [Iter 918/2250] R2[467/600], Temp: 0.1164, Energy: -82.820020-0.002572j
[2025-09-13 16:47:28] [Iter 919/2250] R2[468/600], Temp: 0.1147, Energy: -82.898931+0.001609j
[2025-09-13 16:49:11] [Iter 920/2250] R2[469/600], Temp: 0.1131, Energy: -82.832467-0.005375j
[2025-09-13 16:50:32] [Iter 921/2250] R2[470/600], Temp: 0.1114, Energy: -82.817916+0.000088j
[2025-09-13 16:52:13] [Iter 922/2250] R2[471/600], Temp: 0.1098, Energy: -82.898885+0.003610j
[2025-09-13 16:53:54] [Iter 923/2250] R2[472/600], Temp: 0.1082, Energy: -82.910172+0.000483j
[2025-09-13 16:55:35] [Iter 924/2250] R2[473/600], Temp: 0.1065, Energy: -82.967480+0.002213j
[2025-09-13 16:57:16] [Iter 925/2250] R2[474/600], Temp: 0.1049, Energy: -82.902332-0.002892j
[2025-09-13 16:58:57] [Iter 926/2250] R2[475/600], Temp: 0.1033, Energy: -82.944057-0.001055j
[2025-09-13 17:00:17] [Iter 927/2250] R2[476/600], Temp: 0.1017, Energy: -82.949283-0.001002j
[2025-09-13 17:01:41] [Iter 928/2250] R2[477/600], Temp: 0.1002, Energy: -82.954788+0.000651j
[2025-09-13 17:03:23] [Iter 929/2250] R2[478/600], Temp: 0.0986, Energy: -82.909049+0.000703j
[2025-09-13 17:04:44] [Iter 930/2250] R2[479/600], Temp: 0.0970, Energy: -82.950962+0.003124j
[2025-09-13 17:06:25] [Iter 931/2250] R2[480/600], Temp: 0.0955, Energy: -82.967495+0.000037j
[2025-09-13 17:08:06] [Iter 932/2250] R2[481/600], Temp: 0.0940, Energy: -82.958346-0.001767j
[2025-09-13 17:09:48] [Iter 933/2250] R2[482/600], Temp: 0.0924, Energy: -82.953941+0.000476j
[2025-09-13 17:11:29] [Iter 934/2250] R2[483/600], Temp: 0.0909, Energy: -82.989143+0.000733j
[2025-09-13 17:13:11] [Iter 935/2250] R2[484/600], Temp: 0.0894, Energy: -82.929056-0.002890j
[2025-09-13 17:14:14] [Iter 936/2250] R2[485/600], Temp: 0.0879, Energy: -82.912059-0.004896j
[2025-09-13 17:15:46] [Iter 937/2250] R2[486/600], Temp: 0.0865, Energy: -82.926758+0.003228j
[2025-09-13 17:17:23] [Iter 938/2250] R2[487/600], Temp: 0.0850, Energy: -82.999197+0.002637j
[2025-09-13 17:18:50] [Iter 939/2250] R2[488/600], Temp: 0.0835, Energy: -82.913632+0.003254j
[2025-09-13 17:20:31] [Iter 940/2250] R2[489/600], Temp: 0.0821, Energy: -82.885178-0.002570j
[2025-09-13 17:22:12] [Iter 941/2250] R2[490/600], Temp: 0.0807, Energy: -82.902170+0.000674j
[2025-09-13 17:23:53] [Iter 942/2250] R2[491/600], Temp: 0.0792, Energy: -82.841070+0.000405j
[2025-09-13 17:25:34] [Iter 943/2250] R2[492/600], Temp: 0.0778, Energy: -82.951671-0.000744j
[2025-09-13 17:27:16] [Iter 944/2250] R2[493/600], Temp: 0.0764, Energy: -82.806203+0.000784j
[2025-09-13 17:28:14] [Iter 945/2250] R2[494/600], Temp: 0.0751, Energy: -82.819267-0.003800j
[2025-09-13 17:29:56] [Iter 946/2250] R2[495/600], Temp: 0.0737, Energy: -82.895406-0.000485j
[2025-09-13 17:31:22] [Iter 947/2250] R2[496/600], Temp: 0.0723, Energy: -82.848968-0.000070j
[2025-09-13 17:32:59] [Iter 948/2250] R2[497/600], Temp: 0.0710, Energy: -82.822144+0.000205j
[2025-09-13 17:34:41] [Iter 949/2250] R2[498/600], Temp: 0.0696, Energy: -82.828252+0.000529j
[2025-09-13 17:36:23] [Iter 950/2250] R2[499/600], Temp: 0.0683, Energy: -82.880717-0.003757j
[2025-09-13 17:38:05] [Iter 951/2250] R2[500/600], Temp: 0.0670, Energy: -82.865873+0.002629j
[2025-09-13 17:39:46] [Iter 952/2250] R2[501/600], Temp: 0.0657, Energy: -82.862383+0.001034j
[2025-09-13 17:41:23] [Iter 953/2250] R2[502/600], Temp: 0.0644, Energy: -82.945317+0.002163j
[2025-09-13 17:42:29] [Iter 954/2250] R2[503/600], Temp: 0.0631, Energy: -82.883563+0.001472j
[2025-09-13 17:44:11] [Iter 955/2250] R2[504/600], Temp: 0.0618, Energy: -82.917468-0.004593j
[2025-09-13 17:45:33] [Iter 956/2250] R2[505/600], Temp: 0.0606, Energy: -82.990957+0.001345j
[2025-09-13 17:47:14] [Iter 957/2250] R2[506/600], Temp: 0.0593, Energy: -83.011137+0.002579j
[2025-09-13 17:48:55] [Iter 958/2250] R2[507/600], Temp: 0.0581, Energy: -83.000013+0.003471j
[2025-09-13 17:50:36] [Iter 959/2250] R2[508/600], Temp: 0.0569, Energy: -82.996805+0.002310j
[2025-09-13 17:52:18] [Iter 960/2250] R2[509/600], Temp: 0.0557, Energy: -82.955703+0.002225j
[2025-09-13 17:53:59] [Iter 961/2250] R2[510/600], Temp: 0.0545, Energy: -83.006773+0.000511j
[2025-09-13 17:55:19] [Iter 962/2250] R2[511/600], Temp: 0.0533, Energy: -82.994234-0.001129j
[2025-09-13 17:56:43] [Iter 963/2250] R2[512/600], Temp: 0.0521, Energy: -82.962954-0.002850j
[2025-09-13 17:58:25] [Iter 964/2250] R2[513/600], Temp: 0.0510, Energy: -82.940207+0.002199j
[2025-09-13 17:59:48] [Iter 965/2250] R2[514/600], Temp: 0.0498, Energy: -83.029184-0.002387j
[2025-09-13 18:01:30] [Iter 966/2250] R2[515/600], Temp: 0.0487, Energy: -82.945327+0.000566j
[2025-09-13 18:03:12] [Iter 967/2250] R2[516/600], Temp: 0.0476, Energy: -82.915939-0.001709j
[2025-09-13 18:04:53] [Iter 968/2250] R2[517/600], Temp: 0.0465, Energy: -82.962133+0.002604j
[2025-09-13 18:06:35] [Iter 969/2250] R2[518/600], Temp: 0.0454, Energy: -82.922564-0.000361j
[2025-09-13 18:08:17] [Iter 970/2250] R2[519/600], Temp: 0.0443, Energy: -82.869928+0.002163j
[2025-09-13 18:09:18] [Iter 971/2250] R2[520/600], Temp: 0.0432, Energy: -82.780502+0.003681j
[2025-09-13 18:10:54] [Iter 972/2250] R2[521/600], Temp: 0.0422, Energy: -82.935910+0.000139j
[2025-09-13 18:12:27] [Iter 973/2250] R2[522/600], Temp: 0.0411, Energy: -82.924317+0.000727j
[2025-09-13 18:13:58] [Iter 974/2250] R2[523/600], Temp: 0.0401, Energy: -82.963718+0.002667j
[2025-09-13 18:15:39] [Iter 975/2250] R2[524/600], Temp: 0.0391, Energy: -82.957652+0.004659j
[2025-09-13 18:17:20] [Iter 976/2250] R2[525/600], Temp: 0.0381, Energy: -82.946430+0.002590j
[2025-09-13 18:19:02] [Iter 977/2250] R2[526/600], Temp: 0.0371, Energy: -82.904921-0.002333j
[2025-09-13 18:20:43] [Iter 978/2250] R2[527/600], Temp: 0.0361, Energy: -82.959139+0.000603j
[2025-09-13 18:22:24] [Iter 979/2250] R2[528/600], Temp: 0.0351, Energy: -82.981761+0.001944j
[2025-09-13 18:23:26] [Iter 980/2250] R2[529/600], Temp: 0.0342, Energy: -82.906970-0.002089j
[2025-09-13 18:25:09] [Iter 981/2250] R2[530/600], Temp: 0.0332, Energy: -82.918699-0.002046j
[2025-09-13 18:26:28] [Iter 982/2250] R2[531/600], Temp: 0.0323, Energy: -82.882841-0.001233j
[2025-09-13 18:28:10] [Iter 983/2250] R2[532/600], Temp: 0.0314, Energy: -82.873846-0.001491j
[2025-09-13 18:29:52] [Iter 984/2250] R2[533/600], Temp: 0.0305, Energy: -82.913024+0.000271j
[2025-09-13 18:31:32] [Iter 985/2250] R2[534/600], Temp: 0.0296, Energy: -82.898405+0.003023j
[2025-09-13 18:33:13] [Iter 986/2250] R2[535/600], Temp: 0.0287, Energy: -82.904669-0.000702j
[2025-09-13 18:34:58] [Iter 987/2250] R2[536/600], Temp: 0.0278, Energy: -82.936352-0.000556j
[2025-09-13 18:36:25] [Iter 988/2250] R2[537/600], Temp: 0.0270, Energy: -82.911356+0.001152j
[2025-09-13 18:37:31] [Iter 989/2250] R2[538/600], Temp: 0.0261, Energy: -82.957237+0.000395j
[2025-09-13 18:39:13] [Iter 990/2250] R2[539/600], Temp: 0.0253, Energy: -82.933345+0.000551j
[2025-09-13 18:40:34] [Iter 991/2250] R2[540/600], Temp: 0.0245, Energy: -82.992096-0.000556j
[2025-09-13 18:42:16] [Iter 992/2250] R2[541/600], Temp: 0.0237, Energy: -82.950097-0.000415j
[2025-09-13 18:43:58] [Iter 993/2250] R2[542/600], Temp: 0.0229, Energy: -82.866973+0.001559j
[2025-09-13 18:45:39] [Iter 994/2250] R2[543/600], Temp: 0.0221, Energy: -82.899102-0.000614j
[2025-09-13 18:47:21] [Iter 995/2250] R2[544/600], Temp: 0.0213, Energy: -82.936755-0.000943j
[2025-09-13 18:49:03] [Iter 996/2250] R2[545/600], Temp: 0.0206, Energy: -82.938670-0.003804j
[2025-09-13 18:50:24] [Iter 997/2250] R2[546/600], Temp: 0.0199, Energy: -82.953781+0.000528j
[2025-09-13 18:51:45] [Iter 998/2250] R2[547/600], Temp: 0.0191, Energy: -82.969649-0.003169j
[2025-09-13 18:53:28] [Iter 999/2250] R2[548/600], Temp: 0.0184, Energy: -83.040359-0.001919j
[2025-09-13 18:54:50] [Iter 1000/2250] R2[549/600], Temp: 0.0177, Energy: -82.991289+0.000842j
[2025-09-13 18:54:50] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-09-13 18:56:32] [Iter 1001/2250] R2[550/600], Temp: 0.0170, Energy: -82.901914+0.002530j
[2025-09-13 18:58:13] [Iter 1002/2250] R2[551/600], Temp: 0.0164, Energy: -83.011468-0.001897j
[2025-09-13 18:59:55] [Iter 1003/2250] R2[552/600], Temp: 0.0157, Energy: -83.039927-0.003545j
[2025-09-13 19:01:37] [Iter 1004/2250] R2[553/600], Temp: 0.0151, Energy: -83.000689-0.000985j
[2025-09-13 19:03:19] [Iter 1005/2250] R2[554/600], Temp: 0.0144, Energy: -83.018312+0.003743j
[2025-09-13 19:04:22] [Iter 1006/2250] R2[555/600], Temp: 0.0138, Energy: -82.981959+0.003601j
[2025-09-13 19:05:56] [Iter 1007/2250] R2[556/600], Temp: 0.0132, Energy: -83.036756+0.004046j
[2025-09-13 19:07:31] [Iter 1008/2250] R2[557/600], Temp: 0.0126, Energy: -82.992989+0.005941j
[2025-09-13 19:08:59] [Iter 1009/2250] R2[558/600], Temp: 0.0120, Energy: -82.932473-0.002613j
[2025-09-13 19:10:40] [Iter 1010/2250] R2[559/600], Temp: 0.0115, Energy: -83.004296+0.000866j
[2025-09-13 19:12:21] [Iter 1011/2250] R2[560/600], Temp: 0.0109, Energy: -83.013904+0.001894j
[2025-09-13 19:14:02] [Iter 1012/2250] R2[561/600], Temp: 0.0104, Energy: -82.992808+0.000625j
[2025-09-13 19:15:43] [Iter 1013/2250] R2[562/600], Temp: 0.0099, Energy: -82.920247-0.001173j
[2025-09-13 19:17:25] [Iter 1014/2250] R2[563/600], Temp: 0.0094, Energy: -82.955811+0.001846j
[2025-09-13 19:18:22] [Iter 1015/2250] R2[564/600], Temp: 0.0089, Energy: -83.005390-0.003093j
[2025-09-13 19:20:04] [Iter 1016/2250] R2[565/600], Temp: 0.0084, Energy: -82.947677+0.001009j
[2025-09-13 19:21:30] [Iter 1017/2250] R2[566/600], Temp: 0.0079, Energy: -82.948276+0.002330j
[2025-09-13 19:23:07] [Iter 1018/2250] R2[567/600], Temp: 0.0074, Energy: -82.917551+0.002785j
[2025-09-13 19:24:49] [Iter 1019/2250] R2[568/600], Temp: 0.0070, Energy: -82.933194-0.005164j
[2025-09-13 19:26:30] [Iter 1020/2250] R2[569/600], Temp: 0.0066, Energy: -82.947157-0.000064j
[2025-09-13 19:28:11] [Iter 1021/2250] R2[570/600], Temp: 0.0062, Energy: -82.949908+0.002232j
[2025-09-13 19:29:53] [Iter 1022/2250] R2[571/600], Temp: 0.0058, Energy: -82.955075+0.005808j
[2025-09-13 19:31:29] [Iter 1023/2250] R2[572/600], Temp: 0.0054, Energy: -82.873993-0.003426j
[2025-09-13 19:32:35] [Iter 1024/2250] R2[573/600], Temp: 0.0050, Energy: -82.986616-0.000168j
[2025-09-13 19:34:17] [Iter 1025/2250] R2[574/600], Temp: 0.0046, Energy: -82.965546+0.000607j
[2025-09-13 19:35:39] [Iter 1026/2250] R2[575/600], Temp: 0.0043, Energy: -82.927030-0.003498j
[2025-09-13 19:37:20] [Iter 1027/2250] R2[576/600], Temp: 0.0039, Energy: -82.930027-0.002074j
[2025-09-13 19:39:02] [Iter 1028/2250] R2[577/600], Temp: 0.0036, Energy: -82.959382+0.002141j
[2025-09-13 19:40:43] [Iter 1029/2250] R2[578/600], Temp: 0.0033, Energy: -82.972781-0.002534j
[2025-09-13 19:42:24] [Iter 1030/2250] R2[579/600], Temp: 0.0030, Energy: -82.975069-0.002722j
[2025-09-13 19:44:05] [Iter 1031/2250] R2[580/600], Temp: 0.0027, Energy: -82.991815-0.001502j
[2025-09-13 19:45:25] [Iter 1032/2250] R2[581/600], Temp: 0.0025, Energy: -82.899922-0.002480j
[2025-09-13 19:46:47] [Iter 1033/2250] R2[582/600], Temp: 0.0022, Energy: -82.875217+0.001179j
[2025-09-13 19:48:29] [Iter 1034/2250] R2[583/600], Temp: 0.0020, Energy: -82.884339-0.001373j
[2025-09-13 19:49:51] [Iter 1035/2250] R2[584/600], Temp: 0.0018, Energy: -82.863626+0.002297j
[2025-09-13 19:51:32] [Iter 1036/2250] R2[585/600], Temp: 0.0015, Energy: -82.918115-0.000685j
[2025-09-13 19:53:14] [Iter 1037/2250] R2[586/600], Temp: 0.0013, Energy: -82.901309+0.002590j
[2025-09-13 19:54:55] [Iter 1038/2250] R2[587/600], Temp: 0.0012, Energy: -82.863327+0.001065j
[2025-09-13 19:56:37] [Iter 1039/2250] R2[588/600], Temp: 0.0010, Energy: -82.841081+0.000860j
[2025-09-13 19:58:18] [Iter 1040/2250] R2[589/600], Temp: 0.0008, Energy: -82.912300+0.003452j
[2025-09-13 19:59:22] [Iter 1041/2250] R2[590/600], Temp: 0.0007, Energy: -82.962844-0.000262j
[2025-09-13 20:00:55] [Iter 1042/2250] R2[591/600], Temp: 0.0006, Energy: -82.852936-0.001950j
[2025-09-13 20:02:30] [Iter 1043/2250] R2[592/600], Temp: 0.0004, Energy: -82.851136-0.001850j
[2025-09-13 20:03:59] [Iter 1044/2250] R2[593/600], Temp: 0.0003, Energy: -82.881304+0.001539j
[2025-09-13 20:05:40] [Iter 1045/2250] R2[594/600], Temp: 0.0002, Energy: -82.899181+0.001788j
[2025-09-13 20:07:22] [Iter 1046/2250] R2[595/600], Temp: 0.0002, Energy: -82.849037+0.002068j
[2025-09-13 20:09:04] [Iter 1047/2250] R2[596/600], Temp: 0.0001, Energy: -82.897868+0.002360j
[2025-09-13 20:10:44] [Iter 1048/2250] R2[597/600], Temp: 0.0001, Energy: -82.889392+0.001740j
[2025-09-13 20:12:26] [Iter 1049/2250] R2[598/600], Temp: 0.0000, Energy: -82.839691-0.006162j
[2025-09-13 20:13:20] [Iter 1050/2250] R2[599/600], Temp: 0.0000, Energy: -82.929773-0.002132j
[2025-09-13 20:13:20] RESTART #3 | Period: 1200
[2025-09-13 20:14:06] [Iter 1051/2250] R3[0/1200], Temp: 1.0000, Energy: -82.903230-0.001423j
[2025-09-13 20:14:51] [Iter 1052/2250] R3[1/1200], Temp: 1.0000, Energy: -82.883526-0.002711j
[2025-09-13 20:15:37] [Iter 1053/2250] R3[2/1200], Temp: 1.0000, Energy: -82.927756+0.001969j
[2025-09-13 20:16:23] [Iter 1054/2250] R3[3/1200], Temp: 1.0000, Energy: -82.908280-0.000633j
[2025-09-13 20:17:09] [Iter 1055/2250] R3[4/1200], Temp: 1.0000, Energy: -82.894330-0.002916j
[2025-09-13 20:17:55] [Iter 1056/2250] R3[5/1200], Temp: 1.0000, Energy: -82.907083-0.004250j
[2025-09-13 20:18:41] [Iter 1057/2250] R3[6/1200], Temp: 0.9999, Energy: -82.796318-0.000905j
[2025-09-13 20:19:27] [Iter 1058/2250] R3[7/1200], Temp: 0.9999, Energy: -82.734212-0.000048j
[2025-09-13 20:20:13] [Iter 1059/2250] R3[8/1200], Temp: 0.9999, Energy: -82.778278+0.003417j
[2025-09-13 20:20:58] [Iter 1060/2250] R3[9/1200], Temp: 0.9999, Energy: -82.862926-0.001815j
[2025-09-13 20:21:44] [Iter 1061/2250] R3[10/1200], Temp: 0.9998, Energy: -82.912186-0.000345j
[2025-09-13 20:22:30] [Iter 1062/2250] R3[11/1200], Temp: 0.9998, Energy: -82.907497-0.000616j
[2025-09-13 20:23:16] [Iter 1063/2250] R3[12/1200], Temp: 0.9998, Energy: -82.902939-0.000338j
[2025-09-13 20:24:02] [Iter 1064/2250] R3[13/1200], Temp: 0.9997, Energy: -82.949530+0.001716j
[2025-09-13 20:24:48] [Iter 1065/2250] R3[14/1200], Temp: 0.9997, Energy: -82.904588-0.001269j
[2025-09-13 20:25:34] [Iter 1066/2250] R3[15/1200], Temp: 0.9996, Energy: -82.981146-0.000140j
[2025-09-13 20:26:20] [Iter 1067/2250] R3[16/1200], Temp: 0.9996, Energy: -82.984985-0.002523j
[2025-09-13 20:27:05] [Iter 1068/2250] R3[17/1200], Temp: 0.9995, Energy: -82.938427-0.003854j
[2025-09-13 20:27:51] [Iter 1069/2250] R3[18/1200], Temp: 0.9994, Energy: -82.933792+0.001241j
[2025-09-13 20:28:37] [Iter 1070/2250] R3[19/1200], Temp: 0.9994, Energy: -82.988039+0.002651j
[2025-09-13 20:29:23] [Iter 1071/2250] R3[20/1200], Temp: 0.9993, Energy: -82.985279+0.002685j
[2025-09-13 20:30:09] [Iter 1072/2250] R3[21/1200], Temp: 0.9992, Energy: -83.011950-0.000490j
[2025-09-13 20:30:55] [Iter 1073/2250] R3[22/1200], Temp: 0.9992, Energy: -82.988469+0.002456j
[2025-09-13 20:31:41] [Iter 1074/2250] R3[23/1200], Temp: 0.9991, Energy: -82.957125+0.000606j
[2025-09-13 20:32:27] [Iter 1075/2250] R3[24/1200], Temp: 0.9990, Energy: -82.900871-0.000137j
[2025-09-13 20:33:13] [Iter 1076/2250] R3[25/1200], Temp: 0.9989, Energy: -82.958584+0.000045j
[2025-09-13 20:33:59] [Iter 1077/2250] R3[26/1200], Temp: 0.9988, Energy: -82.852758-0.002282j
[2025-09-13 20:34:44] [Iter 1078/2250] R3[27/1200], Temp: 0.9988, Energy: -82.830129-0.000206j
[2025-09-13 20:35:30] [Iter 1079/2250] R3[28/1200], Temp: 0.9987, Energy: -82.853543+0.000784j
[2025-09-13 20:36:16] [Iter 1080/2250] R3[29/1200], Temp: 0.9986, Energy: -82.932321-0.001982j
[2025-09-13 20:37:02] [Iter 1081/2250] R3[30/1200], Temp: 0.9985, Energy: -82.955110-0.002678j
[2025-09-13 20:37:48] [Iter 1082/2250] R3[31/1200], Temp: 0.9984, Energy: -83.012973-0.003244j
[2025-09-13 20:38:34] [Iter 1083/2250] R3[32/1200], Temp: 0.9982, Energy: -82.993855-0.001073j
[2025-09-13 20:39:20] [Iter 1084/2250] R3[33/1200], Temp: 0.9981, Energy: -82.968764-0.002695j
[2025-09-13 20:40:06] [Iter 1085/2250] R3[34/1200], Temp: 0.9980, Energy: -82.976793+0.003611j
[2025-09-13 20:40:52] [Iter 1086/2250] R3[35/1200], Temp: 0.9979, Energy: -82.960756-0.000196j
[2025-09-13 20:41:37] [Iter 1087/2250] R3[36/1200], Temp: 0.9978, Energy: -83.007129+0.000729j
[2025-09-13 20:42:23] [Iter 1088/2250] R3[37/1200], Temp: 0.9977, Energy: -82.962141+0.000122j
[2025-09-13 20:43:09] [Iter 1089/2250] R3[38/1200], Temp: 0.9975, Energy: -83.003207-0.005888j
[2025-09-13 20:43:55] [Iter 1090/2250] R3[39/1200], Temp: 0.9974, Energy: -82.905188+0.000110j
[2025-09-13 20:44:41] [Iter 1091/2250] R3[40/1200], Temp: 0.9973, Energy: -83.070702-0.003999j
[2025-09-13 20:45:27] [Iter 1092/2250] R3[41/1200], Temp: 0.9971, Energy: -83.067061-0.004938j
[2025-09-13 20:46:13] [Iter 1093/2250] R3[42/1200], Temp: 0.9970, Energy: -82.997205-0.000423j
[2025-09-13 20:46:59] [Iter 1094/2250] R3[43/1200], Temp: 0.9968, Energy: -82.984661-0.004009j
[2025-09-13 20:47:44] [Iter 1095/2250] R3[44/1200], Temp: 0.9967, Energy: -83.030880+0.001666j
[2025-09-13 20:48:30] [Iter 1096/2250] R3[45/1200], Temp: 0.9965, Energy: -83.025330-0.001177j
[2025-09-13 20:49:16] [Iter 1097/2250] R3[46/1200], Temp: 0.9964, Energy: -82.992831-0.003061j
[2025-09-13 20:50:02] [Iter 1098/2250] R3[47/1200], Temp: 0.9962, Energy: -82.999519+0.001753j
[2025-09-13 20:50:48] [Iter 1099/2250] R3[48/1200], Temp: 0.9961, Energy: -82.897184-0.001656j
[2025-09-13 20:51:34] [Iter 1100/2250] R3[49/1200], Temp: 0.9959, Energy: -82.939115+0.001156j
[2025-09-13 20:52:20] [Iter 1101/2250] R3[50/1200], Temp: 0.9957, Energy: -82.951884-0.000602j
[2025-09-13 20:53:06] [Iter 1102/2250] R3[51/1200], Temp: 0.9955, Energy: -82.963805-0.003118j
[2025-09-13 20:53:52] [Iter 1103/2250] R3[52/1200], Temp: 0.9954, Energy: -82.895202-0.001005j
[2025-09-13 20:54:37] [Iter 1104/2250] R3[53/1200], Temp: 0.9952, Energy: -82.884186+0.000498j
[2025-09-13 20:55:23] [Iter 1105/2250] R3[54/1200], Temp: 0.9950, Energy: -82.828255-0.001149j
[2025-09-13 20:56:09] [Iter 1106/2250] R3[55/1200], Temp: 0.9948, Energy: -82.837043-0.002641j
[2025-09-13 20:56:55] [Iter 1107/2250] R3[56/1200], Temp: 0.9946, Energy: -82.863541-0.000383j
[2025-09-13 20:57:41] [Iter 1108/2250] R3[57/1200], Temp: 0.9944, Energy: -82.855236-0.000069j
[2025-09-13 20:58:27] [Iter 1109/2250] R3[58/1200], Temp: 0.9942, Energy: -82.844842-0.000630j
[2025-09-13 20:59:13] [Iter 1110/2250] R3[59/1200], Temp: 0.9940, Energy: -82.928092+0.002286j
[2025-09-13 20:59:59] [Iter 1111/2250] R3[60/1200], Temp: 0.9938, Energy: -82.953135-0.000060j
[2025-09-13 21:00:45] [Iter 1112/2250] R3[61/1200], Temp: 0.9936, Energy: -82.986377-0.002253j
[2025-09-13 21:01:31] [Iter 1113/2250] R3[62/1200], Temp: 0.9934, Energy: -82.944295+0.000084j
[2025-09-13 21:02:16] [Iter 1114/2250] R3[63/1200], Temp: 0.9932, Energy: -82.906191-0.003299j
[2025-09-13 21:03:02] [Iter 1115/2250] R3[64/1200], Temp: 0.9930, Energy: -82.880744+0.003132j
[2025-09-13 21:03:49] [Iter 1116/2250] R3[65/1200], Temp: 0.9928, Energy: -82.948306+0.004672j
[2025-09-13 21:04:35] [Iter 1117/2250] R3[66/1200], Temp: 0.9926, Energy: -82.930363-0.001470j
[2025-09-13 21:05:20] [Iter 1118/2250] R3[67/1200], Temp: 0.9923, Energy: -82.922447-0.000724j
[2025-09-13 21:06:06] [Iter 1119/2250] R3[68/1200], Temp: 0.9921, Energy: -82.883650+0.001249j
[2025-09-13 21:06:52] [Iter 1120/2250] R3[69/1200], Temp: 0.9919, Energy: -82.907157+0.001491j
[2025-09-13 21:07:38] [Iter 1121/2250] R3[70/1200], Temp: 0.9916, Energy: -82.905984-0.002824j
[2025-09-13 21:08:24] [Iter 1122/2250] R3[71/1200], Temp: 0.9914, Energy: -82.903621-0.001012j
[2025-09-13 21:09:10] [Iter 1123/2250] R3[72/1200], Temp: 0.9911, Energy: -82.934855-0.000133j
[2025-09-13 21:09:56] [Iter 1124/2250] R3[73/1200], Temp: 0.9909, Energy: -82.920877-0.001688j
[2025-09-13 21:10:42] [Iter 1125/2250] R3[74/1200], Temp: 0.9906, Energy: -82.940434+0.001393j
[2025-09-13 21:11:27] [Iter 1126/2250] R3[75/1200], Temp: 0.9904, Energy: -82.964792+0.003467j
[2025-09-13 21:12:13] [Iter 1127/2250] R3[76/1200], Temp: 0.9901, Energy: -82.933218+0.000483j
[2025-09-13 21:12:59] [Iter 1128/2250] R3[77/1200], Temp: 0.9899, Energy: -82.927586+0.002568j
[2025-09-13 21:13:45] [Iter 1129/2250] R3[78/1200], Temp: 0.9896, Energy: -82.919936-0.000144j
[2025-09-13 21:14:31] [Iter 1130/2250] R3[79/1200], Temp: 0.9893, Energy: -82.901305-0.002692j
[2025-09-13 21:15:17] [Iter 1131/2250] R3[80/1200], Temp: 0.9891, Energy: -82.911919+0.003151j
[2025-09-13 21:16:03] [Iter 1132/2250] R3[81/1200], Temp: 0.9888, Energy: -82.971636-0.002073j
[2025-09-13 21:16:49] [Iter 1133/2250] R3[82/1200], Temp: 0.9885, Energy: -82.911798-0.004065j
[2025-09-13 21:17:35] [Iter 1134/2250] R3[83/1200], Temp: 0.9882, Energy: -83.007187-0.001420j
[2025-09-13 21:18:20] [Iter 1135/2250] R3[84/1200], Temp: 0.9880, Energy: -83.007503+0.000133j
[2025-09-13 21:19:06] [Iter 1136/2250] R3[85/1200], Temp: 0.9877, Energy: -82.990252+0.000414j
[2025-09-13 21:19:52] [Iter 1137/2250] R3[86/1200], Temp: 0.9874, Energy: -82.981786+0.001554j
[2025-09-13 21:20:38] [Iter 1138/2250] R3[87/1200], Temp: 0.9871, Energy: -82.896593-0.000288j
[2025-09-13 21:21:24] [Iter 1139/2250] R3[88/1200], Temp: 0.9868, Energy: -82.929014-0.001551j
[2025-09-13 21:22:10] [Iter 1140/2250] R3[89/1200], Temp: 0.9865, Energy: -82.917716+0.002082j
[2025-09-13 21:22:56] [Iter 1141/2250] R3[90/1200], Temp: 0.9862, Energy: -82.917292-0.007636j
[2025-09-13 21:23:42] [Iter 1142/2250] R3[91/1200], Temp: 0.9859, Energy: -82.975338-0.005319j
[2025-09-13 21:24:28] [Iter 1143/2250] R3[92/1200], Temp: 0.9856, Energy: -83.064428-0.000412j
[2025-09-13 21:25:13] [Iter 1144/2250] R3[93/1200], Temp: 0.9853, Energy: -83.037527+0.000641j
[2025-09-13 21:25:59] [Iter 1145/2250] R3[94/1200], Temp: 0.9849, Energy: -83.057376+0.001557j
[2025-09-13 21:26:45] [Iter 1146/2250] R3[95/1200], Temp: 0.9846, Energy: -82.968168-0.002102j
[2025-09-13 21:27:31] [Iter 1147/2250] R3[96/1200], Temp: 0.9843, Energy: -82.988201+0.002856j
[2025-09-13 21:28:17] [Iter 1148/2250] R3[97/1200], Temp: 0.9840, Energy: -83.014431+0.003526j
[2025-09-13 21:29:03] [Iter 1149/2250] R3[98/1200], Temp: 0.9836, Energy: -82.972906-0.000363j
[2025-09-13 21:29:49] [Iter 1150/2250] R3[99/1200], Temp: 0.9833, Energy: -82.985472+0.001644j
[2025-09-13 21:30:35] [Iter 1151/2250] R3[100/1200], Temp: 0.9830, Energy: -82.899765+0.000988j
[2025-09-13 21:31:21] [Iter 1152/2250] R3[101/1200], Temp: 0.9826, Energy: -82.903796+0.001822j
[2025-09-13 21:32:06] [Iter 1153/2250] R3[102/1200], Temp: 0.9823, Energy: -82.866114+0.000260j
[2025-09-13 21:32:52] [Iter 1154/2250] R3[103/1200], Temp: 0.9819, Energy: -82.847119+0.000187j
[2025-09-13 21:33:38] [Iter 1155/2250] R3[104/1200], Temp: 0.9816, Energy: -82.836581-0.000231j
[2025-09-13 21:34:24] [Iter 1156/2250] R3[105/1200], Temp: 0.9812, Energy: -82.855911-0.000665j
[2025-09-13 21:35:10] [Iter 1157/2250] R3[106/1200], Temp: 0.9809, Energy: -82.916203-0.000708j
[2025-09-13 21:35:56] [Iter 1158/2250] R3[107/1200], Temp: 0.9805, Energy: -82.878685+0.000261j
[2025-09-13 21:36:42] [Iter 1159/2250] R3[108/1200], Temp: 0.9801, Energy: -82.876356+0.002581j
[2025-09-13 21:37:28] [Iter 1160/2250] R3[109/1200], Temp: 0.9798, Energy: -82.908636+0.002962j
[2025-09-13 21:38:13] [Iter 1161/2250] R3[110/1200], Temp: 0.9794, Energy: -82.933608-0.000151j
[2025-09-13 21:38:59] [Iter 1162/2250] R3[111/1200], Temp: 0.9790, Energy: -83.044055+0.002829j
[2025-09-13 21:39:45] [Iter 1163/2250] R3[112/1200], Temp: 0.9787, Energy: -82.993688-0.004068j
[2025-09-13 21:40:31] [Iter 1164/2250] R3[113/1200], Temp: 0.9783, Energy: -82.988557+0.000986j
[2025-09-13 21:41:17] [Iter 1165/2250] R3[114/1200], Temp: 0.9779, Energy: -83.053891+0.003409j
[2025-09-13 21:42:03] [Iter 1166/2250] R3[115/1200], Temp: 0.9775, Energy: -82.977814-0.001810j
[2025-09-13 21:42:49] [Iter 1167/2250] R3[116/1200], Temp: 0.9771, Energy: -83.001374-0.004504j
[2025-09-13 21:43:35] [Iter 1168/2250] R3[117/1200], Temp: 0.9767, Energy: -83.003942-0.000145j
[2025-09-13 21:44:21] [Iter 1169/2250] R3[118/1200], Temp: 0.9763, Energy: -83.086292+0.002966j
[2025-09-13 21:45:06] [Iter 1170/2250] R3[119/1200], Temp: 0.9759, Energy: -83.025223-0.003608j
[2025-09-13 21:45:52] [Iter 1171/2250] R3[120/1200], Temp: 0.9755, Energy: -82.965976+0.000447j
[2025-09-13 21:46:38] [Iter 1172/2250] R3[121/1200], Temp: 0.9751, Energy: -83.047688+0.001091j
[2025-09-13 21:47:24] [Iter 1173/2250] R3[122/1200], Temp: 0.9747, Energy: -82.940140+0.000214j
[2025-09-13 21:48:10] [Iter 1174/2250] R3[123/1200], Temp: 0.9743, Energy: -82.998639-0.001449j
[2025-09-13 21:48:56] [Iter 1175/2250] R3[124/1200], Temp: 0.9739, Energy: -82.943207+0.001770j
[2025-09-13 21:49:42] [Iter 1176/2250] R3[125/1200], Temp: 0.9735, Energy: -82.937548-0.002233j
[2025-09-13 21:50:27] [Iter 1177/2250] R3[126/1200], Temp: 0.9730, Energy: -82.923592-0.001072j
[2025-09-13 21:51:13] [Iter 1178/2250] R3[127/1200], Temp: 0.9726, Energy: -82.994884+0.003768j
[2025-09-13 21:51:59] [Iter 1179/2250] R3[128/1200], Temp: 0.9722, Energy: -82.966245+0.001112j
[2025-09-13 21:52:45] [Iter 1180/2250] R3[129/1200], Temp: 0.9718, Energy: -82.945891-0.003281j
[2025-09-13 21:53:31] [Iter 1181/2250] R3[130/1200], Temp: 0.9713, Energy: -82.971525+0.001234j
[2025-09-13 21:54:17] [Iter 1182/2250] R3[131/1200], Temp: 0.9709, Energy: -82.976139-0.001466j
[2025-09-13 21:55:03] [Iter 1183/2250] R3[132/1200], Temp: 0.9704, Energy: -82.977424+0.001956j
[2025-09-13 21:55:48] [Iter 1184/2250] R3[133/1200], Temp: 0.9700, Energy: -82.954407-0.001732j
[2025-09-13 21:56:34] [Iter 1185/2250] R3[134/1200], Temp: 0.9695, Energy: -82.993978-0.004256j
[2025-09-13 21:57:20] [Iter 1186/2250] R3[135/1200], Temp: 0.9691, Energy: -82.981646-0.001830j
[2025-09-13 21:58:06] [Iter 1187/2250] R3[136/1200], Temp: 0.9686, Energy: -83.023154-0.000337j
[2025-09-13 21:58:52] [Iter 1188/2250] R3[137/1200], Temp: 0.9682, Energy: -82.956086+0.000153j
[2025-09-13 21:59:38] [Iter 1189/2250] R3[138/1200], Temp: 0.9677, Energy: -82.883600-0.006260j
[2025-09-13 22:00:24] [Iter 1190/2250] R3[139/1200], Temp: 0.9673, Energy: -82.921723-0.001919j
[2025-09-13 22:01:10] [Iter 1191/2250] R3[140/1200], Temp: 0.9668, Energy: -82.849514-0.004017j
[2025-09-13 22:01:56] [Iter 1192/2250] R3[141/1200], Temp: 0.9663, Energy: -82.843322-0.002834j
[2025-09-13 22:02:41] [Iter 1193/2250] R3[142/1200], Temp: 0.9658, Energy: -82.850801+0.003848j
[2025-09-13 22:03:27] [Iter 1194/2250] R3[143/1200], Temp: 0.9654, Energy: -82.832266+0.000669j
[2025-09-13 22:04:13] [Iter 1195/2250] R3[144/1200], Temp: 0.9649, Energy: -82.786148-0.002736j
[2025-09-13 22:04:59] [Iter 1196/2250] R3[145/1200], Temp: 0.9644, Energy: -82.904476-0.001224j
[2025-09-13 22:05:45] [Iter 1197/2250] R3[146/1200], Temp: 0.9639, Energy: -82.873811+0.001426j
[2025-09-13 22:06:31] [Iter 1198/2250] R3[147/1200], Temp: 0.9634, Energy: -82.880417+0.000406j
[2025-09-13 22:07:17] [Iter 1199/2250] R3[148/1200], Temp: 0.9629, Energy: -82.913491-0.003920j
[2025-09-13 22:08:03] [Iter 1200/2250] R3[149/1200], Temp: 0.9624, Energy: -82.882926+0.001893j
[2025-09-13 22:08:48] [Iter 1201/2250] R3[150/1200], Temp: 0.9619, Energy: -82.857256+0.000325j
[2025-09-13 22:09:34] [Iter 1202/2250] R3[151/1200], Temp: 0.9614, Energy: -82.913597-0.001379j
[2025-09-13 22:10:20] [Iter 1203/2250] R3[152/1200], Temp: 0.9609, Energy: -82.857895-0.001280j
[2025-09-13 22:11:06] [Iter 1204/2250] R3[153/1200], Temp: 0.9604, Energy: -82.896923-0.000487j
[2025-09-13 22:11:52] [Iter 1205/2250] R3[154/1200], Temp: 0.9599, Energy: -82.982002+0.002163j
[2025-09-13 22:12:38] [Iter 1206/2250] R3[155/1200], Temp: 0.9594, Energy: -82.871481-0.000184j
[2025-09-13 22:13:24] [Iter 1207/2250] R3[156/1200], Temp: 0.9589, Energy: -82.951177-0.001270j
[2025-09-13 22:14:10] [Iter 1208/2250] R3[157/1200], Temp: 0.9584, Energy: -82.931060+0.007653j
[2025-09-13 22:14:56] [Iter 1209/2250] R3[158/1200], Temp: 0.9578, Energy: -83.016054+0.002098j
[2025-09-13 22:15:42] [Iter 1210/2250] R3[159/1200], Temp: 0.9573, Energy: -83.011436+0.000756j
[2025-09-13 22:16:28] [Iter 1211/2250] R3[160/1200], Temp: 0.9568, Energy: -83.006975-0.002143j
[2025-09-13 22:17:13] [Iter 1212/2250] R3[161/1200], Temp: 0.9562, Energy: -82.975051-0.004573j
[2025-09-13 22:17:59] [Iter 1213/2250] R3[162/1200], Temp: 0.9557, Energy: -82.992520+0.001836j
[2025-09-13 22:18:45] [Iter 1214/2250] R3[163/1200], Temp: 0.9552, Energy: -82.948228+0.001712j
[2025-09-13 22:19:31] [Iter 1215/2250] R3[164/1200], Temp: 0.9546, Energy: -82.975016+0.000546j
[2025-09-13 22:20:17] [Iter 1216/2250] R3[165/1200], Temp: 0.9541, Energy: -82.956170+0.005032j
[2025-09-13 22:21:03] [Iter 1217/2250] R3[166/1200], Temp: 0.9535, Energy: -82.924685-0.002545j
[2025-09-13 22:21:49] [Iter 1218/2250] R3[167/1200], Temp: 0.9530, Energy: -82.904883-0.002071j
[2025-09-13 22:22:35] [Iter 1219/2250] R3[168/1200], Temp: 0.9524, Energy: -82.948980-0.003673j
[2025-09-13 22:23:21] [Iter 1220/2250] R3[169/1200], Temp: 0.9519, Energy: -82.912149-0.000056j
[2025-09-13 22:24:06] [Iter 1221/2250] R3[170/1200], Temp: 0.9513, Energy: -82.931194+0.000380j
[2025-09-13 22:24:52] [Iter 1222/2250] R3[171/1200], Temp: 0.9507, Energy: -82.915236-0.001809j
[2025-09-13 22:25:38] [Iter 1223/2250] R3[172/1200], Temp: 0.9502, Energy: -82.954504-0.000746j
[2025-09-13 22:26:24] [Iter 1224/2250] R3[173/1200], Temp: 0.9496, Energy: -82.925342-0.001669j
[2025-09-13 22:27:10] [Iter 1225/2250] R3[174/1200], Temp: 0.9490, Energy: -82.865492-0.000539j
[2025-09-13 22:27:56] [Iter 1226/2250] R3[175/1200], Temp: 0.9484, Energy: -82.884880+0.000427j
[2025-09-13 22:28:42] [Iter 1227/2250] R3[176/1200], Temp: 0.9479, Energy: -82.949066+0.001966j
[2025-09-13 22:29:28] [Iter 1228/2250] R3[177/1200], Temp: 0.9473, Energy: -82.894848-0.000883j
[2025-09-13 22:30:14] [Iter 1229/2250] R3[178/1200], Temp: 0.9467, Energy: -82.909855+0.003379j
[2025-09-13 22:30:59] [Iter 1230/2250] R3[179/1200], Temp: 0.9461, Energy: -82.918456+0.004485j
[2025-09-13 22:31:45] [Iter 1231/2250] R3[180/1200], Temp: 0.9455, Energy: -82.921497-0.002411j
[2025-09-13 22:32:31] [Iter 1232/2250] R3[181/1200], Temp: 0.9449, Energy: -82.944745-0.002503j
[2025-09-13 22:33:17] [Iter 1233/2250] R3[182/1200], Temp: 0.9443, Energy: -83.047874+0.000800j
[2025-09-13 22:34:03] [Iter 1234/2250] R3[183/1200], Temp: 0.9437, Energy: -83.007244+0.000849j
[2025-09-13 22:34:49] [Iter 1235/2250] R3[184/1200], Temp: 0.9431, Energy: -82.991139+0.003388j
[2025-09-13 22:35:35] [Iter 1236/2250] R3[185/1200], Temp: 0.9425, Energy: -82.936565-0.004362j
[2025-09-13 22:36:21] [Iter 1237/2250] R3[186/1200], Temp: 0.9419, Energy: -82.973760-0.003758j
[2025-09-13 22:37:07] [Iter 1238/2250] R3[187/1200], Temp: 0.9413, Energy: -82.885623-0.000671j
[2025-09-13 22:37:53] [Iter 1239/2250] R3[188/1200], Temp: 0.9407, Energy: -82.922639-0.004030j
[2025-09-13 22:38:38] [Iter 1240/2250] R3[189/1200], Temp: 0.9400, Energy: -82.837410+0.001759j
[2025-09-13 22:39:24] [Iter 1241/2250] R3[190/1200], Temp: 0.9394, Energy: -82.821443-0.000009j
[2025-09-13 22:40:10] [Iter 1242/2250] R3[191/1200], Temp: 0.9388, Energy: -82.848513-0.000161j
[2025-09-13 22:40:56] [Iter 1243/2250] R3[192/1200], Temp: 0.9382, Energy: -82.891839+0.001985j
[2025-09-13 22:41:42] [Iter 1244/2250] R3[193/1200], Temp: 0.9375, Energy: -82.930594+0.001006j
[2025-09-13 22:42:28] [Iter 1245/2250] R3[194/1200], Temp: 0.9369, Energy: -82.882992+0.000034j
[2025-09-13 22:43:14] [Iter 1246/2250] R3[195/1200], Temp: 0.9362, Energy: -82.929748-0.000416j
[2025-09-13 22:44:00] [Iter 1247/2250] R3[196/1200], Temp: 0.9356, Energy: -82.991391+0.000312j
[2025-09-13 22:44:45] [Iter 1248/2250] R3[197/1200], Temp: 0.9350, Energy: -82.976048+0.003768j
[2025-09-13 22:45:31] [Iter 1249/2250] R3[198/1200], Temp: 0.9343, Energy: -82.965057+0.000859j
[2025-09-13 22:46:17] [Iter 1250/2250] R3[199/1200], Temp: 0.9337, Energy: -83.042537+0.001016j
[2025-09-13 22:46:17] ✓ Checkpoint saved: checkpoint_iter_001250.pkl
[2025-09-13 22:47:03] [Iter 1251/2250] R3[200/1200], Temp: 0.9330, Energy: -83.071981+0.001656j
[2025-09-13 22:47:49] [Iter 1252/2250] R3[201/1200], Temp: 0.9324, Energy: -83.023659-0.002787j
[2025-09-13 22:48:35] [Iter 1253/2250] R3[202/1200], Temp: 0.9317, Energy: -83.058243+0.002338j
[2025-09-13 22:49:21] [Iter 1254/2250] R3[203/1200], Temp: 0.9310, Energy: -82.956174+0.002297j
[2025-09-13 22:50:07] [Iter 1255/2250] R3[204/1200], Temp: 0.9304, Energy: -83.039697+0.000943j
[2025-09-13 22:50:53] [Iter 1256/2250] R3[205/1200], Temp: 0.9297, Energy: -82.981949-0.000693j
[2025-09-13 22:51:39] [Iter 1257/2250] R3[206/1200], Temp: 0.9290, Energy: -83.029206+0.001082j
[2025-09-13 22:52:25] [Iter 1258/2250] R3[207/1200], Temp: 0.9284, Energy: -82.973961+0.001079j
[2025-09-13 22:53:10] [Iter 1259/2250] R3[208/1200], Temp: 0.9277, Energy: -82.935747+0.000122j
[2025-09-13 22:53:56] [Iter 1260/2250] R3[209/1200], Temp: 0.9270, Energy: -82.947890+0.000509j
[2025-09-13 22:54:42] [Iter 1261/2250] R3[210/1200], Temp: 0.9263, Energy: -82.942125+0.001110j
[2025-09-13 22:55:28] [Iter 1262/2250] R3[211/1200], Temp: 0.9256, Energy: -82.845797-0.001159j
[2025-09-13 22:56:14] [Iter 1263/2250] R3[212/1200], Temp: 0.9249, Energy: -82.893642-0.004949j
[2025-09-13 22:57:01] [Iter 1264/2250] R3[213/1200], Temp: 0.9243, Energy: -82.922904-0.002187j
[2025-09-13 22:57:47] [Iter 1265/2250] R3[214/1200], Temp: 0.9236, Energy: -82.946240-0.001817j
[2025-09-13 22:58:33] [Iter 1266/2250] R3[215/1200], Temp: 0.9229, Energy: -82.925348+0.001274j
[2025-09-13 22:59:19] [Iter 1267/2250] R3[216/1200], Temp: 0.9222, Energy: -82.951214-0.000962j
[2025-09-13 23:00:05] [Iter 1268/2250] R3[217/1200], Temp: 0.9215, Energy: -82.915110-0.002189j
[2025-09-13 23:00:50] [Iter 1269/2250] R3[218/1200], Temp: 0.9208, Energy: -82.994100-0.003119j
[2025-09-13 23:01:36] [Iter 1270/2250] R3[219/1200], Temp: 0.9200, Energy: -83.045074-0.003855j
[2025-09-13 23:02:22] [Iter 1271/2250] R3[220/1200], Temp: 0.9193, Energy: -83.024969-0.001200j
[2025-09-13 23:03:08] [Iter 1272/2250] R3[221/1200], Temp: 0.9186, Energy: -82.981131+0.000428j
[2025-09-13 23:03:54] [Iter 1273/2250] R3[222/1200], Temp: 0.9179, Energy: -83.003101-0.000068j
[2025-09-13 23:04:40] [Iter 1274/2250] R3[223/1200], Temp: 0.9172, Energy: -82.910383+0.001821j
[2025-09-13 23:05:26] [Iter 1275/2250] R3[224/1200], Temp: 0.9165, Energy: -82.878957-0.001719j
[2025-09-13 23:06:12] [Iter 1276/2250] R3[225/1200], Temp: 0.9157, Energy: -82.879536-0.001828j
[2025-09-13 23:06:57] [Iter 1277/2250] R3[226/1200], Temp: 0.9150, Energy: -82.940171+0.000722j
[2025-09-13 23:07:43] [Iter 1278/2250] R3[227/1200], Temp: 0.9143, Energy: -83.033495-0.002635j
[2025-09-13 23:08:29] [Iter 1279/2250] R3[228/1200], Temp: 0.9135, Energy: -82.960939-0.001553j
[2025-09-13 23:09:15] [Iter 1280/2250] R3[229/1200], Temp: 0.9128, Energy: -83.044939+0.006046j
[2025-09-13 23:10:01] [Iter 1281/2250] R3[230/1200], Temp: 0.9121, Energy: -82.984688+0.000343j
[2025-09-13 23:10:47] [Iter 1282/2250] R3[231/1200], Temp: 0.9113, Energy: -82.975681-0.000667j
[2025-09-13 23:11:33] [Iter 1283/2250] R3[232/1200], Temp: 0.9106, Energy: -82.977902-0.001585j
[2025-09-13 23:12:19] [Iter 1284/2250] R3[233/1200], Temp: 0.9098, Energy: -82.914805-0.000673j
[2025-09-13 23:13:04] [Iter 1285/2250] R3[234/1200], Temp: 0.9091, Energy: -82.895189+0.004477j
[2025-09-13 23:13:50] [Iter 1286/2250] R3[235/1200], Temp: 0.9083, Energy: -82.968965+0.001175j
[2025-09-13 23:14:36] [Iter 1287/2250] R3[236/1200], Temp: 0.9076, Energy: -82.943645+0.001430j
[2025-09-13 23:15:22] [Iter 1288/2250] R3[237/1200], Temp: 0.9068, Energy: -82.896234+0.001623j
[2025-09-13 23:16:08] [Iter 1289/2250] R3[238/1200], Temp: 0.9060, Energy: -82.935064-0.000777j
[2025-09-13 23:16:54] [Iter 1290/2250] R3[239/1200], Temp: 0.9053, Energy: -82.941447-0.000337j
[2025-09-13 23:17:40] [Iter 1291/2250] R3[240/1200], Temp: 0.9045, Energy: -82.884175-0.002246j
[2025-09-13 23:18:26] [Iter 1292/2250] R3[241/1200], Temp: 0.9037, Energy: -82.922506-0.002118j
[2025-09-13 23:19:12] [Iter 1293/2250] R3[242/1200], Temp: 0.9030, Energy: -82.886669-0.002688j
[2025-09-13 23:19:58] [Iter 1294/2250] R3[243/1200], Temp: 0.9022, Energy: -82.985174-0.000163j
[2025-09-13 23:20:44] [Iter 1295/2250] R3[244/1200], Temp: 0.9014, Energy: -82.832252+0.002368j
[2025-09-13 23:21:29] [Iter 1296/2250] R3[245/1200], Temp: 0.9006, Energy: -82.868479-0.001819j
[2025-09-13 23:22:15] [Iter 1297/2250] R3[246/1200], Temp: 0.8998, Energy: -82.968098-0.002910j
[2025-09-13 23:23:01] [Iter 1298/2250] R3[247/1200], Temp: 0.8991, Energy: -82.954210+0.002395j
[2025-09-13 23:23:47] [Iter 1299/2250] R3[248/1200], Temp: 0.8983, Energy: -82.951708-0.002697j
[2025-09-13 23:24:33] [Iter 1300/2250] R3[249/1200], Temp: 0.8975, Energy: -82.898086+0.004731j
[2025-09-13 23:25:19] [Iter 1301/2250] R3[250/1200], Temp: 0.8967, Energy: -82.802173+0.001938j
[2025-09-13 23:26:05] [Iter 1302/2250] R3[251/1200], Temp: 0.8959, Energy: -82.885285-0.001411j
[2025-09-13 23:26:51] [Iter 1303/2250] R3[252/1200], Temp: 0.8951, Energy: -82.885440+0.002996j
[2025-09-13 23:27:36] [Iter 1304/2250] R3[253/1200], Temp: 0.8943, Energy: -82.913154-0.002082j
[2025-09-13 23:28:22] [Iter 1305/2250] R3[254/1200], Temp: 0.8935, Energy: -82.900053-0.000832j
[2025-09-13 23:29:08] [Iter 1306/2250] R3[255/1200], Temp: 0.8927, Energy: -82.865099+0.000011j
[2025-09-13 23:29:54] [Iter 1307/2250] R3[256/1200], Temp: 0.8918, Energy: -82.857793+0.001273j
[2025-09-13 23:30:40] [Iter 1308/2250] R3[257/1200], Temp: 0.8910, Energy: -82.929118-0.000052j
[2025-09-13 23:31:26] [Iter 1309/2250] R3[258/1200], Temp: 0.8902, Energy: -82.858980-0.000141j
[2025-09-13 23:32:12] [Iter 1310/2250] R3[259/1200], Temp: 0.8894, Energy: -82.946378-0.000630j
[2025-09-13 23:32:58] [Iter 1311/2250] R3[260/1200], Temp: 0.8886, Energy: -82.951547+0.000461j
[2025-09-13 23:33:43] [Iter 1312/2250] R3[261/1200], Temp: 0.8877, Energy: -82.906623+0.001643j
[2025-09-13 23:34:29] [Iter 1313/2250] R3[262/1200], Temp: 0.8869, Energy: -82.952491+0.001504j
[2025-09-13 23:35:15] [Iter 1314/2250] R3[263/1200], Temp: 0.8861, Energy: -82.998651-0.004911j
[2025-09-13 23:36:01] [Iter 1315/2250] R3[264/1200], Temp: 0.8853, Energy: -82.944127-0.001938j
[2025-09-13 23:36:47] [Iter 1316/2250] R3[265/1200], Temp: 0.8844, Energy: -82.945043-0.000008j
[2025-09-13 23:37:33] [Iter 1317/2250] R3[266/1200], Temp: 0.8836, Energy: -82.963655-0.000896j
[2025-09-13 23:38:19] [Iter 1318/2250] R3[267/1200], Temp: 0.8827, Energy: -83.002311-0.000662j
[2025-09-13 23:39:05] [Iter 1319/2250] R3[268/1200], Temp: 0.8819, Energy: -83.066443-0.001228j
[2025-09-13 23:39:50] [Iter 1320/2250] R3[269/1200], Temp: 0.8811, Energy: -83.045303-0.002856j
[2025-09-13 23:40:36] [Iter 1321/2250] R3[270/1200], Temp: 0.8802, Energy: -83.002689+0.000595j
[2025-09-13 23:41:22] [Iter 1322/2250] R3[271/1200], Temp: 0.8794, Energy: -82.968668+0.000451j
[2025-09-13 23:42:08] [Iter 1323/2250] R3[272/1200], Temp: 0.8785, Energy: -83.029573+0.000144j
[2025-09-13 23:42:54] [Iter 1324/2250] R3[273/1200], Temp: 0.8776, Energy: -83.037706+0.006224j
[2025-09-13 23:43:40] [Iter 1325/2250] R3[274/1200], Temp: 0.8768, Energy: -83.024949-0.003089j
[2025-09-13 23:44:26] [Iter 1326/2250] R3[275/1200], Temp: 0.8759, Energy: -82.965503+0.000770j
[2025-09-13 23:45:12] [Iter 1327/2250] R3[276/1200], Temp: 0.8751, Energy: -82.881348-0.001350j
[2025-09-13 23:45:58] [Iter 1328/2250] R3[277/1200], Temp: 0.8742, Energy: -82.916148+0.005228j
[2025-09-13 23:46:43] [Iter 1329/2250] R3[278/1200], Temp: 0.8733, Energy: -82.973536-0.001045j
[2025-09-13 23:47:29] [Iter 1330/2250] R3[279/1200], Temp: 0.8724, Energy: -82.990236+0.003325j
[2025-09-13 23:48:15] [Iter 1331/2250] R3[280/1200], Temp: 0.8716, Energy: -82.974933-0.002448j
[2025-09-13 23:49:01] [Iter 1332/2250] R3[281/1200], Temp: 0.8707, Energy: -82.916225+0.002742j
[2025-09-13 23:49:47] [Iter 1333/2250] R3[282/1200], Temp: 0.8698, Energy: -82.960811+0.001039j
[2025-09-13 23:50:33] [Iter 1334/2250] R3[283/1200], Temp: 0.8689, Energy: -82.938568+0.000639j
[2025-09-13 23:51:19] [Iter 1335/2250] R3[284/1200], Temp: 0.8680, Energy: -82.913497+0.002358j
[2025-09-13 23:52:05] [Iter 1336/2250] R3[285/1200], Temp: 0.8672, Energy: -82.928518+0.001362j
[2025-09-13 23:52:50] [Iter 1337/2250] R3[286/1200], Temp: 0.8663, Energy: -82.926861-0.000463j
[2025-09-13 23:53:36] [Iter 1338/2250] R3[287/1200], Temp: 0.8654, Energy: -82.894583-0.002700j
[2025-09-13 23:54:22] [Iter 1339/2250] R3[288/1200], Temp: 0.8645, Energy: -82.952262+0.001996j
[2025-09-13 23:55:08] [Iter 1340/2250] R3[289/1200], Temp: 0.8636, Energy: -82.887778+0.001154j
[2025-09-13 23:55:54] [Iter 1341/2250] R3[290/1200], Temp: 0.8627, Energy: -82.930702+0.000844j
[2025-09-13 23:56:40] [Iter 1342/2250] R3[291/1200], Temp: 0.8618, Energy: -82.967047+0.004528j
[2025-09-13 23:57:26] [Iter 1343/2250] R3[292/1200], Temp: 0.8609, Energy: -82.962295-0.001115j
[2025-09-13 23:58:12] [Iter 1344/2250] R3[293/1200], Temp: 0.8600, Energy: -82.960504+0.000954j
[2025-09-13 23:58:59] [Iter 1345/2250] R3[294/1200], Temp: 0.8591, Energy: -82.826453+0.001989j
[2025-09-13 23:59:45] [Iter 1346/2250] R3[295/1200], Temp: 0.8582, Energy: -82.821348+0.001620j
[2025-09-14 00:00:31] [Iter 1347/2250] R3[296/1200], Temp: 0.8572, Energy: -82.913181-0.000040j
[2025-09-14 00:01:17] [Iter 1348/2250] R3[297/1200], Temp: 0.8563, Energy: -82.867027+0.002308j
[2025-09-14 00:02:03] [Iter 1349/2250] R3[298/1200], Temp: 0.8554, Energy: -82.948559+0.001643j
[2025-09-14 00:02:49] [Iter 1350/2250] R3[299/1200], Temp: 0.8545, Energy: -82.946285-0.000823j
[2025-09-14 00:03:34] [Iter 1351/2250] R3[300/1200], Temp: 0.8536, Energy: -82.965000+0.000505j
[2025-09-14 00:04:20] [Iter 1352/2250] R3[301/1200], Temp: 0.8526, Energy: -83.003305+0.001662j
[2025-09-14 00:05:06] [Iter 1353/2250] R3[302/1200], Temp: 0.8517, Energy: -82.942414+0.005465j
[2025-09-14 00:05:52] [Iter 1354/2250] R3[303/1200], Temp: 0.8508, Energy: -82.929046+0.004084j
[2025-09-14 00:06:38] [Iter 1355/2250] R3[304/1200], Temp: 0.8498, Energy: -82.918210-0.002094j
[2025-09-14 00:07:24] [Iter 1356/2250] R3[305/1200], Temp: 0.8489, Energy: -82.890583+0.001027j
[2025-09-14 00:08:10] [Iter 1357/2250] R3[306/1200], Temp: 0.8480, Energy: -82.884619-0.001744j
[2025-09-14 00:08:56] [Iter 1358/2250] R3[307/1200], Temp: 0.8470, Energy: -82.907895-0.000019j
[2025-09-14 00:09:42] [Iter 1359/2250] R3[308/1200], Temp: 0.8461, Energy: -82.958432-0.002429j
[2025-09-14 00:10:28] [Iter 1360/2250] R3[309/1200], Temp: 0.8451, Energy: -82.932183+0.003321j
[2025-09-14 00:11:14] [Iter 1361/2250] R3[310/1200], Temp: 0.8442, Energy: -82.916034+0.001073j
[2025-09-14 00:12:00] [Iter 1362/2250] R3[311/1200], Temp: 0.8432, Energy: -82.963454-0.001058j
[2025-09-14 00:12:45] [Iter 1363/2250] R3[312/1200], Temp: 0.8423, Energy: -83.004521+0.001966j
[2025-09-14 00:13:31] [Iter 1364/2250] R3[313/1200], Temp: 0.8413, Energy: -83.010369+0.002556j
[2025-09-14 00:14:17] [Iter 1365/2250] R3[314/1200], Temp: 0.8404, Energy: -82.923432+0.001727j
[2025-09-14 00:15:03] [Iter 1366/2250] R3[315/1200], Temp: 0.8394, Energy: -82.941095-0.002912j
[2025-09-14 00:15:49] [Iter 1367/2250] R3[316/1200], Temp: 0.8384, Energy: -83.049672+0.000313j
[2025-09-14 00:16:35] [Iter 1368/2250] R3[317/1200], Temp: 0.8375, Energy: -82.945173+0.002062j
[2025-09-14 00:17:21] [Iter 1369/2250] R3[318/1200], Temp: 0.8365, Energy: -82.930769+0.001943j
[2025-09-14 00:18:07] [Iter 1370/2250] R3[319/1200], Temp: 0.8355, Energy: -82.958922+0.001842j
[2025-09-14 00:18:52] [Iter 1371/2250] R3[320/1200], Temp: 0.8346, Energy: -82.989748-0.001754j
[2025-09-14 00:19:38] [Iter 1372/2250] R3[321/1200], Temp: 0.8336, Energy: -82.949097+0.002191j
[2025-09-14 00:20:24] [Iter 1373/2250] R3[322/1200], Temp: 0.8326, Energy: -82.946484+0.002325j
[2025-09-14 00:21:10] [Iter 1374/2250] R3[323/1200], Temp: 0.8316, Energy: -82.976325-0.002852j
[2025-09-14 00:21:56] [Iter 1375/2250] R3[324/1200], Temp: 0.8307, Energy: -83.015257-0.000066j
[2025-09-14 00:22:42] [Iter 1376/2250] R3[325/1200], Temp: 0.8297, Energy: -83.037770+0.000988j
[2025-09-14 00:23:28] [Iter 1377/2250] R3[326/1200], Temp: 0.8287, Energy: -82.982654-0.001625j
[2025-09-14 00:24:14] [Iter 1378/2250] R3[327/1200], Temp: 0.8277, Energy: -82.978263-0.001330j
[2025-09-14 00:25:00] [Iter 1379/2250] R3[328/1200], Temp: 0.8267, Energy: -83.000707+0.001683j
[2025-09-14 00:25:45] [Iter 1380/2250] R3[329/1200], Temp: 0.8257, Energy: -82.983324+0.000429j
[2025-09-14 00:26:31] [Iter 1381/2250] R3[330/1200], Temp: 0.8247, Energy: -83.021588+0.003553j
[2025-09-14 00:27:17] [Iter 1382/2250] R3[331/1200], Temp: 0.8237, Energy: -82.941167+0.003137j
[2025-09-14 00:28:03] [Iter 1383/2250] R3[332/1200], Temp: 0.8227, Energy: -82.985471-0.002333j
[2025-09-14 00:28:49] [Iter 1384/2250] R3[333/1200], Temp: 0.8217, Energy: -82.986422-0.001029j
[2025-09-14 00:29:35] [Iter 1385/2250] R3[334/1200], Temp: 0.8207, Energy: -82.989441-0.001915j
[2025-09-14 00:30:21] [Iter 1386/2250] R3[335/1200], Temp: 0.8197, Energy: -82.978155+0.001735j
[2025-09-14 00:31:07] [Iter 1387/2250] R3[336/1200], Temp: 0.8187, Energy: -82.961032+0.004391j
[2025-09-14 00:31:53] [Iter 1388/2250] R3[337/1200], Temp: 0.8177, Energy: -82.914840-0.003210j
[2025-09-14 00:32:39] [Iter 1389/2250] R3[338/1200], Temp: 0.8167, Energy: -82.997495+0.001009j
[2025-09-14 00:33:24] [Iter 1390/2250] R3[339/1200], Temp: 0.8157, Energy: -82.994864+0.000759j
[2025-09-14 00:34:10] [Iter 1391/2250] R3[340/1200], Temp: 0.8147, Energy: -82.940487-0.000028j
[2025-09-14 00:34:56] [Iter 1392/2250] R3[341/1200], Temp: 0.8136, Energy: -82.947800-0.002412j
[2025-09-14 00:35:42] [Iter 1393/2250] R3[342/1200], Temp: 0.8126, Energy: -83.023864+0.002274j
[2025-09-14 00:36:28] [Iter 1394/2250] R3[343/1200], Temp: 0.8116, Energy: -83.042414+0.001329j
[2025-09-14 00:37:14] [Iter 1395/2250] R3[344/1200], Temp: 0.8106, Energy: -83.058070+0.001730j
[2025-09-14 00:38:00] [Iter 1396/2250] R3[345/1200], Temp: 0.8095, Energy: -83.041138+0.003501j
[2025-09-14 00:38:46] [Iter 1397/2250] R3[346/1200], Temp: 0.8085, Energy: -83.036998+0.006222j
[2025-09-14 00:39:32] [Iter 1398/2250] R3[347/1200], Temp: 0.8075, Energy: -83.002324+0.001300j
[2025-09-14 00:40:18] [Iter 1399/2250] R3[348/1200], Temp: 0.8065, Energy: -83.015726-0.001171j
[2025-09-14 00:41:03] [Iter 1400/2250] R3[349/1200], Temp: 0.8054, Energy: -82.977716+0.004407j
[2025-09-14 00:41:49] [Iter 1401/2250] R3[350/1200], Temp: 0.8044, Energy: -82.932972+0.001999j
[2025-09-14 00:42:35] [Iter 1402/2250] R3[351/1200], Temp: 0.8033, Energy: -82.897898-0.000392j
[2025-09-14 00:43:21] [Iter 1403/2250] R3[352/1200], Temp: 0.8023, Energy: -82.935367-0.001123j
[2025-09-14 00:44:07] [Iter 1404/2250] R3[353/1200], Temp: 0.8013, Energy: -82.970624-0.003606j
[2025-09-14 00:44:53] [Iter 1405/2250] R3[354/1200], Temp: 0.8002, Energy: -82.958913+0.000465j
[2025-09-14 00:45:39] [Iter 1406/2250] R3[355/1200], Temp: 0.7992, Energy: -82.941129-0.000415j
[2025-09-14 00:46:25] [Iter 1407/2250] R3[356/1200], Temp: 0.7981, Energy: -83.011362+0.002656j
[2025-09-14 00:47:11] [Iter 1408/2250] R3[357/1200], Temp: 0.7971, Energy: -82.940281+0.002487j
[2025-09-14 00:47:57] [Iter 1409/2250] R3[358/1200], Temp: 0.7960, Energy: -82.912912+0.002377j
[2025-09-14 00:48:42] [Iter 1410/2250] R3[359/1200], Temp: 0.7950, Energy: -82.901694-0.002506j
[2025-09-14 00:49:28] [Iter 1411/2250] R3[360/1200], Temp: 0.7939, Energy: -82.925088+0.000253j
[2025-09-14 00:50:14] [Iter 1412/2250] R3[361/1200], Temp: 0.7928, Energy: -82.979958-0.002289j
[2025-09-14 00:51:00] [Iter 1413/2250] R3[362/1200], Temp: 0.7918, Energy: -82.891052+0.000912j
[2025-09-14 00:51:46] [Iter 1414/2250] R3[363/1200], Temp: 0.7907, Energy: -82.923012-0.002967j
[2025-09-14 00:52:32] [Iter 1415/2250] R3[364/1200], Temp: 0.7896, Energy: -82.913817+0.000998j
[2025-09-14 00:53:18] [Iter 1416/2250] R3[365/1200], Temp: 0.7886, Energy: -82.891683+0.000667j
[2025-09-14 00:54:04] [Iter 1417/2250] R3[366/1200], Temp: 0.7875, Energy: -82.920201-0.000062j
[2025-09-14 00:54:50] [Iter 1418/2250] R3[367/1200], Temp: 0.7864, Energy: -82.977759-0.002515j
[2025-09-14 00:55:36] [Iter 1419/2250] R3[368/1200], Temp: 0.7854, Energy: -82.978224-0.003730j
[2025-09-14 00:56:21] [Iter 1420/2250] R3[369/1200], Temp: 0.7843, Energy: -82.960056-0.000584j
[2025-09-14 00:57:07] [Iter 1421/2250] R3[370/1200], Temp: 0.7832, Energy: -82.891417-0.001797j
[2025-09-14 00:57:53] [Iter 1422/2250] R3[371/1200], Temp: 0.7821, Energy: -82.941785-0.000730j
[2025-09-14 00:58:39] [Iter 1423/2250] R3[372/1200], Temp: 0.7810, Energy: -82.914013-0.002541j
[2025-09-14 00:59:25] [Iter 1424/2250] R3[373/1200], Temp: 0.7800, Energy: -82.976838-0.002754j
[2025-09-14 01:00:11] [Iter 1425/2250] R3[374/1200], Temp: 0.7789, Energy: -82.929359+0.001776j
[2025-09-14 01:00:57] [Iter 1426/2250] R3[375/1200], Temp: 0.7778, Energy: -82.963451-0.001074j
[2025-09-14 01:01:43] [Iter 1427/2250] R3[376/1200], Temp: 0.7767, Energy: -82.971757-0.000096j
[2025-09-14 01:02:28] [Iter 1428/2250] R3[377/1200], Temp: 0.7756, Energy: -82.879780-0.000583j
[2025-09-14 01:03:14] [Iter 1429/2250] R3[378/1200], Temp: 0.7745, Energy: -82.866443+0.003898j
[2025-09-14 01:04:00] [Iter 1430/2250] R3[379/1200], Temp: 0.7734, Energy: -82.974955+0.003332j
[2025-09-14 01:04:46] [Iter 1431/2250] R3[380/1200], Temp: 0.7723, Energy: -82.935078-0.004181j
[2025-09-14 01:05:32] [Iter 1432/2250] R3[381/1200], Temp: 0.7712, Energy: -83.000341-0.000664j
[2025-09-14 01:06:18] [Iter 1433/2250] R3[382/1200], Temp: 0.7701, Energy: -83.000394-0.000668j
[2025-09-14 01:07:04] [Iter 1434/2250] R3[383/1200], Temp: 0.7690, Energy: -83.052134-0.001340j
[2025-09-14 01:07:50] [Iter 1435/2250] R3[384/1200], Temp: 0.7679, Energy: -83.075533+0.002453j
[2025-09-14 01:08:36] [Iter 1436/2250] R3[385/1200], Temp: 0.7668, Energy: -82.969069+0.002958j
[2025-09-14 01:09:21] [Iter 1437/2250] R3[386/1200], Temp: 0.7657, Energy: -82.939975+0.002427j
[2025-09-14 01:10:07] [Iter 1438/2250] R3[387/1200], Temp: 0.7646, Energy: -82.884320+0.002549j
[2025-09-14 01:10:53] [Iter 1439/2250] R3[388/1200], Temp: 0.7635, Energy: -82.871397+0.004125j
[2025-09-14 01:11:39] [Iter 1440/2250] R3[389/1200], Temp: 0.7624, Energy: -82.912690-0.003207j
[2025-09-14 01:12:25] [Iter 1441/2250] R3[390/1200], Temp: 0.7612, Energy: -82.909966+0.002061j
[2025-09-14 01:13:11] [Iter 1442/2250] R3[391/1200], Temp: 0.7601, Energy: -82.997853-0.001768j
[2025-09-14 01:13:57] [Iter 1443/2250] R3[392/1200], Temp: 0.7590, Energy: -82.996215-0.000680j
[2025-09-14 01:14:43] [Iter 1444/2250] R3[393/1200], Temp: 0.7579, Energy: -82.945395+0.000685j
[2025-09-14 01:15:29] [Iter 1445/2250] R3[394/1200], Temp: 0.7568, Energy: -82.926025+0.000855j
[2025-09-14 01:16:15] [Iter 1446/2250] R3[395/1200], Temp: 0.7556, Energy: -82.878658+0.000145j
[2025-09-14 01:17:00] [Iter 1447/2250] R3[396/1200], Temp: 0.7545, Energy: -82.977353-0.003534j
[2025-09-14 01:17:46] [Iter 1448/2250] R3[397/1200], Temp: 0.7534, Energy: -82.882930-0.002274j
[2025-09-14 01:18:32] [Iter 1449/2250] R3[398/1200], Temp: 0.7523, Energy: -82.952940+0.002140j
[2025-09-14 01:19:18] [Iter 1450/2250] R3[399/1200], Temp: 0.7511, Energy: -83.003975+0.003597j
[2025-09-14 01:20:04] [Iter 1451/2250] R3[400/1200], Temp: 0.7500, Energy: -82.985944-0.000803j
[2025-09-14 01:20:50] [Iter 1452/2250] R3[401/1200], Temp: 0.7489, Energy: -82.953918+0.001460j
[2025-09-14 01:21:36] [Iter 1453/2250] R3[402/1200], Temp: 0.7477, Energy: -82.942529+0.002818j
[2025-09-14 01:22:22] [Iter 1454/2250] R3[403/1200], Temp: 0.7466, Energy: -82.954236-0.001426j
[2025-09-14 01:23:08] [Iter 1455/2250] R3[404/1200], Temp: 0.7455, Energy: -82.907854+0.001616j
[2025-09-14 01:23:53] [Iter 1456/2250] R3[405/1200], Temp: 0.7443, Energy: -82.936445-0.000976j
[2025-09-14 01:24:39] [Iter 1457/2250] R3[406/1200], Temp: 0.7432, Energy: -82.961770-0.001582j
[2025-09-14 01:25:25] [Iter 1458/2250] R3[407/1200], Temp: 0.7420, Energy: -82.925347+0.000071j
[2025-09-14 01:26:11] [Iter 1459/2250] R3[408/1200], Temp: 0.7409, Energy: -82.979803-0.003616j
[2025-09-14 01:26:57] [Iter 1460/2250] R3[409/1200], Temp: 0.7397, Energy: -82.876943-0.000072j
[2025-09-14 01:27:43] [Iter 1461/2250] R3[410/1200], Temp: 0.7386, Energy: -82.928886+0.005520j
[2025-09-14 01:28:29] [Iter 1462/2250] R3[411/1200], Temp: 0.7374, Energy: -82.912331-0.001991j
[2025-09-14 01:29:15] [Iter 1463/2250] R3[412/1200], Temp: 0.7363, Energy: -82.919028+0.003459j
[2025-09-14 01:30:00] [Iter 1464/2250] R3[413/1200], Temp: 0.7351, Energy: -82.946590+0.004927j
[2025-09-14 01:30:46] [Iter 1465/2250] R3[414/1200], Temp: 0.7340, Energy: -82.995929+0.001995j
[2025-09-14 01:31:32] [Iter 1466/2250] R3[415/1200], Temp: 0.7328, Energy: -82.990083+0.000935j
[2025-09-14 01:32:18] [Iter 1467/2250] R3[416/1200], Temp: 0.7316, Energy: -83.055933+0.005019j
[2025-09-14 01:33:04] [Iter 1468/2250] R3[417/1200], Temp: 0.7305, Energy: -83.002538-0.000038j
[2025-09-14 01:33:50] [Iter 1469/2250] R3[418/1200], Temp: 0.7293, Energy: -82.970455+0.001082j
[2025-09-14 01:34:36] [Iter 1470/2250] R3[419/1200], Temp: 0.7282, Energy: -82.852612-0.002677j
[2025-09-14 01:35:22] [Iter 1471/2250] R3[420/1200], Temp: 0.7270, Energy: -82.980408+0.003468j
[2025-09-14 01:36:07] [Iter 1472/2250] R3[421/1200], Temp: 0.7258, Energy: -82.916325-0.000610j
[2025-09-14 01:36:53] [Iter 1473/2250] R3[422/1200], Temp: 0.7247, Energy: -82.925330+0.001771j
[2025-09-14 01:37:39] [Iter 1474/2250] R3[423/1200], Temp: 0.7235, Energy: -82.909327+0.001458j
[2025-09-14 01:38:25] [Iter 1475/2250] R3[424/1200], Temp: 0.7223, Energy: -82.984958-0.000153j
[2025-09-14 01:39:11] [Iter 1476/2250] R3[425/1200], Temp: 0.7211, Energy: -82.984052-0.000798j
[2025-09-14 01:39:57] [Iter 1477/2250] R3[426/1200], Temp: 0.7200, Energy: -82.858221+0.000248j
[2025-09-14 01:40:43] [Iter 1478/2250] R3[427/1200], Temp: 0.7188, Energy: -82.882635-0.002312j
[2025-09-14 01:41:29] [Iter 1479/2250] R3[428/1200], Temp: 0.7176, Energy: -82.879367-0.001186j
[2025-09-14 01:42:15] [Iter 1480/2250] R3[429/1200], Temp: 0.7164, Energy: -82.894339+0.002554j
[2025-09-14 01:43:01] [Iter 1481/2250] R3[430/1200], Temp: 0.7153, Energy: -82.817306-0.001343j
[2025-09-14 01:43:46] [Iter 1482/2250] R3[431/1200], Temp: 0.7141, Energy: -82.917217-0.002557j
[2025-09-14 01:44:32] [Iter 1483/2250] R3[432/1200], Temp: 0.7129, Energy: -82.894965+0.003989j
[2025-09-14 01:45:18] [Iter 1484/2250] R3[433/1200], Temp: 0.7117, Energy: -82.901734+0.001857j
[2025-09-14 01:46:04] [Iter 1485/2250] R3[434/1200], Temp: 0.7105, Energy: -82.927564-0.000919j
[2025-09-14 01:46:50] [Iter 1486/2250] R3[435/1200], Temp: 0.7093, Energy: -82.906961+0.001402j
[2025-09-14 01:47:36] [Iter 1487/2250] R3[436/1200], Temp: 0.7081, Energy: -82.884736+0.001733j
[2025-09-14 01:48:22] [Iter 1488/2250] R3[437/1200], Temp: 0.7069, Energy: -82.965777-0.001972j
[2025-09-14 01:49:08] [Iter 1489/2250] R3[438/1200], Temp: 0.7058, Energy: -82.940741-0.001938j
[2025-09-14 01:49:54] [Iter 1490/2250] R3[439/1200], Temp: 0.7046, Energy: -82.906204-0.001076j
[2025-09-14 01:50:40] [Iter 1491/2250] R3[440/1200], Temp: 0.7034, Energy: -82.866531-0.000467j
[2025-09-14 01:51:25] [Iter 1492/2250] R3[441/1200], Temp: 0.7022, Energy: -82.850600-0.000707j
[2025-09-14 01:52:11] [Iter 1493/2250] R3[442/1200], Temp: 0.7010, Energy: -82.869217-0.000750j
[2025-09-14 01:52:57] [Iter 1494/2250] R3[443/1200], Temp: 0.6998, Energy: -82.924160-0.000566j
[2025-09-14 01:53:43] [Iter 1495/2250] R3[444/1200], Temp: 0.6986, Energy: -82.890213+0.003979j
[2025-09-14 01:54:29] [Iter 1496/2250] R3[445/1200], Temp: 0.6974, Energy: -82.908467+0.000564j
[2025-09-14 01:55:15] [Iter 1497/2250] R3[446/1200], Temp: 0.6962, Energy: -82.941192+0.000915j
[2025-09-14 01:56:00] [Iter 1498/2250] R3[447/1200], Temp: 0.6950, Energy: -82.870598+0.002983j
[2025-09-14 01:56:46] [Iter 1499/2250] R3[448/1200], Temp: 0.6938, Energy: -82.916834+0.002186j
[2025-09-14 01:57:32] [Iter 1500/2250] R3[449/1200], Temp: 0.6926, Energy: -82.920965-0.000763j
[2025-09-14 01:57:32] ✓ Checkpoint saved: checkpoint_iter_001500.pkl
[2025-09-14 01:58:18] [Iter 1501/2250] R3[450/1200], Temp: 0.6913, Energy: -82.955833+0.001425j
[2025-09-14 01:59:04] [Iter 1502/2250] R3[451/1200], Temp: 0.6901, Energy: -82.930877-0.001089j
[2025-09-14 01:59:50] [Iter 1503/2250] R3[452/1200], Temp: 0.6889, Energy: -82.941519+0.000424j
[2025-09-14 02:00:36] [Iter 1504/2250] R3[453/1200], Temp: 0.6877, Energy: -82.967286-0.002096j
[2025-09-14 02:01:21] [Iter 1505/2250] R3[454/1200], Temp: 0.6865, Energy: -82.874006-0.003623j
[2025-09-14 02:02:07] [Iter 1506/2250] R3[455/1200], Temp: 0.6853, Energy: -82.899354+0.000631j
[2025-09-14 02:02:53] [Iter 1507/2250] R3[456/1200], Temp: 0.6841, Energy: -82.855151-0.000135j
[2025-09-14 02:03:39] [Iter 1508/2250] R3[457/1200], Temp: 0.6828, Energy: -82.940980+0.001314j
[2025-09-14 02:04:25] [Iter 1509/2250] R3[458/1200], Temp: 0.6816, Energy: -82.941517-0.000710j
[2025-09-14 02:05:11] [Iter 1510/2250] R3[459/1200], Temp: 0.6804, Energy: -82.972601-0.002777j
[2025-09-14 02:05:57] [Iter 1511/2250] R3[460/1200], Temp: 0.6792, Energy: -82.937194-0.000530j
[2025-09-14 02:06:43] [Iter 1512/2250] R3[461/1200], Temp: 0.6780, Energy: -82.912262-0.003535j
[2025-09-14 02:07:29] [Iter 1513/2250] R3[462/1200], Temp: 0.6767, Energy: -82.981688+0.000299j
[2025-09-14 02:08:15] [Iter 1514/2250] R3[463/1200], Temp: 0.6755, Energy: -82.969484+0.001224j
[2025-09-14 02:09:00] [Iter 1515/2250] R3[464/1200], Temp: 0.6743, Energy: -82.976485-0.000149j
[2025-09-14 02:09:46] [Iter 1516/2250] R3[465/1200], Temp: 0.6731, Energy: -82.930044-0.000756j
[2025-09-14 02:10:32] [Iter 1517/2250] R3[466/1200], Temp: 0.6718, Energy: -83.024154-0.002720j
[2025-09-14 02:11:18] [Iter 1518/2250] R3[467/1200], Temp: 0.6706, Energy: -82.934543-0.002594j
[2025-09-14 02:12:04] [Iter 1519/2250] R3[468/1200], Temp: 0.6694, Energy: -82.899844+0.001341j
[2025-09-14 02:12:50] [Iter 1520/2250] R3[469/1200], Temp: 0.6681, Energy: -82.858749+0.001720j
[2025-09-14 02:13:36] [Iter 1521/2250] R3[470/1200], Temp: 0.6669, Energy: -82.883425-0.001970j
[2025-09-14 02:14:22] [Iter 1522/2250] R3[471/1200], Temp: 0.6657, Energy: -82.963575+0.001571j
[2025-09-14 02:15:07] [Iter 1523/2250] R3[472/1200], Temp: 0.6644, Energy: -82.914014-0.001956j
[2025-09-14 02:15:53] [Iter 1524/2250] R3[473/1200], Temp: 0.6632, Energy: -82.931504+0.001181j
[2025-09-14 02:16:39] [Iter 1525/2250] R3[474/1200], Temp: 0.6620, Energy: -82.924887-0.000425j
[2025-09-14 02:17:25] [Iter 1526/2250] R3[475/1200], Temp: 0.6607, Energy: -82.947048+0.001482j
[2025-09-14 02:18:11] [Iter 1527/2250] R3[476/1200], Temp: 0.6595, Energy: -82.901478+0.002762j
[2025-09-14 02:18:57] [Iter 1528/2250] R3[477/1200], Temp: 0.6582, Energy: -82.941022+0.002089j
[2025-09-14 02:19:43] [Iter 1529/2250] R3[478/1200], Temp: 0.6570, Energy: -82.960489+0.003440j
[2025-09-14 02:20:29] [Iter 1530/2250] R3[479/1200], Temp: 0.6558, Energy: -82.987500-0.000397j
[2025-09-14 02:21:14] [Iter 1531/2250] R3[480/1200], Temp: 0.6545, Energy: -82.907189-0.000369j
[2025-09-14 02:22:00] [Iter 1532/2250] R3[481/1200], Temp: 0.6533, Energy: -82.929213-0.000473j
[2025-09-14 02:22:46] [Iter 1533/2250] R3[482/1200], Temp: 0.6520, Energy: -82.941046-0.003552j
[2025-09-14 02:23:32] [Iter 1534/2250] R3[483/1200], Temp: 0.6508, Energy: -82.936740+0.001190j
[2025-09-14 02:24:18] [Iter 1535/2250] R3[484/1200], Temp: 0.6495, Energy: -82.965667+0.000284j
[2025-09-14 02:25:04] [Iter 1536/2250] R3[485/1200], Temp: 0.6483, Energy: -82.921804-0.000993j
[2025-09-14 02:25:50] [Iter 1537/2250] R3[486/1200], Temp: 0.6470, Energy: -82.964868+0.002279j
[2025-09-14 02:26:35] [Iter 1538/2250] R3[487/1200], Temp: 0.6458, Energy: -82.992447+0.000458j
[2025-09-14 02:27:21] [Iter 1539/2250] R3[488/1200], Temp: 0.6445, Energy: -82.968010+0.001157j
[2025-09-14 02:28:07] [Iter 1540/2250] R3[489/1200], Temp: 0.6433, Energy: -82.946726-0.000173j
[2025-09-14 02:28:53] [Iter 1541/2250] R3[490/1200], Temp: 0.6420, Energy: -82.981027-0.000010j
[2025-09-14 02:29:39] [Iter 1542/2250] R3[491/1200], Temp: 0.6408, Energy: -82.963008-0.001497j
[2025-09-14 02:30:25] [Iter 1543/2250] R3[492/1200], Temp: 0.6395, Energy: -82.991152-0.001908j
[2025-09-14 02:31:11] [Iter 1544/2250] R3[493/1200], Temp: 0.6382, Energy: -82.995467+0.000791j
[2025-09-14 02:31:56] [Iter 1545/2250] R3[494/1200], Temp: 0.6370, Energy: -82.968244-0.000094j
[2025-09-14 02:32:42] [Iter 1546/2250] R3[495/1200], Temp: 0.6357, Energy: -82.939366-0.002539j
[2025-09-14 02:33:28] [Iter 1547/2250] R3[496/1200], Temp: 0.6345, Energy: -82.897157+0.002483j
[2025-09-14 02:34:14] [Iter 1548/2250] R3[497/1200], Temp: 0.6332, Energy: -82.898043+0.002405j
[2025-09-14 02:35:00] [Iter 1549/2250] R3[498/1200], Temp: 0.6319, Energy: -82.878752+0.003904j
[2025-09-14 02:35:46] [Iter 1550/2250] R3[499/1200], Temp: 0.6307, Energy: -82.873354+0.000046j
[2025-09-14 02:36:32] [Iter 1551/2250] R3[500/1200], Temp: 0.6294, Energy: -82.900897-0.001018j
[2025-09-14 02:37:18] [Iter 1552/2250] R3[501/1200], Temp: 0.6281, Energy: -82.904339+0.000057j
[2025-09-14 02:38:03] [Iter 1553/2250] R3[502/1200], Temp: 0.6269, Energy: -83.011898-0.000335j
[2025-09-14 02:38:49] [Iter 1554/2250] R3[503/1200], Temp: 0.6256, Energy: -83.063925+0.002145j
[2025-09-14 02:39:35] [Iter 1555/2250] R3[504/1200], Temp: 0.6243, Energy: -83.073707+0.001605j
[2025-09-14 02:40:21] [Iter 1556/2250] R3[505/1200], Temp: 0.6231, Energy: -83.021734+0.000270j
[2025-09-14 02:41:07] [Iter 1557/2250] R3[506/1200], Temp: 0.6218, Energy: -83.041507-0.000422j
[2025-09-14 02:41:53] [Iter 1558/2250] R3[507/1200], Temp: 0.6205, Energy: -82.947020+0.000700j
[2025-09-14 02:42:39] [Iter 1559/2250] R3[508/1200], Temp: 0.6193, Energy: -82.973506+0.002111j
[2025-09-14 02:43:24] [Iter 1560/2250] R3[509/1200], Temp: 0.6180, Energy: -82.925288+0.000130j
[2025-09-14 02:44:10] [Iter 1561/2250] R3[510/1200], Temp: 0.6167, Energy: -83.007421-0.000662j
[2025-09-14 02:44:56] [Iter 1562/2250] R3[511/1200], Temp: 0.6154, Energy: -83.069940-0.002602j
[2025-09-14 02:45:42] [Iter 1563/2250] R3[512/1200], Temp: 0.6142, Energy: -83.012398-0.001482j
[2025-09-14 02:46:28] [Iter 1564/2250] R3[513/1200], Temp: 0.6129, Energy: -83.061002+0.000996j
[2025-09-14 02:47:14] [Iter 1565/2250] R3[514/1200], Temp: 0.6116, Energy: -82.977244+0.000250j
[2025-09-14 02:48:00] [Iter 1566/2250] R3[515/1200], Temp: 0.6103, Energy: -82.933051-0.001885j
[2025-09-14 02:48:46] [Iter 1567/2250] R3[516/1200], Temp: 0.6091, Energy: -82.959794+0.000016j
[2025-09-14 02:49:32] [Iter 1568/2250] R3[517/1200], Temp: 0.6078, Energy: -82.991225+0.001768j
[2025-09-14 02:50:17] [Iter 1569/2250] R3[518/1200], Temp: 0.6065, Energy: -82.974628+0.000979j
[2025-09-14 02:51:03] [Iter 1570/2250] R3[519/1200], Temp: 0.6052, Energy: -82.976524-0.000089j
[2025-09-14 02:51:49] [Iter 1571/2250] R3[520/1200], Temp: 0.6040, Energy: -82.991196+0.000448j
[2025-09-14 02:52:35] [Iter 1572/2250] R3[521/1200], Temp: 0.6027, Energy: -82.984792-0.001590j
[2025-09-14 02:53:21] [Iter 1573/2250] R3[522/1200], Temp: 0.6014, Energy: -83.001528+0.003290j
[2025-09-14 02:54:07] [Iter 1574/2250] R3[523/1200], Temp: 0.6001, Energy: -82.969633+0.002289j
[2025-09-14 02:54:53] [Iter 1575/2250] R3[524/1200], Temp: 0.5988, Energy: -82.927965+0.001538j
[2025-09-14 02:55:39] [Iter 1576/2250] R3[525/1200], Temp: 0.5975, Energy: -82.973163-0.001885j
[2025-09-14 02:56:24] [Iter 1577/2250] R3[526/1200], Temp: 0.5963, Energy: -82.962750+0.000378j
[2025-09-14 02:57:10] [Iter 1578/2250] R3[527/1200], Temp: 0.5950, Energy: -82.987865+0.001001j
[2025-09-14 02:57:56] [Iter 1579/2250] R3[528/1200], Temp: 0.5937, Energy: -82.938154-0.001895j
[2025-09-14 02:58:42] [Iter 1580/2250] R3[529/1200], Temp: 0.5924, Energy: -83.009963+0.002472j
[2025-09-14 02:59:28] [Iter 1581/2250] R3[530/1200], Temp: 0.5911, Energy: -82.914209+0.000034j
[2025-09-14 03:00:14] [Iter 1582/2250] R3[531/1200], Temp: 0.5898, Energy: -82.884437+0.004006j
[2025-09-14 03:01:00] [Iter 1583/2250] R3[532/1200], Temp: 0.5885, Energy: -82.950236+0.003216j
[2025-09-14 03:01:46] [Iter 1584/2250] R3[533/1200], Temp: 0.5873, Energy: -83.015771+0.002583j
[2025-09-14 03:02:31] [Iter 1585/2250] R3[534/1200], Temp: 0.5860, Energy: -82.987654-0.000700j
[2025-09-14 03:03:17] [Iter 1586/2250] R3[535/1200], Temp: 0.5847, Energy: -82.966119-0.000047j
[2025-09-14 03:04:03] [Iter 1587/2250] R3[536/1200], Temp: 0.5834, Energy: -82.924992-0.000889j
[2025-09-14 03:04:49] [Iter 1588/2250] R3[537/1200], Temp: 0.5821, Energy: -83.005132-0.001532j
[2025-09-14 03:05:35] [Iter 1589/2250] R3[538/1200], Temp: 0.5808, Energy: -82.991581+0.000922j
[2025-09-14 03:06:21] [Iter 1590/2250] R3[539/1200], Temp: 0.5795, Energy: -82.989690-0.001708j
[2025-09-14 03:07:07] [Iter 1591/2250] R3[540/1200], Temp: 0.5782, Energy: -82.954910+0.001734j
[2025-09-14 03:07:53] [Iter 1592/2250] R3[541/1200], Temp: 0.5769, Energy: -82.925235-0.001654j
[2025-09-14 03:08:39] [Iter 1593/2250] R3[542/1200], Temp: 0.5756, Energy: -82.895915+0.000466j
[2025-09-14 03:09:24] [Iter 1594/2250] R3[543/1200], Temp: 0.5743, Energy: -82.904123+0.000932j
[2025-09-14 03:10:10] [Iter 1595/2250] R3[544/1200], Temp: 0.5730, Energy: -82.872967-0.002520j
[2025-09-14 03:10:56] [Iter 1596/2250] R3[545/1200], Temp: 0.5717, Energy: -82.917186+0.000883j
[2025-09-14 03:11:42] [Iter 1597/2250] R3[546/1200], Temp: 0.5705, Energy: -82.888885+0.001294j
[2025-09-14 03:12:28] [Iter 1598/2250] R3[547/1200], Temp: 0.5692, Energy: -82.896386-0.001289j
[2025-09-14 03:13:14] [Iter 1599/2250] R3[548/1200], Temp: 0.5679, Energy: -82.824417+0.000505j
[2025-09-14 03:14:00] [Iter 1600/2250] R3[549/1200], Temp: 0.5666, Energy: -82.906543+0.002973j
[2025-09-14 03:14:45] [Iter 1601/2250] R3[550/1200], Temp: 0.5653, Energy: -82.938849+0.002915j
[2025-09-14 03:15:31] [Iter 1602/2250] R3[551/1200], Temp: 0.5640, Energy: -82.853034+0.002586j
[2025-09-14 03:16:17] [Iter 1603/2250] R3[552/1200], Temp: 0.5627, Energy: -82.848139-0.000762j
[2025-09-14 03:17:03] [Iter 1604/2250] R3[553/1200], Temp: 0.5614, Energy: -82.888764-0.004266j
[2025-09-14 03:17:49] [Iter 1605/2250] R3[554/1200], Temp: 0.5601, Energy: -82.824917-0.004893j
[2025-09-14 03:18:35] [Iter 1606/2250] R3[555/1200], Temp: 0.5588, Energy: -82.795532-0.001618j
[2025-09-14 03:19:21] [Iter 1607/2250] R3[556/1200], Temp: 0.5575, Energy: -82.884268-0.001796j
[2025-09-14 03:20:07] [Iter 1608/2250] R3[557/1200], Temp: 0.5562, Energy: -82.809800-0.000723j
[2025-09-14 03:20:52] [Iter 1609/2250] R3[558/1200], Temp: 0.5549, Energy: -82.846034-0.002837j
[2025-09-14 03:21:38] [Iter 1610/2250] R3[559/1200], Temp: 0.5536, Energy: -82.874470+0.001058j
[2025-09-14 03:22:24] [Iter 1611/2250] R3[560/1200], Temp: 0.5523, Energy: -82.876961+0.002827j
[2025-09-14 03:23:10] [Iter 1612/2250] R3[561/1200], Temp: 0.5510, Energy: -82.847156-0.001147j
[2025-09-14 03:23:56] [Iter 1613/2250] R3[562/1200], Temp: 0.5497, Energy: -82.874482-0.002470j
[2025-09-14 03:24:42] [Iter 1614/2250] R3[563/1200], Temp: 0.5484, Energy: -82.889915+0.002151j
[2025-09-14 03:25:28] [Iter 1615/2250] R3[564/1200], Temp: 0.5471, Energy: -82.916965-0.000424j
[2025-09-14 03:26:13] [Iter 1616/2250] R3[565/1200], Temp: 0.5458, Energy: -82.939991-0.001304j
[2025-09-14 03:26:59] [Iter 1617/2250] R3[566/1200], Temp: 0.5444, Energy: -82.954910-0.001760j
[2025-09-14 03:27:45] [Iter 1618/2250] R3[567/1200], Temp: 0.5431, Energy: -83.012305-0.000349j
[2025-09-14 03:28:31] [Iter 1619/2250] R3[568/1200], Temp: 0.5418, Energy: -82.951381-0.000555j
[2025-09-14 03:29:17] [Iter 1620/2250] R3[569/1200], Temp: 0.5405, Energy: -82.943036-0.001844j
[2025-09-14 03:30:03] [Iter 1621/2250] R3[570/1200], Temp: 0.5392, Energy: -82.992187+0.000228j
[2025-09-14 03:30:49] [Iter 1622/2250] R3[571/1200], Temp: 0.5379, Energy: -83.019187-0.001559j
[2025-09-14 03:31:34] [Iter 1623/2250] R3[572/1200], Temp: 0.5366, Energy: -83.019151-0.003194j
[2025-09-14 03:32:20] [Iter 1624/2250] R3[573/1200], Temp: 0.5353, Energy: -82.936131+0.002571j
[2025-09-14 03:33:06] [Iter 1625/2250] R3[574/1200], Temp: 0.5340, Energy: -82.954706+0.000589j
[2025-09-14 03:33:52] [Iter 1626/2250] R3[575/1200], Temp: 0.5327, Energy: -82.958142+0.000846j
[2025-09-14 03:34:38] [Iter 1627/2250] R3[576/1200], Temp: 0.5314, Energy: -82.894889-0.001084j
[2025-09-14 03:35:24] [Iter 1628/2250] R3[577/1200], Temp: 0.5301, Energy: -82.920344+0.001141j
[2025-09-14 03:36:10] [Iter 1629/2250] R3[578/1200], Temp: 0.5288, Energy: -82.949767+0.001773j
[2025-09-14 03:36:55] [Iter 1630/2250] R3[579/1200], Temp: 0.5275, Energy: -82.901657-0.000824j
[2025-09-14 03:37:41] [Iter 1631/2250] R3[580/1200], Temp: 0.5262, Energy: -82.928365+0.003623j
[2025-09-14 03:38:27] [Iter 1632/2250] R3[581/1200], Temp: 0.5249, Energy: -82.945261+0.002717j
[2025-09-14 03:39:13] [Iter 1633/2250] R3[582/1200], Temp: 0.5236, Energy: -82.939578-0.001232j
[2025-09-14 03:39:59] [Iter 1634/2250] R3[583/1200], Temp: 0.5222, Energy: -83.035591+0.001717j
[2025-09-14 03:40:45] [Iter 1635/2250] R3[584/1200], Temp: 0.5209, Energy: -82.927119-0.002693j
[2025-09-14 03:41:31] [Iter 1636/2250] R3[585/1200], Temp: 0.5196, Energy: -82.957001-0.000561j
[2025-09-14 03:42:17] [Iter 1637/2250] R3[586/1200], Temp: 0.5183, Energy: -82.987823-0.001678j
[2025-09-14 03:43:02] [Iter 1638/2250] R3[587/1200], Temp: 0.5170, Energy: -83.009468-0.003173j
[2025-09-14 03:43:48] [Iter 1639/2250] R3[588/1200], Temp: 0.5157, Energy: -83.000102-0.001635j
[2025-09-14 03:44:34] [Iter 1640/2250] R3[589/1200], Temp: 0.5144, Energy: -82.999117+0.001897j
[2025-09-14 03:45:20] [Iter 1641/2250] R3[590/1200], Temp: 0.5131, Energy: -82.947761-0.001529j
[2025-09-14 03:46:06] [Iter 1642/2250] R3[591/1200], Temp: 0.5118, Energy: -82.967430+0.001658j
[2025-09-14 03:46:52] [Iter 1643/2250] R3[592/1200], Temp: 0.5105, Energy: -82.984907-0.000373j
[2025-09-14 03:47:38] [Iter 1644/2250] R3[593/1200], Temp: 0.5092, Energy: -82.969827-0.001285j
[2025-09-14 03:48:24] [Iter 1645/2250] R3[594/1200], Temp: 0.5079, Energy: -83.028646-0.001533j
[2025-09-14 03:49:09] [Iter 1646/2250] R3[595/1200], Temp: 0.5065, Energy: -82.982611+0.000648j
[2025-09-14 03:49:55] [Iter 1647/2250] R3[596/1200], Temp: 0.5052, Energy: -83.029137+0.002224j
[2025-09-14 03:50:41] [Iter 1648/2250] R3[597/1200], Temp: 0.5039, Energy: -82.976008-0.001509j
[2025-09-14 03:51:27] [Iter 1649/2250] R3[598/1200], Temp: 0.5026, Energy: -82.919009+0.002803j
[2025-09-14 03:52:13] [Iter 1650/2250] R3[599/1200], Temp: 0.5013, Energy: -82.898396-0.001600j
[2025-09-14 03:52:59] [Iter 1651/2250] R3[600/1200], Temp: 0.5000, Energy: -83.021013+0.000609j
[2025-09-14 03:53:45] [Iter 1652/2250] R3[601/1200], Temp: 0.4987, Energy: -82.921783+0.003302j
[2025-09-14 03:54:30] [Iter 1653/2250] R3[602/1200], Temp: 0.4974, Energy: -82.984098-0.000200j
[2025-09-14 03:55:16] [Iter 1654/2250] R3[603/1200], Temp: 0.4961, Energy: -82.940270+0.000901j
[2025-09-14 03:56:02] [Iter 1655/2250] R3[604/1200], Temp: 0.4948, Energy: -82.873592+0.000955j
[2025-09-14 03:56:48] [Iter 1656/2250] R3[605/1200], Temp: 0.4935, Energy: -82.908953+0.001389j
[2025-09-14 03:57:34] [Iter 1657/2250] R3[606/1200], Temp: 0.4921, Energy: -82.903831-0.001360j
[2025-09-14 03:58:20] [Iter 1658/2250] R3[607/1200], Temp: 0.4908, Energy: -82.911800+0.000868j
[2025-09-14 03:59:06] [Iter 1659/2250] R3[608/1200], Temp: 0.4895, Energy: -82.864684+0.001949j
[2025-09-14 03:59:51] [Iter 1660/2250] R3[609/1200], Temp: 0.4882, Energy: -82.833871+0.000314j
[2025-09-14 04:00:37] [Iter 1661/2250] R3[610/1200], Temp: 0.4869, Energy: -82.911100+0.001750j
[2025-09-14 04:01:23] [Iter 1662/2250] R3[611/1200], Temp: 0.4856, Energy: -82.951215+0.001747j
[2025-09-14 04:02:09] [Iter 1663/2250] R3[612/1200], Temp: 0.4843, Energy: -82.962303+0.000808j
[2025-09-14 04:02:55] [Iter 1664/2250] R3[613/1200], Temp: 0.4830, Energy: -82.976098-0.003081j
[2025-09-14 04:03:41] [Iter 1665/2250] R3[614/1200], Temp: 0.4817, Energy: -82.923923-0.000798j
[2025-09-14 04:04:27] [Iter 1666/2250] R3[615/1200], Temp: 0.4804, Energy: -82.981388-0.000546j
[2025-09-14 04:05:13] [Iter 1667/2250] R3[616/1200], Temp: 0.4791, Energy: -82.903451+0.002915j
[2025-09-14 04:05:58] [Iter 1668/2250] R3[617/1200], Temp: 0.4778, Energy: -82.970364+0.001551j
[2025-09-14 04:06:44] [Iter 1669/2250] R3[618/1200], Temp: 0.4764, Energy: -82.984608+0.000838j
[2025-09-14 04:07:30] [Iter 1670/2250] R3[619/1200], Temp: 0.4751, Energy: -82.945169-0.001902j
[2025-09-14 04:08:16] [Iter 1671/2250] R3[620/1200], Temp: 0.4738, Energy: -82.907482+0.000458j
[2025-09-14 04:09:02] [Iter 1672/2250] R3[621/1200], Temp: 0.4725, Energy: -82.877555-0.000045j
[2025-09-14 04:09:48] [Iter 1673/2250] R3[622/1200], Temp: 0.4712, Energy: -82.888902-0.002547j
[2025-09-14 04:10:34] [Iter 1674/2250] R3[623/1200], Temp: 0.4699, Energy: -82.951566-0.001266j
[2025-09-14 04:11:19] [Iter 1675/2250] R3[624/1200], Temp: 0.4686, Energy: -82.852858-0.002016j
[2025-09-14 04:12:05] [Iter 1676/2250] R3[625/1200], Temp: 0.4673, Energy: -82.854666-0.004427j
[2025-09-14 04:12:51] [Iter 1677/2250] R3[626/1200], Temp: 0.4660, Energy: -82.930419+0.001577j
[2025-09-14 04:13:37] [Iter 1678/2250] R3[627/1200], Temp: 0.4647, Energy: -82.907738+0.000188j
[2025-09-14 04:14:23] [Iter 1679/2250] R3[628/1200], Temp: 0.4634, Energy: -82.924055-0.002556j
[2025-09-14 04:15:09] [Iter 1680/2250] R3[629/1200], Temp: 0.4621, Energy: -82.914158+0.000959j
[2025-09-14 04:15:55] [Iter 1681/2250] R3[630/1200], Temp: 0.4608, Energy: -82.973998-0.002622j
[2025-09-14 04:16:41] [Iter 1682/2250] R3[631/1200], Temp: 0.4595, Energy: -83.018711+0.000328j
[2025-09-14 04:17:26] [Iter 1683/2250] R3[632/1200], Temp: 0.4582, Energy: -83.005538+0.003096j
[2025-09-14 04:18:12] [Iter 1684/2250] R3[633/1200], Temp: 0.4569, Energy: -82.948360-0.001526j
[2025-09-14 04:18:58] [Iter 1685/2250] R3[634/1200], Temp: 0.4556, Energy: -83.004055-0.000614j
[2025-09-14 04:19:44] [Iter 1686/2250] R3[635/1200], Temp: 0.4542, Energy: -82.951574+0.000556j
[2025-09-14 04:20:30] [Iter 1687/2250] R3[636/1200], Temp: 0.4529, Energy: -83.056518-0.000247j
[2025-09-14 04:21:16] [Iter 1688/2250] R3[637/1200], Temp: 0.4516, Energy: -82.996662+0.002750j
[2025-09-14 04:22:02] [Iter 1689/2250] R3[638/1200], Temp: 0.4503, Energy: -82.965718+0.002140j
[2025-09-14 04:22:47] [Iter 1690/2250] R3[639/1200], Temp: 0.4490, Energy: -82.986450-0.002232j
[2025-09-14 04:23:33] [Iter 1691/2250] R3[640/1200], Temp: 0.4477, Energy: -82.933148-0.001814j
[2025-09-14 04:24:19] [Iter 1692/2250] R3[641/1200], Temp: 0.4464, Energy: -82.877984+0.002334j
[2025-09-14 04:25:05] [Iter 1693/2250] R3[642/1200], Temp: 0.4451, Energy: -82.826797-0.000436j
[2025-09-14 04:25:51] [Iter 1694/2250] R3[643/1200], Temp: 0.4438, Energy: -82.842492+0.001670j
[2025-09-14 04:26:37] [Iter 1695/2250] R3[644/1200], Temp: 0.4425, Energy: -82.888602+0.001730j
[2025-09-14 04:27:23] [Iter 1696/2250] R3[645/1200], Temp: 0.4412, Energy: -82.900533+0.000098j
[2025-09-14 04:28:08] [Iter 1697/2250] R3[646/1200], Temp: 0.4399, Energy: -82.936668-0.000786j
[2025-09-14 04:28:54] [Iter 1698/2250] R3[647/1200], Temp: 0.4386, Energy: -82.956266+0.000480j
[2025-09-14 04:29:40] [Iter 1699/2250] R3[648/1200], Temp: 0.4373, Energy: -82.899579-0.000402j
[2025-09-14 04:30:26] [Iter 1700/2250] R3[649/1200], Temp: 0.4360, Energy: -82.931046-0.002092j
[2025-09-14 04:31:12] [Iter 1701/2250] R3[650/1200], Temp: 0.4347, Energy: -82.949952-0.002187j
[2025-09-14 04:31:58] [Iter 1702/2250] R3[651/1200], Temp: 0.4334, Energy: -82.928745-0.002833j
[2025-09-14 04:32:44] [Iter 1703/2250] R3[652/1200], Temp: 0.4321, Energy: -82.959429+0.003031j
[2025-09-14 04:33:30] [Iter 1704/2250] R3[653/1200], Temp: 0.4308, Energy: -82.921362-0.004245j
[2025-09-14 04:34:16] [Iter 1705/2250] R3[654/1200], Temp: 0.4295, Energy: -82.898437-0.000782j
[2025-09-14 04:35:01] [Iter 1706/2250] R3[655/1200], Temp: 0.4283, Energy: -82.897933-0.003277j
[2025-09-14 04:35:47] [Iter 1707/2250] R3[656/1200], Temp: 0.4270, Energy: -82.870404+0.000923j
[2025-09-14 04:36:33] [Iter 1708/2250] R3[657/1200], Temp: 0.4257, Energy: -82.925232-0.004106j
[2025-09-14 04:37:19] [Iter 1709/2250] R3[658/1200], Temp: 0.4244, Energy: -82.988891+0.001820j
[2025-09-14 04:38:05] [Iter 1710/2250] R3[659/1200], Temp: 0.4231, Energy: -82.953058+0.001391j
[2025-09-14 04:38:51] [Iter 1711/2250] R3[660/1200], Temp: 0.4218, Energy: -82.963607-0.001094j
[2025-09-14 04:39:37] [Iter 1712/2250] R3[661/1200], Temp: 0.4205, Energy: -82.904178+0.001503j
[2025-09-14 04:40:23] [Iter 1713/2250] R3[662/1200], Temp: 0.4192, Energy: -82.955446-0.000614j
[2025-09-14 04:41:08] [Iter 1714/2250] R3[663/1200], Temp: 0.4179, Energy: -82.949858-0.000036j
[2025-09-14 04:41:54] [Iter 1715/2250] R3[664/1200], Temp: 0.4166, Energy: -82.976983-0.002185j
[2025-09-14 04:42:40] [Iter 1716/2250] R3[665/1200], Temp: 0.4153, Energy: -82.937886+0.000806j
[2025-09-14 04:43:26] [Iter 1717/2250] R3[666/1200], Temp: 0.4140, Energy: -82.986978+0.000346j
[2025-09-14 04:44:12] [Iter 1718/2250] R3[667/1200], Temp: 0.4127, Energy: -83.001367+0.001825j
[2025-09-14 04:44:58] [Iter 1719/2250] R3[668/1200], Temp: 0.4115, Energy: -82.940627-0.002003j
[2025-09-14 04:45:44] [Iter 1720/2250] R3[669/1200], Temp: 0.4102, Energy: -82.966729-0.000838j
[2025-09-14 04:46:30] [Iter 1721/2250] R3[670/1200], Temp: 0.4089, Energy: -82.948264+0.001207j
[2025-09-14 04:47:16] [Iter 1722/2250] R3[671/1200], Temp: 0.4076, Energy: -82.964073-0.004044j
[2025-09-14 04:48:01] [Iter 1723/2250] R3[672/1200], Temp: 0.4063, Energy: -82.991929+0.001604j
[2025-09-14 04:48:47] [Iter 1724/2250] R3[673/1200], Temp: 0.4050, Energy: -83.006915+0.002763j
[2025-09-14 04:49:33] [Iter 1725/2250] R3[674/1200], Temp: 0.4037, Energy: -82.976085-0.000684j
[2025-09-14 04:50:19] [Iter 1726/2250] R3[675/1200], Temp: 0.4025, Energy: -82.934709+0.000274j
[2025-09-14 04:51:05] [Iter 1727/2250] R3[676/1200], Temp: 0.4012, Energy: -83.010257-0.001784j
[2025-09-14 04:51:51] [Iter 1728/2250] R3[677/1200], Temp: 0.3999, Energy: -83.008024-0.003484j
[2025-09-14 04:52:37] [Iter 1729/2250] R3[678/1200], Temp: 0.3986, Energy: -83.010641-0.002697j
[2025-09-14 04:53:23] [Iter 1730/2250] R3[679/1200], Temp: 0.3973, Energy: -83.007788+0.001771j
[2025-09-14 04:54:08] [Iter 1731/2250] R3[680/1200], Temp: 0.3960, Energy: -82.943549+0.002179j
[2025-09-14 04:54:54] [Iter 1732/2250] R3[681/1200], Temp: 0.3948, Energy: -82.953772-0.002284j
[2025-09-14 04:55:40] [Iter 1733/2250] R3[682/1200], Temp: 0.3935, Energy: -82.998692+0.000862j
[2025-09-14 04:56:26] [Iter 1734/2250] R3[683/1200], Temp: 0.3922, Energy: -82.979865-0.000768j
[2025-09-14 04:57:12] [Iter 1735/2250] R3[684/1200], Temp: 0.3909, Energy: -83.011115+0.000805j
[2025-09-14 04:57:58] [Iter 1736/2250] R3[685/1200], Temp: 0.3897, Energy: -83.002102+0.001211j
[2025-09-14 04:58:44] [Iter 1737/2250] R3[686/1200], Temp: 0.3884, Energy: -83.087453-0.000705j
[2025-09-14 04:59:29] [Iter 1738/2250] R3[687/1200], Temp: 0.3871, Energy: -82.980846-0.000533j
[2025-09-14 05:00:15] [Iter 1739/2250] R3[688/1200], Temp: 0.3858, Energy: -82.913472+0.001069j
[2025-09-14 05:01:01] [Iter 1740/2250] R3[689/1200], Temp: 0.3846, Energy: -82.928421-0.001273j
[2025-09-14 05:01:47] [Iter 1741/2250] R3[690/1200], Temp: 0.3833, Energy: -82.874915-0.002239j
[2025-09-14 05:02:33] [Iter 1742/2250] R3[691/1200], Temp: 0.3820, Energy: -82.898958-0.000349j
[2025-09-14 05:03:19] [Iter 1743/2250] R3[692/1200], Temp: 0.3807, Energy: -82.935065-0.001020j
[2025-09-14 05:04:05] [Iter 1744/2250] R3[693/1200], Temp: 0.3795, Energy: -82.954727+0.000205j
[2025-09-14 05:04:51] [Iter 1745/2250] R3[694/1200], Temp: 0.3782, Energy: -82.954154+0.001899j
[2025-09-14 05:05:36] [Iter 1746/2250] R3[695/1200], Temp: 0.3769, Energy: -82.898116-0.000230j
[2025-09-14 05:06:22] [Iter 1747/2250] R3[696/1200], Temp: 0.3757, Energy: -82.882983-0.001174j
[2025-09-14 05:07:08] [Iter 1748/2250] R3[697/1200], Temp: 0.3744, Energy: -82.898605+0.002219j
[2025-09-14 05:07:54] [Iter 1749/2250] R3[698/1200], Temp: 0.3731, Energy: -82.954189+0.002914j
[2025-09-14 05:08:40] [Iter 1750/2250] R3[699/1200], Temp: 0.3719, Energy: -82.934372-0.000064j
[2025-09-14 05:08:40] ✓ Checkpoint saved: checkpoint_iter_001750.pkl
[2025-09-14 05:09:26] [Iter 1751/2250] R3[700/1200], Temp: 0.3706, Energy: -82.944313+0.003814j
[2025-09-14 05:10:12] [Iter 1752/2250] R3[701/1200], Temp: 0.3693, Energy: -82.908528-0.001926j
[2025-09-14 05:10:57] [Iter 1753/2250] R3[702/1200], Temp: 0.3681, Energy: -82.928111-0.000493j
[2025-09-14 05:11:43] [Iter 1754/2250] R3[703/1200], Temp: 0.3668, Energy: -82.933816-0.003227j
[2025-09-14 05:12:29] [Iter 1755/2250] R3[704/1200], Temp: 0.3655, Energy: -82.936384+0.003820j
[2025-09-14 05:13:15] [Iter 1756/2250] R3[705/1200], Temp: 0.3643, Energy: -82.953322-0.000502j
[2025-09-14 05:14:01] [Iter 1757/2250] R3[706/1200], Temp: 0.3630, Energy: -82.914648-0.002627j
[2025-09-14 05:14:47] [Iter 1758/2250] R3[707/1200], Temp: 0.3618, Energy: -83.005934+0.001338j
[2025-09-14 05:15:33] [Iter 1759/2250] R3[708/1200], Temp: 0.3605, Energy: -82.926821-0.000324j
[2025-09-14 05:16:18] [Iter 1760/2250] R3[709/1200], Temp: 0.3592, Energy: -82.973554-0.000678j
[2025-09-14 05:17:04] [Iter 1761/2250] R3[710/1200], Temp: 0.3580, Energy: -82.919138-0.002547j
[2025-09-14 05:17:50] [Iter 1762/2250] R3[711/1200], Temp: 0.3567, Energy: -82.957032+0.003454j
[2025-09-14 05:18:36] [Iter 1763/2250] R3[712/1200], Temp: 0.3555, Energy: -83.012503-0.003275j
[2025-09-14 05:19:22] [Iter 1764/2250] R3[713/1200], Temp: 0.3542, Energy: -82.964619-0.000714j
[2025-09-14 05:20:08] [Iter 1765/2250] R3[714/1200], Temp: 0.3530, Energy: -82.977979-0.002402j
[2025-09-14 05:20:54] [Iter 1766/2250] R3[715/1200], Temp: 0.3517, Energy: -82.955930-0.000047j
[2025-09-14 05:21:40] [Iter 1767/2250] R3[716/1200], Temp: 0.3505, Energy: -82.995930+0.000472j
[2025-09-14 05:22:25] [Iter 1768/2250] R3[717/1200], Temp: 0.3492, Energy: -82.953150+0.000246j
[2025-09-14 05:23:11] [Iter 1769/2250] R3[718/1200], Temp: 0.3480, Energy: -82.986844+0.001526j
[2025-09-14 05:23:57] [Iter 1770/2250] R3[719/1200], Temp: 0.3467, Energy: -83.039392-0.000942j
[2025-09-14 05:24:43] [Iter 1771/2250] R3[720/1200], Temp: 0.3455, Energy: -83.026191+0.000146j
[2025-09-14 05:25:29] [Iter 1772/2250] R3[721/1200], Temp: 0.3442, Energy: -83.008452-0.001552j
[2025-09-14 05:26:15] [Iter 1773/2250] R3[722/1200], Temp: 0.3430, Energy: -83.002153-0.002568j
[2025-09-14 05:27:01] [Iter 1774/2250] R3[723/1200], Temp: 0.3418, Energy: -82.970600-0.001947j
[2025-09-14 05:27:46] [Iter 1775/2250] R3[724/1200], Temp: 0.3405, Energy: -82.807495-0.000452j
[2025-09-14 05:28:32] [Iter 1776/2250] R3[725/1200], Temp: 0.3393, Energy: -82.907162+0.001217j
[2025-09-14 05:29:18] [Iter 1777/2250] R3[726/1200], Temp: 0.3380, Energy: -82.891505+0.001591j
[2025-09-14 05:30:04] [Iter 1778/2250] R3[727/1200], Temp: 0.3368, Energy: -82.915779-0.000965j
[2025-09-14 05:30:50] [Iter 1779/2250] R3[728/1200], Temp: 0.3356, Energy: -82.950101+0.000611j
[2025-09-14 05:31:36] [Iter 1780/2250] R3[729/1200], Temp: 0.3343, Energy: -82.927874-0.001335j
[2025-09-14 05:32:22] [Iter 1781/2250] R3[730/1200], Temp: 0.3331, Energy: -82.986610+0.000899j
[2025-09-14 05:33:07] [Iter 1782/2250] R3[731/1200], Temp: 0.3319, Energy: -82.905101+0.000580j
[2025-09-14 05:33:53] [Iter 1783/2250] R3[732/1200], Temp: 0.3306, Energy: -82.849730-0.001999j
[2025-09-14 05:34:39] [Iter 1784/2250] R3[733/1200], Temp: 0.3294, Energy: -82.923220-0.005518j
[2025-09-14 05:35:25] [Iter 1785/2250] R3[734/1200], Temp: 0.3282, Energy: -82.877949+0.001198j
[2025-09-14 05:36:11] [Iter 1786/2250] R3[735/1200], Temp: 0.3269, Energy: -82.967085+0.001402j
[2025-09-14 05:36:57] [Iter 1787/2250] R3[736/1200], Temp: 0.3257, Energy: -83.004742+0.000106j
[2025-09-14 05:37:43] [Iter 1788/2250] R3[737/1200], Temp: 0.3245, Energy: -82.956954+0.001908j
[2025-09-14 05:38:28] [Iter 1789/2250] R3[738/1200], Temp: 0.3233, Energy: -82.963585-0.000062j
[2025-09-14 05:39:14] [Iter 1790/2250] R3[739/1200], Temp: 0.3220, Energy: -82.999225-0.003063j
[2025-09-14 05:40:00] [Iter 1791/2250] R3[740/1200], Temp: 0.3208, Energy: -82.990279+0.000748j
[2025-09-14 05:40:46] [Iter 1792/2250] R3[741/1200], Temp: 0.3196, Energy: -82.995968-0.001931j
[2025-09-14 05:41:32] [Iter 1793/2250] R3[742/1200], Temp: 0.3184, Energy: -83.037554-0.001727j
[2025-09-14 05:42:18] [Iter 1794/2250] R3[743/1200], Temp: 0.3172, Energy: -82.993472-0.002873j
[2025-09-14 05:43:03] [Iter 1795/2250] R3[744/1200], Temp: 0.3159, Energy: -82.980426-0.001139j
[2025-09-14 05:43:49] [Iter 1796/2250] R3[745/1200], Temp: 0.3147, Energy: -82.956846+0.004211j
[2025-09-14 05:44:35] [Iter 1797/2250] R3[746/1200], Temp: 0.3135, Energy: -82.923912-0.000024j
[2025-09-14 05:45:21] [Iter 1798/2250] R3[747/1200], Temp: 0.3123, Energy: -82.945749+0.000842j
[2025-09-14 05:46:07] [Iter 1799/2250] R3[748/1200], Temp: 0.3111, Energy: -83.012621-0.001918j
[2025-09-14 05:46:53] [Iter 1800/2250] R3[749/1200], Temp: 0.3099, Energy: -82.953626-0.000617j
[2025-09-14 05:47:39] [Iter 1801/2250] R3[750/1200], Temp: 0.3087, Energy: -83.003399+0.000578j
[2025-09-14 05:48:24] [Iter 1802/2250] R3[751/1200], Temp: 0.3074, Energy: -82.968788-0.000695j
[2025-09-14 05:49:10] [Iter 1803/2250] R3[752/1200], Temp: 0.3062, Energy: -82.962132-0.001140j
[2025-09-14 05:49:56] [Iter 1804/2250] R3[753/1200], Temp: 0.3050, Energy: -82.931972+0.000745j
[2025-09-14 05:50:42] [Iter 1805/2250] R3[754/1200], Temp: 0.3038, Energy: -82.955289-0.000102j
[2025-09-14 05:51:28] [Iter 1806/2250] R3[755/1200], Temp: 0.3026, Energy: -82.922868-0.000529j
[2025-09-14 05:52:14] [Iter 1807/2250] R3[756/1200], Temp: 0.3014, Energy: -82.956983-0.000661j
[2025-09-14 05:53:00] [Iter 1808/2250] R3[757/1200], Temp: 0.3002, Energy: -82.934140-0.001215j
[2025-09-14 05:53:46] [Iter 1809/2250] R3[758/1200], Temp: 0.2990, Energy: -82.993845+0.002579j
[2025-09-14 05:54:31] [Iter 1810/2250] R3[759/1200], Temp: 0.2978, Energy: -83.044873-0.000607j
[2025-09-14 05:55:17] [Iter 1811/2250] R3[760/1200], Temp: 0.2966, Energy: -83.003796+0.002244j
[2025-09-14 05:56:03] [Iter 1812/2250] R3[761/1200], Temp: 0.2954, Energy: -82.928295-0.001206j
[2025-09-14 05:56:49] [Iter 1813/2250] R3[762/1200], Temp: 0.2942, Energy: -82.909996+0.002827j
[2025-09-14 05:57:35] [Iter 1814/2250] R3[763/1200], Temp: 0.2931, Energy: -82.970031-0.002234j
[2025-09-14 05:58:21] [Iter 1815/2250] R3[764/1200], Temp: 0.2919, Energy: -82.972531-0.001590j
[2025-09-14 05:59:07] [Iter 1816/2250] R3[765/1200], Temp: 0.2907, Energy: -82.942914+0.002477j
[2025-09-14 05:59:52] [Iter 1817/2250] R3[766/1200], Temp: 0.2895, Energy: -82.974216+0.000792j
[2025-09-14 06:00:38] [Iter 1818/2250] R3[767/1200], Temp: 0.2883, Energy: -82.937727+0.004045j
[2025-09-14 06:01:24] [Iter 1819/2250] R3[768/1200], Temp: 0.2871, Energy: -82.910244+0.002599j
[2025-09-14 06:02:10] [Iter 1820/2250] R3[769/1200], Temp: 0.2859, Energy: -82.858216-0.000517j
[2025-09-14 06:02:56] [Iter 1821/2250] R3[770/1200], Temp: 0.2847, Energy: -82.922063-0.002678j
[2025-09-14 06:03:42] [Iter 1822/2250] R3[771/1200], Temp: 0.2836, Energy: -82.841291+0.004259j
[2025-09-14 06:04:28] [Iter 1823/2250] R3[772/1200], Temp: 0.2824, Energy: -82.934044+0.003131j
[2025-09-14 06:05:14] [Iter 1824/2250] R3[773/1200], Temp: 0.2812, Energy: -82.904943-0.002014j
[2025-09-14 06:06:00] [Iter 1825/2250] R3[774/1200], Temp: 0.2800, Energy: -82.943344-0.000027j
[2025-09-14 06:06:45] [Iter 1826/2250] R3[775/1200], Temp: 0.2789, Energy: -82.857086+0.004693j
[2025-09-14 06:07:31] [Iter 1827/2250] R3[776/1200], Temp: 0.2777, Energy: -82.921316+0.000070j
[2025-09-14 06:08:17] [Iter 1828/2250] R3[777/1200], Temp: 0.2765, Energy: -82.905266-0.000161j
[2025-09-14 06:09:03] [Iter 1829/2250] R3[778/1200], Temp: 0.2753, Energy: -82.949390+0.000830j
[2025-09-14 06:09:49] [Iter 1830/2250] R3[779/1200], Temp: 0.2742, Energy: -82.938635+0.000092j
[2025-09-14 06:10:35] [Iter 1831/2250] R3[780/1200], Temp: 0.2730, Energy: -82.928862+0.001979j
[2025-09-14 06:11:20] [Iter 1832/2250] R3[781/1200], Temp: 0.2718, Energy: -82.893225+0.001190j
[2025-09-14 06:12:06] [Iter 1833/2250] R3[782/1200], Temp: 0.2707, Energy: -82.935858-0.000970j
[2025-09-14 06:12:52] [Iter 1834/2250] R3[783/1200], Temp: 0.2695, Energy: -82.976453-0.002033j
[2025-09-14 06:13:38] [Iter 1835/2250] R3[784/1200], Temp: 0.2684, Energy: -83.010564-0.001681j
[2025-09-14 06:14:24] [Iter 1836/2250] R3[785/1200], Temp: 0.2672, Energy: -83.028423-0.000804j
[2025-09-14 06:15:10] [Iter 1837/2250] R3[786/1200], Temp: 0.2660, Energy: -83.002644-0.002767j
[2025-09-14 06:15:56] [Iter 1838/2250] R3[787/1200], Temp: 0.2649, Energy: -82.963564-0.001438j
[2025-09-14 06:16:41] [Iter 1839/2250] R3[788/1200], Temp: 0.2637, Energy: -82.986042-0.000927j
[2025-09-14 06:17:27] [Iter 1840/2250] R3[789/1200], Temp: 0.2626, Energy: -82.936692+0.000295j
[2025-09-14 06:18:13] [Iter 1841/2250] R3[790/1200], Temp: 0.2614, Energy: -83.003715+0.001325j
[2025-09-14 06:18:59] [Iter 1842/2250] R3[791/1200], Temp: 0.2603, Energy: -83.073593+0.002537j
[2025-09-14 06:19:45] [Iter 1843/2250] R3[792/1200], Temp: 0.2591, Energy: -83.068150+0.000630j
[2025-09-14 06:20:31] [Iter 1844/2250] R3[793/1200], Temp: 0.2580, Energy: -82.990505-0.000094j
[2025-09-14 06:21:17] [Iter 1845/2250] R3[794/1200], Temp: 0.2568, Energy: -83.013227+0.002271j
[2025-09-14 06:22:02] [Iter 1846/2250] R3[795/1200], Temp: 0.2557, Energy: -82.987631-0.001796j
[2025-09-14 06:22:48] [Iter 1847/2250] R3[796/1200], Temp: 0.2545, Energy: -82.988002-0.001431j
[2025-09-14 06:23:34] [Iter 1848/2250] R3[797/1200], Temp: 0.2534, Energy: -82.918645+0.001089j
[2025-09-14 06:24:20] [Iter 1849/2250] R3[798/1200], Temp: 0.2523, Energy: -82.937817+0.002286j
[2025-09-14 06:25:06] [Iter 1850/2250] R3[799/1200], Temp: 0.2511, Energy: -82.855925-0.002223j
[2025-09-14 06:25:52] [Iter 1851/2250] R3[800/1200], Temp: 0.2500, Energy: -82.958411+0.003029j
[2025-09-14 06:26:38] [Iter 1852/2250] R3[801/1200], Temp: 0.2489, Energy: -82.901846-0.002740j
[2025-09-14 06:27:23] [Iter 1853/2250] R3[802/1200], Temp: 0.2477, Energy: -82.990349+0.000898j
[2025-09-14 06:28:09] [Iter 1854/2250] R3[803/1200], Temp: 0.2466, Energy: -82.934223-0.002359j
[2025-09-14 06:28:55] [Iter 1855/2250] R3[804/1200], Temp: 0.2455, Energy: -82.943099-0.002668j
[2025-09-14 06:29:41] [Iter 1856/2250] R3[805/1200], Temp: 0.2444, Energy: -82.951990+0.001155j
[2025-09-14 06:30:27] [Iter 1857/2250] R3[806/1200], Temp: 0.2432, Energy: -82.973848-0.001024j
[2025-09-14 06:31:13] [Iter 1858/2250] R3[807/1200], Temp: 0.2421, Energy: -82.976477-0.000277j
[2025-09-14 06:31:59] [Iter 1859/2250] R3[808/1200], Temp: 0.2410, Energy: -82.959307+0.000702j
[2025-09-14 06:32:45] [Iter 1860/2250] R3[809/1200], Temp: 0.2399, Energy: -82.923851+0.003077j
[2025-09-14 06:33:30] [Iter 1861/2250] R3[810/1200], Temp: 0.2388, Energy: -82.878808-0.002429j
[2025-09-14 06:34:16] [Iter 1862/2250] R3[811/1200], Temp: 0.2376, Energy: -82.907092-0.000160j
[2025-09-14 06:35:02] [Iter 1863/2250] R3[812/1200], Temp: 0.2365, Energy: -82.921164-0.002329j
[2025-09-14 06:35:48] [Iter 1864/2250] R3[813/1200], Temp: 0.2354, Energy: -82.906084+0.000114j
[2025-09-14 06:36:34] [Iter 1865/2250] R3[814/1200], Temp: 0.2343, Energy: -82.949002-0.001016j
[2025-09-14 06:37:20] [Iter 1866/2250] R3[815/1200], Temp: 0.2332, Energy: -82.943855-0.001769j
[2025-09-14 06:38:06] [Iter 1867/2250] R3[816/1200], Temp: 0.2321, Energy: -82.918787+0.000973j
[2025-09-14 06:38:52] [Iter 1868/2250] R3[817/1200], Temp: 0.2310, Energy: -82.872124-0.002763j
[2025-09-14 06:39:37] [Iter 1869/2250] R3[818/1200], Temp: 0.2299, Energy: -82.876320+0.000770j
[2025-09-14 06:40:23] [Iter 1870/2250] R3[819/1200], Temp: 0.2288, Energy: -82.884192-0.000694j
[2025-09-14 06:41:09] [Iter 1871/2250] R3[820/1200], Temp: 0.2277, Energy: -82.960969+0.000433j
[2025-09-14 06:41:55] [Iter 1872/2250] R3[821/1200], Temp: 0.2266, Energy: -82.974490+0.001677j
[2025-09-14 06:42:41] [Iter 1873/2250] R3[822/1200], Temp: 0.2255, Energy: -83.036542+0.002815j
[2025-09-14 06:43:27] [Iter 1874/2250] R3[823/1200], Temp: 0.2244, Energy: -82.934606+0.000332j
[2025-09-14 06:44:13] [Iter 1875/2250] R3[824/1200], Temp: 0.2233, Energy: -83.000322+0.000088j
[2025-09-14 06:44:59] [Iter 1876/2250] R3[825/1200], Temp: 0.2222, Energy: -83.000263+0.000912j
[2025-09-14 06:45:44] [Iter 1877/2250] R3[826/1200], Temp: 0.2211, Energy: -83.007005+0.001147j
[2025-09-14 06:46:30] [Iter 1878/2250] R3[827/1200], Temp: 0.2200, Energy: -82.933920-0.001521j
[2025-09-14 06:47:16] [Iter 1879/2250] R3[828/1200], Temp: 0.2190, Energy: -82.952229-0.005621j
[2025-09-14 06:48:02] [Iter 1880/2250] R3[829/1200], Temp: 0.2179, Energy: -82.941601-0.001064j
[2025-09-14 06:48:48] [Iter 1881/2250] R3[830/1200], Temp: 0.2168, Energy: -82.953514+0.001461j
[2025-09-14 06:49:34] [Iter 1882/2250] R3[831/1200], Temp: 0.2157, Energy: -82.999957-0.002286j
[2025-09-14 06:50:20] [Iter 1883/2250] R3[832/1200], Temp: 0.2146, Energy: -82.935717-0.000784j
[2025-09-14 06:51:05] [Iter 1884/2250] R3[833/1200], Temp: 0.2136, Energy: -82.933189-0.001556j
[2025-09-14 06:51:51] [Iter 1885/2250] R3[834/1200], Temp: 0.2125, Energy: -82.981226-0.002011j
[2025-09-14 06:52:37] [Iter 1886/2250] R3[835/1200], Temp: 0.2114, Energy: -83.020731+0.001815j
[2025-09-14 06:53:23] [Iter 1887/2250] R3[836/1200], Temp: 0.2104, Energy: -82.970900-0.000754j
[2025-09-14 06:54:09] [Iter 1888/2250] R3[837/1200], Temp: 0.2093, Energy: -82.956093-0.001964j
[2025-09-14 06:54:55] [Iter 1889/2250] R3[838/1200], Temp: 0.2082, Energy: -82.868145-0.001629j
[2025-09-14 06:55:41] [Iter 1890/2250] R3[839/1200], Temp: 0.2072, Energy: -82.847098+0.001823j
[2025-09-14 06:56:26] [Iter 1891/2250] R3[840/1200], Temp: 0.2061, Energy: -82.946119+0.000081j
[2025-09-14 06:57:12] [Iter 1892/2250] R3[841/1200], Temp: 0.2050, Energy: -82.909868+0.000665j
[2025-09-14 06:57:58] [Iter 1893/2250] R3[842/1200], Temp: 0.2040, Energy: -82.943678-0.000159j
[2025-09-14 06:58:44] [Iter 1894/2250] R3[843/1200], Temp: 0.2029, Energy: -82.918105-0.001971j
[2025-09-14 06:59:30] [Iter 1895/2250] R3[844/1200], Temp: 0.2019, Energy: -82.909824-0.000644j
[2025-09-14 07:00:16] [Iter 1896/2250] R3[845/1200], Temp: 0.2008, Energy: -82.971702+0.002286j
[2025-09-14 07:01:02] [Iter 1897/2250] R3[846/1200], Temp: 0.1998, Energy: -82.968153-0.000702j
[2025-09-14 07:01:48] [Iter 1898/2250] R3[847/1200], Temp: 0.1987, Energy: -82.965100-0.002960j
[2025-09-14 07:02:33] [Iter 1899/2250] R3[848/1200], Temp: 0.1977, Energy: -82.958522-0.002150j
[2025-09-14 07:03:19] [Iter 1900/2250] R3[849/1200], Temp: 0.1967, Energy: -82.923231-0.001554j
[2025-09-14 07:04:05] [Iter 1901/2250] R3[850/1200], Temp: 0.1956, Energy: -82.963390-0.002774j
[2025-09-14 07:04:51] [Iter 1902/2250] R3[851/1200], Temp: 0.1946, Energy: -82.914942+0.002464j
[2025-09-14 07:05:37] [Iter 1903/2250] R3[852/1200], Temp: 0.1935, Energy: -82.940386+0.001884j
[2025-09-14 07:06:23] [Iter 1904/2250] R3[853/1200], Temp: 0.1925, Energy: -82.934742+0.000991j
[2025-09-14 07:07:09] [Iter 1905/2250] R3[854/1200], Temp: 0.1915, Energy: -82.935426+0.002855j
[2025-09-14 07:07:55] [Iter 1906/2250] R3[855/1200], Temp: 0.1905, Energy: -82.944416+0.003866j
[2025-09-14 07:08:41] [Iter 1907/2250] R3[856/1200], Temp: 0.1894, Energy: -82.978056+0.001060j
[2025-09-14 07:09:26] [Iter 1908/2250] R3[857/1200], Temp: 0.1884, Energy: -82.951209+0.000593j
[2025-09-14 07:10:12] [Iter 1909/2250] R3[858/1200], Temp: 0.1874, Energy: -82.948439-0.000826j
[2025-09-14 07:10:58] [Iter 1910/2250] R3[859/1200], Temp: 0.1864, Energy: -82.952053+0.001256j
[2025-09-14 07:11:44] [Iter 1911/2250] R3[860/1200], Temp: 0.1853, Energy: -83.016001-0.000346j
[2025-09-14 07:12:30] [Iter 1912/2250] R3[861/1200], Temp: 0.1843, Energy: -82.988090-0.000736j
[2025-09-14 07:13:16] [Iter 1913/2250] R3[862/1200], Temp: 0.1833, Energy: -82.995482-0.002602j
[2025-09-14 07:14:01] [Iter 1914/2250] R3[863/1200], Temp: 0.1823, Energy: -82.937020-0.000056j
[2025-09-14 07:14:47] [Iter 1915/2250] R3[864/1200], Temp: 0.1813, Energy: -83.010407-0.002959j
[2025-09-14 07:15:33] [Iter 1916/2250] R3[865/1200], Temp: 0.1803, Energy: -82.981390-0.000972j
[2025-09-14 07:16:19] [Iter 1917/2250] R3[866/1200], Temp: 0.1793, Energy: -82.971077-0.002032j
[2025-09-14 07:17:05] [Iter 1918/2250] R3[867/1200], Temp: 0.1783, Energy: -83.002200-0.002922j
[2025-09-14 07:17:51] [Iter 1919/2250] R3[868/1200], Temp: 0.1773, Energy: -83.051765+0.000583j
[2025-09-14 07:18:37] [Iter 1920/2250] R3[869/1200], Temp: 0.1763, Energy: -83.023432+0.002019j
[2025-09-14 07:19:22] [Iter 1921/2250] R3[870/1200], Temp: 0.1753, Energy: -82.956139+0.000830j
[2025-09-14 07:20:08] [Iter 1922/2250] R3[871/1200], Temp: 0.1743, Energy: -82.951453+0.001161j
[2025-09-14 07:20:54] [Iter 1923/2250] R3[872/1200], Temp: 0.1733, Energy: -82.955171-0.001204j
[2025-09-14 07:21:40] [Iter 1924/2250] R3[873/1200], Temp: 0.1723, Energy: -82.926634+0.001920j
[2025-09-14 07:22:26] [Iter 1925/2250] R3[874/1200], Temp: 0.1713, Energy: -82.970284-0.000662j
[2025-09-14 07:23:12] [Iter 1926/2250] R3[875/1200], Temp: 0.1703, Energy: -82.987501+0.000656j
[2025-09-14 07:23:58] [Iter 1927/2250] R3[876/1200], Temp: 0.1693, Energy: -83.084148+0.003229j
[2025-09-14 07:24:43] [Iter 1928/2250] R3[877/1200], Temp: 0.1684, Energy: -83.121375+0.002317j
[2025-09-14 07:25:29] [Iter 1929/2250] R3[878/1200], Temp: 0.1674, Energy: -83.110482-0.000958j
[2025-09-14 07:26:15] [Iter 1930/2250] R3[879/1200], Temp: 0.1664, Energy: -83.044057-0.002745j
[2025-09-14 07:27:01] [Iter 1931/2250] R3[880/1200], Temp: 0.1654, Energy: -83.092208-0.000981j
[2025-09-14 07:27:47] [Iter 1932/2250] R3[881/1200], Temp: 0.1645, Energy: -83.069372-0.000924j
[2025-09-14 07:28:33] [Iter 1933/2250] R3[882/1200], Temp: 0.1635, Energy: -83.084825-0.001545j
[2025-09-14 07:29:19] [Iter 1934/2250] R3[883/1200], Temp: 0.1625, Energy: -83.052280+0.000314j
[2025-09-14 07:30:04] [Iter 1935/2250] R3[884/1200], Temp: 0.1616, Energy: -82.967190+0.001256j
[2025-09-14 07:30:50] [Iter 1936/2250] R3[885/1200], Temp: 0.1606, Energy: -82.975265+0.001378j
[2025-09-14 07:31:36] [Iter 1937/2250] R3[886/1200], Temp: 0.1596, Energy: -83.008654+0.001698j
[2025-09-14 07:32:22] [Iter 1938/2250] R3[887/1200], Temp: 0.1587, Energy: -82.998061+0.002757j
[2025-09-14 07:33:08] [Iter 1939/2250] R3[888/1200], Temp: 0.1577, Energy: -83.031419-0.000534j
[2025-09-14 07:33:54] [Iter 1940/2250] R3[889/1200], Temp: 0.1568, Energy: -82.994772-0.002985j
[2025-09-14 07:34:39] [Iter 1941/2250] R3[890/1200], Temp: 0.1558, Energy: -83.098115-0.000658j
[2025-09-14 07:35:25] [Iter 1942/2250] R3[891/1200], Temp: 0.1549, Energy: -83.063333-0.005348j
[2025-09-14 07:36:11] [Iter 1943/2250] R3[892/1200], Temp: 0.1539, Energy: -83.000818+0.001440j
[2025-09-14 07:36:57] [Iter 1944/2250] R3[893/1200], Temp: 0.1530, Energy: -82.997093-0.002337j
[2025-09-14 07:37:43] [Iter 1945/2250] R3[894/1200], Temp: 0.1520, Energy: -83.035889-0.000268j
[2025-09-14 07:38:29] [Iter 1946/2250] R3[895/1200], Temp: 0.1511, Energy: -82.964089-0.003187j
[2025-09-14 07:39:15] [Iter 1947/2250] R3[896/1200], Temp: 0.1502, Energy: -82.951229-0.003024j
[2025-09-14 07:40:01] [Iter 1948/2250] R3[897/1200], Temp: 0.1492, Energy: -82.945311+0.001462j
[2025-09-14 07:40:46] [Iter 1949/2250] R3[898/1200], Temp: 0.1483, Energy: -82.948219-0.003831j
[2025-09-14 07:41:32] [Iter 1950/2250] R3[899/1200], Temp: 0.1474, Energy: -82.971731+0.000815j
[2025-09-14 07:42:18] [Iter 1951/2250] R3[900/1200], Temp: 0.1464, Energy: -82.967706-0.002060j
[2025-09-14 07:43:04] [Iter 1952/2250] R3[901/1200], Temp: 0.1455, Energy: -82.972385+0.000402j
[2025-09-14 07:43:50] [Iter 1953/2250] R3[902/1200], Temp: 0.1446, Energy: -82.952281-0.004949j
[2025-09-14 07:44:36] [Iter 1954/2250] R3[903/1200], Temp: 0.1437, Energy: -83.000765+0.005407j
[2025-09-14 07:45:22] [Iter 1955/2250] R3[904/1200], Temp: 0.1428, Energy: -82.951150-0.000283j
[2025-09-14 07:46:08] [Iter 1956/2250] R3[905/1200], Temp: 0.1418, Energy: -82.916509-0.001827j
[2025-09-14 07:46:53] [Iter 1957/2250] R3[906/1200], Temp: 0.1409, Energy: -82.889956+0.000502j
[2025-09-14 07:47:39] [Iter 1958/2250] R3[907/1200], Temp: 0.1400, Energy: -82.901739-0.000447j
[2025-09-14 07:48:25] [Iter 1959/2250] R3[908/1200], Temp: 0.1391, Energy: -82.944042-0.001437j
[2025-09-14 07:49:11] [Iter 1960/2250] R3[909/1200], Temp: 0.1382, Energy: -82.965708-0.000905j
[2025-09-14 07:49:57] [Iter 1961/2250] R3[910/1200], Temp: 0.1373, Energy: -82.925148-0.000068j
[2025-09-14 07:50:43] [Iter 1962/2250] R3[911/1200], Temp: 0.1364, Energy: -82.921241+0.002517j
[2025-09-14 07:51:29] [Iter 1963/2250] R3[912/1200], Temp: 0.1355, Energy: -82.894957-0.002645j
[2025-09-14 07:52:15] [Iter 1964/2250] R3[913/1200], Temp: 0.1346, Energy: -82.963330+0.001217j
[2025-09-14 07:53:00] [Iter 1965/2250] R3[914/1200], Temp: 0.1337, Energy: -82.886624-0.001775j
[2025-09-14 07:53:46] [Iter 1966/2250] R3[915/1200], Temp: 0.1328, Energy: -82.904980+0.000746j
[2025-09-14 07:54:32] [Iter 1967/2250] R3[916/1200], Temp: 0.1320, Energy: -82.901141+0.000270j
[2025-09-14 07:55:18] [Iter 1968/2250] R3[917/1200], Temp: 0.1311, Energy: -82.970998-0.000227j
[2025-09-14 07:56:04] [Iter 1969/2250] R3[918/1200], Temp: 0.1302, Energy: -82.961272-0.000530j
[2025-09-14 07:56:50] [Iter 1970/2250] R3[919/1200], Temp: 0.1293, Energy: -82.938536-0.000460j
[2025-09-14 07:57:35] [Iter 1971/2250] R3[920/1200], Temp: 0.1284, Energy: -83.028412-0.001749j
[2025-09-14 07:58:21] [Iter 1972/2250] R3[921/1200], Temp: 0.1276, Energy: -83.005332-0.001162j
[2025-09-14 07:59:07] [Iter 1973/2250] R3[922/1200], Temp: 0.1267, Energy: -82.912621-0.002341j
[2025-09-14 07:59:53] [Iter 1974/2250] R3[923/1200], Temp: 0.1258, Energy: -82.923489-0.000205j
[2025-09-14 08:00:39] [Iter 1975/2250] R3[924/1200], Temp: 0.1249, Energy: -82.954968+0.000179j
[2025-09-14 08:01:25] [Iter 1976/2250] R3[925/1200], Temp: 0.1241, Energy: -82.963997+0.000235j
[2025-09-14 08:02:11] [Iter 1977/2250] R3[926/1200], Temp: 0.1232, Energy: -82.994301-0.000724j
[2025-09-14 08:02:57] [Iter 1978/2250] R3[927/1200], Temp: 0.1224, Energy: -82.977216+0.000451j
[2025-09-14 08:03:43] [Iter 1979/2250] R3[928/1200], Temp: 0.1215, Energy: -82.995916-0.001665j
[2025-09-14 08:04:28] [Iter 1980/2250] R3[929/1200], Temp: 0.1206, Energy: -82.912067+0.003104j
[2025-09-14 08:05:14] [Iter 1981/2250] R3[930/1200], Temp: 0.1198, Energy: -82.856758-0.002384j
[2025-09-14 08:06:00] [Iter 1982/2250] R3[931/1200], Temp: 0.1189, Energy: -82.881381+0.000116j
[2025-09-14 08:06:46] [Iter 1983/2250] R3[932/1200], Temp: 0.1181, Energy: -82.931394+0.003169j
[2025-09-14 08:07:32] [Iter 1984/2250] R3[933/1200], Temp: 0.1173, Energy: -82.960731+0.001025j
[2025-09-14 08:08:18] [Iter 1985/2250] R3[934/1200], Temp: 0.1164, Energy: -82.964135-0.001354j
[2025-09-14 08:09:04] [Iter 1986/2250] R3[935/1200], Temp: 0.1156, Energy: -82.959100+0.001212j
[2025-09-14 08:09:50] [Iter 1987/2250] R3[936/1200], Temp: 0.1147, Energy: -82.924256-0.000090j
[2025-09-14 08:10:35] [Iter 1988/2250] R3[937/1200], Temp: 0.1139, Energy: -82.969365-0.003513j
[2025-09-14 08:11:21] [Iter 1989/2250] R3[938/1200], Temp: 0.1131, Energy: -83.010044-0.000083j
[2025-09-14 08:12:07] [Iter 1990/2250] R3[939/1200], Temp: 0.1123, Energy: -82.999661+0.002947j
[2025-09-14 08:12:53] [Iter 1991/2250] R3[940/1200], Temp: 0.1114, Energy: -83.028571-0.001111j
[2025-09-14 08:13:39] [Iter 1992/2250] R3[941/1200], Temp: 0.1106, Energy: -82.986474-0.000337j
[2025-09-14 08:14:25] [Iter 1993/2250] R3[942/1200], Temp: 0.1098, Energy: -83.047887+0.003895j
[2025-09-14 08:15:11] [Iter 1994/2250] R3[943/1200], Temp: 0.1090, Energy: -83.016893+0.000272j
[2025-09-14 08:15:57] [Iter 1995/2250] R3[944/1200], Temp: 0.1082, Energy: -82.996574+0.003353j
[2025-09-14 08:16:42] [Iter 1996/2250] R3[945/1200], Temp: 0.1073, Energy: -83.115337+0.002636j
[2025-09-14 08:17:28] [Iter 1997/2250] R3[946/1200], Temp: 0.1065, Energy: -83.048282-0.003885j
[2025-09-14 08:18:14] [Iter 1998/2250] R3[947/1200], Temp: 0.1057, Energy: -83.069143-0.002465j
[2025-09-14 08:19:00] [Iter 1999/2250] R3[948/1200], Temp: 0.1049, Energy: -83.057835-0.001333j
[2025-09-14 08:19:46] [Iter 2000/2250] R3[949/1200], Temp: 0.1041, Energy: -83.049432+0.003228j
[2025-09-14 08:19:46] ✓ Checkpoint saved: checkpoint_iter_002000.pkl
[2025-09-14 08:20:32] [Iter 2001/2250] R3[950/1200], Temp: 0.1033, Energy: -82.942877-0.001245j
[2025-09-14 08:21:18] [Iter 2002/2250] R3[951/1200], Temp: 0.1025, Energy: -82.989391-0.002615j
[2025-09-14 08:22:04] [Iter 2003/2250] R3[952/1200], Temp: 0.1017, Energy: -83.002129-0.002044j
[2025-09-14 08:22:49] [Iter 2004/2250] R3[953/1200], Temp: 0.1009, Energy: -82.950650-0.001514j
[2025-09-14 08:23:35] [Iter 2005/2250] R3[954/1200], Temp: 0.1002, Energy: -82.971590+0.004809j
[2025-09-14 08:24:21] [Iter 2006/2250] R3[955/1200], Temp: 0.0994, Energy: -83.022872-0.000514j
[2025-09-14 08:25:07] [Iter 2007/2250] R3[956/1200], Temp: 0.0986, Energy: -82.971914-0.002629j
[2025-09-14 08:25:53] [Iter 2008/2250] R3[957/1200], Temp: 0.0978, Energy: -82.958419+0.000618j
[2025-09-14 08:26:39] [Iter 2009/2250] R3[958/1200], Temp: 0.0970, Energy: -82.884855+0.000104j
[2025-09-14 08:27:25] [Iter 2010/2250] R3[959/1200], Temp: 0.0963, Energy: -82.956673+0.003850j
[2025-09-14 08:28:11] [Iter 2011/2250] R3[960/1200], Temp: 0.0955, Energy: -82.945009-0.003539j
[2025-09-14 08:28:57] [Iter 2012/2250] R3[961/1200], Temp: 0.0947, Energy: -82.936463+0.001750j
[2025-09-14 08:29:42] [Iter 2013/2250] R3[962/1200], Temp: 0.0940, Energy: -82.902145+0.001369j
[2025-09-14 08:30:28] [Iter 2014/2250] R3[963/1200], Temp: 0.0932, Energy: -83.005665+0.001643j
[2025-09-14 08:31:14] [Iter 2015/2250] R3[964/1200], Temp: 0.0924, Energy: -83.058316-0.004102j
[2025-09-14 08:32:00] [Iter 2016/2250] R3[965/1200], Temp: 0.0917, Energy: -83.019823-0.000163j
[2025-09-14 08:32:46] [Iter 2017/2250] R3[966/1200], Temp: 0.0909, Energy: -83.042413+0.000698j
[2025-09-14 08:33:32] [Iter 2018/2250] R3[967/1200], Temp: 0.0902, Energy: -82.996866-0.001662j
[2025-09-14 08:34:18] [Iter 2019/2250] R3[968/1200], Temp: 0.0894, Energy: -82.970372+0.001373j
[2025-09-14 08:35:04] [Iter 2020/2250] R3[969/1200], Temp: 0.0887, Energy: -82.966064+0.003475j
[2025-09-14 08:35:50] [Iter 2021/2250] R3[970/1200], Temp: 0.0879, Energy: -82.953774-0.003214j
[2025-09-14 08:36:35] [Iter 2022/2250] R3[971/1200], Temp: 0.0872, Energy: -82.922558+0.000847j
[2025-09-14 08:37:21] [Iter 2023/2250] R3[972/1200], Temp: 0.0865, Energy: -82.987770-0.000768j
[2025-09-14 08:38:07] [Iter 2024/2250] R3[973/1200], Temp: 0.0857, Energy: -82.964698-0.003308j
[2025-09-14 08:38:53] [Iter 2025/2250] R3[974/1200], Temp: 0.0850, Energy: -83.003874-0.000278j
[2025-09-14 08:39:39] [Iter 2026/2250] R3[975/1200], Temp: 0.0843, Energy: -83.008669+0.001459j
[2025-09-14 08:40:25] [Iter 2027/2250] R3[976/1200], Temp: 0.0835, Energy: -83.083820+0.002199j
[2025-09-14 08:41:11] [Iter 2028/2250] R3[977/1200], Temp: 0.0828, Energy: -82.966338+0.001951j
[2025-09-14 08:41:56] [Iter 2029/2250] R3[978/1200], Temp: 0.0821, Energy: -82.934656+0.002151j
[2025-09-14 08:42:42] [Iter 2030/2250] R3[979/1200], Temp: 0.0814, Energy: -82.882218-0.001869j
[2025-09-14 08:43:28] [Iter 2031/2250] R3[980/1200], Temp: 0.0807, Energy: -82.925497+0.000746j
[2025-09-14 08:44:14] [Iter 2032/2250] R3[981/1200], Temp: 0.0800, Energy: -83.007639-0.003690j
[2025-09-14 08:45:00] [Iter 2033/2250] R3[982/1200], Temp: 0.0792, Energy: -82.962595-0.002395j
[2025-09-14 08:45:46] [Iter 2034/2250] R3[983/1200], Temp: 0.0785, Energy: -82.874432-0.000795j
[2025-09-14 08:46:32] [Iter 2035/2250] R3[984/1200], Temp: 0.0778, Energy: -82.927567-0.001945j
[2025-09-14 08:47:17] [Iter 2036/2250] R3[985/1200], Temp: 0.0771, Energy: -82.927704-0.001384j
[2025-09-14 08:48:03] [Iter 2037/2250] R3[986/1200], Temp: 0.0764, Energy: -82.962511+0.001333j
[2025-09-14 08:48:49] [Iter 2038/2250] R3[987/1200], Temp: 0.0757, Energy: -82.887765+0.000264j
[2025-09-14 08:49:35] [Iter 2039/2250] R3[988/1200], Temp: 0.0751, Energy: -82.954576+0.001841j
[2025-09-14 08:50:21] [Iter 2040/2250] R3[989/1200], Temp: 0.0744, Energy: -82.962169+0.001649j
[2025-09-14 08:51:07] [Iter 2041/2250] R3[990/1200], Temp: 0.0737, Energy: -82.974029-0.000117j
[2025-09-14 08:51:53] [Iter 2042/2250] R3[991/1200], Temp: 0.0730, Energy: -82.952988-0.000825j
[2025-09-14 08:52:39] [Iter 2043/2250] R3[992/1200], Temp: 0.0723, Energy: -82.954450+0.000205j
[2025-09-14 08:53:24] [Iter 2044/2250] R3[993/1200], Temp: 0.0716, Energy: -82.942598-0.000015j
[2025-09-14 08:54:10] [Iter 2045/2250] R3[994/1200], Temp: 0.0710, Energy: -83.024179+0.000484j
[2025-09-14 08:54:56] [Iter 2046/2250] R3[995/1200], Temp: 0.0703, Energy: -82.960275+0.000803j
[2025-09-14 08:55:42] [Iter 2047/2250] R3[996/1200], Temp: 0.0696, Energy: -82.955587-0.000449j
[2025-09-14 08:56:28] [Iter 2048/2250] R3[997/1200], Temp: 0.0690, Energy: -82.982508+0.000431j
[2025-09-14 08:57:14] [Iter 2049/2250] R3[998/1200], Temp: 0.0683, Energy: -82.974066-0.001538j
[2025-09-14 08:57:59] [Iter 2050/2250] R3[999/1200], Temp: 0.0676, Energy: -82.972283-0.001792j
[2025-09-14 08:58:45] [Iter 2051/2250] R3[1000/1200], Temp: 0.0670, Energy: -82.991306+0.002897j
[2025-09-14 08:59:31] [Iter 2052/2250] R3[1001/1200], Temp: 0.0663, Energy: -82.927093-0.002034j
[2025-09-14 09:00:17] [Iter 2053/2250] R3[1002/1200], Temp: 0.0657, Energy: -82.976680+0.001588j
[2025-09-14 09:01:03] [Iter 2054/2250] R3[1003/1200], Temp: 0.0650, Energy: -82.890426+0.000394j
[2025-09-14 09:01:49] [Iter 2055/2250] R3[1004/1200], Temp: 0.0644, Energy: -82.836041+0.000689j
[2025-09-14 09:02:35] [Iter 2056/2250] R3[1005/1200], Temp: 0.0638, Energy: -82.918922+0.001701j
[2025-09-14 09:03:21] [Iter 2057/2250] R3[1006/1200], Temp: 0.0631, Energy: -82.953730+0.000196j
[2025-09-14 09:04:07] [Iter 2058/2250] R3[1007/1200], Temp: 0.0625, Energy: -82.947144-0.002040j
[2025-09-14 09:04:52] [Iter 2059/2250] R3[1008/1200], Temp: 0.0618, Energy: -82.942555-0.002859j
[2025-09-14 09:05:38] [Iter 2060/2250] R3[1009/1200], Temp: 0.0612, Energy: -82.886781+0.000935j
[2025-09-14 09:06:24] [Iter 2061/2250] R3[1010/1200], Temp: 0.0606, Energy: -82.997120-0.002343j
[2025-09-14 09:07:10] [Iter 2062/2250] R3[1011/1200], Temp: 0.0600, Energy: -83.046894-0.001694j
[2025-09-14 09:07:56] [Iter 2063/2250] R3[1012/1200], Temp: 0.0593, Energy: -83.009231-0.001148j
[2025-09-14 09:08:42] [Iter 2064/2250] R3[1013/1200], Temp: 0.0587, Energy: -82.990380-0.004172j
[2025-09-14 09:09:28] [Iter 2065/2250] R3[1014/1200], Temp: 0.0581, Energy: -83.041846+0.002331j
[2025-09-14 09:10:13] [Iter 2066/2250] R3[1015/1200], Temp: 0.0575, Energy: -82.995717-0.000500j
[2025-09-14 09:10:59] [Iter 2067/2250] R3[1016/1200], Temp: 0.0569, Energy: -82.983597-0.000986j
[2025-09-14 09:11:45] [Iter 2068/2250] R3[1017/1200], Temp: 0.0563, Energy: -83.013219-0.001436j
[2025-09-14 09:12:31] [Iter 2069/2250] R3[1018/1200], Temp: 0.0557, Energy: -82.878399-0.001910j
[2025-09-14 09:13:17] [Iter 2070/2250] R3[1019/1200], Temp: 0.0551, Energy: -82.980765-0.000651j
[2025-09-14 09:14:03] [Iter 2071/2250] R3[1020/1200], Temp: 0.0545, Energy: -83.006767-0.001644j
[2025-09-14 09:14:49] [Iter 2072/2250] R3[1021/1200], Temp: 0.0539, Energy: -82.886288+0.001657j
[2025-09-14 09:15:35] [Iter 2073/2250] R3[1022/1200], Temp: 0.0533, Energy: -82.923211-0.001308j
[2025-09-14 09:16:21] [Iter 2074/2250] R3[1023/1200], Temp: 0.0527, Energy: -82.919099-0.000944j
[2025-09-14 09:17:06] [Iter 2075/2250] R3[1024/1200], Temp: 0.0521, Energy: -82.870268+0.000425j
[2025-09-14 09:17:52] [Iter 2076/2250] R3[1025/1200], Temp: 0.0516, Energy: -82.845572+0.002197j
[2025-09-14 09:18:38] [Iter 2077/2250] R3[1026/1200], Temp: 0.0510, Energy: -82.952962-0.000416j
[2025-09-14 09:19:24] [Iter 2078/2250] R3[1027/1200], Temp: 0.0504, Energy: -82.880810-0.001004j
[2025-09-14 09:20:10] [Iter 2079/2250] R3[1028/1200], Temp: 0.0498, Energy: -82.962785-0.001535j
[2025-09-14 09:20:56] [Iter 2080/2250] R3[1029/1200], Temp: 0.0493, Energy: -82.946793-0.003328j
[2025-09-14 09:21:42] [Iter 2081/2250] R3[1030/1200], Temp: 0.0487, Energy: -82.933051-0.000700j
[2025-09-14 09:22:28] [Iter 2082/2250] R3[1031/1200], Temp: 0.0481, Energy: -82.942496-0.000447j
[2025-09-14 09:23:13] [Iter 2083/2250] R3[1032/1200], Temp: 0.0476, Energy: -82.997144+0.002091j
[2025-09-14 09:23:59] [Iter 2084/2250] R3[1033/1200], Temp: 0.0470, Energy: -82.938679-0.002444j
[2025-09-14 09:24:45] [Iter 2085/2250] R3[1034/1200], Temp: 0.0465, Energy: -82.946020+0.000828j
[2025-09-14 09:25:31] [Iter 2086/2250] R3[1035/1200], Temp: 0.0459, Energy: -83.016635+0.000920j
[2025-09-14 09:26:17] [Iter 2087/2250] R3[1036/1200], Temp: 0.0454, Energy: -83.002569+0.000536j
[2025-09-14 09:27:03] [Iter 2088/2250] R3[1037/1200], Temp: 0.0448, Energy: -82.908432+0.001076j
[2025-09-14 09:27:49] [Iter 2089/2250] R3[1038/1200], Temp: 0.0443, Energy: -82.956456+0.000810j
[2025-09-14 09:28:35] [Iter 2090/2250] R3[1039/1200], Temp: 0.0438, Energy: -82.880208+0.001206j
[2025-09-14 09:29:21] [Iter 2091/2250] R3[1040/1200], Temp: 0.0432, Energy: -82.968634-0.000814j
[2025-09-14 09:30:06] [Iter 2092/2250] R3[1041/1200], Temp: 0.0427, Energy: -82.937372-0.001381j
[2025-09-14 09:30:52] [Iter 2093/2250] R3[1042/1200], Temp: 0.0422, Energy: -82.864749+0.002518j
[2025-09-14 09:31:38] [Iter 2094/2250] R3[1043/1200], Temp: 0.0416, Energy: -82.937749+0.002530j
[2025-09-14 09:32:24] [Iter 2095/2250] R3[1044/1200], Temp: 0.0411, Energy: -83.035265-0.000639j
[2025-09-14 09:33:10] [Iter 2096/2250] R3[1045/1200], Temp: 0.0406, Energy: -83.019018+0.000996j
[2025-09-14 09:33:56] [Iter 2097/2250] R3[1046/1200], Temp: 0.0401, Energy: -83.011747-0.001871j
[2025-09-14 09:34:42] [Iter 2098/2250] R3[1047/1200], Temp: 0.0396, Energy: -83.087684-0.001588j
[2025-09-14 09:35:28] [Iter 2099/2250] R3[1048/1200], Temp: 0.0391, Energy: -83.011892-0.000596j
[2025-09-14 09:36:14] [Iter 2100/2250] R3[1049/1200], Temp: 0.0386, Energy: -83.095902+0.001173j
[2025-09-14 09:36:59] [Iter 2101/2250] R3[1050/1200], Temp: 0.0381, Energy: -83.085538-0.000855j
[2025-09-14 09:37:45] [Iter 2102/2250] R3[1051/1200], Temp: 0.0376, Energy: -83.040095-0.001711j
[2025-09-14 09:38:31] [Iter 2103/2250] R3[1052/1200], Temp: 0.0371, Energy: -83.000490+0.001775j
[2025-09-14 09:39:17] [Iter 2104/2250] R3[1053/1200], Temp: 0.0366, Energy: -83.004520-0.001623j
[2025-09-14 09:40:03] [Iter 2105/2250] R3[1054/1200], Temp: 0.0361, Energy: -82.960883+0.000538j
[2025-09-14 09:40:49] [Iter 2106/2250] R3[1055/1200], Temp: 0.0356, Energy: -83.008557-0.000727j
[2025-09-14 09:41:35] [Iter 2107/2250] R3[1056/1200], Temp: 0.0351, Energy: -83.003048-0.001457j
[2025-09-14 09:42:21] [Iter 2108/2250] R3[1057/1200], Temp: 0.0346, Energy: -82.967182-0.002699j
[2025-09-14 09:43:06] [Iter 2109/2250] R3[1058/1200], Temp: 0.0342, Energy: -82.955949+0.000394j
[2025-09-14 09:43:52] [Iter 2110/2250] R3[1059/1200], Temp: 0.0337, Energy: -82.980377-0.000379j
[2025-09-14 09:44:38] [Iter 2111/2250] R3[1060/1200], Temp: 0.0332, Energy: -83.015511-0.002741j
[2025-09-14 09:45:24] [Iter 2112/2250] R3[1061/1200], Temp: 0.0327, Energy: -83.080938-0.001855j
[2025-09-14 09:46:10] [Iter 2113/2250] R3[1062/1200], Temp: 0.0323, Energy: -83.002836+0.000816j
[2025-09-14 09:46:56] [Iter 2114/2250] R3[1063/1200], Temp: 0.0318, Energy: -82.988861+0.001968j
[2025-09-14 09:47:41] [Iter 2115/2250] R3[1064/1200], Temp: 0.0314, Energy: -83.057048-0.000904j
[2025-09-14 09:48:27] [Iter 2116/2250] R3[1065/1200], Temp: 0.0309, Energy: -83.061826-0.004829j
[2025-09-14 09:49:13] [Iter 2117/2250] R3[1066/1200], Temp: 0.0305, Energy: -83.057566+0.002855j
[2025-09-14 09:49:59] [Iter 2118/2250] R3[1067/1200], Temp: 0.0300, Energy: -83.019073-0.001484j
[2025-09-14 09:50:45] [Iter 2119/2250] R3[1068/1200], Temp: 0.0296, Energy: -83.054994+0.000859j
[2025-09-14 09:51:31] [Iter 2120/2250] R3[1069/1200], Temp: 0.0291, Energy: -82.977716-0.000876j
[2025-09-14 09:52:17] [Iter 2121/2250] R3[1070/1200], Temp: 0.0287, Energy: -82.993737+0.002381j
[2025-09-14 09:53:03] [Iter 2122/2250] R3[1071/1200], Temp: 0.0282, Energy: -83.019242-0.001344j
[2025-09-14 09:53:49] [Iter 2123/2250] R3[1072/1200], Temp: 0.0278, Energy: -83.017975+0.002351j
[2025-09-14 09:54:34] [Iter 2124/2250] R3[1073/1200], Temp: 0.0274, Energy: -82.985545+0.002222j
[2025-09-14 09:55:20] [Iter 2125/2250] R3[1074/1200], Temp: 0.0270, Energy: -83.007570+0.000216j
[2025-09-14 09:56:06] [Iter 2126/2250] R3[1075/1200], Temp: 0.0265, Energy: -82.982895+0.001087j
[2025-09-14 09:56:52] [Iter 2127/2250] R3[1076/1200], Temp: 0.0261, Energy: -83.009739-0.002208j
[2025-09-14 09:57:38] [Iter 2128/2250] R3[1077/1200], Temp: 0.0257, Energy: -83.013826-0.003503j
[2025-09-14 09:58:24] [Iter 2129/2250] R3[1078/1200], Temp: 0.0253, Energy: -83.020462-0.000626j
[2025-09-14 09:59:09] [Iter 2130/2250] R3[1079/1200], Temp: 0.0249, Energy: -83.028774+0.002606j
[2025-09-14 09:59:55] [Iter 2131/2250] R3[1080/1200], Temp: 0.0245, Energy: -82.997885+0.000639j
[2025-09-14 10:00:41] [Iter 2132/2250] R3[1081/1200], Temp: 0.0241, Energy: -82.978887-0.001127j
[2025-09-14 10:01:27] [Iter 2133/2250] R3[1082/1200], Temp: 0.0237, Energy: -82.961252-0.002495j
[2025-09-14 10:02:13] [Iter 2134/2250] R3[1083/1200], Temp: 0.0233, Energy: -82.947835-0.002212j
[2025-09-14 10:02:59] [Iter 2135/2250] R3[1084/1200], Temp: 0.0229, Energy: -83.001667+0.003723j
[2025-09-14 10:03:45] [Iter 2136/2250] R3[1085/1200], Temp: 0.0225, Energy: -82.983580-0.000519j
[2025-09-14 10:04:31] [Iter 2137/2250] R3[1086/1200], Temp: 0.0221, Energy: -83.077480-0.000835j
[2025-09-14 10:05:16] [Iter 2138/2250] R3[1087/1200], Temp: 0.0217, Energy: -83.026484+0.001231j
[2025-09-14 10:06:02] [Iter 2139/2250] R3[1088/1200], Temp: 0.0213, Energy: -82.980362-0.001791j
[2025-09-14 10:06:48] [Iter 2140/2250] R3[1089/1200], Temp: 0.0210, Energy: -82.968858+0.001412j
[2025-09-14 10:07:34] [Iter 2141/2250] R3[1090/1200], Temp: 0.0206, Energy: -83.040909-0.002474j
[2025-09-14 10:08:20] [Iter 2142/2250] R3[1091/1200], Temp: 0.0202, Energy: -82.941617-0.000400j
[2025-09-14 10:09:06] [Iter 2143/2250] R3[1092/1200], Temp: 0.0199, Energy: -82.954870+0.000657j
[2025-09-14 10:09:52] [Iter 2144/2250] R3[1093/1200], Temp: 0.0195, Energy: -82.969625-0.003322j
[2025-09-14 10:10:38] [Iter 2145/2250] R3[1094/1200], Temp: 0.0191, Energy: -83.009618-0.001405j
[2025-09-14 10:11:23] [Iter 2146/2250] R3[1095/1200], Temp: 0.0188, Energy: -83.037135+0.000118j
[2025-09-14 10:12:09] [Iter 2147/2250] R3[1096/1200], Temp: 0.0184, Energy: -82.921666-0.000758j
[2025-09-14 10:12:55] [Iter 2148/2250] R3[1097/1200], Temp: 0.0181, Energy: -83.078462+0.000188j
[2025-09-14 10:13:41] [Iter 2149/2250] R3[1098/1200], Temp: 0.0177, Energy: -82.975235+0.003056j
[2025-09-14 10:14:27] [Iter 2150/2250] R3[1099/1200], Temp: 0.0174, Energy: -83.029420-0.000675j
[2025-09-14 10:15:13] [Iter 2151/2250] R3[1100/1200], Temp: 0.0170, Energy: -82.962549+0.001700j
[2025-09-14 10:15:59] [Iter 2152/2250] R3[1101/1200], Temp: 0.0167, Energy: -83.022322-0.001011j
[2025-09-14 10:16:45] [Iter 2153/2250] R3[1102/1200], Temp: 0.0164, Energy: -82.988383-0.000205j
[2025-09-14 10:17:30] [Iter 2154/2250] R3[1103/1200], Temp: 0.0160, Energy: -83.014644-0.001871j
[2025-09-14 10:18:16] [Iter 2155/2250] R3[1104/1200], Temp: 0.0157, Energy: -83.095522+0.001881j
[2025-09-14 10:19:02] [Iter 2156/2250] R3[1105/1200], Temp: 0.0154, Energy: -83.040381+0.001678j
[2025-09-14 10:19:48] [Iter 2157/2250] R3[1106/1200], Temp: 0.0151, Energy: -83.026895+0.002555j
[2025-09-14 10:20:34] [Iter 2158/2250] R3[1107/1200], Temp: 0.0147, Energy: -83.034786-0.000119j
[2025-09-14 10:21:20] [Iter 2159/2250] R3[1108/1200], Temp: 0.0144, Energy: -82.972881+0.001804j
[2025-09-14 10:22:05] [Iter 2160/2250] R3[1109/1200], Temp: 0.0141, Energy: -83.113637+0.002559j
[2025-09-14 10:22:51] [Iter 2161/2250] R3[1110/1200], Temp: 0.0138, Energy: -83.000026-0.000287j
[2025-09-14 10:23:37] [Iter 2162/2250] R3[1111/1200], Temp: 0.0135, Energy: -82.970075+0.001583j
[2025-09-14 10:24:23] [Iter 2163/2250] R3[1112/1200], Temp: 0.0132, Energy: -83.009007-0.002405j
[2025-09-14 10:25:09] [Iter 2164/2250] R3[1113/1200], Temp: 0.0129, Energy: -83.005743+0.003175j
[2025-09-14 10:25:55] [Iter 2165/2250] R3[1114/1200], Temp: 0.0126, Energy: -83.099117+0.000409j
[2025-09-14 10:26:41] [Iter 2166/2250] R3[1115/1200], Temp: 0.0123, Energy: -83.014824+0.002477j
[2025-09-14 10:27:27] [Iter 2167/2250] R3[1116/1200], Temp: 0.0120, Energy: -83.018414+0.001684j
[2025-09-14 10:28:12] [Iter 2168/2250] R3[1117/1200], Temp: 0.0118, Energy: -82.991960+0.000912j
[2025-09-14 10:28:58] [Iter 2169/2250] R3[1118/1200], Temp: 0.0115, Energy: -82.966111-0.002398j
[2025-09-14 10:29:44] [Iter 2170/2250] R3[1119/1200], Temp: 0.0112, Energy: -83.017380-0.001474j
[2025-09-14 10:30:30] [Iter 2171/2250] R3[1120/1200], Temp: 0.0109, Energy: -82.956513-0.003021j
[2025-09-14 10:31:16] [Iter 2172/2250] R3[1121/1200], Temp: 0.0107, Energy: -82.976250+0.002027j
[2025-09-14 10:32:02] [Iter 2173/2250] R3[1122/1200], Temp: 0.0104, Energy: -83.004360+0.000416j
[2025-09-14 10:32:48] [Iter 2174/2250] R3[1123/1200], Temp: 0.0101, Energy: -83.030166-0.001436j
[2025-09-14 10:33:34] [Iter 2175/2250] R3[1124/1200], Temp: 0.0099, Energy: -82.990136-0.001471j
[2025-09-14 10:34:19] [Iter 2176/2250] R3[1125/1200], Temp: 0.0096, Energy: -83.035536-0.000363j
[2025-09-14 10:35:05] [Iter 2177/2250] R3[1126/1200], Temp: 0.0094, Energy: -82.973513+0.001141j
[2025-09-14 10:35:51] [Iter 2178/2250] R3[1127/1200], Temp: 0.0091, Energy: -83.061308-0.001252j
[2025-09-14 10:36:37] [Iter 2179/2250] R3[1128/1200], Temp: 0.0089, Energy: -83.066976+0.000671j
[2025-09-14 10:37:23] [Iter 2180/2250] R3[1129/1200], Temp: 0.0086, Energy: -82.974211-0.001986j
[2025-09-14 10:38:09] [Iter 2181/2250] R3[1130/1200], Temp: 0.0084, Energy: -82.986365+0.000234j
[2025-09-14 10:38:55] [Iter 2182/2250] R3[1131/1200], Temp: 0.0081, Energy: -82.956000+0.001372j
[2025-09-14 10:39:40] [Iter 2183/2250] R3[1132/1200], Temp: 0.0079, Energy: -83.061718-0.003691j
[2025-09-14 10:40:26] [Iter 2184/2250] R3[1133/1200], Temp: 0.0077, Energy: -83.014484+0.002354j
[2025-09-14 10:41:12] [Iter 2185/2250] R3[1134/1200], Temp: 0.0074, Energy: -83.064267+0.001914j
[2025-09-14 10:41:58] [Iter 2186/2250] R3[1135/1200], Temp: 0.0072, Energy: -83.069888+0.004279j
[2025-09-14 10:42:44] [Iter 2187/2250] R3[1136/1200], Temp: 0.0070, Energy: -82.973092+0.000492j
[2025-09-14 10:43:30] [Iter 2188/2250] R3[1137/1200], Temp: 0.0068, Energy: -83.024252+0.001670j
[2025-09-14 10:44:16] [Iter 2189/2250] R3[1138/1200], Temp: 0.0066, Energy: -83.047718+0.001490j
[2025-09-14 10:45:01] [Iter 2190/2250] R3[1139/1200], Temp: 0.0064, Energy: -83.007844+0.000926j
[2025-09-14 10:45:47] [Iter 2191/2250] R3[1140/1200], Temp: 0.0062, Energy: -83.082663-0.002214j
[2025-09-14 10:46:33] [Iter 2192/2250] R3[1141/1200], Temp: 0.0060, Energy: -83.096732-0.003058j
[2025-09-14 10:47:19] [Iter 2193/2250] R3[1142/1200], Temp: 0.0058, Energy: -83.098024-0.000141j
[2025-09-14 10:48:05] [Iter 2194/2250] R3[1143/1200], Temp: 0.0056, Energy: -83.012113-0.000397j
[2025-09-14 10:48:51] [Iter 2195/2250] R3[1144/1200], Temp: 0.0054, Energy: -83.001155+0.001409j
[2025-09-14 10:49:37] [Iter 2196/2250] R3[1145/1200], Temp: 0.0052, Energy: -82.997864+0.001842j
[2025-09-14 10:50:22] [Iter 2197/2250] R3[1146/1200], Temp: 0.0050, Energy: -83.077722+0.002394j
[2025-09-14 10:51:08] [Iter 2198/2250] R3[1147/1200], Temp: 0.0048, Energy: -83.007423-0.000975j
[2025-09-14 10:51:54] [Iter 2199/2250] R3[1148/1200], Temp: 0.0046, Energy: -82.949367-0.000495j
[2025-09-14 10:52:40] [Iter 2200/2250] R3[1149/1200], Temp: 0.0045, Energy: -83.026341+0.003379j
[2025-09-14 10:53:26] [Iter 2201/2250] R3[1150/1200], Temp: 0.0043, Energy: -83.004549-0.001808j
[2025-09-14 10:54:12] [Iter 2202/2250] R3[1151/1200], Temp: 0.0041, Energy: -83.040766-0.000918j
[2025-09-14 10:54:58] [Iter 2203/2250] R3[1152/1200], Temp: 0.0039, Energy: -83.026256-0.001066j
[2025-09-14 10:55:44] [Iter 2204/2250] R3[1153/1200], Temp: 0.0038, Energy: -82.985864+0.000504j
[2025-09-14 10:56:29] [Iter 2205/2250] R3[1154/1200], Temp: 0.0036, Energy: -82.924321-0.000763j
[2025-09-14 10:57:15] [Iter 2206/2250] R3[1155/1200], Temp: 0.0035, Energy: -83.005466-0.000669j
[2025-09-14 10:58:01] [Iter 2207/2250] R3[1156/1200], Temp: 0.0033, Energy: -83.006131-0.000397j
[2025-09-14 10:58:47] [Iter 2208/2250] R3[1157/1200], Temp: 0.0032, Energy: -82.984076-0.001112j
[2025-09-14 10:59:33] [Iter 2209/2250] R3[1158/1200], Temp: 0.0030, Energy: -82.934198+0.002930j
[2025-09-14 11:00:19] [Iter 2210/2250] R3[1159/1200], Temp: 0.0029, Energy: -82.855657+0.000070j
[2025-09-14 11:01:05] [Iter 2211/2250] R3[1160/1200], Temp: 0.0027, Energy: -82.861008+0.001046j
[2025-09-14 11:01:51] [Iter 2212/2250] R3[1161/1200], Temp: 0.0026, Energy: -82.969183-0.003894j
[2025-09-14 11:02:37] [Iter 2213/2250] R3[1162/1200], Temp: 0.0025, Energy: -82.942439+0.000719j
[2025-09-14 11:03:22] [Iter 2214/2250] R3[1163/1200], Temp: 0.0023, Energy: -82.968976+0.000547j
[2025-09-14 11:04:08] [Iter 2215/2250] R3[1164/1200], Temp: 0.0022, Energy: -82.989533+0.004432j
[2025-09-14 11:04:54] [Iter 2216/2250] R3[1165/1200], Temp: 0.0021, Energy: -82.999536-0.000401j
[2025-09-14 11:05:40] [Iter 2217/2250] R3[1166/1200], Temp: 0.0020, Energy: -82.994928+0.000201j
[2025-09-14 11:06:26] [Iter 2218/2250] R3[1167/1200], Temp: 0.0019, Energy: -82.966493-0.000938j
[2025-09-14 11:07:12] [Iter 2219/2250] R3[1168/1200], Temp: 0.0018, Energy: -83.045027-0.000180j
[2025-09-14 11:07:58] [Iter 2220/2250] R3[1169/1200], Temp: 0.0016, Energy: -82.991170-0.000236j
[2025-09-14 11:08:43] [Iter 2221/2250] R3[1170/1200], Temp: 0.0015, Energy: -82.981886-0.000387j
[2025-09-14 11:09:29] [Iter 2222/2250] R3[1171/1200], Temp: 0.0014, Energy: -82.980487-0.001300j
[2025-09-14 11:10:15] [Iter 2223/2250] R3[1172/1200], Temp: 0.0013, Energy: -82.928417-0.001300j
[2025-09-14 11:11:01] [Iter 2224/2250] R3[1173/1200], Temp: 0.0012, Energy: -82.936212-0.000758j
[2025-09-14 11:11:47] [Iter 2225/2250] R3[1174/1200], Temp: 0.0012, Energy: -82.950525+0.001571j
[2025-09-14 11:12:33] [Iter 2226/2250] R3[1175/1200], Temp: 0.0011, Energy: -83.024924+0.000940j
[2025-09-14 11:13:19] [Iter 2227/2250] R3[1176/1200], Temp: 0.0010, Energy: -83.002609-0.000005j
[2025-09-14 11:14:04] [Iter 2228/2250] R3[1177/1200], Temp: 0.0009, Energy: -82.955796-0.003086j
[2025-09-14 11:14:50] [Iter 2229/2250] R3[1178/1200], Temp: 0.0008, Energy: -82.982393-0.003129j
[2025-09-14 11:15:36] [Iter 2230/2250] R3[1179/1200], Temp: 0.0008, Energy: -82.927979-0.000446j
[2025-09-14 11:16:22] [Iter 2231/2250] R3[1180/1200], Temp: 0.0007, Energy: -82.961321-0.002544j
[2025-09-14 11:17:08] [Iter 2232/2250] R3[1181/1200], Temp: 0.0006, Energy: -82.946248+0.000149j
[2025-09-14 11:17:54] [Iter 2233/2250] R3[1182/1200], Temp: 0.0006, Energy: -82.923139-0.001912j
[2025-09-14 11:18:39] [Iter 2234/2250] R3[1183/1200], Temp: 0.0005, Energy: -82.858358+0.000571j
[2025-09-14 11:19:25] [Iter 2235/2250] R3[1184/1200], Temp: 0.0004, Energy: -82.904664-0.001580j
[2025-09-14 11:20:11] [Iter 2236/2250] R3[1185/1200], Temp: 0.0004, Energy: -82.846316-0.001639j
[2025-09-14 11:20:57] [Iter 2237/2250] R3[1186/1200], Temp: 0.0003, Energy: -82.945806-0.001990j
[2025-09-14 11:21:43] [Iter 2238/2250] R3[1187/1200], Temp: 0.0003, Energy: -82.948245-0.001600j
[2025-09-14 11:22:29] [Iter 2239/2250] R3[1188/1200], Temp: 0.0002, Energy: -83.027251+0.003200j
[2025-09-14 11:23:14] [Iter 2240/2250] R3[1189/1200], Temp: 0.0002, Energy: -82.962095+0.000358j
[2025-09-14 11:24:00] [Iter 2241/2250] R3[1190/1200], Temp: 0.0002, Energy: -82.948410-0.001652j
[2025-09-14 11:24:46] [Iter 2242/2250] R3[1191/1200], Temp: 0.0001, Energy: -82.942940+0.000078j
[2025-09-14 11:25:32] [Iter 2243/2250] R3[1192/1200], Temp: 0.0001, Energy: -82.940023+0.000538j
[2025-09-14 11:26:18] [Iter 2244/2250] R3[1193/1200], Temp: 0.0001, Energy: -82.876213-0.001722j
[2025-09-14 11:27:04] [Iter 2245/2250] R3[1194/1200], Temp: 0.0001, Energy: -82.853789-0.000320j
[2025-09-14 11:27:50] [Iter 2246/2250] R3[1195/1200], Temp: 0.0000, Energy: -82.882756-0.000365j
[2025-09-14 11:28:36] [Iter 2247/2250] R3[1196/1200], Temp: 0.0000, Energy: -82.910690-0.001927j
[2025-09-14 11:29:21] [Iter 2248/2250] R3[1197/1200], Temp: 0.0000, Energy: -82.970179-0.000174j
[2025-09-14 11:30:07] [Iter 2249/2250] R3[1198/1200], Temp: 0.0000, Energy: -82.873340-0.003748j
[2025-09-14 11:30:53] [Iter 2250/2250] R3[1199/1200], Temp: 0.0000, Energy: -82.963557-0.000945j
[2025-09-14 11:30:53] ✓ Checkpoint saved: checkpoint_iter_002250.pkl
[2025-09-14 11:30:53] ✅ Training completed | Restarts: 3
[2025-09-14 11:30:53] ============================================================
[2025-09-14 11:30:53] Training completed | Runtime: 154139.4s
[2025-09-14 11:31:11] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-14 11:31:11] ============================================================
