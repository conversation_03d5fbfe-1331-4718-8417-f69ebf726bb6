[2025-09-05 14:16:47] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.04/training/checkpoints/final_GCNN.pkl
[2025-09-05 14:16:47]   - 迭代次数: final
[2025-09-05 14:16:47]   - 能量: -85.089073+0.000976j ± 0.056522
[2025-09-05 14:16:47]   - 时间戳: 2025-09-02T21:33:23.744915+08:00
[2025-09-05 14:16:58] ✓ 变分状态参数已从checkpoint恢复
[2025-09-05 14:16:58] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-05 14:16:58] ==================================================
[2025-09-05 14:16:58] GCNN for Shastry-Sutherland Model
[2025-09-05 14:16:58] ==================================================
[2025-09-05 14:16:58] System parameters:
[2025-09-05 14:16:58]   - System size: L=5, N=100
[2025-09-05 14:16:58]   - System parameters: J1=0.05, J2=0.0, Q=1.0
[2025-09-05 14:16:58] --------------------------------------------------
[2025-09-05 14:16:58] Model parameters:
[2025-09-05 14:16:58]   - Number of layers = 4
[2025-09-05 14:16:58]   - Number of features = 4
[2025-09-05 14:16:58]   - Total parameters = 19628
[2025-09-05 14:16:58] --------------------------------------------------
[2025-09-05 14:16:58] Training parameters:
[2025-09-05 14:16:58]   - Learning rate: 0.015
[2025-09-05 14:16:58]   - Total iterations: 1050
[2025-09-05 14:16:58]   - Annealing cycles: 3
[2025-09-05 14:16:58]   - Initial period: 150
[2025-09-05 14:16:58]   - Period multiplier: 2.0
[2025-09-05 14:16:58]   - Temperature range: 0.0-1.0
[2025-09-05 14:16:58]   - Samples: 4096
[2025-09-05 14:16:58]   - Discarded samples: 0
[2025-09-05 14:16:58]   - Chunk size: 2048
[2025-09-05 14:16:58]   - Diagonal shift: 0.2
[2025-09-05 14:16:58]   - Gradient clipping: 1.0
[2025-09-05 14:16:58]   - Checkpoint enabled: interval=105
[2025-09-05 14:16:58]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.05/training/checkpoints
[2025-09-05 14:16:58] --------------------------------------------------
[2025-09-05 14:16:58] Device status:
[2025-09-05 14:16:58]   - Devices model: NVIDIA H200 NVL
[2025-09-05 14:16:58]   - Number of devices: 1
[2025-09-05 14:16:58]   - Sharding: True
[2025-09-05 14:16:58] ============================================================
[2025-09-05 14:17:58] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -85.544103+0.007669j
[2025-09-05 14:18:45] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -85.549196+0.005091j
[2025-09-05 14:19:16] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -85.650068+0.000088j
[2025-09-05 14:19:44] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -85.583128+0.001227j
[2025-09-05 14:20:13] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -85.642359-0.000346j
[2025-09-05 14:20:43] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -85.725404+0.000091j
[2025-09-05 14:21:14] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -85.635307-0.004577j
[2025-09-05 14:21:45] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -85.659779+0.003217j
[2025-09-05 14:22:15] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -85.804334-0.043501j
[2025-09-05 14:22:46] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -85.726438-0.006900j
[2025-09-05 14:23:17] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -85.784020-0.002094j
[2025-09-05 14:23:47] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -85.884078-0.003765j
[2025-09-05 14:24:18] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -85.721828-0.003040j
[2025-09-05 14:24:49] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -85.761046-0.001162j
[2025-09-05 14:25:19] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -85.718594-0.004816j
[2025-09-05 14:25:50] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -85.681471+0.000959j
[2025-09-05 14:26:21] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -85.676528+0.004343j
[2025-09-05 14:26:51] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -85.635316-0.007202j
[2025-09-05 14:27:22] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -85.658596-0.004651j
[2025-09-05 14:27:53] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -85.650893+0.002986j
[2025-09-05 14:28:23] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -85.526867-0.008899j
[2025-09-05 14:28:54] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -85.644821+0.003191j
[2025-09-05 14:29:25] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -85.520425+0.005118j
[2025-09-05 14:29:55] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -85.602062+0.003371j
[2025-09-05 14:30:26] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -85.545326+0.000375j
[2025-09-05 14:30:57] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -85.680225-0.001126j
[2025-09-05 14:31:27] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -85.607410+0.008336j
[2025-09-05 14:31:58] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -85.466482-0.001089j
[2025-09-05 14:32:29] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -85.625836-0.001076j
[2025-09-05 14:32:59] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -85.663582+0.001607j
[2025-09-05 14:33:30] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -85.717758-0.000864j
[2025-09-05 14:34:00] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -85.621984+0.002304j
[2025-09-05 14:34:31] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -85.662082-0.002561j
[2025-09-05 14:34:56] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -85.551077-0.000047j
[2025-09-05 14:35:17] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -85.680214+0.002348j
[2025-09-05 14:35:47] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -85.688062+0.007475j
[2025-09-05 14:36:17] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -85.654558-0.001635j
[2025-09-05 14:36:48] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -85.701718-0.000279j
[2025-09-05 14:37:19] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -85.622793+0.000606j
[2025-09-05 14:37:49] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -85.504825+0.002066j
[2025-09-05 14:38:20] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -85.544867+0.005009j
[2025-09-05 14:38:51] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -85.562185+0.000534j
[2025-09-05 14:39:21] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -85.487069+0.000215j
[2025-09-05 14:39:50] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -85.556017+0.000260j
[2025-09-05 14:40:18] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -85.558083+0.002730j
[2025-09-05 14:40:49] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -85.490300-0.000999j
[2025-09-05 14:41:19] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -85.438743+0.002517j
[2025-09-05 14:41:50] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -85.551061-0.000102j
[2025-09-05 14:42:21] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -85.573363+0.004203j
[2025-09-05 14:42:52] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -85.634275+0.009856j
[2025-09-05 14:43:22] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -85.549337+0.002240j
[2025-09-05 14:43:53] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -85.644657+0.002525j
[2025-09-05 14:44:24] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -85.559553+0.001739j
[2025-09-05 14:44:55] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -85.543319-0.005446j
[2025-09-05 14:45:25] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -85.485638+0.003431j
[2025-09-05 14:45:56] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -85.458454-0.002204j
[2025-09-05 14:46:27] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -85.536929-0.006725j
[2025-09-05 14:46:58] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -85.431881-0.004977j
[2025-09-05 14:47:28] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -85.481261-0.000694j
[2025-09-05 14:47:59] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -85.416851-0.001913j
[2025-09-05 14:48:30] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -85.697808-0.001988j
[2025-09-05 14:49:00] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -85.669729+0.002620j
[2025-09-05 14:49:31] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -85.646157+0.000841j
[2025-09-05 14:50:02] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -85.671661+0.003569j
[2025-09-05 14:50:32] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -85.642016+0.006428j
[2025-09-05 14:51:03] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -85.795575-0.003427j
[2025-09-05 14:51:34] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -85.727425+0.001242j
[2025-09-05 14:52:04] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -85.691768-0.004736j
[2025-09-05 14:52:35] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -85.638903-0.001379j
[2025-09-05 14:53:06] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -85.705993+0.001353j
[2025-09-05 14:53:36] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -85.745045+0.000258j
[2025-09-05 14:54:07] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -85.738139-0.004896j
[2025-09-05 14:54:38] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -85.826765+0.004764j
[2025-09-05 14:55:01] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -85.796629+0.000285j
[2025-09-05 14:55:25] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -85.744114+0.000798j
[2025-09-05 14:55:56] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -85.721535-0.000407j
[2025-09-05 14:56:26] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -85.666640-0.004092j
[2025-09-05 14:56:57] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -85.542310-0.005869j
[2025-09-05 14:57:28] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -85.492006+0.000047j
[2025-09-05 14:57:59] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -85.553404+0.005158j
[2025-09-05 14:58:30] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -85.512407-0.000126j
[2025-09-05 14:59:01] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -85.644970-0.000726j
[2025-09-05 14:59:31] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -85.715566+0.002964j
[2025-09-05 14:59:57] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -85.570968+0.002354j
[2025-09-05 15:00:28] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -85.554144+0.003030j
[2025-09-05 15:00:59] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -85.541013+0.000968j
[2025-09-05 15:01:29] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -85.592017+0.002883j
[2025-09-05 15:02:00] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -85.488843-0.002546j
[2025-09-05 15:02:31] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -85.504272-0.000073j
[2025-09-05 15:03:01] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -85.436950+0.000270j
[2025-09-05 15:03:32] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -85.429478-0.000286j
[2025-09-05 15:04:03] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -85.593346+0.007611j
[2025-09-05 15:04:33] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -85.623851+0.004327j
[2025-09-05 15:05:04] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -85.773693-0.002150j
[2025-09-05 15:05:34] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -85.777714+0.007795j
[2025-09-05 15:06:05] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -85.699276+0.005166j
[2025-09-05 15:06:36] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -85.452752-0.002370j
[2025-09-05 15:07:06] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -85.343541-0.002062j
[2025-09-05 15:07:37] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -85.396370+0.009490j
[2025-09-05 15:08:07] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -85.533176-0.002041j
[2025-09-05 15:08:38] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -85.573049+0.000955j
[2025-09-05 15:09:09] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -85.782255+0.000405j
[2025-09-05 15:09:39] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -85.582922-0.003321j
[2025-09-05 15:10:10] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -85.649503-0.002157j
[2025-09-05 15:10:41] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -85.666600-0.001510j
[2025-09-05 15:10:41] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-05 15:11:11] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -85.647148+0.001643j
[2025-09-05 15:11:42] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -85.511284+0.002259j
[2025-09-05 15:12:13] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -85.472065+0.009179j
[2025-09-05 15:12:43] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -85.631934-0.001870j
[2025-09-05 15:13:14] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -85.660125+0.001495j
[2025-09-05 15:13:44] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -85.674636+0.000202j
[2025-09-05 15:14:15] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -85.622125-0.000664j
[2025-09-05 15:14:44] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -85.683454-0.002310j
[2025-09-05 15:15:05] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -85.700177-0.004551j
[2025-09-05 15:15:31] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -85.655093+0.000435j
[2025-09-05 15:16:02] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -85.595984+0.000304j
[2025-09-05 15:16:32] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -85.641286-0.003142j
[2025-09-05 15:17:03] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -85.648916-0.006376j
[2025-09-05 15:17:34] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -85.688156-0.000137j
[2025-09-05 15:18:04] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -85.575742+0.000588j
[2025-09-05 15:18:35] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -85.616072-0.005865j
[2025-09-05 15:19:06] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -85.489616-0.000226j
[2025-09-05 15:19:36] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -85.648378-0.003497j
[2025-09-05 15:20:02] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -85.560216-0.003063j
[2025-09-05 15:20:33] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -85.480910+0.002100j
[2025-09-05 15:21:04] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -85.604630+0.002799j
[2025-09-05 15:21:34] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -85.648643-0.000866j
[2025-09-05 15:22:05] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -85.786310-0.005706j
[2025-09-05 15:22:35] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -85.812445+0.001791j
[2025-09-05 15:23:06] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -85.860815-0.001435j
[2025-09-05 15:23:37] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -85.934636+0.000141j
[2025-09-05 15:24:07] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -85.757011-0.007914j
[2025-09-05 15:24:38] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -85.807738-0.000690j
[2025-09-05 15:25:09] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -85.669010+0.004604j
[2025-09-05 15:25:39] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -85.777179+0.007416j
[2025-09-05 15:26:10] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -85.774069+0.002796j
[2025-09-05 15:26:41] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -85.884116-0.001561j
[2025-09-05 15:27:11] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -85.761670-0.007933j
[2025-09-05 15:27:42] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -85.761057-0.002147j
[2025-09-05 15:28:13] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -85.766735+0.007571j
[2025-09-05 15:28:43] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -85.775193+0.000605j
[2025-09-05 15:29:14] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -85.722818-0.001058j
[2025-09-05 15:29:44] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -85.637863+0.000084j
[2025-09-05 15:30:15] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -85.653179+0.000339j
[2025-09-05 15:30:46] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -85.735165+0.003059j
[2025-09-05 15:31:16] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -85.824845+0.000240j
[2025-09-05 15:31:47] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -85.591591-0.006093j
[2025-09-05 15:32:18] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -85.617641-0.002621j
[2025-09-05 15:32:48] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -85.691749+0.012744j
[2025-09-05 15:33:19] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -85.460787-0.006850j
[2025-09-05 15:33:19] RESTART #1 | Period: 300
[2025-09-05 15:33:49] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -85.559516+0.003457j
[2025-09-05 15:34:20] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -85.529503-0.003305j
[2025-09-05 15:34:47] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -85.582341+0.006556j
[2025-09-05 15:35:08] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -85.724912-0.000887j
[2025-09-05 15:35:37] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -85.694774-0.003852j
[2025-09-05 15:36:08] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -85.521612+0.000097j
[2025-09-05 15:36:38] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -85.584813-0.001577j
[2025-09-05 15:37:09] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -85.645015-0.003268j
[2025-09-05 15:37:40] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -85.781299+0.004235j
[2025-09-05 15:38:10] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -85.685562+0.002609j
[2025-09-05 15:38:41] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -85.614310-0.004744j
[2025-09-05 15:39:12] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -85.697775+0.003461j
[2025-09-05 15:39:40] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -85.582398+0.001954j
[2025-09-05 15:40:08] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -85.565903+0.008233j
[2025-09-05 15:40:39] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -85.549240+0.004488j
[2025-09-05 15:41:09] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -85.667139+0.006239j
[2025-09-05 15:41:40] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -85.637999+0.003721j
[2025-09-05 15:42:11] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -85.595382-0.003696j
[2025-09-05 15:42:41] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -85.620599-0.000085j
[2025-09-05 15:43:12] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -85.511433-0.001957j
[2025-09-05 15:43:43] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -85.669714+0.001255j
[2025-09-05 15:44:14] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -85.569062-0.003526j
[2025-09-05 15:44:44] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -85.662908+0.003458j
[2025-09-05 15:45:15] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -85.730783+0.001670j
[2025-09-05 15:45:45] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -85.716530-0.000360j
[2025-09-05 15:46:16] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -85.602104+0.008430j
[2025-09-05 15:46:47] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -85.640073+0.005255j
[2025-09-05 15:47:18] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -85.609817+0.002390j
[2025-09-05 15:47:48] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -85.655357+0.003080j
[2025-09-05 15:48:19] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -85.648243+0.002283j
[2025-09-05 15:48:50] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -85.775018+0.004273j
[2025-09-05 15:49:20] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -85.697043+0.007565j
[2025-09-05 15:49:51] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -85.548756+0.000585j
[2025-09-05 15:50:22] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -85.437243+0.004050j
[2025-09-05 15:50:52] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -85.486440-0.000176j
[2025-09-05 15:51:23] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -85.513127-0.003140j
[2025-09-05 15:51:54] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -85.351825+0.006763j
[2025-09-05 15:52:24] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -85.463042+0.002714j
[2025-09-05 15:52:55] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -85.387229-0.002665j
[2025-09-05 15:53:26] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -85.485214+0.005012j
[2025-09-05 15:53:56] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -85.459331+0.000823j
[2025-09-05 15:54:27] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -85.628853-0.001278j
[2025-09-05 15:54:51] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -85.519189-0.003289j
[2025-09-05 15:55:12] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -85.513264-0.001212j
[2025-09-05 15:55:43] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -85.663557+0.001170j
[2025-09-05 15:56:13] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -85.510313+0.006612j
[2025-09-05 15:56:44] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -85.512095-0.000579j
[2025-09-05 15:57:15] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -85.628491+0.001310j
[2025-09-05 15:57:45] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -85.673869-0.001585j
[2025-09-05 15:58:16] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -85.560516+0.008310j
[2025-09-05 15:58:47] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -85.536652+0.002799j
[2025-09-05 15:59:17] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -85.539693-0.004921j
[2025-09-05 15:59:45] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -85.463430-0.001226j
[2025-09-05 16:00:14] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -85.634542+0.001544j
[2025-09-05 16:00:44] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -85.672303-0.002128j
[2025-09-05 16:01:15] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -85.471254-0.000726j
[2025-09-05 16:01:45] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -85.661010-0.000495j
[2025-09-05 16:02:16] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -85.699858-0.001949j
[2025-09-05 16:02:46] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -85.700675+0.001655j
[2025-09-05 16:03:17] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -85.755848-0.003969j
[2025-09-05 16:03:17] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-05 16:03:47] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -85.715050-0.003835j
[2025-09-05 16:04:18] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -85.727632-0.005512j
[2025-09-05 16:04:48] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -85.670094-0.000349j
[2025-09-05 16:05:19] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -85.776004-0.000064j
[2025-09-05 16:05:49] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -85.652326+0.001502j
[2025-09-05 16:06:20] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -85.646860+0.005299j
[2025-09-05 16:06:50] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -85.653249-0.000175j
[2025-09-05 16:07:21] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -85.398820+0.001194j
[2025-09-05 16:07:51] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -85.499367-0.003287j
[2025-09-05 16:08:22] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -85.741047-0.003601j
[2025-09-05 16:08:52] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -85.719038-0.003670j
[2025-09-05 16:09:23] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -85.639970+0.001299j
[2025-09-05 16:09:53] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -85.605580+0.012464j
[2025-09-05 16:10:24] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -85.519793-0.000233j
[2025-09-05 16:10:54] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -85.431030+0.007297j
[2025-09-05 16:11:25] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -85.418745+0.009521j
[2025-09-05 16:11:55] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -85.465559+0.008597j
[2025-09-05 16:12:26] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -85.356094-0.002838j
[2025-09-05 16:12:56] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -85.461089-0.001148j
[2025-09-05 16:13:27] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -85.271937-0.003122j
[2025-09-05 16:13:57] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -85.350295-0.002004j
[2025-09-05 16:14:28] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -85.777004+0.002099j
[2025-09-05 16:14:51] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -85.649316+0.000278j
[2025-09-05 16:15:12] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -85.746110+0.001305j
[2025-09-05 16:15:42] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -85.557748+0.001890j
[2025-09-05 16:16:13] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -85.522093-0.003558j
[2025-09-05 16:16:43] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -85.537932+0.002767j
[2025-09-05 16:17:14] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -85.637925-0.001354j
[2025-09-05 16:17:45] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -85.667345-0.004358j
[2025-09-05 16:18:15] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -85.410331+0.001384j
[2025-09-05 16:18:46] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -85.545826-0.003340j
[2025-09-05 16:19:17] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -85.572198-0.000504j
[2025-09-05 16:19:45] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -85.494666-0.005213j
[2025-09-05 16:20:13] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -85.458474+0.004219j
[2025-09-05 16:20:43] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -85.471297+0.001699j
[2025-09-05 16:21:14] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -85.529542+0.000324j
[2025-09-05 16:21:45] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -85.548535+0.003496j
[2025-09-05 16:22:15] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -85.647090+0.003007j
[2025-09-05 16:22:46] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -85.614107+0.002735j
[2025-09-05 16:23:16] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -85.602411+0.005263j
[2025-09-05 16:23:47] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -85.607349-0.000098j
[2025-09-05 16:24:17] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -85.613998+0.004888j
[2025-09-05 16:24:48] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -85.639178+0.000216j
[2025-09-05 16:25:18] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -85.567342-0.007250j
[2025-09-05 16:25:49] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -85.710153-0.004493j
[2025-09-05 16:26:19] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -85.502831+0.001642j
[2025-09-05 16:26:50] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -85.693844+0.000863j
[2025-09-05 16:27:21] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -85.729787+0.000644j
[2025-09-05 16:27:51] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -85.719512-0.001529j
[2025-09-05 16:28:22] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -85.551271+0.005561j
[2025-09-05 16:28:52] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -85.366707+0.007426j
[2025-09-05 16:29:23] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -85.529152+0.000280j
[2025-09-05 16:29:53] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -85.507448-0.002328j
[2025-09-05 16:30:24] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -85.695885+0.001033j
[2025-09-05 16:30:54] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -85.558562+0.003300j
[2025-09-05 16:31:25] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -85.640844-0.005316j
[2025-09-05 16:31:55] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -85.626250-0.000945j
[2025-09-05 16:32:26] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -85.622066-0.002943j
[2025-09-05 16:32:57] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -85.702124+0.008543j
[2025-09-05 16:33:27] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -85.646937+0.000178j
[2025-09-05 16:33:58] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -85.528999+0.004510j
[2025-09-05 16:34:28] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -85.498096-0.000725j
[2025-09-05 16:34:51] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -85.662506-0.009444j
[2025-09-05 16:35:13] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -85.670864+0.000460j
[2025-09-05 16:35:43] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -85.627741+0.006270j
[2025-09-05 16:36:14] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -85.694635-0.003459j
[2025-09-05 16:36:45] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -85.547179+0.000933j
[2025-09-05 16:37:15] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -85.761116+0.002299j
[2025-09-05 16:37:46] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -85.749545-0.011714j
[2025-09-05 16:38:17] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -85.625234-0.005038j
[2025-09-05 16:38:47] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -85.588140-0.007411j
[2025-09-05 16:39:18] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -85.501309-0.000145j
[2025-09-05 16:39:46] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -85.622480-0.005156j
[2025-09-05 16:40:14] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -85.488079+0.002864j
[2025-09-05 16:40:45] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -85.395691-0.001019j
[2025-09-05 16:41:15] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -85.416256-0.002103j
[2025-09-05 16:41:46] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -85.598033-0.001937j
[2025-09-05 16:42:16] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -85.525084+0.002344j
[2025-09-05 16:42:47] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -85.686073-0.000316j
[2025-09-05 16:43:17] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -85.549237+0.000998j
[2025-09-05 16:43:48] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -85.583662+0.000793j
[2025-09-05 16:44:18] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -85.717236-0.003070j
[2025-09-05 16:44:49] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -85.568609+0.004654j
[2025-09-05 16:45:19] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -85.678830-0.005052j
[2025-09-05 16:45:50] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -85.646247-0.003018j
[2025-09-05 16:46:20] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -85.716554-0.000055j
[2025-09-05 16:46:51] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -85.625001-0.005064j
[2025-09-05 16:47:21] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -85.636562+0.005221j
[2025-09-05 16:47:52] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -85.636491-0.000386j
[2025-09-05 16:48:23] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -85.530721-0.001807j
[2025-09-05 16:48:53] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -85.749712-0.001591j
[2025-09-05 16:49:24] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -85.666786+0.002606j
[2025-09-05 16:49:54] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -85.579166+0.004002j
[2025-09-05 16:50:25] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -85.540938-0.007330j
[2025-09-05 16:50:55] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -85.642231-0.005092j
[2025-09-05 16:51:26] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -85.518295+0.006198j
[2025-09-05 16:51:56] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -85.513697+0.003388j
[2025-09-05 16:52:27] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -85.643308-0.000013j
[2025-09-05 16:52:57] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -85.484631+0.000619j
[2025-09-05 16:53:28] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -85.553187+0.000618j
[2025-09-05 16:53:58] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -85.726233-0.000661j
[2025-09-05 16:54:29] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -85.529511-0.003387j
[2025-09-05 16:54:51] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -85.590454+0.008269j
[2025-09-05 16:55:14] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -85.551952+0.004110j
[2025-09-05 16:55:45] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -85.482459-0.002617j
[2025-09-05 16:55:45] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-05 16:56:15] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -85.741013-0.004661j
[2025-09-05 16:56:46] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -85.804813-0.001714j
[2025-09-05 16:57:16] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -85.808336+0.000362j
[2025-09-05 16:57:47] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -85.824544-0.001291j
[2025-09-05 16:58:18] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -85.696111+0.002445j
[2025-09-05 16:58:48] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -85.685956-0.002770j
[2025-09-05 16:59:19] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -85.445663+0.004384j
[2025-09-05 16:59:46] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -85.734221+0.001198j
[2025-09-05 17:00:15] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -85.861044+0.000745j
[2025-09-05 17:00:46] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -85.667225-0.008506j
[2025-09-05 17:01:17] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -85.562835+0.005623j
[2025-09-05 17:01:47] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -85.603508-0.006962j
[2025-09-05 17:02:18] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -85.663470-0.002695j
[2025-09-05 17:02:48] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -85.779768-0.001775j
[2025-09-05 17:03:19] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -85.686770-0.004155j
[2025-09-05 17:03:50] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -85.565426-0.006342j
[2025-09-05 17:04:20] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -85.646501+0.004770j
[2025-09-05 17:04:51] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -85.651139+0.008033j
[2025-09-05 17:05:21] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -85.563846+0.004871j
[2025-09-05 17:05:52] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -85.706334-0.002149j
[2025-09-05 17:06:23] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -85.495921-0.003749j
[2025-09-05 17:06:53] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -85.837459-0.004767j
[2025-09-05 17:07:24] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -85.705193-0.000119j
[2025-09-05 17:07:54] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -85.733443-0.001480j
[2025-09-05 17:08:25] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -85.703889-0.002369j
[2025-09-05 17:08:56] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -85.823033-0.000627j
[2025-09-05 17:09:26] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -85.539884-0.005197j
[2025-09-05 17:09:57] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -85.653068-0.001505j
[2025-09-05 17:10:27] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -85.632907-0.006364j
[2025-09-05 17:10:58] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -85.742319+0.002283j
[2025-09-05 17:11:29] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -85.768621-0.010248j
[2025-09-05 17:11:59] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -85.877779-0.005324j
[2025-09-05 17:12:30] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -85.785336+0.004018j
[2025-09-05 17:13:00] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -85.789414-0.001272j
[2025-09-05 17:13:31] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -85.767182-0.005364j
[2025-09-05 17:14:02] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -85.645604+0.000333j
[2025-09-05 17:14:32] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -85.683747+0.000642j
[2025-09-05 17:14:53] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -85.678276-0.006873j
[2025-09-05 17:15:17] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -85.546892-0.003512j
[2025-09-05 17:15:48] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -85.560042-0.002291j
[2025-09-05 17:16:19] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -85.621561+0.002454j
[2025-09-05 17:16:49] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -85.611481-0.000549j
[2025-09-05 17:17:20] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -85.580070-0.001756j
[2025-09-05 17:17:51] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -85.507646-0.001823j
[2025-09-05 17:18:21] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -85.422646+0.000728j
[2025-09-05 17:18:52] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -85.493800+0.005521j
[2025-09-05 17:19:23] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -85.673052+0.003456j
[2025-09-05 17:19:49] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -85.407406+0.002984j
[2025-09-05 17:20:19] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -85.570113+0.004172j
[2025-09-05 17:20:50] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -85.648715+0.001953j
[2025-09-05 17:21:21] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -85.565833+0.001651j
[2025-09-05 17:21:51] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -85.445621-0.000106j
[2025-09-05 17:22:22] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -85.589312-0.009154j
[2025-09-05 17:22:53] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -85.544499-0.007039j
[2025-09-05 17:23:23] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -85.564385+0.002255j
[2025-09-05 17:23:54] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -85.646466-0.002828j
[2025-09-05 17:24:24] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -85.560014-0.002620j
[2025-09-05 17:24:55] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -85.553786+0.004207j
[2025-09-05 17:25:26] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -85.460992+0.002925j
[2025-09-05 17:25:56] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -85.743317+0.006172j
[2025-09-05 17:26:27] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -85.675839+0.003476j
[2025-09-05 17:26:57] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -85.759809+0.005919j
[2025-09-05 17:27:28] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -85.617958-0.002850j
[2025-09-05 17:27:58] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -85.639654+0.010310j
[2025-09-05 17:28:29] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -85.569362-0.008939j
[2025-09-05 17:29:00] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -85.579063+0.000200j
[2025-09-05 17:29:30] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -85.508066-0.003990j
[2025-09-05 17:30:01] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -85.540385+0.002340j
[2025-09-05 17:30:31] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -85.531320+0.003176j
[2025-09-05 17:31:02] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -85.589586+0.003729j
[2025-09-05 17:31:32] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -85.532363+0.007824j
[2025-09-05 17:32:03] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -85.527482+0.002023j
[2025-09-05 17:32:34] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -85.534559-0.004323j
[2025-09-05 17:33:04] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -85.615908-0.001196j
[2025-09-05 17:33:35] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -85.485666+0.007177j
[2025-09-05 17:34:05] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -85.450775-0.006996j
[2025-09-05 17:34:34] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -85.637233+0.000781j
[2025-09-05 17:34:55] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -85.644106-0.005294j
[2025-09-05 17:35:22] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -85.641413-0.003259j
[2025-09-05 17:35:52] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -85.477626+0.002621j
[2025-09-05 17:36:23] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -85.703381-0.002385j
[2025-09-05 17:36:53] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -85.713908+0.000997j
[2025-09-05 17:37:24] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -85.689358+0.001147j
[2025-09-05 17:37:55] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -85.751541-0.000004j
[2025-09-05 17:38:25] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -85.712167-0.003019j
[2025-09-05 17:38:56] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -85.737388+0.002828j
[2025-09-05 17:39:26] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -85.662472-0.007043j
[2025-09-05 17:39:53] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -85.727026-0.003921j
[2025-09-05 17:40:23] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -85.638164+0.003216j
[2025-09-05 17:40:54] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -85.467083-0.001434j
[2025-09-05 17:41:24] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -85.586503+0.002684j
[2025-09-05 17:41:55] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -85.740086+0.002420j
[2025-09-05 17:42:26] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -85.661544+0.007152j
[2025-09-05 17:42:56] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -85.672563+0.007069j
[2025-09-05 17:43:27] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -85.665820+0.000837j
[2025-09-05 17:43:58] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -85.605731+0.006519j
[2025-09-05 17:44:28] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -85.752929-0.005358j
[2025-09-05 17:44:59] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -85.604905-0.004188j
[2025-09-05 17:45:30] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -85.695394+0.004039j
[2025-09-05 17:46:00] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -85.959135-0.005424j
[2025-09-05 17:46:31] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -85.764683-0.008081j
[2025-09-05 17:47:02] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -85.729686+0.001996j
[2025-09-05 17:47:32] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -85.717466+0.004060j
[2025-09-05 17:48:03] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -85.716854-0.001697j
[2025-09-05 17:48:34] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -85.608565-0.004190j
[2025-09-05 17:48:34] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-05 17:49:04] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -85.444362+0.002850j
[2025-09-05 17:49:35] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -85.574460-0.000122j
[2025-09-05 17:50:06] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -85.712678-0.002878j
[2025-09-05 17:50:36] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -85.731793-0.005893j
[2025-09-05 17:51:07] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -85.789446+0.003439j
[2025-09-05 17:51:37] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -85.807596-0.010510j
[2025-09-05 17:52:08] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -85.670347+0.000100j
[2025-09-05 17:52:39] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -85.558013+0.001933j
[2025-09-05 17:53:09] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -85.420345-0.003578j
[2025-09-05 17:53:40] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -85.423024-0.003672j
[2025-09-05 17:54:11] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -85.681195-0.006249j
[2025-09-05 17:54:37] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -85.663986+0.005342j
[2025-09-05 17:54:58] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -85.720202-0.006292j
[2025-09-05 17:55:26] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -85.789311-0.009134j
[2025-09-05 17:55:57] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -85.821985+0.000015j
[2025-09-05 17:56:28] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -85.923513+0.001878j
[2025-09-05 17:56:58] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -85.933309-0.001592j
[2025-09-05 17:57:29] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -85.771392+0.019691j
[2025-09-05 17:57:59] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -85.525013+0.008249j
[2025-09-05 17:58:30] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -85.676215-0.001259j
[2025-09-05 17:59:01] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -85.623287-0.005058j
[2025-09-05 17:59:29] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -85.683742+0.002493j
[2025-09-05 17:59:57] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -85.636688-0.003763j
[2025-09-05 18:00:28] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -85.636972-0.004287j
[2025-09-05 18:00:58] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -85.563448-0.001133j
[2025-09-05 18:01:29] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -85.620597+0.000435j
[2025-09-05 18:02:00] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -85.683823-0.002229j
[2025-09-05 18:02:30] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -85.754712-0.001255j
[2025-09-05 18:03:01] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -85.627371+0.002030j
[2025-09-05 18:03:31] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -85.579607+0.002979j
[2025-09-05 18:03:31] RESTART #2 | Period: 600
[2025-09-05 18:04:02] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -85.403969+0.002029j
[2025-09-05 18:04:32] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -85.535754+0.000059j
[2025-09-05 18:05:03] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -85.632855+0.001696j
[2025-09-05 18:05:33] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -85.558455-0.003818j
[2025-09-05 18:06:04] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -85.589881-0.005354j
[2025-09-05 18:06:34] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -85.631850-0.006674j
[2025-09-05 18:07:05] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -85.722618+0.007479j
[2025-09-05 18:07:36] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -85.770721-0.000337j
[2025-09-05 18:08:06] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -85.741011+0.001551j
[2025-09-05 18:08:37] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -85.629568-0.005938j
[2025-09-05 18:09:07] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -85.564558+0.002004j
[2025-09-05 18:09:38] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -85.631328+0.004866j
[2025-09-05 18:10:08] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -85.672078+0.004214j
[2025-09-05 18:10:39] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -85.730645+0.002617j
[2025-09-05 18:11:09] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -85.843916-0.006546j
[2025-09-05 18:11:40] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -85.970149-0.004334j
[2025-09-05 18:12:10] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -85.718747+0.005273j
[2025-09-05 18:12:41] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -85.776872-0.004644j
[2025-09-05 18:13:11] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -85.641124+0.009579j
[2025-09-05 18:13:42] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -85.699383+0.001775j
[2025-09-05 18:14:12] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -85.668969+0.002134j
[2025-09-05 18:14:37] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -85.610743-0.000165j
[2025-09-05 18:14:59] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -85.673866+0.001969j
[2025-09-05 18:15:29] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -85.558567-0.002104j
[2025-09-05 18:16:00] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -85.469354-0.012834j
[2025-09-05 18:16:31] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -85.592679+0.005137j
[2025-09-05 18:17:01] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -85.511469+0.000114j
[2025-09-05 18:17:32] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -85.487198+0.009391j
[2025-09-05 18:18:02] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -85.511526-0.000373j
[2025-09-05 18:18:33] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -85.616272-0.001765j
[2025-09-05 18:19:04] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -85.570545+0.009873j
[2025-09-05 18:19:32] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -85.444038+0.004171j
[2025-09-05 18:20:00] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -85.476830-0.001878j
[2025-09-05 18:20:31] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -85.591603+0.000633j
[2025-09-05 18:21:01] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -85.616654+0.000684j
[2025-09-05 18:21:32] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -85.432801-0.000706j
[2025-09-05 18:22:03] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -85.602374-0.003901j
[2025-09-05 18:22:33] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -85.537407-0.003177j
[2025-09-05 18:23:04] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -85.559144-0.003987j
[2025-09-05 18:23:34] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -85.566595+0.000235j
[2025-09-05 18:24:05] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -85.669898+0.001093j
[2025-09-05 18:24:35] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -85.648252-0.007035j
[2025-09-05 18:25:06] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -85.898031+0.001326j
[2025-09-05 18:25:36] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -85.860588+0.003534j
[2025-09-05 18:26:07] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -85.746388-0.004314j
[2025-09-05 18:26:37] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -85.793833-0.000017j
[2025-09-05 18:27:08] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -85.858580-0.004548j
[2025-09-05 18:27:38] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -85.607950+0.003289j
[2025-09-05 18:28:09] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -85.551070-0.004019j
[2025-09-05 18:28:40] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -85.692884-0.009117j
[2025-09-05 18:29:10] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -85.647526+0.006538j
[2025-09-05 18:29:41] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -85.643144+0.003005j
[2025-09-05 18:30:11] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -85.603727-0.000282j
[2025-09-05 18:30:42] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -85.543159-0.000691j
[2025-09-05 18:31:12] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -85.486271-0.000529j
[2025-09-05 18:31:43] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -85.571677+0.000129j
[2025-09-05 18:32:13] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -85.569787-0.002734j
[2025-09-05 18:32:44] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -85.571510+0.002943j
[2025-09-05 18:33:14] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -85.612621+0.001297j
[2025-09-05 18:33:45] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -85.639892-0.001049j
[2025-09-05 18:34:15] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -85.802897-0.002984j
[2025-09-05 18:34:38] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -85.842149-0.001905j
[2025-09-05 18:34:58] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -85.846178+0.002062j
[2025-09-05 18:35:28] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -85.805617+0.000729j
[2025-09-05 18:35:59] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -85.776764+0.002201j
[2025-09-05 18:36:29] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -85.739704-0.000995j
[2025-09-05 18:37:00] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -85.644435+0.001115j
[2025-09-05 18:37:31] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -85.575788+0.000268j
[2025-09-05 18:38:01] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -85.576688+0.004076j
[2025-09-05 18:38:32] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -85.715139+0.001674j
[2025-09-05 18:39:02] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -85.493955+0.003152j
[2025-09-05 18:39:30] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -85.721544-0.001857j
[2025-09-05 18:39:59] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -85.621486-0.003689j
[2025-09-05 18:40:29] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -85.624106-0.001346j
[2025-09-05 18:41:00] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -85.607748-0.002356j
[2025-09-05 18:41:00] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-05 18:41:31] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -85.650256+0.001259j
[2025-09-05 18:42:01] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -85.679150-0.001769j
[2025-09-05 18:42:32] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -85.588450-0.001468j
[2025-09-05 18:43:03] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -85.545032-0.000128j
[2025-09-05 18:43:33] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -85.687264+0.000033j
[2025-09-05 18:44:04] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -85.682178-0.002102j
[2025-09-05 18:44:35] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -85.461726+0.005920j
[2025-09-05 18:45:05] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -85.466052-0.001178j
[2025-09-05 18:45:36] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -85.466038+0.002388j
[2025-09-05 18:46:07] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -85.477090-0.009838j
[2025-09-05 18:46:38] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -85.630578-0.003870j
[2025-09-05 18:47:08] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -85.662379-0.006941j
[2025-09-05 18:47:39] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -85.697040+0.006571j
[2025-09-05 18:48:10] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -85.596696-0.000392j
[2025-09-05 18:48:40] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -85.743414+0.003880j
[2025-09-05 18:49:11] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -85.687934+0.003014j
[2025-09-05 18:49:42] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -85.579750+0.004462j
[2025-09-05 18:50:12] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -85.774453-0.003630j
[2025-09-05 18:50:43] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -85.673629+0.002005j
[2025-09-05 18:51:14] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -85.670155-0.001423j
[2025-09-05 18:51:44] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -85.725619+0.001471j
[2025-09-05 18:52:15] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -85.821918+0.000293j
[2025-09-05 18:52:46] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -85.914776+0.006158j
[2025-09-05 18:53:16] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -85.868495-0.001751j
[2025-09-05 18:53:47] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -85.880086+0.005919j
[2025-09-05 18:54:18] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -85.675078-0.001717j
[2025-09-05 18:54:41] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -85.620969-0.011587j
[2025-09-05 18:55:03] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -85.594618-0.005833j
[2025-09-05 18:55:34] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -85.585916-0.000186j
[2025-09-05 18:56:04] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -85.543782+0.001497j
[2025-09-05 18:56:35] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -85.775211-0.002258j
[2025-09-05 18:57:06] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -85.697371-0.005457j
[2025-09-05 18:57:36] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -85.738954+0.002604j
[2025-09-05 18:58:07] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -85.771135-0.001682j
[2025-09-05 18:58:38] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -85.739868-0.002166j
[2025-09-05 18:59:08] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -85.578341-0.001468j
[2025-09-05 18:59:36] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -85.444226+0.000830j
[2025-09-05 19:00:05] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -85.390685+0.000830j
[2025-09-05 19:00:35] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -85.577553+0.003005j
[2025-09-05 19:01:06] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -85.418320-0.004356j
[2025-09-05 19:01:36] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -85.588844+0.003451j
[2025-09-05 19:02:07] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -85.497664+0.001053j
[2025-09-05 19:02:37] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -85.471893-0.000381j
[2025-09-05 19:03:08] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -85.482893+0.001569j
[2025-09-05 19:03:38] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -85.575585+0.003387j
[2025-09-05 19:04:09] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -85.628159-0.005666j
[2025-09-05 19:04:39] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -85.561025-0.000395j
[2025-09-05 19:05:10] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -85.605188+0.004778j
[2025-09-05 19:05:40] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -85.719742-0.005097j
[2025-09-05 19:06:11] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -85.568796+0.002514j
[2025-09-05 19:06:41] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -85.655072-0.000460j
[2025-09-05 19:07:12] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -85.795294-0.006221j
[2025-09-05 19:07:43] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -85.682030+0.000215j
[2025-09-05 19:08:13] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -85.786329+0.001163j
[2025-09-05 19:08:44] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -85.659290+0.003278j
[2025-09-05 19:09:14] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -85.619757+0.002662j
[2025-09-05 19:09:45] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -85.761564-0.003975j
[2025-09-05 19:10:15] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -85.596534-0.005481j
[2025-09-05 19:10:46] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -85.560454-0.005886j
[2025-09-05 19:11:16] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -85.621828+0.006379j
[2025-09-05 19:11:47] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -85.630839-0.001161j
[2025-09-05 19:12:17] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -85.561403+0.002727j
[2025-09-05 19:12:48] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -85.685057+0.001871j
[2025-09-05 19:13:18] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -85.605329-0.006736j
[2025-09-05 19:13:49] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -85.642080+0.003277j
[2025-09-05 19:14:19] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -85.578025+0.002320j
[2025-09-05 19:14:41] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -85.493765+0.004846j
[2025-09-05 19:15:03] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -85.487668+0.000587j
[2025-09-05 19:15:34] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -85.586119+0.002209j
[2025-09-05 19:16:04] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -85.587067+0.004249j
[2025-09-05 19:16:35] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -85.700897+0.001627j
[2025-09-05 19:17:05] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -85.604550+0.000457j
[2025-09-05 19:17:36] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -85.609538-0.004493j
[2025-09-05 19:18:07] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -85.586763+0.005623j
[2025-09-05 19:18:37] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -85.523275-0.002472j
[2025-09-05 19:19:08] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -85.595957-0.003941j
[2025-09-05 19:19:33] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -85.506166-0.001006j
[2025-09-05 19:19:58] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -85.603404+0.004270j
[2025-09-05 19:20:29] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -85.569450+0.004750j
[2025-09-05 19:20:59] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -85.468235+0.005654j
[2025-09-05 19:21:30] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -85.591391+0.001632j
[2025-09-05 19:22:00] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -85.550756-0.004298j
[2025-09-05 19:22:31] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -85.485078-0.006490j
[2025-09-05 19:23:01] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -85.501719+0.004293j
[2025-09-05 19:23:32] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -85.440366-0.001473j
[2025-09-05 19:24:03] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -85.515929-0.004050j
[2025-09-05 19:24:33] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -85.529647+0.002446j
[2025-09-05 19:25:04] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -85.602725+0.005150j
[2025-09-05 19:25:34] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -85.563923-0.007534j
[2025-09-05 19:26:05] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -85.694017-0.002011j
[2025-09-05 19:26:36] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -85.582946+0.002421j
[2025-09-05 19:27:06] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -85.639721-0.001332j
[2025-09-05 19:27:37] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -85.642618-0.000403j
[2025-09-05 19:28:07] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -85.630013+0.001760j
[2025-09-05 19:28:38] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -85.748149+0.001848j
[2025-09-05 19:29:09] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -85.666554+0.000168j
[2025-09-05 19:29:39] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -85.615347-0.000147j
[2025-09-05 19:30:10] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -85.679300-0.003978j
[2025-09-05 19:30:40] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -85.724531-0.003089j
[2025-09-05 19:31:11] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -85.797044-0.006843j
[2025-09-05 19:31:42] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -85.771259-0.006469j
[2025-09-05 19:32:12] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -85.620308+0.004873j
[2025-09-05 19:32:43] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -85.698152+0.001114j
[2025-09-05 19:33:13] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -85.593102+0.003533j
[2025-09-05 19:33:44] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -85.543646-0.004479j
[2025-09-05 19:33:44] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-05 19:34:15] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -85.510920-0.004605j
[2025-09-05 19:34:43] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -85.595075-0.001293j
[2025-09-05 19:35:03] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -85.389990-0.004153j
[2025-09-05 19:35:30] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -85.511125-0.003075j
[2025-09-05 19:36:01] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -85.579097-0.001410j
[2025-09-05 19:36:31] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -85.469541+0.006291j
[2025-09-05 19:37:02] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -85.527750+0.001783j
[2025-09-05 19:37:32] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -85.409559-0.001936j
[2025-09-05 19:38:03] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -85.575952+0.007596j
[2025-09-05 19:38:34] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -85.468511+0.000405j
[2025-09-05 19:39:04] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -85.457362+0.002129j
[2025-09-05 19:39:35] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -85.579351-0.006289j
[2025-09-05 19:40:01] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -85.575480-0.000943j
[2025-09-05 19:40:32] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -85.502984-0.003776j
[2025-09-05 19:41:03] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -85.673475+0.002907j
[2025-09-05 19:41:33] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -85.661795+0.004057j
[2025-09-05 19:42:04] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -85.674839-0.000290j
[2025-09-05 19:42:34] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -85.511374+0.001699j
[2025-09-05 19:43:05] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -85.512286-0.002507j
[2025-09-05 19:43:36] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -85.505834+0.005231j
[2025-09-05 19:44:06] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -85.568948-0.000041j
[2025-09-05 19:44:37] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -85.645179-0.000829j
[2025-09-05 19:45:07] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -85.509107+0.002226j
[2025-09-05 19:45:38] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -85.666967-0.000554j
[2025-09-05 19:46:09] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -85.678055+0.001041j
[2025-09-05 19:46:39] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -85.633436-0.000708j
[2025-09-05 19:47:10] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -85.551283+0.003944j
[2025-09-05 19:47:40] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -85.624424+0.001215j
[2025-09-05 19:48:11] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -85.699032-0.001600j
[2025-09-05 19:48:42] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -85.572022-0.001953j
[2025-09-05 19:49:12] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -85.588413+0.003448j
[2025-09-05 19:49:43] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -85.703214+0.002725j
[2025-09-05 19:50:13] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -85.572155+0.000517j
[2025-09-05 19:50:44] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -85.626870+0.005470j
[2025-09-05 19:51:15] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -85.575206-0.000022j
[2025-09-05 19:51:45] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -85.639178-0.005710j
[2025-09-05 19:52:16] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -85.566272+0.003587j
[2025-09-05 19:52:46] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -85.584693-0.004122j
[2025-09-05 19:53:17] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -85.673195+0.004695j
[2025-09-05 19:53:48] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -85.582416+0.000581j
[2025-09-05 19:54:18] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -85.435130-0.005267j
[2025-09-05 19:54:45] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -85.390961+0.005738j
[2025-09-05 19:55:05] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -85.437743+0.001663j
[2025-09-05 19:55:33] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -85.521980-0.001849j
[2025-09-05 19:56:04] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -85.465203+0.004564j
[2025-09-05 19:56:35] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -85.487776-0.000587j
[2025-09-05 19:57:05] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -85.496255+0.000434j
[2025-09-05 19:57:36] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -85.545241-0.000902j
[2025-09-05 19:58:07] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -85.535035+0.001277j
[2025-09-05 19:58:37] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -85.453685+0.000867j
[2025-09-05 19:59:08] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -85.597205+0.003737j
[2025-09-05 19:59:37] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -85.440718+0.003123j
[2025-09-05 20:00:05] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -85.466258+0.005188j
[2025-09-05 20:00:35] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -85.524229-0.002100j
[2025-09-05 20:01:06] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -85.380748+0.001259j
[2025-09-05 20:01:36] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -85.500256+0.002118j
[2025-09-05 20:02:07] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -85.515116-0.004328j
[2025-09-05 20:02:37] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -85.702728+0.005560j
[2025-09-05 20:03:08] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -85.558218+0.004338j
[2025-09-05 20:03:38] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -85.564739-0.002723j
[2025-09-05 20:04:09] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -85.487216-0.001658j
[2025-09-05 20:04:39] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -85.640898-0.000752j
[2025-09-05 20:05:10] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -85.650245-0.000706j
[2025-09-05 20:05:41] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -85.659817+0.000064j
[2025-09-05 20:06:11] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -85.651930+0.000427j
[2025-09-05 20:06:42] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -85.676034-0.000569j
[2025-09-05 20:07:12] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -85.617797-0.002048j
[2025-09-05 20:07:43] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -85.594536-0.001664j
[2025-09-05 20:08:13] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -85.630945+0.002714j
[2025-09-05 20:08:44] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -85.639161-0.004951j
[2025-09-05 20:09:14] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -85.599626-0.002430j
[2025-09-05 20:09:45] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -85.692694+0.002793j
[2025-09-05 20:10:15] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -85.679457+0.005986j
[2025-09-05 20:10:46] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -85.571328+0.001912j
[2025-09-05 20:11:16] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -85.667414-0.004945j
[2025-09-05 20:11:46] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -85.567937+0.000908j
[2025-09-05 20:12:17] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -85.568136+0.003272j
[2025-09-05 20:12:48] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -85.604550+0.005208j
[2025-09-05 20:13:18] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -85.662959-0.004937j
[2025-09-05 20:13:49] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -85.645456-0.003487j
[2025-09-05 20:14:19] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -85.525775+0.001071j
[2025-09-05 20:14:45] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -85.401995+0.002744j
[2025-09-05 20:15:05] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -85.541453+0.000006j
[2025-09-05 20:15:36] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -85.680666+0.002226j
[2025-09-05 20:16:07] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -85.587412-0.001971j
[2025-09-05 20:16:37] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -85.577393+0.001714j
[2025-09-05 20:17:08] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -85.576028-0.001189j
[2025-09-05 20:17:39] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -85.656627-0.001684j
[2025-09-05 20:18:09] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -85.722743-0.000110j
[2025-09-05 20:18:40] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -85.591735-0.000506j
[2025-09-05 20:19:11] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -85.604225-0.001616j
[2025-09-05 20:19:39] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -85.751209+0.000563j
[2025-09-05 20:20:07] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -85.754383-0.000661j
[2025-09-05 20:20:38] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -85.653781-0.005254j
[2025-09-05 20:21:09] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -85.455228-0.003110j
[2025-09-05 20:21:39] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -85.465027+0.000602j
[2025-09-05 20:22:10] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -85.530719-0.000431j
[2025-09-05 20:22:41] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -85.448468+0.001411j
[2025-09-05 20:23:11] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -85.440406-0.000881j
[2025-09-05 20:23:42] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -85.465285-0.002744j
[2025-09-05 20:24:13] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -85.473313-0.004058j
[2025-09-05 20:24:43] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -85.514582+0.000176j
[2025-09-05 20:25:14] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -85.576022-0.002283j
[2025-09-05 20:25:45] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -85.638788+0.000404j
[2025-09-05 20:26:16] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -85.461857-0.003669j
[2025-09-05 20:26:16] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-05 20:26:46] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -85.607979-0.000264j
[2025-09-05 20:27:17] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -85.634162+0.009241j
[2025-09-05 20:27:48] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -85.474505+0.000762j
[2025-09-05 20:28:18] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -85.483295-0.000092j
[2025-09-05 20:28:49] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -85.351580+0.003688j
[2025-09-05 20:29:20] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -85.455929-0.003463j
[2025-09-05 20:29:50] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -85.535002-0.000835j
[2025-09-05 20:30:21] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -85.665599+0.000284j
[2025-09-05 20:30:52] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -85.674217+0.005752j
[2025-09-05 20:31:22] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -85.672912-0.001772j
[2025-09-05 20:31:53] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -85.644390+0.000549j
[2025-09-05 20:32:24] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -85.556800+0.005538j
[2025-09-05 20:32:54] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -85.630214-0.005782j
[2025-09-05 20:33:25] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -85.528901-0.004053j
[2025-09-05 20:33:56] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -85.469190+0.000066j
[2025-09-05 20:34:26] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -85.510206+0.008588j
[2025-09-05 20:34:49] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -85.634110+0.002035j
[2025-09-05 20:35:11] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -85.602114+0.000956j
[2025-09-05 20:35:41] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -85.536024-0.003448j
[2025-09-05 20:36:12] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -85.655941-0.001968j
[2025-09-05 20:36:43] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -85.584981-0.002762j
[2025-09-05 20:37:13] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -85.659850-0.003777j
[2025-09-05 20:37:44] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -85.627267-0.006106j
[2025-09-05 20:38:14] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -85.693556-0.004705j
[2025-09-05 20:38:45] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -85.696448+0.003176j
[2025-09-05 20:39:15] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -85.687187+0.002067j
[2025-09-05 20:39:43] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -85.808070+0.001844j
[2025-09-05 20:40:12] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -85.712480+0.000561j
[2025-09-05 20:40:43] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -85.655838-0.001976j
[2025-09-05 20:41:13] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -85.426808-0.002834j
[2025-09-05 20:41:44] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -85.611536-0.002769j
[2025-09-05 20:42:15] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -85.613408-0.001728j
[2025-09-05 20:42:45] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -85.728010-0.003042j
[2025-09-05 20:43:16] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -85.825051+0.001469j
[2025-09-05 20:43:47] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -85.676559+0.001176j
[2025-09-05 20:44:17] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -85.675849-0.007063j
[2025-09-05 20:44:48] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -85.674379-0.000210j
[2025-09-05 20:45:19] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -85.775764+0.004650j
[2025-09-05 20:45:49] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -85.750405-0.000475j
[2025-09-05 20:46:20] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -85.768495+0.003079j
[2025-09-05 20:46:51] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -85.617780-0.001654j
[2025-09-05 20:47:21] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -85.608987+0.001188j
[2025-09-05 20:47:52] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -85.563356-0.000673j
[2025-09-05 20:48:23] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -85.511005+0.003403j
[2025-09-05 20:48:53] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -85.554331+0.005448j
[2025-09-05 20:49:24] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -85.550205+0.001940j
[2025-09-05 20:49:55] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -85.426932-0.006358j
[2025-09-05 20:50:25] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -85.458210+0.001196j
[2025-09-05 20:50:56] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -85.496462+0.002328j
[2025-09-05 20:51:27] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -85.530597-0.001401j
[2025-09-05 20:51:57] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -85.460125-0.005235j
[2025-09-05 20:52:28] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -85.473827+0.005484j
[2025-09-05 20:52:59] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -85.474109+0.001378j
[2025-09-05 20:53:29] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -85.387287-0.000554j
[2025-09-05 20:54:00] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -85.553387+0.001948j
[2025-09-05 20:54:31] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -85.647876-0.002933j
[2025-09-05 20:54:51] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -85.686188-0.002204j
[2025-09-05 20:55:15] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -85.547264-0.001621j
[2025-09-05 20:55:46] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -85.688278+0.006303j
[2025-09-05 20:56:17] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -85.698411+0.005363j
[2025-09-05 20:56:47] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -85.743904+0.003335j
[2025-09-05 20:57:18] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -85.631538-0.001899j
[2025-09-05 20:57:49] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -85.655099+0.004466j
[2025-09-05 20:58:19] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -85.696283+0.001214j
[2025-09-05 20:58:50] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -85.487121-0.000700j
[2025-09-05 20:59:20] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -85.590548+0.001606j
[2025-09-05 20:59:47] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -85.636549+0.001610j
[2025-09-05 21:00:17] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -85.502556-0.001118j
[2025-09-05 21:00:47] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -85.446576+0.000416j
[2025-09-05 21:01:18] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -85.593963+0.000293j
[2025-09-05 21:01:49] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -85.488558-0.002316j
[2025-09-05 21:02:19] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -85.494960+0.001683j
[2025-09-05 21:02:50] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -85.648072-0.004512j
[2025-09-05 21:03:20] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -85.511725-0.002009j
[2025-09-05 21:03:51] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -85.540821+0.000303j
[2025-09-05 21:04:21] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -85.533422-0.003496j
[2025-09-05 21:04:52] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -85.716223+0.001189j
[2025-09-05 21:05:22] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -85.603026-0.006593j
[2025-09-05 21:05:53] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -85.570425+0.004753j
[2025-09-05 21:06:23] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -85.604861+0.002517j
[2025-09-05 21:06:54] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -85.651590+0.004238j
[2025-09-05 21:07:24] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -85.813044+0.002333j
[2025-09-05 21:07:55] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -85.715577-0.000172j
[2025-09-05 21:08:25] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -85.687698+0.002480j
[2025-09-05 21:08:56] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -85.576987-0.002635j
[2025-09-05 21:09:26] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -85.503269+0.003153j
[2025-09-05 21:09:57] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -85.413378-0.004732j
[2025-09-05 21:10:28] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -85.466306-0.002299j
[2025-09-05 21:10:58] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -85.557567+0.000229j
[2025-09-05 21:11:29] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -85.446393+0.006459j
[2025-09-05 21:11:59] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -85.581239+0.000233j
[2025-09-05 21:12:30] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -85.606736+0.007484j
[2025-09-05 21:13:00] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -85.747106+0.003034j
[2025-09-05 21:13:31] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -85.694506-0.003277j
[2025-09-05 21:14:01] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -85.798081-0.006364j
[2025-09-05 21:14:31] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -85.820456+0.001689j
[2025-09-05 21:14:52] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -85.631690-0.004945j
[2025-09-05 21:15:16] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -85.656223-0.003665j
[2025-09-05 21:15:47] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -85.734257+0.000059j
[2025-09-05 21:16:17] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -85.739676-0.001185j
[2025-09-05 21:16:48] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -85.790585+0.005245j
[2025-09-05 21:17:18] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -85.734463+0.001442j
[2025-09-05 21:17:49] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -85.622336-0.007851j
[2025-09-05 21:18:20] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -85.529811-0.006925j
[2025-09-05 21:18:50] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -85.551198+0.001357j
[2025-09-05 21:18:50] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-05 21:19:21] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -85.501925-0.007101j
[2025-09-05 21:19:47] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -85.622927-0.004954j
[2025-09-05 21:20:17] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -85.786086-0.001853j
[2025-09-05 21:20:48] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -85.491994+0.001973j
[2025-09-05 21:21:19] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -85.606796-0.002769j
[2025-09-05 21:21:49] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -85.652888+0.001419j
[2025-09-05 21:22:20] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -85.568668+0.014040j
[2025-09-05 21:22:50] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -85.646970-0.005534j
[2025-09-05 21:23:21] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -85.564515+0.002673j
[2025-09-05 21:23:51] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -85.563445-0.003425j
[2025-09-05 21:24:22] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -85.637829+0.003040j
[2025-09-05 21:24:52] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -85.534715+0.012054j
[2025-09-05 21:25:23] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -85.634261+0.003316j
[2025-09-05 21:25:53] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -85.499342-0.001098j
[2025-09-05 21:26:24] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -85.503189+0.002549j
[2025-09-05 21:26:54] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -85.499379+0.005361j
[2025-09-05 21:27:25] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -85.437557-0.003607j
[2025-09-05 21:27:55] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -85.519698-0.001614j
[2025-09-05 21:28:26] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -85.647687+0.004550j
[2025-09-05 21:28:57] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -85.573422+0.002391j
[2025-09-05 21:29:27] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -85.571135+0.002217j
[2025-09-05 21:29:58] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -85.574145+0.001173j
[2025-09-05 21:30:28] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -85.550537+0.001033j
[2025-09-05 21:30:59] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -85.527240+0.001329j
[2025-09-05 21:31:29] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -85.424211+0.001884j
[2025-09-05 21:32:00] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -85.534055-0.001477j
[2025-09-05 21:32:30] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -85.525433-0.002308j
[2025-09-05 21:33:01] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -85.674531-0.000297j
[2025-09-05 21:33:32] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -85.805039+0.000542j
[2025-09-05 21:34:02] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -85.769846-0.005902j
[2025-09-05 21:34:32] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -85.671501+0.003268j
[2025-09-05 21:34:53] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -85.704398+0.001271j
[2025-09-05 21:35:18] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -85.713177+0.002195j
[2025-09-05 21:35:49] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -85.795889-0.000068j
[2025-09-05 21:36:19] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -85.681927-0.000243j
[2025-09-05 21:36:50] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -85.732643-0.003791j
[2025-09-05 21:37:20] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -85.677596-0.001921j
[2025-09-05 21:37:51] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -85.758324+0.000681j
[2025-09-05 21:38:22] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -85.694752-0.000778j
[2025-09-05 21:38:52] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -85.677818+0.003226j
[2025-09-05 21:39:23] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -85.447499+0.000329j
[2025-09-05 21:39:49] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -85.512636-0.005582j
[2025-09-05 21:40:19] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -85.537827-0.000363j
[2025-09-05 21:40:50] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -85.443202-0.001194j
[2025-09-05 21:41:20] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -85.478494-0.002181j
[2025-09-05 21:41:51] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -85.504535+0.001499j
[2025-09-05 21:42:22] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -85.720515-0.003894j
[2025-09-05 21:42:52] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -85.374291+0.001410j
[2025-09-05 21:43:23] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -85.663896-0.006106j
[2025-09-05 21:43:53] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -85.686869+0.004101j
[2025-09-05 21:44:24] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -85.506240-0.002278j
[2025-09-05 21:44:54] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -85.477914+0.001649j
[2025-09-05 21:45:25] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -85.546471-0.001600j
[2025-09-05 21:45:56] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -85.435100-0.003073j
[2025-09-05 21:46:26] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -85.564140-0.002510j
[2025-09-05 21:46:57] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -85.706572+0.000860j
[2025-09-05 21:47:27] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -85.582403-0.000311j
[2025-09-05 21:47:58] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -85.618578-0.001136j
[2025-09-05 21:48:28] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -85.649368-0.007871j
[2025-09-05 21:48:59] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -85.469629-0.002978j
[2025-09-05 21:49:29] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -85.638569+0.001499j
[2025-09-05 21:50:00] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -85.488658-0.005971j
[2025-09-05 21:50:30] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -85.569969+0.004809j
[2025-09-05 21:51:01] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -85.684192+0.001023j
[2025-09-05 21:51:31] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -85.606733-0.004476j
[2025-09-05 21:52:02] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -85.668806+0.003884j
[2025-09-05 21:52:32] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -85.845774-0.002446j
[2025-09-05 21:53:03] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -85.638063+0.003238j
[2025-09-05 21:53:34] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -85.534642+0.000793j
[2025-09-05 21:54:04] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -85.502845-0.009785j
[2025-09-05 21:54:32] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -85.464305-0.002800j
[2025-09-05 21:54:53] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -85.543886+0.006562j
[2025-09-05 21:55:21] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -85.486418+0.001195j
[2025-09-05 21:55:51] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -85.545237-0.003144j
[2025-09-05 21:56:22] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -85.608450-0.001715j
[2025-09-05 21:56:53] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -85.481981+0.005099j
[2025-09-05 21:57:24] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -85.605079-0.002869j
[2025-09-05 21:57:54] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -85.569960+0.002002j
[2025-09-05 21:58:25] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -85.537897+0.002690j
[2025-09-05 21:58:56] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -85.593336-0.003980j
[2025-09-05 21:59:25] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -85.683831+0.000111j
[2025-09-05 21:59:52] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -85.690468-0.009033j
[2025-09-05 22:00:23] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -85.655299+0.008187j
[2025-09-05 22:00:53] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -85.702951+0.000361j
[2025-09-05 22:01:24] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -85.714690+0.001463j
[2025-09-05 22:01:54] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -85.784179-0.004076j
[2025-09-05 22:02:25] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -85.573604-0.002244j
[2025-09-05 22:02:55] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -85.769656+0.000203j
[2025-09-05 22:03:26] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -85.962383-0.007122j
[2025-09-05 22:03:56] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -85.845501+0.009828j
[2025-09-05 22:04:27] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -85.776043-0.000878j
[2025-09-05 22:04:57] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -85.662333-0.006343j
[2025-09-05 22:05:28] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -85.619234+0.004896j
[2025-09-05 22:05:58] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -85.756201-0.007583j
[2025-09-05 22:06:29] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -85.884694+0.007809j
[2025-09-05 22:06:59] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -85.896350+0.002921j
[2025-09-05 22:07:29] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -85.829008+0.000630j
[2025-09-05 22:08:00] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -85.589251+0.002492j
[2025-09-05 22:08:30] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -85.695379-0.004280j
[2025-09-05 22:09:01] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -85.705050-0.006321j
[2025-09-05 22:09:31] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -85.706881+0.000034j
[2025-09-05 22:10:02] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -85.574754-0.005572j
[2025-09-05 22:10:32] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -85.684089+0.005179j
[2025-09-05 22:11:03] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -85.555834+0.000080j
[2025-09-05 22:11:33] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -85.657149+0.000446j
[2025-09-05 22:11:33] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-05 22:12:04] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -85.741732+0.000144j
[2025-09-05 22:12:34] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -85.559058-0.009174j
[2025-09-05 22:13:05] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -85.626219-0.000113j
[2025-09-05 22:13:35] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -85.531163+0.005264j
[2025-09-05 22:14:06] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -85.549035+0.002929j
[2025-09-05 22:14:32] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -85.492074+0.003475j
[2025-09-05 22:14:53] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -85.673606+0.000767j
[2025-09-05 22:15:21] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -85.678087+0.006997j
[2025-09-05 22:15:52] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -85.709100+0.002018j
[2025-09-05 22:16:23] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -85.724880-0.004000j
[2025-09-05 22:16:53] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -85.805404-0.000775j
[2025-09-05 22:17:24] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -85.641151+0.006599j
[2025-09-05 22:17:55] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -85.564149-0.003988j
[2025-09-05 22:18:25] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -85.605440-0.004125j
[2025-09-05 22:18:56] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -85.793998-0.005776j
[2025-09-05 22:19:24] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -85.572858-0.004243j
[2025-09-05 22:19:53] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -85.503700-0.003937j
[2025-09-05 22:20:23] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -85.779529+0.001781j
[2025-09-05 22:20:54] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -85.587233-0.001104j
[2025-09-05 22:21:24] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -85.697435-0.000389j
[2025-09-05 22:21:55] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -85.722684+0.003037j
[2025-09-05 22:22:25] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -85.651658+0.004637j
[2025-09-05 22:22:56] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -85.590162+0.002992j
[2025-09-05 22:23:27] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -85.550302-0.003742j
[2025-09-05 22:23:57] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -85.415923-0.000117j
[2025-09-05 22:24:28] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -85.457475-0.000776j
[2025-09-05 22:24:58] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -85.467467+0.000988j
[2025-09-05 22:25:29] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -85.562515-0.004902j
[2025-09-05 22:25:59] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -85.523096-0.001344j
[2025-09-05 22:26:30] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -85.569322-0.000830j
[2025-09-05 22:27:00] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -85.520404-0.000038j
[2025-09-05 22:27:31] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -85.355522+0.006233j
[2025-09-05 22:28:02] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -85.346632+0.001829j
[2025-09-05 22:28:32] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -85.550622+0.000217j
[2025-09-05 22:29:03] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -85.609402-0.001151j
[2025-09-05 22:29:33] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -85.646901+0.004475j
[2025-09-05 22:30:04] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -85.607505+0.003123j
[2025-09-05 22:30:34] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -85.531620-0.003506j
[2025-09-05 22:31:05] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -85.412866-0.000745j
[2025-09-05 22:31:35] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -85.389326+0.001065j
[2025-09-05 22:32:06] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -85.518573+0.006524j
[2025-09-05 22:32:36] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -85.427834-0.005228j
[2025-09-05 22:33:07] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -85.536260-0.002262j
[2025-09-05 22:33:37] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -85.473383+0.003373j
[2025-09-05 22:34:08] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -85.678228+0.003377j
[2025-09-05 22:34:33] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -85.441897-0.003407j
[2025-09-05 22:34:53] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -85.679073+0.002363j
[2025-09-05 22:35:23] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -85.622510-0.006732j
[2025-09-05 22:35:54] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -85.602380-0.001808j
[2025-09-05 22:36:24] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -85.619919+0.003565j
[2025-09-05 22:36:55] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -85.601956+0.001348j
[2025-09-05 22:37:26] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -85.668202-0.003405j
[2025-09-05 22:37:56] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -85.588269-0.006694j
[2025-09-05 22:38:27] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -85.790671-0.002079j
[2025-09-05 22:38:57] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -85.598197+0.002849j
[2025-09-05 22:39:26] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -85.647059+0.006022j
[2025-09-05 22:39:54] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -85.765034-0.002069j
[2025-09-05 22:40:24] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -85.522463-0.000055j
[2025-09-05 22:40:55] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -85.643851-0.004053j
[2025-09-05 22:41:26] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -85.495118-0.000933j
[2025-09-05 22:41:56] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -85.708894-0.004406j
[2025-09-05 22:42:27] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -85.639793-0.001569j
[2025-09-05 22:42:57] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -85.571159+0.003425j
[2025-09-05 22:43:28] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -85.533638+0.000098j
[2025-09-05 22:43:59] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -85.502223+0.000799j
[2025-09-05 22:44:29] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -85.613488-0.000800j
[2025-09-05 22:45:00] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -85.595051-0.000826j
[2025-09-05 22:45:31] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -85.573538-0.001441j
[2025-09-05 22:46:01] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -85.694532-0.001193j
[2025-09-05 22:46:32] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -85.617452+0.003032j
[2025-09-05 22:47:02] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -85.663864-0.003571j
[2025-09-05 22:47:33] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -85.673868+0.000528j
[2025-09-05 22:48:04] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -85.631755+0.003028j
[2025-09-05 22:48:34] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -85.522396-0.002509j
[2025-09-05 22:49:05] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -85.397625-0.003007j
[2025-09-05 22:49:35] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -85.580533-0.001234j
[2025-09-05 22:50:06] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -85.675748+0.001384j
[2025-09-05 22:50:37] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -85.634299+0.004263j
[2025-09-05 22:51:07] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -85.547736-0.001847j
[2025-09-05 22:51:38] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -85.475492+0.001936j
[2025-09-05 22:52:09] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -85.490419-0.000409j
[2025-09-05 22:52:39] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -85.605792-0.001249j
[2025-09-05 22:53:10] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -85.640857+0.001707j
[2025-09-05 22:53:40] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -85.607730-0.002575j
[2025-09-05 22:54:11] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -85.617095-0.002122j
[2025-09-05 22:54:34] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -85.533613+0.001201j
[2025-09-05 22:54:54] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -85.750101-0.002452j
[2025-09-05 22:55:25] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -85.935797-0.003737j
[2025-09-05 22:55:56] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -85.737519+0.001529j
[2025-09-05 22:56:26] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -85.719297-0.001200j
[2025-09-05 22:56:57] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -85.809926-0.000258j
[2025-09-05 22:57:27] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -85.822602-0.003942j
[2025-09-05 22:57:58] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -85.836981-0.006567j
[2025-09-05 22:58:29] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -85.826125-0.001155j
[2025-09-05 22:58:59] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -85.898491+0.000420j
[2025-09-05 22:59:28] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -85.844413-0.001018j
[2025-09-05 22:59:56] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -85.695607-0.001950j
[2025-09-05 23:00:27] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -85.730036+0.001546j
[2025-09-05 23:00:57] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -85.546585-0.000890j
[2025-09-05 23:01:28] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -85.840769+0.001958j
[2025-09-05 23:01:58] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -85.700490+0.006751j
[2025-09-05 23:02:29] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -85.516650+0.006415j
[2025-09-05 23:02:59] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -85.545021+0.000857j
[2025-09-05 23:03:30] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -85.450426-0.000837j
[2025-09-05 23:04:00] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -85.575781+0.000313j
[2025-09-05 23:04:00] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-05 23:04:00] ✅ Training completed | Restarts: 2
[2025-09-05 23:04:00] ============================================================
[2025-09-05 23:04:00] Training completed | Runtime: 31622.0s
[2025-09-05 23:04:13] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-05 23:04:13] ============================================================
