[2025-09-05 14:16:47] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.04/training/checkpoints/final_GCNN.pkl
[2025-09-05 14:16:47]   - 迭代次数: final
[2025-09-05 14:16:47]   - 能量: -85.089073+0.000976j ± 0.056522
[2025-09-05 14:16:47]   - 时间戳: 2025-09-02T21:33:23.744915+08:00
[2025-09-05 14:16:58] ✓ 变分状态参数已从checkpoint恢复
[2025-09-05 14:16:58] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-05 14:16:58] ==================================================
[2025-09-05 14:16:58] GCNN for Shastry-Sutherland Model
[2025-09-05 14:16:58] ==================================================
[2025-09-05 14:16:58] System parameters:
[2025-09-05 14:16:58]   - System size: L=5, N=100
[2025-09-05 14:16:58]   - System parameters: J1=0.03, J2=0.0, Q=1.0
[2025-09-05 14:16:58] --------------------------------------------------
[2025-09-05 14:16:58] Model parameters:
[2025-09-05 14:16:58]   - Number of layers = 4
[2025-09-05 14:16:58]   - Number of features = 4
[2025-09-05 14:16:58]   - Total parameters = 19628
[2025-09-05 14:16:58] --------------------------------------------------
[2025-09-05 14:16:58] Training parameters:
[2025-09-05 14:16:58]   - Learning rate: 0.015
[2025-09-05 14:16:58]   - Total iterations: 1050
[2025-09-05 14:16:58]   - Annealing cycles: 3
[2025-09-05 14:16:58]   - Initial period: 150
[2025-09-05 14:16:58]   - Period multiplier: 2.0
[2025-09-05 14:16:58]   - Temperature range: 0.0-1.0
[2025-09-05 14:16:58]   - Samples: 4096
[2025-09-05 14:16:58]   - Discarded samples: 0
[2025-09-05 14:16:58]   - Chunk size: 2048
[2025-09-05 14:16:58]   - Diagonal shift: 0.2
[2025-09-05 14:16:58]   - Gradient clipping: 1.0
[2025-09-05 14:16:58]   - Checkpoint enabled: interval=105
[2025-09-05 14:16:58]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.03/training/checkpoints
[2025-09-05 14:16:58] --------------------------------------------------
[2025-09-05 14:16:58] Device status:
[2025-09-05 14:16:58]   - Devices model: NVIDIA H200 NVL
[2025-09-05 14:16:58]   - Number of devices: 1
[2025-09-05 14:16:58]   - Sharding: True
[2025-09-05 14:16:58] ============================================================
[2025-09-05 14:17:58] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -84.313632+0.015755j
[2025-09-05 14:18:46] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -84.392119+0.007936j
[2025-09-05 14:19:17] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -84.431252+0.003511j
[2025-09-05 14:19:45] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -84.468056-0.002012j
[2025-09-05 14:20:14] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -84.487684+0.003518j
[2025-09-05 14:20:45] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -84.377229+0.000461j
[2025-09-05 14:21:16] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -84.460179+0.008416j
[2025-09-05 14:21:47] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -84.379596-0.004203j
[2025-09-05 14:22:18] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -84.349908+0.007160j
[2025-09-05 14:22:49] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -84.235094-0.005573j
[2025-09-05 14:23:20] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -84.277339+0.005637j
[2025-09-05 14:23:51] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -84.472344-0.004075j
[2025-09-05 14:24:22] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -84.480675+0.001114j
[2025-09-05 14:24:53] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -84.398250+0.000108j
[2025-09-05 14:25:24] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -84.417512+0.004097j
[2025-09-05 14:25:55] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -84.352029+0.004666j
[2025-09-05 14:26:26] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -84.472454-0.000079j
[2025-09-05 14:26:57] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -84.294348-0.002044j
[2025-09-05 14:27:28] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -84.235715-0.005131j
[2025-09-05 14:27:59] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -84.284820-0.002043j
[2025-09-05 14:28:30] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -84.263798+0.002121j
[2025-09-05 14:29:01] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -84.559433+0.004717j
[2025-09-05 14:29:32] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -84.566464+0.006205j
[2025-09-05 14:30:03] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -84.509605-0.005978j
[2025-09-05 14:30:33] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -84.524407-0.001480j
[2025-09-05 14:31:04] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -84.483228-0.003012j
[2025-09-05 14:31:35] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -84.359237-0.006244j
[2025-09-05 14:32:06] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -84.437327-0.004094j
[2025-09-05 14:32:37] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -84.356135-0.004818j
[2025-09-05 14:33:08] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -84.474546-0.002411j
[2025-09-05 14:33:39] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -84.450778-0.005541j
[2025-09-05 14:34:10] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -84.367849+0.001956j
[2025-09-05 14:34:41] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -84.355651+0.003250j
[2025-09-05 14:35:03] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -84.344555+0.004168j
[2025-09-05 14:35:27] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -84.361449+0.000574j
[2025-09-05 14:35:58] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -84.479974+0.006871j
[2025-09-05 14:36:28] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -84.604336-0.001785j
[2025-09-05 14:36:59] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -84.401562-0.003459j
[2025-09-05 14:37:30] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -84.409602-0.001969j
[2025-09-05 14:38:02] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -84.461205+0.003037j
[2025-09-05 14:38:33] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -84.365801-0.004860j
[2025-09-05 14:39:04] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -84.378660+0.000605j
[2025-09-05 14:39:35] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -84.394401+0.002433j
[2025-09-05 14:40:01] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -84.315147-0.006835j
[2025-09-05 14:40:32] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -84.187715+0.005632j
[2025-09-05 14:41:04] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -84.259503+0.000049j
[2025-09-05 14:41:35] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -84.254143+0.000296j
[2025-09-05 14:42:06] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -84.189639-0.006602j
[2025-09-05 14:42:38] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -84.392396+0.001410j
[2025-09-05 14:43:09] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -84.338348-0.001597j
[2025-09-05 14:43:40] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -84.301335+0.000855j
[2025-09-05 14:44:11] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -84.248966+0.001423j
[2025-09-05 14:44:42] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -84.202100-0.004682j
[2025-09-05 14:45:14] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -84.261708+0.000074j
[2025-09-05 14:45:45] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -84.385217+0.004427j
[2025-09-05 14:46:16] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -84.365456+0.001114j
[2025-09-05 14:46:48] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -84.327187-0.001185j
[2025-09-05 14:47:19] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -84.216568-0.005346j
[2025-09-05 14:47:50] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -84.338962+0.002480j
[2025-09-05 14:48:21] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -84.353095+0.009376j
[2025-09-05 14:48:53] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -84.255295-0.002326j
[2025-09-05 14:49:24] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -84.370238+0.004267j
[2025-09-05 14:49:55] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -84.296557-0.005259j
[2025-09-05 14:50:26] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -84.262675+0.001237j
[2025-09-05 14:50:57] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -84.380434-0.006012j
[2025-09-05 14:51:27] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -84.302467-0.002344j
[2025-09-05 14:51:58] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -84.254838+0.000989j
[2025-09-05 14:52:29] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -84.341101+0.003988j
[2025-09-05 14:53:00] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -84.171465-0.000575j
[2025-09-05 14:53:31] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -84.240214-0.002849j
[2025-09-05 14:54:02] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -84.252498+0.001942j
[2025-09-05 14:54:33] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -84.307164+0.000796j
[2025-09-05 14:54:58] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -84.270000-0.003974j
[2025-09-05 14:55:20] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -84.424220-0.001116j
[2025-09-05 14:55:51] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -84.425606-0.010220j
[2025-09-05 14:56:22] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -84.438440-0.000318j
[2025-09-05 14:56:53] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -84.474884+0.003207j
[2025-09-05 14:57:25] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -84.473822+0.002882j
[2025-09-05 14:57:56] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -84.429676-0.002724j
[2025-09-05 14:58:27] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -84.456544-0.001226j
[2025-09-05 14:58:58] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -84.465276+0.001858j
[2025-09-05 14:59:29] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -84.457907+0.002838j
[2025-09-05 14:59:56] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -84.458765+0.005641j
[2025-09-05 15:00:26] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -84.384071-0.003268j
[2025-09-05 15:00:57] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -84.562395-0.002763j
[2025-09-05 15:01:28] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -84.475352+0.000526j
[2025-09-05 15:01:59] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -84.467547+0.005003j
[2025-09-05 15:02:30] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -84.491480-0.006032j
[2025-09-05 15:03:01] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -84.495018-0.003306j
[2025-09-05 15:03:32] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -84.491836-0.001478j
[2025-09-05 15:04:03] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -84.341190-0.002732j
[2025-09-05 15:04:34] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -84.426935-0.002615j
[2025-09-05 15:05:05] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -84.319422-0.003403j
[2025-09-05 15:05:36] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -84.320936+0.004485j
[2025-09-05 15:06:07] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -84.327662+0.004186j
[2025-09-05 15:06:38] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -84.375416-0.000311j
[2025-09-05 15:07:09] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -84.414545+0.001633j
[2025-09-05 15:07:40] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -84.241758+0.000765j
[2025-09-05 15:08:10] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -84.419101+0.001683j
[2025-09-05 15:08:41] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -84.401523-0.003851j
[2025-09-05 15:09:12] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -84.504724+0.005478j
[2025-09-05 15:09:43] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -84.315411-0.003226j
[2025-09-05 15:10:14] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -84.404543+0.005279j
[2025-09-05 15:10:45] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -84.420668-0.002629j
[2025-09-05 15:11:16] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -84.359430+0.012633j
[2025-09-05 15:11:16] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-05 15:11:47] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -84.440048-0.003760j
[2025-09-05 15:12:18] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -84.421867+0.001744j
[2025-09-05 15:12:49] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -84.538395-0.005042j
[2025-09-05 15:13:20] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -84.629956+0.000707j
[2025-09-05 15:13:51] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -84.425797-0.000889j
[2025-09-05 15:14:22] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -84.588658-0.003933j
[2025-09-05 15:14:49] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -84.413254-0.007507j
[2025-09-05 15:15:10] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -84.508414-0.003455j
[2025-09-05 15:15:39] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -84.581166+0.008359j
[2025-09-05 15:16:10] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -84.543253-0.004601j
[2025-09-05 15:16:41] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -84.250294-0.005688j
[2025-09-05 15:17:12] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -84.306080-0.001091j
[2025-09-05 15:17:43] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -84.365173-0.003944j
[2025-09-05 15:18:14] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -84.443723+0.000054j
[2025-09-05 15:18:45] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -84.317668-0.004121j
[2025-09-05 15:19:16] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -84.296851+0.002404j
[2025-09-05 15:19:44] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -84.253225-0.007713j
[2025-09-05 15:20:13] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -84.059578+0.002927j
[2025-09-05 15:20:44] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -84.190890+0.000862j
[2025-09-05 15:21:15] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -84.402916-0.001343j
[2025-09-05 15:21:46] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -84.249299+0.005430j
[2025-09-05 15:22:17] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -84.384887+0.000989j
[2025-09-05 15:22:48] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -84.180790-0.002398j
[2025-09-05 15:23:18] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -84.267985-0.000778j
[2025-09-05 15:23:49] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -84.308918+0.003003j
[2025-09-05 15:24:20] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -84.327500-0.002686j
[2025-09-05 15:24:51] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -84.324484-0.002581j
[2025-09-05 15:25:22] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -84.358696+0.003203j
[2025-09-05 15:25:53] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -84.438864-0.020684j
[2025-09-05 15:26:24] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -84.334937+0.002542j
[2025-09-05 15:26:55] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -84.228186+0.003673j
[2025-09-05 15:27:26] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -84.383186+0.004739j
[2025-09-05 15:27:57] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -84.393482+0.013420j
[2025-09-05 15:28:28] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -84.462211-0.003883j
[2025-09-05 15:28:59] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -84.521151+0.000806j
[2025-09-05 15:29:30] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -84.420084-0.002494j
[2025-09-05 15:30:01] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -84.369248-0.001570j
[2025-09-05 15:30:32] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -84.348104+0.008190j
[2025-09-05 15:31:03] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -84.216190-0.002228j
[2025-09-05 15:31:34] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -84.156287-0.004896j
[2025-09-05 15:32:05] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -84.285589-0.006228j
[2025-09-05 15:32:36] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -84.272191+0.006901j
[2025-09-05 15:33:06] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -84.403785-0.006035j
[2025-09-05 15:33:37] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -84.435572+0.000807j
[2025-09-05 15:34:08] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -84.299069+0.000542j
[2025-09-05 15:34:08] RESTART #1 | Period: 300
[2025-09-05 15:34:39] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -84.341422-0.003008j
[2025-09-05 15:35:00] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -84.292703-0.000222j
[2025-09-05 15:35:26] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -84.287878+0.007564j
[2025-09-05 15:35:57] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -84.386983-0.000078j
[2025-09-05 15:36:28] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -84.353912+0.000657j
[2025-09-05 15:36:59] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -84.491935-0.005562j
[2025-09-05 15:37:30] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -84.400525-0.000191j
[2025-09-05 15:38:01] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -84.558620+0.001181j
[2025-09-05 15:38:32] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -84.371055-0.004480j
[2025-09-05 15:39:03] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -84.325167+0.001815j
[2025-09-05 15:39:34] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -84.324032+0.003624j
[2025-09-05 15:40:01] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -84.402132+0.007303j
[2025-09-05 15:40:32] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -84.222703-0.001111j
[2025-09-05 15:41:03] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -84.384534+0.000620j
[2025-09-05 15:41:34] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -84.322758-0.000697j
[2025-09-05 15:42:05] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -84.306427+0.002238j
[2025-09-05 15:42:36] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -84.329199-0.003479j
[2025-09-05 15:43:07] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -84.322713+0.001637j
[2025-09-05 15:43:37] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -84.240196-0.012111j
[2025-09-05 15:44:08] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -84.239085+0.005307j
[2025-09-05 15:44:39] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -84.373136-0.000265j
[2025-09-05 15:45:10] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -84.252760+0.000038j
[2025-09-05 15:45:41] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -84.373038-0.001808j
[2025-09-05 15:46:12] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -84.324412-0.000722j
[2025-09-05 15:46:43] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -84.344138+0.007881j
[2025-09-05 15:47:14] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -84.476194-0.001289j
[2025-09-05 15:47:45] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -84.319908-0.000757j
[2025-09-05 15:48:16] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -84.274454+0.001421j
[2025-09-05 15:48:47] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -84.291595-0.004583j
[2025-09-05 15:49:18] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -84.318121+0.002753j
[2025-09-05 15:49:49] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -84.316335-0.001170j
[2025-09-05 15:50:20] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -84.453775+0.003978j
[2025-09-05 15:50:51] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -84.369281+0.000139j
[2025-09-05 15:51:22] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -84.500526-0.005057j
[2025-09-05 15:51:53] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -84.595041-0.002168j
[2025-09-05 15:52:24] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -84.478457-0.001752j
[2025-09-05 15:52:55] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -84.635008-0.006983j
[2025-09-05 15:53:26] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -84.541585+0.001810j
[2025-09-05 15:53:57] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -84.622329+0.000430j
[2025-09-05 15:54:28] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -84.494004+0.001554j
[2025-09-05 15:54:52] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -84.660532-0.000082j
[2025-09-05 15:55:14] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -84.553011-0.001400j
[2025-09-05 15:55:45] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -84.524438+0.002480j
[2025-09-05 15:56:16] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -84.468645-0.002737j
[2025-09-05 15:56:47] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -84.470996+0.005994j
[2025-09-05 15:57:18] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -84.196818+0.003149j
[2025-09-05 15:57:49] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -84.205498-0.000514j
[2025-09-05 15:58:20] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -84.279628+0.002087j
[2025-09-05 15:58:51] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -84.228703-0.008066j
[2025-09-05 15:59:22] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -84.245206-0.004693j
[2025-09-05 15:59:50] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -84.166291-0.000086j
[2025-09-05 16:00:19] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -84.172511-0.005686j
[2025-09-05 16:00:50] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -84.265053-0.002700j
[2025-09-05 16:01:20] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -84.438669-0.003872j
[2025-09-05 16:01:51] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -84.377099-0.004075j
[2025-09-05 16:02:22] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -84.193848-0.002734j
[2025-09-05 16:02:53] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -84.395124-0.003374j
[2025-09-05 16:03:24] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -84.221183-0.003540j
[2025-09-05 16:03:55] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -84.229037-0.001077j
[2025-09-05 16:04:25] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -84.113195-0.001876j
[2025-09-05 16:04:25] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-05 16:04:56] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -84.093777-0.010249j
[2025-09-05 16:05:27] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -84.297155-0.000219j
[2025-09-05 16:05:58] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -84.354447+0.000697j
[2025-09-05 16:06:29] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -84.384392+0.003647j
[2025-09-05 16:07:00] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -84.388453-0.002366j
[2025-09-05 16:07:31] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -84.095707-0.001128j
[2025-09-05 16:08:01] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -84.155989-0.002815j
[2025-09-05 16:08:32] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -84.285795-0.002195j
[2025-09-05 16:09:03] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -84.294300-0.006077j
[2025-09-05 16:09:34] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -84.218524+0.004375j
[2025-09-05 16:10:05] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -84.138638+0.005001j
[2025-09-05 16:10:35] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -84.333287-0.004153j
[2025-09-05 16:11:06] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -84.331854-0.000286j
[2025-09-05 16:11:37] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -84.264565+0.001263j
[2025-09-05 16:12:08] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -84.179835+0.000601j
[2025-09-05 16:12:39] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -84.503077+0.002580j
[2025-09-05 16:13:10] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -84.467409+0.003249j
[2025-09-05 16:13:40] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -84.451470+0.006260j
[2025-09-05 16:14:11] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -84.612474+0.003509j
[2025-09-05 16:14:40] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -84.398142-0.004672j
[2025-09-05 16:15:00] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -84.353374-0.004535j
[2025-09-05 16:15:26] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -84.369346+0.005023j
[2025-09-05 16:15:57] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -84.610702+0.000593j
[2025-09-05 16:16:28] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -84.410345-0.006679j
[2025-09-05 16:16:59] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -84.503189-0.005200j
[2025-09-05 16:17:30] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -84.372450-0.003424j
[2025-09-05 16:18:01] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -84.304815-0.000611j
[2025-09-05 16:18:32] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -84.488503+0.002424j
[2025-09-05 16:19:03] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -84.476590-0.006772j
[2025-09-05 16:19:34] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -84.458589+0.004142j
[2025-09-05 16:20:00] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -84.540593-0.001989j
[2025-09-05 16:20:31] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -84.498788+0.003784j
[2025-09-05 16:21:01] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -84.437563+0.000717j
[2025-09-05 16:21:32] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -84.320583-0.007964j
[2025-09-05 16:22:03] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -84.465486+0.002524j
[2025-09-05 16:22:34] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -84.302689-0.002220j
[2025-09-05 16:23:05] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -84.301611+0.001185j
[2025-09-05 16:23:36] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -84.342125-0.001454j
[2025-09-05 16:24:07] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -84.316363-0.003958j
[2025-09-05 16:24:38] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -84.395560+0.000287j
[2025-09-05 16:25:09] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -84.270194-0.003870j
[2025-09-05 16:25:40] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -84.345239-0.004858j
[2025-09-05 16:26:11] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -84.412918-0.001240j
[2025-09-05 16:26:42] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -84.447228+0.003698j
[2025-09-05 16:27:12] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -84.451595+0.000942j
[2025-09-05 16:27:43] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -84.466235+0.003690j
[2025-09-05 16:28:14] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -84.349429-0.011973j
[2025-09-05 16:28:45] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -84.361060-0.000091j
[2025-09-05 16:29:16] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -84.417939+0.001343j
[2025-09-05 16:29:47] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -84.542666+0.001193j
[2025-09-05 16:30:18] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -84.499019-0.007230j
[2025-09-05 16:30:49] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -84.544069-0.003897j
[2025-09-05 16:31:20] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -84.350167-0.005263j
[2025-09-05 16:31:50] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -84.395711-0.004196j
[2025-09-05 16:32:21] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -84.284670+0.002216j
[2025-09-05 16:32:52] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -84.501371-0.006867j
[2025-09-05 16:33:23] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -84.637764-0.001808j
[2025-09-05 16:33:54] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -84.662864+0.003351j
[2025-09-05 16:34:25] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -84.497866+0.000324j
[2025-09-05 16:34:49] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -84.671378+0.000943j
[2025-09-05 16:35:10] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -84.684431+0.001195j
[2025-09-05 16:35:41] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -84.637135-0.002424j
[2025-09-05 16:36:12] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -84.531425-0.000475j
[2025-09-05 16:36:43] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -84.653580-0.003857j
[2025-09-05 16:37:14] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -84.602679+0.006028j
[2025-09-05 16:37:45] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -84.612096+0.002009j
[2025-09-05 16:38:16] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -84.573962-0.002290j
[2025-09-05 16:38:47] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -84.646892-0.000481j
[2025-09-05 16:39:18] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -84.430402+0.000870j
[2025-09-05 16:39:46] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -84.512411+0.003835j
[2025-09-05 16:40:15] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -84.511989-0.004811j
[2025-09-05 16:40:46] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -84.422166-0.001999j
[2025-09-05 16:41:17] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -84.428244+0.000923j
[2025-09-05 16:41:48] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -84.517012-0.000916j
[2025-09-05 16:42:18] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -84.519035-0.002154j
[2025-09-05 16:42:49] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -84.521165+0.002303j
[2025-09-05 16:43:20] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -84.371998+0.003310j
[2025-09-05 16:43:51] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -84.411050-0.005241j
[2025-09-05 16:44:22] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -84.286073-0.003387j
[2025-09-05 16:44:53] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -84.258576+0.002518j
[2025-09-05 16:45:23] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -84.444426+0.000940j
[2025-09-05 16:45:54] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -84.459771-0.002923j
[2025-09-05 16:46:25] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -84.468537-0.001196j
[2025-09-05 16:46:56] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -84.584434+0.001207j
[2025-09-05 16:47:27] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -84.475892+0.001656j
[2025-09-05 16:47:58] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -84.536844+0.000072j
[2025-09-05 16:48:29] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -84.525405-0.002340j
[2025-09-05 16:48:59] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -84.376854+0.000043j
[2025-09-05 16:49:30] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -84.363108-0.006362j
[2025-09-05 16:50:01] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -84.426016+0.004313j
[2025-09-05 16:50:32] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -84.280881-0.003449j
[2025-09-05 16:51:03] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -84.303762+0.006156j
[2025-09-05 16:51:34] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -84.375428-0.003204j
[2025-09-05 16:52:05] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -84.436677+0.002283j
[2025-09-05 16:52:35] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -84.383188-0.001116j
[2025-09-05 16:53:06] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -84.267046+0.002736j
[2025-09-05 16:53:37] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -84.335057+0.005037j
[2025-09-05 16:54:08] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -84.400658+0.001512j
[2025-09-05 16:54:37] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -84.359621-0.002538j
[2025-09-05 16:54:58] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -84.331151+0.000629j
[2025-09-05 16:55:25] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -84.488210+0.003381j
[2025-09-05 16:55:55] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -84.478125-0.003609j
[2025-09-05 16:56:26] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -84.430735+0.005977j
[2025-09-05 16:56:57] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -84.542982-0.003020j
[2025-09-05 16:57:28] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -84.430324-0.001106j
[2025-09-05 16:57:29] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-05 16:58:00] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -84.425400-0.005735j
[2025-09-05 16:58:30] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -84.411924+0.003861j
[2025-09-05 16:59:01] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -84.439132+0.006008j
[2025-09-05 16:59:31] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -84.480491-0.008947j
[2025-09-05 16:59:59] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -84.256923-0.004640j
[2025-09-05 17:00:30] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -84.321826-0.004274j
[2025-09-05 17:01:01] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -84.458543+0.001962j
[2025-09-05 17:01:32] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -84.571075+0.001763j
[2025-09-05 17:02:03] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -84.390698+0.001201j
[2025-09-05 17:02:34] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -84.335606+0.000085j
[2025-09-05 17:03:05] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -84.370191+0.002112j
[2025-09-05 17:03:36] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -84.338578-0.000907j
[2025-09-05 17:04:07] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -84.260881-0.002241j
[2025-09-05 17:04:38] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -84.459095-0.003625j
[2025-09-05 17:05:09] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -84.224557+0.000390j
[2025-09-05 17:05:40] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -84.420798-0.001883j
[2025-09-05 17:06:11] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -84.179855+0.006962j
[2025-09-05 17:06:42] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -84.167711+0.000333j
[2025-09-05 17:07:13] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -84.275448+0.003521j
[2025-09-05 17:07:44] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -84.236714-0.002401j
[2025-09-05 17:08:15] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -84.308284+0.001011j
[2025-09-05 17:08:46] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -84.325208+0.004615j
[2025-09-05 17:09:17] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -84.451485-0.000923j
[2025-09-05 17:09:48] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -84.276832-0.005366j
[2025-09-05 17:10:19] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -84.497252-0.008708j
[2025-09-05 17:10:50] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -84.365706+0.003390j
[2025-09-05 17:11:21] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -84.359160+0.008520j
[2025-09-05 17:11:52] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -84.369780+0.001451j
[2025-09-05 17:12:23] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -84.443104-0.001667j
[2025-09-05 17:12:54] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -84.391021-0.004360j
[2025-09-05 17:13:25] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -84.356670+0.007217j
[2025-09-05 17:13:56] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -84.383997-0.002952j
[2025-09-05 17:14:27] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -84.284921+0.000695j
[2025-09-05 17:14:50] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -84.481552+0.001352j
[2025-09-05 17:15:13] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -84.230985-0.001410j
[2025-09-05 17:15:44] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -84.363237-0.002556j
[2025-09-05 17:16:15] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -84.362827+0.008505j
[2025-09-05 17:16:46] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -84.384051-0.004275j
[2025-09-05 17:17:17] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -84.445629+0.002933j
[2025-09-05 17:17:48] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -84.361353-0.000547j
[2025-09-05 17:18:19] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -84.517599+0.001494j
[2025-09-05 17:18:49] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -84.580799+0.000485j
[2025-09-05 17:19:20] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -84.602813-0.005831j
[2025-09-05 17:19:47] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -84.419439-0.008427j
[2025-09-05 17:20:18] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -84.341500+0.002441j
[2025-09-05 17:20:49] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -84.343025+0.003463j
[2025-09-05 17:21:19] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -84.381133+0.001094j
[2025-09-05 17:21:50] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -84.278015+0.003589j
[2025-09-05 17:22:21] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -84.303425-0.003755j
[2025-09-05 17:22:52] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -84.227381+0.003832j
[2025-09-05 17:23:23] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -84.262672+0.007249j
[2025-09-05 17:23:54] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -84.384022+0.002230j
[2025-09-05 17:24:25] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -84.467045-0.004384j
[2025-09-05 17:24:56] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -84.390243+0.002217j
[2025-09-05 17:25:27] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -84.300928-0.004742j
[2025-09-05 17:25:58] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -84.445446+0.001781j
[2025-09-05 17:26:29] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -84.357846+0.000760j
[2025-09-05 17:26:59] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -84.185756+0.003736j
[2025-09-05 17:27:30] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -84.436059+0.002280j
[2025-09-05 17:28:01] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -84.345929-0.004195j
[2025-09-05 17:28:32] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -84.449234+0.002858j
[2025-09-05 17:29:03] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -84.317186+0.006373j
[2025-09-05 17:29:34] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -84.308832-0.001997j
[2025-09-05 17:30:05] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -84.337098+0.001128j
[2025-09-05 17:30:36] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -84.362248+0.005280j
[2025-09-05 17:31:07] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -84.387754+0.005573j
[2025-09-05 17:31:38] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -84.187648-0.000536j
[2025-09-05 17:32:09] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -84.234874-0.000248j
[2025-09-05 17:32:40] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -84.130364+0.000913j
[2025-09-05 17:33:11] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -84.293334+0.000847j
[2025-09-05 17:33:42] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -84.331653-0.001703j
[2025-09-05 17:34:13] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -84.187513+0.001147j
[2025-09-05 17:34:39] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -84.151076-0.001743j
[2025-09-05 17:35:00] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -84.227904+0.003631j
[2025-09-05 17:35:30] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -84.172573-0.002608j
[2025-09-05 17:36:01] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -84.284766-0.011368j
[2025-09-05 17:36:32] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -84.184506-0.002766j
[2025-09-05 17:37:03] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -84.405944+0.001926j
[2025-09-05 17:37:34] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -84.474738+0.004284j
[2025-09-05 17:38:05] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -84.345967+0.004586j
[2025-09-05 17:38:36] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -84.509284-0.000246j
[2025-09-05 17:39:06] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -84.464282+0.006514j
[2025-09-05 17:39:35] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -84.389551+0.003416j
[2025-09-05 17:40:04] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -84.449428+0.002551j
[2025-09-05 17:40:35] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -84.530564-0.004382j
[2025-09-05 17:41:06] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -84.484202-0.000606j
[2025-09-05 17:41:37] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -84.334085+0.000844j
[2025-09-05 17:42:08] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -84.292028+0.001451j
[2025-09-05 17:42:39] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -84.336901-0.006306j
[2025-09-05 17:43:10] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -84.272539-0.001768j
[2025-09-05 17:43:41] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -84.430783+0.000326j
[2025-09-05 17:44:12] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -84.486048+0.001244j
[2025-09-05 17:44:43] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -84.444093-0.001569j
[2025-09-05 17:45:14] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -84.383982-0.003095j
[2025-09-05 17:45:45] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -84.233304-0.003167j
[2025-09-05 17:46:16] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -84.424252-0.000238j
[2025-09-05 17:46:47] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -84.287743+0.004576j
[2025-09-05 17:47:18] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -84.327579+0.004175j
[2025-09-05 17:47:49] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -84.264976-0.003898j
[2025-09-05 17:48:20] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -84.316634+0.002476j
[2025-09-05 17:48:51] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -84.344878+0.003534j
[2025-09-05 17:49:22] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -84.433541-0.003891j
[2025-09-05 17:49:53] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -84.401918+0.000077j
[2025-09-05 17:50:24] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -84.374853+0.005370j
[2025-09-05 17:50:55] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -84.308204+0.003605j
[2025-09-05 17:50:55] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-05 17:51:26] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -84.405667+0.004802j
[2025-09-05 17:51:57] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -84.485390-0.008234j
[2025-09-05 17:52:28] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -84.216962-0.000114j
[2025-09-05 17:52:59] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -84.447783+0.003486j
[2025-09-05 17:53:30] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -84.451272+0.011171j
[2025-09-05 17:54:01] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -84.448184+0.002770j
[2025-09-05 17:54:31] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -84.362962-0.008324j
[2025-09-05 17:54:52] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -84.377152-0.000790j
[2025-09-05 17:55:18] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -84.440905+0.003071j
[2025-09-05 17:55:49] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -84.356106-0.001030j
[2025-09-05 17:56:19] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -84.274197+0.001162j
[2025-09-05 17:56:50] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -84.347637+0.006940j
[2025-09-05 17:57:21] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -84.454195+0.003585j
[2025-09-05 17:57:52] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -84.345101+0.006828j
[2025-09-05 17:58:23] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -84.442819+0.004054j
[2025-09-05 17:58:54] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -84.548038+0.001091j
[2025-09-05 17:59:25] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -84.461580-0.002686j
[2025-09-05 17:59:52] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -84.440929-0.004343j
[2025-09-05 18:00:22] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -84.517814-0.002241j
[2025-09-05 18:00:53] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -84.314394-0.000684j
[2025-09-05 18:01:24] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -84.548613+0.004807j
[2025-09-05 18:01:55] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -84.336289+0.002909j
[2025-09-05 18:02:26] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -84.264228-0.003962j
[2025-09-05 18:02:57] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -84.270564+0.000589j
[2025-09-05 18:03:27] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -84.297069-0.003478j
[2025-09-05 18:03:58] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -84.311752+0.000194j
[2025-09-05 18:04:29] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -84.335986+0.001024j
[2025-09-05 18:05:00] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -84.351583+0.005370j
[2025-09-05 18:05:31] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -84.432386+0.000418j
[2025-09-05 18:06:02] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -84.300883-0.004459j
[2025-09-05 18:06:02] RESTART #2 | Period: 600
[2025-09-05 18:06:33] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -84.435206+0.004156j
[2025-09-05 18:07:03] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -84.342484+0.001753j
[2025-09-05 18:07:34] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -84.061774-0.000410j
[2025-09-05 18:08:05] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -84.258006-0.003614j
[2025-09-05 18:08:36] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -84.328195+0.005796j
[2025-09-05 18:09:07] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -84.317327-0.002719j
[2025-09-05 18:09:38] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -84.436384-0.002418j
[2025-09-05 18:10:08] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -84.510830+0.006987j
[2025-09-05 18:10:39] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -84.333996-0.001027j
[2025-09-05 18:11:10] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -84.314606+0.001410j
[2025-09-05 18:11:41] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -84.159029+0.001643j
[2025-09-05 18:12:12] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -84.132992+0.004591j
[2025-09-05 18:12:43] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -83.997590-0.004333j
[2025-09-05 18:13:14] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -84.177941-0.004249j
[2025-09-05 18:13:45] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -84.248145+0.000024j
[2025-09-05 18:14:16] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -84.240523+0.002771j
[2025-09-05 18:14:40] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -84.298890-0.002356j
[2025-09-05 18:15:03] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -84.225869+0.002441j
[2025-09-05 18:15:34] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -84.244063-0.000775j
[2025-09-05 18:16:05] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -84.236049-0.001599j
[2025-09-05 18:16:36] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -84.319365-0.000736j
[2025-09-05 18:17:07] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -84.230160-0.000041j
[2025-09-05 18:17:38] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -84.177470+0.003586j
[2025-09-05 18:18:09] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -84.232751-0.003670j
[2025-09-05 18:18:40] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -84.261266+0.005630j
[2025-09-05 18:19:11] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -84.275557+0.002325j
[2025-09-05 18:19:37] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -84.380777+0.000836j
[2025-09-05 18:20:08] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -84.295922-0.001610j
[2025-09-05 18:20:39] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -84.330493-0.000308j
[2025-09-05 18:21:10] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -84.311145-0.001173j
[2025-09-05 18:21:40] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -84.290229+0.000144j
[2025-09-05 18:22:11] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -84.312892-0.003208j
[2025-09-05 18:22:42] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -84.373823-0.002736j
[2025-09-05 18:23:13] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -84.340842-0.001780j
[2025-09-05 18:23:44] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -84.346143-0.008469j
[2025-09-05 18:24:15] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -84.497043+0.003789j
[2025-09-05 18:24:46] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -84.476336+0.003635j
[2025-09-05 18:25:16] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -84.477152-0.001362j
[2025-09-05 18:25:47] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -84.433943-0.002910j
[2025-09-05 18:26:18] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -84.330949-0.002311j
[2025-09-05 18:26:49] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -84.346669+0.000428j
[2025-09-05 18:27:20] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -84.433306+0.001888j
[2025-09-05 18:27:51] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -84.377668-0.000011j
[2025-09-05 18:28:22] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -84.420987+0.003067j
[2025-09-05 18:28:52] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -84.353005-0.002379j
[2025-09-05 18:29:23] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -84.424416-0.004608j
[2025-09-05 18:29:54] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -84.374269-0.000592j
[2025-09-05 18:30:25] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -84.505443+0.005497j
[2025-09-05 18:30:56] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -84.639839+0.003556j
[2025-09-05 18:31:27] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -84.606559+0.001796j
[2025-09-05 18:31:57] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -84.450317+0.004912j
[2025-09-05 18:32:28] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -84.525472-0.005712j
[2025-09-05 18:32:59] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -84.440252-0.002198j
[2025-09-05 18:33:30] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -84.580705+0.004393j
[2025-09-05 18:34:01] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -84.319399-0.000996j
[2025-09-05 18:34:28] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -84.414802+0.001152j
[2025-09-05 18:34:49] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -84.563732-0.001924j
[2025-09-05 18:35:15] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -84.486231+0.001458j
[2025-09-05 18:35:46] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -84.480062+0.001795j
[2025-09-05 18:36:17] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -84.310318-0.001216j
[2025-09-05 18:36:47] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -84.319954+0.002280j
[2025-09-05 18:37:18] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -84.426109+0.007949j
[2025-09-05 18:37:49] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -84.304999-0.005721j
[2025-09-05 18:38:20] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -84.425150+0.000090j
[2025-09-05 18:38:51] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -84.331391-0.000788j
[2025-09-05 18:39:22] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -84.295473-0.006440j
[2025-09-05 18:39:48] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -84.290702+0.000776j
[2025-09-05 18:40:19] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -84.296668+0.003208j
[2025-09-05 18:40:50] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -84.432354-0.000373j
[2025-09-05 18:41:21] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -84.501104-0.005612j
[2025-09-05 18:41:52] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -84.565807+0.000199j
[2025-09-05 18:42:23] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -84.494636-0.000424j
[2025-09-05 18:42:54] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -84.575594-0.000778j
[2025-09-05 18:43:25] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -84.683262+0.001556j
[2025-09-05 18:43:56] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -84.531648+0.003865j
[2025-09-05 18:43:56] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-05 18:44:27] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -84.469554-0.002319j
[2025-09-05 18:44:58] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -84.470676+0.002618j
[2025-09-05 18:45:29] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -84.584856+0.001035j
[2025-09-05 18:46:00] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -84.540520+0.004231j
[2025-09-05 18:46:31] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -84.237298+0.004963j
[2025-09-05 18:47:02] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -84.206202-0.003572j
[2025-09-05 18:47:33] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -84.192069-0.003201j
[2025-09-05 18:48:04] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -84.278695+0.001553j
[2025-09-05 18:48:35] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -84.255450+0.000271j
[2025-09-05 18:49:06] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -84.467808+0.000650j
[2025-09-05 18:49:37] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -84.265982+0.002575j
[2025-09-05 18:50:08] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -84.355148-0.005454j
[2025-09-05 18:50:39] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -84.303949-0.003170j
[2025-09-05 18:51:10] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -84.197037+0.001184j
[2025-09-05 18:51:41] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -84.194080+0.004157j
[2025-09-05 18:52:12] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -84.215894-0.000153j
[2025-09-05 18:52:43] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -84.048612-0.008686j
[2025-09-05 18:53:14] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -84.255579-0.001194j
[2025-09-05 18:53:45] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -84.428153+0.002645j
[2025-09-05 18:54:16] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -84.442105+0.003222j
[2025-09-05 18:54:40] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -84.437433-0.005457j
[2025-09-05 18:55:02] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -84.399126-0.000886j
[2025-09-05 18:55:33] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -84.376238+0.000889j
[2025-09-05 18:56:04] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -84.347592-0.002363j
[2025-09-05 18:56:35] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -84.266398+0.002975j
[2025-09-05 18:57:06] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -84.364047+0.000612j
[2025-09-05 18:57:37] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -84.378120-0.001941j
[2025-09-05 18:58:08] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -84.312054+0.002472j
[2025-09-05 18:58:39] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -84.355257-0.002153j
[2025-09-05 18:59:10] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -84.347529+0.001047j
[2025-09-05 18:59:38] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -84.371812-0.003070j
[2025-09-05 19:00:07] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -84.438805-0.004903j
[2025-09-05 19:00:38] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -84.444596+0.000541j
[2025-09-05 19:01:09] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -84.398890-0.004061j
[2025-09-05 19:01:40] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -84.403455-0.004374j
[2025-09-05 19:02:10] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -84.359877+0.008582j
[2025-09-05 19:02:41] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -84.478844-0.000935j
[2025-09-05 19:03:12] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -84.549237+0.000915j
[2025-09-05 19:03:43] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -84.402287+0.002761j
[2025-09-05 19:04:14] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -84.365568+0.002623j
[2025-09-05 19:04:45] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -84.594188+0.000332j
[2025-09-05 19:05:16] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -84.437086-0.000799j
[2025-09-05 19:05:46] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -84.496160-0.000626j
[2025-09-05 19:06:17] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -84.387563+0.000268j
[2025-09-05 19:06:48] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -84.368161+0.002260j
[2025-09-05 19:07:19] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -84.449808-0.000311j
[2025-09-05 19:07:50] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -84.294333-0.000675j
[2025-09-05 19:08:21] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -84.470699-0.008700j
[2025-09-05 19:08:52] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -84.312989+0.000602j
[2025-09-05 19:09:22] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -84.413222+0.001341j
[2025-09-05 19:09:53] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -84.413331-0.003566j
[2025-09-05 19:10:24] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -84.347830-0.002599j
[2025-09-05 19:10:55] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -84.346889+0.000192j
[2025-09-05 19:11:26] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -84.393607-0.006929j
[2025-09-05 19:11:57] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -84.536975-0.003322j
[2025-09-05 19:12:27] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -84.309406+0.001738j
[2025-09-05 19:12:58] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -84.575435+0.000503j
[2025-09-05 19:13:29] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -84.408929-0.002283j
[2025-09-05 19:14:00] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -84.397261+0.003678j
[2025-09-05 19:14:29] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -84.485985+0.001052j
[2025-09-05 19:14:49] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -84.555443-0.005512j
[2025-09-05 19:15:15] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -84.244558+0.000757j
[2025-09-05 19:15:46] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -84.441104-0.001857j
[2025-09-05 19:16:17] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -84.373953-0.002213j
[2025-09-05 19:16:48] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -84.477075+0.001000j
[2025-09-05 19:17:19] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -84.501167+0.000710j
[2025-09-05 19:17:50] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -84.261433+0.000540j
[2025-09-05 19:18:21] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -84.532244+0.003661j
[2025-09-05 19:18:52] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -84.456349+0.000305j
[2025-09-05 19:19:22] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -84.546462-0.006189j
[2025-09-05 19:19:44] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -84.462172-0.002044j
[2025-09-05 19:20:13] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -84.435526+0.000539j
[2025-09-05 19:20:44] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -84.484258-0.003611j
[2025-09-05 19:21:15] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -84.568053-0.003326j
[2025-09-05 19:21:46] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -84.508547+0.004058j
[2025-09-05 19:22:17] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -84.550696+0.008329j
[2025-09-05 19:22:48] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -84.503099+0.005484j
[2025-09-05 19:23:19] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -84.378653+0.000548j
[2025-09-05 19:23:50] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -84.285830+0.003111j
[2025-09-05 19:24:21] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -84.425492-0.007528j
[2025-09-05 19:24:52] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -84.432610-0.008147j
[2025-09-05 19:25:23] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -84.391456+0.001579j
[2025-09-05 19:25:54] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -84.450361-0.002628j
[2025-09-05 19:26:25] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -84.454156-0.005908j
[2025-09-05 19:26:56] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -84.426861-0.001141j
[2025-09-05 19:27:27] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -84.546347-0.000892j
[2025-09-05 19:27:58] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -84.341114-0.004419j
[2025-09-05 19:28:29] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -84.343600+0.003578j
[2025-09-05 19:29:00] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -84.376233+0.001799j
[2025-09-05 19:29:31] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -84.400833-0.003674j
[2025-09-05 19:30:02] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -84.289116+0.001272j
[2025-09-05 19:30:33] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -84.492016+0.005890j
[2025-09-05 19:31:04] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -84.431609+0.002697j
[2025-09-05 19:31:35] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -84.461753+0.000992j
[2025-09-05 19:32:06] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -84.425659-0.001038j
[2025-09-05 19:32:37] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -84.470697-0.000620j
[2025-09-05 19:33:08] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -84.247292-0.004819j
[2025-09-05 19:33:39] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -84.329169+0.004380j
[2025-09-05 19:34:10] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -84.174261+0.004649j
[2025-09-05 19:34:40] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -84.123918-0.002872j
[2025-09-05 19:35:01] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -84.205527+0.011006j
[2025-09-05 19:35:26] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -84.131553-0.001078j
[2025-09-05 19:35:57] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -84.154510-0.003424j
[2025-09-05 19:36:28] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -84.239568+0.003581j
[2025-09-05 19:36:59] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -84.285453+0.003088j
[2025-09-05 19:36:59] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-05 19:37:30] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -84.253717-0.001214j
[2025-09-05 19:38:01] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -84.347249-0.001660j
[2025-09-05 19:38:32] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -84.335678+0.007644j
[2025-09-05 19:39:03] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -84.225404+0.002972j
[2025-09-05 19:39:34] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -84.201846-0.006760j
[2025-09-05 19:40:00] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -84.417035-0.001803j
[2025-09-05 19:40:31] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -84.468604-0.000113j
[2025-09-05 19:41:02] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -84.253432+0.005184j
[2025-09-05 19:41:33] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -84.196041-0.000871j
[2025-09-05 19:42:04] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -84.131781+0.005459j
[2025-09-05 19:42:35] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -84.270621-0.005428j
[2025-09-05 19:43:06] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -84.354128+0.001412j
[2025-09-05 19:43:37] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -84.364637-0.001637j
[2025-09-05 19:44:08] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -84.410474-0.003051j
[2025-09-05 19:44:39] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -84.336406+0.003621j
[2025-09-05 19:45:10] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -84.477288-0.002027j
[2025-09-05 19:45:41] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -84.414057+0.001344j
[2025-09-05 19:46:12] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -84.493725+0.005962j
[2025-09-05 19:46:43] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -84.363587-0.003329j
[2025-09-05 19:47:14] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -84.331753-0.006280j
[2025-09-05 19:47:45] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -84.370703+0.000434j
[2025-09-05 19:48:16] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -84.291280-0.001566j
[2025-09-05 19:48:46] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -84.302625-0.003932j
[2025-09-05 19:49:17] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -84.351229-0.002558j
[2025-09-05 19:49:48] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -84.458153-0.005281j
[2025-09-05 19:50:19] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -84.367086-0.004884j
[2025-09-05 19:50:50] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -84.292381+0.004628j
[2025-09-05 19:51:21] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -84.366496+0.006353j
[2025-09-05 19:51:52] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -84.442380+0.000153j
[2025-09-05 19:52:23] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -84.524039-0.001667j
[2025-09-05 19:52:54] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -84.443200-0.003240j
[2025-09-05 19:53:25] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -84.466320+0.003313j
[2025-09-05 19:53:56] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -84.509466+0.002074j
[2025-09-05 19:54:27] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -84.346940-0.001120j
[2025-09-05 19:54:51] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -84.453848-0.001084j
[2025-09-05 19:55:12] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -84.430697+0.000557j
[2025-09-05 19:55:43] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -84.383243-0.000675j
[2025-09-05 19:56:14] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -84.378733-0.000416j
[2025-09-05 19:56:44] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -84.344875+0.004340j
[2025-09-05 19:57:15] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -84.515340-0.003652j
[2025-09-05 19:57:46] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -84.526444+0.002565j
[2025-09-05 19:58:17] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -84.422617-0.002196j
[2025-09-05 19:58:48] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -84.491878+0.011925j
[2025-09-05 19:59:19] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -84.584973+0.003603j
[2025-09-05 19:59:48] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -84.358144-0.002076j
[2025-09-05 20:00:16] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -84.436100-0.003962j
[2025-09-05 20:00:47] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -84.348435+0.004667j
[2025-09-05 20:01:18] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -84.440215-0.004938j
[2025-09-05 20:01:49] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -84.402732+0.003887j
[2025-09-05 20:02:20] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -84.453595-0.003139j
[2025-09-05 20:02:51] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -84.384030-0.001481j
[2025-09-05 20:03:21] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -84.345787+0.004600j
[2025-09-05 20:03:52] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -84.380573-0.000964j
[2025-09-05 20:04:23] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -84.298008+0.004081j
[2025-09-05 20:04:54] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -84.314683+0.000168j
[2025-09-05 20:05:25] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -84.152220-0.001498j
[2025-09-05 20:05:56] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -84.372146-0.005043j
[2025-09-05 20:06:27] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -84.286056-0.000600j
[2025-09-05 20:06:57] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -84.364077+0.001348j
[2025-09-05 20:07:28] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -84.377774+0.000318j
[2025-09-05 20:07:59] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -84.327690-0.002345j
[2025-09-05 20:08:30] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -84.451886-0.006089j
[2025-09-05 20:09:01] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -84.545211+0.000740j
[2025-09-05 20:09:32] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -84.480220-0.001789j
[2025-09-05 20:10:03] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -84.614064+0.005718j
[2025-09-05 20:10:34] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -84.371566-0.007762j
[2025-09-05 20:11:05] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -84.393730-0.000062j
[2025-09-05 20:11:35] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -84.452108+0.002402j
[2025-09-05 20:12:06] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -84.357546+0.004050j
[2025-09-05 20:12:37] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -84.428606+0.005310j
[2025-09-05 20:13:08] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -84.407862+0.003815j
[2025-09-05 20:13:39] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -84.502972-0.002738j
[2025-09-05 20:14:10] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -84.496466+0.004356j
[2025-09-05 20:14:39] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -84.542810+0.003203j
[2025-09-05 20:15:00] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -84.455798+0.004040j
[2025-09-05 20:15:28] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -84.605722-0.001301j
[2025-09-05 20:15:59] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -84.482223-0.001794j
[2025-09-05 20:16:30] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -84.499691-0.000345j
[2025-09-05 20:17:01] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -84.582441-0.003399j
[2025-09-05 20:17:32] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -84.485325-0.000544j
[2025-09-05 20:18:03] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -84.371689-0.002294j
[2025-09-05 20:18:34] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -84.361666+0.001267j
[2025-09-05 20:19:05] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -84.352834-0.009028j
[2025-09-05 20:19:33] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -84.288102+0.004628j
[2025-09-05 20:20:02] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -84.209822-0.000771j
[2025-09-05 20:20:33] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -84.315938-0.005698j
[2025-09-05 20:21:04] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -84.190343+0.002070j
[2025-09-05 20:21:35] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -84.312549-0.000463j
[2025-09-05 20:22:06] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -84.241630-0.003571j
[2025-09-05 20:22:37] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -84.283070-0.000337j
[2025-09-05 20:23:08] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -84.126411-0.000529j
[2025-09-05 20:23:39] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -84.257342-0.005797j
[2025-09-05 20:24:10] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -84.376865-0.006155j
[2025-09-05 20:24:41] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -84.372509-0.000405j
[2025-09-05 20:25:12] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -84.450555-0.002274j
[2025-09-05 20:25:42] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -84.488714-0.007576j
[2025-09-05 20:26:13] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -84.360001-0.003429j
[2025-09-05 20:26:44] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -84.379200-0.000433j
[2025-09-05 20:27:15] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -84.264019-0.002133j
[2025-09-05 20:27:46] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -84.312291+0.000670j
[2025-09-05 20:28:17] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -84.396490-0.000036j
[2025-09-05 20:28:48] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -84.403360+0.000666j
[2025-09-05 20:29:19] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -84.503376+0.003020j
[2025-09-05 20:29:50] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -84.350204-0.003002j
[2025-09-05 20:30:21] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -84.672298-0.001417j
[2025-09-05 20:30:21] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-05 20:30:52] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -84.382795-0.002320j
[2025-09-05 20:31:23] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -84.442440-0.003987j
[2025-09-05 20:31:54] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -84.492504-0.006660j
[2025-09-05 20:32:25] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -84.574095-0.000826j
[2025-09-05 20:32:56] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -84.491414+0.002357j
[2025-09-05 20:33:27] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -84.528836+0.002410j
[2025-09-05 20:33:58] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -84.540176+0.002780j
[2025-09-05 20:34:29] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -84.474462-0.002328j
[2025-09-05 20:34:51] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -84.485554+0.007521j
[2025-09-05 20:35:14] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -84.625528-0.001962j
[2025-09-05 20:35:45] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -84.624198-0.002295j
[2025-09-05 20:36:16] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -84.440172-0.000849j
[2025-09-05 20:36:47] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -84.639524-0.005189j
[2025-09-05 20:37:18] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -84.709095-0.000635j
[2025-09-05 20:37:49] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -84.552508-0.000237j
[2025-09-05 20:38:20] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -84.563822+0.000832j
[2025-09-05 20:38:51] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -84.523875-0.002103j
[2025-09-05 20:39:22] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -84.476644-0.001504j
[2025-09-05 20:39:48] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -84.537713+0.002584j
[2025-09-05 20:40:19] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -84.582267+0.001256j
[2025-09-05 20:40:50] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -84.459958+0.000372j
[2025-09-05 20:41:21] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -84.480608-0.004526j
[2025-09-05 20:41:52] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -84.456814+0.004243j
[2025-09-05 20:42:23] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -84.458307-0.006533j
[2025-09-05 20:42:54] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -84.476313-0.003165j
[2025-09-05 20:43:25] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -84.435917+0.004259j
[2025-09-05 20:43:56] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -84.423783+0.002507j
[2025-09-05 20:44:27] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -84.392376+0.002268j
[2025-09-05 20:44:58] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -84.459288+0.001090j
[2025-09-05 20:45:29] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -84.309743-0.000729j
[2025-09-05 20:46:00] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -84.395577-0.005009j
[2025-09-05 20:46:31] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -84.264349+0.000298j
[2025-09-05 20:47:02] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -84.315506+0.001693j
[2025-09-05 20:47:33] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -84.359853+0.001369j
[2025-09-05 20:48:04] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -84.403961+0.000634j
[2025-09-05 20:48:35] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -84.223576-0.002677j
[2025-09-05 20:49:06] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -84.409188+0.000199j
[2025-09-05 20:49:36] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -84.446651-0.001329j
[2025-09-05 20:50:07] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -84.257043+0.002864j
[2025-09-05 20:50:38] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -84.333760-0.000664j
[2025-09-05 20:51:09] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -84.324530+0.010746j
[2025-09-05 20:51:40] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -84.263725-0.001729j
[2025-09-05 20:52:11] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -84.030491-0.007471j
[2025-09-05 20:52:42] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -84.245480-0.001200j
[2025-09-05 20:53:13] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -84.299602+0.003275j
[2025-09-05 20:53:44] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -84.263517-0.000267j
[2025-09-05 20:54:15] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -84.292553-0.002418j
[2025-09-05 20:54:41] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -84.479282+0.001393j
[2025-09-05 20:55:02] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -84.450024-0.007776j
[2025-09-05 20:55:32] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -84.520099+0.001976j
[2025-09-05 20:56:03] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -84.385881-0.000465j
[2025-09-05 20:56:34] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -84.451032+0.003050j
[2025-09-05 20:57:05] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -84.372426-0.003849j
[2025-09-05 20:57:36] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -84.541230-0.003329j
[2025-09-05 20:58:07] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -84.490608-0.001594j
[2025-09-05 20:58:38] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -84.473171-0.003548j
[2025-09-05 20:59:09] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -84.533255-0.003221j
[2025-09-05 20:59:37] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -84.537507+0.001249j
[2025-09-05 21:00:06] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -84.530353-0.000987j
[2025-09-05 21:00:37] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -84.570231-0.003326j
[2025-09-05 21:01:08] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -84.615696+0.005014j
[2025-09-05 21:01:38] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -84.612643+0.001031j
[2025-09-05 21:02:09] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -84.372035-0.002560j
[2025-09-05 21:02:40] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -84.389039+0.006665j
[2025-09-05 21:03:11] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -84.509499+0.001045j
[2025-09-05 21:03:42] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -84.513955-0.005720j
[2025-09-05 21:04:13] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -84.501598+0.000562j
[2025-09-05 21:04:44] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -84.500425+0.002103j
[2025-09-05 21:05:15] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -84.519738-0.002072j
[2025-09-05 21:05:46] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -84.383090-0.006045j
[2025-09-05 21:06:17] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -84.354858-0.001513j
[2025-09-05 21:06:48] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -84.478906-0.003343j
[2025-09-05 21:07:18] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -84.400371-0.002354j
[2025-09-05 21:07:49] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -84.525013-0.006155j
[2025-09-05 21:08:20] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -84.549746-0.002251j
[2025-09-05 21:08:51] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -84.375638+0.000800j
[2025-09-05 21:09:22] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -84.375286+0.003301j
[2025-09-05 21:09:53] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -84.302549+0.010967j
[2025-09-05 21:10:24] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -84.166621+0.004384j
[2025-09-05 21:10:55] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -84.103468-0.004187j
[2025-09-05 21:11:26] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -84.252421+0.002762j
[2025-09-05 21:11:57] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -84.336872-0.005406j
[2025-09-05 21:12:27] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -84.353746+0.002864j
[2025-09-05 21:12:58] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -84.271281+0.005251j
[2025-09-05 21:13:29] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -84.402744-0.001986j
[2025-09-05 21:14:00] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -84.317002-0.005284j
[2025-09-05 21:14:31] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -84.293628+0.000576j
[2025-09-05 21:14:52] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -84.447049-0.000281j
[2025-09-05 21:15:16] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -84.321460-0.000361j
[2025-09-05 21:15:47] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -84.395147-0.006372j
[2025-09-05 21:16:18] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -84.178903-0.005860j
[2025-09-05 21:16:49] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -84.421894+0.003278j
[2025-09-05 21:17:20] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -84.298732+0.001346j
[2025-09-05 21:17:51] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -84.219279-0.000448j
[2025-09-05 21:18:22] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -84.293246+0.006974j
[2025-09-05 21:18:53] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -84.393338-0.000538j
[2025-09-05 21:19:24] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -84.304802+0.003277j
[2025-09-05 21:19:50] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -84.364078+0.002449j
[2025-09-05 21:20:21] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -84.358414+0.002744j
[2025-09-05 21:20:52] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -84.302329+0.003278j
[2025-09-05 21:21:22] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -84.295207+0.006328j
[2025-09-05 21:21:53] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -84.285682+0.002242j
[2025-09-05 21:22:24] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -84.344109-0.004463j
[2025-09-05 21:22:55] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -84.446049+0.007991j
[2025-09-05 21:23:26] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -84.361558-0.003552j
[2025-09-05 21:23:26] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-05 21:23:57] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -84.646966-0.003328j
[2025-09-05 21:24:27] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -84.537332+0.005662j
[2025-09-05 21:24:58] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -84.496064-0.004021j
[2025-09-05 21:25:29] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -84.545080-0.000310j
[2025-09-05 21:26:00] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -84.576697+0.000507j
[2025-09-05 21:26:31] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -84.608875+0.000442j
[2025-09-05 21:27:02] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -84.561165-0.000287j
[2025-09-05 21:27:32] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -84.435233+0.001366j
[2025-09-05 21:28:03] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -84.366799+0.008335j
[2025-09-05 21:28:34] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -84.527699+0.005092j
[2025-09-05 21:29:05] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -84.592150-0.003840j
[2025-09-05 21:29:36] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -84.635611+0.004441j
[2025-09-05 21:30:07] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -84.717209-0.004244j
[2025-09-05 21:30:37] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -84.438097+0.002041j
[2025-09-05 21:31:08] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -84.509334-0.001962j
[2025-09-05 21:31:39] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -84.425873-0.000647j
[2025-09-05 21:32:10] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -84.510118-0.006647j
[2025-09-05 21:32:41] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -84.523586-0.003569j
[2025-09-05 21:33:12] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -84.425510+0.005297j
[2025-09-05 21:33:43] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -84.624113+0.002442j
[2025-09-05 21:34:13] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -84.628447+0.002117j
[2025-09-05 21:34:40] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -84.547796-0.009083j
[2025-09-05 21:35:01] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -84.651318-0.003377j
[2025-09-05 21:35:30] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -84.656352+0.000299j
[2025-09-05 21:36:01] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -84.562582-0.007415j
[2025-09-05 21:36:32] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -84.546702+0.002341j
[2025-09-05 21:37:03] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -84.594360-0.001832j
[2025-09-05 21:37:34] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -84.389300-0.000352j
[2025-09-05 21:38:05] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -84.299971-0.001368j
[2025-09-05 21:38:36] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -84.339705-0.000415j
[2025-09-05 21:39:07] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -84.433873+0.001332j
[2025-09-05 21:39:35] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -84.458790-0.004690j
[2025-09-05 21:40:04] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -84.189633-0.000370j
[2025-09-05 21:40:35] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -84.342906-0.004181j
[2025-09-05 21:41:06] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -84.335376-0.003685j
[2025-09-05 21:41:37] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -84.225409+0.001500j
[2025-09-05 21:42:07] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -84.335461+0.002513j
[2025-09-05 21:42:38] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -84.252810-0.001943j
[2025-09-05 21:43:09] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -84.272562+0.004600j
[2025-09-05 21:43:40] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -84.170929-0.001059j
[2025-09-05 21:44:11] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -84.246355+0.002691j
[2025-09-05 21:44:42] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -84.340021-0.000800j
[2025-09-05 21:45:13] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -84.447993+0.002422j
[2025-09-05 21:45:44] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -84.415357+0.006139j
[2025-09-05 21:46:14] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -84.379652+0.001978j
[2025-09-05 21:46:45] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -84.405973-0.003671j
[2025-09-05 21:47:16] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -84.256797-0.000896j
[2025-09-05 21:47:47] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -84.221929+0.006441j
[2025-09-05 21:48:18] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -84.327484+0.004820j
[2025-09-05 21:48:49] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -84.257313-0.004506j
[2025-09-05 21:49:20] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -84.369690+0.003049j
[2025-09-05 21:49:51] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -84.234680-0.005753j
[2025-09-05 21:50:21] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -84.311091+0.004623j
[2025-09-05 21:50:52] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -84.213246-0.003387j
[2025-09-05 21:51:23] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -84.227299-0.005098j
[2025-09-05 21:51:54] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -84.581091+0.002781j
[2025-09-05 21:52:25] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -84.419521+0.003299j
[2025-09-05 21:52:56] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -84.315437+0.004430j
[2025-09-05 21:53:27] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -84.464040-0.004196j
[2025-09-05 21:53:58] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -84.498320+0.002888j
[2025-09-05 21:54:28] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -84.368754+0.000659j
[2025-09-05 21:54:49] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -84.479070-0.005990j
[2025-09-05 21:55:15] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -84.498574+0.000963j
[2025-09-05 21:55:46] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -84.457750-0.005858j
[2025-09-05 21:56:17] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -84.495635-0.000264j
[2025-09-05 21:56:48] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -84.405284-0.000409j
[2025-09-05 21:57:19] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -84.425415+0.005656j
[2025-09-05 21:57:50] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -84.430021+0.001050j
[2025-09-05 21:58:21] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -84.445976-0.001546j
[2025-09-05 21:58:52] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -84.402273+0.000944j
[2025-09-05 21:59:22] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -84.310418+0.004236j
[2025-09-05 21:59:49] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -84.309610+0.002177j
[2025-09-05 22:00:20] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -84.360203+0.005674j
[2025-09-05 22:00:51] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -84.437689-0.001840j
[2025-09-05 22:01:21] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -84.500554+0.006801j
[2025-09-05 22:01:52] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -84.477196-0.000160j
[2025-09-05 22:02:23] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -84.448181-0.000541j
[2025-09-05 22:02:54] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -84.490651+0.000046j
[2025-09-05 22:03:25] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -84.403887-0.002095j
[2025-09-05 22:03:56] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -84.257620-0.000888j
[2025-09-05 22:04:27] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -84.418230-0.005106j
[2025-09-05 22:04:57] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -84.215352+0.001309j
[2025-09-05 22:05:28] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -84.340244-0.004309j
[2025-09-05 22:05:59] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -84.337561+0.004810j
[2025-09-05 22:06:30] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -84.134720-0.004606j
[2025-09-05 22:07:01] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -84.178989+0.000464j
[2025-09-05 22:07:32] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -84.221110-0.006380j
[2025-09-05 22:08:02] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -84.240390+0.003161j
[2025-09-05 22:08:33] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -84.379184-0.002455j
[2025-09-05 22:09:04] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -84.332382-0.002413j
[2025-09-05 22:09:35] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -84.284098+0.001298j
[2025-09-05 22:10:06] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -84.387627+0.000866j
[2025-09-05 22:10:37] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -84.337526+0.002727j
[2025-09-05 22:11:08] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -84.554434+0.000612j
[2025-09-05 22:11:39] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -84.463003+0.002822j
[2025-09-05 22:12:09] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -84.556459-0.002226j
[2025-09-05 22:12:40] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -84.534407-0.001661j
[2025-09-05 22:13:11] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -84.504120+0.004245j
[2025-09-05 22:13:42] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -84.484251-0.004395j
[2025-09-05 22:14:13] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -84.475047+0.001298j
[2025-09-05 22:14:37] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -84.458226-0.003121j
[2025-09-05 22:14:59] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -84.382801+0.012309j
[2025-09-05 22:15:30] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -84.414479+0.000023j
[2025-09-05 22:16:01] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -84.463470+0.002114j
[2025-09-05 22:16:32] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -84.359976+0.002785j
[2025-09-05 22:16:32] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-05 22:17:03] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -84.364055+0.005600j
[2025-09-05 22:17:34] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -84.310860-0.000665j
[2025-09-05 22:18:05] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -84.407684-0.000496j
[2025-09-05 22:18:36] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -84.413489+0.004389j
[2025-09-05 22:19:07] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -84.248039+0.001738j
[2025-09-05 22:19:35] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -84.406566-0.002534j
[2025-09-05 22:20:04] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -84.488036-0.004400j
[2025-09-05 22:20:35] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -84.538577-0.001280j
[2025-09-05 22:21:06] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -84.582413+0.004161j
[2025-09-05 22:21:36] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -84.505299-0.003161j
[2025-09-05 22:22:07] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -84.520195+0.001051j
[2025-09-05 22:22:38] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -84.702927+0.002116j
[2025-09-05 22:23:09] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -84.662450+0.000853j
[2025-09-05 22:23:40] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -84.638493-0.000457j
[2025-09-05 22:24:11] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -84.552823+0.007117j
[2025-09-05 22:24:42] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -84.349865+0.004008j
[2025-09-05 22:25:13] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -84.487688-0.005663j
[2025-09-05 22:25:43] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -84.443699+0.002442j
[2025-09-05 22:26:14] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -84.452899-0.004726j
[2025-09-05 22:26:45] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -84.449347+0.003973j
[2025-09-05 22:27:16] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -84.262478+0.002629j
[2025-09-05 22:27:47] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -84.208171-0.002181j
[2025-09-05 22:28:18] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -84.275676-0.007010j
[2025-09-05 22:28:49] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -84.415306-0.000195j
[2025-09-05 22:29:20] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -84.133922-0.002730j
[2025-09-05 22:29:50] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -84.300005-0.007190j
[2025-09-05 22:30:21] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -84.338075-0.003747j
[2025-09-05 22:30:52] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -84.403944-0.005453j
[2025-09-05 22:31:23] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -84.349562+0.001817j
[2025-09-05 22:31:54] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -84.353524+0.000663j
[2025-09-05 22:32:25] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -84.545843-0.004562j
[2025-09-05 22:32:56] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -84.475265-0.001026j
[2025-09-05 22:33:26] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -84.371546-0.005941j
[2025-09-05 22:33:57] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -84.216183-0.000743j
[2025-09-05 22:34:26] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -84.120088+0.002843j
[2025-09-05 22:34:46] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -84.272340-0.001210j
[2025-09-05 22:35:13] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -84.295991-0.002157j
[2025-09-05 22:35:44] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -84.099045-0.000872j
[2025-09-05 22:36:15] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -84.163779+0.006053j
[2025-09-05 22:36:46] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -84.173098-0.002083j
[2025-09-05 22:37:17] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -84.244218+0.000015j
[2025-09-05 22:37:48] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -84.238455+0.001961j
[2025-09-05 22:38:19] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -84.373417+0.000725j
[2025-09-05 22:38:50] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -84.502671-0.001933j
[2025-09-05 22:39:19] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -84.324537-0.006506j
[2025-09-05 22:39:47] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -84.415583-0.000531j
[2025-09-05 22:40:18] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -84.303954+0.006775j
[2025-09-05 22:40:49] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -84.303215-0.001444j
[2025-09-05 22:41:20] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -84.364444-0.000768j
[2025-09-05 22:41:51] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -84.432420-0.005058j
[2025-09-05 22:42:22] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -84.571470+0.003337j
[2025-09-05 22:42:53] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -84.543916-0.007343j
[2025-09-05 22:43:24] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -84.380600-0.002556j
[2025-09-05 22:43:55] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -84.323936+0.000919j
[2025-09-05 22:44:25] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -84.418774+0.000640j
[2025-09-05 22:44:56] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -84.481476+0.000451j
[2025-09-05 22:45:27] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -84.553470-0.000467j
[2025-09-05 22:45:58] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -84.368288+0.000618j
[2025-09-05 22:46:29] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -84.449117-0.003361j
[2025-09-05 22:47:00] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -84.408953-0.004076j
[2025-09-05 22:47:31] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -84.377304-0.000040j
[2025-09-05 22:48:02] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -84.386279+0.004728j
[2025-09-05 22:48:33] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -84.302623+0.001500j
[2025-09-05 22:49:04] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -84.282501-0.004339j
[2025-09-05 22:49:35] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -84.348105-0.001910j
[2025-09-05 22:50:06] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -84.479391+0.000570j
[2025-09-05 22:50:36] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -84.398696-0.002449j
[2025-09-05 22:51:07] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -84.487710+0.002396j
[2025-09-05 22:51:38] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -84.282072-0.000349j
[2025-09-05 22:52:09] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -84.382233-0.000659j
[2025-09-05 22:52:40] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -84.313591+0.000013j
[2025-09-05 22:53:11] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -84.427984-0.003907j
[2025-09-05 22:53:42] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -84.350356+0.000659j
[2025-09-05 22:54:13] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -84.317431-0.002215j
[2025-09-05 22:54:35] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -84.542608+0.000831j
[2025-09-05 22:54:57] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -84.546857-0.003023j
[2025-09-05 22:55:28] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -84.611491-0.000778j
[2025-09-05 22:55:59] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -84.404352+0.001773j
[2025-09-05 22:56:30] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -84.314416-0.002625j
[2025-09-05 22:57:01] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -84.203804-0.000365j
[2025-09-05 22:57:31] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -84.250055-0.007198j
[2025-09-05 22:58:02] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -84.290103+0.001700j
[2025-09-05 22:58:33] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -84.311126+0.004992j
[2025-09-05 22:59:04] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -84.390778+0.006736j
[2025-09-05 22:59:33] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -84.472845+0.000883j
[2025-09-05 23:00:02] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -84.388154+0.000881j
[2025-09-05 23:00:33] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -84.379629+0.001662j
[2025-09-05 23:01:03] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -84.280871+0.002733j
[2025-09-05 23:01:34] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -84.469657+0.004907j
[2025-09-05 23:02:05] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -84.375981+0.001156j
[2025-09-05 23:02:36] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -84.507929+0.003763j
[2025-09-05 23:03:07] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -84.469418-0.000123j
[2025-09-05 23:03:38] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -84.408753-0.003956j
[2025-09-05 23:04:08] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -84.495166+0.003364j
[2025-09-05 23:04:30] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -84.220179-0.005620j
[2025-09-05 23:04:51] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -84.379958-0.003451j
[2025-09-05 23:05:11] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -84.301315-0.006584j
[2025-09-05 23:05:32] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -84.378726+0.003136j
[2025-09-05 23:05:53] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -84.523510-0.012855j
[2025-09-05 23:06:13] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -84.560450-0.000704j
[2025-09-05 23:06:34] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -84.492880+0.003834j
[2025-09-05 23:06:54] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -84.342493-0.002303j
[2025-09-05 23:07:15] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -84.347699+0.004961j
[2025-09-05 23:07:35] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -84.380318+0.004676j
[2025-09-05 23:07:56] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -84.406308-0.004289j
[2025-09-05 23:07:56] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-05 23:07:56] ✅ Training completed | Restarts: 2
[2025-09-05 23:07:56] ============================================================
[2025-09-05 23:07:56] Training completed | Runtime: 31857.7s
[2025-09-05 23:08:04] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-05 23:08:04] ============================================================
