[2025-09-06 16:47:32] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.01/training/checkpoints/final_GCNN.pkl
[2025-09-06 16:47:32]   - 迭代次数: final
[2025-09-06 16:47:32]   - 能量: -82.957060-0.000110j ± 0.114086
[2025-09-06 16:47:32]   - 时间戳: 2025-09-06T16:44:43.807094+08:00
[2025-09-06 16:47:45] ✓ 变分状态参数已从checkpoint恢复
[2025-09-06 16:47:45] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-06 16:47:45] ==================================================
[2025-09-06 16:47:45] GCNN for Shastry-Sutherland Model
[2025-09-06 16:47:45] ==================================================
[2025-09-06 16:47:45] System parameters:
[2025-09-06 16:47:45]   - System size: L=5, N=100
[2025-09-06 16:47:45]   - System parameters: J1=-0.0, J2=0.0, Q=1.0
[2025-09-06 16:47:45] --------------------------------------------------
[2025-09-06 16:47:45] Model parameters:
[2025-09-06 16:47:45]   - Number of layers = 4
[2025-09-06 16:47:45]   - Number of features = 4
[2025-09-06 16:47:45]   - Total parameters = 19628
[2025-09-06 16:47:45] --------------------------------------------------
[2025-09-06 16:47:45] Training parameters:
[2025-09-06 16:47:45]   - Learning rate: 0.015
[2025-09-06 16:47:45]   - Total iterations: 1050
[2025-09-06 16:47:45]   - Annealing cycles: 3
[2025-09-06 16:47:45]   - Initial period: 150
[2025-09-06 16:47:45]   - Period multiplier: 2.0
[2025-09-06 16:47:45]   - Temperature range: 0.0-1.0
[2025-09-06 16:47:45]   - Samples: 4096
[2025-09-06 16:47:45]   - Discarded samples: 0
[2025-09-06 16:47:45]   - Chunk size: 2048
[2025-09-06 16:47:45]   - Diagonal shift: 0.2
[2025-09-06 16:47:45]   - Gradient clipping: 1.0
[2025-09-06 16:47:45]   - Checkpoint enabled: interval=105
[2025-09-06 16:47:45]   - Checkpoint directory: results/L=5/J2=0.00/J1=-0.00/training/checkpoints
[2025-09-06 16:47:45] --------------------------------------------------
[2025-09-06 16:47:45] Device status:
[2025-09-06 16:47:45]   - Devices model: NVIDIA H200 NVL
[2025-09-06 16:47:45]   - Number of devices: 1
[2025-09-06 16:47:45]   - Sharding: True
[2025-09-06 16:47:45] ============================================================
[2025-09-06 16:48:41] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -82.680935+0.008482j
[2025-09-06 16:49:21] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -82.551947+0.016691j
[2025-09-06 16:49:42] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -82.635254+0.005500j
[2025-09-06 16:50:03] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -82.466541+0.005814j
[2025-09-06 16:50:24] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -82.639460-0.003441j
[2025-09-06 16:50:45] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -82.592167+0.003982j
[2025-09-06 16:51:06] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -82.549607-0.000227j
[2025-09-06 16:51:26] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -82.410857-0.006731j
[2025-09-06 16:51:47] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -82.499029+0.000878j
[2025-09-06 16:52:08] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -82.499892-0.001193j
[2025-09-06 16:52:29] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -82.586017+0.000365j
[2025-09-06 16:52:50] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -82.712832-0.008156j
[2025-09-06 16:53:11] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -82.636664-0.005392j
[2025-09-06 16:53:32] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -82.573939-0.004051j
[2025-09-06 16:53:52] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -82.562572-0.001522j
[2025-09-06 16:54:13] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -82.536310+0.001126j
[2025-09-06 16:54:34] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -82.594835-0.002251j
[2025-09-06 16:54:55] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -82.492315-0.006281j
[2025-09-06 16:55:16] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -82.609207+0.005392j
[2025-09-06 16:55:37] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -82.615325+0.001490j
[2025-09-06 16:55:57] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -82.544213+0.001941j
[2025-09-06 16:56:18] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -82.661985+0.000316j
[2025-09-06 16:56:39] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -82.682281+0.002524j
[2025-09-06 16:57:00] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -82.714709-0.009862j
[2025-09-06 16:57:21] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -82.663479-0.000969j
[2025-09-06 16:57:42] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -82.666137-0.006431j
[2025-09-06 16:58:03] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -82.645555+0.000903j
[2025-09-06 16:58:23] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -82.738832-0.001419j
[2025-09-06 16:58:44] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -82.697527-0.002759j
[2025-09-06 16:59:05] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -82.801837+0.001365j
[2025-09-06 16:59:26] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -82.778714+0.005200j
[2025-09-06 16:59:47] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -82.769350+0.000873j
[2025-09-06 17:00:08] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -82.569851+0.003524j
[2025-09-06 17:00:28] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -82.547267-0.003490j
[2025-09-06 17:00:49] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -82.505822-0.001366j
[2025-09-06 17:01:07] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -82.576331+0.000538j
[2025-09-06 17:01:22] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -82.487939+0.002313j
[2025-09-06 17:01:37] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -82.486513+0.002143j
[2025-09-06 17:01:58] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -82.604330+0.004182j
[2025-09-06 17:02:19] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -82.617382+0.002625j
[2025-09-06 17:02:40] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -82.558754+0.000172j
[2025-09-06 17:03:01] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -82.594707-0.000560j
[2025-09-06 17:03:22] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -82.467440+0.000096j
[2025-09-06 17:03:43] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -82.635131-0.003639j
[2025-09-06 17:04:04] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -82.667338-0.005469j
[2025-09-06 17:04:24] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -82.601372-0.002496j
[2025-09-06 17:04:45] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -82.673662-0.004337j
[2025-09-06 17:05:06] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -82.487025-0.003249j
[2025-09-06 17:05:27] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -82.659089+0.002267j
[2025-09-06 17:05:48] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -82.476245+0.003783j
[2025-09-06 17:06:07] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -82.580862+0.002511j
[2025-09-06 17:06:25] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -82.621517-0.003322j
[2025-09-06 17:06:46] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -82.493147-0.002234j
[2025-09-06 17:07:07] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -82.438014+0.003262j
[2025-09-06 17:07:28] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -82.389936-0.002029j
[2025-09-06 17:07:49] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -82.389919-0.000071j
[2025-09-06 17:08:09] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -82.351166-0.001825j
[2025-09-06 17:08:30] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -82.292403-0.001614j
[2025-09-06 17:08:51] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -82.393666-0.002677j
[2025-09-06 17:09:12] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -82.436855-0.002724j
[2025-09-06 17:09:33] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -82.436435+0.000866j
[2025-09-06 17:09:54] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -82.528851+0.001447j
[2025-09-06 17:10:14] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -82.658993+0.000163j
[2025-09-06 17:10:35] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -82.663908-0.003011j
[2025-09-06 17:10:56] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -82.755232+0.004532j
[2025-09-06 17:11:17] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -82.789196-0.002501j
[2025-09-06 17:11:38] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -82.601098-0.004247j
[2025-09-06 17:11:59] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -82.654543+0.001342j
[2025-09-06 17:12:20] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -82.530324+0.001201j
[2025-09-06 17:12:40] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -82.483457-0.001685j
[2025-09-06 17:13:01] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -82.544462+0.001993j
[2025-09-06 17:13:22] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -82.485632+0.002614j
[2025-09-06 17:13:43] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -82.423815+0.005004j
[2025-09-06 17:14:04] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -82.528891+0.002617j
[2025-09-06 17:14:25] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -82.654962+0.001366j
[2025-09-06 17:14:46] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -82.570286+0.001619j
[2025-09-06 17:15:06] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -82.619935-0.000271j
[2025-09-06 17:15:27] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -82.536082-0.004224j
[2025-09-06 17:15:48] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -82.673394-0.001600j
[2025-09-06 17:16:09] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -82.629647-0.001306j
[2025-09-06 17:16:30] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -82.567564+0.003904j
[2025-09-06 17:16:51] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -82.610341-0.000122j
[2025-09-06 17:17:12] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -82.605095-0.000142j
[2025-09-06 17:17:32] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -82.665530-0.002262j
[2025-09-06 17:17:53] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -82.679539-0.003356j
[2025-09-06 17:18:14] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -82.694686-0.000677j
[2025-09-06 17:18:35] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -82.717394-0.000867j
[2025-09-06 17:18:56] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -82.690007+0.003137j
[2025-09-06 17:19:17] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -82.627012-0.002236j
[2025-09-06 17:19:38] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -82.525629+0.000880j
[2025-09-06 17:19:58] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -82.695388-0.003293j
[2025-09-06 17:20:19] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -82.503773-0.002559j
[2025-09-06 17:20:40] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -82.413541+0.000194j
[2025-09-06 17:21:00] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -82.507474+0.000500j
[2025-09-06 17:21:14] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -82.496689-0.002455j
[2025-09-06 17:21:28] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -82.736035-0.004917j
[2025-09-06 17:21:49] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -82.545883-0.000066j
[2025-09-06 17:22:10] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -82.632411+0.000464j
[2025-09-06 17:22:31] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -82.628265-0.002618j
[2025-09-06 17:22:52] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -82.544812+0.002034j
[2025-09-06 17:23:13] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -82.607160+0.003310j
[2025-09-06 17:23:34] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -82.646691+0.000557j
[2025-09-06 17:23:54] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -82.699559+0.002350j
[2025-09-06 17:24:15] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -82.469916+0.000521j
[2025-09-06 17:24:36] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -82.574825+0.001431j
[2025-09-06 17:24:36] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-06 17:24:57] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -82.604447-0.004452j
[2025-09-06 17:25:18] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -82.628833-0.002580j
[2025-09-06 17:25:39] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -82.600478+0.001199j
[2025-09-06 17:25:58] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -82.654831-0.004737j
[2025-09-06 17:26:16] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -82.753647-0.003102j
[2025-09-06 17:26:37] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -82.713585+0.000037j
[2025-09-06 17:26:58] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -82.738126-0.000058j
[2025-09-06 17:27:19] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -82.707707-0.002236j
[2025-09-06 17:27:40] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -82.528071-0.004942j
[2025-09-06 17:28:01] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -82.710289-0.001937j
[2025-09-06 17:28:22] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -82.722037+0.001852j
[2025-09-06 17:28:42] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -82.598619+0.000599j
[2025-09-06 17:29:03] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -82.581120-0.000617j
[2025-09-06 17:29:24] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -82.662071-0.001914j
[2025-09-06 17:29:45] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -82.526693-0.000528j
[2025-09-06 17:30:06] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -82.685751-0.002040j
[2025-09-06 17:30:27] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -82.759871-0.003241j
[2025-09-06 17:30:48] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -82.803273+0.001554j
[2025-09-06 17:31:09] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -82.705806-0.002676j
[2025-09-06 17:31:29] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -82.672092+0.001661j
[2025-09-06 17:31:50] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -82.614524-0.001606j
[2025-09-06 17:32:11] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -82.738980-0.001898j
[2025-09-06 17:32:32] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -82.563411+0.001546j
[2025-09-06 17:32:53] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -82.621104-0.005747j
[2025-09-06 17:33:14] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -82.505325+0.001304j
[2025-09-06 17:33:35] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -82.405564-0.001560j
[2025-09-06 17:33:56] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -82.473783+0.004002j
[2025-09-06 17:34:17] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -82.305266+0.002788j
[2025-09-06 17:34:38] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -82.339608+0.004444j
[2025-09-06 17:34:58] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -82.470924+0.006683j
[2025-09-06 17:35:19] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -82.467682-0.006659j
[2025-09-06 17:35:40] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -82.387152+0.000639j
[2025-09-06 17:36:01] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -82.297881+0.003069j
[2025-09-06 17:36:22] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -82.599482+0.002452j
[2025-09-06 17:36:43] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -82.566270-0.000274j
[2025-09-06 17:37:04] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -82.589356-0.000278j
[2025-09-06 17:37:25] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -82.560409-0.000109j
[2025-09-06 17:37:46] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -82.523012+0.000510j
[2025-09-06 17:38:06] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -82.603410+0.004310j
[2025-09-06 17:38:27] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -82.655528+0.001371j
[2025-09-06 17:38:48] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -82.470870-0.002174j
[2025-09-06 17:39:09] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -82.512065-0.000481j
[2025-09-06 17:39:30] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -82.491344+0.007478j
[2025-09-06 17:39:51] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -82.484526+0.002622j
[2025-09-06 17:40:12] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -82.466073+0.000197j
[2025-09-06 17:40:12] RESTART #1 | Period: 300
[2025-09-06 17:40:33] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -82.455084+0.002112j
[2025-09-06 17:40:53] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -82.591943+0.002021j
[2025-09-06 17:41:07] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -82.672618-0.004247j
[2025-09-06 17:41:22] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -82.688887+0.000722j
[2025-09-06 17:41:40] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -82.720935-0.001004j
[2025-09-06 17:42:01] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -82.535681+0.003777j
[2025-09-06 17:42:22] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -82.498683-0.000191j
[2025-09-06 17:42:43] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -82.471814-0.000192j
[2025-09-06 17:43:04] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -82.636068-0.005837j
[2025-09-06 17:43:25] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -82.540330-0.000782j
[2025-09-06 17:43:46] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -82.428398+0.002434j
[2025-09-06 17:44:07] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -82.512963+0.002180j
[2025-09-06 17:44:28] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -82.478858+0.002696j
[2025-09-06 17:44:49] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -82.683574-0.002932j
[2025-09-06 17:45:09] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -82.664428-0.002881j
[2025-09-06 17:45:30] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -82.692752-0.001238j
[2025-09-06 17:45:50] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -82.673257+0.004050j
[2025-09-06 17:46:07] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -82.568145+0.002543j
[2025-09-06 17:46:28] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -82.565474+0.000704j
[2025-09-06 17:46:49] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -82.621526+0.004338j
[2025-09-06 17:47:10] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -82.529378-0.003177j
[2025-09-06 17:47:31] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -82.579063-0.000780j
[2025-09-06 17:47:52] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -82.658848+0.002351j
[2025-09-06 17:48:13] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -82.526716-0.001859j
[2025-09-06 17:48:34] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -82.516799-0.001573j
[2025-09-06 17:48:55] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -82.525247-0.001372j
[2025-09-06 17:49:15] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -82.490390-0.009762j
[2025-09-06 17:49:36] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -82.548165+0.000912j
[2025-09-06 17:49:57] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -82.520642-0.000909j
[2025-09-06 17:50:18] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -82.456531+0.001851j
[2025-09-06 17:50:39] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -82.313855-0.002242j
[2025-09-06 17:51:00] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -82.466298+0.000097j
[2025-09-06 17:51:21] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -82.446954-0.002572j
[2025-09-06 17:51:42] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -82.569487+0.004749j
[2025-09-06 17:52:03] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -82.537264-0.000529j
[2025-09-06 17:52:24] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -82.596037+0.000467j
[2025-09-06 17:52:44] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -82.528456+0.001981j
[2025-09-06 17:53:05] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -82.489634+0.002959j
[2025-09-06 17:53:26] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -82.369763+0.003214j
[2025-09-06 17:53:47] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -82.369865-0.003196j
[2025-09-06 17:54:08] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -82.451201-0.000436j
[2025-09-06 17:54:29] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -82.425523-0.001486j
[2025-09-06 17:54:50] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -82.419691+0.001413j
[2025-09-06 17:55:11] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -82.518861+0.000747j
[2025-09-06 17:55:32] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -82.432510-0.001123j
[2025-09-06 17:55:53] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -82.488094+0.001498j
[2025-09-06 17:56:13] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -82.414698+0.001574j
[2025-09-06 17:56:34] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -82.604913+0.000510j
[2025-09-06 17:56:55] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -82.624374-0.004421j
[2025-09-06 17:57:16] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -82.897393+0.001619j
[2025-09-06 17:57:37] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -82.515904+0.001317j
[2025-09-06 17:57:58] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -82.483290+0.001190j
[2025-09-06 17:58:19] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -82.556486-0.000466j
[2025-09-06 17:58:40] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -82.538492-0.000646j
[2025-09-06 17:59:01] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -82.647023+0.003840j
[2025-09-06 17:59:22] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -82.544082-0.002811j
[2025-09-06 17:59:42] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -82.501216-0.001455j
[2025-09-06 18:00:03] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -82.527685+0.000044j
[2025-09-06 18:00:24] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -82.555230-0.003395j
[2025-09-06 18:00:45] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -82.567596-0.002009j
[2025-09-06 18:00:45] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-06 18:01:01] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -82.532182+0.001477j
[2025-09-06 18:01:15] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -82.547524+0.000847j
[2025-09-06 18:01:33] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -82.456918+0.001652j
[2025-09-06 18:01:54] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -82.592493-0.002438j
[2025-09-06 18:02:14] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -82.619152-0.002813j
[2025-09-06 18:02:35] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -82.627940+0.000571j
[2025-09-06 18:02:56] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -82.478613+0.006021j
[2025-09-06 18:03:17] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -82.409927+0.001297j
[2025-09-06 18:03:38] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -82.618845-0.000005j
[2025-09-06 18:03:59] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -82.574623-0.003600j
[2025-09-06 18:04:20] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -82.707987-0.002592j
[2025-09-06 18:04:41] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -82.296517-0.000109j
[2025-09-06 18:05:02] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -82.445151-0.001214j
[2025-09-06 18:05:23] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -82.400272-0.001360j
[2025-09-06 18:05:44] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -82.598350-0.000348j
[2025-09-06 18:06:01] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -82.425624-0.001943j
[2025-09-06 18:06:21] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -82.569656+0.001855j
[2025-09-06 18:06:42] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -82.461285-0.001055j
[2025-09-06 18:07:02] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -82.560987+0.000837j
[2025-09-06 18:07:23] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -82.694116+0.000426j
[2025-09-06 18:07:44] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -82.532812-0.003798j
[2025-09-06 18:08:05] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -82.436172+0.000221j
[2025-09-06 18:08:26] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -82.458843-0.005784j
[2025-09-06 18:08:47] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -82.305688-0.002284j
[2025-09-06 18:09:07] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -82.457972+0.000842j
[2025-09-06 18:09:28] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -82.563481+0.000834j
[2025-09-06 18:09:49] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -82.456420+0.002946j
[2025-09-06 18:10:10] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -82.604000+0.003458j
[2025-09-06 18:10:31] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -82.575966-0.002654j
[2025-09-06 18:10:52] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -82.529844+0.000893j
[2025-09-06 18:11:13] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -82.542669-0.000305j
[2025-09-06 18:11:33] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -82.561141-0.000076j
[2025-09-06 18:11:54] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -82.503345+0.000089j
[2025-09-06 18:12:15] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -82.566154+0.002410j
[2025-09-06 18:12:36] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -82.610087-0.001159j
[2025-09-06 18:12:57] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -82.519296-0.001439j
[2025-09-06 18:13:18] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -82.453497-0.002430j
[2025-09-06 18:13:39] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -82.451603+0.001639j
[2025-09-06 18:13:59] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -82.531604+0.000575j
[2025-09-06 18:14:20] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -82.574155+0.003512j
[2025-09-06 18:14:41] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -82.611835+0.007102j
[2025-09-06 18:15:02] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -82.728332+0.005612j
[2025-09-06 18:15:23] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -82.385656+0.003012j
[2025-09-06 18:15:44] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -82.519430+0.004713j
[2025-09-06 18:16:04] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -82.622280-0.000430j
[2025-09-06 18:16:25] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -82.715133+0.002011j
[2025-09-06 18:16:46] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -82.610490-0.000936j
[2025-09-06 18:17:07] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -82.591898-0.001142j
[2025-09-06 18:17:28] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -82.596104+0.003594j
[2025-09-06 18:17:49] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -82.616955-0.003150j
[2025-09-06 18:18:10] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -82.634058+0.001966j
[2025-09-06 18:18:30] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -82.611498+0.000850j
[2025-09-06 18:18:51] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -82.605433+0.000192j
[2025-09-06 18:19:12] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -82.647424+0.001864j
[2025-09-06 18:19:33] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -82.521344+0.003569j
[2025-09-06 18:19:54] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -82.425842-0.000264j
[2025-09-06 18:20:15] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -82.476613+0.000028j
[2025-09-06 18:20:35] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -82.451020+0.004301j
[2025-09-06 18:20:53] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -82.517275+0.000487j
[2025-09-06 18:21:07] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -82.398633+0.002645j
[2025-09-06 18:21:23] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -82.435553+0.003154j
[2025-09-06 18:21:44] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -82.331162-0.002275j
[2025-09-06 18:22:05] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -82.520383-0.000722j
[2025-09-06 18:22:26] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -82.526431-0.001601j
[2025-09-06 18:22:47] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -82.703751+0.000662j
[2025-09-06 18:23:08] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -82.755255+0.005398j
[2025-09-06 18:23:29] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -82.697743-0.000340j
[2025-09-06 18:23:50] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -82.760848+0.001538j
[2025-09-06 18:24:11] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -82.680295-0.000541j
[2025-09-06 18:24:32] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -82.781966-0.000772j
[2025-09-06 18:24:53] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -82.839451-0.003098j
[2025-09-06 18:25:13] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -82.850370+0.002438j
[2025-09-06 18:25:34] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -82.692850-0.004261j
[2025-09-06 18:25:53] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -82.783701-0.001672j
[2025-09-06 18:26:11] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -82.560475+0.001967j
[2025-09-06 18:26:32] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -82.709692+0.001068j
[2025-09-06 18:26:53] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -82.607694-0.004137j
[2025-09-06 18:27:14] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -82.607048-0.002419j
[2025-09-06 18:27:35] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -82.587719+0.001377j
[2025-09-06 18:27:56] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -82.449528-0.002909j
[2025-09-06 18:28:17] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -82.408958-0.000920j
[2025-09-06 18:28:37] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -82.678026+0.004288j
[2025-09-06 18:28:58] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -82.579097+0.001598j
[2025-09-06 18:29:19] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -82.563881-0.003638j
[2025-09-06 18:29:40] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -82.421602+0.003613j
[2025-09-06 18:30:01] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -82.420843-0.001153j
[2025-09-06 18:30:22] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -82.231728-0.001356j
[2025-09-06 18:30:42] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -82.455706-0.003277j
[2025-09-06 18:31:03] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -82.495371+0.003394j
[2025-09-06 18:31:24] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -82.523539-0.001755j
[2025-09-06 18:31:45] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -82.580947-0.002420j
[2025-09-06 18:32:06] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -82.659557-0.004017j
[2025-09-06 18:32:27] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -82.708379+0.001629j
[2025-09-06 18:32:47] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -82.639860+0.001695j
[2025-09-06 18:33:08] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -82.692864+0.000785j
[2025-09-06 18:33:29] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -82.537604-0.001872j
[2025-09-06 18:33:50] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -82.574555-0.001812j
[2025-09-06 18:34:11] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -82.685563+0.008596j
[2025-09-06 18:34:32] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -82.734802-0.001327j
[2025-09-06 18:34:52] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -82.702084-0.003431j
[2025-09-06 18:35:13] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -82.447593+0.000230j
[2025-09-06 18:35:34] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -82.679376+0.001123j
[2025-09-06 18:35:55] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -82.676186-0.001266j
[2025-09-06 18:36:16] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -82.585136-0.000329j
[2025-09-06 18:36:37] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -82.508323+0.000323j
[2025-09-06 18:36:37] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-06 18:36:58] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -82.543383-0.009754j
[2025-09-06 18:37:18] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -82.456073-0.003840j
[2025-09-06 18:37:39] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -82.416038+0.000237j
[2025-09-06 18:38:00] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -82.362030+0.020579j
[2025-09-06 18:38:21] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -82.440251-0.001826j
[2025-09-06 18:38:42] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -82.467210+0.001137j
[2025-09-06 18:39:03] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -82.620419+0.001010j
[2025-09-06 18:39:23] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -82.543252+0.001044j
[2025-09-06 18:39:44] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -82.679196+0.000526j
[2025-09-06 18:40:05] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -82.585506+0.001242j
[2025-09-06 18:40:26] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -82.588463-0.003925j
[2025-09-06 18:40:45] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -82.475575-0.001401j
[2025-09-06 18:40:59] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -82.592906-0.000948j
[2025-09-06 18:41:13] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -82.592277+0.003345j
[2025-09-06 18:41:32] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -82.613595+0.001508j
[2025-09-06 18:41:53] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -82.630452+0.000445j
[2025-09-06 18:42:14] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -82.619558-0.001282j
[2025-09-06 18:42:35] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -82.592036-0.003349j
[2025-09-06 18:42:56] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -82.414553+0.000102j
[2025-09-06 18:43:17] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -82.438445-0.001209j
[2025-09-06 18:43:38] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -82.501165+0.000573j
[2025-09-06 18:43:59] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -82.561881-0.003308j
[2025-09-06 18:44:20] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -82.703177-0.001412j
[2025-09-06 18:44:41] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -82.761008+0.001238j
[2025-09-06 18:45:02] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -82.681284+0.002637j
[2025-09-06 18:45:23] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -82.683673+0.005617j
[2025-09-06 18:45:42] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -82.535496+0.002644j
[2025-09-06 18:46:00] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -82.627690+0.002180j
[2025-09-06 18:46:21] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -82.750986+0.000533j
[2025-09-06 18:46:42] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -82.600048+0.001038j
[2025-09-06 18:47:03] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -82.522845+0.000279j
[2025-09-06 18:47:23] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -82.810417-0.002808j
[2025-09-06 18:47:44] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -82.676684-0.001103j
[2025-09-06 18:48:05] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -82.575404+0.005845j
[2025-09-06 18:48:26] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -82.656361+0.004349j
[2025-09-06 18:48:47] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -82.667070+0.006302j
[2025-09-06 18:49:08] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -82.602259-0.000525j
[2025-09-06 18:49:29] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -82.750772+0.008288j
[2025-09-06 18:49:50] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -82.589781+0.002867j
[2025-09-06 18:50:11] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -82.556159-0.004036j
[2025-09-06 18:50:32] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -82.572266+0.001275j
[2025-09-06 18:50:53] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -82.538562+0.002570j
[2025-09-06 18:51:14] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -82.614524+0.001935j
[2025-09-06 18:51:35] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -82.675062+0.001498j
[2025-09-06 18:51:55] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -82.489267-0.002334j
[2025-09-06 18:52:16] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -82.582106-0.000927j
[2025-09-06 18:52:37] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -82.571460+0.000999j
[2025-09-06 18:52:58] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -82.663575-0.001867j
[2025-09-06 18:53:19] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -82.697373+0.003314j
[2025-09-06 18:53:40] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -82.586759+0.003493j
[2025-09-06 18:54:01] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -82.528205+0.001081j
[2025-09-06 18:54:22] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -82.610876+0.004304j
[2025-09-06 18:54:43] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -82.400091+0.000198j
[2025-09-06 18:55:04] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -82.380016+0.005375j
[2025-09-06 18:55:25] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -82.378367+0.002861j
[2025-09-06 18:55:46] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -82.424798+0.001186j
[2025-09-06 18:56:07] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -82.368138-0.006030j
[2025-09-06 18:56:28] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -82.382172-0.005815j
[2025-09-06 18:56:48] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -82.416355-0.001818j
[2025-09-06 18:57:09] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -82.439389-0.000631j
[2025-09-06 18:57:30] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -82.506960+0.000463j
[2025-09-06 18:57:51] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -82.558427+0.001184j
[2025-09-06 18:58:12] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -82.450065-0.000837j
[2025-09-06 18:58:33] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -82.442744-0.001911j
[2025-09-06 18:58:54] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -82.456770+0.000521j
[2025-09-06 18:59:15] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -82.357299-0.004079j
[2025-09-06 18:59:36] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -82.526788-0.000406j
[2025-09-06 18:59:57] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -82.555149-0.005498j
[2025-09-06 19:00:18] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -82.511633-0.002969j
[2025-09-06 19:00:38] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -82.712092+0.000919j
[2025-09-06 19:00:54] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -82.710711+0.002834j
[2025-09-06 19:01:08] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -82.621973-0.000625j
[2025-09-06 19:01:24] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -82.533186+0.001553j
[2025-09-06 19:01:45] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -82.528261+0.002025j
[2025-09-06 19:02:05] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -82.578054-0.004072j
[2025-09-06 19:02:26] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -82.491028+0.000368j
[2025-09-06 19:02:47] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -82.426957+0.000054j
[2025-09-06 19:03:08] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -82.599088-0.004029j
[2025-09-06 19:03:29] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -82.523353+0.002743j
[2025-09-06 19:03:50] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -82.522099+0.004199j
[2025-09-06 19:04:11] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -82.428484-0.003163j
[2025-09-06 19:04:32] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -82.286612-0.001032j
[2025-09-06 19:04:53] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -82.366917+0.003938j
[2025-09-06 19:05:14] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -82.632495+0.002221j
[2025-09-06 19:05:35] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -82.675187-0.001220j
[2025-09-06 19:05:53] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -82.499488-0.000128j
[2025-09-06 19:06:12] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -82.471906-0.002247j
[2025-09-06 19:06:33] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -82.373676+0.002109j
[2025-09-06 19:06:53] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -82.480938+0.000124j
[2025-09-06 19:07:14] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -82.550813+0.005047j
[2025-09-06 19:07:35] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -82.537944+0.002469j
[2025-09-06 19:07:56] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -82.427650-0.004606j
[2025-09-06 19:08:17] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -82.385458-0.001289j
[2025-09-06 19:08:38] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -82.586199-0.001830j
[2025-09-06 19:08:58] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -82.722168+0.000849j
[2025-09-06 19:09:19] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -82.651820+0.001134j
[2025-09-06 19:09:40] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -82.698846+0.003285j
[2025-09-06 19:10:01] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -82.562729-0.004223j
[2025-09-06 19:10:22] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -82.457845-0.002513j
[2025-09-06 19:10:43] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -82.421127+0.000534j
[2025-09-06 19:11:04] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -82.525824-0.007942j
[2025-09-06 19:11:24] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -82.619122-0.000497j
[2025-09-06 19:11:45] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -82.564976-0.002706j
[2025-09-06 19:12:06] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -82.659481+0.000345j
[2025-09-06 19:12:27] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -82.583581-0.000975j
[2025-09-06 19:12:27] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-06 19:12:48] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -82.627263-0.002018j
[2025-09-06 19:13:09] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -82.589979-0.003706j
[2025-09-06 19:13:30] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -82.560034-0.003179j
[2025-09-06 19:13:50] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -82.529153+0.003048j
[2025-09-06 19:14:11] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -82.534663+0.001248j
[2025-09-06 19:14:32] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -82.580226-0.002849j
[2025-09-06 19:14:53] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -82.460530-0.001461j
[2025-09-06 19:15:14] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -82.525875-0.000593j
[2025-09-06 19:15:35] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -82.570112+0.002434j
[2025-09-06 19:15:55] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -82.459303+0.005146j
[2025-09-06 19:16:16] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -82.540045+0.001428j
[2025-09-06 19:16:37] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -82.379477-0.002421j
[2025-09-06 19:16:58] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -82.557382-0.000731j
[2025-09-06 19:17:19] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -82.549322-0.000884j
[2025-09-06 19:17:40] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -82.602052-0.002409j
[2025-09-06 19:18:00] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -82.702390-0.001115j
[2025-09-06 19:18:21] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -82.565409+0.016328j
[2025-09-06 19:18:42] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -82.502343-0.003876j
[2025-09-06 19:19:03] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -82.569385+0.002204j
[2025-09-06 19:19:24] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -82.743962+0.002751j
[2025-09-06 19:19:45] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -82.602696-0.001811j
[2025-09-06 19:20:05] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -82.628943-0.002933j
[2025-09-06 19:20:26] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -82.753670+0.000542j
[2025-09-06 19:20:45] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -82.710308-0.000383j
[2025-09-06 19:20:59] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -82.814655+0.001805j
[2025-09-06 19:21:14] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -82.549574+0.000921j
[2025-09-06 19:21:35] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -82.540820-0.001846j
[2025-09-06 19:21:55] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -82.556410+0.001499j
[2025-09-06 19:22:16] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -82.559574+0.000716j
[2025-09-06 19:22:37] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -82.544081-0.001792j
[2025-09-06 19:22:37] RESTART #2 | Period: 600
[2025-09-06 19:22:58] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -82.613072+0.001040j
[2025-09-06 19:23:19] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -82.517822+0.001837j
[2025-09-06 19:23:40] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -82.515226-0.000594j
[2025-09-06 19:24:01] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -82.516997-0.008029j
[2025-09-06 19:24:22] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -82.727139-0.001542j
[2025-09-06 19:24:43] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -82.710220-0.003921j
[2025-09-06 19:25:04] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -82.586481-0.000822j
[2025-09-06 19:25:25] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -82.647920-0.000530j
[2025-09-06 19:25:43] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -82.599424+0.001085j
[2025-09-06 19:26:02] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -82.581699-0.001781j
[2025-09-06 19:26:23] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -82.548486-0.002713j
[2025-09-06 19:26:43] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -82.518503+0.001601j
[2025-09-06 19:27:04] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -82.517492-0.001152j
[2025-09-06 19:27:25] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -82.610160+0.002194j
[2025-09-06 19:27:46] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -82.516851+0.001990j
[2025-09-06 19:28:07] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -82.456862+0.002065j
[2025-09-06 19:28:28] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -82.544640+0.005407j
[2025-09-06 19:28:48] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -82.620362-0.002290j
[2025-09-06 19:29:09] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -82.480342-0.002538j
[2025-09-06 19:29:30] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -82.504077-0.000067j
[2025-09-06 19:29:51] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -82.548105+0.002540j
[2025-09-06 19:30:12] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -82.491800+0.002264j
[2025-09-06 19:30:33] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -82.486600+0.000988j
[2025-09-06 19:30:53] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -82.469388-0.001495j
[2025-09-06 19:31:14] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -82.499777-0.000687j
[2025-09-06 19:31:35] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -82.467690-0.000808j
[2025-09-06 19:31:56] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -82.573832+0.000208j
[2025-09-06 19:32:17] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -82.582108-0.000803j
[2025-09-06 19:32:38] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -82.473298-0.001324j
[2025-09-06 19:32:59] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -82.495242-0.002578j
[2025-09-06 19:33:19] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -82.523194-0.004428j
[2025-09-06 19:33:40] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -82.726677-0.000428j
[2025-09-06 19:34:01] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -82.715942+0.002577j
[2025-09-06 19:34:22] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -82.622838-0.003019j
[2025-09-06 19:34:43] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -82.496038+0.000582j
[2025-09-06 19:35:04] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -82.536759-0.002618j
[2025-09-06 19:35:24] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -82.518595-0.003639j
[2025-09-06 19:35:45] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -82.481141+0.003447j
[2025-09-06 19:36:06] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -82.556189+0.001275j
[2025-09-06 19:36:27] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -82.746959+0.000205j
[2025-09-06 19:36:48] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -82.656829+0.000090j
[2025-09-06 19:37:09] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -82.687779+0.003606j
[2025-09-06 19:37:30] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -82.701559+0.001258j
[2025-09-06 19:37:50] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -82.774022+0.005420j
[2025-09-06 19:38:11] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -82.601331+0.003077j
[2025-09-06 19:38:32] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -82.624426-0.000792j
[2025-09-06 19:38:53] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -82.617266-0.003698j
[2025-09-06 19:39:14] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -82.471304-0.001764j
[2025-09-06 19:39:35] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -82.449174-0.002267j
[2025-09-06 19:39:55] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -82.555606+0.003595j
[2025-09-06 19:40:16] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -82.566504+0.004810j
[2025-09-06 19:40:37] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -82.545029+0.002026j
[2025-09-06 19:40:51] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -82.488676+0.000102j
[2025-09-06 19:41:05] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -82.474097+0.001586j
[2025-09-06 19:41:25] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -82.474417+0.004312j
[2025-09-06 19:41:46] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -82.591086-0.000266j
[2025-09-06 19:42:07] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -82.435681+0.002547j
[2025-09-06 19:42:28] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -82.502149+0.003052j
[2025-09-06 19:42:49] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -82.614337-0.002673j
[2025-09-06 19:43:10] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -82.458567+0.000981j
[2025-09-06 19:43:31] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -82.526648-0.001527j
[2025-09-06 19:43:52] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -82.462872+0.002745j
[2025-09-06 19:44:12] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -82.525321+0.000117j
[2025-09-06 19:44:33] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -82.452708+0.003064j
[2025-09-06 19:44:54] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -82.464626+0.001184j
[2025-09-06 19:45:15] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -82.501395-0.000140j
[2025-09-06 19:45:34] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -82.485385+0.002576j
[2025-09-06 19:45:52] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -82.466256-0.000926j
[2025-09-06 19:46:13] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -82.592351-0.000874j
[2025-09-06 19:46:34] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -82.609108+0.002758j
[2025-09-06 19:46:55] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -82.516520-0.003437j
[2025-09-06 19:47:16] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -82.480723-0.003339j
[2025-09-06 19:47:37] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -82.525653+0.003415j
[2025-09-06 19:47:57] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -82.567930+0.000320j
[2025-09-06 19:48:18] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -82.503659-0.003689j
[2025-09-06 19:48:18] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-06 19:48:39] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -82.473176+0.004412j
[2025-09-06 19:49:00] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -82.435931+0.006455j
[2025-09-06 19:49:21] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -82.483819+0.003427j
[2025-09-06 19:49:42] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -82.477012+0.000904j
[2025-09-06 19:50:02] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -82.569392+0.000023j
[2025-09-06 19:50:23] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -82.409510+0.000774j
[2025-09-06 19:50:44] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -82.527657+0.001356j
[2025-09-06 19:51:05] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -82.588013+0.001106j
[2025-09-06 19:51:26] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -82.630936+0.002423j
[2025-09-06 19:51:47] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -82.744475+0.006680j
[2025-09-06 19:52:07] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -82.623795+0.002916j
[2025-09-06 19:52:28] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -82.672635+0.000540j
[2025-09-06 19:52:49] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -82.791937-0.000561j
[2025-09-06 19:53:10] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -82.624229+0.000343j
[2025-09-06 19:53:31] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -82.595393-0.002202j
[2025-09-06 19:53:52] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -82.561604+0.001141j
[2025-09-06 19:54:13] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -82.532888+0.000825j
[2025-09-06 19:54:33] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -82.668073+0.002326j
[2025-09-06 19:54:54] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -82.650472-0.001372j
[2025-09-06 19:55:15] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -82.668357+0.000373j
[2025-09-06 19:55:36] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -82.831174+0.000455j
[2025-09-06 19:55:57] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -82.871656+0.000006j
[2025-09-06 19:56:18] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -82.663503+0.005245j
[2025-09-06 19:56:38] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -82.769304+0.000194j
[2025-09-06 19:56:59] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -82.719220+0.004365j
[2025-09-06 19:57:20] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -82.551227+0.005630j
[2025-09-06 19:57:41] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -82.619990+0.002287j
[2025-09-06 19:58:02] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -82.608427+0.000242j
[2025-09-06 19:58:23] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -82.605232-0.001502j
[2025-09-06 19:58:44] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -82.498607+0.002084j
[2025-09-06 19:59:04] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -82.736965+0.002363j
[2025-09-06 19:59:25] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -82.672414+0.000778j
[2025-09-06 19:59:46] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -82.662332+0.005845j
[2025-09-06 20:00:07] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -82.704755+0.000678j
[2025-09-06 20:00:28] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -82.630595+0.000290j
[2025-09-06 20:00:43] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -82.528332+0.000145j
[2025-09-06 20:00:57] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -82.565384-0.001866j
[2025-09-06 20:01:14] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -82.564972-0.003780j
[2025-09-06 20:01:34] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -82.571759-0.002358j
[2025-09-06 20:01:55] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -82.619822-0.001962j
[2025-09-06 20:02:16] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -82.551689+0.000910j
[2025-09-06 20:02:37] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -82.532741+0.000480j
[2025-09-06 20:02:58] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -82.445799+0.000678j
[2025-09-06 20:03:19] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -82.537224-0.005172j
[2025-09-06 20:03:40] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -82.639882+0.002787j
[2025-09-06 20:04:01] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -82.577962+0.000069j
[2025-09-06 20:04:22] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -82.549576-0.005047j
[2025-09-06 20:04:43] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -82.545181+0.002558j
[2025-09-06 20:05:04] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -82.596339+0.007730j
[2025-09-06 20:05:25] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -82.651786+0.002885j
[2025-09-06 20:05:43] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -82.568014-0.002901j
[2025-09-06 20:06:02] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -82.516026+0.002539j
[2025-09-06 20:06:23] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -82.611899+0.001083j
[2025-09-06 20:06:44] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -82.504824+0.003768j
[2025-09-06 20:07:04] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -82.329271-0.002599j
[2025-09-06 20:07:25] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -82.482949-0.005163j
[2025-09-06 20:07:46] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -82.541850-0.000775j
[2025-09-06 20:08:07] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -82.508889+0.003102j
[2025-09-06 20:08:28] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -82.688610+0.004358j
[2025-09-06 20:08:49] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -82.544022+0.001574j
[2025-09-06 20:09:10] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -82.592799+0.001610j
[2025-09-06 20:09:31] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -82.645066+0.002751j
[2025-09-06 20:09:52] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -82.546607+0.002330j
[2025-09-06 20:10:13] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -82.473945-0.002091j
[2025-09-06 20:10:34] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -82.544060-0.010394j
[2025-09-06 20:10:55] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -82.596946+0.002990j
[2025-09-06 20:11:15] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -82.610527+0.003135j
[2025-09-06 20:11:36] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -82.593730+0.002953j
[2025-09-06 20:11:57] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -82.737880+0.001041j
[2025-09-06 20:12:18] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -82.654478+0.001190j
[2025-09-06 20:12:39] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -82.665481+0.000294j
[2025-09-06 20:13:00] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -82.683585-0.002729j
[2025-09-06 20:13:21] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -82.445525+0.000288j
[2025-09-06 20:13:42] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -82.564835-0.001728j
[2025-09-06 20:14:03] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -82.614552-0.002007j
[2025-09-06 20:14:24] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -82.576244+0.000308j
[2025-09-06 20:14:45] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -82.617526-0.006230j
[2025-09-06 20:15:06] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -82.632743-0.001517j
[2025-09-06 20:15:27] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -82.507560+0.000956j
[2025-09-06 20:15:47] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -82.639519+0.009580j
[2025-09-06 20:16:08] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -82.634933-0.000950j
[2025-09-06 20:16:29] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -82.560690+0.000915j
[2025-09-06 20:16:50] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -82.685262-0.000595j
[2025-09-06 20:17:11] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -82.761344-0.000022j
[2025-09-06 20:17:32] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -82.867864+0.007699j
[2025-09-06 20:17:53] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -82.638145-0.001397j
[2025-09-06 20:18:14] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -82.702829-0.002479j
[2025-09-06 20:18:35] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -82.608144-0.001835j
[2025-09-06 20:18:56] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -82.654106-0.002614j
[2025-09-06 20:19:17] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -82.545305-0.001392j
[2025-09-06 20:19:37] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -82.637857-0.003165j
[2025-09-06 20:19:58] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -82.646785+0.001713j
[2025-09-06 20:20:19] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -82.644982-0.001235j
[2025-09-06 20:20:38] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -82.669749-0.004408j
[2025-09-06 20:20:52] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -82.416472-0.000804j
[2025-09-06 20:21:06] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -82.572610-0.003077j
[2025-09-06 20:21:27] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -82.621779+0.003069j
[2025-09-06 20:21:48] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -82.537532+0.001583j
[2025-09-06 20:22:09] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -82.528537+0.001010j
[2025-09-06 20:22:30] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -82.504352+0.001785j
[2025-09-06 20:22:51] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -82.606115-0.005634j
[2025-09-06 20:23:12] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -82.558905+0.001264j
[2025-09-06 20:23:32] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -82.441171+0.001129j
[2025-09-06 20:23:53] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -82.518598+0.001103j
[2025-09-06 20:24:14] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -82.485164-0.000373j
[2025-09-06 20:24:14] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-06 20:24:35] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -82.571924+0.005024j
[2025-09-06 20:24:56] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -82.614521+0.001000j
[2025-09-06 20:25:17] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -82.508734-0.005524j
[2025-09-06 20:25:36] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -82.554415+0.000825j
[2025-09-06 20:25:54] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -82.504958+0.002066j
[2025-09-06 20:26:15] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -82.617746-0.002045j
[2025-09-06 20:26:36] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -82.663292+0.001353j
[2025-09-06 20:26:57] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -82.596730-0.000939j
[2025-09-06 20:27:18] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -82.611107-0.003128j
[2025-09-06 20:27:39] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -82.503730+0.001301j
[2025-09-06 20:28:00] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -82.491864+0.006450j
[2025-09-06 20:28:21] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -82.626730-0.004975j
[2025-09-06 20:28:42] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -82.523880-0.000222j
[2025-09-06 20:29:03] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -82.578265+0.007034j
[2025-09-06 20:29:24] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -82.546639-0.003346j
[2025-09-06 20:29:45] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -82.549968+0.002556j
[2025-09-06 20:30:05] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -82.686793+0.001922j
[2025-09-06 20:30:26] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -82.561654-0.003060j
[2025-09-06 20:30:47] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -82.532622-0.003568j
[2025-09-06 20:31:08] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -82.626591+0.001117j
[2025-09-06 20:31:29] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -82.581121+0.002287j
[2025-09-06 20:31:50] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -82.374458+0.003002j
[2025-09-06 20:32:11] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -82.592048+0.005153j
[2025-09-06 20:32:32] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -82.422637+0.003543j
[2025-09-06 20:32:53] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -82.456989+0.001138j
[2025-09-06 20:33:14] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -82.450400+0.003665j
[2025-09-06 20:33:35] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -82.548265-0.001819j
[2025-09-06 20:33:56] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -82.548077-0.001146j
[2025-09-06 20:34:16] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -82.509829-0.002358j
[2025-09-06 20:34:37] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -82.676724+0.000383j
[2025-09-06 20:34:58] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -82.585592-0.010374j
[2025-09-06 20:35:19] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -82.547351-0.001765j
[2025-09-06 20:35:40] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -82.477878+0.003121j
[2025-09-06 20:36:01] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -82.556264+0.003279j
[2025-09-06 20:36:22] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -82.447033-0.002826j
[2025-09-06 20:36:43] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -82.658276+0.000587j
[2025-09-06 20:37:04] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -82.720785+0.001538j
[2025-09-06 20:37:25] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -82.687570+0.003264j
[2025-09-06 20:37:46] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -82.570779-0.000499j
[2025-09-06 20:38:07] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -82.706960-0.000785j
[2025-09-06 20:38:27] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -82.791106-0.002209j
[2025-09-06 20:38:48] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -82.643124+0.000829j
[2025-09-06 20:39:09] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -82.695697+0.000719j
[2025-09-06 20:39:30] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -82.769079-0.001494j
[2025-09-06 20:39:51] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -82.743460-0.003388j
[2025-09-06 20:40:12] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -82.640570-0.001959j
[2025-09-06 20:40:32] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -82.556402-0.003974j
[2025-09-06 20:40:46] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -82.652849+0.005106j
[2025-09-06 20:41:01] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -82.679116-0.000451j
[2025-09-06 20:41:20] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -82.658293+0.002798j
[2025-09-06 20:41:41] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -82.656634+0.000489j
[2025-09-06 20:42:01] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -82.569873-0.002100j
[2025-09-06 20:42:22] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -82.617338-0.000267j
[2025-09-06 20:42:43] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -82.722215+0.001253j
[2025-09-06 20:43:04] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -82.862659-0.008894j
[2025-09-06 20:43:25] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -82.733886-0.000443j
[2025-09-06 20:43:46] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -82.631974-0.005259j
[2025-09-06 20:44:07] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -82.554919-0.000755j
[2025-09-06 20:44:28] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -82.602399+0.001940j
[2025-09-06 20:44:49] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -82.416299+0.004214j
[2025-09-06 20:45:10] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -82.489992+0.004418j
[2025-09-06 20:45:29] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -82.443477-0.001088j
[2025-09-06 20:45:47] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -82.580877-0.003064j
[2025-09-06 20:46:08] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -82.625461+0.001960j
[2025-09-06 20:46:29] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -82.650271-0.001058j
[2025-09-06 20:46:50] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -82.872224-0.001715j
[2025-09-06 20:47:11] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -82.637511+0.000068j
[2025-09-06 20:47:32] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -82.584983+0.000347j
[2025-09-06 20:47:52] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -82.536281-0.003622j
[2025-09-06 20:48:13] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -82.569942+0.002248j
[2025-09-06 20:48:34] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -82.567541+0.007833j
[2025-09-06 20:48:55] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -82.605649-0.005350j
[2025-09-06 20:49:16] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -82.576625-0.001018j
[2025-09-06 20:49:37] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -82.682325+0.001684j
[2025-09-06 20:49:58] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -82.589940+0.001098j
[2025-09-06 20:50:19] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -82.611160-0.000352j
[2025-09-06 20:50:40] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -82.526813+0.004265j
[2025-09-06 20:51:01] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -82.444608-0.002385j
[2025-09-06 20:51:22] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -82.564617-0.001060j
[2025-09-06 20:51:43] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -82.512891-0.000307j
[2025-09-06 20:52:04] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -82.588014-0.003117j
[2025-09-06 20:52:24] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -82.547994+0.001502j
[2025-09-06 20:52:45] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -82.368785+0.005080j
[2025-09-06 20:53:06] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -82.523109+0.003522j
[2025-09-06 20:53:27] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -82.442765+0.004274j
[2025-09-06 20:53:48] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -82.646189+0.002779j
[2025-09-06 20:54:09] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -82.666879+0.004192j
[2025-09-06 20:54:30] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -82.555015+0.000083j
[2025-09-06 20:54:51] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -82.573309-0.000084j
[2025-09-06 20:55:12] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -82.539276+0.001776j
[2025-09-06 20:55:33] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -82.581000+0.001840j
[2025-09-06 20:55:54] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -82.596329+0.000738j
[2025-09-06 20:56:14] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -82.658276+0.000736j
[2025-09-06 20:56:35] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -82.697838+0.002400j
[2025-09-06 20:56:56] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -82.563752+0.000012j
[2025-09-06 20:57:17] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -82.557662+0.001419j
[2025-09-06 20:57:38] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -82.475545-0.002127j
[2025-09-06 20:57:59] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -82.508251-0.000818j
[2025-09-06 20:58:20] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -82.326255-0.002901j
[2025-09-06 20:58:41] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -82.502239-0.002849j
[2025-09-06 20:59:02] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -82.439914-0.001732j
[2025-09-06 20:59:23] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -82.510927-0.001692j
[2025-09-06 20:59:44] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -82.521977+0.001228j
[2025-09-06 21:00:04] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -82.394010+0.004035j
[2025-09-06 21:00:25] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -82.583628-0.002128j
[2025-09-06 21:00:25] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-06 21:00:41] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -82.528792-0.002652j
[2025-09-06 21:00:55] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -82.708981+0.000701j
[2025-09-06 21:01:12] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -82.551507-0.000983j
[2025-09-06 21:01:33] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -82.506240+0.003350j
[2025-09-06 21:01:54] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -82.483262-0.000272j
[2025-09-06 21:02:15] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -82.438789+0.003088j
[2025-09-06 21:02:36] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -82.423343-0.007207j
[2025-09-06 21:02:57] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -82.435271+0.002796j
[2025-09-06 21:03:17] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -82.516757+0.002556j
[2025-09-06 21:03:38] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -82.473258+0.001612j
[2025-09-06 21:03:59] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -82.686661+0.001158j
[2025-09-06 21:04:20] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -82.593144-0.001408j
[2025-09-06 21:04:41] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -82.639461-0.003315j
[2025-09-06 21:05:02] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -82.476209-0.002721j
[2025-09-06 21:05:23] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -82.442145-0.001930j
[2025-09-06 21:05:40] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -82.441872-0.001743j
[2025-09-06 21:06:00] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -82.481463-0.000818j
[2025-09-06 21:06:21] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -82.513969-0.000716j
[2025-09-06 21:06:42] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -82.511360+0.003968j
[2025-09-06 21:07:03] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -82.427042+0.000012j
[2025-09-06 21:07:24] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -82.279427+0.002032j
[2025-09-06 21:07:45] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -82.465123-0.000625j
[2025-09-06 21:08:06] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -82.473672-0.002297j
[2025-09-06 21:08:27] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -82.463951+0.001545j
[2025-09-06 21:08:48] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -82.576061-0.000289j
[2025-09-06 21:09:08] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -82.476888-0.000461j
[2025-09-06 21:09:29] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -82.473833+0.000419j
[2025-09-06 21:09:50] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -82.407369+0.000057j
[2025-09-06 21:10:11] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -82.526416+0.002539j
[2025-09-06 21:10:32] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -82.517842+0.001059j
[2025-09-06 21:10:53] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -82.479036-0.000724j
[2025-09-06 21:11:14] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -82.538295+0.003400j
[2025-09-06 21:11:35] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -82.582656+0.000004j
[2025-09-06 21:11:56] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -82.491482+0.003882j
[2025-09-06 21:12:17] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -82.405989+0.000844j
[2025-09-06 21:12:37] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -82.518576-0.000135j
[2025-09-06 21:12:58] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -82.398790+0.000417j
[2025-09-06 21:13:19] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -82.371373+0.002164j
[2025-09-06 21:13:40] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -82.389340+0.002305j
[2025-09-06 21:14:01] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -82.549308+0.001031j
[2025-09-06 21:14:22] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -82.488255-0.002982j
[2025-09-06 21:14:43] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -82.592722+0.000901j
[2025-09-06 21:15:04] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -82.644987-0.000377j
[2025-09-06 21:15:25] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -82.552305-0.003467j
[2025-09-06 21:15:46] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -82.478540-0.002069j
[2025-09-06 21:16:07] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -82.550819-0.002272j
[2025-09-06 21:16:27] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -82.468957-0.002489j
[2025-09-06 21:16:48] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -82.590827-0.002798j
[2025-09-06 21:17:09] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -82.728200+0.002132j
[2025-09-06 21:17:30] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -82.647013+0.001950j
[2025-09-06 21:17:51] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -82.579976+0.003281j
[2025-09-06 21:18:12] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -82.610988+0.002852j
[2025-09-06 21:18:33] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -82.600085+0.000186j
[2025-09-06 21:18:54] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -82.475442-0.001895j
[2025-09-06 21:19:15] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -82.468574+0.001352j
[2025-09-06 21:19:36] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -82.521895-0.000999j
[2025-09-06 21:19:57] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -82.626662+0.000750j
[2025-09-06 21:20:18] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -82.632021+0.001624j
[2025-09-06 21:20:35] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -82.631018-0.000916j
[2025-09-06 21:20:49] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -82.767868-0.002576j
[2025-09-06 21:21:03] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -82.688729+0.002729j
[2025-09-06 21:21:23] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -82.524171-0.000274j
[2025-09-06 21:21:44] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -82.655100+0.003484j
[2025-09-06 21:22:05] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -82.624886+0.004247j
[2025-09-06 21:22:26] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -82.726861+0.006287j
[2025-09-06 21:22:47] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -82.577661-0.001316j
[2025-09-06 21:23:08] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -82.595558-0.005807j
[2025-09-06 21:23:29] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -82.791831-0.000596j
[2025-09-06 21:23:49] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -82.652421+0.001811j
[2025-09-06 21:24:10] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -82.585617+0.000775j
[2025-09-06 21:24:31] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -82.500035+0.004664j
[2025-09-06 21:24:52] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -82.383627+0.004596j
[2025-09-06 21:25:13] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -82.318078-0.000136j
[2025-09-06 21:25:32] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -82.599800+0.010084j
[2025-09-06 21:25:50] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -82.645950+0.001394j
[2025-09-06 21:26:11] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -82.719176+0.001833j
[2025-09-06 21:26:32] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -82.540933-0.000556j
[2025-09-06 21:26:53] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -82.631814+0.002095j
[2025-09-06 21:27:14] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -82.527523-0.002702j
[2025-09-06 21:27:35] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -82.707557+0.006425j
[2025-09-06 21:27:56] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -82.691384+0.001603j
[2025-09-06 21:28:17] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -82.545345-0.000869j
[2025-09-06 21:28:38] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -82.501234-0.000973j
[2025-09-06 21:28:59] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -82.454023+0.000311j
[2025-09-06 21:29:20] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -82.503311+0.001755j
[2025-09-06 21:29:40] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -82.613649-0.003796j
[2025-09-06 21:30:01] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -82.710925+0.000134j
[2025-09-06 21:30:22] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -82.676979-0.001841j
[2025-09-06 21:30:43] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -82.890645-0.000395j
[2025-09-06 21:31:04] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -82.859008-0.002085j
[2025-09-06 21:31:25] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -82.665483+0.008199j
[2025-09-06 21:31:46] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -82.694155-0.000883j
[2025-09-06 21:32:07] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -82.635684+0.003426j
[2025-09-06 21:32:28] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -82.753987-0.003038j
[2025-09-06 21:32:49] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -82.725150+0.000154j
[2025-09-06 21:33:10] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -82.765795-0.002412j
[2025-09-06 21:33:30] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -82.587848+0.001502j
[2025-09-06 21:33:51] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -82.645037-0.002991j
[2025-09-06 21:34:12] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -82.676667+0.004972j
[2025-09-06 21:34:33] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -82.510630+0.001352j
[2025-09-06 21:34:54] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -82.503526+0.005300j
[2025-09-06 21:35:15] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -82.620380-0.002844j
[2025-09-06 21:35:36] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -82.567599+0.001292j
[2025-09-06 21:35:57] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -82.616942-0.002242j
[2025-09-06 21:36:18] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -82.579685+0.000449j
[2025-09-06 21:36:18] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-06 21:36:39] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -82.636394+0.000395j
[2025-09-06 21:37:00] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -82.462622+0.002582j
[2025-09-06 21:37:21] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -82.550486-0.000927j
[2025-09-06 21:37:42] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -82.430382-0.001445j
[2025-09-06 21:38:03] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -82.470571-0.003116j
[2025-09-06 21:38:23] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -82.363790+0.005407j
[2025-09-06 21:38:44] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -82.426346-0.003025j
[2025-09-06 21:39:05] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -82.596868-0.000960j
[2025-09-06 21:39:26] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -82.460918+0.001369j
[2025-09-06 21:39:47] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -82.445844-0.000462j
[2025-09-06 21:40:08] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -82.414467-0.001070j
[2025-09-06 21:40:29] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -82.338250+0.002044j
[2025-09-06 21:40:44] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -82.471857-0.001952j
[2025-09-06 21:40:58] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -82.521487+0.003268j
[2025-09-06 21:41:16] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -82.681948+0.001113j
[2025-09-06 21:41:37] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -82.618636-0.002179j
[2025-09-06 21:41:58] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -82.474891-0.002841j
[2025-09-06 21:42:19] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -82.566229-0.000852j
[2025-09-06 21:42:39] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -82.560928+0.001082j
[2025-09-06 21:43:00] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -82.657502-0.002018j
[2025-09-06 21:43:21] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -82.733671-0.003252j
[2025-09-06 21:43:42] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -82.578709-0.000527j
[2025-09-06 21:44:03] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -82.533480-0.001141j
[2025-09-06 21:44:24] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -82.604250-0.002695j
[2025-09-06 21:44:45] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -82.791993+0.002622j
[2025-09-06 21:45:06] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -82.697645-0.003632j
[2025-09-06 21:45:27] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -82.793326+0.001070j
[2025-09-06 21:45:43] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -82.703858+0.001427j
[2025-09-06 21:46:04] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -82.635839+0.003774j
[2025-09-06 21:46:25] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -82.565926+0.002971j
[2025-09-06 21:46:46] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -82.607183+0.042273j
[2025-09-06 21:47:07] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -82.542115+0.001431j
[2025-09-06 21:47:28] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -82.661273-0.001629j
[2025-09-06 21:47:48] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -82.607393-0.000867j
[2025-09-06 21:48:09] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -82.597955-0.004861j
[2025-09-06 21:48:30] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -82.604073-0.001233j
[2025-09-06 21:48:51] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -82.565257-0.001036j
[2025-09-06 21:49:12] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -82.475050+0.001145j
[2025-09-06 21:49:33] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -82.587217+0.005024j
[2025-09-06 21:49:54] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -82.569202-0.001342j
[2025-09-06 21:50:15] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -82.581407+0.000478j
[2025-09-06 21:50:36] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -82.743374-0.002263j
[2025-09-06 21:50:57] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -82.540162+0.000995j
[2025-09-06 21:51:17] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -82.640332+0.003653j
[2025-09-06 21:51:38] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -82.651167-0.001360j
[2025-09-06 21:51:59] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -82.752065+0.000316j
[2025-09-06 21:52:20] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -82.674181-0.000652j
[2025-09-06 21:52:41] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -82.706203+0.000767j
[2025-09-06 21:53:02] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -82.707058+0.000656j
[2025-09-06 21:53:23] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -82.716592-0.006013j
[2025-09-06 21:53:44] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -82.643777-0.002756j
[2025-09-06 21:54:05] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -82.521921+0.002431j
[2025-09-06 21:54:26] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -82.557540-0.001678j
[2025-09-06 21:54:47] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -82.608737-0.001214j
[2025-09-06 21:55:07] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -82.670274-0.000125j
[2025-09-06 21:55:28] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -82.667619+0.001104j
[2025-09-06 21:55:49] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -82.738968-0.000256j
[2025-09-06 21:56:10] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -82.851914-0.000217j
[2025-09-06 21:56:31] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -82.681507-0.000533j
[2025-09-06 21:56:52] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -82.564481+0.001241j
[2025-09-06 21:57:13] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -82.568702-0.001282j
[2025-09-06 21:57:34] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -82.573199-0.002706j
[2025-09-06 21:57:55] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -82.607484+0.001681j
[2025-09-06 21:58:16] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -82.506745+0.001168j
[2025-09-06 21:58:36] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -82.605778-0.001003j
[2025-09-06 21:58:57] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -82.471962-0.002382j
[2025-09-06 21:59:18] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -82.583757+0.000295j
[2025-09-06 21:59:39] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -82.713954-0.002988j
[2025-09-06 22:00:00] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -82.783183+0.001148j
[2025-09-06 22:00:21] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -82.627011-0.002171j
[2025-09-06 22:00:38] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -82.669833+0.000747j
[2025-09-06 22:00:52] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -82.712269+0.003239j
[2025-09-06 22:01:08] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -82.795646-0.000393j
[2025-09-06 22:01:29] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -82.707444-0.000947j
[2025-09-06 22:01:50] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -82.641888+0.005161j
[2025-09-06 22:02:11] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -82.494249+0.000707j
[2025-09-06 22:02:32] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -82.568891-0.003917j
[2025-09-06 22:02:52] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -82.598903-0.001042j
[2025-09-06 22:03:13] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -82.670583+0.001020j
[2025-09-06 22:03:34] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -82.627799-0.002414j
[2025-09-06 22:03:55] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -82.787733-0.002758j
[2025-09-06 22:04:16] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -82.780325+0.000595j
[2025-09-06 22:04:37] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -82.805090-0.000429j
[2025-09-06 22:04:58] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -82.521184-0.004260j
[2025-09-06 22:05:19] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -82.516282+0.000157j
[2025-09-06 22:05:37] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -82.565196+0.004388j
[2025-09-06 22:05:56] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -82.488853+0.003360j
[2025-09-06 22:06:17] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -82.559818+0.000892j
[2025-09-06 22:06:38] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -82.610798+0.004092j
[2025-09-06 22:06:59] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -82.625248+0.001565j
[2025-09-06 22:07:20] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -82.647038-0.001826j
[2025-09-06 22:07:41] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -82.499112-0.000002j
[2025-09-06 22:08:02] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -82.496083-0.001229j
[2025-09-06 22:08:22] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -82.701119-0.002159j
[2025-09-06 22:08:43] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -82.672267+0.000725j
[2025-09-06 22:09:04] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -82.523073-0.000360j
[2025-09-06 22:09:25] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -82.385089-0.000183j
[2025-09-06 22:09:46] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -82.617720+0.002693j
[2025-09-06 22:10:07] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -82.450588-0.001884j
[2025-09-06 22:10:28] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -82.626279-0.001485j
[2025-09-06 22:10:49] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -82.418294-0.000468j
[2025-09-06 22:11:10] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -82.368553+0.002334j
[2025-09-06 22:11:31] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -82.467974-0.000628j
[2025-09-06 22:11:52] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -82.316605+0.002391j
[2025-09-06 22:12:12] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -82.589009-0.000105j
[2025-09-06 22:12:13] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-06 22:12:33] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -82.529039+0.002286j
[2025-09-06 22:12:54] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -82.636366-0.001510j
[2025-09-06 22:13:15] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -82.542480+0.001069j
[2025-09-06 22:13:36] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -82.639288-0.000698j
[2025-09-06 22:13:57] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -82.618449+0.003364j
[2025-09-06 22:14:18] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -82.578725-0.000537j
[2025-09-06 22:14:39] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -82.529568-0.005310j
[2025-09-06 22:15:00] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -82.575672+0.004476j
[2025-09-06 22:15:21] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -82.445733+0.002754j
[2025-09-06 22:15:42] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -82.707399+0.005429j
[2025-09-06 22:16:03] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -82.610078+0.000760j
[2025-09-06 22:16:23] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -82.719432+0.002648j
[2025-09-06 22:16:44] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -82.642049-0.001954j
[2025-09-06 22:17:05] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -82.659059-0.002392j
[2025-09-06 22:17:26] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -82.618892+0.002487j
[2025-09-06 22:17:47] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -82.639911-0.004539j
[2025-09-06 22:18:08] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -82.532160+0.000868j
[2025-09-06 22:18:29] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -82.480313-0.004502j
[2025-09-06 22:18:50] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -82.600411+0.000068j
[2025-09-06 22:19:11] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -82.510523-0.000627j
[2025-09-06 22:19:32] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -82.593390+0.000873j
[2025-09-06 22:19:53] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -82.427130-0.002300j
[2025-09-06 22:20:14] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -82.456611-0.000656j
[2025-09-06 22:20:33] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -82.510088+0.002510j
[2025-09-06 22:20:47] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -82.367937-0.000567j
[2025-09-06 22:21:01] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -82.366166+0.000467j
[2025-09-06 22:21:21] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -82.310405-0.001286j
[2025-09-06 22:21:42] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -82.505742+0.002999j
[2025-09-06 22:22:03] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -82.632547+0.000387j
[2025-09-06 22:22:24] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -82.560977+0.002505j
[2025-09-06 22:22:45] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -82.656294-0.002820j
[2025-09-06 22:23:06] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -82.581739-0.003907j
[2025-09-06 22:23:27] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -82.490731-0.001647j
[2025-09-06 22:23:48] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -82.451594-0.000170j
[2025-09-06 22:24:09] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -82.577870-0.001310j
[2025-09-06 22:24:30] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -82.603331+0.000776j
[2025-09-06 22:24:50] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -82.538338+0.001027j
[2025-09-06 22:25:11] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -82.546077-0.000622j
[2025-09-06 22:25:30] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -82.444625-0.001028j
[2025-09-06 22:25:49] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -82.579474-0.000165j
[2025-09-06 22:26:10] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -82.571847-0.006865j
[2025-09-06 22:26:31] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -82.639062-0.000200j
[2025-09-06 22:26:51] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -82.555194+0.002069j
[2025-09-06 22:27:12] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -82.456489+0.002223j
[2025-09-06 22:27:33] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -82.569753+0.000157j
[2025-09-06 22:27:54] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -82.563414+0.002936j
[2025-09-06 22:28:15] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -82.572381-0.000509j
[2025-09-06 22:28:36] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -82.570410+0.001439j
[2025-09-06 22:28:57] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -82.643743+0.004845j
[2025-09-06 22:29:18] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -82.629503+0.000242j
[2025-09-06 22:29:39] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -82.579288-0.000477j
[2025-09-06 22:30:00] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -82.517473+0.002843j
[2025-09-06 22:30:21] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -82.545370+0.000452j
[2025-09-06 22:30:42] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -82.725433-0.001318j
[2025-09-06 22:31:02] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -82.866695+0.000025j
[2025-09-06 22:31:23] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -82.866687+0.003452j
[2025-09-06 22:31:44] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -82.816505+0.000208j
[2025-09-06 22:32:05] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -82.737356-0.002452j
[2025-09-06 22:32:26] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -82.848381+0.002595j
[2025-09-06 22:32:47] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -82.853365-0.001275j
[2025-09-06 22:33:08] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -82.852325-0.001401j
[2025-09-06 22:33:29] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -82.816314-0.003322j
[2025-09-06 22:33:50] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -82.696366-0.002692j
[2025-09-06 22:34:11] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -82.761495-0.005314j
[2025-09-06 22:34:32] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -82.678228+0.005041j
[2025-09-06 22:34:52] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -82.772128-0.002732j
[2025-09-06 22:35:13] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -82.711884+0.003050j
[2025-09-06 22:35:34] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -82.706565-0.003579j
[2025-09-06 22:35:55] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -82.735323-0.001990j
[2025-09-06 22:36:16] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -82.751709+0.004015j
[2025-09-06 22:36:37] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -82.693114+0.000110j
[2025-09-06 22:36:58] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -82.812347-0.000758j
[2025-09-06 22:37:19] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -82.736008+0.000886j
[2025-09-06 22:37:40] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -82.709910+0.001369j
[2025-09-06 22:38:01] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -82.680211-0.002482j
[2025-09-06 22:38:21] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -82.502353+0.003882j
[2025-09-06 22:38:42] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -82.346365+0.003558j
[2025-09-06 22:39:03] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -82.397323-0.003867j
[2025-09-06 22:39:24] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -82.339119+0.001115j
[2025-09-06 22:39:45] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -82.405333-0.001801j
[2025-09-06 22:40:06] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -82.334333+0.002976j
[2025-09-06 22:40:27] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -82.435788+0.000881j
[2025-09-06 22:40:41] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -82.358095-0.000788j
[2025-09-06 22:40:55] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -82.546718-0.002647j
[2025-09-06 22:41:15] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -82.651202-0.000560j
[2025-09-06 22:41:36] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -82.608096-0.002988j
[2025-09-06 22:41:57] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -82.514243-0.001037j
[2025-09-06 22:42:18] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -82.537077-0.001676j
[2025-09-06 22:42:39] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -82.460226-0.004685j
[2025-09-06 22:43:00] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -82.460373+0.002274j
[2025-09-06 22:43:20] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -82.435553-0.002524j
[2025-09-06 22:43:41] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -82.409914+0.000794j
[2025-09-06 22:44:02] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -82.649090-0.000663j
[2025-09-06 22:44:23] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -82.583425+0.001880j
[2025-09-06 22:44:44] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -82.660888-0.003950j
[2025-09-06 22:45:05] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -82.507511-0.000522j
[2025-09-06 22:45:24] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -82.493345+0.002431j
[2025-09-06 22:45:42] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -82.533509-0.002623j
[2025-09-06 22:46:03] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -82.628820-0.005464j
[2025-09-06 22:46:24] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -82.563116+0.002747j
[2025-09-06 22:46:45] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -82.532290+0.000623j
[2025-09-06 22:47:05] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -82.607321+0.001508j
[2025-09-06 22:47:26] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -82.464732-0.000563j
[2025-09-06 22:47:47] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -82.351610-0.001056j
[2025-09-06 22:48:08] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -82.423981+0.000623j
[2025-09-06 22:48:08] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-06 22:48:08] ✅ Training completed | Restarts: 2
[2025-09-06 22:48:08] ============================================================
[2025-09-06 22:48:08] Training completed | Runtime: 21623.0s
[2025-09-06 22:48:16] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-06 22:48:16] ============================================================
