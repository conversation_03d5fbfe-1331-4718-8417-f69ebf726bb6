[2025-09-07 00:59:53] 使用checkpoint文件: results/L=5/J2=0.00/J1=0.00/training/checkpoints/checkpoint_iter_000735.pkl
[2025-09-07 01:00:04] ✓ 从checkpoint加载参数: 735
[2025-09-07 01:00:04]   - 能量: -82.583628-0.002128j ± 0.109788
[2025-09-07 01:00:04] ================================================================================
[2025-09-07 01:00:04] 加载量子态: L=5, J2=0.00, J1=0.00, checkpoint=checkpoint_iter_000735
[2025-09-07 01:00:04] 使用采样数目: 1048576
[2025-09-07 01:00:04] 设置样本数为: 1048576
[2025-09-07 01:00:04] 开始生成共享样本集...
[2025-09-07 01:03:01] 样本生成完成,耗时: 176.487 秒
[2025-09-07 01:03:01] ================================================================================
[2025-09-07 01:03:01] 开始计算自旋结构因子...
[2025-09-07 01:03:01] 初始化操作符缓存...
[2025-09-07 01:03:01] 预构建所有自旋相关操作符...
[2025-09-07 01:03:01] 开始计算自旋相关函数...
[2025-09-07 01:03:10] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 9.020s
[2025-09-07 01:03:21] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 10.840s
[2025-09-07 01:03:27] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.088s
[2025-09-07 01:03:33] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.114s
[2025-09-07 01:03:39] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.129s
[2025-09-07 01:03:45] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.095s
[2025-09-07 01:03:51] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.119s
[2025-09-07 01:03:57] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.145s
[2025-09-07 01:04:04] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.141s
[2025-09-07 01:04:10] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.123s
[2025-09-07 01:04:16] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.123s
[2025-09-07 01:04:22] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.128s
[2025-09-07 01:04:28] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.126s
[2025-09-07 01:04:34] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.096s
[2025-09-07 01:04:40] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.093s
[2025-09-07 01:04:46] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.122s
[2025-09-07 01:04:53] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.146s
[2025-09-07 01:04:59] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.091s
[2025-09-07 01:05:05] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.127s
[2025-09-07 01:05:11] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.146s
[2025-09-07 01:05:17] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.121s
[2025-09-07 01:05:23] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.125s
[2025-09-07 01:05:29] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.143s
[2025-09-07 01:05:35] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.146s
[2025-09-07 01:05:42] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.123s
[2025-09-07 01:05:48] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.146s
[2025-09-07 01:05:54] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.118s
[2025-09-07 01:06:00] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.145s
[2025-09-07 01:06:06] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.118s
[2025-09-07 01:06:12] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.147s
[2025-09-07 01:06:18] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.122s
[2025-09-07 01:06:25] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.118s
[2025-09-07 01:06:31] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.093s
[2025-09-07 01:06:37] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.148s
[2025-09-07 01:06:43] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.123s
[2025-09-07 01:06:49] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.094s
[2025-09-07 01:06:55] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.129s
[2025-09-07 01:07:01] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.126s
[2025-09-07 01:07:07] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.132s
[2025-09-07 01:07:14] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.129s
[2025-09-07 01:07:20] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.119s
[2025-09-07 01:07:26] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.129s
[2025-09-07 01:07:32] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.090s
[2025-09-07 01:07:38] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.146s
[2025-09-07 01:07:44] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.123s
[2025-09-07 01:07:50] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.131s
[2025-09-07 01:07:56] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.090s
[2025-09-07 01:08:02] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.091s
[2025-09-07 01:08:09] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.115s
[2025-09-07 01:08:15] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.131s
[2025-09-07 01:08:21] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.092s
[2025-09-07 01:08:27] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.116s
[2025-09-07 01:08:33] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.132s
[2025-09-07 01:08:39] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.098s
[2025-09-07 01:08:45] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.123s
[2025-09-07 01:08:51] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.093s
[2025-09-07 01:08:58] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.127s
[2025-09-07 01:09:04] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.122s
[2025-09-07 01:09:10] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.148s
[2025-09-07 01:09:16] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.094s
[2025-09-07 01:09:22] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.133s
[2025-09-07 01:09:28] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.094s
[2025-09-07 01:09:34] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.148s
[2025-09-07 01:09:40] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.092s
[2025-09-07 01:09:47] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.122s
[2025-09-07 01:09:53] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.092s
[2025-09-07 01:09:59] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.133s
[2025-09-07 01:10:05] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.091s
[2025-09-07 01:10:11] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.092s
[2025-09-07 01:10:17] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.142s
[2025-09-07 01:10:23] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.096s
[2025-09-07 01:10:29] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.142s
[2025-09-07 01:10:35] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.151s
[2025-09-07 01:10:42] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.093s
[2025-09-07 01:10:48] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.147s
[2025-09-07 01:10:54] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.119s
[2025-09-07 01:11:00] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.132s
[2025-09-07 01:11:06] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.091s
[2025-09-07 01:11:12] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.148s
[2025-09-07 01:11:18] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.092s
[2025-09-07 01:11:24] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.093s
[2025-09-07 01:11:31] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.090s
[2025-09-07 01:11:37] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.150s
[2025-09-07 01:11:43] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.115s
[2025-09-07 01:11:49] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.091s
[2025-09-07 01:11:55] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.148s
[2025-09-07 01:12:01] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.122s
[2025-09-07 01:12:07] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.091s
[2025-09-07 01:12:13] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.098s
[2025-09-07 01:12:20] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.146s
[2025-09-07 01:12:26] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.124s
[2025-09-07 01:12:32] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.129s
[2025-09-07 01:12:38] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.147s
[2025-09-07 01:12:44] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.126s
[2025-09-07 01:12:50] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.131s
[2025-09-07 01:12:56] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.126s
[2025-09-07 01:13:02] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.125s
[2025-09-07 01:13:09] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.093s
[2025-09-07 01:13:15] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.143s
[2025-09-07 01:13:21] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.095s
[2025-09-07 01:13:21] 自旋相关函数计算完成,总耗时 619.96 秒
[2025-09-07 01:13:21] 计算傅里叶变换...
[2025-09-07 01:13:22] 自旋结构因子计算完成
[2025-09-07 01:13:23] 自旋相关函数平均误差: 0.000784
