[2025-09-05 23:08:16] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.05/training/checkpoints/final_GCNN.pkl
[2025-09-05 23:08:16]   - 迭代次数: final
[2025-09-05 23:08:16]   - 能量: -85.575781+0.000313j ± 0.113561
[2025-09-05 23:08:16]   - 时间戳: 2025-09-05T23:04:13.344858+08:00
[2025-09-05 23:08:30] ✓ 变分状态参数已从checkpoint恢复
[2025-09-05 23:08:30] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-05 23:08:30] ==================================================
[2025-09-05 23:08:30] GCNN for Shastry-Sutherland Model
[2025-09-05 23:08:30] ==================================================
[2025-09-05 23:08:30] System parameters:
[2025-09-05 23:08:30]   - System size: L=5, N=100
[2025-09-05 23:08:30]   - System parameters: J1=0.06, J2=0.0, Q=1.0
[2025-09-05 23:08:30] --------------------------------------------------
[2025-09-05 23:08:30] Model parameters:
[2025-09-05 23:08:30]   - Number of layers = 4
[2025-09-05 23:08:30]   - Number of features = 4
[2025-09-05 23:08:30]   - Total parameters = 19628
[2025-09-05 23:08:30] --------------------------------------------------
[2025-09-05 23:08:30] Training parameters:
[2025-09-05 23:08:30]   - Learning rate: 0.015
[2025-09-05 23:08:30]   - Total iterations: 1050
[2025-09-05 23:08:30]   - Annealing cycles: 3
[2025-09-05 23:08:30]   - Initial period: 150
[2025-09-05 23:08:30]   - Period multiplier: 2.0
[2025-09-05 23:08:30]   - Temperature range: 0.0-1.0
[2025-09-05 23:08:30]   - Samples: 4096
[2025-09-05 23:08:30]   - Discarded samples: 0
[2025-09-05 23:08:30]   - Chunk size: 2048
[2025-09-05 23:08:30]   - Diagonal shift: 0.2
[2025-09-05 23:08:30]   - Gradient clipping: 1.0
[2025-09-05 23:08:30]   - Checkpoint enabled: interval=105
[2025-09-05 23:08:30]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.06/training/checkpoints
[2025-09-05 23:08:30] --------------------------------------------------
[2025-09-05 23:08:30] Device status:
[2025-09-05 23:08:30]   - Devices model: NVIDIA H200 NVL
[2025-09-05 23:08:30]   - Number of devices: 1
[2025-09-05 23:08:30]   - Sharding: True
[2025-09-05 23:08:30] ============================================================
[2025-09-05 23:09:30] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -86.262767+0.014282j
[2025-09-05 23:10:12] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -86.137277+0.013043j
[2025-09-05 23:10:36] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -86.306767-0.001021j
[2025-09-05 23:11:07] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -86.127298-0.000083j
[2025-09-05 23:11:38] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -86.200609-0.003846j
[2025-09-05 23:12:09] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -86.249898+0.001283j
[2025-09-05 23:12:39] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -86.278115+0.006740j
[2025-09-05 23:13:10] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -86.087337-0.002455j
[2025-09-05 23:13:41] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -86.154270-0.001204j
[2025-09-05 23:14:11] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -86.254234-0.003866j
[2025-09-05 23:14:42] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -86.300931+0.000010j
[2025-09-05 23:15:08] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -86.370586+0.004514j
[2025-09-05 23:15:39] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -86.406979+0.004883j
[2025-09-05 23:16:09] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -86.503340+0.002084j
[2025-09-05 23:16:40] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -86.328266+0.001539j
[2025-09-05 23:17:11] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -86.604194-0.005821j
[2025-09-05 23:17:42] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -86.590244+0.004127j
[2025-09-05 23:18:12] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -86.530172-0.003935j
[2025-09-05 23:18:43] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -86.536780+0.002224j
[2025-09-05 23:19:14] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -86.407231-0.001543j
[2025-09-05 23:19:44] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -86.552821+0.000957j
[2025-09-05 23:20:15] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -86.464138-0.007610j
[2025-09-05 23:20:46] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -86.366689-0.001285j
[2025-09-05 23:21:16] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -86.392673-0.001632j
[2025-09-05 23:21:47] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -86.354417-0.003051j
[2025-09-05 23:22:18] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -86.279790+0.002832j
[2025-09-05 23:22:48] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -86.329164+0.000891j
[2025-09-05 23:23:19] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -86.280060+0.001255j
[2025-09-05 23:23:50] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -86.103581-0.002704j
[2025-09-05 23:24:21] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -86.098758+0.008389j
[2025-09-05 23:24:51] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -86.072398+0.000645j
[2025-09-05 23:25:22] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -86.071477-0.000773j
[2025-09-05 23:25:53] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -86.100422+0.001757j
[2025-09-05 23:26:23] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -86.083774+0.004797j
[2025-09-05 23:26:54] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -86.027391-0.000681j
[2025-09-05 23:27:25] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -86.014776+0.003111j
[2025-09-05 23:27:55] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -86.114141-0.003444j
[2025-09-05 23:28:26] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -86.288774-0.002979j
[2025-09-05 23:28:57] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -86.107076-0.003118j
[2025-09-05 23:29:27] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -86.250502+0.001887j
[2025-09-05 23:29:56] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -86.216749-0.002545j
[2025-09-05 23:30:16] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -86.260119-0.000696j
[2025-09-05 23:30:43] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -86.179341+0.005095j
[2025-09-05 23:31:14] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -86.256333+0.000732j
[2025-09-05 23:31:45] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -86.314201-0.002800j
[2025-09-05 23:32:16] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -86.276835+0.002211j
[2025-09-05 23:32:46] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -86.202563-0.002003j
[2025-09-05 23:33:17] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -86.153332-0.000015j
[2025-09-05 23:33:48] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -86.268843-0.001974j
[2025-09-05 23:34:18] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -86.416417-0.001522j
[2025-09-05 23:34:48] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -86.336802-0.002700j
[2025-09-05 23:35:15] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -86.342964+0.002589j
[2025-09-05 23:35:46] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -86.321235+0.004942j
[2025-09-05 23:36:16] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -86.390874+0.004218j
[2025-09-05 23:36:47] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -86.475809+0.001225j
[2025-09-05 23:37:17] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -86.364795+0.003135j
[2025-09-05 23:37:48] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -86.268133-0.002028j
[2025-09-05 23:38:18] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -86.369577+0.000965j
[2025-09-05 23:38:49] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -86.349254-0.001076j
[2025-09-05 23:39:20] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -86.326218-0.004650j
[2025-09-05 23:39:50] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -86.324252+0.001137j
[2025-09-05 23:40:21] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -86.366804+0.004924j
[2025-09-05 23:40:51] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -86.386140-0.003998j
[2025-09-05 23:41:22] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -86.381719+0.000633j
[2025-09-05 23:41:52] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -86.397151+0.000530j
[2025-09-05 23:42:23] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -86.225372+0.001793j
[2025-09-05 23:42:54] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -86.170791-0.000903j
[2025-09-05 23:43:24] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -86.157891-0.001233j
[2025-09-05 23:43:55] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -86.217100-0.003016j
[2025-09-05 23:44:25] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -86.080279+0.000003j
[2025-09-05 23:44:56] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -86.010479-0.003382j
[2025-09-05 23:45:26] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -86.074422+0.002882j
[2025-09-05 23:45:57] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -86.209887+0.000327j
[2025-09-05 23:46:28] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -86.213442+0.003687j
[2025-09-05 23:46:58] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -86.225428-0.007617j
[2025-09-05 23:47:29] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -86.128648+0.005376j
[2025-09-05 23:47:59] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -86.168579+0.000310j
[2025-09-05 23:48:30] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -86.169959-0.004809j
[2025-09-05 23:49:00] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -86.176111+0.003888j
[2025-09-05 23:49:31] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -86.186943-0.003295j
[2025-09-05 23:49:57] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -86.386734-0.001310j
[2025-09-05 23:50:18] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -86.194160-0.003504j
[2025-09-05 23:50:47] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -86.271500+0.028809j
[2025-09-05 23:51:17] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -86.347754-0.000385j
[2025-09-05 23:51:48] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -86.366709+0.000956j
[2025-09-05 23:52:19] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -86.367284+0.000544j
[2025-09-05 23:52:49] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -86.372065+0.010713j
[2025-09-05 23:53:20] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -86.329088+0.000928j
[2025-09-05 23:53:51] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -86.451464+0.001314j
[2025-09-05 23:54:22] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -86.469285-0.000369j
[2025-09-05 23:54:50] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -86.333361+0.003283j
[2025-09-05 23:55:18] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -86.409629-0.000560j
[2025-09-05 23:55:49] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -86.421197-0.002298j
[2025-09-05 23:56:20] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -86.256422+0.000875j
[2025-09-05 23:56:50] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -86.391228+0.001807j
[2025-09-05 23:57:21] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -86.434571+0.005178j
[2025-09-05 23:57:51] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -86.490209+0.003510j
[2025-09-05 23:58:22] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -86.397774-0.003137j
[2025-09-05 23:58:53] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -86.122483+0.001709j
[2025-09-05 23:59:23] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -86.161819-0.000193j
[2025-09-05 23:59:54] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -86.213161+0.009363j
[2025-09-06 00:00:25] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -86.204322+0.000915j
[2025-09-06 00:00:55] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -86.407165+0.001994j
[2025-09-06 00:01:26] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -86.351094+0.001915j
[2025-09-06 00:01:57] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -86.487987-0.005186j
[2025-09-06 00:01:57] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-06 00:02:27] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -86.389158+0.006276j
[2025-09-06 00:02:58] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -86.489666-0.003488j
[2025-09-06 00:03:28] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -86.415894-0.004647j
[2025-09-06 00:03:59] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -86.305005+0.002072j
[2025-09-06 00:04:30] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -86.480481-0.002501j
[2025-09-06 00:05:00] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -86.465832+0.000881j
[2025-09-06 00:05:31] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -86.277763-0.003031j
[2025-09-06 00:06:01] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -86.374758+0.000908j
[2025-09-06 00:06:32] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -86.399615-0.002550j
[2025-09-06 00:07:03] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -86.259535+0.003144j
[2025-09-06 00:07:33] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -86.213965-0.001306j
[2025-09-06 00:08:04] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -86.188412+0.000279j
[2025-09-06 00:08:35] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -86.144767+0.005842j
[2025-09-06 00:09:05] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -86.159722+0.004246j
[2025-09-06 00:09:36] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -86.181336+0.006832j
[2025-09-06 00:10:00] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -86.165430-0.002834j
[2025-09-06 00:10:21] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -86.347011+0.001668j
[2025-09-06 00:10:52] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -86.240803-0.000256j
[2025-09-06 00:11:22] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -86.294484+0.002728j
[2025-09-06 00:11:53] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -86.352426+0.002296j
[2025-09-06 00:12:24] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -86.221295+0.002271j
[2025-09-06 00:12:55] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -86.188247+0.004557j
[2025-09-06 00:13:25] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -86.136589+0.001018j
[2025-09-06 00:13:56] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -86.108683-0.003602j
[2025-09-06 00:14:27] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -86.025783+0.000237j
[2025-09-06 00:14:55] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -86.123799-0.001752j
[2025-09-06 00:15:23] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -86.174039-0.005548j
[2025-09-06 00:15:54] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -86.194027-0.002641j
[2025-09-06 00:16:24] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -86.370890+0.002189j
[2025-09-06 00:16:55] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -86.275222-0.001847j
[2025-09-06 00:17:26] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -86.358409+0.000530j
[2025-09-06 00:17:56] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -86.351108-0.004509j
[2025-09-06 00:18:27] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -86.269539-0.000177j
[2025-09-06 00:18:57] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -86.266467-0.002631j
[2025-09-06 00:19:28] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -86.149950-0.002404j
[2025-09-06 00:19:58] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -86.093100-0.006092j
[2025-09-06 00:20:29] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -86.109736-0.002735j
[2025-09-06 00:21:00] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -86.107465-0.000681j
[2025-09-06 00:21:30] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -86.100844-0.001242j
[2025-09-06 00:22:01] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -86.182322-0.003928j
[2025-09-06 00:22:31] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -86.285508+0.002965j
[2025-09-06 00:23:02] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -86.294820-0.000639j
[2025-09-06 00:23:32] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -86.305051-0.001449j
[2025-09-06 00:24:03] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -86.300159+0.001225j
[2025-09-06 00:24:34] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -86.199142-0.003437j
[2025-09-06 00:24:34] RESTART #1 | Period: 300
[2025-09-06 00:25:04] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -86.173056-0.002327j
[2025-09-06 00:25:35] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -86.275995+0.000485j
[2025-09-06 00:26:05] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -86.245334-0.002828j
[2025-09-06 00:26:36] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -86.163224+0.004166j
[2025-09-06 00:27:06] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -86.222904+0.000035j
[2025-09-06 00:27:37] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -86.162854+0.000498j
[2025-09-06 00:28:07] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -86.066552-0.003001j
[2025-09-06 00:28:38] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -85.982635-0.000803j
[2025-09-06 00:29:09] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -86.158325-0.000681j
[2025-09-06 00:29:39] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -86.061484+0.000127j
[2025-09-06 00:30:01] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -86.017316+0.003524j
[2025-09-06 00:30:25] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -86.197780+0.000567j
[2025-09-06 00:30:56] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -86.391328-0.001712j
[2025-09-06 00:31:27] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -86.348960+0.001016j
[2025-09-06 00:31:57] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -86.147237-0.002991j
[2025-09-06 00:32:28] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -86.141216+0.004922j
[2025-09-06 00:32:59] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -86.240362+0.001510j
[2025-09-06 00:33:30] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -86.198972+0.000727j
[2025-09-06 00:34:00] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -86.178150-0.003731j
[2025-09-06 00:34:31] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -86.217731-0.005492j
[2025-09-06 00:34:57] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -86.339780-0.003106j
[2025-09-06 00:35:28] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -86.372742+0.003242j
[2025-09-06 00:35:58] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -86.409380-0.001971j
[2025-09-06 00:36:29] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -86.494639+0.003438j
[2025-09-06 00:36:59] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -86.493004+0.001241j
[2025-09-06 00:37:30] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -86.621101-0.003584j
[2025-09-06 00:38:01] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -86.542037-0.000518j
[2025-09-06 00:38:31] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -86.589714-0.005169j
[2025-09-06 00:39:02] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -86.390585-0.000259j
[2025-09-06 00:39:32] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -86.471935-0.001716j
[2025-09-06 00:40:03] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -86.448442+0.000890j
[2025-09-06 00:40:34] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -86.484343+0.000912j
[2025-09-06 00:41:04] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -86.462844-0.007581j
[2025-09-06 00:41:35] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -86.487724+0.008695j
[2025-09-06 00:42:05] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -86.421159-0.003413j
[2025-09-06 00:42:36] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -86.416529-0.007019j
[2025-09-06 00:43:06] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -86.315631+0.002390j
[2025-09-06 00:43:37] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -86.335952+0.001847j
[2025-09-06 00:44:08] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -86.308141+0.001039j
[2025-09-06 00:44:38] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -86.313828-0.001501j
[2025-09-06 00:45:09] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -86.304316+0.003565j
[2025-09-06 00:45:39] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -86.409780-0.006794j
[2025-09-06 00:46:10] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -86.318536+0.001090j
[2025-09-06 00:46:40] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -86.329079+0.004835j
[2025-09-06 00:47:11] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -86.269967+0.001593j
[2025-09-06 00:47:42] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -86.272548-0.002436j
[2025-09-06 00:48:12] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -86.327918+0.000853j
[2025-09-06 00:48:43] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -86.345500+0.005168j
[2025-09-06 00:49:13] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -86.255269+0.002830j
[2025-09-06 00:49:42] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -86.291206-0.002746j
[2025-09-06 00:50:03] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -86.314023+0.002271j
[2025-09-06 00:50:28] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -86.257873+0.000560j
[2025-09-06 00:50:59] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -86.112056-0.000179j
[2025-09-06 00:51:30] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -86.182736-0.001667j
[2025-09-06 00:52:00] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -86.247315-0.001368j
[2025-09-06 00:52:31] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -86.271652+0.000153j
[2025-09-06 00:53:02] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -86.229134-0.003022j
[2025-09-06 00:53:33] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -86.253735-0.003437j
[2025-09-06 00:54:03] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -86.382530+0.001770j
[2025-09-06 00:54:34] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -86.323386-0.002779j
[2025-09-06 00:54:34] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-06 00:55:00] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -86.311055-0.007335j
[2025-09-06 00:55:31] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -86.216232-0.006583j
[2025-09-06 00:56:02] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -86.193619-0.001902j
[2025-09-06 00:56:32] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -86.265733-0.002491j
[2025-09-06 00:57:03] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -86.315441-0.000558j
[2025-09-06 00:57:34] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -86.209753+0.005250j
[2025-09-06 00:58:04] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -86.302151-0.002560j
[2025-09-06 00:58:35] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -86.434153-0.002822j
[2025-09-06 00:59:06] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -86.530156+0.000328j
[2025-09-06 00:59:36] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -86.440932-0.004957j
[2025-09-06 01:00:07] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -86.303287-0.011101j
[2025-09-06 01:00:38] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -86.181390+0.007502j
[2025-09-06 01:01:08] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -86.264167+0.003226j
[2025-09-06 01:01:39] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -86.132071+0.003398j
[2025-09-06 01:02:10] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -86.353540-0.001604j
[2025-09-06 01:02:41] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -86.244700-0.008661j
[2025-09-06 01:03:11] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -86.289781-0.003637j
[2025-09-06 01:03:42] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -86.294437-0.001606j
[2025-09-06 01:04:13] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -86.386424+0.001003j
[2025-09-06 01:04:43] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -86.384115-0.002966j
[2025-09-06 01:05:14] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -86.274691-0.004257j
[2025-09-06 01:05:45] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -86.378605+0.000812j
[2025-09-06 01:06:15] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -86.467244+0.000614j
[2025-09-06 01:06:46] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -86.403431-0.001750j
[2025-09-06 01:07:17] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -86.343891+0.008167j
[2025-09-06 01:07:47] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -86.293470+0.005271j
[2025-09-06 01:08:18] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -86.394938-0.001848j
[2025-09-06 01:08:49] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -86.320469-0.004637j
[2025-09-06 01:09:20] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -86.211337+0.003553j
[2025-09-06 01:09:47] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -86.180487-0.005972j
[2025-09-06 01:10:07] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -86.243392+0.002251j
[2025-09-06 01:10:33] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -86.121966+0.001325j
[2025-09-06 01:11:03] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -86.258801+0.000952j
[2025-09-06 01:11:34] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -86.283239+0.001249j
[2025-09-06 01:12:05] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -86.249205-0.006676j
[2025-09-06 01:12:36] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -86.186946+0.000174j
[2025-09-06 01:13:06] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -86.278973+0.002508j
[2025-09-06 01:13:37] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -86.343063-0.001937j
[2025-09-06 01:14:08] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -86.205634-0.003160j
[2025-09-06 01:14:39] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -86.280954+0.006052j
[2025-09-06 01:15:05] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -86.250203+0.000962j
[2025-09-06 01:15:35] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -86.274844-0.000537j
[2025-09-06 01:16:06] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -86.293492+0.002039j
[2025-09-06 01:16:37] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -86.335443-0.000162j
[2025-09-06 01:17:07] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -86.277608-0.001650j
[2025-09-06 01:17:38] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -86.250195-0.002940j
[2025-09-06 01:18:08] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -86.279644+0.005326j
[2025-09-06 01:18:39] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -86.256603-0.000987j
[2025-09-06 01:19:10] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -86.084570+0.002211j
[2025-09-06 01:19:40] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -86.148921-0.002542j
[2025-09-06 01:20:11] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -86.204489+0.001338j
[2025-09-06 01:20:42] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -86.369163-0.000230j
[2025-09-06 01:21:12] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -86.227363+0.005917j
[2025-09-06 01:21:43] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -86.428240-0.000359j
[2025-09-06 01:22:13] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -86.483727+0.001235j
[2025-09-06 01:22:44] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -86.318353+0.004023j
[2025-09-06 01:23:15] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -86.280241-0.001953j
[2025-09-06 01:23:45] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -86.238988-0.003717j
[2025-09-06 01:24:16] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -86.141626-0.001306j
[2025-09-06 01:24:47] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -86.252260-0.001305j
[2025-09-06 01:25:17] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -86.234495-0.002513j
[2025-09-06 01:25:48] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -86.298389-0.007785j
[2025-09-06 01:26:18] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -86.323162-0.002598j
[2025-09-06 01:26:49] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -86.282466-0.004299j
[2025-09-06 01:27:19] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -86.097775-0.002406j
[2025-09-06 01:27:50] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -86.261046-0.002598j
[2025-09-06 01:28:20] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -86.121538-0.000415j
[2025-09-06 01:28:51] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -86.421130+0.000652j
[2025-09-06 01:29:22] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -86.130346-0.000992j
[2025-09-06 01:29:49] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -86.102084+0.001174j
[2025-09-06 01:30:09] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -86.102764-0.005642j
[2025-09-06 01:30:37] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -86.009464+0.001584j
[2025-09-06 01:31:08] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -86.147029-0.006174j
[2025-09-06 01:31:38] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -86.012915+0.001577j
[2025-09-06 01:32:09] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -86.143089-0.001714j
[2025-09-06 01:32:40] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -86.377494-0.003595j
[2025-09-06 01:33:10] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -86.403627+0.003694j
[2025-09-06 01:33:41] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -86.339896-0.000961j
[2025-09-06 01:34:12] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -86.009557+0.004101j
[2025-09-06 01:34:41] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -86.148521+0.000018j
[2025-09-06 01:35:08] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -86.121706-0.002839j
[2025-09-06 01:35:39] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -86.175330-0.003020j
[2025-09-06 01:36:09] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -86.160325+0.000427j
[2025-09-06 01:36:40] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -86.258197+0.001743j
[2025-09-06 01:37:10] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -86.256471-0.002810j
[2025-09-06 01:37:41] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -86.424154-0.000510j
[2025-09-06 01:38:12] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -86.250341-0.013171j
[2025-09-06 01:38:42] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -86.366195+0.003348j
[2025-09-06 01:39:13] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -86.299977-0.003254j
[2025-09-06 01:39:43] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -86.078083-0.005435j
[2025-09-06 01:40:14] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -86.175451-0.004121j
[2025-09-06 01:40:44] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -86.188978+0.001666j
[2025-09-06 01:41:15] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -86.418726-0.004430j
[2025-09-06 01:41:45] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -86.230998-0.003432j
[2025-09-06 01:42:16] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -86.260557-0.002938j
[2025-09-06 01:42:47] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -86.305309+0.002649j
[2025-09-06 01:43:17] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -86.483823-0.001113j
[2025-09-06 01:43:48] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -86.488497+0.000657j
[2025-09-06 01:44:18] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -86.203017+0.003151j
[2025-09-06 01:44:49] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -86.354376+0.002482j
[2025-09-06 01:45:19] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -86.387711+0.001373j
[2025-09-06 01:45:50] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -86.378787-0.004836j
[2025-09-06 01:46:20] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -86.368035-0.000261j
[2025-09-06 01:46:51] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -86.337309-0.001768j
[2025-09-06 01:47:21] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -86.175380-0.006807j
[2025-09-06 01:47:22] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-06 01:47:52] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -86.234249+0.001188j
[2025-09-06 01:48:23] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -86.320568+0.003597j
[2025-09-06 01:48:53] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -86.148488+0.006301j
[2025-09-06 01:49:24] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -86.261870+0.002816j
[2025-09-06 01:49:49] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -86.164351+0.001880j
[2025-09-06 01:50:10] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -86.217813-0.000130j
[2025-09-06 01:50:39] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -86.118150+0.000213j
[2025-09-06 01:51:10] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -86.097865+0.000296j
[2025-09-06 01:51:41] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -86.201866+0.002939j
[2025-09-06 01:52:11] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -86.260780+0.001648j
[2025-09-06 01:52:42] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -86.304335-0.009115j
[2025-09-06 01:53:13] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -86.217197+0.003457j
[2025-09-06 01:53:43] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -86.347281-0.002942j
[2025-09-06 01:54:14] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -86.204858-0.001081j
[2025-09-06 01:54:42] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -86.183145+0.001079j
[2025-09-06 01:55:11] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -86.412989+0.003199j
[2025-09-06 01:55:42] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -86.408502-0.002720j
[2025-09-06 01:56:12] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -86.555173-0.003404j
[2025-09-06 01:56:43] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -86.312470-0.002799j
[2025-09-06 01:57:13] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -86.211349-0.000454j
[2025-09-06 01:57:44] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -86.207703+0.000191j
[2025-09-06 01:58:15] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -86.198298+0.000740j
[2025-09-06 01:58:46] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -86.037703+0.000067j
[2025-09-06 01:59:16] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -86.141031-0.002024j
[2025-09-06 01:59:47] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -86.209149+0.001645j
[2025-09-06 02:00:18] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -86.324535-0.001421j
[2025-09-06 02:00:48] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -86.248923-0.001532j
[2025-09-06 02:01:19] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -86.195003-0.000634j
[2025-09-06 02:01:49] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -86.225581+0.005360j
[2025-09-06 02:02:20] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -86.172798-0.003485j
[2025-09-06 02:02:51] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -86.098291-0.000915j
[2025-09-06 02:03:21] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -86.097530+0.005679j
[2025-09-06 02:03:52] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -86.130906-0.004551j
[2025-09-06 02:04:23] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -86.268499+0.004368j
[2025-09-06 02:04:53] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -86.399955-0.007192j
[2025-09-06 02:05:24] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -86.289389-0.004975j
[2025-09-06 02:05:55] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -86.422544+0.002064j
[2025-09-06 02:06:25] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -86.349387-0.003779j
[2025-09-06 02:06:56] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -86.319664-0.003258j
[2025-09-06 02:07:27] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -86.268651+0.002939j
[2025-09-06 02:07:57] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -86.202512+0.004885j
[2025-09-06 02:08:28] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -86.137288-0.002726j
[2025-09-06 02:08:59] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -86.298173-0.000279j
[2025-09-06 02:09:29] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -86.240849-0.001265j
[2025-09-06 02:09:52] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -86.266318+0.004735j
[2025-09-06 02:10:15] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -86.421578-0.007309j
[2025-09-06 02:10:46] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -86.308016-0.008521j
[2025-09-06 02:11:16] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -86.166996-0.004118j
[2025-09-06 02:11:47] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -86.254942+0.000517j
[2025-09-06 02:12:18] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -86.340750+0.001870j
[2025-09-06 02:12:48] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -86.386933-0.001109j
[2025-09-06 02:13:19] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -86.277781+0.008712j
[2025-09-06 02:13:50] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -86.300912+0.003591j
[2025-09-06 02:14:21] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -86.461895+0.000466j
[2025-09-06 02:14:48] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -86.300632-0.004729j
[2025-09-06 02:15:17] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -86.235188-0.001904j
[2025-09-06 02:15:48] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -86.107576+0.002258j
[2025-09-06 02:16:19] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -86.208463+0.003671j
[2025-09-06 02:16:49] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -86.213154+0.000918j
[2025-09-06 02:17:20] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -85.961561+0.001979j
[2025-09-06 02:17:51] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -85.939573-0.003654j
[2025-09-06 02:18:21] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -86.118517-0.005276j
[2025-09-06 02:18:52] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -86.218067-0.004790j
[2025-09-06 02:19:23] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -86.274587+0.004738j
[2025-09-06 02:19:54] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -86.213956+0.001034j
[2025-09-06 02:20:24] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -86.250626+0.003795j
[2025-09-06 02:20:55] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -86.075299-0.000727j
[2025-09-06 02:21:26] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -86.158280+0.001487j
[2025-09-06 02:21:56] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -86.155849-0.001477j
[2025-09-06 02:22:27] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -86.045686-0.005202j
[2025-09-06 02:22:58] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -86.064278+0.001338j
[2025-09-06 02:23:28] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -85.980142+0.004152j
[2025-09-06 02:23:59] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -86.015407+0.002520j
[2025-09-06 02:24:30] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -86.041326-0.002108j
[2025-09-06 02:25:00] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -86.129702-0.001593j
[2025-09-06 02:25:31] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -86.150790-0.003199j
[2025-09-06 02:26:02] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -86.262706+0.004614j
[2025-09-06 02:26:33] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -86.352588+0.000549j
[2025-09-06 02:27:03] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -86.346443-0.000455j
[2025-09-06 02:27:34] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -86.487975+0.002479j
[2025-09-06 02:28:05] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -86.560760+0.004613j
[2025-09-06 02:28:35] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -86.302764-0.002806j
[2025-09-06 02:29:06] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -86.301831+0.004035j
[2025-09-06 02:29:36] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -86.218164-0.006259j
[2025-09-06 02:29:57] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -86.175125-0.008038j
[2025-09-06 02:30:20] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -86.241411-0.003098j
[2025-09-06 02:30:51] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -86.166291-0.002334j
[2025-09-06 02:31:22] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -86.082119-0.001052j
[2025-09-06 02:31:52] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -86.219881+0.000772j
[2025-09-06 02:32:23] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -86.269031-0.001451j
[2025-09-06 02:32:54] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -86.148241-0.000229j
[2025-09-06 02:33:24] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -86.198060+0.002758j
[2025-09-06 02:33:55] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -86.185966-0.000947j
[2025-09-06 02:34:26] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -86.347813+0.003379j
[2025-09-06 02:34:52] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -86.417013+0.005236j
[2025-09-06 02:35:22] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -86.528194+0.000031j
[2025-09-06 02:35:53] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -86.516093+0.002641j
[2025-09-06 02:36:24] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -86.446623+0.002965j
[2025-09-06 02:36:54] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -86.410617+0.000978j
[2025-09-06 02:37:25] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -86.483107-0.001657j
[2025-09-06 02:37:55] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -86.254768-0.000154j
[2025-09-06 02:38:26] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -86.413575-0.000004j
[2025-09-06 02:38:57] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -86.445207-0.000061j
[2025-09-06 02:39:27] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -86.485404-0.002881j
[2025-09-06 02:39:58] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -86.356033-0.001637j
[2025-09-06 02:39:58] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-06 02:40:28] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -86.199177-0.000463j
[2025-09-06 02:40:59] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -86.266321-0.003529j
[2025-09-06 02:41:29] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -86.241483+0.002026j
[2025-09-06 02:42:00] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -86.303863-0.000167j
[2025-09-06 02:42:31] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -86.179088-0.006203j
[2025-09-06 02:43:01] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -86.395852-0.003400j
[2025-09-06 02:43:32] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -86.221142+0.002221j
[2025-09-06 02:44:02] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -86.204270+0.000622j
[2025-09-06 02:44:33] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -86.140987-0.001023j
[2025-09-06 02:45:04] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -86.218200+0.002190j
[2025-09-06 02:45:34] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -86.183286+0.005238j
[2025-09-06 02:46:05] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -86.124615+0.000616j
[2025-09-06 02:46:35] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -86.172467-0.004803j
[2025-09-06 02:47:06] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -86.307115+0.000988j
[2025-09-06 02:47:36] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -86.392248-0.006199j
[2025-09-06 02:48:07] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -86.313328+0.001772j
[2025-09-06 02:48:38] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -86.288230-0.001059j
[2025-09-06 02:49:08] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -86.351659+0.002330j
[2025-09-06 02:49:38] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -86.313603+0.000887j
[2025-09-06 02:49:59] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -86.289395+0.001848j
[2025-09-06 02:50:24] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -86.258002+0.003856j
[2025-09-06 02:50:55] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -86.180214-0.001592j
[2025-09-06 02:51:25] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -86.265518-0.001793j
[2025-09-06 02:51:56] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -86.292301+0.001261j
[2025-09-06 02:52:27] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -86.327935+0.001008j
[2025-09-06 02:52:57] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -86.224589+0.003670j
[2025-09-06 02:53:28] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -86.086179+0.002473j
[2025-09-06 02:53:59] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -86.170086+0.000818j
[2025-09-06 02:54:30] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -86.206784+0.001267j
[2025-09-06 02:54:55] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -86.134637-0.004231j
[2025-09-06 02:54:55] RESTART #2 | Period: 600
[2025-09-06 02:55:26] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -86.208277-0.004262j
[2025-09-06 02:55:57] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -86.336557-0.003745j
[2025-09-06 02:56:27] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -86.280366+0.002126j
[2025-09-06 02:56:58] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -86.337908-0.002625j
[2025-09-06 02:57:28] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -86.319871-0.006329j
[2025-09-06 02:57:59] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -86.304235-0.002795j
[2025-09-06 02:58:30] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -86.131173-0.001158j
[2025-09-06 02:59:00] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -86.138452+0.002913j
[2025-09-06 02:59:31] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -86.178213-0.003921j
[2025-09-06 03:00:01] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -86.461353-0.000281j
[2025-09-06 03:00:32] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -86.204858-0.002255j
[2025-09-06 03:01:03] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -86.187975+0.001310j
[2025-09-06 03:01:33] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -86.317036+0.001833j
[2025-09-06 03:02:04] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -86.134159-0.001271j
[2025-09-06 03:02:34] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -86.194172-0.005922j
[2025-09-06 03:03:05] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -86.369159-0.007902j
[2025-09-06 03:03:36] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -86.149970-0.004100j
[2025-09-06 03:04:06] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -86.133821-0.006445j
[2025-09-06 03:04:37] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -86.071176+0.001648j
[2025-09-06 03:05:07] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -86.070245+0.006886j
[2025-09-06 03:05:38] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -86.074170+0.000763j
[2025-09-06 03:06:08] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -86.171449+0.002773j
[2025-09-06 03:06:39] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -86.286413+0.002688j
[2025-09-06 03:07:10] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -86.274141-0.000947j
[2025-09-06 03:07:40] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -86.209605-0.002666j
[2025-09-06 03:08:11] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -86.209907-0.008176j
[2025-09-06 03:08:41] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -86.265910+0.000345j
[2025-09-06 03:09:12] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -86.336799-0.001176j
[2025-09-06 03:09:40] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -86.376329+0.003101j
[2025-09-06 03:10:01] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -86.182760+0.000666j
[2025-09-06 03:10:29] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -86.234975-0.000783j
[2025-09-06 03:10:59] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -86.321372-0.002426j
[2025-09-06 03:11:30] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -86.233193-0.004503j
[2025-09-06 03:12:01] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -86.194771-0.001783j
[2025-09-06 03:12:32] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -86.433658-0.002090j
[2025-09-06 03:13:02] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -86.338639+0.002643j
[2025-09-06 03:13:33] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -86.259518+0.002373j
[2025-09-06 03:14:04] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -86.265627+0.000820j
[2025-09-06 03:14:32] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -86.408141+0.003721j
[2025-09-06 03:15:00] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -86.393041-0.002791j
[2025-09-06 03:15:31] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -86.284706+0.000916j
[2025-09-06 03:16:02] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -86.257503+0.002934j
[2025-09-06 03:16:33] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -86.156190-0.002508j
[2025-09-06 03:17:03] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -86.130150+0.001966j
[2025-09-06 03:17:34] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -86.124504-0.003884j
[2025-09-06 03:18:05] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -86.197405+0.003622j
[2025-09-06 03:18:35] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -85.936588+0.000585j
[2025-09-06 03:19:06] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -86.019358-0.002552j
[2025-09-06 03:19:37] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -86.082716+0.003716j
[2025-09-06 03:20:08] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -85.983096+0.001011j
[2025-09-06 03:20:38] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -86.105115+0.000523j
[2025-09-06 03:21:09] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -86.273512-0.000984j
[2025-09-06 03:21:40] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -86.347723-0.004934j
[2025-09-06 03:22:11] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -86.234895+0.000562j
[2025-09-06 03:22:41] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -86.205232-0.001782j
[2025-09-06 03:23:12] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -86.310912-0.000009j
[2025-09-06 03:23:43] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -86.336317-0.003596j
[2025-09-06 03:24:13] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -86.362074+0.001421j
[2025-09-06 03:24:44] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -86.421061-0.001761j
[2025-09-06 03:25:15] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -86.294061-0.001323j
[2025-09-06 03:25:46] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -86.395216+0.004292j
[2025-09-06 03:26:16] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -86.392823-0.000022j
[2025-09-06 03:26:47] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -86.339205-0.002816j
[2025-09-06 03:27:18] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -86.275609-0.001671j
[2025-09-06 03:27:49] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -86.410370+0.005062j
[2025-09-06 03:28:19] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -86.438957-0.004440j
[2025-09-06 03:28:50] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -86.186700-0.006029j
[2025-09-06 03:29:21] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -86.202267+0.000913j
[2025-09-06 03:29:45] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -86.338064+0.001383j
[2025-09-06 03:30:06] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -86.285258+0.005725j
[2025-09-06 03:30:33] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -86.313662+0.001827j
[2025-09-06 03:31:04] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -86.226977+0.004885j
[2025-09-06 03:31:34] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -86.233984-0.000834j
[2025-09-06 03:32:05] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -86.186094-0.000534j
[2025-09-06 03:32:36] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -86.209245-0.000472j
[2025-09-06 03:32:36] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-06 03:33:06] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -85.999785-0.005457j
[2025-09-06 03:33:37] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -86.056464+0.003502j
[2025-09-06 03:34:08] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -86.242787-0.002081j
[2025-09-06 03:34:37] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -86.185819-0.004608j
[2025-09-06 03:35:04] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -86.220174+0.003993j
[2025-09-06 03:35:35] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -86.167578-0.000440j
[2025-09-06 03:36:05] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -86.336819+0.013816j
[2025-09-06 03:36:36] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -86.310180-0.000862j
[2025-09-06 03:37:07] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -86.199713+0.006520j
[2025-09-06 03:37:37] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -86.158782-0.007107j
[2025-09-06 03:38:08] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -86.220560+0.000190j
[2025-09-06 03:38:38] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -86.299045-0.004653j
[2025-09-06 03:39:09] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -86.299745-0.003190j
[2025-09-06 03:39:39] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -86.518047-0.003667j
[2025-09-06 03:40:10] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -86.581259-0.001876j
[2025-09-06 03:40:41] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -86.664421+0.002311j
[2025-09-06 03:41:11] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -86.388354+0.004165j
[2025-09-06 03:41:42] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -86.560334+0.001296j
[2025-09-06 03:42:12] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -86.482073+0.003880j
[2025-09-06 03:42:43] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -86.594502-0.006318j
[2025-09-06 03:43:14] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -86.507402-0.000496j
[2025-09-06 03:43:44] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -86.409296+0.001894j
[2025-09-06 03:44:15] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -86.333823+0.000271j
[2025-09-06 03:44:45] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -86.340632+0.004114j
[2025-09-06 03:45:16] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -86.368606+0.000263j
[2025-09-06 03:45:46] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -86.425479-0.000478j
[2025-09-06 03:46:17] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -86.421180+0.000029j
[2025-09-06 03:46:48] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -86.349737-0.003648j
[2025-09-06 03:47:18] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -86.334003+0.003582j
[2025-09-06 03:47:49] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -86.377683+0.006127j
[2025-09-06 03:48:19] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -86.442380+0.000254j
[2025-09-06 03:48:50] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -86.263256+0.006664j
[2025-09-06 03:49:20] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -86.289513+0.000606j
[2025-09-06 03:49:46] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -86.383575+0.001745j
[2025-09-06 03:50:07] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -86.268825+0.004961j
[2025-09-06 03:50:37] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -86.408791-0.001141j
[2025-09-06 03:51:08] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -86.358379-0.000344j
[2025-09-06 03:51:38] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -86.211386-0.005132j
[2025-09-06 03:52:09] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -86.320488+0.005194j
[2025-09-06 03:52:40] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -86.295769+0.003321j
[2025-09-06 03:53:10] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -86.253992+0.000375j
[2025-09-06 03:53:41] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -86.265323+0.002610j
[2025-09-06 03:54:12] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -86.370260+0.002560j
[2025-09-06 03:54:40] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -86.401408-0.000906j
[2025-09-06 03:55:08] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -86.479855+0.002758j
[2025-09-06 03:55:39] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -86.428720-0.003092j
[2025-09-06 03:56:09] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -86.193491-0.003880j
[2025-09-06 03:56:40] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -86.260203-0.004202j
[2025-09-06 03:57:10] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -86.252247-0.002264j
[2025-09-06 03:57:41] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -86.387657+0.000065j
[2025-09-06 03:58:12] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -86.416069+0.000129j
[2025-09-06 03:58:42] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -86.214886-0.005690j
[2025-09-06 03:59:13] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -86.232841+0.004809j
[2025-09-06 03:59:43] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -86.203860+0.008307j
[2025-09-06 04:00:14] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -86.408143+0.006672j
[2025-09-06 04:00:44] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -86.340437+0.001988j
[2025-09-06 04:01:15] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -86.204950-0.003248j
[2025-09-06 04:01:45] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -86.205540+0.005675j
[2025-09-06 04:02:16] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -86.089420-0.000579j
[2025-09-06 04:02:47] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -86.139523-0.004605j
[2025-09-06 04:03:17] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -86.101987+0.002373j
[2025-09-06 04:03:48] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -86.263678+0.004842j
[2025-09-06 04:04:18] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -86.276113-0.001437j
[2025-09-06 04:04:49] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -86.462110+0.004821j
[2025-09-06 04:05:19] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -86.413359-0.003074j
[2025-09-06 04:05:50] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -86.326219-0.001168j
[2025-09-06 04:06:20] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -86.235424-0.002280j
[2025-09-06 04:06:51] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -86.130997-0.003847j
[2025-09-06 04:07:22] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -86.186680-0.004337j
[2025-09-06 04:07:52] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -86.328618-0.000310j
[2025-09-06 04:08:23] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -86.298552-0.002884j
[2025-09-06 04:08:53] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -86.378832-0.000543j
[2025-09-06 04:09:24] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -86.296139-0.002552j
[2025-09-06 04:09:47] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -86.363710-0.007649j
[2025-09-06 04:10:09] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -86.460978+0.004262j
[2025-09-06 04:10:40] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -86.270987+0.000518j
[2025-09-06 04:11:11] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -86.207628+0.001353j
[2025-09-06 04:11:41] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -86.178815-0.000064j
[2025-09-06 04:12:12] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -86.117873-0.003826j
[2025-09-06 04:12:43] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -86.301075+0.002006j
[2025-09-06 04:13:13] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -86.400466-0.003625j
[2025-09-06 04:13:44] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -86.352131+0.002183j
[2025-09-06 04:14:15] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -86.240335-0.000277j
[2025-09-06 04:14:42] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -86.228167-0.002596j
[2025-09-06 04:15:11] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -86.203921+0.001641j
[2025-09-06 04:15:42] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -86.108747-0.004620j
[2025-09-06 04:16:13] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -86.339218-0.000520j
[2025-09-06 04:16:43] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -86.090522+0.003454j
[2025-09-06 04:17:14] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -86.136137-0.000929j
[2025-09-06 04:17:44] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -86.012983-0.003827j
[2025-09-06 04:18:15] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -85.969249-0.002274j
[2025-09-06 04:18:46] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -86.052701+0.003032j
[2025-09-06 04:19:16] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -85.986664-0.005583j
[2025-09-06 04:19:47] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -86.258050-0.001334j
[2025-09-06 04:20:17] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -86.116044+0.000257j
[2025-09-06 04:20:48] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -86.146323-0.002465j
[2025-09-06 04:21:19] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -86.232287-0.000200j
[2025-09-06 04:21:49] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -86.268986+0.001164j
[2025-09-06 04:22:20] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -86.200720+0.004590j
[2025-09-06 04:22:50] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -86.125619+0.001100j
[2025-09-06 04:23:21] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -86.025615+0.004661j
[2025-09-06 04:23:51] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -86.241938+0.003255j
[2025-09-06 04:24:22] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -86.327338-0.005308j
[2025-09-06 04:24:53] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -86.214204+0.002244j
[2025-09-06 04:25:23] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -86.166649-0.000088j
[2025-09-06 04:25:23] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-06 04:25:54] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -86.327367+0.006045j
[2025-09-06 04:26:24] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -86.380986-0.002854j
[2025-09-06 04:26:55] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -86.204600-0.001490j
[2025-09-06 04:27:25] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -86.250651+0.000031j
[2025-09-06 04:27:56] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -86.267377-0.000382j
[2025-09-06 04:28:27] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -86.245566-0.002645j
[2025-09-06 04:28:57] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -86.247722-0.002911j
[2025-09-06 04:29:28] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -86.013213+0.004028j
[2025-09-06 04:29:49] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -86.226237+0.002264j
[2025-09-06 04:30:14] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -86.343428+0.007024j
[2025-09-06 04:30:44] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -86.442539-0.002271j
[2025-09-06 04:31:15] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -86.259513-0.005961j
[2025-09-06 04:31:46] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -86.432097-0.003492j
[2025-09-06 04:32:16] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -86.190670+0.001325j
[2025-09-06 04:32:47] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -86.279361-0.007010j
[2025-09-06 04:33:18] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -86.295854-0.001603j
[2025-09-06 04:33:48] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -86.209175-0.006706j
[2025-09-06 04:34:19] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -86.407973-0.007318j
[2025-09-06 04:34:45] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -86.230836-0.000870j
[2025-09-06 04:35:16] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -86.359362-0.003725j
[2025-09-06 04:35:46] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -86.281148+0.005371j
[2025-09-06 04:36:17] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -86.348580+0.001938j
[2025-09-06 04:36:47] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -86.269970+0.006054j
[2025-09-06 04:37:18] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -86.138657+0.003399j
[2025-09-06 04:37:49] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -86.188414-0.006940j
[2025-09-06 04:38:19] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -86.116794-0.000280j
[2025-09-06 04:38:50] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -86.274685-0.003047j
[2025-09-06 04:39:20] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -86.406997-0.001154j
[2025-09-06 04:39:51] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -86.398073+0.000512j
[2025-09-06 04:40:21] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -86.474685+0.006255j
[2025-09-06 04:40:52] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -86.406921+0.005815j
[2025-09-06 04:41:23] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -86.448909+0.002963j
[2025-09-06 04:41:53] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -86.353422+0.005671j
[2025-09-06 04:42:24] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -86.468892+0.000562j
[2025-09-06 04:42:54] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -86.297183+0.001388j
[2025-09-06 04:43:25] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -86.296827+0.000232j
[2025-09-06 04:43:56] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -86.327728-0.004426j
[2025-09-06 04:44:26] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -86.354870-0.000142j
[2025-09-06 04:44:57] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -86.297594-0.000538j
[2025-09-06 04:45:27] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -86.489379+0.000757j
[2025-09-06 04:45:58] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -86.481112-0.002174j
[2025-09-06 04:46:28] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -86.258948-0.003631j
[2025-09-06 04:46:59] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -86.189168+0.000770j
[2025-09-06 04:47:29] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -86.145190+0.003182j
[2025-09-06 04:48:00] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -86.259671+0.001196j
[2025-09-06 04:48:31] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -86.217076-0.000862j
[2025-09-06 04:49:01] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -86.205985+0.002001j
[2025-09-06 04:49:29] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -86.269240+0.007485j
[2025-09-06 04:49:50] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -86.179027+0.006422j
[2025-09-06 04:50:17] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -86.010617-0.001276j
[2025-09-06 04:50:48] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -86.180867+0.003368j
[2025-09-06 04:51:19] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -86.184838+0.000020j
[2025-09-06 04:51:49] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -86.300086-0.001591j
[2025-09-06 04:52:20] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -86.251891-0.002976j
[2025-09-06 04:52:51] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -86.505037-0.003520j
[2025-09-06 04:53:21] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -86.514064+0.006102j
[2025-09-06 04:53:52] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -86.185917-0.002307j
[2025-09-06 04:54:21] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -86.371635+0.004719j
[2025-09-06 04:54:49] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -86.501012+0.002280j
[2025-09-06 04:55:19] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -86.477874+0.002505j
[2025-09-06 04:55:50] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -86.448841-0.004723j
[2025-09-06 04:56:21] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -86.630314+0.004648j
[2025-09-06 04:56:51] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -86.537077+0.005471j
[2025-09-06 04:57:22] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -86.544177+0.000641j
[2025-09-06 04:57:52] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -86.392497-0.003387j
[2025-09-06 04:58:23] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -86.398606+0.002017j
[2025-09-06 04:58:53] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -86.505186+0.002841j
[2025-09-06 04:59:24] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -86.291248+0.002363j
[2025-09-06 04:59:55] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -86.343469+0.000792j
[2025-09-06 05:00:25] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -86.204446-0.003689j
[2025-09-06 05:00:56] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -86.261298-0.002545j
[2025-09-06 05:01:26] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -86.270445+0.000244j
[2025-09-06 05:01:57] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -86.233306+0.002148j
[2025-09-06 05:02:28] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -86.147375+0.001186j
[2025-09-06 05:02:58] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -86.210749-0.000692j
[2025-09-06 05:03:29] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -86.074488-0.001717j
[2025-09-06 05:03:59] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -86.128865+0.003771j
[2025-09-06 05:04:30] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -86.269270+0.000863j
[2025-09-06 05:05:00] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -86.227659-0.000147j
[2025-09-06 05:05:31] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -86.346860-0.003819j
[2025-09-06 05:06:02] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -86.354263+0.001510j
[2025-09-06 05:06:32] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -86.332155+0.001737j
[2025-09-06 05:07:03] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -86.189470-0.003913j
[2025-09-06 05:07:33] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -86.243784+0.001216j
[2025-09-06 05:08:04] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -86.321523+0.000267j
[2025-09-06 05:08:34] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -86.251693+0.003629j
[2025-09-06 05:09:05] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -86.245656+0.000078j
[2025-09-06 05:09:30] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -86.204846+0.000901j
[2025-09-06 05:09:51] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -86.264775+0.000334j
[2025-09-06 05:10:20] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -86.390462+0.005503j
[2025-09-06 05:10:51] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -86.261219-0.000703j
[2025-09-06 05:11:22] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -86.296980-0.001368j
[2025-09-06 05:11:52] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -86.305221+0.002115j
[2025-09-06 05:12:23] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -86.345478+0.005208j
[2025-09-06 05:12:54] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -86.223857+0.002547j
[2025-09-06 05:13:25] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -86.308680+0.000649j
[2025-09-06 05:13:55] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -86.344516-0.002302j
[2025-09-06 05:14:24] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -86.271524+0.001764j
[2025-09-06 05:14:52] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -86.353059-0.001718j
[2025-09-06 05:15:23] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -86.341441+0.003920j
[2025-09-06 05:15:53] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -86.347704+0.000526j
[2025-09-06 05:16:24] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -86.314395+0.001313j
[2025-09-06 05:16:55] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -86.114551+0.001504j
[2025-09-06 05:17:26] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -86.072732-0.005215j
[2025-09-06 05:17:56] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -86.112853+0.001838j
[2025-09-06 05:17:56] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-06 05:18:27] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -86.166004+0.000129j
[2025-09-06 05:18:58] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -86.284173+0.000061j
[2025-09-06 05:19:28] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -86.319010-0.002672j
[2025-09-06 05:19:59] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -86.259598-0.002365j
[2025-09-06 05:20:30] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -86.333487-0.001926j
[2025-09-06 05:21:00] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -86.320315+0.002408j
[2025-09-06 05:21:31] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -86.263416-0.000815j
[2025-09-06 05:22:02] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -86.292222-0.001151j
[2025-09-06 05:22:33] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -86.258380-0.006171j
[2025-09-06 05:23:03] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -86.252855-0.004922j
[2025-09-06 05:23:34] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -86.206593+0.001938j
[2025-09-06 05:24:05] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -86.224806-0.000243j
[2025-09-06 05:24:35] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -86.444081+0.006259j
[2025-09-06 05:25:06] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -86.322738-0.000340j
[2025-09-06 05:25:37] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -86.201658-0.000442j
[2025-09-06 05:26:07] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -86.238164-0.004087j
[2025-09-06 05:26:38] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -86.295940-0.002017j
[2025-09-06 05:27:09] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -86.250551+0.004138j
[2025-09-06 05:27:39] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -86.213155+0.002020j
[2025-09-06 05:28:10] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -86.347541+0.002864j
[2025-09-06 05:28:41] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -86.274896+0.003115j
[2025-09-06 05:29:11] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -86.323319-0.000675j
[2025-09-06 05:29:35] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -86.192355-0.002862j
[2025-09-06 05:29:56] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -86.130551+0.000094j
[2025-09-06 05:30:27] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -86.144355-0.003110j
[2025-09-06 05:30:58] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -86.160123+0.002702j
[2025-09-06 05:31:28] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -86.242680-0.002608j
[2025-09-06 05:31:59] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -86.137974-0.003291j
[2025-09-06 05:32:30] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -86.280208+0.003929j
[2025-09-06 05:33:00] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -86.242233+0.003668j
[2025-09-06 05:33:31] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -86.267978+0.005432j
[2025-09-06 05:34:01] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -86.250764-0.001699j
[2025-09-06 05:34:30] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -86.268396+0.000873j
[2025-09-06 05:34:58] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -86.225432-0.004560j
[2025-09-06 05:35:29] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -86.182594+0.001876j
[2025-09-06 05:36:00] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -86.241861+0.001975j
[2025-09-06 05:36:30] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -86.132455+0.001442j
[2025-09-06 05:37:01] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -86.184239-0.004029j
[2025-09-06 05:37:32] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -86.281772-0.004826j
[2025-09-06 05:38:02] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -86.307212-0.001462j
[2025-09-06 05:38:33] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -86.215434+0.002989j
[2025-09-06 05:39:04] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -86.179626-0.000354j
[2025-09-06 05:39:34] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -86.377485+0.002446j
[2025-09-06 05:40:05] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -86.263369-0.000332j
[2025-09-06 05:40:36] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -86.260556-0.002422j
[2025-09-06 05:41:07] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -86.269145-0.002222j
[2025-09-06 05:41:37] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -86.186035-0.002363j
[2025-09-06 05:42:08] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -86.199654-0.004318j
[2025-09-06 05:42:39] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -86.297102-0.001398j
[2025-09-06 05:43:09] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -86.312280-0.000460j
[2025-09-06 05:43:40] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -86.270395+0.003319j
[2025-09-06 05:44:11] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -86.210174+0.001844j
[2025-09-06 05:44:41] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -86.196547-0.003343j
[2025-09-06 05:45:12] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -86.147561-0.003936j
[2025-09-06 05:45:43] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -86.328077+0.001399j
[2025-09-06 05:46:13] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -86.318931-0.000563j
[2025-09-06 05:46:44] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -86.228045-0.000663j
[2025-09-06 05:47:15] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -86.199512-0.003112j
[2025-09-06 05:47:45] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -86.270720-0.002512j
[2025-09-06 05:48:16] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -86.122813+0.000384j
[2025-09-06 05:48:47] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -86.258585+0.002084j
[2025-09-06 05:49:17] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -86.053322+0.004032j
[2025-09-06 05:49:39] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -86.140991-0.002578j
[2025-09-06 05:49:59] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -86.106469-0.000830j
[2025-09-06 05:50:30] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -86.326894+0.003908j
[2025-09-06 05:51:01] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -86.372041-0.000231j
[2025-09-06 05:51:31] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -86.277150-0.000661j
[2025-09-06 05:52:02] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -86.352792-0.000692j
[2025-09-06 05:52:33] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -86.285083+0.004870j
[2025-09-06 05:53:04] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -86.222777-0.005146j
[2025-09-06 05:53:34] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -86.121865-0.004994j
[2025-09-06 05:54:05] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -86.094419-0.001724j
[2025-09-06 05:54:33] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -86.147034+0.000949j
[2025-09-06 05:55:02] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -85.939743+0.001916j
[2025-09-06 05:55:32] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -86.012130-0.002102j
[2025-09-06 05:56:03] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -85.980609-0.000589j
[2025-09-06 05:56:34] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -86.126396+0.004572j
[2025-09-06 05:57:04] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -86.181138-0.004075j
[2025-09-06 05:57:35] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -86.294616-0.001386j
[2025-09-06 05:58:06] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -86.160651-0.001475j
[2025-09-06 05:58:37] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -86.381527-0.003704j
[2025-09-06 05:59:07] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -86.146734-0.001294j
[2025-09-06 05:59:38] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -86.099812+0.001128j
[2025-09-06 06:00:09] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -85.989519+0.002713j
[2025-09-06 06:00:39] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -86.205061-0.002019j
[2025-09-06 06:01:10] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -86.317278+0.002747j
[2025-09-06 06:01:41] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -86.224551-0.000438j
[2025-09-06 06:02:11] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -86.185349-0.004177j
[2025-09-06 06:02:42] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -86.309372+0.003127j
[2025-09-06 06:03:13] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -86.331338-0.003041j
[2025-09-06 06:03:43] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -86.244146+0.002071j
[2025-09-06 06:04:14] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -86.235470+0.000515j
[2025-09-06 06:04:45] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -86.352166+0.003693j
[2025-09-06 06:05:15] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -86.337093+0.003042j
[2025-09-06 06:05:46] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -86.232581+0.001444j
[2025-09-06 06:06:17] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -86.179358-0.000609j
[2025-09-06 06:06:48] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -86.168050-0.000631j
[2025-09-06 06:07:18] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -86.324332+0.000767j
[2025-09-06 06:07:49] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -86.518986+0.000421j
[2025-09-06 06:08:20] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -86.272231-0.001494j
[2025-09-06 06:08:50] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -86.462850+0.000321j
[2025-09-06 06:09:21] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -86.210169-0.000653j
[2025-09-06 06:09:43] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -86.212231-0.003688j
[2025-09-06 06:10:07] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -86.254095-0.002871j
[2025-09-06 06:10:38] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -86.204813-0.002722j
[2025-09-06 06:10:38] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-06 06:11:09] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -86.196671-0.003359j
[2025-09-06 06:11:39] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -86.308027-0.003739j
[2025-09-06 06:12:10] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -86.064172+0.001519j
[2025-09-06 06:12:41] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -86.086982-0.002335j
[2025-09-06 06:13:11] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -86.330826-0.002214j
[2025-09-06 06:13:42] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -86.198610+0.006904j
[2025-09-06 06:14:13] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -86.200525+0.001529j
[2025-09-06 06:14:39] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -86.205133+0.000109j
[2025-09-06 06:15:10] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -86.218039+0.003223j
[2025-09-06 06:15:40] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -86.534693+0.001772j
[2025-09-06 06:16:11] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -86.354423-0.002037j
[2025-09-06 06:16:42] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -86.276526-0.003155j
[2025-09-06 06:17:12] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -86.357645+0.000215j
[2025-09-06 06:17:43] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -86.418644-0.000001j
[2025-09-06 06:18:14] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -86.428326-0.004253j
[2025-09-06 06:18:44] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -86.389651-0.000752j
[2025-09-06 06:19:15] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -86.229659-0.005305j
[2025-09-06 06:19:46] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -86.316122-0.004644j
[2025-09-06 06:20:16] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -86.150245+0.001881j
[2025-09-06 06:20:47] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -86.206603+0.001620j
[2025-09-06 06:21:18] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -86.315686+0.000028j
[2025-09-06 06:21:48] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -86.281536-0.002829j
[2025-09-06 06:22:19] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -86.406705-0.000865j
[2025-09-06 06:22:50] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -86.411781+0.003088j
[2025-09-06 06:23:20] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -86.308311+0.003586j
[2025-09-06 06:23:51] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -86.216870-0.002415j
[2025-09-06 06:24:22] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -86.248739+0.002574j
[2025-09-06 06:24:52] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -86.239887-0.003153j
[2025-09-06 06:25:23] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -86.250790-0.002041j
[2025-09-06 06:25:54] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -86.264368-0.004552j
[2025-09-06 06:26:24] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -86.228844+0.001532j
[2025-09-06 06:26:55] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -86.288241+0.001756j
[2025-09-06 06:27:26] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -86.222928+0.000764j
[2025-09-06 06:27:56] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -86.324818-0.000716j
[2025-09-06 06:28:27] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -86.116054+0.000494j
[2025-09-06 06:28:58] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -86.342272-0.001806j
[2025-09-06 06:29:27] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -86.292688+0.005384j
[2025-09-06 06:29:47] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -86.316577+0.002214j
[2025-09-06 06:30:15] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -86.344216-0.002023j
[2025-09-06 06:30:45] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -86.383281-0.003105j
[2025-09-06 06:31:16] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -86.417266-0.006030j
[2025-09-06 06:31:47] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -86.453094+0.002001j
[2025-09-06 06:32:18] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -86.313147+0.002749j
[2025-09-06 06:32:48] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -86.263402+0.000611j
[2025-09-06 06:33:19] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -86.369202-0.002102j
[2025-09-06 06:33:50] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -86.314792+0.003707j
[2025-09-06 06:34:20] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -86.287905+0.002080j
[2025-09-06 06:34:47] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -86.397710+0.000110j
[2025-09-06 06:35:18] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -86.137014-0.002327j
[2025-09-06 06:35:49] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -86.259954+0.005841j
[2025-09-06 06:36:19] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -86.306323+0.002919j
[2025-09-06 06:36:50] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -86.361528+0.002531j
[2025-09-06 06:37:20] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -86.217233-0.003062j
[2025-09-06 06:37:51] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -86.190516-0.004628j
[2025-09-06 06:38:21] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -86.255282+0.002000j
[2025-09-06 06:38:52] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -86.151554-0.003738j
[2025-09-06 06:39:22] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -86.460336+0.002308j
[2025-09-06 06:39:53] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -86.268258+0.004241j
[2025-09-06 06:40:24] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -86.110277-0.003600j
[2025-09-06 06:40:54] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -86.093592+0.000095j
[2025-09-06 06:41:25] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -86.049327-0.003095j
[2025-09-06 06:41:55] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -86.117819+0.001021j
[2025-09-06 06:42:26] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -86.164115+0.002288j
[2025-09-06 06:42:56] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -86.133840-0.000016j
[2025-09-06 06:43:27] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -86.142426+0.004158j
[2025-09-06 06:43:58] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -86.213625+0.001975j
[2025-09-06 06:44:28] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -86.077357-0.000002j
[2025-09-06 06:44:59] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -86.013231+0.003533j
[2025-09-06 06:45:29] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -86.162048-0.000362j
[2025-09-06 06:46:00] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -86.292831+0.001095j
[2025-09-06 06:46:30] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -86.235925-0.000402j
[2025-09-06 06:47:01] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -86.221541-0.001495j
[2025-09-06 06:47:32] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -86.103951+0.001515j
[2025-09-06 06:48:02] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -86.227532+0.000885j
[2025-09-06 06:48:33] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -86.088353+0.004220j
[2025-09-06 06:49:03] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -86.282262+0.000141j
[2025-09-06 06:49:29] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -86.290555-0.000600j
[2025-09-06 06:49:50] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -86.207008+0.006046j
[2025-09-06 06:50:19] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -86.166865-0.003683j
[2025-09-06 06:50:50] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -86.082395-0.000260j
[2025-09-06 06:51:21] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -86.144446+0.001587j
[2025-09-06 06:51:51] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -86.094550-0.006048j
[2025-09-06 06:52:22] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -86.155924-0.000913j
[2025-09-06 06:52:53] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -86.350447+0.001065j
[2025-09-06 06:53:23] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -86.242676+0.006285j
[2025-09-06 06:53:54] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -86.147819-0.001585j
[2025-09-06 06:54:22] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -86.200972-0.004522j
[2025-09-06 06:54:51] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -86.310781+0.001457j
[2025-09-06 06:55:21] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -86.425435+0.004669j
[2025-09-06 06:55:52] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -86.427875-0.002292j
[2025-09-06 06:56:22] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -86.329351+0.000519j
[2025-09-06 06:56:53] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -86.547432+0.005550j
[2025-09-06 06:57:23] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -86.396546-0.000784j
[2025-09-06 06:57:54] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -86.239843+0.006234j
[2025-09-06 06:58:25] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -86.313272+0.002092j
[2025-09-06 06:58:55] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -86.296392-0.002191j
[2025-09-06 06:59:26] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -86.376253+0.004273j
[2025-09-06 06:59:56] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -86.416605-0.001861j
[2025-09-06 07:00:27] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -86.308360-0.001460j
[2025-09-06 07:00:57] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -86.262316+0.002063j
[2025-09-06 07:01:28] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -86.375085+0.002117j
[2025-09-06 07:01:59] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -86.429720-0.007435j
[2025-09-06 07:02:29] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -86.495608-0.004092j
[2025-09-06 07:03:00] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -86.352516+0.003606j
[2025-09-06 07:03:30] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -86.232229-0.000564j
[2025-09-06 07:03:30] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-06 07:04:01] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -86.380465+0.002193j
[2025-09-06 07:04:31] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -86.215187+0.001567j
[2025-09-06 07:05:02] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -86.220164-0.005702j
[2025-09-06 07:05:33] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -86.336562+0.003276j
[2025-09-06 07:06:03] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -86.156494+0.003348j
[2025-09-06 07:06:34] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -86.413852-0.003493j
[2025-09-06 07:07:04] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -86.380532-0.002478j
[2025-09-06 07:07:35] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -86.316986+0.000809j
[2025-09-06 07:08:06] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -86.408010-0.003545j
[2025-09-06 07:08:36] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -86.329812+0.005709j
[2025-09-06 07:09:07] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -86.293487+0.002872j
[2025-09-06 07:09:30] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -86.200920-0.001710j
[2025-09-06 07:09:53] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -86.317138+0.000385j
[2025-09-06 07:10:23] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -86.285536-0.002722j
[2025-09-06 07:10:54] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -86.238965-0.003618j
[2025-09-06 07:11:25] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -86.112653-0.000400j
[2025-09-06 07:11:55] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -86.179405-0.000286j
[2025-09-06 07:12:26] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -86.160564+0.000358j
[2025-09-06 07:12:57] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -86.101277+0.001715j
[2025-09-06 07:13:28] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -86.118135-0.001182j
[2025-09-06 07:13:58] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -86.187947-0.000379j
[2025-09-06 07:14:25] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -86.028508+0.000369j
[2025-09-06 07:14:55] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -86.122988-0.003346j
[2025-09-06 07:15:26] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -86.177718-0.004679j
[2025-09-06 07:15:56] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -86.188030+0.005109j
[2025-09-06 07:16:27] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -86.158913-0.000115j
[2025-09-06 07:16:58] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -86.011057+0.000694j
[2025-09-06 07:17:28] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -86.327269-0.000103j
[2025-09-06 07:17:59] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -86.320252+0.002242j
[2025-09-06 07:18:30] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -85.990084+0.006969j
[2025-09-06 07:19:00] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -86.301801-0.001527j
[2025-09-06 07:19:31] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -86.223281+0.003035j
[2025-09-06 07:20:02] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -86.338407+0.000735j
[2025-09-06 07:20:32] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -86.334619+0.002465j
[2025-09-06 07:21:03] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -86.276944+0.001204j
[2025-09-06 07:21:34] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -86.304372+0.003003j
[2025-09-06 07:22:05] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -86.361788+0.002874j
[2025-09-06 07:22:35] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -86.382468+0.000879j
[2025-09-06 07:23:06] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -86.277593+0.003249j
[2025-09-06 07:23:37] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -86.319917-0.002817j
[2025-09-06 07:24:07] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -86.341609+0.001402j
[2025-09-06 07:24:38] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -86.183722-0.001573j
[2025-09-06 07:25:09] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -86.178033-0.001177j
[2025-09-06 07:25:39] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -86.123120+0.002073j
[2025-09-06 07:26:10] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -86.128542+0.001834j
[2025-09-06 07:26:41] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -86.121712-0.000748j
[2025-09-06 07:27:11] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -86.033142+0.001296j
[2025-09-06 07:27:42] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -86.051356+0.000676j
[2025-09-06 07:28:13] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -86.185179+0.001041j
[2025-09-06 07:28:43] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -86.114849+0.002306j
[2025-09-06 07:29:14] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -86.216050+0.002507j
[2025-09-06 07:29:35] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -86.347229+0.003960j
[2025-09-06 07:30:01] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -86.252082+0.003911j
[2025-09-06 07:30:31] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -86.216887+0.000527j
[2025-09-06 07:31:02] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -86.126600+0.000667j
[2025-09-06 07:31:33] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -86.257716-0.001113j
[2025-09-06 07:32:04] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -86.231894+0.000695j
[2025-09-06 07:32:34] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -86.196260+0.004215j
[2025-09-06 07:33:05] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -86.182297+0.003468j
[2025-09-06 07:33:36] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -86.075347-0.001490j
[2025-09-06 07:34:06] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -86.163793-0.004960j
[2025-09-06 07:34:32] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -86.220124-0.001344j
[2025-09-06 07:35:03] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -86.101574+0.002030j
[2025-09-06 07:35:34] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -86.198070+0.004191j
[2025-09-06 07:36:04] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -86.213064+0.007403j
[2025-09-06 07:36:35] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -86.257035-0.001879j
[2025-09-06 07:37:06] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -86.106257+0.002478j
[2025-09-06 07:37:36] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -86.177742+0.001928j
[2025-09-06 07:38:07] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -86.288147-0.001198j
[2025-09-06 07:38:38] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -86.368171+0.005171j
[2025-09-06 07:39:08] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -86.312819-0.002418j
[2025-09-06 07:39:39] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -86.311279+0.003907j
[2025-09-06 07:40:10] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -86.125428-0.000488j
[2025-09-06 07:40:40] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -86.252463-0.003854j
[2025-09-06 07:41:11] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -86.324994-0.004466j
[2025-09-06 07:41:42] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -86.140964+0.000012j
[2025-09-06 07:42:12] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -86.271534-0.000032j
[2025-09-06 07:42:43] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -86.145178+0.002702j
[2025-09-06 07:43:14] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -86.205927+0.002135j
[2025-09-06 07:43:44] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -86.133024+0.002092j
[2025-09-06 07:44:15] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -86.178005+0.001161j
[2025-09-06 07:44:46] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -86.062215-0.002018j
[2025-09-06 07:45:17] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -86.128895-0.000749j
[2025-09-06 07:45:47] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -86.064740+0.001903j
[2025-09-06 07:46:18] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -86.128631+0.001869j
[2025-09-06 07:46:49] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -86.068758-0.002179j
[2025-09-06 07:47:19] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -86.188729+0.002723j
[2025-09-06 07:47:50] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -86.176003+0.012653j
[2025-09-06 07:48:21] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -86.220412+0.001281j
[2025-09-06 07:48:51] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -86.000952+0.000108j
[2025-09-06 07:49:18] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -86.227241-0.007583j
[2025-09-06 07:49:38] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -86.256846+0.007153j
[2025-09-06 07:50:07] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -86.234123+0.002991j
[2025-09-06 07:50:38] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -86.370976+0.000408j
[2025-09-06 07:51:09] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -86.251051-0.000388j
[2025-09-06 07:51:39] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -86.184678+0.002032j
[2025-09-06 07:52:10] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -86.376009+0.003692j
[2025-09-06 07:52:41] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -86.319659-0.002086j
[2025-09-06 07:53:11] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -86.357353+0.000544j
[2025-09-06 07:53:42] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -86.209144-0.003109j
[2025-09-06 07:54:11] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -86.314419+0.000110j
[2025-09-06 07:54:39] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -86.316079+0.000561j
[2025-09-06 07:55:09] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -86.401768+0.003233j
[2025-09-06 07:55:40] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -86.526048-0.001463j
[2025-09-06 07:56:10] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -86.399633+0.002043j
[2025-09-06 07:56:10] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-06 07:56:10] ✅ Training completed | Restarts: 2
[2025-09-06 07:56:10] ============================================================
[2025-09-06 07:56:10] Training completed | Runtime: 31660.6s
[2025-09-06 07:56:23] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-06 07:56:23] ============================================================
