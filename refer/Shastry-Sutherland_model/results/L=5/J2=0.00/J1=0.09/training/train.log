[2025-09-07 00:35:47] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.08/training/checkpoints/final_GCNN.pkl
[2025-09-07 00:35:47]   - 迭代次数: final
[2025-09-07 00:35:47]   - 能量: -87.457471-0.002413j ± 0.109918
[2025-09-07 00:35:47]   - 时间戳: 2025-09-07T00:35:37.873847+08:00
[2025-09-07 00:36:00] ✓ 变分状态参数已从checkpoint恢复
[2025-09-07 00:36:00] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-07 00:36:00] ==================================================
[2025-09-07 00:36:00] GCNN for Shastry-Sutherland Model
[2025-09-07 00:36:00] ==================================================
[2025-09-07 00:36:00] System parameters:
[2025-09-07 00:36:00]   - System size: L=5, N=100
[2025-09-07 00:36:00]   - System parameters: J1=0.09, J2=0.0, Q=1.0
[2025-09-07 00:36:00] --------------------------------------------------
[2025-09-07 00:36:00] Model parameters:
[2025-09-07 00:36:00]   - Number of layers = 4
[2025-09-07 00:36:00]   - Number of features = 4
[2025-09-07 00:36:00]   - Total parameters = 19628
[2025-09-07 00:36:00] --------------------------------------------------
[2025-09-07 00:36:00] Training parameters:
[2025-09-07 00:36:00]   - Learning rate: 0.015
[2025-09-07 00:36:00]   - Total iterations: 1050
[2025-09-07 00:36:00]   - Annealing cycles: 3
[2025-09-07 00:36:00]   - Initial period: 150
[2025-09-07 00:36:00]   - Period multiplier: 2.0
[2025-09-07 00:36:00]   - Temperature range: 0.0-1.0
[2025-09-07 00:36:00]   - Samples: 4096
[2025-09-07 00:36:00]   - Discarded samples: 0
[2025-09-07 00:36:00]   - Chunk size: 2048
[2025-09-07 00:36:00]   - Diagonal shift: 0.2
[2025-09-07 00:36:00]   - Gradient clipping: 1.0
[2025-09-07 00:36:00]   - Checkpoint enabled: interval=105
[2025-09-07 00:36:00]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.09/training/checkpoints
[2025-09-07 00:36:00] --------------------------------------------------
[2025-09-07 00:36:00] Device status:
[2025-09-07 00:36:00]   - Devices model: NVIDIA H200 NVL
[2025-09-07 00:36:00]   - Number of devices: 1
[2025-09-07 00:36:00]   - Sharding: True
[2025-09-07 00:36:00] ============================================================
[2025-09-07 00:36:52] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -88.070967-0.009041j
[2025-09-07 00:37:30] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -88.021901-0.001560j
[2025-09-07 00:37:50] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -88.080378+0.000755j
[2025-09-07 00:38:11] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -88.167310+0.002552j
[2025-09-07 00:38:31] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -88.235233+0.003397j
[2025-09-07 00:38:52] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -88.030369+0.004231j
[2025-09-07 00:39:12] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -87.929246-0.000821j
[2025-09-07 00:39:33] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -88.039686-0.000158j
[2025-09-07 00:39:53] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -88.021813-0.002085j
[2025-09-07 00:40:14] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -88.216835-0.002801j
[2025-09-07 00:40:34] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -88.256501+0.003694j
[2025-09-07 00:40:55] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -88.228480-0.002822j
[2025-09-07 00:41:15] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -88.374861-0.001552j
[2025-09-07 00:41:36] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -88.356317+0.000979j
[2025-09-07 00:41:56] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -88.130780-0.000291j
[2025-09-07 00:42:17] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -88.105170-0.002268j
[2025-09-07 00:42:37] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -88.148390+0.000687j
[2025-09-07 00:42:51] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -87.988516-0.001197j
[2025-09-07 00:43:00] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -87.950490+0.004038j
[2025-09-07 00:43:10] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -88.127312+0.003244j
[2025-09-07 00:43:26] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -87.993417-0.006456j
[2025-09-07 00:43:46] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -87.963147-0.002188j
[2025-09-07 00:44:07] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -88.059173-0.000035j
[2025-09-07 00:44:28] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -88.199918+0.000575j
[2025-09-07 00:44:48] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -88.178259-0.001733j
[2025-09-07 00:45:09] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -88.098402-0.000810j
[2025-09-07 00:45:30] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -87.811493+0.002848j
[2025-09-07 00:45:50] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -87.892597-0.003622j
[2025-09-07 00:46:09] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -87.894163-0.004224j
[2025-09-07 00:46:21] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -87.914152+0.003075j
[2025-09-07 00:46:40] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -88.168186-0.002788j
[2025-09-07 00:47:01] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -88.005003+0.002948j
[2025-09-07 00:47:21] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -88.067731-0.001607j
[2025-09-07 00:47:42] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -88.091069-0.000372j
[2025-09-07 00:48:03] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -88.011177+0.000048j
[2025-09-07 00:48:23] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -88.121017-0.001634j
[2025-09-07 00:48:44] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -87.953935-0.000128j
[2025-09-07 00:49:04] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -87.951026+0.002293j
[2025-09-07 00:49:25] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -87.953937-0.001002j
[2025-09-07 00:49:46] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -88.104225+0.002135j
[2025-09-07 00:50:06] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -88.145613+0.001002j
[2025-09-07 00:50:27] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -88.150369+0.005042j
[2025-09-07 00:50:47] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -88.169052+0.000971j
[2025-09-07 00:51:08] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -88.177840+0.001446j
[2025-09-07 00:51:29] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -88.269491-0.001768j
[2025-09-07 00:51:49] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -88.122863+0.001497j
[2025-09-07 00:52:10] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -88.147163-0.000819j
[2025-09-07 00:52:31] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -88.078002-0.001446j
[2025-09-07 00:52:51] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -88.045422-0.001352j
[2025-09-07 00:53:12] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -87.962516-0.000406j
[2025-09-07 00:53:33] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -87.901333-0.000636j
[2025-09-07 00:53:53] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -87.956265-0.001593j
[2025-09-07 00:54:14] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -88.134851-0.001658j
[2025-09-07 00:54:34] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -88.089443+0.000051j
[2025-09-07 00:54:55] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -87.993229+0.002525j
[2025-09-07 00:55:16] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -88.206058+0.000723j
[2025-09-07 00:55:36] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -88.314742+0.000078j
[2025-09-07 00:55:57] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -88.194592-0.001112j
[2025-09-07 00:56:16] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -88.237361+0.003861j
[2025-09-07 00:56:25] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -88.233953-0.002181j
[2025-09-07 00:56:35] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -88.235101-0.003001j
[2025-09-07 00:56:48] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -88.253345-0.000965j
[2025-09-07 00:57:09] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -88.206726+0.001221j
[2025-09-07 00:57:30] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -88.182116-0.001909j
[2025-09-07 00:57:50] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -88.191967+0.008102j
[2025-09-07 00:58:11] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -88.052308+0.002143j
[2025-09-07 00:58:32] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -88.013639+0.011508j
[2025-09-07 00:58:52] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -88.074760+0.001841j
[2025-09-07 00:59:13] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -87.940540-0.000425j
[2025-09-07 00:59:33] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -87.903560-0.000741j
[2025-09-07 00:59:48] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -87.863796+0.000087j
[2025-09-07 01:00:03] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -87.971334-0.000817j
[2025-09-07 01:00:23] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -87.958448+0.003252j
[2025-09-07 01:00:44] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -88.305857+0.001567j
[2025-09-07 01:01:04] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -88.255626-0.002086j
[2025-09-07 01:01:25] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -88.258385-0.003983j
[2025-09-07 01:01:45] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -88.383927-0.003811j
[2025-09-07 01:02:06] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -88.320832+0.000823j
[2025-09-07 01:02:26] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -88.220497+0.002925j
[2025-09-07 01:02:47] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -88.378093+0.001149j
[2025-09-07 01:03:07] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -88.143138-0.000042j
[2025-09-07 01:03:28] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -88.264613-0.004506j
[2025-09-07 01:03:48] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -88.215150-0.003310j
[2025-09-07 01:04:09] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -88.141344-0.000591j
[2025-09-07 01:04:29] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -88.071863+0.002937j
[2025-09-07 01:04:50] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -88.051038-0.002508j
[2025-09-07 01:05:10] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -88.049342-0.001676j
[2025-09-07 01:05:31] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -88.043736-0.000540j
[2025-09-07 01:05:51] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -88.048272-0.000670j
[2025-09-07 01:06:12] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -88.185027-0.003771j
[2025-09-07 01:06:32] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -88.235886-0.004375j
[2025-09-07 01:06:53] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -88.200960+0.000803j
[2025-09-07 01:07:13] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -88.110064-0.002031j
[2025-09-07 01:07:34] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -88.037383-0.006394j
[2025-09-07 01:07:54] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -88.164682-0.001483j
[2025-09-07 01:08:15] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -87.960007+0.001269j
[2025-09-07 01:08:35] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -88.133571-0.000543j
[2025-09-07 01:08:56] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -88.209313-0.003370j
[2025-09-07 01:09:16] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -88.073434-0.003821j
[2025-09-07 01:09:37] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -88.084980-0.001075j
[2025-09-07 01:09:48] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -87.997145-0.000503j
[2025-09-07 01:09:58] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -87.967822+0.000149j
[2025-09-07 01:10:07] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -87.977997+0.001660j
[2025-09-07 01:10:25] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -88.223220+0.002359j
[2025-09-07 01:10:46] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -88.085116-0.003162j
[2025-09-07 01:10:46] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-07 01:11:07] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -88.223740+0.002503j
[2025-09-07 01:11:27] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -88.134730-0.001299j
[2025-09-07 01:11:48] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -88.099466-0.002343j
[2025-09-07 01:12:09] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -88.259498-0.001201j
[2025-09-07 01:12:29] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -88.048742-0.002872j
[2025-09-07 01:12:50] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -88.175433-0.000336j
[2025-09-07 01:13:07] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -88.219019+0.004435j
[2025-09-07 01:13:19] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -88.264242-0.002281j
[2025-09-07 01:13:40] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -87.965223+0.001151j
[2025-09-07 01:14:01] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -87.956911-0.004670j
[2025-09-07 01:14:21] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -87.917556+0.001054j
[2025-09-07 01:14:42] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -87.934019+0.000644j
[2025-09-07 01:15:03] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -88.061490-0.004695j
[2025-09-07 01:15:23] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -88.171220-0.001900j
[2025-09-07 01:15:44] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -88.004079+0.001325j
[2025-09-07 01:16:04] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -88.102292-0.000236j
[2025-09-07 01:16:25] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -88.211788-0.003611j
[2025-09-07 01:16:45] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -87.994011+0.004668j
[2025-09-07 01:17:06] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -88.112868-0.001834j
[2025-09-07 01:17:27] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -88.009935+0.000305j
[2025-09-07 01:17:47] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -88.015087+0.000801j
[2025-09-07 01:18:08] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -88.209252+0.001688j
[2025-09-07 01:18:28] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -88.171078-0.000891j
[2025-09-07 01:18:49] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -88.256491+0.002380j
[2025-09-07 01:19:10] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -88.209811-0.001117j
[2025-09-07 01:19:30] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -88.285521-0.002486j
[2025-09-07 01:19:51] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -88.296247+0.000751j
[2025-09-07 01:20:12] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -88.255276+0.002107j
[2025-09-07 01:20:32] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -88.279672-0.003701j
[2025-09-07 01:20:53] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -88.132699+0.001043j
[2025-09-07 01:21:13] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -88.271410+0.004332j
[2025-09-07 01:21:34] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -88.056621+0.001068j
[2025-09-07 01:21:55] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -88.145278+0.001346j
[2025-09-07 01:22:15] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -88.197039+0.001242j
[2025-09-07 01:22:36] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -88.199829+0.000886j
[2025-09-07 01:22:56] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -88.098991+0.004478j
[2025-09-07 01:23:13] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -88.096049+0.003958j
[2025-09-07 01:23:23] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -88.052210-0.000712j
[2025-09-07 01:23:32] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -88.000559+0.000827j
[2025-09-07 01:23:47] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -88.027244+0.002660j
[2025-09-07 01:24:07] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -88.110318-0.002640j
[2025-09-07 01:24:28] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -88.063047+0.003521j
[2025-09-07 01:24:49] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -88.126902+0.000341j
[2025-09-07 01:25:09] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -88.129084+0.002888j
[2025-09-07 01:25:30] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -88.098245+0.000212j
[2025-09-07 01:25:30] RESTART #1 | Period: 300
[2025-09-07 01:25:51] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -88.126262-0.002697j
[2025-09-07 01:26:11] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -88.211263-0.001570j
[2025-09-07 01:26:32] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -88.163308+0.000784j
[2025-09-07 01:26:45] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -88.204805+0.002071j
[2025-09-07 01:27:01] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -88.025591+0.002920j
[2025-09-07 01:27:22] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -88.119154-0.000782j
[2025-09-07 01:27:43] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -88.080002+0.002688j
[2025-09-07 01:28:03] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -88.008941+0.001236j
[2025-09-07 01:28:24] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -87.907915-0.003801j
[2025-09-07 01:28:44] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -87.969249+0.002012j
[2025-09-07 01:29:05] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -88.104942-0.001668j
[2025-09-07 01:29:25] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -87.956206+0.015341j
[2025-09-07 01:29:45] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -88.080873+0.000008j
[2025-09-07 01:30:06] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -87.957321-0.004410j
[2025-09-07 01:30:26] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -87.994320-0.002860j
[2025-09-07 01:30:47] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -88.111076-0.002531j
[2025-09-07 01:31:07] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -88.121910-0.000463j
[2025-09-07 01:31:28] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -88.127611-0.000905j
[2025-09-07 01:31:48] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -88.004853-0.002199j
[2025-09-07 01:32:09] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -88.176940+0.002433j
[2025-09-07 01:32:29] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -87.960215-0.002969j
[2025-09-07 01:32:50] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -88.040869+0.000374j
[2025-09-07 01:33:10] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -88.048230-0.001854j
[2025-09-07 01:33:31] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -87.826538+0.000364j
[2025-09-07 01:33:51] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -87.896772-0.002259j
[2025-09-07 01:34:12] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -88.037730-0.000060j
[2025-09-07 01:34:32] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -88.115692+0.002164j
[2025-09-07 01:34:53] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -88.045902-0.003732j
[2025-09-07 01:35:13] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -88.015409-0.004730j
[2025-09-07 01:35:34] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -87.908061+0.000574j
[2025-09-07 01:35:54] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -88.120514+0.005290j
[2025-09-07 01:36:15] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -88.062932-0.001989j
[2025-09-07 01:36:35] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -87.915240+0.003192j
[2025-09-07 01:36:45] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -87.960691-0.002550j
[2025-09-07 01:36:55] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -88.128226+0.000571j
[2025-09-07 01:37:06] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -87.927706-0.000660j
[2025-09-07 01:37:27] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -88.047823-0.002143j
[2025-09-07 01:37:48] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -88.056254-0.003269j
[2025-09-07 01:38:08] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -87.879280-0.000640j
[2025-09-07 01:38:29] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -88.036273-0.000573j
[2025-09-07 01:38:49] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -88.117443-0.001533j
[2025-09-07 01:39:10] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -87.917685-0.000211j
[2025-09-07 01:39:31] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -87.819405-0.000649j
[2025-09-07 01:39:51] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -87.808690+0.003747j
[2025-09-07 01:40:06] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -88.005974+0.001688j
[2025-09-07 01:40:21] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -88.154593-0.003239j
[2025-09-07 01:40:41] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -88.109450+0.000076j
[2025-09-07 01:41:02] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -88.224315-0.003274j
[2025-09-07 01:41:22] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -88.038347-0.000982j
[2025-09-07 01:41:43] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -88.105781+0.001746j
[2025-09-07 01:42:03] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -88.193093-0.001718j
[2025-09-07 01:42:24] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -88.263558+0.002560j
[2025-09-07 01:42:44] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -88.334486-0.001137j
[2025-09-07 01:43:05] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -88.217339-0.002008j
[2025-09-07 01:43:25] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -88.103388+0.001704j
[2025-09-07 01:43:45] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -88.005893-0.002756j
[2025-09-07 01:44:06] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -88.025633-0.001860j
[2025-09-07 01:44:26] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -88.133795-0.000558j
[2025-09-07 01:44:47] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -87.977587+0.001355j
[2025-09-07 01:45:07] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -88.125309-0.001006j
[2025-09-07 01:45:07] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-07 01:45:28] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -88.280325-0.001752j
[2025-09-07 01:45:48] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -88.288473-0.003621j
[2025-09-07 01:46:09] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -88.247470+0.004129j
[2025-09-07 01:46:29] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -88.035296+0.001386j
[2025-09-07 01:46:50] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -88.207693-0.001343j
[2025-09-07 01:47:10] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -88.164308+0.000307j
[2025-09-07 01:47:31] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -88.152463-0.002995j
[2025-09-07 01:47:51] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -88.145901+0.002710j
[2025-09-07 01:48:12] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -88.193038-0.003738j
[2025-09-07 01:48:32] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -88.117564+0.000442j
[2025-09-07 01:48:53] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -88.167304+0.002568j
[2025-09-07 01:49:13] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -88.144134-0.001152j
[2025-09-07 01:49:34] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -88.117053+0.002290j
[2025-09-07 01:49:54] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -88.049376-0.000490j
[2025-09-07 01:50:08] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -88.116709-0.001478j
[2025-09-07 01:50:18] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -88.044525-0.001045j
[2025-09-07 01:50:27] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -88.180806-0.000588j
[2025-09-07 01:50:46] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -87.884664-0.002328j
[2025-09-07 01:51:06] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -88.031198+0.002523j
[2025-09-07 01:51:27] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -87.946759+0.000954j
[2025-09-07 01:51:47] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -87.972543+0.000367j
[2025-09-07 01:52:08] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -88.080472-0.001957j
[2025-09-07 01:52:29] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -87.954433-0.001823j
[2025-09-07 01:52:49] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -88.187820-0.001122j
[2025-09-07 01:53:10] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -87.968707+0.000339j
[2025-09-07 01:53:27] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -88.050946+0.002432j
[2025-09-07 01:53:39] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -88.036055+0.001709j
[2025-09-07 01:54:00] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -88.016840+0.002910j
[2025-09-07 01:54:20] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -87.987907-0.001673j
[2025-09-07 01:54:41] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -88.110478+0.001523j
[2025-09-07 01:55:01] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -88.079668-0.000826j
[2025-09-07 01:55:22] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -88.011382-0.002980j
[2025-09-07 01:55:42] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -88.197535-0.000440j
[2025-09-07 01:56:03] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -88.138970-0.003489j
[2025-09-07 01:56:23] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -88.030476+0.001002j
[2025-09-07 01:56:44] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -88.143579-0.003138j
[2025-09-07 01:57:04] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -88.086488-0.001812j
[2025-09-07 01:57:25] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -88.036944-0.002556j
[2025-09-07 01:57:45] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -88.106719-0.001071j
[2025-09-07 01:58:06] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -88.123826+0.000519j
[2025-09-07 01:58:26] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -88.166487-0.000744j
[2025-09-07 01:58:47] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -88.280303+0.000930j
[2025-09-07 01:59:07] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -88.305379-0.001638j
[2025-09-07 01:59:28] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -88.284780-0.000104j
[2025-09-07 01:59:48] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -88.166688-0.001248j
[2025-09-07 02:00:09] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -88.333727+0.000943j
[2025-09-07 02:00:30] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -88.271331-0.000660j
[2025-09-07 02:00:50] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -88.297586+0.001662j
[2025-09-07 02:01:11] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -88.201611-0.000284j
[2025-09-07 02:01:31] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -88.242616+0.001057j
[2025-09-07 02:01:52] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -88.177501-0.000229j
[2025-09-07 02:02:12] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -88.128809+0.003250j
[2025-09-07 02:02:33] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -88.165314-0.003198j
[2025-09-07 02:02:53] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -88.293458-0.001666j
[2025-09-07 02:03:14] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -88.309414-0.000242j
[2025-09-07 02:03:31] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -88.358316-0.000218j
[2025-09-07 02:03:40] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -88.308192-0.004413j
[2025-09-07 02:03:50] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -88.309696+0.001005j
[2025-09-07 02:04:02] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -88.312771-0.001474j
[2025-09-07 02:04:23] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -88.307596+0.002550j
[2025-09-07 02:04:43] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -88.286862-0.000802j
[2025-09-07 02:05:04] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -88.221038-0.001548j
[2025-09-07 02:05:25] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -88.278377-0.002781j
[2025-09-07 02:05:45] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -88.055606-0.003008j
[2025-09-07 02:06:06] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -88.233541-0.005168j
[2025-09-07 02:06:27] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -88.267321+0.002016j
[2025-09-07 02:06:47] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -88.203237+0.002752j
[2025-09-07 02:07:02] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -88.204415+0.003741j
[2025-09-07 02:07:16] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -88.208935+0.001366j
[2025-09-07 02:07:37] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -88.197352+0.002932j
[2025-09-07 02:07:57] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -88.115764+0.003142j
[2025-09-07 02:08:18] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -88.091373-0.003178j
[2025-09-07 02:08:38] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -88.075564-0.000310j
[2025-09-07 02:08:59] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -88.199232-0.000105j
[2025-09-07 02:09:19] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -88.178512+0.000575j
[2025-09-07 02:09:40] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -88.236482+0.003580j
[2025-09-07 02:10:00] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -88.150967-0.000420j
[2025-09-07 02:10:21] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -88.152043+0.002396j
[2025-09-07 02:10:41] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -88.205584+0.003171j
[2025-09-07 02:11:02] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -88.115197-0.000097j
[2025-09-07 02:11:22] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -88.317386-0.003810j
[2025-09-07 02:11:43] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -88.229465-0.002138j
[2025-09-07 02:12:03] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -88.218727+0.003026j
[2025-09-07 02:12:24] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -88.099353-0.000716j
[2025-09-07 02:12:44] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -88.170446+0.000158j
[2025-09-07 02:13:05] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -88.065847+0.002068j
[2025-09-07 02:13:25] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -88.124186-0.002952j
[2025-09-07 02:13:46] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -88.112957-0.001586j
[2025-09-07 02:14:06] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -88.023103-0.000560j
[2025-09-07 02:14:27] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -88.111854-0.001765j
[2025-09-07 02:14:47] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -88.173124+0.003909j
[2025-09-07 02:15:08] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -88.134415-0.003377j
[2025-09-07 02:15:28] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -88.046476+0.000909j
[2025-09-07 02:15:49] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -88.103091-0.004834j
[2025-09-07 02:16:09] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -88.218105-0.004328j
[2025-09-07 02:16:30] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -88.234085-0.000322j
[2025-09-07 02:16:50] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -88.129794-0.000517j
[2025-09-07 02:17:02] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -87.941547-0.000980j
[2025-09-07 02:17:12] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -88.027010-0.003379j
[2025-09-07 02:17:21] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -88.239406+0.003843j
[2025-09-07 02:17:42] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -87.970501+0.001952j
[2025-09-07 02:18:02] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -88.099428+0.000537j
[2025-09-07 02:18:23] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -88.234969-0.001483j
[2025-09-07 02:18:43] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -88.167577+0.000219j
[2025-09-07 02:19:04] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -88.207636-0.001626j
[2025-09-07 02:19:04] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-07 02:19:25] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -88.135687+0.006311j
[2025-09-07 02:19:45] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -88.226755-0.002585j
[2025-09-07 02:20:06] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -88.117652+0.003892j
[2025-09-07 02:20:21] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -88.256764+0.000138j
[2025-09-07 02:20:35] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -88.245939-0.001925j
[2025-09-07 02:20:56] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -88.066367-0.000294j
[2025-09-07 02:21:17] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -88.235014-0.002329j
[2025-09-07 02:21:37] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -87.949538-0.000185j
[2025-09-07 02:21:58] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -87.989797-0.003380j
[2025-09-07 02:22:18] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -88.173630-0.003954j
[2025-09-07 02:22:39] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -88.104467+0.003896j
[2025-09-07 02:23:00] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -88.263661+0.001888j
[2025-09-07 02:23:20] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -88.225644-0.001468j
[2025-09-07 02:23:41] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -88.193227-0.001892j
[2025-09-07 02:24:01] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -87.942664-0.001781j
[2025-09-07 02:24:22] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -88.089530-0.001774j
[2025-09-07 02:24:42] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -88.069327-0.001019j
[2025-09-07 02:25:03] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -88.150315+0.002452j
[2025-09-07 02:25:24] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -88.165725-0.007406j
[2025-09-07 02:25:44] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -88.255947-0.003248j
[2025-09-07 02:26:05] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -88.089968+0.000531j
[2025-09-07 02:26:25] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -88.080033-0.001616j
[2025-09-07 02:26:46] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -87.997369-0.003173j
[2025-09-07 02:27:06] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -88.112658-0.001335j
[2025-09-07 02:27:27] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -88.121127-0.002369j
[2025-09-07 02:27:48] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -88.150474+0.002878j
[2025-09-07 02:28:08] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -88.203031-0.000535j
[2025-09-07 02:28:29] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -88.144297-0.005658j
[2025-09-07 02:28:49] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -88.072649-0.003246j
[2025-09-07 02:29:10] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -88.053703-0.004014j
[2025-09-07 02:29:31] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -88.204436+0.003107j
[2025-09-07 02:29:51] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -88.180165-0.001564j
[2025-09-07 02:30:12] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -88.085959+0.002126j
[2025-09-07 02:30:27] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -88.021356+0.000540j
[2025-09-07 02:30:36] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -88.346276+0.000905j
[2025-09-07 02:30:46] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -88.066444+0.001740j
[2025-09-07 02:31:01] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -88.133616-0.000503j
[2025-09-07 02:31:22] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -88.074421+0.000137j
[2025-09-07 02:31:43] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -88.163001-0.001569j
[2025-09-07 02:32:03] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -88.175234-0.000128j
[2025-09-07 02:32:24] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -88.283483-0.002324j
[2025-09-07 02:32:44] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -88.092936-0.001724j
[2025-09-07 02:33:05] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -88.043720+0.003276j
[2025-09-07 02:33:26] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -88.101512+0.002009j
[2025-09-07 02:33:45] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -87.985371+0.002743j
[2025-09-07 02:33:58] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -88.026079-0.000108j
[2025-09-07 02:34:16] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -88.239245+0.001233j
[2025-09-07 02:34:36] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -88.192343+0.000171j
[2025-09-07 02:34:57] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -88.075250+0.002536j
[2025-09-07 02:35:17] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -88.270620+0.005873j
[2025-09-07 02:35:38] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -88.147401-0.002048j
[2025-09-07 02:35:59] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -88.227676+0.002421j
[2025-09-07 02:36:19] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -88.242828+0.001873j
[2025-09-07 02:36:40] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -88.243755-0.002733j
[2025-09-07 02:37:00] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -88.344363-0.000517j
[2025-09-07 02:37:21] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -88.475355-0.002044j
[2025-09-07 02:37:41] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -88.263068-0.001592j
[2025-09-07 02:38:02] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -88.367068-0.003908j
[2025-09-07 02:38:23] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -88.298438+0.002188j
[2025-09-07 02:38:43] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -88.304599-0.002296j
[2025-09-07 02:39:04] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -88.310078-0.000669j
[2025-09-07 02:39:24] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -88.321570+0.006492j
[2025-09-07 02:39:45] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -88.239442+0.001414j
[2025-09-07 02:40:06] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -88.113438-0.002645j
[2025-09-07 02:40:26] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -88.015671+0.000788j
[2025-09-07 02:40:47] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -88.022456+0.002220j
[2025-09-07 02:41:07] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -88.092912-0.002224j
[2025-09-07 02:41:28] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -88.107424+0.000733j
[2025-09-07 02:41:49] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -88.171315-0.001758j
[2025-09-07 02:42:09] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -88.155037-0.000294j
[2025-09-07 02:42:30] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -88.088594+0.000271j
[2025-09-07 02:42:50] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -87.977249-0.000377j
[2025-09-07 02:43:11] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -88.000130-0.002448j
[2025-09-07 02:43:32] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -88.214149+0.003959j
[2025-09-07 02:43:51] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -88.138409+0.000009j
[2025-09-07 02:44:01] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -88.045402+0.000875j
[2025-09-07 02:44:11] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -88.152820-0.002661j
[2025-09-07 02:44:20] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -88.157765+0.003241j
[2025-09-07 02:44:41] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -88.295552-0.000715j
[2025-09-07 02:45:02] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -88.290496+0.000034j
[2025-09-07 02:45:22] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -88.222728+0.003054j
[2025-09-07 02:45:43] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -88.246483-0.002809j
[2025-09-07 02:46:04] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -88.200704+0.006001j
[2025-09-07 02:46:24] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -88.185386+0.000989j
[2025-09-07 02:46:45] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -88.131424-0.002205j
[2025-09-07 02:47:05] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -88.334482-0.001430j
[2025-09-07 02:47:20] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -88.287353+0.000229j
[2025-09-07 02:47:35] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -88.291738+0.002031j
[2025-09-07 02:47:55] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -88.216386-0.000123j
[2025-09-07 02:48:16] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -88.155762-0.001843j
[2025-09-07 02:48:36] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -88.009386-0.001062j
[2025-09-07 02:48:57] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -88.142482+0.000894j
[2025-09-07 02:49:17] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -88.172257-0.003121j
[2025-09-07 02:49:37] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -87.980895-0.001160j
[2025-09-07 02:49:58] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -88.058309+0.000196j
[2025-09-07 02:50:18] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -87.894213+0.000403j
[2025-09-07 02:50:39] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -88.002302+0.000274j
[2025-09-07 02:50:59] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -88.042026+0.004932j
[2025-09-07 02:51:20] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -88.062894+0.000649j
[2025-09-07 02:51:40] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -88.226810-0.000989j
[2025-09-07 02:52:01] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -88.099085-0.000134j
[2025-09-07 02:52:21] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -88.014575+0.000972j
[2025-09-07 02:52:42] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -88.072898-0.002620j
[2025-09-07 02:53:02] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -88.053708-0.004472j
[2025-09-07 02:53:23] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -88.094921-0.001332j
[2025-09-07 02:53:23] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-07 02:53:43] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -88.052897-0.000341j
[2025-09-07 02:54:04] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -88.091711-0.001052j
[2025-09-07 02:54:24] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -88.216369-0.000394j
[2025-09-07 02:54:45] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -88.114059-0.000526j
[2025-09-07 02:55:05] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -88.112226+0.001551j
[2025-09-07 02:55:26] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -88.066057-0.000925j
[2025-09-07 02:55:46] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -88.053035-0.002242j
[2025-09-07 02:56:07] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -88.148466-0.000482j
[2025-09-07 02:56:27] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -88.161219+0.002312j
[2025-09-07 02:56:48] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -88.061971-0.000101j
[2025-09-07 02:57:08] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -88.100139+0.003117j
[2025-09-07 02:57:23] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -88.222919+0.001874j
[2025-09-07 02:57:33] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -88.038077-0.001569j
[2025-09-07 02:57:42] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -88.169197-0.004282j
[2025-09-07 02:57:56] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -88.205812+0.000184j
[2025-09-07 02:58:17] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -88.026440-0.002517j
[2025-09-07 02:58:38] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -88.010922-0.003932j
[2025-09-07 02:58:58] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -88.015045-0.003197j
[2025-09-07 02:59:19] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -88.105446-0.002097j
[2025-09-07 02:59:39] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -88.008702+0.000544j
[2025-09-07 03:00:00] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -88.250964+0.003296j
[2025-09-07 03:00:21] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -88.174967-0.003530j
[2025-09-07 03:00:41] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -88.164021-0.001151j
[2025-09-07 03:00:55] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -88.222900+0.000976j
[2025-09-07 03:01:11] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -87.911253+0.000208j
[2025-09-07 03:01:31] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -88.076320+0.005738j
[2025-09-07 03:01:52] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -88.104715-0.000468j
[2025-09-07 03:02:12] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -88.093619+0.001923j
[2025-09-07 03:02:33] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -88.121045+0.002286j
[2025-09-07 03:02:53] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -88.200469+0.002479j
[2025-09-07 03:02:53] RESTART #2 | Period: 600
[2025-09-07 03:03:14] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -88.006481+0.001320j
[2025-09-07 03:03:34] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -88.080866+0.000618j
[2025-09-07 03:03:55] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -87.952762-0.000461j
[2025-09-07 03:04:15] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -88.196708-0.002365j
[2025-09-07 03:04:35] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -88.053667-0.000065j
[2025-09-07 03:04:56] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -88.072491-0.002492j
[2025-09-07 03:05:16] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -88.052562-0.001253j
[2025-09-07 03:05:37] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -88.292874+0.002460j
[2025-09-07 03:05:57] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -88.084767+0.000094j
[2025-09-07 03:06:18] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -88.016655-0.000392j
[2025-09-07 03:06:38] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -88.124883-0.000010j
[2025-09-07 03:06:59] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -88.067608+0.003893j
[2025-09-07 03:07:19] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -88.148616+0.001250j
[2025-09-07 03:07:40] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -88.039989-0.001953j
[2025-09-07 03:08:00] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -87.975812-0.001930j
[2025-09-07 03:08:21] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -88.087076-0.002226j
[2025-09-07 03:08:41] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -88.111127+0.000916j
[2025-09-07 03:09:02] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -88.165900-0.000699j
[2025-09-07 03:09:22] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -88.291142+0.000606j
[2025-09-07 03:09:43] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -88.350176-0.004434j
[2025-09-07 03:10:03] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -88.347611+0.000902j
[2025-09-07 03:10:24] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -88.254190-0.000995j
[2025-09-07 03:10:45] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -88.234755+0.005480j
[2025-09-07 03:10:55] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -88.350932+0.001679j
[2025-09-07 03:11:05] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -88.292318-0.001056j
[2025-09-07 03:11:14] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -88.223299-0.000238j
[2025-09-07 03:11:31] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -88.257221-0.000710j
[2025-09-07 03:11:52] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -88.109262-0.003133j
[2025-09-07 03:12:12] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -88.104038+0.002614j
[2025-09-07 03:12:33] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -87.979591-0.002226j
[2025-09-07 03:12:54] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -87.989894-0.001323j
[2025-09-07 03:13:14] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -87.939874-0.000221j
[2025-09-07 03:13:35] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -87.929945-0.000893j
[2025-09-07 03:13:56] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -88.021378-0.001635j
[2025-09-07 03:14:15] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -88.100111+0.000412j
[2025-09-07 03:14:27] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -88.123057-0.001772j
[2025-09-07 03:14:46] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -88.215449+0.003839j
[2025-09-07 03:15:06] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -88.307931-0.001146j
[2025-09-07 03:15:27] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -88.379581+0.004881j
[2025-09-07 03:15:47] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -88.256477+0.001939j
[2025-09-07 03:16:08] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -88.108936+0.000027j
[2025-09-07 03:16:28] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -88.061651+0.001036j
[2025-09-07 03:16:49] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -87.910218-0.000376j
[2025-09-07 03:17:09] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -88.013573-0.003266j
[2025-09-07 03:17:30] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -87.956730-0.002812j
[2025-09-07 03:17:51] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -88.075613+0.001673j
[2025-09-07 03:18:11] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -88.078197-0.004130j
[2025-09-07 03:18:32] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -87.995657+0.003688j
[2025-09-07 03:18:52] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -88.126920-0.001631j
[2025-09-07 03:19:12] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -88.178825-0.000369j
[2025-09-07 03:19:33] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -88.084998-0.002937j
[2025-09-07 03:19:53] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -88.078633-0.000986j
[2025-09-07 03:20:14] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -88.180616-0.001797j
[2025-09-07 03:20:34] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -88.240235+0.002695j
[2025-09-07 03:20:55] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -88.453169-0.000571j
[2025-09-07 03:21:15] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -88.279869-0.004654j
[2025-09-07 03:21:36] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -88.266947+0.005135j
[2025-09-07 03:21:56] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -88.171461+0.001478j
[2025-09-07 03:22:17] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -88.173698+0.005577j
[2025-09-07 03:22:37] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -88.083174+0.000036j
[2025-09-07 03:22:58] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -88.107496+0.001242j
[2025-09-07 03:23:18] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -87.906386-0.001373j
[2025-09-07 03:23:39] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -87.940984+0.003817j
[2025-09-07 03:23:59] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -87.943168-0.001169j
[2025-09-07 03:24:18] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -87.920628+0.002428j
[2025-09-07 03:24:27] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -88.105002-0.002554j
[2025-09-07 03:24:37] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -88.114876-0.002715j
[2025-09-07 03:24:50] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -88.127220+0.001452j
[2025-09-07 03:25:10] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -88.179677+0.000307j
[2025-09-07 03:25:31] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -88.072855-0.001661j
[2025-09-07 03:25:52] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -88.156582-0.003080j
[2025-09-07 03:26:12] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -88.039452+0.001709j
[2025-09-07 03:26:33] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -88.268206+0.001276j
[2025-09-07 03:26:54] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -87.858164-0.002123j
[2025-09-07 03:27:14] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -88.009443+0.001498j
[2025-09-07 03:27:14] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-07 03:27:35] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -88.077302-0.001615j
[2025-09-07 03:27:49] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -88.272387-0.001116j
[2025-09-07 03:28:04] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -88.201929+0.001071j
[2025-09-07 03:28:25] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -88.339106-0.000092j
[2025-09-07 03:28:45] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -88.271698-0.000219j
[2025-09-07 03:29:06] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -88.143710+0.001419j
[2025-09-07 03:29:26] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -88.119123-0.001483j
[2025-09-07 03:29:47] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -88.146666+0.004026j
[2025-09-07 03:30:07] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -88.001934+0.001762j
[2025-09-07 03:30:28] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -87.984527-0.003833j
[2025-09-07 03:30:48] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -87.969040+0.004180j
[2025-09-07 03:31:09] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -87.967029-0.000175j
[2025-09-07 03:31:29] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -88.046291-0.000006j
[2025-09-07 03:31:50] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -87.975601-0.004661j
[2025-09-07 03:32:11] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -87.963360-0.001651j
[2025-09-07 03:32:31] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -88.150731+0.000723j
[2025-09-07 03:32:52] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -88.087991-0.000685j
[2025-09-07 03:33:12] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -88.184206-0.000882j
[2025-09-07 03:33:33] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -88.123346+0.000698j
[2025-09-07 03:33:53] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -88.166430-0.000855j
[2025-09-07 03:34:14] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -88.118399+0.002266j
[2025-09-07 03:34:34] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -88.265324-0.002981j
[2025-09-07 03:34:55] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -88.095992-0.003309j
[2025-09-07 03:35:15] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -87.973559-0.003092j
[2025-09-07 03:35:36] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -87.964977+0.001260j
[2025-09-07 03:35:56] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -87.973979-0.000233j
[2025-09-07 03:36:17] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -88.033125+0.001072j
[2025-09-07 03:36:37] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -88.179957+0.001038j
[2025-09-07 03:36:58] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -88.041554-0.002930j
[2025-09-07 03:37:18] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -87.966255+0.000228j
[2025-09-07 03:37:39] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -88.147286-0.000628j
[2025-09-07 03:37:50] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -88.149292+0.003066j
[2025-09-07 03:37:59] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -88.042839-0.001966j
[2025-09-07 03:38:09] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -87.992465+0.001729j
[2025-09-07 03:38:18] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -88.084374+0.001571j
[2025-09-07 03:38:27] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -88.058018-0.000464j
[2025-09-07 03:38:36] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -88.115096+0.003020j
[2025-09-07 03:38:46] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -87.998453+0.001573j
[2025-09-07 03:38:55] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -88.042277+0.000758j
[2025-09-07 03:39:04] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -87.932065+0.000239j
[2025-09-07 03:39:13] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -87.913419-0.001259j
[2025-09-07 03:39:23] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -88.020676-0.002231j
[2025-09-07 03:39:32] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -87.936467+0.001829j
[2025-09-07 03:39:41] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -88.104049+0.000311j
[2025-09-07 03:39:50] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -88.064827-0.000959j
[2025-09-07 03:40:00] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -88.056848-0.000043j
[2025-09-07 03:40:09] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -87.973786+0.003048j
[2025-09-07 03:40:18] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -87.936280+0.000373j
[2025-09-07 03:40:27] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -88.192034+0.001167j
[2025-09-07 03:40:36] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -88.283834-0.002805j
[2025-09-07 03:40:46] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -88.308585-0.002750j
[2025-09-07 03:40:55] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -88.074863+0.000581j
[2025-09-07 03:41:04] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -88.135243+0.000627j
[2025-09-07 03:41:13] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -88.056175+0.000051j
[2025-09-07 03:41:23] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -88.135839+0.002646j
[2025-09-07 03:41:32] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -88.178783-0.000164j
[2025-09-07 03:41:41] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -88.275892+0.003534j
[2025-09-07 03:41:50] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -88.193330+0.001220j
[2025-09-07 03:42:00] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -88.130504-0.000216j
[2025-09-07 03:42:09] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -88.256729+0.001793j
[2025-09-07 03:42:18] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -88.207496-0.004883j
[2025-09-07 03:42:27] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -88.297502-0.000271j
[2025-09-07 03:42:37] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -88.439785-0.001282j
[2025-09-07 03:42:46] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -88.442483+0.004137j
[2025-09-07 03:42:55] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -88.320946-0.001326j
[2025-09-07 03:43:04] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -88.121097-0.005757j
[2025-09-07 03:43:14] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -88.374818+0.001287j
[2025-09-07 03:43:23] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -88.253215+0.003189j
[2025-09-07 03:43:32] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -88.420113+0.004838j
[2025-09-07 03:43:41] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -88.306526+0.003726j
[2025-09-07 03:43:51] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -88.308152-0.000310j
[2025-09-07 03:44:00] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -88.152477-0.003926j
[2025-09-07 03:44:09] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -88.283460-0.001012j
[2025-09-07 03:44:18] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -88.207013-0.003011j
[2025-09-07 03:44:28] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -88.042479-0.001248j
[2025-09-07 03:44:37] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -88.048153+0.005398j
[2025-09-07 03:44:46] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -88.074741+0.000521j
[2025-09-07 03:44:55] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -88.175901+0.000857j
[2025-09-07 03:45:05] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -88.140217+0.003204j
[2025-09-07 03:45:14] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -88.281150+0.000572j
[2025-09-07 03:45:23] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -88.076830+0.000150j
[2025-09-07 03:45:32] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -88.120887-0.001318j
[2025-09-07 03:45:42] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -88.113614-0.000932j
[2025-09-07 03:45:51] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -88.031223-0.001468j
[2025-09-07 03:46:00] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -88.031680+0.004353j
[2025-09-07 03:46:09] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -88.119685+0.001034j
[2025-09-07 03:46:19] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -88.379342+0.004782j
[2025-09-07 03:46:28] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -88.175683-0.000638j
[2025-09-07 03:46:37] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -88.102590-0.001490j
[2025-09-07 03:46:46] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -88.100123-0.000025j
[2025-09-07 03:46:56] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -88.060130-0.000028j
[2025-09-07 03:47:05] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -88.087568-0.001763j
[2025-09-07 03:47:14] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -88.144847-0.001002j
[2025-09-07 03:47:23] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -88.156018+0.000890j
[2025-09-07 03:47:32] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -88.304950-0.002780j
[2025-09-07 03:47:42] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -88.109790+0.004530j
[2025-09-07 03:47:51] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -88.239075+0.001059j
[2025-09-07 03:48:00] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -88.155970+0.001790j
[2025-09-07 03:48:09] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -88.190202+0.000621j
[2025-09-07 03:48:19] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -88.327707-0.000395j
[2025-09-07 03:48:28] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -88.162921+0.001490j
[2025-09-07 03:48:37] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -88.203365+0.002529j
[2025-09-07 03:48:46] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -88.150464-0.004229j
[2025-09-07 03:48:56] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -88.056933-0.000517j
[2025-09-07 03:49:05] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -88.056842-0.001335j
[2025-09-07 03:49:05] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-07 03:49:14] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -88.223569+0.001479j
[2025-09-07 03:49:23] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -88.210287+0.000065j
[2025-09-07 03:49:33] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -88.071409-0.002246j
[2025-09-07 03:49:42] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -88.097636-0.002649j
[2025-09-07 03:49:51] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -88.103238-0.001220j
[2025-09-07 03:50:00] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -88.069175+0.002274j
[2025-09-07 03:50:10] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -88.086483-0.002526j
[2025-09-07 03:50:19] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -88.151316-0.004394j
[2025-09-07 03:50:28] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -88.196895+0.000463j
[2025-09-07 03:50:37] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -88.202528+0.003064j
[2025-09-07 03:50:47] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -88.129612-0.000848j
[2025-09-07 03:50:56] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -88.112157+0.000434j
[2025-09-07 03:51:05] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -87.890795+0.003449j
[2025-09-07 03:51:14] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -87.909482+0.000075j
[2025-09-07 03:51:24] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -87.888173+0.001382j
[2025-09-07 03:51:33] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -88.087269-0.002880j
[2025-09-07 03:51:42] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -88.246220-0.000707j
[2025-09-07 03:51:51] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -88.121536-0.005109j
[2025-09-07 03:52:01] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -88.131216+0.002154j
[2025-09-07 03:52:10] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -88.186914-0.001869j
[2025-09-07 03:52:19] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -88.134804-0.002258j
[2025-09-07 03:52:28] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -88.147099+0.000400j
[2025-09-07 03:52:38] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -88.179819+0.002908j
[2025-09-07 03:52:47] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -87.989031-0.001054j
[2025-09-07 03:52:56] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -87.967250+0.002326j
[2025-09-07 03:53:05] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -88.147649+0.001003j
[2025-09-07 03:53:14] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -88.142876-0.000313j
[2025-09-07 03:53:24] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -88.036411+0.004597j
[2025-09-07 03:53:33] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -88.106783-0.002744j
[2025-09-07 03:53:42] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -88.071865-0.003113j
[2025-09-07 03:53:51] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -88.318918+0.000066j
[2025-09-07 03:54:01] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -88.202366-0.001747j
[2025-09-07 03:54:10] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -88.305047-0.004435j
[2025-09-07 03:54:19] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -88.422864-0.003169j
[2025-09-07 03:54:28] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -88.238538+0.001646j
[2025-09-07 03:54:38] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -88.284001+0.004926j
[2025-09-07 03:54:47] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -88.319204-0.000345j
[2025-09-07 03:54:56] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -88.109916+0.000093j
[2025-09-07 03:55:05] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -88.161503-0.007638j
[2025-09-07 03:55:15] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -88.248635-0.001132j
[2025-09-07 03:55:24] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -88.273086+0.000560j
[2025-09-07 03:55:33] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -88.168123+0.002202j
[2025-09-07 03:55:42] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -88.159980-0.001634j
[2025-09-07 03:55:52] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -88.210974+0.002164j
[2025-09-07 03:56:01] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -88.177220-0.002098j
[2025-09-07 03:56:10] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -88.151788-0.003082j
[2025-09-07 03:56:19] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -88.316492+0.000674j
[2025-09-07 03:56:29] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -88.166514+0.007633j
[2025-09-07 03:56:38] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -88.277691+0.002237j
[2025-09-07 03:56:47] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -88.198718-0.001271j
[2025-09-07 03:56:56] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -88.325636-0.001453j
[2025-09-07 03:57:06] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -88.337128-0.002706j
[2025-09-07 03:57:15] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -88.249862+0.002648j
[2025-09-07 03:57:24] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -88.136097-0.000270j
[2025-09-07 03:57:33] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -88.226486-0.003270j
[2025-09-07 03:57:43] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -88.255910-0.002734j
[2025-09-07 03:57:52] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -88.213987-0.002874j
[2025-09-07 03:58:01] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -88.212580-0.000175j
[2025-09-07 03:58:10] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -88.143425-0.004099j
[2025-09-07 03:58:20] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -88.092796+0.001614j
[2025-09-07 03:58:29] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -88.013967-0.003695j
[2025-09-07 03:58:38] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -88.035975-0.001852j
[2025-09-07 03:58:47] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -88.068300-0.000701j
[2025-09-07 03:58:57] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -87.970397+0.003070j
[2025-09-07 03:59:06] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -87.998452-0.001339j
[2025-09-07 03:59:15] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -88.230346-0.001219j
[2025-09-07 03:59:24] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -88.070404-0.002421j
[2025-09-07 03:59:34] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -88.078532-0.000153j
[2025-09-07 03:59:43] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -88.107695-0.002995j
[2025-09-07 03:59:52] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -88.074511+0.001906j
[2025-09-07 04:00:01] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -88.046546+0.002801j
[2025-09-07 04:00:10] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -88.123035+0.003152j
[2025-09-07 04:00:20] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -88.155690+0.005798j
[2025-09-07 04:00:29] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -88.059405-0.001036j
[2025-09-07 04:00:38] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -88.056852+0.000771j
[2025-09-07 04:00:47] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -88.195404-0.000100j
[2025-09-07 04:00:57] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -88.254325-0.004893j
[2025-09-07 04:01:06] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -88.019658+0.000309j
[2025-09-07 04:01:15] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -88.085609+0.001341j
[2025-09-07 04:01:24] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -87.963445+0.001137j
[2025-09-07 04:01:34] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -87.891191+0.002120j
[2025-09-07 04:01:43] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -88.110766-0.000332j
[2025-09-07 04:01:52] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -87.921648-0.000792j
[2025-09-07 04:02:01] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -88.009601-0.001155j
[2025-09-07 04:02:11] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -88.136009+0.004011j
[2025-09-07 04:02:20] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -88.147175+0.000163j
[2025-09-07 04:02:29] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -88.113829-0.000210j
[2025-09-07 04:02:38] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -88.173380-0.002481j
[2025-09-07 04:02:48] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -88.005396+0.000178j
[2025-09-07 04:02:57] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -88.145519-0.000306j
[2025-09-07 04:03:06] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -88.167819+0.003454j
[2025-09-07 04:03:15] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -88.025967-0.001167j
[2025-09-07 04:03:24] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -88.108785-0.002622j
[2025-09-07 04:03:34] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -88.182425+0.001733j
[2025-09-07 04:03:43] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -88.084850-0.003518j
[2025-09-07 04:03:52] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -88.234966-0.002815j
[2025-09-07 04:04:01] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -88.167424+0.001458j
[2025-09-07 04:04:11] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -88.338015+0.000755j
[2025-09-07 04:04:20] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -88.248017-0.001566j
[2025-09-07 04:04:29] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -88.207418-0.002134j
[2025-09-07 04:04:38] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -88.199574-0.004276j
[2025-09-07 04:04:48] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -88.135206-0.001012j
[2025-09-07 04:04:57] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -88.338306+0.002375j
[2025-09-07 04:05:06] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -88.276524+0.001802j
[2025-09-07 04:05:15] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -88.372718+0.000421j
[2025-09-07 04:05:15] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-07 04:05:25] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -88.373483+0.000293j
[2025-09-07 04:05:34] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -88.375455+0.004385j
[2025-09-07 04:05:43] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -88.274318-0.004090j
[2025-09-07 04:05:52] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -88.303026+0.002463j
[2025-09-07 04:06:02] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -88.255852+0.002698j
[2025-09-07 04:06:11] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -88.133754+0.000058j
[2025-09-07 04:06:20] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -88.144896-0.000789j
[2025-09-07 04:06:29] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -87.987926-0.000848j
[2025-09-07 04:06:39] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -88.068176-0.004254j
[2025-09-07 04:06:48] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -88.048274-0.004739j
[2025-09-07 04:06:57] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -88.174146+0.000676j
[2025-09-07 04:07:06] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -87.911977+0.002836j
[2025-09-07 04:07:16] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -87.959923-0.000488j
[2025-09-07 04:07:25] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -88.095713+0.001273j
[2025-09-07 04:07:34] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -88.030607-0.002482j
[2025-09-07 04:07:43] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -88.206638+0.001743j
[2025-09-07 04:07:53] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -88.173167+0.000001j
[2025-09-07 04:08:02] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -88.000017+0.001320j
[2025-09-07 04:08:11] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -88.126573+0.002977j
[2025-09-07 04:08:20] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -87.967843-0.000202j
[2025-09-07 04:08:30] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -88.149688+0.000445j
[2025-09-07 04:08:39] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -88.092101-0.001220j
[2025-09-07 04:08:48] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -88.309204-0.001990j
[2025-09-07 04:08:57] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -88.241379-0.001143j
[2025-09-07 04:09:07] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -88.282111-0.003189j
[2025-09-07 04:09:16] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -88.112384-0.003199j
[2025-09-07 04:09:25] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -88.140601+0.000859j
[2025-09-07 04:09:34] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -88.065862+0.001146j
[2025-09-07 04:09:44] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -88.099211-0.000578j
[2025-09-07 04:09:53] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -88.160542+0.003671j
[2025-09-07 04:10:02] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -87.960411-0.000973j
[2025-09-07 04:10:11] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -87.969797-0.000040j
[2025-09-07 04:10:21] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -87.899589-0.000707j
[2025-09-07 04:10:30] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -88.061913+0.000769j
[2025-09-07 04:10:39] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -88.023524-0.001508j
[2025-09-07 04:10:48] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -88.112752-0.005453j
[2025-09-07 04:10:58] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -88.167668-0.000691j
[2025-09-07 04:11:07] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -88.121172-0.001036j
[2025-09-07 04:11:16] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -88.097081-0.002765j
[2025-09-07 04:11:25] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -88.200607+0.001522j
[2025-09-07 04:11:35] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -88.268835+0.004231j
[2025-09-07 04:11:44] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -88.173662+0.001754j
[2025-09-07 04:11:53] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -88.084002-0.000963j
[2025-09-07 04:12:02] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -88.088828-0.000902j
[2025-09-07 04:12:12] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -88.241276+0.001454j
[2025-09-07 04:12:21] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -88.200908+0.000615j
[2025-09-07 04:12:30] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -88.190784+0.002110j
[2025-09-07 04:12:39] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -88.138234-0.000739j
[2025-09-07 04:12:48] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -88.091945+0.004411j
[2025-09-07 04:12:58] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -88.205339+0.000644j
[2025-09-07 04:13:07] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -88.204441+0.004921j
[2025-09-07 04:13:16] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -88.161247+0.004695j
[2025-09-07 04:13:25] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -88.043532-0.000531j
[2025-09-07 04:13:35] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -88.269407+0.002964j
[2025-09-07 04:13:44] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -88.235564-0.002444j
[2025-09-07 04:13:53] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -88.315015-0.003256j
[2025-09-07 04:14:03] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -88.341650-0.001808j
[2025-09-07 04:14:12] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -88.419580+0.000599j
[2025-09-07 04:14:21] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -88.329035-0.000702j
[2025-09-07 04:14:30] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -88.380037+0.001051j
[2025-09-07 04:14:39] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -88.263490+0.001093j
[2025-09-07 04:14:49] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -88.232126+0.002646j
[2025-09-07 04:14:58] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -88.165611+0.001664j
[2025-09-07 04:15:07] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -88.310890+0.001576j
[2025-09-07 04:15:16] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -88.130115-0.002141j
[2025-09-07 04:15:26] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -88.292252-0.001303j
[2025-09-07 04:15:35] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -88.291798+0.000543j
[2025-09-07 04:15:44] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -88.185899-0.000340j
[2025-09-07 04:15:53] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -88.319634-0.000486j
[2025-09-07 04:16:03] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -88.267697-0.004068j
[2025-09-07 04:16:12] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -88.193366-0.000635j
[2025-09-07 04:16:21] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -88.142488+0.003724j
[2025-09-07 04:16:30] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -88.224674+0.001316j
[2025-09-07 04:16:40] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -88.093334+0.004472j
[2025-09-07 04:16:49] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -88.133088+0.002241j
[2025-09-07 04:16:58] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -88.086066+0.002015j
[2025-09-07 04:17:07] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -88.141664+0.001992j
[2025-09-07 04:17:17] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -88.369658-0.002704j
[2025-09-07 04:17:26] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -88.240062+0.002480j
[2025-09-07 04:17:35] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -88.295512-0.001066j
[2025-09-07 04:17:44] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -88.413373+0.000827j
[2025-09-07 04:17:53] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -88.150109+0.004022j
[2025-09-07 04:18:03] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -88.081589+0.000153j
[2025-09-07 04:18:12] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -88.180316+0.001068j
[2025-09-07 04:18:21] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -88.251594-0.001085j
[2025-09-07 04:18:30] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -88.072646+0.001471j
[2025-09-07 04:18:40] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -88.151969-0.003047j
[2025-09-07 04:18:49] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -88.233872+0.003205j
[2025-09-07 04:18:58] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -88.277847-0.002746j
[2025-09-07 04:19:07] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -88.275858-0.002069j
[2025-09-07 04:19:17] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -88.185846-0.000584j
[2025-09-07 04:19:26] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -88.094461+0.002106j
[2025-09-07 04:19:35] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -87.952261-0.001796j
[2025-09-07 04:19:44] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -88.150243+0.000013j
[2025-09-07 04:19:54] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -88.143240+0.001338j
[2025-09-07 04:20:03] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -88.096203-0.000176j
[2025-09-07 04:20:12] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -88.057252+0.003439j
[2025-09-07 04:20:21] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -88.054475-0.004394j
[2025-09-07 04:20:31] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -88.143412-0.000437j
[2025-09-07 04:20:40] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -88.247128+0.001272j
[2025-09-07 04:20:49] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -88.248927-0.001659j
[2025-09-07 04:20:58] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -88.239629+0.000627j
[2025-09-07 04:21:08] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -88.075778-0.003265j
[2025-09-07 04:21:17] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -88.128897-0.000244j
[2025-09-07 04:21:26] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -88.184513-0.001729j
[2025-09-07 04:21:26] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-07 04:21:35] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -88.207167+0.001798j
[2025-09-07 04:21:45] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -88.101389-0.000308j
[2025-09-07 04:21:54] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -88.238738+0.002635j
[2025-09-07 04:22:03] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -88.225795+0.000355j
[2025-09-07 04:22:12] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -88.221025+0.000213j
[2025-09-07 04:22:22] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -88.211127+0.000760j
[2025-09-07 04:22:31] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -88.213410-0.001434j
[2025-09-07 04:22:40] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -88.181401-0.004598j
[2025-09-07 04:22:49] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -88.171906-0.002128j
[2025-09-07 04:22:58] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -88.138563+0.002479j
[2025-09-07 04:23:08] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -88.070013-0.000453j
[2025-09-07 04:23:17] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -88.077535-0.000287j
[2025-09-07 04:23:26] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -88.106051+0.001033j
[2025-09-07 04:23:35] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -87.999788+0.000710j
[2025-09-07 04:23:45] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -87.998192+0.000508j
[2025-09-07 04:23:54] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -88.200248-0.000114j
[2025-09-07 04:24:03] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -88.060516+0.001651j
[2025-09-07 04:24:12] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -88.206210+0.000115j
[2025-09-07 04:24:22] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -88.268741+0.001456j
[2025-09-07 04:24:31] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -88.306512+0.002888j
[2025-09-07 04:24:40] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -88.200268+0.001898j
[2025-09-07 04:24:49] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -88.237183+0.002043j
[2025-09-07 04:24:59] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -88.210807-0.000359j
[2025-09-07 04:25:08] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -88.160071+0.002049j
[2025-09-07 04:25:17] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -88.119021-0.002864j
[2025-09-07 04:25:26] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -88.154722-0.000751j
[2025-09-07 04:25:36] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -88.164922+0.000202j
[2025-09-07 04:25:45] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -88.234088-0.000900j
[2025-09-07 04:25:54] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -88.167591-0.000895j
[2025-09-07 04:26:03] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -88.079942-0.003301j
[2025-09-07 04:26:13] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -88.115692-0.004041j
[2025-09-07 04:26:22] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -88.073850-0.001570j
[2025-09-07 04:26:31] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -88.002434-0.002506j
[2025-09-07 04:26:40] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -88.141942+0.004171j
[2025-09-07 04:26:50] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -88.112111+0.000008j
[2025-09-07 04:26:59] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -88.088324+0.001187j
[2025-09-07 04:27:08] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -88.042450-0.000901j
[2025-09-07 04:27:17] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -88.019421+0.004782j
[2025-09-07 04:27:27] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -88.112004-0.000708j
[2025-09-07 04:27:36] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -88.187992-0.002233j
[2025-09-07 04:27:45] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -88.350319-0.002712j
[2025-09-07 04:27:54] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -88.112540-0.004240j
[2025-09-07 04:28:04] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -87.987825+0.001972j
[2025-09-07 04:28:13] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -88.017294-0.000761j
[2025-09-07 04:28:22] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -88.059991-0.001877j
[2025-09-07 04:28:31] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -88.015348-0.002918j
[2025-09-07 04:28:41] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -88.081458+0.004167j
[2025-09-07 04:28:50] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -88.104444+0.000059j
[2025-09-07 04:28:59] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -88.028334+0.001661j
[2025-09-07 04:29:08] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -88.009133+0.003157j
[2025-09-07 04:29:17] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -88.053898+0.001554j
[2025-09-07 04:29:27] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -88.031894+0.000975j
[2025-09-07 04:29:36] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -88.033472-0.000181j
[2025-09-07 04:29:45] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -88.071161-0.001754j
[2025-09-07 04:29:54] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -88.026018+0.001647j
[2025-09-07 04:30:04] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -88.128724+0.001675j
[2025-09-07 04:30:13] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -88.180521+0.001822j
[2025-09-07 04:30:22] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -88.114071-0.002634j
[2025-09-07 04:30:31] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -88.065136-0.003173j
[2025-09-07 04:30:41] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -88.106677+0.001952j
[2025-09-07 04:30:50] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -88.032452-0.000046j
[2025-09-07 04:30:59] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -88.084786+0.004092j
[2025-09-07 04:31:08] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -88.091653+0.001958j
[2025-09-07 04:31:18] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -88.015143+0.002899j
[2025-09-07 04:31:27] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -88.111838+0.001283j
[2025-09-07 04:31:36] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -88.175700-0.002994j
[2025-09-07 04:31:45] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -88.105032-0.001985j
[2025-09-07 04:31:55] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -88.100880-0.002819j
[2025-09-07 04:32:04] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -88.060929-0.002330j
[2025-09-07 04:32:13] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -87.942400-0.005709j
[2025-09-07 04:32:22] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -87.938498+0.002399j
[2025-09-07 04:32:32] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -87.977367-0.000953j
[2025-09-07 04:32:41] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -88.073530-0.000669j
[2025-09-07 04:32:50] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -88.160617-0.000779j
[2025-09-07 04:32:59] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -88.085081-0.002915j
[2025-09-07 04:33:09] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -88.275555-0.001564j
[2025-09-07 04:33:18] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -88.255118+0.001392j
[2025-09-07 04:33:27] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -88.158730-0.001757j
[2025-09-07 04:33:36] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -88.174126-0.003893j
[2025-09-07 04:33:46] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -88.098567+0.002601j
[2025-09-07 04:33:55] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -88.265514-0.002570j
[2025-09-07 04:34:04] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -88.203537-0.000690j
[2025-09-07 04:34:13] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -88.114066+0.001149j
[2025-09-07 04:34:22] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -87.898114-0.002491j
[2025-09-07 04:34:32] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -88.047365+0.001784j
[2025-09-07 04:34:41] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -87.901865+0.000534j
[2025-09-07 04:34:50] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -88.013481+0.001123j
[2025-09-07 04:34:59] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -88.110035+0.003538j
[2025-09-07 04:35:09] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -88.170039-0.001792j
[2025-09-07 04:35:18] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -88.187468+0.000666j
[2025-09-07 04:35:27] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -88.265879-0.001628j
[2025-09-07 04:35:36] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -88.264900+0.001158j
[2025-09-07 04:35:46] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -88.347343-0.000779j
[2025-09-07 04:35:55] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -88.138237-0.000325j
[2025-09-07 04:36:04] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -88.191108+0.001791j
[2025-09-07 04:36:13] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -88.187253-0.000370j
[2025-09-07 04:36:23] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -88.320244-0.003298j
[2025-09-07 04:36:32] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -88.189227+0.001207j
[2025-09-07 04:36:41] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -88.173081+0.003936j
[2025-09-07 04:36:50] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -88.182734+0.003012j
[2025-09-07 04:37:00] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -88.187793+0.003860j
[2025-09-07 04:37:09] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -88.132126-0.000832j
[2025-09-07 04:37:18] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -88.225487+0.003932j
[2025-09-07 04:37:27] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -88.233425+0.001218j
[2025-09-07 04:37:37] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -88.091754+0.000982j
[2025-09-07 04:37:37] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-07 04:37:46] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -87.994197-0.003296j
[2025-09-07 04:37:55] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -88.047079+0.000928j
[2025-09-07 04:38:04] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -88.139150+0.001229j
[2025-09-07 04:38:14] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -87.858454-0.000907j
[2025-09-07 04:38:23] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -88.067836+0.003316j
[2025-09-07 04:38:32] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -88.064151-0.000128j
[2025-09-07 04:38:41] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -88.113658+0.004864j
[2025-09-07 04:38:51] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -88.151502-0.013547j
[2025-09-07 04:39:00] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -88.174258+0.002030j
[2025-09-07 04:39:09] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -88.198879-0.000706j
[2025-09-07 04:39:18] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -88.150426-0.000794j
[2025-09-07 04:39:28] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -88.232956-0.000268j
[2025-09-07 04:39:37] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -88.083854-0.002287j
[2025-09-07 04:39:46] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -88.010667+0.005225j
[2025-09-07 04:39:55] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -88.156981-0.002555j
[2025-09-07 04:40:05] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -88.084482-0.002649j
[2025-09-07 04:40:14] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -88.063841+0.001809j
[2025-09-07 04:40:23] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -88.007839-0.001083j
[2025-09-07 04:40:32] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -88.197815-0.001489j
[2025-09-07 04:40:42] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -88.230490-0.000779j
[2025-09-07 04:40:51] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -88.152987+0.000673j
[2025-09-07 04:41:00] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -88.160898+0.002788j
[2025-09-07 04:41:09] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -88.150079-0.005015j
[2025-09-07 04:41:18] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -88.211990+0.002909j
[2025-09-07 04:41:28] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -88.246324-0.001666j
[2025-09-07 04:41:37] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -88.134701+0.000750j
[2025-09-07 04:41:46] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -88.150655+0.000189j
[2025-09-07 04:41:55] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -88.178729+0.000838j
[2025-09-07 04:42:05] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -88.173222-0.001472j
[2025-09-07 04:42:14] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -88.063917+0.003053j
[2025-09-07 04:42:23] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -88.104793-0.000819j
[2025-09-07 04:42:32] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -88.319936-0.000582j
[2025-09-07 04:42:42] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -88.205623+0.002110j
[2025-09-07 04:42:51] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -88.167712-0.001856j
[2025-09-07 04:43:00] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -88.116775-0.000427j
[2025-09-07 04:43:09] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -88.142223-0.000148j
[2025-09-07 04:43:19] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -88.094509-0.001003j
[2025-09-07 04:43:28] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -88.194613-0.000300j
[2025-09-07 04:43:37] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -88.243196-0.001319j
[2025-09-07 04:43:46] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -88.125325-0.001113j
[2025-09-07 04:43:56] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -88.118211-0.002713j
[2025-09-07 04:44:05] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -88.009471+0.001368j
[2025-09-07 04:44:14] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -87.951998+0.001522j
[2025-09-07 04:44:23] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -88.080546-0.001925j
[2025-09-07 04:44:33] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -87.952576+0.002098j
[2025-09-07 04:44:42] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -87.829116+0.000372j
[2025-09-07 04:44:51] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -87.813697+0.002401j
[2025-09-07 04:45:00] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -88.073909+0.000400j
[2025-09-07 04:45:10] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -88.131187-0.003096j
[2025-09-07 04:45:19] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -88.067958+0.001927j
[2025-09-07 04:45:28] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -88.006238-0.001802j
[2025-09-07 04:45:37] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -88.068746+0.002705j
[2025-09-07 04:45:46] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -87.955310+0.001720j
[2025-09-07 04:45:56] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -88.000258+0.001360j
[2025-09-07 04:46:05] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -87.980284+0.001053j
[2025-09-07 04:46:14] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -87.955532+0.001008j
[2025-09-07 04:46:23] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -87.960272-0.000361j
[2025-09-07 04:46:33] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -88.042710+0.000220j
[2025-09-07 04:46:42] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -88.107635-0.002611j
[2025-09-07 04:46:51] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -87.964624+0.001554j
[2025-09-07 04:47:00] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -87.961061-0.003407j
[2025-09-07 04:47:10] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -88.093547-0.003213j
[2025-09-07 04:47:19] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -88.080363-0.000914j
[2025-09-07 04:47:28] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -88.062823+0.002417j
[2025-09-07 04:47:37] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -87.971754-0.001883j
[2025-09-07 04:47:47] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -87.956028-0.002106j
[2025-09-07 04:47:56] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -88.078972+0.001258j
[2025-09-07 04:48:05] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -87.966467+0.003206j
[2025-09-07 04:48:14] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -88.031121+0.002752j
[2025-09-07 04:48:24] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -88.003737+0.002737j
[2025-09-07 04:48:33] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -88.108428+0.000866j
[2025-09-07 04:48:42] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -88.067821+0.000709j
[2025-09-07 04:48:51] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -88.147417-0.001862j
[2025-09-07 04:49:01] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -88.002413-0.000023j
[2025-09-07 04:49:10] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -88.305662+0.001804j
[2025-09-07 04:49:19] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -88.158640-0.003775j
[2025-09-07 04:49:28] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -88.184147-0.003304j
[2025-09-07 04:49:38] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -88.194401+0.000164j
[2025-09-07 04:49:47] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -87.975623+0.001014j
[2025-09-07 04:49:56] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -88.075260+0.001287j
[2025-09-07 04:50:05] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -88.180097+0.004266j
[2025-09-07 04:50:15] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -88.188678+0.002535j
[2025-09-07 04:50:24] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -88.160074-0.000913j
[2025-09-07 04:50:33] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -88.179509-0.000262j
[2025-09-07 04:50:42] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -88.166081+0.001244j
[2025-09-07 04:50:52] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -88.030642-0.001931j
[2025-09-07 04:51:01] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -87.943733-0.003200j
[2025-09-07 04:51:10] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -88.201561+0.003918j
[2025-09-07 04:51:19] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -88.042466+0.000077j
[2025-09-07 04:51:29] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -88.051376+0.001333j
[2025-09-07 04:51:38] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -88.155225-0.001328j
[2025-09-07 04:51:47] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -88.028112-0.004304j
[2025-09-07 04:51:56] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -88.030644-0.000907j
[2025-09-07 04:52:05] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -87.977067+0.000264j
[2025-09-07 04:52:15] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -87.925859-0.001169j
[2025-09-07 04:52:24] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -87.949192-0.000490j
[2025-09-07 04:52:33] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -87.892130-0.001544j
[2025-09-07 04:52:42] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -87.982360+0.001413j
[2025-09-07 04:52:52] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -88.166565-0.001914j
[2025-09-07 04:53:01] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -88.243158+0.001023j
[2025-09-07 04:53:10] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -88.262423-0.000527j
[2025-09-07 04:53:19] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -88.090532-0.000422j
[2025-09-07 04:53:29] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -88.103833-0.002065j
[2025-09-07 04:53:38] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -88.152273-0.001042j
[2025-09-07 04:53:47] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -87.968371-0.000533j
[2025-09-07 04:53:47] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-07 04:53:47] ✅ Training completed | Restarts: 2
[2025-09-07 04:53:47] ============================================================
[2025-09-07 04:53:47] Training completed | Runtime: 15466.9s
[2025-09-07 04:53:51] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-07 04:53:51] ============================================================
