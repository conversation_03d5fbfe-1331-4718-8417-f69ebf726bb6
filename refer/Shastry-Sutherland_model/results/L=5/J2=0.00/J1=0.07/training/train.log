[2025-09-06 07:57:15] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.06/training/checkpoints/final_GCNN.pkl
[2025-09-06 07:57:15]   - 迭代次数: final
[2025-09-06 07:57:15]   - 能量: -86.399633+0.002043j ± 0.113981
[2025-09-06 07:57:15]   - 时间戳: 2025-09-06T07:56:23.380549+08:00
[2025-09-06 07:57:28] ✓ 变分状态参数已从checkpoint恢复
[2025-09-06 07:57:28] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-06 07:57:28] ==================================================
[2025-09-06 07:57:28] GCNN for Shastry-Sutherland Model
[2025-09-06 07:57:28] ==================================================
[2025-09-06 07:57:28] System parameters:
[2025-09-06 07:57:28]   - System size: L=5, N=100
[2025-09-06 07:57:28]   - System parameters: J1=0.07, J2=0.0, Q=1.0
[2025-09-06 07:57:28] --------------------------------------------------
[2025-09-06 07:57:28] Model parameters:
[2025-09-06 07:57:28]   - Number of layers = 4
[2025-09-06 07:57:28]   - Number of features = 4
[2025-09-06 07:57:28]   - Total parameters = 19628
[2025-09-06 07:57:28] --------------------------------------------------
[2025-09-06 07:57:28] Training parameters:
[2025-09-06 07:57:28]   - Learning rate: 0.015
[2025-09-06 07:57:28]   - Total iterations: 1050
[2025-09-06 07:57:28]   - Annealing cycles: 3
[2025-09-06 07:57:28]   - Initial period: 150
[2025-09-06 07:57:28]   - Period multiplier: 2.0
[2025-09-06 07:57:28]   - Temperature range: 0.0-1.0
[2025-09-06 07:57:28]   - Samples: 4096
[2025-09-06 07:57:28]   - Discarded samples: 0
[2025-09-06 07:57:28]   - Chunk size: 2048
[2025-09-06 07:57:28]   - Diagonal shift: 0.2
[2025-09-06 07:57:28]   - Gradient clipping: 1.0
[2025-09-06 07:57:28]   - Checkpoint enabled: interval=105
[2025-09-06 07:57:28]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.07/training/checkpoints
[2025-09-06 07:57:28] --------------------------------------------------
[2025-09-06 07:57:28] Device status:
[2025-09-06 07:57:28]   - Devices model: NVIDIA H200 NVL
[2025-09-06 07:57:28]   - Number of devices: 1
[2025-09-06 07:57:29]   - Sharding: True
[2025-09-06 07:57:29] ============================================================
[2025-09-06 07:58:29] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -86.923875-0.000471j
[2025-09-06 07:59:16] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -86.828211+0.001233j
[2025-09-06 07:59:47] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -86.692584+0.003213j
[2025-09-06 08:00:18] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -86.836271+0.002343j
[2025-09-06 08:00:48] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -86.615578+0.004923j
[2025-09-06 08:01:19] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -86.585305+0.003954j
[2025-09-06 08:01:50] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -86.550201+0.002309j
[2025-09-06 08:02:21] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -86.657747+0.005028j
[2025-09-06 08:02:52] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -86.611593-0.004758j
[2025-09-06 08:03:22] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -86.693415+0.003033j
[2025-09-06 08:03:53] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -86.683483-0.002283j
[2025-09-06 08:04:24] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -86.775231-0.003672j
[2025-09-06 08:04:55] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -86.804679-0.003035j
[2025-09-06 08:05:26] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -86.875755-0.003182j
[2025-09-06 08:05:56] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -87.017273-0.000118j
[2025-09-06 08:06:18] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -86.998429+0.000282j
[2025-09-06 08:06:41] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -86.924238-0.001048j
[2025-09-06 08:07:13] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -86.863221+0.002444j
[2025-09-06 08:07:44] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -86.786595-0.000769j
[2025-09-06 08:08:15] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -86.951280+0.001218j
[2025-09-06 08:08:46] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -86.863992-0.001110j
[2025-09-06 08:09:17] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -86.937449-0.003589j
[2025-09-06 08:09:48] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -86.913240-0.002021j
[2025-09-06 08:10:19] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -86.876168-0.000263j
[2025-09-06 08:10:50] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -86.785779+0.004726j
[2025-09-06 08:11:16] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -86.914989-0.004040j
[2025-09-06 08:11:47] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -86.834628-0.001436j
[2025-09-06 08:12:18] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -86.960313+0.002089j
[2025-09-06 08:12:49] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -86.790820+0.000200j
[2025-09-06 08:13:20] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -86.773933-0.000850j
[2025-09-06 08:13:50] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -86.936692+0.002497j
[2025-09-06 08:14:21] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -86.850340+0.002599j
[2025-09-06 08:14:52] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -86.782163+0.001682j
[2025-09-06 08:15:22] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -86.733658-0.002740j
[2025-09-06 08:15:53] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -86.851378-0.006004j
[2025-09-06 08:16:23] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -86.867767+0.002442j
[2025-09-06 08:16:54] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -86.820685-0.004941j
[2025-09-06 08:17:24] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -86.642985-0.005625j
[2025-09-06 08:17:55] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -86.730746-0.002092j
[2025-09-06 08:18:26] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -86.869200+0.002809j
[2025-09-06 08:18:56] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -86.932971-0.004017j
[2025-09-06 08:19:27] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -86.720842-0.003979j
[2025-09-06 08:19:57] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -86.864362-0.003495j
[2025-09-06 08:20:28] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -86.663552-0.001885j
[2025-09-06 08:20:59] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -86.768902+0.000593j
[2025-09-06 08:21:29] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -86.723387+0.000398j
[2025-09-06 08:22:00] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -86.689782-0.004557j
[2025-09-06 08:22:30] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -86.792320-0.003020j
[2025-09-06 08:23:01] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -86.860455-0.002239j
[2025-09-06 08:23:32] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -86.791848+0.001640j
[2025-09-06 08:24:02] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -86.898006-0.001732j
[2025-09-06 08:24:33] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -87.042134+0.000031j
[2025-09-06 08:25:03] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -87.077616+0.003349j
[2025-09-06 08:25:34] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -86.865008-0.001820j
[2025-09-06 08:26:02] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -86.925081+0.002878j
[2025-09-06 08:26:23] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -86.900015+0.003957j
[2025-09-06 08:26:50] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -86.891420+0.005321j
[2025-09-06 08:27:21] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -87.014049+0.001961j
[2025-09-06 08:27:52] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -87.055261-0.000964j
[2025-09-06 08:28:23] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -86.924478-0.000115j
[2025-09-06 08:28:54] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -86.844504+0.002586j
[2025-09-06 08:29:25] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -86.918347+0.000776j
[2025-09-06 08:29:55] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -86.865825+0.004511j
[2025-09-06 08:30:26] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -87.050436-0.005836j
[2025-09-06 08:30:56] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -87.071391-0.000727j
[2025-09-06 08:31:24] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -87.035086-0.000012j
[2025-09-06 08:31:54] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -87.033682-0.001652j
[2025-09-06 08:32:25] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -87.201717-0.003071j
[2025-09-06 08:32:56] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -87.017384-0.002797j
[2025-09-06 08:33:27] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -87.010531-0.001913j
[2025-09-06 08:33:58] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -86.978657-0.000475j
[2025-09-06 08:34:29] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -87.013518-0.000584j
[2025-09-06 08:35:00] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -87.219226-0.001349j
[2025-09-06 08:35:31] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -87.108048-0.007659j
[2025-09-06 08:36:02] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -86.791952-0.000307j
[2025-09-06 08:36:33] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -86.726653-0.003133j
[2025-09-06 08:37:04] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -86.844192+0.002965j
[2025-09-06 08:37:35] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -86.783106+0.000536j
[2025-09-06 08:38:05] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -86.920236+0.000734j
[2025-09-06 08:38:36] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -86.852437-0.001622j
[2025-09-06 08:39:07] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -86.845251+0.000806j
[2025-09-06 08:39:38] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -86.818577-0.002232j
[2025-09-06 08:40:09] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -86.815513-0.003878j
[2025-09-06 08:40:40] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -86.787987-0.001058j
[2025-09-06 08:41:11] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -86.779276-0.004103j
[2025-09-06 08:41:42] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -86.855596-0.002914j
[2025-09-06 08:42:13] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -86.788670-0.002171j
[2025-09-06 08:42:44] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -86.904785-0.001342j
[2025-09-06 08:43:14] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -86.957700+0.003872j
[2025-09-06 08:43:45] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -86.983636+0.000633j
[2025-09-06 08:44:16] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -86.857394+0.000886j
[2025-09-06 08:44:47] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -87.024842+0.005653j
[2025-09-06 08:45:18] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -86.944979-0.004906j
[2025-09-06 08:45:49] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -86.986572+0.000505j
[2025-09-06 08:46:12] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -87.035007-0.003401j
[2025-09-06 08:46:35] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -87.069942+0.003822j
[2025-09-06 08:47:06] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -86.906699+0.005959j
[2025-09-06 08:47:37] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -86.925209-0.001893j
[2025-09-06 08:48:08] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -86.872993-0.001427j
[2025-09-06 08:48:39] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -86.994300+0.001435j
[2025-09-06 08:49:10] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -86.836665+0.004065j
[2025-09-06 08:49:41] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -86.874077+0.001203j
[2025-09-06 08:50:12] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -86.818966+0.003210j
[2025-09-06 08:50:42] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -86.841959+0.001520j
[2025-09-06 08:51:09] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -86.870599+0.001020j
[2025-09-06 08:51:09] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-06 08:51:40] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -86.846168-0.001140j
[2025-09-06 08:52:10] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -86.814180+0.001301j
[2025-09-06 08:52:41] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -86.723888-0.000555j
[2025-09-06 08:53:12] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -86.737771-0.000521j
[2025-09-06 08:53:43] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -86.805958+0.002502j
[2025-09-06 08:54:14] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -86.598784-0.006571j
[2025-09-06 08:54:44] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -86.727410+0.000981j
[2025-09-06 08:55:15] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -86.561162-0.004060j
[2025-09-06 08:55:46] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -86.513232+0.001337j
[2025-09-06 08:56:17] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -86.778951-0.001147j
[2025-09-06 08:56:47] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -86.815802+0.002855j
[2025-09-06 08:57:18] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -86.868681+0.001249j
[2025-09-06 08:57:49] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -86.840482+0.005754j
[2025-09-06 08:58:20] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -86.934454-0.006255j
[2025-09-06 08:58:50] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -87.094154-0.003484j
[2025-09-06 08:59:21] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -86.887346+0.001295j
[2025-09-06 08:59:52] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -86.863910-0.000056j
[2025-09-06 09:00:23] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -87.002994-0.002921j
[2025-09-06 09:00:54] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -86.839186+0.000098j
[2025-09-06 09:01:25] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -86.777379+0.003107j
[2025-09-06 09:01:55] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -86.870950-0.000981j
[2025-09-06 09:02:26] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -86.755400-0.001559j
[2025-09-06 09:02:57] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -86.840176+0.002884j
[2025-09-06 09:03:28] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -86.887478+0.003029j
[2025-09-06 09:03:58] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -86.896930-0.002946j
[2025-09-06 09:04:29] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -86.966960-0.000094j
[2025-09-06 09:05:00] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -86.835939-0.001869j
[2025-09-06 09:05:31] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -86.943963+0.002844j
[2025-09-06 09:05:58] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -86.969638-0.002213j
[2025-09-06 09:06:18] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -87.092428+0.002615j
[2025-09-06 09:06:46] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -86.947990+0.003770j
[2025-09-06 09:07:17] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -86.815416-0.006219j
[2025-09-06 09:07:48] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -86.893734+0.001675j
[2025-09-06 09:08:19] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -86.811403+0.002906j
[2025-09-06 09:08:50] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -87.040983+0.002664j
[2025-09-06 09:09:21] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -86.815742+0.001572j
[2025-09-06 09:09:51] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -86.835883-0.001261j
[2025-09-06 09:10:22] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -86.714268+0.001860j
[2025-09-06 09:10:51] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -86.807575+0.001962j
[2025-09-06 09:11:19] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -86.935142+0.001856j
[2025-09-06 09:11:50] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -86.923084-0.000898j
[2025-09-06 09:12:21] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -86.999765+0.001092j
[2025-09-06 09:12:52] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -86.955252-0.002045j
[2025-09-06 09:13:23] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -87.121048+0.000253j
[2025-09-06 09:13:53] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -87.266886-0.000913j
[2025-09-06 09:13:53] RESTART #1 | Period: 300
[2025-09-06 09:14:24] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -87.238433+0.002767j
[2025-09-06 09:14:55] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -87.095070-0.000868j
[2025-09-06 09:15:26] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -86.884016+0.000396j
[2025-09-06 09:15:57] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -86.925130+0.000934j
[2025-09-06 09:16:28] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -86.817676-0.000847j
[2025-09-06 09:16:58] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -86.809788+0.001897j
[2025-09-06 09:17:29] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -86.910901+0.001644j
[2025-09-06 09:18:00] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -87.059383-0.003837j
[2025-09-06 09:18:31] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -87.076184-0.004377j
[2025-09-06 09:19:02] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -86.965124-0.002673j
[2025-09-06 09:19:33] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -87.032846-0.004510j
[2025-09-06 09:20:03] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -86.940138+0.000866j
[2025-09-06 09:20:34] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -86.830119+0.001442j
[2025-09-06 09:21:05] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -87.009549-0.005557j
[2025-09-06 09:21:36] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -86.798200-0.004695j
[2025-09-06 09:22:06] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -87.075460-0.000031j
[2025-09-06 09:22:37] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -86.975572+0.003572j
[2025-09-06 09:23:08] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -87.094560-0.007322j
[2025-09-06 09:23:39] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -87.081161-0.005383j
[2025-09-06 09:24:09] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -87.012828+0.000985j
[2025-09-06 09:24:40] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -87.047959-0.000900j
[2025-09-06 09:25:11] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -86.923338-0.000356j
[2025-09-06 09:25:42] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -86.990570+0.002396j
[2025-09-06 09:26:04] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -86.923422+0.002815j
[2025-09-06 09:26:27] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -86.718197+0.000113j
[2025-09-06 09:26:58] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -86.796996-0.001598j
[2025-09-06 09:27:29] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -86.797465-0.006455j
[2025-09-06 09:28:00] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -86.912919-0.005872j
[2025-09-06 09:28:31] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -86.827067-0.001540j
[2025-09-06 09:29:01] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -86.876770+0.000830j
[2025-09-06 09:29:32] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -86.906713-0.000855j
[2025-09-06 09:30:03] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -86.888822-0.002805j
[2025-09-06 09:30:34] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -86.979243+0.003494j
[2025-09-06 09:31:01] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -86.922117+0.001492j
[2025-09-06 09:31:31] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -86.891200-0.000208j
[2025-09-06 09:32:02] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -86.957077+0.002270j
[2025-09-06 09:32:33] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -87.067468+0.001362j
[2025-09-06 09:33:04] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -87.005932-0.001793j
[2025-09-06 09:33:35] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -87.065670-0.001748j
[2025-09-06 09:34:06] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -87.094669-0.000543j
[2025-09-06 09:34:37] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -86.989548-0.003192j
[2025-09-06 09:35:08] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -87.061512+0.000472j
[2025-09-06 09:35:39] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -86.756302-0.002566j
[2025-09-06 09:36:10] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -86.789299-0.003499j
[2025-09-06 09:36:41] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -86.799676+0.001598j
[2025-09-06 09:37:12] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -86.815401-0.002825j
[2025-09-06 09:37:43] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -86.654123+0.003681j
[2025-09-06 09:38:14] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -86.837100+0.003443j
[2025-09-06 09:38:45] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -86.933200-0.002236j
[2025-09-06 09:39:15] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -86.959660-0.003607j
[2025-09-06 09:39:46] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -86.980345-0.005134j
[2025-09-06 09:40:17] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -87.082500-0.004683j
[2025-09-06 09:40:48] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -87.007544-0.002568j
[2025-09-06 09:41:19] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -86.956996-0.001248j
[2025-09-06 09:41:50] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -86.857830+0.000795j
[2025-09-06 09:42:21] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -87.037833-0.002830j
[2025-09-06 09:42:52] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -87.101910+0.002559j
[2025-09-06 09:43:23] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -87.069827-0.001369j
[2025-09-06 09:43:54] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -86.980803+0.000879j
[2025-09-06 09:44:24] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -86.921708+0.000085j
[2025-09-06 09:44:24] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-06 09:44:55] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -87.004566-0.001019j
[2025-09-06 09:45:26] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -86.942943+0.003641j
[2025-09-06 09:45:54] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -86.894254+0.000894j
[2025-09-06 09:46:15] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -86.883444-0.001586j
[2025-09-06 09:46:44] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -86.923163+0.002408j
[2025-09-06 09:47:15] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -86.879086-0.000780j
[2025-09-06 09:47:46] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -86.702638+0.001371j
[2025-09-06 09:48:17] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -86.936664+0.002023j
[2025-09-06 09:48:47] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -86.844147-0.001246j
[2025-09-06 09:49:18] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -86.968484-0.001673j
[2025-09-06 09:49:49] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -86.971779+0.001071j
[2025-09-06 09:50:20] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -87.039665-0.004414j
[2025-09-06 09:50:48] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -86.847820-0.000240j
[2025-09-06 09:51:17] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -86.849464-0.000807j
[2025-09-06 09:51:48] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -87.052967+0.001880j
[2025-09-06 09:52:19] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -87.175613+0.000351j
[2025-09-06 09:52:50] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -86.966410-0.002713j
[2025-09-06 09:53:20] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -87.023569-0.004003j
[2025-09-06 09:53:51] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -86.969228-0.000559j
[2025-09-06 09:54:22] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -87.007589+0.001669j
[2025-09-06 09:54:53] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -87.018232+0.000985j
[2025-09-06 09:55:24] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -86.893353-0.006287j
[2025-09-06 09:55:55] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -86.890286+0.002407j
[2025-09-06 09:56:26] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -86.729688+0.003728j
[2025-09-06 09:56:56] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -86.859980+0.000550j
[2025-09-06 09:57:27] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -86.949226-0.000040j
[2025-09-06 09:57:58] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -86.927043+0.000870j
[2025-09-06 09:58:29] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -86.943765-0.001999j
[2025-09-06 09:59:00] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -86.906761-0.001664j
[2025-09-06 09:59:31] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -87.113067+0.003363j
[2025-09-06 10:00:02] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -86.966085+0.001446j
[2025-09-06 10:00:32] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -87.052288-0.003998j
[2025-09-06 10:01:03] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -86.971419-0.001449j
[2025-09-06 10:01:34] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -86.803182+0.003761j
[2025-09-06 10:02:05] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -86.835756+0.000847j
[2025-09-06 10:02:36] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -87.115098-0.000711j
[2025-09-06 10:03:07] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -87.033795-0.001163j
[2025-09-06 10:03:38] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -87.065466-0.001011j
[2025-09-06 10:04:09] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -87.031567-0.005578j
[2025-09-06 10:04:39] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -87.048125-0.000534j
[2025-09-06 10:05:10] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -86.971352+0.000939j
[2025-09-06 10:05:41] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -86.995212-0.001930j
[2025-09-06 10:06:02] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -87.060116-0.004061j
[2025-09-06 10:06:25] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -86.877424+0.003731j
[2025-09-06 10:06:56] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -86.696779+0.000236j
[2025-09-06 10:07:26] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -86.822568+0.005958j
[2025-09-06 10:07:57] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -86.851668-0.002855j
[2025-09-06 10:08:28] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -86.851519-0.003504j
[2025-09-06 10:08:59] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -86.857237+0.002291j
[2025-09-06 10:09:30] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -86.934398-0.001165j
[2025-09-06 10:10:01] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -86.897308+0.001443j
[2025-09-06 10:10:32] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -86.955444-0.002284j
[2025-09-06 10:10:59] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -86.749396-0.001173j
[2025-09-06 10:11:29] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -86.728404+0.004353j
[2025-09-06 10:11:59] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -86.991174-0.000952j
[2025-09-06 10:12:30] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -86.819190+0.001749j
[2025-09-06 10:13:01] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -86.900946+0.001183j
[2025-09-06 10:13:32] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -86.864127-0.004290j
[2025-09-06 10:14:02] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -86.922229-0.002355j
[2025-09-06 10:14:33] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -86.794614-0.001176j
[2025-09-06 10:15:04] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -86.843163-0.002519j
[2025-09-06 10:15:35] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -86.905859-0.000448j
[2025-09-06 10:16:06] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -86.970666+0.001314j
[2025-09-06 10:16:36] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -86.833369+0.002215j
[2025-09-06 10:17:07] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -86.845538+0.001793j
[2025-09-06 10:17:38] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -86.893010-0.002437j
[2025-09-06 10:18:08] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -86.880605+0.000192j
[2025-09-06 10:18:39] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -86.924835+0.001699j
[2025-09-06 10:19:10] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -86.876841+0.001227j
[2025-09-06 10:19:41] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -86.605292+0.001642j
[2025-09-06 10:20:11] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -86.836674-0.000921j
[2025-09-06 10:20:42] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -86.740734+0.005163j
[2025-09-06 10:21:13] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -86.733771+0.002048j
[2025-09-06 10:21:44] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -86.808557+0.000842j
[2025-09-06 10:22:15] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -86.720677+0.005368j
[2025-09-06 10:22:45] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -86.670898+0.005326j
[2025-09-06 10:23:16] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -86.786480-0.001671j
[2025-09-06 10:23:47] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -86.676075+0.001177j
[2025-09-06 10:24:17] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -86.839270+0.000657j
[2025-09-06 10:24:48] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -86.859024+0.000028j
[2025-09-06 10:25:19] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -86.745660-0.001581j
[2025-09-06 10:25:48] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -86.785195+0.001782j
[2025-09-06 10:26:08] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -86.924426+0.001379j
[2025-09-06 10:26:37] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -86.789028+0.001477j
[2025-09-06 10:27:08] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -86.699773+0.007699j
[2025-09-06 10:27:39] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -86.675892-0.001761j
[2025-09-06 10:28:09] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -86.748676-0.003060j
[2025-09-06 10:28:40] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -86.688129-0.002679j
[2025-09-06 10:29:11] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -86.831994+0.003036j
[2025-09-06 10:29:42] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -86.957415-0.003427j
[2025-09-06 10:30:13] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -86.854930-0.000467j
[2025-09-06 10:30:42] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -86.861244-0.001048j
[2025-09-06 10:31:10] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -86.854631+0.001233j
[2025-09-06 10:31:41] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -86.843593+0.000579j
[2025-09-06 10:32:12] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -86.801992+0.003553j
[2025-09-06 10:32:42] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -86.820317-0.000940j
[2025-09-06 10:33:13] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -86.822490-0.000903j
[2025-09-06 10:33:44] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -86.595401-0.002609j
[2025-09-06 10:34:15] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -86.841041-0.003566j
[2025-09-06 10:34:46] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -86.928620-0.000770j
[2025-09-06 10:35:16] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -86.870677+0.000198j
[2025-09-06 10:35:47] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -86.855619-0.003534j
[2025-09-06 10:36:18] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -87.002834+0.001408j
[2025-09-06 10:36:49] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -86.970663+0.001655j
[2025-09-06 10:37:19] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -86.929302+0.003861j
[2025-09-06 10:37:20] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-06 10:37:50] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -86.861663-0.000427j
[2025-09-06 10:38:21] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -86.926628-0.005216j
[2025-09-06 10:38:52] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -86.890029-0.002448j
[2025-09-06 10:39:23] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -86.976723-0.001914j
[2025-09-06 10:39:54] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -86.918831+0.001870j
[2025-09-06 10:40:24] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -86.960542-0.001392j
[2025-09-06 10:40:55] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -86.808804-0.000067j
[2025-09-06 10:41:26] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -86.707250+0.000548j
[2025-09-06 10:41:57] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -86.728895-0.003604j
[2025-09-06 10:42:28] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -86.740781-0.000845j
[2025-09-06 10:42:59] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -86.840701+0.004158j
[2025-09-06 10:43:29] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -86.928949-0.001179j
[2025-09-06 10:44:00] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -86.954567-0.002031j
[2025-09-06 10:44:31] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -86.755762+0.000027j
[2025-09-06 10:45:02] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -86.838799+0.001147j
[2025-09-06 10:45:32] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -86.944931+0.000471j
[2025-09-06 10:45:55] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -86.989455+0.002102j
[2025-09-06 10:46:19] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -86.969981-0.001761j
[2025-09-06 10:46:50] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -87.029578+0.002539j
[2025-09-06 10:47:21] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -86.968329+0.003868j
[2025-09-06 10:47:52] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -87.047316-0.005634j
[2025-09-06 10:48:23] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -86.887399-0.002464j
[2025-09-06 10:48:53] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -86.936988+0.000951j
[2025-09-06 10:49:24] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -86.996848+0.002529j
[2025-09-06 10:49:55] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -87.134535+0.003322j
[2025-09-06 10:50:26] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -87.074354+0.000917j
[2025-09-06 10:50:52] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -86.868576+0.003106j
[2025-09-06 10:51:23] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -86.997839-0.001646j
[2025-09-06 10:51:54] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -86.956965-0.000824j
[2025-09-06 10:52:25] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -87.044866-0.003059j
[2025-09-06 10:52:55] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -86.928894+0.002359j
[2025-09-06 10:53:26] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -86.982373-0.001418j
[2025-09-06 10:53:57] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -86.921294+0.004534j
[2025-09-06 10:54:28] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -86.938182+0.002943j
[2025-09-06 10:54:58] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -86.963162+0.002239j
[2025-09-06 10:55:29] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -86.848946-0.000653j
[2025-09-06 10:56:00] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -86.854602+0.000464j
[2025-09-06 10:56:31] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -86.935443+0.003326j
[2025-09-06 10:57:02] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -86.794132+0.001117j
[2025-09-06 10:57:33] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -87.008072-0.000593j
[2025-09-06 10:58:03] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -86.883625-0.002024j
[2025-09-06 10:58:34] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -86.893617-0.003230j
[2025-09-06 10:59:05] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -86.966221+0.000122j
[2025-09-06 10:59:36] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -86.928016+0.003454j
[2025-09-06 11:00:07] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -86.936419-0.005817j
[2025-09-06 11:00:38] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -86.897904-0.004736j
[2025-09-06 11:01:08] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -86.779843-0.003523j
[2025-09-06 11:01:39] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -86.743241+0.001748j
[2025-09-06 11:02:10] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -86.748731+0.003010j
[2025-09-06 11:02:41] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -86.849702+0.000687j
[2025-09-06 11:03:11] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -86.722965+0.002956j
[2025-09-06 11:03:42] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -86.898994+0.001848j
[2025-09-06 11:04:13] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -86.949719-0.001568j
[2025-09-06 11:04:44] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -86.901331+0.005869j
[2025-09-06 11:05:14] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -86.958813+0.000714j
[2025-09-06 11:05:41] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -86.842452-0.000069j
[2025-09-06 11:06:02] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -86.965140-0.000586j
[2025-09-06 11:06:32] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -86.997462+0.001182j
[2025-09-06 11:07:03] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -86.991043+0.000507j
[2025-09-06 11:07:33] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -86.888979+0.002018j
[2025-09-06 11:08:04] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -87.005434+0.002644j
[2025-09-06 11:08:35] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -87.047986-0.002201j
[2025-09-06 11:09:06] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -87.040618+0.001645j
[2025-09-06 11:09:37] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -86.919396-0.000465j
[2025-09-06 11:10:08] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -86.808470-0.003379j
[2025-09-06 11:10:36] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -87.006076+0.001887j
[2025-09-06 11:11:05] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -87.006725-0.000548j
[2025-09-06 11:11:36] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -86.909436+0.000183j
[2025-09-06 11:12:07] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -86.835582-0.004831j
[2025-09-06 11:12:38] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -87.021221-0.005682j
[2025-09-06 11:13:08] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -86.914316-0.003403j
[2025-09-06 11:13:39] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -86.686462-0.001431j
[2025-09-06 11:14:10] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -86.747989-0.002735j
[2025-09-06 11:14:41] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -86.655071-0.005434j
[2025-09-06 11:15:12] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -86.826079+0.002868j
[2025-09-06 11:15:43] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -86.781628+0.001969j
[2025-09-06 11:16:14] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -86.854918+0.000807j
[2025-09-06 11:16:45] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -86.774159+0.002242j
[2025-09-06 11:17:15] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -86.685649+0.001354j
[2025-09-06 11:17:46] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -86.642451-0.000640j
[2025-09-06 11:18:17] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -86.803544+0.001896j
[2025-09-06 11:18:48] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -86.819335+0.002695j
[2025-09-06 11:19:19] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -87.017355+0.001761j
[2025-09-06 11:19:50] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -86.975183-0.005150j
[2025-09-06 11:20:20] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -86.857092+0.000783j
[2025-09-06 11:20:51] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -86.868805+0.000156j
[2025-09-06 11:21:22] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -86.934048-0.000011j
[2025-09-06 11:21:53] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -86.911904-0.000007j
[2025-09-06 11:22:24] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -86.790469+0.000856j
[2025-09-06 11:22:55] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -86.713607-0.010355j
[2025-09-06 11:23:26] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -86.838616+0.000246j
[2025-09-06 11:23:56] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -86.884351+0.001123j
[2025-09-06 11:24:27] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -86.888474-0.003050j
[2025-09-06 11:24:58] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -87.024611+0.000527j
[2025-09-06 11:25:29] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -86.933822-0.002369j
[2025-09-06 11:25:49] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -86.906898-0.004972j
[2025-09-06 11:26:15] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -86.842793-0.000176j
[2025-09-06 11:26:46] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -86.889899+0.001160j
[2025-09-06 11:27:17] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -86.765330-0.000730j
[2025-09-06 11:27:48] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -86.951193+0.000890j
[2025-09-06 11:28:19] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -86.794600+0.002417j
[2025-09-06 11:28:50] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -86.760397-0.002406j
[2025-09-06 11:29:21] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -86.789582-0.003535j
[2025-09-06 11:29:52] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -86.782744-0.001409j
[2025-09-06 11:30:22] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -86.713178-0.002161j
[2025-09-06 11:30:23] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-06 11:30:49] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -86.588065-0.004256j
[2025-09-06 11:31:20] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -86.686942-0.001845j
[2025-09-06 11:31:51] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -86.688045+0.001439j
[2025-09-06 11:32:22] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -86.876476+0.001211j
[2025-09-06 11:32:53] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -86.914020+0.000137j
[2025-09-06 11:33:24] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -86.947122+0.003496j
[2025-09-06 11:33:55] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -86.901406+0.000416j
[2025-09-06 11:34:26] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -86.951640+0.002863j
[2025-09-06 11:34:56] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -87.097582+0.000929j
[2025-09-06 11:35:27] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -86.931289-0.001484j
[2025-09-06 11:35:58] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -86.951905-0.005618j
[2025-09-06 11:36:29] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -86.971715+0.003698j
[2025-09-06 11:37:00] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -86.837973+0.000347j
[2025-09-06 11:37:31] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -86.898461+0.000304j
[2025-09-06 11:38:02] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -86.873452+0.002541j
[2025-09-06 11:38:33] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -86.947083-0.000311j
[2025-09-06 11:39:03] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -86.915320+0.002182j
[2025-09-06 11:39:34] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -86.813918-0.002310j
[2025-09-06 11:40:05] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -86.795671-0.003521j
[2025-09-06 11:40:36] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -86.918771+0.004752j
[2025-09-06 11:41:07] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -86.970991+0.000935j
[2025-09-06 11:41:38] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -86.861423+0.000210j
[2025-09-06 11:42:09] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -86.662134+0.000790j
[2025-09-06 11:42:39] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -86.709620+0.003186j
[2025-09-06 11:43:10] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -86.963352+0.000051j
[2025-09-06 11:43:41] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -86.970568+0.006944j
[2025-09-06 11:44:12] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -86.959999+0.001903j
[2025-09-06 11:44:43] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -86.831569+0.000452j
[2025-09-06 11:45:14] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -86.851984-0.003266j
[2025-09-06 11:45:38] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -86.621819+0.001609j
[2025-09-06 11:45:38] RESTART #2 | Period: 600
[2025-09-06 11:45:59] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -86.748929+0.003842j
[2025-09-06 11:46:29] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -86.720176-0.004578j
[2025-09-06 11:47:00] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -86.756152-0.002649j
[2025-09-06 11:47:31] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -86.700721+0.001054j
[2025-09-06 11:48:02] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -86.799684-0.000253j
[2025-09-06 11:48:33] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -86.906097+0.007709j
[2025-09-06 11:49:03] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -87.026097-0.000453j
[2025-09-06 11:49:34] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -86.882006+0.005860j
[2025-09-06 11:50:05] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -86.917407+0.001913j
[2025-09-06 11:50:34] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -86.788491+0.001928j
[2025-09-06 11:51:02] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -86.950597-0.004978j
[2025-09-06 11:51:33] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -86.995984-0.003433j
[2025-09-06 11:52:04] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -86.931512-0.002896j
[2025-09-06 11:52:34] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -86.944055-0.002256j
[2025-09-06 11:53:05] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -86.747537+0.003651j
[2025-09-06 11:53:36] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -86.837103+0.001413j
[2025-09-06 11:54:07] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -86.781861-0.004226j
[2025-09-06 11:54:38] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -86.725078+0.000572j
[2025-09-06 11:55:08] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -86.839721+0.004540j
[2025-09-06 11:55:39] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -86.810141+0.000149j
[2025-09-06 11:56:10] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -86.821888+0.004686j
[2025-09-06 11:56:41] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -86.846206+0.003261j
[2025-09-06 11:57:12] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -86.824281+0.000924j
[2025-09-06 11:57:43] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -86.979664-0.000188j
[2025-09-06 11:58:13] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -86.926770+0.001379j
[2025-09-06 11:58:44] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -86.809310-0.001068j
[2025-09-06 11:59:15] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -86.907570+0.002816j
[2025-09-06 11:59:46] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -86.894412-0.001798j
[2025-09-06 12:00:17] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -86.824523+0.002530j
[2025-09-06 12:00:47] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -86.765167+0.000880j
[2025-09-06 12:01:18] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -86.847750-0.003703j
[2025-09-06 12:01:49] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -86.695994-0.000595j
[2025-09-06 12:02:20] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -86.764308-0.001301j
[2025-09-06 12:02:51] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -86.814952+0.001545j
[2025-09-06 12:03:21] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -86.908704-0.001171j
[2025-09-06 12:03:52] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -86.822789+0.001911j
[2025-09-06 12:04:23] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -86.927245+0.001705j
[2025-09-06 12:04:54] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -86.766469-0.000599j
[2025-09-06 12:05:24] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -86.826048-0.000564j
[2025-09-06 12:05:45] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -86.891673-0.001406j
[2025-09-06 12:06:11] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -86.969716+0.007814j
[2025-09-06 12:06:42] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -86.912912-0.000154j
[2025-09-06 12:07:13] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -87.023571+0.003324j
[2025-09-06 12:07:44] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -87.000264-0.002072j
[2025-09-06 12:08:15] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -87.038082-0.000800j
[2025-09-06 12:08:46] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -87.088045-0.008383j
[2025-09-06 12:09:17] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -87.091278+0.000817j
[2025-09-06 12:09:48] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -87.055160-0.003961j
[2025-09-06 12:10:19] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -86.878012+0.002025j
[2025-09-06 12:10:45] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -86.883986+0.000407j
[2025-09-06 12:11:16] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -86.767594+0.004738j
[2025-09-06 12:11:46] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -86.830537+0.005167j
[2025-09-06 12:12:17] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -86.908806+0.001359j
[2025-09-06 12:12:48] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -86.928392-0.000465j
[2025-09-06 12:13:19] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -86.868479-0.005751j
[2025-09-06 12:13:50] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -86.953389+0.000780j
[2025-09-06 12:14:20] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -87.132167+0.001058j
[2025-09-06 12:14:51] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -87.254216-0.002091j
[2025-09-06 12:15:22] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -87.225436-0.004959j
[2025-09-06 12:15:53] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -87.228848+0.000826j
[2025-09-06 12:16:24] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -87.024921+0.000369j
[2025-09-06 12:16:54] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -87.056312-0.000878j
[2025-09-06 12:17:25] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -87.103057+0.000279j
[2025-09-06 12:17:56] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -86.981650+0.000329j
[2025-09-06 12:18:27] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -86.922044+0.000332j
[2025-09-06 12:18:58] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -86.878995-0.003769j
[2025-09-06 12:19:28] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -86.930294+0.004457j
[2025-09-06 12:19:59] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -86.890432-0.004772j
[2025-09-06 12:20:30] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -86.935108-0.000580j
[2025-09-06 12:21:01] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -86.816866-0.002303j
[2025-09-06 12:21:31] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -87.008060+0.001435j
[2025-09-06 12:22:02] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -86.928783+0.002466j
[2025-09-06 12:22:33] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -86.922220+0.002237j
[2025-09-06 12:23:04] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -87.148649-0.003289j
[2025-09-06 12:23:35] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -87.088918-0.000060j
[2025-09-06 12:23:35] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-06 12:24:05] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -86.909973+0.000038j
[2025-09-06 12:24:36] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -86.972798+0.002278j
[2025-09-06 12:25:07] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -87.055266-0.005917j
[2025-09-06 12:25:32] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -87.067286+0.001293j
[2025-09-06 12:25:53] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -86.981614-0.009834j
[2025-09-06 12:26:21] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -86.935234-0.001603j
[2025-09-06 12:26:52] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -86.857759+0.000204j
[2025-09-06 12:27:23] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -86.982818-0.000566j
[2025-09-06 12:27:54] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -86.899442-0.003367j
[2025-09-06 12:28:25] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -86.886999+0.005142j
[2025-09-06 12:28:55] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -86.920397-0.001520j
[2025-09-06 12:29:26] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -86.881219+0.003609j
[2025-09-06 12:29:57] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -86.927950-0.001936j
[2025-09-06 12:30:25] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -86.934400+0.000297j
[2025-09-06 12:30:54] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -86.825710+0.000201j
[2025-09-06 12:31:25] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -86.922944+0.001250j
[2025-09-06 12:31:56] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -86.923525-0.001535j
[2025-09-06 12:32:27] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -86.826019+0.006489j
[2025-09-06 12:32:58] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -86.948920-0.000695j
[2025-09-06 12:33:29] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -87.003657+0.001258j
[2025-09-06 12:34:00] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -86.968613+0.003039j
[2025-09-06 12:34:31] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -86.774803-0.001818j
[2025-09-06 12:35:02] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -86.880885+0.006177j
[2025-09-06 12:35:33] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -86.752447-0.005720j
[2025-09-06 12:36:04] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -86.824910+0.000293j
[2025-09-06 12:36:34] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -86.847643+0.003150j
[2025-09-06 12:37:05] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -86.966897-0.001766j
[2025-09-06 12:37:36] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -86.939063-0.000425j
[2025-09-06 12:38:07] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -86.897560-0.005470j
[2025-09-06 12:38:38] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -86.859261-0.003643j
[2025-09-06 12:39:09] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -86.797710-0.003639j
[2025-09-06 12:39:40] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -86.778035+0.001727j
[2025-09-06 12:40:11] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -86.783362+0.002634j
[2025-09-06 12:40:42] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -86.781822-0.001921j
[2025-09-06 12:41:13] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -86.803773-0.001847j
[2025-09-06 12:41:44] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -86.782651+0.000284j
[2025-09-06 12:42:15] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -86.727014+0.003074j
[2025-09-06 12:42:46] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -86.729907-0.001916j
[2025-09-06 12:43:16] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -86.785638-0.000422j
[2025-09-06 12:43:47] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -86.883347-0.000321j
[2025-09-06 12:44:18] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -86.805503-0.000726j
[2025-09-06 12:44:49] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -86.755006-0.005636j
[2025-09-06 12:45:20] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -86.936891+0.000596j
[2025-09-06 12:45:42] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -86.955298-0.001682j
[2025-09-06 12:46:07] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -86.735710+0.004284j
[2025-09-06 12:46:38] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -86.644399+0.000424j
[2025-09-06 12:47:09] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -86.705712+0.001792j
[2025-09-06 12:47:40] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -86.677880+0.001298j
[2025-09-06 12:48:11] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -86.739491+0.001230j
[2025-09-06 12:48:41] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -86.749249-0.002202j
[2025-09-06 12:49:12] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -86.834074-0.002323j
[2025-09-06 12:49:43] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -86.837364-0.001841j
[2025-09-06 12:50:14] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -86.842802-0.000255j
[2025-09-06 12:50:41] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -86.909669-0.001507j
[2025-09-06 12:51:11] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -87.041212+0.000000j
[2025-09-06 12:51:42] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -86.883710-0.002950j
[2025-09-06 12:52:13] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -87.009907-0.004498j
[2025-09-06 12:52:44] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -86.774384+0.002925j
[2025-09-06 12:53:15] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -86.886470+0.000765j
[2025-09-06 12:53:46] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -86.759401+0.001201j
[2025-09-06 12:54:17] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -86.734435-0.000359j
[2025-09-06 12:54:47] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -86.769031-0.002568j
[2025-09-06 12:55:18] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -86.822591-0.003875j
[2025-09-06 12:55:49] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -86.841065-0.004372j
[2025-09-06 12:56:20] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -86.770495-0.000165j
[2025-09-06 12:56:51] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -86.902288+0.000662j
[2025-09-06 12:57:22] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -86.912523-0.002223j
[2025-09-06 12:57:53] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -86.876354+0.002858j
[2025-09-06 12:58:24] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -86.820345-0.001012j
[2025-09-06 12:58:54] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -86.876288+0.003512j
[2025-09-06 12:59:25] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -86.784382-0.001137j
[2025-09-06 12:59:56] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -86.821414+0.000227j
[2025-09-06 13:00:27] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -86.881160+0.000163j
[2025-09-06 13:00:58] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -86.934895+0.001077j
[2025-09-06 13:01:29] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -86.822811+0.000234j
[2025-09-06 13:02:00] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -86.892512+0.005796j
[2025-09-06 13:02:30] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -86.944135+0.000895j
[2025-09-06 13:03:01] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -86.910788+0.004681j
[2025-09-06 13:03:32] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -86.984664+0.002907j
[2025-09-06 13:04:03] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -86.956211-0.000574j
[2025-09-06 13:04:34] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -86.871103+0.002566j
[2025-09-06 13:05:05] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -86.952604+0.002625j
[2025-09-06 13:05:30] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -86.899623-0.003126j
[2025-09-06 13:05:51] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -86.831978+0.001165j
[2025-09-06 13:06:22] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -86.866064+0.001793j
[2025-09-06 13:06:52] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -86.957769+0.001179j
[2025-09-06 13:07:23] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -86.863877-0.001982j
[2025-09-06 13:07:54] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -87.088758+0.000354j
[2025-09-06 13:08:25] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -86.986723+0.002848j
[2025-09-06 13:08:56] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -86.937986-0.004845j
[2025-09-06 13:09:27] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -86.883370+0.002847j
[2025-09-06 13:09:58] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -86.986181+0.001920j
[2025-09-06 13:10:26] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -86.906380+0.002246j
[2025-09-06 13:10:55] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -87.046169-0.001183j
[2025-09-06 13:11:26] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -86.976531-0.000142j
[2025-09-06 13:11:57] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -86.939607+0.001706j
[2025-09-06 13:12:28] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -86.987269+0.001063j
[2025-09-06 13:12:59] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -86.972598-0.003898j
[2025-09-06 13:13:30] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -87.071462+0.001043j
[2025-09-06 13:14:01] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -86.993949+0.004702j
[2025-09-06 13:14:32] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -86.951127+0.001668j
[2025-09-06 13:15:03] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -86.970797-0.005835j
[2025-09-06 13:15:33] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -87.022607-0.003876j
[2025-09-06 13:16:04] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -86.927371+0.001945j
[2025-09-06 13:16:35] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -87.072687+0.000083j
[2025-09-06 13:16:35] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-06 13:17:06] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -86.934151-0.002059j
[2025-09-06 13:17:37] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -86.860881+0.000118j
[2025-09-06 13:18:08] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -86.918569-0.001963j
[2025-09-06 13:18:39] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -87.119950+0.000329j
[2025-09-06 13:19:10] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -87.044491+0.002273j
[2025-09-06 13:19:41] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -86.812524-0.005469j
[2025-09-06 13:20:12] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -86.852371-0.000266j
[2025-09-06 13:20:42] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -86.753731+0.000032j
[2025-09-06 13:21:13] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -86.734282-0.003283j
[2025-09-06 13:21:44] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -86.823476-0.005395j
[2025-09-06 13:22:15] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -86.937284-0.000634j
[2025-09-06 13:22:46] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -86.838754-0.002672j
[2025-09-06 13:23:17] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -86.833639-0.001114j
[2025-09-06 13:23:48] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -86.703820-0.000584j
[2025-09-06 13:24:19] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -86.661131+0.001054j
[2025-09-06 13:24:50] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -86.788020+0.000080j
[2025-09-06 13:25:19] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -86.796230+0.003572j
[2025-09-06 13:25:39] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -86.933628-0.000370j
[2025-09-06 13:26:06] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -86.880229+0.001699j
[2025-09-06 13:26:36] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -86.824869+0.000703j
[2025-09-06 13:27:07] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -86.866701+0.000080j
[2025-09-06 13:27:38] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -86.871046+0.001821j
[2025-09-06 13:28:09] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -86.880331+0.000098j
[2025-09-06 13:28:40] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -86.900227+0.002730j
[2025-09-06 13:29:11] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -86.819660-0.001109j
[2025-09-06 13:29:42] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -86.769148+0.000397j
[2025-09-06 13:30:13] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -86.810843+0.002727j
[2025-09-06 13:30:39] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -86.833373-0.000644j
[2025-09-06 13:31:10] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -86.901113-0.004253j
[2025-09-06 13:31:40] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -86.869158-0.002179j
[2025-09-06 13:32:11] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -86.882136+0.000803j
[2025-09-06 13:32:42] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -86.891704-0.001912j
[2025-09-06 13:33:13] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -86.954700+0.001638j
[2025-09-06 13:33:43] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -86.765521+0.002138j
[2025-09-06 13:34:14] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -86.933771-0.000812j
[2025-09-06 13:34:45] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -86.867557-0.003300j
[2025-09-06 13:35:16] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -87.008706+0.004441j
[2025-09-06 13:35:46] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -86.988994-0.000819j
[2025-09-06 13:36:17] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -86.972303+0.002917j
[2025-09-06 13:36:48] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -86.952359+0.003160j
[2025-09-06 13:37:19] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -86.972169-0.004971j
[2025-09-06 13:37:49] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -86.832820-0.001558j
[2025-09-06 13:38:20] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -86.834395+0.002716j
[2025-09-06 13:38:51] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -86.901247-0.000433j
[2025-09-06 13:39:22] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -86.869192+0.002972j
[2025-09-06 13:39:52] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -86.903068+0.001883j
[2025-09-06 13:40:23] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -86.881128+0.001908j
[2025-09-06 13:40:54] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -86.714835+0.000047j
[2025-09-06 13:41:25] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -86.727256-0.000112j
[2025-09-06 13:41:55] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -86.852010+0.001690j
[2025-09-06 13:42:26] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -86.850271+0.002709j
[2025-09-06 13:42:57] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -86.772987+0.003309j
[2025-09-06 13:43:27] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -86.693242-0.005289j
[2025-09-06 13:43:58] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -86.741786+0.001055j
[2025-09-06 13:44:29] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -86.961123-0.005388j
[2025-09-06 13:45:00] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -87.014948-0.000716j
[2025-09-06 13:45:25] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -87.068421-0.000044j
[2025-09-06 13:45:46] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -86.833673-0.002309j
[2025-09-06 13:46:17] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -86.852067-0.002125j
[2025-09-06 13:46:48] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -86.932098+0.004348j
[2025-09-06 13:47:19] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -86.836036+0.004967j
[2025-09-06 13:47:49] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -86.796234+0.001171j
[2025-09-06 13:48:20] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -86.794886+0.001353j
[2025-09-06 13:48:51] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -86.729629+0.000170j
[2025-09-06 13:49:22] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -86.812265-0.000996j
[2025-09-06 13:49:53] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -86.893669-0.003196j
[2025-09-06 13:50:20] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -87.008087-0.003516j
[2025-09-06 13:50:50] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -86.894888+0.000269j
[2025-09-06 13:51:21] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -87.023434-0.001623j
[2025-09-06 13:51:52] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -86.982060-0.006526j
[2025-09-06 13:52:22] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -87.016908-0.002194j
[2025-09-06 13:52:53] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -86.839111-0.006369j
[2025-09-06 13:53:24] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -86.936760-0.001090j
[2025-09-06 13:53:55] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -87.071275-0.002628j
[2025-09-06 13:54:26] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -86.879803-0.002022j
[2025-09-06 13:54:57] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -86.760055-0.001572j
[2025-09-06 13:55:28] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -86.732238-0.000661j
[2025-09-06 13:55:59] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -86.703173-0.000516j
[2025-09-06 13:56:30] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -86.762724+0.004235j
[2025-09-06 13:57:00] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -86.815783+0.001661j
[2025-09-06 13:57:31] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -86.712923-0.000538j
[2025-09-06 13:58:02] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -86.629921+0.000254j
[2025-09-06 13:58:33] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -86.812980+0.001508j
[2025-09-06 13:59:04] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -86.974531+0.002878j
[2025-09-06 13:59:35] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -86.799737+0.005043j
[2025-09-06 14:00:06] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -86.735418+0.005851j
[2025-09-06 14:00:37] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -86.828057+0.002567j
[2025-09-06 14:01:07] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -86.761286+0.002492j
[2025-09-06 14:01:38] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -87.068603-0.000601j
[2025-09-06 14:02:09] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -87.051331+0.004249j
[2025-09-06 14:02:40] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -86.902707-0.001328j
[2025-09-06 14:03:11] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -87.017608+0.002509j
[2025-09-06 14:03:42] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -86.989403-0.000047j
[2025-09-06 14:04:13] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -86.969177-0.003675j
[2025-09-06 14:04:44] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -86.990162-0.000499j
[2025-09-06 14:05:13] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -86.973430+0.002662j
[2025-09-06 14:05:33] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -86.872381-0.001492j
[2025-09-06 14:06:02] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -86.671023-0.002460j
[2025-09-06 14:06:33] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -86.819062-0.000039j
[2025-09-06 14:07:04] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -86.743490-0.002458j
[2025-09-06 14:07:35] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -86.754352-0.002353j
[2025-09-06 14:08:06] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -86.804550-0.001230j
[2025-09-06 14:08:37] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -86.888739+0.003164j
[2025-09-06 14:09:08] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -86.937187-0.003424j
[2025-09-06 14:09:39] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -86.942121-0.001910j
[2025-09-06 14:09:39] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-06 14:10:07] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -86.922650-0.003081j
[2025-09-06 14:10:36] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -86.964294-0.003498j
[2025-09-06 14:11:07] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -86.926342+0.000637j
[2025-09-06 14:11:38] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -86.877911+0.003642j
[2025-09-06 14:12:08] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -86.827555-0.000704j
[2025-09-06 14:12:39] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -86.956189+0.004587j
[2025-09-06 14:13:10] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -86.987177-0.003714j
[2025-09-06 14:13:41] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -86.996373+0.000370j
[2025-09-06 14:14:12] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -86.798228+0.000375j
[2025-09-06 14:14:43] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -86.824828-0.001768j
[2025-09-06 14:15:14] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -86.766209+0.001500j
[2025-09-06 14:15:44] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -86.762503-0.001215j
[2025-09-06 14:16:15] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -86.770860+0.002121j
[2025-09-06 14:16:46] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -86.816363-0.007491j
[2025-09-06 14:17:17] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -86.769153-0.002036j
[2025-09-06 14:17:48] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -86.790985+0.001424j
[2025-09-06 14:18:19] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -86.767244-0.002437j
[2025-09-06 14:18:50] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -86.886566+0.002763j
[2025-09-06 14:19:20] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -86.948901-0.001482j
[2025-09-06 14:19:51] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -87.010165-0.004550j
[2025-09-06 14:20:22] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -86.775759-0.002888j
[2025-09-06 14:20:53] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -87.001560-0.000739j
[2025-09-06 14:21:24] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -86.863181-0.000938j
[2025-09-06 14:21:55] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -86.938709-0.000978j
[2025-09-06 14:22:26] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -86.891160-0.002192j
[2025-09-06 14:22:56] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -86.865792-0.001277j
[2025-09-06 14:23:27] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -86.931329-0.000747j
[2025-09-06 14:23:58] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -86.985966-0.004422j
[2025-09-06 14:24:29] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -86.939301-0.000144j
[2025-09-06 14:25:00] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -86.826384+0.000720j
[2025-09-06 14:25:21] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -86.859631+0.000456j
[2025-09-06 14:25:45] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -86.843366+0.006662j
[2025-09-06 14:26:15] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -86.789730+0.000280j
[2025-09-06 14:26:46] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -86.813313-0.003664j
[2025-09-06 14:27:17] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -86.905818-0.000569j
[2025-09-06 14:27:48] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -86.942189-0.006170j
[2025-09-06 14:28:19] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -87.052754-0.000787j
[2025-09-06 14:28:50] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -87.120574+0.000233j
[2025-09-06 14:29:21] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -87.033411-0.001104j
[2025-09-06 14:29:51] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -86.939122-0.000429j
[2025-09-06 14:30:17] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -86.940039-0.005330j
[2025-09-06 14:30:48] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -86.779355-0.000225j
[2025-09-06 14:31:19] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -86.974455-0.002429j
[2025-09-06 14:31:50] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -87.008739+0.001379j
[2025-09-06 14:32:21] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -86.952667-0.001623j
[2025-09-06 14:32:52] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -87.023756-0.001499j
[2025-09-06 14:33:23] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -86.900170-0.000078j
[2025-09-06 14:33:53] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -87.027634-0.001691j
[2025-09-06 14:34:24] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -86.870717+0.000274j
[2025-09-06 14:34:55] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -86.935466+0.002424j
[2025-09-06 14:35:26] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -86.693618-0.001737j
[2025-09-06 14:35:57] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -86.880366+0.002574j
[2025-09-06 14:36:28] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -86.843210-0.003485j
[2025-09-06 14:36:59] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -86.809575+0.002424j
[2025-09-06 14:37:29] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -86.867687+0.003912j
[2025-09-06 14:38:00] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -86.985330-0.001220j
[2025-09-06 14:38:31] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -86.884366-0.002150j
[2025-09-06 14:39:02] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -86.893628-0.005697j
[2025-09-06 14:39:33] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -86.878265-0.000636j
[2025-09-06 14:40:03] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -86.949510+0.000823j
[2025-09-06 14:40:34] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -86.811441+0.002382j
[2025-09-06 14:41:05] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -87.050673+0.003434j
[2025-09-06 14:41:36] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -86.932126-0.003764j
[2025-09-06 14:42:07] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -86.878348+0.001789j
[2025-09-06 14:42:38] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -86.924692-0.000366j
[2025-09-06 14:43:08] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -86.972433+0.000789j
[2025-09-06 14:43:39] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -86.853333-0.003121j
[2025-09-06 14:44:10] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -86.793978+0.004485j
[2025-09-06 14:44:41] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -86.854200+0.002011j
[2025-09-06 14:45:08] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -86.783657-0.000135j
[2025-09-06 14:45:29] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -86.900887+0.005109j
[2025-09-06 14:45:55] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -86.859742-0.000142j
[2025-09-06 14:46:26] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -86.868756-0.005608j
[2025-09-06 14:46:57] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -86.938476+0.003465j
[2025-09-06 14:47:28] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -86.956646-0.002100j
[2025-09-06 14:47:59] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -86.874260-0.007021j
[2025-09-06 14:48:30] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -87.062123-0.002589j
[2025-09-06 14:49:01] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -86.928083-0.001362j
[2025-09-06 14:49:32] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -86.979998-0.003946j
[2025-09-06 14:50:02] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -86.967139+0.002614j
[2025-09-06 14:50:29] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -86.974370-0.000369j
[2025-09-06 14:51:00] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -86.938061+0.000989j
[2025-09-06 14:51:31] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -86.808043-0.001897j
[2025-09-06 14:52:02] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -86.899934+0.002722j
[2025-09-06 14:52:32] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -86.796179-0.004159j
[2025-09-06 14:53:03] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -86.958254-0.000499j
[2025-09-06 14:53:34] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -86.891111-0.002195j
[2025-09-06 14:54:05] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -86.936282+0.001093j
[2025-09-06 14:54:36] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -87.097859+0.001503j
[2025-09-06 14:55:07] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -87.068014+0.002597j
[2025-09-06 14:55:38] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -87.148266+0.002817j
[2025-09-06 14:56:08] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -86.917631+0.003622j
[2025-09-06 14:56:39] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -86.967935+0.007452j
[2025-09-06 14:57:10] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -87.035026-0.003307j
[2025-09-06 14:57:41] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -86.863095+0.004663j
[2025-09-06 14:58:12] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -86.852170-0.002251j
[2025-09-06 14:58:42] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -86.735511+0.003412j
[2025-09-06 14:59:13] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -86.730371-0.000434j
[2025-09-06 14:59:44] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -86.680288-0.003903j
[2025-09-06 15:00:15] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -86.701929+0.004276j
[2025-09-06 15:00:46] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -86.745167+0.000962j
[2025-09-06 15:01:16] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -86.596734-0.000401j
[2025-09-06 15:01:47] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -86.698538-0.003859j
[2025-09-06 15:02:18] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -86.820591-0.000695j
[2025-09-06 15:02:49] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -86.789365-0.004551j
[2025-09-06 15:02:49] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-06 15:03:20] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -86.863890-0.000849j
[2025-09-06 15:03:51] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -86.765339+0.001804j
[2025-09-06 15:04:21] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -86.769803+0.001498j
[2025-09-06 15:04:52] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -86.800004-0.001831j
[2025-09-06 15:05:16] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -86.820373+0.000177j
[2025-09-06 15:05:38] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -86.812576+0.004668j
[2025-09-06 15:06:09] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -86.804653+0.000890j
[2025-09-06 15:06:40] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -86.819216-0.002005j
[2025-09-06 15:07:11] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -86.747272-0.000834j
[2025-09-06 15:07:42] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -86.652522-0.002031j
[2025-09-06 15:08:13] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -86.610315-0.000008j
[2025-09-06 15:08:44] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -86.810847-0.002923j
[2025-09-06 15:09:15] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -86.713500-0.005483j
[2025-09-06 15:09:46] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -86.818920-0.000984j
[2025-09-06 15:10:13] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -86.976058-0.000430j
[2025-09-06 15:10:43] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -87.071251+0.000993j
[2025-09-06 15:11:14] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -86.873332-0.003798j
[2025-09-06 15:11:45] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -86.808195+0.000130j
[2025-09-06 15:12:16] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -87.090888+0.000655j
[2025-09-06 15:12:46] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -86.885205+0.001709j
[2025-09-06 15:13:17] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -86.857929-0.002636j
[2025-09-06 15:13:48] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -86.862584+0.002461j
[2025-09-06 15:14:19] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -86.909024+0.003163j
[2025-09-06 15:14:50] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -86.873419+0.002182j
[2025-09-06 15:15:21] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -86.944063-0.005304j
[2025-09-06 15:15:51] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -86.830700+0.001400j
[2025-09-06 15:16:22] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -86.830973-0.000855j
[2025-09-06 15:16:53] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -86.906489+0.001701j
[2025-09-06 15:17:24] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -86.993323+0.000314j
[2025-09-06 15:17:55] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -86.983279-0.002825j
[2025-09-06 15:18:25] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -87.067494-0.001238j
[2025-09-06 15:18:56] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -87.226106-0.000629j
[2025-09-06 15:19:27] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -87.232978+0.000417j
[2025-09-06 15:19:58] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -87.112459-0.000675j
[2025-09-06 15:20:29] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -87.158441-0.003430j
[2025-09-06 15:21:00] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -87.149010+0.001372j
[2025-09-06 15:21:31] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -87.241071-0.004716j
[2025-09-06 15:22:01] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -87.065331+0.002259j
[2025-09-06 15:22:32] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -87.043013+0.003008j
[2025-09-06 15:23:03] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -87.011731-0.001765j
[2025-09-06 15:23:34] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -86.976309+0.000642j
[2025-09-06 15:24:05] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -86.905073+0.001845j
[2025-09-06 15:24:35] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -87.001761-0.001558j
[2025-09-06 15:25:04] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -86.918968+0.002974j
[2025-09-06 15:25:24] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -87.025848+0.001469j
[2025-09-06 15:25:52] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -87.143156-0.003016j
[2025-09-06 15:26:23] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -86.976952-0.000579j
[2025-09-06 15:26:54] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -86.933584+0.004040j
[2025-09-06 15:27:24] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -87.065229+0.005027j
[2025-09-06 15:27:55] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -86.880890-0.000407j
[2025-09-06 15:28:26] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -86.940643-0.004830j
[2025-09-06 15:28:57] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -86.892074-0.000817j
[2025-09-06 15:29:28] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -86.726251+0.000114j
[2025-09-06 15:29:57] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -86.898227-0.003311j
[2025-09-06 15:30:25] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -86.862824-0.001323j
[2025-09-06 15:30:56] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -86.894896+0.002112j
[2025-09-06 15:31:27] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -86.765576+0.000582j
[2025-09-06 15:31:57] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -86.951249-0.001279j
[2025-09-06 15:32:28] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -86.973323+0.000289j
[2025-09-06 15:32:59] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -86.799787-0.001774j
[2025-09-06 15:33:30] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -86.767472-0.002128j
[2025-09-06 15:34:00] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -86.809605+0.001875j
[2025-09-06 15:34:31] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -86.831087+0.002422j
[2025-09-06 15:35:02] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -86.941534-0.002789j
[2025-09-06 15:35:33] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -86.865419+0.001343j
[2025-09-06 15:36:04] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -86.802038-0.000557j
[2025-09-06 15:36:35] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -86.789880-0.003552j
[2025-09-06 15:37:06] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -86.908515+0.001254j
[2025-09-06 15:37:36] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -86.723727-0.002434j
[2025-09-06 15:38:07] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -86.746273-0.003090j
[2025-09-06 15:38:38] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -86.692110+0.001676j
[2025-09-06 15:39:09] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -86.684987+0.001950j
[2025-09-06 15:39:40] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -86.729149-0.005268j
[2025-09-06 15:40:10] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -86.794968+0.000130j
[2025-09-06 15:40:41] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -86.805941-0.002213j
[2025-09-06 15:41:12] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -86.737311+0.000576j
[2025-09-06 15:41:43] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -86.727873+0.000320j
[2025-09-06 15:42:14] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -86.769820-0.002138j
[2025-09-06 15:42:44] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -86.955534+0.001656j
[2025-09-06 15:43:15] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -86.919968+0.000906j
[2025-09-06 15:43:46] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -87.008000+0.001339j
[2025-09-06 15:44:17] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -86.958740-0.005740j
[2025-09-06 15:44:47] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -87.037619-0.000320j
[2025-09-06 15:45:10] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -86.957554-0.003050j
[2025-09-06 15:45:33] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -86.870485-0.000392j
[2025-09-06 15:46:04] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -87.080574-0.004478j
[2025-09-06 15:46:35] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -86.943626-0.005389j
[2025-09-06 15:47:06] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -86.834331-0.000523j
[2025-09-06 15:47:37] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -86.862527-0.006135j
[2025-09-06 15:48:07] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -86.848850+0.002534j
[2025-09-06 15:48:38] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -86.864452+0.000963j
[2025-09-06 15:49:09] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -87.026200+0.004947j
[2025-09-06 15:49:40] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -87.114895-0.002981j
[2025-09-06 15:50:06] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -86.889021+0.001536j
[2025-09-06 15:50:37] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -86.988458+0.000668j
[2025-09-06 15:51:08] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -86.979086-0.004323j
[2025-09-06 15:51:39] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -86.740110-0.000743j
[2025-09-06 15:52:10] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -86.728125+0.001002j
[2025-09-06 15:52:41] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -86.927781+0.001625j
[2025-09-06 15:53:12] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -86.865343-0.000293j
[2025-09-06 15:53:43] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -86.938765-0.003228j
[2025-09-06 15:54:14] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -86.960761+0.001087j
[2025-09-06 15:54:45] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -87.005838-0.003073j
[2025-09-06 15:55:16] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -87.027700+0.001091j
[2025-09-06 15:55:47] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -87.046353-0.001336j
[2025-09-06 15:55:47] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-06 15:56:18] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -87.139746+0.002350j
[2025-09-06 15:56:49] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -87.090815+0.004152j
[2025-09-06 15:57:20] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -87.063151+0.003369j
[2025-09-06 15:57:51] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -86.977891-0.000935j
[2025-09-06 15:58:22] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -86.982921+0.000576j
[2025-09-06 15:58:53] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -87.039704+0.002371j
[2025-09-06 15:59:23] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -86.922788+0.000145j
[2025-09-06 15:59:54] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -87.015767+0.007880j
[2025-09-06 16:00:25] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -86.942159-0.001641j
[2025-09-06 16:00:56] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -86.778902+0.003654j
[2025-09-06 16:01:27] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -86.871439-0.001510j
[2025-09-06 16:01:58] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -86.811717+0.001352j
[2025-09-06 16:02:29] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -87.072477-0.005081j
[2025-09-06 16:03:00] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -86.976656-0.001336j
[2025-09-06 16:03:30] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -86.916037-0.001699j
[2025-09-06 16:04:01] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -87.007496+0.003516j
[2025-09-06 16:04:32] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -86.955051+0.004033j
[2025-09-06 16:04:59] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -87.011209+0.001417j
[2025-09-06 16:05:20] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -86.855144-0.005524j
[2025-09-06 16:05:49] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -86.928594-0.001164j
[2025-09-06 16:06:20] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -86.762319-0.004172j
[2025-09-06 16:06:51] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -86.927607-0.002710j
[2025-09-06 16:07:22] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -86.725128-0.001679j
[2025-09-06 16:07:53] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -86.678789+0.001926j
[2025-09-06 16:08:24] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -86.718339+0.003321j
[2025-09-06 16:08:54] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -86.792288-0.002613j
[2025-09-06 16:09:25] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -86.837129+0.004471j
[2025-09-06 16:09:54] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -86.802458+0.004094j
[2025-09-06 16:10:22] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -86.898499+0.004607j
[2025-09-06 16:10:53] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -86.930331+0.002612j
[2025-09-06 16:11:24] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -86.824305-0.000149j
[2025-09-06 16:11:55] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -86.789630-0.005132j
[2025-09-06 16:12:26] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -86.850425-0.000870j
[2025-09-06 16:12:57] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -86.810665-0.004362j
[2025-09-06 16:13:27] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -86.827989+0.001537j
[2025-09-06 16:13:58] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -86.840009-0.002943j
[2025-09-06 16:14:29] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -86.907940+0.004531j
[2025-09-06 16:15:00] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -86.938760-0.001671j
[2025-09-06 16:15:31] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -86.963083+0.000332j
[2025-09-06 16:16:01] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -86.893408+0.002645j
[2025-09-06 16:16:32] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -87.032234+0.005624j
[2025-09-06 16:17:03] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -86.917067-0.002716j
[2025-09-06 16:17:34] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -86.932948+0.001591j
[2025-09-06 16:18:05] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -86.995911+0.002395j
[2025-09-06 16:18:36] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -86.917351+0.005000j
[2025-09-06 16:19:07] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -86.917191-0.000916j
[2025-09-06 16:19:38] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -86.751331-0.001223j
[2025-09-06 16:20:09] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -86.724854+0.001542j
[2025-09-06 16:20:40] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -86.855508-0.004665j
[2025-09-06 16:21:10] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -86.957805+0.000686j
[2025-09-06 16:21:41] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -86.959303-0.000963j
[2025-09-06 16:22:12] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -86.878590+0.006059j
[2025-09-06 16:22:43] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -86.891741+0.000648j
[2025-09-06 16:23:14] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -86.892039+0.002525j
[2025-09-06 16:23:45] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -86.895658-0.000047j
[2025-09-06 16:24:16] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -86.927582+0.001636j
[2025-09-06 16:24:47] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -86.887668+0.003906j
[2025-09-06 16:25:08] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -86.987829-0.003321j
[2025-09-06 16:25:33] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -86.825177+0.002253j
[2025-09-06 16:26:04] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -86.915211-0.001257j
[2025-09-06 16:26:35] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -86.999537-0.001699j
[2025-09-06 16:27:06] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -86.929493+0.001133j
[2025-09-06 16:27:37] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -86.929024+0.007484j
[2025-09-06 16:28:08] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -86.880724-0.002849j
[2025-09-06 16:28:39] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -86.866695-0.004366j
[2025-09-06 16:29:10] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -86.881053-0.000662j
[2025-09-06 16:29:41] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -86.945091-0.002518j
[2025-09-06 16:30:07] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -87.052250-0.001065j
[2025-09-06 16:30:38] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -87.001960+0.001627j
[2025-09-06 16:31:09] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -86.897135+0.002634j
[2025-09-06 16:31:40] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -86.931566+0.002643j
[2025-09-06 16:32:11] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -86.868637+0.000832j
[2025-09-06 16:32:41] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -86.684994-0.000174j
[2025-09-06 16:33:12] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -86.761102-0.003696j
[2025-09-06 16:33:43] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -86.811941-0.004362j
[2025-09-06 16:34:14] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -86.737484+0.001252j
[2025-09-06 16:34:45] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -86.835477-0.002725j
[2025-09-06 16:35:16] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -86.885124+0.003470j
[2025-09-06 16:35:47] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -86.732800+0.001084j
[2025-09-06 16:36:18] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -86.793840+0.002731j
[2025-09-06 16:36:49] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -86.940185+0.005050j
[2025-09-06 16:37:20] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -87.088614+0.000808j
[2025-09-06 16:37:51] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -86.997230-0.000175j
[2025-09-06 16:38:22] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -86.835099+0.000400j
[2025-09-06 16:38:52] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -86.934391-0.007716j
[2025-09-06 16:39:23] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -86.803155-0.002038j
[2025-09-06 16:39:54] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -86.814792+0.000350j
[2025-09-06 16:40:25] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -86.955649+0.000251j
[2025-09-06 16:40:56] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -86.870417-0.003619j
[2025-09-06 16:41:27] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -86.948366+0.000436j
[2025-09-06 16:41:58] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -86.992575+0.002825j
[2025-09-06 16:42:28] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -86.927079-0.002513j
[2025-09-06 16:42:59] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -86.889148+0.001945j
[2025-09-06 16:43:30] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -86.825494-0.000071j
[2025-09-06 16:44:01] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -86.927124-0.002630j
[2025-09-06 16:44:32] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -86.776233+0.000458j
[2025-09-06 16:44:50] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -86.729623-0.004335j
[2025-09-06 16:44:59] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -86.714111-0.001245j
[2025-09-06 16:45:09] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -86.788382+0.004175j
[2025-09-06 16:45:27] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -86.769956-0.000959j
[2025-09-06 16:45:47] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -86.752547+0.002918j
[2025-09-06 16:46:08] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -87.022015-0.002723j
[2025-09-06 16:46:28] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -86.940771+0.000114j
[2025-09-06 16:46:49] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -87.195456+0.002042j
[2025-09-06 16:47:10] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -87.112300-0.003308j
[2025-09-06 16:47:10] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-06 16:47:10] ✅ Training completed | Restarts: 2
[2025-09-06 16:47:10] ============================================================
[2025-09-06 16:47:10] Training completed | Runtime: 31781.2s
[2025-09-06 16:47:18] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-06 16:47:18] ============================================================
