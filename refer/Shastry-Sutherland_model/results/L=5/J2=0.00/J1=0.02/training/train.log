[2025-09-05 23:08:16] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.03/training/checkpoints/final_GCNN.pkl
[2025-09-05 23:08:16]   - 迭代次数: final
[2025-09-05 23:08:16]   - 能量: -84.406308-0.004289j ± 0.112816
[2025-09-05 23:08:16]   - 时间戳: 2025-09-05T23:08:04.665971+08:00
[2025-09-05 23:08:30] ✓ 变分状态参数已从checkpoint恢复
[2025-09-05 23:08:30] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-05 23:08:30] ==================================================
[2025-09-05 23:08:30] GCNN for Shastry-Sutherland Model
[2025-09-05 23:08:30] ==================================================
[2025-09-05 23:08:30] System parameters:
[2025-09-05 23:08:30]   - System size: L=5, N=100
[2025-09-05 23:08:30]   - System parameters: J1=0.02, J2=0.0, Q=1.0
[2025-09-05 23:08:30] --------------------------------------------------
[2025-09-05 23:08:30] Model parameters:
[2025-09-05 23:08:30]   - Number of layers = 4
[2025-09-05 23:08:30]   - Number of features = 4
[2025-09-05 23:08:30]   - Total parameters = 19628
[2025-09-05 23:08:30] --------------------------------------------------
[2025-09-05 23:08:30] Training parameters:
[2025-09-05 23:08:30]   - Learning rate: 0.015
[2025-09-05 23:08:30]   - Total iterations: 1050
[2025-09-05 23:08:30]   - Annealing cycles: 3
[2025-09-05 23:08:30]   - Initial period: 150
[2025-09-05 23:08:30]   - Period multiplier: 2.0
[2025-09-05 23:08:30]   - Temperature range: 0.0-1.0
[2025-09-05 23:08:30]   - Samples: 4096
[2025-09-05 23:08:30]   - Discarded samples: 0
[2025-09-05 23:08:30]   - Chunk size: 2048
[2025-09-05 23:08:30]   - Diagonal shift: 0.2
[2025-09-05 23:08:30]   - Gradient clipping: 1.0
[2025-09-05 23:08:30]   - Checkpoint enabled: interval=105
[2025-09-05 23:08:30]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.02/training/checkpoints
[2025-09-05 23:08:30] --------------------------------------------------
[2025-09-05 23:08:30] Device status:
[2025-09-05 23:08:30]   - Devices model: NVIDIA H200 NVL
[2025-09-05 23:08:30]   - Number of devices: 1
[2025-09-05 23:08:30]   - Sharding: True
[2025-09-05 23:08:30] ============================================================
[2025-09-05 23:09:30] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -83.621725+0.049750j
[2025-09-05 23:10:12] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -83.655858+0.019440j
[2025-09-05 23:10:36] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -83.629534+0.016893j
[2025-09-05 23:11:07] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -83.777266+0.002455j
[2025-09-05 23:11:38] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -83.786433-0.003403j
[2025-09-05 23:12:09] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -83.900660-0.003968j
[2025-09-05 23:12:39] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -83.885339+0.001803j
[2025-09-05 23:13:10] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -83.779794+0.000184j
[2025-09-05 23:13:41] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -83.854956+0.004019j
[2025-09-05 23:14:11] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -83.676442-0.002644j
[2025-09-05 23:14:42] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -83.600771+0.000181j
[2025-09-05 23:15:08] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -83.573782-0.000036j
[2025-09-05 23:15:39] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -83.477452-0.004284j
[2025-09-05 23:16:10] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -83.608793-0.000409j
[2025-09-05 23:16:40] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -83.663459+0.004665j
[2025-09-05 23:17:11] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -83.653117-0.001571j
[2025-09-05 23:17:42] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -83.698072+0.004489j
[2025-09-05 23:18:13] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -83.702537-0.002405j
[2025-09-05 23:18:43] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -83.738448-0.001382j
[2025-09-05 23:19:14] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -83.959434-0.002028j
[2025-09-05 23:19:45] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -83.823118+0.000909j
[2025-09-05 23:20:16] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -83.906390+0.003913j
[2025-09-05 23:20:46] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -83.886798+0.001260j
[2025-09-05 23:21:17] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -83.965034+0.002802j
[2025-09-05 23:21:48] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -83.741180+0.004250j
[2025-09-05 23:22:19] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -83.780207+0.005883j
[2025-09-05 23:22:49] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -83.892761-0.003543j
[2025-09-05 23:23:20] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -83.756247-0.001837j
[2025-09-05 23:23:51] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -83.771623-0.000193j
[2025-09-05 23:24:21] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -83.842354+0.002826j
[2025-09-05 23:24:52] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -83.831803-0.001243j
[2025-09-05 23:25:23] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -83.966888+0.003967j
[2025-09-05 23:25:54] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -83.814081+0.001345j
[2025-09-05 23:26:24] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -83.882221-0.003107j
[2025-09-05 23:26:55] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -83.687720-0.003905j
[2025-09-05 23:27:26] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -83.713446+0.003009j
[2025-09-05 23:27:57] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -83.778774-0.003228j
[2025-09-05 23:28:28] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -83.697877+0.000127j
[2025-09-05 23:28:58] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -83.700044+0.003209j
[2025-09-05 23:29:29] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -83.824224-0.002597j
[2025-09-05 23:29:57] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -83.675159-0.005731j
[2025-09-05 23:30:17] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -83.724878-0.000880j
[2025-09-05 23:30:45] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -83.705970+0.000805j
[2025-09-05 23:31:16] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -83.625702+0.007960j
[2025-09-05 23:31:47] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -83.805307+0.001234j
[2025-09-05 23:32:17] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -83.729419+0.004639j
[2025-09-05 23:32:48] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -83.805676+0.001563j
[2025-09-05 23:33:19] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -83.814382-0.000660j
[2025-09-05 23:33:49] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -83.860243-0.003672j
[2025-09-05 23:34:20] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -83.780636-0.000113j
[2025-09-05 23:34:50] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -83.894319+0.002812j
[2025-09-05 23:35:17] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -83.814384-0.001020j
[2025-09-05 23:35:48] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -83.870219-0.004100j
[2025-09-05 23:36:18] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -83.777003+0.004250j
[2025-09-05 23:36:49] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -83.866834-0.003718j
[2025-09-05 23:37:19] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -83.929486+0.002615j
[2025-09-05 23:37:50] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -83.808876-0.000575j
[2025-09-05 23:38:21] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -83.752508-0.001677j
[2025-09-05 23:38:51] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -83.648781+0.001956j
[2025-09-05 23:39:22] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -83.541012+0.000158j
[2025-09-05 23:39:53] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -83.639669+0.006338j
[2025-09-05 23:40:23] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -83.789911+0.005404j
[2025-09-05 23:40:54] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -83.792795-0.006309j
[2025-09-05 23:41:25] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -83.591815+0.004222j
[2025-09-05 23:41:55] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -83.767448-0.002375j
[2025-09-05 23:42:26] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -83.659815+0.002134j
[2025-09-05 23:42:56] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -83.731113+0.001541j
[2025-09-05 23:43:27] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -83.643243+0.004229j
[2025-09-05 23:43:58] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -83.641575-0.000482j
[2025-09-05 23:44:28] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -83.778394-0.001857j
[2025-09-05 23:44:59] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -83.683561-0.003427j
[2025-09-05 23:45:30] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -83.630384-0.005174j
[2025-09-05 23:46:00] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -83.631366+0.004966j
[2025-09-05 23:46:31] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -83.645318+0.005212j
[2025-09-05 23:47:02] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -83.787292+0.000983j
[2025-09-05 23:47:32] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -83.657712-0.005117j
[2025-09-05 23:48:03] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -83.584002-0.006893j
[2025-09-05 23:48:34] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -83.815686-0.009040j
[2025-09-05 23:49:04] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -83.809726-0.003421j
[2025-09-05 23:49:35] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -83.780414-0.007856j
[2025-09-05 23:50:00] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -83.887971+0.001388j
[2025-09-05 23:50:21] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -83.751922+0.002954j
[2025-09-05 23:50:51] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -83.752798-0.002139j
[2025-09-05 23:51:22] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -83.928495-0.001276j
[2025-09-05 23:51:52] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -83.866113-0.000123j
[2025-09-05 23:52:23] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -83.858978+0.000344j
[2025-09-05 23:52:54] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -83.801490-0.000143j
[2025-09-05 23:53:25] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -83.695231+0.002261j
[2025-09-05 23:53:55] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -83.621302-0.000469j
[2025-09-05 23:54:26] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -83.655017-0.003619j
[2025-09-05 23:54:54] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -83.714968+0.002421j
[2025-09-05 23:55:23] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -83.742172+0.004624j
[2025-09-05 23:55:53] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -83.742399+0.008001j
[2025-09-05 23:56:24] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -83.779314-0.001721j
[2025-09-05 23:56:55] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -83.854952-0.008982j
[2025-09-05 23:57:25] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -83.839321+0.002713j
[2025-09-05 23:57:56] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -83.743749-0.002919j
[2025-09-05 23:58:27] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -83.761640-0.006129j
[2025-09-05 23:58:58] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -83.861816-0.001686j
[2025-09-05 23:59:28] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -83.750936-0.002625j
[2025-09-05 23:59:59] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -83.836892+0.001054j
[2025-09-06 00:00:30] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -83.982583-0.001623j
[2025-09-06 00:01:00] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -83.810507-0.001698j
[2025-09-06 00:01:31] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -83.767006+0.004446j
[2025-09-06 00:02:02] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -83.862800-0.003554j
[2025-09-06 00:02:02] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-06 00:02:33] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -83.849561+0.007685j
[2025-09-06 00:03:03] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -83.762850+0.002843j
[2025-09-06 00:03:34] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -83.913507+0.004564j
[2025-09-06 00:04:05] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -83.768377+0.000053j
[2025-09-06 00:04:36] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -83.809115-0.000426j
[2025-09-06 00:05:06] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -83.736069+0.001194j
[2025-09-06 00:05:37] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -83.752820-0.002120j
[2025-09-06 00:06:08] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -83.833812+0.000436j
[2025-09-06 00:06:38] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -83.728854-0.002578j
[2025-09-06 00:07:09] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -83.631459+0.000573j
[2025-09-06 00:07:40] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -83.633997-0.000015j
[2025-09-06 00:08:10] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -83.703288+0.004563j
[2025-09-06 00:08:41] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -83.696748+0.005501j
[2025-09-06 00:09:12] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -83.607298-0.001092j
[2025-09-06 00:09:43] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -83.652032+0.000243j
[2025-09-06 00:10:04] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -83.613116+0.000035j
[2025-09-06 00:10:28] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -83.768051-0.006480j
[2025-09-06 00:10:58] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -83.704958+0.006872j
[2025-09-06 00:11:29] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -83.777430+0.000118j
[2025-09-06 00:12:00] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -83.758869-0.004457j
[2025-09-06 00:12:31] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -83.549647+0.003872j
[2025-09-06 00:13:01] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -83.696998-0.002216j
[2025-09-06 00:13:32] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -83.661177-0.002217j
[2025-09-06 00:14:03] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -83.802716-0.002091j
[2025-09-06 00:14:34] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -83.747503-0.003663j
[2025-09-06 00:15:00] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -83.844657-0.000937j
[2025-09-06 00:15:30] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -83.965348+0.003302j
[2025-09-06 00:16:01] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -83.938363-0.002600j
[2025-09-06 00:16:32] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -83.838882-0.002413j
[2025-09-06 00:17:02] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -83.831948+0.000059j
[2025-09-06 00:17:33] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -83.802832-0.002126j
[2025-09-06 00:18:04] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -83.867296-0.001712j
[2025-09-06 00:18:34] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -83.850271+0.000941j
[2025-09-06 00:19:05] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -83.740808+0.006143j
[2025-09-06 00:19:36] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -83.657768+0.000987j
[2025-09-06 00:20:06] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -83.615541+0.009628j
[2025-09-06 00:20:37] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -83.643533-0.001645j
[2025-09-06 00:21:07] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -83.617142+0.000554j
[2025-09-06 00:21:38] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -83.581008+0.005692j
[2025-09-06 00:22:09] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -83.660665+0.006203j
[2025-09-06 00:22:39] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -83.594128+0.001108j
[2025-09-06 00:23:10] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -83.680680-0.004390j
[2025-09-06 00:23:41] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -83.511081-0.003612j
[2025-09-06 00:24:11] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -83.607967-0.001456j
[2025-09-06 00:24:42] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -83.536729-0.006134j
[2025-09-06 00:24:42] RESTART #1 | Period: 300
[2025-09-06 00:25:13] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -83.592473+0.004403j
[2025-09-06 00:25:43] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -83.727855+0.001496j
[2025-09-06 00:26:14] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -83.707988-0.000937j
[2025-09-06 00:26:44] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -83.701857+0.003128j
[2025-09-06 00:27:15] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -83.916516+0.001873j
[2025-09-06 00:27:46] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -83.892467-0.000018j
[2025-09-06 00:28:16] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -83.798243+0.002736j
[2025-09-06 00:28:47] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -83.717814+0.001963j
[2025-09-06 00:29:18] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -83.724924-0.004055j
[2025-09-06 00:29:46] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -83.776142+0.003753j
[2025-09-06 00:30:07] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -83.609034+0.001531j
[2025-09-06 00:30:35] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -83.642009-0.004410j
[2025-09-06 00:31:05] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -83.582337-0.003426j
[2025-09-06 00:31:36] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -83.687784+0.004185j
[2025-09-06 00:32:07] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -83.703940+0.002958j
[2025-09-06 00:32:38] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -83.744531-0.000425j
[2025-09-06 00:33:08] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -83.801999+0.007268j
[2025-09-06 00:33:39] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -83.909271-0.000386j
[2025-09-06 00:34:10] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -83.955423-0.001255j
[2025-09-06 00:34:39] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -83.906981+0.005694j
[2025-09-06 00:35:07] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -83.799041-0.002016j
[2025-09-06 00:35:37] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -83.627685-0.001089j
[2025-09-06 00:36:08] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -83.801497+0.001063j
[2025-09-06 00:36:38] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -83.814371-0.000453j
[2025-09-06 00:37:09] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -83.870528+0.004970j
[2025-09-06 00:37:40] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -83.788522-0.001909j
[2025-09-06 00:38:10] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -83.861609-0.001941j
[2025-09-06 00:38:41] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -83.871861-0.001668j
[2025-09-06 00:39:12] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -83.800260+0.001977j
[2025-09-06 00:39:42] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -83.862498-0.005422j
[2025-09-06 00:40:13] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -83.681744+0.010688j
[2025-09-06 00:40:44] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -83.751036-0.003151j
[2025-09-06 00:41:14] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -83.873191+0.001598j
[2025-09-06 00:41:45] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -83.897297-0.004011j
[2025-09-06 00:42:16] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -83.917307+0.006390j
[2025-09-06 00:42:46] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -83.875959+0.003288j
[2025-09-06 00:43:17] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -83.815924+0.006275j
[2025-09-06 00:43:48] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -83.843760-0.005743j
[2025-09-06 00:44:18] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -83.903729-0.002323j
[2025-09-06 00:44:49] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -84.038411+0.003650j
[2025-09-06 00:45:20] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -83.897403-0.007007j
[2025-09-06 00:45:50] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -83.794230-0.001117j
[2025-09-06 00:46:21] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -83.786425+0.003051j
[2025-09-06 00:46:51] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -83.713783+0.002165j
[2025-09-06 00:47:22] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -83.642553+0.005049j
[2025-09-06 00:47:53] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -83.660423+0.001199j
[2025-09-06 00:48:23] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -83.696150-0.003610j
[2025-09-06 00:48:54] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -83.658777+0.000952j
[2025-09-06 00:49:25] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -83.674932-0.002902j
[2025-09-06 00:49:50] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -83.869920-0.000218j
[2025-09-06 00:50:10] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -83.887002+0.007873j
[2025-09-06 00:50:40] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -83.805847+0.002493j
[2025-09-06 00:51:10] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -83.978789-0.001358j
[2025-09-06 00:51:41] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -83.959155-0.000257j
[2025-09-06 00:52:12] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -83.824657-0.004237j
[2025-09-06 00:52:43] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -83.851481-0.001553j
[2025-09-06 00:53:13] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -83.873213+0.003523j
[2025-09-06 00:53:44] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -83.798091-0.005066j
[2025-09-06 00:54:15] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -83.879871+0.000945j
[2025-09-06 00:54:43] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -83.828276+0.000331j
[2025-09-06 00:54:43] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-06 00:55:12] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -83.856044+0.000487j
[2025-09-06 00:55:42] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -83.903761-0.002101j
[2025-09-06 00:56:13] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -83.782316-0.002903j
[2025-09-06 00:56:44] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -83.811018-0.001739j
[2025-09-06 00:57:15] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -83.675196+0.001863j
[2025-09-06 00:57:45] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -83.658028-0.002369j
[2025-09-06 00:58:16] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -83.521478-0.001536j
[2025-09-06 00:58:47] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -83.641252+0.003285j
[2025-09-06 00:59:17] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -83.615282-0.001611j
[2025-09-06 00:59:48] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -83.597886-0.002189j
[2025-09-06 01:00:19] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -83.598559-0.001598j
[2025-09-06 01:00:49] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -83.743962-0.000970j
[2025-09-06 01:01:20] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -83.801799-0.000787j
[2025-09-06 01:01:51] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -83.656909+0.002876j
[2025-09-06 01:02:22] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -83.593048-0.005098j
[2025-09-06 01:02:52] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -83.789954-0.000467j
[2025-09-06 01:03:23] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -83.726926+0.000579j
[2025-09-06 01:03:54] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -83.597489+0.005046j
[2025-09-06 01:04:24] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -83.597089-0.004071j
[2025-09-06 01:04:55] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -83.616519-0.004306j
[2025-09-06 01:05:26] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -83.578754+0.002190j
[2025-09-06 01:05:57] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -83.613110-0.000530j
[2025-09-06 01:06:27] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -83.614063-0.003810j
[2025-09-06 01:06:58] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -83.697989+0.002404j
[2025-09-06 01:07:29] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -83.640679-0.001563j
[2025-09-06 01:07:59] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -83.616875+0.000263j
[2025-09-06 01:08:30] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -83.560232-0.004044j
[2025-09-06 01:09:01] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -83.647192+0.001855j
[2025-09-06 01:09:32] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -83.823116+0.004550j
[2025-09-06 01:09:55] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -83.742275-0.007866j
[2025-09-06 01:10:15] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -83.721741+0.006515j
[2025-09-06 01:10:45] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -83.904207-0.002363j
[2025-09-06 01:11:16] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -83.753118+0.003686j
[2025-09-06 01:11:47] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -83.886842+0.000278j
[2025-09-06 01:12:17] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -83.772508+0.000321j
[2025-09-06 01:12:48] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -83.821919-0.000927j
[2025-09-06 01:13:19] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -84.078101+0.002374j
[2025-09-06 01:13:49] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -84.058047+0.003271j
[2025-09-06 01:14:20] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -83.903517-0.000639j
[2025-09-06 01:14:49] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -83.871655+0.002263j
[2025-09-06 01:15:17] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -83.890127+0.000436j
[2025-09-06 01:15:48] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -83.735806+0.000275j
[2025-09-06 01:16:18] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -83.826428+0.005013j
[2025-09-06 01:16:49] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -83.796751-0.000005j
[2025-09-06 01:17:20] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -83.766348+0.003456j
[2025-09-06 01:17:50] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -83.836292+0.001675j
[2025-09-06 01:18:21] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -83.902346-0.003215j
[2025-09-06 01:18:51] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -83.873493-0.000812j
[2025-09-06 01:19:22] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -83.692997+0.004112j
[2025-09-06 01:19:53] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -83.797391+0.001615j
[2025-09-06 01:20:23] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -83.672435-0.001712j
[2025-09-06 01:20:54] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -83.731299+0.003814j
[2025-09-06 01:21:25] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -83.730897-0.000894j
[2025-09-06 01:21:55] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -83.723058+0.002694j
[2025-09-06 01:22:26] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -83.684708-0.003743j
[2025-09-06 01:22:57] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -83.764451-0.000649j
[2025-09-06 01:23:27] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -83.704020-0.004625j
[2025-09-06 01:23:58] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -83.664112-0.000282j
[2025-09-06 01:24:28] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -83.654599-0.001594j
[2025-09-06 01:24:59] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -83.706470+0.000419j
[2025-09-06 01:25:30] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -83.774987-0.002936j
[2025-09-06 01:26:00] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -83.702641+0.001922j
[2025-09-06 01:26:31] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -83.726115-0.000374j
[2025-09-06 01:27:02] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -83.719556+0.004529j
[2025-09-06 01:27:32] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -83.746122+0.004315j
[2025-09-06 01:28:03] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -83.749032-0.003024j
[2025-09-06 01:28:34] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -83.681318+0.003550j
[2025-09-06 01:29:05] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -83.696697-0.000581j
[2025-09-06 01:29:35] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -83.618201-0.006125j
[2025-09-06 01:29:58] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -83.786434+0.000555j
[2025-09-06 01:30:20] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -83.756286+0.005145j
[2025-09-06 01:30:51] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -83.648142+0.001778j
[2025-09-06 01:31:22] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -83.659809-0.000091j
[2025-09-06 01:31:52] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -83.571859-0.001207j
[2025-09-06 01:32:23] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -83.572460-0.002173j
[2025-09-06 01:32:54] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -83.596216-0.003167j
[2025-09-06 01:33:24] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -83.800920-0.002581j
[2025-09-06 01:33:55] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -83.788117-0.003382j
[2025-09-06 01:34:26] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -83.741709+0.001493j
[2025-09-06 01:34:54] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -83.793227-0.002995j
[2025-09-06 01:35:23] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -83.710009+0.002235j
[2025-09-06 01:35:53] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -83.721886-0.000562j
[2025-09-06 01:36:24] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -83.567466+0.002391j
[2025-09-06 01:36:54] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -83.775068-0.003945j
[2025-09-06 01:37:25] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -83.873608-0.001445j
[2025-09-06 01:37:56] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -83.727871-0.005692j
[2025-09-06 01:38:26] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -83.799967+0.002069j
[2025-09-06 01:38:57] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -83.640069+0.000693j
[2025-09-06 01:39:27] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -83.769725-0.003731j
[2025-09-06 01:39:58] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -83.648701-0.003295j
[2025-09-06 01:40:28] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -83.639806-0.002821j
[2025-09-06 01:40:59] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -83.782611-0.006036j
[2025-09-06 01:41:30] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -83.710672-0.008436j
[2025-09-06 01:42:00] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -83.718629-0.000687j
[2025-09-06 01:42:31] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -83.561516+0.002709j
[2025-09-06 01:43:01] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -83.714307+0.006334j
[2025-09-06 01:43:32] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -83.689272+0.001289j
[2025-09-06 01:44:03] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -83.644782-0.008900j
[2025-09-06 01:44:33] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -83.658599+0.002545j
[2025-09-06 01:45:04] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -83.710631-0.004165j
[2025-09-06 01:45:34] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -83.859982-0.003517j
[2025-09-06 01:46:05] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -83.790618-0.001193j
[2025-09-06 01:46:35] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -83.618373+0.002473j
[2025-09-06 01:47:06] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -83.823719+0.005256j
[2025-09-06 01:47:37] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -83.747320-0.002729j
[2025-09-06 01:47:37] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-06 01:48:07] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -83.647191-0.003571j
[2025-09-06 01:48:38] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -83.837172+0.001058j
[2025-09-06 01:49:08] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -83.701765+0.005767j
[2025-09-06 01:49:39] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -83.688154+0.002147j
[2025-09-06 01:50:00] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -83.732632+0.007213j
[2025-09-06 01:50:24] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -83.922357+0.000760j
[2025-09-06 01:50:55] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -83.902242-0.005916j
[2025-09-06 01:51:26] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -83.840375-0.000717j
[2025-09-06 01:51:56] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -83.840780+0.002649j
[2025-09-06 01:52:27] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -83.732779-0.003333j
[2025-09-06 01:52:58] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -83.974933+0.002508j
[2025-09-06 01:53:28] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -83.774857-0.004783j
[2025-09-06 01:53:59] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -83.681606+0.002362j
[2025-09-06 01:54:30] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -83.726021+0.000169j
[2025-09-06 01:54:56] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -83.779066+0.001056j
[2025-09-06 01:55:27] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -83.863022-0.001173j
[2025-09-06 01:55:57] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -83.786545+0.000513j
[2025-09-06 01:56:28] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -83.850266+0.000307j
[2025-09-06 01:56:59] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -84.024922-0.000181j
[2025-09-06 01:57:30] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -83.918158-0.000525j
[2025-09-06 01:58:00] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -83.919705-0.000188j
[2025-09-06 01:58:31] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -83.755457-0.009368j
[2025-09-06 01:59:02] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -83.905520-0.000500j
[2025-09-06 01:59:32] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -83.915109-0.001757j
[2025-09-06 02:00:03] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -83.817826+0.001828j
[2025-09-06 02:00:34] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -83.874815+0.000944j
[2025-09-06 02:01:05] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -83.889667+0.003889j
[2025-09-06 02:01:35] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -83.738641-0.001653j
[2025-09-06 02:02:06] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -83.774756+0.008725j
[2025-09-06 02:02:37] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -83.781425-0.002717j
[2025-09-06 02:03:07] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -83.895005+0.000118j
[2025-09-06 02:03:38] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -83.914267+0.005486j
[2025-09-06 02:04:09] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -83.818549+0.002299j
[2025-09-06 02:04:40] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -83.834851-0.002227j
[2025-09-06 02:05:10] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -83.802534-0.006605j
[2025-09-06 02:05:41] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -84.075224+0.004996j
[2025-09-06 02:06:12] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -84.099133-0.002122j
[2025-09-06 02:06:43] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -83.946277-0.000303j
[2025-09-06 02:07:13] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -83.962225+0.001914j
[2025-09-06 02:07:44] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -83.765617-0.007124j
[2025-09-06 02:08:15] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -83.917509+0.004221j
[2025-09-06 02:08:45] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -83.814463+0.001812j
[2025-09-06 02:09:16] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -83.810751+0.000373j
[2025-09-06 02:09:44] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -83.824416-0.008136j
[2025-09-06 02:10:04] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -83.910685+0.000765j
[2025-09-06 02:10:33] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -83.889438+0.000741j
[2025-09-06 02:11:03] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -83.727665-0.000704j
[2025-09-06 02:11:34] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -83.735412-0.000079j
[2025-09-06 02:12:05] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -83.640295-0.002165j
[2025-09-06 02:12:36] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -83.618973-0.002130j
[2025-09-06 02:13:06] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -83.641796-0.001775j
[2025-09-06 02:13:37] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -83.678669+0.007490j
[2025-09-06 02:14:08] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -83.552605-0.003513j
[2025-09-06 02:14:36] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -83.583440+0.002494j
[2025-09-06 02:15:05] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -83.561362+0.005901j
[2025-09-06 02:15:36] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -83.639016-0.002918j
[2025-09-06 02:16:06] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -83.512328+0.003926j
[2025-09-06 02:16:37] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -83.592949+0.000251j
[2025-09-06 02:17:08] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -83.581404-0.001558j
[2025-09-06 02:17:39] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -83.540991+0.001511j
[2025-09-06 02:18:09] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -83.682153-0.000646j
[2025-09-06 02:18:40] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -83.658172+0.004448j
[2025-09-06 02:19:11] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -83.556061+0.000232j
[2025-09-06 02:19:42] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -83.575757-0.007504j
[2025-09-06 02:20:12] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -83.639567+0.006391j
[2025-09-06 02:20:43] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -83.779280+0.002126j
[2025-09-06 02:21:14] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -83.909068-0.000792j
[2025-09-06 02:21:45] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -83.868711-0.003229j
[2025-09-06 02:22:15] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -83.672058-0.002997j
[2025-09-06 02:22:46] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -83.625789-0.002973j
[2025-09-06 02:23:17] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -83.580156+0.009903j
[2025-09-06 02:23:48] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -83.628252+0.004029j
[2025-09-06 02:24:18] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -83.646484+0.003265j
[2025-09-06 02:24:49] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -83.676009+0.007473j
[2025-09-06 02:25:20] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -83.573584+0.000995j
[2025-09-06 02:25:51] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -83.859100+0.003127j
[2025-09-06 02:26:21] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -83.842405+0.000833j
[2025-09-06 02:26:52] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -83.753000-0.002546j
[2025-09-06 02:27:23] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -83.813207-0.003224j
[2025-09-06 02:27:54] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -83.964851-0.000175j
[2025-09-06 02:28:24] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -83.918593+0.008322j
[2025-09-06 02:28:55] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -83.930547+0.001807j
[2025-09-06 02:29:26] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -83.826164-0.003612j
[2025-09-06 02:29:50] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -83.535484+0.003620j
[2025-09-06 02:30:11] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -83.671278+0.001834j
[2025-09-06 02:30:41] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -83.679286+0.004017j
[2025-09-06 02:31:11] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -83.828551-0.000927j
[2025-09-06 02:31:42] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -83.868693+0.009274j
[2025-09-06 02:32:13] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -83.887817+0.000252j
[2025-09-06 02:32:44] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -83.787057-0.004963j
[2025-09-06 02:33:14] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -83.740727+0.002760j
[2025-09-06 02:33:45] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -83.745857+0.004227j
[2025-09-06 02:34:16] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -83.920565+0.000289j
[2025-09-06 02:34:44] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -83.892529-0.002528j
[2025-09-06 02:35:13] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -83.898215+0.003307j
[2025-09-06 02:35:43] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -83.847584-0.003077j
[2025-09-06 02:36:14] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -83.832596-0.000434j
[2025-09-06 02:36:45] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -83.736304-0.002559j
[2025-09-06 02:37:15] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -83.872161+0.001396j
[2025-09-06 02:37:46] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -83.807899+0.002294j
[2025-09-06 02:38:17] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -83.831744+0.002079j
[2025-09-06 02:38:47] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -83.827170-0.000115j
[2025-09-06 02:39:18] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -83.749687+0.003854j
[2025-09-06 02:39:49] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -83.852650-0.002550j
[2025-09-06 02:40:19] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -83.854063-0.003079j
[2025-09-06 02:40:19] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-06 02:40:50] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -83.840219-0.002185j
[2025-09-06 02:41:20] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -83.811378+0.002359j
[2025-09-06 02:41:51] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -83.765593-0.000980j
[2025-09-06 02:42:22] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -83.842135-0.000539j
[2025-09-06 02:42:52] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -83.816710+0.000947j
[2025-09-06 02:43:23] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -83.875222+0.004446j
[2025-09-06 02:43:54] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -84.019840+0.002559j
[2025-09-06 02:44:24] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -83.855719-0.007957j
[2025-09-06 02:44:55] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -83.956845-0.003038j
[2025-09-06 02:45:26] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -83.741175+0.000166j
[2025-09-06 02:45:56] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -83.681844-0.002710j
[2025-09-06 02:46:27] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -83.674972+0.001215j
[2025-09-06 02:46:58] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -83.798192-0.001293j
[2025-09-06 02:47:28] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -83.882155+0.003301j
[2025-09-06 02:47:59] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -83.762243-0.003349j
[2025-09-06 02:48:29] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -83.813292+0.005823j
[2025-09-06 02:49:00] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -83.755233-0.001442j
[2025-09-06 02:49:31] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -83.821503-0.002509j
[2025-09-06 02:49:53] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -83.935503+0.001283j
[2025-09-06 02:50:16] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -83.834420+0.003079j
[2025-09-06 02:50:47] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -83.887984+0.002691j
[2025-09-06 02:51:17] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -83.876430+0.001190j
[2025-09-06 02:51:48] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -83.810691-0.000518j
[2025-09-06 02:52:19] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -83.827878+0.008194j
[2025-09-06 02:52:50] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -83.841781-0.000281j
[2025-09-06 02:53:20] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -83.784212-0.004066j
[2025-09-06 02:53:51] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -83.754281-0.002320j
[2025-09-06 02:54:22] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -83.748422+0.001855j
[2025-09-06 02:54:49] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -83.853208-0.000439j
[2025-09-06 02:55:18] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -83.681220-0.001359j
[2025-09-06 02:55:18] RESTART #2 | Period: 600
[2025-09-06 02:55:49] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -83.840331+0.003811j
[2025-09-06 02:56:20] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -83.835744-0.002069j
[2025-09-06 02:56:50] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -83.975590-0.004738j
[2025-09-06 02:57:21] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -83.959062-0.006119j
[2025-09-06 02:57:52] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -83.844825+0.002560j
[2025-09-06 02:58:22] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -83.720980+0.001090j
[2025-09-06 02:58:53] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -83.819476-0.004460j
[2025-09-06 02:59:24] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -83.752888-0.003011j
[2025-09-06 02:59:54] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -83.797108-0.001407j
[2025-09-06 03:00:25] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -83.739316+0.004663j
[2025-09-06 03:00:56] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -83.724685+0.001847j
[2025-09-06 03:01:26] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -83.867830-0.003549j
[2025-09-06 03:01:57] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -83.984518+0.000165j
[2025-09-06 03:02:28] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -84.022259+0.003087j
[2025-09-06 03:02:58] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -83.950665+0.003072j
[2025-09-06 03:03:29] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -83.897111-0.002661j
[2025-09-06 03:04:00] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -83.827378+0.001448j
[2025-09-06 03:04:30] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -83.751614-0.001898j
[2025-09-06 03:05:01] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -83.777023-0.001678j
[2025-09-06 03:05:32] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -83.859054+0.003314j
[2025-09-06 03:06:02] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -83.846154-0.005849j
[2025-09-06 03:06:33] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -83.860339+0.000936j
[2025-09-06 03:07:03] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -83.891399+0.001693j
[2025-09-06 03:07:34] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -83.835191-0.000326j
[2025-09-06 03:08:05] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -83.791818+0.002500j
[2025-09-06 03:08:35] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -83.915632-0.005604j
[2025-09-06 03:09:06] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -83.877721+0.001934j
[2025-09-06 03:09:36] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -83.775254+0.000740j
[2025-09-06 03:09:57] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -83.828003+0.004759j
[2025-09-06 03:10:23] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -83.681308-0.001531j
[2025-09-06 03:10:54] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -83.639663-0.006179j
[2025-09-06 03:11:25] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -83.819185-0.005817j
[2025-09-06 03:11:55] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -83.897156-0.000369j
[2025-09-06 03:12:26] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -83.765210+0.003129j
[2025-09-06 03:12:57] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -83.769742+0.000289j
[2025-09-06 03:13:28] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -83.697161-0.000277j
[2025-09-06 03:13:58] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -83.747605-0.004062j
[2025-09-06 03:14:29] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -83.487061-0.001157j
[2025-09-06 03:14:55] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -83.687910-0.000451j
[2025-09-06 03:15:26] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -83.778723-0.001436j
[2025-09-06 03:15:57] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -83.640106-0.002912j
[2025-09-06 03:16:27] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -83.673560-0.008400j
[2025-09-06 03:16:58] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -83.627045+0.003004j
[2025-09-06 03:17:29] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -83.678075+0.000478j
[2025-09-06 03:18:00] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -83.704616-0.003764j
[2025-09-06 03:18:30] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -83.495025-0.002113j
[2025-09-06 03:19:01] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -83.722145-0.000546j
[2025-09-06 03:19:32] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -83.751408+0.001532j
[2025-09-06 03:20:02] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -83.694033+0.004626j
[2025-09-06 03:20:33] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -83.745713+0.000775j
[2025-09-06 03:21:04] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -83.585899-0.003354j
[2025-09-06 03:21:35] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -83.735206-0.001802j
[2025-09-06 03:22:05] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -83.820261+0.002399j
[2025-09-06 03:22:36] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -83.832483-0.000133j
[2025-09-06 03:23:07] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -83.771010-0.007447j
[2025-09-06 03:23:38] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -83.744541-0.000017j
[2025-09-06 03:24:08] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -83.753157+0.000061j
[2025-09-06 03:24:39] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -83.870620+0.006351j
[2025-09-06 03:25:10] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -83.887629-0.006438j
[2025-09-06 03:25:40] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -83.880477+0.000215j
[2025-09-06 03:26:11] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -83.974619-0.003313j
[2025-09-06 03:26:42] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -83.949964+0.000251j
[2025-09-06 03:27:13] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -83.974352+0.000752j
[2025-09-06 03:27:43] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -83.959499+0.005950j
[2025-09-06 03:28:14] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -83.896728-0.000206j
[2025-09-06 03:28:45] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -83.805618+0.000364j
[2025-09-06 03:29:16] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -83.783814-0.005303j
[2025-09-06 03:29:42] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -83.896539+0.001362j
[2025-09-06 03:30:02] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -83.853119+0.003429j
[2025-09-06 03:30:28] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -83.750105-0.003808j
[2025-09-06 03:30:59] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -83.724937-0.001943j
[2025-09-06 03:31:30] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -83.702289+0.001329j
[2025-09-06 03:32:00] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -83.845891-0.002796j
[2025-09-06 03:32:31] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -83.720647-0.004152j
[2025-09-06 03:33:02] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -83.851027+0.000572j
[2025-09-06 03:33:02] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-06 03:33:33] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -83.736907+0.004555j
[2025-09-06 03:34:03] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -83.917065+0.002715j
[2025-09-06 03:34:34] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -83.838525-0.002948j
[2025-09-06 03:35:00] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -83.680206-0.001071j
[2025-09-06 03:35:31] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -83.792926-0.003274j
[2025-09-06 03:36:01] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -83.794512-0.001849j
[2025-09-06 03:36:32] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -83.798835+0.002609j
[2025-09-06 03:37:03] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -83.778279+0.004059j
[2025-09-06 03:37:33] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -83.700222-0.000076j
[2025-09-06 03:38:04] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -83.660874-0.001172j
[2025-09-06 03:38:34] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -83.759139+0.005165j
[2025-09-06 03:39:05] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -83.780569-0.000486j
[2025-09-06 03:39:35] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -83.676826-0.002934j
[2025-09-06 03:40:06] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -83.742996-0.001173j
[2025-09-06 03:40:37] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -83.609909+0.005626j
[2025-09-06 03:41:07] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -83.568368-0.002012j
[2025-09-06 03:41:38] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -83.622784-0.005741j
[2025-09-06 03:42:08] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -83.593704-0.002072j
[2025-09-06 03:42:39] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -83.692429+0.001624j
[2025-09-06 03:43:10] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -83.648906+0.004175j
[2025-09-06 03:43:40] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -83.737787-0.000123j
[2025-09-06 03:44:11] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -83.845292+0.000353j
[2025-09-06 03:44:41] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -83.853231-0.006217j
[2025-09-06 03:45:12] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -83.873773+0.003661j
[2025-09-06 03:45:43] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -83.725108-0.001329j
[2025-09-06 03:46:13] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -83.812010-0.002816j
[2025-09-06 03:46:44] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -83.662390-0.001480j
[2025-09-06 03:47:14] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -83.778253-0.003905j
[2025-09-06 03:47:45] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -83.666733+0.001767j
[2025-09-06 03:48:15] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -83.655372-0.001468j
[2025-09-06 03:48:46] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -83.745877+0.003301j
[2025-09-06 03:49:17] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -83.781803-0.003716j
[2025-09-06 03:49:44] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -83.685196+0.005440j
[2025-09-06 03:50:04] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -83.701038+0.004173j
[2025-09-06 03:50:33] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -83.709818-0.000438j
[2025-09-06 03:51:04] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -83.777902-0.002209j
[2025-09-06 03:51:35] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -83.641767-0.001104j
[2025-09-06 03:52:05] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -83.614935+0.003769j
[2025-09-06 03:52:36] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -83.805170+0.001381j
[2025-09-06 03:53:07] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -83.685201+0.004150j
[2025-09-06 03:53:38] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -83.633525+0.001739j
[2025-09-06 03:54:08] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -83.519682+0.002124j
[2025-09-06 03:54:37] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -83.705459-0.000196j
[2025-09-06 03:55:05] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -83.756780+0.001055j
[2025-09-06 03:55:36] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -83.777128-0.006302j
[2025-09-06 03:56:06] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -83.891570-0.000057j
[2025-09-06 03:56:37] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -83.646526+0.001133j
[2025-09-06 03:57:07] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -83.802354-0.001444j
[2025-09-06 03:57:38] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -83.801551+0.005862j
[2025-09-06 03:58:09] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -83.797440-0.001087j
[2025-09-06 03:58:39] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -83.588789-0.003223j
[2025-09-06 03:59:10] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -83.601559+0.001676j
[2025-09-06 03:59:40] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -83.562397-0.003268j
[2025-09-06 04:00:11] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -83.622213+0.001352j
[2025-09-06 04:00:41] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -83.764421-0.003531j
[2025-09-06 04:01:12] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -83.777962-0.002797j
[2025-09-06 04:01:43] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -83.693349+0.000031j
[2025-09-06 04:02:13] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -83.800391+0.006866j
[2025-09-06 04:02:44] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -83.765224-0.002226j
[2025-09-06 04:03:14] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -83.870128-0.000335j
[2025-09-06 04:03:45] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -83.839614-0.001845j
[2025-09-06 04:04:16] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -83.846820+0.004875j
[2025-09-06 04:04:46] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -83.814005-0.000980j
[2025-09-06 04:05:17] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -83.768943-0.000790j
[2025-09-06 04:05:47] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -83.768731+0.004440j
[2025-09-06 04:06:18] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -83.763586+0.004988j
[2025-09-06 04:06:48] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -83.693142+0.003317j
[2025-09-06 04:07:19] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -83.754763-0.000737j
[2025-09-06 04:07:50] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -83.764519-0.000330j
[2025-09-06 04:08:20] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -83.745847-0.000491j
[2025-09-06 04:08:51] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -83.720868-0.005009j
[2025-09-06 04:09:21] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -83.652628+0.006269j
[2025-09-06 04:09:45] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -83.708165+0.001883j
[2025-09-06 04:10:07] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -83.841664-0.000540j
[2025-09-06 04:10:38] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -83.781017-0.004689j
[2025-09-06 04:11:09] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -83.865535+0.002137j
[2025-09-06 04:11:39] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -83.937863-0.002390j
[2025-09-06 04:12:10] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -83.793332+0.003404j
[2025-09-06 04:12:41] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -83.821069+0.001370j
[2025-09-06 04:13:12] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -83.883339+0.000890j
[2025-09-06 04:13:42] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -83.920731+0.000656j
[2025-09-06 04:14:13] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -83.833317+0.003763j
[2025-09-06 04:14:41] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -83.828004+0.000733j
[2025-09-06 04:15:10] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -83.815573-0.003549j
[2025-09-06 04:15:40] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -83.890140+0.002795j
[2025-09-06 04:16:11] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -83.846785-0.002338j
[2025-09-06 04:16:42] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -83.682801+0.002578j
[2025-09-06 04:17:12] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -83.705284+0.001797j
[2025-09-06 04:17:43] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -83.712380-0.004976j
[2025-09-06 04:18:14] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -83.698488+0.006389j
[2025-09-06 04:18:44] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -83.743825+0.002315j
[2025-09-06 04:19:15] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -83.627592+0.004897j
[2025-09-06 04:19:45] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -83.636116+0.006135j
[2025-09-06 04:20:16] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -83.836240+0.002250j
[2025-09-06 04:20:47] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -83.858595+0.000601j
[2025-09-06 04:21:17] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -83.876919-0.001244j
[2025-09-06 04:21:48] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -83.940961+0.002119j
[2025-09-06 04:22:18] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -83.962894+0.011446j
[2025-09-06 04:22:49] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -83.985794-0.002227j
[2025-09-06 04:23:19] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -84.000933+0.002090j
[2025-09-06 04:23:50] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -83.879056-0.000506j
[2025-09-06 04:24:21] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -83.819128+0.000227j
[2025-09-06 04:24:51] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -83.730718+0.000861j
[2025-09-06 04:25:22] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -83.775849-0.001809j
[2025-09-06 04:25:53] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -83.744264+0.002985j
[2025-09-06 04:25:53] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-06 04:26:23] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -83.814487+0.007616j
[2025-09-06 04:26:54] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -83.869296+0.008420j
[2025-09-06 04:27:24] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -83.916117+0.001381j
[2025-09-06 04:27:55] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -83.719471+0.006409j
[2025-09-06 04:28:26] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -83.767383+0.004572j
[2025-09-06 04:28:56] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -83.692740-0.000160j
[2025-09-06 04:29:27] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -83.943426+0.000641j
[2025-09-06 04:29:48] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -83.744984+0.000379j
[2025-09-06 04:30:13] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -83.699603-0.000999j
[2025-09-06 04:30:44] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -83.706193-0.002689j
[2025-09-06 04:31:14] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -83.716008-0.004481j
[2025-09-06 04:31:45] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -83.824313-0.002670j
[2025-09-06 04:32:16] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -83.870158-0.004777j
[2025-09-06 04:32:47] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -83.825123+0.003113j
[2025-09-06 04:33:17] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -83.641283+0.002344j
[2025-09-06 04:33:48] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -83.676917+0.004227j
[2025-09-06 04:34:20] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -83.734098+0.001665j
[2025-09-06 04:34:46] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -83.904058-0.004748j
[2025-09-06 04:35:16] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -83.905807+0.000676j
[2025-09-06 04:35:47] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -83.853843+0.006354j
[2025-09-06 04:36:17] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -83.860925-0.004181j
[2025-09-06 04:36:48] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -83.984116-0.000552j
[2025-09-06 04:37:19] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -83.962936-0.000989j
[2025-09-06 04:37:49] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -83.999613+0.005006j
[2025-09-06 04:38:20] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -83.940729-0.002013j
[2025-09-06 04:38:51] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -83.929324+0.000057j
[2025-09-06 04:39:21] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -83.851369-0.005762j
[2025-09-06 04:39:52] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -83.808647+0.005929j
[2025-09-06 04:40:23] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -83.585914+0.007743j
[2025-09-06 04:40:53] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -83.643024+0.003726j
[2025-09-06 04:41:24] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -83.638143+0.003624j
[2025-09-06 04:41:55] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -83.825599-0.002677j
[2025-09-06 04:42:25] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -83.971174-0.006238j
[2025-09-06 04:42:56] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -83.872934+0.000126j
[2025-09-06 04:43:27] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -83.866197+0.000593j
[2025-09-06 04:43:57] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -83.927964-0.004886j
[2025-09-06 04:44:28] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -83.914243+0.000346j
[2025-09-06 04:44:59] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -83.862367-0.009006j
[2025-09-06 04:45:29] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -83.842372+0.006364j
[2025-09-06 04:46:00] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -83.785826-0.002791j
[2025-09-06 04:46:30] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -83.814101-0.000138j
[2025-09-06 04:47:01] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -83.770578+0.001647j
[2025-09-06 04:47:32] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -83.801565-0.006419j
[2025-09-06 04:48:02] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -83.779585+0.002648j
[2025-09-06 04:48:33] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -83.791172-0.001741j
[2025-09-06 04:49:04] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -83.836656+0.000599j
[2025-09-06 04:49:31] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -83.806896-0.001469j
[2025-09-06 04:49:51] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -83.926532+0.000500j
[2025-09-06 04:50:20] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -83.792572+0.001384j
[2025-09-06 04:50:51] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -83.821791-0.000856j
[2025-09-06 04:51:21] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -83.832725+0.001149j
[2025-09-06 04:51:52] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -83.804896-0.002843j
[2025-09-06 04:52:23] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -83.818478-0.000877j
[2025-09-06 04:52:54] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -83.886898-0.002521j
[2025-09-06 04:53:24] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -83.870794-0.008275j
[2025-09-06 04:53:55] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -83.767810+0.000737j
[2025-09-06 04:54:23] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -83.751077-0.001566j
[2025-09-06 04:54:52] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -83.750134-0.001097j
[2025-09-06 04:55:22] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -83.747421+0.002867j
[2025-09-06 04:55:53] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -83.704451-0.002548j
[2025-09-06 04:56:24] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -83.691274-0.001210j
[2025-09-06 04:56:54] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -83.794034+0.000578j
[2025-09-06 04:57:25] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -83.802502-0.003763j
[2025-09-06 04:57:56] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -83.841536-0.005349j
[2025-09-06 04:58:26] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -83.864729+0.002227j
[2025-09-06 04:58:57] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -83.892097+0.003594j
[2025-09-06 04:59:28] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -83.732087+0.000517j
[2025-09-06 04:59:58] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -83.732263+0.000927j
[2025-09-06 05:00:29] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -83.787836+0.002349j
[2025-09-06 05:01:00] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -83.640498-0.000470j
[2025-09-06 05:01:30] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -83.590066+0.004555j
[2025-09-06 05:02:01] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -83.669688-0.001432j
[2025-09-06 05:02:32] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -83.645621-0.000798j
[2025-09-06 05:03:02] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -83.649690+0.000708j
[2025-09-06 05:03:33] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -83.597622-0.002095j
[2025-09-06 05:04:04] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -83.777848+0.002065j
[2025-09-06 05:04:34] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -83.701115-0.002939j
[2025-09-06 05:05:05] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -83.733008+0.001437j
[2025-09-06 05:05:36] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -83.762159+0.003285j
[2025-09-06 05:06:06] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -83.832495-0.001029j
[2025-09-06 05:06:37] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -83.812864+0.001449j
[2025-09-06 05:07:08] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -83.720526-0.001732j
[2025-09-06 05:07:38] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -83.676768-0.000326j
[2025-09-06 05:08:09] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -83.768869-0.003745j
[2025-09-06 05:08:40] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -83.809536-0.005546j
[2025-09-06 05:09:10] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -83.777768+0.001446j
[2025-09-06 05:09:34] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -83.547818+0.000796j
[2025-09-06 05:09:55] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -83.653997+0.003712j
[2025-09-06 05:10:26] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -83.660808+0.002022j
[2025-09-06 05:10:56] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -83.586854-0.003660j
[2025-09-06 05:11:27] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -83.557938+0.008097j
[2025-09-06 05:11:58] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -83.422503+0.000930j
[2025-09-06 05:12:29] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -83.614270-0.005559j
[2025-09-06 05:12:59] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -83.577937+0.002560j
[2025-09-06 05:13:30] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -83.629327-0.002169j
[2025-09-06 05:14:01] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -83.683569+0.003248j
[2025-09-06 05:14:29] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -83.720266+0.001411j
[2025-09-06 05:14:58] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -83.813611-0.001627j
[2025-09-06 05:15:28] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -83.668648+0.005633j
[2025-09-06 05:15:59] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -83.837083+0.004749j
[2025-09-06 05:16:30] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -83.858646-0.002254j
[2025-09-06 05:17:01] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -83.761830-0.002019j
[2025-09-06 05:17:31] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -83.722538+0.001645j
[2025-09-06 05:18:02] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -83.792023+0.000358j
[2025-09-06 05:18:33] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -83.643637-0.003614j
[2025-09-06 05:18:33] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-06 05:19:04] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -83.745977-0.003838j
[2025-09-06 05:19:34] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -83.751655-0.001253j
[2025-09-06 05:20:05] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -83.717837+0.000109j
[2025-09-06 05:20:36] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -83.843915-0.002868j
[2025-09-06 05:21:07] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -83.933706+0.001895j
[2025-09-06 05:21:37] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -83.828795-0.002800j
[2025-09-06 05:22:08] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -83.749470+0.000579j
[2025-09-06 05:22:39] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -84.026121+0.003700j
[2025-09-06 05:23:10] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -83.913239-0.003696j
[2025-09-06 05:23:40] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -83.930217+0.002518j
[2025-09-06 05:24:11] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -84.089258+0.005990j
[2025-09-06 05:24:42] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -84.039271+0.000287j
[2025-09-06 05:25:13] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -84.037633+0.001061j
[2025-09-06 05:25:44] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -83.889295-0.008508j
[2025-09-06 05:26:14] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -83.860912-0.007483j
[2025-09-06 05:26:45] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -83.881690+0.004150j
[2025-09-06 05:27:16] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -83.930942-0.004511j
[2025-09-06 05:27:47] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -83.848369-0.001899j
[2025-09-06 05:28:17] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -83.888477+0.002638j
[2025-09-06 05:28:48] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -83.756018+0.000797j
[2025-09-06 05:29:19] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -84.017512+0.001269j
[2025-09-06 05:29:40] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -83.915420+0.004755j
[2025-09-06 05:30:04] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -83.929486-0.001298j
[2025-09-06 05:30:34] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -83.720441+0.004122j
[2025-09-06 05:31:05] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -83.597481-0.000070j
[2025-09-06 05:31:36] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -83.657340+0.005255j
[2025-09-06 05:32:07] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -83.710719+0.000864j
[2025-09-06 05:32:37] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -83.741096-0.001307j
[2025-09-06 05:33:08] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -83.887942+0.004725j
[2025-09-06 05:33:39] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -83.828912-0.002349j
[2025-09-06 05:34:10] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -83.924236-0.005032j
[2025-09-06 05:34:36] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -83.798032+0.001255j
[2025-09-06 05:35:07] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -83.762933-0.000900j
[2025-09-06 05:35:37] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -83.762490+0.002056j
[2025-09-06 05:36:08] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -83.811812+0.001157j
[2025-09-06 05:36:39] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -83.821986+0.000907j
[2025-09-06 05:37:09] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -83.848424-0.000660j
[2025-09-06 05:37:40] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -83.721014-0.000088j
[2025-09-06 05:38:11] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -83.662396+0.002228j
[2025-09-06 05:38:42] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -83.722338-0.005986j
[2025-09-06 05:39:12] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -83.744199+0.000770j
[2025-09-06 05:39:43] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -83.788235+0.004438j
[2025-09-06 05:40:14] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -83.760792-0.002551j
[2025-09-06 05:40:44] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -83.842914+0.000442j
[2025-09-06 05:41:15] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -83.886279+0.002703j
[2025-09-06 05:41:46] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -83.727801-0.003237j
[2025-09-06 05:42:17] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -83.704767-0.003458j
[2025-09-06 05:42:47] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -83.612425-0.002279j
[2025-09-06 05:43:18] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -83.544195-0.001499j
[2025-09-06 05:43:49] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -83.554569-0.000385j
[2025-09-06 05:44:19] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -83.695295+0.001175j
[2025-09-06 05:44:50] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -83.752040+0.003352j
[2025-09-06 05:45:21] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -83.682112-0.001258j
[2025-09-06 05:45:51] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -83.743358-0.003304j
[2025-09-06 05:46:22] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -83.879124-0.003705j
[2025-09-06 05:46:53] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -83.815380+0.001219j
[2025-09-06 05:47:24] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -83.822290+0.003444j
[2025-09-06 05:47:54] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -83.794960+0.001443j
[2025-09-06 05:48:25] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -83.845689+0.001312j
[2025-09-06 05:48:56] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -83.883151-0.000855j
[2025-09-06 05:49:24] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -83.893364-0.000884j
[2025-09-06 05:49:45] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -83.929363-0.007587j
[2025-09-06 05:50:08] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -83.883930+0.000263j
[2025-09-06 05:50:39] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -83.944348+0.005161j
[2025-09-06 05:51:10] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -84.034992-0.001962j
[2025-09-06 05:51:41] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -83.877252-0.004666j
[2025-09-06 05:52:11] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -83.791980-0.000735j
[2025-09-06 05:52:42] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -83.778674-0.001731j
[2025-09-06 05:53:13] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -83.720607+0.003097j
[2025-09-06 05:53:43] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -83.907115+0.000867j
[2025-09-06 05:54:14] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -83.872692+0.000788j
[2025-09-06 05:54:40] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -83.845653+0.002068j
[2025-09-06 05:55:11] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -83.942586+0.003696j
[2025-09-06 05:55:42] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -83.975963-0.004364j
[2025-09-06 05:56:12] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -83.861913+0.001597j
[2025-09-06 05:56:43] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -83.747514+0.007146j
[2025-09-06 05:57:14] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -83.728355-0.003654j
[2025-09-06 05:57:45] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -83.774967-0.001380j
[2025-09-06 05:58:15] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -83.660067-0.004482j
[2025-09-06 05:58:46] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -83.777601-0.001852j
[2025-09-06 05:59:17] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -83.823352+0.002711j
[2025-09-06 05:59:48] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -83.655847-0.001609j
[2025-09-06 06:00:18] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -83.810656-0.002337j
[2025-09-06 06:00:49] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -83.652764+0.001016j
[2025-09-06 06:01:20] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -83.740473+0.001856j
[2025-09-06 06:01:51] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -83.787157-0.000005j
[2025-09-06 06:02:21] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -83.765200-0.003786j
[2025-09-06 06:02:52] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -83.771899+0.001978j
[2025-09-06 06:03:23] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -83.573359-0.001069j
[2025-09-06 06:03:54] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -83.657293+0.004018j
[2025-09-06 06:04:24] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -83.786644+0.002388j
[2025-09-06 06:04:55] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -83.773920-0.001319j
[2025-09-06 06:05:26] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -83.821613+0.004683j
[2025-09-06 06:05:57] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -83.627716+0.001369j
[2025-09-06 06:06:27] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -83.718890+0.001517j
[2025-09-06 06:06:58] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -83.789083+0.000610j
[2025-09-06 06:07:29] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -83.891572-0.000867j
[2025-09-06 06:08:00] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -83.807456-0.004593j
[2025-09-06 06:08:30] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -83.765644-0.003851j
[2025-09-06 06:09:01] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -83.754774-0.003763j
[2025-09-06 06:09:30] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -83.898050-0.001411j
[2025-09-06 06:09:51] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -83.810678+0.000392j
[2025-09-06 06:10:18] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -83.818924+0.001653j
[2025-09-06 06:10:49] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -83.814708-0.000928j
[2025-09-06 06:11:20] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -83.889881+0.002951j
[2025-09-06 06:11:20] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-06 06:11:51] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -83.837861-0.000560j
[2025-09-06 06:12:21] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -83.840518+0.001652j
[2025-09-06 06:12:52] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -83.853748+0.000898j
[2025-09-06 06:13:23] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -83.793229+0.002069j
[2025-09-06 06:13:54] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -83.802009+0.009036j
[2025-09-06 06:14:23] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -83.650338-0.001191j
[2025-09-06 06:14:51] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -83.692716+0.007080j
[2025-09-06 06:15:21] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -83.648226+0.007041j
[2025-09-06 06:15:52] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -83.780410+0.008848j
[2025-09-06 06:16:23] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -83.758318+0.002480j
[2025-09-06 06:16:53] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -83.688340-0.000063j
[2025-09-06 06:17:24] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -83.645543+0.000527j
[2025-09-06 06:17:55] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -83.721162+0.003824j
[2025-09-06 06:18:26] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -83.620283+0.000494j
[2025-09-06 06:18:56] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -83.677240+0.000422j
[2025-09-06 06:19:27] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -83.708556-0.004861j
[2025-09-06 06:19:58] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -83.781518+0.000849j
[2025-09-06 06:20:29] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -83.653612+0.002536j
[2025-09-06 06:20:59] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -83.729080+0.002181j
[2025-09-06 06:21:30] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -83.882592+0.002212j
[2025-09-06 06:22:01] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -83.881840-0.005387j
[2025-09-06 06:22:32] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -83.634637-0.001294j
[2025-09-06 06:23:02] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -83.827393-0.000621j
[2025-09-06 06:23:33] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -83.806547+0.006034j
[2025-09-06 06:24:04] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -83.700134+0.000406j
[2025-09-06 06:24:35] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -83.729122-0.000962j
[2025-09-06 06:25:05] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -83.630977-0.004897j
[2025-09-06 06:25:36] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -83.455245-0.001800j
[2025-09-06 06:26:07] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -83.580023+0.002142j
[2025-09-06 06:26:38] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -83.644605+0.001956j
[2025-09-06 06:27:09] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -83.557591-0.002517j
[2025-09-06 06:27:39] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -83.653177+0.001828j
[2025-09-06 06:28:10] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -83.574004+0.001706j
[2025-09-06 06:28:41] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -83.758911+0.000942j
[2025-09-06 06:29:12] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -83.836136-0.001346j
[2025-09-06 06:29:36] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -83.795900-0.001536j
[2025-09-06 06:29:58] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -83.851630-0.001664j
[2025-09-06 06:30:29] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -83.872030-0.003472j
[2025-09-06 06:31:00] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -83.901169-0.002066j
[2025-09-06 06:31:30] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -83.911693-0.002845j
[2025-09-06 06:32:01] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -83.661416+0.003442j
[2025-09-06 06:32:32] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -83.790203+0.001300j
[2025-09-06 06:33:03] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -83.904778-0.000369j
[2025-09-06 06:33:33] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -83.818938+0.003524j
[2025-09-06 06:34:04] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -83.723698-0.006672j
[2025-09-06 06:34:32] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -83.778151+0.000994j
[2025-09-06 06:35:00] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -83.539601+0.002782j
[2025-09-06 06:35:31] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -83.642955-0.005653j
[2025-09-06 06:36:02] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -83.648189+0.006179j
[2025-09-06 06:36:32] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -83.569160+0.003421j
[2025-09-06 06:37:03] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -83.634099-0.001772j
[2025-09-06 06:37:34] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -83.613660+0.003620j
[2025-09-06 06:38:04] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -83.550627-0.005087j
[2025-09-06 06:38:35] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -83.630777+0.002994j
[2025-09-06 06:39:06] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -83.642987-0.004899j
[2025-09-06 06:39:36] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -83.788512-0.003129j
[2025-09-06 06:40:07] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -83.671136-0.003749j
[2025-09-06 06:40:37] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -83.504867-0.003681j
[2025-09-06 06:41:08] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -83.499367-0.007484j
[2025-09-06 06:41:39] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -83.764067+0.001349j
[2025-09-06 06:42:09] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -83.822275-0.003988j
[2025-09-06 06:42:40] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -83.800981+0.000209j
[2025-09-06 06:43:11] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -83.848596+0.004551j
[2025-09-06 06:43:41] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -83.725760-0.000165j
[2025-09-06 06:44:12] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -83.683967-0.006008j
[2025-09-06 06:44:43] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -83.628327-0.000602j
[2025-09-06 06:45:13] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -83.465078+0.000557j
[2025-09-06 06:45:44] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -83.521865-0.004752j
[2025-09-06 06:46:15] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -83.496373+0.004426j
[2025-09-06 06:46:45] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -83.518526+0.004306j
[2025-09-06 06:47:16] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -83.518330+0.002325j
[2025-09-06 06:47:47] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -83.682179+0.002556j
[2025-09-06 06:48:17] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -83.798351+0.000882j
[2025-09-06 06:48:48] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -83.598773-0.000812j
[2025-09-06 06:49:19] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -83.498592+0.002080j
[2025-09-06 06:49:39] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -83.732708+0.001141j
[2025-09-06 06:50:04] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -83.553780-0.001577j
[2025-09-06 06:50:35] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -83.671910-0.002612j
[2025-09-06 06:51:05] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -83.512762+0.007635j
[2025-09-06 06:51:36] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -83.729563-0.005060j
[2025-09-06 06:52:07] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -83.605102-0.003730j
[2025-09-06 06:52:37] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -83.636545-0.002255j
[2025-09-06 06:53:08] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -83.664791-0.002712j
[2025-09-06 06:53:39] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -83.752996+0.004621j
[2025-09-06 06:54:10] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -83.728340-0.002833j
[2025-09-06 06:54:36] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -83.714347-0.004641j
[2025-09-06 06:55:06] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -83.634634+0.001769j
[2025-09-06 06:55:37] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -83.637211+0.001777j
[2025-09-06 06:56:07] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -83.617757+0.002445j
[2025-09-06 06:56:38] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -83.658062+0.002412j
[2025-09-06 06:57:09] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -83.618090-0.003130j
[2025-09-06 06:57:39] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -83.788867-0.006040j
[2025-09-06 06:58:10] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -83.816806+0.002015j
[2025-09-06 06:58:40] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -83.794918-0.000726j
[2025-09-06 06:59:11] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -83.775978+0.000629j
[2025-09-06 06:59:42] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -83.566105-0.000204j
[2025-09-06 07:00:12] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -83.692537+0.003719j
[2025-09-06 07:00:43] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -83.754652-0.002130j
[2025-09-06 07:01:14] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -83.858207-0.002089j
[2025-09-06 07:01:44] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -83.737161+0.003463j
[2025-09-06 07:02:15] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -83.880070-0.003023j
[2025-09-06 07:02:46] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -84.068286+0.000187j
[2025-09-06 07:03:16] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -83.687818+0.000721j
[2025-09-06 07:03:47] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -83.518431-0.003310j
[2025-09-06 07:04:18] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -83.532761-0.004103j
[2025-09-06 07:04:18] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-06 07:04:48] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -83.539656-0.003726j
[2025-09-06 07:05:19] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -83.656073+0.001427j
[2025-09-06 07:05:50] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -83.843121-0.000540j
[2025-09-06 07:06:20] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -83.741681+0.001447j
[2025-09-06 07:06:51] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -83.717604-0.003799j
[2025-09-06 07:07:22] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -83.768948-0.001874j
[2025-09-06 07:07:52] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -83.621378+0.002572j
[2025-09-06 07:08:23] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -83.906693-0.001407j
[2025-09-06 07:08:54] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -83.784508+0.000598j
[2025-09-06 07:09:21] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -83.743250+0.001337j
[2025-09-06 07:09:42] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -83.665989-0.000643j
[2025-09-06 07:10:10] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -83.749498+0.001137j
[2025-09-06 07:10:41] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -83.824999+0.001951j
[2025-09-06 07:11:12] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -83.811022+0.001291j
[2025-09-06 07:11:43] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -83.845068+0.003745j
[2025-09-06 07:12:13] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -83.871832+0.003262j
[2025-09-06 07:12:44] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -83.806605+0.002542j
[2025-09-06 07:13:15] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -83.843039+0.001836j
[2025-09-06 07:13:46] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -83.728207+0.000940j
[2025-09-06 07:14:14] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -83.901811+0.001477j
[2025-09-06 07:14:42] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -83.863677-0.000317j
[2025-09-06 07:15:13] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -83.726612-0.000357j
[2025-09-06 07:15:44] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -83.738718+0.000323j
[2025-09-06 07:16:15] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -83.848953-0.000083j
[2025-09-06 07:16:45] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -83.752980+0.004524j
[2025-09-06 07:17:16] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -83.665960+0.005258j
[2025-09-06 07:17:47] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -83.557209-0.001162j
[2025-09-06 07:18:18] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -83.604411-0.003449j
[2025-09-06 07:18:48] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -83.654457-0.002158j
[2025-09-06 07:19:19] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -83.727585-0.003477j
[2025-09-06 07:19:50] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -83.778812-0.002517j
[2025-09-06 07:20:21] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -83.827630+0.003861j
[2025-09-06 07:20:51] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -83.706041-0.002394j
[2025-09-06 07:21:22] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -83.679704+0.002413j
[2025-09-06 07:21:53] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -83.683328-0.006187j
[2025-09-06 07:22:24] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -83.679624+0.000520j
[2025-09-06 07:22:54] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -83.534730+0.002874j
[2025-09-06 07:23:25] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -83.721552+0.003515j
[2025-09-06 07:23:56] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -83.827293-0.002857j
[2025-09-06 07:24:27] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -83.742766-0.001240j
[2025-09-06 07:24:57] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -83.700460-0.002842j
[2025-09-06 07:25:28] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -83.749428+0.002117j
[2025-09-06 07:25:59] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -83.682978+0.005033j
[2025-09-06 07:26:30] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -83.765802-0.003889j
[2025-09-06 07:27:00] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -83.744810-0.002949j
[2025-09-06 07:27:31] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -83.726846+0.003666j
[2025-09-06 07:28:02] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -83.973402+0.001270j
[2025-09-06 07:28:33] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -83.935777-0.000325j
[2025-09-06 07:29:03] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -83.762825+0.000321j
[2025-09-06 07:29:27] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -83.877674-0.002741j
[2025-09-06 07:29:50] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -83.663904+0.003100j
[2025-09-06 07:30:21] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -83.913204+0.002409j
[2025-09-06 07:30:52] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -83.753269+0.001322j
[2025-09-06 07:31:22] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -83.778946-0.005798j
[2025-09-06 07:31:53] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -83.832360-0.002250j
[2025-09-06 07:32:24] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -83.771925+0.000494j
[2025-09-06 07:32:55] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -83.759694-0.001650j
[2025-09-06 07:33:25] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -83.730586+0.000173j
[2025-09-06 07:33:56] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -83.940884+0.005292j
[2025-09-06 07:34:23] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -83.804013-0.002159j
[2025-09-06 07:34:53] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -83.922585+0.001259j
[2025-09-06 07:35:24] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -83.928609-0.001536j
[2025-09-06 07:35:54] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -84.017769+0.004749j
[2025-09-06 07:36:25] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -83.900343-0.000431j
[2025-09-06 07:36:56] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -83.938719+0.000206j
[2025-09-06 07:37:27] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -83.800605+0.002705j
[2025-09-06 07:37:57] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -83.876758-0.000037j
[2025-09-06 07:38:28] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -83.664067+0.000919j
[2025-09-06 07:38:59] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -83.852138+0.006485j
[2025-09-06 07:39:30] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -83.714906+0.005706j
[2025-09-06 07:40:00] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -83.715136-0.000942j
[2025-09-06 07:40:31] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -83.710674-0.003777j
[2025-09-06 07:41:02] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -83.718359+0.003779j
[2025-09-06 07:41:32] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -83.744800-0.001373j
[2025-09-06 07:42:03] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -83.940177+0.001505j
[2025-09-06 07:42:34] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -83.927190+0.012261j
[2025-09-06 07:43:05] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -84.049563+0.002325j
[2025-09-06 07:43:35] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -83.936413+0.002115j
[2025-09-06 07:44:06] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -83.883928-0.006353j
[2025-09-06 07:44:37] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -84.050595-0.001255j
[2025-09-06 07:45:08] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -84.009876+0.000255j
[2025-09-06 07:45:38] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -83.952988-0.004475j
[2025-09-06 07:46:09] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -83.871694+0.005560j
[2025-09-06 07:46:40] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -83.901157-0.001714j
[2025-09-06 07:47:10] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -83.855258-0.003574j
[2025-09-06 07:47:41] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -84.058426-0.001104j
[2025-09-06 07:48:12] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -83.992672-0.001882j
[2025-09-06 07:48:43] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -83.998484+0.004473j
[2025-09-06 07:49:12] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -83.952814-0.004313j
[2025-09-06 07:49:33] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -83.907165-0.003854j
[2025-09-06 07:49:59] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -83.918606+0.000216j
[2025-09-06 07:50:30] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -84.062410-0.005014j
[2025-09-06 07:51:00] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -84.002933-0.002595j
[2025-09-06 07:51:31] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -84.052642+0.001091j
[2025-09-06 07:52:02] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -83.933149-0.003326j
[2025-09-06 07:52:33] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -83.983277-0.001125j
[2025-09-06 07:53:03] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -83.678637+0.007357j
[2025-09-06 07:53:34] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -83.641083+0.003987j
[2025-09-06 07:54:05] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -83.639058-0.005028j
[2025-09-06 07:54:31] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -83.580556-0.001268j
[2025-09-06 07:55:02] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -83.639548-0.002353j
[2025-09-06 07:55:32] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -83.651427-0.001253j
[2025-09-06 07:56:03] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -83.688663+0.000095j
[2025-09-06 07:56:30] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -83.805020-0.003499j
[2025-09-06 07:56:50] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -83.613435-0.003008j
[2025-09-06 07:56:50] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-06 07:56:50] ✅ Training completed | Restarts: 2
[2025-09-06 07:56:50] ============================================================
[2025-09-06 07:56:50] Training completed | Runtime: 31700.5s
[2025-09-06 07:56:59] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-06 07:56:59] ============================================================
