[2025-09-01 20:19:43] ==================================================
[2025-09-01 20:19:43] GCNN for Shastry-Sutherland Model
[2025-09-01 20:19:43] ==================================================
[2025-09-01 20:19:43] System parameters:
[2025-09-01 20:19:43]   - System size: L=5, N=100
[2025-09-01 20:19:43]   - System parameters: J1=0.04, J2=0.0, Q=1.0
[2025-09-01 20:19:43] --------------------------------------------------
[2025-09-01 20:19:43] Model parameters:
[2025-09-01 20:19:43]   - Number of layers = 4
[2025-09-01 20:19:43]   - Number of features = 4
[2025-09-01 20:19:43]   - Total parameters = 19628
[2025-09-01 20:19:43] --------------------------------------------------
[2025-09-01 20:19:43] Training parameters:
[2025-09-01 20:19:43]   - Learning rate: 0.015
[2025-09-01 20:19:43]   - Total iterations: 2250
[2025-09-01 20:19:43]   - Annealing cycles: 4
[2025-09-01 20:19:43]   - Initial period: 150
[2025-09-01 20:19:43]   - Period multiplier: 2.0
[2025-09-01 20:19:43]   - Temperature range: 0.0-1.0
[2025-09-01 20:19:43]   - Samples: 16384
[2025-09-01 20:19:43]   - Discarded samples: 0
[2025-09-01 20:19:43]   - Chunk size: 2048
[2025-09-01 20:19:43]   - Diagonal shift: 0.2
[2025-09-01 20:19:43]   - Gradient clipping: 1.0
[2025-09-01 20:19:43]   - Checkpoint enabled: interval=250
[2025-09-01 20:19:43]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.04/training/checkpoints
[2025-09-01 20:19:43] --------------------------------------------------
[2025-09-01 20:19:43] Device status:
[2025-09-01 20:19:43]   - Devices model: NVIDIA H200 NVL
[2025-09-01 20:19:43]   - Number of devices: 1
[2025-09-01 20:19:43]   - Sharding: True
[2025-09-01 20:19:43] ============================================================
[2025-09-01 20:21:07] [Iter 1/2250] R0[0/150], Temp: 1.0000, Energy: 1.999504-0.000052j
[2025-09-01 20:22:15] [Iter 2/2250] R0[1/150], Temp: 0.9999, Energy: 1.999373+0.000165j
[2025-09-01 20:22:55] [Iter 3/2250] R0[2/150], Temp: 0.9996, Energy: 1.999910+0.000162j
[2025-09-01 20:23:35] [Iter 4/2250] R0[3/150], Temp: 0.9990, Energy: 1.999394+0.000113j
[2025-09-01 20:24:16] [Iter 5/2250] R0[4/150], Temp: 0.9982, Energy: 1.999572+0.000034j
[2025-09-01 20:24:56] [Iter 6/2250] R0[5/150], Temp: 0.9973, Energy: 1.999755-0.000088j
[2025-09-01 20:25:37] [Iter 7/2250] R0[6/150], Temp: 0.9961, Energy: 1.999732+0.000103j
[2025-09-01 20:26:17] [Iter 8/2250] R0[7/150], Temp: 0.9946, Energy: 1.999289-0.000088j
[2025-09-01 20:26:58] [Iter 9/2250] R0[8/150], Temp: 0.9930, Energy: 1.999177+0.000042j
[2025-09-01 20:27:38] [Iter 10/2250] R0[9/150], Temp: 0.9911, Energy: 1.999682+0.000029j
[2025-09-01 20:28:18] [Iter 11/2250] R0[10/150], Temp: 0.9891, Energy: 1.999327-0.000011j
[2025-09-01 20:28:59] [Iter 12/2250] R0[11/150], Temp: 0.9868, Energy: 1.999013-0.000168j
[2025-09-01 20:29:39] [Iter 13/2250] R0[12/150], Temp: 0.9843, Energy: 1.999459-0.000129j
[2025-09-01 20:30:20] [Iter 14/2250] R0[13/150], Temp: 0.9816, Energy: 1.999265-0.000164j
[2025-09-01 20:31:00] [Iter 15/2250] R0[14/150], Temp: 0.9787, Energy: 1.999710+0.000097j
[2025-09-01 20:31:40] [Iter 16/2250] R0[15/150], Temp: 0.9755, Energy: 1.998946-0.000119j
[2025-09-01 20:32:21] [Iter 17/2250] R0[16/150], Temp: 0.9722, Energy: 1.999278-0.000157j
[2025-09-01 20:33:01] [Iter 18/2250] R0[17/150], Temp: 0.9686, Energy: 1.999765-0.000061j
[2025-09-01 20:33:42] [Iter 19/2250] R0[18/150], Temp: 0.9649, Energy: 1.998996+0.000290j
[2025-09-01 20:34:22] [Iter 20/2250] R0[19/150], Temp: 0.9609, Energy: 1.999486-0.000003j
[2025-09-01 20:35:02] [Iter 21/2250] R0[20/150], Temp: 0.9568, Energy: 1.998706+0.000091j
[2025-09-01 20:35:43] [Iter 22/2250] R0[21/150], Temp: 0.9524, Energy: 1.998860-0.000031j
[2025-09-01 20:36:23] [Iter 23/2250] R0[22/150], Temp: 0.9479, Energy: 1.999037-0.000073j
[2025-09-01 20:37:04] [Iter 24/2250] R0[23/150], Temp: 0.9431, Energy: 1.999266+0.000058j
[2025-09-01 20:37:44] [Iter 25/2250] R0[24/150], Temp: 0.9382, Energy: 1.999645+0.000006j
[2025-09-01 20:38:24] [Iter 26/2250] R0[25/150], Temp: 0.9330, Energy: 1.998997+0.000059j
[2025-09-01 20:39:05] [Iter 27/2250] R0[26/150], Temp: 0.9277, Energy: 1.998979-0.000122j
[2025-09-01 20:39:45] [Iter 28/2250] R0[27/150], Temp: 0.9222, Energy: 1.999133+0.000158j
[2025-09-01 20:40:26] [Iter 29/2250] R0[28/150], Temp: 0.9165, Energy: 1.999839-0.000004j
[2025-09-01 20:41:06] [Iter 30/2250] R0[29/150], Temp: 0.9106, Energy: 1.999637+0.000282j
[2025-09-01 20:41:46] [Iter 31/2250] R0[30/150], Temp: 0.9045, Energy: 1.998899-0.000117j
[2025-09-01 20:42:27] [Iter 32/2250] R0[31/150], Temp: 0.8983, Energy: 1.999309-0.000127j
[2025-09-01 20:43:07] [Iter 33/2250] R0[32/150], Temp: 0.8918, Energy: 1.999222+0.000051j
[2025-09-01 20:43:48] [Iter 34/2250] R0[33/150], Temp: 0.8853, Energy: 1.998850+0.000236j
[2025-09-01 20:44:28] [Iter 35/2250] R0[34/150], Temp: 0.8785, Energy: 1.998838-0.000113j
[2025-09-01 20:45:08] [Iter 36/2250] R0[35/150], Temp: 0.8716, Energy: 1.999131+0.000094j
[2025-09-01 20:45:49] [Iter 37/2250] R0[36/150], Temp: 0.8645, Energy: 1.998527+0.000016j
[2025-09-01 20:46:29] [Iter 38/2250] R0[37/150], Temp: 0.8572, Energy: 1.998874+0.000347j
[2025-09-01 20:47:10] [Iter 39/2250] R0[38/150], Temp: 0.8498, Energy: 1.998470-0.000100j
[2025-09-01 20:47:50] [Iter 40/2250] R0[39/150], Temp: 0.8423, Energy: 1.998498-0.000189j
[2025-09-01 20:48:30] [Iter 41/2250] R0[40/150], Temp: 0.8346, Energy: 1.999075-0.000192j
[2025-09-01 20:49:11] [Iter 42/2250] R0[41/150], Temp: 0.8267, Energy: 1.998753-0.000043j
[2025-09-01 20:49:51] [Iter 43/2250] R0[42/150], Temp: 0.8187, Energy: 1.998967-0.000052j
[2025-09-01 20:50:31] [Iter 44/2250] R0[43/150], Temp: 0.8106, Energy: 1.998689-0.000124j
[2025-09-01 20:51:12] [Iter 45/2250] R0[44/150], Temp: 0.8023, Energy: 1.997121-0.000008j
[2025-09-01 20:51:52] [Iter 46/2250] R0[45/150], Temp: 0.7939, Energy: 1.998697+0.000104j
[2025-09-01 20:52:33] [Iter 47/2250] R0[46/150], Temp: 0.7854, Energy: 1.999329+0.000020j
[2025-09-01 20:53:13] [Iter 48/2250] R0[47/150], Temp: 0.7767, Energy: 1.999151+0.000100j
[2025-09-01 20:53:53] [Iter 49/2250] R0[48/150], Temp: 0.7679, Energy: 1.999270+0.000177j
[2025-09-01 20:54:34] [Iter 50/2250] R0[49/150], Temp: 0.7590, Energy: 1.998913-0.000005j
[2025-09-01 20:55:14] [Iter 51/2250] R0[50/150], Temp: 0.7500, Energy: 1.999193+0.000073j
[2025-09-01 20:55:55] [Iter 52/2250] R0[51/150], Temp: 0.7409, Energy: 1.997772+0.000091j
[2025-09-01 20:56:35] [Iter 53/2250] R0[52/150], Temp: 0.7316, Energy: 1.998269+0.000059j
[2025-09-01 20:57:15] [Iter 54/2250] R0[53/150], Temp: 0.7223, Energy: 1.997443-0.000041j
[2025-09-01 20:57:56] [Iter 55/2250] R0[54/150], Temp: 0.7129, Energy: 1.998466-0.000035j
[2025-09-01 20:58:36] [Iter 56/2250] R0[55/150], Temp: 0.7034, Energy: 1.998723-0.000157j
[2025-09-01 20:59:17] [Iter 57/2250] R0[56/150], Temp: 0.6938, Energy: 1.998345-0.000111j
[2025-09-01 20:59:57] [Iter 58/2250] R0[57/150], Temp: 0.6841, Energy: 1.998024+0.000036j
[2025-09-01 21:00:37] [Iter 59/2250] R0[58/150], Temp: 0.6743, Energy: 1.997611+0.000154j
[2025-09-01 21:01:18] [Iter 60/2250] R0[59/150], Temp: 0.6644, Energy: 1.998194+0.000014j
[2025-09-01 21:01:58] [Iter 61/2250] R0[60/150], Temp: 0.6545, Energy: 1.997974-0.000080j
[2025-09-01 21:02:39] [Iter 62/2250] R0[61/150], Temp: 0.6445, Energy: 1.997771+0.000012j
[2025-09-01 21:03:19] [Iter 63/2250] R0[62/150], Temp: 0.6345, Energy: 1.997069-0.000139j
[2025-09-01 21:03:59] [Iter 64/2250] R0[63/150], Temp: 0.6243, Energy: 1.997828+0.000006j
[2025-09-01 21:04:40] [Iter 65/2250] R0[64/150], Temp: 0.6142, Energy: 1.996928+0.000031j
[2025-09-01 21:05:20] [Iter 66/2250] R0[65/150], Temp: 0.6040, Energy: 1.996537+0.000047j
[2025-09-01 21:06:00] [Iter 67/2250] R0[66/150], Temp: 0.5937, Energy: 1.997109+0.000067j
[2025-09-01 21:06:41] [Iter 68/2250] R0[67/150], Temp: 0.5834, Energy: 1.997339+0.000113j
[2025-09-01 21:07:21] [Iter 69/2250] R0[68/150], Temp: 0.5730, Energy: 1.995428-0.000311j
[2025-09-01 21:08:02] [Iter 70/2250] R0[69/150], Temp: 0.5627, Energy: 1.996324+0.000008j
[2025-09-01 21:08:42] [Iter 71/2250] R0[70/150], Temp: 0.5523, Energy: 1.995980-0.000111j
[2025-09-01 21:09:23] [Iter 72/2250] R0[71/150], Temp: 0.5418, Energy: 1.995749+0.000162j
[2025-09-01 21:10:03] [Iter 73/2250] R0[72/150], Temp: 0.5314, Energy: 1.995650-0.000131j
[2025-09-01 21:10:43] [Iter 74/2250] R0[73/150], Temp: 0.5209, Energy: 1.994180+0.000057j
[2025-09-01 21:11:24] [Iter 75/2250] R0[74/150], Temp: 0.5105, Energy: 1.996294+0.000062j
[2025-09-01 21:12:04] [Iter 76/2250] R0[75/150], Temp: 0.5000, Energy: 1.994611+0.000169j
[2025-09-01 21:12:44] [Iter 77/2250] R0[76/150], Temp: 0.4895, Energy: 1.994649-0.000192j
[2025-09-01 21:13:25] [Iter 78/2250] R0[77/150], Temp: 0.4791, Energy: 1.993385+0.000141j
[2025-09-01 21:14:05] [Iter 79/2250] R0[78/150], Temp: 0.4686, Energy: 1.994833-0.000051j
[2025-09-01 21:14:46] [Iter 80/2250] R0[79/150], Temp: 0.4582, Energy: 1.992272+0.000002j
[2025-09-01 21:15:26] [Iter 81/2250] R0[80/150], Temp: 0.4477, Energy: 1.993331-0.000073j
[2025-09-01 21:16:06] [Iter 82/2250] R0[81/150], Temp: 0.4373, Energy: 1.992813-0.000098j
[2025-09-01 21:16:47] [Iter 83/2250] R0[82/150], Temp: 0.4270, Energy: 1.990600+0.000051j
[2025-09-01 21:17:27] [Iter 84/2250] R0[83/150], Temp: 0.4166, Energy: 1.992755-0.000132j
[2025-09-01 21:18:08] [Iter 85/2250] R0[84/150], Temp: 0.4063, Energy: 1.991584+0.000509j
[2025-09-01 21:18:48] [Iter 86/2250] R0[85/150], Temp: 0.3960, Energy: 1.991634-0.000329j
[2025-09-01 21:19:28] [Iter 87/2250] R0[86/150], Temp: 0.3858, Energy: 1.989670+0.000036j
[2025-09-01 21:20:09] [Iter 88/2250] R0[87/150], Temp: 0.3757, Energy: 1.986839-0.000106j
[2025-09-01 21:20:49] [Iter 89/2250] R0[88/150], Temp: 0.3655, Energy: 1.985795+0.000023j
[2025-09-01 21:21:30] [Iter 90/2250] R0[89/150], Temp: 0.3555, Energy: 1.987143+0.000220j
[2025-09-01 21:22:10] [Iter 91/2250] R0[90/150], Temp: 0.3455, Energy: 1.983853-0.000112j
[2025-09-01 21:22:50] [Iter 92/2250] R0[91/150], Temp: 0.3356, Energy: 1.981441-0.000161j
[2025-09-01 21:23:31] [Iter 93/2250] R0[92/150], Temp: 0.3257, Energy: 1.984935-0.000039j
[2025-09-01 21:24:11] [Iter 94/2250] R0[93/150], Temp: 0.3159, Energy: 1.980892+0.000510j
[2025-09-01 21:24:51] [Iter 95/2250] R0[94/150], Temp: 0.3062, Energy: 1.981486-0.000501j
[2025-09-01 21:25:32] [Iter 96/2250] R0[95/150], Temp: 0.2966, Energy: 1.975296-0.000397j
[2025-09-01 21:26:12] [Iter 97/2250] R0[96/150], Temp: 0.2871, Energy: 1.978681-0.000067j
[2025-09-01 21:26:53] [Iter 98/2250] R0[97/150], Temp: 0.2777, Energy: 1.969587+0.000503j
[2025-09-01 21:27:33] [Iter 99/2250] R0[98/150], Temp: 0.2684, Energy: 1.969630+0.000660j
[2025-09-01 21:28:13] [Iter 100/2250] R0[99/150], Temp: 0.2591, Energy: 1.967825-0.000431j
[2025-09-01 21:28:54] [Iter 101/2250] R0[100/150], Temp: 0.2500, Energy: 1.967547+0.000201j
[2025-09-01 21:29:34] [Iter 102/2250] R0[101/150], Temp: 0.2410, Energy: 1.960938-0.000209j
[2025-09-01 21:30:15] [Iter 103/2250] R0[102/150], Temp: 0.2321, Energy: 1.958134+0.000305j
[2025-09-01 21:30:55] [Iter 104/2250] R0[103/150], Temp: 0.2233, Energy: 1.953332+0.000093j
[2025-09-01 21:31:35] [Iter 105/2250] R0[104/150], Temp: 0.2146, Energy: 1.953427-0.000187j
[2025-09-01 21:32:16] [Iter 106/2250] R0[105/150], Temp: 0.2061, Energy: 1.945534+0.000320j
[2025-09-01 21:32:56] [Iter 107/2250] R0[106/150], Temp: 0.1977, Energy: 1.933131+0.000229j
[2025-09-01 21:33:37] [Iter 108/2250] R0[107/150], Temp: 0.1894, Energy: 1.936907+0.000067j
[2025-09-01 21:34:17] [Iter 109/2250] R0[108/150], Temp: 0.1813, Energy: 1.930876+0.000398j
[2025-09-01 21:34:57] [Iter 110/2250] R0[109/150], Temp: 0.1733, Energy: 1.909066-0.000500j
[2025-09-01 21:35:38] [Iter 111/2250] R0[110/150], Temp: 0.1654, Energy: 1.908434+0.000068j
[2025-09-01 21:36:18] [Iter 112/2250] R0[111/150], Temp: 0.1577, Energy: 1.902163-0.000074j
[2025-09-01 21:36:59] [Iter 113/2250] R0[112/150], Temp: 0.1502, Energy: 1.901035+0.000089j
[2025-09-01 21:37:39] [Iter 114/2250] R0[113/150], Temp: 0.1428, Energy: 1.890557-0.000097j
[2025-09-01 21:38:19] [Iter 115/2250] R0[114/150], Temp: 0.1355, Energy: 1.875005-0.000800j
[2025-09-01 21:39:00] [Iter 116/2250] R0[115/150], Temp: 0.1284, Energy: 1.859242-0.000333j
[2025-09-01 21:39:40] [Iter 117/2250] R0[116/150], Temp: 0.1215, Energy: 1.841546+0.000652j
[2025-09-01 21:40:21] [Iter 118/2250] R0[117/150], Temp: 0.1147, Energy: 1.820415+0.001145j
[2025-09-01 21:41:01] [Iter 119/2250] R0[118/150], Temp: 0.1082, Energy: 1.799273-0.001267j
[2025-09-01 21:41:41] [Iter 120/2250] R0[119/150], Temp: 0.1017, Energy: 1.790814-0.000541j
[2025-09-01 21:42:22] [Iter 121/2250] R0[120/150], Temp: 0.0955, Energy: 1.760374-0.000918j
[2025-09-01 21:43:02] [Iter 122/2250] R0[121/150], Temp: 0.0894, Energy: 1.725325-0.000996j
[2025-09-01 21:43:43] [Iter 123/2250] R0[122/150], Temp: 0.0835, Energy: 1.702885-0.000555j
[2025-09-01 21:44:23] [Iter 124/2250] R0[123/150], Temp: 0.0778, Energy: 1.676302-0.000483j
[2025-09-01 21:45:03] [Iter 125/2250] R0[124/150], Temp: 0.0723, Energy: 1.648210+0.002242j
[2025-09-01 21:45:44] [Iter 126/2250] R0[125/150], Temp: 0.0670, Energy: 1.603990+0.000004j
[2025-09-01 21:46:24] [Iter 127/2250] R0[126/150], Temp: 0.0618, Energy: 1.545451+0.000429j
[2025-09-01 21:47:04] [Iter 128/2250] R0[127/150], Temp: 0.0569, Energy: 1.505315+0.002070j
[2025-09-01 21:47:45] [Iter 129/2250] R0[128/150], Temp: 0.0521, Energy: 1.448516-0.000052j
[2025-09-01 21:48:25] [Iter 130/2250] R0[129/150], Temp: 0.0476, Energy: 1.387261-0.000946j
[2025-09-01 21:49:06] [Iter 131/2250] R0[130/150], Temp: 0.0432, Energy: 1.340659-0.005563j
[2025-09-01 21:49:46] [Iter 132/2250] R0[131/150], Temp: 0.0391, Energy: 1.231626-0.001892j
[2025-09-01 21:50:26] [Iter 133/2250] R0[132/150], Temp: 0.0351, Energy: 1.137326-0.002145j
[2025-09-01 21:51:07] [Iter 134/2250] R0[133/150], Temp: 0.0314, Energy: 1.045954-0.000883j
[2025-09-01 21:51:47] [Iter 135/2250] R0[134/150], Temp: 0.0278, Energy: 0.950488+0.001003j
[2025-09-01 21:52:27] [Iter 136/2250] R0[135/150], Temp: 0.0245, Energy: 0.826616+0.002826j
[2025-09-01 21:53:08] [Iter 137/2250] R0[136/150], Temp: 0.0213, Energy: 0.694694-0.000598j
[2025-09-01 21:53:48] [Iter 138/2250] R0[137/150], Temp: 0.0184, Energy: 0.558901+0.002478j
[2025-09-01 21:54:29] [Iter 139/2250] R0[138/150], Temp: 0.0157, Energy: 0.384948+0.001195j
[2025-09-01 21:55:09] [Iter 140/2250] R0[139/150], Temp: 0.0132, Energy: 0.174916+0.000114j
[2025-09-01 21:55:49] [Iter 141/2250] R0[140/150], Temp: 0.0109, Energy: 0.002869-0.005966j
[2025-09-01 21:56:30] [Iter 142/2250] R0[141/150], Temp: 0.0089, Energy: -0.233524-0.004331j
[2025-09-01 21:57:10] [Iter 143/2250] R0[142/150], Temp: 0.0070, Energy: -0.463399-0.000440j
[2025-09-01 21:57:51] [Iter 144/2250] R0[143/150], Temp: 0.0054, Energy: -0.788107+0.002345j
[2025-09-01 21:58:31] [Iter 145/2250] R0[144/150], Temp: 0.0039, Energy: -1.020445+0.001205j
[2025-09-01 21:59:11] [Iter 146/2250] R0[145/150], Temp: 0.0027, Energy: -1.406814-0.001310j
[2025-09-01 21:59:52] [Iter 147/2250] R0[146/150], Temp: 0.0018, Energy: -1.795184+0.000812j
[2025-09-01 22:00:32] [Iter 148/2250] R0[147/150], Temp: 0.0010, Energy: -2.186433-0.001482j
[2025-09-01 22:01:12] [Iter 149/2250] R0[148/150], Temp: 0.0004, Energy: -2.624844-0.001125j
[2025-09-01 22:01:53] [Iter 150/2250] R0[149/150], Temp: 0.0001, Energy: -3.083879+0.003453j
[2025-09-01 22:01:53] RESTART #1 | Period: 300
[2025-09-01 22:02:33] [Iter 151/2250] R1[0/300], Temp: 1.0000, Energy: -3.554391+0.001451j
[2025-09-01 22:03:14] [Iter 152/2250] R1[1/300], Temp: 1.0000, Energy: -4.244825+0.010620j
[2025-09-01 22:03:54] [Iter 153/2250] R1[2/300], Temp: 0.9999, Energy: -4.833058-0.007348j
[2025-09-01 22:04:34] [Iter 154/2250] R1[3/300], Temp: 0.9998, Energy: -5.484654-0.017029j
[2025-09-01 22:05:15] [Iter 155/2250] R1[4/300], Temp: 0.9996, Energy: -6.254135+0.003579j
[2025-09-01 22:05:55] [Iter 156/2250] R1[5/300], Temp: 0.9993, Energy: -6.980558-0.026868j
[2025-09-01 22:06:35] [Iter 157/2250] R1[6/300], Temp: 0.9990, Energy: -7.815703-0.010745j
[2025-09-01 22:07:16] [Iter 158/2250] R1[7/300], Temp: 0.9987, Energy: -8.801611+0.024786j
[2025-09-01 22:07:56] [Iter 159/2250] R1[8/300], Temp: 0.9982, Energy: -9.804231-0.018237j
[2025-09-01 22:08:36] [Iter 160/2250] R1[9/300], Temp: 0.9978, Energy: -10.714832+0.009352j
[2025-09-01 22:09:17] [Iter 161/2250] R1[10/300], Temp: 0.9973, Energy: -11.943997+0.018803j
[2025-09-01 22:09:57] [Iter 162/2250] R1[11/300], Temp: 0.9967, Energy: -13.012407+0.022252j
[2025-09-01 22:10:37] [Iter 163/2250] R1[12/300], Temp: 0.9961, Energy: -14.353498-0.020420j
[2025-09-01 22:11:18] [Iter 164/2250] R1[13/300], Temp: 0.9954, Energy: -15.662562+0.001985j
[2025-09-01 22:11:58] [Iter 165/2250] R1[14/300], Temp: 0.9946, Energy: -17.046139+0.019773j
[2025-09-01 22:12:38] [Iter 166/2250] R1[15/300], Temp: 0.9938, Energy: -18.553578-0.018973j
[2025-09-01 22:13:19] [Iter 167/2250] R1[16/300], Temp: 0.9930, Energy: -20.052026-0.063919j
[2025-09-01 22:13:59] [Iter 168/2250] R1[17/300], Temp: 0.9921, Energy: -21.672131+0.070870j
[2025-09-01 22:14:39] [Iter 169/2250] R1[18/300], Temp: 0.9911, Energy: -23.422338+0.037540j
[2025-09-01 22:15:20] [Iter 170/2250] R1[19/300], Temp: 0.9901, Energy: -25.364076+0.029118j
[2025-09-01 22:16:00] [Iter 171/2250] R1[20/300], Temp: 0.9891, Energy: -27.265725-0.004695j
[2025-09-01 22:16:40] [Iter 172/2250] R1[21/300], Temp: 0.9880, Energy: -29.050991+0.011708j
[2025-09-01 22:17:21] [Iter 173/2250] R1[22/300], Temp: 0.9868, Energy: -31.121894+0.070363j
[2025-09-01 22:18:01] [Iter 174/2250] R1[23/300], Temp: 0.9856, Energy: -33.242154+0.058647j
[2025-09-01 22:18:42] [Iter 175/2250] R1[24/300], Temp: 0.9843, Energy: -35.377451+0.008406j
[2025-09-01 22:19:22] [Iter 176/2250] R1[25/300], Temp: 0.9830, Energy: -37.840972+0.195823j
[2025-09-01 22:20:02] [Iter 177/2250] R1[26/300], Temp: 0.9816, Energy: -40.267733+0.192661j
[2025-09-01 22:20:43] [Iter 178/2250] R1[27/300], Temp: 0.9801, Energy: -43.218251+0.116277j
[2025-09-01 22:21:23] [Iter 179/2250] R1[28/300], Temp: 0.9787, Energy: -46.239304+0.270770j
[2025-09-01 22:22:03] [Iter 180/2250] R1[29/300], Temp: 0.9771, Energy: -49.487134+0.213264j
[2025-09-01 22:22:44] [Iter 181/2250] R1[30/300], Temp: 0.9755, Energy: -53.193713+0.240575j
[2025-09-01 22:23:24] [Iter 182/2250] R1[31/300], Temp: 0.9739, Energy: -56.794499+0.363838j
[2025-09-01 22:24:04] [Iter 183/2250] R1[32/300], Temp: 0.9722, Energy: -60.390862+0.363435j
[2025-09-01 22:24:45] [Iter 184/2250] R1[33/300], Temp: 0.9704, Energy: -64.149761+0.368863j
[2025-09-01 22:25:25] [Iter 185/2250] R1[34/300], Temp: 0.9686, Energy: -67.048232+0.393268j
[2025-09-01 22:26:05] [Iter 186/2250] R1[35/300], Temp: 0.9668, Energy: -69.451697+0.363465j
[2025-09-01 22:26:46] [Iter 187/2250] R1[36/300], Temp: 0.9649, Energy: -71.280776+0.269696j
[2025-09-01 22:27:26] [Iter 188/2250] R1[37/300], Temp: 0.9629, Energy: -72.783022+0.236911j
[2025-09-01 22:28:07] [Iter 189/2250] R1[38/300], Temp: 0.9609, Energy: -73.974537+0.216395j
[2025-09-01 22:28:47] [Iter 190/2250] R1[39/300], Temp: 0.9589, Energy: -75.070486+0.195654j
[2025-09-01 22:29:27] [Iter 191/2250] R1[40/300], Temp: 0.9568, Energy: -75.596115+0.150734j
[2025-09-01 22:30:07] [Iter 192/2250] R1[41/300], Temp: 0.9546, Energy: -76.267458+0.093133j
[2025-09-01 22:30:48] [Iter 193/2250] R1[42/300], Temp: 0.9524, Energy: -76.824745+0.114978j
[2025-09-01 22:31:28] [Iter 194/2250] R1[43/300], Temp: 0.9502, Energy: -77.433996+0.087200j
[2025-09-01 22:32:09] [Iter 195/2250] R1[44/300], Temp: 0.9479, Energy: -77.987859+0.056678j
[2025-09-01 22:32:49] [Iter 196/2250] R1[45/300], Temp: 0.9455, Energy: -78.428497+0.081484j
[2025-09-01 22:33:29] [Iter 197/2250] R1[46/300], Temp: 0.9431, Energy: -78.680393+0.015212j
[2025-09-01 22:34:10] [Iter 198/2250] R1[47/300], Temp: 0.9407, Energy: -78.958808+0.040423j
[2025-09-01 22:34:50] [Iter 199/2250] R1[48/300], Temp: 0.9382, Energy: -79.197039+0.025893j
[2025-09-01 22:35:30] [Iter 200/2250] R1[49/300], Temp: 0.9356, Energy: -79.466634-0.004117j
[2025-09-01 22:36:11] [Iter 201/2250] R1[50/300], Temp: 0.9330, Energy: -79.634231-0.013986j
[2025-09-01 22:36:51] [Iter 202/2250] R1[51/300], Temp: 0.9304, Energy: -79.854171-0.011980j
[2025-09-01 22:37:31] [Iter 203/2250] R1[52/300], Temp: 0.9277, Energy: -80.006770+0.003523j
[2025-09-01 22:38:12] [Iter 204/2250] R1[53/300], Temp: 0.9249, Energy: -80.172638-0.017579j
[2025-09-01 22:38:52] [Iter 205/2250] R1[54/300], Temp: 0.9222, Energy: -80.371921-0.002045j
[2025-09-01 22:39:32] [Iter 206/2250] R1[55/300], Temp: 0.9193, Energy: -80.524396-0.032355j
[2025-09-01 22:40:12] [Iter 207/2250] R1[56/300], Temp: 0.9165, Energy: -80.699345-0.019520j
[2025-09-01 22:40:53] [Iter 208/2250] R1[57/300], Temp: 0.9135, Energy: -80.880144+0.011609j
[2025-09-01 22:41:33] [Iter 209/2250] R1[58/300], Temp: 0.9106, Energy: -81.031516-0.006422j
[2025-09-01 22:42:13] [Iter 210/2250] R1[59/300], Temp: 0.9076, Energy: -81.142051-0.025730j
[2025-09-01 22:42:54] [Iter 211/2250] R1[60/300], Temp: 0.9045, Energy: -81.252438-0.009892j
[2025-09-01 22:43:34] [Iter 212/2250] R1[61/300], Temp: 0.9014, Energy: -81.434493-0.033033j
[2025-09-01 22:44:14] [Iter 213/2250] R1[62/300], Temp: 0.8983, Energy: -81.464314-0.010206j
[2025-09-01 22:44:54] [Iter 214/2250] R1[63/300], Temp: 0.8951, Energy: -81.619912+0.008292j
[2025-09-01 22:45:35] [Iter 215/2250] R1[64/300], Temp: 0.8918, Energy: -81.745022+0.014252j
[2025-09-01 22:46:15] [Iter 216/2250] R1[65/300], Temp: 0.8886, Energy: -81.857248-0.012021j
[2025-09-01 22:46:55] [Iter 217/2250] R1[66/300], Temp: 0.8853, Energy: -81.882644-0.015562j
[2025-09-01 22:47:35] [Iter 218/2250] R1[67/300], Temp: 0.8819, Energy: -81.990536-0.021360j
[2025-09-01 22:48:16] [Iter 219/2250] R1[68/300], Temp: 0.8785, Energy: -82.105915-0.017863j
[2025-09-01 22:48:56] [Iter 220/2250] R1[69/300], Temp: 0.8751, Energy: -82.128738+0.000356j
[2025-09-01 22:49:36] [Iter 221/2250] R1[70/300], Temp: 0.8716, Energy: -82.222230-0.004402j
[2025-09-01 22:50:16] [Iter 222/2250] R1[71/300], Temp: 0.8680, Energy: -82.340105-0.022800j
[2025-09-01 22:50:57] [Iter 223/2250] R1[72/300], Temp: 0.8645, Energy: -82.455936+0.013792j
[2025-09-01 22:51:37] [Iter 224/2250] R1[73/300], Temp: 0.8609, Energy: -82.444788-0.002166j
[2025-09-01 22:52:17] [Iter 225/2250] R1[74/300], Temp: 0.8572, Energy: -82.653732-0.017136j
[2025-09-01 22:52:58] [Iter 226/2250] R1[75/300], Temp: 0.8536, Energy: -82.709460-0.009164j
[2025-09-01 22:53:38] [Iter 227/2250] R1[76/300], Temp: 0.8498, Energy: -82.595899+0.024403j
[2025-09-01 22:54:18] [Iter 228/2250] R1[77/300], Temp: 0.8461, Energy: -82.647713+0.004595j
[2025-09-01 22:54:58] [Iter 229/2250] R1[78/300], Temp: 0.8423, Energy: -82.736748-0.008583j
[2025-09-01 22:55:39] [Iter 230/2250] R1[79/300], Temp: 0.8384, Energy: -82.712918+0.000836j
[2025-09-01 22:56:19] [Iter 231/2250] R1[80/300], Temp: 0.8346, Energy: -82.776813-0.005806j
[2025-09-01 22:56:59] [Iter 232/2250] R1[81/300], Temp: 0.8307, Energy: -82.783013+0.014622j
[2025-09-01 22:57:39] [Iter 233/2250] R1[82/300], Temp: 0.8267, Energy: -82.799573+0.000259j
[2025-09-01 22:58:20] [Iter 234/2250] R1[83/300], Temp: 0.8227, Energy: -82.894921-0.003256j
[2025-09-01 22:59:00] [Iter 235/2250] R1[84/300], Temp: 0.8187, Energy: -82.876535+0.004995j
[2025-09-01 22:59:40] [Iter 236/2250] R1[85/300], Temp: 0.8147, Energy: -82.986277+0.001539j
[2025-09-01 23:00:20] [Iter 237/2250] R1[86/300], Temp: 0.8106, Energy: -82.929339+0.011515j
[2025-09-01 23:01:01] [Iter 238/2250] R1[87/300], Temp: 0.8065, Energy: -82.986703-0.012021j
[2025-09-01 23:01:41] [Iter 239/2250] R1[88/300], Temp: 0.8023, Energy: -83.076108-0.005799j
[2025-09-01 23:02:21] [Iter 240/2250] R1[89/300], Temp: 0.7981, Energy: -83.110895-0.003241j
[2025-09-01 23:03:02] [Iter 241/2250] R1[90/300], Temp: 0.7939, Energy: -83.150401-0.004744j
[2025-09-01 23:03:42] [Iter 242/2250] R1[91/300], Temp: 0.7896, Energy: -83.211076-0.007681j
[2025-09-01 23:04:22] [Iter 243/2250] R1[92/300], Temp: 0.7854, Energy: -83.278679+0.005756j
[2025-09-01 23:05:02] [Iter 244/2250] R1[93/300], Temp: 0.7810, Energy: -83.339126-0.010387j
[2025-09-01 23:05:43] [Iter 245/2250] R1[94/300], Temp: 0.7767, Energy: -83.351939+0.015973j
[2025-09-01 23:06:23] [Iter 246/2250] R1[95/300], Temp: 0.7723, Energy: -83.432124+0.000546j
[2025-09-01 23:07:03] [Iter 247/2250] R1[96/300], Temp: 0.7679, Energy: -83.381067-0.015571j
[2025-09-01 23:07:43] [Iter 248/2250] R1[97/300], Temp: 0.7635, Energy: -83.437126+0.002899j
[2025-09-01 23:08:24] [Iter 249/2250] R1[98/300], Temp: 0.7590, Energy: -83.499658+0.005550j
[2025-09-01 23:09:04] [Iter 250/2250] R1[99/300], Temp: 0.7545, Energy: -83.472651+0.007806j
[2025-09-01 23:09:04] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-09-01 23:09:44] [Iter 251/2250] R1[100/300], Temp: 0.7500, Energy: -83.552279+0.002213j
[2025-09-01 23:10:25] [Iter 252/2250] R1[101/300], Temp: 0.7455, Energy: -83.583266+0.013952j
[2025-09-01 23:11:05] [Iter 253/2250] R1[102/300], Temp: 0.7409, Energy: -83.614819-0.001828j
[2025-09-01 23:11:45] [Iter 254/2250] R1[103/300], Temp: 0.7363, Energy: -83.642453+0.015281j
[2025-09-01 23:12:25] [Iter 255/2250] R1[104/300], Temp: 0.7316, Energy: -83.622194+0.007302j
[2025-09-01 23:13:06] [Iter 256/2250] R1[105/300], Temp: 0.7270, Energy: -83.701819+0.015536j
[2025-09-01 23:13:46] [Iter 257/2250] R1[106/300], Temp: 0.7223, Energy: -83.732816+0.019003j
[2025-09-01 23:14:26] [Iter 258/2250] R1[107/300], Temp: 0.7176, Energy: -83.749480+0.000793j
[2025-09-01 23:15:07] [Iter 259/2250] R1[108/300], Temp: 0.7129, Energy: -83.729120+0.005149j
[2025-09-01 23:15:47] [Iter 260/2250] R1[109/300], Temp: 0.7081, Energy: -83.706360-0.018975j
[2025-09-01 23:16:27] [Iter 261/2250] R1[110/300], Temp: 0.7034, Energy: -83.686725+0.014950j
[2025-09-01 23:17:08] [Iter 262/2250] R1[111/300], Temp: 0.6986, Energy: -83.795077+0.015549j
[2025-09-01 23:17:48] [Iter 263/2250] R1[112/300], Temp: 0.6938, Energy: -83.796327+0.004284j
[2025-09-01 23:18:29] [Iter 264/2250] R1[113/300], Temp: 0.6889, Energy: -83.800174-0.025195j
[2025-09-01 23:19:09] [Iter 265/2250] R1[114/300], Temp: 0.6841, Energy: -83.869166+0.008372j
[2025-09-01 23:19:50] [Iter 266/2250] R1[115/300], Temp: 0.6792, Energy: -83.962900-0.001535j
[2025-09-01 23:20:31] [Iter 267/2250] R1[116/300], Temp: 0.6743, Energy: -83.923960-0.004556j
[2025-09-01 23:21:11] [Iter 268/2250] R1[117/300], Temp: 0.6694, Energy: -83.886920-0.003138j
[2025-09-01 23:21:52] [Iter 269/2250] R1[118/300], Temp: 0.6644, Energy: -83.947488+0.006810j
[2025-09-01 23:22:33] [Iter 270/2250] R1[119/300], Temp: 0.6595, Energy: -83.963050+0.023218j
[2025-09-01 23:23:13] [Iter 271/2250] R1[120/300], Temp: 0.6545, Energy: -83.913180+0.006864j
[2025-09-01 23:23:54] [Iter 272/2250] R1[121/300], Temp: 0.6495, Energy: -83.931107-0.001783j
[2025-09-01 23:24:35] [Iter 273/2250] R1[122/300], Temp: 0.6445, Energy: -83.988359-0.002820j
[2025-09-01 23:25:15] [Iter 274/2250] R1[123/300], Temp: 0.6395, Energy: -84.057298-0.019909j
[2025-09-01 23:25:56] [Iter 275/2250] R1[124/300], Temp: 0.6345, Energy: -84.079786-0.001677j
[2025-09-01 23:26:36] [Iter 276/2250] R1[125/300], Temp: 0.6294, Energy: -84.077468+0.005209j
[2025-09-01 23:27:17] [Iter 277/2250] R1[126/300], Temp: 0.6243, Energy: -84.132442-0.014653j
[2025-09-01 23:27:58] [Iter 278/2250] R1[127/300], Temp: 0.6193, Energy: -84.199785+0.002075j
[2025-09-01 23:28:38] [Iter 279/2250] R1[128/300], Temp: 0.6142, Energy: -84.154902+0.016287j
[2025-09-01 23:29:19] [Iter 280/2250] R1[129/300], Temp: 0.6091, Energy: -84.253428-0.000476j
[2025-09-01 23:29:59] [Iter 281/2250] R1[130/300], Temp: 0.6040, Energy: -84.288013-0.005435j
[2025-09-01 23:30:40] [Iter 282/2250] R1[131/300], Temp: 0.5988, Energy: -84.191662+0.014414j
[2025-09-01 23:31:21] [Iter 283/2250] R1[132/300], Temp: 0.5937, Energy: -84.271369-0.006925j
[2025-09-01 23:32:01] [Iter 284/2250] R1[133/300], Temp: 0.5885, Energy: -84.249832-0.019634j
[2025-09-01 23:32:42] [Iter 285/2250] R1[134/300], Temp: 0.5834, Energy: -84.236486-0.005936j
[2025-09-01 23:33:23] [Iter 286/2250] R1[135/300], Temp: 0.5782, Energy: -84.217303-0.008473j
[2025-09-01 23:34:03] [Iter 287/2250] R1[136/300], Temp: 0.5730, Energy: -84.262043+0.006602j
[2025-09-01 23:34:44] [Iter 288/2250] R1[137/300], Temp: 0.5679, Energy: -84.306026-0.009673j
[2025-09-01 23:35:25] [Iter 289/2250] R1[138/300], Temp: 0.5627, Energy: -84.377381-0.005642j
[2025-09-01 23:36:05] [Iter 290/2250] R1[139/300], Temp: 0.5575, Energy: -84.374438+0.004429j
[2025-09-01 23:36:46] [Iter 291/2250] R1[140/300], Temp: 0.5523, Energy: -84.394274-0.008298j
[2025-09-01 23:37:27] [Iter 292/2250] R1[141/300], Temp: 0.5471, Energy: -84.440724+0.004596j
[2025-09-01 23:38:07] [Iter 293/2250] R1[142/300], Temp: 0.5418, Energy: -84.435431-0.000908j
[2025-09-01 23:38:48] [Iter 294/2250] R1[143/300], Temp: 0.5366, Energy: -84.468670-0.005003j
[2025-09-01 23:39:29] [Iter 295/2250] R1[144/300], Temp: 0.5314, Energy: -84.402985+0.006338j
[2025-09-01 23:40:09] [Iter 296/2250] R1[145/300], Temp: 0.5262, Energy: -84.461794+0.002790j
[2025-09-01 23:40:50] [Iter 297/2250] R1[146/300], Temp: 0.5209, Energy: -84.428127-0.003387j
[2025-09-01 23:41:30] [Iter 298/2250] R1[147/300], Temp: 0.5157, Energy: -84.485985+0.000984j
[2025-09-01 23:42:11] [Iter 299/2250] R1[148/300], Temp: 0.5105, Energy: -84.509374-0.010056j
[2025-09-01 23:42:51] [Iter 300/2250] R1[149/300], Temp: 0.5052, Energy: -84.493828-0.002465j
[2025-09-01 23:43:32] [Iter 301/2250] R1[150/300], Temp: 0.5000, Energy: -84.485619-0.008070j
[2025-09-01 23:44:12] [Iter 302/2250] R1[151/300], Temp: 0.4948, Energy: -84.566816+0.022418j
[2025-09-01 23:44:53] [Iter 303/2250] R1[152/300], Temp: 0.4895, Energy: -84.564741-0.003204j
[2025-09-01 23:45:33] [Iter 304/2250] R1[153/300], Temp: 0.4843, Energy: -84.598267+0.009571j
[2025-09-01 23:46:14] [Iter 305/2250] R1[154/300], Temp: 0.4791, Energy: -84.606501-0.003777j
[2025-09-01 23:46:55] [Iter 306/2250] R1[155/300], Temp: 0.4738, Energy: -84.574361+0.003479j
[2025-09-01 23:47:36] [Iter 307/2250] R1[156/300], Temp: 0.4686, Energy: -84.564096-0.002664j
[2025-09-01 23:48:16] [Iter 308/2250] R1[157/300], Temp: 0.4634, Energy: -84.608722+0.005797j
[2025-09-01 23:48:57] [Iter 309/2250] R1[158/300], Temp: 0.4582, Energy: -84.538272-0.001235j
[2025-09-01 23:49:38] [Iter 310/2250] R1[159/300], Temp: 0.4529, Energy: -84.671127+0.002705j
[2025-09-01 23:50:18] [Iter 311/2250] R1[160/300], Temp: 0.4477, Energy: -84.559027-0.001386j
[2025-09-01 23:50:59] [Iter 312/2250] R1[161/300], Temp: 0.4425, Energy: -84.554490+0.014164j
[2025-09-01 23:51:39] [Iter 313/2250] R1[162/300], Temp: 0.4373, Energy: -84.613308-0.005271j
[2025-09-01 23:52:20] [Iter 314/2250] R1[163/300], Temp: 0.4321, Energy: -84.584507+0.004745j
[2025-09-01 23:53:02] [Iter 315/2250] R1[164/300], Temp: 0.4270, Energy: -84.532220+0.000477j
[2025-09-01 23:53:43] [Iter 316/2250] R1[165/300], Temp: 0.4218, Energy: -84.603335-0.007397j
[2025-09-01 23:54:24] [Iter 317/2250] R1[166/300], Temp: 0.4166, Energy: -84.590724-0.007694j
[2025-09-01 23:55:04] [Iter 318/2250] R1[167/300], Temp: 0.4115, Energy: -84.590436-0.005846j
[2025-09-01 23:55:45] [Iter 319/2250] R1[168/300], Temp: 0.4063, Energy: -84.645626-0.001003j
[2025-09-01 23:56:25] [Iter 320/2250] R1[169/300], Temp: 0.4012, Energy: -84.669851-0.006382j
[2025-09-01 23:57:06] [Iter 321/2250] R1[170/300], Temp: 0.3960, Energy: -84.709196+0.006996j
[2025-09-01 23:57:46] [Iter 322/2250] R1[171/300], Temp: 0.3909, Energy: -84.672886+0.007733j
[2025-09-01 23:58:27] [Iter 323/2250] R1[172/300], Temp: 0.3858, Energy: -84.733710-0.004869j
[2025-09-01 23:59:08] [Iter 324/2250] R1[173/300], Temp: 0.3807, Energy: -84.715320-0.007649j
[2025-09-01 23:59:48] [Iter 325/2250] R1[174/300], Temp: 0.3757, Energy: -84.675481+0.005778j
[2025-09-02 00:00:29] [Iter 326/2250] R1[175/300], Temp: 0.3706, Energy: -84.663681-0.008264j
[2025-09-02 00:01:10] [Iter 327/2250] R1[176/300], Temp: 0.3655, Energy: -84.657042-0.013825j
[2025-09-02 00:01:50] [Iter 328/2250] R1[177/300], Temp: 0.3605, Energy: -84.628620+0.010859j
[2025-09-02 00:02:30] [Iter 329/2250] R1[178/300], Temp: 0.3555, Energy: -84.648341-0.002148j
[2025-09-02 00:03:11] [Iter 330/2250] R1[179/300], Temp: 0.3505, Energy: -84.662501+0.003271j
[2025-09-02 00:03:51] [Iter 331/2250] R1[180/300], Temp: 0.3455, Energy: -84.583808-0.009007j
[2025-09-02 00:04:32] [Iter 332/2250] R1[181/300], Temp: 0.3405, Energy: -84.616850+0.002382j
[2025-09-02 00:05:13] [Iter 333/2250] R1[182/300], Temp: 0.3356, Energy: -84.607606+0.004472j
[2025-09-02 00:05:53] [Iter 334/2250] R1[183/300], Temp: 0.3306, Energy: -84.701635+0.001104j
[2025-09-02 00:06:34] [Iter 335/2250] R1[184/300], Temp: 0.3257, Energy: -84.675443+0.002707j
[2025-09-02 00:07:14] [Iter 336/2250] R1[185/300], Temp: 0.3208, Energy: -84.766526+0.001382j
[2025-09-02 00:07:55] [Iter 337/2250] R1[186/300], Temp: 0.3159, Energy: -84.639537-0.011273j
[2025-09-02 00:08:35] [Iter 338/2250] R1[187/300], Temp: 0.3111, Energy: -84.659252-0.001392j
[2025-09-02 00:09:16] [Iter 339/2250] R1[188/300], Temp: 0.3062, Energy: -84.607718+0.003882j
[2025-09-02 00:09:57] [Iter 340/2250] R1[189/300], Temp: 0.3014, Energy: -84.685486+0.001525j
[2025-09-02 00:10:37] [Iter 341/2250] R1[190/300], Temp: 0.2966, Energy: -84.709881-0.000911j
[2025-09-02 00:11:18] [Iter 342/2250] R1[191/300], Temp: 0.2919, Energy: -84.766588-0.001008j
[2025-09-02 00:11:59] [Iter 343/2250] R1[192/300], Temp: 0.2871, Energy: -84.813700-0.002880j
[2025-09-02 00:12:39] [Iter 344/2250] R1[193/300], Temp: 0.2824, Energy: -84.826750+0.007462j
[2025-09-02 00:13:20] [Iter 345/2250] R1[194/300], Temp: 0.2777, Energy: -84.806474+0.003088j
[2025-09-02 00:14:00] [Iter 346/2250] R1[195/300], Temp: 0.2730, Energy: -84.751046+0.001189j
[2025-09-02 00:14:41] [Iter 347/2250] R1[196/300], Temp: 0.2684, Energy: -84.672450+0.001667j
[2025-09-02 00:15:22] [Iter 348/2250] R1[197/300], Temp: 0.2637, Energy: -84.733685+0.008906j
[2025-09-02 00:16:02] [Iter 349/2250] R1[198/300], Temp: 0.2591, Energy: -84.712895+0.008571j
[2025-09-02 00:16:43] [Iter 350/2250] R1[199/300], Temp: 0.2545, Energy: -84.618604-0.001835j
[2025-09-02 00:17:24] [Iter 351/2250] R1[200/300], Temp: 0.2500, Energy: -84.623120+0.000635j
[2025-09-02 00:18:04] [Iter 352/2250] R1[201/300], Temp: 0.2455, Energy: -84.682654+0.000919j
[2025-09-02 00:18:44] [Iter 353/2250] R1[202/300], Temp: 0.2410, Energy: -84.693191-0.004903j
[2025-09-02 00:19:25] [Iter 354/2250] R1[203/300], Temp: 0.2365, Energy: -84.752030-0.000894j
[2025-09-02 00:20:06] [Iter 355/2250] R1[204/300], Temp: 0.2321, Energy: -84.717349+0.001566j
[2025-09-02 00:20:47] [Iter 356/2250] R1[205/300], Temp: 0.2277, Energy: -84.761464-0.005765j
[2025-09-02 00:21:27] [Iter 357/2250] R1[206/300], Temp: 0.2233, Energy: -84.721521+0.001566j
[2025-09-02 00:22:08] [Iter 358/2250] R1[207/300], Temp: 0.2190, Energy: -84.770069+0.000990j
[2025-09-02 00:22:48] [Iter 359/2250] R1[208/300], Temp: 0.2146, Energy: -84.713661-0.007750j
[2025-09-02 00:23:29] [Iter 360/2250] R1[209/300], Temp: 0.2104, Energy: -84.655320-0.002076j
[2025-09-02 00:24:10] [Iter 361/2250] R1[210/300], Temp: 0.2061, Energy: -84.734237-0.000747j
[2025-09-02 00:24:50] [Iter 362/2250] R1[211/300], Temp: 0.2019, Energy: -84.708011+0.001801j
[2025-09-02 00:25:31] [Iter 363/2250] R1[212/300], Temp: 0.1977, Energy: -84.739410-0.008485j
[2025-09-02 00:26:11] [Iter 364/2250] R1[213/300], Temp: 0.1935, Energy: -84.658763-0.004578j
[2025-09-02 00:26:52] [Iter 365/2250] R1[214/300], Temp: 0.1894, Energy: -84.745142+0.004294j
[2025-09-02 00:27:32] [Iter 366/2250] R1[215/300], Temp: 0.1853, Energy: -84.708270+0.000459j
[2025-09-02 00:28:14] [Iter 367/2250] R1[216/300], Temp: 0.1813, Energy: -84.689662+0.001126j
[2025-09-02 00:28:54] [Iter 368/2250] R1[217/300], Temp: 0.1773, Energy: -84.762010+0.004250j
[2025-09-02 00:29:35] [Iter 369/2250] R1[218/300], Temp: 0.1733, Energy: -84.674623+0.007236j
[2025-09-02 00:30:15] [Iter 370/2250] R1[219/300], Temp: 0.1693, Energy: -84.715359-0.005159j
[2025-09-02 00:30:56] [Iter 371/2250] R1[220/300], Temp: 0.1654, Energy: -84.805245-0.000791j
[2025-09-02 00:31:36] [Iter 372/2250] R1[221/300], Temp: 0.1616, Energy: -84.850299-0.003519j
[2025-09-02 00:32:17] [Iter 373/2250] R1[222/300], Temp: 0.1577, Energy: -84.736005-0.002418j
[2025-09-02 00:32:57] [Iter 374/2250] R1[223/300], Temp: 0.1539, Energy: -84.796814-0.003197j
[2025-09-02 00:33:38] [Iter 375/2250] R1[224/300], Temp: 0.1502, Energy: -84.809922+0.003272j
[2025-09-02 00:34:19] [Iter 376/2250] R1[225/300], Temp: 0.1464, Energy: -84.893602+0.002121j
[2025-09-02 00:34:59] [Iter 377/2250] R1[226/300], Temp: 0.1428, Energy: -84.908627+0.002848j
[2025-09-02 00:35:40] [Iter 378/2250] R1[227/300], Temp: 0.1391, Energy: -84.865524+0.002271j
[2025-09-02 00:36:21] [Iter 379/2250] R1[228/300], Temp: 0.1355, Energy: -84.786841+0.000783j
[2025-09-02 00:37:01] [Iter 380/2250] R1[229/300], Temp: 0.1320, Energy: -84.784013+0.001232j
[2025-09-02 00:37:42] [Iter 381/2250] R1[230/300], Temp: 0.1284, Energy: -84.823829-0.000939j
[2025-09-02 00:38:22] [Iter 382/2250] R1[231/300], Temp: 0.1249, Energy: -84.843681+0.003691j
[2025-09-02 00:39:03] [Iter 383/2250] R1[232/300], Temp: 0.1215, Energy: -84.819201+0.001889j
[2025-09-02 00:39:44] [Iter 384/2250] R1[233/300], Temp: 0.1181, Energy: -84.727373+0.000354j
[2025-09-02 00:40:24] [Iter 385/2250] R1[234/300], Temp: 0.1147, Energy: -84.799027+0.005471j
[2025-09-02 00:41:05] [Iter 386/2250] R1[235/300], Temp: 0.1114, Energy: -84.719477-0.004044j
[2025-09-02 00:41:45] [Iter 387/2250] R1[236/300], Temp: 0.1082, Energy: -84.724257-0.003608j
[2025-09-02 00:42:26] [Iter 388/2250] R1[237/300], Temp: 0.1049, Energy: -84.743873-0.000636j
[2025-09-02 00:43:07] [Iter 389/2250] R1[238/300], Temp: 0.1017, Energy: -84.796408-0.007800j
[2025-09-02 00:43:47] [Iter 390/2250] R1[239/300], Temp: 0.0986, Energy: -84.711470+0.007431j
[2025-09-02 00:44:28] [Iter 391/2250] R1[240/300], Temp: 0.0955, Energy: -84.708060-0.002584j
[2025-09-02 00:45:08] [Iter 392/2250] R1[241/300], Temp: 0.0924, Energy: -84.789360-0.005477j
[2025-09-02 00:45:49] [Iter 393/2250] R1[242/300], Temp: 0.0894, Energy: -84.831362+0.013303j
[2025-09-02 00:46:30] [Iter 394/2250] R1[243/300], Temp: 0.0865, Energy: -84.746292+0.002561j
[2025-09-02 00:47:11] [Iter 395/2250] R1[244/300], Temp: 0.0835, Energy: -84.771337-0.000788j
[2025-09-02 00:47:51] [Iter 396/2250] R1[245/300], Temp: 0.0807, Energy: -84.744077+0.001916j
[2025-09-02 00:48:31] [Iter 397/2250] R1[246/300], Temp: 0.0778, Energy: -84.790928-0.002376j
[2025-09-02 00:49:12] [Iter 398/2250] R1[247/300], Temp: 0.0751, Energy: -84.805383-0.001388j
[2025-09-02 00:49:53] [Iter 399/2250] R1[248/300], Temp: 0.0723, Energy: -84.723024+0.004074j
[2025-09-02 00:50:34] [Iter 400/2250] R1[249/300], Temp: 0.0696, Energy: -84.726426+0.004700j
[2025-09-02 00:51:14] [Iter 401/2250] R1[250/300], Temp: 0.0670, Energy: -84.766677+0.001566j
[2025-09-02 00:51:55] [Iter 402/2250] R1[251/300], Temp: 0.0644, Energy: -84.716084+0.002543j
[2025-09-02 00:52:35] [Iter 403/2250] R1[252/300], Temp: 0.0618, Energy: -84.818763-0.009431j
[2025-09-02 00:53:16] [Iter 404/2250] R1[253/300], Temp: 0.0593, Energy: -84.799560-0.000775j
[2025-09-02 00:53:57] [Iter 405/2250] R1[254/300], Temp: 0.0569, Energy: -84.770220+0.001589j
[2025-09-02 00:54:37] [Iter 406/2250] R1[255/300], Temp: 0.0545, Energy: -84.825997+0.001955j
[2025-09-02 00:55:18] [Iter 407/2250] R1[256/300], Temp: 0.0521, Energy: -84.847057-0.002497j
[2025-09-02 00:55:59] [Iter 408/2250] R1[257/300], Temp: 0.0498, Energy: -84.794049-0.004711j
[2025-09-02 00:56:39] [Iter 409/2250] R1[258/300], Temp: 0.0476, Energy: -84.867491-0.002475j
[2025-09-02 00:57:20] [Iter 410/2250] R1[259/300], Temp: 0.0454, Energy: -84.841034+0.002460j
[2025-09-02 00:58:01] [Iter 411/2250] R1[260/300], Temp: 0.0432, Energy: -84.841432-0.000549j
[2025-09-02 00:58:41] [Iter 412/2250] R1[261/300], Temp: 0.0411, Energy: -84.944505-0.000861j
[2025-09-02 00:59:21] [Iter 413/2250] R1[262/300], Temp: 0.0391, Energy: -84.864252-0.001942j
[2025-09-02 01:00:02] [Iter 414/2250] R1[263/300], Temp: 0.0371, Energy: -84.845748+0.000279j
[2025-09-02 01:00:43] [Iter 415/2250] R1[264/300], Temp: 0.0351, Energy: -84.908496-0.000735j
[2025-09-02 01:01:24] [Iter 416/2250] R1[265/300], Temp: 0.0332, Energy: -84.812302+0.010237j
[2025-09-02 01:02:04] [Iter 417/2250] R1[266/300], Temp: 0.0314, Energy: -84.816081+0.004569j
[2025-09-02 01:02:45] [Iter 418/2250] R1[267/300], Temp: 0.0296, Energy: -84.803816+0.005787j
[2025-09-02 01:03:26] [Iter 419/2250] R1[268/300], Temp: 0.0278, Energy: -84.865826-0.002238j
[2025-09-02 01:04:07] [Iter 420/2250] R1[269/300], Temp: 0.0261, Energy: -84.882763+0.003719j
[2025-09-02 01:04:47] [Iter 421/2250] R1[270/300], Temp: 0.0245, Energy: -84.859662-0.002745j
[2025-09-02 01:05:27] [Iter 422/2250] R1[271/300], Temp: 0.0229, Energy: -84.952411-0.001511j
[2025-09-02 01:06:08] [Iter 423/2250] R1[272/300], Temp: 0.0213, Energy: -84.982895+0.001764j
[2025-09-02 01:06:48] [Iter 424/2250] R1[273/300], Temp: 0.0199, Energy: -84.919798+0.001151j
[2025-09-02 01:07:29] [Iter 425/2250] R1[274/300], Temp: 0.0184, Energy: -84.959028-0.001120j
[2025-09-02 01:08:10] [Iter 426/2250] R1[275/300], Temp: 0.0170, Energy: -84.978447-0.000901j
[2025-09-02 01:08:50] [Iter 427/2250] R1[276/300], Temp: 0.0157, Energy: -84.896170+0.003418j
[2025-09-02 01:09:31] [Iter 428/2250] R1[277/300], Temp: 0.0144, Energy: -84.895278+0.003093j
[2025-09-02 01:10:11] [Iter 429/2250] R1[278/300], Temp: 0.0132, Energy: -84.828800+0.005610j
[2025-09-02 01:10:52] [Iter 430/2250] R1[279/300], Temp: 0.0120, Energy: -84.883941-0.000255j
[2025-09-02 01:11:33] [Iter 431/2250] R1[280/300], Temp: 0.0109, Energy: -84.876245-0.006830j
[2025-09-02 01:12:13] [Iter 432/2250] R1[281/300], Temp: 0.0099, Energy: -84.861817-0.000366j
[2025-09-02 01:12:54] [Iter 433/2250] R1[282/300], Temp: 0.0089, Energy: -84.813340+0.003162j
[2025-09-02 01:13:34] [Iter 434/2250] R1[283/300], Temp: 0.0079, Energy: -84.821921-0.001060j
[2025-09-02 01:14:15] [Iter 435/2250] R1[284/300], Temp: 0.0070, Energy: -84.798124+0.004013j
[2025-09-02 01:14:55] [Iter 436/2250] R1[285/300], Temp: 0.0062, Energy: -84.894480+0.001776j
[2025-09-02 01:15:36] [Iter 437/2250] R1[286/300], Temp: 0.0054, Energy: -84.848710+0.005699j
[2025-09-02 01:16:16] [Iter 438/2250] R1[287/300], Temp: 0.0046, Energy: -84.811494-0.002650j
[2025-09-02 01:16:57] [Iter 439/2250] R1[288/300], Temp: 0.0039, Energy: -84.831096+0.002360j
[2025-09-02 01:17:37] [Iter 440/2250] R1[289/300], Temp: 0.0033, Energy: -84.832053-0.003988j
[2025-09-02 01:18:18] [Iter 441/2250] R1[290/300], Temp: 0.0027, Energy: -84.803682+0.003079j
[2025-09-02 01:18:59] [Iter 442/2250] R1[291/300], Temp: 0.0022, Energy: -84.803080-0.005487j
[2025-09-02 01:19:39] [Iter 443/2250] R1[292/300], Temp: 0.0018, Energy: -84.800565-0.004018j
[2025-09-02 01:20:20] [Iter 444/2250] R1[293/300], Temp: 0.0013, Energy: -84.861093+0.001275j
[2025-09-02 01:21:01] [Iter 445/2250] R1[294/300], Temp: 0.0010, Energy: -84.892214-0.001755j
[2025-09-02 01:21:41] [Iter 446/2250] R1[295/300], Temp: 0.0007, Energy: -84.906965+0.005366j
[2025-09-02 01:22:22] [Iter 447/2250] R1[296/300], Temp: 0.0004, Energy: -85.015513+0.003664j
[2025-09-02 01:23:02] [Iter 448/2250] R1[297/300], Temp: 0.0002, Energy: -84.988213+0.000846j
[2025-09-02 01:23:43] [Iter 449/2250] R1[298/300], Temp: 0.0001, Energy: -84.891574-0.002144j
[2025-09-02 01:24:23] [Iter 450/2250] R1[299/300], Temp: 0.0000, Energy: -84.947338-0.000934j
[2025-09-02 01:24:23] RESTART #2 | Period: 600
[2025-09-02 01:25:04] [Iter 451/2250] R2[0/600], Temp: 1.0000, Energy: -84.963609+0.006723j
[2025-09-02 01:25:45] [Iter 452/2250] R2[1/600], Temp: 1.0000, Energy: -84.882616+0.004099j
[2025-09-02 01:26:25] [Iter 453/2250] R2[2/600], Temp: 1.0000, Energy: -84.918345+0.001224j
[2025-09-02 01:27:06] [Iter 454/2250] R2[3/600], Temp: 0.9999, Energy: -84.921680+0.003864j
[2025-09-02 01:27:46] [Iter 455/2250] R2[4/600], Temp: 0.9999, Energy: -84.910598+0.002889j
[2025-09-02 01:28:27] [Iter 456/2250] R2[5/600], Temp: 0.9998, Energy: -84.935326+0.002154j
[2025-09-02 01:29:07] [Iter 457/2250] R2[6/600], Temp: 0.9998, Energy: -84.856401+0.008457j
[2025-09-02 01:29:48] [Iter 458/2250] R2[7/600], Temp: 0.9997, Energy: -84.788887+0.003739j
[2025-09-02 01:30:29] [Iter 459/2250] R2[8/600], Temp: 0.9996, Energy: -84.804099+0.006858j
[2025-09-02 01:31:09] [Iter 460/2250] R2[9/600], Temp: 0.9994, Energy: -84.751855+0.008197j
[2025-09-02 01:31:50] [Iter 461/2250] R2[10/600], Temp: 0.9993, Energy: -84.813811-0.001562j
[2025-09-02 01:32:30] [Iter 462/2250] R2[11/600], Temp: 0.9992, Energy: -84.797529-0.011363j
[2025-09-02 01:33:10] [Iter 463/2250] R2[12/600], Temp: 0.9990, Energy: -84.806578-0.001078j
[2025-09-02 01:33:51] [Iter 464/2250] R2[13/600], Temp: 0.9988, Energy: -84.887723+0.001283j
[2025-09-02 01:34:32] [Iter 465/2250] R2[14/600], Temp: 0.9987, Energy: -84.838818-0.000227j
[2025-09-02 01:35:12] [Iter 466/2250] R2[15/600], Temp: 0.9985, Energy: -84.879487-0.003561j
[2025-09-02 01:35:53] [Iter 467/2250] R2[16/600], Temp: 0.9982, Energy: -84.875821+0.000323j
[2025-09-02 01:36:34] [Iter 468/2250] R2[17/600], Temp: 0.9980, Energy: -84.871950-0.000336j
[2025-09-02 01:37:14] [Iter 469/2250] R2[18/600], Temp: 0.9978, Energy: -84.889094+0.000092j
[2025-09-02 01:37:55] [Iter 470/2250] R2[19/600], Temp: 0.9975, Energy: -84.838918-0.000653j
[2025-09-02 01:38:35] [Iter 471/2250] R2[20/600], Temp: 0.9973, Energy: -84.872469+0.003081j
[2025-09-02 01:39:16] [Iter 472/2250] R2[21/600], Temp: 0.9970, Energy: -84.912257-0.004859j
[2025-09-02 01:39:57] [Iter 473/2250] R2[22/600], Temp: 0.9967, Energy: -84.834404+0.005893j
[2025-09-02 01:40:37] [Iter 474/2250] R2[23/600], Temp: 0.9964, Energy: -84.821970+0.001193j
[2025-09-02 01:41:18] [Iter 475/2250] R2[24/600], Temp: 0.9961, Energy: -84.954760+0.000701j
[2025-09-02 01:41:59] [Iter 476/2250] R2[25/600], Temp: 0.9957, Energy: -84.939826+0.006103j
[2025-09-02 01:42:39] [Iter 477/2250] R2[26/600], Temp: 0.9954, Energy: -84.915307-0.002961j
[2025-09-02 01:43:19] [Iter 478/2250] R2[27/600], Temp: 0.9950, Energy: -84.913790+0.001652j
[2025-09-02 01:43:59] [Iter 479/2250] R2[28/600], Temp: 0.9946, Energy: -84.846636+0.001934j
[2025-09-02 01:44:40] [Iter 480/2250] R2[29/600], Temp: 0.9942, Energy: -84.894962-0.004968j
[2025-09-02 01:45:20] [Iter 481/2250] R2[30/600], Temp: 0.9938, Energy: -84.871898-0.004371j
[2025-09-02 01:46:00] [Iter 482/2250] R2[31/600], Temp: 0.9934, Energy: -84.877984+0.002534j
[2025-09-02 01:46:41] [Iter 483/2250] R2[32/600], Temp: 0.9930, Energy: -84.923049-0.000185j
[2025-09-02 01:47:21] [Iter 484/2250] R2[33/600], Temp: 0.9926, Energy: -84.965926+0.002772j
[2025-09-02 01:48:01] [Iter 485/2250] R2[34/600], Temp: 0.9921, Energy: -84.985605-0.000132j
[2025-09-02 01:48:42] [Iter 486/2250] R2[35/600], Temp: 0.9916, Energy: -84.912372-0.002922j
[2025-09-02 01:49:22] [Iter 487/2250] R2[36/600], Temp: 0.9911, Energy: -84.883414-0.002313j
[2025-09-02 01:50:02] [Iter 488/2250] R2[37/600], Temp: 0.9906, Energy: -84.907235-0.004810j
[2025-09-02 01:50:42] [Iter 489/2250] R2[38/600], Temp: 0.9901, Energy: -84.946145-0.002171j
[2025-09-02 01:51:23] [Iter 490/2250] R2[39/600], Temp: 0.9896, Energy: -84.899019-0.002302j
[2025-09-02 01:52:03] [Iter 491/2250] R2[40/600], Temp: 0.9891, Energy: -84.854704-0.003153j
[2025-09-02 01:52:43] [Iter 492/2250] R2[41/600], Temp: 0.9885, Energy: -84.915181-0.000424j
[2025-09-02 01:53:24] [Iter 493/2250] R2[42/600], Temp: 0.9880, Energy: -84.966478-0.000732j
[2025-09-02 01:54:04] [Iter 494/2250] R2[43/600], Temp: 0.9874, Energy: -84.908381+0.001760j
[2025-09-02 01:54:44] [Iter 495/2250] R2[44/600], Temp: 0.9868, Energy: -84.984516+0.005079j
[2025-09-02 01:55:24] [Iter 496/2250] R2[45/600], Temp: 0.9862, Energy: -85.026657+0.002320j
[2025-09-02 01:56:05] [Iter 497/2250] R2[46/600], Temp: 0.9856, Energy: -84.992463+0.002003j
[2025-09-02 01:56:45] [Iter 498/2250] R2[47/600], Temp: 0.9849, Energy: -84.965845-0.003295j
[2025-09-02 01:57:25] [Iter 499/2250] R2[48/600], Temp: 0.9843, Energy: -84.945950+0.001576j
[2025-09-02 01:58:06] [Iter 500/2250] R2[49/600], Temp: 0.9836, Energy: -84.972528+0.001091j
[2025-09-02 01:58:06] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-09-02 01:58:46] [Iter 501/2250] R2[50/600], Temp: 0.9830, Energy: -84.913865+0.002012j
[2025-09-02 01:59:26] [Iter 502/2250] R2[51/600], Temp: 0.9823, Energy: -84.939256-0.000376j
[2025-09-02 02:00:06] [Iter 503/2250] R2[52/600], Temp: 0.9816, Energy: -84.895999+0.002104j
[2025-09-02 02:00:47] [Iter 504/2250] R2[53/600], Temp: 0.9809, Energy: -84.969096-0.000221j
[2025-09-02 02:01:27] [Iter 505/2250] R2[54/600], Temp: 0.9801, Energy: -85.084307+0.000426j
[2025-09-02 02:02:07] [Iter 506/2250] R2[55/600], Temp: 0.9794, Energy: -85.008275-0.005740j
[2025-09-02 02:02:48] [Iter 507/2250] R2[56/600], Temp: 0.9787, Energy: -85.001885-0.003242j
[2025-09-02 02:03:28] [Iter 508/2250] R2[57/600], Temp: 0.9779, Energy: -84.934486+0.000922j
[2025-09-02 02:04:08] [Iter 509/2250] R2[58/600], Temp: 0.9771, Energy: -84.934053+0.001313j
[2025-09-02 02:04:49] [Iter 510/2250] R2[59/600], Temp: 0.9763, Energy: -84.973302-0.001481j
[2025-09-02 02:05:29] [Iter 511/2250] R2[60/600], Temp: 0.9755, Energy: -84.928546-0.003968j
[2025-09-02 02:06:09] [Iter 512/2250] R2[61/600], Temp: 0.9747, Energy: -84.866253-0.003847j
[2025-09-02 02:06:49] [Iter 513/2250] R2[62/600], Temp: 0.9739, Energy: -84.903270+0.000879j
[2025-09-02 02:07:30] [Iter 514/2250] R2[63/600], Temp: 0.9730, Energy: -84.885198+0.001156j
[2025-09-02 02:08:10] [Iter 515/2250] R2[64/600], Temp: 0.9722, Energy: -84.923836-0.001910j
[2025-09-02 02:08:50] [Iter 516/2250] R2[65/600], Temp: 0.9713, Energy: -84.875805-0.005027j
[2025-09-02 02:09:31] [Iter 517/2250] R2[66/600], Temp: 0.9704, Energy: -84.876393-0.000924j
[2025-09-02 02:10:11] [Iter 518/2250] R2[67/600], Temp: 0.9695, Energy: -84.890230+0.004175j
[2025-09-02 02:10:51] [Iter 519/2250] R2[68/600], Temp: 0.9686, Energy: -84.839244+0.003072j
[2025-09-02 02:11:31] [Iter 520/2250] R2[69/600], Temp: 0.9677, Energy: -84.914529-0.001294j
[2025-09-02 02:12:12] [Iter 521/2250] R2[70/600], Temp: 0.9668, Energy: -84.943733+0.002280j
[2025-09-02 02:12:52] [Iter 522/2250] R2[71/600], Temp: 0.9658, Energy: -85.038121+0.001136j
[2025-09-02 02:13:32] [Iter 523/2250] R2[72/600], Temp: 0.9649, Energy: -84.993888+0.002580j
[2025-09-02 02:14:13] [Iter 524/2250] R2[73/600], Temp: 0.9639, Energy: -84.951691-0.000362j
[2025-09-02 02:14:53] [Iter 525/2250] R2[74/600], Temp: 0.9629, Energy: -84.988896-0.006762j
[2025-09-02 02:15:33] [Iter 526/2250] R2[75/600], Temp: 0.9619, Energy: -84.969120+0.003794j
[2025-09-02 02:16:14] [Iter 527/2250] R2[76/600], Temp: 0.9609, Energy: -84.928510+0.000515j
[2025-09-02 02:16:54] [Iter 528/2250] R2[77/600], Temp: 0.9599, Energy: -84.989501+0.001301j
[2025-09-02 02:17:34] [Iter 529/2250] R2[78/600], Temp: 0.9589, Energy: -84.957518-0.000092j
[2025-09-02 02:18:14] [Iter 530/2250] R2[79/600], Temp: 0.9578, Energy: -84.871291+0.000642j
[2025-09-02 02:18:55] [Iter 531/2250] R2[80/600], Temp: 0.9568, Energy: -84.897927+0.001019j
[2025-09-02 02:19:35] [Iter 532/2250] R2[81/600], Temp: 0.9557, Energy: -84.926485+0.003060j
[2025-09-02 02:20:15] [Iter 533/2250] R2[82/600], Temp: 0.9546, Energy: -84.982363-0.000296j
[2025-09-02 02:20:56] [Iter 534/2250] R2[83/600], Temp: 0.9535, Energy: -84.963553+0.000899j
[2025-09-02 02:21:36] [Iter 535/2250] R2[84/600], Temp: 0.9524, Energy: -85.004293-0.001866j
[2025-09-02 02:22:16] [Iter 536/2250] R2[85/600], Temp: 0.9513, Energy: -84.983080+0.001388j
[2025-09-02 02:22:57] [Iter 537/2250] R2[86/600], Temp: 0.9502, Energy: -85.009001-0.001734j
[2025-09-02 02:23:37] [Iter 538/2250] R2[87/600], Temp: 0.9490, Energy: -84.974288-0.001897j
[2025-09-02 02:24:17] [Iter 539/2250] R2[88/600], Temp: 0.9479, Energy: -84.971400+0.005355j
[2025-09-02 02:24:57] [Iter 540/2250] R2[89/600], Temp: 0.9467, Energy: -85.004738+0.004164j
[2025-09-02 02:25:38] [Iter 541/2250] R2[90/600], Temp: 0.9455, Energy: -85.001454-0.001334j
[2025-09-02 02:26:18] [Iter 542/2250] R2[91/600], Temp: 0.9443, Energy: -85.005807-0.000829j
[2025-09-02 02:26:58] [Iter 543/2250] R2[92/600], Temp: 0.9431, Energy: -85.035978-0.003664j
[2025-09-02 02:27:39] [Iter 544/2250] R2[93/600], Temp: 0.9419, Energy: -85.038162-0.000749j
[2025-09-02 02:28:19] [Iter 545/2250] R2[94/600], Temp: 0.9407, Energy: -84.980990+0.000789j
[2025-09-02 02:28:59] [Iter 546/2250] R2[95/600], Temp: 0.9394, Energy: -84.961215-0.003640j
[2025-09-02 02:29:39] [Iter 547/2250] R2[96/600], Temp: 0.9382, Energy: -84.975676-0.001242j
[2025-09-02 02:30:20] [Iter 548/2250] R2[97/600], Temp: 0.9369, Energy: -84.863076-0.002824j
[2025-09-02 02:31:00] [Iter 549/2250] R2[98/600], Temp: 0.9356, Energy: -84.826807+0.002147j
[2025-09-02 02:31:40] [Iter 550/2250] R2[99/600], Temp: 0.9343, Energy: -84.868077-0.002722j
[2025-09-02 02:32:21] [Iter 551/2250] R2[100/600], Temp: 0.9330, Energy: -84.867991+0.004014j
[2025-09-02 02:33:01] [Iter 552/2250] R2[101/600], Temp: 0.9317, Energy: -84.967752-0.001000j
[2025-09-02 02:33:41] [Iter 553/2250] R2[102/600], Temp: 0.9304, Energy: -84.905832+0.000899j
[2025-09-02 02:34:22] [Iter 554/2250] R2[103/600], Temp: 0.9290, Energy: -84.943622-0.002134j
[2025-09-02 02:35:02] [Iter 555/2250] R2[104/600], Temp: 0.9277, Energy: -84.969981+0.001666j
[2025-09-02 02:35:42] [Iter 556/2250] R2[105/600], Temp: 0.9263, Energy: -84.986706-0.002170j
[2025-09-02 02:36:22] [Iter 557/2250] R2[106/600], Temp: 0.9249, Energy: -84.999177-0.002272j
[2025-09-02 02:37:03] [Iter 558/2250] R2[107/600], Temp: 0.9236, Energy: -84.840880-0.001663j
[2025-09-02 02:37:43] [Iter 559/2250] R2[108/600], Temp: 0.9222, Energy: -84.965071-0.002622j
[2025-09-02 02:38:23] [Iter 560/2250] R2[109/600], Temp: 0.9208, Energy: -85.030357-0.001752j
[2025-09-02 02:39:04] [Iter 561/2250] R2[110/600], Temp: 0.9193, Energy: -84.953845+0.004054j
[2025-09-02 02:39:44] [Iter 562/2250] R2[111/600], Temp: 0.9179, Energy: -85.011233-0.000632j
[2025-09-02 02:40:24] [Iter 563/2250] R2[112/600], Temp: 0.9165, Energy: -84.971941+0.001838j
[2025-09-02 02:41:04] [Iter 564/2250] R2[113/600], Temp: 0.9150, Energy: -84.950050-0.001433j
[2025-09-02 02:41:45] [Iter 565/2250] R2[114/600], Temp: 0.9135, Energy: -84.945088+0.001163j
[2025-09-02 02:42:25] [Iter 566/2250] R2[115/600], Temp: 0.9121, Energy: -84.966433-0.003928j
[2025-09-02 02:43:05] [Iter 567/2250] R2[116/600], Temp: 0.9106, Energy: -85.003148-0.000392j
[2025-09-02 02:43:46] [Iter 568/2250] R2[117/600], Temp: 0.9091, Energy: -85.033530+0.003687j
[2025-09-02 02:44:26] [Iter 569/2250] R2[118/600], Temp: 0.9076, Energy: -85.009544+0.002344j
[2025-09-02 02:45:06] [Iter 570/2250] R2[119/600], Temp: 0.9060, Energy: -85.059068+0.001180j
[2025-09-02 02:45:47] [Iter 571/2250] R2[120/600], Temp: 0.9045, Energy: -85.033547-0.000843j
[2025-09-02 02:46:27] [Iter 572/2250] R2[121/600], Temp: 0.9030, Energy: -85.015554+0.006690j
[2025-09-02 02:47:07] [Iter 573/2250] R2[122/600], Temp: 0.9014, Energy: -85.067893+0.004811j
[2025-09-02 02:47:48] [Iter 574/2250] R2[123/600], Temp: 0.8998, Energy: -84.971116-0.002001j
[2025-09-02 02:48:28] [Iter 575/2250] R2[124/600], Temp: 0.8983, Energy: -85.061885-0.001753j
[2025-09-02 02:49:08] [Iter 576/2250] R2[125/600], Temp: 0.8967, Energy: -85.039772+0.001048j
[2025-09-02 02:49:48] [Iter 577/2250] R2[126/600], Temp: 0.8951, Energy: -85.082693-0.002590j
[2025-09-02 02:50:29] [Iter 578/2250] R2[127/600], Temp: 0.8935, Energy: -84.962642+0.000555j
[2025-09-02 02:51:09] [Iter 579/2250] R2[128/600], Temp: 0.8918, Energy: -84.920237-0.003147j
[2025-09-02 02:51:49] [Iter 580/2250] R2[129/600], Temp: 0.8902, Energy: -85.022351+0.005598j
[2025-09-02 02:52:30] [Iter 581/2250] R2[130/600], Temp: 0.8886, Energy: -85.036089+0.001753j
[2025-09-02 02:53:10] [Iter 582/2250] R2[131/600], Temp: 0.8869, Energy: -84.957697+0.001068j
[2025-09-02 02:53:50] [Iter 583/2250] R2[132/600], Temp: 0.8853, Energy: -84.910596-0.003200j
[2025-09-02 02:54:31] [Iter 584/2250] R2[133/600], Temp: 0.8836, Energy: -84.873039-0.001145j
[2025-09-02 02:55:11] [Iter 585/2250] R2[134/600], Temp: 0.8819, Energy: -84.891116+0.002126j
[2025-09-02 02:55:51] [Iter 586/2250] R2[135/600], Temp: 0.8802, Energy: -84.886874+0.004039j
[2025-09-02 02:56:31] [Iter 587/2250] R2[136/600], Temp: 0.8785, Energy: -84.953176-0.001414j
[2025-09-02 02:57:12] [Iter 588/2250] R2[137/600], Temp: 0.8768, Energy: -84.970590-0.000775j
[2025-09-02 02:57:52] [Iter 589/2250] R2[138/600], Temp: 0.8751, Energy: -85.004209+0.003716j
[2025-09-02 02:58:32] [Iter 590/2250] R2[139/600], Temp: 0.8733, Energy: -85.029854-0.000034j
[2025-09-02 02:59:13] [Iter 591/2250] R2[140/600], Temp: 0.8716, Energy: -85.062410-0.001101j
[2025-09-02 02:59:53] [Iter 592/2250] R2[141/600], Temp: 0.8698, Energy: -85.006992-0.001136j
[2025-09-02 03:00:33] [Iter 593/2250] R2[142/600], Temp: 0.8680, Energy: -85.031705-0.001357j
[2025-09-02 03:01:14] [Iter 594/2250] R2[143/600], Temp: 0.8663, Energy: -84.955238-0.003382j
[2025-09-02 03:01:54] [Iter 595/2250] R2[144/600], Temp: 0.8645, Energy: -84.929833+0.003958j
[2025-09-02 03:02:34] [Iter 596/2250] R2[145/600], Temp: 0.8627, Energy: -84.965965+0.002441j
[2025-09-02 03:03:14] [Iter 597/2250] R2[146/600], Temp: 0.8609, Energy: -84.927764-0.003868j
[2025-09-02 03:03:55] [Iter 598/2250] R2[147/600], Temp: 0.8591, Energy: -84.922506-0.006289j
[2025-09-02 03:04:35] [Iter 599/2250] R2[148/600], Temp: 0.8572, Energy: -84.909221+0.002191j
[2025-09-02 03:05:15] [Iter 600/2250] R2[149/600], Temp: 0.8554, Energy: -84.969356-0.001011j
[2025-09-02 03:05:56] [Iter 601/2250] R2[150/600], Temp: 0.8536, Energy: -84.952443+0.000965j
[2025-09-02 03:06:36] [Iter 602/2250] R2[151/600], Temp: 0.8517, Energy: -84.940920-0.001434j
[2025-09-02 03:07:16] [Iter 603/2250] R2[152/600], Temp: 0.8498, Energy: -85.027542-0.001844j
[2025-09-02 03:07:57] [Iter 604/2250] R2[153/600], Temp: 0.8480, Energy: -84.948170-0.000487j
[2025-09-02 03:08:37] [Iter 605/2250] R2[154/600], Temp: 0.8461, Energy: -84.992312+0.001136j
[2025-09-02 03:09:17] [Iter 606/2250] R2[155/600], Temp: 0.8442, Energy: -85.035281-0.002659j
[2025-09-02 03:09:57] [Iter 607/2250] R2[156/600], Temp: 0.8423, Energy: -84.969878+0.004523j
[2025-09-02 03:10:38] [Iter 608/2250] R2[157/600], Temp: 0.8404, Energy: -84.951387+0.006496j
[2025-09-02 03:11:18] [Iter 609/2250] R2[158/600], Temp: 0.8384, Energy: -84.953413-0.001955j
[2025-09-02 03:11:58] [Iter 610/2250] R2[159/600], Temp: 0.8365, Energy: -84.974765-0.003178j
[2025-09-02 03:12:39] [Iter 611/2250] R2[160/600], Temp: 0.8346, Energy: -84.905934+0.000098j
[2025-09-02 03:13:19] [Iter 612/2250] R2[161/600], Temp: 0.8326, Energy: -84.968653-0.000422j
[2025-09-02 03:13:59] [Iter 613/2250] R2[162/600], Temp: 0.8307, Energy: -84.970692-0.001988j
[2025-09-02 03:14:39] [Iter 614/2250] R2[163/600], Temp: 0.8287, Energy: -85.013753+0.001105j
[2025-09-02 03:15:20] [Iter 615/2250] R2[164/600], Temp: 0.8267, Energy: -84.976417+0.000154j
[2025-09-02 03:16:00] [Iter 616/2250] R2[165/600], Temp: 0.8247, Energy: -84.878944+0.001003j
[2025-09-02 03:16:40] [Iter 617/2250] R2[166/600], Temp: 0.8227, Energy: -84.977920+0.001368j
[2025-09-02 03:17:20] [Iter 618/2250] R2[167/600], Temp: 0.8207, Energy: -84.955474+0.000515j
[2025-09-02 03:18:01] [Iter 619/2250] R2[168/600], Temp: 0.8187, Energy: -85.074306+0.000562j
[2025-09-02 03:18:41] [Iter 620/2250] R2[169/600], Temp: 0.8167, Energy: -84.978345+0.000645j
[2025-09-02 03:19:21] [Iter 621/2250] R2[170/600], Temp: 0.8147, Energy: -85.053557-0.005408j
[2025-09-02 03:20:02] [Iter 622/2250] R2[171/600], Temp: 0.8126, Energy: -84.995764+0.003297j
[2025-09-02 03:20:42] [Iter 623/2250] R2[172/600], Temp: 0.8106, Energy: -85.039760+0.001505j
[2025-09-02 03:21:22] [Iter 624/2250] R2[173/600], Temp: 0.8085, Energy: -84.997515+0.002595j
[2025-09-02 03:22:02] [Iter 625/2250] R2[174/600], Temp: 0.8065, Energy: -85.020790+0.002067j
[2025-09-02 03:22:43] [Iter 626/2250] R2[175/600], Temp: 0.8044, Energy: -84.971915+0.000224j
[2025-09-02 03:23:23] [Iter 627/2250] R2[176/600], Temp: 0.8023, Energy: -85.046406+0.002411j
[2025-09-02 03:24:03] [Iter 628/2250] R2[177/600], Temp: 0.8002, Energy: -85.017522-0.002117j
[2025-09-02 03:24:44] [Iter 629/2250] R2[178/600], Temp: 0.7981, Energy: -85.007014-0.000965j
[2025-09-02 03:25:24] [Iter 630/2250] R2[179/600], Temp: 0.7960, Energy: -85.044981+0.000299j
[2025-09-02 03:26:04] [Iter 631/2250] R2[180/600], Temp: 0.7939, Energy: -84.946505-0.000578j
[2025-09-02 03:26:44] [Iter 632/2250] R2[181/600], Temp: 0.7918, Energy: -84.989615-0.001048j
[2025-09-02 03:27:25] [Iter 633/2250] R2[182/600], Temp: 0.7896, Energy: -84.953770-0.001413j
[2025-09-02 03:28:05] [Iter 634/2250] R2[183/600], Temp: 0.7875, Energy: -84.997239+0.000116j
[2025-09-02 03:28:45] [Iter 635/2250] R2[184/600], Temp: 0.7854, Energy: -84.884470+0.000171j
[2025-09-02 03:29:26] [Iter 636/2250] R2[185/600], Temp: 0.7832, Energy: -84.971858-0.002181j
[2025-09-02 03:30:06] [Iter 637/2250] R2[186/600], Temp: 0.7810, Energy: -84.926336+0.000938j
[2025-09-02 03:30:46] [Iter 638/2250] R2[187/600], Temp: 0.7789, Energy: -84.942310+0.000811j
[2025-09-02 03:31:27] [Iter 639/2250] R2[188/600], Temp: 0.7767, Energy: -84.935002-0.000401j
[2025-09-02 03:32:07] [Iter 640/2250] R2[189/600], Temp: 0.7745, Energy: -84.942951+0.003241j
[2025-09-02 03:32:47] [Iter 641/2250] R2[190/600], Temp: 0.7723, Energy: -85.002854+0.006144j
[2025-09-02 03:33:27] [Iter 642/2250] R2[191/600], Temp: 0.7701, Energy: -85.035529-0.003922j
[2025-09-02 03:34:08] [Iter 643/2250] R2[192/600], Temp: 0.7679, Energy: -85.004196+0.000411j
[2025-09-02 03:34:48] [Iter 644/2250] R2[193/600], Temp: 0.7657, Energy: -84.945309-0.002093j
[2025-09-02 03:35:28] [Iter 645/2250] R2[194/600], Temp: 0.7635, Energy: -84.955770-0.004087j
[2025-09-02 03:36:09] [Iter 646/2250] R2[195/600], Temp: 0.7612, Energy: -84.976670+0.002095j
[2025-09-02 03:36:49] [Iter 647/2250] R2[196/600], Temp: 0.7590, Energy: -84.921661-0.000764j
[2025-09-02 03:37:29] [Iter 648/2250] R2[197/600], Temp: 0.7568, Energy: -84.934212-0.002846j
[2025-09-02 03:38:10] [Iter 649/2250] R2[198/600], Temp: 0.7545, Energy: -84.918314-0.001056j
[2025-09-02 03:38:50] [Iter 650/2250] R2[199/600], Temp: 0.7523, Energy: -84.935927+0.000083j
[2025-09-02 03:39:30] [Iter 651/2250] R2[200/600], Temp: 0.7500, Energy: -84.908518-0.000374j
[2025-09-02 03:40:10] [Iter 652/2250] R2[201/600], Temp: 0.7477, Energy: -84.925271-0.004519j
[2025-09-02 03:40:51] [Iter 653/2250] R2[202/600], Temp: 0.7455, Energy: -84.980016+0.000697j
[2025-09-02 03:41:31] [Iter 654/2250] R2[203/600], Temp: 0.7432, Energy: -84.966981-0.002734j
[2025-09-02 03:42:11] [Iter 655/2250] R2[204/600], Temp: 0.7409, Energy: -84.959383+0.001002j
[2025-09-02 03:42:52] [Iter 656/2250] R2[205/600], Temp: 0.7386, Energy: -84.949732-0.000002j
[2025-09-02 03:43:32] [Iter 657/2250] R2[206/600], Temp: 0.7363, Energy: -84.960362+0.000920j
[2025-09-02 03:44:12] [Iter 658/2250] R2[207/600], Temp: 0.7340, Energy: -84.973111+0.002141j
[2025-09-02 03:44:52] [Iter 659/2250] R2[208/600], Temp: 0.7316, Energy: -85.043268-0.000993j
[2025-09-02 03:45:33] [Iter 660/2250] R2[209/600], Temp: 0.7293, Energy: -84.966075+0.002809j
[2025-09-02 03:46:13] [Iter 661/2250] R2[210/600], Temp: 0.7270, Energy: -85.078068+0.003567j
[2025-09-02 03:46:53] [Iter 662/2250] R2[211/600], Temp: 0.7247, Energy: -85.026557+0.001760j
[2025-09-02 03:47:33] [Iter 663/2250] R2[212/600], Temp: 0.7223, Energy: -84.933913-0.000991j
[2025-09-02 03:48:14] [Iter 664/2250] R2[213/600], Temp: 0.7200, Energy: -84.943914+0.000167j
[2025-09-02 03:48:54] [Iter 665/2250] R2[214/600], Temp: 0.7176, Energy: -84.895204+0.000231j
[2025-09-02 03:49:34] [Iter 666/2250] R2[215/600], Temp: 0.7153, Energy: -84.886617+0.000237j
[2025-09-02 03:50:15] [Iter 667/2250] R2[216/600], Temp: 0.7129, Energy: -84.905131+0.000226j
[2025-09-02 03:50:55] [Iter 668/2250] R2[217/600], Temp: 0.7105, Energy: -84.991617+0.003513j
[2025-09-02 03:51:35] [Iter 669/2250] R2[218/600], Temp: 0.7081, Energy: -85.022574+0.001209j
[2025-09-02 03:52:15] [Iter 670/2250] R2[219/600], Temp: 0.7058, Energy: -84.915827+0.003624j
[2025-09-02 03:52:56] [Iter 671/2250] R2[220/600], Temp: 0.7034, Energy: -84.920152+0.003244j
[2025-09-02 03:53:36] [Iter 672/2250] R2[221/600], Temp: 0.7010, Energy: -84.813738-0.006425j
[2025-09-02 03:54:16] [Iter 673/2250] R2[222/600], Temp: 0.6986, Energy: -84.864695+0.002262j
[2025-09-02 03:54:57] [Iter 674/2250] R2[223/600], Temp: 0.6962, Energy: -84.878502+0.005691j
[2025-09-02 03:55:37] [Iter 675/2250] R2[224/600], Temp: 0.6938, Energy: -84.911075+0.002786j
[2025-09-02 03:56:17] [Iter 676/2250] R2[225/600], Temp: 0.6913, Energy: -84.877338+0.001683j
[2025-09-02 03:56:57] [Iter 677/2250] R2[226/600], Temp: 0.6889, Energy: -84.900290-0.001658j
[2025-09-02 03:57:38] [Iter 678/2250] R2[227/600], Temp: 0.6865, Energy: -84.900931+0.000220j
[2025-09-02 03:58:18] [Iter 679/2250] R2[228/600], Temp: 0.6841, Energy: -84.920163-0.001126j
[2025-09-02 03:58:58] [Iter 680/2250] R2[229/600], Temp: 0.6816, Energy: -84.907978+0.004721j
[2025-09-02 03:59:39] [Iter 681/2250] R2[230/600], Temp: 0.6792, Energy: -84.927240+0.000525j
[2025-09-02 04:00:19] [Iter 682/2250] R2[231/600], Temp: 0.6767, Energy: -84.919913+0.000357j
[2025-09-02 04:00:59] [Iter 683/2250] R2[232/600], Temp: 0.6743, Energy: -84.966921-0.003558j
[2025-09-02 04:01:40] [Iter 684/2250] R2[233/600], Temp: 0.6718, Energy: -84.885714+0.000232j
[2025-09-02 04:02:20] [Iter 685/2250] R2[234/600], Temp: 0.6694, Energy: -84.925047+0.004616j
[2025-09-02 04:03:00] [Iter 686/2250] R2[235/600], Temp: 0.6669, Energy: -84.842127+0.001149j
[2025-09-02 04:03:40] [Iter 687/2250] R2[236/600], Temp: 0.6644, Energy: -84.902117+0.000731j
[2025-09-02 04:04:21] [Iter 688/2250] R2[237/600], Temp: 0.6620, Energy: -84.998986-0.004784j
[2025-09-02 04:05:01] [Iter 689/2250] R2[238/600], Temp: 0.6595, Energy: -84.968726+0.005369j
[2025-09-02 04:05:41] [Iter 690/2250] R2[239/600], Temp: 0.6570, Energy: -84.953389+0.000813j
[2025-09-02 04:06:22] [Iter 691/2250] R2[240/600], Temp: 0.6545, Energy: -84.893458-0.001254j
[2025-09-02 04:07:02] [Iter 692/2250] R2[241/600], Temp: 0.6520, Energy: -84.948671-0.000958j
[2025-09-02 04:07:42] [Iter 693/2250] R2[242/600], Temp: 0.6495, Energy: -84.948136-0.003484j
[2025-09-02 04:08:23] [Iter 694/2250] R2[243/600], Temp: 0.6470, Energy: -84.950187+0.001625j
[2025-09-02 04:09:03] [Iter 695/2250] R2[244/600], Temp: 0.6445, Energy: -84.897408-0.001056j
[2025-09-02 04:09:43] [Iter 696/2250] R2[245/600], Temp: 0.6420, Energy: -84.906159-0.004977j
[2025-09-02 04:10:23] [Iter 697/2250] R2[246/600], Temp: 0.6395, Energy: -84.939018-0.003510j
[2025-09-02 04:11:04] [Iter 698/2250] R2[247/600], Temp: 0.6370, Energy: -85.013869+0.006888j
[2025-09-02 04:11:44] [Iter 699/2250] R2[248/600], Temp: 0.6345, Energy: -85.024363-0.002022j
[2025-09-02 04:12:24] [Iter 700/2250] R2[249/600], Temp: 0.6319, Energy: -84.988142-0.001754j
[2025-09-02 04:13:05] [Iter 701/2250] R2[250/600], Temp: 0.6294, Energy: -85.062374-0.001635j
[2025-09-02 04:13:45] [Iter 702/2250] R2[251/600], Temp: 0.6269, Energy: -85.052117-0.004905j
[2025-09-02 04:14:25] [Iter 703/2250] R2[252/600], Temp: 0.6243, Energy: -84.963501+0.004713j
[2025-09-02 04:15:05] [Iter 704/2250] R2[253/600], Temp: 0.6218, Energy: -84.999896+0.000956j
[2025-09-02 04:15:46] [Iter 705/2250] R2[254/600], Temp: 0.6193, Energy: -85.078520+0.000545j
[2025-09-02 04:16:26] [Iter 706/2250] R2[255/600], Temp: 0.6167, Energy: -84.996056-0.003213j
[2025-09-02 04:17:06] [Iter 707/2250] R2[256/600], Temp: 0.6142, Energy: -85.036814+0.001026j
[2025-09-02 04:17:47] [Iter 708/2250] R2[257/600], Temp: 0.6116, Energy: -84.976834-0.002639j
[2025-09-02 04:18:27] [Iter 709/2250] R2[258/600], Temp: 0.6091, Energy: -84.890282-0.003572j
[2025-09-02 04:19:07] [Iter 710/2250] R2[259/600], Temp: 0.6065, Energy: -84.943806+0.004996j
[2025-09-02 04:19:47] [Iter 711/2250] R2[260/600], Temp: 0.6040, Energy: -84.948771-0.003156j
[2025-09-02 04:20:28] [Iter 712/2250] R2[261/600], Temp: 0.6014, Energy: -84.869335-0.002141j
[2025-09-02 04:21:08] [Iter 713/2250] R2[262/600], Temp: 0.5988, Energy: -84.898959+0.000728j
[2025-09-02 04:21:48] [Iter 714/2250] R2[263/600], Temp: 0.5963, Energy: -84.933008+0.001256j
[2025-09-02 04:22:29] [Iter 715/2250] R2[264/600], Temp: 0.5937, Energy: -84.989700+0.002657j
[2025-09-02 04:23:09] [Iter 716/2250] R2[265/600], Temp: 0.5911, Energy: -85.006878+0.000295j
[2025-09-02 04:23:49] [Iter 717/2250] R2[266/600], Temp: 0.5885, Energy: -84.952795+0.004126j
[2025-09-02 04:24:30] [Iter 718/2250] R2[267/600], Temp: 0.5860, Energy: -85.058512+0.001576j
[2025-09-02 04:25:10] [Iter 719/2250] R2[268/600], Temp: 0.5834, Energy: -85.119303-0.002003j
[2025-09-02 04:25:50] [Iter 720/2250] R2[269/600], Temp: 0.5808, Energy: -85.074506+0.003237j
[2025-09-02 04:26:30] [Iter 721/2250] R2[270/600], Temp: 0.5782, Energy: -84.949408-0.000532j
[2025-09-02 04:27:11] [Iter 722/2250] R2[271/600], Temp: 0.5756, Energy: -85.056910+0.003381j
[2025-09-02 04:27:51] [Iter 723/2250] R2[272/600], Temp: 0.5730, Energy: -85.033450-0.000129j
[2025-09-02 04:28:31] [Iter 724/2250] R2[273/600], Temp: 0.5705, Energy: -85.026779+0.000314j
[2025-09-02 04:29:12] [Iter 725/2250] R2[274/600], Temp: 0.5679, Energy: -84.979024-0.002334j
[2025-09-02 04:29:52] [Iter 726/2250] R2[275/600], Temp: 0.5653, Energy: -84.983423-0.001907j
[2025-09-02 04:30:32] [Iter 727/2250] R2[276/600], Temp: 0.5627, Energy: -85.042895-0.001679j
[2025-09-02 04:31:12] [Iter 728/2250] R2[277/600], Temp: 0.5601, Energy: -85.027860-0.003423j
[2025-09-02 04:31:53] [Iter 729/2250] R2[278/600], Temp: 0.5575, Energy: -84.981603+0.003999j
[2025-09-02 04:32:33] [Iter 730/2250] R2[279/600], Temp: 0.5549, Energy: -85.025418+0.004381j
[2025-09-02 04:33:13] [Iter 731/2250] R2[280/600], Temp: 0.5523, Energy: -84.926014+0.000441j
[2025-09-02 04:33:54] [Iter 732/2250] R2[281/600], Temp: 0.5497, Energy: -84.939634-0.002476j
[2025-09-02 04:34:34] [Iter 733/2250] R2[282/600], Temp: 0.5471, Energy: -84.939018-0.004447j
[2025-09-02 04:35:14] [Iter 734/2250] R2[283/600], Temp: 0.5444, Energy: -84.900679+0.001548j
[2025-09-02 04:35:55] [Iter 735/2250] R2[284/600], Temp: 0.5418, Energy: -84.890588+0.000525j
[2025-09-02 04:36:35] [Iter 736/2250] R2[285/600], Temp: 0.5392, Energy: -84.973018-0.005367j
[2025-09-02 04:37:15] [Iter 737/2250] R2[286/600], Temp: 0.5366, Energy: -84.965043-0.001170j
[2025-09-02 04:37:55] [Iter 738/2250] R2[287/600], Temp: 0.5340, Energy: -84.969901+0.000189j
[2025-09-02 04:38:36] [Iter 739/2250] R2[288/600], Temp: 0.5314, Energy: -84.902913-0.005779j
[2025-09-02 04:39:16] [Iter 740/2250] R2[289/600], Temp: 0.5288, Energy: -84.974086+0.001566j
[2025-09-02 04:39:56] [Iter 741/2250] R2[290/600], Temp: 0.5262, Energy: -85.033385-0.002638j
[2025-09-02 04:40:37] [Iter 742/2250] R2[291/600], Temp: 0.5236, Energy: -84.998234+0.001587j
[2025-09-02 04:41:17] [Iter 743/2250] R2[292/600], Temp: 0.5209, Energy: -85.072602+0.000875j
[2025-09-02 04:41:57] [Iter 744/2250] R2[293/600], Temp: 0.5183, Energy: -85.092850-0.000660j
[2025-09-02 04:42:38] [Iter 745/2250] R2[294/600], Temp: 0.5157, Energy: -85.030753+0.003899j
[2025-09-02 04:43:18] [Iter 746/2250] R2[295/600], Temp: 0.5131, Energy: -84.975001+0.000965j
[2025-09-02 04:43:58] [Iter 747/2250] R2[296/600], Temp: 0.5105, Energy: -84.991184+0.002168j
[2025-09-02 04:44:38] [Iter 748/2250] R2[297/600], Temp: 0.5079, Energy: -84.962630+0.002395j
[2025-09-02 04:45:19] [Iter 749/2250] R2[298/600], Temp: 0.5052, Energy: -84.899771-0.000697j
[2025-09-02 04:45:59] [Iter 750/2250] R2[299/600], Temp: 0.5026, Energy: -84.973503+0.004798j
[2025-09-02 04:45:59] ✓ Checkpoint saved: checkpoint_iter_000750.pkl
[2025-09-02 04:46:39] [Iter 751/2250] R2[300/600], Temp: 0.5000, Energy: -84.946222+0.000504j
[2025-09-02 04:47:20] [Iter 752/2250] R2[301/600], Temp: 0.4974, Energy: -84.977452+0.000969j
[2025-09-02 04:48:00] [Iter 753/2250] R2[302/600], Temp: 0.4948, Energy: -84.956187-0.003111j
[2025-09-02 04:48:40] [Iter 754/2250] R2[303/600], Temp: 0.4921, Energy: -84.895561-0.002769j
[2025-09-02 04:49:20] [Iter 755/2250] R2[304/600], Temp: 0.4895, Energy: -84.873935+0.002681j
[2025-09-02 04:50:01] [Iter 756/2250] R2[305/600], Temp: 0.4869, Energy: -84.865056+0.000300j
[2025-09-02 04:50:41] [Iter 757/2250] R2[306/600], Temp: 0.4843, Energy: -84.892126-0.005955j
[2025-09-02 04:51:21] [Iter 758/2250] R2[307/600], Temp: 0.4817, Energy: -84.909494+0.000593j
[2025-09-02 04:52:02] [Iter 759/2250] R2[308/600], Temp: 0.4791, Energy: -84.958334+0.000552j
[2025-09-02 04:52:42] [Iter 760/2250] R2[309/600], Temp: 0.4764, Energy: -84.864867+0.001598j
[2025-09-02 04:53:22] [Iter 761/2250] R2[310/600], Temp: 0.4738, Energy: -84.966156-0.001218j
[2025-09-02 04:54:02] [Iter 762/2250] R2[311/600], Temp: 0.4712, Energy: -84.989602-0.000519j
[2025-09-02 04:54:43] [Iter 763/2250] R2[312/600], Temp: 0.4686, Energy: -85.036013+0.001187j
[2025-09-02 04:55:23] [Iter 764/2250] R2[313/600], Temp: 0.4660, Energy: -84.991877-0.000444j
[2025-09-02 04:56:03] [Iter 765/2250] R2[314/600], Temp: 0.4634, Energy: -84.910562+0.001841j
[2025-09-02 04:56:44] [Iter 766/2250] R2[315/600], Temp: 0.4608, Energy: -84.982381+0.000127j
[2025-09-02 04:57:24] [Iter 767/2250] R2[316/600], Temp: 0.4582, Energy: -85.051131-0.002514j
[2025-09-02 04:58:04] [Iter 768/2250] R2[317/600], Temp: 0.4556, Energy: -84.959056+0.000447j
[2025-09-02 04:58:45] [Iter 769/2250] R2[318/600], Temp: 0.4529, Energy: -84.979363+0.001116j
[2025-09-02 04:59:25] [Iter 770/2250] R2[319/600], Temp: 0.4503, Energy: -84.929677+0.007527j
[2025-09-02 05:00:05] [Iter 771/2250] R2[320/600], Temp: 0.4477, Energy: -84.945926+0.004124j
[2025-09-02 05:00:45] [Iter 772/2250] R2[321/600], Temp: 0.4451, Energy: -85.013371+0.001601j
[2025-09-02 05:01:26] [Iter 773/2250] R2[322/600], Temp: 0.4425, Energy: -84.992986-0.001723j
[2025-09-02 05:02:06] [Iter 774/2250] R2[323/600], Temp: 0.4399, Energy: -85.036140-0.003770j
[2025-09-02 05:02:46] [Iter 775/2250] R2[324/600], Temp: 0.4373, Energy: -85.062515-0.000913j
[2025-09-02 05:03:27] [Iter 776/2250] R2[325/600], Temp: 0.4347, Energy: -85.040482-0.001251j
[2025-09-02 05:04:07] [Iter 777/2250] R2[326/600], Temp: 0.4321, Energy: -85.048537-0.006282j
[2025-09-02 05:04:47] [Iter 778/2250] R2[327/600], Temp: 0.4295, Energy: -85.045232+0.001956j
[2025-09-02 05:05:27] [Iter 779/2250] R2[328/600], Temp: 0.4270, Energy: -84.981891-0.006759j
[2025-09-02 05:06:08] [Iter 780/2250] R2[329/600], Temp: 0.4244, Energy: -84.985340+0.001326j
[2025-09-02 05:06:48] [Iter 781/2250] R2[330/600], Temp: 0.4218, Energy: -84.992325+0.000100j
[2025-09-02 05:07:28] [Iter 782/2250] R2[331/600], Temp: 0.4192, Energy: -85.027314+0.002960j
[2025-09-02 05:08:09] [Iter 783/2250] R2[332/600], Temp: 0.4166, Energy: -85.028601+0.000008j
[2025-09-02 05:08:49] [Iter 784/2250] R2[333/600], Temp: 0.4140, Energy: -85.090734+0.005768j
[2025-09-02 05:09:29] [Iter 785/2250] R2[334/600], Temp: 0.4115, Energy: -85.052817-0.004039j
[2025-09-02 05:10:09] [Iter 786/2250] R2[335/600], Temp: 0.4089, Energy: -85.013068+0.000804j
[2025-09-02 05:10:50] [Iter 787/2250] R2[336/600], Temp: 0.4063, Energy: -85.056153-0.003406j
[2025-09-02 05:11:30] [Iter 788/2250] R2[337/600], Temp: 0.4037, Energy: -84.997359-0.004823j
[2025-09-02 05:12:10] [Iter 789/2250] R2[338/600], Temp: 0.4012, Energy: -85.037181-0.000037j
[2025-09-02 05:12:51] [Iter 790/2250] R2[339/600], Temp: 0.3986, Energy: -84.997185-0.001257j
[2025-09-02 05:13:31] [Iter 791/2250] R2[340/600], Temp: 0.3960, Energy: -85.008873-0.005187j
[2025-09-02 05:14:11] [Iter 792/2250] R2[341/600], Temp: 0.3935, Energy: -84.977766+0.005499j
[2025-09-02 05:14:52] [Iter 793/2250] R2[342/600], Temp: 0.3909, Energy: -84.992513+0.003485j
[2025-09-02 05:15:32] [Iter 794/2250] R2[343/600], Temp: 0.3884, Energy: -84.954411-0.003790j
[2025-09-02 05:16:12] [Iter 795/2250] R2[344/600], Temp: 0.3858, Energy: -84.973476-0.001122j
[2025-09-02 05:16:52] [Iter 796/2250] R2[345/600], Temp: 0.3833, Energy: -85.003506+0.003644j
[2025-09-02 05:17:33] [Iter 797/2250] R2[346/600], Temp: 0.3807, Energy: -85.006171-0.013996j
[2025-09-02 05:18:13] [Iter 798/2250] R2[347/600], Temp: 0.3782, Energy: -84.997133-0.007493j
[2025-09-02 05:18:53] [Iter 799/2250] R2[348/600], Temp: 0.3757, Energy: -85.024009-0.003379j
[2025-09-02 05:19:33] [Iter 800/2250] R2[349/600], Temp: 0.3731, Energy: -84.997739+0.000694j
[2025-09-02 05:20:14] [Iter 801/2250] R2[350/600], Temp: 0.3706, Energy: -85.069188+0.003870j
[2025-09-02 05:20:54] [Iter 802/2250] R2[351/600], Temp: 0.3681, Energy: -85.026745+0.001594j
[2025-09-02 05:21:34] [Iter 803/2250] R2[352/600], Temp: 0.3655, Energy: -84.945920-0.000138j
[2025-09-02 05:22:15] [Iter 804/2250] R2[353/600], Temp: 0.3630, Energy: -85.022157-0.001814j
[2025-09-02 05:22:55] [Iter 805/2250] R2[354/600], Temp: 0.3605, Energy: -84.987627+0.000353j
[2025-09-02 05:23:35] [Iter 806/2250] R2[355/600], Temp: 0.3580, Energy: -85.082228-0.000250j
[2025-09-02 05:24:15] [Iter 807/2250] R2[356/600], Temp: 0.3555, Energy: -84.961848+0.001953j
[2025-09-02 05:24:56] [Iter 808/2250] R2[357/600], Temp: 0.3530, Energy: -85.053829+0.000670j
[2025-09-02 05:25:36] [Iter 809/2250] R2[358/600], Temp: 0.3505, Energy: -84.983635-0.001150j
[2025-09-02 05:26:16] [Iter 810/2250] R2[359/600], Temp: 0.3480, Energy: -84.911433-0.002451j
[2025-09-02 05:26:57] [Iter 811/2250] R2[360/600], Temp: 0.3455, Energy: -84.959170+0.003793j
[2025-09-02 05:27:37] [Iter 812/2250] R2[361/600], Temp: 0.3430, Energy: -84.949068-0.001520j
[2025-09-02 05:28:17] [Iter 813/2250] R2[362/600], Temp: 0.3405, Energy: -84.966861+0.000874j
[2025-09-02 05:28:57] [Iter 814/2250] R2[363/600], Temp: 0.3380, Energy: -85.006430+0.001139j
[2025-09-02 05:29:38] [Iter 815/2250] R2[364/600], Temp: 0.3356, Energy: -85.008258-0.001502j
[2025-09-02 05:30:18] [Iter 816/2250] R2[365/600], Temp: 0.3331, Energy: -85.005841+0.000878j
[2025-09-02 05:30:58] [Iter 817/2250] R2[366/600], Temp: 0.3306, Energy: -84.929505-0.005418j
[2025-09-02 05:31:38] [Iter 818/2250] R2[367/600], Temp: 0.3282, Energy: -84.941867-0.001632j
[2025-09-02 05:32:19] [Iter 819/2250] R2[368/600], Temp: 0.3257, Energy: -84.955556-0.002005j
[2025-09-02 05:32:59] [Iter 820/2250] R2[369/600], Temp: 0.3233, Energy: -84.939159+0.003357j
[2025-09-02 05:33:39] [Iter 821/2250] R2[370/600], Temp: 0.3208, Energy: -84.986338+0.000587j
[2025-09-02 05:34:20] [Iter 822/2250] R2[371/600], Temp: 0.3184, Energy: -84.992322-0.004364j
[2025-09-02 05:35:00] [Iter 823/2250] R2[372/600], Temp: 0.3159, Energy: -85.046011-0.002857j
[2025-09-02 05:35:40] [Iter 824/2250] R2[373/600], Temp: 0.3135, Energy: -85.044631-0.000948j
[2025-09-02 05:36:21] [Iter 825/2250] R2[374/600], Temp: 0.3111, Energy: -85.023897-0.002891j
[2025-09-02 05:37:01] [Iter 826/2250] R2[375/600], Temp: 0.3087, Energy: -84.936725-0.001974j
[2025-09-02 05:37:41] [Iter 827/2250] R2[376/600], Temp: 0.3062, Energy: -84.898152-0.002751j
[2025-09-02 05:38:21] [Iter 828/2250] R2[377/600], Temp: 0.3038, Energy: -85.005548-0.001094j
[2025-09-02 05:39:02] [Iter 829/2250] R2[378/600], Temp: 0.3014, Energy: -84.995106-0.001182j
[2025-09-02 05:39:42] [Iter 830/2250] R2[379/600], Temp: 0.2990, Energy: -85.027764-0.000467j
[2025-09-02 05:40:22] [Iter 831/2250] R2[380/600], Temp: 0.2966, Energy: -85.083793+0.003079j
[2025-09-02 05:41:03] [Iter 832/2250] R2[381/600], Temp: 0.2942, Energy: -84.929387-0.000410j
[2025-09-02 05:41:43] [Iter 833/2250] R2[382/600], Temp: 0.2919, Energy: -84.933724+0.001297j
[2025-09-02 05:42:23] [Iter 834/2250] R2[383/600], Temp: 0.2895, Energy: -84.911555+0.000022j
[2025-09-02 05:43:03] [Iter 835/2250] R2[384/600], Temp: 0.2871, Energy: -84.955726+0.003058j
[2025-09-02 05:43:44] [Iter 836/2250] R2[385/600], Temp: 0.2847, Energy: -84.917570+0.001013j
[2025-09-02 05:44:24] [Iter 837/2250] R2[386/600], Temp: 0.2824, Energy: -84.891855-0.000273j
[2025-09-02 05:45:04] [Iter 838/2250] R2[387/600], Temp: 0.2800, Energy: -84.956424+0.004689j
[2025-09-02 05:45:45] [Iter 839/2250] R2[388/600], Temp: 0.2777, Energy: -85.018031-0.002009j
[2025-09-02 05:46:25] [Iter 840/2250] R2[389/600], Temp: 0.2753, Energy: -84.948531-0.001909j
[2025-09-02 05:47:05] [Iter 841/2250] R2[390/600], Temp: 0.2730, Energy: -84.885624-0.005578j
[2025-09-02 05:47:45] [Iter 842/2250] R2[391/600], Temp: 0.2707, Energy: -84.891525+0.002057j
[2025-09-02 05:48:26] [Iter 843/2250] R2[392/600], Temp: 0.2684, Energy: -84.926952-0.002218j
[2025-09-02 05:49:06] [Iter 844/2250] R2[393/600], Temp: 0.2660, Energy: -84.978859+0.002147j
[2025-09-02 05:49:46] [Iter 845/2250] R2[394/600], Temp: 0.2637, Energy: -85.009194-0.000852j
[2025-09-02 05:50:26] [Iter 846/2250] R2[395/600], Temp: 0.2614, Energy: -84.964386-0.002232j
[2025-09-02 05:51:07] [Iter 847/2250] R2[396/600], Temp: 0.2591, Energy: -84.992657-0.000789j
[2025-09-02 05:51:47] [Iter 848/2250] R2[397/600], Temp: 0.2568, Energy: -84.975952+0.003198j
[2025-09-02 05:52:27] [Iter 849/2250] R2[398/600], Temp: 0.2545, Energy: -85.004892+0.000151j
[2025-09-02 05:53:08] [Iter 850/2250] R2[399/600], Temp: 0.2523, Energy: -84.988117+0.000445j
[2025-09-02 05:53:48] [Iter 851/2250] R2[400/600], Temp: 0.2500, Energy: -84.994186+0.004154j
[2025-09-02 05:54:28] [Iter 852/2250] R2[401/600], Temp: 0.2477, Energy: -85.009506+0.000286j
[2025-09-02 05:55:08] [Iter 853/2250] R2[402/600], Temp: 0.2455, Energy: -85.009208-0.003524j
[2025-09-02 05:55:49] [Iter 854/2250] R2[403/600], Temp: 0.2432, Energy: -85.057629+0.001584j
[2025-09-02 05:56:29] [Iter 855/2250] R2[404/600], Temp: 0.2410, Energy: -85.029906+0.003372j
[2025-09-02 05:57:09] [Iter 856/2250] R2[405/600], Temp: 0.2388, Energy: -84.968104-0.000540j
[2025-09-02 05:57:49] [Iter 857/2250] R2[406/600], Temp: 0.2365, Energy: -85.016913+0.001783j
[2025-09-02 05:58:30] [Iter 858/2250] R2[407/600], Temp: 0.2343, Energy: -85.061528-0.002327j
[2025-09-02 05:59:10] [Iter 859/2250] R2[408/600], Temp: 0.2321, Energy: -85.031282+0.004120j
[2025-09-02 05:59:50] [Iter 860/2250] R2[409/600], Temp: 0.2299, Energy: -85.031407-0.001431j
[2025-09-02 06:00:31] [Iter 861/2250] R2[410/600], Temp: 0.2277, Energy: -84.975522-0.001212j
[2025-09-02 06:01:11] [Iter 862/2250] R2[411/600], Temp: 0.2255, Energy: -84.982925+0.001030j
[2025-09-02 06:01:51] [Iter 863/2250] R2[412/600], Temp: 0.2233, Energy: -85.028603-0.000656j
[2025-09-02 06:02:31] [Iter 864/2250] R2[413/600], Temp: 0.2211, Energy: -85.000619+0.001263j
[2025-09-02 06:03:12] [Iter 865/2250] R2[414/600], Temp: 0.2190, Energy: -84.988023-0.008444j
[2025-09-02 06:03:52] [Iter 866/2250] R2[415/600], Temp: 0.2168, Energy: -85.011691-0.000410j
[2025-09-02 06:04:32] [Iter 867/2250] R2[416/600], Temp: 0.2146, Energy: -84.919968-0.002917j
[2025-09-02 06:05:12] [Iter 868/2250] R2[417/600], Temp: 0.2125, Energy: -85.057595-0.002069j
[2025-09-02 06:05:53] [Iter 869/2250] R2[418/600], Temp: 0.2104, Energy: -85.016021+0.002471j
[2025-09-02 06:06:33] [Iter 870/2250] R2[419/600], Temp: 0.2082, Energy: -85.035763+0.002667j
[2025-09-02 06:07:13] [Iter 871/2250] R2[420/600], Temp: 0.2061, Energy: -84.979940-0.001967j
[2025-09-02 06:07:54] [Iter 872/2250] R2[421/600], Temp: 0.2040, Energy: -85.002030-0.001014j
[2025-09-02 06:08:34] [Iter 873/2250] R2[422/600], Temp: 0.2019, Energy: -84.981118+0.000823j
[2025-09-02 06:09:14] [Iter 874/2250] R2[423/600], Temp: 0.1998, Energy: -85.018755+0.003893j
[2025-09-02 06:09:54] [Iter 875/2250] R2[424/600], Temp: 0.1977, Energy: -85.056842+0.003521j
[2025-09-02 06:10:35] [Iter 876/2250] R2[425/600], Temp: 0.1956, Energy: -84.981531-0.005060j
[2025-09-02 06:11:15] [Iter 877/2250] R2[426/600], Temp: 0.1935, Energy: -85.047315-0.000964j
[2025-09-02 06:11:55] [Iter 878/2250] R2[427/600], Temp: 0.1915, Energy: -85.007513+0.003748j
[2025-09-02 06:12:36] [Iter 879/2250] R2[428/600], Temp: 0.1894, Energy: -84.932607-0.002252j
[2025-09-02 06:13:16] [Iter 880/2250] R2[429/600], Temp: 0.1874, Energy: -84.976917-0.002125j
[2025-09-02 06:13:56] [Iter 881/2250] R2[430/600], Temp: 0.1853, Energy: -84.984116+0.000040j
[2025-09-02 06:14:36] [Iter 882/2250] R2[431/600], Temp: 0.1833, Energy: -84.987796-0.000319j
[2025-09-02 06:15:17] [Iter 883/2250] R2[432/600], Temp: 0.1813, Energy: -84.956051-0.000130j
[2025-09-02 06:15:57] [Iter 884/2250] R2[433/600], Temp: 0.1793, Energy: -84.913401-0.004839j
[2025-09-02 06:16:37] [Iter 885/2250] R2[434/600], Temp: 0.1773, Energy: -85.014278+0.000334j
[2025-09-02 06:17:18] [Iter 886/2250] R2[435/600], Temp: 0.1753, Energy: -85.042881-0.002210j
[2025-09-02 06:17:58] [Iter 887/2250] R2[436/600], Temp: 0.1733, Energy: -85.017899-0.000911j
[2025-09-02 06:18:38] [Iter 888/2250] R2[437/600], Temp: 0.1713, Energy: -84.982870-0.002568j
[2025-09-02 06:19:18] [Iter 889/2250] R2[438/600], Temp: 0.1693, Energy: -85.023102-0.001642j
[2025-09-02 06:19:59] [Iter 890/2250] R2[439/600], Temp: 0.1674, Energy: -84.968298+0.000894j
[2025-09-02 06:20:39] [Iter 891/2250] R2[440/600], Temp: 0.1654, Energy: -84.905214-0.000375j
[2025-09-02 06:21:19] [Iter 892/2250] R2[441/600], Temp: 0.1635, Energy: -84.971573-0.003271j
[2025-09-02 06:21:59] [Iter 893/2250] R2[442/600], Temp: 0.1616, Energy: -84.948417+0.000840j
[2025-09-02 06:22:40] [Iter 894/2250] R2[443/600], Temp: 0.1596, Energy: -84.944649-0.001572j
[2025-09-02 06:23:20] [Iter 895/2250] R2[444/600], Temp: 0.1577, Energy: -85.078419-0.004002j
[2025-09-02 06:24:00] [Iter 896/2250] R2[445/600], Temp: 0.1558, Energy: -85.076852+0.003439j
[2025-09-02 06:24:41] [Iter 897/2250] R2[446/600], Temp: 0.1539, Energy: -85.099913+0.001529j
[2025-09-02 06:25:21] [Iter 898/2250] R2[447/600], Temp: 0.1520, Energy: -85.042067+0.001475j
[2025-09-02 06:26:01] [Iter 899/2250] R2[448/600], Temp: 0.1502, Energy: -84.995913+0.000297j
[2025-09-02 06:26:41] [Iter 900/2250] R2[449/600], Temp: 0.1483, Energy: -84.969993+0.006284j
[2025-09-02 06:27:22] [Iter 901/2250] R2[450/600], Temp: 0.1464, Energy: -84.971300+0.001648j
[2025-09-02 06:28:02] [Iter 902/2250] R2[451/600], Temp: 0.1446, Energy: -84.939265+0.001131j
[2025-09-02 06:28:42] [Iter 903/2250] R2[452/600], Temp: 0.1428, Energy: -84.925550+0.000329j
[2025-09-02 06:29:22] [Iter 904/2250] R2[453/600], Temp: 0.1409, Energy: -84.884508-0.000535j
[2025-09-02 06:30:03] [Iter 905/2250] R2[454/600], Temp: 0.1391, Energy: -84.921275+0.001954j
[2025-09-02 06:30:43] [Iter 906/2250] R2[455/600], Temp: 0.1373, Energy: -84.892150+0.000898j
[2025-09-02 06:31:23] [Iter 907/2250] R2[456/600], Temp: 0.1355, Energy: -84.886637+0.000601j
[2025-09-02 06:32:03] [Iter 908/2250] R2[457/600], Temp: 0.1337, Energy: -84.950899-0.000554j
[2025-09-02 06:32:44] [Iter 909/2250] R2[458/600], Temp: 0.1320, Energy: -84.979040-0.002923j
[2025-09-02 06:33:24] [Iter 910/2250] R2[459/600], Temp: 0.1302, Energy: -84.974509+0.000384j
[2025-09-02 06:34:04] [Iter 911/2250] R2[460/600], Temp: 0.1284, Energy: -85.007212+0.000055j
[2025-09-02 06:34:44] [Iter 912/2250] R2[461/600], Temp: 0.1267, Energy: -85.016267+0.002573j
[2025-09-02 06:35:25] [Iter 913/2250] R2[462/600], Temp: 0.1249, Energy: -85.003770-0.005329j
[2025-09-02 06:36:05] [Iter 914/2250] R2[463/600], Temp: 0.1232, Energy: -85.027066-0.002312j
[2025-09-02 06:36:45] [Iter 915/2250] R2[464/600], Temp: 0.1215, Energy: -85.012564+0.002550j
[2025-09-02 06:37:25] [Iter 916/2250] R2[465/600], Temp: 0.1198, Energy: -85.092390-0.006100j
[2025-09-02 06:38:06] [Iter 917/2250] R2[466/600], Temp: 0.1181, Energy: -85.015852+0.001612j
[2025-09-02 06:38:46] [Iter 918/2250] R2[467/600], Temp: 0.1164, Energy: -85.003815+0.003905j
[2025-09-02 06:39:26] [Iter 919/2250] R2[468/600], Temp: 0.1147, Energy: -84.935529-0.000412j
[2025-09-02 06:40:06] [Iter 920/2250] R2[469/600], Temp: 0.1131, Energy: -84.973548-0.002547j
[2025-09-02 06:40:47] [Iter 921/2250] R2[470/600], Temp: 0.1114, Energy: -85.026010+0.001136j
[2025-09-02 06:41:27] [Iter 922/2250] R2[471/600], Temp: 0.1098, Energy: -84.971718+0.001040j
[2025-09-02 06:42:07] [Iter 923/2250] R2[472/600], Temp: 0.1082, Energy: -84.957424+0.001730j
[2025-09-02 06:42:48] [Iter 924/2250] R2[473/600], Temp: 0.1065, Energy: -84.987784-0.000397j
[2025-09-02 06:43:28] [Iter 925/2250] R2[474/600], Temp: 0.1049, Energy: -85.035552-0.002488j
[2025-09-02 06:44:08] [Iter 926/2250] R2[475/600], Temp: 0.1033, Energy: -84.963107+0.002252j
[2025-09-02 06:44:48] [Iter 927/2250] R2[476/600], Temp: 0.1017, Energy: -84.906479-0.000834j
[2025-09-02 06:45:29] [Iter 928/2250] R2[477/600], Temp: 0.1002, Energy: -84.840058+0.000167j
[2025-09-02 06:46:09] [Iter 929/2250] R2[478/600], Temp: 0.0986, Energy: -84.840612-0.000999j
[2025-09-02 06:46:49] [Iter 930/2250] R2[479/600], Temp: 0.0970, Energy: -84.992015+0.000341j
[2025-09-02 06:47:29] [Iter 931/2250] R2[480/600], Temp: 0.0955, Energy: -85.000900+0.004307j
[2025-09-02 06:48:10] [Iter 932/2250] R2[481/600], Temp: 0.0940, Energy: -84.985499-0.001061j
[2025-09-02 06:48:50] [Iter 933/2250] R2[482/600], Temp: 0.0924, Energy: -84.987451-0.000269j
[2025-09-02 06:49:30] [Iter 934/2250] R2[483/600], Temp: 0.0909, Energy: -85.051385-0.000010j
[2025-09-02 06:50:10] [Iter 935/2250] R2[484/600], Temp: 0.0894, Energy: -85.011585+0.000165j
[2025-09-02 06:50:51] [Iter 936/2250] R2[485/600], Temp: 0.0879, Energy: -84.970079-0.000111j
[2025-09-02 06:51:31] [Iter 937/2250] R2[486/600], Temp: 0.0865, Energy: -85.013012+0.003369j
[2025-09-02 06:52:11] [Iter 938/2250] R2[487/600], Temp: 0.0850, Energy: -85.028458+0.001849j
[2025-09-02 06:52:52] [Iter 939/2250] R2[488/600], Temp: 0.0835, Energy: -85.033060-0.002191j
[2025-09-02 06:53:32] [Iter 940/2250] R2[489/600], Temp: 0.0821, Energy: -85.068071-0.002659j
[2025-09-02 06:54:12] [Iter 941/2250] R2[490/600], Temp: 0.0807, Energy: -85.009253+0.000840j
[2025-09-02 06:54:52] [Iter 942/2250] R2[491/600], Temp: 0.0792, Energy: -84.995827+0.001582j
[2025-09-02 06:55:33] [Iter 943/2250] R2[492/600], Temp: 0.0778, Energy: -85.043346-0.002596j
[2025-09-02 06:56:13] [Iter 944/2250] R2[493/600], Temp: 0.0764, Energy: -85.054460-0.000098j
[2025-09-02 06:56:53] [Iter 945/2250] R2[494/600], Temp: 0.0751, Energy: -85.119957-0.000580j
[2025-09-02 06:57:34] [Iter 946/2250] R2[495/600], Temp: 0.0737, Energy: -85.160786-0.002295j
[2025-09-02 06:58:14] [Iter 947/2250] R2[496/600], Temp: 0.0723, Energy: -85.086199-0.001044j
[2025-09-02 06:58:54] [Iter 948/2250] R2[497/600], Temp: 0.0710, Energy: -85.000746-0.001018j
[2025-09-02 06:59:34] [Iter 949/2250] R2[498/600], Temp: 0.0696, Energy: -84.996623-0.002259j
[2025-09-02 07:00:15] [Iter 950/2250] R2[499/600], Temp: 0.0683, Energy: -85.002551-0.000251j
[2025-09-02 07:00:55] [Iter 951/2250] R2[500/600], Temp: 0.0670, Energy: -84.980102-0.001212j
[2025-09-02 07:01:35] [Iter 952/2250] R2[501/600], Temp: 0.0657, Energy: -84.997808-0.002266j
[2025-09-02 07:02:16] [Iter 953/2250] R2[502/600], Temp: 0.0644, Energy: -85.009527-0.000642j
[2025-09-02 07:02:56] [Iter 954/2250] R2[503/600], Temp: 0.0631, Energy: -84.956582+0.001557j
[2025-09-02 07:03:36] [Iter 955/2250] R2[504/600], Temp: 0.0618, Energy: -84.971167-0.000644j
[2025-09-02 07:04:16] [Iter 956/2250] R2[505/600], Temp: 0.0606, Energy: -84.933743+0.002644j
[2025-09-02 07:04:57] [Iter 957/2250] R2[506/600], Temp: 0.0593, Energy: -84.970486-0.005071j
[2025-09-02 07:05:37] [Iter 958/2250] R2[507/600], Temp: 0.0581, Energy: -84.916409-0.001589j
[2025-09-02 07:06:17] [Iter 959/2250] R2[508/600], Temp: 0.0569, Energy: -84.985569-0.000828j
[2025-09-02 07:06:57] [Iter 960/2250] R2[509/600], Temp: 0.0557, Energy: -85.023062+0.002495j
[2025-09-02 07:07:38] [Iter 961/2250] R2[510/600], Temp: 0.0545, Energy: -85.007662+0.002388j
[2025-09-02 07:08:18] [Iter 962/2250] R2[511/600], Temp: 0.0533, Energy: -84.985947+0.000385j
[2025-09-02 07:08:58] [Iter 963/2250] R2[512/600], Temp: 0.0521, Energy: -85.041272-0.004396j
[2025-09-02 07:09:38] [Iter 964/2250] R2[513/600], Temp: 0.0510, Energy: -85.082652+0.001011j
[2025-09-02 07:10:19] [Iter 965/2250] R2[514/600], Temp: 0.0498, Energy: -85.029758+0.001193j
[2025-09-02 07:10:59] [Iter 966/2250] R2[515/600], Temp: 0.0487, Energy: -85.021470+0.001195j
[2025-09-02 07:11:39] [Iter 967/2250] R2[516/600], Temp: 0.0476, Energy: -84.954910+0.004895j
[2025-09-02 07:12:19] [Iter 968/2250] R2[517/600], Temp: 0.0465, Energy: -85.025057+0.002196j
[2025-09-02 07:13:00] [Iter 969/2250] R2[518/600], Temp: 0.0454, Energy: -84.959673-0.001535j
[2025-09-02 07:13:40] [Iter 970/2250] R2[519/600], Temp: 0.0443, Energy: -84.989061+0.000652j
[2025-09-02 07:14:20] [Iter 971/2250] R2[520/600], Temp: 0.0432, Energy: -84.968276-0.000060j
[2025-09-02 07:15:01] [Iter 972/2250] R2[521/600], Temp: 0.0422, Energy: -85.044462-0.001482j
[2025-09-02 07:15:41] [Iter 973/2250] R2[522/600], Temp: 0.0411, Energy: -85.056303-0.000686j
[2025-09-02 07:16:21] [Iter 974/2250] R2[523/600], Temp: 0.0401, Energy: -85.084051+0.003667j
[2025-09-02 07:17:01] [Iter 975/2250] R2[524/600], Temp: 0.0391, Energy: -85.097650-0.001205j
[2025-09-02 07:17:42] [Iter 976/2250] R2[525/600], Temp: 0.0381, Energy: -85.027403-0.004012j
[2025-09-02 07:18:22] [Iter 977/2250] R2[526/600], Temp: 0.0371, Energy: -85.009635-0.000421j
[2025-09-02 07:19:02] [Iter 978/2250] R2[527/600], Temp: 0.0361, Energy: -85.092446+0.003189j
[2025-09-02 07:19:42] [Iter 979/2250] R2[528/600], Temp: 0.0351, Energy: -84.975878-0.000234j
[2025-09-02 07:20:23] [Iter 980/2250] R2[529/600], Temp: 0.0342, Energy: -85.025557-0.002319j
[2025-09-02 07:21:03] [Iter 981/2250] R2[530/600], Temp: 0.0332, Energy: -84.980609-0.000716j
[2025-09-02 07:21:43] [Iter 982/2250] R2[531/600], Temp: 0.0323, Energy: -85.031627+0.001158j
[2025-09-02 07:22:23] [Iter 983/2250] R2[532/600], Temp: 0.0314, Energy: -85.007260-0.000639j
[2025-09-02 07:23:04] [Iter 984/2250] R2[533/600], Temp: 0.0305, Energy: -84.988063+0.002844j
[2025-09-02 07:23:44] [Iter 985/2250] R2[534/600], Temp: 0.0296, Energy: -85.010891+0.002593j
[2025-09-02 07:24:24] [Iter 986/2250] R2[535/600], Temp: 0.0287, Energy: -85.032554+0.002988j
[2025-09-02 07:25:04] [Iter 987/2250] R2[536/600], Temp: 0.0278, Energy: -84.933381-0.000799j
[2025-09-02 07:25:45] [Iter 988/2250] R2[537/600], Temp: 0.0270, Energy: -84.954301+0.000765j
[2025-09-02 07:26:25] [Iter 989/2250] R2[538/600], Temp: 0.0261, Energy: -84.954704-0.000154j
[2025-09-02 07:27:05] [Iter 990/2250] R2[539/600], Temp: 0.0253, Energy: -84.970617-0.004659j
[2025-09-02 07:27:45] [Iter 991/2250] R2[540/600], Temp: 0.0245, Energy: -84.989048+0.000859j
[2025-09-02 07:28:26] [Iter 992/2250] R2[541/600], Temp: 0.0237, Energy: -84.919619-0.000293j
[2025-09-02 07:29:06] [Iter 993/2250] R2[542/600], Temp: 0.0229, Energy: -84.921833+0.000820j
[2025-09-02 07:29:46] [Iter 994/2250] R2[543/600], Temp: 0.0221, Energy: -85.032624-0.003367j
[2025-09-02 07:30:27] [Iter 995/2250] R2[544/600], Temp: 0.0213, Energy: -84.948840+0.001624j
[2025-09-02 07:31:07] [Iter 996/2250] R2[545/600], Temp: 0.0206, Energy: -84.946088-0.001971j
[2025-09-02 07:31:47] [Iter 997/2250] R2[546/600], Temp: 0.0199, Energy: -84.922294-0.001911j
[2025-09-02 07:32:27] [Iter 998/2250] R2[547/600], Temp: 0.0191, Energy: -84.995584-0.001905j
[2025-09-02 07:33:08] [Iter 999/2250] R2[548/600], Temp: 0.0184, Energy: -84.985297-0.001873j
[2025-09-02 07:33:48] [Iter 1000/2250] R2[549/600], Temp: 0.0177, Energy: -85.048613+0.003217j
[2025-09-02 07:33:48] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-09-02 07:34:28] [Iter 1001/2250] R2[550/600], Temp: 0.0170, Energy: -85.007199+0.000686j
[2025-09-02 07:35:08] [Iter 1002/2250] R2[551/600], Temp: 0.0164, Energy: -85.032839+0.000778j
[2025-09-02 07:35:49] [Iter 1003/2250] R2[552/600], Temp: 0.0157, Energy: -85.000677-0.002267j
[2025-09-02 07:36:29] [Iter 1004/2250] R2[553/600], Temp: 0.0151, Energy: -84.989845+0.002044j
[2025-09-02 07:37:09] [Iter 1005/2250] R2[554/600], Temp: 0.0144, Energy: -85.010017+0.002173j
[2025-09-02 07:37:50] [Iter 1006/2250] R2[555/600], Temp: 0.0138, Energy: -85.096230+0.000092j
[2025-09-02 07:38:30] [Iter 1007/2250] R2[556/600], Temp: 0.0132, Energy: -85.056335-0.002401j
[2025-09-02 07:39:10] [Iter 1008/2250] R2[557/600], Temp: 0.0126, Energy: -85.012439-0.002847j
[2025-09-02 07:39:50] [Iter 1009/2250] R2[558/600], Temp: 0.0120, Energy: -85.010561-0.001641j
[2025-09-02 07:40:31] [Iter 1010/2250] R2[559/600], Temp: 0.0115, Energy: -85.074492+0.001958j
[2025-09-02 07:41:11] [Iter 1011/2250] R2[560/600], Temp: 0.0109, Energy: -85.060331-0.002869j
[2025-09-02 07:41:51] [Iter 1012/2250] R2[561/600], Temp: 0.0104, Energy: -85.069584+0.000659j
[2025-09-02 07:42:31] [Iter 1013/2250] R2[562/600], Temp: 0.0099, Energy: -84.977703+0.000131j
[2025-09-02 07:43:12] [Iter 1014/2250] R2[563/600], Temp: 0.0094, Energy: -84.897198-0.003013j
[2025-09-02 07:43:52] [Iter 1015/2250] R2[564/600], Temp: 0.0089, Energy: -85.041829+0.001520j
[2025-09-02 07:44:32] [Iter 1016/2250] R2[565/600], Temp: 0.0084, Energy: -85.028103+0.001697j
[2025-09-02 07:45:12] [Iter 1017/2250] R2[566/600], Temp: 0.0079, Energy: -84.977815+0.001484j
[2025-09-02 07:45:53] [Iter 1018/2250] R2[567/600], Temp: 0.0074, Energy: -84.995781-0.000683j
[2025-09-02 07:46:33] [Iter 1019/2250] R2[568/600], Temp: 0.0070, Energy: -85.011584+0.000474j
[2025-09-02 07:47:13] [Iter 1020/2250] R2[569/600], Temp: 0.0066, Energy: -85.040342-0.000005j
[2025-09-02 07:47:53] [Iter 1021/2250] R2[570/600], Temp: 0.0062, Energy: -85.062840+0.000628j
[2025-09-02 07:48:34] [Iter 1022/2250] R2[571/600], Temp: 0.0058, Energy: -85.060843-0.002383j
[2025-09-02 07:49:14] [Iter 1023/2250] R2[572/600], Temp: 0.0054, Energy: -85.037136-0.000566j
[2025-09-02 07:49:54] [Iter 1024/2250] R2[573/600], Temp: 0.0050, Energy: -85.006313+0.001454j
[2025-09-02 07:50:35] [Iter 1025/2250] R2[574/600], Temp: 0.0046, Energy: -85.067784+0.002402j
[2025-09-02 07:51:15] [Iter 1026/2250] R2[575/600], Temp: 0.0043, Energy: -84.954963+0.000187j
[2025-09-02 07:51:55] [Iter 1027/2250] R2[576/600], Temp: 0.0039, Energy: -85.062447+0.005745j
[2025-09-02 07:52:35] [Iter 1028/2250] R2[577/600], Temp: 0.0036, Energy: -85.004717-0.002242j
[2025-09-02 07:53:16] [Iter 1029/2250] R2[578/600], Temp: 0.0033, Energy: -85.129796-0.000007j
[2025-09-02 07:53:56] [Iter 1030/2250] R2[579/600], Temp: 0.0030, Energy: -85.083337+0.000507j
[2025-09-02 07:54:36] [Iter 1031/2250] R2[580/600], Temp: 0.0027, Energy: -85.117687+0.001133j
[2025-09-02 07:55:16] [Iter 1032/2250] R2[581/600], Temp: 0.0025, Energy: -85.113799+0.000424j
[2025-09-02 07:55:57] [Iter 1033/2250] R2[582/600], Temp: 0.0022, Energy: -85.068631+0.000346j
[2025-09-02 07:56:37] [Iter 1034/2250] R2[583/600], Temp: 0.0020, Energy: -85.117516+0.003139j
[2025-09-02 07:57:17] [Iter 1035/2250] R2[584/600], Temp: 0.0018, Energy: -85.102846+0.001668j
[2025-09-02 07:57:58] [Iter 1036/2250] R2[585/600], Temp: 0.0015, Energy: -85.108932-0.000470j
[2025-09-02 07:58:38] [Iter 1037/2250] R2[586/600], Temp: 0.0013, Energy: -85.065507-0.002133j
[2025-09-02 07:59:18] [Iter 1038/2250] R2[587/600], Temp: 0.0012, Energy: -85.131510-0.001686j
[2025-09-02 07:59:58] [Iter 1039/2250] R2[588/600], Temp: 0.0010, Energy: -85.077327+0.002262j
[2025-09-02 08:00:39] [Iter 1040/2250] R2[589/600], Temp: 0.0008, Energy: -85.080985+0.000920j
[2025-09-02 08:01:19] [Iter 1041/2250] R2[590/600], Temp: 0.0007, Energy: -85.054173+0.003906j
[2025-09-02 08:01:59] [Iter 1042/2250] R2[591/600], Temp: 0.0006, Energy: -85.037707+0.004288j
[2025-09-02 08:02:40] [Iter 1043/2250] R2[592/600], Temp: 0.0004, Energy: -85.015374+0.002092j
[2025-09-02 08:03:20] [Iter 1044/2250] R2[593/600], Temp: 0.0003, Energy: -85.034591-0.002006j
[2025-09-02 08:04:00] [Iter 1045/2250] R2[594/600], Temp: 0.0002, Energy: -85.008890-0.000358j
[2025-09-02 08:04:40] [Iter 1046/2250] R2[595/600], Temp: 0.0002, Energy: -84.996889+0.000959j
[2025-09-02 08:05:21] [Iter 1047/2250] R2[596/600], Temp: 0.0001, Energy: -85.025492-0.000954j
[2025-09-02 08:06:01] [Iter 1048/2250] R2[597/600], Temp: 0.0001, Energy: -85.014121-0.002948j
[2025-09-02 08:06:41] [Iter 1049/2250] R2[598/600], Temp: 0.0000, Energy: -85.003624-0.003396j
[2025-09-02 08:07:22] [Iter 1050/2250] R2[599/600], Temp: 0.0000, Energy: -85.049985+0.002048j
[2025-09-02 08:07:22] RESTART #3 | Period: 1200
[2025-09-02 08:08:02] [Iter 1051/2250] R3[0/1200], Temp: 1.0000, Energy: -85.049962+0.002719j
[2025-09-02 08:08:42] [Iter 1052/2250] R3[1/1200], Temp: 1.0000, Energy: -85.132316-0.000308j
[2025-09-02 08:09:22] [Iter 1053/2250] R3[2/1200], Temp: 1.0000, Energy: -85.051197+0.004997j
[2025-09-02 08:10:03] [Iter 1054/2250] R3[3/1200], Temp: 1.0000, Energy: -85.052303-0.000062j
[2025-09-02 08:10:43] [Iter 1055/2250] R3[4/1200], Temp: 1.0000, Energy: -85.056664+0.002226j
[2025-09-02 08:11:23] [Iter 1056/2250] R3[5/1200], Temp: 1.0000, Energy: -85.021706+0.003068j
[2025-09-02 08:12:03] [Iter 1057/2250] R3[6/1200], Temp: 0.9999, Energy: -85.053530-0.001984j
[2025-09-02 08:12:44] [Iter 1058/2250] R3[7/1200], Temp: 0.9999, Energy: -85.056252-0.002439j
[2025-09-02 08:13:24] [Iter 1059/2250] R3[8/1200], Temp: 0.9999, Energy: -85.104521+0.000997j
[2025-09-02 08:14:04] [Iter 1060/2250] R3[9/1200], Temp: 0.9999, Energy: -85.044565-0.000938j
[2025-09-02 08:14:45] [Iter 1061/2250] R3[10/1200], Temp: 0.9998, Energy: -85.100872-0.000296j
[2025-09-02 08:15:25] [Iter 1062/2250] R3[11/1200], Temp: 0.9998, Energy: -85.058155+0.000739j
[2025-09-02 08:16:05] [Iter 1063/2250] R3[12/1200], Temp: 0.9998, Energy: -84.978729-0.004771j
[2025-09-02 08:16:45] [Iter 1064/2250] R3[13/1200], Temp: 0.9997, Energy: -85.018786-0.005517j
[2025-09-02 08:17:26] [Iter 1065/2250] R3[14/1200], Temp: 0.9997, Energy: -85.000790-0.002747j
[2025-09-02 08:18:06] [Iter 1066/2250] R3[15/1200], Temp: 0.9996, Energy: -84.984203-0.000592j
[2025-09-02 08:18:46] [Iter 1067/2250] R3[16/1200], Temp: 0.9996, Energy: -85.021749+0.001228j
[2025-09-02 08:19:27] [Iter 1068/2250] R3[17/1200], Temp: 0.9995, Energy: -85.050502+0.004083j
[2025-09-02 08:20:07] [Iter 1069/2250] R3[18/1200], Temp: 0.9994, Energy: -85.042683+0.000605j
[2025-09-02 08:20:47] [Iter 1070/2250] R3[19/1200], Temp: 0.9994, Energy: -85.062446+0.000279j
[2025-09-02 08:21:27] [Iter 1071/2250] R3[20/1200], Temp: 0.9993, Energy: -85.059099-0.004278j
[2025-09-02 08:22:08] [Iter 1072/2250] R3[21/1200], Temp: 0.9992, Energy: -85.004890+0.003698j
[2025-09-02 08:22:48] [Iter 1073/2250] R3[22/1200], Temp: 0.9992, Energy: -85.067535+0.001168j
[2025-09-02 08:23:28] [Iter 1074/2250] R3[23/1200], Temp: 0.9991, Energy: -85.027559-0.000335j
[2025-09-02 08:24:08] [Iter 1075/2250] R3[24/1200], Temp: 0.9990, Energy: -85.069700+0.001667j
[2025-09-02 08:24:49] [Iter 1076/2250] R3[25/1200], Temp: 0.9989, Energy: -85.071748+0.000215j
[2025-09-02 08:25:29] [Iter 1077/2250] R3[26/1200], Temp: 0.9988, Energy: -85.062661-0.000934j
[2025-09-02 08:26:09] [Iter 1078/2250] R3[27/1200], Temp: 0.9988, Energy: -85.054030-0.000126j
[2025-09-02 08:26:50] [Iter 1079/2250] R3[28/1200], Temp: 0.9987, Energy: -84.957928-0.005762j
[2025-09-02 08:27:30] [Iter 1080/2250] R3[29/1200], Temp: 0.9986, Energy: -84.965517-0.001924j
[2025-09-02 08:28:10] [Iter 1081/2250] R3[30/1200], Temp: 0.9985, Energy: -84.898240+0.000646j
[2025-09-02 08:28:51] [Iter 1082/2250] R3[31/1200], Temp: 0.9984, Energy: -84.956795-0.001383j
[2025-09-02 08:29:31] [Iter 1083/2250] R3[32/1200], Temp: 0.9982, Energy: -84.944310+0.002751j
[2025-09-02 08:30:11] [Iter 1084/2250] R3[33/1200], Temp: 0.9981, Energy: -84.994269-0.000468j
[2025-09-02 08:30:51] [Iter 1085/2250] R3[34/1200], Temp: 0.9980, Energy: -85.066007+0.000527j
[2025-09-02 08:31:32] [Iter 1086/2250] R3[35/1200], Temp: 0.9979, Energy: -85.088487-0.000538j
[2025-09-02 08:32:12] [Iter 1087/2250] R3[36/1200], Temp: 0.9978, Energy: -85.136075-0.006881j
[2025-09-02 08:32:52] [Iter 1088/2250] R3[37/1200], Temp: 0.9977, Energy: -85.097873-0.001652j
[2025-09-02 08:33:32] [Iter 1089/2250] R3[38/1200], Temp: 0.9975, Energy: -85.124327-0.000420j
[2025-09-02 08:34:13] [Iter 1090/2250] R3[39/1200], Temp: 0.9974, Energy: -85.059006-0.001181j
[2025-09-02 08:34:53] [Iter 1091/2250] R3[40/1200], Temp: 0.9973, Energy: -85.111044+0.000468j
[2025-09-02 08:35:33] [Iter 1092/2250] R3[41/1200], Temp: 0.9971, Energy: -85.067289-0.001081j
[2025-09-02 08:36:14] [Iter 1093/2250] R3[42/1200], Temp: 0.9970, Energy: -85.141119-0.003256j
[2025-09-02 08:36:54] [Iter 1094/2250] R3[43/1200], Temp: 0.9968, Energy: -85.080855+0.002977j
[2025-09-02 08:37:34] [Iter 1095/2250] R3[44/1200], Temp: 0.9967, Energy: -85.056884+0.000710j
[2025-09-02 08:38:14] [Iter 1096/2250] R3[45/1200], Temp: 0.9965, Energy: -85.037992+0.000437j
[2025-09-02 08:38:55] [Iter 1097/2250] R3[46/1200], Temp: 0.9964, Energy: -85.078098+0.000052j
[2025-09-02 08:39:35] [Iter 1098/2250] R3[47/1200], Temp: 0.9962, Energy: -85.073299+0.000114j
[2025-09-02 08:40:15] [Iter 1099/2250] R3[48/1200], Temp: 0.9961, Energy: -85.026746+0.004508j
[2025-09-02 08:40:55] [Iter 1100/2250] R3[49/1200], Temp: 0.9959, Energy: -84.980394-0.002145j
[2025-09-02 08:41:36] [Iter 1101/2250] R3[50/1200], Temp: 0.9957, Energy: -84.957199+0.000258j
[2025-09-02 08:42:16] [Iter 1102/2250] R3[51/1200], Temp: 0.9955, Energy: -84.991237-0.002715j
[2025-09-02 08:42:56] [Iter 1103/2250] R3[52/1200], Temp: 0.9954, Energy: -85.049932+0.002998j
[2025-09-02 08:43:36] [Iter 1104/2250] R3[53/1200], Temp: 0.9952, Energy: -84.968457-0.000868j
[2025-09-02 08:44:17] [Iter 1105/2250] R3[54/1200], Temp: 0.9950, Energy: -85.027284+0.000536j
[2025-09-02 08:44:57] [Iter 1106/2250] R3[55/1200], Temp: 0.9948, Energy: -84.954741+0.000159j
[2025-09-02 08:45:37] [Iter 1107/2250] R3[56/1200], Temp: 0.9946, Energy: -84.980687+0.001068j
[2025-09-02 08:46:17] [Iter 1108/2250] R3[57/1200], Temp: 0.9944, Energy: -85.026338-0.000550j
[2025-09-02 08:46:58] [Iter 1109/2250] R3[58/1200], Temp: 0.9942, Energy: -85.035082-0.002180j
[2025-09-02 08:47:38] [Iter 1110/2250] R3[59/1200], Temp: 0.9940, Energy: -84.999319+0.002475j
[2025-09-02 08:48:18] [Iter 1111/2250] R3[60/1200], Temp: 0.9938, Energy: -85.043555-0.002187j
[2025-09-02 08:48:59] [Iter 1112/2250] R3[61/1200], Temp: 0.9936, Energy: -85.037028+0.001998j
[2025-09-02 08:49:39] [Iter 1113/2250] R3[62/1200], Temp: 0.9934, Energy: -85.025197-0.002080j
[2025-09-02 08:50:19] [Iter 1114/2250] R3[63/1200], Temp: 0.9932, Energy: -85.078099-0.001148j
[2025-09-02 08:50:59] [Iter 1115/2250] R3[64/1200], Temp: 0.9930, Energy: -84.954924-0.001276j
[2025-09-02 08:51:40] [Iter 1116/2250] R3[65/1200], Temp: 0.9928, Energy: -84.993684+0.001040j
[2025-09-02 08:52:20] [Iter 1117/2250] R3[66/1200], Temp: 0.9926, Energy: -84.985144+0.002042j
[2025-09-02 08:53:00] [Iter 1118/2250] R3[67/1200], Temp: 0.9923, Energy: -85.021630+0.002547j
[2025-09-02 08:53:40] [Iter 1119/2250] R3[68/1200], Temp: 0.9921, Energy: -85.059659+0.001365j
[2025-09-02 08:54:21] [Iter 1120/2250] R3[69/1200], Temp: 0.9919, Energy: -85.066211-0.002615j
[2025-09-02 08:55:01] [Iter 1121/2250] R3[70/1200], Temp: 0.9916, Energy: -85.087645-0.000860j
[2025-09-02 08:55:41] [Iter 1122/2250] R3[71/1200], Temp: 0.9914, Energy: -85.051095-0.003449j
[2025-09-02 08:56:21] [Iter 1123/2250] R3[72/1200], Temp: 0.9911, Energy: -85.090503-0.002244j
[2025-09-02 08:57:02] [Iter 1124/2250] R3[73/1200], Temp: 0.9909, Energy: -85.104825+0.003822j
[2025-09-02 08:57:42] [Iter 1125/2250] R3[74/1200], Temp: 0.9906, Energy: -85.089655-0.001130j
[2025-09-02 08:58:22] [Iter 1126/2250] R3[75/1200], Temp: 0.9904, Energy: -85.064213-0.003137j
[2025-09-02 08:59:02] [Iter 1127/2250] R3[76/1200], Temp: 0.9901, Energy: -85.051594-0.002178j
[2025-09-02 08:59:43] [Iter 1128/2250] R3[77/1200], Temp: 0.9899, Energy: -85.017888-0.002496j
[2025-09-02 09:00:23] [Iter 1129/2250] R3[78/1200], Temp: 0.9896, Energy: -85.113123-0.004373j
[2025-09-02 09:01:03] [Iter 1130/2250] R3[79/1200], Temp: 0.9893, Energy: -85.101296+0.005362j
[2025-09-02 09:01:43] [Iter 1131/2250] R3[80/1200], Temp: 0.9891, Energy: -85.019966+0.003473j
[2025-09-02 09:02:24] [Iter 1132/2250] R3[81/1200], Temp: 0.9888, Energy: -85.035394+0.000514j
[2025-09-02 09:03:04] [Iter 1133/2250] R3[82/1200], Temp: 0.9885, Energy: -85.015684+0.003264j
[2025-09-02 09:03:44] [Iter 1134/2250] R3[83/1200], Temp: 0.9882, Energy: -84.996968+0.000209j
[2025-09-02 09:04:24] [Iter 1135/2250] R3[84/1200], Temp: 0.9880, Energy: -85.016935+0.000104j
[2025-09-02 09:05:05] [Iter 1136/2250] R3[85/1200], Temp: 0.9877, Energy: -85.076035+0.003059j
[2025-09-02 09:05:45] [Iter 1137/2250] R3[86/1200], Temp: 0.9874, Energy: -85.082565+0.000926j
[2025-09-02 09:06:25] [Iter 1138/2250] R3[87/1200], Temp: 0.9871, Energy: -85.132019+0.001182j
[2025-09-02 09:07:05] [Iter 1139/2250] R3[88/1200], Temp: 0.9868, Energy: -85.079943-0.000276j
[2025-09-02 09:07:46] [Iter 1140/2250] R3[89/1200], Temp: 0.9865, Energy: -85.012378-0.001829j
[2025-09-02 09:08:26] [Iter 1141/2250] R3[90/1200], Temp: 0.9862, Energy: -84.969613+0.000437j
[2025-09-02 09:09:06] [Iter 1142/2250] R3[91/1200], Temp: 0.9859, Energy: -84.997380-0.000348j
[2025-09-02 09:09:47] [Iter 1143/2250] R3[92/1200], Temp: 0.9856, Energy: -85.085371+0.001646j
[2025-09-02 09:10:27] [Iter 1144/2250] R3[93/1200], Temp: 0.9853, Energy: -85.032914+0.001603j
[2025-09-02 09:11:07] [Iter 1145/2250] R3[94/1200], Temp: 0.9849, Energy: -84.931678+0.000034j
[2025-09-02 09:11:47] [Iter 1146/2250] R3[95/1200], Temp: 0.9846, Energy: -85.068160-0.003204j
[2025-09-02 09:12:28] [Iter 1147/2250] R3[96/1200], Temp: 0.9843, Energy: -85.024998-0.003091j
[2025-09-02 09:13:08] [Iter 1148/2250] R3[97/1200], Temp: 0.9840, Energy: -85.019372+0.003404j
[2025-09-02 09:13:48] [Iter 1149/2250] R3[98/1200], Temp: 0.9836, Energy: -85.057646+0.004760j
[2025-09-02 09:14:29] [Iter 1150/2250] R3[99/1200], Temp: 0.9833, Energy: -85.019520+0.001661j
[2025-09-02 09:15:09] [Iter 1151/2250] R3[100/1200], Temp: 0.9830, Energy: -84.982777+0.001079j
[2025-09-02 09:15:49] [Iter 1152/2250] R3[101/1200], Temp: 0.9826, Energy: -85.002429-0.002426j
[2025-09-02 09:16:29] [Iter 1153/2250] R3[102/1200], Temp: 0.9823, Energy: -85.064555-0.000061j
[2025-09-02 09:17:10] [Iter 1154/2250] R3[103/1200], Temp: 0.9819, Energy: -85.043465+0.003297j
[2025-09-02 09:17:50] [Iter 1155/2250] R3[104/1200], Temp: 0.9816, Energy: -85.000483+0.001214j
[2025-09-02 09:18:30] [Iter 1156/2250] R3[105/1200], Temp: 0.9812, Energy: -85.030637-0.000506j
[2025-09-02 09:19:10] [Iter 1157/2250] R3[106/1200], Temp: 0.9809, Energy: -84.991483-0.001264j
[2025-09-02 09:19:51] [Iter 1158/2250] R3[107/1200], Temp: 0.9805, Energy: -84.996011-0.000910j
[2025-09-02 09:20:31] [Iter 1159/2250] R3[108/1200], Temp: 0.9801, Energy: -85.055856-0.000738j
[2025-09-02 09:21:11] [Iter 1160/2250] R3[109/1200], Temp: 0.9798, Energy: -84.932279-0.003404j
[2025-09-02 09:21:51] [Iter 1161/2250] R3[110/1200], Temp: 0.9794, Energy: -84.973408-0.002873j
[2025-09-02 09:22:32] [Iter 1162/2250] R3[111/1200], Temp: 0.9790, Energy: -84.982052+0.002863j
[2025-09-02 09:23:12] [Iter 1163/2250] R3[112/1200], Temp: 0.9787, Energy: -84.935777+0.003334j
[2025-09-02 09:23:52] [Iter 1164/2250] R3[113/1200], Temp: 0.9783, Energy: -84.994489-0.002576j
[2025-09-02 09:24:32] [Iter 1165/2250] R3[114/1200], Temp: 0.9779, Energy: -85.023312-0.000665j
[2025-09-02 09:25:13] [Iter 1166/2250] R3[115/1200], Temp: 0.9775, Energy: -85.049374+0.002299j
[2025-09-02 09:25:53] [Iter 1167/2250] R3[116/1200], Temp: 0.9771, Energy: -85.023425+0.001852j
[2025-09-02 09:26:33] [Iter 1168/2250] R3[117/1200], Temp: 0.9767, Energy: -85.003639+0.000762j
[2025-09-02 09:27:13] [Iter 1169/2250] R3[118/1200], Temp: 0.9763, Energy: -85.022533-0.000718j
[2025-09-02 09:27:54] [Iter 1170/2250] R3[119/1200], Temp: 0.9759, Energy: -85.054072+0.002312j
[2025-09-02 09:28:34] [Iter 1171/2250] R3[120/1200], Temp: 0.9755, Energy: -85.021039+0.000809j
[2025-09-02 09:29:14] [Iter 1172/2250] R3[121/1200], Temp: 0.9751, Energy: -85.089852+0.000849j
[2025-09-02 09:29:54] [Iter 1173/2250] R3[122/1200], Temp: 0.9747, Energy: -85.012804-0.000876j
[2025-09-02 09:30:35] [Iter 1174/2250] R3[123/1200], Temp: 0.9743, Energy: -84.998080+0.003439j
[2025-09-02 09:31:15] [Iter 1175/2250] R3[124/1200], Temp: 0.9739, Energy: -84.961774-0.000628j
[2025-09-02 09:31:55] [Iter 1176/2250] R3[125/1200], Temp: 0.9735, Energy: -85.023115+0.002124j
[2025-09-02 09:32:35] [Iter 1177/2250] R3[126/1200], Temp: 0.9730, Energy: -85.143158-0.004284j
[2025-09-02 09:33:16] [Iter 1178/2250] R3[127/1200], Temp: 0.9726, Energy: -85.097603-0.003935j
[2025-09-02 09:33:56] [Iter 1179/2250] R3[128/1200], Temp: 0.9722, Energy: -85.133278-0.003686j
[2025-09-02 09:34:36] [Iter 1180/2250] R3[129/1200], Temp: 0.9718, Energy: -85.097478-0.000261j
[2025-09-02 09:35:17] [Iter 1181/2250] R3[130/1200], Temp: 0.9713, Energy: -85.101225-0.005369j
[2025-09-02 09:35:57] [Iter 1182/2250] R3[131/1200], Temp: 0.9709, Energy: -85.036918+0.000912j
[2025-09-02 09:36:37] [Iter 1183/2250] R3[132/1200], Temp: 0.9704, Energy: -85.041737-0.002694j
[2025-09-02 09:37:17] [Iter 1184/2250] R3[133/1200], Temp: 0.9700, Energy: -84.988045+0.000195j
[2025-09-02 09:37:58] [Iter 1185/2250] R3[134/1200], Temp: 0.9695, Energy: -85.034589+0.001169j
[2025-09-02 09:38:38] [Iter 1186/2250] R3[135/1200], Temp: 0.9691, Energy: -85.020026+0.000571j
[2025-09-02 09:39:18] [Iter 1187/2250] R3[136/1200], Temp: 0.9686, Energy: -85.088899+0.000626j
[2025-09-02 09:39:58] [Iter 1188/2250] R3[137/1200], Temp: 0.9682, Energy: -85.085181+0.000116j
[2025-09-02 09:40:39] [Iter 1189/2250] R3[138/1200], Temp: 0.9677, Energy: -85.097235+0.000476j
[2025-09-02 09:41:19] [Iter 1190/2250] R3[139/1200], Temp: 0.9673, Energy: -85.060355-0.001280j
[2025-09-02 09:41:59] [Iter 1191/2250] R3[140/1200], Temp: 0.9668, Energy: -85.070487-0.001017j
[2025-09-02 09:42:39] [Iter 1192/2250] R3[141/1200], Temp: 0.9663, Energy: -85.060947-0.000146j
[2025-09-02 09:43:20] [Iter 1193/2250] R3[142/1200], Temp: 0.9658, Energy: -85.057581-0.002819j
[2025-09-02 09:44:00] [Iter 1194/2250] R3[143/1200], Temp: 0.9654, Energy: -85.013691-0.001728j
[2025-09-02 09:44:40] [Iter 1195/2250] R3[144/1200], Temp: 0.9649, Energy: -85.000953-0.002408j
[2025-09-02 09:45:21] [Iter 1196/2250] R3[145/1200], Temp: 0.9644, Energy: -84.985891+0.001140j
[2025-09-02 09:46:01] [Iter 1197/2250] R3[146/1200], Temp: 0.9639, Energy: -85.050383-0.001076j
[2025-09-02 09:46:41] [Iter 1198/2250] R3[147/1200], Temp: 0.9634, Energy: -85.099493+0.001332j
[2025-09-02 09:47:21] [Iter 1199/2250] R3[148/1200], Temp: 0.9629, Energy: -85.039230-0.002401j
[2025-09-02 09:48:02] [Iter 1200/2250] R3[149/1200], Temp: 0.9624, Energy: -85.037949+0.002679j
[2025-09-02 09:48:42] [Iter 1201/2250] R3[150/1200], Temp: 0.9619, Energy: -84.992775+0.000220j
[2025-09-02 09:49:22] [Iter 1202/2250] R3[151/1200], Temp: 0.9614, Energy: -85.023723+0.000413j
[2025-09-02 09:50:02] [Iter 1203/2250] R3[152/1200], Temp: 0.9609, Energy: -85.033884+0.000762j
[2025-09-02 09:50:43] [Iter 1204/2250] R3[153/1200], Temp: 0.9604, Energy: -85.157927+0.003557j
[2025-09-02 09:51:23] [Iter 1205/2250] R3[154/1200], Temp: 0.9599, Energy: -85.054349-0.002365j
[2025-09-02 09:52:03] [Iter 1206/2250] R3[155/1200], Temp: 0.9594, Energy: -85.012485-0.001133j
[2025-09-02 09:52:43] [Iter 1207/2250] R3[156/1200], Temp: 0.9589, Energy: -85.001575-0.001973j
[2025-09-02 09:53:24] [Iter 1208/2250] R3[157/1200], Temp: 0.9584, Energy: -85.053766-0.001057j
[2025-09-02 09:54:04] [Iter 1209/2250] R3[158/1200], Temp: 0.9578, Energy: -85.006531+0.000439j
[2025-09-02 09:54:44] [Iter 1210/2250] R3[159/1200], Temp: 0.9573, Energy: -85.021054-0.002224j
[2025-09-02 09:55:25] [Iter 1211/2250] R3[160/1200], Temp: 0.9568, Energy: -85.037128-0.001976j
[2025-09-02 09:56:05] [Iter 1212/2250] R3[161/1200], Temp: 0.9562, Energy: -84.967965-0.002905j
[2025-09-02 09:56:45] [Iter 1213/2250] R3[162/1200], Temp: 0.9557, Energy: -84.999684+0.001985j
[2025-09-02 09:57:25] [Iter 1214/2250] R3[163/1200], Temp: 0.9552, Energy: -85.006630-0.000210j
[2025-09-02 09:58:06] [Iter 1215/2250] R3[164/1200], Temp: 0.9546, Energy: -84.986109+0.001936j
[2025-09-02 09:58:46] [Iter 1216/2250] R3[165/1200], Temp: 0.9541, Energy: -85.013031-0.002500j
[2025-09-02 09:59:26] [Iter 1217/2250] R3[166/1200], Temp: 0.9535, Energy: -84.949549+0.000017j
[2025-09-02 10:00:06] [Iter 1218/2250] R3[167/1200], Temp: 0.9530, Energy: -84.944007+0.000294j
[2025-09-02 10:00:47] [Iter 1219/2250] R3[168/1200], Temp: 0.9524, Energy: -85.005742+0.002725j
[2025-09-02 10:01:27] [Iter 1220/2250] R3[169/1200], Temp: 0.9519, Energy: -84.966293+0.002470j
[2025-09-02 10:02:07] [Iter 1221/2250] R3[170/1200], Temp: 0.9513, Energy: -84.992859-0.000823j
[2025-09-02 10:02:48] [Iter 1222/2250] R3[171/1200], Temp: 0.9507, Energy: -85.066847-0.000280j
[2025-09-02 10:03:28] [Iter 1223/2250] R3[172/1200], Temp: 0.9502, Energy: -85.023506+0.003087j
[2025-09-02 10:04:08] [Iter 1224/2250] R3[173/1200], Temp: 0.9496, Energy: -84.999935-0.000701j
[2025-09-02 10:04:48] [Iter 1225/2250] R3[174/1200], Temp: 0.9490, Energy: -84.907182-0.003572j
[2025-09-02 10:05:29] [Iter 1226/2250] R3[175/1200], Temp: 0.9484, Energy: -84.951777-0.004105j
[2025-09-02 10:06:09] [Iter 1227/2250] R3[176/1200], Temp: 0.9479, Energy: -84.978154-0.000780j
[2025-09-02 10:06:49] [Iter 1228/2250] R3[177/1200], Temp: 0.9473, Energy: -85.009600+0.001932j
[2025-09-02 10:07:29] [Iter 1229/2250] R3[178/1200], Temp: 0.9467, Energy: -85.013987-0.004444j
[2025-09-02 10:08:10] [Iter 1230/2250] R3[179/1200], Temp: 0.9461, Energy: -85.116456+0.000558j
[2025-09-02 10:08:50] [Iter 1231/2250] R3[180/1200], Temp: 0.9455, Energy: -85.078437-0.002648j
[2025-09-02 10:09:30] [Iter 1232/2250] R3[181/1200], Temp: 0.9449, Energy: -85.032339+0.002169j
[2025-09-02 10:10:11] [Iter 1233/2250] R3[182/1200], Temp: 0.9443, Energy: -85.141896-0.000489j
[2025-09-02 10:10:51] [Iter 1234/2250] R3[183/1200], Temp: 0.9437, Energy: -85.014853-0.000373j
[2025-09-02 10:11:31] [Iter 1235/2250] R3[184/1200], Temp: 0.9431, Energy: -84.992895-0.002011j
[2025-09-02 10:12:11] [Iter 1236/2250] R3[185/1200], Temp: 0.9425, Energy: -84.923101-0.003380j
[2025-09-02 10:12:52] [Iter 1237/2250] R3[186/1200], Temp: 0.9419, Energy: -84.958229+0.001394j
[2025-09-02 10:13:32] [Iter 1238/2250] R3[187/1200], Temp: 0.9413, Energy: -84.940308-0.002599j
[2025-09-02 10:14:12] [Iter 1239/2250] R3[188/1200], Temp: 0.9407, Energy: -84.966476-0.000660j
[2025-09-02 10:14:52] [Iter 1240/2250] R3[189/1200], Temp: 0.9400, Energy: -85.041147+0.000090j
[2025-09-02 10:15:33] [Iter 1241/2250] R3[190/1200], Temp: 0.9394, Energy: -85.039965+0.000071j
[2025-09-02 10:16:13] [Iter 1242/2250] R3[191/1200], Temp: 0.9388, Energy: -85.077847+0.001316j
[2025-09-02 10:16:53] [Iter 1243/2250] R3[192/1200], Temp: 0.9382, Energy: -84.956383+0.002528j
[2025-09-02 10:17:34] [Iter 1244/2250] R3[193/1200], Temp: 0.9375, Energy: -85.028897+0.003939j
[2025-09-02 10:18:14] [Iter 1245/2250] R3[194/1200], Temp: 0.9369, Energy: -85.053687-0.002851j
[2025-09-02 10:18:54] [Iter 1246/2250] R3[195/1200], Temp: 0.9362, Energy: -85.011163+0.001490j
[2025-09-02 10:19:34] [Iter 1247/2250] R3[196/1200], Temp: 0.9356, Energy: -85.003329-0.000539j
[2025-09-02 10:20:15] [Iter 1248/2250] R3[197/1200], Temp: 0.9350, Energy: -84.988065+0.001052j
[2025-09-02 10:20:55] [Iter 1249/2250] R3[198/1200], Temp: 0.9343, Energy: -85.073423-0.001379j
[2025-09-02 10:21:35] [Iter 1250/2250] R3[199/1200], Temp: 0.9337, Energy: -85.028770-0.002382j
[2025-09-02 10:21:35] ✓ Checkpoint saved: checkpoint_iter_001250.pkl
[2025-09-02 10:22:16] [Iter 1251/2250] R3[200/1200], Temp: 0.9330, Energy: -85.103210+0.000866j
[2025-09-02 10:22:56] [Iter 1252/2250] R3[201/1200], Temp: 0.9324, Energy: -85.039391-0.000232j
[2025-09-02 10:23:36] [Iter 1253/2250] R3[202/1200], Temp: 0.9317, Energy: -84.961486-0.000757j
[2025-09-02 10:24:16] [Iter 1254/2250] R3[203/1200], Temp: 0.9310, Energy: -85.031424-0.001035j
[2025-09-02 10:24:57] [Iter 1255/2250] R3[204/1200], Temp: 0.9304, Energy: -84.995323-0.001518j
[2025-09-02 10:25:37] [Iter 1256/2250] R3[205/1200], Temp: 0.9297, Energy: -84.930938+0.001051j
[2025-09-02 10:26:17] [Iter 1257/2250] R3[206/1200], Temp: 0.9290, Energy: -84.884183+0.000741j
[2025-09-02 10:26:58] [Iter 1258/2250] R3[207/1200], Temp: 0.9284, Energy: -84.941924-0.001948j
[2025-09-02 10:27:38] [Iter 1259/2250] R3[208/1200], Temp: 0.9277, Energy: -84.900634-0.000647j
[2025-09-02 10:28:18] [Iter 1260/2250] R3[209/1200], Temp: 0.9270, Energy: -84.910887-0.002571j
[2025-09-02 10:28:58] [Iter 1261/2250] R3[210/1200], Temp: 0.9263, Energy: -84.979931+0.000371j
[2025-09-02 10:29:39] [Iter 1262/2250] R3[211/1200], Temp: 0.9256, Energy: -85.016688-0.002824j
[2025-09-02 10:30:19] [Iter 1263/2250] R3[212/1200], Temp: 0.9249, Energy: -85.040609+0.000451j
[2025-09-02 10:30:59] [Iter 1264/2250] R3[213/1200], Temp: 0.9243, Energy: -85.023920+0.002088j
[2025-09-02 10:31:40] [Iter 1265/2250] R3[214/1200], Temp: 0.9236, Energy: -84.972468+0.001722j
[2025-09-02 10:32:20] [Iter 1266/2250] R3[215/1200], Temp: 0.9229, Energy: -84.975206-0.000900j
[2025-09-02 10:33:00] [Iter 1267/2250] R3[216/1200], Temp: 0.9222, Energy: -84.977284+0.001207j
[2025-09-02 10:33:40] [Iter 1268/2250] R3[217/1200], Temp: 0.9215, Energy: -85.027197+0.000032j
[2025-09-02 10:34:21] [Iter 1269/2250] R3[218/1200], Temp: 0.9208, Energy: -84.946694+0.001410j
[2025-09-02 10:35:01] [Iter 1270/2250] R3[219/1200], Temp: 0.9200, Energy: -84.928721+0.001541j
[2025-09-02 10:35:41] [Iter 1271/2250] R3[220/1200], Temp: 0.9193, Energy: -84.953827-0.001619j
[2025-09-02 10:36:22] [Iter 1272/2250] R3[221/1200], Temp: 0.9186, Energy: -85.072808+0.003622j
[2025-09-02 10:37:02] [Iter 1273/2250] R3[222/1200], Temp: 0.9179, Energy: -85.013900+0.001117j
[2025-09-02 10:37:42] [Iter 1274/2250] R3[223/1200], Temp: 0.9172, Energy: -85.009552-0.001353j
[2025-09-02 10:38:22] [Iter 1275/2250] R3[224/1200], Temp: 0.9165, Energy: -85.054378+0.002667j
[2025-09-02 10:39:03] [Iter 1276/2250] R3[225/1200], Temp: 0.9157, Energy: -85.002476+0.000753j
[2025-09-02 10:39:43] [Iter 1277/2250] R3[226/1200], Temp: 0.9150, Energy: -85.057434+0.003076j
[2025-09-02 10:40:23] [Iter 1278/2250] R3[227/1200], Temp: 0.9143, Energy: -85.042586-0.003689j
[2025-09-02 10:41:04] [Iter 1279/2250] R3[228/1200], Temp: 0.9135, Energy: -84.970337-0.000115j
[2025-09-02 10:41:44] [Iter 1280/2250] R3[229/1200], Temp: 0.9128, Energy: -85.067441+0.000982j
[2025-09-02 10:42:24] [Iter 1281/2250] R3[230/1200], Temp: 0.9121, Energy: -85.057780+0.001235j
[2025-09-02 10:43:04] [Iter 1282/2250] R3[231/1200], Temp: 0.9113, Energy: -85.075371+0.003296j
[2025-09-02 10:43:45] [Iter 1283/2250] R3[232/1200], Temp: 0.9106, Energy: -85.144997+0.000097j
[2025-09-02 10:44:25] [Iter 1284/2250] R3[233/1200], Temp: 0.9098, Energy: -85.071759+0.002638j
[2025-09-02 10:45:05] [Iter 1285/2250] R3[234/1200], Temp: 0.9091, Energy: -85.088012+0.001727j
[2025-09-02 10:45:45] [Iter 1286/2250] R3[235/1200], Temp: 0.9083, Energy: -84.994815-0.002679j
[2025-09-02 10:46:26] [Iter 1287/2250] R3[236/1200], Temp: 0.9076, Energy: -85.013593+0.000056j
[2025-09-02 10:47:06] [Iter 1288/2250] R3[237/1200], Temp: 0.9068, Energy: -85.040491-0.003940j
[2025-09-02 10:47:46] [Iter 1289/2250] R3[238/1200], Temp: 0.9060, Energy: -85.047177+0.003951j
[2025-09-02 10:48:27] [Iter 1290/2250] R3[239/1200], Temp: 0.9053, Energy: -85.070715+0.000701j
[2025-09-02 10:49:07] [Iter 1291/2250] R3[240/1200], Temp: 0.9045, Energy: -85.013125+0.000717j
[2025-09-02 10:49:47] [Iter 1292/2250] R3[241/1200], Temp: 0.9037, Energy: -85.044432-0.000720j
[2025-09-02 10:50:27] [Iter 1293/2250] R3[242/1200], Temp: 0.9030, Energy: -84.979415+0.000546j
[2025-09-02 10:51:08] [Iter 1294/2250] R3[243/1200], Temp: 0.9022, Energy: -84.997041-0.000494j
[2025-09-02 10:51:48] [Iter 1295/2250] R3[244/1200], Temp: 0.9014, Energy: -85.001953+0.001061j
[2025-09-02 10:52:28] [Iter 1296/2250] R3[245/1200], Temp: 0.9006, Energy: -85.065963-0.000969j
[2025-09-02 10:53:08] [Iter 1297/2250] R3[246/1200], Temp: 0.8998, Energy: -85.021428+0.004025j
[2025-09-02 10:53:49] [Iter 1298/2250] R3[247/1200], Temp: 0.8991, Energy: -84.947680-0.002512j
[2025-09-02 10:54:29] [Iter 1299/2250] R3[248/1200], Temp: 0.8983, Energy: -84.968992-0.000258j
[2025-09-02 10:55:09] [Iter 1300/2250] R3[249/1200], Temp: 0.8975, Energy: -84.991465-0.001275j
[2025-09-02 10:55:50] [Iter 1301/2250] R3[250/1200], Temp: 0.8967, Energy: -85.049076-0.002426j
[2025-09-02 10:56:30] [Iter 1302/2250] R3[251/1200], Temp: 0.8959, Energy: -85.070334-0.002970j
[2025-09-02 10:57:10] [Iter 1303/2250] R3[252/1200], Temp: 0.8951, Energy: -85.046667+0.001439j
[2025-09-02 10:57:51] [Iter 1304/2250] R3[253/1200], Temp: 0.8943, Energy: -85.030218-0.000106j
[2025-09-02 10:58:31] [Iter 1305/2250] R3[254/1200], Temp: 0.8935, Energy: -85.070802+0.003507j
[2025-09-02 10:59:11] [Iter 1306/2250] R3[255/1200], Temp: 0.8927, Energy: -85.051295-0.001952j
[2025-09-02 10:59:52] [Iter 1307/2250] R3[256/1200], Temp: 0.8918, Energy: -85.013229-0.000504j
[2025-09-02 11:00:32] [Iter 1308/2250] R3[257/1200], Temp: 0.8910, Energy: -85.080562-0.001671j
[2025-09-02 11:01:12] [Iter 1309/2250] R3[258/1200], Temp: 0.8902, Energy: -85.088129-0.002544j
[2025-09-02 11:01:53] [Iter 1310/2250] R3[259/1200], Temp: 0.8894, Energy: -85.050283-0.000653j
[2025-09-02 11:02:33] [Iter 1311/2250] R3[260/1200], Temp: 0.8886, Energy: -85.046782+0.001334j
[2025-09-02 11:03:14] [Iter 1312/2250] R3[261/1200], Temp: 0.8877, Energy: -85.069257+0.003440j
[2025-09-02 11:03:54] [Iter 1313/2250] R3[262/1200], Temp: 0.8869, Energy: -85.024818+0.000282j
[2025-09-02 11:04:34] [Iter 1314/2250] R3[263/1200], Temp: 0.8861, Energy: -85.096270+0.000948j
[2025-09-02 11:05:15] [Iter 1315/2250] R3[264/1200], Temp: 0.8853, Energy: -85.121321-0.002298j
[2025-09-02 11:05:55] [Iter 1316/2250] R3[265/1200], Temp: 0.8844, Energy: -85.098083+0.000215j
[2025-09-02 11:06:35] [Iter 1317/2250] R3[266/1200], Temp: 0.8836, Energy: -85.102957-0.000724j
[2025-09-02 11:07:16] [Iter 1318/2250] R3[267/1200], Temp: 0.8827, Energy: -85.010887-0.003172j
[2025-09-02 11:07:56] [Iter 1319/2250] R3[268/1200], Temp: 0.8819, Energy: -84.928275-0.002812j
[2025-09-02 11:08:36] [Iter 1320/2250] R3[269/1200], Temp: 0.8811, Energy: -84.965334-0.000870j
[2025-09-02 11:09:17] [Iter 1321/2250] R3[270/1200], Temp: 0.8802, Energy: -84.909337-0.001540j
[2025-09-02 11:09:57] [Iter 1322/2250] R3[271/1200], Temp: 0.8794, Energy: -84.990874-0.000388j
[2025-09-02 11:10:37] [Iter 1323/2250] R3[272/1200], Temp: 0.8785, Energy: -85.013352+0.000879j
[2025-09-02 11:11:18] [Iter 1324/2250] R3[273/1200], Temp: 0.8776, Energy: -84.990166-0.002129j
[2025-09-02 11:11:58] [Iter 1325/2250] R3[274/1200], Temp: 0.8768, Energy: -85.016602+0.002780j
[2025-09-02 11:12:38] [Iter 1326/2250] R3[275/1200], Temp: 0.8759, Energy: -85.017730-0.002732j
[2025-09-02 11:13:19] [Iter 1327/2250] R3[276/1200], Temp: 0.8751, Energy: -85.085903-0.000947j
[2025-09-02 11:13:59] [Iter 1328/2250] R3[277/1200], Temp: 0.8742, Energy: -85.076116+0.000403j
[2025-09-02 11:14:39] [Iter 1329/2250] R3[278/1200], Temp: 0.8733, Energy: -85.055583+0.001829j
[2025-09-02 11:15:20] [Iter 1330/2250] R3[279/1200], Temp: 0.8724, Energy: -85.036392+0.002822j
[2025-09-02 11:16:00] [Iter 1331/2250] R3[280/1200], Temp: 0.8716, Energy: -85.008540+0.003554j
[2025-09-02 11:16:40] [Iter 1332/2250] R3[281/1200], Temp: 0.8707, Energy: -85.078580+0.000717j
[2025-09-02 11:17:21] [Iter 1333/2250] R3[282/1200], Temp: 0.8698, Energy: -85.082208-0.000848j
[2025-09-02 11:18:01] [Iter 1334/2250] R3[283/1200], Temp: 0.8689, Energy: -85.076435-0.002001j
[2025-09-02 11:18:41] [Iter 1335/2250] R3[284/1200], Temp: 0.8680, Energy: -85.112416-0.000630j
[2025-09-02 11:19:22] [Iter 1336/2250] R3[285/1200], Temp: 0.8672, Energy: -85.004871-0.002654j
[2025-09-02 11:20:02] [Iter 1337/2250] R3[286/1200], Temp: 0.8663, Energy: -85.036392-0.001740j
[2025-09-02 11:20:42] [Iter 1338/2250] R3[287/1200], Temp: 0.8654, Energy: -85.036328-0.001926j
[2025-09-02 11:21:22] [Iter 1339/2250] R3[288/1200], Temp: 0.8645, Energy: -84.996780+0.002343j
[2025-09-02 11:22:03] [Iter 1340/2250] R3[289/1200], Temp: 0.8636, Energy: -85.003033-0.000502j
[2025-09-02 11:22:43] [Iter 1341/2250] R3[290/1200], Temp: 0.8627, Energy: -84.988374+0.000476j
[2025-09-02 11:23:23] [Iter 1342/2250] R3[291/1200], Temp: 0.8618, Energy: -85.062952+0.002181j
[2025-09-02 11:24:04] [Iter 1343/2250] R3[292/1200], Temp: 0.8609, Energy: -85.020014+0.001782j
[2025-09-02 11:24:44] [Iter 1344/2250] R3[293/1200], Temp: 0.8600, Energy: -84.972267-0.002633j
[2025-09-02 11:25:24] [Iter 1345/2250] R3[294/1200], Temp: 0.8591, Energy: -84.995044+0.003727j
[2025-09-02 11:26:05] [Iter 1346/2250] R3[295/1200], Temp: 0.8582, Energy: -84.956607+0.001723j
[2025-09-02 11:26:45] [Iter 1347/2250] R3[296/1200], Temp: 0.8572, Energy: -85.008808+0.001212j
[2025-09-02 11:27:25] [Iter 1348/2250] R3[297/1200], Temp: 0.8563, Energy: -85.017840+0.000968j
[2025-09-02 11:28:05] [Iter 1349/2250] R3[298/1200], Temp: 0.8554, Energy: -84.979931+0.000266j
[2025-09-02 11:28:46] [Iter 1350/2250] R3[299/1200], Temp: 0.8545, Energy: -85.074452+0.000155j
[2025-09-02 11:29:26] [Iter 1351/2250] R3[300/1200], Temp: 0.8536, Energy: -85.006181+0.002025j
[2025-09-02 11:30:06] [Iter 1352/2250] R3[301/1200], Temp: 0.8526, Energy: -85.005520+0.003426j
[2025-09-02 11:30:47] [Iter 1353/2250] R3[302/1200], Temp: 0.8517, Energy: -85.097723+0.001415j
[2025-09-02 11:31:27] [Iter 1354/2250] R3[303/1200], Temp: 0.8508, Energy: -85.039146+0.001693j
[2025-09-02 11:32:07] [Iter 1355/2250] R3[304/1200], Temp: 0.8498, Energy: -85.043196-0.000053j
[2025-09-02 11:32:47] [Iter 1356/2250] R3[305/1200], Temp: 0.8489, Energy: -85.122180+0.006953j
[2025-09-02 11:33:28] [Iter 1357/2250] R3[306/1200], Temp: 0.8480, Energy: -85.106372+0.000468j
[2025-09-02 11:34:08] [Iter 1358/2250] R3[307/1200], Temp: 0.8470, Energy: -85.102395+0.001669j
[2025-09-02 11:34:49] [Iter 1359/2250] R3[308/1200], Temp: 0.8461, Energy: -85.128831+0.000824j
[2025-09-02 11:35:29] [Iter 1360/2250] R3[309/1200], Temp: 0.8451, Energy: -85.053147+0.001602j
[2025-09-02 11:36:10] [Iter 1361/2250] R3[310/1200], Temp: 0.8442, Energy: -85.072767+0.000175j
[2025-09-02 11:36:50] [Iter 1362/2250] R3[311/1200], Temp: 0.8432, Energy: -85.036142-0.000820j
[2025-09-02 11:37:30] [Iter 1363/2250] R3[312/1200], Temp: 0.8423, Energy: -84.914641+0.000952j
[2025-09-02 11:38:11] [Iter 1364/2250] R3[313/1200], Temp: 0.8413, Energy: -85.001644+0.000224j
[2025-09-02 11:38:51] [Iter 1365/2250] R3[314/1200], Temp: 0.8404, Energy: -85.082338+0.000742j
[2025-09-02 11:39:31] [Iter 1366/2250] R3[315/1200], Temp: 0.8394, Energy: -85.124130-0.005190j
[2025-09-02 11:40:12] [Iter 1367/2250] R3[316/1200], Temp: 0.8384, Energy: -85.078301+0.000127j
[2025-09-02 11:40:52] [Iter 1368/2250] R3[317/1200], Temp: 0.8375, Energy: -85.116983+0.001353j
[2025-09-02 11:41:32] [Iter 1369/2250] R3[318/1200], Temp: 0.8365, Energy: -85.108210+0.000499j
[2025-09-02 11:42:13] [Iter 1370/2250] R3[319/1200], Temp: 0.8355, Energy: -85.051085+0.001783j
[2025-09-02 11:42:53] [Iter 1371/2250] R3[320/1200], Temp: 0.8346, Energy: -84.995490-0.000470j
[2025-09-02 11:43:33] [Iter 1372/2250] R3[321/1200], Temp: 0.8336, Energy: -85.024845+0.002735j
[2025-09-02 11:44:14] [Iter 1373/2250] R3[322/1200], Temp: 0.8326, Energy: -85.051768+0.000095j
[2025-09-02 11:44:54] [Iter 1374/2250] R3[323/1200], Temp: 0.8316, Energy: -85.014533+0.001730j
[2025-09-02 11:45:34] [Iter 1375/2250] R3[324/1200], Temp: 0.8307, Energy: -85.055925+0.001010j
[2025-09-02 11:46:15] [Iter 1376/2250] R3[325/1200], Temp: 0.8297, Energy: -85.056830-0.000345j
[2025-09-02 11:46:55] [Iter 1377/2250] R3[326/1200], Temp: 0.8287, Energy: -85.039712+0.000379j
[2025-09-02 11:47:35] [Iter 1378/2250] R3[327/1200], Temp: 0.8277, Energy: -85.074069+0.001315j
[2025-09-02 11:48:16] [Iter 1379/2250] R3[328/1200], Temp: 0.8267, Energy: -85.099020+0.004985j
[2025-09-02 11:48:56] [Iter 1380/2250] R3[329/1200], Temp: 0.8257, Energy: -85.177681-0.003103j
[2025-09-02 11:49:37] [Iter 1381/2250] R3[330/1200], Temp: 0.8247, Energy: -85.169840-0.002311j
[2025-09-02 11:50:17] [Iter 1382/2250] R3[331/1200], Temp: 0.8237, Energy: -85.046059+0.001149j
[2025-09-02 11:50:57] [Iter 1383/2250] R3[332/1200], Temp: 0.8227, Energy: -85.074446-0.001104j
[2025-09-02 11:51:37] [Iter 1384/2250] R3[333/1200], Temp: 0.8217, Energy: -85.058022-0.002275j
[2025-09-02 11:52:18] [Iter 1385/2250] R3[334/1200], Temp: 0.8207, Energy: -85.104182-0.002145j
[2025-09-02 11:52:58] [Iter 1386/2250] R3[335/1200], Temp: 0.8197, Energy: -85.099159+0.000427j
[2025-09-02 11:53:39] [Iter 1387/2250] R3[336/1200], Temp: 0.8187, Energy: -85.001078+0.002239j
[2025-09-02 11:54:19] [Iter 1388/2250] R3[337/1200], Temp: 0.8177, Energy: -85.029872+0.000998j
[2025-09-02 11:54:59] [Iter 1389/2250] R3[338/1200], Temp: 0.8167, Energy: -85.007477+0.000821j
[2025-09-02 11:55:40] [Iter 1390/2250] R3[339/1200], Temp: 0.8157, Energy: -84.940643+0.000763j
[2025-09-02 11:56:20] [Iter 1391/2250] R3[340/1200], Temp: 0.8147, Energy: -84.948273-0.001674j
[2025-09-02 11:57:00] [Iter 1392/2250] R3[341/1200], Temp: 0.8136, Energy: -85.063809+0.000998j
[2025-09-02 11:57:41] [Iter 1393/2250] R3[342/1200], Temp: 0.8126, Energy: -85.073259+0.001008j
[2025-09-02 11:58:21] [Iter 1394/2250] R3[343/1200], Temp: 0.8116, Energy: -85.035572-0.003433j
[2025-09-02 11:59:01] [Iter 1395/2250] R3[344/1200], Temp: 0.8106, Energy: -85.058172-0.001065j
[2025-09-02 11:59:42] [Iter 1396/2250] R3[345/1200], Temp: 0.8095, Energy: -85.066074+0.000254j
[2025-09-02 12:00:22] [Iter 1397/2250] R3[346/1200], Temp: 0.8085, Energy: -85.089013-0.002798j
[2025-09-02 12:01:02] [Iter 1398/2250] R3[347/1200], Temp: 0.8075, Energy: -85.104149-0.004440j
[2025-09-02 12:01:43] [Iter 1399/2250] R3[348/1200], Temp: 0.8065, Energy: -85.091175-0.001725j
[2025-09-02 12:02:23] [Iter 1400/2250] R3[349/1200], Temp: 0.8054, Energy: -85.021310-0.002617j
[2025-09-02 12:03:04] [Iter 1401/2250] R3[350/1200], Temp: 0.8044, Energy: -85.095408-0.000256j
[2025-09-02 12:03:44] [Iter 1402/2250] R3[351/1200], Temp: 0.8033, Energy: -85.038817-0.001190j
[2025-09-02 12:04:24] [Iter 1403/2250] R3[352/1200], Temp: 0.8023, Energy: -85.000971+0.002201j
[2025-09-02 12:05:05] [Iter 1404/2250] R3[353/1200], Temp: 0.8013, Energy: -85.038018-0.000150j
[2025-09-02 12:05:45] [Iter 1405/2250] R3[354/1200], Temp: 0.8002, Energy: -85.048416-0.002101j
[2025-09-02 12:06:25] [Iter 1406/2250] R3[355/1200], Temp: 0.7992, Energy: -84.942338+0.002818j
[2025-09-02 12:07:06] [Iter 1407/2250] R3[356/1200], Temp: 0.7981, Energy: -85.079733-0.003606j
[2025-09-02 12:07:46] [Iter 1408/2250] R3[357/1200], Temp: 0.7971, Energy: -85.035713-0.000823j
[2025-09-02 12:08:26] [Iter 1409/2250] R3[358/1200], Temp: 0.7960, Energy: -84.979387-0.000107j
[2025-09-02 12:09:07] [Iter 1410/2250] R3[359/1200], Temp: 0.7950, Energy: -85.002523+0.000046j
[2025-09-02 12:09:47] [Iter 1411/2250] R3[360/1200], Temp: 0.7939, Energy: -84.953869+0.002662j
[2025-09-02 12:10:27] [Iter 1412/2250] R3[361/1200], Temp: 0.7928, Energy: -84.977132-0.001917j
[2025-09-02 12:11:08] [Iter 1413/2250] R3[362/1200], Temp: 0.7918, Energy: -85.068355-0.000432j
[2025-09-02 12:11:48] [Iter 1414/2250] R3[363/1200], Temp: 0.7907, Energy: -85.045961+0.000968j
[2025-09-02 12:12:28] [Iter 1415/2250] R3[364/1200], Temp: 0.7896, Energy: -85.052804-0.001569j
[2025-09-02 12:13:09] [Iter 1416/2250] R3[365/1200], Temp: 0.7886, Energy: -85.145152-0.010092j
[2025-09-02 12:13:49] [Iter 1417/2250] R3[366/1200], Temp: 0.7875, Energy: -85.117385-0.004636j
[2025-09-02 12:14:29] [Iter 1418/2250] R3[367/1200], Temp: 0.7864, Energy: -85.073314+0.001509j
[2025-09-02 12:15:10] [Iter 1419/2250] R3[368/1200], Temp: 0.7854, Energy: -85.029070-0.000425j
[2025-09-02 12:15:50] [Iter 1420/2250] R3[369/1200], Temp: 0.7843, Energy: -84.984344+0.001945j
[2025-09-02 12:16:31] [Iter 1421/2250] R3[370/1200], Temp: 0.7832, Energy: -85.026687-0.003926j
[2025-09-02 12:17:11] [Iter 1422/2250] R3[371/1200], Temp: 0.7821, Energy: -85.036569-0.000704j
[2025-09-02 12:17:51] [Iter 1423/2250] R3[372/1200], Temp: 0.7810, Energy: -85.057410-0.000166j
[2025-09-02 12:18:32] [Iter 1424/2250] R3[373/1200], Temp: 0.7800, Energy: -85.131421+0.000092j
[2025-09-02 12:19:12] [Iter 1425/2250] R3[374/1200], Temp: 0.7789, Energy: -85.073519-0.001202j
[2025-09-02 12:19:52] [Iter 1426/2250] R3[375/1200], Temp: 0.7778, Energy: -85.074841-0.000711j
[2025-09-02 12:20:33] [Iter 1427/2250] R3[376/1200], Temp: 0.7767, Energy: -85.138008-0.001564j
[2025-09-02 12:21:13] [Iter 1428/2250] R3[377/1200], Temp: 0.7756, Energy: -85.136761+0.001375j
[2025-09-02 12:21:53] [Iter 1429/2250] R3[378/1200], Temp: 0.7745, Energy: -85.107258+0.002387j
[2025-09-02 12:22:34] [Iter 1430/2250] R3[379/1200], Temp: 0.7734, Energy: -85.036052-0.002464j
[2025-09-02 12:23:14] [Iter 1431/2250] R3[380/1200], Temp: 0.7723, Energy: -85.005485-0.001080j
[2025-09-02 12:23:55] [Iter 1432/2250] R3[381/1200], Temp: 0.7712, Energy: -85.000418+0.003182j
[2025-09-02 12:24:35] [Iter 1433/2250] R3[382/1200], Temp: 0.7701, Energy: -85.014172+0.000413j
[2025-09-02 12:25:15] [Iter 1434/2250] R3[383/1200], Temp: 0.7690, Energy: -85.015004-0.001417j
[2025-09-02 12:25:56] [Iter 1435/2250] R3[384/1200], Temp: 0.7679, Energy: -85.036007+0.000626j
[2025-09-02 12:26:36] [Iter 1436/2250] R3[385/1200], Temp: 0.7668, Energy: -85.090281+0.001891j
[2025-09-02 12:27:16] [Iter 1437/2250] R3[386/1200], Temp: 0.7657, Energy: -85.063534-0.000013j
[2025-09-02 12:27:57] [Iter 1438/2250] R3[387/1200], Temp: 0.7646, Energy: -85.063463+0.000881j
[2025-09-02 12:28:37] [Iter 1439/2250] R3[388/1200], Temp: 0.7635, Energy: -85.101389+0.001294j
[2025-09-02 12:29:17] [Iter 1440/2250] R3[389/1200], Temp: 0.7624, Energy: -85.067966+0.003449j
[2025-09-02 12:29:58] [Iter 1441/2250] R3[390/1200], Temp: 0.7612, Energy: -85.034049-0.000171j
[2025-09-02 12:30:38] [Iter 1442/2250] R3[391/1200], Temp: 0.7601, Energy: -85.037170+0.000158j
[2025-09-02 12:31:18] [Iter 1443/2250] R3[392/1200], Temp: 0.7590, Energy: -84.990219+0.001262j
[2025-09-02 12:31:59] [Iter 1444/2250] R3[393/1200], Temp: 0.7579, Energy: -84.963345-0.000437j
[2025-09-02 12:32:39] [Iter 1445/2250] R3[394/1200], Temp: 0.7568, Energy: -85.023422-0.004584j
[2025-09-02 12:33:19] [Iter 1446/2250] R3[395/1200], Temp: 0.7556, Energy: -84.994941-0.004406j
[2025-09-02 12:34:00] [Iter 1447/2250] R3[396/1200], Temp: 0.7545, Energy: -85.021020+0.000822j
[2025-09-02 12:34:40] [Iter 1448/2250] R3[397/1200], Temp: 0.7534, Energy: -85.057373-0.001813j
[2025-09-02 12:35:21] [Iter 1449/2250] R3[398/1200], Temp: 0.7523, Energy: -84.992376+0.002533j
[2025-09-02 12:36:01] [Iter 1450/2250] R3[399/1200], Temp: 0.7511, Energy: -84.997357+0.003952j
[2025-09-02 12:36:41] [Iter 1451/2250] R3[400/1200], Temp: 0.7500, Energy: -85.011591+0.000620j
[2025-09-02 12:37:22] [Iter 1452/2250] R3[401/1200], Temp: 0.7489, Energy: -84.971921+0.000974j
[2025-09-02 12:38:02] [Iter 1453/2250] R3[402/1200], Temp: 0.7477, Energy: -85.021895+0.003167j
[2025-09-02 12:38:42] [Iter 1454/2250] R3[403/1200], Temp: 0.7466, Energy: -85.055492+0.000391j
[2025-09-02 12:39:23] [Iter 1455/2250] R3[404/1200], Temp: 0.7455, Energy: -85.063116-0.000221j
[2025-09-02 12:40:03] [Iter 1456/2250] R3[405/1200], Temp: 0.7443, Energy: -85.082538+0.000492j
[2025-09-02 12:40:43] [Iter 1457/2250] R3[406/1200], Temp: 0.7432, Energy: -85.113121-0.001261j
[2025-09-02 12:41:24] [Iter 1458/2250] R3[407/1200], Temp: 0.7420, Energy: -85.127791+0.000321j
[2025-09-02 12:42:04] [Iter 1459/2250] R3[408/1200], Temp: 0.7409, Energy: -85.070061-0.000959j
[2025-09-02 12:42:44] [Iter 1460/2250] R3[409/1200], Temp: 0.7397, Energy: -85.027033-0.002142j
[2025-09-02 12:43:25] [Iter 1461/2250] R3[410/1200], Temp: 0.7386, Energy: -85.066606-0.002064j
[2025-09-02 12:44:05] [Iter 1462/2250] R3[411/1200], Temp: 0.7374, Energy: -85.101480+0.000645j
[2025-09-02 12:44:45] [Iter 1463/2250] R3[412/1200], Temp: 0.7363, Energy: -85.128285+0.000584j
[2025-09-02 12:45:26] [Iter 1464/2250] R3[413/1200], Temp: 0.7351, Energy: -85.106726-0.003318j
[2025-09-02 12:46:06] [Iter 1465/2250] R3[414/1200], Temp: 0.7340, Energy: -85.141466+0.001367j
[2025-09-02 12:46:47] [Iter 1466/2250] R3[415/1200], Temp: 0.7328, Energy: -85.105471-0.000576j
[2025-09-02 12:47:27] [Iter 1467/2250] R3[416/1200], Temp: 0.7316, Energy: -85.144826+0.001439j
[2025-09-02 12:48:07] [Iter 1468/2250] R3[417/1200], Temp: 0.7305, Energy: -85.083693+0.001685j
[2025-09-02 12:48:48] [Iter 1469/2250] R3[418/1200], Temp: 0.7293, Energy: -85.078858+0.001591j
[2025-09-02 12:49:28] [Iter 1470/2250] R3[419/1200], Temp: 0.7282, Energy: -85.130475+0.000749j
[2025-09-02 12:50:08] [Iter 1471/2250] R3[420/1200], Temp: 0.7270, Energy: -85.072875+0.000652j
[2025-09-02 12:50:49] [Iter 1472/2250] R3[421/1200], Temp: 0.7258, Energy: -85.150210-0.002645j
[2025-09-02 12:51:29] [Iter 1473/2250] R3[422/1200], Temp: 0.7247, Energy: -85.067976+0.001193j
[2025-09-02 12:52:09] [Iter 1474/2250] R3[423/1200], Temp: 0.7235, Energy: -85.027562+0.001487j
[2025-09-02 12:52:50] [Iter 1475/2250] R3[424/1200], Temp: 0.7223, Energy: -84.929968-0.001506j
[2025-09-02 12:53:30] [Iter 1476/2250] R3[425/1200], Temp: 0.7211, Energy: -84.967794-0.001089j
[2025-09-02 12:54:11] [Iter 1477/2250] R3[426/1200], Temp: 0.7200, Energy: -85.049398+0.002731j
[2025-09-02 12:54:51] [Iter 1478/2250] R3[427/1200], Temp: 0.7188, Energy: -85.073848+0.000078j
[2025-09-02 12:55:31] [Iter 1479/2250] R3[428/1200], Temp: 0.7176, Energy: -85.117501+0.001799j
[2025-09-02 12:56:12] [Iter 1480/2250] R3[429/1200], Temp: 0.7164, Energy: -85.058392+0.000966j
[2025-09-02 12:56:52] [Iter 1481/2250] R3[430/1200], Temp: 0.7153, Energy: -85.011669-0.000444j
[2025-09-02 12:57:33] [Iter 1482/2250] R3[431/1200], Temp: 0.7141, Energy: -84.977565-0.001114j
[2025-09-02 12:58:13] [Iter 1483/2250] R3[432/1200], Temp: 0.7129, Energy: -84.956506+0.001993j
[2025-09-02 12:58:53] [Iter 1484/2250] R3[433/1200], Temp: 0.7117, Energy: -84.989632+0.002467j
[2025-09-02 12:59:33] [Iter 1485/2250] R3[434/1200], Temp: 0.7105, Energy: -84.987880-0.001900j
[2025-09-02 13:00:14] [Iter 1486/2250] R3[435/1200], Temp: 0.7093, Energy: -84.971702+0.000502j
[2025-09-02 13:00:54] [Iter 1487/2250] R3[436/1200], Temp: 0.7081, Energy: -84.947072-0.000369j
[2025-09-02 13:01:34] [Iter 1488/2250] R3[437/1200], Temp: 0.7069, Energy: -84.997099-0.001749j
[2025-09-02 13:02:15] [Iter 1489/2250] R3[438/1200], Temp: 0.7058, Energy: -85.009893+0.000482j
[2025-09-02 13:02:55] [Iter 1490/2250] R3[439/1200], Temp: 0.7046, Energy: -84.955031-0.000637j
[2025-09-02 13:03:35] [Iter 1491/2250] R3[440/1200], Temp: 0.7034, Energy: -84.959794+0.000536j
[2025-09-02 13:04:15] [Iter 1492/2250] R3[441/1200], Temp: 0.7022, Energy: -84.916844+0.002272j
[2025-09-02 13:04:56] [Iter 1493/2250] R3[442/1200], Temp: 0.7010, Energy: -84.993595-0.000394j
[2025-09-02 13:05:36] [Iter 1494/2250] R3[443/1200], Temp: 0.6998, Energy: -84.962750-0.002434j
[2025-09-02 13:06:16] [Iter 1495/2250] R3[444/1200], Temp: 0.6986, Energy: -84.919351+0.000978j
[2025-09-02 13:06:56] [Iter 1496/2250] R3[445/1200], Temp: 0.6974, Energy: -84.966008+0.000844j
[2025-09-02 13:07:37] [Iter 1497/2250] R3[446/1200], Temp: 0.6962, Energy: -84.967235+0.000780j
[2025-09-02 13:08:17] [Iter 1498/2250] R3[447/1200], Temp: 0.6950, Energy: -85.069649-0.002822j
[2025-09-02 13:08:57] [Iter 1499/2250] R3[448/1200], Temp: 0.6938, Energy: -85.131433-0.002536j
[2025-09-02 13:09:38] [Iter 1500/2250] R3[449/1200], Temp: 0.6926, Energy: -85.072085-0.000789j
[2025-09-02 13:09:38] ✓ Checkpoint saved: checkpoint_iter_001500.pkl
[2025-09-02 13:10:18] [Iter 1501/2250] R3[450/1200], Temp: 0.6913, Energy: -85.030231-0.000951j
[2025-09-02 13:10:58] [Iter 1502/2250] R3[451/1200], Temp: 0.6901, Energy: -85.020333-0.002442j
[2025-09-02 13:11:39] [Iter 1503/2250] R3[452/1200], Temp: 0.6889, Energy: -85.075923+0.003190j
[2025-09-02 13:12:19] [Iter 1504/2250] R3[453/1200], Temp: 0.6877, Energy: -85.090119+0.002007j
[2025-09-02 13:12:59] [Iter 1505/2250] R3[454/1200], Temp: 0.6865, Energy: -85.039455-0.003506j
[2025-09-02 13:13:40] [Iter 1506/2250] R3[455/1200], Temp: 0.6853, Energy: -84.945778+0.002941j
[2025-09-02 13:14:20] [Iter 1507/2250] R3[456/1200], Temp: 0.6841, Energy: -84.924303+0.000694j
[2025-09-02 13:15:00] [Iter 1508/2250] R3[457/1200], Temp: 0.6828, Energy: -84.959902+0.002416j
[2025-09-02 13:15:40] [Iter 1509/2250] R3[458/1200], Temp: 0.6816, Energy: -84.975970-0.004911j
[2025-09-02 13:16:21] [Iter 1510/2250] R3[459/1200], Temp: 0.6804, Energy: -85.018120+0.000012j
[2025-09-02 13:17:01] [Iter 1511/2250] R3[460/1200], Temp: 0.6792, Energy: -84.986090-0.002492j
[2025-09-02 13:17:41] [Iter 1512/2250] R3[461/1200], Temp: 0.6780, Energy: -85.043600+0.000136j
[2025-09-02 13:18:21] [Iter 1513/2250] R3[462/1200], Temp: 0.6767, Energy: -85.111461+0.002191j
[2025-09-02 13:19:02] [Iter 1514/2250] R3[463/1200], Temp: 0.6755, Energy: -85.090689+0.001095j
[2025-09-02 13:19:42] [Iter 1515/2250] R3[464/1200], Temp: 0.6743, Energy: -85.078918+0.003527j
[2025-09-02 13:20:22] [Iter 1516/2250] R3[465/1200], Temp: 0.6731, Energy: -85.131998-0.000498j
[2025-09-02 13:21:03] [Iter 1517/2250] R3[466/1200], Temp: 0.6718, Energy: -85.181364-0.001592j
[2025-09-02 13:21:43] [Iter 1518/2250] R3[467/1200], Temp: 0.6706, Energy: -85.166855+0.001647j
[2025-09-02 13:22:23] [Iter 1519/2250] R3[468/1200], Temp: 0.6694, Energy: -85.147512-0.003492j
[2025-09-02 13:23:03] [Iter 1520/2250] R3[469/1200], Temp: 0.6681, Energy: -85.192025+0.001123j
[2025-09-02 13:23:44] [Iter 1521/2250] R3[470/1200], Temp: 0.6669, Energy: -85.148559-0.001419j
[2025-09-02 13:24:24] [Iter 1522/2250] R3[471/1200], Temp: 0.6657, Energy: -85.087180+0.000183j
[2025-09-02 13:25:04] [Iter 1523/2250] R3[472/1200], Temp: 0.6644, Energy: -85.026363+0.001956j
[2025-09-02 13:25:45] [Iter 1524/2250] R3[473/1200], Temp: 0.6632, Energy: -84.960664-0.002411j
[2025-09-02 13:26:25] [Iter 1525/2250] R3[474/1200], Temp: 0.6620, Energy: -85.006111+0.000458j
[2025-09-02 13:27:05] [Iter 1526/2250] R3[475/1200], Temp: 0.6607, Energy: -84.998019-0.001284j
[2025-09-02 13:27:45] [Iter 1527/2250] R3[476/1200], Temp: 0.6595, Energy: -85.082817-0.000216j
[2025-09-02 13:28:26] [Iter 1528/2250] R3[477/1200], Temp: 0.6582, Energy: -85.030546+0.002225j
[2025-09-02 13:29:06] [Iter 1529/2250] R3[478/1200], Temp: 0.6570, Energy: -85.041523-0.000970j
[2025-09-02 13:29:46] [Iter 1530/2250] R3[479/1200], Temp: 0.6558, Energy: -85.065601-0.000880j
[2025-09-02 13:30:26] [Iter 1531/2250] R3[480/1200], Temp: 0.6545, Energy: -85.048831-0.002846j
[2025-09-02 13:31:07] [Iter 1532/2250] R3[481/1200], Temp: 0.6533, Energy: -85.023228-0.005865j
[2025-09-02 13:31:47] [Iter 1533/2250] R3[482/1200], Temp: 0.6520, Energy: -84.992596-0.000251j
[2025-09-02 13:32:27] [Iter 1534/2250] R3[483/1200], Temp: 0.6508, Energy: -84.995378+0.000578j
[2025-09-02 13:33:08] [Iter 1535/2250] R3[484/1200], Temp: 0.6495, Energy: -85.015712-0.002412j
[2025-09-02 13:33:48] [Iter 1536/2250] R3[485/1200], Temp: 0.6483, Energy: -85.066032-0.003606j
[2025-09-02 13:34:28] [Iter 1537/2250] R3[486/1200], Temp: 0.6470, Energy: -85.049093+0.000023j
[2025-09-02 13:35:08] [Iter 1538/2250] R3[487/1200], Temp: 0.6458, Energy: -84.982665+0.000582j
[2025-09-02 13:35:49] [Iter 1539/2250] R3[488/1200], Temp: 0.6445, Energy: -85.041350+0.002071j
[2025-09-02 13:36:29] [Iter 1540/2250] R3[489/1200], Temp: 0.6433, Energy: -85.078265-0.000106j
[2025-09-02 13:37:09] [Iter 1541/2250] R3[490/1200], Temp: 0.6420, Energy: -85.106199+0.000202j
[2025-09-02 13:37:49] [Iter 1542/2250] R3[491/1200], Temp: 0.6408, Energy: -85.143672-0.000237j
[2025-09-02 13:38:30] [Iter 1543/2250] R3[492/1200], Temp: 0.6395, Energy: -85.090198-0.000698j
[2025-09-02 13:39:10] [Iter 1544/2250] R3[493/1200], Temp: 0.6382, Energy: -85.110137+0.000738j
[2025-09-02 13:39:50] [Iter 1545/2250] R3[494/1200], Temp: 0.6370, Energy: -85.106820-0.001438j
[2025-09-02 13:40:31] [Iter 1546/2250] R3[495/1200], Temp: 0.6357, Energy: -85.118905+0.001059j
[2025-09-02 13:41:11] [Iter 1547/2250] R3[496/1200], Temp: 0.6345, Energy: -85.220834-0.000550j
[2025-09-02 13:41:51] [Iter 1548/2250] R3[497/1200], Temp: 0.6332, Energy: -85.144359-0.001996j
[2025-09-02 13:42:31] [Iter 1549/2250] R3[498/1200], Temp: 0.6319, Energy: -85.125562+0.002113j
[2025-09-02 13:43:12] [Iter 1550/2250] R3[499/1200], Temp: 0.6307, Energy: -85.125915+0.002615j
[2025-09-02 13:43:52] [Iter 1551/2250] R3[500/1200], Temp: 0.6294, Energy: -85.103092-0.000599j
[2025-09-02 13:44:32] [Iter 1552/2250] R3[501/1200], Temp: 0.6281, Energy: -85.077694-0.000012j
[2025-09-02 13:45:13] [Iter 1553/2250] R3[502/1200], Temp: 0.6269, Energy: -85.065167+0.001130j
[2025-09-02 13:45:53] [Iter 1554/2250] R3[503/1200], Temp: 0.6256, Energy: -85.052197-0.000734j
[2025-09-02 13:46:33] [Iter 1555/2250] R3[504/1200], Temp: 0.6243, Energy: -85.080868+0.000444j
[2025-09-02 13:47:13] [Iter 1556/2250] R3[505/1200], Temp: 0.6231, Energy: -85.114141+0.001338j
[2025-09-02 13:47:54] [Iter 1557/2250] R3[506/1200], Temp: 0.6218, Energy: -85.009272+0.001251j
[2025-09-02 13:48:34] [Iter 1558/2250] R3[507/1200], Temp: 0.6205, Energy: -84.988703+0.002570j
[2025-09-02 13:49:14] [Iter 1559/2250] R3[508/1200], Temp: 0.6193, Energy: -85.016044+0.002446j
[2025-09-02 13:49:54] [Iter 1560/2250] R3[509/1200], Temp: 0.6180, Energy: -85.067362-0.001884j
[2025-09-02 13:50:35] [Iter 1561/2250] R3[510/1200], Temp: 0.6167, Energy: -85.102400-0.000931j
[2025-09-02 13:51:15] [Iter 1562/2250] R3[511/1200], Temp: 0.6154, Energy: -85.081570+0.002594j
[2025-09-02 13:51:55] [Iter 1563/2250] R3[512/1200], Temp: 0.6142, Energy: -85.055547-0.001854j
[2025-09-02 13:52:35] [Iter 1564/2250] R3[513/1200], Temp: 0.6129, Energy: -85.082376+0.000895j
[2025-09-02 13:53:16] [Iter 1565/2250] R3[514/1200], Temp: 0.6116, Energy: -85.027070+0.000540j
[2025-09-02 13:53:56] [Iter 1566/2250] R3[515/1200], Temp: 0.6103, Energy: -85.020888+0.000362j
[2025-09-02 13:54:36] [Iter 1567/2250] R3[516/1200], Temp: 0.6091, Energy: -85.065180-0.002027j
[2025-09-02 13:55:16] [Iter 1568/2250] R3[517/1200], Temp: 0.6078, Energy: -84.990155-0.001331j
[2025-09-02 13:55:57] [Iter 1569/2250] R3[518/1200], Temp: 0.6065, Energy: -85.077008-0.001350j
[2025-09-02 13:56:37] [Iter 1570/2250] R3[519/1200], Temp: 0.6052, Energy: -85.143516+0.000344j
[2025-09-02 13:57:17] [Iter 1571/2250] R3[520/1200], Temp: 0.6040, Energy: -85.123452-0.003160j
[2025-09-02 13:57:57] [Iter 1572/2250] R3[521/1200], Temp: 0.6027, Energy: -85.102332+0.000025j
[2025-09-02 13:58:38] [Iter 1573/2250] R3[522/1200], Temp: 0.6014, Energy: -85.096819+0.002194j
[2025-09-02 13:59:18] [Iter 1574/2250] R3[523/1200], Temp: 0.6001, Energy: -85.012782-0.000955j
[2025-09-02 13:59:58] [Iter 1575/2250] R3[524/1200], Temp: 0.5988, Energy: -84.961489+0.002605j
[2025-09-02 14:00:39] [Iter 1576/2250] R3[525/1200], Temp: 0.5975, Energy: -85.022530-0.000648j
[2025-09-02 14:01:19] [Iter 1577/2250] R3[526/1200], Temp: 0.5963, Energy: -85.056707+0.000145j
[2025-09-02 14:01:59] [Iter 1578/2250] R3[527/1200], Temp: 0.5950, Energy: -84.984099-0.000860j
[2025-09-02 14:02:39] [Iter 1579/2250] R3[528/1200], Temp: 0.5937, Energy: -84.953846+0.000152j
[2025-09-02 14:03:20] [Iter 1580/2250] R3[529/1200], Temp: 0.5924, Energy: -85.038471+0.001782j
[2025-09-02 14:04:00] [Iter 1581/2250] R3[530/1200], Temp: 0.5911, Energy: -85.097994+0.001739j
[2025-09-02 14:04:40] [Iter 1582/2250] R3[531/1200], Temp: 0.5898, Energy: -85.073886+0.002517j
[2025-09-02 14:05:20] [Iter 1583/2250] R3[532/1200], Temp: 0.5885, Energy: -85.066255+0.003282j
[2025-09-02 14:06:01] [Iter 1584/2250] R3[533/1200], Temp: 0.5873, Energy: -84.995384-0.000998j
[2025-09-02 14:06:41] [Iter 1585/2250] R3[534/1200], Temp: 0.5860, Energy: -85.089557-0.000822j
[2025-09-02 14:07:21] [Iter 1586/2250] R3[535/1200], Temp: 0.5847, Energy: -85.173677-0.000090j
[2025-09-02 14:08:01] [Iter 1587/2250] R3[536/1200], Temp: 0.5834, Energy: -85.029607+0.004251j
[2025-09-02 14:08:42] [Iter 1588/2250] R3[537/1200], Temp: 0.5821, Energy: -84.999029+0.001150j
[2025-09-02 14:09:22] [Iter 1589/2250] R3[538/1200], Temp: 0.5808, Energy: -85.050672-0.001340j
[2025-09-02 14:10:02] [Iter 1590/2250] R3[539/1200], Temp: 0.5795, Energy: -85.001400+0.001290j
[2025-09-02 14:10:43] [Iter 1591/2250] R3[540/1200], Temp: 0.5782, Energy: -85.036445+0.000030j
[2025-09-02 14:11:23] [Iter 1592/2250] R3[541/1200], Temp: 0.5769, Energy: -84.987692-0.003173j
[2025-09-02 14:12:03] [Iter 1593/2250] R3[542/1200], Temp: 0.5756, Energy: -85.004576+0.000704j
[2025-09-02 14:12:43] [Iter 1594/2250] R3[543/1200], Temp: 0.5743, Energy: -85.103509-0.000711j
[2025-09-02 14:13:24] [Iter 1595/2250] R3[544/1200], Temp: 0.5730, Energy: -85.012753-0.002553j
[2025-09-02 14:14:04] [Iter 1596/2250] R3[545/1200], Temp: 0.5717, Energy: -85.009332-0.000848j
[2025-09-02 14:14:44] [Iter 1597/2250] R3[546/1200], Temp: 0.5705, Energy: -85.094910+0.001004j
[2025-09-02 14:15:24] [Iter 1598/2250] R3[547/1200], Temp: 0.5692, Energy: -85.089015-0.001783j
[2025-09-02 14:16:05] [Iter 1599/2250] R3[548/1200], Temp: 0.5679, Energy: -84.990098-0.001675j
[2025-09-02 14:16:45] [Iter 1600/2250] R3[549/1200], Temp: 0.5666, Energy: -85.031497+0.002652j
[2025-09-02 14:17:25] [Iter 1601/2250] R3[550/1200], Temp: 0.5653, Energy: -85.028429+0.001222j
[2025-09-02 14:18:05] [Iter 1602/2250] R3[551/1200], Temp: 0.5640, Energy: -84.972383+0.000694j
[2025-09-02 14:18:46] [Iter 1603/2250] R3[552/1200], Temp: 0.5627, Energy: -85.059158+0.001252j
[2025-09-02 14:19:26] [Iter 1604/2250] R3[553/1200], Temp: 0.5614, Energy: -84.998565+0.001359j
[2025-09-02 14:20:06] [Iter 1605/2250] R3[554/1200], Temp: 0.5601, Energy: -84.993857-0.000278j
[2025-09-02 14:20:46] [Iter 1606/2250] R3[555/1200], Temp: 0.5588, Energy: -85.009950-0.003265j
[2025-09-02 14:21:27] [Iter 1607/2250] R3[556/1200], Temp: 0.5575, Energy: -85.008465-0.001156j
[2025-09-02 14:22:07] [Iter 1608/2250] R3[557/1200], Temp: 0.5562, Energy: -84.951540+0.001369j
[2025-09-02 14:22:47] [Iter 1609/2250] R3[558/1200], Temp: 0.5549, Energy: -85.025737-0.002425j
[2025-09-02 14:23:27] [Iter 1610/2250] R3[559/1200], Temp: 0.5536, Energy: -85.008442+0.000961j
[2025-09-02 14:24:08] [Iter 1611/2250] R3[560/1200], Temp: 0.5523, Energy: -84.997090-0.003093j
[2025-09-02 14:24:48] [Iter 1612/2250] R3[561/1200], Temp: 0.5510, Energy: -85.107863+0.001262j
[2025-09-02 14:25:28] [Iter 1613/2250] R3[562/1200], Temp: 0.5497, Energy: -85.081859+0.002076j
[2025-09-02 14:26:08] [Iter 1614/2250] R3[563/1200], Temp: 0.5484, Energy: -85.100849-0.002774j
[2025-09-02 14:26:49] [Iter 1615/2250] R3[564/1200], Temp: 0.5471, Energy: -85.039070-0.001539j
[2025-09-02 14:27:29] [Iter 1616/2250] R3[565/1200], Temp: 0.5458, Energy: -85.062757-0.002409j
[2025-09-02 14:28:09] [Iter 1617/2250] R3[566/1200], Temp: 0.5444, Energy: -85.036498+0.000893j
[2025-09-02 14:28:49] [Iter 1618/2250] R3[567/1200], Temp: 0.5431, Energy: -85.083147-0.000374j
[2025-09-02 14:29:30] [Iter 1619/2250] R3[568/1200], Temp: 0.5418, Energy: -85.057029+0.000149j
[2025-09-02 14:30:10] [Iter 1620/2250] R3[569/1200], Temp: 0.5405, Energy: -85.049598-0.001415j
[2025-09-02 14:30:50] [Iter 1621/2250] R3[570/1200], Temp: 0.5392, Energy: -85.162767-0.003443j
[2025-09-02 14:31:30] [Iter 1622/2250] R3[571/1200], Temp: 0.5379, Energy: -85.148669-0.002554j
[2025-09-02 14:32:11] [Iter 1623/2250] R3[572/1200], Temp: 0.5366, Energy: -85.132501-0.002742j
[2025-09-02 14:32:51] [Iter 1624/2250] R3[573/1200], Temp: 0.5353, Energy: -85.120475+0.000027j
[2025-09-02 14:33:31] [Iter 1625/2250] R3[574/1200], Temp: 0.5340, Energy: -85.178046-0.004446j
[2025-09-02 14:34:11] [Iter 1626/2250] R3[575/1200], Temp: 0.5327, Energy: -85.143729+0.001599j
[2025-09-02 14:34:52] [Iter 1627/2250] R3[576/1200], Temp: 0.5314, Energy: -85.154890+0.003562j
[2025-09-02 14:35:32] [Iter 1628/2250] R3[577/1200], Temp: 0.5301, Energy: -85.201145+0.002873j
[2025-09-02 14:36:12] [Iter 1629/2250] R3[578/1200], Temp: 0.5288, Energy: -85.169257-0.002312j
[2025-09-02 14:36:52] [Iter 1630/2250] R3[579/1200], Temp: 0.5275, Energy: -85.104843-0.003224j
[2025-09-02 14:37:33] [Iter 1631/2250] R3[580/1200], Temp: 0.5262, Energy: -85.086086+0.000104j
[2025-09-02 14:38:13] [Iter 1632/2250] R3[581/1200], Temp: 0.5249, Energy: -85.094009-0.001624j
[2025-09-02 14:38:53] [Iter 1633/2250] R3[582/1200], Temp: 0.5236, Energy: -85.087774+0.000832j
[2025-09-02 14:39:33] [Iter 1634/2250] R3[583/1200], Temp: 0.5222, Energy: -85.104318+0.000501j
[2025-09-02 14:40:14] [Iter 1635/2250] R3[584/1200], Temp: 0.5209, Energy: -85.166673+0.002627j
[2025-09-02 14:40:54] [Iter 1636/2250] R3[585/1200], Temp: 0.5196, Energy: -85.153734+0.000698j
[2025-09-02 14:41:34] [Iter 1637/2250] R3[586/1200], Temp: 0.5183, Energy: -85.193330-0.000305j
[2025-09-02 14:42:14] [Iter 1638/2250] R3[587/1200], Temp: 0.5170, Energy: -85.131918-0.000126j
[2025-09-02 14:42:55] [Iter 1639/2250] R3[588/1200], Temp: 0.5157, Energy: -85.191351+0.001316j
[2025-09-02 14:43:35] [Iter 1640/2250] R3[589/1200], Temp: 0.5144, Energy: -85.154431+0.001669j
[2025-09-02 14:44:15] [Iter 1641/2250] R3[590/1200], Temp: 0.5131, Energy: -85.159184+0.000502j
[2025-09-02 14:44:55] [Iter 1642/2250] R3[591/1200], Temp: 0.5118, Energy: -85.131766-0.000076j
[2025-09-02 14:45:36] [Iter 1643/2250] R3[592/1200], Temp: 0.5105, Energy: -85.071329+0.000193j
[2025-09-02 14:46:16] [Iter 1644/2250] R3[593/1200], Temp: 0.5092, Energy: -85.046357-0.002654j
[2025-09-02 14:46:56] [Iter 1645/2250] R3[594/1200], Temp: 0.5079, Energy: -85.068974+0.001164j
[2025-09-02 14:47:36] [Iter 1646/2250] R3[595/1200], Temp: 0.5065, Energy: -85.031556-0.001533j
[2025-09-02 14:48:17] [Iter 1647/2250] R3[596/1200], Temp: 0.5052, Energy: -85.000806+0.001533j
[2025-09-02 14:48:57] [Iter 1648/2250] R3[597/1200], Temp: 0.5039, Energy: -85.102481-0.001097j
[2025-09-02 14:49:37] [Iter 1649/2250] R3[598/1200], Temp: 0.5026, Energy: -85.144094-0.000739j
[2025-09-02 14:50:18] [Iter 1650/2250] R3[599/1200], Temp: 0.5013, Energy: -85.078588+0.002529j
[2025-09-02 14:50:58] [Iter 1651/2250] R3[600/1200], Temp: 0.5000, Energy: -85.123160+0.002939j
[2025-09-02 14:51:38] [Iter 1652/2250] R3[601/1200], Temp: 0.4987, Energy: -85.092115+0.000803j
[2025-09-02 14:52:18] [Iter 1653/2250] R3[602/1200], Temp: 0.4974, Energy: -85.128649+0.000645j
[2025-09-02 14:52:59] [Iter 1654/2250] R3[603/1200], Temp: 0.4961, Energy: -85.046048+0.002277j
[2025-09-02 14:53:39] [Iter 1655/2250] R3[604/1200], Temp: 0.4948, Energy: -85.098562+0.014170j
[2025-09-02 14:54:19] [Iter 1656/2250] R3[605/1200], Temp: 0.4935, Energy: -85.032946-0.001748j
[2025-09-02 14:55:00] [Iter 1657/2250] R3[606/1200], Temp: 0.4921, Energy: -85.062491+0.002009j
[2025-09-02 14:55:40] [Iter 1658/2250] R3[607/1200], Temp: 0.4908, Energy: -85.051046+0.002063j
[2025-09-02 14:56:20] [Iter 1659/2250] R3[608/1200], Temp: 0.4895, Energy: -85.034761+0.000432j
[2025-09-02 14:57:00] [Iter 1660/2250] R3[609/1200], Temp: 0.4882, Energy: -85.076424+0.000678j
[2025-09-02 14:57:41] [Iter 1661/2250] R3[610/1200], Temp: 0.4869, Energy: -85.071160+0.000050j
[2025-09-02 14:58:21] [Iter 1662/2250] R3[611/1200], Temp: 0.4856, Energy: -85.042328+0.000099j
[2025-09-02 14:59:01] [Iter 1663/2250] R3[612/1200], Temp: 0.4843, Energy: -84.954487+0.002065j
[2025-09-02 14:59:41] [Iter 1664/2250] R3[613/1200], Temp: 0.4830, Energy: -85.032744+0.002698j
[2025-09-02 15:00:22] [Iter 1665/2250] R3[614/1200], Temp: 0.4817, Energy: -85.046760-0.001327j
[2025-09-02 15:01:02] [Iter 1666/2250] R3[615/1200], Temp: 0.4804, Energy: -85.060030-0.001139j
[2025-09-02 15:01:42] [Iter 1667/2250] R3[616/1200], Temp: 0.4791, Energy: -84.986313+0.000768j
[2025-09-02 15:02:22] [Iter 1668/2250] R3[617/1200], Temp: 0.4778, Energy: -85.064777+0.001020j
[2025-09-02 15:03:03] [Iter 1669/2250] R3[618/1200], Temp: 0.4764, Energy: -85.117875-0.000524j
[2025-09-02 15:03:43] [Iter 1670/2250] R3[619/1200], Temp: 0.4751, Energy: -85.105333+0.000170j
[2025-09-02 15:04:23] [Iter 1671/2250] R3[620/1200], Temp: 0.4738, Energy: -85.150294-0.000747j
[2025-09-02 15:05:04] [Iter 1672/2250] R3[621/1200], Temp: 0.4725, Energy: -85.160975-0.000386j
[2025-09-02 15:05:44] [Iter 1673/2250] R3[622/1200], Temp: 0.4712, Energy: -85.135324-0.005608j
[2025-09-02 15:06:24] [Iter 1674/2250] R3[623/1200], Temp: 0.4699, Energy: -85.124895-0.001847j
[2025-09-02 15:07:04] [Iter 1675/2250] R3[624/1200], Temp: 0.4686, Energy: -85.242489+0.001097j
[2025-09-02 15:07:45] [Iter 1676/2250] R3[625/1200], Temp: 0.4673, Energy: -85.134673-0.001622j
[2025-09-02 15:08:25] [Iter 1677/2250] R3[626/1200], Temp: 0.4660, Energy: -85.188198-0.000579j
[2025-09-02 15:09:05] [Iter 1678/2250] R3[627/1200], Temp: 0.4647, Energy: -85.150593-0.001445j
[2025-09-02 15:09:45] [Iter 1679/2250] R3[628/1200], Temp: 0.4634, Energy: -85.151582-0.001154j
[2025-09-02 15:10:26] [Iter 1680/2250] R3[629/1200], Temp: 0.4621, Energy: -85.159505-0.000949j
[2025-09-02 15:11:06] [Iter 1681/2250] R3[630/1200], Temp: 0.4608, Energy: -85.142822+0.000295j
[2025-09-02 15:11:46] [Iter 1682/2250] R3[631/1200], Temp: 0.4595, Energy: -85.027687+0.003091j
[2025-09-02 15:12:26] [Iter 1683/2250] R3[632/1200], Temp: 0.4582, Energy: -85.077024-0.003032j
[2025-09-02 15:13:07] [Iter 1684/2250] R3[633/1200], Temp: 0.4569, Energy: -84.989352-0.002226j
[2025-09-02 15:13:47] [Iter 1685/2250] R3[634/1200], Temp: 0.4556, Energy: -85.032172+0.001332j
[2025-09-02 15:14:27] [Iter 1686/2250] R3[635/1200], Temp: 0.4542, Energy: -85.041061+0.000206j
[2025-09-02 15:15:08] [Iter 1687/2250] R3[636/1200], Temp: 0.4529, Energy: -85.109853-0.002868j
[2025-09-02 15:15:48] [Iter 1688/2250] R3[637/1200], Temp: 0.4516, Energy: -85.143066+0.000112j
[2025-09-02 15:16:28] [Iter 1689/2250] R3[638/1200], Temp: 0.4503, Energy: -85.108162-0.001104j
[2025-09-02 15:17:08] [Iter 1690/2250] R3[639/1200], Temp: 0.4490, Energy: -85.127827-0.002112j
[2025-09-02 15:17:49] [Iter 1691/2250] R3[640/1200], Temp: 0.4477, Energy: -85.084977-0.001906j
[2025-09-02 15:18:29] [Iter 1692/2250] R3[641/1200], Temp: 0.4464, Energy: -85.037102-0.000476j
[2025-09-02 15:19:10] [Iter 1693/2250] R3[642/1200], Temp: 0.4451, Energy: -85.012201-0.000333j
[2025-09-02 15:19:50] [Iter 1694/2250] R3[643/1200], Temp: 0.4438, Energy: -85.043501+0.000624j
[2025-09-02 15:20:31] [Iter 1695/2250] R3[644/1200], Temp: 0.4425, Energy: -85.028596-0.000430j
[2025-09-02 15:21:12] [Iter 1696/2250] R3[645/1200], Temp: 0.4412, Energy: -85.090367+0.005475j
[2025-09-02 15:21:52] [Iter 1697/2250] R3[646/1200], Temp: 0.4399, Energy: -85.192463-0.000560j
[2025-09-02 15:22:33] [Iter 1698/2250] R3[647/1200], Temp: 0.4386, Energy: -85.129911+0.001062j
[2025-09-02 15:23:14] [Iter 1699/2250] R3[648/1200], Temp: 0.4373, Energy: -85.173414-0.003672j
[2025-09-02 15:23:54] [Iter 1700/2250] R3[649/1200], Temp: 0.4360, Energy: -85.155966+0.000274j
[2025-09-02 15:24:35] [Iter 1701/2250] R3[650/1200], Temp: 0.4347, Energy: -85.098839-0.000411j
[2025-09-02 15:25:15] [Iter 1702/2250] R3[651/1200], Temp: 0.4334, Energy: -85.097412+0.001717j
[2025-09-02 15:25:56] [Iter 1703/2250] R3[652/1200], Temp: 0.4321, Energy: -85.065072+0.001390j
[2025-09-02 15:26:36] [Iter 1704/2250] R3[653/1200], Temp: 0.4308, Energy: -85.089849-0.001697j
[2025-09-02 15:27:17] [Iter 1705/2250] R3[654/1200], Temp: 0.4295, Energy: -85.081266+0.001496j
[2025-09-02 15:27:57] [Iter 1706/2250] R3[655/1200], Temp: 0.4283, Energy: -85.108687+0.002755j
[2025-09-02 15:28:38] [Iter 1707/2250] R3[656/1200], Temp: 0.4270, Energy: -85.149249+0.001544j
[2025-09-02 15:29:19] [Iter 1708/2250] R3[657/1200], Temp: 0.4257, Energy: -85.105447-0.001732j
[2025-09-02 15:30:00] [Iter 1709/2250] R3[658/1200], Temp: 0.4244, Energy: -85.171084-0.000816j
[2025-09-02 15:30:40] [Iter 1710/2250] R3[659/1200], Temp: 0.4231, Energy: -85.128809-0.001735j
[2025-09-02 15:31:21] [Iter 1711/2250] R3[660/1200], Temp: 0.4218, Energy: -85.175740-0.001889j
[2025-09-02 15:32:01] [Iter 1712/2250] R3[661/1200], Temp: 0.4205, Energy: -85.110762-0.001587j
[2025-09-02 15:32:42] [Iter 1713/2250] R3[662/1200], Temp: 0.4192, Energy: -85.073785+0.000063j
[2025-09-02 15:33:22] [Iter 1714/2250] R3[663/1200], Temp: 0.4179, Energy: -85.064188-0.000148j
[2025-09-02 15:34:03] [Iter 1715/2250] R3[664/1200], Temp: 0.4166, Energy: -84.939970+0.000908j
[2025-09-02 15:34:43] [Iter 1716/2250] R3[665/1200], Temp: 0.4153, Energy: -85.095614+0.000887j
[2025-09-02 15:35:24] [Iter 1717/2250] R3[666/1200], Temp: 0.4140, Energy: -85.061662+0.000315j
[2025-09-02 15:36:04] [Iter 1718/2250] R3[667/1200], Temp: 0.4127, Energy: -84.981750-0.002901j
[2025-09-02 15:36:44] [Iter 1719/2250] R3[668/1200], Temp: 0.4115, Energy: -85.096624-0.000395j
[2025-09-02 15:37:24] [Iter 1720/2250] R3[669/1200], Temp: 0.4102, Energy: -85.098996-0.000218j
[2025-09-02 15:38:05] [Iter 1721/2250] R3[670/1200], Temp: 0.4089, Energy: -85.028193+0.000902j
[2025-09-02 15:38:45] [Iter 1722/2250] R3[671/1200], Temp: 0.4076, Energy: -85.043624-0.002350j
[2025-09-02 15:39:25] [Iter 1723/2250] R3[672/1200], Temp: 0.4063, Energy: -85.057354-0.000245j
[2025-09-02 15:40:06] [Iter 1724/2250] R3[673/1200], Temp: 0.4050, Energy: -85.053258+0.001230j
[2025-09-02 15:40:46] [Iter 1725/2250] R3[674/1200], Temp: 0.4037, Energy: -85.039241+0.002218j
[2025-09-02 15:41:26] [Iter 1726/2250] R3[675/1200], Temp: 0.4025, Energy: -85.074027+0.000018j
[2025-09-02 15:42:06] [Iter 1727/2250] R3[676/1200], Temp: 0.4012, Energy: -85.079920-0.001068j
[2025-09-02 15:42:47] [Iter 1728/2250] R3[677/1200], Temp: 0.3999, Energy: -85.116962+0.000450j
[2025-09-02 15:43:27] [Iter 1729/2250] R3[678/1200], Temp: 0.3986, Energy: -85.061937+0.001669j
[2025-09-02 15:44:07] [Iter 1730/2250] R3[679/1200], Temp: 0.3973, Energy: -85.031082+0.003539j
[2025-09-02 15:44:47] [Iter 1731/2250] R3[680/1200], Temp: 0.3960, Energy: -85.007517+0.000318j
[2025-09-02 15:45:28] [Iter 1732/2250] R3[681/1200], Temp: 0.3948, Energy: -84.986594+0.002694j
[2025-09-02 15:46:08] [Iter 1733/2250] R3[682/1200], Temp: 0.3935, Energy: -85.038861+0.002629j
[2025-09-02 15:46:48] [Iter 1734/2250] R3[683/1200], Temp: 0.3922, Energy: -85.013044-0.002790j
[2025-09-02 15:47:29] [Iter 1735/2250] R3[684/1200], Temp: 0.3909, Energy: -84.996008-0.001204j
[2025-09-02 15:48:09] [Iter 1736/2250] R3[685/1200], Temp: 0.3897, Energy: -84.911747-0.000880j
[2025-09-02 15:48:49] [Iter 1737/2250] R3[686/1200], Temp: 0.3884, Energy: -84.963306+0.000041j
[2025-09-02 15:49:29] [Iter 1738/2250] R3[687/1200], Temp: 0.3871, Energy: -84.941030-0.002660j
[2025-09-02 15:50:10] [Iter 1739/2250] R3[688/1200], Temp: 0.3858, Energy: -84.960288+0.000761j
[2025-09-02 15:50:50] [Iter 1740/2250] R3[689/1200], Temp: 0.3846, Energy: -84.933127-0.000058j
[2025-09-02 15:51:30] [Iter 1741/2250] R3[690/1200], Temp: 0.3833, Energy: -84.945632+0.001497j
[2025-09-02 15:52:11] [Iter 1742/2250] R3[691/1200], Temp: 0.3820, Energy: -84.927923+0.002551j
[2025-09-02 15:52:51] [Iter 1743/2250] R3[692/1200], Temp: 0.3807, Energy: -85.020745+0.003053j
[2025-09-02 15:53:31] [Iter 1744/2250] R3[693/1200], Temp: 0.3795, Energy: -85.053871-0.003123j
[2025-09-02 15:54:11] [Iter 1745/2250] R3[694/1200], Temp: 0.3782, Energy: -85.065198+0.000799j
[2025-09-02 15:54:52] [Iter 1746/2250] R3[695/1200], Temp: 0.3769, Energy: -85.044187-0.000937j
[2025-09-02 15:55:32] [Iter 1747/2250] R3[696/1200], Temp: 0.3757, Energy: -84.986943-0.002388j
[2025-09-02 15:56:12] [Iter 1748/2250] R3[697/1200], Temp: 0.3744, Energy: -85.019853-0.000183j
[2025-09-02 15:56:53] [Iter 1749/2250] R3[698/1200], Temp: 0.3731, Energy: -85.042877-0.000688j
[2025-09-02 15:57:33] [Iter 1750/2250] R3[699/1200], Temp: 0.3719, Energy: -85.010093-0.001905j
[2025-09-02 15:57:33] ✓ Checkpoint saved: checkpoint_iter_001750.pkl
[2025-09-02 15:58:13] [Iter 1751/2250] R3[700/1200], Temp: 0.3706, Energy: -85.091673-0.000783j
[2025-09-02 15:58:53] [Iter 1752/2250] R3[701/1200], Temp: 0.3693, Energy: -85.062085+0.000222j
[2025-09-02 15:59:34] [Iter 1753/2250] R3[702/1200], Temp: 0.3681, Energy: -85.121881+0.000213j
[2025-09-02 16:00:14] [Iter 1754/2250] R3[703/1200], Temp: 0.3668, Energy: -85.061124-0.002337j
[2025-09-02 16:00:54] [Iter 1755/2250] R3[704/1200], Temp: 0.3655, Energy: -85.022850+0.000747j
[2025-09-02 16:01:34] [Iter 1756/2250] R3[705/1200], Temp: 0.3643, Energy: -85.028915+0.005836j
[2025-09-02 16:02:15] [Iter 1757/2250] R3[706/1200], Temp: 0.3630, Energy: -85.025379+0.000545j
[2025-09-02 16:02:55] [Iter 1758/2250] R3[707/1200], Temp: 0.3618, Energy: -85.079372+0.000943j
[2025-09-02 16:03:35] [Iter 1759/2250] R3[708/1200], Temp: 0.3605, Energy: -85.074639-0.000072j
[2025-09-02 16:04:15] [Iter 1760/2250] R3[709/1200], Temp: 0.3592, Energy: -85.097843+0.001838j
[2025-09-02 16:04:56] [Iter 1761/2250] R3[710/1200], Temp: 0.3580, Energy: -85.100729-0.002059j
[2025-09-02 16:05:36] [Iter 1762/2250] R3[711/1200], Temp: 0.3567, Energy: -85.105758-0.000970j
[2025-09-02 16:06:16] [Iter 1763/2250] R3[712/1200], Temp: 0.3555, Energy: -85.102649+0.000319j
[2025-09-02 16:06:57] [Iter 1764/2250] R3[713/1200], Temp: 0.3542, Energy: -85.061524+0.000815j
[2025-09-02 16:07:37] [Iter 1765/2250] R3[714/1200], Temp: 0.3530, Energy: -85.203838-0.002359j
[2025-09-02 16:08:17] [Iter 1766/2250] R3[715/1200], Temp: 0.3517, Energy: -85.226626-0.001137j
[2025-09-02 16:08:57] [Iter 1767/2250] R3[716/1200], Temp: 0.3505, Energy: -85.106219+0.001491j
[2025-09-02 16:09:38] [Iter 1768/2250] R3[717/1200], Temp: 0.3492, Energy: -85.051755+0.001359j
[2025-09-02 16:10:18] [Iter 1769/2250] R3[718/1200], Temp: 0.3480, Energy: -85.065932-0.001845j
[2025-09-02 16:10:58] [Iter 1770/2250] R3[719/1200], Temp: 0.3467, Energy: -85.040719+0.002358j
[2025-09-02 16:11:38] [Iter 1771/2250] R3[720/1200], Temp: 0.3455, Energy: -84.974496+0.004114j
[2025-09-02 16:12:19] [Iter 1772/2250] R3[721/1200], Temp: 0.3442, Energy: -85.066294+0.001500j
[2025-09-02 16:12:59] [Iter 1773/2250] R3[722/1200], Temp: 0.3430, Energy: -85.012380-0.002092j
[2025-09-02 16:13:39] [Iter 1774/2250] R3[723/1200], Temp: 0.3418, Energy: -84.965682+0.001303j
[2025-09-02 16:14:20] [Iter 1775/2250] R3[724/1200], Temp: 0.3405, Energy: -84.921358-0.000126j
[2025-09-02 16:15:00] [Iter 1776/2250] R3[725/1200], Temp: 0.3393, Energy: -85.008850+0.001500j
[2025-09-02 16:15:40] [Iter 1777/2250] R3[726/1200], Temp: 0.3380, Energy: -85.094862-0.001927j
[2025-09-02 16:16:20] [Iter 1778/2250] R3[727/1200], Temp: 0.3368, Energy: -85.022227-0.000894j
[2025-09-02 16:17:01] [Iter 1779/2250] R3[728/1200], Temp: 0.3356, Energy: -85.013105-0.000030j
[2025-09-02 16:17:41] [Iter 1780/2250] R3[729/1200], Temp: 0.3343, Energy: -85.022871-0.002526j
[2025-09-02 16:18:21] [Iter 1781/2250] R3[730/1200], Temp: 0.3331, Energy: -85.005199-0.000323j
[2025-09-02 16:19:01] [Iter 1782/2250] R3[731/1200], Temp: 0.3319, Energy: -84.997542+0.000088j
[2025-09-02 16:19:42] [Iter 1783/2250] R3[732/1200], Temp: 0.3306, Energy: -84.955627+0.001098j
[2025-09-02 16:20:22] [Iter 1784/2250] R3[733/1200], Temp: 0.3294, Energy: -85.064846+0.001791j
[2025-09-02 16:21:02] [Iter 1785/2250] R3[734/1200], Temp: 0.3282, Energy: -85.101771+0.000337j
[2025-09-02 16:21:42] [Iter 1786/2250] R3[735/1200], Temp: 0.3269, Energy: -85.053467+0.000042j
[2025-09-02 16:22:23] [Iter 1787/2250] R3[736/1200], Temp: 0.3257, Energy: -84.974247-0.000899j
[2025-09-02 16:23:03] [Iter 1788/2250] R3[737/1200], Temp: 0.3245, Energy: -85.065369-0.005874j
[2025-09-02 16:23:43] [Iter 1789/2250] R3[738/1200], Temp: 0.3233, Energy: -85.041327+0.000190j
[2025-09-02 16:24:23] [Iter 1790/2250] R3[739/1200], Temp: 0.3220, Energy: -84.999384+0.001192j
[2025-09-02 16:25:04] [Iter 1791/2250] R3[740/1200], Temp: 0.3208, Energy: -84.981289+0.000559j
[2025-09-02 16:25:44] [Iter 1792/2250] R3[741/1200], Temp: 0.3196, Energy: -85.067160+0.000733j
[2025-09-02 16:26:24] [Iter 1793/2250] R3[742/1200], Temp: 0.3184, Energy: -85.055617+0.001967j
[2025-09-02 16:27:05] [Iter 1794/2250] R3[743/1200], Temp: 0.3172, Energy: -85.029723-0.001394j
[2025-09-02 16:27:45] [Iter 1795/2250] R3[744/1200], Temp: 0.3159, Energy: -85.033508+0.002835j
[2025-09-02 16:28:25] [Iter 1796/2250] R3[745/1200], Temp: 0.3147, Energy: -85.083540-0.000186j
[2025-09-02 16:29:05] [Iter 1797/2250] R3[746/1200], Temp: 0.3135, Energy: -85.148696+0.000714j
[2025-09-02 16:29:46] [Iter 1798/2250] R3[747/1200], Temp: 0.3123, Energy: -85.235628+0.002273j
[2025-09-02 16:30:26] [Iter 1799/2250] R3[748/1200], Temp: 0.3111, Energy: -85.078869+0.003179j
[2025-09-02 16:31:06] [Iter 1800/2250] R3[749/1200], Temp: 0.3099, Energy: -85.078325+0.000835j
[2025-09-02 16:31:47] [Iter 1801/2250] R3[750/1200], Temp: 0.3087, Energy: -85.063940-0.000717j
[2025-09-02 16:32:27] [Iter 1802/2250] R3[751/1200], Temp: 0.3074, Energy: -85.069352-0.001215j
[2025-09-02 16:33:07] [Iter 1803/2250] R3[752/1200], Temp: 0.3062, Energy: -84.987708-0.002841j
[2025-09-02 16:33:47] [Iter 1804/2250] R3[753/1200], Temp: 0.3050, Energy: -85.068133-0.000258j
[2025-09-02 16:34:28] [Iter 1805/2250] R3[754/1200], Temp: 0.3038, Energy: -85.047571+0.002540j
[2025-09-02 16:35:08] [Iter 1806/2250] R3[755/1200], Temp: 0.3026, Energy: -85.051997+0.000609j
[2025-09-02 16:35:48] [Iter 1807/2250] R3[756/1200], Temp: 0.3014, Energy: -85.018960+0.001131j
[2025-09-02 16:36:28] [Iter 1808/2250] R3[757/1200], Temp: 0.3002, Energy: -85.074036+0.001396j
[2025-09-02 16:37:09] [Iter 1809/2250] R3[758/1200], Temp: 0.2990, Energy: -85.022866-0.001039j
[2025-09-02 16:37:49] [Iter 1810/2250] R3[759/1200], Temp: 0.2978, Energy: -84.947043+0.001325j
[2025-09-02 16:38:29] [Iter 1811/2250] R3[760/1200], Temp: 0.2966, Energy: -85.028068-0.002429j
[2025-09-02 16:39:09] [Iter 1812/2250] R3[761/1200], Temp: 0.2954, Energy: -85.060170-0.002243j
[2025-09-02 16:39:50] [Iter 1813/2250] R3[762/1200], Temp: 0.2942, Energy: -85.041301+0.001694j
[2025-09-02 16:40:30] [Iter 1814/2250] R3[763/1200], Temp: 0.2931, Energy: -84.996393+0.001311j
[2025-09-02 16:41:10] [Iter 1815/2250] R3[764/1200], Temp: 0.2919, Energy: -84.939879+0.001947j
[2025-09-02 16:41:50] [Iter 1816/2250] R3[765/1200], Temp: 0.2907, Energy: -84.983255-0.001735j
[2025-09-02 16:42:31] [Iter 1817/2250] R3[766/1200], Temp: 0.2895, Energy: -84.992257-0.000948j
[2025-09-02 16:43:11] [Iter 1818/2250] R3[767/1200], Temp: 0.2883, Energy: -85.004943+0.000319j
[2025-09-02 16:43:51] [Iter 1819/2250] R3[768/1200], Temp: 0.2871, Energy: -85.086856-0.001435j
[2025-09-02 16:44:31] [Iter 1820/2250] R3[769/1200], Temp: 0.2859, Energy: -85.057114+0.000802j
[2025-09-02 16:45:12] [Iter 1821/2250] R3[770/1200], Temp: 0.2847, Energy: -85.052309-0.001022j
[2025-09-02 16:45:52] [Iter 1822/2250] R3[771/1200], Temp: 0.2836, Energy: -84.994421-0.002634j
[2025-09-02 16:46:32] [Iter 1823/2250] R3[772/1200], Temp: 0.2824, Energy: -85.051853-0.004166j
[2025-09-02 16:47:13] [Iter 1824/2250] R3[773/1200], Temp: 0.2812, Energy: -84.942667+0.000747j
[2025-09-02 16:47:53] [Iter 1825/2250] R3[774/1200], Temp: 0.2800, Energy: -85.002182+0.001030j
[2025-09-02 16:48:33] [Iter 1826/2250] R3[775/1200], Temp: 0.2789, Energy: -84.953018-0.000466j
[2025-09-02 16:49:13] [Iter 1827/2250] R3[776/1200], Temp: 0.2777, Energy: -85.070512+0.000478j
[2025-09-02 16:49:54] [Iter 1828/2250] R3[777/1200], Temp: 0.2765, Energy: -85.087859+0.002042j
[2025-09-02 16:50:34] [Iter 1829/2250] R3[778/1200], Temp: 0.2753, Energy: -85.016043-0.000772j
[2025-09-02 16:51:14] [Iter 1830/2250] R3[779/1200], Temp: 0.2742, Energy: -85.059706-0.002588j
[2025-09-02 16:51:54] [Iter 1831/2250] R3[780/1200], Temp: 0.2730, Energy: -85.016275-0.000208j
[2025-09-02 16:52:35] [Iter 1832/2250] R3[781/1200], Temp: 0.2718, Energy: -85.092546-0.000443j
[2025-09-02 16:53:15] [Iter 1833/2250] R3[782/1200], Temp: 0.2707, Energy: -85.051988-0.001065j
[2025-09-02 16:53:55] [Iter 1834/2250] R3[783/1200], Temp: 0.2695, Energy: -85.011484+0.002045j
[2025-09-02 16:54:36] [Iter 1835/2250] R3[784/1200], Temp: 0.2684, Energy: -85.067982+0.000987j
[2025-09-02 16:55:16] [Iter 1836/2250] R3[785/1200], Temp: 0.2672, Energy: -85.104567-0.001323j
[2025-09-02 16:55:56] [Iter 1837/2250] R3[786/1200], Temp: 0.2660, Energy: -85.089339+0.002025j
[2025-09-02 16:56:37] [Iter 1838/2250] R3[787/1200], Temp: 0.2649, Energy: -85.020845-0.000845j
[2025-09-02 16:57:17] [Iter 1839/2250] R3[788/1200], Temp: 0.2637, Energy: -85.040369+0.002761j
[2025-09-02 16:57:57] [Iter 1840/2250] R3[789/1200], Temp: 0.2626, Energy: -85.060623-0.000857j
[2025-09-02 16:58:37] [Iter 1841/2250] R3[790/1200], Temp: 0.2614, Energy: -85.070247-0.002397j
[2025-09-02 16:59:18] [Iter 1842/2250] R3[791/1200], Temp: 0.2603, Energy: -85.106588+0.000050j
[2025-09-02 16:59:58] [Iter 1843/2250] R3[792/1200], Temp: 0.2591, Energy: -85.086120-0.000115j
[2025-09-02 17:00:38] [Iter 1844/2250] R3[793/1200], Temp: 0.2580, Energy: -84.990977+0.001960j
[2025-09-02 17:01:19] [Iter 1845/2250] R3[794/1200], Temp: 0.2568, Energy: -85.071027+0.001256j
[2025-09-02 17:01:59] [Iter 1846/2250] R3[795/1200], Temp: 0.2557, Energy: -85.021207-0.000931j
[2025-09-02 17:02:39] [Iter 1847/2250] R3[796/1200], Temp: 0.2545, Energy: -85.109049+0.000593j
[2025-09-02 17:03:19] [Iter 1848/2250] R3[797/1200], Temp: 0.2534, Energy: -85.127385+0.003263j
[2025-09-02 17:04:00] [Iter 1849/2250] R3[798/1200], Temp: 0.2523, Energy: -85.104669-0.000410j
[2025-09-02 17:04:40] [Iter 1850/2250] R3[799/1200], Temp: 0.2511, Energy: -84.995994-0.000211j
[2025-09-02 17:05:20] [Iter 1851/2250] R3[800/1200], Temp: 0.2500, Energy: -85.068677+0.002786j
[2025-09-02 17:06:01] [Iter 1852/2250] R3[801/1200], Temp: 0.2489, Energy: -85.095350-0.002096j
[2025-09-02 17:06:41] [Iter 1853/2250] R3[802/1200], Temp: 0.2477, Energy: -85.029293-0.000235j
[2025-09-02 17:07:21] [Iter 1854/2250] R3[803/1200], Temp: 0.2466, Energy: -85.077718-0.001139j
[2025-09-02 17:08:01] [Iter 1855/2250] R3[804/1200], Temp: 0.2455, Energy: -85.030468+0.000443j
[2025-09-02 17:08:42] [Iter 1856/2250] R3[805/1200], Temp: 0.2444, Energy: -84.976457-0.000160j
[2025-09-02 17:09:22] [Iter 1857/2250] R3[806/1200], Temp: 0.2432, Energy: -85.036336+0.000454j
[2025-09-02 17:10:02] [Iter 1858/2250] R3[807/1200], Temp: 0.2421, Energy: -84.933578+0.000526j
[2025-09-02 17:10:43] [Iter 1859/2250] R3[808/1200], Temp: 0.2410, Energy: -84.971154+0.001215j
[2025-09-02 17:11:23] [Iter 1860/2250] R3[809/1200], Temp: 0.2399, Energy: -84.982918-0.001188j
[2025-09-02 17:12:03] [Iter 1861/2250] R3[810/1200], Temp: 0.2388, Energy: -84.997475-0.001591j
[2025-09-02 17:12:43] [Iter 1862/2250] R3[811/1200], Temp: 0.2376, Energy: -85.090388-0.000322j
[2025-09-02 17:13:24] [Iter 1863/2250] R3[812/1200], Temp: 0.2365, Energy: -85.126506+0.001093j
[2025-09-02 17:14:04] [Iter 1864/2250] R3[813/1200], Temp: 0.2354, Energy: -85.028508-0.003324j
[2025-09-02 17:14:44] [Iter 1865/2250] R3[814/1200], Temp: 0.2343, Energy: -85.010685+0.002204j
[2025-09-02 17:15:24] [Iter 1866/2250] R3[815/1200], Temp: 0.2332, Energy: -84.954069+0.002307j
[2025-09-02 17:16:05] [Iter 1867/2250] R3[816/1200], Temp: 0.2321, Energy: -84.941519+0.000626j
[2025-09-02 17:16:45] [Iter 1868/2250] R3[817/1200], Temp: 0.2310, Energy: -84.978373-0.002080j
[2025-09-02 17:17:25] [Iter 1869/2250] R3[818/1200], Temp: 0.2299, Energy: -84.962455+0.001709j
[2025-09-02 17:18:06] [Iter 1870/2250] R3[819/1200], Temp: 0.2288, Energy: -84.984677+0.000003j
[2025-09-02 17:18:46] [Iter 1871/2250] R3[820/1200], Temp: 0.2277, Energy: -84.963286-0.002031j
[2025-09-02 17:19:26] [Iter 1872/2250] R3[821/1200], Temp: 0.2266, Energy: -85.084635+0.000892j
[2025-09-02 17:20:06] [Iter 1873/2250] R3[822/1200], Temp: 0.2255, Energy: -84.999603-0.000825j
[2025-09-02 17:20:47] [Iter 1874/2250] R3[823/1200], Temp: 0.2244, Energy: -85.077405+0.001049j
[2025-09-02 17:21:27] [Iter 1875/2250] R3[824/1200], Temp: 0.2233, Energy: -85.045691+0.000440j
[2025-09-02 17:22:07] [Iter 1876/2250] R3[825/1200], Temp: 0.2222, Energy: -85.009497+0.002071j
[2025-09-02 17:22:48] [Iter 1877/2250] R3[826/1200], Temp: 0.2211, Energy: -85.035607-0.001817j
[2025-09-02 17:23:28] [Iter 1878/2250] R3[827/1200], Temp: 0.2200, Energy: -85.096536+0.001120j
[2025-09-02 17:24:08] [Iter 1879/2250] R3[828/1200], Temp: 0.2190, Energy: -85.016863-0.000870j
[2025-09-02 17:24:48] [Iter 1880/2250] R3[829/1200], Temp: 0.2179, Energy: -85.063121+0.001192j
[2025-09-02 17:25:29] [Iter 1881/2250] R3[830/1200], Temp: 0.2168, Energy: -85.062330+0.001950j
[2025-09-02 17:26:09] [Iter 1882/2250] R3[831/1200], Temp: 0.2157, Energy: -85.049383-0.001711j
[2025-09-02 17:26:49] [Iter 1883/2250] R3[832/1200], Temp: 0.2146, Energy: -85.005880-0.000002j
[2025-09-02 17:27:30] [Iter 1884/2250] R3[833/1200], Temp: 0.2136, Energy: -84.991239+0.000184j
[2025-09-02 17:28:10] [Iter 1885/2250] R3[834/1200], Temp: 0.2125, Energy: -85.040633-0.002730j
[2025-09-02 17:28:50] [Iter 1886/2250] R3[835/1200], Temp: 0.2114, Energy: -84.992002-0.001531j
[2025-09-02 17:29:31] [Iter 1887/2250] R3[836/1200], Temp: 0.2104, Energy: -84.973510-0.002879j
[2025-09-02 17:30:11] [Iter 1888/2250] R3[837/1200], Temp: 0.2093, Energy: -84.992420+0.001202j
[2025-09-02 17:30:51] [Iter 1889/2250] R3[838/1200], Temp: 0.2082, Energy: -85.045318-0.000536j
[2025-09-02 17:31:31] [Iter 1890/2250] R3[839/1200], Temp: 0.2072, Energy: -84.967240+0.000583j
[2025-09-02 17:32:12] [Iter 1891/2250] R3[840/1200], Temp: 0.2061, Energy: -85.053103-0.000394j
[2025-09-02 17:32:52] [Iter 1892/2250] R3[841/1200], Temp: 0.2050, Energy: -85.026543+0.001287j
[2025-09-02 17:33:32] [Iter 1893/2250] R3[842/1200], Temp: 0.2040, Energy: -85.090088+0.003857j
[2025-09-02 17:34:12] [Iter 1894/2250] R3[843/1200], Temp: 0.2029, Energy: -85.110276-0.004421j
[2025-09-02 17:34:53] [Iter 1895/2250] R3[844/1200], Temp: 0.2019, Energy: -85.003676+0.000015j
[2025-09-02 17:35:33] [Iter 1896/2250] R3[845/1200], Temp: 0.2008, Energy: -85.050949+0.000828j
[2025-09-02 17:36:13] [Iter 1897/2250] R3[846/1200], Temp: 0.1998, Energy: -85.086318+0.000127j
[2025-09-02 17:36:53] [Iter 1898/2250] R3[847/1200], Temp: 0.1987, Energy: -85.050938+0.000170j
[2025-09-02 17:37:34] [Iter 1899/2250] R3[848/1200], Temp: 0.1977, Energy: -85.120211-0.000353j
[2025-09-02 17:38:14] [Iter 1900/2250] R3[849/1200], Temp: 0.1967, Energy: -85.109998+0.001324j
[2025-09-02 17:38:54] [Iter 1901/2250] R3[850/1200], Temp: 0.1956, Energy: -85.168774-0.001304j
[2025-09-02 17:39:35] [Iter 1902/2250] R3[851/1200], Temp: 0.1946, Energy: -85.095631+0.001320j
[2025-09-02 17:40:15] [Iter 1903/2250] R3[852/1200], Temp: 0.1935, Energy: -85.125210-0.000326j
[2025-09-02 17:40:55] [Iter 1904/2250] R3[853/1200], Temp: 0.1925, Energy: -85.006198+0.000452j
[2025-09-02 17:41:35] [Iter 1905/2250] R3[854/1200], Temp: 0.1915, Energy: -85.090383-0.003467j
[2025-09-02 17:42:16] [Iter 1906/2250] R3[855/1200], Temp: 0.1905, Energy: -85.025045+0.000759j
[2025-09-02 17:42:56] [Iter 1907/2250] R3[856/1200], Temp: 0.1894, Energy: -85.094371-0.000915j
[2025-09-02 17:43:36] [Iter 1908/2250] R3[857/1200], Temp: 0.1884, Energy: -85.138122-0.001652j
[2025-09-02 17:44:17] [Iter 1909/2250] R3[858/1200], Temp: 0.1874, Energy: -85.122536-0.001131j
[2025-09-02 17:44:57] [Iter 1910/2250] R3[859/1200], Temp: 0.1864, Energy: -85.141864+0.000905j
[2025-09-02 17:45:37] [Iter 1911/2250] R3[860/1200], Temp: 0.1853, Energy: -85.119145+0.001042j
[2025-09-02 17:46:17] [Iter 1912/2250] R3[861/1200], Temp: 0.1843, Energy: -85.051760+0.001134j
[2025-09-02 17:46:58] [Iter 1913/2250] R3[862/1200], Temp: 0.1833, Energy: -85.102432-0.000751j
[2025-09-02 17:47:38] [Iter 1914/2250] R3[863/1200], Temp: 0.1823, Energy: -85.083421+0.001154j
[2025-09-02 17:48:18] [Iter 1915/2250] R3[864/1200], Temp: 0.1813, Energy: -85.137070-0.004136j
[2025-09-02 17:48:59] [Iter 1916/2250] R3[865/1200], Temp: 0.1803, Energy: -85.205789+0.000005j
[2025-09-02 17:49:39] [Iter 1917/2250] R3[866/1200], Temp: 0.1793, Energy: -85.121562-0.002682j
[2025-09-02 17:50:19] [Iter 1918/2250] R3[867/1200], Temp: 0.1783, Energy: -85.054755+0.000625j
[2025-09-02 17:50:59] [Iter 1919/2250] R3[868/1200], Temp: 0.1773, Energy: -85.056504+0.001060j
[2025-09-02 17:51:40] [Iter 1920/2250] R3[869/1200], Temp: 0.1763, Energy: -85.093200+0.000571j
[2025-09-02 17:52:20] [Iter 1921/2250] R3[870/1200], Temp: 0.1753, Energy: -85.048335+0.000535j
[2025-09-02 17:53:00] [Iter 1922/2250] R3[871/1200], Temp: 0.1743, Energy: -84.977427-0.002027j
[2025-09-02 17:53:40] [Iter 1923/2250] R3[872/1200], Temp: 0.1733, Energy: -85.030873-0.000033j
[2025-09-02 17:54:21] [Iter 1924/2250] R3[873/1200], Temp: 0.1723, Energy: -85.065719-0.000345j
[2025-09-02 17:55:01] [Iter 1925/2250] R3[874/1200], Temp: 0.1713, Energy: -85.055653-0.001228j
[2025-09-02 17:55:41] [Iter 1926/2250] R3[875/1200], Temp: 0.1703, Energy: -85.013862-0.001141j
[2025-09-02 17:56:22] [Iter 1927/2250] R3[876/1200], Temp: 0.1693, Energy: -85.054743+0.002703j
[2025-09-02 17:57:02] [Iter 1928/2250] R3[877/1200], Temp: 0.1684, Energy: -84.948140+0.001544j
[2025-09-02 17:57:42] [Iter 1929/2250] R3[878/1200], Temp: 0.1674, Energy: -85.045251+0.001485j
[2025-09-02 17:58:23] [Iter 1930/2250] R3[879/1200], Temp: 0.1664, Energy: -85.007128+0.000226j
[2025-09-02 17:59:03] [Iter 1931/2250] R3[880/1200], Temp: 0.1654, Energy: -85.017441-0.000217j
[2025-09-02 17:59:43] [Iter 1932/2250] R3[881/1200], Temp: 0.1645, Energy: -85.046957-0.000014j
[2025-09-02 18:00:23] [Iter 1933/2250] R3[882/1200], Temp: 0.1635, Energy: -84.988318+0.000583j
[2025-09-02 18:01:04] [Iter 1934/2250] R3[883/1200], Temp: 0.1625, Energy: -85.033405+0.002544j
[2025-09-02 18:01:44] [Iter 1935/2250] R3[884/1200], Temp: 0.1616, Energy: -84.999261+0.000792j
[2025-09-02 18:02:24] [Iter 1936/2250] R3[885/1200], Temp: 0.1606, Energy: -85.024568+0.000294j
[2025-09-02 18:03:04] [Iter 1937/2250] R3[886/1200], Temp: 0.1596, Energy: -84.999553-0.000600j
[2025-09-02 18:03:45] [Iter 1938/2250] R3[887/1200], Temp: 0.1587, Energy: -85.021896+0.000688j
[2025-09-02 18:04:25] [Iter 1939/2250] R3[888/1200], Temp: 0.1577, Energy: -85.077583-0.001240j
[2025-09-02 18:05:05] [Iter 1940/2250] R3[889/1200], Temp: 0.1568, Energy: -85.084738-0.005196j
[2025-09-02 18:05:46] [Iter 1941/2250] R3[890/1200], Temp: 0.1558, Energy: -85.129613-0.000633j
[2025-09-02 18:06:26] [Iter 1942/2250] R3[891/1200], Temp: 0.1549, Energy: -85.080389+0.000059j
[2025-09-02 18:07:06] [Iter 1943/2250] R3[892/1200], Temp: 0.1539, Energy: -85.073058+0.000136j
[2025-09-02 18:07:46] [Iter 1944/2250] R3[893/1200], Temp: 0.1530, Energy: -85.024164-0.001796j
[2025-09-02 18:08:27] [Iter 1945/2250] R3[894/1200], Temp: 0.1520, Energy: -85.074187-0.001931j
[2025-09-02 18:09:07] [Iter 1946/2250] R3[895/1200], Temp: 0.1511, Energy: -85.062289-0.002418j
[2025-09-02 18:09:47] [Iter 1947/2250] R3[896/1200], Temp: 0.1502, Energy: -85.147412-0.000789j
[2025-09-02 18:10:28] [Iter 1948/2250] R3[897/1200], Temp: 0.1492, Energy: -85.097255+0.001661j
[2025-09-02 18:11:08] [Iter 1949/2250] R3[898/1200], Temp: 0.1483, Energy: -85.104643-0.002855j
[2025-09-02 18:11:48] [Iter 1950/2250] R3[899/1200], Temp: 0.1474, Energy: -85.081674+0.000742j
[2025-09-02 18:12:28] [Iter 1951/2250] R3[900/1200], Temp: 0.1464, Energy: -85.092578-0.000372j
[2025-09-02 18:13:09] [Iter 1952/2250] R3[901/1200], Temp: 0.1455, Energy: -85.007571-0.002522j
[2025-09-02 18:13:49] [Iter 1953/2250] R3[902/1200], Temp: 0.1446, Energy: -84.968868-0.001075j
[2025-09-02 18:14:29] [Iter 1954/2250] R3[903/1200], Temp: 0.1437, Energy: -85.035372+0.000729j
[2025-09-02 18:15:09] [Iter 1955/2250] R3[904/1200], Temp: 0.1428, Energy: -84.984344-0.000229j
[2025-09-02 18:15:50] [Iter 1956/2250] R3[905/1200], Temp: 0.1418, Energy: -85.047024-0.002132j
[2025-09-02 18:16:30] [Iter 1957/2250] R3[906/1200], Temp: 0.1409, Energy: -85.048681-0.001196j
[2025-09-02 18:17:10] [Iter 1958/2250] R3[907/1200], Temp: 0.1400, Energy: -85.086451+0.000243j
[2025-09-02 18:17:50] [Iter 1959/2250] R3[908/1200], Temp: 0.1391, Energy: -85.122163+0.001666j
[2025-09-02 18:18:31] [Iter 1960/2250] R3[909/1200], Temp: 0.1382, Energy: -85.143123+0.001773j
[2025-09-02 18:19:11] [Iter 1961/2250] R3[910/1200], Temp: 0.1373, Energy: -85.153653+0.000893j
[2025-09-02 18:19:51] [Iter 1962/2250] R3[911/1200], Temp: 0.1364, Energy: -85.070447-0.001001j
[2025-09-02 18:20:31] [Iter 1963/2250] R3[912/1200], Temp: 0.1355, Energy: -85.090549+0.001543j
[2025-09-02 18:21:12] [Iter 1964/2250] R3[913/1200], Temp: 0.1346, Energy: -85.072421-0.001110j
[2025-09-02 18:21:52] [Iter 1965/2250] R3[914/1200], Temp: 0.1337, Energy: -85.007828+0.000028j
[2025-09-02 18:22:32] [Iter 1966/2250] R3[915/1200], Temp: 0.1328, Energy: -85.056019-0.002127j
[2025-09-02 18:23:13] [Iter 1967/2250] R3[916/1200], Temp: 0.1320, Energy: -85.119723+0.003722j
[2025-09-02 18:23:53] [Iter 1968/2250] R3[917/1200], Temp: 0.1311, Energy: -85.076662-0.001522j
[2025-09-02 18:24:33] [Iter 1969/2250] R3[918/1200], Temp: 0.1302, Energy: -84.979572+0.002167j
[2025-09-02 18:25:13] [Iter 1970/2250] R3[919/1200], Temp: 0.1293, Energy: -84.982780+0.000759j
[2025-09-02 18:25:54] [Iter 1971/2250] R3[920/1200], Temp: 0.1284, Energy: -85.088384+0.000763j
[2025-09-02 18:26:34] [Iter 1972/2250] R3[921/1200], Temp: 0.1276, Energy: -85.002806+0.000158j
[2025-09-02 18:27:14] [Iter 1973/2250] R3[922/1200], Temp: 0.1267, Energy: -85.087109-0.001206j
[2025-09-02 18:27:54] [Iter 1974/2250] R3[923/1200], Temp: 0.1258, Energy: -85.005832-0.000276j
[2025-09-02 18:28:35] [Iter 1975/2250] R3[924/1200], Temp: 0.1249, Energy: -85.021109+0.000179j
[2025-09-02 18:29:15] [Iter 1976/2250] R3[925/1200], Temp: 0.1241, Energy: -85.022045-0.000070j
[2025-09-02 18:29:55] [Iter 1977/2250] R3[926/1200], Temp: 0.1232, Energy: -85.052663-0.001284j
[2025-09-02 18:30:36] [Iter 1978/2250] R3[927/1200], Temp: 0.1224, Energy: -85.018234+0.002932j
[2025-09-02 18:31:16] [Iter 1979/2250] R3[928/1200], Temp: 0.1215, Energy: -85.009404+0.002067j
[2025-09-02 18:31:56] [Iter 1980/2250] R3[929/1200], Temp: 0.1206, Energy: -85.030565-0.000945j
[2025-09-02 18:32:36] [Iter 1981/2250] R3[930/1200], Temp: 0.1198, Energy: -85.035565+0.003585j
[2025-09-02 18:33:17] [Iter 1982/2250] R3[931/1200], Temp: 0.1189, Energy: -85.002623-0.001752j
[2025-09-02 18:33:57] [Iter 1983/2250] R3[932/1200], Temp: 0.1181, Energy: -84.956251+0.001117j
[2025-09-02 18:34:37] [Iter 1984/2250] R3[933/1200], Temp: 0.1173, Energy: -84.976868+0.000153j
[2025-09-02 18:35:18] [Iter 1985/2250] R3[934/1200], Temp: 0.1164, Energy: -85.044215-0.002058j
[2025-09-02 18:35:58] [Iter 1986/2250] R3[935/1200], Temp: 0.1156, Energy: -84.962829-0.000863j
[2025-09-02 18:36:38] [Iter 1987/2250] R3[936/1200], Temp: 0.1147, Energy: -85.029352+0.001217j
[2025-09-02 18:37:18] [Iter 1988/2250] R3[937/1200], Temp: 0.1139, Energy: -85.078443-0.001137j
[2025-09-02 18:37:59] [Iter 1989/2250] R3[938/1200], Temp: 0.1131, Energy: -85.039329-0.002876j
[2025-09-02 18:38:39] [Iter 1990/2250] R3[939/1200], Temp: 0.1123, Energy: -85.101008+0.001749j
[2025-09-02 18:39:19] [Iter 1991/2250] R3[940/1200], Temp: 0.1114, Energy: -85.005224+0.000071j
[2025-09-02 18:39:59] [Iter 1992/2250] R3[941/1200], Temp: 0.1106, Energy: -85.111776-0.002536j
[2025-09-02 18:40:40] [Iter 1993/2250] R3[942/1200], Temp: 0.1098, Energy: -85.117566+0.000645j
[2025-09-02 18:41:20] [Iter 1994/2250] R3[943/1200], Temp: 0.1090, Energy: -85.053210-0.000944j
[2025-09-02 18:42:00] [Iter 1995/2250] R3[944/1200], Temp: 0.1082, Energy: -85.081995+0.001101j
[2025-09-02 18:42:40] [Iter 1996/2250] R3[945/1200], Temp: 0.1073, Energy: -85.040409-0.004259j
[2025-09-02 18:43:21] [Iter 1997/2250] R3[946/1200], Temp: 0.1065, Energy: -85.051136-0.000727j
[2025-09-02 18:44:01] [Iter 1998/2250] R3[947/1200], Temp: 0.1057, Energy: -85.155065-0.001783j
[2025-09-02 18:44:41] [Iter 1999/2250] R3[948/1200], Temp: 0.1049, Energy: -85.097002+0.000338j
[2025-09-02 18:45:22] [Iter 2000/2250] R3[949/1200], Temp: 0.1041, Energy: -85.075225+0.001436j
[2025-09-02 18:45:22] ✓ Checkpoint saved: checkpoint_iter_002000.pkl
[2025-09-02 18:46:02] [Iter 2001/2250] R3[950/1200], Temp: 0.1033, Energy: -85.165558-0.001635j
[2025-09-02 18:46:42] [Iter 2002/2250] R3[951/1200], Temp: 0.1025, Energy: -85.095671-0.002051j
[2025-09-02 18:47:23] [Iter 2003/2250] R3[952/1200], Temp: 0.1017, Energy: -85.037781+0.003478j
[2025-09-02 18:48:03] [Iter 2004/2250] R3[953/1200], Temp: 0.1009, Energy: -85.129677+0.000990j
[2025-09-02 18:48:43] [Iter 2005/2250] R3[954/1200], Temp: 0.1002, Energy: -85.070360-0.000566j
[2025-09-02 18:49:23] [Iter 2006/2250] R3[955/1200], Temp: 0.0994, Energy: -85.054625+0.000273j
[2025-09-02 18:50:04] [Iter 2007/2250] R3[956/1200], Temp: 0.0986, Energy: -85.071579-0.000603j
[2025-09-02 18:50:44] [Iter 2008/2250] R3[957/1200], Temp: 0.0978, Energy: -85.101403-0.001553j
[2025-09-02 18:51:24] [Iter 2009/2250] R3[958/1200], Temp: 0.0970, Energy: -85.052410+0.000362j
[2025-09-02 18:52:05] [Iter 2010/2250] R3[959/1200], Temp: 0.0963, Energy: -85.006756-0.001854j
[2025-09-02 18:52:45] [Iter 2011/2250] R3[960/1200], Temp: 0.0955, Energy: -84.966162-0.002715j
[2025-09-02 18:53:25] [Iter 2012/2250] R3[961/1200], Temp: 0.0947, Energy: -85.038074+0.003433j
[2025-09-02 18:54:05] [Iter 2013/2250] R3[962/1200], Temp: 0.0940, Energy: -85.060934+0.000627j
[2025-09-02 18:54:46] [Iter 2014/2250] R3[963/1200], Temp: 0.0932, Energy: -84.987366+0.001867j
[2025-09-02 18:55:26] [Iter 2015/2250] R3[964/1200], Temp: 0.0924, Energy: -84.969862-0.000027j
[2025-09-02 18:56:06] [Iter 2016/2250] R3[965/1200], Temp: 0.0917, Energy: -84.947179-0.000635j
[2025-09-02 18:56:47] [Iter 2017/2250] R3[966/1200], Temp: 0.0909, Energy: -84.964223-0.000280j
[2025-09-02 18:57:27] [Iter 2018/2250] R3[967/1200], Temp: 0.0902, Energy: -84.964027-0.000031j
[2025-09-02 18:58:07] [Iter 2019/2250] R3[968/1200], Temp: 0.0894, Energy: -85.003439+0.000121j
[2025-09-02 18:58:47] [Iter 2020/2250] R3[969/1200], Temp: 0.0887, Energy: -84.906317+0.001245j
[2025-09-02 18:59:28] [Iter 2021/2250] R3[970/1200], Temp: 0.0879, Energy: -84.951390-0.000188j
[2025-09-02 19:00:08] [Iter 2022/2250] R3[971/1200], Temp: 0.0872, Energy: -84.932525-0.002080j
[2025-09-02 19:00:48] [Iter 2023/2250] R3[972/1200], Temp: 0.0865, Energy: -84.970558+0.002203j
[2025-09-02 19:01:29] [Iter 2024/2250] R3[973/1200], Temp: 0.0857, Energy: -84.917668+0.001837j
[2025-09-02 19:02:09] [Iter 2025/2250] R3[974/1200], Temp: 0.0850, Energy: -85.015017+0.001615j
[2025-09-02 19:02:49] [Iter 2026/2250] R3[975/1200], Temp: 0.0843, Energy: -85.017219+0.000870j
[2025-09-02 19:03:29] [Iter 2027/2250] R3[976/1200], Temp: 0.0835, Energy: -84.995276-0.000615j
[2025-09-02 19:04:10] [Iter 2028/2250] R3[977/1200], Temp: 0.0828, Energy: -84.984461+0.001625j
[2025-09-02 19:04:50] [Iter 2029/2250] R3[978/1200], Temp: 0.0821, Energy: -85.032311+0.001259j
[2025-09-02 19:05:30] [Iter 2030/2250] R3[979/1200], Temp: 0.0814, Energy: -85.035820-0.000765j
[2025-09-02 19:06:11] [Iter 2031/2250] R3[980/1200], Temp: 0.0807, Energy: -85.027219+0.002806j
[2025-09-02 19:06:51] [Iter 2032/2250] R3[981/1200], Temp: 0.0800, Energy: -84.986522+0.000983j
[2025-09-02 19:07:31] [Iter 2033/2250] R3[982/1200], Temp: 0.0792, Energy: -85.059642+0.000509j
[2025-09-02 19:08:11] [Iter 2034/2250] R3[983/1200], Temp: 0.0785, Energy: -85.117225+0.000577j
[2025-09-02 19:08:52] [Iter 2035/2250] R3[984/1200], Temp: 0.0778, Energy: -85.016715-0.002018j
[2025-09-02 19:09:32] [Iter 2036/2250] R3[985/1200], Temp: 0.0771, Energy: -85.017263-0.000257j
[2025-09-02 19:10:12] [Iter 2037/2250] R3[986/1200], Temp: 0.0764, Energy: -84.959125-0.000729j
[2025-09-02 19:10:52] [Iter 2038/2250] R3[987/1200], Temp: 0.0757, Energy: -85.019503+0.002808j
[2025-09-02 19:11:33] [Iter 2039/2250] R3[988/1200], Temp: 0.0751, Energy: -85.038857+0.000438j
[2025-09-02 19:12:13] [Iter 2040/2250] R3[989/1200], Temp: 0.0744, Energy: -85.103869+0.000366j
[2025-09-02 19:12:53] [Iter 2041/2250] R3[990/1200], Temp: 0.0737, Energy: -85.053093-0.000389j
[2025-09-02 19:13:34] [Iter 2042/2250] R3[991/1200], Temp: 0.0730, Energy: -85.016723+0.003077j
[2025-09-02 19:14:14] [Iter 2043/2250] R3[992/1200], Temp: 0.0723, Energy: -85.041545+0.001775j
[2025-09-02 19:14:54] [Iter 2044/2250] R3[993/1200], Temp: 0.0716, Energy: -85.013175+0.001079j
[2025-09-02 19:15:34] [Iter 2045/2250] R3[994/1200], Temp: 0.0710, Energy: -85.078152-0.000961j
[2025-09-02 19:16:15] [Iter 2046/2250] R3[995/1200], Temp: 0.0703, Energy: -85.081639+0.000201j
[2025-09-02 19:16:55] [Iter 2047/2250] R3[996/1200], Temp: 0.0696, Energy: -85.053345-0.000589j
[2025-09-02 19:17:35] [Iter 2048/2250] R3[997/1200], Temp: 0.0690, Energy: -85.071226+0.000871j
[2025-09-02 19:18:15] [Iter 2049/2250] R3[998/1200], Temp: 0.0683, Energy: -85.072469+0.000427j
[2025-09-02 19:18:56] [Iter 2050/2250] R3[999/1200], Temp: 0.0676, Energy: -85.032917+0.001874j
[2025-09-02 19:19:36] [Iter 2051/2250] R3[1000/1200], Temp: 0.0670, Energy: -85.005753+0.001910j
[2025-09-02 19:20:16] [Iter 2052/2250] R3[1001/1200], Temp: 0.0663, Energy: -85.094237-0.000255j
[2025-09-02 19:20:57] [Iter 2053/2250] R3[1002/1200], Temp: 0.0657, Energy: -85.020662-0.000751j
[2025-09-02 19:21:37] [Iter 2054/2250] R3[1003/1200], Temp: 0.0650, Energy: -85.168480+0.001007j
[2025-09-02 19:22:17] [Iter 2055/2250] R3[1004/1200], Temp: 0.0644, Energy: -85.182398+0.003174j
[2025-09-02 19:22:57] [Iter 2056/2250] R3[1005/1200], Temp: 0.0638, Energy: -85.135676+0.000196j
[2025-09-02 19:23:38] [Iter 2057/2250] R3[1006/1200], Temp: 0.0631, Energy: -85.032007-0.001117j
[2025-09-02 19:24:18] [Iter 2058/2250] R3[1007/1200], Temp: 0.0625, Energy: -85.026240-0.000028j
[2025-09-02 19:24:58] [Iter 2059/2250] R3[1008/1200], Temp: 0.0618, Energy: -85.054527+0.000738j
[2025-09-02 19:25:38] [Iter 2060/2250] R3[1009/1200], Temp: 0.0612, Energy: -85.044864+0.001194j
[2025-09-02 19:26:19] [Iter 2061/2250] R3[1010/1200], Temp: 0.0606, Energy: -85.048470-0.001915j
[2025-09-02 19:26:59] [Iter 2062/2250] R3[1011/1200], Temp: 0.0600, Energy: -85.107428-0.002336j
[2025-09-02 19:27:39] [Iter 2063/2250] R3[1012/1200], Temp: 0.0593, Energy: -85.082836-0.000333j
[2025-09-02 19:28:19] [Iter 2064/2250] R3[1013/1200], Temp: 0.0587, Energy: -85.157083+0.000746j
[2025-09-02 19:29:00] [Iter 2065/2250] R3[1014/1200], Temp: 0.0581, Energy: -85.181770+0.002001j
[2025-09-02 19:29:40] [Iter 2066/2250] R3[1015/1200], Temp: 0.0575, Energy: -85.193953-0.001266j
[2025-09-02 19:30:20] [Iter 2067/2250] R3[1016/1200], Temp: 0.0569, Energy: -85.137410+0.000549j
[2025-09-02 19:31:00] [Iter 2068/2250] R3[1017/1200], Temp: 0.0563, Energy: -85.072407-0.001788j
[2025-09-02 19:31:41] [Iter 2069/2250] R3[1018/1200], Temp: 0.0557, Energy: -85.124871+0.001097j
[2025-09-02 19:32:21] [Iter 2070/2250] R3[1019/1200], Temp: 0.0551, Energy: -85.098637+0.000967j
[2025-09-02 19:33:01] [Iter 2071/2250] R3[1020/1200], Temp: 0.0545, Energy: -85.084154+0.000112j
[2025-09-02 19:33:42] [Iter 2072/2250] R3[1021/1200], Temp: 0.0539, Energy: -85.076098-0.002963j
[2025-09-02 19:34:22] [Iter 2073/2250] R3[1022/1200], Temp: 0.0533, Energy: -85.042208+0.001376j
[2025-09-02 19:35:02] [Iter 2074/2250] R3[1023/1200], Temp: 0.0527, Energy: -85.051454+0.000162j
[2025-09-02 19:35:42] [Iter 2075/2250] R3[1024/1200], Temp: 0.0521, Energy: -85.080063-0.000785j
[2025-09-02 19:36:23] [Iter 2076/2250] R3[1025/1200], Temp: 0.0516, Energy: -85.061178+0.002105j
[2025-09-02 19:37:03] [Iter 2077/2250] R3[1026/1200], Temp: 0.0510, Energy: -85.076169+0.001192j
[2025-09-02 19:37:43] [Iter 2078/2250] R3[1027/1200], Temp: 0.0504, Energy: -85.118303-0.001558j
[2025-09-02 19:38:23] [Iter 2079/2250] R3[1028/1200], Temp: 0.0498, Energy: -85.134184-0.001735j
[2025-09-02 19:39:04] [Iter 2080/2250] R3[1029/1200], Temp: 0.0493, Energy: -85.075625+0.003404j
[2025-09-02 19:39:44] [Iter 2081/2250] R3[1030/1200], Temp: 0.0487, Energy: -85.112261-0.001471j
[2025-09-02 19:40:24] [Iter 2082/2250] R3[1031/1200], Temp: 0.0481, Energy: -85.105155+0.000012j
[2025-09-02 19:41:04] [Iter 2083/2250] R3[1032/1200], Temp: 0.0476, Energy: -85.132522-0.000800j
[2025-09-02 19:41:45] [Iter 2084/2250] R3[1033/1200], Temp: 0.0470, Energy: -85.051179+0.000926j
[2025-09-02 19:42:25] [Iter 2085/2250] R3[1034/1200], Temp: 0.0465, Energy: -85.050463-0.000324j
[2025-09-02 19:43:05] [Iter 2086/2250] R3[1035/1200], Temp: 0.0459, Energy: -85.030389-0.001055j
[2025-09-02 19:43:45] [Iter 2087/2250] R3[1036/1200], Temp: 0.0454, Energy: -85.141897-0.003071j
[2025-09-02 19:44:26] [Iter 2088/2250] R3[1037/1200], Temp: 0.0448, Energy: -85.085565-0.000254j
[2025-09-02 19:45:06] [Iter 2089/2250] R3[1038/1200], Temp: 0.0443, Energy: -85.047926-0.001940j
[2025-09-02 19:45:46] [Iter 2090/2250] R3[1039/1200], Temp: 0.0438, Energy: -85.048333+0.000558j
[2025-09-02 19:46:27] [Iter 2091/2250] R3[1040/1200], Temp: 0.0432, Energy: -84.971536+0.001068j
[2025-09-02 19:47:07] [Iter 2092/2250] R3[1041/1200], Temp: 0.0427, Energy: -85.063630-0.000480j
[2025-09-02 19:47:47] [Iter 2093/2250] R3[1042/1200], Temp: 0.0422, Energy: -85.023523-0.002209j
[2025-09-02 19:48:27] [Iter 2094/2250] R3[1043/1200], Temp: 0.0416, Energy: -84.950057-0.000972j
[2025-09-02 19:49:08] [Iter 2095/2250] R3[1044/1200], Temp: 0.0411, Energy: -85.000260-0.003382j
[2025-09-02 19:49:48] [Iter 2096/2250] R3[1045/1200], Temp: 0.0406, Energy: -85.021976+0.001354j
[2025-09-02 19:50:28] [Iter 2097/2250] R3[1046/1200], Temp: 0.0401, Energy: -85.023527+0.001807j
[2025-09-02 19:51:09] [Iter 2098/2250] R3[1047/1200], Temp: 0.0396, Energy: -84.988491+0.000087j
[2025-09-02 19:51:49] [Iter 2099/2250] R3[1048/1200], Temp: 0.0391, Energy: -84.987563+0.000266j
[2025-09-02 19:52:29] [Iter 2100/2250] R3[1049/1200], Temp: 0.0386, Energy: -85.078705+0.001955j
[2025-09-02 19:53:09] [Iter 2101/2250] R3[1050/1200], Temp: 0.0381, Energy: -85.020696-0.000377j
[2025-09-02 19:53:50] [Iter 2102/2250] R3[1051/1200], Temp: 0.0376, Energy: -85.052472-0.000339j
[2025-09-02 19:54:30] [Iter 2103/2250] R3[1052/1200], Temp: 0.0371, Energy: -85.036121-0.003035j
[2025-09-02 19:55:10] [Iter 2104/2250] R3[1053/1200], Temp: 0.0366, Energy: -85.037341+0.000091j
[2025-09-02 19:55:50] [Iter 2105/2250] R3[1054/1200], Temp: 0.0361, Energy: -84.996580+0.000739j
[2025-09-02 19:56:31] [Iter 2106/2250] R3[1055/1200], Temp: 0.0356, Energy: -84.975621-0.001694j
[2025-09-02 19:57:11] [Iter 2107/2250] R3[1056/1200], Temp: 0.0351, Energy: -84.953292+0.001787j
[2025-09-02 19:57:51] [Iter 2108/2250] R3[1057/1200], Temp: 0.0346, Energy: -84.962589+0.001971j
[2025-09-02 19:58:32] [Iter 2109/2250] R3[1058/1200], Temp: 0.0342, Energy: -85.008613-0.001075j
[2025-09-02 19:59:12] [Iter 2110/2250] R3[1059/1200], Temp: 0.0337, Energy: -84.966366-0.001137j
[2025-09-02 19:59:52] [Iter 2111/2250] R3[1060/1200], Temp: 0.0332, Energy: -84.981383+0.001513j
[2025-09-02 20:00:32] [Iter 2112/2250] R3[1061/1200], Temp: 0.0327, Energy: -85.027898-0.002513j
[2025-09-02 20:01:12] [Iter 2113/2250] R3[1062/1200], Temp: 0.0323, Energy: -84.963118-0.000341j
[2025-09-02 20:01:53] [Iter 2114/2250] R3[1063/1200], Temp: 0.0318, Energy: -84.940856+0.002612j
[2025-09-02 20:02:33] [Iter 2115/2250] R3[1064/1200], Temp: 0.0314, Energy: -84.983632-0.005092j
[2025-09-02 20:03:13] [Iter 2116/2250] R3[1065/1200], Temp: 0.0309, Energy: -84.928675+0.000790j
[2025-09-02 20:03:53] [Iter 2117/2250] R3[1066/1200], Temp: 0.0305, Energy: -84.873250-0.001567j
[2025-09-02 20:04:34] [Iter 2118/2250] R3[1067/1200], Temp: 0.0300, Energy: -84.968775+0.001480j
[2025-09-02 20:05:14] [Iter 2119/2250] R3[1068/1200], Temp: 0.0296, Energy: -84.929226+0.002117j
[2025-09-02 20:05:54] [Iter 2120/2250] R3[1069/1200], Temp: 0.0291, Energy: -84.982804+0.000218j
[2025-09-02 20:06:35] [Iter 2121/2250] R3[1070/1200], Temp: 0.0287, Energy: -84.991145-0.000343j
[2025-09-02 20:07:15] [Iter 2122/2250] R3[1071/1200], Temp: 0.0282, Energy: -84.959789-0.001409j
[2025-09-02 20:07:55] [Iter 2123/2250] R3[1072/1200], Temp: 0.0278, Energy: -85.022214+0.001980j
[2025-09-02 20:08:35] [Iter 2124/2250] R3[1073/1200], Temp: 0.0274, Energy: -84.998047+0.001198j
[2025-09-02 20:09:16] [Iter 2125/2250] R3[1074/1200], Temp: 0.0270, Energy: -85.054543-0.000530j
[2025-09-02 20:09:56] [Iter 2126/2250] R3[1075/1200], Temp: 0.0265, Energy: -85.157172-0.004106j
[2025-09-02 20:10:36] [Iter 2127/2250] R3[1076/1200], Temp: 0.0261, Energy: -85.066127-0.001082j
[2025-09-02 20:11:16] [Iter 2128/2250] R3[1077/1200], Temp: 0.0257, Energy: -85.112316+0.000584j
[2025-09-02 20:11:57] [Iter 2129/2250] R3[1078/1200], Temp: 0.0253, Energy: -85.017106-0.001328j
[2025-09-02 20:12:37] [Iter 2130/2250] R3[1079/1200], Temp: 0.0249, Energy: -85.038915-0.001710j
[2025-09-02 20:13:17] [Iter 2131/2250] R3[1080/1200], Temp: 0.0245, Energy: -85.063938+0.000785j
[2025-09-02 20:13:57] [Iter 2132/2250] R3[1081/1200], Temp: 0.0241, Energy: -85.069313-0.000052j
[2025-09-02 20:14:38] [Iter 2133/2250] R3[1082/1200], Temp: 0.0237, Energy: -85.093960+0.002350j
[2025-09-02 20:15:18] [Iter 2134/2250] R3[1083/1200], Temp: 0.0233, Energy: -85.008973-0.001152j
[2025-09-02 20:15:58] [Iter 2135/2250] R3[1084/1200], Temp: 0.0229, Energy: -85.032971+0.000798j
[2025-09-02 20:16:39] [Iter 2136/2250] R3[1085/1200], Temp: 0.0225, Energy: -84.950109+0.002410j
[2025-09-02 20:17:19] [Iter 2137/2250] R3[1086/1200], Temp: 0.0221, Energy: -84.945988+0.001772j
[2025-09-02 20:17:59] [Iter 2138/2250] R3[1087/1200], Temp: 0.0217, Energy: -84.892877+0.001871j
[2025-09-02 20:18:39] [Iter 2139/2250] R3[1088/1200], Temp: 0.0213, Energy: -84.967386-0.000281j
[2025-09-02 20:19:20] [Iter 2140/2250] R3[1089/1200], Temp: 0.0210, Energy: -85.068157-0.000425j
[2025-09-02 20:20:00] [Iter 2141/2250] R3[1090/1200], Temp: 0.0206, Energy: -85.080402-0.002792j
[2025-09-02 20:20:40] [Iter 2142/2250] R3[1091/1200], Temp: 0.0202, Energy: -85.038690+0.000515j
[2025-09-02 20:21:20] [Iter 2143/2250] R3[1092/1200], Temp: 0.0199, Energy: -85.045516-0.001190j
[2025-09-02 20:22:01] [Iter 2144/2250] R3[1093/1200], Temp: 0.0195, Energy: -85.115891-0.004512j
[2025-09-02 20:22:41] [Iter 2145/2250] R3[1094/1200], Temp: 0.0191, Energy: -85.055275-0.000537j
[2025-09-02 20:23:21] [Iter 2146/2250] R3[1095/1200], Temp: 0.0188, Energy: -85.110100-0.001664j
[2025-09-02 20:24:02] [Iter 2147/2250] R3[1096/1200], Temp: 0.0184, Energy: -85.054685-0.000103j
[2025-09-02 20:24:42] [Iter 2148/2250] R3[1097/1200], Temp: 0.0181, Energy: -85.080314+0.000405j
[2025-09-02 20:25:22] [Iter 2149/2250] R3[1098/1200], Temp: 0.0177, Energy: -84.887061+0.002197j
[2025-09-02 20:26:02] [Iter 2150/2250] R3[1099/1200], Temp: 0.0174, Energy: -85.034753-0.003681j
[2025-09-02 20:26:43] [Iter 2151/2250] R3[1100/1200], Temp: 0.0170, Energy: -84.984816+0.000841j
[2025-09-02 20:27:23] [Iter 2152/2250] R3[1101/1200], Temp: 0.0167, Energy: -84.991834+0.000443j
[2025-09-02 20:28:03] [Iter 2153/2250] R3[1102/1200], Temp: 0.0164, Energy: -84.978930-0.000176j
[2025-09-02 20:28:43] [Iter 2154/2250] R3[1103/1200], Temp: 0.0160, Energy: -85.014202-0.000579j
[2025-09-02 20:29:24] [Iter 2155/2250] R3[1104/1200], Temp: 0.0157, Energy: -85.057107-0.001967j
[2025-09-02 20:30:04] [Iter 2156/2250] R3[1105/1200], Temp: 0.0154, Energy: -84.939980-0.000070j
[2025-09-02 20:30:44] [Iter 2157/2250] R3[1106/1200], Temp: 0.0151, Energy: -85.010196-0.000997j
[2025-09-02 20:31:24] [Iter 2158/2250] R3[1107/1200], Temp: 0.0147, Energy: -85.016669+0.000736j
[2025-09-02 20:32:05] [Iter 2159/2250] R3[1108/1200], Temp: 0.0144, Energy: -85.003437-0.001483j
[2025-09-02 20:32:45] [Iter 2160/2250] R3[1109/1200], Temp: 0.0141, Energy: -85.032324-0.000759j
[2025-09-02 20:33:25] [Iter 2161/2250] R3[1110/1200], Temp: 0.0138, Energy: -85.037852+0.000142j
[2025-09-02 20:34:06] [Iter 2162/2250] R3[1111/1200], Temp: 0.0135, Energy: -84.983883+0.000066j
[2025-09-02 20:34:46] [Iter 2163/2250] R3[1112/1200], Temp: 0.0132, Energy: -84.927325+0.002389j
[2025-09-02 20:35:26] [Iter 2164/2250] R3[1113/1200], Temp: 0.0129, Energy: -85.002651+0.001658j
[2025-09-02 20:36:06] [Iter 2165/2250] R3[1114/1200], Temp: 0.0126, Energy: -85.022106+0.000837j
[2025-09-02 20:36:47] [Iter 2166/2250] R3[1115/1200], Temp: 0.0123, Energy: -85.016499-0.001577j
[2025-09-02 20:37:27] [Iter 2167/2250] R3[1116/1200], Temp: 0.0120, Energy: -85.024847-0.001090j
[2025-09-02 20:38:07] [Iter 2168/2250] R3[1117/1200], Temp: 0.0118, Energy: -85.014614-0.000820j
[2025-09-02 20:38:48] [Iter 2169/2250] R3[1118/1200], Temp: 0.0115, Energy: -85.026190+0.001130j
[2025-09-02 20:39:28] [Iter 2170/2250] R3[1119/1200], Temp: 0.0112, Energy: -85.036418-0.001193j
[2025-09-02 20:40:08] [Iter 2171/2250] R3[1120/1200], Temp: 0.0109, Energy: -85.060523+0.001732j
[2025-09-02 20:40:48] [Iter 2172/2250] R3[1121/1200], Temp: 0.0107, Energy: -85.078960-0.002120j
[2025-09-02 20:41:28] [Iter 2173/2250] R3[1122/1200], Temp: 0.0104, Energy: -85.105633-0.002747j
[2025-09-02 20:42:09] [Iter 2174/2250] R3[1123/1200], Temp: 0.0101, Energy: -85.119093+0.003554j
[2025-09-02 20:42:49] [Iter 2175/2250] R3[1124/1200], Temp: 0.0099, Energy: -85.035851-0.001410j
[2025-09-02 20:43:29] [Iter 2176/2250] R3[1125/1200], Temp: 0.0096, Energy: -85.067667-0.000556j
[2025-09-02 20:44:10] [Iter 2177/2250] R3[1126/1200], Temp: 0.0094, Energy: -85.032757-0.001668j
[2025-09-02 20:44:50] [Iter 2178/2250] R3[1127/1200], Temp: 0.0091, Energy: -85.070642+0.001173j
[2025-09-02 20:45:30] [Iter 2179/2250] R3[1128/1200], Temp: 0.0089, Energy: -85.102317+0.000221j
[2025-09-02 20:46:10] [Iter 2180/2250] R3[1129/1200], Temp: 0.0086, Energy: -85.092460+0.000079j
[2025-09-02 20:46:51] [Iter 2181/2250] R3[1130/1200], Temp: 0.0084, Energy: -85.104388-0.002034j
[2025-09-02 20:47:31] [Iter 2182/2250] R3[1131/1200], Temp: 0.0081, Energy: -85.128311+0.000984j
[2025-09-02 20:48:11] [Iter 2183/2250] R3[1132/1200], Temp: 0.0079, Energy: -85.111605-0.001425j
[2025-09-02 20:48:51] [Iter 2184/2250] R3[1133/1200], Temp: 0.0077, Energy: -85.024406-0.000882j
[2025-09-02 20:49:32] [Iter 2185/2250] R3[1134/1200], Temp: 0.0074, Energy: -85.051773-0.002621j
[2025-09-02 20:50:12] [Iter 2186/2250] R3[1135/1200], Temp: 0.0072, Energy: -85.096226+0.000545j
[2025-09-02 20:50:52] [Iter 2187/2250] R3[1136/1200], Temp: 0.0070, Energy: -85.109300+0.000428j
[2025-09-02 20:51:32] [Iter 2188/2250] R3[1137/1200], Temp: 0.0068, Energy: -85.047809-0.000538j
[2025-09-02 20:52:13] [Iter 2189/2250] R3[1138/1200], Temp: 0.0066, Energy: -85.119507-0.000991j
[2025-09-02 20:52:53] [Iter 2190/2250] R3[1139/1200], Temp: 0.0064, Energy: -85.122835-0.002318j
[2025-09-02 20:53:33] [Iter 2191/2250] R3[1140/1200], Temp: 0.0062, Energy: -85.087097+0.002344j
[2025-09-02 20:54:13] [Iter 2192/2250] R3[1141/1200], Temp: 0.0060, Energy: -85.013974-0.000179j
[2025-09-02 20:54:54] [Iter 2193/2250] R3[1142/1200], Temp: 0.0058, Energy: -84.989061+0.002827j
[2025-09-02 20:55:34] [Iter 2194/2250] R3[1143/1200], Temp: 0.0056, Energy: -85.030433+0.000419j
[2025-09-02 20:56:14] [Iter 2195/2250] R3[1144/1200], Temp: 0.0054, Energy: -85.027689-0.000372j
[2025-09-02 20:56:54] [Iter 2196/2250] R3[1145/1200], Temp: 0.0052, Energy: -85.070041-0.002139j
[2025-09-02 20:57:35] [Iter 2197/2250] R3[1146/1200], Temp: 0.0050, Energy: -85.021625-0.000815j
[2025-09-02 20:58:15] [Iter 2198/2250] R3[1147/1200], Temp: 0.0048, Energy: -85.051374+0.005263j
[2025-09-02 20:58:55] [Iter 2199/2250] R3[1148/1200], Temp: 0.0046, Energy: -85.095111+0.000371j
[2025-09-02 20:59:35] [Iter 2200/2250] R3[1149/1200], Temp: 0.0045, Energy: -85.075019+0.000218j
[2025-09-02 21:00:16] [Iter 2201/2250] R3[1150/1200], Temp: 0.0043, Energy: -85.047957-0.000368j
[2025-09-02 21:00:56] [Iter 2202/2250] R3[1151/1200], Temp: 0.0041, Energy: -84.989598-0.000373j
[2025-09-02 21:01:36] [Iter 2203/2250] R3[1152/1200], Temp: 0.0039, Energy: -85.062353-0.000963j
[2025-09-02 21:02:16] [Iter 2204/2250] R3[1153/1200], Temp: 0.0038, Energy: -85.044493-0.000677j
[2025-09-02 21:02:57] [Iter 2205/2250] R3[1154/1200], Temp: 0.0036, Energy: -84.973395+0.003227j
[2025-09-02 21:03:37] [Iter 2206/2250] R3[1155/1200], Temp: 0.0035, Energy: -85.008962-0.001001j
[2025-09-02 21:04:17] [Iter 2207/2250] R3[1156/1200], Temp: 0.0033, Energy: -85.038952-0.001382j
[2025-09-02 21:04:57] [Iter 2208/2250] R3[1157/1200], Temp: 0.0032, Energy: -85.050939-0.000698j
[2025-09-02 21:05:38] [Iter 2209/2250] R3[1158/1200], Temp: 0.0030, Energy: -85.003241+0.002915j
[2025-09-02 21:06:18] [Iter 2210/2250] R3[1159/1200], Temp: 0.0029, Energy: -85.007326+0.001189j
[2025-09-02 21:06:58] [Iter 2211/2250] R3[1160/1200], Temp: 0.0027, Energy: -85.007939+0.001502j
[2025-09-02 21:07:38] [Iter 2212/2250] R3[1161/1200], Temp: 0.0026, Energy: -85.015715+0.000176j
[2025-09-02 21:08:19] [Iter 2213/2250] R3[1162/1200], Temp: 0.0025, Energy: -85.025792-0.001111j
[2025-09-02 21:08:59] [Iter 2214/2250] R3[1163/1200], Temp: 0.0023, Energy: -85.018761+0.001428j
[2025-09-02 21:09:39] [Iter 2215/2250] R3[1164/1200], Temp: 0.0022, Energy: -84.963742+0.000429j
[2025-09-02 21:10:19] [Iter 2216/2250] R3[1165/1200], Temp: 0.0021, Energy: -84.902137+0.000902j
[2025-09-02 21:11:00] [Iter 2217/2250] R3[1166/1200], Temp: 0.0020, Energy: -84.971683-0.000467j
[2025-09-02 21:11:40] [Iter 2218/2250] R3[1167/1200], Temp: 0.0019, Energy: -85.090853-0.001048j
[2025-09-02 21:12:20] [Iter 2219/2250] R3[1168/1200], Temp: 0.0018, Energy: -85.099667-0.001980j
[2025-09-02 21:13:00] [Iter 2220/2250] R3[1169/1200], Temp: 0.0016, Energy: -85.017432+0.001554j
[2025-09-02 21:13:41] [Iter 2221/2250] R3[1170/1200], Temp: 0.0015, Energy: -85.063494+0.002166j
[2025-09-02 21:14:21] [Iter 2222/2250] R3[1171/1200], Temp: 0.0014, Energy: -85.092594+0.000443j
[2025-09-02 21:15:01] [Iter 2223/2250] R3[1172/1200], Temp: 0.0013, Energy: -85.043974+0.000581j
[2025-09-02 21:15:41] [Iter 2224/2250] R3[1173/1200], Temp: 0.0012, Energy: -85.108208+0.000992j
[2025-09-02 21:16:22] [Iter 2225/2250] R3[1174/1200], Temp: 0.0012, Energy: -85.117849+0.000758j
[2025-09-02 21:17:02] [Iter 2226/2250] R3[1175/1200], Temp: 0.0011, Energy: -85.010722+0.002595j
[2025-09-02 21:17:42] [Iter 2227/2250] R3[1176/1200], Temp: 0.0010, Energy: -85.006553-0.001056j
[2025-09-02 21:18:22] [Iter 2228/2250] R3[1177/1200], Temp: 0.0009, Energy: -85.036205+0.002177j
[2025-09-02 21:19:03] [Iter 2229/2250] R3[1178/1200], Temp: 0.0008, Energy: -85.012856+0.000095j
[2025-09-02 21:19:43] [Iter 2230/2250] R3[1179/1200], Temp: 0.0008, Energy: -84.978453+0.000159j
[2025-09-02 21:20:23] [Iter 2231/2250] R3[1180/1200], Temp: 0.0007, Energy: -84.976630+0.000209j
[2025-09-02 21:21:03] [Iter 2232/2250] R3[1181/1200], Temp: 0.0006, Energy: -85.032092-0.000876j
[2025-09-02 21:21:44] [Iter 2233/2250] R3[1182/1200], Temp: 0.0006, Energy: -84.991409-0.000400j
[2025-09-02 21:22:24] [Iter 2234/2250] R3[1183/1200], Temp: 0.0005, Energy: -85.118747+0.002271j
[2025-09-02 21:23:04] [Iter 2235/2250] R3[1184/1200], Temp: 0.0004, Energy: -85.112109+0.003237j
[2025-09-02 21:23:44] [Iter 2236/2250] R3[1185/1200], Temp: 0.0004, Energy: -85.039030+0.001507j
[2025-09-02 21:24:25] [Iter 2237/2250] R3[1186/1200], Temp: 0.0003, Energy: -85.034731+0.001556j
[2025-09-02 21:25:05] [Iter 2238/2250] R3[1187/1200], Temp: 0.0003, Energy: -85.086227+0.000723j
[2025-09-02 21:25:45] [Iter 2239/2250] R3[1188/1200], Temp: 0.0002, Energy: -85.063162-0.000061j
[2025-09-02 21:26:26] [Iter 2240/2250] R3[1189/1200], Temp: 0.0002, Energy: -84.993302-0.002925j
[2025-09-02 21:27:06] [Iter 2241/2250] R3[1190/1200], Temp: 0.0002, Energy: -85.025693+0.002338j
[2025-09-02 21:27:46] [Iter 2242/2250] R3[1191/1200], Temp: 0.0001, Energy: -85.013598-0.000322j
[2025-09-02 21:28:26] [Iter 2243/2250] R3[1192/1200], Temp: 0.0001, Energy: -85.050934+0.001188j
[2025-09-02 21:29:07] [Iter 2244/2250] R3[1193/1200], Temp: 0.0001, Energy: -85.040732+0.002503j
[2025-09-02 21:29:47] [Iter 2245/2250] R3[1194/1200], Temp: 0.0001, Energy: -84.992387-0.002015j
[2025-09-02 21:30:27] [Iter 2246/2250] R3[1195/1200], Temp: 0.0000, Energy: -84.999252+0.002445j
[2025-09-02 21:31:07] [Iter 2247/2250] R3[1196/1200], Temp: 0.0000, Energy: -85.038551+0.000078j
[2025-09-02 21:31:48] [Iter 2248/2250] R3[1197/1200], Temp: 0.0000, Energy: -85.102024-0.001587j
[2025-09-02 21:32:28] [Iter 2249/2250] R3[1198/1200], Temp: 0.0000, Energy: -85.048794+0.003195j
[2025-09-02 21:33:08] [Iter 2250/2250] R3[1199/1200], Temp: 0.0000, Energy: -85.089073+0.000976j
[2025-09-02 21:33:08] ✓ Checkpoint saved: checkpoint_iter_002250.pkl
[2025-09-02 21:33:08] ✅ Training completed | Restarts: 3
[2025-09-02 21:33:08] ============================================================
[2025-09-02 21:33:08] Training completed | Runtime: 90805.5s
[2025-09-02 21:33:23] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-02 21:33:23] ============================================================
