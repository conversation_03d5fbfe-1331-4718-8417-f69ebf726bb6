[2025-09-06 07:57:15] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.02/training/checkpoints/final_GCNN.pkl
[2025-09-06 07:57:15]   - 迭代次数: final
[2025-09-06 07:57:15]   - 能量: -83.613435-0.003008j ± 0.115893
[2025-09-06 07:57:15]   - 时间戳: 2025-09-06T07:56:59.245599+08:00
[2025-09-06 07:57:29] ✓ 变分状态参数已从checkpoint恢复
[2025-09-06 07:57:29] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-06 07:57:29] ==================================================
[2025-09-06 07:57:29] GCNN for Shastry-Sutherland Model
[2025-09-06 07:57:29] ==================================================
[2025-09-06 07:57:29] System parameters:
[2025-09-06 07:57:29]   - System size: L=5, N=100
[2025-09-06 07:57:29]   - System parameters: J1=0.01, J2=0.0, Q=1.0
[2025-09-06 07:57:29] --------------------------------------------------
[2025-09-06 07:57:29] Model parameters:
[2025-09-06 07:57:29]   - Number of layers = 4
[2025-09-06 07:57:29]   - Number of features = 4
[2025-09-06 07:57:29]   - Total parameters = 19628
[2025-09-06 07:57:29] --------------------------------------------------
[2025-09-06 07:57:29] Training parameters:
[2025-09-06 07:57:29]   - Learning rate: 0.015
[2025-09-06 07:57:29]   - Total iterations: 1050
[2025-09-06 07:57:29]   - Annealing cycles: 3
[2025-09-06 07:57:29]   - Initial period: 150
[2025-09-06 07:57:29]   - Period multiplier: 2.0
[2025-09-06 07:57:29]   - Temperature range: 0.0-1.0
[2025-09-06 07:57:29]   - Samples: 4096
[2025-09-06 07:57:29]   - Discarded samples: 0
[2025-09-06 07:57:29]   - Chunk size: 2048
[2025-09-06 07:57:29]   - Diagonal shift: 0.2
[2025-09-06 07:57:29]   - Gradient clipping: 1.0
[2025-09-06 07:57:29]   - Checkpoint enabled: interval=105
[2025-09-06 07:57:29]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.01/training/checkpoints
[2025-09-06 07:57:29] --------------------------------------------------
[2025-09-06 07:57:29] Device status:
[2025-09-06 07:57:29]   - Devices model: NVIDIA H200 NVL
[2025-09-06 07:57:29]   - Number of devices: 1
[2025-09-06 07:57:29]   - Sharding: True
[2025-09-06 07:57:29] ============================================================
[2025-09-06 07:58:29] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -83.144961-0.007521j
[2025-09-06 07:59:16] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -83.232755-0.011221j
[2025-09-06 07:59:46] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -83.129834-0.002378j
[2025-09-06 08:00:17] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -83.267741-0.004606j
[2025-09-06 08:00:47] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -83.391898-0.003319j
[2025-09-06 08:01:18] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -83.353935-0.001503j
[2025-09-06 08:01:48] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -83.416062+0.001811j
[2025-09-06 08:02:19] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -83.347032+0.003426j
[2025-09-06 08:02:49] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -83.461887-0.000146j
[2025-09-06 08:03:19] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -83.175523+0.001056j
[2025-09-06 08:03:50] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -83.104458-0.003168j
[2025-09-06 08:04:20] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -83.424466-0.002794j
[2025-09-06 08:04:51] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -83.325548+0.004462j
[2025-09-06 08:05:21] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -83.340599-0.003206j
[2025-09-06 08:05:51] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -83.251085-0.005030j
[2025-09-06 08:06:14] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -83.120278+0.003672j
[2025-09-06 08:06:36] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -82.985483+0.000811j
[2025-09-06 08:07:06] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -83.029716+0.001526j
[2025-09-06 08:07:37] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -83.194779+0.004378j
[2025-09-06 08:08:08] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -83.200656+0.001177j
[2025-09-06 08:08:38] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -83.182759+0.006942j
[2025-09-06 08:09:09] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -83.158724-0.001702j
[2025-09-06 08:09:40] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -83.210847-0.005517j
[2025-09-06 08:10:10] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -83.131333+0.005280j
[2025-09-06 08:10:41] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -83.102411-0.002777j
[2025-09-06 08:11:09] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -83.157986+0.001348j
[2025-09-06 08:11:37] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -83.073247+0.004713j
[2025-09-06 08:12:08] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -83.147182-0.004688j
[2025-09-06 08:12:39] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -82.972927-0.002092j
[2025-09-06 08:13:09] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -83.039665-0.006243j
[2025-09-06 08:13:40] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -83.021816-0.005822j
[2025-09-06 08:14:10] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -83.105998+0.001260j
[2025-09-06 08:14:41] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -83.207578+0.000008j
[2025-09-06 08:15:11] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -83.245410-0.000419j
[2025-09-06 08:15:42] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -83.057256+0.004780j
[2025-09-06 08:16:12] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -83.206525+0.003541j
[2025-09-06 08:16:43] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -83.058582+0.004614j
[2025-09-06 08:17:13] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -83.152385+0.005807j
[2025-09-06 08:17:44] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -83.224299+0.000164j
[2025-09-06 08:18:14] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -83.183100+0.006364j
[2025-09-06 08:18:45] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -83.115840+0.001917j
[2025-09-06 08:19:15] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -83.149137+0.000911j
[2025-09-06 08:19:46] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -83.236931-0.000059j
[2025-09-06 08:20:16] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -83.187321-0.003960j
[2025-09-06 08:20:47] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -83.354133-0.001982j
[2025-09-06 08:21:17] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -83.208279-0.004385j
[2025-09-06 08:21:48] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -83.283627+0.003160j
[2025-09-06 08:22:18] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -83.280769+0.001544j
[2025-09-06 08:22:49] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -83.341150-0.004665j
[2025-09-06 08:23:19] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -83.399110-0.001021j
[2025-09-06 08:23:50] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -83.261262-0.001816j
[2025-09-06 08:24:20] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -83.154786+0.000593j
[2025-09-06 08:24:51] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -83.160311-0.004365j
[2025-09-06 08:25:21] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -83.106438+0.002447j
[2025-09-06 08:25:52] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -83.139048+0.001262j
[2025-09-06 08:26:14] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -83.161859+0.003120j
[2025-09-06 08:26:37] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -82.899714+0.003662j
[2025-09-06 08:27:07] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -82.909589-0.000123j
[2025-09-06 08:27:38] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -83.048752-0.000509j
[2025-09-06 08:28:09] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -83.167463+0.003227j
[2025-09-06 08:28:39] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -83.194826-0.003957j
[2025-09-06 08:29:10] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -82.984770+0.000549j
[2025-09-06 08:29:40] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -83.092141+0.010430j
[2025-09-06 08:30:11] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -83.081221+0.002113j
[2025-09-06 08:30:42] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -83.166820+0.001552j
[2025-09-06 08:31:09] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -83.281403-0.001983j
[2025-09-06 08:31:38] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -83.204250+0.004574j
[2025-09-06 08:32:09] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -83.193920-0.006740j
[2025-09-06 08:32:39] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -83.366482-0.003737j
[2025-09-06 08:33:10] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -83.197497-0.001502j
[2025-09-06 08:33:41] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -83.136814-0.004586j
[2025-09-06 08:34:11] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -83.242006-0.005112j
[2025-09-06 08:34:42] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -83.093115-0.000061j
[2025-09-06 08:35:12] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -83.043328-0.000659j
[2025-09-06 08:35:43] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -83.115057+0.001213j
[2025-09-06 08:36:14] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -83.219926+0.006357j
[2025-09-06 08:36:44] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -83.272922+0.005524j
[2025-09-06 08:37:15] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -83.170216-0.000388j
[2025-09-06 08:37:45] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -83.152079-0.001232j
[2025-09-06 08:38:16] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -83.129386-0.005899j
[2025-09-06 08:38:46] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -83.173796-0.004948j
[2025-09-06 08:39:17] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -83.103594-0.002827j
[2025-09-06 08:39:48] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -83.154075+0.003059j
[2025-09-06 08:40:18] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -83.059996-0.003611j
[2025-09-06 08:40:49] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -83.130292-0.000560j
[2025-09-06 08:41:19] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -83.040222+0.000166j
[2025-09-06 08:41:50] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -82.879658-0.000900j
[2025-09-06 08:42:20] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -82.900976-0.001895j
[2025-09-06 08:42:51] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -82.976247-0.003445j
[2025-09-06 08:43:22] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -83.133120+0.002047j
[2025-09-06 08:43:52] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -83.074706-0.003675j
[2025-09-06 08:44:23] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -83.093739+0.007802j
[2025-09-06 08:44:53] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -83.238374+0.001957j
[2025-09-06 08:45:24] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -83.165780+0.002249j
[2025-09-06 08:45:54] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -83.117752+0.001571j
[2025-09-06 08:46:15] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -83.117254+0.002424j
[2025-09-06 08:46:40] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -83.097617+0.000613j
[2025-09-06 08:47:11] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -83.360556+0.005373j
[2025-09-06 08:47:42] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -83.251086+0.008306j
[2025-09-06 08:48:12] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -83.177630-0.000063j
[2025-09-06 08:48:43] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -83.058206+0.005561j
[2025-09-06 08:49:14] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -83.286451+0.000349j
[2025-09-06 08:49:44] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -83.089820-0.005348j
[2025-09-06 08:50:15] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -83.039804-0.000671j
[2025-09-06 08:50:46] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -83.037847+0.001954j
[2025-09-06 08:50:46] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-06 08:51:12] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -83.128822-0.000845j
[2025-09-06 08:51:42] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -83.043038+0.005289j
[2025-09-06 08:52:13] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -83.165380-0.003124j
[2025-09-06 08:52:43] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -83.157483+0.001612j
[2025-09-06 08:53:14] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -83.312412+0.004163j
[2025-09-06 08:53:44] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -83.346625-0.001180j
[2025-09-06 08:54:15] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -83.214063+0.001612j
[2025-09-06 08:54:46] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -83.170006+0.004197j
[2025-09-06 08:55:16] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -83.277665-0.000487j
[2025-09-06 08:55:47] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -83.286390-0.002396j
[2025-09-06 08:56:17] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -83.242864-0.000618j
[2025-09-06 08:56:48] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -83.209058-0.001127j
[2025-09-06 08:57:18] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -83.301578+0.003125j
[2025-09-06 08:57:49] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -83.297919+0.006080j
[2025-09-06 08:58:20] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -83.268407-0.000552j
[2025-09-06 08:58:50] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -83.248048-0.000984j
[2025-09-06 08:59:21] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -83.179773+0.002019j
[2025-09-06 08:59:51] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -83.206380-0.000437j
[2025-09-06 09:00:22] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -83.268596-0.000857j
[2025-09-06 09:00:52] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -83.412300-0.000554j
[2025-09-06 09:01:23] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -83.410130-0.000135j
[2025-09-06 09:01:53] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -83.267539+0.001915j
[2025-09-06 09:02:24] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -83.280690+0.000065j
[2025-09-06 09:02:54] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -83.231794+0.001114j
[2025-09-06 09:03:25] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -83.204466-0.000487j
[2025-09-06 09:03:55] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -82.984317+0.003007j
[2025-09-06 09:04:26] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -83.110190-0.005840j
[2025-09-06 09:04:57] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -83.041683-0.012185j
[2025-09-06 09:05:27] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -83.270619-0.008342j
[2025-09-06 09:05:55] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -83.293526+0.000854j
[2025-09-06 09:06:16] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -83.312219+0.001549j
[2025-09-06 09:06:42] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -83.361211-0.001444j
[2025-09-06 09:07:13] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -83.426295+0.007428j
[2025-09-06 09:07:43] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -83.167067+0.000322j
[2025-09-06 09:08:14] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -83.180082-0.001933j
[2025-09-06 09:08:44] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -83.053525+0.000589j
[2025-09-06 09:09:15] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -83.278211-0.001408j
[2025-09-06 09:09:46] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -83.287939-0.003810j
[2025-09-06 09:10:16] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -83.234392+0.003779j
[2025-09-06 09:10:47] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -83.285474+0.001211j
[2025-09-06 09:11:13] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -83.226902+0.004073j
[2025-09-06 09:11:43] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -83.098055-0.005643j
[2025-09-06 09:12:14] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -83.138012+0.001701j
[2025-09-06 09:12:44] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -83.154748-0.009827j
[2025-09-06 09:13:15] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -83.206069+0.003599j
[2025-09-06 09:13:15] RESTART #1 | Period: 300
[2025-09-06 09:13:45] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -83.148970-0.003815j
[2025-09-06 09:14:16] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -83.207937+0.001873j
[2025-09-06 09:14:46] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -82.945214-0.005047j
[2025-09-06 09:15:17] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -83.067446-0.000058j
[2025-09-06 09:15:47] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -83.072747-0.001354j
[2025-09-06 09:16:18] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -83.105427-0.003186j
[2025-09-06 09:16:48] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -82.960659-0.004402j
[2025-09-06 09:17:19] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -83.143672-0.003341j
[2025-09-06 09:17:49] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -83.174105+0.003116j
[2025-09-06 09:18:20] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -83.089390-0.003779j
[2025-09-06 09:18:50] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -83.017716+0.003346j
[2025-09-06 09:19:21] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -83.104334-0.002828j
[2025-09-06 09:19:51] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -83.179996+0.002161j
[2025-09-06 09:20:22] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -83.085444-0.003617j
[2025-09-06 09:20:52] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -83.172557-0.008334j
[2025-09-06 09:21:23] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -83.184800+0.000043j
[2025-09-06 09:21:54] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -83.108363+0.003792j
[2025-09-06 09:22:24] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -82.982641-0.004408j
[2025-09-06 09:22:55] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -82.933414+0.006526j
[2025-09-06 09:23:25] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -82.850017+0.001777j
[2025-09-06 09:23:56] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -83.026286+0.001380j
[2025-09-06 09:24:26] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -83.004969-0.006475j
[2025-09-06 09:24:57] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -83.111356-0.001211j
[2025-09-06 09:25:28] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -83.047233-0.000974j
[2025-09-06 09:25:55] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -83.182625+0.000129j
[2025-09-06 09:26:15] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -83.248783-0.004795j
[2025-09-06 09:26:43] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -83.235890-0.003402j
[2025-09-06 09:27:14] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -83.214605+0.000401j
[2025-09-06 09:27:44] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -83.254813-0.000911j
[2025-09-06 09:28:15] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -83.298060-0.002661j
[2025-09-06 09:28:45] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -83.193514-0.006490j
[2025-09-06 09:29:16] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -83.106122-0.001596j
[2025-09-06 09:29:47] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -83.174290-0.003011j
[2025-09-06 09:30:17] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -83.243911-0.001759j
[2025-09-06 09:30:46] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -83.307950+0.004440j
[2025-09-06 09:31:14] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -83.161294+0.001093j
[2025-09-06 09:31:44] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -83.264886-0.003773j
[2025-09-06 09:32:15] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -83.053165-0.001235j
[2025-09-06 09:32:45] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -82.990531+0.002701j
[2025-09-06 09:33:16] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -83.064039-0.000186j
[2025-09-06 09:33:47] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -83.120185-0.002433j
[2025-09-06 09:34:17] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -83.104392-0.001850j
[2025-09-06 09:34:48] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -83.143205-0.003280j
[2025-09-06 09:35:18] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -83.146317+0.005307j
[2025-09-06 09:35:49] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -83.125450-0.000100j
[2025-09-06 09:36:20] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -83.048345+0.002248j
[2025-09-06 09:36:50] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -83.012890+0.001689j
[2025-09-06 09:37:21] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -83.086611+0.000425j
[2025-09-06 09:37:51] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -83.039486-0.001271j
[2025-09-06 09:38:22] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -83.160417-0.001964j
[2025-09-06 09:38:53] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -83.001934+0.002244j
[2025-09-06 09:39:23] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -83.057181+0.001850j
[2025-09-06 09:39:54] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -82.902164+0.003105j
[2025-09-06 09:40:25] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -83.000187+0.001019j
[2025-09-06 09:40:55] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -83.011302-0.004715j
[2025-09-06 09:41:26] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -82.871099+0.004564j
[2025-09-06 09:41:56] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -83.099521+0.001839j
[2025-09-06 09:42:27] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -83.060982+0.003220j
[2025-09-06 09:42:58] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -83.117429-0.000903j
[2025-09-06 09:43:28] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -83.120780+0.001232j
[2025-09-06 09:43:28] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-06 09:43:59] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -83.086929-0.005065j
[2025-09-06 09:44:29] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -83.098619-0.004678j
[2025-09-06 09:45:00] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -83.224991-0.003532j
[2025-09-06 09:45:31] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -83.202819-0.005297j
[2025-09-06 09:45:56] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -83.268112+0.000254j
[2025-09-06 09:46:17] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -83.264713+0.007276j
[2025-09-06 09:46:48] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -83.130510-0.002747j
[2025-09-06 09:47:18] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -83.040413-0.003491j
[2025-09-06 09:47:49] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -82.967159-0.005865j
[2025-09-06 09:48:20] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -83.144522-0.004432j
[2025-09-06 09:48:50] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -83.208848-0.004650j
[2025-09-06 09:49:21] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -83.388381-0.006615j
[2025-09-06 09:49:52] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -83.422675+0.005323j
[2025-09-06 09:50:22] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -83.312862+0.001076j
[2025-09-06 09:50:51] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -83.229592-0.003421j
[2025-09-06 09:51:19] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -83.365081-0.003374j
[2025-09-06 09:51:50] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -83.302451-0.001093j
[2025-09-06 09:52:20] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -83.334598+0.001873j
[2025-09-06 09:52:51] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -83.449586+0.005227j
[2025-09-06 09:53:22] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -83.150985-0.001848j
[2025-09-06 09:53:52] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -83.318647-0.000157j
[2025-09-06 09:54:23] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -83.570800+0.000879j
[2025-09-06 09:54:53] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -83.393531+0.002859j
[2025-09-06 09:55:24] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -83.390922+0.003142j
[2025-09-06 09:55:55] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -83.281619-0.005079j
[2025-09-06 09:56:25] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -83.308464-0.002743j
[2025-09-06 09:56:56] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -83.348056-0.001556j
[2025-09-06 09:57:27] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -83.223018-0.000391j
[2025-09-06 09:57:57] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -83.162697+0.001850j
[2025-09-06 09:58:28] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -83.257665-0.002926j
[2025-09-06 09:58:58] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -83.340702+0.002494j
[2025-09-06 09:59:29] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -83.200929-0.002058j
[2025-09-06 10:00:00] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -83.201240+0.000477j
[2025-09-06 10:00:30] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -83.176587+0.005011j
[2025-09-06 10:01:01] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -83.180227-0.003482j
[2025-09-06 10:01:31] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -83.213939-0.000730j
[2025-09-06 10:02:02] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -83.317344+0.001107j
[2025-09-06 10:02:33] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -83.124488-0.004076j
[2025-09-06 10:03:03] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -83.118530+0.000191j
[2025-09-06 10:03:34] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -83.201607+0.003467j
[2025-09-06 10:04:04] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -83.025456+0.001617j
[2025-09-06 10:04:35] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -83.072602+0.001783j
[2025-09-06 10:05:06] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -83.113303-0.000641j
[2025-09-06 10:05:36] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -83.138937+0.002302j
[2025-09-06 10:05:59] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -83.144590-0.004588j
[2025-09-06 10:06:20] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -83.056994+0.001237j
[2025-09-06 10:06:50] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -83.169741-0.001075j
[2025-09-06 10:07:21] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -83.150071-0.003238j
[2025-09-06 10:07:51] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -83.015395+0.003307j
[2025-09-06 10:08:22] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -82.944957+0.001670j
[2025-09-06 10:08:53] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -82.984307+0.000536j
[2025-09-06 10:09:23] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -83.098189+0.001041j
[2025-09-06 10:09:54] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -83.170309-0.003576j
[2025-09-06 10:10:25] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -83.048184-0.001477j
[2025-09-06 10:10:53] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -83.051896+0.002968j
[2025-09-06 10:11:21] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -82.927690-0.003883j
[2025-09-06 10:11:52] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -83.051099-0.008500j
[2025-09-06 10:12:22] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -82.983228-0.002146j
[2025-09-06 10:12:53] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -83.089886+0.000417j
[2025-09-06 10:13:23] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -83.090376+0.000576j
[2025-09-06 10:13:54] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -83.289510+0.000874j
[2025-09-06 10:14:24] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -83.116764+0.003801j
[2025-09-06 10:14:55] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -83.033918+0.000268j
[2025-09-06 10:15:25] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -83.068779+0.000232j
[2025-09-06 10:15:56] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -83.061490+0.000131j
[2025-09-06 10:16:26] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -83.030428-0.000256j
[2025-09-06 10:16:57] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -83.105797-0.004713j
[2025-09-06 10:17:27] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -83.040081+0.002353j
[2025-09-06 10:17:58] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -83.051509+0.000074j
[2025-09-06 10:18:29] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -83.122038-0.005486j
[2025-09-06 10:18:59] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -83.179452-0.003413j
[2025-09-06 10:19:30] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -83.100992+0.005195j
[2025-09-06 10:20:00] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -83.122191-0.000437j
[2025-09-06 10:20:31] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -83.180069-0.004829j
[2025-09-06 10:21:01] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -83.208272+0.001270j
[2025-09-06 10:21:32] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -83.196542+0.000975j
[2025-09-06 10:22:02] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -83.204454+0.001340j
[2025-09-06 10:22:33] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -83.291224+0.003670j
[2025-09-06 10:23:03] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -83.262011-0.000062j
[2025-09-06 10:23:34] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -83.200832+0.007528j
[2025-09-06 10:24:04] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -83.104509+0.000195j
[2025-09-06 10:24:35] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -83.091790+0.001900j
[2025-09-06 10:25:05] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -83.096579-0.000957j
[2025-09-06 10:25:36] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -83.021092-0.001500j
[2025-09-06 10:25:59] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -82.902836-0.003251j
[2025-09-06 10:26:22] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -82.829353-0.002542j
[2025-09-06 10:26:53] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -82.948035-0.002501j
[2025-09-06 10:27:23] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -82.938016-0.001618j
[2025-09-06 10:27:54] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -82.953540-0.005671j
[2025-09-06 10:28:25] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -82.991940+0.006257j
[2025-09-06 10:28:55] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -82.987151-0.001213j
[2025-09-06 10:29:26] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -83.209544-0.008413j
[2025-09-06 10:29:56] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -83.358169-0.000751j
[2025-09-06 10:30:27] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -83.272422-0.002888j
[2025-09-06 10:30:53] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -83.245793+0.001800j
[2025-09-06 10:31:23] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -83.219048-0.007296j
[2025-09-06 10:31:54] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -83.071768+0.000392j
[2025-09-06 10:32:24] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -82.841353-0.001395j
[2025-09-06 10:32:55] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -83.068447+0.000165j
[2025-09-06 10:33:25] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -83.107811+0.006707j
[2025-09-06 10:33:56] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -83.088133-0.002930j
[2025-09-06 10:34:26] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -83.053114+0.003006j
[2025-09-06 10:34:57] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -83.106674-0.000851j
[2025-09-06 10:35:27] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -83.016519-0.000059j
[2025-09-06 10:35:58] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -83.043913-0.001925j
[2025-09-06 10:35:58] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-06 10:36:29] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -83.137835-0.003228j
[2025-09-06 10:36:59] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -83.086161-0.000755j
[2025-09-06 10:37:30] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -83.139089+0.001166j
[2025-09-06 10:38:00] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -83.112053-0.002425j
[2025-09-06 10:38:30] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -83.143921+0.002397j
[2025-09-06 10:39:01] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -83.269878+0.002161j
[2025-09-06 10:39:31] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -83.286147+0.002527j
[2025-09-06 10:40:02] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -83.162320+0.000938j
[2025-09-06 10:40:32] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -83.134858+0.003616j
[2025-09-06 10:41:03] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -83.070123-0.001532j
[2025-09-06 10:41:33] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -83.107856-0.001463j
[2025-09-06 10:42:04] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -82.984664-0.003266j
[2025-09-06 10:42:34] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -83.054705+0.001356j
[2025-09-06 10:43:05] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -82.945079-0.005572j
[2025-09-06 10:43:35] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -83.107246-0.000618j
[2025-09-06 10:44:06] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -83.042106+0.000666j
[2025-09-06 10:44:36] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -83.091971-0.002482j
[2025-09-06 10:45:07] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -83.172021-0.000661j
[2025-09-06 10:45:37] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -83.116532-0.008016j
[2025-09-06 10:45:58] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -83.098523+0.004125j
[2025-09-06 10:46:23] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -83.081005-0.000228j
[2025-09-06 10:46:54] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -82.936383+0.004292j
[2025-09-06 10:47:25] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -83.030911+0.001079j
[2025-09-06 10:47:55] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -83.193499-0.001779j
[2025-09-06 10:48:26] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -83.290017+0.004354j
[2025-09-06 10:48:56] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -83.268800+0.001210j
[2025-09-06 10:49:27] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -83.214154-0.000865j
[2025-09-06 10:49:58] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -83.069135-0.003491j
[2025-09-06 10:50:28] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -83.197319-0.000213j
[2025-09-06 10:50:54] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -83.195961+0.000153j
[2025-09-06 10:51:25] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -83.310156+0.003200j
[2025-09-06 10:51:55] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -83.218360+0.000007j
[2025-09-06 10:52:26] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -83.243726-0.002361j
[2025-09-06 10:52:56] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -83.301320+0.004378j
[2025-09-06 10:53:27] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -83.312244-0.000270j
[2025-09-06 10:53:58] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -83.140619+0.000715j
[2025-09-06 10:54:28] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -83.295067+0.001225j
[2025-09-06 10:54:59] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -83.285426-0.001948j
[2025-09-06 10:55:29] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -83.232582-0.001263j
[2025-09-06 10:56:00] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -83.003315-0.002540j
[2025-09-06 10:56:30] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -83.199817-0.001394j
[2025-09-06 10:57:01] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -83.066372+0.002141j
[2025-09-06 10:57:31] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -83.213479+0.004213j
[2025-09-06 10:58:02] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -83.227598-0.000254j
[2025-09-06 10:58:33] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -83.030894-0.002549j
[2025-09-06 10:59:03] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -83.297678+0.001637j
[2025-09-06 10:59:34] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -83.148992+0.000902j
[2025-09-06 11:00:04] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -83.077224-0.000595j
[2025-09-06 11:00:35] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -82.982179-0.005453j
[2025-09-06 11:01:05] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -83.046109-0.000826j
[2025-09-06 11:01:36] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -83.012561+0.001430j
[2025-09-06 11:02:07] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -83.116247-0.000930j
[2025-09-06 11:02:37] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -83.048829+0.005936j
[2025-09-06 11:03:08] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -83.136362-0.003611j
[2025-09-06 11:03:38] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -83.205405-0.001208j
[2025-09-06 11:04:09] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -83.193812+0.000054j
[2025-09-06 11:04:39] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -83.250865-0.004746j
[2025-09-06 11:05:10] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -83.055018-0.001169j
[2025-09-06 11:05:38] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -83.064319+0.001439j
[2025-09-06 11:05:58] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -83.124591+0.001022j
[2025-09-06 11:06:26] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -83.194727+0.000715j
[2025-09-06 11:06:57] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -83.145591-0.010375j
[2025-09-06 11:07:28] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -83.179412+0.001648j
[2025-09-06 11:07:58] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -83.225235-0.002502j
[2025-09-06 11:08:29] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -83.226822-0.000051j
[2025-09-06 11:09:00] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -83.130222-0.000664j
[2025-09-06 11:09:30] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -83.157366+0.000522j
[2025-09-06 11:10:01] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -83.207200+0.000368j
[2025-09-06 11:10:29] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -83.156051+0.003569j
[2025-09-06 11:10:58] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -83.217553+0.000267j
[2025-09-06 11:11:28] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -83.211326-0.004154j
[2025-09-06 11:11:59] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -83.213296-0.000644j
[2025-09-06 11:12:29] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -83.030866-0.003344j
[2025-09-06 11:13:00] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -83.234825-0.001530j
[2025-09-06 11:13:31] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -83.221051-0.004074j
[2025-09-06 11:14:01] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -83.109757+0.002497j
[2025-09-06 11:14:32] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -83.241836-0.002085j
[2025-09-06 11:15:03] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -83.294303-0.000428j
[2025-09-06 11:15:33] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -83.134819-0.002323j
[2025-09-06 11:16:04] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -83.145407-0.003080j
[2025-09-06 11:16:35] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -83.022047+0.000957j
[2025-09-06 11:17:05] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -82.945896-0.000932j
[2025-09-06 11:17:36] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -82.823066+0.001310j
[2025-09-06 11:18:06] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -82.880007-0.000188j
[2025-09-06 11:18:37] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -82.963093+0.010017j
[2025-09-06 11:19:08] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -83.249286-0.000395j
[2025-09-06 11:19:38] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -83.307481-0.005318j
[2025-09-06 11:20:09] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -83.291971+0.000149j
[2025-09-06 11:20:40] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -83.248813+0.002224j
[2025-09-06 11:21:10] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -83.159927-0.000829j
[2025-09-06 11:21:41] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -83.308455-0.004085j
[2025-09-06 11:22:12] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -83.179635+0.000623j
[2025-09-06 11:22:42] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -83.370019+0.000930j
[2025-09-06 11:23:13] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -83.288188+0.001559j
[2025-09-06 11:23:43] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -83.355597-0.002841j
[2025-09-06 11:24:14] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -83.222926+0.001719j
[2025-09-06 11:24:45] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -83.445155-0.000304j
[2025-09-06 11:25:15] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -83.374492+0.001148j
[2025-09-06 11:25:40] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -83.439113-0.000742j
[2025-09-06 11:26:02] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -83.320555-0.000797j
[2025-09-06 11:26:32] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -83.397553+0.000517j
[2025-09-06 11:27:03] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -83.353005+0.000673j
[2025-09-06 11:27:33] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -83.322156+0.002273j
[2025-09-06 11:28:04] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -83.158997-0.005347j
[2025-09-06 11:28:35] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -83.147357+0.000498j
[2025-09-06 11:28:35] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-06 11:29:06] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -83.079922-0.004634j
[2025-09-06 11:29:36] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -83.048467-0.004924j
[2025-09-06 11:30:07] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -83.048925+0.000264j
[2025-09-06 11:30:35] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -83.003738-0.005799j
[2025-09-06 11:31:03] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -83.123765-0.001022j
[2025-09-06 11:31:34] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -83.182569+0.001494j
[2025-09-06 11:32:05] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -83.197485+0.000087j
[2025-09-06 11:32:35] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -83.196700+0.003105j
[2025-09-06 11:33:06] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -83.246540-0.001719j
[2025-09-06 11:33:37] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -83.160052+0.005350j
[2025-09-06 11:34:07] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -83.238988-0.005216j
[2025-09-06 11:34:38] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -83.231889-0.003205j
[2025-09-06 11:35:09] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -83.249750+0.001100j
[2025-09-06 11:35:39] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -83.287426+0.002072j
[2025-09-06 11:36:10] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -83.310319+0.004686j
[2025-09-06 11:36:41] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -83.290072-0.004376j
[2025-09-06 11:37:11] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -83.273104-0.003340j
[2025-09-06 11:37:42] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -83.179111-0.004027j
[2025-09-06 11:38:13] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -83.046282-0.002103j
[2025-09-06 11:38:43] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -83.061679+0.000594j
[2025-09-06 11:39:14] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -83.139997+0.007024j
[2025-09-06 11:39:45] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -83.070851+0.000981j
[2025-09-06 11:40:15] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -83.073429+0.001622j
[2025-09-06 11:40:46] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -83.076635-0.004409j
[2025-09-06 11:41:17] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -83.270464-0.001818j
[2025-09-06 11:41:47] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -83.163116-0.004634j
[2025-09-06 11:42:18] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -83.135409+0.000301j
[2025-09-06 11:42:49] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -83.172157+0.006598j
[2025-09-06 11:43:19] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -83.165154+0.001939j
[2025-09-06 11:43:50] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -83.019863+0.003190j
[2025-09-06 11:43:50] RESTART #2 | Period: 600
[2025-09-06 11:44:21] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -83.110789-0.000641j
[2025-09-06 11:44:51] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -83.203915+0.002100j
[2025-09-06 11:45:22] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -83.243198+0.000411j
[2025-09-06 11:45:44] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -83.290053+0.001092j
[2025-09-06 11:46:06] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -83.231356-0.000234j
[2025-09-06 11:46:36] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -83.253300+0.001518j
[2025-09-06 11:47:07] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -83.122084+0.004109j
[2025-09-06 11:47:38] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -82.987047-0.001218j
[2025-09-06 11:48:08] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -83.235079+0.000940j
[2025-09-06 11:48:39] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -83.155832-0.001590j
[2025-09-06 11:49:09] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -83.214742+0.000378j
[2025-09-06 11:49:40] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -83.123795-0.002488j
[2025-09-06 11:50:11] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -83.171926+0.004012j
[2025-09-06 11:50:38] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -83.135260-0.001164j
[2025-09-06 11:51:07] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -83.041160-0.002449j
[2025-09-06 11:51:38] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -83.119252+0.005268j
[2025-09-06 11:52:08] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -83.141942+0.000543j
[2025-09-06 11:52:39] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -83.243711+0.000633j
[2025-09-06 11:53:09] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -83.210287+0.002295j
[2025-09-06 11:53:40] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -83.026826-0.003030j
[2025-09-06 11:54:10] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -83.139143+0.000439j
[2025-09-06 11:54:41] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -83.068455+0.001898j
[2025-09-06 11:55:11] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -83.156754-0.000146j
[2025-09-06 11:55:42] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -83.320954+0.002965j
[2025-09-06 11:56:12] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -83.368217-0.001357j
[2025-09-06 11:56:43] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -83.316034+0.000598j
[2025-09-06 11:57:14] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -83.318647+0.001962j
[2025-09-06 11:57:44] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -83.186063+0.001498j
[2025-09-06 11:58:15] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -83.177950+0.000686j
[2025-09-06 11:58:45] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -83.211593+0.000905j
[2025-09-06 11:59:16] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -83.028944+0.001107j
[2025-09-06 11:59:46] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -83.184357-0.007549j
[2025-09-06 12:00:17] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -83.120005-0.003838j
[2025-09-06 12:00:47] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -83.294134-0.002901j
[2025-09-06 12:01:18] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -83.082807-0.003252j
[2025-09-06 12:01:48] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -83.058090-0.000303j
[2025-09-06 12:02:19] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -82.975811+0.006171j
[2025-09-06 12:02:49] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -82.895151-0.000301j
[2025-09-06 12:03:20] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -83.059992+0.002055j
[2025-09-06 12:03:50] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -83.135967+0.002929j
[2025-09-06 12:04:21] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -82.987922+0.005060j
[2025-09-06 12:04:51] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -83.136537-0.001573j
[2025-09-06 12:05:22] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -83.287708+0.002138j
[2025-09-06 12:05:43] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -83.189769-0.003544j
[2025-09-06 12:06:07] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -83.189274+0.002857j
[2025-09-06 12:06:38] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -83.187198-0.002672j
[2025-09-06 12:07:09] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -83.139457+0.002178j
[2025-09-06 12:07:39] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -83.264546+0.004841j
[2025-09-06 12:08:10] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -83.232728+0.001801j
[2025-09-06 12:08:41] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -83.213853+0.001472j
[2025-09-06 12:09:11] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -83.059760-0.003244j
[2025-09-06 12:09:42] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -82.902764+0.005805j
[2025-09-06 12:10:12] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -82.921223-0.002772j
[2025-09-06 12:10:38] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -83.140184-0.002635j
[2025-09-06 12:11:09] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -83.073598+0.001237j
[2025-09-06 12:11:39] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -83.260732+0.000481j
[2025-09-06 12:12:10] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -83.160716-0.000350j
[2025-09-06 12:12:40] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -83.043105+0.003525j
[2025-09-06 12:13:11] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -83.117577+0.004372j
[2025-09-06 12:13:41] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -83.259335-0.002394j
[2025-09-06 12:14:12] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -83.245225-0.006831j
[2025-09-06 12:14:42] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -83.217376-0.000858j
[2025-09-06 12:15:13] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -83.231226-0.000575j
[2025-09-06 12:15:43] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -83.182393+0.004292j
[2025-09-06 12:16:14] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -83.150575-0.002928j
[2025-09-06 12:16:44] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -83.130033-0.003249j
[2025-09-06 12:17:15] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -83.060834-0.005643j
[2025-09-06 12:17:45] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -83.118703-0.001139j
[2025-09-06 12:18:16] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -83.072991-0.001544j
[2025-09-06 12:18:46] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -83.192585-0.001277j
[2025-09-06 12:19:17] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -83.199218-0.005256j
[2025-09-06 12:19:47] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -83.364670-0.004632j
[2025-09-06 12:20:18] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -83.237730-0.000738j
[2025-09-06 12:20:48] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -83.266887-0.002175j
[2025-09-06 12:21:19] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -83.518395+0.000553j
[2025-09-06 12:21:19] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-06 12:21:49] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -83.427654+0.002027j
[2025-09-06 12:22:20] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -83.352305+0.001020j
[2025-09-06 12:22:51] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -83.138360-0.003646j
[2025-09-06 12:23:21] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -83.219234-0.003007j
[2025-09-06 12:23:52] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -83.030944-0.001863j
[2025-09-06 12:24:22] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -83.117323-0.004031j
[2025-09-06 12:24:53] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -83.021484-0.000009j
[2025-09-06 12:25:22] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -83.069690+0.003205j
[2025-09-06 12:25:43] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -83.151623-0.002960j
[2025-09-06 12:26:06] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -83.086663-0.001802j
[2025-09-06 12:26:37] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -83.040935+0.001163j
[2025-09-06 12:27:07] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -83.031630+0.001269j
[2025-09-06 12:27:38] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -82.929437-0.000613j
[2025-09-06 12:28:08] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -83.172475+0.001979j
[2025-09-06 12:28:39] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -83.149885-0.001395j
[2025-09-06 12:29:10] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -83.084888-0.002894j
[2025-09-06 12:29:40] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -83.080906-0.002380j
[2025-09-06 12:30:11] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -83.047322-0.002519j
[2025-09-06 12:30:37] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -83.084857+0.002395j
[2025-09-06 12:31:07] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -83.094941+0.003214j
[2025-09-06 12:31:38] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -83.065890-0.003009j
[2025-09-06 12:32:08] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -83.106129-0.007775j
[2025-09-06 12:32:39] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -83.265810-0.005979j
[2025-09-06 12:33:10] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -83.274244+0.008570j
[2025-09-06 12:33:40] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -83.279669-0.001249j
[2025-09-06 12:34:11] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -83.331241+0.001532j
[2025-09-06 12:34:41] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -83.318433+0.003449j
[2025-09-06 12:35:12] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -83.282098+0.001283j
[2025-09-06 12:35:43] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -83.390617-0.007243j
[2025-09-06 12:36:13] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -83.170881+0.003115j
[2025-09-06 12:36:44] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -83.271737+0.000204j
[2025-09-06 12:37:14] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -83.411930-0.000692j
[2025-09-06 12:37:45] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -83.453178+0.001069j
[2025-09-06 12:38:16] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -83.421440-0.000956j
[2025-09-06 12:38:46] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -83.313540+0.001161j
[2025-09-06 12:39:17] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -83.208939-0.002507j
[2025-09-06 12:39:47] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -83.262354+0.002684j
[2025-09-06 12:40:18] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -83.119720+0.000693j
[2025-09-06 12:40:49] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -83.280033+0.000413j
[2025-09-06 12:41:19] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -83.148774-0.000948j
[2025-09-06 12:41:50] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -83.135944-0.006343j
[2025-09-06 12:42:20] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -83.154211+0.001613j
[2025-09-06 12:42:51] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -83.269634+0.001327j
[2025-09-06 12:43:22] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -83.459973+0.000357j
[2025-09-06 12:43:52] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -83.212036+0.000154j
[2025-09-06 12:44:23] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -83.145595-0.001913j
[2025-09-06 12:44:53] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -83.140173-0.002334j
[2025-09-06 12:45:23] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -83.163521-0.002254j
[2025-09-06 12:45:44] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -83.219534+0.003423j
[2025-09-06 12:46:10] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -83.249106+0.003732j
[2025-09-06 12:46:41] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -83.238321-0.001596j
[2025-09-06 12:47:12] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -83.251258+0.002587j
[2025-09-06 12:47:42] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -83.221797-0.003920j
[2025-09-06 12:48:13] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -83.269171+0.000468j
[2025-09-06 12:48:44] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -83.155813-0.001723j
[2025-09-06 12:49:14] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -83.266217-0.006358j
[2025-09-06 12:49:45] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -83.319470+0.002263j
[2025-09-06 12:50:15] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -83.258254+0.003337j
[2025-09-06 12:50:42] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -83.317815+0.004316j
[2025-09-06 12:51:12] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -83.274180+0.000806j
[2025-09-06 12:51:43] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -83.309403+0.003764j
[2025-09-06 12:52:14] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -83.217715+0.002207j
[2025-09-06 12:52:44] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -83.253541-0.001602j
[2025-09-06 12:53:15] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -83.266501-0.001054j
[2025-09-06 12:53:46] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -83.235192-0.004733j
[2025-09-06 12:54:16] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -83.279196-0.000345j
[2025-09-06 12:54:47] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -83.331963-0.000485j
[2025-09-06 12:55:17] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -83.486201+0.001164j
[2025-09-06 12:55:48] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -83.441962+0.001864j
[2025-09-06 12:56:19] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -83.433662-0.006393j
[2025-09-06 12:56:49] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -83.368831-0.003397j
[2025-09-06 12:57:20] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -83.365853-0.001531j
[2025-09-06 12:57:51] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -83.294570-0.002801j
[2025-09-06 12:58:21] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -83.380308-0.000509j
[2025-09-06 12:58:52] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -83.186185-0.003079j
[2025-09-06 12:59:22] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -83.306236+0.001427j
[2025-09-06 12:59:53] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -83.202756+0.001013j
[2025-09-06 13:00:24] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -83.237443-0.002353j
[2025-09-06 13:00:54] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -83.020439-0.000673j
[2025-09-06 13:01:25] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -83.042004+0.002162j
[2025-09-06 13:01:55] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -82.975353+0.000632j
[2025-09-06 13:02:26] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -82.998163-0.005295j
[2025-09-06 13:02:57] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -83.079581-0.000511j
[2025-09-06 13:03:27] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -83.083071-0.001169j
[2025-09-06 13:03:58] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -82.920493+0.000064j
[2025-09-06 13:04:28] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -83.147670-0.000723j
[2025-09-06 13:04:59] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -83.066680-0.000157j
[2025-09-06 13:05:26] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -83.095279+0.001200j
[2025-09-06 13:05:46] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -83.215280-0.002466j
[2025-09-06 13:06:15] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -83.194258-0.006752j
[2025-09-06 13:06:46] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -83.313101-0.000952j
[2025-09-06 13:07:16] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -83.189906+0.000976j
[2025-09-06 13:07:47] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -83.203373-0.000539j
[2025-09-06 13:08:18] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -83.268640+0.000148j
[2025-09-06 13:08:48] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -83.262259-0.001361j
[2025-09-06 13:09:19] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -83.117015-0.001703j
[2025-09-06 13:09:50] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -83.178878+0.000966j
[2025-09-06 13:10:18] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -83.247543-0.000826j
[2025-09-06 13:10:46] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -83.085241-0.004239j
[2025-09-06 13:11:17] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -83.226208+0.000215j
[2025-09-06 13:11:48] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -83.318251-0.000500j
[2025-09-06 13:12:18] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -83.346944+0.002714j
[2025-09-06 13:12:49] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -83.292207+0.000666j
[2025-09-06 13:13:19] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -83.248674-0.001869j
[2025-09-06 13:13:50] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -83.230569-0.002159j
[2025-09-06 13:13:50] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-06 13:14:21] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -83.159208+0.000596j
[2025-09-06 13:14:51] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -83.174063+0.002697j
[2025-09-06 13:15:22] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -83.277463-0.001384j
[2025-09-06 13:15:53] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -83.054516+0.004996j
[2025-09-06 13:16:23] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -83.163285+0.003425j
[2025-09-06 13:16:54] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -82.997131+0.000492j
[2025-09-06 13:17:25] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -82.969902-0.001907j
[2025-09-06 13:17:55] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -83.132023+0.014789j
[2025-09-06 13:18:26] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -83.068750+0.002762j
[2025-09-06 13:18:57] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -83.174397+0.002103j
[2025-09-06 13:19:27] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -83.163911+0.000728j
[2025-09-06 13:19:58] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -82.991927+0.004356j
[2025-09-06 13:20:29] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -82.998626+0.001102j
[2025-09-06 13:20:59] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -82.905742+0.002501j
[2025-09-06 13:21:30] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -83.107692-0.002324j
[2025-09-06 13:22:01] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -83.023257+0.001703j
[2025-09-06 13:22:31] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -83.093427+0.001393j
[2025-09-06 13:23:02] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -83.033258-0.001238j
[2025-09-06 13:23:33] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -83.100376-0.002292j
[2025-09-06 13:24:03] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -83.039524+0.002551j
[2025-09-06 13:24:34] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -83.075140-0.002023j
[2025-09-06 13:25:04] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -83.142930+0.000570j
[2025-09-06 13:25:29] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -83.055311+0.001022j
[2025-09-06 13:25:49] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -83.157776+0.001270j
[2025-09-06 13:26:20] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -83.160758+0.001221j
[2025-09-06 13:26:50] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -83.097646-0.002979j
[2025-09-06 13:27:21] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -83.046390+0.000312j
[2025-09-06 13:27:52] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -82.965351-0.000483j
[2025-09-06 13:28:22] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -83.135870-0.005351j
[2025-09-06 13:28:53] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -83.230575+0.004293j
[2025-09-06 13:29:23] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -83.196473-0.001306j
[2025-09-06 13:29:54] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -83.188753-0.002849j
[2025-09-06 13:30:22] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -83.094511+0.002597j
[2025-09-06 13:30:51] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -83.206075+0.003806j
[2025-09-06 13:31:21] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -83.369131+0.003165j
[2025-09-06 13:31:52] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -83.421828-0.004616j
[2025-09-06 13:32:22] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -83.441752+0.000256j
[2025-09-06 13:32:53] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -83.291807+0.002805j
[2025-09-06 13:33:23] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -83.338844-0.000929j
[2025-09-06 13:33:54] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -83.256467+0.003121j
[2025-09-06 13:34:24] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -83.344433+0.000539j
[2025-09-06 13:34:55] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -83.191414+0.001707j
[2025-09-06 13:35:25] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -83.193286+0.002040j
[2025-09-06 13:35:56] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -83.302943-0.002972j
[2025-09-06 13:36:26] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -83.097480-0.002466j
[2025-09-06 13:36:57] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -83.198060+0.002126j
[2025-09-06 13:37:27] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -83.260922-0.000857j
[2025-09-06 13:37:58] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -83.106542-0.001032j
[2025-09-06 13:38:28] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -83.075544-0.000354j
[2025-09-06 13:38:59] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -83.318724+0.005390j
[2025-09-06 13:39:29] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -83.247458+0.004906j
[2025-09-06 13:40:00] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -83.302639+0.003803j
[2025-09-06 13:40:31] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -83.219782+0.003323j
[2025-09-06 13:41:01] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -83.323252+0.000509j
[2025-09-06 13:41:32] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -83.297669+0.002096j
[2025-09-06 13:42:02] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -83.330825-0.001687j
[2025-09-06 13:42:33] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -83.342442+0.005441j
[2025-09-06 13:43:03] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -83.208032+0.001997j
[2025-09-06 13:43:34] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -82.995414-0.002363j
[2025-09-06 13:44:04] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -82.956668+0.003070j
[2025-09-06 13:44:35] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -83.158749-0.000859j
[2025-09-06 13:45:05] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -83.228449-0.001121j
[2025-09-06 13:45:28] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -83.238242-0.001107j
[2025-09-06 13:45:51] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -83.088949-0.000676j
[2025-09-06 13:46:22] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -83.125525-0.000621j
[2025-09-06 13:46:53] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -83.246783+0.003085j
[2025-09-06 13:47:23] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -83.427122+0.003819j
[2025-09-06 13:47:54] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -83.230630+0.006200j
[2025-09-06 13:48:24] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -83.153575-0.003419j
[2025-09-06 13:48:55] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -83.120081-0.004143j
[2025-09-06 13:49:26] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -83.059157-0.003126j
[2025-09-06 13:49:56] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -83.063329-0.000422j
[2025-09-06 13:50:23] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -83.121322+0.002756j
[2025-09-06 13:50:53] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -83.223620+0.001455j
[2025-09-06 13:51:24] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -83.264528-0.001634j
[2025-09-06 13:51:54] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -83.207568-0.004922j
[2025-09-06 13:52:25] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -83.190653-0.003172j
[2025-09-06 13:52:56] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -83.277016-0.004686j
[2025-09-06 13:53:26] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -83.349648-0.001653j
[2025-09-06 13:53:57] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -82.917433+0.003300j
[2025-09-06 13:54:28] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -83.083077+0.001201j
[2025-09-06 13:54:58] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -83.141379+0.001029j
[2025-09-06 13:55:29] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -82.980990-0.000812j
[2025-09-06 13:55:59] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -83.020466+0.001180j
[2025-09-06 13:56:30] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -83.249007+0.001078j
[2025-09-06 13:57:01] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -83.176791+0.001182j
[2025-09-06 13:57:31] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -83.146941+0.000566j
[2025-09-06 13:58:02] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -83.184638+0.000471j
[2025-09-06 13:58:32] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -83.086862-0.002434j
[2025-09-06 13:59:03] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -83.018487-0.006805j
[2025-09-06 13:59:34] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -83.038042-0.001273j
[2025-09-06 14:00:04] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -83.105490+0.005114j
[2025-09-06 14:00:35] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -83.018227-0.005360j
[2025-09-06 14:01:05] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -83.113146+0.000239j
[2025-09-06 14:01:36] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -83.150201+0.002206j
[2025-09-06 14:02:07] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -83.077248-0.000817j
[2025-09-06 14:02:37] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -83.244378-0.003830j
[2025-09-06 14:03:08] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -83.127201-0.000521j
[2025-09-06 14:03:38] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -82.867859-0.004707j
[2025-09-06 14:04:09] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -83.047873-0.005050j
[2025-09-06 14:04:39] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -83.130518-0.005054j
[2025-09-06 14:05:09] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -83.032790+0.002828j
[2025-09-06 14:05:30] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -83.164524-0.002357j
[2025-09-06 14:05:56] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -83.323125-0.000159j
[2025-09-06 14:06:27] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -83.273300+0.002350j
[2025-09-06 14:06:27] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-06 14:06:58] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -83.163512+0.002389j
[2025-09-06 14:07:28] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -83.222709+0.000382j
[2025-09-06 14:07:59] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -83.187624+0.008134j
[2025-09-06 14:08:30] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -83.198016-0.002717j
[2025-09-06 14:09:00] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -83.291300+0.002058j
[2025-09-06 14:09:31] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -83.142419+0.003954j
[2025-09-06 14:10:01] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -83.213983-0.015015j
[2025-09-06 14:10:27] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -82.992590-0.001678j
[2025-09-06 14:10:58] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -83.033788-0.001114j
[2025-09-06 14:11:28] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -83.321104-0.001607j
[2025-09-06 14:11:59] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -83.247390-0.000062j
[2025-09-06 14:12:29] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -83.193449+0.001991j
[2025-09-06 14:13:00] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -83.113061-0.001215j
[2025-09-06 14:13:31] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -83.200753-0.000358j
[2025-09-06 14:14:01] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -83.229729+0.004130j
[2025-09-06 14:14:32] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -83.073610+0.003930j
[2025-09-06 14:15:02] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -83.158474-0.001258j
[2025-09-06 14:15:33] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -83.068809+0.002525j
[2025-09-06 14:16:03] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -83.001163-0.004150j
[2025-09-06 14:16:34] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -82.957182+0.000753j
[2025-09-06 14:17:04] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -83.097886-0.004345j
[2025-09-06 14:17:35] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -83.193292+0.002942j
[2025-09-06 14:18:06] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -83.021176-0.001062j
[2025-09-06 14:18:36] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -83.198473-0.005183j
[2025-09-06 14:19:07] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -83.017383-0.002208j
[2025-09-06 14:19:37] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -83.045912-0.006936j
[2025-09-06 14:20:08] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -83.016331-0.000677j
[2025-09-06 14:20:38] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -83.102031+0.000656j
[2025-09-06 14:21:09] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -83.206765-0.002426j
[2025-09-06 14:21:39] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -83.143317-0.001401j
[2025-09-06 14:22:10] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -83.141680+0.000746j
[2025-09-06 14:22:41] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -83.132290-0.001359j
[2025-09-06 14:23:11] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -83.182276-0.000165j
[2025-09-06 14:23:42] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -83.230008+0.004403j
[2025-09-06 14:24:12] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -83.125656-0.000318j
[2025-09-06 14:24:43] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -83.231016-0.003694j
[2025-09-06 14:25:10] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -83.313900-0.002640j
[2025-09-06 14:25:30] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -83.244182+0.002866j
[2025-09-06 14:25:58] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -83.435499-0.000625j
[2025-09-06 14:26:28] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -83.336962-0.002177j
[2025-09-06 14:26:59] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -83.340908-0.003205j
[2025-09-06 14:27:30] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -83.226224+0.004436j
[2025-09-06 14:28:00] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -83.144006+0.001712j
[2025-09-06 14:28:31] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -83.164298+0.001702j
[2025-09-06 14:29:01] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -83.056507+0.000907j
[2025-09-06 14:29:32] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -83.087117+0.002060j
[2025-09-06 14:30:01] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -83.140369+0.003323j
[2025-09-06 14:30:29] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -83.098087-0.000892j
[2025-09-06 14:30:59] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -83.016410+0.000300j
[2025-09-06 14:31:30] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -83.097520+0.004056j
[2025-09-06 14:32:01] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -83.007380+0.003810j
[2025-09-06 14:32:31] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -83.134546+0.004364j
[2025-09-06 14:33:02] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -83.183336+0.011553j
[2025-09-06 14:33:33] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -83.140964-0.000334j
[2025-09-06 14:34:03] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -83.187171+0.005149j
[2025-09-06 14:34:34] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -83.243491-0.001721j
[2025-09-06 14:35:05] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -83.294639-0.005738j
[2025-09-06 14:35:35] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -83.330571+0.000353j
[2025-09-06 14:36:06] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -83.241047-0.004570j
[2025-09-06 14:36:36] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -83.217302-0.000836j
[2025-09-06 14:37:07] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -83.177688-0.004325j
[2025-09-06 14:37:38] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -83.078593+0.000022j
[2025-09-06 14:38:08] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -83.024999+0.003939j
[2025-09-06 14:38:39] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -83.064792-0.003984j
[2025-09-06 14:39:10] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -83.176148+0.002841j
[2025-09-06 14:39:40] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -83.231021-0.003167j
[2025-09-06 14:40:11] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -83.230996+0.002066j
[2025-09-06 14:40:42] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -83.242170-0.001326j
[2025-09-06 14:41:12] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -83.214602+0.001625j
[2025-09-06 14:41:43] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -83.148924-0.003704j
[2025-09-06 14:42:14] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -83.063787-0.001160j
[2025-09-06 14:42:44] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -83.083285-0.000757j
[2025-09-06 14:43:15] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -83.140422+0.001444j
[2025-09-06 14:43:45] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -83.157092+0.005334j
[2025-09-06 14:44:16] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -83.148534-0.002917j
[2025-09-06 14:44:47] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -83.319584-0.001089j
[2025-09-06 14:45:12] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -83.098554+0.001224j
[2025-09-06 14:45:33] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -83.216368-0.004647j
[2025-09-06 14:46:00] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -83.183851+0.001970j
[2025-09-06 14:46:31] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -83.053330+0.003242j
[2025-09-06 14:47:02] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -83.192342-0.010231j
[2025-09-06 14:47:32] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -83.239443-0.000862j
[2025-09-06 14:48:03] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -83.126342-0.002607j
[2025-09-06 14:48:33] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -83.212727-0.001212j
[2025-09-06 14:49:04] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -83.203438-0.001871j
[2025-09-06 14:49:35] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -83.133761+0.001322j
[2025-09-06 14:50:04] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -83.167826+0.001677j
[2025-09-06 14:50:31] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -83.364244+0.005358j
[2025-09-06 14:51:02] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -83.251715-0.003021j
[2025-09-06 14:51:32] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -83.199102+0.004034j
[2025-09-06 14:52:03] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -83.193206+0.000258j
[2025-09-06 14:52:34] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -83.222787-0.002423j
[2025-09-06 14:53:04] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -83.163993-0.000540j
[2025-09-06 14:53:35] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -83.127460-0.000637j
[2025-09-06 14:54:05] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -83.176567+0.001510j
[2025-09-06 14:54:36] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -83.215056-0.002860j
[2025-09-06 14:55:06] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -83.086834-0.004119j
[2025-09-06 14:55:37] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -83.013131+0.003835j
[2025-09-06 14:56:08] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -82.988880+0.001927j
[2025-09-06 14:56:38] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -83.195944-0.000686j
[2025-09-06 14:57:09] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -83.126819+0.000646j
[2025-09-06 14:57:39] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -83.189978+0.006099j
[2025-09-06 14:58:10] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -83.275636+0.001534j
[2025-09-06 14:58:41] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -83.417047+0.002809j
[2025-09-06 14:59:11] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -83.307113-0.003770j
[2025-09-06 14:59:11] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-06 14:59:42] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -83.280127-0.000897j
[2025-09-06 15:00:12] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -83.313481-0.003870j
[2025-09-06 15:00:43] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -83.375833+0.005287j
[2025-09-06 15:01:13] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -83.395819+0.001657j
[2025-09-06 15:01:44] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -83.405594-0.004807j
[2025-09-06 15:02:15] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -83.200584+0.002070j
[2025-09-06 15:02:45] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -83.373321+0.005130j
[2025-09-06 15:03:16] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -83.249890+0.002782j
[2025-09-06 15:03:46] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -83.259674-0.000361j
[2025-09-06 15:04:17] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -83.159452+0.003958j
[2025-09-06 15:04:47] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -83.159144-0.004230j
[2025-09-06 15:05:13] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -83.113343+0.001913j
[2025-09-06 15:05:33] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -83.114750-0.001032j
[2025-09-06 15:06:03] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -83.053590+0.001305j
[2025-09-06 15:06:34] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -83.060712+0.001488j
[2025-09-06 15:07:05] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -83.164123+0.001419j
[2025-09-06 15:07:35] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -83.298188+0.001362j
[2025-09-06 15:08:06] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -83.289491-0.002582j
[2025-09-06 15:08:36] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -83.367522-0.001520j
[2025-09-06 15:09:07] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -83.193613-0.008092j
[2025-09-06 15:09:38] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -83.063580-0.002220j
[2025-09-06 15:10:06] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -82.979198+0.000556j
[2025-09-06 15:10:34] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -83.036223+0.002600j
[2025-09-06 15:11:05] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -82.975769-0.001705j
[2025-09-06 15:11:35] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -83.075104-0.001842j
[2025-09-06 15:12:06] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -83.177394+0.001047j
[2025-09-06 15:12:36] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -83.134224-0.000657j
[2025-09-06 15:13:07] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -83.007778-0.003952j
[2025-09-06 15:13:37] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -83.120520-0.002652j
[2025-09-06 15:14:08] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -83.041799-0.001587j
[2025-09-06 15:14:39] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -83.323469+0.002037j
[2025-09-06 15:15:09] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -83.234783-0.002075j
[2025-09-06 15:15:40] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -83.213415+0.000600j
[2025-09-06 15:16:10] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -83.233881+0.001148j
[2025-09-06 15:16:41] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -83.158610-0.004414j
[2025-09-06 15:17:11] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -83.084047-0.001815j
[2025-09-06 15:17:42] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -83.039687-0.002181j
[2025-09-06 15:18:13] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -83.112359+0.000459j
[2025-09-06 15:18:43] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -83.155082-0.001539j
[2025-09-06 15:19:14] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -82.995628+0.002648j
[2025-09-06 15:19:44] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -83.075195+0.003964j
[2025-09-06 15:20:15] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -83.002118+0.000955j
[2025-09-06 15:20:45] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -83.006423-0.002768j
[2025-09-06 15:21:16] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -82.942389-0.004897j
[2025-09-06 15:21:46] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -83.085022-0.002957j
[2025-09-06 15:22:17] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -83.095062-0.001565j
[2025-09-06 15:22:47] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -83.194553+0.000788j
[2025-09-06 15:23:18] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -83.343417-0.001954j
[2025-09-06 15:23:48] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -83.397801+0.001506j
[2025-09-06 15:24:19] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -83.274361+0.004451j
[2025-09-06 15:24:50] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -83.358750-0.001055j
[2025-09-06 15:25:13] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -83.383076+0.003018j
[2025-09-06 15:25:35] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -83.270677-0.001633j
[2025-09-06 15:26:05] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -83.368447-0.000050j
[2025-09-06 15:26:36] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -83.318608-0.001571j
[2025-09-06 15:27:06] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -83.125496-0.000835j
[2025-09-06 15:27:37] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -83.226370-0.002312j
[2025-09-06 15:28:08] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -83.224742+0.000287j
[2025-09-06 15:28:38] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -83.167466-0.001385j
[2025-09-06 15:29:09] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -83.136963+0.001132j
[2025-09-06 15:29:40] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -83.250099-0.003022j
[2025-09-06 15:30:08] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -83.314009-0.001560j
[2025-09-06 15:30:36] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -83.270976-0.001849j
[2025-09-06 15:31:06] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -83.251383-0.005805j
[2025-09-06 15:31:37] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -83.143473-0.000538j
[2025-09-06 15:32:07] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -83.176981-0.002652j
[2025-09-06 15:32:38] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -83.158616-0.003217j
[2025-09-06 15:33:08] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -83.107442-0.001545j
[2025-09-06 15:33:39] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -83.111998-0.002638j
[2025-09-06 15:34:09] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -82.991607+0.003321j
[2025-09-06 15:34:40] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -82.904096-0.002615j
[2025-09-06 15:35:10] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -83.102221-0.004389j
[2025-09-06 15:35:41] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -83.042692+0.005660j
[2025-09-06 15:36:11] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -83.014269+0.004963j
[2025-09-06 15:36:42] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -83.074440-0.000037j
[2025-09-06 15:37:12] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -82.964833+0.003398j
[2025-09-06 15:37:43] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -82.954895-0.002844j
[2025-09-06 15:38:13] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -82.792597-0.006349j
[2025-09-06 15:38:44] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -83.089761-0.005636j
[2025-09-06 15:39:14] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -83.139486+0.004350j
[2025-09-06 15:39:45] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -83.249569+0.005794j
[2025-09-06 15:40:15] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -83.095405+0.001401j
[2025-09-06 15:40:46] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -83.041571+0.002703j
[2025-09-06 15:41:16] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -83.090518+0.000289j
[2025-09-06 15:41:46] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -83.105083-0.000594j
[2025-09-06 15:42:17] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -83.146956+0.001322j
[2025-09-06 15:42:48] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -83.067395+0.003105j
[2025-09-06 15:43:18] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -82.848480-0.001165j
[2025-09-06 15:43:49] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -82.912764-0.004103j
[2025-09-06 15:44:19] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -82.908620+0.000248j
[2025-09-06 15:44:50] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -82.936960+0.003773j
[2025-09-06 15:45:12] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -82.995425+0.002579j
[2025-09-06 15:45:35] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -83.063099+0.004333j
[2025-09-06 15:46:05] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -83.106768-0.000792j
[2025-09-06 15:46:36] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -83.101956+0.001654j
[2025-09-06 15:47:07] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -83.122141+0.000832j
[2025-09-06 15:47:37] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -83.080122-0.000161j
[2025-09-06 15:48:08] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -83.109835-0.003187j
[2025-09-06 15:48:38] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -83.301027-0.002351j
[2025-09-06 15:49:09] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -83.152059+0.001572j
[2025-09-06 15:49:40] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -82.936369-0.001675j
[2025-09-06 15:50:06] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -83.302563+0.002360j
[2025-09-06 15:50:36] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -83.237878-0.000229j
[2025-09-06 15:51:07] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -83.270161+0.003063j
[2025-09-06 15:51:38] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -83.130626+0.001733j
[2025-09-06 15:51:38] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-06 15:52:08] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -83.088811+0.000427j
[2025-09-06 15:52:39] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -82.981735+0.001129j
[2025-09-06 15:53:09] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -83.097322-0.000876j
[2025-09-06 15:53:40] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -83.083003-0.002342j
[2025-09-06 15:54:10] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -83.006087-0.001544j
[2025-09-06 15:54:41] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -82.952051-0.000503j
[2025-09-06 15:55:12] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -82.912297-0.001410j
[2025-09-06 15:55:42] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -83.030938-0.001692j
[2025-09-06 15:56:13] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -83.223101-0.001621j
[2025-09-06 15:56:43] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -83.084559-0.001277j
[2025-09-06 15:57:14] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -82.998196+0.001416j
[2025-09-06 15:57:44] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -83.029391+0.001288j
[2025-09-06 15:58:15] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -83.021332+0.000856j
[2025-09-06 15:58:46] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -83.175784-0.001094j
[2025-09-06 15:59:16] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -83.148441+0.001132j
[2025-09-06 15:59:47] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -82.863228-0.003262j
[2025-09-06 16:00:17] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -82.863128-0.003103j
[2025-09-06 16:00:48] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -82.932380-0.002692j
[2025-09-06 16:01:18] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -83.145136+0.005843j
[2025-09-06 16:01:49] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -83.021868+0.000222j
[2025-09-06 16:02:20] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -83.060902-0.001826j
[2025-09-06 16:02:50] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -83.034356-0.002132j
[2025-09-06 16:03:21] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -83.151481+0.001632j
[2025-09-06 16:03:51] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -83.118142-0.006355j
[2025-09-06 16:04:22] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -83.186101-0.002555j
[2025-09-06 16:04:52] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -83.052090+0.000498j
[2025-09-06 16:05:13] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -83.179038-0.000250j
[2025-09-06 16:05:38] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -83.159777-0.001803j
[2025-09-06 16:06:09] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -83.068614-0.003623j
[2025-09-06 16:06:39] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -83.361619-0.002143j
[2025-09-06 16:07:10] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -83.483415-0.003561j
[2025-09-06 16:07:41] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -83.320142+0.012404j
[2025-09-06 16:08:11] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -83.346263-0.001962j
[2025-09-06 16:08:42] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -83.087142+0.002229j
[2025-09-06 16:09:12] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -83.334300+0.003834j
[2025-09-06 16:09:43] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -83.355105+0.001541j
[2025-09-06 16:10:09] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -83.290269+0.002409j
[2025-09-06 16:10:40] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -83.164718+0.000581j
[2025-09-06 16:11:10] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -83.171086-0.002425j
[2025-09-06 16:11:41] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -83.201818+0.001470j
[2025-09-06 16:12:11] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -83.162983+0.001427j
[2025-09-06 16:12:42] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -83.000205+0.000675j
[2025-09-06 16:13:13] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -83.020298-0.002308j
[2025-09-06 16:13:43] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -82.980155-0.000551j
[2025-09-06 16:14:14] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -82.969059-0.000516j
[2025-09-06 16:14:44] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -83.033285-0.001822j
[2025-09-06 16:15:15] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -83.064208+0.000870j
[2025-09-06 16:15:46] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -83.010549-0.001648j
[2025-09-06 16:16:16] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -82.943669+0.003222j
[2025-09-06 16:16:47] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -83.021733-0.003296j
[2025-09-06 16:17:17] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -83.029910+0.005283j
[2025-09-06 16:17:48] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -83.030950-0.001177j
[2025-09-06 16:18:18] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -83.191168-0.002212j
[2025-09-06 16:18:49] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -83.048653+0.000448j
[2025-09-06 16:19:20] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -83.179595+0.001827j
[2025-09-06 16:19:50] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -83.124785+0.000087j
[2025-09-06 16:20:21] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -82.988609+0.000561j
[2025-09-06 16:20:51] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -83.055039+0.000699j
[2025-09-06 16:21:22] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -83.127180+0.002107j
[2025-09-06 16:21:53] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -83.043381+0.003126j
[2025-09-06 16:22:23] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -82.945821+0.002015j
[2025-09-06 16:22:54] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -83.065829-0.001103j
[2025-09-06 16:23:24] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -83.215086-0.000583j
[2025-09-06 16:23:55] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -83.238603+0.001317j
[2025-09-06 16:24:26] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -83.179186+0.004859j
[2025-09-06 16:24:54] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -83.181537+0.003818j
[2025-09-06 16:25:14] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -83.136085-0.006167j
[2025-09-06 16:25:42] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -83.238366-0.000519j
[2025-09-06 16:26:13] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -83.137353-0.003160j
[2025-09-06 16:26:43] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -83.135760-0.000354j
[2025-09-06 16:27:14] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -83.120353-0.001175j
[2025-09-06 16:27:45] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -83.166781+0.004365j
[2025-09-06 16:28:15] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -83.218025-0.003232j
[2025-09-06 16:28:46] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -83.263926+0.001033j
[2025-09-06 16:29:16] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -83.331840+0.002163j
[2025-09-06 16:29:45] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -83.307335+0.000913j
[2025-09-06 16:30:13] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -83.448047-0.000859j
[2025-09-06 16:30:44] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -83.294415-0.003193j
[2025-09-06 16:31:14] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -83.271772+0.002478j
[2025-09-06 16:31:45] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -83.366172+0.000670j
[2025-09-06 16:32:16] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -83.291692+0.002017j
[2025-09-06 16:32:46] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -83.272800+0.001609j
[2025-09-06 16:33:17] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -83.125217+0.001711j
[2025-09-06 16:33:48] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -82.991662-0.001312j
[2025-09-06 16:34:18] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -83.069632+0.003623j
[2025-09-06 16:34:49] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -83.064095+0.000579j
[2025-09-06 16:35:19] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -83.171180+0.001378j
[2025-09-06 16:35:50] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -83.225033+0.003366j
[2025-09-06 16:36:21] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -83.189367-0.002709j
[2025-09-06 16:36:51] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -83.173876+0.001147j
[2025-09-06 16:37:22] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -83.160797+0.000122j
[2025-09-06 16:37:53] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -83.304934-0.001830j
[2025-09-06 16:38:23] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -83.196197+0.002440j
[2025-09-06 16:38:54] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -83.128866-0.000535j
[2025-09-06 16:39:24] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -83.244721+0.000701j
[2025-09-06 16:39:55] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -83.284495+0.001294j
[2025-09-06 16:40:26] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -83.114906+0.003095j
[2025-09-06 16:40:56] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -83.148065+0.004492j
[2025-09-06 16:41:27] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -82.955691-0.001146j
[2025-09-06 16:41:58] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -83.086570-0.003609j
[2025-09-06 16:42:28] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -83.118007-0.005409j
[2025-09-06 16:42:59] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -82.969978-0.004953j
[2025-09-06 16:43:30] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -83.022074-0.003514j
[2025-09-06 16:44:00] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -83.016714-0.000141j
[2025-09-06 16:44:31] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -82.957060-0.000110j
[2025-09-06 16:44:31] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-06 16:44:31] ✅ Training completed | Restarts: 2
[2025-09-06 16:44:31] ============================================================
[2025-09-06 16:44:31] Training completed | Runtime: 31622.0s
[2025-09-06 16:44:43] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-06 16:44:43] ============================================================
