[2025-09-06 16:47:32] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.07/training/checkpoints/final_GCNN.pkl
[2025-09-06 16:47:32]   - 迭代次数: final
[2025-09-06 16:47:32]   - 能量: -87.112300-0.003308j ± 0.110072
[2025-09-06 16:47:32]   - 时间戳: 2025-09-06T16:47:18.661978+08:00
[2025-09-06 16:47:45] ✓ 变分状态参数已从checkpoint恢复
[2025-09-06 16:47:45] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-06 16:47:45] ==================================================
[2025-09-06 16:47:45] GCNN for Shastry-Sutherland Model
[2025-09-06 16:47:45] ==================================================
[2025-09-06 16:47:45] System parameters:
[2025-09-06 16:47:45]   - System size: L=5, N=100
[2025-09-06 16:47:45]   - System parameters: J1=0.08, J2=0.0, Q=1.0
[2025-09-06 16:47:45] --------------------------------------------------
[2025-09-06 16:47:45] Model parameters:
[2025-09-06 16:47:45]   - Number of layers = 4
[2025-09-06 16:47:45]   - Number of features = 4
[2025-09-06 16:47:45]   - Total parameters = 19628
[2025-09-06 16:47:45] --------------------------------------------------
[2025-09-06 16:47:45] Training parameters:
[2025-09-06 16:47:45]   - Learning rate: 0.015
[2025-09-06 16:47:45]   - Total iterations: 1050
[2025-09-06 16:47:45]   - Annealing cycles: 3
[2025-09-06 16:47:45]   - Initial period: 150
[2025-09-06 16:47:45]   - Period multiplier: 2.0
[2025-09-06 16:47:45]   - Temperature range: 0.0-1.0
[2025-09-06 16:47:45]   - Samples: 4096
[2025-09-06 16:47:45]   - Discarded samples: 0
[2025-09-06 16:47:45]   - Chunk size: 2048
[2025-09-06 16:47:45]   - Diagonal shift: 0.2
[2025-09-06 16:47:45]   - Gradient clipping: 1.0
[2025-09-06 16:47:45]   - Checkpoint enabled: interval=105
[2025-09-06 16:47:45]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.08/training/checkpoints
[2025-09-06 16:47:45] --------------------------------------------------
[2025-09-06 16:47:45] Device status:
[2025-09-06 16:47:45]   - Devices model: NVIDIA H200 NVL
[2025-09-06 16:47:45]   - Number of devices: 1
[2025-09-06 16:47:45]   - Sharding: True
[2025-09-06 16:47:45] ============================================================
[2025-09-06 16:48:41] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -87.648109+0.000300j
[2025-09-06 16:49:25] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -87.832633+0.006161j
[2025-09-06 16:49:56] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -87.617657+0.002443j
[2025-09-06 16:50:26] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -87.766460+0.001251j
[2025-09-06 16:50:57] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -87.735211+0.000593j
[2025-09-06 16:51:28] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -87.602072+0.002678j
[2025-09-06 16:51:58] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -87.554401+0.003866j
[2025-09-06 16:52:29] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -87.551094+0.004239j
[2025-09-06 16:52:59] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -87.598773+0.004654j
[2025-09-06 16:53:30] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -87.775475-0.003653j
[2025-09-06 16:54:00] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -87.721138+0.001501j
[2025-09-06 16:54:31] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -87.552459+0.002674j
[2025-09-06 16:55:02] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -87.608199+0.000249j
[2025-09-06 16:55:32] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -87.496218+0.000015j
[2025-09-06 16:56:03] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -87.501116-0.001044j
[2025-09-06 16:56:33] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -87.479721+0.001558j
[2025-09-06 16:57:04] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -87.546537+0.000899j
[2025-09-06 16:57:34] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -87.647710+0.000899j
[2025-09-06 16:58:05] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -87.555782+0.000110j
[2025-09-06 16:58:36] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -87.548121-0.003390j
[2025-09-06 16:59:06] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -87.362962+0.002397j
[2025-09-06 16:59:37] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -87.466523+0.003280j
[2025-09-06 17:00:07] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -87.514177+0.000863j
[2025-09-06 17:00:38] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -87.403392-0.000210j
[2025-09-06 17:01:06] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -87.470194-0.001086j
[2025-09-06 17:01:27] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -87.444072+0.002604j
[2025-09-06 17:01:55] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -87.397877-0.001310j
[2025-09-06 17:02:25] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -87.519145+0.000300j
[2025-09-06 17:02:56] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -87.585411-0.002198j
[2025-09-06 17:03:27] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -87.489562+0.002855j
[2025-09-06 17:03:57] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -87.360927+0.000569j
[2025-09-06 17:04:28] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -87.588825-0.001262j
[2025-09-06 17:04:59] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -87.551729+0.000325j
[2025-09-06 17:05:29] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -87.516852+0.000664j
[2025-09-06 17:05:59] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -87.438223-0.000347j
[2025-09-06 17:06:26] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -87.593554+0.001465j
[2025-09-06 17:06:57] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -87.580769-0.000640j
[2025-09-06 17:07:27] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -87.654794+0.004146j
[2025-09-06 17:07:58] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -87.628028+0.003021j
[2025-09-06 17:08:28] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -87.505364+0.001215j
[2025-09-06 17:08:59] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -87.586570-0.001101j
[2025-09-06 17:09:29] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -87.515913+0.000251j
[2025-09-06 17:10:00] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -87.471797-0.000790j
[2025-09-06 17:10:31] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -87.603999+0.000744j
[2025-09-06 17:11:01] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -87.664942+0.002020j
[2025-09-06 17:11:32] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -87.699833-0.001901j
[2025-09-06 17:12:02] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -87.499098+0.000466j
[2025-09-06 17:12:33] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -87.594681+0.002620j
[2025-09-06 17:13:04] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -87.419614-0.000724j
[2025-09-06 17:13:34] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -87.573298-0.006227j
[2025-09-06 17:14:05] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -87.520705-0.000619j
[2025-09-06 17:14:35] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -87.342360-0.000986j
[2025-09-06 17:15:06] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -87.436253+0.002467j
[2025-09-06 17:15:36] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -87.457366+0.003909j
[2025-09-06 17:16:07] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -87.563797+0.000419j
[2025-09-06 17:16:38] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -87.547648-0.001607j
[2025-09-06 17:17:08] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -87.546443-0.001339j
[2025-09-06 17:17:39] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -87.505181+0.000451j
[2025-09-06 17:18:09] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -87.611396+0.004921j
[2025-09-06 17:18:40] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -87.605467+0.000717j
[2025-09-06 17:19:11] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -87.679044+0.001931j
[2025-09-06 17:19:41] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -87.646277+0.000474j
[2025-09-06 17:20:12] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -87.670088+0.001790j
[2025-09-06 17:20:42] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -87.691036+0.000107j
[2025-09-06 17:21:08] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -87.559469+0.000965j
[2025-09-06 17:21:29] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -87.637881+0.000436j
[2025-09-06 17:22:00] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -87.429953-0.002252j
[2025-09-06 17:22:30] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -87.352123+0.000407j
[2025-09-06 17:23:01] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -87.356812+0.000619j
[2025-09-06 17:23:32] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -87.406853+0.005970j
[2025-09-06 17:24:02] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -87.544467+0.000605j
[2025-09-06 17:24:33] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -87.530985-0.004427j
[2025-09-06 17:25:04] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -87.508843-0.003566j
[2025-09-06 17:25:34] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -87.559530+0.001262j
[2025-09-06 17:26:03] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -87.391103+0.005252j
[2025-09-06 17:26:31] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -87.322594-0.000555j
[2025-09-06 17:27:02] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -87.340329+0.002184j
[2025-09-06 17:27:32] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -87.349425+0.002686j
[2025-09-06 17:28:03] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -87.366577+0.002884j
[2025-09-06 17:28:34] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -87.386828+0.005806j
[2025-09-06 17:29:04] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -87.400124+0.002924j
[2025-09-06 17:29:35] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -87.424885+0.000002j
[2025-09-06 17:30:05] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -87.549855+0.000402j
[2025-09-06 17:30:36] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -87.634042+0.000764j
[2025-09-06 17:31:07] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -87.401982-0.002324j
[2025-09-06 17:31:37] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -87.403760+0.001502j
[2025-09-06 17:32:08] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -87.411336+0.004161j
[2025-09-06 17:32:38] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -87.474773+0.003861j
[2025-09-06 17:33:09] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -87.462498+0.000159j
[2025-09-06 17:33:40] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -87.419021-0.002543j
[2025-09-06 17:34:10] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -87.464204-0.004328j
[2025-09-06 17:34:41] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -87.430196-0.005987j
[2025-09-06 17:35:12] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -87.500298+0.000891j
[2025-09-06 17:35:42] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -87.331790-0.003695j
[2025-09-06 17:36:13] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -87.516368+0.002006j
[2025-09-06 17:36:44] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -87.537161-0.000668j
[2025-09-06 17:37:14] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -87.473652-0.005266j
[2025-09-06 17:37:45] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -87.495774-0.000335j
[2025-09-06 17:38:15] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -87.761451-0.000484j
[2025-09-06 17:38:46] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -87.676627+0.000402j
[2025-09-06 17:39:17] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -87.576899+0.007195j
[2025-09-06 17:39:47] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -87.677736+0.002449j
[2025-09-06 17:40:18] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -87.804943+0.001945j
[2025-09-06 17:40:49] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -87.580061-0.000575j
[2025-09-06 17:41:11] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -87.625072+0.001439j
[2025-09-06 17:41:11] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-06 17:41:34] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -87.584447+0.004702j
[2025-09-06 17:42:04] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -87.514406-0.003656j
[2025-09-06 17:42:35] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -87.525581-0.002641j
[2025-09-06 17:43:06] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -87.557408-0.000552j
[2025-09-06 17:43:36] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -87.389031+0.004311j
[2025-09-06 17:44:07] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -87.448523+0.002765j
[2025-09-06 17:44:38] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -87.569410+0.000090j
[2025-09-06 17:45:08] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -87.308622+0.003289j
[2025-09-06 17:45:39] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -87.298768+0.001812j
[2025-09-06 17:46:05] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -87.290483+0.001508j
[2025-09-06 17:46:35] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -87.391158-0.002727j
[2025-09-06 17:47:06] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -87.417004+0.002048j
[2025-09-06 17:47:37] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -87.528690+0.004609j
[2025-09-06 17:48:07] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -87.639941-0.001269j
[2025-09-06 17:48:38] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -87.513261-0.004569j
[2025-09-06 17:49:09] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -87.491037-0.001360j
[2025-09-06 17:49:39] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -87.533658-0.000226j
[2025-09-06 17:50:10] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -87.588543-0.000870j
[2025-09-06 17:50:41] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -87.631609-0.003601j
[2025-09-06 17:51:11] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -87.462591+0.002588j
[2025-09-06 17:51:42] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -87.631067-0.001530j
[2025-09-06 17:52:12] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -87.495033+0.006361j
[2025-09-06 17:52:43] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -87.438728-0.000508j
[2025-09-06 17:53:14] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -87.571555-0.001621j
[2025-09-06 17:53:44] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -87.605688+0.001199j
[2025-09-06 17:54:15] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -87.704789+0.001560j
[2025-09-06 17:54:46] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -87.646476+0.000511j
[2025-09-06 17:55:16] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -87.578372-0.002080j
[2025-09-06 17:55:47] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -87.620986-0.002362j
[2025-09-06 17:56:17] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -87.710969+0.000473j
[2025-09-06 17:56:48] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -87.682844+0.000902j
[2025-09-06 17:57:19] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -87.636277-0.002935j
[2025-09-06 17:57:49] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -87.526677+0.001545j
[2025-09-06 17:58:20] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -87.561475-0.004037j
[2025-09-06 17:58:51] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -87.755242+0.001491j
[2025-09-06 17:59:21] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -87.656795-0.002507j
[2025-09-06 17:59:52] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -87.684357-0.000067j
[2025-09-06 18:00:23] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -87.585799+0.002577j
[2025-09-06 18:00:53] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -87.659077-0.000155j
[2025-09-06 18:01:13] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -87.568473-0.001686j
[2025-09-06 18:01:40] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -87.659189+0.003074j
[2025-09-06 18:02:10] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -87.543862+0.002523j
[2025-09-06 18:02:41] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -87.677809+0.001583j
[2025-09-06 18:03:12] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -87.809308-0.003363j
[2025-09-06 18:03:42] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -87.834566-0.000742j
[2025-09-06 18:03:42] RESTART #1 | Period: 300
[2025-09-06 18:04:13] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -87.917047-0.004555j
[2025-09-06 18:04:44] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -87.734506+0.001083j
[2025-09-06 18:05:14] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -87.741887+0.000923j
[2025-09-06 18:05:45] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -87.746504+0.001619j
[2025-09-06 18:06:11] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -87.867205-0.002229j
[2025-09-06 18:06:42] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -87.671943+0.003939j
[2025-09-06 18:07:12] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -87.841004-0.001943j
[2025-09-06 18:07:43] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -87.586570-0.003064j
[2025-09-06 18:08:13] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -87.493213+0.003174j
[2025-09-06 18:08:44] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -87.527888+0.002735j
[2025-09-06 18:09:14] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -87.585415-0.002577j
[2025-09-06 18:09:45] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -87.431177-0.006042j
[2025-09-06 18:10:16] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -87.559839-0.001429j
[2025-09-06 18:10:46] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -87.454755-0.004251j
[2025-09-06 18:11:17] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -87.666607-0.005413j
[2025-09-06 18:11:47] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -87.571597-0.000460j
[2025-09-06 18:12:18] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -87.649516+0.003304j
[2025-09-06 18:12:48] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -87.606258+0.003386j
[2025-09-06 18:13:19] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -87.501719-0.001637j
[2025-09-06 18:13:50] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -87.513538+0.000959j
[2025-09-06 18:14:20] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -87.714368-0.004129j
[2025-09-06 18:14:51] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -87.545488-0.002993j
[2025-09-06 18:15:21] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -87.511237+0.015297j
[2025-09-06 18:15:52] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -87.587874-0.001179j
[2025-09-06 18:16:22] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -87.606781-0.003486j
[2025-09-06 18:16:53] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -87.642201+0.001169j
[2025-09-06 18:17:24] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -87.427827-0.001060j
[2025-09-06 18:17:54] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -87.540717-0.001111j
[2025-09-06 18:18:25] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -87.549199+0.001932j
[2025-09-06 18:18:55] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -87.541149-0.000620j
[2025-09-06 18:19:26] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -87.351787-0.001996j
[2025-09-06 18:19:56] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -87.407486-0.000050j
[2025-09-06 18:20:27] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -87.609003+0.002474j
[2025-09-06 18:20:54] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -87.424877-0.001051j
[2025-09-06 18:21:15] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -87.414662-0.000547j
[2025-09-06 18:21:44] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -87.565535+0.001863j
[2025-09-06 18:22:15] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -87.403225+0.001850j
[2025-09-06 18:22:46] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -87.329413+0.000473j
[2025-09-06 18:23:16] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -87.427157-0.002581j
[2025-09-06 18:23:47] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -87.432990+0.000725j
[2025-09-06 18:24:18] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -87.459181+0.000746j
[2025-09-06 18:24:48] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -87.422977+0.001393j
[2025-09-06 18:25:19] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -87.390607+0.002210j
[2025-09-06 18:25:47] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -87.449924+0.000781j
[2025-09-06 18:26:15] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -87.543749+0.004600j
[2025-09-06 18:26:46] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -87.611795-0.000906j
[2025-09-06 18:27:17] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -87.655487-0.004978j
[2025-09-06 18:27:47] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -87.645633+0.000451j
[2025-09-06 18:28:18] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -87.588281-0.003657j
[2025-09-06 18:28:48] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -87.687953-0.001612j
[2025-09-06 18:29:19] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -87.608449-0.005102j
[2025-09-06 18:29:49] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -87.530736+0.000767j
[2025-09-06 18:30:20] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -87.630110-0.000403j
[2025-09-06 18:30:50] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -87.568746-0.000991j
[2025-09-06 18:31:21] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -87.427331-0.001118j
[2025-09-06 18:31:51] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -87.329938+0.002120j
[2025-09-06 18:32:22] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -87.467659+0.001254j
[2025-09-06 18:32:53] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -87.336699+0.001703j
[2025-09-06 18:33:23] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -87.479032-0.001469j
[2025-09-06 18:33:54] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -87.495292-0.001185j
[2025-09-06 18:33:54] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-06 18:34:24] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -87.393894-0.001220j
[2025-09-06 18:34:55] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -87.396204-0.004833j
[2025-09-06 18:35:25] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -87.472129+0.000576j
[2025-09-06 18:35:56] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -87.606544-0.001599j
[2025-09-06 18:36:26] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -87.599368-0.001142j
[2025-09-06 18:36:57] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -87.543994-0.001245j
[2025-09-06 18:37:27] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -87.504841-0.003109j
[2025-09-06 18:37:58] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -87.639059+0.003503j
[2025-09-06 18:38:28] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -87.688082+0.000248j
[2025-09-06 18:38:59] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -87.717818+0.002576j
[2025-09-06 18:39:30] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -87.484961-0.000050j
[2025-09-06 18:40:00] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -87.628059+0.002276j
[2025-09-06 18:40:31] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -87.674615-0.009411j
[2025-09-06 18:40:55] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -87.446049-0.001991j
[2025-09-06 18:41:15] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -87.466682-0.000217j
[2025-09-06 18:41:45] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -87.511197-0.003376j
[2025-09-06 18:42:16] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -87.466711+0.002521j
[2025-09-06 18:42:47] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -87.438612+0.003945j
[2025-09-06 18:43:17] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -87.370737-0.001988j
[2025-09-06 18:43:48] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -87.459079+0.003520j
[2025-09-06 18:44:19] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -87.393735+0.002868j
[2025-09-06 18:44:50] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -87.481728+0.000610j
[2025-09-06 18:45:20] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -87.617004+0.001090j
[2025-09-06 18:45:48] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -87.433183+0.002985j
[2025-09-06 18:46:17] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -87.502817+0.000034j
[2025-09-06 18:46:48] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -87.509189+0.002265j
[2025-09-06 18:47:18] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -87.336200+0.000490j
[2025-09-06 18:47:49] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -87.494553-0.001122j
[2025-09-06 18:48:20] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -87.270455-0.000095j
[2025-09-06 18:48:50] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -87.550175-0.007696j
[2025-09-06 18:49:21] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -87.497083-0.000253j
[2025-09-06 18:49:52] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -87.555993-0.000394j
[2025-09-06 18:50:22] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -87.498759-0.000956j
[2025-09-06 18:50:53] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -87.530666-0.001100j
[2025-09-06 18:51:24] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -87.519507-0.002657j
[2025-09-06 18:51:54] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -87.526748-0.001911j
[2025-09-06 18:52:25] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -87.534090+0.003111j
[2025-09-06 18:52:56] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -87.567662-0.003648j
[2025-09-06 18:53:26] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -87.510148+0.002035j
[2025-09-06 18:53:57] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -87.556173+0.003948j
[2025-09-06 18:54:28] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -87.669476-0.001934j
[2025-09-06 18:54:58] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -87.695917+0.001660j
[2025-09-06 18:55:29] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -87.719555+0.000797j
[2025-09-06 18:56:00] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -87.594750+0.000834j
[2025-09-06 18:56:31] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -87.525986-0.001220j
[2025-09-06 18:57:01] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -87.567604-0.000642j
[2025-09-06 18:57:32] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -87.629821+0.003114j
[2025-09-06 18:58:03] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -87.605836-0.001533j
[2025-09-06 18:58:33] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -87.560145-0.000939j
[2025-09-06 18:59:04] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -87.550690+0.000931j
[2025-09-06 18:59:35] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -87.663832+0.002754j
[2025-09-06 19:00:05] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -87.615638-0.000819j
[2025-09-06 19:00:36] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -87.597342+0.001835j
[2025-09-06 19:00:58] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -87.534988+0.000161j
[2025-09-06 19:01:20] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -87.568150-0.001616j
[2025-09-06 19:01:50] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -87.384078-0.000109j
[2025-09-06 19:02:21] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -87.474837-0.000379j
[2025-09-06 19:02:52] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -87.567629-0.001992j
[2025-09-06 19:03:22] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -87.715408+0.000269j
[2025-09-06 19:03:53] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -87.439801-0.001953j
[2025-09-06 19:04:24] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -87.446776+0.001615j
[2025-09-06 19:04:54] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -87.404885-0.002176j
[2025-09-06 19:05:25] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -87.419149+0.000978j
[2025-09-06 19:05:53] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -87.480189+0.000280j
[2025-09-06 19:06:22] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -87.521896+0.001071j
[2025-09-06 19:06:52] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -87.499949+0.000961j
[2025-09-06 19:07:23] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -87.506331+0.002959j
[2025-09-06 19:07:53] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -87.545547+0.003286j
[2025-09-06 19:08:24] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -87.529412-0.001653j
[2025-09-06 19:08:54] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -87.622811+0.001448j
[2025-09-06 19:09:25] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -87.481839+0.003657j
[2025-09-06 19:09:55] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -87.568546-0.004466j
[2025-09-06 19:10:26] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -87.775020+0.001521j
[2025-09-06 19:10:56] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -87.788849+0.001681j
[2025-09-06 19:11:27] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -87.884777+0.000279j
[2025-09-06 19:11:57] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -87.910714-0.001319j
[2025-09-06 19:12:28] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -87.734772-0.001433j
[2025-09-06 19:12:59] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -87.735950+0.000926j
[2025-09-06 19:13:29] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -87.683749+0.001787j
[2025-09-06 19:14:00] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -87.620629+0.001547j
[2025-09-06 19:14:30] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -87.507355+0.004816j
[2025-09-06 19:15:01] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -87.549200-0.004696j
[2025-09-06 19:15:31] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -87.611318+0.001241j
[2025-09-06 19:16:02] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -87.636624+0.000893j
[2025-09-06 19:16:32] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -87.571464+0.001911j
[2025-09-06 19:17:03] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -87.423500-0.000333j
[2025-09-06 19:17:33] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -87.615379+0.001828j
[2025-09-06 19:18:04] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -87.545872+0.000182j
[2025-09-06 19:18:34] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -87.454145-0.003619j
[2025-09-06 19:19:05] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -87.397094+0.003058j
[2025-09-06 19:19:35] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -87.352635-0.003968j
[2025-09-06 19:20:06] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -87.354392+0.006348j
[2025-09-06 19:20:37] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -87.532652-0.004377j
[2025-09-06 19:20:59] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -87.511981-0.000225j
[2025-09-06 19:21:22] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -87.427576-0.002193j
[2025-09-06 19:21:53] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -87.501242+0.004771j
[2025-09-06 19:22:24] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -87.419819-0.002675j
[2025-09-06 19:22:55] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -87.619878-0.002235j
[2025-09-06 19:23:25] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -87.469150-0.002175j
[2025-09-06 19:23:56] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -87.474335+0.003343j
[2025-09-06 19:24:27] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -87.479043+0.001291j
[2025-09-06 19:24:57] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -87.326353+0.000275j
[2025-09-06 19:25:28] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -87.534685+0.001601j
[2025-09-06 19:25:54] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -87.627836-0.004190j
[2025-09-06 19:26:25] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -87.640305-0.000503j
[2025-09-06 19:26:25] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-06 19:26:55] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -87.421739-0.003774j
[2025-09-06 19:27:26] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -87.325088-0.000281j
[2025-09-06 19:27:56] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -87.432811+0.002037j
[2025-09-06 19:28:27] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -87.365604-0.001056j
[2025-09-06 19:28:57] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -87.454039+0.003930j
[2025-09-06 19:29:28] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -87.404828+0.000599j
[2025-09-06 19:29:59] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -87.214233+0.001668j
[2025-09-06 19:30:29] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -87.433572+0.004768j
[2025-09-06 19:31:00] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -87.528167-0.001220j
[2025-09-06 19:31:30] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -87.456264+0.000901j
[2025-09-06 19:32:01] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -87.604999-0.001179j
[2025-09-06 19:32:31] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -87.621194+0.002845j
[2025-09-06 19:33:02] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -87.551362+0.000995j
[2025-09-06 19:33:32] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -87.522032+0.002585j
[2025-09-06 19:34:03] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -87.504398+0.002635j
[2025-09-06 19:34:34] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -87.579645-0.001808j
[2025-09-06 19:35:04] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -87.663986+0.003215j
[2025-09-06 19:35:35] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -87.454974-0.000136j
[2025-09-06 19:36:05] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -87.478347-0.002359j
[2025-09-06 19:36:36] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -87.483832+0.001836j
[2025-09-06 19:37:06] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -87.464169-0.001434j
[2025-09-06 19:37:37] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -87.589777+0.001976j
[2025-09-06 19:38:08] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -87.510949-0.000029j
[2025-09-06 19:38:38] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -87.492646+0.003093j
[2025-09-06 19:39:09] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -87.438462+0.000473j
[2025-09-06 19:39:39] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -87.440751-0.001234j
[2025-09-06 19:40:10] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -87.646936-0.004177j
[2025-09-06 19:40:39] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -87.515560+0.001139j
[2025-09-06 19:41:00] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -87.505661+0.000698j
[2025-09-06 19:41:27] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -87.629689+0.000364j
[2025-09-06 19:41:57] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -87.417192+0.002180j
[2025-09-06 19:42:28] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -87.448489-0.003728j
[2025-09-06 19:42:59] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -87.441481-0.002297j
[2025-09-06 19:43:29] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -87.390976-0.000614j
[2025-09-06 19:44:00] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -87.424215-0.002435j
[2025-09-06 19:44:30] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -87.434506+0.000943j
[2025-09-06 19:45:01] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -87.411686-0.006660j
[2025-09-06 19:45:32] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -87.637000-0.002025j
[2025-09-06 19:45:58] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -87.567889-0.004676j
[2025-09-06 19:46:28] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -87.557510+0.004846j
[2025-09-06 19:46:59] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -87.508114+0.002723j
[2025-09-06 19:47:29] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -87.455211-0.000588j
[2025-09-06 19:48:00] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -87.549832-0.001615j
[2025-09-06 19:48:30] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -87.546125-0.004441j
[2025-09-06 19:49:01] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -87.479092-0.003116j
[2025-09-06 19:49:32] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -87.511813+0.000575j
[2025-09-06 19:50:02] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -87.363615-0.002874j
[2025-09-06 19:50:33] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -87.283231-0.004052j
[2025-09-06 19:51:03] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -87.399765-0.000714j
[2025-09-06 19:51:34] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -87.449207+0.002971j
[2025-09-06 19:52:05] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -87.422888-0.004664j
[2025-09-06 19:52:35] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -87.477769-0.001862j
[2025-09-06 19:53:06] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -87.558140+0.003045j
[2025-09-06 19:53:36] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -87.383523-0.000949j
[2025-09-06 19:54:07] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -87.498403-0.000710j
[2025-09-06 19:54:37] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -87.452837+0.001426j
[2025-09-06 19:55:08] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -87.605238+0.001342j
[2025-09-06 19:55:38] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -87.387842+0.002823j
[2025-09-06 19:56:09] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -87.415456-0.004969j
[2025-09-06 19:56:40] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -87.487209-0.004427j
[2025-09-06 19:57:10] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -87.545910-0.000344j
[2025-09-06 19:57:41] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -87.503031-0.001362j
[2025-09-06 19:58:11] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -87.521223-0.004315j
[2025-09-06 19:58:42] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -87.437055-0.001482j
[2025-09-06 19:59:12] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -87.474777-0.000640j
[2025-09-06 19:59:43] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -87.425921-0.000327j
[2025-09-06 20:00:14] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -87.555278+0.003077j
[2025-09-06 20:00:40] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -87.490767-0.002844j
[2025-09-06 20:01:01] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -87.301753-0.001546j
[2025-09-06 20:01:28] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -87.373453-0.002804j
[2025-09-06 20:01:59] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -87.334839+0.003670j
[2025-09-06 20:02:30] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -87.404145-0.003671j
[2025-09-06 20:03:00] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -87.499170-0.004620j
[2025-09-06 20:03:31] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -87.471582+0.002282j
[2025-09-06 20:04:02] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -87.530133-0.000431j
[2025-09-06 20:04:32] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -87.598565-0.001374j
[2025-09-06 20:05:03] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -87.608096-0.002301j
[2025-09-06 20:05:32] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -87.512712+0.004926j
[2025-09-06 20:05:59] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -87.530428+0.000360j
[2025-09-06 20:06:30] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -87.396308+0.001419j
[2025-09-06 20:07:01] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -87.522488+0.001104j
[2025-09-06 20:07:31] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -87.534237+0.001893j
[2025-09-06 20:08:02] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -87.489193+0.000521j
[2025-09-06 20:08:33] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -87.484604-0.002943j
[2025-09-06 20:09:03] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -87.491734+0.000911j
[2025-09-06 20:09:34] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -87.643117-0.001408j
[2025-09-06 20:10:05] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -87.498773+0.000322j
[2025-09-06 20:10:35] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -87.593314-0.002327j
[2025-09-06 20:11:06] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -87.472738+0.000704j
[2025-09-06 20:11:37] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -87.375388+0.002497j
[2025-09-06 20:12:07] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -87.413980-0.002336j
[2025-09-06 20:12:38] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -87.561183+0.000434j
[2025-09-06 20:13:09] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -87.618663+0.003125j
[2025-09-06 20:13:39] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -87.616147-0.002593j
[2025-09-06 20:14:10] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -87.571182-0.000887j
[2025-09-06 20:14:41] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -87.493979-0.001998j
[2025-09-06 20:15:11] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -87.324779+0.002998j
[2025-09-06 20:15:42] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -87.277743-0.000320j
[2025-09-06 20:16:13] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -87.442920+0.000783j
[2025-09-06 20:16:43] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -87.619504+0.000041j
[2025-09-06 20:17:14] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -87.618959-0.001452j
[2025-09-06 20:17:45] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -87.546734+0.004545j
[2025-09-06 20:18:15] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -87.486420-0.002863j
[2025-09-06 20:18:46] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -87.441442-0.001090j
[2025-09-06 20:19:17] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -87.566704-0.001556j
[2025-09-06 20:19:17] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-06 20:19:47] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -87.701520+0.001319j
[2025-09-06 20:20:18] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -87.517465+0.001254j
[2025-09-06 20:20:43] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -87.621429+0.001068j
[2025-09-06 20:21:04] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -87.693899+0.000732j
[2025-09-06 20:21:33] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -87.689068+0.000717j
[2025-09-06 20:22:04] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -87.896547-0.000195j
[2025-09-06 20:22:34] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -87.843376-0.000227j
[2025-09-06 20:23:05] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -87.670911+0.000439j
[2025-09-06 20:23:36] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -87.515561+0.002040j
[2025-09-06 20:24:06] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -87.580579-0.001106j
[2025-09-06 20:24:37] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -87.549315+0.003475j
[2025-09-06 20:25:08] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -87.637735+0.004055j
[2025-09-06 20:25:36] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -87.670697+0.001669j
[2025-09-06 20:26:04] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -87.485493+0.005909j
[2025-09-06 20:26:35] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -87.578220-0.005771j
[2025-09-06 20:27:06] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -87.638327-0.003055j
[2025-09-06 20:27:36] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -87.524582+0.002399j
[2025-09-06 20:28:07] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -87.478133+0.002080j
[2025-09-06 20:28:38] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -87.522481-0.001216j
[2025-09-06 20:29:08] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -87.431319+0.004063j
[2025-09-06 20:29:39] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -87.380942+0.000844j
[2025-09-06 20:30:10] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -87.410392+0.003257j
[2025-09-06 20:30:40] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -87.371699-0.000440j
[2025-09-06 20:31:11] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -87.566629+0.004001j
[2025-09-06 20:31:42] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -87.352731-0.000409j
[2025-09-06 20:32:12] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -87.436987+0.003056j
[2025-09-06 20:32:43] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -87.397651-0.002613j
[2025-09-06 20:33:14] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -87.264373+0.000171j
[2025-09-06 20:33:44] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -87.388948+0.000071j
[2025-09-06 20:34:15] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -87.307395+0.001641j
[2025-09-06 20:34:15] RESTART #2 | Period: 600
[2025-09-06 20:34:46] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -87.401476+0.003902j
[2025-09-06 20:35:16] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -87.280508-0.001796j
[2025-09-06 20:35:47] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -87.436804+0.000304j
[2025-09-06 20:36:18] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -87.309844-0.004571j
[2025-09-06 20:36:48] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -87.344661-0.000094j
[2025-09-06 20:37:19] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -87.295171+0.002566j
[2025-09-06 20:37:50] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -87.300426+0.001642j
[2025-09-06 20:38:20] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -87.494211-0.006490j
[2025-09-06 20:38:51] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -87.421981+0.000289j
[2025-09-06 20:39:22] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -87.426850-0.008267j
[2025-09-06 20:39:52] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -87.393971+0.000410j
[2025-09-06 20:40:23] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -87.485228-0.002605j
[2025-09-06 20:40:46] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -87.287212-0.000790j
[2025-09-06 20:41:08] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -87.609170-0.002566j
[2025-09-06 20:41:39] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -87.627114+0.000103j
[2025-09-06 20:42:09] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -87.519094+0.004610j
[2025-09-06 20:42:40] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -87.683051+0.004512j
[2025-09-06 20:43:10] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -87.627567+0.000193j
[2025-09-06 20:43:41] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -87.495612+0.000122j
[2025-09-06 20:44:12] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -87.491312-0.000768j
[2025-09-06 20:44:42] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -87.589689+0.000312j
[2025-09-06 20:45:13] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -87.394762-0.000008j
[2025-09-06 20:45:41] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -87.412588+0.000951j
[2025-09-06 20:46:10] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -87.515605+0.001763j
[2025-09-06 20:46:40] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -87.591307+0.003498j
[2025-09-06 20:47:11] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -87.542153+0.002126j
[2025-09-06 20:47:42] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -87.644087+0.000542j
[2025-09-06 20:48:12] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -87.593941-0.000290j
[2025-09-06 20:48:43] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -87.484302-0.000723j
[2025-09-06 20:49:13] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -87.531504-0.002101j
[2025-09-06 20:49:44] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -87.535302+0.000762j
[2025-09-06 20:50:15] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -87.572282-0.002156j
[2025-09-06 20:50:45] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -87.553353+0.000377j
[2025-09-06 20:51:16] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -87.556230-0.000860j
[2025-09-06 20:51:47] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -87.532278+0.000722j
[2025-09-06 20:52:17] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -87.486839+0.001679j
[2025-09-06 20:52:48] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -87.531572-0.000065j
[2025-09-06 20:53:19] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -87.503931-0.002285j
[2025-09-06 20:53:49] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -87.652659+0.001664j
[2025-09-06 20:54:20] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -87.617498-0.000911j
[2025-09-06 20:54:51] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -87.487968+0.003915j
[2025-09-06 20:55:21] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -87.678417-0.001632j
[2025-09-06 20:55:52] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -87.557215+0.003217j
[2025-09-06 20:56:23] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -87.619881+0.000550j
[2025-09-06 20:56:53] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -87.640042-0.002164j
[2025-09-06 20:57:24] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -87.584209+0.001595j
[2025-09-06 20:57:55] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -87.563926+0.000104j
[2025-09-06 20:58:25] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -87.620638-0.000699j
[2025-09-06 20:58:56] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -87.621474-0.002950j
[2025-09-06 20:59:26] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -87.614163+0.003993j
[2025-09-06 20:59:57] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -87.673819+0.001015j
[2025-09-06 21:00:28] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -87.682398-0.001178j
[2025-09-06 21:00:49] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -87.643570-0.001661j
[2025-09-06 21:01:13] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -87.860746+0.001616j
[2025-09-06 21:01:44] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -87.731583+0.005203j
[2025-09-06 21:02:14] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -87.667019-0.013433j
[2025-09-06 21:02:45] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -87.730505+0.001940j
[2025-09-06 21:03:16] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -87.641569+0.001595j
[2025-09-06 21:03:46] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -87.643214+0.004738j
[2025-09-06 21:04:17] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -87.573819+0.000569j
[2025-09-06 21:04:48] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -87.595192+0.002904j
[2025-09-06 21:05:18] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -87.509557-0.003039j
[2025-09-06 21:05:45] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -87.629035+0.001703j
[2025-09-06 21:06:15] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -87.578348+0.003929j
[2025-09-06 21:06:46] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -87.515890+0.002118j
[2025-09-06 21:07:17] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -87.644122+0.002405j
[2025-09-06 21:07:47] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -87.840813+0.000745j
[2025-09-06 21:08:18] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -87.663680-0.000528j
[2025-09-06 21:08:49] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -87.640389+0.002648j
[2025-09-06 21:09:19] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -87.573583+0.001670j
[2025-09-06 21:09:50] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -87.708183-0.000208j
[2025-09-06 21:10:20] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -87.413221+0.000829j
[2025-09-06 21:10:51] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -87.498623-0.003690j
[2025-09-06 21:11:22] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -87.453375-0.000401j
[2025-09-06 21:11:52] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -87.334233-0.000312j
[2025-09-06 21:11:53] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-06 21:12:23] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -87.378164+0.002836j
[2025-09-06 21:12:54] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -87.436911-0.000621j
[2025-09-06 21:13:24] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -87.409287-0.000665j
[2025-09-06 21:13:55] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -87.314152+0.000334j
[2025-09-06 21:14:26] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -87.570937-0.003652j
[2025-09-06 21:14:56] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -87.566019-0.001346j
[2025-09-06 21:15:27] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -87.571621+0.000128j
[2025-09-06 21:15:58] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -87.635282-0.000985j
[2025-09-06 21:16:28] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -87.649165+0.004573j
[2025-09-06 21:16:59] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -87.729567+0.003939j
[2025-09-06 21:17:30] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -87.680681-0.008078j
[2025-09-06 21:18:00] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -87.799516+0.002329j
[2025-09-06 21:18:31] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -87.637796+0.001388j
[2025-09-06 21:19:02] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -87.658390+0.011599j
[2025-09-06 21:19:32] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -87.440123+0.003570j
[2025-09-06 21:20:03] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -87.574467-0.002781j
[2025-09-06 21:20:32] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -87.577445-0.002189j
[2025-09-06 21:20:52] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -87.490017+0.003280j
[2025-09-06 21:21:17] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -87.542500-0.000860j
[2025-09-06 21:21:47] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -87.459273+0.005440j
[2025-09-06 21:22:18] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -87.362394-0.004305j
[2025-09-06 21:22:49] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -87.440840-0.000480j
[2025-09-06 21:23:19] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -87.560063+0.000090j
[2025-09-06 21:23:50] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -87.403692+0.002858j
[2025-09-06 21:24:21] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -87.600264-0.003527j
[2025-09-06 21:24:51] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -87.620008-0.003415j
[2025-09-06 21:25:22] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -87.574217-0.002068j
[2025-09-06 21:25:48] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -87.706298+0.000137j
[2025-09-06 21:26:19] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -87.436534+0.004655j
[2025-09-06 21:26:49] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -87.550624+0.001268j
[2025-09-06 21:27:20] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -87.473641-0.002211j
[2025-09-06 21:27:51] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -87.528531-0.000672j
[2025-09-06 21:28:21] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -87.394090+0.001787j
[2025-09-06 21:28:52] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -87.344543-0.001694j
[2025-09-06 21:29:23] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -87.585721+0.001784j
[2025-09-06 21:29:53] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -87.536883-0.002611j
[2025-09-06 21:30:24] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -87.536766+0.001677j
[2025-09-06 21:30:55] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -87.409165-0.000390j
[2025-09-06 21:31:25] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -87.434247-0.001112j
[2025-09-06 21:31:56] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -87.385077+0.000144j
[2025-09-06 21:32:27] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -87.309046-0.003604j
[2025-09-06 21:32:57] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -87.372483+0.001971j
[2025-09-06 21:33:28] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -87.267284-0.000816j
[2025-09-06 21:33:59] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -87.073341+0.000504j
[2025-09-06 21:34:29] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -87.280655-0.001003j
[2025-09-06 21:35:00] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -87.296879+0.001115j
[2025-09-06 21:35:31] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -87.410193+0.000806j
[2025-09-06 21:36:01] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -87.328097+0.000086j
[2025-09-06 21:36:32] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -87.355723-0.002088j
[2025-09-06 21:37:03] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -87.366724-0.001774j
[2025-09-06 21:37:33] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -87.406488-0.002618j
[2025-09-06 21:38:04] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -87.330119+0.001071j
[2025-09-06 21:38:35] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -87.357041-0.000991j
[2025-09-06 21:39:05] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -87.466066+0.000235j
[2025-09-06 21:39:36] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -87.456912-0.000069j
[2025-09-06 21:40:07] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -87.558910-0.002555j
[2025-09-06 21:40:35] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -87.494483+0.000790j
[2025-09-06 21:40:56] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -87.547973+0.001953j
[2025-09-06 21:41:23] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -87.475982+0.001095j
[2025-09-06 21:41:53] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -87.512613-0.002385j
[2025-09-06 21:42:24] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -87.501796-0.003122j
[2025-09-06 21:42:55] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -87.471278-0.008158j
[2025-09-06 21:43:25] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -87.544801-0.000348j
[2025-09-06 21:43:56] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -87.459056-0.003056j
[2025-09-06 21:44:27] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -87.426578+0.006613j
[2025-09-06 21:44:57] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -87.514257+0.003513j
[2025-09-06 21:45:27] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -87.548666+0.001679j
[2025-09-06 21:45:54] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -87.612122-0.002487j
[2025-09-06 21:46:24] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -87.666850+0.004505j
[2025-09-06 21:46:55] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -87.562581-0.000117j
[2025-09-06 21:47:26] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -87.601211+0.001797j
[2025-09-06 21:47:56] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -87.426294+0.005924j
[2025-09-06 21:48:27] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -87.460236-0.000507j
[2025-09-06 21:48:58] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -87.448614+0.000639j
[2025-09-06 21:49:28] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -87.574014-0.003001j
[2025-09-06 21:49:59] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -87.303459-0.000820j
[2025-09-06 21:50:30] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -87.468286-0.004957j
[2025-09-06 21:51:00] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -87.492745+0.000429j
[2025-09-06 21:51:31] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -87.376799-0.000173j
[2025-09-06 21:52:02] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -87.427022-0.003295j
[2025-09-06 21:52:32] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -87.528420+0.002279j
[2025-09-06 21:53:03] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -87.525054+0.001782j
[2025-09-06 21:53:34] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -87.575066-0.000503j
[2025-09-06 21:54:04] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -87.607705-0.001558j
[2025-09-06 21:54:35] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -87.656984-0.002050j
[2025-09-06 21:55:06] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -87.667812-0.002834j
[2025-09-06 21:55:36] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -87.651752-0.002641j
[2025-09-06 21:56:07] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -87.594381-0.003769j
[2025-09-06 21:56:38] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -87.626909-0.000310j
[2025-09-06 21:57:08] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -87.563344+0.000127j
[2025-09-06 21:57:39] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -87.636616-0.002521j
[2025-09-06 21:58:09] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -87.804496-0.000295j
[2025-09-06 21:58:40] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -87.725614+0.002970j
[2025-09-06 21:59:11] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -87.548867-0.002589j
[2025-09-06 21:59:41] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -87.325924-0.002882j
[2025-09-06 22:00:12] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -87.530070+0.002500j
[2025-09-06 22:00:38] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -87.728181-0.000254j
[2025-09-06 22:00:59] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -87.779265+0.000072j
[2025-09-06 22:01:28] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -87.694665+0.000203j
[2025-09-06 22:01:59] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -87.561020+0.004098j
[2025-09-06 22:02:29] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -87.543600-0.000053j
[2025-09-06 22:03:00] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -87.490231-0.000258j
[2025-09-06 22:03:31] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -87.407671+0.001781j
[2025-09-06 22:04:02] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -87.239342-0.000459j
[2025-09-06 22:04:32] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -87.283082-0.003824j
[2025-09-06 22:04:32] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-06 22:05:03] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -87.310536+0.003363j
[2025-09-06 22:05:31] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -87.515062-0.003097j
[2025-09-06 22:06:00] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -87.531502+0.000559j
[2025-09-06 22:06:30] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -87.495956-0.000396j
[2025-09-06 22:07:01] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -87.442982-0.001683j
[2025-09-06 22:07:32] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -87.586141+0.001700j
[2025-09-06 22:08:03] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -87.501558-0.003032j
[2025-09-06 22:08:33] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -87.418111+0.000385j
[2025-09-06 22:09:04] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -87.317637-0.000170j
[2025-09-06 22:09:34] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -87.321864+0.002212j
[2025-09-06 22:10:05] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -87.507618-0.005805j
[2025-09-06 22:10:36] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -87.497114+0.001549j
[2025-09-06 22:11:06] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -87.422519+0.002367j
[2025-09-06 22:11:37] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -87.566110-0.004594j
[2025-09-06 22:12:08] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -87.598206+0.000061j
[2025-09-06 22:12:38] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -87.615511+0.002716j
[2025-09-06 22:13:09] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -87.486076+0.002856j
[2025-09-06 22:13:40] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -87.525243-0.002519j
[2025-09-06 22:14:10] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -87.613047-0.003052j
[2025-09-06 22:14:41] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -87.591420+0.000277j
[2025-09-06 22:15:12] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -87.532624+0.000189j
[2025-09-06 22:15:42] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -87.552645-0.001233j
[2025-09-06 22:16:13] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -87.400281-0.002134j
[2025-09-06 22:16:44] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -87.510980-0.001106j
[2025-09-06 22:17:14] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -87.509576-0.001475j
[2025-09-06 22:17:45] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -87.453498-0.001597j
[2025-09-06 22:18:16] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -87.517174+0.000738j
[2025-09-06 22:18:46] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -87.512456+0.003803j
[2025-09-06 22:19:17] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -87.584532+0.000518j
[2025-09-06 22:19:48] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -87.459758-0.002241j
[2025-09-06 22:20:18] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -87.498744+0.000860j
[2025-09-06 22:20:42] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -87.345177-0.004121j
[2025-09-06 22:21:04] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -87.465189-0.002422j
[2025-09-06 22:21:34] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -87.520514-0.001676j
[2025-09-06 22:22:05] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -87.587016-0.001261j
[2025-09-06 22:22:35] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -87.614949-0.002878j
[2025-09-06 22:23:06] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -87.514504-0.001760j
[2025-09-06 22:23:37] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -87.636135+0.003408j
[2025-09-06 22:24:07] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -87.682184-0.001383j
[2025-09-06 22:24:38] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -87.569803-0.000001j
[2025-09-06 22:25:09] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -87.456753-0.000862j
[2025-09-06 22:25:37] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -87.548286-0.001388j
[2025-09-06 22:26:05] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -87.392059-0.000567j
[2025-09-06 22:26:36] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -87.635124-0.001829j
[2025-09-06 22:27:07] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -87.478798+0.001072j
[2025-09-06 22:27:37] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -87.589265+0.001525j
[2025-09-06 22:28:08] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -87.653207+0.004408j
[2025-09-06 22:28:39] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -87.681484-0.000503j
[2025-09-06 22:29:09] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -87.653798+0.000740j
[2025-09-06 22:29:40] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -87.447001-0.004531j
[2025-09-06 22:30:11] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -87.637277-0.000016j
[2025-09-06 22:30:41] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -87.460026+0.003426j
[2025-09-06 22:31:12] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -87.524476+0.000847j
[2025-09-06 22:31:43] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -87.545801-0.000304j
[2025-09-06 22:32:13] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -87.625974+0.002355j
[2025-09-06 22:32:44] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -87.519289+0.003149j
[2025-09-06 22:33:15] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -87.506912-0.004993j
[2025-09-06 22:33:45] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -87.451352+0.002179j
[2025-09-06 22:34:16] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -87.564959+0.002534j
[2025-09-06 22:34:47] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -87.426282+0.000040j
[2025-09-06 22:35:17] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -87.642438+0.000492j
[2025-09-06 22:35:48] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -87.555468+0.004673j
[2025-09-06 22:36:19] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -87.532298-0.000282j
[2025-09-06 22:36:49] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -87.687835+0.000388j
[2025-09-06 22:37:20] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -87.583564-0.006525j
[2025-09-06 22:37:51] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -87.655426+0.000249j
[2025-09-06 22:38:21] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -87.611869+0.002697j
[2025-09-06 22:38:52] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -87.539322+0.003021j
[2025-09-06 22:39:23] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -87.548424-0.003338j
[2025-09-06 22:39:53] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -87.648796+0.001657j
[2025-09-06 22:40:24] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -87.569909-0.001563j
[2025-09-06 22:40:46] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -87.372543-0.003058j
[2025-09-06 22:41:10] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -87.412896-0.002988j
[2025-09-06 22:41:41] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -87.539627+0.007992j
[2025-09-06 22:42:12] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -87.540660+0.001193j
[2025-09-06 22:42:42] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -87.547015-0.007744j
[2025-09-06 22:43:13] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -87.555175+0.000604j
[2025-09-06 22:43:44] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -87.682881+0.001687j
[2025-09-06 22:44:14] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -87.478375-0.001142j
[2025-09-06 22:44:45] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -87.531307+0.002953j
[2025-09-06 22:45:16] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -87.591635-0.005201j
[2025-09-06 22:45:42] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -87.683924+0.001713j
[2025-09-06 22:46:12] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -87.570922+0.001950j
[2025-09-06 22:46:43] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -87.668324+0.002334j
[2025-09-06 22:47:14] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -87.841871-0.002827j
[2025-09-06 22:47:44] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -87.616203-0.003411j
[2025-09-06 22:48:15] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -87.694932-0.001670j
[2025-09-06 22:48:35] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -87.646826+0.000460j
[2025-09-06 22:48:56] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -87.605238-0.015462j
[2025-09-06 22:49:16] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -87.660264+0.001797j
[2025-09-06 22:49:36] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -87.536514-0.004299j
[2025-09-06 22:49:57] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -87.493097-0.004820j
[2025-09-06 22:50:17] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -87.562911+0.001959j
[2025-09-06 22:50:37] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -87.468195-0.000842j
[2025-09-06 22:50:58] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -87.442802+0.004209j
[2025-09-06 22:51:18] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -87.438868+0.001271j
[2025-09-06 22:51:38] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -87.505146+0.001711j
[2025-09-06 22:51:59] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -87.512548+0.000753j
[2025-09-06 22:52:19] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -87.722696-0.002814j
[2025-09-06 22:52:40] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -87.607079-0.001066j
[2025-09-06 22:53:00] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -87.589070-0.002474j
[2025-09-06 22:53:20] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -87.451905+0.002916j
[2025-09-06 22:53:41] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -87.533807-0.000454j
[2025-09-06 22:54:01] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -87.584190-0.003104j
[2025-09-06 22:54:21] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -87.690599-0.000942j
[2025-09-06 22:54:21] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-06 22:54:42] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -87.639581+0.006155j
[2025-09-06 22:55:02] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -87.683660-0.001428j
[2025-09-06 22:55:22] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -87.571895-0.000269j
[2025-09-06 22:55:43] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -87.623860+0.004765j
[2025-09-06 22:56:03] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -87.608771+0.002800j
[2025-09-06 22:56:21] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -87.527272-0.000723j
[2025-09-06 22:56:30] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -87.491404+0.000774j
[2025-09-06 22:56:40] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -87.582967+0.000730j
[2025-09-06 22:56:53] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -87.446024-0.004439j
[2025-09-06 22:57:14] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -87.505800-0.004281j
[2025-09-06 22:57:34] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -87.444577+0.004090j
[2025-09-06 22:57:55] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -87.439886+0.002920j
[2025-09-06 22:58:15] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -87.444927-0.000300j
[2025-09-06 22:58:36] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -87.445982+0.002832j
[2025-09-06 22:58:56] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -87.410905+0.002679j
[2025-09-06 22:59:17] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -87.748974+0.003301j
[2025-09-06 22:59:37] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -87.667718+0.003478j
[2025-09-06 22:59:51] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -87.654426-0.004440j
[2025-09-06 23:00:06] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -87.648827+0.002256j
[2025-09-06 23:00:26] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -87.577107+0.004949j
[2025-09-06 23:00:47] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -87.505000-0.003183j
[2025-09-06 23:01:07] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -87.556084+0.003518j
[2025-09-06 23:01:27] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -87.474011-0.000076j
[2025-09-06 23:01:48] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -87.505875+0.001572j
[2025-09-06 23:02:08] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -87.461758-0.001864j
[2025-09-06 23:02:28] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -87.545950+0.001183j
[2025-09-06 23:02:49] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -87.325865-0.001573j
[2025-09-06 23:03:09] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -87.517234+0.001478j
[2025-09-06 23:03:29] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -87.533207+0.000527j
[2025-09-06 23:03:50] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -87.471432+0.002185j
[2025-09-06 23:04:10] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -87.414256-0.000131j
[2025-09-06 23:04:30] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -87.527823-0.001581j
[2025-09-06 23:04:51] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -87.581557-0.005948j
[2025-09-06 23:05:11] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -87.575652-0.002390j
[2025-09-06 23:05:31] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -87.560132-0.000769j
[2025-09-06 23:05:52] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -87.495785-0.000094j
[2025-09-06 23:06:12] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -87.501750+0.002622j
[2025-09-06 23:06:32] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -87.500632-0.000431j
[2025-09-06 23:06:53] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -87.403395+0.001858j
[2025-09-06 23:07:13] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -87.344661+0.002290j
[2025-09-06 23:07:33] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -87.333238+0.002981j
[2025-09-06 23:07:54] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -87.402312+0.001899j
[2025-09-06 23:08:14] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -87.372425+0.001039j
[2025-09-06 23:08:34] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -87.479119+0.004379j
[2025-09-06 23:08:55] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -87.306321+0.000809j
[2025-09-06 23:09:15] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -87.439702-0.002008j
[2025-09-06 23:09:35] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -87.567265-0.000296j
[2025-09-06 23:09:50] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -87.589401+0.000240j
[2025-09-06 23:10:00] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -87.650900+0.000802j
[2025-09-06 23:10:09] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -87.601155-0.000287j
[2025-09-06 23:10:23] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -87.584122-0.003542j
[2025-09-06 23:10:44] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -87.529816-0.000127j
[2025-09-06 23:11:04] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -87.601984-0.005646j
[2025-09-06 23:11:25] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -87.450700+0.000077j
[2025-09-06 23:11:45] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -87.384814+0.002068j
[2025-09-06 23:12:06] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -87.407049-0.000180j
[2025-09-06 23:12:26] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -87.481960-0.000691j
[2025-09-06 23:12:46] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -87.448330-0.001439j
[2025-09-06 23:13:07] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -87.521651+0.001960j
[2025-09-06 23:13:21] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -87.559424+0.000094j
[2025-09-06 23:13:36] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -87.398596-0.000607j
[2025-09-06 23:13:56] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -87.457636+0.005476j
[2025-09-06 23:14:17] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -87.523522-0.002815j
[2025-09-06 23:14:37] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -87.488618+0.005827j
[2025-09-06 23:14:57] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -87.471285+0.001975j
[2025-09-06 23:15:18] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -87.424612-0.001592j
[2025-09-06 23:15:38] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -87.462139-0.001744j
[2025-09-06 23:15:58] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -87.456811+0.001903j
[2025-09-06 23:16:19] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -87.505061+0.002647j
[2025-09-06 23:16:39] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -87.444778+0.000557j
[2025-09-06 23:16:59] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -87.395054-0.001401j
[2025-09-06 23:17:20] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -87.476232+0.007392j
[2025-09-06 23:17:40] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -87.469906+0.000774j
[2025-09-06 23:18:00] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -87.265128+0.000062j
[2025-09-06 23:18:21] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -87.273412-0.001476j
[2025-09-06 23:18:41] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -87.288451-0.001655j
[2025-09-06 23:19:01] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -87.247426+0.000730j
[2025-09-06 23:19:22] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -87.045230+0.000376j
[2025-09-06 23:19:42] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -87.360491-0.005755j
[2025-09-06 23:20:02] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -87.304059-0.000426j
[2025-09-06 23:20:23] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -87.362031-0.002399j
[2025-09-06 23:20:43] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -87.350946-0.001392j
[2025-09-06 23:21:03] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -87.377150+0.001564j
[2025-09-06 23:21:24] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -87.420695+0.001323j
[2025-09-06 23:21:44] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -87.388793+0.000315j
[2025-09-06 23:22:04] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -87.569752+0.001085j
[2025-09-06 23:22:25] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -87.470038-0.002230j
[2025-09-06 23:22:45] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -87.432952-0.001614j
[2025-09-06 23:23:05] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -87.509270+0.001702j
[2025-09-06 23:23:19] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -87.591230-0.001684j
[2025-09-06 23:23:28] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -87.611723-0.003590j
[2025-09-06 23:23:38] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -87.619314-0.005159j
[2025-09-06 23:23:48] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -87.654582+0.003435j
[2025-09-06 23:24:08] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -87.635430+0.001241j
[2025-09-06 23:24:29] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -87.432189+0.002456j
[2025-09-06 23:24:49] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -87.522914+0.005632j
[2025-09-06 23:25:10] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -87.559229+0.000034j
[2025-09-06 23:25:30] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -87.575046-0.001014j
[2025-09-06 23:25:51] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -87.539452+0.004043j
[2025-09-06 23:26:11] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -87.360543+0.003633j
[2025-09-06 23:26:32] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -87.480565-0.001422j
[2025-09-06 23:26:46] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -87.541844+0.001218j
[2025-09-06 23:27:01] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -87.457969+0.001198j
[2025-09-06 23:27:21] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -87.393331+0.009816j
[2025-09-06 23:27:42] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -87.481508-0.004564j
[2025-09-06 23:27:42] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-06 23:28:02] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -87.374810-0.004621j
[2025-09-06 23:28:23] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -87.473133+0.000725j
[2025-09-06 23:28:43] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -87.342126-0.001753j
[2025-09-06 23:29:03] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -87.136108-0.002693j
[2025-09-06 23:29:24] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -87.356688-0.000158j
[2025-09-06 23:29:44] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -87.442006-0.000662j
[2025-09-06 23:30:05] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -87.591812-0.001138j
[2025-09-06 23:30:25] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -87.413174-0.000174j
[2025-09-06 23:30:45] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -87.504238-0.003168j
[2025-09-06 23:31:06] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -87.336685-0.001588j
[2025-09-06 23:31:26] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -87.392731-0.000681j
[2025-09-06 23:31:47] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -87.378605-0.002892j
[2025-09-06 23:32:07] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -87.463617-0.001106j
[2025-09-06 23:32:27] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -87.380940-0.000084j
[2025-09-06 23:32:48] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -87.218684+0.003041j
[2025-09-06 23:33:08] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -87.336616+0.004699j
[2025-09-06 23:33:29] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -87.339460+0.001204j
[2025-09-06 23:33:49] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -87.404612+0.002428j
[2025-09-06 23:34:09] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -87.358170-0.000688j
[2025-09-06 23:34:30] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -87.372336+0.001509j
[2025-09-06 23:34:50] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -87.435938-0.003614j
[2025-09-06 23:35:11] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -87.595229-0.000534j
[2025-09-06 23:35:31] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -87.488724+0.000431j
[2025-09-06 23:35:52] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -87.651295+0.003413j
[2025-09-06 23:36:12] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -87.658644-0.001045j
[2025-09-06 23:36:32] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -87.713627-0.000923j
[2025-09-06 23:36:50] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -87.500396+0.001150j
[2025-09-06 23:36:59] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -87.518311+0.002290j
[2025-09-06 23:37:08] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -87.517635-0.000156j
[2025-09-06 23:37:23] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -87.719436+0.001435j
[2025-09-06 23:37:44] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -87.567916-0.002876j
[2025-09-06 23:38:04] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -87.482344-0.000894j
[2025-09-06 23:38:25] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -87.565080-0.000969j
[2025-09-06 23:38:45] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -87.523405-0.001237j
[2025-09-06 23:39:06] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -87.322772+0.002462j
[2025-09-06 23:39:26] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -87.413377-0.001424j
[2025-09-06 23:39:46] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -87.317914+0.000541j
[2025-09-06 23:40:07] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -87.383121-0.000264j
[2025-09-06 23:40:20] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -87.367975-0.003668j
[2025-09-06 23:40:36] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -87.444902+0.000488j
[2025-09-06 23:40:57] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -87.395267-0.001317j
[2025-09-06 23:41:17] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -87.444804+0.000336j
[2025-09-06 23:41:37] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -87.593057-0.002010j
[2025-09-06 23:41:58] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -87.512046+0.002167j
[2025-09-06 23:42:18] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -87.381532+0.001957j
[2025-09-06 23:42:38] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -87.436550+0.001771j
[2025-09-06 23:42:59] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -87.332754+0.001045j
[2025-09-06 23:43:19] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -87.305312-0.000540j
[2025-09-06 23:43:39] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -87.401355-0.000919j
[2025-09-06 23:44:00] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -87.484578+0.002894j
[2025-09-06 23:44:20] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -87.455287-0.001701j
[2025-09-06 23:44:40] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -87.623039+0.001954j
[2025-09-06 23:45:01] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -87.552746+0.004049j
[2025-09-06 23:45:21] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -87.352296-0.003851j
[2025-09-06 23:45:41] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -87.524463-0.001727j
[2025-09-06 23:46:02] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -87.481369-0.000749j
[2025-09-06 23:46:22] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -87.514541+0.002922j
[2025-09-06 23:46:42] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -87.587459-0.000492j
[2025-09-06 23:47:03] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -87.443008+0.000756j
[2025-09-06 23:47:23] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -87.458108-0.000832j
[2025-09-06 23:47:43] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -87.503554+0.002652j
[2025-09-06 23:48:04] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -87.458842+0.000766j
[2025-09-06 23:48:24] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -87.426087+0.003267j
[2025-09-06 23:48:44] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -87.502054-0.000841j
[2025-09-06 23:49:05] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -87.514595+0.000259j
[2025-09-06 23:49:25] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -87.569399+0.000356j
[2025-09-06 23:49:45] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -87.498267+0.001087j
[2025-09-06 23:50:06] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -87.498853-0.002456j
[2025-09-06 23:50:18] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -87.424086+0.001052j
[2025-09-06 23:50:28] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -87.415470+0.003228j
[2025-09-06 23:50:37] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -87.399458-0.004558j
[2025-09-06 23:50:55] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -87.598966+0.000577j
[2025-09-06 23:51:16] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -87.481086+0.009037j
[2025-09-06 23:51:36] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -87.701696+0.001249j
[2025-09-06 23:51:57] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -87.442142+0.002650j
[2025-09-06 23:52:17] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -87.467937+0.002274j
[2025-09-06 23:52:38] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -87.539843-0.002303j
[2025-09-06 23:52:58] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -87.484367+0.001275j
[2025-09-06 23:53:19] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -87.429706-0.001694j
[2025-09-06 23:53:36] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -87.405285+0.000413j
[2025-09-06 23:53:48] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -87.583640+0.003618j
[2025-09-06 23:54:08] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -87.382183-0.002424j
[2025-09-06 23:54:29] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -87.557157+0.000140j
[2025-09-06 23:54:49] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -87.405172-0.002048j
[2025-09-06 23:55:10] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -87.556325+0.001615j
[2025-09-06 23:55:30] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -87.540120+0.002293j
[2025-09-06 23:55:50] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -87.415596+0.000487j
[2025-09-06 23:56:11] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -87.454867-0.001052j
[2025-09-06 23:56:31] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -87.439206-0.002282j
[2025-09-06 23:56:52] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -87.459186-0.001690j
[2025-09-06 23:57:12] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -87.494903-0.002736j
[2025-09-06 23:57:33] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -87.654761-0.001808j
[2025-09-06 23:57:53] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -87.677031+0.000092j
[2025-09-06 23:58:14] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -87.573732-0.000438j
[2025-09-06 23:58:34] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -87.522272+0.000131j
[2025-09-06 23:58:54] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -87.422784-0.002778j
[2025-09-06 23:59:15] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -87.608183+0.005218j
[2025-09-06 23:59:35] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -87.624712-0.001393j
[2025-09-06 23:59:56] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -87.704688-0.001354j
[2025-09-07 00:00:16] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -87.678716-0.004270j
[2025-09-07 00:00:37] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -87.568960+0.001834j
[2025-09-07 00:00:57] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -87.600674-0.002512j
[2025-09-07 00:01:18] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -87.561164-0.002971j
[2025-09-07 00:01:38] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -87.593269+0.000698j
[2025-09-07 00:01:59] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -87.493839-0.000500j
[2025-09-07 00:01:59] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-07 00:02:19] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -87.542621+0.001067j
[2025-09-07 00:02:39] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -87.650189-0.000027j
[2025-09-07 00:03:00] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -87.739308+0.002656j
[2025-09-07 00:03:20] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -87.548644-0.003677j
[2025-09-07 00:03:41] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -87.542025-0.000089j
[2025-09-07 00:03:50] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -87.554158-0.003217j
[2025-09-07 00:04:00] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -87.614305-0.000876j
[2025-09-07 00:04:09] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -87.603792+0.000855j
[2025-09-07 00:04:29] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -87.721466+0.001860j
[2025-09-07 00:04:49] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -87.622041-0.001089j
[2025-09-07 00:05:10] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -87.586946+0.002335j
[2025-09-07 00:05:30] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -87.595004-0.001821j
[2025-09-07 00:05:51] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -87.634346+0.000138j
[2025-09-07 00:06:11] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -87.587403+0.000609j
[2025-09-07 00:06:31] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -87.425944+0.000642j
[2025-09-07 00:06:52] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -87.519878-0.001575j
[2025-09-07 00:07:08] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -87.514366+0.002308j
[2025-09-07 00:07:21] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -87.447931-0.000841j
[2025-09-07 00:07:41] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -87.525747-0.003545j
[2025-09-07 00:08:02] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -87.552508+0.002946j
[2025-09-07 00:08:22] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -87.456808+0.001623j
[2025-09-07 00:08:43] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -87.250396-0.003466j
[2025-09-07 00:09:03] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -87.362640+0.001098j
[2025-09-07 00:09:24] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -87.465896+0.003420j
[2025-09-07 00:09:44] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -87.496304+0.002323j
[2025-09-07 00:10:05] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -87.465370+0.003950j
[2025-09-07 00:10:25] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -87.393960+0.001325j
[2025-09-07 00:10:46] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -87.491879-0.000385j
[2025-09-07 00:11:06] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -87.585283+0.000211j
[2025-09-07 00:11:26] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -87.425238+0.002608j
[2025-09-07 00:11:47] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -87.452494-0.001565j
[2025-09-07 00:12:07] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -87.472106-0.001316j
[2025-09-07 00:12:28] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -87.415631-0.000597j
[2025-09-07 00:12:48] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -87.610258+0.001522j
[2025-09-07 00:13:09] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -87.520902+0.001688j
[2025-09-07 00:13:29] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -87.415643-0.001766j
[2025-09-07 00:13:50] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -87.314724-0.003274j
[2025-09-07 00:14:10] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -87.411831-0.001871j
[2025-09-07 00:14:30] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -87.301405+0.001529j
[2025-09-07 00:14:51] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -87.410714+0.000843j
[2025-09-07 00:15:11] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -87.321149+0.004803j
[2025-09-07 00:15:32] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -87.381477-0.004202j
[2025-09-07 00:15:52] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -87.387766+0.001502j
[2025-09-07 00:16:13] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -87.563344-0.000912j
[2025-09-07 00:16:33] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -87.477387+0.007188j
[2025-09-07 00:16:54] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -87.436581-0.000462j
[2025-09-07 00:17:12] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -87.334043-0.000828j
[2025-09-07 00:17:21] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -87.380127+0.002767j
[2025-09-07 00:17:31] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -87.356304-0.000747j
[2025-09-07 00:17:44] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -87.479819-0.001962j
[2025-09-07 00:18:04] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -87.355091-0.000149j
[2025-09-07 00:18:25] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -87.429851-0.000144j
[2025-09-07 00:18:45] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -87.583170+0.005233j
[2025-09-07 00:19:06] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -87.569118-0.001589j
[2025-09-07 00:19:26] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -87.618887+0.002418j
[2025-09-07 00:19:46] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -87.650814+0.001148j
[2025-09-07 00:20:07] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -87.443589-0.002347j
[2025-09-07 00:20:27] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -87.528310-0.001651j
[2025-09-07 00:20:42] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -87.520312+0.001457j
[2025-09-07 00:20:57] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -87.437562+0.002019j
[2025-09-07 00:21:17] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -87.541730+0.000353j
[2025-09-07 00:21:38] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -87.419545-0.000281j
[2025-09-07 00:21:58] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -87.524332-0.003527j
[2025-09-07 00:22:18] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -87.502154+0.001005j
[2025-09-07 00:22:39] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -87.401850-0.003749j
[2025-09-07 00:22:59] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -87.622867-0.001066j
[2025-09-07 00:23:20] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -87.580787-0.001986j
[2025-09-07 00:23:40] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -87.513760+0.002624j
[2025-09-07 00:24:01] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -87.562399-0.000847j
[2025-09-07 00:24:21] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -87.743805+0.006378j
[2025-09-07 00:24:42] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -87.480883+0.004409j
[2025-09-07 00:25:02] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -87.499953-0.000837j
[2025-09-07 00:25:23] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -87.412591+0.001275j
[2025-09-07 00:25:43] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -87.573196-0.000293j
[2025-09-07 00:26:03] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -87.567427-0.001821j
[2025-09-07 00:26:24] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -87.439681-0.002286j
[2025-09-07 00:26:44] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -87.505949+0.002797j
[2025-09-07 00:27:05] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -87.572728+0.000567j
[2025-09-07 00:27:25] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -87.362605+0.000504j
[2025-09-07 00:27:46] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -87.399740+0.000830j
[2025-09-07 00:28:06] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -87.558096+0.001102j
[2025-09-07 00:28:27] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -87.604179-0.001534j
[2025-09-07 00:28:47] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -87.561162-0.002003j
[2025-09-07 00:29:08] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -87.644076+0.002834j
[2025-09-07 00:29:28] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -87.597302-0.002078j
[2025-09-07 00:29:48] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -87.604915-0.000728j
[2025-09-07 00:30:09] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -87.560083-0.000431j
[2025-09-07 00:30:29] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -87.647118+0.001514j
[2025-09-07 00:30:44] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -87.601325+0.004536j
[2025-09-07 00:30:53] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -87.719947+0.006809j
[2025-09-07 00:31:03] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -87.601748+0.000894j
[2025-09-07 00:31:15] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -87.551317+0.001932j
[2025-09-07 00:31:35] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -87.501116-0.003100j
[2025-09-07 00:31:56] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -87.697435-0.003204j
[2025-09-07 00:32:16] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -87.640441+0.000787j
[2025-09-07 00:32:37] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -87.662594+0.001186j
[2025-09-07 00:32:57] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -87.466227-0.000788j
[2025-09-07 00:33:18] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -87.409447+0.005789j
[2025-09-07 00:33:38] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -87.577446-0.002916j
[2025-09-07 00:33:59] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -87.354293-0.005578j
[2025-09-07 00:34:13] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -87.389455-0.001061j
[2025-09-07 00:34:28] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -87.543665-0.002893j
[2025-09-07 00:34:48] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -87.444358-0.001622j
[2025-09-07 00:35:09] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -87.490227-0.002829j
[2025-09-07 00:35:29] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -87.457471-0.002413j
[2025-09-07 00:35:29] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-07 00:35:29] ✅ Training completed | Restarts: 2
[2025-09-07 00:35:29] ============================================================
[2025-09-07 00:35:29] Training completed | Runtime: 28064.1s
[2025-09-07 00:35:37] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-07 00:35:37] ============================================================
