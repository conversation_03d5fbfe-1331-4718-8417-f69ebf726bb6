[2025-09-07 04:53:58] ✓ 从checkpoint恢复: results/L=5/J2=0.00/J1=0.09/training/checkpoints/final_GCNN.pkl
[2025-09-07 04:53:58]   - 迭代次数: final
[2025-09-07 04:53:58]   - 能量: -87.968371-0.000533j ± 0.107312
[2025-09-07 04:53:58]   - 时间戳: 2025-09-07T04:53:51.521819+08:00
[2025-09-07 04:54:08] ✓ 变分状态参数已从checkpoint恢复
[2025-09-07 04:54:08] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-07 04:54:08] ==================================================
[2025-09-07 04:54:08] GCNN for Shastry-Sutherland Model
[2025-09-07 04:54:08] ==================================================
[2025-09-07 04:54:08] System parameters:
[2025-09-07 04:54:08]   - System size: L=5, N=100
[2025-09-07 04:54:08]   - System parameters: J1=0.1, J2=0.0, Q=1.0
[2025-09-07 04:54:08] --------------------------------------------------
[2025-09-07 04:54:08] Model parameters:
[2025-09-07 04:54:08]   - Number of layers = 4
[2025-09-07 04:54:08]   - Number of features = 4
[2025-09-07 04:54:08]   - Total parameters = 19628
[2025-09-07 04:54:08] --------------------------------------------------
[2025-09-07 04:54:08] Training parameters:
[2025-09-07 04:54:08]   - Learning rate: 0.015
[2025-09-07 04:54:08]   - Total iterations: 1050
[2025-09-07 04:54:08]   - Annealing cycles: 3
[2025-09-07 04:54:08]   - Initial period: 150
[2025-09-07 04:54:08]   - Period multiplier: 2.0
[2025-09-07 04:54:08]   - Temperature range: 0.0-1.0
[2025-09-07 04:54:08]   - Samples: 4096
[2025-09-07 04:54:08]   - Discarded samples: 0
[2025-09-07 04:54:08]   - Chunk size: 2048
[2025-09-07 04:54:08]   - Diagonal shift: 0.2
[2025-09-07 04:54:08]   - Gradient clipping: 1.0
[2025-09-07 04:54:08]   - Checkpoint enabled: interval=105
[2025-09-07 04:54:08]   - Checkpoint directory: results/L=5/J2=0.00/J1=0.10/training/checkpoints
[2025-09-07 04:54:08] --------------------------------------------------
[2025-09-07 04:54:08] Device status:
[2025-09-07 04:54:08]   - Devices model: NVIDIA H200 NVL
[2025-09-07 04:54:08]   - Number of devices: 1
[2025-09-07 04:54:08]   - Sharding: True
[2025-09-07 04:54:08] ============================================================
[2025-09-07 04:54:49] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -88.568912-0.001835j
[2025-09-07 04:55:16] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -88.592749+0.003307j
[2025-09-07 04:55:25] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -88.670594+0.000427j
[2025-09-07 04:55:34] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -88.748613-0.001044j
[2025-09-07 04:55:44] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -88.741985-0.001073j
[2025-09-07 04:55:53] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -88.550059-0.001786j
[2025-09-07 04:56:02] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -88.660191+0.000641j
[2025-09-07 04:56:12] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -88.689977-0.000148j
[2025-09-07 04:56:21] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -88.832758+0.001110j
[2025-09-07 04:56:30] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -88.666811-0.002588j
[2025-09-07 04:56:39] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -88.806655+0.004235j
[2025-09-07 04:56:49] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -88.757755+0.002283j
[2025-09-07 04:56:58] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -88.747085-0.000704j
[2025-09-07 04:57:07] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -88.786985-0.002002j
[2025-09-07 04:57:16] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -88.902496+0.000109j
[2025-09-07 04:57:26] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -88.749654-0.001344j
[2025-09-07 04:57:35] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -88.714660+0.001261j
[2025-09-07 04:57:44] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -88.587058+0.001833j
[2025-09-07 04:57:53] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -88.758223-0.000518j
[2025-09-07 04:58:03] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -88.765382+0.000088j
[2025-09-07 04:58:12] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -88.765622+0.000789j
[2025-09-07 04:58:21] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -88.837495-0.001386j
[2025-09-07 04:58:30] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -88.769134-0.003243j
[2025-09-07 04:58:40] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -88.969838-0.002668j
[2025-09-07 04:58:49] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -88.917542+0.000663j
[2025-09-07 04:58:58] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -88.818576+0.001905j
[2025-09-07 04:59:08] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -89.019723+0.002093j
[2025-09-07 04:59:17] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -88.798673-0.000239j
[2025-09-07 04:59:26] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -88.826423-0.000529j
[2025-09-07 04:59:35] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -88.953720+0.002414j
[2025-09-07 04:59:45] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -88.808651-0.000449j
[2025-09-07 04:59:54] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -88.860682+0.001260j
[2025-09-07 05:00:03] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -88.775683+0.001531j
[2025-09-07 05:00:12] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -88.865406-0.001674j
[2025-09-07 05:00:22] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -88.752332-0.001052j
[2025-09-07 05:00:31] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -88.737699-0.002381j
[2025-09-07 05:00:40] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -88.816374-0.003186j
[2025-09-07 05:00:49] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -88.740071-0.003041j
[2025-09-07 05:00:59] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -88.794391-0.001961j
[2025-09-07 05:01:08] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -88.804342-0.002051j
[2025-09-07 05:01:17] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -88.846495-0.002977j
[2025-09-07 05:01:27] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -88.844894+0.000293j
[2025-09-07 05:01:36] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -88.888993-0.000930j
[2025-09-07 05:01:45] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -88.883546-0.000754j
[2025-09-07 05:01:54] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -88.949648-0.005426j
[2025-09-07 05:02:04] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -88.809629-0.002743j
[2025-09-07 05:02:13] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -88.659729-0.002983j
[2025-09-07 05:02:22] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -88.756437-0.000197j
[2025-09-07 05:02:31] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -88.812599+0.002397j
[2025-09-07 05:02:41] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -88.795811+0.001986j
[2025-09-07 05:02:50] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -88.696058-0.000187j
[2025-09-07 05:02:59] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -88.653392-0.001687j
[2025-09-07 05:03:08] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -88.742435-0.001232j
[2025-09-07 05:03:18] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -88.738068+0.004105j
[2025-09-07 05:03:27] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -88.863836+0.003677j
[2025-09-07 05:03:36] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -88.804296+0.002309j
[2025-09-07 05:03:46] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -88.653001+0.002376j
[2025-09-07 05:03:55] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -88.851731+0.003053j
[2025-09-07 05:04:04] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -88.615688+0.001634j
[2025-09-07 05:04:13] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -88.734644-0.001856j
[2025-09-07 05:04:23] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -88.639707-0.000545j
[2025-09-07 05:04:32] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -88.615909+0.003098j
[2025-09-07 05:04:41] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -88.740396-0.000977j
[2025-09-07 05:04:50] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -88.714113+0.002027j
[2025-09-07 05:05:00] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -88.664868+0.001686j
[2025-09-07 05:05:09] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -88.685831+0.004794j
[2025-09-07 05:05:18] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -88.751525+0.000054j
[2025-09-07 05:05:27] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -88.657378-0.000541j
[2025-09-07 05:05:37] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -88.725476-0.001110j
[2025-09-07 05:05:46] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -88.616198-0.001297j
[2025-09-07 05:05:55] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -88.798668+0.002234j
[2025-09-07 05:06:05] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -88.732636+0.000454j
[2025-09-07 05:06:14] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -88.822320-0.001272j
[2025-09-07 05:06:23] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -88.615241-0.001948j
[2025-09-07 05:06:32] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -88.731898+0.001982j
[2025-09-07 05:06:42] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -88.783070-0.001889j
[2025-09-07 05:06:51] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -89.015550-0.002067j
[2025-09-07 05:07:00] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -88.775512-0.000922j
[2025-09-07 05:07:10] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -88.665720+0.000827j
[2025-09-07 05:07:19] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -88.626958-0.001210j
[2025-09-07 05:07:28] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -88.856977-0.000375j
[2025-09-07 05:07:38] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -88.690454+0.001465j
[2025-09-07 05:07:47] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -88.676321+0.005629j
[2025-09-07 05:07:56] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -88.706542-0.000686j
[2025-09-07 05:08:05] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -88.640866-0.000636j
[2025-09-07 05:08:15] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -88.597075-0.003341j
[2025-09-07 05:08:24] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -88.578072-0.001587j
[2025-09-07 05:08:33] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -88.557388-0.002327j
[2025-09-07 05:08:42] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -88.662871+0.000455j
[2025-09-07 05:08:52] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -88.768509-0.000132j
[2025-09-07 05:09:01] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -88.726977-0.001315j
[2025-09-07 05:09:10] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -88.651006-0.000469j
[2025-09-07 05:09:19] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -88.608909-0.000374j
[2025-09-07 05:09:29] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -88.634368-0.001321j
[2025-09-07 05:09:38] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -88.751836-0.000424j
[2025-09-07 05:09:47] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -88.697957+0.001346j
[2025-09-07 05:09:57] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -88.882155+0.001051j
[2025-09-07 05:10:06] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -88.809487+0.000416j
[2025-09-07 05:10:15] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -88.762028+0.001391j
[2025-09-07 05:10:24] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -88.711118+0.000988j
[2025-09-07 05:10:34] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -88.670096+0.002040j
[2025-09-07 05:10:43] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -88.773179-0.001364j
[2025-09-07 05:10:52] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -88.769954+0.000287j
[2025-09-07 05:11:01] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -88.783862-0.000878j
[2025-09-07 05:11:11] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -88.879866+0.002225j
[2025-09-07 05:11:11] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-07 05:11:20] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -88.677996+0.002134j
[2025-09-07 05:11:29] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -88.824493-0.001602j
[2025-09-07 05:11:38] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -88.884214-0.001117j
[2025-09-07 05:11:48] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -88.888373+0.000612j
[2025-09-07 05:11:57] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -88.688697+0.000096j
[2025-09-07 05:12:06] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -88.869234+0.001543j
[2025-09-07 05:12:16] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -88.833114+0.002925j
[2025-09-07 05:12:25] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -88.813604+0.000929j
[2025-09-07 05:12:34] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -88.770475+0.000724j
[2025-09-07 05:12:43] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -88.597180+0.001999j
[2025-09-07 05:12:53] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -88.659776-0.002738j
[2025-09-07 05:13:02] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -88.786754-0.004151j
[2025-09-07 05:13:11] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -88.871310+0.003223j
[2025-09-07 05:13:20] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -88.962824+0.001026j
[2025-09-07 05:13:30] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -88.891574-0.005411j
[2025-09-07 05:13:39] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -88.806146-0.001620j
[2025-09-07 05:13:48] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -88.768731+0.003166j
[2025-09-07 05:13:57] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -88.768297-0.003714j
[2025-09-07 05:14:07] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -88.831496+0.000500j
[2025-09-07 05:14:16] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -88.664467+0.000945j
[2025-09-07 05:14:25] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -88.572788-0.001031j
[2025-09-07 05:14:34] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -88.603828-0.001546j
[2025-09-07 05:14:44] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -88.731137+0.000493j
[2025-09-07 05:14:53] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -88.675522-0.001904j
[2025-09-07 05:15:02] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -88.736714-0.003305j
[2025-09-07 05:15:12] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -88.727528-0.002547j
[2025-09-07 05:15:21] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -88.852682-0.003442j
[2025-09-07 05:15:30] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -88.699059-0.002643j
[2025-09-07 05:15:39] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -88.865559-0.001422j
[2025-09-07 05:15:49] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -88.773962+0.001122j
[2025-09-07 05:15:58] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -88.705455-0.005569j
[2025-09-07 05:16:07] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -88.625984-0.001553j
[2025-09-07 05:16:16] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -88.629783-0.002434j
[2025-09-07 05:16:26] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -88.643323+0.001254j
[2025-09-07 05:16:35] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -88.842646-0.000345j
[2025-09-07 05:16:44] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -88.758893-0.000158j
[2025-09-07 05:16:53] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -88.776592-0.000949j
[2025-09-07 05:17:03] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -88.742135+0.003236j
[2025-09-07 05:17:12] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -88.675722-0.000354j
[2025-09-07 05:17:21] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -88.763155-0.001841j
[2025-09-07 05:17:30] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -88.759238-0.001590j
[2025-09-07 05:17:40] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -88.794279-0.003406j
[2025-09-07 05:17:49] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -88.711889-0.005770j
[2025-09-07 05:17:58] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -88.935020-0.003964j
[2025-09-07 05:18:07] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -88.907584-0.000711j
[2025-09-07 05:18:07] RESTART #1 | Period: 300
[2025-09-07 05:18:17] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -88.935905-0.000322j
[2025-09-07 05:18:26] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -88.778147-0.002746j
[2025-09-07 05:18:35] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -88.849124+0.000745j
[2025-09-07 05:18:45] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -88.631251-0.000980j
[2025-09-07 05:18:54] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -88.669679-0.001060j
[2025-09-07 05:19:03] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -88.792356+0.004766j
[2025-09-07 05:19:12] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -88.825282+0.001359j
[2025-09-07 05:19:22] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -88.811433+0.000692j
[2025-09-07 05:19:31] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -88.559745-0.002328j
[2025-09-07 05:19:40] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -88.624613+0.000321j
[2025-09-07 05:19:49] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -88.743357+0.000958j
[2025-09-07 05:19:59] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -88.697424-0.001685j
[2025-09-07 05:20:08] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -88.650114-0.002821j
[2025-09-07 05:20:17] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -88.689297+0.004517j
[2025-09-07 05:20:26] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -88.631196-0.000615j
[2025-09-07 05:20:36] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -88.628612-0.005010j
[2025-09-07 05:20:45] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -88.600536-0.000001j
[2025-09-07 05:20:54] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -88.665017-0.000790j
[2025-09-07 05:21:04] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -88.727404+0.001443j
[2025-09-07 05:21:13] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -88.725976-0.002246j
[2025-09-07 05:21:22] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -88.735965-0.000786j
[2025-09-07 05:21:31] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -88.789507+0.001093j
[2025-09-07 05:21:41] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -88.698901+0.001009j
[2025-09-07 05:21:50] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -88.716179+0.005625j
[2025-09-07 05:21:59] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -88.526948-0.001190j
[2025-09-07 05:22:08] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -88.661639-0.000366j
[2025-09-07 05:22:18] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -88.730742-0.000492j
[2025-09-07 05:22:27] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -88.680559+0.003374j
[2025-09-07 05:22:36] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -88.717477-0.000838j
[2025-09-07 05:22:45] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -88.828204-0.000082j
[2025-09-07 05:22:55] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -88.706975+0.000256j
[2025-09-07 05:23:04] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -88.811122+0.003979j
[2025-09-07 05:23:13] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -88.745627+0.005574j
[2025-09-07 05:23:23] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -88.730912+0.001108j
[2025-09-07 05:23:32] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -88.921184+0.003590j
[2025-09-07 05:23:41] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -88.986455-0.001378j
[2025-09-07 05:23:50] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -88.731620-0.000384j
[2025-09-07 05:24:00] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -88.811397+0.001880j
[2025-09-07 05:24:09] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -88.748006-0.001920j
[2025-09-07 05:24:18] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -88.702981-0.005188j
[2025-09-07 05:24:27] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -88.766969+0.002194j
[2025-09-07 05:24:37] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -88.567020-0.001236j
[2025-09-07 05:24:46] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -88.590990-0.001181j
[2025-09-07 05:24:55] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -88.782365-0.002095j
[2025-09-07 05:25:04] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -88.693978-0.003046j
[2025-09-07 05:25:14] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -88.765612+0.003151j
[2025-09-07 05:25:23] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -88.580717-0.001100j
[2025-09-07 05:25:32] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -88.754393-0.000637j
[2025-09-07 05:25:42] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -88.728818+0.000521j
[2025-09-07 05:25:51] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -88.878712+0.002349j
[2025-09-07 05:26:00] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -88.740166-0.002368j
[2025-09-07 05:26:09] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -88.708264-0.000489j
[2025-09-07 05:26:19] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -88.713956-0.001352j
[2025-09-07 05:26:28] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -88.661648+0.002528j
[2025-09-07 05:26:37] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -88.707801+0.006854j
[2025-09-07 05:26:46] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -88.902087+0.002519j
[2025-09-07 05:26:56] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -88.698468-0.002963j
[2025-09-07 05:27:05] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -88.927366+0.006164j
[2025-09-07 05:27:14] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -88.872631+0.001009j
[2025-09-07 05:27:23] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -88.594039+0.007218j
[2025-09-07 05:27:23] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-07 05:27:33] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -88.655524-0.002930j
[2025-09-07 05:27:42] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -88.789673+0.002177j
[2025-09-07 05:27:51] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -88.591762+0.002615j
[2025-09-07 05:28:00] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -88.554890+0.000591j
[2025-09-07 05:28:10] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -88.732550+0.002862j
[2025-09-07 05:28:19] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -88.723800+0.000895j
[2025-09-07 05:28:28] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -88.783991+0.001057j
[2025-09-07 05:28:37] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -88.645337-0.003210j
[2025-09-07 05:28:47] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -88.856618+0.003826j
[2025-09-07 05:28:56] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -88.744919-0.001603j
[2025-09-07 05:29:05] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -88.794175+0.001993j
[2025-09-07 05:29:15] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -88.774698+0.001132j
[2025-09-07 05:29:24] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -88.924972-0.001661j
[2025-09-07 05:29:33] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -88.968987-0.000089j
[2025-09-07 05:29:42] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -88.886677-0.003193j
[2025-09-07 05:29:52] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -88.902945+0.000972j
[2025-09-07 05:30:01] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -88.801074+0.000295j
[2025-09-07 05:30:10] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -88.872595+0.000163j
[2025-09-07 05:30:19] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -88.837702-0.000123j
[2025-09-07 05:30:29] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -88.624990+0.003575j
[2025-09-07 05:30:38] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -88.751361-0.002503j
[2025-09-07 05:30:47] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -88.664040-0.001892j
[2025-09-07 05:30:56] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -88.667470-0.001798j
[2025-09-07 05:31:06] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -88.749245-0.003896j
[2025-09-07 05:31:15] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -88.632147-0.004716j
[2025-09-07 05:31:24] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -88.719883-0.001441j
[2025-09-07 05:31:33] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -88.700608+0.006616j
[2025-09-07 05:31:43] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -88.609322+0.000994j
[2025-09-07 05:31:52] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -88.661814+0.000693j
[2025-09-07 05:32:01] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -88.657373-0.000120j
[2025-09-07 05:32:11] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -88.688859+0.005269j
[2025-09-07 05:32:20] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -88.708632+0.001137j
[2025-09-07 05:32:29] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -88.868865-0.000388j
[2025-09-07 05:32:38] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -88.830943+0.000196j
[2025-09-07 05:32:48] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -88.900132-0.004300j
[2025-09-07 05:32:57] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -88.854970-0.000831j
[2025-09-07 05:33:06] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -88.800986+0.000517j
[2025-09-07 05:33:15] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -88.810132-0.003416j
[2025-09-07 05:33:25] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -88.763924+0.000218j
[2025-09-07 05:33:34] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -88.820648+0.001531j
[2025-09-07 05:33:43] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -88.732950-0.000053j
[2025-09-07 05:33:52] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -88.779493-0.002750j
[2025-09-07 05:34:02] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -88.816983-0.001311j
[2025-09-07 05:34:11] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -88.785657+0.000343j
[2025-09-07 05:34:20] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -88.601994-0.000225j
[2025-09-07 05:34:30] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -88.742151+0.002105j
[2025-09-07 05:34:39] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -88.703909-0.000796j
[2025-09-07 05:34:48] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -88.737084-0.000633j
[2025-09-07 05:34:57] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -88.656946-0.002363j
[2025-09-07 05:35:07] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -88.629897+0.001938j
[2025-09-07 05:35:16] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -88.743513+0.000525j
[2025-09-07 05:35:25] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -88.896540-0.000846j
[2025-09-07 05:35:34] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -88.900187+0.001597j
[2025-09-07 05:35:44] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -88.884773-0.003348j
[2025-09-07 05:35:53] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -88.852730+0.001175j
[2025-09-07 05:36:02] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -88.770713-0.001779j
[2025-09-07 05:36:11] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -88.991710+0.000363j
[2025-09-07 05:36:21] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -88.843946-0.002002j
[2025-09-07 05:36:30] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -88.903181-0.003009j
[2025-09-07 05:36:39] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -88.878829-0.000584j
[2025-09-07 05:36:49] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -88.984149-0.000167j
[2025-09-07 05:36:58] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -88.872611-0.001585j
[2025-09-07 05:37:07] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -88.851207-0.002144j
[2025-09-07 05:37:16] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -88.710100+0.001311j
[2025-09-07 05:37:26] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -88.811879-0.000294j
[2025-09-07 05:37:35] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -88.758820-0.001571j
[2025-09-07 05:37:44] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -88.782709-0.000971j
[2025-09-07 05:37:53] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -88.639596-0.002496j
[2025-09-07 05:38:03] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -88.600685+0.005761j
[2025-09-07 05:38:12] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -88.708824-0.000529j
[2025-09-07 05:38:21] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -88.882953-0.004806j
[2025-09-07 05:38:30] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -88.784260-0.002415j
[2025-09-07 05:38:40] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -88.667297-0.000599j
[2025-09-07 05:38:49] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -88.689673+0.001150j
[2025-09-07 05:38:58] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -88.740588+0.007865j
[2025-09-07 05:39:07] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -88.751636-0.003417j
[2025-09-07 05:39:17] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -88.803979+0.003289j
[2025-09-07 05:39:26] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -88.774558-0.001665j
[2025-09-07 05:39:35] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -88.593805+0.003714j
[2025-09-07 05:39:44] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -88.683679+0.001538j
[2025-09-07 05:39:54] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -88.976085+0.002455j
[2025-09-07 05:40:03] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -88.866929+0.001916j
[2025-09-07 05:40:12] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -88.884642-0.001173j
[2025-09-07 05:40:22] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -88.718519+0.002074j
[2025-09-07 05:40:31] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -88.900876+0.002139j
[2025-09-07 05:40:40] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -88.856824+0.001064j
[2025-09-07 05:40:49] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -89.105137-0.001327j
[2025-09-07 05:40:59] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -88.840256+0.001365j
[2025-09-07 05:41:08] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -88.919288+0.001547j
[2025-09-07 05:41:17] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -88.760549+0.000965j
[2025-09-07 05:41:26] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -88.889857-0.002363j
[2025-09-07 05:41:36] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -88.914957+0.000069j
[2025-09-07 05:41:45] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -88.809771-0.000989j
[2025-09-07 05:41:54] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -88.705295-0.001535j
[2025-09-07 05:42:03] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -88.646833-0.000041j
[2025-09-07 05:42:13] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -88.657826+0.000006j
[2025-09-07 05:42:22] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -88.670971+0.003266j
[2025-09-07 05:42:31] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -88.786196-0.001780j
[2025-09-07 05:42:40] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -88.756777-0.001843j
[2025-09-07 05:42:50] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -88.795416+0.003437j
[2025-09-07 05:42:59] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -88.802352+0.000623j
[2025-09-07 05:43:08] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -88.646913-0.002299j
[2025-09-07 05:43:17] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -88.541909+0.000863j
[2025-09-07 05:43:27] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -88.560322+0.001738j
[2025-09-07 05:43:36] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -88.680186-0.002876j
[2025-09-07 05:43:36] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-07 05:43:45] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -88.743340-0.001641j
[2025-09-07 05:43:55] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -88.863702+0.001498j
[2025-09-07 05:44:04] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -88.801648+0.001298j
[2025-09-07 05:44:13] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -88.829021+0.001395j
[2025-09-07 05:44:22] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -88.720918+0.000327j
[2025-09-07 05:44:32] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -88.854261+0.000809j
[2025-09-07 05:44:41] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -88.979633-0.000532j
[2025-09-07 05:44:50] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -88.969873+0.000254j
[2025-09-07 05:45:00] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -88.742999-0.002006j
[2025-09-07 05:45:09] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -88.776389-0.001530j
[2025-09-07 05:45:18] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -88.671872+0.001555j
[2025-09-07 05:45:27] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -88.735260+0.001479j
[2025-09-07 05:45:37] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -88.768807-0.002429j
[2025-09-07 05:45:46] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -88.798525+0.000227j
[2025-09-07 05:45:55] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -88.810127+0.000313j
[2025-09-07 05:46:04] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -88.655692-0.000331j
[2025-09-07 05:46:14] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -88.604996-0.000835j
[2025-09-07 05:46:23] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -88.650928-0.001366j
[2025-09-07 05:46:32] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -88.596851+0.000652j
[2025-09-07 05:46:41] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -88.618374-0.001829j
[2025-09-07 05:46:51] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -88.646825+0.001287j
[2025-09-07 05:47:00] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -88.792462-0.000668j
[2025-09-07 05:47:09] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -88.735076-0.002612j
[2025-09-07 05:47:18] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -88.523016-0.000438j
[2025-09-07 05:47:28] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -88.689771+0.003937j
[2025-09-07 05:47:37] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -88.711260+0.001418j
[2025-09-07 05:47:46] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -88.508893-0.000705j
[2025-09-07 05:47:56] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -88.682219-0.000439j
[2025-09-07 05:48:05] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -88.639102+0.000180j
[2025-09-07 05:48:14] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -88.658102+0.001592j
[2025-09-07 05:48:23] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -88.800811+0.001874j
[2025-09-07 05:48:33] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -88.716575+0.003171j
[2025-09-07 05:48:42] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -88.774648+0.001714j
[2025-09-07 05:48:51] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -88.690976-0.002056j
[2025-09-07 05:49:00] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -88.696223-0.001075j
[2025-09-07 05:49:10] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -88.822974+0.002124j
[2025-09-07 05:49:19] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -88.779567+0.000594j
[2025-09-07 05:49:28] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -88.806280+0.001675j
[2025-09-07 05:49:37] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -88.754457-0.004481j
[2025-09-07 05:49:47] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -88.698343-0.001509j
[2025-09-07 05:49:56] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -88.860129-0.002226j
[2025-09-07 05:50:05] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -89.037060+0.002857j
[2025-09-07 05:50:14] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -88.768069+0.001057j
[2025-09-07 05:50:24] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -88.731179+0.001232j
[2025-09-07 05:50:33] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -88.689968-0.001876j
[2025-09-07 05:50:42] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -88.810544+0.001706j
[2025-09-07 05:50:52] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -88.652276+0.002066j
[2025-09-07 05:51:01] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -88.842731-0.001068j
[2025-09-07 05:51:10] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -88.905704-0.001508j
[2025-09-07 05:51:19] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -88.787731-0.001864j
[2025-09-07 05:51:29] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -88.766466+0.006500j
[2025-09-07 05:51:38] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -88.835325+0.000314j
[2025-09-07 05:51:47] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -88.720321-0.001355j
[2025-09-07 05:51:56] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -88.698831+0.001218j
[2025-09-07 05:52:06] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -88.739261+0.000232j
[2025-09-07 05:52:15] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -88.764344-0.001264j
[2025-09-07 05:52:24] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -88.704634+0.002232j
[2025-09-07 05:52:33] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -88.730126-0.001383j
[2025-09-07 05:52:43] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -88.893796-0.002973j
[2025-09-07 05:52:52] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -88.810849+0.002125j
[2025-09-07 05:53:01] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -88.885746+0.003342j
[2025-09-07 05:53:10] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -88.739746+0.003814j
[2025-09-07 05:53:20] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -88.689011+0.001552j
[2025-09-07 05:53:29] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -88.718043+0.001536j
[2025-09-07 05:53:38] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -88.685018-0.004016j
[2025-09-07 05:53:48] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -88.745986+0.000298j
[2025-09-07 05:53:57] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -88.784490-0.002591j
[2025-09-07 05:54:06] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -88.664764+0.002728j
[2025-09-07 05:54:15] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -88.743417+0.001976j
[2025-09-07 05:54:25] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -88.646407-0.001315j
[2025-09-07 05:54:34] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -88.650822+0.000409j
[2025-09-07 05:54:43] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -88.733387+0.003364j
[2025-09-07 05:54:52] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -88.942647+0.000294j
[2025-09-07 05:55:02] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -88.817075-0.000916j
[2025-09-07 05:55:11] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -88.906231-0.000914j
[2025-09-07 05:55:20] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -88.891656-0.000174j
[2025-09-07 05:55:29] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -88.903374-0.002874j
[2025-09-07 05:55:39] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -88.886244+0.001763j
[2025-09-07 05:55:48] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -88.733945+0.000658j
[2025-09-07 05:55:57] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -88.823403+0.000761j
[2025-09-07 05:56:06] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -88.821964+0.001894j
[2025-09-07 05:56:16] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -88.834566-0.001905j
[2025-09-07 05:56:25] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -88.854630+0.005331j
[2025-09-07 05:56:34] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -88.872087+0.002335j
[2025-09-07 05:56:44] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -88.842255+0.002846j
[2025-09-07 05:56:53] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -88.757265-0.001019j
[2025-09-07 05:57:02] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -88.750806-0.002798j
[2025-09-07 05:57:11] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -88.903915+0.000509j
[2025-09-07 05:57:21] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -88.799087+0.000894j
[2025-09-07 05:57:30] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -88.773460-0.004052j
[2025-09-07 05:57:39] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -88.795016-0.001734j
[2025-09-07 05:57:48] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -88.797684-0.002101j
[2025-09-07 05:57:58] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -88.755375+0.000098j
[2025-09-07 05:58:07] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -88.773803+0.001422j
[2025-09-07 05:58:16] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -88.642854+0.001465j
[2025-09-07 05:58:25] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -88.735417-0.004683j
[2025-09-07 05:58:35] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -88.575378+0.004170j
[2025-09-07 05:58:44] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -88.601459-0.003331j
[2025-09-07 05:58:53] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -88.834823-0.000430j
[2025-09-07 05:59:03] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -88.551888-0.003146j
[2025-09-07 05:59:12] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -88.654696-0.001128j
[2025-09-07 05:59:21] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -88.670836+0.001537j
[2025-09-07 05:59:30] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -88.796068+0.000136j
[2025-09-07 05:59:40] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -88.739956-0.000907j
[2025-09-07 05:59:49] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -88.863883-0.000427j
[2025-09-07 05:59:49] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-07 05:59:58] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -88.876790+0.003903j
[2025-09-07 06:00:07] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -88.937779-0.000757j
[2025-09-07 06:00:17] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -88.935754-0.000245j
[2025-09-07 06:00:26] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -88.947174+0.000866j
[2025-09-07 06:00:35] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -88.842544+0.002838j
[2025-09-07 06:00:45] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -88.709061+0.003958j
[2025-09-07 06:00:54] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -88.902820+0.000496j
[2025-09-07 06:01:03] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -88.724644+0.001494j
[2025-09-07 06:01:12] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -88.808542-0.000085j
[2025-09-07 06:01:22] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -88.794406+0.000354j
[2025-09-07 06:01:31] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -88.777525+0.001379j
[2025-09-07 06:01:40] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -88.712107-0.002399j
[2025-09-07 06:01:49] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -88.814747-0.003915j
[2025-09-07 06:01:59] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -88.826655-0.000938j
[2025-09-07 06:02:08] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -88.700760-0.004395j
[2025-09-07 06:02:17] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -88.888981+0.004661j
[2025-09-07 06:02:27] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -89.029346+0.001165j
[2025-09-07 06:02:36] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -88.995999+0.000612j
[2025-09-07 06:02:45] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -88.893145-0.000422j
[2025-09-07 06:02:54] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -88.789753+0.001338j
[2025-09-07 06:03:04] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -88.847189+0.001196j
[2025-09-07 06:03:13] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -88.682583-0.000144j
[2025-09-07 06:03:22] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -88.675542+0.000531j
[2025-09-07 06:03:31] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -88.718322+0.004847j
[2025-09-07 06:03:41] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -88.896280+0.001159j
[2025-09-07 06:03:50] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -88.967284+0.001361j
[2025-09-07 06:03:59] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -88.771415-0.001344j
[2025-09-07 06:04:08] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -88.807443+0.000722j
[2025-09-07 06:04:18] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -88.784628-0.001323j
[2025-09-07 06:04:27] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -88.629581+0.002392j
[2025-09-07 06:04:27] RESTART #2 | Period: 600
[2025-09-07 06:04:36] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -88.736958-0.002937j
[2025-09-07 06:04:45] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -88.697418+0.001805j
[2025-09-07 06:04:55] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -88.837979+0.003454j
[2025-09-07 06:05:04] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -88.933088-0.000154j
[2025-09-07 06:05:13] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -88.845141-0.000118j
[2025-09-07 06:05:22] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -88.800872+0.002053j
[2025-09-07 06:05:32] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -88.559000+0.001728j
[2025-09-07 06:05:41] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -88.684049-0.000658j
[2025-09-07 06:05:50] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -88.750579-0.000870j
[2025-09-07 06:05:59] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -88.914006+0.003389j
[2025-09-07 06:06:09] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -88.909434-0.001009j
[2025-09-07 06:06:18] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -88.801055+0.001006j
[2025-09-07 06:06:27] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -88.871861-0.003353j
[2025-09-07 06:06:36] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -88.749016-0.002942j
[2025-09-07 06:06:46] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -88.687168-0.002182j
[2025-09-07 06:06:55] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -88.710885+0.005768j
[2025-09-07 06:07:04] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -88.708868+0.001026j
[2025-09-07 06:07:14] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -88.770589+0.000949j
[2025-09-07 06:07:23] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -88.642907+0.000374j
[2025-09-07 06:07:32] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -88.563604+0.003662j
[2025-09-07 06:07:41] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -88.610401+0.001277j
[2025-09-07 06:07:51] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -88.488126+0.002054j
[2025-09-07 06:08:00] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -88.599824-0.004439j
[2025-09-07 06:08:09] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -88.630374+0.003653j
[2025-09-07 06:08:19] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -88.722036-0.002733j
[2025-09-07 06:08:28] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -88.691981+0.000190j
[2025-09-07 06:08:37] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -88.649921+0.003765j
[2025-09-07 06:08:46] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -88.636977+0.001701j
[2025-09-07 06:08:56] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -88.857974+0.002998j
[2025-09-07 06:09:05] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -88.829155-0.000472j
[2025-09-07 06:09:14] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -88.907527+0.002228j
[2025-09-07 06:09:23] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -88.896185+0.000444j
[2025-09-07 06:09:33] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -88.917175+0.002263j
[2025-09-07 06:09:42] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -88.971522-0.000241j
[2025-09-07 06:09:51] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -88.979125-0.000908j
[2025-09-07 06:10:00] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -88.980799+0.003139j
[2025-09-07 06:10:10] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -89.049532+0.001885j
[2025-09-07 06:10:19] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -88.980834+0.001156j
[2025-09-07 06:10:28] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -88.787491+0.000983j
[2025-09-07 06:10:38] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -88.849510+0.000567j
[2025-09-07 06:10:47] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -88.924783-0.002804j
[2025-09-07 06:10:56] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -88.768870+0.002319j
[2025-09-07 06:11:05] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -88.770485-0.002122j
[2025-09-07 06:11:15] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -88.941698-0.001357j
[2025-09-07 06:11:24] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -88.741831-0.000764j
[2025-09-07 06:11:33] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -88.900415+0.001629j
[2025-09-07 06:11:42] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -88.890029+0.000884j
[2025-09-07 06:11:52] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -88.814803+0.001388j
[2025-09-07 06:12:01] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -88.808786+0.000060j
[2025-09-07 06:12:10] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -88.744703+0.000247j
[2025-09-07 06:12:19] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -88.759007-0.000397j
[2025-09-07 06:12:29] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -88.951992+0.000596j
[2025-09-07 06:12:38] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -88.739377+0.002330j
[2025-09-07 06:12:47] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -88.787706+0.001486j
[2025-09-07 06:12:56] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -88.759098-0.001617j
[2025-09-07 06:13:06] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -88.842352+0.002551j
[2025-09-07 06:13:15] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -88.832696-0.001460j
[2025-09-07 06:13:24] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -88.880078-0.000344j
[2025-09-07 06:13:34] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -88.550972+0.000364j
[2025-09-07 06:13:43] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -88.602643+0.000428j
[2025-09-07 06:13:52] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -88.772127-0.000671j
[2025-09-07 06:14:01] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -88.751132-0.000672j
[2025-09-07 06:14:11] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -88.709798-0.002653j
[2025-09-07 06:14:20] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -88.695230-0.000072j
[2025-09-07 06:14:29] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -88.775222+0.001044j
[2025-09-07 06:14:38] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -88.784016-0.002232j
[2025-09-07 06:14:48] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -88.860814-0.002469j
[2025-09-07 06:14:57] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -88.847934-0.001061j
[2025-09-07 06:15:06] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -88.886989-0.001516j
[2025-09-07 06:15:15] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -88.945249-0.001999j
[2025-09-07 06:15:25] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -88.697755-0.000538j
[2025-09-07 06:15:34] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -88.804771+0.000549j
[2025-09-07 06:15:43] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -88.750117-0.000323j
[2025-09-07 06:15:53] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -88.824526+0.001615j
[2025-09-07 06:16:02] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -88.915908+0.002341j
[2025-09-07 06:16:02] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-07 06:16:11] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -88.947617-0.000315j
[2025-09-07 06:16:20] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -88.788610-0.001394j
[2025-09-07 06:16:30] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -88.717221+0.001004j
[2025-09-07 06:16:39] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -88.695563-0.000592j
[2025-09-07 06:16:48] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -88.786252-0.000999j
[2025-09-07 06:16:57] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -88.852822-0.002054j
[2025-09-07 06:17:07] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -88.614916+0.002068j
[2025-09-07 06:17:16] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -88.709224+0.000384j
[2025-09-07 06:17:25] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -88.765940-0.000976j
[2025-09-07 06:17:34] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -88.696186+0.002704j
[2025-09-07 06:17:44] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -88.779462-0.000012j
[2025-09-07 06:17:53] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -88.659249-0.001175j
[2025-09-07 06:18:02] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -88.752975+0.002130j
[2025-09-07 06:18:11] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -88.791684+0.002709j
[2025-09-07 06:18:21] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -88.665292+0.001077j
[2025-09-07 06:18:30] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -88.844714-0.003408j
[2025-09-07 06:18:39] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -88.835748+0.002180j
[2025-09-07 06:18:49] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -88.768290+0.002901j
[2025-09-07 06:18:58] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -88.593984-0.000317j
[2025-09-07 06:19:07] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -88.883628-0.000407j
[2025-09-07 06:19:16] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -88.832115-0.001636j
[2025-09-07 06:19:26] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -88.787852-0.002553j
[2025-09-07 06:19:35] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -88.823487-0.000126j
[2025-09-07 06:19:44] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -88.868762-0.000894j
[2025-09-07 06:19:53] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -88.849404+0.000671j
[2025-09-07 06:20:03] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -88.813117-0.000945j
[2025-09-07 06:20:12] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -88.779091-0.001034j
[2025-09-07 06:20:21] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -88.956902+0.000102j
[2025-09-07 06:20:30] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -88.966501+0.003373j
[2025-09-07 06:20:40] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -88.854473+0.001807j
[2025-09-07 06:20:49] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -88.999737+0.001471j
[2025-09-07 06:20:58] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -88.763938-0.002958j
[2025-09-07 06:21:08] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -88.968172-0.004078j
[2025-09-07 06:21:17] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -88.924286+0.002153j
[2025-09-07 06:21:26] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -88.946005+0.001937j
[2025-09-07 06:21:35] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -88.814081-0.001255j
[2025-09-07 06:21:45] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -88.810851-0.001742j
[2025-09-07 06:21:54] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -88.809191-0.001065j
[2025-09-07 06:22:03] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -88.752043-0.003066j
[2025-09-07 06:22:12] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -88.709165+0.004137j
[2025-09-07 06:22:22] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -88.680706-0.002079j
[2025-09-07 06:22:31] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -88.752619+0.002618j
[2025-09-07 06:22:40] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -88.751489-0.000867j
[2025-09-07 06:22:49] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -88.604865-0.001242j
[2025-09-07 06:22:59] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -88.671512+0.000642j
[2025-09-07 06:23:08] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -88.808524-0.001727j
[2025-09-07 06:23:17] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -88.813751+0.003259j
[2025-09-07 06:23:27] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -88.831100+0.000468j
[2025-09-07 06:23:36] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -88.586244-0.002139j
[2025-09-07 06:23:45] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -88.815755-0.000185j
[2025-09-07 06:23:54] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -88.760227+0.001967j
[2025-09-07 06:24:04] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -88.808314+0.001943j
[2025-09-07 06:24:13] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -88.569852+0.000526j
[2025-09-07 06:24:22] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -88.597193+0.003034j
[2025-09-07 06:24:31] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -88.596211-0.002197j
[2025-09-07 06:24:41] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -88.759855-0.000613j
[2025-09-07 06:24:50] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -88.741797+0.001504j
[2025-09-07 06:24:59] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -88.840843+0.000744j
[2025-09-07 06:25:09] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -88.928235+0.000117j
[2025-09-07 06:25:18] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -88.833685+0.000519j
[2025-09-07 06:25:27] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -88.690696-0.000526j
[2025-09-07 06:25:36] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -88.709746+0.001445j
[2025-09-07 06:25:46] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -88.853334+0.000825j
[2025-09-07 06:25:55] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -88.841287+0.004246j
[2025-09-07 06:26:04] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -88.717257-0.001074j
[2025-09-07 06:26:13] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -88.825192-0.001176j
[2025-09-07 06:26:23] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -88.946035+0.002121j
[2025-09-07 06:26:32] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -88.767910-0.002525j
[2025-09-07 06:26:41] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -88.719928-0.001397j
[2025-09-07 06:26:50] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -88.651579-0.000874j
[2025-09-07 06:27:00] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -88.635912-0.003733j
[2025-09-07 06:27:09] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -88.745515-0.002413j
[2025-09-07 06:27:18] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -88.766946-0.002051j
[2025-09-07 06:27:27] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -88.701834+0.000518j
[2025-09-07 06:27:37] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -88.854094-0.000832j
[2025-09-07 06:27:46] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -88.812558+0.000026j
[2025-09-07 06:27:55] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -88.886879+0.001050j
[2025-09-07 06:28:04] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -88.728929+0.002627j
[2025-09-07 06:28:14] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -88.908619+0.001830j
[2025-09-07 06:28:23] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -88.775907-0.004177j
[2025-09-07 06:28:32] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -88.705656-0.002625j
[2025-09-07 06:28:42] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -88.701155-0.001838j
[2025-09-07 06:28:51] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -88.605924-0.000564j
[2025-09-07 06:29:00] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -88.673096+0.001886j
[2025-09-07 06:29:09] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -88.742912+0.000900j
[2025-09-07 06:29:19] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -88.679094-0.001380j
[2025-09-07 06:29:28] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -88.904962-0.001891j
[2025-09-07 06:29:37] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -88.795092-0.001297j
[2025-09-07 06:29:46] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -88.766260+0.002930j
[2025-09-07 06:29:56] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -88.769661-0.000455j
[2025-09-07 06:30:05] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -88.869406-0.001003j
[2025-09-07 06:30:14] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -88.751510-0.002439j
[2025-09-07 06:30:23] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -88.748648+0.003250j
[2025-09-07 06:30:33] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -88.737229+0.001315j
[2025-09-07 06:30:42] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -88.688186-0.000271j
[2025-09-07 06:30:51] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -88.682577-0.000872j
[2025-09-07 06:31:00] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -88.747807-0.003210j
[2025-09-07 06:31:10] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -88.752870+0.005368j
[2025-09-07 06:31:19] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -88.614918+0.000120j
[2025-09-07 06:31:28] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -88.821283+0.001511j
[2025-09-07 06:31:37] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -88.701146+0.003119j
[2025-09-07 06:31:47] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -88.768393-0.000856j
[2025-09-07 06:31:56] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -88.779871-0.002734j
[2025-09-07 06:32:05] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -88.760822-0.001164j
[2025-09-07 06:32:14] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -88.676444-0.000257j
[2025-09-07 06:32:15] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-07 06:32:24] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -88.759479+0.000091j
[2025-09-07 06:32:33] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -88.678874-0.001977j
[2025-09-07 06:32:42] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -88.722263+0.003539j
[2025-09-07 06:32:52] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -88.749200-0.002013j
[2025-09-07 06:33:01] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -88.704336+0.001543j
[2025-09-07 06:33:10] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -88.549774-0.002386j
[2025-09-07 06:33:19] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -88.818254-0.000896j
[2025-09-07 06:33:29] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -88.675667+0.001296j
[2025-09-07 06:33:38] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -88.618680-0.002037j
[2025-09-07 06:33:47] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -88.611614-0.000183j
[2025-09-07 06:33:56] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -88.765456+0.000375j
[2025-09-07 06:34:06] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -88.906446+0.000528j
[2025-09-07 06:34:15] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -88.817041+0.001241j
[2025-09-07 06:34:24] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -88.908161+0.003772j
[2025-09-07 06:34:33] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -88.815981+0.000124j
[2025-09-07 06:34:43] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -88.753938+0.003321j
[2025-09-07 06:34:52] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -88.708852-0.000311j
[2025-09-07 06:35:01] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -88.669542-0.001708j
[2025-09-07 06:35:10] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -88.741398-0.000380j
[2025-09-07 06:35:20] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -88.717683+0.000453j
[2025-09-07 06:35:29] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -88.800018-0.000207j
[2025-09-07 06:35:38] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -88.885148-0.002973j
[2025-09-07 06:35:48] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -88.859271-0.000163j
[2025-09-07 06:35:57] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -88.809766-0.002058j
[2025-09-07 06:36:06] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -88.707638+0.000426j
[2025-09-07 06:36:15] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -88.729399-0.001491j
[2025-09-07 06:36:25] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -88.859374-0.003160j
[2025-09-07 06:36:34] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -88.625296+0.004872j
[2025-09-07 06:36:43] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -88.644472-0.000470j
[2025-09-07 06:36:52] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -88.818678+0.000023j
[2025-09-07 06:37:02] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -88.677115+0.000148j
[2025-09-07 06:37:11] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -88.798572+0.003996j
[2025-09-07 06:37:20] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -88.755432+0.000820j
[2025-09-07 06:37:29] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -88.778523+0.001916j
[2025-09-07 06:37:39] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -88.808833+0.001639j
[2025-09-07 06:37:48] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -88.727645-0.002997j
[2025-09-07 06:37:57] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -88.729341-0.000660j
[2025-09-07 06:38:07] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -88.679505+0.003895j
[2025-09-07 06:38:16] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -88.796796+0.000299j
[2025-09-07 06:38:25] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -88.682963+0.001678j
[2025-09-07 06:38:34] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -88.754244-0.002870j
[2025-09-07 06:38:44] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -88.697379-0.002294j
[2025-09-07 06:38:53] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -88.736290-0.003204j
[2025-09-07 06:39:02] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -88.804144+0.000279j
[2025-09-07 06:39:11] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -88.979192+0.003439j
[2025-09-07 06:39:21] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -88.975634-0.003656j
[2025-09-07 06:39:30] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -88.855008+0.001117j
[2025-09-07 06:39:39] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -88.965327-0.002294j
[2025-09-07 06:39:48] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -88.858110+0.000290j
[2025-09-07 06:39:58] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -88.909289-0.004403j
[2025-09-07 06:40:07] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -88.626278+0.003451j
[2025-09-07 06:40:16] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -88.714148+0.003898j
[2025-09-07 06:40:25] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -88.560460+0.002220j
[2025-09-07 06:40:35] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -88.812358+0.000781j
[2025-09-07 06:40:44] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -88.857050-0.001099j
[2025-09-07 06:40:53] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -88.934248-0.001681j
[2025-09-07 06:41:03] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -88.814146+0.003293j
[2025-09-07 06:41:12] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -88.833197+0.002104j
[2025-09-07 06:41:21] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -88.739721-0.000230j
[2025-09-07 06:41:30] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -88.884010-0.003728j
[2025-09-07 06:41:40] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -88.726872+0.001580j
[2025-09-07 06:41:49] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -88.898594+0.002675j
[2025-09-07 06:41:58] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -88.778906+0.002422j
[2025-09-07 06:42:07] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -88.703187+0.001916j
[2025-09-07 06:42:17] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -88.701353-0.001153j
[2025-09-07 06:42:26] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -88.561005+0.003852j
[2025-09-07 06:42:35] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -88.605872-0.002913j
[2025-09-07 06:42:44] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -88.670389+0.000247j
[2025-09-07 06:42:54] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -88.617710-0.001801j
[2025-09-07 06:43:03] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -88.786412-0.002729j
[2025-09-07 06:43:12] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -88.606257-0.000706j
[2025-09-07 06:43:21] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -88.693354-0.000624j
[2025-09-07 06:43:31] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -88.523000-0.001974j
[2025-09-07 06:43:40] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -88.621879+0.002745j
[2025-09-07 06:43:49] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -88.709018-0.001217j
[2025-09-07 06:43:59] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -88.858945-0.000372j
[2025-09-07 06:44:08] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -88.722728+0.000045j
[2025-09-07 06:44:17] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -88.660318-0.001749j
[2025-09-07 06:44:26] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -88.843750-0.003044j
[2025-09-07 06:44:36] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -88.833442+0.001283j
[2025-09-07 06:44:45] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -88.791014-0.001737j
[2025-09-07 06:44:54] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -88.782504-0.001531j
[2025-09-07 06:45:03] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -88.934257+0.001188j
[2025-09-07 06:45:13] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -88.859940-0.001530j
[2025-09-07 06:45:22] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -88.883315-0.000030j
[2025-09-07 06:45:31] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -88.934472-0.001281j
[2025-09-07 06:45:41] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -88.867648+0.000243j
[2025-09-07 06:45:50] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -88.810011+0.000098j
[2025-09-07 06:45:59] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -88.613005+0.001411j
[2025-09-07 06:46:08] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -88.780324+0.000165j
[2025-09-07 06:46:18] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -88.855030+0.003059j
[2025-09-07 06:46:27] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -88.817971-0.002028j
[2025-09-07 06:46:36] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -88.850677+0.004317j
[2025-09-07 06:46:45] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -88.813162+0.003103j
[2025-09-07 06:46:55] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -88.814765+0.001138j
[2025-09-07 06:47:04] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -88.739227-0.000209j
[2025-09-07 06:47:13] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -88.557805-0.001422j
[2025-09-07 06:47:22] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -88.564658-0.003359j
[2025-09-07 06:47:32] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -88.754357-0.000643j
[2025-09-07 06:47:41] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -88.575747+0.001047j
[2025-09-07 06:47:50] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -88.753996+0.001078j
[2025-09-07 06:47:59] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -88.714443+0.000923j
[2025-09-07 06:48:09] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -88.717077+0.000886j
[2025-09-07 06:48:18] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -88.761240-0.000239j
[2025-09-07 06:48:27] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -88.717726-0.000123j
[2025-09-07 06:48:27] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-07 06:48:36] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -88.808027+0.001227j
[2025-09-07 06:48:46] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -88.828386-0.000984j
[2025-09-07 06:48:55] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -88.749060+0.000166j
[2025-09-07 06:49:04] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -88.712792+0.001076j
[2025-09-07 06:49:14] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -88.756056+0.000553j
[2025-09-07 06:49:23] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -88.832514-0.002537j
[2025-09-07 06:49:32] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -88.930432+0.004395j
[2025-09-07 06:49:41] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -88.735647+0.002141j
[2025-09-07 06:49:51] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -88.735581-0.001536j
[2025-09-07 06:50:00] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -88.827349+0.001044j
[2025-09-07 06:50:09] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -88.921218+0.003094j
[2025-09-07 06:50:18] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -88.687782-0.000160j
[2025-09-07 06:50:28] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -88.591853-0.000756j
[2025-09-07 06:50:37] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -88.546312-0.002231j
[2025-09-07 06:50:46] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -88.471976+0.000438j
[2025-09-07 06:50:55] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -88.542933+0.001307j
[2025-09-07 06:51:05] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -88.606235-0.000871j
[2025-09-07 06:51:14] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -88.750922+0.000045j
[2025-09-07 06:51:23] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -88.701523+0.003073j
[2025-09-07 06:51:33] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -88.961751-0.000918j
[2025-09-07 06:51:42] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -88.749866-0.001909j
[2025-09-07 06:51:51] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -88.817515-0.000877j
[2025-09-07 06:52:00] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -88.812078+0.000054j
[2025-09-07 06:52:10] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -88.831814+0.001491j
[2025-09-07 06:52:19] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -88.996238-0.001468j
[2025-09-07 06:52:28] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -88.982303-0.001437j
[2025-09-07 06:52:37] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -88.961938+0.003124j
[2025-09-07 06:52:47] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -89.039095+0.002323j
[2025-09-07 06:52:56] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -89.005369-0.000261j
[2025-09-07 06:53:05] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -89.047796+0.003259j
[2025-09-07 06:53:14] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -88.799393-0.002215j
[2025-09-07 06:53:24] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -88.772998-0.001327j
[2025-09-07 06:53:33] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -88.683532+0.000930j
[2025-09-07 06:53:42] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -88.587409-0.002038j
[2025-09-07 06:53:51] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -88.722326+0.000517j
[2025-09-07 06:54:01] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -88.713287-0.000462j
[2025-09-07 06:54:10] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -88.731283-0.001315j
[2025-09-07 06:54:19] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -88.792698-0.002835j
[2025-09-07 06:54:28] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -88.808229+0.000874j
[2025-09-07 06:54:38] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -88.573957+0.000481j
[2025-09-07 06:54:47] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -88.619707-0.000142j
[2025-09-07 06:54:56] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -88.740954-0.000704j
[2025-09-07 06:55:06] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -88.832164-0.000323j
[2025-09-07 06:55:15] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -88.884864-0.001148j
[2025-09-07 06:55:24] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -88.720135-0.000717j
[2025-09-07 06:55:33] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -88.621771-0.000837j
[2025-09-07 06:55:43] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -88.533330-0.000587j
[2025-09-07 06:55:52] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -88.675952-0.002854j
[2025-09-07 06:56:01] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -88.722240-0.003684j
[2025-09-07 06:56:10] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -88.801967+0.007863j
[2025-09-07 06:56:20] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -88.673321-0.001490j
[2025-09-07 06:56:29] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -88.686836-0.001964j
[2025-09-07 06:56:38] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -88.687010+0.000891j
[2025-09-07 06:56:47] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -88.707627+0.002440j
[2025-09-07 06:56:57] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -88.575898+0.001235j
[2025-09-07 06:57:06] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -88.640736+0.002481j
[2025-09-07 06:57:15] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -88.647891-0.000242j
[2025-09-07 06:57:24] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -88.687524+0.004781j
[2025-09-07 06:57:34] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -88.732541-0.002525j
[2025-09-07 06:57:43] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -88.902408-0.001950j
[2025-09-07 06:57:52] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -88.755768+0.002754j
[2025-09-07 06:58:02] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -88.697826+0.004435j
[2025-09-07 06:58:11] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -88.879309+0.002719j
[2025-09-07 06:58:20] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -89.047392+0.000926j
[2025-09-07 06:58:29] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -88.803709-0.000023j
[2025-09-07 06:58:39] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -88.746416-0.002303j
[2025-09-07 06:58:48] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -88.791725-0.003757j
[2025-09-07 06:58:57] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -88.803229+0.003520j
[2025-09-07 06:59:06] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -88.892391-0.000692j
[2025-09-07 06:59:16] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -88.821077-0.000980j
[2025-09-07 06:59:25] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -88.874667-0.000874j
[2025-09-07 06:59:34] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -88.645818+0.000420j
[2025-09-07 06:59:43] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -88.646174-0.000440j
[2025-09-07 06:59:53] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -88.733228+0.001603j
[2025-09-07 07:00:02] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -88.760969-0.001729j
[2025-09-07 07:00:11] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -88.781873-0.002737j
[2025-09-07 07:00:20] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -88.882013-0.000206j
[2025-09-07 07:00:30] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -88.819475-0.002929j
[2025-09-07 07:00:39] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -88.840322-0.002414j
[2025-09-07 07:00:48] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -88.821960+0.001861j
[2025-09-07 07:00:57] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -88.742961+0.002201j
[2025-09-07 07:01:07] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -88.752994-0.002058j
[2025-09-07 07:01:16] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -88.818808-0.003749j
[2025-09-07 07:01:25] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -88.818073-0.001392j
[2025-09-07 07:01:34] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -88.885923+0.004147j
[2025-09-07 07:01:44] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -88.678915+0.000316j
[2025-09-07 07:01:53] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -88.852053+0.000099j
[2025-09-07 07:02:02] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -88.984608+0.000696j
[2025-09-07 07:02:12] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -89.067495+0.002798j
[2025-09-07 07:02:21] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -89.089049+0.001941j
[2025-09-07 07:02:30] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -88.952889+0.002269j
[2025-09-07 07:02:39] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -88.740580-0.002169j
[2025-09-07 07:02:49] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -88.816872-0.000785j
[2025-09-07 07:02:58] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -88.798665+0.000851j
[2025-09-07 07:03:07] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -88.832319+0.000507j
[2025-09-07 07:03:16] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -88.894810+0.005349j
[2025-09-07 07:03:26] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -88.976994-0.004631j
[2025-09-07 07:03:35] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -88.850200+0.004562j
[2025-09-07 07:03:44] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -88.819350-0.003000j
[2025-09-07 07:03:53] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -88.828154+0.003032j
[2025-09-07 07:04:03] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -88.785231+0.000670j
[2025-09-07 07:04:12] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -88.833731+0.001443j
[2025-09-07 07:04:21] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -88.672810+0.002555j
[2025-09-07 07:04:30] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -88.727666-0.000187j
[2025-09-07 07:04:40] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -88.771400+0.003781j
[2025-09-07 07:04:40] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-07 07:04:49] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -88.667939+0.001716j
[2025-09-07 07:04:58] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -88.786246-0.004800j
[2025-09-07 07:05:08] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -88.747191+0.000723j
[2025-09-07 07:05:17] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -88.708106-0.003043j
[2025-09-07 07:05:26] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -88.636104+0.000277j
[2025-09-07 07:05:35] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -88.799873+0.006158j
[2025-09-07 07:05:45] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -88.792600+0.001803j
[2025-09-07 07:05:54] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -88.946184+0.000028j
[2025-09-07 07:06:03] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -88.871198-0.000100j
[2025-09-07 07:06:12] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -88.846099+0.001215j
[2025-09-07 07:06:22] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -88.779391-0.000916j
[2025-09-07 07:06:31] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -88.837482+0.000659j
[2025-09-07 07:06:40] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -88.936517-0.000052j
[2025-09-07 07:06:50] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -88.921865+0.001032j
[2025-09-07 07:06:59] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -89.034155-0.002093j
[2025-09-07 07:07:08] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -89.080498+0.002538j
[2025-09-07 07:07:17] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -89.007725-0.002454j
[2025-09-07 07:07:27] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -89.023636+0.002218j
[2025-09-07 07:07:36] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -89.025107-0.006504j
[2025-09-07 07:07:45] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -88.972696-0.000248j
[2025-09-07 07:07:54] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -88.933881+0.000861j
[2025-09-07 07:08:04] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -88.886706+0.000609j
[2025-09-07 07:08:13] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -88.913557-0.001303j
[2025-09-07 07:08:22] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -88.910210+0.002638j
[2025-09-07 07:08:31] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -88.857744+0.003357j
[2025-09-07 07:08:41] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -88.819344+0.000325j
[2025-09-07 07:08:50] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -88.825064+0.002962j
[2025-09-07 07:08:59] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -88.821268+0.001076j
[2025-09-07 07:09:08] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -88.765846+0.001200j
[2025-09-07 07:09:18] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -88.837131-0.000038j
[2025-09-07 07:09:27] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -88.744282+0.001451j
[2025-09-07 07:09:36] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -88.882124-0.000764j
[2025-09-07 07:09:46] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -88.942522+0.002207j
[2025-09-07 07:09:55] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -88.913304-0.001967j
[2025-09-07 07:10:04] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -88.732510+0.000293j
[2025-09-07 07:10:13] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -88.737402-0.004109j
[2025-09-07 07:10:23] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -88.803830-0.001066j
[2025-09-07 07:10:32] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -88.664919-0.004685j
[2025-09-07 07:10:41] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -88.590797-0.002372j
[2025-09-07 07:10:51] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -88.624185-0.002126j
[2025-09-07 07:11:00] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -88.741286-0.001111j
[2025-09-07 07:11:09] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -88.746516+0.002826j
[2025-09-07 07:11:18] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -88.701677-0.004199j
[2025-09-07 07:11:28] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -88.759409-0.000761j
[2025-09-07 07:11:37] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -88.797099+0.000210j
[2025-09-07 07:11:46] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -88.863185+0.002640j
[2025-09-07 07:11:55] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -88.695871+0.002829j
[2025-09-07 07:12:05] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -88.886176+0.002098j
[2025-09-07 07:12:14] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -88.801156+0.002355j
[2025-09-07 07:12:23] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -88.828457-0.002529j
[2025-09-07 07:12:32] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -88.735279-0.000686j
[2025-09-07 07:12:42] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -88.726483+0.000581j
[2025-09-07 07:12:51] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -88.631049-0.001591j
[2025-09-07 07:13:00] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -88.584340+0.001172j
[2025-09-07 07:13:10] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -88.567467+0.002347j
[2025-09-07 07:13:19] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -88.626697+0.002881j
[2025-09-07 07:13:28] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -88.703719-0.000926j
[2025-09-07 07:13:37] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -88.803596+0.001596j
[2025-09-07 07:13:47] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -88.694133+0.001618j
[2025-09-07 07:13:56] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -88.801861+0.000976j
[2025-09-07 07:14:05] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -88.684432-0.002717j
[2025-09-07 07:14:14] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -88.749056+0.002805j
[2025-09-07 07:14:24] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -88.724916+0.000972j
[2025-09-07 07:14:33] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -88.613419-0.000052j
[2025-09-07 07:14:42] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -88.722484+0.000875j
[2025-09-07 07:14:51] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -88.703336-0.004046j
[2025-09-07 07:15:01] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -88.765904-0.000783j
[2025-09-07 07:15:10] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -88.674154-0.000749j
[2025-09-07 07:15:19] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -88.791612-0.003899j
[2025-09-07 07:15:29] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -88.671069-0.003984j
[2025-09-07 07:15:38] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -88.564316+0.000840j
[2025-09-07 07:15:47] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -88.626330+0.000711j
[2025-09-07 07:15:56] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -88.560696-0.000628j
[2025-09-07 07:16:06] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -88.539613+0.001385j
[2025-09-07 07:16:15] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -88.699984+0.003124j
[2025-09-07 07:16:24] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -88.667398+0.000773j
[2025-09-07 07:16:33] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -88.689604-0.000420j
[2025-09-07 07:16:43] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -88.657709+0.001704j
[2025-09-07 07:16:52] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -88.759355-0.001853j
[2025-09-07 07:17:01] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -88.908243+0.000835j
[2025-09-07 07:17:10] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -88.745592-0.002309j
[2025-09-07 07:17:20] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -88.779575+0.003225j
[2025-09-07 07:17:29] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -88.764738-0.000903j
[2025-09-07 07:17:38] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -88.725953-0.002726j
[2025-09-07 07:17:47] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -88.738974-0.005838j
[2025-09-07 07:17:57] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -88.669327+0.000081j
[2025-09-07 07:18:06] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -88.662002-0.002011j
[2025-09-07 07:18:15] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -88.700849+0.000739j
[2025-09-07 07:18:25] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -88.770031+0.004151j
[2025-09-07 07:18:34] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -88.736099+0.001799j
[2025-09-07 07:18:43] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -88.843080+0.001206j
[2025-09-07 07:18:52] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -88.776130-0.003960j
[2025-09-07 07:19:02] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -88.734580+0.000543j
[2025-09-07 07:19:11] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -88.799045+0.002646j
[2025-09-07 07:19:20] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -88.653909+0.000003j
[2025-09-07 07:19:29] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -88.780033+0.000621j
[2025-09-07 07:19:39] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -88.559113+0.003240j
[2025-09-07 07:19:48] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -88.547102+0.000903j
[2025-09-07 07:19:57] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -88.548971-0.001231j
[2025-09-07 07:20:06] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -88.703263+0.001701j
[2025-09-07 07:20:16] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -88.719613-0.000788j
[2025-09-07 07:20:25] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -88.746202+0.002720j
[2025-09-07 07:20:34] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -88.706286+0.002199j
[2025-09-07 07:20:44] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -88.879692+0.001251j
[2025-09-07 07:20:53] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -88.891842-0.000289j
[2025-09-07 07:20:53] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-07 07:21:02] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -88.831065+0.003978j
[2025-09-07 07:21:11] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -88.747609-0.000435j
[2025-09-07 07:21:21] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -88.779445+0.002030j
[2025-09-07 07:21:30] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -88.854401-0.001079j
[2025-09-07 07:21:39] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -88.730218-0.001107j
[2025-09-07 07:21:49] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -88.877942+0.000562j
[2025-09-07 07:21:58] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -88.819075-0.001779j
[2025-09-07 07:22:07] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -88.733937+0.002334j
[2025-09-07 07:22:16] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -88.756027-0.002961j
[2025-09-07 07:22:26] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -88.860560-0.001614j
[2025-09-07 07:22:35] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -88.625650-0.000358j
[2025-09-07 07:22:44] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -88.779756-0.002115j
[2025-09-07 07:22:53] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -88.549020+0.000329j
[2025-09-07 07:23:03] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -88.775085+0.002805j
[2025-09-07 07:23:12] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -88.788306+0.000181j
[2025-09-07 07:23:21] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -88.669415-0.002926j
[2025-09-07 07:23:30] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -88.830092+0.002547j
[2025-09-07 07:23:40] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -88.854114-0.003525j
[2025-09-07 07:23:49] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -88.947094+0.000073j
[2025-09-07 07:23:58] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -88.985729+0.002104j
[2025-09-07 07:24:08] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -88.863906-0.004076j
[2025-09-07 07:24:17] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -88.851735+0.001487j
[2025-09-07 07:24:26] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -88.960148+0.001783j
[2025-09-07 07:24:35] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -89.061651+0.001539j
[2025-09-07 07:24:45] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -88.875675-0.003435j
[2025-09-07 07:24:54] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -88.909521-0.000071j
[2025-09-07 07:25:03] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -88.842151-0.000666j
[2025-09-07 07:25:12] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -88.873777+0.001416j
[2025-09-07 07:25:22] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -88.752635+0.001544j
[2025-09-07 07:25:31] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -88.725108-0.002163j
[2025-09-07 07:25:40] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -88.841719-0.003817j
[2025-09-07 07:25:49] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -88.776884+0.002335j
[2025-09-07 07:25:59] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -88.751751+0.000227j
[2025-09-07 07:26:08] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -88.814379-0.000427j
[2025-09-07 07:26:17] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -88.837785+0.001748j
[2025-09-07 07:26:26] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -88.745394+0.000675j
[2025-09-07 07:26:36] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -88.905587-0.000079j
[2025-09-07 07:26:45] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -88.758074-0.000330j
[2025-09-07 07:26:54] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -88.746359+0.001826j
[2025-09-07 07:27:03] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -88.719747-0.001727j
[2025-09-07 07:27:13] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -88.843437-0.001282j
[2025-09-07 07:27:22] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -88.772972+0.001745j
[2025-09-07 07:27:31] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -88.892240-0.002006j
[2025-09-07 07:27:41] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -88.727133+0.000957j
[2025-09-07 07:27:50] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -88.826714+0.001510j
[2025-09-07 07:27:59] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -88.755519-0.002155j
[2025-09-07 07:28:08] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -88.732527+0.003394j
[2025-09-07 07:28:18] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -88.705538+0.003714j
[2025-09-07 07:28:27] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -88.811324+0.003664j
[2025-09-07 07:28:36] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -88.757223-0.002192j
[2025-09-07 07:28:45] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -88.616291+0.001062j
[2025-09-07 07:28:55] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -88.529590-0.002877j
[2025-09-07 07:29:04] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -88.675409+0.000029j
[2025-09-07 07:29:13] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -88.563995-0.001552j
[2025-09-07 07:29:22] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -88.746593+0.002608j
[2025-09-07 07:29:32] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -88.648327-0.001058j
[2025-09-07 07:29:41] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -88.820461+0.000032j
[2025-09-07 07:29:50] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -88.789890-0.000896j
[2025-09-07 07:29:59] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -88.810329+0.002798j
[2025-09-07 07:30:09] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -88.780490-0.003536j
[2025-09-07 07:30:18] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -88.914456+0.000588j
[2025-09-07 07:30:27] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -88.940948+0.001724j
[2025-09-07 07:30:36] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -88.747268-0.000653j
[2025-09-07 07:30:46] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -88.688064-0.004387j
[2025-09-07 07:30:55] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -88.630738+0.001855j
[2025-09-07 07:31:04] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -88.683712-0.002001j
[2025-09-07 07:31:14] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -88.575193+0.001429j
[2025-09-07 07:31:23] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -88.551050+0.001585j
[2025-09-07 07:31:32] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -88.622960+0.003785j
[2025-09-07 07:31:41] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -88.592461-0.003644j
[2025-09-07 07:31:51] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -88.784415+0.002955j
[2025-09-07 07:32:00] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -88.712152+0.003945j
[2025-09-07 07:32:09] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -88.828509+0.001655j
[2025-09-07 07:32:18] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -88.804757+0.001570j
[2025-09-07 07:32:28] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -88.705549+0.000147j
[2025-09-07 07:32:37] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -88.706790+0.002989j
[2025-09-07 07:32:46] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -88.895967+0.003414j
[2025-09-07 07:32:55] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -88.860976+0.003095j
[2025-09-07 07:33:05] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -88.890653-0.001853j
[2025-09-07 07:33:14] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -88.849174-0.000565j
[2025-09-07 07:33:23] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -88.778304-0.001120j
[2025-09-07 07:33:32] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -88.672782-0.000577j
[2025-09-07 07:33:42] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -88.906212+0.001412j
[2025-09-07 07:33:51] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -88.693822-0.001767j
[2025-09-07 07:34:00] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -88.717569-0.002168j
[2025-09-07 07:34:10] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -88.707138-0.001020j
[2025-09-07 07:34:19] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -88.795023-0.002869j
[2025-09-07 07:34:28] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -88.754205-0.000551j
[2025-09-07 07:34:37] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -88.724588-0.001240j
[2025-09-07 07:34:47] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -88.813962+0.001233j
[2025-09-07 07:34:56] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -88.830050+0.001807j
[2025-09-07 07:35:05] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -88.724350+0.002346j
[2025-09-07 07:35:14] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -88.866066+0.001843j
[2025-09-07 07:35:24] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -88.653693+0.002066j
[2025-09-07 07:35:33] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -88.685729+0.004153j
[2025-09-07 07:35:42] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -88.641318-0.003666j
[2025-09-07 07:35:52] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -88.760654-0.002338j
[2025-09-07 07:36:01] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -88.754695+0.000544j
[2025-09-07 07:36:10] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -88.761629-0.001118j
[2025-09-07 07:36:19] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -88.549891+0.002714j
[2025-09-07 07:36:29] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -88.583246+0.000035j
[2025-09-07 07:36:38] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -88.867759+0.001649j
[2025-09-07 07:36:47] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -88.869196+0.000254j
[2025-09-07 07:36:56] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -88.823557-0.001684j
[2025-09-07 07:37:06] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -88.732880+0.001833j
[2025-09-07 07:37:06] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-07 07:37:06] ✅ Training completed | Restarts: 2
[2025-09-07 07:37:06] ============================================================
[2025-09-07 07:37:06] Training completed | Runtime: 9777.3s
[2025-09-07 07:37:09] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-07 07:37:09] ============================================================
