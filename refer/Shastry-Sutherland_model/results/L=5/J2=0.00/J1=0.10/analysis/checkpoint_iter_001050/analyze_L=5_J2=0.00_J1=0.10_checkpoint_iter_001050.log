[2025-09-08 02:31:31] 使用checkpoint文件: results/L=5/J2=0.00/J1=0.10/training/checkpoints/checkpoint_iter_001050.pkl
[2025-09-08 02:31:42] ✓ 从checkpoint加载参数: 1050
[2025-09-08 02:31:42]   - 能量: -88.732880+0.001833j ± 0.109246
[2025-09-08 02:31:42] ================================================================================
[2025-09-08 02:31:42] 加载量子态: L=5, J2=0.00, J1=0.10, checkpoint=checkpoint_iter_001050
[2025-09-08 02:31:42] 使用采样数目: 1048576
[2025-09-08 02:31:42] 设置样本数为: 1048576
[2025-09-08 02:31:42] 开始生成共享样本集...
[2025-09-08 02:34:39] 样本生成完成,耗时: 177.178 秒
[2025-09-08 02:34:39] ================================================================================
[2025-09-08 02:34:39] 开始计算自旋结构因子...
[2025-09-08 02:34:39] 初始化操作符缓存...
[2025-09-08 02:34:39] 预构建所有自旋相关操作符...
[2025-09-08 02:34:39] 开始计算自旋相关函数...
[2025-09-08 02:34:49] 自旋算符进度: 1/100, 当前算符: S_0 · S_0, 耗时: 9.406s
[2025-09-08 02:35:00] 自旋算符进度: 2/100, 当前算符: S_0 · S_1, 耗时: 10.905s
[2025-09-08 02:35:06] 自旋算符进度: 3/100, 当前算符: S_0 · S_2, 耗时: 6.147s
[2025-09-08 02:35:12] 自旋算符进度: 4/100, 当前算符: S_0 · S_3, 耗时: 6.154s
[2025-09-08 02:35:18] 自旋算符进度: 5/100, 当前算符: S_0 · S_4, 耗时: 6.181s
[2025-09-08 02:35:24] 自旋算符进度: 6/100, 当前算符: S_0 · S_5, 耗时: 6.153s
[2025-09-08 02:35:31] 自旋算符进度: 7/100, 当前算符: S_0 · S_6, 耗时: 6.160s
[2025-09-08 02:35:37] 自旋算符进度: 8/100, 当前算符: S_0 · S_7, 耗时: 6.209s
[2025-09-08 02:35:43] 自旋算符进度: 9/100, 当前算符: S_0 · S_8, 耗时: 6.210s
[2025-09-08 02:35:49] 自旋算符进度: 10/100, 当前算符: S_0 · S_9, 耗时: 6.163s
[2025-09-08 02:35:55] 自旋算符进度: 11/100, 当前算符: S_0 · S_10, 耗时: 6.178s
[2025-09-08 02:36:02] 自旋算符进度: 12/100, 当前算符: S_0 · S_11, 耗时: 6.178s
[2025-09-08 02:36:08] 自旋算符进度: 13/100, 当前算符: S_0 · S_12, 耗时: 6.174s
[2025-09-08 02:36:14] 自旋算符进度: 14/100, 当前算符: S_0 · S_13, 耗时: 6.150s
[2025-09-08 02:36:20] 自旋算符进度: 15/100, 当前算符: S_0 · S_14, 耗时: 6.150s
[2025-09-08 02:36:26] 自旋算符进度: 16/100, 当前算符: S_0 · S_15, 耗时: 6.156s
[2025-09-08 02:36:32] 自旋算符进度: 17/100, 当前算符: S_0 · S_16, 耗时: 6.204s
[2025-09-08 02:36:39] 自旋算符进度: 18/100, 当前算符: S_0 · S_17, 耗时: 6.148s
[2025-09-08 02:36:45] 自旋算符进度: 19/100, 当前算符: S_0 · S_18, 耗时: 6.175s
[2025-09-08 02:36:51] 自旋算符进度: 20/100, 当前算符: S_0 · S_19, 耗时: 6.205s
[2025-09-08 02:36:57] 自旋算符进度: 21/100, 当前算符: S_0 · S_20, 耗时: 6.158s
[2025-09-08 02:37:03] 自旋算符进度: 22/100, 当前算符: S_0 · S_21, 耗时: 6.176s
[2025-09-08 02:37:10] 自旋算符进度: 23/100, 当前算符: S_0 · S_22, 耗时: 6.203s
[2025-09-08 02:37:16] 自旋算符进度: 24/100, 当前算符: S_0 · S_23, 耗时: 6.203s
[2025-09-08 02:37:22] 自旋算符进度: 25/100, 当前算符: S_0 · S_24, 耗时: 6.157s
[2025-09-08 02:37:28] 自旋算符进度: 26/100, 当前算符: S_0 · S_25, 耗时: 6.203s
[2025-09-08 02:37:34] 自旋算符进度: 27/100, 当前算符: S_0 · S_26, 耗时: 6.156s
[2025-09-08 02:37:40] 自旋算符进度: 28/100, 当前算符: S_0 · S_27, 耗时: 6.205s
[2025-09-08 02:37:47] 自旋算符进度: 29/100, 当前算符: S_0 · S_28, 耗时: 6.156s
[2025-09-08 02:37:53] 自旋算符进度: 30/100, 当前算符: S_0 · S_29, 耗时: 6.204s
[2025-09-08 02:37:59] 自旋算符进度: 31/100, 当前算符: S_0 · S_30, 耗时: 6.158s
[2025-09-08 02:38:05] 自旋算符进度: 32/100, 当前算符: S_0 · S_31, 耗时: 6.154s
[2025-09-08 02:38:11] 自旋算符进度: 33/100, 当前算符: S_0 · S_32, 耗时: 6.149s
[2025-09-08 02:38:17] 自旋算符进度: 34/100, 当前算符: S_0 · S_33, 耗时: 6.206s
[2025-09-08 02:38:24] 自旋算符进度: 35/100, 当前算符: S_0 · S_34, 耗时: 6.158s
[2025-09-08 02:38:30] 自旋算符进度: 36/100, 当前算符: S_0 · S_35, 耗时: 6.151s
[2025-09-08 02:38:36] 自旋算符进度: 37/100, 当前算符: S_0 · S_36, 耗时: 6.175s
[2025-09-08 02:38:42] 自旋算符进度: 38/100, 当前算符: S_0 · S_37, 耗时: 6.156s
[2025-09-08 02:38:48] 自旋算符进度: 39/100, 当前算符: S_0 · S_38, 耗时: 6.175s
[2025-09-08 02:38:55] 自旋算符进度: 40/100, 当前算符: S_0 · S_39, 耗时: 6.175s
[2025-09-08 02:39:01] 自旋算符进度: 41/100, 当前算符: S_0 · S_40, 耗时: 6.155s
[2025-09-08 02:39:07] 自旋算符进度: 42/100, 当前算符: S_0 · S_41, 耗时: 6.176s
[2025-09-08 02:39:13] 自旋算符进度: 43/100, 当前算符: S_0 · S_42, 耗时: 6.147s
[2025-09-08 02:39:19] 自旋算符进度: 44/100, 当前算符: S_0 · S_43, 耗时: 6.204s
[2025-09-08 02:39:25] 自旋算符进度: 45/100, 当前算符: S_0 · S_44, 耗时: 6.157s
[2025-09-08 02:39:32] 自旋算符进度: 46/100, 当前算符: S_0 · S_45, 耗时: 6.176s
[2025-09-08 02:39:38] 自旋算符进度: 47/100, 当前算符: S_0 · S_46, 耗时: 6.146s
[2025-09-08 02:39:44] 自旋算符进度: 48/100, 当前算符: S_0 · S_47, 耗时: 6.148s
[2025-09-08 02:39:50] 自旋算符进度: 49/100, 当前算符: S_0 · S_48, 耗时: 6.155s
[2025-09-08 02:39:56] 自旋算符进度: 50/100, 当前算符: S_0 · S_49, 耗时: 6.177s
[2025-09-08 02:40:02] 自旋算符进度: 51/100, 当前算符: S_0 · S_50, 耗时: 6.149s
[2025-09-08 02:40:09] 自旋算符进度: 52/100, 当前算符: S_0 · S_51, 耗时: 6.155s
[2025-09-08 02:40:15] 自旋算符进度: 53/100, 当前算符: S_0 · S_52, 耗时: 6.177s
[2025-09-08 02:40:21] 自旋算符进度: 54/100, 当前算符: S_0 · S_53, 耗时: 6.149s
[2025-09-08 02:40:27] 自旋算符进度: 55/100, 当前算符: S_0 · S_54, 耗时: 6.157s
[2025-09-08 02:40:33] 自旋算符进度: 56/100, 当前算符: S_0 · S_55, 耗时: 6.151s
[2025-09-08 02:40:39] 自旋算符进度: 57/100, 当前算符: S_0 · S_56, 耗时: 6.176s
[2025-09-08 02:40:45] 自旋算符进度: 58/100, 当前算符: S_0 · S_57, 耗时: 6.157s
[2025-09-08 02:40:52] 自旋算符进度: 59/100, 当前算符: S_0 · S_58, 耗时: 6.205s
[2025-09-08 02:40:58] 自旋算符进度: 60/100, 当前算符: S_0 · S_59, 耗时: 6.152s
[2025-09-08 02:41:04] 自旋算符进度: 61/100, 当前算符: S_0 · S_60, 耗时: 6.177s
[2025-09-08 02:41:10] 自旋算符进度: 62/100, 当前算符: S_0 · S_61, 耗时: 6.150s
[2025-09-08 02:41:16] 自旋算符进度: 63/100, 当前算符: S_0 · S_62, 耗时: 6.204s
[2025-09-08 02:41:23] 自旋算符进度: 64/100, 当前算符: S_0 · S_63, 耗时: 6.149s
[2025-09-08 02:41:29] 自旋算符进度: 65/100, 当前算符: S_0 · S_64, 耗时: 6.157s
[2025-09-08 02:41:35] 自旋算符进度: 66/100, 当前算符: S_0 · S_65, 耗时: 6.150s
[2025-09-08 02:41:41] 自旋算符进度: 67/100, 当前算符: S_0 · S_66, 耗时: 6.176s
[2025-09-08 02:41:47] 自旋算符进度: 68/100, 当前算符: S_0 · S_67, 耗时: 6.149s
[2025-09-08 02:41:53] 自旋算符进度: 69/100, 当前算符: S_0 · S_68, 耗时: 6.148s
[2025-09-08 02:42:00] 自旋算符进度: 70/100, 当前算符: S_0 · S_69, 耗时: 6.203s
[2025-09-08 02:42:06] 自旋算符进度: 71/100, 当前算符: S_0 · S_70, 耗时: 6.151s
[2025-09-08 02:42:12] 自旋算符进度: 72/100, 当前算符: S_0 · S_71, 耗时: 6.204s
[2025-09-08 02:42:18] 自旋算符进度: 73/100, 当前算符: S_0 · S_72, 耗时: 6.206s
[2025-09-08 02:42:24] 自旋算符进度: 74/100, 当前算符: S_0 · S_73, 耗时: 6.150s
[2025-09-08 02:42:30] 自旋算符进度: 75/100, 当前算符: S_0 · S_74, 耗时: 6.206s
[2025-09-08 02:42:37] 自旋算符进度: 76/100, 当前算符: S_0 · S_75, 耗时: 6.154s
[2025-09-08 02:42:43] 自旋算符进度: 77/100, 当前算符: S_0 · S_76, 耗时: 6.176s
[2025-09-08 02:42:49] 自旋算符进度: 78/100, 当前算符: S_0 · S_77, 耗时: 6.147s
[2025-09-08 02:42:55] 自旋算符进度: 79/100, 当前算符: S_0 · S_78, 耗时: 6.207s
[2025-09-08 02:43:01] 自旋算符进度: 80/100, 当前算符: S_0 · S_79, 耗时: 6.148s
[2025-09-08 02:43:07] 自旋算符进度: 81/100, 当前算符: S_0 · S_80, 耗时: 6.149s
[2025-09-08 02:43:14] 自旋算符进度: 82/100, 当前算符: S_0 · S_81, 耗时: 6.149s
[2025-09-08 02:43:20] 自旋算符进度: 83/100, 当前算符: S_0 · S_82, 耗时: 6.206s
[2025-09-08 02:43:26] 自旋算符进度: 84/100, 当前算符: S_0 · S_83, 耗时: 6.154s
[2025-09-08 02:43:32] 自旋算符进度: 85/100, 当前算符: S_0 · S_84, 耗时: 6.147s
[2025-09-08 02:43:38] 自旋算符进度: 86/100, 当前算符: S_0 · S_85, 耗时: 6.204s
[2025-09-08 02:43:45] 自旋算符进度: 87/100, 当前算符: S_0 · S_86, 耗时: 6.157s
[2025-09-08 02:43:51] 自旋算符进度: 88/100, 当前算符: S_0 · S_87, 耗时: 6.149s
[2025-09-08 02:43:57] 自旋算符进度: 89/100, 当前算符: S_0 · S_88, 耗时: 6.153s
[2025-09-08 02:44:03] 自旋算符进度: 90/100, 当前算符: S_0 · S_89, 耗时: 6.206s
[2025-09-08 02:44:09] 自旋算符进度: 91/100, 当前算符: S_0 · S_90, 耗时: 6.158s
[2025-09-08 02:44:15] 自旋算符进度: 92/100, 当前算符: S_0 · S_91, 耗时: 6.176s
[2025-09-08 02:44:22] 自旋算符进度: 93/100, 当前算符: S_0 · S_92, 耗时: 6.205s
[2025-09-08 02:44:28] 自旋算符进度: 94/100, 当前算符: S_0 · S_93, 耗时: 6.157s
[2025-09-08 02:44:34] 自旋算符进度: 95/100, 当前算符: S_0 · S_94, 耗时: 6.176s
[2025-09-08 02:44:40] 自旋算符进度: 96/100, 当前算符: S_0 · S_95, 耗时: 6.167s
[2025-09-08 02:44:46] 自旋算符进度: 97/100, 当前算符: S_0 · S_96, 耗时: 6.164s
[2025-09-08 02:44:52] 自旋算符进度: 98/100, 当前算符: S_0 · S_97, 耗时: 6.149s
[2025-09-08 02:44:59] 自旋算符进度: 99/100, 当前算符: S_0 · S_98, 耗时: 6.206s
[2025-09-08 02:45:05] 自旋算符进度: 100/100, 当前算符: S_0 · S_99, 耗时: 6.153s
[2025-09-08 02:45:05] 自旋相关函数计算完成,总耗时 625.33 秒
[2025-09-08 02:45:05] 计算傅里叶变换...
[2025-09-08 02:45:06] 自旋结构因子计算完成
[2025-09-08 02:45:07] 自旋相关函数平均误差: 0.000745
