[2025-09-18 17:46:24] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.79/training/checkpoints/final_GCNN.pkl
[2025-09-18 17:46:24]   - 迭代次数: final
[2025-09-18 17:46:24]   - 能量: -44.234624+0.001697j ± 0.008289
[2025-09-18 17:46:24]   - 时间戳: 2025-09-18T17:46:01.221750+08:00
[2025-09-18 17:46:45] ✓ 变分状态参数已从checkpoint恢复
[2025-09-18 17:46:45] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-18 17:46:45] ==================================================
[2025-09-18 17:46:45] GCNN for Shastry-Sutherland Model
[2025-09-18 17:46:45] ==================================================
[2025-09-18 17:46:45] System parameters:
[2025-09-18 17:46:45]   - System size: L=5, N=100
[2025-09-18 17:46:45]   - System parameters: J1=0.78, J2=1.0, Q=0.0
[2025-09-18 17:46:45] --------------------------------------------------
[2025-09-18 17:46:45] Model parameters:
[2025-09-18 17:46:45]   - Number of layers = 4
[2025-09-18 17:46:45]   - Number of features = 4
[2025-09-18 17:46:45]   - Total parameters = 19628
[2025-09-18 17:46:45] --------------------------------------------------
[2025-09-18 17:46:45] Training parameters:
[2025-09-18 17:46:45]   - Learning rate: 0.015
[2025-09-18 17:46:45]   - Total iterations: 1050
[2025-09-18 17:46:45]   - Annealing cycles: 3
[2025-09-18 17:46:45]   - Initial period: 150
[2025-09-18 17:46:45]   - Period multiplier: 2.0
[2025-09-18 17:46:45]   - Temperature range: 0.0-1.0
[2025-09-18 17:46:45]   - Samples: 4096
[2025-09-18 17:46:45]   - Discarded samples: 0
[2025-09-18 17:46:45]   - Chunk size: 2048
[2025-09-18 17:46:45]   - Diagonal shift: 0.2
[2025-09-18 17:46:45]   - Gradient clipping: 1.0
[2025-09-18 17:46:45]   - Checkpoint enabled: interval=105
[2025-09-18 17:46:45]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.78/training/checkpoints
[2025-09-18 17:46:45] --------------------------------------------------
[2025-09-18 17:46:45] Device status:
[2025-09-18 17:46:45]   - Devices model: NVIDIA H200 NVL
[2025-09-18 17:46:45]   - Number of devices: 1
[2025-09-18 17:46:45]   - Sharding: True
[2025-09-18 17:46:45] ============================================================
[2025-09-18 17:47:46] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -43.566894+0.005213j
[2025-09-18 17:48:25] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -43.579958-0.006554j
[2025-09-18 17:48:38] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -43.586682-0.000998j
[2025-09-18 17:48:50] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -43.592287+0.001395j
[2025-09-18 17:49:03] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -43.588600-0.000857j
[2025-09-18 17:49:15] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -43.588221-0.002993j
[2025-09-18 17:49:28] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -43.588153-0.002761j
[2025-09-18 17:49:40] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -43.580231-0.000204j
[2025-09-18 17:49:53] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -43.572190-0.000461j
[2025-09-18 17:50:05] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -43.584941-0.002286j
[2025-09-18 17:50:18] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -43.587670+0.000662j
[2025-09-18 17:50:30] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -43.584342-0.003692j
[2025-09-18 17:50:43] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -43.580403+0.000894j
[2025-09-18 17:50:55] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -43.592898-0.000776j
[2025-09-18 17:51:07] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -43.580226-0.004920j
[2025-09-18 17:51:20] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -43.572245-0.002715j
[2025-09-18 17:51:32] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -43.582594+0.001734j
[2025-09-18 17:51:45] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -43.586951+0.001554j
[2025-09-18 17:51:57] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -43.597736-0.002877j
[2025-09-18 17:52:10] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -43.577747+0.004047j
[2025-09-18 17:52:22] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -43.588836+0.000086j
[2025-09-18 17:52:34] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -43.580413+0.000191j
[2025-09-18 17:52:47] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -43.609831+0.000836j
[2025-09-18 17:52:59] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -43.584980+0.000882j
[2025-09-18 17:53:12] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -43.580852-0.002688j
[2025-09-18 17:53:24] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -43.576257+0.001739j
[2025-09-18 17:53:37] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -43.591181+0.001020j
[2025-09-18 17:53:49] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -43.592827+0.001053j
[2025-09-18 17:54:01] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -43.587321+0.000976j
[2025-09-18 17:54:14] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -43.593765+0.002807j
[2025-09-18 17:54:26] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -43.590812+0.002397j
[2025-09-18 17:54:39] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -43.578515-0.000239j
[2025-09-18 17:54:51] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -43.586241-0.001806j
[2025-09-18 17:55:04] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -43.571383+0.002505j
[2025-09-18 17:55:16] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -43.602837+0.006469j
[2025-09-18 17:55:29] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -43.585012+0.001376j
[2025-09-18 17:55:41] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -43.587614+0.002040j
[2025-09-18 17:55:54] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -43.586138-0.000514j
[2025-09-18 17:56:06] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -43.576030-0.007460j
[2025-09-18 17:56:18] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -43.598821-0.000245j
[2025-09-18 17:56:31] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -43.595316-0.001417j
[2025-09-18 17:56:43] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -43.590945-0.000598j
[2025-09-18 17:56:56] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -43.582638-0.000629j
[2025-09-18 17:57:08] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -43.586659+0.004923j
[2025-09-18 17:57:21] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -43.556755-0.000230j
[2025-09-18 17:57:33] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -43.582989-0.002601j
[2025-09-18 17:57:45] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -43.585301-0.005981j
[2025-09-18 17:57:58] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -43.585466+0.000856j
[2025-09-18 17:58:10] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -43.578035+0.000641j
[2025-09-18 17:58:23] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -43.580088-0.000823j
[2025-09-18 17:58:35] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -43.582299-0.000273j
[2025-09-18 17:58:48] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -43.605086+0.002631j
[2025-09-18 17:59:00] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -43.601326-0.001439j
[2025-09-18 17:59:13] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -43.590447-0.000291j
[2025-09-18 17:59:25] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -43.591157-0.000518j
[2025-09-18 17:59:38] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -43.577947-0.002369j
[2025-09-18 17:59:50] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -43.594518-0.001405j
[2025-09-18 18:00:02] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -43.571770-0.000217j
[2025-09-18 18:00:15] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -43.606541-0.002692j
[2025-09-18 18:00:27] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -43.577436+0.004957j
[2025-09-18 18:00:40] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -43.593726-0.001208j
[2025-09-18 18:00:52] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -43.584972+0.003327j
[2025-09-18 18:01:05] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -43.592051-0.001896j
[2025-09-18 18:01:17] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -43.578485-0.002483j
[2025-09-18 18:01:30] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -43.576006-0.000051j
[2025-09-18 18:01:42] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -43.587727-0.000683j
[2025-09-18 18:01:55] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -43.593844+0.002535j
[2025-09-18 18:02:07] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -43.599354-0.000391j
[2025-09-18 18:02:20] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -43.599579-0.001554j
[2025-09-18 18:02:32] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -43.587807-0.000301j
[2025-09-18 18:02:45] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -43.579587-0.000596j
[2025-09-18 18:02:57] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -43.584684-0.001212j
[2025-09-18 18:03:09] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -43.585018-0.001839j
[2025-09-18 18:03:22] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -43.582354-0.003386j
[2025-09-18 18:03:34] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -43.595630-0.002388j
[2025-09-18 18:03:47] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -43.593985-0.001007j
[2025-09-18 18:03:59] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -43.587676-0.000338j
[2025-09-18 18:04:12] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -43.578341-0.001089j
[2025-09-18 18:04:24] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -43.585857+0.002776j
[2025-09-18 18:04:37] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -43.590863-0.001966j
[2025-09-18 18:04:49] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -43.572202+0.001242j
[2025-09-18 18:05:01] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -43.596648-0.000931j
[2025-09-18 18:05:14] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -43.580322-0.002251j
[2025-09-18 18:05:26] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -43.595088+0.002230j
[2025-09-18 18:05:39] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -43.601236+0.001333j
[2025-09-18 18:05:51] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -43.572406+0.000618j
[2025-09-18 18:06:04] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -43.587277-0.003482j
[2025-09-18 18:06:16] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -43.587436+0.000482j
[2025-09-18 18:06:29] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -43.586929-0.001748j
[2025-09-18 18:06:41] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -43.586662+0.001940j
[2025-09-18 18:06:53] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -43.592088-0.000390j
[2025-09-18 18:07:06] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -43.591371-0.001279j
[2025-09-18 18:07:18] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -43.585709+0.000718j
[2025-09-18 18:07:31] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -43.589760-0.000387j
[2025-09-18 18:07:43] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -43.585534+0.003398j
[2025-09-18 18:07:56] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -43.587822-0.001708j
[2025-09-18 18:08:08] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -43.585968-0.000325j
[2025-09-18 18:08:20] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -43.600235+0.004482j
[2025-09-18 18:08:33] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -43.596092-0.002261j
[2025-09-18 18:08:45] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -43.572547+0.000036j
[2025-09-18 18:08:58] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -43.576208+0.000615j
[2025-09-18 18:09:10] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -43.585313+0.002414j
[2025-09-18 18:09:23] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -43.595432+0.000067j
[2025-09-18 18:09:35] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -43.587424+0.000348j
[2025-09-18 18:09:47] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -43.594089+0.000855j
[2025-09-18 18:09:47] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-18 18:10:00] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -43.580718-0.001778j
[2025-09-18 18:10:12] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -43.585250+0.003205j
[2025-09-18 18:10:25] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -43.582703+0.003903j
[2025-09-18 18:10:37] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -43.577474+0.000373j
[2025-09-18 18:10:50] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -43.590190-0.001037j
[2025-09-18 18:11:02] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -43.586883-0.000686j
[2025-09-18 18:11:15] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -43.587805+0.001007j
[2025-09-18 18:11:27] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -43.588816+0.002019j
[2025-09-18 18:11:39] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -43.574328+0.002997j
[2025-09-18 18:11:52] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -43.597037+0.002056j
[2025-09-18 18:12:04] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -43.586168-0.002378j
[2025-09-18 18:12:17] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -43.585980-0.001008j
[2025-09-18 18:12:29] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -43.578939+0.000955j
[2025-09-18 18:12:42] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -43.585384+0.001582j
[2025-09-18 18:12:54] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -43.585963-0.001448j
[2025-09-18 18:13:06] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -43.587816+0.000423j
[2025-09-18 18:13:19] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -43.582358-0.000837j
[2025-09-18 18:13:31] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -43.589263+0.000081j
[2025-09-18 18:13:44] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -43.592286+0.000473j
[2025-09-18 18:13:56] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -43.579505-0.001942j
[2025-09-18 18:14:09] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -43.588357+0.001578j
[2025-09-18 18:14:21] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -43.585677+0.000591j
[2025-09-18 18:14:34] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -43.580143+0.000616j
[2025-09-18 18:14:46] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -43.583683-0.000097j
[2025-09-18 18:14:58] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -43.612411+0.001840j
[2025-09-18 18:15:11] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -43.593317+0.002483j
[2025-09-18 18:15:23] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -43.577588+0.000797j
[2025-09-18 18:15:36] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -43.604345+0.001807j
[2025-09-18 18:15:48] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -43.576203-0.000510j
[2025-09-18 18:16:01] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -43.590859-0.001664j
[2025-09-18 18:16:13] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -43.570928-0.001326j
[2025-09-18 18:16:25] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -43.601540+0.001202j
[2025-09-18 18:16:38] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -43.589629+0.003940j
[2025-09-18 18:16:50] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -43.575521-0.001169j
[2025-09-18 18:17:03] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -43.585588-0.003362j
[2025-09-18 18:17:15] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -43.593451-0.008356j
[2025-09-18 18:17:28] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -43.589132+0.002812j
[2025-09-18 18:17:40] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -43.579930+0.005521j
[2025-09-18 18:17:53] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -43.598850+0.001104j
[2025-09-18 18:18:05] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -43.581766+0.000099j
[2025-09-18 18:18:18] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -43.571666-0.000595j
[2025-09-18 18:18:30] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -43.600893+0.001781j
[2025-09-18 18:18:42] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -43.566515-0.001899j
[2025-09-18 18:18:55] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -43.582475-0.000121j
[2025-09-18 18:19:07] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -43.583211-0.002743j
[2025-09-18 18:19:07] RESTART #1 | Period: 300
[2025-09-18 18:19:20] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -43.588803+0.001291j
[2025-09-18 18:19:32] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -43.579032+0.003222j
[2025-09-18 18:19:45] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -43.582052-0.003983j
[2025-09-18 18:19:57] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -43.585440-0.004077j
[2025-09-18 18:20:10] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -43.592591-0.003563j
[2025-09-18 18:20:22] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -43.602694-0.004912j
[2025-09-18 18:20:34] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -43.583159-0.001316j
[2025-09-18 18:20:47] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -43.596276-0.000915j
[2025-09-18 18:20:59] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -43.579139-0.001179j
[2025-09-18 18:21:12] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -43.591284-0.000211j
[2025-09-18 18:21:24] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -43.587156+0.000048j
[2025-09-18 18:21:37] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -43.605743+0.001395j
[2025-09-18 18:21:49] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -43.580336+0.001810j
[2025-09-18 18:22:02] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -43.587961+0.001887j
[2025-09-18 18:22:14] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -43.603466+0.004141j
[2025-09-18 18:22:26] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -43.589408+0.002362j
[2025-09-18 18:22:39] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -43.584868+0.000196j
[2025-09-18 18:22:51] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -43.576948+0.002267j
[2025-09-18 18:23:04] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -43.573009-0.000664j
[2025-09-18 18:23:16] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -43.587445-0.001738j
[2025-09-18 18:23:29] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -43.584584-0.000532j
[2025-09-18 18:23:41] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -43.593031-0.005857j
[2025-09-18 18:23:54] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -43.589442+0.001061j
[2025-09-18 18:24:06] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -43.586967-0.000239j
[2025-09-18 18:24:18] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -43.595459+0.002716j
[2025-09-18 18:24:31] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -43.581345+0.006770j
[2025-09-18 18:24:43] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -43.577874+0.002545j
[2025-09-18 18:24:56] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -43.579279-0.000449j
[2025-09-18 18:25:08] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -43.598059+0.001814j
[2025-09-18 18:25:21] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -43.587633+0.002192j
[2025-09-18 18:25:33] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -43.593766+0.000525j
[2025-09-18 18:25:45] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -43.588392+0.001015j
[2025-09-18 18:25:58] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -43.566454+0.000843j
[2025-09-18 18:26:10] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -43.586776-0.003165j
[2025-09-18 18:26:23] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -43.609163+0.000684j
[2025-09-18 18:26:35] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -43.590843+0.000039j
[2025-09-18 18:26:48] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -43.584971+0.004130j
[2025-09-18 18:27:00] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -43.591019+0.003246j
[2025-09-18 18:27:13] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -43.575534-0.000258j
[2025-09-18 18:27:25] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -43.601117+0.001640j
[2025-09-18 18:27:38] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -43.602020-0.007959j
[2025-09-18 18:27:50] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -43.583046+0.002412j
[2025-09-18 18:28:02] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -43.591518-0.002969j
[2025-09-18 18:28:15] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -43.587794-0.001939j
[2025-09-18 18:28:27] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -43.594227-0.001518j
[2025-09-18 18:28:40] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -43.595276-0.001575j
[2025-09-18 18:28:52] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -43.594103-0.000553j
[2025-09-18 18:29:05] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -43.582572-0.001918j
[2025-09-18 18:29:17] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -43.612075-0.011217j
[2025-09-18 18:29:29] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -43.600554-0.003267j
[2025-09-18 18:29:42] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -43.595570-0.001459j
[2025-09-18 18:29:54] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -43.587888+0.000574j
[2025-09-18 18:30:07] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -43.600865-0.002194j
[2025-09-18 18:30:19] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -43.582812-0.000684j
[2025-09-18 18:30:32] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -43.579447-0.004914j
[2025-09-18 18:30:44] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -43.597801+0.000131j
[2025-09-18 18:30:57] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -43.583234+0.000362j
[2025-09-18 18:31:09] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -43.587087-0.001955j
[2025-09-18 18:31:21] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -43.592131-0.002130j
[2025-09-18 18:31:34] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -43.588261-0.006756j
[2025-09-18 18:31:34] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-18 18:31:46] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -43.582620-0.001158j
[2025-09-18 18:31:59] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -43.594637+0.000942j
[2025-09-18 18:32:11] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -43.599261+0.001813j
[2025-09-18 18:32:24] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -43.581737+0.000194j
[2025-09-18 18:32:36] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -43.580578+0.002437j
[2025-09-18 18:32:49] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -43.575340-0.001972j
[2025-09-18 18:33:01] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -43.580888+0.001194j
[2025-09-18 18:33:14] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -43.575413-0.001535j
[2025-09-18 18:33:26] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -43.594810-0.000682j
[2025-09-18 18:33:38] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -43.594167+0.000929j
[2025-09-18 18:33:51] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -43.568054-0.001631j
[2025-09-18 18:34:03] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -43.588210+0.001727j
[2025-09-18 18:34:16] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -43.576899-0.002388j
[2025-09-18 18:34:28] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -43.578036-0.004361j
[2025-09-18 18:34:41] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -43.585388-0.002207j
[2025-09-18 18:34:53] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -43.586670-0.001035j
[2025-09-18 18:35:06] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -43.574922+0.003491j
[2025-09-18 18:35:18] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -43.598446-0.000828j
[2025-09-18 18:35:30] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -43.584998+0.003480j
[2025-09-18 18:35:43] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -43.585755-0.000037j
[2025-09-18 18:35:55] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -43.585631+0.000501j
[2025-09-18 18:36:08] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -43.581950-0.002900j
[2025-09-18 18:36:20] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -43.576506+0.002194j
[2025-09-18 18:36:33] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -43.583243+0.003201j
[2025-09-18 18:36:45] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -43.596139-0.002564j
[2025-09-18 18:36:57] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -43.588199-0.001132j
[2025-09-18 18:37:10] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -43.577521-0.001404j
[2025-09-18 18:37:22] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -43.579357-0.001561j
[2025-09-18 18:37:35] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -43.586520+0.000481j
[2025-09-18 18:37:47] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -43.592855+0.002724j
[2025-09-18 18:38:00] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -43.598718+0.000713j
[2025-09-18 18:38:12] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -43.586135-0.002768j
[2025-09-18 18:38:25] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -43.595421+0.001520j
[2025-09-18 18:38:37] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -43.581870+0.003385j
[2025-09-18 18:38:49] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -43.586243+0.004859j
[2025-09-18 18:39:02] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -43.592352-0.000638j
[2025-09-18 18:39:14] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -43.577425+0.005537j
[2025-09-18 18:39:27] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -43.590689-0.000011j
[2025-09-18 18:39:39] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -43.602121+0.002395j
[2025-09-18 18:39:51] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -43.582833+0.000928j
[2025-09-18 18:40:04] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -43.595189+0.003561j
[2025-09-18 18:40:16] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -43.585454+0.001645j
[2025-09-18 18:40:29] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -43.587440-0.003165j
[2025-09-18 18:40:41] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -43.591022-0.000534j
[2025-09-18 18:40:54] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -43.585658-0.002432j
[2025-09-18 18:41:06] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -43.591040+0.001841j
[2025-09-18 18:41:18] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -43.590877+0.000058j
[2025-09-18 18:41:31] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -43.592514+0.000596j
[2025-09-18 18:41:43] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -43.587709+0.002402j
[2025-09-18 18:41:56] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -43.585102+0.000072j
[2025-09-18 18:42:08] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -43.587941-0.004060j
[2025-09-18 18:42:21] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -43.592076-0.001831j
[2025-09-18 18:42:33] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -43.587860+0.003079j
[2025-09-18 18:42:46] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -43.589674-0.001341j
[2025-09-18 18:42:58] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -43.599788-0.003104j
[2025-09-18 18:43:10] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -43.581537-0.000778j
[2025-09-18 18:43:23] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -43.586927-0.003607j
[2025-09-18 18:43:35] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -43.610730+0.000083j
[2025-09-18 18:43:48] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -43.606252-0.000941j
[2025-09-18 18:44:00] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -43.600142+0.000733j
[2025-09-18 18:44:13] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -43.576348+0.002526j
[2025-09-18 18:44:25] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -43.594257+0.002351j
[2025-09-18 18:44:37] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -43.588729+0.002475j
[2025-09-18 18:44:50] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -43.586202+0.002313j
[2025-09-18 18:45:02] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -43.592450-0.001615j
[2025-09-18 18:45:15] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -43.581736+0.001242j
[2025-09-18 18:45:27] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -43.603259-0.000339j
[2025-09-18 18:45:40] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -43.583931-0.000443j
[2025-09-18 18:45:52] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -43.581488-0.000753j
[2025-09-18 18:46:05] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -43.606566-0.001711j
[2025-09-18 18:46:17] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -43.585857+0.001349j
[2025-09-18 18:46:29] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -43.589691-0.002387j
[2025-09-18 18:46:42] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -43.581051+0.000438j
[2025-09-18 18:46:54] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -43.576415+0.001216j
[2025-09-18 18:47:07] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -43.576083-0.001223j
[2025-09-18 18:47:19] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -43.591399-0.000237j
[2025-09-18 18:47:32] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -43.582086-0.001170j
[2025-09-18 18:47:44] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -43.605009-0.000659j
[2025-09-18 18:47:57] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -43.589625+0.002412j
[2025-09-18 18:48:09] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -43.584222+0.000173j
[2025-09-18 18:48:22] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -43.600889-0.003268j
[2025-09-18 18:48:34] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -43.599559+0.001202j
[2025-09-18 18:48:46] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -43.591680+0.000518j
[2025-09-18 18:48:59] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -43.601690+0.000066j
[2025-09-18 18:49:11] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -43.597915+0.002834j
[2025-09-18 18:49:24] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -43.605086-0.004901j
[2025-09-18 18:49:36] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -43.587592-0.000271j
[2025-09-18 18:49:49] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -43.582278+0.000682j
[2025-09-18 18:50:01] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -43.603277-0.003499j
[2025-09-18 18:50:14] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -43.589169-0.000722j
[2025-09-18 18:50:26] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -43.599185-0.002957j
[2025-09-18 18:50:38] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -43.591564+0.002295j
[2025-09-18 18:50:51] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -43.580977+0.001768j
[2025-09-18 18:51:03] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -43.584925-0.005550j
[2025-09-18 18:51:16] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -43.578589-0.002773j
[2025-09-18 18:51:28] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -43.585360-0.004815j
[2025-09-18 18:51:41] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -43.571982+0.001043j
[2025-09-18 18:51:53] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -43.598929-0.000675j
[2025-09-18 18:52:06] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -43.586737+0.000116j
[2025-09-18 18:52:18] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -43.588564-0.001608j
[2025-09-18 18:52:31] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -43.601091+0.001629j
[2025-09-18 18:52:43] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -43.600038-0.003368j
[2025-09-18 18:52:56] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -43.584204-0.002128j
[2025-09-18 18:53:08] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -43.598020-0.001526j
[2025-09-18 18:53:21] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -43.596004+0.000656j
[2025-09-18 18:53:21] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-18 18:53:33] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -43.611863-0.000265j
[2025-09-18 18:53:46] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -43.599675-0.001266j
[2025-09-18 18:53:58] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -43.583502+0.000431j
[2025-09-18 18:54:10] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -43.604376+0.002646j
[2025-09-18 18:54:23] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -43.587427-0.004618j
[2025-09-18 18:54:35] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -43.585405+0.002225j
[2025-09-18 18:54:48] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -43.596855-0.002592j
[2025-09-18 18:55:00] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -43.589642-0.000316j
[2025-09-18 18:55:13] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -43.612634-0.000532j
[2025-09-18 18:55:25] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -43.597300+0.001718j
[2025-09-18 18:55:38] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -43.590344+0.002552j
[2025-09-18 18:55:50] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -43.591085+0.000265j
[2025-09-18 18:56:02] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -43.590721-0.000199j
[2025-09-18 18:56:15] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -43.582765+0.002369j
[2025-09-18 18:56:27] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -43.592375+0.002696j
[2025-09-18 18:56:40] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -43.597168+0.005852j
[2025-09-18 18:56:52] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -43.600120+0.001341j
[2025-09-18 18:57:05] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -43.609316+0.000155j
[2025-09-18 18:57:17] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -43.585880-0.003027j
[2025-09-18 18:57:29] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -43.590815+0.005058j
[2025-09-18 18:57:42] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -43.598070+0.002954j
[2025-09-18 18:57:54] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -43.580366+0.001298j
[2025-09-18 18:58:07] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -43.585940+0.000431j
[2025-09-18 18:58:19] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -43.574781+0.004139j
[2025-09-18 18:58:32] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -43.582898-0.003630j
[2025-09-18 18:58:44] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -43.587739+0.000025j
[2025-09-18 18:58:57] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -43.572672+0.002273j
[2025-09-18 18:59:09] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -43.589955-0.000015j
[2025-09-18 18:59:21] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -43.588648+0.001951j
[2025-09-18 18:59:34] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -43.594102-0.001384j
[2025-09-18 18:59:46] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -43.598160+0.000034j
[2025-09-18 18:59:59] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -43.594699+0.001321j
[2025-09-18 19:00:11] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -43.585237-0.000957j
[2025-09-18 19:00:24] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -43.601985-0.000348j
[2025-09-18 19:00:36] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -43.598864-0.002265j
[2025-09-18 19:00:48] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -43.594398+0.000133j
[2025-09-18 19:01:01] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -43.605821+0.002161j
[2025-09-18 19:01:13] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -43.577765-0.000129j
[2025-09-18 19:01:26] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -43.590913+0.000309j
[2025-09-18 19:01:38] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -43.581917-0.000482j
[2025-09-18 19:01:51] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -43.581778-0.001832j
[2025-09-18 19:02:03] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -43.592787+0.001238j
[2025-09-18 19:02:16] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -43.590014+0.000579j
[2025-09-18 19:02:28] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -43.580158-0.003044j
[2025-09-18 19:02:40] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -43.589254-0.001116j
[2025-09-18 19:02:53] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -43.588840+0.000688j
[2025-09-18 19:03:05] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -43.593192-0.001456j
[2025-09-18 19:03:18] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -43.594747+0.000771j
[2025-09-18 19:03:30] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -43.594353+0.000592j
[2025-09-18 19:03:42] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -43.595982-0.000089j
[2025-09-18 19:03:55] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -43.591289-0.002337j
[2025-09-18 19:04:07] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -43.596997-0.001529j
[2025-09-18 19:04:20] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -43.588418+0.001147j
[2025-09-18 19:04:32] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -43.583628-0.005692j
[2025-09-18 19:04:45] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -43.607169-0.000564j
[2025-09-18 19:04:57] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -43.584481+0.000634j
[2025-09-18 19:05:09] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -43.589630+0.002846j
[2025-09-18 19:05:22] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -43.582524-0.002313j
[2025-09-18 19:05:34] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -43.584351+0.003062j
[2025-09-18 19:05:47] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -43.582814-0.000550j
[2025-09-18 19:05:59] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -43.581736-0.002638j
[2025-09-18 19:06:12] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -43.585340-0.000676j
[2025-09-18 19:06:24] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -43.592266-0.000693j
[2025-09-18 19:06:36] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -43.588627-0.001812j
[2025-09-18 19:06:49] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -43.590157+0.002977j
[2025-09-18 19:07:01] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -43.595129+0.000108j
[2025-09-18 19:07:14] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -43.595303+0.001611j
[2025-09-18 19:07:26] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -43.581520+0.002018j
[2025-09-18 19:07:39] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -43.597661-0.005681j
[2025-09-18 19:07:51] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -43.580552-0.005735j
[2025-09-18 19:08:03] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -43.585127+0.002613j
[2025-09-18 19:08:16] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -43.594772-0.000837j
[2025-09-18 19:08:28] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -43.589965+0.002562j
[2025-09-18 19:08:41] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -43.605843+0.000001j
[2025-09-18 19:08:53] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -43.582397+0.003327j
[2025-09-18 19:09:06] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -43.584445+0.002122j
[2025-09-18 19:09:18] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -43.594108+0.000920j
[2025-09-18 19:09:31] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -43.588007-0.000586j
[2025-09-18 19:09:43] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -43.589171-0.000458j
[2025-09-18 19:09:56] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -43.597398+0.002859j
[2025-09-18 19:10:08] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -43.592745-0.002837j
[2025-09-18 19:10:20] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -43.605015-0.001797j
[2025-09-18 19:10:33] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -43.600478-0.000729j
[2025-09-18 19:10:45] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -43.590810-0.000740j
[2025-09-18 19:10:58] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -43.595413-0.004403j
[2025-09-18 19:11:10] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -43.583725-0.003213j
[2025-09-18 19:11:23] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -43.597090+0.003028j
[2025-09-18 19:11:35] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -43.609317+0.000914j
[2025-09-18 19:11:48] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -43.607512-0.000517j
[2025-09-18 19:12:00] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -43.590701+0.000942j
[2025-09-18 19:12:13] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -43.580322+0.001472j
[2025-09-18 19:12:25] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -43.581355-0.002718j
[2025-09-18 19:12:38] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -43.596035-0.003606j
[2025-09-18 19:12:50] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -43.586351-0.001306j
[2025-09-18 19:13:03] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -43.596097-0.000930j
[2025-09-18 19:13:15] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -43.578663-0.000303j
[2025-09-18 19:13:28] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -43.600808-0.006420j
[2025-09-18 19:13:40] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -43.595326+0.001204j
[2025-09-18 19:13:53] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -43.583740+0.002862j
[2025-09-18 19:14:05] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -43.582921-0.001101j
[2025-09-18 19:14:18] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -43.582857-0.002354j
[2025-09-18 19:14:30] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -43.592318+0.003795j
[2025-09-18 19:14:42] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -43.583963+0.003162j
[2025-09-18 19:14:55] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -43.570778+0.000916j
[2025-09-18 19:15:07] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -43.593335+0.000258j
[2025-09-18 19:15:07] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-18 19:15:20] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -43.593092+0.001227j
[2025-09-18 19:15:32] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -43.596016-0.002776j
[2025-09-18 19:15:45] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -43.598676-0.002300j
[2025-09-18 19:15:57] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -43.615522+0.003005j
[2025-09-18 19:16:09] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -43.593626-0.002071j
[2025-09-18 19:16:22] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -43.589788-0.000315j
[2025-09-18 19:16:34] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -43.592013+0.000197j
[2025-09-18 19:16:47] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -43.584051+0.002132j
[2025-09-18 19:16:59] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -43.586078+0.001953j
[2025-09-18 19:17:12] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -43.582445+0.003151j
[2025-09-18 19:17:24] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -43.591368-0.002520j
[2025-09-18 19:17:37] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -43.587431-0.001141j
[2025-09-18 19:17:49] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -43.582961+0.001957j
[2025-09-18 19:18:01] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -43.596835+0.000288j
[2025-09-18 19:18:14] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -43.584890-0.000924j
[2025-09-18 19:18:26] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -43.583549+0.001909j
[2025-09-18 19:18:39] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -43.591535+0.000396j
[2025-09-18 19:18:51] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -43.589232-0.000492j
[2025-09-18 19:19:04] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -43.600588+0.004609j
[2025-09-18 19:19:16] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -43.587082-0.000726j
[2025-09-18 19:19:28] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -43.589716-0.002341j
[2025-09-18 19:19:41] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -43.575029+0.000561j
[2025-09-18 19:19:53] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -43.598881-0.007440j
[2025-09-18 19:20:06] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -43.594725-0.002424j
[2025-09-18 19:20:18] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -43.604180+0.004165j
[2025-09-18 19:20:31] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -43.587044+0.000162j
[2025-09-18 19:20:43] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -43.599542+0.005653j
[2025-09-18 19:20:56] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -43.585191+0.001517j
[2025-09-18 19:21:08] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -43.604190-0.000434j
[2025-09-18 19:21:20] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -43.593905+0.000263j
[2025-09-18 19:21:20] RESTART #2 | Period: 600
[2025-09-18 19:21:33] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -43.580252-0.000664j
[2025-09-18 19:21:45] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -43.598603+0.000997j
[2025-09-18 19:21:58] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -43.605077-0.000953j
[2025-09-18 19:22:10] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -43.594784-0.000307j
[2025-09-18 19:22:22] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -43.593239-0.000890j
[2025-09-18 19:22:35] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -43.580558+0.000322j
[2025-09-18 19:22:47] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -43.589357+0.001107j
[2025-09-18 19:23:00] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -43.588747+0.001181j
[2025-09-18 19:23:12] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -43.590497+0.000354j
[2025-09-18 19:23:25] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -43.588701-0.002930j
[2025-09-18 19:23:37] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -43.588172-0.000291j
[2025-09-18 19:23:50] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -43.600517-0.000376j
[2025-09-18 19:24:02] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -43.595843+0.000028j
[2025-09-18 19:24:14] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -43.591961-0.001142j
[2025-09-18 19:24:27] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -43.597339+0.002316j
[2025-09-18 19:24:39] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -43.591549+0.002467j
[2025-09-18 19:24:52] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -43.598295-0.002676j
[2025-09-18 19:25:04] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -43.599620+0.001782j
[2025-09-18 19:25:17] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -43.592977+0.000297j
[2025-09-18 19:25:29] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -43.592559+0.001080j
[2025-09-18 19:25:41] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -43.582731-0.001326j
[2025-09-18 19:25:54] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -43.593963-0.003645j
[2025-09-18 19:26:06] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -43.587626+0.001661j
[2025-09-18 19:26:19] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -43.585072+0.001332j
[2025-09-18 19:26:31] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -43.589088+0.001931j
[2025-09-18 19:26:44] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -43.584786-0.002826j
[2025-09-18 19:26:56] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -43.579477-0.001436j
[2025-09-18 19:27:09] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -43.600865+0.002410j
[2025-09-18 19:27:21] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -43.593520+0.003434j
[2025-09-18 19:27:34] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -43.605075+0.000070j
[2025-09-18 19:27:46] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -43.580785-0.000224j
[2025-09-18 19:27:58] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -43.603796-0.001748j
[2025-09-18 19:28:11] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -43.591113+0.000192j
[2025-09-18 19:28:23] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -43.581990+0.003751j
[2025-09-18 19:28:36] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -43.582567-0.005668j
[2025-09-18 19:28:48] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -43.587667+0.001461j
[2025-09-18 19:29:00] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -43.590598+0.002024j
[2025-09-18 19:29:13] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -43.587916-0.000843j
[2025-09-18 19:29:25] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -43.584813+0.000216j
[2025-09-18 19:29:38] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -43.592241+0.001305j
[2025-09-18 19:29:50] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -43.584601+0.001601j
[2025-09-18 19:30:03] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -43.592693-0.001974j
[2025-09-18 19:30:15] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -43.595674+0.001621j
[2025-09-18 19:30:28] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -43.588602+0.001440j
[2025-09-18 19:30:40] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -43.590050+0.001060j
[2025-09-18 19:30:52] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -43.590228+0.004792j
[2025-09-18 19:31:05] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -43.585125+0.000129j
[2025-09-18 19:31:17] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -43.596961-0.000744j
[2025-09-18 19:31:30] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -43.591641-0.002380j
[2025-09-18 19:31:42] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -43.589895+0.009774j
[2025-09-18 19:31:55] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -43.596901+0.003965j
[2025-09-18 19:32:07] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -43.595368+0.001964j
[2025-09-18 19:32:20] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -43.597710-0.000763j
[2025-09-18 19:32:32] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -43.587672-0.001589j
[2025-09-18 19:32:45] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -43.590030-0.003966j
[2025-09-18 19:32:57] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -43.588768+0.001122j
[2025-09-18 19:33:09] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -43.597393+0.000254j
[2025-09-18 19:33:22] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -43.580285+0.003782j
[2025-09-18 19:33:34] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -43.605094-0.002672j
[2025-09-18 19:33:47] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -43.582165+0.000330j
[2025-09-18 19:33:59] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -43.584748-0.000892j
[2025-09-18 19:34:12] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -43.586195+0.003791j
[2025-09-18 19:34:24] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -43.589945+0.001282j
[2025-09-18 19:34:37] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -43.597498+0.001260j
[2025-09-18 19:34:49] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -43.576693-0.001542j
[2025-09-18 19:35:02] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -43.602925-0.000002j
[2025-09-18 19:35:14] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -43.600364+0.001808j
[2025-09-18 19:35:26] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -43.577950-0.000158j
[2025-09-18 19:35:39] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -43.591242+0.001019j
[2025-09-18 19:35:51] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -43.589728-0.002520j
[2025-09-18 19:36:04] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -43.602099-0.003407j
[2025-09-18 19:36:16] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -43.601615+0.001698j
[2025-09-18 19:36:29] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -43.604644+0.000688j
[2025-09-18 19:36:41] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -43.595495-0.002707j
[2025-09-18 19:36:53] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -43.589332-0.000892j
[2025-09-18 19:36:53] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-18 19:37:06] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -43.593447+0.001931j
[2025-09-18 19:37:18] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -43.594603+0.000355j
[2025-09-18 19:37:31] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -43.608695-0.002098j
[2025-09-18 19:37:43] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -43.585609-0.001449j
[2025-09-18 19:37:56] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -43.587311-0.001174j
[2025-09-18 19:38:08] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -43.600734-0.000997j
[2025-09-18 19:38:21] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -43.584330-0.002074j
[2025-09-18 19:38:33] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -43.578750-0.000109j
[2025-09-18 19:38:45] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -43.588739+0.000270j
[2025-09-18 19:38:58] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -43.598876-0.001234j
[2025-09-18 19:39:10] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -43.590332+0.003058j
[2025-09-18 19:39:23] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -43.594399-0.004374j
[2025-09-18 19:39:35] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -43.593214-0.000892j
[2025-09-18 19:39:48] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -43.590920+0.002261j
[2025-09-18 19:40:00] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -43.581686-0.000916j
[2025-09-18 19:40:13] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -43.588928-0.000616j
[2025-09-18 19:40:25] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -43.589932-0.005395j
[2025-09-18 19:40:37] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -43.587633-0.000268j
[2025-09-18 19:40:50] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -43.584900-0.001283j
[2025-09-18 19:41:02] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -43.599126+0.000355j
[2025-09-18 19:41:15] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -43.592591-0.001294j
[2025-09-18 19:41:27] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -43.596808-0.000061j
[2025-09-18 19:41:39] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -43.597602-0.001627j
[2025-09-18 19:41:52] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -43.585142-0.000316j
[2025-09-18 19:42:04] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -43.599322+0.001605j
[2025-09-18 19:42:17] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -43.591975-0.000523j
[2025-09-18 19:42:29] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -43.591706+0.000522j
[2025-09-18 19:42:42] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -43.588580-0.000018j
[2025-09-18 19:42:54] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -43.597329-0.001755j
[2025-09-18 19:43:06] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -43.595299+0.000983j
[2025-09-18 19:43:19] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -43.587922-0.004718j
[2025-09-18 19:43:31] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -43.584637+0.003990j
[2025-09-18 19:43:44] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -43.602658+0.002481j
[2025-09-18 19:43:56] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -43.592731+0.000225j
[2025-09-18 19:44:09] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -43.604884+0.001355j
[2025-09-18 19:44:21] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -43.612338+0.003828j
[2025-09-18 19:44:34] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -43.598378+0.001462j
[2025-09-18 19:44:46] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -43.581123+0.000269j
[2025-09-18 19:44:58] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -43.598056+0.001648j
[2025-09-18 19:45:11] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -43.594138-0.000149j
[2025-09-18 19:45:23] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -43.586660-0.000197j
[2025-09-18 19:45:36] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -43.576828+0.001575j
[2025-09-18 19:45:48] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -43.604667-0.003896j
[2025-09-18 19:46:01] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -43.585715+0.000234j
[2025-09-18 19:46:13] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -43.585290+0.015261j
[2025-09-18 19:46:26] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -43.600210+0.000313j
[2025-09-18 19:46:38] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -43.609982-0.001059j
[2025-09-18 19:46:50] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -43.592533-0.001525j
[2025-09-18 19:47:03] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -43.587013-0.004160j
[2025-09-18 19:47:15] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -43.579216+0.002070j
[2025-09-18 19:47:28] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -43.588063-0.002729j
[2025-09-18 19:47:40] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -43.596702-0.002489j
[2025-09-18 19:47:53] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -43.587673-0.003917j
[2025-09-18 19:48:05] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -43.579388-0.000616j
[2025-09-18 19:48:18] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -43.577729-0.000456j
[2025-09-18 19:48:30] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -43.591044+0.001692j
[2025-09-18 19:48:42] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -43.590223-0.000265j
[2025-09-18 19:48:55] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -43.603605+0.001350j
[2025-09-18 19:49:07] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -43.600807+0.004423j
[2025-09-18 19:49:20] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -43.600527-0.003019j
[2025-09-18 19:49:32] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -43.600712+0.001504j
[2025-09-18 19:49:45] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -43.591128+0.001325j
[2025-09-18 19:49:57] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -43.583191+0.004969j
[2025-09-18 19:50:09] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -43.588880-0.000526j
[2025-09-18 19:50:22] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -43.596751+0.000078j
[2025-09-18 19:50:34] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -43.596605-0.000690j
[2025-09-18 19:50:47] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -43.597280-0.002674j
[2025-09-18 19:50:59] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -43.599607+0.000796j
[2025-09-18 19:51:12] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -43.587044+0.001522j
[2025-09-18 19:51:24] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -43.594551-0.003559j
[2025-09-18 19:51:37] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -43.594102-0.002732j
[2025-09-18 19:51:49] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -43.594995+0.000553j
[2025-09-18 19:52:01] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -43.590057-0.001220j
[2025-09-18 19:52:14] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -43.571723+0.000530j
[2025-09-18 19:52:26] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -43.590571+0.000319j
[2025-09-18 19:52:39] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -43.592447+0.000776j
[2025-09-18 19:52:51] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -43.583989-0.003881j
[2025-09-18 19:53:04] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -43.604751+0.000335j
[2025-09-18 19:53:16] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -43.579343+0.000788j
[2025-09-18 19:53:28] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -43.583543-0.001379j
[2025-09-18 19:53:41] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -43.583380-0.003986j
[2025-09-18 19:53:53] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -43.600573-0.003814j
[2025-09-18 19:54:06] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -43.598065-0.000648j
[2025-09-18 19:54:18] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -43.602894-0.001000j
[2025-09-18 19:54:31] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -43.595100+0.000043j
[2025-09-18 19:54:43] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -43.594425-0.000629j
[2025-09-18 19:54:55] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -43.601774-0.000325j
[2025-09-18 19:55:08] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -43.602251+0.002509j
[2025-09-18 19:55:20] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -43.591812+0.002422j
[2025-09-18 19:55:33] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -43.592172+0.003163j
[2025-09-18 19:55:45] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -43.580670-0.000273j
[2025-09-18 19:55:58] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -43.564538+0.002215j
[2025-09-18 19:56:10] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -43.610585+0.000518j
[2025-09-18 19:56:22] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -43.593907-0.000441j
[2025-09-18 19:56:35] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -43.599908-0.002222j
[2025-09-18 19:56:47] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -43.581271-0.004827j
[2025-09-18 19:57:00] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -43.598324+0.000386j
[2025-09-18 19:57:12] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -43.588933-0.001265j
[2025-09-18 19:57:25] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -43.594940+0.000183j
[2025-09-18 19:57:37] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -43.601542-0.004331j
[2025-09-18 19:57:49] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -43.588393+0.000175j
[2025-09-18 19:58:02] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -43.588234-0.004064j
[2025-09-18 19:58:14] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -43.596066+0.001281j
[2025-09-18 19:58:27] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -43.600065+0.002177j
[2025-09-18 19:58:39] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -43.592585-0.002027j
[2025-09-18 19:58:39] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-18 19:58:52] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -43.594135-0.000806j
[2025-09-18 19:59:04] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -43.582526+0.006041j
[2025-09-18 19:59:16] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -43.581182+0.000011j
[2025-09-18 19:59:29] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -43.599851+0.001076j
[2025-09-18 19:59:41] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -43.587712-0.000050j
[2025-09-18 19:59:54] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -43.609039-0.002605j
[2025-09-18 20:00:06] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -43.596746-0.001874j
[2025-09-18 20:00:19] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -43.599569+0.000407j
[2025-09-18 20:00:31] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -43.602614+0.002216j
[2025-09-18 20:00:43] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -43.592125+0.002614j
[2025-09-18 20:00:56] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -43.607736+0.001565j
[2025-09-18 20:01:08] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -43.574236-0.000672j
[2025-09-18 20:01:21] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -43.584458+0.001321j
[2025-09-18 20:01:33] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -43.605880-0.003673j
[2025-09-18 20:01:46] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -43.588252+0.000378j
[2025-09-18 20:01:58] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -43.579042+0.003833j
[2025-09-18 20:02:10] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -43.605716-0.001397j
[2025-09-18 20:02:23] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -43.585722-0.000165j
[2025-09-18 20:02:35] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -43.588911-0.002390j
[2025-09-18 20:02:48] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -43.587566-0.001617j
[2025-09-18 20:03:00] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -43.596076-0.000258j
[2025-09-18 20:03:13] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -43.585135-0.004290j
[2025-09-18 20:03:25] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -43.592722-0.001763j
[2025-09-18 20:03:37] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -43.578928+0.002179j
[2025-09-18 20:03:50] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -43.589039+0.000357j
[2025-09-18 20:04:02] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -43.590169+0.001399j
[2025-09-18 20:04:15] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -43.606554-0.002566j
[2025-09-18 20:04:27] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -43.595673+0.002021j
[2025-09-18 20:04:40] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -43.601152+0.001135j
[2025-09-18 20:04:52] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -43.586296-0.001196j
[2025-09-18 20:05:05] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -43.599022-0.000192j
[2025-09-18 20:05:17] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -43.581920+0.002299j
[2025-09-18 20:05:29] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -43.594694+0.003117j
[2025-09-18 20:05:42] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -43.603701-0.000319j
[2025-09-18 20:05:54] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -43.604972-0.000250j
[2025-09-18 20:06:07] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -43.592731-0.001527j
[2025-09-18 20:06:19] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -43.578983+0.003606j
[2025-09-18 20:06:32] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -43.588761-0.002640j
[2025-09-18 20:06:44] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -43.603149-0.003701j
[2025-09-18 20:06:57] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -43.604456+0.001077j
[2025-09-18 20:07:09] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -43.617555+0.003398j
[2025-09-18 20:07:22] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -43.594245+0.001349j
[2025-09-18 20:07:34] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -43.608178+0.000542j
[2025-09-18 20:07:46] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -43.586601+0.001962j
[2025-09-18 20:07:59] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -43.598563-0.001183j
[2025-09-18 20:08:11] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -43.590817+0.003350j
[2025-09-18 20:08:24] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -43.582485+0.000910j
[2025-09-18 20:08:36] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -43.591123-0.002257j
[2025-09-18 20:08:49] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -43.600313+0.002947j
[2025-09-18 20:09:01] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -43.596160-0.001164j
[2025-09-18 20:09:13] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -43.574585+0.001541j
[2025-09-18 20:09:25] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -43.593480-0.002331j
[2025-09-18 20:09:38] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -43.603581+0.002838j
[2025-09-18 20:09:50] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -43.592612+0.000561j
[2025-09-18 20:10:03] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -43.600267+0.000030j
[2025-09-18 20:10:15] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -43.596790-0.000287j
[2025-09-18 20:10:28] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -43.594192+0.000568j
[2025-09-18 20:10:40] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -43.605241+0.000652j
[2025-09-18 20:10:52] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -43.588858-0.001592j
[2025-09-18 20:11:05] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -43.596057-0.002184j
[2025-09-18 20:11:17] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -43.584369-0.004264j
[2025-09-18 20:11:30] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -43.592877-0.001946j
[2025-09-18 20:11:42] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -43.581917-0.000947j
[2025-09-18 20:11:54] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -43.594887+0.002586j
[2025-09-18 20:12:07] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -43.604424+0.004764j
[2025-09-18 20:12:19] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -43.587311+0.002976j
[2025-09-18 20:12:32] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -43.591289+0.002419j
[2025-09-18 20:12:44] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -43.605885-0.001029j
[2025-09-18 20:12:57] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -43.577210+0.002084j
[2025-09-18 20:13:09] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -43.613865-0.001772j
[2025-09-18 20:13:21] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -43.582512-0.002508j
[2025-09-18 20:13:34] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -43.602964+0.002180j
[2025-09-18 20:13:46] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -43.588890-0.005008j
[2025-09-18 20:13:59] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -43.588941-0.000635j
[2025-09-18 20:14:11] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -43.591924+0.002941j
[2025-09-18 20:14:24] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -43.588038+0.002763j
[2025-09-18 20:14:36] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -43.599586+0.001738j
[2025-09-18 20:14:48] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -43.595104-0.002111j
[2025-09-18 20:15:01] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -43.581197-0.001029j
[2025-09-18 20:15:13] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -43.580927+0.003986j
[2025-09-18 20:15:26] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -43.596020+0.005268j
[2025-09-18 20:15:38] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -43.615823+0.004682j
[2025-09-18 20:15:51] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -43.586822-0.001763j
[2025-09-18 20:16:03] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -43.576535-0.000594j
[2025-09-18 20:16:15] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -43.603605-0.004913j
[2025-09-18 20:16:28] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -43.599143-0.002374j
[2025-09-18 20:16:40] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -43.598650-0.000279j
[2025-09-18 20:16:53] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -43.592575-0.002598j
[2025-09-18 20:17:05] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -43.599890-0.000017j
[2025-09-18 20:17:18] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -43.596178-0.001543j
[2025-09-18 20:17:30] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -43.582199+0.000587j
[2025-09-18 20:17:42] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -43.589783-0.001344j
[2025-09-18 20:17:55] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -43.592512-0.002667j
[2025-09-18 20:18:07] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -43.587319+0.001696j
[2025-09-18 20:18:20] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -43.594847+0.004855j
[2025-09-18 20:18:32] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -43.593279+0.000910j
[2025-09-18 20:18:44] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -43.593766-0.005355j
[2025-09-18 20:18:57] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -43.606034+0.000239j
[2025-09-18 20:19:09] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -43.587689-0.002431j
[2025-09-18 20:19:22] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -43.594529-0.002868j
[2025-09-18 20:19:34] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -43.588754-0.001035j
[2025-09-18 20:19:47] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -43.599350+0.001598j
[2025-09-18 20:19:59] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -43.598740+0.000745j
[2025-09-18 20:20:11] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -43.593598+0.001138j
[2025-09-18 20:20:24] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -43.597243-0.000192j
[2025-09-18 20:20:24] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-18 20:20:36] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -43.595344-0.003127j
[2025-09-18 20:20:49] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -43.597135-0.000118j
[2025-09-18 20:21:01] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -43.595514-0.000267j
[2025-09-18 20:21:14] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -43.608370-0.004409j
[2025-09-18 20:21:26] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -43.594583-0.000726j
[2025-09-18 20:21:38] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -43.581426+0.000693j
[2025-09-18 20:21:51] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -43.594718-0.003057j
[2025-09-18 20:22:03] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -43.591301-0.003405j
[2025-09-18 20:22:16] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -43.592593-0.001076j
[2025-09-18 20:22:28] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -43.593647+0.000022j
[2025-09-18 20:22:41] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -43.591336-0.000611j
[2025-09-18 20:22:53] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -43.602440+0.001872j
[2025-09-18 20:23:06] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -43.600779+0.000743j
[2025-09-18 20:23:18] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -43.615968-0.001298j
[2025-09-18 20:23:30] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -43.598924+0.002940j
[2025-09-18 20:23:43] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -43.591236-0.001526j
[2025-09-18 20:23:55] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -43.590791-0.001289j
[2025-09-18 20:24:08] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -43.603393+0.000697j
[2025-09-18 20:24:20] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -43.580770+0.002274j
[2025-09-18 20:24:33] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -43.581175-0.000584j
[2025-09-18 20:24:45] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -43.586007+0.005757j
[2025-09-18 20:24:57] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -43.584240+0.002460j
[2025-09-18 20:25:10] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -43.582250-0.003730j
[2025-09-18 20:25:22] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -43.598139+0.000461j
[2025-09-18 20:25:35] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -43.605339-0.004224j
[2025-09-18 20:25:47] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -43.580665+0.000572j
[2025-09-18 20:25:59] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -43.589924+0.002253j
[2025-09-18 20:26:12] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -43.601132-0.014779j
[2025-09-18 20:26:24] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -43.586309-0.000906j
[2025-09-18 20:26:37] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -43.582112-0.001674j
[2025-09-18 20:26:49] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -43.584979+0.003739j
[2025-09-18 20:27:02] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -43.587420-0.000650j
[2025-09-18 20:27:14] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -43.597974+0.003045j
[2025-09-18 20:27:27] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -43.595747+0.014336j
[2025-09-18 20:27:39] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -43.594700+0.002752j
[2025-09-18 20:27:51] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -43.581719+0.004482j
[2025-09-18 20:28:04] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -43.595146-0.001990j
[2025-09-18 20:28:16] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -43.584743-0.001782j
[2025-09-18 20:28:29] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -43.601585-0.000801j
[2025-09-18 20:28:41] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -43.599074-0.000205j
[2025-09-18 20:28:54] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -43.601964-0.002096j
[2025-09-18 20:29:06] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -43.603189-0.005967j
[2025-09-18 20:29:19] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -43.584650-0.000140j
[2025-09-18 20:29:31] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -43.594170-0.002863j
[2025-09-18 20:29:43] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -43.600289-0.000234j
[2025-09-18 20:29:56] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -43.608754+0.003472j
[2025-09-18 20:30:08] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -43.595035-0.004400j
[2025-09-18 20:30:21] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -43.597713-0.000412j
[2025-09-18 20:30:33] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -43.593026-0.002019j
[2025-09-18 20:30:46] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -43.581640-0.000441j
[2025-09-18 20:30:58] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -43.600525-0.000313j
[2025-09-18 20:31:11] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -43.586600-0.001270j
[2025-09-18 20:31:23] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -43.579173+0.003897j
[2025-09-18 20:31:36] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -43.597359+0.000025j
[2025-09-18 20:31:48] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -43.600727-0.001923j
[2025-09-18 20:32:00] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -43.590635+0.000536j
[2025-09-18 20:32:13] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -43.612748+0.000328j
[2025-09-18 20:32:25] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -43.588840+0.002320j
[2025-09-18 20:32:38] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -43.599996+0.000872j
[2025-09-18 20:32:50] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -43.587233+0.002350j
[2025-09-18 20:33:03] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -43.608198+0.002061j
[2025-09-18 20:33:15] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -43.600942-0.001288j
[2025-09-18 20:33:28] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -43.587847+0.003246j
[2025-09-18 20:33:40] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -43.601689-0.001197j
[2025-09-18 20:33:52] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -43.600024+0.003007j
[2025-09-18 20:34:05] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -43.602079-0.004302j
[2025-09-18 20:34:17] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -43.604847+0.000440j
[2025-09-18 20:34:30] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -43.588709+0.001566j
[2025-09-18 20:34:42] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -43.599728-0.002509j
[2025-09-18 20:34:55] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -43.591810-0.001539j
[2025-09-18 20:35:07] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -43.585580-0.001658j
[2025-09-18 20:35:20] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -43.587210-0.002485j
[2025-09-18 20:35:32] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -43.589000-0.001801j
[2025-09-18 20:35:45] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -43.591566-0.010955j
[2025-09-18 20:35:57] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -43.598071-0.003670j
[2025-09-18 20:36:09] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -43.605659-0.002428j
[2025-09-18 20:36:22] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -43.598168+0.000239j
[2025-09-18 20:36:34] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -43.595477-0.004389j
[2025-09-18 20:36:47] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -43.591098-0.001331j
[2025-09-18 20:36:59] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -43.594088-0.001399j
[2025-09-18 20:37:12] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -43.602006-0.003455j
[2025-09-18 20:37:24] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -43.605528-0.001124j
[2025-09-18 20:37:36] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -43.599468-0.001218j
[2025-09-18 20:37:49] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -43.602296-0.001092j
[2025-09-18 20:38:01] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -43.600328-0.000586j
[2025-09-18 20:38:14] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -43.580558+0.001816j
[2025-09-18 20:38:26] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -43.591545+0.006500j
[2025-09-18 20:38:38] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -43.603401+0.000937j
[2025-09-18 20:38:51] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -43.599376-0.000786j
[2025-09-18 20:39:03] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -43.588500-0.000345j
[2025-09-18 20:39:16] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -43.602715+0.002084j
[2025-09-18 20:39:28] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -43.599853+0.001990j
[2025-09-18 20:39:41] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -43.587569-0.001517j
[2025-09-18 20:39:53] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -43.595095-0.001184j
[2025-09-18 20:40:05] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -43.577806-0.000780j
[2025-09-18 20:40:18] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -43.580854-0.002675j
[2025-09-18 20:40:30] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -43.603563-0.000545j
[2025-09-18 20:40:43] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -43.605510+0.000037j
[2025-09-18 20:40:55] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -43.598776+0.003664j
[2025-09-18 20:41:08] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -43.604274+0.005674j
[2025-09-18 20:41:20] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -43.587863+0.003209j
[2025-09-18 20:41:32] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -43.594484+0.001165j
[2025-09-18 20:41:45] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -43.591107-0.000894j
[2025-09-18 20:41:57] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -43.601369+0.001082j
[2025-09-18 20:42:10] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -43.593874-0.000737j
[2025-09-18 20:42:10] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-18 20:42:22] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -43.602894+0.005980j
[2025-09-18 20:42:35] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -43.596499-0.003252j
[2025-09-18 20:42:47] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -43.583589-0.000999j
[2025-09-18 20:43:00] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -43.596739+0.005417j
[2025-09-18 20:43:12] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -43.612546+0.002515j
[2025-09-18 20:43:25] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -43.599642+0.004266j
[2025-09-18 20:43:37] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -43.589864-0.001173j
[2025-09-18 20:43:49] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -43.604582+0.001707j
[2025-09-18 20:44:02] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -43.598252+0.002081j
[2025-09-18 20:44:14] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -43.592926+0.001836j
[2025-09-18 20:44:27] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -43.580664+0.004062j
[2025-09-18 20:44:39] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -43.594025-0.002791j
[2025-09-18 20:44:51] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -43.602994-0.003180j
[2025-09-18 20:45:04] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -43.598203-0.002623j
[2025-09-18 20:45:16] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -43.588626-0.001254j
[2025-09-18 20:45:29] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -43.582059-0.001330j
[2025-09-18 20:45:41] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -43.596324+0.006392j
[2025-09-18 20:45:54] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -43.599105-0.001460j
[2025-09-18 20:46:06] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -43.601695-0.000416j
[2025-09-18 20:46:19] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -43.607198+0.000841j
[2025-09-18 20:46:31] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -43.591617-0.002043j
[2025-09-18 20:46:43] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -43.603823-0.000389j
[2025-09-18 20:46:56] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -43.584943-0.002431j
[2025-09-18 20:47:08] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -43.589161-0.000326j
[2025-09-18 20:47:21] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -43.605003+0.002834j
[2025-09-18 20:47:33] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -43.604350+0.001917j
[2025-09-18 20:47:46] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -43.599165-0.000169j
[2025-09-18 20:47:58] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -43.592161-0.001075j
[2025-09-18 20:48:10] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -43.587786-0.002081j
[2025-09-18 20:48:23] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -43.584729+0.000218j
[2025-09-18 20:48:35] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -43.602853-0.009235j
[2025-09-18 20:48:48] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -43.599485-0.001501j
[2025-09-18 20:49:00] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -43.596679+0.000499j
[2025-09-18 20:49:13] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -43.589495-0.002021j
[2025-09-18 20:49:25] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -43.583776+0.001254j
[2025-09-18 20:49:37] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -43.603433-0.000798j
[2025-09-18 20:49:50] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -43.592785+0.000075j
[2025-09-18 20:50:02] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -43.609738-0.002288j
[2025-09-18 20:50:15] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -43.573862+0.000251j
[2025-09-18 20:50:27] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -43.599613+0.000763j
[2025-09-18 20:50:40] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -43.605700-0.002606j
[2025-09-18 20:50:52] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -43.582827-0.001840j
[2025-09-18 20:51:05] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -43.599033-0.004931j
[2025-09-18 20:51:17] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -43.606213-0.001199j
[2025-09-18 20:51:30] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -43.613664-0.000868j
[2025-09-18 20:51:42] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -43.592377-0.001913j
[2025-09-18 20:51:54] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -43.589607-0.001378j
[2025-09-18 20:52:07] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -43.595073+0.000985j
[2025-09-18 20:52:19] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -43.587558+0.000427j
[2025-09-18 20:52:32] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -43.605541+0.003288j
[2025-09-18 20:52:44] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -43.600316-0.000083j
[2025-09-18 20:52:56] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -43.599356+0.001659j
[2025-09-18 20:53:09] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -43.588550+0.006173j
[2025-09-18 20:53:21] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -43.593472+0.000813j
[2025-09-18 20:53:34] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -43.597234-0.002224j
[2025-09-18 20:53:46] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -43.596389-0.001760j
[2025-09-18 20:53:59] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -43.588432-0.001167j
[2025-09-18 20:54:11] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -43.582197+0.001274j
[2025-09-18 20:54:23] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -43.596695+0.003100j
[2025-09-18 20:54:36] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -43.599493-0.000832j
[2025-09-18 20:54:48] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -43.593691+0.000706j
[2025-09-18 20:55:01] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -43.593660+0.002768j
[2025-09-18 20:55:13] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -43.600996-0.001492j
[2025-09-18 20:55:26] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -43.592289-0.002028j
[2025-09-18 20:55:38] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -43.580352-0.002319j
[2025-09-18 20:55:51] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -43.586456+0.004474j
[2025-09-18 20:56:03] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -43.596562-0.004449j
[2025-09-18 20:56:15] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -43.594685+0.004677j
[2025-09-18 20:56:28] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -43.578154-0.002716j
[2025-09-18 20:56:40] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -43.579416+0.002700j
[2025-09-18 20:56:53] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -43.596410-0.000031j
[2025-09-18 20:57:05] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -43.564367+0.001299j
[2025-09-18 20:57:18] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -43.598573+0.000094j
[2025-09-18 20:57:30] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -43.592186-0.001120j
[2025-09-18 20:57:43] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -43.608399+0.002765j
[2025-09-18 20:57:55] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -43.590458+0.000813j
[2025-09-18 20:58:07] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -43.591307-0.000624j
[2025-09-18 20:58:20] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -43.609197+0.002999j
[2025-09-18 20:58:32] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -43.590112-0.000034j
[2025-09-18 20:58:45] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -43.594863-0.001987j
[2025-09-18 20:58:57] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -43.592101+0.000919j
[2025-09-18 20:59:09] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -43.602235+0.000445j
[2025-09-18 20:59:22] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -43.593689-0.003499j
[2025-09-18 20:59:34] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -43.605029+0.000089j
[2025-09-18 20:59:47] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -43.585949-0.000375j
[2025-09-18 20:59:59] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -43.609916-0.001916j
[2025-09-18 21:00:12] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -43.598630-0.001894j
[2025-09-18 21:00:24] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -43.602873-0.000153j
[2025-09-18 21:00:37] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -43.603044+0.005136j
[2025-09-18 21:00:49] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -43.601321-0.000825j
[2025-09-18 21:01:02] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -43.593493+0.001327j
[2025-09-18 21:01:14] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -43.592845-0.001335j
[2025-09-18 21:01:26] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -43.599963-0.000984j
[2025-09-18 21:01:39] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -43.604884+0.000499j
[2025-09-18 21:01:51] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -43.605025+0.003416j
[2025-09-18 21:02:04] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -43.586394+0.003100j
[2025-09-18 21:02:16] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -43.597613-0.004448j
[2025-09-18 21:02:29] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -43.592850+0.000267j
[2025-09-18 21:02:41] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -43.609761-0.003934j
[2025-09-18 21:02:53] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -43.582277+0.000399j
[2025-09-18 21:03:06] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -43.585195+0.002518j
[2025-09-18 21:03:18] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -43.595376-0.001786j
[2025-09-18 21:03:31] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -43.592521-0.002182j
[2025-09-18 21:03:43] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -43.583181-0.003037j
[2025-09-18 21:03:56] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -43.599974-0.001539j
[2025-09-18 21:03:56] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-18 21:04:08] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -43.591246+0.000504j
[2025-09-18 21:04:21] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -43.583181+0.001339j
[2025-09-18 21:04:33] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -43.591099+0.001250j
[2025-09-18 21:04:45] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -43.589333-0.000225j
[2025-09-18 21:04:58] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -43.612486+0.000060j
[2025-09-18 21:05:10] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -43.610683+0.001450j
[2025-09-18 21:05:23] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -43.601655+0.001368j
[2025-09-18 21:05:35] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -43.599398-0.001261j
[2025-09-18 21:05:48] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -43.604551+0.001015j
[2025-09-18 21:06:00] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -43.592907+0.003201j
[2025-09-18 21:06:12] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -43.586940-0.000064j
[2025-09-18 21:06:25] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -43.592042+0.000151j
[2025-09-18 21:06:37] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -43.598269+0.006980j
[2025-09-18 21:06:50] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -43.597367-0.000868j
[2025-09-18 21:07:02] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -43.577370+0.001646j
[2025-09-18 21:07:15] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -43.600736-0.001192j
[2025-09-18 21:07:27] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -43.608744+0.003048j
[2025-09-18 21:07:39] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -43.603491+0.000105j
[2025-09-18 21:07:52] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -43.601899-0.001378j
[2025-09-18 21:08:04] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -43.584251+0.000834j
[2025-09-18 21:08:17] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -43.595115+0.004361j
[2025-09-18 21:08:29] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -43.594912-0.000264j
[2025-09-18 21:08:42] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -43.585207+0.003334j
[2025-09-18 21:08:54] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -43.589096-0.000592j
[2025-09-18 21:09:06] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -43.591151-0.000491j
[2025-09-18 21:09:19] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -43.578793+0.000926j
[2025-09-18 21:09:31] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -43.591499+0.000127j
[2025-09-18 21:09:44] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -43.585625-0.000766j
[2025-09-18 21:09:56] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -43.589918+0.002415j
[2025-09-18 21:10:08] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -43.591131-0.001349j
[2025-09-18 21:10:21] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -43.579845+0.001834j
[2025-09-18 21:10:33] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -43.591167+0.010106j
[2025-09-18 21:10:46] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -43.603883-0.001632j
[2025-09-18 21:10:58] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -43.596243+0.000273j
[2025-09-18 21:11:11] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -43.599265+0.001592j
[2025-09-18 21:11:23] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -43.590968-0.002167j
[2025-09-18 21:11:35] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -43.606541-0.003224j
[2025-09-18 21:11:48] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -43.602942+0.001616j
[2025-09-18 21:12:00] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -43.585886-0.000128j
[2025-09-18 21:12:13] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -43.584778-0.001116j
[2025-09-18 21:12:25] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -43.618850+0.001201j
[2025-09-18 21:12:38] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -43.591728+0.000695j
[2025-09-18 21:12:50] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -43.582643+0.001751j
[2025-09-18 21:13:02] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -43.601730+0.003513j
[2025-09-18 21:13:15] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -43.592983+0.001037j
[2025-09-18 21:13:27] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -43.586551-0.001596j
[2025-09-18 21:13:40] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -43.584003-0.001372j
[2025-09-18 21:13:52] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -43.581845-0.003903j
[2025-09-18 21:14:05] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -43.600293-0.003533j
[2025-09-18 21:14:17] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -43.611068-0.000147j
[2025-09-18 21:14:30] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -43.594815-0.000933j
[2025-09-18 21:14:42] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -43.604842+0.000437j
[2025-09-18 21:14:54] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -43.599132+0.000948j
[2025-09-18 21:15:07] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -43.589644+0.003472j
[2025-09-18 21:15:19] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -43.594285+0.000848j
[2025-09-18 21:15:32] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -43.590434+0.001098j
[2025-09-18 21:15:44] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -43.602183+0.000525j
[2025-09-18 21:15:56] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -43.608973-0.000160j
[2025-09-18 21:16:08] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -43.591087+0.001060j
[2025-09-18 21:16:21] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -43.593413+0.001599j
[2025-09-18 21:16:33] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -43.590987+0.000594j
[2025-09-18 21:16:45] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -43.584629+0.001440j
[2025-09-18 21:16:58] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -43.603070+0.006712j
[2025-09-18 21:17:10] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -43.599467-0.001354j
[2025-09-18 21:17:23] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -43.586549-0.006451j
[2025-09-18 21:17:35] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -43.603360+0.000389j
[2025-09-18 21:17:47] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -43.589688-0.000818j
[2025-09-18 21:18:00] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -43.600206-0.000854j
[2025-09-18 21:18:12] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -43.584852-0.000388j
[2025-09-18 21:18:25] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -43.595072+0.001550j
[2025-09-18 21:18:37] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -43.572819-0.003220j
[2025-09-18 21:18:49] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -43.603062-0.000474j
[2025-09-18 21:19:02] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -43.585616+0.002642j
[2025-09-18 21:19:14] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -43.594307-0.001309j
[2025-09-18 21:19:27] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -43.587725-0.000233j
[2025-09-18 21:19:39] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -43.597751+0.003408j
[2025-09-18 21:19:52] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -43.590419+0.000295j
[2025-09-18 21:20:04] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -43.603285+0.001378j
[2025-09-18 21:20:17] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -43.600404+0.001404j
[2025-09-18 21:20:29] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -43.606553-0.003286j
[2025-09-18 21:20:41] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -43.600496+0.006440j
[2025-09-18 21:20:54] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -43.609544+0.002093j
[2025-09-18 21:21:06] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -43.595093-0.005762j
[2025-09-18 21:21:19] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -43.596775+0.000701j
[2025-09-18 21:21:31] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -43.602061+0.000740j
[2025-09-18 21:21:44] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -43.591928+0.001520j
[2025-09-18 21:21:56] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -43.586247-0.002615j
[2025-09-18 21:22:09] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -43.594242+0.002296j
[2025-09-18 21:22:21] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -43.594358+0.003015j
[2025-09-18 21:22:33] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -43.604206+0.001168j
[2025-09-18 21:22:46] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -43.584268+0.003084j
[2025-09-18 21:22:58] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -43.614517+0.000194j
[2025-09-18 21:23:11] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -43.604468-0.000995j
[2025-09-18 21:23:23] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -43.600477-0.000567j
[2025-09-18 21:23:36] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -43.595466+0.004032j
[2025-09-18 21:23:48] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -43.584552+0.002664j
[2025-09-18 21:24:01] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -43.586632+0.006629j
[2025-09-18 21:24:13] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -43.592444-0.000055j
[2025-09-18 21:24:20] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -43.592679+0.000075j
[2025-09-18 21:24:26] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -43.602411-0.004104j
[2025-09-18 21:24:31] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -43.597634-0.001541j
[2025-09-18 21:24:37] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -43.603623+0.004340j
[2025-09-18 21:24:43] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -43.586420+0.000478j
[2025-09-18 21:24:48] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -43.590395-0.001267j
[2025-09-18 21:24:54] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -43.581312+0.000932j
[2025-09-18 21:24:54] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-18 21:24:54] ✅ Training completed | Restarts: 2
[2025-09-18 21:24:54] ============================================================
[2025-09-18 21:24:54] Training completed | Runtime: 13088.7s
[2025-09-18 21:24:56] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-18 21:24:56] ============================================================
