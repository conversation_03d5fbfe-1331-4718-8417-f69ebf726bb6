[2025-09-18 14:06:01] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.80/training/checkpoints/final_GCNN.pkl
[2025-09-18 14:06:01]   - 迭代次数: final
[2025-09-18 14:06:01]   - 能量: -44.855045+0.001618j ± 0.006447
[2025-09-18 14:06:01]   - 时间戳: 2025-09-17T23:35:56.985143+08:00
[2025-09-18 14:06:24] ✓ 变分状态参数已从checkpoint恢复
[2025-09-18 14:06:24] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-18 14:06:24] ==================================================
[2025-09-18 14:06:24] GCNN for Shastry-Sutherland Model
[2025-09-18 14:06:24] ==================================================
[2025-09-18 14:06:24] System parameters:
[2025-09-18 14:06:24]   - System size: L=5, N=100
[2025-09-18 14:06:24]   - System parameters: J1=0.79, J2=1.0, Q=0.0
[2025-09-18 14:06:24] --------------------------------------------------
[2025-09-18 14:06:24] Model parameters:
[2025-09-18 14:06:24]   - Number of layers = 4
[2025-09-18 14:06:24]   - Number of features = 4
[2025-09-18 14:06:24]   - Total parameters = 19628
[2025-09-18 14:06:24] --------------------------------------------------
[2025-09-18 14:06:24] Training parameters:
[2025-09-18 14:06:24]   - Learning rate: 0.015
[2025-09-18 14:06:24]   - Total iterations: 1050
[2025-09-18 14:06:24]   - Annealing cycles: 3
[2025-09-18 14:06:24]   - Initial period: 150
[2025-09-18 14:06:24]   - Period multiplier: 2.0
[2025-09-18 14:06:24]   - Temperature range: 0.0-1.0
[2025-09-18 14:06:24]   - Samples: 4096
[2025-09-18 14:06:24]   - Discarded samples: 0
[2025-09-18 14:06:24]   - Chunk size: 2048
[2025-09-18 14:06:24]   - Diagonal shift: 0.2
[2025-09-18 14:06:24]   - Gradient clipping: 1.0
[2025-09-18 14:06:24]   - Checkpoint enabled: interval=105
[2025-09-18 14:06:24]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.79/training/checkpoints
[2025-09-18 14:06:24] --------------------------------------------------
[2025-09-18 14:06:24] Device status:
[2025-09-18 14:06:24]   - Devices model: NVIDIA H200 NVL
[2025-09-18 14:06:24]   - Number of devices: 1
[2025-09-18 14:06:24]   - Sharding: True
[2025-09-18 14:06:24] ============================================================
[2025-09-18 14:07:31] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -44.197636+0.010382j
[2025-09-18 14:08:14] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -44.217216+0.003295j
[2025-09-18 14:08:26] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -44.207885-0.006108j
[2025-09-18 14:08:39] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -44.206021-0.001562j
[2025-09-18 14:08:51] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -44.205772-0.006121j
[2025-09-18 14:09:04] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -44.215039+0.002613j
[2025-09-18 14:09:16] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -44.205061+0.000023j
[2025-09-18 14:09:29] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -44.200221-0.000682j
[2025-09-18 14:09:41] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -44.212668-0.002043j
[2025-09-18 14:09:54] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -44.210802-0.000943j
[2025-09-18 14:10:06] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -44.215321+0.007112j
[2025-09-18 14:10:19] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -44.224730+0.000308j
[2025-09-18 14:10:31] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -44.211289-0.000911j
[2025-09-18 14:10:44] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -44.208412-0.006094j
[2025-09-18 14:10:56] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -44.212257-0.001723j
[2025-09-18 14:11:09] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -44.221610-0.003203j
[2025-09-18 14:11:21] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -44.197596-0.002996j
[2025-09-18 14:11:34] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -44.226021-0.002695j
[2025-09-18 14:11:46] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -44.201060-0.002092j
[2025-09-18 14:11:59] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -44.217782+0.001544j
[2025-09-18 14:12:11] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -44.216995-0.001651j
[2025-09-18 14:12:24] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -44.223423-0.002183j
[2025-09-18 14:12:36] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -44.217642-0.001018j
[2025-09-18 14:12:49] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -44.226937+0.000158j
[2025-09-18 14:13:01] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -44.224881-0.002647j
[2025-09-18 14:13:14] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -44.219866+0.002262j
[2025-09-18 14:13:26] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -44.206590-0.001015j
[2025-09-18 14:13:39] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -44.210375-0.000667j
[2025-09-18 14:13:51] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -44.208811-0.002401j
[2025-09-18 14:14:04] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -44.220461+0.001151j
[2025-09-18 14:14:16] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -44.213224+0.000375j
[2025-09-18 14:14:29] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -44.220701+0.001558j
[2025-09-18 14:14:41] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -44.203265-0.001051j
[2025-09-18 14:14:54] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -44.215239+0.000084j
[2025-09-18 14:15:06] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -44.217999-0.000024j
[2025-09-18 14:15:19] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -44.197090+0.003937j
[2025-09-18 14:15:31] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -44.216986+0.003659j
[2025-09-18 14:15:43] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -44.205574+0.006112j
[2025-09-18 14:15:56] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -44.220208+0.001579j
[2025-09-18 14:16:09] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -44.221382-0.001794j
[2025-09-18 14:16:21] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -44.198406+0.004799j
[2025-09-18 14:16:33] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -44.203171+0.002293j
[2025-09-18 14:16:46] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -44.218107-0.001138j
[2025-09-18 14:16:58] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -44.223830+0.000564j
[2025-09-18 14:17:11] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -44.203677+0.000360j
[2025-09-18 14:17:23] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -44.202797-0.001164j
[2025-09-18 14:17:36] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -44.205763-0.002630j
[2025-09-18 14:17:48] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -44.212082+0.005669j
[2025-09-18 14:18:00] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -44.208293+0.002303j
[2025-09-18 14:18:13] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -44.211950+0.001986j
[2025-09-18 14:18:25] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -44.212144+0.003025j
[2025-09-18 14:18:37] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -44.219197+0.001086j
[2025-09-18 14:18:50] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -44.219628-0.000147j
[2025-09-18 14:19:02] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -44.208255-0.003053j
[2025-09-18 14:19:15] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -44.203981+0.000051j
[2025-09-18 14:19:27] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -44.204062+0.002696j
[2025-09-18 14:19:39] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -44.205370+0.000584j
[2025-09-18 14:19:52] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -44.197749-0.000535j
[2025-09-18 14:20:04] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -44.195356+0.000759j
[2025-09-18 14:20:16] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -44.228123+0.002974j
[2025-09-18 14:20:29] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -44.204512+0.001145j
[2025-09-18 14:20:41] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -44.208155-0.002755j
[2025-09-18 14:20:54] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -44.202168+0.002267j
[2025-09-18 14:21:06] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -44.204252-0.001590j
[2025-09-18 14:21:18] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -44.207908-0.000880j
[2025-09-18 14:21:31] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -44.192595+0.001477j
[2025-09-18 14:21:43] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -44.207292-0.001095j
[2025-09-18 14:21:56] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -44.221663-0.001925j
[2025-09-18 14:22:08] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -44.214676+0.007332j
[2025-09-18 14:22:20] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -44.212603+0.000058j
[2025-09-18 14:22:33] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -44.214740+0.006035j
[2025-09-18 14:22:45] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -44.200183+0.002045j
[2025-09-18 14:22:57] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -44.210993-0.002014j
[2025-09-18 14:23:10] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -44.217597-0.000316j
[2025-09-18 14:23:22] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -44.210932-0.001060j
[2025-09-18 14:23:35] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -44.215681-0.000174j
[2025-09-18 14:23:47] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -44.214767+0.003780j
[2025-09-18 14:23:59] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -44.224893-0.000949j
[2025-09-18 14:24:12] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -44.208069-0.000251j
[2025-09-18 14:24:24] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -44.215615+0.007140j
[2025-09-18 14:24:36] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -44.217902-0.002273j
[2025-09-18 14:24:49] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -44.214834-0.001334j
[2025-09-18 14:25:01] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -44.219027-0.001132j
[2025-09-18 14:25:14] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -44.218327+0.000420j
[2025-09-18 14:25:26] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -44.216206+0.002066j
[2025-09-18 14:25:38] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -44.214893+0.002318j
[2025-09-18 14:25:51] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -44.209103-0.002084j
[2025-09-18 14:26:03] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -44.205460+0.000187j
[2025-09-18 14:26:15] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -44.219416+0.002706j
[2025-09-18 14:26:28] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -44.225121-0.000891j
[2025-09-18 14:26:40] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -44.221637+0.000994j
[2025-09-18 14:26:53] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -44.224923+0.004431j
[2025-09-18 14:27:05] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -44.210819-0.003194j
[2025-09-18 14:27:17] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -44.228361-0.001163j
[2025-09-18 14:27:30] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -44.213617+0.002098j
[2025-09-18 14:27:42] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -44.210959-0.000857j
[2025-09-18 14:27:54] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -44.211713-0.001199j
[2025-09-18 14:28:07] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -44.228564-0.000363j
[2025-09-18 14:28:19] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -44.225996+0.000251j
[2025-09-18 14:28:32] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -44.201470+0.002163j
[2025-09-18 14:28:44] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -44.211467+0.003233j
[2025-09-18 14:28:56] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -44.203472-0.000775j
[2025-09-18 14:29:09] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -44.212071+0.000451j
[2025-09-18 14:29:21] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -44.214920-0.001751j
[2025-09-18 14:29:34] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -44.204515+0.002141j
[2025-09-18 14:29:34] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-18 14:29:46] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -44.212699+0.001618j
[2025-09-18 14:29:58] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -44.207907-0.001948j
[2025-09-18 14:30:11] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -44.199800+0.003292j
[2025-09-18 14:30:23] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -44.217859-0.001318j
[2025-09-18 14:30:35] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -44.210964-0.003615j
[2025-09-18 14:30:48] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -44.215795-0.000332j
[2025-09-18 14:31:00] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -44.209984-0.001789j
[2025-09-18 14:31:13] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -44.224764+0.000736j
[2025-09-18 14:31:25] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -44.220995+0.000925j
[2025-09-18 14:31:37] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -44.212150+0.000919j
[2025-09-18 14:31:50] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -44.205010-0.000902j
[2025-09-18 14:32:02] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -44.207967-0.001091j
[2025-09-18 14:32:14] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -44.205626+0.000626j
[2025-09-18 14:32:27] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -44.209462-0.001159j
[2025-09-18 14:32:39] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -44.204383+0.002461j
[2025-09-18 14:32:52] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -44.224231-0.005109j
[2025-09-18 14:33:04] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -44.201633+0.001020j
[2025-09-18 14:33:16] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -44.215094-0.003441j
[2025-09-18 14:33:29] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -44.207450-0.000469j
[2025-09-18 14:33:41] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -44.203964-0.004133j
[2025-09-18 14:33:54] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -44.209395+0.001686j
[2025-09-18 14:34:06] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -44.212112+0.002473j
[2025-09-18 14:34:18] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -44.210901-0.000652j
[2025-09-18 14:34:31] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -44.204328+0.000473j
[2025-09-18 14:34:43] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -44.205134-0.000305j
[2025-09-18 14:34:55] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -44.214294+0.001313j
[2025-09-18 14:35:08] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -44.204594-0.001000j
[2025-09-18 14:35:20] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -44.213983+0.001747j
[2025-09-18 14:35:33] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -44.222669+0.000865j
[2025-09-18 14:35:45] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -44.220470-0.000822j
[2025-09-18 14:35:57] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -44.219102+0.004204j
[2025-09-18 14:36:10] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -44.190449-0.001313j
[2025-09-18 14:36:22] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -44.207790+0.004438j
[2025-09-18 14:36:35] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -44.207392-0.006837j
[2025-09-18 14:36:47] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -44.209868+0.002065j
[2025-09-18 14:36:59] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -44.216291-0.001730j
[2025-09-18 14:37:12] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -44.209092+0.000574j
[2025-09-18 14:37:24] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -44.211395+0.002157j
[2025-09-18 14:37:37] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -44.209402+0.002548j
[2025-09-18 14:37:49] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -44.216065-0.002936j
[2025-09-18 14:38:01] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -44.227198+0.000729j
[2025-09-18 14:38:14] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -44.205814+0.000368j
[2025-09-18 14:38:26] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -44.202663+0.003817j
[2025-09-18 14:38:38] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -44.222197+0.000735j
[2025-09-18 14:38:51] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -44.208990-0.003265j
[2025-09-18 14:38:51] RESTART #1 | Period: 300
[2025-09-18 14:39:03] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -44.199548+0.000541j
[2025-09-18 14:39:16] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -44.216658-0.003186j
[2025-09-18 14:39:28] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -44.217233-0.003762j
[2025-09-18 14:39:40] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -44.219663-0.001257j
[2025-09-18 14:39:53] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -44.211470+0.000200j
[2025-09-18 14:40:05] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -44.213913-0.003615j
[2025-09-18 14:40:18] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -44.216827-0.001952j
[2025-09-18 14:40:30] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -44.209419-0.001373j
[2025-09-18 14:40:42] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -44.212902-0.000893j
[2025-09-18 14:40:55] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -44.216109+0.001727j
[2025-09-18 14:41:07] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -44.210519-0.007717j
[2025-09-18 14:41:20] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -44.208750-0.001838j
[2025-09-18 14:41:32] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -44.202478-0.001518j
[2025-09-18 14:41:44] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -44.214719-0.000120j
[2025-09-18 14:41:57] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -44.221350-0.000602j
[2025-09-18 14:42:09] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -44.199156-0.003753j
[2025-09-18 14:42:22] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -44.215196+0.001136j
[2025-09-18 14:42:34] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -44.217347+0.000457j
[2025-09-18 14:42:46] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -44.206105-0.002523j
[2025-09-18 14:42:59] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -44.220110-0.002173j
[2025-09-18 14:43:11] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -44.222190-0.002904j
[2025-09-18 14:43:24] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -44.213879+0.000948j
[2025-09-18 14:43:36] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -44.204009+0.000984j
[2025-09-18 14:43:48] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -44.216906+0.002351j
[2025-09-18 14:44:01] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -44.208342+0.004284j
[2025-09-18 14:44:13] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -44.216671-0.001033j
[2025-09-18 14:44:26] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -44.208788-0.002605j
[2025-09-18 14:44:38] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -44.217258+0.002448j
[2025-09-18 14:44:50] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -44.215368+0.001011j
[2025-09-18 14:45:03] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -44.215538+0.001621j
[2025-09-18 14:45:15] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -44.215386+0.000550j
[2025-09-18 14:45:28] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -44.229850+0.002323j
[2025-09-18 14:45:40] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -44.224894+0.003001j
[2025-09-18 14:45:52] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -44.227621-0.000228j
[2025-09-18 14:46:05] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -44.217470+0.002003j
[2025-09-18 14:46:17] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -44.227012-0.001737j
[2025-09-18 14:46:30] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -44.219159+0.000603j
[2025-09-18 14:46:42] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -44.220399+0.003018j
[2025-09-18 14:46:54] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -44.221909-0.001248j
[2025-09-18 14:47:07] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -44.218267+0.004051j
[2025-09-18 14:47:19] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -44.223285-0.001980j
[2025-09-18 14:47:31] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -44.205748+0.000567j
[2025-09-18 14:47:44] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -44.228137-0.001546j
[2025-09-18 14:47:56] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -44.212362-0.001754j
[2025-09-18 14:48:09] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -44.205284+0.002317j
[2025-09-18 14:48:21] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -44.215621+0.004687j
[2025-09-18 14:48:33] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -44.224277-0.002284j
[2025-09-18 14:48:46] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -44.212867+0.000684j
[2025-09-18 14:48:58] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -44.221217+0.000791j
[2025-09-18 14:49:11] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -44.214217+0.001090j
[2025-09-18 14:49:23] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -44.219735-0.000699j
[2025-09-18 14:49:35] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -44.224029+0.005732j
[2025-09-18 14:49:48] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -44.206601-0.001248j
[2025-09-18 14:50:00] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -44.210616+0.000342j
[2025-09-18 14:50:12] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -44.235326+0.000095j
[2025-09-18 14:50:25] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -44.218497+0.000213j
[2025-09-18 14:50:37] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -44.214084-0.000027j
[2025-09-18 14:50:50] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -44.212421+0.003578j
[2025-09-18 14:51:02] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -44.222730+0.001885j
[2025-09-18 14:51:14] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -44.221198+0.001831j
[2025-09-18 14:51:14] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-18 14:51:27] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -44.204461+0.002306j
[2025-09-18 14:51:39] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -44.219261+0.003548j
[2025-09-18 14:51:51] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -44.218778+0.000155j
[2025-09-18 14:52:04] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -44.227643-0.000557j
[2025-09-18 14:52:16] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -44.221158+0.002950j
[2025-09-18 14:52:29] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -44.218564+0.003554j
[2025-09-18 14:52:41] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -44.211255-0.002099j
[2025-09-18 14:52:54] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -44.217916+0.003680j
[2025-09-18 14:53:06] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -44.211177-0.004929j
[2025-09-18 14:53:19] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -44.215790+0.004488j
[2025-09-18 14:53:31] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -44.223035+0.006465j
[2025-09-18 14:53:43] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -44.224086-0.005098j
[2025-09-18 14:53:56] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -44.208436+0.000922j
[2025-09-18 14:54:08] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -44.217167-0.002343j
[2025-09-18 14:54:21] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -44.220416-0.001193j
[2025-09-18 14:54:33] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -44.208073-0.000566j
[2025-09-18 14:54:45] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -44.204106-0.000691j
[2025-09-18 14:54:58] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -44.231704+0.002454j
[2025-09-18 14:55:10] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -44.210743+0.003824j
[2025-09-18 14:55:22] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -44.206599-0.000765j
[2025-09-18 14:55:35] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -44.208302-0.004216j
[2025-09-18 14:55:47] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -44.206805+0.001761j
[2025-09-18 14:56:00] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -44.217336+0.001623j
[2025-09-18 14:56:12] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -44.220236+0.002183j
[2025-09-18 14:56:24] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -44.192271+0.000679j
[2025-09-18 14:56:37] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -44.226655-0.001798j
[2025-09-18 14:56:49] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -44.210161-0.001538j
[2025-09-18 14:57:01] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -44.215523+0.002662j
[2025-09-18 14:57:14] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -44.230212+0.000520j
[2025-09-18 14:57:26] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -44.203683+0.000740j
[2025-09-18 14:57:39] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -44.216863+0.001239j
[2025-09-18 14:57:51] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -44.221572-0.001286j
[2025-09-18 14:58:03] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -44.224303+0.001488j
[2025-09-18 14:58:16] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -44.210884-0.000764j
[2025-09-18 14:58:28] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -44.208737-0.000317j
[2025-09-18 14:58:41] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -44.214412+0.000276j
[2025-09-18 14:58:53] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -44.205886+0.010912j
[2025-09-18 14:59:06] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -44.219186-0.001030j
[2025-09-18 14:59:18] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -44.224083-0.001406j
[2025-09-18 14:59:31] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -44.216900-0.000967j
[2025-09-18 14:59:43] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -44.209014-0.002353j
[2025-09-18 14:59:55] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -44.208616+0.000719j
[2025-09-18 15:00:08] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -44.209156+0.000570j
[2025-09-18 15:00:20] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -44.212728+0.000464j
[2025-09-18 15:00:33] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -44.206445+0.000180j
[2025-09-18 15:00:45] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -44.220290+0.000307j
[2025-09-18 15:00:58] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -44.210425-0.001687j
[2025-09-18 15:01:10] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -44.207056-0.000056j
[2025-09-18 15:01:23] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -44.208291-0.002678j
[2025-09-18 15:01:35] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -44.213729+0.001940j
[2025-09-18 15:01:48] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -44.213408-0.001561j
[2025-09-18 15:02:00] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -44.220484-0.001821j
[2025-09-18 15:02:13] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -44.218899-0.000707j
[2025-09-18 15:02:25] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -44.227510+0.001461j
[2025-09-18 15:02:37] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -44.219467-0.000806j
[2025-09-18 15:02:50] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -44.212687-0.003009j
[2025-09-18 15:03:02] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -44.232694-0.001227j
[2025-09-18 15:03:15] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -44.234535+0.002128j
[2025-09-18 15:03:27] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -44.222853-0.004738j
[2025-09-18 15:03:40] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -44.212184-0.000833j
[2025-09-18 15:03:52] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -44.210421-0.000956j
[2025-09-18 15:04:05] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -44.210692-0.001144j
[2025-09-18 15:04:17] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -44.228332+0.002009j
[2025-09-18 15:04:30] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -44.221339+0.000890j
[2025-09-18 15:04:42] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -44.205965-0.001165j
[2025-09-18 15:04:55] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -44.206782+0.004551j
[2025-09-18 15:05:07] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -44.203371-0.000393j
[2025-09-18 15:05:20] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -44.217929-0.005354j
[2025-09-18 15:05:32] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -44.203843+0.001938j
[2025-09-18 15:05:45] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -44.209893+0.001419j
[2025-09-18 15:05:57] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -44.220444+0.002561j
[2025-09-18 15:06:09] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -44.223469+0.004829j
[2025-09-18 15:06:22] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -44.219625+0.000505j
[2025-09-18 15:06:34] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -44.220506-0.000766j
[2025-09-18 15:06:47] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -44.227717-0.002569j
[2025-09-18 15:06:59] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -44.225972+0.004319j
[2025-09-18 15:07:12] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -44.221402-0.000481j
[2025-09-18 15:07:24] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -44.214124-0.001313j
[2025-09-18 15:07:37] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -44.217330-0.001327j
[2025-09-18 15:07:49] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -44.227094+0.004236j
[2025-09-18 15:08:02] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -44.202188-0.004540j
[2025-09-18 15:08:14] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -44.206767+0.003060j
[2025-09-18 15:08:26] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -44.207713+0.003947j
[2025-09-18 15:08:39] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -44.213325-0.001204j
[2025-09-18 15:08:51] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -44.218756-0.000128j
[2025-09-18 15:09:04] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -44.205515-0.004147j
[2025-09-18 15:09:16] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -44.209828+0.005003j
[2025-09-18 15:09:29] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -44.208943-0.000237j
[2025-09-18 15:09:41] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -44.225569+0.000792j
[2025-09-18 15:09:54] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -44.209092-0.001383j
[2025-09-18 15:10:06] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -44.220779-0.000267j
[2025-09-18 15:10:18] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -44.223124-0.002191j
[2025-09-18 15:10:31] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -44.219826+0.003667j
[2025-09-18 15:10:43] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -44.210126+0.002729j
[2025-09-18 15:10:56] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -44.211743-0.000727j
[2025-09-18 15:11:08] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -44.207191-0.003035j
[2025-09-18 15:11:21] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -44.218829+0.001703j
[2025-09-18 15:11:33] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -44.207806+0.002769j
[2025-09-18 15:11:46] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -44.210582+0.003724j
[2025-09-18 15:11:58] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -44.221595+0.002852j
[2025-09-18 15:12:10] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -44.208425-0.001685j
[2025-09-18 15:12:23] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -44.227805-0.002781j
[2025-09-18 15:12:35] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -44.233534-0.003622j
[2025-09-18 15:12:48] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -44.224452+0.003325j
[2025-09-18 15:13:00] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -44.214622-0.000765j
[2025-09-18 15:13:00] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-18 15:13:13] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -44.228981+0.001783j
[2025-09-18 15:13:25] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -44.209315-0.005701j
[2025-09-18 15:13:38] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -44.203385-0.000271j
[2025-09-18 15:13:50] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -44.209157+0.000532j
[2025-09-18 15:14:02] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -44.217286+0.003662j
[2025-09-18 15:14:15] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -44.220061-0.002141j
[2025-09-18 15:14:27] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -44.215885-0.003149j
[2025-09-18 15:14:40] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -44.225872-0.000442j
[2025-09-18 15:14:52] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -44.208987-0.001031j
[2025-09-18 15:15:05] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -44.226778+0.003843j
[2025-09-18 15:15:17] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -44.215816+0.000170j
[2025-09-18 15:15:30] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -44.215018-0.001920j
[2025-09-18 15:15:42] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -44.206409-0.001209j
[2025-09-18 15:15:55] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -44.219474+0.000678j
[2025-09-18 15:16:07] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -44.227721+0.001600j
[2025-09-18 15:16:19] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -44.220154+0.000598j
[2025-09-18 15:16:32] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -44.221511-0.000184j
[2025-09-18 15:16:44] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -44.202182+0.001004j
[2025-09-18 15:16:57] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -44.215771+0.001757j
[2025-09-18 15:17:09] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -44.223178-0.000646j
[2025-09-18 15:17:22] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -44.218492-0.000319j
[2025-09-18 15:17:34] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -44.210608+0.002726j
[2025-09-18 15:17:47] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -44.217280-0.002876j
[2025-09-18 15:17:59] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -44.219942-0.001766j
[2025-09-18 15:18:11] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -44.214883+0.003022j
[2025-09-18 15:18:24] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -44.228316-0.000206j
[2025-09-18 15:18:36] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -44.203673-0.000627j
[2025-09-18 15:18:49] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -44.220058-0.000050j
[2025-09-18 15:19:01] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -44.212226-0.000168j
[2025-09-18 15:19:14] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -44.220516+0.000991j
[2025-09-18 15:19:26] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -44.226026+0.000063j
[2025-09-18 15:19:39] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -44.208790+0.000426j
[2025-09-18 15:19:51] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -44.225651-0.000960j
[2025-09-18 15:20:03] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -44.225270+0.001134j
[2025-09-18 15:20:16] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -44.215618-0.000100j
[2025-09-18 15:20:28] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -44.232681+0.001539j
[2025-09-18 15:20:41] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -44.210241+0.001287j
[2025-09-18 15:20:53] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -44.230961-0.008143j
[2025-09-18 15:21:06] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -44.216535-0.001386j
[2025-09-18 15:21:18] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -44.208754+0.002283j
[2025-09-18 15:21:31] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -44.225438+0.000245j
[2025-09-18 15:21:43] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -44.220429+0.001208j
[2025-09-18 15:21:56] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -44.212296+0.003181j
[2025-09-18 15:22:08] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -44.222003-0.004185j
[2025-09-18 15:22:21] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -44.233990-0.000696j
[2025-09-18 15:22:33] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -44.221074+0.000371j
[2025-09-18 15:22:45] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -44.235093-0.000903j
[2025-09-18 15:22:58] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -44.235603+0.000775j
[2025-09-18 15:23:10] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -44.232619+0.000927j
[2025-09-18 15:23:23] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -44.231656-0.000555j
[2025-09-18 15:23:35] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -44.221657+0.001433j
[2025-09-18 15:23:48] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -44.231227-0.003609j
[2025-09-18 15:24:00] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -44.206055-0.000707j
[2025-09-18 15:24:13] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -44.222243+0.001267j
[2025-09-18 15:24:25] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -44.213709-0.000361j
[2025-09-18 15:24:37] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -44.231910-0.000022j
[2025-09-18 15:24:50] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -44.222670-0.000204j
[2025-09-18 15:25:02] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -44.215280+0.003197j
[2025-09-18 15:25:15] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -44.223316+0.001933j
[2025-09-18 15:25:27] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -44.202155+0.004268j
[2025-09-18 15:25:40] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -44.211754+0.000940j
[2025-09-18 15:25:52] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -44.220276-0.005691j
[2025-09-18 15:26:05] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -44.218360+0.002117j
[2025-09-18 15:26:17] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -44.224501+0.000526j
[2025-09-18 15:26:29] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -44.201866-0.005332j
[2025-09-18 15:26:42] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -44.209634-0.000973j
[2025-09-18 15:26:54] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -44.220579+0.000960j
[2025-09-18 15:27:07] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -44.209169-0.002864j
[2025-09-18 15:27:19] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -44.224450+0.005095j
[2025-09-18 15:27:32] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -44.217915-0.001253j
[2025-09-18 15:27:44] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -44.225973-0.000563j
[2025-09-18 15:27:57] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -44.221052-0.002130j
[2025-09-18 15:28:09] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -44.225384+0.001430j
[2025-09-18 15:28:21] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -44.222743-0.003684j
[2025-09-18 15:28:34] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -44.221767-0.007402j
[2025-09-18 15:28:46] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -44.218738+0.005699j
[2025-09-18 15:28:59] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -44.193598-0.001274j
[2025-09-18 15:29:11] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -44.226046-0.001198j
[2025-09-18 15:29:24] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -44.229567-0.004178j
[2025-09-18 15:29:36] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -44.219150-0.003063j
[2025-09-18 15:29:49] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -44.222204+0.001541j
[2025-09-18 15:30:01] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -44.194313-0.002392j
[2025-09-18 15:30:14] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -44.208158-0.002766j
[2025-09-18 15:30:26] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -44.228425-0.006165j
[2025-09-18 15:30:38] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -44.220896+0.000513j
[2025-09-18 15:30:51] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -44.220961+0.001579j
[2025-09-18 15:31:03] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -44.215703-0.000932j
[2025-09-18 15:31:16] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -44.200416+0.002380j
[2025-09-18 15:31:28] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -44.221568+0.002258j
[2025-09-18 15:31:41] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -44.219630+0.001000j
[2025-09-18 15:31:53] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -44.218921-0.000424j
[2025-09-18 15:32:05] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -44.218870+0.001393j
[2025-09-18 15:32:18] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -44.231026+0.003076j
[2025-09-18 15:32:30] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -44.219981-0.000597j
[2025-09-18 15:32:43] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -44.220444-0.003721j
[2025-09-18 15:32:55] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -44.218107+0.002213j
[2025-09-18 15:33:08] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -44.231111-0.000051j
[2025-09-18 15:33:20] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -44.213909-0.000253j
[2025-09-18 15:33:33] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -44.227865+0.003549j
[2025-09-18 15:33:45] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -44.230840+0.000505j
[2025-09-18 15:33:58] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -44.213580+0.001208j
[2025-09-18 15:34:10] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -44.228201+0.001743j
[2025-09-18 15:34:22] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -44.223669+0.001037j
[2025-09-18 15:34:35] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -44.210731+0.002213j
[2025-09-18 15:34:47] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -44.233266+0.000688j
[2025-09-18 15:34:48] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-18 15:35:00] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -44.228864+0.001165j
[2025-09-18 15:35:12] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -44.201826-0.001002j
[2025-09-18 15:35:25] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -44.219780-0.000477j
[2025-09-18 15:35:37] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -44.220436-0.004611j
[2025-09-18 15:35:50] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -44.223845+0.001140j
[2025-09-18 15:36:02] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -44.203246+0.004467j
[2025-09-18 15:36:15] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -44.225211-0.003481j
[2025-09-18 15:36:27] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -44.214761-0.000195j
[2025-09-18 15:36:40] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -44.220033-0.002566j
[2025-09-18 15:36:52] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -44.199945-0.002680j
[2025-09-18 15:37:04] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -44.231291-0.002064j
[2025-09-18 15:37:17] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -44.216714-0.000330j
[2025-09-18 15:37:29] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -44.209544+0.002726j
[2025-09-18 15:37:42] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -44.216519+0.000271j
[2025-09-18 15:37:54] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -44.220755+0.003077j
[2025-09-18 15:38:07] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -44.219942-0.003593j
[2025-09-18 15:38:19] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -44.204548-0.001458j
[2025-09-18 15:38:32] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -44.219772-0.002191j
[2025-09-18 15:38:44] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -44.230144-0.001056j
[2025-09-18 15:38:56] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -44.221053+0.002226j
[2025-09-18 15:39:09] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -44.216732+0.004300j
[2025-09-18 15:39:21] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -44.219992-0.000310j
[2025-09-18 15:39:34] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -44.232988-0.001343j
[2025-09-18 15:39:46] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -44.219051-0.000613j
[2025-09-18 15:39:59] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -44.206996+0.002224j
[2025-09-18 15:40:11] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -44.205202+0.000899j
[2025-09-18 15:40:24] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -44.232796-0.002971j
[2025-09-18 15:40:36] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -44.215359-0.005405j
[2025-09-18 15:40:48] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -44.219876-0.004864j
[2025-09-18 15:41:01] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -44.196934+0.001514j
[2025-09-18 15:41:01] RESTART #2 | Period: 600
[2025-09-18 15:41:13] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -44.201008+0.001658j
[2025-09-18 15:41:26] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -44.232572-0.003722j
[2025-09-18 15:41:38] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -44.225720+0.000935j
[2025-09-18 15:41:51] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -44.221099-0.000570j
[2025-09-18 15:42:03] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -44.219316-0.000339j
[2025-09-18 15:42:16] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -44.205161-0.000621j
[2025-09-18 15:42:28] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -44.221971-0.000830j
[2025-09-18 15:42:41] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -44.215872-0.000349j
[2025-09-18 15:42:53] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -44.224266+0.003970j
[2025-09-18 15:43:05] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -44.227623-0.001699j
[2025-09-18 15:43:18] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -44.215201-0.005754j
[2025-09-18 15:43:30] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -44.218519+0.000271j
[2025-09-18 15:43:43] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -44.238807+0.000879j
[2025-09-18 15:43:55] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -44.208770-0.000300j
[2025-09-18 15:44:08] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -44.227094-0.000516j
[2025-09-18 15:44:20] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -44.211484-0.005530j
[2025-09-18 15:44:33] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -44.234820-0.000689j
[2025-09-18 15:44:45] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -44.215661-0.001317j
[2025-09-18 15:44:57] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -44.228587-0.003527j
[2025-09-18 15:45:10] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -44.220116-0.001225j
[2025-09-18 15:45:22] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -44.221032-0.000285j
[2025-09-18 15:45:35] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -44.229844+0.002175j
[2025-09-18 15:45:47] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -44.213972+0.000496j
[2025-09-18 15:46:00] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -44.230199-0.002406j
[2025-09-18 15:46:12] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -44.217042+0.001111j
[2025-09-18 15:46:25] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -44.215335+0.006101j
[2025-09-18 15:46:37] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -44.215065+0.002271j
[2025-09-18 15:46:50] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -44.222927-0.002053j
[2025-09-18 15:47:02] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -44.233404-0.004966j
[2025-09-18 15:47:15] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -44.219791-0.004888j
[2025-09-18 15:47:27] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -44.225185-0.000195j
[2025-09-18 15:47:39] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -44.230646+0.001268j
[2025-09-18 15:47:52] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -44.212900-0.001914j
[2025-09-18 15:48:04] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -44.221765-0.002269j
[2025-09-18 15:48:17] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -44.216840-0.002701j
[2025-09-18 15:48:29] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -44.209465+0.002013j
[2025-09-18 15:48:42] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -44.228452+0.006178j
[2025-09-18 15:48:54] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -44.220530+0.001799j
[2025-09-18 15:49:07] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -44.225502-0.000879j
[2025-09-18 15:49:19] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -44.205466-0.001200j
[2025-09-18 15:49:32] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -44.228584+0.002060j
[2025-09-18 15:49:44] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -44.215778-0.004054j
[2025-09-18 15:49:56] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -44.225761-0.001836j
[2025-09-18 15:50:09] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -44.215938-0.000479j
[2025-09-18 15:50:21] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -44.205091-0.002399j
[2025-09-18 15:50:34] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -44.217093-0.000497j
[2025-09-18 15:50:46] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -44.218845-0.001549j
[2025-09-18 15:50:59] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -44.219836+0.003699j
[2025-09-18 15:51:11] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -44.216211+0.000252j
[2025-09-18 15:51:24] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -44.226831-0.001222j
[2025-09-18 15:51:36] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -44.225347-0.000418j
[2025-09-18 15:51:49] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -44.228460-0.001210j
[2025-09-18 15:52:01] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -44.221478+0.007269j
[2025-09-18 15:52:14] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -44.208099+0.001204j
[2025-09-18 15:52:26] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -44.221381-0.002299j
[2025-09-18 15:52:38] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -44.215013-0.000222j
[2025-09-18 15:52:51] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -44.221696-0.001056j
[2025-09-18 15:53:03] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -44.216366+0.002131j
[2025-09-18 15:53:16] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -44.215268+0.004337j
[2025-09-18 15:53:28] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -44.224832+0.000194j
[2025-09-18 15:53:41] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -44.228649-0.005177j
[2025-09-18 15:53:53] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -44.220443-0.000756j
[2025-09-18 15:54:05] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -44.233182-0.001422j
[2025-09-18 15:54:18] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -44.220159+0.002726j
[2025-09-18 15:54:30] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -44.214005-0.000891j
[2025-09-18 15:54:43] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -44.214955+0.001552j
[2025-09-18 15:54:55] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -44.222647-0.003825j
[2025-09-18 15:55:08] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -44.211820+0.000998j
[2025-09-18 15:55:20] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -44.221674+0.001181j
[2025-09-18 15:55:33] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -44.237296+0.004267j
[2025-09-18 15:55:45] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -44.220569-0.004118j
[2025-09-18 15:55:58] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -44.222595+0.000653j
[2025-09-18 15:56:10] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -44.228252-0.001175j
[2025-09-18 15:56:23] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -44.215900+0.001557j
[2025-09-18 15:56:35] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -44.211685+0.001005j
[2025-09-18 15:56:35] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-18 15:56:47] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -44.199171+0.001865j
[2025-09-18 15:57:00] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -44.215492-0.000085j
[2025-09-18 15:57:12] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -44.218913+0.003101j
[2025-09-18 15:57:25] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -44.220229+0.001516j
[2025-09-18 15:57:37] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -44.220277-0.000421j
[2025-09-18 15:57:50] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -44.209075+0.001566j
[2025-09-18 15:58:02] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -44.219908-0.001023j
[2025-09-18 15:58:14] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -44.217034-0.001544j
[2025-09-18 15:58:27] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -44.215863+0.002389j
[2025-09-18 15:58:39] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -44.203291+0.001666j
[2025-09-18 15:58:52] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -44.228196+0.001044j
[2025-09-18 15:59:04] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -44.207490+0.000944j
[2025-09-18 15:59:17] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -44.223069+0.000632j
[2025-09-18 15:59:29] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -44.228738+0.000619j
[2025-09-18 15:59:42] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -44.214174-0.001395j
[2025-09-18 15:59:54] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -44.223744-0.001544j
[2025-09-18 16:00:06] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -44.237419-0.001806j
[2025-09-18 16:00:19] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -44.216989+0.002475j
[2025-09-18 16:00:31] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -44.228364+0.005497j
[2025-09-18 16:00:44] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -44.225490+0.001346j
[2025-09-18 16:00:56] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -44.220599-0.000448j
[2025-09-18 16:01:09] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -44.232609+0.000174j
[2025-09-18 16:01:21] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -44.200584-0.001236j
[2025-09-18 16:01:34] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -44.220518+0.000906j
[2025-09-18 16:01:46] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -44.227510-0.002159j
[2025-09-18 16:01:59] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -44.205872-0.001559j
[2025-09-18 16:02:11] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -44.215909-0.000326j
[2025-09-18 16:02:24] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -44.227676-0.001541j
[2025-09-18 16:02:36] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -44.219979-0.004526j
[2025-09-18 16:02:49] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -44.207304+0.004386j
[2025-09-18 16:03:01] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -44.239920-0.000929j
[2025-09-18 16:03:13] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -44.231909-0.001404j
[2025-09-18 16:03:26] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -44.224933-0.006582j
[2025-09-18 16:03:38] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -44.228001-0.003301j
[2025-09-18 16:03:51] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -44.215703-0.001860j
[2025-09-18 16:04:03] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -44.207251-0.003740j
[2025-09-18 16:04:16] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -44.203801+0.000170j
[2025-09-18 16:04:28] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -44.218048+0.005034j
[2025-09-18 16:04:41] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -44.232812-0.002962j
[2025-09-18 16:04:53] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -44.222011+0.003998j
[2025-09-18 16:05:05] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -44.217379-0.002208j
[2025-09-18 16:05:18] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -44.228226+0.001135j
[2025-09-18 16:05:30] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -44.209754-0.000334j
[2025-09-18 16:05:43] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -44.237453+0.000018j
[2025-09-18 16:05:55] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -44.234877-0.000079j
[2025-09-18 16:06:08] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -44.224702+0.001741j
[2025-09-18 16:06:20] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -44.220725+0.003158j
[2025-09-18 16:06:33] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -44.230070+0.002131j
[2025-09-18 16:06:45] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -44.215155-0.001765j
[2025-09-18 16:06:58] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -44.218894+0.001114j
[2025-09-18 16:07:10] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -44.213499-0.002428j
[2025-09-18 16:07:22] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -44.223791-0.001129j
[2025-09-18 16:07:35] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -44.204675+0.002687j
[2025-09-18 16:07:47] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -44.217032+0.002550j
[2025-09-18 16:08:00] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -44.212701-0.000180j
[2025-09-18 16:08:12] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -44.221437-0.002677j
[2025-09-18 16:08:25] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -44.222445+0.000622j
[2025-09-18 16:08:37] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -44.216106-0.004655j
[2025-09-18 16:08:50] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -44.220895+0.005215j
[2025-09-18 16:09:02] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -44.219605+0.000338j
[2025-09-18 16:09:14] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -44.206808+0.001476j
[2025-09-18 16:09:27] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -44.217294-0.001326j
[2025-09-18 16:09:39] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -44.223534-0.002153j
[2025-09-18 16:09:52] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -44.226407+0.001502j
[2025-09-18 16:10:04] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -44.228280-0.006618j
[2025-09-18 16:10:17] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -44.225702-0.004476j
[2025-09-18 16:10:29] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -44.217284-0.000267j
[2025-09-18 16:10:42] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -44.224313-0.003237j
[2025-09-18 16:10:54] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -44.221757+0.000326j
[2025-09-18 16:11:06] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -44.217746+0.004051j
[2025-09-18 16:11:19] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -44.228638+0.001218j
[2025-09-18 16:11:31] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -44.217631+0.002609j
[2025-09-18 16:11:44] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -44.218466+0.000277j
[2025-09-18 16:11:56] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -44.218853+0.000590j
[2025-09-18 16:12:09] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -44.212051-0.002788j
[2025-09-18 16:12:21] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -44.215022+0.005941j
[2025-09-18 16:12:34] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -44.211077-0.005595j
[2025-09-18 16:12:46] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -44.223628-0.000883j
[2025-09-18 16:12:58] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -44.227963+0.000059j
[2025-09-18 16:13:11] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -44.219841-0.001858j
[2025-09-18 16:13:23] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -44.214451-0.001021j
[2025-09-18 16:13:36] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -44.216973+0.001624j
[2025-09-18 16:13:48] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -44.234438-0.000071j
[2025-09-18 16:14:01] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -44.221727-0.002929j
[2025-09-18 16:14:13] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -44.212560-0.005203j
[2025-09-18 16:14:26] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -44.219496+0.001672j
[2025-09-18 16:14:38] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -44.226888+0.000336j
[2025-09-18 16:14:50] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -44.219813+0.004261j
[2025-09-18 16:15:03] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -44.196329-0.003046j
[2025-09-18 16:15:15] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -44.204519+0.000723j
[2025-09-18 16:15:28] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -44.233203+0.000779j
[2025-09-18 16:15:40] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -44.223038+0.002909j
[2025-09-18 16:15:53] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -44.223584+0.000177j
[2025-09-18 16:16:05] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -44.228475-0.004187j
[2025-09-18 16:16:18] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -44.219120+0.004847j
[2025-09-18 16:16:30] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -44.219665-0.000878j
[2025-09-18 16:16:43] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -44.219172-0.000750j
[2025-09-18 16:16:55] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -44.220653-0.000911j
[2025-09-18 16:17:07] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -44.221345+0.002903j
[2025-09-18 16:17:20] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -44.223143+0.003288j
[2025-09-18 16:17:32] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -44.218367+0.001013j
[2025-09-18 16:17:45] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -44.210920-0.002283j
[2025-09-18 16:17:57] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -44.211262-0.000009j
[2025-09-18 16:18:10] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -44.232854-0.000255j
[2025-09-18 16:18:22] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -44.223450-0.004402j
[2025-09-18 16:18:22] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-18 16:18:35] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -44.221720+0.000201j
[2025-09-18 16:18:47] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -44.219316-0.053293j
[2025-09-18 16:18:59] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -44.219953-0.004716j
[2025-09-18 16:19:12] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -44.223320-0.000661j
[2025-09-18 16:19:24] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -44.221878+0.003549j
[2025-09-18 16:19:37] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -44.224451+0.001354j
[2025-09-18 16:19:49] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -44.204148-0.000140j
[2025-09-18 16:20:02] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -44.219900-0.001553j
[2025-09-18 16:20:14] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -44.210912-0.001513j
[2025-09-18 16:20:27] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -44.229741+0.001980j
[2025-09-18 16:20:39] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -44.205778+0.000259j
[2025-09-18 16:20:52] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -44.219069-0.002665j
[2025-09-18 16:21:04] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -44.213985-0.003653j
[2025-09-18 16:21:16] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -44.216703+0.000031j
[2025-09-18 16:21:29] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -44.218735-0.000847j
[2025-09-18 16:21:41] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -44.204166+0.002699j
[2025-09-18 16:21:54] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -44.226660-0.000557j
[2025-09-18 16:22:06] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -44.220525-0.002927j
[2025-09-18 16:22:19] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -44.216050+0.003180j
[2025-09-18 16:22:31] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -44.226300+0.002055j
[2025-09-18 16:22:44] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -44.215295+0.000347j
[2025-09-18 16:22:56] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -44.220684-0.001934j
[2025-09-18 16:23:09] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -44.207585+0.002677j
[2025-09-18 16:23:21] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -44.231932-0.000079j
[2025-09-18 16:23:34] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -44.218554+0.002012j
[2025-09-18 16:23:46] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -44.217361+0.001950j
[2025-09-18 16:23:59] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -44.216488+0.002662j
[2025-09-18 16:24:11] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -44.222511+0.000955j
[2025-09-18 16:24:24] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -44.234647-0.004158j
[2025-09-18 16:24:37] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -44.222864+0.000152j
[2025-09-18 16:24:49] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -44.219405+0.002241j
[2025-09-18 16:25:02] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -44.223685+0.000632j
[2025-09-18 16:25:14] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -44.216349+0.004405j
[2025-09-18 16:25:27] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -44.211942+0.000968j
[2025-09-18 16:25:39] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -44.222403-0.002242j
[2025-09-18 16:25:52] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -44.206711-0.000099j
[2025-09-18 16:26:04] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -44.224743-0.001907j
[2025-09-18 16:26:17] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -44.226697+0.000888j
[2025-09-18 16:26:29] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -44.215209-0.000761j
[2025-09-18 16:26:42] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -44.226561+0.001217j
[2025-09-18 16:26:55] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -44.227461-0.000469j
[2025-09-18 16:27:07] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -44.213873-0.002801j
[2025-09-18 16:27:20] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -44.220642+0.000527j
[2025-09-18 16:27:32] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -44.228448+0.003479j
[2025-09-18 16:27:45] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -44.230968-0.000932j
[2025-09-18 16:27:57] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -44.223210-0.002415j
[2025-09-18 16:28:10] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -44.211712+0.001905j
[2025-09-18 16:28:22] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -44.220285-0.000604j
[2025-09-18 16:28:35] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -44.228804+0.000932j
[2025-09-18 16:28:47] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -44.219588-0.000547j
[2025-09-18 16:29:00] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -44.231869+0.000883j
[2025-09-18 16:29:13] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -44.236059+0.000901j
[2025-09-18 16:29:25] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -44.220324-0.001223j
[2025-09-18 16:29:38] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -44.201552+0.003705j
[2025-09-18 16:29:50] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -44.225623-0.003159j
[2025-09-18 16:30:03] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -44.228874-0.003281j
[2025-09-18 16:30:15] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -44.215559+0.000619j
[2025-09-18 16:30:28] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -44.230230+0.000927j
[2025-09-18 16:30:40] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -44.222787-0.000297j
[2025-09-18 16:30:53] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -44.233397+0.001075j
[2025-09-18 16:31:05] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -44.217472+0.000037j
[2025-09-18 16:31:18] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -44.229882+0.002232j
[2025-09-18 16:31:30] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -44.223727+0.000545j
[2025-09-18 16:31:43] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -44.225547-0.002583j
[2025-09-18 16:31:56] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -44.225094+0.003474j
[2025-09-18 16:32:08] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -44.224012-0.000624j
[2025-09-18 16:32:21] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -44.231239+0.003727j
[2025-09-18 16:32:33] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -44.220377-0.003345j
[2025-09-18 16:32:46] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -44.219669+0.000345j
[2025-09-18 16:32:58] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -44.221330-0.003334j
[2025-09-18 16:33:11] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -44.219444+0.000677j
[2025-09-18 16:33:23] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -44.219475+0.001373j
[2025-09-18 16:33:36] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -44.231501-0.001407j
[2025-09-18 16:33:49] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -44.231687+0.000765j
[2025-09-18 16:34:01] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -44.210180-0.001382j
[2025-09-18 16:34:14] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -44.220120-0.001411j
[2025-09-18 16:34:26] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -44.215948+0.000800j
[2025-09-18 16:34:39] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -44.214751-0.000352j
[2025-09-18 16:34:51] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -44.228208+0.008503j
[2025-09-18 16:35:04] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -44.229279+0.001283j
[2025-09-18 16:35:16] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -44.228857+0.000831j
[2025-09-18 16:35:29] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -44.199709+0.001553j
[2025-09-18 16:35:42] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -44.209942+0.001017j
[2025-09-18 16:35:54] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -44.231053-0.000041j
[2025-09-18 16:36:07] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -44.218260-0.000593j
[2025-09-18 16:36:19] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -44.236108+0.001602j
[2025-09-18 16:36:32] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -44.225405+0.000718j
[2025-09-18 16:36:44] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -44.212870-0.002074j
[2025-09-18 16:36:57] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -44.204579-0.002828j
[2025-09-18 16:37:09] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -44.231914-0.000222j
[2025-09-18 16:37:22] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -44.228734-0.005253j
[2025-09-18 16:37:34] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -44.211748-0.003137j
[2025-09-18 16:37:47] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -44.232747+0.003499j
[2025-09-18 16:38:00] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -44.217644-0.000693j
[2025-09-18 16:38:12] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -44.229702+0.001308j
[2025-09-18 16:38:25] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -44.228550-0.002824j
[2025-09-18 16:38:37] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -44.242856-0.005028j
[2025-09-18 16:38:50] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -44.232296-0.002526j
[2025-09-18 16:39:02] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -44.221344+0.002219j
[2025-09-18 16:39:15] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -44.228339+0.001965j
[2025-09-18 16:39:28] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -44.215938-0.000305j
[2025-09-18 16:39:40] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -44.229482-0.007516j
[2025-09-18 16:39:53] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -44.233393-0.004156j
[2025-09-18 16:40:05] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -44.217272-0.001581j
[2025-09-18 16:40:18] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -44.222203-0.002761j
[2025-09-18 16:40:18] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-18 16:40:30] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -44.221861-0.000057j
[2025-09-18 16:40:43] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -44.232383-0.000538j
[2025-09-18 16:40:55] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -44.231478-0.001700j
[2025-09-18 16:41:08] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -44.222677-0.003286j
[2025-09-18 16:41:20] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -44.228855+0.000401j
[2025-09-18 16:41:33] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -44.217000+0.001291j
[2025-09-18 16:41:45] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -44.221160-0.003860j
[2025-09-18 16:41:58] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -44.233942-0.001484j
[2025-09-18 16:42:11] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -44.219621+0.002288j
[2025-09-18 16:42:23] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -44.215195+0.000054j
[2025-09-18 16:42:36] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -44.230172+0.004524j
[2025-09-18 16:42:48] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -44.214485-0.000113j
[2025-09-18 16:43:01] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -44.205578-0.003688j
[2025-09-18 16:43:13] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -44.217992+0.001317j
[2025-09-18 16:43:26] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -44.226005-0.003556j
[2025-09-18 16:43:38] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -44.205892-0.001556j
[2025-09-18 16:43:51] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -44.231485-0.001233j
[2025-09-18 16:44:03] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -44.208692+0.000598j
[2025-09-18 16:44:16] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -44.226770-0.000614j
[2025-09-18 16:44:29] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -44.227488-0.000912j
[2025-09-18 16:44:41] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -44.227970+0.001427j
[2025-09-18 16:44:54] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -44.211444+0.000770j
[2025-09-18 16:45:06] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -44.224176-0.000270j
[2025-09-18 16:45:19] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -44.226178+0.002040j
[2025-09-18 16:45:31] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -44.218178+0.004901j
[2025-09-18 16:45:44] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -44.229764+0.000180j
[2025-09-18 16:45:56] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -44.222037-0.001277j
[2025-09-18 16:46:09] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -44.214895-0.000435j
[2025-09-18 16:46:21] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -44.223040+0.001360j
[2025-09-18 16:46:34] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -44.226015-0.001886j
[2025-09-18 16:46:47] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -44.212886-0.000553j
[2025-09-18 16:46:59] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -44.208552+0.003288j
[2025-09-18 16:47:12] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -44.206093-0.000487j
[2025-09-18 16:47:24] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -44.228832+0.001062j
[2025-09-18 16:47:37] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -44.235208+0.001178j
[2025-09-18 16:47:49] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -44.225148-0.000614j
[2025-09-18 16:48:02] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -44.227403-0.001630j
[2025-09-18 16:48:14] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -44.222862+0.000226j
[2025-09-18 16:48:27] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -44.227031-0.000544j
[2025-09-18 16:48:39] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -44.216480+0.001819j
[2025-09-18 16:48:52] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -44.212271+0.001529j
[2025-09-18 16:49:04] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -44.224459+0.001123j
[2025-09-18 16:49:17] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -44.215719-0.000623j
[2025-09-18 16:49:29] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -44.233575-0.000149j
[2025-09-18 16:49:42] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -44.223523-0.000233j
[2025-09-18 16:49:54] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -44.216416-0.001795j
[2025-09-18 16:50:07] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -44.229125+0.000831j
[2025-09-18 16:50:19] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -44.218278+0.001316j
[2025-09-18 16:50:32] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -44.229027+0.002230j
[2025-09-18 16:50:45] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -44.221909-0.000180j
[2025-09-18 16:50:57] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -44.222599+0.000518j
[2025-09-18 16:51:10] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -44.226816+0.002803j
[2025-09-18 16:51:22] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -44.208413+0.001715j
[2025-09-18 16:51:35] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -44.211679-0.000271j
[2025-09-18 16:51:47] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -44.216120+0.001521j
[2025-09-18 16:52:00] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -44.214466+0.001683j
[2025-09-18 16:52:12] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -44.221176-0.001866j
[2025-09-18 16:52:25] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -44.222585-0.002550j
[2025-09-18 16:52:38] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -44.223562-0.002355j
[2025-09-18 16:52:50] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -44.221531+0.000393j
[2025-09-18 16:53:03] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -44.213044-0.001652j
[2025-09-18 16:53:15] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -44.221627-0.000432j
[2025-09-18 16:53:28] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -44.222945+0.000652j
[2025-09-18 16:53:40] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -44.222445+0.000145j
[2025-09-18 16:53:53] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -44.221448+0.002362j
[2025-09-18 16:54:06] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -44.219683-0.002668j
[2025-09-18 16:54:18] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -44.226061-0.000203j
[2025-09-18 16:54:31] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -44.223911+0.001249j
[2025-09-18 16:54:43] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -44.230960+0.006089j
[2025-09-18 16:54:56] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -44.229682-0.000942j
[2025-09-18 16:55:08] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -44.237800+0.002932j
[2025-09-18 16:55:21] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -44.221234+0.000773j
[2025-09-18 16:55:33] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -44.237176-0.001755j
[2025-09-18 16:55:46] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -44.228298+0.000551j
[2025-09-18 16:55:58] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -44.236968+0.000807j
[2025-09-18 16:56:11] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -44.205732-0.006042j
[2025-09-18 16:56:23] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -44.215656+0.000234j
[2025-09-18 16:56:36] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -44.228414-0.001806j
[2025-09-18 16:56:49] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -44.199853-0.005940j
[2025-09-18 16:57:01] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -44.226845-0.000236j
[2025-09-18 16:57:14] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -44.223441+0.000603j
[2025-09-18 16:57:26] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -44.217705+0.000585j
[2025-09-18 16:57:39] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -44.223815+0.000560j
[2025-09-18 16:57:51] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -44.223532-0.001494j
[2025-09-18 16:58:04] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -44.210409-0.000276j
[2025-09-18 16:58:16] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -44.210289+0.001478j
[2025-09-18 16:58:29] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -44.214265-0.002549j
[2025-09-18 16:58:41] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -44.234619-0.003499j
[2025-09-18 16:58:54] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -44.231472-0.000421j
[2025-09-18 16:59:07] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -44.232762+0.000118j
[2025-09-18 16:59:19] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -44.201339+0.000361j
[2025-09-18 16:59:32] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -44.222894-0.002080j
[2025-09-18 16:59:44] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -44.234189+0.003144j
[2025-09-18 16:59:57] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -44.226827-0.001388j
[2025-09-18 17:00:09] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -44.236642+0.000807j
[2025-09-18 17:00:22] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -44.223829+0.002356j
[2025-09-18 17:00:34] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -44.203884+0.000626j
[2025-09-18 17:00:47] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -44.221059-0.000349j
[2025-09-18 17:00:59] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -44.220683-0.001200j
[2025-09-18 17:01:12] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -44.221537-0.000080j
[2025-09-18 17:01:24] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -44.221947+0.004845j
[2025-09-18 17:01:37] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -44.216115+0.003147j
[2025-09-18 17:01:49] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -44.233985+0.000234j
[2025-09-18 17:02:02] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -44.215186+0.002999j
[2025-09-18 17:02:14] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -44.221086+0.003207j
[2025-09-18 17:02:14] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-18 17:02:27] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -44.221655-0.000310j
[2025-09-18 17:02:39] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -44.231134+0.000619j
[2025-09-18 17:02:52] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -44.231439+0.000110j
[2025-09-18 17:03:04] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -44.215604-0.004318j
[2025-09-18 17:03:17] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -44.219123+0.000639j
[2025-09-18 17:03:29] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -44.214284+0.001984j
[2025-09-18 17:03:42] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -44.202412+0.002241j
[2025-09-18 17:03:55] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -44.212004+0.002233j
[2025-09-18 17:04:07] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -44.219029-0.000730j
[2025-09-18 17:04:20] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -44.218815-0.001026j
[2025-09-18 17:04:32] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -44.226888+0.000347j
[2025-09-18 17:04:45] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -44.219009+0.002822j
[2025-09-18 17:04:57] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -44.229646+0.003868j
[2025-09-18 17:05:10] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -44.229993+0.000737j
[2025-09-18 17:05:22] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -44.227354-0.001759j
[2025-09-18 17:05:35] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -44.194451+0.000204j
[2025-09-18 17:05:47] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -44.223469+0.000795j
[2025-09-18 17:06:00] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -44.218413-0.001940j
[2025-09-18 17:06:13] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -44.225674-0.001828j
[2025-09-18 17:06:25] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -44.215782+0.004626j
[2025-09-18 17:06:38] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -44.225910-0.001971j
[2025-09-18 17:06:50] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -44.226675-0.002606j
[2025-09-18 17:07:03] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -44.230583+0.003698j
[2025-09-18 17:07:15] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -44.237259+0.003064j
[2025-09-18 17:07:28] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -44.227539-0.002518j
[2025-09-18 17:07:40] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -44.227898+0.003852j
[2025-09-18 17:07:53] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -44.226899+0.000748j
[2025-09-18 17:08:05] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -44.230322+0.001370j
[2025-09-18 17:08:18] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -44.221055-0.001158j
[2025-09-18 17:08:31] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -44.221654+0.002192j
[2025-09-18 17:08:43] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -44.228535-0.000237j
[2025-09-18 17:08:55] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -44.219073+0.003122j
[2025-09-18 17:09:08] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -44.221415-0.001924j
[2025-09-18 17:09:20] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -44.219191+0.004853j
[2025-09-18 17:09:33] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -44.216397-0.001772j
[2025-09-18 17:09:45] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -44.225303-0.000310j
[2025-09-18 17:09:58] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -44.223248+0.004331j
[2025-09-18 17:10:10] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -44.219199-0.001505j
[2025-09-18 17:10:23] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -44.232781-0.004101j
[2025-09-18 17:10:36] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -44.223020-0.002273j
[2025-09-18 17:10:48] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -44.216490+0.001411j
[2025-09-18 17:11:01] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -44.214745+0.001504j
[2025-09-18 17:11:13] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -44.229568-0.002983j
[2025-09-18 17:11:26] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -44.220280-0.003496j
[2025-09-18 17:11:38] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -44.206559-0.000227j
[2025-09-18 17:11:51] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -44.212930-0.000303j
[2025-09-18 17:12:03] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -44.227020-0.003690j
[2025-09-18 17:12:16] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -44.242427+0.004231j
[2025-09-18 17:12:28] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -44.229343-0.003581j
[2025-09-18 17:12:41] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -44.212721-0.001422j
[2025-09-18 17:12:53] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -44.222811-0.000173j
[2025-09-18 17:13:06] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -44.232261-0.000771j
[2025-09-18 17:13:18] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -44.213319-0.004470j
[2025-09-18 17:13:31] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -44.236691+0.003923j
[2025-09-18 17:13:43] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -44.201874+0.001617j
[2025-09-18 17:13:56] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -44.230515-0.004910j
[2025-09-18 17:14:08] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -44.218793+0.003526j
[2025-09-18 17:14:21] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -44.232450+0.003584j
[2025-09-18 17:14:33] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -44.221845-0.001450j
[2025-09-18 17:14:45] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -44.227669+0.000294j
[2025-09-18 17:14:58] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -44.222625+0.000494j
[2025-09-18 17:15:10] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -44.232137-0.001738j
[2025-09-18 17:15:23] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -44.217490+0.002803j
[2025-09-18 17:15:35] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -44.224059-0.000019j
[2025-09-18 17:15:48] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -44.226687-0.000203j
[2025-09-18 17:16:00] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -44.226278-0.001978j
[2025-09-18 17:16:13] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -44.210466+0.003355j
[2025-09-18 17:16:25] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -44.229836-0.000410j
[2025-09-18 17:16:37] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -44.226635-0.001633j
[2025-09-18 17:16:50] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -44.225294+0.000473j
[2025-09-18 17:17:02] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -44.221587+0.000375j
[2025-09-18 17:17:15] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -44.221929+0.002354j
[2025-09-18 17:17:27] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -44.233169-0.002212j
[2025-09-18 17:17:40] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -44.219845-0.001829j
[2025-09-18 17:17:52] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -44.221022+0.001719j
[2025-09-18 17:18:05] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -44.222268+0.001542j
[2025-09-18 17:18:17] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -44.219634-0.002674j
[2025-09-18 17:18:29] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -44.228777-0.000499j
[2025-09-18 17:18:42] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -44.216967-0.000191j
[2025-09-18 17:18:54] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -44.227534+0.000470j
[2025-09-18 17:19:07] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -44.222153+0.001055j
[2025-09-18 17:19:19] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -44.208546-0.000749j
[2025-09-18 17:19:32] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -44.236347-0.001866j
[2025-09-18 17:19:44] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -44.206404-0.001805j
[2025-09-18 17:19:57] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -44.229431+0.001456j
[2025-09-18 17:20:09] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -44.221740-0.002309j
[2025-09-18 17:20:22] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -44.219599-0.001340j
[2025-09-18 17:20:34] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -44.234007+0.000221j
[2025-09-18 17:20:47] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -44.223693-0.002705j
[2025-09-18 17:20:59] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -44.222606-0.003651j
[2025-09-18 17:21:12] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -44.227299+0.001722j
[2025-09-18 17:21:25] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -44.233615-0.002923j
[2025-09-18 17:21:37] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -44.238613-0.000881j
[2025-09-18 17:21:50] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -44.235452-0.001678j
[2025-09-18 17:22:02] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -44.231998+0.004946j
[2025-09-18 17:22:15] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -44.231709-0.000722j
[2025-09-18 17:22:27] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -44.199447+0.001734j
[2025-09-18 17:22:40] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -44.217289-0.000401j
[2025-09-18 17:22:52] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -44.221489-0.000243j
[2025-09-18 17:23:05] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -44.223442-0.002362j
[2025-09-18 17:23:18] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -44.210959-0.005030j
[2025-09-18 17:23:30] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -44.235890+0.000255j
[2025-09-18 17:23:43] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -44.229378+0.002666j
[2025-09-18 17:23:55] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -44.219521-0.000178j
[2025-09-18 17:24:07] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -44.226862+0.005055j
[2025-09-18 17:24:07] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-18 17:24:20] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -44.230192-0.004966j
[2025-09-18 17:24:32] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -44.227301+0.000003j
[2025-09-18 17:24:45] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -44.218609-0.000864j
[2025-09-18 17:24:57] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -44.213771-0.001118j
[2025-09-18 17:25:10] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -44.228502-0.002503j
[2025-09-18 17:25:22] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -44.210475+0.004651j
[2025-09-18 17:25:34] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -44.220797-0.000299j
[2025-09-18 17:25:47] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -44.224641-0.003064j
[2025-09-18 17:25:59] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -44.227635+0.001741j
[2025-09-18 17:26:12] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -44.225242-0.000590j
[2025-09-18 17:26:24] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -44.239386+0.000130j
[2025-09-18 17:26:37] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -44.213700-0.003886j
[2025-09-18 17:26:49] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -44.223608-0.001199j
[2025-09-18 17:27:02] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -44.216059-0.001140j
[2025-09-18 17:27:14] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -44.217857-0.002498j
[2025-09-18 17:27:26] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -44.228970-0.001869j
[2025-09-18 17:27:39] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -44.230974+0.004582j
[2025-09-18 17:27:51] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -44.225374+0.001216j
[2025-09-18 17:28:04] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -44.241653-0.000047j
[2025-09-18 17:28:16] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -44.211699-0.000081j
[2025-09-18 17:28:29] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -44.224618-0.004348j
[2025-09-18 17:28:41] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -44.217402+0.001661j
[2025-09-18 17:28:54] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -44.222509+0.000403j
[2025-09-18 17:29:06] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -44.225479+0.001379j
[2025-09-18 17:29:18] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -44.237212-0.004069j
[2025-09-18 17:29:31] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -44.222670-0.000649j
[2025-09-18 17:29:43] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -44.204856-0.000589j
[2025-09-18 17:29:56] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -44.217634-0.000852j
[2025-09-18 17:30:08] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -44.222978-0.002141j
[2025-09-18 17:30:21] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -44.222096-0.002708j
[2025-09-18 17:30:33] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -44.220164+0.001763j
[2025-09-18 17:30:46] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -44.226860-0.002490j
[2025-09-18 17:30:58] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -44.216477-0.000472j
[2025-09-18 17:31:10] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -44.230201-0.001764j
[2025-09-18 17:31:23] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -44.221966-0.002302j
[2025-09-18 17:31:35] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -44.221314-0.001166j
[2025-09-18 17:31:48] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -44.204930+0.001976j
[2025-09-18 17:32:00] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -44.234192+0.001776j
[2025-09-18 17:32:13] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -44.209253-0.002804j
[2025-09-18 17:32:25] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -44.237259+0.002623j
[2025-09-18 17:32:38] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -44.211585+0.006412j
[2025-09-18 17:32:50] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -44.220494+0.001012j
[2025-09-18 17:33:03] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -44.215657+0.002752j
[2025-09-18 17:33:15] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -44.211642+0.003542j
[2025-09-18 17:33:27] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -44.224172+0.000608j
[2025-09-18 17:33:40] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -44.226375-0.003994j
[2025-09-18 17:33:52] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -44.208162-0.001287j
[2025-09-18 17:34:05] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -44.246759+0.001326j
[2025-09-18 17:34:17] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -44.227201-0.001340j
[2025-09-18 17:34:30] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -44.231671-0.002578j
[2025-09-18 17:34:42] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -44.226423-0.003236j
[2025-09-18 17:34:55] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -44.236977-0.001342j
[2025-09-18 17:35:07] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -44.216250+0.004199j
[2025-09-18 17:35:20] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -44.227144+0.002375j
[2025-09-18 17:35:32] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -44.230578-0.001667j
[2025-09-18 17:35:45] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -44.218783+0.000449j
[2025-09-18 17:35:57] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -44.222644-0.001366j
[2025-09-18 17:36:09] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -44.232004-0.002578j
[2025-09-18 17:36:22] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -44.229947+0.000126j
[2025-09-18 17:36:34] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -44.216591+0.000529j
[2025-09-18 17:36:47] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -44.219222-0.001521j
[2025-09-18 17:36:59] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -44.216298+0.002047j
[2025-09-18 17:37:12] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -44.227624+0.005348j
[2025-09-18 17:37:24] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -44.226740-0.002457j
[2025-09-18 17:37:37] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -44.218302-0.002771j
[2025-09-18 17:37:49] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -44.228874-0.004039j
[2025-09-18 17:38:01] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -44.219241-0.000125j
[2025-09-18 17:38:14] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -44.241484+0.001924j
[2025-09-18 17:38:26] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -44.214880+0.002207j
[2025-09-18 17:38:39] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -44.223868-0.000666j
[2025-09-18 17:38:51] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -44.213696+0.004581j
[2025-09-18 17:39:04] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -44.236502+0.000573j
[2025-09-18 17:39:16] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -44.207644+0.002467j
[2025-09-18 17:39:29] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -44.227781-0.000629j
[2025-09-18 17:39:41] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -44.229041+0.003642j
[2025-09-18 17:39:54] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -44.224542+0.000950j
[2025-09-18 17:40:06] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -44.225719-0.001098j
[2025-09-18 17:40:19] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -44.227789-0.000305j
[2025-09-18 17:40:31] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -44.223836-0.002986j
[2025-09-18 17:40:43] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -44.218441-0.000560j
[2025-09-18 17:40:56] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -44.224684+0.000292j
[2025-09-18 17:41:09] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -44.225495+0.001023j
[2025-09-18 17:41:21] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -44.230788+0.000728j
[2025-09-18 17:41:34] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -44.230059+0.000974j
[2025-09-18 17:41:46] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -44.215257+0.002163j
[2025-09-18 17:41:59] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -44.215545+0.001523j
[2025-09-18 17:42:11] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -44.225601-0.001986j
[2025-09-18 17:42:24] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -44.214400+0.000223j
[2025-09-18 17:42:36] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -44.222051-0.001387j
[2025-09-18 17:42:49] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -44.231770+0.004578j
[2025-09-18 17:43:02] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -44.228098+0.000760j
[2025-09-18 17:43:14] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -44.215362-0.001280j
[2025-09-18 17:43:27] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -44.213751+0.000172j
[2025-09-18 17:43:39] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -44.233641+0.000320j
[2025-09-18 17:43:52] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -44.229226-0.005634j
[2025-09-18 17:44:04] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -44.212224-0.000326j
[2025-09-18 17:44:17] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -44.219148+0.002154j
[2025-09-18 17:44:29] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -44.232249+0.003337j
[2025-09-18 17:44:42] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -44.222147+0.001447j
[2025-09-18 17:44:54] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -44.244287-0.004380j
[2025-09-18 17:45:07] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -44.221685+0.004258j
[2025-09-18 17:45:19] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -44.231803+0.004974j
[2025-09-18 17:45:31] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -44.232224+0.000706j
[2025-09-18 17:45:44] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -44.217275+0.005974j
[2025-09-18 17:45:56] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -44.234624+0.001697j
[2025-09-18 17:45:56] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-18 17:45:56] ✅ Training completed | Restarts: 2
[2025-09-18 17:45:56] ============================================================
[2025-09-18 17:45:56] Training completed | Runtime: 13172.1s
[2025-09-18 17:46:01] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-18 17:46:01] ============================================================
