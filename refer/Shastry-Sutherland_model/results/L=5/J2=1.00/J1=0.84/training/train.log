[2025-09-19 01:06:04] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.83/training/checkpoints/final_GCNN.pkl
[2025-09-19 01:06:04]   - 迭代次数: final
[2025-09-19 01:06:04]   - 能量: -46.795794+0.000064j ± 0.007756
[2025-09-19 01:06:04]   - 时间戳: 2025-09-19T01:05:22.751964+08:00
[2025-09-19 01:06:24] ✓ 变分状态参数已从checkpoint恢复
[2025-09-19 01:06:24] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-19 01:06:24] ==================================================
[2025-09-19 01:06:24] GCNN for Shastry-Sutherland Model
[2025-09-19 01:06:24] ==================================================
[2025-09-19 01:06:24] System parameters:
[2025-09-19 01:06:24]   - System size: L=5, N=100
[2025-09-19 01:06:24]   - System parameters: J1=0.84, J2=1.0, Q=0.0
[2025-09-19 01:06:24] --------------------------------------------------
[2025-09-19 01:06:24] Model parameters:
[2025-09-19 01:06:24]   - Number of layers = 4
[2025-09-19 01:06:24]   - Number of features = 4
[2025-09-19 01:06:24]   - Total parameters = 19628
[2025-09-19 01:06:24] --------------------------------------------------
[2025-09-19 01:06:24] Training parameters:
[2025-09-19 01:06:24]   - Learning rate: 0.015
[2025-09-19 01:06:24]   - Total iterations: 1050
[2025-09-19 01:06:24]   - Annealing cycles: 3
[2025-09-19 01:06:24]   - Initial period: 150
[2025-09-19 01:06:24]   - Period multiplier: 2.0
[2025-09-19 01:06:24]   - Temperature range: 0.0-1.0
[2025-09-19 01:06:24]   - Samples: 4096
[2025-09-19 01:06:24]   - Discarded samples: 0
[2025-09-19 01:06:24]   - Chunk size: 2048
[2025-09-19 01:06:24]   - Diagonal shift: 0.2
[2025-09-19 01:06:24]   - Gradient clipping: 1.0
[2025-09-19 01:06:24]   - Checkpoint enabled: interval=105
[2025-09-19 01:06:24]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.84/training/checkpoints
[2025-09-19 01:06:24] --------------------------------------------------
[2025-09-19 01:06:24] Device status:
[2025-09-19 01:06:24]   - Devices model: NVIDIA H200 NVL
[2025-09-19 01:06:24]   - Number of devices: 1
[2025-09-19 01:06:24]   - Sharding: True
[2025-09-19 01:06:24] ============================================================
[2025-09-19 01:07:21] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -47.433706+0.003827j
[2025-09-19 01:08:00] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -47.430702-0.001929j
[2025-09-19 01:08:13] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -47.448627+0.000523j
[2025-09-19 01:08:25] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -47.441862-0.004967j
[2025-09-19 01:08:38] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -47.438962-0.003754j
[2025-09-19 01:08:50] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -47.434790+0.002617j
[2025-09-19 01:09:02] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -47.434682+0.000750j
[2025-09-19 01:09:15] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -47.452344-0.009101j
[2025-09-19 01:09:27] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -47.448235-0.000472j
[2025-09-19 01:09:40] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -47.449028+0.000905j
[2025-09-19 01:09:52] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -47.450382+0.000507j
[2025-09-19 01:10:05] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -47.429211+0.001467j
[2025-09-19 01:10:17] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -47.449222-0.000571j
[2025-09-19 01:10:30] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -47.443553+0.000956j
[2025-09-19 01:10:42] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -47.446980+0.001294j
[2025-09-19 01:10:54] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -47.442807-0.001427j
[2025-09-19 01:11:07] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -47.443838+0.001259j
[2025-09-19 01:11:19] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -47.443100-0.000058j
[2025-09-19 01:11:32] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -47.458054-0.002039j
[2025-09-19 01:11:44] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -47.450012-0.001631j
[2025-09-19 01:11:56] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -47.453999+0.002214j
[2025-09-19 01:12:09] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -47.434550+0.000025j
[2025-09-19 01:12:21] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -47.455472+0.000027j
[2025-09-19 01:12:34] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -47.449806-0.001938j
[2025-09-19 01:12:46] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -47.449295+0.001073j
[2025-09-19 01:12:59] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -47.427335-0.001716j
[2025-09-19 01:13:11] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -47.454283-0.001707j
[2025-09-19 01:13:24] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -47.443428-0.000203j
[2025-09-19 01:13:36] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -47.449851-0.002180j
[2025-09-19 01:13:48] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -47.451434-0.002337j
[2025-09-19 01:14:01] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -47.439609-0.000451j
[2025-09-19 01:14:13] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -47.440025+0.000892j
[2025-09-19 01:14:26] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -47.453190+0.000606j
[2025-09-19 01:14:38] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -47.444460+0.000464j
[2025-09-19 01:14:50] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -47.446599-0.001392j
[2025-09-19 01:15:03] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -47.435619+0.000432j
[2025-09-19 01:15:15] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -47.443198+0.001644j
[2025-09-19 01:15:28] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -47.442579-0.004081j
[2025-09-19 01:15:40] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -47.449908+0.002009j
[2025-09-19 01:15:52] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -47.454619+0.001174j
[2025-09-19 01:16:05] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -47.455680+0.001053j
[2025-09-19 01:16:17] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -47.447863-0.002367j
[2025-09-19 01:16:29] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -47.426135+0.000281j
[2025-09-19 01:16:42] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -47.443569-0.002532j
[2025-09-19 01:16:54] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -47.439145+0.000839j
[2025-09-19 01:17:06] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -47.447130-0.002597j
[2025-09-19 01:17:19] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -47.451570+0.001382j
[2025-09-19 01:17:31] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -47.428744-0.000413j
[2025-09-19 01:17:44] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -47.425178-0.000608j
[2025-09-19 01:17:56] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -47.439392+0.000798j
[2025-09-19 01:18:08] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -47.437074+0.000800j
[2025-09-19 01:18:21] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -47.445583+0.002581j
[2025-09-19 01:18:33] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -47.450551-0.000850j
[2025-09-19 01:18:46] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -47.437346-0.000492j
[2025-09-19 01:18:58] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -47.454744+0.000560j
[2025-09-19 01:19:11] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -47.434061+0.001256j
[2025-09-19 01:19:23] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -47.445418+0.001278j
[2025-09-19 01:19:35] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -47.431760+0.003248j
[2025-09-19 01:19:48] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -47.445329-0.001703j
[2025-09-19 01:20:00] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -47.443466+0.001646j
[2025-09-19 01:20:13] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -47.435057+0.000525j
[2025-09-19 01:20:25] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -47.456494+0.001205j
[2025-09-19 01:20:37] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -47.434201+0.000496j
[2025-09-19 01:20:50] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -47.445559-0.000655j
[2025-09-19 01:21:02] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -47.438840-0.002423j
[2025-09-19 01:21:15] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -47.451512-0.001721j
[2025-09-19 01:21:27] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -47.439277-0.000602j
[2025-09-19 01:21:40] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -47.448762-0.001403j
[2025-09-19 01:21:52] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -47.449009+0.001826j
[2025-09-19 01:22:04] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -47.440304-0.001345j
[2025-09-19 01:22:17] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -47.452104+0.002363j
[2025-09-19 01:22:29] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -47.456707+0.002643j
[2025-09-19 01:22:42] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -47.448317-0.001543j
[2025-09-19 01:22:54] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -47.442086-0.005404j
[2025-09-19 01:23:07] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -47.444346-0.002405j
[2025-09-19 01:23:19] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -47.442358-0.001723j
[2025-09-19 01:23:31] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -47.449700+0.001750j
[2025-09-19 01:23:44] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -47.442621+0.001491j
[2025-09-19 01:23:56] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -47.438241+0.001329j
[2025-09-19 01:24:09] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -47.445315-0.001168j
[2025-09-19 01:24:21] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -47.432922+0.002373j
[2025-09-19 01:24:34] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -47.444207+0.000302j
[2025-09-19 01:24:46] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -47.438742-0.002513j
[2025-09-19 01:24:58] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -47.439175-0.000364j
[2025-09-19 01:25:11] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -47.450589-0.001757j
[2025-09-19 01:25:23] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -47.452319+0.001407j
[2025-09-19 01:25:36] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -47.455416+0.001801j
[2025-09-19 01:25:48] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -47.448394-0.002007j
[2025-09-19 01:26:01] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -47.448141-0.000711j
[2025-09-19 01:26:13] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -47.430358+0.000411j
[2025-09-19 01:26:26] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -47.438180+0.002228j
[2025-09-19 01:26:38] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -47.446577+0.000680j
[2025-09-19 01:26:50] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -47.447379+0.000846j
[2025-09-19 01:27:03] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -47.452853+0.000463j
[2025-09-19 01:27:15] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -47.459704+0.000532j
[2025-09-19 01:27:28] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -47.456059-0.000868j
[2025-09-19 01:27:40] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -47.448564-0.000963j
[2025-09-19 01:27:53] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -47.452203+0.000503j
[2025-09-19 01:28:05] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -47.457902+0.000906j
[2025-09-19 01:28:18] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -47.442651+0.002249j
[2025-09-19 01:28:30] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -47.445560-0.001927j
[2025-09-19 01:28:42] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -47.436791-0.003225j
[2025-09-19 01:28:55] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -47.442124-0.000500j
[2025-09-19 01:29:07] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -47.444896-0.001312j
[2025-09-19 01:29:20] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -47.439685+0.000307j
[2025-09-19 01:29:20] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-19 01:29:32] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -47.442898-0.001494j
[2025-09-19 01:29:45] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -47.437290-0.002688j
[2025-09-19 01:29:57] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -47.452290+0.001593j
[2025-09-19 01:30:10] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -47.443429-0.000604j
[2025-09-19 01:30:22] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -47.441060-0.000180j
[2025-09-19 01:30:34] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -47.441689+0.002726j
[2025-09-19 01:30:47] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -47.449827+0.001365j
[2025-09-19 01:30:59] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -47.454731-0.000983j
[2025-09-19 01:31:12] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -47.456681+0.001077j
[2025-09-19 01:31:24] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -47.446650-0.002815j
[2025-09-19 01:31:37] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -47.445776-0.000026j
[2025-09-19 01:31:49] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -47.439528-0.000814j
[2025-09-19 01:32:01] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -47.441041-0.002588j
[2025-09-19 01:32:14] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -47.445028+0.002483j
[2025-09-19 01:32:26] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -47.447900-0.001561j
[2025-09-19 01:32:39] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -47.456541-0.001689j
[2025-09-19 01:32:51] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -47.459049+0.000720j
[2025-09-19 01:33:03] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -47.440343+0.001991j
[2025-09-19 01:33:16] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -47.438999+0.002451j
[2025-09-19 01:33:28] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -47.451246+0.000677j
[2025-09-19 01:33:41] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -47.440590-0.000410j
[2025-09-19 01:33:53] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -47.428032-0.002221j
[2025-09-19 01:34:06] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -47.435086+0.001592j
[2025-09-19 01:34:18] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -47.439467-0.002273j
[2025-09-19 01:34:31] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -47.441462-0.002175j
[2025-09-19 01:34:43] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -47.437607+0.000870j
[2025-09-19 01:34:56] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -47.449139-0.000090j
[2025-09-19 01:35:08] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -47.448936-0.000614j
[2025-09-19 01:35:21] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -47.451334+0.000704j
[2025-09-19 01:35:33] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -47.441674-0.000348j
[2025-09-19 01:35:45] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -47.448450+0.000150j
[2025-09-19 01:35:58] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -47.451326-0.000116j
[2025-09-19 01:36:10] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -47.451729-0.003069j
[2025-09-19 01:36:22] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -47.440836-0.000996j
[2025-09-19 01:36:35] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -47.451674-0.002432j
[2025-09-19 01:36:47] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -47.445871+0.000742j
[2025-09-19 01:37:00] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -47.453351-0.001688j
[2025-09-19 01:37:12] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -47.444406-0.001683j
[2025-09-19 01:37:24] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -47.445139+0.000470j
[2025-09-19 01:37:37] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -47.437174-0.001122j
[2025-09-19 01:37:49] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -47.447951+0.001915j
[2025-09-19 01:38:01] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -47.455943-0.000874j
[2025-09-19 01:38:14] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -47.448465+0.001868j
[2025-09-19 01:38:26] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -47.443068-0.001759j
[2025-09-19 01:38:38] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -47.445192-0.000524j
[2025-09-19 01:38:38] RESTART #1 | Period: 300
[2025-09-19 01:38:51] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -47.439000-0.002766j
[2025-09-19 01:39:03] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -47.448569+0.000912j
[2025-09-19 01:39:16] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -47.447155+0.000759j
[2025-09-19 01:39:28] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -47.440189+0.001458j
[2025-09-19 01:39:40] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -47.456671+0.000264j
[2025-09-19 01:39:53] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -47.456780-0.000065j
[2025-09-19 01:40:05] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -47.447558-0.004632j
[2025-09-19 01:40:17] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -47.435023-0.000493j
[2025-09-19 01:40:30] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -47.447541-0.003815j
[2025-09-19 01:40:42] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -47.443614-0.000363j
[2025-09-19 01:40:54] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -47.437587-0.004980j
[2025-09-19 01:41:07] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -47.446734-0.001554j
[2025-09-19 01:41:19] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -47.448209+0.000699j
[2025-09-19 01:41:32] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -47.455456+0.000163j
[2025-09-19 01:41:44] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -47.451042+0.002628j
[2025-09-19 01:41:56] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -47.452028+0.002748j
[2025-09-19 01:42:09] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -47.455935-0.000922j
[2025-09-19 01:42:21] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -47.453206-0.000110j
[2025-09-19 01:42:33] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -47.453349-0.000805j
[2025-09-19 01:42:46] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -47.448263+0.002857j
[2025-09-19 01:42:58] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -47.446171+0.000152j
[2025-09-19 01:43:10] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -47.448069-0.003706j
[2025-09-19 01:43:23] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -47.443925-0.002139j
[2025-09-19 01:43:35] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -47.448112+0.000586j
[2025-09-19 01:43:48] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -47.448178+0.004250j
[2025-09-19 01:44:00] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -47.443394+0.001904j
[2025-09-19 01:44:12] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -47.443362-0.002573j
[2025-09-19 01:44:25] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -47.441287-0.000146j
[2025-09-19 01:44:37] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -47.442329+0.000742j
[2025-09-19 01:44:50] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -47.447643+0.004338j
[2025-09-19 01:45:02] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -47.442463-0.000290j
[2025-09-19 01:45:14] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -47.430564-0.001188j
[2025-09-19 01:45:27] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -47.450535-0.000584j
[2025-09-19 01:45:39] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -47.450005+0.000476j
[2025-09-19 01:45:51] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -47.454490-0.000417j
[2025-09-19 01:46:04] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -47.444451+0.004362j
[2025-09-19 01:46:16] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -47.442083-0.001242j
[2025-09-19 01:46:29] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -47.436078+0.003902j
[2025-09-19 01:46:41] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -47.452447-0.000870j
[2025-09-19 01:46:53] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -47.443993+0.000386j
[2025-09-19 01:47:06] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -47.454336-0.003609j
[2025-09-19 01:47:18] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -47.448811+0.003086j
[2025-09-19 01:47:30] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -47.450722+0.003987j
[2025-09-19 01:47:43] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -47.435623-0.002591j
[2025-09-19 01:47:55] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -47.444311-0.000850j
[2025-09-19 01:48:08] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -47.427404-0.001332j
[2025-09-19 01:48:20] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -47.442439+0.000245j
[2025-09-19 01:48:32] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -47.450491+0.000120j
[2025-09-19 01:48:45] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -47.451159-0.004829j
[2025-09-19 01:48:57] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -47.454700-0.002265j
[2025-09-19 01:49:10] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -47.452056-0.001049j
[2025-09-19 01:49:22] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -47.457448+0.000781j
[2025-09-19 01:49:35] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -47.441950-0.001604j
[2025-09-19 01:49:47] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -47.447426-0.002590j
[2025-09-19 01:49:59] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -47.452886-0.003322j
[2025-09-19 01:50:12] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -47.456556+0.001052j
[2025-09-19 01:50:25] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -47.436495+0.001224j
[2025-09-19 01:50:37] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -47.459080+0.002760j
[2025-09-19 01:50:49] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -47.451773-0.000608j
[2025-09-19 01:51:02] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -47.446814-0.000348j
[2025-09-19 01:51:02] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-19 01:51:14] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -47.449334+0.001144j
[2025-09-19 01:51:27] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -47.455626+0.001082j
[2025-09-19 01:51:39] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -47.444907+0.002229j
[2025-09-19 01:51:52] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -47.440326+0.002636j
[2025-09-19 01:52:04] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -47.440504+0.001861j
[2025-09-19 01:52:16] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -47.443179+0.000338j
[2025-09-19 01:52:29] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -47.446650-0.001329j
[2025-09-19 01:52:41] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -47.449340+0.002550j
[2025-09-19 01:52:54] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -47.443041-0.000101j
[2025-09-19 01:53:06] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -47.456123-0.002095j
[2025-09-19 01:53:19] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -47.442453-0.001090j
[2025-09-19 01:53:31] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -47.448145+0.002305j
[2025-09-19 01:53:44] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -47.457421+0.004404j
[2025-09-19 01:53:56] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -47.443936+0.000271j
[2025-09-19 01:54:08] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -47.454593+0.000172j
[2025-09-19 01:54:21] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -47.439446-0.000129j
[2025-09-19 01:54:33] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -47.444972+0.000662j
[2025-09-19 01:54:46] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -47.439995+0.002404j
[2025-09-19 01:54:58] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -47.429258+0.002466j
[2025-09-19 01:55:11] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -47.448942-0.004143j
[2025-09-19 01:55:23] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -47.442580+0.001275j
[2025-09-19 01:55:35] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -47.439390+0.002044j
[2025-09-19 01:55:48] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -47.438432+0.001518j
[2025-09-19 01:56:00] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -47.446565-0.002392j
[2025-09-19 01:56:13] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -47.443893+0.001432j
[2025-09-19 01:56:25] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -47.449980+0.001489j
[2025-09-19 01:56:38] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -47.439751+0.001669j
[2025-09-19 01:56:50] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -47.431648-0.001160j
[2025-09-19 01:57:02] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -47.454351+0.002117j
[2025-09-19 01:57:15] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -47.457002+0.003412j
[2025-09-19 01:57:27] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -47.445108+0.002586j
[2025-09-19 01:57:40] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -47.456054-0.001188j
[2025-09-19 01:57:52] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -47.444944+0.004574j
[2025-09-19 01:58:04] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -47.438192-0.002149j
[2025-09-19 01:58:17] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -47.439146-0.002520j
[2025-09-19 01:58:29] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -47.449573-0.002468j
[2025-09-19 01:58:42] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -47.440842+0.002328j
[2025-09-19 01:58:54] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -47.452467+0.000588j
[2025-09-19 01:59:06] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -47.443536-0.000520j
[2025-09-19 01:59:19] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -47.449387+0.002170j
[2025-09-19 01:59:31] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -47.445531+0.012918j
[2025-09-19 01:59:44] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -47.446033+0.000998j
[2025-09-19 01:59:56] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -47.439398-0.003441j
[2025-09-19 02:00:09] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -47.444677-0.002479j
[2025-09-19 02:00:21] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -47.451775+0.001844j
[2025-09-19 02:00:33] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -47.444739+0.000684j
[2025-09-19 02:00:46] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -47.448534-0.001425j
[2025-09-19 02:00:58] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -47.443564+0.000114j
[2025-09-19 02:01:11] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -47.448514-0.001329j
[2025-09-19 02:01:23] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -47.446517-0.001321j
[2025-09-19 02:01:36] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -47.452943+0.001430j
[2025-09-19 02:01:48] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -47.466321+0.001408j
[2025-09-19 02:02:00] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -47.457331+0.001631j
[2025-09-19 02:02:13] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -47.447939-0.001338j
[2025-09-19 02:02:25] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -47.448686+0.004429j
[2025-09-19 02:02:38] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -47.452547+0.003517j
[2025-09-19 02:02:50] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -47.440594+0.001695j
[2025-09-19 02:03:02] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -47.440901+0.000680j
[2025-09-19 02:03:15] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -47.435503+0.002447j
[2025-09-19 02:03:27] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -47.449324-0.001675j
[2025-09-19 02:03:40] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -47.448162+0.001597j
[2025-09-19 02:03:52] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -47.458950-0.001531j
[2025-09-19 02:04:05] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -47.432129-0.001220j
[2025-09-19 02:04:17] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -47.445924-0.001537j
[2025-09-19 02:04:29] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -47.455773-0.002913j
[2025-09-19 02:04:42] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -47.442095-0.001078j
[2025-09-19 02:04:54] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -47.451848+0.000469j
[2025-09-19 02:05:07] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -47.451527-0.000376j
[2025-09-19 02:05:19] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -47.446583+0.002417j
[2025-09-19 02:05:32] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -47.460240-0.001186j
[2025-09-19 02:05:44] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -47.448554-0.002985j
[2025-09-19 02:05:56] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -47.436603-0.003319j
[2025-09-19 02:06:09] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -47.437372+0.003081j
[2025-09-19 02:06:21] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -47.443931-0.000635j
[2025-09-19 02:06:34] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -47.439895-0.003101j
[2025-09-19 02:06:46] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -47.451300-0.000978j
[2025-09-19 02:06:58] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -47.456859+0.003123j
[2025-09-19 02:07:11] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -47.449451-0.000514j
[2025-09-19 02:07:23] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -47.445051+0.000860j
[2025-09-19 02:07:36] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -47.446497-0.002274j
[2025-09-19 02:07:48] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -47.449261+0.002056j
[2025-09-19 02:08:01] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -47.448446-0.002533j
[2025-09-19 02:08:13] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -47.433790-0.003995j
[2025-09-19 02:08:26] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -47.434663-0.000170j
[2025-09-19 02:08:38] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -47.437348-0.000383j
[2025-09-19 02:08:50] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -47.443732+0.004754j
[2025-09-19 02:09:03] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -47.456159+0.000642j
[2025-09-19 02:09:15] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -47.442697-0.000712j
[2025-09-19 02:09:28] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -47.440914-0.000116j
[2025-09-19 02:09:40] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -47.436805+0.000024j
[2025-09-19 02:09:53] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -47.437829+0.001414j
[2025-09-19 02:10:05] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -47.428166+0.001904j
[2025-09-19 02:10:17] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -47.458197+0.000955j
[2025-09-19 02:10:30] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -47.447106+0.003020j
[2025-09-19 02:10:42] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -47.449851+0.000061j
[2025-09-19 02:10:55] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -47.455370+0.000586j
[2025-09-19 02:11:07] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -47.445143-0.000696j
[2025-09-19 02:11:20] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -47.457996-0.002482j
[2025-09-19 02:11:32] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -47.445190-0.001687j
[2025-09-19 02:11:44] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -47.450892+0.001252j
[2025-09-19 02:11:57] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -47.437614+0.003461j
[2025-09-19 02:12:09] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -47.447469+0.003549j
[2025-09-19 02:12:22] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -47.440081-0.002541j
[2025-09-19 02:12:34] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -47.452773-0.003753j
[2025-09-19 02:12:46] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -47.450398+0.001289j
[2025-09-19 02:12:47] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-19 02:12:59] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -47.443512-0.000772j
[2025-09-19 02:13:11] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -47.448679-0.001259j
[2025-09-19 02:13:24] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -47.450831+0.001252j
[2025-09-19 02:13:36] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -47.450861+0.000685j
[2025-09-19 02:13:49] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -47.445421+0.002306j
[2025-09-19 02:14:01] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -47.456039-0.001290j
[2025-09-19 02:14:13] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -47.454082+0.002491j
[2025-09-19 02:14:26] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -47.460990-0.003380j
[2025-09-19 02:14:38] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -47.449130+0.001169j
[2025-09-19 02:14:51] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -47.444933+0.001344j
[2025-09-19 02:15:03] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -47.459085-0.000291j
[2025-09-19 02:15:16] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -47.448125-0.002561j
[2025-09-19 02:15:28] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -47.428126+0.009260j
[2025-09-19 02:15:40] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -47.433625+0.000364j
[2025-09-19 02:15:53] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -47.447549+0.001113j
[2025-09-19 02:16:05] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -47.443125+0.001432j
[2025-09-19 02:16:18] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -47.441713-0.002184j
[2025-09-19 02:16:30] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -47.463168-0.001327j
[2025-09-19 02:16:43] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -47.445711+0.000253j
[2025-09-19 02:16:55] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -47.449995+0.000038j
[2025-09-19 02:17:07] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -47.450801+0.000519j
[2025-09-19 02:17:20] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -47.454226-0.001321j
[2025-09-19 02:17:32] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -47.460161+0.001052j
[2025-09-19 02:17:45] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -47.447799-0.003141j
[2025-09-19 02:17:57] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -47.442836+0.002533j
[2025-09-19 02:18:10] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -47.439719+0.002354j
[2025-09-19 02:18:22] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -47.441265+0.001595j
[2025-09-19 02:18:34] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -47.437545-0.004269j
[2025-09-19 02:18:47] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -47.449699+0.003674j
[2025-09-19 02:18:59] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -47.443763+0.001982j
[2025-09-19 02:19:12] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -47.440552-0.003051j
[2025-09-19 02:19:24] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -47.450334-0.002326j
[2025-09-19 02:19:36] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -47.448859+0.000021j
[2025-09-19 02:19:49] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -47.446590+0.004566j
[2025-09-19 02:20:01] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -47.452394+0.002268j
[2025-09-19 02:20:14] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -47.448069-0.000699j
[2025-09-19 02:20:26] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -47.439007+0.001253j
[2025-09-19 02:20:39] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -47.446490-0.001609j
[2025-09-19 02:20:51] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -47.460658-0.000123j
[2025-09-19 02:21:03] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -47.456346-0.005274j
[2025-09-19 02:21:16] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -47.436625+0.002038j
[2025-09-19 02:21:28] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -47.454212+0.001063j
[2025-09-19 02:21:41] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -47.445803+0.003647j
[2025-09-19 02:21:53] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -47.447850-0.001629j
[2025-09-19 02:22:06] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -47.449857-0.002658j
[2025-09-19 02:22:18] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -47.446011-0.002048j
[2025-09-19 02:22:30] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -47.436079-0.001098j
[2025-09-19 02:22:43] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -47.441819+0.002728j
[2025-09-19 02:22:55] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -47.439733+0.002141j
[2025-09-19 02:23:08] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -47.449507-0.002290j
[2025-09-19 02:23:20] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -47.440637+0.000584j
[2025-09-19 02:23:33] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -47.447523-0.001400j
[2025-09-19 02:23:45] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -47.452801+0.002825j
[2025-09-19 02:23:57] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -47.453513-0.002489j
[2025-09-19 02:24:10] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -47.448310-0.000934j
[2025-09-19 02:24:22] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -47.433599+0.003955j
[2025-09-19 02:24:35] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -47.457525+0.001496j
[2025-09-19 02:24:47] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -47.448273+0.000603j
[2025-09-19 02:24:59] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -47.439916-0.000418j
[2025-09-19 02:25:12] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -47.458054+0.000519j
[2025-09-19 02:25:24] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -47.457229+0.000330j
[2025-09-19 02:25:37] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -47.435018-0.002997j
[2025-09-19 02:25:49] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -47.441826-0.001020j
[2025-09-19 02:26:02] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -47.444244-0.001925j
[2025-09-19 02:26:14] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -47.456785+0.002649j
[2025-09-19 02:26:26] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -47.451106+0.001410j
[2025-09-19 02:26:39] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -47.452005+0.001620j
[2025-09-19 02:26:51] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -47.452717-0.000085j
[2025-09-19 02:27:04] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -47.437947+0.000474j
[2025-09-19 02:27:16] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -47.456819-0.000457j
[2025-09-19 02:27:28] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -47.447536-0.000694j
[2025-09-19 02:27:41] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -47.434367+0.000482j
[2025-09-19 02:27:53] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -47.442889-0.003288j
[2025-09-19 02:28:06] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -47.440076-0.000751j
[2025-09-19 02:28:18] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -47.461801-0.000151j
[2025-09-19 02:28:31] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -47.426476+0.003022j
[2025-09-19 02:28:43] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -47.442256-0.000382j
[2025-09-19 02:28:56] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -47.438223+0.000990j
[2025-09-19 02:29:08] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -47.436232+0.000071j
[2025-09-19 02:29:20] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -47.455510-0.001826j
[2025-09-19 02:29:33] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -47.452013+0.000863j
[2025-09-19 02:29:45] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -47.431822-0.001557j
[2025-09-19 02:29:58] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -47.444745+0.001008j
[2025-09-19 02:30:10] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -47.446502-0.003250j
[2025-09-19 02:30:23] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -47.454216-0.000197j
[2025-09-19 02:30:35] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -47.456962-0.000250j
[2025-09-19 02:30:47] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -47.448279-0.002368j
[2025-09-19 02:31:00] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -47.441498-0.001602j
[2025-09-19 02:31:12] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -47.457827-0.002184j
[2025-09-19 02:31:25] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -47.453710+0.001881j
[2025-09-19 02:31:37] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -47.456196-0.000579j
[2025-09-19 02:31:50] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -47.441322+0.000051j
[2025-09-19 02:32:02] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -47.438451+0.002887j
[2025-09-19 02:32:14] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -47.449235+0.002293j
[2025-09-19 02:32:27] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -47.437565+0.001110j
[2025-09-19 02:32:39] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -47.448149+0.003256j
[2025-09-19 02:32:52] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -47.449734-0.002651j
[2025-09-19 02:33:04] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -47.442466-0.001713j
[2025-09-19 02:33:17] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -47.448502+0.002780j
[2025-09-19 02:33:29] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -47.439579-0.003394j
[2025-09-19 02:33:41] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -47.452528+0.001087j
[2025-09-19 02:33:54] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -47.457910+0.000931j
[2025-09-19 02:34:06] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -47.446043+0.003929j
[2025-09-19 02:34:19] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -47.444312+0.003271j
[2025-09-19 02:34:31] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -47.439930-0.005202j
[2025-09-19 02:34:31] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-19 02:34:44] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -47.443814+0.002550j
[2025-09-19 02:34:56] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -47.440531-0.000862j
[2025-09-19 02:35:09] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -47.442978-0.003429j
[2025-09-19 02:35:21] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -47.446616-0.000667j
[2025-09-19 02:35:34] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -47.455182+0.002852j
[2025-09-19 02:35:46] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -47.453665-0.002141j
[2025-09-19 02:35:58] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -47.443221+0.001629j
[2025-09-19 02:36:11] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -47.449236+0.000113j
[2025-09-19 02:36:23] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -47.437251-0.002835j
[2025-09-19 02:36:36] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -47.449988-0.000156j
[2025-09-19 02:36:48] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -47.440307-0.000719j
[2025-09-19 02:37:01] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -47.437992+0.004010j
[2025-09-19 02:37:13] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -47.449655-0.001047j
[2025-09-19 02:37:25] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -47.443890-0.000026j
[2025-09-19 02:37:38] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -47.454143-0.000900j
[2025-09-19 02:37:50] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -47.446655+0.004052j
[2025-09-19 02:38:03] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -47.442856-0.000762j
[2025-09-19 02:38:15] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -47.440968-0.000406j
[2025-09-19 02:38:27] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -47.448900-0.001931j
[2025-09-19 02:38:40] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -47.445488+0.000226j
[2025-09-19 02:38:52] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -47.455020+0.000040j
[2025-09-19 02:39:05] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -47.447698+0.004893j
[2025-09-19 02:39:17] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -47.442994-0.002815j
[2025-09-19 02:39:30] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -47.444483-0.002673j
[2025-09-19 02:39:42] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -47.449545+0.000298j
[2025-09-19 02:39:54] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -47.442377+0.004104j
[2025-09-19 02:40:07] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -47.454724-0.002848j
[2025-09-19 02:40:19] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -47.451793+0.002017j
[2025-09-19 02:40:32] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -47.443254-0.000843j
[2025-09-19 02:40:44] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -47.456562-0.000603j
[2025-09-19 02:40:44] RESTART #2 | Period: 600
[2025-09-19 02:40:57] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -47.441555-0.000853j
[2025-09-19 02:41:09] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -47.451722-0.003619j
[2025-09-19 02:41:21] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -47.438238-0.001675j
[2025-09-19 02:41:34] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -47.432050+0.001858j
[2025-09-19 02:41:46] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -47.434225+0.002250j
[2025-09-19 02:41:59] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -47.435433+0.003181j
[2025-09-19 02:42:11] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -47.448318+0.001210j
[2025-09-19 02:42:24] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -47.458475+0.000269j
[2025-09-19 02:42:36] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -47.456175-0.001965j
[2025-09-19 02:42:48] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -47.436188-0.002699j
[2025-09-19 02:43:01] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -47.460339+0.002384j
[2025-09-19 02:43:13] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -47.449598+0.001899j
[2025-09-19 02:43:26] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -47.444926+0.003782j
[2025-09-19 02:43:38] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -47.446576+0.001619j
[2025-09-19 02:43:50] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -47.447435-0.000650j
[2025-09-19 02:44:03] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -47.445401+0.001451j
[2025-09-19 02:44:15] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -47.448608+0.000836j
[2025-09-19 02:44:28] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -47.436648-0.000033j
[2025-09-19 02:44:40] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -47.443278+0.001122j
[2025-09-19 02:44:53] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -47.451038-0.001356j
[2025-09-19 02:45:05] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -47.451976-0.003745j
[2025-09-19 02:45:17] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -47.430620-0.001549j
[2025-09-19 02:45:30] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -47.460174+0.003246j
[2025-09-19 02:45:42] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -47.448380-0.002401j
[2025-09-19 02:45:55] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -47.443712+0.003562j
[2025-09-19 02:46:07] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -47.448756+0.003320j
[2025-09-19 02:46:20] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -47.441372-0.001500j
[2025-09-19 02:46:32] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -47.447629+0.001518j
[2025-09-19 02:46:45] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -47.446555-0.000587j
[2025-09-19 02:46:57] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -47.440120-0.000724j
[2025-09-19 02:47:09] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -47.443061+0.001282j
[2025-09-19 02:47:22] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -47.448335+0.000161j
[2025-09-19 02:47:34] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -47.443526-0.004419j
[2025-09-19 02:47:47] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -47.439039+0.000323j
[2025-09-19 02:47:59] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -47.451267+0.000355j
[2025-09-19 02:48:11] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -47.440421+0.001432j
[2025-09-19 02:48:24] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -47.427800+0.004212j
[2025-09-19 02:48:36] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -47.451772-0.000330j
[2025-09-19 02:48:49] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -47.440657+0.000792j
[2025-09-19 02:49:01] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -47.449983-0.000560j
[2025-09-19 02:49:14] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -47.442366+0.001209j
[2025-09-19 02:49:26] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -47.441231-0.001730j
[2025-09-19 02:49:38] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -47.434785+0.002433j
[2025-09-19 02:49:51] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -47.446867-0.001328j
[2025-09-19 02:50:03] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -47.447517+0.002827j
[2025-09-19 02:50:16] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -47.431583+0.000497j
[2025-09-19 02:50:28] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -47.448778+0.000949j
[2025-09-19 02:50:41] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -47.446648+0.004051j
[2025-09-19 02:50:53] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -47.450172-0.000609j
[2025-09-19 02:51:05] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -47.438599-0.004802j
[2025-09-19 02:51:18] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -47.450481+0.000443j
[2025-09-19 02:51:30] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -47.444300-0.000008j
[2025-09-19 02:51:43] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -47.457635+0.000379j
[2025-09-19 02:51:55] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -47.442154+0.003266j
[2025-09-19 02:52:07] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -47.438525-0.001845j
[2025-09-19 02:52:20] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -47.425008-0.002192j
[2025-09-19 02:52:33] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -47.455554+0.003217j
[2025-09-19 02:52:45] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -47.444618-0.000674j
[2025-09-19 02:52:58] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -47.441146+0.002611j
[2025-09-19 02:53:10] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -47.447585+0.002736j
[2025-09-19 02:53:23] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -47.442481-0.003591j
[2025-09-19 02:53:35] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -47.453983+0.001581j
[2025-09-19 02:53:48] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -47.448553+0.002062j
[2025-09-19 02:54:01] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -47.450744-0.004001j
[2025-09-19 02:54:14] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -47.449924-0.002881j
[2025-09-19 02:54:26] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -47.437959-0.000660j
[2025-09-19 02:54:38] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -47.445203+0.000133j
[2025-09-19 02:54:51] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -47.438812+0.001603j
[2025-09-19 02:55:03] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -47.450584+0.000842j
[2025-09-19 02:55:16] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -47.441944-0.000681j
[2025-09-19 02:55:28] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -47.444164+0.002623j
[2025-09-19 02:55:40] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -47.441457-0.001156j
[2025-09-19 02:55:53] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -47.449255+0.001932j
[2025-09-19 02:56:05] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -47.453475+0.003474j
[2025-09-19 02:56:18] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -47.457405+0.003265j
[2025-09-19 02:56:18] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-19 02:56:30] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -47.444034+0.003846j
[2025-09-19 02:56:43] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -47.437836-0.003552j
[2025-09-19 02:56:55] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -47.439228-0.003550j
[2025-09-19 02:57:07] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -47.442323+0.000946j
[2025-09-19 02:57:20] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -47.454581-0.000421j
[2025-09-19 02:57:32] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -47.450828+0.001570j
[2025-09-19 02:57:45] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -47.454147+0.000892j
[2025-09-19 02:57:57] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -47.451861+0.001039j
[2025-09-19 02:58:10] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -47.444250-0.000966j
[2025-09-19 02:58:22] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -47.454441-0.000212j
[2025-09-19 02:58:34] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -47.459239+0.001005j
[2025-09-19 02:58:47] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -47.446780-0.002713j
[2025-09-19 02:58:59] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -47.445681+0.002267j
[2025-09-19 02:59:12] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -47.438842-0.001783j
[2025-09-19 02:59:24] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -47.440792+0.000195j
[2025-09-19 02:59:37] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -47.444660+0.001155j
[2025-09-19 02:59:49] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -47.448991+0.001995j
[2025-09-19 03:00:01] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -47.438121+0.001141j
[2025-09-19 03:00:14] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -47.442222-0.006546j
[2025-09-19 03:00:26] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -47.467917+0.001136j
[2025-09-19 03:00:39] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -47.438243-0.002543j
[2025-09-19 03:00:51] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -47.441063-0.001111j
[2025-09-19 03:01:04] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -47.441530-0.000282j
[2025-09-19 03:01:16] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -47.436571-0.000147j
[2025-09-19 03:01:28] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -47.449502-0.000368j
[2025-09-19 03:01:41] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -47.437728+0.000208j
[2025-09-19 03:01:53] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -47.452889+0.002078j
[2025-09-19 03:02:06] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -47.446623-0.001646j
[2025-09-19 03:02:18] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -47.441975+0.002158j
[2025-09-19 03:02:31] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -47.446048+0.000286j
[2025-09-19 03:02:43] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -47.446822-0.000165j
[2025-09-19 03:02:55] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -47.448892-0.000198j
[2025-09-19 03:03:08] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -47.450388-0.001548j
[2025-09-19 03:03:20] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -47.448235+0.004055j
[2025-09-19 03:03:33] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -47.440178-0.001439j
[2025-09-19 03:03:45] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -47.447731-0.003206j
[2025-09-19 03:03:58] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -47.444800+0.002085j
[2025-09-19 03:04:10] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -47.450574-0.001845j
[2025-09-19 03:04:22] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -47.449108-0.000009j
[2025-09-19 03:04:35] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -47.441709+0.001233j
[2025-09-19 03:04:47] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -47.446021-0.000554j
[2025-09-19 03:05:00] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -47.453762-0.000135j
[2025-09-19 03:05:12] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -47.439751+0.000884j
[2025-09-19 03:05:25] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -47.455230-0.002313j
[2025-09-19 03:05:37] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -47.449404-0.001214j
[2025-09-19 03:05:50] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -47.450627-0.001229j
[2025-09-19 03:06:02] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -47.443225+0.001482j
[2025-09-19 03:06:14] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -47.441479+0.001217j
[2025-09-19 03:06:27] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -47.454044+0.000435j
[2025-09-19 03:06:39] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -47.457227-0.002415j
[2025-09-19 03:06:52] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -47.443113+0.001142j
[2025-09-19 03:07:04] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -47.460113-0.001339j
[2025-09-19 03:07:16] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -47.444371+0.001354j
[2025-09-19 03:07:29] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -47.445636+0.001677j
[2025-09-19 03:07:41] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -47.447065-0.002335j
[2025-09-19 03:07:54] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -47.451443+0.000984j
[2025-09-19 03:08:06] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -47.446762-0.001848j
[2025-09-19 03:08:19] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -47.449808-0.000369j
[2025-09-19 03:08:31] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -47.448062-0.000600j
[2025-09-19 03:08:43] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -47.444825-0.004116j
[2025-09-19 03:08:56] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -47.460044-0.002476j
[2025-09-19 03:09:08] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -47.431810-0.001405j
[2025-09-19 03:09:21] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -47.458078-0.000663j
[2025-09-19 03:09:33] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -47.450739-0.002300j
[2025-09-19 03:09:46] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -47.443439+0.000418j
[2025-09-19 03:09:58] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -47.446420+0.000645j
[2025-09-19 03:10:10] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -47.456180-0.001504j
[2025-09-19 03:10:23] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -47.452335-0.002710j
[2025-09-19 03:10:35] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -47.435694-0.002637j
[2025-09-19 03:10:48] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -47.450186-0.000603j
[2025-09-19 03:11:00] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -47.440151-0.000597j
[2025-09-19 03:11:13] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -47.440506+0.000786j
[2025-09-19 03:11:25] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -47.437841-0.001009j
[2025-09-19 03:11:37] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -47.450889-0.002245j
[2025-09-19 03:11:50] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -47.447852-0.000237j
[2025-09-19 03:12:02] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -47.433048+0.000982j
[2025-09-19 03:12:14] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -47.454513-0.001221j
[2025-09-19 03:12:27] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -47.442109+0.000341j
[2025-09-19 03:12:39] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -47.451426+0.002165j
[2025-09-19 03:12:52] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -47.460652-0.000180j
[2025-09-19 03:13:04] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -47.440942-0.002528j
[2025-09-19 03:13:17] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -47.449030-0.000418j
[2025-09-19 03:13:29] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -47.453547-0.002180j
[2025-09-19 03:13:41] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -47.456561-0.001274j
[2025-09-19 03:13:54] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -47.447935+0.003635j
[2025-09-19 03:14:06] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -47.447472+0.001221j
[2025-09-19 03:14:19] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -47.442983+0.001287j
[2025-09-19 03:14:31] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -47.460095-0.002055j
[2025-09-19 03:14:44] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -47.448225+0.001264j
[2025-09-19 03:14:56] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -47.452186-0.001126j
[2025-09-19 03:15:08] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -47.443385+0.000331j
[2025-09-19 03:15:21] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -47.452522-0.000746j
[2025-09-19 03:15:33] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -47.453110-0.002265j
[2025-09-19 03:15:46] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -47.449091+0.001228j
[2025-09-19 03:15:58] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -47.444443+0.000508j
[2025-09-19 03:16:11] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -47.449105-0.001444j
[2025-09-19 03:16:23] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -47.447609+0.001464j
[2025-09-19 03:16:36] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -47.455687-0.000485j
[2025-09-19 03:16:48] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -47.446771+0.004961j
[2025-09-19 03:17:00] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -47.446878-0.002719j
[2025-09-19 03:17:13] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -47.446412-0.002055j
[2025-09-19 03:17:25] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -47.443453+0.005370j
[2025-09-19 03:17:38] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -47.447384+0.004030j
[2025-09-19 03:17:50] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -47.444886+0.000098j
[2025-09-19 03:18:02] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -47.462141-0.003909j
[2025-09-19 03:18:03] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-19 03:18:15] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -47.448994-0.003903j
[2025-09-19 03:18:27] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -47.452584-0.003675j
[2025-09-19 03:18:40] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -47.436003+0.003849j
[2025-09-19 03:18:52] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -47.440508-0.001252j
[2025-09-19 03:19:05] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -47.455167-0.000725j
[2025-09-19 03:19:17] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -47.457595+0.002417j
[2025-09-19 03:19:30] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -47.447248+0.000823j
[2025-09-19 03:19:42] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -47.443587-0.003735j
[2025-09-19 03:19:54] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -47.449038+0.002358j
[2025-09-19 03:20:07] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -47.441208+0.000607j
[2025-09-19 03:20:19] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -47.449743+0.000365j
[2025-09-19 03:20:32] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -47.445363-0.001951j
[2025-09-19 03:20:44] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -47.450944+0.004346j
[2025-09-19 03:20:56] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -47.443365-0.000108j
[2025-09-19 03:21:09] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -47.446860-0.005711j
[2025-09-19 03:21:21] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -47.454397+0.001044j
[2025-09-19 03:21:34] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -47.439966-0.001821j
[2025-09-19 03:21:46] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -47.442023-0.000383j
[2025-09-19 03:21:59] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -47.448927+0.001040j
[2025-09-19 03:22:11] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -47.453144+0.001377j
[2025-09-19 03:22:23] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -47.449058+0.000063j
[2025-09-19 03:22:36] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -47.439597+0.002282j
[2025-09-19 03:22:48] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -47.449962-0.002518j
[2025-09-19 03:23:01] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -47.449660-0.002267j
[2025-09-19 03:23:13] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -47.446101-0.002260j
[2025-09-19 03:23:26] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -47.446093+0.004230j
[2025-09-19 03:23:38] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -47.438421+0.000988j
[2025-09-19 03:23:50] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -47.452061-0.004874j
[2025-09-19 03:24:03] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -47.448781-0.000907j
[2025-09-19 03:24:15] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -47.442110-0.000253j
[2025-09-19 03:24:28] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -47.443381+0.000730j
[2025-09-19 03:24:40] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -47.456025+0.001066j
[2025-09-19 03:24:53] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -47.447014+0.003641j
[2025-09-19 03:25:05] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -47.446624+0.002137j
[2025-09-19 03:25:17] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -47.461574-0.002113j
[2025-09-19 03:25:30] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -47.438540+0.001341j
[2025-09-19 03:25:42] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -47.442067+0.002245j
[2025-09-19 03:25:55] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -47.443489-0.001597j
[2025-09-19 03:26:07] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -47.448517+0.003127j
[2025-09-19 03:26:19] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -47.444284+0.000633j
[2025-09-19 03:26:32] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -47.441669+0.002285j
[2025-09-19 03:26:44] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -47.441488-0.003187j
[2025-09-19 03:26:57] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -47.440738+0.005234j
[2025-09-19 03:27:09] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -47.449519-0.001598j
[2025-09-19 03:27:22] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -47.451084-0.012953j
[2025-09-19 03:27:34] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -47.442571+0.000506j
[2025-09-19 03:27:46] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -47.457395-0.002934j
[2025-09-19 03:27:59] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -47.445720-0.002184j
[2025-09-19 03:28:11] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -47.447587+0.001086j
[2025-09-19 03:28:24] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -47.455226-0.006633j
[2025-09-19 03:28:36] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -47.439785+0.000970j
[2025-09-19 03:28:48] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -47.442070+0.000338j
[2025-09-19 03:29:01] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -47.448471+0.002894j
[2025-09-19 03:29:13] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -47.458671-0.001178j
[2025-09-19 03:29:26] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -47.445168+0.000272j
[2025-09-19 03:29:38] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -47.443204-0.002057j
[2025-09-19 03:29:50] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -47.439487+0.001416j
[2025-09-19 03:30:03] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -47.451882-0.000078j
[2025-09-19 03:30:15] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -47.455383-0.002414j
[2025-09-19 03:30:28] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -47.448677+0.000659j
[2025-09-19 03:30:40] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -47.431165+0.000495j
[2025-09-19 03:30:53] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -47.455056+0.003718j
[2025-09-19 03:31:05] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -47.442611+0.000682j
[2025-09-19 03:31:17] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -47.443609+0.002991j
[2025-09-19 03:31:30] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -47.441175+0.000321j
[2025-09-19 03:31:42] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -47.441070-0.000117j
[2025-09-19 03:31:55] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -47.439273+0.001807j
[2025-09-19 03:32:07] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -47.452383-0.006053j
[2025-09-19 03:32:19] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -47.458304+0.004911j
[2025-09-19 03:32:32] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -47.441853+0.003222j
[2025-09-19 03:32:44] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -47.446289+0.001352j
[2025-09-19 03:32:57] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -47.440416-0.002997j
[2025-09-19 03:33:09] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -47.451129+0.001887j
[2025-09-19 03:33:22] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -47.452450+0.004331j
[2025-09-19 03:33:34] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -47.438999+0.001342j
[2025-09-19 03:33:47] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -47.446216+0.002238j
[2025-09-19 03:33:59] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -47.463242+0.002219j
[2025-09-19 03:34:11] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -47.442298+0.000953j
[2025-09-19 03:34:24] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -47.450938+0.000071j
[2025-09-19 03:34:36] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -47.451153+0.002558j
[2025-09-19 03:34:49] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -47.443045+0.000857j
[2025-09-19 03:35:01] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -47.449567-0.000735j
[2025-09-19 03:35:14] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -47.451673-0.001453j
[2025-09-19 03:35:26] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -47.450248-0.000944j
[2025-09-19 03:35:39] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -47.442371+0.002359j
[2025-09-19 03:35:51] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -47.450188+0.001028j
[2025-09-19 03:36:03] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -47.434270+0.001079j
[2025-09-19 03:36:16] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -47.443778-0.000967j
[2025-09-19 03:36:28] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -47.443132+0.003038j
[2025-09-19 03:36:41] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -47.445440+0.001355j
[2025-09-19 03:36:53] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -47.441958-0.001675j
[2025-09-19 03:37:06] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -47.445999+0.003904j
[2025-09-19 03:37:18] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -47.448506+0.001571j
[2025-09-19 03:37:30] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -47.447985-0.001862j
[2025-09-19 03:37:43] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -47.454675+0.000678j
[2025-09-19 03:37:55] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -47.440388+0.000828j
[2025-09-19 03:38:08] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -47.444644-0.003015j
[2025-09-19 03:38:20] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -47.459426-0.000509j
[2025-09-19 03:38:32] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -47.440930-0.002030j
[2025-09-19 03:38:45] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -47.456773+0.004920j
[2025-09-19 03:38:57] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -47.448184-0.000493j
[2025-09-19 03:39:09] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -47.440558-0.000684j
[2025-09-19 03:39:22] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -47.445484-0.001703j
[2025-09-19 03:39:34] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -47.443347-0.001374j
[2025-09-19 03:39:47] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -47.463788-0.000545j
[2025-09-19 03:39:47] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-19 03:39:59] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -47.447689+0.001997j
[2025-09-19 03:40:12] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -47.445204+0.003255j
[2025-09-19 03:40:24] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -47.451827+0.000720j
[2025-09-19 03:40:37] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -47.455326-0.003378j
[2025-09-19 03:40:49] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -47.432306-0.000762j
[2025-09-19 03:41:01] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -47.447569-0.002015j
[2025-09-19 03:41:14] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -47.451079+0.000975j
[2025-09-19 03:41:26] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -47.439259-0.001553j
[2025-09-19 03:41:39] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -47.440908-0.001368j
[2025-09-19 03:41:51] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -47.431654+0.000204j
[2025-09-19 03:42:04] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -47.439234+0.000524j
[2025-09-19 03:42:16] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -47.445530+0.002409j
[2025-09-19 03:42:28] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -47.448334-0.000039j
[2025-09-19 03:42:41] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -47.441601+0.002603j
[2025-09-19 03:42:53] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -47.439505+0.002310j
[2025-09-19 03:43:06] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -47.439333+0.001890j
[2025-09-19 03:43:18] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -47.435673-0.001297j
[2025-09-19 03:43:31] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -47.446356-0.003507j
[2025-09-19 03:43:43] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -47.456777-0.003044j
[2025-09-19 03:43:55] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -47.441404-0.001908j
[2025-09-19 03:44:08] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -47.435471+0.005808j
[2025-09-19 03:44:20] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -47.449185-0.001271j
[2025-09-19 03:44:33] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -47.453269+0.000431j
[2025-09-19 03:44:45] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -47.440186+0.000102j
[2025-09-19 03:44:58] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -47.455791+0.001049j
[2025-09-19 03:45:10] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -47.460148+0.004715j
[2025-09-19 03:45:23] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -47.463402-0.000412j
[2025-09-19 03:45:35] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -47.454087-0.000724j
[2025-09-19 03:45:47] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -47.443417+0.005512j
[2025-09-19 03:46:00] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -47.450776-0.001773j
[2025-09-19 03:46:12] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -47.448515-0.002328j
[2025-09-19 03:46:25] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -47.448610+0.003874j
[2025-09-19 03:46:37] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -47.442827-0.000230j
[2025-09-19 03:46:49] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -47.471782-0.000900j
[2025-09-19 03:47:02] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -47.453747+0.001616j
[2025-09-19 03:47:14] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -47.443446-0.001199j
[2025-09-19 03:47:27] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -47.445610-0.000627j
[2025-09-19 03:47:39] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -47.448931+0.002933j
[2025-09-19 03:47:52] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -47.446681+0.002384j
[2025-09-19 03:48:04] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -47.454054+0.002291j
[2025-09-19 03:48:17] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -47.454476+0.000007j
[2025-09-19 03:48:29] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -47.444789-0.001802j
[2025-09-19 03:48:41] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -47.446185+0.000359j
[2025-09-19 03:48:54] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -47.444906-0.002403j
[2025-09-19 03:49:06] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -47.456792+0.001664j
[2025-09-19 03:49:19] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -47.446192+0.000321j
[2025-09-19 03:49:31] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -47.438944-0.003847j
[2025-09-19 03:49:44] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -47.444125+0.002890j
[2025-09-19 03:49:56] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -47.441443-0.000863j
[2025-09-19 03:50:08] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -47.445965+0.002545j
[2025-09-19 03:50:21] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -47.449864-0.000951j
[2025-09-19 03:50:33] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -47.454754+0.000753j
[2025-09-19 03:50:46] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -47.441045-0.004299j
[2025-09-19 03:50:58] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -47.446840+0.001954j
[2025-09-19 03:51:10] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -47.456408-0.002197j
[2025-09-19 03:51:23] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -47.448237+0.000024j
[2025-09-19 03:51:35] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -47.445078-0.001081j
[2025-09-19 03:51:48] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -47.431042-0.001622j
[2025-09-19 03:52:00] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -47.455254+0.003429j
[2025-09-19 03:52:13] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -47.445690-0.001745j
[2025-09-19 03:52:25] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -47.440885+0.002477j
[2025-09-19 03:52:37] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -47.445968-0.001201j
[2025-09-19 03:52:50] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -47.450232+0.001526j
[2025-09-19 03:53:02] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -47.459014-0.000810j
[2025-09-19 03:53:15] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -47.439862+0.000765j
[2025-09-19 03:53:27] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -47.439879+0.001146j
[2025-09-19 03:53:40] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -47.443024-0.003836j
[2025-09-19 03:53:52] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -47.452707+0.005272j
[2025-09-19 03:54:04] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -47.451448-0.000859j
[2025-09-19 03:54:17] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -47.447958-0.000712j
[2025-09-19 03:54:29] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -47.445469-0.001380j
[2025-09-19 03:54:42] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -47.442793-0.001068j
[2025-09-19 03:54:54] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -47.453257+0.004952j
[2025-09-19 03:55:07] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -47.450255-0.000783j
[2025-09-19 03:55:19] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -47.446920+0.000799j
[2025-09-19 03:55:31] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -47.441112+0.001609j
[2025-09-19 03:55:44] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -47.450526+0.002993j
[2025-09-19 03:55:56] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -47.453216-0.003135j
[2025-09-19 03:56:09] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -47.454551+0.002597j
[2025-09-19 03:56:21] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -47.454796+0.000466j
[2025-09-19 03:56:34] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -47.454640+0.002806j
[2025-09-19 03:56:46] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -47.460032-0.002361j
[2025-09-19 03:56:58] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -47.459933-0.001057j
[2025-09-19 03:57:11] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -47.442973+0.003430j
[2025-09-19 03:57:23] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -47.461174-0.000093j
[2025-09-19 03:57:36] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -47.447293+0.003967j
[2025-09-19 03:57:48] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -47.443894+0.002439j
[2025-09-19 03:58:00] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -47.450658+0.006139j
[2025-09-19 03:58:13] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -47.455033+0.002133j
[2025-09-19 03:58:25] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -47.445487+0.004206j
[2025-09-19 03:58:38] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -47.449895-0.000956j
[2025-09-19 03:58:50] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -47.449403+0.000829j
[2025-09-19 03:59:03] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -47.460308+0.000843j
[2025-09-19 03:59:15] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -47.458705-0.000100j
[2025-09-19 03:59:27] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -47.440328-0.000558j
[2025-09-19 03:59:40] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -47.460125-0.002877j
[2025-09-19 03:59:52] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -47.445357+0.000010j
[2025-09-19 04:00:05] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -47.445633-0.001873j
[2025-09-19 04:00:17] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -47.455969-0.003455j
[2025-09-19 04:00:30] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -47.435833-0.000212j
[2025-09-19 04:00:42] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -47.453589+0.001984j
[2025-09-19 04:00:54] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -47.458249-0.001352j
[2025-09-19 04:01:07] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -47.437202+0.000578j
[2025-09-19 04:01:19] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -47.446408-0.000644j
[2025-09-19 04:01:32] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -47.439116+0.001242j
[2025-09-19 04:01:32] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-19 04:01:44] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -47.436131+0.001617j
[2025-09-19 04:01:57] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -47.446055-0.002134j
[2025-09-19 04:02:09] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -47.427581+0.000211j
[2025-09-19 04:02:21] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -47.440583-0.000679j
[2025-09-19 04:02:34] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -47.428019-0.001449j
[2025-09-19 04:02:46] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -47.444473-0.001716j
[2025-09-19 04:02:59] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -47.444709+0.002351j
[2025-09-19 04:03:11] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -47.448428+0.001332j
[2025-09-19 04:03:24] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -47.448358+0.002724j
[2025-09-19 04:03:36] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -47.446676-0.000716j
[2025-09-19 04:03:48] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -47.446686-0.001129j
[2025-09-19 04:04:01] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -47.442057+0.001707j
[2025-09-19 04:04:13] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -47.454787-0.001364j
[2025-09-19 04:04:26] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -47.459268-0.004219j
[2025-09-19 04:04:38] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -47.439447-0.001336j
[2025-09-19 04:04:50] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -47.459462+0.001940j
[2025-09-19 04:05:03] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -47.457749-0.002770j
[2025-09-19 04:05:15] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -47.454434+0.000102j
[2025-09-19 04:05:28] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -47.440821+0.004275j
[2025-09-19 04:05:40] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -47.441774-0.000983j
[2025-09-19 04:05:53] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -47.443839-0.001174j
[2025-09-19 04:06:05] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -47.455912+0.000925j
[2025-09-19 04:06:17] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -47.451433+0.000059j
[2025-09-19 04:06:30] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -47.449959-0.003653j
[2025-09-19 04:06:42] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -47.446686-0.001829j
[2025-09-19 04:06:55] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -47.450840+0.001121j
[2025-09-19 04:07:07] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -47.439695+0.000955j
[2025-09-19 04:07:20] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -47.448786-0.001697j
[2025-09-19 04:07:32] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -47.444076-0.002312j
[2025-09-19 04:07:44] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -47.452692+0.002649j
[2025-09-19 04:07:57] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -47.439028+0.001764j
[2025-09-19 04:08:09] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -47.450984+0.001083j
[2025-09-19 04:08:22] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -47.450169+0.001834j
[2025-09-19 04:08:34] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -47.450495+0.002689j
[2025-09-19 04:08:47] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -47.452431-0.000494j
[2025-09-19 04:08:59] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -47.455858-0.002270j
[2025-09-19 04:09:11] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -47.454203+0.000770j
[2025-09-19 04:09:24] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -47.441234+0.004173j
[2025-09-19 04:09:36] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -47.434720+0.002020j
[2025-09-19 04:09:49] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -47.444534-0.001860j
[2025-09-19 04:10:01] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -47.443083-0.000162j
[2025-09-19 04:10:14] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -47.439779-0.002034j
[2025-09-19 04:10:26] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -47.446982-0.003022j
[2025-09-19 04:10:38] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -47.444260+0.003449j
[2025-09-19 04:10:51] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -47.451970+0.001645j
[2025-09-19 04:11:03] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -47.445879+0.003112j
[2025-09-19 04:11:16] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -47.445487+0.000465j
[2025-09-19 04:11:28] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -47.446228-0.003139j
[2025-09-19 04:11:41] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -47.445474+0.000515j
[2025-09-19 04:11:53] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -47.435535-0.001823j
[2025-09-19 04:12:05] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -47.448631-0.002189j
[2025-09-19 04:12:18] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -47.448508-0.000796j
[2025-09-19 04:12:30] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -47.446806+0.000821j
[2025-09-19 04:12:43] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -47.443910+0.001368j
[2025-09-19 04:12:55] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -47.445439-0.002410j
[2025-09-19 04:13:08] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -47.443476+0.001917j
[2025-09-19 04:13:20] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -47.444528+0.001656j
[2025-09-19 04:13:33] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -47.438886+0.000174j
[2025-09-19 04:13:45] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -47.461877-0.000083j
[2025-09-19 04:13:57] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -47.439128-0.003219j
[2025-09-19 04:14:10] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -47.460574-0.008140j
[2025-09-19 04:14:22] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -47.433175+0.000406j
[2025-09-19 04:14:35] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -47.451387-0.001412j
[2025-09-19 04:14:47] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -47.450454+0.001511j
[2025-09-19 04:14:59] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -47.460304-0.001568j
[2025-09-19 04:15:12] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -47.440343-0.004333j
[2025-09-19 04:15:24] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -47.449022-0.000700j
[2025-09-19 04:15:37] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -47.440259-0.002076j
[2025-09-19 04:15:49] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -47.451915-0.003327j
[2025-09-19 04:16:01] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -47.461848-0.001194j
[2025-09-19 04:16:14] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -47.455748+0.001341j
[2025-09-19 04:16:26] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -47.444096-0.001041j
[2025-09-19 04:16:39] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -47.449979+0.005016j
[2025-09-19 04:16:51] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -47.452117+0.001381j
[2025-09-19 04:17:04] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -47.446529-0.000185j
[2025-09-19 04:17:16] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -47.459692-0.001139j
[2025-09-19 04:17:29] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -47.449660-0.000782j
[2025-09-19 04:17:41] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -47.454211-0.000259j
[2025-09-19 04:17:53] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -47.444517-0.000873j
[2025-09-19 04:18:06] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -47.436775-0.000832j
[2025-09-19 04:18:18] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -47.435846+0.001381j
[2025-09-19 04:18:30] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -47.450673-0.000093j
[2025-09-19 04:18:43] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -47.450785-0.001132j
[2025-09-19 04:18:55] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -47.444937+0.001599j
[2025-09-19 04:19:08] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -47.454591+0.001883j
[2025-09-19 04:19:20] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -47.451327-0.000991j
[2025-09-19 04:19:33] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -47.444707-0.001281j
[2025-09-19 04:19:45] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -47.449141-0.000025j
[2025-09-19 04:19:57] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -47.453839+0.000303j
[2025-09-19 04:20:10] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -47.450269-0.001762j
[2025-09-19 04:20:22] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -47.444858+0.000782j
[2025-09-19 04:20:35] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -47.453930+0.002744j
[2025-09-19 04:20:47] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -47.444014-0.000956j
[2025-09-19 04:21:00] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -47.443456-0.001374j
[2025-09-19 04:21:12] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -47.442495-0.001527j
[2025-09-19 04:21:24] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -47.457738+0.002377j
[2025-09-19 04:21:37] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -47.429500-0.000946j
[2025-09-19 04:21:49] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -47.439171-0.000676j
[2025-09-19 04:22:02] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -47.437531+0.000958j
[2025-09-19 04:22:14] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -47.453995+0.001804j
[2025-09-19 04:22:27] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -47.461133+0.002703j
[2025-09-19 04:22:39] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -47.443049-0.000117j
[2025-09-19 04:22:51] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -47.452953-0.001444j
[2025-09-19 04:23:04] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -47.442749-0.003434j
[2025-09-19 04:23:16] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -47.458523+0.003148j
[2025-09-19 04:23:16] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-19 04:23:29] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -47.451447+0.000366j
[2025-09-19 04:23:41] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -47.444004+0.000007j
[2025-09-19 04:23:54] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -47.444990-0.000454j
[2025-09-19 04:24:06] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -47.455353+0.000418j
[2025-09-19 04:24:18] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -47.441350-0.001028j
[2025-09-19 04:24:31] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -47.452844+0.001571j
[2025-09-19 04:24:43] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -47.447775-0.001196j
[2025-09-19 04:24:56] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -47.446746+0.000762j
[2025-09-19 04:25:08] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -47.453681-0.002343j
[2025-09-19 04:25:20] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -47.451697-0.000804j
[2025-09-19 04:25:33] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -47.442506+0.002891j
[2025-09-19 04:25:45] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -47.459612+0.000793j
[2025-09-19 04:25:58] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -47.444887+0.000940j
[2025-09-19 04:26:10] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -47.440725-0.001924j
[2025-09-19 04:26:23] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -47.444141+0.000516j
[2025-09-19 04:26:35] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -47.448604-0.002222j
[2025-09-19 04:26:47] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -47.433953-0.002974j
[2025-09-19 04:27:00] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -47.454915+0.001174j
[2025-09-19 04:27:12] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -47.442809+0.000330j
[2025-09-19 04:27:25] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -47.446471+0.000600j
[2025-09-19 04:27:37] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -47.449548-0.000208j
[2025-09-19 04:27:50] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -47.460339+0.000756j
[2025-09-19 04:28:02] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -47.453466-0.000205j
[2025-09-19 04:28:14] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -47.449677-0.000502j
[2025-09-19 04:28:27] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -47.448493+0.002310j
[2025-09-19 04:28:39] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -47.439648-0.001762j
[2025-09-19 04:28:52] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -47.443531+0.000700j
[2025-09-19 04:29:04] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -47.444173-0.001227j
[2025-09-19 04:29:17] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -47.447741+0.000053j
[2025-09-19 04:29:29] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -47.442116+0.005099j
[2025-09-19 04:29:42] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -47.458614+0.000940j
[2025-09-19 04:29:54] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -47.455275+0.000853j
[2025-09-19 04:30:07] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -47.446160-0.004835j
[2025-09-19 04:30:19] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -47.459282-0.000676j
[2025-09-19 04:30:31] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -47.458292-0.003456j
[2025-09-19 04:30:44] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -47.449875+0.001396j
[2025-09-19 04:30:56] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -47.455715+0.000521j
[2025-09-19 04:31:09] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -47.445377+0.000887j
[2025-09-19 04:31:21] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -47.443741+0.001529j
[2025-09-19 04:31:34] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -47.427398+0.000768j
[2025-09-19 04:31:46] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -47.466496+0.000162j
[2025-09-19 04:31:58] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -47.447155-0.001575j
[2025-09-19 04:32:11] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -47.445168-0.001738j
[2025-09-19 04:32:23] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -47.453808+0.000030j
[2025-09-19 04:32:36] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -47.445505-0.000354j
[2025-09-19 04:32:48] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -47.439317+0.000665j
[2025-09-19 04:33:01] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -47.464751+0.001574j
[2025-09-19 04:33:13] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -47.442965+0.001535j
[2025-09-19 04:33:25] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -47.437876-0.002335j
[2025-09-19 04:33:38] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -47.440196-0.000852j
[2025-09-19 04:33:50] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -47.450890+0.004225j
[2025-09-19 04:34:03] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -47.442482+0.000939j
[2025-09-19 04:34:15] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -47.447267+0.001334j
[2025-09-19 04:34:27] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -47.452525-0.002997j
[2025-09-19 04:34:40] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -47.454886+0.002101j
[2025-09-19 04:34:52] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -47.441986-0.002605j
[2025-09-19 04:35:05] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -47.439939+0.000478j
[2025-09-19 04:35:17] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -47.435744+0.002838j
[2025-09-19 04:35:30] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -47.468747+0.002081j
[2025-09-19 04:35:42] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -47.444535-0.001653j
[2025-09-19 04:35:54] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -47.452072+0.001785j
[2025-09-19 04:36:07] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -47.439894-0.000245j
[2025-09-19 04:36:19] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -47.440588-0.001132j
[2025-09-19 04:36:32] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -47.445581-0.000442j
[2025-09-19 04:36:44] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -47.459768-0.000113j
[2025-09-19 04:36:57] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -47.443608-0.000973j
[2025-09-19 04:37:09] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -47.430849-0.002856j
[2025-09-19 04:37:21] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -47.461529+0.000587j
[2025-09-19 04:37:34] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -47.433980-0.000501j
[2025-09-19 04:37:46] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -47.443379-0.002187j
[2025-09-19 04:37:59] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -47.439862+0.000071j
[2025-09-19 04:38:11] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -47.446703-0.002245j
[2025-09-19 04:38:24] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -47.446099+0.002769j
[2025-09-19 04:38:36] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -47.447862-0.000638j
[2025-09-19 04:38:49] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -47.442445+0.004148j
[2025-09-19 04:39:01] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -47.445148+0.001408j
[2025-09-19 04:39:14] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -47.443047+0.003678j
[2025-09-19 04:39:26] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -47.441588+0.000874j
[2025-09-19 04:39:39] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -47.447479+0.000419j
[2025-09-19 04:39:51] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -47.450999+0.000414j
[2025-09-19 04:40:03] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -47.454869+0.000689j
[2025-09-19 04:40:16] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -47.452255+0.002589j
[2025-09-19 04:40:28] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -47.446817+0.000089j
[2025-09-19 04:40:41] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -47.443440+0.003819j
[2025-09-19 04:40:53] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -47.445115-0.001444j
[2025-09-19 04:41:05] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -47.435421-0.003095j
[2025-09-19 04:41:18] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -47.453389+0.000365j
[2025-09-19 04:41:30] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -47.451624+0.001867j
[2025-09-19 04:41:43] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -47.451047+0.000794j
[2025-09-19 04:41:55] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -47.438570-0.001879j
[2025-09-19 04:42:08] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -47.448905+0.002664j
[2025-09-19 04:42:20] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -47.434310-0.001583j
[2025-09-19 04:42:33] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -47.443975-0.001264j
[2025-09-19 04:42:45] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -47.438274+0.002404j
[2025-09-19 04:42:57] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -47.431957-0.005414j
[2025-09-19 04:43:10] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -47.450899-0.001706j
[2025-09-19 04:43:22] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -47.458129+0.001254j
[2025-09-19 04:43:29] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -47.457006-0.000255j
[2025-09-19 04:43:35] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -47.443711-0.002295j
[2025-09-19 04:43:40] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -47.445941+0.002087j
[2025-09-19 04:43:46] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -47.457349+0.001175j
[2025-09-19 04:43:52] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -47.449783+0.001632j
[2025-09-19 04:43:57] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -47.446890+0.002296j
[2025-09-19 04:44:03] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -47.428736+0.000619j
[2025-09-19 04:44:08] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -47.451748-0.002455j
[2025-09-19 04:44:08] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-19 04:44:08] ✅ Training completed | Restarts: 2
[2025-09-19 04:44:08] ============================================================
[2025-09-19 04:44:08] Training completed | Runtime: 13064.0s
[2025-09-19 04:44:11] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-19 04:44:11] ============================================================
