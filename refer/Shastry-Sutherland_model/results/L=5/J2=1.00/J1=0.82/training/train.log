[2025-09-18 17:46:24] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.81/training/checkpoints/final_GCNN.pkl
[2025-09-18 17:46:24]   - 迭代次数: final
[2025-09-18 17:46:24]   - 能量: -45.513383-0.000927j ± 0.007816
[2025-09-18 17:46:24]   - 时间戳: 2025-09-18T17:46:07.132878+08:00
[2025-09-18 17:46:41] ✓ 变分状态参数已从checkpoint恢复
[2025-09-18 17:46:41] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-18 17:46:41] ==================================================
[2025-09-18 17:46:41] GCNN for Shastry-Sutherland Model
[2025-09-18 17:46:41] ==================================================
[2025-09-18 17:46:41] System parameters:
[2025-09-18 17:46:41]   - System size: L=5, N=100
[2025-09-18 17:46:41]   - System parameters: J1=0.82, J2=1.0, Q=0.0
[2025-09-18 17:46:41] --------------------------------------------------
[2025-09-18 17:46:42] Model parameters:
[2025-09-18 17:46:42]   - Number of layers = 4
[2025-09-18 17:46:42]   - Number of features = 4
[2025-09-18 17:46:42]   - Total parameters = 19628
[2025-09-18 17:46:42] --------------------------------------------------
[2025-09-18 17:46:42] Training parameters:
[2025-09-18 17:46:42]   - Learning rate: 0.015
[2025-09-18 17:46:42]   - Total iterations: 1050
[2025-09-18 17:46:42]   - Annealing cycles: 3
[2025-09-18 17:46:42]   - Initial period: 150
[2025-09-18 17:46:42]   - Period multiplier: 2.0
[2025-09-18 17:46:42]   - Temperature range: 0.0-1.0
[2025-09-18 17:46:42]   - Samples: 4096
[2025-09-18 17:46:42]   - Discarded samples: 0
[2025-09-18 17:46:42]   - Chunk size: 2048
[2025-09-18 17:46:42]   - Diagonal shift: 0.2
[2025-09-18 17:46:42]   - Gradient clipping: 1.0
[2025-09-18 17:46:42]   - Checkpoint enabled: interval=105
[2025-09-18 17:46:42]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.82/training/checkpoints
[2025-09-18 17:46:42] --------------------------------------------------
[2025-09-18 17:46:42] Device status:
[2025-09-18 17:46:42]   - Devices model: NVIDIA H200 NVL
[2025-09-18 17:46:42]   - Number of devices: 1
[2025-09-18 17:46:42]   - Sharding: True
[2025-09-18 17:46:42] ============================================================
[2025-09-18 17:47:42] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -46.137086+0.003428j
[2025-09-18 17:48:19] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -46.140270-0.001423j
[2025-09-18 17:48:31] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -46.145948+0.000647j
[2025-09-18 17:48:43] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -46.135745-0.002267j
[2025-09-18 17:48:56] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -46.135891-0.003336j
[2025-09-18 17:49:08] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -46.147563-0.003157j
[2025-09-18 17:49:20] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -46.131569-0.000999j
[2025-09-18 17:49:33] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -46.140569+0.000570j
[2025-09-18 17:49:45] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -46.137878-0.000116j
[2025-09-18 17:49:57] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -46.132602+0.000028j
[2025-09-18 17:50:10] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -46.138999+0.000809j
[2025-09-18 17:50:22] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -46.154538+0.000574j
[2025-09-18 17:50:34] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -46.157075+0.000738j
[2025-09-18 17:50:47] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -46.142939-0.001780j
[2025-09-18 17:50:59] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -46.151182+0.001226j
[2025-09-18 17:51:11] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -46.146527-0.004249j
[2025-09-18 17:51:24] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -46.149652-0.002238j
[2025-09-18 17:51:36] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -46.124554+0.002525j
[2025-09-18 17:51:48] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -46.158437-0.000208j
[2025-09-18 17:52:01] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -46.137628-0.000727j
[2025-09-18 17:52:13] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -46.153628-0.003665j
[2025-09-18 17:52:26] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -46.137779+0.001528j
[2025-09-18 17:52:38] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -46.149924-0.000826j
[2025-09-18 17:52:50] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -46.138054+0.001189j
[2025-09-18 17:53:03] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -46.144791+0.002918j
[2025-09-18 17:53:15] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -46.152454-0.002280j
[2025-09-18 17:53:27] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -46.143525-0.000881j
[2025-09-18 17:53:40] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -46.138342-0.001792j
[2025-09-18 17:53:52] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -46.150784-0.001016j
[2025-09-18 17:54:04] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -46.147975+0.005715j
[2025-09-18 17:54:17] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -46.140965-0.000326j
[2025-09-18 17:54:29] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -46.146374+0.000521j
[2025-09-18 17:54:41] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -46.144118+0.001654j
[2025-09-18 17:54:54] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -46.171146-0.001411j
[2025-09-18 17:55:06] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -46.139525+0.000296j
[2025-09-18 17:55:19] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -46.150698-0.003880j
[2025-09-18 17:55:31] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -46.153340+0.001408j
[2025-09-18 17:55:43] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -46.139830+0.001191j
[2025-09-18 17:55:56] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -46.152798+0.000545j
[2025-09-18 17:56:08] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -46.170087+0.002123j
[2025-09-18 17:56:20] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -46.153582-0.000697j
[2025-09-18 17:56:33] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -46.149180-0.002884j
[2025-09-18 17:56:45] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -46.144471-0.001326j
[2025-09-18 17:56:57] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -46.150171-0.000383j
[2025-09-18 17:57:10] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -46.132648-0.001958j
[2025-09-18 17:57:22] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -46.152749+0.002555j
[2025-09-18 17:57:35] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -46.125936-0.001543j
[2025-09-18 17:57:47] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -46.149354+0.001954j
[2025-09-18 17:57:59] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -46.155761-0.001877j
[2025-09-18 17:58:12] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -46.134607-0.001507j
[2025-09-18 17:58:24] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -46.148451+0.000454j
[2025-09-18 17:58:36] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -46.158449+0.003154j
[2025-09-18 17:58:49] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -46.143816-0.002676j
[2025-09-18 17:59:01] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -46.153550+0.001320j
[2025-09-18 17:59:13] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -46.151452-0.000003j
[2025-09-18 17:59:26] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -46.154682+0.000734j
[2025-09-18 17:59:38] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -46.155022-0.002155j
[2025-09-18 17:59:50] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -46.137725-0.003026j
[2025-09-18 18:00:03] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -46.140541-0.001039j
[2025-09-18 18:00:15] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -46.147495+0.001900j
[2025-09-18 18:00:27] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -46.147925-0.000306j
[2025-09-18 18:00:40] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -46.158158+0.005632j
[2025-09-18 18:00:52] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -46.147094+0.002584j
[2025-09-18 18:01:05] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -46.138383+0.001944j
[2025-09-18 18:01:17] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -46.152877-0.000618j
[2025-09-18 18:01:30] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -46.134718-0.009571j
[2025-09-18 18:01:42] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -46.160125-0.001984j
[2025-09-18 18:01:55] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -46.141134+0.004063j
[2025-09-18 18:02:07] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -46.138210-0.001403j
[2025-09-18 18:02:20] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -46.153953+0.000904j
[2025-09-18 18:02:32] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -46.151148+0.000121j
[2025-09-18 18:02:44] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -46.144830+0.001025j
[2025-09-18 18:02:57] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -46.138830+0.000463j
[2025-09-18 18:03:09] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -46.154502+0.000198j
[2025-09-18 18:03:22] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -46.140395+0.001773j
[2025-09-18 18:03:34] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -46.146045-0.000521j
[2025-09-18 18:03:46] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -46.144335+0.002943j
[2025-09-18 18:03:59] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -46.145710-0.002905j
[2025-09-18 18:04:11] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -46.148092-0.001426j
[2025-09-18 18:04:23] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -46.145850-0.003678j
[2025-09-18 18:04:36] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -46.146680-0.001488j
[2025-09-18 18:04:48] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -46.149373+0.000342j
[2025-09-18 18:05:00] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -46.139876+0.001279j
[2025-09-18 18:05:13] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -46.143968-0.001612j
[2025-09-18 18:05:25] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -46.148863-0.002801j
[2025-09-18 18:05:37] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -46.147493-0.001463j
[2025-09-18 18:05:50] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -46.138389+0.001575j
[2025-09-18 18:06:02] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -46.150121-0.004085j
[2025-09-18 18:06:14] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -46.143375-0.001998j
[2025-09-18 18:06:27] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -46.150532+0.004179j
[2025-09-18 18:06:39] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -46.144114-0.001607j
[2025-09-18 18:06:51] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -46.173282-0.001662j
[2025-09-18 18:07:04] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -46.144697-0.000719j
[2025-09-18 18:07:16] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -46.152762+0.003564j
[2025-09-18 18:07:29] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -46.139210+0.002986j
[2025-09-18 18:07:41] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -46.144060+0.001146j
[2025-09-18 18:07:53] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -46.145773+0.003287j
[2025-09-18 18:08:06] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -46.149758-0.002314j
[2025-09-18 18:08:18] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -46.132655+0.000421j
[2025-09-18 18:08:30] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -46.152849-0.000216j
[2025-09-18 18:08:43] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -46.145804+0.003177j
[2025-09-18 18:08:55] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -46.148123+0.007397j
[2025-09-18 18:09:07] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -46.150619+0.000826j
[2025-09-18 18:09:20] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -46.147756-0.001095j
[2025-09-18 18:09:32] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -46.139167-0.003267j
[2025-09-18 18:09:32] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-18 18:09:45] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -46.156480-0.001986j
[2025-09-18 18:09:57] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -46.138975-0.002044j
[2025-09-18 18:10:09] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -46.152695-0.002836j
[2025-09-18 18:10:22] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -46.149812-0.003113j
[2025-09-18 18:10:34] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -46.148060+0.001265j
[2025-09-18 18:10:46] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -46.150011+0.001576j
[2025-09-18 18:10:59] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -46.155586-0.000073j
[2025-09-18 18:11:11] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -46.139052-0.000256j
[2025-09-18 18:11:23] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -46.137440+0.001586j
[2025-09-18 18:11:36] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -46.136956-0.000855j
[2025-09-18 18:11:48] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -46.139366-0.002120j
[2025-09-18 18:12:00] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -46.151982+0.003872j
[2025-09-18 18:12:13] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -46.148137-0.001102j
[2025-09-18 18:12:25] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -46.139500-0.004023j
[2025-09-18 18:12:37] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -46.140338+0.000034j
[2025-09-18 18:12:50] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -46.147112+0.000851j
[2025-09-18 18:13:02] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -46.154230+0.002203j
[2025-09-18 18:13:15] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -46.145305+0.001036j
[2025-09-18 18:13:27] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -46.145030-0.001609j
[2025-09-18 18:13:39] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -46.152244-0.002160j
[2025-09-18 18:13:52] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -46.154805-0.000489j
[2025-09-18 18:14:04] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -46.159109+0.000431j
[2025-09-18 18:14:16] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -46.161686-0.002784j
[2025-09-18 18:14:29] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -46.144286+0.003681j
[2025-09-18 18:14:41] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -46.145849+0.001759j
[2025-09-18 18:14:53] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -46.139633+0.001818j
[2025-09-18 18:15:06] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -46.150384-0.001867j
[2025-09-18 18:15:18] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -46.143239+0.002241j
[2025-09-18 18:15:30] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -46.133239+0.000106j
[2025-09-18 18:15:43] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -46.158204+0.000537j
[2025-09-18 18:15:55] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -46.142487+0.003091j
[2025-09-18 18:16:07] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -46.142962+0.001241j
[2025-09-18 18:16:20] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -46.138209-0.000338j
[2025-09-18 18:16:32] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -46.147556-0.005756j
[2025-09-18 18:16:45] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -46.142002-0.002392j
[2025-09-18 18:16:57] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -46.160944+0.001608j
[2025-09-18 18:17:09] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -46.154534+0.001511j
[2025-09-18 18:17:22] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -46.154021-0.004920j
[2025-09-18 18:17:34] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -46.146322-0.000030j
[2025-09-18 18:17:46] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -46.142922-0.002319j
[2025-09-18 18:17:59] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -46.157348-0.001852j
[2025-09-18 18:18:11] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -46.142626+0.002290j
[2025-09-18 18:18:23] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -46.146466+0.002747j
[2025-09-18 18:18:36] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -46.138496-0.001035j
[2025-09-18 18:18:48] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -46.128771+0.000053j
[2025-09-18 18:18:48] RESTART #1 | Period: 300
[2025-09-18 18:19:01] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -46.159581+0.003654j
[2025-09-18 18:19:13] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -46.138979-0.000371j
[2025-09-18 18:19:25] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -46.138467-0.000334j
[2025-09-18 18:19:38] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -46.155118-0.001234j
[2025-09-18 18:19:50] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -46.140901-0.000561j
[2025-09-18 18:20:03] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -46.149178-0.000623j
[2025-09-18 18:20:15] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -46.143366+0.000640j
[2025-09-18 18:20:27] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -46.151411+0.003339j
[2025-09-18 18:20:40] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -46.143403-0.000475j
[2025-09-18 18:20:52] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -46.138509+0.001429j
[2025-09-18 18:21:04] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -46.143998-0.000927j
[2025-09-18 18:21:17] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -46.148363+0.002886j
[2025-09-18 18:21:29] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -46.141735-0.000522j
[2025-09-18 18:21:41] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -46.132926-0.000472j
[2025-09-18 18:21:54] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -46.159914-0.004101j
[2025-09-18 18:22:06] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -46.143819-0.000905j
[2025-09-18 18:22:19] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -46.152944-0.002160j
[2025-09-18 18:22:31] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -46.140133-0.001481j
[2025-09-18 18:22:43] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -46.134909+0.001520j
[2025-09-18 18:22:56] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -46.131282+0.001589j
[2025-09-18 18:23:08] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -46.153693-0.000264j
[2025-09-18 18:23:20] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -46.147724-0.000041j
[2025-09-18 18:23:33] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -46.141145-0.002291j
[2025-09-18 18:23:45] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -46.143581+0.000119j
[2025-09-18 18:23:57] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -46.146050+0.000881j
[2025-09-18 18:24:10] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -46.153241-0.001209j
[2025-09-18 18:24:22] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -46.132605+0.002657j
[2025-09-18 18:24:34] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -46.148243-0.000744j
[2025-09-18 18:24:47] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -46.142523+0.000857j
[2025-09-18 18:24:59] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -46.140149-0.001405j
[2025-09-18 18:25:12] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -46.143511-0.000594j
[2025-09-18 18:25:24] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -46.155145+0.000469j
[2025-09-18 18:25:36] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -46.144662+0.001926j
[2025-09-18 18:25:49] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -46.142593-0.000505j
[2025-09-18 18:26:01] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -46.156632+0.001164j
[2025-09-18 18:26:13] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -46.156818-0.002625j
[2025-09-18 18:26:26] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -46.147880-0.000718j
[2025-09-18 18:26:38] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -46.149953-0.002696j
[2025-09-18 18:26:50] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -46.153733+0.000235j
[2025-09-18 18:27:03] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -46.152969-0.000693j
[2025-09-18 18:27:15] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -46.150790+0.001305j
[2025-09-18 18:27:27] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -46.159584+0.002256j
[2025-09-18 18:27:40] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -46.153616-0.001330j
[2025-09-18 18:27:52] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -46.152417-0.001358j
[2025-09-18 18:28:05] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -46.138274-0.000267j
[2025-09-18 18:28:17] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -46.137174-0.000567j
[2025-09-18 18:28:29] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -46.146510-0.000513j
[2025-09-18 18:28:42] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -46.137660-0.002112j
[2025-09-18 18:28:54] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -46.152266-0.000522j
[2025-09-18 18:29:06] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -46.142754+0.000034j
[2025-09-18 18:29:19] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -46.151801+0.001319j
[2025-09-18 18:29:31] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -46.141749-0.002703j
[2025-09-18 18:29:43] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -46.147122-0.001016j
[2025-09-18 18:29:56] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -46.149284+0.001118j
[2025-09-18 18:30:08] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -46.153022-0.000537j
[2025-09-18 18:30:21] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -46.152670+0.000297j
[2025-09-18 18:30:33] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -46.149050-0.002142j
[2025-09-18 18:30:45] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -46.158303+0.003246j
[2025-09-18 18:30:58] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -46.156503-0.001827j
[2025-09-18 18:31:10] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -46.156143-0.002106j
[2025-09-18 18:31:10] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-18 18:31:22] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -46.145090-0.000614j
[2025-09-18 18:31:35] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -46.135244+0.002880j
[2025-09-18 18:31:47] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -46.157196-0.002531j
[2025-09-18 18:31:59] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -46.163757+0.000107j
[2025-09-18 18:32:12] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -46.152289+0.003875j
[2025-09-18 18:32:24] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -46.137946-0.000453j
[2025-09-18 18:32:36] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -46.151322-0.001930j
[2025-09-18 18:32:49] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -46.160208+0.005233j
[2025-09-18 18:33:01] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -46.155003+0.004736j
[2025-09-18 18:33:14] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -46.145729+0.004045j
[2025-09-18 18:33:26] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -46.148395-0.001849j
[2025-09-18 18:33:38] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -46.145196+0.000398j
[2025-09-18 18:33:51] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -46.135428-0.002201j
[2025-09-18 18:34:03] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -46.157193+0.001866j
[2025-09-18 18:34:16] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -46.153257+0.003985j
[2025-09-18 18:34:28] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -46.142680-0.003471j
[2025-09-18 18:34:40] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -46.148026+0.000811j
[2025-09-18 18:34:53] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -46.152582-0.002295j
[2025-09-18 18:35:05] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -46.158547-0.000850j
[2025-09-18 18:35:17] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -46.152361+0.001357j
[2025-09-18 18:35:30] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -46.159182+0.000233j
[2025-09-18 18:35:42] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -46.153206-0.005839j
[2025-09-18 18:35:54] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -46.146491-0.000512j
[2025-09-18 18:36:07] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -46.145602-0.000861j
[2025-09-18 18:36:19] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -46.150226-0.001106j
[2025-09-18 18:36:32] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -46.146249-0.001306j
[2025-09-18 18:36:44] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -46.142231+0.003865j
[2025-09-18 18:36:56] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -46.160218-0.004013j
[2025-09-18 18:37:09] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -46.150781-0.001183j
[2025-09-18 18:37:21] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -46.153856+0.002343j
[2025-09-18 18:37:33] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -46.141977-0.000161j
[2025-09-18 18:37:46] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -46.141133-0.000360j
[2025-09-18 18:37:58] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -46.144087-0.001344j
[2025-09-18 18:38:10] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -46.148890-0.002006j
[2025-09-18 18:38:23] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -46.153007+0.004125j
[2025-09-18 18:38:35] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -46.146226+0.006859j
[2025-09-18 18:38:48] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -46.163791-0.000494j
[2025-09-18 18:39:00] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -46.154453+0.002066j
[2025-09-18 18:39:12] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -46.145386+0.001437j
[2025-09-18 18:39:25] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -46.145252-0.000810j
[2025-09-18 18:39:37] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -46.150488+0.004039j
[2025-09-18 18:39:49] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -46.141520+0.004962j
[2025-09-18 18:40:02] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -46.153576+0.001179j
[2025-09-18 18:40:14] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -46.143707-0.001961j
[2025-09-18 18:40:26] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -46.157708-0.000744j
[2025-09-18 18:40:39] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -46.147818-0.000328j
[2025-09-18 18:40:51] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -46.132816+0.003694j
[2025-09-18 18:41:04] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -46.142114+0.001361j
[2025-09-18 18:41:16] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -46.135971-0.009588j
[2025-09-18 18:41:28] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -46.160893-0.007825j
[2025-09-18 18:41:41] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -46.145376+0.002222j
[2025-09-18 18:41:53] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -46.145780-0.000002j
[2025-09-18 18:42:05] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -46.143973+0.000365j
[2025-09-18 18:42:18] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -46.162970+0.000691j
[2025-09-18 18:42:30] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -46.140726-0.002200j
[2025-09-18 18:42:42] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -46.157650-0.000041j
[2025-09-18 18:42:55] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -46.142794+0.003115j
[2025-09-18 18:43:07] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -46.157890+0.000738j
[2025-09-18 18:43:20] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -46.133894-0.000387j
[2025-09-18 18:43:32] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -46.142244+0.002307j
[2025-09-18 18:43:44] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -46.149472-0.004641j
[2025-09-18 18:43:57] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -46.152363+0.000748j
[2025-09-18 18:44:09] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -46.155360-0.002087j
[2025-09-18 18:44:21] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -46.145308-0.003157j
[2025-09-18 18:44:34] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -46.140334+0.001524j
[2025-09-18 18:44:46] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -46.145501-0.000122j
[2025-09-18 18:44:58] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -46.149517-0.000148j
[2025-09-18 18:45:11] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -46.144734-0.002392j
[2025-09-18 18:45:23] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -46.144341+0.003314j
[2025-09-18 18:45:35] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -46.144112+0.000059j
[2025-09-18 18:45:48] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -46.142951+0.001487j
[2025-09-18 18:46:00] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -46.152007-0.001196j
[2025-09-18 18:46:13] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -46.156166+0.000082j
[2025-09-18 18:46:25] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -46.155400-0.002519j
[2025-09-18 18:46:37] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -46.132338-0.000186j
[2025-09-18 18:46:50] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -46.146784-0.000679j
[2025-09-18 18:47:02] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -46.141960+0.000624j
[2025-09-18 18:47:14] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -46.147200-0.000470j
[2025-09-18 18:47:27] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -46.169454-0.000462j
[2025-09-18 18:47:39] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -46.146185-0.002458j
[2025-09-18 18:47:51] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -46.149215+0.003390j
[2025-09-18 18:48:04] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -46.142122-0.001157j
[2025-09-18 18:48:16] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -46.158992+0.001357j
[2025-09-18 18:48:28] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -46.152617+0.002837j
[2025-09-18 18:48:41] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -46.149740-0.001919j
[2025-09-18 18:48:53] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -46.140328+0.000573j
[2025-09-18 18:49:05] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -46.153488+0.003046j
[2025-09-18 18:49:18] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -46.143035+0.002356j
[2025-09-18 18:49:30] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -46.146628-0.000517j
[2025-09-18 18:49:43] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -46.138489-0.002045j
[2025-09-18 18:49:55] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -46.150431-0.001121j
[2025-09-18 18:50:07] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -46.147933+0.000769j
[2025-09-18 18:50:20] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -46.152516+0.000935j
[2025-09-18 18:50:32] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -46.159200+0.000399j
[2025-09-18 18:50:44] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -46.160798-0.003356j
[2025-09-18 18:50:57] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -46.147033+0.000785j
[2025-09-18 18:51:09] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -46.145019-0.000186j
[2025-09-18 18:51:22] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -46.147374+0.001128j
[2025-09-18 18:51:34] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -46.163398+0.009114j
[2025-09-18 18:51:47] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -46.149378-0.002018j
[2025-09-18 18:51:59] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -46.141979+0.000808j
[2025-09-18 18:52:11] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -46.148039+0.000364j
[2025-09-18 18:52:24] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -46.163706+0.006662j
[2025-09-18 18:52:36] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -46.153544+0.002538j
[2025-09-18 18:52:48] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -46.156220-0.000172j
[2025-09-18 18:52:48] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-18 18:53:01] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -46.165239-0.004996j
[2025-09-18 18:53:13] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -46.141356+0.006233j
[2025-09-18 18:53:25] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -46.154152+0.003541j
[2025-09-18 18:53:38] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -46.139081-0.000480j
[2025-09-18 18:53:50] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -46.152076+0.003321j
[2025-09-18 18:54:02] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -46.152836-0.000348j
[2025-09-18 18:54:15] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -46.142799+0.003614j
[2025-09-18 18:54:27] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -46.145401-0.001235j
[2025-09-18 18:54:40] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -46.146089-0.000618j
[2025-09-18 18:54:52] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -46.144533+0.001227j
[2025-09-18 18:55:04] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -46.150681+0.002380j
[2025-09-18 18:55:17] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -46.159116-0.002131j
[2025-09-18 18:55:29] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -46.144900+0.001351j
[2025-09-18 18:55:41] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -46.150620-0.000875j
[2025-09-18 18:55:54] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -46.152732+0.001839j
[2025-09-18 18:56:06] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -46.155647-0.000412j
[2025-09-18 18:56:18] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -46.160534-0.000691j
[2025-09-18 18:56:31] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -46.159392-0.002873j
[2025-09-18 18:56:43] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -46.142544+0.000351j
[2025-09-18 18:56:56] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -46.154486+0.003728j
[2025-09-18 18:57:08] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -46.147513-0.000470j
[2025-09-18 18:57:20] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -46.128687+0.000713j
[2025-09-18 18:57:33] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -46.152159-0.001717j
[2025-09-18 18:57:45] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -46.166457-0.002595j
[2025-09-18 18:57:57] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -46.150270-0.001262j
[2025-09-18 18:58:10] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -46.136616-0.000729j
[2025-09-18 18:58:22] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -46.158784+0.001635j
[2025-09-18 18:58:34] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -46.151587+0.000792j
[2025-09-18 18:58:47] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -46.155939+0.001752j
[2025-09-18 18:58:59] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -46.161119+0.002074j
[2025-09-18 18:59:11] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -46.157382+0.001069j
[2025-09-18 18:59:24] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -46.143477-0.002198j
[2025-09-18 18:59:36] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -46.143463+0.001012j
[2025-09-18 18:59:49] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -46.147376-0.000017j
[2025-09-18 19:00:01] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -46.152368+0.002474j
[2025-09-18 19:00:13] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -46.154378-0.002179j
[2025-09-18 19:00:26] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -46.146652-0.000793j
[2025-09-18 19:00:38] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -46.134703-0.001560j
[2025-09-18 19:00:50] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -46.162895-0.001864j
[2025-09-18 19:01:03] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -46.145195+0.001973j
[2025-09-18 19:01:15] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -46.150337+0.002792j
[2025-09-18 19:01:27] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -46.140374-0.004166j
[2025-09-18 19:01:40] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -46.147125+0.005551j
[2025-09-18 19:01:52] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -46.155495-0.002379j
[2025-09-18 19:02:05] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -46.151467+0.000011j
[2025-09-18 19:02:17] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -46.161910+0.000193j
[2025-09-18 19:02:30] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -46.152203+0.002086j
[2025-09-18 19:02:42] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -46.159050-0.003017j
[2025-09-18 19:02:54] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -46.134987-0.001107j
[2025-09-18 19:03:07] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -46.139393+0.003146j
[2025-09-18 19:03:19] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -46.139026+0.001725j
[2025-09-18 19:03:32] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -46.160556+0.002230j
[2025-09-18 19:03:44] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -46.142931-0.001636j
[2025-09-18 19:03:56] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -46.148320-0.000346j
[2025-09-18 19:04:09] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -46.151611-0.000745j
[2025-09-18 19:04:21] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -46.152158-0.001530j
[2025-09-18 19:04:34] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -46.161535-0.005707j
[2025-09-18 19:04:46] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -46.157707-0.002120j
[2025-09-18 19:04:58] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -46.144548-0.000221j
[2025-09-18 19:05:11] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -46.136116+0.000344j
[2025-09-18 19:05:23] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -46.140038+0.001783j
[2025-09-18 19:05:35] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -46.145775-0.001728j
[2025-09-18 19:05:48] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -46.137105+0.001592j
[2025-09-18 19:06:00] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -46.143652-0.001443j
[2025-09-18 19:06:12] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -46.158256-0.002220j
[2025-09-18 19:06:25] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -46.153342-0.000581j
[2025-09-18 19:06:37] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -46.148432+0.002815j
[2025-09-18 19:06:50] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -46.138773-0.002134j
[2025-09-18 19:07:02] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -46.134551-0.002580j
[2025-09-18 19:07:14] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -46.148930+0.000490j
[2025-09-18 19:07:27] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -46.164456+0.001118j
[2025-09-18 19:07:39] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -46.139605-0.003298j
[2025-09-18 19:07:51] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -46.148952+0.000315j
[2025-09-18 19:08:04] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -46.138238+0.002606j
[2025-09-18 19:08:16] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -46.141052-0.002344j
[2025-09-18 19:08:28] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -46.146788-0.001363j
[2025-09-18 19:08:41] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -46.156914-0.006199j
[2025-09-18 19:08:53] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -46.144767-0.000954j
[2025-09-18 19:09:06] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -46.147220+0.001910j
[2025-09-18 19:09:18] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -46.160731-0.000360j
[2025-09-18 19:09:31] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -46.161087+0.001672j
[2025-09-18 19:09:43] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -46.152615+0.004403j
[2025-09-18 19:09:55] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -46.158942-0.001344j
[2025-09-18 19:10:08] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -46.154392-0.001037j
[2025-09-18 19:10:20] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -46.142863-0.005066j
[2025-09-18 19:10:33] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -46.154389-0.000385j
[2025-09-18 19:10:45] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -46.160477-0.000574j
[2025-09-18 19:10:58] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -46.143613+0.002030j
[2025-09-18 19:11:10] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -46.146386-0.002112j
[2025-09-18 19:11:23] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -46.137642-0.000131j
[2025-09-18 19:11:35] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -46.150354-0.002316j
[2025-09-18 19:11:48] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -46.145227-0.002546j
[2025-09-18 19:12:00] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -46.146961+0.002519j
[2025-09-18 19:12:13] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -46.150447-0.002022j
[2025-09-18 19:12:25] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -46.168007+0.000011j
[2025-09-18 19:12:38] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -46.165106+0.001535j
[2025-09-18 19:12:50] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -46.150241+0.001456j
[2025-09-18 19:13:03] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -46.154155-0.004333j
[2025-09-18 19:13:15] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -46.145601-0.001473j
[2025-09-18 19:13:28] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -46.143923-0.002088j
[2025-09-18 19:13:40] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -46.146052+0.000905j
[2025-09-18 19:13:53] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -46.164629+0.000759j
[2025-09-18 19:14:05] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -46.145100-0.001821j
[2025-09-18 19:14:17] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -46.148704-0.002123j
[2025-09-18 19:14:30] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -46.149645+0.002641j
[2025-09-18 19:14:30] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-18 19:14:42] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -46.136406-0.000491j
[2025-09-18 19:14:54] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -46.161195+0.000288j
[2025-09-18 19:15:07] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -46.145368-0.001321j
[2025-09-18 19:15:19] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -46.134990+0.002445j
[2025-09-18 19:15:31] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -46.149600+0.003052j
[2025-09-18 19:15:44] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -46.157190+0.002234j
[2025-09-18 19:15:56] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -46.135736-0.000167j
[2025-09-18 19:16:08] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -46.149966+0.001924j
[2025-09-18 19:16:21] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -46.157078-0.001054j
[2025-09-18 19:16:33] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -46.156728+0.000488j
[2025-09-18 19:16:45] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -46.150228+0.004523j
[2025-09-18 19:16:58] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -46.152394-0.000214j
[2025-09-18 19:17:10] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -46.129215-0.003835j
[2025-09-18 19:17:23] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -46.155261-0.003625j
[2025-09-18 19:17:35] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -46.148502-0.001601j
[2025-09-18 19:17:47] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -46.164360+0.000689j
[2025-09-18 19:18:00] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -46.150120-0.005701j
[2025-09-18 19:18:12] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -46.136945+0.001646j
[2025-09-18 19:18:24] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -46.143321+0.001930j
[2025-09-18 19:18:37] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -46.153356+0.003547j
[2025-09-18 19:18:49] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -46.146668+0.002332j
[2025-09-18 19:19:01] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -46.149560+0.002642j
[2025-09-18 19:19:14] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -46.158421-0.001708j
[2025-09-18 19:19:26] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -46.145032-0.006800j
[2025-09-18 19:19:38] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -46.157937-0.000334j
[2025-09-18 19:19:51] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -46.152411+0.000733j
[2025-09-18 19:20:03] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -46.139329-0.000111j
[2025-09-18 19:20:15] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -46.152981-0.000517j
[2025-09-18 19:20:28] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -46.164525-0.000201j
[2025-09-18 19:20:40] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -46.147927+0.001039j
[2025-09-18 19:20:40] RESTART #2 | Period: 600
[2025-09-18 19:20:52] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -46.154515+0.000241j
[2025-09-18 19:21:05] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -46.154716-0.004143j
[2025-09-18 19:21:17] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -46.153064-0.003225j
[2025-09-18 19:21:30] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -46.144861-0.000787j
[2025-09-18 19:21:42] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -46.146323+0.000903j
[2025-09-18 19:21:54] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -46.151839+0.001157j
[2025-09-18 19:22:07] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -46.147949-0.003256j
[2025-09-18 19:22:19] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -46.140342-0.003568j
[2025-09-18 19:22:31] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -46.152669+0.006426j
[2025-09-18 19:22:44] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -46.151628+0.001524j
[2025-09-18 19:22:56] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -46.139107-0.003686j
[2025-09-18 19:23:08] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -46.148921+0.000176j
[2025-09-18 19:23:21] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -46.139115+0.000023j
[2025-09-18 19:23:33] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -46.159207-0.001778j
[2025-09-18 19:23:45] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -46.148424+0.002607j
[2025-09-18 19:23:58] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -46.154359+0.002399j
[2025-09-18 19:24:10] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -46.138195+0.000450j
[2025-09-18 19:24:23] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -46.144570+0.000048j
[2025-09-18 19:24:35] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -46.161399-0.000301j
[2025-09-18 19:24:47] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -46.163236+0.000519j
[2025-09-18 19:25:00] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -46.145242+0.003608j
[2025-09-18 19:25:12] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -46.134887+0.001322j
[2025-09-18 19:25:25] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -46.148851+0.001857j
[2025-09-18 19:25:37] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -46.157969+0.000017j
[2025-09-18 19:25:49] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -46.140887+0.002615j
[2025-09-18 19:26:02] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -46.142732+0.000414j
[2025-09-18 19:26:14] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -46.143249+0.001468j
[2025-09-18 19:26:27] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -46.143119-0.000192j
[2025-09-18 19:26:39] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -46.151106-0.004009j
[2025-09-18 19:26:51] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -46.146131-0.000743j
[2025-09-18 19:27:04] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -46.142264-0.000407j
[2025-09-18 19:27:16] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -46.153571+0.002692j
[2025-09-18 19:27:28] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -46.144153-0.002507j
[2025-09-18 19:27:41] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -46.143404-0.002079j
[2025-09-18 19:27:53] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -46.142631+0.003452j
[2025-09-18 19:28:05] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -46.155333+0.001420j
[2025-09-18 19:28:18] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -46.144216-0.003347j
[2025-09-18 19:28:30] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -46.155597-0.000002j
[2025-09-18 19:28:42] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -46.146862-0.000592j
[2025-09-18 19:28:55] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -46.163379-0.000466j
[2025-09-18 19:29:07] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -46.142910+0.002049j
[2025-09-18 19:29:20] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -46.151329+0.001836j
[2025-09-18 19:29:32] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -46.148111+0.002558j
[2025-09-18 19:29:44] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -46.157997-0.001329j
[2025-09-18 19:29:57] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -46.154962-0.002330j
[2025-09-18 19:30:09] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -46.157570-0.001844j
[2025-09-18 19:30:22] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -46.149057-0.001162j
[2025-09-18 19:30:34] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -46.163785-0.000666j
[2025-09-18 19:30:46] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -46.148036+0.001962j
[2025-09-18 19:30:59] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -46.139626-0.000529j
[2025-09-18 19:31:11] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -46.146695+0.000978j
[2025-09-18 19:31:23] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -46.146024+0.000909j
[2025-09-18 19:31:36] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -46.147983-0.000216j
[2025-09-18 19:31:48] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -46.133718+0.003247j
[2025-09-18 19:32:01] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -46.144313+0.001233j
[2025-09-18 19:32:13] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -46.152473+0.002108j
[2025-09-18 19:32:26] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -46.162037+0.003006j
[2025-09-18 19:32:38] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -46.154147-0.000114j
[2025-09-18 19:32:50] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -46.155971+0.000515j
[2025-09-18 19:33:03] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -46.159314+0.001931j
[2025-09-18 19:33:15] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -46.137591-0.001115j
[2025-09-18 19:33:28] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -46.155969+0.001751j
[2025-09-18 19:33:40] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -46.139574-0.000018j
[2025-09-18 19:33:53] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -46.147251+0.000067j
[2025-09-18 19:34:05] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -46.151152-0.001062j
[2025-09-18 19:34:17] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -46.145971-0.007140j
[2025-09-18 19:34:30] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -46.163090-0.000993j
[2025-09-18 19:34:42] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -46.145221+0.001180j
[2025-09-18 19:34:54] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -46.156797-0.002186j
[2025-09-18 19:35:07] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -46.132629-0.000591j
[2025-09-18 19:35:19] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -46.150070+0.002205j
[2025-09-18 19:35:31] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -46.177505+0.004416j
[2025-09-18 19:35:44] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -46.152462+0.000840j
[2025-09-18 19:35:56] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -46.150303-0.000750j
[2025-09-18 19:36:08] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -46.153431-0.000177j
[2025-09-18 19:36:09] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-18 19:36:21] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -46.151188+0.000679j
[2025-09-18 19:36:33] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -46.152901+0.002170j
[2025-09-18 19:36:46] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -46.144243+0.002708j
[2025-09-18 19:36:58] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -46.147211+0.000370j
[2025-09-18 19:37:10] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -46.155872-0.004129j
[2025-09-18 19:37:23] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -46.147087+0.002878j
[2025-09-18 19:37:35] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -46.178576+0.000996j
[2025-09-18 19:37:47] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -46.162377-0.002777j
[2025-09-18 19:38:00] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -46.142530+0.002664j
[2025-09-18 19:38:12] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -46.149449-0.000247j
[2025-09-18 19:38:24] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -46.160516-0.000738j
[2025-09-18 19:38:37] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -46.152286+0.001049j
[2025-09-18 19:38:49] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -46.147630+0.002009j
[2025-09-18 19:39:01] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -46.148614+0.003149j
[2025-09-18 19:39:14] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -46.140648-0.002795j
[2025-09-18 19:39:26] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -46.157049+0.000598j
[2025-09-18 19:39:39] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -46.165681+0.001697j
[2025-09-18 19:39:51] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -46.157962+0.001029j
[2025-09-18 19:40:03] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -46.162302+0.011132j
[2025-09-18 19:40:16] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -46.135563+0.003085j
[2025-09-18 19:40:28] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -46.147777+0.000034j
[2025-09-18 19:40:40] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -46.155141-0.001216j
[2025-09-18 19:40:53] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -46.149242+0.003232j
[2025-09-18 19:41:05] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -46.146039-0.002281j
[2025-09-18 19:41:17] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -46.148310+0.000699j
[2025-09-18 19:41:30] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -46.148204+0.002059j
[2025-09-18 19:41:42] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -46.162038+0.005651j
[2025-09-18 19:41:55] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -46.154174-0.000307j
[2025-09-18 19:42:07] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -46.137123+0.013361j
[2025-09-18 19:42:19] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -46.150562-0.000695j
[2025-09-18 19:42:32] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -46.153063-0.001206j
[2025-09-18 19:42:44] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -46.143919-0.002609j
[2025-09-18 19:42:56] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -46.151635+0.000191j
[2025-09-18 19:43:09] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -46.156930+0.001263j
[2025-09-18 19:43:21] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -46.153410-0.002646j
[2025-09-18 19:43:34] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -46.158116+0.005254j
[2025-09-18 19:43:46] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -46.147120+0.002353j
[2025-09-18 19:43:58] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -46.147996-0.000195j
[2025-09-18 19:44:11] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -46.149874+0.001127j
[2025-09-18 19:44:23] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -46.148903-0.000511j
[2025-09-18 19:44:35] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -46.147406+0.001152j
[2025-09-18 19:44:48] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -46.140199-0.000567j
[2025-09-18 19:45:00] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -46.147270-0.001625j
[2025-09-18 19:45:12] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -46.144646-0.002512j
[2025-09-18 19:45:25] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -46.159479-0.003778j
[2025-09-18 19:45:37] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -46.159097-0.007201j
[2025-09-18 19:45:49] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -46.161956-0.002505j
[2025-09-18 19:46:02] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -46.137945-0.002353j
[2025-09-18 19:46:14] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -46.164651+0.003137j
[2025-09-18 19:46:26] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -46.158917-0.001836j
[2025-09-18 19:46:39] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -46.146905+0.000635j
[2025-09-18 19:46:51] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -46.155744+0.003621j
[2025-09-18 19:47:03] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -46.150393-0.001297j
[2025-09-18 19:47:16] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -46.152093-0.001485j
[2025-09-18 19:47:28] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -46.142557-0.006132j
[2025-09-18 19:47:40] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -46.148182-0.000428j
[2025-09-18 19:47:53] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -46.154770-0.001420j
[2025-09-18 19:48:05] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -46.152683-0.001793j
[2025-09-18 19:48:17] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -46.161206+0.000708j
[2025-09-18 19:48:30] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -46.138141-0.002652j
[2025-09-18 19:48:42] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -46.148114-0.000744j
[2025-09-18 19:48:55] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -46.157196+0.001509j
[2025-09-18 19:49:07] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -46.149299-0.001699j
[2025-09-18 19:49:19] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -46.152918+0.000193j
[2025-09-18 19:49:32] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -46.153582+0.003845j
[2025-09-18 19:49:44] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -46.136427-0.001324j
[2025-09-18 19:49:56] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -46.156804-0.001279j
[2025-09-18 19:50:09] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -46.150124+0.005304j
[2025-09-18 19:50:21] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -46.145174-0.000773j
[2025-09-18 19:50:34] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -46.157321+0.002016j
[2025-09-18 19:50:46] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -46.151714-0.001119j
[2025-09-18 19:50:58] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -46.147123+0.000022j
[2025-09-18 19:51:11] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -46.165078+0.000530j
[2025-09-18 19:51:23] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -46.152213+0.000564j
[2025-09-18 19:51:35] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -46.159000+0.001413j
[2025-09-18 19:51:48] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -46.136044+0.002236j
[2025-09-18 19:52:00] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -46.150131+0.002013j
[2025-09-18 19:52:12] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -46.144691-0.001192j
[2025-09-18 19:52:25] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -46.149871-0.004662j
[2025-09-18 19:52:37] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -46.142688-0.002564j
[2025-09-18 19:52:49] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -46.160757+0.002403j
[2025-09-18 19:53:02] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -46.153967+0.001220j
[2025-09-18 19:53:14] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -46.146439+0.001944j
[2025-09-18 19:53:26] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -46.146183-0.000705j
[2025-09-18 19:53:39] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -46.155422+0.000706j
[2025-09-18 19:53:51] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -46.152502+0.000349j
[2025-09-18 19:54:04] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -46.136398+0.001151j
[2025-09-18 19:54:16] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -46.151661-0.000126j
[2025-09-18 19:54:28] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -46.157235-0.003048j
[2025-09-18 19:54:41] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -46.149584+0.004338j
[2025-09-18 19:54:53] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -46.156521-0.002943j
[2025-09-18 19:55:05] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -46.132357+0.001617j
[2025-09-18 19:55:18] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -46.149945-0.000540j
[2025-09-18 19:55:30] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -46.153948-0.000775j
[2025-09-18 19:55:42] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -46.155158+0.000425j
[2025-09-18 19:55:55] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -46.148292+0.002476j
[2025-09-18 19:56:07] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -46.158633+0.001570j
[2025-09-18 19:56:19] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -46.149862-0.003463j
[2025-09-18 19:56:32] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -46.152149+0.004954j
[2025-09-18 19:56:44] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -46.135533+0.004419j
[2025-09-18 19:56:56] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -46.161038-0.002528j
[2025-09-18 19:57:08] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -46.160183+0.000574j
[2025-09-18 19:57:21] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -46.152504+0.003294j
[2025-09-18 19:57:33] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -46.146801-0.000514j
[2025-09-18 19:57:45] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -46.141706-0.005050j
[2025-09-18 19:57:46] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-18 19:57:58] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -46.146692+0.000828j
[2025-09-18 19:58:10] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -46.154696+0.000629j
[2025-09-18 19:58:22] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -46.147933+0.000869j
[2025-09-18 19:58:35] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -46.149979-0.000963j
[2025-09-18 19:58:47] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -46.143764+0.011939j
[2025-09-18 19:59:00] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -46.147501-0.001723j
[2025-09-18 19:59:12] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -46.154901+0.000310j
[2025-09-18 19:59:24] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -46.150496+0.000225j
[2025-09-18 19:59:37] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -46.158246+0.000266j
[2025-09-18 19:59:49] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -46.155564-0.001204j
[2025-09-18 20:00:01] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -46.169297-0.001098j
[2025-09-18 20:00:14] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -46.158615-0.000855j
[2025-09-18 20:00:26] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -46.161727-0.001454j
[2025-09-18 20:00:38] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -46.154761-0.003972j
[2025-09-18 20:00:51] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -46.153003-0.001160j
[2025-09-18 20:01:03] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -46.164104-0.001780j
[2025-09-18 20:01:15] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -46.151502+0.003032j
[2025-09-18 20:01:28] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -46.159159+0.002204j
[2025-09-18 20:01:40] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -46.144810-0.000990j
[2025-09-18 20:01:52] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -46.156178+0.003365j
[2025-09-18 20:02:05] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -46.140765-0.001585j
[2025-09-18 20:02:17] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -46.139180-0.002100j
[2025-09-18 20:02:29] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -46.152023+0.000308j
[2025-09-18 20:02:42] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -46.148367-0.000339j
[2025-09-18 20:02:54] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -46.154169-0.001257j
[2025-09-18 20:03:06] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -46.150998+0.001855j
[2025-09-18 20:03:19] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -46.150083-0.000295j
[2025-09-18 20:03:31] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -46.131823+0.006809j
[2025-09-18 20:03:44] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -46.161397-0.001245j
[2025-09-18 20:03:56] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -46.165244-0.003093j
[2025-09-18 20:04:08] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -46.140784+0.000032j
[2025-09-18 20:04:21] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -46.162893-0.001331j
[2025-09-18 20:04:33] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -46.149980+0.001209j
[2025-09-18 20:04:45] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -46.137923+0.002081j
[2025-09-18 20:04:58] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -46.151536-0.002069j
[2025-09-18 20:05:10] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -46.142259-0.001445j
[2025-09-18 20:05:23] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -46.154334+0.001621j
[2025-09-18 20:05:35] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -46.156775-0.004238j
[2025-09-18 20:05:47] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -46.144716+0.001649j
[2025-09-18 20:06:00] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -46.145953-0.001868j
[2025-09-18 20:06:12] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -46.155842+0.000540j
[2025-09-18 20:06:24] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -46.150031-0.001716j
[2025-09-18 20:06:36] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -46.159786-0.002165j
[2025-09-18 20:06:49] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -46.155833-0.001219j
[2025-09-18 20:07:01] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -46.139625-0.000281j
[2025-09-18 20:07:13] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -46.151873+0.000316j
[2025-09-18 20:07:26] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -46.155981-0.001693j
[2025-09-18 20:07:38] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -46.155339-0.000937j
[2025-09-18 20:07:50] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -46.161073-0.001101j
[2025-09-18 20:08:03] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -46.148313-0.000375j
[2025-09-18 20:08:15] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -46.145128+0.000437j
[2025-09-18 20:08:27] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -46.149775-0.000227j
[2025-09-18 20:08:40] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -46.150897+0.000174j
[2025-09-18 20:08:52] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -46.160805+0.000769j
[2025-09-18 20:09:05] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -46.136582+0.001544j
[2025-09-18 20:09:17] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -46.143339+0.000546j
[2025-09-18 20:09:29] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -46.145297-0.000892j
[2025-09-18 20:09:42] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -46.131580-0.000769j
[2025-09-18 20:09:54] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -46.155304+0.001747j
[2025-09-18 20:10:07] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -46.152134-0.001539j
[2025-09-18 20:10:19] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -46.148059+0.002184j
[2025-09-18 20:10:31] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -46.154967-0.001582j
[2025-09-18 20:10:44] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -46.142400+0.004561j
[2025-09-18 20:10:56] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -46.143064+0.001026j
[2025-09-18 20:11:08] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -46.145360-0.004839j
[2025-09-18 20:11:21] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -46.158042+0.000163j
[2025-09-18 20:11:33] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -46.167206-0.002760j
[2025-09-18 20:11:45] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -46.160965+0.002397j
[2025-09-18 20:11:58] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -46.162971+0.000193j
[2025-09-18 20:12:10] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -46.161045+0.000566j
[2025-09-18 20:12:22] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -46.154528-0.000742j
[2025-09-18 20:12:35] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -46.143986+0.001149j
[2025-09-18 20:12:47] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -46.155739+0.000567j
[2025-09-18 20:12:59] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -46.140355+0.004628j
[2025-09-18 20:13:12] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -46.174104-0.002830j
[2025-09-18 20:13:24] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -46.154831-0.000276j
[2025-09-18 20:13:36] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -46.152792-0.002307j
[2025-09-18 20:13:49] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -46.142329-0.001682j
[2025-09-18 20:14:01] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -46.149855+0.001240j
[2025-09-18 20:14:13] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -46.144815-0.001014j
[2025-09-18 20:14:26] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -46.142950-0.001207j
[2025-09-18 20:14:38] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -46.155977-0.000666j
[2025-09-18 20:14:50] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -46.154614-0.000587j
[2025-09-18 20:15:03] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -46.148244-0.001252j
[2025-09-18 20:15:15] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -46.151165+0.001754j
[2025-09-18 20:15:28] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -46.160022+0.001517j
[2025-09-18 20:15:40] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -46.145406-0.003610j
[2025-09-18 20:15:52] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -46.153038-0.003040j
[2025-09-18 20:16:05] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -46.151049-0.000665j
[2025-09-18 20:16:17] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -46.147003+0.001360j
[2025-09-18 20:16:29] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -46.147350-0.001711j
[2025-09-18 20:16:42] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -46.156842+0.003521j
[2025-09-18 20:16:54] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -46.145085-0.003753j
[2025-09-18 20:17:06] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -46.148416+0.001247j
[2025-09-18 20:17:19] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -46.147853-0.000837j
[2025-09-18 20:17:31] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -46.158414+0.000498j
[2025-09-18 20:17:44] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -46.152927-0.000772j
[2025-09-18 20:17:56] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -46.160497+0.001429j
[2025-09-18 20:18:08] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -46.166240-0.004226j
[2025-09-18 20:18:21] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -46.147878+0.000998j
[2025-09-18 20:18:33] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -46.151790-0.000607j
[2025-09-18 20:18:45] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -46.157068+0.002257j
[2025-09-18 20:18:58] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -46.151404-0.000773j
[2025-09-18 20:19:10] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -46.160137-0.000290j
[2025-09-18 20:19:22] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -46.146871-0.002432j
[2025-09-18 20:19:22] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-18 20:19:35] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -46.154017-0.002854j
[2025-09-18 20:19:47] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -46.165568+0.000404j
[2025-09-18 20:19:59] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -46.147177+0.000942j
[2025-09-18 20:20:12] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -46.165566+0.000454j
[2025-09-18 20:20:24] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -46.150595+0.001747j
[2025-09-18 20:20:36] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -46.151074-0.002037j
[2025-09-18 20:20:49] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -46.137790+0.000752j
[2025-09-18 20:21:01] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -46.153348-0.000425j
[2025-09-18 20:21:13] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -46.139699+0.001599j
[2025-09-18 20:21:26] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -46.151692+0.000475j
[2025-09-18 20:21:38] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -46.153269+0.001409j
[2025-09-18 20:21:50] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -46.138686+0.000859j
[2025-09-18 20:22:03] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -46.144750-0.003266j
[2025-09-18 20:22:15] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -46.149962-0.005030j
[2025-09-18 20:22:27] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -46.152471+0.000859j
[2025-09-18 20:22:40] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -46.151611+0.002440j
[2025-09-18 20:22:52] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -46.133613-0.000268j
[2025-09-18 20:23:04] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -46.149028+0.000235j
[2025-09-18 20:23:17] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -46.159252-0.002509j
[2025-09-18 20:23:29] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -46.146521-0.000875j
[2025-09-18 20:23:41] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -46.147081+0.000159j
[2025-09-18 20:23:54] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -46.147257+0.001910j
[2025-09-18 20:24:06] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -46.157394-0.000914j
[2025-09-18 20:24:18] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -46.159339-0.002956j
[2025-09-18 20:24:31] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -46.162745+0.002579j
[2025-09-18 20:24:43] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -46.138259+0.000405j
[2025-09-18 20:24:55] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -46.150085+0.001568j
[2025-09-18 20:25:08] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -46.134774+0.001361j
[2025-09-18 20:25:20] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -46.173241+0.004650j
[2025-09-18 20:25:32] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -46.147064-0.001667j
[2025-09-18 20:25:45] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -46.143354-0.003452j
[2025-09-18 20:25:57] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -46.140973-0.003485j
[2025-09-18 20:26:10] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -46.156565-0.002542j
[2025-09-18 20:26:22] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -46.155934+0.001250j
[2025-09-18 20:26:34] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -46.150362+0.003017j
[2025-09-18 20:26:47] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -46.149393+0.000042j
[2025-09-18 20:26:59] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -46.152343+0.001975j
[2025-09-18 20:27:11] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -46.148741-0.002326j
[2025-09-18 20:27:24] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -46.154771+0.001636j
[2025-09-18 20:27:36] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -46.156443-0.005251j
[2025-09-18 20:27:48] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -46.140268-0.002628j
[2025-09-18 20:28:01] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -46.140770-0.001190j
[2025-09-18 20:28:13] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -46.151937-0.000425j
[2025-09-18 20:28:25] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -46.151123+0.000815j
[2025-09-18 20:28:38] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -46.150975-0.001197j
[2025-09-18 20:28:50] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -46.148471-0.003082j
[2025-09-18 20:29:02] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -46.140233-0.000387j
[2025-09-18 20:29:15] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -46.159767+0.000310j
[2025-09-18 20:29:27] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -46.142279+0.002982j
[2025-09-18 20:29:39] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -46.154075-0.000293j
[2025-09-18 20:29:52] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -46.144842-0.002928j
[2025-09-18 20:30:04] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -46.149649-0.001557j
[2025-09-18 20:30:16] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -46.138602-0.003243j
[2025-09-18 20:30:29] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -46.153418-0.000875j
[2025-09-18 20:30:41] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -46.144510-0.003168j
[2025-09-18 20:30:53] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -46.164422+0.002621j
[2025-09-18 20:31:06] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -46.164435+0.000143j
[2025-09-18 20:31:18] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -46.136669-0.002726j
[2025-09-18 20:31:30] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -46.164331-0.001387j
[2025-09-18 20:31:43] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -46.139359+0.001282j
[2025-09-18 20:31:55] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -46.161188-0.000293j
[2025-09-18 20:32:07] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -46.152029+0.001857j
[2025-09-18 20:32:20] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -46.152348-0.000671j
[2025-09-18 20:32:32] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -46.143168-0.000801j
[2025-09-18 20:32:44] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -46.149316+0.001773j
[2025-09-18 20:32:57] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -46.145165-0.003947j
[2025-09-18 20:33:09] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -46.142018-0.003093j
[2025-09-18 20:33:21] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -46.158546-0.001886j
[2025-09-18 20:33:34] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -46.161180-0.002022j
[2025-09-18 20:33:46] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -46.148723+0.001112j
[2025-09-18 20:33:58] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -46.152736+0.001001j
[2025-09-18 20:34:11] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -46.161370+0.000607j
[2025-09-18 20:34:23] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -46.144896+0.003271j
[2025-09-18 20:34:35] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -46.140646-0.000671j
[2025-09-18 20:34:48] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -46.152614+0.001398j
[2025-09-18 20:35:00] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -46.136572-0.001413j
[2025-09-18 20:35:12] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -46.143193-0.000153j
[2025-09-18 20:35:25] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -46.146635+0.001120j
[2025-09-18 20:35:37] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -46.148358+0.002334j
[2025-09-18 20:35:49] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -46.150185+0.002443j
[2025-09-18 20:36:02] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -46.152680-0.001281j
[2025-09-18 20:36:14] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -46.166429+0.000605j
[2025-09-18 20:36:26] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -46.134737-0.000702j
[2025-09-18 20:36:39] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -46.148532-0.000119j
[2025-09-18 20:36:51] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -46.157384-0.002043j
[2025-09-18 20:37:03] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -46.156538-0.005869j
[2025-09-18 20:37:16] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -46.152481+0.003668j
[2025-09-18 20:37:28] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -46.154796-0.003385j
[2025-09-18 20:37:40] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -46.153282+0.000234j
[2025-09-18 20:37:53] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -46.155621+0.001472j
[2025-09-18 20:38:05] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -46.138278-0.001550j
[2025-09-18 20:38:18] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -46.151562+0.001331j
[2025-09-18 20:38:30] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -46.150752-0.001147j
[2025-09-18 20:38:42] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -46.157980+0.001166j
[2025-09-18 20:38:55] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -46.156719+0.000131j
[2025-09-18 20:39:07] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -46.153343+0.004141j
[2025-09-18 20:39:19] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -46.159132+0.002114j
[2025-09-18 20:39:32] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -46.143707-0.002784j
[2025-09-18 20:39:44] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -46.149393-0.000133j
[2025-09-18 20:39:56] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -46.149062-0.001862j
[2025-09-18 20:40:09] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -46.156839-0.001190j
[2025-09-18 20:40:21] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -46.136158-0.001912j
[2025-09-18 20:40:33] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -46.152496-0.001757j
[2025-09-18 20:40:46] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -46.163696+0.000797j
[2025-09-18 20:40:58] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -46.138004-0.000311j
[2025-09-18 20:40:58] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-18 20:41:10] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -46.150242+0.003601j
[2025-09-18 20:41:23] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -46.155838-0.003513j
[2025-09-18 20:41:35] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -46.141450+0.000523j
[2025-09-18 20:41:47] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -46.139066-0.002051j
[2025-09-18 20:42:00] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -46.139052+0.001382j
[2025-09-18 20:42:12] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -46.144219+0.002501j
[2025-09-18 20:42:24] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -46.173039-0.002166j
[2025-09-18 20:42:37] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -46.140327-0.000630j
[2025-09-18 20:42:49] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -46.161012+0.000156j
[2025-09-18 20:43:01] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -46.156389+0.001624j
[2025-09-18 20:43:14] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -46.161762-0.004502j
[2025-09-18 20:43:26] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -46.151681+0.002437j
[2025-09-18 20:43:38] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -46.151978+0.004440j
[2025-09-18 20:43:51] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -46.156170+0.001530j
[2025-09-18 20:44:03] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -46.164464+0.000501j
[2025-09-18 20:44:15] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -46.144458-0.001466j
[2025-09-18 20:44:28] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -46.167015-0.000281j
[2025-09-18 20:44:40] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -46.165680-0.001041j
[2025-09-18 20:44:52] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -46.155445-0.012888j
[2025-09-18 20:45:05] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -46.162730-0.002234j
[2025-09-18 20:45:17] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -46.146890-0.000551j
[2025-09-18 20:45:29] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -46.154669-0.000433j
[2025-09-18 20:45:42] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -46.156995-0.001458j
[2025-09-18 20:45:54] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -46.151465-0.003119j
[2025-09-18 20:46:06] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -46.164559-0.002069j
[2025-09-18 20:46:19] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -46.145706+0.004206j
[2025-09-18 20:46:31] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -46.154633-0.000372j
[2025-09-18 20:46:43] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -46.146473+0.003749j
[2025-09-18 20:46:56] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -46.153160+0.001008j
[2025-09-18 20:47:08] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -46.160285+0.000926j
[2025-09-18 20:47:21] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -46.156080+0.002001j
[2025-09-18 20:47:33] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -46.139976-0.008639j
[2025-09-18 20:47:45] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -46.149714-0.001262j
[2025-09-18 20:47:58] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -46.164694-0.000287j
[2025-09-18 20:48:10] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -46.144765+0.002556j
[2025-09-18 20:48:22] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -46.147963-0.002113j
[2025-09-18 20:48:35] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -46.156566-0.000430j
[2025-09-18 20:48:47] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -46.159226-0.000667j
[2025-09-18 20:48:59] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -46.148301-0.000812j
[2025-09-18 20:49:12] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -46.149004+0.002943j
[2025-09-18 20:49:24] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -46.148191+0.001369j
[2025-09-18 20:49:36] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -46.159375+0.000218j
[2025-09-18 20:49:49] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -46.165919+0.001176j
[2025-09-18 20:50:01] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -46.159524+0.000805j
[2025-09-18 20:50:13] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -46.158437+0.000383j
[2025-09-18 20:50:26] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -46.155247-0.001542j
[2025-09-18 20:50:38] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -46.139955-0.001824j
[2025-09-18 20:50:50] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -46.150733+0.001117j
[2025-09-18 20:51:03] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -46.134062+0.000640j
[2025-09-18 20:51:15] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -46.160464-0.001785j
[2025-09-18 20:51:27] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -46.142113+0.000099j
[2025-09-18 20:51:40] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -46.140354-0.002531j
[2025-09-18 20:51:52] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -46.145551-0.000354j
[2025-09-18 20:52:04] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -46.152958+0.002073j
[2025-09-18 20:52:17] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -46.158385-0.001577j
[2025-09-18 20:52:29] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -46.148122+0.002965j
[2025-09-18 20:52:41] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -46.147827-0.001307j
[2025-09-18 20:52:54] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -46.161055+0.002503j
[2025-09-18 20:53:06] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -46.149717-0.000728j
[2025-09-18 20:53:18] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -46.146745-0.000078j
[2025-09-18 20:53:31] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -46.158567+0.004032j
[2025-09-18 20:53:43] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -46.152899+0.001184j
[2025-09-18 20:53:55] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -46.151663+0.000242j
[2025-09-18 20:54:08] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -46.153314+0.001769j
[2025-09-18 20:54:20] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -46.144678-0.002498j
[2025-09-18 20:54:32] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -46.147244-0.004230j
[2025-09-18 20:54:45] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -46.145808+0.001203j
[2025-09-18 20:54:57] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -46.149779-0.002033j
[2025-09-18 20:55:09] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -46.126427+0.001888j
[2025-09-18 20:55:22] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -46.135402+0.001769j
[2025-09-18 20:55:34] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -46.157553-0.000998j
[2025-09-18 20:55:46] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -46.139542+0.002121j
[2025-09-18 20:55:59] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -46.151390-0.002709j
[2025-09-18 20:56:11] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -46.155731+0.001248j
[2025-09-18 20:56:23] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -46.154621+0.002601j
[2025-09-18 20:56:36] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -46.142140+0.000984j
[2025-09-18 20:56:48] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -46.126220-0.001260j
[2025-09-18 20:57:00] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -46.169596-0.003818j
[2025-09-18 20:57:13] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -46.155939-0.002759j
[2025-09-18 20:57:25] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -46.156738+0.000304j
[2025-09-18 20:57:38] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -46.140724-0.001834j
[2025-09-18 20:57:50] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -46.164466-0.004186j
[2025-09-18 20:58:02] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -46.145892-0.000848j
[2025-09-18 20:58:15] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -46.166425-0.001786j
[2025-09-18 20:58:27] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -46.141105-0.001855j
[2025-09-18 20:58:39] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -46.155610+0.002297j
[2025-09-18 20:58:52] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -46.156619-0.001582j
[2025-09-18 20:59:04] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -46.140215+0.001712j
[2025-09-18 20:59:16] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -46.154122+0.000709j
[2025-09-18 20:59:29] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -46.156285+0.004324j
[2025-09-18 20:59:41] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -46.158032-0.002053j
[2025-09-18 20:59:53] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -46.149649-0.003794j
[2025-09-18 21:00:06] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -46.158848-0.000249j
[2025-09-18 21:00:18] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -46.137982-0.001706j
[2025-09-18 21:00:31] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -46.167503+0.000137j
[2025-09-18 21:00:43] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -46.151984+0.002303j
[2025-09-18 21:00:55] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -46.150456+0.000037j
[2025-09-18 21:01:08] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -46.148924-0.003172j
[2025-09-18 21:01:20] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -46.154723-0.002510j
[2025-09-18 21:01:32] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -46.162185+0.001225j
[2025-09-18 21:01:45] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -46.154822+0.004198j
[2025-09-18 21:01:57] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -46.128359-0.005550j
[2025-09-18 21:02:10] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -46.159025+0.000601j
[2025-09-18 21:02:22] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -46.157511-0.000636j
[2025-09-18 21:02:34] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -46.150390+0.000566j
[2025-09-18 21:02:35] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-18 21:02:47] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -46.152643+0.001992j
[2025-09-18 21:02:59] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -46.153021+0.000573j
[2025-09-18 21:03:12] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -46.161801-0.003757j
[2025-09-18 21:03:24] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -46.160307+0.003430j
[2025-09-18 21:03:36] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -46.153897-0.001024j
[2025-09-18 21:03:49] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -46.150448-0.002498j
[2025-09-18 21:04:01] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -46.154044-0.000920j
[2025-09-18 21:04:13] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -46.147866-0.000991j
[2025-09-18 21:04:26] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -46.149152+0.001316j
[2025-09-18 21:04:38] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -46.153781+0.002544j
[2025-09-18 21:04:50] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -46.156299-0.005411j
[2025-09-18 21:05:03] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -46.146018+0.010755j
[2025-09-18 21:05:15] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -46.162594-0.001304j
[2025-09-18 21:05:27] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -46.155806-0.000330j
[2025-09-18 21:05:40] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -46.154432+0.001183j
[2025-09-18 21:05:52] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -46.161355+0.001533j
[2025-09-18 21:06:04] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -46.147427+0.003194j
[2025-09-18 21:06:17] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -46.147022-0.001960j
[2025-09-18 21:06:29] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -46.146581+0.001390j
[2025-09-18 21:06:42] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -46.154834-0.001595j
[2025-09-18 21:06:54] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -46.140050-0.001211j
[2025-09-18 21:07:06] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -46.152291+0.000861j
[2025-09-18 21:07:19] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -46.147493+0.002681j
[2025-09-18 21:07:31] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -46.146435+0.002698j
[2025-09-18 21:07:43] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -46.152318-0.002902j
[2025-09-18 21:07:56] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -46.163047-0.004907j
[2025-09-18 21:08:08] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -46.145219-0.000615j
[2025-09-18 21:08:20] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -46.134751-0.009265j
[2025-09-18 21:08:33] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -46.142723+0.002272j
[2025-09-18 21:08:45] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -46.149682-0.000564j
[2025-09-18 21:08:57] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -46.137827-0.000606j
[2025-09-18 21:09:10] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -46.159812+0.000694j
[2025-09-18 21:09:22] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -46.150374-0.004339j
[2025-09-18 21:09:34] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -46.153434+0.001393j
[2025-09-18 21:09:47] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -46.153769+0.002597j
[2025-09-18 21:09:59] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -46.150785-0.000209j
[2025-09-18 21:10:11] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -46.158393+0.000522j
[2025-09-18 21:10:24] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -46.158369-0.001541j
[2025-09-18 21:10:36] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -46.148122-0.001770j
[2025-09-18 21:10:48] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -46.156437+0.000200j
[2025-09-18 21:11:01] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -46.149203-0.001067j
[2025-09-18 21:11:13] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -46.156500-0.000363j
[2025-09-18 21:11:25] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -46.129248-0.000342j
[2025-09-18 21:11:38] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -46.151250-0.002131j
[2025-09-18 21:11:50] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -46.157640+0.001924j
[2025-09-18 21:12:02] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -46.145934+0.001350j
[2025-09-18 21:12:15] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -46.153067+0.001378j
[2025-09-18 21:12:27] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -46.164802-0.002778j
[2025-09-18 21:12:39] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -46.155652+0.006442j
[2025-09-18 21:12:52] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -46.148741+0.002378j
[2025-09-18 21:13:04] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -46.151963+0.000909j
[2025-09-18 21:13:16] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -46.153821+0.000537j
[2025-09-18 21:13:29] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -46.147345-0.000498j
[2025-09-18 21:13:41] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -46.148576+0.000565j
[2025-09-18 21:13:53] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -46.161059-0.005181j
[2025-09-18 21:14:06] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -46.149694+0.003499j
[2025-09-18 21:14:18] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -46.156074+0.000250j
[2025-09-18 21:14:30] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -46.157747+0.002477j
[2025-09-18 21:14:43] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -46.148765+0.002140j
[2025-09-18 21:14:55] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -46.153148-0.002386j
[2025-09-18 21:15:07] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -46.150990+0.000711j
[2025-09-18 21:15:20] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -46.149141-0.000955j
[2025-09-18 21:15:32] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -46.152726+0.000653j
[2025-09-18 21:15:45] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -46.158387-0.001141j
[2025-09-18 21:15:57] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -46.141388+0.001469j
[2025-09-18 21:16:10] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -46.153399-0.001943j
[2025-09-18 21:16:22] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -46.154084-0.000433j
[2025-09-18 21:16:35] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -46.133867-0.002080j
[2025-09-18 21:16:47] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -46.144269-0.000582j
[2025-09-18 21:16:59] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -46.157726-0.004686j
[2025-09-18 21:17:12] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -46.166795-0.002012j
[2025-09-18 21:17:24] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -46.151314-0.002355j
[2025-09-18 21:17:37] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -46.156651+0.001140j
[2025-09-18 21:17:49] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -46.147400-0.000417j
[2025-09-18 21:18:01] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -46.156816-0.001568j
[2025-09-18 21:18:14] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -46.156148-0.002618j
[2025-09-18 21:18:26] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -46.153317-0.001004j
[2025-09-18 21:18:38] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -46.150646-0.000672j
[2025-09-18 21:18:51] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -46.144767+0.000027j
[2025-09-18 21:19:03] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -46.153931+0.001639j
[2025-09-18 21:19:16] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -46.154464+0.002325j
[2025-09-18 21:19:28] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -46.156599+0.003155j
[2025-09-18 21:19:40] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -46.149000+0.002194j
[2025-09-18 21:19:52] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -46.155488-0.001495j
[2025-09-18 21:20:05] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -46.154031+0.001237j
[2025-09-18 21:20:17] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -46.155504+0.000932j
[2025-09-18 21:20:29] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -46.156306-0.003146j
[2025-09-18 21:20:42] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -46.163422+0.000178j
[2025-09-18 21:20:54] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -46.148034-0.001423j
[2025-09-18 21:21:06] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -46.151366-0.001495j
[2025-09-18 21:21:19] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -46.153585+0.006177j
[2025-09-18 21:21:31] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -46.139482+0.001929j
[2025-09-18 21:21:43] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -46.159801-0.004174j
[2025-09-18 21:21:56] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -46.158882+0.003439j
[2025-09-18 21:22:08] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -46.139995+0.000007j
[2025-09-18 21:22:20] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -46.149940-0.000091j
[2025-09-18 21:22:33] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -46.157698+0.004318j
[2025-09-18 21:22:45] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -46.155978+0.002478j
[2025-09-18 21:22:57] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -46.153086-0.000919j
[2025-09-18 21:23:10] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -46.153339+0.002153j
[2025-09-18 21:23:22] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -46.156583-0.002007j
[2025-09-18 21:23:34] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -46.153163+0.001011j
[2025-09-18 21:23:47] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -46.149024-0.000732j
[2025-09-18 21:23:59] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -46.160321-0.001600j
[2025-09-18 21:24:11] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -46.154013+0.001699j
[2025-09-18 21:24:11] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-18 21:24:11] ✅ Training completed | Restarts: 2
[2025-09-18 21:24:11] ============================================================
[2025-09-18 21:24:11] Training completed | Runtime: 13049.3s
[2025-09-18 21:24:16] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-18 21:24:16] ============================================================
