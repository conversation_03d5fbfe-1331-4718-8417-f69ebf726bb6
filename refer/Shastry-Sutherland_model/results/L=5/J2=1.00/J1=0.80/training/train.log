[2025-09-17 16:22:26] ==================================================
[2025-09-17 16:22:26] GCNN for Shastry-Sutherland Model
[2025-09-17 16:22:26] ==================================================
[2025-09-17 16:22:26] System parameters:
[2025-09-17 16:22:26]   - System size: L=5, N=100
[2025-09-17 16:22:26]   - System parameters: J1=0.8, J2=1.0, Q=0.0
[2025-09-17 16:22:26] --------------------------------------------------
[2025-09-17 16:22:26] Model parameters:
[2025-09-17 16:22:26]   - Number of layers = 4
[2025-09-17 16:22:26]   - Number of features = 4
[2025-09-17 16:22:26]   - Total parameters = 19628
[2025-09-17 16:22:26] --------------------------------------------------
[2025-09-17 16:22:26] Training parameters:
[2025-09-17 16:22:26]   - Learning rate: 0.015
[2025-09-17 16:22:26]   - Total iterations: 2250
[2025-09-17 16:22:26]   - Annealing cycles: 4
[2025-09-17 16:22:26]   - Initial period: 150
[2025-09-17 16:22:26]   - Period multiplier: 2.0
[2025-09-17 16:22:26]   - Temperature range: 0.0-1.0
[2025-09-17 16:22:26]   - Samples: 8192
[2025-09-17 16:22:26]   - Discarded samples: 0
[2025-09-17 16:22:26]   - Chunk size: 2048
[2025-09-17 16:22:26]   - Diagonal shift: 0.2
[2025-09-17 16:22:26]   - Gradient clipping: 1.0
[2025-09-17 16:22:26]   - Checkpoint enabled: interval=250
[2025-09-17 16:22:26]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.80/training/checkpoints
[2025-09-17 16:22:26] --------------------------------------------------
[2025-09-17 16:22:26] Device status:
[2025-09-17 16:22:26]   - Devices model: NVIDIA H200 NVL
[2025-09-17 16:22:26]   - Number of devices: 1
[2025-09-17 16:22:26]   - Sharding: True
[2025-09-17 16:22:26] ============================================================
[2025-09-17 16:23:02] [Iter 1/2250] R0[0/150], Temp: 1.0000, Energy: 52.495314-0.001305j
[2025-09-17 16:23:13] [Iter 2/2250] R0[1/150], Temp: 0.9999, Energy: 52.490170-0.002919j
[2025-09-17 16:23:25] [Iter 3/2250] R0[2/150], Temp: 0.9996, Energy: 52.493153-0.000478j
[2025-09-17 16:23:36] [Iter 4/2250] R0[3/150], Temp: 0.9990, Energy: 52.485420-0.002351j
[2025-09-17 16:23:48] [Iter 5/2250] R0[4/150], Temp: 0.9982, Energy: 52.484627-0.001410j
[2025-09-17 16:23:59] [Iter 6/2250] R0[5/150], Temp: 0.9973, Energy: 52.486557+0.000672j
[2025-09-17 16:24:11] [Iter 7/2250] R0[6/150], Temp: 0.9961, Energy: 52.480613+0.005119j
[2025-09-17 16:24:22] [Iter 8/2250] R0[7/150], Temp: 0.9946, Energy: 52.476972+0.001329j
[2025-09-17 16:24:34] [Iter 9/2250] R0[8/150], Temp: 0.9930, Energy: 52.466523-0.005305j
[2025-09-17 16:24:45] [Iter 10/2250] R0[9/150], Temp: 0.9911, Energy: 52.461609+0.003526j
[2025-09-17 16:24:57] [Iter 11/2250] R0[10/150], Temp: 0.9891, Energy: 52.451828-0.005916j
[2025-09-17 16:25:09] [Iter 12/2250] R0[11/150], Temp: 0.9868, Energy: 52.429505-0.013114j
[2025-09-17 16:25:20] [Iter 13/2250] R0[12/150], Temp: 0.9843, Energy: 52.412265+0.006147j
[2025-09-17 16:25:32] [Iter 14/2250] R0[13/150], Temp: 0.9816, Energy: 52.387735-0.010542j
[2025-09-17 16:25:43] [Iter 15/2250] R0[14/150], Temp: 0.9787, Energy: 52.359305+0.018130j
[2025-09-17 16:25:55] [Iter 16/2250] R0[15/150], Temp: 0.9755, Energy: 52.304531+0.006550j
[2025-09-17 16:26:06] [Iter 17/2250] R0[16/150], Temp: 0.9722, Energy: 52.242861+0.009504j
[2025-09-17 16:26:18] [Iter 18/2250] R0[17/150], Temp: 0.9686, Energy: 52.146587-0.002257j
[2025-09-17 16:26:29] [Iter 19/2250] R0[18/150], Temp: 0.9649, Energy: 52.019223+0.004885j
[2025-09-17 16:26:41] [Iter 20/2250] R0[19/150], Temp: 0.9609, Energy: 51.839605+0.017104j
[2025-09-17 16:26:53] [Iter 21/2250] R0[20/150], Temp: 0.9568, Energy: 51.574167-0.029998j
[2025-09-17 16:27:04] [Iter 22/2250] R0[21/150], Temp: 0.9524, Energy: 51.230050+0.001929j
[2025-09-17 16:27:16] [Iter 23/2250] R0[22/150], Temp: 0.9479, Energy: 50.692766+0.023964j
[2025-09-17 16:27:27] [Iter 24/2250] R0[23/150], Temp: 0.9431, Energy: 49.989259-0.034630j
[2025-09-17 16:27:39] [Iter 25/2250] R0[24/150], Temp: 0.9382, Energy: 48.793133-0.054566j
[2025-09-17 16:27:50] [Iter 26/2250] R0[25/150], Temp: 0.9330, Energy: 47.063053-0.006948j
[2025-09-17 16:28:02] [Iter 27/2250] R0[26/150], Temp: 0.9277, Energy: 44.559824+0.021088j
[2025-09-17 16:28:14] [Iter 28/2250] R0[27/150], Temp: 0.9222, Energy: 40.999100-0.019603j
[2025-09-17 16:28:25] [Iter 29/2250] R0[28/150], Temp: 0.9165, Energy: 36.982483+0.105904j
[2025-09-17 16:28:37] [Iter 30/2250] R0[29/150], Temp: 0.9106, Energy: 32.963208+0.025084j
[2025-09-17 16:28:48] [Iter 31/2250] R0[30/150], Temp: 0.9045, Energy: 29.492274+0.108811j
[2025-09-17 16:29:00] [Iter 32/2250] R0[31/150], Temp: 0.8983, Energy: 26.218808+0.026604j
[2025-09-17 16:29:11] [Iter 33/2250] R0[32/150], Temp: 0.8918, Energy: 23.421967-0.032127j
[2025-09-17 16:29:23] [Iter 34/2250] R0[33/150], Temp: 0.8853, Energy: 20.546641+0.092989j
[2025-09-17 16:29:34] [Iter 35/2250] R0[34/150], Temp: 0.8785, Energy: 17.763055-0.039591j
[2025-09-17 16:29:46] [Iter 36/2250] R0[35/150], Temp: 0.8716, Energy: 15.174769-0.002503j
[2025-09-17 16:29:58] [Iter 37/2250] R0[36/150], Temp: 0.8645, Energy: 12.480466+0.017180j
[2025-09-17 16:30:09] [Iter 38/2250] R0[37/150], Temp: 0.8572, Energy: 10.031308-0.033737j
[2025-09-17 16:30:21] [Iter 39/2250] R0[38/150], Temp: 0.8498, Energy: 7.658639+0.018004j
[2025-09-17 16:30:32] [Iter 40/2250] R0[39/150], Temp: 0.8423, Energy: 5.511494-0.030880j
[2025-09-17 16:30:44] [Iter 41/2250] R0[40/150], Temp: 0.8346, Energy: 3.354811+0.009733j
[2025-09-17 16:30:55] [Iter 42/2250] R0[41/150], Temp: 0.8267, Energy: 1.387393-0.012499j
[2025-09-17 16:31:07] [Iter 43/2250] R0[42/150], Temp: 0.8187, Energy: -0.431763+0.019151j
[2025-09-17 16:31:18] [Iter 44/2250] R0[43/150], Temp: 0.8106, Energy: -1.933392-0.041842j
[2025-09-17 16:31:30] [Iter 45/2250] R0[44/150], Temp: 0.8023, Energy: -3.335757-0.055552j
[2025-09-17 16:31:42] [Iter 46/2250] R0[45/150], Temp: 0.7939, Energy: -4.789296-0.074768j
[2025-09-17 16:31:53] [Iter 47/2250] R0[46/150], Temp: 0.7854, Energy: -5.983118+0.050233j
[2025-09-17 16:32:05] [Iter 48/2250] R0[47/150], Temp: 0.7767, Energy: -7.108609-0.033730j
[2025-09-17 16:32:16] [Iter 49/2250] R0[48/150], Temp: 0.7679, Energy: -8.143639+0.058431j
[2025-09-17 16:32:28] [Iter 50/2250] R0[49/150], Temp: 0.7590, Energy: -9.256583-0.095876j
[2025-09-17 16:32:39] [Iter 51/2250] R0[50/150], Temp: 0.7500, Energy: -10.189585+0.036146j
[2025-09-17 16:32:51] [Iter 52/2250] R0[51/150], Temp: 0.7409, Energy: -11.046449+0.010540j
[2025-09-17 16:33:03] [Iter 53/2250] R0[52/150], Temp: 0.7316, Energy: -11.848757+0.043757j
[2025-09-17 16:33:14] [Iter 54/2250] R0[53/150], Temp: 0.7223, Energy: -12.614486+0.001735j
[2025-09-17 16:33:26] [Iter 55/2250] R0[54/150], Temp: 0.7129, Energy: -13.403820-0.004124j
[2025-09-17 16:33:37] [Iter 56/2250] R0[55/150], Temp: 0.7034, Energy: -14.109185+0.039002j
[2025-09-17 16:33:49] [Iter 57/2250] R0[56/150], Temp: 0.6938, Energy: -14.703005+0.036516j
[2025-09-17 16:34:00] [Iter 58/2250] R0[57/150], Temp: 0.6841, Energy: -15.484514-0.032110j
[2025-09-17 16:34:12] [Iter 59/2250] R0[58/150], Temp: 0.6743, Energy: -16.028275-0.050941j
[2025-09-17 16:34:24] [Iter 60/2250] R0[59/150], Temp: 0.6644, Energy: -16.635783-0.065608j
[2025-09-17 16:34:35] [Iter 61/2250] R0[60/150], Temp: 0.6545, Energy: -17.085135+0.013682j
[2025-09-17 16:34:47] [Iter 62/2250] R0[61/150], Temp: 0.6445, Energy: -17.700216-0.065359j
[2025-09-17 16:34:58] [Iter 63/2250] R0[62/150], Temp: 0.6345, Energy: -18.294045+0.017647j
[2025-09-17 16:35:10] [Iter 64/2250] R0[63/150], Temp: 0.6243, Energy: -18.691986+0.031977j
[2025-09-17 16:35:21] [Iter 65/2250] R0[64/150], Temp: 0.6142, Energy: -19.205590+0.025506j
[2025-09-17 16:35:33] [Iter 66/2250] R0[65/150], Temp: 0.6040, Energy: -19.679106+0.005564j
[2025-09-17 16:35:44] [Iter 67/2250] R0[66/150], Temp: 0.5937, Energy: -20.092348-0.018537j
[2025-09-17 16:35:56] [Iter 68/2250] R0[67/150], Temp: 0.5834, Energy: -20.547877-0.008769j
[2025-09-17 16:36:08] [Iter 69/2250] R0[68/150], Temp: 0.5730, Energy: -20.909846+0.024968j
[2025-09-17 16:36:19] [Iter 70/2250] R0[69/150], Temp: 0.5627, Energy: -21.339660-0.001902j
[2025-09-17 16:36:31] [Iter 71/2250] R0[70/150], Temp: 0.5523, Energy: -21.651134-0.058696j
[2025-09-17 16:36:42] [Iter 72/2250] R0[71/150], Temp: 0.5418, Energy: -22.026189-0.008180j
[2025-09-17 16:36:54] [Iter 73/2250] R0[72/150], Temp: 0.5314, Energy: -22.426716-0.019756j
[2025-09-17 16:37:05] [Iter 74/2250] R0[73/150], Temp: 0.5209, Energy: -22.688954+0.036876j
[2025-09-17 16:37:17] [Iter 75/2250] R0[74/150], Temp: 0.5105, Energy: -23.089544+0.021720j
[2025-09-17 16:37:28] [Iter 76/2250] R0[75/150], Temp: 0.5000, Energy: -23.339245+0.062571j
[2025-09-17 16:37:40] [Iter 77/2250] R0[76/150], Temp: 0.4895, Energy: -23.755028-0.021945j
[2025-09-17 16:37:51] [Iter 78/2250] R0[77/150], Temp: 0.4791, Energy: -24.049105-0.029798j
[2025-09-17 16:38:03] [Iter 79/2250] R0[78/150], Temp: 0.4686, Energy: -24.230062-0.011384j
[2025-09-17 16:38:15] [Iter 80/2250] R0[79/150], Temp: 0.4582, Energy: -24.583032+0.009446j
[2025-09-17 16:38:26] [Iter 81/2250] R0[80/150], Temp: 0.4477, Energy: -24.817405-0.076340j
[2025-09-17 16:38:38] [Iter 82/2250] R0[81/150], Temp: 0.4373, Energy: -25.112364+0.023145j
[2025-09-17 16:38:49] [Iter 83/2250] R0[82/150], Temp: 0.4270, Energy: -25.364692-0.031729j
[2025-09-17 16:39:01] [Iter 84/2250] R0[83/150], Temp: 0.4166, Energy: -25.589925-0.029350j
[2025-09-17 16:39:12] [Iter 85/2250] R0[84/150], Temp: 0.4063, Energy: -25.823693+0.014848j
[2025-09-17 16:39:24] [Iter 86/2250] R0[85/150], Temp: 0.3960, Energy: -26.021142-0.015786j
[2025-09-17 16:39:35] [Iter 87/2250] R0[86/150], Temp: 0.3858, Energy: -26.256457+0.005577j
[2025-09-17 16:39:47] [Iter 88/2250] R0[87/150], Temp: 0.3757, Energy: -26.457494-0.015648j
[2025-09-17 16:39:59] [Iter 89/2250] R0[88/150], Temp: 0.3655, Energy: -26.703960-0.025109j
[2025-09-17 16:40:10] [Iter 90/2250] R0[89/150], Temp: 0.3555, Energy: -26.869504-0.033453j
[2025-09-17 16:40:22] [Iter 91/2250] R0[90/150], Temp: 0.3455, Energy: -27.044355+0.002636j
[2025-09-17 16:40:33] [Iter 92/2250] R0[91/150], Temp: 0.3356, Energy: -27.299175+0.037698j
[2025-09-17 16:40:45] [Iter 93/2250] R0[92/150], Temp: 0.3257, Energy: -27.471421-0.003763j
[2025-09-17 16:40:56] [Iter 94/2250] R0[93/150], Temp: 0.3159, Energy: -27.657896-0.030816j
[2025-09-17 16:41:08] [Iter 95/2250] R0[94/150], Temp: 0.3062, Energy: -27.850841+0.039483j
[2025-09-17 16:41:19] [Iter 96/2250] R0[95/150], Temp: 0.2966, Energy: -28.017792+0.019339j
[2025-09-17 16:41:31] [Iter 97/2250] R0[96/150], Temp: 0.2871, Energy: -28.143499+0.032576j
[2025-09-17 16:41:43] [Iter 98/2250] R0[97/150], Temp: 0.2777, Energy: -28.284993+0.003186j
[2025-09-17 16:41:54] [Iter 99/2250] R0[98/150], Temp: 0.2684, Energy: -28.507292-0.016348j
[2025-09-17 16:42:06] [Iter 100/2250] R0[99/150], Temp: 0.2591, Energy: -28.644361+0.025286j
[2025-09-17 16:42:18] [Iter 101/2250] R0[100/150], Temp: 0.2500, Energy: -28.857963+0.022038j
[2025-09-17 16:42:30] [Iter 102/2250] R0[101/150], Temp: 0.2410, Energy: -28.959809-0.052841j
[2025-09-17 16:42:41] [Iter 103/2250] R0[102/150], Temp: 0.2321, Energy: -29.116841-0.014124j
[2025-09-17 16:42:53] [Iter 104/2250] R0[103/150], Temp: 0.2233, Energy: -29.258196-0.006863j
[2025-09-17 16:43:05] [Iter 105/2250] R0[104/150], Temp: 0.2146, Energy: -29.451078-0.025584j
[2025-09-17 16:43:16] [Iter 106/2250] R0[105/150], Temp: 0.2061, Energy: -29.509791+0.021317j
[2025-09-17 16:43:28] [Iter 107/2250] R0[106/150], Temp: 0.1977, Energy: -29.703078-0.018616j
[2025-09-17 16:43:39] [Iter 108/2250] R0[107/150], Temp: 0.1894, Energy: -29.825436+0.049680j
[2025-09-17 16:43:51] [Iter 109/2250] R0[108/150], Temp: 0.1813, Energy: -29.993186+0.004904j
[2025-09-17 16:44:02] [Iter 110/2250] R0[109/150], Temp: 0.1733, Energy: -30.109191-0.005094j
[2025-09-17 16:44:14] [Iter 111/2250] R0[110/150], Temp: 0.1654, Energy: -30.259831-0.007299j
[2025-09-17 16:44:25] [Iter 112/2250] R0[111/150], Temp: 0.1577, Energy: -30.353895-0.005204j
[2025-09-17 16:44:37] [Iter 113/2250] R0[112/150], Temp: 0.1502, Energy: -30.534101+0.000546j
[2025-09-17 16:44:49] [Iter 114/2250] R0[113/150], Temp: 0.1428, Energy: -30.651384-0.011607j
[2025-09-17 16:45:00] [Iter 115/2250] R0[114/150], Temp: 0.1355, Energy: -30.757260-0.025100j
[2025-09-17 16:45:12] [Iter 116/2250] R0[115/150], Temp: 0.1284, Energy: -30.912192-0.047869j
[2025-09-17 16:45:23] [Iter 117/2250] R0[116/150], Temp: 0.1215, Energy: -31.020979-0.023729j
[2025-09-17 16:45:35] [Iter 118/2250] R0[117/150], Temp: 0.1147, Energy: -31.137196-0.041140j
[2025-09-17 16:45:46] [Iter 119/2250] R0[118/150], Temp: 0.1082, Energy: -31.231269-0.001084j
[2025-09-17 16:45:58] [Iter 120/2250] R0[119/150], Temp: 0.1017, Energy: -31.277942-0.017030j
[2025-09-17 16:46:09] [Iter 121/2250] R0[120/150], Temp: 0.0955, Energy: -31.514029+0.004932j
[2025-09-17 16:46:21] [Iter 122/2250] R0[121/150], Temp: 0.0894, Energy: -31.548054+0.014733j
[2025-09-17 16:46:33] [Iter 123/2250] R0[122/150], Temp: 0.0835, Energy: -31.705941+0.015254j
[2025-09-17 16:46:44] [Iter 124/2250] R0[123/150], Temp: 0.0778, Energy: -31.911173-0.009033j
[2025-09-17 16:46:56] [Iter 125/2250] R0[124/150], Temp: 0.0723, Energy: -31.942783-0.017531j
[2025-09-17 16:47:07] [Iter 126/2250] R0[125/150], Temp: 0.0670, Energy: -32.115739-0.031495j
[2025-09-17 16:47:19] [Iter 127/2250] R0[126/150], Temp: 0.0618, Energy: -32.222869-0.001072j
[2025-09-17 16:47:30] [Iter 128/2250] R0[127/150], Temp: 0.0569, Energy: -32.334615-0.018632j
[2025-09-17 16:47:42] [Iter 129/2250] R0[128/150], Temp: 0.0521, Energy: -32.422431+0.037760j
[2025-09-17 16:47:53] [Iter 130/2250] R0[129/150], Temp: 0.0476, Energy: -32.589211+0.037251j
[2025-09-17 16:48:05] [Iter 131/2250] R0[130/150], Temp: 0.0432, Energy: -32.701421+0.006237j
[2025-09-17 16:48:16] [Iter 132/2250] R0[131/150], Temp: 0.0391, Energy: -32.827824-0.019132j
[2025-09-17 16:48:28] [Iter 133/2250] R0[132/150], Temp: 0.0351, Energy: -32.858791+0.002760j
[2025-09-17 16:48:40] [Iter 134/2250] R0[133/150], Temp: 0.0314, Energy: -33.049843-0.010701j
[2025-09-17 16:48:51] [Iter 135/2250] R0[134/150], Temp: 0.0278, Energy: -33.099326-0.006963j
[2025-09-17 16:49:03] [Iter 136/2250] R0[135/150], Temp: 0.0245, Energy: -33.236122+0.030767j
[2025-09-17 16:49:14] [Iter 137/2250] R0[136/150], Temp: 0.0213, Energy: -33.345530-0.038358j
[2025-09-17 16:49:26] [Iter 138/2250] R0[137/150], Temp: 0.0184, Energy: -33.501238-0.023005j
[2025-09-17 16:49:37] [Iter 139/2250] R0[138/150], Temp: 0.0157, Energy: -33.582034+0.004227j
[2025-09-17 16:49:49] [Iter 140/2250] R0[139/150], Temp: 0.0132, Energy: -33.616298+0.107510j
[2025-09-17 16:50:00] [Iter 141/2250] R0[140/150], Temp: 0.0109, Energy: -33.800509+0.012638j
[2025-09-17 16:50:12] [Iter 142/2250] R0[141/150], Temp: 0.0089, Energy: -33.879574-0.021207j
[2025-09-17 16:50:24] [Iter 143/2250] R0[142/150], Temp: 0.0070, Energy: -33.985574+0.000666j
[2025-09-17 16:50:35] [Iter 144/2250] R0[143/150], Temp: 0.0054, Energy: -34.073923-0.035636j
[2025-09-17 16:50:47] [Iter 145/2250] R0[144/150], Temp: 0.0039, Energy: -34.182238-0.001763j
[2025-09-17 16:50:58] [Iter 146/2250] R0[145/150], Temp: 0.0027, Energy: -34.307958-0.027205j
[2025-09-17 16:51:10] [Iter 147/2250] R0[146/150], Temp: 0.0018, Energy: -34.408787-0.036094j
[2025-09-17 16:51:21] [Iter 148/2250] R0[147/150], Temp: 0.0010, Energy: -34.512721+0.035232j
[2025-09-17 16:51:33] [Iter 149/2250] R0[148/150], Temp: 0.0004, Energy: -34.579195-0.043558j
[2025-09-17 16:51:44] [Iter 150/2250] R0[149/150], Temp: 0.0001, Energy: -34.741806+0.009811j
[2025-09-17 16:51:44] RESTART #1 | Period: 300
[2025-09-17 16:51:56] [Iter 151/2250] R1[0/300], Temp: 1.0000, Energy: -34.831437-0.002949j
[2025-09-17 16:52:07] [Iter 152/2250] R1[1/300], Temp: 1.0000, Energy: -34.955534-0.037315j
[2025-09-17 16:52:19] [Iter 153/2250] R1[2/300], Temp: 0.9999, Energy: -35.036728+0.002859j
[2025-09-17 16:52:31] [Iter 154/2250] R1[3/300], Temp: 0.9998, Energy: -35.098512+0.007704j
[2025-09-17 16:52:42] [Iter 155/2250] R1[4/300], Temp: 0.9996, Energy: -35.216857-0.034600j
[2025-09-17 16:52:54] [Iter 156/2250] R1[5/300], Temp: 0.9993, Energy: -35.355809-0.020087j
[2025-09-17 16:53:05] [Iter 157/2250] R1[6/300], Temp: 0.9990, Energy: -35.400376-0.056761j
[2025-09-17 16:53:17] [Iter 158/2250] R1[7/300], Temp: 0.9987, Energy: -35.510226+0.063696j
[2025-09-17 16:53:28] [Iter 159/2250] R1[8/300], Temp: 0.9982, Energy: -35.571650+0.010907j
[2025-09-17 16:53:40] [Iter 160/2250] R1[9/300], Temp: 0.9978, Energy: -35.727281+0.047813j
[2025-09-17 16:53:51] [Iter 161/2250] R1[10/300], Temp: 0.9973, Energy: -35.795952-0.012923j
[2025-09-17 16:54:03] [Iter 162/2250] R1[11/300], Temp: 0.9967, Energy: -35.875021+0.009052j
[2025-09-17 16:54:15] [Iter 163/2250] R1[12/300], Temp: 0.9961, Energy: -35.958441-0.001724j
[2025-09-17 16:54:26] [Iter 164/2250] R1[13/300], Temp: 0.9954, Energy: -36.058001-0.009322j
[2025-09-17 16:54:38] [Iter 165/2250] R1[14/300], Temp: 0.9946, Energy: -36.118914+0.013883j
[2025-09-17 16:54:49] [Iter 166/2250] R1[15/300], Temp: 0.9938, Energy: -36.226355-0.001185j
[2025-09-17 16:55:01] [Iter 167/2250] R1[16/300], Temp: 0.9930, Energy: -36.307251+0.058851j
[2025-09-17 16:55:12] [Iter 168/2250] R1[17/300], Temp: 0.9921, Energy: -36.378653-0.009938j
[2025-09-17 16:55:24] [Iter 169/2250] R1[18/300], Temp: 0.9911, Energy: -36.476801+0.010381j
[2025-09-17 16:55:35] [Iter 170/2250] R1[19/300], Temp: 0.9901, Energy: -36.580579+0.019510j
[2025-09-17 16:55:47] [Iter 171/2250] R1[20/300], Temp: 0.9891, Energy: -36.636057+0.011921j
[2025-09-17 16:55:58] [Iter 172/2250] R1[21/300], Temp: 0.9880, Energy: -36.761573-0.002437j
[2025-09-17 16:56:10] [Iter 173/2250] R1[22/300], Temp: 0.9868, Energy: -36.825240-0.000161j
[2025-09-17 16:56:22] [Iter 174/2250] R1[23/300], Temp: 0.9856, Energy: -36.932695+0.001661j
[2025-09-17 16:56:33] [Iter 175/2250] R1[24/300], Temp: 0.9843, Energy: -36.945700+0.036691j
[2025-09-17 16:56:45] [Iter 176/2250] R1[25/300], Temp: 0.9830, Energy: -37.034715+0.005706j
[2025-09-17 16:56:56] [Iter 177/2250] R1[26/300], Temp: 0.9816, Energy: -37.138524+0.020222j
[2025-09-17 16:57:08] [Iter 178/2250] R1[27/300], Temp: 0.9801, Energy: -37.226631+0.005838j
[2025-09-17 16:57:19] [Iter 179/2250] R1[28/300], Temp: 0.9787, Energy: -37.281007+0.009806j
[2025-09-17 16:57:31] [Iter 180/2250] R1[29/300], Temp: 0.9771, Energy: -37.362944-0.006942j
[2025-09-17 16:57:42] [Iter 181/2250] R1[30/300], Temp: 0.9755, Energy: -37.444676-0.006757j
[2025-09-17 16:57:54] [Iter 182/2250] R1[31/300], Temp: 0.9739, Energy: -37.532983-0.013314j
[2025-09-17 16:58:06] [Iter 183/2250] R1[32/300], Temp: 0.9722, Energy: -37.579245-0.029537j
[2025-09-17 16:58:17] [Iter 184/2250] R1[33/300], Temp: 0.9704, Energy: -37.645425+0.000040j
[2025-09-17 16:58:29] [Iter 185/2250] R1[34/300], Temp: 0.9686, Energy: -37.703868+0.020595j
[2025-09-17 16:58:40] [Iter 186/2250] R1[35/300], Temp: 0.9668, Energy: -37.827834-0.049081j
[2025-09-17 16:58:52] [Iter 187/2250] R1[36/300], Temp: 0.9649, Energy: -37.881722-0.030942j
[2025-09-17 16:59:03] [Iter 188/2250] R1[37/300], Temp: 0.9629, Energy: -37.962780-0.004188j
[2025-09-17 16:59:15] [Iter 189/2250] R1[38/300], Temp: 0.9609, Energy: -37.990903+0.010764j
[2025-09-17 16:59:26] [Iter 190/2250] R1[39/300], Temp: 0.9589, Energy: -38.031006+0.008310j
[2025-09-17 16:59:38] [Iter 191/2250] R1[40/300], Temp: 0.9568, Energy: -38.069191-0.005566j
[2025-09-17 16:59:50] [Iter 192/2250] R1[41/300], Temp: 0.9546, Energy: -38.099150-0.008440j
[2025-09-17 17:00:01] [Iter 193/2250] R1[42/300], Temp: 0.9524, Energy: -38.228049-0.032386j
[2025-09-17 17:00:13] [Iter 194/2250] R1[43/300], Temp: 0.9502, Energy: -38.328029+0.004900j
[2025-09-17 17:00:24] [Iter 195/2250] R1[44/300], Temp: 0.9479, Energy: -38.324653-0.010290j
[2025-09-17 17:00:36] [Iter 196/2250] R1[45/300], Temp: 0.9455, Energy: -38.398138-0.015200j
[2025-09-17 17:00:47] [Iter 197/2250] R1[46/300], Temp: 0.9431, Energy: -38.426071+0.018786j
[2025-09-17 17:00:59] [Iter 198/2250] R1[47/300], Temp: 0.9407, Energy: -38.517009-0.012932j
[2025-09-17 17:01:10] [Iter 199/2250] R1[48/300], Temp: 0.9382, Energy: -38.584693+0.011414j
[2025-09-17 17:01:22] [Iter 200/2250] R1[49/300], Temp: 0.9356, Energy: -38.585327-0.009651j
[2025-09-17 17:01:34] [Iter 201/2250] R1[50/300], Temp: 0.9330, Energy: -38.646348-0.019035j
[2025-09-17 17:01:45] [Iter 202/2250] R1[51/300], Temp: 0.9304, Energy: -38.720873+0.018436j
[2025-09-17 17:01:57] [Iter 203/2250] R1[52/300], Temp: 0.9277, Energy: -38.769689-0.012360j
[2025-09-17 17:02:08] [Iter 204/2250] R1[53/300], Temp: 0.9249, Energy: -38.863848+0.037083j
[2025-09-17 17:02:20] [Iter 205/2250] R1[54/300], Temp: 0.9222, Energy: -38.839786+0.013113j
[2025-09-17 17:02:31] [Iter 206/2250] R1[55/300], Temp: 0.9193, Energy: -38.920922-0.006559j
[2025-09-17 17:02:43] [Iter 207/2250] R1[56/300], Temp: 0.9165, Energy: -38.970282-0.002739j
[2025-09-17 17:02:54] [Iter 208/2250] R1[57/300], Temp: 0.9135, Energy: -39.003740-0.025259j
[2025-09-17 17:03:06] [Iter 209/2250] R1[58/300], Temp: 0.9106, Energy: -39.096549+0.007858j
[2025-09-17 17:03:17] [Iter 210/2250] R1[59/300], Temp: 0.9076, Energy: -39.144554+0.004046j
[2025-09-17 17:03:29] [Iter 211/2250] R1[60/300], Temp: 0.9045, Energy: -39.145388+0.001573j
[2025-09-17 17:03:41] [Iter 212/2250] R1[61/300], Temp: 0.9014, Energy: -39.185137-0.001583j
[2025-09-17 17:03:52] [Iter 213/2250] R1[62/300], Temp: 0.8983, Energy: -39.205120+0.000528j
[2025-09-17 17:04:04] [Iter 214/2250] R1[63/300], Temp: 0.8951, Energy: -39.345659+0.012476j
[2025-09-17 17:04:15] [Iter 215/2250] R1[64/300], Temp: 0.8918, Energy: -39.344824-0.003608j
[2025-09-17 17:04:27] [Iter 216/2250] R1[65/300], Temp: 0.8886, Energy: -39.398907-0.039541j
[2025-09-17 17:04:38] [Iter 217/2250] R1[66/300], Temp: 0.8853, Energy: -39.418253-0.014897j
[2025-09-17 17:04:50] [Iter 218/2250] R1[67/300], Temp: 0.8819, Energy: -39.442835+0.013543j
[2025-09-17 17:05:01] [Iter 219/2250] R1[68/300], Temp: 0.8785, Energy: -39.512111-0.003000j
[2025-09-17 17:05:13] [Iter 220/2250] R1[69/300], Temp: 0.8751, Energy: -39.575173-0.004253j
[2025-09-17 17:05:24] [Iter 221/2250] R1[70/300], Temp: 0.8716, Energy: -39.589438+0.030155j
[2025-09-17 17:05:36] [Iter 222/2250] R1[71/300], Temp: 0.8680, Energy: -39.630631+0.007707j
[2025-09-17 17:05:48] [Iter 223/2250] R1[72/300], Temp: 0.8645, Energy: -39.641454+0.001054j
[2025-09-17 17:05:59] [Iter 224/2250] R1[73/300], Temp: 0.8609, Energy: -39.731809+0.008054j
[2025-09-17 17:06:11] [Iter 225/2250] R1[74/300], Temp: 0.8572, Energy: -39.765298-0.023863j
[2025-09-17 17:06:22] [Iter 226/2250] R1[75/300], Temp: 0.8536, Energy: -39.773882-0.010007j
[2025-09-17 17:06:34] [Iter 227/2250] R1[76/300], Temp: 0.8498, Energy: -39.840377+0.009082j
[2025-09-17 17:06:45] [Iter 228/2250] R1[77/300], Temp: 0.8461, Energy: -39.858306+0.024799j
[2025-09-17 17:06:57] [Iter 229/2250] R1[78/300], Temp: 0.8423, Energy: -39.880821+0.021391j
[2025-09-17 17:07:08] [Iter 230/2250] R1[79/300], Temp: 0.8384, Energy: -39.964472+0.000753j
[2025-09-17 17:07:20] [Iter 231/2250] R1[80/300], Temp: 0.8346, Energy: -39.948103-0.011055j
[2025-09-17 17:07:31] [Iter 232/2250] R1[81/300], Temp: 0.8307, Energy: -40.000943-0.020442j
[2025-09-17 17:07:43] [Iter 233/2250] R1[82/300], Temp: 0.8267, Energy: -40.026432-0.004456j
[2025-09-17 17:07:55] [Iter 234/2250] R1[83/300], Temp: 0.8227, Energy: -40.060900-0.032181j
[2025-09-17 17:08:06] [Iter 235/2250] R1[84/300], Temp: 0.8187, Energy: -40.068751+0.015768j
[2025-09-17 17:08:18] [Iter 236/2250] R1[85/300], Temp: 0.8147, Energy: -40.150124+0.006240j
[2025-09-17 17:08:29] [Iter 237/2250] R1[86/300], Temp: 0.8106, Energy: -40.178239-0.025539j
[2025-09-17 17:08:41] [Iter 238/2250] R1[87/300], Temp: 0.8065, Energy: -40.209664+0.005012j
[2025-09-17 17:08:52] [Iter 239/2250] R1[88/300], Temp: 0.8023, Energy: -40.187652+0.006909j
[2025-09-17 17:09:04] [Iter 240/2250] R1[89/300], Temp: 0.7981, Energy: -40.262458-0.026824j
[2025-09-17 17:09:15] [Iter 241/2250] R1[90/300], Temp: 0.7939, Energy: -40.270310+0.028649j
[2025-09-17 17:09:27] [Iter 242/2250] R1[91/300], Temp: 0.7896, Energy: -40.312513+0.012957j
[2025-09-17 17:09:38] [Iter 243/2250] R1[92/300], Temp: 0.7854, Energy: -40.343310-0.024421j
[2025-09-17 17:09:50] [Iter 244/2250] R1[93/300], Temp: 0.7810, Energy: -40.369909-0.007793j
[2025-09-17 17:10:02] [Iter 245/2250] R1[94/300], Temp: 0.7767, Energy: -40.436643-0.004743j
[2025-09-17 17:10:13] [Iter 246/2250] R1[95/300], Temp: 0.7723, Energy: -40.434446-0.031009j
[2025-09-17 17:10:25] [Iter 247/2250] R1[96/300], Temp: 0.7679, Energy: -40.464764+0.023179j
[2025-09-17 17:10:36] [Iter 248/2250] R1[97/300], Temp: 0.7635, Energy: -40.491626-0.008341j
[2025-09-17 17:10:48] [Iter 249/2250] R1[98/300], Temp: 0.7590, Energy: -40.533046-0.005407j
[2025-09-17 17:10:59] [Iter 250/2250] R1[99/300], Temp: 0.7545, Energy: -40.556603-0.001447j
[2025-09-17 17:10:59] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-09-17 17:11:11] [Iter 251/2250] R1[100/300], Temp: 0.7500, Energy: -40.587258-0.006886j
[2025-09-17 17:11:22] [Iter 252/2250] R1[101/300], Temp: 0.7455, Energy: -40.578542-0.003188j
[2025-09-17 17:11:34] [Iter 253/2250] R1[102/300], Temp: 0.7409, Energy: -40.629285+0.008666j
[2025-09-17 17:11:46] [Iter 254/2250] R1[103/300], Temp: 0.7363, Energy: -40.676315+0.025942j
[2025-09-17 17:11:57] [Iter 255/2250] R1[104/300], Temp: 0.7316, Energy: -40.691342-0.014330j
[2025-09-17 17:12:09] [Iter 256/2250] R1[105/300], Temp: 0.7270, Energy: -40.689474+0.014056j
[2025-09-17 17:12:20] [Iter 257/2250] R1[106/300], Temp: 0.7223, Energy: -40.735584+0.019786j
[2025-09-17 17:12:32] [Iter 258/2250] R1[107/300], Temp: 0.7176, Energy: -40.740218+0.025381j
[2025-09-17 17:12:43] [Iter 259/2250] R1[108/300], Temp: 0.7129, Energy: -40.808213-0.025934j
[2025-09-17 17:12:55] [Iter 260/2250] R1[109/300], Temp: 0.7081, Energy: -40.837661+0.016126j
[2025-09-17 17:13:06] [Iter 261/2250] R1[110/300], Temp: 0.7034, Energy: -40.843249+0.036443j
[2025-09-17 17:13:18] [Iter 262/2250] R1[111/300], Temp: 0.6986, Energy: -40.850019-0.001043j
[2025-09-17 17:13:29] [Iter 263/2250] R1[112/300], Temp: 0.6938, Energy: -40.905257-0.017259j
[2025-09-17 17:13:41] [Iter 264/2250] R1[113/300], Temp: 0.6889, Energy: -40.937953+0.000663j
[2025-09-17 17:13:53] [Iter 265/2250] R1[114/300], Temp: 0.6841, Energy: -40.928951+0.004790j
[2025-09-17 17:14:04] [Iter 266/2250] R1[115/300], Temp: 0.6792, Energy: -41.005893-0.021807j
[2025-09-17 17:14:16] [Iter 267/2250] R1[116/300], Temp: 0.6743, Energy: -41.007844+0.002553j
[2025-09-17 17:14:27] [Iter 268/2250] R1[117/300], Temp: 0.6694, Energy: -41.017876+0.014276j
[2025-09-17 17:14:39] [Iter 269/2250] R1[118/300], Temp: 0.6644, Energy: -41.018071-0.009322j
[2025-09-17 17:14:50] [Iter 270/2250] R1[119/300], Temp: 0.6595, Energy: -41.048720-0.020961j
[2025-09-17 17:15:02] [Iter 271/2250] R1[120/300], Temp: 0.6545, Energy: -41.104808+0.015495j
[2025-09-17 17:15:13] [Iter 272/2250] R1[121/300], Temp: 0.6495, Energy: -41.177224-0.001642j
[2025-09-17 17:15:25] [Iter 273/2250] R1[122/300], Temp: 0.6445, Energy: -41.162599+0.012102j
[2025-09-17 17:15:36] [Iter 274/2250] R1[123/300], Temp: 0.6395, Energy: -41.193108+0.006921j
[2025-09-17 17:15:48] [Iter 275/2250] R1[124/300], Temp: 0.6345, Energy: -41.234205+0.000492j
[2025-09-17 17:16:00] [Iter 276/2250] R1[125/300], Temp: 0.6294, Energy: -41.252479+0.003146j
[2025-09-17 17:16:11] [Iter 277/2250] R1[126/300], Temp: 0.6243, Energy: -41.258982-0.004411j
[2025-09-17 17:16:23] [Iter 278/2250] R1[127/300], Temp: 0.6193, Energy: -41.281510+0.011377j
[2025-09-17 17:16:34] [Iter 279/2250] R1[128/300], Temp: 0.6142, Energy: -41.335119-0.018654j
[2025-09-17 17:16:46] [Iter 280/2250] R1[129/300], Temp: 0.6091, Energy: -41.334778+0.016912j
[2025-09-17 17:16:57] [Iter 281/2250] R1[130/300], Temp: 0.6040, Energy: -41.351338-0.002258j
[2025-09-17 17:17:09] [Iter 282/2250] R1[131/300], Temp: 0.5988, Energy: -41.435605+0.014543j
[2025-09-17 17:17:20] [Iter 283/2250] R1[132/300], Temp: 0.5937, Energy: -41.468179-0.007265j
[2025-09-17 17:17:32] [Iter 284/2250] R1[133/300], Temp: 0.5885, Energy: -41.493803-0.000832j
[2025-09-17 17:17:44] [Iter 285/2250] R1[134/300], Temp: 0.5834, Energy: -41.503342-0.030173j
[2025-09-17 17:17:55] [Iter 286/2250] R1[135/300], Temp: 0.5782, Energy: -41.575342+0.029896j
[2025-09-17 17:18:07] [Iter 287/2250] R1[136/300], Temp: 0.5730, Energy: -41.607833+0.006811j
[2025-09-17 17:18:18] [Iter 288/2250] R1[137/300], Temp: 0.5679, Energy: -41.627765+0.014754j
[2025-09-17 17:18:30] [Iter 289/2250] R1[138/300], Temp: 0.5627, Energy: -41.679443+0.038264j
[2025-09-17 17:18:41] [Iter 290/2250] R1[139/300], Temp: 0.5575, Energy: -41.704237+0.023557j
[2025-09-17 17:18:53] [Iter 291/2250] R1[140/300], Temp: 0.5523, Energy: -41.752619+0.001554j
[2025-09-17 17:19:05] [Iter 292/2250] R1[141/300], Temp: 0.5471, Energy: -41.793749+0.002044j
[2025-09-17 17:19:16] [Iter 293/2250] R1[142/300], Temp: 0.5418, Energy: -41.820564+0.004391j
[2025-09-17 17:19:28] [Iter 294/2250] R1[143/300], Temp: 0.5366, Energy: -41.879237+0.022768j
[2025-09-17 17:19:40] [Iter 295/2250] R1[144/300], Temp: 0.5314, Energy: -41.915780+0.018113j
[2025-09-17 17:19:51] [Iter 296/2250] R1[145/300], Temp: 0.5262, Energy: -41.978172-0.005673j
[2025-09-17 17:20:03] [Iter 297/2250] R1[146/300], Temp: 0.5209, Energy: -42.008238-0.013648j
[2025-09-17 17:20:14] [Iter 298/2250] R1[147/300], Temp: 0.5157, Energy: -42.038641+0.016682j
[2025-09-17 17:20:26] [Iter 299/2250] R1[148/300], Temp: 0.5105, Energy: -42.081732-0.019069j
[2025-09-17 17:20:37] [Iter 300/2250] R1[149/300], Temp: 0.5052, Energy: -42.170713+0.005132j
[2025-09-17 17:20:49] [Iter 301/2250] R1[150/300], Temp: 0.5000, Energy: -42.252447+0.002100j
[2025-09-17 17:21:00] [Iter 302/2250] R1[151/300], Temp: 0.4948, Energy: -42.300591-0.002773j
[2025-09-17 17:21:12] [Iter 303/2250] R1[152/300], Temp: 0.4895, Energy: -42.352338-0.006898j
[2025-09-17 17:21:24] [Iter 304/2250] R1[153/300], Temp: 0.4843, Energy: -42.370399-0.008600j
[2025-09-17 17:21:35] [Iter 305/2250] R1[154/300], Temp: 0.4791, Energy: -42.479837-0.007311j
[2025-09-17 17:21:47] [Iter 306/2250] R1[155/300], Temp: 0.4738, Energy: -42.525036+0.007940j
[2025-09-17 17:21:58] [Iter 307/2250] R1[156/300], Temp: 0.4686, Energy: -42.561504-0.033181j
[2025-09-17 17:22:10] [Iter 308/2250] R1[157/300], Temp: 0.4634, Energy: -42.661376-0.014166j
[2025-09-17 17:22:21] [Iter 309/2250] R1[158/300], Temp: 0.4582, Energy: -42.751269-0.007102j
[2025-09-17 17:22:33] [Iter 310/2250] R1[159/300], Temp: 0.4529, Energy: -42.788577-0.003069j
[2025-09-17 17:22:44] [Iter 311/2250] R1[160/300], Temp: 0.4477, Energy: -42.866498+0.000359j
[2025-09-17 17:22:56] [Iter 312/2250] R1[161/300], Temp: 0.4425, Energy: -42.929568+0.001720j
[2025-09-17 17:23:07] [Iter 313/2250] R1[162/300], Temp: 0.4373, Energy: -43.042874-0.003680j
[2025-09-17 17:23:19] [Iter 314/2250] R1[163/300], Temp: 0.4321, Energy: -43.099764-0.000741j
[2025-09-17 17:23:31] [Iter 315/2250] R1[164/300], Temp: 0.4270, Energy: -43.151261-0.019344j
[2025-09-17 17:23:42] [Iter 316/2250] R1[165/300], Temp: 0.4218, Energy: -43.258477+0.006791j
[2025-09-17 17:23:54] [Iter 317/2250] R1[166/300], Temp: 0.4166, Energy: -43.324854+0.000465j
[2025-09-17 17:24:05] [Iter 318/2250] R1[167/300], Temp: 0.4115, Energy: -43.350928-0.009607j
[2025-09-17 17:24:17] [Iter 319/2250] R1[168/300], Temp: 0.4063, Energy: -43.409216+0.020364j
[2025-09-17 17:24:28] [Iter 320/2250] R1[169/300], Temp: 0.4012, Energy: -43.483392+0.001896j
[2025-09-17 17:24:40] [Iter 321/2250] R1[170/300], Temp: 0.3960, Energy: -43.514169+0.014206j
[2025-09-17 17:24:51] [Iter 322/2250] R1[171/300], Temp: 0.3909, Energy: -43.580130+0.003846j
[2025-09-17 17:25:03] [Iter 323/2250] R1[172/300], Temp: 0.3858, Energy: -43.627177+0.008135j
[2025-09-17 17:25:14] [Iter 324/2250] R1[173/300], Temp: 0.3807, Energy: -43.717246+0.015925j
[2025-09-17 17:25:26] [Iter 325/2250] R1[174/300], Temp: 0.3757, Energy: -43.747485+0.014044j
[2025-09-17 17:25:38] [Iter 326/2250] R1[175/300], Temp: 0.3706, Energy: -43.808400+0.007235j
[2025-09-17 17:25:49] [Iter 327/2250] R1[176/300], Temp: 0.3655, Energy: -43.833337-0.001603j
[2025-09-17 17:26:01] [Iter 328/2250] R1[177/300], Temp: 0.3605, Energy: -43.891371+0.002566j
[2025-09-17 17:26:12] [Iter 329/2250] R1[178/300], Temp: 0.3555, Energy: -43.907645-0.008298j
[2025-09-17 17:26:24] [Iter 330/2250] R1[179/300], Temp: 0.3505, Energy: -43.943836+0.002044j
[2025-09-17 17:26:35] [Iter 331/2250] R1[180/300], Temp: 0.3455, Energy: -43.975734+0.000671j
[2025-09-17 17:26:47] [Iter 332/2250] R1[181/300], Temp: 0.3405, Energy: -44.021741-0.002375j
[2025-09-17 17:26:58] [Iter 333/2250] R1[182/300], Temp: 0.3356, Energy: -44.053137+0.008814j
[2025-09-17 17:27:10] [Iter 334/2250] R1[183/300], Temp: 0.3306, Energy: -44.098276+0.002809j
[2025-09-17 17:27:21] [Iter 335/2250] R1[184/300], Temp: 0.3257, Energy: -44.094613+0.002274j
[2025-09-17 17:27:33] [Iter 336/2250] R1[185/300], Temp: 0.3208, Energy: -44.126421-0.016092j
[2025-09-17 17:27:44] [Iter 337/2250] R1[186/300], Temp: 0.3159, Energy: -44.131938+0.001220j
[2025-09-17 17:27:56] [Iter 338/2250] R1[187/300], Temp: 0.3111, Energy: -44.170926-0.001029j
[2025-09-17 17:28:08] [Iter 339/2250] R1[188/300], Temp: 0.3062, Energy: -44.174973+0.003675j
[2025-09-17 17:28:19] [Iter 340/2250] R1[189/300], Temp: 0.3014, Energy: -44.193003+0.005391j
[2025-09-17 17:28:31] [Iter 341/2250] R1[190/300], Temp: 0.2966, Energy: -44.230618-0.000120j
[2025-09-17 17:28:42] [Iter 342/2250] R1[191/300], Temp: 0.2919, Energy: -44.248806-0.004958j
[2025-09-17 17:28:54] [Iter 343/2250] R1[192/300], Temp: 0.2871, Energy: -44.271847-0.010627j
[2025-09-17 17:29:05] [Iter 344/2250] R1[193/300], Temp: 0.2824, Energy: -44.273816+0.006215j
[2025-09-17 17:29:17] [Iter 345/2250] R1[194/300], Temp: 0.2777, Energy: -44.273206-0.004494j
[2025-09-17 17:29:28] [Iter 346/2250] R1[195/300], Temp: 0.2730, Energy: -44.317527-0.003505j
[2025-09-17 17:29:40] [Iter 347/2250] R1[196/300], Temp: 0.2684, Energy: -44.320188-0.005198j
[2025-09-17 17:29:51] [Iter 348/2250] R1[197/300], Temp: 0.2637, Energy: -44.322234-0.007812j
[2025-09-17 17:30:03] [Iter 349/2250] R1[198/300], Temp: 0.2591, Energy: -44.359188-0.005269j
[2025-09-17 17:30:15] [Iter 350/2250] R1[199/300], Temp: 0.2545, Energy: -44.369482+0.004594j
[2025-09-17 17:30:26] [Iter 351/2250] R1[200/300], Temp: 0.2500, Energy: -44.370712-0.001893j
[2025-09-17 17:30:38] [Iter 352/2250] R1[201/300], Temp: 0.2455, Energy: -44.366848+0.002466j
[2025-09-17 17:30:49] [Iter 353/2250] R1[202/300], Temp: 0.2410, Energy: -44.398220+0.004455j
[2025-09-17 17:31:01] [Iter 354/2250] R1[203/300], Temp: 0.2365, Energy: -44.407979-0.004618j
[2025-09-17 17:31:12] [Iter 355/2250] R1[204/300], Temp: 0.2321, Energy: -44.394780+0.001282j
[2025-09-17 17:31:24] [Iter 356/2250] R1[205/300], Temp: 0.2277, Energy: -44.400830+0.000385j
[2025-09-17 17:31:35] [Iter 357/2250] R1[206/300], Temp: 0.2233, Energy: -44.401149-0.002540j
[2025-09-17 17:31:47] [Iter 358/2250] R1[207/300], Temp: 0.2190, Energy: -44.409524+0.006004j
[2025-09-17 17:31:58] [Iter 359/2250] R1[208/300], Temp: 0.2146, Energy: -44.437727+0.001748j
[2025-09-17 17:32:10] [Iter 360/2250] R1[209/300], Temp: 0.2104, Energy: -44.452284-0.004463j
[2025-09-17 17:32:21] [Iter 361/2250] R1[210/300], Temp: 0.2061, Energy: -44.448202-0.000912j
[2025-09-17 17:32:33] [Iter 362/2250] R1[211/300], Temp: 0.2019, Energy: -44.439462+0.001897j
[2025-09-17 17:32:45] [Iter 363/2250] R1[212/300], Temp: 0.1977, Energy: -44.459607-0.013906j
[2025-09-17 17:32:56] [Iter 364/2250] R1[213/300], Temp: 0.1935, Energy: -44.450512+0.003687j
[2025-09-17 17:33:08] [Iter 365/2250] R1[214/300], Temp: 0.1894, Energy: -44.484024+0.004351j
[2025-09-17 17:33:19] [Iter 366/2250] R1[215/300], Temp: 0.1853, Energy: -44.492226+0.000357j
[2025-09-17 17:33:31] [Iter 367/2250] R1[216/300], Temp: 0.1813, Energy: -44.482044-0.005337j
[2025-09-17 17:33:42] [Iter 368/2250] R1[217/300], Temp: 0.1773, Energy: -44.475366-0.002055j
[2025-09-17 17:33:54] [Iter 369/2250] R1[218/300], Temp: 0.1733, Energy: -44.476859+0.002266j
[2025-09-17 17:34:05] [Iter 370/2250] R1[219/300], Temp: 0.1693, Energy: -44.482039+0.002421j
[2025-09-17 17:34:17] [Iter 371/2250] R1[220/300], Temp: 0.1654, Energy: -44.506362-0.005407j
[2025-09-17 17:34:28] [Iter 372/2250] R1[221/300], Temp: 0.1616, Energy: -44.518880-0.006587j
[2025-09-17 17:34:40] [Iter 373/2250] R1[222/300], Temp: 0.1577, Energy: -44.526664-0.003541j
[2025-09-17 17:34:52] [Iter 374/2250] R1[223/300], Temp: 0.1539, Energy: -44.505285+0.000628j
[2025-09-17 17:35:03] [Iter 375/2250] R1[224/300], Temp: 0.1502, Energy: -44.520822-0.005694j
[2025-09-17 17:35:15] [Iter 376/2250] R1[225/300], Temp: 0.1464, Energy: -44.509264-0.010049j
[2025-09-17 17:35:26] [Iter 377/2250] R1[226/300], Temp: 0.1428, Energy: -44.547717+0.003005j
[2025-09-17 17:35:38] [Iter 378/2250] R1[227/300], Temp: 0.1391, Energy: -44.537487+0.000152j
[2025-09-17 17:35:49] [Iter 379/2250] R1[228/300], Temp: 0.1355, Energy: -44.536271-0.007825j
[2025-09-17 17:36:01] [Iter 380/2250] R1[229/300], Temp: 0.1320, Energy: -44.537631-0.003698j
[2025-09-17 17:36:12] [Iter 381/2250] R1[230/300], Temp: 0.1284, Energy: -44.542496+0.002591j
[2025-09-17 17:36:24] [Iter 382/2250] R1[231/300], Temp: 0.1249, Energy: -44.545547+0.001551j
[2025-09-17 17:36:36] [Iter 383/2250] R1[232/300], Temp: 0.1215, Energy: -44.540101+0.014592j
[2025-09-17 17:36:47] [Iter 384/2250] R1[233/300], Temp: 0.1181, Energy: -44.555545+0.006691j
[2025-09-17 17:36:59] [Iter 385/2250] R1[234/300], Temp: 0.1147, Energy: -44.575492+0.002912j
[2025-09-17 17:37:10] [Iter 386/2250] R1[235/300], Temp: 0.1114, Energy: -44.560056+0.001590j
[2025-09-17 17:37:22] [Iter 387/2250] R1[236/300], Temp: 0.1082, Energy: -44.571163+0.002451j
[2025-09-17 17:37:33] [Iter 388/2250] R1[237/300], Temp: 0.1049, Energy: -44.566269-0.004405j
[2025-09-17 17:37:45] [Iter 389/2250] R1[238/300], Temp: 0.1017, Energy: -44.566173-0.002604j
[2025-09-17 17:37:56] [Iter 390/2250] R1[239/300], Temp: 0.0986, Energy: -44.578014-0.002673j
[2025-09-17 17:38:08] [Iter 391/2250] R1[240/300], Temp: 0.0955, Energy: -44.594211-0.000064j
[2025-09-17 17:38:19] [Iter 392/2250] R1[241/300], Temp: 0.0924, Energy: -44.600576-0.000558j
[2025-09-17 17:38:31] [Iter 393/2250] R1[242/300], Temp: 0.0894, Energy: -44.593875+0.000059j
[2025-09-17 17:38:43] [Iter 394/2250] R1[243/300], Temp: 0.0865, Energy: -44.581156+0.003312j
[2025-09-17 17:38:54] [Iter 395/2250] R1[244/300], Temp: 0.0835, Energy: -44.575192-0.001310j
[2025-09-17 17:39:06] [Iter 396/2250] R1[245/300], Temp: 0.0807, Energy: -44.572272+0.003673j
[2025-09-17 17:39:17] [Iter 397/2250] R1[246/300], Temp: 0.0778, Energy: -44.615230+0.002111j
[2025-09-17 17:39:29] [Iter 398/2250] R1[247/300], Temp: 0.0751, Energy: -44.589691-0.009554j
[2025-09-17 17:39:40] [Iter 399/2250] R1[248/300], Temp: 0.0723, Energy: -44.569910-0.003924j
[2025-09-17 17:39:52] [Iter 400/2250] R1[249/300], Temp: 0.0696, Energy: -44.604296+0.005858j
[2025-09-17 17:40:03] [Iter 401/2250] R1[250/300], Temp: 0.0670, Energy: -44.600736+0.008183j
[2025-09-17 17:40:15] [Iter 402/2250] R1[251/300], Temp: 0.0644, Energy: -44.598982+0.002898j
[2025-09-17 17:40:26] [Iter 403/2250] R1[252/300], Temp: 0.0618, Energy: -44.607071+0.002779j
[2025-09-17 17:40:38] [Iter 404/2250] R1[253/300], Temp: 0.0593, Energy: -44.596151-0.004030j
[2025-09-17 17:40:49] [Iter 405/2250] R1[254/300], Temp: 0.0569, Energy: -44.611983-0.005032j
[2025-09-17 17:41:01] [Iter 406/2250] R1[255/300], Temp: 0.0545, Energy: -44.624159+0.001338j
[2025-09-17 17:41:13] [Iter 407/2250] R1[256/300], Temp: 0.0521, Energy: -44.626475+0.004479j
[2025-09-17 17:41:24] [Iter 408/2250] R1[257/300], Temp: 0.0498, Energy: -44.628505+0.005193j
[2025-09-17 17:41:36] [Iter 409/2250] R1[258/300], Temp: 0.0476, Energy: -44.639477+0.003685j
[2025-09-17 17:41:47] [Iter 410/2250] R1[259/300], Temp: 0.0454, Energy: -44.624650-0.000663j
[2025-09-17 17:41:59] [Iter 411/2250] R1[260/300], Temp: 0.0432, Energy: -44.619756-0.002514j
[2025-09-17 17:42:10] [Iter 412/2250] R1[261/300], Temp: 0.0411, Energy: -44.626475-0.003289j
[2025-09-17 17:42:22] [Iter 413/2250] R1[262/300], Temp: 0.0391, Energy: -44.625073+0.000689j
[2025-09-17 17:42:33] [Iter 414/2250] R1[263/300], Temp: 0.0371, Energy: -44.617005+0.000924j
[2025-09-17 17:42:45] [Iter 415/2250] R1[264/300], Temp: 0.0351, Energy: -44.636528-0.006050j
[2025-09-17 17:42:56] [Iter 416/2250] R1[265/300], Temp: 0.0332, Energy: -44.642203-0.001544j
[2025-09-17 17:43:08] [Iter 417/2250] R1[266/300], Temp: 0.0314, Energy: -44.646298+0.001160j
[2025-09-17 17:43:20] [Iter 418/2250] R1[267/300], Temp: 0.0296, Energy: -44.657120+0.007585j
[2025-09-17 17:43:31] [Iter 419/2250] R1[268/300], Temp: 0.0278, Energy: -44.646180-0.004210j
[2025-09-17 17:43:43] [Iter 420/2250] R1[269/300], Temp: 0.0261, Energy: -44.639219-0.009923j
[2025-09-17 17:43:54] [Iter 421/2250] R1[270/300], Temp: 0.0245, Energy: -44.644789+0.008453j
[2025-09-17 17:44:06] [Iter 422/2250] R1[271/300], Temp: 0.0229, Energy: -44.646266+0.003841j
[2025-09-17 17:44:17] [Iter 423/2250] R1[272/300], Temp: 0.0213, Energy: -44.649446+0.009276j
[2025-09-17 17:44:29] [Iter 424/2250] R1[273/300], Temp: 0.0199, Energy: -44.641144-0.002706j
[2025-09-17 17:44:40] [Iter 425/2250] R1[274/300], Temp: 0.0184, Energy: -44.662230+0.001110j
[2025-09-17 17:44:52] [Iter 426/2250] R1[275/300], Temp: 0.0170, Energy: -44.658591+0.002115j
[2025-09-17 17:45:03] [Iter 427/2250] R1[276/300], Temp: 0.0157, Energy: -44.655577+0.000784j
[2025-09-17 17:45:15] [Iter 428/2250] R1[277/300], Temp: 0.0144, Energy: -44.667674+0.002338j
[2025-09-17 17:45:26] [Iter 429/2250] R1[278/300], Temp: 0.0132, Energy: -44.641184+0.008174j
[2025-09-17 17:45:38] [Iter 430/2250] R1[279/300], Temp: 0.0120, Energy: -44.663811+0.000386j
[2025-09-17 17:45:50] [Iter 431/2250] R1[280/300], Temp: 0.0109, Energy: -44.658490+0.001260j
[2025-09-17 17:46:01] [Iter 432/2250] R1[281/300], Temp: 0.0099, Energy: -44.656893+0.001631j
[2025-09-17 17:46:13] [Iter 433/2250] R1[282/300], Temp: 0.0089, Energy: -44.664821+0.001957j
[2025-09-17 17:46:24] [Iter 434/2250] R1[283/300], Temp: 0.0079, Energy: -44.651375+0.002733j
[2025-09-17 17:46:36] [Iter 435/2250] R1[284/300], Temp: 0.0070, Energy: -44.654907-0.004406j
[2025-09-17 17:46:47] [Iter 436/2250] R1[285/300], Temp: 0.0062, Energy: -44.664104+0.000931j
[2025-09-17 17:46:59] [Iter 437/2250] R1[286/300], Temp: 0.0054, Energy: -44.667904-0.001562j
[2025-09-17 17:47:10] [Iter 438/2250] R1[287/300], Temp: 0.0046, Energy: -44.663160-0.007683j
[2025-09-17 17:47:22] [Iter 439/2250] R1[288/300], Temp: 0.0039, Energy: -44.664783+0.004791j
[2025-09-17 17:47:33] [Iter 440/2250] R1[289/300], Temp: 0.0033, Energy: -44.651656+0.001232j
[2025-09-17 17:47:45] [Iter 441/2250] R1[290/300], Temp: 0.0027, Energy: -44.670076-0.002953j
[2025-09-17 17:47:56] [Iter 442/2250] R1[291/300], Temp: 0.0022, Energy: -44.672835+0.004617j
[2025-09-17 17:48:08] [Iter 443/2250] R1[292/300], Temp: 0.0018, Energy: -44.679317-0.005471j
[2025-09-17 17:48:20] [Iter 444/2250] R1[293/300], Temp: 0.0013, Energy: -44.674094+0.003662j
[2025-09-17 17:48:31] [Iter 445/2250] R1[294/300], Temp: 0.0010, Energy: -44.681861+0.003191j
[2025-09-17 17:48:43] [Iter 446/2250] R1[295/300], Temp: 0.0007, Energy: -44.655354+0.008852j
[2025-09-17 17:48:54] [Iter 447/2250] R1[296/300], Temp: 0.0004, Energy: -44.655361-0.002321j
[2025-09-17 17:49:06] [Iter 448/2250] R1[297/300], Temp: 0.0002, Energy: -44.687431-0.001609j
[2025-09-17 17:49:17] [Iter 449/2250] R1[298/300], Temp: 0.0001, Energy: -44.662947-0.001560j
[2025-09-17 17:49:29] [Iter 450/2250] R1[299/300], Temp: 0.0000, Energy: -44.672660+0.001756j
[2025-09-17 17:49:29] RESTART #2 | Period: 600
[2025-09-17 17:49:40] [Iter 451/2250] R2[0/600], Temp: 1.0000, Energy: -44.693727+0.001642j
[2025-09-17 17:49:52] [Iter 452/2250] R2[1/600], Temp: 1.0000, Energy: -44.668232-0.001316j
[2025-09-17 17:50:03] [Iter 453/2250] R2[2/600], Temp: 1.0000, Energy: -44.696504-0.003141j
[2025-09-17 17:50:15] [Iter 454/2250] R2[3/600], Temp: 0.9999, Energy: -44.660220+0.006705j
[2025-09-17 17:50:26] [Iter 455/2250] R2[4/600], Temp: 0.9999, Energy: -44.694123+0.002924j
[2025-09-17 17:50:38] [Iter 456/2250] R2[5/600], Temp: 0.9998, Energy: -44.676526+0.003349j
[2025-09-17 17:50:50] [Iter 457/2250] R2[6/600], Temp: 0.9998, Energy: -44.666434+0.001239j
[2025-09-17 17:51:01] [Iter 458/2250] R2[7/600], Temp: 0.9997, Energy: -44.703438+0.004817j
[2025-09-17 17:51:13] [Iter 459/2250] R2[8/600], Temp: 0.9996, Energy: -44.702142-0.001105j
[2025-09-17 17:51:24] [Iter 460/2250] R2[9/600], Temp: 0.9994, Energy: -44.691568+0.002958j
[2025-09-17 17:51:36] [Iter 461/2250] R2[10/600], Temp: 0.9993, Energy: -44.695614+0.005836j
[2025-09-17 17:51:47] [Iter 462/2250] R2[11/600], Temp: 0.9992, Energy: -44.675063+0.001944j
[2025-09-17 17:51:59] [Iter 463/2250] R2[12/600], Temp: 0.9990, Energy: -44.685195+0.000722j
[2025-09-17 17:52:10] [Iter 464/2250] R2[13/600], Temp: 0.9988, Energy: -44.715761-0.005073j
[2025-09-17 17:52:22] [Iter 465/2250] R2[14/600], Temp: 0.9987, Energy: -44.680413-0.011119j
[2025-09-17 17:52:33] [Iter 466/2250] R2[15/600], Temp: 0.9985, Energy: -44.686618-0.001571j
[2025-09-17 17:52:45] [Iter 467/2250] R2[16/600], Temp: 0.9982, Energy: -44.711531+0.000276j
[2025-09-17 17:52:57] [Iter 468/2250] R2[17/600], Temp: 0.9980, Energy: -44.698922+0.002756j
[2025-09-17 17:53:08] [Iter 469/2250] R2[18/600], Temp: 0.9978, Energy: -44.681157+0.002275j
[2025-09-17 17:53:20] [Iter 470/2250] R2[19/600], Temp: 0.9975, Energy: -44.688737+0.007338j
[2025-09-17 17:53:31] [Iter 471/2250] R2[20/600], Temp: 0.9973, Energy: -44.695257-0.000316j
[2025-09-17 17:53:43] [Iter 472/2250] R2[21/600], Temp: 0.9970, Energy: -44.708567+0.000832j
[2025-09-17 17:53:54] [Iter 473/2250] R2[22/600], Temp: 0.9967, Energy: -44.713920+0.006009j
[2025-09-17 17:54:06] [Iter 474/2250] R2[23/600], Temp: 0.9964, Energy: -44.708618-0.001234j
[2025-09-17 17:54:17] [Iter 475/2250] R2[24/600], Temp: 0.9961, Energy: -44.691080+0.006654j
[2025-09-17 17:54:29] [Iter 476/2250] R2[25/600], Temp: 0.9957, Energy: -44.699324+0.000484j
[2025-09-17 17:54:40] [Iter 477/2250] R2[26/600], Temp: 0.9954, Energy: -44.707633-0.003204j
[2025-09-17 17:54:52] [Iter 478/2250] R2[27/600], Temp: 0.9950, Energy: -44.704129-0.005354j
[2025-09-17 17:55:03] [Iter 479/2250] R2[28/600], Temp: 0.9946, Energy: -44.712945+0.001721j
[2025-09-17 17:55:15] [Iter 480/2250] R2[29/600], Temp: 0.9942, Energy: -44.693033+0.002721j
[2025-09-17 17:55:27] [Iter 481/2250] R2[30/600], Temp: 0.9938, Energy: -44.715323-0.000073j
[2025-09-17 17:55:38] [Iter 482/2250] R2[31/600], Temp: 0.9934, Energy: -44.704078+0.005863j
[2025-09-17 17:55:50] [Iter 483/2250] R2[32/600], Temp: 0.9930, Energy: -44.723403+0.001229j
[2025-09-17 17:56:01] [Iter 484/2250] R2[33/600], Temp: 0.9926, Energy: -44.696532-0.001334j
[2025-09-17 17:56:13] [Iter 485/2250] R2[34/600], Temp: 0.9921, Energy: -44.724840-0.000490j
[2025-09-17 17:56:24] [Iter 486/2250] R2[35/600], Temp: 0.9916, Energy: -44.714033-0.000392j
[2025-09-17 17:56:36] [Iter 487/2250] R2[36/600], Temp: 0.9911, Energy: -44.701923+0.003656j
[2025-09-17 17:56:47] [Iter 488/2250] R2[37/600], Temp: 0.9906, Energy: -44.699916-0.003455j
[2025-09-17 17:56:59] [Iter 489/2250] R2[38/600], Temp: 0.9901, Energy: -44.714053-0.004834j
[2025-09-17 17:57:10] [Iter 490/2250] R2[39/600], Temp: 0.9896, Energy: -44.701004-0.001177j
[2025-09-17 17:57:22] [Iter 491/2250] R2[40/600], Temp: 0.9891, Energy: -44.699806-0.003882j
[2025-09-17 17:57:33] [Iter 492/2250] R2[41/600], Temp: 0.9885, Energy: -44.710450-0.003662j
[2025-09-17 17:57:45] [Iter 493/2250] R2[42/600], Temp: 0.9880, Energy: -44.713978+0.000919j
[2025-09-17 17:57:57] [Iter 494/2250] R2[43/600], Temp: 0.9874, Energy: -44.705106+0.001175j
[2025-09-17 17:58:08] [Iter 495/2250] R2[44/600], Temp: 0.9868, Energy: -44.703240-0.001076j
[2025-09-17 17:58:20] [Iter 496/2250] R2[45/600], Temp: 0.9862, Energy: -44.700267-0.008596j
[2025-09-17 17:58:31] [Iter 497/2250] R2[46/600], Temp: 0.9856, Energy: -44.722888-0.001065j
[2025-09-17 17:58:43] [Iter 498/2250] R2[47/600], Temp: 0.9849, Energy: -44.714219-0.001961j
[2025-09-17 17:58:54] [Iter 499/2250] R2[48/600], Temp: 0.9843, Energy: -44.727354+0.003555j
[2025-09-17 17:59:06] [Iter 500/2250] R2[49/600], Temp: 0.9836, Energy: -44.723759-0.003082j
[2025-09-17 17:59:06] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-09-17 17:59:17] [Iter 501/2250] R2[50/600], Temp: 0.9830, Energy: -44.737667-0.003817j
[2025-09-17 17:59:29] [Iter 502/2250] R2[51/600], Temp: 0.9823, Energy: -44.694361-0.000022j
[2025-09-17 17:59:41] [Iter 503/2250] R2[52/600], Temp: 0.9816, Energy: -44.724910+0.000637j
[2025-09-17 17:59:52] [Iter 504/2250] R2[53/600], Temp: 0.9809, Energy: -44.719458-0.002016j
[2025-09-17 18:00:04] [Iter 505/2250] R2[54/600], Temp: 0.9801, Energy: -44.703468-0.003057j
[2025-09-17 18:00:15] [Iter 506/2250] R2[55/600], Temp: 0.9794, Energy: -44.715334+0.005706j
[2025-09-17 18:00:27] [Iter 507/2250] R2[56/600], Temp: 0.9787, Energy: -44.711774+0.003516j
[2025-09-17 18:00:38] [Iter 508/2250] R2[57/600], Temp: 0.9779, Energy: -44.700598-0.000606j
[2025-09-17 18:00:50] [Iter 509/2250] R2[58/600], Temp: 0.9771, Energy: -44.719097+0.000675j
[2025-09-17 18:01:01] [Iter 510/2250] R2[59/600], Temp: 0.9763, Energy: -44.711189+0.002960j
[2025-09-17 18:01:13] [Iter 511/2250] R2[60/600], Temp: 0.9755, Energy: -44.712899+0.003263j
[2025-09-17 18:01:24] [Iter 512/2250] R2[61/600], Temp: 0.9747, Energy: -44.732184-0.004385j
[2025-09-17 18:01:36] [Iter 513/2250] R2[62/600], Temp: 0.9739, Energy: -44.709888-0.001807j
[2025-09-17 18:01:48] [Iter 514/2250] R2[63/600], Temp: 0.9730, Energy: -44.730950+0.000374j
[2025-09-17 18:01:59] [Iter 515/2250] R2[64/600], Temp: 0.9722, Energy: -44.718491-0.000383j
[2025-09-17 18:02:11] [Iter 516/2250] R2[65/600], Temp: 0.9713, Energy: -44.711006-0.001842j
[2025-09-17 18:02:22] [Iter 517/2250] R2[66/600], Temp: 0.9704, Energy: -44.734592+0.000429j
[2025-09-17 18:02:34] [Iter 518/2250] R2[67/600], Temp: 0.9695, Energy: -44.709893-0.000036j
[2025-09-17 18:02:45] [Iter 519/2250] R2[68/600], Temp: 0.9686, Energy: -44.726955-0.004151j
[2025-09-17 18:02:57] [Iter 520/2250] R2[69/600], Temp: 0.9677, Energy: -44.720374-0.001492j
[2025-09-17 18:03:08] [Iter 521/2250] R2[70/600], Temp: 0.9668, Energy: -44.740259-0.002458j
[2025-09-17 18:03:20] [Iter 522/2250] R2[71/600], Temp: 0.9658, Energy: -44.738742-0.003861j
[2025-09-17 18:03:31] [Iter 523/2250] R2[72/600], Temp: 0.9649, Energy: -44.734330-0.004244j
[2025-09-17 18:03:43] [Iter 524/2250] R2[73/600], Temp: 0.9639, Energy: -44.718770+0.001514j
[2025-09-17 18:03:54] [Iter 525/2250] R2[74/600], Temp: 0.9629, Energy: -44.730933+0.006020j
[2025-09-17 18:04:06] [Iter 526/2250] R2[75/600], Temp: 0.9619, Energy: -44.715617+0.002542j
[2025-09-17 18:04:18] [Iter 527/2250] R2[76/600], Temp: 0.9609, Energy: -44.717156-0.000665j
[2025-09-17 18:04:29] [Iter 528/2250] R2[77/600], Temp: 0.9599, Energy: -44.728596+0.000522j
[2025-09-17 18:04:41] [Iter 529/2250] R2[78/600], Temp: 0.9589, Energy: -44.740663+0.006127j
[2025-09-17 18:04:52] [Iter 530/2250] R2[79/600], Temp: 0.9578, Energy: -44.745784+0.003998j
[2025-09-17 18:05:04] [Iter 531/2250] R2[80/600], Temp: 0.9568, Energy: -44.725168-0.002638j
[2025-09-17 18:05:15] [Iter 532/2250] R2[81/600], Temp: 0.9557, Energy: -44.743605+0.002880j
[2025-09-17 18:05:27] [Iter 533/2250] R2[82/600], Temp: 0.9546, Energy: -44.730600+0.001882j
[2025-09-17 18:05:38] [Iter 534/2250] R2[83/600], Temp: 0.9535, Energy: -44.728799-0.005394j
[2025-09-17 18:05:50] [Iter 535/2250] R2[84/600], Temp: 0.9524, Energy: -44.743180+0.000121j
[2025-09-17 18:06:01] [Iter 536/2250] R2[85/600], Temp: 0.9513, Energy: -44.734984-0.004517j
[2025-09-17 18:06:13] [Iter 537/2250] R2[86/600], Temp: 0.9502, Energy: -44.726388-0.002462j
[2025-09-17 18:06:25] [Iter 538/2250] R2[87/600], Temp: 0.9490, Energy: -44.738496+0.002646j
[2025-09-17 18:06:36] [Iter 539/2250] R2[88/600], Temp: 0.9479, Energy: -44.746305+0.002674j
[2025-09-17 18:06:48] [Iter 540/2250] R2[89/600], Temp: 0.9467, Energy: -44.721341-0.002068j
[2025-09-17 18:06:59] [Iter 541/2250] R2[90/600], Temp: 0.9455, Energy: -44.736611+0.001145j
[2025-09-17 18:07:11] [Iter 542/2250] R2[91/600], Temp: 0.9443, Energy: -44.721159+0.001663j
[2025-09-17 18:07:22] [Iter 543/2250] R2[92/600], Temp: 0.9431, Energy: -44.732448-0.006246j
[2025-09-17 18:07:34] [Iter 544/2250] R2[93/600], Temp: 0.9419, Energy: -44.750909-0.002096j
[2025-09-17 18:07:45] [Iter 545/2250] R2[94/600], Temp: 0.9407, Energy: -44.743201-0.000852j
[2025-09-17 18:07:57] [Iter 546/2250] R2[95/600], Temp: 0.9394, Energy: -44.725179+0.001297j
[2025-09-17 18:08:08] [Iter 547/2250] R2[96/600], Temp: 0.9382, Energy: -44.735641-0.000537j
[2025-09-17 18:08:20] [Iter 548/2250] R2[97/600], Temp: 0.9369, Energy: -44.736363+0.007254j
[2025-09-17 18:08:31] [Iter 549/2250] R2[98/600], Temp: 0.9356, Energy: -44.742134-0.000199j
[2025-09-17 18:08:43] [Iter 550/2250] R2[99/600], Temp: 0.9343, Energy: -44.756721+0.002240j
[2025-09-17 18:08:55] [Iter 551/2250] R2[100/600], Temp: 0.9330, Energy: -44.734755-0.002761j
[2025-09-17 18:09:06] [Iter 552/2250] R2[101/600], Temp: 0.9317, Energy: -44.736416+0.001115j
[2025-09-17 18:09:18] [Iter 553/2250] R2[102/600], Temp: 0.9304, Energy: -44.748501+0.001013j
[2025-09-17 18:09:29] [Iter 554/2250] R2[103/600], Temp: 0.9290, Energy: -44.734625-0.001648j
[2025-09-17 18:09:41] [Iter 555/2250] R2[104/600], Temp: 0.9277, Energy: -44.728931-0.002024j
[2025-09-17 18:09:52] [Iter 556/2250] R2[105/600], Temp: 0.9263, Energy: -44.722934+0.000369j
[2025-09-17 18:10:04] [Iter 557/2250] R2[106/600], Temp: 0.9249, Energy: -44.745518+0.000072j
[2025-09-17 18:10:15] [Iter 558/2250] R2[107/600], Temp: 0.9236, Energy: -44.739316+0.000335j
[2025-09-17 18:10:27] [Iter 559/2250] R2[108/600], Temp: 0.9222, Energy: -44.750614+0.002346j
[2025-09-17 18:10:38] [Iter 560/2250] R2[109/600], Temp: 0.9208, Energy: -44.733414-0.001389j
[2025-09-17 18:10:50] [Iter 561/2250] R2[110/600], Temp: 0.9193, Energy: -44.735062-0.002574j
[2025-09-17 18:11:02] [Iter 562/2250] R2[111/600], Temp: 0.9179, Energy: -44.742545-0.002743j
[2025-09-17 18:11:13] [Iter 563/2250] R2[112/600], Temp: 0.9165, Energy: -44.745489+0.002177j
[2025-09-17 18:11:25] [Iter 564/2250] R2[113/600], Temp: 0.9150, Energy: -44.754126+0.008189j
[2025-09-17 18:11:36] [Iter 565/2250] R2[114/600], Temp: 0.9135, Energy: -44.748750+0.000197j
[2025-09-17 18:11:48] [Iter 566/2250] R2[115/600], Temp: 0.9121, Energy: -44.743765+0.000887j
[2025-09-17 18:11:59] [Iter 567/2250] R2[116/600], Temp: 0.9106, Energy: -44.746483-0.000204j
[2025-09-17 18:12:11] [Iter 568/2250] R2[117/600], Temp: 0.9091, Energy: -44.742399+0.000147j
[2025-09-17 18:12:22] [Iter 569/2250] R2[118/600], Temp: 0.9076, Energy: -44.736004-0.003277j
[2025-09-17 18:12:34] [Iter 570/2250] R2[119/600], Temp: 0.9060, Energy: -44.741153+0.003085j
[2025-09-17 18:12:45] [Iter 571/2250] R2[120/600], Temp: 0.9045, Energy: -44.752891-0.001219j
[2025-09-17 18:12:57] [Iter 572/2250] R2[121/600], Temp: 0.9030, Energy: -44.730711+0.001085j
[2025-09-17 18:13:08] [Iter 573/2250] R2[122/600], Temp: 0.9014, Energy: -44.752434+0.009321j
[2025-09-17 18:13:20] [Iter 574/2250] R2[123/600], Temp: 0.8998, Energy: -44.752852-0.001234j
[2025-09-17 18:13:32] [Iter 575/2250] R2[124/600], Temp: 0.8983, Energy: -44.747091-0.000606j
[2025-09-17 18:13:43] [Iter 576/2250] R2[125/600], Temp: 0.8967, Energy: -44.751224+0.005659j
[2025-09-17 18:13:55] [Iter 577/2250] R2[126/600], Temp: 0.8951, Energy: -44.740734-0.001513j
[2025-09-17 18:14:06] [Iter 578/2250] R2[127/600], Temp: 0.8935, Energy: -44.747195-0.008115j
[2025-09-17 18:14:18] [Iter 579/2250] R2[128/600], Temp: 0.8918, Energy: -44.750255-0.000948j
[2025-09-17 18:14:29] [Iter 580/2250] R2[129/600], Temp: 0.8902, Energy: -44.756929+0.002593j
[2025-09-17 18:14:41] [Iter 581/2250] R2[130/600], Temp: 0.8886, Energy: -44.744936-0.004525j
[2025-09-17 18:14:52] [Iter 582/2250] R2[131/600], Temp: 0.8869, Energy: -44.752119-0.001462j
[2025-09-17 18:15:04] [Iter 583/2250] R2[132/600], Temp: 0.8853, Energy: -44.743668-0.002483j
[2025-09-17 18:15:16] [Iter 584/2250] R2[133/600], Temp: 0.8836, Energy: -44.733817+0.003971j
[2025-09-17 18:15:27] [Iter 585/2250] R2[134/600], Temp: 0.8819, Energy: -44.752995-0.002245j
[2025-09-17 18:15:39] [Iter 586/2250] R2[135/600], Temp: 0.8802, Energy: -44.746449+0.000452j
[2025-09-17 18:15:50] [Iter 587/2250] R2[136/600], Temp: 0.8785, Energy: -44.738148+0.000641j
[2025-09-17 18:16:02] [Iter 588/2250] R2[137/600], Temp: 0.8768, Energy: -44.748857+0.003410j
[2025-09-17 18:16:13] [Iter 589/2250] R2[138/600], Temp: 0.8751, Energy: -44.753115-0.004746j
[2025-09-17 18:16:25] [Iter 590/2250] R2[139/600], Temp: 0.8733, Energy: -44.756930+0.001213j
[2025-09-17 18:16:36] [Iter 591/2250] R2[140/600], Temp: 0.8716, Energy: -44.750683+0.003459j
[2025-09-17 18:16:48] [Iter 592/2250] R2[141/600], Temp: 0.8698, Energy: -44.761817-0.001662j
[2025-09-17 18:16:59] [Iter 593/2250] R2[142/600], Temp: 0.8680, Energy: -44.738361-0.004488j
[2025-09-17 18:17:11] [Iter 594/2250] R2[143/600], Temp: 0.8663, Energy: -44.738730+0.001355j
[2025-09-17 18:17:22] [Iter 595/2250] R2[144/600], Temp: 0.8645, Energy: -44.760649+0.002990j
[2025-09-17 18:17:34] [Iter 596/2250] R2[145/600], Temp: 0.8627, Energy: -44.750344-0.002048j
[2025-09-17 18:17:45] [Iter 597/2250] R2[146/600], Temp: 0.8609, Energy: -44.756588-0.000772j
[2025-09-17 18:17:57] [Iter 598/2250] R2[147/600], Temp: 0.8591, Energy: -44.754657+0.002055j
[2025-09-17 18:18:09] [Iter 599/2250] R2[148/600], Temp: 0.8572, Energy: -44.747864-0.000630j
[2025-09-17 18:18:20] [Iter 600/2250] R2[149/600], Temp: 0.8554, Energy: -44.751037-0.000092j
[2025-09-17 18:18:32] [Iter 601/2250] R2[150/600], Temp: 0.8536, Energy: -44.774316+0.004229j
[2025-09-17 18:18:43] [Iter 602/2250] R2[151/600], Temp: 0.8517, Energy: -44.764604+0.004502j
[2025-09-17 18:18:55] [Iter 603/2250] R2[152/600], Temp: 0.8498, Energy: -44.745694+0.000169j
[2025-09-17 18:19:06] [Iter 604/2250] R2[153/600], Temp: 0.8480, Energy: -44.750312-0.006041j
[2025-09-17 18:19:18] [Iter 605/2250] R2[154/600], Temp: 0.8461, Energy: -44.758169-0.000773j
[2025-09-17 18:19:29] [Iter 606/2250] R2[155/600], Temp: 0.8442, Energy: -44.742503+0.001267j
[2025-09-17 18:19:41] [Iter 607/2250] R2[156/600], Temp: 0.8423, Energy: -44.748089+0.004227j
[2025-09-17 18:19:52] [Iter 608/2250] R2[157/600], Temp: 0.8404, Energy: -44.763810-0.001340j
[2025-09-17 18:20:04] [Iter 609/2250] R2[158/600], Temp: 0.8384, Energy: -44.766390-0.003908j
[2025-09-17 18:20:15] [Iter 610/2250] R2[159/600], Temp: 0.8365, Energy: -44.759933-0.005068j
[2025-09-17 18:20:27] [Iter 611/2250] R2[160/600], Temp: 0.8346, Energy: -44.748945+0.005495j
[2025-09-17 18:20:39] [Iter 612/2250] R2[161/600], Temp: 0.8326, Energy: -44.776158-0.000820j
[2025-09-17 18:20:50] [Iter 613/2250] R2[162/600], Temp: 0.8307, Energy: -44.744579-0.001044j
[2025-09-17 18:21:02] [Iter 614/2250] R2[163/600], Temp: 0.8287, Energy: -44.754494-0.002839j
[2025-09-17 18:21:13] [Iter 615/2250] R2[164/600], Temp: 0.8267, Energy: -44.754976-0.003063j
[2025-09-17 18:21:25] [Iter 616/2250] R2[165/600], Temp: 0.8247, Energy: -44.768159+0.002771j
[2025-09-17 18:21:36] [Iter 617/2250] R2[166/600], Temp: 0.8227, Energy: -44.764841-0.001270j
[2025-09-17 18:21:48] [Iter 618/2250] R2[167/600], Temp: 0.8207, Energy: -44.750745+0.000670j
[2025-09-17 18:21:59] [Iter 619/2250] R2[168/600], Temp: 0.8187, Energy: -44.765156-0.004586j
[2025-09-17 18:22:11] [Iter 620/2250] R2[169/600], Temp: 0.8167, Energy: -44.764023+0.004026j
[2025-09-17 18:22:22] [Iter 621/2250] R2[170/600], Temp: 0.8147, Energy: -44.759329-0.001021j
[2025-09-17 18:22:34] [Iter 622/2250] R2[171/600], Temp: 0.8126, Energy: -44.754551+0.001099j
[2025-09-17 18:22:46] [Iter 623/2250] R2[172/600], Temp: 0.8106, Energy: -44.758384+0.001852j
[2025-09-17 18:22:57] [Iter 624/2250] R2[173/600], Temp: 0.8085, Energy: -44.760938-0.000428j
[2025-09-17 18:23:09] [Iter 625/2250] R2[174/600], Temp: 0.8065, Energy: -44.741210-0.001709j
[2025-09-17 18:23:20] [Iter 626/2250] R2[175/600], Temp: 0.8044, Energy: -44.763637-0.000734j
[2025-09-17 18:23:32] [Iter 627/2250] R2[176/600], Temp: 0.8023, Energy: -44.749666+0.000130j
[2025-09-17 18:23:43] [Iter 628/2250] R2[177/600], Temp: 0.8002, Energy: -44.752735+0.000062j
[2025-09-17 18:23:55] [Iter 629/2250] R2[178/600], Temp: 0.7981, Energy: -44.763227-0.001707j
[2025-09-17 18:24:06] [Iter 630/2250] R2[179/600], Temp: 0.7960, Energy: -44.766482-0.000897j
[2025-09-17 18:24:18] [Iter 631/2250] R2[180/600], Temp: 0.7939, Energy: -44.760496-0.003544j
[2025-09-17 18:24:29] [Iter 632/2250] R2[181/600], Temp: 0.7918, Energy: -44.751357+0.002215j
[2025-09-17 18:24:41] [Iter 633/2250] R2[182/600], Temp: 0.7896, Energy: -44.763658-0.001115j
[2025-09-17 18:24:53] [Iter 634/2250] R2[183/600], Temp: 0.7875, Energy: -44.763656+0.001032j
[2025-09-17 18:25:04] [Iter 635/2250] R2[184/600], Temp: 0.7854, Energy: -44.764767-0.001843j
[2025-09-17 18:25:16] [Iter 636/2250] R2[185/600], Temp: 0.7832, Energy: -44.774773+0.003141j
[2025-09-17 18:25:27] [Iter 637/2250] R2[186/600], Temp: 0.7810, Energy: -44.761207-0.000613j
[2025-09-17 18:25:39] [Iter 638/2250] R2[187/600], Temp: 0.7789, Energy: -44.761077+0.000810j
[2025-09-17 18:25:50] [Iter 639/2250] R2[188/600], Temp: 0.7767, Energy: -44.772017-0.005771j
[2025-09-17 18:26:02] [Iter 640/2250] R2[189/600], Temp: 0.7745, Energy: -44.760981+0.000628j
[2025-09-17 18:26:13] [Iter 641/2250] R2[190/600], Temp: 0.7723, Energy: -44.771447+0.003287j
[2025-09-17 18:26:25] [Iter 642/2250] R2[191/600], Temp: 0.7701, Energy: -44.774346+0.003861j
[2025-09-17 18:26:36] [Iter 643/2250] R2[192/600], Temp: 0.7679, Energy: -44.769379+0.001035j
[2025-09-17 18:26:48] [Iter 644/2250] R2[193/600], Temp: 0.7657, Energy: -44.751675-0.003452j
[2025-09-17 18:26:59] [Iter 645/2250] R2[194/600], Temp: 0.7635, Energy: -44.778949+0.002250j
[2025-09-17 18:27:11] [Iter 646/2250] R2[195/600], Temp: 0.7612, Energy: -44.767485-0.004968j
[2025-09-17 18:27:23] [Iter 647/2250] R2[196/600], Temp: 0.7590, Energy: -44.769664+0.001413j
[2025-09-17 18:27:34] [Iter 648/2250] R2[197/600], Temp: 0.7568, Energy: -44.751733-0.002531j
[2025-09-17 18:27:46] [Iter 649/2250] R2[198/600], Temp: 0.7545, Energy: -44.762085-0.000857j
[2025-09-17 18:27:57] [Iter 650/2250] R2[199/600], Temp: 0.7523, Energy: -44.764160-0.006831j
[2025-09-17 18:28:09] [Iter 651/2250] R2[200/600], Temp: 0.7500, Energy: -44.757028+0.004500j
[2025-09-17 18:28:20] [Iter 652/2250] R2[201/600], Temp: 0.7477, Energy: -44.772738-0.002970j
[2025-09-17 18:28:32] [Iter 653/2250] R2[202/600], Temp: 0.7455, Energy: -44.769049+0.001880j
[2025-09-17 18:28:43] [Iter 654/2250] R2[203/600], Temp: 0.7432, Energy: -44.770593+0.006864j
[2025-09-17 18:28:55] [Iter 655/2250] R2[204/600], Temp: 0.7409, Energy: -44.788696+0.002267j
[2025-09-17 18:29:06] [Iter 656/2250] R2[205/600], Temp: 0.7386, Energy: -44.780485+0.002071j
[2025-09-17 18:29:18] [Iter 657/2250] R2[206/600], Temp: 0.7363, Energy: -44.766950-0.001020j
[2025-09-17 18:29:30] [Iter 658/2250] R2[207/600], Temp: 0.7340, Energy: -44.761205+0.002996j
[2025-09-17 18:29:41] [Iter 659/2250] R2[208/600], Temp: 0.7316, Energy: -44.773278-0.001737j
[2025-09-17 18:29:53] [Iter 660/2250] R2[209/600], Temp: 0.7293, Energy: -44.772528+0.004078j
[2025-09-17 18:30:04] [Iter 661/2250] R2[210/600], Temp: 0.7270, Energy: -44.770265+0.002053j
[2025-09-17 18:30:16] [Iter 662/2250] R2[211/600], Temp: 0.7247, Energy: -44.757756-0.001891j
[2025-09-17 18:30:27] [Iter 663/2250] R2[212/600], Temp: 0.7223, Energy: -44.777429+0.000827j
[2025-09-17 18:30:39] [Iter 664/2250] R2[213/600], Temp: 0.7200, Energy: -44.773462-0.002361j
[2025-09-17 18:30:50] [Iter 665/2250] R2[214/600], Temp: 0.7176, Energy: -44.777972+0.000396j
[2025-09-17 18:31:02] [Iter 666/2250] R2[215/600], Temp: 0.7153, Energy: -44.767567+0.002201j
[2025-09-17 18:31:13] [Iter 667/2250] R2[216/600], Temp: 0.7129, Energy: -44.762989-0.003301j
[2025-09-17 18:31:25] [Iter 668/2250] R2[217/600], Temp: 0.7105, Energy: -44.769718-0.001610j
[2025-09-17 18:31:36] [Iter 669/2250] R2[218/600], Temp: 0.7081, Energy: -44.762481-0.000720j
[2025-09-17 18:31:48] [Iter 670/2250] R2[219/600], Temp: 0.7058, Energy: -44.762500-0.001775j
[2025-09-17 18:32:00] [Iter 671/2250] R2[220/600], Temp: 0.7034, Energy: -44.773071+0.000794j
[2025-09-17 18:32:11] [Iter 672/2250] R2[221/600], Temp: 0.7010, Energy: -44.766263+0.000226j
[2025-09-17 18:32:23] [Iter 673/2250] R2[222/600], Temp: 0.6986, Energy: -44.774511-0.002499j
[2025-09-17 18:32:34] [Iter 674/2250] R2[223/600], Temp: 0.6962, Energy: -44.787012+0.003893j
[2025-09-17 18:32:46] [Iter 675/2250] R2[224/600], Temp: 0.6938, Energy: -44.764029-0.000453j
[2025-09-17 18:32:57] [Iter 676/2250] R2[225/600], Temp: 0.6913, Energy: -44.766394+0.001233j
[2025-09-17 18:33:09] [Iter 677/2250] R2[226/600], Temp: 0.6889, Energy: -44.773722+0.003312j
[2025-09-17 18:33:20] [Iter 678/2250] R2[227/600], Temp: 0.6865, Energy: -44.772383-0.004295j
[2025-09-17 18:33:32] [Iter 679/2250] R2[228/600], Temp: 0.6841, Energy: -44.772116-0.001018j
[2025-09-17 18:33:43] [Iter 680/2250] R2[229/600], Temp: 0.6816, Energy: -44.773612+0.001086j
[2025-09-17 18:33:55] [Iter 681/2250] R2[230/600], Temp: 0.6792, Energy: -44.771516-0.003212j
[2025-09-17 18:34:06] [Iter 682/2250] R2[231/600], Temp: 0.6767, Energy: -44.774570+0.001338j
[2025-09-17 18:34:18] [Iter 683/2250] R2[232/600], Temp: 0.6743, Energy: -44.789139-0.000483j
[2025-09-17 18:34:30] [Iter 684/2250] R2[233/600], Temp: 0.6718, Energy: -44.778396-0.002704j
[2025-09-17 18:34:41] [Iter 685/2250] R2[234/600], Temp: 0.6694, Energy: -44.772455-0.003811j
[2025-09-17 18:34:53] [Iter 686/2250] R2[235/600], Temp: 0.6669, Energy: -44.774315+0.006664j
[2025-09-17 18:35:04] [Iter 687/2250] R2[236/600], Temp: 0.6644, Energy: -44.783156+0.004563j
[2025-09-17 18:35:16] [Iter 688/2250] R2[237/600], Temp: 0.6620, Energy: -44.771964+0.003048j
[2025-09-17 18:35:27] [Iter 689/2250] R2[238/600], Temp: 0.6595, Energy: -44.790699-0.002334j
[2025-09-17 18:35:39] [Iter 690/2250] R2[239/600], Temp: 0.6570, Energy: -44.774034-0.002943j
[2025-09-17 18:35:50] [Iter 691/2250] R2[240/600], Temp: 0.6545, Energy: -44.771767+0.005539j
[2025-09-17 18:36:02] [Iter 692/2250] R2[241/600], Temp: 0.6520, Energy: -44.781694-0.003203j
[2025-09-17 18:36:13] [Iter 693/2250] R2[242/600], Temp: 0.6495, Energy: -44.778174-0.003028j
[2025-09-17 18:36:25] [Iter 694/2250] R2[243/600], Temp: 0.6470, Energy: -44.770051-0.002019j
[2025-09-17 18:36:36] [Iter 695/2250] R2[244/600], Temp: 0.6445, Energy: -44.767903+0.001898j
[2025-09-17 18:36:48] [Iter 696/2250] R2[245/600], Temp: 0.6420, Energy: -44.772086-0.001773j
[2025-09-17 18:37:00] [Iter 697/2250] R2[246/600], Temp: 0.6395, Energy: -44.768078+0.001175j
[2025-09-17 18:37:11] [Iter 698/2250] R2[247/600], Temp: 0.6370, Energy: -44.772579-0.000452j
[2025-09-17 18:37:23] [Iter 699/2250] R2[248/600], Temp: 0.6345, Energy: -44.762020+0.001976j
[2025-09-17 18:37:34] [Iter 700/2250] R2[249/600], Temp: 0.6319, Energy: -44.778318+0.001206j
[2025-09-17 18:37:46] [Iter 701/2250] R2[250/600], Temp: 0.6294, Energy: -44.785834-0.002013j
[2025-09-17 18:37:57] [Iter 702/2250] R2[251/600], Temp: 0.6269, Energy: -44.776526-0.001404j
[2025-09-17 18:38:09] [Iter 703/2250] R2[252/600], Temp: 0.6243, Energy: -44.777389-0.000214j
[2025-09-17 18:38:20] [Iter 704/2250] R2[253/600], Temp: 0.6218, Energy: -44.775524+0.000065j
[2025-09-17 18:38:32] [Iter 705/2250] R2[254/600], Temp: 0.6193, Energy: -44.760160-0.004102j
[2025-09-17 18:38:43] [Iter 706/2250] R2[255/600], Temp: 0.6167, Energy: -44.764304+0.001441j
[2025-09-17 18:38:55] [Iter 707/2250] R2[256/600], Temp: 0.6142, Energy: -44.769417-0.001929j
[2025-09-17 18:39:07] [Iter 708/2250] R2[257/600], Temp: 0.6116, Energy: -44.780056-0.000514j
[2025-09-17 18:39:18] [Iter 709/2250] R2[258/600], Temp: 0.6091, Energy: -44.780718-0.000520j
[2025-09-17 18:39:30] [Iter 710/2250] R2[259/600], Temp: 0.6065, Energy: -44.785051-0.000687j
[2025-09-17 18:39:41] [Iter 711/2250] R2[260/600], Temp: 0.6040, Energy: -44.777131-0.000954j
[2025-09-17 18:39:53] [Iter 712/2250] R2[261/600], Temp: 0.6014, Energy: -44.793099-0.002986j
[2025-09-17 18:40:04] [Iter 713/2250] R2[262/600], Temp: 0.5988, Energy: -44.782414+0.005036j
[2025-09-17 18:40:16] [Iter 714/2250] R2[263/600], Temp: 0.5963, Energy: -44.783005-0.000607j
[2025-09-17 18:40:27] [Iter 715/2250] R2[264/600], Temp: 0.5937, Energy: -44.788163-0.001744j
[2025-09-17 18:40:39] [Iter 716/2250] R2[265/600], Temp: 0.5911, Energy: -44.786577-0.000480j
[2025-09-17 18:40:50] [Iter 717/2250] R2[266/600], Temp: 0.5885, Energy: -44.785499+0.001248j
[2025-09-17 18:41:02] [Iter 718/2250] R2[267/600], Temp: 0.5860, Energy: -44.772158-0.000603j
[2025-09-17 18:41:14] [Iter 719/2250] R2[268/600], Temp: 0.5834, Energy: -44.772734-0.004575j
[2025-09-17 18:41:25] [Iter 720/2250] R2[269/600], Temp: 0.5808, Energy: -44.776267+0.004949j
[2025-09-17 18:41:37] [Iter 721/2250] R2[270/600], Temp: 0.5782, Energy: -44.780567-0.000811j
[2025-09-17 18:41:48] [Iter 722/2250] R2[271/600], Temp: 0.5756, Energy: -44.781965-0.005481j
[2025-09-17 18:42:00] [Iter 723/2250] R2[272/600], Temp: 0.5730, Energy: -44.776435+0.000812j
[2025-09-17 18:42:11] [Iter 724/2250] R2[273/600], Temp: 0.5705, Energy: -44.771316-0.000629j
[2025-09-17 18:42:23] [Iter 725/2250] R2[274/600], Temp: 0.5679, Energy: -44.767878-0.000893j
[2025-09-17 18:42:34] [Iter 726/2250] R2[275/600], Temp: 0.5653, Energy: -44.784924+0.001260j
[2025-09-17 18:42:46] [Iter 727/2250] R2[276/600], Temp: 0.5627, Energy: -44.779474+0.002438j
[2025-09-17 18:42:57] [Iter 728/2250] R2[277/600], Temp: 0.5601, Energy: -44.782906+0.000007j
[2025-09-17 18:43:09] [Iter 729/2250] R2[278/600], Temp: 0.5575, Energy: -44.770745-0.000309j
[2025-09-17 18:43:20] [Iter 730/2250] R2[279/600], Temp: 0.5549, Energy: -44.776765+0.000496j
[2025-09-17 18:43:32] [Iter 731/2250] R2[280/600], Temp: 0.5523, Energy: -44.779191+0.003268j
[2025-09-17 18:43:44] [Iter 732/2250] R2[281/600], Temp: 0.5497, Energy: -44.787906-0.001010j
[2025-09-17 18:43:55] [Iter 733/2250] R2[282/600], Temp: 0.5471, Energy: -44.785968-0.001638j
[2025-09-17 18:44:07] [Iter 734/2250] R2[283/600], Temp: 0.5444, Energy: -44.780678+0.003884j
[2025-09-17 18:44:18] [Iter 735/2250] R2[284/600], Temp: 0.5418, Energy: -44.788463+0.002404j
[2025-09-17 18:44:30] [Iter 736/2250] R2[285/600], Temp: 0.5392, Energy: -44.763554+0.000255j
[2025-09-17 18:44:41] [Iter 737/2250] R2[286/600], Temp: 0.5366, Energy: -44.784952-0.001266j
[2025-09-17 18:44:53] [Iter 738/2250] R2[287/600], Temp: 0.5340, Energy: -44.790669-0.001540j
[2025-09-17 18:45:04] [Iter 739/2250] R2[288/600], Temp: 0.5314, Energy: -44.780388-0.001831j
[2025-09-17 18:45:16] [Iter 740/2250] R2[289/600], Temp: 0.5288, Energy: -44.766316+0.003776j
[2025-09-17 18:45:27] [Iter 741/2250] R2[290/600], Temp: 0.5262, Energy: -44.776459+0.002164j
[2025-09-17 18:45:39] [Iter 742/2250] R2[291/600], Temp: 0.5236, Energy: -44.781932+0.002107j
[2025-09-17 18:45:50] [Iter 743/2250] R2[292/600], Temp: 0.5209, Energy: -44.784726-0.002836j
[2025-09-17 18:46:02] [Iter 744/2250] R2[293/600], Temp: 0.5183, Energy: -44.777307+0.000635j
[2025-09-17 18:46:14] [Iter 745/2250] R2[294/600], Temp: 0.5157, Energy: -44.775134+0.000100j
[2025-09-17 18:46:25] [Iter 746/2250] R2[295/600], Temp: 0.5131, Energy: -44.778654-0.000326j
[2025-09-17 18:46:37] [Iter 747/2250] R2[296/600], Temp: 0.5105, Energy: -44.788698-0.001830j
[2025-09-17 18:46:48] [Iter 748/2250] R2[297/600], Temp: 0.5079, Energy: -44.776949+0.001142j
[2025-09-17 18:47:00] [Iter 749/2250] R2[298/600], Temp: 0.5052, Energy: -44.785914-0.000185j
[2025-09-17 18:47:11] [Iter 750/2250] R2[299/600], Temp: 0.5026, Energy: -44.773936-0.004065j
[2025-09-17 18:47:11] ✓ Checkpoint saved: checkpoint_iter_000750.pkl
[2025-09-17 18:47:23] [Iter 751/2250] R2[300/600], Temp: 0.5000, Energy: -44.775930-0.000498j
[2025-09-17 18:47:34] [Iter 752/2250] R2[301/600], Temp: 0.4974, Energy: -44.789512-0.000863j
[2025-09-17 18:47:46] [Iter 753/2250] R2[302/600], Temp: 0.4948, Energy: -44.774468+0.003926j
[2025-09-17 18:47:57] [Iter 754/2250] R2[303/600], Temp: 0.4921, Energy: -44.782027+0.001143j
[2025-09-17 18:48:09] [Iter 755/2250] R2[304/600], Temp: 0.4895, Energy: -44.774251-0.003344j
[2025-09-17 18:48:21] [Iter 756/2250] R2[305/600], Temp: 0.4869, Energy: -44.785447-0.001292j
[2025-09-17 18:48:32] [Iter 757/2250] R2[306/600], Temp: 0.4843, Energy: -44.785617+0.000083j
[2025-09-17 18:48:44] [Iter 758/2250] R2[307/600], Temp: 0.4817, Energy: -44.775902-0.001126j
[2025-09-17 18:48:55] [Iter 759/2250] R2[308/600], Temp: 0.4791, Energy: -44.774519-0.001628j
[2025-09-17 18:49:07] [Iter 760/2250] R2[309/600], Temp: 0.4764, Energy: -44.784338+0.006544j
[2025-09-17 18:49:18] [Iter 761/2250] R2[310/600], Temp: 0.4738, Energy: -44.794964-0.003863j
[2025-09-17 18:49:30] [Iter 762/2250] R2[311/600], Temp: 0.4712, Energy: -44.780383+0.000259j
[2025-09-17 18:49:41] [Iter 763/2250] R2[312/600], Temp: 0.4686, Energy: -44.794352-0.000430j
[2025-09-17 18:49:53] [Iter 764/2250] R2[313/600], Temp: 0.4660, Energy: -44.789461+0.004084j
[2025-09-17 18:50:04] [Iter 765/2250] R2[314/600], Temp: 0.4634, Energy: -44.788871-0.000659j
[2025-09-17 18:50:16] [Iter 766/2250] R2[315/600], Temp: 0.4608, Energy: -44.793151+0.006592j
[2025-09-17 18:50:27] [Iter 767/2250] R2[316/600], Temp: 0.4582, Energy: -44.785723+0.002093j
[2025-09-17 18:50:39] [Iter 768/2250] R2[317/600], Temp: 0.4556, Energy: -44.783589+0.002672j
[2025-09-17 18:50:51] [Iter 769/2250] R2[318/600], Temp: 0.4529, Energy: -44.791202-0.001245j
[2025-09-17 18:51:02] [Iter 770/2250] R2[319/600], Temp: 0.4503, Energy: -44.791812+0.002714j
[2025-09-17 18:51:14] [Iter 771/2250] R2[320/600], Temp: 0.4477, Energy: -44.795736-0.000237j
[2025-09-17 18:51:25] [Iter 772/2250] R2[321/600], Temp: 0.4451, Energy: -44.771312-0.001139j
[2025-09-17 18:51:37] [Iter 773/2250] R2[322/600], Temp: 0.4425, Energy: -44.782211+0.002172j
[2025-09-17 18:51:48] [Iter 774/2250] R2[323/600], Temp: 0.4399, Energy: -44.780471+0.000204j
[2025-09-17 18:52:00] [Iter 775/2250] R2[324/600], Temp: 0.4373, Energy: -44.791038-0.001638j
[2025-09-17 18:52:11] [Iter 776/2250] R2[325/600], Temp: 0.4347, Energy: -44.792586-0.001508j
[2025-09-17 18:52:23] [Iter 777/2250] R2[326/600], Temp: 0.4321, Energy: -44.788571+0.000013j
[2025-09-17 18:52:34] [Iter 778/2250] R2[327/600], Temp: 0.4295, Energy: -44.784640-0.002267j
[2025-09-17 18:52:46] [Iter 779/2250] R2[328/600], Temp: 0.4270, Energy: -44.790713-0.002298j
[2025-09-17 18:52:58] [Iter 780/2250] R2[329/600], Temp: 0.4244, Energy: -44.782365+0.001502j
[2025-09-17 18:53:09] [Iter 781/2250] R2[330/600], Temp: 0.4218, Energy: -44.791403-0.001479j
[2025-09-17 18:53:21] [Iter 782/2250] R2[331/600], Temp: 0.4192, Energy: -44.795466+0.003095j
[2025-09-17 18:53:32] [Iter 783/2250] R2[332/600], Temp: 0.4166, Energy: -44.785161-0.000816j
[2025-09-17 18:53:44] [Iter 784/2250] R2[333/600], Temp: 0.4140, Energy: -44.788561+0.002760j
[2025-09-17 18:53:55] [Iter 785/2250] R2[334/600], Temp: 0.4115, Energy: -44.793857+0.001340j
[2025-09-17 18:54:07] [Iter 786/2250] R2[335/600], Temp: 0.4089, Energy: -44.781470-0.003666j
[2025-09-17 18:54:18] [Iter 787/2250] R2[336/600], Temp: 0.4063, Energy: -44.794651+0.001643j
[2025-09-17 18:54:30] [Iter 788/2250] R2[337/600], Temp: 0.4037, Energy: -44.789331-0.003148j
[2025-09-17 18:54:42] [Iter 789/2250] R2[338/600], Temp: 0.4012, Energy: -44.781554-0.002352j
[2025-09-17 18:54:53] [Iter 790/2250] R2[339/600], Temp: 0.3986, Energy: -44.795796-0.000636j
[2025-09-17 18:55:05] [Iter 791/2250] R2[340/600], Temp: 0.3960, Energy: -44.797123+0.000003j
[2025-09-17 18:55:16] [Iter 792/2250] R2[341/600], Temp: 0.3935, Energy: -44.788551-0.000098j
[2025-09-17 18:55:28] [Iter 793/2250] R2[342/600], Temp: 0.3909, Energy: -44.793380-0.001605j
[2025-09-17 18:55:39] [Iter 794/2250] R2[343/600], Temp: 0.3884, Energy: -44.798521-0.001358j
[2025-09-17 18:55:51] [Iter 795/2250] R2[344/600], Temp: 0.3858, Energy: -44.782321+0.000141j
[2025-09-17 18:56:02] [Iter 796/2250] R2[345/600], Temp: 0.3833, Energy: -44.795235-0.001581j
[2025-09-17 18:56:14] [Iter 797/2250] R2[346/600], Temp: 0.3807, Energy: -44.769624-0.000405j
[2025-09-17 18:56:25] [Iter 798/2250] R2[347/600], Temp: 0.3782, Energy: -44.798569+0.004492j
[2025-09-17 18:56:37] [Iter 799/2250] R2[348/600], Temp: 0.3757, Energy: -44.796481+0.000225j
[2025-09-17 18:56:48] [Iter 800/2250] R2[349/600], Temp: 0.3731, Energy: -44.786943+0.002819j
[2025-09-17 18:57:00] [Iter 801/2250] R2[350/600], Temp: 0.3706, Energy: -44.795762+0.003829j
[2025-09-17 18:57:12] [Iter 802/2250] R2[351/600], Temp: 0.3681, Energy: -44.793253+0.001764j
[2025-09-17 18:57:23] [Iter 803/2250] R2[352/600], Temp: 0.3655, Energy: -44.806348+0.000589j
[2025-09-17 18:57:35] [Iter 804/2250] R2[353/600], Temp: 0.3630, Energy: -44.792819-0.000836j
[2025-09-17 18:57:46] [Iter 805/2250] R2[354/600], Temp: 0.3605, Energy: -44.788480-0.002742j
[2025-09-17 18:57:58] [Iter 806/2250] R2[355/600], Temp: 0.3580, Energy: -44.797109+0.002229j
[2025-09-17 18:58:09] [Iter 807/2250] R2[356/600], Temp: 0.3555, Energy: -44.792701-0.000089j
[2025-09-17 18:58:21] [Iter 808/2250] R2[357/600], Temp: 0.3530, Energy: -44.794288-0.000541j
[2025-09-17 18:58:32] [Iter 809/2250] R2[358/600], Temp: 0.3505, Energy: -44.779636+0.001365j
[2025-09-17 18:58:44] [Iter 810/2250] R2[359/600], Temp: 0.3480, Energy: -44.784080-0.001586j
[2025-09-17 18:58:56] [Iter 811/2250] R2[360/600], Temp: 0.3455, Energy: -44.780121-0.001576j
[2025-09-17 18:59:07] [Iter 812/2250] R2[361/600], Temp: 0.3430, Energy: -44.798914+0.003051j
[2025-09-17 18:59:19] [Iter 813/2250] R2[362/600], Temp: 0.3405, Energy: -44.792936-0.001860j
[2025-09-17 18:59:30] [Iter 814/2250] R2[363/600], Temp: 0.3380, Energy: -44.787615-0.002869j
[2025-09-17 18:59:42] [Iter 815/2250] R2[364/600], Temp: 0.3356, Energy: -44.792302+0.001514j
[2025-09-17 18:59:53] [Iter 816/2250] R2[365/600], Temp: 0.3331, Energy: -44.802590-0.000312j
[2025-09-17 19:00:05] [Iter 817/2250] R2[366/600], Temp: 0.3306, Energy: -44.794144-0.002571j
[2025-09-17 19:00:16] [Iter 818/2250] R2[367/600], Temp: 0.3282, Energy: -44.787119+0.000929j
[2025-09-17 19:00:28] [Iter 819/2250] R2[368/600], Temp: 0.3257, Energy: -44.785019-0.001601j
[2025-09-17 19:00:39] [Iter 820/2250] R2[369/600], Temp: 0.3233, Energy: -44.799291+0.001300j
[2025-09-17 19:00:51] [Iter 821/2250] R2[370/600], Temp: 0.3208, Energy: -44.790034-0.000086j
[2025-09-17 19:01:02] [Iter 822/2250] R2[371/600], Temp: 0.3184, Energy: -44.803332+0.002407j
[2025-09-17 19:01:14] [Iter 823/2250] R2[372/600], Temp: 0.3159, Energy: -44.785081+0.002647j
[2025-09-17 19:01:26] [Iter 824/2250] R2[373/600], Temp: 0.3135, Energy: -44.798372-0.002969j
[2025-09-17 19:01:37] [Iter 825/2250] R2[374/600], Temp: 0.3111, Energy: -44.801101+0.001783j
[2025-09-17 19:01:49] [Iter 826/2250] R2[375/600], Temp: 0.3087, Energy: -44.788512+0.001795j
[2025-09-17 19:02:00] [Iter 827/2250] R2[376/600], Temp: 0.3062, Energy: -44.795203-0.001825j
[2025-09-17 19:02:12] [Iter 828/2250] R2[377/600], Temp: 0.3038, Energy: -44.786186+0.001025j
[2025-09-17 19:02:23] [Iter 829/2250] R2[378/600], Temp: 0.3014, Energy: -44.802009-0.001353j
[2025-09-17 19:02:35] [Iter 830/2250] R2[379/600], Temp: 0.2990, Energy: -44.788428+0.000508j
[2025-09-17 19:02:46] [Iter 831/2250] R2[380/600], Temp: 0.2966, Energy: -44.791181-0.000204j
[2025-09-17 19:02:58] [Iter 832/2250] R2[381/600], Temp: 0.2942, Energy: -44.780643-0.000268j
[2025-09-17 19:03:09] [Iter 833/2250] R2[382/600], Temp: 0.2919, Energy: -44.779405-0.000941j
[2025-09-17 19:03:21] [Iter 834/2250] R2[383/600], Temp: 0.2895, Energy: -44.796201+0.000199j
[2025-09-17 19:03:33] [Iter 835/2250] R2[384/600], Temp: 0.2871, Energy: -44.788495+0.004163j
[2025-09-17 19:03:44] [Iter 836/2250] R2[385/600], Temp: 0.2847, Energy: -44.801773-0.000124j
[2025-09-17 19:03:56] [Iter 837/2250] R2[386/600], Temp: 0.2824, Energy: -44.803090-0.001622j
[2025-09-17 19:04:07] [Iter 838/2250] R2[387/600], Temp: 0.2800, Energy: -44.787233-0.001107j
[2025-09-17 19:04:19] [Iter 839/2250] R2[388/600], Temp: 0.2777, Energy: -44.788711+0.008931j
[2025-09-17 19:04:30] [Iter 840/2250] R2[389/600], Temp: 0.2753, Energy: -44.800401-0.001323j
[2025-09-17 19:04:42] [Iter 841/2250] R2[390/600], Temp: 0.2730, Energy: -44.785366+0.000637j
[2025-09-17 19:04:53] [Iter 842/2250] R2[391/600], Temp: 0.2707, Energy: -44.778534-0.003957j
[2025-09-17 19:05:05] [Iter 843/2250] R2[392/600], Temp: 0.2684, Energy: -44.777939-0.000252j
[2025-09-17 19:05:16] [Iter 844/2250] R2[393/600], Temp: 0.2660, Energy: -44.790085+0.000336j
[2025-09-17 19:05:28] [Iter 845/2250] R2[394/600], Temp: 0.2637, Energy: -44.813559+0.000577j
[2025-09-17 19:05:39] [Iter 846/2250] R2[395/600], Temp: 0.2614, Energy: -44.800446+0.000729j
[2025-09-17 19:05:51] [Iter 847/2250] R2[396/600], Temp: 0.2591, Energy: -44.798375-0.002424j
[2025-09-17 19:06:03] [Iter 848/2250] R2[397/600], Temp: 0.2568, Energy: -44.803930+0.001469j
[2025-09-17 19:06:14] [Iter 849/2250] R2[398/600], Temp: 0.2545, Energy: -44.790952+0.001736j
[2025-09-17 19:06:26] [Iter 850/2250] R2[399/600], Temp: 0.2523, Energy: -44.794293+0.002279j
[2025-09-17 19:06:37] [Iter 851/2250] R2[400/600], Temp: 0.2500, Energy: -44.786786+0.001258j
[2025-09-17 19:06:49] [Iter 852/2250] R2[401/600], Temp: 0.2477, Energy: -44.797867-0.001375j
[2025-09-17 19:07:00] [Iter 853/2250] R2[402/600], Temp: 0.2455, Energy: -44.784452+0.003011j
[2025-09-17 19:07:12] [Iter 854/2250] R2[403/600], Temp: 0.2432, Energy: -44.786256+0.000768j
[2025-09-17 19:07:23] [Iter 855/2250] R2[404/600], Temp: 0.2410, Energy: -44.805578-0.000789j
[2025-09-17 19:07:35] [Iter 856/2250] R2[405/600], Temp: 0.2388, Energy: -44.791525+0.001884j
[2025-09-17 19:07:46] [Iter 857/2250] R2[406/600], Temp: 0.2365, Energy: -44.798536-0.001957j
[2025-09-17 19:07:58] [Iter 858/2250] R2[407/600], Temp: 0.2343, Energy: -44.788468+0.001559j
[2025-09-17 19:08:09] [Iter 859/2250] R2[408/600], Temp: 0.2321, Energy: -44.789585+0.003607j
[2025-09-17 19:08:21] [Iter 860/2250] R2[409/600], Temp: 0.2299, Energy: -44.802231+0.002873j
[2025-09-17 19:08:33] [Iter 861/2250] R2[410/600], Temp: 0.2277, Energy: -44.798221+0.000238j
[2025-09-17 19:08:44] [Iter 862/2250] R2[411/600], Temp: 0.2255, Energy: -44.781999-0.000680j
[2025-09-17 19:08:56] [Iter 863/2250] R2[412/600], Temp: 0.2233, Energy: -44.795674-0.004081j
[2025-09-17 19:09:07] [Iter 864/2250] R2[413/600], Temp: 0.2211, Energy: -44.789939+0.002027j
[2025-09-17 19:09:19] [Iter 865/2250] R2[414/600], Temp: 0.2190, Energy: -44.802768-0.000960j
[2025-09-17 19:09:30] [Iter 866/2250] R2[415/600], Temp: 0.2168, Energy: -44.796241-0.001634j
[2025-09-17 19:09:42] [Iter 867/2250] R2[416/600], Temp: 0.2146, Energy: -44.797507+0.002405j
[2025-09-17 19:09:53] [Iter 868/2250] R2[417/600], Temp: 0.2125, Energy: -44.797135-0.000685j
[2025-09-17 19:10:05] [Iter 869/2250] R2[418/600], Temp: 0.2104, Energy: -44.797606+0.003606j
[2025-09-17 19:10:16] [Iter 870/2250] R2[419/600], Temp: 0.2082, Energy: -44.795375+0.001333j
[2025-09-17 19:10:28] [Iter 871/2250] R2[420/600], Temp: 0.2061, Energy: -44.801799+0.006168j
[2025-09-17 19:10:40] [Iter 872/2250] R2[421/600], Temp: 0.2040, Energy: -44.788926-0.004767j
[2025-09-17 19:10:51] [Iter 873/2250] R2[422/600], Temp: 0.2019, Energy: -44.800774-0.003872j
[2025-09-17 19:11:03] [Iter 874/2250] R2[423/600], Temp: 0.1998, Energy: -44.790804+0.000635j
[2025-09-17 19:11:14] [Iter 875/2250] R2[424/600], Temp: 0.1977, Energy: -44.798546-0.003179j
[2025-09-17 19:11:26] [Iter 876/2250] R2[425/600], Temp: 0.1956, Energy: -44.799308-0.001089j
[2025-09-17 19:11:37] [Iter 877/2250] R2[426/600], Temp: 0.1935, Energy: -44.799450-0.001251j
[2025-09-17 19:11:49] [Iter 878/2250] R2[427/600], Temp: 0.1915, Energy: -44.790882+0.001313j
[2025-09-17 19:12:00] [Iter 879/2250] R2[428/600], Temp: 0.1894, Energy: -44.818105+0.003166j
[2025-09-17 19:12:12] [Iter 880/2250] R2[429/600], Temp: 0.1874, Energy: -44.791493+0.001193j
[2025-09-17 19:12:23] [Iter 881/2250] R2[430/600], Temp: 0.1853, Energy: -44.783770-0.003315j
[2025-09-17 19:12:35] [Iter 882/2250] R2[431/600], Temp: 0.1833, Energy: -44.788671+0.002473j
[2025-09-17 19:12:46] [Iter 883/2250] R2[432/600], Temp: 0.1813, Energy: -44.805714-0.003847j
[2025-09-17 19:12:58] [Iter 884/2250] R2[433/600], Temp: 0.1793, Energy: -44.799058+0.003024j
[2025-09-17 19:13:10] [Iter 885/2250] R2[434/600], Temp: 0.1773, Energy: -44.801067+0.001336j
[2025-09-17 19:13:21] [Iter 886/2250] R2[435/600], Temp: 0.1753, Energy: -44.787281+0.002463j
[2025-09-17 19:13:33] [Iter 887/2250] R2[436/600], Temp: 0.1733, Energy: -44.801359+0.000708j
[2025-09-17 19:13:44] [Iter 888/2250] R2[437/600], Temp: 0.1713, Energy: -44.807730+0.000874j
[2025-09-17 19:13:56] [Iter 889/2250] R2[438/600], Temp: 0.1693, Energy: -44.793900+0.002087j
[2025-09-17 19:14:07] [Iter 890/2250] R2[439/600], Temp: 0.1674, Energy: -44.796947-0.000683j
[2025-09-17 19:14:19] [Iter 891/2250] R2[440/600], Temp: 0.1654, Energy: -44.808861-0.001699j
[2025-09-17 19:14:30] [Iter 892/2250] R2[441/600], Temp: 0.1635, Energy: -44.802423-0.003110j
[2025-09-17 19:14:42] [Iter 893/2250] R2[442/600], Temp: 0.1616, Energy: -44.810834+0.001746j
[2025-09-17 19:14:53] [Iter 894/2250] R2[443/600], Temp: 0.1596, Energy: -44.796742-0.001550j
[2025-09-17 19:15:07] [Iter 895/2250] R2[444/600], Temp: 0.1577, Energy: -44.802579-0.001078j
[2025-09-17 19:15:19] [Iter 896/2250] R2[445/600], Temp: 0.1558, Energy: -44.796411-0.002729j
[2025-09-17 19:15:30] [Iter 897/2250] R2[446/600], Temp: 0.1539, Energy: -44.792985+0.000129j
[2025-09-17 19:15:42] [Iter 898/2250] R2[447/600], Temp: 0.1520, Energy: -44.785129-0.001976j
[2025-09-17 19:15:53] [Iter 899/2250] R2[448/600], Temp: 0.1502, Energy: -44.794693-0.003292j
[2025-09-17 19:16:05] [Iter 900/2250] R2[449/600], Temp: 0.1483, Energy: -44.804233-0.002077j
[2025-09-17 19:16:17] [Iter 901/2250] R2[450/600], Temp: 0.1464, Energy: -44.798033-0.001768j
[2025-09-17 19:16:28] [Iter 902/2250] R2[451/600], Temp: 0.1446, Energy: -44.808762+0.000941j
[2025-09-17 19:16:40] [Iter 903/2250] R2[452/600], Temp: 0.1428, Energy: -44.788824+0.001056j
[2025-09-17 19:16:51] [Iter 904/2250] R2[453/600], Temp: 0.1409, Energy: -44.791709-0.000079j
[2025-09-17 19:17:03] [Iter 905/2250] R2[454/600], Temp: 0.1391, Energy: -44.797775-0.001692j
[2025-09-17 19:17:14] [Iter 906/2250] R2[455/600], Temp: 0.1373, Energy: -44.801413-0.000042j
[2025-09-17 19:17:26] [Iter 907/2250] R2[456/600], Temp: 0.1355, Energy: -44.798162-0.001647j
[2025-09-17 19:17:37] [Iter 908/2250] R2[457/600], Temp: 0.1337, Energy: -44.798616-0.002138j
[2025-09-17 19:17:49] [Iter 909/2250] R2[458/600], Temp: 0.1320, Energy: -44.792352-0.001749j
[2025-09-17 19:18:00] [Iter 910/2250] R2[459/600], Temp: 0.1302, Energy: -44.796418-0.000284j
[2025-09-17 19:18:12] [Iter 911/2250] R2[460/600], Temp: 0.1284, Energy: -44.791165+0.002736j
[2025-09-17 19:18:23] [Iter 912/2250] R2[461/600], Temp: 0.1267, Energy: -44.813612-0.003268j
[2025-09-17 19:18:35] [Iter 913/2250] R2[462/600], Temp: 0.1249, Energy: -44.796462+0.000652j
[2025-09-17 19:18:47] [Iter 914/2250] R2[463/600], Temp: 0.1232, Energy: -44.796987-0.002240j
[2025-09-17 19:18:58] [Iter 915/2250] R2[464/600], Temp: 0.1215, Energy: -44.790816+0.001643j
[2025-09-17 19:19:10] [Iter 916/2250] R2[465/600], Temp: 0.1198, Energy: -44.830728-0.000311j
[2025-09-17 19:19:21] [Iter 917/2250] R2[466/600], Temp: 0.1181, Energy: -44.793169+0.002636j
[2025-09-17 19:19:33] [Iter 918/2250] R2[467/600], Temp: 0.1164, Energy: -44.805140-0.000533j
[2025-09-17 19:19:44] [Iter 919/2250] R2[468/600], Temp: 0.1147, Energy: -44.794084+0.000414j
[2025-09-17 19:19:56] [Iter 920/2250] R2[469/600], Temp: 0.1131, Energy: -44.809346-0.000122j
[2025-09-17 19:20:07] [Iter 921/2250] R2[470/600], Temp: 0.1114, Energy: -44.797489-0.001992j
[2025-09-17 19:20:19] [Iter 922/2250] R2[471/600], Temp: 0.1098, Energy: -44.785392-0.001401j
[2025-09-17 19:20:30] [Iter 923/2250] R2[472/600], Temp: 0.1082, Energy: -44.803254-0.000249j
[2025-09-17 19:20:42] [Iter 924/2250] R2[473/600], Temp: 0.1065, Energy: -44.802850+0.002108j
[2025-09-17 19:20:54] [Iter 925/2250] R2[474/600], Temp: 0.1049, Energy: -44.790410+0.000037j
[2025-09-17 19:21:05] [Iter 926/2250] R2[475/600], Temp: 0.1033, Energy: -44.806964+0.000232j
[2025-09-17 19:21:17] [Iter 927/2250] R2[476/600], Temp: 0.1017, Energy: -44.798733-0.000348j
[2025-09-17 19:21:28] [Iter 928/2250] R2[477/600], Temp: 0.1002, Energy: -44.807056-0.001012j
[2025-09-17 19:21:40] [Iter 929/2250] R2[478/600], Temp: 0.0986, Energy: -44.799619-0.006081j
[2025-09-17 19:21:51] [Iter 930/2250] R2[479/600], Temp: 0.0970, Energy: -44.813689+0.002014j
[2025-09-17 19:22:03] [Iter 931/2250] R2[480/600], Temp: 0.0955, Energy: -44.806735+0.002779j
[2025-09-17 19:22:14] [Iter 932/2250] R2[481/600], Temp: 0.0940, Energy: -44.794074-0.003548j
[2025-09-17 19:22:26] [Iter 933/2250] R2[482/600], Temp: 0.0924, Energy: -44.799352+0.002085j
[2025-09-17 19:22:37] [Iter 934/2250] R2[483/600], Temp: 0.0909, Energy: -44.792731+0.003494j
[2025-09-17 19:22:49] [Iter 935/2250] R2[484/600], Temp: 0.0894, Energy: -44.798275+0.002747j
[2025-09-17 19:23:00] [Iter 936/2250] R2[485/600], Temp: 0.0879, Energy: -44.804009+0.001997j
[2025-09-17 19:23:12] [Iter 937/2250] R2[486/600], Temp: 0.0865, Energy: -44.795164+0.001148j
[2025-09-17 19:23:24] [Iter 938/2250] R2[487/600], Temp: 0.0850, Energy: -44.811176-0.000766j
[2025-09-17 19:23:35] [Iter 939/2250] R2[488/600], Temp: 0.0835, Energy: -44.818197-0.001707j
[2025-09-17 19:23:47] [Iter 940/2250] R2[489/600], Temp: 0.0821, Energy: -44.805404+0.001033j
[2025-09-17 19:23:58] [Iter 941/2250] R2[490/600], Temp: 0.0807, Energy: -44.789580-0.004028j
[2025-09-17 19:24:10] [Iter 942/2250] R2[491/600], Temp: 0.0792, Energy: -44.787361+0.002640j
[2025-09-17 19:24:21] [Iter 943/2250] R2[492/600], Temp: 0.0778, Energy: -44.804425+0.001672j
[2025-09-17 19:24:33] [Iter 944/2250] R2[493/600], Temp: 0.0764, Energy: -44.812618+0.000410j
[2025-09-17 19:24:44] [Iter 945/2250] R2[494/600], Temp: 0.0751, Energy: -44.789088-0.001040j
[2025-09-17 19:24:56] [Iter 946/2250] R2[495/600], Temp: 0.0737, Energy: -44.801798+0.000061j
[2025-09-17 19:25:07] [Iter 947/2250] R2[496/600], Temp: 0.0723, Energy: -44.804326+0.000364j
[2025-09-17 19:25:19] [Iter 948/2250] R2[497/600], Temp: 0.0710, Energy: -44.782846-0.004554j
[2025-09-17 19:25:31] [Iter 949/2250] R2[498/600], Temp: 0.0696, Energy: -44.803001+0.001252j
[2025-09-17 19:25:42] [Iter 950/2250] R2[499/600], Temp: 0.0683, Energy: -44.803384-0.002137j
[2025-09-17 19:25:54] [Iter 951/2250] R2[500/600], Temp: 0.0670, Energy: -44.811862-0.000492j
[2025-09-17 19:26:05] [Iter 952/2250] R2[501/600], Temp: 0.0657, Energy: -44.790212-0.002650j
[2025-09-17 19:26:17] [Iter 953/2250] R2[502/600], Temp: 0.0644, Energy: -44.797708+0.001775j
[2025-09-17 19:26:28] [Iter 954/2250] R2[503/600], Temp: 0.0631, Energy: -44.814096+0.000626j
[2025-09-17 19:26:40] [Iter 955/2250] R2[504/600], Temp: 0.0618, Energy: -44.805109-0.001965j
[2025-09-17 19:26:51] [Iter 956/2250] R2[505/600], Temp: 0.0606, Energy: -44.812595+0.001975j
[2025-09-17 19:27:03] [Iter 957/2250] R2[506/600], Temp: 0.0593, Energy: -44.809903-0.005144j
[2025-09-17 19:27:14] [Iter 958/2250] R2[507/600], Temp: 0.0581, Energy: -44.790594+0.005217j
[2025-09-17 19:27:26] [Iter 959/2250] R2[508/600], Temp: 0.0569, Energy: -44.809709+0.000968j
[2025-09-17 19:27:38] [Iter 960/2250] R2[509/600], Temp: 0.0557, Energy: -44.801037-0.000728j
[2025-09-17 19:27:49] [Iter 961/2250] R2[510/600], Temp: 0.0545, Energy: -44.797267+0.003552j
[2025-09-17 19:28:01] [Iter 962/2250] R2[511/600], Temp: 0.0533, Energy: -44.799703-0.002822j
[2025-09-17 19:28:12] [Iter 963/2250] R2[512/600], Temp: 0.0521, Energy: -44.802443+0.000591j
[2025-09-17 19:28:24] [Iter 964/2250] R2[513/600], Temp: 0.0510, Energy: -44.814260-0.003297j
[2025-09-17 19:28:35] [Iter 965/2250] R2[514/600], Temp: 0.0498, Energy: -44.809110+0.000084j
[2025-09-17 19:28:47] [Iter 966/2250] R2[515/600], Temp: 0.0487, Energy: -44.811847+0.000236j
[2025-09-17 19:28:58] [Iter 967/2250] R2[516/600], Temp: 0.0476, Energy: -44.816865-0.003331j
[2025-09-17 19:29:10] [Iter 968/2250] R2[517/600], Temp: 0.0465, Energy: -44.817290+0.001624j
[2025-09-17 19:29:21] [Iter 969/2250] R2[518/600], Temp: 0.0454, Energy: -44.800541+0.001883j
[2025-09-17 19:29:33] [Iter 970/2250] R2[519/600], Temp: 0.0443, Energy: -44.799637+0.001938j
[2025-09-17 19:29:44] [Iter 971/2250] R2[520/600], Temp: 0.0432, Energy: -44.815314+0.002560j
[2025-09-17 19:29:56] [Iter 972/2250] R2[521/600], Temp: 0.0422, Energy: -44.823602+0.001013j
[2025-09-17 19:30:08] [Iter 973/2250] R2[522/600], Temp: 0.0411, Energy: -44.804893-0.001898j
[2025-09-17 19:30:19] [Iter 974/2250] R2[523/600], Temp: 0.0401, Energy: -44.810037+0.003188j
[2025-09-17 19:30:31] [Iter 975/2250] R2[524/600], Temp: 0.0391, Energy: -44.806503-0.000763j
[2025-09-17 19:30:42] [Iter 976/2250] R2[525/600], Temp: 0.0381, Energy: -44.798399-0.000172j
[2025-09-17 19:30:54] [Iter 977/2250] R2[526/600], Temp: 0.0371, Energy: -44.807953+0.004746j
[2025-09-17 19:31:05] [Iter 978/2250] R2[527/600], Temp: 0.0361, Energy: -44.803327-0.003800j
[2025-09-17 19:31:17] [Iter 979/2250] R2[528/600], Temp: 0.0351, Energy: -44.805699+0.002083j
[2025-09-17 19:31:28] [Iter 980/2250] R2[529/600], Temp: 0.0342, Energy: -44.799199-0.001089j
[2025-09-17 19:31:40] [Iter 981/2250] R2[530/600], Temp: 0.0332, Energy: -44.804974-0.001834j
[2025-09-17 19:31:51] [Iter 982/2250] R2[531/600], Temp: 0.0323, Energy: -44.819415+0.000950j
[2025-09-17 19:32:03] [Iter 983/2250] R2[532/600], Temp: 0.0314, Energy: -44.806681-0.000074j
[2025-09-17 19:32:15] [Iter 984/2250] R2[533/600], Temp: 0.0305, Energy: -44.799940+0.000211j
[2025-09-17 19:32:26] [Iter 985/2250] R2[534/600], Temp: 0.0296, Energy: -44.796377-0.001378j
[2025-09-17 19:32:38] [Iter 986/2250] R2[535/600], Temp: 0.0287, Energy: -44.797056-0.003063j
[2025-09-17 19:32:49] [Iter 987/2250] R2[536/600], Temp: 0.0278, Energy: -44.793899+0.000011j
[2025-09-17 19:33:01] [Iter 988/2250] R2[537/600], Temp: 0.0270, Energy: -44.799139-0.000360j
[2025-09-17 19:33:12] [Iter 989/2250] R2[538/600], Temp: 0.0261, Energy: -44.801493+0.000294j
[2025-09-17 19:33:24] [Iter 990/2250] R2[539/600], Temp: 0.0253, Energy: -44.817770+0.001949j
[2025-09-17 19:33:35] [Iter 991/2250] R2[540/600], Temp: 0.0245, Energy: -44.821667+0.001239j
[2025-09-17 19:33:47] [Iter 992/2250] R2[541/600], Temp: 0.0237, Energy: -44.786865+0.004410j
[2025-09-17 19:33:58] [Iter 993/2250] R2[542/600], Temp: 0.0229, Energy: -44.810434+0.001516j
[2025-09-17 19:34:10] [Iter 994/2250] R2[543/600], Temp: 0.0221, Energy: -44.812930-0.001004j
[2025-09-17 19:34:21] [Iter 995/2250] R2[544/600], Temp: 0.0213, Energy: -44.806388-0.000803j
[2025-09-17 19:34:33] [Iter 996/2250] R2[545/600], Temp: 0.0206, Energy: -44.801834-0.002064j
[2025-09-17 19:34:45] [Iter 997/2250] R2[546/600], Temp: 0.0199, Energy: -44.805853+0.002978j
[2025-09-17 19:34:56] [Iter 998/2250] R2[547/600], Temp: 0.0191, Energy: -44.804176+0.001897j
[2025-09-17 19:35:08] [Iter 999/2250] R2[548/600], Temp: 0.0184, Energy: -44.807627+0.002568j
[2025-09-17 19:35:19] [Iter 1000/2250] R2[549/600], Temp: 0.0177, Energy: -44.807556-0.001894j
[2025-09-17 19:35:19] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-09-17 19:35:31] [Iter 1001/2250] R2[550/600], Temp: 0.0170, Energy: -44.798890-0.003078j
[2025-09-17 19:35:42] [Iter 1002/2250] R2[551/600], Temp: 0.0164, Energy: -44.807810+0.000962j
[2025-09-17 19:35:54] [Iter 1003/2250] R2[552/600], Temp: 0.0157, Energy: -44.794531-0.002675j
[2025-09-17 19:36:05] [Iter 1004/2250] R2[553/600], Temp: 0.0151, Energy: -44.798393-0.001508j
[2025-09-17 19:36:17] [Iter 1005/2250] R2[554/600], Temp: 0.0144, Energy: -44.802605-0.000444j
[2025-09-17 19:36:28] [Iter 1006/2250] R2[555/600], Temp: 0.0138, Energy: -44.813835+0.000357j
[2025-09-17 19:36:40] [Iter 1007/2250] R2[556/600], Temp: 0.0132, Energy: -44.808466-0.002382j
[2025-09-17 19:36:52] [Iter 1008/2250] R2[557/600], Temp: 0.0126, Energy: -44.807754+0.000003j
[2025-09-17 19:37:03] [Iter 1009/2250] R2[558/600], Temp: 0.0120, Energy: -44.823196+0.001107j
[2025-09-17 19:37:15] [Iter 1010/2250] R2[559/600], Temp: 0.0115, Energy: -44.815259+0.001335j
[2025-09-17 19:37:26] [Iter 1011/2250] R2[560/600], Temp: 0.0109, Energy: -44.795165+0.000432j
[2025-09-17 19:37:38] [Iter 1012/2250] R2[561/600], Temp: 0.0104, Energy: -44.810023+0.001788j
[2025-09-17 19:37:49] [Iter 1013/2250] R2[562/600], Temp: 0.0099, Energy: -44.804226-0.002149j
[2025-09-17 19:38:01] [Iter 1014/2250] R2[563/600], Temp: 0.0094, Energy: -44.806137+0.000771j
[2025-09-17 19:38:12] [Iter 1015/2250] R2[564/600], Temp: 0.0089, Energy: -44.816386-0.002172j
[2025-09-17 19:38:24] [Iter 1016/2250] R2[565/600], Temp: 0.0084, Energy: -44.815087-0.000213j
[2025-09-17 19:38:35] [Iter 1017/2250] R2[566/600], Temp: 0.0079, Energy: -44.813342+0.002332j
[2025-09-17 19:38:47] [Iter 1018/2250] R2[567/600], Temp: 0.0074, Energy: -44.805565-0.001073j
[2025-09-17 19:38:59] [Iter 1019/2250] R2[568/600], Temp: 0.0070, Energy: -44.797554-0.002019j
[2025-09-17 19:39:10] [Iter 1020/2250] R2[569/600], Temp: 0.0066, Energy: -44.812408+0.001521j
[2025-09-17 19:39:22] [Iter 1021/2250] R2[570/600], Temp: 0.0062, Energy: -44.810652+0.000147j
[2025-09-17 19:39:33] [Iter 1022/2250] R2[571/600], Temp: 0.0058, Energy: -44.811901+0.000935j
[2025-09-17 19:39:45] [Iter 1023/2250] R2[572/600], Temp: 0.0054, Energy: -44.804353-0.000246j
[2025-09-17 19:39:56] [Iter 1024/2250] R2[573/600], Temp: 0.0050, Energy: -44.816430+0.001140j
[2025-09-17 19:40:08] [Iter 1025/2250] R2[574/600], Temp: 0.0046, Energy: -44.808906-0.001607j
[2025-09-17 19:40:19] [Iter 1026/2250] R2[575/600], Temp: 0.0043, Energy: -44.796198+0.001828j
[2025-09-17 19:40:31] [Iter 1027/2250] R2[576/600], Temp: 0.0039, Energy: -44.798050+0.002660j
[2025-09-17 19:40:42] [Iter 1028/2250] R2[577/600], Temp: 0.0036, Energy: -44.814440-0.000027j
[2025-09-17 19:40:54] [Iter 1029/2250] R2[578/600], Temp: 0.0033, Energy: -44.803995+0.001393j
[2025-09-17 19:41:06] [Iter 1030/2250] R2[579/600], Temp: 0.0030, Energy: -44.807226-0.000849j
[2025-09-17 19:41:17] [Iter 1031/2250] R2[580/600], Temp: 0.0027, Energy: -44.803684+0.001114j
[2025-09-17 19:41:29] [Iter 1032/2250] R2[581/600], Temp: 0.0025, Energy: -44.807804+0.003121j
[2025-09-17 19:41:40] [Iter 1033/2250] R2[582/600], Temp: 0.0022, Energy: -44.814779-0.000839j
[2025-09-17 19:41:52] [Iter 1034/2250] R2[583/600], Temp: 0.0020, Energy: -44.800047-0.001854j
[2025-09-17 19:42:03] [Iter 1035/2250] R2[584/600], Temp: 0.0018, Energy: -44.791752-0.000598j
[2025-09-17 19:42:15] [Iter 1036/2250] R2[585/600], Temp: 0.0015, Energy: -44.813536+0.001376j
[2025-09-17 19:42:26] [Iter 1037/2250] R2[586/600], Temp: 0.0013, Energy: -44.806433-0.000339j
[2025-09-17 19:42:38] [Iter 1038/2250] R2[587/600], Temp: 0.0012, Energy: -44.802070-0.001465j
[2025-09-17 19:42:49] [Iter 1039/2250] R2[588/600], Temp: 0.0010, Energy: -44.811296+0.003704j
[2025-09-17 19:43:01] [Iter 1040/2250] R2[589/600], Temp: 0.0008, Energy: -44.800743-0.001498j
[2025-09-17 19:43:12] [Iter 1041/2250] R2[590/600], Temp: 0.0007, Energy: -44.815068+0.001463j
[2025-09-17 19:43:24] [Iter 1042/2250] R2[591/600], Temp: 0.0006, Energy: -44.809488-0.000796j
[2025-09-17 19:43:36] [Iter 1043/2250] R2[592/600], Temp: 0.0004, Energy: -44.817980-0.002746j
[2025-09-17 19:43:47] [Iter 1044/2250] R2[593/600], Temp: 0.0003, Energy: -44.807754-0.000703j
[2025-09-17 19:43:59] [Iter 1045/2250] R2[594/600], Temp: 0.0002, Energy: -44.819583+0.000081j
[2025-09-17 19:44:10] [Iter 1046/2250] R2[595/600], Temp: 0.0002, Energy: -44.813209-0.000825j
[2025-09-17 19:44:22] [Iter 1047/2250] R2[596/600], Temp: 0.0001, Energy: -44.796126+0.000982j
[2025-09-17 19:44:33] [Iter 1048/2250] R2[597/600], Temp: 0.0001, Energy: -44.813892-0.002409j
[2025-09-17 19:44:45] [Iter 1049/2250] R2[598/600], Temp: 0.0000, Energy: -44.818024+0.000353j
[2025-09-17 19:44:57] [Iter 1050/2250] R2[599/600], Temp: 0.0000, Energy: -44.803599-0.000162j
[2025-09-17 19:44:57] RESTART #3 | Period: 1200
[2025-09-17 19:45:08] [Iter 1051/2250] R3[0/1200], Temp: 1.0000, Energy: -44.816301-0.000753j
[2025-09-17 19:45:20] [Iter 1052/2250] R3[1/1200], Temp: 1.0000, Energy: -44.818830+0.001804j
[2025-09-17 19:45:31] [Iter 1053/2250] R3[2/1200], Temp: 1.0000, Energy: -44.807179+0.001421j
[2025-09-17 19:45:43] [Iter 1054/2250] R3[3/1200], Temp: 1.0000, Energy: -44.808059-0.000835j
[2025-09-17 19:45:54] [Iter 1055/2250] R3[4/1200], Temp: 1.0000, Energy: -44.817783-0.001635j
[2025-09-17 19:46:06] [Iter 1056/2250] R3[5/1200], Temp: 1.0000, Energy: -44.806712-0.003721j
[2025-09-17 19:46:17] [Iter 1057/2250] R3[6/1200], Temp: 0.9999, Energy: -44.811804+0.001054j
[2025-09-17 19:46:29] [Iter 1058/2250] R3[7/1200], Temp: 0.9999, Energy: -44.802013-0.001762j
[2025-09-17 19:46:40] [Iter 1059/2250] R3[8/1200], Temp: 0.9999, Energy: -44.814053-0.002195j
[2025-09-17 19:46:52] [Iter 1060/2250] R3[9/1200], Temp: 0.9999, Energy: -44.805882+0.001062j
[2025-09-17 19:47:03] [Iter 1061/2250] R3[10/1200], Temp: 0.9998, Energy: -44.798734-0.002624j
[2025-09-17 19:47:15] [Iter 1062/2250] R3[11/1200], Temp: 0.9998, Energy: -44.814971+0.001105j
[2025-09-17 19:47:26] [Iter 1063/2250] R3[12/1200], Temp: 0.9998, Energy: -44.806121-0.001052j
[2025-09-17 19:47:38] [Iter 1064/2250] R3[13/1200], Temp: 0.9997, Energy: -44.815359+0.001684j
[2025-09-17 19:47:50] [Iter 1065/2250] R3[14/1200], Temp: 0.9997, Energy: -44.814980+0.004348j
[2025-09-17 19:48:01] [Iter 1066/2250] R3[15/1200], Temp: 0.9996, Energy: -44.810055-0.001235j
[2025-09-17 19:48:13] [Iter 1067/2250] R3[16/1200], Temp: 0.9996, Energy: -44.813243-0.002235j
[2025-09-17 19:48:24] [Iter 1068/2250] R3[17/1200], Temp: 0.9995, Energy: -44.813765-0.002304j
[2025-09-17 19:48:36] [Iter 1069/2250] R3[18/1200], Temp: 0.9994, Energy: -44.822121-0.000451j
[2025-09-17 19:48:47] [Iter 1070/2250] R3[19/1200], Temp: 0.9994, Energy: -44.822676-0.000633j
[2025-09-17 19:48:59] [Iter 1071/2250] R3[20/1200], Temp: 0.9993, Energy: -44.819977+0.000741j
[2025-09-17 19:49:10] [Iter 1072/2250] R3[21/1200], Temp: 0.9992, Energy: -44.818803-0.002465j
[2025-09-17 19:49:22] [Iter 1073/2250] R3[22/1200], Temp: 0.9992, Energy: -44.818193-0.000124j
[2025-09-17 19:49:34] [Iter 1074/2250] R3[23/1200], Temp: 0.9991, Energy: -44.813786+0.001655j
[2025-09-17 19:49:45] [Iter 1075/2250] R3[24/1200], Temp: 0.9990, Energy: -44.809738+0.000235j
[2025-09-17 19:49:57] [Iter 1076/2250] R3[25/1200], Temp: 0.9989, Energy: -44.807681+0.002594j
[2025-09-17 19:50:08] [Iter 1077/2250] R3[26/1200], Temp: 0.9988, Energy: -44.821014+0.000823j
[2025-09-17 19:50:20] [Iter 1078/2250] R3[27/1200], Temp: 0.9988, Energy: -44.806766+0.001164j
[2025-09-17 19:50:31] [Iter 1079/2250] R3[28/1200], Temp: 0.9987, Energy: -44.817119+0.000990j
[2025-09-17 19:50:43] [Iter 1080/2250] R3[29/1200], Temp: 0.9986, Energy: -44.815032-0.003925j
[2025-09-17 19:50:54] [Iter 1081/2250] R3[30/1200], Temp: 0.9985, Energy: -44.809355-0.001477j
[2025-09-17 19:51:06] [Iter 1082/2250] R3[31/1200], Temp: 0.9984, Energy: -44.805093+0.000549j
[2025-09-17 19:51:17] [Iter 1083/2250] R3[32/1200], Temp: 0.9982, Energy: -44.813943+0.002677j
[2025-09-17 19:51:29] [Iter 1084/2250] R3[33/1200], Temp: 0.9981, Energy: -44.808184-0.000654j
[2025-09-17 19:51:40] [Iter 1085/2250] R3[34/1200], Temp: 0.9980, Energy: -44.813462-0.000727j
[2025-09-17 19:51:52] [Iter 1086/2250] R3[35/1200], Temp: 0.9979, Energy: -44.815135+0.001560j
[2025-09-17 19:52:04] [Iter 1087/2250] R3[36/1200], Temp: 0.9978, Energy: -44.815567-0.004302j
[2025-09-17 19:52:15] [Iter 1088/2250] R3[37/1200], Temp: 0.9977, Energy: -44.807919-0.001369j
[2025-09-17 19:52:27] [Iter 1089/2250] R3[38/1200], Temp: 0.9975, Energy: -44.804309+0.000390j
[2025-09-17 19:52:38] [Iter 1090/2250] R3[39/1200], Temp: 0.9974, Energy: -44.811952+0.001428j
[2025-09-17 19:52:50] [Iter 1091/2250] R3[40/1200], Temp: 0.9973, Energy: -44.817846+0.002344j
[2025-09-17 19:53:01] [Iter 1092/2250] R3[41/1200], Temp: 0.9971, Energy: -44.808848+0.006172j
[2025-09-17 19:53:13] [Iter 1093/2250] R3[42/1200], Temp: 0.9970, Energy: -44.808361-0.000140j
[2025-09-17 19:53:24] [Iter 1094/2250] R3[43/1200], Temp: 0.9968, Energy: -44.810804-0.000889j
[2025-09-17 19:53:36] [Iter 1095/2250] R3[44/1200], Temp: 0.9967, Energy: -44.813165+0.001450j
[2025-09-17 19:53:47] [Iter 1096/2250] R3[45/1200], Temp: 0.9965, Energy: -44.809639+0.001282j
[2025-09-17 19:53:59] [Iter 1097/2250] R3[46/1200], Temp: 0.9964, Energy: -44.804684-0.002666j
[2025-09-17 19:54:11] [Iter 1098/2250] R3[47/1200], Temp: 0.9962, Energy: -44.813745+0.003579j
[2025-09-17 19:54:22] [Iter 1099/2250] R3[48/1200], Temp: 0.9961, Energy: -44.808820+0.002126j
[2025-09-17 19:54:34] [Iter 1100/2250] R3[49/1200], Temp: 0.9959, Energy: -44.816153+0.002013j
[2025-09-17 19:54:45] [Iter 1101/2250] R3[50/1200], Temp: 0.9957, Energy: -44.819215-0.001656j
[2025-09-17 19:54:57] [Iter 1102/2250] R3[51/1200], Temp: 0.9955, Energy: -44.820171-0.000359j
[2025-09-17 19:55:08] [Iter 1103/2250] R3[52/1200], Temp: 0.9954, Energy: -44.811778+0.002958j
[2025-09-17 19:55:20] [Iter 1104/2250] R3[53/1200], Temp: 0.9952, Energy: -44.815461-0.003424j
[2025-09-17 19:55:31] [Iter 1105/2250] R3[54/1200], Temp: 0.9950, Energy: -44.822216-0.000137j
[2025-09-17 19:55:43] [Iter 1106/2250] R3[55/1200], Temp: 0.9948, Energy: -44.821416+0.000709j
[2025-09-17 19:55:54] [Iter 1107/2250] R3[56/1200], Temp: 0.9946, Energy: -44.816472+0.002677j
[2025-09-17 19:56:06] [Iter 1108/2250] R3[57/1200], Temp: 0.9944, Energy: -44.828143+0.000634j
[2025-09-17 19:56:17] [Iter 1109/2250] R3[58/1200], Temp: 0.9942, Energy: -44.800592+0.000295j
[2025-09-17 19:56:29] [Iter 1110/2250] R3[59/1200], Temp: 0.9940, Energy: -44.816501-0.000805j
[2025-09-17 19:56:41] [Iter 1111/2250] R3[60/1200], Temp: 0.9938, Energy: -44.815426-0.005500j
[2025-09-17 19:56:52] [Iter 1112/2250] R3[61/1200], Temp: 0.9936, Energy: -44.816069-0.000604j
[2025-09-17 19:57:04] [Iter 1113/2250] R3[62/1200], Temp: 0.9934, Energy: -44.820711-0.003408j
[2025-09-17 19:57:15] [Iter 1114/2250] R3[63/1200], Temp: 0.9932, Energy: -44.798158+0.000280j
[2025-09-17 19:57:27] [Iter 1115/2250] R3[64/1200], Temp: 0.9930, Energy: -44.806285-0.001051j
[2025-09-17 19:57:38] [Iter 1116/2250] R3[65/1200], Temp: 0.9928, Energy: -44.828076+0.000688j
[2025-09-17 19:57:50] [Iter 1117/2250] R3[66/1200], Temp: 0.9926, Energy: -44.827585-0.002141j
[2025-09-17 19:58:01] [Iter 1118/2250] R3[67/1200], Temp: 0.9923, Energy: -44.813327-0.004671j
[2025-09-17 19:58:13] [Iter 1119/2250] R3[68/1200], Temp: 0.9921, Energy: -44.820586+0.000063j
[2025-09-17 19:58:24] [Iter 1120/2250] R3[69/1200], Temp: 0.9919, Energy: -44.816568-0.002659j
[2025-09-17 19:58:36] [Iter 1121/2250] R3[70/1200], Temp: 0.9916, Energy: -44.813132+0.000433j
[2025-09-17 19:58:47] [Iter 1122/2250] R3[71/1200], Temp: 0.9914, Energy: -44.804678+0.004955j
[2025-09-17 19:58:59] [Iter 1123/2250] R3[72/1200], Temp: 0.9911, Energy: -44.821075+0.001221j
[2025-09-17 19:59:11] [Iter 1124/2250] R3[73/1200], Temp: 0.9909, Energy: -44.807419-0.000733j
[2025-09-17 19:59:22] [Iter 1125/2250] R3[74/1200], Temp: 0.9906, Energy: -44.805796-0.001059j
[2025-09-17 19:59:34] [Iter 1126/2250] R3[75/1200], Temp: 0.9904, Energy: -44.814993-0.000556j
[2025-09-17 19:59:45] [Iter 1127/2250] R3[76/1200], Temp: 0.9901, Energy: -44.817743+0.000388j
[2025-09-17 19:59:57] [Iter 1128/2250] R3[77/1200], Temp: 0.9899, Energy: -44.808902-0.000815j
[2025-09-17 20:00:08] [Iter 1129/2250] R3[78/1200], Temp: 0.9896, Energy: -44.815004+0.001248j
[2025-09-17 20:00:20] [Iter 1130/2250] R3[79/1200], Temp: 0.9893, Energy: -44.830214-0.001238j
[2025-09-17 20:00:31] [Iter 1131/2250] R3[80/1200], Temp: 0.9891, Energy: -44.818421+0.000699j
[2025-09-17 20:00:43] [Iter 1132/2250] R3[81/1200], Temp: 0.9888, Energy: -44.816064-0.001215j
[2025-09-17 20:00:54] [Iter 1133/2250] R3[82/1200], Temp: 0.9885, Energy: -44.811307-0.001210j
[2025-09-17 20:01:06] [Iter 1134/2250] R3[83/1200], Temp: 0.9882, Energy: -44.818963-0.000343j
[2025-09-17 20:01:17] [Iter 1135/2250] R3[84/1200], Temp: 0.9880, Energy: -44.816931-0.000833j
[2025-09-17 20:01:29] [Iter 1136/2250] R3[85/1200], Temp: 0.9877, Energy: -44.817394+0.003160j
[2025-09-17 20:01:41] [Iter 1137/2250] R3[86/1200], Temp: 0.9874, Energy: -44.813025-0.000833j
[2025-09-17 20:01:52] [Iter 1138/2250] R3[87/1200], Temp: 0.9871, Energy: -44.818211+0.002071j
[2025-09-17 20:02:04] [Iter 1139/2250] R3[88/1200], Temp: 0.9868, Energy: -44.817636-0.001092j
[2025-09-17 20:02:15] [Iter 1140/2250] R3[89/1200], Temp: 0.9865, Energy: -44.814220-0.000145j
[2025-09-17 20:02:27] [Iter 1141/2250] R3[90/1200], Temp: 0.9862, Energy: -44.807017-0.002739j
[2025-09-17 20:02:38] [Iter 1142/2250] R3[91/1200], Temp: 0.9859, Energy: -44.828385+0.001364j
[2025-09-17 20:02:50] [Iter 1143/2250] R3[92/1200], Temp: 0.9856, Energy: -44.803272-0.001012j
[2025-09-17 20:03:01] [Iter 1144/2250] R3[93/1200], Temp: 0.9853, Energy: -44.810962+0.000558j
[2025-09-17 20:03:13] [Iter 1145/2250] R3[94/1200], Temp: 0.9849, Energy: -44.817326-0.004215j
[2025-09-17 20:03:24] [Iter 1146/2250] R3[95/1200], Temp: 0.9846, Energy: -44.825662+0.005299j
[2025-09-17 20:03:36] [Iter 1147/2250] R3[96/1200], Temp: 0.9843, Energy: -44.809487-0.000410j
[2025-09-17 20:03:47] [Iter 1148/2250] R3[97/1200], Temp: 0.9840, Energy: -44.824598-0.001453j
[2025-09-17 20:03:59] [Iter 1149/2250] R3[98/1200], Temp: 0.9836, Energy: -44.816523+0.004397j
[2025-09-17 20:04:11] [Iter 1150/2250] R3[99/1200], Temp: 0.9833, Energy: -44.816614+0.001638j
[2025-09-17 20:04:22] [Iter 1151/2250] R3[100/1200], Temp: 0.9830, Energy: -44.811504-0.000234j
[2025-09-17 20:04:34] [Iter 1152/2250] R3[101/1200], Temp: 0.9826, Energy: -44.824358+0.000157j
[2025-09-17 20:04:45] [Iter 1153/2250] R3[102/1200], Temp: 0.9823, Energy: -44.819118+0.000791j
[2025-09-17 20:04:57] [Iter 1154/2250] R3[103/1200], Temp: 0.9819, Energy: -44.824503+0.001648j
[2025-09-17 20:05:08] [Iter 1155/2250] R3[104/1200], Temp: 0.9816, Energy: -44.815124+0.000367j
[2025-09-17 20:05:20] [Iter 1156/2250] R3[105/1200], Temp: 0.9812, Energy: -44.827336-0.002856j
[2025-09-17 20:05:31] [Iter 1157/2250] R3[106/1200], Temp: 0.9809, Energy: -44.826203+0.000736j
[2025-09-17 20:05:43] [Iter 1158/2250] R3[107/1200], Temp: 0.9805, Energy: -44.805976+0.001216j
[2025-09-17 20:05:54] [Iter 1159/2250] R3[108/1200], Temp: 0.9801, Energy: -44.811493+0.002961j
[2025-09-17 20:06:06] [Iter 1160/2250] R3[109/1200], Temp: 0.9798, Energy: -44.810647+0.000743j
[2025-09-17 20:06:17] [Iter 1161/2250] R3[110/1200], Temp: 0.9794, Energy: -44.824125+0.001266j
[2025-09-17 20:06:29] [Iter 1162/2250] R3[111/1200], Temp: 0.9790, Energy: -44.817900-0.000347j
[2025-09-17 20:06:41] [Iter 1163/2250] R3[112/1200], Temp: 0.9787, Energy: -44.815004+0.000075j
[2025-09-17 20:06:52] [Iter 1164/2250] R3[113/1200], Temp: 0.9783, Energy: -44.800498+0.000648j
[2025-09-17 20:07:04] [Iter 1165/2250] R3[114/1200], Temp: 0.9779, Energy: -44.822422-0.002649j
[2025-09-17 20:07:15] [Iter 1166/2250] R3[115/1200], Temp: 0.9775, Energy: -44.814466+0.005165j
[2025-09-17 20:07:27] [Iter 1167/2250] R3[116/1200], Temp: 0.9771, Energy: -44.814042+0.001577j
[2025-09-17 20:07:38] [Iter 1168/2250] R3[117/1200], Temp: 0.9767, Energy: -44.812075+0.001457j
[2025-09-17 20:07:50] [Iter 1169/2250] R3[118/1200], Temp: 0.9763, Energy: -44.806885-0.000789j
[2025-09-17 20:08:01] [Iter 1170/2250] R3[119/1200], Temp: 0.9759, Energy: -44.804319+0.002388j
[2025-09-17 20:08:13] [Iter 1171/2250] R3[120/1200], Temp: 0.9755, Energy: -44.814348-0.000702j
[2025-09-17 20:08:24] [Iter 1172/2250] R3[121/1200], Temp: 0.9751, Energy: -44.811725-0.000372j
[2025-09-17 20:08:36] [Iter 1173/2250] R3[122/1200], Temp: 0.9747, Energy: -44.814406-0.002234j
[2025-09-17 20:08:48] [Iter 1174/2250] R3[123/1200], Temp: 0.9743, Energy: -44.822903-0.002106j
[2025-09-17 20:08:59] [Iter 1175/2250] R3[124/1200], Temp: 0.9739, Energy: -44.820346-0.001104j
[2025-09-17 20:09:11] [Iter 1176/2250] R3[125/1200], Temp: 0.9735, Energy: -44.815648-0.001968j
[2025-09-17 20:09:22] [Iter 1177/2250] R3[126/1200], Temp: 0.9730, Energy: -44.818689-0.000379j
[2025-09-17 20:09:34] [Iter 1178/2250] R3[127/1200], Temp: 0.9726, Energy: -44.831616-0.002524j
[2025-09-17 20:09:45] [Iter 1179/2250] R3[128/1200], Temp: 0.9722, Energy: -44.818829+0.000018j
[2025-09-17 20:09:57] [Iter 1180/2250] R3[129/1200], Temp: 0.9718, Energy: -44.817926+0.002123j
[2025-09-17 20:10:08] [Iter 1181/2250] R3[130/1200], Temp: 0.9713, Energy: -44.817285-0.000369j
[2025-09-17 20:10:20] [Iter 1182/2250] R3[131/1200], Temp: 0.9709, Energy: -44.822097+0.002040j
[2025-09-17 20:10:31] [Iter 1183/2250] R3[132/1200], Temp: 0.9704, Energy: -44.815276+0.001889j
[2025-09-17 20:10:43] [Iter 1184/2250] R3[133/1200], Temp: 0.9700, Energy: -44.811645-0.000523j
[2025-09-17 20:10:54] [Iter 1185/2250] R3[134/1200], Temp: 0.9695, Energy: -44.816298-0.000755j
[2025-09-17 20:11:06] [Iter 1186/2250] R3[135/1200], Temp: 0.9691, Energy: -44.815494+0.002491j
[2025-09-17 20:11:18] [Iter 1187/2250] R3[136/1200], Temp: 0.9686, Energy: -44.819251+0.002705j
[2025-09-17 20:11:29] [Iter 1188/2250] R3[137/1200], Temp: 0.9682, Energy: -44.819077+0.002889j
[2025-09-17 20:11:41] [Iter 1189/2250] R3[138/1200], Temp: 0.9677, Energy: -44.818680-0.000503j
[2025-09-17 20:11:52] [Iter 1190/2250] R3[139/1200], Temp: 0.9673, Energy: -44.807303+0.001099j
[2025-09-17 20:12:04] [Iter 1191/2250] R3[140/1200], Temp: 0.9668, Energy: -44.822948+0.001472j
[2025-09-17 20:12:15] [Iter 1192/2250] R3[141/1200], Temp: 0.9663, Energy: -44.804599+0.002004j
[2025-09-17 20:12:27] [Iter 1193/2250] R3[142/1200], Temp: 0.9658, Energy: -44.823781-0.000758j
[2025-09-17 20:12:38] [Iter 1194/2250] R3[143/1200], Temp: 0.9654, Energy: -44.817331-0.002310j
[2025-09-17 20:12:50] [Iter 1195/2250] R3[144/1200], Temp: 0.9649, Energy: -44.823308-0.000838j
[2025-09-17 20:13:01] [Iter 1196/2250] R3[145/1200], Temp: 0.9644, Energy: -44.822392-0.002112j
[2025-09-17 20:13:13] [Iter 1197/2250] R3[146/1200], Temp: 0.9639, Energy: -44.811899-0.000149j
[2025-09-17 20:13:25] [Iter 1198/2250] R3[147/1200], Temp: 0.9634, Energy: -44.820576-0.005329j
[2025-09-17 20:13:36] [Iter 1199/2250] R3[148/1200], Temp: 0.9629, Energy: -44.807969+0.002527j
[2025-09-17 20:13:48] [Iter 1200/2250] R3[149/1200], Temp: 0.9624, Energy: -44.810471+0.000706j
[2025-09-17 20:13:59] [Iter 1201/2250] R3[150/1200], Temp: 0.9619, Energy: -44.818359+0.002193j
[2025-09-17 20:14:11] [Iter 1202/2250] R3[151/1200], Temp: 0.9614, Energy: -44.821590+0.000279j
[2025-09-17 20:14:22] [Iter 1203/2250] R3[152/1200], Temp: 0.9609, Energy: -44.808432-0.000969j
[2025-09-17 20:14:34] [Iter 1204/2250] R3[153/1200], Temp: 0.9604, Energy: -44.823691+0.000167j
[2025-09-17 20:14:45] [Iter 1205/2250] R3[154/1200], Temp: 0.9599, Energy: -44.819523+0.000550j
[2025-09-17 20:14:57] [Iter 1206/2250] R3[155/1200], Temp: 0.9594, Energy: -44.810776+0.004992j
[2025-09-17 20:15:08] [Iter 1207/2250] R3[156/1200], Temp: 0.9589, Energy: -44.823645-0.001740j
[2025-09-17 20:15:20] [Iter 1208/2250] R3[157/1200], Temp: 0.9584, Energy: -44.815320+0.000254j
[2025-09-17 20:15:32] [Iter 1209/2250] R3[158/1200], Temp: 0.9578, Energy: -44.818027+0.000887j
[2025-09-17 20:15:43] [Iter 1210/2250] R3[159/1200], Temp: 0.9573, Energy: -44.820745+0.000456j
[2025-09-17 20:15:55] [Iter 1211/2250] R3[160/1200], Temp: 0.9568, Energy: -44.836188-0.000348j
[2025-09-17 20:16:06] [Iter 1212/2250] R3[161/1200], Temp: 0.9562, Energy: -44.826697-0.001173j
[2025-09-17 20:16:18] [Iter 1213/2250] R3[162/1200], Temp: 0.9557, Energy: -44.812876-0.002713j
[2025-09-17 20:16:29] [Iter 1214/2250] R3[163/1200], Temp: 0.9552, Energy: -44.811710-0.000670j
[2025-09-17 20:16:41] [Iter 1215/2250] R3[164/1200], Temp: 0.9546, Energy: -44.823495-0.000054j
[2025-09-17 20:16:52] [Iter 1216/2250] R3[165/1200], Temp: 0.9541, Energy: -44.833739-0.000773j
[2025-09-17 20:17:04] [Iter 1217/2250] R3[166/1200], Temp: 0.9535, Energy: -44.825335-0.002611j
[2025-09-17 20:17:15] [Iter 1218/2250] R3[167/1200], Temp: 0.9530, Energy: -44.812744-0.003442j
[2025-09-17 20:17:27] [Iter 1219/2250] R3[168/1200], Temp: 0.9524, Energy: -44.823068+0.001511j
[2025-09-17 20:17:39] [Iter 1220/2250] R3[169/1200], Temp: 0.9519, Energy: -44.828715-0.001240j
[2025-09-17 20:17:50] [Iter 1221/2250] R3[170/1200], Temp: 0.9513, Energy: -44.821150-0.000522j
[2025-09-17 20:18:02] [Iter 1222/2250] R3[171/1200], Temp: 0.9507, Energy: -44.820451-0.002300j
[2025-09-17 20:18:13] [Iter 1223/2250] R3[172/1200], Temp: 0.9502, Energy: -44.811097+0.004288j
[2025-09-17 20:18:25] [Iter 1224/2250] R3[173/1200], Temp: 0.9496, Energy: -44.808567-0.001077j
[2025-09-17 20:18:36] [Iter 1225/2250] R3[174/1200], Temp: 0.9490, Energy: -44.807217+0.001262j
[2025-09-17 20:18:48] [Iter 1226/2250] R3[175/1200], Temp: 0.9484, Energy: -44.816688-0.001769j
[2025-09-17 20:18:59] [Iter 1227/2250] R3[176/1200], Temp: 0.9479, Energy: -44.829510-0.000420j
[2025-09-17 20:19:11] [Iter 1228/2250] R3[177/1200], Temp: 0.9473, Energy: -44.817495-0.002426j
[2025-09-17 20:19:22] [Iter 1229/2250] R3[178/1200], Temp: 0.9467, Energy: -44.824465+0.000663j
[2025-09-17 20:19:34] [Iter 1230/2250] R3[179/1200], Temp: 0.9461, Energy: -44.824825+0.001134j
[2025-09-17 20:19:46] [Iter 1231/2250] R3[180/1200], Temp: 0.9455, Energy: -44.830874+0.001233j
[2025-09-17 20:19:57] [Iter 1232/2250] R3[181/1200], Temp: 0.9449, Energy: -44.828622-0.004685j
[2025-09-17 20:20:09] [Iter 1233/2250] R3[182/1200], Temp: 0.9443, Energy: -44.822195+0.001728j
[2025-09-17 20:20:20] [Iter 1234/2250] R3[183/1200], Temp: 0.9437, Energy: -44.830636+0.001259j
[2025-09-17 20:20:32] [Iter 1235/2250] R3[184/1200], Temp: 0.9431, Energy: -44.818910-0.000922j
[2025-09-17 20:20:43] [Iter 1236/2250] R3[185/1200], Temp: 0.9425, Energy: -44.816248+0.001122j
[2025-09-17 20:20:55] [Iter 1237/2250] R3[186/1200], Temp: 0.9419, Energy: -44.813778-0.004261j
[2025-09-17 20:21:06] [Iter 1238/2250] R3[187/1200], Temp: 0.9413, Energy: -44.821428+0.001550j
[2025-09-17 20:21:18] [Iter 1239/2250] R3[188/1200], Temp: 0.9407, Energy: -44.830121-0.004843j
[2025-09-17 20:21:29] [Iter 1240/2250] R3[189/1200], Temp: 0.9400, Energy: -44.823412+0.001054j
[2025-09-17 20:21:41] [Iter 1241/2250] R3[190/1200], Temp: 0.9394, Energy: -44.837102+0.002093j
[2025-09-17 20:21:52] [Iter 1242/2250] R3[191/1200], Temp: 0.9388, Energy: -44.824368-0.002544j
[2025-09-17 20:22:04] [Iter 1243/2250] R3[192/1200], Temp: 0.9382, Energy: -44.821561+0.000799j
[2025-09-17 20:22:16] [Iter 1244/2250] R3[193/1200], Temp: 0.9375, Energy: -44.830640+0.002764j
[2025-09-17 20:22:27] [Iter 1245/2250] R3[194/1200], Temp: 0.9369, Energy: -44.840249+0.001891j
[2025-09-17 20:22:39] [Iter 1246/2250] R3[195/1200], Temp: 0.9362, Energy: -44.811716+0.001324j
[2025-09-17 20:22:50] [Iter 1247/2250] R3[196/1200], Temp: 0.9356, Energy: -44.808597-0.000371j
[2025-09-17 20:23:02] [Iter 1248/2250] R3[197/1200], Temp: 0.9350, Energy: -44.809668+0.001790j
[2025-09-17 20:23:13] [Iter 1249/2250] R3[198/1200], Temp: 0.9343, Energy: -44.818497-0.000622j
[2025-09-17 20:23:25] [Iter 1250/2250] R3[199/1200], Temp: 0.9337, Energy: -44.809739+0.000794j
[2025-09-17 20:23:25] ✓ Checkpoint saved: checkpoint_iter_001250.pkl
[2025-09-17 20:23:37] [Iter 1251/2250] R3[200/1200], Temp: 0.9330, Energy: -44.827041-0.001258j
[2025-09-17 20:23:48] [Iter 1252/2250] R3[201/1200], Temp: 0.9324, Energy: -44.822268-0.000624j
[2025-09-17 20:24:00] [Iter 1253/2250] R3[202/1200], Temp: 0.9317, Energy: -44.817024+0.001101j
[2025-09-17 20:24:11] [Iter 1254/2250] R3[203/1200], Temp: 0.9310, Energy: -44.831216-0.000912j
[2025-09-17 20:24:23] [Iter 1255/2250] R3[204/1200], Temp: 0.9304, Energy: -44.818676+0.001414j
[2025-09-17 20:24:34] [Iter 1256/2250] R3[205/1200], Temp: 0.9297, Energy: -44.814378+0.000828j
[2025-09-17 20:24:46] [Iter 1257/2250] R3[206/1200], Temp: 0.9290, Energy: -44.832524+0.002456j
[2025-09-17 20:24:57] [Iter 1258/2250] R3[207/1200], Temp: 0.9284, Energy: -44.822058+0.003434j
[2025-09-17 20:25:09] [Iter 1259/2250] R3[208/1200], Temp: 0.9277, Energy: -44.821461-0.000051j
[2025-09-17 20:25:20] [Iter 1260/2250] R3[209/1200], Temp: 0.9270, Energy: -44.805613+0.001155j
[2025-09-17 20:25:32] [Iter 1261/2250] R3[210/1200], Temp: 0.9263, Energy: -44.812510-0.000829j
[2025-09-17 20:25:43] [Iter 1262/2250] R3[211/1200], Temp: 0.9256, Energy: -44.811336+0.001531j
[2025-09-17 20:25:55] [Iter 1263/2250] R3[212/1200], Temp: 0.9249, Energy: -44.825850+0.000939j
[2025-09-17 20:26:07] [Iter 1264/2250] R3[213/1200], Temp: 0.9243, Energy: -44.825545-0.000294j
[2025-09-17 20:26:18] [Iter 1265/2250] R3[214/1200], Temp: 0.9236, Energy: -44.829822+0.000541j
[2025-09-17 20:26:30] [Iter 1266/2250] R3[215/1200], Temp: 0.9229, Energy: -44.838344+0.001924j
[2025-09-17 20:26:41] [Iter 1267/2250] R3[216/1200], Temp: 0.9222, Energy: -44.832248+0.000515j
[2025-09-17 20:26:53] [Iter 1268/2250] R3[217/1200], Temp: 0.9215, Energy: -44.802439+0.000282j
[2025-09-17 20:27:04] [Iter 1269/2250] R3[218/1200], Temp: 0.9208, Energy: -44.835328-0.001643j
[2025-09-17 20:27:16] [Iter 1270/2250] R3[219/1200], Temp: 0.9200, Energy: -44.819384-0.004210j
[2025-09-17 20:27:27] [Iter 1271/2250] R3[220/1200], Temp: 0.9193, Energy: -44.822270+0.003575j
[2025-09-17 20:27:39] [Iter 1272/2250] R3[221/1200], Temp: 0.9186, Energy: -44.822048+0.001457j
[2025-09-17 20:27:50] [Iter 1273/2250] R3[222/1200], Temp: 0.9179, Energy: -44.829036+0.001259j
[2025-09-17 20:28:02] [Iter 1274/2250] R3[223/1200], Temp: 0.9172, Energy: -44.825047+0.001600j
[2025-09-17 20:28:14] [Iter 1275/2250] R3[224/1200], Temp: 0.9165, Energy: -44.819928-0.001314j
[2025-09-17 20:28:25] [Iter 1276/2250] R3[225/1200], Temp: 0.9157, Energy: -44.827688+0.000975j
[2025-09-17 20:28:37] [Iter 1277/2250] R3[226/1200], Temp: 0.9150, Energy: -44.818891+0.001312j
[2025-09-17 20:28:48] [Iter 1278/2250] R3[227/1200], Temp: 0.9143, Energy: -44.832743-0.005811j
[2025-09-17 20:29:00] [Iter 1279/2250] R3[228/1200], Temp: 0.9135, Energy: -44.822458-0.002266j
[2025-09-17 20:29:11] [Iter 1280/2250] R3[229/1200], Temp: 0.9128, Energy: -44.821328+0.000264j
[2025-09-17 20:29:23] [Iter 1281/2250] R3[230/1200], Temp: 0.9121, Energy: -44.818369-0.001000j
[2025-09-17 20:29:34] [Iter 1282/2250] R3[231/1200], Temp: 0.9113, Energy: -44.816911-0.002129j
[2025-09-17 20:29:46] [Iter 1283/2250] R3[232/1200], Temp: 0.9106, Energy: -44.815152+0.001520j
[2025-09-17 20:29:57] [Iter 1284/2250] R3[233/1200], Temp: 0.9098, Energy: -44.834496+0.001360j
[2025-09-17 20:30:09] [Iter 1285/2250] R3[234/1200], Temp: 0.9091, Energy: -44.834793+0.000849j
[2025-09-17 20:30:20] [Iter 1286/2250] R3[235/1200], Temp: 0.9083, Energy: -44.815297+0.000089j
[2025-09-17 20:30:32] [Iter 1287/2250] R3[236/1200], Temp: 0.9076, Energy: -44.827613+0.001294j
[2025-09-17 20:30:44] [Iter 1288/2250] R3[237/1200], Temp: 0.9068, Energy: -44.818950+0.001142j
[2025-09-17 20:30:55] [Iter 1289/2250] R3[238/1200], Temp: 0.9060, Energy: -44.811945+0.000692j
[2025-09-17 20:31:07] [Iter 1290/2250] R3[239/1200], Temp: 0.9053, Energy: -44.823758+0.002156j
[2025-09-17 20:31:18] [Iter 1291/2250] R3[240/1200], Temp: 0.9045, Energy: -44.832346+0.001737j
[2025-09-17 20:31:30] [Iter 1292/2250] R3[241/1200], Temp: 0.9037, Energy: -44.826410-0.001059j
[2025-09-17 20:31:41] [Iter 1293/2250] R3[242/1200], Temp: 0.9030, Energy: -44.809926+0.001268j
[2025-09-17 20:31:53] [Iter 1294/2250] R3[243/1200], Temp: 0.9022, Energy: -44.822879+0.000288j
[2025-09-17 20:32:04] [Iter 1295/2250] R3[244/1200], Temp: 0.9014, Energy: -44.823498+0.001587j
[2025-09-17 20:32:16] [Iter 1296/2250] R3[245/1200], Temp: 0.9006, Energy: -44.821087+0.000850j
[2025-09-17 20:32:27] [Iter 1297/2250] R3[246/1200], Temp: 0.8998, Energy: -44.815106-0.000373j
[2025-09-17 20:32:39] [Iter 1298/2250] R3[247/1200], Temp: 0.8991, Energy: -44.834240+0.000024j
[2025-09-17 20:32:50] [Iter 1299/2250] R3[248/1200], Temp: 0.8983, Energy: -44.828108+0.000811j
[2025-09-17 20:33:02] [Iter 1300/2250] R3[249/1200], Temp: 0.8975, Energy: -44.820447+0.003710j
[2025-09-17 20:33:14] [Iter 1301/2250] R3[250/1200], Temp: 0.8967, Energy: -44.805225-0.000163j
[2025-09-17 20:33:25] [Iter 1302/2250] R3[251/1200], Temp: 0.8959, Energy: -44.821933+0.003938j
[2025-09-17 20:33:37] [Iter 1303/2250] R3[252/1200], Temp: 0.8951, Energy: -44.824600-0.002348j
[2025-09-17 20:33:48] [Iter 1304/2250] R3[253/1200], Temp: 0.8943, Energy: -44.830179+0.001976j
[2025-09-17 20:34:00] [Iter 1305/2250] R3[254/1200], Temp: 0.8935, Energy: -44.812547+0.002105j
[2025-09-17 20:34:11] [Iter 1306/2250] R3[255/1200], Temp: 0.8927, Energy: -44.825290-0.002474j
[2025-09-17 20:34:23] [Iter 1307/2250] R3[256/1200], Temp: 0.8918, Energy: -44.827021+0.002968j
[2025-09-17 20:34:34] [Iter 1308/2250] R3[257/1200], Temp: 0.8910, Energy: -44.816095-0.000878j
[2025-09-17 20:34:46] [Iter 1309/2250] R3[258/1200], Temp: 0.8902, Energy: -44.833498-0.003416j
[2025-09-17 20:34:57] [Iter 1310/2250] R3[259/1200], Temp: 0.8894, Energy: -44.819913+0.000547j
[2025-09-17 20:35:09] [Iter 1311/2250] R3[260/1200], Temp: 0.8886, Energy: -44.819056-0.000754j
[2025-09-17 20:35:21] [Iter 1312/2250] R3[261/1200], Temp: 0.8877, Energy: -44.821393+0.000555j
[2025-09-17 20:35:32] [Iter 1313/2250] R3[262/1200], Temp: 0.8869, Energy: -44.835336-0.000360j
[2025-09-17 20:35:44] [Iter 1314/2250] R3[263/1200], Temp: 0.8861, Energy: -44.834905-0.001211j
[2025-09-17 20:35:55] [Iter 1315/2250] R3[264/1200], Temp: 0.8853, Energy: -44.826825-0.000960j
[2025-09-17 20:36:07] [Iter 1316/2250] R3[265/1200], Temp: 0.8844, Energy: -44.834279+0.000398j
[2025-09-17 20:36:18] [Iter 1317/2250] R3[266/1200], Temp: 0.8836, Energy: -44.833167+0.002462j
[2025-09-17 20:36:30] [Iter 1318/2250] R3[267/1200], Temp: 0.8827, Energy: -44.832268+0.000056j
[2025-09-17 20:36:41] [Iter 1319/2250] R3[268/1200], Temp: 0.8819, Energy: -44.828498-0.002970j
[2025-09-17 20:36:53] [Iter 1320/2250] R3[269/1200], Temp: 0.8811, Energy: -44.826128+0.000397j
[2025-09-17 20:37:04] [Iter 1321/2250] R3[270/1200], Temp: 0.8802, Energy: -44.817231-0.002775j
[2025-09-17 20:37:16] [Iter 1322/2250] R3[271/1200], Temp: 0.8794, Energy: -44.813075-0.000816j
[2025-09-17 20:37:27] [Iter 1323/2250] R3[272/1200], Temp: 0.8785, Energy: -44.833568-0.003490j
[2025-09-17 20:37:39] [Iter 1324/2250] R3[273/1200], Temp: 0.8776, Energy: -44.815373-0.002506j
[2025-09-17 20:37:51] [Iter 1325/2250] R3[274/1200], Temp: 0.8768, Energy: -44.832469+0.001937j
[2025-09-17 20:38:02] [Iter 1326/2250] R3[275/1200], Temp: 0.8759, Energy: -44.825109-0.001703j
[2025-09-17 20:38:14] [Iter 1327/2250] R3[276/1200], Temp: 0.8751, Energy: -44.827303+0.000379j
[2025-09-17 20:38:25] [Iter 1328/2250] R3[277/1200], Temp: 0.8742, Energy: -44.827495+0.002422j
[2025-09-17 20:38:37] [Iter 1329/2250] R3[278/1200], Temp: 0.8733, Energy: -44.828419-0.001522j
[2025-09-17 20:38:48] [Iter 1330/2250] R3[279/1200], Temp: 0.8724, Energy: -44.819134+0.000431j
[2025-09-17 20:39:00] [Iter 1331/2250] R3[280/1200], Temp: 0.8716, Energy: -44.829868-0.001766j
[2025-09-17 20:39:11] [Iter 1332/2250] R3[281/1200], Temp: 0.8707, Energy: -44.829445-0.000001j
[2025-09-17 20:39:23] [Iter 1333/2250] R3[282/1200], Temp: 0.8698, Energy: -44.825814-0.000647j
[2025-09-17 20:39:34] [Iter 1334/2250] R3[283/1200], Temp: 0.8689, Energy: -44.820806-0.001057j
[2025-09-17 20:39:46] [Iter 1335/2250] R3[284/1200], Temp: 0.8680, Energy: -44.830627+0.002805j
[2025-09-17 20:39:58] [Iter 1336/2250] R3[285/1200], Temp: 0.8672, Energy: -44.833470+0.001010j
[2025-09-17 20:40:09] [Iter 1337/2250] R3[286/1200], Temp: 0.8663, Energy: -44.821504+0.000969j
[2025-09-17 20:40:21] [Iter 1338/2250] R3[287/1200], Temp: 0.8654, Energy: -44.820675-0.000093j
[2025-09-17 20:40:32] [Iter 1339/2250] R3[288/1200], Temp: 0.8645, Energy: -44.829990-0.000057j
[2025-09-17 20:40:44] [Iter 1340/2250] R3[289/1200], Temp: 0.8636, Energy: -44.828416+0.002292j
[2025-09-17 20:40:55] [Iter 1341/2250] R3[290/1200], Temp: 0.8627, Energy: -44.824278+0.002042j
[2025-09-17 20:41:07] [Iter 1342/2250] R3[291/1200], Temp: 0.8618, Energy: -44.827962+0.001676j
[2025-09-17 20:41:18] [Iter 1343/2250] R3[292/1200], Temp: 0.8609, Energy: -44.826690-0.001952j
[2025-09-17 20:41:30] [Iter 1344/2250] R3[293/1200], Temp: 0.8600, Energy: -44.825410-0.002255j
[2025-09-17 20:41:41] [Iter 1345/2250] R3[294/1200], Temp: 0.8591, Energy: -44.823345-0.002194j
[2025-09-17 20:41:53] [Iter 1346/2250] R3[295/1200], Temp: 0.8582, Energy: -44.826882-0.000076j
[2025-09-17 20:42:04] [Iter 1347/2250] R3[296/1200], Temp: 0.8572, Energy: -44.819578-0.002883j
[2025-09-17 20:42:16] [Iter 1348/2250] R3[297/1200], Temp: 0.8563, Energy: -44.827547-0.003783j
[2025-09-17 20:42:28] [Iter 1349/2250] R3[298/1200], Temp: 0.8554, Energy: -44.821449+0.003822j
[2025-09-17 20:42:39] [Iter 1350/2250] R3[299/1200], Temp: 0.8545, Energy: -44.825097-0.000074j
[2025-09-17 20:42:51] [Iter 1351/2250] R3[300/1200], Temp: 0.8536, Energy: -44.830037+0.001779j
[2025-09-17 20:43:02] [Iter 1352/2250] R3[301/1200], Temp: 0.8526, Energy: -44.829323-0.000091j
[2025-09-17 20:43:14] [Iter 1353/2250] R3[302/1200], Temp: 0.8517, Energy: -44.822541-0.002816j
[2025-09-17 20:43:25] [Iter 1354/2250] R3[303/1200], Temp: 0.8508, Energy: -44.832762+0.001047j
[2025-09-17 20:43:37] [Iter 1355/2250] R3[304/1200], Temp: 0.8498, Energy: -44.813139+0.000849j
[2025-09-17 20:43:48] [Iter 1356/2250] R3[305/1200], Temp: 0.8489, Energy: -44.825431+0.001017j
[2025-09-17 20:44:00] [Iter 1357/2250] R3[306/1200], Temp: 0.8480, Energy: -44.825633+0.000815j
[2025-09-17 20:44:11] [Iter 1358/2250] R3[307/1200], Temp: 0.8470, Energy: -44.817496+0.000081j
[2025-09-17 20:44:23] [Iter 1359/2250] R3[308/1200], Temp: 0.8461, Energy: -44.822573-0.002633j
[2025-09-17 20:44:34] [Iter 1360/2250] R3[309/1200], Temp: 0.8451, Energy: -44.819565+0.000383j
[2025-09-17 20:44:46] [Iter 1361/2250] R3[310/1200], Temp: 0.8442, Energy: -44.833674+0.000554j
[2025-09-17 20:44:58] [Iter 1362/2250] R3[311/1200], Temp: 0.8432, Energy: -44.835063-0.001425j
[2025-09-17 20:45:09] [Iter 1363/2250] R3[312/1200], Temp: 0.8423, Energy: -44.833573-0.002439j
[2025-09-17 20:45:21] [Iter 1364/2250] R3[313/1200], Temp: 0.8413, Energy: -44.827752-0.002137j
[2025-09-17 20:45:32] [Iter 1365/2250] R3[314/1200], Temp: 0.8404, Energy: -44.835839-0.001069j
[2025-09-17 20:45:44] [Iter 1366/2250] R3[315/1200], Temp: 0.8394, Energy: -44.831442-0.000716j
[2025-09-17 20:45:55] [Iter 1367/2250] R3[316/1200], Temp: 0.8384, Energy: -44.811218-0.002305j
[2025-09-17 20:46:07] [Iter 1368/2250] R3[317/1200], Temp: 0.8375, Energy: -44.820535+0.000030j
[2025-09-17 20:46:18] [Iter 1369/2250] R3[318/1200], Temp: 0.8365, Energy: -44.832451+0.002516j
[2025-09-17 20:46:30] [Iter 1370/2250] R3[319/1200], Temp: 0.8355, Energy: -44.826500+0.005027j
[2025-09-17 20:46:41] [Iter 1371/2250] R3[320/1200], Temp: 0.8346, Energy: -44.824404+0.000222j
[2025-09-17 20:46:53] [Iter 1372/2250] R3[321/1200], Temp: 0.8336, Energy: -44.842035-0.002498j
[2025-09-17 20:47:05] [Iter 1373/2250] R3[322/1200], Temp: 0.8326, Energy: -44.833327-0.001617j
[2025-09-17 20:47:16] [Iter 1374/2250] R3[323/1200], Temp: 0.8316, Energy: -44.827111+0.002125j
[2025-09-17 20:47:28] [Iter 1375/2250] R3[324/1200], Temp: 0.8307, Energy: -44.837428+0.001020j
[2025-09-17 20:47:39] [Iter 1376/2250] R3[325/1200], Temp: 0.8297, Energy: -44.824393-0.001358j
[2025-09-17 20:47:51] [Iter 1377/2250] R3[326/1200], Temp: 0.8287, Energy: -44.825639-0.000279j
[2025-09-17 20:48:02] [Iter 1378/2250] R3[327/1200], Temp: 0.8277, Energy: -44.825986-0.001807j
[2025-09-17 20:48:14] [Iter 1379/2250] R3[328/1200], Temp: 0.8267, Energy: -44.835842+0.003147j
[2025-09-17 20:48:25] [Iter 1380/2250] R3[329/1200], Temp: 0.8257, Energy: -44.825354-0.002330j
[2025-09-17 20:48:37] [Iter 1381/2250] R3[330/1200], Temp: 0.8247, Energy: -44.836862-0.000879j
[2025-09-17 20:48:48] [Iter 1382/2250] R3[331/1200], Temp: 0.8237, Energy: -44.842937+0.000832j
[2025-09-17 20:49:00] [Iter 1383/2250] R3[332/1200], Temp: 0.8227, Energy: -44.821695-0.001049j
[2025-09-17 20:49:12] [Iter 1384/2250] R3[333/1200], Temp: 0.8217, Energy: -44.824561+0.000222j
[2025-09-17 20:49:23] [Iter 1385/2250] R3[334/1200], Temp: 0.8207, Energy: -44.830600+0.000121j
[2025-09-17 20:49:35] [Iter 1386/2250] R3[335/1200], Temp: 0.8197, Energy: -44.827310-0.000434j
[2025-09-17 20:49:46] [Iter 1387/2250] R3[336/1200], Temp: 0.8187, Energy: -44.829481+0.003128j
[2025-09-17 20:49:58] [Iter 1388/2250] R3[337/1200], Temp: 0.8177, Energy: -44.821881-0.001787j
[2025-09-17 20:50:09] [Iter 1389/2250] R3[338/1200], Temp: 0.8167, Energy: -44.827085+0.000692j
[2025-09-17 20:50:21] [Iter 1390/2250] R3[339/1200], Temp: 0.8157, Energy: -44.816284-0.002056j
[2025-09-17 20:50:32] [Iter 1391/2250] R3[340/1200], Temp: 0.8147, Energy: -44.829614-0.001704j
[2025-09-17 20:50:44] [Iter 1392/2250] R3[341/1200], Temp: 0.8136, Energy: -44.840971-0.000598j
[2025-09-17 20:50:55] [Iter 1393/2250] R3[342/1200], Temp: 0.8126, Energy: -44.821548+0.000282j
[2025-09-17 20:51:07] [Iter 1394/2250] R3[343/1200], Temp: 0.8116, Energy: -44.826261-0.000388j
[2025-09-17 20:51:19] [Iter 1395/2250] R3[344/1200], Temp: 0.8106, Energy: -44.829519-0.001459j
[2025-09-17 20:51:30] [Iter 1396/2250] R3[345/1200], Temp: 0.8095, Energy: -44.820321+0.003530j
[2025-09-17 20:51:42] [Iter 1397/2250] R3[346/1200], Temp: 0.8085, Energy: -44.816868-0.002643j
[2025-09-17 20:51:53] [Iter 1398/2250] R3[347/1200], Temp: 0.8075, Energy: -44.828786-0.001988j
[2025-09-17 20:52:05] [Iter 1399/2250] R3[348/1200], Temp: 0.8065, Energy: -44.826552+0.000061j
[2025-09-17 20:52:16] [Iter 1400/2250] R3[349/1200], Temp: 0.8054, Energy: -44.828064-0.002264j
[2025-09-17 20:52:28] [Iter 1401/2250] R3[350/1200], Temp: 0.8044, Energy: -44.822969+0.004123j
[2025-09-17 20:52:39] [Iter 1402/2250] R3[351/1200], Temp: 0.8033, Energy: -44.831067-0.001111j
[2025-09-17 20:52:51] [Iter 1403/2250] R3[352/1200], Temp: 0.8023, Energy: -44.821253+0.003985j
[2025-09-17 20:53:02] [Iter 1404/2250] R3[353/1200], Temp: 0.8013, Energy: -44.827458-0.000182j
[2025-09-17 20:53:14] [Iter 1405/2250] R3[354/1200], Temp: 0.8002, Energy: -44.831965-0.001329j
[2025-09-17 20:53:26] [Iter 1406/2250] R3[355/1200], Temp: 0.7992, Energy: -44.836920+0.000590j
[2025-09-17 20:53:37] [Iter 1407/2250] R3[356/1200], Temp: 0.7981, Energy: -44.836751-0.001235j
[2025-09-17 20:53:49] [Iter 1408/2250] R3[357/1200], Temp: 0.7971, Energy: -44.825438-0.000645j
[2025-09-17 20:54:00] [Iter 1409/2250] R3[358/1200], Temp: 0.7960, Energy: -44.840549+0.000343j
[2025-09-17 20:54:12] [Iter 1410/2250] R3[359/1200], Temp: 0.7950, Energy: -44.836849+0.006093j
[2025-09-17 20:54:23] [Iter 1411/2250] R3[360/1200], Temp: 0.7939, Energy: -44.832422-0.001761j
[2025-09-17 20:54:35] [Iter 1412/2250] R3[361/1200], Temp: 0.7928, Energy: -44.819928-0.000445j
[2025-09-17 20:54:46] [Iter 1413/2250] R3[362/1200], Temp: 0.7918, Energy: -44.825369+0.001011j
[2025-09-17 20:55:00] [Iter 1414/2250] R3[363/1200], Temp: 0.7907, Energy: -44.833213+0.001538j
[2025-09-17 20:55:12] [Iter 1415/2250] R3[364/1200], Temp: 0.7896, Energy: -44.834994-0.001018j
[2025-09-17 20:55:23] [Iter 1416/2250] R3[365/1200], Temp: 0.7886, Energy: -44.833813+0.002439j
[2025-09-17 20:55:35] [Iter 1417/2250] R3[366/1200], Temp: 0.7875, Energy: -44.826449+0.002139j
[2025-09-17 20:55:46] [Iter 1418/2250] R3[367/1200], Temp: 0.7864, Energy: -44.832340+0.001921j
[2025-09-17 20:55:58] [Iter 1419/2250] R3[368/1200], Temp: 0.7854, Energy: -44.821945+0.000505j
[2025-09-17 20:56:09] [Iter 1420/2250] R3[369/1200], Temp: 0.7843, Energy: -44.816224-0.000981j
[2025-09-17 20:56:21] [Iter 1421/2250] R3[370/1200], Temp: 0.7832, Energy: -44.837917-0.003472j
[2025-09-17 20:56:32] [Iter 1422/2250] R3[371/1200], Temp: 0.7821, Energy: -44.822683+0.000524j
[2025-09-17 20:56:44] [Iter 1423/2250] R3[372/1200], Temp: 0.7810, Energy: -44.817761-0.000593j
[2025-09-17 20:56:55] [Iter 1424/2250] R3[373/1200], Temp: 0.7800, Energy: -44.828050+0.004826j
[2025-09-17 20:57:07] [Iter 1425/2250] R3[374/1200], Temp: 0.7789, Energy: -44.825342+0.001149j
[2025-09-17 20:57:19] [Iter 1426/2250] R3[375/1200], Temp: 0.7778, Energy: -44.825056-0.001487j
[2025-09-17 20:57:30] [Iter 1427/2250] R3[376/1200], Temp: 0.7767, Energy: -44.812111-0.004739j
[2025-09-17 20:57:42] [Iter 1428/2250] R3[377/1200], Temp: 0.7756, Energy: -44.825287-0.001905j
[2025-09-17 20:57:53] [Iter 1429/2250] R3[378/1200], Temp: 0.7745, Energy: -44.818125+0.000738j
[2025-09-17 20:58:05] [Iter 1430/2250] R3[379/1200], Temp: 0.7734, Energy: -44.825697+0.000478j
[2025-09-17 20:58:16] [Iter 1431/2250] R3[380/1200], Temp: 0.7723, Energy: -44.820338-0.001140j
[2025-09-17 20:58:28] [Iter 1432/2250] R3[381/1200], Temp: 0.7712, Energy: -44.829308+0.001752j
[2025-09-17 20:58:39] [Iter 1433/2250] R3[382/1200], Temp: 0.7701, Energy: -44.821768-0.003052j
[2025-09-17 20:58:51] [Iter 1434/2250] R3[383/1200], Temp: 0.7690, Energy: -44.828868-0.002986j
[2025-09-17 20:59:02] [Iter 1435/2250] R3[384/1200], Temp: 0.7679, Energy: -44.835682+0.000648j
[2025-09-17 20:59:14] [Iter 1436/2250] R3[385/1200], Temp: 0.7668, Energy: -44.838936+0.000882j
[2025-09-17 20:59:25] [Iter 1437/2250] R3[386/1200], Temp: 0.7657, Energy: -44.833792-0.001948j
[2025-09-17 20:59:37] [Iter 1438/2250] R3[387/1200], Temp: 0.7646, Energy: -44.838003+0.001125j
[2025-09-17 20:59:49] [Iter 1439/2250] R3[388/1200], Temp: 0.7635, Energy: -44.839807+0.000559j
[2025-09-17 21:00:00] [Iter 1440/2250] R3[389/1200], Temp: 0.7624, Energy: -44.813100-0.000989j
[2025-09-17 21:00:12] [Iter 1441/2250] R3[390/1200], Temp: 0.7612, Energy: -44.834555-0.001676j
[2025-09-17 21:00:23] [Iter 1442/2250] R3[391/1200], Temp: 0.7601, Energy: -44.830327-0.000570j
[2025-09-17 21:00:35] [Iter 1443/2250] R3[392/1200], Temp: 0.7590, Energy: -44.839200-0.001429j
[2025-09-17 21:00:46] [Iter 1444/2250] R3[393/1200], Temp: 0.7579, Energy: -44.824695+0.000843j
[2025-09-17 21:00:58] [Iter 1445/2250] R3[394/1200], Temp: 0.7568, Energy: -44.826880+0.002223j
[2025-09-17 21:01:09] [Iter 1446/2250] R3[395/1200], Temp: 0.7556, Energy: -44.840428-0.005857j
[2025-09-17 21:01:21] [Iter 1447/2250] R3[396/1200], Temp: 0.7545, Energy: -44.837318+0.001045j
[2025-09-17 21:01:32] [Iter 1448/2250] R3[397/1200], Temp: 0.7534, Energy: -44.833063+0.000703j
[2025-09-17 21:01:44] [Iter 1449/2250] R3[398/1200], Temp: 0.7523, Energy: -44.829803+0.000510j
[2025-09-17 21:01:56] [Iter 1450/2250] R3[399/1200], Temp: 0.7511, Energy: -44.830654-0.001399j
[2025-09-17 21:02:07] [Iter 1451/2250] R3[400/1200], Temp: 0.7500, Energy: -44.825387-0.002004j
[2025-09-17 21:02:19] [Iter 1452/2250] R3[401/1200], Temp: 0.7489, Energy: -44.844245-0.004275j
[2025-09-17 21:02:30] [Iter 1453/2250] R3[402/1200], Temp: 0.7477, Energy: -44.822952-0.002429j
[2025-09-17 21:02:42] [Iter 1454/2250] R3[403/1200], Temp: 0.7466, Energy: -44.832913-0.000859j
[2025-09-17 21:02:53] [Iter 1455/2250] R3[404/1200], Temp: 0.7455, Energy: -44.828923-0.000502j
[2025-09-17 21:03:05] [Iter 1456/2250] R3[405/1200], Temp: 0.7443, Energy: -44.834370-0.000053j
[2025-09-17 21:03:16] [Iter 1457/2250] R3[406/1200], Temp: 0.7432, Energy: -44.830495-0.000481j
[2025-09-17 21:03:28] [Iter 1458/2250] R3[407/1200], Temp: 0.7420, Energy: -44.837109-0.000038j
[2025-09-17 21:03:39] [Iter 1459/2250] R3[408/1200], Temp: 0.7409, Energy: -44.830874+0.000529j
[2025-09-17 21:03:51] [Iter 1460/2250] R3[409/1200], Temp: 0.7397, Energy: -44.839762+0.000582j
[2025-09-17 21:04:02] [Iter 1461/2250] R3[410/1200], Temp: 0.7386, Energy: -44.821289-0.000376j
[2025-09-17 21:04:14] [Iter 1462/2250] R3[411/1200], Temp: 0.7374, Energy: -44.828000-0.002188j
[2025-09-17 21:04:26] [Iter 1463/2250] R3[412/1200], Temp: 0.7363, Energy: -44.831308-0.001183j
[2025-09-17 21:04:37] [Iter 1464/2250] R3[413/1200], Temp: 0.7351, Energy: -44.817264+0.000907j
[2025-09-17 21:04:49] [Iter 1465/2250] R3[414/1200], Temp: 0.7340, Energy: -44.833487-0.000254j
[2025-09-17 21:05:00] [Iter 1466/2250] R3[415/1200], Temp: 0.7328, Energy: -44.810892-0.000631j
[2025-09-17 21:05:12] [Iter 1467/2250] R3[416/1200], Temp: 0.7316, Energy: -44.826435-0.002660j
[2025-09-17 21:05:23] [Iter 1468/2250] R3[417/1200], Temp: 0.7305, Energy: -44.827367+0.002296j
[2025-09-17 21:05:35] [Iter 1469/2250] R3[418/1200], Temp: 0.7293, Energy: -44.836035-0.002091j
[2025-09-17 21:05:46] [Iter 1470/2250] R3[419/1200], Temp: 0.7282, Energy: -44.833551-0.000791j
[2025-09-17 21:05:58] [Iter 1471/2250] R3[420/1200], Temp: 0.7270, Energy: -44.819388-0.002201j
[2025-09-17 21:06:09] [Iter 1472/2250] R3[421/1200], Temp: 0.7258, Energy: -44.834484-0.001767j
[2025-09-17 21:06:21] [Iter 1473/2250] R3[422/1200], Temp: 0.7247, Energy: -44.821072+0.000137j
[2025-09-17 21:06:32] [Iter 1474/2250] R3[423/1200], Temp: 0.7235, Energy: -44.819933-0.003261j
[2025-09-17 21:06:44] [Iter 1475/2250] R3[424/1200], Temp: 0.7223, Energy: -44.821501+0.001447j
[2025-09-17 21:06:56] [Iter 1476/2250] R3[425/1200], Temp: 0.7211, Energy: -44.828123+0.000425j
[2025-09-17 21:07:07] [Iter 1477/2250] R3[426/1200], Temp: 0.7200, Energy: -44.835068+0.000427j
[2025-09-17 21:07:19] [Iter 1478/2250] R3[427/1200], Temp: 0.7188, Energy: -44.835301-0.000559j
[2025-09-17 21:07:30] [Iter 1479/2250] R3[428/1200], Temp: 0.7176, Energy: -44.841189+0.000559j
[2025-09-17 21:07:42] [Iter 1480/2250] R3[429/1200], Temp: 0.7164, Energy: -44.815970+0.002330j
[2025-09-17 21:07:53] [Iter 1481/2250] R3[430/1200], Temp: 0.7153, Energy: -44.834361-0.001878j
[2025-09-17 21:08:05] [Iter 1482/2250] R3[431/1200], Temp: 0.7141, Energy: -44.835337+0.000720j
[2025-09-17 21:08:16] [Iter 1483/2250] R3[432/1200], Temp: 0.7129, Energy: -44.814820-0.000530j
[2025-09-17 21:08:28] [Iter 1484/2250] R3[433/1200], Temp: 0.7117, Energy: -44.836796-0.000942j
[2025-09-17 21:08:39] [Iter 1485/2250] R3[434/1200], Temp: 0.7105, Energy: -44.829478-0.000867j
[2025-09-17 21:08:51] [Iter 1486/2250] R3[435/1200], Temp: 0.7093, Energy: -44.840729+0.001767j
[2025-09-17 21:09:02] [Iter 1487/2250] R3[436/1200], Temp: 0.7081, Energy: -44.833740-0.001608j
[2025-09-17 21:09:14] [Iter 1488/2250] R3[437/1200], Temp: 0.7069, Energy: -44.827947-0.001451j
[2025-09-17 21:09:25] [Iter 1489/2250] R3[438/1200], Temp: 0.7058, Energy: -44.826083+0.000637j
[2025-09-17 21:09:37] [Iter 1490/2250] R3[439/1200], Temp: 0.7046, Energy: -44.822408-0.001773j
[2025-09-17 21:09:49] [Iter 1491/2250] R3[440/1200], Temp: 0.7034, Energy: -44.827573+0.004402j
[2025-09-17 21:10:00] [Iter 1492/2250] R3[441/1200], Temp: 0.7022, Energy: -44.839669+0.002256j
[2025-09-17 21:10:12] [Iter 1493/2250] R3[442/1200], Temp: 0.7010, Energy: -44.840767+0.003286j
[2025-09-17 21:10:23] [Iter 1494/2250] R3[443/1200], Temp: 0.6998, Energy: -44.826762+0.000929j
[2025-09-17 21:10:35] [Iter 1495/2250] R3[444/1200], Temp: 0.6986, Energy: -44.827240+0.002832j
[2025-09-17 21:10:46] [Iter 1496/2250] R3[445/1200], Temp: 0.6974, Energy: -44.828948-0.001130j
[2025-09-17 21:10:58] [Iter 1497/2250] R3[446/1200], Temp: 0.6962, Energy: -44.846996-0.001128j
[2025-09-17 21:11:09] [Iter 1498/2250] R3[447/1200], Temp: 0.6950, Energy: -44.830764-0.001462j
[2025-09-17 21:11:21] [Iter 1499/2250] R3[448/1200], Temp: 0.6938, Energy: -44.839703-0.000681j
[2025-09-17 21:11:32] [Iter 1500/2250] R3[449/1200], Temp: 0.6926, Energy: -44.840464-0.000591j
[2025-09-17 21:11:32] ✓ Checkpoint saved: checkpoint_iter_001500.pkl
[2025-09-17 21:11:44] [Iter 1501/2250] R3[450/1200], Temp: 0.6913, Energy: -44.839591-0.001417j
[2025-09-17 21:11:56] [Iter 1502/2250] R3[451/1200], Temp: 0.6901, Energy: -44.830005-0.000176j
[2025-09-17 21:12:07] [Iter 1503/2250] R3[452/1200], Temp: 0.6889, Energy: -44.831115-0.001749j
[2025-09-17 21:12:19] [Iter 1504/2250] R3[453/1200], Temp: 0.6877, Energy: -44.840273-0.001084j
[2025-09-17 21:12:30] [Iter 1505/2250] R3[454/1200], Temp: 0.6865, Energy: -44.829537-0.002846j
[2025-09-17 21:12:42] [Iter 1506/2250] R3[455/1200], Temp: 0.6853, Energy: -44.830685-0.000134j
[2025-09-17 21:12:53] [Iter 1507/2250] R3[456/1200], Temp: 0.6841, Energy: -44.843602-0.002933j
[2025-09-17 21:13:05] [Iter 1508/2250] R3[457/1200], Temp: 0.6828, Energy: -44.837270+0.001423j
[2025-09-17 21:13:16] [Iter 1509/2250] R3[458/1200], Temp: 0.6816, Energy: -44.843403-0.000910j
[2025-09-17 21:13:28] [Iter 1510/2250] R3[459/1200], Temp: 0.6804, Energy: -44.840692-0.002091j
[2025-09-17 21:13:39] [Iter 1511/2250] R3[460/1200], Temp: 0.6792, Energy: -44.836095+0.002300j
[2025-09-17 21:13:51] [Iter 1512/2250] R3[461/1200], Temp: 0.6780, Energy: -44.831165-0.000068j
[2025-09-17 21:14:03] [Iter 1513/2250] R3[462/1200], Temp: 0.6767, Energy: -44.823975-0.000425j
[2025-09-17 21:14:14] [Iter 1514/2250] R3[463/1200], Temp: 0.6755, Energy: -44.839285-0.001301j
[2025-09-17 21:14:26] [Iter 1515/2250] R3[464/1200], Temp: 0.6743, Energy: -44.823744-0.000926j
[2025-09-17 21:14:37] [Iter 1516/2250] R3[465/1200], Temp: 0.6731, Energy: -44.843138+0.001545j
[2025-09-17 21:14:49] [Iter 1517/2250] R3[466/1200], Temp: 0.6718, Energy: -44.826178-0.000708j
[2025-09-17 21:15:00] [Iter 1518/2250] R3[467/1200], Temp: 0.6706, Energy: -44.829028-0.001978j
[2025-09-17 21:15:12] [Iter 1519/2250] R3[468/1200], Temp: 0.6694, Energy: -44.835903-0.000322j
[2025-09-17 21:15:23] [Iter 1520/2250] R3[469/1200], Temp: 0.6681, Energy: -44.831797+0.000081j
[2025-09-17 21:15:35] [Iter 1521/2250] R3[470/1200], Temp: 0.6669, Energy: -44.827385+0.000792j
[2025-09-17 21:15:46] [Iter 1522/2250] R3[471/1200], Temp: 0.6657, Energy: -44.824060-0.001810j
[2025-09-17 21:15:58] [Iter 1523/2250] R3[472/1200], Temp: 0.6644, Energy: -44.823672+0.001106j
[2025-09-17 21:16:10] [Iter 1524/2250] R3[473/1200], Temp: 0.6632, Energy: -44.853356-0.004994j
[2025-09-17 21:16:21] [Iter 1525/2250] R3[474/1200], Temp: 0.6620, Energy: -44.825740-0.001317j
[2025-09-17 21:16:33] [Iter 1526/2250] R3[475/1200], Temp: 0.6607, Energy: -44.833215+0.002295j
[2025-09-17 21:16:44] [Iter 1527/2250] R3[476/1200], Temp: 0.6595, Energy: -44.834689-0.000527j
[2025-09-17 21:16:56] [Iter 1528/2250] R3[477/1200], Temp: 0.6582, Energy: -44.835612+0.000407j
[2025-09-17 21:17:07] [Iter 1529/2250] R3[478/1200], Temp: 0.6570, Energy: -44.827646+0.000786j
[2025-09-17 21:17:19] [Iter 1530/2250] R3[479/1200], Temp: 0.6558, Energy: -44.849128-0.001150j
[2025-09-17 21:17:30] [Iter 1531/2250] R3[480/1200], Temp: 0.6545, Energy: -44.846196-0.000650j
[2025-09-17 21:17:42] [Iter 1532/2250] R3[481/1200], Temp: 0.6533, Energy: -44.837203-0.000513j
[2025-09-17 21:17:53] [Iter 1533/2250] R3[482/1200], Temp: 0.6520, Energy: -44.849168+0.002356j
[2025-09-17 21:18:05] [Iter 1534/2250] R3[483/1200], Temp: 0.6508, Energy: -44.833306-0.001173j
[2025-09-17 21:18:16] [Iter 1535/2250] R3[484/1200], Temp: 0.6495, Energy: -44.832164+0.000322j
[2025-09-17 21:18:28] [Iter 1536/2250] R3[485/1200], Temp: 0.6483, Energy: -44.826040-0.000104j
[2025-09-17 21:18:40] [Iter 1537/2250] R3[486/1200], Temp: 0.6470, Energy: -44.826114+0.000714j
[2025-09-17 21:18:51] [Iter 1538/2250] R3[487/1200], Temp: 0.6458, Energy: -44.832618-0.001927j
[2025-09-17 21:19:03] [Iter 1539/2250] R3[488/1200], Temp: 0.6445, Energy: -44.830911+0.000189j
[2025-09-17 21:19:14] [Iter 1540/2250] R3[489/1200], Temp: 0.6433, Energy: -44.835870-0.000132j
[2025-09-17 21:19:26] [Iter 1541/2250] R3[490/1200], Temp: 0.6420, Energy: -44.841884+0.001667j
[2025-09-17 21:19:37] [Iter 1542/2250] R3[491/1200], Temp: 0.6408, Energy: -44.837056+0.004283j
[2025-09-17 21:19:49] [Iter 1543/2250] R3[492/1200], Temp: 0.6395, Energy: -44.837234+0.000519j
[2025-09-17 21:20:00] [Iter 1544/2250] R3[493/1200], Temp: 0.6382, Energy: -44.837134+0.000940j
[2025-09-17 21:20:12] [Iter 1545/2250] R3[494/1200], Temp: 0.6370, Energy: -44.849363-0.003126j
[2025-09-17 21:20:23] [Iter 1546/2250] R3[495/1200], Temp: 0.6357, Energy: -44.838098+0.000305j
[2025-09-17 21:20:35] [Iter 1547/2250] R3[496/1200], Temp: 0.6345, Energy: -44.831339-0.001000j
[2025-09-17 21:20:46] [Iter 1548/2250] R3[497/1200], Temp: 0.6332, Energy: -44.823372-0.001741j
[2025-09-17 21:20:58] [Iter 1549/2250] R3[498/1200], Temp: 0.6319, Energy: -44.826071+0.001870j
[2025-09-17 21:21:10] [Iter 1550/2250] R3[499/1200], Temp: 0.6307, Energy: -44.830679-0.001150j
[2025-09-17 21:21:21] [Iter 1551/2250] R3[500/1200], Temp: 0.6294, Energy: -44.834077+0.002173j
[2025-09-17 21:21:33] [Iter 1552/2250] R3[501/1200], Temp: 0.6281, Energy: -44.836848+0.001483j
[2025-09-17 21:21:44] [Iter 1553/2250] R3[502/1200], Temp: 0.6269, Energy: -44.835589-0.001537j
[2025-09-17 21:21:56] [Iter 1554/2250] R3[503/1200], Temp: 0.6256, Energy: -44.827565+0.001276j
[2025-09-17 21:22:07] [Iter 1555/2250] R3[504/1200], Temp: 0.6243, Energy: -44.827393-0.000226j
[2025-09-17 21:22:19] [Iter 1556/2250] R3[505/1200], Temp: 0.6231, Energy: -44.816561-0.001512j
[2025-09-17 21:22:30] [Iter 1557/2250] R3[506/1200], Temp: 0.6218, Energy: -44.825775+0.002746j
[2025-09-17 21:22:42] [Iter 1558/2250] R3[507/1200], Temp: 0.6205, Energy: -44.835492+0.001040j
[2025-09-17 21:22:54] [Iter 1559/2250] R3[508/1200], Temp: 0.6193, Energy: -44.841627-0.000877j
[2025-09-17 21:23:05] [Iter 1560/2250] R3[509/1200], Temp: 0.6180, Energy: -44.842625-0.000425j
[2025-09-17 21:23:17] [Iter 1561/2250] R3[510/1200], Temp: 0.6167, Energy: -44.835035-0.000096j
[2025-09-17 21:23:28] [Iter 1562/2250] R3[511/1200], Temp: 0.6154, Energy: -44.829400+0.000157j
[2025-09-17 21:23:40] [Iter 1563/2250] R3[512/1200], Temp: 0.6142, Energy: -44.830400+0.003071j
[2025-09-17 21:23:51] [Iter 1564/2250] R3[513/1200], Temp: 0.6129, Energy: -44.842340-0.000267j
[2025-09-17 21:24:03] [Iter 1565/2250] R3[514/1200], Temp: 0.6116, Energy: -44.827052+0.000547j
[2025-09-17 21:24:14] [Iter 1566/2250] R3[515/1200], Temp: 0.6103, Energy: -44.844343-0.000115j
[2025-09-17 21:24:26] [Iter 1567/2250] R3[516/1200], Temp: 0.6091, Energy: -44.842642-0.000189j
[2025-09-17 21:24:37] [Iter 1568/2250] R3[517/1200], Temp: 0.6078, Energy: -44.828634+0.000423j
[2025-09-17 21:24:49] [Iter 1569/2250] R3[518/1200], Temp: 0.6065, Energy: -44.832756+0.000562j
[2025-09-17 21:25:00] [Iter 1570/2250] R3[519/1200], Temp: 0.6052, Energy: -44.828214-0.001082j
[2025-09-17 21:25:12] [Iter 1571/2250] R3[520/1200], Temp: 0.6040, Energy: -44.835100-0.001491j
[2025-09-17 21:25:24] [Iter 1572/2250] R3[521/1200], Temp: 0.6027, Energy: -44.832976+0.000217j
[2025-09-17 21:25:35] [Iter 1573/2250] R3[522/1200], Temp: 0.6014, Energy: -44.832613+0.000828j
[2025-09-17 21:25:47] [Iter 1574/2250] R3[523/1200], Temp: 0.6001, Energy: -44.835679+0.000530j
[2025-09-17 21:25:58] [Iter 1575/2250] R3[524/1200], Temp: 0.5988, Energy: -44.836749-0.001350j
[2025-09-17 21:26:10] [Iter 1576/2250] R3[525/1200], Temp: 0.5975, Energy: -44.845136+0.000215j
[2025-09-17 21:26:21] [Iter 1577/2250] R3[526/1200], Temp: 0.5963, Energy: -44.831759+0.003681j
[2025-09-17 21:26:33] [Iter 1578/2250] R3[527/1200], Temp: 0.5950, Energy: -44.834312-0.001382j
[2025-09-17 21:26:44] [Iter 1579/2250] R3[528/1200], Temp: 0.5937, Energy: -44.834512+0.002824j
[2025-09-17 21:26:56] [Iter 1580/2250] R3[529/1200], Temp: 0.5924, Energy: -44.838014+0.002801j
[2025-09-17 21:27:07] [Iter 1581/2250] R3[530/1200], Temp: 0.5911, Energy: -44.837490+0.003016j
[2025-09-17 21:27:19] [Iter 1582/2250] R3[531/1200], Temp: 0.5898, Energy: -44.833004+0.001563j
[2025-09-17 21:27:31] [Iter 1583/2250] R3[532/1200], Temp: 0.5885, Energy: -44.826118-0.000664j
[2025-09-17 21:27:42] [Iter 1584/2250] R3[533/1200], Temp: 0.5873, Energy: -44.829733+0.001631j
[2025-09-17 21:27:54] [Iter 1585/2250] R3[534/1200], Temp: 0.5860, Energy: -44.843775-0.001059j
[2025-09-17 21:28:05] [Iter 1586/2250] R3[535/1200], Temp: 0.5847, Energy: -44.843512+0.002580j
[2025-09-17 21:28:17] [Iter 1587/2250] R3[536/1200], Temp: 0.5834, Energy: -44.839722-0.002338j
[2025-09-17 21:28:28] [Iter 1588/2250] R3[537/1200], Temp: 0.5821, Energy: -44.850548-0.000575j
[2025-09-17 21:28:40] [Iter 1589/2250] R3[538/1200], Temp: 0.5808, Energy: -44.834694+0.000593j
[2025-09-17 21:28:51] [Iter 1590/2250] R3[539/1200], Temp: 0.5795, Energy: -44.834456+0.000049j
[2025-09-17 21:29:03] [Iter 1591/2250] R3[540/1200], Temp: 0.5782, Energy: -44.840331+0.001928j
[2025-09-17 21:29:14] [Iter 1592/2250] R3[541/1200], Temp: 0.5769, Energy: -44.850178-0.000379j
[2025-09-17 21:29:26] [Iter 1593/2250] R3[542/1200], Temp: 0.5756, Energy: -44.831824-0.002064j
[2025-09-17 21:29:37] [Iter 1594/2250] R3[543/1200], Temp: 0.5743, Energy: -44.826779-0.000204j
[2025-09-17 21:29:49] [Iter 1595/2250] R3[544/1200], Temp: 0.5730, Energy: -44.840843-0.001295j
[2025-09-17 21:30:01] [Iter 1596/2250] R3[545/1200], Temp: 0.5717, Energy: -44.828754-0.001625j
[2025-09-17 21:30:12] [Iter 1597/2250] R3[546/1200], Temp: 0.5705, Energy: -44.833551-0.002437j
[2025-09-17 21:30:24] [Iter 1598/2250] R3[547/1200], Temp: 0.5692, Energy: -44.836952-0.000502j
[2025-09-17 21:30:35] [Iter 1599/2250] R3[548/1200], Temp: 0.5679, Energy: -44.837325-0.003005j
[2025-09-17 21:30:47] [Iter 1600/2250] R3[549/1200], Temp: 0.5666, Energy: -44.829636+0.001183j
[2025-09-17 21:30:58] [Iter 1601/2250] R3[550/1200], Temp: 0.5653, Energy: -44.829549-0.000170j
[2025-09-17 21:31:10] [Iter 1602/2250] R3[551/1200], Temp: 0.5640, Energy: -44.830397+0.001310j
[2025-09-17 21:31:21] [Iter 1603/2250] R3[552/1200], Temp: 0.5627, Energy: -44.835017+0.000520j
[2025-09-17 21:31:33] [Iter 1604/2250] R3[553/1200], Temp: 0.5614, Energy: -44.831132-0.003121j
[2025-09-17 21:31:44] [Iter 1605/2250] R3[554/1200], Temp: 0.5601, Energy: -44.836321-0.001500j
[2025-09-17 21:31:56] [Iter 1606/2250] R3[555/1200], Temp: 0.5588, Energy: -44.828494-0.000395j
[2025-09-17 21:32:07] [Iter 1607/2250] R3[556/1200], Temp: 0.5575, Energy: -44.836223+0.000422j
[2025-09-17 21:32:19] [Iter 1608/2250] R3[557/1200], Temp: 0.5562, Energy: -44.843974-0.002132j
[2025-09-17 21:32:31] [Iter 1609/2250] R3[558/1200], Temp: 0.5549, Energy: -44.836428+0.001403j
[2025-09-17 21:32:42] [Iter 1610/2250] R3[559/1200], Temp: 0.5536, Energy: -44.834095+0.000607j
[2025-09-17 21:32:54] [Iter 1611/2250] R3[560/1200], Temp: 0.5523, Energy: -44.826713-0.002922j
[2025-09-17 21:33:05] [Iter 1612/2250] R3[561/1200], Temp: 0.5510, Energy: -44.836216+0.000597j
[2025-09-17 21:33:17] [Iter 1613/2250] R3[562/1200], Temp: 0.5497, Energy: -44.844452+0.001871j
[2025-09-17 21:33:28] [Iter 1614/2250] R3[563/1200], Temp: 0.5484, Energy: -44.826117+0.000628j
[2025-09-17 21:33:40] [Iter 1615/2250] R3[564/1200], Temp: 0.5471, Energy: -44.823981+0.001951j
[2025-09-17 21:33:51] [Iter 1616/2250] R3[565/1200], Temp: 0.5458, Energy: -44.846571-0.002403j
[2025-09-17 21:34:03] [Iter 1617/2250] R3[566/1200], Temp: 0.5444, Energy: -44.835346-0.000096j
[2025-09-17 21:34:14] [Iter 1618/2250] R3[567/1200], Temp: 0.5431, Energy: -44.833744-0.001543j
[2025-09-17 21:34:26] [Iter 1619/2250] R3[568/1200], Temp: 0.5418, Energy: -44.827896+0.002672j
[2025-09-17 21:34:38] [Iter 1620/2250] R3[569/1200], Temp: 0.5405, Energy: -44.836178-0.001623j
[2025-09-17 21:34:49] [Iter 1621/2250] R3[570/1200], Temp: 0.5392, Energy: -44.835158-0.001014j
[2025-09-17 21:35:01] [Iter 1622/2250] R3[571/1200], Temp: 0.5379, Energy: -44.834832+0.001415j
[2025-09-17 21:35:12] [Iter 1623/2250] R3[572/1200], Temp: 0.5366, Energy: -44.824031+0.000136j
[2025-09-17 21:35:24] [Iter 1624/2250] R3[573/1200], Temp: 0.5353, Energy: -44.838468+0.001704j
[2025-09-17 21:35:35] [Iter 1625/2250] R3[574/1200], Temp: 0.5340, Energy: -44.834621-0.000437j
[2025-09-17 21:35:47] [Iter 1626/2250] R3[575/1200], Temp: 0.5327, Energy: -44.852465-0.000861j
[2025-09-17 21:35:58] [Iter 1627/2250] R3[576/1200], Temp: 0.5314, Energy: -44.834247+0.000978j
[2025-09-17 21:36:10] [Iter 1628/2250] R3[577/1200], Temp: 0.5301, Energy: -44.832250-0.000258j
[2025-09-17 21:36:21] [Iter 1629/2250] R3[578/1200], Temp: 0.5288, Energy: -44.838082+0.001640j
[2025-09-17 21:36:33] [Iter 1630/2250] R3[579/1200], Temp: 0.5275, Energy: -44.826033-0.003554j
[2025-09-17 21:36:44] [Iter 1631/2250] R3[580/1200], Temp: 0.5262, Energy: -44.839653-0.002881j
[2025-09-17 21:36:56] [Iter 1632/2250] R3[581/1200], Temp: 0.5249, Energy: -44.841961-0.001615j
[2025-09-17 21:37:08] [Iter 1633/2250] R3[582/1200], Temp: 0.5236, Energy: -44.844930-0.000347j
[2025-09-17 21:37:19] [Iter 1634/2250] R3[583/1200], Temp: 0.5222, Energy: -44.836457+0.001764j
[2025-09-17 21:37:31] [Iter 1635/2250] R3[584/1200], Temp: 0.5209, Energy: -44.831824-0.000469j
[2025-09-17 21:37:42] [Iter 1636/2250] R3[585/1200], Temp: 0.5196, Energy: -44.822904+0.003574j
[2025-09-17 21:37:54] [Iter 1637/2250] R3[586/1200], Temp: 0.5183, Energy: -44.841632+0.001340j
[2025-09-17 21:38:05] [Iter 1638/2250] R3[587/1200], Temp: 0.5170, Energy: -44.832546+0.000331j
[2025-09-17 21:38:17] [Iter 1639/2250] R3[588/1200], Temp: 0.5157, Energy: -44.826301-0.000895j
[2025-09-17 21:38:28] [Iter 1640/2250] R3[589/1200], Temp: 0.5144, Energy: -44.835521+0.001189j
[2025-09-17 21:38:40] [Iter 1641/2250] R3[590/1200], Temp: 0.5131, Energy: -44.835551-0.000386j
[2025-09-17 21:38:51] [Iter 1642/2250] R3[591/1200], Temp: 0.5118, Energy: -44.838275+0.001960j
[2025-09-17 21:39:03] [Iter 1643/2250] R3[592/1200], Temp: 0.5105, Energy: -44.842857+0.000673j
[2025-09-17 21:39:15] [Iter 1644/2250] R3[593/1200], Temp: 0.5092, Energy: -44.834015-0.002063j
[2025-09-17 21:39:26] [Iter 1645/2250] R3[594/1200], Temp: 0.5079, Energy: -44.837840-0.000443j
[2025-09-17 21:39:38] [Iter 1646/2250] R3[595/1200], Temp: 0.5065, Energy: -44.830510-0.000306j
[2025-09-17 21:39:49] [Iter 1647/2250] R3[596/1200], Temp: 0.5052, Energy: -44.835611-0.003131j
[2025-09-17 21:40:01] [Iter 1648/2250] R3[597/1200], Temp: 0.5039, Energy: -44.835278+0.002463j
[2025-09-17 21:40:12] [Iter 1649/2250] R3[598/1200], Temp: 0.5026, Energy: -44.836952+0.000135j
[2025-09-17 21:40:24] [Iter 1650/2250] R3[599/1200], Temp: 0.5013, Energy: -44.847939+0.000773j
[2025-09-17 21:40:35] [Iter 1651/2250] R3[600/1200], Temp: 0.5000, Energy: -44.832782+0.000715j
[2025-09-17 21:40:47] [Iter 1652/2250] R3[601/1200], Temp: 0.4987, Energy: -44.843848+0.001023j
[2025-09-17 21:40:58] [Iter 1653/2250] R3[602/1200], Temp: 0.4974, Energy: -44.839404+0.000991j
[2025-09-17 21:41:10] [Iter 1654/2250] R3[603/1200], Temp: 0.4961, Energy: -44.839379-0.000342j
[2025-09-17 21:41:22] [Iter 1655/2250] R3[604/1200], Temp: 0.4948, Energy: -44.837528+0.000557j
[2025-09-17 21:41:33] [Iter 1656/2250] R3[605/1200], Temp: 0.4935, Energy: -44.845326-0.001008j
[2025-09-17 21:41:45] [Iter 1657/2250] R3[606/1200], Temp: 0.4921, Energy: -44.832205+0.002084j
[2025-09-17 21:41:56] [Iter 1658/2250] R3[607/1200], Temp: 0.4908, Energy: -44.835845-0.000969j
[2025-09-17 21:42:08] [Iter 1659/2250] R3[608/1200], Temp: 0.4895, Energy: -44.825362-0.002897j
[2025-09-17 21:42:19] [Iter 1660/2250] R3[609/1200], Temp: 0.4882, Energy: -44.838884+0.002286j
[2025-09-17 21:42:31] [Iter 1661/2250] R3[610/1200], Temp: 0.4869, Energy: -44.837182+0.002431j
[2025-09-17 21:42:42] [Iter 1662/2250] R3[611/1200], Temp: 0.4856, Energy: -44.836132+0.000713j
[2025-09-17 21:42:54] [Iter 1663/2250] R3[612/1200], Temp: 0.4843, Energy: -44.838886+0.000662j
[2025-09-17 21:43:05] [Iter 1664/2250] R3[613/1200], Temp: 0.4830, Energy: -44.823901+0.001558j
[2025-09-17 21:43:17] [Iter 1665/2250] R3[614/1200], Temp: 0.4817, Energy: -44.844907-0.000598j
[2025-09-17 21:43:28] [Iter 1666/2250] R3[615/1200], Temp: 0.4804, Energy: -44.845048+0.000834j
[2025-09-17 21:43:40] [Iter 1667/2250] R3[616/1200], Temp: 0.4791, Energy: -44.838537+0.000284j
[2025-09-17 21:43:52] [Iter 1668/2250] R3[617/1200], Temp: 0.4778, Energy: -44.837477-0.000462j
[2025-09-17 21:44:03] [Iter 1669/2250] R3[618/1200], Temp: 0.4764, Energy: -44.839498+0.000632j
[2025-09-17 21:44:15] [Iter 1670/2250] R3[619/1200], Temp: 0.4751, Energy: -44.827837-0.002968j
[2025-09-17 21:44:26] [Iter 1671/2250] R3[620/1200], Temp: 0.4738, Energy: -44.840646-0.002479j
[2025-09-17 21:44:38] [Iter 1672/2250] R3[621/1200], Temp: 0.4725, Energy: -44.834894+0.002923j
[2025-09-17 21:44:49] [Iter 1673/2250] R3[622/1200], Temp: 0.4712, Energy: -44.838243-0.001159j
[2025-09-17 21:45:01] [Iter 1674/2250] R3[623/1200], Temp: 0.4699, Energy: -44.837841+0.000232j
[2025-09-17 21:45:12] [Iter 1675/2250] R3[624/1200], Temp: 0.4686, Energy: -44.837748+0.000613j
[2025-09-17 21:45:24] [Iter 1676/2250] R3[625/1200], Temp: 0.4673, Energy: -44.824655-0.000758j
[2025-09-17 21:45:35] [Iter 1677/2250] R3[626/1200], Temp: 0.4660, Energy: -44.835599-0.000469j
[2025-09-17 21:45:47] [Iter 1678/2250] R3[627/1200], Temp: 0.4647, Energy: -44.838983-0.000230j
[2025-09-17 21:45:59] [Iter 1679/2250] R3[628/1200], Temp: 0.4634, Energy: -44.835108-0.000688j
[2025-09-17 21:46:10] [Iter 1680/2250] R3[629/1200], Temp: 0.4621, Energy: -44.846437-0.000941j
[2025-09-17 21:46:22] [Iter 1681/2250] R3[630/1200], Temp: 0.4608, Energy: -44.841405+0.001400j
[2025-09-17 21:46:33] [Iter 1682/2250] R3[631/1200], Temp: 0.4595, Energy: -44.841307+0.001651j
[2025-09-17 21:46:45] [Iter 1683/2250] R3[632/1200], Temp: 0.4582, Energy: -44.851831+0.000540j
[2025-09-17 21:46:56] [Iter 1684/2250] R3[633/1200], Temp: 0.4569, Energy: -44.842967+0.003383j
[2025-09-17 21:47:08] [Iter 1685/2250] R3[634/1200], Temp: 0.4556, Energy: -44.830744+0.000492j
[2025-09-17 21:47:19] [Iter 1686/2250] R3[635/1200], Temp: 0.4542, Energy: -44.836475-0.002076j
[2025-09-17 21:47:31] [Iter 1687/2250] R3[636/1200], Temp: 0.4529, Energy: -44.846669-0.001370j
[2025-09-17 21:47:42] [Iter 1688/2250] R3[637/1200], Temp: 0.4516, Energy: -44.840265+0.001605j
[2025-09-17 21:47:54] [Iter 1689/2250] R3[638/1200], Temp: 0.4503, Energy: -44.840861-0.001439j
[2025-09-17 21:48:06] [Iter 1690/2250] R3[639/1200], Temp: 0.4490, Energy: -44.850390+0.001299j
[2025-09-17 21:48:17] [Iter 1691/2250] R3[640/1200], Temp: 0.4477, Energy: -44.826395-0.001526j
[2025-09-17 21:48:29] [Iter 1692/2250] R3[641/1200], Temp: 0.4464, Energy: -44.836885+0.001176j
[2025-09-17 21:48:40] [Iter 1693/2250] R3[642/1200], Temp: 0.4451, Energy: -44.843259+0.002067j
[2025-09-17 21:48:52] [Iter 1694/2250] R3[643/1200], Temp: 0.4438, Energy: -44.838905+0.000223j
[2025-09-17 21:49:03] [Iter 1695/2250] R3[644/1200], Temp: 0.4425, Energy: -44.847236-0.000691j
[2025-09-17 21:49:15] [Iter 1696/2250] R3[645/1200], Temp: 0.4412, Energy: -44.832075-0.002045j
[2025-09-17 21:49:26] [Iter 1697/2250] R3[646/1200], Temp: 0.4399, Energy: -44.843822-0.001065j
[2025-09-17 21:49:38] [Iter 1698/2250] R3[647/1200], Temp: 0.4386, Energy: -44.840007+0.003815j
[2025-09-17 21:49:49] [Iter 1699/2250] R3[648/1200], Temp: 0.4373, Energy: -44.838604+0.001894j
[2025-09-17 21:50:01] [Iter 1700/2250] R3[649/1200], Temp: 0.4360, Energy: -44.844489-0.000154j
[2025-09-17 21:50:12] [Iter 1701/2250] R3[650/1200], Temp: 0.4347, Energy: -44.839907-0.000926j
[2025-09-17 21:50:24] [Iter 1702/2250] R3[651/1200], Temp: 0.4334, Energy: -44.840499+0.002656j
[2025-09-17 21:50:36] [Iter 1703/2250] R3[652/1200], Temp: 0.4321, Energy: -44.841119+0.000955j
[2025-09-17 21:50:47] [Iter 1704/2250] R3[653/1200], Temp: 0.4308, Energy: -44.839305+0.002056j
[2025-09-17 21:50:59] [Iter 1705/2250] R3[654/1200], Temp: 0.4295, Energy: -44.845053-0.000086j
[2025-09-17 21:51:10] [Iter 1706/2250] R3[655/1200], Temp: 0.4283, Energy: -44.836058-0.003453j
[2025-09-17 21:51:22] [Iter 1707/2250] R3[656/1200], Temp: 0.4270, Energy: -44.838080-0.002168j
[2025-09-17 21:51:33] [Iter 1708/2250] R3[657/1200], Temp: 0.4257, Energy: -44.837074+0.002183j
[2025-09-17 21:51:45] [Iter 1709/2250] R3[658/1200], Temp: 0.4244, Energy: -44.841967+0.001850j
[2025-09-17 21:51:56] [Iter 1710/2250] R3[659/1200], Temp: 0.4231, Energy: -44.845579-0.002041j
[2025-09-17 21:52:08] [Iter 1711/2250] R3[660/1200], Temp: 0.4218, Energy: -44.845312-0.001096j
[2025-09-17 21:52:19] [Iter 1712/2250] R3[661/1200], Temp: 0.4205, Energy: -44.834631-0.001166j
[2025-09-17 21:52:31] [Iter 1713/2250] R3[662/1200], Temp: 0.4192, Energy: -44.828603-0.001876j
[2025-09-17 21:52:42] [Iter 1714/2250] R3[663/1200], Temp: 0.4179, Energy: -44.842985+0.001898j
[2025-09-17 21:52:54] [Iter 1715/2250] R3[664/1200], Temp: 0.4166, Energy: -44.844306+0.001782j
[2025-09-17 21:53:06] [Iter 1716/2250] R3[665/1200], Temp: 0.4153, Energy: -44.853770-0.002837j
[2025-09-17 21:53:17] [Iter 1717/2250] R3[666/1200], Temp: 0.4140, Energy: -44.834607+0.001126j
[2025-09-17 21:53:29] [Iter 1718/2250] R3[667/1200], Temp: 0.4127, Energy: -44.828238+0.000742j
[2025-09-17 21:53:40] [Iter 1719/2250] R3[668/1200], Temp: 0.4115, Energy: -44.835118-0.000428j
[2025-09-17 21:53:52] [Iter 1720/2250] R3[669/1200], Temp: 0.4102, Energy: -44.833004+0.000629j
[2025-09-17 21:54:03] [Iter 1721/2250] R3[670/1200], Temp: 0.4089, Energy: -44.832103+0.002273j
[2025-09-17 21:54:15] [Iter 1722/2250] R3[671/1200], Temp: 0.4076, Energy: -44.841320+0.001327j
[2025-09-17 21:54:26] [Iter 1723/2250] R3[672/1200], Temp: 0.4063, Energy: -44.837947+0.000826j
[2025-09-17 21:54:38] [Iter 1724/2250] R3[673/1200], Temp: 0.4050, Energy: -44.843587-0.000312j
[2025-09-17 21:54:49] [Iter 1725/2250] R3[674/1200], Temp: 0.4037, Energy: -44.821998-0.001062j
[2025-09-17 21:55:01] [Iter 1726/2250] R3[675/1200], Temp: 0.4025, Energy: -44.840312-0.000229j
[2025-09-17 21:55:12] [Iter 1727/2250] R3[676/1200], Temp: 0.4012, Energy: -44.842025+0.000351j
[2025-09-17 21:55:24] [Iter 1728/2250] R3[677/1200], Temp: 0.3999, Energy: -44.849507-0.000014j
[2025-09-17 21:55:36] [Iter 1729/2250] R3[678/1200], Temp: 0.3986, Energy: -44.845340-0.000674j
[2025-09-17 21:55:50] [Iter 1730/2250] R3[679/1200], Temp: 0.3973, Energy: -44.839346-0.002248j
[2025-09-17 21:56:01] [Iter 1731/2250] R3[680/1200], Temp: 0.3960, Energy: -44.845047-0.002216j
[2025-09-17 21:56:13] [Iter 1732/2250] R3[681/1200], Temp: 0.3948, Energy: -44.846578+0.000688j
[2025-09-17 21:56:24] [Iter 1733/2250] R3[682/1200], Temp: 0.3935, Energy: -44.841236+0.001416j
[2025-09-17 21:56:36] [Iter 1734/2250] R3[683/1200], Temp: 0.3922, Energy: -44.845845-0.000881j
[2025-09-17 21:56:47] [Iter 1735/2250] R3[684/1200], Temp: 0.3909, Energy: -44.853589-0.000602j
[2025-09-17 21:56:59] [Iter 1736/2250] R3[685/1200], Temp: 0.3897, Energy: -44.844978+0.002031j
[2025-09-17 21:57:11] [Iter 1737/2250] R3[686/1200], Temp: 0.3884, Energy: -44.843769-0.000373j
[2025-09-17 21:57:22] [Iter 1738/2250] R3[687/1200], Temp: 0.3871, Energy: -44.840938+0.000056j
[2025-09-17 21:57:34] [Iter 1739/2250] R3[688/1200], Temp: 0.3858, Energy: -44.837023+0.000592j
[2025-09-17 21:57:45] [Iter 1740/2250] R3[689/1200], Temp: 0.3846, Energy: -44.840024+0.000837j
[2025-09-17 21:57:57] [Iter 1741/2250] R3[690/1200], Temp: 0.3833, Energy: -44.847661-0.002679j
[2025-09-17 21:58:08] [Iter 1742/2250] R3[691/1200], Temp: 0.3820, Energy: -44.841765+0.003138j
[2025-09-17 21:58:20] [Iter 1743/2250] R3[692/1200], Temp: 0.3807, Energy: -44.829651-0.001274j
[2025-09-17 21:58:31] [Iter 1744/2250] R3[693/1200], Temp: 0.3795, Energy: -44.831852+0.000303j
[2025-09-17 21:58:43] [Iter 1745/2250] R3[694/1200], Temp: 0.3782, Energy: -44.850730+0.001786j
[2025-09-17 21:58:54] [Iter 1746/2250] R3[695/1200], Temp: 0.3769, Energy: -44.835582-0.000047j
[2025-09-17 21:59:06] [Iter 1747/2250] R3[696/1200], Temp: 0.3757, Energy: -44.843907-0.000885j
[2025-09-17 21:59:18] [Iter 1748/2250] R3[697/1200], Temp: 0.3744, Energy: -44.840257-0.001718j
[2025-09-17 21:59:29] [Iter 1749/2250] R3[698/1200], Temp: 0.3731, Energy: -44.847373+0.000196j
[2025-09-17 21:59:41] [Iter 1750/2250] R3[699/1200], Temp: 0.3719, Energy: -44.846116+0.001517j
[2025-09-17 21:59:41] ✓ Checkpoint saved: checkpoint_iter_001750.pkl
[2025-09-17 21:59:52] [Iter 1751/2250] R3[700/1200], Temp: 0.3706, Energy: -44.843294-0.000940j
[2025-09-17 22:00:04] [Iter 1752/2250] R3[701/1200], Temp: 0.3693, Energy: -44.848751-0.000764j
[2025-09-17 22:00:15] [Iter 1753/2250] R3[702/1200], Temp: 0.3681, Energy: -44.838041+0.000224j
[2025-09-17 22:00:27] [Iter 1754/2250] R3[703/1200], Temp: 0.3668, Energy: -44.849094-0.001782j
[2025-09-17 22:00:38] [Iter 1755/2250] R3[704/1200], Temp: 0.3655, Energy: -44.838022-0.000555j
[2025-09-17 22:00:50] [Iter 1756/2250] R3[705/1200], Temp: 0.3643, Energy: -44.859197+0.002453j
[2025-09-17 22:01:01] [Iter 1757/2250] R3[706/1200], Temp: 0.3630, Energy: -44.831023-0.002675j
[2025-09-17 22:01:13] [Iter 1758/2250] R3[707/1200], Temp: 0.3618, Energy: -44.827352+0.000305j
[2025-09-17 22:01:25] [Iter 1759/2250] R3[708/1200], Temp: 0.3605, Energy: -44.839737-0.002879j
[2025-09-17 22:01:36] [Iter 1760/2250] R3[709/1200], Temp: 0.3592, Energy: -44.835407-0.000769j
[2025-09-17 22:01:48] [Iter 1761/2250] R3[710/1200], Temp: 0.3580, Energy: -44.841428-0.002934j
[2025-09-17 22:01:59] [Iter 1762/2250] R3[711/1200], Temp: 0.3567, Energy: -44.843753+0.000396j
[2025-09-17 22:02:11] [Iter 1763/2250] R3[712/1200], Temp: 0.3555, Energy: -44.848906+0.000853j
[2025-09-17 22:02:22] [Iter 1764/2250] R3[713/1200], Temp: 0.3542, Energy: -44.836505-0.000400j
[2025-09-17 22:02:34] [Iter 1765/2250] R3[714/1200], Temp: 0.3530, Energy: -44.839535+0.000000j
[2025-09-17 22:02:45] [Iter 1766/2250] R3[715/1200], Temp: 0.3517, Energy: -44.838165-0.000506j
[2025-09-17 22:02:57] [Iter 1767/2250] R3[716/1200], Temp: 0.3505, Energy: -44.850546-0.000895j
[2025-09-17 22:03:08] [Iter 1768/2250] R3[717/1200], Temp: 0.3492, Energy: -44.850449+0.000741j
[2025-09-17 22:03:20] [Iter 1769/2250] R3[718/1200], Temp: 0.3480, Energy: -44.847226-0.001829j
[2025-09-17 22:03:32] [Iter 1770/2250] R3[719/1200], Temp: 0.3467, Energy: -44.852286+0.000075j
[2025-09-17 22:03:43] [Iter 1771/2250] R3[720/1200], Temp: 0.3455, Energy: -44.839248+0.001178j
[2025-09-17 22:03:55] [Iter 1772/2250] R3[721/1200], Temp: 0.3442, Energy: -44.852614-0.000478j
[2025-09-17 22:04:06] [Iter 1773/2250] R3[722/1200], Temp: 0.3430, Energy: -44.836011-0.000268j
[2025-09-17 22:04:18] [Iter 1774/2250] R3[723/1200], Temp: 0.3418, Energy: -44.840841+0.000204j
[2025-09-17 22:04:29] [Iter 1775/2250] R3[724/1200], Temp: 0.3405, Energy: -44.822851-0.000897j
[2025-09-17 22:04:41] [Iter 1776/2250] R3[725/1200], Temp: 0.3393, Energy: -44.834424+0.001190j
[2025-09-17 22:04:52] [Iter 1777/2250] R3[726/1200], Temp: 0.3380, Energy: -44.839601+0.001892j
[2025-09-17 22:05:04] [Iter 1778/2250] R3[727/1200], Temp: 0.3368, Energy: -44.850507+0.001880j
[2025-09-17 22:05:15] [Iter 1779/2250] R3[728/1200], Temp: 0.3356, Energy: -44.839286+0.001242j
[2025-09-17 22:05:27] [Iter 1780/2250] R3[729/1200], Temp: 0.3343, Energy: -44.841569+0.004210j
[2025-09-17 22:05:39] [Iter 1781/2250] R3[730/1200], Temp: 0.3331, Energy: -44.839879+0.001895j
[2025-09-17 22:05:50] [Iter 1782/2250] R3[731/1200], Temp: 0.3319, Energy: -44.857968+0.002225j
[2025-09-17 22:06:02] [Iter 1783/2250] R3[732/1200], Temp: 0.3306, Energy: -44.831797-0.000458j
[2025-09-17 22:06:13] [Iter 1784/2250] R3[733/1200], Temp: 0.3294, Energy: -44.835536+0.000240j
[2025-09-17 22:06:25] [Iter 1785/2250] R3[734/1200], Temp: 0.3282, Energy: -44.844546+0.001192j
[2025-09-17 22:06:36] [Iter 1786/2250] R3[735/1200], Temp: 0.3269, Energy: -44.840729+0.001939j
[2025-09-17 22:06:48] [Iter 1787/2250] R3[736/1200], Temp: 0.3257, Energy: -44.853155-0.002387j
[2025-09-17 22:06:59] [Iter 1788/2250] R3[737/1200], Temp: 0.3245, Energy: -44.842016-0.004961j
[2025-09-17 22:07:11] [Iter 1789/2250] R3[738/1200], Temp: 0.3233, Energy: -44.837285+0.002754j
[2025-09-17 22:07:22] [Iter 1790/2250] R3[739/1200], Temp: 0.3220, Energy: -44.844955+0.000893j
[2025-09-17 22:07:34] [Iter 1791/2250] R3[740/1200], Temp: 0.3208, Energy: -44.844677-0.002096j
[2025-09-17 22:07:45] [Iter 1792/2250] R3[741/1200], Temp: 0.3196, Energy: -44.832662+0.000092j
[2025-09-17 22:07:57] [Iter 1793/2250] R3[742/1200], Temp: 0.3184, Energy: -44.843669+0.001274j
[2025-09-17 22:08:09] [Iter 1794/2250] R3[743/1200], Temp: 0.3172, Energy: -44.849645+0.002108j
[2025-09-17 22:08:20] [Iter 1795/2250] R3[744/1200], Temp: 0.3159, Energy: -44.841106+0.000031j
[2025-09-17 22:08:32] [Iter 1796/2250] R3[745/1200], Temp: 0.3147, Energy: -44.849284+0.001098j
[2025-09-17 22:08:43] [Iter 1797/2250] R3[746/1200], Temp: 0.3135, Energy: -44.833297+0.004134j
[2025-09-17 22:08:55] [Iter 1798/2250] R3[747/1200], Temp: 0.3123, Energy: -44.842166-0.002189j
[2025-09-17 22:09:06] [Iter 1799/2250] R3[748/1200], Temp: 0.3111, Energy: -44.843331-0.000551j
[2025-09-17 22:09:18] [Iter 1800/2250] R3[749/1200], Temp: 0.3099, Energy: -44.841451+0.001329j
[2025-09-17 22:09:29] [Iter 1801/2250] R3[750/1200], Temp: 0.3087, Energy: -44.840615-0.000980j
[2025-09-17 22:09:41] [Iter 1802/2250] R3[751/1200], Temp: 0.3074, Energy: -44.849726-0.002477j
[2025-09-17 22:09:52] [Iter 1803/2250] R3[752/1200], Temp: 0.3062, Energy: -44.847346-0.003431j
[2025-09-17 22:10:04] [Iter 1804/2250] R3[753/1200], Temp: 0.3050, Energy: -44.835485-0.003505j
[2025-09-17 22:10:16] [Iter 1805/2250] R3[754/1200], Temp: 0.3038, Energy: -44.837954+0.000331j
[2025-09-17 22:10:27] [Iter 1806/2250] R3[755/1200], Temp: 0.3026, Energy: -44.845785+0.000378j
[2025-09-17 22:10:39] [Iter 1807/2250] R3[756/1200], Temp: 0.3014, Energy: -44.842137-0.000876j
[2025-09-17 22:10:50] [Iter 1808/2250] R3[757/1200], Temp: 0.3002, Energy: -44.842834+0.000799j
[2025-09-17 22:11:02] [Iter 1809/2250] R3[758/1200], Temp: 0.2990, Energy: -44.840376+0.002592j
[2025-09-17 22:11:13] [Iter 1810/2250] R3[759/1200], Temp: 0.2978, Energy: -44.835372+0.000763j
[2025-09-17 22:11:25] [Iter 1811/2250] R3[760/1200], Temp: 0.2966, Energy: -44.851371+0.000231j
[2025-09-17 22:11:36] [Iter 1812/2250] R3[761/1200], Temp: 0.2954, Energy: -44.830469+0.001482j
[2025-09-17 22:11:48] [Iter 1813/2250] R3[762/1200], Temp: 0.2942, Energy: -44.844676-0.000150j
[2025-09-17 22:11:59] [Iter 1814/2250] R3[763/1200], Temp: 0.2931, Energy: -44.850113+0.000324j
[2025-09-17 22:12:11] [Iter 1815/2250] R3[764/1200], Temp: 0.2919, Energy: -44.843452+0.000271j
[2025-09-17 22:12:22] [Iter 1816/2250] R3[765/1200], Temp: 0.2907, Energy: -44.838099-0.000533j
[2025-09-17 22:12:34] [Iter 1817/2250] R3[766/1200], Temp: 0.2895, Energy: -44.841896-0.000469j
[2025-09-17 22:12:46] [Iter 1818/2250] R3[767/1200], Temp: 0.2883, Energy: -44.843373-0.000108j
[2025-09-17 22:12:57] [Iter 1819/2250] R3[768/1200], Temp: 0.2871, Energy: -44.840272-0.000114j
[2025-09-17 22:13:09] [Iter 1820/2250] R3[769/1200], Temp: 0.2859, Energy: -44.845247+0.003675j
[2025-09-17 22:13:20] [Iter 1821/2250] R3[770/1200], Temp: 0.2847, Energy: -44.847637-0.000006j
[2025-09-17 22:13:32] [Iter 1822/2250] R3[771/1200], Temp: 0.2836, Energy: -44.841174-0.000714j
[2025-09-17 22:13:43] [Iter 1823/2250] R3[772/1200], Temp: 0.2824, Energy: -44.831088-0.005026j
[2025-09-17 22:13:55] [Iter 1824/2250] R3[773/1200], Temp: 0.2812, Energy: -44.830553-0.001097j
[2025-09-17 22:14:06] [Iter 1825/2250] R3[774/1200], Temp: 0.2800, Energy: -44.838971+0.001029j
[2025-09-17 22:14:18] [Iter 1826/2250] R3[775/1200], Temp: 0.2789, Energy: -44.838844-0.000893j
[2025-09-17 22:14:30] [Iter 1827/2250] R3[776/1200], Temp: 0.2777, Energy: -44.854204-0.000975j
[2025-09-17 22:14:41] [Iter 1828/2250] R3[777/1200], Temp: 0.2765, Energy: -44.853148-0.001184j
[2025-09-17 22:14:53] [Iter 1829/2250] R3[778/1200], Temp: 0.2753, Energy: -44.847045-0.002093j
[2025-09-17 22:15:04] [Iter 1830/2250] R3[779/1200], Temp: 0.2742, Energy: -44.846109-0.002358j
[2025-09-17 22:15:16] [Iter 1831/2250] R3[780/1200], Temp: 0.2730, Energy: -44.837638-0.001684j
[2025-09-17 22:15:27] [Iter 1832/2250] R3[781/1200], Temp: 0.2718, Energy: -44.848523-0.000548j
[2025-09-17 22:15:39] [Iter 1833/2250] R3[782/1200], Temp: 0.2707, Energy: -44.834956+0.002282j
[2025-09-17 22:15:50] [Iter 1834/2250] R3[783/1200], Temp: 0.2695, Energy: -44.843361+0.002436j
[2025-09-17 22:16:02] [Iter 1835/2250] R3[784/1200], Temp: 0.2684, Energy: -44.842080+0.000225j
[2025-09-17 22:16:13] [Iter 1836/2250] R3[785/1200], Temp: 0.2672, Energy: -44.839317-0.001541j
[2025-09-17 22:16:25] [Iter 1837/2250] R3[786/1200], Temp: 0.2660, Energy: -44.837179-0.001459j
[2025-09-17 22:16:37] [Iter 1838/2250] R3[787/1200], Temp: 0.2649, Energy: -44.840863+0.000766j
[2025-09-17 22:16:48] [Iter 1839/2250] R3[788/1200], Temp: 0.2637, Energy: -44.849267-0.000665j
[2025-09-17 22:17:00] [Iter 1840/2250] R3[789/1200], Temp: 0.2626, Energy: -44.844909-0.000245j
[2025-09-17 22:17:11] [Iter 1841/2250] R3[790/1200], Temp: 0.2614, Energy: -44.835413+0.000428j
[2025-09-17 22:17:23] [Iter 1842/2250] R3[791/1200], Temp: 0.2603, Energy: -44.838998-0.001704j
[2025-09-17 22:17:34] [Iter 1843/2250] R3[792/1200], Temp: 0.2591, Energy: -44.847284-0.001011j
[2025-09-17 22:17:46] [Iter 1844/2250] R3[793/1200], Temp: 0.2580, Energy: -44.836728+0.002107j
[2025-09-17 22:17:57] [Iter 1845/2250] R3[794/1200], Temp: 0.2568, Energy: -44.846249-0.000159j
[2025-09-17 22:18:09] [Iter 1846/2250] R3[795/1200], Temp: 0.2557, Energy: -44.840414+0.000720j
[2025-09-17 22:18:20] [Iter 1847/2250] R3[796/1200], Temp: 0.2545, Energy: -44.840420+0.001168j
[2025-09-17 22:18:32] [Iter 1848/2250] R3[797/1200], Temp: 0.2534, Energy: -44.840562-0.003113j
[2025-09-17 22:18:43] [Iter 1849/2250] R3[798/1200], Temp: 0.2523, Energy: -44.840158+0.003386j
[2025-09-17 22:18:55] [Iter 1850/2250] R3[799/1200], Temp: 0.2511, Energy: -44.851340+0.002890j
[2025-09-17 22:19:07] [Iter 1851/2250] R3[800/1200], Temp: 0.2500, Energy: -44.847337+0.000772j
[2025-09-17 22:19:18] [Iter 1852/2250] R3[801/1200], Temp: 0.2489, Energy: -44.847367-0.001016j
[2025-09-17 22:19:30] [Iter 1853/2250] R3[802/1200], Temp: 0.2477, Energy: -44.850415-0.000084j
[2025-09-17 22:19:41] [Iter 1854/2250] R3[803/1200], Temp: 0.2466, Energy: -44.845073-0.000246j
[2025-09-17 22:19:53] [Iter 1855/2250] R3[804/1200], Temp: 0.2455, Energy: -44.835774-0.001052j
[2025-09-17 22:20:04] [Iter 1856/2250] R3[805/1200], Temp: 0.2444, Energy: -44.837403-0.000278j
[2025-09-17 22:20:16] [Iter 1857/2250] R3[806/1200], Temp: 0.2432, Energy: -44.839213+0.003033j
[2025-09-17 22:20:27] [Iter 1858/2250] R3[807/1200], Temp: 0.2421, Energy: -44.845059+0.003189j
[2025-09-17 22:20:39] [Iter 1859/2250] R3[808/1200], Temp: 0.2410, Energy: -44.843277+0.001693j
[2025-09-17 22:20:50] [Iter 1860/2250] R3[809/1200], Temp: 0.2399, Energy: -44.838028-0.000095j
[2025-09-17 22:21:02] [Iter 1861/2250] R3[810/1200], Temp: 0.2388, Energy: -44.852789+0.000725j
[2025-09-17 22:21:13] [Iter 1862/2250] R3[811/1200], Temp: 0.2376, Energy: -44.852769-0.001697j
[2025-09-17 22:21:25] [Iter 1863/2250] R3[812/1200], Temp: 0.2365, Energy: -44.832191-0.003302j
[2025-09-17 22:21:37] [Iter 1864/2250] R3[813/1200], Temp: 0.2354, Energy: -44.845090+0.001986j
[2025-09-17 22:21:48] [Iter 1865/2250] R3[814/1200], Temp: 0.2343, Energy: -44.835722-0.002710j
[2025-09-17 22:22:00] [Iter 1866/2250] R3[815/1200], Temp: 0.2332, Energy: -44.844344+0.000803j
[2025-09-17 22:22:11] [Iter 1867/2250] R3[816/1200], Temp: 0.2321, Energy: -44.838738+0.001381j
[2025-09-17 22:22:23] [Iter 1868/2250] R3[817/1200], Temp: 0.2310, Energy: -44.844956-0.000671j
[2025-09-17 22:22:34] [Iter 1869/2250] R3[818/1200], Temp: 0.2299, Energy: -44.841379+0.000149j
[2025-09-17 22:22:46] [Iter 1870/2250] R3[819/1200], Temp: 0.2288, Energy: -44.846294-0.000399j
[2025-09-17 22:22:57] [Iter 1871/2250] R3[820/1200], Temp: 0.2277, Energy: -44.859632+0.000690j
[2025-09-17 22:23:09] [Iter 1872/2250] R3[821/1200], Temp: 0.2266, Energy: -44.844253+0.000402j
[2025-09-17 22:23:20] [Iter 1873/2250] R3[822/1200], Temp: 0.2255, Energy: -44.844849-0.000644j
[2025-09-17 22:23:32] [Iter 1874/2250] R3[823/1200], Temp: 0.2244, Energy: -44.837469+0.001623j
[2025-09-17 22:23:44] [Iter 1875/2250] R3[824/1200], Temp: 0.2233, Energy: -44.827566-0.001547j
[2025-09-17 22:23:55] [Iter 1876/2250] R3[825/1200], Temp: 0.2222, Energy: -44.838087-0.000698j
[2025-09-17 22:24:07] [Iter 1877/2250] R3[826/1200], Temp: 0.2211, Energy: -44.851282-0.001259j
[2025-09-17 22:24:18] [Iter 1878/2250] R3[827/1200], Temp: 0.2200, Energy: -44.838159+0.000813j
[2025-09-17 22:24:30] [Iter 1879/2250] R3[828/1200], Temp: 0.2190, Energy: -44.839878+0.002424j
[2025-09-17 22:24:41] [Iter 1880/2250] R3[829/1200], Temp: 0.2179, Energy: -44.852626-0.001961j
[2025-09-17 22:24:53] [Iter 1881/2250] R3[830/1200], Temp: 0.2168, Energy: -44.843417-0.001000j
[2025-09-17 22:25:04] [Iter 1882/2250] R3[831/1200], Temp: 0.2157, Energy: -44.847403+0.000146j
[2025-09-17 22:25:16] [Iter 1883/2250] R3[832/1200], Temp: 0.2146, Energy: -44.846198+0.002293j
[2025-09-17 22:25:27] [Iter 1884/2250] R3[833/1200], Temp: 0.2136, Energy: -44.844001-0.001975j
[2025-09-17 22:25:39] [Iter 1885/2250] R3[834/1200], Temp: 0.2125, Energy: -44.851903-0.002305j
[2025-09-17 22:25:51] [Iter 1886/2250] R3[835/1200], Temp: 0.2114, Energy: -44.846996-0.001749j
[2025-09-17 22:26:02] [Iter 1887/2250] R3[836/1200], Temp: 0.2104, Energy: -44.843965+0.001097j
[2025-09-17 22:26:14] [Iter 1888/2250] R3[837/1200], Temp: 0.2093, Energy: -44.839811-0.004689j
[2025-09-17 22:26:25] [Iter 1889/2250] R3[838/1200], Temp: 0.2082, Energy: -44.854446+0.001020j
[2025-09-17 22:26:37] [Iter 1890/2250] R3[839/1200], Temp: 0.2072, Energy: -44.839778+0.000509j
[2025-09-17 22:26:48] [Iter 1891/2250] R3[840/1200], Temp: 0.2061, Energy: -44.839405+0.000998j
[2025-09-17 22:27:00] [Iter 1892/2250] R3[841/1200], Temp: 0.2050, Energy: -44.845930+0.001985j
[2025-09-17 22:27:11] [Iter 1893/2250] R3[842/1200], Temp: 0.2040, Energy: -44.830216-0.002278j
[2025-09-17 22:27:23] [Iter 1894/2250] R3[843/1200], Temp: 0.2029, Energy: -44.841890-0.000407j
[2025-09-17 22:27:34] [Iter 1895/2250] R3[844/1200], Temp: 0.2019, Energy: -44.843032-0.000931j
[2025-09-17 22:27:46] [Iter 1896/2250] R3[845/1200], Temp: 0.2008, Energy: -44.847088-0.003032j
[2025-09-17 22:27:57] [Iter 1897/2250] R3[846/1200], Temp: 0.1998, Energy: -44.845730+0.004770j
[2025-09-17 22:28:09] [Iter 1898/2250] R3[847/1200], Temp: 0.1987, Energy: -44.844462-0.000964j
[2025-09-17 22:28:21] [Iter 1899/2250] R3[848/1200], Temp: 0.1977, Energy: -44.853630-0.002000j
[2025-09-17 22:28:32] [Iter 1900/2250] R3[849/1200], Temp: 0.1967, Energy: -44.840863-0.004133j
[2025-09-17 22:28:44] [Iter 1901/2250] R3[850/1200], Temp: 0.1956, Energy: -44.842915+0.002772j
[2025-09-17 22:28:55] [Iter 1902/2250] R3[851/1200], Temp: 0.1946, Energy: -44.851139-0.000489j
[2025-09-17 22:29:07] [Iter 1903/2250] R3[852/1200], Temp: 0.1935, Energy: -44.841669+0.001808j
[2025-09-17 22:29:18] [Iter 1904/2250] R3[853/1200], Temp: 0.1925, Energy: -44.846458-0.003799j
[2025-09-17 22:29:30] [Iter 1905/2250] R3[854/1200], Temp: 0.1915, Energy: -44.833307+0.003186j
[2025-09-17 22:29:41] [Iter 1906/2250] R3[855/1200], Temp: 0.1905, Energy: -44.847775-0.002106j
[2025-09-17 22:29:53] [Iter 1907/2250] R3[856/1200], Temp: 0.1894, Energy: -44.840289+0.001760j
[2025-09-17 22:30:04] [Iter 1908/2250] R3[857/1200], Temp: 0.1884, Energy: -44.851838+0.001072j
[2025-09-17 22:30:16] [Iter 1909/2250] R3[858/1200], Temp: 0.1874, Energy: -44.846467-0.001660j
[2025-09-17 22:30:28] [Iter 1910/2250] R3[859/1200], Temp: 0.1864, Energy: -44.839306+0.003089j
[2025-09-17 22:30:39] [Iter 1911/2250] R3[860/1200], Temp: 0.1853, Energy: -44.845178+0.003237j
[2025-09-17 22:30:51] [Iter 1912/2250] R3[861/1200], Temp: 0.1843, Energy: -44.852270-0.000062j
[2025-09-17 22:31:02] [Iter 1913/2250] R3[862/1200], Temp: 0.1833, Energy: -44.847608-0.000627j
[2025-09-17 22:31:14] [Iter 1914/2250] R3[863/1200], Temp: 0.1823, Energy: -44.841917+0.000172j
[2025-09-17 22:31:25] [Iter 1915/2250] R3[864/1200], Temp: 0.1813, Energy: -44.846575+0.001968j
[2025-09-17 22:31:37] [Iter 1916/2250] R3[865/1200], Temp: 0.1803, Energy: -44.846979+0.001249j
[2025-09-17 22:31:48] [Iter 1917/2250] R3[866/1200], Temp: 0.1793, Energy: -44.848415+0.001866j
[2025-09-17 22:32:00] [Iter 1918/2250] R3[867/1200], Temp: 0.1783, Energy: -44.851788+0.000760j
[2025-09-17 22:32:12] [Iter 1919/2250] R3[868/1200], Temp: 0.1773, Energy: -44.848533+0.003125j
[2025-09-17 22:32:23] [Iter 1920/2250] R3[869/1200], Temp: 0.1763, Energy: -44.842046-0.002360j
[2025-09-17 22:32:35] [Iter 1921/2250] R3[870/1200], Temp: 0.1753, Energy: -44.839426-0.000049j
[2025-09-17 22:32:46] [Iter 1922/2250] R3[871/1200], Temp: 0.1743, Energy: -44.846848+0.000565j
[2025-09-17 22:32:58] [Iter 1923/2250] R3[872/1200], Temp: 0.1733, Energy: -44.843477-0.002078j
[2025-09-17 22:33:09] [Iter 1924/2250] R3[873/1200], Temp: 0.1723, Energy: -44.841201-0.001161j
[2025-09-17 22:33:21] [Iter 1925/2250] R3[874/1200], Temp: 0.1713, Energy: -44.848120+0.003731j
[2025-09-17 22:33:32] [Iter 1926/2250] R3[875/1200], Temp: 0.1703, Energy: -44.838682+0.005026j
[2025-09-17 22:33:44] [Iter 1927/2250] R3[876/1200], Temp: 0.1693, Energy: -44.845938-0.001026j
[2025-09-17 22:33:55] [Iter 1928/2250] R3[877/1200], Temp: 0.1684, Energy: -44.840497+0.000327j
[2025-09-17 22:34:07] [Iter 1929/2250] R3[878/1200], Temp: 0.1674, Energy: -44.858704-0.001346j
[2025-09-17 22:34:19] [Iter 1930/2250] R3[879/1200], Temp: 0.1664, Energy: -44.845987+0.002361j
[2025-09-17 22:34:30] [Iter 1931/2250] R3[880/1200], Temp: 0.1654, Energy: -44.839176-0.000934j
[2025-09-17 22:34:42] [Iter 1932/2250] R3[881/1200], Temp: 0.1645, Energy: -44.844165-0.001946j
[2025-09-17 22:34:53] [Iter 1933/2250] R3[882/1200], Temp: 0.1635, Energy: -44.854449+0.000836j
[2025-09-17 22:35:05] [Iter 1934/2250] R3[883/1200], Temp: 0.1625, Energy: -44.846361+0.001768j
[2025-09-17 22:35:16] [Iter 1935/2250] R3[884/1200], Temp: 0.1616, Energy: -44.843315-0.000574j
[2025-09-17 22:35:28] [Iter 1936/2250] R3[885/1200], Temp: 0.1606, Energy: -44.838207-0.000241j
[2025-09-17 22:35:39] [Iter 1937/2250] R3[886/1200], Temp: 0.1596, Energy: -44.842555+0.000038j
[2025-09-17 22:35:51] [Iter 1938/2250] R3[887/1200], Temp: 0.1587, Energy: -44.848468+0.000762j
[2025-09-17 22:36:02] [Iter 1939/2250] R3[888/1200], Temp: 0.1577, Energy: -44.861461-0.001452j
[2025-09-17 22:36:14] [Iter 1940/2250] R3[889/1200], Temp: 0.1568, Energy: -44.841748-0.001077j
[2025-09-17 22:36:25] [Iter 1941/2250] R3[890/1200], Temp: 0.1558, Energy: -44.841196-0.001909j
[2025-09-17 22:36:37] [Iter 1942/2250] R3[891/1200], Temp: 0.1549, Energy: -44.838403+0.004189j
[2025-09-17 22:36:49] [Iter 1943/2250] R3[892/1200], Temp: 0.1539, Energy: -44.838334-0.002058j
[2025-09-17 22:37:00] [Iter 1944/2250] R3[893/1200], Temp: 0.1530, Energy: -44.849594-0.000842j
[2025-09-17 22:37:12] [Iter 1945/2250] R3[894/1200], Temp: 0.1520, Energy: -44.837635-0.001841j
[2025-09-17 22:37:23] [Iter 1946/2250] R3[895/1200], Temp: 0.1511, Energy: -44.844943-0.000406j
[2025-09-17 22:37:35] [Iter 1947/2250] R3[896/1200], Temp: 0.1502, Energy: -44.851167+0.004802j
[2025-09-17 22:37:46] [Iter 1948/2250] R3[897/1200], Temp: 0.1492, Energy: -44.842263-0.002665j
[2025-09-17 22:37:58] [Iter 1949/2250] R3[898/1200], Temp: 0.1483, Energy: -44.840809-0.000036j
[2025-09-17 22:38:09] [Iter 1950/2250] R3[899/1200], Temp: 0.1474, Energy: -44.840035+0.000124j
[2025-09-17 22:38:21] [Iter 1951/2250] R3[900/1200], Temp: 0.1464, Energy: -44.839423+0.001885j
[2025-09-17 22:38:32] [Iter 1952/2250] R3[901/1200], Temp: 0.1455, Energy: -44.847611-0.000837j
[2025-09-17 22:38:44] [Iter 1953/2250] R3[902/1200], Temp: 0.1446, Energy: -44.855295-0.004464j
[2025-09-17 22:38:56] [Iter 1954/2250] R3[903/1200], Temp: 0.1437, Energy: -44.839533+0.001018j
[2025-09-17 22:39:07] [Iter 1955/2250] R3[904/1200], Temp: 0.1428, Energy: -44.847129+0.000287j
[2025-09-17 22:39:19] [Iter 1956/2250] R3[905/1200], Temp: 0.1418, Energy: -44.843984-0.001129j
[2025-09-17 22:39:30] [Iter 1957/2250] R3[906/1200], Temp: 0.1409, Energy: -44.840117-0.002439j
[2025-09-17 22:39:42] [Iter 1958/2250] R3[907/1200], Temp: 0.1400, Energy: -44.839424-0.000127j
[2025-09-17 22:39:53] [Iter 1959/2250] R3[908/1200], Temp: 0.1391, Energy: -44.863576-0.005064j
[2025-09-17 22:40:05] [Iter 1960/2250] R3[909/1200], Temp: 0.1382, Energy: -44.854456-0.000870j
[2025-09-17 22:40:16] [Iter 1961/2250] R3[910/1200], Temp: 0.1373, Energy: -44.848377-0.000924j
[2025-09-17 22:40:28] [Iter 1962/2250] R3[911/1200], Temp: 0.1364, Energy: -44.849956+0.001699j
[2025-09-17 22:40:39] [Iter 1963/2250] R3[912/1200], Temp: 0.1355, Energy: -44.851302+0.000138j
[2025-09-17 22:40:51] [Iter 1964/2250] R3[913/1200], Temp: 0.1346, Energy: -44.829298-0.000845j
[2025-09-17 22:41:02] [Iter 1965/2250] R3[914/1200], Temp: 0.1337, Energy: -44.853836-0.001254j
[2025-09-17 22:41:14] [Iter 1966/2250] R3[915/1200], Temp: 0.1328, Energy: -44.843895-0.000160j
[2025-09-17 22:41:25] [Iter 1967/2250] R3[916/1200], Temp: 0.1320, Energy: -44.856156-0.003844j
[2025-09-17 22:41:37] [Iter 1968/2250] R3[917/1200], Temp: 0.1311, Energy: -44.837763-0.001813j
[2025-09-17 22:41:49] [Iter 1969/2250] R3[918/1200], Temp: 0.1302, Energy: -44.852833-0.000346j
[2025-09-17 22:42:00] [Iter 1970/2250] R3[919/1200], Temp: 0.1293, Energy: -44.853163+0.000222j
[2025-09-17 22:42:12] [Iter 1971/2250] R3[920/1200], Temp: 0.1284, Energy: -44.858762+0.001728j
[2025-09-17 22:42:23] [Iter 1972/2250] R3[921/1200], Temp: 0.1276, Energy: -44.850672+0.001835j
[2025-09-17 22:42:35] [Iter 1973/2250] R3[922/1200], Temp: 0.1267, Energy: -44.847826-0.000080j
[2025-09-17 22:42:46] [Iter 1974/2250] R3[923/1200], Temp: 0.1258, Energy: -44.832613-0.001470j
[2025-09-17 22:42:58] [Iter 1975/2250] R3[924/1200], Temp: 0.1249, Energy: -44.835746-0.000910j
[2025-09-17 22:43:09] [Iter 1976/2250] R3[925/1200], Temp: 0.1241, Energy: -44.838531-0.000775j
[2025-09-17 22:43:21] [Iter 1977/2250] R3[926/1200], Temp: 0.1232, Energy: -44.847959+0.000814j
[2025-09-17 22:43:32] [Iter 1978/2250] R3[927/1200], Temp: 0.1224, Energy: -44.833144+0.001062j
[2025-09-17 22:43:44] [Iter 1979/2250] R3[928/1200], Temp: 0.1215, Energy: -44.849268-0.002219j
[2025-09-17 22:43:55] [Iter 1980/2250] R3[929/1200], Temp: 0.1206, Energy: -44.852082-0.000665j
[2025-09-17 22:44:07] [Iter 1981/2250] R3[930/1200], Temp: 0.1198, Energy: -44.855077-0.001953j
[2025-09-17 22:44:19] [Iter 1982/2250] R3[931/1200], Temp: 0.1189, Energy: -44.841627-0.000135j
[2025-09-17 22:44:30] [Iter 1983/2250] R3[932/1200], Temp: 0.1181, Energy: -44.844303-0.001058j
[2025-09-17 22:44:42] [Iter 1984/2250] R3[933/1200], Temp: 0.1173, Energy: -44.849872-0.000622j
[2025-09-17 22:44:53] [Iter 1985/2250] R3[934/1200], Temp: 0.1164, Energy: -44.857033-0.000240j
[2025-09-17 22:45:05] [Iter 1986/2250] R3[935/1200], Temp: 0.1156, Energy: -44.834087-0.001745j
[2025-09-17 22:45:16] [Iter 1987/2250] R3[936/1200], Temp: 0.1147, Energy: -44.834294+0.001473j
[2025-09-17 22:45:28] [Iter 1988/2250] R3[937/1200], Temp: 0.1139, Energy: -44.839548-0.000189j
[2025-09-17 22:45:39] [Iter 1989/2250] R3[938/1200], Temp: 0.1131, Energy: -44.854520-0.001198j
[2025-09-17 22:45:51] [Iter 1990/2250] R3[939/1200], Temp: 0.1123, Energy: -44.835482+0.000238j
[2025-09-17 22:46:02] [Iter 1991/2250] R3[940/1200], Temp: 0.1114, Energy: -44.856111+0.001456j
[2025-09-17 22:46:14] [Iter 1992/2250] R3[941/1200], Temp: 0.1106, Energy: -44.840113-0.000695j
[2025-09-17 22:46:25] [Iter 1993/2250] R3[942/1200], Temp: 0.1098, Energy: -44.859657-0.000904j
[2025-09-17 22:46:37] [Iter 1994/2250] R3[943/1200], Temp: 0.1090, Energy: -44.859445+0.001548j
[2025-09-17 22:46:49] [Iter 1995/2250] R3[944/1200], Temp: 0.1082, Energy: -44.835774+0.004296j
[2025-09-17 22:47:00] [Iter 1996/2250] R3[945/1200], Temp: 0.1073, Energy: -44.842586+0.001285j
[2025-09-17 22:47:12] [Iter 1997/2250] R3[946/1200], Temp: 0.1065, Energy: -44.848342-0.000911j
[2025-09-17 22:47:23] [Iter 1998/2250] R3[947/1200], Temp: 0.1057, Energy: -44.841665+0.003621j
[2025-09-17 22:47:35] [Iter 1999/2250] R3[948/1200], Temp: 0.1049, Energy: -44.842345-0.000043j
[2025-09-17 22:47:46] [Iter 2000/2250] R3[949/1200], Temp: 0.1041, Energy: -44.855695-0.000276j
[2025-09-17 22:47:46] ✓ Checkpoint saved: checkpoint_iter_002000.pkl
[2025-09-17 22:47:58] [Iter 2001/2250] R3[950/1200], Temp: 0.1033, Energy: -44.852149-0.002105j
[2025-09-17 22:48:09] [Iter 2002/2250] R3[951/1200], Temp: 0.1025, Energy: -44.851087-0.001418j
[2025-09-17 22:48:21] [Iter 2003/2250] R3[952/1200], Temp: 0.1017, Energy: -44.849613+0.003618j
[2025-09-17 22:48:32] [Iter 2004/2250] R3[953/1200], Temp: 0.1009, Energy: -44.844568+0.002053j
[2025-09-17 22:48:44] [Iter 2005/2250] R3[954/1200], Temp: 0.1002, Energy: -44.854306+0.002986j
[2025-09-17 22:48:56] [Iter 2006/2250] R3[955/1200], Temp: 0.0994, Energy: -44.850268+0.004480j
[2025-09-17 22:49:07] [Iter 2007/2250] R3[956/1200], Temp: 0.0986, Energy: -44.843620-0.000377j
[2025-09-17 22:49:19] [Iter 2008/2250] R3[957/1200], Temp: 0.0978, Energy: -44.844157+0.000227j
[2025-09-17 22:49:30] [Iter 2009/2250] R3[958/1200], Temp: 0.0970, Energy: -44.840088-0.004035j
[2025-09-17 22:49:42] [Iter 2010/2250] R3[959/1200], Temp: 0.0963, Energy: -44.839340-0.001933j
[2025-09-17 22:49:53] [Iter 2011/2250] R3[960/1200], Temp: 0.0955, Energy: -44.856828+0.000004j
[2025-09-17 22:50:05] [Iter 2012/2250] R3[961/1200], Temp: 0.0947, Energy: -44.843358-0.000465j
[2025-09-17 22:50:16] [Iter 2013/2250] R3[962/1200], Temp: 0.0940, Energy: -44.844920-0.001895j
[2025-09-17 22:50:28] [Iter 2014/2250] R3[963/1200], Temp: 0.0932, Energy: -44.858480-0.002995j
[2025-09-17 22:50:39] [Iter 2015/2250] R3[964/1200], Temp: 0.0924, Energy: -44.844599-0.002499j
[2025-09-17 22:50:51] [Iter 2016/2250] R3[965/1200], Temp: 0.0917, Energy: -44.845815+0.001796j
[2025-09-17 22:51:03] [Iter 2017/2250] R3[966/1200], Temp: 0.0909, Energy: -44.847201+0.001373j
[2025-09-17 22:51:14] [Iter 2018/2250] R3[967/1200], Temp: 0.0902, Energy: -44.841648-0.000883j
[2025-09-17 22:51:26] [Iter 2019/2250] R3[968/1200], Temp: 0.0894, Energy: -44.842406-0.000610j
[2025-09-17 22:51:37] [Iter 2020/2250] R3[969/1200], Temp: 0.0887, Energy: -44.850037-0.001137j
[2025-09-17 22:51:49] [Iter 2021/2250] R3[970/1200], Temp: 0.0879, Energy: -44.843352+0.000891j
[2025-09-17 22:52:00] [Iter 2022/2250] R3[971/1200], Temp: 0.0872, Energy: -44.836641-0.000997j
[2025-09-17 22:52:12] [Iter 2023/2250] R3[972/1200], Temp: 0.0865, Energy: -44.838501+0.000050j
[2025-09-17 22:52:23] [Iter 2024/2250] R3[973/1200], Temp: 0.0857, Energy: -44.840074-0.001218j
[2025-09-17 22:52:35] [Iter 2025/2250] R3[974/1200], Temp: 0.0850, Energy: -44.856252+0.001817j
[2025-09-17 22:52:46] [Iter 2026/2250] R3[975/1200], Temp: 0.0843, Energy: -44.845418+0.001334j
[2025-09-17 22:52:58] [Iter 2027/2250] R3[976/1200], Temp: 0.0835, Energy: -44.845981-0.001607j
[2025-09-17 22:53:10] [Iter 2028/2250] R3[977/1200], Temp: 0.0828, Energy: -44.842481-0.000269j
[2025-09-17 22:53:21] [Iter 2029/2250] R3[978/1200], Temp: 0.0821, Energy: -44.846397+0.000249j
[2025-09-17 22:53:33] [Iter 2030/2250] R3[979/1200], Temp: 0.0814, Energy: -44.846802+0.002809j
[2025-09-17 22:53:44] [Iter 2031/2250] R3[980/1200], Temp: 0.0807, Energy: -44.843285-0.002831j
[2025-09-17 22:53:56] [Iter 2032/2250] R3[981/1200], Temp: 0.0800, Energy: -44.846590-0.002696j
[2025-09-17 22:54:07] [Iter 2033/2250] R3[982/1200], Temp: 0.0792, Energy: -44.839203-0.001118j
[2025-09-17 22:54:19] [Iter 2034/2250] R3[983/1200], Temp: 0.0785, Energy: -44.858485+0.004594j
[2025-09-17 22:54:30] [Iter 2035/2250] R3[984/1200], Temp: 0.0778, Energy: -44.846298+0.000303j
[2025-09-17 22:54:42] [Iter 2036/2250] R3[985/1200], Temp: 0.0771, Energy: -44.840683-0.000574j
[2025-09-17 22:54:53] [Iter 2037/2250] R3[986/1200], Temp: 0.0764, Energy: -44.848194+0.000361j
[2025-09-17 22:55:05] [Iter 2038/2250] R3[987/1200], Temp: 0.0757, Energy: -44.842624+0.001917j
[2025-09-17 22:55:16] [Iter 2039/2250] R3[988/1200], Temp: 0.0751, Energy: -44.859094-0.006310j
[2025-09-17 22:55:28] [Iter 2040/2250] R3[989/1200], Temp: 0.0744, Energy: -44.843972+0.000047j
[2025-09-17 22:55:40] [Iter 2041/2250] R3[990/1200], Temp: 0.0737, Energy: -44.847798+0.001549j
[2025-09-17 22:55:51] [Iter 2042/2250] R3[991/1200], Temp: 0.0730, Energy: -44.848053-0.000631j
[2025-09-17 22:56:03] [Iter 2043/2250] R3[992/1200], Temp: 0.0723, Energy: -44.850852+0.000552j
[2025-09-17 22:56:14] [Iter 2044/2250] R3[993/1200], Temp: 0.0716, Energy: -44.840236-0.000678j
[2025-09-17 22:56:26] [Iter 2045/2250] R3[994/1200], Temp: 0.0710, Energy: -44.846391+0.001947j
[2025-09-17 22:56:38] [Iter 2046/2250] R3[995/1200], Temp: 0.0703, Energy: -44.841329-0.001505j
[2025-09-17 22:56:49] [Iter 2047/2250] R3[996/1200], Temp: 0.0696, Energy: -44.858344+0.001848j
[2025-09-17 22:57:01] [Iter 2048/2250] R3[997/1200], Temp: 0.0690, Energy: -44.834958+0.002051j
[2025-09-17 22:57:12] [Iter 2049/2250] R3[998/1200], Temp: 0.0683, Energy: -44.843883+0.002096j
[2025-09-17 22:57:24] [Iter 2050/2250] R3[999/1200], Temp: 0.0676, Energy: -44.842548+0.001113j
[2025-09-17 22:57:35] [Iter 2051/2250] R3[1000/1200], Temp: 0.0670, Energy: -44.838542-0.000706j
[2025-09-17 22:57:47] [Iter 2052/2250] R3[1001/1200], Temp: 0.0663, Energy: -44.855404-0.001312j
[2025-09-17 22:57:58] [Iter 2053/2250] R3[1002/1200], Temp: 0.0657, Energy: -44.849807-0.000158j
[2025-09-17 22:58:10] [Iter 2054/2250] R3[1003/1200], Temp: 0.0650, Energy: -44.856848-0.000848j
[2025-09-17 22:58:22] [Iter 2055/2250] R3[1004/1200], Temp: 0.0644, Energy: -44.848707+0.000557j
[2025-09-17 22:58:33] [Iter 2056/2250] R3[1005/1200], Temp: 0.0638, Energy: -44.861279-0.004582j
[2025-09-17 22:58:45] [Iter 2057/2250] R3[1006/1200], Temp: 0.0631, Energy: -44.853147+0.001030j
[2025-09-17 22:58:56] [Iter 2058/2250] R3[1007/1200], Temp: 0.0625, Energy: -44.850707-0.002394j
[2025-09-17 22:59:08] [Iter 2059/2250] R3[1008/1200], Temp: 0.0618, Energy: -44.833549+0.002727j
[2025-09-17 22:59:19] [Iter 2060/2250] R3[1009/1200], Temp: 0.0612, Energy: -44.843232-0.002173j
[2025-09-17 22:59:31] [Iter 2061/2250] R3[1010/1200], Temp: 0.0606, Energy: -44.843810+0.001515j
[2025-09-17 22:59:42] [Iter 2062/2250] R3[1011/1200], Temp: 0.0600, Energy: -44.846629+0.001158j
[2025-09-17 22:59:54] [Iter 2063/2250] R3[1012/1200], Temp: 0.0593, Energy: -44.854295+0.002573j
[2025-09-17 23:00:05] [Iter 2064/2250] R3[1013/1200], Temp: 0.0587, Energy: -44.854120+0.000494j
[2025-09-17 23:00:17] [Iter 2065/2250] R3[1014/1200], Temp: 0.0581, Energy: -44.840688-0.000183j
[2025-09-17 23:00:29] [Iter 2066/2250] R3[1015/1200], Temp: 0.0575, Energy: -44.850341-0.002661j
[2025-09-17 23:00:40] [Iter 2067/2250] R3[1016/1200], Temp: 0.0569, Energy: -44.833046-0.002313j
[2025-09-17 23:00:52] [Iter 2068/2250] R3[1017/1200], Temp: 0.0563, Energy: -44.853447+0.001404j
[2025-09-17 23:01:03] [Iter 2069/2250] R3[1018/1200], Temp: 0.0557, Energy: -44.850104-0.002099j
[2025-09-17 23:01:15] [Iter 2070/2250] R3[1019/1200], Temp: 0.0551, Energy: -44.842797+0.001821j
[2025-09-17 23:01:26] [Iter 2071/2250] R3[1020/1200], Temp: 0.0545, Energy: -44.845819-0.000562j
[2025-09-17 23:01:38] [Iter 2072/2250] R3[1021/1200], Temp: 0.0539, Energy: -44.846780-0.001274j
[2025-09-17 23:01:49] [Iter 2073/2250] R3[1022/1200], Temp: 0.0533, Energy: -44.840897-0.000432j
[2025-09-17 23:02:01] [Iter 2074/2250] R3[1023/1200], Temp: 0.0527, Energy: -44.857390-0.001481j
[2025-09-17 23:02:12] [Iter 2075/2250] R3[1024/1200], Temp: 0.0521, Energy: -44.843778-0.001362j
[2025-09-17 23:02:24] [Iter 2076/2250] R3[1025/1200], Temp: 0.0516, Energy: -44.841546-0.000702j
[2025-09-17 23:02:35] [Iter 2077/2250] R3[1026/1200], Temp: 0.0510, Energy: -44.849674+0.001062j
[2025-09-17 23:02:47] [Iter 2078/2250] R3[1027/1200], Temp: 0.0504, Energy: -44.852565+0.001030j
[2025-09-17 23:02:59] [Iter 2079/2250] R3[1028/1200], Temp: 0.0498, Energy: -44.849017+0.000363j
[2025-09-17 23:03:10] [Iter 2080/2250] R3[1029/1200], Temp: 0.0493, Energy: -44.852229-0.003244j
[2025-09-17 23:03:22] [Iter 2081/2250] R3[1030/1200], Temp: 0.0487, Energy: -44.843998+0.001314j
[2025-09-17 23:03:33] [Iter 2082/2250] R3[1031/1200], Temp: 0.0481, Energy: -44.854317+0.002494j
[2025-09-17 23:03:45] [Iter 2083/2250] R3[1032/1200], Temp: 0.0476, Energy: -44.851632-0.000934j
[2025-09-17 23:03:56] [Iter 2084/2250] R3[1033/1200], Temp: 0.0470, Energy: -44.851498+0.001118j
[2025-09-17 23:04:08] [Iter 2085/2250] R3[1034/1200], Temp: 0.0465, Energy: -44.850475+0.001336j
[2025-09-17 23:04:19] [Iter 2086/2250] R3[1035/1200], Temp: 0.0459, Energy: -44.850632+0.001973j
[2025-09-17 23:04:31] [Iter 2087/2250] R3[1036/1200], Temp: 0.0454, Energy: -44.843919+0.000841j
[2025-09-17 23:04:42] [Iter 2088/2250] R3[1037/1200], Temp: 0.0448, Energy: -44.848140-0.000005j
[2025-09-17 23:04:54] [Iter 2089/2250] R3[1038/1200], Temp: 0.0443, Energy: -44.847030+0.000383j
[2025-09-17 23:05:05] [Iter 2090/2250] R3[1039/1200], Temp: 0.0438, Energy: -44.844729+0.002152j
[2025-09-17 23:05:17] [Iter 2091/2250] R3[1040/1200], Temp: 0.0432, Energy: -44.847444-0.000980j
[2025-09-17 23:05:29] [Iter 2092/2250] R3[1041/1200], Temp: 0.0427, Energy: -44.851753+0.000443j
[2025-09-17 23:05:40] [Iter 2093/2250] R3[1042/1200], Temp: 0.0422, Energy: -44.842917+0.001564j
[2025-09-17 23:05:52] [Iter 2094/2250] R3[1043/1200], Temp: 0.0416, Energy: -44.850255+0.002776j
[2025-09-17 23:06:03] [Iter 2095/2250] R3[1044/1200], Temp: 0.0411, Energy: -44.859175-0.000849j
[2025-09-17 23:06:15] [Iter 2096/2250] R3[1045/1200], Temp: 0.0406, Energy: -44.856297-0.002959j
[2025-09-17 23:06:26] [Iter 2097/2250] R3[1046/1200], Temp: 0.0401, Energy: -44.855895+0.000410j
[2025-09-17 23:06:38] [Iter 2098/2250] R3[1047/1200], Temp: 0.0396, Energy: -44.855927-0.002978j
[2025-09-17 23:06:49] [Iter 2099/2250] R3[1048/1200], Temp: 0.0391, Energy: -44.858054+0.000748j
[2025-09-17 23:07:01] [Iter 2100/2250] R3[1049/1200], Temp: 0.0386, Energy: -44.850694-0.002318j
[2025-09-17 23:07:12] [Iter 2101/2250] R3[1050/1200], Temp: 0.0381, Energy: -44.850537+0.002685j
[2025-09-17 23:07:24] [Iter 2102/2250] R3[1051/1200], Temp: 0.0376, Energy: -44.852777+0.000849j
[2025-09-17 23:07:36] [Iter 2103/2250] R3[1052/1200], Temp: 0.0371, Energy: -44.858705+0.000032j
[2025-09-17 23:07:47] [Iter 2104/2250] R3[1053/1200], Temp: 0.0366, Energy: -44.847094-0.000329j
[2025-09-17 23:07:59] [Iter 2105/2250] R3[1054/1200], Temp: 0.0361, Energy: -44.838538+0.002793j
[2025-09-17 23:08:10] [Iter 2106/2250] R3[1055/1200], Temp: 0.0356, Energy: -44.859544-0.000566j
[2025-09-17 23:08:22] [Iter 2107/2250] R3[1056/1200], Temp: 0.0351, Energy: -44.850341+0.001928j
[2025-09-17 23:08:33] [Iter 2108/2250] R3[1057/1200], Temp: 0.0346, Energy: -44.844366+0.001157j
[2025-09-17 23:08:45] [Iter 2109/2250] R3[1058/1200], Temp: 0.0342, Energy: -44.857010+0.001648j
[2025-09-17 23:08:56] [Iter 2110/2250] R3[1059/1200], Temp: 0.0337, Energy: -44.843921-0.000010j
[2025-09-17 23:09:08] [Iter 2111/2250] R3[1060/1200], Temp: 0.0332, Energy: -44.848956-0.001810j
[2025-09-17 23:09:19] [Iter 2112/2250] R3[1061/1200], Temp: 0.0327, Energy: -44.847340+0.001374j
[2025-09-17 23:09:31] [Iter 2113/2250] R3[1062/1200], Temp: 0.0323, Energy: -44.852992-0.001603j
[2025-09-17 23:09:43] [Iter 2114/2250] R3[1063/1200], Temp: 0.0318, Energy: -44.851031+0.002149j
[2025-09-17 23:09:54] [Iter 2115/2250] R3[1064/1200], Temp: 0.0314, Energy: -44.846168+0.000151j
[2025-09-17 23:10:06] [Iter 2116/2250] R3[1065/1200], Temp: 0.0309, Energy: -44.848589+0.000824j
[2025-09-17 23:10:17] [Iter 2117/2250] R3[1066/1200], Temp: 0.0305, Energy: -44.840758-0.001144j
[2025-09-17 23:10:29] [Iter 2118/2250] R3[1067/1200], Temp: 0.0300, Energy: -44.848053+0.004146j
[2025-09-17 23:10:40] [Iter 2119/2250] R3[1068/1200], Temp: 0.0296, Energy: -44.846483+0.002594j
[2025-09-17 23:10:52] [Iter 2120/2250] R3[1069/1200], Temp: 0.0291, Energy: -44.838951+0.002620j
[2025-09-17 23:11:03] [Iter 2121/2250] R3[1070/1200], Temp: 0.0287, Energy: -44.844071-0.001438j
[2025-09-17 23:11:15] [Iter 2122/2250] R3[1071/1200], Temp: 0.0282, Energy: -44.851951-0.001004j
[2025-09-17 23:11:26] [Iter 2123/2250] R3[1072/1200], Temp: 0.0278, Energy: -44.848804+0.002494j
[2025-09-17 23:11:38] [Iter 2124/2250] R3[1073/1200], Temp: 0.0274, Energy: -44.847519-0.000155j
[2025-09-17 23:11:50] [Iter 2125/2250] R3[1074/1200], Temp: 0.0270, Energy: -44.847993-0.001160j
[2025-09-17 23:12:01] [Iter 2126/2250] R3[1075/1200], Temp: 0.0265, Energy: -44.849904+0.000559j
[2025-09-17 23:12:13] [Iter 2127/2250] R3[1076/1200], Temp: 0.0261, Energy: -44.850843+0.002083j
[2025-09-17 23:12:24] [Iter 2128/2250] R3[1077/1200], Temp: 0.0257, Energy: -44.842327-0.001364j
[2025-09-17 23:12:36] [Iter 2129/2250] R3[1078/1200], Temp: 0.0253, Energy: -44.854452+0.001008j
[2025-09-17 23:12:47] [Iter 2130/2250] R3[1079/1200], Temp: 0.0249, Energy: -44.851551+0.000089j
[2025-09-17 23:12:59] [Iter 2131/2250] R3[1080/1200], Temp: 0.0245, Energy: -44.843986+0.000401j
[2025-09-17 23:13:10] [Iter 2132/2250] R3[1081/1200], Temp: 0.0241, Energy: -44.850047+0.000599j
[2025-09-17 23:13:22] [Iter 2133/2250] R3[1082/1200], Temp: 0.0237, Energy: -44.857916-0.000731j
[2025-09-17 23:13:33] [Iter 2134/2250] R3[1083/1200], Temp: 0.0233, Energy: -44.855104+0.004816j
[2025-09-17 23:13:45] [Iter 2135/2250] R3[1084/1200], Temp: 0.0229, Energy: -44.853133+0.000646j
[2025-09-17 23:13:57] [Iter 2136/2250] R3[1085/1200], Temp: 0.0225, Energy: -44.842419-0.002439j
[2025-09-17 23:14:08] [Iter 2137/2250] R3[1086/1200], Temp: 0.0221, Energy: -44.849017-0.004595j
[2025-09-17 23:14:20] [Iter 2138/2250] R3[1087/1200], Temp: 0.0217, Energy: -44.850537+0.003580j
[2025-09-17 23:14:31] [Iter 2139/2250] R3[1088/1200], Temp: 0.0213, Energy: -44.849788-0.001242j
[2025-09-17 23:14:43] [Iter 2140/2250] R3[1089/1200], Temp: 0.0210, Energy: -44.846132-0.001678j
[2025-09-17 23:14:54] [Iter 2141/2250] R3[1090/1200], Temp: 0.0206, Energy: -44.851001-0.002057j
[2025-09-17 23:15:06] [Iter 2142/2250] R3[1091/1200], Temp: 0.0202, Energy: -44.845986+0.000578j
[2025-09-17 23:15:17] [Iter 2143/2250] R3[1092/1200], Temp: 0.0199, Energy: -44.858339+0.001662j
[2025-09-17 23:15:29] [Iter 2144/2250] R3[1093/1200], Temp: 0.0195, Energy: -44.842363-0.000580j
[2025-09-17 23:15:40] [Iter 2145/2250] R3[1094/1200], Temp: 0.0191, Energy: -44.850542-0.001081j
[2025-09-17 23:15:52] [Iter 2146/2250] R3[1095/1200], Temp: 0.0188, Energy: -44.855804-0.000883j
[2025-09-17 23:16:04] [Iter 2147/2250] R3[1096/1200], Temp: 0.0184, Energy: -44.850819-0.001453j
[2025-09-17 23:16:15] [Iter 2148/2250] R3[1097/1200], Temp: 0.0181, Energy: -44.858062+0.018399j
[2025-09-17 23:16:27] [Iter 2149/2250] R3[1098/1200], Temp: 0.0177, Energy: -44.845700-0.001094j
[2025-09-17 23:16:38] [Iter 2150/2250] R3[1099/1200], Temp: 0.0174, Energy: -44.849318-0.002064j
[2025-09-17 23:16:50] [Iter 2151/2250] R3[1100/1200], Temp: 0.0170, Energy: -44.850297+0.002023j
[2025-09-17 23:17:01] [Iter 2152/2250] R3[1101/1200], Temp: 0.0167, Energy: -44.839652+0.001612j
[2025-09-17 23:17:13] [Iter 2153/2250] R3[1102/1200], Temp: 0.0164, Energy: -44.842687-0.000496j
[2025-09-17 23:17:24] [Iter 2154/2250] R3[1103/1200], Temp: 0.0160, Energy: -44.858014+0.000423j
[2025-09-17 23:17:36] [Iter 2155/2250] R3[1104/1200], Temp: 0.0157, Energy: -44.855503-0.000368j
[2025-09-17 23:17:47] [Iter 2156/2250] R3[1105/1200], Temp: 0.0154, Energy: -44.852091-0.000879j
[2025-09-17 23:17:59] [Iter 2157/2250] R3[1106/1200], Temp: 0.0151, Energy: -44.836995+0.000994j
[2025-09-17 23:18:11] [Iter 2158/2250] R3[1107/1200], Temp: 0.0147, Energy: -44.846310-0.000896j
[2025-09-17 23:18:22] [Iter 2159/2250] R3[1108/1200], Temp: 0.0144, Energy: -44.861504-0.000244j
[2025-09-17 23:18:34] [Iter 2160/2250] R3[1109/1200], Temp: 0.0141, Energy: -44.846829-0.000520j
[2025-09-17 23:18:45] [Iter 2161/2250] R3[1110/1200], Temp: 0.0138, Energy: -44.856066-0.000791j
[2025-09-17 23:18:57] [Iter 2162/2250] R3[1111/1200], Temp: 0.0135, Energy: -44.852813-0.000228j
[2025-09-17 23:19:08] [Iter 2163/2250] R3[1112/1200], Temp: 0.0132, Energy: -44.844275-0.001729j
[2025-09-17 23:19:20] [Iter 2164/2250] R3[1113/1200], Temp: 0.0129, Energy: -44.854615-0.002659j
[2025-09-17 23:19:31] [Iter 2165/2250] R3[1114/1200], Temp: 0.0126, Energy: -44.851107+0.000078j
[2025-09-17 23:19:43] [Iter 2166/2250] R3[1115/1200], Temp: 0.0123, Energy: -44.850908-0.000538j
[2025-09-17 23:19:54] [Iter 2167/2250] R3[1116/1200], Temp: 0.0120, Energy: -44.849175-0.002082j
[2025-09-17 23:20:06] [Iter 2168/2250] R3[1117/1200], Temp: 0.0118, Energy: -44.853376-0.002013j
[2025-09-17 23:20:18] [Iter 2169/2250] R3[1118/1200], Temp: 0.0115, Energy: -44.841132+0.001919j
[2025-09-17 23:20:29] [Iter 2170/2250] R3[1119/1200], Temp: 0.0112, Energy: -44.850019+0.000167j
[2025-09-17 23:20:41] [Iter 2171/2250] R3[1120/1200], Temp: 0.0109, Energy: -44.846710-0.000066j
[2025-09-17 23:20:52] [Iter 2172/2250] R3[1121/1200], Temp: 0.0107, Energy: -44.850581+0.001405j
[2025-09-17 23:21:04] [Iter 2173/2250] R3[1122/1200], Temp: 0.0104, Energy: -44.850970-0.001107j
[2025-09-17 23:21:15] [Iter 2174/2250] R3[1123/1200], Temp: 0.0101, Energy: -44.846589+0.002276j
[2025-09-17 23:21:27] [Iter 2175/2250] R3[1124/1200], Temp: 0.0099, Energy: -44.854704-0.001504j
[2025-09-17 23:21:38] [Iter 2176/2250] R3[1125/1200], Temp: 0.0096, Energy: -44.855941+0.001500j
[2025-09-17 23:21:50] [Iter 2177/2250] R3[1126/1200], Temp: 0.0094, Energy: -44.864997-0.002373j
[2025-09-17 23:22:01] [Iter 2178/2250] R3[1127/1200], Temp: 0.0091, Energy: -44.851566+0.001682j
[2025-09-17 23:22:13] [Iter 2179/2250] R3[1128/1200], Temp: 0.0089, Energy: -44.854926-0.001890j
[2025-09-17 23:22:25] [Iter 2180/2250] R3[1129/1200], Temp: 0.0086, Energy: -44.851761-0.001493j
[2025-09-17 23:22:36] [Iter 2181/2250] R3[1130/1200], Temp: 0.0084, Energy: -44.835872+0.000882j
[2025-09-17 23:22:48] [Iter 2182/2250] R3[1131/1200], Temp: 0.0081, Energy: -44.850557+0.000774j
[2025-09-17 23:22:59] [Iter 2183/2250] R3[1132/1200], Temp: 0.0079, Energy: -44.849847-0.001527j
[2025-09-17 23:23:11] [Iter 2184/2250] R3[1133/1200], Temp: 0.0077, Energy: -44.861418+0.002226j
[2025-09-17 23:23:22] [Iter 2185/2250] R3[1134/1200], Temp: 0.0074, Energy: -44.851094-0.004168j
[2025-09-17 23:23:34] [Iter 2186/2250] R3[1135/1200], Temp: 0.0072, Energy: -44.849234-0.000573j
[2025-09-17 23:23:45] [Iter 2187/2250] R3[1136/1200], Temp: 0.0070, Energy: -44.852571+0.000545j
[2025-09-17 23:23:57] [Iter 2188/2250] R3[1137/1200], Temp: 0.0068, Energy: -44.854203-0.003482j
[2025-09-17 23:24:08] [Iter 2189/2250] R3[1138/1200], Temp: 0.0066, Energy: -44.830973-0.000155j
[2025-09-17 23:24:20] [Iter 2190/2250] R3[1139/1200], Temp: 0.0064, Energy: -44.855193+0.002477j
[2025-09-17 23:24:32] [Iter 2191/2250] R3[1140/1200], Temp: 0.0062, Energy: -44.851145+0.000053j
[2025-09-17 23:24:43] [Iter 2192/2250] R3[1141/1200], Temp: 0.0060, Energy: -44.846694-0.000033j
[2025-09-17 23:24:55] [Iter 2193/2250] R3[1142/1200], Temp: 0.0058, Energy: -44.853507-0.000129j
[2025-09-17 23:25:06] [Iter 2194/2250] R3[1143/1200], Temp: 0.0056, Energy: -44.855753+0.000494j
[2025-09-17 23:25:18] [Iter 2195/2250] R3[1144/1200], Temp: 0.0054, Energy: -44.847646-0.000073j
[2025-09-17 23:25:29] [Iter 2196/2250] R3[1145/1200], Temp: 0.0052, Energy: -44.849266-0.000468j
[2025-09-17 23:25:41] [Iter 2197/2250] R3[1146/1200], Temp: 0.0050, Energy: -44.853258+0.001425j
[2025-09-17 23:25:52] [Iter 2198/2250] R3[1147/1200], Temp: 0.0048, Energy: -44.860493+0.005010j
[2025-09-17 23:26:04] [Iter 2199/2250] R3[1148/1200], Temp: 0.0046, Energy: -44.850405+0.000416j
[2025-09-17 23:26:15] [Iter 2200/2250] R3[1149/1200], Temp: 0.0045, Energy: -44.856845-0.001625j
[2025-09-17 23:26:27] [Iter 2201/2250] R3[1150/1200], Temp: 0.0043, Energy: -44.844102+0.002532j
[2025-09-17 23:26:38] [Iter 2202/2250] R3[1151/1200], Temp: 0.0041, Energy: -44.854156-0.000681j
[2025-09-17 23:26:50] [Iter 2203/2250] R3[1152/1200], Temp: 0.0039, Energy: -44.850774+0.000235j
[2025-09-17 23:27:02] [Iter 2204/2250] R3[1153/1200], Temp: 0.0038, Energy: -44.855782+0.001883j
[2025-09-17 23:27:13] [Iter 2205/2250] R3[1154/1200], Temp: 0.0036, Energy: -44.855718-0.000290j
[2025-09-17 23:27:25] [Iter 2206/2250] R3[1155/1200], Temp: 0.0035, Energy: -44.850891-0.001378j
[2025-09-17 23:27:36] [Iter 2207/2250] R3[1156/1200], Temp: 0.0033, Energy: -44.854557+0.000631j
[2025-09-17 23:27:48] [Iter 2208/2250] R3[1157/1200], Temp: 0.0032, Energy: -44.857352-0.001695j
[2025-09-17 23:27:59] [Iter 2209/2250] R3[1158/1200], Temp: 0.0030, Energy: -44.850764-0.000592j
[2025-09-17 23:28:11] [Iter 2210/2250] R3[1159/1200], Temp: 0.0029, Energy: -44.853974+0.001360j
[2025-09-17 23:28:22] [Iter 2211/2250] R3[1160/1200], Temp: 0.0027, Energy: -44.862999-0.002530j
[2025-09-17 23:28:34] [Iter 2212/2250] R3[1161/1200], Temp: 0.0026, Energy: -44.849628-0.001383j
[2025-09-17 23:28:45] [Iter 2213/2250] R3[1162/1200], Temp: 0.0025, Energy: -44.858883+0.001985j
[2025-09-17 23:28:57] [Iter 2214/2250] R3[1163/1200], Temp: 0.0023, Energy: -44.845254-0.001446j
[2025-09-17 23:29:09] [Iter 2215/2250] R3[1164/1200], Temp: 0.0022, Energy: -44.861840-0.000528j
[2025-09-17 23:29:20] [Iter 2216/2250] R3[1165/1200], Temp: 0.0021, Energy: -44.843765-0.001701j
[2025-09-17 23:29:32] [Iter 2217/2250] R3[1166/1200], Temp: 0.0020, Energy: -44.864112-0.000193j
[2025-09-17 23:29:43] [Iter 2218/2250] R3[1167/1200], Temp: 0.0019, Energy: -44.858193-0.000073j
[2025-09-17 23:29:55] [Iter 2219/2250] R3[1168/1200], Temp: 0.0018, Energy: -44.857172+0.002472j
[2025-09-17 23:30:06] [Iter 2220/2250] R3[1169/1200], Temp: 0.0016, Energy: -44.841996-0.002267j
[2025-09-17 23:30:18] [Iter 2221/2250] R3[1170/1200], Temp: 0.0015, Energy: -44.861010-0.000274j
[2025-09-17 23:30:29] [Iter 2222/2250] R3[1171/1200], Temp: 0.0014, Energy: -44.852754-0.000477j
[2025-09-17 23:30:41] [Iter 2223/2250] R3[1172/1200], Temp: 0.0013, Energy: -44.852218-0.003066j
[2025-09-17 23:30:52] [Iter 2224/2250] R3[1173/1200], Temp: 0.0012, Energy: -44.840710+0.000498j
[2025-09-17 23:31:04] [Iter 2225/2250] R3[1174/1200], Temp: 0.0012, Energy: -44.855518+0.001772j
[2025-09-17 23:31:16] [Iter 2226/2250] R3[1175/1200], Temp: 0.0011, Energy: -44.849664+0.000182j
[2025-09-17 23:31:27] [Iter 2227/2250] R3[1176/1200], Temp: 0.0010, Energy: -44.847568+0.000282j
[2025-09-17 23:31:39] [Iter 2228/2250] R3[1177/1200], Temp: 0.0009, Energy: -44.849862-0.000345j
[2025-09-17 23:31:50] [Iter 2229/2250] R3[1178/1200], Temp: 0.0008, Energy: -44.858914-0.001010j
[2025-09-17 23:32:02] [Iter 2230/2250] R3[1179/1200], Temp: 0.0008, Energy: -44.861276-0.002235j
[2025-09-17 23:32:13] [Iter 2231/2250] R3[1180/1200], Temp: 0.0007, Energy: -44.847482-0.001375j
[2025-09-17 23:32:25] [Iter 2232/2250] R3[1181/1200], Temp: 0.0006, Energy: -44.846699-0.001447j
[2025-09-17 23:32:36] [Iter 2233/2250] R3[1182/1200], Temp: 0.0006, Energy: -44.845053+0.000325j
[2025-09-17 23:32:48] [Iter 2234/2250] R3[1183/1200], Temp: 0.0005, Energy: -44.840484+0.001309j
[2025-09-17 23:32:59] [Iter 2235/2250] R3[1184/1200], Temp: 0.0004, Energy: -44.847576-0.000855j
[2025-09-17 23:33:11] [Iter 2236/2250] R3[1185/1200], Temp: 0.0004, Energy: -44.852074+0.001273j
[2025-09-17 23:33:23] [Iter 2237/2250] R3[1186/1200], Temp: 0.0003, Energy: -44.859520-0.000750j
[2025-09-17 23:33:34] [Iter 2238/2250] R3[1187/1200], Temp: 0.0003, Energy: -44.851768-0.001498j
[2025-09-17 23:33:46] [Iter 2239/2250] R3[1188/1200], Temp: 0.0002, Energy: -44.844705-0.000936j
[2025-09-17 23:33:57] [Iter 2240/2250] R3[1189/1200], Temp: 0.0002, Energy: -44.854005+0.000120j
[2025-09-17 23:34:09] [Iter 2241/2250] R3[1190/1200], Temp: 0.0002, Energy: -44.850517-0.000333j
[2025-09-17 23:34:20] [Iter 2242/2250] R3[1191/1200], Temp: 0.0001, Energy: -44.856574+0.001315j
[2025-09-17 23:34:32] [Iter 2243/2250] R3[1192/1200], Temp: 0.0001, Energy: -44.851261+0.000720j
[2025-09-17 23:34:43] [Iter 2244/2250] R3[1193/1200], Temp: 0.0001, Energy: -44.857965-0.000878j
[2025-09-17 23:34:55] [Iter 2245/2250] R3[1194/1200], Temp: 0.0001, Energy: -44.841718+0.000569j
[2025-09-17 23:35:06] [Iter 2246/2250] R3[1195/1200], Temp: 0.0000, Energy: -44.843112+0.001114j
[2025-09-17 23:35:18] [Iter 2247/2250] R3[1196/1200], Temp: 0.0000, Energy: -44.846669+0.005728j
[2025-09-17 23:35:29] [Iter 2248/2250] R3[1197/1200], Temp: 0.0000, Energy: -44.842219+0.001501j
[2025-09-17 23:35:41] [Iter 2249/2250] R3[1198/1200], Temp: 0.0000, Energy: -44.837458+0.001843j
[2025-09-17 23:35:53] [Iter 2250/2250] R3[1199/1200], Temp: 0.0000, Energy: -44.855045+0.001618j
[2025-09-17 23:35:53] ✓ Checkpoint saved: checkpoint_iter_002250.pkl
[2025-09-17 23:35:53] ✅ Training completed | Restarts: 3
[2025-09-17 23:35:53] ============================================================
[2025-09-17 23:35:53] Training completed | Runtime: 26006.9s
[2025-09-17 23:35:57] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-17 23:35:57] ============================================================
