[2025-09-19 01:06:04] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.77/training/checkpoints/final_GCNN.pkl
[2025-09-19 01:06:04]   - 迭代次数: final
[2025-09-19 01:06:04]   - 能量: -42.969351-0.001503j ± 0.008241
[2025-09-19 01:06:04]   - 时间戳: 2025-09-19T01:05:22.078285+08:00
[2025-09-19 01:06:21] ✓ 变分状态参数已从checkpoint恢复
[2025-09-19 01:06:21] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-19 01:06:21] ==================================================
[2025-09-19 01:06:21] GCNN for Shastry-Sutherland Model
[2025-09-19 01:06:21] ==================================================
[2025-09-19 01:06:21] System parameters:
[2025-09-19 01:06:21]   - System size: L=5, N=100
[2025-09-19 01:06:21]   - System parameters: J1=0.76, J2=1.0, Q=0.0
[2025-09-19 01:06:21] --------------------------------------------------
[2025-09-19 01:06:21] Model parameters:
[2025-09-19 01:06:21]   - Number of layers = 4
[2025-09-19 01:06:21]   - Number of features = 4
[2025-09-19 01:06:21]   - Total parameters = 19628
[2025-09-19 01:06:21] --------------------------------------------------
[2025-09-19 01:06:21] Training parameters:
[2025-09-19 01:06:21]   - Learning rate: 0.015
[2025-09-19 01:06:21]   - Total iterations: 1050
[2025-09-19 01:06:21]   - Annealing cycles: 3
[2025-09-19 01:06:21]   - Initial period: 150
[2025-09-19 01:06:21]   - Period multiplier: 2.0
[2025-09-19 01:06:22]   - Temperature range: 0.0-1.0
[2025-09-19 01:06:22]   - Samples: 4096
[2025-09-19 01:06:22]   - Discarded samples: 0
[2025-09-19 01:06:22]   - Chunk size: 2048
[2025-09-19 01:06:22]   - Diagonal shift: 0.2
[2025-09-19 01:06:22]   - Gradient clipping: 1.0
[2025-09-19 01:06:22]   - Checkpoint enabled: interval=105
[2025-09-19 01:06:22]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.76/training/checkpoints
[2025-09-19 01:06:22] --------------------------------------------------
[2025-09-19 01:06:22] Device status:
[2025-09-19 01:06:22]   - Devices model: NVIDIA H200 NVL
[2025-09-19 01:06:22]   - Number of devices: 1
[2025-09-19 01:06:22]   - Sharding: True
[2025-09-19 01:06:22] ============================================================
[2025-09-19 01:07:20] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -42.317566+0.001430j
[2025-09-19 01:07:57] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -42.322798-0.001160j
[2025-09-19 01:08:09] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -42.317199-0.004201j
[2025-09-19 01:08:22] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -42.340484+0.001192j
[2025-09-19 01:08:34] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -42.339197-0.003988j
[2025-09-19 01:08:46] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -42.346922+0.000749j
[2025-09-19 01:08:59] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -42.342694-0.000757j
[2025-09-19 01:09:11] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -42.323896+0.003353j
[2025-09-19 01:09:23] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -42.349740-0.000958j
[2025-09-19 01:09:36] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -42.329653-0.002546j
[2025-09-19 01:09:48] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -42.325331+0.000564j
[2025-09-19 01:10:00] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -42.322596-0.005699j
[2025-09-19 01:10:13] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -42.333509+0.006994j
[2025-09-19 01:10:25] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -42.337933-0.001578j
[2025-09-19 01:10:38] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -42.332302+0.000267j
[2025-09-19 01:10:50] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -42.318948+0.000722j
[2025-09-19 01:11:02] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -42.337150-0.004438j
[2025-09-19 01:11:15] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -42.332871+0.001051j
[2025-09-19 01:11:27] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -42.342877+0.001320j
[2025-09-19 01:11:40] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -42.327744+0.004696j
[2025-09-19 01:11:52] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -42.342999+0.004388j
[2025-09-19 01:12:04] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -42.338492+0.000880j
[2025-09-19 01:12:17] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -42.324948-0.001422j
[2025-09-19 01:12:29] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -42.326097-0.000602j
[2025-09-19 01:12:41] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -42.323083+0.000668j
[2025-09-19 01:12:53] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -42.330631-0.000210j
[2025-09-19 01:13:06] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -42.340195-0.000687j
[2025-09-19 01:13:18] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -42.326004+0.004485j
[2025-09-19 01:13:30] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -42.327787+0.000218j
[2025-09-19 01:13:43] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -42.320735-0.001522j
[2025-09-19 01:13:55] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -42.315859+0.002030j
[2025-09-19 01:14:07] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -42.329997+0.002400j
[2025-09-19 01:14:20] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -42.332354+0.001662j
[2025-09-19 01:14:32] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -42.349573-0.002432j
[2025-09-19 01:14:44] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -42.335699-0.001447j
[2025-09-19 01:14:57] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -42.332548+0.001682j
[2025-09-19 01:15:09] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -42.347245-0.001768j
[2025-09-19 01:15:22] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -42.329264+0.000206j
[2025-09-19 01:15:34] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -42.331241-0.001204j
[2025-09-19 01:15:46] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -42.336121-0.000279j
[2025-09-19 01:15:59] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -42.338397-0.004371j
[2025-09-19 01:16:11] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -42.322664+0.003356j
[2025-09-19 01:16:23] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -42.332034-0.000023j
[2025-09-19 01:16:36] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -42.328754+0.004363j
[2025-09-19 01:16:48] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -42.329954-0.000545j
[2025-09-19 01:17:00] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -42.322210-0.001308j
[2025-09-19 01:17:12] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -42.337306+0.003708j
[2025-09-19 01:17:25] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -42.327857-0.000629j
[2025-09-19 01:17:37] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -42.333570+0.001955j
[2025-09-19 01:17:49] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -42.333864-0.001263j
[2025-09-19 01:18:02] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -42.331682+0.000326j
[2025-09-19 01:18:14] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -42.328662+0.001213j
[2025-09-19 01:18:27] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -42.339909+0.000467j
[2025-09-19 01:18:39] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -42.342570-0.008888j
[2025-09-19 01:18:51] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -42.337869+0.000579j
[2025-09-19 01:19:04] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -42.319804+0.001097j
[2025-09-19 01:19:16] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -42.346453-0.000680j
[2025-09-19 01:19:28] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -42.329888-0.001630j
[2025-09-19 01:19:41] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -42.338234+0.001038j
[2025-09-19 01:19:53] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -42.334037+0.002037j
[2025-09-19 01:20:05] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -42.319504+0.001114j
[2025-09-19 01:20:18] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -42.325437+0.000580j
[2025-09-19 01:20:30] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -42.344839-0.004540j
[2025-09-19 01:20:42] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -42.337493+0.001211j
[2025-09-19 01:20:55] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -42.345348-0.001160j
[2025-09-19 01:21:07] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -42.330808-0.000723j
[2025-09-19 01:21:19] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -42.327233-0.002750j
[2025-09-19 01:21:32] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -42.324518-0.001507j
[2025-09-19 01:21:44] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -42.340948-0.001081j
[2025-09-19 01:21:56] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -42.331206-0.005611j
[2025-09-19 01:22:09] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -42.334432-0.000382j
[2025-09-19 01:22:21] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -42.327827-0.002539j
[2025-09-19 01:22:33] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -42.339696-0.000115j
[2025-09-19 01:22:46] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -42.344150-0.002725j
[2025-09-19 01:22:58] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -42.347167-0.000876j
[2025-09-19 01:23:10] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -42.347579+0.001013j
[2025-09-19 01:23:23] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -42.329916-0.003483j
[2025-09-19 01:23:35] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -42.342840+0.004019j
[2025-09-19 01:23:47] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -42.332218+0.001033j
[2025-09-19 01:24:00] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -42.333745-0.001309j
[2025-09-19 01:24:12] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -42.334836+0.000319j
[2025-09-19 01:24:24] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -42.331063-0.000027j
[2025-09-19 01:24:37] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -42.326974-0.001505j
[2025-09-19 01:24:49] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -42.327911+0.000838j
[2025-09-19 01:25:01] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -42.343913-0.002254j
[2025-09-19 01:25:14] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -42.342221-0.004233j
[2025-09-19 01:25:26] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -42.329493+0.001085j
[2025-09-19 01:25:38] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -42.331704-0.002076j
[2025-09-19 01:25:51] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -42.333982-0.000044j
[2025-09-19 01:26:03] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -42.348170-0.000892j
[2025-09-19 01:26:15] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -42.336262-0.000844j
[2025-09-19 01:26:28] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -42.328427+0.001942j
[2025-09-19 01:26:40] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -42.344100+0.000918j
[2025-09-19 01:26:52] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -42.341066+0.000521j
[2025-09-19 01:27:05] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -42.334356+0.001254j
[2025-09-19 01:27:17] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -42.344713-0.001123j
[2025-09-19 01:27:29] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -42.342039-0.001424j
[2025-09-19 01:27:41] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -42.331013-0.001912j
[2025-09-19 01:27:54] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -42.325273-0.003237j
[2025-09-19 01:28:06] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -42.326726+0.000142j
[2025-09-19 01:28:18] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -42.325482-0.002931j
[2025-09-19 01:28:31] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -42.346066-0.000280j
[2025-09-19 01:28:43] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -42.339238-0.001976j
[2025-09-19 01:28:55] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -42.311805+0.000729j
[2025-09-19 01:29:08] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -42.353071+0.002098j
[2025-09-19 01:29:08] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-19 01:29:20] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -42.331494+0.002669j
[2025-09-19 01:29:32] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -42.326754+0.000145j
[2025-09-19 01:29:45] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -42.336481-0.000270j
[2025-09-19 01:29:57] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -42.347437+0.000003j
[2025-09-19 01:30:09] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -42.329321+0.000840j
[2025-09-19 01:30:22] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -42.338281-0.002218j
[2025-09-19 01:30:34] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -42.327918-0.004340j
[2025-09-19 01:30:46] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -42.335020-0.003003j
[2025-09-19 01:30:59] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -42.347548+0.001654j
[2025-09-19 01:31:11] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -42.340005-0.003902j
[2025-09-19 01:31:23] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -42.321306-0.002473j
[2025-09-19 01:31:36] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -42.334222+0.002775j
[2025-09-19 01:31:48] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -42.342749-0.002551j
[2025-09-19 01:32:00] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -42.343635-0.000011j
[2025-09-19 01:32:13] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -42.335313+0.002392j
[2025-09-19 01:32:25] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -42.332381-0.001428j
[2025-09-19 01:32:37] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -42.340581-0.002922j
[2025-09-19 01:32:50] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -42.320817-0.003260j
[2025-09-19 01:33:02] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -42.337643-0.002233j
[2025-09-19 01:33:14] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -42.342493-0.001706j
[2025-09-19 01:33:27] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -42.343035-0.002532j
[2025-09-19 01:33:39] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -42.333528-0.002283j
[2025-09-19 01:33:51] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -42.338476+0.001114j
[2025-09-19 01:34:03] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -42.348912+0.001203j
[2025-09-19 01:34:16] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -42.339829-0.003017j
[2025-09-19 01:34:28] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -42.332284+0.000151j
[2025-09-19 01:34:40] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -42.321312+0.000608j
[2025-09-19 01:34:52] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -42.335600+0.001482j
[2025-09-19 01:35:05] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -42.343707+0.004077j
[2025-09-19 01:35:17] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -42.328475-0.001458j
[2025-09-19 01:35:29] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -42.333099-0.000421j
[2025-09-19 01:35:42] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -42.342162-0.000248j
[2025-09-19 01:35:54] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -42.332695+0.003240j
[2025-09-19 01:36:06] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -42.325028+0.002070j
[2025-09-19 01:36:19] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -42.338444-0.000778j
[2025-09-19 01:36:31] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -42.319255+0.001367j
[2025-09-19 01:36:43] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -42.336752-0.002573j
[2025-09-19 01:36:55] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -42.339844+0.003235j
[2025-09-19 01:37:08] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -42.326043-0.000512j
[2025-09-19 01:37:20] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -42.343693+0.000012j
[2025-09-19 01:37:32] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -42.343280-0.000679j
[2025-09-19 01:37:45] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -42.330827+0.004897j
[2025-09-19 01:37:57] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -42.331095-0.004456j
[2025-09-19 01:38:09] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -42.349166+0.003012j
[2025-09-19 01:38:22] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -42.345399+0.000786j
[2025-09-19 01:38:22] RESTART #1 | Period: 300
[2025-09-19 01:38:34] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -42.333474-0.001318j
[2025-09-19 01:38:46] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -42.335378+0.006852j
[2025-09-19 01:38:59] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -42.325064+0.000710j
[2025-09-19 01:39:11] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -42.328298-0.000027j
[2025-09-19 01:39:23] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -42.335155-0.001964j
[2025-09-19 01:39:36] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -42.345839+0.002359j
[2025-09-19 01:39:48] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -42.332328-0.002147j
[2025-09-19 01:40:00] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -42.345225+0.000409j
[2025-09-19 01:40:13] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -42.342003-0.001315j
[2025-09-19 01:40:25] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -42.347247-0.002705j
[2025-09-19 01:40:37] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -42.327436-0.002947j
[2025-09-19 01:40:50] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -42.342843+0.000346j
[2025-09-19 01:41:02] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -42.333712-0.002096j
[2025-09-19 01:41:14] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -42.331261-0.000723j
[2025-09-19 01:41:26] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -42.328175+0.000419j
[2025-09-19 01:41:39] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -42.317896+0.003656j
[2025-09-19 01:41:51] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -42.325862-0.004072j
[2025-09-19 01:42:03] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -42.354351+0.003505j
[2025-09-19 01:42:16] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -42.311914+0.006922j
[2025-09-19 01:42:28] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -42.335511-0.002409j
[2025-09-19 01:42:40] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -42.332272+0.005646j
[2025-09-19 01:42:53] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -42.338274-0.000979j
[2025-09-19 01:43:05] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -42.329087-0.004303j
[2025-09-19 01:43:17] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -42.334061-0.000994j
[2025-09-19 01:43:30] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -42.344138+0.000055j
[2025-09-19 01:43:42] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -42.326307-0.003748j
[2025-09-19 01:43:54] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -42.339622+0.003065j
[2025-09-19 01:44:07] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -42.337731+0.001610j
[2025-09-19 01:44:19] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -42.341406+0.003599j
[2025-09-19 01:44:31] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -42.332475+0.000458j
[2025-09-19 01:44:44] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -42.347872-0.006765j
[2025-09-19 01:44:56] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -42.341341-0.001537j
[2025-09-19 01:45:08] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -42.345101-0.000169j
[2025-09-19 01:45:21] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -42.333935+0.000553j
[2025-09-19 01:45:33] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -42.326386-0.003637j
[2025-09-19 01:45:46] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -42.336272+0.002919j
[2025-09-19 01:45:58] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -42.345781+0.004824j
[2025-09-19 01:46:10] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -42.333584+0.004447j
[2025-09-19 01:46:23] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -42.327523+0.000710j
[2025-09-19 01:46:35] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -42.334585-0.000157j
[2025-09-19 01:46:47] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -42.341938-0.002354j
[2025-09-19 01:47:00] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -42.318468-0.000699j
[2025-09-19 01:47:12] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -42.336031+0.001742j
[2025-09-19 01:47:24] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -42.338037-0.002076j
[2025-09-19 01:47:37] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -42.329484-0.001574j
[2025-09-19 01:47:49] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -42.332403-0.001147j
[2025-09-19 01:48:01] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -42.336781+0.002923j
[2025-09-19 01:48:14] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -42.329222-0.000029j
[2025-09-19 01:48:26] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -42.332188-0.000365j
[2025-09-19 01:48:38] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -42.335539+0.001532j
[2025-09-19 01:48:51] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -42.334024-0.003084j
[2025-09-19 01:49:03] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -42.348892+0.003250j
[2025-09-19 01:49:16] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -42.329167-0.001227j
[2025-09-19 01:49:28] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -42.344497-0.001472j
[2025-09-19 01:49:40] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -42.332561+0.001391j
[2025-09-19 01:49:52] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -42.342684+0.003447j
[2025-09-19 01:50:05] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -42.347847+0.000262j
[2025-09-19 01:50:17] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -42.326403-0.000304j
[2025-09-19 01:50:29] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -42.330028+0.002538j
[2025-09-19 01:50:42] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -42.341928-0.003958j
[2025-09-19 01:50:42] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-19 01:50:54] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -42.331707-0.000284j
[2025-09-19 01:51:06] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -42.330429+0.001940j
[2025-09-19 01:51:19] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -42.333217+0.001051j
[2025-09-19 01:51:31] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -42.327707-0.001570j
[2025-09-19 01:51:43] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -42.326031-0.001186j
[2025-09-19 01:51:55] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -42.324624-0.001493j
[2025-09-19 01:52:08] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -42.339403+0.001236j
[2025-09-19 01:52:20] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -42.318352+0.001328j
[2025-09-19 01:52:33] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -42.340585-0.000839j
[2025-09-19 01:52:45] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -42.312014+0.001765j
[2025-09-19 01:52:57] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -42.333761-0.002767j
[2025-09-19 01:53:09] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -42.336427+0.001750j
[2025-09-19 01:53:22] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -42.323888-0.004429j
[2025-09-19 01:53:34] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -42.333896-0.000694j
[2025-09-19 01:53:46] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -42.329869+0.002966j
[2025-09-19 01:53:59] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -42.348661+0.002492j
[2025-09-19 01:54:11] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -42.342555+0.002015j
[2025-09-19 01:54:23] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -42.335144+0.002144j
[2025-09-19 01:54:36] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -42.331721-0.000788j
[2025-09-19 01:54:48] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -42.330730-0.001116j
[2025-09-19 01:55:00] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -42.338438+0.001650j
[2025-09-19 01:55:13] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -42.332208-0.002638j
[2025-09-19 01:55:25] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -42.331077+0.000959j
[2025-09-19 01:55:37] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -42.335025-0.000729j
[2025-09-19 01:55:50] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -42.343174+0.000998j
[2025-09-19 01:56:02] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -42.341722-0.001986j
[2025-09-19 01:56:14] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -42.331604+0.000706j
[2025-09-19 01:56:27] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -42.340424+0.001192j
[2025-09-19 01:56:39] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -42.334512+0.001003j
[2025-09-19 01:56:52] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -42.322467+0.001916j
[2025-09-19 01:57:04] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -42.330160-0.002696j
[2025-09-19 01:57:16] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -42.350183-0.004138j
[2025-09-19 01:57:29] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -42.345404+0.005181j
[2025-09-19 01:57:41] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -42.339479-0.003392j
[2025-09-19 01:57:53] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -42.334795+0.000216j
[2025-09-19 01:58:06] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -42.339209-0.001274j
[2025-09-19 01:58:18] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -42.324308+0.000006j
[2025-09-19 01:58:30] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -42.326512-0.000082j
[2025-09-19 01:58:43] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -42.314352-0.002817j
[2025-09-19 01:58:55] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -42.334912-0.000340j
[2025-09-19 01:59:07] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -42.325922+0.001480j
[2025-09-19 01:59:20] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -42.340794-0.001959j
[2025-09-19 01:59:32] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -42.327440+0.000580j
[2025-09-19 01:59:44] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -42.315751+0.003818j
[2025-09-19 01:59:57] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -42.329338-0.002421j
[2025-09-19 02:00:09] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -42.354457-0.004745j
[2025-09-19 02:00:21] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -42.321883+0.000218j
[2025-09-19 02:00:34] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -42.347027-0.000919j
[2025-09-19 02:00:46] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -42.331656-0.001906j
[2025-09-19 02:00:58] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -42.324529-0.004309j
[2025-09-19 02:01:11] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -42.339764-0.004148j
[2025-09-19 02:01:23] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -42.351727+0.001038j
[2025-09-19 02:01:35] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -42.334387+0.001463j
[2025-09-19 02:01:48] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -42.319430+0.003473j
[2025-09-19 02:02:00] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -42.330248-0.002326j
[2025-09-19 02:02:12] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -42.326884-0.001784j
[2025-09-19 02:02:25] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -42.347114+0.000997j
[2025-09-19 02:02:37] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -42.338463-0.002253j
[2025-09-19 02:02:49] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -42.331129-0.001472j
[2025-09-19 02:03:02] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -42.333898+0.000058j
[2025-09-19 02:03:14] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -42.328106+0.002214j
[2025-09-19 02:03:26] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -42.336907+0.000804j
[2025-09-19 02:03:39] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -42.348488-0.002521j
[2025-09-19 02:03:51] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -42.340432+0.004819j
[2025-09-19 02:04:03] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -42.336587-0.003270j
[2025-09-19 02:04:16] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -42.333424+0.000546j
[2025-09-19 02:04:28] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -42.329100+0.003177j
[2025-09-19 02:04:40] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -42.340206+0.004946j
[2025-09-19 02:04:53] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -42.328693+0.002081j
[2025-09-19 02:05:05] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -42.333427-0.000177j
[2025-09-19 02:05:17] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -42.351868+0.002439j
[2025-09-19 02:05:30] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -42.326867+0.000017j
[2025-09-19 02:05:42] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -42.323434+0.000932j
[2025-09-19 02:05:54] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -42.353996+0.001219j
[2025-09-19 02:06:07] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -42.355273+0.002493j
[2025-09-19 02:06:19] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -42.332290+0.000710j
[2025-09-19 02:06:31] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -42.329979+0.000067j
[2025-09-19 02:06:44] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -42.344470+0.001689j
[2025-09-19 02:06:56] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -42.343556+0.000116j
[2025-09-19 02:07:08] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -42.353112-0.001751j
[2025-09-19 02:07:21] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -42.331119-0.000643j
[2025-09-19 02:07:33] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -42.331921-0.000865j
[2025-09-19 02:07:45] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -42.334363+0.000793j
[2025-09-19 02:07:58] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -42.331822+0.002157j
[2025-09-19 02:08:10] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -42.329576-0.006000j
[2025-09-19 02:08:22] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -42.335058+0.002864j
[2025-09-19 02:08:35] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -42.323987+0.000217j
[2025-09-19 02:08:47] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -42.335421-0.003007j
[2025-09-19 02:08:59] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -42.335996-0.003251j
[2025-09-19 02:09:12] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -42.338418-0.000179j
[2025-09-19 02:09:24] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -42.335379+0.001664j
[2025-09-19 02:09:36] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -42.343968-0.003748j
[2025-09-19 02:09:49] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -42.338882-0.003510j
[2025-09-19 02:10:01] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -42.356868+0.001477j
[2025-09-19 02:10:13] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -42.327760+0.003981j
[2025-09-19 02:10:26] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -42.339008-0.000775j
[2025-09-19 02:10:38] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -42.312598-0.001829j
[2025-09-19 02:10:50] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -42.325041-0.001062j
[2025-09-19 02:11:03] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -42.334596+0.001742j
[2025-09-19 02:11:15] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -42.347561+0.000201j
[2025-09-19 02:11:27] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -42.336435-0.006796j
[2025-09-19 02:11:40] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -42.330775+0.000684j
[2025-09-19 02:11:52] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -42.340882+0.008398j
[2025-09-19 02:12:04] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -42.358263-0.003807j
[2025-09-19 02:12:17] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -42.327915-0.002888j
[2025-09-19 02:12:17] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-19 02:12:29] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -42.328495+0.002521j
[2025-09-19 02:12:41] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -42.328158-0.000383j
[2025-09-19 02:12:54] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -42.334479-0.000759j
[2025-09-19 02:13:06] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -42.343163+0.001640j
[2025-09-19 02:13:18] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -42.333772+0.003675j
[2025-09-19 02:13:31] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -42.329994+0.001621j
[2025-09-19 02:13:43] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -42.327725+0.000185j
[2025-09-19 02:13:55] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -42.331182-0.002097j
[2025-09-19 02:14:08] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -42.330653-0.002164j
[2025-09-19 02:14:20] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -42.314912+0.003673j
[2025-09-19 02:14:32] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -42.341625-0.001568j
[2025-09-19 02:14:45] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -42.338734+0.001601j
[2025-09-19 02:14:57] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -42.338338+0.000504j
[2025-09-19 02:15:09] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -42.336225-0.006018j
[2025-09-19 02:15:22] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -42.344482-0.001420j
[2025-09-19 02:15:34] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -42.324784-0.002532j
[2025-09-19 02:15:46] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -42.351054-0.000922j
[2025-09-19 02:15:59] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -42.332571+0.000041j
[2025-09-19 02:16:11] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -42.322662+0.001452j
[2025-09-19 02:16:24] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -42.326644-0.001973j
[2025-09-19 02:16:36] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -42.340754+0.002154j
[2025-09-19 02:16:48] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -42.336945+0.001060j
[2025-09-19 02:17:01] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -42.331905+0.000165j
[2025-09-19 02:17:13] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -42.328483-0.002758j
[2025-09-19 02:17:25] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -42.347308-0.000548j
[2025-09-19 02:17:37] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -42.336638+0.002433j
[2025-09-19 02:17:50] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -42.339275+0.006646j
[2025-09-19 02:18:02] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -42.326629-0.000691j
[2025-09-19 02:18:14] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -42.341337-0.002543j
[2025-09-19 02:18:27] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -42.324052-0.001071j
[2025-09-19 02:18:39] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -42.345059-0.000506j
[2025-09-19 02:18:51] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -42.341834-0.002378j
[2025-09-19 02:19:04] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -42.342376+0.001329j
[2025-09-19 02:19:16] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -42.314622-0.001851j
[2025-09-19 02:19:28] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -42.329586+0.002089j
[2025-09-19 02:19:41] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -42.339269-0.000691j
[2025-09-19 02:19:53] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -42.349373+0.001754j
[2025-09-19 02:20:05] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -42.317915-0.001388j
[2025-09-19 02:20:18] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -42.341956-0.004230j
[2025-09-19 02:20:30] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -42.323368+0.000714j
[2025-09-19 02:20:42] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -42.336257+0.000790j
[2025-09-19 02:20:55] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -42.340947-0.002629j
[2025-09-19 02:21:07] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -42.348913-0.002641j
[2025-09-19 02:21:19] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -42.334732+0.001135j
[2025-09-19 02:21:32] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -42.327401-0.000654j
[2025-09-19 02:21:44] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -42.344752+0.003171j
[2025-09-19 02:21:56] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -42.338853-0.000115j
[2025-09-19 02:22:09] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -42.334392-0.000783j
[2025-09-19 02:22:21] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -42.335773+0.000617j
[2025-09-19 02:22:33] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -42.328305+0.000622j
[2025-09-19 02:22:46] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -42.314790+0.002101j
[2025-09-19 02:22:58] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -42.338476-0.001566j
[2025-09-19 02:23:10] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -42.326142-0.000785j
[2025-09-19 02:23:23] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -42.348401-0.000825j
[2025-09-19 02:23:35] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -42.338310-0.003423j
[2025-09-19 02:23:47] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -42.346310-0.000352j
[2025-09-19 02:24:00] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -42.349177-0.002695j
[2025-09-19 02:24:12] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -42.336232-0.003832j
[2025-09-19 02:24:24] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -42.325270+0.000586j
[2025-09-19 02:24:37] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -42.341578+0.001371j
[2025-09-19 02:24:49] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -42.342662-0.003441j
[2025-09-19 02:25:01] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -42.342326+0.003572j
[2025-09-19 02:25:14] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -42.332987+0.004179j
[2025-09-19 02:25:26] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -42.339161+0.000091j
[2025-09-19 02:25:38] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -42.336918-0.002424j
[2025-09-19 02:25:51] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -42.331810+0.000295j
[2025-09-19 02:26:03] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -42.331430-0.001702j
[2025-09-19 02:26:15] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -42.333544-0.000957j
[2025-09-19 02:26:28] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -42.327275+0.000687j
[2025-09-19 02:26:40] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -42.334390-0.001910j
[2025-09-19 02:26:52] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -42.327570-0.000218j
[2025-09-19 02:27:05] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -42.332581-0.004779j
[2025-09-19 02:27:17] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -42.336351+0.001325j
[2025-09-19 02:27:29] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -42.309608-0.000413j
[2025-09-19 02:27:42] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -42.337307-0.003050j
[2025-09-19 02:27:54] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -42.332631-0.000262j
[2025-09-19 02:28:06] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -42.337362+0.003106j
[2025-09-19 02:28:19] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -42.325160+0.000657j
[2025-09-19 02:28:31] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -42.340454+0.005190j
[2025-09-19 02:28:43] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -42.333153-0.002271j
[2025-09-19 02:28:56] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -42.341659-0.000035j
[2025-09-19 02:29:08] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -42.338147+0.003599j
[2025-09-19 02:29:20] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -42.332071-0.001408j
[2025-09-19 02:29:33] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -42.321557+0.001400j
[2025-09-19 02:29:45] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -42.329588-0.000335j
[2025-09-19 02:29:57] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -42.335205-0.000605j
[2025-09-19 02:30:10] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -42.340742-0.000712j
[2025-09-19 02:30:22] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -42.340007-0.001673j
[2025-09-19 02:30:34] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -42.346389-0.001755j
[2025-09-19 02:30:47] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -42.338708-0.002570j
[2025-09-19 02:30:59] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -42.336455+0.003419j
[2025-09-19 02:31:11] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -42.344291+0.001169j
[2025-09-19 02:31:24] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -42.328238+0.000462j
[2025-09-19 02:31:36] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -42.331380-0.000924j
[2025-09-19 02:31:49] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -42.338091-0.001326j
[2025-09-19 02:32:01] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -42.326942+0.003819j
[2025-09-19 02:32:13] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -42.326923+0.003032j
[2025-09-19 02:32:25] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -42.341510-0.000814j
[2025-09-19 02:32:38] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -42.342333+0.001125j
[2025-09-19 02:32:50] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -42.338625+0.000525j
[2025-09-19 02:33:02] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -42.329181+0.001065j
[2025-09-19 02:33:15] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -42.313819-0.000607j
[2025-09-19 02:33:27] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -42.336065-0.001505j
[2025-09-19 02:33:39] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -42.341938+0.002331j
[2025-09-19 02:33:52] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -42.342113-0.000339j
[2025-09-19 02:33:52] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-19 02:34:04] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -42.347981+0.000035j
[2025-09-19 02:34:16] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -42.327739-0.000442j
[2025-09-19 02:34:29] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -42.341343+0.000172j
[2025-09-19 02:34:41] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -42.337121-0.005378j
[2025-09-19 02:34:53] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -42.355007+0.003634j
[2025-09-19 02:35:05] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -42.337456-0.001353j
[2025-09-19 02:35:18] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -42.329444-0.000873j
[2025-09-19 02:35:30] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -42.333602+0.000986j
[2025-09-19 02:35:42] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -42.332233-0.001342j
[2025-09-19 02:35:55] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -42.332406+0.000008j
[2025-09-19 02:36:07] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -42.335361+0.001894j
[2025-09-19 02:36:19] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -42.326711+0.000096j
[2025-09-19 02:36:32] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -42.313411-0.006049j
[2025-09-19 02:36:44] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -42.336849+0.001469j
[2025-09-19 02:36:56] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -42.333891+0.002862j
[2025-09-19 02:37:09] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -42.325491-0.000159j
[2025-09-19 02:37:21] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -42.338992-0.003533j
[2025-09-19 02:37:33] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -42.350888-0.000440j
[2025-09-19 02:37:46] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -42.336713+0.001357j
[2025-09-19 02:37:58] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -42.331379+0.002116j
[2025-09-19 02:38:10] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -42.353009+0.003631j
[2025-09-19 02:38:23] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -42.322764-0.000829j
[2025-09-19 02:38:35] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -42.337287+0.001561j
[2025-09-19 02:38:47] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -42.331167-0.003187j
[2025-09-19 02:39:00] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -42.343463-0.002611j
[2025-09-19 02:39:12] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -42.318913-0.003220j
[2025-09-19 02:39:24] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -42.338284-0.001052j
[2025-09-19 02:39:37] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -42.333525+0.002542j
[2025-09-19 02:39:49] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -42.334007+0.000928j
[2025-09-19 02:40:01] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -42.340504+0.004237j
[2025-09-19 02:40:01] RESTART #2 | Period: 600
[2025-09-19 02:40:14] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -42.338105-0.000600j
[2025-09-19 02:40:26] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -42.338892-0.000730j
[2025-09-19 02:40:38] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -42.328876+0.004870j
[2025-09-19 02:40:51] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -42.326925-0.001745j
[2025-09-19 02:41:03] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -42.337120-0.001113j
[2025-09-19 02:41:15] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -42.343347-0.001857j
[2025-09-19 02:41:28] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -42.325939+0.001419j
[2025-09-19 02:41:40] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -42.331196-0.000044j
[2025-09-19 02:41:52] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -42.328864-0.003317j
[2025-09-19 02:42:05] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -42.331133-0.000428j
[2025-09-19 02:42:17] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -42.332311-0.000906j
[2025-09-19 02:42:29] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -42.332858+0.000995j
[2025-09-19 02:42:42] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -42.340510-0.002378j
[2025-09-19 02:42:54] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -42.338240+0.000427j
[2025-09-19 02:43:07] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -42.337087+0.004129j
[2025-09-19 02:43:19] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -42.325309-0.000907j
[2025-09-19 02:43:31] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -42.330646-0.001111j
[2025-09-19 02:43:44] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -42.324684-0.001776j
[2025-09-19 02:43:56] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -42.329874+0.002537j
[2025-09-19 02:44:08] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -42.340902-0.000375j
[2025-09-19 02:44:21] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -42.325164-0.006046j
[2025-09-19 02:44:33] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -42.335394-0.001213j
[2025-09-19 02:44:45] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -42.334237+0.001524j
[2025-09-19 02:44:58] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -42.333455-0.001300j
[2025-09-19 02:45:10] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -42.329933-0.004565j
[2025-09-19 02:45:22] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -42.336285+0.000344j
[2025-09-19 02:45:34] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -42.345906+0.006618j
[2025-09-19 02:45:47] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -42.323610-0.000631j
[2025-09-19 02:45:59] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -42.327588+0.006302j
[2025-09-19 02:46:11] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -42.332326+0.003865j
[2025-09-19 02:46:24] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -42.339874+0.000352j
[2025-09-19 02:46:36] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -42.334841-0.000478j
[2025-09-19 02:46:48] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -42.325426-0.002209j
[2025-09-19 02:47:01] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -42.332372-0.000548j
[2025-09-19 02:47:13] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -42.331077-0.000218j
[2025-09-19 02:47:25] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -42.349411-0.002611j
[2025-09-19 02:47:38] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -42.342862+0.005117j
[2025-09-19 02:47:50] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -42.336568-0.000230j
[2025-09-19 02:48:02] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -42.336465+0.000858j
[2025-09-19 02:48:15] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -42.337967+0.006320j
[2025-09-19 02:48:27] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -42.347896+0.002178j
[2025-09-19 02:48:39] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -42.325935+0.001472j
[2025-09-19 02:48:52] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -42.335827+0.001022j
[2025-09-19 02:49:04] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -42.330591-0.005433j
[2025-09-19 02:49:16] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -42.345271+0.003367j
[2025-09-19 02:49:29] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -42.323513+0.001731j
[2025-09-19 02:49:41] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -42.338486+0.005936j
[2025-09-19 02:49:53] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -42.320094+0.000328j
[2025-09-19 02:50:06] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -42.329787-0.003902j
[2025-09-19 02:50:18] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -42.325065-0.000157j
[2025-09-19 02:50:30] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -42.344005-0.003819j
[2025-09-19 02:50:43] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -42.340279-0.001541j
[2025-09-19 02:50:55] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -42.343873-0.002231j
[2025-09-19 02:51:07] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -42.345755+0.000408j
[2025-09-19 02:51:20] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -42.333765+0.000035j
[2025-09-19 02:51:32] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -42.331943-0.004962j
[2025-09-19 02:51:44] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -42.327634-0.000765j
[2025-09-19 02:51:57] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -42.322555-0.000380j
[2025-09-19 02:52:09] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -42.346991+0.001146j
[2025-09-19 02:52:21] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -42.326573+0.002564j
[2025-09-19 02:52:33] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -42.348368-0.000676j
[2025-09-19 02:52:45] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -42.330699+0.005146j
[2025-09-19 02:52:58] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -42.332374-0.001675j
[2025-09-19 02:53:10] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -42.340724+0.004755j
[2025-09-19 02:53:22] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -42.352363+0.000401j
[2025-09-19 02:53:34] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -42.337266-0.000530j
[2025-09-19 02:53:46] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -42.333564+0.003776j
[2025-09-19 02:53:59] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -42.330194-0.002520j
[2025-09-19 02:54:10] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -42.347022-0.003526j
[2025-09-19 02:54:23] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -42.321203-0.001489j
[2025-09-19 02:54:35] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -42.340875-0.002225j
[2025-09-19 02:54:47] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -42.333382+0.002151j
[2025-09-19 02:55:00] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -42.332064-0.000501j
[2025-09-19 02:55:12] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -42.334103+0.000940j
[2025-09-19 02:55:24] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -42.337787+0.004193j
[2025-09-19 02:55:25] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-19 02:55:37] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -42.332670+0.003246j
[2025-09-19 02:55:49] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -42.337950-0.001060j
[2025-09-19 02:56:01] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -42.329394-0.000621j
[2025-09-19 02:56:14] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -42.337021-0.001356j
[2025-09-19 02:56:26] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -42.344209+0.000085j
[2025-09-19 02:56:38] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -42.329616+0.000140j
[2025-09-19 02:56:51] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -42.353717+0.000144j
[2025-09-19 02:57:03] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -42.335461-0.002605j
[2025-09-19 02:57:16] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -42.330956-0.001434j
[2025-09-19 02:57:28] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -42.326875+0.001124j
[2025-09-19 02:57:40] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -42.346358+0.004882j
[2025-09-19 02:57:53] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -42.354798+0.000682j
[2025-09-19 02:58:05] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -42.349247+0.002031j
[2025-09-19 02:58:17] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -42.326112+0.003681j
[2025-09-19 02:58:30] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -42.347917-0.001436j
[2025-09-19 02:58:42] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -42.342066-0.003074j
[2025-09-19 02:58:54] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -42.330204+0.001488j
[2025-09-19 02:59:07] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -42.337758-0.001023j
[2025-09-19 02:59:19] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -42.332244-0.001965j
[2025-09-19 02:59:31] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -42.331450-0.000399j
[2025-09-19 02:59:44] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -42.325520-0.000631j
[2025-09-19 02:59:56] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -42.342659-0.002681j
[2025-09-19 03:00:08] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -42.329252+0.001553j
[2025-09-19 03:00:21] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -42.327190-0.001491j
[2025-09-19 03:00:33] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -42.336900-0.000128j
[2025-09-19 03:00:45] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -42.343830+0.001063j
[2025-09-19 03:00:58] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -42.340690+0.003219j
[2025-09-19 03:01:10] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -42.316753+0.002250j
[2025-09-19 03:01:22] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -42.355683-0.000671j
[2025-09-19 03:01:35] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -42.355979+0.003450j
[2025-09-19 03:01:47] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -42.342194-0.001728j
[2025-09-19 03:01:59] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -42.336694+0.001115j
[2025-09-19 03:02:12] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -42.329966+0.002301j
[2025-09-19 03:02:24] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -42.350248+0.003139j
[2025-09-19 03:02:36] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -42.313828+0.001344j
[2025-09-19 03:02:49] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -42.336059-0.000026j
[2025-09-19 03:03:01] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -42.338156+0.002008j
[2025-09-19 03:03:13] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -42.335098+0.002316j
[2025-09-19 03:03:26] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -42.340769-0.000814j
[2025-09-19 03:03:38] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -42.344874-0.000412j
[2025-09-19 03:03:50] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -42.332841+0.002175j
[2025-09-19 03:04:03] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -42.334928+0.002499j
[2025-09-19 03:04:15] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -42.332881-0.000902j
[2025-09-19 03:04:27] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -42.328317+0.000441j
[2025-09-19 03:04:40] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -42.339883+0.001207j
[2025-09-19 03:04:52] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -42.336847+0.000047j
[2025-09-19 03:05:04] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -42.327210-0.001771j
[2025-09-19 03:05:16] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -42.340642+0.001016j
[2025-09-19 03:05:29] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -42.336872+0.001750j
[2025-09-19 03:05:41] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -42.333168+0.001185j
[2025-09-19 03:05:53] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -42.335350-0.002775j
[2025-09-19 03:06:06] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -42.327416-0.002087j
[2025-09-19 03:06:18] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -42.340843-0.001415j
[2025-09-19 03:06:30] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -42.341467-0.002719j
[2025-09-19 03:06:43] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -42.344546-0.000104j
[2025-09-19 03:06:55] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -42.331884+0.000912j
[2025-09-19 03:07:07] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -42.341389-0.004859j
[2025-09-19 03:07:20] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -42.328162+0.003149j
[2025-09-19 03:07:32] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -42.339147-0.000823j
[2025-09-19 03:07:44] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -42.338912-0.002980j
[2025-09-19 03:07:57] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -42.333254-0.000692j
[2025-09-19 03:08:09] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -42.354188-0.000283j
[2025-09-19 03:08:21] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -42.332478+0.000786j
[2025-09-19 03:08:34] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -42.341301-0.002297j
[2025-09-19 03:08:46] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -42.341758-0.000287j
[2025-09-19 03:08:58] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -42.335512-0.000200j
[2025-09-19 03:09:11] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -42.348610-0.002088j
[2025-09-19 03:09:23] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -42.316294+0.002060j
[2025-09-19 03:09:35] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -42.343610+0.002278j
[2025-09-19 03:09:48] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -42.348468+0.000724j
[2025-09-19 03:10:00] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -42.334169-0.001960j
[2025-09-19 03:10:12] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -42.339620+0.000734j
[2025-09-19 03:10:25] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -42.337622+0.001801j
[2025-09-19 03:10:37] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -42.338280+0.001063j
[2025-09-19 03:10:49] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -42.348438-0.001284j
[2025-09-19 03:11:02] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -42.330226+0.001799j
[2025-09-19 03:11:14] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -42.342916-0.000115j
[2025-09-19 03:11:26] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -42.344119-0.001752j
[2025-09-19 03:11:39] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -42.328205-0.004496j
[2025-09-19 03:11:51] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -42.347164+0.004278j
[2025-09-19 03:12:04] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -42.318931-0.003782j
[2025-09-19 03:12:16] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -42.331366+0.002905j
[2025-09-19 03:12:28] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -42.354767+0.000882j
[2025-09-19 03:12:41] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -42.331659-0.002077j
[2025-09-19 03:12:53] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -42.338222+0.004450j
[2025-09-19 03:13:05] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -42.329362+0.003855j
[2025-09-19 03:13:18] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -42.339990-0.000648j
[2025-09-19 03:13:30] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -42.330303-0.001571j
[2025-09-19 03:13:42] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -42.332206+0.001516j
[2025-09-19 03:13:55] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -42.333378-0.000896j
[2025-09-19 03:14:07] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -42.340701-0.001872j
[2025-09-19 03:14:19] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -42.337900-0.001444j
[2025-09-19 03:14:32] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -42.342754-0.004035j
[2025-09-19 03:14:44] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -42.334137-0.001442j
[2025-09-19 03:14:56] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -42.351358+0.001409j
[2025-09-19 03:15:09] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -42.347189-0.002789j
[2025-09-19 03:15:21] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -42.342283+0.000021j
[2025-09-19 03:15:33] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -42.329242+0.000674j
[2025-09-19 03:15:46] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -42.326585+0.003072j
[2025-09-19 03:15:58] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -42.333199-0.002291j
[2025-09-19 03:16:11] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -42.351181-0.001683j
[2025-09-19 03:16:23] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -42.330945-0.004569j
[2025-09-19 03:16:35] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -42.329818-0.002574j
[2025-09-19 03:16:48] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -42.330434+0.000772j
[2025-09-19 03:17:00] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -42.335262-0.001658j
[2025-09-19 03:17:00] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-19 03:17:13] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -42.347694-0.000039j
[2025-09-19 03:17:25] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -42.332907-0.005291j
[2025-09-19 03:17:37] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -42.339944+0.001731j
[2025-09-19 03:17:50] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -42.319778+0.000770j
[2025-09-19 03:18:02] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -42.342600+0.002499j
[2025-09-19 03:18:14] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -42.341443-0.003378j
[2025-09-19 03:18:26] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -42.321043+0.002988j
[2025-09-19 03:18:39] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -42.341793+0.001299j
[2025-09-19 03:18:51] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -42.325660-0.000854j
[2025-09-19 03:19:03] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -42.347090-0.001622j
[2025-09-19 03:19:16] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -42.325056-0.003339j
[2025-09-19 03:19:28] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -42.338185-0.001136j
[2025-09-19 03:19:40] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -42.330045-0.002664j
[2025-09-19 03:19:53] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -42.331861+0.002338j
[2025-09-19 03:20:05] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -42.329718+0.000349j
[2025-09-19 03:20:17] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -42.331441+0.002423j
[2025-09-19 03:20:30] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -42.333909+0.000448j
[2025-09-19 03:20:42] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -42.340280-0.001248j
[2025-09-19 03:20:54] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -42.331173-0.000202j
[2025-09-19 03:21:07] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -42.333608+0.001852j
[2025-09-19 03:21:19] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -42.344337-0.002654j
[2025-09-19 03:21:31] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -42.337044-0.000911j
[2025-09-19 03:21:44] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -42.337889+0.001981j
[2025-09-19 03:21:56] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -42.333976-0.002870j
[2025-09-19 03:22:08] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -42.339915+0.003988j
[2025-09-19 03:22:21] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -42.343390+0.002948j
[2025-09-19 03:22:33] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -42.343640+0.000651j
[2025-09-19 03:22:45] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -42.335209+0.001777j
[2025-09-19 03:22:58] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -42.327720-0.001662j
[2025-09-19 03:23:10] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -42.323269+0.002116j
[2025-09-19 03:23:22] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -42.330392+0.003979j
[2025-09-19 03:23:34] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -42.333798+0.000880j
[2025-09-19 03:23:47] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -42.327321-0.001527j
[2025-09-19 03:23:59] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -42.338393+0.000106j
[2025-09-19 03:24:11] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -42.337994+0.000411j
[2025-09-19 03:24:24] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -42.349357+0.002297j
[2025-09-19 03:24:36] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -42.351288-0.000810j
[2025-09-19 03:24:48] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -42.331103-0.002247j
[2025-09-19 03:25:01] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -42.341046-0.001268j
[2025-09-19 03:25:13] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -42.332908-0.002540j
[2025-09-19 03:25:26] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -42.335292+0.001890j
[2025-09-19 03:25:38] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -42.334253+0.002026j
[2025-09-19 03:25:50] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -42.330581-0.002555j
[2025-09-19 03:26:03] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -42.328025-0.001457j
[2025-09-19 03:26:15] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -42.328954+0.000030j
[2025-09-19 03:26:28] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -42.353091+0.000775j
[2025-09-19 03:26:40] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -42.344793+0.003853j
[2025-09-19 03:26:52] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -42.345355-0.002359j
[2025-09-19 03:27:05] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -42.338629-0.001060j
[2025-09-19 03:27:17] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -42.341660+0.002056j
[2025-09-19 03:27:29] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -42.335181-0.000025j
[2025-09-19 03:27:42] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -42.346910+0.001915j
[2025-09-19 03:27:54] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -42.337304+0.002747j
[2025-09-19 03:28:06] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -42.323692-0.000137j
[2025-09-19 03:28:19] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -42.330807-0.000705j
[2025-09-19 03:28:31] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -42.342775+0.000976j
[2025-09-19 03:28:43] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -42.335901+0.002558j
[2025-09-19 03:28:56] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -42.335321+0.002244j
[2025-09-19 03:29:08] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -42.334906+0.001335j
[2025-09-19 03:29:20] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -42.354053-0.001135j
[2025-09-19 03:29:33] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -42.349649+0.003700j
[2025-09-19 03:29:45] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -42.322963-0.000644j
[2025-09-19 03:29:57] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -42.331617+0.000260j
[2025-09-19 03:30:10] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -42.336773-0.004345j
[2025-09-19 03:30:22] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -42.339099-0.000627j
[2025-09-19 03:30:34] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -42.337644+0.003210j
[2025-09-19 03:30:47] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -42.352497+0.001008j
[2025-09-19 03:30:59] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -42.339042-0.000033j
[2025-09-19 03:31:11] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -42.324595-0.001709j
[2025-09-19 03:31:24] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -42.328424-0.000030j
[2025-09-19 03:31:36] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -42.347464-0.000730j
[2025-09-19 03:31:49] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -42.343211+0.001242j
[2025-09-19 03:32:01] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -42.337122+0.001419j
[2025-09-19 03:32:13] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -42.345298+0.000163j
[2025-09-19 03:32:26] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -42.346921-0.000495j
[2025-09-19 03:32:38] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -42.332144+0.008278j
[2025-09-19 03:32:50] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -42.350338-0.001813j
[2025-09-19 03:33:03] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -42.346158-0.000286j
[2025-09-19 03:33:15] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -42.333744+0.000600j
[2025-09-19 03:33:27] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -42.336563+0.000779j
[2025-09-19 03:33:40] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -42.352548-0.002484j
[2025-09-19 03:33:52] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -42.334430-0.002561j
[2025-09-19 03:34:04] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -42.337976-0.001163j
[2025-09-19 03:34:16] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -42.342013-0.000375j
[2025-09-19 03:34:29] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -42.326548-0.001111j
[2025-09-19 03:34:41] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -42.336936+0.001859j
[2025-09-19 03:34:53] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -42.343303-0.001082j
[2025-09-19 03:35:06] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -42.331959+0.000021j
[2025-09-19 03:35:18] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -42.340226+0.002970j
[2025-09-19 03:35:30] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -42.330983+0.001588j
[2025-09-19 03:35:43] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -42.342716+0.003347j
[2025-09-19 03:35:55] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -42.344182+0.001241j
[2025-09-19 03:36:07] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -42.338028-0.003739j
[2025-09-19 03:36:20] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -42.327443-0.001850j
[2025-09-19 03:36:32] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -42.323693-0.000356j
[2025-09-19 03:36:44] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -42.331554+0.001681j
[2025-09-19 03:36:57] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -42.330751+0.006388j
[2025-09-19 03:37:09] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -42.354732-0.000610j
[2025-09-19 03:37:21] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -42.334256-0.001165j
[2025-09-19 03:37:34] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -42.340634+0.002143j
[2025-09-19 03:37:46] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -42.332667+0.000213j
[2025-09-19 03:37:58] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -42.338276-0.002152j
[2025-09-19 03:38:11] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -42.339452+0.002041j
[2025-09-19 03:38:23] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -42.337899-0.003487j
[2025-09-19 03:38:35] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -42.334220-0.000731j
[2025-09-19 03:38:36] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-19 03:38:48] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -42.325329-0.002839j
[2025-09-19 03:39:00] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -42.327035+0.003746j
[2025-09-19 03:39:12] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -42.349031+0.000826j
[2025-09-19 03:39:25] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -42.344894-0.000718j
[2025-09-19 03:39:37] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -42.320301+0.002626j
[2025-09-19 03:39:49] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -42.338547-0.004498j
[2025-09-19 03:40:02] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -42.340469-0.003011j
[2025-09-19 03:40:14] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -42.331106-0.002279j
[2025-09-19 03:40:26] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -42.339791-0.000252j
[2025-09-19 03:40:38] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -42.339747+0.000854j
[2025-09-19 03:40:51] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -42.342451-0.002518j
[2025-09-19 03:41:03] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -42.322239+0.001601j
[2025-09-19 03:41:15] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -42.335618+0.000567j
[2025-09-19 03:41:28] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -42.344893-0.001587j
[2025-09-19 03:41:40] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -42.351691+0.000933j
[2025-09-19 03:41:52] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -42.339346+0.002619j
[2025-09-19 03:42:05] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -42.331634-0.001145j
[2025-09-19 03:42:17] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -42.342694-0.003488j
[2025-09-19 03:42:29] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -42.346078-0.002552j
[2025-09-19 03:42:42] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -42.335524+0.001291j
[2025-09-19 03:42:54] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -42.324534+0.003401j
[2025-09-19 03:43:06] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -42.338170-0.000663j
[2025-09-19 03:43:19] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -42.337695-0.001268j
[2025-09-19 03:43:31] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -42.344304+0.005313j
[2025-09-19 03:43:43] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -42.333608-0.006869j
[2025-09-19 03:43:56] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -42.334181+0.000994j
[2025-09-19 03:44:08] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -42.328038-0.002758j
[2025-09-19 03:44:20] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -42.341937-0.001861j
[2025-09-19 03:44:33] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -42.323326+0.001636j
[2025-09-19 03:44:45] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -42.329076+0.001461j
[2025-09-19 03:44:58] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -42.339460+0.000373j
[2025-09-19 03:45:10] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -42.349417-0.004476j
[2025-09-19 03:45:22] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -42.334471-0.001320j
[2025-09-19 03:45:35] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -42.334151-0.002444j
[2025-09-19 03:45:47] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -42.349553+0.000261j
[2025-09-19 03:45:59] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -42.330148+0.001650j
[2025-09-19 03:46:12] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -42.333894+0.001814j
[2025-09-19 03:46:24] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -42.326109-0.001728j
[2025-09-19 03:46:36] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -42.349654-0.000802j
[2025-09-19 03:46:49] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -42.345900+0.005385j
[2025-09-19 03:47:01] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -42.339355-0.003885j
[2025-09-19 03:47:13] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -42.339974+0.003205j
[2025-09-19 03:47:26] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -42.330715-0.003385j
[2025-09-19 03:47:38] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -42.327972-0.001055j
[2025-09-19 03:47:50] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -42.346246-0.001204j
[2025-09-19 03:48:03] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -42.349664+0.000539j
[2025-09-19 03:48:15] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -42.322331-0.003183j
[2025-09-19 03:48:27] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -42.336775-0.000037j
[2025-09-19 03:48:40] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -42.343346-0.001780j
[2025-09-19 03:48:52] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -42.348502-0.000358j
[2025-09-19 03:49:04] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -42.330104+0.003132j
[2025-09-19 03:49:17] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -42.323822-0.000050j
[2025-09-19 03:49:29] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -42.346521+0.000371j
[2025-09-19 03:49:41] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -42.323329+0.001353j
[2025-09-19 03:49:54] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -42.335352-0.003039j
[2025-09-19 03:50:06] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -42.349454-0.004164j
[2025-09-19 03:50:18] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -42.347604-0.001633j
[2025-09-19 03:50:30] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -42.342997-0.000698j
[2025-09-19 03:50:43] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -42.338955-0.003560j
[2025-09-19 03:50:55] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -42.340944-0.000725j
[2025-09-19 03:51:07] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -42.351444-0.000971j
[2025-09-19 03:51:20] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -42.329824-0.002872j
[2025-09-19 03:51:32] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -42.332703-0.002051j
[2025-09-19 03:51:44] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -42.330749+0.000207j
[2025-09-19 03:51:57] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -42.336598-0.000479j
[2025-09-19 03:52:09] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -42.321352-0.004181j
[2025-09-19 03:52:21] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -42.349317-0.003668j
[2025-09-19 03:52:34] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -42.350903-0.000395j
[2025-09-19 03:52:46] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -42.341829-0.002103j
[2025-09-19 03:52:58] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -42.343784-0.000370j
[2025-09-19 03:53:11] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -42.354799-0.000192j
[2025-09-19 03:53:23] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -42.339393+0.003851j
[2025-09-19 03:53:35] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -42.325096-0.002224j
[2025-09-19 03:53:48] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -42.330570-0.002173j
[2025-09-19 03:54:00] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -42.336458-0.000007j
[2025-09-19 03:54:13] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -42.327106+0.002029j
[2025-09-19 03:54:25] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -42.338760+0.000754j
[2025-09-19 03:54:37] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -42.333919-0.000252j
[2025-09-19 03:54:50] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -42.336847+0.000233j
[2025-09-19 03:55:02] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -42.351967+0.002758j
[2025-09-19 03:55:14] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -42.338405-0.000720j
[2025-09-19 03:55:27] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -42.345644-0.002362j
[2025-09-19 03:55:39] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -42.335000-0.001776j
[2025-09-19 03:55:52] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -42.346503-0.001989j
[2025-09-19 03:56:04] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -42.325032+0.000304j
[2025-09-19 03:56:16] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -42.324245+0.002267j
[2025-09-19 03:56:28] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -42.346292-0.000222j
[2025-09-19 03:56:41] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -42.341472-0.003667j
[2025-09-19 03:56:53] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -42.328177-0.001466j
[2025-09-19 03:57:05] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -42.336108-0.001118j
[2025-09-19 03:57:18] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -42.331125-0.001565j
[2025-09-19 03:57:30] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -42.340606+0.001046j
[2025-09-19 03:57:43] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -42.336245-0.003594j
[2025-09-19 03:57:55] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -42.334579-0.001588j
[2025-09-19 03:58:07] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -42.339936-0.000363j
[2025-09-19 03:58:19] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -42.330243+0.001178j
[2025-09-19 03:58:32] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -42.332119-0.001414j
[2025-09-19 03:58:44] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -42.336056+0.002775j
[2025-09-19 03:58:57] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -42.332459-0.001370j
[2025-09-19 03:59:09] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -42.339701-0.002105j
[2025-09-19 03:59:21] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -42.326497+0.004191j
[2025-09-19 03:59:34] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -42.343362-0.004773j
[2025-09-19 03:59:46] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -42.345323+0.000378j
[2025-09-19 03:59:58] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -42.348149-0.000120j
[2025-09-19 04:00:10] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -42.334104+0.004524j
[2025-09-19 04:00:11] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-19 04:00:23] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -42.339593-0.000658j
[2025-09-19 04:00:35] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -42.344436-0.000810j
[2025-09-19 04:00:48] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -42.346791-0.000064j
[2025-09-19 04:01:00] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -42.326255+0.002845j
[2025-09-19 04:01:12] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -42.349057+0.002068j
[2025-09-19 04:01:25] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -42.342712+0.002590j
[2025-09-19 04:01:37] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -42.367878-0.001405j
[2025-09-19 04:01:49] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -42.336887-0.004251j
[2025-09-19 04:02:01] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -42.333274-0.005025j
[2025-09-19 04:02:14] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -42.338479+0.001674j
[2025-09-19 04:02:26] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -42.328096+0.003910j
[2025-09-19 04:02:38] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -42.332922-0.003003j
[2025-09-19 04:02:51] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -42.319646+0.001371j
[2025-09-19 04:03:03] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -42.334170-0.000675j
[2025-09-19 04:03:15] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -42.338624-0.001281j
[2025-09-19 04:03:28] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -42.340945+0.002281j
[2025-09-19 04:03:40] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -42.327954-0.000212j
[2025-09-19 04:03:52] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -42.341237-0.001725j
[2025-09-19 04:04:05] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -42.338086+0.000906j
[2025-09-19 04:04:17] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -42.349427+0.002939j
[2025-09-19 04:04:29] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -42.346024-0.003192j
[2025-09-19 04:04:42] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -42.340624+0.006122j
[2025-09-19 04:04:54] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -42.329884-0.000729j
[2025-09-19 04:05:06] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -42.340317-0.000305j
[2025-09-19 04:05:19] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -42.340196+0.001146j
[2025-09-19 04:05:31] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -42.338337+0.000217j
[2025-09-19 04:05:43] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -42.332690+0.004848j
[2025-09-19 04:05:56] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -42.342590+0.004571j
[2025-09-19 04:06:08] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -42.344559-0.007209j
[2025-09-19 04:06:20] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -42.336857+0.002186j
[2025-09-19 04:06:33] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -42.342168-0.003475j
[2025-09-19 04:06:45] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -42.338863-0.001491j
[2025-09-19 04:06:57] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -42.344964-0.002000j
[2025-09-19 04:07:10] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -42.344189-0.000394j
[2025-09-19 04:07:22] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -42.339480-0.001732j
[2025-09-19 04:07:34] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -42.338627-0.001131j
[2025-09-19 04:07:47] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -42.331631-0.001172j
[2025-09-19 04:07:59] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -42.342450-0.002215j
[2025-09-19 04:08:11] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -42.330456+0.001063j
[2025-09-19 04:08:24] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -42.327566-0.000496j
[2025-09-19 04:08:36] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -42.346369-0.002323j
[2025-09-19 04:08:48] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -42.350511+0.000608j
[2025-09-19 04:09:01] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -42.336131+0.002471j
[2025-09-19 04:09:13] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -42.343683+0.003608j
[2025-09-19 04:09:25] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -42.347621+0.003232j
[2025-09-19 04:09:38] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -42.317495-0.003282j
[2025-09-19 04:09:50] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -42.349955-0.000392j
[2025-09-19 04:10:02] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -42.338379-0.001159j
[2025-09-19 04:10:15] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -42.337840-0.000699j
[2025-09-19 04:10:27] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -42.335879-0.004201j
[2025-09-19 04:10:39] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -42.334305-0.000566j
[2025-09-19 04:10:52] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -42.336597+0.000914j
[2025-09-19 04:11:04] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -42.335930-0.000414j
[2025-09-19 04:11:16] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -42.336465+0.001450j
[2025-09-19 04:11:29] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -42.330646-0.000774j
[2025-09-19 04:11:41] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -42.352027+0.000081j
[2025-09-19 04:11:53] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -42.337080-0.001820j
[2025-09-19 04:12:06] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -42.341762+0.001324j
[2025-09-19 04:12:18] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -42.347983+0.000604j
[2025-09-19 04:12:30] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -42.343505-0.000229j
[2025-09-19 04:12:43] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -42.340119-0.004417j
[2025-09-19 04:12:55] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -42.336110-0.002253j
[2025-09-19 04:13:08] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -42.351855-0.001112j
[2025-09-19 04:13:20] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -42.341600+0.001036j
[2025-09-19 04:13:32] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -42.336418+0.002761j
[2025-09-19 04:13:45] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -42.323402-0.001645j
[2025-09-19 04:13:57] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -42.343197+0.000947j
[2025-09-19 04:14:09] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -42.332975+0.000255j
[2025-09-19 04:14:22] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -42.337177-0.001458j
[2025-09-19 04:14:34] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -42.343153+0.002314j
[2025-09-19 04:14:47] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -42.333891-0.001096j
[2025-09-19 04:14:59] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -42.339604+0.001064j
[2025-09-19 04:15:11] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -42.333452-0.000662j
[2025-09-19 04:15:24] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -42.343480-0.002021j
[2025-09-19 04:15:36] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -42.337388-0.000435j
[2025-09-19 04:15:48] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -42.339764-0.002242j
[2025-09-19 04:16:01] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -42.335818-0.001465j
[2025-09-19 04:16:13] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -42.350069-0.001673j
[2025-09-19 04:16:25] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -42.348163-0.003453j
[2025-09-19 04:16:37] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -42.346882-0.000538j
[2025-09-19 04:16:50] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -42.342626+0.000394j
[2025-09-19 04:17:02] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -42.355067+0.003550j
[2025-09-19 04:17:14] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -42.341694+0.001213j
[2025-09-19 04:17:27] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -42.351452+0.000629j
[2025-09-19 04:17:39] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -42.331028-0.002542j
[2025-09-19 04:17:52] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -42.338280+0.000338j
[2025-09-19 04:18:04] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -42.328784+0.001327j
[2025-09-19 04:18:16] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -42.319704+0.001983j
[2025-09-19 04:18:29] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -42.343516-0.000680j
[2025-09-19 04:18:41] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -42.324017-0.000560j
[2025-09-19 04:18:53] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -42.337608+0.000586j
[2025-09-19 04:19:06] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -42.354869-0.000771j
[2025-09-19 04:19:18] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -42.332511+0.000076j
[2025-09-19 04:19:30] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -42.337285+0.003061j
[2025-09-19 04:19:43] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -42.338864+0.003921j
[2025-09-19 04:19:55] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -42.343805+0.001432j
[2025-09-19 04:20:07] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -42.328817-0.000852j
[2025-09-19 04:20:19] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -42.348172+0.002808j
[2025-09-19 04:20:32] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -42.324907-0.000545j
[2025-09-19 04:20:44] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -42.339136-0.009584j
[2025-09-19 04:20:56] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -42.332675-0.000295j
[2025-09-19 04:21:09] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -42.337307-0.000460j
[2025-09-19 04:21:21] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -42.347164+0.000323j
[2025-09-19 04:21:33] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -42.338113+0.001764j
[2025-09-19 04:21:46] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -42.336780-0.001818j
[2025-09-19 04:21:46] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-19 04:21:58] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -42.327091-0.001347j
[2025-09-19 04:22:10] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -42.361365-0.003774j
[2025-09-19 04:22:23] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -42.336872-0.001544j
[2025-09-19 04:22:35] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -42.335181+0.003934j
[2025-09-19 04:22:47] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -42.336145+0.000916j
[2025-09-19 04:23:00] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -42.337965+0.000351j
[2025-09-19 04:23:12] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -42.331943-0.000237j
[2025-09-19 04:23:24] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -42.341607-0.000739j
[2025-09-19 04:23:37] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -42.333430+0.001666j
[2025-09-19 04:23:49] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -42.341607+0.001385j
[2025-09-19 04:24:01] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -42.318570+0.002540j
[2025-09-19 04:24:14] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -42.331509+0.002767j
[2025-09-19 04:24:26] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -42.340594+0.000693j
[2025-09-19 04:24:38] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -42.333139+0.000817j
[2025-09-19 04:24:51] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -42.335023+0.001960j
[2025-09-19 04:25:03] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -42.337936-0.002140j
[2025-09-19 04:25:15] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -42.338366-0.000311j
[2025-09-19 04:25:28] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -42.342439-0.003348j
[2025-09-19 04:25:40] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -42.342222-0.001636j
[2025-09-19 04:25:52] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -42.327452+0.000808j
[2025-09-19 04:26:05] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -42.343410+0.001966j
[2025-09-19 04:26:17] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -42.328161+0.000455j
[2025-09-19 04:26:29] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -42.350463-0.001004j
[2025-09-19 04:26:42] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -42.341383-0.003226j
[2025-09-19 04:26:54] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -42.337212+0.001821j
[2025-09-19 04:27:06] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -42.327300+0.004062j
[2025-09-19 04:27:19] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -42.338800+0.002624j
[2025-09-19 04:27:31] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -42.340126+0.000894j
[2025-09-19 04:27:43] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -42.338427-0.002623j
[2025-09-19 04:27:56] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -42.350955-0.000273j
[2025-09-19 04:28:08] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -42.335105-0.004103j
[2025-09-19 04:28:21] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -42.337919-0.000205j
[2025-09-19 04:28:33] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -42.326102-0.002933j
[2025-09-19 04:28:45] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -42.357219-0.000093j
[2025-09-19 04:28:58] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -42.326296+0.000811j
[2025-09-19 04:29:10] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -42.335749+0.000755j
[2025-09-19 04:29:22] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -42.341053+0.002857j
[2025-09-19 04:29:34] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -42.354884+0.000076j
[2025-09-19 04:29:47] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -42.338468+0.002934j
[2025-09-19 04:29:59] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -42.338035-0.000254j
[2025-09-19 04:30:11] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -42.335318+0.000671j
[2025-09-19 04:30:24] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -42.342524+0.002240j
[2025-09-19 04:30:36] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -42.339387+0.001977j
[2025-09-19 04:30:48] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -42.349370-0.002181j
[2025-09-19 04:31:01] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -42.338394+0.001128j
[2025-09-19 04:31:13] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -42.344071+0.002670j
[2025-09-19 04:31:25] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -42.330582-0.000982j
[2025-09-19 04:31:38] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -42.328619-0.000303j
[2025-09-19 04:31:50] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -42.347386+0.000756j
[2025-09-19 04:32:02] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -42.347692-0.002034j
[2025-09-19 04:32:15] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -42.354827+0.001850j
[2025-09-19 04:32:27] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -42.340654-0.001418j
[2025-09-19 04:32:39] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -42.345455-0.000297j
[2025-09-19 04:32:52] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -42.344820-0.000070j
[2025-09-19 04:33:04] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -42.330431-0.000705j
[2025-09-19 04:33:16] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -42.321567+0.002800j
[2025-09-19 04:33:29] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -42.343802+0.001299j
[2025-09-19 04:33:41] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -42.351354+0.004248j
[2025-09-19 04:33:53] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -42.345976-0.001487j
[2025-09-19 04:34:06] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -42.355621-0.001592j
[2025-09-19 04:34:18] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -42.344420-0.000670j
[2025-09-19 04:34:30] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -42.332728+0.004067j
[2025-09-19 04:34:43] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -42.341979-0.000104j
[2025-09-19 04:34:55] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -42.347262+0.001838j
[2025-09-19 04:35:07] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -42.332807+0.000831j
[2025-09-19 04:35:19] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -42.330271-0.002889j
[2025-09-19 04:35:32] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -42.342753+0.000627j
[2025-09-19 04:35:44] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -42.344874-0.001360j
[2025-09-19 04:35:57] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -42.330904-0.001766j
[2025-09-19 04:36:09] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -42.352958-0.001254j
[2025-09-19 04:36:21] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -42.337905-0.003048j
[2025-09-19 04:36:33] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -42.354610-0.002549j
[2025-09-19 04:36:46] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -42.329738-0.001794j
[2025-09-19 04:36:58] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -42.336262+0.000238j
[2025-09-19 04:37:11] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -42.322270-0.001947j
[2025-09-19 04:37:23] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -42.343507-0.000905j
[2025-09-19 04:37:35] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -42.332746+0.000323j
[2025-09-19 04:37:48] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -42.327723+0.001612j
[2025-09-19 04:38:00] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -42.329886+0.005789j
[2025-09-19 04:38:12] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -42.335385-0.000643j
[2025-09-19 04:38:24] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -42.334766+0.000390j
[2025-09-19 04:38:36] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -42.343041-0.001882j
[2025-09-19 04:38:49] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -42.364329+0.001423j
[2025-09-19 04:39:01] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -42.342846+0.001656j
[2025-09-19 04:39:14] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -42.346213-0.000035j
[2025-09-19 04:39:26] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -42.342311-0.000131j
[2025-09-19 04:39:38] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -42.337277+0.000030j
[2025-09-19 04:39:50] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -42.340244+0.007015j
[2025-09-19 04:40:03] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -42.331118+0.002694j
[2025-09-19 04:40:15] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -42.339571+0.001105j
[2025-09-19 04:40:28] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -42.339321-0.001042j
[2025-09-19 04:40:40] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -42.341605-0.002399j
[2025-09-19 04:40:52] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -42.321913+0.000286j
[2025-09-19 04:41:05] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -42.336284-0.000631j
[2025-09-19 04:41:17] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -42.341915-0.000182j
[2025-09-19 04:41:29] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -42.327910-0.001983j
[2025-09-19 04:41:41] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -42.335942+0.002661j
[2025-09-19 04:41:54] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -42.339722-0.000387j
[2025-09-19 04:42:06] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -42.327194+0.001108j
[2025-09-19 04:42:18] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -42.345956-0.002001j
[2025-09-19 04:42:31] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -42.339178+0.001856j
[2025-09-19 04:42:43] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -42.333515-0.001684j
[2025-09-19 04:42:55] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -42.345750-0.000593j
[2025-09-19 04:43:08] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -42.334422+0.001410j
[2025-09-19 04:43:20] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -42.344221-0.000966j
[2025-09-19 04:43:20] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-19 04:43:20] ✅ Training completed | Restarts: 2
[2025-09-19 04:43:20] ============================================================
[2025-09-19 04:43:20] Training completed | Runtime: 13018.6s
[2025-09-19 04:43:25] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-19 04:43:25] ============================================================
