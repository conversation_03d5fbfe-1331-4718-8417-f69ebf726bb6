[2025-09-18 14:06:01] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.80/training/checkpoints/final_GCNN.pkl
[2025-09-18 14:06:01]   - 迭代次数: final
[2025-09-18 14:06:01]   - 能量: -44.855045+0.001618j ± 0.006447
[2025-09-18 14:06:01]   - 时间戳: 2025-09-17T23:35:56.985143+08:00
[2025-09-18 14:06:24] ✓ 变分状态参数已从checkpoint恢复
[2025-09-18 14:06:24] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-18 14:06:24] ==================================================
[2025-09-18 14:06:24] GCNN for Shastry-Sutherland Model
[2025-09-18 14:06:24] ==================================================
[2025-09-18 14:06:24] System parameters:
[2025-09-18 14:06:24]   - System size: L=5, N=100
[2025-09-18 14:06:24]   - System parameters: J1=0.81, J2=1.0, Q=0.0
[2025-09-18 14:06:24] --------------------------------------------------
[2025-09-18 14:06:24] Model parameters:
[2025-09-18 14:06:24]   - Number of layers = 4
[2025-09-18 14:06:24]   - Number of features = 4
[2025-09-18 14:06:24]   - Total parameters = 19628
[2025-09-18 14:06:24] --------------------------------------------------
[2025-09-18 14:06:24] Training parameters:
[2025-09-18 14:06:24]   - Learning rate: 0.015
[2025-09-18 14:06:24]   - Total iterations: 1050
[2025-09-18 14:06:24]   - Annealing cycles: 3
[2025-09-18 14:06:24]   - Initial period: 150
[2025-09-18 14:06:24]   - Period multiplier: 2.0
[2025-09-18 14:06:24]   - Temperature range: 0.0-1.0
[2025-09-18 14:06:24]   - Samples: 4096
[2025-09-18 14:06:24]   - Discarded samples: 0
[2025-09-18 14:06:24]   - Chunk size: 2048
[2025-09-18 14:06:24]   - Diagonal shift: 0.2
[2025-09-18 14:06:24]   - Gradient clipping: 1.0
[2025-09-18 14:06:24]   - Checkpoint enabled: interval=105
[2025-09-18 14:06:24]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.81/training/checkpoints
[2025-09-18 14:06:24] --------------------------------------------------
[2025-09-18 14:06:24] Device status:
[2025-09-18 14:06:24]   - Devices model: NVIDIA H200 NVL
[2025-09-18 14:06:24]   - Number of devices: 1
[2025-09-18 14:06:24]   - Sharding: True
[2025-09-18 14:06:24] ============================================================
[2025-09-18 14:07:31] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -45.470542+0.001526j
[2025-09-18 14:08:14] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -45.478331+0.002465j
[2025-09-18 14:08:26] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -45.470560+0.002038j
[2025-09-18 14:08:39] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -45.506658-0.002772j
[2025-09-18 14:08:51] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -45.488595-0.003798j
[2025-09-18 14:09:04] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -45.484824-0.000175j
[2025-09-18 14:09:16] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -45.496334+0.000563j
[2025-09-18 14:09:29] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -45.500742-0.000506j
[2025-09-18 14:09:41] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -45.501334-0.004711j
[2025-09-18 14:09:54] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -45.484450+0.000791j
[2025-09-18 14:10:06] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -45.503491-0.002919j
[2025-09-18 14:10:19] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -45.492722+0.000105j
[2025-09-18 14:10:31] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -45.494794-0.003031j
[2025-09-18 14:10:44] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -45.493056+0.001691j
[2025-09-18 14:10:56] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -45.488134+0.000017j
[2025-09-18 14:11:09] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -45.491994-0.000766j
[2025-09-18 14:11:21] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -45.478109-0.000718j
[2025-09-18 14:11:34] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -45.477460-0.000659j
[2025-09-18 14:11:46] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -45.507173+0.004910j
[2025-09-18 14:11:59] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -45.485462+0.002201j
[2025-09-18 14:12:11] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -45.486177+0.000163j
[2025-09-18 14:12:24] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -45.479060-0.001965j
[2025-09-18 14:12:36] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -45.492444-0.001576j
[2025-09-18 14:12:49] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -45.490002+0.000969j
[2025-09-18 14:13:01] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -45.497533-0.000314j
[2025-09-18 14:13:14] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -45.499395-0.000142j
[2025-09-18 14:13:26] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -45.501726+0.001998j
[2025-09-18 14:13:39] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -45.504944-0.000992j
[2025-09-18 14:13:51] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -45.490670-0.000883j
[2025-09-18 14:14:04] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -45.499522-0.000385j
[2025-09-18 14:14:16] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -45.484888+0.001458j
[2025-09-18 14:14:29] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -45.495197+0.000092j
[2025-09-18 14:14:41] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -45.494918+0.001059j
[2025-09-18 14:14:54] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -45.493848-0.001327j
[2025-09-18 14:15:06] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -45.477498+0.004948j
[2025-09-18 14:15:19] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -45.487260+0.004724j
[2025-09-18 14:15:31] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -45.484719-0.001347j
[2025-09-18 14:15:44] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -45.502273-0.008344j
[2025-09-18 14:15:56] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -45.485307+0.002324j
[2025-09-18 14:16:08] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -45.496687-0.003376j
[2025-09-18 14:16:21] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -45.501064+0.001868j
[2025-09-18 14:16:33] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -45.478728+0.002854j
[2025-09-18 14:16:46] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -45.495260+0.004195j
[2025-09-18 14:16:58] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -45.511957-0.004619j
[2025-09-18 14:17:11] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -45.483698+0.000813j
[2025-09-18 14:17:23] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -45.491233-0.003067j
[2025-09-18 14:17:36] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -45.484506+0.000785j
[2025-09-18 14:17:48] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -45.501795-0.001086j
[2025-09-18 14:18:01] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -45.491569+0.001243j
[2025-09-18 14:18:13] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -45.496546+0.001153j
[2025-09-18 14:18:25] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -45.491391+0.000448j
[2025-09-18 14:18:38] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -45.483300-0.002281j
[2025-09-18 14:18:50] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -45.488602+0.001025j
[2025-09-18 14:19:03] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -45.495919+0.002430j
[2025-09-18 14:19:15] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -45.491847+0.000338j
[2025-09-18 14:19:28] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -45.495572+0.001461j
[2025-09-18 14:19:40] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -45.491039+0.001722j
[2025-09-18 14:19:52] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -45.489971+0.001000j
[2025-09-18 14:20:05] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -45.492046-0.001294j
[2025-09-18 14:20:17] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -45.482248-0.003237j
[2025-09-18 14:20:30] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -45.492313-0.000529j
[2025-09-18 14:20:42] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -45.498008+0.000048j
[2025-09-18 14:20:55] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -45.486570+0.002255j
[2025-09-18 14:21:07] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -45.492548+0.004038j
[2025-09-18 14:21:20] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -45.500075-0.001130j
[2025-09-18 14:21:32] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -45.497109-0.000178j
[2025-09-18 14:21:44] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -45.490994+0.002675j
[2025-09-18 14:21:57] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -45.488907-0.000236j
[2025-09-18 14:22:09] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -45.498825-0.001330j
[2025-09-18 14:22:22] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -45.505202-0.001577j
[2025-09-18 14:22:34] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -45.486931+0.000341j
[2025-09-18 14:22:47] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -45.502557+0.004400j
[2025-09-18 14:22:59] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -45.477352+0.003403j
[2025-09-18 14:23:11] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -45.477700+0.000506j
[2025-09-18 14:23:24] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -45.496011+0.001534j
[2025-09-18 14:23:36] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -45.489533-0.003743j
[2025-09-18 14:23:49] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -45.481767-0.005256j
[2025-09-18 14:24:01] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -45.502815+0.001402j
[2025-09-18 14:24:14] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -45.488170-0.002446j
[2025-09-18 14:24:26] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -45.494387+0.002956j
[2025-09-18 14:24:39] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -45.504929-0.002828j
[2025-09-18 14:24:51] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -45.480490+0.000740j
[2025-09-18 14:25:03] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -45.491784+0.000968j
[2025-09-18 14:25:16] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -45.508083-0.000090j
[2025-09-18 14:25:28] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -45.495003+0.002438j
[2025-09-18 14:25:41] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -45.494276-0.002344j
[2025-09-18 14:25:53] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -45.488126-0.001409j
[2025-09-18 14:26:06] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -45.496237+0.002256j
[2025-09-18 14:26:18] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -45.486731+0.001806j
[2025-09-18 14:26:30] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -45.506262+0.004227j
[2025-09-18 14:26:43] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -45.491790+0.000655j
[2025-09-18 14:26:55] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -45.502203+0.000117j
[2025-09-18 14:27:08] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -45.494490-0.001699j
[2025-09-18 14:27:20] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -45.494706+0.002474j
[2025-09-18 14:27:33] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -45.499138-0.002593j
[2025-09-18 14:27:45] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -45.480811-0.005080j
[2025-09-18 14:27:57] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -45.491298+0.001639j
[2025-09-18 14:28:10] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -45.484945-0.004288j
[2025-09-18 14:28:22] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -45.488037-0.001934j
[2025-09-18 14:28:35] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -45.508714-0.003648j
[2025-09-18 14:28:47] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -45.492206+0.004003j
[2025-09-18 14:29:00] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -45.496501-0.000771j
[2025-09-18 14:29:12] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -45.493986-0.003556j
[2025-09-18 14:29:24] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -45.485405+0.002913j
[2025-09-18 14:29:37] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -45.495513-0.001364j
[2025-09-18 14:29:37] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-18 14:29:49] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -45.490824-0.001647j
[2025-09-18 14:30:02] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -45.491378+0.001704j
[2025-09-18 14:30:14] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -45.487098+0.000716j
[2025-09-18 14:30:27] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -45.491200-0.002680j
[2025-09-18 14:30:39] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -45.499429-0.000806j
[2025-09-18 14:30:51] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -45.495052-0.000725j
[2025-09-18 14:31:04] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -45.493779+0.002167j
[2025-09-18 14:31:16] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -45.486353-0.001506j
[2025-09-18 14:31:29] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -45.499183+0.001413j
[2025-09-18 14:31:41] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -45.502871+0.000739j
[2025-09-18 14:31:54] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -45.505444-0.003166j
[2025-09-18 14:32:06] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -45.492893-0.001147j
[2025-09-18 14:32:18] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -45.491727-0.004040j
[2025-09-18 14:32:31] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -45.484473-0.004125j
[2025-09-18 14:32:43] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -45.494478-0.000972j
[2025-09-18 14:32:56] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -45.506654+0.003944j
[2025-09-18 14:33:08] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -45.497810+0.002009j
[2025-09-18 14:33:21] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -45.517910-0.000986j
[2025-09-18 14:33:33] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -45.501986-0.002420j
[2025-09-18 14:33:46] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -45.496354-0.001281j
[2025-09-18 14:33:58] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -45.494474-0.000600j
[2025-09-18 14:34:11] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -45.493915-0.002206j
[2025-09-18 14:34:23] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -45.484235-0.002504j
[2025-09-18 14:34:36] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -45.484953-0.001507j
[2025-09-18 14:34:48] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -45.514289-0.004020j
[2025-09-18 14:35:01] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -45.487639+0.001296j
[2025-09-18 14:35:13] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -45.499412-0.003299j
[2025-09-18 14:35:26] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -45.500072+0.002760j
[2025-09-18 14:35:38] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -45.492571+0.001163j
[2025-09-18 14:35:51] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -45.493440+0.001630j
[2025-09-18 14:36:03] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -45.503227+0.002993j
[2025-09-18 14:36:16] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -45.502412+0.005409j
[2025-09-18 14:36:28] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -45.490121+0.000509j
[2025-09-18 14:36:41] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -45.475960+0.000295j
[2025-09-18 14:36:53] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -45.495194-0.000053j
[2025-09-18 14:37:06] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -45.497948-0.001370j
[2025-09-18 14:37:18] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -45.501644+0.001877j
[2025-09-18 14:37:31] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -45.504228-0.000415j
[2025-09-18 14:37:43] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -45.493995-0.002901j
[2025-09-18 14:37:56] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -45.492607+0.001227j
[2025-09-18 14:38:08] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -45.481015+0.000970j
[2025-09-18 14:38:21] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -45.483602-0.003703j
[2025-09-18 14:38:33] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -45.494830-0.003983j
[2025-09-18 14:38:46] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -45.498556+0.001234j
[2025-09-18 14:38:58] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -45.500984-0.000243j
[2025-09-18 14:38:58] RESTART #1 | Period: 300
[2025-09-18 14:39:11] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -45.489437-0.001290j
[2025-09-18 14:39:23] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -45.506420-0.000013j
[2025-09-18 14:39:36] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -45.489946-0.001380j
[2025-09-18 14:39:48] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -45.486196+0.000471j
[2025-09-18 14:40:01] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -45.493692-0.000501j
[2025-09-18 14:40:13] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -45.504603-0.004149j
[2025-09-18 14:40:26] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -45.504581+0.001693j
[2025-09-18 14:40:38] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -45.497006-0.000051j
[2025-09-18 14:40:51] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -45.501321-0.001659j
[2025-09-18 14:41:03] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -45.499078+0.000591j
[2025-09-18 14:41:16] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -45.493886-0.005554j
[2025-09-18 14:41:28] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -45.500570-0.004982j
[2025-09-18 14:41:41] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -45.496464+0.004672j
[2025-09-18 14:41:53] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -45.508177-0.000866j
[2025-09-18 14:42:06] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -45.478461-0.001131j
[2025-09-18 14:42:18] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -45.502276-0.001745j
[2025-09-18 14:42:30] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -45.492415-0.002456j
[2025-09-18 14:42:43] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -45.494918-0.002351j
[2025-09-18 14:42:55] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -45.495857+0.001458j
[2025-09-18 14:43:08] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -45.500900+0.002984j
[2025-09-18 14:43:20] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -45.506075+0.003045j
[2025-09-18 14:43:33] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -45.488144-0.002904j
[2025-09-18 14:43:45] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -45.494914+0.002057j
[2025-09-18 14:43:58] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -45.486472+0.000220j
[2025-09-18 14:44:10] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -45.504788-0.001216j
[2025-09-18 14:44:23] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -45.493128+0.000128j
[2025-09-18 14:44:35] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -45.493891-0.001222j
[2025-09-18 14:44:48] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -45.498541+0.000357j
[2025-09-18 14:45:00] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -45.508061+0.003348j
[2025-09-18 14:45:13] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -45.498821-0.001470j
[2025-09-18 14:45:25] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -45.495492-0.003450j
[2025-09-18 14:45:38] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -45.500903-0.000874j
[2025-09-18 14:45:50] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -45.501116-0.002763j
[2025-09-18 14:46:03] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -45.496358-0.000499j
[2025-09-18 14:46:15] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -45.498958+0.004690j
[2025-09-18 14:46:28] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -45.488699+0.000436j
[2025-09-18 14:46:40] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -45.490605+0.002256j
[2025-09-18 14:46:53] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -45.487300-0.008540j
[2025-09-18 14:47:05] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -45.504875-0.002513j
[2025-09-18 14:47:18] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -45.490143+0.000378j
[2025-09-18 14:47:30] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -45.491183-0.000422j
[2025-09-18 14:47:42] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -45.505422+0.006856j
[2025-09-18 14:47:55] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -45.511774+0.004393j
[2025-09-18 14:48:07] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -45.509780-0.001769j
[2025-09-18 14:48:20] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -45.498558+0.000270j
[2025-09-18 14:48:32] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -45.495662+0.000272j
[2025-09-18 14:48:45] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -45.482426-0.000927j
[2025-09-18 14:48:57] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -45.496797-0.002048j
[2025-09-18 14:49:09] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -45.492400-0.000799j
[2025-09-18 14:49:22] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -45.495404+0.000501j
[2025-09-18 14:49:34] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -45.481401+0.001134j
[2025-09-18 14:49:47] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -45.496816+0.002593j
[2025-09-18 14:49:59] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -45.507667+0.001601j
[2025-09-18 14:50:12] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -45.484626+0.001586j
[2025-09-18 14:50:24] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -45.498982-0.002178j
[2025-09-18 14:50:36] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -45.499288+0.001458j
[2025-09-18 14:50:49] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -45.498035+0.001194j
[2025-09-18 14:51:01] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -45.483603-0.000420j
[2025-09-18 14:51:14] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -45.490174+0.002358j
[2025-09-18 14:51:26] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -45.481357+0.001651j
[2025-09-18 14:51:26] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-18 14:51:39] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -45.502152+0.000300j
[2025-09-18 14:51:51] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -45.501727-0.003869j
[2025-09-18 14:52:04] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -45.484304-0.001437j
[2025-09-18 14:52:16] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -45.492124+0.000514j
[2025-09-18 14:52:29] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -45.482623+0.002624j
[2025-09-18 14:52:41] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -45.499651-0.000736j
[2025-09-18 14:52:54] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -45.487723+0.002330j
[2025-09-18 14:53:06] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -45.491551-0.001140j
[2025-09-18 14:53:19] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -45.500587+0.002227j
[2025-09-18 14:53:31] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -45.492768+0.000159j
[2025-09-18 14:53:43] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -45.509238+0.001668j
[2025-09-18 14:53:56] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -45.494403-0.003032j
[2025-09-18 14:54:08] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -45.494185+0.001600j
[2025-09-18 14:54:21] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -45.496044+0.001096j
[2025-09-18 14:54:33] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -45.490397-0.003390j
[2025-09-18 14:54:46] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -45.489425+0.001033j
[2025-09-18 14:54:58] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -45.507707-0.002744j
[2025-09-18 14:55:10] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -45.489843+0.001521j
[2025-09-18 14:55:23] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -45.487427+0.000263j
[2025-09-18 14:55:35] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -45.498302-0.004010j
[2025-09-18 14:55:48] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -45.489511+0.001009j
[2025-09-18 14:56:00] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -45.496785+0.000975j
[2025-09-18 14:56:12] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -45.498495+0.000653j
[2025-09-18 14:56:25] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -45.503739-0.000991j
[2025-09-18 14:56:37] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -45.507679+0.001267j
[2025-09-18 14:56:50] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -45.484519-0.000311j
[2025-09-18 14:57:02] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -45.508545+0.000383j
[2025-09-18 14:57:15] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -45.503210-0.001093j
[2025-09-18 14:57:27] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -45.492502-0.000892j
[2025-09-18 14:57:40] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -45.488231-0.000012j
[2025-09-18 14:57:52] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -45.500706+0.000263j
[2025-09-18 14:58:04] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -45.487542+0.001995j
[2025-09-18 14:58:17] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -45.501801-0.001300j
[2025-09-18 14:58:29] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -45.492909-0.004108j
[2025-09-18 14:58:42] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -45.501184-0.003908j
[2025-09-18 14:58:54] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -45.498568+0.000176j
[2025-09-18 14:59:07] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -45.499213+0.002704j
[2025-09-18 14:59:19] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -45.509943+0.000038j
[2025-09-18 14:59:32] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -45.496262-0.002144j
[2025-09-18 14:59:44] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -45.512645+0.002055j
[2025-09-18 14:59:56] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -45.495284+0.001024j
[2025-09-18 15:00:09] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -45.499469+0.002211j
[2025-09-18 15:00:21] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -45.493756-0.000469j
[2025-09-18 15:00:34] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -45.496142+0.001180j
[2025-09-18 15:00:46] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -45.514097-0.000924j
[2025-09-18 15:00:59] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -45.496437+0.000155j
[2025-09-18 15:01:11] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -45.478747-0.002499j
[2025-09-18 15:01:24] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -45.491470+0.000940j
[2025-09-18 15:01:36] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -45.491414-0.000225j
[2025-09-18 15:01:49] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -45.503831+0.006483j
[2025-09-18 15:02:01] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -45.494783+0.000766j
[2025-09-18 15:02:13] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -45.493734+0.004931j
[2025-09-18 15:02:26] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -45.482074-0.001354j
[2025-09-18 15:02:38] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -45.498145-0.002365j
[2025-09-18 15:02:51] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -45.488087+0.001243j
[2025-09-18 15:03:03] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -45.495220-0.000605j
[2025-09-18 15:03:16] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -45.497236-0.000193j
[2025-09-18 15:03:28] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -45.495373+0.001928j
[2025-09-18 15:03:41] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -45.489798+0.001810j
[2025-09-18 15:03:53] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -45.485822+0.001797j
[2025-09-18 15:04:05] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -45.494499+0.001355j
[2025-09-18 15:04:18] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -45.504954-0.001872j
[2025-09-18 15:04:31] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -45.496330-0.001358j
[2025-09-18 15:04:43] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -45.489408-0.002079j
[2025-09-18 15:04:55] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -45.484400-0.001358j
[2025-09-18 15:05:08] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -45.496946+0.002405j
[2025-09-18 15:05:20] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -45.500010+0.006568j
[2025-09-18 15:05:33] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -45.509062+0.003384j
[2025-09-18 15:05:45] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -45.488574+0.002277j
[2025-09-18 15:05:58] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -45.488870+0.007303j
[2025-09-18 15:06:10] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -45.492413-0.000898j
[2025-09-18 15:06:22] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -45.498967+0.003724j
[2025-09-18 15:06:35] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -45.491014-0.000150j
[2025-09-18 15:06:47] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -45.487782-0.003154j
[2025-09-18 15:07:00] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -45.496908+0.000504j
[2025-09-18 15:07:12] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -45.495767+0.001780j
[2025-09-18 15:07:25] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -45.495698-0.001888j
[2025-09-18 15:07:37] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -45.487473-0.002186j
[2025-09-18 15:07:50] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -45.510092-0.002193j
[2025-09-18 15:08:02] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -45.496648+0.000117j
[2025-09-18 15:08:15] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -45.496364-0.003562j
[2025-09-18 15:08:27] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -45.510451-0.004205j
[2025-09-18 15:08:39] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -45.477920+0.000532j
[2025-09-18 15:08:52] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -45.486841+0.000255j
[2025-09-18 15:09:04] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -45.502224-0.000004j
[2025-09-18 15:09:17] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -45.482195+0.003494j
[2025-09-18 15:09:29] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -45.480002+0.005105j
[2025-09-18 15:09:42] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -45.504878+0.004149j
[2025-09-18 15:09:54] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -45.494943-0.000452j
[2025-09-18 15:10:07] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -45.496196+0.003453j
[2025-09-18 15:10:19] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -45.491631+0.001190j
[2025-09-18 15:10:31] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -45.497806+0.000184j
[2025-09-18 15:10:44] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -45.509748-0.002291j
[2025-09-18 15:10:56] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -45.502962-0.000469j
[2025-09-18 15:11:09] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -45.507093-0.003298j
[2025-09-18 15:11:21] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -45.491630+0.000576j
[2025-09-18 15:11:34] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -45.501716+0.002557j
[2025-09-18 15:11:46] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -45.487980-0.002346j
[2025-09-18 15:11:59] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -45.494520+0.005527j
[2025-09-18 15:12:11] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -45.487761+0.005239j
[2025-09-18 15:12:23] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -45.503359+0.003431j
[2025-09-18 15:12:36] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -45.502610+0.004529j
[2025-09-18 15:12:48] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -45.491424+0.002174j
[2025-09-18 15:13:01] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -45.499829+0.003612j
[2025-09-18 15:13:13] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -45.514994-0.001815j
[2025-09-18 15:13:13] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-18 15:13:26] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -45.505830+0.000637j
[2025-09-18 15:13:38] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -45.491638-0.004058j
[2025-09-18 15:13:51] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -45.503860+0.004942j
[2025-09-18 15:14:03] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -45.508972-0.001814j
[2025-09-18 15:14:15] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -45.492030+0.005051j
[2025-09-18 15:14:28] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -45.502366-0.000016j
[2025-09-18 15:14:40] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -45.510012+0.004492j
[2025-09-18 15:14:53] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -45.497321+0.001878j
[2025-09-18 15:15:05] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -45.510992+0.002117j
[2025-09-18 15:15:18] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -45.484622+0.001604j
[2025-09-18 15:15:30] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -45.496073+0.000285j
[2025-09-18 15:15:43] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -45.499639-0.001455j
[2025-09-18 15:15:55] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -45.492714+0.000643j
[2025-09-18 15:16:07] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -45.492361+0.004536j
[2025-09-18 15:16:20] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -45.496243-0.002285j
[2025-09-18 15:16:32] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -45.489993+0.003399j
[2025-09-18 15:16:45] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -45.504669+0.002712j
[2025-09-18 15:16:57] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -45.495011+0.003799j
[2025-09-18 15:17:10] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -45.494782-0.003828j
[2025-09-18 15:17:22] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -45.490143+0.001520j
[2025-09-18 15:17:35] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -45.504987-0.001031j
[2025-09-18 15:17:47] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -45.499296-0.003601j
[2025-09-18 15:18:00] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -45.496852-0.001597j
[2025-09-18 15:18:12] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -45.494353+0.001754j
[2025-09-18 15:18:24] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -45.498826+0.000226j
[2025-09-18 15:18:37] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -45.500315+0.001326j
[2025-09-18 15:18:49] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -45.491505-0.001131j
[2025-09-18 15:19:02] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -45.494725-0.000236j
[2025-09-18 15:19:14] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -45.505436+0.003760j
[2025-09-18 15:19:27] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -45.506609-0.000187j
[2025-09-18 15:19:39] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -45.492643+0.000984j
[2025-09-18 15:19:51] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -45.492252-0.005683j
[2025-09-18 15:20:04] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -45.483510-0.004429j
[2025-09-18 15:20:16] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -45.498084+0.003532j
[2025-09-18 15:20:29] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -45.493939-0.003584j
[2025-09-18 15:20:41] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -45.494230+0.001533j
[2025-09-18 15:20:54] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -45.506765+0.002063j
[2025-09-18 15:21:06] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -45.491676-0.001413j
[2025-09-18 15:21:19] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -45.486580+0.000573j
[2025-09-18 15:21:31] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -45.483829-0.000640j
[2025-09-18 15:21:43] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -45.487319-0.001151j
[2025-09-18 15:21:56] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -45.490016-0.001751j
[2025-09-18 15:22:08] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -45.498586-0.003065j
[2025-09-18 15:22:21] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -45.500862+0.004154j
[2025-09-18 15:22:33] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -45.508420+0.003591j
[2025-09-18 15:22:46] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -45.503290+0.005518j
[2025-09-18 15:22:58] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -45.479347-0.003749j
[2025-09-18 15:23:11] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -45.489593+0.000785j
[2025-09-18 15:23:23] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -45.502392+0.002934j
[2025-09-18 15:23:36] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -45.496643+0.004408j
[2025-09-18 15:23:48] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -45.493313-0.000546j
[2025-09-18 15:24:00] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -45.517781+0.000533j
[2025-09-18 15:24:13] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -45.503007-0.002883j
[2025-09-18 15:24:25] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -45.493874-0.003627j
[2025-09-18 15:24:38] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -45.510585+0.002067j
[2025-09-18 15:24:50] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -45.489839+0.001465j
[2025-09-18 15:25:03] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -45.487651+0.000044j
[2025-09-18 15:25:15] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -45.497082+0.000612j
[2025-09-18 15:25:28] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -45.494391-0.003443j
[2025-09-18 15:25:40] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -45.513783-0.000745j
[2025-09-18 15:25:52] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -45.498020-0.001321j
[2025-09-18 15:26:05] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -45.504009-0.000189j
[2025-09-18 15:26:17] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -45.512570+0.001170j
[2025-09-18 15:26:30] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -45.500628-0.005690j
[2025-09-18 15:26:42] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -45.487953+0.003410j
[2025-09-18 15:26:55] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -45.493022-0.003279j
[2025-09-18 15:27:07] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -45.521978+0.002365j
[2025-09-18 15:27:20] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -45.508505+0.000713j
[2025-09-18 15:27:32] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -45.497760-0.002054j
[2025-09-18 15:27:45] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -45.507563-0.001706j
[2025-09-18 15:27:57] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -45.486198+0.002507j
[2025-09-18 15:28:09] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -45.496329+0.001318j
[2025-09-18 15:28:22] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -45.497437-0.004721j
[2025-09-18 15:28:34] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -45.493521-0.000411j
[2025-09-18 15:28:47] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -45.494283+0.000485j
[2025-09-18 15:28:59] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -45.498937-0.003242j
[2025-09-18 15:29:12] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -45.513236+0.003747j
[2025-09-18 15:29:24] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -45.489315+0.001394j
[2025-09-18 15:29:37] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -45.491164-0.000034j
[2025-09-18 15:29:49] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -45.508128-0.000078j
[2025-09-18 15:30:01] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -45.504462-0.003278j
[2025-09-18 15:30:14] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -45.493641+0.000302j
[2025-09-18 15:30:26] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -45.505242-0.001449j
[2025-09-18 15:30:39] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -45.501401-0.000843j
[2025-09-18 15:30:51] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -45.485376-0.001041j
[2025-09-18 15:31:04] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -45.508256-0.002466j
[2025-09-18 15:31:16] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -45.493647-0.000495j
[2025-09-18 15:31:29] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -45.492568-0.002727j
[2025-09-18 15:31:41] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -45.503442+0.002026j
[2025-09-18 15:31:54] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -45.495054+0.000560j
[2025-09-18 15:32:06] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -45.484035-0.001035j
[2025-09-18 15:32:18] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -45.499550+0.000923j
[2025-09-18 15:32:31] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -45.497962-0.000020j
[2025-09-18 15:32:43] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -45.496460-0.001566j
[2025-09-18 15:32:56] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -45.490269-0.001265j
[2025-09-18 15:33:08] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -45.494610+0.001369j
[2025-09-18 15:33:21] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -45.496289+0.000702j
[2025-09-18 15:33:33] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -45.493227+0.000887j
[2025-09-18 15:33:46] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -45.509077+0.000228j
[2025-09-18 15:33:58] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -45.486027+0.002601j
[2025-09-18 15:34:10] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -45.496159+0.001484j
[2025-09-18 15:34:23] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -45.518213+0.000286j
[2025-09-18 15:34:35] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -45.503811+0.002277j
[2025-09-18 15:34:48] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -45.496517-0.001141j
[2025-09-18 15:35:00] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -45.499764-0.000952j
[2025-09-18 15:35:00] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-18 15:35:13] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -45.504027-0.001191j
[2025-09-18 15:35:25] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -45.506134-0.000789j
[2025-09-18 15:35:38] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -45.485838+0.002451j
[2025-09-18 15:35:50] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -45.505103+0.003466j
[2025-09-18 15:36:02] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -45.498458+0.001698j
[2025-09-18 15:36:15] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -45.502882+0.001782j
[2025-09-18 15:36:27] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -45.491878-0.001564j
[2025-09-18 15:36:40] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -45.492909-0.001565j
[2025-09-18 15:36:52] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -45.508787+0.000637j
[2025-09-18 15:37:05] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -45.510472+0.000901j
[2025-09-18 15:37:17] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -45.496120-0.000223j
[2025-09-18 15:37:30] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -45.482371-0.001566j
[2025-09-18 15:37:42] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -45.482926+0.000954j
[2025-09-18 15:37:55] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -45.510074-0.000664j
[2025-09-18 15:38:07] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -45.491522+0.001913j
[2025-09-18 15:38:19] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -45.502955-0.001722j
[2025-09-18 15:38:32] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -45.506754+0.001851j
[2025-09-18 15:38:44] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -45.494815-0.001799j
[2025-09-18 15:38:57] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -45.499817+0.003923j
[2025-09-18 15:39:09] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -45.514764+0.000527j
[2025-09-18 15:39:22] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -45.507747-0.003316j
[2025-09-18 15:39:34] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -45.488566-0.005176j
[2025-09-18 15:39:47] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -45.499676-0.001986j
[2025-09-18 15:39:59] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -45.494447-0.001761j
[2025-09-18 15:40:12] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -45.488236+0.001921j
[2025-09-18 15:40:24] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -45.501730-0.001304j
[2025-09-18 15:40:37] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -45.498670-0.002866j
[2025-09-18 15:40:49] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -45.497041-0.001833j
[2025-09-18 15:41:01] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -45.498392+0.005363j
[2025-09-18 15:41:14] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -45.519026+0.005454j
[2025-09-18 15:41:14] RESTART #2 | Period: 600
[2025-09-18 15:41:26] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -45.514380+0.002999j
[2025-09-18 15:41:39] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -45.504717+0.000942j
[2025-09-18 15:41:51] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -45.505376+0.000787j
[2025-09-18 15:42:04] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -45.512861-0.000876j
[2025-09-18 15:42:16] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -45.511927-0.003874j
[2025-09-18 15:42:29] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -45.516218-0.003424j
[2025-09-18 15:42:41] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -45.502459+0.001722j
[2025-09-18 15:42:53] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -45.492059+0.001093j
[2025-09-18 15:43:06] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -45.503162-0.000938j
[2025-09-18 15:43:18] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -45.485806-0.005866j
[2025-09-18 15:43:31] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -45.508643-0.006180j
[2025-09-18 15:43:43] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -45.507975-0.002790j
[2025-09-18 15:43:56] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -45.517294+0.001265j
[2025-09-18 15:44:08] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -45.504818-0.000505j
[2025-09-18 15:44:21] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -45.497479+0.001493j
[2025-09-18 15:44:33] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -45.503181+0.004343j
[2025-09-18 15:44:46] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -45.482676-0.002100j
[2025-09-18 15:44:58] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -45.486825-0.000607j
[2025-09-18 15:45:10] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -45.497577+0.000275j
[2025-09-18 15:45:23] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -45.500863+0.002742j
[2025-09-18 15:45:35] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -45.500441+0.000879j
[2025-09-18 15:45:48] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -45.480825+0.000545j
[2025-09-18 15:46:00] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -45.519295-0.006812j
[2025-09-18 15:46:13] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -45.490945+0.003114j
[2025-09-18 15:46:25] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -45.508974-0.000163j
[2025-09-18 15:46:37] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -45.498929+0.001194j
[2025-09-18 15:46:50] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -45.508360+0.000025j
[2025-09-18 15:47:02] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -45.490903+0.000207j
[2025-09-18 15:47:15] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -45.507078-0.003832j
[2025-09-18 15:47:27] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -45.492589-0.001822j
[2025-09-18 15:47:40] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -45.503659-0.001932j
[2025-09-18 15:47:52] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -45.490825-0.001366j
[2025-09-18 15:48:05] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -45.502528-0.001165j
[2025-09-18 15:48:17] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -45.498788+0.001071j
[2025-09-18 15:48:30] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -45.486811-0.000852j
[2025-09-18 15:48:42] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -45.488178-0.002184j
[2025-09-18 15:48:54] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -45.495817+0.001307j
[2025-09-18 15:49:07] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -45.490281-0.001424j
[2025-09-18 15:49:19] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -45.506651-0.002408j
[2025-09-18 15:49:32] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -45.491530-0.002469j
[2025-09-18 15:49:44] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -45.487590+0.002279j
[2025-09-18 15:49:57] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -45.485543+0.000418j
[2025-09-18 15:50:09] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -45.507428+0.000501j
[2025-09-18 15:50:22] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -45.495150+0.000738j
[2025-09-18 15:50:34] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -45.505045-0.001835j
[2025-09-18 15:50:47] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -45.495400-0.001954j
[2025-09-18 15:50:59] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -45.495416+0.001093j
[2025-09-18 15:51:12] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -45.508493+0.004191j
[2025-09-18 15:51:24] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -45.496248-0.001733j
[2025-09-18 15:51:36] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -45.500675+0.000962j
[2025-09-18 15:51:49] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -45.507940-0.001076j
[2025-09-18 15:52:01] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -45.503268+0.002913j
[2025-09-18 15:52:14] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -45.493194+0.001867j
[2025-09-18 15:52:26] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -45.505816-0.002498j
[2025-09-18 15:52:39] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -45.488706-0.002509j
[2025-09-18 15:52:51] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -45.507732+0.001290j
[2025-09-18 15:53:04] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -45.510001-0.000236j
[2025-09-18 15:53:16] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -45.505930+0.002469j
[2025-09-18 15:53:29] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -45.512158+0.003544j
[2025-09-18 15:53:41] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -45.504345-0.001467j
[2025-09-18 15:53:54] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -45.494098-0.000092j
[2025-09-18 15:54:06] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -45.506060+0.000403j
[2025-09-18 15:54:18] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -45.499140+0.000514j
[2025-09-18 15:54:31] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -45.490680+0.000105j
[2025-09-18 15:54:43] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -45.503618+0.002584j
[2025-09-18 15:54:56] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -45.501656+0.000801j
[2025-09-18 15:55:08] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -45.507219+0.001582j
[2025-09-18 15:55:21] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -45.508307+0.002094j
[2025-09-18 15:55:33] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -45.497663-0.002198j
[2025-09-18 15:55:46] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -45.500395+0.000970j
[2025-09-18 15:55:58] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -45.491165+0.003683j
[2025-09-18 15:56:11] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -45.496733+0.004845j
[2025-09-18 15:56:23] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -45.496575-0.000220j
[2025-09-18 15:56:35] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -45.510953+0.001923j
[2025-09-18 15:56:48] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -45.495974+0.000406j
[2025-09-18 15:56:48] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-18 15:57:00] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -45.497313-0.001849j
[2025-09-18 15:57:13] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -45.491027+0.001827j
[2025-09-18 15:57:25] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -45.499079-0.000628j
[2025-09-18 15:57:38] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -45.491590-0.002978j
[2025-09-18 15:57:50] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -45.491704+0.000188j
[2025-09-18 15:58:03] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -45.497322+0.000682j
[2025-09-18 15:58:15] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -45.506689+0.001067j
[2025-09-18 15:58:28] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -45.507159+0.001065j
[2025-09-18 15:58:40] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -45.499194-0.001840j
[2025-09-18 15:58:52] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -45.502397+0.003006j
[2025-09-18 15:59:05] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -45.505819+0.005162j
[2025-09-18 15:59:17] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -45.502032+0.004826j
[2025-09-18 15:59:30] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -45.501000+0.004500j
[2025-09-18 15:59:42] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -45.490337+0.001220j
[2025-09-18 15:59:55] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -45.517367+0.003801j
[2025-09-18 16:00:07] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -45.496671+0.003232j
[2025-09-18 16:00:20] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -45.504677-0.001319j
[2025-09-18 16:00:32] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -45.507554-0.001269j
[2025-09-18 16:00:45] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -45.498263-0.007696j
[2025-09-18 16:00:57] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -45.489319+0.001924j
[2025-09-18 16:01:10] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -45.497719-0.002772j
[2025-09-18 16:01:22] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -45.511629+0.002445j
[2025-09-18 16:01:34] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -45.507025+0.003246j
[2025-09-18 16:01:47] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -45.500266-0.000315j
[2025-09-18 16:01:59] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -45.488881+0.000565j
[2025-09-18 16:02:12] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -45.496603-0.001425j
[2025-09-18 16:02:24] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -45.500715-0.002056j
[2025-09-18 16:02:37] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -45.487976+0.000198j
[2025-09-18 16:02:49] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -45.504320+0.000301j
[2025-09-18 16:03:02] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -45.503057+0.006687j
[2025-09-18 16:03:14] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -45.501883-0.002159j
[2025-09-18 16:03:26] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -45.492505+0.001334j
[2025-09-18 16:03:39] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -45.500314-0.001099j
[2025-09-18 16:03:51] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -45.498801+0.000715j
[2025-09-18 16:04:04] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -45.490915+0.000555j
[2025-09-18 16:04:16] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -45.501839-0.004370j
[2025-09-18 16:04:29] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -45.508917+0.001851j
[2025-09-18 16:04:41] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -45.490956-0.001362j
[2025-09-18 16:04:54] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -45.493733+0.001934j
[2025-09-18 16:05:06] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -45.509563-0.001035j
[2025-09-18 16:05:18] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -45.488525-0.002892j
[2025-09-18 16:05:31] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -45.503085-0.000238j
[2025-09-18 16:05:43] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -45.496340-0.001323j
[2025-09-18 16:05:56] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -45.515202+0.001296j
[2025-09-18 16:06:08] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -45.498357+0.001011j
[2025-09-18 16:06:21] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -45.498237-0.000998j
[2025-09-18 16:06:33] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -45.514424+0.001534j
[2025-09-18 16:06:46] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -45.499490+0.004073j
[2025-09-18 16:06:58] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -45.510410+0.001031j
[2025-09-18 16:07:10] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -45.500858-0.000255j
[2025-09-18 16:07:23] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -45.508917+0.000001j
[2025-09-18 16:07:35] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -45.491894-0.002185j
[2025-09-18 16:07:48] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -45.499496-0.001811j
[2025-09-18 16:08:00] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -45.512873+0.001338j
[2025-09-18 16:08:13] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -45.521594+0.002657j
[2025-09-18 16:08:25] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -45.503248-0.002740j
[2025-09-18 16:08:38] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -45.501707-0.002813j
[2025-09-18 16:08:50] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -45.499099+0.002717j
[2025-09-18 16:09:02] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -45.506653-0.004726j
[2025-09-18 16:09:15] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -45.487645+0.001835j
[2025-09-18 16:09:27] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -45.501327+0.003072j
[2025-09-18 16:09:40] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -45.506482+0.005434j
[2025-09-18 16:09:52] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -45.494303+0.001824j
[2025-09-18 16:10:05] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -45.482782+0.003203j
[2025-09-18 16:10:17] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -45.496594+0.004100j
[2025-09-18 16:10:30] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -45.495503+0.003071j
[2025-09-18 16:10:42] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -45.485113+0.000801j
[2025-09-18 16:10:54] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -45.498810+0.001204j
[2025-09-18 16:11:07] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -45.496034+0.000200j
[2025-09-18 16:11:19] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -45.504287+0.001576j
[2025-09-18 16:11:32] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -45.508923+0.000361j
[2025-09-18 16:11:44] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -45.503300+0.000795j
[2025-09-18 16:11:57] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -45.511585-0.001861j
[2025-09-18 16:12:09] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -45.490082+0.002495j
[2025-09-18 16:12:22] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -45.511984-0.003976j
[2025-09-18 16:12:34] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -45.485837+0.003464j
[2025-09-18 16:12:47] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -45.507301-0.000952j
[2025-09-18 16:12:59] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -45.515535-0.003109j
[2025-09-18 16:13:11] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -45.499686+0.001099j
[2025-09-18 16:13:24] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -45.511252+0.002100j
[2025-09-18 16:13:36] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -45.502168+0.001438j
[2025-09-18 16:13:49] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -45.492531+0.001596j
[2025-09-18 16:14:01] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -45.491229+0.001156j
[2025-09-18 16:14:14] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -45.500766-0.000277j
[2025-09-18 16:14:26] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -45.518667+0.000345j
[2025-09-18 16:14:38] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -45.504615-0.001393j
[2025-09-18 16:14:51] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -45.502727+0.000993j
[2025-09-18 16:15:03] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -45.490860+0.000845j
[2025-09-18 16:15:16] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -45.492092-0.005547j
[2025-09-18 16:15:28] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -45.511650+0.000164j
[2025-09-18 16:15:41] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -45.510224+0.001334j
[2025-09-18 16:15:53] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -45.487226+0.003063j
[2025-09-18 16:16:06] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -45.515633+0.000414j
[2025-09-18 16:16:18] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -45.501593+0.001759j
[2025-09-18 16:16:30] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -45.503895+0.000565j
[2025-09-18 16:16:43] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -45.506652-0.001028j
[2025-09-18 16:16:55] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -45.488863+0.004787j
[2025-09-18 16:17:08] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -45.506782+0.001520j
[2025-09-18 16:17:20] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -45.515633+0.002070j
[2025-09-18 16:17:33] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -45.508528+0.001975j
[2025-09-18 16:17:45] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -45.502822-0.000453j
[2025-09-18 16:17:58] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -45.492234-0.000435j
[2025-09-18 16:18:10] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -45.479754-0.000305j
[2025-09-18 16:18:23] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -45.488816-0.001434j
[2025-09-18 16:18:35] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -45.482427+0.002139j
[2025-09-18 16:18:35] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-18 16:18:48] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -45.498636-0.000549j
[2025-09-18 16:19:00] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -45.499852+0.000524j
[2025-09-18 16:19:12] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -45.494917-0.005378j
[2025-09-18 16:19:25] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -45.494231-0.001432j
[2025-09-18 16:19:37] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -45.494468+0.000613j
[2025-09-18 16:19:50] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -45.516392+0.001439j
[2025-09-18 16:20:02] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -45.501142-0.000987j
[2025-09-18 16:20:15] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -45.507574+0.010062j
[2025-09-18 16:20:27] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -45.506062-0.000181j
[2025-09-18 16:20:39] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -45.496339-0.003547j
[2025-09-18 16:20:52] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -45.499920+0.004084j
[2025-09-18 16:21:04] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -45.506245+0.001923j
[2025-09-18 16:21:17] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -45.507623-0.000664j
[2025-09-18 16:21:29] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -45.497111+0.002647j
[2025-09-18 16:21:42] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -45.501035-0.002101j
[2025-09-18 16:21:54] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -45.479027+0.002261j
[2025-09-18 16:22:07] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -45.498341+0.000489j
[2025-09-18 16:22:19] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -45.521316-0.000219j
[2025-09-18 16:22:31] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -45.493288-0.001060j
[2025-09-18 16:22:44] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -45.494146-0.000941j
[2025-09-18 16:22:56] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -45.510943+0.001079j
[2025-09-18 16:23:09] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -45.495294+0.004117j
[2025-09-18 16:23:21] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -45.485415-0.000445j
[2025-09-18 16:23:34] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -45.515024-0.003405j
[2025-09-18 16:23:46] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -45.497731+0.002152j
[2025-09-18 16:23:59] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -45.511996+0.002082j
[2025-09-18 16:24:11] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -45.494907-0.003539j
[2025-09-18 16:24:24] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -45.492534-0.004395j
[2025-09-18 16:24:37] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -45.496157-0.001531j
[2025-09-18 16:24:49] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -45.500019-0.001169j
[2025-09-18 16:25:02] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -45.506039+0.000876j
[2025-09-18 16:25:14] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -45.503456+0.003359j
[2025-09-18 16:25:27] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -45.508022-0.001598j
[2025-09-18 16:25:39] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -45.500693-0.000180j
[2025-09-18 16:25:52] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -45.483227-0.002638j
[2025-09-18 16:26:04] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -45.499335+0.001589j
[2025-09-18 16:26:17] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -45.492018-0.002668j
[2025-09-18 16:26:29] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -45.496325-0.002507j
[2025-09-18 16:26:42] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -45.494181-0.003859j
[2025-09-18 16:26:55] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -45.499016+0.002038j
[2025-09-18 16:27:07] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -45.488590+0.000881j
[2025-09-18 16:27:20] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -45.513432-0.001103j
[2025-09-18 16:27:32] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -45.495912-0.000510j
[2025-09-18 16:27:45] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -45.494720-0.001467j
[2025-09-18 16:27:57] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -45.498893+0.000645j
[2025-09-18 16:28:10] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -45.486732-0.001596j
[2025-09-18 16:28:22] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -45.493879-0.002024j
[2025-09-18 16:28:35] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -45.508081-0.003505j
[2025-09-18 16:28:47] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -45.503390-0.007116j
[2025-09-18 16:29:00] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -45.499045+0.001745j
[2025-09-18 16:29:12] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -45.501518-0.004542j
[2025-09-18 16:29:25] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -45.508983-0.000323j
[2025-09-18 16:29:38] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -45.501239+0.002921j
[2025-09-18 16:29:50] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -45.488429+0.001592j
[2025-09-18 16:30:03] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -45.507859-0.000850j
[2025-09-18 16:30:15] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -45.503749+0.001798j
[2025-09-18 16:30:28] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -45.500892-0.000813j
[2025-09-18 16:30:40] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -45.514311+0.000926j
[2025-09-18 16:30:53] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -45.496517+0.002466j
[2025-09-18 16:31:05] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -45.520024-0.000320j
[2025-09-18 16:31:18] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -45.521441+0.003413j
[2025-09-18 16:31:30] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -45.497358+0.003146j
[2025-09-18 16:31:43] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -45.498807+0.003424j
[2025-09-18 16:31:56] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -45.504021+0.003653j
[2025-09-18 16:32:08] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -45.499223-0.002367j
[2025-09-18 16:32:21] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -45.515240+0.002022j
[2025-09-18 16:32:33] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -45.505594-0.003107j
[2025-09-18 16:32:46] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -45.492664-0.004407j
[2025-09-18 16:32:58] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -45.501901+0.002455j
[2025-09-18 16:33:11] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -45.497680+0.007461j
[2025-09-18 16:33:23] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -45.497276+0.000353j
[2025-09-18 16:33:36] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -45.500348-0.006619j
[2025-09-18 16:33:49] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -45.485892+0.004613j
[2025-09-18 16:34:01] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -45.511931+0.000011j
[2025-09-18 16:34:14] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -45.492864-0.001342j
[2025-09-18 16:34:26] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -45.508215-0.000404j
[2025-09-18 16:34:39] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -45.505895+0.001750j
[2025-09-18 16:34:51] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -45.496925-0.000491j
[2025-09-18 16:35:04] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -45.508475+0.004333j
[2025-09-18 16:35:16] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -45.496036-0.000933j
[2025-09-18 16:35:29] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -45.498671-0.001714j
[2025-09-18 16:35:42] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -45.511048+0.007169j
[2025-09-18 16:35:54] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -45.504076+0.001149j
[2025-09-18 16:36:07] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -45.491293+0.001063j
[2025-09-18 16:36:19] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -45.509069-0.003929j
[2025-09-18 16:36:32] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -45.501046-0.002078j
[2025-09-18 16:36:44] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -45.505354+0.003105j
[2025-09-18 16:36:57] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -45.498219-0.000056j
[2025-09-18 16:37:09] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -45.503472-0.003843j
[2025-09-18 16:37:22] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -45.494792+0.001934j
[2025-09-18 16:37:34] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -45.507765+0.001671j
[2025-09-18 16:37:47] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -45.499653-0.000573j
[2025-09-18 16:38:00] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -45.496983+0.002707j
[2025-09-18 16:38:12] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -45.510284+0.003253j
[2025-09-18 16:38:25] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -45.508136+0.003628j
[2025-09-18 16:38:37] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -45.495388-0.003479j
[2025-09-18 16:38:50] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -45.502763+0.003062j
[2025-09-18 16:39:02] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -45.503838-0.001942j
[2025-09-18 16:39:15] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -45.504927+0.001953j
[2025-09-18 16:39:28] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -45.506149+0.000495j
[2025-09-18 16:39:40] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -45.499224-0.003379j
[2025-09-18 16:39:53] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -45.497923+0.002344j
[2025-09-18 16:40:05] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -45.508273-0.001181j
[2025-09-18 16:40:18] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -45.509965+0.000872j
[2025-09-18 16:40:30] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -45.498431+0.004065j
[2025-09-18 16:40:30] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-18 16:40:43] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -45.504354+0.001438j
[2025-09-18 16:40:55] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -45.503627+0.000306j
[2025-09-18 16:41:08] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -45.506325-0.004233j
[2025-09-18 16:41:20] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -45.504929-0.003851j
[2025-09-18 16:41:33] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -45.494936-0.000738j
[2025-09-18 16:41:45] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -45.497009-0.002970j
[2025-09-18 16:41:58] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -45.493433-0.005923j
[2025-09-18 16:42:11] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -45.498044+0.000159j
[2025-09-18 16:42:23] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -45.497759+0.002868j
[2025-09-18 16:42:36] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -45.511989+0.003614j
[2025-09-18 16:42:48] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -45.509826+0.002233j
[2025-09-18 16:43:01] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -45.496964-0.000261j
[2025-09-18 16:43:13] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -45.513164-0.003531j
[2025-09-18 16:43:26] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -45.493484-0.002138j
[2025-09-18 16:43:38] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -45.507317+0.000568j
[2025-09-18 16:43:51] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -45.495990-0.002826j
[2025-09-18 16:44:03] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -45.506368-0.002272j
[2025-09-18 16:44:16] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -45.501579-0.001486j
[2025-09-18 16:44:29] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -45.502076-0.001758j
[2025-09-18 16:44:41] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -45.486612-0.001834j
[2025-09-18 16:44:54] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -45.505484-0.001539j
[2025-09-18 16:45:06] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -45.498225+0.001313j
[2025-09-18 16:45:19] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -45.499125-0.002687j
[2025-09-18 16:45:31] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -45.502715+0.000600j
[2025-09-18 16:45:44] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -45.508573+0.000739j
[2025-09-18 16:45:56] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -45.514379+0.001156j
[2025-09-18 16:46:09] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -45.495441+0.003034j
[2025-09-18 16:46:21] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -45.510323-0.000219j
[2025-09-18 16:46:34] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -45.486364-0.000364j
[2025-09-18 16:46:47] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -45.494559-0.001391j
[2025-09-18 16:46:59] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -45.523756+0.001215j
[2025-09-18 16:47:12] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -45.509180+0.003302j
[2025-09-18 16:47:24] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -45.519228-0.002371j
[2025-09-18 16:47:37] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -45.508893-0.002699j
[2025-09-18 16:47:49] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -45.494523+0.004182j
[2025-09-18 16:48:02] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -45.511701-0.001027j
[2025-09-18 16:48:14] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -45.509456+0.000380j
[2025-09-18 16:48:27] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -45.488072+0.006898j
[2025-09-18 16:48:39] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -45.492167+0.003260j
[2025-09-18 16:48:52] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -45.490826-0.002403j
[2025-09-18 16:49:04] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -45.504311+0.001416j
[2025-09-18 16:49:17] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -45.511369-0.002447j
[2025-09-18 16:49:29] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -45.486740+0.002098j
[2025-09-18 16:49:42] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -45.514765+0.000715j
[2025-09-18 16:49:55] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -45.495791-0.002768j
[2025-09-18 16:50:07] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -45.505331+0.002724j
[2025-09-18 16:50:20] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -45.493396+0.003139j
[2025-09-18 16:50:32] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -45.511258-0.002039j
[2025-09-18 16:50:45] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -45.497684+0.001018j
[2025-09-18 16:50:57] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -45.501671-0.001264j
[2025-09-18 16:51:10] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -45.482892-0.002376j
[2025-09-18 16:51:22] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -45.506197-0.000793j
[2025-09-18 16:51:35] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -45.493916-0.000273j
[2025-09-18 16:51:47] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -45.506450-0.002306j
[2025-09-18 16:52:00] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -45.484725-0.001636j
[2025-09-18 16:52:12] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -45.492807-0.004160j
[2025-09-18 16:52:25] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -45.493261+0.000916j
[2025-09-18 16:52:38] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -45.499767+0.006914j
[2025-09-18 16:52:50] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -45.498018-0.000177j
[2025-09-18 16:53:03] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -45.509909-0.000748j
[2025-09-18 16:53:15] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -45.488173+0.002197j
[2025-09-18 16:53:28] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -45.502337+0.001581j
[2025-09-18 16:53:40] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -45.501643-0.002094j
[2025-09-18 16:53:53] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -45.509875+0.000027j
[2025-09-18 16:54:06] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -45.508047-0.000421j
[2025-09-18 16:54:18] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -45.483766+0.002008j
[2025-09-18 16:54:31] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -45.500610-0.003531j
[2025-09-18 16:54:43] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -45.498123+0.001812j
[2025-09-18 16:54:56] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -45.508311-0.003015j
[2025-09-18 16:55:08] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -45.488584-0.004189j
[2025-09-18 16:55:21] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -45.504785-0.005726j
[2025-09-18 16:55:33] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -45.494535+0.001636j
[2025-09-18 16:55:46] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -45.494498+0.001625j
[2025-09-18 16:55:58] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -45.507211-0.001200j
[2025-09-18 16:56:11] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -45.512736-0.000472j
[2025-09-18 16:56:24] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -45.505715-0.001023j
[2025-09-18 16:56:36] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -45.516511-0.000188j
[2025-09-18 16:56:49] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -45.505523+0.000786j
[2025-09-18 16:57:01] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -45.501213+0.002939j
[2025-09-18 16:57:14] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -45.500435+0.001671j
[2025-09-18 16:57:26] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -45.496927+0.002690j
[2025-09-18 16:57:39] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -45.505336-0.001376j
[2025-09-18 16:57:51] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -45.511801-0.001054j
[2025-09-18 16:58:04] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -45.494484+0.001116j
[2025-09-18 16:58:16] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -45.507724+0.002109j
[2025-09-18 16:58:29] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -45.485382-0.001035j
[2025-09-18 16:58:41] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -45.517942+0.004896j
[2025-09-18 16:58:54] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -45.502681-0.000862j
[2025-09-18 16:59:07] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -45.491956-0.002786j
[2025-09-18 16:59:19] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -45.496850-0.002019j
[2025-09-18 16:59:32] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -45.500706+0.002794j
[2025-09-18 16:59:44] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -45.519861+0.003265j
[2025-09-18 16:59:57] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -45.499959-0.001711j
[2025-09-18 17:00:09] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -45.502334-0.000003j
[2025-09-18 17:00:22] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -45.492304+0.005298j
[2025-09-18 17:00:34] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -45.522552+0.004697j
[2025-09-18 17:00:47] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -45.509709-0.000822j
[2025-09-18 17:00:59] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -45.498588-0.000622j
[2025-09-18 17:01:12] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -45.492858-0.000548j
[2025-09-18 17:01:24] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -45.496174+0.002265j
[2025-09-18 17:01:37] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -45.516712-0.000188j
[2025-09-18 17:01:49] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -45.503660+0.003911j
[2025-09-18 17:02:02] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -45.501816+0.001315j
[2025-09-18 17:02:14] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -45.509844+0.002850j
[2025-09-18 17:02:27] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -45.525991-0.000970j
[2025-09-18 17:02:27] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-18 17:02:39] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -45.492579+0.000075j
[2025-09-18 17:02:52] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -45.511187+0.002712j
[2025-09-18 17:03:04] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -45.500539-0.002022j
[2025-09-18 17:03:17] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -45.514666-0.002742j
[2025-09-18 17:03:29] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -45.516287+0.000406j
[2025-09-18 17:03:42] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -45.511960+0.001628j
[2025-09-18 17:03:55] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -45.514613+0.003771j
[2025-09-18 17:04:07] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -45.498522+0.002998j
[2025-09-18 17:04:20] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -45.500156+0.002929j
[2025-09-18 17:04:32] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -45.491173-0.000766j
[2025-09-18 17:04:45] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -45.502190+0.000577j
[2025-09-18 17:04:57] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -45.502652+0.002039j
[2025-09-18 17:05:10] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -45.511000+0.003415j
[2025-09-18 17:05:22] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -45.512990+0.000762j
[2025-09-18 17:05:35] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -45.502742-0.000051j
[2025-09-18 17:05:47] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -45.506857+0.004108j
[2025-09-18 17:06:00] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -45.507540+0.001852j
[2025-09-18 17:06:12] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -45.490663+0.000003j
[2025-09-18 17:06:25] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -45.495685-0.004400j
[2025-09-18 17:06:38] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -45.503081+0.003981j
[2025-09-18 17:06:50] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -45.502769-0.000863j
[2025-09-18 17:07:03] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -45.515189-0.001739j
[2025-09-18 17:07:15] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -45.489600-0.001536j
[2025-09-18 17:07:28] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -45.499399+0.000628j
[2025-09-18 17:07:40] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -45.495193-0.000368j
[2025-09-18 17:07:53] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -45.495695+0.000643j
[2025-09-18 17:08:05] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -45.505968+0.004586j
[2025-09-18 17:08:18] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -45.510878-0.000543j
[2025-09-18 17:08:31] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -45.508063+0.000348j
[2025-09-18 17:08:43] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -45.495171+0.000662j
[2025-09-18 17:08:56] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -45.490684-0.000692j
[2025-09-18 17:09:08] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -45.501753-0.000750j
[2025-09-18 17:09:20] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -45.500395+0.001498j
[2025-09-18 17:09:33] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -45.503462-0.000594j
[2025-09-18 17:09:45] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -45.510347-0.000103j
[2025-09-18 17:09:58] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -45.491767+0.000236j
[2025-09-18 17:10:10] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -45.499267+0.000966j
[2025-09-18 17:10:23] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -45.508801+0.002694j
[2025-09-18 17:10:36] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -45.506955+0.003861j
[2025-09-18 17:10:48] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -45.505812+0.001627j
[2025-09-18 17:11:01] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -45.494192+0.002586j
[2025-09-18 17:11:13] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -45.495882-0.000713j
[2025-09-18 17:11:26] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -45.499714+0.000264j
[2025-09-18 17:11:38] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -45.501706-0.001817j
[2025-09-18 17:11:51] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -45.502403+0.005231j
[2025-09-18 17:12:03] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -45.504725-0.000599j
[2025-09-18 17:12:16] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -45.499378+0.002017j
[2025-09-18 17:12:28] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -45.510977-0.000722j
[2025-09-18 17:12:41] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -45.495621+0.000408j
[2025-09-18 17:12:54] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -45.508653+0.000380j
[2025-09-18 17:13:06] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -45.499041+0.000620j
[2025-09-18 17:13:18] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -45.507780+0.001050j
[2025-09-18 17:13:31] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -45.498695+0.001598j
[2025-09-18 17:13:43] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -45.492208-0.000378j
[2025-09-18 17:13:56] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -45.493488-0.000137j
[2025-09-18 17:14:08] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -45.501243-0.002389j
[2025-09-18 17:14:21] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -45.500541+0.000267j
[2025-09-18 17:14:33] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -45.493682-0.000446j
[2025-09-18 17:14:46] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -45.508310-0.000253j
[2025-09-18 17:14:58] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -45.493427-0.001502j
[2025-09-18 17:15:11] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -45.513748-0.002883j
[2025-09-18 17:15:23] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -45.511717+0.002286j
[2025-09-18 17:15:35] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -45.493687+0.000711j
[2025-09-18 17:15:48] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -45.494622+0.000927j
[2025-09-18 17:16:00] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -45.505602-0.002759j
[2025-09-18 17:16:13] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -45.506890-0.002676j
[2025-09-18 17:16:25] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -45.508422+0.003716j
[2025-09-18 17:16:38] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -45.519402-0.004160j
[2025-09-18 17:16:50] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -45.510748+0.004290j
[2025-09-18 17:17:03] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -45.499359-0.001751j
[2025-09-18 17:17:15] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -45.522100+0.000672j
[2025-09-18 17:17:28] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -45.505104+0.001076j
[2025-09-18 17:17:40] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -45.502362+0.001394j
[2025-09-18 17:17:52] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -45.497823+0.002532j
[2025-09-18 17:18:05] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -45.505151+0.001339j
[2025-09-18 17:18:17] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -45.505786+0.000140j
[2025-09-18 17:18:30] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -45.507793+0.001363j
[2025-09-18 17:18:42] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -45.504364+0.001276j
[2025-09-18 17:18:55] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -45.489652-0.003497j
[2025-09-18 17:19:07] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -45.511299-0.001329j
[2025-09-18 17:19:20] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -45.502576-0.003230j
[2025-09-18 17:19:32] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -45.503344+0.005689j
[2025-09-18 17:19:44] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -45.512849+0.000674j
[2025-09-18 17:19:57] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -45.515027+0.002351j
[2025-09-18 17:20:09] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -45.508058-0.002249j
[2025-09-18 17:20:22] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -45.495046+0.002586j
[2025-09-18 17:20:34] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -45.499027-0.001419j
[2025-09-18 17:20:47] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -45.496973+0.000937j
[2025-09-18 17:20:59] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -45.499543-0.003885j
[2025-09-18 17:21:12] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -45.501883-0.001971j
[2025-09-18 17:21:25] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -45.501362+0.010540j
[2025-09-18 17:21:37] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -45.509750+0.002732j
[2025-09-18 17:21:50] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -45.502022+0.008294j
[2025-09-18 17:22:02] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -45.500106-0.001138j
[2025-09-18 17:22:15] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -45.502857-0.002762j
[2025-09-18 17:22:27] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -45.507507+0.002009j
[2025-09-18 17:22:40] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -45.511005+0.002312j
[2025-09-18 17:22:52] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -45.511902+0.000921j
[2025-09-18 17:23:05] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -45.479636+0.001366j
[2025-09-18 17:23:18] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -45.515642-0.000057j
[2025-09-18 17:23:30] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -45.518727+0.000516j
[2025-09-18 17:23:43] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -45.514283+0.000341j
[2025-09-18 17:23:55] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -45.514282+0.001336j
[2025-09-18 17:24:08] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -45.502896+0.000885j
[2025-09-18 17:24:20] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -45.500072-0.000624j
[2025-09-18 17:24:20] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-18 17:24:33] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -45.507635+0.000518j
[2025-09-18 17:24:45] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -45.502083-0.000885j
[2025-09-18 17:24:57] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -45.504789-0.005770j
[2025-09-18 17:25:10] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -45.513307-0.002282j
[2025-09-18 17:25:22] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -45.506691-0.000090j
[2025-09-18 17:25:35] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -45.513002-0.001493j
[2025-09-18 17:25:47] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -45.510332+0.000634j
[2025-09-18 17:26:00] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -45.505199-0.002898j
[2025-09-18 17:26:12] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -45.519712-0.000502j
[2025-09-18 17:26:25] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -45.509153-0.000337j
[2025-09-18 17:26:37] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -45.505885-0.002117j
[2025-09-18 17:26:50] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -45.509666-0.001393j
[2025-09-18 17:27:02] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -45.492862-0.001571j
[2025-09-18 17:27:14] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -45.495402-0.002089j
[2025-09-18 17:27:27] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -45.505349+0.003125j
[2025-09-18 17:27:39] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -45.491255+0.001197j
[2025-09-18 17:27:52] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -45.503678-0.001027j
[2025-09-18 17:28:04] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -45.507227-0.001995j
[2025-09-18 17:28:17] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -45.492616+0.000427j
[2025-09-18 17:28:29] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -45.501006+0.003239j
[2025-09-18 17:28:42] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -45.501910+0.003260j
[2025-09-18 17:28:54] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -45.499087+0.000741j
[2025-09-18 17:29:06] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -45.504696+0.003976j
[2025-09-18 17:29:19] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -45.511104+0.004388j
[2025-09-18 17:29:31] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -45.489907+0.000597j
[2025-09-18 17:29:44] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -45.500403+0.000611j
[2025-09-18 17:29:56] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -45.495395-0.002900j
[2025-09-18 17:30:09] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -45.505113+0.003385j
[2025-09-18 17:30:21] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -45.503183-0.000442j
[2025-09-18 17:30:34] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -45.494706-0.001432j
[2025-09-18 17:30:46] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -45.502095+0.000567j
[2025-09-18 17:30:58] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -45.506706+0.002937j
[2025-09-18 17:31:11] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -45.507591-0.002253j
[2025-09-18 17:31:23] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -45.506717-0.000938j
[2025-09-18 17:31:36] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -45.520663+0.001022j
[2025-09-18 17:31:48] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -45.507523+0.001115j
[2025-09-18 17:32:01] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -45.494922-0.001200j
[2025-09-18 17:32:13] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -45.504880-0.002196j
[2025-09-18 17:32:26] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -45.506902+0.000098j
[2025-09-18 17:32:38] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -45.500911-0.001174j
[2025-09-18 17:32:50] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -45.490191+0.002934j
[2025-09-18 17:33:03] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -45.518845+0.000318j
[2025-09-18 17:33:15] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -45.485193-0.001360j
[2025-09-18 17:33:28] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -45.500984+0.003310j
[2025-09-18 17:33:40] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -45.501714-0.003466j
[2025-09-18 17:33:53] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -45.515291+0.000547j
[2025-09-18 17:34:05] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -45.502959-0.003023j
[2025-09-18 17:34:18] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -45.509429+0.004274j
[2025-09-18 17:34:30] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -45.492693+0.001127j
[2025-09-18 17:34:43] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -45.507532-0.000765j
[2025-09-18 17:34:55] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -45.503933+0.001803j
[2025-09-18 17:35:07] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -45.503513-0.000479j
[2025-09-18 17:35:20] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -45.511565-0.000650j
[2025-09-18 17:35:32] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -45.506920+0.004111j
[2025-09-18 17:35:45] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -45.516498-0.000450j
[2025-09-18 17:35:57] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -45.504202-0.002169j
[2025-09-18 17:36:10] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -45.502103-0.002718j
[2025-09-18 17:36:22] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -45.511521-0.000235j
[2025-09-18 17:36:35] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -45.518144-0.004698j
[2025-09-18 17:36:47] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -45.503255+0.002168j
[2025-09-18 17:36:59] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -45.496622+0.004015j
[2025-09-18 17:37:12] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -45.519992-0.002339j
[2025-09-18 17:37:24] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -45.497787+0.000863j
[2025-09-18 17:37:37] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -45.508666+0.000657j
[2025-09-18 17:37:49] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -45.500523+0.001761j
[2025-09-18 17:38:02] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -45.503462-0.003361j
[2025-09-18 17:38:14] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -45.507424+0.001686j
[2025-09-18 17:38:27] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -45.507420+0.001420j
[2025-09-18 17:38:39] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -45.503199-0.001816j
[2025-09-18 17:38:52] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -45.517528-0.000679j
[2025-09-18 17:39:04] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -45.511860-0.001464j
[2025-09-18 17:39:16] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -45.490977+0.002986j
[2025-09-18 17:39:29] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -45.508644-0.005080j
[2025-09-18 17:39:41] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -45.515164-0.002510j
[2025-09-18 17:39:54] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -45.507700-0.000568j
[2025-09-18 17:40:06] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -45.503066+0.002086j
[2025-09-18 17:40:19] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -45.501812+0.002805j
[2025-09-18 17:40:31] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -45.507752-0.004087j
[2025-09-18 17:40:44] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -45.514392+0.001447j
[2025-09-18 17:40:56] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -45.498233-0.000605j
[2025-09-18 17:41:09] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -45.520591+0.001422j
[2025-09-18 17:41:21] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -45.496747+0.000454j
[2025-09-18 17:41:34] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -45.503108-0.001014j
[2025-09-18 17:41:46] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -45.524230-0.002938j
[2025-09-18 17:41:59] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -45.511864+0.001448j
[2025-09-18 17:42:11] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -45.489686-0.000501j
[2025-09-18 17:42:24] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -45.486509+0.000717j
[2025-09-18 17:42:37] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -45.501052+0.003824j
[2025-09-18 17:42:49] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -45.491964+0.001056j
[2025-09-18 17:43:02] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -45.496372+0.002896j
[2025-09-18 17:43:14] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -45.499344+0.001173j
[2025-09-18 17:43:27] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -45.491094+0.004349j
[2025-09-18 17:43:39] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -45.502741+0.002144j
[2025-09-18 17:43:52] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -45.513920+0.002298j
[2025-09-18 17:44:04] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -45.506422-0.001618j
[2025-09-18 17:44:17] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -45.499848+0.002039j
[2025-09-18 17:44:29] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -45.498518-0.002311j
[2025-09-18 17:44:42] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -45.507319-0.002930j
[2025-09-18 17:44:54] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -45.505993-0.003713j
[2025-09-18 17:45:07] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -45.503206+0.003839j
[2025-09-18 17:45:19] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -45.507554+0.001114j
[2025-09-18 17:45:32] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -45.505625+0.000034j
[2025-09-18 17:45:44] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -45.497328+0.002903j
[2025-09-18 17:45:57] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -45.512871-0.000517j
[2025-09-18 17:46:05] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -45.513383-0.000927j
[2025-09-18 17:46:05] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-18 17:46:05] ✅ Training completed | Restarts: 2
[2025-09-18 17:46:05] ============================================================
[2025-09-18 17:46:05] Training completed | Runtime: 13180.5s
[2025-09-18 17:46:07] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-18 17:46:07] ============================================================
