[2025-09-18 21:25:33] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.82/training/checkpoints/final_GCNN.pkl
[2025-09-18 21:25:33]   - 迭代次数: final
[2025-09-18 21:25:33]   - 能量: -46.154013+0.001699j ± 0.008479
[2025-09-18 21:25:33]   - 时间戳: 2025-09-18T21:24:16.122926+08:00
[2025-09-18 21:25:49] ✓ 变分状态参数已从checkpoint恢复
[2025-09-18 21:25:49] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-18 21:25:49] ==================================================
[2025-09-18 21:25:49] GCNN for Shastry-Sutherland Model
[2025-09-18 21:25:49] ==================================================
[2025-09-18 21:25:49] System parameters:
[2025-09-18 21:25:49]   - System size: L=5, N=100
[2025-09-18 21:25:49]   - System parameters: J1=0.83, J2=1.0, Q=0.0
[2025-09-18 21:25:49] --------------------------------------------------
[2025-09-18 21:25:49] Model parameters:
[2025-09-18 21:25:49]   - Number of layers = 4
[2025-09-18 21:25:49]   - Number of features = 4
[2025-09-18 21:25:49]   - Total parameters = 19628
[2025-09-18 21:25:49] --------------------------------------------------
[2025-09-18 21:25:50] Training parameters:
[2025-09-18 21:25:50]   - Learning rate: 0.015
[2025-09-18 21:25:50]   - Total iterations: 1050
[2025-09-18 21:25:50]   - Annealing cycles: 3
[2025-09-18 21:25:50]   - Initial period: 150
[2025-09-18 21:25:50]   - Period multiplier: 2.0
[2025-09-18 21:25:50]   - Temperature range: 0.0-1.0
[2025-09-18 21:25:50]   - Samples: 4096
[2025-09-18 21:25:50]   - Discarded samples: 0
[2025-09-18 21:25:50]   - Chunk size: 2048
[2025-09-18 21:25:50]   - Diagonal shift: 0.2
[2025-09-18 21:25:50]   - Gradient clipping: 1.0
[2025-09-18 21:25:50]   - Checkpoint enabled: interval=105
[2025-09-18 21:25:50]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.83/training/checkpoints
[2025-09-18 21:25:50] --------------------------------------------------
[2025-09-18 21:25:50] Device status:
[2025-09-18 21:25:50]   - Devices model: NVIDIA H200 NVL
[2025-09-18 21:25:50]   - Number of devices: 1
[2025-09-18 21:25:50]   - Sharding: True
[2025-09-18 21:25:50] ============================================================
[2025-09-18 21:26:48] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -46.757289-0.004421j
[2025-09-18 21:27:24] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -46.805676-0.004349j
[2025-09-18 21:27:36] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -46.779154-0.003659j
[2025-09-18 21:27:49] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -46.796821+0.001503j
[2025-09-18 21:28:01] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -46.804871+0.002320j
[2025-09-18 21:28:14] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -46.819272-0.001348j
[2025-09-18 21:28:26] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -46.795685+0.000212j
[2025-09-18 21:28:39] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -46.801229-0.001455j
[2025-09-18 21:28:51] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -46.812263+0.001725j
[2025-09-18 21:29:04] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -46.803973+0.000159j
[2025-09-18 21:29:16] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -46.793257+0.000296j
[2025-09-18 21:29:29] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -46.799765+0.001196j
[2025-09-18 21:29:41] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -46.784928-0.000986j
[2025-09-18 21:29:54] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -46.794773+0.001338j
[2025-09-18 21:30:06] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -46.785270+0.000407j
[2025-09-18 21:30:18] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -46.797910-0.001146j
[2025-09-18 21:30:31] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -46.793415+0.001028j
[2025-09-18 21:30:43] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -46.807812+0.001696j
[2025-09-18 21:30:56] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -46.794191-0.000606j
[2025-09-18 21:31:08] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -46.793267+0.001245j
[2025-09-18 21:31:21] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -46.815233+0.000750j
[2025-09-18 21:31:33] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -46.795594+0.003598j
[2025-09-18 21:31:46] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -46.797037+0.002550j
[2025-09-18 21:31:58] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -46.796254-0.001524j
[2025-09-18 21:32:11] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -46.812875-0.003345j
[2025-09-18 21:32:23] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -46.777403-0.002279j
[2025-09-18 21:32:36] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -46.789388+0.001646j
[2025-09-18 21:32:48] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -46.785492-0.000823j
[2025-09-18 21:33:01] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -46.810352-0.000378j
[2025-09-18 21:33:13] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -46.796823-0.001910j
[2025-09-18 21:33:26] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -46.770409+0.003792j
[2025-09-18 21:33:38] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -46.806036+0.001073j
[2025-09-18 21:33:51] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -46.782449-0.000004j
[2025-09-18 21:34:04] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -46.781623-0.000778j
[2025-09-18 21:34:16] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -46.793184+0.004593j
[2025-09-18 21:34:28] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -46.793156+0.000472j
[2025-09-18 21:34:41] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -46.802509-0.003119j
[2025-09-18 21:34:53] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -46.804802-0.002675j
[2025-09-18 21:35:06] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -46.794834-0.000667j
[2025-09-18 21:35:18] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -46.796913+0.002660j
[2025-09-18 21:35:31] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -46.798379+0.002937j
[2025-09-18 21:35:43] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -46.806043+0.001699j
[2025-09-18 21:35:56] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -46.801167-0.000426j
[2025-09-18 21:36:08] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -46.785221+0.000754j
[2025-09-18 21:36:20] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -46.792008+0.001552j
[2025-09-18 21:36:33] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -46.796540-0.000786j
[2025-09-18 21:36:45] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -46.792097-0.001169j
[2025-09-18 21:36:58] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -46.789231-0.001068j
[2025-09-18 21:37:10] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -46.793371-0.000449j
[2025-09-18 21:37:23] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -46.809818+0.001057j
[2025-09-18 21:37:35] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -46.797252+0.003009j
[2025-09-18 21:37:48] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -46.807929-0.001140j
[2025-09-18 21:38:00] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -46.792891+0.000714j
[2025-09-18 21:38:13] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -46.790711-0.002140j
[2025-09-18 21:38:25] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -46.795991-0.002944j
[2025-09-18 21:38:37] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -46.797831-0.000084j
[2025-09-18 21:38:50] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -46.796322-0.000834j
[2025-09-18 21:39:02] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -46.780947+0.001305j
[2025-09-18 21:39:15] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -46.798736+0.003873j
[2025-09-18 21:39:27] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -46.799829-0.002395j
[2025-09-18 21:39:40] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -46.798262+0.000763j
[2025-09-18 21:39:52] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -46.790023-0.001312j
[2025-09-18 21:40:05] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -46.798526+0.003180j
[2025-09-18 21:40:17] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -46.797234-0.001996j
[2025-09-18 21:40:30] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -46.799539+0.004880j
[2025-09-18 21:40:42] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -46.789146-0.001013j
[2025-09-18 21:40:55] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -46.788466+0.000868j
[2025-09-18 21:41:07] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -46.801402-0.001070j
[2025-09-18 21:41:19] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -46.800302-0.002646j
[2025-09-18 21:41:32] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -46.795747+0.004177j
[2025-09-18 21:41:44] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -46.785592+0.002324j
[2025-09-18 21:41:57] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -46.793284-0.004321j
[2025-09-18 21:42:09] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -46.798441+0.000203j
[2025-09-18 21:42:22] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -46.790052+0.001282j
[2025-09-18 21:42:34] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -46.797505-0.000226j
[2025-09-18 21:42:46] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -46.808072-0.004618j
[2025-09-18 21:42:59] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -46.796699+0.002680j
[2025-09-18 21:43:11] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -46.792450+0.000897j
[2025-09-18 21:43:24] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -46.812094-0.001251j
[2025-09-18 21:43:36] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -46.795858-0.001207j
[2025-09-18 21:43:49] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -46.792765+0.000512j
[2025-09-18 21:44:01] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -46.790358+0.001415j
[2025-09-18 21:44:14] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -46.789437-0.002436j
[2025-09-18 21:44:26] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -46.794987+0.000117j
[2025-09-18 21:44:39] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -46.795604+0.000312j
[2025-09-18 21:44:51] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -46.790487-0.001016j
[2025-09-18 21:45:03] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -46.800221+0.001235j
[2025-09-18 21:45:16] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -46.810835-0.002983j
[2025-09-18 21:45:28] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -46.785595-0.000270j
[2025-09-18 21:45:41] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -46.784989-0.001581j
[2025-09-18 21:45:53] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -46.816404-0.000064j
[2025-09-18 21:46:06] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -46.787920-0.004539j
[2025-09-18 21:46:18] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -46.801010+0.004355j
[2025-09-18 21:46:31] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -46.783554+0.001557j
[2025-09-18 21:46:43] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -46.809841+0.000599j
[2025-09-18 21:46:56] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -46.792603-0.003806j
[2025-09-18 21:47:08] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -46.789968-0.002451j
[2025-09-18 21:47:20] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -46.795677+0.001504j
[2025-09-18 21:47:33] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -46.796052+0.003498j
[2025-09-18 21:47:45] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -46.800641+0.001300j
[2025-09-18 21:47:58] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -46.800407-0.000597j
[2025-09-18 21:48:10] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -46.796426-0.001849j
[2025-09-18 21:48:23] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -46.795542+0.002845j
[2025-09-18 21:48:35] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -46.792196+0.003693j
[2025-09-18 21:48:48] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -46.794776-0.000722j
[2025-09-18 21:48:48] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-18 21:49:00] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -46.797035+0.000807j
[2025-09-18 21:49:13] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -46.799494+0.001328j
[2025-09-18 21:49:25] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -46.805132-0.002191j
[2025-09-18 21:49:37] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -46.802446+0.001863j
[2025-09-18 21:49:50] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -46.805864+0.002429j
[2025-09-18 21:50:02] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -46.797676-0.000536j
[2025-09-18 21:50:15] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -46.806374-0.000270j
[2025-09-18 21:50:27] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -46.805023-0.002336j
[2025-09-18 21:50:40] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -46.795094-0.001818j
[2025-09-18 21:50:52] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -46.798999+0.000694j
[2025-09-18 21:51:04] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -46.806182+0.000316j
[2025-09-18 21:51:17] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -46.809508-0.001839j
[2025-09-18 21:51:29] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -46.788894+0.000555j
[2025-09-18 21:51:42] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -46.808521-0.000988j
[2025-09-18 21:51:54] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -46.800150+0.000555j
[2025-09-18 21:52:07] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -46.798111-0.002584j
[2025-09-18 21:52:19] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -46.795256-0.002354j
[2025-09-18 21:52:32] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -46.802792+0.000901j
[2025-09-18 21:52:45] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -46.809202-0.002231j
[2025-09-18 21:52:57] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -46.788726+0.000920j
[2025-09-18 21:53:10] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -46.798453-0.010809j
[2025-09-18 21:53:22] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -46.801954+0.001207j
[2025-09-18 21:53:35] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -46.805639+0.001155j
[2025-09-18 21:53:47] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -46.790342-0.000249j
[2025-09-18 21:54:00] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -46.782415-0.001841j
[2025-09-18 21:54:12] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -46.798829+0.001667j
[2025-09-18 21:54:25] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -46.788834+0.000981j
[2025-09-18 21:54:37] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -46.790381-0.000038j
[2025-09-18 21:54:49] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -46.800857-0.000454j
[2025-09-18 21:55:02] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -46.791515-0.002227j
[2025-09-18 21:55:14] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -46.804363-0.001184j
[2025-09-18 21:55:27] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -46.802937-0.000244j
[2025-09-18 21:55:39] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -46.800767+0.002738j
[2025-09-18 21:55:52] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -46.801637-0.001908j
[2025-09-18 21:56:04] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -46.793807+0.003449j
[2025-09-18 21:56:17] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -46.790989+0.000666j
[2025-09-18 21:56:29] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -46.795405-0.001065j
[2025-09-18 21:56:41] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -46.795095+0.000899j
[2025-09-18 21:56:54] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -46.800734-0.001655j
[2025-09-18 21:57:06] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -46.785879+0.001174j
[2025-09-18 21:57:19] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -46.787238+0.001595j
[2025-09-18 21:57:31] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -46.797991+0.000450j
[2025-09-18 21:57:44] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -46.796870+0.000326j
[2025-09-18 21:57:56] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -46.805873+0.002015j
[2025-09-18 21:58:09] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -46.786644+0.005083j
[2025-09-18 21:58:09] RESTART #1 | Period: 300
[2025-09-18 21:58:21] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -46.807444+0.001951j
[2025-09-18 21:58:33] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -46.801050+0.001037j
[2025-09-18 21:58:46] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -46.810732-0.002262j
[2025-09-18 21:58:58] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -46.785519-0.003995j
[2025-09-18 21:59:11] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -46.797739+0.000595j
[2025-09-18 21:59:23] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -46.793399+0.001308j
[2025-09-18 21:59:36] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -46.786181+0.001154j
[2025-09-18 21:59:48] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -46.803166+0.000568j
[2025-09-18 22:00:01] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -46.796499+0.002573j
[2025-09-18 22:00:13] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -46.804438-0.003178j
[2025-09-18 22:00:26] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -46.797148-0.002868j
[2025-09-18 22:00:38] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -46.786924-0.001373j
[2025-09-18 22:00:51] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -46.794630-0.002482j
[2025-09-18 22:01:03] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -46.783551-0.001090j
[2025-09-18 22:01:16] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -46.792583+0.001148j
[2025-09-18 22:01:28] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -46.800861-0.000345j
[2025-09-18 22:01:41] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -46.806610+0.001656j
[2025-09-18 22:01:53] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -46.797816+0.004671j
[2025-09-18 22:02:05] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -46.791791+0.002283j
[2025-09-18 22:02:18] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -46.798582-0.004283j
[2025-09-18 22:02:30] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -46.801321-0.000304j
[2025-09-18 22:02:43] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -46.804716+0.001223j
[2025-09-18 22:02:55] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -46.812394-0.000678j
[2025-09-18 22:03:08] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -46.791676+0.001603j
[2025-09-18 22:03:20] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -46.773250+0.001685j
[2025-09-18 22:03:33] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -46.810773-0.001744j
[2025-09-18 22:03:45] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -46.792937+0.004765j
[2025-09-18 22:03:58] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -46.792330-0.000649j
[2025-09-18 22:04:10] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -46.800978-0.000232j
[2025-09-18 22:04:23] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -46.800568-0.001065j
[2025-09-18 22:04:35] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -46.801980-0.003083j
[2025-09-18 22:04:48] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -46.785820+0.000834j
[2025-09-18 22:05:00] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -46.790907-0.000460j
[2025-09-18 22:05:12] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -46.799303-0.000101j
[2025-09-18 22:05:25] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -46.796588-0.000151j
[2025-09-18 22:05:37] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -46.789094-0.000128j
[2025-09-18 22:05:50] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -46.801916+0.000774j
[2025-09-18 22:06:02] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -46.805200-0.001415j
[2025-09-18 22:06:15] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -46.805694-0.000372j
[2025-09-18 22:06:27] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -46.801092-0.000124j
[2025-09-18 22:06:40] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -46.802681-0.001848j
[2025-09-18 22:06:52] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -46.812819+0.000863j
[2025-09-18 22:07:05] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -46.785852-0.000494j
[2025-09-18 22:07:17] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -46.792160-0.001974j
[2025-09-18 22:07:30] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -46.801427-0.000723j
[2025-09-18 22:07:42] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -46.795305-0.000679j
[2025-09-18 22:07:55] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -46.793410-0.000365j
[2025-09-18 22:08:07] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -46.803875+0.001498j
[2025-09-18 22:08:20] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -46.796755-0.000876j
[2025-09-18 22:08:32] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -46.783118-0.001013j
[2025-09-18 22:08:45] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -46.790807+0.001043j
[2025-09-18 22:08:57] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -46.787883-0.002686j
[2025-09-18 22:09:10] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -46.799966-0.002779j
[2025-09-18 22:09:22] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -46.790101+0.001681j
[2025-09-18 22:09:34] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -46.792071-0.001856j
[2025-09-18 22:09:47] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -46.793347-0.000770j
[2025-09-18 22:09:59] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -46.807227-0.001263j
[2025-09-18 22:10:12] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -46.789691-0.000140j
[2025-09-18 22:10:24] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -46.801415-0.001463j
[2025-09-18 22:10:37] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -46.794397+0.001871j
[2025-09-18 22:10:37] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-18 22:10:49] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -46.791272+0.000590j
[2025-09-18 22:11:02] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -46.809796+0.003809j
[2025-09-18 22:11:14] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -46.790760+0.003459j
[2025-09-18 22:11:26] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -46.794553+0.000510j
[2025-09-18 22:11:39] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -46.783072+0.002235j
[2025-09-18 22:11:51] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -46.794143+0.002217j
[2025-09-18 22:12:04] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -46.802968+0.001829j
[2025-09-18 22:12:16] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -46.805916+0.003232j
[2025-09-18 22:12:29] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -46.800211+0.002481j
[2025-09-18 22:12:41] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -46.799249-0.002601j
[2025-09-18 22:12:54] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -46.801688-0.001459j
[2025-09-18 22:13:06] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -46.799557+0.001017j
[2025-09-18 22:13:18] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -46.791183+0.000984j
[2025-09-18 22:13:31] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -46.793641+0.000080j
[2025-09-18 22:13:43] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -46.800705-0.000359j
[2025-09-18 22:13:56] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -46.808720+0.000238j
[2025-09-18 22:14:08] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -46.794925+0.001928j
[2025-09-18 22:14:21] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -46.791688+0.001017j
[2025-09-18 22:14:33] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -46.818409-0.000897j
[2025-09-18 22:14:46] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -46.797656-0.001200j
[2025-09-18 22:14:58] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -46.802456+0.000035j
[2025-09-18 22:15:11] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -46.794661-0.000102j
[2025-09-18 22:15:23] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -46.803556-0.000147j
[2025-09-18 22:15:35] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -46.786387-0.000838j
[2025-09-18 22:15:48] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -46.810054+0.000072j
[2025-09-18 22:16:00] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -46.799438-0.002587j
[2025-09-18 22:16:13] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -46.796671-0.002952j
[2025-09-18 22:16:25] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -46.785401-0.001485j
[2025-09-18 22:16:38] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -46.793749-0.008170j
[2025-09-18 22:16:50] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -46.800539+0.000040j
[2025-09-18 22:17:03] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -46.787914+0.001721j
[2025-09-18 22:17:15] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -46.778177+0.002184j
[2025-09-18 22:17:27] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -46.790426+0.007837j
[2025-09-18 22:17:40] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -46.791519-0.000266j
[2025-09-18 22:17:52] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -46.790997-0.000741j
[2025-09-18 22:18:05] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -46.812142+0.003077j
[2025-09-18 22:18:17] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -46.789654-0.001445j
[2025-09-18 22:18:30] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -46.784811-0.001218j
[2025-09-18 22:18:42] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -46.796734-0.002970j
[2025-09-18 22:18:55] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -46.789894+0.002465j
[2025-09-18 22:19:07] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -46.792004+0.001226j
[2025-09-18 22:19:20] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -46.807993+0.003655j
[2025-09-18 22:19:32] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -46.811239+0.001238j
[2025-09-18 22:19:44] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -46.801934+0.000343j
[2025-09-18 22:19:57] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -46.794450-0.003066j
[2025-09-18 22:20:09] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -46.799481-0.000142j
[2025-09-18 22:20:22] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -46.801278-0.002219j
[2025-09-18 22:20:34] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -46.802351-0.002502j
[2025-09-18 22:20:47] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -46.792508+0.002255j
[2025-09-18 22:20:59] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -46.802688+0.000551j
[2025-09-18 22:21:12] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -46.802108+0.001849j
[2025-09-18 22:21:24] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -46.792956+0.001556j
[2025-09-18 22:21:36] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -46.797755+0.000637j
[2025-09-18 22:21:49] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -46.808333-0.002011j
[2025-09-18 22:22:01] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -46.792754-0.000300j
[2025-09-18 22:22:14] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -46.798395+0.000462j
[2025-09-18 22:22:26] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -46.795823+0.001124j
[2025-09-18 22:22:39] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -46.798737-0.002561j
[2025-09-18 22:22:51] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -46.789871-0.000799j
[2025-09-18 22:23:04] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -46.790025+0.001269j
[2025-09-18 22:23:16] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -46.808870-0.002933j
[2025-09-18 22:23:28] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -46.792097+0.000719j
[2025-09-18 22:23:41] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -46.798056-0.002988j
[2025-09-18 22:23:53] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -46.799483+0.001997j
[2025-09-18 22:24:06] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -46.794545+0.000745j
[2025-09-18 22:24:18] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -46.793860+0.005060j
[2025-09-18 22:24:31] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -46.806278+0.000146j
[2025-09-18 22:24:43] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -46.798785-0.004192j
[2025-09-18 22:24:56] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -46.803920-0.002324j
[2025-09-18 22:25:08] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -46.801081+0.002788j
[2025-09-18 22:25:21] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -46.777393-0.001747j
[2025-09-18 22:25:33] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -46.794756+0.003402j
[2025-09-18 22:25:45] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -46.802618+0.000566j
[2025-09-18 22:25:58] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -46.796908+0.002046j
[2025-09-18 22:26:10] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -46.802037+0.000910j
[2025-09-18 22:26:23] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -46.800223-0.000919j
[2025-09-18 22:26:35] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -46.816507-0.002453j
[2025-09-18 22:26:48] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -46.805155+0.002390j
[2025-09-18 22:27:00] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -46.789775-0.001907j
[2025-09-18 22:27:12] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -46.816321+0.000206j
[2025-09-18 22:27:25] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -46.799323+0.000018j
[2025-09-18 22:27:37] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -46.806495-0.003142j
[2025-09-18 22:27:50] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -46.798678-0.000144j
[2025-09-18 22:28:02] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -46.803422+0.002659j
[2025-09-18 22:28:15] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -46.782872-0.003123j
[2025-09-18 22:28:27] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -46.798997-0.002029j
[2025-09-18 22:28:40] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -46.800869+0.001221j
[2025-09-18 22:28:52] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -46.797661+0.001320j
[2025-09-18 22:29:04] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -46.793498+0.009002j
[2025-09-18 22:29:17] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -46.786711-0.002544j
[2025-09-18 22:29:29] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -46.794084+0.000292j
[2025-09-18 22:29:42] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -46.798666-0.002582j
[2025-09-18 22:29:54] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -46.791675-0.000004j
[2025-09-18 22:30:07] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -46.789411+0.000022j
[2025-09-18 22:30:19] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -46.791492-0.000185j
[2025-09-18 22:30:32] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -46.787472+0.000739j
[2025-09-18 22:30:44] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -46.800687-0.002130j
[2025-09-18 22:30:56] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -46.796875+0.000346j
[2025-09-18 22:31:09] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -46.781055+0.000051j
[2025-09-18 22:31:21] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -46.783930+0.001946j
[2025-09-18 22:31:34] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -46.802876+0.000856j
[2025-09-18 22:31:46] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -46.797419-0.001241j
[2025-09-18 22:31:59] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -46.809818+0.001986j
[2025-09-18 22:32:11] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -46.803363+0.001477j
[2025-09-18 22:32:24] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -46.783749-0.006741j
[2025-09-18 22:32:24] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-18 22:32:36] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -46.804691+0.000840j
[2025-09-18 22:32:49] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -46.801313+0.001214j
[2025-09-18 22:33:01] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -46.807063+0.001420j
[2025-09-18 22:33:14] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -46.798203+0.004500j
[2025-09-18 22:33:26] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -46.785509-0.003241j
[2025-09-18 22:33:39] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -46.795915-0.005347j
[2025-09-18 22:33:51] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -46.789700-0.000654j
[2025-09-18 22:34:03] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -46.799077+0.000007j
[2025-09-18 22:34:16] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -46.776617-0.000266j
[2025-09-18 22:34:28] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -46.791066+0.003108j
[2025-09-18 22:34:41] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -46.806499+0.000965j
[2025-09-18 22:34:53] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -46.796846+0.000203j
[2025-09-18 22:35:06] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -46.798861+0.008446j
[2025-09-18 22:35:18] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -46.796539+0.002029j
[2025-09-18 22:35:31] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -46.784514-0.001819j
[2025-09-18 22:35:43] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -46.796937+0.001440j
[2025-09-18 22:35:56] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -46.814168+0.000564j
[2025-09-18 22:36:08] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -46.796786+0.001731j
[2025-09-18 22:36:20] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -46.798496+0.000029j
[2025-09-18 22:36:33] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -46.801928-0.001464j
[2025-09-18 22:36:45] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -46.796221+0.001492j
[2025-09-18 22:36:58] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -46.791171+0.001932j
[2025-09-18 22:37:10] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -46.799783+0.001212j
[2025-09-18 22:37:23] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -46.783495-0.000185j
[2025-09-18 22:37:35] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -46.793666+0.001731j
[2025-09-18 22:37:48] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -46.797347-0.000054j
[2025-09-18 22:38:00] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -46.797525+0.001497j
[2025-09-18 22:38:13] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -46.795633+0.000654j
[2025-09-18 22:38:25] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -46.790318+0.003073j
[2025-09-18 22:38:38] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -46.776317-0.002701j
[2025-09-18 22:38:50] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -46.796962-0.000746j
[2025-09-18 22:39:02] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -46.810073-0.002802j
[2025-09-18 22:39:15] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -46.789545-0.004280j
[2025-09-18 22:39:27] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -46.784444+0.000194j
[2025-09-18 22:39:40] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -46.796322+0.005825j
[2025-09-18 22:39:52] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -46.798642-0.003396j
[2025-09-18 22:40:05] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -46.802342+0.000727j
[2025-09-18 22:40:17] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -46.787757+0.001695j
[2025-09-18 22:40:30] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -46.796218-0.001046j
[2025-09-18 22:40:42] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -46.795413-0.000310j
[2025-09-18 22:40:55] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -46.795362+0.001608j
[2025-09-18 22:41:07] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -46.807558+0.000701j
[2025-09-18 22:41:19] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -46.799642-0.006835j
[2025-09-18 22:41:32] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -46.794919+0.001374j
[2025-09-18 22:41:44] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -46.788880+0.000600j
[2025-09-18 22:41:57] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -46.796199+0.002059j
[2025-09-18 22:42:09] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -46.786861-0.001729j
[2025-09-18 22:42:22] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -46.797209-0.000742j
[2025-09-18 22:42:34] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -46.784960-0.000084j
[2025-09-18 22:42:47] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -46.814460+0.000557j
[2025-09-18 22:42:59] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -46.793924-0.001091j
[2025-09-18 22:43:12] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -46.810401+0.003106j
[2025-09-18 22:43:24] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -46.808309+0.001133j
[2025-09-18 22:43:36] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -46.802812+0.000940j
[2025-09-18 22:43:49] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -46.803686+0.001581j
[2025-09-18 22:44:01] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -46.801977-0.000859j
[2025-09-18 22:44:14] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -46.794691-0.001224j
[2025-09-18 22:44:26] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -46.798448-0.000260j
[2025-09-18 22:44:39] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -46.794529+0.002866j
[2025-09-18 22:44:51] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -46.796175+0.001138j
[2025-09-18 22:45:04] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -46.785629-0.000744j
[2025-09-18 22:45:16] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -46.791784-0.002103j
[2025-09-18 22:45:29] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -46.812409-0.000527j
[2025-09-18 22:45:41] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -46.816606-0.001450j
[2025-09-18 22:45:54] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -46.794340-0.000711j
[2025-09-18 22:46:06] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -46.807465+0.001502j
[2025-09-18 22:46:18] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -46.801472-0.001953j
[2025-09-18 22:46:31] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -46.801083+0.001205j
[2025-09-18 22:46:43] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -46.798560+0.003130j
[2025-09-18 22:46:56] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -46.798805+0.001418j
[2025-09-18 22:47:08] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -46.802161+0.000137j
[2025-09-18 22:47:21] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -46.808025+0.002300j
[2025-09-18 22:47:33] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -46.807250+0.000111j
[2025-09-18 22:47:46] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -46.809054-0.001114j
[2025-09-18 22:47:58] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -46.807438+0.001264j
[2025-09-18 22:48:10] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -46.797642-0.002712j
[2025-09-18 22:48:23] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -46.798030-0.001220j
[2025-09-18 22:48:35] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -46.803901-0.002457j
[2025-09-18 22:48:48] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -46.796929-0.000987j
[2025-09-18 22:49:00] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -46.799720+0.005872j
[2025-09-18 22:49:13] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -46.803882+0.000787j
[2025-09-18 22:49:25] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -46.797691-0.002344j
[2025-09-18 22:49:38] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -46.794684-0.002521j
[2025-09-18 22:49:50] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -46.795065-0.000788j
[2025-09-18 22:50:03] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -46.793684-0.000306j
[2025-09-18 22:50:15] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -46.804857-0.002883j
[2025-09-18 22:50:27] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -46.794623-0.000861j
[2025-09-18 22:50:40] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -46.797301+0.000515j
[2025-09-18 22:50:52] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -46.795528-0.004082j
[2025-09-18 22:51:05] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -46.812013+0.000577j
[2025-09-18 22:51:17] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -46.803156-0.002851j
[2025-09-18 22:51:30] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -46.794269-0.003384j
[2025-09-18 22:51:42] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -46.809548-0.003448j
[2025-09-18 22:51:55] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -46.810102+0.001379j
[2025-09-18 22:52:07] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -46.799181-0.002794j
[2025-09-18 22:52:20] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -46.805852-0.001320j
[2025-09-18 22:52:32] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -46.799657-0.001960j
[2025-09-18 22:52:45] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -46.796391+0.003364j
[2025-09-18 22:52:57] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -46.798028+0.004488j
[2025-09-18 22:53:10] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -46.804973-0.000609j
[2025-09-18 22:53:22] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -46.792378+0.002039j
[2025-09-18 22:53:34] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -46.799787-0.003039j
[2025-09-18 22:53:47] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -46.791727+0.004145j
[2025-09-18 22:53:59] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -46.796700+0.003035j
[2025-09-18 22:54:12] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -46.799974+0.001561j
[2025-09-18 22:54:12] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-18 22:54:24] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -46.807548+0.002225j
[2025-09-18 22:54:37] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -46.811477+0.002020j
[2025-09-18 22:54:49] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -46.795571-0.004491j
[2025-09-18 22:55:02] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -46.811364+0.000983j
[2025-09-18 22:55:14] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -46.786295-0.002692j
[2025-09-18 22:55:27] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -46.798269+0.000099j
[2025-09-18 22:55:39] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -46.803339+0.000069j
[2025-09-18 22:55:51] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -46.816235-0.000012j
[2025-09-18 22:56:04] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -46.795367-0.003900j
[2025-09-18 22:56:16] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -46.800148-0.002088j
[2025-09-18 22:56:29] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -46.793342-0.000679j
[2025-09-18 22:56:41] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -46.795595-0.003959j
[2025-09-18 22:56:54] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -46.785469+0.001602j
[2025-09-18 22:57:06] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -46.792260+0.000070j
[2025-09-18 22:57:19] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -46.784620+0.000615j
[2025-09-18 22:57:31] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -46.788116-0.000316j
[2025-09-18 22:57:43] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -46.787778+0.001131j
[2025-09-18 22:57:56] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -46.797641+0.002841j
[2025-09-18 22:58:08] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -46.810322+0.004648j
[2025-09-18 22:58:21] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -46.779567+0.001312j
[2025-09-18 22:58:33] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -46.805046-0.003048j
[2025-09-18 22:58:46] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -46.801703+0.003086j
[2025-09-18 22:58:58] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -46.813280+0.000091j
[2025-09-18 22:59:11] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -46.811634+0.000979j
[2025-09-18 22:59:23] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -46.799782-0.001992j
[2025-09-18 22:59:36] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -46.806141-0.000586j
[2025-09-18 22:59:48] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -46.808436+0.001183j
[2025-09-18 23:00:01] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -46.793018-0.001466j
[2025-09-18 23:00:13] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -46.790159+0.002170j
[2025-09-18 23:00:25] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -46.797284-0.002369j
[2025-09-18 23:00:25] RESTART #2 | Period: 600
[2025-09-18 23:00:38] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -46.791978+0.001373j
[2025-09-18 23:00:50] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -46.805710-0.000913j
[2025-09-18 23:01:03] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -46.783791-0.001325j
[2025-09-18 23:01:15] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -46.798855+0.001717j
[2025-09-18 23:01:28] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -46.800646-0.000112j
[2025-09-18 23:01:40] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -46.792681-0.002058j
[2025-09-18 23:01:53] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -46.791336-0.000766j
[2025-09-18 23:02:05] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -46.799082-0.001567j
[2025-09-18 23:02:18] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -46.801693+0.001925j
[2025-09-18 23:02:30] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -46.808393+0.002671j
[2025-09-18 23:02:43] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -46.799798+0.002925j
[2025-09-18 23:02:55] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -46.792285+0.002519j
[2025-09-18 23:03:07] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -46.785681+0.000766j
[2025-09-18 23:03:20] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -46.809409-0.002101j
[2025-09-18 23:03:32] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -46.794964-0.001220j
[2025-09-18 23:03:45] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -46.797742+0.002612j
[2025-09-18 23:03:57] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -46.812298+0.000688j
[2025-09-18 23:04:10] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -46.811737+0.002716j
[2025-09-18 23:04:22] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -46.785783-0.000228j
[2025-09-18 23:04:35] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -46.797615+0.001038j
[2025-09-18 23:04:47] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -46.794463+0.001018j
[2025-09-18 23:05:00] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -46.794139+0.000032j
[2025-09-18 23:05:12] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -46.800924-0.000381j
[2025-09-18 23:05:25] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -46.801227+0.001591j
[2025-09-18 23:05:37] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -46.799305+0.000040j
[2025-09-18 23:05:50] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -46.796603-0.004143j
[2025-09-18 23:06:02] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -46.794932-0.001768j
[2025-09-18 23:06:14] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -46.793494+0.003193j
[2025-09-18 23:06:27] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -46.783576-0.000762j
[2025-09-18 23:06:39] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -46.795097+0.003985j
[2025-09-18 23:06:52] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -46.804720-0.002348j
[2025-09-18 23:07:04] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -46.789679+0.003161j
[2025-09-18 23:07:17] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -46.800803-0.001064j
[2025-09-18 23:07:29] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -46.805490+0.001622j
[2025-09-18 23:07:42] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -46.811223-0.001703j
[2025-09-18 23:07:54] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -46.807410+0.001213j
[2025-09-18 23:08:07] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -46.773113-0.026160j
[2025-09-18 23:08:19] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -46.808235-0.000215j
[2025-09-18 23:08:32] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -46.793811+0.001928j
[2025-09-18 23:08:44] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -46.802846+0.001820j
[2025-09-18 23:08:56] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -46.790931-0.000566j
[2025-09-18 23:09:09] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -46.795248+0.002463j
[2025-09-18 23:09:21] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -46.800468+0.002136j
[2025-09-18 23:09:34] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -46.808977+0.000793j
[2025-09-18 23:09:46] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -46.791688-0.013355j
[2025-09-18 23:09:59] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -46.803477+0.001142j
[2025-09-18 23:10:11] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -46.790316-0.004148j
[2025-09-18 23:10:24] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -46.793899+0.000836j
[2025-09-18 23:10:36] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -46.813725+0.000670j
[2025-09-18 23:10:49] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -46.799090-0.001805j
[2025-09-18 23:11:01] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -46.797436+0.001309j
[2025-09-18 23:11:14] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -46.801256-0.001396j
[2025-09-18 23:11:26] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -46.796160+0.000695j
[2025-09-18 23:11:39] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -46.786897+0.003284j
[2025-09-18 23:11:51] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -46.803325-0.001571j
[2025-09-18 23:12:03] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -46.788614-0.001300j
[2025-09-18 23:12:16] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -46.787458+0.000050j
[2025-09-18 23:12:28] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -46.802979+0.002032j
[2025-09-18 23:12:41] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -46.801831-0.000974j
[2025-09-18 23:12:53] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -46.810759+0.000464j
[2025-09-18 23:13:06] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -46.788396-0.000984j
[2025-09-18 23:13:18] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -46.792183+0.004491j
[2025-09-18 23:13:31] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -46.794351-0.003731j
[2025-09-18 23:13:43] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -46.782939+0.003755j
[2025-09-18 23:13:56] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -46.808237+0.000269j
[2025-09-18 23:14:08] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -46.806992+0.004294j
[2025-09-18 23:14:21] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -46.798860+0.002628j
[2025-09-18 23:14:33] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -46.809674+0.003564j
[2025-09-18 23:14:46] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -46.807686+0.001853j
[2025-09-18 23:14:58] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -46.813384-0.001633j
[2025-09-18 23:15:10] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -46.794128-0.000894j
[2025-09-18 23:15:23] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -46.787713-0.000617j
[2025-09-18 23:15:35] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -46.787310+0.001594j
[2025-09-18 23:15:48] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -46.796768-0.003871j
[2025-09-18 23:16:00] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -46.801614-0.000082j
[2025-09-18 23:16:01] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-18 23:16:13] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -46.803004-0.000462j
[2025-09-18 23:16:25] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -46.790920+0.001497j
[2025-09-18 23:16:38] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -46.808763-0.000222j
[2025-09-18 23:16:50] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -46.801378-0.002221j
[2025-09-18 23:17:03] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -46.798297+0.001122j
[2025-09-18 23:17:15] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -46.801418-0.002624j
[2025-09-18 23:17:28] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -46.804967-0.004848j
[2025-09-18 23:17:40] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -46.800509+0.001954j
[2025-09-18 23:17:52] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -46.811074+0.001429j
[2025-09-18 23:18:05] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -46.799859+0.000461j
[2025-09-18 23:18:17] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -46.795535-0.000389j
[2025-09-18 23:18:30] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -46.810126-0.001062j
[2025-09-18 23:18:42] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -46.801864+0.001503j
[2025-09-18 23:18:55] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -46.791274-0.000269j
[2025-09-18 23:19:07] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -46.799789-0.000962j
[2025-09-18 23:19:19] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -46.801000-0.001156j
[2025-09-18 23:19:32] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -46.801262+0.002471j
[2025-09-18 23:19:44] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -46.797985-0.000221j
[2025-09-18 23:19:57] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -46.799697-0.001976j
[2025-09-18 23:20:09] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -46.809032-0.002429j
[2025-09-18 23:20:22] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -46.803586-0.001956j
[2025-09-18 23:20:34] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -46.805310-0.003253j
[2025-09-18 23:20:47] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -46.799388+0.000975j
[2025-09-18 23:20:59] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -46.795693-0.000991j
[2025-09-18 23:21:12] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -46.805376+0.001746j
[2025-09-18 23:21:24] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -46.790124-0.001197j
[2025-09-18 23:21:37] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -46.787392-0.002243j
[2025-09-18 23:21:49] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -46.800409-0.001264j
[2025-09-18 23:22:02] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -46.795852-0.001210j
[2025-09-18 23:22:14] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -46.802015+0.000730j
[2025-09-18 23:22:27] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -46.813034+0.004544j
[2025-09-18 23:22:39] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -46.804611+0.002056j
[2025-09-18 23:22:51] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -46.792562-0.002549j
[2025-09-18 23:23:04] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -46.793903-0.001600j
[2025-09-18 23:23:16] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -46.805203+0.001180j
[2025-09-18 23:23:29] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -46.802635-0.000377j
[2025-09-18 23:23:41] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -46.794774-0.001942j
[2025-09-18 23:23:54] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -46.789745+0.002563j
[2025-09-18 23:24:06] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -46.800564+0.001417j
[2025-09-18 23:24:19] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -46.798153+0.003973j
[2025-09-18 23:24:31] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -46.810591-0.005949j
[2025-09-18 23:24:43] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -46.811919-0.000112j
[2025-09-18 23:24:56] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -46.802029-0.001695j
[2025-09-18 23:25:08] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -46.794631+0.000699j
[2025-09-18 23:25:21] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -46.797973+0.001748j
[2025-09-18 23:25:33] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -46.787569+0.000440j
[2025-09-18 23:25:46] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -46.791097-0.000470j
[2025-09-18 23:25:58] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -46.793783-0.001149j
[2025-09-18 23:26:10] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -46.795930-0.002072j
[2025-09-18 23:26:23] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -46.789080-0.000912j
[2025-09-18 23:26:35] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -46.786878-0.000284j
[2025-09-18 23:26:48] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -46.794262-0.005030j
[2025-09-18 23:27:00] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -46.800172+0.000145j
[2025-09-18 23:27:13] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -46.800222-0.001209j
[2025-09-18 23:27:25] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -46.793065-0.000703j
[2025-09-18 23:27:38] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -46.803366-0.002685j
[2025-09-18 23:27:50] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -46.802681-0.002732j
[2025-09-18 23:28:02] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -46.803305+0.001790j
[2025-09-18 23:28:15] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -46.795527+0.002677j
[2025-09-18 23:28:27] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -46.799765+0.002350j
[2025-09-18 23:28:40] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -46.804692+0.000929j
[2025-09-18 23:28:52] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -46.810658-0.000551j
[2025-09-18 23:29:05] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -46.794061+0.004157j
[2025-09-18 23:29:17] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -46.801881-0.001875j
[2025-09-18 23:29:30] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -46.811697+0.001749j
[2025-09-18 23:29:42] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -46.795499-0.002884j
[2025-09-18 23:29:55] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -46.812485+0.000037j
[2025-09-18 23:30:07] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -46.807866+0.001244j
[2025-09-18 23:30:20] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -46.795707-0.005148j
[2025-09-18 23:30:32] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -46.807005-0.002948j
[2025-09-18 23:30:45] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -46.788156-0.000056j
[2025-09-18 23:30:57] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -46.794491-0.000505j
[2025-09-18 23:31:10] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -46.795905-0.000058j
[2025-09-18 23:31:22] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -46.799188+0.002878j
[2025-09-18 23:31:35] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -46.791676+0.012535j
[2025-09-18 23:31:47] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -46.811360-0.002261j
[2025-09-18 23:32:00] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -46.800424-0.003297j
[2025-09-18 23:32:13] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -46.803191+0.001678j
[2025-09-18 23:32:25] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -46.801216-0.000874j
[2025-09-18 23:32:38] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -46.804517+0.000463j
[2025-09-18 23:32:50] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -46.806648-0.001582j
[2025-09-18 23:33:03] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -46.801052+0.001125j
[2025-09-18 23:33:15] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -46.797356-0.001580j
[2025-09-18 23:33:28] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -46.798167-0.003870j
[2025-09-18 23:33:40] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -46.799883-0.000362j
[2025-09-18 23:33:53] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -46.805054-0.003639j
[2025-09-18 23:34:06] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -46.801229+0.001345j
[2025-09-18 23:34:18] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -46.794449-0.000502j
[2025-09-18 23:34:31] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -46.811986-0.003799j
[2025-09-18 23:34:43] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -46.804199-0.004152j
[2025-09-18 23:34:56] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -46.795535-0.001294j
[2025-09-18 23:35:08] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -46.808702+0.003747j
[2025-09-18 23:35:21] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -46.798988-0.000211j
[2025-09-18 23:35:34] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -46.807390+0.002100j
[2025-09-18 23:35:46] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -46.808150+0.004374j
[2025-09-18 23:35:59] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -46.798302+0.001788j
[2025-09-18 23:36:11] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -46.802865-0.003646j
[2025-09-18 23:36:24] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -46.792462+0.001034j
[2025-09-18 23:36:36] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -46.793690+0.000728j
[2025-09-18 23:36:49] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -46.805074+0.000948j
[2025-09-18 23:37:01] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -46.790122+0.001093j
[2025-09-18 23:37:14] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -46.795549+0.004975j
[2025-09-18 23:37:26] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -46.797870+0.001831j
[2025-09-18 23:37:39] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -46.804942+0.001870j
[2025-09-18 23:37:51] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -46.803376+0.002616j
[2025-09-18 23:37:51] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-18 23:38:04] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -46.795470+0.005485j
[2025-09-18 23:38:16] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -46.790508-0.001167j
[2025-09-18 23:38:29] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -46.790143-0.001902j
[2025-09-18 23:38:42] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -46.799171-0.004293j
[2025-09-18 23:38:54] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -46.798138+0.003013j
[2025-09-18 23:39:07] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -46.788046+0.000833j
[2025-09-18 23:39:19] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -46.808966-0.000819j
[2025-09-18 23:39:32] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -46.807014+0.000625j
[2025-09-18 23:39:44] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -46.803410+0.000771j
[2025-09-18 23:39:57] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -46.813133-0.000694j
[2025-09-18 23:40:09] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -46.803448+0.001021j
[2025-09-18 23:40:22] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -46.801217-0.000638j
[2025-09-18 23:40:34] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -46.793806-0.001122j
[2025-09-18 23:40:47] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -46.796058+0.003573j
[2025-09-18 23:40:59] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -46.790699-0.004450j
[2025-09-18 23:41:12] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -46.794225-0.002343j
[2025-09-18 23:41:24] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -46.802823+0.001600j
[2025-09-18 23:41:37] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -46.806628+0.007799j
[2025-09-18 23:41:50] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -46.790303-0.002067j
[2025-09-18 23:42:02] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -46.794811-0.001462j
[2025-09-18 23:42:15] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -46.821237-0.000259j
[2025-09-18 23:42:27] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -46.792100+0.002544j
[2025-09-18 23:42:40] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -46.805680-0.001030j
[2025-09-18 23:42:52] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -46.799761-0.001742j
[2025-09-18 23:43:05] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -46.799086-0.005455j
[2025-09-18 23:43:17] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -46.798139+0.001533j
[2025-09-18 23:43:30] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -46.806759-0.001206j
[2025-09-18 23:43:42] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -46.788825+0.004466j
[2025-09-18 23:43:55] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -46.797522-0.000393j
[2025-09-18 23:44:07] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -46.796257-0.000228j
[2025-09-18 23:44:20] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -46.819997-0.002136j
[2025-09-18 23:44:32] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -46.796232-0.004435j
[2025-09-18 23:44:45] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -46.793720+0.000135j
[2025-09-18 23:44:57] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -46.784047-0.002563j
[2025-09-18 23:45:10] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -46.806988+0.001314j
[2025-09-18 23:45:22] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -46.798604+0.002133j
[2025-09-18 23:45:35] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -46.804907-0.000319j
[2025-09-18 23:45:47] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -46.792625-0.000715j
[2025-09-18 23:46:00] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -46.798496-0.000844j
[2025-09-18 23:46:12] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -46.800032-0.001251j
[2025-09-18 23:46:25] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -46.800056-0.002829j
[2025-09-18 23:46:37] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -46.796185+0.002854j
[2025-09-18 23:46:49] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -46.796790+0.000225j
[2025-09-18 23:47:02] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -46.793303-0.000739j
[2025-09-18 23:47:14] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -46.788729+0.000700j
[2025-09-18 23:47:27] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -46.790405-0.000052j
[2025-09-18 23:47:39] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -46.807618+0.001635j
[2025-09-18 23:47:52] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -46.800948-0.000314j
[2025-09-18 23:48:04] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -46.802299+0.000712j
[2025-09-18 23:48:17] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -46.798874+0.000969j
[2025-09-18 23:48:29] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -46.799767+0.004349j
[2025-09-18 23:48:42] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -46.795009-0.001783j
[2025-09-18 23:48:54] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -46.810230-0.000469j
[2025-09-18 23:49:06] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -46.797211-0.001436j
[2025-09-18 23:49:19] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -46.794649+0.001508j
[2025-09-18 23:49:31] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -46.793841-0.001499j
[2025-09-18 23:49:44] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -46.781886+0.001138j
[2025-09-18 23:49:56] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -46.775084-0.001481j
[2025-09-18 23:50:09] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -46.804038-0.001338j
[2025-09-18 23:50:21] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -46.790010-0.000831j
[2025-09-18 23:50:34] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -46.798032-0.000624j
[2025-09-18 23:50:46] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -46.808427+0.001848j
[2025-09-18 23:50:58] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -46.802446-0.000709j
[2025-09-18 23:51:11] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -46.801503+0.002779j
[2025-09-18 23:51:23] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -46.808840-0.004080j
[2025-09-18 23:51:36] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -46.802671+0.002013j
[2025-09-18 23:51:48] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -46.792545-0.004067j
[2025-09-18 23:52:01] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -46.801950+0.000603j
[2025-09-18 23:52:13] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -46.803885+0.000058j
[2025-09-18 23:52:26] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -46.804552+0.002755j
[2025-09-18 23:52:38] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -46.799472+0.001915j
[2025-09-18 23:52:50] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -46.803657+0.001029j
[2025-09-18 23:53:03] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -46.805140+0.002227j
[2025-09-18 23:53:15] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -46.805741-0.001301j
[2025-09-18 23:53:28] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -46.815031+0.000595j
[2025-09-18 23:53:40] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -46.795916-0.001242j
[2025-09-18 23:53:53] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -46.797150-0.001822j
[2025-09-18 23:54:05] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -46.815641-0.001218j
[2025-09-18 23:54:17] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -46.796827+0.000191j
[2025-09-18 23:54:30] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -46.812313+0.001427j
[2025-09-18 23:54:42] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -46.801959+0.002953j
[2025-09-18 23:54:55] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -46.803498-0.001774j
[2025-09-18 23:55:07] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -46.804530+0.000346j
[2025-09-18 23:55:20] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -46.804440-0.000025j
[2025-09-18 23:55:32] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -46.795019+0.003526j
[2025-09-18 23:55:45] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -46.795578+0.000035j
[2025-09-18 23:55:57] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -46.801174-0.000725j
[2025-09-18 23:56:10] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -46.793308-0.001606j
[2025-09-18 23:56:22] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -46.802186-0.001924j
[2025-09-18 23:56:35] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -46.799160+0.001972j
[2025-09-18 23:56:48] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -46.799389+0.001563j
[2025-09-18 23:57:00] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -46.795631-0.001281j
[2025-09-18 23:57:13] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -46.805369-0.001046j
[2025-09-18 23:57:25] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -46.784635-0.001752j
[2025-09-18 23:57:38] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -46.805713-0.004356j
[2025-09-18 23:57:50] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -46.806896+0.000590j
[2025-09-18 23:58:03] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -46.810261-0.001349j
[2025-09-18 23:58:15] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -46.800410+0.003609j
[2025-09-18 23:58:28] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -46.799091-0.002115j
[2025-09-18 23:58:41] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -46.807508-0.002253j
[2025-09-18 23:58:53] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -46.803631+0.000803j
[2025-09-18 23:59:06] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -46.791685-0.003569j
[2025-09-18 23:59:18] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -46.796739+0.000206j
[2025-09-18 23:59:30] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -46.804061+0.000015j
[2025-09-18 23:59:43] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -46.800637-0.003655j
[2025-09-18 23:59:43] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-18 23:59:56] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -46.801440-0.000259j
[2025-09-19 00:00:08] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -46.806942+0.001303j
[2025-09-19 00:00:21] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -46.786547+0.000242j
[2025-09-19 00:00:33] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -46.799150-0.000194j
[2025-09-19 00:00:46] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -46.803072+0.001223j
[2025-09-19 00:00:58] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -46.799275+0.004484j
[2025-09-19 00:01:11] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -46.791437+0.001671j
[2025-09-19 00:01:23] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -46.812269-0.001948j
[2025-09-19 00:01:36] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -46.799442-0.000383j
[2025-09-19 00:01:48] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -46.804909+0.001436j
[2025-09-19 00:02:01] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -46.813816-0.000888j
[2025-09-19 00:02:13] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -46.804728-0.000334j
[2025-09-19 00:02:26] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -46.796581+0.000791j
[2025-09-19 00:02:39] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -46.801455+0.001522j
[2025-09-19 00:02:51] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -46.801605-0.002345j
[2025-09-19 00:03:04] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -46.779433+0.000823j
[2025-09-19 00:03:16] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -46.796732+0.000014j
[2025-09-19 00:03:29] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -46.799969+0.002006j
[2025-09-19 00:03:41] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -46.807973-0.001012j
[2025-09-19 00:03:54] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -46.802644-0.003223j
[2025-09-19 00:04:06] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -46.795196-0.005822j
[2025-09-19 00:04:19] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -46.809136-0.001160j
[2025-09-19 00:04:31] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -46.809372+0.001897j
[2025-09-19 00:04:44] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -46.803189+0.003193j
[2025-09-19 00:04:56] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -46.794682-0.001271j
[2025-09-19 00:05:09] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -46.803116+0.003613j
[2025-09-19 00:05:22] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -46.789641-0.002616j
[2025-09-19 00:05:34] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -46.803145+0.003833j
[2025-09-19 00:05:47] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -46.798122+0.001974j
[2025-09-19 00:05:59] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -46.802028+0.004656j
[2025-09-19 00:06:12] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -46.799348-0.000407j
[2025-09-19 00:06:24] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -46.800648-0.000890j
[2025-09-19 00:06:37] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -46.791330-0.000792j
[2025-09-19 00:06:49] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -46.805539+0.001811j
[2025-09-19 00:07:02] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -46.787427-0.000077j
[2025-09-19 00:07:15] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -46.804440-0.000357j
[2025-09-19 00:07:27] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -46.789963+0.003243j
[2025-09-19 00:07:40] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -46.798353-0.000381j
[2025-09-19 00:07:52] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -46.793637+0.003927j
[2025-09-19 00:08:05] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -46.791313+0.000974j
[2025-09-19 00:08:17] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -46.795417+0.001066j
[2025-09-19 00:08:30] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -46.816571+0.001032j
[2025-09-19 00:08:42] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -46.777300-0.000558j
[2025-09-19 00:08:55] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -46.798978+0.001921j
[2025-09-19 00:09:07] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -46.808901-0.002241j
[2025-09-19 00:09:20] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -46.794002-0.000277j
[2025-09-19 00:09:32] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -46.796704+0.000690j
[2025-09-19 00:09:45] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -46.793211-0.000477j
[2025-09-19 00:09:57] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -46.807520-0.001068j
[2025-09-19 00:10:10] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -46.794931+0.004003j
[2025-09-19 00:10:22] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -46.803413+0.000048j
[2025-09-19 00:10:35] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -46.794825+0.001853j
[2025-09-19 00:10:48] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -46.812693+0.002175j
[2025-09-19 00:11:00] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -46.793929-0.003375j
[2025-09-19 00:11:13] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -46.802359+0.002505j
[2025-09-19 00:11:25] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -46.803484+0.002070j
[2025-09-19 00:11:38] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -46.791893+0.000939j
[2025-09-19 00:11:50] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -46.805919-0.002779j
[2025-09-19 00:12:03] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -46.793189+0.000967j
[2025-09-19 00:12:15] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -46.793795-0.000678j
[2025-09-19 00:12:28] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -46.791538-0.000653j
[2025-09-19 00:12:40] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -46.796730+0.001605j
[2025-09-19 00:12:53] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -46.812315+0.000877j
[2025-09-19 00:13:05] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -46.806298-0.002267j
[2025-09-19 00:13:18] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -46.797858-0.000222j
[2025-09-19 00:13:30] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -46.798213-0.001494j
[2025-09-19 00:13:43] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -46.805281-0.001591j
[2025-09-19 00:13:55] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -46.803986+0.002447j
[2025-09-19 00:14:08] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -46.810064+0.002172j
[2025-09-19 00:14:20] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -46.796707-0.001522j
[2025-09-19 00:14:33] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -46.807575-0.001135j
[2025-09-19 00:14:45] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -46.793079-0.001984j
[2025-09-19 00:14:58] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -46.794867-0.000179j
[2025-09-19 00:15:10] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -46.787962-0.000541j
[2025-09-19 00:15:23] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -46.795939-0.000070j
[2025-09-19 00:15:36] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -46.803569+0.002556j
[2025-09-19 00:15:48] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -46.805202-0.002042j
[2025-09-19 00:16:01] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -46.807883+0.000684j
[2025-09-19 00:16:13] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -46.797319-0.003404j
[2025-09-19 00:16:26] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -46.808621+0.001489j
[2025-09-19 00:16:38] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -46.796121+0.000081j
[2025-09-19 00:16:51] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -46.803143-0.002555j
[2025-09-19 00:17:03] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -46.801438+0.002471j
[2025-09-19 00:17:16] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -46.799539-0.000728j
[2025-09-19 00:17:28] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -46.799233-0.000244j
[2025-09-19 00:17:41] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -46.784965+0.000982j
[2025-09-19 00:17:53] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -46.807827-0.001429j
[2025-09-19 00:18:06] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -46.816106-0.000117j
[2025-09-19 00:18:18] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -46.794007+0.003317j
[2025-09-19 00:18:31] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -46.803808+0.001366j
[2025-09-19 00:18:44] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -46.803377+0.002458j
[2025-09-19 00:18:56] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -46.788998-0.000413j
[2025-09-19 00:19:09] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -46.790784-0.002199j
[2025-09-19 00:19:22] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -46.807002-0.001621j
[2025-09-19 00:19:34] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -46.797792-0.000541j
[2025-09-19 00:19:47] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -46.793806+0.004730j
[2025-09-19 00:19:59] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -46.788387-0.003620j
[2025-09-19 00:20:12] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -46.808838+0.000439j
[2025-09-19 00:20:24] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -46.797375+0.000166j
[2025-09-19 00:20:37] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -46.812363-0.000303j
[2025-09-19 00:20:49] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -46.804618-0.000576j
[2025-09-19 00:21:02] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -46.803069-0.000125j
[2025-09-19 00:21:14] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -46.804007+0.003594j
[2025-09-19 00:21:27] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -46.814691-0.001638j
[2025-09-19 00:21:40] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -46.810493+0.000580j
[2025-09-19 00:21:40] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-19 00:21:52] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -46.800507+0.000449j
[2025-09-19 00:22:05] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -46.799359-0.001769j
[2025-09-19 00:22:17] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -46.799279+0.002451j
[2025-09-19 00:22:30] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -46.795562+0.000483j
[2025-09-19 00:22:42] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -46.798560-0.002506j
[2025-09-19 00:22:55] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -46.794696-0.002087j
[2025-09-19 00:23:07] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -46.805596+0.000158j
[2025-09-19 00:23:20] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -46.807742+0.000161j
[2025-09-19 00:23:33] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -46.799918-0.004126j
[2025-09-19 00:23:45] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -46.790359-0.000223j
[2025-09-19 00:23:58] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -46.808064-0.000220j
[2025-09-19 00:24:10] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -46.804325-0.000498j
[2025-09-19 00:24:23] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -46.790262+0.005379j
[2025-09-19 00:24:35] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -46.795048+0.000409j
[2025-09-19 00:24:48] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -46.797475+0.002236j
[2025-09-19 00:25:00] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -46.801749-0.002756j
[2025-09-19 00:25:13] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -46.797301-0.002473j
[2025-09-19 00:25:25] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -46.799932-0.000646j
[2025-09-19 00:25:38] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -46.800894+0.001266j
[2025-09-19 00:25:50] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -46.790975+0.000611j
[2025-09-19 00:26:03] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -46.803539+0.001664j
[2025-09-19 00:26:15] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -46.793237+0.000042j
[2025-09-19 00:26:28] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -46.807290+0.002328j
[2025-09-19 00:26:40] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -46.809472-0.002182j
[2025-09-19 00:26:53] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -46.790099+0.001082j
[2025-09-19 00:27:05] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -46.798415-0.002495j
[2025-09-19 00:27:17] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -46.797093-0.002832j
[2025-09-19 00:27:30] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -46.792622-0.001701j
[2025-09-19 00:27:42] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -46.794747+0.000151j
[2025-09-19 00:27:55] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -46.801101+0.002095j
[2025-09-19 00:28:07] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -46.794410-0.003138j
[2025-09-19 00:28:20] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -46.802827-0.003690j
[2025-09-19 00:28:32] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -46.785093-0.001727j
[2025-09-19 00:28:45] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -46.807922+0.000836j
[2025-09-19 00:28:57] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -46.789846+0.002478j
[2025-09-19 00:29:09] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -46.810595-0.003388j
[2025-09-19 00:29:22] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -46.804924+0.001213j
[2025-09-19 00:29:34] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -46.803504-0.003349j
[2025-09-19 00:29:47] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -46.807216-0.000842j
[2025-09-19 00:29:59] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -46.791456+0.002164j
[2025-09-19 00:30:12] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -46.801301-0.000709j
[2025-09-19 00:30:24] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -46.793636+0.003655j
[2025-09-19 00:30:36] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -46.804876+0.001108j
[2025-09-19 00:30:49] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -46.806499+0.000989j
[2025-09-19 00:31:01] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -46.798641+0.000823j
[2025-09-19 00:31:14] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -46.790586+0.003888j
[2025-09-19 00:31:26] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -46.799458-0.000837j
[2025-09-19 00:31:39] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -46.802939-0.001212j
[2025-09-19 00:31:51] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -46.786571+0.002255j
[2025-09-19 00:32:04] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -46.809578+0.000226j
[2025-09-19 00:32:16] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -46.801782+0.001925j
[2025-09-19 00:32:28] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -46.799199+0.000416j
[2025-09-19 00:32:41] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -46.798830+0.000672j
[2025-09-19 00:32:53] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -46.803424-0.001953j
[2025-09-19 00:33:06] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -46.787357-0.004477j
[2025-09-19 00:33:18] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -46.806141-0.003399j
[2025-09-19 00:33:31] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -46.786561-0.001383j
[2025-09-19 00:33:43] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -46.803873-0.000358j
[2025-09-19 00:33:55] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -46.810002+0.000911j
[2025-09-19 00:34:08] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -46.799475-0.003134j
[2025-09-19 00:34:20] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -46.798613-0.002158j
[2025-09-19 00:34:33] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -46.803898-0.000270j
[2025-09-19 00:34:45] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -46.797777+0.000204j
[2025-09-19 00:34:58] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -46.801627+0.003149j
[2025-09-19 00:35:10] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -46.792072-0.001483j
[2025-09-19 00:35:22] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -46.799668+0.000135j
[2025-09-19 00:35:35] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -46.797126+0.000560j
[2025-09-19 00:35:47] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -46.808200+0.000936j
[2025-09-19 00:36:00] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -46.797328-0.003095j
[2025-09-19 00:36:12] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -46.806305-0.000707j
[2025-09-19 00:36:25] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -46.822168+0.000722j
[2025-09-19 00:36:37] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -46.799491-0.002750j
[2025-09-19 00:36:50] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -46.800144-0.000966j
[2025-09-19 00:37:02] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -46.794584-0.000884j
[2025-09-19 00:37:15] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -46.790939-0.001525j
[2025-09-19 00:37:27] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -46.803362-0.001737j
[2025-09-19 00:37:40] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -46.804858+0.000384j
[2025-09-19 00:37:52] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -46.804402+0.001000j
[2025-09-19 00:38:05] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -46.794740+0.000840j
[2025-09-19 00:38:17] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -46.811180-0.003698j
[2025-09-19 00:38:30] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -46.786989-0.000171j
[2025-09-19 00:38:42] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -46.800043-0.000989j
[2025-09-19 00:38:55] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -46.802560+0.000086j
[2025-09-19 00:39:07] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -46.794270+0.002930j
[2025-09-19 00:39:20] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -46.801009-0.001910j
[2025-09-19 00:39:32] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -46.799491-0.002458j
[2025-09-19 00:39:45] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -46.800989+0.001685j
[2025-09-19 00:39:57] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -46.808740+0.000642j
[2025-09-19 00:40:10] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -46.802673-0.001349j
[2025-09-19 00:40:22] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -46.793251-0.000307j
[2025-09-19 00:40:35] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -46.799606-0.005534j
[2025-09-19 00:40:48] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -46.798140+0.002134j
[2025-09-19 00:41:00] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -46.802547+0.000481j
[2025-09-19 00:41:13] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -46.799164+0.003187j
[2025-09-19 00:41:25] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -46.800198+0.001163j
[2025-09-19 00:41:38] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -46.809714+0.001165j
[2025-09-19 00:41:50] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -46.791573+0.002455j
[2025-09-19 00:42:03] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -46.800969+0.000339j
[2025-09-19 00:42:15] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -46.794193-0.000655j
[2025-09-19 00:42:28] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -46.784310-0.000042j
[2025-09-19 00:42:40] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -46.807114-0.000480j
[2025-09-19 00:42:53] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -46.811600+0.001042j
[2025-09-19 00:43:06] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -46.809454-0.000041j
[2025-09-19 00:43:18] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -46.806009-0.002233j
[2025-09-19 00:43:31] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -46.787086+0.002042j
[2025-09-19 00:43:31] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-19 00:43:43] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -46.794945+0.000621j
[2025-09-19 00:43:56] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -46.803556+0.002702j
[2025-09-19 00:44:08] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -46.795439-0.001721j
[2025-09-19 00:44:21] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -46.805317-0.000211j
[2025-09-19 00:44:33] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -46.800265-0.003158j
[2025-09-19 00:44:46] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -46.795517+0.001151j
[2025-09-19 00:44:58] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -46.802313+0.002003j
[2025-09-19 00:45:11] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -46.805243-0.002164j
[2025-09-19 00:45:23] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -46.789750-0.003762j
[2025-09-19 00:45:36] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -46.798151+0.000151j
[2025-09-19 00:45:48] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -46.803250-0.002108j
[2025-09-19 00:46:01] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -46.804960+0.000531j
[2025-09-19 00:46:13] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -46.780232+0.000561j
[2025-09-19 00:46:25] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -46.795071-0.000074j
[2025-09-19 00:46:38] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -46.798622+0.000457j
[2025-09-19 00:46:50] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -46.807643-0.002103j
[2025-09-19 00:47:03] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -46.806481-0.004486j
[2025-09-19 00:47:15] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -46.805176-0.002385j
[2025-09-19 00:47:28] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -46.801885+0.001263j
[2025-09-19 00:47:40] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -46.799633-0.002197j
[2025-09-19 00:47:53] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -46.797581-0.001016j
[2025-09-19 00:48:05] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -46.798821-0.000577j
[2025-09-19 00:48:17] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -46.800247+0.000526j
[2025-09-19 00:48:30] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -46.787787-0.000041j
[2025-09-19 00:48:42] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -46.793527+0.002076j
[2025-09-19 00:48:55] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -46.796664-0.003065j
[2025-09-19 00:49:07] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -46.785334+0.001420j
[2025-09-19 00:49:19] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -46.791279+0.003033j
[2025-09-19 00:49:32] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -46.789898-0.000518j
[2025-09-19 00:49:44] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -46.802321-0.000983j
[2025-09-19 00:49:57] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -46.808296+0.001659j
[2025-09-19 00:50:09] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -46.811990-0.000146j
[2025-09-19 00:50:22] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -46.793921+0.004192j
[2025-09-19 00:50:34] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -46.806367-0.004639j
[2025-09-19 00:50:46] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -46.797887+0.000285j
[2025-09-19 00:50:59] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -46.802241-0.000540j
[2025-09-19 00:51:11] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -46.801830+0.000472j
[2025-09-19 00:51:24] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -46.802817+0.002100j
[2025-09-19 00:51:36] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -46.799050+0.000917j
[2025-09-19 00:51:49] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -46.798977+0.000047j
[2025-09-19 00:52:01] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -46.796245+0.001470j
[2025-09-19 00:52:13] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -46.796987-0.001885j
[2025-09-19 00:52:26] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -46.798285-0.004001j
[2025-09-19 00:52:38] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -46.802473+0.004804j
[2025-09-19 00:52:51] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -46.777767-0.000126j
[2025-09-19 00:53:03] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -46.797607+0.002304j
[2025-09-19 00:53:16] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -46.799844+0.003254j
[2025-09-19 00:53:28] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -46.799444+0.000141j
[2025-09-19 00:53:41] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -46.800961+0.000427j
[2025-09-19 00:53:53] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -46.797141-0.000167j
[2025-09-19 00:54:06] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -46.786960-0.000458j
[2025-09-19 00:54:18] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -46.797111+0.002176j
[2025-09-19 00:54:31] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -46.799580-0.000134j
[2025-09-19 00:54:43] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -46.810933+0.002758j
[2025-09-19 00:54:55] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -46.809421-0.002771j
[2025-09-19 00:55:08] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -46.794577-0.000121j
[2025-09-19 00:55:20] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -46.787141-0.001076j
[2025-09-19 00:55:33] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -46.791106-0.002034j
[2025-09-19 00:55:46] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -46.807143-0.000543j
[2025-09-19 00:55:58] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -46.808252+0.000023j
[2025-09-19 00:56:11] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -46.806315+0.003836j
[2025-09-19 00:56:23] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -46.800576+0.000922j
[2025-09-19 00:56:36] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -46.804453+0.000344j
[2025-09-19 00:56:48] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -46.800558+0.002028j
[2025-09-19 00:57:01] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -46.799310-0.000079j
[2025-09-19 00:57:13] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -46.790801-0.001749j
[2025-09-19 00:57:25] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -46.795137+0.000469j
[2025-09-19 00:57:38] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -46.810552-0.000575j
[2025-09-19 00:57:50] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -46.790378+0.000002j
[2025-09-19 00:58:03] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -46.786833-0.001539j
[2025-09-19 00:58:15] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -46.813853+0.001536j
[2025-09-19 00:58:28] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -46.806384+0.000839j
[2025-09-19 00:58:40] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -46.807366-0.000134j
[2025-09-19 00:58:53] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -46.796162-0.003922j
[2025-09-19 00:59:05] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -46.798730+0.003167j
[2025-09-19 00:59:17] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -46.791232-0.001005j
[2025-09-19 00:59:30] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -46.806271+0.000655j
[2025-09-19 00:59:42] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -46.808070+0.001196j
[2025-09-19 00:59:55] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -46.805527+0.000943j
[2025-09-19 01:00:07] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -46.797377-0.002507j
[2025-09-19 01:00:20] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -46.806653-0.000608j
[2025-09-19 01:00:32] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -46.808751+0.002491j
[2025-09-19 01:00:44] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -46.802193-0.001378j
[2025-09-19 01:00:57] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -46.790878+0.000546j
[2025-09-19 01:01:09] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -46.799462-0.002124j
[2025-09-19 01:01:22] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -46.799712-0.001955j
[2025-09-19 01:01:34] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -46.800628+0.000187j
[2025-09-19 01:01:47] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -46.805408+0.001399j
[2025-09-19 01:01:59] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -46.803339-0.000111j
[2025-09-19 01:02:12] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -46.797946-0.001254j
[2025-09-19 01:02:24] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -46.803932-0.000350j
[2025-09-19 01:02:37] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -46.798466+0.001974j
[2025-09-19 01:02:49] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -46.810139+0.000927j
[2025-09-19 01:03:01] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -46.810026-0.000104j
[2025-09-19 01:03:14] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -46.797590+0.001165j
[2025-09-19 01:03:26] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -46.806720+0.002480j
[2025-09-19 01:03:39] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -46.800933+0.001413j
[2025-09-19 01:03:51] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -46.800842-0.004329j
[2025-09-19 01:04:04] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -46.794473+0.001895j
[2025-09-19 01:04:16] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -46.798301+0.003546j
[2025-09-19 01:04:28] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -46.812451-0.000562j
[2025-09-19 01:04:41] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -46.804884+0.000962j
[2025-09-19 01:04:54] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -46.814014-0.000506j
[2025-09-19 01:05:06] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -46.799245+0.004152j
[2025-09-19 01:05:19] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -46.795794+0.000064j
[2025-09-19 01:05:19] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-19 01:05:19] ✅ Training completed | Restarts: 2
[2025-09-19 01:05:19] ============================================================
[2025-09-19 01:05:19] Training completed | Runtime: 13168.8s
[2025-09-19 01:05:22] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-19 01:05:22] ============================================================
