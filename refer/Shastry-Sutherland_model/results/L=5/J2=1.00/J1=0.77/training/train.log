[2025-09-18 21:25:33] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.78/training/checkpoints/final_GCNN.pkl
[2025-09-18 21:25:33]   - 迭代次数: final
[2025-09-18 21:25:33]   - 能量: -43.581312+0.000932j ± 0.008558
[2025-09-18 21:25:33]   - 时间戳: 2025-09-18T21:24:56.578847+08:00
[2025-09-18 21:25:53] ✓ 变分状态参数已从checkpoint恢复
[2025-09-18 21:25:53] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-18 21:25:53] ==================================================
[2025-09-18 21:25:54] GCNN for Shastry-Sutherland Model
[2025-09-18 21:25:54] ==================================================
[2025-09-18 21:25:54] System parameters:
[2025-09-18 21:25:54]   - System size: L=5, N=100
[2025-09-18 21:25:54]   - System parameters: J1=0.77, J2=1.0, Q=0.0
[2025-09-18 21:25:54] --------------------------------------------------
[2025-09-18 21:25:54] Model parameters:
[2025-09-18 21:25:54]   - Number of layers = 4
[2025-09-18 21:25:54]   - Number of features = 4
[2025-09-18 21:25:54]   - Total parameters = 19628
[2025-09-18 21:25:54] --------------------------------------------------
[2025-09-18 21:25:54] Training parameters:
[2025-09-18 21:25:54]   - Learning rate: 0.015
[2025-09-18 21:25:54]   - Total iterations: 1050
[2025-09-18 21:25:54]   - Annealing cycles: 3
[2025-09-18 21:25:54]   - Initial period: 150
[2025-09-18 21:25:54]   - Period multiplier: 2.0
[2025-09-18 21:25:54]   - Temperature range: 0.0-1.0
[2025-09-18 21:25:54]   - Samples: 4096
[2025-09-18 21:25:54]   - Discarded samples: 0
[2025-09-18 21:25:54]   - Chunk size: 2048
[2025-09-18 21:25:54]   - Diagonal shift: 0.2
[2025-09-18 21:25:54]   - Gradient clipping: 1.0
[2025-09-18 21:25:54]   - Checkpoint enabled: interval=105
[2025-09-18 21:25:54]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.77/training/checkpoints
[2025-09-18 21:25:54] --------------------------------------------------
[2025-09-18 21:25:54] Device status:
[2025-09-18 21:25:54]   - Devices model: NVIDIA H200 NVL
[2025-09-18 21:25:54]   - Number of devices: 1
[2025-09-18 21:25:54]   - Sharding: True
[2025-09-18 21:25:54] ============================================================
[2025-09-18 21:26:51] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -42.945039-0.000264j
[2025-09-18 21:27:30] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -42.954791+0.004686j
[2025-09-18 21:27:42] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -42.973363+0.002488j
[2025-09-18 21:27:54] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -42.974734+0.004056j
[2025-09-18 21:28:07] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -42.960782-0.000095j
[2025-09-18 21:28:19] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -42.970480+0.000381j
[2025-09-18 21:28:32] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -42.961719+0.000498j
[2025-09-18 21:28:44] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -42.957335+0.004363j
[2025-09-18 21:28:57] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -42.954356+0.000055j
[2025-09-18 21:29:10] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -42.954954+0.001057j
[2025-09-18 21:29:22] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -42.947971-0.003286j
[2025-09-18 21:29:34] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -42.962580-0.000058j
[2025-09-18 21:29:47] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -42.973108-0.001985j
[2025-09-18 21:29:59] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -42.965240+0.003859j
[2025-09-18 21:30:12] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -42.949395+0.000323j
[2025-09-18 21:30:24] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -42.958805+0.002328j
[2025-09-18 21:30:37] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -42.969631+0.000656j
[2025-09-18 21:30:49] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -42.966568-0.000887j
[2025-09-18 21:31:02] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -42.957423-0.002248j
[2025-09-18 21:31:14] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -42.964319-0.001687j
[2025-09-18 21:31:27] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -42.963039+0.000770j
[2025-09-18 21:31:39] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -42.960742-0.001514j
[2025-09-18 21:31:52] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -42.970719-0.001377j
[2025-09-18 21:32:04] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -42.951682-0.002433j
[2025-09-18 21:32:17] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -42.971828-0.000761j
[2025-09-18 21:32:29] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -42.973588-0.000816j
[2025-09-18 21:32:42] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -42.960188+0.000013j
[2025-09-18 21:32:54] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -42.961461+0.007416j
[2025-09-18 21:33:07] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -42.967246-0.003263j
[2025-09-18 21:33:19] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -42.947355+0.001012j
[2025-09-18 21:33:32] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -42.953229-0.004251j
[2025-09-18 21:33:44] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -42.945708+0.006711j
[2025-09-18 21:33:56] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -42.967359+0.001215j
[2025-09-18 21:34:09] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -42.974132-0.001688j
[2025-09-18 21:34:21] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -42.957508+0.000399j
[2025-09-18 21:34:33] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -42.957201+0.001425j
[2025-09-18 21:34:46] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -42.971671+0.002000j
[2025-09-18 21:34:58] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -42.967738-0.000588j
[2025-09-18 21:35:11] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -42.968752-0.000857j
[2025-09-18 21:35:23] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -42.951513-0.000906j
[2025-09-18 21:35:36] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -42.967890-0.000594j
[2025-09-18 21:35:48] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -42.957140-0.000924j
[2025-09-18 21:36:01] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -42.976415+0.002401j
[2025-09-18 21:36:13] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -42.968569-0.000693j
[2025-09-18 21:36:26] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -42.968066+0.001171j
[2025-09-18 21:36:38] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -42.953669+0.000413j
[2025-09-18 21:36:50] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -42.977871-0.001731j
[2025-09-18 21:37:03] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -42.969934-0.000707j
[2025-09-18 21:37:15] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -42.968330+0.001643j
[2025-09-18 21:37:28] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -42.972568+0.004462j
[2025-09-18 21:37:40] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -42.954325+0.000117j
[2025-09-18 21:37:53] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -42.958456+0.000995j
[2025-09-18 21:38:05] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -42.967912-0.000850j
[2025-09-18 21:38:18] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -42.962613+0.001106j
[2025-09-18 21:38:30] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -42.947778-0.001022j
[2025-09-18 21:38:43] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -42.952676+0.002801j
[2025-09-18 21:38:55] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -42.961179+0.002544j
[2025-09-18 21:39:07] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -42.980253+0.006796j
[2025-09-18 21:39:20] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -42.964257+0.002342j
[2025-09-18 21:39:32] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -42.952837-0.001268j
[2025-09-18 21:39:45] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -42.951013+0.000647j
[2025-09-18 21:39:57] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -42.949483-0.001002j
[2025-09-18 21:40:10] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -42.951517-0.001313j
[2025-09-18 21:40:22] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -42.962279-0.000269j
[2025-09-18 21:40:34] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -42.969690+0.001466j
[2025-09-18 21:40:47] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -42.965142-0.002728j
[2025-09-18 21:40:59] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -42.964148+0.001313j
[2025-09-18 21:41:12] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -42.967007-0.003713j
[2025-09-18 21:41:24] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -42.954768+0.000927j
[2025-09-18 21:41:37] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -42.962002+0.001804j
[2025-09-18 21:41:49] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -42.962072+0.003368j
[2025-09-18 21:42:02] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -42.964361+0.001866j
[2025-09-18 21:42:14] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -42.977662+0.000498j
[2025-09-18 21:42:27] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -42.941968-0.003489j
[2025-09-18 21:42:39] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -42.960941-0.001765j
[2025-09-18 21:42:52] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -42.953967+0.005295j
[2025-09-18 21:43:04] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -42.947738-0.000447j
[2025-09-18 21:43:16] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -42.975956-0.000524j
[2025-09-18 21:43:29] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -42.966523+0.001482j
[2025-09-18 21:43:41] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -42.951600+0.001496j
[2025-09-18 21:43:54] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -42.946831-0.001669j
[2025-09-18 21:44:06] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -42.966259-0.001557j
[2025-09-18 21:44:19] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -42.962809-0.001617j
[2025-09-18 21:44:31] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -42.963324-0.004164j
[2025-09-18 21:44:44] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -42.962989+0.000705j
[2025-09-18 21:44:56] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -42.961711-0.002445j
[2025-09-18 21:45:09] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -42.956180+0.002491j
[2025-09-18 21:45:21] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -42.959910-0.001491j
[2025-09-18 21:45:33] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -42.949456+0.003302j
[2025-09-18 21:45:46] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -42.962983+0.000458j
[2025-09-18 21:45:58] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -42.961775-0.000454j
[2025-09-18 21:46:11] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -42.977432-0.000941j
[2025-09-18 21:46:23] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -42.968778+0.001978j
[2025-09-18 21:46:36] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -42.944804-0.001068j
[2025-09-18 21:46:48] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -42.957360-0.001002j
[2025-09-18 21:47:01] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -42.964880+0.000113j
[2025-09-18 21:47:13] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -42.967130-0.000525j
[2025-09-18 21:47:26] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -42.956533-0.000423j
[2025-09-18 21:47:38] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -42.963203+0.001600j
[2025-09-18 21:47:50] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -42.962820+0.001504j
[2025-09-18 21:48:03] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -42.966716-0.000132j
[2025-09-18 21:48:15] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -42.959617-0.000257j
[2025-09-18 21:48:28] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -42.957114+0.000486j
[2025-09-18 21:48:40] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -42.975688-0.006021j
[2025-09-18 21:48:53] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -42.961133+0.003433j
[2025-09-18 21:48:53] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-18 21:49:05] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -42.964289+0.001919j
[2025-09-18 21:49:18] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -42.948979+0.001998j
[2025-09-18 21:49:30] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -42.965374-0.000937j
[2025-09-18 21:49:43] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -42.947046+0.001190j
[2025-09-18 21:49:55] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -42.953544-0.002502j
[2025-09-18 21:50:08] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -42.970064+0.000822j
[2025-09-18 21:50:20] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -42.954722+0.004786j
[2025-09-18 21:50:33] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -42.962654+0.004716j
[2025-09-18 21:50:45] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -42.970445+0.002339j
[2025-09-18 21:50:58] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -42.953002-0.000824j
[2025-09-18 21:51:10] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -42.955821-0.002298j
[2025-09-18 21:51:23] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -42.965487-0.001105j
[2025-09-18 21:51:35] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -42.961434-0.003423j
[2025-09-18 21:51:48] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -42.946679-0.000626j
[2025-09-18 21:52:00] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -42.978149+0.001940j
[2025-09-18 21:52:13] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -42.962941-0.002020j
[2025-09-18 21:52:25] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -42.971526-0.000908j
[2025-09-18 21:52:37] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -42.970068-0.004592j
[2025-09-18 21:52:49] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -42.971738+0.000192j
[2025-09-18 21:53:02] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -42.954474-0.000629j
[2025-09-18 21:53:14] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -42.960825+0.004972j
[2025-09-18 21:53:27] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -42.982554+0.001057j
[2025-09-18 21:53:39] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -42.973103-0.003153j
[2025-09-18 21:53:52] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -42.955070+0.000716j
[2025-09-18 21:54:04] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -42.957015-0.003099j
[2025-09-18 21:54:16] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -42.957919-0.000364j
[2025-09-18 21:54:29] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -42.951363-0.000085j
[2025-09-18 21:54:41] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -42.960306-0.001501j
[2025-09-18 21:54:54] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -42.960078-0.000411j
[2025-09-18 21:55:06] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -42.958173-0.003334j
[2025-09-18 21:55:19] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -42.975545+0.004163j
[2025-09-18 21:55:31] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -42.971630-0.002707j
[2025-09-18 21:55:44] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -42.962755+0.002274j
[2025-09-18 21:55:56] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -42.958802-0.000987j
[2025-09-18 21:56:09] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -42.954904+0.000518j
[2025-09-18 21:56:21] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -42.957007-0.002768j
[2025-09-18 21:56:34] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -42.961032+0.000269j
[2025-09-18 21:56:46] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -42.968759-0.000841j
[2025-09-18 21:56:58] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -42.948736+0.002643j
[2025-09-18 21:57:11] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -42.965088+0.000919j
[2025-09-18 21:57:23] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -42.955739+0.001356j
[2025-09-18 21:57:36] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -42.959425+0.001532j
[2025-09-18 21:57:48] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -42.959889-0.003771j
[2025-09-18 21:58:01] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -42.966121+0.001775j
[2025-09-18 21:58:13] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -42.951378-0.002761j
[2025-09-18 21:58:13] RESTART #1 | Period: 300
[2025-09-18 21:58:26] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -42.958033+0.000226j
[2025-09-18 21:58:38] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -42.956964-0.000108j
[2025-09-18 21:58:50] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -42.965884+0.003402j
[2025-09-18 21:59:03] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -42.951055-0.003505j
[2025-09-18 21:59:15] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -42.963258+0.003033j
[2025-09-18 21:59:28] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -42.958970-0.004131j
[2025-09-18 21:59:40] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -42.965833-0.000716j
[2025-09-18 21:59:53] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -42.974750-0.001630j
[2025-09-18 22:00:05] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -42.961291+0.006583j
[2025-09-18 22:00:18] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -42.968079+0.000169j
[2025-09-18 22:00:30] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -42.940746-0.003382j
[2025-09-18 22:00:43] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -42.962984+0.004090j
[2025-09-18 22:00:55] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -42.968965-0.001326j
[2025-09-18 22:01:08] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -42.932703+0.001423j
[2025-09-18 22:01:20] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -42.963705+0.000405j
[2025-09-18 22:01:32] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -42.962712-0.000017j
[2025-09-18 22:01:45] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -42.955854-0.000284j
[2025-09-18 22:01:57] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -42.975740-0.001248j
[2025-09-18 22:02:10] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -42.961064+0.000859j
[2025-09-18 22:02:22] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -42.945832-0.000750j
[2025-09-18 22:02:35] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -42.975798-0.000741j
[2025-09-18 22:02:47] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -42.970395+0.002352j
[2025-09-18 22:03:00] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -42.972427+0.000426j
[2025-09-18 22:03:12] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -42.951772-0.000439j
[2025-09-18 22:03:25] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -42.959297+0.002177j
[2025-09-18 22:03:37] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -42.962526+0.001482j
[2025-09-18 22:03:49] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -42.954189-0.000681j
[2025-09-18 22:04:02] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -42.959615-0.003382j
[2025-09-18 22:04:14] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -42.974260+0.001433j
[2025-09-18 22:04:27] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -42.951827+0.000538j
[2025-09-18 22:04:39] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -42.951305-0.001655j
[2025-09-18 22:04:52] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -42.962774+0.002237j
[2025-09-18 22:05:04] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -42.954286+0.001679j
[2025-09-18 22:05:17] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -42.960785-0.000471j
[2025-09-18 22:05:29] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -42.956424+0.000687j
[2025-09-18 22:05:42] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -42.962287+0.000600j
[2025-09-18 22:05:54] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -42.964940+0.002273j
[2025-09-18 22:06:07] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -42.957930-0.000136j
[2025-09-18 22:06:19] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -42.960192-0.000339j
[2025-09-18 22:06:32] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -42.976448+0.000500j
[2025-09-18 22:06:44] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -42.967581-0.001465j
[2025-09-18 22:06:57] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -42.978870+0.000684j
[2025-09-18 22:07:09] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -42.955782-0.004092j
[2025-09-18 22:07:22] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -42.968220-0.000772j
[2025-09-18 22:07:34] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -42.949783-0.001229j
[2025-09-18 22:07:47] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -42.957186+0.001747j
[2025-09-18 22:07:59] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -42.965622-0.000494j
[2025-09-18 22:08:11] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -42.960492+0.001566j
[2025-09-18 22:08:24] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -42.968050-0.002982j
[2025-09-18 22:08:36] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -42.955656-0.000059j
[2025-09-18 22:08:49] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -42.950703-0.001042j
[2025-09-18 22:09:01] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -42.966683+0.000777j
[2025-09-18 22:09:14] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -42.964175+0.001966j
[2025-09-18 22:09:26] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -42.964334-0.000849j
[2025-09-18 22:09:39] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -42.965692+0.000063j
[2025-09-18 22:09:51] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -42.951679+0.000271j
[2025-09-18 22:10:04] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -42.952073+0.003258j
[2025-09-18 22:10:16] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -42.966159+0.001327j
[2025-09-18 22:10:28] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -42.949975+0.002828j
[2025-09-18 22:10:41] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -42.955725-0.001913j
[2025-09-18 22:10:41] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-18 22:10:53] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -42.955292+0.001828j
[2025-09-18 22:11:06] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -42.965491-0.001522j
[2025-09-18 22:11:18] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -42.977444-0.000899j
[2025-09-18 22:11:31] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -42.961777+0.002835j
[2025-09-18 22:11:43] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -42.975802-0.003805j
[2025-09-18 22:11:56] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -42.949293-0.002792j
[2025-09-18 22:12:08] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -42.965558+0.001537j
[2025-09-18 22:12:20] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -42.963324+0.000195j
[2025-09-18 22:12:33] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -42.977861-0.004125j
[2025-09-18 22:12:45] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -42.960616+0.001881j
[2025-09-18 22:12:58] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -42.959649-0.004050j
[2025-09-18 22:13:10] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -42.963402+0.002762j
[2025-09-18 22:13:23] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -42.962690-0.002228j
[2025-09-18 22:13:35] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -42.964927+0.000121j
[2025-09-18 22:13:48] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -42.946844+0.001119j
[2025-09-18 22:14:00] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -42.960902+0.001571j
[2025-09-18 22:14:13] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -42.966661+0.001727j
[2025-09-18 22:14:25] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -42.973644-0.000317j
[2025-09-18 22:14:37] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -42.951622+0.001928j
[2025-09-18 22:14:50] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -42.954548+0.005273j
[2025-09-18 22:15:02] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -42.947418-0.001539j
[2025-09-18 22:15:15] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -42.963309+0.000933j
[2025-09-18 22:15:27] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -42.956704-0.002418j
[2025-09-18 22:15:40] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -42.957768-0.000902j
[2025-09-18 22:15:52] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -42.949566-0.000290j
[2025-09-18 22:16:05] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -42.964311+0.001445j
[2025-09-18 22:16:17] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -42.977608-0.000790j
[2025-09-18 22:16:29] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -42.959058-0.000900j
[2025-09-18 22:16:42] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -42.958588-0.000021j
[2025-09-18 22:16:54] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -42.964758+0.001067j
[2025-09-18 22:17:07] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -42.944875-0.000166j
[2025-09-18 22:17:19] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -42.966048+0.000092j
[2025-09-18 22:17:32] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -42.963838-0.001464j
[2025-09-18 22:17:44] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -42.969898+0.000720j
[2025-09-18 22:17:56] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -42.946954-0.001119j
[2025-09-18 22:18:09] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -42.965677-0.001789j
[2025-09-18 22:18:21] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -42.975746+0.002059j
[2025-09-18 22:18:34] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -42.950182-0.000371j
[2025-09-18 22:18:46] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -42.968831-0.000071j
[2025-09-18 22:18:58] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -42.955793-0.001596j
[2025-09-18 22:19:11] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -42.959592-0.004276j
[2025-09-18 22:19:23] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -42.962116-0.004297j
[2025-09-18 22:19:36] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -42.961023+0.002000j
[2025-09-18 22:19:48] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -42.959543+0.002022j
[2025-09-18 22:20:01] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -42.954993+0.003944j
[2025-09-18 22:20:13] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -42.969262+0.002920j
[2025-09-18 22:20:26] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -42.964867+0.001839j
[2025-09-18 22:20:38] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -42.954019+0.003026j
[2025-09-18 22:20:50] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -42.970195+0.002085j
[2025-09-18 22:21:03] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -42.947094-0.005955j
[2025-09-18 22:21:15] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -42.974628+0.000780j
[2025-09-18 22:21:28] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -42.956669+0.000555j
[2025-09-18 22:21:40] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -42.977063+0.001748j
[2025-09-18 22:21:53] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -42.963625+0.000027j
[2025-09-18 22:22:05] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -42.968492-0.002066j
[2025-09-18 22:22:17] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -42.974717+0.000429j
[2025-09-18 22:22:30] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -42.953864-0.004540j
[2025-09-18 22:22:42] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -42.957947-0.003096j
[2025-09-18 22:22:55] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -42.976175+0.000640j
[2025-09-18 22:23:07] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -42.952817-0.001534j
[2025-09-18 22:23:20] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -42.962945+0.002240j
[2025-09-18 22:23:32] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -42.979700+0.002895j
[2025-09-18 22:23:45] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -42.964101-0.001100j
[2025-09-18 22:23:57] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -42.960794-0.000722j
[2025-09-18 22:24:09] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -42.967608+0.002370j
[2025-09-18 22:24:22] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -42.944759+0.003499j
[2025-09-18 22:24:34] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -42.967167-0.000454j
[2025-09-18 22:24:47] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -42.955572-0.002130j
[2025-09-18 22:24:59] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -42.950374-0.000103j
[2025-09-18 22:25:12] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -42.948215+0.003862j
[2025-09-18 22:25:24] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -42.958564-0.003145j
[2025-09-18 22:25:36] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -42.976374+0.001824j
[2025-09-18 22:25:49] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -42.966694+0.000714j
[2025-09-18 22:26:01] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -42.962032+0.000185j
[2025-09-18 22:26:14] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -42.961499-0.005849j
[2025-09-18 22:26:26] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -42.949024+0.000996j
[2025-09-18 22:26:39] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -42.946702+0.003571j
[2025-09-18 22:26:51] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -42.974707-0.001638j
[2025-09-18 22:27:04] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -42.953058+0.002113j
[2025-09-18 22:27:16] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -42.956931+0.003741j
[2025-09-18 22:27:28] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -42.958965+0.002901j
[2025-09-18 22:27:41] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -42.968609-0.000355j
[2025-09-18 22:27:53] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -42.965285-0.000622j
[2025-09-18 22:28:06] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -42.961394+0.001198j
[2025-09-18 22:28:18] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -42.953815+0.000289j
[2025-09-18 22:28:31] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -42.973872+0.002729j
[2025-09-18 22:28:43] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -42.955207+0.001778j
[2025-09-18 22:28:56] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -42.974090+0.000890j
[2025-09-18 22:29:08] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -42.966955-0.000021j
[2025-09-18 22:29:20] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -42.970547+0.000555j
[2025-09-18 22:29:33] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -42.963392-0.000358j
[2025-09-18 22:29:45] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -42.959133+0.000147j
[2025-09-18 22:29:58] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -42.966364-0.005536j
[2025-09-18 22:30:10] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -42.956921+0.001265j
[2025-09-18 22:30:23] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -42.972069+0.002310j
[2025-09-18 22:30:35] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -42.948383+0.001493j
[2025-09-18 22:30:48] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -42.961676-0.000996j
[2025-09-18 22:31:00] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -42.960928+0.002628j
[2025-09-18 22:31:12] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -42.948559-0.001277j
[2025-09-18 22:31:25] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -42.959393+0.001452j
[2025-09-18 22:31:37] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -42.956877-0.004328j
[2025-09-18 22:31:50] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -42.960386-0.000176j
[2025-09-18 22:32:02] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -42.946030-0.003048j
[2025-09-18 22:32:14] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -42.968915-0.001207j
[2025-09-18 22:32:26] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -42.959244-0.002274j
[2025-09-18 22:32:27] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-18 22:32:39] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -42.960645-0.000685j
[2025-09-18 22:32:51] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -42.961822-0.001850j
[2025-09-18 22:33:04] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -42.952783+0.000949j
[2025-09-18 22:33:16] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -42.955656-0.000157j
[2025-09-18 22:33:29] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -42.958629-0.001375j
[2025-09-18 22:33:41] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -42.971012+0.002763j
[2025-09-18 22:33:54] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -42.967090-0.001876j
[2025-09-18 22:34:06] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -42.961613+0.002471j
[2025-09-18 22:34:18] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -42.956116+0.001603j
[2025-09-18 22:34:31] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -42.980636+0.002946j
[2025-09-18 22:34:43] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -42.972998+0.000812j
[2025-09-18 22:34:56] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -42.965074+0.001885j
[2025-09-18 22:35:08] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -42.957995+0.000685j
[2025-09-18 22:35:21] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -42.964602+0.001898j
[2025-09-18 22:35:33] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -42.972328-0.000568j
[2025-09-18 22:35:45] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -42.966468-0.001556j
[2025-09-18 22:35:58] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -42.956920-0.001120j
[2025-09-18 22:36:10] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -42.966815-0.000669j
[2025-09-18 22:36:23] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -42.944624-0.000089j
[2025-09-18 22:36:35] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -42.967264+0.000851j
[2025-09-18 22:36:48] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -42.948084+0.001519j
[2025-09-18 22:37:00] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -42.963421-0.001932j
[2025-09-18 22:37:13] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -42.970799-0.002534j
[2025-09-18 22:37:25] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -42.964264-0.002959j
[2025-09-18 22:37:38] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -42.957238+0.000283j
[2025-09-18 22:37:50] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -42.961224+0.004309j
[2025-09-18 22:38:02] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -42.956920+0.002745j
[2025-09-18 22:38:15] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -42.955008-0.001642j
[2025-09-18 22:38:27] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -42.963291+0.001271j
[2025-09-18 22:38:40] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -42.962552-0.001198j
[2025-09-18 22:38:52] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -42.968818-0.003664j
[2025-09-18 22:39:05] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -42.968370+0.000044j
[2025-09-18 22:39:17] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -42.965183-0.001583j
[2025-09-18 22:39:30] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -42.975787-0.017505j
[2025-09-18 22:39:42] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -42.969193-0.000043j
[2025-09-18 22:39:55] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -42.959295+0.002518j
[2025-09-18 22:40:07] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -42.954796+0.001635j
[2025-09-18 22:40:19] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -42.983813-0.000308j
[2025-09-18 22:40:32] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -42.951910-0.000177j
[2025-09-18 22:40:44] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -42.974201+0.000356j
[2025-09-18 22:40:57] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -42.965544-0.000995j
[2025-09-18 22:41:09] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -42.969578+0.000051j
[2025-09-18 22:41:22] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -42.944799-0.002659j
[2025-09-18 22:41:34] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -42.938180-0.006058j
[2025-09-18 22:41:47] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -42.960640-0.003976j
[2025-09-18 22:41:59] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -42.976420+0.006752j
[2025-09-18 22:42:12] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -42.964799-0.000694j
[2025-09-18 22:42:24] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -42.966913+0.001950j
[2025-09-18 22:42:36] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -42.951853-0.001887j
[2025-09-18 22:42:49] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -42.969397-0.000223j
[2025-09-18 22:43:01] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -42.973395-0.001296j
[2025-09-18 22:43:14] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -42.962443+0.000847j
[2025-09-18 22:43:26] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -42.947697+0.002032j
[2025-09-18 22:43:39] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -42.971926+0.000099j
[2025-09-18 22:43:51] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -42.944935-0.002536j
[2025-09-18 22:44:04] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -42.950051-0.001952j
[2025-09-18 22:44:16] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -42.954927+0.005254j
[2025-09-18 22:44:29] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -42.992699-0.003104j
[2025-09-18 22:44:41] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -42.965524-0.000481j
[2025-09-18 22:44:53] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -42.971794-0.001934j
[2025-09-18 22:45:06] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -42.960127+0.006591j
[2025-09-18 22:45:18] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -42.981527-0.000898j
[2025-09-18 22:45:31] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -42.954164+0.000943j
[2025-09-18 22:45:43] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -42.967509+0.001412j
[2025-09-18 22:45:56] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -42.960731-0.001551j
[2025-09-18 22:46:08] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -42.953724+0.002313j
[2025-09-18 22:46:21] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -42.950827+0.001278j
[2025-09-18 22:46:33] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -42.960785+0.000468j
[2025-09-18 22:46:45] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -42.955852+0.002287j
[2025-09-18 22:46:58] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -42.962228-0.001668j
[2025-09-18 22:47:10] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -42.974790-0.000184j
[2025-09-18 22:47:23] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -42.969296+0.000948j
[2025-09-18 22:47:35] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -42.962155+0.002536j
[2025-09-18 22:47:48] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -42.960619-0.003039j
[2025-09-18 22:48:00] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -42.958415-0.001492j
[2025-09-18 22:48:12] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -42.968576+0.004581j
[2025-09-18 22:48:25] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -42.965857-0.000531j
[2025-09-18 22:48:37] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -42.967115+0.001103j
[2025-09-18 22:48:50] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -42.955451-0.000587j
[2025-09-18 22:49:02] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -42.950270+0.002409j
[2025-09-18 22:49:15] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -42.951200+0.003111j
[2025-09-18 22:49:27] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -42.954228-0.001018j
[2025-09-18 22:49:39] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -42.956836+0.000384j
[2025-09-18 22:49:52] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -42.958472-0.000146j
[2025-09-18 22:50:04] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -42.974262-0.000284j
[2025-09-18 22:50:17] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -42.963354+0.002006j
[2025-09-18 22:50:29] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -42.957049+0.000611j
[2025-09-18 22:50:42] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -42.956326+0.002004j
[2025-09-18 22:50:54] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -42.975671+0.004124j
[2025-09-18 22:51:06] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -42.979481+0.001444j
[2025-09-18 22:51:19] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -42.958635-0.000918j
[2025-09-18 22:51:31] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -42.972072-0.001783j
[2025-09-18 22:51:44] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -42.960711-0.001545j
[2025-09-18 22:51:56] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -42.955952+0.001447j
[2025-09-18 22:52:09] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -42.954576-0.001341j
[2025-09-18 22:52:21] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -42.971615-0.003085j
[2025-09-18 22:52:34] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -42.974823-0.000907j
[2025-09-18 22:52:46] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -42.963456+0.001537j
[2025-09-18 22:52:59] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -42.951570-0.001913j
[2025-09-18 22:53:11] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -42.959402-0.003127j
[2025-09-18 22:53:24] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -42.973443+0.001581j
[2025-09-18 22:53:36] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -42.960973-0.000446j
[2025-09-18 22:53:49] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -42.967446+0.000899j
[2025-09-18 22:54:01] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -42.969735-0.001238j
[2025-09-18 22:54:13] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -42.959333-0.004454j
[2025-09-18 22:54:13] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-18 22:54:26] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -42.962811-0.000902j
[2025-09-18 22:54:38] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -42.978383-0.000307j
[2025-09-18 22:54:51] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -42.968296+0.000168j
[2025-09-18 22:55:03] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -42.959395-0.003585j
[2025-09-18 22:55:16] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -42.968687-0.001341j
[2025-09-18 22:55:28] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -42.961427-0.001787j
[2025-09-18 22:55:41] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -42.964853-0.000460j
[2025-09-18 22:55:53] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -42.959392+0.002785j
[2025-09-18 22:56:05] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -42.963117-0.001532j
[2025-09-18 22:56:18] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -42.977542-0.001099j
[2025-09-18 22:56:30] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -42.967135-0.003154j
[2025-09-18 22:56:43] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -42.960851+0.001510j
[2025-09-18 22:56:55] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -42.952534+0.000283j
[2025-09-18 22:57:08] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -42.957686+0.000586j
[2025-09-18 22:57:20] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -42.947854+0.001995j
[2025-09-18 22:57:33] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -42.970189-0.001256j
[2025-09-18 22:57:45] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -42.959634+0.000029j
[2025-09-18 22:57:57] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -42.967124+0.001115j
[2025-09-18 22:58:10] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -42.981556-0.000568j
[2025-09-18 22:58:22] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -42.954518-0.001619j
[2025-09-18 22:58:35] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -42.969799+0.002677j
[2025-09-18 22:58:47] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -42.975115+0.003839j
[2025-09-18 22:59:00] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -42.960338-0.000485j
[2025-09-18 22:59:12] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -42.958696+0.002487j
[2025-09-18 22:59:25] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -42.966197+0.000716j
[2025-09-18 22:59:37] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -42.963168-0.000956j
[2025-09-18 22:59:50] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -42.967670+0.003382j
[2025-09-18 23:00:02] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -42.964160-0.003749j
[2025-09-18 23:00:15] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -42.970285-0.001237j
[2025-09-18 23:00:27] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -42.961555-0.000260j
[2025-09-18 23:00:27] RESTART #2 | Period: 600
[2025-09-18 23:00:40] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -42.969963-0.000831j
[2025-09-18 23:00:52] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -42.973874+0.000613j
[2025-09-18 23:01:04] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -42.968637-0.002863j
[2025-09-18 23:01:17] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -42.968690-0.001358j
[2025-09-18 23:01:29] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -42.969141-0.002520j
[2025-09-18 23:01:42] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -42.970875-0.000804j
[2025-09-18 23:01:54] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -42.965343-0.000801j
[2025-09-18 23:02:07] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -42.979720+0.000874j
[2025-09-18 23:02:19] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -42.959517+0.001546j
[2025-09-18 23:02:32] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -42.962340-0.002259j
[2025-09-18 23:02:44] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -42.977515-0.001628j
[2025-09-18 23:02:57] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -42.954107+0.002613j
[2025-09-18 23:03:09] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -42.974828+0.000258j
[2025-09-18 23:03:21] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -42.950862-0.002307j
[2025-09-18 23:03:34] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -42.957456+0.000267j
[2025-09-18 23:03:46] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -42.976066+0.001008j
[2025-09-18 23:03:59] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -42.962204+0.001198j
[2025-09-18 23:04:11] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -42.951018-0.000445j
[2025-09-18 23:04:24] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -42.965766+0.000118j
[2025-09-18 23:04:36] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -42.962819-0.002564j
[2025-09-18 23:04:49] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -42.949501-0.000031j
[2025-09-18 23:05:01] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -42.961292+0.001656j
[2025-09-18 23:05:14] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -42.965266-0.000729j
[2025-09-18 23:05:26] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -42.952447-0.003628j
[2025-09-18 23:05:39] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -42.966212+0.003950j
[2025-09-18 23:05:51] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -42.980756-0.001536j
[2025-09-18 23:06:04] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -42.962876+0.000893j
[2025-09-18 23:06:16] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -42.965331+0.000671j
[2025-09-18 23:06:29] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -42.967484-0.005798j
[2025-09-18 23:06:41] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -42.963472-0.000691j
[2025-09-18 23:06:53] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -42.949542+0.001251j
[2025-09-18 23:07:06] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -42.957786+0.001110j
[2025-09-18 23:07:18] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -42.964250-0.001706j
[2025-09-18 23:07:31] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -42.953480+0.004826j
[2025-09-18 23:07:43] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -42.963342-0.003116j
[2025-09-18 23:07:56] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -42.958579-0.000035j
[2025-09-18 23:08:08] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -42.952085+0.004697j
[2025-09-18 23:08:21] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -42.947049-0.002214j
[2025-09-18 23:08:33] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -42.967807+0.000358j
[2025-09-18 23:08:46] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -42.967380-0.000070j
[2025-09-18 23:08:58] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -42.958151-0.002580j
[2025-09-18 23:09:10] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -42.956438+0.000009j
[2025-09-18 23:09:23] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -42.960154-0.001571j
[2025-09-18 23:09:35] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -42.959062-0.001568j
[2025-09-18 23:09:48] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -42.979637+0.000077j
[2025-09-18 23:10:00] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -42.949793+0.001082j
[2025-09-18 23:10:13] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -42.955391-0.008148j
[2025-09-18 23:10:25] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -42.969646+0.000360j
[2025-09-18 23:10:38] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -42.950207+0.001709j
[2025-09-18 23:10:50] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -42.974943-0.000009j
[2025-09-18 23:11:03] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -42.971228+0.003105j
[2025-09-18 23:11:15] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -42.956059+0.001143j
[2025-09-18 23:11:28] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -42.973051+0.001515j
[2025-09-18 23:11:40] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -42.964363+0.002012j
[2025-09-18 23:11:53] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -42.959381-0.000823j
[2025-09-18 23:12:05] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -42.963403-0.001033j
[2025-09-18 23:12:17] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -42.974583-0.001593j
[2025-09-18 23:12:30] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -42.978631-0.000543j
[2025-09-18 23:12:42] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -42.955158-0.001441j
[2025-09-18 23:12:55] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -42.966832+0.001869j
[2025-09-18 23:13:07] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -42.966476-0.004499j
[2025-09-18 23:13:20] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -42.966349+0.001082j
[2025-09-18 23:13:32] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -42.961429+0.005173j
[2025-09-18 23:13:45] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -42.963134+0.001744j
[2025-09-18 23:13:57] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -42.958737-0.002460j
[2025-09-18 23:14:10] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -42.964631+0.000204j
[2025-09-18 23:14:22] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -42.963574-0.000879j
[2025-09-18 23:14:35] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -42.964605+0.001757j
[2025-09-18 23:14:47] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -42.970981-0.003901j
[2025-09-18 23:15:00] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -42.977537-0.000719j
[2025-09-18 23:15:12] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -42.968359-0.003073j
[2025-09-18 23:15:24] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -42.979146+0.002093j
[2025-09-18 23:15:37] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -42.949815+0.001382j
[2025-09-18 23:15:49] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -42.958590-0.003102j
[2025-09-18 23:16:02] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -42.969623-0.001624j
[2025-09-18 23:16:02] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-18 23:16:14] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -42.947017+0.000659j
[2025-09-18 23:16:27] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -42.968986-0.001960j
[2025-09-18 23:16:39] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -42.979822-0.000005j
[2025-09-18 23:16:51] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -42.968151+0.002153j
[2025-09-18 23:17:04] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -42.952717+0.000866j
[2025-09-18 23:17:16] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -42.959720+0.001644j
[2025-09-18 23:17:29] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -42.959939+0.000686j
[2025-09-18 23:17:41] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -42.968095-0.000550j
[2025-09-18 23:17:54] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -42.965678+0.002641j
[2025-09-18 23:18:06] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -42.948675+0.000006j
[2025-09-18 23:18:19] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -42.970954-0.001443j
[2025-09-18 23:18:31] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -42.976381-0.003576j
[2025-09-18 23:18:44] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -42.956268-0.004823j
[2025-09-18 23:18:56] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -42.965063+0.004356j
[2025-09-18 23:19:09] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -42.972478-0.003747j
[2025-09-18 23:19:21] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -42.966808-0.000038j
[2025-09-18 23:19:33] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -42.977939-0.002851j
[2025-09-18 23:19:46] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -42.956028+0.000376j
[2025-09-18 23:19:58] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -42.975149+0.003575j
[2025-09-18 23:20:11] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -42.967508-0.001753j
[2025-09-18 23:20:23] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -42.955687+0.001087j
[2025-09-18 23:20:36] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -42.968685-0.004020j
[2025-09-18 23:20:48] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -42.952082-0.000837j
[2025-09-18 23:21:00] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -42.966322+0.000180j
[2025-09-18 23:21:12] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -42.947071+0.001612j
[2025-09-18 23:21:25] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -42.985081+0.001753j
[2025-09-18 23:21:37] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -42.961017-0.006156j
[2025-09-18 23:21:50] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -42.948834-0.002515j
[2025-09-18 23:22:02] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -42.957090+0.002096j
[2025-09-18 23:22:15] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -42.967900-0.002866j
[2025-09-18 23:22:27] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -42.963823+0.000637j
[2025-09-18 23:22:39] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -42.966749+0.000421j
[2025-09-18 23:22:52] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -42.969299+0.001421j
[2025-09-18 23:23:04] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -42.961230+0.002032j
[2025-09-18 23:23:17] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -42.960232+0.000396j
[2025-09-18 23:23:29] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -42.948292-0.000246j
[2025-09-18 23:23:42] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -42.953969+0.000538j
[2025-09-18 23:23:54] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -42.965269+0.000310j
[2025-09-18 23:24:07] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -42.973606-0.000287j
[2025-09-18 23:24:19] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -42.976414-0.000024j
[2025-09-18 23:24:31] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -42.958341-0.002470j
[2025-09-18 23:24:44] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -42.975446+0.000531j
[2025-09-18 23:24:56] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -42.962773-0.003418j
[2025-09-18 23:25:09] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -42.970401+0.002235j
[2025-09-18 23:25:21] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -42.952055-0.003123j
[2025-09-18 23:25:33] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -42.956057+0.000967j
[2025-09-18 23:25:46] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -42.951476+0.002698j
[2025-09-18 23:25:58] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -42.980658+0.000771j
[2025-09-18 23:26:11] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -42.959335-0.001327j
[2025-09-18 23:26:23] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -42.949373-0.000493j
[2025-09-18 23:26:36] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -42.971647-0.000726j
[2025-09-18 23:26:48] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -42.965900+0.000578j
[2025-09-18 23:27:00] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -42.968420-0.003014j
[2025-09-18 23:27:13] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -42.947557+0.000575j
[2025-09-18 23:27:25] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -42.971601-0.000214j
[2025-09-18 23:27:38] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -42.983067+0.000358j
[2025-09-18 23:27:50] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -42.972348-0.001284j
[2025-09-18 23:28:03] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -42.963541-0.001326j
[2025-09-18 23:28:15] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -42.948745-0.000205j
[2025-09-18 23:28:27] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -42.958045-0.004262j
[2025-09-18 23:28:40] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -42.957245+0.002300j
[2025-09-18 23:28:52] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -42.958380-0.001029j
[2025-09-18 23:29:05] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -42.957981-0.001298j
[2025-09-18 23:29:17] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -42.962415-0.000364j
[2025-09-18 23:29:30] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -42.957564+0.004027j
[2025-09-18 23:29:42] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -42.970171-0.003644j
[2025-09-18 23:29:55] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -42.981753-0.001039j
[2025-09-18 23:30:07] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -42.964670+0.000647j
[2025-09-18 23:30:20] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -42.973637+0.000842j
[2025-09-18 23:30:32] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -42.977134+0.001463j
[2025-09-18 23:30:45] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -42.965674+0.000399j
[2025-09-18 23:30:57] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -42.953743+0.007418j
[2025-09-18 23:31:10] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -42.961343-0.002286j
[2025-09-18 23:31:22] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -42.962793+0.001979j
[2025-09-18 23:31:35] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -42.953557-0.001073j
[2025-09-18 23:31:47] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -42.968863+0.003079j
[2025-09-18 23:32:00] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -42.954093+0.001612j
[2025-09-18 23:32:13] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -42.949229+0.002783j
[2025-09-18 23:32:25] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -42.966514+0.000672j
[2025-09-18 23:32:38] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -42.956513-0.003608j
[2025-09-18 23:32:50] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -42.970054+0.003087j
[2025-09-18 23:33:03] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -42.975161+0.000407j
[2025-09-18 23:33:15] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -42.952012+0.000676j
[2025-09-18 23:33:28] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -42.970387+0.003086j
[2025-09-18 23:33:40] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -42.961870-0.001386j
[2025-09-18 23:33:53] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -42.962995+0.000637j
[2025-09-18 23:34:06] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -42.964033+0.000747j
[2025-09-18 23:34:18] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -42.955529-0.001336j
[2025-09-18 23:34:31] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -42.969788-0.001821j
[2025-09-18 23:34:43] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -42.955506+0.000313j
[2025-09-18 23:34:56] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -42.959609+0.001451j
[2025-09-18 23:35:08] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -42.963404-0.001640j
[2025-09-18 23:35:21] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -42.956838-0.000065j
[2025-09-18 23:35:34] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -42.950762-0.000307j
[2025-09-18 23:35:46] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -42.955323-0.002578j
[2025-09-18 23:35:59] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -42.964114+0.002014j
[2025-09-18 23:36:11] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -42.949625+0.001596j
[2025-09-18 23:36:24] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -42.948941-0.000542j
[2025-09-18 23:36:36] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -42.947536+0.000815j
[2025-09-18 23:36:49] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -42.970150-0.001782j
[2025-09-18 23:37:01] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -42.966635-0.000849j
[2025-09-18 23:37:14] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -42.962972-0.002076j
[2025-09-18 23:37:26] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -42.971390+0.000254j
[2025-09-18 23:37:39] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -42.951554-0.001215j
[2025-09-18 23:37:51] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -42.952036-0.001260j
[2025-09-18 23:37:51] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-18 23:38:04] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -42.970260-0.000304j
[2025-09-18 23:38:16] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -42.967208-0.000764j
[2025-09-18 23:38:29] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -42.957834+0.004000j
[2025-09-18 23:38:42] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -42.962722-0.000299j
[2025-09-18 23:38:54] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -42.959918-0.003634j
[2025-09-18 23:39:07] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -42.970632+0.000046j
[2025-09-18 23:39:19] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -42.959744-0.001151j
[2025-09-18 23:39:32] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -42.970212-0.001732j
[2025-09-18 23:39:44] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -42.964495+0.000993j
[2025-09-18 23:39:57] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -42.967598-0.001577j
[2025-09-18 23:40:09] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -42.969525-0.001040j
[2025-09-18 23:40:22] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -42.963676+0.000102j
[2025-09-18 23:40:34] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -42.961536+0.005265j
[2025-09-18 23:40:47] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -42.974568-0.004322j
[2025-09-18 23:40:59] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -42.965696+0.001559j
[2025-09-18 23:41:12] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -42.970098+0.001246j
[2025-09-18 23:41:24] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -42.972586-0.003218j
[2025-09-18 23:41:37] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -42.965643+0.000774j
[2025-09-18 23:41:50] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -42.969280+0.003548j
[2025-09-18 23:42:02] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -42.961700-0.000461j
[2025-09-18 23:42:15] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -42.946001-0.000062j
[2025-09-18 23:42:27] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -42.955788-0.002278j
[2025-09-18 23:42:40] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -42.958274+0.002244j
[2025-09-18 23:42:52] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -42.961881+0.001493j
[2025-09-18 23:43:05] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -42.951620-0.001185j
[2025-09-18 23:43:17] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -42.961631-0.000857j
[2025-09-18 23:43:30] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -42.958281+0.000342j
[2025-09-18 23:43:42] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -42.955173-0.002951j
[2025-09-18 23:43:55] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -42.968489+0.001689j
[2025-09-18 23:44:07] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -42.958972-0.000620j
[2025-09-18 23:44:20] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -42.983575-0.008764j
[2025-09-18 23:44:32] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -42.958765+0.000592j
[2025-09-18 23:44:45] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -42.964464-0.000254j
[2025-09-18 23:44:57] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -42.953164+0.002688j
[2025-09-18 23:45:10] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -42.945443-0.001107j
[2025-09-18 23:45:22] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -42.945936+0.000118j
[2025-09-18 23:45:35] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -42.968133+0.004880j
[2025-09-18 23:45:47] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -42.961906+0.003095j
[2025-09-18 23:46:00] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -42.956934-0.003751j
[2025-09-18 23:46:12] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -42.977991+0.003463j
[2025-09-18 23:46:25] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -42.961513+0.005091j
[2025-09-18 23:46:37] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -42.965872-0.001199j
[2025-09-18 23:46:50] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -42.952557-0.000517j
[2025-09-18 23:47:02] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -42.962878-0.004188j
[2025-09-18 23:47:14] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -42.967891-0.002909j
[2025-09-18 23:47:27] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -42.978253-0.002168j
[2025-09-18 23:47:39] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -42.984486+0.001820j
[2025-09-18 23:47:52] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -42.965146+0.002860j
[2025-09-18 23:48:04] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -42.975967-0.000210j
[2025-09-18 23:48:17] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -42.970298+0.000883j
[2025-09-18 23:48:29] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -42.966175-0.000362j
[2025-09-18 23:48:42] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -42.966166+0.001588j
[2025-09-18 23:48:54] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -42.955534+0.001427j
[2025-09-18 23:49:07] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -42.952549-0.001087j
[2025-09-18 23:49:19] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -42.975674+0.001343j
[2025-09-18 23:49:32] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -42.957379-0.003097j
[2025-09-18 23:49:44] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -42.958384-0.003127j
[2025-09-18 23:49:57] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -42.980737-0.004661j
[2025-09-18 23:50:09] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -42.955792+0.000335j
[2025-09-18 23:50:21] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -42.966989-0.001526j
[2025-09-18 23:50:34] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -42.943772+0.000798j
[2025-09-18 23:50:46] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -42.968800-0.005241j
[2025-09-18 23:50:59] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -42.961767-0.001033j
[2025-09-18 23:51:11] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -42.953924-0.002972j
[2025-09-18 23:51:24] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -42.964257+0.000975j
[2025-09-18 23:51:36] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -42.952685-0.000331j
[2025-09-18 23:51:48] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -42.956813-0.001612j
[2025-09-18 23:52:01] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -42.959265+0.001264j
[2025-09-18 23:52:13] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -42.963028-0.000081j
[2025-09-18 23:52:26] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -42.963155+0.000885j
[2025-09-18 23:52:38] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -42.963643+0.000185j
[2025-09-18 23:52:51] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -42.951203+0.004471j
[2025-09-18 23:53:03] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -42.962779+0.003276j
[2025-09-18 23:53:15] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -42.970727-0.000094j
[2025-09-18 23:53:28] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -42.962958-0.002784j
[2025-09-18 23:53:40] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -42.970075-0.000092j
[2025-09-18 23:53:53] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -42.958967+0.000511j
[2025-09-18 23:54:05] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -42.938142+0.001680j
[2025-09-18 23:54:18] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -42.962772-0.003099j
[2025-09-18 23:54:30] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -42.968362+0.001759j
[2025-09-18 23:54:43] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -42.969461-0.003485j
[2025-09-18 23:54:55] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -42.963538+0.003088j
[2025-09-18 23:55:07] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -42.963735+0.001761j
[2025-09-18 23:55:20] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -42.973769+0.000252j
[2025-09-18 23:55:32] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -42.971804-0.000647j
[2025-09-18 23:55:45] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -42.969665+0.002327j
[2025-09-18 23:55:57] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -42.962383-0.000973j
[2025-09-18 23:56:10] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -42.966328-0.002936j
[2025-09-18 23:56:22] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -42.966620+0.006074j
[2025-09-18 23:56:35] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -42.953920+0.001095j
[2025-09-18 23:56:48] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -42.970019-0.001160j
[2025-09-18 23:57:00] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -42.963556-0.002275j
[2025-09-18 23:57:13] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -42.970814+0.001210j
[2025-09-18 23:57:25] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -42.965995-0.001641j
[2025-09-18 23:57:38] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -42.973218+0.002110j
[2025-09-18 23:57:50] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -42.964692+0.000030j
[2025-09-18 23:58:03] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -42.965904+0.001774j
[2025-09-18 23:58:15] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -42.959117+0.007592j
[2025-09-18 23:58:28] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -42.958271+0.000263j
[2025-09-18 23:58:41] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -42.957912+0.005843j
[2025-09-18 23:58:53] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -42.958228-0.000944j
[2025-09-18 23:59:06] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -42.970310+0.001400j
[2025-09-18 23:59:18] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -42.972972+0.001670j
[2025-09-18 23:59:31] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -42.967384+0.003815j
[2025-09-18 23:59:43] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -42.979704-0.002885j
[2025-09-18 23:59:43] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-18 23:59:56] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -42.956326-0.010023j
[2025-09-19 00:00:08] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -42.963396-0.005109j
[2025-09-19 00:00:21] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -42.947393-0.003292j
[2025-09-19 00:00:33] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -42.976789-0.000141j
[2025-09-19 00:00:46] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -42.972426+0.002582j
[2025-09-19 00:00:58] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -42.978265-0.000159j
[2025-09-19 00:01:11] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -42.974009-0.000534j
[2025-09-19 00:01:23] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -42.975534-0.000213j
[2025-09-19 00:01:36] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -42.955573-0.000393j
[2025-09-19 00:01:48] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -42.972890-0.001040j
[2025-09-19 00:02:01] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -42.963503-0.001388j
[2025-09-19 00:02:14] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -42.956350+0.001112j
[2025-09-19 00:02:26] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -42.960390+0.001488j
[2025-09-19 00:02:39] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -42.974903+0.001498j
[2025-09-19 00:02:51] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -42.966514-0.002257j
[2025-09-19 00:03:04] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -42.955889+0.003199j
[2025-09-19 00:03:16] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -42.964519+0.001383j
[2025-09-19 00:03:29] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -42.974402+0.004396j
[2025-09-19 00:03:41] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -42.945598+0.000009j
[2025-09-19 00:03:54] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -42.984168+0.002232j
[2025-09-19 00:04:06] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -42.956506+0.003322j
[2025-09-19 00:04:19] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -42.976960+0.002408j
[2025-09-19 00:04:31] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -42.946255+0.001860j
[2025-09-19 00:04:44] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -42.964232+0.001617j
[2025-09-19 00:04:57] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -42.961178+0.000947j
[2025-09-19 00:05:09] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -42.979101-0.002890j
[2025-09-19 00:05:22] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -42.954107-0.003156j
[2025-09-19 00:05:34] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -42.960815+0.004555j
[2025-09-19 00:05:47] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -42.958103-0.002085j
[2025-09-19 00:05:59] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -42.952968+0.002096j
[2025-09-19 00:06:12] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -42.963562+0.000555j
[2025-09-19 00:06:24] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -42.964239+0.000014j
[2025-09-19 00:06:37] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -42.978056-0.000007j
[2025-09-19 00:06:49] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -42.963000+0.002622j
[2025-09-19 00:07:02] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -42.947075-0.001317j
[2025-09-19 00:07:15] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -42.972935+0.002191j
[2025-09-19 00:07:27] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -42.972702-0.002711j
[2025-09-19 00:07:40] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -42.963094+0.009431j
[2025-09-19 00:07:52] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -42.954818+0.001329j
[2025-09-19 00:08:05] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -42.968559+0.000483j
[2025-09-19 00:08:17] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -42.971026-0.000777j
[2025-09-19 00:08:30] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -42.952230+0.004099j
[2025-09-19 00:08:42] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -42.943030+0.013319j
[2025-09-19 00:08:55] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -42.980559-0.000223j
[2025-09-19 00:09:07] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -42.968596+0.004078j
[2025-09-19 00:09:20] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -42.958626+0.003202j
[2025-09-19 00:09:32] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -42.963209-0.001804j
[2025-09-19 00:09:45] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -42.958845-0.000638j
[2025-09-19 00:09:57] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -42.956275-0.004522j
[2025-09-19 00:10:10] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -42.964099+0.002299j
[2025-09-19 00:10:23] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -42.954212+0.001747j
[2025-09-19 00:10:35] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -42.979746-0.001313j
[2025-09-19 00:10:48] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -42.967503-0.000473j
[2025-09-19 00:11:00] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -42.948457+0.003193j
[2025-09-19 00:11:13] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -42.975115-0.001690j
[2025-09-19 00:11:25] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -42.957955+0.000461j
[2025-09-19 00:11:38] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -42.983191+0.002636j
[2025-09-19 00:11:50] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -42.981433+0.000976j
[2025-09-19 00:12:03] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -42.957520-0.000995j
[2025-09-19 00:12:15] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -42.967998-0.000022j
[2025-09-19 00:12:28] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -42.966343+0.002806j
[2025-09-19 00:12:40] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -42.965847+0.000449j
[2025-09-19 00:12:53] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -42.964055+0.000552j
[2025-09-19 00:13:05] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -42.966901+0.000603j
[2025-09-19 00:13:18] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -42.964710-0.001678j
[2025-09-19 00:13:30] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -42.970936-0.000769j
[2025-09-19 00:13:43] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -42.951863-0.000545j
[2025-09-19 00:13:55] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -42.952741+0.001127j
[2025-09-19 00:14:08] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -42.964278+0.000447j
[2025-09-19 00:14:20] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -42.973338+0.002561j
[2025-09-19 00:14:33] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -42.961945-0.000241j
[2025-09-19 00:14:45] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -42.957973-0.002333j
[2025-09-19 00:14:58] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -42.974734+0.004491j
[2025-09-19 00:15:10] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -42.962953+0.003215j
[2025-09-19 00:15:23] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -42.957024-0.001439j
[2025-09-19 00:15:36] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -42.972627-0.000427j
[2025-09-19 00:15:48] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -42.969738+0.002413j
[2025-09-19 00:16:01] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -42.969197-0.001188j
[2025-09-19 00:16:13] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -42.959828-0.002180j
[2025-09-19 00:16:26] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -42.958529-0.004117j
[2025-09-19 00:16:38] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -42.985248+0.002034j
[2025-09-19 00:16:51] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -42.950742-0.001048j
[2025-09-19 00:17:03] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -42.954803+0.000869j
[2025-09-19 00:17:16] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -42.954072+0.004046j
[2025-09-19 00:17:28] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -42.967255-0.002189j
[2025-09-19 00:17:41] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -42.946862-0.001002j
[2025-09-19 00:17:53] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -42.958468+0.003050j
[2025-09-19 00:18:06] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -42.972282+0.000867j
[2025-09-19 00:18:18] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -42.962061-0.002601j
[2025-09-19 00:18:31] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -42.963193-0.001048j
[2025-09-19 00:18:44] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -42.982892+0.000863j
[2025-09-19 00:18:56] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -42.978975+0.000258j
[2025-09-19 00:19:09] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -42.966994-0.000756j
[2025-09-19 00:19:22] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -42.971936+0.002550j
[2025-09-19 00:19:34] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -42.954925+0.001685j
[2025-09-19 00:19:47] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -42.961137+0.000316j
[2025-09-19 00:19:59] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -42.981444+0.002974j
[2025-09-19 00:20:12] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -42.969792+0.002856j
[2025-09-19 00:20:24] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -42.974317-0.004043j
[2025-09-19 00:20:37] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -42.965168+0.000496j
[2025-09-19 00:20:49] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -42.960886+0.000743j
[2025-09-19 00:21:02] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -42.972400+0.001104j
[2025-09-19 00:21:14] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -42.970471-0.001657j
[2025-09-19 00:21:27] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -42.970128+0.001344j
[2025-09-19 00:21:40] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -42.963525+0.000309j
[2025-09-19 00:21:40] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-19 00:21:52] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -42.966371-0.003662j
[2025-09-19 00:22:05] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -42.981322-0.000528j
[2025-09-19 00:22:17] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -42.982302+0.001777j
[2025-09-19 00:22:30] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -42.976835+0.001297j
[2025-09-19 00:22:42] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -42.965033+0.002731j
[2025-09-19 00:22:55] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -42.969414-0.000038j
[2025-09-19 00:23:07] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -42.955487-0.001573j
[2025-09-19 00:23:20] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -42.947553-0.000856j
[2025-09-19 00:23:33] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -42.954640+0.000059j
[2025-09-19 00:23:45] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -42.951003-0.004774j
[2025-09-19 00:23:58] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -42.978155+0.002130j
[2025-09-19 00:24:10] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -42.980831+0.000646j
[2025-09-19 00:24:23] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -42.956819+0.001637j
[2025-09-19 00:24:35] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -42.973701-0.000160j
[2025-09-19 00:24:48] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -42.962437-0.008087j
[2025-09-19 00:25:00] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -42.959848+0.000841j
[2025-09-19 00:25:13] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -42.960297+0.001941j
[2025-09-19 00:25:25] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -42.960576-0.001517j
[2025-09-19 00:25:38] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -42.980526-0.001394j
[2025-09-19 00:25:50] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -42.952762+0.000163j
[2025-09-19 00:26:03] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -42.972478-0.000916j
[2025-09-19 00:26:15] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -42.977133+0.001980j
[2025-09-19 00:26:28] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -42.977676-0.002140j
[2025-09-19 00:26:40] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -42.952196+0.010034j
[2025-09-19 00:26:53] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -42.964127-0.000064j
[2025-09-19 00:27:05] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -42.967530-0.006011j
[2025-09-19 00:27:18] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -42.968226+0.000969j
[2025-09-19 00:27:30] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -42.956347+0.001439j
[2025-09-19 00:27:42] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -42.975571+0.001409j
[2025-09-19 00:27:55] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -42.955487-0.000241j
[2025-09-19 00:28:07] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -42.958906+0.000563j
[2025-09-19 00:28:20] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -42.971913+0.000506j
[2025-09-19 00:28:32] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -42.971887+0.000397j
[2025-09-19 00:28:45] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -42.984624+0.000289j
[2025-09-19 00:28:57] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -42.964396-0.001734j
[2025-09-19 00:29:10] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -42.981572+0.003014j
[2025-09-19 00:29:22] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -42.964724-0.000326j
[2025-09-19 00:29:35] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -42.961319+0.001018j
[2025-09-19 00:29:47] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -42.979913+0.001387j
[2025-09-19 00:29:59] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -42.971074+0.000110j
[2025-09-19 00:30:12] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -42.966386+0.002612j
[2025-09-19 00:30:24] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -42.978506-0.001506j
[2025-09-19 00:30:37] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -42.983559-0.000556j
[2025-09-19 00:30:49] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -42.968131-0.002035j
[2025-09-19 00:31:02] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -42.961301+0.000077j
[2025-09-19 00:31:14] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -42.971634+0.001918j
[2025-09-19 00:31:26] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -42.963217-0.000740j
[2025-09-19 00:31:39] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -42.962063+0.001048j
[2025-09-19 00:31:51] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -42.974882-0.001517j
[2025-09-19 00:32:04] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -42.954420+0.001891j
[2025-09-19 00:32:16] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -42.955969+0.004703j
[2025-09-19 00:32:29] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -42.982343+0.002709j
[2025-09-19 00:32:41] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -42.971893-0.004676j
[2025-09-19 00:32:54] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -42.978796-0.001857j
[2025-09-19 00:33:06] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -42.971193-0.005431j
[2025-09-19 00:33:18] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -42.970658+0.006639j
[2025-09-19 00:33:31] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -42.967138+0.000056j
[2025-09-19 00:33:43] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -42.951209+0.006686j
[2025-09-19 00:33:56] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -42.973725+0.001427j
[2025-09-19 00:34:08] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -42.967635+0.003225j
[2025-09-19 00:34:21] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -42.969171-0.000293j
[2025-09-19 00:34:33] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -42.972606-0.000361j
[2025-09-19 00:34:46] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -42.964174+0.001368j
[2025-09-19 00:34:58] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -42.964896+0.001442j
[2025-09-19 00:35:11] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -42.963383-0.001660j
[2025-09-19 00:35:23] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -42.969987-0.000993j
[2025-09-19 00:35:35] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -42.971190+0.000629j
[2025-09-19 00:35:48] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -42.955981-0.000481j
[2025-09-19 00:36:00] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -42.965834+0.000484j
[2025-09-19 00:36:12] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -42.964617-0.002430j
[2025-09-19 00:36:25] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -42.949807-0.001676j
[2025-09-19 00:36:37] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -42.964939-0.000745j
[2025-09-19 00:36:50] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -42.962971-0.000177j
[2025-09-19 00:37:02] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -42.976796+0.002222j
[2025-09-19 00:37:15] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -42.973091+0.003702j
[2025-09-19 00:37:27] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -42.962878-0.000532j
[2025-09-19 00:37:40] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -42.969470+0.000764j
[2025-09-19 00:37:52] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -42.982915-0.003122j
[2025-09-19 00:38:05] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -42.963825-0.001715j
[2025-09-19 00:38:17] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -42.974445-0.000972j
[2025-09-19 00:38:30] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -42.961824-0.000265j
[2025-09-19 00:38:42] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -42.978588+0.002539j
[2025-09-19 00:38:55] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -42.967446-0.000155j
[2025-09-19 00:39:07] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -42.956994+0.003582j
[2025-09-19 00:39:20] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -42.961347+0.000829j
[2025-09-19 00:39:32] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -42.973849+0.002382j
[2025-09-19 00:39:45] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -42.960009+0.003968j
[2025-09-19 00:39:57] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -42.951583-0.000931j
[2025-09-19 00:40:10] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -42.957661-0.001296j
[2025-09-19 00:40:22] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -42.955554-0.000364j
[2025-09-19 00:40:35] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -42.976471-0.000722j
[2025-09-19 00:40:48] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -42.971352-0.001010j
[2025-09-19 00:41:00] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -42.974454+0.003618j
[2025-09-19 00:41:13] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -42.973842+0.001679j
[2025-09-19 00:41:25] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -42.957401-0.000609j
[2025-09-19 00:41:38] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -42.970883+0.001725j
[2025-09-19 00:41:50] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -42.959575-0.000867j
[2025-09-19 00:42:03] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -42.968411-0.001885j
[2025-09-19 00:42:15] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -42.969976+0.000831j
[2025-09-19 00:42:28] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -42.968622-0.003466j
[2025-09-19 00:42:40] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -42.973155-0.002620j
[2025-09-19 00:42:53] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -42.968120+0.000194j
[2025-09-19 00:43:06] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -42.954262+0.001178j
[2025-09-19 00:43:18] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -42.971487+0.000530j
[2025-09-19 00:43:31] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -42.959385-0.000456j
[2025-09-19 00:43:31] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-19 00:43:43] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -42.965379+0.000554j
[2025-09-19 00:43:56] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -42.966267+0.000082j
[2025-09-19 00:44:08] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -42.977075-0.004462j
[2025-09-19 00:44:21] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -42.966026-0.000066j
[2025-09-19 00:44:33] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -42.976911+0.001277j
[2025-09-19 00:44:46] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -42.966806-0.003007j
[2025-09-19 00:44:58] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -42.961623+0.000354j
[2025-09-19 00:45:11] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -42.969269-0.002551j
[2025-09-19 00:45:23] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -42.966401-0.000343j
[2025-09-19 00:45:36] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -42.945562+0.000287j
[2025-09-19 00:45:48] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -42.965510-0.001852j
[2025-09-19 00:46:01] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -42.954562+0.002357j
[2025-09-19 00:46:14] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -42.978698+0.000262j
[2025-09-19 00:46:26] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -42.983937+0.010370j
[2025-09-19 00:46:39] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -42.961897+0.000542j
[2025-09-19 00:46:51] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -42.969280+0.000935j
[2025-09-19 00:47:03] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -42.964469-0.001782j
[2025-09-19 00:47:16] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -42.970134-0.001016j
[2025-09-19 00:47:28] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -42.966999+0.000659j
[2025-09-19 00:47:41] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -42.975568-0.001034j
[2025-09-19 00:47:53] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -42.949132-0.000755j
[2025-09-19 00:48:06] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -42.979046+0.006052j
[2025-09-19 00:48:18] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -42.969307+0.000784j
[2025-09-19 00:48:31] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -42.947011+0.000014j
[2025-09-19 00:48:43] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -42.957575+0.003383j
[2025-09-19 00:48:56] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -42.966357-0.001194j
[2025-09-19 00:49:08] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -42.970056-0.000432j
[2025-09-19 00:49:21] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -42.961026-0.002826j
[2025-09-19 00:49:33] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -42.961730+0.000100j
[2025-09-19 00:49:45] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -42.966845-0.001434j
[2025-09-19 00:49:58] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -42.974629-0.003873j
[2025-09-19 00:50:10] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -42.964360-0.001624j
[2025-09-19 00:50:23] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -42.953321+0.004113j
[2025-09-19 00:50:35] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -42.970699-0.000024j
[2025-09-19 00:50:48] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -42.982370+0.001219j
[2025-09-19 00:51:00] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -42.956864+0.001418j
[2025-09-19 00:51:13] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -42.968629-0.000296j
[2025-09-19 00:51:25] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -42.975287-0.003590j
[2025-09-19 00:51:38] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -42.964257-0.003414j
[2025-09-19 00:51:50] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -42.967123+0.001047j
[2025-09-19 00:52:03] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -42.975812-0.001029j
[2025-09-19 00:52:15] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -42.964774-0.000573j
[2025-09-19 00:52:27] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -42.963468-0.000494j
[2025-09-19 00:52:40] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -42.979025-0.001766j
[2025-09-19 00:52:52] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -42.975199+0.001019j
[2025-09-19 00:53:05] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -42.975913+0.001214j
[2025-09-19 00:53:17] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -42.980015-0.000265j
[2025-09-19 00:53:30] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -42.970225-0.000317j
[2025-09-19 00:53:42] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -42.974409-0.006924j
[2025-09-19 00:53:55] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -42.970463-0.000605j
[2025-09-19 00:54:07] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -42.967175-0.000709j
[2025-09-19 00:54:20] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -42.973702+0.004825j
[2025-09-19 00:54:32] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -42.965611-0.002747j
[2025-09-19 00:54:44] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -42.975107+0.001621j
[2025-09-19 00:54:57] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -42.971159+0.001292j
[2025-09-19 00:55:09] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -42.969981-0.001674j
[2025-09-19 00:55:22] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -42.976083-0.002350j
[2025-09-19 00:55:33] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -42.962088-0.000094j
[2025-09-19 00:55:46] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -42.952725+0.004155j
[2025-09-19 00:55:58] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -42.960031+0.001113j
[2025-09-19 00:56:11] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -42.956067-0.000247j
[2025-09-19 00:56:23] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -42.965000+0.000311j
[2025-09-19 00:56:36] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -42.962218+0.001494j
[2025-09-19 00:56:48] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -42.979631+0.001734j
[2025-09-19 00:57:00] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -42.968083-0.001000j
[2025-09-19 00:57:13] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -42.955893+0.001982j
[2025-09-19 00:57:25] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -42.970966-0.000155j
[2025-09-19 00:57:38] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -42.969524-0.004456j
[2025-09-19 00:57:50] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -42.965978+0.001013j
[2025-09-19 00:58:03] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -42.951264+0.000921j
[2025-09-19 00:58:15] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -42.952249-0.002204j
[2025-09-19 00:58:27] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -42.958208+0.001060j
[2025-09-19 00:58:40] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -42.960273-0.000419j
[2025-09-19 00:58:52] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -42.973839+0.002159j
[2025-09-19 00:59:05] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -42.962688-0.001767j
[2025-09-19 00:59:17] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -42.959573+0.000730j
[2025-09-19 00:59:30] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -42.970081-0.003795j
[2025-09-19 00:59:42] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -42.976128+0.000293j
[2025-09-19 00:59:55] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -42.967230+0.000231j
[2025-09-19 01:00:07] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -42.979694-0.001564j
[2025-09-19 01:00:19] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -42.978565+0.004018j
[2025-09-19 01:00:32] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -42.965524-0.001375j
[2025-09-19 01:00:44] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -42.963135-0.001304j
[2025-09-19 01:00:57] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -42.951241+0.001577j
[2025-09-19 01:01:09] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -42.968990-0.002056j
[2025-09-19 01:01:22] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -42.983772-0.004007j
[2025-09-19 01:01:34] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -42.956304+0.006830j
[2025-09-19 01:01:46] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -42.990361-0.001794j
[2025-09-19 01:01:59] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -42.959447-0.001139j
[2025-09-19 01:02:11] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -42.957082+0.001458j
[2025-09-19 01:02:24] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -42.973699+0.003003j
[2025-09-19 01:02:36] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -42.961777-0.001671j
[2025-09-19 01:02:49] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -42.977821-0.000247j
[2025-09-19 01:03:01] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -42.972416+0.001835j
[2025-09-19 01:03:14] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -42.964269+0.002263j
[2025-09-19 01:03:26] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -42.971105+0.001708j
[2025-09-19 01:03:38] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -42.960963+0.002100j
[2025-09-19 01:03:51] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -42.957582+0.000265j
[2025-09-19 01:04:03] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -42.955636-0.001496j
[2025-09-19 01:04:16] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -42.961679+0.003521j
[2025-09-19 01:04:28] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -42.969871-0.001755j
[2025-09-19 01:04:41] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -42.959444-0.001272j
[2025-09-19 01:04:52] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -42.973808-0.000134j
[2025-09-19 01:05:05] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -42.949405+0.004570j
[2025-09-19 01:05:17] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -42.969351-0.001503j
[2025-09-19 01:05:17] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-19 01:05:17] ✅ Training completed | Restarts: 2
[2025-09-19 01:05:17] ============================================================
[2025-09-19 01:05:17] Training completed | Runtime: 13163.3s
[2025-09-19 01:05:22] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-19 01:05:22] ============================================================
