[2025-09-19 23:30:53] ✓ 从checkpoint恢复: results/L=6/J2=1.00/J1=0.78/training/checkpoints/final_GCNN.pkl
[2025-09-19 23:30:53]   - 迭代次数: final
[2025-09-19 23:30:53]   - 能量: -62.760933-0.003674j ± 0.009609
[2025-09-19 23:30:53]   - 时间戳: 2025-09-19T23:28:38.640371+08:00
[2025-09-19 23:31:12] ✓ 变分状态参数已从checkpoint恢复
[2025-09-19 23:31:12] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-19 23:31:12] ==================================================
[2025-09-19 23:31:12] GCNN for Shastry-Sutherland Model
[2025-09-19 23:31:12] ==================================================
[2025-09-19 23:31:12] System parameters:
[2025-09-19 23:31:12]   - System size: L=6, N=144
[2025-09-19 23:31:12]   - System parameters: J1=0.77, J2=1.0, Q=0.0
[2025-09-19 23:31:12] --------------------------------------------------
[2025-09-19 23:31:12] Model parameters:
[2025-09-19 23:31:12]   - Number of layers = 4
[2025-09-19 23:31:12]   - Number of features = 4
[2025-09-19 23:31:12]   - Total parameters = 28252
[2025-09-19 23:31:12] --------------------------------------------------
[2025-09-19 23:31:12] Training parameters:
[2025-09-19 23:31:12]   - Learning rate: 0.015
[2025-09-19 23:31:12]   - Total iterations: 1050
[2025-09-19 23:31:12]   - Annealing cycles: 3
[2025-09-19 23:31:12]   - Initial period: 150
[2025-09-19 23:31:12]   - Period multiplier: 2.0
[2025-09-19 23:31:12]   - Temperature range: 0.0-1.0
[2025-09-19 23:31:12]   - Samples: 4096
[2025-09-19 23:31:13]   - Discarded samples: 0
[2025-09-19 23:31:13]   - Chunk size: 2048
[2025-09-19 23:31:13]   - Diagonal shift: 0.2
[2025-09-19 23:31:13]   - Gradient clipping: 1.0
[2025-09-19 23:31:13]   - Checkpoint enabled: interval=105
[2025-09-19 23:31:13]   - Checkpoint directory: results/L=6/J2=1.00/J1=0.77/training/checkpoints
[2025-09-19 23:31:13] --------------------------------------------------
[2025-09-19 23:31:13] Device status:
[2025-09-19 23:31:13]   - Devices model: NVIDIA H200 NVL
[2025-09-19 23:31:13]   - Number of devices: 1
[2025-09-19 23:31:13]   - Sharding: True
[2025-09-19 23:31:13] ============================================================
[2025-09-19 23:32:48] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -25.363198-0.004111j
[2025-09-19 23:33:51] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -26.971464+0.003501j
[2025-09-19 23:34:23] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -29.025528-0.002165j
[2025-09-19 23:34:55] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -31.485974-0.183537j
[2025-09-19 23:35:28] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -35.667890+0.039699j
[2025-09-19 23:36:00] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -42.335670-0.025894j
[2025-09-19 23:36:32] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -49.515629+0.029411j
[2025-09-19 23:37:04] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -55.186984-0.025107j
[2025-09-19 23:37:35] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -58.809385-0.022795j
[2025-09-19 23:38:08] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -60.861682+0.012014j
[2025-09-19 23:38:39] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -61.604730+0.002809j
[2025-09-19 23:39:12] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -61.804262-0.003169j
[2025-09-19 23:39:43] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -61.807608-0.000908j
[2025-09-19 23:40:15] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -61.799763-0.003087j
[2025-09-19 23:40:47] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -61.814464-0.002265j
[2025-09-19 23:41:19] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -61.818153-0.002119j
[2025-09-19 23:41:51] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -61.808069-0.000192j
[2025-09-19 23:42:23] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -61.798226+0.001715j
[2025-09-19 23:42:55] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -61.809305+0.012113j
[2025-09-19 23:43:27] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -61.811799-0.003415j
[2025-09-19 23:43:59] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -61.822103+0.000259j
[2025-09-19 23:44:31] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -61.807972-0.004501j
[2025-09-19 23:45:03] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -61.833432+0.002983j
[2025-09-19 23:45:35] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -61.815509+0.005967j
[2025-09-19 23:46:07] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -61.815761+0.000530j
[2025-09-19 23:46:39] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -61.795658-0.001929j
[2025-09-19 23:47:11] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -61.843308-0.000823j
[2025-09-19 23:47:43] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -61.828121+0.003351j
[2025-09-19 23:48:15] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -61.803888+0.001478j
[2025-09-19 23:48:47] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -61.818398-0.002697j
[2025-09-19 23:49:19] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -61.816447-0.008068j
[2025-09-19 23:49:51] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -61.818872+0.000452j
[2025-09-19 23:50:23] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -61.815015+0.004296j
[2025-09-19 23:50:55] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -61.827984+0.003189j
[2025-09-19 23:51:27] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -61.829381+0.000302j
[2025-09-19 23:51:59] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -61.825103+0.001245j
[2025-09-19 23:52:31] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -61.820922-0.000355j
[2025-09-19 23:53:03] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -61.822025-0.000901j
[2025-09-19 23:53:35] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -61.823770-0.000169j
[2025-09-19 23:54:07] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -61.821217+0.002973j
[2025-09-19 23:54:39] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -61.819016-0.000809j
[2025-09-19 23:55:11] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -61.817019+0.000195j
[2025-09-19 23:55:43] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -61.817519+0.002459j
[2025-09-19 23:56:15] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -61.809297-0.000325j
[2025-09-19 23:56:47] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -61.814795-0.001069j
[2025-09-19 23:57:19] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -61.831680+0.000156j
[2025-09-19 23:57:51] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -61.833438-0.004793j
[2025-09-19 23:58:23] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -61.814343-0.002219j
[2025-09-19 23:58:55] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -61.799758-0.000983j
[2025-09-19 23:59:27] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -61.821969-0.002655j
[2025-09-19 23:59:59] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -61.831216+0.000194j
[2025-09-20 00:00:31] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -61.792611-0.001267j
[2025-09-20 00:01:03] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -61.829440-0.001937j
[2025-09-20 00:01:34] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -61.820315+0.000916j
[2025-09-20 00:02:06] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -61.828805-0.000589j
[2025-09-20 00:02:38] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -61.806053+0.001072j
[2025-09-20 00:03:10] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -61.813640-0.002644j
[2025-09-20 00:03:42] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -61.834763-0.003210j
[2025-09-20 00:04:14] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -61.806443-0.001239j
[2025-09-20 00:04:46] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -61.836046-0.002392j
[2025-09-20 00:05:18] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -61.840182+0.001330j
[2025-09-20 00:05:50] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -61.817641-0.005310j
[2025-09-20 00:06:22] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -61.843896-0.002522j
[2025-09-20 00:06:54] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -61.821364+0.003178j
[2025-09-20 00:07:26] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -61.816006-0.000501j
[2025-09-20 00:07:58] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -61.826706-0.002575j
[2025-09-20 00:08:30] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -61.806882-0.000757j
[2025-09-20 00:09:02] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -61.832754-0.003416j
[2025-09-20 00:09:34] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -61.833949-0.003622j
[2025-09-20 00:10:06] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -61.805606+0.000403j
[2025-09-20 00:10:38] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -61.808915+0.005211j
[2025-09-20 00:11:10] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -61.820307-0.001945j
[2025-09-20 00:11:42] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -61.816132+0.001319j
[2025-09-20 00:12:14] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -61.842243+0.000010j
[2025-09-20 00:12:46] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -61.826089-0.001547j
[2025-09-20 00:13:18] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -61.838254+0.003700j
[2025-09-20 00:13:50] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -61.821152-0.001599j
[2025-09-20 00:14:22] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -61.808925+0.001181j
[2025-09-20 00:14:54] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -61.825462+0.001299j
[2025-09-20 00:15:26] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -61.821184-0.000805j
[2025-09-20 00:15:58] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -61.820182+0.001665j
[2025-09-20 00:16:30] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -61.838127-0.000770j
[2025-09-20 00:17:02] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -61.811703-0.005453j
[2025-09-20 00:17:34] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -61.820640+0.000632j
[2025-09-20 00:18:06] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -61.819744-0.004454j
[2025-09-20 00:18:38] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -61.826669+0.001504j
[2025-09-20 00:19:10] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -61.822971-0.001725j
[2025-09-20 00:19:42] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -61.828248+0.001128j
[2025-09-20 00:20:14] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -61.815746-0.002300j
[2025-09-20 00:20:46] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -61.826179+0.000630j
[2025-09-20 00:21:18] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -61.816942+0.002363j
[2025-09-20 00:21:50] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -61.816253+0.001023j
[2025-09-20 00:22:22] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -61.811063+0.001342j
[2025-09-20 00:22:53] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -61.819114+0.001913j
[2025-09-20 00:23:25] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -61.825591+0.002408j
[2025-09-20 00:23:57] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -61.823825-0.000997j
[2025-09-20 00:24:29] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -61.830240+0.000835j
[2025-09-20 00:25:01] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -61.816286+0.001523j
[2025-09-20 00:25:33] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -61.811402+0.004664j
[2025-09-20 00:26:05] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -61.814793+0.001905j
[2025-09-20 00:26:37] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -61.822347+0.000472j
[2025-09-20 00:27:09] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -61.822518+0.000173j
[2025-09-20 00:27:41] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -61.815617+0.000755j
[2025-09-20 00:28:13] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -61.806863+0.006644j
[2025-09-20 00:28:45] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -61.801255+0.004471j
[2025-09-20 00:28:45] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-20 00:29:17] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -61.811003-0.000292j
[2025-09-20 00:29:49] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -61.851574+0.000426j
[2025-09-20 00:30:21] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -61.826978+0.000172j
[2025-09-20 00:30:53] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -61.832820-0.003900j
[2025-09-20 00:31:25] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -61.828864+0.002721j
[2025-09-20 00:31:57] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -61.814725+0.008266j
[2025-09-20 00:32:29] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -61.807285-0.005801j
[2025-09-20 00:33:01] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -61.812398-0.001217j
[2025-09-20 00:33:33] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -61.830735+0.002507j
[2025-09-20 00:34:05] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -61.820304+0.001383j
[2025-09-20 00:34:37] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -61.828789-0.002967j
[2025-09-20 00:35:09] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -61.821280-0.000330j
[2025-09-20 00:35:41] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -61.813380-0.000237j
[2025-09-20 00:36:13] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -61.822490+0.003989j
[2025-09-20 00:36:45] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -61.826425+0.000521j
[2025-09-20 00:37:17] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -61.810520-0.001261j
[2025-09-20 00:37:49] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -61.829196+0.000397j
[2025-09-20 00:38:21] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -61.830340+0.001393j
[2025-09-20 00:38:53] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -61.825346+0.008903j
[2025-09-20 00:39:25] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -61.816489+0.000541j
[2025-09-20 00:39:57] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -61.807493-0.001153j
[2025-09-20 00:40:29] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -61.810661-0.000412j
[2025-09-20 00:41:01] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -61.808489-0.000541j
[2025-09-20 00:41:33] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -61.824821-0.001448j
[2025-09-20 00:42:05] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -61.834641-0.003574j
[2025-09-20 00:42:37] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -61.809195+0.002922j
[2025-09-20 00:43:09] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -61.813349-0.000883j
[2025-09-20 00:43:41] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -61.835100+0.001760j
[2025-09-20 00:44:13] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -61.833567+0.000379j
[2025-09-20 00:44:45] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -61.829932-0.001424j
[2025-09-20 00:45:17] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -61.827736-0.000127j
[2025-09-20 00:45:49] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -61.847816+0.007141j
[2025-09-20 00:46:21] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -61.835619+0.001714j
[2025-09-20 00:46:54] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -61.840622-0.001215j
[2025-09-20 00:47:26] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -61.824963+0.000333j
[2025-09-20 00:47:57] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -61.808234-0.001031j
[2025-09-20 00:48:29] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -61.814935-0.000152j
[2025-09-20 00:49:01] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -61.816327+0.000723j
[2025-09-20 00:49:34] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -61.832337+0.003591j
[2025-09-20 00:50:06] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -61.822607+0.001571j
[2025-09-20 00:50:38] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -61.829176+0.000583j
[2025-09-20 00:51:10] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -61.831197+0.001290j
[2025-09-20 00:51:42] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -61.820584-0.002693j
[2025-09-20 00:52:14] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -61.823126-0.001849j
[2025-09-20 00:52:46] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -61.837328+0.000663j
[2025-09-20 00:52:46] RESTART #1 | Period: 300
[2025-09-20 00:53:18] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -61.818894+0.002087j
[2025-09-20 00:53:50] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -61.825332+0.007068j
[2025-09-20 00:54:22] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -61.816871-0.000082j
[2025-09-20 00:54:54] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -61.828758+0.002100j
[2025-09-20 00:55:26] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -61.835996+0.002269j
[2025-09-20 00:55:58] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -61.800862+0.002294j
[2025-09-20 00:56:30] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -61.826162-0.004938j
[2025-09-20 00:57:02] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -61.830175-0.002014j
[2025-09-20 00:57:34] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -61.821355-0.001576j
[2025-09-20 00:58:07] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -61.826068+0.000113j
[2025-09-20 00:58:39] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -61.816260+0.001329j
[2025-09-20 00:59:11] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -61.810530+0.000082j
[2025-09-20 00:59:43] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -61.817875-0.001133j
[2025-09-20 01:00:15] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -61.830140-0.003995j
[2025-09-20 01:00:47] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -61.800155+0.001561j
[2025-09-20 01:01:19] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -61.797641-0.000843j
[2025-09-20 01:01:51] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -61.813741+0.002141j
[2025-09-20 01:02:24] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -61.830191+0.002529j
[2025-09-20 01:02:56] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -61.833542+0.000693j
[2025-09-20 01:03:28] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -61.830754-0.001987j
[2025-09-20 01:04:00] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -61.816755+0.003252j
[2025-09-20 01:04:32] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -61.821544-0.003714j
[2025-09-20 01:05:04] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -61.808056+0.003018j
[2025-09-20 01:05:36] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -61.817459+0.001066j
[2025-09-20 01:06:08] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -61.831398-0.001621j
[2025-09-20 01:06:40] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -61.808136+0.001502j
[2025-09-20 01:07:12] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -61.807256+0.000098j
[2025-09-20 01:07:44] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -61.817642+0.003631j
[2025-09-20 01:08:16] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -61.819074-0.000958j
[2025-09-20 01:08:48] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -61.824224+0.000735j
[2025-09-20 01:09:20] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -61.826907+0.002945j
[2025-09-20 01:09:52] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -61.818710-0.000341j
[2025-09-20 01:10:24] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -61.829534+0.000679j
[2025-09-20 01:10:57] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -61.805443-0.000495j
[2025-09-20 01:11:29] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -61.827239-0.003665j
[2025-09-20 01:12:01] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -61.841356+0.000662j
[2025-09-20 01:12:33] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -61.820414-0.000918j
[2025-09-20 01:13:05] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -61.827919+0.002204j
[2025-09-20 01:13:36] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -61.817615+0.001536j
[2025-09-20 01:14:08] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -61.803688+0.001694j
[2025-09-20 01:14:40] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -61.825027+0.000192j
[2025-09-20 01:15:12] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -61.815384-0.004795j
[2025-09-20 01:15:44] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -61.838296-0.001382j
[2025-09-20 01:16:16] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -61.811854+0.004375j
[2025-09-20 01:16:48] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -61.811844+0.000904j
[2025-09-20 01:17:20] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -61.835036-0.001960j
[2025-09-20 01:17:52] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -61.828997-0.001576j
[2025-09-20 01:18:24] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -61.826964+0.001529j
[2025-09-20 01:18:56] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -61.821616+0.001723j
[2025-09-20 01:19:28] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -61.807422-0.000042j
[2025-09-20 01:20:00] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -61.808893-0.001409j
[2025-09-20 01:20:32] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -61.789450-0.000367j
[2025-09-20 01:21:03] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -61.809506+0.001385j
[2025-09-20 01:21:35] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -61.801600+0.002878j
[2025-09-20 01:22:07] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -61.806512-0.000274j
[2025-09-20 01:22:39] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -61.822598-0.002707j
[2025-09-20 01:23:11] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -61.801234+0.005177j
[2025-09-20 01:23:43] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -61.789656-0.002778j
[2025-09-20 01:24:15] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -61.811447-0.000189j
[2025-09-20 01:24:47] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -61.808700-0.005113j
[2025-09-20 01:24:47] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-20 01:25:19] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -61.807511+0.002823j
[2025-09-20 01:25:51] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -61.819031-0.002721j
[2025-09-20 01:26:23] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -61.825716+0.001670j
[2025-09-20 01:26:55] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -61.821677+0.001365j
[2025-09-20 01:27:27] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -61.826690-0.002349j
[2025-09-20 01:27:59] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -61.825967-0.001416j
[2025-09-20 01:28:31] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -61.816495+0.003155j
[2025-09-20 01:29:03] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -61.823440-0.003681j
[2025-09-20 01:29:35] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -61.820777-0.002871j
[2025-09-20 01:30:07] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -61.813052+0.003072j
[2025-09-20 01:30:39] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -61.827515-0.002053j
[2025-09-20 01:31:11] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -61.819033-0.001013j
[2025-09-20 01:31:43] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -61.818503+0.000548j
[2025-09-20 01:32:15] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -61.806867-0.000304j
[2025-09-20 01:32:47] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -61.804216+0.009321j
[2025-09-20 01:33:19] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -61.820944-0.002515j
[2025-09-20 01:33:51] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -61.822358+0.001222j
[2025-09-20 01:34:23] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -61.855869-0.018019j
[2025-09-20 01:34:55] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -61.839306+0.003961j
[2025-09-20 01:35:27] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -61.831526+0.001776j
[2025-09-20 01:35:59] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -61.841950+0.001323j
[2025-09-20 01:36:31] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -61.813340-0.000234j
[2025-09-20 01:37:03] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -61.827210+0.000894j
[2025-09-20 01:37:36] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -61.812131-0.001674j
[2025-09-20 01:38:08] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -61.822911+0.000416j
[2025-09-20 01:38:39] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -61.828724+0.000305j
[2025-09-20 01:39:12] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -61.832078+0.002653j
[2025-09-20 01:39:44] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -61.817405-0.001335j
[2025-09-20 01:40:16] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -61.833122+0.004221j
[2025-09-20 01:40:48] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -61.807160+0.003209j
[2025-09-20 01:41:20] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -61.819608+0.002398j
[2025-09-20 01:41:52] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -61.815104-0.001893j
[2025-09-20 01:42:24] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -61.814503+0.002818j
[2025-09-20 01:42:56] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -61.822410+0.002649j
[2025-09-20 01:43:28] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -61.830788+0.000918j
[2025-09-20 01:44:00] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -61.826766-0.000678j
[2025-09-20 01:44:32] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -61.827157-0.001548j
[2025-09-20 01:45:04] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -61.813081+0.000773j
[2025-09-20 01:45:36] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -61.829140-0.000633j
[2025-09-20 01:46:08] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -61.841527+0.000008j
[2025-09-20 01:46:40] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -61.842033-0.003480j
[2025-09-20 01:47:12] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -61.809500+0.000657j
[2025-09-20 01:47:44] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -61.805831+0.001774j
[2025-09-20 01:48:16] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -61.815202-0.004023j
[2025-09-20 01:48:48] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -61.825249+0.003734j
[2025-09-20 01:49:20] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -61.832186+0.003596j
[2025-09-20 01:49:52] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -61.809542-0.001553j
[2025-09-20 01:50:24] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -61.827587+0.001814j
[2025-09-20 01:50:56] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -61.843860-0.000656j
[2025-09-20 01:51:29] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -61.830285+0.000684j
[2025-09-20 01:52:01] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -61.826087+0.002372j
[2025-09-20 01:52:33] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -61.825337+0.006153j
[2025-09-20 01:53:05] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -61.819984-0.006467j
[2025-09-20 01:53:37] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -61.816987-0.001123j
[2025-09-20 01:54:09] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -61.811435+0.000662j
[2025-09-20 01:54:41] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -61.828282-0.002107j
[2025-09-20 01:55:13] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -61.818195+0.001221j
[2025-09-20 01:55:45] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -61.836182-0.001703j
[2025-09-20 01:56:17] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -61.805472-0.003797j
[2025-09-20 01:56:49] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -61.834959+0.002065j
[2025-09-20 01:57:21] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -61.815308-0.001445j
[2025-09-20 01:57:53] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -61.827180-0.002716j
[2025-09-20 01:58:25] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -61.811162+0.001271j
[2025-09-20 01:58:57] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -61.833923-0.002071j
[2025-09-20 01:59:29] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -61.825343-0.002911j
[2025-09-20 02:00:01] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -61.829467+0.002391j
[2025-09-20 02:00:33] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -61.816413-0.001574j
[2025-09-20 02:01:05] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -61.833388-0.000430j
[2025-09-20 02:01:37] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -61.826680-0.000627j
[2025-09-20 02:02:09] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -61.819347-0.003083j
[2025-09-20 02:02:41] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -61.821111-0.001627j
[2025-09-20 02:03:13] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -61.826784-0.000595j
[2025-09-20 02:03:45] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -61.827125-0.002403j
[2025-09-20 02:04:17] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -61.827582-0.003092j
[2025-09-20 02:04:49] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -61.816277-0.001589j
[2025-09-20 02:05:21] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -61.821825+0.002136j
[2025-09-20 02:05:54] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -61.836934+0.001174j
[2025-09-20 02:06:26] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -61.808477+0.004156j
[2025-09-20 02:06:58] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -61.838519+0.001337j
[2025-09-20 02:07:30] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -61.818318-0.003078j
[2025-09-20 02:08:02] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -61.823464+0.002587j
[2025-09-20 02:08:34] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -61.823535-0.003832j
[2025-09-20 02:09:06] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -61.824771-0.001038j
[2025-09-20 02:09:38] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -61.834806-0.001759j
[2025-09-20 02:10:10] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -61.824670+0.002446j
[2025-09-20 02:10:42] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -61.835329-0.000291j
[2025-09-20 02:11:14] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -61.814222+0.003150j
[2025-09-20 02:11:46] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -61.822940-0.000550j
[2025-09-20 02:12:18] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -61.822346-0.001199j
[2025-09-20 02:12:50] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -61.827665-0.001746j
[2025-09-20 02:13:22] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -61.840831+0.001402j
[2025-09-20 02:13:54] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -61.834531-0.002504j
[2025-09-20 02:14:26] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -61.822607-0.003474j
[2025-09-20 02:14:58] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -61.821574-0.002745j
[2025-09-20 02:15:31] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -61.810386+0.001462j
[2025-09-20 02:16:03] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -61.824337-0.001345j
[2025-09-20 02:16:35] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -61.841217-0.003339j
[2025-09-20 02:17:07] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -61.812766-0.001586j
[2025-09-20 02:17:39] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -61.821126-0.002978j
[2025-09-20 02:18:11] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -61.817194-0.002885j
[2025-09-20 02:18:43] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -61.826190-0.000016j
[2025-09-20 02:19:15] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -61.791642+0.002103j
[2025-09-20 02:19:47] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -61.842327+0.001672j
[2025-09-20 02:20:19] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -61.840468-0.001995j
[2025-09-20 02:20:51] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -61.820121+0.001503j
[2025-09-20 02:20:51] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-20 02:21:23] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -61.810562+0.004676j
[2025-09-20 02:21:55] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -61.827678-0.000426j
[2025-09-20 02:22:27] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -61.839312-0.004645j
[2025-09-20 02:22:59] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -61.831101+0.000108j
[2025-09-20 02:23:31] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -61.824487+0.001358j
[2025-09-20 02:24:03] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -61.809071-0.002651j
[2025-09-20 02:24:35] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -61.813215+0.000454j
[2025-09-20 02:25:07] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -61.835643+0.002440j
[2025-09-20 02:25:39] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -61.817356+0.001897j
[2025-09-20 02:26:11] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -61.820215-0.002701j
[2025-09-20 02:26:43] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -61.830885+0.002974j
[2025-09-20 02:27:15] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -61.813032+0.004219j
[2025-09-20 02:27:47] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -61.821852+0.003849j
[2025-09-20 02:28:19] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -61.820580+0.001421j
[2025-09-20 02:28:51] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -61.816971+0.003036j
[2025-09-20 02:29:23] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -61.826087-0.000255j
[2025-09-20 02:29:55] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -61.830568-0.002996j
[2025-09-20 02:30:27] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -61.816180+0.000862j
[2025-09-20 02:30:59] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -61.832199-0.000185j
[2025-09-20 02:31:31] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -61.811630-0.004050j
[2025-09-20 02:32:03] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -61.836803-0.003347j
[2025-09-20 02:32:35] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -61.812194+0.000855j
[2025-09-20 02:33:07] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -61.785603+0.006798j
[2025-09-20 02:33:39] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -61.834009-0.000677j
[2025-09-20 02:34:11] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -61.821974+0.000499j
[2025-09-20 02:34:44] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -61.796921-0.005896j
[2025-09-20 02:35:16] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -61.832128+0.002802j
[2025-09-20 02:35:48] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -61.818791+0.000436j
[2025-09-20 02:36:20] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -61.827416+0.001002j
[2025-09-20 02:36:52] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -61.835379-0.000214j
[2025-09-20 02:37:24] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -61.820260-0.002312j
[2025-09-20 02:37:56] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -61.820152+0.006222j
[2025-09-20 02:38:28] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -61.821333-0.002846j
[2025-09-20 02:39:00] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -61.807552+0.003701j
[2025-09-20 02:39:32] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -61.815967+0.005294j
[2025-09-20 02:40:04] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -61.831438-0.000159j
[2025-09-20 02:40:36] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -61.823719-0.000348j
[2025-09-20 02:41:08] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -61.826077+0.000406j
[2025-09-20 02:41:40] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -61.812031-0.002289j
[2025-09-20 02:42:12] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -61.822810+0.003412j
[2025-09-20 02:42:44] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -61.823452+0.001342j
[2025-09-20 02:43:17] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -61.824929+0.002240j
[2025-09-20 02:43:49] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -61.820414-0.000958j
[2025-09-20 02:44:21] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -61.817178-0.000080j
[2025-09-20 02:44:53] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -61.820994+0.002299j
[2025-09-20 02:45:25] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -61.830338+0.000931j
[2025-09-20 02:45:57] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -61.838585+0.000464j
[2025-09-20 02:46:29] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -61.832311+0.006629j
[2025-09-20 02:47:01] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -61.811937-0.003196j
[2025-09-20 02:47:33] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -61.810888-0.002322j
[2025-09-20 02:48:05] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -61.835188+0.001966j
[2025-09-20 02:48:37] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -61.820519+0.001178j
[2025-09-20 02:49:09] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -61.832990+0.002992j
[2025-09-20 02:49:41] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -61.816424+0.000195j
[2025-09-20 02:50:13] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -61.829615-0.000880j
[2025-09-20 02:50:45] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -61.818959-0.003642j
[2025-09-20 02:51:17] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -61.830578+0.000084j
[2025-09-20 02:51:49] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -61.826943-0.003624j
[2025-09-20 02:52:21] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -61.842262+0.000870j
[2025-09-20 02:52:53] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -61.830081+0.004586j
[2025-09-20 02:53:25] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -61.822750+0.004327j
[2025-09-20 02:53:57] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -61.833872+0.007556j
[2025-09-20 02:54:29] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -61.824348+0.002574j
[2025-09-20 02:55:01] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -61.824024-0.001189j
[2025-09-20 02:55:33] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -61.815229-0.006086j
[2025-09-20 02:56:05] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -61.826708-0.004397j
[2025-09-20 02:56:38] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -61.810543-0.000803j
[2025-09-20 02:57:10] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -61.836635-0.001665j
[2025-09-20 02:57:42] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -61.842587-0.001513j
[2025-09-20 02:58:14] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -61.806714+0.000294j
[2025-09-20 02:58:46] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -61.821108-0.003506j
[2025-09-20 02:59:18] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -61.799594-0.005003j
[2025-09-20 02:59:50] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -61.818166-0.002872j
[2025-09-20 03:00:22] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -61.838706-0.008991j
[2025-09-20 03:00:54] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -61.826291-0.004156j
[2025-09-20 03:01:26] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -61.836221-0.002331j
[2025-09-20 03:01:58] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -61.832440-0.003593j
[2025-09-20 03:02:30] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -61.810650+0.002924j
[2025-09-20 03:03:02] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -61.812695-0.003203j
[2025-09-20 03:03:34] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -61.831427+0.010220j
[2025-09-20 03:04:06] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -61.837423-0.001385j
[2025-09-20 03:04:38] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -61.827546-0.000159j
[2025-09-20 03:05:10] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -61.822814+0.001202j
[2025-09-20 03:05:42] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -61.828717-0.000274j
[2025-09-20 03:06:14] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -61.831137-0.000014j
[2025-09-20 03:06:47] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -61.819839-0.001215j
[2025-09-20 03:07:18] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -61.814404+0.001722j
[2025-09-20 03:07:51] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -61.840279-0.006186j
[2025-09-20 03:08:23] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -61.828535+0.004000j
[2025-09-20 03:08:55] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -61.820471-0.000196j
[2025-09-20 03:09:27] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -61.817209-0.001134j
[2025-09-20 03:09:59] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -61.837371-0.004668j
[2025-09-20 03:10:31] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -61.839635-0.002638j
[2025-09-20 03:11:03] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -61.820721+0.000372j
[2025-09-20 03:11:35] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -61.817497-0.002562j
[2025-09-20 03:12:07] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -61.836877+0.000059j
[2025-09-20 03:12:39] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -61.827439-0.008687j
[2025-09-20 03:13:11] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -61.815370-0.001329j
[2025-09-20 03:13:43] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -61.816068+0.002929j
[2025-09-20 03:14:15] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -61.839545-0.007476j
[2025-09-20 03:14:47] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -61.832847-0.000625j
[2025-09-20 03:15:19] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -61.812084+0.003906j
[2025-09-20 03:15:51] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -61.830996+0.003537j
[2025-09-20 03:16:23] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -61.836710-0.001865j
[2025-09-20 03:16:56] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -61.825303+0.000387j
[2025-09-20 03:16:56] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-20 03:17:28] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -61.844347+0.001298j
[2025-09-20 03:18:00] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -61.822529-0.001588j
[2025-09-20 03:18:32] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -61.814476+0.000352j
[2025-09-20 03:19:04] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -61.826888+0.002034j
[2025-09-20 03:19:36] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -61.824505-0.005141j
[2025-09-20 03:20:08] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -61.819995-0.003343j
[2025-09-20 03:20:40] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -61.825947+0.010959j
[2025-09-20 03:21:12] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -61.821020-0.000639j
[2025-09-20 03:21:44] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -61.821451-0.002430j
[2025-09-20 03:22:16] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -61.835139-0.000350j
[2025-09-20 03:22:48] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -61.821147-0.001177j
[2025-09-20 03:23:20] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -61.832446+0.005291j
[2025-09-20 03:23:52] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -61.831202-0.000354j
[2025-09-20 03:24:24] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -61.831105+0.002192j
[2025-09-20 03:24:56] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -61.831347+0.001639j
[2025-09-20 03:25:28] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -61.807999+0.000331j
[2025-09-20 03:26:00] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -61.820552-0.000370j
[2025-09-20 03:26:32] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -61.823508+0.000846j
[2025-09-20 03:27:04] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -61.828255+0.002550j
[2025-09-20 03:27:36] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -61.818737+0.001160j
[2025-09-20 03:28:08] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -61.830559-0.002175j
[2025-09-20 03:28:40] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -61.834901+0.000292j
[2025-09-20 03:29:12] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -61.823828-0.000546j
[2025-09-20 03:29:43] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -61.835048+0.003356j
[2025-09-20 03:30:15] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -61.817467+0.003133j
[2025-09-20 03:30:47] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -61.825692+0.001640j
[2025-09-20 03:31:19] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -61.836576-0.007014j
[2025-09-20 03:31:51] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -61.809127-0.001016j
[2025-09-20 03:32:23] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -61.814302-0.002799j
[2025-09-20 03:32:55] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -61.836433+0.000538j
[2025-09-20 03:32:55] RESTART #2 | Period: 600
[2025-09-20 03:33:27] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -61.818240-0.000357j
[2025-09-20 03:33:59] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -61.813652-0.002505j
[2025-09-20 03:34:31] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -61.836108-0.001119j
[2025-09-20 03:35:03] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -61.812707+0.000578j
[2025-09-20 03:35:35] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -61.822448-0.002101j
[2025-09-20 03:36:06] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -61.832658-0.002684j
[2025-09-20 03:36:38] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -61.818629+0.000793j
[2025-09-20 03:37:10] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -61.825548-0.001990j
[2025-09-20 03:37:42] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -61.830222+0.000025j
[2025-09-20 03:38:14] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -61.813657+0.000428j
[2025-09-20 03:38:46] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -61.825045-0.000783j
[2025-09-20 03:39:18] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -61.825116+0.001671j
[2025-09-20 03:39:50] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -61.832712+0.000182j
[2025-09-20 03:40:22] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -61.808240-0.001770j
[2025-09-20 03:40:54] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -61.818576-0.001596j
[2025-09-20 03:41:25] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -61.844991-0.004063j
[2025-09-20 03:41:57] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -61.814428-0.000009j
[2025-09-20 03:42:29] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -61.830680+0.000947j
[2025-09-20 03:43:01] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -61.826244+0.000728j
[2025-09-20 03:43:33] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -61.825967+0.002819j
[2025-09-20 03:44:05] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -61.820773+0.002282j
[2025-09-20 03:44:37] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -61.824809-0.002422j
[2025-09-20 03:45:09] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -61.836212+0.000406j
[2025-09-20 03:45:41] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -61.834676-0.000334j
[2025-09-20 03:46:12] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -61.823941+0.001206j
[2025-09-20 03:46:44] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -61.831462-0.003963j
[2025-09-20 03:47:16] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -61.837093-0.001444j
[2025-09-20 03:47:48] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -61.825827-0.007649j
[2025-09-20 03:48:21] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -61.819403+0.000269j
[2025-09-20 03:48:53] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -61.852400+0.001868j
[2025-09-20 03:49:25] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -61.825444-0.003826j
[2025-09-20 03:49:57] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -61.824281+0.003053j
[2025-09-20 03:50:29] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -61.829144+0.000997j
[2025-09-20 03:51:01] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -61.810783-0.002660j
[2025-09-20 03:51:33] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -61.819689-0.000644j
[2025-09-20 03:52:05] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -61.841721-0.004736j
[2025-09-20 03:52:37] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -61.815704-0.002716j
[2025-09-20 03:53:09] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -61.823542-0.006395j
[2025-09-20 03:53:41] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -61.829326+0.005558j
[2025-09-20 03:54:13] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -61.824888+0.002792j
[2025-09-20 03:54:45] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -61.828192+0.001212j
[2025-09-20 03:55:17] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -61.828040+0.001721j
[2025-09-20 03:55:49] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -61.826149-0.001599j
[2025-09-20 03:56:21] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -61.837191-0.001350j
[2025-09-20 03:56:53] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -61.827860+0.000432j
[2025-09-20 03:57:25] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -61.837162+0.000892j
[2025-09-20 03:57:58] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -61.837736+0.000541j
[2025-09-20 03:58:30] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -61.824735-0.000514j
[2025-09-20 03:59:02] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -61.834913-0.000048j
[2025-09-20 03:59:34] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -61.842946+0.001975j
[2025-09-20 04:00:06] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -61.822998-0.003320j
[2025-09-20 04:00:38] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -61.824431+0.000815j
[2025-09-20 04:01:10] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -61.822239-0.001026j
[2025-09-20 04:01:42] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -61.817630+0.000364j
[2025-09-20 04:02:14] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -61.827434-0.000523j
[2025-09-20 04:02:46] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -61.825277-0.001303j
[2025-09-20 04:03:18] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -61.820733+0.002834j
[2025-09-20 04:03:50] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -61.829177+0.001334j
[2025-09-20 04:04:22] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -61.822995-0.000424j
[2025-09-20 04:04:54] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -61.811705-0.003369j
[2025-09-20 04:05:26] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -61.811354+0.004920j
[2025-09-20 04:05:59] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -61.814484+0.000605j
[2025-09-20 04:06:30] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -61.819072-0.001051j
[2025-09-20 04:07:02] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -61.810891-0.001880j
[2025-09-20 04:07:35] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -61.810346-0.002803j
[2025-09-20 04:08:07] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -61.821717-0.003166j
[2025-09-20 04:08:38] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -61.821070-0.001902j
[2025-09-20 04:09:10] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -61.818510-0.000704j
[2025-09-20 04:09:42] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -61.835600+0.000224j
[2025-09-20 04:10:14] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -61.832791+0.001541j
[2025-09-20 04:10:46] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -61.814344-0.002076j
[2025-09-20 04:11:18] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -61.799584+0.001389j
[2025-09-20 04:11:50] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -61.829018+0.002971j
[2025-09-20 04:12:22] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -61.819484-0.003149j
[2025-09-20 04:12:54] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -61.825865+0.002391j
[2025-09-20 04:12:54] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-20 04:13:26] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -61.822194-0.001989j
[2025-09-20 04:13:58] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -61.809154-0.003888j
[2025-09-20 04:14:29] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -61.818678+0.002088j
[2025-09-20 04:15:01] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -61.808363+0.003676j
[2025-09-20 04:15:33] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -61.819290+0.002077j
[2025-09-20 04:16:05] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -61.822165-0.001124j
[2025-09-20 04:16:37] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -61.824337+0.000340j
[2025-09-20 04:17:09] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -61.807408+0.001125j
[2025-09-20 04:17:41] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -61.830580+0.002691j
[2025-09-20 04:18:13] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -61.840126+0.000013j
[2025-09-20 04:18:45] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -61.825998-0.003554j
[2025-09-20 04:19:17] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -61.822363+0.006450j
[2025-09-20 04:19:49] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -61.844567+0.000081j
[2025-09-20 04:20:21] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -61.815407-0.002087j
[2025-09-20 04:20:53] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -61.815094-0.001047j
[2025-09-20 04:21:25] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -61.815954+0.000987j
[2025-09-20 04:21:57] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -61.840102-0.000629j
[2025-09-20 04:22:29] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -61.830095+0.000073j
[2025-09-20 04:23:01] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -61.820846-0.003258j
[2025-09-20 04:23:33] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -61.844698-0.006460j
[2025-09-20 04:24:05] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -61.813692+0.002961j
[2025-09-20 04:24:38] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -61.821519-0.002437j
[2025-09-20 04:25:09] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -61.834516+0.000584j
[2025-09-20 04:25:42] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -61.827298-0.001168j
[2025-09-20 04:26:14] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -61.823843+0.004357j
[2025-09-20 04:26:46] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -61.817977-0.000847j
[2025-09-20 04:27:18] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -61.825454-0.001059j
[2025-09-20 04:27:50] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -61.833378-0.002551j
[2025-09-20 04:28:22] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -61.857844+0.001602j
[2025-09-20 04:28:54] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -61.811143+0.001407j
[2025-09-20 04:29:26] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -61.805767+0.001268j
[2025-09-20 04:29:58] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -61.832331-0.004077j
[2025-09-20 04:30:30] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -61.839206+0.001687j
[2025-09-20 04:31:02] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -61.831731-0.001978j
[2025-09-20 04:31:34] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -61.825351+0.002131j
[2025-09-20 04:32:06] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -61.837152+0.004339j
[2025-09-20 04:32:38] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -61.823102+0.003528j
[2025-09-20 04:33:10] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -61.834321-0.000639j
[2025-09-20 04:33:42] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -61.808444-0.001335j
[2025-09-20 04:34:14] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -61.826949-0.001686j
[2025-09-20 04:34:46] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -61.833311-0.002594j
[2025-09-20 04:35:18] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -61.826388-0.002864j
[2025-09-20 04:35:50] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -61.827673+0.000181j
[2025-09-20 04:36:22] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -61.850842-0.000988j
[2025-09-20 04:36:54] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -61.817604-0.000637j
[2025-09-20 04:37:26] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -61.822425-0.003074j
[2025-09-20 04:37:58] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -61.819310+0.002098j
[2025-09-20 04:38:30] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -61.807825+0.000948j
[2025-09-20 04:39:02] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -61.825602-0.003300j
[2025-09-20 04:39:34] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -61.821426-0.000312j
[2025-09-20 04:40:07] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -61.821772-0.001996j
[2025-09-20 04:40:39] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -61.849261+0.001811j
[2025-09-20 04:41:11] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -61.842599+0.002769j
[2025-09-20 04:41:43] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -61.835264-0.001035j
[2025-09-20 04:42:15] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -61.822555+0.001096j
[2025-09-20 04:42:47] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -61.827315-0.003143j
[2025-09-20 04:43:19] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -61.828273-0.001490j
[2025-09-20 04:43:51] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -61.820131-0.005120j
[2025-09-20 04:44:23] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -61.815736-0.000890j
[2025-09-20 04:44:55] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -61.831494+0.003910j
[2025-09-20 04:45:27] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -61.836691-0.001059j
[2025-09-20 04:45:59] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -61.817733-0.000563j
[2025-09-20 04:46:31] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -61.806784+0.002427j
[2025-09-20 04:47:03] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -61.830934-0.000297j
[2025-09-20 04:47:35] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -61.820352+0.000120j
[2025-09-20 04:48:07] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -61.817559-0.001015j
[2025-09-20 04:48:39] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -61.829580+0.003682j
[2025-09-20 04:49:12] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -61.820515+0.001571j
[2025-09-20 04:49:43] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -61.825547+0.000743j
[2025-09-20 04:50:15] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -61.809678-0.001143j
[2025-09-20 04:50:47] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -61.837224-0.002973j
[2025-09-20 04:51:19] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -61.818732+0.001100j
[2025-09-20 04:51:51] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -61.830685+0.001994j
[2025-09-20 04:52:23] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -61.813374-0.003224j
[2025-09-20 04:52:55] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -61.831757+0.001873j
[2025-09-20 04:53:27] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -61.827472-0.000246j
[2025-09-20 04:53:59] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -61.832090-0.002154j
[2025-09-20 04:54:31] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -61.824487+0.000284j
[2025-09-20 04:55:03] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -61.831389+0.002432j
[2025-09-20 04:55:35] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -61.835096+0.000788j
[2025-09-20 04:56:07] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -61.829183-0.002670j
[2025-09-20 04:56:38] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -61.822221-0.001956j
[2025-09-20 04:57:10] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -61.823025+0.000295j
[2025-09-20 04:57:42] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -61.825447+0.000681j
[2025-09-20 04:58:14] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -61.809212-0.008151j
[2025-09-20 04:58:46] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -61.841959+0.004721j
[2025-09-20 04:59:18] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -61.821287-0.005156j
[2025-09-20 04:59:50] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -61.826262-0.001106j
[2025-09-20 05:00:22] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -61.820600+0.000874j
[2025-09-20 05:00:54] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -61.834728-0.002580j
[2025-09-20 05:01:26] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -61.835972-0.002194j
[2025-09-20 05:01:58] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -61.820214+0.001690j
[2025-09-20 05:02:30] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -61.832985-0.000099j
[2025-09-20 05:03:02] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -61.831798-0.001493j
[2025-09-20 05:03:34] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -61.810498+0.003871j
[2025-09-20 05:04:06] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -61.838158+0.000274j
[2025-09-20 05:04:39] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -61.820315+0.000637j
[2025-09-20 05:05:11] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -61.840837+0.003636j
[2025-09-20 05:05:43] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -61.816786-0.003113j
[2025-09-20 05:06:15] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -61.823681-0.001130j
[2025-09-20 05:06:46] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -61.829878-0.000612j
[2025-09-20 05:07:19] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -61.840065-0.000512j
[2025-09-20 05:07:51] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -61.828965+0.000500j
[2025-09-20 05:08:23] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -61.833437+0.000634j
[2025-09-20 05:08:54] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -61.829264+0.000663j
[2025-09-20 05:08:54] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-20 05:09:26] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -61.822154+0.000316j
[2025-09-20 05:09:58] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -61.829158+0.000292j
[2025-09-20 05:10:30] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -61.841153+0.002974j
[2025-09-20 05:11:02] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -61.824408+0.001890j
[2025-09-20 05:11:34] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -61.833754+0.001122j
[2025-09-20 05:12:06] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -61.825612+0.000326j
[2025-09-20 05:12:38] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -61.841629+0.002916j
[2025-09-20 05:13:10] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -61.826285-0.001873j
[2025-09-20 05:13:42] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -61.823158-0.002741j
[2025-09-20 05:14:14] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -61.832272+0.000960j
[2025-09-20 05:14:45] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -61.823805+0.000471j
[2025-09-20 05:15:17] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -61.806754-0.000908j
[2025-09-20 05:15:49] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -61.845765+0.002105j
[2025-09-20 05:16:21] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -61.824894+0.005552j
[2025-09-20 05:16:53] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -61.825514-0.003045j
[2025-09-20 05:17:25] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -61.834959+0.002140j
[2025-09-20 05:17:57] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -61.832389-0.002004j
[2025-09-20 05:18:29] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -61.799938-0.000343j
[2025-09-20 05:19:01] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -61.845645-0.002061j
[2025-09-20 05:19:32] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -61.813392-0.001193j
[2025-09-20 05:20:04] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -61.841629+0.003300j
[2025-09-20 05:20:36] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -61.830184-0.000394j
[2025-09-20 05:21:08] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -61.832306-0.002926j
[2025-09-20 05:21:40] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -61.830514-0.000552j
[2025-09-20 05:22:12] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -61.825730-0.001885j
[2025-09-20 05:22:44] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -61.807177+0.000141j
[2025-09-20 05:23:16] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -61.822626+0.002753j
[2025-09-20 05:23:48] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -61.834715-0.003753j
[2025-09-20 05:24:20] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -61.816873+0.001222j
[2025-09-20 05:24:52] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -61.828201+0.000681j
[2025-09-20 05:25:23] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -61.843269-0.000455j
[2025-09-20 05:25:55] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -61.799713-0.000201j
[2025-09-20 05:26:27] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -61.837527-0.000560j
[2025-09-20 05:26:59] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -61.833080-0.003558j
[2025-09-20 05:27:31] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -61.826160-0.001240j
[2025-09-20 05:28:03] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -61.846179-0.000993j
[2025-09-20 05:28:35] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -61.830544+0.000031j
[2025-09-20 05:29:07] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -61.824290-0.002522j
[2025-09-20 05:29:39] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -61.825694-0.001844j
[2025-09-20 05:30:11] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -61.827719+0.002795j
[2025-09-20 05:30:43] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -61.842511-0.000173j
[2025-09-20 05:31:15] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -61.839446+0.000705j
[2025-09-20 05:31:47] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -61.837448-0.000214j
[2025-09-20 05:32:18] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -61.817118-0.001272j
[2025-09-20 05:32:50] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -61.806905+0.000322j
[2025-09-20 05:33:22] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -61.837317-0.001400j
[2025-09-20 05:33:54] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -61.812073+0.004473j
[2025-09-20 05:34:26] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -61.817986-0.002119j
[2025-09-20 05:34:58] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -61.826670-0.001156j
[2025-09-20 05:35:30] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -61.844619+0.002433j
[2025-09-20 05:36:02] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -61.822546-0.000851j
[2025-09-20 05:36:33] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -61.823780+0.000537j
[2025-09-20 05:37:05] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -61.837886+0.000659j
[2025-09-20 05:37:37] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -61.841952-0.001646j
[2025-09-20 05:38:09] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -61.814048+0.012170j
[2025-09-20 05:38:41] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -61.812426-0.001021j
[2025-09-20 05:39:13] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -61.832393-0.006850j
[2025-09-20 05:39:45] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -61.838288-0.000164j
[2025-09-20 05:40:17] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -61.836266+0.000359j
[2025-09-20 05:40:49] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -61.831086+0.003942j
[2025-09-20 05:41:21] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -61.837082+0.001732j
[2025-09-20 05:41:53] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -61.829361+0.003028j
[2025-09-20 05:42:25] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -61.821927-0.001180j
[2025-09-20 05:42:57] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -61.803226-0.000092j
[2025-09-20 05:43:29] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -61.823142-0.000942j
[2025-09-20 05:44:01] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -61.825695+0.003245j
[2025-09-20 05:44:33] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -61.815167+0.002816j
[2025-09-20 05:45:05] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -61.812050-0.000314j
[2025-09-20 05:45:37] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -61.841596-0.000344j
[2025-09-20 05:46:10] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -61.827722-0.005848j
[2025-09-20 05:46:42] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -61.840994-0.001523j
[2025-09-20 05:47:14] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -61.816290-0.000513j
[2025-09-20 05:47:46] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -61.829815+0.000511j
[2025-09-20 05:48:18] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -61.822127-0.001866j
[2025-09-20 05:48:50] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -61.827430+0.001364j
[2025-09-20 05:49:22] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -61.821922-0.000000j
[2025-09-20 05:49:54] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -61.830673-0.001737j
[2025-09-20 05:50:26] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -61.836977-0.001924j
[2025-09-20 05:50:58] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -61.823042+0.003626j
[2025-09-20 05:51:30] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -61.816676+0.001960j
[2025-09-20 05:52:02] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -61.824766+0.004457j
[2025-09-20 05:52:34] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -61.838945+0.001468j
[2025-09-20 05:53:06] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -61.829738-0.003361j
[2025-09-20 05:53:38] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -61.842619+0.001101j
[2025-09-20 05:54:10] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -61.836066-0.002999j
[2025-09-20 05:54:42] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -61.831939-0.001485j
[2025-09-20 05:55:14] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -61.827713+0.001980j
[2025-09-20 05:55:46] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -61.824179-0.000571j
[2025-09-20 05:56:18] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -61.824592+0.002096j
[2025-09-20 05:56:50] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -61.811036+0.001601j
[2025-09-20 05:57:22] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -61.846022+0.002309j
[2025-09-20 05:57:54] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -61.824600+0.002804j
[2025-09-20 05:58:26] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -61.827679-0.000171j
[2025-09-20 05:58:58] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -61.834504+0.000423j
[2025-09-20 05:59:30] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -61.819876-0.000999j
[2025-09-20 06:00:03] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -61.837083+0.001799j
[2025-09-20 06:00:35] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -61.824238+0.000665j
[2025-09-20 06:01:07] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -61.826418+0.001321j
[2025-09-20 06:01:39] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -61.837168-0.002317j
[2025-09-20 06:02:11] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -61.838479+0.001304j
[2025-09-20 06:02:43] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -61.826935+0.000548j
[2025-09-20 06:03:15] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -61.826150+0.000786j
[2025-09-20 06:03:47] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -61.819043+0.004596j
[2025-09-20 06:04:19] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -61.829746-0.000481j
[2025-09-20 06:04:51] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -61.827850+0.002258j
[2025-09-20 06:04:51] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-20 06:05:23] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -61.815451-0.004356j
[2025-09-20 06:05:55] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -61.829323-0.000220j
[2025-09-20 06:06:27] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -61.841824-0.006295j
[2025-09-20 06:06:59] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -61.838825+0.002560j
[2025-09-20 06:07:31] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -61.819975+0.005323j
[2025-09-20 06:08:03] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -61.845315-0.000624j
[2025-09-20 06:08:35] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -61.821565-0.003889j
[2025-09-20 06:09:07] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -61.841679+0.001491j
[2025-09-20 06:09:39] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -61.843853+0.000473j
[2025-09-20 06:10:11] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -61.813163+0.000377j
[2025-09-20 06:10:43] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -61.838104+0.000538j
[2025-09-20 06:11:16] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -61.817542-0.000430j
[2025-09-20 06:11:48] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -61.840356-0.005236j
[2025-09-20 06:12:20] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -61.839109-0.002854j
[2025-09-20 06:12:52] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -61.831351-0.000354j
[2025-09-20 06:13:24] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -61.841220-0.001845j
[2025-09-20 06:13:56] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -61.827841-0.002618j
[2025-09-20 06:14:28] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -61.834619+0.001200j
[2025-09-20 06:15:00] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -61.835496+0.003149j
[2025-09-20 06:15:32] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -61.823678-0.000868j
[2025-09-20 06:16:04] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -61.819935-0.000677j
[2025-09-20 06:16:36] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -61.832719-0.001042j
[2025-09-20 06:17:08] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -61.839616+0.004967j
[2025-09-20 06:17:40] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -61.818249-0.000523j
[2025-09-20 06:18:12] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -61.833475+0.001635j
[2025-09-20 06:18:45] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -61.822705+0.005675j
[2025-09-20 06:19:17] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -61.832801+0.001982j
[2025-09-20 06:19:49] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -61.844446-0.000218j
[2025-09-20 06:20:21] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -61.825684-0.001097j
[2025-09-20 06:20:53] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -61.805430-0.001393j
[2025-09-20 06:21:25] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -61.819208+0.000827j
[2025-09-20 06:21:57] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -61.814658+0.001347j
[2025-09-20 06:22:29] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -61.820325+0.002414j
[2025-09-20 06:23:01] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -61.818643-0.002150j
[2025-09-20 06:23:33] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -61.842239-0.000025j
[2025-09-20 06:24:05] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -61.826532+0.000241j
[2025-09-20 06:24:37] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -61.822821-0.000027j
[2025-09-20 06:25:09] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -61.828443+0.001445j
[2025-09-20 06:25:41] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -61.836435+0.001015j
[2025-09-20 06:26:13] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -61.826224-0.004012j
[2025-09-20 06:26:45] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -61.805066+0.004523j
[2025-09-20 06:27:17] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -61.841528-0.000222j
[2025-09-20 06:27:49] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -61.844336-0.001350j
[2025-09-20 06:28:21] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -61.828556-0.001457j
[2025-09-20 06:28:53] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -61.830129-0.000329j
[2025-09-20 06:29:25] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -61.820696-0.002294j
[2025-09-20 06:29:57] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -61.837193-0.000701j
[2025-09-20 06:30:29] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -61.834496+0.002407j
[2025-09-20 06:31:01] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -61.817583+0.002787j
[2025-09-20 06:31:33] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -61.832792-0.002970j
[2025-09-20 06:32:05] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -61.825529+0.001249j
[2025-09-20 06:32:37] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -61.836709-0.001937j
[2025-09-20 06:33:10] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -61.831740-0.005437j
[2025-09-20 06:33:42] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -61.842246-0.001269j
[2025-09-20 06:34:14] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -61.836382-0.004343j
[2025-09-20 06:34:46] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -61.834684+0.004075j
[2025-09-20 06:35:18] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -61.834017-0.002636j
[2025-09-20 06:35:50] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -61.820396+0.000325j
[2025-09-20 06:36:22] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -61.824248+0.000234j
[2025-09-20 06:36:54] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -61.832998+0.002472j
[2025-09-20 06:37:26] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -61.823001+0.004306j
[2025-09-20 06:37:58] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -61.826939-0.001551j
[2025-09-20 06:38:30] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -61.829810+0.000437j
[2025-09-20 06:39:02] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -61.830363+0.002607j
[2025-09-20 06:39:34] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -61.824523+0.001475j
[2025-09-20 06:40:06] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -61.833953-0.001730j
[2025-09-20 06:40:38] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -61.829096+0.001715j
[2025-09-20 06:41:10] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -61.815803+0.001465j
[2025-09-20 06:41:42] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -61.831119-0.002323j
[2025-09-20 06:42:14] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -61.809126-0.001491j
[2025-09-20 06:42:47] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -61.820073-0.002566j
[2025-09-20 06:43:18] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -61.851704-0.000118j
[2025-09-20 06:43:50] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -61.833391+0.001102j
[2025-09-20 06:44:22] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -61.817687+0.000370j
[2025-09-20 06:44:54] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -61.846180+0.002520j
[2025-09-20 06:45:26] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -61.836736+0.001347j
[2025-09-20 06:45:58] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -61.807482-0.003544j
[2025-09-20 06:46:30] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -61.813166+0.000161j
[2025-09-20 06:47:02] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -61.852941+0.003471j
[2025-09-20 06:47:34] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -61.840941+0.000727j
[2025-09-20 06:48:06] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -61.834342+0.003848j
[2025-09-20 06:48:38] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -61.821957+0.002329j
[2025-09-20 06:49:10] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -61.824545-0.001693j
[2025-09-20 06:49:42] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -61.821495-0.000740j
[2025-09-20 06:50:14] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -61.814402+0.000271j
[2025-09-20 06:50:46] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -61.841230+0.000456j
[2025-09-20 06:51:18] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -61.827380-0.003873j
[2025-09-20 06:51:50] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -61.834932+0.001554j
[2025-09-20 06:52:22] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -61.826785+0.000206j
[2025-09-20 06:52:54] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -61.848567+0.001178j
[2025-09-20 06:53:26] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -61.817850+0.002527j
[2025-09-20 06:53:58] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -61.836289+0.001867j
[2025-09-20 06:54:31] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -61.814467+0.004107j
[2025-09-20 06:55:03] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -61.831291+0.001174j
[2025-09-20 06:55:35] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -61.820118-0.000971j
[2025-09-20 06:56:07] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -61.822242-0.000200j
[2025-09-20 06:56:39] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -61.833968-0.002352j
[2025-09-20 06:57:11] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -61.854234-0.006253j
[2025-09-20 06:57:43] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -61.834855-0.003066j
[2025-09-20 06:58:15] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -61.832829+0.000355j
[2025-09-20 06:58:47] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -61.821340+0.000559j
[2025-09-20 06:59:19] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -61.818281+0.002411j
[2025-09-20 06:59:51] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -61.841263+0.002941j
[2025-09-20 07:00:23] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -61.832947+0.000514j
[2025-09-20 07:00:55] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -61.830640+0.006157j
[2025-09-20 07:00:55] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-20 07:01:27] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -61.831489-0.000987j
[2025-09-20 07:01:59] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -61.836826+0.001352j
[2025-09-20 07:02:32] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -61.819436+0.002290j
[2025-09-20 07:03:04] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -61.834073+0.005170j
[2025-09-20 07:03:36] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -61.834641+0.001435j
[2025-09-20 07:04:08] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -61.829592-0.001228j
[2025-09-20 07:04:40] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -61.826004+0.001872j
[2025-09-20 07:05:12] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -61.821371-0.000201j
[2025-09-20 07:05:44] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -61.809604+0.001022j
[2025-09-20 07:06:16] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -61.824076+0.001714j
[2025-09-20 07:06:48] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -61.825812+0.005867j
[2025-09-20 07:07:20] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -61.828620+0.003539j
[2025-09-20 07:07:52] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -61.823095-0.003706j
[2025-09-20 07:08:24] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -61.820648-0.003053j
[2025-09-20 07:08:56] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -61.851692+0.000568j
[2025-09-20 07:09:28] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -61.821779+0.007722j
[2025-09-20 07:10:00] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -61.838337+0.004766j
[2025-09-20 07:10:32] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -61.840104+0.000892j
[2025-09-20 07:11:04] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -61.816224-0.001201j
[2025-09-20 07:11:36] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -61.829781-0.002600j
[2025-09-20 07:12:08] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -61.819770-0.000820j
[2025-09-20 07:12:40] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -61.839514+0.000775j
[2025-09-20 07:13:12] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -61.844331-0.001737j
[2025-09-20 07:13:44] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -61.839914-0.003444j
[2025-09-20 07:14:16] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -61.827962+0.001263j
[2025-09-20 07:14:48] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -61.823601-0.000870j
[2025-09-20 07:15:21] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -61.845687-0.000999j
[2025-09-20 07:15:53] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -61.836369-0.000659j
[2025-09-20 07:16:25] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -61.828502+0.001224j
[2025-09-20 07:16:57] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -61.837673+0.001970j
[2025-09-20 07:17:29] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -61.823196+0.002472j
[2025-09-20 07:18:01] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -61.841619-0.002596j
[2025-09-20 07:18:33] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -61.808424+0.001556j
[2025-09-20 07:19:05] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -61.833050+0.002184j
[2025-09-20 07:19:37] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -61.815590+0.005147j
[2025-09-20 07:20:09] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -61.834925+0.000156j
[2025-09-20 07:20:41] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -61.824875+0.005948j
[2025-09-20 07:21:13] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -61.824586+0.003480j
[2025-09-20 07:21:45] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -61.821359-0.002509j
[2025-09-20 07:22:17] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -61.812192+0.003493j
[2025-09-20 07:22:49] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -61.827164-0.000949j
[2025-09-20 07:23:21] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -61.821513+0.000491j
[2025-09-20 07:23:53] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -61.834386-0.001301j
[2025-09-20 07:24:25] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -61.828140+0.001681j
[2025-09-20 07:24:57] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -61.839303-0.001539j
[2025-09-20 07:25:30] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -61.816339+0.001949j
[2025-09-20 07:26:02] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -61.837528-0.001591j
[2025-09-20 07:26:34] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -61.824328+0.001122j
[2025-09-20 07:27:06] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -61.837890+0.002043j
[2025-09-20 07:27:38] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -61.839421-0.001169j
[2025-09-20 07:28:10] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -61.832235+0.002268j
[2025-09-20 07:28:42] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -61.843942-0.001990j
[2025-09-20 07:29:14] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -61.842503+0.003326j
[2025-09-20 07:29:46] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -61.827758-0.000657j
[2025-09-20 07:30:18] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -61.824014+0.001419j
[2025-09-20 07:30:50] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -61.830815+0.006664j
[2025-09-20 07:31:22] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -61.824148+0.003438j
[2025-09-20 07:31:54] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -61.817205+0.002258j
[2025-09-20 07:32:26] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -61.838827-0.001474j
[2025-09-20 07:32:58] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -61.852362+0.002859j
[2025-09-20 07:33:30] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -61.843774+0.003892j
[2025-09-20 07:34:02] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -61.843063+0.002791j
[2025-09-20 07:34:34] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -61.854252+0.004099j
[2025-09-20 07:35:06] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -61.847251+0.005109j
[2025-09-20 07:35:38] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -61.819124+0.013107j
[2025-09-20 07:36:10] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -61.825603-0.000131j
[2025-09-20 07:36:42] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -61.840471-0.002068j
[2025-09-20 07:37:13] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -61.833318+0.002305j
[2025-09-20 07:37:45] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -61.828985+0.002871j
[2025-09-20 07:38:17] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -61.839001-0.000214j
[2025-09-20 07:38:49] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -61.834177-0.000458j
[2025-09-20 07:39:21] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -61.833751-0.001272j
[2025-09-20 07:39:53] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -61.852415+0.002967j
[2025-09-20 07:40:25] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -61.834861+0.002492j
[2025-09-20 07:40:57] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -61.819693-0.001795j
[2025-09-20 07:41:29] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -61.839051+0.003631j
[2025-09-20 07:42:01] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -61.828049-0.001402j
[2025-09-20 07:42:33] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -61.847743-0.003570j
[2025-09-20 07:43:05] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -61.820743-0.000039j
[2025-09-20 07:43:37] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -61.824106+0.002326j
[2025-09-20 07:44:09] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -61.836048-0.002677j
[2025-09-20 07:44:41] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -61.849030-0.001114j
[2025-09-20 07:45:12] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -61.827856-0.001876j
[2025-09-20 07:45:44] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -61.837416+0.000466j
[2025-09-20 07:46:16] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -61.840735-0.003249j
[2025-09-20 07:46:48] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -61.832235-0.002532j
[2025-09-20 07:47:20] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -61.818018+0.001116j
[2025-09-20 07:47:52] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -61.817970+0.001894j
[2025-09-20 07:48:24] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -61.823230-0.001573j
[2025-09-20 07:48:56] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -61.819826-0.002222j
[2025-09-20 07:49:28] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -61.802323-0.002801j
[2025-09-20 07:50:00] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -61.824885+0.002113j
[2025-09-20 07:50:31] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -61.824180-0.005049j
[2025-09-20 07:51:03] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -61.840015+0.003810j
[2025-09-20 07:51:35] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -61.816908+0.001173j
[2025-09-20 07:52:07] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -61.834017+0.001453j
[2025-09-20 07:52:39] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -61.840769-0.004570j
[2025-09-20 07:53:11] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -61.820560-0.000440j
[2025-09-20 07:53:43] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -61.820115-0.007012j
[2025-09-20 07:54:15] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -61.839334+0.000251j
[2025-09-20 07:54:47] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -61.823597-0.002754j
[2025-09-20 07:55:19] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -61.845053-0.001293j
[2025-09-20 07:55:51] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -61.819594+0.002551j
[2025-09-20 07:56:23] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -61.820624-0.000863j
[2025-09-20 07:56:54] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -61.827905-0.000879j
[2025-09-20 07:56:54] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-20 07:57:26] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -61.830533+0.003462j
[2025-09-20 07:57:58] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -61.814102+0.000437j
[2025-09-20 07:58:30] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -61.833072+0.005503j
[2025-09-20 07:59:02] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -61.837677-0.000117j
[2025-09-20 07:59:34] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -61.828378+0.003135j
[2025-09-20 08:00:06] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -61.841335+0.005362j
[2025-09-20 08:00:38] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -61.849638+0.003107j
[2025-09-20 08:01:10] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -61.831065+0.005972j
[2025-09-20 08:01:42] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -61.836159+0.001391j
[2025-09-20 08:02:14] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -61.825272-0.000453j
[2025-09-20 08:02:46] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -61.835260-0.000007j
[2025-09-20 08:03:18] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -61.833808-0.000802j
[2025-09-20 08:03:50] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -61.796244+0.000873j
[2025-09-20 08:04:22] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -61.824479-0.001850j
[2025-09-20 08:04:54] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -61.834630-0.002496j
[2025-09-20 08:05:26] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -61.821051-0.002935j
[2025-09-20 08:05:58] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -61.816800-0.000702j
[2025-09-20 08:06:30] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -61.838543-0.002717j
[2025-09-20 08:07:02] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -61.844013+0.004507j
[2025-09-20 08:07:34] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -61.825647+0.003713j
[2025-09-20 08:08:06] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -61.828947+0.000526j
[2025-09-20 08:08:38] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -61.822499-0.001360j
[2025-09-20 08:09:10] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -61.833361-0.002573j
[2025-09-20 08:09:42] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -61.851992-0.000486j
[2025-09-20 08:10:15] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -61.825956-0.000114j
[2025-09-20 08:10:47] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -61.829926+0.000217j
[2025-09-20 08:11:19] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -61.833590-0.000215j
[2025-09-20 08:11:51] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -61.839427+0.000630j
[2025-09-20 08:12:23] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -61.839622+0.003579j
[2025-09-20 08:12:55] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -61.835379-0.000803j
[2025-09-20 08:13:27] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -61.818319-0.001592j
[2025-09-20 08:13:59] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -61.845863+0.002882j
[2025-09-20 08:14:31] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -61.831511+0.001424j
[2025-09-20 08:15:03] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -61.824668+0.002316j
[2025-09-20 08:15:35] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -61.833428+0.003345j
[2025-09-20 08:16:07] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -61.824360-0.001704j
[2025-09-20 08:16:39] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -61.814132-0.000733j
[2025-09-20 08:17:11] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -61.836845+0.001584j
[2025-09-20 08:17:43] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -61.826792-0.001690j
[2025-09-20 08:18:15] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -61.844675-0.004173j
[2025-09-20 08:18:47] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -61.831974+0.001733j
[2025-09-20 08:19:20] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -61.823092-0.000948j
[2025-09-20 08:19:52] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -61.837953+0.003011j
[2025-09-20 08:20:24] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -61.843579+0.004468j
[2025-09-20 08:20:56] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -61.828075-0.001860j
[2025-09-20 08:21:27] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -61.818014+0.001198j
[2025-09-20 08:21:59] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -61.836620-0.000990j
[2025-09-20 08:22:31] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -61.834994+0.000407j
[2025-09-20 08:23:03] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -61.820133-0.000845j
[2025-09-20 08:23:35] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -61.855723+0.002553j
[2025-09-20 08:24:07] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -61.839754+0.003415j
[2025-09-20 08:24:39] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -61.834515+0.001464j
[2025-09-20 08:25:11] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -61.835213+0.000811j
[2025-09-20 08:25:43] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -61.833267-0.000807j
[2025-09-20 08:26:15] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -61.826367-0.002202j
[2025-09-20 08:26:47] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -61.824977+0.000208j
[2025-09-20 08:27:19] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -61.821841+0.001186j
[2025-09-20 08:27:51] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -61.833085-0.008266j
[2025-09-20 08:28:23] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -61.816659-0.002081j
[2025-09-20 08:28:55] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -61.841176-0.002080j
[2025-09-20 08:29:27] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -61.830943-0.000164j
[2025-09-20 08:29:59] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -61.824875+0.004085j
[2025-09-20 08:30:31] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -61.816135-0.005018j
[2025-09-20 08:31:03] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -61.836069-0.002629j
[2025-09-20 08:31:35] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -61.822335+0.000555j
[2025-09-20 08:32:07] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -61.822699-0.003535j
[2025-09-20 08:32:39] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -61.841714+0.001786j
[2025-09-20 08:33:11] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -61.841630+0.001527j
[2025-09-20 08:33:43] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -61.825312+0.002674j
[2025-09-20 08:34:15] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -61.823933-0.001198j
[2025-09-20 08:34:47] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -61.844861-0.000080j
[2025-09-20 08:35:19] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -61.811717+0.000659j
[2025-09-20 08:35:51] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -61.835332-0.002152j
[2025-09-20 08:36:23] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -61.827134+0.000614j
[2025-09-20 08:36:55] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -61.824226-0.000752j
[2025-09-20 08:37:27] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -61.833728+0.002829j
[2025-09-20 08:37:59] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -61.828472-0.001288j
[2025-09-20 08:38:32] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -61.832646+0.001276j
[2025-09-20 08:39:04] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -61.840460-0.001842j
[2025-09-20 08:39:36] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -61.817156-0.002117j
[2025-09-20 08:40:08] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -61.819533-0.000166j
[2025-09-20 08:40:40] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -61.835145+0.001440j
[2025-09-20 08:41:12] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -61.835395+0.000669j
[2025-09-20 08:41:44] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -61.819864+0.003530j
[2025-09-20 08:42:16] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -61.838196-0.000081j
[2025-09-20 08:42:48] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -61.801234-0.003939j
[2025-09-20 08:43:20] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -61.846092-0.000707j
[2025-09-20 08:43:52] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -61.855893-0.000522j
[2025-09-20 08:44:24] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -61.823018-0.001110j
[2025-09-20 08:44:56] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -61.830857+0.002581j
[2025-09-20 08:45:28] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -61.841897+0.002234j
[2025-09-20 08:45:59] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -61.836778-0.001471j
[2025-09-20 08:46:31] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -61.835606+0.001884j
[2025-09-20 08:47:03] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -61.828104-0.000371j
[2025-09-20 08:47:35] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -61.841646+0.004375j
[2025-09-20 08:48:07] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -61.822614-0.000834j
[2025-09-20 08:48:39] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -61.824248-0.001663j
[2025-09-20 08:49:10] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -61.816115+0.000904j
[2025-09-20 08:49:42] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -61.810582+0.000818j
[2025-09-20 08:50:14] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -61.833930-0.003872j
[2025-09-20 08:50:46] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -61.816380+0.001622j
[2025-09-20 08:51:18] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -61.827206-0.002045j
[2025-09-20 08:51:50] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -61.833715-0.000262j
[2025-09-20 08:52:22] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -61.822946+0.001974j
[2025-09-20 08:52:54] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -61.831875+0.002288j
[2025-09-20 08:52:54] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-20 08:52:54] ✅ Training completed | Restarts: 2
[2025-09-20 08:52:54] ============================================================
[2025-09-20 08:52:54] Training completed | Runtime: 33701.1s
[2025-09-20 08:53:05] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-20 08:53:05] ============================================================
