[2025-09-19 04:44:56] ✓ 从checkpoint恢复: results/L=6/J2=1.00/J1=0.80/training/checkpoints/final_GCNN.pkl
[2025-09-19 04:44:56]   - 迭代次数: final
[2025-09-19 04:44:56]   - 能量: -64.533015-0.001325j ± 0.007957
[2025-09-19 04:44:56]   - 时间戳: 2025-09-18T18:03:43.683875+08:00
[2025-09-19 04:45:20] ✓ 变分状态参数已从checkpoint恢复
[2025-09-19 04:45:20] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-19 04:45:20] ==================================================
[2025-09-19 04:45:20] GCNN for Shastry-Sutherland Model
[2025-09-19 04:45:20] ==================================================
[2025-09-19 04:45:20] System parameters:
[2025-09-19 04:45:20]   - System size: L=6, N=144
[2025-09-19 04:45:20]   - System parameters: J1=0.79, J2=1.0, Q=0.0
[2025-09-19 04:45:20] --------------------------------------------------
[2025-09-19 04:45:20] Model parameters:
[2025-09-19 04:45:20]   - Number of layers = 4
[2025-09-19 04:45:20]   - Number of features = 4
[2025-09-19 04:45:20]   - Total parameters = 28252
[2025-09-19 04:45:20] --------------------------------------------------
[2025-09-19 04:45:20] Training parameters:
[2025-09-19 04:45:20]   - Learning rate: 0.015
[2025-09-19 04:45:20]   - Total iterations: 1050
[2025-09-19 04:45:20]   - Annealing cycles: 3
[2025-09-19 04:45:20]   - Initial period: 150
[2025-09-19 04:45:20]   - Period multiplier: 2.0
[2025-09-19 04:45:20]   - Temperature range: 0.0-1.0
[2025-09-19 04:45:20]   - Samples: 4096
[2025-09-19 04:45:20]   - Discarded samples: 0
[2025-09-19 04:45:20]   - Chunk size: 2048
[2025-09-19 04:45:20]   - Diagonal shift: 0.2
[2025-09-19 04:45:20]   - Gradient clipping: 1.0
[2025-09-19 04:45:20]   - Checkpoint enabled: interval=105
[2025-09-19 04:45:20]   - Checkpoint directory: results/L=6/J2=1.00/J1=0.79/training/checkpoints
[2025-09-19 04:45:20] --------------------------------------------------
[2025-09-19 04:45:20] Device status:
[2025-09-19 04:45:21]   - Devices model: NVIDIA H200 NVL
[2025-09-19 04:45:21]   - Number of devices: 1
[2025-09-19 04:45:21]   - Sharding: True
[2025-09-19 04:45:21] ============================================================
[2025-09-19 04:46:54] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: 50.168420+0.041443j
[2025-09-19 04:47:58] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: 48.837303+0.098225j
[2025-09-19 04:48:30] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: 47.158054-0.176657j
[2025-09-19 04:49:02] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: 45.447114+0.099485j
[2025-09-19 04:49:34] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: 43.157649-0.057015j
[2025-09-19 04:50:05] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: 39.762058+0.094057j
[2025-09-19 04:50:37] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: 35.231920-0.000578j
[2025-09-19 04:51:09] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: 27.401413-0.069531j
[2025-09-19 04:51:41] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: 14.819003-0.050467j
[2025-09-19 04:52:13] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -1.522427+0.035816j
[2025-09-19 04:52:45] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -20.013134-0.008269j
[2025-09-19 04:53:17] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -37.820655-0.076454j
[2025-09-19 04:53:48] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -51.437415+0.297533j
[2025-09-19 04:54:20] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -59.093337+0.020846j
[2025-09-19 04:54:52] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -62.146002+0.001096j
[2025-09-19 04:55:24] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -63.099999-0.010519j
[2025-09-19 04:55:56] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -63.493088-0.008833j
[2025-09-19 04:56:28] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -63.541585-0.018325j
[2025-09-19 04:57:00] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -63.536750-0.000301j
[2025-09-19 04:57:32] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -63.553937-0.008844j
[2025-09-19 04:58:03] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -63.576198-0.000599j
[2025-09-19 04:58:35] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -63.567611+0.001210j
[2025-09-19 04:59:07] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -63.586056-0.001453j
[2025-09-19 04:59:39] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -63.576781-0.005639j
[2025-09-19 05:00:11] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -63.596964+0.003170j
[2025-09-19 05:00:43] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -63.610514-0.003935j
[2025-09-19 05:01:15] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -63.610350+0.001942j
[2025-09-19 05:01:47] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -63.588199-0.002012j
[2025-09-19 05:02:18] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -63.601232+0.004541j
[2025-09-19 05:02:50] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -63.603818+0.000819j
[2025-09-19 05:03:22] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -63.602849-0.001534j
[2025-09-19 05:03:54] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -63.620080-0.000361j
[2025-09-19 05:04:26] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -63.616178-0.001332j
[2025-09-19 05:04:58] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -63.612854-0.004713j
[2025-09-19 05:05:30] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -63.580330-0.000893j
[2025-09-19 05:06:01] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -63.608919+0.002674j
[2025-09-19 05:06:33] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -63.603418+0.000463j
[2025-09-19 05:07:05] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -63.615375+0.002756j
[2025-09-19 05:07:37] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -63.596846+0.004214j
[2025-09-19 05:08:09] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -63.614914+0.000159j
[2025-09-19 05:08:41] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -63.633945+0.005056j
[2025-09-19 05:09:12] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -63.642718-0.005612j
[2025-09-19 05:09:44] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -63.604271-0.000249j
[2025-09-19 05:10:16] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -63.626718-0.002027j
[2025-09-19 05:10:48] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -63.619570-0.005986j
[2025-09-19 05:11:20] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -63.631459+0.008457j
[2025-09-19 05:11:52] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -63.618048+0.000022j
[2025-09-19 05:12:24] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -63.604192-0.002189j
[2025-09-19 05:12:55] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -63.598790-0.000259j
[2025-09-19 05:13:27] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -63.616463-0.001409j
[2025-09-19 05:13:59] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -63.600233+0.002961j
[2025-09-19 05:14:31] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -63.590750+0.002248j
[2025-09-19 05:15:03] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -63.629576-0.002916j
[2025-09-19 05:15:35] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -63.616536+0.000642j
[2025-09-19 05:16:07] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -63.617264+0.000894j
[2025-09-19 05:16:38] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -63.618764-0.003059j
[2025-09-19 05:17:10] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -63.611127-0.002135j
[2025-09-19 05:17:42] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -63.615868+0.002109j
[2025-09-19 05:18:14] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -63.609711+0.000737j
[2025-09-19 05:18:46] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -63.610821+0.000115j
[2025-09-19 05:19:18] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -63.619974-0.000088j
[2025-09-19 05:19:50] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -63.621184+0.000088j
[2025-09-19 05:20:22] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -63.607244+0.003976j
[2025-09-19 05:20:53] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -63.615550+0.002410j
[2025-09-19 05:21:25] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -63.616113+0.002260j
[2025-09-19 05:21:57] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -63.631170+0.006788j
[2025-09-19 05:22:29] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -63.619164+0.002478j
[2025-09-19 05:23:01] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -63.601764+0.001611j
[2025-09-19 05:23:33] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -63.641061+0.005829j
[2025-09-19 05:24:05] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -63.603326+0.003758j
[2025-09-19 05:24:36] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -63.620955+0.000435j
[2025-09-19 05:25:08] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -63.623953-0.001875j
[2025-09-19 05:25:40] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -63.632657-0.001459j
[2025-09-19 05:26:12] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -63.627400+0.000220j
[2025-09-19 05:26:44] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -63.618169-0.001912j
[2025-09-19 05:27:16] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -63.625534-0.001604j
[2025-09-19 05:27:47] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -63.630250-0.004519j
[2025-09-19 05:28:19] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -63.627964-0.003193j
[2025-09-19 05:28:51] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -63.605497-0.002801j
[2025-09-19 05:29:23] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -63.618651+0.003260j
[2025-09-19 05:29:55] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -63.621749+0.000291j
[2025-09-19 05:30:27] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -63.631730+0.006846j
[2025-09-19 05:30:58] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -63.614940-0.000410j
[2025-09-19 05:31:30] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -63.614890+0.000950j
[2025-09-19 05:32:02] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -63.625296-0.002242j
[2025-09-19 05:32:34] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -63.623008+0.001123j
[2025-09-19 05:33:06] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -63.616002-0.004140j
[2025-09-19 05:33:38] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -63.623504-0.006322j
[2025-09-19 05:34:10] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -63.624879-0.002140j
[2025-09-19 05:34:42] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -63.614304+0.001920j
[2025-09-19 05:35:14] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -63.619831+0.004800j
[2025-09-19 05:35:46] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -63.629284-0.003793j
[2025-09-19 05:36:17] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -63.613142+0.003761j
[2025-09-19 05:36:49] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -63.620694+0.000963j
[2025-09-19 05:37:21] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -63.624122+0.000724j
[2025-09-19 05:37:53] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -63.639681+0.001299j
[2025-09-19 05:38:25] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -63.627351-0.001573j
[2025-09-19 05:38:57] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -63.644807-0.003065j
[2025-09-19 05:39:29] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -63.616326+0.002441j
[2025-09-19 05:40:01] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -63.629572+0.005912j
[2025-09-19 05:40:33] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -63.619643+0.004381j
[2025-09-19 05:41:04] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -63.611050+0.005477j
[2025-09-19 05:41:36] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -63.613837-0.001697j
[2025-09-19 05:42:08] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -63.616587-0.004171j
[2025-09-19 05:42:40] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -63.606941-0.000480j
[2025-09-19 05:42:40] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-19 05:43:12] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -63.596747+0.001052j
[2025-09-19 05:43:44] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -63.642597+0.002586j
[2025-09-19 05:44:15] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -63.644747-0.001865j
[2025-09-19 05:44:47] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -63.616633+0.005943j
[2025-09-19 05:45:19] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -63.621268+0.002024j
[2025-09-19 05:45:51] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -63.627998+0.001103j
[2025-09-19 05:46:23] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -63.624985-0.002488j
[2025-09-19 05:46:55] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -63.609130+0.003867j
[2025-09-19 05:47:27] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -63.618274-0.001937j
[2025-09-19 05:47:59] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -63.631677+0.005141j
[2025-09-19 05:48:31] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -63.608599-0.002171j
[2025-09-19 05:49:03] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -63.631284+0.004480j
[2025-09-19 05:49:35] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -63.638422+0.003879j
[2025-09-19 05:50:07] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -63.610766+0.003576j
[2025-09-19 05:50:38] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -63.621334-0.007347j
[2025-09-19 05:51:10] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -63.613229-0.003746j
[2025-09-19 05:51:42] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -63.614789-0.001740j
[2025-09-19 05:52:14] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -63.603446+0.002288j
[2025-09-19 05:52:46] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -63.609682-0.004406j
[2025-09-19 05:53:18] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -63.625525+0.000586j
[2025-09-19 05:53:50] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -63.609330-0.000524j
[2025-09-19 05:54:22] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -63.617691+0.000208j
[2025-09-19 05:54:54] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -63.610699-0.003351j
[2025-09-19 05:55:26] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -63.630394+0.002367j
[2025-09-19 05:55:58] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -63.639819-0.000005j
[2025-09-19 05:56:30] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -63.628110+0.003212j
[2025-09-19 05:57:01] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -63.608018+0.004776j
[2025-09-19 05:57:33] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -63.630384-0.011961j
[2025-09-19 05:58:05] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -63.618226-0.000705j
[2025-09-19 05:58:37] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -63.624039-0.006730j
[2025-09-19 05:59:09] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -63.615840+0.002077j
[2025-09-19 05:59:41] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -63.641598+0.000298j
[2025-09-19 06:00:12] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -63.611562-0.001637j
[2025-09-19 06:00:44] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -63.628520+0.000684j
[2025-09-19 06:01:16] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -63.648259-0.001428j
[2025-09-19 06:01:48] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -63.615720-0.000779j
[2025-09-19 06:02:20] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -63.625293+0.003022j
[2025-09-19 06:02:52] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -63.641297-0.006084j
[2025-09-19 06:03:24] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -63.616642+0.000861j
[2025-09-19 06:03:55] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -63.623982+0.000501j
[2025-09-19 06:04:27] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -63.632873-0.000473j
[2025-09-19 06:04:59] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -63.630772-0.002818j
[2025-09-19 06:05:31] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -63.629451+0.001391j
[2025-09-19 06:06:03] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -63.624845+0.001971j
[2025-09-19 06:06:35] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -63.627039+0.003960j
[2025-09-19 06:06:35] RESTART #1 | Period: 300
[2025-09-19 06:07:07] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -63.614099+0.000712j
[2025-09-19 06:07:38] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -63.625590+0.001125j
[2025-09-19 06:08:10] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -63.625948-0.000774j
[2025-09-19 06:08:42] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -63.611834-0.001997j
[2025-09-19 06:09:14] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -63.602915+0.002211j
[2025-09-19 06:09:46] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -63.630339+0.003895j
[2025-09-19 06:10:18] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -63.636142+0.010382j
[2025-09-19 06:10:49] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -63.644945+0.001633j
[2025-09-19 06:11:21] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -63.620917+0.001813j
[2025-09-19 06:11:53] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -63.620277+0.005214j
[2025-09-19 06:12:25] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -63.624203+0.002253j
[2025-09-19 06:12:57] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -63.633523+0.005658j
[2025-09-19 06:13:29] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -63.630307+0.001813j
[2025-09-19 06:14:01] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -63.631721+0.005049j
[2025-09-19 06:14:33] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -63.619336+0.000650j
[2025-09-19 06:15:04] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -63.624668-0.000012j
[2025-09-19 06:15:36] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -63.603932+0.003921j
[2025-09-19 06:16:08] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -63.602335+0.002470j
[2025-09-19 06:16:40] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -63.614386+0.006459j
[2025-09-19 06:17:12] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -63.625958+0.007453j
[2025-09-19 06:17:44] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -63.632716+0.004383j
[2025-09-19 06:18:16] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -63.617082-0.000146j
[2025-09-19 06:18:48] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -63.608318+0.000340j
[2025-09-19 06:19:19] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -63.638949-0.000798j
[2025-09-19 06:19:51] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -63.606082+0.000940j
[2025-09-19 06:20:23] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -63.622822+0.000190j
[2025-09-19 06:20:55] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -63.633481+0.000560j
[2025-09-19 06:21:27] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -63.629474-0.000052j
[2025-09-19 06:21:58] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -63.632398+0.002964j
[2025-09-19 06:22:30] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -63.618996-0.000573j
[2025-09-19 06:23:02] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -63.631159+0.000474j
[2025-09-19 06:23:34] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -63.632801-0.002352j
[2025-09-19 06:24:06] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -63.616039+0.005810j
[2025-09-19 06:24:38] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -63.642445+0.003389j
[2025-09-19 06:25:09] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -63.615836+0.010833j
[2025-09-19 06:25:41] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -63.620887-0.002737j
[2025-09-19 06:26:13] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -63.618852+0.003694j
[2025-09-19 06:26:45] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -63.624361+0.009592j
[2025-09-19 06:27:17] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -63.625481+0.000111j
[2025-09-19 06:27:49] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -63.630434+0.002225j
[2025-09-19 06:28:21] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -63.626593+0.003343j
[2025-09-19 06:28:53] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -63.632113-0.000641j
[2025-09-19 06:29:25] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -63.627860-0.002567j
[2025-09-19 06:29:56] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -63.627272-0.003478j
[2025-09-19 06:30:28] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -63.624703+0.002964j
[2025-09-19 06:31:00] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -63.640495+0.001777j
[2025-09-19 06:31:32] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -63.617884-0.001386j
[2025-09-19 06:32:04] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -63.624208+0.004466j
[2025-09-19 06:32:36] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -63.639985+0.000437j
[2025-09-19 06:33:08] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -63.632953-0.000089j
[2025-09-19 06:33:39] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -63.630464-0.000309j
[2025-09-19 06:34:11] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -63.621774-0.000908j
[2025-09-19 06:34:43] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -63.628000+0.004583j
[2025-09-19 06:35:15] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -63.619188-0.003430j
[2025-09-19 06:35:47] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -63.632800-0.004470j
[2025-09-19 06:36:19] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -63.635690+0.002979j
[2025-09-19 06:36:51] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -63.619056+0.001881j
[2025-09-19 06:37:23] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -63.606473-0.000315j
[2025-09-19 06:37:54] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -63.622013+0.005400j
[2025-09-19 06:38:26] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -63.628902+0.004129j
[2025-09-19 06:38:26] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-19 06:38:58] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -63.636114+0.009323j
[2025-09-19 06:39:30] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -63.627117+0.003652j
[2025-09-19 06:40:02] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -63.632072-0.002791j
[2025-09-19 06:40:34] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -63.621488-0.002794j
[2025-09-19 06:41:06] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -63.645537-0.001325j
[2025-09-19 06:41:37] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -63.647201+0.000410j
[2025-09-19 06:42:09] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -63.648336-0.003420j
[2025-09-19 06:42:41] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -63.617354-0.000930j
[2025-09-19 06:43:13] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -63.627037+0.001885j
[2025-09-19 06:43:45] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -63.634863-0.000889j
[2025-09-19 06:44:17] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -63.616943+0.000411j
[2025-09-19 06:44:49] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -63.636579+0.006556j
[2025-09-19 06:45:20] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -63.631563+0.002947j
[2025-09-19 06:45:52] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -63.643342-0.002738j
[2025-09-19 06:46:24] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -63.627071-0.008863j
[2025-09-19 06:46:56] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -63.610982-0.004301j
[2025-09-19 06:47:28] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -63.609194+0.001982j
[2025-09-19 06:47:59] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -63.619747+0.004418j
[2025-09-19 06:48:31] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -63.628700+0.001495j
[2025-09-19 06:49:03] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -63.640624+0.000451j
[2025-09-19 06:49:35] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -63.611108-0.003255j
[2025-09-19 06:50:07] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -63.630855-0.002173j
[2025-09-19 06:50:39] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -63.609646+0.002787j
[2025-09-19 06:51:11] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -63.613474+0.003610j
[2025-09-19 06:51:42] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -63.638242-0.003671j
[2025-09-19 06:52:14] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -63.628957+0.000243j
[2025-09-19 06:52:46] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -63.613701+0.002157j
[2025-09-19 06:53:18] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -63.630359+0.001837j
[2025-09-19 06:53:50] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -63.645209-0.000949j
[2025-09-19 06:54:22] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -63.605903+0.001992j
[2025-09-19 06:54:54] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -63.600332+0.001865j
[2025-09-19 06:55:26] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -63.616897+0.001248j
[2025-09-19 06:55:58] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -63.636356-0.004094j
[2025-09-19 06:56:30] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -63.626874-0.000072j
[2025-09-19 06:57:01] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -63.608189+0.001963j
[2025-09-19 06:57:33] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -63.632762+0.002433j
[2025-09-19 06:58:05] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -63.612147+0.001606j
[2025-09-19 06:58:37] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -63.614325-0.003054j
[2025-09-19 06:59:09] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -63.616105+0.004416j
[2025-09-19 06:59:41] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -63.629163-0.000454j
[2025-09-19 07:00:13] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -63.643934+0.001745j
[2025-09-19 07:00:44] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -63.607974+0.005131j
[2025-09-19 07:01:16] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -63.627878+0.000639j
[2025-09-19 07:01:48] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -63.637063-0.003034j
[2025-09-19 07:02:20] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -63.613583-0.001988j
[2025-09-19 07:02:52] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -63.635012-0.005850j
[2025-09-19 07:03:24] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -63.631002-0.001178j
[2025-09-19 07:03:56] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -63.632371+0.003558j
[2025-09-19 07:04:27] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -63.640592+0.005723j
[2025-09-19 07:04:59] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -63.622017-0.000785j
[2025-09-19 07:05:31] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -63.630335+0.002131j
[2025-09-19 07:06:03] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -63.626986-0.001188j
[2025-09-19 07:06:35] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -63.613621+0.002883j
[2025-09-19 07:07:07] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -63.633128+0.004128j
[2025-09-19 07:07:39] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -63.617657+0.003375j
[2025-09-19 07:08:11] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -63.623859-0.003193j
[2025-09-19 07:08:43] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -63.618302+0.001010j
[2025-09-19 07:09:14] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -63.634562-0.007246j
[2025-09-19 07:09:46] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -63.621380+0.002607j
[2025-09-19 07:10:18] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -63.657536-0.000173j
[2025-09-19 07:10:50] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -63.628029+0.000735j
[2025-09-19 07:11:22] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -63.632773+0.000179j
[2025-09-19 07:11:54] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -63.651880+0.000423j
[2025-09-19 07:12:26] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -63.619954+0.000427j
[2025-09-19 07:12:57] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -63.627141-0.000937j
[2025-09-19 07:13:29] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -63.635563+0.001318j
[2025-09-19 07:14:01] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -63.652401-0.000089j
[2025-09-19 07:14:33] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -63.604058+0.002725j
[2025-09-19 07:15:05] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -63.646423+0.001346j
[2025-09-19 07:15:36] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -63.617540-0.003868j
[2025-09-19 07:16:08] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -63.616009+0.000035j
[2025-09-19 07:16:40] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -63.629263-0.001563j
[2025-09-19 07:17:12] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -63.636247-0.004049j
[2025-09-19 07:17:44] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -63.616893+0.002086j
[2025-09-19 07:18:16] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -63.628422-0.001239j
[2025-09-19 07:18:48] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -63.627993-0.001606j
[2025-09-19 07:19:19] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -63.634491+0.002023j
[2025-09-19 07:19:51] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -63.622879+0.003938j
[2025-09-19 07:20:23] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -63.637076-0.000444j
[2025-09-19 07:20:55] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -63.635357-0.000060j
[2025-09-19 07:21:27] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -63.633829-0.001210j
[2025-09-19 07:21:59] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -63.619327-0.000176j
[2025-09-19 07:22:31] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -63.621848+0.007532j
[2025-09-19 07:23:02] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -63.615966-0.006063j
[2025-09-19 07:23:34] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -63.625350+0.002374j
[2025-09-19 07:24:06] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -63.629075+0.006344j
[2025-09-19 07:24:38] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -63.624356-0.004359j
[2025-09-19 07:25:10] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -63.631998-0.008863j
[2025-09-19 07:25:42] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -63.635983+0.005182j
[2025-09-19 07:26:14] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -63.630929+0.003990j
[2025-09-19 07:26:45] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -63.628854-0.003258j
[2025-09-19 07:27:17] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -63.637634+0.000750j
[2025-09-19 07:27:49] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -63.655945-0.000754j
[2025-09-19 07:28:21] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -63.630091+0.001211j
[2025-09-19 07:28:53] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -63.615782+0.005679j
[2025-09-19 07:29:24] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -63.625140-0.003960j
[2025-09-19 07:29:56] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -63.625894+0.003656j
[2025-09-19 07:30:28] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -63.631173+0.000858j
[2025-09-19 07:31:00] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -63.617115-0.003252j
[2025-09-19 07:31:32] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -63.630396-0.001391j
[2025-09-19 07:32:03] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -63.626443+0.002979j
[2025-09-19 07:32:35] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -63.626820-0.002889j
[2025-09-19 07:33:07] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -63.638164+0.000913j
[2025-09-19 07:33:39] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -63.625783-0.004692j
[2025-09-19 07:34:11] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -63.641479+0.000299j
[2025-09-19 07:34:11] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-19 07:34:43] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -63.630315-0.000296j
[2025-09-19 07:35:15] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -63.609455+0.000650j
[2025-09-19 07:35:46] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -63.647952-0.003953j
[2025-09-19 07:36:18] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -63.634563-0.004787j
[2025-09-19 07:36:50] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -63.647764+0.007719j
[2025-09-19 07:37:22] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -63.624338+0.004337j
[2025-09-19 07:37:54] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -63.645854+0.001983j
[2025-09-19 07:38:25] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -63.633916+0.004491j
[2025-09-19 07:38:57] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -63.620965+0.001706j
[2025-09-19 07:39:29] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -63.643999-0.004261j
[2025-09-19 07:40:01] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -63.634496-0.001061j
[2025-09-19 07:40:33] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -63.630649+0.000296j
[2025-09-19 07:41:04] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -63.622955-0.000701j
[2025-09-19 07:41:36] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -63.634851+0.000794j
[2025-09-19 07:42:08] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -63.628008-0.000614j
[2025-09-19 07:42:39] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -63.602137+0.000839j
[2025-09-19 07:43:11] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -63.636991+0.005157j
[2025-09-19 07:43:43] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -63.623884-0.001426j
[2025-09-19 07:44:14] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -63.622198-0.003125j
[2025-09-19 07:44:46] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -63.644987-0.002023j
[2025-09-19 07:45:18] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -63.645211+0.001806j
[2025-09-19 07:45:49] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -63.641260-0.003953j
[2025-09-19 07:46:21] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -63.615305-0.000418j
[2025-09-19 07:46:53] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -63.635852+0.001830j
[2025-09-19 07:47:24] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -63.625141+0.002332j
[2025-09-19 07:47:56] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -63.605338-0.000372j
[2025-09-19 07:48:28] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -63.607260-0.001050j
[2025-09-19 07:48:59] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -63.637569+0.001025j
[2025-09-19 07:49:31] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -63.622283+0.000309j
[2025-09-19 07:50:03] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -63.625628+0.003555j
[2025-09-19 07:50:34] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -63.624103+0.000714j
[2025-09-19 07:51:06] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -63.634038-0.002198j
[2025-09-19 07:51:38] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -63.622681+0.001456j
[2025-09-19 07:52:09] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -63.641327+0.000292j
[2025-09-19 07:52:41] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -63.638018+0.002476j
[2025-09-19 07:53:13] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -63.636769-0.000989j
[2025-09-19 07:53:44] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -63.627740+0.002928j
[2025-09-19 07:54:16] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -63.643223-0.001094j
[2025-09-19 07:54:48] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -63.632141-0.000593j
[2025-09-19 07:55:19] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -63.632710-0.003341j
[2025-09-19 07:55:51] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -63.622245+0.000411j
[2025-09-19 07:56:23] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -63.621703+0.001130j
[2025-09-19 07:56:54] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -63.638711+0.001044j
[2025-09-19 07:57:26] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -63.644415-0.003714j
[2025-09-19 07:57:58] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -63.634484-0.003547j
[2025-09-19 07:58:29] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -63.639212+0.002915j
[2025-09-19 07:59:01] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -63.630466-0.001411j
[2025-09-19 07:59:33] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -63.646609+0.006128j
[2025-09-19 08:00:05] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -63.637508-0.002623j
[2025-09-19 08:00:37] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -63.642436+0.001249j
[2025-09-19 08:01:09] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -63.634695-0.000753j
[2025-09-19 08:01:41] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -63.629329+0.002063j
[2025-09-19 08:02:12] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -63.622165-0.002613j
[2025-09-19 08:02:44] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -63.636656-0.000156j
[2025-09-19 08:03:16] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -63.629807-0.001729j
[2025-09-19 08:03:48] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -63.639579-0.004740j
[2025-09-19 08:04:20] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -63.640078+0.005004j
[2025-09-19 08:04:52] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -63.622191-0.000221j
[2025-09-19 08:05:24] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -63.618454+0.004090j
[2025-09-19 08:05:55] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -63.642038+0.002686j
[2025-09-19 08:06:27] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -63.629314+0.001788j
[2025-09-19 08:06:59] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -63.639982-0.004733j
[2025-09-19 08:07:31] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -63.631748+0.006466j
[2025-09-19 08:08:03] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -63.627121+0.002149j
[2025-09-19 08:08:35] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -63.642919-0.003226j
[2025-09-19 08:09:07] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -63.624724-0.000118j
[2025-09-19 08:09:38] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -63.619197+0.004992j
[2025-09-19 08:10:10] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -63.614999+0.001792j
[2025-09-19 08:10:42] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -63.638152+0.002958j
[2025-09-19 08:11:14] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -63.628500+0.001488j
[2025-09-19 08:11:46] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -63.631273+0.002440j
[2025-09-19 08:12:18] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -63.636310-0.003292j
[2025-09-19 08:12:50] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -63.619529-0.000569j
[2025-09-19 08:13:22] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -63.620626-0.005464j
[2025-09-19 08:13:53] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -63.647941-0.001713j
[2025-09-19 08:14:25] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -63.648689-0.000242j
[2025-09-19 08:14:57] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -63.641360-0.000293j
[2025-09-19 08:15:29] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -63.635301+0.002622j
[2025-09-19 08:16:01] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -63.629066+0.002705j
[2025-09-19 08:16:33] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -63.622481-0.000579j
[2025-09-19 08:17:04] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -63.627376-0.001816j
[2025-09-19 08:17:36] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -63.613639-0.000373j
[2025-09-19 08:18:08] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -63.615006+0.001224j
[2025-09-19 08:18:40] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -63.625370-0.000671j
[2025-09-19 08:19:12] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -63.633296-0.000359j
[2025-09-19 08:19:44] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -63.609830+0.005500j
[2025-09-19 08:20:16] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -63.635608+0.001572j
[2025-09-19 08:20:48] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -63.632854+0.003409j
[2025-09-19 08:21:19] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -63.605976+0.000209j
[2025-09-19 08:21:51] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -63.641498-0.000305j
[2025-09-19 08:22:23] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -63.627161-0.001093j
[2025-09-19 08:22:55] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -63.628331-0.000156j
[2025-09-19 08:23:27] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -63.641383+0.001837j
[2025-09-19 08:23:58] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -63.629065-0.002213j
[2025-09-19 08:24:30] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -63.645824-0.003704j
[2025-09-19 08:25:02] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -63.649337-0.001050j
[2025-09-19 08:25:34] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -63.619654-0.001079j
[2025-09-19 08:26:06] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -63.633135-0.000419j
[2025-09-19 08:26:38] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -63.625023-0.001364j
[2025-09-19 08:27:10] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -63.636498+0.001520j
[2025-09-19 08:27:41] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -63.630328-0.002165j
[2025-09-19 08:28:13] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -63.637475+0.000050j
[2025-09-19 08:28:45] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -63.642623-0.003156j
[2025-09-19 08:29:17] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -63.622825-0.002253j
[2025-09-19 08:29:49] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -63.620331-0.001684j
[2025-09-19 08:29:49] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-19 08:30:21] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -63.649223-0.001272j
[2025-09-19 08:30:53] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -63.628353-0.000373j
[2025-09-19 08:31:24] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -63.619336+0.004273j
[2025-09-19 08:31:56] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -63.633739+0.000849j
[2025-09-19 08:32:28] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -63.647043+0.002876j
[2025-09-19 08:33:00] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -63.628208+0.000876j
[2025-09-19 08:33:32] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -63.608961-0.000715j
[2025-09-19 08:34:04] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -63.619588+0.002644j
[2025-09-19 08:34:36] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -63.636403-0.001232j
[2025-09-19 08:35:08] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -63.632601+0.001278j
[2025-09-19 08:35:39] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -63.629236-0.002808j
[2025-09-19 08:36:11] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -63.619491-0.002720j
[2025-09-19 08:36:43] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -63.636965-0.002052j
[2025-09-19 08:37:15] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -63.633859-0.000282j
[2025-09-19 08:37:47] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -63.629506-0.006008j
[2025-09-19 08:38:19] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -63.655967+0.000800j
[2025-09-19 08:38:51] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -63.627072+0.000620j
[2025-09-19 08:39:23] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -63.626481+0.000646j
[2025-09-19 08:39:55] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -63.614268+0.000008j
[2025-09-19 08:40:26] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -63.636515-0.000756j
[2025-09-19 08:40:58] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -63.626882-0.000282j
[2025-09-19 08:41:30] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -63.644474+0.001911j
[2025-09-19 08:42:02] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -63.640372+0.001737j
[2025-09-19 08:42:34] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -63.633784-0.000652j
[2025-09-19 08:43:06] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -63.623549-0.000569j
[2025-09-19 08:43:38] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -63.657308-0.002221j
[2025-09-19 08:44:10] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -63.634117-0.003335j
[2025-09-19 08:44:41] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -63.628368+0.001439j
[2025-09-19 08:45:13] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -63.623109-0.001995j
[2025-09-19 08:45:45] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -63.636189+0.001117j
[2025-09-19 08:45:45] RESTART #2 | Period: 600
[2025-09-19 08:46:17] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -63.640838+0.000990j
[2025-09-19 08:46:49] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -63.635493-0.003745j
[2025-09-19 08:47:20] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -63.636694-0.005837j
[2025-09-19 08:47:52] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -63.627017-0.003682j
[2025-09-19 08:48:24] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -63.619309+0.000063j
[2025-09-19 08:48:56] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -63.636015-0.002132j
[2025-09-19 08:49:28] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -63.634005+0.001598j
[2025-09-19 08:50:00] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -63.636878-0.001763j
[2025-09-19 08:50:31] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -63.625344-0.003516j
[2025-09-19 08:51:03] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -63.641159+0.000111j
[2025-09-19 08:51:35] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -63.633794+0.000345j
[2025-09-19 08:52:07] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -63.648519+0.002697j
[2025-09-19 08:52:39] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -63.631492+0.000527j
[2025-09-19 08:53:11] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -63.620333+0.000732j
[2025-09-19 08:53:43] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -63.621716+0.000466j
[2025-09-19 08:54:15] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -63.622849-0.003186j
[2025-09-19 08:54:47] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -63.618394+0.002566j
[2025-09-19 08:55:18] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -63.610101+0.002363j
[2025-09-19 08:55:50] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -63.635967+0.002242j
[2025-09-19 08:56:22] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -63.641872-0.001230j
[2025-09-19 08:56:54] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -63.647331+0.002315j
[2025-09-19 08:57:26] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -63.633831+0.005277j
[2025-09-19 08:57:58] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -63.624909+0.004039j
[2025-09-19 08:58:30] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -63.607807+0.000288j
[2025-09-19 08:59:01] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -63.608972+0.004772j
[2025-09-19 08:59:33] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -63.639308+0.000599j
[2025-09-19 09:00:05] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -63.619541+0.004258j
[2025-09-19 09:00:37] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -63.638007-0.000241j
[2025-09-19 09:01:09] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -63.627987-0.002795j
[2025-09-19 09:01:40] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -63.654952-0.002285j
[2025-09-19 09:02:12] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -63.621333+0.005208j
[2025-09-19 09:02:44] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -63.632971-0.004365j
[2025-09-19 09:03:16] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -63.628850-0.004245j
[2025-09-19 09:03:48] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -63.620859-0.000768j
[2025-09-19 09:04:20] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -63.638517-0.000380j
[2025-09-19 09:04:51] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -63.621342+0.003837j
[2025-09-19 09:05:23] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -63.618397+0.002727j
[2025-09-19 09:05:55] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -63.618607-0.004218j
[2025-09-19 09:06:27] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -63.639229-0.001149j
[2025-09-19 09:06:59] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -63.624932+0.006917j
[2025-09-19 09:07:31] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -63.616422-0.000644j
[2025-09-19 09:08:03] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -63.640753-0.003378j
[2025-09-19 09:08:34] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -63.632252-0.003598j
[2025-09-19 09:09:06] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -63.634707-0.003917j
[2025-09-19 09:09:38] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -63.630139-0.000747j
[2025-09-19 09:10:10] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -63.635957+0.000729j
[2025-09-19 09:10:42] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -63.632959+0.001025j
[2025-09-19 09:11:14] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -63.626697+0.001015j
[2025-09-19 09:11:45] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -63.616583-0.000661j
[2025-09-19 09:12:17] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -63.652365+0.001048j
[2025-09-19 09:12:49] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -63.639386+0.000328j
[2025-09-19 09:13:21] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -63.629673-0.000678j
[2025-09-19 09:13:53] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -63.626919-0.003387j
[2025-09-19 09:14:25] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -63.649465-0.002523j
[2025-09-19 09:14:57] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -63.625638+0.000486j
[2025-09-19 09:15:28] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -63.623969-0.000053j
[2025-09-19 09:16:00] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -63.624728-0.005053j
[2025-09-19 09:16:32] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -63.625281+0.000786j
[2025-09-19 09:17:04] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -63.649384-0.000705j
[2025-09-19 09:17:36] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -63.628871-0.000831j
[2025-09-19 09:18:08] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -63.643921-0.001376j
[2025-09-19 09:18:40] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -63.640869+0.003179j
[2025-09-19 09:19:11] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -63.622864+0.001715j
[2025-09-19 09:19:43] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -63.631232-0.007398j
[2025-09-19 09:20:15] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -63.642880+0.001211j
[2025-09-19 09:20:47] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -63.642467+0.000530j
[2025-09-19 09:21:19] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -63.633694+0.001358j
[2025-09-19 09:21:51] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -63.638846+0.001210j
[2025-09-19 09:22:22] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -63.633593-0.000649j
[2025-09-19 09:22:54] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -63.628956-0.000077j
[2025-09-19 09:23:26] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -63.648702+0.000424j
[2025-09-19 09:23:58] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -63.614714+0.003498j
[2025-09-19 09:24:30] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -63.631775-0.001334j
[2025-09-19 09:25:02] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -63.638341+0.004331j
[2025-09-19 09:25:34] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -63.619920-0.004745j
[2025-09-19 09:25:34] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-19 09:26:06] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -63.632247-0.003525j
[2025-09-19 09:26:38] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -63.623801-0.000928j
[2025-09-19 09:27:10] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -63.631603+0.000277j
[2025-09-19 09:27:41] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -63.629215-0.005321j
[2025-09-19 09:28:13] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -63.618922+0.000722j
[2025-09-19 09:28:45] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -63.620246+0.000794j
[2025-09-19 09:29:17] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -63.634345-0.004249j
[2025-09-19 09:29:49] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -63.620541-0.000103j
[2025-09-19 09:30:21] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -63.630421-0.000636j
[2025-09-19 09:30:52] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -63.633489-0.001422j
[2025-09-19 09:31:24] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -63.628750+0.005596j
[2025-09-19 09:31:56] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -63.639952-0.001410j
[2025-09-19 09:32:28] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -63.617263+0.002946j
[2025-09-19 09:33:00] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -63.642715+0.002938j
[2025-09-19 09:33:32] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -63.621681-0.000143j
[2025-09-19 09:34:03] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -63.632532-0.003484j
[2025-09-19 09:34:35] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -63.631079+0.000925j
[2025-09-19 09:35:07] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -63.619575+0.002442j
[2025-09-19 09:35:39] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -63.616530+0.001486j
[2025-09-19 09:36:11] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -63.634181-0.003616j
[2025-09-19 09:36:43] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -63.637707-0.000195j
[2025-09-19 09:37:14] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -63.635178+0.002019j
[2025-09-19 09:37:46] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -63.634858+0.000463j
[2025-09-19 09:38:18] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -63.632300+0.002631j
[2025-09-19 09:38:50] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -63.625893+0.001042j
[2025-09-19 09:39:22] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -63.630298-0.002167j
[2025-09-19 09:39:54] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -63.630392+0.002496j
[2025-09-19 09:40:25] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -63.634171+0.001732j
[2025-09-19 09:40:57] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -63.623264+0.000358j
[2025-09-19 09:41:29] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -63.630190+0.001803j
[2025-09-19 09:42:01] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -63.623901-0.001497j
[2025-09-19 09:42:33] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -63.629614-0.000595j
[2025-09-19 09:43:05] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -63.642318+0.003287j
[2025-09-19 09:43:37] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -63.634434+0.002519j
[2025-09-19 09:44:09] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -63.635917-0.001512j
[2025-09-19 09:44:40] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -63.636863+0.001802j
[2025-09-19 09:45:12] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -63.619861-0.001301j
[2025-09-19 09:45:44] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -63.639033+0.002325j
[2025-09-19 09:46:16] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -63.625985+0.003117j
[2025-09-19 09:46:48] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -63.629127-0.001316j
[2025-09-19 09:47:20] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -63.650032-0.000026j
[2025-09-19 09:47:52] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -63.643246+0.002587j
[2025-09-19 09:48:23] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -63.645110-0.001360j
[2025-09-19 09:48:55] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -63.621703-0.004167j
[2025-09-19 09:49:27] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -63.661840-0.001908j
[2025-09-19 09:49:59] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -63.657027+0.000990j
[2025-09-19 09:50:31] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -63.628235+0.004733j
[2025-09-19 09:51:03] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -63.646304+0.000612j
[2025-09-19 09:51:35] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -63.633084+0.006783j
[2025-09-19 09:52:07] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -63.633221-0.001313j
[2025-09-19 09:52:38] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -63.645244+0.000774j
[2025-09-19 09:53:10] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -63.632702-0.000734j
[2025-09-19 09:53:42] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -63.642668+0.000361j
[2025-09-19 09:54:14] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -63.630000-0.001752j
[2025-09-19 09:54:46] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -63.641550-0.005964j
[2025-09-19 09:55:18] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -63.617925+0.001808j
[2025-09-19 09:55:50] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -63.626894+0.001237j
[2025-09-19 09:56:22] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -63.628156-0.004011j
[2025-09-19 09:56:54] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -63.629718+0.004756j
[2025-09-19 09:57:25] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -63.640578+0.003169j
[2025-09-19 09:57:57] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -63.637113-0.008778j
[2025-09-19 09:58:29] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -63.632146+0.002073j
[2025-09-19 09:59:01] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -63.616280+0.001530j
[2025-09-19 09:59:33] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -63.648683+0.002168j
[2025-09-19 10:00:05] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -63.618752+0.000987j
[2025-09-19 10:00:37] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -63.652001+0.003105j
[2025-09-19 10:01:08] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -63.642220+0.003012j
[2025-09-19 10:01:40] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -63.654001+0.005281j
[2025-09-19 10:02:12] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -63.627713+0.000307j
[2025-09-19 10:02:44] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -63.652802-0.000668j
[2025-09-19 10:03:16] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -63.631870-0.005963j
[2025-09-19 10:03:48] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -63.614949-0.003422j
[2025-09-19 10:04:20] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -63.636521-0.001591j
[2025-09-19 10:04:51] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -63.641053-0.002840j
[2025-09-19 10:05:23] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -63.635410-0.001151j
[2025-09-19 10:05:55] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -63.645740+0.002118j
[2025-09-19 10:06:27] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -63.651429+0.001873j
[2025-09-19 10:06:59] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -63.627761-0.002217j
[2025-09-19 10:07:31] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -63.640553+0.006642j
[2025-09-19 10:08:03] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -63.634265+0.000673j
[2025-09-19 10:08:34] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -63.617984-0.002567j
[2025-09-19 10:09:06] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -63.642156+0.000493j
[2025-09-19 10:09:38] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -63.642078+0.001557j
[2025-09-19 10:10:10] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -63.615746-0.002793j
[2025-09-19 10:10:42] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -63.633243-0.002299j
[2025-09-19 10:11:14] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -63.627885-0.003848j
[2025-09-19 10:11:46] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -63.629371-0.003106j
[2025-09-19 10:12:17] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -63.649228-0.001347j
[2025-09-19 10:12:49] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -63.623792-0.002100j
[2025-09-19 10:13:21] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -63.632671-0.004021j
[2025-09-19 10:13:53] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -63.613493-0.002192j
[2025-09-19 10:14:25] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -63.646114-0.005072j
[2025-09-19 10:14:57] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -63.627257+0.002860j
[2025-09-19 10:15:29] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -63.631899+0.006509j
[2025-09-19 10:16:01] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -63.628764+0.002825j
[2025-09-19 10:16:33] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -63.635359-0.005877j
[2025-09-19 10:17:05] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -63.652011-0.001542j
[2025-09-19 10:17:37] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -63.632471-0.003919j
[2025-09-19 10:18:09] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -63.649885+0.002891j
[2025-09-19 10:18:40] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -63.637763+0.000049j
[2025-09-19 10:19:12] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -63.627743-0.000130j
[2025-09-19 10:19:44] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -63.626326+0.001034j
[2025-09-19 10:20:16] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -63.634348-0.001374j
[2025-09-19 10:20:48] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -63.634858-0.000774j
[2025-09-19 10:21:20] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -63.642477-0.001195j
[2025-09-19 10:21:20] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-19 10:21:52] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -63.640066-0.002624j
[2025-09-19 10:22:24] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -63.631342-0.003311j
[2025-09-19 10:22:56] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -63.619262+0.001520j
[2025-09-19 10:23:28] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -63.657668+0.000982j
[2025-09-19 10:23:59] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -63.637723-0.000844j
[2025-09-19 10:24:31] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -63.619123-0.005204j
[2025-09-19 10:25:03] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -63.644308-0.001595j
[2025-09-19 10:25:35] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -63.635058-0.000039j
[2025-09-19 10:26:07] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -63.631968+0.000113j
[2025-09-19 10:26:38] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -63.644273-0.001542j
[2025-09-19 10:27:10] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -63.639767+0.000273j
[2025-09-19 10:27:42] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -63.652758+0.002378j
[2025-09-19 10:28:14] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -63.646218-0.005195j
[2025-09-19 10:28:46] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -63.641735-0.001399j
[2025-09-19 10:29:17] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -63.629250+0.003504j
[2025-09-19 10:29:49] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -63.639789-0.004526j
[2025-09-19 10:30:21] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -63.652993+0.000455j
[2025-09-19 10:30:53] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -63.646062+0.002639j
[2025-09-19 10:31:25] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -63.626145-0.000915j
[2025-09-19 10:31:57] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -63.640450-0.003097j
[2025-09-19 10:32:29] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -63.627173-0.000543j
[2025-09-19 10:33:01] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -63.635815-0.000993j
[2025-09-19 10:33:32] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -63.626739-0.001232j
[2025-09-19 10:34:04] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -63.649649+0.002750j
[2025-09-19 10:34:36] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -63.628462+0.004419j
[2025-09-19 10:35:08] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -63.633148+0.001942j
[2025-09-19 10:35:40] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -63.628296-0.000097j
[2025-09-19 10:36:12] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -63.619907+0.000498j
[2025-09-19 10:36:44] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -63.653324-0.000078j
[2025-09-19 10:37:16] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -63.642427-0.000056j
[2025-09-19 10:37:47] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -63.635952-0.001192j
[2025-09-19 10:38:19] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -63.654372+0.000949j
[2025-09-19 10:38:51] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -63.635858-0.001125j
[2025-09-19 10:39:23] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -63.623556+0.002055j
[2025-09-19 10:39:55] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -63.640576-0.003192j
[2025-09-19 10:40:27] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -63.623969-0.003124j
[2025-09-19 10:40:59] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -63.627691-0.000105j
[2025-09-19 10:41:31] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -63.636749+0.002822j
[2025-09-19 10:42:02] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -63.635414-0.003829j
[2025-09-19 10:42:34] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -63.622880+0.000149j
[2025-09-19 10:43:06] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -63.619283+0.001767j
[2025-09-19 10:43:38] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -63.620077+0.001758j
[2025-09-19 10:44:10] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -63.648592+0.000711j
[2025-09-19 10:44:42] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -63.623445-0.000468j
[2025-09-19 10:45:14] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -63.637133+0.003240j
[2025-09-19 10:45:46] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -63.635511+0.000211j
[2025-09-19 10:46:17] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -63.646972+0.001686j
[2025-09-19 10:46:49] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -63.629960-0.003915j
[2025-09-19 10:47:21] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -63.632454-0.001759j
[2025-09-19 10:47:53] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -63.617726-0.002399j
[2025-09-19 10:48:25] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -63.624608+0.003201j
[2025-09-19 10:48:57] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -63.621712-0.004252j
[2025-09-19 10:49:29] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -63.638461+0.001443j
[2025-09-19 10:50:00] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -63.615921-0.000269j
[2025-09-19 10:50:32] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -63.632495-0.001837j
[2025-09-19 10:51:04] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -63.630935-0.001657j
[2025-09-19 10:51:36] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -63.646104+0.000822j
[2025-09-19 10:52:08] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -63.652116+0.002419j
[2025-09-19 10:52:40] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -63.652670-0.005709j
[2025-09-19 10:53:11] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -63.634916-0.006541j
[2025-09-19 10:53:43] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -63.654537+0.001692j
[2025-09-19 10:54:15] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -63.628703-0.002925j
[2025-09-19 10:54:47] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -63.629939+0.004497j
[2025-09-19 10:55:19] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -63.643163-0.001049j
[2025-09-19 10:55:51] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -63.629611+0.003057j
[2025-09-19 10:56:22] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -63.632873-0.000488j
[2025-09-19 10:56:54] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -63.624209+0.001891j
[2025-09-19 10:57:26] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -63.638840+0.005066j
[2025-09-19 10:57:58] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -63.641286+0.002426j
[2025-09-19 10:58:30] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -63.638239+0.000045j
[2025-09-19 10:59:02] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -63.636180-0.001922j
[2025-09-19 10:59:34] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -63.642878+0.002806j
[2025-09-19 11:00:06] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -63.660311+0.000767j
[2025-09-19 11:00:37] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -63.651349-0.001709j
[2025-09-19 11:01:09] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -63.624708+0.000883j
[2025-09-19 11:01:41] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -63.618087+0.000447j
[2025-09-19 11:02:13] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -63.632162-0.005351j
[2025-09-19 11:02:45] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -63.638623-0.001655j
[2025-09-19 11:03:17] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -63.624343+0.006571j
[2025-09-19 11:03:49] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -63.638070-0.003891j
[2025-09-19 11:04:20] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -63.630694-0.001587j
[2025-09-19 11:04:52] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -63.639363-0.001170j
[2025-09-19 11:05:24] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -63.636595-0.000487j
[2025-09-19 11:05:56] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -63.637270+0.001459j
[2025-09-19 11:06:28] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -63.624585+0.004699j
[2025-09-19 11:07:00] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -63.644681+0.000195j
[2025-09-19 11:07:32] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -63.627859+0.002244j
[2025-09-19 11:08:04] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -63.624456+0.004248j
[2025-09-19 11:08:36] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -63.640574-0.003037j
[2025-09-19 11:09:07] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -63.631995+0.005909j
[2025-09-19 11:09:39] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -63.632375-0.004008j
[2025-09-19 11:10:11] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -63.643989+0.003371j
[2025-09-19 11:10:43] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -63.633158+0.002232j
[2025-09-19 11:11:15] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -63.639785+0.003354j
[2025-09-19 11:11:47] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -63.641559+0.002336j
[2025-09-19 11:12:19] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -63.645213+0.001075j
[2025-09-19 11:12:51] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -63.639610+0.001800j
[2025-09-19 11:13:22] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -63.633577+0.000268j
[2025-09-19 11:13:54] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -63.620340+0.000351j
[2025-09-19 11:14:26] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -63.627458+0.001905j
[2025-09-19 11:14:58] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -63.639442+0.001174j
[2025-09-19 11:15:30] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -63.637327+0.003768j
[2025-09-19 11:16:02] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -63.648961+0.003425j
[2025-09-19 11:16:34] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -63.620198-0.001009j
[2025-09-19 11:17:05] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -63.631124+0.003309j
[2025-09-19 11:17:05] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-19 11:17:37] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -63.627573-0.002075j
[2025-09-19 11:18:09] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -63.642265-0.003640j
[2025-09-19 11:18:41] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -63.645124-0.004255j
[2025-09-19 11:19:13] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -63.638925+0.000791j
[2025-09-19 11:19:45] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -63.639311-0.000753j
[2025-09-19 11:20:16] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -63.630847-0.004087j
[2025-09-19 11:20:48] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -63.637201-0.001290j
[2025-09-19 11:21:20] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -63.655756-0.000356j
[2025-09-19 11:21:52] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -63.651146-0.002081j
[2025-09-19 11:22:24] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -63.643666-0.004176j
[2025-09-19 11:22:56] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -63.634291-0.002227j
[2025-09-19 11:23:28] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -63.623562+0.002391j
[2025-09-19 11:23:59] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -63.625568+0.004479j
[2025-09-19 11:24:31] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -63.618960+0.001517j
[2025-09-19 11:25:03] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -63.655969-0.000773j
[2025-09-19 11:25:35] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -63.656740-0.000363j
[2025-09-19 11:26:07] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -63.638465+0.000509j
[2025-09-19 11:26:39] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -63.635062-0.002679j
[2025-09-19 11:27:11] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -63.639629-0.000641j
[2025-09-19 11:27:42] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -63.638745+0.000561j
[2025-09-19 11:28:14] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -63.639734-0.001532j
[2025-09-19 11:28:46] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -63.630230-0.002534j
[2025-09-19 11:29:18] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -63.622309-0.003963j
[2025-09-19 11:29:50] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -63.645858-0.000067j
[2025-09-19 11:30:22] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -63.637301+0.000217j
[2025-09-19 11:30:54] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -63.628982-0.004198j
[2025-09-19 11:31:25] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -63.628952+0.000382j
[2025-09-19 11:31:57] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -63.642395+0.001034j
[2025-09-19 11:32:29] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -63.620000+0.004848j
[2025-09-19 11:33:01] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -63.645299+0.003056j
[2025-09-19 11:33:33] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -63.645041-0.002258j
[2025-09-19 11:34:05] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -63.651428+0.001733j
[2025-09-19 11:34:37] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -63.635960+0.003140j
[2025-09-19 11:35:09] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -63.657410-0.006777j
[2025-09-19 11:35:40] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -63.639299-0.006645j
[2025-09-19 11:36:12] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -63.629948-0.001676j
[2025-09-19 11:36:44] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -63.653427+0.001047j
[2025-09-19 11:37:16] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -63.650551+0.001183j
[2025-09-19 11:37:48] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -63.626430-0.004203j
[2025-09-19 11:38:20] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -63.642351-0.004297j
[2025-09-19 11:38:52] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -63.630846+0.005561j
[2025-09-19 11:39:24] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -63.644901+0.003311j
[2025-09-19 11:39:55] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -63.631205-0.000331j
[2025-09-19 11:40:27] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -63.620883+0.001063j
[2025-09-19 11:40:59] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -63.621169-0.001156j
[2025-09-19 11:41:31] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -63.669652-0.001035j
[2025-09-19 11:42:03] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -63.628968+0.002225j
[2025-09-19 11:42:35] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -63.647947-0.001431j
[2025-09-19 11:43:07] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -63.639893+0.002603j
[2025-09-19 11:43:38] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -63.626092+0.001125j
[2025-09-19 11:44:10] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -63.635488-0.001918j
[2025-09-19 11:44:42] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -63.628793-0.012345j
[2025-09-19 11:45:14] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -63.649271-0.001487j
[2025-09-19 11:45:46] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -63.636270+0.002229j
[2025-09-19 11:46:18] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -63.645843-0.002448j
[2025-09-19 11:46:50] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -63.653142+0.001938j
[2025-09-19 11:47:21] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -63.650292+0.001178j
[2025-09-19 11:47:53] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -63.642293-0.004320j
[2025-09-19 11:48:25] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -63.637232-0.000300j
[2025-09-19 11:48:57] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -63.620967-0.002376j
[2025-09-19 11:49:29] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -63.633425-0.004995j
[2025-09-19 11:50:01] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -63.646990+0.001495j
[2025-09-19 11:50:33] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -63.636868-0.002205j
[2025-09-19 11:51:05] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -63.638243-0.000096j
[2025-09-19 11:51:36] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -63.610503-0.003124j
[2025-09-19 11:52:08] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -63.641928-0.000673j
[2025-09-19 11:52:40] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -63.651466+0.001838j
[2025-09-19 11:53:12] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -63.630986-0.001009j
[2025-09-19 11:53:44] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -63.644670+0.002471j
[2025-09-19 11:54:16] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -63.646723+0.000083j
[2025-09-19 11:54:48] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -63.649789+0.000842j
[2025-09-19 11:55:20] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -63.620718-0.000036j
[2025-09-19 11:55:52] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -63.626406+0.002237j
[2025-09-19 11:56:23] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -63.664548+0.004057j
[2025-09-19 11:56:55] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -63.632464-0.009058j
[2025-09-19 11:57:27] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -63.635286-0.000685j
[2025-09-19 11:57:59] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -63.624576-0.002927j
[2025-09-19 11:58:31] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -63.634868+0.001950j
[2025-09-19 11:59:03] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -63.649512-0.000078j
[2025-09-19 11:59:35] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -63.612756-0.002149j
[2025-09-19 12:00:07] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -63.642111+0.002835j
[2025-09-19 12:00:38] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -63.646055-0.000055j
[2025-09-19 12:01:10] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -63.643942-0.000581j
[2025-09-19 12:01:42] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -63.643998+0.002695j
[2025-09-19 12:02:14] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -63.635914-0.000015j
[2025-09-19 12:02:46] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -63.631930+0.002337j
[2025-09-19 12:03:18] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -63.640841-0.007233j
[2025-09-19 12:03:50] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -63.651252-0.002897j
[2025-09-19 12:04:22] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -63.653191+0.002692j
[2025-09-19 12:04:53] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -63.651699-0.004773j
[2025-09-19 12:05:25] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -63.638870-0.006283j
[2025-09-19 12:05:57] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -63.631112-0.003571j
[2025-09-19 12:06:29] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -63.613428-0.000030j
[2025-09-19 12:07:01] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -63.628975-0.000535j
[2025-09-19 12:07:33] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -63.625292+0.001830j
[2025-09-19 12:08:04] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -63.638734+0.000747j
[2025-09-19 12:08:36] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -63.641992-0.001666j
[2025-09-19 12:09:08] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -63.652615-0.002419j
[2025-09-19 12:09:40] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -63.643832-0.000366j
[2025-09-19 12:10:12] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -63.604274+0.002140j
[2025-09-19 12:10:44] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -63.644550-0.001177j
[2025-09-19 12:11:15] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -63.623189-0.003207j
[2025-09-19 12:11:47] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -63.652076-0.002776j
[2025-09-19 12:12:19] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -63.646291+0.000194j
[2025-09-19 12:12:51] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -63.640701-0.003705j
[2025-09-19 12:12:51] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-19 12:13:23] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -63.638890-0.001387j
[2025-09-19 12:13:55] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -63.645816+0.000754j
[2025-09-19 12:14:27] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -63.620585-0.003388j
[2025-09-19 12:14:58] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -63.643148+0.001868j
[2025-09-19 12:15:30] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -63.639748-0.001206j
[2025-09-19 12:16:02] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -63.627094+0.003431j
[2025-09-19 12:16:34] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -63.638768-0.003329j
[2025-09-19 12:17:06] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -63.627404-0.003427j
[2025-09-19 12:17:38] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -63.632803+0.000564j
[2025-09-19 12:18:09] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -63.639190-0.000861j
[2025-09-19 12:18:41] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -63.621538-0.000521j
[2025-09-19 12:19:13] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -63.648384-0.003958j
[2025-09-19 12:19:45] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -63.626206+0.001591j
[2025-09-19 12:20:17] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -63.645189-0.002695j
[2025-09-19 12:20:49] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -63.635555-0.001692j
[2025-09-19 12:21:21] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -63.648046-0.005124j
[2025-09-19 12:21:52] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -63.619252+0.003842j
[2025-09-19 12:22:24] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -63.627178+0.000728j
[2025-09-19 12:22:56] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -63.643972+0.001071j
[2025-09-19 12:23:28] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -63.631494-0.002659j
[2025-09-19 12:24:00] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -63.642808+0.002220j
[2025-09-19 12:24:32] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -63.650215-0.000595j
[2025-09-19 12:25:04] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -63.639583+0.002314j
[2025-09-19 12:25:36] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -63.640638-0.002650j
[2025-09-19 12:26:07] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -63.636678-0.003462j
[2025-09-19 12:26:39] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -63.632129+0.004010j
[2025-09-19 12:27:11] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -63.665377-0.002770j
[2025-09-19 12:27:43] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -63.629837-0.003961j
[2025-09-19 12:28:15] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -63.618571+0.004832j
[2025-09-19 12:28:47] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -63.634800+0.004246j
[2025-09-19 12:29:19] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -63.648454+0.004505j
[2025-09-19 12:29:51] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -63.645708+0.001110j
[2025-09-19 12:30:22] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -63.631239+0.000479j
[2025-09-19 12:30:54] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -63.628127+0.000617j
[2025-09-19 12:31:26] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -63.626921+0.004453j
[2025-09-19 12:31:58] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -63.661901+0.004633j
[2025-09-19 12:32:30] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -63.628617-0.006111j
[2025-09-19 12:33:02] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -63.644178+0.000246j
[2025-09-19 12:33:34] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -63.657790+0.000332j
[2025-09-19 12:34:05] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -63.640008+0.004799j
[2025-09-19 12:34:37] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -63.642214+0.000043j
[2025-09-19 12:35:09] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -63.631377+0.002034j
[2025-09-19 12:35:41] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -63.651716-0.000295j
[2025-09-19 12:36:13] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -63.655535+0.003464j
[2025-09-19 12:36:45] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -63.651645-0.002180j
[2025-09-19 12:37:17] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -63.635776+0.004785j
[2025-09-19 12:37:48] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -63.643131+0.003711j
[2025-09-19 12:38:20] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -63.631424-0.006889j
[2025-09-19 12:38:52] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -63.621949+0.000735j
[2025-09-19 12:39:24] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -63.651123+0.001614j
[2025-09-19 12:39:56] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -63.633681-0.000232j
[2025-09-19 12:40:28] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -63.636512-0.003092j
[2025-09-19 12:41:00] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -63.636593+0.004125j
[2025-09-19 12:41:32] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -63.631206+0.000874j
[2025-09-19 12:42:04] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -63.630468-0.001510j
[2025-09-19 12:42:35] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -63.641562+0.004052j
[2025-09-19 12:43:07] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -63.646014-0.001264j
[2025-09-19 12:43:39] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -63.642175+0.000815j
[2025-09-19 12:44:11] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -63.653539-0.000303j
[2025-09-19 12:44:43] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -63.649089+0.000558j
[2025-09-19 12:45:15] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -63.639002-0.001340j
[2025-09-19 12:45:47] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -63.630080-0.003789j
[2025-09-19 12:46:19] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -63.618306+0.001449j
[2025-09-19 12:46:50] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -63.653048+0.000495j
[2025-09-19 12:47:22] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -63.641257-0.002355j
[2025-09-19 12:47:54] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -63.635422+0.002449j
[2025-09-19 12:48:26] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -63.645189+0.004291j
[2025-09-19 12:48:58] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -63.655859+0.000140j
[2025-09-19 12:49:30] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -63.619266-0.000322j
[2025-09-19 12:50:02] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -63.639058-0.010816j
[2025-09-19 12:50:33] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -63.643565+0.001071j
[2025-09-19 12:51:05] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -63.649011+0.000212j
[2025-09-19 12:51:37] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -63.621191-0.000007j
[2025-09-19 12:52:09] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -63.661622-0.001374j
[2025-09-19 12:52:41] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -63.642638+0.005359j
[2025-09-19 12:53:13] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -63.638806+0.000604j
[2025-09-19 12:53:45] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -63.646299+0.001917j
[2025-09-19 12:54:17] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -63.640330-0.001303j
[2025-09-19 12:54:49] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -63.631733-0.004203j
[2025-09-19 12:55:20] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -63.621459-0.000626j
[2025-09-19 12:55:52] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -63.639614-0.002477j
[2025-09-19 12:56:24] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -63.654200+0.000222j
[2025-09-19 12:56:56] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -63.656384-0.001650j
[2025-09-19 12:57:28] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -63.645535-0.000645j
[2025-09-19 12:58:00] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -63.625979+0.002479j
[2025-09-19 12:58:32] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -63.633372+0.002472j
[2025-09-19 12:59:03] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -63.638600+0.002118j
[2025-09-19 12:59:35] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -63.645230+0.006355j
[2025-09-19 13:00:07] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -63.632043-0.002468j
[2025-09-19 13:00:39] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -63.622587+0.000834j
[2025-09-19 13:01:11] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -63.635180+0.000931j
[2025-09-19 13:01:43] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -63.645996-0.002830j
[2025-09-19 13:02:14] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -63.646141-0.001138j
[2025-09-19 13:02:46] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -63.636564-0.002917j
[2025-09-19 13:03:18] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -63.623861-0.001454j
[2025-09-19 13:03:50] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -63.639390+0.006372j
[2025-09-19 13:04:22] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -63.617499-0.005090j
[2025-09-19 13:04:54] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -63.638440+0.004958j
[2025-09-19 13:05:26] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -63.635017+0.000011j
[2025-09-19 13:05:57] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -63.655478+0.001109j
[2025-09-19 13:06:29] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -63.627297+0.001530j
[2025-09-19 13:07:01] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -63.611779+0.000182j
[2025-09-19 13:07:33] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -63.641688-0.006466j
[2025-09-19 13:08:05] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -63.639375-0.002124j
[2025-09-19 13:08:37] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -63.633896+0.001013j
[2025-09-19 13:08:37] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-19 13:09:09] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -63.621108+0.002228j
[2025-09-19 13:09:40] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -63.646270+0.001705j
[2025-09-19 13:10:12] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -63.629021-0.001887j
[2025-09-19 13:10:44] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -63.642144+0.001530j
[2025-09-19 13:11:16] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -63.638617-0.002177j
[2025-09-19 13:11:48] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -63.647212-0.000415j
[2025-09-19 13:12:20] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -63.656980-0.002577j
[2025-09-19 13:12:51] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -63.645091-0.001154j
[2025-09-19 13:13:23] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -63.636266+0.000724j
[2025-09-19 13:13:55] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -63.637082-0.008056j
[2025-09-19 13:14:27] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -63.643562+0.002492j
[2025-09-19 13:14:59] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -63.655319-0.001851j
[2025-09-19 13:15:30] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -63.653166-0.001679j
[2025-09-19 13:16:02] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -63.629178-0.000124j
[2025-09-19 13:16:34] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -63.628996+0.000392j
[2025-09-19 13:17:06] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -63.641846+0.004193j
[2025-09-19 13:17:38] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -63.642535-0.005285j
[2025-09-19 13:18:10] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -63.633158-0.003696j
[2025-09-19 13:18:42] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -63.651227-0.002454j
[2025-09-19 13:19:14] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -63.645680-0.000456j
[2025-09-19 13:19:45] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -63.653864+0.001909j
[2025-09-19 13:20:17] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -63.656410-0.003248j
[2025-09-19 13:20:49] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -63.642512-0.003742j
[2025-09-19 13:21:21] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -63.640959-0.003678j
[2025-09-19 13:21:53] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -63.651183+0.001390j
[2025-09-19 13:22:25] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -63.625593-0.003746j
[2025-09-19 13:22:57] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -63.637824+0.002341j
[2025-09-19 13:23:29] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -63.641433-0.002611j
[2025-09-19 13:24:00] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -63.640875-0.001954j
[2025-09-19 13:24:32] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -63.652293+0.003895j
[2025-09-19 13:25:04] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -63.632530+0.001697j
[2025-09-19 13:25:36] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -63.652866+0.001109j
[2025-09-19 13:26:08] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -63.657185+0.002026j
[2025-09-19 13:26:40] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -63.655526-0.004054j
[2025-09-19 13:27:11] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -63.624307-0.004218j
[2025-09-19 13:27:43] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -63.628453-0.003151j
[2025-09-19 13:28:15] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -63.646049-0.000604j
[2025-09-19 13:28:47] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -63.648490-0.001319j
[2025-09-19 13:29:19] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -63.637110+0.002970j
[2025-09-19 13:29:51] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -63.642821-0.004003j
[2025-09-19 13:30:23] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -63.648833-0.000294j
[2025-09-19 13:30:55] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -63.638105+0.001685j
[2025-09-19 13:31:27] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -63.630116-0.001127j
[2025-09-19 13:31:58] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -63.653905+0.001732j
[2025-09-19 13:32:30] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -63.657234+0.001406j
[2025-09-19 13:33:02] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -63.634240+0.001839j
[2025-09-19 13:33:34] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -63.676058-0.000979j
[2025-09-19 13:34:06] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -63.641378-0.000559j
[2025-09-19 13:34:38] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -63.657336-0.002654j
[2025-09-19 13:35:10] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -63.640802+0.000389j
[2025-09-19 13:35:42] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -63.651687+0.001485j
[2025-09-19 13:36:13] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -63.645007+0.000101j
[2025-09-19 13:36:45] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -63.639317-0.000049j
[2025-09-19 13:37:17] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -63.652093-0.003913j
[2025-09-19 13:37:49] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -63.643736+0.000000j
[2025-09-19 13:38:21] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -63.654692-0.000450j
[2025-09-19 13:38:52] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -63.640022+0.001979j
[2025-09-19 13:39:24] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -63.655024+0.003697j
[2025-09-19 13:39:56] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -63.625986-0.000912j
[2025-09-19 13:40:28] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -63.645062+0.003309j
[2025-09-19 13:40:59] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -63.641604+0.000567j
[2025-09-19 13:41:31] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -63.645187+0.002480j
[2025-09-19 13:42:03] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -63.631449-0.001875j
[2025-09-19 13:42:35] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -63.630147+0.002197j
[2025-09-19 13:43:07] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -63.635520-0.001594j
[2025-09-19 13:43:39] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -63.639675-0.000815j
[2025-09-19 13:44:10] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -63.641344+0.001880j
[2025-09-19 13:44:42] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -63.654807-0.002884j
[2025-09-19 13:45:14] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -63.644982+0.000876j
[2025-09-19 13:45:46] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -63.632181-0.000909j
[2025-09-19 13:46:18] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -63.646607+0.006497j
[2025-09-19 13:46:50] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -63.650763-0.023319j
[2025-09-19 13:47:21] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -63.666407-0.001074j
[2025-09-19 13:47:53] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -63.646953+0.005143j
[2025-09-19 13:48:25] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -63.643215+0.000519j
[2025-09-19 13:48:57] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -63.625721-0.000716j
[2025-09-19 13:49:29] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -63.659960+0.001861j
[2025-09-19 13:50:01] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -63.624447-0.002073j
[2025-09-19 13:50:32] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -63.627060-0.000716j
[2025-09-19 13:51:04] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -63.644870+0.003069j
[2025-09-19 13:51:36] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -63.616364-0.000482j
[2025-09-19 13:52:08] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -63.624214-0.001877j
[2025-09-19 13:52:40] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -63.634164+0.000038j
[2025-09-19 13:53:12] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -63.631788+0.002233j
[2025-09-19 13:53:44] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -63.652847-0.002797j
[2025-09-19 13:54:15] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -63.651949-0.001744j
[2025-09-19 13:54:47] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -63.637699-0.000906j
[2025-09-19 13:55:19] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -63.645168-0.002056j
[2025-09-19 13:55:51] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -63.648526-0.005084j
[2025-09-19 13:56:23] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -63.636352+0.001387j
[2025-09-19 13:56:55] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -63.667716-0.002133j
[2025-09-19 13:57:27] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -63.649522-0.002077j
[2025-09-19 13:57:58] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -63.652677-0.000126j
[2025-09-19 13:58:30] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -63.642656+0.004631j
[2025-09-19 13:59:02] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -63.638963+0.001453j
[2025-09-19 13:59:34] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -63.632543+0.000587j
[2025-09-19 14:00:06] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -63.634938-0.000869j
[2025-09-19 14:00:38] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -63.627616-0.001396j
[2025-09-19 14:01:09] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -63.637436+0.000117j
[2025-09-19 14:01:41] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -63.646285+0.000201j
[2025-09-19 14:02:13] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -63.652570+0.007624j
[2025-09-19 14:02:45] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -63.625896+0.001135j
[2025-09-19 14:03:17] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -63.651372-0.000627j
[2025-09-19 14:03:49] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -63.639115+0.001900j
[2025-09-19 14:04:21] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -63.618870-0.000115j
[2025-09-19 14:04:21] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-19 14:04:21] ✅ Training completed | Restarts: 2
[2025-09-19 14:04:21] ============================================================
[2025-09-19 14:04:21] Training completed | Runtime: 33540.0s
[2025-09-19 14:04:32] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-19 14:04:32] ============================================================
