[2025-09-19 14:07:49] ✓ 从checkpoint恢复: results/L=6/J2=1.00/J1=0.79/training/checkpoints/final_GCNN.pkl
[2025-09-19 14:07:49]   - 迭代次数: final
[2025-09-19 14:07:49]   - 能量: -63.618870-0.000115j ± 0.010538
[2025-09-19 14:07:49]   - 时间戳: 2025-09-19T14:04:32.433821+08:00
[2025-09-19 14:08:10] ✓ 变分状态参数已从checkpoint恢复
[2025-09-19 14:08:10] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-19 14:08:10] ==================================================
[2025-09-19 14:08:10] GCNN for Shastry-Sutherland Model
[2025-09-19 14:08:10] ==================================================
[2025-09-19 14:08:10] System parameters:
[2025-09-19 14:08:10]   - System size: L=6, N=144
[2025-09-19 14:08:10]   - System parameters: J1=0.78, J2=1.0, Q=0.0
[2025-09-19 14:08:10] --------------------------------------------------
[2025-09-19 14:08:10] Model parameters:
[2025-09-19 14:08:10]   - Number of layers = 4
[2025-09-19 14:08:10]   - Number of features = 4
[2025-09-19 14:08:10]   - Total parameters = 28252
[2025-09-19 14:08:10] --------------------------------------------------
[2025-09-19 14:08:10] Training parameters:
[2025-09-19 14:08:10]   - Learning rate: 0.015
[2025-09-19 14:08:10]   - Total iterations: 1050
[2025-09-19 14:08:10]   - Annealing cycles: 3
[2025-09-19 14:08:10]   - Initial period: 150
[2025-09-19 14:08:10]   - Period multiplier: 2.0
[2025-09-19 14:08:10]   - Temperature range: 0.0-1.0
[2025-09-19 14:08:10]   - Samples: 4096
[2025-09-19 14:08:10]   - Discarded samples: 0
[2025-09-19 14:08:10]   - Chunk size: 2048
[2025-09-19 14:08:10]   - Diagonal shift: 0.2
[2025-09-19 14:08:10]   - Gradient clipping: 1.0
[2025-09-19 14:08:10]   - Checkpoint enabled: interval=105
[2025-09-19 14:08:10]   - Checkpoint directory: results/L=6/J2=1.00/J1=0.78/training/checkpoints
[2025-09-19 14:08:10] --------------------------------------------------
[2025-09-19 14:08:10] Device status:
[2025-09-19 14:08:10]   - Devices model: NVIDIA H200 NVL
[2025-09-19 14:08:10]   - Number of devices: 1
[2025-09-19 14:08:10]   - Sharding: True
[2025-09-19 14:08:10] ============================================================
[2025-09-19 14:09:56] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -20.772052-0.111219j
[2025-09-19 14:11:08] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -21.769381-0.124688j
[2025-09-19 14:11:40] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -23.068795+0.068995j
[2025-09-19 14:12:12] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -24.915828+0.006203j
[2025-09-19 14:12:44] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -27.794730-0.047717j
[2025-09-19 14:13:15] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -32.215713-0.026754j
[2025-09-19 14:13:47] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -38.528851-0.030634j
[2025-09-19 14:14:19] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -46.011393-0.092652j
[2025-09-19 14:14:51] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -53.137220+0.011706j
[2025-09-19 14:15:23] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -57.946779-0.042998j
[2025-09-19 14:15:55] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -60.941852-0.007641j
[2025-09-19 14:16:27] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -62.170233+0.014499j
[2025-09-19 14:16:59] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -62.654751+0.003038j
[2025-09-19 14:17:31] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -62.709667-0.032050j
[2025-09-19 14:18:02] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -62.714254+0.004064j
[2025-09-19 14:18:34] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -62.727143-0.002163j
[2025-09-19 14:19:06] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -62.733288-0.001171j
[2025-09-19 14:19:38] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -62.715606-0.004882j
[2025-09-19 14:20:10] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -62.722663+0.000744j
[2025-09-19 14:20:42] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -62.709771-0.000727j
[2025-09-19 14:21:14] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -62.734112-0.001778j
[2025-09-19 14:21:46] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -62.724582+0.001270j
[2025-09-19 14:22:18] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -62.717978-0.001774j
[2025-09-19 14:22:50] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -62.714478-0.001119j
[2025-09-19 14:23:22] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -62.716480-0.000120j
[2025-09-19 14:23:54] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -62.701739+0.002176j
[2025-09-19 14:24:25] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -62.710452-0.002435j
[2025-09-19 14:24:57] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -62.716475-0.006035j
[2025-09-19 14:25:29] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -62.735510-0.001486j
[2025-09-19 14:26:01] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -62.729384+0.004005j
[2025-09-19 14:26:33] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -62.709494-0.001702j
[2025-09-19 14:27:05] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -62.717683+0.000417j
[2025-09-19 14:27:37] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -62.713495+0.003517j
[2025-09-19 14:28:09] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -62.724355-0.002864j
[2025-09-19 14:28:41] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -62.743594-0.000604j
[2025-09-19 14:29:13] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -62.718038+0.006036j
[2025-09-19 14:29:44] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -62.707320+0.000722j
[2025-09-19 14:30:16] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -62.732488-0.000442j
[2025-09-19 14:30:48] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -62.727327-0.003769j
[2025-09-19 14:31:20] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -62.725662+0.000596j
[2025-09-19 14:31:52] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -62.740096+0.003055j
[2025-09-19 14:32:24] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -62.726622+0.002664j
[2025-09-19 14:32:56] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -62.713794-0.008904j
[2025-09-19 14:33:28] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -62.701141-0.001224j
[2025-09-19 14:34:00] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -62.719722+0.004059j
[2025-09-19 14:34:32] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -62.729684-0.008158j
[2025-09-19 14:35:03] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -62.728107+0.004017j
[2025-09-19 14:35:35] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -62.721501+0.002347j
[2025-09-19 14:36:07] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -62.722174+0.001695j
[2025-09-19 14:36:39] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -62.716306+0.001274j
[2025-09-19 14:37:11] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -62.707548-0.004046j
[2025-09-19 14:37:43] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -62.714095-0.006664j
[2025-09-19 14:38:15] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -62.744943+0.001043j
[2025-09-19 14:38:47] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -62.707110+0.003996j
[2025-09-19 14:39:19] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -62.721464-0.002088j
[2025-09-19 14:39:51] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -62.722450+0.007459j
[2025-09-19 14:40:23] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -62.735915+0.002399j
[2025-09-19 14:40:55] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -62.734648-0.000304j
[2025-09-19 14:41:27] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -62.726127+0.000478j
[2025-09-19 14:41:59] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -62.735013-0.000278j
[2025-09-19 14:42:31] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -62.714501-0.002900j
[2025-09-19 14:43:03] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -62.725927-0.002036j
[2025-09-19 14:43:35] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -62.717969+0.000390j
[2025-09-19 14:44:07] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -62.731720+0.000349j
[2025-09-19 14:44:39] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -62.708784+0.001827j
[2025-09-19 14:45:11] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -62.719704-0.003381j
[2025-09-19 14:45:43] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -62.722724+0.001151j
[2025-09-19 14:46:15] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -62.716098-0.002716j
[2025-09-19 14:46:47] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -62.722709+0.001290j
[2025-09-19 14:47:19] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -62.741146-0.004898j
[2025-09-19 14:47:51] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -62.716557+0.007273j
[2025-09-19 14:48:23] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -62.712216+0.000780j
[2025-09-19 14:48:55] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -62.710901+0.003039j
[2025-09-19 14:49:27] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -62.730341+0.000282j
[2025-09-19 14:49:59] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -62.721838-0.002622j
[2025-09-19 14:50:31] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -62.743347+0.000255j
[2025-09-19 14:51:03] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -62.716044-0.000051j
[2025-09-19 14:51:34] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -62.721794-0.000138j
[2025-09-19 14:52:06] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -62.724776-0.001297j
[2025-09-19 14:52:38] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -62.739720-0.000684j
[2025-09-19 14:53:10] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -62.719572-0.005199j
[2025-09-19 14:53:42] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -62.735434-0.001615j
[2025-09-19 14:54:14] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -62.736366-0.003331j
[2025-09-19 14:54:46] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -62.721816+0.004922j
[2025-09-19 14:55:18] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -62.726280-0.004425j
[2025-09-19 14:55:50] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -62.718222-0.000580j
[2025-09-19 14:56:22] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -62.737368+0.002207j
[2025-09-19 14:56:54] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -62.735417+0.002138j
[2025-09-19 14:57:26] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -62.719395-0.000148j
[2025-09-19 14:57:57] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -62.722325+0.000011j
[2025-09-19 14:58:29] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -62.713825+0.000729j
[2025-09-19 14:59:01] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -62.721792-0.002970j
[2025-09-19 14:59:33] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -62.720026+0.001417j
[2025-09-19 15:00:05] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -62.731682+0.000181j
[2025-09-19 15:00:37] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -62.722810+0.003215j
[2025-09-19 15:01:09] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -62.716525-0.000085j
[2025-09-19 15:01:41] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -62.748439+0.001363j
[2025-09-19 15:02:12] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -62.730193-0.001305j
[2025-09-19 15:02:44] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -62.728303+0.000506j
[2025-09-19 15:03:16] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -62.704109+0.000923j
[2025-09-19 15:03:48] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -62.724033+0.001393j
[2025-09-19 15:04:20] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -62.702014-0.004609j
[2025-09-19 15:04:52] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -62.730009-0.002619j
[2025-09-19 15:05:24] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -62.728167+0.000665j
[2025-09-19 15:05:56] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -62.739840+0.000530j
[2025-09-19 15:05:56] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-19 15:06:27] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -62.728923+0.001740j
[2025-09-19 15:06:59] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -62.752328+0.004279j
[2025-09-19 15:07:31] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -62.727157+0.004384j
[2025-09-19 15:08:03] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -62.724498-0.002035j
[2025-09-19 15:08:35] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -62.715090+0.001278j
[2025-09-19 15:09:07] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -62.750443-0.001614j
[2025-09-19 15:09:39] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -62.730884+0.003253j
[2025-09-19 15:10:11] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -62.721021-0.000741j
[2025-09-19 15:10:43] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -62.695591+0.006834j
[2025-09-19 15:11:15] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -62.719069-0.000001j
[2025-09-19 15:11:47] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -62.729927-0.001276j
[2025-09-19 15:12:18] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -62.728678-0.001765j
[2025-09-19 15:12:50] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -62.724671-0.002416j
[2025-09-19 15:13:22] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -62.726082-0.001147j
[2025-09-19 15:13:54] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -62.734577-0.000570j
[2025-09-19 15:14:26] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -62.725037-0.001545j
[2025-09-19 15:14:58] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -62.723240+0.002974j
[2025-09-19 15:15:30] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -62.728416+0.008443j
[2025-09-19 15:16:02] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -62.717555+0.000126j
[2025-09-19 15:16:34] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -62.722007+0.003303j
[2025-09-19 15:17:06] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -62.729636+0.000956j
[2025-09-19 15:17:38] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -62.722849+0.004455j
[2025-09-19 15:18:10] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -62.729612-0.002207j
[2025-09-19 15:18:42] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -62.724907+0.002849j
[2025-09-19 15:19:14] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -62.731396+0.001994j
[2025-09-19 15:19:46] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -62.737427+0.001749j
[2025-09-19 15:20:18] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -62.732626+0.000009j
[2025-09-19 15:20:50] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -62.722327-0.000760j
[2025-09-19 15:21:21] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -62.732486-0.000535j
[2025-09-19 15:21:53] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -62.716162-0.000331j
[2025-09-19 15:22:25] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -62.727787-0.001403j
[2025-09-19 15:22:57] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -62.733689+0.002567j
[2025-09-19 15:23:29] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -62.723682-0.001942j
[2025-09-19 15:24:01] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -62.719305+0.001551j
[2025-09-19 15:24:33] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -62.716912-0.002359j
[2025-09-19 15:25:05] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -62.731938-0.003325j
[2025-09-19 15:25:37] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -62.741818-0.001281j
[2025-09-19 15:26:09] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -62.723596-0.003354j
[2025-09-19 15:26:41] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -62.721557+0.004085j
[2025-09-19 15:27:13] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -62.730035-0.001045j
[2025-09-19 15:27:44] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -62.720574-0.001787j
[2025-09-19 15:28:16] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -62.734835+0.000813j
[2025-09-19 15:28:48] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -62.724102+0.001568j
[2025-09-19 15:29:20] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -62.744819-0.004915j
[2025-09-19 15:29:52] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -62.725046+0.001443j
[2025-09-19 15:29:52] RESTART #1 | Period: 300
[2025-09-19 15:30:24] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -62.731417+0.002297j
[2025-09-19 15:30:56] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -62.731796-0.000806j
[2025-09-19 15:31:28] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -62.720780+0.005135j
[2025-09-19 15:32:00] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -62.731996+0.000170j
[2025-09-19 15:32:31] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -62.738672-0.001811j
[2025-09-19 15:33:03] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -62.731734+0.002359j
[2025-09-19 15:33:35] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -62.720875+0.003073j
[2025-09-19 15:34:07] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -62.709193+0.001222j
[2025-09-19 15:34:39] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -62.726163-0.000214j
[2025-09-19 15:35:11] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -62.704265-0.001072j
[2025-09-19 15:35:43] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -62.701680-0.004098j
[2025-09-19 15:36:15] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -62.748812-0.001431j
[2025-09-19 15:36:46] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -62.715225+0.001178j
[2025-09-19 15:37:18] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -62.719946+0.002474j
[2025-09-19 15:37:50] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -62.725684+0.000670j
[2025-09-19 15:38:22] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -62.714557-0.001256j
[2025-09-19 15:38:54] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -62.743287+0.001504j
[2025-09-19 15:39:26] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -62.717720-0.000044j
[2025-09-19 15:39:58] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -62.728738-0.000828j
[2025-09-19 15:40:30] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -62.748781-0.004905j
[2025-09-19 15:41:02] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -62.711091-0.001086j
[2025-09-19 15:41:34] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -62.739326+0.002964j
[2025-09-19 15:42:05] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -62.745756-0.002728j
[2025-09-19 15:42:37] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -62.708751+0.000232j
[2025-09-19 15:43:09] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -62.731497+0.000060j
[2025-09-19 15:43:41] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -62.725708-0.001200j
[2025-09-19 15:44:13] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -62.719677+0.005889j
[2025-09-19 15:44:45] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -62.729408-0.000350j
[2025-09-19 15:45:17] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -62.740706+0.001452j
[2025-09-19 15:45:49] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -62.714335+0.003200j
[2025-09-19 15:46:21] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -62.717596+0.001607j
[2025-09-19 15:46:53] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -62.723333+0.000052j
[2025-09-19 15:47:25] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -62.731764-0.006615j
[2025-09-19 15:47:57] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -62.733366+0.003010j
[2025-09-19 15:48:29] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -62.725486+0.002235j
[2025-09-19 15:49:01] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -62.714298+0.002067j
[2025-09-19 15:49:33] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -62.703224+0.001402j
[2025-09-19 15:50:05] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -62.727923+0.002939j
[2025-09-19 15:50:37] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -62.728215+0.000263j
[2025-09-19 15:51:09] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -62.714987-0.000199j
[2025-09-19 15:51:41] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -62.735778-0.002548j
[2025-09-19 15:52:12] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -62.734262-0.000130j
[2025-09-19 15:52:44] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -62.752630+0.001153j
[2025-09-19 15:53:16] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -62.738366+0.003335j
[2025-09-19 15:53:48] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -62.714311-0.001691j
[2025-09-19 15:54:20] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -62.737254-0.002250j
[2025-09-19 15:54:52] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -62.727364+0.000327j
[2025-09-19 15:55:24] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -62.714744+0.002463j
[2025-09-19 15:55:56] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -62.738396+0.001433j
[2025-09-19 15:56:28] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -62.744180+0.003568j
[2025-09-19 15:57:00] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -62.720708-0.002492j
[2025-09-19 15:57:32] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -62.709199-0.000557j
[2025-09-19 15:58:04] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -62.724575-0.000132j
[2025-09-19 15:58:36] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -62.722110+0.001387j
[2025-09-19 15:59:08] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -62.732154-0.000914j
[2025-09-19 15:59:40] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -62.726903-0.001969j
[2025-09-19 16:00:12] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -62.716646-0.000886j
[2025-09-19 16:00:44] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -62.728594-0.000125j
[2025-09-19 16:01:15] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -62.727745+0.001020j
[2025-09-19 16:01:47] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -62.734669+0.000689j
[2025-09-19 16:01:47] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-19 16:02:19] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -62.715512-0.002092j
[2025-09-19 16:02:51] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -62.735548+0.003391j
[2025-09-19 16:03:23] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -62.719828-0.006016j
[2025-09-19 16:03:55] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -62.747905-0.000764j
[2025-09-19 16:04:27] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -62.741341-0.000907j
[2025-09-19 16:04:59] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -62.736741-0.002906j
[2025-09-19 16:05:31] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -62.723544+0.000181j
[2025-09-19 16:06:02] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -62.711541+0.005220j
[2025-09-19 16:06:34] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -62.727041+0.003258j
[2025-09-19 16:07:06] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -62.729466+0.001030j
[2025-09-19 16:07:38] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -62.725166-0.004058j
[2025-09-19 16:08:10] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -62.717084+0.000257j
[2025-09-19 16:08:42] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -62.720971-0.000512j
[2025-09-19 16:09:14] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -62.752564+0.001087j
[2025-09-19 16:09:46] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -62.735118+0.002503j
[2025-09-19 16:10:17] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -62.706663+0.000655j
[2025-09-19 16:10:49] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -62.716364+0.000868j
[2025-09-19 16:11:21] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -62.730310+0.006422j
[2025-09-19 16:11:53] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -62.717487+0.006144j
[2025-09-19 16:12:25] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -62.710856-0.000405j
[2025-09-19 16:12:57] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -62.714614-0.005467j
[2025-09-19 16:13:29] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -62.732089-0.002672j
[2025-09-19 16:14:01] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -62.740119-0.003823j
[2025-09-19 16:14:33] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -62.722601+0.001490j
[2025-09-19 16:15:04] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -62.741539+0.001438j
[2025-09-19 16:15:36] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -62.732621+0.000824j
[2025-09-19 16:16:08] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -62.721216-0.000397j
[2025-09-19 16:16:40] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -62.734162+0.000653j
[2025-09-19 16:17:12] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -62.723779-0.000690j
[2025-09-19 16:17:44] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -62.715646-0.001089j
[2025-09-19 16:18:16] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -62.744036-0.000702j
[2025-09-19 16:18:48] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -62.715187-0.000024j
[2025-09-19 16:19:19] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -62.714935+0.001657j
[2025-09-19 16:19:51] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -62.727506-0.003078j
[2025-09-19 16:20:23] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -62.705988+0.000917j
[2025-09-19 16:20:55] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -62.752142+0.003004j
[2025-09-19 16:21:27] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -62.728608-0.001048j
[2025-09-19 16:22:00] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -62.715761-0.004532j
[2025-09-19 16:22:32] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -62.723095-0.003227j
[2025-09-19 16:23:03] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -62.717842-0.000849j
[2025-09-19 16:23:35] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -62.728015-0.000357j
[2025-09-19 16:24:07] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -62.714419+0.001931j
[2025-09-19 16:24:39] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -62.731158-0.003471j
[2025-09-19 16:25:11] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -62.699623-0.001712j
[2025-09-19 16:25:43] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -62.715523-0.000817j
[2025-09-19 16:26:15] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -62.702314+0.002669j
[2025-09-19 16:26:47] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -62.725141+0.000484j
[2025-09-19 16:27:19] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -62.727569+0.001138j
[2025-09-19 16:27:51] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -62.732597+0.002350j
[2025-09-19 16:28:23] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -62.724568+0.001335j
[2025-09-19 16:28:55] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -62.749573-0.000710j
[2025-09-19 16:29:26] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -62.711905+0.009439j
[2025-09-19 16:29:58] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -62.724657-0.000425j
[2025-09-19 16:30:30] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -62.734148-0.001741j
[2025-09-19 16:31:02] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -62.729524+0.002228j
[2025-09-19 16:31:34] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -62.713861-0.000581j
[2025-09-19 16:32:06] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -62.739345-0.003933j
[2025-09-19 16:32:38] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -62.715713-0.002397j
[2025-09-19 16:33:10] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -62.715033-0.000882j
[2025-09-19 16:33:42] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -62.729790+0.003961j
[2025-09-19 16:34:14] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -62.721205+0.001529j
[2025-09-19 16:34:46] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -62.730568+0.002754j
[2025-09-19 16:35:17] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -62.728643+0.000586j
[2025-09-19 16:35:49] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -62.721344-0.002665j
[2025-09-19 16:36:21] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -62.736338-0.001472j
[2025-09-19 16:36:53] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -62.698996+0.003766j
[2025-09-19 16:37:25] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -62.726605+0.001595j
[2025-09-19 16:37:57] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -62.732244-0.000116j
[2025-09-19 16:38:29] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -62.722773+0.003168j
[2025-09-19 16:39:01] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -62.717800+0.001945j
[2025-09-19 16:39:33] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -62.743030-0.004796j
[2025-09-19 16:40:05] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -62.745024-0.001205j
[2025-09-19 16:40:37] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -62.716227-0.000320j
[2025-09-19 16:41:08] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -62.742669+0.001662j
[2025-09-19 16:41:40] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -62.721348+0.001279j
[2025-09-19 16:42:12] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -62.741274-0.001910j
[2025-09-19 16:42:44] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -62.727368+0.004194j
[2025-09-19 16:43:16] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -62.728207+0.002585j
[2025-09-19 16:43:48] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -62.735601-0.004272j
[2025-09-19 16:44:20] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -62.719496-0.000434j
[2025-09-19 16:44:52] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -62.730092+0.004523j
[2025-09-19 16:45:24] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -62.713580+0.001481j
[2025-09-19 16:45:55] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -62.717916-0.001477j
[2025-09-19 16:46:27] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -62.740889-0.002604j
[2025-09-19 16:46:59] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -62.710539+0.002329j
[2025-09-19 16:47:31] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -62.745007+0.001039j
[2025-09-19 16:48:03] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -62.762954+0.001548j
[2025-09-19 16:48:35] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -62.716963+0.001432j
[2025-09-19 16:49:07] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -62.731195+0.003556j
[2025-09-19 16:49:39] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -62.723506+0.001641j
[2025-09-19 16:50:11] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -62.753164+0.003896j
[2025-09-19 16:50:43] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -62.726560-0.010506j
[2025-09-19 16:51:14] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -62.726133-0.004300j
[2025-09-19 16:51:46] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -62.715655+0.002034j
[2025-09-19 16:52:18] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -62.740718-0.001275j
[2025-09-19 16:52:50] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -62.734805+0.004677j
[2025-09-19 16:53:22] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -62.720583+0.002683j
[2025-09-19 16:53:54] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -62.736064+0.004381j
[2025-09-19 16:54:26] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -62.708241+0.003483j
[2025-09-19 16:54:58] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -62.713970+0.000178j
[2025-09-19 16:55:30] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -62.736560-0.001378j
[2025-09-19 16:56:02] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -62.720911+0.001570j
[2025-09-19 16:56:34] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -62.728319+0.003546j
[2025-09-19 16:57:06] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -62.708391+0.000856j
[2025-09-19 16:57:38] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -62.724882+0.000995j
[2025-09-19 16:57:38] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-19 16:58:10] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -62.726764-0.000662j
[2025-09-19 16:58:42] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -62.713863-0.000510j
[2025-09-19 16:59:13] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -62.723514+0.000622j
[2025-09-19 16:59:45] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -62.715473-0.000818j
[2025-09-19 17:00:17] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -62.730591-0.002555j
[2025-09-19 17:00:49] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -62.714000-0.000133j
[2025-09-19 17:01:21] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -62.729431-0.002288j
[2025-09-19 17:01:53] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -62.728999-0.000689j
[2025-09-19 17:02:25] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -62.746534+0.002663j
[2025-09-19 17:02:57] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -62.731651+0.010278j
[2025-09-19 17:03:29] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -62.738117-0.001867j
[2025-09-19 17:04:01] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -62.724235+0.000026j
[2025-09-19 17:04:33] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -62.734727+0.000436j
[2025-09-19 17:05:04] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -62.712253+0.001600j
[2025-09-19 17:05:36] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -62.746009+0.003497j
[2025-09-19 17:06:08] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -62.726950+0.001870j
[2025-09-19 17:06:40] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -62.726935-0.000544j
[2025-09-19 17:07:12] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -62.731554+0.000982j
[2025-09-19 17:07:44] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -62.727325-0.000023j
[2025-09-19 17:08:16] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -62.725935-0.003622j
[2025-09-19 17:08:47] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -62.715951-0.001382j
[2025-09-19 17:09:19] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -62.738465+0.000359j
[2025-09-19 17:09:51] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -62.729123+0.003712j
[2025-09-19 17:10:23] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -62.729143+0.000820j
[2025-09-19 17:10:55] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -62.723284-0.000472j
[2025-09-19 17:11:27] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -62.710331+0.003537j
[2025-09-19 17:11:59] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -62.716581+0.003038j
[2025-09-19 17:12:31] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -62.727361-0.001425j
[2025-09-19 17:13:03] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -62.754186-0.000112j
[2025-09-19 17:13:35] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -62.743491+0.004227j
[2025-09-19 17:14:06] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -62.731070+0.000009j
[2025-09-19 17:14:38] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -62.725398-0.000256j
[2025-09-19 17:15:10] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -62.714463-0.000170j
[2025-09-19 17:15:42] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -62.726098+0.006501j
[2025-09-19 17:16:14] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -62.728601+0.000099j
[2025-09-19 17:16:45] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -62.722337+0.001703j
[2025-09-19 17:17:17] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -62.719966-0.002652j
[2025-09-19 17:17:49] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -62.733377+0.002678j
[2025-09-19 17:18:21] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -62.733799-0.001423j
[2025-09-19 17:18:53] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -62.737977+0.001062j
[2025-09-19 17:19:25] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -62.737528-0.003899j
[2025-09-19 17:19:57] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -62.746462-0.000243j
[2025-09-19 17:20:29] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -62.737396-0.000669j
[2025-09-19 17:21:01] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -62.734577-0.004633j
[2025-09-19 17:21:32] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -62.739144+0.002527j
[2025-09-19 17:22:04] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -62.728170+0.000032j
[2025-09-19 17:22:36] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -62.726783+0.002566j
[2025-09-19 17:23:08] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -62.733231+0.000479j
[2025-09-19 17:23:40] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -62.739861-0.003668j
[2025-09-19 17:24:12] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -62.741857-0.001863j
[2025-09-19 17:24:44] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -62.724707-0.001613j
[2025-09-19 17:25:16] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -62.714813-0.001973j
[2025-09-19 17:25:48] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -62.721917+0.000544j
[2025-09-19 17:26:20] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -62.736537+0.006105j
[2025-09-19 17:26:51] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -62.748444+0.000347j
[2025-09-19 17:27:23] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -62.716542-0.000719j
[2025-09-19 17:27:55] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -62.723861-0.000247j
[2025-09-19 17:28:27] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -62.729813-0.000955j
[2025-09-19 17:28:59] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -62.736395-0.002669j
[2025-09-19 17:29:31] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -62.715479+0.000157j
[2025-09-19 17:30:03] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -62.739390+0.000880j
[2025-09-19 17:30:35] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -62.719157+0.000532j
[2025-09-19 17:31:07] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -62.714995-0.003266j
[2025-09-19 17:31:39] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -62.737125+0.002912j
[2025-09-19 17:32:11] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -62.715929+0.002186j
[2025-09-19 17:32:43] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -62.738733-0.000597j
[2025-09-19 17:33:15] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -62.713588+0.003896j
[2025-09-19 17:33:47] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -62.729056+0.000018j
[2025-09-19 17:34:19] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -62.755200+0.001328j
[2025-09-19 17:34:51] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -62.711872+0.003700j
[2025-09-19 17:35:22] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -62.726599-0.001284j
[2025-09-19 17:35:54] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -62.712766-0.000578j
[2025-09-19 17:36:26] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -62.724501-0.001518j
[2025-09-19 17:36:58] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -62.737794-0.000414j
[2025-09-19 17:37:30] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -62.728297-0.000011j
[2025-09-19 17:38:02] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -62.742421-0.002739j
[2025-09-19 17:38:34] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -62.727875-0.000908j
[2025-09-19 17:39:06] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -62.732920-0.000771j
[2025-09-19 17:39:38] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -62.743356+0.002216j
[2025-09-19 17:40:10] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -62.741378-0.000963j
[2025-09-19 17:40:42] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -62.743271-0.003836j
[2025-09-19 17:41:13] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -62.733807+0.003378j
[2025-09-19 17:41:46] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -62.719411+0.000138j
[2025-09-19 17:42:18] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -62.734529+0.000227j
[2025-09-19 17:42:49] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -62.733990-0.001131j
[2025-09-19 17:43:21] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -62.747198+0.003755j
[2025-09-19 17:43:53] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -62.713366+0.001731j
[2025-09-19 17:44:25] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -62.719649-0.001359j
[2025-09-19 17:44:57] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -62.735921-0.001357j
[2025-09-19 17:45:29] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -62.740405+0.004207j
[2025-09-19 17:46:01] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -62.726116-0.004289j
[2025-09-19 17:46:33] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -62.744451-0.000299j
[2025-09-19 17:47:05] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -62.725838+0.002034j
[2025-09-19 17:47:37] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -62.741297+0.002792j
[2025-09-19 17:48:08] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -62.712721+0.004198j
[2025-09-19 17:48:40] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -62.724110-0.002967j
[2025-09-19 17:49:12] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -62.720091-0.003051j
[2025-09-19 17:49:44] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -62.710149+0.000151j
[2025-09-19 17:50:16] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -62.714604+0.000576j
[2025-09-19 17:50:48] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -62.726630-0.003985j
[2025-09-19 17:51:20] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -62.726234-0.002961j
[2025-09-19 17:51:52] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -62.735064-0.004845j
[2025-09-19 17:52:24] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -62.730303+0.000433j
[2025-09-19 17:52:56] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -62.749360-0.005649j
[2025-09-19 17:53:27] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -62.733497+0.002060j
[2025-09-19 17:53:27] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-19 17:53:59] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -62.724886+0.001442j
[2025-09-19 17:54:31] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -62.752752-0.001974j
[2025-09-19 17:55:03] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -62.708854+0.001122j
[2025-09-19 17:55:35] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -62.738008-0.005374j
[2025-09-19 17:56:07] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -62.735739-0.000045j
[2025-09-19 17:56:39] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -62.760642+0.005423j
[2025-09-19 17:57:11] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -62.732848+0.003399j
[2025-09-19 17:57:43] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -62.750683-0.003954j
[2025-09-19 17:58:15] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -62.735097+0.001748j
[2025-09-19 17:58:46] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -62.726129-0.001046j
[2025-09-19 17:59:18] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -62.737076+0.000169j
[2025-09-19 17:59:50] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -62.721135-0.007856j
[2025-09-19 18:00:22] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -62.708308-0.006865j
[2025-09-19 18:00:54] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -62.744875-0.003915j
[2025-09-19 18:01:26] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -62.731899+0.002262j
[2025-09-19 18:01:58] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -62.739463-0.001381j
[2025-09-19 18:02:30] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -62.722466-0.000305j
[2025-09-19 18:03:02] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -62.733355-0.002474j
[2025-09-19 18:03:34] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -62.729267-0.002329j
[2025-09-19 18:04:06] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -62.723694+0.002793j
[2025-09-19 18:04:37] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -62.731787+0.004043j
[2025-09-19 18:05:09] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -62.710598+0.000887j
[2025-09-19 18:05:41] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -62.728400-0.004238j
[2025-09-19 18:06:13] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -62.740252+0.002278j
[2025-09-19 18:06:45] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -62.728965+0.008733j
[2025-09-19 18:07:17] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -62.726451+0.001894j
[2025-09-19 18:07:49] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -62.721523-0.004811j
[2025-09-19 18:08:21] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -62.714323+0.002191j
[2025-09-19 18:08:53] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -62.733259-0.000623j
[2025-09-19 18:09:25] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -62.737740+0.006441j
[2025-09-19 18:09:25] RESTART #2 | Period: 600
[2025-09-19 18:09:57] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -62.744507+0.001604j
[2025-09-19 18:10:29] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -62.731981-0.001671j
[2025-09-19 18:11:01] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -62.755227-0.000526j
[2025-09-19 18:11:33] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -62.723181+0.003833j
[2025-09-19 18:12:05] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -62.706029+0.003315j
[2025-09-19 18:12:36] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -62.722177+0.001445j
[2025-09-19 18:13:08] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -62.722048-0.000326j
[2025-09-19 18:13:40] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -62.755935+0.001986j
[2025-09-19 18:14:12] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -62.743932-0.001949j
[2025-09-19 18:14:44] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -62.726052+0.000789j
[2025-09-19 18:15:16] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -62.732088+0.002986j
[2025-09-19 18:15:48] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -62.728660-0.002336j
[2025-09-19 18:16:20] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -62.734932+0.004308j
[2025-09-19 18:16:52] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -62.717413+0.002210j
[2025-09-19 18:17:24] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -62.726785-0.002994j
[2025-09-19 18:17:56] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -62.742259-0.001533j
[2025-09-19 18:18:27] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -62.744825-0.002264j
[2025-09-19 18:18:59] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -62.723981+0.003811j
[2025-09-19 18:19:31] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -62.755802-0.004502j
[2025-09-19 18:20:03] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -62.730127+0.000260j
[2025-09-19 18:20:35] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -62.728019+0.002609j
[2025-09-19 18:21:07] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -62.747573-0.001158j
[2025-09-19 18:21:39] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -62.721662+0.002851j
[2025-09-19 18:22:11] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -62.732043-0.002292j
[2025-09-19 18:22:43] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -62.727976-0.002647j
[2025-09-19 18:23:14] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -62.722650+0.000176j
[2025-09-19 18:23:46] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -62.735203+0.003239j
[2025-09-19 18:24:18] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -62.719435-0.001428j
[2025-09-19 18:24:50] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -62.739877-0.001149j
[2025-09-19 18:25:22] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -62.739606+0.006216j
[2025-09-19 18:25:54] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -62.738921-0.000792j
[2025-09-19 18:26:26] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -62.735310+0.003599j
[2025-09-19 18:26:58] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -62.741791-0.004931j
[2025-09-19 18:27:30] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -62.754083+0.004472j
[2025-09-19 18:28:01] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -62.737470-0.000876j
[2025-09-19 18:28:33] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -62.717499+0.000540j
[2025-09-19 18:29:05] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -62.733917+0.002341j
[2025-09-19 18:29:37] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -62.705808+0.011219j
[2025-09-19 18:30:09] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -62.741770-0.003151j
[2025-09-19 18:30:41] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -62.734124+0.000517j
[2025-09-19 18:31:13] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -62.737666+0.001789j
[2025-09-19 18:31:45] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -62.718374+0.000827j
[2025-09-19 18:32:17] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -62.721200-0.001910j
[2025-09-19 18:32:49] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -62.726992-0.000634j
[2025-09-19 18:33:20] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -62.734197-0.001357j
[2025-09-19 18:33:52] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -62.732673+0.000607j
[2025-09-19 18:34:24] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -62.735392+0.001237j
[2025-09-19 18:34:56] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -62.742287-0.003294j
[2025-09-19 18:35:28] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -62.747666-0.002134j
[2025-09-19 18:36:00] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -62.739126+0.002373j
[2025-09-19 18:36:32] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -62.734056+0.003635j
[2025-09-19 18:37:04] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -62.751685-0.000919j
[2025-09-19 18:37:36] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -62.734985+0.006410j
[2025-09-19 18:38:08] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -62.721121+0.002390j
[2025-09-19 18:38:40] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -62.724429-0.000554j
[2025-09-19 18:39:12] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -62.729270-0.003580j
[2025-09-19 18:39:44] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -62.742959-0.000103j
[2025-09-19 18:40:16] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -62.737639-0.003536j
[2025-09-19 18:40:48] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -62.722608+0.002162j
[2025-09-19 18:41:20] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -62.729116+0.002704j
[2025-09-19 18:41:52] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -62.733914+0.001041j
[2025-09-19 18:42:24] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -62.750320+0.001848j
[2025-09-19 18:42:55] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -62.724065+0.001174j
[2025-09-19 18:43:27] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -62.725873+0.000388j
[2025-09-19 18:43:59] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -62.738136+0.001217j
[2025-09-19 18:44:31] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -62.719361-0.003567j
[2025-09-19 18:45:03] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -62.738551+0.002288j
[2025-09-19 18:45:35] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -62.736704-0.000130j
[2025-09-19 18:46:06] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -62.726776+0.003106j
[2025-09-19 18:46:38] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -62.724944+0.003820j
[2025-09-19 18:47:10] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -62.725154+0.001608j
[2025-09-19 18:47:42] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -62.711259+0.000907j
[2025-09-19 18:48:14] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -62.741561-0.002737j
[2025-09-19 18:48:46] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -62.737186-0.004168j
[2025-09-19 18:49:18] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -62.722712-0.000865j
[2025-09-19 18:49:18] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-19 18:49:50] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -62.716729-0.001553j
[2025-09-19 18:50:22] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -62.716906+0.007074j
[2025-09-19 18:50:54] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -62.745460+0.001545j
[2025-09-19 18:51:26] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -62.722073-0.003034j
[2025-09-19 18:51:58] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -62.731642+0.002815j
[2025-09-19 18:52:29] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -62.731609+0.002496j
[2025-09-19 18:53:01] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -62.735014-0.003594j
[2025-09-19 18:53:33] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -62.738464-0.003147j
[2025-09-19 18:54:05] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -62.736293-0.001481j
[2025-09-19 18:54:37] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -62.720014+0.001925j
[2025-09-19 18:55:09] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -62.723554-0.002943j
[2025-09-19 18:55:41] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -62.711909+0.002386j
[2025-09-19 18:56:13] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -62.728959+0.000933j
[2025-09-19 18:56:45] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -62.745452+0.005952j
[2025-09-19 18:57:16] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -62.728243+0.000549j
[2025-09-19 18:57:48] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -62.728722-0.003831j
[2025-09-19 18:58:20] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -62.730969+0.001197j
[2025-09-19 18:58:52] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -62.746669+0.000237j
[2025-09-19 18:59:24] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -62.745383-0.003815j
[2025-09-19 18:59:55] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -62.709201-0.003087j
[2025-09-19 19:00:27] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -62.725245-0.004595j
[2025-09-19 19:00:59] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -62.730167+0.001972j
[2025-09-19 19:01:31] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -62.731580-0.004082j
[2025-09-19 19:02:03] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -62.722809-0.000732j
[2025-09-19 19:02:35] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -62.739138-0.004642j
[2025-09-19 19:03:07] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -62.740206-0.004881j
[2025-09-19 19:03:38] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -62.725866-0.001041j
[2025-09-19 19:04:10] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -62.719955-0.002479j
[2025-09-19 19:04:42] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -62.755427-0.003545j
[2025-09-19 19:05:14] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -62.723108+0.002428j
[2025-09-19 19:05:46] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -62.724325+0.001112j
[2025-09-19 19:06:18] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -62.713926-0.000011j
[2025-09-19 19:06:50] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -62.732370-0.000540j
[2025-09-19 19:07:22] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -62.729008-0.002644j
[2025-09-19 19:07:54] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -62.740426+0.001407j
[2025-09-19 19:08:26] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -62.751204-0.002844j
[2025-09-19 19:08:58] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -62.721053+0.001100j
[2025-09-19 19:09:30] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -62.726592-0.001925j
[2025-09-19 19:10:02] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -62.732447+0.001817j
[2025-09-19 19:10:34] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -62.743171+0.000093j
[2025-09-19 19:11:06] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -62.738562-0.000387j
[2025-09-19 19:11:37] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -62.729774-0.000766j
[2025-09-19 19:12:09] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -62.707988+0.003758j
[2025-09-19 19:12:41] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -62.732375-0.001843j
[2025-09-19 19:13:13] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -62.743338-0.000478j
[2025-09-19 19:13:45] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -62.747114+0.004913j
[2025-09-19 19:14:17] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -62.731620+0.004117j
[2025-09-19 19:14:49] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -62.725860-0.003618j
[2025-09-19 19:15:21] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -62.736932-0.006207j
[2025-09-19 19:15:53] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -62.737704+0.001980j
[2025-09-19 19:16:25] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -62.724195-0.001192j
[2025-09-19 19:16:57] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -62.718080-0.001064j
[2025-09-19 19:17:29] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -62.744576-0.003194j
[2025-09-19 19:18:01] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -62.719410-0.003740j
[2025-09-19 19:18:33] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -62.733367-0.001240j
[2025-09-19 19:19:05] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -62.735483+0.007343j
[2025-09-19 19:19:37] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -62.725691-0.000290j
[2025-09-19 19:20:09] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -62.742233-0.001185j
[2025-09-19 19:20:41] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -62.735821+0.001029j
[2025-09-19 19:21:13] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -62.718423+0.001901j
[2025-09-19 19:21:45] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -62.746973-0.003521j
[2025-09-19 19:22:16] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -62.727824-0.001714j
[2025-09-19 19:22:48] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -62.718993-0.000772j
[2025-09-19 19:23:20] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -62.723733+0.002346j
[2025-09-19 19:23:52] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -62.745326+0.001125j
[2025-09-19 19:24:24] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -62.747995-0.001694j
[2025-09-19 19:24:56] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -62.732699-0.001339j
[2025-09-19 19:25:28] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -62.739732+0.000459j
[2025-09-19 19:26:00] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -62.721665+0.001373j
[2025-09-19 19:26:32] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -62.723936+0.000061j
[2025-09-19 19:27:04] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -62.727333+0.000760j
[2025-09-19 19:27:36] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -62.721434+0.000724j
[2025-09-19 19:28:08] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -62.715630-0.002403j
[2025-09-19 19:28:40] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -62.737375+0.000359j
[2025-09-19 19:29:12] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -62.737629-0.014313j
[2025-09-19 19:29:44] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -62.735919+0.004552j
[2025-09-19 19:30:16] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -62.748636-0.000493j
[2025-09-19 19:30:48] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -62.731941+0.002801j
[2025-09-19 19:31:19] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -62.727510-0.000531j
[2025-09-19 19:31:51] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -62.736433+0.000356j
[2025-09-19 19:32:23] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -62.732297-0.000632j
[2025-09-19 19:32:55] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -62.720421+0.000375j
[2025-09-19 19:33:27] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -62.735229-0.003255j
[2025-09-19 19:33:59] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -62.731795+0.000641j
[2025-09-19 19:34:31] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -62.713594+0.002136j
[2025-09-19 19:35:03] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -62.705738+0.000054j
[2025-09-19 19:35:34] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -62.734321-0.001232j
[2025-09-19 19:36:06] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -62.734525-0.002390j
[2025-09-19 19:36:38] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -62.729656+0.002198j
[2025-09-19 19:37:10] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -62.725690+0.003441j
[2025-09-19 19:37:41] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -62.736765+0.004015j
[2025-09-19 19:38:13] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -62.747109+0.000625j
[2025-09-19 19:38:45] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -62.728191+0.003095j
[2025-09-19 19:39:17] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -62.716154-0.000395j
[2025-09-19 19:39:49] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -62.746080+0.000520j
[2025-09-19 19:40:21] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -62.735320+0.005019j
[2025-09-19 19:40:53] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -62.717403-0.001795j
[2025-09-19 19:41:25] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -62.742793-0.002561j
[2025-09-19 19:41:57] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -62.716045-0.003569j
[2025-09-19 19:42:28] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -62.726684-0.001550j
[2025-09-19 19:43:00] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -62.745797+0.004621j
[2025-09-19 19:43:32] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -62.745384+0.002246j
[2025-09-19 19:44:04] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -62.715507+0.004547j
[2025-09-19 19:44:36] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -62.726761-0.002016j
[2025-09-19 19:45:08] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -62.736286+0.002420j
[2025-09-19 19:45:08] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-19 19:45:40] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -62.721045-0.004256j
[2025-09-19 19:46:12] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -62.729955+0.001324j
[2025-09-19 19:46:44] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -62.741075-0.001059j
[2025-09-19 19:47:16] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -62.721100+0.000390j
[2025-09-19 19:47:47] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -62.736067-0.000384j
[2025-09-19 19:48:19] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -62.725523+0.000014j
[2025-09-19 19:48:51] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -62.716724+0.001086j
[2025-09-19 19:49:23] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -62.744897+0.006346j
[2025-09-19 19:49:55] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -62.732711-0.002210j
[2025-09-19 19:50:27] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -62.738472+0.002106j
[2025-09-19 19:50:59] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -62.731146+0.004306j
[2025-09-19 19:51:31] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -62.736383+0.002399j
[2025-09-19 19:52:03] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -62.715038+0.000667j
[2025-09-19 19:52:35] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -62.727891-0.001242j
[2025-09-19 19:53:07] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -62.728286+0.002935j
[2025-09-19 19:53:39] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -62.727305-0.000227j
[2025-09-19 19:54:11] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -62.726005+0.006985j
[2025-09-19 19:54:43] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -62.739625+0.001046j
[2025-09-19 19:55:15] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -62.737349+0.000187j
[2025-09-19 19:55:47] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -62.740228-0.000171j
[2025-09-19 19:56:18] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -62.748492-0.000761j
[2025-09-19 19:56:51] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -62.727612+0.004357j
[2025-09-19 19:57:22] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -62.747452+0.001083j
[2025-09-19 19:57:54] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -62.737969-0.002746j
[2025-09-19 19:58:26] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -62.761529+0.001122j
[2025-09-19 19:58:58] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -62.726022-0.001990j
[2025-09-19 19:59:30] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -62.732916+0.000969j
[2025-09-19 20:00:02] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -62.718501-0.001003j
[2025-09-19 20:00:34] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -62.713853-0.000281j
[2025-09-19 20:01:06] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -62.725959+0.002037j
[2025-09-19 20:01:37] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -62.730700-0.004366j
[2025-09-19 20:02:09] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -62.749459-0.006331j
[2025-09-19 20:02:41] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -62.732074-0.000351j
[2025-09-19 20:03:13] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -62.724733-0.005647j
[2025-09-19 20:03:45] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -62.751968+0.002384j
[2025-09-19 20:04:17] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -62.745477+0.003184j
[2025-09-19 20:04:49] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -62.725806+0.000560j
[2025-09-19 20:05:21] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -62.736970+0.000614j
[2025-09-19 20:05:53] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -62.747646-0.000269j
[2025-09-19 20:06:25] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -62.729892+0.001345j
[2025-09-19 20:06:57] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -62.720499+0.002333j
[2025-09-19 20:07:29] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -62.732843-0.006695j
[2025-09-19 20:08:01] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -62.734886+0.006366j
[2025-09-19 20:08:32] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -62.735895+0.002176j
[2025-09-19 20:09:04] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -62.731144+0.000456j
[2025-09-19 20:09:36] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -62.728933+0.002514j
[2025-09-19 20:10:08] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -62.729635+0.000100j
[2025-09-19 20:10:40] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -62.720529+0.001630j
[2025-09-19 20:11:12] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -62.742397-0.000570j
[2025-09-19 20:11:43] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -62.728702+0.002432j
[2025-09-19 20:12:15] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -62.714197-0.000791j
[2025-09-19 20:12:47] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -62.743668+0.003582j
[2025-09-19 20:13:19] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -62.730686-0.003181j
[2025-09-19 20:13:51] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -62.732561-0.002663j
[2025-09-19 20:14:23] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -62.737262-0.005797j
[2025-09-19 20:14:55] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -62.724126+0.000416j
[2025-09-19 20:15:27] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -62.704311-0.004791j
[2025-09-19 20:15:59] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -62.727934-0.003175j
[2025-09-19 20:16:30] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -62.746482-0.002738j
[2025-09-19 20:17:02] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -62.739527+0.001632j
[2025-09-19 20:17:34] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -62.735706-0.002499j
[2025-09-19 20:18:06] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -62.741588+0.001709j
[2025-09-19 20:18:38] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -62.731281+0.000635j
[2025-09-19 20:19:10] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -62.731883+0.003094j
[2025-09-19 20:19:42] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -62.723046-0.002210j
[2025-09-19 20:20:14] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -62.748585-0.002234j
[2025-09-19 20:20:45] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -62.748869+0.003889j
[2025-09-19 20:21:17] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -62.728589-0.000617j
[2025-09-19 20:21:49] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -62.718031+0.000318j
[2025-09-19 20:22:21] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -62.738714-0.004508j
[2025-09-19 20:22:53] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -62.718531-0.002840j
[2025-09-19 20:23:25] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -62.736162-0.003572j
[2025-09-19 20:23:57] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -62.730136-0.000196j
[2025-09-19 20:24:29] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -62.741066-0.004103j
[2025-09-19 20:25:01] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -62.734376-0.001450j
[2025-09-19 20:25:32] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -62.724532-0.000492j
[2025-09-19 20:26:04] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -62.730136-0.000231j
[2025-09-19 20:26:36] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -62.743768-0.004046j
[2025-09-19 20:27:08] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -62.734893-0.000838j
[2025-09-19 20:27:40] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -62.735047+0.000083j
[2025-09-19 20:28:12] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -62.748539+0.000636j
[2025-09-19 20:28:44] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -62.734631+0.002217j
[2025-09-19 20:29:16] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -62.720319+0.000574j
[2025-09-19 20:29:48] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -62.733239+0.004351j
[2025-09-19 20:30:20] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -62.720098-0.003389j
[2025-09-19 20:30:51] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -62.741908+0.001179j
[2025-09-19 20:31:23] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -62.732657-0.000719j
[2025-09-19 20:31:55] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -62.737585-0.000640j
[2025-09-19 20:32:27] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -62.743430+0.003769j
[2025-09-19 20:32:59] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -62.727299-0.001580j
[2025-09-19 20:33:31] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -62.712829+0.000053j
[2025-09-19 20:34:03] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -62.741052+0.002643j
[2025-09-19 20:34:35] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -62.718774-0.004381j
[2025-09-19 20:35:07] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -62.708914-0.001090j
[2025-09-19 20:35:39] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -62.713250+0.000600j
[2025-09-19 20:36:11] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -62.745207+0.001861j
[2025-09-19 20:36:43] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -62.736851-0.004076j
[2025-09-19 20:37:15] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -62.733516+0.000057j
[2025-09-19 20:37:47] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -62.735144+0.003456j
[2025-09-19 20:38:19] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -62.747350-0.002504j
[2025-09-19 20:38:51] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -62.734705-0.000533j
[2025-09-19 20:39:23] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -62.728460-0.000818j
[2025-09-19 20:39:55] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -62.762570+0.001359j
[2025-09-19 20:40:26] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -62.741128+0.000966j
[2025-09-19 20:40:58] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -62.727708+0.000185j
[2025-09-19 20:40:58] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-19 20:41:30] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -62.735134-0.003724j
[2025-09-19 20:42:02] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -62.733286+0.001764j
[2025-09-19 20:42:34] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -62.726612-0.002007j
[2025-09-19 20:43:06] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -62.742578-0.001960j
[2025-09-19 20:43:38] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -62.738805-0.008566j
[2025-09-19 20:44:10] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -62.743836+0.001311j
[2025-09-19 20:44:42] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -62.736108-0.000204j
[2025-09-19 20:45:14] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -62.740388+0.002922j
[2025-09-19 20:45:46] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -62.740279-0.000799j
[2025-09-19 20:46:17] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -62.739631+0.000060j
[2025-09-19 20:46:49] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -62.739512+0.002539j
[2025-09-19 20:47:21] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -62.716179+0.001727j
[2025-09-19 20:47:53] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -62.731051-0.003013j
[2025-09-19 20:48:25] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -62.726347+0.002929j
[2025-09-19 20:48:57] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -62.743426-0.000011j
[2025-09-19 20:49:29] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -62.742916-0.000219j
[2025-09-19 20:50:01] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -62.759061-0.001010j
[2025-09-19 20:50:33] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -62.714154+0.001318j
[2025-09-19 20:51:05] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -62.748247+0.001714j
[2025-09-19 20:51:37] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -62.715280-0.002442j
[2025-09-19 20:52:08] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -62.716029-0.003860j
[2025-09-19 20:52:40] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -62.734492-0.000504j
[2025-09-19 20:53:12] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -62.742983-0.003081j
[2025-09-19 20:53:44] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -62.724302+0.002838j
[2025-09-19 20:54:16] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -62.724952-0.000600j
[2025-09-19 20:54:48] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -62.743289-0.000871j
[2025-09-19 20:55:20] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -62.743564+0.000862j
[2025-09-19 20:55:52] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -62.736511+0.002433j
[2025-09-19 20:56:24] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -62.745915+0.000689j
[2025-09-19 20:56:56] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -62.740837+0.005764j
[2025-09-19 20:57:27] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -62.721275-0.001544j
[2025-09-19 20:57:59] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -62.748105-0.012024j
[2025-09-19 20:58:31] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -62.726928+0.001378j
[2025-09-19 20:59:03] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -62.724297+0.002725j
[2025-09-19 20:59:35] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -62.731177-0.000497j
[2025-09-19 21:00:07] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -62.726875+0.000881j
[2025-09-19 21:00:39] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -62.740431+0.000811j
[2025-09-19 21:01:11] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -62.730349+0.000941j
[2025-09-19 21:01:43] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -62.726651-0.000268j
[2025-09-19 21:02:14] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -62.734719+0.000885j
[2025-09-19 21:02:46] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -62.742036-0.000770j
[2025-09-19 21:03:18] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -62.739623+0.000800j
[2025-09-19 21:03:50] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -62.743521-0.002661j
[2025-09-19 21:04:22] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -62.730464+0.000530j
[2025-09-19 21:04:54] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -62.736051-0.020301j
[2025-09-19 21:05:26] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -62.717579-0.000463j
[2025-09-19 21:05:58] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -62.756357+0.003754j
[2025-09-19 21:06:30] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -62.731940-0.000141j
[2025-09-19 21:07:02] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -62.749909-0.000372j
[2025-09-19 21:07:34] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -62.735616+0.002636j
[2025-09-19 21:08:06] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -62.735296-0.000345j
[2025-09-19 21:08:37] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -62.739026-0.004665j
[2025-09-19 21:09:09] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -62.725004-0.001342j
[2025-09-19 21:09:41] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -62.739478+0.002034j
[2025-09-19 21:10:13] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -62.726122-0.002794j
[2025-09-19 21:10:45] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -62.730818+0.002132j
[2025-09-19 21:11:17] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -62.742949+0.004491j
[2025-09-19 21:11:49] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -62.730897-0.004697j
[2025-09-19 21:12:21] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -62.733770+0.000074j
[2025-09-19 21:12:53] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -62.725413+0.001380j
[2025-09-19 21:13:25] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -62.724894+0.007258j
[2025-09-19 21:13:57] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -62.737269+0.001656j
[2025-09-19 21:14:29] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -62.735916+0.000432j
[2025-09-19 21:15:01] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -62.721960+0.000469j
[2025-09-19 21:15:33] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -62.730227-0.000745j
[2025-09-19 21:16:04] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -62.744309-0.000503j
[2025-09-19 21:16:36] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -62.738867+0.005704j
[2025-09-19 21:17:08] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -62.728561+0.001740j
[2025-09-19 21:17:40] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -62.730029-0.005317j
[2025-09-19 21:18:12] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -62.736898-0.000998j
[2025-09-19 21:18:44] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -62.747792-0.000274j
[2025-09-19 21:19:16] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -62.740485+0.000834j
[2025-09-19 21:19:48] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -62.737367+0.002057j
[2025-09-19 21:20:20] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -62.742359-0.001575j
[2025-09-19 21:20:52] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -62.724238+0.001162j
[2025-09-19 21:21:24] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -62.760737-0.005269j
[2025-09-19 21:21:55] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -62.731851-0.001849j
[2025-09-19 21:22:27] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -62.726421-0.002158j
[2025-09-19 21:22:59] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -62.729958-0.001923j
[2025-09-19 21:23:31] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -62.733865-0.000547j
[2025-09-19 21:24:03] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -62.739041+0.001591j
[2025-09-19 21:24:35] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -62.736544-0.000458j
[2025-09-19 21:25:07] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -62.727830+0.004519j
[2025-09-19 21:25:39] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -62.736458-0.006837j
[2025-09-19 21:26:11] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -62.709662-0.002440j
[2025-09-19 21:26:43] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -62.732913-0.002261j
[2025-09-19 21:27:14] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -62.712591-0.001955j
[2025-09-19 21:27:46] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -62.729146-0.003228j
[2025-09-19 21:28:18] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -62.728397+0.003075j
[2025-09-19 21:28:50] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -62.743343+0.004964j
[2025-09-19 21:29:22] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -62.747271-0.007499j
[2025-09-19 21:29:54] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -62.722015-0.002737j
[2025-09-19 21:30:26] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -62.732298+0.001896j
[2025-09-19 21:30:57] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -62.731785-0.000832j
[2025-09-19 21:31:29] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -62.738603-0.001309j
[2025-09-19 21:32:01] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -62.725380-0.000818j
[2025-09-19 21:32:33] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -62.728368-0.001134j
[2025-09-19 21:33:05] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -62.726287-0.004569j
[2025-09-19 21:33:37] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -62.743633+0.006999j
[2025-09-19 21:34:09] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -62.721231-0.000667j
[2025-09-19 21:34:41] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -62.735570+0.000545j
[2025-09-19 21:35:12] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -62.735420+0.003036j
[2025-09-19 21:35:44] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -62.734048+0.001763j
[2025-09-19 21:36:16] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -62.740540+0.001424j
[2025-09-19 21:36:48] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -62.752618+0.003831j
[2025-09-19 21:36:48] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-19 21:37:20] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -62.720199+0.001487j
[2025-09-19 21:37:52] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -62.726000-0.001042j
[2025-09-19 21:38:24] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -62.741219+0.000387j
[2025-09-19 21:38:56] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -62.744891-0.002915j
[2025-09-19 21:39:28] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -62.703646-0.003728j
[2025-09-19 21:39:59] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -62.733594+0.000552j
[2025-09-19 21:40:31] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -62.722776+0.003101j
[2025-09-19 21:41:03] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -62.722172-0.003648j
[2025-09-19 21:41:35] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -62.741888-0.000817j
[2025-09-19 21:42:07] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -62.748091-0.001070j
[2025-09-19 21:42:39] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -62.731175+0.003919j
[2025-09-19 21:43:11] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -62.750681+0.002359j
[2025-09-19 21:43:42] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -62.719583-0.001344j
[2025-09-19 21:44:14] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -62.727249-0.000330j
[2025-09-19 21:44:46] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -62.719548-0.002441j
[2025-09-19 21:45:18] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -62.739454+0.001216j
[2025-09-19 21:45:50] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -62.708026+0.000907j
[2025-09-19 21:46:22] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -62.737701-0.000984j
[2025-09-19 21:46:54] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -62.735481-0.000157j
[2025-09-19 21:47:26] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -62.728826-0.002001j
[2025-09-19 21:47:58] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -62.735820+0.000866j
[2025-09-19 21:48:30] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -62.743547+0.002237j
[2025-09-19 21:49:02] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -62.715234-0.003187j
[2025-09-19 21:49:34] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -62.726240+0.000191j
[2025-09-19 21:50:06] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -62.741246+0.001470j
[2025-09-19 21:50:38] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -62.725714-0.008383j
[2025-09-19 21:51:10] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -62.718097+0.002070j
[2025-09-19 21:51:41] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -62.738277+0.003471j
[2025-09-19 21:52:13] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -62.745984-0.003423j
[2025-09-19 21:52:45] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -62.726772+0.001095j
[2025-09-19 21:53:17] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -62.733155+0.001560j
[2025-09-19 21:53:49] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -62.748836-0.002062j
[2025-09-19 21:54:21] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -62.734316+0.003386j
[2025-09-19 21:54:53] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -62.745733+0.000855j
[2025-09-19 21:55:25] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -62.695561-0.001664j
[2025-09-19 21:55:57] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -62.734204+0.002238j
[2025-09-19 21:56:29] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -62.727282-0.008955j
[2025-09-19 21:57:01] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -62.744439-0.017942j
[2025-09-19 21:57:33] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -62.735709-0.002899j
[2025-09-19 21:58:04] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -62.741720-0.001479j
[2025-09-19 21:58:36] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -62.729393-0.002560j
[2025-09-19 21:59:08] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -62.725388-0.002938j
[2025-09-19 21:59:40] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -62.731233+0.000239j
[2025-09-19 22:00:12] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -62.739124-0.002567j
[2025-09-19 22:00:44] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -62.736573-0.000612j
[2025-09-19 22:01:16] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -62.725798+0.001117j
[2025-09-19 22:01:48] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -62.704079+0.000860j
[2025-09-19 22:02:20] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -62.737548-0.004146j
[2025-09-19 22:02:52] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -62.731745+0.001207j
[2025-09-19 22:03:24] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -62.731812+0.001962j
[2025-09-19 22:03:55] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -62.723794-0.000660j
[2025-09-19 22:04:27] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -62.750850-0.009433j
[2025-09-19 22:04:59] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -62.718874-0.001478j
[2025-09-19 22:05:31] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -62.741636-0.002524j
[2025-09-19 22:06:03] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -62.730294-0.001204j
[2025-09-19 22:06:35] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -62.728069-0.002896j
[2025-09-19 22:07:07] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -62.739787+0.001168j
[2025-09-19 22:07:38] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -62.739760-0.004384j
[2025-09-19 22:08:10] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -62.722516+0.000091j
[2025-09-19 22:08:42] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -62.723636-0.000618j
[2025-09-19 22:09:14] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -62.734084+0.002173j
[2025-09-19 22:09:46] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -62.726165-0.000072j
[2025-09-19 22:10:18] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -62.738288-0.002768j
[2025-09-19 22:10:50] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -62.744316+0.000045j
[2025-09-19 22:11:22] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -62.711939+0.003289j
[2025-09-19 22:11:54] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -62.746129+0.001253j
[2025-09-19 22:12:26] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -62.719754+0.000594j
[2025-09-19 22:12:57] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -62.711856+0.003183j
[2025-09-19 22:13:29] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -62.731050+0.000304j
[2025-09-19 22:14:01] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -62.735190-0.001865j
[2025-09-19 22:14:33] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -62.725606-0.001165j
[2025-09-19 22:15:05] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -62.737955+0.001160j
[2025-09-19 22:15:37] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -62.731543+0.000281j
[2025-09-19 22:16:09] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -62.735353-0.001661j
[2025-09-19 22:16:41] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -62.739872-0.003037j
[2025-09-19 22:17:13] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -62.722035+0.003254j
[2025-09-19 22:17:45] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -62.738143+0.000783j
[2025-09-19 22:18:16] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -62.743821+0.000899j
[2025-09-19 22:18:48] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -62.754847+0.002521j
[2025-09-19 22:19:20] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -62.738340+0.000190j
[2025-09-19 22:19:52] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -62.716990-0.002291j
[2025-09-19 22:20:24] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -62.742028+0.000402j
[2025-09-19 22:20:56] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -62.725842+0.003838j
[2025-09-19 22:21:28] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -62.736971-0.001333j
[2025-09-19 22:22:00] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -62.739497-0.001759j
[2025-09-19 22:22:32] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -62.750466+0.003554j
[2025-09-19 22:23:04] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -62.745265-0.003443j
[2025-09-19 22:23:35] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -62.746798+0.000146j
[2025-09-19 22:24:07] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -62.722333-0.003254j
[2025-09-19 22:24:39] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -62.733133+0.000971j
[2025-09-19 22:25:11] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -62.735663-0.004036j
[2025-09-19 22:25:43] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -62.715097-0.001251j
[2025-09-19 22:26:15] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -62.732059-0.002667j
[2025-09-19 22:26:47] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -62.729238-0.000175j
[2025-09-19 22:27:19] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -62.737284+0.000575j
[2025-09-19 22:27:51] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -62.739377+0.003548j
[2025-09-19 22:28:23] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -62.738547-0.000625j
[2025-09-19 22:28:55] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -62.749364+0.000879j
[2025-09-19 22:29:27] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -62.739258-0.000757j
[2025-09-19 22:29:59] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -62.729970-0.001527j
[2025-09-19 22:30:30] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -62.731018-0.001876j
[2025-09-19 22:31:02] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -62.730806+0.002896j
[2025-09-19 22:31:34] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -62.723836-0.000640j
[2025-09-19 22:32:06] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -62.729959-0.004272j
[2025-09-19 22:32:38] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -62.734552+0.014396j
[2025-09-19 22:32:38] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-19 22:33:10] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -62.735240-0.000913j
[2025-09-19 22:33:42] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -62.731215-0.003056j
[2025-09-19 22:34:14] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -62.721167+0.002873j
[2025-09-19 22:34:46] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -62.746618+0.000207j
[2025-09-19 22:35:18] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -62.727659+0.000405j
[2025-09-19 22:35:50] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -62.731272+0.005693j
[2025-09-19 22:36:21] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -62.748315-0.002600j
[2025-09-19 22:36:53] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -62.722128-0.000793j
[2025-09-19 22:37:25] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -62.747546+0.004140j
[2025-09-19 22:37:57] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -62.736526-0.000533j
[2025-09-19 22:38:29] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -62.723209-0.002228j
[2025-09-19 22:39:01] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -62.738321+0.002908j
[2025-09-19 22:39:32] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -62.738086+0.006652j
[2025-09-19 22:40:04] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -62.743821-0.002588j
[2025-09-19 22:40:36] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -62.733412+0.000158j
[2025-09-19 22:41:08] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -62.746555+0.003085j
[2025-09-19 22:41:40] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -62.733836-0.002934j
[2025-09-19 22:42:12] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -62.732559+0.000453j
[2025-09-19 22:42:44] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -62.739427+0.003327j
[2025-09-19 22:43:16] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -62.729439+0.000126j
[2025-09-19 22:43:48] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -62.746277-0.003226j
[2025-09-19 22:44:19] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -62.731030-0.002120j
[2025-09-19 22:44:51] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -62.734341+0.001822j
[2025-09-19 22:45:23] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -62.737081-0.000844j
[2025-09-19 22:45:55] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -62.740021-0.001473j
[2025-09-19 22:46:27] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -62.726522-0.000658j
[2025-09-19 22:46:59] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -62.750519-0.000939j
[2025-09-19 22:47:31] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -62.735983+0.001484j
[2025-09-19 22:48:03] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -62.738321+0.003219j
[2025-09-19 22:48:35] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -62.728910+0.003449j
[2025-09-19 22:49:06] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -62.740020-0.000892j
[2025-09-19 22:49:38] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -62.722903+0.004649j
[2025-09-19 22:50:10] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -62.721958+0.000827j
[2025-09-19 22:50:42] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -62.729197+0.000007j
[2025-09-19 22:51:14] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -62.744272-0.003149j
[2025-09-19 22:51:46] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -62.722234+0.000332j
[2025-09-19 22:52:18] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -62.726094-0.002632j
[2025-09-19 22:52:50] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -62.735409+0.003154j
[2025-09-19 22:53:22] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -62.735372+0.004361j
[2025-09-19 22:53:54] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -62.727145-0.004351j
[2025-09-19 22:54:25] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -62.717184-0.006748j
[2025-09-19 22:54:57] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -62.742071+0.002609j
[2025-09-19 22:55:29] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -62.743035-0.002211j
[2025-09-19 22:56:01] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -62.749411-0.000560j
[2025-09-19 22:56:33] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -62.734031+0.006399j
[2025-09-19 22:57:05] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -62.743514+0.001437j
[2025-09-19 22:57:37] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -62.732512+0.008690j
[2025-09-19 22:58:09] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -62.736993+0.002870j
[2025-09-19 22:58:41] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -62.735004+0.005739j
[2025-09-19 22:59:12] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -62.741396+0.000427j
[2025-09-19 22:59:44] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -62.732316-0.001080j
[2025-09-19 23:00:16] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -62.734199+0.000415j
[2025-09-19 23:00:48] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -62.738119+0.005310j
[2025-09-19 23:01:20] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -62.758253-0.004543j
[2025-09-19 23:01:52] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -62.730286+0.002858j
[2025-09-19 23:02:24] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -62.745693-0.001806j
[2025-09-19 23:02:56] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -62.738879-0.002530j
[2025-09-19 23:03:28] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -62.754802-0.002793j
[2025-09-19 23:04:00] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -62.740289+0.002565j
[2025-09-19 23:04:32] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -62.749350-0.000519j
[2025-09-19 23:05:03] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -62.745418-0.002567j
[2025-09-19 23:05:35] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -62.736775-0.003617j
[2025-09-19 23:06:07] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -62.738092+0.001707j
[2025-09-19 23:06:39] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -62.744972-0.002567j
[2025-09-19 23:07:11] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -62.728898-0.002457j
[2025-09-19 23:07:43] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -62.754277+0.003359j
[2025-09-19 23:08:15] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -62.736877-0.000951j
[2025-09-19 23:08:47] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -62.749484-0.001165j
[2025-09-19 23:09:19] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -62.741536-0.001422j
[2025-09-19 23:09:50] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -62.724967+0.000721j
[2025-09-19 23:10:22] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -62.748483+0.000633j
[2025-09-19 23:10:54] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -62.735960-0.002823j
[2025-09-19 23:11:26] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -62.741551+0.002617j
[2025-09-19 23:11:58] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -62.745497-0.000098j
[2025-09-19 23:12:30] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -62.763517+0.001611j
[2025-09-19 23:13:02] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -62.732360+0.000129j
[2025-09-19 23:13:34] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -62.735513-0.002817j
[2025-09-19 23:14:06] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -62.734528+0.001758j
[2025-09-19 23:14:38] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -62.742857-0.001157j
[2025-09-19 23:15:10] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -62.734717-0.002442j
[2025-09-19 23:15:41] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -62.740604-0.000896j
[2025-09-19 23:16:13] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -62.749705+0.002072j
[2025-09-19 23:16:45] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -62.729532-0.003580j
[2025-09-19 23:17:17] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -62.725729+0.001015j
[2025-09-19 23:17:49] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -62.729009+0.000395j
[2025-09-19 23:18:21] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -62.743867-0.001073j
[2025-09-19 23:18:53] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -62.715066+0.003412j
[2025-09-19 23:19:25] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -62.726014-0.000644j
[2025-09-19 23:19:57] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -62.750601-0.002078j
[2025-09-19 23:20:29] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -62.725566-0.001013j
[2025-09-19 23:21:00] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -62.732169+0.001148j
[2025-09-19 23:21:32] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -62.746376-0.000289j
[2025-09-19 23:22:04] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -62.733367-0.001453j
[2025-09-19 23:22:36] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -62.742535+0.003177j
[2025-09-19 23:23:08] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -62.726384-0.001858j
[2025-09-19 23:23:40] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -62.721655+0.000764j
[2025-09-19 23:24:12] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -62.733290-0.001415j
[2025-09-19 23:24:44] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -62.729932+0.006079j
[2025-09-19 23:25:15] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -62.734299+0.002689j
[2025-09-19 23:25:47] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -62.733862+0.001801j
[2025-09-19 23:26:19] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -62.734561+0.001045j
[2025-09-19 23:26:51] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -62.740952+0.002281j
[2025-09-19 23:27:23] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -62.721490+0.001501j
[2025-09-19 23:27:55] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -62.724038+0.000686j
[2025-09-19 23:28:27] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -62.760933-0.003674j
[2025-09-19 23:28:27] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-19 23:28:27] ✅ Training completed | Restarts: 2
[2025-09-19 23:28:27] ============================================================
[2025-09-19 23:28:27] Training completed | Runtime: 33616.9s
[2025-09-19 23:28:38] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-19 23:28:38] ============================================================
