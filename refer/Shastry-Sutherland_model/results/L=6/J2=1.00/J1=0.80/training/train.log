[2025-09-17 23:36:41] ==================================================
[2025-09-17 23:36:41] GCNN for Shastry-Sutherland Model
[2025-09-17 23:36:41] ==================================================
[2025-09-17 23:36:41] System parameters:
[2025-09-17 23:36:41]   - System size: L=6, N=144
[2025-09-17 23:36:41]   - System parameters: J1=0.8, J2=1.0, Q=0.0
[2025-09-17 23:36:41] --------------------------------------------------
[2025-09-17 23:36:41] Model parameters:
[2025-09-17 23:36:41]   - Number of layers = 4
[2025-09-17 23:36:41]   - Number of features = 4
[2025-09-17 23:36:41]   - Total parameters = 28252
[2025-09-17 23:36:41] --------------------------------------------------
[2025-09-17 23:36:41] Training parameters:
[2025-09-17 23:36:41]   - Learning rate: 0.015
[2025-09-17 23:36:41]   - Total iterations: 2250
[2025-09-17 23:36:41]   - Annealing cycles: 4
[2025-09-17 23:36:41]   - Initial period: 150
[2025-09-17 23:36:41]   - Period multiplier: 2.0
[2025-09-17 23:36:41]   - Temperature range: 0.0-1.0
[2025-09-17 23:36:41]   - Samples: 8192
[2025-09-17 23:36:41]   - Discarded samples: 0
[2025-09-17 23:36:41]   - Chunk size: 2048
[2025-09-17 23:36:41]   - Diagonal shift: 0.2
[2025-09-17 23:36:41]   - Gradient clipping: 1.0
[2025-09-17 23:36:41]   - Checkpoint enabled: interval=250
[2025-09-17 23:36:41]   - Checkpoint directory: results/L=6/J2=1.00/J1=0.80/training/checkpoints
[2025-09-17 23:36:41] --------------------------------------------------
[2025-09-17 23:36:41] Device status:
[2025-09-17 23:36:41]   - Devices model: NVIDIA H200 NVL
[2025-09-17 23:36:41]   - Number of devices: 1
[2025-09-17 23:36:41]   - Sharding: True
[2025-09-17 23:36:41] ============================================================
[2025-09-17 23:37:47] [Iter 1/2250] R0[0/150], Temp: 1.0000, Energy: 75.596453-0.001690j
[2025-09-17 23:38:16] [Iter 2/2250] R0[1/150], Temp: 0.9999, Energy: 75.593637+0.000500j
[2025-09-17 23:38:46] [Iter 3/2250] R0[2/150], Temp: 0.9996, Energy: 75.591375-0.001680j
[2025-09-17 23:39:15] [Iter 4/2250] R0[3/150], Temp: 0.9990, Energy: 75.592267+0.000489j
[2025-09-17 23:39:45] [Iter 5/2250] R0[4/150], Temp: 0.9982, Energy: 75.589025+0.000869j
[2025-09-17 23:40:14] [Iter 6/2250] R0[5/150], Temp: 0.9973, Energy: 75.589883-0.002481j
[2025-09-17 23:40:44] [Iter 7/2250] R0[6/150], Temp: 0.9961, Energy: 75.583438-0.000449j
[2025-09-17 23:41:14] [Iter 8/2250] R0[7/150], Temp: 0.9946, Energy: 75.588695+0.000132j
[2025-09-17 23:41:43] [Iter 9/2250] R0[8/150], Temp: 0.9930, Energy: 75.580640-0.002079j
[2025-09-17 23:42:13] [Iter 10/2250] R0[9/150], Temp: 0.9911, Energy: 75.581102-0.000783j
[2025-09-17 23:42:42] [Iter 11/2250] R0[10/150], Temp: 0.9891, Energy: 75.571145-0.005296j
[2025-09-17 23:43:12] [Iter 12/2250] R0[11/150], Temp: 0.9868, Energy: 75.567303+0.003640j
[2025-09-17 23:43:41] [Iter 13/2250] R0[12/150], Temp: 0.9843, Energy: 75.562031-0.001324j
[2025-09-17 23:44:11] [Iter 14/2250] R0[13/150], Temp: 0.9816, Energy: 75.546340+0.005628j
[2025-09-17 23:44:40] [Iter 15/2250] R0[14/150], Temp: 0.9787, Energy: 75.524919+0.005459j
[2025-09-17 23:45:10] [Iter 16/2250] R0[15/150], Temp: 0.9755, Energy: 75.514460-0.008311j
[2025-09-17 23:45:40] [Iter 17/2250] R0[16/150], Temp: 0.9722, Energy: 75.488039-0.011698j
[2025-09-17 23:46:09] [Iter 18/2250] R0[17/150], Temp: 0.9686, Energy: 75.442729+0.000702j
[2025-09-17 23:46:39] [Iter 19/2250] R0[18/150], Temp: 0.9649, Energy: 75.365269-0.002149j
[2025-09-17 23:47:08] [Iter 20/2250] R0[19/150], Temp: 0.9609, Energy: 75.317224-0.014814j
[2025-09-17 23:47:38] [Iter 21/2250] R0[20/150], Temp: 0.9568, Energy: 75.179783+0.013870j
[2025-09-17 23:48:07] [Iter 22/2250] R0[21/150], Temp: 0.9524, Energy: 74.952003-0.006405j
[2025-09-17 23:48:37] [Iter 23/2250] R0[22/150], Temp: 0.9479, Energy: 74.624388+0.029874j
[2025-09-17 23:49:06] [Iter 24/2250] R0[23/150], Temp: 0.9431, Energy: 74.021069+0.008942j
[2025-09-17 23:49:36] [Iter 25/2250] R0[24/150], Temp: 0.9382, Energy: 72.880925-0.043837j
[2025-09-17 23:50:05] [Iter 26/2250] R0[25/150], Temp: 0.9330, Energy: 70.483813-0.008829j
[2025-09-17 23:50:35] [Iter 27/2250] R0[26/150], Temp: 0.9277, Energy: 65.713357+0.065822j
[2025-09-17 23:51:05] [Iter 28/2250] R0[27/150], Temp: 0.9222, Energy: 58.947895-0.104710j
[2025-09-17 23:51:34] [Iter 29/2250] R0[28/150], Temp: 0.9165, Energy: 51.826731+0.040501j
[2025-09-17 23:52:04] [Iter 30/2250] R0[29/150], Temp: 0.9106, Energy: 46.532385+0.013650j
[2025-09-17 23:52:33] [Iter 31/2250] R0[30/150], Temp: 0.9045, Energy: 42.182614-0.012797j
[2025-09-17 23:53:03] [Iter 32/2250] R0[31/150], Temp: 0.8983, Energy: 38.090585-0.193395j
[2025-09-17 23:53:32] [Iter 33/2250] R0[32/150], Temp: 0.8918, Energy: 33.893498-0.024417j
[2025-09-17 23:54:02] [Iter 34/2250] R0[33/150], Temp: 0.8853, Energy: 29.724629+0.089957j
[2025-09-17 23:54:31] [Iter 35/2250] R0[34/150], Temp: 0.8785, Energy: 25.205859-0.058235j
[2025-09-17 23:55:01] [Iter 36/2250] R0[35/150], Temp: 0.8716, Energy: 20.525994-0.035310j
[2025-09-17 23:55:30] [Iter 37/2250] R0[36/150], Temp: 0.8645, Energy: 16.066508-0.004669j
[2025-09-17 23:56:00] [Iter 38/2250] R0[37/150], Temp: 0.8572, Energy: 11.520556+0.063007j
[2025-09-17 23:56:29] [Iter 39/2250] R0[38/150], Temp: 0.8498, Energy: 7.153862+0.051654j
[2025-09-17 23:56:59] [Iter 40/2250] R0[39/150], Temp: 0.8423, Energy: 3.050497-0.054963j
[2025-09-17 23:57:29] [Iter 41/2250] R0[40/150], Temp: 0.8346, Energy: -0.891311+0.006585j
[2025-09-17 23:57:58] [Iter 42/2250] R0[41/150], Temp: 0.8267, Energy: -4.492931-0.048299j
[2025-09-17 23:58:28] [Iter 43/2250] R0[42/150], Temp: 0.8187, Energy: -7.669504+0.044013j
[2025-09-17 23:58:57] [Iter 44/2250] R0[43/150], Temp: 0.8106, Energy: -10.763534+0.014121j
[2025-09-17 23:59:27] [Iter 45/2250] R0[44/150], Temp: 0.8023, Energy: -13.379659-0.127328j
[2025-09-17 23:59:56] [Iter 46/2250] R0[45/150], Temp: 0.7939, Energy: -15.741404-0.104311j
[2025-09-18 00:00:26] [Iter 47/2250] R0[46/150], Temp: 0.7854, Energy: -17.999741+0.015969j
[2025-09-18 00:00:55] [Iter 48/2250] R0[47/150], Temp: 0.7767, Energy: -19.985255-0.019781j
[2025-09-18 00:01:25] [Iter 49/2250] R0[48/150], Temp: 0.7679, Energy: -21.685421-0.037420j
[2025-09-18 00:01:54] [Iter 50/2250] R0[49/150], Temp: 0.7590, Energy: -23.549308-0.027805j
[2025-09-18 00:02:24] [Iter 51/2250] R0[50/150], Temp: 0.7500, Energy: -25.104960-0.075934j
[2025-09-18 00:02:53] [Iter 52/2250] R0[51/150], Temp: 0.7409, Energy: -26.399827-0.050001j
[2025-09-18 00:03:23] [Iter 53/2250] R0[52/150], Temp: 0.7316, Energy: -27.900302+0.002121j
[2025-09-18 00:03:52] [Iter 54/2250] R0[53/150], Temp: 0.7223, Energy: -28.993289-0.001064j
[2025-09-18 00:04:22] [Iter 55/2250] R0[54/150], Temp: 0.7129, Energy: -30.152382-0.056080j
[2025-09-18 00:04:51] [Iter 56/2250] R0[55/150], Temp: 0.7034, Energy: -31.237180-0.025100j
[2025-09-18 00:05:21] [Iter 57/2250] R0[56/150], Temp: 0.6938, Energy: -32.185812+0.004157j
[2025-09-18 00:05:51] [Iter 58/2250] R0[57/150], Temp: 0.6841, Energy: -33.116760+0.041709j
[2025-09-18 00:06:20] [Iter 59/2250] R0[58/150], Temp: 0.6743, Energy: -34.029270-0.060801j
[2025-09-18 00:06:50] [Iter 60/2250] R0[59/150], Temp: 0.6644, Energy: -34.796377+0.002803j
[2025-09-18 00:07:19] [Iter 61/2250] R0[60/150], Temp: 0.6545, Energy: -35.617903+0.031260j
[2025-09-18 00:07:49] [Iter 62/2250] R0[61/150], Temp: 0.6445, Energy: -36.311921+0.048398j
[2025-09-18 00:08:18] [Iter 63/2250] R0[62/150], Temp: 0.6345, Energy: -36.956739+0.033638j
[2025-09-18 00:08:48] [Iter 64/2250] R0[63/150], Temp: 0.6243, Energy: -37.661152+0.020458j
[2025-09-18 00:09:17] [Iter 65/2250] R0[64/150], Temp: 0.6142, Energy: -38.289607+0.035876j
[2025-09-18 00:09:47] [Iter 66/2250] R0[65/150], Temp: 0.6040, Energy: -38.807228-0.017468j
[2025-09-18 00:10:16] [Iter 67/2250] R0[66/150], Temp: 0.5937, Energy: -39.287935-0.008777j
[2025-09-18 00:10:46] [Iter 68/2250] R0[67/150], Temp: 0.5834, Energy: -39.888464+0.042446j
[2025-09-18 00:11:16] [Iter 69/2250] R0[68/150], Temp: 0.5730, Energy: -40.355849+0.038887j
[2025-09-18 00:11:45] [Iter 70/2250] R0[69/150], Temp: 0.5627, Energy: -40.773948+0.061141j
[2025-09-18 00:12:15] [Iter 71/2250] R0[70/150], Temp: 0.5523, Energy: -41.323612-0.011019j
[2025-09-18 00:12:44] [Iter 72/2250] R0[71/150], Temp: 0.5418, Energy: -41.661421-0.062764j
[2025-09-18 00:13:14] [Iter 73/2250] R0[72/150], Temp: 0.5314, Energy: -42.094748-0.030530j
[2025-09-18 00:13:43] [Iter 74/2250] R0[73/150], Temp: 0.5209, Energy: -42.498665-0.044614j
[2025-09-18 00:14:13] [Iter 75/2250] R0[74/150], Temp: 0.5105, Energy: -42.854103-0.059452j
[2025-09-18 00:14:42] [Iter 76/2250] R0[75/150], Temp: 0.5000, Energy: -43.117088+0.074008j
[2025-09-18 00:15:12] [Iter 77/2250] R0[76/150], Temp: 0.4895, Energy: -43.435164-0.014812j
[2025-09-18 00:15:41] [Iter 78/2250] R0[77/150], Temp: 0.4791, Energy: -43.840123+0.042136j
[2025-09-18 00:16:11] [Iter 79/2250] R0[78/150], Temp: 0.4686, Energy: -44.124955+0.043786j
[2025-09-18 00:16:40] [Iter 80/2250] R0[79/150], Temp: 0.4582, Energy: -44.459409+0.037741j
[2025-09-18 00:17:10] [Iter 81/2250] R0[80/150], Temp: 0.4477, Energy: -44.710264-0.099283j
[2025-09-18 00:17:39] [Iter 82/2250] R0[81/150], Temp: 0.4373, Energy: -44.925018-0.027276j
[2025-09-18 00:18:09] [Iter 83/2250] R0[82/150], Temp: 0.4270, Energy: -45.248204+0.009844j
[2025-09-18 00:18:38] [Iter 84/2250] R0[83/150], Temp: 0.4166, Energy: -45.561312+0.001167j
[2025-09-18 00:19:08] [Iter 85/2250] R0[84/150], Temp: 0.4063, Energy: -45.741939+0.015449j
[2025-09-18 00:19:37] [Iter 86/2250] R0[85/150], Temp: 0.3960, Energy: -45.954850-0.009758j
[2025-09-18 00:20:07] [Iter 87/2250] R0[86/150], Temp: 0.3858, Energy: -46.229107-0.040457j
[2025-09-18 00:20:36] [Iter 88/2250] R0[87/150], Temp: 0.3757, Energy: -46.466162+0.041150j
[2025-09-18 00:21:06] [Iter 89/2250] R0[88/150], Temp: 0.3655, Energy: -46.604247-0.010570j
[2025-09-18 00:21:36] [Iter 90/2250] R0[89/150], Temp: 0.3555, Energy: -46.851355-0.030027j
[2025-09-18 00:22:05] [Iter 91/2250] R0[90/150], Temp: 0.3455, Energy: -47.025480+0.028179j
[2025-09-18 00:22:35] [Iter 92/2250] R0[91/150], Temp: 0.3356, Energy: -47.198932+0.009265j
[2025-09-18 00:23:04] [Iter 93/2250] R0[92/150], Temp: 0.3257, Energy: -47.443004-0.062898j
[2025-09-18 00:23:34] [Iter 94/2250] R0[93/150], Temp: 0.3159, Energy: -47.539043-0.002378j
[2025-09-18 00:24:03] [Iter 95/2250] R0[94/150], Temp: 0.3062, Energy: -47.719145-0.062700j
[2025-09-18 00:24:33] [Iter 96/2250] R0[95/150], Temp: 0.2966, Energy: -47.845642+0.017597j
[2025-09-18 00:25:02] [Iter 97/2250] R0[96/150], Temp: 0.2871, Energy: -48.062313-0.062474j
[2025-09-18 00:25:32] [Iter 98/2250] R0[97/150], Temp: 0.2777, Energy: -48.218239-0.012165j
[2025-09-18 00:26:01] [Iter 99/2250] R0[98/150], Temp: 0.2684, Energy: -48.448111+0.029075j
[2025-09-18 00:26:31] [Iter 100/2250] R0[99/150], Temp: 0.2591, Energy: -48.615633-0.032885j
[2025-09-18 00:27:00] [Iter 101/2250] R0[100/150], Temp: 0.2500, Energy: -48.742014+0.084738j
[2025-09-18 00:27:30] [Iter 102/2250] R0[101/150], Temp: 0.2410, Energy: -48.965267+0.074143j
[2025-09-18 00:27:59] [Iter 103/2250] R0[102/150], Temp: 0.2321, Energy: -49.058940+0.027376j
[2025-09-18 00:28:29] [Iter 104/2250] R0[103/150], Temp: 0.2233, Energy: -49.281704+0.017827j
[2025-09-18 00:28:58] [Iter 105/2250] R0[104/150], Temp: 0.2146, Energy: -49.378675-0.008505j
[2025-09-18 00:29:28] [Iter 106/2250] R0[105/150], Temp: 0.2061, Energy: -49.480322-0.014134j
[2025-09-18 00:29:58] [Iter 107/2250] R0[106/150], Temp: 0.1977, Energy: -49.607827+0.043458j
[2025-09-18 00:30:27] [Iter 108/2250] R0[107/150], Temp: 0.1894, Energy: -49.716774-0.020317j
[2025-09-18 00:30:57] [Iter 109/2250] R0[108/150], Temp: 0.1813, Energy: -49.807168+0.004934j
[2025-09-18 00:31:26] [Iter 110/2250] R0[109/150], Temp: 0.1733, Energy: -49.910663+0.017145j
[2025-09-18 00:31:56] [Iter 111/2250] R0[110/150], Temp: 0.1654, Energy: -50.133555+0.033491j
[2025-09-18 00:32:25] [Iter 112/2250] R0[111/150], Temp: 0.1577, Energy: -50.276672+0.006055j
[2025-09-18 00:32:55] [Iter 113/2250] R0[112/150], Temp: 0.1502, Energy: -50.385439+0.048621j
[2025-09-18 00:33:24] [Iter 114/2250] R0[113/150], Temp: 0.1428, Energy: -50.432605-0.047029j
[2025-09-18 00:33:54] [Iter 115/2250] R0[114/150], Temp: 0.1355, Energy: -50.623448+0.005670j
[2025-09-18 00:34:23] [Iter 116/2250] R0[115/150], Temp: 0.1284, Energy: -50.703472-0.035341j
[2025-09-18 00:34:53] [Iter 117/2250] R0[116/150], Temp: 0.1215, Energy: -50.852366+0.044948j
[2025-09-18 00:35:22] [Iter 118/2250] R0[117/150], Temp: 0.1147, Energy: -51.025389-0.004055j
[2025-09-18 00:35:52] [Iter 119/2250] R0[118/150], Temp: 0.1082, Energy: -51.075691-0.051425j
[2025-09-18 00:36:22] [Iter 120/2250] R0[119/150], Temp: 0.1017, Energy: -51.198226-0.058437j
[2025-09-18 00:36:51] [Iter 121/2250] R0[120/150], Temp: 0.0955, Energy: -51.320117-0.044285j
[2025-09-18 00:37:21] [Iter 122/2250] R0[121/150], Temp: 0.0894, Energy: -51.454599-0.004798j
[2025-09-18 00:37:50] [Iter 123/2250] R0[122/150], Temp: 0.0835, Energy: -51.582264+0.004071j
[2025-09-18 00:38:20] [Iter 124/2250] R0[123/150], Temp: 0.0778, Energy: -51.687986+0.034743j
[2025-09-18 00:38:49] [Iter 125/2250] R0[124/150], Temp: 0.0723, Energy: -51.783996-0.008852j
[2025-09-18 00:39:19] [Iter 126/2250] R0[125/150], Temp: 0.0670, Energy: -51.913246+0.009925j
[2025-09-18 00:39:48] [Iter 127/2250] R0[126/150], Temp: 0.0618, Energy: -51.963929+0.032913j
[2025-09-18 00:40:18] [Iter 128/2250] R0[127/150], Temp: 0.0569, Energy: -52.055315+0.024681j
[2025-09-18 00:40:47] [Iter 129/2250] R0[128/150], Temp: 0.0521, Energy: -52.212756+0.017976j
[2025-09-18 00:41:17] [Iter 130/2250] R0[129/150], Temp: 0.0476, Energy: -52.316341-0.013883j
[2025-09-18 00:41:46] [Iter 131/2250] R0[130/150], Temp: 0.0432, Energy: -52.418709-0.019989j
[2025-09-18 00:42:16] [Iter 132/2250] R0[131/150], Temp: 0.0391, Energy: -52.500746+0.011421j
[2025-09-18 00:42:46] [Iter 133/2250] R0[132/150], Temp: 0.0351, Energy: -52.602278-0.024320j
[2025-09-18 00:43:15] [Iter 134/2250] R0[133/150], Temp: 0.0314, Energy: -52.706759-0.022716j
[2025-09-18 00:43:45] [Iter 135/2250] R0[134/150], Temp: 0.0278, Energy: -52.803178+0.018765j
[2025-09-18 00:44:14] [Iter 136/2250] R0[135/150], Temp: 0.0245, Energy: -52.966664+0.032535j
[2025-09-18 00:44:44] [Iter 137/2250] R0[136/150], Temp: 0.0213, Energy: -53.020025+0.008092j
[2025-09-18 00:45:13] [Iter 138/2250] R0[137/150], Temp: 0.0184, Energy: -53.123777-0.036882j
[2025-09-18 00:45:43] [Iter 139/2250] R0[138/150], Temp: 0.0157, Energy: -53.185500+0.037764j
[2025-09-18 00:46:12] [Iter 140/2250] R0[139/150], Temp: 0.0132, Energy: -53.356003-0.014977j
[2025-09-18 00:46:42] [Iter 141/2250] R0[140/150], Temp: 0.0109, Energy: -53.457041+0.046940j
[2025-09-18 00:47:11] [Iter 142/2250] R0[141/150], Temp: 0.0089, Energy: -53.516715+0.010483j
[2025-09-18 00:47:41] [Iter 143/2250] R0[142/150], Temp: 0.0070, Energy: -53.650310+0.019236j
[2025-09-18 00:48:10] [Iter 144/2250] R0[143/150], Temp: 0.0054, Energy: -53.744849+0.025354j
[2025-09-18 00:48:40] [Iter 145/2250] R0[144/150], Temp: 0.0039, Energy: -53.860737-0.004368j
[2025-09-18 00:49:09] [Iter 146/2250] R0[145/150], Temp: 0.0027, Energy: -53.949271-0.003454j
[2025-09-18 00:49:39] [Iter 147/2250] R0[146/150], Temp: 0.0018, Energy: -53.984695-0.000966j
[2025-09-18 00:50:08] [Iter 148/2250] R0[147/150], Temp: 0.0010, Energy: -54.150202+0.002885j
[2025-09-18 00:50:38] [Iter 149/2250] R0[148/150], Temp: 0.0004, Energy: -54.236666-0.006546j
[2025-09-18 00:51:07] [Iter 150/2250] R0[149/150], Temp: 0.0001, Energy: -54.325581-0.018099j
[2025-09-18 00:51:07] RESTART #1 | Period: 300
[2025-09-18 00:51:37] [Iter 151/2250] R1[0/300], Temp: 1.0000, Energy: -54.445859+0.080207j
[2025-09-18 00:52:07] [Iter 152/2250] R1[1/300], Temp: 1.0000, Energy: -54.537268+0.029789j
[2025-09-18 00:52:36] [Iter 153/2250] R1[2/300], Temp: 0.9999, Energy: -54.558793-0.014381j
[2025-09-18 00:53:06] [Iter 154/2250] R1[3/300], Temp: 0.9998, Energy: -54.672744-0.032541j
[2025-09-18 00:53:35] [Iter 155/2250] R1[4/300], Temp: 0.9996, Energy: -54.805865-0.022093j
[2025-09-18 00:54:05] [Iter 156/2250] R1[5/300], Temp: 0.9993, Energy: -54.850120+0.042084j
[2025-09-18 00:54:34] [Iter 157/2250] R1[6/300], Temp: 0.9990, Energy: -55.001862-0.020169j
[2025-09-18 00:55:04] [Iter 158/2250] R1[7/300], Temp: 0.9987, Energy: -55.058844-0.007386j
[2025-09-18 00:55:33] [Iter 159/2250] R1[8/300], Temp: 0.9982, Energy: -55.088099+0.006421j
[2025-09-18 00:56:03] [Iter 160/2250] R1[9/300], Temp: 0.9978, Energy: -55.196511-0.014797j
[2025-09-18 00:56:32] [Iter 161/2250] R1[10/300], Temp: 0.9973, Energy: -55.325216+0.032892j
[2025-09-18 00:57:02] [Iter 162/2250] R1[11/300], Temp: 0.9967, Energy: -55.379222+0.034227j
[2025-09-18 00:57:31] [Iter 163/2250] R1[12/300], Temp: 0.9961, Energy: -55.473543-0.007740j
[2025-09-18 00:58:01] [Iter 164/2250] R1[13/300], Temp: 0.9954, Energy: -55.596545+0.022294j
[2025-09-18 00:58:31] [Iter 165/2250] R1[14/300], Temp: 0.9946, Energy: -55.624793+0.009741j
[2025-09-18 00:59:00] [Iter 166/2250] R1[15/300], Temp: 0.9938, Energy: -55.732810-0.052385j
[2025-09-18 00:59:30] [Iter 167/2250] R1[16/300], Temp: 0.9930, Energy: -55.753240+0.012473j
[2025-09-18 00:59:59] [Iter 168/2250] R1[17/300], Temp: 0.9921, Energy: -55.865596-0.049312j
[2025-09-18 01:00:29] [Iter 169/2250] R1[18/300], Temp: 0.9911, Energy: -55.879331+0.005289j
[2025-09-18 01:00:58] [Iter 170/2250] R1[19/300], Temp: 0.9901, Energy: -55.956368-0.025843j
[2025-09-18 01:01:28] [Iter 171/2250] R1[20/300], Temp: 0.9891, Energy: -56.047512-0.022624j
[2025-09-18 01:01:57] [Iter 172/2250] R1[21/300], Temp: 0.9880, Energy: -56.192098-0.022292j
[2025-09-18 01:02:27] [Iter 173/2250] R1[22/300], Temp: 0.9868, Energy: -56.235759-0.035722j
[2025-09-18 01:02:56] [Iter 174/2250] R1[23/300], Temp: 0.9856, Energy: -56.286977+0.014499j
[2025-09-18 01:03:26] [Iter 175/2250] R1[24/300], Temp: 0.9843, Energy: -56.383904-0.005176j
[2025-09-18 01:03:55] [Iter 176/2250] R1[25/300], Temp: 0.9830, Energy: -56.482667+0.019254j
[2025-09-18 01:04:25] [Iter 177/2250] R1[26/300], Temp: 0.9816, Energy: -56.512596+0.019640j
[2025-09-18 01:04:54] [Iter 178/2250] R1[27/300], Temp: 0.9801, Energy: -56.626840+0.006402j
[2025-09-18 01:05:24] [Iter 179/2250] R1[28/300], Temp: 0.9787, Energy: -56.654193+0.020901j
[2025-09-18 01:05:53] [Iter 180/2250] R1[29/300], Temp: 0.9771, Energy: -56.772401+0.011146j
[2025-09-18 01:06:23] [Iter 181/2250] R1[30/300], Temp: 0.9755, Energy: -56.818956-0.017815j
[2025-09-18 01:06:52] [Iter 182/2250] R1[31/300], Temp: 0.9739, Energy: -56.862804-0.058100j
[2025-09-18 01:07:22] [Iter 183/2250] R1[32/300], Temp: 0.9722, Energy: -56.974666-0.032114j
[2025-09-18 01:07:52] [Iter 184/2250] R1[33/300], Temp: 0.9704, Energy: -57.041830+0.001399j
[2025-09-18 01:08:21] [Iter 185/2250] R1[34/300], Temp: 0.9686, Energy: -57.072701-0.019826j
[2025-09-18 01:08:51] [Iter 186/2250] R1[35/300], Temp: 0.9668, Energy: -57.163223+0.022021j
[2025-09-18 01:09:20] [Iter 187/2250] R1[36/300], Temp: 0.9649, Energy: -57.223476-0.034564j
[2025-09-18 01:09:50] [Iter 188/2250] R1[37/300], Temp: 0.9629, Energy: -57.270170+0.025359j
[2025-09-18 01:10:19] [Iter 189/2250] R1[38/300], Temp: 0.9609, Energy: -57.314479-0.027962j
[2025-09-18 01:10:49] [Iter 190/2250] R1[39/300], Temp: 0.9589, Energy: -57.444422-0.005038j
[2025-09-18 01:11:18] [Iter 191/2250] R1[40/300], Temp: 0.9568, Energy: -57.504995+0.022787j
[2025-09-18 01:11:48] [Iter 192/2250] R1[41/300], Temp: 0.9546, Energy: -57.610083-0.039989j
[2025-09-18 01:12:17] [Iter 193/2250] R1[42/300], Temp: 0.9524, Energy: -57.655202+0.049387j
[2025-09-18 01:12:47] [Iter 194/2250] R1[43/300], Temp: 0.9502, Energy: -57.745632+0.025220j
[2025-09-18 01:13:16] [Iter 195/2250] R1[44/300], Temp: 0.9479, Energy: -57.722466-0.037816j
[2025-09-18 01:13:46] [Iter 196/2250] R1[45/300], Temp: 0.9455, Energy: -57.890657+0.010003j
[2025-09-18 01:14:15] [Iter 197/2250] R1[46/300], Temp: 0.9431, Energy: -57.886192+0.034909j
[2025-09-18 01:14:45] [Iter 198/2250] R1[47/300], Temp: 0.9407, Energy: -58.012445+0.000354j
[2025-09-18 01:15:14] [Iter 199/2250] R1[48/300], Temp: 0.9382, Energy: -58.048855-0.023653j
[2025-09-18 01:15:44] [Iter 200/2250] R1[49/300], Temp: 0.9356, Energy: -58.118429-0.013844j
[2025-09-18 01:16:13] [Iter 201/2250] R1[50/300], Temp: 0.9330, Energy: -58.203294+0.012927j
[2025-09-18 01:16:43] [Iter 202/2250] R1[51/300], Temp: 0.9304, Energy: -58.261600-0.014903j
[2025-09-18 01:17:12] [Iter 203/2250] R1[52/300], Temp: 0.9277, Energy: -58.356305-0.021274j
[2025-09-18 01:17:42] [Iter 204/2250] R1[53/300], Temp: 0.9249, Energy: -58.448842+0.024851j
[2025-09-18 01:18:11] [Iter 205/2250] R1[54/300], Temp: 0.9222, Energy: -58.451696+0.016266j
[2025-09-18 01:18:41] [Iter 206/2250] R1[55/300], Temp: 0.9193, Energy: -58.591164-0.009612j
[2025-09-18 01:19:11] [Iter 207/2250] R1[56/300], Temp: 0.9165, Energy: -58.612408+0.014230j
[2025-09-18 01:19:40] [Iter 208/2250] R1[57/300], Temp: 0.9135, Energy: -58.683630+0.025901j
[2025-09-18 01:20:10] [Iter 209/2250] R1[58/300], Temp: 0.9106, Energy: -58.790238-0.006744j
[2025-09-18 01:20:39] [Iter 210/2250] R1[59/300], Temp: 0.9076, Energy: -58.842880+0.001067j
[2025-09-18 01:21:09] [Iter 211/2250] R1[60/300], Temp: 0.9045, Energy: -58.880578+0.022847j
[2025-09-18 01:21:38] [Iter 212/2250] R1[61/300], Temp: 0.9014, Energy: -58.984755+0.022157j
[2025-09-18 01:22:08] [Iter 213/2250] R1[62/300], Temp: 0.8983, Energy: -59.035209-0.025869j
[2025-09-18 01:22:37] [Iter 214/2250] R1[63/300], Temp: 0.8951, Energy: -59.100359+0.019004j
[2025-09-18 01:23:07] [Iter 215/2250] R1[64/300], Temp: 0.8918, Energy: -59.199610-0.000856j
[2025-09-18 01:23:36] [Iter 216/2250] R1[65/300], Temp: 0.8886, Energy: -59.274213-0.044924j
[2025-09-18 01:24:06] [Iter 217/2250] R1[66/300], Temp: 0.8853, Energy: -59.367767-0.046493j
[2025-09-18 01:24:35] [Iter 218/2250] R1[67/300], Temp: 0.8819, Energy: -59.472838+0.015509j
[2025-09-18 01:25:05] [Iter 219/2250] R1[68/300], Temp: 0.8785, Energy: -59.529549+0.044210j
[2025-09-18 01:25:34] [Iter 220/2250] R1[69/300], Temp: 0.8751, Energy: -59.529430-0.012731j
[2025-09-18 01:26:04] [Iter 221/2250] R1[70/300], Temp: 0.8716, Energy: -59.673892-0.006830j
[2025-09-18 01:26:33] [Iter 222/2250] R1[71/300], Temp: 0.8680, Energy: -59.781396-0.000689j
[2025-09-18 01:27:03] [Iter 223/2250] R1[72/300], Temp: 0.8645, Energy: -59.852001-0.002474j
[2025-09-18 01:27:32] [Iter 224/2250] R1[73/300], Temp: 0.8609, Energy: -59.921437-0.028184j
[2025-09-18 01:28:02] [Iter 225/2250] R1[74/300], Temp: 0.8572, Energy: -59.993663-0.000723j
[2025-09-18 01:28:31] [Iter 226/2250] R1[75/300], Temp: 0.8536, Energy: -60.105691+0.009429j
[2025-09-18 01:29:01] [Iter 227/2250] R1[76/300], Temp: 0.8498, Energy: -60.203331-0.033429j
[2025-09-18 01:29:30] [Iter 228/2250] R1[77/300], Temp: 0.8461, Energy: -60.264252-0.010720j
[2025-09-18 01:30:00] [Iter 229/2250] R1[78/300], Temp: 0.8423, Energy: -60.328001-0.019189j
[2025-09-18 01:30:29] [Iter 230/2250] R1[79/300], Temp: 0.8384, Energy: -60.438111+0.014818j
[2025-09-18 01:30:59] [Iter 231/2250] R1[80/300], Temp: 0.8346, Energy: -60.553754+0.005739j
[2025-09-18 01:31:28] [Iter 232/2250] R1[81/300], Temp: 0.8307, Energy: -60.694466-0.008140j
[2025-09-18 01:31:58] [Iter 233/2250] R1[82/300], Temp: 0.8267, Energy: -60.734917+0.002346j
[2025-09-18 01:32:27] [Iter 234/2250] R1[83/300], Temp: 0.8227, Energy: -60.833837+0.010003j
[2025-09-18 01:32:57] [Iter 235/2250] R1[84/300], Temp: 0.8187, Energy: -60.926014+0.017715j
[2025-09-18 01:33:27] [Iter 236/2250] R1[85/300], Temp: 0.8147, Energy: -61.030014+0.016086j
[2025-09-18 01:33:56] [Iter 237/2250] R1[86/300], Temp: 0.8106, Energy: -61.163448-0.007145j
[2025-09-18 01:34:26] [Iter 238/2250] R1[87/300], Temp: 0.8065, Energy: -61.217606-0.008914j
[2025-09-18 01:34:55] [Iter 239/2250] R1[88/300], Temp: 0.8023, Energy: -61.343091-0.011455j
[2025-09-18 01:35:25] [Iter 240/2250] R1[89/300], Temp: 0.7981, Energy: -61.441117+0.005211j
[2025-09-18 01:35:54] [Iter 241/2250] R1[90/300], Temp: 0.7939, Energy: -61.492573+0.002531j
[2025-09-18 01:36:24] [Iter 242/2250] R1[91/300], Temp: 0.7896, Energy: -61.643676-0.012048j
[2025-09-18 01:36:53] [Iter 243/2250] R1[92/300], Temp: 0.7854, Energy: -61.774523+0.001980j
[2025-09-18 01:37:23] [Iter 244/2250] R1[93/300], Temp: 0.7810, Energy: -61.840955-0.026231j
[2025-09-18 01:37:52] [Iter 245/2250] R1[94/300], Temp: 0.7767, Energy: -61.933292+0.001272j
[2025-09-18 01:38:22] [Iter 246/2250] R1[95/300], Temp: 0.7723, Energy: -62.061419+0.010836j
[2025-09-18 01:38:51] [Iter 247/2250] R1[96/300], Temp: 0.7679, Energy: -62.121317+0.001050j
[2025-09-18 01:39:21] [Iter 248/2250] R1[97/300], Temp: 0.7635, Energy: -62.190055+0.006321j
[2025-09-18 01:39:50] [Iter 249/2250] R1[98/300], Temp: 0.7590, Energy: -62.308566+0.009209j
[2025-09-18 01:40:20] [Iter 250/2250] R1[99/300], Temp: 0.7545, Energy: -62.411650-0.024253j
[2025-09-18 01:40:20] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-09-18 01:40:50] [Iter 251/2250] R1[100/300], Temp: 0.7500, Energy: -62.478375+0.041295j
[2025-09-18 01:41:19] [Iter 252/2250] R1[101/300], Temp: 0.7455, Energy: -62.575614+0.009589j
[2025-09-18 01:41:49] [Iter 253/2250] R1[102/300], Temp: 0.7409, Energy: -62.646415-0.018125j
[2025-09-18 01:42:18] [Iter 254/2250] R1[103/300], Temp: 0.7363, Energy: -62.698722+0.004351j
[2025-09-18 01:42:48] [Iter 255/2250] R1[104/300], Temp: 0.7316, Energy: -62.758026-0.034745j
[2025-09-18 01:43:17] [Iter 256/2250] R1[105/300], Temp: 0.7270, Energy: -62.852608-0.003984j
[2025-09-18 01:43:47] [Iter 257/2250] R1[106/300], Temp: 0.7223, Energy: -62.876963+0.007595j
[2025-09-18 01:44:16] [Iter 258/2250] R1[107/300], Temp: 0.7176, Energy: -62.985958-0.012923j
[2025-09-18 01:44:46] [Iter 259/2250] R1[108/300], Temp: 0.7129, Energy: -62.991408-0.007188j
[2025-09-18 01:45:15] [Iter 260/2250] R1[109/300], Temp: 0.7081, Energy: -63.066811+0.013502j
[2025-09-18 01:45:45] [Iter 261/2250] R1[110/300], Temp: 0.7034, Energy: -63.144621-0.008449j
[2025-09-18 01:46:14] [Iter 262/2250] R1[111/300], Temp: 0.6986, Energy: -63.180813-0.005030j
[2025-09-18 01:46:44] [Iter 263/2250] R1[112/300], Temp: 0.6938, Energy: -63.208710+0.002385j
[2025-09-18 01:47:13] [Iter 264/2250] R1[113/300], Temp: 0.6889, Energy: -63.248399+0.003543j
[2025-09-18 01:47:43] [Iter 265/2250] R1[114/300], Temp: 0.6841, Energy: -63.294876-0.004883j
[2025-09-18 01:48:12] [Iter 266/2250] R1[115/300], Temp: 0.6792, Energy: -63.348521-0.004668j
[2025-09-18 01:48:42] [Iter 267/2250] R1[116/300], Temp: 0.6743, Energy: -63.413020+0.016625j
[2025-09-18 01:49:11] [Iter 268/2250] R1[117/300], Temp: 0.6694, Energy: -63.448066+0.009085j
[2025-09-18 01:49:41] [Iter 269/2250] R1[118/300], Temp: 0.6644, Energy: -63.500109+0.000915j
[2025-09-18 01:50:10] [Iter 270/2250] R1[119/300], Temp: 0.6595, Energy: -63.481316+0.002117j
[2025-09-18 01:50:40] [Iter 271/2250] R1[120/300], Temp: 0.6545, Energy: -63.515390-0.003655j
[2025-09-18 01:51:09] [Iter 272/2250] R1[121/300], Temp: 0.6495, Energy: -63.551359-0.010622j
[2025-09-18 01:51:39] [Iter 273/2250] R1[122/300], Temp: 0.6445, Energy: -63.575671-0.014824j
[2025-09-18 01:52:08] [Iter 274/2250] R1[123/300], Temp: 0.6395, Energy: -63.601282+0.001302j
[2025-09-18 01:52:38] [Iter 275/2250] R1[124/300], Temp: 0.6345, Energy: -63.638400+0.004580j
[2025-09-18 01:53:07] [Iter 276/2250] R1[125/300], Temp: 0.6294, Energy: -63.651478-0.009464j
[2025-09-18 01:53:37] [Iter 277/2250] R1[126/300], Temp: 0.6243, Energy: -63.683527-0.006265j
[2025-09-18 01:54:07] [Iter 278/2250] R1[127/300], Temp: 0.6193, Energy: -63.699522-0.000290j
[2025-09-18 01:54:36] [Iter 279/2250] R1[128/300], Temp: 0.6142, Energy: -63.746150+0.001601j
[2025-09-18 01:55:06] [Iter 280/2250] R1[129/300], Temp: 0.6091, Energy: -63.779277+0.002243j
[2025-09-18 01:55:35] [Iter 281/2250] R1[130/300], Temp: 0.6040, Energy: -63.766593+0.007681j
[2025-09-18 01:56:05] [Iter 282/2250] R1[131/300], Temp: 0.5988, Energy: -63.774702+0.003546j
[2025-09-18 01:56:34] [Iter 283/2250] R1[132/300], Temp: 0.5937, Energy: -63.767227+0.013316j
[2025-09-18 01:57:04] [Iter 284/2250] R1[133/300], Temp: 0.5885, Energy: -63.809074+0.010542j
[2025-09-18 01:57:33] [Iter 285/2250] R1[134/300], Temp: 0.5834, Energy: -63.855591+0.006392j
[2025-09-18 01:58:03] [Iter 286/2250] R1[135/300], Temp: 0.5782, Energy: -63.818118-0.001416j
[2025-09-18 01:58:32] [Iter 287/2250] R1[136/300], Temp: 0.5730, Energy: -63.856949+0.008499j
[2025-09-18 01:59:02] [Iter 288/2250] R1[137/300], Temp: 0.5679, Energy: -63.864165+0.005929j
[2025-09-18 01:59:31] [Iter 289/2250] R1[138/300], Temp: 0.5627, Energy: -63.879483+0.003214j
[2025-09-18 02:00:01] [Iter 290/2250] R1[139/300], Temp: 0.5575, Energy: -63.908301+0.001068j
[2025-09-18 02:00:30] [Iter 291/2250] R1[140/300], Temp: 0.5523, Energy: -63.891406-0.001424j
[2025-09-18 02:01:00] [Iter 292/2250] R1[141/300], Temp: 0.5471, Energy: -63.935575-0.009278j
[2025-09-18 02:01:30] [Iter 293/2250] R1[142/300], Temp: 0.5418, Energy: -63.936464-0.008495j
[2025-09-18 02:01:59] [Iter 294/2250] R1[143/300], Temp: 0.5366, Energy: -63.913451-0.003696j
[2025-09-18 02:02:29] [Iter 295/2250] R1[144/300], Temp: 0.5314, Energy: -63.933334-0.000064j
[2025-09-18 02:02:58] [Iter 296/2250] R1[145/300], Temp: 0.5262, Energy: -63.990251+0.006580j
[2025-09-18 02:03:28] [Iter 297/2250] R1[146/300], Temp: 0.5209, Energy: -63.995755-0.005874j
[2025-09-18 02:03:57] [Iter 298/2250] R1[147/300], Temp: 0.5157, Energy: -63.980976+0.005329j
[2025-09-18 02:04:27] [Iter 299/2250] R1[148/300], Temp: 0.5105, Energy: -63.998238+0.001894j
[2025-09-18 02:04:56] [Iter 300/2250] R1[149/300], Temp: 0.5052, Energy: -63.996913+0.012360j
[2025-09-18 02:05:26] [Iter 301/2250] R1[150/300], Temp: 0.5000, Energy: -64.019183+0.007752j
[2025-09-18 02:05:56] [Iter 302/2250] R1[151/300], Temp: 0.4948, Energy: -64.044699+0.014507j
[2025-09-18 02:06:25] [Iter 303/2250] R1[152/300], Temp: 0.4895, Energy: -64.013818+0.008790j
[2025-09-18 02:06:55] [Iter 304/2250] R1[153/300], Temp: 0.4843, Energy: -64.037461+0.007059j
[2025-09-18 02:07:24] [Iter 305/2250] R1[154/300], Temp: 0.4791, Energy: -64.038357+0.012596j
[2025-09-18 02:07:54] [Iter 306/2250] R1[155/300], Temp: 0.4738, Energy: -64.039944-0.004469j
[2025-09-18 02:08:23] [Iter 307/2250] R1[156/300], Temp: 0.4686, Energy: -64.065637-0.008083j
[2025-09-18 02:08:53] [Iter 308/2250] R1[157/300], Temp: 0.4634, Energy: -64.066600-0.005533j
[2025-09-18 02:09:22] [Iter 309/2250] R1[158/300], Temp: 0.4582, Energy: -64.084033-0.002167j
[2025-09-18 02:09:52] [Iter 310/2250] R1[159/300], Temp: 0.4529, Energy: -64.081367-0.003449j
[2025-09-18 02:10:21] [Iter 311/2250] R1[160/300], Temp: 0.4477, Energy: -64.059509-0.009332j
[2025-09-18 02:10:51] [Iter 312/2250] R1[161/300], Temp: 0.4425, Energy: -64.097262+0.007411j
[2025-09-18 02:11:20] [Iter 313/2250] R1[162/300], Temp: 0.4373, Energy: -64.079548+0.003988j
[2025-09-18 02:11:50] [Iter 314/2250] R1[163/300], Temp: 0.4321, Energy: -64.096092-0.000540j
[2025-09-18 02:12:19] [Iter 315/2250] R1[164/300], Temp: 0.4270, Energy: -64.115720+0.003123j
[2025-09-18 02:12:49] [Iter 316/2250] R1[165/300], Temp: 0.4218, Energy: -64.129281-0.009421j
[2025-09-18 02:13:18] [Iter 317/2250] R1[166/300], Temp: 0.4166, Energy: -64.111036-0.001459j
[2025-09-18 02:13:48] [Iter 318/2250] R1[167/300], Temp: 0.4115, Energy: -64.135314-0.005537j
[2025-09-18 02:14:17] [Iter 319/2250] R1[168/300], Temp: 0.4063, Energy: -64.117017+0.001521j
[2025-09-18 02:14:47] [Iter 320/2250] R1[169/300], Temp: 0.4012, Energy: -64.111979-0.008431j
[2025-09-18 02:15:16] [Iter 321/2250] R1[170/300], Temp: 0.3960, Energy: -64.137539-0.001347j
[2025-09-18 02:15:46] [Iter 322/2250] R1[171/300], Temp: 0.3909, Energy: -64.147524-0.005446j
[2025-09-18 02:16:16] [Iter 323/2250] R1[172/300], Temp: 0.3858, Energy: -64.159892-0.002544j
[2025-09-18 02:16:45] [Iter 324/2250] R1[173/300], Temp: 0.3807, Energy: -64.144354-0.007019j
[2025-09-18 02:17:15] [Iter 325/2250] R1[174/300], Temp: 0.3757, Energy: -64.139306-0.013288j
[2025-09-18 02:17:44] [Iter 326/2250] R1[175/300], Temp: 0.3706, Energy: -64.142866+0.001953j
[2025-09-18 02:18:14] [Iter 327/2250] R1[176/300], Temp: 0.3655, Energy: -64.171827-0.007040j
[2025-09-18 02:18:43] [Iter 328/2250] R1[177/300], Temp: 0.3605, Energy: -64.168646-0.002332j
[2025-09-18 02:19:13] [Iter 329/2250] R1[178/300], Temp: 0.3555, Energy: -64.152794+0.003840j
[2025-09-18 02:19:42] [Iter 330/2250] R1[179/300], Temp: 0.3505, Energy: -64.163373+0.005196j
[2025-09-18 02:20:12] [Iter 331/2250] R1[180/300], Temp: 0.3455, Energy: -64.169761-0.003580j
[2025-09-18 02:20:41] [Iter 332/2250] R1[181/300], Temp: 0.3405, Energy: -64.175233-0.000447j
[2025-09-18 02:21:11] [Iter 333/2250] R1[182/300], Temp: 0.3356, Energy: -64.170245-0.006866j
[2025-09-18 02:21:40] [Iter 334/2250] R1[183/300], Temp: 0.3306, Energy: -64.203062-0.003883j
[2025-09-18 02:22:10] [Iter 335/2250] R1[184/300], Temp: 0.3257, Energy: -64.175023-0.007496j
[2025-09-18 02:22:39] [Iter 336/2250] R1[185/300], Temp: 0.3208, Energy: -64.175076-0.000186j
[2025-09-18 02:23:09] [Iter 337/2250] R1[186/300], Temp: 0.3159, Energy: -64.195666+0.001559j
[2025-09-18 02:23:39] [Iter 338/2250] R1[187/300], Temp: 0.3111, Energy: -64.203826-0.002950j
[2025-09-18 02:24:08] [Iter 339/2250] R1[188/300], Temp: 0.3062, Energy: -64.166957+0.007928j
[2025-09-18 02:24:38] [Iter 340/2250] R1[189/300], Temp: 0.3014, Energy: -64.208142-0.005831j
[2025-09-18 02:25:07] [Iter 341/2250] R1[190/300], Temp: 0.2966, Energy: -64.241136+0.001206j
[2025-09-18 02:25:37] [Iter 342/2250] R1[191/300], Temp: 0.2919, Energy: -64.196030-0.003866j
[2025-09-18 02:26:06] [Iter 343/2250] R1[192/300], Temp: 0.2871, Energy: -64.197358+0.009167j
[2025-09-18 02:26:36] [Iter 344/2250] R1[193/300], Temp: 0.2824, Energy: -64.238410-0.003274j
[2025-09-18 02:27:05] [Iter 345/2250] R1[194/300], Temp: 0.2777, Energy: -64.224858+0.004253j
[2025-09-18 02:27:35] [Iter 346/2250] R1[195/300], Temp: 0.2730, Energy: -64.189742+0.009126j
[2025-09-18 02:28:04] [Iter 347/2250] R1[196/300], Temp: 0.2684, Energy: -64.204364+0.000370j
[2025-09-18 02:28:34] [Iter 348/2250] R1[197/300], Temp: 0.2637, Energy: -64.201038+0.002470j
[2025-09-18 02:29:03] [Iter 349/2250] R1[198/300], Temp: 0.2591, Energy: -64.210126+0.000470j
[2025-09-18 02:29:33] [Iter 350/2250] R1[199/300], Temp: 0.2545, Energy: -64.230174+0.013695j
[2025-09-18 02:30:02] [Iter 351/2250] R1[200/300], Temp: 0.2500, Energy: -64.234418+0.003870j
[2025-09-18 02:30:32] [Iter 352/2250] R1[201/300], Temp: 0.2455, Energy: -64.185213-0.000276j
[2025-09-18 02:31:01] [Iter 353/2250] R1[202/300], Temp: 0.2410, Energy: -64.238157-0.003825j
[2025-09-18 02:31:31] [Iter 354/2250] R1[203/300], Temp: 0.2365, Energy: -64.226278+0.005777j
[2025-09-18 02:32:00] [Iter 355/2250] R1[204/300], Temp: 0.2321, Energy: -64.237672+0.005294j
[2025-09-18 02:32:30] [Iter 356/2250] R1[205/300], Temp: 0.2277, Energy: -64.210061+0.000093j
[2025-09-18 02:32:59] [Iter 357/2250] R1[206/300], Temp: 0.2233, Energy: -64.254422-0.006573j
[2025-09-18 02:33:29] [Iter 358/2250] R1[207/300], Temp: 0.2190, Energy: -64.236230-0.005295j
[2025-09-18 02:33:58] [Iter 359/2250] R1[208/300], Temp: 0.2146, Energy: -64.232544-0.003410j
[2025-09-18 02:34:28] [Iter 360/2250] R1[209/300], Temp: 0.2104, Energy: -64.249711-0.000092j
[2025-09-18 02:34:57] [Iter 361/2250] R1[210/300], Temp: 0.2061, Energy: -64.246111-0.003101j
[2025-09-18 02:35:27] [Iter 362/2250] R1[211/300], Temp: 0.2019, Energy: -64.247418-0.008786j
[2025-09-18 02:35:56] [Iter 363/2250] R1[212/300], Temp: 0.1977, Energy: -64.244924+0.004856j
[2025-09-18 02:36:26] [Iter 364/2250] R1[213/300], Temp: 0.1935, Energy: -64.233764+0.005344j
[2025-09-18 02:36:55] [Iter 365/2250] R1[214/300], Temp: 0.1894, Energy: -64.241978-0.002283j
[2025-09-18 02:37:25] [Iter 366/2250] R1[215/300], Temp: 0.1853, Energy: -64.238984+0.003709j
[2025-09-18 02:37:54] [Iter 367/2250] R1[216/300], Temp: 0.1813, Energy: -64.253881+0.003176j
[2025-09-18 02:38:24] [Iter 368/2250] R1[217/300], Temp: 0.1773, Energy: -64.256124-0.003673j
[2025-09-18 02:38:53] [Iter 369/2250] R1[218/300], Temp: 0.1733, Energy: -64.264024-0.003795j
[2025-09-18 02:39:23] [Iter 370/2250] R1[219/300], Temp: 0.1693, Energy: -64.252361+0.009862j
[2025-09-18 02:39:52] [Iter 371/2250] R1[220/300], Temp: 0.1654, Energy: -64.239427+0.006725j
[2025-09-18 02:40:22] [Iter 372/2250] R1[221/300], Temp: 0.1616, Energy: -64.266811-0.006887j
[2025-09-18 02:40:52] [Iter 373/2250] R1[222/300], Temp: 0.1577, Energy: -64.251805-0.000669j
[2025-09-18 02:41:21] [Iter 374/2250] R1[223/300], Temp: 0.1539, Energy: -64.235738-0.001202j
[2025-09-18 02:41:51] [Iter 375/2250] R1[224/300], Temp: 0.1502, Energy: -64.277640+0.002057j
[2025-09-18 02:42:20] [Iter 376/2250] R1[225/300], Temp: 0.1464, Energy: -64.234679+0.000752j
[2025-09-18 02:42:50] [Iter 377/2250] R1[226/300], Temp: 0.1428, Energy: -64.284609-0.007752j
[2025-09-18 02:43:19] [Iter 378/2250] R1[227/300], Temp: 0.1391, Energy: -64.254093+0.004878j
[2025-09-18 02:43:49] [Iter 379/2250] R1[228/300], Temp: 0.1355, Energy: -64.266396+0.002895j
[2025-09-18 02:44:18] [Iter 380/2250] R1[229/300], Temp: 0.1320, Energy: -64.256211-0.006360j
[2025-09-18 02:44:48] [Iter 381/2250] R1[230/300], Temp: 0.1284, Energy: -64.265759+0.001550j
[2025-09-18 02:45:17] [Iter 382/2250] R1[231/300], Temp: 0.1249, Energy: -64.258313+0.009242j
[2025-09-18 02:45:47] [Iter 383/2250] R1[232/300], Temp: 0.1215, Energy: -64.274524+0.002973j
[2025-09-18 02:46:16] [Iter 384/2250] R1[233/300], Temp: 0.1181, Energy: -64.281463+0.004127j
[2025-09-18 02:46:46] [Iter 385/2250] R1[234/300], Temp: 0.1147, Energy: -64.270842-0.006280j
[2025-09-18 02:47:15] [Iter 386/2250] R1[235/300], Temp: 0.1114, Energy: -64.286461+0.000935j
[2025-09-18 02:47:45] [Iter 387/2250] R1[236/300], Temp: 0.1082, Energy: -64.266426-0.003808j
[2025-09-18 02:48:14] [Iter 388/2250] R1[237/300], Temp: 0.1049, Energy: -64.268768-0.002930j
[2025-09-18 02:48:44] [Iter 389/2250] R1[238/300], Temp: 0.1017, Energy: -64.258678-0.004458j
[2025-09-18 02:49:13] [Iter 390/2250] R1[239/300], Temp: 0.0986, Energy: -64.285910-0.000734j
[2025-09-18 02:49:43] [Iter 391/2250] R1[240/300], Temp: 0.0955, Energy: -64.282703+0.006618j
[2025-09-18 02:50:12] [Iter 392/2250] R1[241/300], Temp: 0.0924, Energy: -64.273188+0.004489j
[2025-09-18 02:50:42] [Iter 393/2250] R1[242/300], Temp: 0.0894, Energy: -64.290987-0.001874j
[2025-09-18 02:51:11] [Iter 394/2250] R1[243/300], Temp: 0.0865, Energy: -64.280600-0.004274j
[2025-09-18 02:51:41] [Iter 395/2250] R1[244/300], Temp: 0.0835, Energy: -64.260150-0.007178j
[2025-09-18 02:52:10] [Iter 396/2250] R1[245/300], Temp: 0.0807, Energy: -64.289445-0.002445j
[2025-09-18 02:52:40] [Iter 397/2250] R1[246/300], Temp: 0.0778, Energy: -64.285576+0.010919j
[2025-09-18 02:53:09] [Iter 398/2250] R1[247/300], Temp: 0.0751, Energy: -64.272378+0.006331j
[2025-09-18 02:53:39] [Iter 399/2250] R1[248/300], Temp: 0.0723, Energy: -64.261222-0.004132j
[2025-09-18 02:54:08] [Iter 400/2250] R1[249/300], Temp: 0.0696, Energy: -64.277384-0.006678j
[2025-09-18 02:54:38] [Iter 401/2250] R1[250/300], Temp: 0.0670, Energy: -64.262264-0.006188j
[2025-09-18 02:55:07] [Iter 402/2250] R1[251/300], Temp: 0.0644, Energy: -64.288243-0.004589j
[2025-09-18 02:55:37] [Iter 403/2250] R1[252/300], Temp: 0.0618, Energy: -64.283318-0.003703j
[2025-09-18 02:56:06] [Iter 404/2250] R1[253/300], Temp: 0.0593, Energy: -64.289153+0.002175j
[2025-09-18 02:56:36] [Iter 405/2250] R1[254/300], Temp: 0.0569, Energy: -64.274646-0.005131j
[2025-09-18 02:57:05] [Iter 406/2250] R1[255/300], Temp: 0.0545, Energy: -64.289798-0.001114j
[2025-09-18 02:57:35] [Iter 407/2250] R1[256/300], Temp: 0.0521, Energy: -64.299395+0.006927j
[2025-09-18 02:58:05] [Iter 408/2250] R1[257/300], Temp: 0.0498, Energy: -64.266897-0.000948j
[2025-09-18 02:58:34] [Iter 409/2250] R1[258/300], Temp: 0.0476, Energy: -64.303398+0.005189j
[2025-09-18 02:59:04] [Iter 410/2250] R1[259/300], Temp: 0.0454, Energy: -64.267240-0.001324j
[2025-09-18 02:59:33] [Iter 411/2250] R1[260/300], Temp: 0.0432, Energy: -64.292381-0.000502j
[2025-09-18 03:00:03] [Iter 412/2250] R1[261/300], Temp: 0.0411, Energy: -64.303083-0.004882j
[2025-09-18 03:00:32] [Iter 413/2250] R1[262/300], Temp: 0.0391, Energy: -64.296816-0.005518j
[2025-09-18 03:01:02] [Iter 414/2250] R1[263/300], Temp: 0.0371, Energy: -64.281239-0.008517j
[2025-09-18 03:01:31] [Iter 415/2250] R1[264/300], Temp: 0.0351, Energy: -64.300264+0.010207j
[2025-09-18 03:02:01] [Iter 416/2250] R1[265/300], Temp: 0.0332, Energy: -64.289010+0.006194j
[2025-09-18 03:02:30] [Iter 417/2250] R1[266/300], Temp: 0.0314, Energy: -64.276063+0.000487j
[2025-09-18 03:03:00] [Iter 418/2250] R1[267/300], Temp: 0.0296, Energy: -64.301703-0.002371j
[2025-09-18 03:03:29] [Iter 419/2250] R1[268/300], Temp: 0.0278, Energy: -64.293733-0.002547j
[2025-09-18 03:03:59] [Iter 420/2250] R1[269/300], Temp: 0.0261, Energy: -64.283794-0.005938j
[2025-09-18 03:04:28] [Iter 421/2250] R1[270/300], Temp: 0.0245, Energy: -64.286037+0.002893j
[2025-09-18 03:04:58] [Iter 422/2250] R1[271/300], Temp: 0.0229, Energy: -64.297149+0.000108j
[2025-09-18 03:05:27] [Iter 423/2250] R1[272/300], Temp: 0.0213, Energy: -64.286128+0.005070j
[2025-09-18 03:05:57] [Iter 424/2250] R1[273/300], Temp: 0.0199, Energy: -64.323919+0.005980j
[2025-09-18 03:06:26] [Iter 425/2250] R1[274/300], Temp: 0.0184, Energy: -64.303255-0.001904j
[2025-09-18 03:06:56] [Iter 426/2250] R1[275/300], Temp: 0.0170, Energy: -64.301558+0.002243j
[2025-09-18 03:07:25] [Iter 427/2250] R1[276/300], Temp: 0.0157, Energy: -64.289093+0.003234j
[2025-09-18 03:07:55] [Iter 428/2250] R1[277/300], Temp: 0.0144, Energy: -64.287112-0.010959j
[2025-09-18 03:08:24] [Iter 429/2250] R1[278/300], Temp: 0.0132, Energy: -64.329725-0.002192j
[2025-09-18 03:08:54] [Iter 430/2250] R1[279/300], Temp: 0.0120, Energy: -64.303792-0.002686j
[2025-09-18 03:09:23] [Iter 431/2250] R1[280/300], Temp: 0.0109, Energy: -64.311678+0.002742j
[2025-09-18 03:09:53] [Iter 432/2250] R1[281/300], Temp: 0.0099, Energy: -64.302435-0.004709j
[2025-09-18 03:10:22] [Iter 433/2250] R1[282/300], Temp: 0.0089, Energy: -64.310435-0.005533j
[2025-09-18 03:10:52] [Iter 434/2250] R1[283/300], Temp: 0.0079, Energy: -64.310284+0.002195j
[2025-09-18 03:11:21] [Iter 435/2250] R1[284/300], Temp: 0.0070, Energy: -64.309985-0.006451j
[2025-09-18 03:11:51] [Iter 436/2250] R1[285/300], Temp: 0.0062, Energy: -64.311369-0.003973j
[2025-09-18 03:12:20] [Iter 437/2250] R1[286/300], Temp: 0.0054, Energy: -64.319330-0.004475j
[2025-09-18 03:12:50] [Iter 438/2250] R1[287/300], Temp: 0.0046, Energy: -64.318501-0.001970j
[2025-09-18 03:13:19] [Iter 439/2250] R1[288/300], Temp: 0.0039, Energy: -64.332177+0.002227j
[2025-09-18 03:13:49] [Iter 440/2250] R1[289/300], Temp: 0.0033, Energy: -64.305997-0.007309j
[2025-09-18 03:14:19] [Iter 441/2250] R1[290/300], Temp: 0.0027, Energy: -64.303402+0.000554j
[2025-09-18 03:14:48] [Iter 442/2250] R1[291/300], Temp: 0.0022, Energy: -64.319506-0.001419j
[2025-09-18 03:15:18] [Iter 443/2250] R1[292/300], Temp: 0.0018, Energy: -64.318545-0.000119j
[2025-09-18 03:15:47] [Iter 444/2250] R1[293/300], Temp: 0.0013, Energy: -64.325555+0.006173j
[2025-09-18 03:16:17] [Iter 445/2250] R1[294/300], Temp: 0.0010, Energy: -64.313427-0.004309j
[2025-09-18 03:16:46] [Iter 446/2250] R1[295/300], Temp: 0.0007, Energy: -64.326981-0.001858j
[2025-09-18 03:17:16] [Iter 447/2250] R1[296/300], Temp: 0.0004, Energy: -64.314958+0.001756j
[2025-09-18 03:17:45] [Iter 448/2250] R1[297/300], Temp: 0.0002, Energy: -64.307810-0.000800j
[2025-09-18 03:18:15] [Iter 449/2250] R1[298/300], Temp: 0.0001, Energy: -64.313474-0.008885j
[2025-09-18 03:18:44] [Iter 450/2250] R1[299/300], Temp: 0.0000, Energy: -64.332767+0.004181j
[2025-09-18 03:18:44] RESTART #2 | Period: 600
[2025-09-18 03:19:14] [Iter 451/2250] R2[0/600], Temp: 1.0000, Energy: -64.311453-0.002002j
[2025-09-18 03:19:43] [Iter 452/2250] R2[1/600], Temp: 1.0000, Energy: -64.335530-0.007346j
[2025-09-18 03:20:13] [Iter 453/2250] R2[2/600], Temp: 1.0000, Energy: -64.318300+0.004152j
[2025-09-18 03:20:43] [Iter 454/2250] R2[3/600], Temp: 0.9999, Energy: -64.341704-0.000795j
[2025-09-18 03:21:12] [Iter 455/2250] R2[4/600], Temp: 0.9999, Energy: -64.328246+0.002714j
[2025-09-18 03:21:42] [Iter 456/2250] R2[5/600], Temp: 0.9998, Energy: -64.319374+0.002627j
[2025-09-18 03:22:11] [Iter 457/2250] R2[6/600], Temp: 0.9998, Energy: -64.315474+0.003145j
[2025-09-18 03:22:41] [Iter 458/2250] R2[7/600], Temp: 0.9997, Energy: -64.313119+0.009192j
[2025-09-18 03:23:10] [Iter 459/2250] R2[8/600], Temp: 0.9996, Energy: -64.318763+0.001540j
[2025-09-18 03:23:40] [Iter 460/2250] R2[9/600], Temp: 0.9994, Energy: -64.290791+0.001219j
[2025-09-18 03:24:09] [Iter 461/2250] R2[10/600], Temp: 0.9993, Energy: -64.352629-0.002648j
[2025-09-18 03:24:39] [Iter 462/2250] R2[11/600], Temp: 0.9992, Energy: -64.329801-0.002705j
[2025-09-18 03:25:08] [Iter 463/2250] R2[12/600], Temp: 0.9990, Energy: -64.311415-0.005987j
[2025-09-18 03:25:38] [Iter 464/2250] R2[13/600], Temp: 0.9988, Energy: -64.344124+0.000798j
[2025-09-18 03:26:07] [Iter 465/2250] R2[14/600], Temp: 0.9987, Energy: -64.319397-0.000991j
[2025-09-18 03:26:37] [Iter 466/2250] R2[15/600], Temp: 0.9985, Energy: -64.315051+0.000699j
[2025-09-18 03:27:06] [Iter 467/2250] R2[16/600], Temp: 0.9982, Energy: -64.311406+0.001895j
[2025-09-18 03:27:36] [Iter 468/2250] R2[17/600], Temp: 0.9980, Energy: -64.321928+0.008804j
[2025-09-18 03:28:05] [Iter 469/2250] R2[18/600], Temp: 0.9978, Energy: -64.325496-0.005741j
[2025-09-18 03:28:35] [Iter 470/2250] R2[19/600], Temp: 0.9975, Energy: -64.334103-0.000413j
[2025-09-18 03:29:04] [Iter 471/2250] R2[20/600], Temp: 0.9973, Energy: -64.319576-0.000150j
[2025-09-18 03:29:34] [Iter 472/2250] R2[21/600], Temp: 0.9970, Energy: -64.346127+0.001280j
[2025-09-18 03:30:03] [Iter 473/2250] R2[22/600], Temp: 0.9967, Energy: -64.341161+0.000390j
[2025-09-18 03:30:33] [Iter 474/2250] R2[23/600], Temp: 0.9964, Energy: -64.343962-0.002278j
[2025-09-18 03:31:02] [Iter 475/2250] R2[24/600], Temp: 0.9961, Energy: -64.310458-0.001665j
[2025-09-18 03:31:32] [Iter 476/2250] R2[25/600], Temp: 0.9957, Energy: -64.326545+0.003546j
[2025-09-18 03:32:01] [Iter 477/2250] R2[26/600], Temp: 0.9954, Energy: -64.319536+0.004397j
[2025-09-18 03:32:31] [Iter 478/2250] R2[27/600], Temp: 0.9950, Energy: -64.330835+0.000056j
[2025-09-18 03:33:00] [Iter 479/2250] R2[28/600], Temp: 0.9946, Energy: -64.310800+0.001610j
[2025-09-18 03:33:30] [Iter 480/2250] R2[29/600], Temp: 0.9942, Energy: -64.324218-0.002253j
[2025-09-18 03:33:59] [Iter 481/2250] R2[30/600], Temp: 0.9938, Energy: -64.315044-0.006320j
[2025-09-18 03:34:29] [Iter 482/2250] R2[31/600], Temp: 0.9934, Energy: -64.315522+0.001044j
[2025-09-18 03:34:58] [Iter 483/2250] R2[32/600], Temp: 0.9930, Energy: -64.317799+0.009889j
[2025-09-18 03:35:28] [Iter 484/2250] R2[33/600], Temp: 0.9926, Energy: -64.328327+0.000968j
[2025-09-18 03:35:57] [Iter 485/2250] R2[34/600], Temp: 0.9921, Energy: -64.330635+0.002602j
[2025-09-18 03:36:27] [Iter 486/2250] R2[35/600], Temp: 0.9916, Energy: -64.332954-0.004394j
[2025-09-18 03:36:56] [Iter 487/2250] R2[36/600], Temp: 0.9911, Energy: -64.336659-0.007408j
[2025-09-18 03:37:26] [Iter 488/2250] R2[37/600], Temp: 0.9906, Energy: -64.318726+0.002548j
[2025-09-18 03:37:55] [Iter 489/2250] R2[38/600], Temp: 0.9901, Energy: -64.338960-0.001661j
[2025-09-18 03:38:25] [Iter 490/2250] R2[39/600], Temp: 0.9896, Energy: -64.331772-0.001828j
[2025-09-18 03:38:54] [Iter 491/2250] R2[40/600], Temp: 0.9891, Energy: -64.347395-0.006725j
[2025-09-18 03:39:24] [Iter 492/2250] R2[41/600], Temp: 0.9885, Energy: -64.321522-0.007719j
[2025-09-18 03:39:53] [Iter 493/2250] R2[42/600], Temp: 0.9880, Energy: -64.346904-0.002859j
[2025-09-18 03:40:23] [Iter 494/2250] R2[43/600], Temp: 0.9874, Energy: -64.335355+0.001075j
[2025-09-18 03:40:52] [Iter 495/2250] R2[44/600], Temp: 0.9868, Energy: -64.342816+0.000115j
[2025-09-18 03:41:22] [Iter 496/2250] R2[45/600], Temp: 0.9862, Energy: -64.353125-0.003890j
[2025-09-18 03:41:52] [Iter 497/2250] R2[46/600], Temp: 0.9856, Energy: -64.342366+0.002540j
[2025-09-18 03:42:21] [Iter 498/2250] R2[47/600], Temp: 0.9849, Energy: -64.330088+0.003659j
[2025-09-18 03:42:51] [Iter 499/2250] R2[48/600], Temp: 0.9843, Energy: -64.335878-0.005477j
[2025-09-18 03:43:20] [Iter 500/2250] R2[49/600], Temp: 0.9836, Energy: -64.351668-0.004430j
[2025-09-18 03:43:20] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-09-18 03:43:50] [Iter 501/2250] R2[50/600], Temp: 0.9830, Energy: -64.324421-0.001726j
[2025-09-18 03:44:19] [Iter 502/2250] R2[51/600], Temp: 0.9823, Energy: -64.335331-0.000251j
[2025-09-18 03:44:49] [Iter 503/2250] R2[52/600], Temp: 0.9816, Energy: -64.333337+0.001720j
[2025-09-18 03:45:18] [Iter 504/2250] R2[53/600], Temp: 0.9809, Energy: -64.346587+0.000322j
[2025-09-18 03:45:48] [Iter 505/2250] R2[54/600], Temp: 0.9801, Energy: -64.346617-0.001088j
[2025-09-18 03:46:17] [Iter 506/2250] R2[55/600], Temp: 0.9794, Energy: -64.357129-0.002041j
[2025-09-18 03:46:47] [Iter 507/2250] R2[56/600], Temp: 0.9787, Energy: -64.333316+0.002822j
[2025-09-18 03:47:16] [Iter 508/2250] R2[57/600], Temp: 0.9779, Energy: -64.332340-0.001072j
[2025-09-18 03:47:46] [Iter 509/2250] R2[58/600], Temp: 0.9771, Energy: -64.333347+0.002023j
[2025-09-18 03:48:15] [Iter 510/2250] R2[59/600], Temp: 0.9763, Energy: -64.347030-0.000755j
[2025-09-18 03:48:45] [Iter 511/2250] R2[60/600], Temp: 0.9755, Energy: -64.351886-0.002175j
[2025-09-18 03:49:14] [Iter 512/2250] R2[61/600], Temp: 0.9747, Energy: -64.359785+0.000875j
[2025-09-18 03:49:44] [Iter 513/2250] R2[62/600], Temp: 0.9739, Energy: -64.337496-0.000832j
[2025-09-18 03:50:13] [Iter 514/2250] R2[63/600], Temp: 0.9730, Energy: -64.351798+0.005838j
[2025-09-18 03:50:43] [Iter 515/2250] R2[64/600], Temp: 0.9722, Energy: -64.343719+0.003116j
[2025-09-18 03:51:12] [Iter 516/2250] R2[65/600], Temp: 0.9713, Energy: -64.321946+0.000582j
[2025-09-18 03:51:42] [Iter 517/2250] R2[66/600], Temp: 0.9704, Energy: -64.334624-0.003960j
[2025-09-18 03:52:11] [Iter 518/2250] R2[67/600], Temp: 0.9695, Energy: -64.381111+0.002582j
[2025-09-18 03:52:41] [Iter 519/2250] R2[68/600], Temp: 0.9686, Energy: -64.349455-0.005092j
[2025-09-18 03:53:10] [Iter 520/2250] R2[69/600], Temp: 0.9677, Energy: -64.341867-0.006547j
[2025-09-18 03:53:40] [Iter 521/2250] R2[70/600], Temp: 0.9668, Energy: -64.334462-0.003390j
[2025-09-18 03:54:09] [Iter 522/2250] R2[71/600], Temp: 0.9658, Energy: -64.334558+0.005556j
[2025-09-18 03:54:39] [Iter 523/2250] R2[72/600], Temp: 0.9649, Energy: -64.344455+0.005073j
[2025-09-18 03:55:08] [Iter 524/2250] R2[73/600], Temp: 0.9639, Energy: -64.367390-0.001067j
[2025-09-18 03:55:38] [Iter 525/2250] R2[74/600], Temp: 0.9629, Energy: -64.348753-0.002473j
[2025-09-18 03:56:07] [Iter 526/2250] R2[75/600], Temp: 0.9619, Energy: -64.363769+0.004473j
[2025-09-18 03:56:37] [Iter 527/2250] R2[76/600], Temp: 0.9609, Energy: -64.333396-0.003374j
[2025-09-18 03:57:06] [Iter 528/2250] R2[77/600], Temp: 0.9599, Energy: -64.336976+0.006799j
[2025-09-18 03:57:36] [Iter 529/2250] R2[78/600], Temp: 0.9589, Energy: -64.363086-0.002607j
[2025-09-18 03:58:05] [Iter 530/2250] R2[79/600], Temp: 0.9578, Energy: -64.363367-0.000145j
[2025-09-18 03:58:35] [Iter 531/2250] R2[80/600], Temp: 0.9568, Energy: -64.360334+0.001588j
[2025-09-18 03:59:04] [Iter 532/2250] R2[81/600], Temp: 0.9557, Energy: -64.333452+0.005466j
[2025-09-18 03:59:34] [Iter 533/2250] R2[82/600], Temp: 0.9546, Energy: -64.340905-0.006424j
[2025-09-18 04:00:03] [Iter 534/2250] R2[83/600], Temp: 0.9535, Energy: -64.345700+0.000806j
[2025-09-18 04:00:33] [Iter 535/2250] R2[84/600], Temp: 0.9524, Energy: -64.358441+0.003692j
[2025-09-18 04:01:02] [Iter 536/2250] R2[85/600], Temp: 0.9513, Energy: -64.358562+0.003534j
[2025-09-18 04:01:32] [Iter 537/2250] R2[86/600], Temp: 0.9502, Energy: -64.358307-0.000489j
[2025-09-18 04:02:02] [Iter 538/2250] R2[87/600], Temp: 0.9490, Energy: -64.365289+0.000227j
[2025-09-18 04:02:31] [Iter 539/2250] R2[88/600], Temp: 0.9479, Energy: -64.361931-0.002798j
[2025-09-18 04:03:01] [Iter 540/2250] R2[89/600], Temp: 0.9467, Energy: -64.350705-0.004162j
[2025-09-18 04:03:30] [Iter 541/2250] R2[90/600], Temp: 0.9455, Energy: -64.357994-0.009585j
[2025-09-18 04:04:00] [Iter 542/2250] R2[91/600], Temp: 0.9443, Energy: -64.345956+0.002477j
[2025-09-18 04:04:29] [Iter 543/2250] R2[92/600], Temp: 0.9431, Energy: -64.359750+0.002112j
[2025-09-18 04:04:59] [Iter 544/2250] R2[93/600], Temp: 0.9419, Energy: -64.348294+0.007391j
[2025-09-18 04:05:28] [Iter 545/2250] R2[94/600], Temp: 0.9407, Energy: -64.342708-0.004360j
[2025-09-18 04:05:57] [Iter 546/2250] R2[95/600], Temp: 0.9394, Energy: -64.345816-0.005301j
[2025-09-18 04:06:27] [Iter 547/2250] R2[96/600], Temp: 0.9382, Energy: -64.349097+0.003273j
[2025-09-18 04:06:57] [Iter 548/2250] R2[97/600], Temp: 0.9369, Energy: -64.363272+0.004137j
[2025-09-18 04:07:26] [Iter 549/2250] R2[98/600], Temp: 0.9356, Energy: -64.370865+0.006254j
[2025-09-18 04:07:55] [Iter 550/2250] R2[99/600], Temp: 0.9343, Energy: -64.339966-0.006651j
[2025-09-18 04:08:25] [Iter 551/2250] R2[100/600], Temp: 0.9330, Energy: -64.358393+0.001133j
[2025-09-18 04:08:54] [Iter 552/2250] R2[101/600], Temp: 0.9317, Energy: -64.361486+0.006681j
[2025-09-18 04:09:24] [Iter 553/2250] R2[102/600], Temp: 0.9304, Energy: -64.353169+0.004364j
[2025-09-18 04:09:53] [Iter 554/2250] R2[103/600], Temp: 0.9290, Energy: -64.348741+0.003645j
[2025-09-18 04:10:23] [Iter 555/2250] R2[104/600], Temp: 0.9277, Energy: -64.359646+0.001021j
[2025-09-18 04:10:52] [Iter 556/2250] R2[105/600], Temp: 0.9263, Energy: -64.337844-0.002670j
[2025-09-18 04:11:22] [Iter 557/2250] R2[106/600], Temp: 0.9249, Energy: -64.345429+0.010742j
[2025-09-18 04:11:52] [Iter 558/2250] R2[107/600], Temp: 0.9236, Energy: -64.350154-0.000552j
[2025-09-18 04:12:21] [Iter 559/2250] R2[108/600], Temp: 0.9222, Energy: -64.372470+0.002508j
[2025-09-18 04:12:51] [Iter 560/2250] R2[109/600], Temp: 0.9208, Energy: -64.330500+0.000666j
[2025-09-18 04:13:20] [Iter 561/2250] R2[110/600], Temp: 0.9193, Energy: -64.363571+0.005897j
[2025-09-18 04:13:50] [Iter 562/2250] R2[111/600], Temp: 0.9179, Energy: -64.332608+0.004054j
[2025-09-18 04:14:19] [Iter 563/2250] R2[112/600], Temp: 0.9165, Energy: -64.368288-0.004795j
[2025-09-18 04:14:49] [Iter 564/2250] R2[113/600], Temp: 0.9150, Energy: -64.356565-0.002294j
[2025-09-18 04:15:18] [Iter 565/2250] R2[114/600], Temp: 0.9135, Energy: -64.356565-0.000957j
[2025-09-18 04:15:48] [Iter 566/2250] R2[115/600], Temp: 0.9121, Energy: -64.347442+0.004017j
[2025-09-18 04:16:17] [Iter 567/2250] R2[116/600], Temp: 0.9106, Energy: -64.344125+0.005543j
[2025-09-18 04:16:47] [Iter 568/2250] R2[117/600], Temp: 0.9091, Energy: -64.351136-0.004293j
[2025-09-18 04:17:16] [Iter 569/2250] R2[118/600], Temp: 0.9076, Energy: -64.375801+0.006200j
[2025-09-18 04:17:46] [Iter 570/2250] R2[119/600], Temp: 0.9060, Energy: -64.362621-0.006429j
[2025-09-18 04:18:15] [Iter 571/2250] R2[120/600], Temp: 0.9045, Energy: -64.376195-0.001210j
[2025-09-18 04:18:45] [Iter 572/2250] R2[121/600], Temp: 0.9030, Energy: -64.355221+0.003016j
[2025-09-18 04:19:14] [Iter 573/2250] R2[122/600], Temp: 0.9014, Energy: -64.374676-0.003292j
[2025-09-18 04:19:44] [Iter 574/2250] R2[123/600], Temp: 0.8998, Energy: -64.366822-0.001407j
[2025-09-18 04:20:13] [Iter 575/2250] R2[124/600], Temp: 0.8983, Energy: -64.356281+0.001555j
[2025-09-18 04:20:43] [Iter 576/2250] R2[125/600], Temp: 0.8967, Energy: -64.371301-0.006059j
[2025-09-18 04:21:12] [Iter 577/2250] R2[126/600], Temp: 0.8951, Energy: -64.380428+0.001432j
[2025-09-18 04:21:42] [Iter 578/2250] R2[127/600], Temp: 0.8935, Energy: -64.390838+0.005236j
[2025-09-18 04:22:11] [Iter 579/2250] R2[128/600], Temp: 0.8918, Energy: -64.362262+0.005232j
[2025-09-18 04:22:41] [Iter 580/2250] R2[129/600], Temp: 0.8902, Energy: -64.366027+0.001377j
[2025-09-18 04:23:10] [Iter 581/2250] R2[130/600], Temp: 0.8886, Energy: -64.367823+0.002815j
[2025-09-18 04:23:40] [Iter 582/2250] R2[131/600], Temp: 0.8869, Energy: -64.364910-0.003103j
[2025-09-18 04:24:09] [Iter 583/2250] R2[132/600], Temp: 0.8853, Energy: -64.397023-0.001107j
[2025-09-18 04:24:39] [Iter 584/2250] R2[133/600], Temp: 0.8836, Energy: -64.368688-0.002133j
[2025-09-18 04:25:08] [Iter 585/2250] R2[134/600], Temp: 0.8819, Energy: -64.366195-0.003235j
[2025-09-18 04:25:38] [Iter 586/2250] R2[135/600], Temp: 0.8802, Energy: -64.351104+0.000256j
[2025-09-18 04:26:07] [Iter 587/2250] R2[136/600], Temp: 0.8785, Energy: -64.368211+0.010723j
[2025-09-18 04:26:37] [Iter 588/2250] R2[137/600], Temp: 0.8768, Energy: -64.351453-0.001099j
[2025-09-18 04:27:07] [Iter 589/2250] R2[138/600], Temp: 0.8751, Energy: -64.374310-0.002723j
[2025-09-18 04:27:36] [Iter 590/2250] R2[139/600], Temp: 0.8733, Energy: -64.348209-0.002079j
[2025-09-18 04:28:06] [Iter 591/2250] R2[140/600], Temp: 0.8716, Energy: -64.363700-0.000398j
[2025-09-18 04:28:35] [Iter 592/2250] R2[141/600], Temp: 0.8698, Energy: -64.358205+0.001643j
[2025-09-18 04:29:05] [Iter 593/2250] R2[142/600], Temp: 0.8680, Energy: -64.366044-0.005879j
[2025-09-18 04:29:34] [Iter 594/2250] R2[143/600], Temp: 0.8663, Energy: -64.374176+0.003287j
[2025-09-18 04:30:04] [Iter 595/2250] R2[144/600], Temp: 0.8645, Energy: -64.344931+0.002962j
[2025-09-18 04:30:33] [Iter 596/2250] R2[145/600], Temp: 0.8627, Energy: -64.375295+0.003553j
[2025-09-18 04:31:03] [Iter 597/2250] R2[146/600], Temp: 0.8609, Energy: -64.376907+0.006457j
[2025-09-18 04:31:32] [Iter 598/2250] R2[147/600], Temp: 0.8591, Energy: -64.365340+0.002373j
[2025-09-18 04:32:02] [Iter 599/2250] R2[148/600], Temp: 0.8572, Energy: -64.377604-0.006494j
[2025-09-18 04:32:31] [Iter 600/2250] R2[149/600], Temp: 0.8554, Energy: -64.380840-0.000590j
[2025-09-18 04:33:01] [Iter 601/2250] R2[150/600], Temp: 0.8536, Energy: -64.370712+0.000981j
[2025-09-18 04:33:30] [Iter 602/2250] R2[151/600], Temp: 0.8517, Energy: -64.394106-0.003338j
[2025-09-18 04:34:00] [Iter 603/2250] R2[152/600], Temp: 0.8498, Energy: -64.381065-0.003563j
[2025-09-18 04:34:29] [Iter 604/2250] R2[153/600], Temp: 0.8480, Energy: -64.383212-0.009990j
[2025-09-18 04:34:59] [Iter 605/2250] R2[154/600], Temp: 0.8461, Energy: -64.370474-0.002914j
[2025-09-18 04:35:28] [Iter 606/2250] R2[155/600], Temp: 0.8442, Energy: -64.393306-0.004742j
[2025-09-18 04:35:58] [Iter 607/2250] R2[156/600], Temp: 0.8423, Energy: -64.375094-0.003162j
[2025-09-18 04:36:27] [Iter 608/2250] R2[157/600], Temp: 0.8404, Energy: -64.381444+0.001849j
[2025-09-18 04:36:57] [Iter 609/2250] R2[158/600], Temp: 0.8384, Energy: -64.390352-0.004370j
[2025-09-18 04:37:26] [Iter 610/2250] R2[159/600], Temp: 0.8365, Energy: -64.379233+0.012152j
[2025-09-18 04:37:56] [Iter 611/2250] R2[160/600], Temp: 0.8346, Energy: -64.385569+0.003484j
[2025-09-18 04:38:25] [Iter 612/2250] R2[161/600], Temp: 0.8326, Energy: -64.381497-0.001971j
[2025-09-18 04:38:55] [Iter 613/2250] R2[162/600], Temp: 0.8307, Energy: -64.368417-0.005656j
[2025-09-18 04:39:24] [Iter 614/2250] R2[163/600], Temp: 0.8287, Energy: -64.405418+0.010788j
[2025-09-18 04:39:54] [Iter 615/2250] R2[164/600], Temp: 0.8267, Energy: -64.360202+0.005196j
[2025-09-18 04:40:23] [Iter 616/2250] R2[165/600], Temp: 0.8247, Energy: -64.374784-0.006694j
[2025-09-18 04:40:53] [Iter 617/2250] R2[166/600], Temp: 0.8227, Energy: -64.380863-0.006274j
[2025-09-18 04:41:22] [Iter 618/2250] R2[167/600], Temp: 0.8207, Energy: -64.382418+0.003255j
[2025-09-18 04:41:52] [Iter 619/2250] R2[168/600], Temp: 0.8187, Energy: -64.370723+0.000799j
[2025-09-18 04:42:21] [Iter 620/2250] R2[169/600], Temp: 0.8167, Energy: -64.377456-0.000229j
[2025-09-18 04:42:51] [Iter 621/2250] R2[170/600], Temp: 0.8147, Energy: -64.387825-0.004439j
[2025-09-18 04:43:20] [Iter 622/2250] R2[171/600], Temp: 0.8126, Energy: -64.383677-0.001268j
[2025-09-18 04:43:50] [Iter 623/2250] R2[172/600], Temp: 0.8106, Energy: -64.389033+0.004835j
[2025-09-18 04:44:19] [Iter 624/2250] R2[173/600], Temp: 0.8085, Energy: -64.372231-0.005692j
[2025-09-18 04:44:49] [Iter 625/2250] R2[174/600], Temp: 0.8065, Energy: -64.373866-0.001631j
[2025-09-18 04:45:18] [Iter 626/2250] R2[175/600], Temp: 0.8044, Energy: -64.380783-0.001767j
[2025-09-18 04:45:48] [Iter 627/2250] R2[176/600], Temp: 0.8023, Energy: -64.397874-0.003330j
[2025-09-18 04:46:17] [Iter 628/2250] R2[177/600], Temp: 0.8002, Energy: -64.386189+0.006778j
[2025-09-18 04:46:47] [Iter 629/2250] R2[178/600], Temp: 0.7981, Energy: -64.380254-0.001165j
[2025-09-18 04:47:16] [Iter 630/2250] R2[179/600], Temp: 0.7960, Energy: -64.372673-0.003288j
[2025-09-18 04:47:46] [Iter 631/2250] R2[180/600], Temp: 0.7939, Energy: -64.395084+0.001510j
[2025-09-18 04:48:15] [Iter 632/2250] R2[181/600], Temp: 0.7918, Energy: -64.382699-0.003323j
[2025-09-18 04:48:45] [Iter 633/2250] R2[182/600], Temp: 0.7896, Energy: -64.375799-0.002740j
[2025-09-18 04:49:14] [Iter 634/2250] R2[183/600], Temp: 0.7875, Energy: -64.382978-0.002338j
[2025-09-18 04:49:44] [Iter 635/2250] R2[184/600], Temp: 0.7854, Energy: -64.380057+0.000395j
[2025-09-18 04:50:13] [Iter 636/2250] R2[185/600], Temp: 0.7832, Energy: -64.373893+0.004681j
[2025-09-18 04:50:43] [Iter 637/2250] R2[186/600], Temp: 0.7810, Energy: -64.359159-0.000614j
[2025-09-18 04:51:12] [Iter 638/2250] R2[187/600], Temp: 0.7789, Energy: -64.375528-0.006040j
[2025-09-18 04:51:42] [Iter 639/2250] R2[188/600], Temp: 0.7767, Energy: -64.387741+0.010829j
[2025-09-18 04:52:11] [Iter 640/2250] R2[189/600], Temp: 0.7745, Energy: -64.391611+0.000412j
[2025-09-18 04:52:41] [Iter 641/2250] R2[190/600], Temp: 0.7723, Energy: -64.375633+0.006094j
[2025-09-18 04:53:10] [Iter 642/2250] R2[191/600], Temp: 0.7701, Energy: -64.390164+0.000353j
[2025-09-18 04:53:40] [Iter 643/2250] R2[192/600], Temp: 0.7679, Energy: -64.397281+0.004773j
[2025-09-18 04:54:09] [Iter 644/2250] R2[193/600], Temp: 0.7657, Energy: -64.379370-0.000674j
[2025-09-18 04:54:39] [Iter 645/2250] R2[194/600], Temp: 0.7635, Energy: -64.388572-0.000044j
[2025-09-18 04:55:08] [Iter 646/2250] R2[195/600], Temp: 0.7612, Energy: -64.398953+0.007844j
[2025-09-18 04:55:38] [Iter 647/2250] R2[196/600], Temp: 0.7590, Energy: -64.397092+0.005194j
[2025-09-18 04:56:07] [Iter 648/2250] R2[197/600], Temp: 0.7568, Energy: -64.411705-0.002681j
[2025-09-18 04:56:37] [Iter 649/2250] R2[198/600], Temp: 0.7545, Energy: -64.370657-0.002246j
[2025-09-18 04:57:06] [Iter 650/2250] R2[199/600], Temp: 0.7523, Energy: -64.378306-0.003178j
[2025-09-18 04:57:36] [Iter 651/2250] R2[200/600], Temp: 0.7500, Energy: -64.374455-0.001033j
[2025-09-18 04:58:05] [Iter 652/2250] R2[201/600], Temp: 0.7477, Energy: -64.390238+0.001067j
[2025-09-18 04:58:35] [Iter 653/2250] R2[202/600], Temp: 0.7455, Energy: -64.376883+0.003197j
[2025-09-18 04:59:04] [Iter 654/2250] R2[203/600], Temp: 0.7432, Energy: -64.398136+0.002667j
[2025-09-18 04:59:34] [Iter 655/2250] R2[204/600], Temp: 0.7409, Energy: -64.391592-0.012015j
[2025-09-18 05:00:03] [Iter 656/2250] R2[205/600], Temp: 0.7386, Energy: -64.402512-0.001620j
[2025-09-18 05:00:33] [Iter 657/2250] R2[206/600], Temp: 0.7363, Energy: -64.392451+0.003772j
[2025-09-18 05:01:02] [Iter 658/2250] R2[207/600], Temp: 0.7340, Energy: -64.360633-0.003213j
[2025-09-18 05:01:32] [Iter 659/2250] R2[208/600], Temp: 0.7316, Energy: -64.382279-0.001871j
[2025-09-18 05:02:01] [Iter 660/2250] R2[209/600], Temp: 0.7293, Energy: -64.380031+0.003286j
[2025-09-18 05:02:31] [Iter 661/2250] R2[210/600], Temp: 0.7270, Energy: -64.411865+0.005888j
[2025-09-18 05:03:00] [Iter 662/2250] R2[211/600], Temp: 0.7247, Energy: -64.400930-0.001139j
[2025-09-18 05:03:30] [Iter 663/2250] R2[212/600], Temp: 0.7223, Energy: -64.394832+0.002714j
[2025-09-18 05:03:59] [Iter 664/2250] R2[213/600], Temp: 0.7200, Energy: -64.402413+0.007508j
[2025-09-18 05:04:29] [Iter 665/2250] R2[214/600], Temp: 0.7176, Energy: -64.397190+0.001171j
[2025-09-18 05:04:58] [Iter 666/2250] R2[215/600], Temp: 0.7153, Energy: -64.378291-0.000462j
[2025-09-18 05:05:28] [Iter 667/2250] R2[216/600], Temp: 0.7129, Energy: -64.382852+0.003186j
[2025-09-18 05:05:57] [Iter 668/2250] R2[217/600], Temp: 0.7105, Energy: -64.398661+0.001913j
[2025-09-18 05:06:27] [Iter 669/2250] R2[218/600], Temp: 0.7081, Energy: -64.385976-0.005541j
[2025-09-18 05:06:56] [Iter 670/2250] R2[219/600], Temp: 0.7058, Energy: -64.397631-0.008666j
[2025-09-18 05:07:26] [Iter 671/2250] R2[220/600], Temp: 0.7034, Energy: -64.378530+0.005104j
[2025-09-18 05:07:55] [Iter 672/2250] R2[221/600], Temp: 0.7010, Energy: -64.394325-0.002844j
[2025-09-18 05:08:25] [Iter 673/2250] R2[222/600], Temp: 0.6986, Energy: -64.387485+0.004736j
[2025-09-18 05:08:54] [Iter 674/2250] R2[223/600], Temp: 0.6962, Energy: -64.374569-0.005672j
[2025-09-18 05:09:24] [Iter 675/2250] R2[224/600], Temp: 0.6938, Energy: -64.388321+0.001187j
[2025-09-18 05:09:53] [Iter 676/2250] R2[225/600], Temp: 0.6913, Energy: -64.381518+0.003744j
[2025-09-18 05:10:23] [Iter 677/2250] R2[226/600], Temp: 0.6889, Energy: -64.385747+0.000431j
[2025-09-18 05:10:53] [Iter 678/2250] R2[227/600], Temp: 0.6865, Energy: -64.411546-0.001952j
[2025-09-18 05:11:22] [Iter 679/2250] R2[228/600], Temp: 0.6841, Energy: -64.406584+0.005047j
[2025-09-18 05:11:52] [Iter 680/2250] R2[229/600], Temp: 0.6816, Energy: -64.394763+0.001748j
[2025-09-18 05:12:21] [Iter 681/2250] R2[230/600], Temp: 0.6792, Energy: -64.384842-0.005216j
[2025-09-18 05:12:51] [Iter 682/2250] R2[231/600], Temp: 0.6767, Energy: -64.385465+0.006247j
[2025-09-18 05:13:20] [Iter 683/2250] R2[232/600], Temp: 0.6743, Energy: -64.374641+0.002681j
[2025-09-18 05:13:50] [Iter 684/2250] R2[233/600], Temp: 0.6718, Energy: -64.378850+0.005440j
[2025-09-18 05:14:19] [Iter 685/2250] R2[234/600], Temp: 0.6694, Energy: -64.393429+0.006480j
[2025-09-18 05:14:49] [Iter 686/2250] R2[235/600], Temp: 0.6669, Energy: -64.402851-0.001003j
[2025-09-18 05:15:18] [Iter 687/2250] R2[236/600], Temp: 0.6644, Energy: -64.395160-0.003071j
[2025-09-18 05:15:48] [Iter 688/2250] R2[237/600], Temp: 0.6620, Energy: -64.385068-0.005614j
[2025-09-18 05:16:17] [Iter 689/2250] R2[238/600], Temp: 0.6595, Energy: -64.392615+0.004197j
[2025-09-18 05:16:47] [Iter 690/2250] R2[239/600], Temp: 0.6570, Energy: -64.395190-0.000500j
[2025-09-18 05:17:16] [Iter 691/2250] R2[240/600], Temp: 0.6545, Energy: -64.404225+0.003426j
[2025-09-18 05:17:46] [Iter 692/2250] R2[241/600], Temp: 0.6520, Energy: -64.399575-0.000761j
[2025-09-18 05:18:15] [Iter 693/2250] R2[242/600], Temp: 0.6495, Energy: -64.396091-0.003343j
[2025-09-18 05:18:45] [Iter 694/2250] R2[243/600], Temp: 0.6470, Energy: -64.390430-0.005452j
[2025-09-18 05:19:14] [Iter 695/2250] R2[244/600], Temp: 0.6445, Energy: -64.372576+0.000814j
[2025-09-18 05:19:44] [Iter 696/2250] R2[245/600], Temp: 0.6420, Energy: -64.396149-0.003739j
[2025-09-18 05:20:13] [Iter 697/2250] R2[246/600], Temp: 0.6395, Energy: -64.377642+0.004743j
[2025-09-18 05:20:43] [Iter 698/2250] R2[247/600], Temp: 0.6370, Energy: -64.388305-0.001165j
[2025-09-18 05:21:12] [Iter 699/2250] R2[248/600], Temp: 0.6345, Energy: -64.398241+0.001306j
[2025-09-18 05:21:42] [Iter 700/2250] R2[249/600], Temp: 0.6319, Energy: -64.407524-0.004858j
[2025-09-18 05:22:11] [Iter 701/2250] R2[250/600], Temp: 0.6294, Energy: -64.389021+0.003390j
[2025-09-18 05:22:41] [Iter 702/2250] R2[251/600], Temp: 0.6269, Energy: -64.412197+0.004566j
[2025-09-18 05:23:10] [Iter 703/2250] R2[252/600], Temp: 0.6243, Energy: -64.415399-0.005558j
[2025-09-18 05:23:40] [Iter 704/2250] R2[253/600], Temp: 0.6218, Energy: -64.420063+0.001285j
[2025-09-18 05:24:09] [Iter 705/2250] R2[254/600], Temp: 0.6193, Energy: -64.391438+0.000972j
[2025-09-18 05:24:39] [Iter 706/2250] R2[255/600], Temp: 0.6167, Energy: -64.382209+0.006756j
[2025-09-18 05:25:08] [Iter 707/2250] R2[256/600], Temp: 0.6142, Energy: -64.400153-0.002043j
[2025-09-18 05:25:38] [Iter 708/2250] R2[257/600], Temp: 0.6116, Energy: -64.401165-0.001876j
[2025-09-18 05:26:07] [Iter 709/2250] R2[258/600], Temp: 0.6091, Energy: -64.388553+0.001818j
[2025-09-18 05:26:37] [Iter 710/2250] R2[259/600], Temp: 0.6065, Energy: -64.409150+0.000178j
[2025-09-18 05:27:06] [Iter 711/2250] R2[260/600], Temp: 0.6040, Energy: -64.415372-0.001183j
[2025-09-18 05:27:36] [Iter 712/2250] R2[261/600], Temp: 0.6014, Energy: -64.405499-0.005977j
[2025-09-18 05:28:05] [Iter 713/2250] R2[262/600], Temp: 0.5988, Energy: -64.410418-0.005126j
[2025-09-18 05:28:35] [Iter 714/2250] R2[263/600], Temp: 0.5963, Energy: -64.382456+0.003583j
[2025-09-18 05:29:04] [Iter 715/2250] R2[264/600], Temp: 0.5937, Energy: -64.409344-0.002885j
[2025-09-18 05:29:34] [Iter 716/2250] R2[265/600], Temp: 0.5911, Energy: -64.406739-0.003705j
[2025-09-18 05:30:03] [Iter 717/2250] R2[266/600], Temp: 0.5885, Energy: -64.411238+0.000625j
[2025-09-18 05:30:33] [Iter 718/2250] R2[267/600], Temp: 0.5860, Energy: -64.386318-0.003634j
[2025-09-18 05:31:02] [Iter 719/2250] R2[268/600], Temp: 0.5834, Energy: -64.395946+0.002463j
[2025-09-18 05:31:32] [Iter 720/2250] R2[269/600], Temp: 0.5808, Energy: -64.417311-0.002076j
[2025-09-18 05:32:01] [Iter 721/2250] R2[270/600], Temp: 0.5782, Energy: -64.425839+0.003076j
[2025-09-18 05:32:31] [Iter 722/2250] R2[271/600], Temp: 0.5756, Energy: -64.406041+0.005222j
[2025-09-18 05:33:00] [Iter 723/2250] R2[272/600], Temp: 0.5730, Energy: -64.394366-0.002120j
[2025-09-18 05:33:30] [Iter 724/2250] R2[273/600], Temp: 0.5705, Energy: -64.419641-0.001568j
[2025-09-18 05:33:59] [Iter 725/2250] R2[274/600], Temp: 0.5679, Energy: -64.391179-0.004891j
[2025-09-18 05:34:29] [Iter 726/2250] R2[275/600], Temp: 0.5653, Energy: -64.388164+0.002866j
[2025-09-18 05:34:58] [Iter 727/2250] R2[276/600], Temp: 0.5627, Energy: -64.397905-0.000388j
[2025-09-18 05:35:28] [Iter 728/2250] R2[277/600], Temp: 0.5601, Energy: -64.375968-0.001424j
[2025-09-18 05:35:57] [Iter 729/2250] R2[278/600], Temp: 0.5575, Energy: -64.382310+0.001727j
[2025-09-18 05:36:27] [Iter 730/2250] R2[279/600], Temp: 0.5549, Energy: -64.399971+0.003959j
[2025-09-18 05:36:56] [Iter 731/2250] R2[280/600], Temp: 0.5523, Energy: -64.394184+0.002520j
[2025-09-18 05:37:26] [Iter 732/2250] R2[281/600], Temp: 0.5497, Energy: -64.385791+0.005367j
[2025-09-18 05:37:55] [Iter 733/2250] R2[282/600], Temp: 0.5471, Energy: -64.377398-0.001150j
[2025-09-18 05:38:25] [Iter 734/2250] R2[283/600], Temp: 0.5444, Energy: -64.407417+0.000672j
[2025-09-18 05:38:54] [Iter 735/2250] R2[284/600], Temp: 0.5418, Energy: -64.404848-0.005649j
[2025-09-18 05:39:24] [Iter 736/2250] R2[285/600], Temp: 0.5392, Energy: -64.400460+0.000003j
[2025-09-18 05:39:53] [Iter 737/2250] R2[286/600], Temp: 0.5366, Energy: -64.403700+0.004487j
[2025-09-18 05:40:23] [Iter 738/2250] R2[287/600], Temp: 0.5340, Energy: -64.412194-0.000780j
[2025-09-18 05:40:52] [Iter 739/2250] R2[288/600], Temp: 0.5314, Energy: -64.424460+0.003178j
[2025-09-18 05:41:22] [Iter 740/2250] R2[289/600], Temp: 0.5288, Energy: -64.396251-0.007738j
[2025-09-18 05:41:51] [Iter 741/2250] R2[290/600], Temp: 0.5262, Energy: -64.405205-0.002410j
[2025-09-18 05:42:21] [Iter 742/2250] R2[291/600], Temp: 0.5236, Energy: -64.394486+0.003681j
[2025-09-18 05:42:50] [Iter 743/2250] R2[292/600], Temp: 0.5209, Energy: -64.407640-0.000894j
[2025-09-18 05:43:20] [Iter 744/2250] R2[293/600], Temp: 0.5183, Energy: -64.427003-0.006033j
[2025-09-18 05:43:49] [Iter 745/2250] R2[294/600], Temp: 0.5157, Energy: -64.399721+0.001795j
[2025-09-18 05:44:19] [Iter 746/2250] R2[295/600], Temp: 0.5131, Energy: -64.409133-0.008792j
[2025-09-18 05:44:48] [Iter 747/2250] R2[296/600], Temp: 0.5105, Energy: -64.415071+0.000182j
[2025-09-18 05:45:18] [Iter 748/2250] R2[297/600], Temp: 0.5079, Energy: -64.404058-0.000116j
[2025-09-18 05:45:47] [Iter 749/2250] R2[298/600], Temp: 0.5052, Energy: -64.414240+0.002404j
[2025-09-18 05:46:17] [Iter 750/2250] R2[299/600], Temp: 0.5026, Energy: -64.413938+0.003206j
[2025-09-18 05:46:17] ✓ Checkpoint saved: checkpoint_iter_000750.pkl
[2025-09-18 05:46:46] [Iter 751/2250] R2[300/600], Temp: 0.5000, Energy: -64.408750+0.000133j
[2025-09-18 05:47:16] [Iter 752/2250] R2[301/600], Temp: 0.4974, Energy: -64.391674+0.002298j
[2025-09-18 05:47:45] [Iter 753/2250] R2[302/600], Temp: 0.4948, Energy: -64.392135-0.005137j
[2025-09-18 05:48:15] [Iter 754/2250] R2[303/600], Temp: 0.4921, Energy: -64.414494+0.005699j
[2025-09-18 05:48:44] [Iter 755/2250] R2[304/600], Temp: 0.4895, Energy: -64.418558+0.001511j
[2025-09-18 05:49:14] [Iter 756/2250] R2[305/600], Temp: 0.4869, Energy: -64.393174+0.002735j
[2025-09-18 05:49:43] [Iter 757/2250] R2[306/600], Temp: 0.4843, Energy: -64.423865+0.000415j
[2025-09-18 05:50:13] [Iter 758/2250] R2[307/600], Temp: 0.4817, Energy: -64.415896+0.003404j
[2025-09-18 05:50:42] [Iter 759/2250] R2[308/600], Temp: 0.4791, Energy: -64.414179+0.001025j
[2025-09-18 05:51:12] [Iter 760/2250] R2[309/600], Temp: 0.4764, Energy: -64.405803+0.004773j
[2025-09-18 05:51:41] [Iter 761/2250] R2[310/600], Temp: 0.4738, Energy: -64.408515+0.002322j
[2025-09-18 05:52:11] [Iter 762/2250] R2[311/600], Temp: 0.4712, Energy: -64.386730+0.000175j
[2025-09-18 05:52:40] [Iter 763/2250] R2[312/600], Temp: 0.4686, Energy: -64.407158-0.000588j
[2025-09-18 05:53:10] [Iter 764/2250] R2[313/600], Temp: 0.4660, Energy: -64.419661-0.000355j
[2025-09-18 05:53:39] [Iter 765/2250] R2[314/600], Temp: 0.4634, Energy: -64.401725+0.001950j
[2025-09-18 05:54:09] [Iter 766/2250] R2[315/600], Temp: 0.4608, Energy: -64.402727-0.000147j
[2025-09-18 05:54:38] [Iter 767/2250] R2[316/600], Temp: 0.4582, Energy: -64.406595+0.001191j
[2025-09-18 05:55:08] [Iter 768/2250] R2[317/600], Temp: 0.4556, Energy: -64.396246-0.005653j
[2025-09-18 05:55:38] [Iter 769/2250] R2[318/600], Temp: 0.4529, Energy: -64.385529+0.000083j
[2025-09-18 05:56:07] [Iter 770/2250] R2[319/600], Temp: 0.4503, Energy: -64.412745-0.006353j
[2025-09-18 05:56:37] [Iter 771/2250] R2[320/600], Temp: 0.4477, Energy: -64.410044-0.002335j
[2025-09-18 05:57:06] [Iter 772/2250] R2[321/600], Temp: 0.4451, Energy: -64.391123+0.001592j
[2025-09-18 05:57:36] [Iter 773/2250] R2[322/600], Temp: 0.4425, Energy: -64.428558+0.001041j
[2025-09-18 05:58:05] [Iter 774/2250] R2[323/600], Temp: 0.4399, Energy: -64.425173+0.004170j
[2025-09-18 05:58:35] [Iter 775/2250] R2[324/600], Temp: 0.4373, Energy: -64.426795+0.003011j
[2025-09-18 05:59:04] [Iter 776/2250] R2[325/600], Temp: 0.4347, Energy: -64.423947+0.000390j
[2025-09-18 05:59:34] [Iter 777/2250] R2[326/600], Temp: 0.4321, Energy: -64.427436-0.003841j
[2025-09-18 06:00:03] [Iter 778/2250] R2[327/600], Temp: 0.4295, Energy: -64.425513+0.008617j
[2025-09-18 06:00:33] [Iter 779/2250] R2[328/600], Temp: 0.4270, Energy: -64.410773-0.003563j
[2025-09-18 06:01:02] [Iter 780/2250] R2[329/600], Temp: 0.4244, Energy: -64.413798-0.002738j
[2025-09-18 06:01:32] [Iter 781/2250] R2[330/600], Temp: 0.4218, Energy: -64.420960-0.004014j
[2025-09-18 06:02:01] [Iter 782/2250] R2[331/600], Temp: 0.4192, Energy: -64.405424+0.000856j
[2025-09-18 06:02:31] [Iter 783/2250] R2[332/600], Temp: 0.4166, Energy: -64.414555+0.004478j
[2025-09-18 06:03:00] [Iter 784/2250] R2[333/600], Temp: 0.4140, Energy: -64.427681-0.001258j
[2025-09-18 06:03:30] [Iter 785/2250] R2[334/600], Temp: 0.4115, Energy: -64.399124+0.004659j
[2025-09-18 06:03:59] [Iter 786/2250] R2[335/600], Temp: 0.4089, Energy: -64.432853+0.001623j
[2025-09-18 06:04:29] [Iter 787/2250] R2[336/600], Temp: 0.4063, Energy: -64.417279+0.004863j
[2025-09-18 06:04:58] [Iter 788/2250] R2[337/600], Temp: 0.4037, Energy: -64.410985-0.000482j
[2025-09-18 06:05:28] [Iter 789/2250] R2[338/600], Temp: 0.4012, Energy: -64.422098+0.007621j
[2025-09-18 06:05:57] [Iter 790/2250] R2[339/600], Temp: 0.3986, Energy: -64.402616+0.000031j
[2025-09-18 06:06:27] [Iter 791/2250] R2[340/600], Temp: 0.3960, Energy: -64.430208+0.005728j
[2025-09-18 06:06:56] [Iter 792/2250] R2[341/600], Temp: 0.3935, Energy: -64.420053-0.001586j
[2025-09-18 06:07:26] [Iter 793/2250] R2[342/600], Temp: 0.3909, Energy: -64.417925-0.001018j
[2025-09-18 06:07:55] [Iter 794/2250] R2[343/600], Temp: 0.3884, Energy: -64.407767-0.003911j
[2025-09-18 06:08:25] [Iter 795/2250] R2[344/600], Temp: 0.3858, Energy: -64.413148-0.002092j
[2025-09-18 06:08:54] [Iter 796/2250] R2[345/600], Temp: 0.3833, Energy: -64.427846+0.002908j
[2025-09-18 06:09:24] [Iter 797/2250] R2[346/600], Temp: 0.3807, Energy: -64.422967-0.004071j
[2025-09-18 06:09:53] [Iter 798/2250] R2[347/600], Temp: 0.3782, Energy: -64.406149-0.002153j
[2025-09-18 06:10:23] [Iter 799/2250] R2[348/600], Temp: 0.3757, Energy: -64.412053-0.003224j
[2025-09-18 06:10:52] [Iter 800/2250] R2[349/600], Temp: 0.3731, Energy: -64.427916-0.000564j
[2025-09-18 06:11:22] [Iter 801/2250] R2[350/600], Temp: 0.3706, Energy: -64.430315+0.001585j
[2025-09-18 06:11:51] [Iter 802/2250] R2[351/600], Temp: 0.3681, Energy: -64.384839-0.000788j
[2025-09-18 06:12:21] [Iter 803/2250] R2[352/600], Temp: 0.3655, Energy: -64.408580-0.000751j
[2025-09-18 06:12:50] [Iter 804/2250] R2[353/600], Temp: 0.3630, Energy: -64.407640+0.002018j
[2025-09-18 06:13:20] [Iter 805/2250] R2[354/600], Temp: 0.3605, Energy: -64.424504-0.002350j
[2025-09-18 06:13:49] [Iter 806/2250] R2[355/600], Temp: 0.3580, Energy: -64.411690-0.001778j
[2025-09-18 06:14:19] [Iter 807/2250] R2[356/600], Temp: 0.3555, Energy: -64.427793-0.000749j
[2025-09-18 06:14:48] [Iter 808/2250] R2[357/600], Temp: 0.3530, Energy: -64.430392+0.001735j
[2025-09-18 06:15:18] [Iter 809/2250] R2[358/600], Temp: 0.3505, Energy: -64.406500-0.001264j
[2025-09-18 06:15:47] [Iter 810/2250] R2[359/600], Temp: 0.3480, Energy: -64.435192-0.003442j
[2025-09-18 06:16:17] [Iter 811/2250] R2[360/600], Temp: 0.3455, Energy: -64.433575-0.002550j
[2025-09-18 06:16:46] [Iter 812/2250] R2[361/600], Temp: 0.3430, Energy: -64.432191-0.000122j
[2025-09-18 06:17:16] [Iter 813/2250] R2[362/600], Temp: 0.3405, Energy: -64.426954-0.002196j
[2025-09-18 06:17:45] [Iter 814/2250] R2[363/600], Temp: 0.3380, Energy: -64.427466+0.001423j
[2025-09-18 06:18:15] [Iter 815/2250] R2[364/600], Temp: 0.3356, Energy: -64.412958+0.000406j
[2025-09-18 06:18:44] [Iter 816/2250] R2[365/600], Temp: 0.3331, Energy: -64.439065-0.005769j
[2025-09-18 06:19:14] [Iter 817/2250] R2[366/600], Temp: 0.3306, Energy: -64.423451-0.004040j
[2025-09-18 06:19:43] [Iter 818/2250] R2[367/600], Temp: 0.3282, Energy: -64.415083-0.000712j
[2025-09-18 06:20:13] [Iter 819/2250] R2[368/600], Temp: 0.3257, Energy: -64.437168-0.004514j
[2025-09-18 06:20:42] [Iter 820/2250] R2[369/600], Temp: 0.3233, Energy: -64.429242+0.002954j
[2025-09-18 06:21:12] [Iter 821/2250] R2[370/600], Temp: 0.3208, Energy: -64.425014+0.001423j
[2025-09-18 06:21:41] [Iter 822/2250] R2[371/600], Temp: 0.3184, Energy: -64.446778+0.005531j
[2025-09-18 06:22:11] [Iter 823/2250] R2[372/600], Temp: 0.3159, Energy: -64.427919-0.000611j
[2025-09-18 06:22:40] [Iter 824/2250] R2[373/600], Temp: 0.3135, Energy: -64.439432+0.001169j
[2025-09-18 06:23:10] [Iter 825/2250] R2[374/600], Temp: 0.3111, Energy: -64.407802+0.000289j
[2025-09-18 06:23:39] [Iter 826/2250] R2[375/600], Temp: 0.3087, Energy: -64.431279-0.003379j
[2025-09-18 06:24:09] [Iter 827/2250] R2[376/600], Temp: 0.3062, Energy: -64.430873-0.005668j
[2025-09-18 06:24:38] [Iter 828/2250] R2[377/600], Temp: 0.3038, Energy: -64.428582+0.004895j
[2025-09-18 06:25:08] [Iter 829/2250] R2[378/600], Temp: 0.3014, Energy: -64.421122+0.004394j
[2025-09-18 06:25:37] [Iter 830/2250] R2[379/600], Temp: 0.2990, Energy: -64.430665+0.002062j
[2025-09-18 06:26:07] [Iter 831/2250] R2[380/600], Temp: 0.2966, Energy: -64.420037+0.003430j
[2025-09-18 06:26:36] [Iter 832/2250] R2[381/600], Temp: 0.2942, Energy: -64.414348+0.008359j
[2025-09-18 06:27:06] [Iter 833/2250] R2[382/600], Temp: 0.2919, Energy: -64.427774+0.002741j
[2025-09-18 06:27:35] [Iter 834/2250] R2[383/600], Temp: 0.2895, Energy: -64.420782+0.005844j
[2025-09-18 06:28:05] [Iter 835/2250] R2[384/600], Temp: 0.2871, Energy: -64.412332-0.005431j
[2025-09-18 06:28:35] [Iter 836/2250] R2[385/600], Temp: 0.2847, Energy: -64.429547+0.002169j
[2025-09-18 06:29:04] [Iter 837/2250] R2[386/600], Temp: 0.2824, Energy: -64.413615+0.002683j
[2025-09-18 06:29:33] [Iter 838/2250] R2[387/600], Temp: 0.2800, Energy: -64.428677+0.002522j
[2025-09-18 06:30:03] [Iter 839/2250] R2[388/600], Temp: 0.2777, Energy: -64.414837+0.004799j
[2025-09-18 06:30:32] [Iter 840/2250] R2[389/600], Temp: 0.2753, Energy: -64.422845+0.005527j
[2025-09-18 06:31:02] [Iter 841/2250] R2[390/600], Temp: 0.2730, Energy: -64.394311+0.001487j
[2025-09-18 06:31:31] [Iter 842/2250] R2[391/600], Temp: 0.2707, Energy: -64.396909+0.004607j
[2025-09-18 06:32:01] [Iter 843/2250] R2[392/600], Temp: 0.2684, Energy: -64.417046-0.000921j
[2025-09-18 06:32:30] [Iter 844/2250] R2[393/600], Temp: 0.2660, Energy: -64.408623-0.001426j
[2025-09-18 06:33:00] [Iter 845/2250] R2[394/600], Temp: 0.2637, Energy: -64.443002+0.003949j
[2025-09-18 06:33:29] [Iter 846/2250] R2[395/600], Temp: 0.2614, Energy: -64.441012+0.004405j
[2025-09-18 06:33:59] [Iter 847/2250] R2[396/600], Temp: 0.2591, Energy: -64.415157-0.002641j
[2025-09-18 06:34:28] [Iter 848/2250] R2[397/600], Temp: 0.2568, Energy: -64.436067-0.001845j
[2025-09-18 06:34:58] [Iter 849/2250] R2[398/600], Temp: 0.2545, Energy: -64.410701-0.001828j
[2025-09-18 06:35:27] [Iter 850/2250] R2[399/600], Temp: 0.2523, Energy: -64.419928+0.001982j
[2025-09-18 06:35:57] [Iter 851/2250] R2[400/600], Temp: 0.2500, Energy: -64.429322+0.001801j
[2025-09-18 06:36:26] [Iter 852/2250] R2[401/600], Temp: 0.2477, Energy: -64.435339-0.008246j
[2025-09-18 06:36:56] [Iter 853/2250] R2[402/600], Temp: 0.2455, Energy: -64.433427+0.002438j
[2025-09-18 06:37:25] [Iter 854/2250] R2[403/600], Temp: 0.2432, Energy: -64.417638+0.002313j
[2025-09-18 06:37:55] [Iter 855/2250] R2[404/600], Temp: 0.2410, Energy: -64.428562+0.009466j
[2025-09-18 06:38:24] [Iter 856/2250] R2[405/600], Temp: 0.2388, Energy: -64.442311+0.005649j
[2025-09-18 06:38:54] [Iter 857/2250] R2[406/600], Temp: 0.2365, Energy: -64.423032-0.002784j
[2025-09-18 06:39:23] [Iter 858/2250] R2[407/600], Temp: 0.2343, Energy: -64.439873+0.001990j
[2025-09-18 06:39:53] [Iter 859/2250] R2[408/600], Temp: 0.2321, Energy: -64.429245+0.001490j
[2025-09-18 06:40:22] [Iter 860/2250] R2[409/600], Temp: 0.2299, Energy: -64.431762+0.003662j
[2025-09-18 06:40:52] [Iter 861/2250] R2[410/600], Temp: 0.2277, Energy: -64.457450-0.003413j
[2025-09-18 06:41:21] [Iter 862/2250] R2[411/600], Temp: 0.2255, Energy: -64.437795+0.001943j
[2025-09-18 06:41:51] [Iter 863/2250] R2[412/600], Temp: 0.2233, Energy: -64.425192+0.000711j
[2025-09-18 06:42:20] [Iter 864/2250] R2[413/600], Temp: 0.2211, Energy: -64.432921-0.003564j
[2025-09-18 06:42:50] [Iter 865/2250] R2[414/600], Temp: 0.2190, Energy: -64.419772-0.003512j
[2025-09-18 06:43:19] [Iter 866/2250] R2[415/600], Temp: 0.2168, Energy: -64.437398-0.002173j
[2025-09-18 06:43:49] [Iter 867/2250] R2[416/600], Temp: 0.2146, Energy: -64.431175-0.005271j
[2025-09-18 06:44:18] [Iter 868/2250] R2[417/600], Temp: 0.2125, Energy: -64.428622-0.002435j
[2025-09-18 06:44:48] [Iter 869/2250] R2[418/600], Temp: 0.2104, Energy: -64.449852-0.000829j
[2025-09-18 06:45:17] [Iter 870/2250] R2[419/600], Temp: 0.2082, Energy: -64.417275-0.010387j
[2025-09-18 06:45:46] [Iter 871/2250] R2[420/600], Temp: 0.2061, Energy: -64.442483+0.002614j
[2025-09-18 06:46:16] [Iter 872/2250] R2[421/600], Temp: 0.2040, Energy: -64.436313-0.001272j
[2025-09-18 06:46:46] [Iter 873/2250] R2[422/600], Temp: 0.2019, Energy: -64.431510-0.000092j
[2025-09-18 06:47:15] [Iter 874/2250] R2[423/600], Temp: 0.1998, Energy: -64.429313-0.001364j
[2025-09-18 06:47:45] [Iter 875/2250] R2[424/600], Temp: 0.1977, Energy: -64.415150+0.004395j
[2025-09-18 06:48:14] [Iter 876/2250] R2[425/600], Temp: 0.1956, Energy: -64.406754-0.007263j
[2025-09-18 06:48:43] [Iter 877/2250] R2[426/600], Temp: 0.1935, Energy: -64.439227-0.000761j
[2025-09-18 06:49:13] [Iter 878/2250] R2[427/600], Temp: 0.1915, Energy: -64.421170+0.003441j
[2025-09-18 06:49:42] [Iter 879/2250] R2[428/600], Temp: 0.1894, Energy: -64.433888+0.001379j
[2025-09-18 06:50:12] [Iter 880/2250] R2[429/600], Temp: 0.1874, Energy: -64.410117+0.000971j
[2025-09-18 06:50:41] [Iter 881/2250] R2[430/600], Temp: 0.1853, Energy: -64.418656-0.000058j
[2025-09-18 06:51:11] [Iter 882/2250] R2[431/600], Temp: 0.1833, Energy: -64.416708+0.004838j
[2025-09-18 06:51:40] [Iter 883/2250] R2[432/600], Temp: 0.1813, Energy: -64.423739+0.005798j
[2025-09-18 06:52:10] [Iter 884/2250] R2[433/600], Temp: 0.1793, Energy: -64.437573+0.005087j
[2025-09-18 06:52:39] [Iter 885/2250] R2[434/600], Temp: 0.1773, Energy: -64.438141-0.001358j
[2025-09-18 06:53:09] [Iter 886/2250] R2[435/600], Temp: 0.1753, Energy: -64.434632+0.005506j
[2025-09-18 06:53:38] [Iter 887/2250] R2[436/600], Temp: 0.1733, Energy: -64.437223+0.002565j
[2025-09-18 06:54:08] [Iter 888/2250] R2[437/600], Temp: 0.1713, Energy: -64.430813+0.005504j
[2025-09-18 06:54:37] [Iter 889/2250] R2[438/600], Temp: 0.1693, Energy: -64.424258+0.002800j
[2025-09-18 06:55:07] [Iter 890/2250] R2[439/600], Temp: 0.1674, Energy: -64.457806+0.000078j
[2025-09-18 06:55:36] [Iter 891/2250] R2[440/600], Temp: 0.1654, Energy: -64.423078+0.001546j
[2025-09-18 06:56:06] [Iter 892/2250] R2[441/600], Temp: 0.1635, Energy: -64.436358+0.003912j
[2025-09-18 06:56:35] [Iter 893/2250] R2[442/600], Temp: 0.1616, Energy: -64.427404+0.002762j
[2025-09-18 06:57:05] [Iter 894/2250] R2[443/600], Temp: 0.1596, Energy: -64.431228-0.003912j
[2025-09-18 06:57:34] [Iter 895/2250] R2[444/600], Temp: 0.1577, Energy: -64.441741-0.003558j
[2025-09-18 06:58:04] [Iter 896/2250] R2[445/600], Temp: 0.1558, Energy: -64.435982+0.006753j
[2025-09-18 06:58:33] [Iter 897/2250] R2[446/600], Temp: 0.1539, Energy: -64.426492+0.001813j
[2025-09-18 06:59:03] [Iter 898/2250] R2[447/600], Temp: 0.1520, Energy: -64.431236+0.010016j
[2025-09-18 06:59:32] [Iter 899/2250] R2[448/600], Temp: 0.1502, Energy: -64.427142+0.003320j
[2025-09-18 07:00:02] [Iter 900/2250] R2[449/600], Temp: 0.1483, Energy: -64.436844+0.000320j
[2025-09-18 07:00:31] [Iter 901/2250] R2[450/600], Temp: 0.1464, Energy: -64.425804+0.002981j
[2025-09-18 07:01:01] [Iter 902/2250] R2[451/600], Temp: 0.1446, Energy: -64.444671-0.009991j
[2025-09-18 07:01:30] [Iter 903/2250] R2[452/600], Temp: 0.1428, Energy: -64.437269+0.001387j
[2025-09-18 07:02:00] [Iter 904/2250] R2[453/600], Temp: 0.1409, Energy: -64.462744+0.003200j
[2025-09-18 07:02:29] [Iter 905/2250] R2[454/600], Temp: 0.1391, Energy: -64.443035-0.001619j
[2025-09-18 07:02:59] [Iter 906/2250] R2[455/600], Temp: 0.1373, Energy: -64.424497-0.007213j
[2025-09-18 07:03:28] [Iter 907/2250] R2[456/600], Temp: 0.1355, Energy: -64.424873+0.002032j
[2025-09-18 07:03:58] [Iter 908/2250] R2[457/600], Temp: 0.1337, Energy: -64.443778-0.001470j
[2025-09-18 07:04:27] [Iter 909/2250] R2[458/600], Temp: 0.1320, Energy: -64.449954+0.004634j
[2025-09-18 07:04:57] [Iter 910/2250] R2[459/600], Temp: 0.1302, Energy: -64.434186+0.001077j
[2025-09-18 07:05:26] [Iter 911/2250] R2[460/600], Temp: 0.1284, Energy: -64.434663+0.002072j
[2025-09-18 07:05:56] [Iter 912/2250] R2[461/600], Temp: 0.1267, Energy: -64.412209+0.001686j
[2025-09-18 07:06:25] [Iter 913/2250] R2[462/600], Temp: 0.1249, Energy: -64.416445-0.001463j
[2025-09-18 07:06:55] [Iter 914/2250] R2[463/600], Temp: 0.1232, Energy: -64.444822-0.001018j
[2025-09-18 07:07:24] [Iter 915/2250] R2[464/600], Temp: 0.1215, Energy: -64.446512+0.003010j
[2025-09-18 07:07:54] [Iter 916/2250] R2[465/600], Temp: 0.1198, Energy: -64.441509+0.007399j
[2025-09-18 07:08:23] [Iter 917/2250] R2[466/600], Temp: 0.1181, Energy: -64.448891-0.002316j
[2025-09-18 07:08:53] [Iter 918/2250] R2[467/600], Temp: 0.1164, Energy: -64.438110-0.001255j
[2025-09-18 07:09:22] [Iter 919/2250] R2[468/600], Temp: 0.1147, Energy: -64.447756-0.001730j
[2025-09-18 07:09:52] [Iter 920/2250] R2[469/600], Temp: 0.1131, Energy: -64.443794-0.000246j
[2025-09-18 07:10:21] [Iter 921/2250] R2[470/600], Temp: 0.1114, Energy: -64.434021-0.003127j
[2025-09-18 07:10:51] [Iter 922/2250] R2[471/600], Temp: 0.1098, Energy: -64.416903+0.005477j
[2025-09-18 07:11:20] [Iter 923/2250] R2[472/600], Temp: 0.1082, Energy: -64.422220+0.001060j
[2025-09-18 07:11:50] [Iter 924/2250] R2[473/600], Temp: 0.1065, Energy: -64.431569+0.000809j
[2025-09-18 07:12:19] [Iter 925/2250] R2[474/600], Temp: 0.1049, Energy: -64.449693+0.001721j
[2025-09-18 07:12:49] [Iter 926/2250] R2[475/600], Temp: 0.1033, Energy: -64.441765+0.000093j
[2025-09-18 07:13:18] [Iter 927/2250] R2[476/600], Temp: 0.1017, Energy: -64.428531-0.000006j
[2025-09-18 07:13:48] [Iter 928/2250] R2[477/600], Temp: 0.1002, Energy: -64.434388+0.002855j
[2025-09-18 07:14:17] [Iter 929/2250] R2[478/600], Temp: 0.0986, Energy: -64.428162-0.005667j
[2025-09-18 07:14:47] [Iter 930/2250] R2[479/600], Temp: 0.0970, Energy: -64.435136-0.001894j
[2025-09-18 07:15:16] [Iter 931/2250] R2[480/600], Temp: 0.0955, Energy: -64.446743+0.004104j
[2025-09-18 07:15:45] [Iter 932/2250] R2[481/600], Temp: 0.0940, Energy: -64.433758-0.003635j
[2025-09-18 07:16:15] [Iter 933/2250] R2[482/600], Temp: 0.0924, Energy: -64.437974+0.004337j
[2025-09-18 07:16:45] [Iter 934/2250] R2[483/600], Temp: 0.0909, Energy: -64.443328+0.001991j
[2025-09-18 07:17:14] [Iter 935/2250] R2[484/600], Temp: 0.0894, Energy: -64.451570-0.002571j
[2025-09-18 07:17:43] [Iter 936/2250] R2[485/600], Temp: 0.0879, Energy: -64.440347+0.000340j
[2025-09-18 07:18:13] [Iter 937/2250] R2[486/600], Temp: 0.0865, Energy: -64.453515+0.002491j
[2025-09-18 07:18:43] [Iter 938/2250] R2[487/600], Temp: 0.0850, Energy: -64.443982+0.002967j
[2025-09-18 07:19:12] [Iter 939/2250] R2[488/600], Temp: 0.0835, Energy: -64.434877+0.000703j
[2025-09-18 07:19:42] [Iter 940/2250] R2[489/600], Temp: 0.0821, Energy: -64.451362-0.003133j
[2025-09-18 07:20:11] [Iter 941/2250] R2[490/600], Temp: 0.0807, Energy: -64.432086+0.009915j
[2025-09-18 07:20:41] [Iter 942/2250] R2[491/600], Temp: 0.0792, Energy: -64.452503-0.003739j
[2025-09-18 07:21:10] [Iter 943/2250] R2[492/600], Temp: 0.0778, Energy: -64.439981+0.001786j
[2025-09-18 07:21:40] [Iter 944/2250] R2[493/600], Temp: 0.0764, Energy: -64.427808-0.002328j
[2025-09-18 07:22:09] [Iter 945/2250] R2[494/600], Temp: 0.0751, Energy: -64.440733+0.002073j
[2025-09-18 07:22:39] [Iter 946/2250] R2[495/600], Temp: 0.0737, Energy: -64.421712+0.002238j
[2025-09-18 07:23:08] [Iter 947/2250] R2[496/600], Temp: 0.0723, Energy: -64.425267-0.001896j
[2025-09-18 07:23:38] [Iter 948/2250] R2[497/600], Temp: 0.0710, Energy: -64.452216+0.000625j
[2025-09-18 07:24:07] [Iter 949/2250] R2[498/600], Temp: 0.0696, Energy: -64.448401-0.008428j
[2025-09-18 07:24:37] [Iter 950/2250] R2[499/600], Temp: 0.0683, Energy: -64.456639+0.000935j
[2025-09-18 07:25:06] [Iter 951/2250] R2[500/600], Temp: 0.0670, Energy: -64.459808-0.002327j
[2025-09-18 07:25:36] [Iter 952/2250] R2[501/600], Temp: 0.0657, Energy: -64.453353+0.004140j
[2025-09-18 07:26:05] [Iter 953/2250] R2[502/600], Temp: 0.0644, Energy: -64.440465+0.000348j
[2025-09-18 07:26:35] [Iter 954/2250] R2[503/600], Temp: 0.0631, Energy: -64.462618-0.001471j
[2025-09-18 07:27:04] [Iter 955/2250] R2[504/600], Temp: 0.0618, Energy: -64.430222-0.007617j
[2025-09-18 07:27:34] [Iter 956/2250] R2[505/600], Temp: 0.0606, Energy: -64.449929+0.008388j
[2025-09-18 07:28:03] [Iter 957/2250] R2[506/600], Temp: 0.0593, Energy: -64.451159-0.003618j
[2025-09-18 07:28:33] [Iter 958/2250] R2[507/600], Temp: 0.0581, Energy: -64.447358+0.000993j
[2025-09-18 07:29:02] [Iter 959/2250] R2[508/600], Temp: 0.0569, Energy: -64.448661-0.000446j
[2025-09-18 07:29:32] [Iter 960/2250] R2[509/600], Temp: 0.0557, Energy: -64.453011-0.000445j
[2025-09-18 07:30:01] [Iter 961/2250] R2[510/600], Temp: 0.0545, Energy: -64.435632-0.005899j
[2025-09-18 07:30:31] [Iter 962/2250] R2[511/600], Temp: 0.0533, Energy: -64.443265+0.004293j
[2025-09-18 07:31:00] [Iter 963/2250] R2[512/600], Temp: 0.0521, Energy: -64.451436-0.003236j
[2025-09-18 07:31:30] [Iter 964/2250] R2[513/600], Temp: 0.0510, Energy: -64.448435-0.000168j
[2025-09-18 07:31:59] [Iter 965/2250] R2[514/600], Temp: 0.0498, Energy: -64.434394+0.003295j
[2025-09-18 07:32:29] [Iter 966/2250] R2[515/600], Temp: 0.0487, Energy: -64.451316-0.000911j
[2025-09-18 07:32:58] [Iter 967/2250] R2[516/600], Temp: 0.0476, Energy: -64.447849+0.002807j
[2025-09-18 07:33:28] [Iter 968/2250] R2[517/600], Temp: 0.0465, Energy: -64.449355+0.003244j
[2025-09-18 07:33:57] [Iter 969/2250] R2[518/600], Temp: 0.0454, Energy: -64.437041-0.002388j
[2025-09-18 07:34:27] [Iter 970/2250] R2[519/600], Temp: 0.0443, Energy: -64.437217-0.007184j
[2025-09-18 07:34:56] [Iter 971/2250] R2[520/600], Temp: 0.0432, Energy: -64.428178+0.000954j
[2025-09-18 07:35:26] [Iter 972/2250] R2[521/600], Temp: 0.0422, Energy: -64.459562+0.002697j
[2025-09-18 07:35:55] [Iter 973/2250] R2[522/600], Temp: 0.0411, Energy: -64.451029+0.004407j
[2025-09-18 07:36:24] [Iter 974/2250] R2[523/600], Temp: 0.0401, Energy: -64.475778+0.001914j
[2025-09-18 07:36:54] [Iter 975/2250] R2[524/600], Temp: 0.0391, Energy: -64.452294+0.001515j
[2025-09-18 07:37:23] [Iter 976/2250] R2[525/600], Temp: 0.0381, Energy: -64.441548-0.004594j
[2025-09-18 07:37:53] [Iter 977/2250] R2[526/600], Temp: 0.0371, Energy: -64.456700+0.003390j
[2025-09-18 07:38:22] [Iter 978/2250] R2[527/600], Temp: 0.0361, Energy: -64.443452-0.001780j
[2025-09-18 07:38:52] [Iter 979/2250] R2[528/600], Temp: 0.0351, Energy: -64.451412+0.003085j
[2025-09-18 07:39:21] [Iter 980/2250] R2[529/600], Temp: 0.0342, Energy: -64.466884+0.000973j
[2025-09-18 07:39:51] [Iter 981/2250] R2[530/600], Temp: 0.0332, Energy: -64.452758-0.001282j
[2025-09-18 07:40:20] [Iter 982/2250] R2[531/600], Temp: 0.0323, Energy: -64.442325-0.000966j
[2025-09-18 07:40:50] [Iter 983/2250] R2[532/600], Temp: 0.0314, Energy: -64.444051-0.007938j
[2025-09-18 07:41:19] [Iter 984/2250] R2[533/600], Temp: 0.0305, Energy: -64.449777-0.000924j
[2025-09-18 07:41:49] [Iter 985/2250] R2[534/600], Temp: 0.0296, Energy: -64.444250-0.000395j
[2025-09-18 07:42:18] [Iter 986/2250] R2[535/600], Temp: 0.0287, Energy: -64.434215+0.007284j
[2025-09-18 07:42:48] [Iter 987/2250] R2[536/600], Temp: 0.0278, Energy: -64.448514-0.002296j
[2025-09-18 07:43:17] [Iter 988/2250] R2[537/600], Temp: 0.0270, Energy: -64.441351-0.002383j
[2025-09-18 07:43:47] [Iter 989/2250] R2[538/600], Temp: 0.0261, Energy: -64.439171-0.003775j
[2025-09-18 07:44:16] [Iter 990/2250] R2[539/600], Temp: 0.0253, Energy: -64.445455-0.001185j
[2025-09-18 07:44:46] [Iter 991/2250] R2[540/600], Temp: 0.0245, Energy: -64.451212-0.000525j
[2025-09-18 07:45:16] [Iter 992/2250] R2[541/600], Temp: 0.0237, Energy: -64.449524+0.007375j
[2025-09-18 07:45:45] [Iter 993/2250] R2[542/600], Temp: 0.0229, Energy: -64.454268-0.001379j
[2025-09-18 07:46:15] [Iter 994/2250] R2[543/600], Temp: 0.0221, Energy: -64.437993-0.002933j
[2025-09-18 07:46:44] [Iter 995/2250] R2[544/600], Temp: 0.0213, Energy: -64.442945+0.002713j
[2025-09-18 07:47:13] [Iter 996/2250] R2[545/600], Temp: 0.0206, Energy: -64.453743-0.000288j
[2025-09-18 07:47:43] [Iter 997/2250] R2[546/600], Temp: 0.0199, Energy: -64.466881+0.002862j
[2025-09-18 07:48:12] [Iter 998/2250] R2[547/600], Temp: 0.0191, Energy: -64.464352-0.005155j
[2025-09-18 07:48:42] [Iter 999/2250] R2[548/600], Temp: 0.0184, Energy: -64.461881-0.001545j
[2025-09-18 07:49:11] [Iter 1000/2250] R2[549/600], Temp: 0.0177, Energy: -64.470974-0.003466j
[2025-09-18 07:49:11] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-09-18 07:49:41] [Iter 1001/2250] R2[550/600], Temp: 0.0170, Energy: -64.456012-0.001609j
[2025-09-18 07:50:10] [Iter 1002/2250] R2[551/600], Temp: 0.0164, Energy: -64.459238+0.003360j
[2025-09-18 07:50:40] [Iter 1003/2250] R2[552/600], Temp: 0.0157, Energy: -64.449481-0.002763j
[2025-09-18 07:51:09] [Iter 1004/2250] R2[553/600], Temp: 0.0151, Energy: -64.424247+0.000603j
[2025-09-18 07:51:39] [Iter 1005/2250] R2[554/600], Temp: 0.0144, Energy: -64.449407+0.001767j
[2025-09-18 07:52:08] [Iter 1006/2250] R2[555/600], Temp: 0.0138, Energy: -64.454579-0.001708j
[2025-09-18 07:52:38] [Iter 1007/2250] R2[556/600], Temp: 0.0132, Energy: -64.442368+0.000501j
[2025-09-18 07:53:07] [Iter 1008/2250] R2[557/600], Temp: 0.0126, Energy: -64.453525+0.002251j
[2025-09-18 07:53:37] [Iter 1009/2250] R2[558/600], Temp: 0.0120, Energy: -64.452519-0.007158j
[2025-09-18 07:54:06] [Iter 1010/2250] R2[559/600], Temp: 0.0115, Energy: -64.452487-0.007321j
[2025-09-18 07:54:36] [Iter 1011/2250] R2[560/600], Temp: 0.0109, Energy: -64.436514-0.000231j
[2025-09-18 07:55:05] [Iter 1012/2250] R2[561/600], Temp: 0.0104, Energy: -64.425554+0.009000j
[2025-09-18 07:55:35] [Iter 1013/2250] R2[562/600], Temp: 0.0099, Energy: -64.451595+0.002657j
[2025-09-18 07:56:04] [Iter 1014/2250] R2[563/600], Temp: 0.0094, Energy: -64.458868+0.010001j
[2025-09-18 07:56:34] [Iter 1015/2250] R2[564/600], Temp: 0.0089, Energy: -64.456784-0.002706j
[2025-09-18 07:57:03] [Iter 1016/2250] R2[565/600], Temp: 0.0084, Energy: -64.441320+0.005586j
[2025-09-18 07:57:33] [Iter 1017/2250] R2[566/600], Temp: 0.0079, Energy: -64.447928+0.006196j
[2025-09-18 07:58:02] [Iter 1018/2250] R2[567/600], Temp: 0.0074, Energy: -64.456347+0.003780j
[2025-09-18 07:58:32] [Iter 1019/2250] R2[568/600], Temp: 0.0070, Energy: -64.456859-0.001052j
[2025-09-18 07:59:01] [Iter 1020/2250] R2[569/600], Temp: 0.0066, Energy: -64.465425+0.006707j
[2025-09-18 07:59:31] [Iter 1021/2250] R2[570/600], Temp: 0.0062, Energy: -64.453423+0.001946j
[2025-09-18 08:00:00] [Iter 1022/2250] R2[571/600], Temp: 0.0058, Energy: -64.460569+0.000248j
[2025-09-18 08:00:30] [Iter 1023/2250] R2[572/600], Temp: 0.0054, Energy: -64.455465-0.001697j
[2025-09-18 08:00:59] [Iter 1024/2250] R2[573/600], Temp: 0.0050, Energy: -64.457451+0.003630j
[2025-09-18 08:01:29] [Iter 1025/2250] R2[574/600], Temp: 0.0046, Energy: -64.456427+0.002212j
[2025-09-18 08:01:58] [Iter 1026/2250] R2[575/600], Temp: 0.0043, Energy: -64.456185+0.001171j
[2025-09-18 08:02:28] [Iter 1027/2250] R2[576/600], Temp: 0.0039, Energy: -64.451838+0.003570j
[2025-09-18 08:02:57] [Iter 1028/2250] R2[577/600], Temp: 0.0036, Energy: -64.455165+0.005167j
[2025-09-18 08:03:27] [Iter 1029/2250] R2[578/600], Temp: 0.0033, Energy: -64.464951-0.003762j
[2025-09-18 08:03:56] [Iter 1030/2250] R2[579/600], Temp: 0.0030, Energy: -64.448250+0.000384j
[2025-09-18 08:04:26] [Iter 1031/2250] R2[580/600], Temp: 0.0027, Energy: -64.426051+0.011603j
[2025-09-18 08:04:55] [Iter 1032/2250] R2[581/600], Temp: 0.0025, Energy: -64.441653-0.001019j
[2025-09-18 08:05:25] [Iter 1033/2250] R2[582/600], Temp: 0.0022, Energy: -64.442233-0.000967j
[2025-09-18 08:05:54] [Iter 1034/2250] R2[583/600], Temp: 0.0020, Energy: -64.463180+0.001220j
[2025-09-18 08:06:24] [Iter 1035/2250] R2[584/600], Temp: 0.0018, Energy: -64.454730-0.001964j
[2025-09-18 08:06:53] [Iter 1036/2250] R2[585/600], Temp: 0.0015, Energy: -64.456697+0.001437j
[2025-09-18 08:07:23] [Iter 1037/2250] R2[586/600], Temp: 0.0013, Energy: -64.444262-0.002194j
[2025-09-18 08:07:52] [Iter 1038/2250] R2[587/600], Temp: 0.0012, Energy: -64.462099-0.000190j
[2025-09-18 08:08:22] [Iter 1039/2250] R2[588/600], Temp: 0.0010, Energy: -64.473392+0.000428j
[2025-09-18 08:08:51] [Iter 1040/2250] R2[589/600], Temp: 0.0008, Energy: -64.458921-0.001004j
[2025-09-18 08:09:21] [Iter 1041/2250] R2[590/600], Temp: 0.0007, Energy: -64.472153+0.005532j
[2025-09-18 08:09:50] [Iter 1042/2250] R2[591/600], Temp: 0.0006, Energy: -64.453352+0.002456j
[2025-09-18 08:10:20] [Iter 1043/2250] R2[592/600], Temp: 0.0004, Energy: -64.447398+0.003380j
[2025-09-18 08:10:49] [Iter 1044/2250] R2[593/600], Temp: 0.0003, Energy: -64.455178+0.002428j
[2025-09-18 08:11:19] [Iter 1045/2250] R2[594/600], Temp: 0.0002, Energy: -64.471346-0.000116j
[2025-09-18 08:11:48] [Iter 1046/2250] R2[595/600], Temp: 0.0002, Energy: -64.450484-0.005155j
[2025-09-18 08:12:17] [Iter 1047/2250] R2[596/600], Temp: 0.0001, Energy: -64.454498-0.004230j
[2025-09-18 08:12:47] [Iter 1048/2250] R2[597/600], Temp: 0.0001, Energy: -64.456907-0.000533j
[2025-09-18 08:13:16] [Iter 1049/2250] R2[598/600], Temp: 0.0000, Energy: -64.464806-0.000056j
[2025-09-18 08:13:46] [Iter 1050/2250] R2[599/600], Temp: 0.0000, Energy: -64.456526+0.002531j
[2025-09-18 08:13:46] RESTART #3 | Period: 1200
[2025-09-18 08:14:15] [Iter 1051/2250] R3[0/1200], Temp: 1.0000, Energy: -64.453693+0.001831j
[2025-09-18 08:14:45] [Iter 1052/2250] R3[1/1200], Temp: 1.0000, Energy: -64.464745+0.002317j
[2025-09-18 08:15:14] [Iter 1053/2250] R3[2/1200], Temp: 1.0000, Energy: -64.456099-0.000537j
[2025-09-18 08:15:44] [Iter 1054/2250] R3[3/1200], Temp: 1.0000, Energy: -64.453728+0.004291j
[2025-09-18 08:16:13] [Iter 1055/2250] R3[4/1200], Temp: 1.0000, Energy: -64.463953-0.000229j
[2025-09-18 08:16:43] [Iter 1056/2250] R3[5/1200], Temp: 1.0000, Energy: -64.465989-0.006740j
[2025-09-18 08:17:12] [Iter 1057/2250] R3[6/1200], Temp: 0.9999, Energy: -64.452338+0.006193j
[2025-09-18 08:17:42] [Iter 1058/2250] R3[7/1200], Temp: 0.9999, Energy: -64.476113+0.004075j
[2025-09-18 08:18:11] [Iter 1059/2250] R3[8/1200], Temp: 0.9999, Energy: -64.461551-0.006999j
[2025-09-18 08:18:41] [Iter 1060/2250] R3[9/1200], Temp: 0.9999, Energy: -64.472651+0.005852j
[2025-09-18 08:19:10] [Iter 1061/2250] R3[10/1200], Temp: 0.9998, Energy: -64.469240+0.004089j
[2025-09-18 08:19:40] [Iter 1062/2250] R3[11/1200], Temp: 0.9998, Energy: -64.460224-0.003002j
[2025-09-18 08:20:09] [Iter 1063/2250] R3[12/1200], Temp: 0.9998, Energy: -64.455601-0.001701j
[2025-09-18 08:20:39] [Iter 1064/2250] R3[13/1200], Temp: 0.9997, Energy: -64.460218+0.002351j
[2025-09-18 08:21:08] [Iter 1065/2250] R3[14/1200], Temp: 0.9997, Energy: -64.471276-0.000886j
[2025-09-18 08:21:38] [Iter 1066/2250] R3[15/1200], Temp: 0.9996, Energy: -64.447225+0.004723j
[2025-09-18 08:22:07] [Iter 1067/2250] R3[16/1200], Temp: 0.9996, Energy: -64.470777+0.003336j
[2025-09-18 08:22:37] [Iter 1068/2250] R3[17/1200], Temp: 0.9995, Energy: -64.472397-0.003338j
[2025-09-18 08:23:06] [Iter 1069/2250] R3[18/1200], Temp: 0.9994, Energy: -64.462761+0.004667j
[2025-09-18 08:23:36] [Iter 1070/2250] R3[19/1200], Temp: 0.9994, Energy: -64.468434-0.001007j
[2025-09-18 08:24:05] [Iter 1071/2250] R3[20/1200], Temp: 0.9993, Energy: -64.462527+0.003304j
[2025-09-18 08:24:35] [Iter 1072/2250] R3[21/1200], Temp: 0.9992, Energy: -64.449254+0.000421j
[2025-09-18 08:25:04] [Iter 1073/2250] R3[22/1200], Temp: 0.9992, Energy: -64.454538+0.000439j
[2025-09-18 08:25:34] [Iter 1074/2250] R3[23/1200], Temp: 0.9991, Energy: -64.449757+0.002299j
[2025-09-18 08:26:03] [Iter 1075/2250] R3[24/1200], Temp: 0.9990, Energy: -64.476913-0.000608j
[2025-09-18 08:26:33] [Iter 1076/2250] R3[25/1200], Temp: 0.9989, Energy: -64.448914+0.003403j
[2025-09-18 08:27:02] [Iter 1077/2250] R3[26/1200], Temp: 0.9988, Energy: -64.467427+0.000472j
[2025-09-18 08:27:32] [Iter 1078/2250] R3[27/1200], Temp: 0.9988, Energy: -64.465006+0.000846j
[2025-09-18 08:28:01] [Iter 1079/2250] R3[28/1200], Temp: 0.9987, Energy: -64.444898+0.003504j
[2025-09-18 08:28:31] [Iter 1080/2250] R3[29/1200], Temp: 0.9986, Energy: -64.471921-0.001729j
[2025-09-18 08:29:00] [Iter 1081/2250] R3[30/1200], Temp: 0.9985, Energy: -64.453339-0.002035j
[2025-09-18 08:29:30] [Iter 1082/2250] R3[31/1200], Temp: 0.9984, Energy: -64.439971-0.000191j
[2025-09-18 08:29:59] [Iter 1083/2250] R3[32/1200], Temp: 0.9982, Energy: -64.444308+0.005579j
[2025-09-18 08:30:29] [Iter 1084/2250] R3[33/1200], Temp: 0.9981, Energy: -64.452871-0.002372j
[2025-09-18 08:30:58] [Iter 1085/2250] R3[34/1200], Temp: 0.9980, Energy: -64.448371-0.002832j
[2025-09-18 08:31:28] [Iter 1086/2250] R3[35/1200], Temp: 0.9979, Energy: -64.437713+0.000453j
[2025-09-18 08:31:57] [Iter 1087/2250] R3[36/1200], Temp: 0.9978, Energy: -64.440754-0.004536j
[2025-09-18 08:32:27] [Iter 1088/2250] R3[37/1200], Temp: 0.9977, Energy: -64.430037-0.004134j
[2025-09-18 08:32:56] [Iter 1089/2250] R3[38/1200], Temp: 0.9975, Energy: -64.452097+0.003886j
[2025-09-18 08:33:25] [Iter 1090/2250] R3[39/1200], Temp: 0.9974, Energy: -64.455646+0.002472j
[2025-09-18 08:33:55] [Iter 1091/2250] R3[40/1200], Temp: 0.9973, Energy: -64.460478-0.005689j
[2025-09-18 08:34:24] [Iter 1092/2250] R3[41/1200], Temp: 0.9971, Energy: -64.465807+0.002511j
[2025-09-18 08:34:54] [Iter 1093/2250] R3[42/1200], Temp: 0.9970, Energy: -64.454447-0.000973j
[2025-09-18 08:35:23] [Iter 1094/2250] R3[43/1200], Temp: 0.9968, Energy: -64.460945-0.000001j
[2025-09-18 08:35:53] [Iter 1095/2250] R3[44/1200], Temp: 0.9967, Energy: -64.467104-0.002926j
[2025-09-18 08:36:22] [Iter 1096/2250] R3[45/1200], Temp: 0.9965, Energy: -64.462059+0.000673j
[2025-09-18 08:36:52] [Iter 1097/2250] R3[46/1200], Temp: 0.9964, Energy: -64.464094+0.001731j
[2025-09-18 08:37:21] [Iter 1098/2250] R3[47/1200], Temp: 0.9962, Energy: -64.477273-0.000245j
[2025-09-18 08:37:51] [Iter 1099/2250] R3[48/1200], Temp: 0.9961, Energy: -64.468686-0.001974j
[2025-09-18 08:38:20] [Iter 1100/2250] R3[49/1200], Temp: 0.9959, Energy: -64.456161+0.001198j
[2025-09-18 08:38:50] [Iter 1101/2250] R3[50/1200], Temp: 0.9957, Energy: -64.456745-0.002861j
[2025-09-18 08:39:19] [Iter 1102/2250] R3[51/1200], Temp: 0.9955, Energy: -64.458857+0.004132j
[2025-09-18 08:39:49] [Iter 1103/2250] R3[52/1200], Temp: 0.9954, Energy: -64.478306-0.003375j
[2025-09-18 08:40:18] [Iter 1104/2250] R3[53/1200], Temp: 0.9952, Energy: -64.453344+0.001604j
[2025-09-18 08:40:48] [Iter 1105/2250] R3[54/1200], Temp: 0.9950, Energy: -64.461346-0.003874j
[2025-09-18 08:41:17] [Iter 1106/2250] R3[55/1200], Temp: 0.9948, Energy: -64.469225+0.000318j
[2025-09-18 08:41:46] [Iter 1107/2250] R3[56/1200], Temp: 0.9946, Energy: -64.463482-0.000288j
[2025-09-18 08:42:16] [Iter 1108/2250] R3[57/1200], Temp: 0.9944, Energy: -64.479388-0.003878j
[2025-09-18 08:42:45] [Iter 1109/2250] R3[58/1200], Temp: 0.9942, Energy: -64.461878-0.000131j
[2025-09-18 08:43:15] [Iter 1110/2250] R3[59/1200], Temp: 0.9940, Energy: -64.481752+0.007022j
[2025-09-18 08:43:44] [Iter 1111/2250] R3[60/1200], Temp: 0.9938, Energy: -64.473241+0.005206j
[2025-09-18 08:44:14] [Iter 1112/2250] R3[61/1200], Temp: 0.9936, Energy: -64.472132-0.002937j
[2025-09-18 08:44:43] [Iter 1113/2250] R3[62/1200], Temp: 0.9934, Energy: -64.463317-0.002854j
[2025-09-18 08:45:13] [Iter 1114/2250] R3[63/1200], Temp: 0.9932, Energy: -64.468911-0.000327j
[2025-09-18 08:45:42] [Iter 1115/2250] R3[64/1200], Temp: 0.9930, Energy: -64.477818-0.007729j
[2025-09-18 08:46:12] [Iter 1116/2250] R3[65/1200], Temp: 0.9928, Energy: -64.482783-0.003172j
[2025-09-18 08:46:41] [Iter 1117/2250] R3[66/1200], Temp: 0.9926, Energy: -64.479197-0.003129j
[2025-09-18 08:47:11] [Iter 1118/2250] R3[67/1200], Temp: 0.9923, Energy: -64.466624-0.001624j
[2025-09-18 08:47:40] [Iter 1119/2250] R3[68/1200], Temp: 0.9921, Energy: -64.469349-0.004762j
[2025-09-18 08:48:10] [Iter 1120/2250] R3[69/1200], Temp: 0.9919, Energy: -64.460393+0.001883j
[2025-09-18 08:48:39] [Iter 1121/2250] R3[70/1200], Temp: 0.9916, Energy: -64.479773-0.000055j
[2025-09-18 08:49:09] [Iter 1122/2250] R3[71/1200], Temp: 0.9914, Energy: -64.463457+0.004947j
[2025-09-18 08:49:38] [Iter 1123/2250] R3[72/1200], Temp: 0.9911, Energy: -64.475432+0.003186j
[2025-09-18 08:50:08] [Iter 1124/2250] R3[73/1200], Temp: 0.9909, Energy: -64.474219-0.004345j
[2025-09-18 08:50:37] [Iter 1125/2250] R3[74/1200], Temp: 0.9906, Energy: -64.478736+0.001380j
[2025-09-18 08:51:07] [Iter 1126/2250] R3[75/1200], Temp: 0.9904, Energy: -64.460526+0.000354j
[2025-09-18 08:51:36] [Iter 1127/2250] R3[76/1200], Temp: 0.9901, Energy: -64.479459-0.000249j
[2025-09-18 08:52:05] [Iter 1128/2250] R3[77/1200], Temp: 0.9899, Energy: -64.484594-0.004028j
[2025-09-18 08:52:35] [Iter 1129/2250] R3[78/1200], Temp: 0.9896, Energy: -64.469703+0.000603j
[2025-09-18 08:53:04] [Iter 1130/2250] R3[79/1200], Temp: 0.9893, Energy: -64.482108-0.005003j
[2025-09-18 08:53:34] [Iter 1131/2250] R3[80/1200], Temp: 0.9891, Energy: -64.465085-0.001977j
[2025-09-18 08:54:03] [Iter 1132/2250] R3[81/1200], Temp: 0.9888, Energy: -64.475812+0.001466j
[2025-09-18 08:54:33] [Iter 1133/2250] R3[82/1200], Temp: 0.9885, Energy: -64.471976-0.009712j
[2025-09-18 08:55:02] [Iter 1134/2250] R3[83/1200], Temp: 0.9882, Energy: -64.458460+0.001347j
[2025-09-18 08:55:32] [Iter 1135/2250] R3[84/1200], Temp: 0.9880, Energy: -64.461359-0.006355j
[2025-09-18 08:56:01] [Iter 1136/2250] R3[85/1200], Temp: 0.9877, Energy: -64.461096-0.000162j
[2025-09-18 08:56:31] [Iter 1137/2250] R3[86/1200], Temp: 0.9874, Energy: -64.462642-0.003990j
[2025-09-18 08:57:00] [Iter 1138/2250] R3[87/1200], Temp: 0.9871, Energy: -64.456499-0.000012j
[2025-09-18 08:57:30] [Iter 1139/2250] R3[88/1200], Temp: 0.9868, Energy: -64.458579+0.005194j
[2025-09-18 08:57:59] [Iter 1140/2250] R3[89/1200], Temp: 0.9865, Energy: -64.454237+0.003683j
[2025-09-18 08:58:29] [Iter 1141/2250] R3[90/1200], Temp: 0.9862, Energy: -64.477393+0.004093j
[2025-09-18 08:58:58] [Iter 1142/2250] R3[91/1200], Temp: 0.9859, Energy: -64.478785-0.001736j
[2025-09-18 08:59:28] [Iter 1143/2250] R3[92/1200], Temp: 0.9856, Energy: -64.473317-0.003657j
[2025-09-18 08:59:57] [Iter 1144/2250] R3[93/1200], Temp: 0.9853, Energy: -64.470621-0.001022j
[2025-09-18 09:00:27] [Iter 1145/2250] R3[94/1200], Temp: 0.9849, Energy: -64.462611+0.002854j
[2025-09-18 09:00:56] [Iter 1146/2250] R3[95/1200], Temp: 0.9846, Energy: -64.503397-0.000177j
[2025-09-18 09:01:26] [Iter 1147/2250] R3[96/1200], Temp: 0.9843, Energy: -64.478464+0.002164j
[2025-09-18 09:01:55] [Iter 1148/2250] R3[97/1200], Temp: 0.9840, Energy: -64.469681-0.000213j
[2025-09-18 09:02:25] [Iter 1149/2250] R3[98/1200], Temp: 0.9836, Energy: -64.463849-0.004871j
[2025-09-18 09:02:54] [Iter 1150/2250] R3[99/1200], Temp: 0.9833, Energy: -64.480196-0.001415j
[2025-09-18 09:03:24] [Iter 1151/2250] R3[100/1200], Temp: 0.9830, Energy: -64.461859-0.002491j
[2025-09-18 09:03:53] [Iter 1152/2250] R3[101/1200], Temp: 0.9826, Energy: -64.480792+0.003225j
[2025-09-18 09:04:23] [Iter 1153/2250] R3[102/1200], Temp: 0.9823, Energy: -64.455515+0.000881j
[2025-09-18 09:04:52] [Iter 1154/2250] R3[103/1200], Temp: 0.9819, Energy: -64.470069+0.000643j
[2025-09-18 09:05:22] [Iter 1155/2250] R3[104/1200], Temp: 0.9816, Energy: -64.467513+0.000361j
[2025-09-18 09:05:51] [Iter 1156/2250] R3[105/1200], Temp: 0.9812, Energy: -64.471225-0.002708j
[2025-09-18 09:06:20] [Iter 1157/2250] R3[106/1200], Temp: 0.9809, Energy: -64.490042+0.004280j
[2025-09-18 09:06:50] [Iter 1158/2250] R3[107/1200], Temp: 0.9805, Energy: -64.462763+0.000559j
[2025-09-18 09:07:19] [Iter 1159/2250] R3[108/1200], Temp: 0.9801, Energy: -64.474014-0.001146j
[2025-09-18 09:07:49] [Iter 1160/2250] R3[109/1200], Temp: 0.9798, Energy: -64.462925-0.003910j
[2025-09-18 09:08:18] [Iter 1161/2250] R3[110/1200], Temp: 0.9794, Energy: -64.496681-0.000491j
[2025-09-18 09:08:48] [Iter 1162/2250] R3[111/1200], Temp: 0.9790, Energy: -64.476613-0.000633j
[2025-09-18 09:09:17] [Iter 1163/2250] R3[112/1200], Temp: 0.9787, Energy: -64.454316+0.004182j
[2025-09-18 09:09:47] [Iter 1164/2250] R3[113/1200], Temp: 0.9783, Energy: -64.487195+0.002908j
[2025-09-18 09:10:16] [Iter 1165/2250] R3[114/1200], Temp: 0.9779, Energy: -64.483155-0.000295j
[2025-09-18 09:10:46] [Iter 1166/2250] R3[115/1200], Temp: 0.9775, Energy: -64.481940-0.004397j
[2025-09-18 09:11:15] [Iter 1167/2250] R3[116/1200], Temp: 0.9771, Energy: -64.471182-0.001974j
[2025-09-18 09:11:45] [Iter 1168/2250] R3[117/1200], Temp: 0.9767, Energy: -64.482700+0.004414j
[2025-09-18 09:12:14] [Iter 1169/2250] R3[118/1200], Temp: 0.9763, Energy: -64.479998-0.001043j
[2025-09-18 09:12:44] [Iter 1170/2250] R3[119/1200], Temp: 0.9759, Energy: -64.466278+0.001292j
[2025-09-18 09:13:13] [Iter 1171/2250] R3[120/1200], Temp: 0.9755, Energy: -64.472825-0.001634j
[2025-09-18 09:13:43] [Iter 1172/2250] R3[121/1200], Temp: 0.9751, Energy: -64.476827-0.005537j
[2025-09-18 09:14:12] [Iter 1173/2250] R3[122/1200], Temp: 0.9747, Energy: -64.492067+0.001508j
[2025-09-18 09:14:42] [Iter 1174/2250] R3[123/1200], Temp: 0.9743, Energy: -64.472164-0.000388j
[2025-09-18 09:15:11] [Iter 1175/2250] R3[124/1200], Temp: 0.9739, Energy: -64.487199+0.000273j
[2025-09-18 09:15:41] [Iter 1176/2250] R3[125/1200], Temp: 0.9735, Energy: -64.464723+0.001483j
[2025-09-18 09:16:10] [Iter 1177/2250] R3[126/1200], Temp: 0.9730, Energy: -64.482973+0.000201j
[2025-09-18 09:16:40] [Iter 1178/2250] R3[127/1200], Temp: 0.9726, Energy: -64.482050+0.001624j
[2025-09-18 09:17:09] [Iter 1179/2250] R3[128/1200], Temp: 0.9722, Energy: -64.496696+0.003708j
[2025-09-18 09:17:39] [Iter 1180/2250] R3[129/1200], Temp: 0.9718, Energy: -64.484634+0.000911j
[2025-09-18 09:18:08] [Iter 1181/2250] R3[130/1200], Temp: 0.9713, Energy: -64.492008-0.000207j
[2025-09-18 09:18:37] [Iter 1182/2250] R3[131/1200], Temp: 0.9709, Energy: -64.475933-0.004707j
[2025-09-18 09:19:07] [Iter 1183/2250] R3[132/1200], Temp: 0.9704, Energy: -64.486407+0.000626j
[2025-09-18 09:19:36] [Iter 1184/2250] R3[133/1200], Temp: 0.9700, Energy: -64.463731+0.004771j
[2025-09-18 09:20:06] [Iter 1185/2250] R3[134/1200], Temp: 0.9695, Energy: -64.476232+0.000701j
[2025-09-18 09:20:35] [Iter 1186/2250] R3[135/1200], Temp: 0.9691, Energy: -64.482879-0.003373j
[2025-09-18 09:21:05] [Iter 1187/2250] R3[136/1200], Temp: 0.9686, Energy: -64.478292+0.002982j
[2025-09-18 09:21:34] [Iter 1188/2250] R3[137/1200], Temp: 0.9682, Energy: -64.497493-0.001139j
[2025-09-18 09:22:04] [Iter 1189/2250] R3[138/1200], Temp: 0.9677, Energy: -64.476296+0.002593j
[2025-09-18 09:22:33] [Iter 1190/2250] R3[139/1200], Temp: 0.9673, Energy: -64.498376-0.006668j
[2025-09-18 09:23:03] [Iter 1191/2250] R3[140/1200], Temp: 0.9668, Energy: -64.479578-0.001803j
[2025-09-18 09:23:32] [Iter 1192/2250] R3[141/1200], Temp: 0.9663, Energy: -64.475867-0.002322j
[2025-09-18 09:24:02] [Iter 1193/2250] R3[142/1200], Temp: 0.9658, Energy: -64.457309-0.004216j
[2025-09-18 09:24:31] [Iter 1194/2250] R3[143/1200], Temp: 0.9654, Energy: -64.476077-0.002653j
[2025-09-18 09:25:01] [Iter 1195/2250] R3[144/1200], Temp: 0.9649, Energy: -64.476243-0.007396j
[2025-09-18 09:25:30] [Iter 1196/2250] R3[145/1200], Temp: 0.9644, Energy: -64.488212-0.001133j
[2025-09-18 09:26:00] [Iter 1197/2250] R3[146/1200], Temp: 0.9639, Energy: -64.477361+0.005237j
[2025-09-18 09:26:29] [Iter 1198/2250] R3[147/1200], Temp: 0.9634, Energy: -64.472732-0.000451j
[2025-09-18 09:26:59] [Iter 1199/2250] R3[148/1200], Temp: 0.9629, Energy: -64.480847+0.003363j
[2025-09-18 09:27:28] [Iter 1200/2250] R3[149/1200], Temp: 0.9624, Energy: -64.467232+0.001010j
[2025-09-18 09:27:58] [Iter 1201/2250] R3[150/1200], Temp: 0.9619, Energy: -64.495975+0.004165j
[2025-09-18 09:28:27] [Iter 1202/2250] R3[151/1200], Temp: 0.9614, Energy: -64.490222+0.004137j
[2025-09-18 09:28:57] [Iter 1203/2250] R3[152/1200], Temp: 0.9609, Energy: -64.484099-0.003550j
[2025-09-18 09:29:26] [Iter 1204/2250] R3[153/1200], Temp: 0.9604, Energy: -64.480554-0.001447j
[2025-09-18 09:29:56] [Iter 1205/2250] R3[154/1200], Temp: 0.9599, Energy: -64.477441-0.001705j
[2025-09-18 09:30:25] [Iter 1206/2250] R3[155/1200], Temp: 0.9594, Energy: -64.472249+0.002197j
[2025-09-18 09:30:55] [Iter 1207/2250] R3[156/1200], Temp: 0.9589, Energy: -64.477094+0.000365j
[2025-09-18 09:31:24] [Iter 1208/2250] R3[157/1200], Temp: 0.9584, Energy: -64.487393-0.003039j
[2025-09-18 09:31:54] [Iter 1209/2250] R3[158/1200], Temp: 0.9578, Energy: -64.467057+0.006976j
[2025-09-18 09:32:23] [Iter 1210/2250] R3[159/1200], Temp: 0.9573, Energy: -64.472132+0.001800j
[2025-09-18 09:32:53] [Iter 1211/2250] R3[160/1200], Temp: 0.9568, Energy: -64.459223+0.004727j
[2025-09-18 09:33:22] [Iter 1212/2250] R3[161/1200], Temp: 0.9562, Energy: -64.466243-0.002819j
[2025-09-18 09:33:52] [Iter 1213/2250] R3[162/1200], Temp: 0.9557, Energy: -64.472306+0.002602j
[2025-09-18 09:34:21] [Iter 1214/2250] R3[163/1200], Temp: 0.9552, Energy: -64.488905+0.002279j
[2025-09-18 09:34:51] [Iter 1215/2250] R3[164/1200], Temp: 0.9546, Energy: -64.482421+0.001525j
[2025-09-18 09:35:20] [Iter 1216/2250] R3[165/1200], Temp: 0.9541, Energy: -64.482959-0.000841j
[2025-09-18 09:35:50] [Iter 1217/2250] R3[166/1200], Temp: 0.9535, Energy: -64.475184-0.000448j
[2025-09-18 09:36:19] [Iter 1218/2250] R3[167/1200], Temp: 0.9530, Energy: -64.473896-0.003622j
[2025-09-18 09:36:48] [Iter 1219/2250] R3[168/1200], Temp: 0.9524, Energy: -64.474475+0.002751j
[2025-09-18 09:37:18] [Iter 1220/2250] R3[169/1200], Temp: 0.9519, Energy: -64.492338-0.005997j
[2025-09-18 09:37:47] [Iter 1221/2250] R3[170/1200], Temp: 0.9513, Energy: -64.483928+0.001072j
[2025-09-18 09:38:17] [Iter 1222/2250] R3[171/1200], Temp: 0.9507, Energy: -64.472612+0.000227j
[2025-09-18 09:38:46] [Iter 1223/2250] R3[172/1200], Temp: 0.9502, Energy: -64.493976+0.002103j
[2025-09-18 09:39:16] [Iter 1224/2250] R3[173/1200], Temp: 0.9496, Energy: -64.491434-0.000863j
[2025-09-18 09:39:45] [Iter 1225/2250] R3[174/1200], Temp: 0.9490, Energy: -64.478797-0.001659j
[2025-09-18 09:40:15] [Iter 1226/2250] R3[175/1200], Temp: 0.9484, Energy: -64.486330-0.003786j
[2025-09-18 09:40:44] [Iter 1227/2250] R3[176/1200], Temp: 0.9479, Energy: -64.482094-0.000461j
[2025-09-18 09:41:14] [Iter 1228/2250] R3[177/1200], Temp: 0.9473, Energy: -64.483359+0.002189j
[2025-09-18 09:41:43] [Iter 1229/2250] R3[178/1200], Temp: 0.9467, Energy: -64.468115-0.001025j
[2025-09-18 09:42:13] [Iter 1230/2250] R3[179/1200], Temp: 0.9461, Energy: -64.486030-0.001706j
[2025-09-18 09:42:42] [Iter 1231/2250] R3[180/1200], Temp: 0.9455, Energy: -64.493950-0.002805j
[2025-09-18 09:43:12] [Iter 1232/2250] R3[181/1200], Temp: 0.9449, Energy: -64.489890-0.002941j
[2025-09-18 09:43:41] [Iter 1233/2250] R3[182/1200], Temp: 0.9443, Energy: -64.473613-0.002425j
[2025-09-18 09:44:11] [Iter 1234/2250] R3[183/1200], Temp: 0.9437, Energy: -64.472551+0.000394j
[2025-09-18 09:44:40] [Iter 1235/2250] R3[184/1200], Temp: 0.9431, Energy: -64.482879-0.002582j
[2025-09-18 09:45:10] [Iter 1236/2250] R3[185/1200], Temp: 0.9425, Energy: -64.475440+0.002975j
[2025-09-18 09:45:39] [Iter 1237/2250] R3[186/1200], Temp: 0.9419, Energy: -64.496090-0.000923j
[2025-09-18 09:46:09] [Iter 1238/2250] R3[187/1200], Temp: 0.9413, Energy: -64.483871+0.004711j
[2025-09-18 09:46:38] [Iter 1239/2250] R3[188/1200], Temp: 0.9407, Energy: -64.484524-0.001570j
[2025-09-18 09:47:08] [Iter 1240/2250] R3[189/1200], Temp: 0.9400, Energy: -64.478271-0.002311j
[2025-09-18 09:47:37] [Iter 1241/2250] R3[190/1200], Temp: 0.9394, Energy: -64.488785-0.003145j
[2025-09-18 09:48:07] [Iter 1242/2250] R3[191/1200], Temp: 0.9388, Energy: -64.492237-0.005201j
[2025-09-18 09:48:36] [Iter 1243/2250] R3[192/1200], Temp: 0.9382, Energy: -64.488167+0.002456j
[2025-09-18 09:49:06] [Iter 1244/2250] R3[193/1200], Temp: 0.9375, Energy: -64.486760+0.000926j
[2025-09-18 09:49:35] [Iter 1245/2250] R3[194/1200], Temp: 0.9369, Energy: -64.490650+0.000679j
[2025-09-18 09:50:05] [Iter 1246/2250] R3[195/1200], Temp: 0.9362, Energy: -64.476880+0.001205j
[2025-09-18 09:50:34] [Iter 1247/2250] R3[196/1200], Temp: 0.9356, Energy: -64.498174+0.002731j
[2025-09-18 09:51:04] [Iter 1248/2250] R3[197/1200], Temp: 0.9350, Energy: -64.486591+0.003896j
[2025-09-18 09:51:33] [Iter 1249/2250] R3[198/1200], Temp: 0.9343, Energy: -64.481371+0.002184j
[2025-09-18 09:52:03] [Iter 1250/2250] R3[199/1200], Temp: 0.9337, Energy: -64.495359-0.005277j
[2025-09-18 09:52:03] ✓ Checkpoint saved: checkpoint_iter_001250.pkl
[2025-09-18 09:52:32] [Iter 1251/2250] R3[200/1200], Temp: 0.9330, Energy: -64.491132-0.003256j
[2025-09-18 09:53:02] [Iter 1252/2250] R3[201/1200], Temp: 0.9324, Energy: -64.486273-0.005020j
[2025-09-18 09:53:31] [Iter 1253/2250] R3[202/1200], Temp: 0.9317, Energy: -64.489797-0.001035j
[2025-09-18 09:54:01] [Iter 1254/2250] R3[203/1200], Temp: 0.9310, Energy: -64.471586-0.002410j
[2025-09-18 09:54:30] [Iter 1255/2250] R3[204/1200], Temp: 0.9304, Energy: -64.462389-0.003332j
[2025-09-18 09:55:00] [Iter 1256/2250] R3[205/1200], Temp: 0.9297, Energy: -64.474505-0.004015j
[2025-09-18 09:55:29] [Iter 1257/2250] R3[206/1200], Temp: 0.9290, Energy: -64.461115+0.000754j
[2025-09-18 09:55:58] [Iter 1258/2250] R3[207/1200], Temp: 0.9284, Energy: -64.455719+0.002259j
[2025-09-18 09:56:28] [Iter 1259/2250] R3[208/1200], Temp: 0.9277, Energy: -64.444218+0.004428j
[2025-09-18 09:56:57] [Iter 1260/2250] R3[209/1200], Temp: 0.9270, Energy: -64.444061-0.000762j
[2025-09-18 09:57:27] [Iter 1261/2250] R3[210/1200], Temp: 0.9263, Energy: -64.474960+0.001873j
[2025-09-18 09:57:56] [Iter 1262/2250] R3[211/1200], Temp: 0.9256, Energy: -64.466618+0.004091j
[2025-09-18 09:58:26] [Iter 1263/2250] R3[212/1200], Temp: 0.9249, Energy: -64.474338-0.002899j
[2025-09-18 09:58:55] [Iter 1264/2250] R3[213/1200], Temp: 0.9243, Energy: -64.469190+0.001810j
[2025-09-18 09:59:25] [Iter 1265/2250] R3[214/1200], Temp: 0.9236, Energy: -64.491169-0.001240j
[2025-09-18 09:59:54] [Iter 1266/2250] R3[215/1200], Temp: 0.9229, Energy: -64.482567-0.000883j
[2025-09-18 10:00:24] [Iter 1267/2250] R3[216/1200], Temp: 0.9222, Energy: -64.482424-0.002801j
[2025-09-18 10:00:53] [Iter 1268/2250] R3[217/1200], Temp: 0.9215, Energy: -64.477952-0.001602j
[2025-09-18 10:01:23] [Iter 1269/2250] R3[218/1200], Temp: 0.9208, Energy: -64.491082-0.000635j
[2025-09-18 10:01:52] [Iter 1270/2250] R3[219/1200], Temp: 0.9200, Energy: -64.489514+0.001439j
[2025-09-18 10:02:22] [Iter 1271/2250] R3[220/1200], Temp: 0.9193, Energy: -64.472256+0.002332j
[2025-09-18 10:02:51] [Iter 1272/2250] R3[221/1200], Temp: 0.9186, Energy: -64.483237+0.000500j
[2025-09-18 10:03:21] [Iter 1273/2250] R3[222/1200], Temp: 0.9179, Energy: -64.477511+0.004855j
[2025-09-18 10:03:50] [Iter 1274/2250] R3[223/1200], Temp: 0.9172, Energy: -64.498269+0.000041j
[2025-09-18 10:04:20] [Iter 1275/2250] R3[224/1200], Temp: 0.9165, Energy: -64.479267-0.003499j
[2025-09-18 10:04:50] [Iter 1276/2250] R3[225/1200], Temp: 0.9157, Energy: -64.475247+0.003106j
[2025-09-18 10:05:21] [Iter 1277/2250] R3[226/1200], Temp: 0.9150, Energy: -64.484777+0.001933j
[2025-09-18 10:05:51] [Iter 1278/2250] R3[227/1200], Temp: 0.9143, Energy: -64.485259-0.000668j
[2025-09-18 10:06:21] [Iter 1279/2250] R3[228/1200], Temp: 0.9135, Energy: -64.502306-0.001744j
[2025-09-18 10:06:51] [Iter 1280/2250] R3[229/1200], Temp: 0.9128, Energy: -64.477369+0.004877j
[2025-09-18 10:07:21] [Iter 1281/2250] R3[230/1200], Temp: 0.9121, Energy: -64.474971+0.001893j
[2025-09-18 10:07:50] [Iter 1282/2250] R3[231/1200], Temp: 0.9113, Energy: -64.488816+0.001095j
[2025-09-18 10:08:20] [Iter 1283/2250] R3[232/1200], Temp: 0.9106, Energy: -64.482869+0.004094j
[2025-09-18 10:08:50] [Iter 1284/2250] R3[233/1200], Temp: 0.9098, Energy: -64.491512+0.004072j
[2025-09-18 10:09:19] [Iter 1285/2250] R3[234/1200], Temp: 0.9091, Energy: -64.495111-0.007533j
[2025-09-18 10:09:49] [Iter 1286/2250] R3[235/1200], Temp: 0.9083, Energy: -64.489552-0.000684j
[2025-09-18 10:10:18] [Iter 1287/2250] R3[236/1200], Temp: 0.9076, Energy: -64.494509+0.003214j
[2025-09-18 10:10:48] [Iter 1288/2250] R3[237/1200], Temp: 0.9068, Energy: -64.497279-0.002125j
[2025-09-18 10:11:17] [Iter 1289/2250] R3[238/1200], Temp: 0.9060, Energy: -64.479781-0.003707j
[2025-09-18 10:11:47] [Iter 1290/2250] R3[239/1200], Temp: 0.9053, Energy: -64.482451-0.000519j
[2025-09-18 10:12:16] [Iter 1291/2250] R3[240/1200], Temp: 0.9045, Energy: -64.502814+0.007128j
[2025-09-18 10:12:46] [Iter 1292/2250] R3[241/1200], Temp: 0.9037, Energy: -64.497032+0.001011j
[2025-09-18 10:13:15] [Iter 1293/2250] R3[242/1200], Temp: 0.9030, Energy: -64.489758+0.001104j
[2025-09-18 10:13:45] [Iter 1294/2250] R3[243/1200], Temp: 0.9022, Energy: -64.486579-0.003578j
[2025-09-18 10:14:14] [Iter 1295/2250] R3[244/1200], Temp: 0.9014, Energy: -64.500821+0.003680j
[2025-09-18 10:14:44] [Iter 1296/2250] R3[245/1200], Temp: 0.9006, Energy: -64.493467-0.001663j
[2025-09-18 10:15:13] [Iter 1297/2250] R3[246/1200], Temp: 0.8998, Energy: -64.483514+0.001979j
[2025-09-18 10:15:43] [Iter 1298/2250] R3[247/1200], Temp: 0.8991, Energy: -64.496710+0.003464j
[2025-09-18 10:16:12] [Iter 1299/2250] R3[248/1200], Temp: 0.8983, Energy: -64.499660-0.001666j
[2025-09-18 10:16:42] [Iter 1300/2250] R3[249/1200], Temp: 0.8975, Energy: -64.495043+0.002212j
[2025-09-18 10:17:11] [Iter 1301/2250] R3[250/1200], Temp: 0.8967, Energy: -64.482314-0.004284j
[2025-09-18 10:17:41] [Iter 1302/2250] R3[251/1200], Temp: 0.8959, Energy: -64.497970+0.002621j
[2025-09-18 10:18:10] [Iter 1303/2250] R3[252/1200], Temp: 0.8951, Energy: -64.485495-0.003579j
[2025-09-18 10:18:40] [Iter 1304/2250] R3[253/1200], Temp: 0.8943, Energy: -64.490674+0.000676j
[2025-09-18 10:19:09] [Iter 1305/2250] R3[254/1200], Temp: 0.8935, Energy: -64.490332-0.000797j
[2025-09-18 10:19:39] [Iter 1306/2250] R3[255/1200], Temp: 0.8927, Energy: -64.496461+0.002771j
[2025-09-18 10:20:08] [Iter 1307/2250] R3[256/1200], Temp: 0.8918, Energy: -64.503290+0.002410j
[2025-09-18 10:20:38] [Iter 1308/2250] R3[257/1200], Temp: 0.8910, Energy: -64.484310+0.006709j
[2025-09-18 10:21:07] [Iter 1309/2250] R3[258/1200], Temp: 0.8902, Energy: -64.492533-0.002193j
[2025-09-18 10:21:37] [Iter 1310/2250] R3[259/1200], Temp: 0.8894, Energy: -64.483418+0.000686j
[2025-09-18 10:22:06] [Iter 1311/2250] R3[260/1200], Temp: 0.8886, Energy: -64.491831-0.002946j
[2025-09-18 10:22:36] [Iter 1312/2250] R3[261/1200], Temp: 0.8877, Energy: -64.501201+0.003554j
[2025-09-18 10:23:05] [Iter 1313/2250] R3[262/1200], Temp: 0.8869, Energy: -64.495900-0.002454j
[2025-09-18 10:23:35] [Iter 1314/2250] R3[263/1200], Temp: 0.8861, Energy: -64.484965+0.002939j
[2025-09-18 10:24:04] [Iter 1315/2250] R3[264/1200], Temp: 0.8853, Energy: -64.498730+0.003844j
[2025-09-18 10:24:34] [Iter 1316/2250] R3[265/1200], Temp: 0.8844, Energy: -64.507504-0.001115j
[2025-09-18 10:25:03] [Iter 1317/2250] R3[266/1200], Temp: 0.8836, Energy: -64.496064-0.001360j
[2025-09-18 10:25:33] [Iter 1318/2250] R3[267/1200], Temp: 0.8827, Energy: -64.498050+0.000841j
[2025-09-18 10:26:02] [Iter 1319/2250] R3[268/1200], Temp: 0.8819, Energy: -64.479888-0.000637j
[2025-09-18 10:26:31] [Iter 1320/2250] R3[269/1200], Temp: 0.8811, Energy: -64.479930-0.000224j
[2025-09-18 10:27:01] [Iter 1321/2250] R3[270/1200], Temp: 0.8802, Energy: -64.497940-0.004283j
[2025-09-18 10:27:31] [Iter 1322/2250] R3[271/1200], Temp: 0.8794, Energy: -64.494180+0.000420j
[2025-09-18 10:28:00] [Iter 1323/2250] R3[272/1200], Temp: 0.8785, Energy: -64.505088-0.001455j
[2025-09-18 10:28:29] [Iter 1324/2250] R3[273/1200], Temp: 0.8776, Energy: -64.488233+0.002176j
[2025-09-18 10:28:59] [Iter 1325/2250] R3[274/1200], Temp: 0.8768, Energy: -64.498216-0.000792j
[2025-09-18 10:29:28] [Iter 1326/2250] R3[275/1200], Temp: 0.8759, Energy: -64.490479+0.002243j
[2025-09-18 10:29:58] [Iter 1327/2250] R3[276/1200], Temp: 0.8751, Energy: -64.487957+0.004458j
[2025-09-18 10:30:27] [Iter 1328/2250] R3[277/1200], Temp: 0.8742, Energy: -64.498214+0.002072j
[2025-09-18 10:30:57] [Iter 1329/2250] R3[278/1200], Temp: 0.8733, Energy: -64.485544+0.003163j
[2025-09-18 10:31:26] [Iter 1330/2250] R3[279/1200], Temp: 0.8724, Energy: -64.490617-0.001953j
[2025-09-18 10:31:56] [Iter 1331/2250] R3[280/1200], Temp: 0.8716, Energy: -64.501529+0.002658j
[2025-09-18 10:32:25] [Iter 1332/2250] R3[281/1200], Temp: 0.8707, Energy: -64.489092+0.000047j
[2025-09-18 10:32:55] [Iter 1333/2250] R3[282/1200], Temp: 0.8698, Energy: -64.501691-0.003307j
[2025-09-18 10:33:24] [Iter 1334/2250] R3[283/1200], Temp: 0.8689, Energy: -64.503227-0.004553j
[2025-09-18 10:33:54] [Iter 1335/2250] R3[284/1200], Temp: 0.8680, Energy: -64.489222-0.002719j
[2025-09-18 10:34:23] [Iter 1336/2250] R3[285/1200], Temp: 0.8672, Energy: -64.498624+0.002270j
[2025-09-18 10:34:53] [Iter 1337/2250] R3[286/1200], Temp: 0.8663, Energy: -64.479998+0.000211j
[2025-09-18 10:35:22] [Iter 1338/2250] R3[287/1200], Temp: 0.8654, Energy: -64.489888+0.000082j
[2025-09-18 10:35:52] [Iter 1339/2250] R3[288/1200], Temp: 0.8645, Energy: -64.481034+0.003430j
[2025-09-18 10:36:21] [Iter 1340/2250] R3[289/1200], Temp: 0.8636, Energy: -64.495956-0.002069j
[2025-09-18 10:36:51] [Iter 1341/2250] R3[290/1200], Temp: 0.8627, Energy: -64.487336+0.001899j
[2025-09-18 10:37:20] [Iter 1342/2250] R3[291/1200], Temp: 0.8618, Energy: -64.501389-0.000667j
[2025-09-18 10:37:50] [Iter 1343/2250] R3[292/1200], Temp: 0.8609, Energy: -64.506800+0.001802j
[2025-09-18 10:38:19] [Iter 1344/2250] R3[293/1200], Temp: 0.8600, Energy: -64.503069-0.000424j
[2025-09-18 10:38:49] [Iter 1345/2250] R3[294/1200], Temp: 0.8591, Energy: -64.494406+0.000940j
[2025-09-18 10:39:18] [Iter 1346/2250] R3[295/1200], Temp: 0.8582, Energy: -64.505787-0.000278j
[2025-09-18 10:39:48] [Iter 1347/2250] R3[296/1200], Temp: 0.8572, Energy: -64.498104+0.004310j
[2025-09-18 10:40:17] [Iter 1348/2250] R3[297/1200], Temp: 0.8563, Energy: -64.499196+0.001124j
[2025-09-18 10:40:46] [Iter 1349/2250] R3[298/1200], Temp: 0.8554, Energy: -64.492027+0.008155j
[2025-09-18 10:41:16] [Iter 1350/2250] R3[299/1200], Temp: 0.8545, Energy: -64.495051-0.001175j
[2025-09-18 10:41:46] [Iter 1351/2250] R3[300/1200], Temp: 0.8536, Energy: -64.493244+0.003818j
[2025-09-18 10:42:15] [Iter 1352/2250] R3[301/1200], Temp: 0.8526, Energy: -64.483907-0.001171j
[2025-09-18 10:42:45] [Iter 1353/2250] R3[302/1200], Temp: 0.8517, Energy: -64.495674-0.000131j
[2025-09-18 10:43:15] [Iter 1354/2250] R3[303/1200], Temp: 0.8508, Energy: -64.489578+0.000758j
[2025-09-18 10:43:44] [Iter 1355/2250] R3[304/1200], Temp: 0.8498, Energy: -64.501299+0.002394j
[2025-09-18 10:44:14] [Iter 1356/2250] R3[305/1200], Temp: 0.8489, Energy: -64.490027+0.006007j
[2025-09-18 10:44:43] [Iter 1357/2250] R3[306/1200], Temp: 0.8480, Energy: -64.505563+0.000332j
[2025-09-18 10:45:13] [Iter 1358/2250] R3[307/1200], Temp: 0.8470, Energy: -64.503995-0.001823j
[2025-09-18 10:45:42] [Iter 1359/2250] R3[308/1200], Temp: 0.8461, Energy: -64.504741-0.002683j
[2025-09-18 10:46:11] [Iter 1360/2250] R3[309/1200], Temp: 0.8451, Energy: -64.506788+0.001164j
[2025-09-18 10:46:41] [Iter 1361/2250] R3[310/1200], Temp: 0.8442, Energy: -64.489521-0.000741j
[2025-09-18 10:47:10] [Iter 1362/2250] R3[311/1200], Temp: 0.8432, Energy: -64.494062+0.006738j
[2025-09-18 10:47:40] [Iter 1363/2250] R3[312/1200], Temp: 0.8423, Energy: -64.485536+0.002487j
[2025-09-18 10:48:09] [Iter 1364/2250] R3[313/1200], Temp: 0.8413, Energy: -64.482236-0.003676j
[2025-09-18 10:48:39] [Iter 1365/2250] R3[314/1200], Temp: 0.8404, Energy: -64.516017+0.003752j
[2025-09-18 10:49:08] [Iter 1366/2250] R3[315/1200], Temp: 0.8394, Energy: -64.486881-0.001234j
[2025-09-18 10:49:38] [Iter 1367/2250] R3[316/1200], Temp: 0.8384, Energy: -64.495832-0.000337j
[2025-09-18 10:50:07] [Iter 1368/2250] R3[317/1200], Temp: 0.8375, Energy: -64.488612+0.000976j
[2025-09-18 10:50:37] [Iter 1369/2250] R3[318/1200], Temp: 0.8365, Energy: -64.502676-0.000264j
[2025-09-18 10:51:06] [Iter 1370/2250] R3[319/1200], Temp: 0.8355, Energy: -64.507800-0.003711j
[2025-09-18 10:51:36] [Iter 1371/2250] R3[320/1200], Temp: 0.8346, Energy: -64.487998-0.005749j
[2025-09-18 10:52:05] [Iter 1372/2250] R3[321/1200], Temp: 0.8336, Energy: -64.504865+0.001881j
[2025-09-18 10:52:35] [Iter 1373/2250] R3[322/1200], Temp: 0.8326, Energy: -64.494866-0.001244j
[2025-09-18 10:53:04] [Iter 1374/2250] R3[323/1200], Temp: 0.8316, Energy: -64.488138-0.001437j
[2025-09-18 10:53:34] [Iter 1375/2250] R3[324/1200], Temp: 0.8307, Energy: -64.504314-0.004681j
[2025-09-18 10:54:03] [Iter 1376/2250] R3[325/1200], Temp: 0.8297, Energy: -64.503470-0.001237j
[2025-09-18 10:54:33] [Iter 1377/2250] R3[326/1200], Temp: 0.8287, Energy: -64.506491+0.004814j
[2025-09-18 10:55:02] [Iter 1378/2250] R3[327/1200], Temp: 0.8277, Energy: -64.484213-0.000041j
[2025-09-18 10:55:32] [Iter 1379/2250] R3[328/1200], Temp: 0.8267, Energy: -64.494272+0.001877j
[2025-09-18 10:56:01] [Iter 1380/2250] R3[329/1200], Temp: 0.8257, Energy: -64.491870+0.001222j
[2025-09-18 10:56:31] [Iter 1381/2250] R3[330/1200], Temp: 0.8247, Energy: -64.495090-0.005117j
[2025-09-18 10:57:00] [Iter 1382/2250] R3[331/1200], Temp: 0.8237, Energy: -64.498543-0.001015j
[2025-09-18 10:57:30] [Iter 1383/2250] R3[332/1200], Temp: 0.8227, Energy: -64.492063+0.004811j
[2025-09-18 10:57:59] [Iter 1384/2250] R3[333/1200], Temp: 0.8217, Energy: -64.505176+0.006412j
[2025-09-18 10:58:29] [Iter 1385/2250] R3[334/1200], Temp: 0.8207, Energy: -64.491287+0.001858j
[2025-09-18 10:58:58] [Iter 1386/2250] R3[335/1200], Temp: 0.8197, Energy: -64.509393+0.006638j
[2025-09-18 10:59:28] [Iter 1387/2250] R3[336/1200], Temp: 0.8187, Energy: -64.505773+0.000791j
[2025-09-18 10:59:57] [Iter 1388/2250] R3[337/1200], Temp: 0.8177, Energy: -64.505856+0.001216j
[2025-09-18 11:00:27] [Iter 1389/2250] R3[338/1200], Temp: 0.8167, Energy: -64.502423+0.001406j
[2025-09-18 11:00:56] [Iter 1390/2250] R3[339/1200], Temp: 0.8157, Energy: -64.501221+0.003195j
[2025-09-18 11:01:26] [Iter 1391/2250] R3[340/1200], Temp: 0.8147, Energy: -64.489485-0.003108j
[2025-09-18 11:01:55] [Iter 1392/2250] R3[341/1200], Temp: 0.8136, Energy: -64.485771+0.000454j
[2025-09-18 11:02:25] [Iter 1393/2250] R3[342/1200], Temp: 0.8126, Energy: -64.503762+0.001245j
[2025-09-18 11:02:54] [Iter 1394/2250] R3[343/1200], Temp: 0.8116, Energy: -64.513074-0.002461j
[2025-09-18 11:03:24] [Iter 1395/2250] R3[344/1200], Temp: 0.8106, Energy: -64.498015+0.003766j
[2025-09-18 11:03:53] [Iter 1396/2250] R3[345/1200], Temp: 0.8095, Energy: -64.488124+0.004832j
[2025-09-18 11:04:23] [Iter 1397/2250] R3[346/1200], Temp: 0.8085, Energy: -64.512059+0.004946j
[2025-09-18 11:04:52] [Iter 1398/2250] R3[347/1200], Temp: 0.8075, Energy: -64.513567+0.002357j
[2025-09-18 11:05:22] [Iter 1399/2250] R3[348/1200], Temp: 0.8065, Energy: -64.496496+0.000663j
[2025-09-18 11:05:51] [Iter 1400/2250] R3[349/1200], Temp: 0.8054, Energy: -64.502529+0.001784j
[2025-09-18 11:06:21] [Iter 1401/2250] R3[350/1200], Temp: 0.8044, Energy: -64.501097+0.002090j
[2025-09-18 11:06:50] [Iter 1402/2250] R3[351/1200], Temp: 0.8033, Energy: -64.509732+0.004451j
[2025-09-18 11:07:20] [Iter 1403/2250] R3[352/1200], Temp: 0.8023, Energy: -64.506713-0.002625j
[2025-09-18 11:07:49] [Iter 1404/2250] R3[353/1200], Temp: 0.8013, Energy: -64.497964+0.001683j
[2025-09-18 11:08:19] [Iter 1405/2250] R3[354/1200], Temp: 0.8002, Energy: -64.499829-0.000225j
[2025-09-18 11:08:48] [Iter 1406/2250] R3[355/1200], Temp: 0.7992, Energy: -64.498082-0.003289j
[2025-09-18 11:09:18] [Iter 1407/2250] R3[356/1200], Temp: 0.7981, Energy: -64.487863+0.005062j
[2025-09-18 11:09:47] [Iter 1408/2250] R3[357/1200], Temp: 0.7971, Energy: -64.507929-0.000110j
[2025-09-18 11:10:17] [Iter 1409/2250] R3[358/1200], Temp: 0.7960, Energy: -64.496611-0.001474j
[2025-09-18 11:10:46] [Iter 1410/2250] R3[359/1200], Temp: 0.7950, Energy: -64.501904-0.001761j
[2025-09-18 11:11:16] [Iter 1411/2250] R3[360/1200], Temp: 0.7939, Energy: -64.491383+0.000816j
[2025-09-18 11:11:45] [Iter 1412/2250] R3[361/1200], Temp: 0.7928, Energy: -64.506560+0.001704j
[2025-09-18 11:12:15] [Iter 1413/2250] R3[362/1200], Temp: 0.7918, Energy: -64.482912-0.002589j
[2025-09-18 11:12:44] [Iter 1414/2250] R3[363/1200], Temp: 0.7907, Energy: -64.468877-0.001549j
[2025-09-18 11:13:14] [Iter 1415/2250] R3[364/1200], Temp: 0.7896, Energy: -64.483160-0.003334j
[2025-09-18 11:13:43] [Iter 1416/2250] R3[365/1200], Temp: 0.7886, Energy: -64.484943+0.003134j
[2025-09-18 11:14:13] [Iter 1417/2250] R3[366/1200], Temp: 0.7875, Energy: -64.490210-0.002212j
[2025-09-18 11:14:42] [Iter 1418/2250] R3[367/1200], Temp: 0.7864, Energy: -64.493820+0.001267j
[2025-09-18 11:15:12] [Iter 1419/2250] R3[368/1200], Temp: 0.7854, Energy: -64.492382+0.004146j
[2025-09-18 11:15:41] [Iter 1420/2250] R3[369/1200], Temp: 0.7843, Energy: -64.484700-0.000725j
[2025-09-18 11:16:11] [Iter 1421/2250] R3[370/1200], Temp: 0.7832, Energy: -64.495533-0.002578j
[2025-09-18 11:16:40] [Iter 1422/2250] R3[371/1200], Temp: 0.7821, Energy: -64.487325-0.006191j
[2025-09-18 11:17:10] [Iter 1423/2250] R3[372/1200], Temp: 0.7810, Energy: -64.497417+0.003374j
[2025-09-18 11:17:39] [Iter 1424/2250] R3[373/1200], Temp: 0.7800, Energy: -64.477134+0.002746j
[2025-09-18 11:18:09] [Iter 1425/2250] R3[374/1200], Temp: 0.7789, Energy: -64.499489+0.002335j
[2025-09-18 11:18:38] [Iter 1426/2250] R3[375/1200], Temp: 0.7778, Energy: -64.493570+0.003322j
[2025-09-18 11:19:08] [Iter 1427/2250] R3[376/1200], Temp: 0.7767, Energy: -64.506702-0.002271j
[2025-09-18 11:19:37] [Iter 1428/2250] R3[377/1200], Temp: 0.7756, Energy: -64.519518-0.002054j
[2025-09-18 11:20:07] [Iter 1429/2250] R3[378/1200], Temp: 0.7745, Energy: -64.501323-0.000382j
[2025-09-18 11:20:36] [Iter 1430/2250] R3[379/1200], Temp: 0.7734, Energy: -64.507809-0.003319j
[2025-09-18 11:21:06] [Iter 1431/2250] R3[380/1200], Temp: 0.7723, Energy: -64.514212+0.001697j
[2025-09-18 11:21:35] [Iter 1432/2250] R3[381/1200], Temp: 0.7712, Energy: -64.501375+0.001554j
[2025-09-18 11:22:05] [Iter 1433/2250] R3[382/1200], Temp: 0.7701, Energy: -64.503200-0.003623j
[2025-09-18 11:22:34] [Iter 1434/2250] R3[383/1200], Temp: 0.7690, Energy: -64.505468-0.001139j
[2025-09-18 11:23:03] [Iter 1435/2250] R3[384/1200], Temp: 0.7679, Energy: -64.482435+0.003560j
[2025-09-18 11:23:33] [Iter 1436/2250] R3[385/1200], Temp: 0.7668, Energy: -64.510982+0.005276j
[2025-09-18 11:24:02] [Iter 1437/2250] R3[386/1200], Temp: 0.7657, Energy: -64.510857+0.000397j
[2025-09-18 11:24:32] [Iter 1438/2250] R3[387/1200], Temp: 0.7646, Energy: -64.516608+0.002489j
[2025-09-18 11:25:01] [Iter 1439/2250] R3[388/1200], Temp: 0.7635, Energy: -64.509168+0.004094j
[2025-09-18 11:25:31] [Iter 1440/2250] R3[389/1200], Temp: 0.7624, Energy: -64.528673+0.002486j
[2025-09-18 11:26:00] [Iter 1441/2250] R3[390/1200], Temp: 0.7612, Energy: -64.494107+0.008226j
[2025-09-18 11:26:30] [Iter 1442/2250] R3[391/1200], Temp: 0.7601, Energy: -64.507338+0.000577j
[2025-09-18 11:26:59] [Iter 1443/2250] R3[392/1200], Temp: 0.7590, Energy: -64.513490-0.000399j
[2025-09-18 11:27:29] [Iter 1444/2250] R3[393/1200], Temp: 0.7579, Energy: -64.510437-0.001375j
[2025-09-18 11:27:58] [Iter 1445/2250] R3[394/1200], Temp: 0.7568, Energy: -64.508878-0.001651j
[2025-09-18 11:28:28] [Iter 1446/2250] R3[395/1200], Temp: 0.7556, Energy: -64.488634-0.000278j
[2025-09-18 11:28:57] [Iter 1447/2250] R3[396/1200], Temp: 0.7545, Energy: -64.502506-0.004859j
[2025-09-18 11:29:26] [Iter 1448/2250] R3[397/1200], Temp: 0.7534, Energy: -64.509402-0.000969j
[2025-09-18 11:29:56] [Iter 1449/2250] R3[398/1200], Temp: 0.7523, Energy: -64.512970-0.001745j
[2025-09-18 11:30:25] [Iter 1450/2250] R3[399/1200], Temp: 0.7511, Energy: -64.514697+0.000164j
[2025-09-18 11:30:55] [Iter 1451/2250] R3[400/1200], Temp: 0.7500, Energy: -64.499704-0.001804j
[2025-09-18 11:31:24] [Iter 1452/2250] R3[401/1200], Temp: 0.7489, Energy: -64.492644+0.000481j
[2025-09-18 11:31:54] [Iter 1453/2250] R3[402/1200], Temp: 0.7477, Energy: -64.523909-0.000140j
[2025-09-18 11:32:23] [Iter 1454/2250] R3[403/1200], Temp: 0.7466, Energy: -64.517278+0.000616j
[2025-09-18 11:32:53] [Iter 1455/2250] R3[404/1200], Temp: 0.7455, Energy: -64.502883+0.002561j
[2025-09-18 11:33:22] [Iter 1456/2250] R3[405/1200], Temp: 0.7443, Energy: -64.512942-0.002622j
[2025-09-18 11:33:52] [Iter 1457/2250] R3[406/1200], Temp: 0.7432, Energy: -64.508095+0.001906j
[2025-09-18 11:34:21] [Iter 1458/2250] R3[407/1200], Temp: 0.7420, Energy: -64.510803-0.003071j
[2025-09-18 11:34:50] [Iter 1459/2250] R3[408/1200], Temp: 0.7409, Energy: -64.497230+0.002423j
[2025-09-18 11:35:20] [Iter 1460/2250] R3[409/1200], Temp: 0.7397, Energy: -64.497011-0.001374j
[2025-09-18 11:35:49] [Iter 1461/2250] R3[410/1200], Temp: 0.7386, Energy: -64.496196-0.001065j
[2025-09-18 11:36:19] [Iter 1462/2250] R3[411/1200], Temp: 0.7374, Energy: -64.500544-0.001749j
[2025-09-18 11:36:48] [Iter 1463/2250] R3[412/1200], Temp: 0.7363, Energy: -64.512060+0.000405j
[2025-09-18 11:37:18] [Iter 1464/2250] R3[413/1200], Temp: 0.7351, Energy: -64.510653-0.002621j
[2025-09-18 11:37:47] [Iter 1465/2250] R3[414/1200], Temp: 0.7340, Energy: -64.501652-0.000027j
[2025-09-18 11:38:17] [Iter 1466/2250] R3[415/1200], Temp: 0.7328, Energy: -64.518765-0.001795j
[2025-09-18 11:38:46] [Iter 1467/2250] R3[416/1200], Temp: 0.7316, Energy: -64.511128+0.000696j
[2025-09-18 11:39:16] [Iter 1468/2250] R3[417/1200], Temp: 0.7305, Energy: -64.501686+0.003929j
[2025-09-18 11:39:45] [Iter 1469/2250] R3[418/1200], Temp: 0.7293, Energy: -64.500811+0.000684j
[2025-09-18 11:40:15] [Iter 1470/2250] R3[419/1200], Temp: 0.7282, Energy: -64.494988-0.001947j
[2025-09-18 11:40:44] [Iter 1471/2250] R3[420/1200], Temp: 0.7270, Energy: -64.502789-0.001482j
[2025-09-18 11:41:13] [Iter 1472/2250] R3[421/1200], Temp: 0.7258, Energy: -64.512757+0.004350j
[2025-09-18 11:41:43] [Iter 1473/2250] R3[422/1200], Temp: 0.7247, Energy: -64.503576+0.000121j
[2025-09-18 11:42:12] [Iter 1474/2250] R3[423/1200], Temp: 0.7235, Energy: -64.507631+0.000987j
[2025-09-18 11:42:42] [Iter 1475/2250] R3[424/1200], Temp: 0.7223, Energy: -64.497967+0.000433j
[2025-09-18 11:43:11] [Iter 1476/2250] R3[425/1200], Temp: 0.7211, Energy: -64.509947+0.004076j
[2025-09-18 11:43:41] [Iter 1477/2250] R3[426/1200], Temp: 0.7200, Energy: -64.503352-0.000643j
[2025-09-18 11:44:10] [Iter 1478/2250] R3[427/1200], Temp: 0.7188, Energy: -64.509994-0.001550j
[2025-09-18 11:44:40] [Iter 1479/2250] R3[428/1200], Temp: 0.7176, Energy: -64.508293+0.002028j
[2025-09-18 11:45:09] [Iter 1480/2250] R3[429/1200], Temp: 0.7164, Energy: -64.500828+0.002440j
[2025-09-18 11:45:39] [Iter 1481/2250] R3[430/1200], Temp: 0.7153, Energy: -64.505448+0.000745j
[2025-09-18 11:46:08] [Iter 1482/2250] R3[431/1200], Temp: 0.7141, Energy: -64.510411+0.003705j
[2025-09-18 11:46:38] [Iter 1483/2250] R3[432/1200], Temp: 0.7129, Energy: -64.513987-0.001164j
[2025-09-18 11:47:07] [Iter 1484/2250] R3[433/1200], Temp: 0.7117, Energy: -64.511219-0.000605j
[2025-09-18 11:47:36] [Iter 1485/2250] R3[434/1200], Temp: 0.7105, Energy: -64.513208+0.000129j
[2025-09-18 11:48:06] [Iter 1486/2250] R3[435/1200], Temp: 0.7093, Energy: -64.513526-0.002317j
[2025-09-18 11:48:35] [Iter 1487/2250] R3[436/1200], Temp: 0.7081, Energy: -64.505443-0.002634j
[2025-09-18 11:49:05] [Iter 1488/2250] R3[437/1200], Temp: 0.7069, Energy: -64.521056+0.002008j
[2025-09-18 11:49:34] [Iter 1489/2250] R3[438/1200], Temp: 0.7058, Energy: -64.501885+0.000035j
[2025-09-18 11:50:04] [Iter 1490/2250] R3[439/1200], Temp: 0.7046, Energy: -64.518190+0.003143j
[2025-09-18 11:50:33] [Iter 1491/2250] R3[440/1200], Temp: 0.7034, Energy: -64.502437+0.000813j
[2025-09-18 11:51:03] [Iter 1492/2250] R3[441/1200], Temp: 0.7022, Energy: -64.513915+0.001368j
[2025-09-18 11:51:32] [Iter 1493/2250] R3[442/1200], Temp: 0.7010, Energy: -64.513157-0.002183j
[2025-09-18 11:52:02] [Iter 1494/2250] R3[443/1200], Temp: 0.6998, Energy: -64.490584+0.000844j
[2025-09-18 11:52:31] [Iter 1495/2250] R3[444/1200], Temp: 0.6986, Energy: -64.521896+0.000732j
[2025-09-18 11:53:01] [Iter 1496/2250] R3[445/1200], Temp: 0.6974, Energy: -64.511070-0.004614j
[2025-09-18 11:53:30] [Iter 1497/2250] R3[446/1200], Temp: 0.6962, Energy: -64.509238+0.000649j
[2025-09-18 11:54:00] [Iter 1498/2250] R3[447/1200], Temp: 0.6950, Energy: -64.503906-0.001822j
[2025-09-18 11:54:29] [Iter 1499/2250] R3[448/1200], Temp: 0.6938, Energy: -64.506868+0.001840j
[2025-09-18 11:54:58] [Iter 1500/2250] R3[449/1200], Temp: 0.6926, Energy: -64.524218+0.001343j
[2025-09-18 11:54:58] ✓ Checkpoint saved: checkpoint_iter_001500.pkl
[2025-09-18 11:55:28] [Iter 1501/2250] R3[450/1200], Temp: 0.6913, Energy: -64.530984+0.001762j
[2025-09-18 11:55:57] [Iter 1502/2250] R3[451/1200], Temp: 0.6901, Energy: -64.510900-0.000684j
[2025-09-18 11:56:27] [Iter 1503/2250] R3[452/1200], Temp: 0.6889, Energy: -64.514683+0.003267j
[2025-09-18 11:56:56] [Iter 1504/2250] R3[453/1200], Temp: 0.6877, Energy: -64.505673+0.000700j
[2025-09-18 11:57:26] [Iter 1505/2250] R3[454/1200], Temp: 0.6865, Energy: -64.500650-0.000950j
[2025-09-18 11:57:55] [Iter 1506/2250] R3[455/1200], Temp: 0.6853, Energy: -64.511559+0.002960j
[2025-09-18 11:58:25] [Iter 1507/2250] R3[456/1200], Temp: 0.6841, Energy: -64.512693+0.000741j
[2025-09-18 11:58:54] [Iter 1508/2250] R3[457/1200], Temp: 0.6828, Energy: -64.513189+0.000398j
[2025-09-18 11:59:24] [Iter 1509/2250] R3[458/1200], Temp: 0.6816, Energy: -64.488931+0.002239j
[2025-09-18 11:59:53] [Iter 1510/2250] R3[459/1200], Temp: 0.6804, Energy: -64.507825+0.004051j
[2025-09-18 12:00:23] [Iter 1511/2250] R3[460/1200], Temp: 0.6792, Energy: -64.524720-0.003839j
[2025-09-18 12:00:52] [Iter 1512/2250] R3[461/1200], Temp: 0.6780, Energy: -64.506233+0.002865j
[2025-09-18 12:01:21] [Iter 1513/2250] R3[462/1200], Temp: 0.6767, Energy: -64.516503+0.003212j
[2025-09-18 12:01:51] [Iter 1514/2250] R3[463/1200], Temp: 0.6755, Energy: -64.509181+0.001609j
[2025-09-18 12:02:20] [Iter 1515/2250] R3[464/1200], Temp: 0.6743, Energy: -64.500506-0.003203j
[2025-09-18 12:02:50] [Iter 1516/2250] R3[465/1200], Temp: 0.6731, Energy: -64.511433-0.002268j
[2025-09-18 12:03:19] [Iter 1517/2250] R3[466/1200], Temp: 0.6718, Energy: -64.519095-0.002421j
[2025-09-18 12:03:49] [Iter 1518/2250] R3[467/1200], Temp: 0.6706, Energy: -64.504802+0.003184j
[2025-09-18 12:04:18] [Iter 1519/2250] R3[468/1200], Temp: 0.6694, Energy: -64.522763-0.000259j
[2025-09-18 12:04:48] [Iter 1520/2250] R3[469/1200], Temp: 0.6681, Energy: -64.501405-0.000743j
[2025-09-18 12:05:17] [Iter 1521/2250] R3[470/1200], Temp: 0.6669, Energy: -64.510928+0.001100j
[2025-09-18 12:05:47] [Iter 1522/2250] R3[471/1200], Temp: 0.6657, Energy: -64.508851-0.001149j
[2025-09-18 12:06:16] [Iter 1523/2250] R3[472/1200], Temp: 0.6644, Energy: -64.503404-0.000092j
[2025-09-18 12:06:46] [Iter 1524/2250] R3[473/1200], Temp: 0.6632, Energy: -64.511593-0.005472j
[2025-09-18 12:07:15] [Iter 1525/2250] R3[474/1200], Temp: 0.6620, Energy: -64.497517+0.001117j
[2025-09-18 12:07:44] [Iter 1526/2250] R3[475/1200], Temp: 0.6607, Energy: -64.506983+0.002644j
[2025-09-18 12:08:14] [Iter 1527/2250] R3[476/1200], Temp: 0.6595, Energy: -64.501215+0.002030j
[2025-09-18 12:08:43] [Iter 1528/2250] R3[477/1200], Temp: 0.6582, Energy: -64.509770+0.003991j
[2025-09-18 12:09:15] [Iter 1529/2250] R3[478/1200], Temp: 0.6570, Energy: -64.519459+0.000462j
[2025-09-18 12:09:44] [Iter 1530/2250] R3[479/1200], Temp: 0.6558, Energy: -64.520502+0.002362j
[2025-09-18 12:10:14] [Iter 1531/2250] R3[480/1200], Temp: 0.6545, Energy: -64.504743+0.003026j
[2025-09-18 12:10:43] [Iter 1532/2250] R3[481/1200], Temp: 0.6533, Energy: -64.515314-0.000687j
[2025-09-18 12:11:13] [Iter 1533/2250] R3[482/1200], Temp: 0.6520, Energy: -64.504706-0.002354j
[2025-09-18 12:11:42] [Iter 1534/2250] R3[483/1200], Temp: 0.6508, Energy: -64.513921-0.001007j
[2025-09-18 12:12:12] [Iter 1535/2250] R3[484/1200], Temp: 0.6495, Energy: -64.512661-0.000339j
[2025-09-18 12:12:41] [Iter 1536/2250] R3[485/1200], Temp: 0.6483, Energy: -64.518485+0.001877j
[2025-09-18 12:13:11] [Iter 1537/2250] R3[486/1200], Temp: 0.6470, Energy: -64.497464-0.000862j
[2025-09-18 12:13:40] [Iter 1538/2250] R3[487/1200], Temp: 0.6458, Energy: -64.515361-0.004402j
[2025-09-18 12:14:10] [Iter 1539/2250] R3[488/1200], Temp: 0.6445, Energy: -64.508581+0.003378j
[2025-09-18 12:14:39] [Iter 1540/2250] R3[489/1200], Temp: 0.6433, Energy: -64.503644+0.001869j
[2025-09-18 12:15:09] [Iter 1541/2250] R3[490/1200], Temp: 0.6420, Energy: -64.509964+0.001275j
[2025-09-18 12:15:38] [Iter 1542/2250] R3[491/1200], Temp: 0.6408, Energy: -64.509597+0.002099j
[2025-09-18 12:16:08] [Iter 1543/2250] R3[492/1200], Temp: 0.6395, Energy: -64.512775-0.000468j
[2025-09-18 12:16:37] [Iter 1544/2250] R3[493/1200], Temp: 0.6382, Energy: -64.517695+0.000367j
[2025-09-18 12:17:07] [Iter 1545/2250] R3[494/1200], Temp: 0.6370, Energy: -64.504838-0.004905j
[2025-09-18 12:17:36] [Iter 1546/2250] R3[495/1200], Temp: 0.6357, Energy: -64.512514+0.004065j
[2025-09-18 12:18:05] [Iter 1547/2250] R3[496/1200], Temp: 0.6345, Energy: -64.509867+0.002461j
[2025-09-18 12:18:35] [Iter 1548/2250] R3[497/1200], Temp: 0.6332, Energy: -64.508788+0.002634j
[2025-09-18 12:19:04] [Iter 1549/2250] R3[498/1200], Temp: 0.6319, Energy: -64.532966-0.000225j
[2025-09-18 12:19:34] [Iter 1550/2250] R3[499/1200], Temp: 0.6307, Energy: -64.508165+0.000533j
[2025-09-18 12:20:03] [Iter 1551/2250] R3[500/1200], Temp: 0.6294, Energy: -64.506467-0.006366j
[2025-09-18 12:20:33] [Iter 1552/2250] R3[501/1200], Temp: 0.6281, Energy: -64.498568-0.003762j
[2025-09-18 12:21:02] [Iter 1553/2250] R3[502/1200], Temp: 0.6269, Energy: -64.506122-0.003322j
[2025-09-18 12:21:32] [Iter 1554/2250] R3[503/1200], Temp: 0.6256, Energy: -64.526560-0.003127j
[2025-09-18 12:22:01] [Iter 1555/2250] R3[504/1200], Temp: 0.6243, Energy: -64.498868+0.001258j
[2025-09-18 12:22:31] [Iter 1556/2250] R3[505/1200], Temp: 0.6231, Energy: -64.519467-0.003066j
[2025-09-18 12:23:00] [Iter 1557/2250] R3[506/1200], Temp: 0.6218, Energy: -64.515339+0.003365j
[2025-09-18 12:23:30] [Iter 1558/2250] R3[507/1200], Temp: 0.6205, Energy: -64.518691-0.004438j
[2025-09-18 12:23:59] [Iter 1559/2250] R3[508/1200], Temp: 0.6193, Energy: -64.509917+0.002287j
[2025-09-18 12:24:29] [Iter 1560/2250] R3[509/1200], Temp: 0.6180, Energy: -64.521342-0.003266j
[2025-09-18 12:24:58] [Iter 1561/2250] R3[510/1200], Temp: 0.6167, Energy: -64.520818-0.000352j
[2025-09-18 12:25:27] [Iter 1562/2250] R3[511/1200], Temp: 0.6154, Energy: -64.514112+0.004173j
[2025-09-18 12:25:57] [Iter 1563/2250] R3[512/1200], Temp: 0.6142, Energy: -64.513865+0.003053j
[2025-09-18 12:26:26] [Iter 1564/2250] R3[513/1200], Temp: 0.6129, Energy: -64.517002-0.000891j
[2025-09-18 12:26:56] [Iter 1565/2250] R3[514/1200], Temp: 0.6116, Energy: -64.495015-0.000947j
[2025-09-18 12:27:25] [Iter 1566/2250] R3[515/1200], Temp: 0.6103, Energy: -64.522851-0.001011j
[2025-09-18 12:27:55] [Iter 1567/2250] R3[516/1200], Temp: 0.6091, Energy: -64.517590+0.002348j
[2025-09-18 12:28:24] [Iter 1568/2250] R3[517/1200], Temp: 0.6078, Energy: -64.504050-0.002898j
[2025-09-18 12:28:54] [Iter 1569/2250] R3[518/1200], Temp: 0.6065, Energy: -64.501761-0.000476j
[2025-09-18 12:29:23] [Iter 1570/2250] R3[519/1200], Temp: 0.6052, Energy: -64.515252-0.003544j
[2025-09-18 12:29:53] [Iter 1571/2250] R3[520/1200], Temp: 0.6040, Energy: -64.525700+0.001553j
[2025-09-18 12:30:22] [Iter 1572/2250] R3[521/1200], Temp: 0.6027, Energy: -64.514790-0.001244j
[2025-09-18 12:30:52] [Iter 1573/2250] R3[522/1200], Temp: 0.6014, Energy: -64.508775+0.008341j
[2025-09-18 12:31:21] [Iter 1574/2250] R3[523/1200], Temp: 0.6001, Energy: -64.514495+0.004812j
[2025-09-18 12:31:51] [Iter 1575/2250] R3[524/1200], Temp: 0.5988, Energy: -64.521916-0.002134j
[2025-09-18 12:32:20] [Iter 1576/2250] R3[525/1200], Temp: 0.5975, Energy: -64.513393-0.000594j
[2025-09-18 12:32:50] [Iter 1577/2250] R3[526/1200], Temp: 0.5963, Energy: -64.505092-0.003399j
[2025-09-18 12:33:19] [Iter 1578/2250] R3[527/1200], Temp: 0.5950, Energy: -64.505843-0.005794j
[2025-09-18 12:33:48] [Iter 1579/2250] R3[528/1200], Temp: 0.5937, Energy: -64.521264+0.001841j
[2025-09-18 12:34:18] [Iter 1580/2250] R3[529/1200], Temp: 0.5924, Energy: -64.507161+0.002650j
[2025-09-18 12:34:47] [Iter 1581/2250] R3[530/1200], Temp: 0.5911, Energy: -64.531709+0.000334j
[2025-09-18 12:35:17] [Iter 1582/2250] R3[531/1200], Temp: 0.5898, Energy: -64.502304+0.002053j
[2025-09-18 12:35:46] [Iter 1583/2250] R3[532/1200], Temp: 0.5885, Energy: -64.522544+0.003175j
[2025-09-18 12:36:16] [Iter 1584/2250] R3[533/1200], Temp: 0.5873, Energy: -64.483720-0.000237j
[2025-09-18 12:36:45] [Iter 1585/2250] R3[534/1200], Temp: 0.5860, Energy: -64.512536-0.002065j
[2025-09-18 12:37:15] [Iter 1586/2250] R3[535/1200], Temp: 0.5847, Energy: -64.506174+0.002627j
[2025-09-18 12:37:44] [Iter 1587/2250] R3[536/1200], Temp: 0.5834, Energy: -64.506599-0.000421j
[2025-09-18 12:38:14] [Iter 1588/2250] R3[537/1200], Temp: 0.5821, Energy: -64.503448-0.007083j
[2025-09-18 12:38:43] [Iter 1589/2250] R3[538/1200], Temp: 0.5808, Energy: -64.524460+0.000585j
[2025-09-18 12:39:13] [Iter 1590/2250] R3[539/1200], Temp: 0.5795, Energy: -64.503871-0.001463j
[2025-09-18 12:39:42] [Iter 1591/2250] R3[540/1200], Temp: 0.5782, Energy: -64.515978+0.003192j
[2025-09-18 12:40:12] [Iter 1592/2250] R3[541/1200], Temp: 0.5769, Energy: -64.519000+0.000015j
[2025-09-18 12:40:41] [Iter 1593/2250] R3[542/1200], Temp: 0.5756, Energy: -64.518475-0.004108j
[2025-09-18 12:41:10] [Iter 1594/2250] R3[543/1200], Temp: 0.5743, Energy: -64.514436+0.001715j
[2025-09-18 12:41:40] [Iter 1595/2250] R3[544/1200], Temp: 0.5730, Energy: -64.502129+0.002089j
[2025-09-18 12:42:09] [Iter 1596/2250] R3[545/1200], Temp: 0.5717, Energy: -64.507119-0.000014j
[2025-09-18 12:42:39] [Iter 1597/2250] R3[546/1200], Temp: 0.5705, Energy: -64.512944-0.000983j
[2025-09-18 12:43:08] [Iter 1598/2250] R3[547/1200], Temp: 0.5692, Energy: -64.526100-0.002593j
[2025-09-18 12:43:38] [Iter 1599/2250] R3[548/1200], Temp: 0.5679, Energy: -64.523986-0.004449j
[2025-09-18 12:44:07] [Iter 1600/2250] R3[549/1200], Temp: 0.5666, Energy: -64.527485-0.001978j
[2025-09-18 12:44:37] [Iter 1601/2250] R3[550/1200], Temp: 0.5653, Energy: -64.524721+0.001091j
[2025-09-18 12:45:06] [Iter 1602/2250] R3[551/1200], Temp: 0.5640, Energy: -64.515661-0.009584j
[2025-09-18 12:45:36] [Iter 1603/2250] R3[552/1200], Temp: 0.5627, Energy: -64.519962-0.000979j
[2025-09-18 12:46:05] [Iter 1604/2250] R3[553/1200], Temp: 0.5614, Energy: -64.523669+0.001598j
[2025-09-18 12:46:34] [Iter 1605/2250] R3[554/1200], Temp: 0.5601, Energy: -64.518626+0.000200j
[2025-09-18 12:47:04] [Iter 1606/2250] R3[555/1200], Temp: 0.5588, Energy: -64.509678+0.001194j
[2025-09-18 12:47:33] [Iter 1607/2250] R3[556/1200], Temp: 0.5575, Energy: -64.518058-0.001072j
[2025-09-18 12:48:03] [Iter 1608/2250] R3[557/1200], Temp: 0.5562, Energy: -64.502875+0.001470j
[2025-09-18 12:48:32] [Iter 1609/2250] R3[558/1200], Temp: 0.5549, Energy: -64.505845-0.000241j
[2025-09-18 12:49:02] [Iter 1610/2250] R3[559/1200], Temp: 0.5536, Energy: -64.520782-0.002725j
[2025-09-18 12:49:31] [Iter 1611/2250] R3[560/1200], Temp: 0.5523, Energy: -64.509790-0.000903j
[2025-09-18 12:50:01] [Iter 1612/2250] R3[561/1200], Temp: 0.5510, Energy: -64.532910-0.006741j
[2025-09-18 12:50:30] [Iter 1613/2250] R3[562/1200], Temp: 0.5497, Energy: -64.518117-0.001718j
[2025-09-18 12:51:00] [Iter 1614/2250] R3[563/1200], Temp: 0.5484, Energy: -64.517045+0.001422j
[2025-09-18 12:51:29] [Iter 1615/2250] R3[564/1200], Temp: 0.5471, Energy: -64.506315-0.000960j
[2025-09-18 12:51:59] [Iter 1616/2250] R3[565/1200], Temp: 0.5458, Energy: -64.494228+0.006900j
[2025-09-18 12:52:28] [Iter 1617/2250] R3[566/1200], Temp: 0.5444, Energy: -64.491265-0.000975j
[2025-09-18 12:52:58] [Iter 1618/2250] R3[567/1200], Temp: 0.5431, Energy: -64.499276+0.000139j
[2025-09-18 12:53:27] [Iter 1619/2250] R3[568/1200], Temp: 0.5418, Energy: -64.494884+0.001656j
[2025-09-18 12:53:56] [Iter 1620/2250] R3[569/1200], Temp: 0.5405, Energy: -64.508017+0.001605j
[2025-09-18 12:54:26] [Iter 1621/2250] R3[570/1200], Temp: 0.5392, Energy: -64.513129+0.003151j
[2025-09-18 12:54:55] [Iter 1622/2250] R3[571/1200], Temp: 0.5379, Energy: -64.494621-0.006176j
[2025-09-18 12:55:25] [Iter 1623/2250] R3[572/1200], Temp: 0.5366, Energy: -64.509540-0.001277j
[2025-09-18 12:55:54] [Iter 1624/2250] R3[573/1200], Temp: 0.5353, Energy: -64.505147+0.001072j
[2025-09-18 12:56:24] [Iter 1625/2250] R3[574/1200], Temp: 0.5340, Energy: -64.518442-0.005101j
[2025-09-18 12:56:53] [Iter 1626/2250] R3[575/1200], Temp: 0.5327, Energy: -64.497999+0.006011j
[2025-09-18 12:57:23] [Iter 1627/2250] R3[576/1200], Temp: 0.5314, Energy: -64.503963+0.000842j
[2025-09-18 12:57:52] [Iter 1628/2250] R3[577/1200], Temp: 0.5301, Energy: -64.491240+0.001731j
[2025-09-18 12:58:22] [Iter 1629/2250] R3[578/1200], Temp: 0.5288, Energy: -64.505430+0.001419j
[2025-09-18 12:58:51] [Iter 1630/2250] R3[579/1200], Temp: 0.5275, Energy: -64.526678+0.002933j
[2025-09-18 12:59:21] [Iter 1631/2250] R3[580/1200], Temp: 0.5262, Energy: -64.529497-0.001135j
[2025-09-18 12:59:50] [Iter 1632/2250] R3[581/1200], Temp: 0.5249, Energy: -64.521313+0.003107j
[2025-09-18 13:00:19] [Iter 1633/2250] R3[582/1200], Temp: 0.5236, Energy: -64.522045+0.002838j
[2025-09-18 13:00:49] [Iter 1634/2250] R3[583/1200], Temp: 0.5222, Energy: -64.510523+0.001481j
[2025-09-18 13:01:18] [Iter 1635/2250] R3[584/1200], Temp: 0.5209, Energy: -64.523547+0.002870j
[2025-09-18 13:01:48] [Iter 1636/2250] R3[585/1200], Temp: 0.5196, Energy: -64.514957-0.003303j
[2025-09-18 13:02:17] [Iter 1637/2250] R3[586/1200], Temp: 0.5183, Energy: -64.516580+0.002337j
[2025-09-18 13:02:47] [Iter 1638/2250] R3[587/1200], Temp: 0.5170, Energy: -64.516848-0.001097j
[2025-09-18 13:03:16] [Iter 1639/2250] R3[588/1200], Temp: 0.5157, Energy: -64.523297+0.000444j
[2025-09-18 13:03:46] [Iter 1640/2250] R3[589/1200], Temp: 0.5144, Energy: -64.519150+0.001304j
[2025-09-18 13:04:15] [Iter 1641/2250] R3[590/1200], Temp: 0.5131, Energy: -64.529063-0.000035j
[2025-09-18 13:04:45] [Iter 1642/2250] R3[591/1200], Temp: 0.5118, Energy: -64.519757+0.001229j
[2025-09-18 13:05:16] [Iter 1643/2250] R3[592/1200], Temp: 0.5105, Energy: -64.507044+0.000232j
[2025-09-18 13:05:45] [Iter 1644/2250] R3[593/1200], Temp: 0.5092, Energy: -64.534535-0.000874j
[2025-09-18 13:06:15] [Iter 1645/2250] R3[594/1200], Temp: 0.5079, Energy: -64.521745+0.000464j
[2025-09-18 13:06:44] [Iter 1646/2250] R3[595/1200], Temp: 0.5065, Energy: -64.513186-0.000245j
[2025-09-18 13:07:14] [Iter 1647/2250] R3[596/1200], Temp: 0.5052, Energy: -64.516725+0.002206j
[2025-09-18 13:07:43] [Iter 1648/2250] R3[597/1200], Temp: 0.5039, Energy: -64.504581-0.000408j
[2025-09-18 13:08:13] [Iter 1649/2250] R3[598/1200], Temp: 0.5026, Energy: -64.512685-0.001033j
[2025-09-18 13:08:42] [Iter 1650/2250] R3[599/1200], Temp: 0.5013, Energy: -64.521380+0.000453j
[2025-09-18 13:09:12] [Iter 1651/2250] R3[600/1200], Temp: 0.5000, Energy: -64.514544+0.001269j
[2025-09-18 13:09:41] [Iter 1652/2250] R3[601/1200], Temp: 0.4987, Energy: -64.514511-0.005043j
[2025-09-18 13:10:11] [Iter 1653/2250] R3[602/1200], Temp: 0.4974, Energy: -64.522377-0.002369j
[2025-09-18 13:10:40] [Iter 1654/2250] R3[603/1200], Temp: 0.4961, Energy: -64.512165+0.000080j
[2025-09-18 13:11:10] [Iter 1655/2250] R3[604/1200], Temp: 0.4948, Energy: -64.510643-0.000178j
[2025-09-18 13:11:39] [Iter 1656/2250] R3[605/1200], Temp: 0.4935, Energy: -64.529388-0.001355j
[2025-09-18 13:12:09] [Iter 1657/2250] R3[606/1200], Temp: 0.4921, Energy: -64.513828+0.002729j
[2025-09-18 13:12:38] [Iter 1658/2250] R3[607/1200], Temp: 0.4908, Energy: -64.532768-0.003090j
[2025-09-18 13:13:08] [Iter 1659/2250] R3[608/1200], Temp: 0.4895, Energy: -64.518370-0.000805j
[2025-09-18 13:13:37] [Iter 1660/2250] R3[609/1200], Temp: 0.4882, Energy: -64.511446+0.002142j
[2025-09-18 13:14:07] [Iter 1661/2250] R3[610/1200], Temp: 0.4869, Energy: -64.527270+0.002270j
[2025-09-18 13:14:36] [Iter 1662/2250] R3[611/1200], Temp: 0.4856, Energy: -64.510734-0.000583j
[2025-09-18 13:15:05] [Iter 1663/2250] R3[612/1200], Temp: 0.4843, Energy: -64.513711-0.000956j
[2025-09-18 13:15:35] [Iter 1664/2250] R3[613/1200], Temp: 0.4830, Energy: -64.520851+0.004709j
[2025-09-18 13:16:04] [Iter 1665/2250] R3[614/1200], Temp: 0.4817, Energy: -64.522415+0.002034j
[2025-09-18 13:16:34] [Iter 1666/2250] R3[615/1200], Temp: 0.4804, Energy: -64.512599+0.003682j
[2025-09-18 13:17:03] [Iter 1667/2250] R3[616/1200], Temp: 0.4791, Energy: -64.507554+0.000770j
[2025-09-18 13:17:33] [Iter 1668/2250] R3[617/1200], Temp: 0.4778, Energy: -64.518078+0.002879j
[2025-09-18 13:18:02] [Iter 1669/2250] R3[618/1200], Temp: 0.4764, Energy: -64.516381+0.000947j
[2025-09-18 13:18:32] [Iter 1670/2250] R3[619/1200], Temp: 0.4751, Energy: -64.525426-0.003325j
[2025-09-18 13:19:01] [Iter 1671/2250] R3[620/1200], Temp: 0.4738, Energy: -64.521763-0.002689j
[2025-09-18 13:19:31] [Iter 1672/2250] R3[621/1200], Temp: 0.4725, Energy: -64.532003-0.001282j
[2025-09-18 13:20:00] [Iter 1673/2250] R3[622/1200], Temp: 0.4712, Energy: -64.522027+0.000909j
[2025-09-18 13:20:30] [Iter 1674/2250] R3[623/1200], Temp: 0.4699, Energy: -64.530160-0.001768j
[2025-09-18 13:20:59] [Iter 1675/2250] R3[624/1200], Temp: 0.4686, Energy: -64.515104-0.000374j
[2025-09-18 13:21:28] [Iter 1676/2250] R3[625/1200], Temp: 0.4673, Energy: -64.528454-0.002767j
[2025-09-18 13:21:58] [Iter 1677/2250] R3[626/1200], Temp: 0.4660, Energy: -64.524681+0.001837j
[2025-09-18 13:22:27] [Iter 1678/2250] R3[627/1200], Temp: 0.4647, Energy: -64.535976-0.000658j
[2025-09-18 13:22:57] [Iter 1679/2250] R3[628/1200], Temp: 0.4634, Energy: -64.520856-0.001225j
[2025-09-18 13:23:26] [Iter 1680/2250] R3[629/1200], Temp: 0.4621, Energy: -64.522744+0.001819j
[2025-09-18 13:23:56] [Iter 1681/2250] R3[630/1200], Temp: 0.4608, Energy: -64.511243+0.001538j
[2025-09-18 13:24:25] [Iter 1682/2250] R3[631/1200], Temp: 0.4595, Energy: -64.527539+0.005075j
[2025-09-18 13:24:55] [Iter 1683/2250] R3[632/1200], Temp: 0.4582, Energy: -64.516942-0.000228j
[2025-09-18 13:25:24] [Iter 1684/2250] R3[633/1200], Temp: 0.4569, Energy: -64.515623-0.001122j
[2025-09-18 13:25:54] [Iter 1685/2250] R3[634/1200], Temp: 0.4556, Energy: -64.514863-0.001579j
[2025-09-18 13:26:23] [Iter 1686/2250] R3[635/1200], Temp: 0.4542, Energy: -64.532477-0.001335j
[2025-09-18 13:26:53] [Iter 1687/2250] R3[636/1200], Temp: 0.4529, Energy: -64.523496-0.004950j
[2025-09-18 13:27:22] [Iter 1688/2250] R3[637/1200], Temp: 0.4516, Energy: -64.526426+0.001155j
[2025-09-18 13:27:52] [Iter 1689/2250] R3[638/1200], Temp: 0.4503, Energy: -64.528894-0.001677j
[2025-09-18 13:28:21] [Iter 1690/2250] R3[639/1200], Temp: 0.4490, Energy: -64.513545+0.002704j
[2025-09-18 13:28:50] [Iter 1691/2250] R3[640/1200], Temp: 0.4477, Energy: -64.518195-0.001231j
[2025-09-18 13:29:20] [Iter 1692/2250] R3[641/1200], Temp: 0.4464, Energy: -64.533292+0.003383j
[2025-09-18 13:29:49] [Iter 1693/2250] R3[642/1200], Temp: 0.4451, Energy: -64.515779+0.002198j
[2025-09-18 13:30:19] [Iter 1694/2250] R3[643/1200], Temp: 0.4438, Energy: -64.546139-0.004008j
[2025-09-18 13:30:48] [Iter 1695/2250] R3[644/1200], Temp: 0.4425, Energy: -64.525635-0.004332j
[2025-09-18 13:31:18] [Iter 1696/2250] R3[645/1200], Temp: 0.4412, Energy: -64.535821-0.000671j
[2025-09-18 13:31:47] [Iter 1697/2250] R3[646/1200], Temp: 0.4399, Energy: -64.523070+0.000466j
[2025-09-18 13:32:17] [Iter 1698/2250] R3[647/1200], Temp: 0.4386, Energy: -64.527135-0.000983j
[2025-09-18 13:32:46] [Iter 1699/2250] R3[648/1200], Temp: 0.4373, Energy: -64.509882+0.000667j
[2025-09-18 13:33:16] [Iter 1700/2250] R3[649/1200], Temp: 0.4360, Energy: -64.514023+0.003114j
[2025-09-18 13:33:45] [Iter 1701/2250] R3[650/1200], Temp: 0.4347, Energy: -64.507269-0.000573j
[2025-09-18 13:34:14] [Iter 1702/2250] R3[651/1200], Temp: 0.4334, Energy: -64.518933+0.001333j
[2025-09-18 13:34:44] [Iter 1703/2250] R3[652/1200], Temp: 0.4321, Energy: -64.510298-0.000763j
[2025-09-18 13:35:13] [Iter 1704/2250] R3[653/1200], Temp: 0.4308, Energy: -64.518165-0.001043j
[2025-09-18 13:35:43] [Iter 1705/2250] R3[654/1200], Temp: 0.4295, Energy: -64.531517+0.000652j
[2025-09-18 13:36:12] [Iter 1706/2250] R3[655/1200], Temp: 0.4283, Energy: -64.515541-0.002995j
[2025-09-18 13:36:42] [Iter 1707/2250] R3[656/1200], Temp: 0.4270, Energy: -64.518363-0.002202j
[2025-09-18 13:37:11] [Iter 1708/2250] R3[657/1200], Temp: 0.4257, Energy: -64.513086-0.000290j
[2025-09-18 13:37:41] [Iter 1709/2250] R3[658/1200], Temp: 0.4244, Energy: -64.518469+0.000466j
[2025-09-18 13:38:10] [Iter 1710/2250] R3[659/1200], Temp: 0.4231, Energy: -64.514689-0.000996j
[2025-09-18 13:38:40] [Iter 1711/2250] R3[660/1200], Temp: 0.4218, Energy: -64.522071+0.000069j
[2025-09-18 13:39:09] [Iter 1712/2250] R3[661/1200], Temp: 0.4205, Energy: -64.538730-0.003383j
[2025-09-18 13:39:39] [Iter 1713/2250] R3[662/1200], Temp: 0.4192, Energy: -64.522653-0.002296j
[2025-09-18 13:40:08] [Iter 1714/2250] R3[663/1200], Temp: 0.4179, Energy: -64.533120+0.002916j
[2025-09-18 13:40:38] [Iter 1715/2250] R3[664/1200], Temp: 0.4166, Energy: -64.518366+0.000527j
[2025-09-18 13:41:07] [Iter 1716/2250] R3[665/1200], Temp: 0.4153, Energy: -64.522274+0.000644j
[2025-09-18 13:41:37] [Iter 1717/2250] R3[666/1200], Temp: 0.4140, Energy: -64.526243+0.001713j
[2025-09-18 13:42:06] [Iter 1718/2250] R3[667/1200], Temp: 0.4127, Energy: -64.528533+0.004742j
[2025-09-18 13:42:35] [Iter 1719/2250] R3[668/1200], Temp: 0.4115, Energy: -64.521715+0.003137j
[2025-09-18 13:43:05] [Iter 1720/2250] R3[669/1200], Temp: 0.4102, Energy: -64.529971+0.004315j
[2025-09-18 13:43:34] [Iter 1721/2250] R3[670/1200], Temp: 0.4089, Energy: -64.528019+0.000909j
[2025-09-18 13:44:04] [Iter 1722/2250] R3[671/1200], Temp: 0.4076, Energy: -64.517125+0.000132j
[2025-09-18 13:44:33] [Iter 1723/2250] R3[672/1200], Temp: 0.4063, Energy: -64.535046+0.004106j
[2025-09-18 13:45:03] [Iter 1724/2250] R3[673/1200], Temp: 0.4050, Energy: -64.525848+0.000615j
[2025-09-18 13:45:32] [Iter 1725/2250] R3[674/1200], Temp: 0.4037, Energy: -64.515475-0.001245j
[2025-09-18 13:46:02] [Iter 1726/2250] R3[675/1200], Temp: 0.4025, Energy: -64.531579+0.000250j
[2025-09-18 13:46:31] [Iter 1727/2250] R3[676/1200], Temp: 0.4012, Energy: -64.529434+0.004167j
[2025-09-18 13:47:01] [Iter 1728/2250] R3[677/1200], Temp: 0.3999, Energy: -64.523945+0.000175j
[2025-09-18 13:47:30] [Iter 1729/2250] R3[678/1200], Temp: 0.3986, Energy: -64.533045-0.000000j
[2025-09-18 13:48:00] [Iter 1730/2250] R3[679/1200], Temp: 0.3973, Energy: -64.524628-0.001123j
[2025-09-18 13:48:29] [Iter 1731/2250] R3[680/1200], Temp: 0.3960, Energy: -64.526052-0.003207j
[2025-09-18 13:48:59] [Iter 1732/2250] R3[681/1200], Temp: 0.3948, Energy: -64.518398+0.001053j
[2025-09-18 13:49:28] [Iter 1733/2250] R3[682/1200], Temp: 0.3935, Energy: -64.516680+0.002640j
[2025-09-18 13:49:57] [Iter 1734/2250] R3[683/1200], Temp: 0.3922, Energy: -64.519627-0.000408j
[2025-09-18 13:50:27] [Iter 1735/2250] R3[684/1200], Temp: 0.3909, Energy: -64.544682+0.002398j
[2025-09-18 13:50:56] [Iter 1736/2250] R3[685/1200], Temp: 0.3897, Energy: -64.528387+0.000772j
[2025-09-18 13:51:26] [Iter 1737/2250] R3[686/1200], Temp: 0.3884, Energy: -64.530182-0.004441j
[2025-09-18 13:51:55] [Iter 1738/2250] R3[687/1200], Temp: 0.3871, Energy: -64.535733-0.003427j
[2025-09-18 13:52:25] [Iter 1739/2250] R3[688/1200], Temp: 0.3858, Energy: -64.521352-0.001099j
[2025-09-18 13:52:54] [Iter 1740/2250] R3[689/1200], Temp: 0.3846, Energy: -64.521179-0.003053j
[2025-09-18 13:53:24] [Iter 1741/2250] R3[690/1200], Temp: 0.3833, Energy: -64.544364+0.000807j
[2025-09-18 13:53:53] [Iter 1742/2250] R3[691/1200], Temp: 0.3820, Energy: -64.533190+0.001837j
[2025-09-18 13:54:23] [Iter 1743/2250] R3[692/1200], Temp: 0.3807, Energy: -64.530605+0.002418j
[2025-09-18 13:54:52] [Iter 1744/2250] R3[693/1200], Temp: 0.3795, Energy: -64.529958+0.002697j
[2025-09-18 13:55:21] [Iter 1745/2250] R3[694/1200], Temp: 0.3782, Energy: -64.519870+0.002009j
[2025-09-18 13:55:51] [Iter 1746/2250] R3[695/1200], Temp: 0.3769, Energy: -64.538181-0.003255j
[2025-09-18 13:56:20] [Iter 1747/2250] R3[696/1200], Temp: 0.3757, Energy: -64.524118-0.004484j
[2025-09-18 13:56:50] [Iter 1748/2250] R3[697/1200], Temp: 0.3744, Energy: -64.522186+0.001178j
[2025-09-18 13:57:19] [Iter 1749/2250] R3[698/1200], Temp: 0.3731, Energy: -64.533044+0.000393j
[2025-09-18 13:57:49] [Iter 1750/2250] R3[699/1200], Temp: 0.3719, Energy: -64.517467-0.004936j
[2025-09-18 13:57:49] ✓ Checkpoint saved: checkpoint_iter_001750.pkl
[2025-09-18 13:58:18] [Iter 1751/2250] R3[700/1200], Temp: 0.3706, Energy: -64.520411-0.003662j
[2025-09-18 13:58:48] [Iter 1752/2250] R3[701/1200], Temp: 0.3693, Energy: -64.538565-0.000709j
[2025-09-18 13:59:17] [Iter 1753/2250] R3[702/1200], Temp: 0.3681, Energy: -64.511098+0.002598j
[2025-09-18 13:59:47] [Iter 1754/2250] R3[703/1200], Temp: 0.3668, Energy: -64.519064-0.001124j
[2025-09-18 14:00:16] [Iter 1755/2250] R3[704/1200], Temp: 0.3655, Energy: -64.513913+0.000953j
[2025-09-18 14:00:46] [Iter 1756/2250] R3[705/1200], Temp: 0.3643, Energy: -64.534537-0.001093j
[2025-09-18 14:01:15] [Iter 1757/2250] R3[706/1200], Temp: 0.3630, Energy: -64.529485-0.000614j
[2025-09-18 14:01:45] [Iter 1758/2250] R3[707/1200], Temp: 0.3618, Energy: -64.511888+0.002117j
[2025-09-18 14:02:14] [Iter 1759/2250] R3[708/1200], Temp: 0.3605, Energy: -64.510987+0.000461j
[2025-09-18 14:02:44] [Iter 1760/2250] R3[709/1200], Temp: 0.3592, Energy: -64.524358-0.004083j
[2025-09-18 14:03:13] [Iter 1761/2250] R3[710/1200], Temp: 0.3580, Energy: -64.525824+0.004986j
[2025-09-18 14:03:43] [Iter 1762/2250] R3[711/1200], Temp: 0.3567, Energy: -64.533809-0.002233j
[2025-09-18 14:04:12] [Iter 1763/2250] R3[712/1200], Temp: 0.3555, Energy: -64.530520+0.002044j
[2025-09-18 14:04:42] [Iter 1764/2250] R3[713/1200], Temp: 0.3542, Energy: -64.535015+0.001993j
[2025-09-18 14:05:11] [Iter 1765/2250] R3[714/1200], Temp: 0.3530, Energy: -64.519948-0.001317j
[2025-09-18 14:05:41] [Iter 1766/2250] R3[715/1200], Temp: 0.3517, Energy: -64.523304-0.001790j
[2025-09-18 14:06:14] [Iter 1767/2250] R3[716/1200], Temp: 0.3505, Energy: -64.510463+0.001779j
[2025-09-18 14:06:44] [Iter 1768/2250] R3[717/1200], Temp: 0.3492, Energy: -64.529749-0.004272j
[2025-09-18 14:07:13] [Iter 1769/2250] R3[718/1200], Temp: 0.3480, Energy: -64.525558-0.000431j
[2025-09-18 14:07:43] [Iter 1770/2250] R3[719/1200], Temp: 0.3467, Energy: -64.526054-0.000918j
[2025-09-18 14:08:12] [Iter 1771/2250] R3[720/1200], Temp: 0.3455, Energy: -64.521085-0.000964j
[2025-09-18 14:08:42] [Iter 1772/2250] R3[721/1200], Temp: 0.3442, Energy: -64.520051-0.004554j
[2025-09-18 14:09:11] [Iter 1773/2250] R3[722/1200], Temp: 0.3430, Energy: -64.534113-0.000340j
[2025-09-18 14:09:41] [Iter 1774/2250] R3[723/1200], Temp: 0.3418, Energy: -64.528752+0.002814j
[2025-09-18 14:10:10] [Iter 1775/2250] R3[724/1200], Temp: 0.3405, Energy: -64.520064+0.001888j
[2025-09-18 14:10:40] [Iter 1776/2250] R3[725/1200], Temp: 0.3393, Energy: -64.526529-0.003371j
[2025-09-18 14:11:09] [Iter 1777/2250] R3[726/1200], Temp: 0.3380, Energy: -64.517449+0.001368j
[2025-09-18 14:11:39] [Iter 1778/2250] R3[727/1200], Temp: 0.3368, Energy: -64.526533+0.002808j
[2025-09-18 14:12:08] [Iter 1779/2250] R3[728/1200], Temp: 0.3356, Energy: -64.523594+0.002764j
[2025-09-18 14:12:38] [Iter 1780/2250] R3[729/1200], Temp: 0.3343, Energy: -64.516681-0.002943j
[2025-09-18 14:13:08] [Iter 1781/2250] R3[730/1200], Temp: 0.3331, Energy: -64.521254-0.000386j
[2025-09-18 14:13:37] [Iter 1782/2250] R3[731/1200], Temp: 0.3319, Energy: -64.527628-0.002866j
[2025-09-18 14:14:07] [Iter 1783/2250] R3[732/1200], Temp: 0.3306, Energy: -64.521618-0.005295j
[2025-09-18 14:14:36] [Iter 1784/2250] R3[733/1200], Temp: 0.3294, Energy: -64.523688-0.001103j
[2025-09-18 14:15:05] [Iter 1785/2250] R3[734/1200], Temp: 0.3282, Energy: -64.535180-0.000168j
[2025-09-18 14:15:35] [Iter 1786/2250] R3[735/1200], Temp: 0.3269, Energy: -64.524364+0.000720j
[2025-09-18 14:16:05] [Iter 1787/2250] R3[736/1200], Temp: 0.3257, Energy: -64.516236+0.002046j
[2025-09-18 14:16:34] [Iter 1788/2250] R3[737/1200], Temp: 0.3245, Energy: -64.517890+0.002210j
[2025-09-18 14:17:04] [Iter 1789/2250] R3[738/1200], Temp: 0.3233, Energy: -64.527897-0.002803j
[2025-09-18 14:17:33] [Iter 1790/2250] R3[739/1200], Temp: 0.3220, Energy: -64.539168+0.003353j
[2025-09-18 14:18:03] [Iter 1791/2250] R3[740/1200], Temp: 0.3208, Energy: -64.511068+0.002805j
[2025-09-18 14:18:32] [Iter 1792/2250] R3[741/1200], Temp: 0.3196, Energy: -64.513016-0.003490j
[2025-09-18 14:19:02] [Iter 1793/2250] R3[742/1200], Temp: 0.3184, Energy: -64.533765+0.002739j
[2025-09-18 14:19:31] [Iter 1794/2250] R3[743/1200], Temp: 0.3172, Energy: -64.525892+0.003674j
[2025-09-18 14:20:01] [Iter 1795/2250] R3[744/1200], Temp: 0.3159, Energy: -64.529506+0.000403j
[2025-09-18 14:20:30] [Iter 1796/2250] R3[745/1200], Temp: 0.3147, Energy: -64.528910+0.004609j
[2025-09-18 14:21:00] [Iter 1797/2250] R3[746/1200], Temp: 0.3135, Energy: -64.517090+0.001879j
[2025-09-18 14:21:29] [Iter 1798/2250] R3[747/1200], Temp: 0.3123, Energy: -64.521433-0.001225j
[2025-09-18 14:21:59] [Iter 1799/2250] R3[748/1200], Temp: 0.3111, Energy: -64.523173+0.002313j
[2025-09-18 14:22:28] [Iter 1800/2250] R3[749/1200], Temp: 0.3099, Energy: -64.530523+0.000461j
[2025-09-18 14:22:58] [Iter 1801/2250] R3[750/1200], Temp: 0.3087, Energy: -64.535260-0.002474j
[2025-09-18 14:23:27] [Iter 1802/2250] R3[751/1200], Temp: 0.3074, Energy: -64.519385+0.001543j
[2025-09-18 14:23:57] [Iter 1803/2250] R3[752/1200], Temp: 0.3062, Energy: -64.515564-0.003328j
[2025-09-18 14:24:26] [Iter 1804/2250] R3[753/1200], Temp: 0.3050, Energy: -64.531233-0.002799j
[2025-09-18 14:24:56] [Iter 1805/2250] R3[754/1200], Temp: 0.3038, Energy: -64.528702+0.001387j
[2025-09-18 14:25:25] [Iter 1806/2250] R3[755/1200], Temp: 0.3026, Energy: -64.532398-0.003013j
[2025-09-18 14:25:55] [Iter 1807/2250] R3[756/1200], Temp: 0.3014, Energy: -64.544654+0.001776j
[2025-09-18 14:26:24] [Iter 1808/2250] R3[757/1200], Temp: 0.3002, Energy: -64.543107+0.003333j
[2025-09-18 14:26:53] [Iter 1809/2250] R3[758/1200], Temp: 0.2990, Energy: -64.529679-0.002900j
[2025-09-18 14:27:23] [Iter 1810/2250] R3[759/1200], Temp: 0.2978, Energy: -64.546690+0.002243j
[2025-09-18 14:27:52] [Iter 1811/2250] R3[760/1200], Temp: 0.2966, Energy: -64.533127+0.003817j
[2025-09-18 14:28:22] [Iter 1812/2250] R3[761/1200], Temp: 0.2954, Energy: -64.521889+0.001846j
[2025-09-18 14:28:51] [Iter 1813/2250] R3[762/1200], Temp: 0.2942, Energy: -64.516985-0.000513j
[2025-09-18 14:29:21] [Iter 1814/2250] R3[763/1200], Temp: 0.2931, Energy: -64.520360+0.001693j
[2025-09-18 14:29:50] [Iter 1815/2250] R3[764/1200], Temp: 0.2919, Energy: -64.523591+0.002938j
[2025-09-18 14:30:20] [Iter 1816/2250] R3[765/1200], Temp: 0.2907, Energy: -64.536853-0.001783j
[2025-09-18 14:30:49] [Iter 1817/2250] R3[766/1200], Temp: 0.2895, Energy: -64.530716+0.002335j
[2025-09-18 14:31:19] [Iter 1818/2250] R3[767/1200], Temp: 0.2883, Energy: -64.525355-0.000825j
[2025-09-18 14:31:48] [Iter 1819/2250] R3[768/1200], Temp: 0.2871, Energy: -64.527935+0.002175j
[2025-09-18 14:32:18] [Iter 1820/2250] R3[769/1200], Temp: 0.2859, Energy: -64.513676-0.002505j
[2025-09-18 14:32:47] [Iter 1821/2250] R3[770/1200], Temp: 0.2847, Energy: -64.522535-0.004405j
[2025-09-18 14:33:17] [Iter 1822/2250] R3[771/1200], Temp: 0.2836, Energy: -64.534845-0.000471j
[2025-09-18 14:33:46] [Iter 1823/2250] R3[772/1200], Temp: 0.2824, Energy: -64.524818+0.002187j
[2025-09-18 14:34:15] [Iter 1824/2250] R3[773/1200], Temp: 0.2812, Energy: -64.528657+0.003976j
[2025-09-18 14:34:45] [Iter 1825/2250] R3[774/1200], Temp: 0.2800, Energy: -64.531687-0.002430j
[2025-09-18 14:35:14] [Iter 1826/2250] R3[775/1200], Temp: 0.2789, Energy: -64.534272+0.002803j
[2025-09-18 14:35:44] [Iter 1827/2250] R3[776/1200], Temp: 0.2777, Energy: -64.532810-0.001206j
[2025-09-18 14:36:13] [Iter 1828/2250] R3[777/1200], Temp: 0.2765, Energy: -64.523687-0.000231j
[2025-09-18 14:36:43] [Iter 1829/2250] R3[778/1200], Temp: 0.2753, Energy: -64.535090-0.001927j
[2025-09-18 14:37:12] [Iter 1830/2250] R3[779/1200], Temp: 0.2742, Energy: -64.530676+0.001574j
[2025-09-18 14:37:42] [Iter 1831/2250] R3[780/1200], Temp: 0.2730, Energy: -64.523260-0.001172j
[2025-09-18 14:38:11] [Iter 1832/2250] R3[781/1200], Temp: 0.2718, Energy: -64.518958+0.001457j
[2025-09-18 14:38:41] [Iter 1833/2250] R3[782/1200], Temp: 0.2707, Energy: -64.526164-0.001138j
[2025-09-18 14:39:10] [Iter 1834/2250] R3[783/1200], Temp: 0.2695, Energy: -64.533814-0.001902j
[2025-09-18 14:39:40] [Iter 1835/2250] R3[784/1200], Temp: 0.2684, Energy: -64.528474-0.006025j
[2025-09-18 14:40:09] [Iter 1836/2250] R3[785/1200], Temp: 0.2672, Energy: -64.519578+0.000046j
[2025-09-18 14:40:39] [Iter 1837/2250] R3[786/1200], Temp: 0.2660, Energy: -64.532895+0.000650j
[2025-09-18 14:41:08] [Iter 1838/2250] R3[787/1200], Temp: 0.2649, Energy: -64.523110-0.001221j
[2025-09-18 14:41:38] [Iter 1839/2250] R3[788/1200], Temp: 0.2637, Energy: -64.515321+0.004472j
[2025-09-18 14:42:07] [Iter 1840/2250] R3[789/1200], Temp: 0.2626, Energy: -64.512732+0.002863j
[2025-09-18 14:42:36] [Iter 1841/2250] R3[790/1200], Temp: 0.2614, Energy: -64.532483-0.002506j
[2025-09-18 14:43:06] [Iter 1842/2250] R3[791/1200], Temp: 0.2603, Energy: -64.518597+0.003539j
[2025-09-18 14:43:35] [Iter 1843/2250] R3[792/1200], Temp: 0.2591, Energy: -64.531098-0.001113j
[2025-09-18 14:44:05] [Iter 1844/2250] R3[793/1200], Temp: 0.2580, Energy: -64.524201-0.001567j
[2025-09-18 14:44:34] [Iter 1845/2250] R3[794/1200], Temp: 0.2568, Energy: -64.528531-0.000136j
[2025-09-18 14:45:04] [Iter 1846/2250] R3[795/1200], Temp: 0.2557, Energy: -64.526778-0.001982j
[2025-09-18 14:45:33] [Iter 1847/2250] R3[796/1200], Temp: 0.2545, Energy: -64.533015+0.004606j
[2025-09-18 14:46:03] [Iter 1848/2250] R3[797/1200], Temp: 0.2534, Energy: -64.524021-0.007827j
[2025-09-18 14:46:32] [Iter 1849/2250] R3[798/1200], Temp: 0.2523, Energy: -64.522331+0.009256j
[2025-09-18 14:47:02] [Iter 1850/2250] R3[799/1200], Temp: 0.2511, Energy: -64.540236-0.004362j
[2025-09-18 14:47:31] [Iter 1851/2250] R3[800/1200], Temp: 0.2500, Energy: -64.522824+0.003222j
[2025-09-18 14:48:01] [Iter 1852/2250] R3[801/1200], Temp: 0.2489, Energy: -64.518147+0.002195j
[2025-09-18 14:48:30] [Iter 1853/2250] R3[802/1200], Temp: 0.2477, Energy: -64.524464+0.002040j
[2025-09-18 14:49:00] [Iter 1854/2250] R3[803/1200], Temp: 0.2466, Energy: -64.528926+0.001448j
[2025-09-18 14:49:29] [Iter 1855/2250] R3[804/1200], Temp: 0.2455, Energy: -64.535377-0.001664j
[2025-09-18 14:49:58] [Iter 1856/2250] R3[805/1200], Temp: 0.2444, Energy: -64.518512-0.001933j
[2025-09-18 14:50:28] [Iter 1857/2250] R3[806/1200], Temp: 0.2432, Energy: -64.524745-0.001500j
[2025-09-18 14:50:58] [Iter 1858/2250] R3[807/1200], Temp: 0.2421, Energy: -64.534279+0.002996j
[2025-09-18 14:51:27] [Iter 1859/2250] R3[808/1200], Temp: 0.2410, Energy: -64.541925+0.001859j
[2025-09-18 14:51:57] [Iter 1860/2250] R3[809/1200], Temp: 0.2399, Energy: -64.520028-0.005390j
[2025-09-18 14:52:26] [Iter 1861/2250] R3[810/1200], Temp: 0.2388, Energy: -64.524340+0.001339j
[2025-09-18 14:52:56] [Iter 1862/2250] R3[811/1200], Temp: 0.2376, Energy: -64.513739+0.000666j
[2025-09-18 14:53:25] [Iter 1863/2250] R3[812/1200], Temp: 0.2365, Energy: -64.526664-0.002433j
[2025-09-18 14:53:55] [Iter 1864/2250] R3[813/1200], Temp: 0.2354, Energy: -64.533097-0.000299j
[2025-09-18 14:54:24] [Iter 1865/2250] R3[814/1200], Temp: 0.2343, Energy: -64.529371-0.000413j
[2025-09-18 14:54:54] [Iter 1866/2250] R3[815/1200], Temp: 0.2332, Energy: -64.528409+0.002968j
[2025-09-18 14:55:23] [Iter 1867/2250] R3[816/1200], Temp: 0.2321, Energy: -64.533152-0.003680j
[2025-09-18 14:55:53] [Iter 1868/2250] R3[817/1200], Temp: 0.2310, Energy: -64.521530-0.001964j
[2025-09-18 14:56:22] [Iter 1869/2250] R3[818/1200], Temp: 0.2299, Energy: -64.526724-0.000329j
[2025-09-18 14:56:52] [Iter 1870/2250] R3[819/1200], Temp: 0.2288, Energy: -64.517891-0.001490j
[2025-09-18 14:57:21] [Iter 1871/2250] R3[820/1200], Temp: 0.2277, Energy: -64.528121-0.003821j
[2025-09-18 14:57:50] [Iter 1872/2250] R3[821/1200], Temp: 0.2266, Energy: -64.523242+0.003199j
[2025-09-18 14:58:20] [Iter 1873/2250] R3[822/1200], Temp: 0.2255, Energy: -64.520534+0.000908j
[2025-09-18 14:58:49] [Iter 1874/2250] R3[823/1200], Temp: 0.2244, Energy: -64.520866+0.003924j
[2025-09-18 14:59:19] [Iter 1875/2250] R3[824/1200], Temp: 0.2233, Energy: -64.530940+0.002599j
[2025-09-18 14:59:48] [Iter 1876/2250] R3[825/1200], Temp: 0.2222, Energy: -64.520929+0.003150j
[2025-09-18 15:00:18] [Iter 1877/2250] R3[826/1200], Temp: 0.2211, Energy: -64.529226-0.005326j
[2025-09-18 15:00:47] [Iter 1878/2250] R3[827/1200], Temp: 0.2200, Energy: -64.560890+0.001842j
[2025-09-18 15:01:17] [Iter 1879/2250] R3[828/1200], Temp: 0.2190, Energy: -64.518554+0.000715j
[2025-09-18 15:01:46] [Iter 1880/2250] R3[829/1200], Temp: 0.2179, Energy: -64.520144+0.000369j
[2025-09-18 15:02:16] [Iter 1881/2250] R3[830/1200], Temp: 0.2168, Energy: -64.536332+0.000420j
[2025-09-18 15:02:45] [Iter 1882/2250] R3[831/1200], Temp: 0.2157, Energy: -64.536346-0.000334j
[2025-09-18 15:03:15] [Iter 1883/2250] R3[832/1200], Temp: 0.2146, Energy: -64.527861-0.000529j
[2025-09-18 15:03:44] [Iter 1884/2250] R3[833/1200], Temp: 0.2136, Energy: -64.525392-0.001285j
[2025-09-18 15:04:14] [Iter 1885/2250] R3[834/1200], Temp: 0.2125, Energy: -64.524712-0.003840j
[2025-09-18 15:04:43] [Iter 1886/2250] R3[835/1200], Temp: 0.2114, Energy: -64.533101+0.001907j
[2025-09-18 15:05:13] [Iter 1887/2250] R3[836/1200], Temp: 0.2104, Energy: -64.539830+0.000548j
[2025-09-18 15:05:42] [Iter 1888/2250] R3[837/1200], Temp: 0.2093, Energy: -64.525526-0.004073j
[2025-09-18 15:06:12] [Iter 1889/2250] R3[838/1200], Temp: 0.2082, Energy: -64.523067+0.001163j
[2025-09-18 15:06:41] [Iter 1890/2250] R3[839/1200], Temp: 0.2072, Energy: -64.544384-0.001104j
[2025-09-18 15:07:11] [Iter 1891/2250] R3[840/1200], Temp: 0.2061, Energy: -64.540311+0.001224j
[2025-09-18 15:07:40] [Iter 1892/2250] R3[841/1200], Temp: 0.2050, Energy: -64.536382+0.001624j
[2025-09-18 15:08:09] [Iter 1893/2250] R3[842/1200], Temp: 0.2040, Energy: -64.522191+0.000982j
[2025-09-18 15:08:39] [Iter 1894/2250] R3[843/1200], Temp: 0.2029, Energy: -64.519534-0.001691j
[2025-09-18 15:09:08] [Iter 1895/2250] R3[844/1200], Temp: 0.2019, Energy: -64.530072-0.000361j
[2025-09-18 15:09:38] [Iter 1896/2250] R3[845/1200], Temp: 0.2008, Energy: -64.536816+0.001650j
[2025-09-18 15:10:07] [Iter 1897/2250] R3[846/1200], Temp: 0.1998, Energy: -64.529611+0.000424j
[2025-09-18 15:10:37] [Iter 1898/2250] R3[847/1200], Temp: 0.1987, Energy: -64.538952-0.001737j
[2025-09-18 15:11:06] [Iter 1899/2250] R3[848/1200], Temp: 0.1977, Energy: -64.541219-0.004092j
[2025-09-18 15:11:36] [Iter 1900/2250] R3[849/1200], Temp: 0.1967, Energy: -64.524258+0.000491j
[2025-09-18 15:12:05] [Iter 1901/2250] R3[850/1200], Temp: 0.1956, Energy: -64.523016-0.003815j
[2025-09-18 15:12:35] [Iter 1902/2250] R3[851/1200], Temp: 0.1946, Energy: -64.526779-0.000441j
[2025-09-18 15:13:04] [Iter 1903/2250] R3[852/1200], Temp: 0.1935, Energy: -64.528155-0.002913j
[2025-09-18 15:13:34] [Iter 1904/2250] R3[853/1200], Temp: 0.1925, Energy: -64.523716+0.001213j
[2025-09-18 15:14:03] [Iter 1905/2250] R3[854/1200], Temp: 0.1915, Energy: -64.533776-0.000393j
[2025-09-18 15:14:33] [Iter 1906/2250] R3[855/1200], Temp: 0.1905, Energy: -64.528747-0.001928j
[2025-09-18 15:15:02] [Iter 1907/2250] R3[856/1200], Temp: 0.1894, Energy: -64.525916-0.002201j
[2025-09-18 15:15:32] [Iter 1908/2250] R3[857/1200], Temp: 0.1884, Energy: -64.506873-0.000321j
[2025-09-18 15:16:01] [Iter 1909/2250] R3[858/1200], Temp: 0.1874, Energy: -64.517583-0.001151j
[2025-09-18 15:16:31] [Iter 1910/2250] R3[859/1200], Temp: 0.1864, Energy: -64.524811+0.001026j
[2025-09-18 15:17:00] [Iter 1911/2250] R3[860/1200], Temp: 0.1853, Energy: -64.513763+0.002570j
[2025-09-18 15:17:29] [Iter 1912/2250] R3[861/1200], Temp: 0.1843, Energy: -64.513701+0.001401j
[2025-09-18 15:17:59] [Iter 1913/2250] R3[862/1200], Temp: 0.1833, Energy: -64.500541+0.000722j
[2025-09-18 15:18:28] [Iter 1914/2250] R3[863/1200], Temp: 0.1823, Energy: -64.514467-0.000365j
[2025-09-18 15:18:58] [Iter 1915/2250] R3[864/1200], Temp: 0.1813, Energy: -64.506080-0.000421j
[2025-09-18 15:19:27] [Iter 1916/2250] R3[865/1200], Temp: 0.1803, Energy: -64.528699+0.001034j
[2025-09-18 15:19:57] [Iter 1917/2250] R3[866/1200], Temp: 0.1793, Energy: -64.523166+0.001660j
[2025-09-18 15:20:26] [Iter 1918/2250] R3[867/1200], Temp: 0.1783, Energy: -64.521018-0.005970j
[2025-09-18 15:20:56] [Iter 1919/2250] R3[868/1200], Temp: 0.1773, Energy: -64.530875+0.002802j
[2025-09-18 15:21:25] [Iter 1920/2250] R3[869/1200], Temp: 0.1763, Energy: -64.531525-0.000061j
[2025-09-18 15:21:55] [Iter 1921/2250] R3[870/1200], Temp: 0.1753, Energy: -64.519644-0.001635j
[2025-09-18 15:22:24] [Iter 1922/2250] R3[871/1200], Temp: 0.1743, Energy: -64.507492+0.003704j
[2025-09-18 15:22:54] [Iter 1923/2250] R3[872/1200], Temp: 0.1733, Energy: -64.532510-0.000535j
[2025-09-18 15:23:23] [Iter 1924/2250] R3[873/1200], Temp: 0.1723, Energy: -64.518033+0.000337j
[2025-09-18 15:23:53] [Iter 1925/2250] R3[874/1200], Temp: 0.1713, Energy: -64.538360+0.002331j
[2025-09-18 15:24:22] [Iter 1926/2250] R3[875/1200], Temp: 0.1703, Energy: -64.528761-0.003607j
[2025-09-18 15:24:52] [Iter 1927/2250] R3[876/1200], Temp: 0.1693, Energy: -64.531232-0.002376j
[2025-09-18 15:25:21] [Iter 1928/2250] R3[877/1200], Temp: 0.1684, Energy: -64.522219+0.001040j
[2025-09-18 15:25:51] [Iter 1929/2250] R3[878/1200], Temp: 0.1674, Energy: -64.535370+0.003161j
[2025-09-18 15:26:20] [Iter 1930/2250] R3[879/1200], Temp: 0.1664, Energy: -64.540788-0.003763j
[2025-09-18 15:26:50] [Iter 1931/2250] R3[880/1200], Temp: 0.1654, Energy: -64.530425+0.001797j
[2025-09-18 15:27:19] [Iter 1932/2250] R3[881/1200], Temp: 0.1645, Energy: -64.528946+0.001983j
[2025-09-18 15:27:49] [Iter 1933/2250] R3[882/1200], Temp: 0.1635, Energy: -64.546169+0.000243j
[2025-09-18 15:28:18] [Iter 1934/2250] R3[883/1200], Temp: 0.1625, Energy: -64.542419+0.001592j
[2025-09-18 15:28:47] [Iter 1935/2250] R3[884/1200], Temp: 0.1616, Energy: -64.528954-0.009139j
[2025-09-18 15:29:17] [Iter 1936/2250] R3[885/1200], Temp: 0.1606, Energy: -64.534796+0.000418j
[2025-09-18 15:29:46] [Iter 1937/2250] R3[886/1200], Temp: 0.1596, Energy: -64.531987+0.002966j
[2025-09-18 15:30:16] [Iter 1938/2250] R3[887/1200], Temp: 0.1587, Energy: -64.535222+0.002638j
[2025-09-18 15:30:45] [Iter 1939/2250] R3[888/1200], Temp: 0.1577, Energy: -64.521126+0.000754j
[2025-09-18 15:31:15] [Iter 1940/2250] R3[889/1200], Temp: 0.1568, Energy: -64.536254+0.001083j
[2025-09-18 15:31:44] [Iter 1941/2250] R3[890/1200], Temp: 0.1558, Energy: -64.541289+0.000696j
[2025-09-18 15:32:14] [Iter 1942/2250] R3[891/1200], Temp: 0.1549, Energy: -64.536153-0.002131j
[2025-09-18 15:32:43] [Iter 1943/2250] R3[892/1200], Temp: 0.1539, Energy: -64.538683+0.001235j
[2025-09-18 15:33:13] [Iter 1944/2250] R3[893/1200], Temp: 0.1530, Energy: -64.529470-0.000459j
[2025-09-18 15:33:42] [Iter 1945/2250] R3[894/1200], Temp: 0.1520, Energy: -64.522672-0.002076j
[2025-09-18 15:34:12] [Iter 1946/2250] R3[895/1200], Temp: 0.1511, Energy: -64.518860+0.001576j
[2025-09-18 15:34:41] [Iter 1947/2250] R3[896/1200], Temp: 0.1502, Energy: -64.537452+0.004867j
[2025-09-18 15:35:11] [Iter 1948/2250] R3[897/1200], Temp: 0.1492, Energy: -64.533326+0.001748j
[2025-09-18 15:35:40] [Iter 1949/2250] R3[898/1200], Temp: 0.1483, Energy: -64.536951+0.004118j
[2025-09-18 15:36:10] [Iter 1950/2250] R3[899/1200], Temp: 0.1474, Energy: -64.544297+0.000118j
[2025-09-18 15:36:39] [Iter 1951/2250] R3[900/1200], Temp: 0.1464, Energy: -64.548430-0.001438j
[2025-09-18 15:37:09] [Iter 1952/2250] R3[901/1200], Temp: 0.1455, Energy: -64.543590-0.002923j
[2025-09-18 15:37:38] [Iter 1953/2250] R3[902/1200], Temp: 0.1446, Energy: -64.546578-0.000735j
[2025-09-18 15:38:07] [Iter 1954/2250] R3[903/1200], Temp: 0.1437, Energy: -64.517653-0.000898j
[2025-09-18 15:38:37] [Iter 1955/2250] R3[904/1200], Temp: 0.1428, Energy: -64.547400+0.001714j
[2025-09-18 15:39:06] [Iter 1956/2250] R3[905/1200], Temp: 0.1418, Energy: -64.540412-0.002844j
[2025-09-18 15:39:36] [Iter 1957/2250] R3[906/1200], Temp: 0.1409, Energy: -64.528224+0.001901j
[2025-09-18 15:40:05] [Iter 1958/2250] R3[907/1200], Temp: 0.1400, Energy: -64.545131+0.001715j
[2025-09-18 15:40:35] [Iter 1959/2250] R3[908/1200], Temp: 0.1391, Energy: -64.533585-0.000888j
[2025-09-18 15:41:04] [Iter 1960/2250] R3[909/1200], Temp: 0.1382, Energy: -64.529838+0.002780j
[2025-09-18 15:41:34] [Iter 1961/2250] R3[910/1200], Temp: 0.1373, Energy: -64.524445+0.001039j
[2025-09-18 15:42:03] [Iter 1962/2250] R3[911/1200], Temp: 0.1364, Energy: -64.537899+0.001770j
[2025-09-18 15:42:33] [Iter 1963/2250] R3[912/1200], Temp: 0.1355, Energy: -64.540548+0.000531j
[2025-09-18 15:43:02] [Iter 1964/2250] R3[913/1200], Temp: 0.1346, Energy: -64.553173-0.005257j
[2025-09-18 15:43:32] [Iter 1965/2250] R3[914/1200], Temp: 0.1337, Energy: -64.540422-0.000516j
[2025-09-18 15:44:01] [Iter 1966/2250] R3[915/1200], Temp: 0.1328, Energy: -64.537294+0.001720j
[2025-09-18 15:44:31] [Iter 1967/2250] R3[916/1200], Temp: 0.1320, Energy: -64.542462-0.002849j
[2025-09-18 15:45:00] [Iter 1968/2250] R3[917/1200], Temp: 0.1311, Energy: -64.524972+0.003226j
[2025-09-18 15:45:30] [Iter 1969/2250] R3[918/1200], Temp: 0.1302, Energy: -64.545396+0.001047j
[2025-09-18 15:45:59] [Iter 1970/2250] R3[919/1200], Temp: 0.1293, Energy: -64.533714+0.000761j
[2025-09-18 15:46:29] [Iter 1971/2250] R3[920/1200], Temp: 0.1284, Energy: -64.524559-0.002718j
[2025-09-18 15:46:58] [Iter 1972/2250] R3[921/1200], Temp: 0.1276, Energy: -64.529981+0.001247j
[2025-09-18 15:47:28] [Iter 1973/2250] R3[922/1200], Temp: 0.1267, Energy: -64.526050-0.002235j
[2025-09-18 15:47:57] [Iter 1974/2250] R3[923/1200], Temp: 0.1258, Energy: -64.534780-0.000947j
[2025-09-18 15:48:27] [Iter 1975/2250] R3[924/1200], Temp: 0.1249, Energy: -64.541026+0.001566j
[2025-09-18 15:48:56] [Iter 1976/2250] R3[925/1200], Temp: 0.1241, Energy: -64.516452+0.000221j
[2025-09-18 15:49:26] [Iter 1977/2250] R3[926/1200], Temp: 0.1232, Energy: -64.543611-0.003056j
[2025-09-18 15:49:55] [Iter 1978/2250] R3[927/1200], Temp: 0.1224, Energy: -64.517870-0.001197j
[2025-09-18 15:50:25] [Iter 1979/2250] R3[928/1200], Temp: 0.1215, Energy: -64.535021+0.005360j
[2025-09-18 15:50:54] [Iter 1980/2250] R3[929/1200], Temp: 0.1206, Energy: -64.527730+0.003084j
[2025-09-18 15:51:24] [Iter 1981/2250] R3[930/1200], Temp: 0.1198, Energy: -64.524851-0.001331j
[2025-09-18 15:51:53] [Iter 1982/2250] R3[931/1200], Temp: 0.1189, Energy: -64.534782-0.000808j
[2025-09-18 15:52:22] [Iter 1983/2250] R3[932/1200], Temp: 0.1181, Energy: -64.529985-0.002479j
[2025-09-18 15:52:52] [Iter 1984/2250] R3[933/1200], Temp: 0.1173, Energy: -64.525449+0.000787j
[2025-09-18 15:53:21] [Iter 1985/2250] R3[934/1200], Temp: 0.1164, Energy: -64.538051-0.000984j
[2025-09-18 15:53:51] [Iter 1986/2250] R3[935/1200], Temp: 0.1156, Energy: -64.532804-0.000666j
[2025-09-18 15:54:20] [Iter 1987/2250] R3[936/1200], Temp: 0.1147, Energy: -64.528319-0.001901j
[2025-09-18 15:54:50] [Iter 1988/2250] R3[937/1200], Temp: 0.1139, Energy: -64.545222+0.001228j
[2025-09-18 15:55:19] [Iter 1989/2250] R3[938/1200], Temp: 0.1131, Energy: -64.543456-0.002829j
[2025-09-18 15:55:49] [Iter 1990/2250] R3[939/1200], Temp: 0.1123, Energy: -64.524476-0.001035j
[2025-09-18 15:56:18] [Iter 1991/2250] R3[940/1200], Temp: 0.1114, Energy: -64.531193+0.000916j
[2025-09-18 15:56:48] [Iter 1992/2250] R3[941/1200], Temp: 0.1106, Energy: -64.531161+0.003721j
[2025-09-18 15:57:17] [Iter 1993/2250] R3[942/1200], Temp: 0.1098, Energy: -64.529933+0.000323j
[2025-09-18 15:57:47] [Iter 1994/2250] R3[943/1200], Temp: 0.1090, Energy: -64.537037+0.003203j
[2025-09-18 15:58:16] [Iter 1995/2250] R3[944/1200], Temp: 0.1082, Energy: -64.513622+0.001133j
[2025-09-18 15:58:46] [Iter 1996/2250] R3[945/1200], Temp: 0.1073, Energy: -64.528019+0.003024j
[2025-09-18 15:59:15] [Iter 1997/2250] R3[946/1200], Temp: 0.1065, Energy: -64.526211-0.000035j
[2025-09-18 15:59:45] [Iter 1998/2250] R3[947/1200], Temp: 0.1057, Energy: -64.534065+0.001891j
[2025-09-18 16:00:14] [Iter 1999/2250] R3[948/1200], Temp: 0.1049, Energy: -64.533492+0.001380j
[2025-09-18 16:00:43] [Iter 2000/2250] R3[949/1200], Temp: 0.1041, Energy: -64.524161-0.002266j
[2025-09-18 16:00:44] ✓ Checkpoint saved: checkpoint_iter_002000.pkl
[2025-09-18 16:01:13] [Iter 2001/2250] R3[950/1200], Temp: 0.1033, Energy: -64.531292+0.000560j
[2025-09-18 16:01:42] [Iter 2002/2250] R3[951/1200], Temp: 0.1025, Energy: -64.529482+0.000965j
[2025-09-18 16:02:12] [Iter 2003/2250] R3[952/1200], Temp: 0.1017, Energy: -64.538296+0.000979j
[2025-09-18 16:02:41] [Iter 2004/2250] R3[953/1200], Temp: 0.1009, Energy: -64.526473-0.001324j
[2025-09-18 16:03:11] [Iter 2005/2250] R3[954/1200], Temp: 0.1002, Energy: -64.542678-0.002277j
[2025-09-18 16:03:40] [Iter 2006/2250] R3[955/1200], Temp: 0.0994, Energy: -64.529046+0.002799j
[2025-09-18 16:04:10] [Iter 2007/2250] R3[956/1200], Temp: 0.0986, Energy: -64.535199+0.001111j
[2025-09-18 16:04:39] [Iter 2008/2250] R3[957/1200], Temp: 0.0978, Energy: -64.539403+0.005281j
[2025-09-18 16:05:09] [Iter 2009/2250] R3[958/1200], Temp: 0.0970, Energy: -64.528537+0.001608j
[2025-09-18 16:05:38] [Iter 2010/2250] R3[959/1200], Temp: 0.0963, Energy: -64.543629+0.000503j
[2025-09-18 16:06:08] [Iter 2011/2250] R3[960/1200], Temp: 0.0955, Energy: -64.524666+0.005538j
[2025-09-18 16:06:37] [Iter 2012/2250] R3[961/1200], Temp: 0.0947, Energy: -64.543264-0.002101j
[2025-09-18 16:07:07] [Iter 2013/2250] R3[962/1200], Temp: 0.0940, Energy: -64.521720-0.001320j
[2025-09-18 16:07:36] [Iter 2014/2250] R3[963/1200], Temp: 0.0932, Energy: -64.530656-0.001663j
[2025-09-18 16:08:06] [Iter 2015/2250] R3[964/1200], Temp: 0.0924, Energy: -64.533959+0.000604j
[2025-09-18 16:08:35] [Iter 2016/2250] R3[965/1200], Temp: 0.0917, Energy: -64.542996-0.003686j
[2025-09-18 16:09:05] [Iter 2017/2250] R3[966/1200], Temp: 0.0909, Energy: -64.549020+0.000402j
[2025-09-18 16:09:34] [Iter 2018/2250] R3[967/1200], Temp: 0.0902, Energy: -64.535266-0.004280j
[2025-09-18 16:10:04] [Iter 2019/2250] R3[968/1200], Temp: 0.0894, Energy: -64.539586+0.000056j
[2025-09-18 16:10:33] [Iter 2020/2250] R3[969/1200], Temp: 0.0887, Energy: -64.535715-0.002028j
[2025-09-18 16:11:02] [Iter 2021/2250] R3[970/1200], Temp: 0.0879, Energy: -64.555424+0.002155j
[2025-09-18 16:11:32] [Iter 2022/2250] R3[971/1200], Temp: 0.0872, Energy: -64.542236+0.003095j
[2025-09-18 16:12:01] [Iter 2023/2250] R3[972/1200], Temp: 0.0865, Energy: -64.548909-0.001464j
[2025-09-18 16:12:31] [Iter 2024/2250] R3[973/1200], Temp: 0.0857, Energy: -64.535862-0.001392j
[2025-09-18 16:13:00] [Iter 2025/2250] R3[974/1200], Temp: 0.0850, Energy: -64.527482+0.001531j
[2025-09-18 16:13:30] [Iter 2026/2250] R3[975/1200], Temp: 0.0843, Energy: -64.543141+0.001146j
[2025-09-18 16:13:59] [Iter 2027/2250] R3[976/1200], Temp: 0.0835, Energy: -64.551682+0.000864j
[2025-09-18 16:14:29] [Iter 2028/2250] R3[977/1200], Temp: 0.0828, Energy: -64.517322-0.001401j
[2025-09-18 16:14:58] [Iter 2029/2250] R3[978/1200], Temp: 0.0821, Energy: -64.540185+0.001990j
[2025-09-18 16:15:28] [Iter 2030/2250] R3[979/1200], Temp: 0.0814, Energy: -64.535229-0.000559j
[2025-09-18 16:15:57] [Iter 2031/2250] R3[980/1200], Temp: 0.0807, Energy: -64.542671+0.000274j
[2025-09-18 16:16:27] [Iter 2032/2250] R3[981/1200], Temp: 0.0800, Energy: -64.545702+0.000505j
[2025-09-18 16:16:56] [Iter 2033/2250] R3[982/1200], Temp: 0.0792, Energy: -64.533446+0.001767j
[2025-09-18 16:17:26] [Iter 2034/2250] R3[983/1200], Temp: 0.0785, Energy: -64.539959-0.001967j
[2025-09-18 16:17:55] [Iter 2035/2250] R3[984/1200], Temp: 0.0778, Energy: -64.536107+0.002321j
[2025-09-18 16:18:25] [Iter 2036/2250] R3[985/1200], Temp: 0.0771, Energy: -64.547859-0.000013j
[2025-09-18 16:18:54] [Iter 2037/2250] R3[986/1200], Temp: 0.0764, Energy: -64.536185-0.001683j
[2025-09-18 16:19:23] [Iter 2038/2250] R3[987/1200], Temp: 0.0757, Energy: -64.528383-0.001170j
[2025-09-18 16:19:53] [Iter 2039/2250] R3[988/1200], Temp: 0.0751, Energy: -64.546898-0.001455j
[2025-09-18 16:20:22] [Iter 2040/2250] R3[989/1200], Temp: 0.0744, Energy: -64.534657+0.000607j
[2025-09-18 16:20:52] [Iter 2041/2250] R3[990/1200], Temp: 0.0737, Energy: -64.555979+0.004442j
[2025-09-18 16:21:21] [Iter 2042/2250] R3[991/1200], Temp: 0.0730, Energy: -64.547121-0.000575j
[2025-09-18 16:21:51] [Iter 2043/2250] R3[992/1200], Temp: 0.0723, Energy: -64.529406-0.000360j
[2025-09-18 16:22:20] [Iter 2044/2250] R3[993/1200], Temp: 0.0716, Energy: -64.545626-0.003305j
[2025-09-18 16:22:50] [Iter 2045/2250] R3[994/1200], Temp: 0.0710, Energy: -64.541051+0.002507j
[2025-09-18 16:23:19] [Iter 2046/2250] R3[995/1200], Temp: 0.0703, Energy: -64.541869-0.000814j
[2025-09-18 16:23:49] [Iter 2047/2250] R3[996/1200], Temp: 0.0696, Energy: -64.538796+0.003975j
[2025-09-18 16:24:18] [Iter 2048/2250] R3[997/1200], Temp: 0.0690, Energy: -64.538120-0.002265j
[2025-09-18 16:24:47] [Iter 2049/2250] R3[998/1200], Temp: 0.0683, Energy: -64.550944-0.002080j
[2025-09-18 16:25:17] [Iter 2050/2250] R3[999/1200], Temp: 0.0676, Energy: -64.540342-0.002777j
[2025-09-18 16:25:46] [Iter 2051/2250] R3[1000/1200], Temp: 0.0670, Energy: -64.531451-0.003833j
[2025-09-18 16:26:16] [Iter 2052/2250] R3[1001/1200], Temp: 0.0663, Energy: -64.545612-0.005923j
[2025-09-18 16:26:45] [Iter 2053/2250] R3[1002/1200], Temp: 0.0657, Energy: -64.542574-0.003326j
[2025-09-18 16:27:15] [Iter 2054/2250] R3[1003/1200], Temp: 0.0650, Energy: -64.540982-0.001388j
[2025-09-18 16:27:44] [Iter 2055/2250] R3[1004/1200], Temp: 0.0644, Energy: -64.537749+0.001330j
[2025-09-18 16:28:14] [Iter 2056/2250] R3[1005/1200], Temp: 0.0638, Energy: -64.537697-0.001999j
[2025-09-18 16:28:43] [Iter 2057/2250] R3[1006/1200], Temp: 0.0631, Energy: -64.535533+0.001397j
[2025-09-18 16:29:13] [Iter 2058/2250] R3[1007/1200], Temp: 0.0625, Energy: -64.534317-0.001000j
[2025-09-18 16:29:42] [Iter 2059/2250] R3[1008/1200], Temp: 0.0618, Energy: -64.540245+0.000224j
[2025-09-18 16:30:12] [Iter 2060/2250] R3[1009/1200], Temp: 0.0612, Energy: -64.539989+0.001275j
[2025-09-18 16:30:41] [Iter 2061/2250] R3[1010/1200], Temp: 0.0606, Energy: -64.547861-0.003205j
[2025-09-18 16:31:11] [Iter 2062/2250] R3[1011/1200], Temp: 0.0600, Energy: -64.535131-0.002464j
[2025-09-18 16:31:40] [Iter 2063/2250] R3[1012/1200], Temp: 0.0593, Energy: -64.540955+0.002513j
[2025-09-18 16:32:10] [Iter 2064/2250] R3[1013/1200], Temp: 0.0587, Energy: -64.534163-0.002525j
[2025-09-18 16:32:39] [Iter 2065/2250] R3[1014/1200], Temp: 0.0581, Energy: -64.534156+0.000915j
[2025-09-18 16:33:09] [Iter 2066/2250] R3[1015/1200], Temp: 0.0575, Energy: -64.541890+0.001801j
[2025-09-18 16:33:38] [Iter 2067/2250] R3[1016/1200], Temp: 0.0569, Energy: -64.537427-0.001422j
[2025-09-18 16:34:08] [Iter 2068/2250] R3[1017/1200], Temp: 0.0563, Energy: -64.545428-0.002198j
[2025-09-18 16:34:37] [Iter 2069/2250] R3[1018/1200], Temp: 0.0557, Energy: -64.538883+0.002356j
[2025-09-18 16:35:07] [Iter 2070/2250] R3[1019/1200], Temp: 0.0551, Energy: -64.532789-0.001193j
[2025-09-18 16:35:36] [Iter 2071/2250] R3[1020/1200], Temp: 0.0545, Energy: -64.543821-0.000341j
[2025-09-18 16:36:06] [Iter 2072/2250] R3[1021/1200], Temp: 0.0539, Energy: -64.537927+0.001963j
[2025-09-18 16:36:35] [Iter 2073/2250] R3[1022/1200], Temp: 0.0533, Energy: -64.536190+0.000951j
[2025-09-18 16:37:04] [Iter 2074/2250] R3[1023/1200], Temp: 0.0527, Energy: -64.543424-0.003493j
[2025-09-18 16:37:34] [Iter 2075/2250] R3[1024/1200], Temp: 0.0521, Energy: -64.534869-0.002172j
[2025-09-18 16:38:03] [Iter 2076/2250] R3[1025/1200], Temp: 0.0516, Energy: -64.542100+0.002215j
[2025-09-18 16:38:33] [Iter 2077/2250] R3[1026/1200], Temp: 0.0510, Energy: -64.548684+0.000288j
[2025-09-18 16:39:02] [Iter 2078/2250] R3[1027/1200], Temp: 0.0504, Energy: -64.532961+0.006304j
[2025-09-18 16:39:32] [Iter 2079/2250] R3[1028/1200], Temp: 0.0498, Energy: -64.539622+0.002137j
[2025-09-18 16:40:01] [Iter 2080/2250] R3[1029/1200], Temp: 0.0493, Energy: -64.543099+0.002286j
[2025-09-18 16:40:31] [Iter 2081/2250] R3[1030/1200], Temp: 0.0487, Energy: -64.549843-0.004770j
[2025-09-18 16:41:00] [Iter 2082/2250] R3[1031/1200], Temp: 0.0481, Energy: -64.544702+0.005066j
[2025-09-18 16:41:30] [Iter 2083/2250] R3[1032/1200], Temp: 0.0476, Energy: -64.542463-0.002670j
[2025-09-18 16:41:59] [Iter 2084/2250] R3[1033/1200], Temp: 0.0470, Energy: -64.542918+0.000771j
[2025-09-18 16:42:29] [Iter 2085/2250] R3[1034/1200], Temp: 0.0465, Energy: -64.531326+0.001736j
[2025-09-18 16:42:58] [Iter 2086/2250] R3[1035/1200], Temp: 0.0459, Energy: -64.531561+0.002204j
[2025-09-18 16:43:28] [Iter 2087/2250] R3[1036/1200], Temp: 0.0454, Energy: -64.544310+0.000741j
[2025-09-18 16:43:57] [Iter 2088/2250] R3[1037/1200], Temp: 0.0448, Energy: -64.543123-0.000509j
[2025-09-18 16:44:27] [Iter 2089/2250] R3[1038/1200], Temp: 0.0443, Energy: -64.530231+0.001144j
[2025-09-18 16:44:56] [Iter 2090/2250] R3[1039/1200], Temp: 0.0438, Energy: -64.518559-0.002063j
[2025-09-18 16:45:25] [Iter 2091/2250] R3[1040/1200], Temp: 0.0432, Energy: -64.528782-0.002719j
[2025-09-18 16:45:55] [Iter 2092/2250] R3[1041/1200], Temp: 0.0427, Energy: -64.538671+0.000421j
[2025-09-18 16:46:24] [Iter 2093/2250] R3[1042/1200], Temp: 0.0422, Energy: -64.533123+0.005366j
[2025-09-18 16:46:54] [Iter 2094/2250] R3[1043/1200], Temp: 0.0416, Energy: -64.547089+0.000850j
[2025-09-18 16:47:23] [Iter 2095/2250] R3[1044/1200], Temp: 0.0411, Energy: -64.536681+0.002195j
[2025-09-18 16:47:53] [Iter 2096/2250] R3[1045/1200], Temp: 0.0406, Energy: -64.531164+0.001947j
[2025-09-18 16:48:22] [Iter 2097/2250] R3[1046/1200], Temp: 0.0401, Energy: -64.532467+0.001527j
[2025-09-18 16:48:52] [Iter 2098/2250] R3[1047/1200], Temp: 0.0396, Energy: -64.533232+0.000526j
[2025-09-18 16:49:21] [Iter 2099/2250] R3[1048/1200], Temp: 0.0391, Energy: -64.531593+0.002543j
[2025-09-18 16:49:51] [Iter 2100/2250] R3[1049/1200], Temp: 0.0386, Energy: -64.536036+0.000514j
[2025-09-18 16:50:20] [Iter 2101/2250] R3[1050/1200], Temp: 0.0381, Energy: -64.534812-0.000584j
[2025-09-18 16:50:50] [Iter 2102/2250] R3[1051/1200], Temp: 0.0376, Energy: -64.541222-0.002763j
[2025-09-18 16:51:19] [Iter 2103/2250] R3[1052/1200], Temp: 0.0371, Energy: -64.532928+0.000281j
[2025-09-18 16:51:49] [Iter 2104/2250] R3[1053/1200], Temp: 0.0366, Energy: -64.537629-0.000943j
[2025-09-18 16:52:18] [Iter 2105/2250] R3[1054/1200], Temp: 0.0361, Energy: -64.540144-0.002660j
[2025-09-18 16:52:47] [Iter 2106/2250] R3[1055/1200], Temp: 0.0356, Energy: -64.540632+0.001442j
[2025-09-18 16:53:17] [Iter 2107/2250] R3[1056/1200], Temp: 0.0351, Energy: -64.559161-0.002545j
[2025-09-18 16:53:46] [Iter 2108/2250] R3[1057/1200], Temp: 0.0346, Energy: -64.542807-0.000605j
[2025-09-18 16:54:16] [Iter 2109/2250] R3[1058/1200], Temp: 0.0342, Energy: -64.533990+0.001883j
[2025-09-18 16:54:45] [Iter 2110/2250] R3[1059/1200], Temp: 0.0337, Energy: -64.541257+0.000302j
[2025-09-18 16:55:15] [Iter 2111/2250] R3[1060/1200], Temp: 0.0332, Energy: -64.538269-0.002069j
[2025-09-18 16:55:44] [Iter 2112/2250] R3[1061/1200], Temp: 0.0327, Energy: -64.538972-0.000140j
[2025-09-18 16:56:14] [Iter 2113/2250] R3[1062/1200], Temp: 0.0323, Energy: -64.536417-0.002245j
[2025-09-18 16:56:43] [Iter 2114/2250] R3[1063/1200], Temp: 0.0318, Energy: -64.540598-0.000986j
[2025-09-18 16:57:13] [Iter 2115/2250] R3[1064/1200], Temp: 0.0314, Energy: -64.540543-0.001129j
[2025-09-18 16:57:42] [Iter 2116/2250] R3[1065/1200], Temp: 0.0309, Energy: -64.543487-0.000178j
[2025-09-18 16:58:11] [Iter 2117/2250] R3[1066/1200], Temp: 0.0305, Energy: -64.560004-0.002430j
[2025-09-18 16:58:41] [Iter 2118/2250] R3[1067/1200], Temp: 0.0300, Energy: -64.562570-0.001907j
[2025-09-18 16:59:10] [Iter 2119/2250] R3[1068/1200], Temp: 0.0296, Energy: -64.553982-0.000712j
[2025-09-18 16:59:40] [Iter 2120/2250] R3[1069/1200], Temp: 0.0291, Energy: -64.535186+0.001735j
[2025-09-18 17:00:09] [Iter 2121/2250] R3[1070/1200], Temp: 0.0287, Energy: -64.532484+0.002413j
[2025-09-18 17:00:39] [Iter 2122/2250] R3[1071/1200], Temp: 0.0282, Energy: -64.512785-0.000658j
[2025-09-18 17:01:09] [Iter 2123/2250] R3[1072/1200], Temp: 0.0278, Energy: -64.530911-0.002366j
[2025-09-18 17:01:38] [Iter 2124/2250] R3[1073/1200], Temp: 0.0274, Energy: -64.537892+0.003526j
[2025-09-18 17:02:07] [Iter 2125/2250] R3[1074/1200], Temp: 0.0270, Energy: -64.545026+0.000852j
[2025-09-18 17:02:37] [Iter 2126/2250] R3[1075/1200], Temp: 0.0265, Energy: -64.531757-0.002105j
[2025-09-18 17:03:06] [Iter 2127/2250] R3[1076/1200], Temp: 0.0261, Energy: -64.551382-0.000449j
[2025-09-18 17:03:36] [Iter 2128/2250] R3[1077/1200], Temp: 0.0257, Energy: -64.541403-0.000321j
[2025-09-18 17:04:05] [Iter 2129/2250] R3[1078/1200], Temp: 0.0253, Energy: -64.550923+0.001764j
[2025-09-18 17:04:35] [Iter 2130/2250] R3[1079/1200], Temp: 0.0249, Energy: -64.541516+0.002939j
[2025-09-18 17:05:04] [Iter 2131/2250] R3[1080/1200], Temp: 0.0245, Energy: -64.529893+0.001646j
[2025-09-18 17:05:34] [Iter 2132/2250] R3[1081/1200], Temp: 0.0241, Energy: -64.537096+0.002266j
[2025-09-18 17:06:03] [Iter 2133/2250] R3[1082/1200], Temp: 0.0237, Energy: -64.527366-0.001490j
[2025-09-18 17:06:33] [Iter 2134/2250] R3[1083/1200], Temp: 0.0233, Energy: -64.540860-0.001536j
[2025-09-18 17:07:02] [Iter 2135/2250] R3[1084/1200], Temp: 0.0229, Energy: -64.541116-0.001200j
[2025-09-18 17:07:32] [Iter 2136/2250] R3[1085/1200], Temp: 0.0225, Energy: -64.547597-0.000702j
[2025-09-18 17:08:01] [Iter 2137/2250] R3[1086/1200], Temp: 0.0221, Energy: -64.535921-0.000390j
[2025-09-18 17:08:31] [Iter 2138/2250] R3[1087/1200], Temp: 0.0217, Energy: -64.543203-0.000392j
[2025-09-18 17:09:00] [Iter 2139/2250] R3[1088/1200], Temp: 0.0213, Energy: -64.533895+0.000189j
[2025-09-18 17:09:30] [Iter 2140/2250] R3[1089/1200], Temp: 0.0210, Energy: -64.537548-0.000379j
[2025-09-18 17:09:59] [Iter 2141/2250] R3[1090/1200], Temp: 0.0206, Energy: -64.546632+0.000875j
[2025-09-18 17:10:28] [Iter 2142/2250] R3[1091/1200], Temp: 0.0202, Energy: -64.543643-0.001749j
[2025-09-18 17:10:58] [Iter 2143/2250] R3[1092/1200], Temp: 0.0199, Energy: -64.545553-0.001867j
[2025-09-18 17:11:27] [Iter 2144/2250] R3[1093/1200], Temp: 0.0195, Energy: -64.535224+0.000966j
[2025-09-18 17:11:57] [Iter 2145/2250] R3[1094/1200], Temp: 0.0191, Energy: -64.532065+0.002348j
[2025-09-18 17:12:26] [Iter 2146/2250] R3[1095/1200], Temp: 0.0188, Energy: -64.538925+0.002615j
[2025-09-18 17:12:56] [Iter 2147/2250] R3[1096/1200], Temp: 0.0184, Energy: -64.556361-0.002101j
[2025-09-18 17:13:25] [Iter 2148/2250] R3[1097/1200], Temp: 0.0181, Energy: -64.550471+0.005050j
[2025-09-18 17:13:55] [Iter 2149/2250] R3[1098/1200], Temp: 0.0177, Energy: -64.556910-0.000125j
[2025-09-18 17:14:24] [Iter 2150/2250] R3[1099/1200], Temp: 0.0174, Energy: -64.520217+0.000083j
[2025-09-18 17:14:54] [Iter 2151/2250] R3[1100/1200], Temp: 0.0170, Energy: -64.538545+0.000767j
[2025-09-18 17:15:23] [Iter 2152/2250] R3[1101/1200], Temp: 0.0167, Energy: -64.529633+0.000335j
[2025-09-18 17:15:53] [Iter 2153/2250] R3[1102/1200], Temp: 0.0164, Energy: -64.545365-0.001526j
[2025-09-18 17:16:22] [Iter 2154/2250] R3[1103/1200], Temp: 0.0160, Energy: -64.541335+0.000759j
[2025-09-18 17:16:52] [Iter 2155/2250] R3[1104/1200], Temp: 0.0157, Energy: -64.546812+0.002714j
[2025-09-18 17:17:21] [Iter 2156/2250] R3[1105/1200], Temp: 0.0154, Energy: -64.541034+0.003267j
[2025-09-18 17:17:51] [Iter 2157/2250] R3[1106/1200], Temp: 0.0151, Energy: -64.541222+0.000584j
[2025-09-18 17:18:20] [Iter 2158/2250] R3[1107/1200], Temp: 0.0147, Energy: -64.545691+0.001518j
[2025-09-18 17:18:50] [Iter 2159/2250] R3[1108/1200], Temp: 0.0144, Energy: -64.550042-0.000705j
[2025-09-18 17:19:19] [Iter 2160/2250] R3[1109/1200], Temp: 0.0141, Energy: -64.532292+0.001519j
[2025-09-18 17:19:48] [Iter 2161/2250] R3[1110/1200], Temp: 0.0138, Energy: -64.534760+0.001193j
[2025-09-18 17:20:18] [Iter 2162/2250] R3[1111/1200], Temp: 0.0135, Energy: -64.550636-0.004972j
[2025-09-18 17:20:47] [Iter 2163/2250] R3[1112/1200], Temp: 0.0132, Energy: -64.535342-0.002967j
[2025-09-18 17:21:17] [Iter 2164/2250] R3[1113/1200], Temp: 0.0129, Energy: -64.529271-0.001200j
[2025-09-18 17:21:46] [Iter 2165/2250] R3[1114/1200], Temp: 0.0126, Energy: -64.543223-0.000977j
[2025-09-18 17:22:16] [Iter 2166/2250] R3[1115/1200], Temp: 0.0123, Energy: -64.546486+0.003197j
[2025-09-18 17:22:45] [Iter 2167/2250] R3[1116/1200], Temp: 0.0120, Energy: -64.519580+0.000617j
[2025-09-18 17:23:15] [Iter 2168/2250] R3[1117/1200], Temp: 0.0118, Energy: -64.535129-0.000683j
[2025-09-18 17:23:44] [Iter 2169/2250] R3[1118/1200], Temp: 0.0115, Energy: -64.548809+0.000471j
[2025-09-18 17:24:14] [Iter 2170/2250] R3[1119/1200], Temp: 0.0112, Energy: -64.525042-0.001016j
[2025-09-18 17:24:43] [Iter 2171/2250] R3[1120/1200], Temp: 0.0109, Energy: -64.529115-0.002757j
[2025-09-18 17:25:13] [Iter 2172/2250] R3[1121/1200], Temp: 0.0107, Energy: -64.534988+0.004923j
[2025-09-18 17:25:42] [Iter 2173/2250] R3[1122/1200], Temp: 0.0104, Energy: -64.545006+0.000579j
[2025-09-18 17:26:12] [Iter 2174/2250] R3[1123/1200], Temp: 0.0101, Energy: -64.546171-0.000175j
[2025-09-18 17:26:41] [Iter 2175/2250] R3[1124/1200], Temp: 0.0099, Energy: -64.544189+0.000049j
[2025-09-18 17:27:11] [Iter 2176/2250] R3[1125/1200], Temp: 0.0096, Energy: -64.536815-0.002621j
[2025-09-18 17:27:40] [Iter 2177/2250] R3[1126/1200], Temp: 0.0094, Energy: -64.525446-0.000313j
[2025-09-18 17:28:10] [Iter 2178/2250] R3[1127/1200], Temp: 0.0091, Energy: -64.547353-0.002492j
[2025-09-18 17:28:39] [Iter 2179/2250] R3[1128/1200], Temp: 0.0089, Energy: -64.533668-0.000578j
[2025-09-18 17:29:09] [Iter 2180/2250] R3[1129/1200], Temp: 0.0086, Energy: -64.533862+0.001863j
[2025-09-18 17:29:38] [Iter 2181/2250] R3[1130/1200], Temp: 0.0084, Energy: -64.534828-0.000904j
[2025-09-18 17:30:08] [Iter 2182/2250] R3[1131/1200], Temp: 0.0081, Energy: -64.546328-0.000320j
[2025-09-18 17:30:37] [Iter 2183/2250] R3[1132/1200], Temp: 0.0079, Energy: -64.523060+0.003644j
[2025-09-18 17:31:06] [Iter 2184/2250] R3[1133/1200], Temp: 0.0077, Energy: -64.553041-0.000464j
[2025-09-18 17:31:36] [Iter 2185/2250] R3[1134/1200], Temp: 0.0074, Energy: -64.545103-0.002957j
[2025-09-18 17:32:05] [Iter 2186/2250] R3[1135/1200], Temp: 0.0072, Energy: -64.542695-0.000741j
[2025-09-18 17:32:35] [Iter 2187/2250] R3[1136/1200], Temp: 0.0070, Energy: -64.545523-0.002147j
[2025-09-18 17:33:04] [Iter 2188/2250] R3[1137/1200], Temp: 0.0068, Energy: -64.549452-0.001387j
[2025-09-18 17:33:34] [Iter 2189/2250] R3[1138/1200], Temp: 0.0066, Energy: -64.543365+0.000314j
[2025-09-18 17:34:03] [Iter 2190/2250] R3[1139/1200], Temp: 0.0064, Energy: -64.531860-0.001643j
[2025-09-18 17:34:33] [Iter 2191/2250] R3[1140/1200], Temp: 0.0062, Energy: -64.549979-0.004321j
[2025-09-18 17:35:02] [Iter 2192/2250] R3[1141/1200], Temp: 0.0060, Energy: -64.542655+0.000149j
[2025-09-18 17:35:32] [Iter 2193/2250] R3[1142/1200], Temp: 0.0058, Energy: -64.548280-0.001513j
[2025-09-18 17:36:01] [Iter 2194/2250] R3[1143/1200], Temp: 0.0056, Energy: -64.542081-0.004782j
[2025-09-18 17:36:31] [Iter 2195/2250] R3[1144/1200], Temp: 0.0054, Energy: -64.542464-0.001187j
[2025-09-18 17:37:00] [Iter 2196/2250] R3[1145/1200], Temp: 0.0052, Energy: -64.535493-0.001911j
[2025-09-18 17:37:30] [Iter 2197/2250] R3[1146/1200], Temp: 0.0050, Energy: -64.544488+0.000798j
[2025-09-18 17:37:59] [Iter 2198/2250] R3[1147/1200], Temp: 0.0048, Energy: -64.551835+0.000444j
[2025-09-18 17:38:29] [Iter 2199/2250] R3[1148/1200], Temp: 0.0046, Energy: -64.553846+0.005526j
[2025-09-18 17:38:58] [Iter 2200/2250] R3[1149/1200], Temp: 0.0045, Energy: -64.556033+0.000304j
[2025-09-18 17:39:28] [Iter 2201/2250] R3[1150/1200], Temp: 0.0043, Energy: -64.549816+0.003158j
[2025-09-18 17:39:57] [Iter 2202/2250] R3[1151/1200], Temp: 0.0041, Energy: -64.536363+0.002964j
[2025-09-18 17:40:27] [Iter 2203/2250] R3[1152/1200], Temp: 0.0039, Energy: -64.536396-0.001246j
[2025-09-18 17:40:56] [Iter 2204/2250] R3[1153/1200], Temp: 0.0038, Energy: -64.546547-0.002559j
[2025-09-18 17:41:26] [Iter 2205/2250] R3[1154/1200], Temp: 0.0036, Energy: -64.543306+0.003108j
[2025-09-18 17:41:55] [Iter 2206/2250] R3[1155/1200], Temp: 0.0035, Energy: -64.526192+0.001736j
[2025-09-18 17:42:24] [Iter 2207/2250] R3[1156/1200], Temp: 0.0033, Energy: -64.544904+0.005069j
[2025-09-18 17:42:54] [Iter 2208/2250] R3[1157/1200], Temp: 0.0032, Energy: -64.542362-0.001966j
[2025-09-18 17:43:23] [Iter 2209/2250] R3[1158/1200], Temp: 0.0030, Energy: -64.560348-0.000501j
[2025-09-18 17:43:53] [Iter 2210/2250] R3[1159/1200], Temp: 0.0029, Energy: -64.546813+0.005901j
[2025-09-18 17:44:22] [Iter 2211/2250] R3[1160/1200], Temp: 0.0027, Energy: -64.550981-0.001978j
[2025-09-18 17:44:52] [Iter 2212/2250] R3[1161/1200], Temp: 0.0026, Energy: -64.539524-0.001580j
[2025-09-18 17:45:21] [Iter 2213/2250] R3[1162/1200], Temp: 0.0025, Energy: -64.530612+0.000218j
[2025-09-18 17:45:51] [Iter 2214/2250] R3[1163/1200], Temp: 0.0023, Energy: -64.544008-0.002451j
[2025-09-18 17:46:21] [Iter 2215/2250] R3[1164/1200], Temp: 0.0022, Energy: -64.526921+0.001335j
[2025-09-18 17:46:51] [Iter 2216/2250] R3[1165/1200], Temp: 0.0021, Energy: -64.550724+0.002048j
[2025-09-18 17:47:20] [Iter 2217/2250] R3[1166/1200], Temp: 0.0020, Energy: -64.557031+0.001370j
[2025-09-18 17:47:50] [Iter 2218/2250] R3[1167/1200], Temp: 0.0019, Energy: -64.546245+0.000727j
[2025-09-18 17:48:19] [Iter 2219/2250] R3[1168/1200], Temp: 0.0018, Energy: -64.541379+0.005079j
[2025-09-18 17:48:48] [Iter 2220/2250] R3[1169/1200], Temp: 0.0016, Energy: -64.537240+0.001040j
[2025-09-18 17:49:18] [Iter 2221/2250] R3[1170/1200], Temp: 0.0015, Energy: -64.549574-0.001341j
[2025-09-18 17:49:47] [Iter 2222/2250] R3[1171/1200], Temp: 0.0014, Energy: -64.546464-0.000943j
[2025-09-18 17:50:17] [Iter 2223/2250] R3[1172/1200], Temp: 0.0013, Energy: -64.558925-0.000196j
[2025-09-18 17:50:46] [Iter 2224/2250] R3[1173/1200], Temp: 0.0012, Energy: -64.553106-0.001105j
[2025-09-18 17:51:16] [Iter 2225/2250] R3[1174/1200], Temp: 0.0012, Energy: -64.547673-0.001693j
[2025-09-18 17:51:45] [Iter 2226/2250] R3[1175/1200], Temp: 0.0011, Energy: -64.542618-0.004870j
[2025-09-18 17:52:15] [Iter 2227/2250] R3[1176/1200], Temp: 0.0010, Energy: -64.542910-0.004788j
[2025-09-18 17:52:44] [Iter 2228/2250] R3[1177/1200], Temp: 0.0009, Energy: -64.538520+0.000144j
[2025-09-18 17:53:14] [Iter 2229/2250] R3[1178/1200], Temp: 0.0008, Energy: -64.535592+0.000008j
[2025-09-18 17:53:43] [Iter 2230/2250] R3[1179/1200], Temp: 0.0008, Energy: -64.550960-0.001869j
[2025-09-18 17:54:13] [Iter 2231/2250] R3[1180/1200], Temp: 0.0007, Energy: -64.527932-0.001147j
[2025-09-18 17:54:42] [Iter 2232/2250] R3[1181/1200], Temp: 0.0006, Energy: -64.541938-0.004048j
[2025-09-18 17:55:12] [Iter 2233/2250] R3[1182/1200], Temp: 0.0006, Energy: -64.542445-0.000559j
[2025-09-18 17:55:41] [Iter 2234/2250] R3[1183/1200], Temp: 0.0005, Energy: -64.546238-0.006820j
[2025-09-18 17:56:11] [Iter 2235/2250] R3[1184/1200], Temp: 0.0004, Energy: -64.542935-0.000562j
[2025-09-18 17:56:40] [Iter 2236/2250] R3[1185/1200], Temp: 0.0004, Energy: -64.543031-0.000901j
[2025-09-18 17:57:10] [Iter 2237/2250] R3[1186/1200], Temp: 0.0003, Energy: -64.548430+0.001865j
[2025-09-18 17:57:39] [Iter 2238/2250] R3[1187/1200], Temp: 0.0003, Energy: -64.550330+0.000679j
[2025-09-18 17:58:08] [Iter 2239/2250] R3[1188/1200], Temp: 0.0002, Energy: -64.553367+0.000750j
[2025-09-18 17:58:38] [Iter 2240/2250] R3[1189/1200], Temp: 0.0002, Energy: -64.546907+0.000217j
[2025-09-18 17:59:07] [Iter 2241/2250] R3[1190/1200], Temp: 0.0002, Energy: -64.554800+0.000367j
[2025-09-18 17:59:37] [Iter 2242/2250] R3[1191/1200], Temp: 0.0001, Energy: -64.534129-0.002921j
[2025-09-18 18:00:06] [Iter 2243/2250] R3[1192/1200], Temp: 0.0001, Energy: -64.538354-0.008813j
[2025-09-18 18:00:36] [Iter 2244/2250] R3[1193/1200], Temp: 0.0001, Energy: -64.537998-0.002445j
[2025-09-18 18:01:06] [Iter 2245/2250] R3[1194/1200], Temp: 0.0001, Energy: -64.541024-0.003811j
[2025-09-18 18:01:35] [Iter 2246/2250] R3[1195/1200], Temp: 0.0000, Energy: -64.534669-0.000983j
[2025-09-18 18:02:04] [Iter 2247/2250] R3[1196/1200], Temp: 0.0000, Energy: -64.542853+0.003029j
[2025-09-18 18:02:34] [Iter 2248/2250] R3[1197/1200], Temp: 0.0000, Energy: -64.533662-0.003105j
[2025-09-18 18:03:03] [Iter 2249/2250] R3[1198/1200], Temp: 0.0000, Energy: -64.543953-0.003989j
[2025-09-18 18:03:33] [Iter 2250/2250] R3[1199/1200], Temp: 0.0000, Energy: -64.533015-0.001325j
[2025-09-18 18:03:33] ✓ Checkpoint saved: checkpoint_iter_002250.pkl
[2025-09-18 18:03:33] ✅ Training completed | Restarts: 3
[2025-09-18 18:03:33] ============================================================
[2025-09-18 18:03:33] Training completed | Runtime: 66411.4s
[2025-09-18 18:03:43] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-18 18:03:43] ============================================================
