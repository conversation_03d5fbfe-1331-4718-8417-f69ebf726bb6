[2025-09-19 23:30:53] ✓ 从checkpoint恢复: results/L=6/J2=1.00/J1=0.82/training/checkpoints/final_GCNN.pkl
[2025-09-19 23:30:53]   - 迭代次数: final
[2025-09-19 23:30:53]   - 能量: -66.425732+0.002592j ± 0.009585
[2025-09-19 23:30:53]   - 时间戳: 2025-09-19T23:30:30.875167+08:00
[2025-09-19 23:31:16] ✓ 变分状态参数已从checkpoint恢复
[2025-09-19 23:31:16] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-19 23:31:16] ==================================================
[2025-09-19 23:31:16] GCNN for Shastry-Sutherland Model
[2025-09-19 23:31:16] ==================================================
[2025-09-19 23:31:16] System parameters:
[2025-09-19 23:31:16]   - System size: L=6, N=144
[2025-09-19 23:31:16]   - System parameters: J1=0.83, J2=1.0, Q=0.0
[2025-09-19 23:31:16] --------------------------------------------------
[2025-09-19 23:31:16] Model parameters:
[2025-09-19 23:31:16]   - Number of layers = 4
[2025-09-19 23:31:16]   - Number of features = 4
[2025-09-19 23:31:16]   - Total parameters = 28252
[2025-09-19 23:31:16] --------------------------------------------------
[2025-09-19 23:31:16] Training parameters:
[2025-09-19 23:31:16]   - Learning rate: 0.015
[2025-09-19 23:31:16]   - Total iterations: 1050
[2025-09-19 23:31:16]   - Annealing cycles: 3
[2025-09-19 23:31:16]   - Initial period: 150
[2025-09-19 23:31:16]   - Period multiplier: 2.0
[2025-09-19 23:31:16]   - Temperature range: 0.0-1.0
[2025-09-19 23:31:16]   - Samples: 4096
[2025-09-19 23:31:17]   - Discarded samples: 0
[2025-09-19 23:31:17]   - Chunk size: 2048
[2025-09-19 23:31:17]   - Diagonal shift: 0.2
[2025-09-19 23:31:17]   - Gradient clipping: 1.0
[2025-09-19 23:31:17]   - Checkpoint enabled: interval=105
[2025-09-19 23:31:17]   - Checkpoint directory: results/L=6/J2=1.00/J1=0.83/training/checkpoints
[2025-09-19 23:31:17] --------------------------------------------------
[2025-09-19 23:31:17] Device status:
[2025-09-19 23:31:17]   - Devices model: NVIDIA H200 NVL
[2025-09-19 23:31:17]   - Number of devices: 1
[2025-09-19 23:31:17]   - Sharding: True
[2025-09-19 23:31:17] ============================================================
[2025-09-19 23:32:52] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -39.246640-0.002557j
[2025-09-19 23:33:58] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -41.259394+0.060918j
[2025-09-19 23:34:30] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -44.002766+0.027741j
[2025-09-19 23:35:02] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -47.713392-0.003747j
[2025-09-19 23:35:34] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -53.632144+0.050506j
[2025-09-19 23:36:05] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -59.801984-0.017396j
[2025-09-19 23:36:37] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -64.754952+0.001483j
[2025-09-19 23:37:09] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -66.688272-0.019018j
[2025-09-19 23:37:41] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -67.282722-0.003010j
[2025-09-19 23:38:13] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -67.337233+0.000333j
[2025-09-19 23:38:45] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -67.340658-0.000298j
[2025-09-19 23:39:17] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -67.342006-0.003642j
[2025-09-19 23:39:49] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -67.342581-0.002324j
[2025-09-19 23:40:21] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -67.334120+0.000310j
[2025-09-19 23:40:53] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -67.342115+0.001707j
[2025-09-19 23:41:24] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -67.340782+0.001848j
[2025-09-19 23:41:56] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -67.345130+0.003894j
[2025-09-19 23:42:28] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -67.349397-0.003223j
[2025-09-19 23:43:00] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -67.333380-0.002582j
[2025-09-19 23:43:32] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -67.332031+0.001275j
[2025-09-19 23:44:04] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -67.338060-0.000653j
[2025-09-19 23:44:36] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -67.349757-0.001506j
[2025-09-19 23:45:08] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -67.340375+0.002306j
[2025-09-19 23:45:40] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -67.348351-0.001704j
[2025-09-19 23:46:11] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -67.345651-0.001325j
[2025-09-19 23:46:43] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -67.349437+0.002204j
[2025-09-19 23:47:15] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -67.347657+0.000161j
[2025-09-19 23:47:47] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -67.318268-0.002653j
[2025-09-19 23:48:19] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -67.346146-0.002285j
[2025-09-19 23:48:51] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -67.347953-0.001178j
[2025-09-19 23:49:23] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -67.340457-0.003137j
[2025-09-19 23:49:55] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -67.330593-0.001197j
[2025-09-19 23:50:27] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -67.322527-0.002509j
[2025-09-19 23:50:59] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -67.352760-0.000183j
[2025-09-19 23:51:31] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -67.363905+0.001414j
[2025-09-19 23:52:03] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -67.340784+0.000168j
[2025-09-19 23:52:35] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -67.338534-0.000809j
[2025-09-19 23:53:07] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -67.340005-0.000141j
[2025-09-19 23:53:39] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -67.330920+0.000819j
[2025-09-19 23:54:11] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -67.327976+0.000738j
[2025-09-19 23:54:43] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -67.344181-0.000811j
[2025-09-19 23:55:15] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -67.339465-0.007895j
[2025-09-19 23:55:47] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -67.345917-0.002016j
[2025-09-19 23:56:19] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -67.327779+0.000926j
[2025-09-19 23:56:51] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -67.326048-0.000404j
[2025-09-19 23:57:23] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -67.336077+0.000560j
[2025-09-19 23:57:55] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -67.346477+0.000670j
[2025-09-19 23:58:27] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -67.350116+0.001761j
[2025-09-19 23:58:59] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -67.352757+0.001443j
[2025-09-19 23:59:31] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -67.333020+0.000173j
[2025-09-20 00:00:03] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -67.328127+0.001353j
[2025-09-20 00:00:35] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -67.351337-0.001332j
[2025-09-20 00:01:07] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -67.350362-0.001056j
[2025-09-20 00:01:39] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -67.343135-0.003910j
[2025-09-20 00:02:11] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -67.342844-0.001200j
[2025-09-20 00:02:42] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -67.326894-0.025037j
[2025-09-20 00:03:14] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -67.344272-0.001973j
[2025-09-20 00:03:46] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -67.349066+0.000081j
[2025-09-20 00:04:18] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -67.330373+0.001687j
[2025-09-20 00:04:50] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -67.339179-0.004150j
[2025-09-20 00:05:22] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -67.357326-0.003102j
[2025-09-20 00:05:54] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -67.333500-0.002194j
[2025-09-20 00:06:26] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -67.337771+0.005930j
[2025-09-20 00:06:58] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -67.349439-0.000833j
[2025-09-20 00:07:30] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -67.340064-0.003324j
[2025-09-20 00:08:02] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -67.353089+0.001558j
[2025-09-20 00:08:34] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -67.343608-0.002202j
[2025-09-20 00:09:06] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -67.360813-0.000019j
[2025-09-20 00:09:38] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -67.333330-0.001425j
[2025-09-20 00:10:10] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -67.336327-0.000484j
[2025-09-20 00:10:42] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -67.356716+0.001924j
[2025-09-20 00:11:14] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -67.350655-0.002209j
[2025-09-20 00:11:46] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -67.346533+0.001501j
[2025-09-20 00:12:18] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -67.359950-0.003665j
[2025-09-20 00:12:50] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -67.343521+0.003191j
[2025-09-20 00:13:22] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -67.348608-0.003620j
[2025-09-20 00:13:54] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -67.337898-0.000357j
[2025-09-20 00:14:26] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -67.344133+0.000787j
[2025-09-20 00:14:58] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -67.351319-0.001610j
[2025-09-20 00:15:30] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -67.331455-0.004937j
[2025-09-20 00:16:02] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -67.333278-0.000078j
[2025-09-20 00:16:34] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -67.343725-0.002703j
[2025-09-20 00:17:06] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -67.334034-0.001475j
[2025-09-20 00:17:38] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -67.341904+0.000445j
[2025-09-20 00:18:10] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -67.358280+0.002851j
[2025-09-20 00:18:42] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -67.343594+0.000312j
[2025-09-20 00:19:14] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -67.327353+0.001787j
[2025-09-20 00:19:46] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -67.341581+0.002568j
[2025-09-20 00:20:18] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -67.331701-0.002922j
[2025-09-20 00:20:50] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -67.342240+0.002913j
[2025-09-20 00:21:22] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -67.351193+0.005139j
[2025-09-20 00:21:54] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -67.354103+0.001338j
[2025-09-20 00:22:26] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -67.353522-0.000231j
[2025-09-20 00:22:58] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -67.349421-0.000035j
[2025-09-20 00:23:30] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -67.338605+0.001310j
[2025-09-20 00:24:02] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -67.351767-0.000221j
[2025-09-20 00:24:34] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -67.343540+0.000273j
[2025-09-20 00:25:05] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -67.336913+0.003504j
[2025-09-20 00:25:37] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -67.314598-0.001970j
[2025-09-20 00:26:09] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -67.350027+0.002523j
[2025-09-20 00:26:41] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -67.323365-0.000144j
[2025-09-20 00:27:13] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -67.347363+0.000756j
[2025-09-20 00:27:45] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -67.338078+0.002545j
[2025-09-20 00:28:17] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -67.340984+0.000925j
[2025-09-20 00:28:49] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -67.345311+0.003230j
[2025-09-20 00:28:49] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-20 00:29:21] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -67.345041-0.003454j
[2025-09-20 00:29:53] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -67.339858+0.001767j
[2025-09-20 00:30:25] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -67.352386-0.002395j
[2025-09-20 00:30:57] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -67.349933-0.002704j
[2025-09-20 00:31:29] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -67.347369+0.002366j
[2025-09-20 00:32:01] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -67.339647+0.003246j
[2025-09-20 00:32:33] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -67.343889-0.002501j
[2025-09-20 00:33:05] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -67.352251+0.000350j
[2025-09-20 00:33:37] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -67.333384+0.000373j
[2025-09-20 00:34:09] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -67.342272-0.000463j
[2025-09-20 00:34:41] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -67.346151-0.003361j
[2025-09-20 00:35:13] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -67.336265+0.000831j
[2025-09-20 00:35:45] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -67.355134-0.000242j
[2025-09-20 00:36:17] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -67.347804+0.001269j
[2025-09-20 00:36:49] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -67.347173+0.000512j
[2025-09-20 00:37:21] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -67.339500-0.000229j
[2025-09-20 00:37:52] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -67.341378-0.001489j
[2025-09-20 00:38:24] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -67.347159-0.000915j
[2025-09-20 00:38:56] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -67.329408-0.001397j
[2025-09-20 00:39:28] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -67.321892-0.003485j
[2025-09-20 00:40:00] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -67.329791-0.000661j
[2025-09-20 00:40:32] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -67.337393+0.004242j
[2025-09-20 00:41:04] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -67.339716+0.000459j
[2025-09-20 00:41:35] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -67.345941+0.000075j
[2025-09-20 00:42:07] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -67.338254-0.001612j
[2025-09-20 00:42:39] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -67.331719+0.002120j
[2025-09-20 00:43:11] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -67.332351+0.000891j
[2025-09-20 00:43:43] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -67.365517+0.001632j
[2025-09-20 00:44:15] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -67.341425-0.001420j
[2025-09-20 00:44:46] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -67.343014-0.001047j
[2025-09-20 00:45:18] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -67.358291+0.000920j
[2025-09-20 00:45:50] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -67.345108+0.000282j
[2025-09-20 00:46:22] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -67.329194+0.000845j
[2025-09-20 00:46:54] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -67.339756-0.003589j
[2025-09-20 00:47:26] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -67.337443+0.002698j
[2025-09-20 00:47:58] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -67.327329+0.001663j
[2025-09-20 00:48:30] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -67.342737+0.000849j
[2025-09-20 00:49:01] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -67.346036+0.001383j
[2025-09-20 00:49:34] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -67.342275-0.000611j
[2025-09-20 00:50:06] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -67.347797+0.001298j
[2025-09-20 00:50:38] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -67.347337+0.000097j
[2025-09-20 00:51:10] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -67.341244-0.001196j
[2025-09-20 00:51:42] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -67.355762+0.000143j
[2025-09-20 00:52:14] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -67.344974+0.001387j
[2025-09-20 00:52:46] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -67.332902-0.002541j
[2025-09-20 00:52:46] RESTART #1 | Period: 300
[2025-09-20 00:53:18] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -67.368894-0.000224j
[2025-09-20 00:53:50] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -67.348477-0.000872j
[2025-09-20 00:54:22] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -67.347100+0.003487j
[2025-09-20 00:54:54] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -67.356849-0.003879j
[2025-09-20 00:55:26] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -67.351080+0.001540j
[2025-09-20 00:55:58] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -67.366097-0.000156j
[2025-09-20 00:56:30] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -67.340130-0.000379j
[2025-09-20 00:57:02] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -67.355410+0.000982j
[2025-09-20 00:57:35] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -67.345570+0.001615j
[2025-09-20 00:58:07] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -67.336057+0.002950j
[2025-09-20 00:58:39] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -67.336069-0.002387j
[2025-09-20 00:59:11] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -67.354990+0.001671j
[2025-09-20 00:59:43] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -67.360281+0.001817j
[2025-09-20 01:00:15] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -67.346254-0.003105j
[2025-09-20 01:00:47] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -67.328507+0.003720j
[2025-09-20 01:01:19] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -67.345923+0.001547j
[2025-09-20 01:01:51] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -67.336667+0.000640j
[2025-09-20 01:02:24] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -67.338457-0.000505j
[2025-09-20 01:02:56] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -67.337025-0.002801j
[2025-09-20 01:03:28] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -67.348582+0.018234j
[2025-09-20 01:04:00] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -67.332941+0.000310j
[2025-09-20 01:04:32] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -67.360010-0.001455j
[2025-09-20 01:05:04] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -67.341490-0.001158j
[2025-09-20 01:05:36] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -67.339882-0.001129j
[2025-09-20 01:06:08] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -67.333379-0.000935j
[2025-09-20 01:06:40] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -67.349597-0.002227j
[2025-09-20 01:07:12] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -67.352774+0.000303j
[2025-09-20 01:07:44] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -67.340179-0.001014j
[2025-09-20 01:08:16] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -67.335043+0.000255j
[2025-09-20 01:08:48] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -67.351527+0.001939j
[2025-09-20 01:09:20] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -67.347075-0.003931j
[2025-09-20 01:09:52] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -67.326627-0.000956j
[2025-09-20 01:10:24] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -67.348736-0.002156j
[2025-09-20 01:10:56] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -67.343221-0.001471j
[2025-09-20 01:11:29] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -67.355586+0.002723j
[2025-09-20 01:12:00] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -67.346221-0.001221j
[2025-09-20 01:12:32] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -67.345201+0.001435j
[2025-09-20 01:13:04] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -67.349902+0.001016j
[2025-09-20 01:13:36] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -67.339164+0.000274j
[2025-09-20 01:14:08] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -67.357545-0.001202j
[2025-09-20 01:14:40] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -67.339423+0.003027j
[2025-09-20 01:15:12] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -67.345167+0.004954j
[2025-09-20 01:15:44] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -67.345378+0.002841j
[2025-09-20 01:16:16] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -67.334582+0.002272j
[2025-09-20 01:16:48] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -67.340323+0.000042j
[2025-09-20 01:17:20] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -67.355165-0.002163j
[2025-09-20 01:17:52] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -67.335481+0.005624j
[2025-09-20 01:18:24] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -67.349279+0.000868j
[2025-09-20 01:18:55] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -67.338475+0.001019j
[2025-09-20 01:19:27] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -67.336673-0.001798j
[2025-09-20 01:19:59] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -67.344254+0.002143j
[2025-09-20 01:20:31] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -67.331396+0.003274j
[2025-09-20 01:21:03] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -67.358680-0.002075j
[2025-09-20 01:21:35] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -67.339899-0.001058j
[2025-09-20 01:22:07] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -67.343414-0.000016j
[2025-09-20 01:22:39] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -67.325466+0.001156j
[2025-09-20 01:23:11] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -67.351400+0.003230j
[2025-09-20 01:23:43] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -67.352521-0.002243j
[2025-09-20 01:24:15] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -67.335274-0.002255j
[2025-09-20 01:24:47] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -67.336332+0.000976j
[2025-09-20 01:24:47] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-20 01:25:19] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -67.328614+0.000429j
[2025-09-20 01:25:51] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -67.326109-0.000786j
[2025-09-20 01:26:23] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -67.339125-0.000408j
[2025-09-20 01:26:55] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -67.339388+0.001571j
[2025-09-20 01:27:27] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -67.352059-0.003644j
[2025-09-20 01:27:59] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -67.354997+0.002662j
[2025-09-20 01:28:31] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -67.336428-0.003584j
[2025-09-20 01:29:03] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -67.343500+0.000792j
[2025-09-20 01:29:35] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -67.336595-0.002211j
[2025-09-20 01:30:07] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -67.356087-0.000310j
[2025-09-20 01:30:39] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -67.344402-0.000342j
[2025-09-20 01:31:11] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -67.358691+0.003394j
[2025-09-20 01:31:43] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -67.331513-0.002553j
[2025-09-20 01:32:15] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -67.341691-0.000198j
[2025-09-20 01:32:47] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -67.348425-0.002757j
[2025-09-20 01:33:19] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -67.350617+0.002648j
[2025-09-20 01:33:51] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -67.339571+0.000816j
[2025-09-20 01:34:23] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -67.350457+0.000822j
[2025-09-20 01:34:55] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -67.335093-0.000209j
[2025-09-20 01:35:27] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -67.347560-0.001044j
[2025-09-20 01:35:59] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -67.347216-0.001357j
[2025-09-20 01:36:31] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -67.333037+0.001509j
[2025-09-20 01:37:03] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -67.355291-0.001197j
[2025-09-20 01:37:36] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -67.349998+0.000496j
[2025-09-20 01:38:07] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -67.342715+0.002287j
[2025-09-20 01:38:39] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -67.333637-0.003903j
[2025-09-20 01:39:11] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -67.354547+0.000687j
[2025-09-20 01:39:44] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -67.344372+0.004631j
[2025-09-20 01:40:16] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -67.341683-0.000077j
[2025-09-20 01:40:48] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -67.344689+0.003325j
[2025-09-20 01:41:20] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -67.351259-0.001228j
[2025-09-20 01:41:52] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -67.339297+0.002150j
[2025-09-20 01:42:24] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -67.362537+0.001358j
[2025-09-20 01:42:56] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -67.336740+0.003966j
[2025-09-20 01:43:28] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -67.333550+0.005270j
[2025-09-20 01:44:00] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -67.347646-0.002334j
[2025-09-20 01:44:32] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -67.337652+0.002086j
[2025-09-20 01:45:04] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -67.355020-0.000972j
[2025-09-20 01:45:36] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -67.343850+0.001201j
[2025-09-20 01:46:08] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -67.329046-0.001852j
[2025-09-20 01:46:40] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -67.352721-0.000037j
[2025-09-20 01:47:12] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -67.338191-0.000935j
[2025-09-20 01:47:44] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -67.346460-0.003073j
[2025-09-20 01:48:16] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -67.334723+0.000179j
[2025-09-20 01:48:48] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -67.350441+0.000113j
[2025-09-20 01:49:20] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -67.331169+0.002366j
[2025-09-20 01:49:52] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -67.339729-0.000797j
[2025-09-20 01:50:24] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -67.368132-0.000768j
[2025-09-20 01:50:56] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -67.333066-0.001223j
[2025-09-20 01:51:28] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -67.348481-0.001412j
[2025-09-20 01:52:01] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -67.338859-0.003498j
[2025-09-20 01:52:33] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -67.347082-0.000993j
[2025-09-20 01:53:05] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -67.328989+0.000843j
[2025-09-20 01:53:37] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -67.337366-0.000955j
[2025-09-20 01:54:09] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -67.334517+0.002920j
[2025-09-20 01:54:41] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -67.347642-0.000290j
[2025-09-20 01:55:13] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -67.349639+0.003377j
[2025-09-20 01:55:45] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -67.341485+0.001483j
[2025-09-20 01:56:17] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -67.360124+0.007243j
[2025-09-20 01:56:49] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -67.346773-0.000536j
[2025-09-20 01:57:21] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -67.364995-0.003289j
[2025-09-20 01:57:53] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -67.345511-0.000011j
[2025-09-20 01:58:25] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -67.346670+0.000861j
[2025-09-20 01:58:57] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -67.333439-0.000060j
[2025-09-20 01:59:29] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -67.355194+0.001752j
[2025-09-20 02:00:01] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -67.325822+0.000384j
[2025-09-20 02:00:33] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -67.344288-0.000850j
[2025-09-20 02:01:05] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -67.354524+0.000775j
[2025-09-20 02:01:37] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -67.352536-0.000714j
[2025-09-20 02:02:09] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -67.344566+0.001927j
[2025-09-20 02:02:41] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -67.362550+0.001680j
[2025-09-20 02:03:13] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -67.321011-0.001224j
[2025-09-20 02:03:45] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -67.342396-0.002037j
[2025-09-20 02:04:17] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -67.355366-0.000621j
[2025-09-20 02:04:49] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -67.356045+0.002814j
[2025-09-20 02:05:21] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -67.360535+0.001479j
[2025-09-20 02:05:54] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -67.358596+0.000924j
[2025-09-20 02:06:26] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -67.341339-0.002883j
[2025-09-20 02:06:58] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -67.328374-0.001843j
[2025-09-20 02:07:30] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -67.354566+0.002842j
[2025-09-20 02:08:02] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -67.331466-0.003914j
[2025-09-20 02:08:34] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -67.322123+0.001508j
[2025-09-20 02:09:06] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -67.344868-0.001441j
[2025-09-20 02:09:38] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -67.345788+0.001398j
[2025-09-20 02:10:10] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -67.354518-0.003070j
[2025-09-20 02:10:42] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -67.357560-0.001957j
[2025-09-20 02:11:14] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -67.331037-0.002796j
[2025-09-20 02:11:46] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -67.329363-0.001999j
[2025-09-20 02:12:18] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -67.339123+0.003429j
[2025-09-20 02:12:50] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -67.348866+0.000569j
[2025-09-20 02:13:22] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -67.349128-0.001456j
[2025-09-20 02:13:54] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -67.348015+0.004149j
[2025-09-20 02:14:26] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -67.330288+0.001244j
[2025-09-20 02:14:58] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -67.351236+0.001011j
[2025-09-20 02:15:31] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -67.345286+0.002356j
[2025-09-20 02:16:03] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -67.353721-0.000427j
[2025-09-20 02:16:35] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -67.343037-0.000299j
[2025-09-20 02:17:07] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -67.345057+0.002072j
[2025-09-20 02:17:39] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -67.330335-0.001236j
[2025-09-20 02:18:11] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -67.337729-0.003533j
[2025-09-20 02:18:43] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -67.328511-0.002088j
[2025-09-20 02:19:15] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -67.345117+0.000380j
[2025-09-20 02:19:47] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -67.340683-0.003878j
[2025-09-20 02:20:19] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -67.346312-0.000103j
[2025-09-20 02:20:51] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -67.344939+0.000576j
[2025-09-20 02:20:51] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-20 02:21:23] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -67.348485+0.003951j
[2025-09-20 02:21:55] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -67.345591+0.000671j
[2025-09-20 02:22:27] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -67.350010+0.004561j
[2025-09-20 02:22:59] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -67.338817+0.001169j
[2025-09-20 02:23:31] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -67.340798-0.001484j
[2025-09-20 02:24:03] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -67.345272+0.002028j
[2025-09-20 02:24:35] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -67.327752-0.003199j
[2025-09-20 02:25:07] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -67.346587+0.003148j
[2025-09-20 02:25:39] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -67.339986+0.001303j
[2025-09-20 02:26:11] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -67.350469-0.002543j
[2025-09-20 02:26:43] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -67.328865-0.004094j
[2025-09-20 02:27:15] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -67.331561-0.001270j
[2025-09-20 02:27:47] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -67.353205-0.001464j
[2025-09-20 02:28:19] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -67.352198-0.003549j
[2025-09-20 02:28:51] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -67.348979+0.001801j
[2025-09-20 02:29:23] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -67.334831-0.001100j
[2025-09-20 02:29:55] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -67.348844-0.000649j
[2025-09-20 02:30:27] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -67.346002+0.003118j
[2025-09-20 02:30:59] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -67.333667-0.004941j
[2025-09-20 02:31:31] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -67.341941+0.002307j
[2025-09-20 02:32:03] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -67.347759+0.001894j
[2025-09-20 02:32:35] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -67.344501-0.000167j
[2025-09-20 02:33:07] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -67.355912+0.001852j
[2025-09-20 02:33:39] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -67.344382+0.001798j
[2025-09-20 02:34:11] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -67.347240-0.002556j
[2025-09-20 02:34:44] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -67.338255+0.000642j
[2025-09-20 02:35:16] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -67.336256-0.001850j
[2025-09-20 02:35:48] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -67.358587+0.001302j
[2025-09-20 02:36:20] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -67.361433-0.001894j
[2025-09-20 02:36:52] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -67.346606-0.000302j
[2025-09-20 02:37:24] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -67.341281+0.000292j
[2025-09-20 02:37:56] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -67.338786-0.003338j
[2025-09-20 02:38:28] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -67.349317+0.005541j
[2025-09-20 02:39:00] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -67.338764+0.000584j
[2025-09-20 02:39:32] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -67.338513-0.000572j
[2025-09-20 02:40:04] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -67.348362+0.002818j
[2025-09-20 02:40:36] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -67.350372+0.001426j
[2025-09-20 02:41:08] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -67.352756-0.005434j
[2025-09-20 02:41:40] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -67.354569+0.002647j
[2025-09-20 02:42:12] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -67.335470-0.000812j
[2025-09-20 02:42:44] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -67.351323+0.001589j
[2025-09-20 02:43:17] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -67.341147-0.000095j
[2025-09-20 02:43:49] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -67.361136-0.001697j
[2025-09-20 02:44:21] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -67.341369+0.002136j
[2025-09-20 02:44:53] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -67.327349+0.000260j
[2025-09-20 02:45:25] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -67.340388+0.000806j
[2025-09-20 02:45:57] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -67.331531+0.000019j
[2025-09-20 02:46:29] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -67.346993-0.004403j
[2025-09-20 02:47:01] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -67.356389-0.002102j
[2025-09-20 02:47:33] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -67.346192+0.003315j
[2025-09-20 02:48:05] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -67.342573-0.000209j
[2025-09-20 02:48:37] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -67.342000-0.002251j
[2025-09-20 02:49:09] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -67.329384-0.002176j
[2025-09-20 02:49:41] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -67.336555-0.001789j
[2025-09-20 02:50:13] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -67.350094+0.000550j
[2025-09-20 02:50:45] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -67.344337+0.002301j
[2025-09-20 02:51:17] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -67.339539-0.000838j
[2025-09-20 02:51:49] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -67.346627+0.007960j
[2025-09-20 02:52:21] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -67.334110-0.002060j
[2025-09-20 02:52:53] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -67.343913-0.003588j
[2025-09-20 02:53:25] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -67.338289+0.002581j
[2025-09-20 02:53:57] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -67.351328-0.002541j
[2025-09-20 02:54:29] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -67.358924+0.005166j
[2025-09-20 02:55:01] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -67.330133-0.000591j
[2025-09-20 02:55:33] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -67.351089-0.002047j
[2025-09-20 02:56:05] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -67.335946-0.004243j
[2025-09-20 02:56:38] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -67.380179-0.003160j
[2025-09-20 02:57:10] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -67.339480+0.000734j
[2025-09-20 02:57:42] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -67.364553+0.000068j
[2025-09-20 02:58:14] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -67.365566-0.000531j
[2025-09-20 02:58:46] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -67.358580+0.001582j
[2025-09-20 02:59:18] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -67.365131+0.000971j
[2025-09-20 02:59:50] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -67.329089-0.001065j
[2025-09-20 03:00:22] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -67.346827-0.002741j
[2025-09-20 03:00:54] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -67.338910+0.000002j
[2025-09-20 03:01:26] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -67.350940+0.001371j
[2025-09-20 03:01:58] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -67.345285-0.000639j
[2025-09-20 03:02:30] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -67.342466-0.000994j
[2025-09-20 03:03:02] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -67.340835+0.000475j
[2025-09-20 03:03:34] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -67.330558-0.003382j
[2025-09-20 03:04:06] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -67.345605+0.003569j
[2025-09-20 03:04:38] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -67.354504-0.001694j
[2025-09-20 03:05:10] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -67.332164+0.001401j
[2025-09-20 03:05:42] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -67.332897+0.002678j
[2025-09-20 03:06:14] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -67.339486-0.001103j
[2025-09-20 03:06:47] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -67.345619-0.000798j
[2025-09-20 03:07:18] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -67.342997+0.002981j
[2025-09-20 03:07:51] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -67.348060+0.000539j
[2025-09-20 03:08:23] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -67.356734-0.000801j
[2025-09-20 03:08:55] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -67.343684+0.000517j
[2025-09-20 03:09:27] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -67.340190-0.006521j
[2025-09-20 03:09:59] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -67.343087+0.001878j
[2025-09-20 03:10:31] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -67.328087+0.000826j
[2025-09-20 03:11:03] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -67.326772-0.001436j
[2025-09-20 03:11:35] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -67.341271+0.002044j
[2025-09-20 03:12:07] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -67.345372+0.000231j
[2025-09-20 03:12:39] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -67.340987-0.002145j
[2025-09-20 03:13:11] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -67.335201-0.002386j
[2025-09-20 03:13:43] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -67.356135+0.010945j
[2025-09-20 03:14:15] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -67.329163-0.003354j
[2025-09-20 03:14:47] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -67.349569+0.000046j
[2025-09-20 03:15:19] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -67.348165+0.003752j
[2025-09-20 03:15:51] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -67.336248-0.001089j
[2025-09-20 03:16:23] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -67.342386+0.000602j
[2025-09-20 03:16:56] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -67.325446-0.000031j
[2025-09-20 03:16:56] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-20 03:17:28] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -67.358153+0.000637j
[2025-09-20 03:18:00] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -67.328869+0.000956j
[2025-09-20 03:18:32] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -67.365627-0.002050j
[2025-09-20 03:19:04] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -67.337635+0.001472j
[2025-09-20 03:19:36] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -67.339252+0.002031j
[2025-09-20 03:20:08] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -67.353362+0.003466j
[2025-09-20 03:20:40] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -67.335046+0.002589j
[2025-09-20 03:21:12] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -67.348887+0.002068j
[2025-09-20 03:21:44] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -67.342625+0.000631j
[2025-09-20 03:22:16] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -67.353987+0.002245j
[2025-09-20 03:22:48] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -67.341828-0.007392j
[2025-09-20 03:23:20] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -67.356745+0.000419j
[2025-09-20 03:23:52] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -67.344600+0.000386j
[2025-09-20 03:24:24] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -67.347009+0.002971j
[2025-09-20 03:24:56] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -67.346456-0.001867j
[2025-09-20 03:25:28] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -67.328968+0.000249j
[2025-09-20 03:26:00] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -67.346321-0.000380j
[2025-09-20 03:26:31] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -67.347961+0.001767j
[2025-09-20 03:27:03] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -67.338778-0.001901j
[2025-09-20 03:27:35] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -67.338068+0.002484j
[2025-09-20 03:28:07] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -67.346876-0.002031j
[2025-09-20 03:28:39] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -67.350577-0.002592j
[2025-09-20 03:29:11] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -67.354049+0.002326j
[2025-09-20 03:29:43] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -67.332621-0.001021j
[2025-09-20 03:30:15] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -67.332536+0.001577j
[2025-09-20 03:30:47] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -67.352844+0.000474j
[2025-09-20 03:31:19] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -67.345068+0.002649j
[2025-09-20 03:31:51] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -67.336967-0.000157j
[2025-09-20 03:32:23] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -67.334966+0.001929j
[2025-09-20 03:32:54] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -67.337325+0.000737j
[2025-09-20 03:32:54] RESTART #2 | Period: 600
[2025-09-20 03:33:26] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -67.342008+0.000590j
[2025-09-20 03:33:58] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -67.340050+0.001807j
[2025-09-20 03:34:30] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -67.320067+0.000338j
[2025-09-20 03:35:02] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -67.348364+0.000958j
[2025-09-20 03:35:34] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -67.349356+0.000079j
[2025-09-20 03:36:06] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -67.340456-0.001918j
[2025-09-20 03:36:38] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -67.351973+0.000513j
[2025-09-20 03:37:10] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -67.340242+0.000479j
[2025-09-20 03:37:42] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -67.338208+0.000598j
[2025-09-20 03:38:14] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -67.352797-0.001059j
[2025-09-20 03:38:45] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -67.357071-0.001917j
[2025-09-20 03:39:17] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -67.345840-0.001608j
[2025-09-20 03:39:49] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -67.347560+0.001580j
[2025-09-20 03:40:21] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -67.350856+0.001235j
[2025-09-20 03:40:53] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -67.350299-0.001615j
[2025-09-20 03:41:25] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -67.346094-0.001920j
[2025-09-20 03:41:57] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -67.349842+0.001033j
[2025-09-20 03:42:29] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -67.348039-0.002521j
[2025-09-20 03:43:01] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -67.359739-0.002355j
[2025-09-20 03:43:33] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -67.341450-0.002215j
[2025-09-20 03:44:05] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -67.332082+0.000298j
[2025-09-20 03:44:37] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -67.352562-0.001316j
[2025-09-20 03:45:08] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -67.348269-0.002392j
[2025-09-20 03:45:40] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -67.350764+0.000570j
[2025-09-20 03:46:12] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -67.343794-0.001556j
[2025-09-20 03:46:44] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -67.349528+0.004627j
[2025-09-20 03:47:16] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -67.356395-0.005034j
[2025-09-20 03:47:48] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -67.354724+0.000652j
[2025-09-20 03:48:20] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -67.344722-0.003040j
[2025-09-20 03:48:53] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -67.335158+0.000391j
[2025-09-20 03:49:25] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -67.340061-0.002792j
[2025-09-20 03:49:57] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -67.350607+0.003974j
[2025-09-20 03:50:29] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -67.350776+0.000170j
[2025-09-20 03:51:01] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -67.349293+0.000166j
[2025-09-20 03:51:33] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -67.345815-0.000955j
[2025-09-20 03:52:05] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -67.345745+0.002832j
[2025-09-20 03:52:37] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -67.332027+0.001915j
[2025-09-20 03:53:09] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -67.333394-0.001290j
[2025-09-20 03:53:41] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -67.335611-0.000428j
[2025-09-20 03:54:13] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -67.338712+0.000792j
[2025-09-20 03:54:45] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -67.339109+0.004024j
[2025-09-20 03:55:17] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -67.325300+0.002131j
[2025-09-20 03:55:49] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -67.352244+0.000761j
[2025-09-20 03:56:21] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -67.324539+0.002916j
[2025-09-20 03:56:53] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -67.329603+0.001577j
[2025-09-20 03:57:25] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -67.355456+0.001872j
[2025-09-20 03:57:58] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -67.337044+0.003291j
[2025-09-20 03:58:30] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -67.343575-0.001538j
[2025-09-20 03:59:02] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -67.342173+0.002096j
[2025-09-20 03:59:34] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -67.335493-0.001035j
[2025-09-20 04:00:06] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -67.353304-0.000971j
[2025-09-20 04:00:38] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -67.333611-0.000559j
[2025-09-20 04:01:10] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -67.344753-0.003001j
[2025-09-20 04:01:42] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -67.358089-0.002687j
[2025-09-20 04:02:14] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -67.349241-0.008695j
[2025-09-20 04:02:46] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -67.352572-0.002252j
[2025-09-20 04:03:18] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -67.345085+0.001935j
[2025-09-20 04:03:50] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -67.336406+0.000675j
[2025-09-20 04:04:22] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -67.345630+0.000884j
[2025-09-20 04:04:54] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -67.345402-0.007980j
[2025-09-20 04:05:26] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -67.355361-0.004103j
[2025-09-20 04:05:59] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -67.321912+0.005251j
[2025-09-20 04:06:30] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -67.359866+0.000993j
[2025-09-20 04:07:02] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -67.354478+0.001934j
[2025-09-20 04:07:34] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -67.367987+0.002455j
[2025-09-20 04:08:06] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -67.337100+0.002202j
[2025-09-20 04:08:38] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -67.359376-0.005387j
[2025-09-20 04:09:10] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -67.345853+0.000736j
[2025-09-20 04:09:42] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -67.363863-0.000146j
[2025-09-20 04:10:14] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -67.353745-0.001195j
[2025-09-20 04:10:46] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -67.337514-0.003071j
[2025-09-20 04:11:18] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -67.344974+0.002123j
[2025-09-20 04:11:50] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -67.343685+0.001095j
[2025-09-20 04:12:21] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -67.352453+0.002204j
[2025-09-20 04:12:53] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -67.337152-0.001472j
[2025-09-20 04:12:53] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-20 04:13:25] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -67.349167-0.001521j
[2025-09-20 04:13:57] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -67.364477-0.001740j
[2025-09-20 04:14:29] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -67.352184-0.000186j
[2025-09-20 04:15:01] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -67.332069-0.000612j
[2025-09-20 04:15:33] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -67.341235+0.000531j
[2025-09-20 04:16:05] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -67.365997-0.000362j
[2025-09-20 04:16:37] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -67.333986-0.002483j
[2025-09-20 04:17:09] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -67.339894-0.001330j
[2025-09-20 04:17:41] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -67.345432-0.001969j
[2025-09-20 04:18:13] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -67.347056+0.000504j
[2025-09-20 04:18:45] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -67.353800-0.000547j
[2025-09-20 04:19:17] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -67.350895+0.001797j
[2025-09-20 04:19:49] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -67.340781+0.005600j
[2025-09-20 04:20:21] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -67.338584-0.000911j
[2025-09-20 04:20:53] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -67.349384+0.001450j
[2025-09-20 04:21:25] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -67.338536+0.001974j
[2025-09-20 04:21:57] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -67.353237-0.002585j
[2025-09-20 04:22:29] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -67.354518-0.001180j
[2025-09-20 04:23:01] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -67.342189-0.001226j
[2025-09-20 04:23:33] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -67.351541-0.000897j
[2025-09-20 04:24:05] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -67.359153-0.000495j
[2025-09-20 04:24:38] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -67.361850-0.000181j
[2025-09-20 04:25:09] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -67.344258+0.000750j
[2025-09-20 04:25:42] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -67.339830+0.000945j
[2025-09-20 04:26:14] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -67.343769+0.002692j
[2025-09-20 04:26:46] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -67.337763+0.000781j
[2025-09-20 04:27:18] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -67.340428-0.000727j
[2025-09-20 04:27:50] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -67.334369+0.004826j
[2025-09-20 04:28:22] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -67.354799+0.002255j
[2025-09-20 04:28:54] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -67.353163-0.001733j
[2025-09-20 04:29:26] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -67.348317+0.002807j
[2025-09-20 04:29:58] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -67.345827+0.001263j
[2025-09-20 04:30:30] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -67.335622-0.002022j
[2025-09-20 04:31:02] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -67.349971-0.003520j
[2025-09-20 04:31:34] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -67.338252-0.001202j
[2025-09-20 04:32:06] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -67.344411-0.000132j
[2025-09-20 04:32:38] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -67.352804+0.000429j
[2025-09-20 04:33:10] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -67.329702-0.000450j
[2025-09-20 04:33:42] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -67.354268-0.003794j
[2025-09-20 04:34:14] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -67.349008-0.002035j
[2025-09-20 04:34:46] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -67.349784-0.000299j
[2025-09-20 04:35:18] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -67.344300-0.000241j
[2025-09-20 04:35:50] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -67.357805+0.003046j
[2025-09-20 04:36:22] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -67.349547-0.001719j
[2025-09-20 04:36:54] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -67.348288-0.000147j
[2025-09-20 04:37:26] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -67.343562-0.002731j
[2025-09-20 04:37:58] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -67.352708-0.002026j
[2025-09-20 04:38:30] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -67.334672+0.001219j
[2025-09-20 04:39:02] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -67.344995-0.001789j
[2025-09-20 04:39:34] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -67.346455+0.000523j
[2025-09-20 04:40:07] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -67.360135-0.000071j
[2025-09-20 04:40:39] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -67.352873+0.000449j
[2025-09-20 04:41:11] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -67.335861+0.001265j
[2025-09-20 04:41:43] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -67.358982+0.004355j
[2025-09-20 04:42:15] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -67.341821+0.002821j
[2025-09-20 04:42:47] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -67.339357+0.000419j
[2025-09-20 04:43:19] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -67.337147+0.002126j
[2025-09-20 04:43:51] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -67.330006-0.002926j
[2025-09-20 04:44:23] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -67.343763+0.002545j
[2025-09-20 04:44:55] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -67.337300-0.000283j
[2025-09-20 04:45:27] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -67.356735-0.001010j
[2025-09-20 04:45:59] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -67.347409-0.006386j
[2025-09-20 04:46:31] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -67.344124-0.000980j
[2025-09-20 04:47:03] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -67.349140-0.000032j
[2025-09-20 04:47:35] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -67.349937-0.000324j
[2025-09-20 04:48:07] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -67.354566-0.000207j
[2025-09-20 04:48:39] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -67.350090+0.003177j
[2025-09-20 04:49:11] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -67.324128+0.000334j
[2025-09-20 04:49:43] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -67.353717+0.002931j
[2025-09-20 04:50:15] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -67.342146-0.004884j
[2025-09-20 04:50:47] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -67.350339-0.004890j
[2025-09-20 04:51:19] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -67.342221-0.001826j
[2025-09-20 04:51:51] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -67.341959-0.000424j
[2025-09-20 04:52:23] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -67.358528-0.004104j
[2025-09-20 04:52:55] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -67.362858+0.001008j
[2025-09-20 04:53:27] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -67.344521-0.003438j
[2025-09-20 04:53:58] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -67.357337-0.002792j
[2025-09-20 04:54:30] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -67.353074+0.002041j
[2025-09-20 04:55:02] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -67.333946-0.002159j
[2025-09-20 04:55:34] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -67.355507+0.001462j
[2025-09-20 04:56:06] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -67.351320-0.005244j
[2025-09-20 04:56:38] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -67.341690+0.001026j
[2025-09-20 04:57:10] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -67.336942-0.000633j
[2025-09-20 04:57:42] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -67.341301+0.002586j
[2025-09-20 04:58:14] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -67.338468-0.002398j
[2025-09-20 04:58:46] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -67.351488-0.003300j
[2025-09-20 04:59:18] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -67.336719+0.002690j
[2025-09-20 04:59:50] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -67.346710+0.003660j
[2025-09-20 05:00:22] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -67.356536+0.001395j
[2025-09-20 05:00:54] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -67.348007-0.001166j
[2025-09-20 05:01:26] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -67.342378-0.006084j
[2025-09-20 05:01:58] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -67.342157+0.000869j
[2025-09-20 05:02:30] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -67.350296-0.002204j
[2025-09-20 05:03:02] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -67.356794-0.000658j
[2025-09-20 05:03:34] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -67.338894+0.002446j
[2025-09-20 05:04:06] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -67.347794-0.003820j
[2025-09-20 05:04:39] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -67.349324-0.000373j
[2025-09-20 05:05:11] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -67.357308+0.000712j
[2025-09-20 05:05:43] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -67.354794-0.000205j
[2025-09-20 05:06:14] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -67.364960-0.001624j
[2025-09-20 05:06:46] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -67.350831+0.003388j
[2025-09-20 05:07:18] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -67.333713+0.001906j
[2025-09-20 05:07:50] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -67.348562+0.003427j
[2025-09-20 05:08:22] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -67.353339+0.001146j
[2025-09-20 05:08:54] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -67.358753-0.000010j
[2025-09-20 05:08:54] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-20 05:09:26] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -67.358645+0.000127j
[2025-09-20 05:09:58] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -67.340219-0.003053j
[2025-09-20 05:10:29] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -67.348693-0.002935j
[2025-09-20 05:11:01] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -67.328976+0.000903j
[2025-09-20 05:11:33] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -67.348175+0.000773j
[2025-09-20 05:12:05] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -67.346053-0.000244j
[2025-09-20 05:12:37] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -67.337954-0.003679j
[2025-09-20 05:13:09] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -67.354452-0.001448j
[2025-09-20 05:13:41] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -67.351530+0.003091j
[2025-09-20 05:14:13] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -67.338993-0.000822j
[2025-09-20 05:14:45] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -67.351064+0.001916j
[2025-09-20 05:15:17] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -67.339259-0.000930j
[2025-09-20 05:15:49] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -67.334283+0.001352j
[2025-09-20 05:16:21] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -67.333136-0.000736j
[2025-09-20 05:16:53] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -67.368556+0.001877j
[2025-09-20 05:17:24] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -67.350347+0.001250j
[2025-09-20 05:17:56] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -67.345314+0.001642j
[2025-09-20 05:18:28] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -67.340225-0.001618j
[2025-09-20 05:19:00] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -67.336564+0.001917j
[2025-09-20 05:19:32] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -67.352817+0.003767j
[2025-09-20 05:20:04] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -67.355969+0.001779j
[2025-09-20 05:20:36] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -67.341679-0.002929j
[2025-09-20 05:21:08] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -67.352930-0.000533j
[2025-09-20 05:21:40] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -67.343257-0.000562j
[2025-09-20 05:22:12] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -67.341559+0.001111j
[2025-09-20 05:22:44] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -67.354408-0.002624j
[2025-09-20 05:23:15] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -67.347611-0.002166j
[2025-09-20 05:23:47] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -67.347167-0.000754j
[2025-09-20 05:24:19] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -67.352882+0.000312j
[2025-09-20 05:24:51] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -67.345014+0.000405j
[2025-09-20 05:25:23] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -67.357099+0.001012j
[2025-09-20 05:25:55] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -67.350824-0.001702j
[2025-09-20 05:26:27] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -67.354699+0.000112j
[2025-09-20 05:26:59] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -67.348447-0.001022j
[2025-09-20 05:27:31] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -67.348803-0.004787j
[2025-09-20 05:28:03] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -67.352096+0.001202j
[2025-09-20 05:28:35] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -67.326912+0.003686j
[2025-09-20 05:29:07] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -67.355996-0.004372j
[2025-09-20 05:29:38] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -67.340920+0.001482j
[2025-09-20 05:30:10] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -67.340956+0.001444j
[2025-09-20 05:30:42] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -67.347595-0.006612j
[2025-09-20 05:31:14] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -67.346285-0.000673j
[2025-09-20 05:31:46] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -67.349299-0.003221j
[2025-09-20 05:32:18] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -67.357372-0.000799j
[2025-09-20 05:32:50] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -67.359204-0.001713j
[2025-09-20 05:33:22] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -67.327048+0.003877j
[2025-09-20 05:33:54] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -67.348871+0.001763j
[2025-09-20 05:34:26] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -67.349882-0.003007j
[2025-09-20 05:34:57] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -67.337877+0.000925j
[2025-09-20 05:35:29] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -67.351745-0.000673j
[2025-09-20 05:36:01] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -67.348089-0.000446j
[2025-09-20 05:36:33] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -67.340309-0.001170j
[2025-09-20 05:37:05] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -67.359063+0.003463j
[2025-09-20 05:37:37] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -67.355847-0.002021j
[2025-09-20 05:38:09] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -67.346500-0.002139j
[2025-09-20 05:38:41] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -67.351978+0.003913j
[2025-09-20 05:39:13] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -67.347699-0.000973j
[2025-09-20 05:39:45] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -67.348832+0.002996j
[2025-09-20 05:40:17] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -67.353171-0.003367j
[2025-09-20 05:40:49] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -67.346327+0.001777j
[2025-09-20 05:41:21] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -67.343251-0.002565j
[2025-09-20 05:41:53] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -67.338700+0.000873j
[2025-09-20 05:42:25] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -67.350266+0.001631j
[2025-09-20 05:42:57] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -67.350808-0.001229j
[2025-09-20 05:43:29] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -67.346935-0.003782j
[2025-09-20 05:44:01] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -67.342658+0.005354j
[2025-09-20 05:44:33] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -67.352680-0.004126j
[2025-09-20 05:45:05] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -67.341512-0.004641j
[2025-09-20 05:45:37] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -67.357957-0.001128j
[2025-09-20 05:46:10] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -67.344466+0.000993j
[2025-09-20 05:46:42] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -67.342013+0.002839j
[2025-09-20 05:47:14] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -67.337863+0.000361j
[2025-09-20 05:47:46] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -67.347569-0.000410j
[2025-09-20 05:48:18] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -67.353459-0.001935j
[2025-09-20 05:48:50] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -67.371138-0.001490j
[2025-09-20 05:49:22] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -67.349583-0.002218j
[2025-09-20 05:49:54] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -67.321618-0.024953j
[2025-09-20 05:50:26] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -67.354633-0.002517j
[2025-09-20 05:50:58] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -67.341564-0.000876j
[2025-09-20 05:51:30] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -67.336215+0.004310j
[2025-09-20 05:52:02] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -67.341132+0.004027j
[2025-09-20 05:52:34] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -67.329672-0.000746j
[2025-09-20 05:53:06] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -67.356876+0.001149j
[2025-09-20 05:53:38] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -67.328822-0.000723j
[2025-09-20 05:54:10] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -67.353279-0.001634j
[2025-09-20 05:54:42] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -67.354722-0.001520j
[2025-09-20 05:55:14] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -67.356826+0.000261j
[2025-09-20 05:55:46] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -67.350610-0.002587j
[2025-09-20 05:56:18] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -67.343126+0.000366j
[2025-09-20 05:56:50] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -67.348392+0.003428j
[2025-09-20 05:57:22] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -67.350386-0.000731j
[2025-09-20 05:57:54] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -67.367946+0.002140j
[2025-09-20 05:58:26] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -67.339049+0.000706j
[2025-09-20 05:58:58] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -67.346865-0.001229j
[2025-09-20 05:59:30] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -67.347490-0.000690j
[2025-09-20 06:00:03] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -67.339588-0.002659j
[2025-09-20 06:00:35] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -67.347875+0.000511j
[2025-09-20 06:01:07] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -67.336687-0.000483j
[2025-09-20 06:01:39] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -67.352177-0.000750j
[2025-09-20 06:02:11] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -67.346734+0.004464j
[2025-09-20 06:02:43] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -67.357025-0.003286j
[2025-09-20 06:03:15] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -67.340116-0.001566j
[2025-09-20 06:03:47] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -67.373564+0.000435j
[2025-09-20 06:04:19] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -67.360255+0.004560j
[2025-09-20 06:04:51] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -67.339113+0.001175j
[2025-09-20 06:04:51] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-20 06:05:23] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -67.355558+0.000807j
[2025-09-20 06:05:55] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -67.342362-0.000035j
[2025-09-20 06:06:27] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -67.355828-0.002453j
[2025-09-20 06:06:59] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -67.351359+0.002224j
[2025-09-20 06:07:31] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -67.360805-0.000152j
[2025-09-20 06:08:03] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -67.335892+0.003753j
[2025-09-20 06:08:35] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -67.349297+0.000943j
[2025-09-20 06:09:07] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -67.334831+0.001578j
[2025-09-20 06:09:39] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -67.359336-0.000888j
[2025-09-20 06:10:11] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -67.364706+0.004227j
[2025-09-20 06:10:43] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -67.353524-0.002009j
[2025-09-20 06:11:16] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -67.346355+0.001222j
[2025-09-20 06:11:48] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -67.347417-0.002902j
[2025-09-20 06:12:20] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -67.351526+0.000857j
[2025-09-20 06:12:52] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -67.341927+0.000537j
[2025-09-20 06:13:24] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -67.346085+0.001662j
[2025-09-20 06:13:56] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -67.342605-0.001411j
[2025-09-20 06:14:28] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -67.341363+0.000258j
[2025-09-20 06:15:00] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -67.341266-0.002764j
[2025-09-20 06:15:32] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -67.355543+0.003831j
[2025-09-20 06:16:04] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -67.351344+0.002189j
[2025-09-20 06:16:36] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -67.340737+0.000140j
[2025-09-20 06:17:08] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -67.347431+0.003775j
[2025-09-20 06:17:40] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -67.348959-0.000131j
[2025-09-20 06:18:12] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -67.346955+0.001330j
[2025-09-20 06:18:45] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -67.356897-0.003002j
[2025-09-20 06:19:17] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -67.341236+0.000798j
[2025-09-20 06:19:49] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -67.360432-0.002893j
[2025-09-20 06:20:21] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -67.333708-0.000794j
[2025-09-20 06:20:53] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -67.358475+0.005138j
[2025-09-20 06:21:25] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -67.354086+0.000852j
[2025-09-20 06:21:57] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -67.357400-0.006179j
[2025-09-20 06:22:29] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -67.359739-0.000173j
[2025-09-20 06:23:01] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -67.348735-0.000122j
[2025-09-20 06:23:33] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -67.350326+0.000301j
[2025-09-20 06:24:05] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -67.343037+0.001672j
[2025-09-20 06:24:37] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -67.339362+0.002838j
[2025-09-20 06:25:09] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -67.360567-0.000804j
[2025-09-20 06:25:41] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -67.355873-0.000348j
[2025-09-20 06:26:13] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -67.334706+0.002306j
[2025-09-20 06:26:45] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -67.363693-0.000951j
[2025-09-20 06:27:17] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -67.356009-0.002079j
[2025-09-20 06:27:49] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -67.334523-0.000924j
[2025-09-20 06:28:21] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -67.358310+0.002312j
[2025-09-20 06:28:53] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -67.349808-0.001695j
[2025-09-20 06:29:25] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -67.329031+0.001045j
[2025-09-20 06:29:57] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -67.363204+0.000734j
[2025-09-20 06:30:29] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -67.341754-0.001101j
[2025-09-20 06:31:01] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -67.351193+0.000076j
[2025-09-20 06:31:33] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -67.361274+0.000452j
[2025-09-20 06:32:05] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -67.347760+0.000713j
[2025-09-20 06:32:38] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -67.346851+0.001396j
[2025-09-20 06:33:10] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -67.340889+0.002836j
[2025-09-20 06:33:42] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -67.359593+0.001734j
[2025-09-20 06:34:14] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -67.364248-0.003544j
[2025-09-20 06:34:46] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -67.350352-0.000415j
[2025-09-20 06:35:18] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -67.343429-0.003602j
[2025-09-20 06:35:50] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -67.333481+0.000794j
[2025-09-20 06:36:22] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -67.347394+0.001461j
[2025-09-20 06:36:54] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -67.351604-0.002128j
[2025-09-20 06:37:26] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -67.353107-0.001739j
[2025-09-20 06:37:58] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -67.329418+0.001103j
[2025-09-20 06:38:30] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -67.334944-0.001589j
[2025-09-20 06:39:02] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -67.367988-0.007816j
[2025-09-20 06:39:34] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -67.343920+0.000061j
[2025-09-20 06:40:06] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -67.341409+0.000313j
[2025-09-20 06:40:38] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -67.344615+0.002442j
[2025-09-20 06:41:10] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -67.356802+0.000994j
[2025-09-20 06:41:42] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -67.349089-0.002593j
[2025-09-20 06:42:15] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -67.349636-0.000048j
[2025-09-20 06:42:46] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -67.340875-0.001630j
[2025-09-20 06:43:18] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -67.349213-0.002011j
[2025-09-20 06:43:50] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -67.358231-0.001461j
[2025-09-20 06:44:22] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -67.344727-0.000754j
[2025-09-20 06:44:54] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -67.337551-0.000312j
[2025-09-20 06:45:26] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -67.364096-0.002423j
[2025-09-20 06:45:58] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -67.347579-0.007776j
[2025-09-20 06:46:30] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -67.347420+0.000011j
[2025-09-20 06:47:02] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -67.345778+0.000377j
[2025-09-20 06:47:34] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -67.355758-0.001802j
[2025-09-20 06:48:06] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -67.338312+0.000626j
[2025-09-20 06:48:38] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -67.351667-0.000097j
[2025-09-20 06:49:10] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -67.342050+0.001092j
[2025-09-20 06:49:42] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -67.353620+0.001094j
[2025-09-20 06:50:14] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -67.351332-0.001609j
[2025-09-20 06:50:46] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -67.344054-0.001584j
[2025-09-20 06:51:18] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -67.343283+0.000517j
[2025-09-20 06:51:50] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -67.357675+0.000064j
[2025-09-20 06:52:22] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -67.365806-0.001045j
[2025-09-20 06:52:54] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -67.351704+0.003996j
[2025-09-20 06:53:26] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -67.352741-0.001704j
[2025-09-20 06:53:58] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -67.354301+0.000343j
[2025-09-20 06:54:31] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -67.351133-0.001369j
[2025-09-20 06:55:03] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -67.369183+0.002243j
[2025-09-20 06:55:35] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -67.343401+0.002869j
[2025-09-20 06:56:07] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -67.346736+0.000639j
[2025-09-20 06:56:39] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -67.352313+0.003188j
[2025-09-20 06:57:11] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -67.340401-0.002755j
[2025-09-20 06:57:43] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -67.357818-0.003809j
[2025-09-20 06:58:15] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -67.349162-0.002221j
[2025-09-20 06:58:47] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -67.349871+0.003621j
[2025-09-20 06:59:19] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -67.365251-0.000914j
[2025-09-20 06:59:51] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -67.354688-0.002951j
[2025-09-20 07:00:23] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -67.341125+0.003221j
[2025-09-20 07:00:55] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -67.354746-0.001180j
[2025-09-20 07:00:55] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-20 07:01:27] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -67.338940+0.000383j
[2025-09-20 07:01:59] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -67.346232+0.000459j
[2025-09-20 07:02:32] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -67.361535-0.000746j
[2025-09-20 07:03:04] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -67.357317-0.000197j
[2025-09-20 07:03:36] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -67.343633+0.000781j
[2025-09-20 07:04:08] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -67.331330-0.001517j
[2025-09-20 07:04:40] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -67.352471-0.000925j
[2025-09-20 07:05:12] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -67.358812-0.003352j
[2025-09-20 07:05:44] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -67.347280-0.002910j
[2025-09-20 07:06:16] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -67.344316-0.001565j
[2025-09-20 07:06:48] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -67.354614+0.002447j
[2025-09-20 07:07:20] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -67.357677-0.000125j
[2025-09-20 07:07:52] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -67.354610+0.002779j
[2025-09-20 07:08:24] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -67.344873-0.002359j
[2025-09-20 07:08:56] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -67.349767-0.002099j
[2025-09-20 07:09:28] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -67.365690-0.004208j
[2025-09-20 07:10:00] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -67.350541+0.003562j
[2025-09-20 07:10:32] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -67.353184-0.001186j
[2025-09-20 07:11:04] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -67.356292-0.002076j
[2025-09-20 07:11:36] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -67.359320+0.001395j
[2025-09-20 07:12:08] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -67.353205-0.000372j
[2025-09-20 07:12:40] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -67.345543-0.002866j
[2025-09-20 07:13:12] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -67.345986+0.001654j
[2025-09-20 07:13:44] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -67.340823-0.001397j
[2025-09-20 07:14:16] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -67.325324+0.000942j
[2025-09-20 07:14:48] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -67.343081+0.003185j
[2025-09-20 07:15:20] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -67.342041-0.001902j
[2025-09-20 07:15:53] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -67.339446-0.001567j
[2025-09-20 07:16:25] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -67.313677+0.008419j
[2025-09-20 07:16:57] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -67.341396+0.001115j
[2025-09-20 07:17:29] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -67.345308+0.003971j
[2025-09-20 07:18:01] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -67.335633+0.000250j
[2025-09-20 07:18:33] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -67.367952-0.006053j
[2025-09-20 07:19:05] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -67.356278+0.000629j
[2025-09-20 07:19:37] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -67.343182-0.001992j
[2025-09-20 07:20:09] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -67.336738+0.003251j
[2025-09-20 07:20:41] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -67.355781+0.000715j
[2025-09-20 07:21:13] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -67.336662+0.000344j
[2025-09-20 07:21:45] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -67.355340+0.000054j
[2025-09-20 07:22:17] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -67.350300+0.000411j
[2025-09-20 07:22:49] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -67.353450-0.001125j
[2025-09-20 07:23:21] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -67.348061+0.008052j
[2025-09-20 07:23:53] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -67.358310-0.003398j
[2025-09-20 07:24:25] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -67.350295+0.000704j
[2025-09-20 07:24:57] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -67.351545+0.001981j
[2025-09-20 07:25:29] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -67.351154-0.003946j
[2025-09-20 07:26:02] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -67.368633-0.003888j
[2025-09-20 07:26:33] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -67.330808+0.000089j
[2025-09-20 07:27:05] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -67.358073-0.001750j
[2025-09-20 07:27:38] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -67.352438+0.000519j
[2025-09-20 07:28:10] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -67.344531+0.000361j
[2025-09-20 07:28:42] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -67.344809-0.000498j
[2025-09-20 07:29:14] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -67.353023+0.003632j
[2025-09-20 07:29:46] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -67.343680+0.002657j
[2025-09-20 07:30:18] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -67.338392-0.001333j
[2025-09-20 07:30:50] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -67.336421+0.000136j
[2025-09-20 07:31:22] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -67.346832-0.005662j
[2025-09-20 07:31:54] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -67.361231-0.002366j
[2025-09-20 07:32:26] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -67.361788+0.000811j
[2025-09-20 07:32:58] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -67.354529-0.002380j
[2025-09-20 07:33:30] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -67.334571+0.001932j
[2025-09-20 07:34:02] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -67.355459+0.001969j
[2025-09-20 07:34:33] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -67.352523-0.002117j
[2025-09-20 07:35:05] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -67.348811-0.000132j
[2025-09-20 07:35:37] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -67.352361+0.001503j
[2025-09-20 07:36:09] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -67.370270+0.000554j
[2025-09-20 07:36:41] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -67.330034-0.000909j
[2025-09-20 07:37:13] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -67.352869-0.002804j
[2025-09-20 07:37:45] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -67.366634+0.001718j
[2025-09-20 07:38:17] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -67.341602+0.002020j
[2025-09-20 07:38:49] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -67.345811+0.000473j
[2025-09-20 07:39:21] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -67.346735+0.000366j
[2025-09-20 07:39:52] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -67.345681+0.000167j
[2025-09-20 07:40:24] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -67.354394+0.001063j
[2025-09-20 07:40:56] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -67.343866+0.004007j
[2025-09-20 07:41:28] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -67.351097-0.000769j
[2025-09-20 07:42:00] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -67.356184+0.001339j
[2025-09-20 07:42:32] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -67.368456+0.001531j
[2025-09-20 07:43:04] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -67.346746-0.000144j
[2025-09-20 07:43:36] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -67.352169-0.006528j
[2025-09-20 07:44:08] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -67.338332+0.000210j
[2025-09-20 07:44:40] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -67.340351-0.001727j
[2025-09-20 07:45:12] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -67.343640+0.001647j
[2025-09-20 07:45:44] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -67.355737+0.005750j
[2025-09-20 07:46:16] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -67.365679+0.001670j
[2025-09-20 07:46:47] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -67.343569+0.001720j
[2025-09-20 07:47:19] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -67.345374+0.000421j
[2025-09-20 07:47:51] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -67.350847+0.000943j
[2025-09-20 07:48:23] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -67.353472-0.003112j
[2025-09-20 07:48:55] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -67.340353-0.003147j
[2025-09-20 07:49:27] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -67.352206-0.001260j
[2025-09-20 07:49:59] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -67.350401-0.000712j
[2025-09-20 07:50:31] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -67.333629-0.001971j
[2025-09-20 07:51:03] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -67.353992-0.001704j
[2025-09-20 07:51:35] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -67.358868+0.005798j
[2025-09-20 07:52:07] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -67.348517-0.007370j
[2025-09-20 07:52:39] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -67.328683-0.001677j
[2025-09-20 07:53:11] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -67.358276+0.002171j
[2025-09-20 07:53:43] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -67.341754-0.001465j
[2025-09-20 07:54:14] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -67.349841+0.001276j
[2025-09-20 07:54:46] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -67.347057-0.004242j
[2025-09-20 07:55:18] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -67.354621+0.001302j
[2025-09-20 07:55:50] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -67.371392+0.003071j
[2025-09-20 07:56:22] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -67.354442+0.001384j
[2025-09-20 07:56:54] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -67.339923-0.001636j
[2025-09-20 07:56:54] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-20 07:57:26] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -67.342848+0.001571j
[2025-09-20 07:57:58] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -67.333134+0.003279j
[2025-09-20 07:58:30] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -67.349005-0.001714j
[2025-09-20 07:59:02] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -67.360126-0.007449j
[2025-09-20 07:59:33] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -67.339075-0.001758j
[2025-09-20 08:00:06] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -67.344434+0.002886j
[2025-09-20 08:00:38] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -67.348977-0.001615j
[2025-09-20 08:01:10] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -67.362934-0.000685j
[2025-09-20 08:01:42] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -67.353657+0.001888j
[2025-09-20 08:02:14] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -67.365146+0.002662j
[2025-09-20 08:02:46] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -67.342753+0.001479j
[2025-09-20 08:03:18] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -67.358767-0.002200j
[2025-09-20 08:03:50] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -67.352801-0.003661j
[2025-09-20 08:04:22] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -67.345548+0.001064j
[2025-09-20 08:04:54] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -67.357723+0.000215j
[2025-09-20 08:05:26] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -67.348044+0.000325j
[2025-09-20 08:05:58] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -67.354423-0.002118j
[2025-09-20 08:06:30] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -67.350858+0.002047j
[2025-09-20 08:07:02] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -67.355452-0.001089j
[2025-09-20 08:07:34] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -67.350869-0.000627j
[2025-09-20 08:08:06] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -67.348913+0.002489j
[2025-09-20 08:08:38] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -67.361909+0.003864j
[2025-09-20 08:09:10] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -67.342095+0.001498j
[2025-09-20 08:09:42] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -67.342983+0.004665j
[2025-09-20 08:10:14] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -67.354504+0.002043j
[2025-09-20 08:10:47] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -67.330082-0.000584j
[2025-09-20 08:11:19] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -67.341951+0.001001j
[2025-09-20 08:11:51] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -67.353617-0.000903j
[2025-09-20 08:12:23] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -67.338189+0.002622j
[2025-09-20 08:12:55] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -67.354340+0.002043j
[2025-09-20 08:13:27] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -67.331039-0.002688j
[2025-09-20 08:13:59] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -67.351730+0.001237j
[2025-09-20 08:14:31] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -67.342132-0.000709j
[2025-09-20 08:15:03] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -67.353330-0.005367j
[2025-09-20 08:15:35] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -67.356427-0.002296j
[2025-09-20 08:16:07] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -67.355839-0.003467j
[2025-09-20 08:16:39] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -67.353933-0.001732j
[2025-09-20 08:17:11] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -67.357310+0.000192j
[2025-09-20 08:17:43] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -67.355100-0.001805j
[2025-09-20 08:18:15] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -67.341272-0.001120j
[2025-09-20 08:18:47] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -67.333441-0.002192j
[2025-09-20 08:19:20] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -67.348273+0.000514j
[2025-09-20 08:19:52] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -67.349774+0.000935j
[2025-09-20 08:20:23] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -67.346134-0.001698j
[2025-09-20 08:20:55] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -67.356530+0.001213j
[2025-09-20 08:21:27] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -67.348701+0.001293j
[2025-09-20 08:21:59] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -67.368698+0.002569j
[2025-09-20 08:22:31] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -67.369064+0.001821j
[2025-09-20 08:23:03] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -67.338428+0.001526j
[2025-09-20 08:23:35] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -67.355928-0.001679j
[2025-09-20 08:24:07] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -67.340594+0.003400j
[2025-09-20 08:24:39] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -67.330966-0.001206j
[2025-09-20 08:25:11] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -67.350491-0.000113j
[2025-09-20 08:25:43] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -67.336893-0.000086j
[2025-09-20 08:26:15] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -67.370595-0.003735j
[2025-09-20 08:26:47] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -67.332915-0.000367j
[2025-09-20 08:27:19] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -67.340283+0.000150j
[2025-09-20 08:27:51] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -67.370693+0.003335j
[2025-09-20 08:28:23] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -67.357922+0.001095j
[2025-09-20 08:28:55] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -67.368156+0.004384j
[2025-09-20 08:29:27] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -67.351035+0.001427j
[2025-09-20 08:29:59] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -67.348880+0.000367j
[2025-09-20 08:30:31] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -67.353094+0.000542j
[2025-09-20 08:31:03] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -67.341361+0.000895j
[2025-09-20 08:31:35] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -67.353788-0.000126j
[2025-09-20 08:32:07] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -67.359709+0.000857j
[2025-09-20 08:32:39] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -67.350347+0.001939j
[2025-09-20 08:33:11] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -67.350367-0.000714j
[2025-09-20 08:33:43] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -67.346738+0.000793j
[2025-09-20 08:34:15] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -67.365419+0.001411j
[2025-09-20 08:34:47] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -67.341465-0.001552j
[2025-09-20 08:35:19] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -67.349685-0.002588j
[2025-09-20 08:35:51] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -67.346614-0.000225j
[2025-09-20 08:36:23] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -67.341298-0.002019j
[2025-09-20 08:36:55] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -67.353925-0.000284j
[2025-09-20 08:37:27] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -67.337248-0.001495j
[2025-09-20 08:37:59] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -67.366365-0.002680j
[2025-09-20 08:38:32] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -67.358685-0.000881j
[2025-09-20 08:39:04] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -67.352237+0.001706j
[2025-09-20 08:39:36] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -67.349106-0.000083j
[2025-09-20 08:40:08] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -67.355285-0.002399j
[2025-09-20 08:40:40] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -67.350124+0.003870j
[2025-09-20 08:41:12] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -67.343882+0.005414j
[2025-09-20 08:41:44] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -67.352583+0.001665j
[2025-09-20 08:42:15] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -67.349894+0.003243j
[2025-09-20 08:42:47] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -67.344359-0.000797j
[2025-09-20 08:43:19] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -67.358187-0.000946j
[2025-09-20 08:43:50] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -67.353802-0.001214j
[2025-09-20 08:44:22] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -67.340816+0.001065j
[2025-09-20 08:44:54] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -67.348430-0.000127j
[2025-09-20 08:45:26] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -67.349725-0.001760j
[2025-09-20 08:45:58] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -67.342946-0.000177j
[2025-09-20 08:46:30] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -67.345944+0.001782j
[2025-09-20 08:47:02] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -67.350442+0.001613j
[2025-09-20 08:47:34] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -67.347017+0.001516j
[2025-09-20 08:48:06] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -67.354011-0.001212j
[2025-09-20 08:48:38] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -67.345858-0.001251j
[2025-09-20 08:49:10] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -67.356710-0.002358j
[2025-09-20 08:49:42] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -67.341239+0.008066j
[2025-09-20 08:50:13] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -67.350020+0.001619j
[2025-09-20 08:50:45] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -67.355651+0.002179j
[2025-09-20 08:51:17] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -67.351221+0.000989j
[2025-09-20 08:51:49] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -67.338829-0.001137j
[2025-09-20 08:52:21] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -67.351727+0.000288j
[2025-09-20 08:52:53] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -67.346026-0.000072j
[2025-09-20 08:52:53] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-20 08:52:53] ✅ Training completed | Restarts: 2
[2025-09-20 08:52:53] ============================================================
[2025-09-20 08:52:53] Training completed | Runtime: 33696.5s
[2025-09-20 08:53:05] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-20 08:53:05] ============================================================
