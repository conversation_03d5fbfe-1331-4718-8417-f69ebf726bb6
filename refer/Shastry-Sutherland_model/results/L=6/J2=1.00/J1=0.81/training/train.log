[2025-09-19 04:44:56] ✓ 从checkpoint恢复: results/L=6/J2=1.00/J1=0.80/training/checkpoints/final_GCNN.pkl
[2025-09-19 04:44:56]   - 迭代次数: final
[2025-09-19 04:44:56]   - 能量: -64.533015-0.001325j ± 0.007957
[2025-09-19 04:44:56]   - 时间戳: 2025-09-18T18:03:43.683875+08:00
[2025-09-19 04:45:22] ✓ 变分状态参数已从checkpoint恢复
[2025-09-19 04:45:22] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-19 04:45:22] ==================================================
[2025-09-19 04:45:22] GCNN for Shastry-Sutherland Model
[2025-09-19 04:45:23] ==================================================
[2025-09-19 04:45:23] System parameters:
[2025-09-19 04:45:23]   - System size: L=6, N=144
[2025-09-19 04:45:23]   - System parameters: J1=0.81, J2=1.0, Q=0.0
[2025-09-19 04:45:23] --------------------------------------------------
[2025-09-19 04:45:23] Model parameters:
[2025-09-19 04:45:23]   - Number of layers = 4
[2025-09-19 04:45:23]   - Number of features = 4
[2025-09-19 04:45:23]   - Total parameters = 28252
[2025-09-19 04:45:23] --------------------------------------------------
[2025-09-19 04:45:23] Training parameters:
[2025-09-19 04:45:23]   - Learning rate: 0.015
[2025-09-19 04:45:23]   - Total iterations: 1050
[2025-09-19 04:45:23]   - Annealing cycles: 3
[2025-09-19 04:45:23]   - Initial period: 150
[2025-09-19 04:45:23]   - Period multiplier: 2.0
[2025-09-19 04:45:23]   - Temperature range: 0.0-1.0
[2025-09-19 04:45:23]   - Samples: 4096
[2025-09-19 04:45:23]   - Discarded samples: 0
[2025-09-19 04:45:23]   - Chunk size: 2048
[2025-09-19 04:45:23]   - Diagonal shift: 0.2
[2025-09-19 04:45:23]   - Gradient clipping: 1.0
[2025-09-19 04:45:23]   - Checkpoint enabled: interval=105
[2025-09-19 04:45:23]   - Checkpoint directory: results/L=6/J2=1.00/J1=0.81/training/checkpoints
[2025-09-19 04:45:23] --------------------------------------------------
[2025-09-19 04:45:23] Device status:
[2025-09-19 04:45:23]   - Devices model: NVIDIA H200 NVL
[2025-09-19 04:45:23]   - Number of devices: 1
[2025-09-19 04:45:23]   - Sharding: True
[2025-09-19 04:45:23] ============================================================
[2025-09-19 04:46:57] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: 50.544680-0.066593j
[2025-09-19 04:48:02] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: 49.447620-0.072408j
[2025-09-19 04:48:34] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: 47.806217+0.081588j
[2025-09-19 04:49:06] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: 45.780191+0.053604j
[2025-09-19 04:49:38] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: 43.414399-0.105313j
[2025-09-19 04:50:11] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: 40.036092+0.078540j
[2025-09-19 04:50:43] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: 35.181301+0.006669j
[2025-09-19 04:51:15] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: 26.809698-0.155632j
[2025-09-19 04:51:47] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: 13.015213-0.071026j
[2025-09-19 04:52:19] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -4.546192-0.151615j
[2025-09-19 04:52:52] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -23.862039+0.036780j
[2025-09-19 04:53:24] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -42.839120-0.063006j
[2025-09-19 04:53:56] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -55.126249-0.101139j
[2025-09-19 04:54:28] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -61.778245-0.051212j
[2025-09-19 04:55:00] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -64.463292-0.008970j
[2025-09-19 04:55:33] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -65.234616-0.013490j
[2025-09-19 04:56:05] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -65.400907-0.004865j
[2025-09-19 04:56:37] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -65.421448-0.014481j
[2025-09-19 04:57:09] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -65.409099+0.000468j
[2025-09-19 04:57:41] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -65.407447-0.004241j
[2025-09-19 04:58:13] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -65.441725+0.000736j
[2025-09-19 04:58:46] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -65.420902-0.005380j
[2025-09-19 04:59:18] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -65.402368-0.005031j
[2025-09-19 04:59:50] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -65.436585-0.007268j
[2025-09-19 05:00:22] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -65.429084-0.007011j
[2025-09-19 05:00:54] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -65.422292-0.004135j
[2025-09-19 05:01:26] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -65.423643-0.000168j
[2025-09-19 05:01:59] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -65.455412+0.004499j
[2025-09-19 05:02:31] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -65.440126+0.001978j
[2025-09-19 05:03:03] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -65.464071+0.001200j
[2025-09-19 05:03:35] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -65.444236-0.003487j
[2025-09-19 05:04:08] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -65.444227+0.003863j
[2025-09-19 05:04:40] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -65.470111+0.005810j
[2025-09-19 05:05:12] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -65.449114-0.002961j
[2025-09-19 05:05:44] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -65.466925-0.009231j
[2025-09-19 05:06:16] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -65.472411+0.000485j
[2025-09-19 05:06:49] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -65.471786-0.001229j
[2025-09-19 05:07:21] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -65.478697-0.001731j
[2025-09-19 05:07:53] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -65.474711-0.004061j
[2025-09-19 05:08:25] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -65.467602+0.001579j
[2025-09-19 05:08:58] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -65.461623-0.003927j
[2025-09-19 05:09:30] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -65.451553-0.000323j
[2025-09-19 05:10:02] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -65.432065+0.003945j
[2025-09-19 05:10:34] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -65.470011+0.001068j
[2025-09-19 05:11:06] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -65.459846+0.001531j
[2025-09-19 05:11:38] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -65.473619-0.000779j
[2025-09-19 05:12:11] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -65.450003+0.000678j
[2025-09-19 05:12:43] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -65.451734-0.000112j
[2025-09-19 05:13:15] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -65.446721-0.000283j
[2025-09-19 05:13:47] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -65.445131+0.000621j
[2025-09-19 05:14:19] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -65.472259+0.004442j
[2025-09-19 05:14:52] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -65.459511+0.000749j
[2025-09-19 05:15:24] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -65.454673-0.000757j
[2025-09-19 05:15:56] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -65.450342+0.002713j
[2025-09-19 05:16:28] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -65.451544+0.001129j
[2025-09-19 05:17:00] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -65.458756-0.001424j
[2025-09-19 05:17:33] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -65.457428-0.000651j
[2025-09-19 05:18:05] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -65.469846-0.002379j
[2025-09-19 05:18:37] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -65.475656+0.001674j
[2025-09-19 05:19:09] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -65.484133+0.002262j
[2025-09-19 05:19:41] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -65.461646+0.003177j
[2025-09-19 05:20:14] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -65.478586+0.002200j
[2025-09-19 05:20:46] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -65.458708-0.001481j
[2025-09-19 05:21:18] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -65.469723+0.000105j
[2025-09-19 05:21:50] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -65.435711+0.007462j
[2025-09-19 05:22:22] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -65.452456-0.001927j
[2025-09-19 05:22:54] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -65.463487-0.001080j
[2025-09-19 05:23:27] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -65.464010-0.000355j
[2025-09-19 05:23:59] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -65.488358+0.004883j
[2025-09-19 05:24:31] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -65.461114+0.006771j
[2025-09-19 05:25:03] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -65.464012-0.001366j
[2025-09-19 05:25:35] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -65.473546+0.001175j
[2025-09-19 05:26:08] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -65.477656+0.002490j
[2025-09-19 05:26:40] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -65.481057+0.001806j
[2025-09-19 05:27:12] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -65.471240-0.003057j
[2025-09-19 05:27:44] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -65.470203+0.000828j
[2025-09-19 05:28:16] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -65.490340-0.002129j
[2025-09-19 05:28:49] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -65.461891-0.006418j
[2025-09-19 05:29:21] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -65.468844-0.000723j
[2025-09-19 05:29:53] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -65.463908-0.006968j
[2025-09-19 05:30:25] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -65.443327+0.002656j
[2025-09-19 05:30:58] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -65.457482-0.002581j
[2025-09-19 05:31:30] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -65.460100-0.003665j
[2025-09-19 05:32:02] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -65.465089+0.000109j
[2025-09-19 05:32:34] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -65.477293-0.002111j
[2025-09-19 05:33:06] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -65.467583-0.003471j
[2025-09-19 05:33:38] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -65.459236+0.003812j
[2025-09-19 05:34:11] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -65.477590+0.000697j
[2025-09-19 05:34:43] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -65.462761+0.001065j
[2025-09-19 05:35:15] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -65.476929+0.000073j
[2025-09-19 05:35:47] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -65.470998+0.000426j
[2025-09-19 05:36:19] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -65.492749-0.004374j
[2025-09-19 05:36:51] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -65.475229+0.000026j
[2025-09-19 05:37:24] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -65.477278+0.005671j
[2025-09-19 05:37:56] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -65.479277-0.000853j
[2025-09-19 05:38:28] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -65.474105+0.001206j
[2025-09-19 05:39:00] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -65.462845-0.000388j
[2025-09-19 05:39:32] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -65.469458-0.001452j
[2025-09-19 05:40:05] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -65.454983-0.001847j
[2025-09-19 05:40:37] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -65.468175-0.001463j
[2025-09-19 05:41:09] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -65.464825-0.001594j
[2025-09-19 05:41:41] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -65.483160+0.001168j
[2025-09-19 05:42:13] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -65.460912-0.000575j
[2025-09-19 05:42:45] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -65.464251-0.000486j
[2025-09-19 05:43:18] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -65.466989+0.000816j
[2025-09-19 05:43:18] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-19 05:43:50] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -65.481577+0.001402j
[2025-09-19 05:44:22] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -65.465911-0.000843j
[2025-09-19 05:44:54] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -65.452604-0.002644j
[2025-09-19 05:45:26] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -65.456062-0.001887j
[2025-09-19 05:45:59] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -65.465823+0.002268j
[2025-09-19 05:46:31] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -65.468432-0.002378j
[2025-09-19 05:47:03] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -65.465778+0.000149j
[2025-09-19 05:47:35] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -65.455755-0.002018j
[2025-09-19 05:48:07] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -65.465348+0.001084j
[2025-09-19 05:48:40] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -65.449114-0.000002j
[2025-09-19 05:49:12] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -65.480252+0.003324j
[2025-09-19 05:49:44] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -65.449525+0.002392j
[2025-09-19 05:50:16] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -65.472166-0.001042j
[2025-09-19 05:50:49] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -65.481021-0.003962j
[2025-09-19 05:51:21] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -65.459238-0.000885j
[2025-09-19 05:51:53] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -65.462655-0.004187j
[2025-09-19 05:52:25] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -65.457383-0.000843j
[2025-09-19 05:52:57] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -65.476407+0.016296j
[2025-09-19 05:53:30] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -65.453611-0.006210j
[2025-09-19 05:54:02] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -65.473059-0.002396j
[2025-09-19 05:54:34] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -65.467372-0.005467j
[2025-09-19 05:55:06] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -65.461928+0.002821j
[2025-09-19 05:55:38] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -65.466853-0.002647j
[2025-09-19 05:56:10] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -65.448353+0.001072j
[2025-09-19 05:56:43] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -65.475746-0.005556j
[2025-09-19 05:57:15] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -65.468431-0.000939j
[2025-09-19 05:57:47] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -65.469518-0.002713j
[2025-09-19 05:58:19] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -65.470838+0.002640j
[2025-09-19 05:58:51] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -65.471893+0.001619j
[2025-09-19 05:59:24] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -65.466066-0.002855j
[2025-09-19 05:59:56] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -65.458306-0.003191j
[2025-09-19 06:00:28] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -65.482883-0.001158j
[2025-09-19 06:01:00] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -65.468707+0.001830j
[2025-09-19 06:01:32] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -65.465831-0.001070j
[2025-09-19 06:02:05] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -65.454376-0.001578j
[2025-09-19 06:02:37] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -65.458379-0.002196j
[2025-09-19 06:03:09] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -65.483471+0.000873j
[2025-09-19 06:03:41] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -65.464203+0.019652j
[2025-09-19 06:04:13] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -65.465490+0.000264j
[2025-09-19 06:04:46] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -65.468131+0.003281j
[2025-09-19 06:05:18] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -65.469282+0.000497j
[2025-09-19 06:05:50] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -65.445485-0.002486j
[2025-09-19 06:06:22] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -65.487649-0.003080j
[2025-09-19 06:06:54] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -65.455669+0.001864j
[2025-09-19 06:07:27] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -65.470335-0.002983j
[2025-09-19 06:07:27] RESTART #1 | Period: 300
[2025-09-19 06:07:59] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -65.464056+0.003543j
[2025-09-19 06:08:31] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -65.474566+0.004127j
[2025-09-19 06:09:03] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -65.469205-0.003522j
[2025-09-19 06:09:35] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -65.486213+0.003206j
[2025-09-19 06:10:08] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -65.471017+0.003863j
[2025-09-19 06:10:40] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -65.478627+0.000348j
[2025-09-19 06:11:12] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -65.481792+0.000934j
[2025-09-19 06:11:44] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -65.455858-0.000142j
[2025-09-19 06:12:16] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -65.478039-0.001246j
[2025-09-19 06:12:49] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -65.469142+0.001008j
[2025-09-19 06:13:21] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -65.482070+0.002059j
[2025-09-19 06:13:53] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -65.466045+0.002113j
[2025-09-19 06:14:25] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -65.475023-0.003031j
[2025-09-19 06:14:57] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -65.477479-0.002124j
[2025-09-19 06:15:29] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -65.475411+0.003708j
[2025-09-19 06:16:02] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -65.453182+0.002750j
[2025-09-19 06:16:34] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -65.469113-0.002209j
[2025-09-19 06:17:06] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -65.464227+0.000195j
[2025-09-19 06:17:38] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -65.458132-0.003663j
[2025-09-19 06:18:10] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -65.483978+0.002541j
[2025-09-19 06:18:43] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -65.461834+0.001640j
[2025-09-19 06:19:15] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -65.461477-0.000482j
[2025-09-19 06:19:47] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -65.460984+0.000204j
[2025-09-19 06:20:19] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -65.465624-0.000635j
[2025-09-19 06:20:51] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -65.460210+0.001270j
[2025-09-19 06:21:24] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -65.489357+0.001176j
[2025-09-19 06:21:56] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -65.477300+0.002106j
[2025-09-19 06:22:28] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -65.449727-0.002624j
[2025-09-19 06:23:00] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -65.499484+0.002399j
[2025-09-19 06:23:33] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -65.481534+0.002630j
[2025-09-19 06:24:05] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -65.474675-0.000021j
[2025-09-19 06:24:37] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -65.468061+0.000818j
[2025-09-19 06:25:09] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -65.481327-0.004363j
[2025-09-19 06:25:42] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -65.480233-0.004058j
[2025-09-19 06:26:14] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -65.468873+0.000149j
[2025-09-19 06:26:46] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -65.465051+0.003894j
[2025-09-19 06:27:18] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -65.482750-0.000001j
[2025-09-19 06:27:50] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -65.483796-0.001703j
[2025-09-19 06:28:22] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -65.483394-0.002333j
[2025-09-19 06:28:55] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -65.468207-0.000710j
[2025-09-19 06:29:27] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -65.462662+0.000649j
[2025-09-19 06:29:59] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -65.489615-0.000083j
[2025-09-19 06:30:31] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -65.473077-0.002201j
[2025-09-19 06:31:03] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -65.477390-0.000623j
[2025-09-19 06:31:35] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -65.475148-0.000836j
[2025-09-19 06:32:07] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -65.466193-0.001697j
[2025-09-19 06:32:40] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -65.461721+0.001601j
[2025-09-19 06:33:12] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -65.471134+0.001517j
[2025-09-19 06:33:44] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -65.465960+0.002179j
[2025-09-19 06:34:16] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -65.486725-0.000415j
[2025-09-19 06:34:49] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -65.469372-0.000143j
[2025-09-19 06:35:21] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -65.486433+0.004468j
[2025-09-19 06:35:53] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -65.470175-0.001612j
[2025-09-19 06:36:25] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -65.490197+0.001444j
[2025-09-19 06:36:57] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -65.471188-0.000870j
[2025-09-19 06:37:30] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -65.465278-0.001963j
[2025-09-19 06:38:02] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -65.467931+0.002611j
[2025-09-19 06:38:34] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -65.469106-0.003124j
[2025-09-19 06:39:06] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -65.482085-0.000710j
[2025-09-19 06:39:38] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -65.475338-0.003486j
[2025-09-19 06:39:38] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-19 06:40:10] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -65.462947-0.000590j
[2025-09-19 06:40:43] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -65.463527-0.010158j
[2025-09-19 06:41:15] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -65.476522-0.004878j
[2025-09-19 06:41:47] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -65.460540+0.003154j
[2025-09-19 06:42:19] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -65.468228+0.000319j
[2025-09-19 06:42:51] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -65.489628-0.001997j
[2025-09-19 06:43:24] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -65.460647-0.001519j
[2025-09-19 06:43:56] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -65.479729+0.000691j
[2025-09-19 06:44:28] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -65.456228-0.000612j
[2025-09-19 06:45:00] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -65.468719+0.002666j
[2025-09-19 06:45:32] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -65.489418+0.003911j
[2025-09-19 06:46:04] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -65.465571+0.001610j
[2025-09-19 06:46:37] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -65.470558+0.000832j
[2025-09-19 06:47:09] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -65.465455-0.003726j
[2025-09-19 06:47:41] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -65.482769-0.005829j
[2025-09-19 06:48:13] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -65.464321-0.002518j
[2025-09-19 06:48:46] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -65.451209-0.001327j
[2025-09-19 06:49:18] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -65.456476+0.002304j
[2025-09-19 06:49:50] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -65.479300-0.004239j
[2025-09-19 06:50:22] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -65.482860+0.002259j
[2025-09-19 06:50:54] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -65.462790-0.004444j
[2025-09-19 06:51:27] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -65.458209+0.000831j
[2025-09-19 06:51:59] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -65.473429-0.001774j
[2025-09-19 06:52:31] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -65.469984+0.000467j
[2025-09-19 06:53:03] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -65.491779-0.005470j
[2025-09-19 06:53:35] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -65.458636+0.000530j
[2025-09-19 06:54:08] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -65.478397-0.000831j
[2025-09-19 06:54:40] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -65.486264-0.001609j
[2025-09-19 06:55:12] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -65.468780-0.000556j
[2025-09-19 06:55:44] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -65.468034+0.002662j
[2025-09-19 06:56:16] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -65.462296-0.000161j
[2025-09-19 06:56:48] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -65.453434+0.001538j
[2025-09-19 06:57:20] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -65.460468-0.001034j
[2025-09-19 06:57:53] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -65.476428+0.005635j
[2025-09-19 06:58:25] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -65.469727+0.004952j
[2025-09-19 06:58:57] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -65.474840-0.002084j
[2025-09-19 06:59:29] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -65.492283-0.000405j
[2025-09-19 07:00:02] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -65.484288-0.000200j
[2025-09-19 07:00:34] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -65.476811-0.003484j
[2025-09-19 07:01:06] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -65.476980-0.003233j
[2025-09-19 07:01:38] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -65.458394+0.001920j
[2025-09-19 07:02:10] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -65.470829-0.001651j
[2025-09-19 07:02:43] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -65.475675-0.002477j
[2025-09-19 07:03:15] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -65.468794+0.001073j
[2025-09-19 07:03:47] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -65.483226+0.000920j
[2025-09-19 07:04:19] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -65.477024+0.004209j
[2025-09-19 07:04:51] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -65.469968-0.002897j
[2025-09-19 07:05:23] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -65.485309-0.001334j
[2025-09-19 07:05:56] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -65.467839+0.008294j
[2025-09-19 07:06:28] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -65.466949-0.002903j
[2025-09-19 07:07:00] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -65.474437+0.001702j
[2025-09-19 07:07:32] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -65.478492-0.004395j
[2025-09-19 07:08:04] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -65.468628-0.000808j
[2025-09-19 07:08:36] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -65.457275-0.002734j
[2025-09-19 07:09:08] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -65.473238+0.001884j
[2025-09-19 07:09:41] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -65.459953+0.008476j
[2025-09-19 07:10:13] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -65.470396+0.001899j
[2025-09-19 07:10:45] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -65.469584-0.003417j
[2025-09-19 07:11:17] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -65.472837+0.001469j
[2025-09-19 07:11:49] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -65.475638-0.000544j
[2025-09-19 07:12:21] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -65.481748+0.001059j
[2025-09-19 07:12:54] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -65.470362+0.003388j
[2025-09-19 07:13:26] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -65.472952-0.001682j
[2025-09-19 07:13:58] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -65.468821+0.000932j
[2025-09-19 07:14:30] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -65.456896-0.000596j
[2025-09-19 07:15:03] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -65.465123+0.004032j
[2025-09-19 07:15:35] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -65.484481-0.004529j
[2025-09-19 07:16:07] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -65.466520+0.006029j
[2025-09-19 07:16:39] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -65.462058+0.002778j
[2025-09-19 07:17:12] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -65.478019+0.003779j
[2025-09-19 07:17:44] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -65.491172-0.001032j
[2025-09-19 07:18:16] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -65.498370-0.001597j
[2025-09-19 07:18:48] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -65.480943-0.000381j
[2025-09-19 07:19:20] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -65.477765-0.001489j
[2025-09-19 07:19:52] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -65.451134+0.000569j
[2025-09-19 07:20:25] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -65.484864-0.000514j
[2025-09-19 07:20:57] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -65.466327+0.004829j
[2025-09-19 07:21:29] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -65.474610-0.004779j
[2025-09-19 07:22:01] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -65.489331+0.001320j
[2025-09-19 07:22:33] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -65.463951-0.002500j
[2025-09-19 07:23:06] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -65.447741+0.001250j
[2025-09-19 07:23:38] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -65.465944+0.001745j
[2025-09-19 07:24:10] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -65.466937+0.000642j
[2025-09-19 07:24:42] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -65.473657-0.001093j
[2025-09-19 07:25:15] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -65.475343-0.003162j
[2025-09-19 07:25:47] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -65.473170+0.001187j
[2025-09-19 07:26:19] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -65.463216-0.000376j
[2025-09-19 07:26:51] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -65.479621+0.004955j
[2025-09-19 07:27:23] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -65.471354-0.000724j
[2025-09-19 07:27:56] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -65.480155+0.001466j
[2025-09-19 07:28:28] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -65.460085-0.000760j
[2025-09-19 07:29:00] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -65.462341+0.002160j
[2025-09-19 07:29:32] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -65.474699+0.000215j
[2025-09-19 07:30:05] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -65.485830-0.001372j
[2025-09-19 07:30:37] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -65.458319+0.002099j
[2025-09-19 07:31:09] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -65.464256-0.000117j
[2025-09-19 07:31:41] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -65.477516+0.000091j
[2025-09-19 07:32:13] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -65.468286+0.002932j
[2025-09-19 07:32:46] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -65.473948-0.002456j
[2025-09-19 07:33:18] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -65.465352+0.003023j
[2025-09-19 07:33:50] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -65.481026+0.003463j
[2025-09-19 07:34:22] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -65.482881+0.003598j
[2025-09-19 07:34:54] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -65.478687-0.000954j
[2025-09-19 07:35:26] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -65.469392-0.001067j
[2025-09-19 07:35:59] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -65.473597-0.002095j
[2025-09-19 07:35:59] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-19 07:36:31] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -65.459841-0.001033j
[2025-09-19 07:37:03] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -65.474191-0.003187j
[2025-09-19 07:37:35] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -65.470004+0.006551j
[2025-09-19 07:38:08] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -65.463244+0.002057j
[2025-09-19 07:38:40] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -65.469727+0.008596j
[2025-09-19 07:39:12] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -65.492685-0.005194j
[2025-09-19 07:39:44] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -65.476619+0.006565j
[2025-09-19 07:40:16] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -65.473115+0.000882j
[2025-09-19 07:40:49] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -65.464676+0.000192j
[2025-09-19 07:41:21] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -65.479239-0.002487j
[2025-09-19 07:41:53] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -65.457210+0.004416j
[2025-09-19 07:42:25] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -65.475136-0.001139j
[2025-09-19 07:42:57] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -65.475339+0.007859j
[2025-09-19 07:43:29] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -65.460082-0.003886j
[2025-09-19 07:44:01] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -65.472708+0.005038j
[2025-09-19 07:44:34] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -65.473070-0.002500j
[2025-09-19 07:45:06] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -65.473604-0.003974j
[2025-09-19 07:45:38] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -65.482735+0.001752j
[2025-09-19 07:46:10] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -65.469006+0.002130j
[2025-09-19 07:46:42] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -65.465686-0.000016j
[2025-09-19 07:47:14] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -65.472839-0.001420j
[2025-09-19 07:47:46] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -65.476311-0.006097j
[2025-09-19 07:48:18] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -65.475723-0.001477j
[2025-09-19 07:48:51] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -65.470444-0.000649j
[2025-09-19 07:49:23] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -65.471961+0.004361j
[2025-09-19 07:49:55] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -65.480166-0.003501j
[2025-09-19 07:50:27] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -65.479051-0.003534j
[2025-09-19 07:50:59] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -65.467680+0.000908j
[2025-09-19 07:51:31] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -65.484385-0.000900j
[2025-09-19 07:52:03] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -65.489506+0.003614j
[2025-09-19 07:52:35] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -65.488940-0.003472j
[2025-09-19 07:53:07] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -65.474038+0.004707j
[2025-09-19 07:53:40] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -65.464175+0.004452j
[2025-09-19 07:54:12] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -65.484832-0.000831j
[2025-09-19 07:54:44] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -65.463921+0.000094j
[2025-09-19 07:55:16] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -65.466461-0.003864j
[2025-09-19 07:55:48] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -65.456030+0.004318j
[2025-09-19 07:56:20] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -65.476505-0.005443j
[2025-09-19 07:56:52] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -65.463873+0.002120j
[2025-09-19 07:57:24] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -65.459249+0.001505j
[2025-09-19 07:57:57] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -65.479535+0.002292j
[2025-09-19 07:58:29] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -65.484414+0.000461j
[2025-09-19 07:59:01] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -65.475613-0.000403j
[2025-09-19 07:59:33] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -65.476362+0.001581j
[2025-09-19 08:00:05] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -65.464315-0.003911j
[2025-09-19 08:00:37] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -65.486012-0.001842j
[2025-09-19 08:01:10] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -65.466562-0.003070j
[2025-09-19 08:01:42] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -65.451664+0.000258j
[2025-09-19 08:02:14] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -65.466539-0.001578j
[2025-09-19 08:02:46] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -65.480172+0.000133j
[2025-09-19 08:03:18] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -65.478443-0.002496j
[2025-09-19 08:03:51] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -65.483699+0.000121j
[2025-09-19 08:04:23] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -65.493265+0.002474j
[2025-09-19 08:04:55] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -65.475636+0.012234j
[2025-09-19 08:05:27] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -65.465419+0.005366j
[2025-09-19 08:05:59] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -65.472494-0.004263j
[2025-09-19 08:06:32] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -65.478501+0.001386j
[2025-09-19 08:07:04] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -65.488221-0.006078j
[2025-09-19 08:07:36] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -65.474797-0.002396j
[2025-09-19 08:08:08] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -65.479786+0.000781j
[2025-09-19 08:08:41] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -65.474567+0.002544j
[2025-09-19 08:09:13] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -65.482039-0.002667j
[2025-09-19 08:09:45] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -65.483731+0.000745j
[2025-09-19 08:10:17] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -65.463081-0.002884j
[2025-09-19 08:10:49] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -65.477071-0.000766j
[2025-09-19 08:11:22] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -65.469111-0.002538j
[2025-09-19 08:11:54] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -65.463769-0.001832j
[2025-09-19 08:12:26] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -65.475444+0.000729j
[2025-09-19 08:12:58] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -65.473960+0.000582j
[2025-09-19 08:13:30] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -65.474628-0.000558j
[2025-09-19 08:14:02] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -65.472890+0.004186j
[2025-09-19 08:14:35] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -65.473594+0.001775j
[2025-09-19 08:15:07] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -65.455863-0.006075j
[2025-09-19 08:15:39] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -65.485536+0.002590j
[2025-09-19 08:16:11] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -65.464902-0.001410j
[2025-09-19 08:16:43] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -65.466834+0.001152j
[2025-09-19 08:17:16] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -65.480964-0.001916j
[2025-09-19 08:17:48] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -65.482750+0.004146j
[2025-09-19 08:18:20] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -65.471065-0.003694j
[2025-09-19 08:18:52] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -65.467848-0.000891j
[2025-09-19 08:19:24] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -65.491714+0.001064j
[2025-09-19 08:19:57] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -65.489138-0.004665j
[2025-09-19 08:20:29] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -65.465454+0.002331j
[2025-09-19 08:21:01] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -65.469160-0.002681j
[2025-09-19 08:21:33] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -65.463955+0.000634j
[2025-09-19 08:22:05] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -65.475043-0.002017j
[2025-09-19 08:22:38] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -65.508583-0.001411j
[2025-09-19 08:23:10] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -65.478673+0.004271j
[2025-09-19 08:23:42] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -65.463377-0.005849j
[2025-09-19 08:24:14] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -65.468047-0.000027j
[2025-09-19 08:24:47] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -65.473077-0.001696j
[2025-09-19 08:25:19] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -65.477370+0.005019j
[2025-09-19 08:25:51] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -65.467559-0.002560j
[2025-09-19 08:26:23] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -65.471228+0.005749j
[2025-09-19 08:26:55] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -65.486093+0.006535j
[2025-09-19 08:27:28] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -65.480312+0.000940j
[2025-09-19 08:28:00] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -65.472265+0.004337j
[2025-09-19 08:28:32] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -65.490210+0.001697j
[2025-09-19 08:29:04] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -65.454103+0.001788j
[2025-09-19 08:29:36] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -65.477683+0.002197j
[2025-09-19 08:30:09] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -65.483508-0.004164j
[2025-09-19 08:30:41] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -65.471196+0.002271j
[2025-09-19 08:31:13] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -65.491886-0.004160j
[2025-09-19 08:31:45] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -65.478780-0.002613j
[2025-09-19 08:32:17] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -65.494676+0.001081j
[2025-09-19 08:32:18] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-19 08:32:50] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -65.465650-0.001235j
[2025-09-19 08:33:22] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -65.480299-0.000376j
[2025-09-19 08:33:54] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -65.454622-0.000748j
[2025-09-19 08:34:26] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -65.490903+0.006811j
[2025-09-19 08:34:59] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -65.466786-0.001069j
[2025-09-19 08:35:31] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -65.462727+0.002275j
[2025-09-19 08:36:03] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -65.475669-0.004035j
[2025-09-19 08:36:35] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -65.463621+0.000341j
[2025-09-19 08:37:07] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -65.482232-0.003395j
[2025-09-19 08:37:39] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -65.486469+0.004563j
[2025-09-19 08:38:12] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -65.473002+0.000863j
[2025-09-19 08:38:44] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -65.472927-0.000516j
[2025-09-19 08:39:16] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -65.477383+0.000885j
[2025-09-19 08:39:48] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -65.465832+0.011863j
[2025-09-19 08:40:20] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -65.459911-0.000566j
[2025-09-19 08:40:52] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -65.479409+0.002489j
[2025-09-19 08:41:25] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -65.483002-0.004121j
[2025-09-19 08:41:57] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -65.480129-0.001154j
[2025-09-19 08:42:29] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -65.464936+0.003509j
[2025-09-19 08:43:01] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -65.475737+0.001808j
[2025-09-19 08:43:33] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -65.465531-0.012750j
[2025-09-19 08:44:05] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -65.477373+0.000677j
[2025-09-19 08:44:38] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -65.465767+0.001745j
[2025-09-19 08:45:10] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -65.496884+0.003231j
[2025-09-19 08:45:42] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -65.474197+0.001089j
[2025-09-19 08:46:14] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -65.462354-0.003304j
[2025-09-19 08:46:47] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -65.491252-0.000049j
[2025-09-19 08:47:19] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -65.467901-0.001565j
[2025-09-19 08:47:51] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -65.483581-0.000577j
[2025-09-19 08:48:23] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -65.475790-0.001467j
[2025-09-19 08:48:23] RESTART #2 | Period: 600
[2025-09-19 08:48:55] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -65.454963-0.000396j
[2025-09-19 08:49:28] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -65.471412-0.004080j
[2025-09-19 08:50:00] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -65.463383-0.000483j
[2025-09-19 08:50:32] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -65.487266-0.000734j
[2025-09-19 08:51:04] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -65.460060+0.001485j
[2025-09-19 08:51:36] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -65.473570+0.003271j
[2025-09-19 08:52:09] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -65.484403+0.000514j
[2025-09-19 08:52:41] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -65.479679-0.000687j
[2025-09-19 08:53:13] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -65.467010-0.005531j
[2025-09-19 08:53:45] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -65.475513+0.003351j
[2025-09-19 08:54:17] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -65.483274+0.002453j
[2025-09-19 08:54:50] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -65.486055-0.001161j
[2025-09-19 08:55:22] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -65.463156+0.002949j
[2025-09-19 08:55:54] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -65.462089-0.001973j
[2025-09-19 08:56:26] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -65.456246+0.001977j
[2025-09-19 08:56:58] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -65.498551+0.003525j
[2025-09-19 08:57:31] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -65.466472+0.002043j
[2025-09-19 08:58:03] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -65.456642+0.000820j
[2025-09-19 08:58:35] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -65.474419+0.001969j
[2025-09-19 08:59:07] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -65.489274+0.000102j
[2025-09-19 08:59:40] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -65.471185-0.000547j
[2025-09-19 09:00:12] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -65.483963+0.002987j
[2025-09-19 09:00:44] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -65.494872+0.003648j
[2025-09-19 09:01:16] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -65.477688-0.004476j
[2025-09-19 09:01:48] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -65.477734+0.003428j
[2025-09-19 09:02:21] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -65.459190-0.003683j
[2025-09-19 09:02:53] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -65.453953+0.004073j
[2025-09-19 09:03:25] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -65.493787-0.005692j
[2025-09-19 09:03:57] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -65.471491+0.004863j
[2025-09-19 09:04:29] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -65.486271-0.005935j
[2025-09-19 09:05:02] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -65.471949+0.000346j
[2025-09-19 09:05:34] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -65.479797-0.003175j
[2025-09-19 09:06:06] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -65.461135-0.002813j
[2025-09-19 09:06:38] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -65.480106+0.005909j
[2025-09-19 09:07:10] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -65.466561+0.001491j
[2025-09-19 09:07:42] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -65.479119+0.004601j
[2025-09-19 09:08:15] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -65.469098-0.000699j
[2025-09-19 09:08:47] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -65.480472+0.002143j
[2025-09-19 09:09:19] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -65.480251-0.002891j
[2025-09-19 09:09:51] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -65.479146-0.000552j
[2025-09-19 09:10:24] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -65.493234+0.000337j
[2025-09-19 09:10:56] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -65.476391-0.006834j
[2025-09-19 09:11:28] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -65.471816+0.000544j
[2025-09-19 09:12:00] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -65.489865-0.002648j
[2025-09-19 09:12:32] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -65.486935-0.009803j
[2025-09-19 09:13:05] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -65.454230+0.002608j
[2025-09-19 09:13:37] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -65.461462-0.004027j
[2025-09-19 09:14:09] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -65.474208-0.001992j
[2025-09-19 09:14:41] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -65.467886-0.003761j
[2025-09-19 09:15:14] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -65.481444-0.001239j
[2025-09-19 09:15:46] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -65.460857-0.002127j
[2025-09-19 09:16:18] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -65.475114-0.001491j
[2025-09-19 09:16:50] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -65.481922+0.002301j
[2025-09-19 09:17:22] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -65.485390+0.001635j
[2025-09-19 09:17:55] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -65.460741-0.003855j
[2025-09-19 09:18:27] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -65.493564+0.000210j
[2025-09-19 09:18:59] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -65.479649-0.001374j
[2025-09-19 09:19:31] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -65.452962+0.001556j
[2025-09-19 09:20:04] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -65.460525+0.001465j
[2025-09-19 09:20:36] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -65.477551-0.000859j
[2025-09-19 09:21:08] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -65.472510-0.000907j
[2025-09-19 09:21:40] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -65.494059-0.001353j
[2025-09-19 09:22:12] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -65.485772-0.002307j
[2025-09-19 09:22:45] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -65.474866+0.003838j
[2025-09-19 09:23:17] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -65.494063+0.003148j
[2025-09-19 09:23:49] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -65.490535-0.001548j
[2025-09-19 09:24:21] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -65.478110-0.004632j
[2025-09-19 09:24:53] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -65.446268+0.000044j
[2025-09-19 09:25:25] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -65.462489+0.002198j
[2025-09-19 09:25:57] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -65.500716-0.002280j
[2025-09-19 09:26:30] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -65.468921+0.000667j
[2025-09-19 09:27:02] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -65.477739-0.002319j
[2025-09-19 09:27:34] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -65.472931-0.002975j
[2025-09-19 09:28:06] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -65.475994+0.001074j
[2025-09-19 09:28:38] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -65.488569-0.000938j
[2025-09-19 09:28:38] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-19 09:29:11] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -65.483125+0.000945j
[2025-09-19 09:29:43] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -65.470685+0.000016j
[2025-09-19 09:30:15] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -65.487861+0.003637j
[2025-09-19 09:30:47] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -65.470786-0.003448j
[2025-09-19 09:31:19] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -65.469947+0.002543j
[2025-09-19 09:31:52] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -65.472239-0.003737j
[2025-09-19 09:32:24] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -65.483432-0.000147j
[2025-09-19 09:32:56] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -65.485939-0.001382j
[2025-09-19 09:33:28] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -65.488854+0.002628j
[2025-09-19 09:34:00] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -65.478028-0.003428j
[2025-09-19 09:34:33] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -65.501815+0.005408j
[2025-09-19 09:35:05] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -65.447941-0.000020j
[2025-09-19 09:35:37] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -65.467921+0.001156j
[2025-09-19 09:36:09] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -65.482835-0.001442j
[2025-09-19 09:36:41] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -65.475627+0.000722j
[2025-09-19 09:37:14] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -65.464236+0.001909j
[2025-09-19 09:37:46] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -65.470812+0.002092j
[2025-09-19 09:38:18] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -65.460640+0.001170j
[2025-09-19 09:38:50] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -65.474120+0.002463j
[2025-09-19 09:39:22] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -65.475343-0.001184j
[2025-09-19 09:39:55] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -65.464455+0.001254j
[2025-09-19 09:40:27] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -65.491448-0.000665j
[2025-09-19 09:40:59] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -65.475787-0.003222j
[2025-09-19 09:41:31] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -65.478410+0.007063j
[2025-09-19 09:42:03] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -65.475841+0.003057j
[2025-09-19 09:42:36] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -65.492275+0.000219j
[2025-09-19 09:43:08] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -65.466532+0.003135j
[2025-09-19 09:43:40] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -65.471198-0.003185j
[2025-09-19 09:44:12] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -65.481843+0.003677j
[2025-09-19 09:44:44] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -65.483028+0.003599j
[2025-09-19 09:45:17] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -65.476018-0.000105j
[2025-09-19 09:45:49] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -65.466672-0.001164j
[2025-09-19 09:46:21] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -65.472358-0.002197j
[2025-09-19 09:46:53] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -65.468279-0.003033j
[2025-09-19 09:47:25] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -65.478982-0.007208j
[2025-09-19 09:47:57] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -65.479031-0.003064j
[2025-09-19 09:48:30] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -65.484054-0.000898j
[2025-09-19 09:49:02] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -65.484041+0.004734j
[2025-09-19 09:49:34] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -65.479739+0.000042j
[2025-09-19 09:50:06] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -65.482594-0.001350j
[2025-09-19 09:50:38] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -65.490361-0.002607j
[2025-09-19 09:51:11] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -65.478793+0.000274j
[2025-09-19 09:51:43] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -65.469471-0.003528j
[2025-09-19 09:52:15] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -65.486637-0.001431j
[2025-09-19 09:52:47] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -65.492395+0.002829j
[2025-09-19 09:53:19] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -65.490023+0.004481j
[2025-09-19 09:53:52] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -65.473748+0.001745j
[2025-09-19 09:54:24] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -65.480193-0.001347j
[2025-09-19 09:54:56] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -65.481473+0.000642j
[2025-09-19 09:55:28] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -65.471516+0.003583j
[2025-09-19 09:56:00] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -65.478691+0.000759j
[2025-09-19 09:56:32] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -65.480504-0.003710j
[2025-09-19 09:57:05] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -65.476110-0.002459j
[2025-09-19 09:57:37] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -65.457318+0.000581j
[2025-09-19 09:58:09] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -65.472996+0.001350j
[2025-09-19 09:58:41] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -65.461742+0.003008j
[2025-09-19 09:59:13] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -65.484011+0.000072j
[2025-09-19 09:59:46] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -65.483440-0.004101j
[2025-09-19 10:00:18] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -65.486859-0.000490j
[2025-09-19 10:00:50] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -65.501905+0.002313j
[2025-09-19 10:01:22] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -65.476297+0.003291j
[2025-09-19 10:01:54] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -65.472856+0.004338j
[2025-09-19 10:02:27] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -65.478071+0.002309j
[2025-09-19 10:02:59] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -65.466009+0.000607j
[2025-09-19 10:03:31] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -65.448209+0.010900j
[2025-09-19 10:04:03] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -65.501080-0.000004j
[2025-09-19 10:04:35] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -65.486579+0.000990j
[2025-09-19 10:05:08] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -65.467318+0.003750j
[2025-09-19 10:05:40] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -65.488807-0.000955j
[2025-09-19 10:06:12] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -65.463410+0.000093j
[2025-09-19 10:06:45] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -65.489440+0.006163j
[2025-09-19 10:07:17] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -65.479811+0.000347j
[2025-09-19 10:07:49] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -65.479157+0.006199j
[2025-09-19 10:08:21] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -65.483941+0.000388j
[2025-09-19 10:08:53] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -65.484686+0.005863j
[2025-09-19 10:09:26] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -65.459215-0.003149j
[2025-09-19 10:09:58] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -65.496675+0.014314j
[2025-09-19 10:10:30] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -65.475884+0.000341j
[2025-09-19 10:11:02] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -65.474041-0.002666j
[2025-09-19 10:11:34] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -65.467810-0.003220j
[2025-09-19 10:12:07] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -65.477544+0.005186j
[2025-09-19 10:12:39] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -65.485980-0.000291j
[2025-09-19 10:13:11] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -65.480757-0.001173j
[2025-09-19 10:13:43] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -65.470631-0.001034j
[2025-09-19 10:14:15] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -65.481771-0.005001j
[2025-09-19 10:14:48] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -65.466772-0.000209j
[2025-09-19 10:15:20] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -65.483614+0.005705j
[2025-09-19 10:15:52] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -65.482853+0.000590j
[2025-09-19 10:16:24] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -65.479548-0.003698j
[2025-09-19 10:16:57] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -65.469729-0.002635j
[2025-09-19 10:17:29] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -65.487092-0.001273j
[2025-09-19 10:18:01] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -65.456673-0.000650j
[2025-09-19 10:18:33] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -65.492961-0.001203j
[2025-09-19 10:19:06] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -65.478096-0.003453j
[2025-09-19 10:19:38] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -65.467373+0.001248j
[2025-09-19 10:20:10] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -65.462037-0.001605j
[2025-09-19 10:20:42] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -65.465206-0.005931j
[2025-09-19 10:21:14] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -65.478398+0.002963j
[2025-09-19 10:21:46] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -65.480585+0.001463j
[2025-09-19 10:22:19] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -65.480800-0.000436j
[2025-09-19 10:22:51] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -65.462928-0.000601j
[2025-09-19 10:23:23] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -65.492023+0.004376j
[2025-09-19 10:23:55] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -65.482267-0.003087j
[2025-09-19 10:24:27] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -65.471943-0.001924j
[2025-09-19 10:24:59] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -65.471453-0.002752j
[2025-09-19 10:25:00] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-19 10:25:32] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -65.481067+0.002266j
[2025-09-19 10:26:04] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -65.489323+0.002971j
[2025-09-19 10:26:36] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -65.478442-0.003788j
[2025-09-19 10:27:08] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -65.488544-0.000595j
[2025-09-19 10:27:41] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -65.480503-0.005226j
[2025-09-19 10:28:13] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -65.460553+0.002876j
[2025-09-19 10:28:45] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -65.455998+0.000110j
[2025-09-19 10:29:17] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -65.472459+0.000182j
[2025-09-19 10:29:50] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -65.468646+0.013927j
[2025-09-19 10:30:22] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -65.485179-0.002185j
[2025-09-19 10:30:54] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -65.481974-0.001912j
[2025-09-19 10:31:26] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -65.478825+0.003937j
[2025-09-19 10:31:58] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -65.497124+0.001391j
[2025-09-19 10:32:31] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -65.483942+0.000646j
[2025-09-19 10:33:03] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -65.481163-0.001766j
[2025-09-19 10:33:35] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -65.490587+0.000574j
[2025-09-19 10:34:07] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -65.471332+0.001683j
[2025-09-19 10:34:39] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -65.454585+0.002264j
[2025-09-19 10:35:12] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -65.483124-0.000977j
[2025-09-19 10:35:44] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -65.476620-0.006729j
[2025-09-19 10:36:16] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -65.481919+0.000099j
[2025-09-19 10:36:48] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -65.481748-0.002209j
[2025-09-19 10:37:20] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -65.466714+0.000329j
[2025-09-19 10:37:53] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -65.465308-0.000745j
[2025-09-19 10:38:25] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -65.472184+0.000878j
[2025-09-19 10:38:57] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -65.474164+0.000625j
[2025-09-19 10:39:29] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -65.499746-0.003853j
[2025-09-19 10:40:01] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -65.488468+0.003784j
[2025-09-19 10:40:34] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -65.491077+0.000850j
[2025-09-19 10:41:06] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -65.473989+0.000178j
[2025-09-19 10:41:38] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -65.481809-0.004699j
[2025-09-19 10:42:10] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -65.473799-0.000962j
[2025-09-19 10:42:42] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -65.490039-0.002187j
[2025-09-19 10:43:14] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -65.485137+0.002556j
[2025-09-19 10:43:47] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -65.479335+0.000615j
[2025-09-19 10:44:19] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -65.479758-0.001772j
[2025-09-19 10:44:51] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -65.491387-0.000330j
[2025-09-19 10:45:23] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -65.453033-0.000392j
[2025-09-19 10:45:55] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -65.483450-0.006077j
[2025-09-19 10:46:27] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -65.475786+0.005195j
[2025-09-19 10:47:00] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -65.482596-0.003516j
[2025-09-19 10:47:32] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -65.469163-0.000745j
[2025-09-19 10:48:04] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -65.473057-0.000035j
[2025-09-19 10:48:36] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -65.481725+0.001114j
[2025-09-19 10:49:08] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -65.486647+0.000689j
[2025-09-19 10:49:40] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -65.488179+0.001799j
[2025-09-19 10:50:13] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -65.487795+0.007497j
[2025-09-19 10:50:45] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -65.490248-0.000846j
[2025-09-19 10:51:17] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -65.494048+0.002233j
[2025-09-19 10:51:49] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -65.491921-0.001742j
[2025-09-19 10:52:21] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -65.481044-0.006240j
[2025-09-19 10:52:54] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -65.497586-0.008546j
[2025-09-19 10:53:26] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -65.474830-0.004565j
[2025-09-19 10:53:58] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -65.483134+0.000560j
[2025-09-19 10:54:31] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -65.468309+0.001689j
[2025-09-19 10:55:03] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -65.491306-0.000938j
[2025-09-19 10:55:35] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -65.492026-0.002755j
[2025-09-19 10:56:07] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -65.478713+0.000266j
[2025-09-19 10:56:39] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -65.471791+0.002861j
[2025-09-19 10:57:11] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -65.469712-0.003789j
[2025-09-19 10:57:44] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -65.479544-0.000611j
[2025-09-19 10:58:16] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -65.489556-0.000490j
[2025-09-19 10:58:48] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -65.476140+0.000119j
[2025-09-19 10:59:20] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -65.486438+0.001461j
[2025-09-19 10:59:52] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -65.478381-0.000713j
[2025-09-19 11:00:25] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -65.493125-0.000955j
[2025-09-19 11:00:57] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -65.481553-0.000386j
[2025-09-19 11:01:29] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -65.475704-0.004560j
[2025-09-19 11:02:01] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -65.484874-0.003117j
[2025-09-19 11:02:34] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -65.504438-0.006303j
[2025-09-19 11:03:06] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -65.473310-0.002761j
[2025-09-19 11:03:38] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -65.490599-0.001995j
[2025-09-19 11:04:10] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -65.483528-0.003456j
[2025-09-19 11:04:42] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -65.471043-0.001905j
[2025-09-19 11:05:15] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -65.485484-0.001827j
[2025-09-19 11:05:47] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -65.460088+0.001056j
[2025-09-19 11:06:19] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -65.500485-0.004098j
[2025-09-19 11:06:51] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -65.477644-0.002719j
[2025-09-19 11:07:23] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -65.459361+0.006423j
[2025-09-19 11:07:55] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -65.482080-0.002265j
[2025-09-19 11:08:27] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -65.464285-0.000254j
[2025-09-19 11:09:00] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -65.475212-0.000989j
[2025-09-19 11:09:32] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -65.467364+0.002142j
[2025-09-19 11:10:04] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -65.474015-0.001061j
[2025-09-19 11:10:36] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -65.485839-0.001339j
[2025-09-19 11:11:08] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -65.487645+0.000576j
[2025-09-19 11:11:41] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -65.479208+0.001857j
[2025-09-19 11:12:13] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -65.488229-0.002264j
[2025-09-19 11:12:45] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -65.491127+0.001028j
[2025-09-19 11:13:17] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -65.476065-0.003377j
[2025-09-19 11:13:49] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -65.462034-0.003366j
[2025-09-19 11:14:21] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -65.482180-0.001778j
[2025-09-19 11:14:54] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -65.475620-0.002637j
[2025-09-19 11:15:26] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -65.496105-0.003433j
[2025-09-19 11:15:58] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -65.466459+0.002377j
[2025-09-19 11:16:30] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -65.486127-0.002285j
[2025-09-19 11:17:02] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -65.458253-0.002813j
[2025-09-19 11:17:35] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -65.489693+0.004678j
[2025-09-19 11:18:07] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -65.471313+0.002006j
[2025-09-19 11:18:39] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -65.500663-0.001932j
[2025-09-19 11:19:11] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -65.486608+0.002701j
[2025-09-19 11:19:43] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -65.489892+0.000974j
[2025-09-19 11:20:16] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -65.499766+0.003580j
[2025-09-19 11:20:48] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -65.483940-0.002316j
[2025-09-19 11:21:20] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -65.470453-0.002284j
[2025-09-19 11:21:20] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-19 11:21:52] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -65.483812+0.001688j
[2025-09-19 11:22:25] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -65.486606+0.002512j
[2025-09-19 11:22:57] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -65.507850+0.000809j
[2025-09-19 11:23:29] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -65.468854-0.000688j
[2025-09-19 11:24:01] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -65.482125-0.001027j
[2025-09-19 11:24:34] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -65.458397+0.008908j
[2025-09-19 11:25:06] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -65.476485-0.002369j
[2025-09-19 11:25:38] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -65.482368-0.005245j
[2025-09-19 11:26:10] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -65.483144+0.001987j
[2025-09-19 11:26:42] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -65.477020-0.004816j
[2025-09-19 11:27:14] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -65.481995+0.000890j
[2025-09-19 11:27:47] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -65.489978-0.000330j
[2025-09-19 11:28:19] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -65.480539+0.000666j
[2025-09-19 11:28:51] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -65.466736-0.002063j
[2025-09-19 11:29:23] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -65.484804-0.000234j
[2025-09-19 11:29:56] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -65.469656-0.002144j
[2025-09-19 11:30:28] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -65.493647-0.004765j
[2025-09-19 11:31:00] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -65.460351-0.000755j
[2025-09-19 11:31:32] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -65.470326+0.000502j
[2025-09-19 11:32:05] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -65.492426-0.000155j
[2025-09-19 11:32:37] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -65.478946+0.000785j
[2025-09-19 11:33:09] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -65.465079-0.000946j
[2025-09-19 11:33:41] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -65.475201-0.000431j
[2025-09-19 11:34:13] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -65.479155+0.001997j
[2025-09-19 11:34:45] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -65.470540-0.000575j
[2025-09-19 11:35:18] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -65.477646-0.002757j
[2025-09-19 11:35:50] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -65.465243-0.006182j
[2025-09-19 11:36:22] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -65.456223+0.004479j
[2025-09-19 11:36:54] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -65.483052+0.000397j
[2025-09-19 11:37:26] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -65.462659-0.000950j
[2025-09-19 11:37:59] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -65.473636-0.001298j
[2025-09-19 11:38:31] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -65.483110+0.004753j
[2025-09-19 11:39:03] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -65.481425-0.002622j
[2025-09-19 11:39:35] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -65.478885+0.003753j
[2025-09-19 11:40:07] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -65.475310+0.000842j
[2025-09-19 11:40:39] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -65.470835+0.001739j
[2025-09-19 11:41:12] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -65.476876+0.002126j
[2025-09-19 11:41:44] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -65.479927+0.002024j
[2025-09-19 11:42:16] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -65.494752-0.003940j
[2025-09-19 11:42:48] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -65.467348-0.001033j
[2025-09-19 11:43:20] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -65.489632+0.001843j
[2025-09-19 11:43:53] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -65.476810+0.004049j
[2025-09-19 11:44:25] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -65.475648-0.007269j
[2025-09-19 11:44:57] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -65.468769+0.003949j
[2025-09-19 11:45:29] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -65.475024+0.001004j
[2025-09-19 11:46:02] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -65.489061+0.003732j
[2025-09-19 11:46:34] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -65.470612-0.002651j
[2025-09-19 11:47:06] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -65.472565-0.000458j
[2025-09-19 11:47:38] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -65.475109+0.002800j
[2025-09-19 11:48:10] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -65.463146+0.004130j
[2025-09-19 11:48:43] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -65.463070+0.000632j
[2025-09-19 11:49:15] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -65.487977+0.000922j
[2025-09-19 11:49:47] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -65.482598+0.000123j
[2025-09-19 11:50:19] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -65.471079+0.002577j
[2025-09-19 11:50:51] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -65.463566-0.004627j
[2025-09-19 11:51:24] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -65.487544-0.001822j
[2025-09-19 11:51:56] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -65.497709+0.001408j
[2025-09-19 11:52:28] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -65.493092-0.002915j
[2025-09-19 11:53:00] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -65.472763-0.001727j
[2025-09-19 11:53:32] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -65.475563+0.002613j
[2025-09-19 11:54:05] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -65.462749-0.000895j
[2025-09-19 11:54:37] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -65.483568-0.000207j
[2025-09-19 11:55:09] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -65.466642-0.002864j
[2025-09-19 11:55:41] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -65.477920-0.001893j
[2025-09-19 11:56:13] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -65.498629+0.002332j
[2025-09-19 11:56:46] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -65.473809+0.001012j
[2025-09-19 11:57:18] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -65.487750+0.002996j
[2025-09-19 11:57:50] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -65.490747-0.000906j
[2025-09-19 11:58:22] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -65.491966+0.001702j
[2025-09-19 11:58:54] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -65.484278-0.003867j
[2025-09-19 11:59:27] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -65.478696-0.007698j
[2025-09-19 11:59:59] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -65.486226-0.005093j
[2025-09-19 12:00:31] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -65.478706+0.004219j
[2025-09-19 12:01:03] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -65.501170-0.003358j
[2025-09-19 12:01:35] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -65.486023+0.000497j
[2025-09-19 12:02:07] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -65.461560+0.001149j
[2025-09-19 12:02:40] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -65.502997+0.000444j
[2025-09-19 12:03:12] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -65.463642+0.000442j
[2025-09-19 12:03:44] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -65.487241+0.002304j
[2025-09-19 12:04:16] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -65.466300+0.000692j
[2025-09-19 12:04:48] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -65.497807-0.001722j
[2025-09-19 12:05:21] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -65.468657-0.005505j
[2025-09-19 12:05:53] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -65.496232+0.001597j
[2025-09-19 12:06:25] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -65.486300+0.003497j
[2025-09-19 12:06:57] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -65.491733+0.000947j
[2025-09-19 12:07:29] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -65.484446-0.005790j
[2025-09-19 12:08:02] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -65.475799+0.005341j
[2025-09-19 12:08:34] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -65.481634+0.000875j
[2025-09-19 12:09:06] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -65.483493-0.000792j
[2025-09-19 12:09:38] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -65.479203-0.000743j
[2025-09-19 12:10:10] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -65.467943+0.006131j
[2025-09-19 12:10:43] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -65.477932+0.004895j
[2025-09-19 12:11:15] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -65.470616+0.001082j
[2025-09-19 12:11:47] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -65.472357+0.000090j
[2025-09-19 12:12:19] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -65.488026-0.002861j
[2025-09-19 12:12:52] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -65.464796+0.001607j
[2025-09-19 12:13:24] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -65.485983+0.000763j
[2025-09-19 12:13:56] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -65.475268+0.001571j
[2025-09-19 12:14:28] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -65.496277-0.001397j
[2025-09-19 12:15:00] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -65.476130+0.001836j
[2025-09-19 12:15:33] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -65.479384+0.004808j
[2025-09-19 12:16:05] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -65.478215+0.001163j
[2025-09-19 12:16:37] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -65.477671+0.002715j
[2025-09-19 12:17:09] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -65.479527+0.000676j
[2025-09-19 12:17:41] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -65.492702-0.004617j
[2025-09-19 12:17:41] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-19 12:18:14] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -65.483276-0.005181j
[2025-09-19 12:18:46] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -65.472288+0.003249j
[2025-09-19 12:19:18] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -65.503477+0.002427j
[2025-09-19 12:19:50] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -65.475882+0.009422j
[2025-09-19 12:20:23] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -65.462784-0.003370j
[2025-09-19 12:20:55] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -65.477005+0.002210j
[2025-09-19 12:21:27] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -65.474244-0.002025j
[2025-09-19 12:21:59] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -65.479559-0.007100j
[2025-09-19 12:22:31] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -65.462693+0.001553j
[2025-09-19 12:23:03] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -65.480328-0.001821j
[2025-09-19 12:23:36] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -65.467952+0.002472j
[2025-09-19 12:24:08] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -65.480669-0.002695j
[2025-09-19 12:24:40] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -65.480951-0.003035j
[2025-09-19 12:25:12] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -65.475527+0.003447j
[2025-09-19 12:25:44] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -65.478262-0.003697j
[2025-09-19 12:26:16] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -65.468756-0.002692j
[2025-09-19 12:26:49] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -65.475414+0.003779j
[2025-09-19 12:27:21] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -65.482821-0.004901j
[2025-09-19 12:27:53] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -65.499107+0.001705j
[2025-09-19 12:28:25] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -65.478438-0.002829j
[2025-09-19 12:28:57] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -65.504900-0.001341j
[2025-09-19 12:29:29] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -65.469690-0.000877j
[2025-09-19 12:30:02] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -65.483136+0.003658j
[2025-09-19 12:30:34] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -65.477068-0.000642j
[2025-09-19 12:31:06] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -65.480334+0.001812j
[2025-09-19 12:31:38] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -65.466078+0.002681j
[2025-09-19 12:32:10] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -65.477896-0.003301j
[2025-09-19 12:32:42] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -65.491011+0.000234j
[2025-09-19 12:33:15] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -65.464597-0.002663j
[2025-09-19 12:33:47] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -65.478648-0.001583j
[2025-09-19 12:34:19] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -65.483470+0.000215j
[2025-09-19 12:34:51] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -65.479684+0.002220j
[2025-09-19 12:35:24] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -65.498847+0.001701j
[2025-09-19 12:35:56] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -65.477245-0.001587j
[2025-09-19 12:36:28] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -65.479000+0.004022j
[2025-09-19 12:37:00] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -65.467914+0.000627j
[2025-09-19 12:37:32] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -65.485420-0.002235j
[2025-09-19 12:38:05] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -65.469461+0.003733j
[2025-09-19 12:38:37] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -65.493361-0.000573j
[2025-09-19 12:39:09] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -65.471971+0.001010j
[2025-09-19 12:39:41] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -65.463786-0.003125j
[2025-09-19 12:40:13] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -65.480541+0.002071j
[2025-09-19 12:40:46] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -65.466432-0.002720j
[2025-09-19 12:41:18] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -65.484382-0.002760j
[2025-09-19 12:41:50] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -65.480348+0.003260j
[2025-09-19 12:42:22] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -65.483178-0.003705j
[2025-09-19 12:42:54] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -65.480899+0.004292j
[2025-09-19 12:43:27] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -65.488747+0.004876j
[2025-09-19 12:43:59] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -65.495591-0.007744j
[2025-09-19 12:44:31] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -65.482126-0.001752j
[2025-09-19 12:45:03] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -65.483944+0.004503j
[2025-09-19 12:45:36] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -65.487080+0.001309j
[2025-09-19 12:46:08] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -65.484974-0.001853j
[2025-09-19 12:46:40] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -65.475327-0.001487j
[2025-09-19 12:47:12] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -65.494165-0.001898j
[2025-09-19 12:47:44] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -65.493877-0.002390j
[2025-09-19 12:48:16] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -65.469867-0.003494j
[2025-09-19 12:48:49] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -65.488407-0.005967j
[2025-09-19 12:49:21] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -65.473952-0.004000j
[2025-09-19 12:49:53] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -65.480949-0.006389j
[2025-09-19 12:50:25] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -65.485443-0.001276j
[2025-09-19 12:50:57] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -65.465802+0.001225j
[2025-09-19 12:51:30] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -65.489387-0.002066j
[2025-09-19 12:52:02] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -65.469526+0.003359j
[2025-09-19 12:52:34] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -65.465173-0.003295j
[2025-09-19 12:53:06] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -65.486841-0.001379j
[2025-09-19 12:53:38] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -65.484173+0.001038j
[2025-09-19 12:54:10] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -65.488930-0.003703j
[2025-09-19 12:54:43] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -65.492335-0.001297j
[2025-09-19 12:55:15] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -65.463016+0.003504j
[2025-09-19 12:55:47] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -65.502003+0.002891j
[2025-09-19 12:56:19] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -65.488143+0.001266j
[2025-09-19 12:56:51] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -65.451312-0.001722j
[2025-09-19 12:57:24] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -65.493684+0.000774j
[2025-09-19 12:57:56] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -65.495095-0.001517j
[2025-09-19 12:58:28] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -65.495710-0.001258j
[2025-09-19 12:59:00] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -65.456954+0.001051j
[2025-09-19 12:59:32] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -65.484874+0.004200j
[2025-09-19 13:00:05] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -65.479238+0.003397j
[2025-09-19 13:00:37] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -65.499814+0.003861j
[2025-09-19 13:01:09] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -65.467548+0.000526j
[2025-09-19 13:01:41] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -65.465237+0.001911j
[2025-09-19 13:02:14] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -65.467929-0.001011j
[2025-09-19 13:02:46] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -65.478493-0.001447j
[2025-09-19 13:03:18] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -65.489503+0.001208j
[2025-09-19 13:03:50] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -65.496825+0.001650j
[2025-09-19 13:04:23] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -65.472940-0.000493j
[2025-09-19 13:04:55] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -65.505406-0.001785j
[2025-09-19 13:05:27] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -65.475107-0.000409j
[2025-09-19 13:05:59] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -65.464731-0.006487j
[2025-09-19 13:06:31] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -65.491620+0.002823j
[2025-09-19 13:07:04] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -65.491468-0.002596j
[2025-09-19 13:07:36] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -65.470367+0.004163j
[2025-09-19 13:08:08] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -65.480733-0.003246j
[2025-09-19 13:08:40] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -65.477615-0.001309j
[2025-09-19 13:09:12] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -65.486543-0.001192j
[2025-09-19 13:09:45] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -65.473214-0.002805j
[2025-09-19 13:10:17] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -65.480158-0.004382j
[2025-09-19 13:10:49] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -65.480674+0.001856j
[2025-09-19 13:11:21] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -65.487524+0.003236j
[2025-09-19 13:11:54] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -65.491081+0.000119j
[2025-09-19 13:12:26] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -65.491321+0.000289j
[2025-09-19 13:12:58] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -65.494740+0.001495j
[2025-09-19 13:13:30] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -65.486938-0.000533j
[2025-09-19 13:14:02] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -65.482271-0.000416j
[2025-09-19 13:14:03] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-19 13:14:35] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -65.476709-0.004117j
[2025-09-19 13:15:07] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -65.489218+0.000615j
[2025-09-19 13:15:39] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -65.476629+0.002052j
[2025-09-19 13:16:11] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -65.482363-0.004709j
[2025-09-19 13:16:43] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -65.492135-0.000480j
[2025-09-19 13:17:16] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -65.500466-0.000999j
[2025-09-19 13:17:48] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -65.473981+0.001526j
[2025-09-19 13:18:20] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -65.482961-0.001560j
[2025-09-19 13:18:52] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -65.468878+0.002581j
[2025-09-19 13:19:24] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -65.474447-0.001895j
[2025-09-19 13:19:57] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -65.481100-0.002788j
[2025-09-19 13:20:29] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -65.489900+0.005034j
[2025-09-19 13:21:01] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -65.472178-0.000091j
[2025-09-19 13:21:33] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -65.497953-0.000924j
[2025-09-19 13:22:05] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -65.487251-0.002071j
[2025-09-19 13:22:37] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -65.484992-0.005275j
[2025-09-19 13:23:09] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -65.473553-0.000253j
[2025-09-19 13:23:42] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -65.479002+0.001430j
[2025-09-19 13:24:14] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -65.459176+0.002944j
[2025-09-19 13:24:46] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -65.495665+0.000703j
[2025-09-19 13:25:18] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -65.479484+0.001713j
[2025-09-19 13:25:51] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -65.462391-0.000013j
[2025-09-19 13:26:23] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -65.473175-0.002567j
[2025-09-19 13:26:55] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -65.515461+0.008501j
[2025-09-19 13:27:27] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -65.494165-0.006778j
[2025-09-19 13:28:00] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -65.483513+0.001283j
[2025-09-19 13:28:32] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -65.488170+0.011082j
[2025-09-19 13:29:04] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -65.476215-0.002662j
[2025-09-19 13:29:36] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -65.487623-0.001369j
[2025-09-19 13:30:08] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -65.474163+0.001705j
[2025-09-19 13:30:41] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -65.496813-0.000840j
[2025-09-19 13:31:13] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -65.486242-0.002596j
[2025-09-19 13:31:45] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -65.480317-0.000252j
[2025-09-19 13:32:17] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -65.488495-0.006438j
[2025-09-19 13:32:49] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -65.466914+0.002720j
[2025-09-19 13:33:21] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -65.482929-0.002798j
[2025-09-19 13:33:54] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -65.497168+0.004481j
[2025-09-19 13:34:26] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -65.482238+0.003979j
[2025-09-19 13:34:58] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -65.472283+0.001139j
[2025-09-19 13:35:30] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -65.493343-0.006366j
[2025-09-19 13:36:03] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -65.479522-0.003653j
[2025-09-19 13:36:35] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -65.486686-0.002912j
[2025-09-19 13:37:07] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -65.467839-0.003011j
[2025-09-19 13:37:39] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -65.497351+0.000210j
[2025-09-19 13:38:12] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -65.493590+0.000087j
[2025-09-19 13:38:44] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -65.470733+0.003168j
[2025-09-19 13:39:16] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -65.475838+0.001063j
[2025-09-19 13:39:48] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -65.488882-0.002857j
[2025-09-19 13:40:20] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -65.480450+0.001106j
[2025-09-19 13:40:53] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -65.472361-0.005550j
[2025-09-19 13:41:25] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -65.490571-0.002045j
[2025-09-19 13:41:57] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -65.471827+0.001536j
[2025-09-19 13:42:29] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -65.495352+0.000609j
[2025-09-19 13:43:01] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -65.475505+0.002013j
[2025-09-19 13:43:34] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -65.484356-0.000634j
[2025-09-19 13:44:06] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -65.475267-0.002901j
[2025-09-19 13:44:38] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -65.509975+0.005625j
[2025-09-19 13:45:10] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -65.493658+0.003223j
[2025-09-19 13:45:42] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -65.475733+0.001687j
[2025-09-19 13:46:15] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -65.473454-0.004985j
[2025-09-19 13:46:47] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -65.484921+0.000642j
[2025-09-19 13:47:19] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -65.498251+0.001413j
[2025-09-19 13:47:51] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -65.484215-0.004029j
[2025-09-19 13:48:24] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -65.512692+0.003160j
[2025-09-19 13:48:56] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -65.475620-0.002872j
[2025-09-19 13:49:28] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -65.485328-0.000938j
[2025-09-19 13:50:00] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -65.497136+0.001496j
[2025-09-19 13:50:32] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -65.485688-0.000436j
[2025-09-19 13:51:05] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -65.478232+0.000587j
[2025-09-19 13:51:37] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -65.465746+0.002346j
[2025-09-19 13:52:09] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -65.484240+0.001079j
[2025-09-19 13:52:41] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -65.480525-0.004105j
[2025-09-19 13:53:13] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -65.488105-0.000536j
[2025-09-19 13:53:46] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -65.483222-0.000298j
[2025-09-19 13:54:18] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -65.467880-0.003558j
[2025-09-19 13:54:50] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -65.472735+0.001518j
[2025-09-19 13:55:22] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -65.481165-0.002057j
[2025-09-19 13:55:54] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -65.476909-0.004171j
[2025-09-19 13:56:26] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -65.472886-0.002371j
[2025-09-19 13:56:59] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -65.489412-0.000336j
[2025-09-19 13:57:31] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -65.487153+0.000471j
[2025-09-19 13:58:03] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -65.479969-0.000812j
[2025-09-19 13:58:35] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -65.481299-0.002856j
[2025-09-19 13:59:08] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -65.507019-0.001234j
[2025-09-19 13:59:40] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -65.461251-0.003852j
[2025-09-19 14:00:12] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -65.480321-0.002293j
[2025-09-19 14:00:44] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -65.475998+0.001049j
[2025-09-19 14:01:16] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -65.476346+0.002772j
[2025-09-19 14:01:49] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -65.489404-0.000732j
[2025-09-19 14:02:21] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -65.480931-0.007675j
[2025-09-19 14:02:53] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -65.486691-0.001365j
[2025-09-19 14:03:25] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -65.479841+0.004018j
[2025-09-19 14:03:57] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -65.482319-0.001085j
[2025-09-19 14:04:29] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -65.491599+0.002142j
[2025-09-19 14:04:45] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -65.482277-0.000261j
[2025-09-19 14:05:00] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -65.504068+0.000274j
[2025-09-19 14:05:14] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -65.491247+0.000308j
[2025-09-19 14:05:29] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -65.492894+0.001922j
[2025-09-19 14:05:43] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -65.475358+0.002082j
[2025-09-19 14:05:58] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -65.483395-0.002412j
[2025-09-19 14:06:12] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -65.493236-0.000780j
[2025-09-19 14:06:27] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -65.471159-0.002137j
[2025-09-19 14:06:41] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -65.473059-0.000582j
[2025-09-19 14:06:56] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -65.487105+0.000994j
[2025-09-19 14:07:10] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -65.499269-0.000231j
[2025-09-19 14:07:10] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-19 14:07:10] ✅ Training completed | Restarts: 2
[2025-09-19 14:07:10] ============================================================
[2025-09-19 14:07:10] Training completed | Runtime: 33707.3s
[2025-09-19 14:07:15] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-19 14:07:15] ============================================================
