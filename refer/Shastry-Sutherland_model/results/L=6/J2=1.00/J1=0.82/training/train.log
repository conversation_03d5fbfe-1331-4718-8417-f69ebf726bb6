[2025-09-19 14:07:49] ✓ 从checkpoint恢复: results/L=6/J2=1.00/J1=0.81/training/checkpoints/final_GCNN.pkl
[2025-09-19 14:07:49]   - 迭代次数: final
[2025-09-19 14:07:49]   - 能量: -65.499269-0.000231j ± 0.011047
[2025-09-19 14:07:49]   - 时间戳: 2025-09-19T14:07:15.889857+08:00
[2025-09-19 14:08:10] ✓ 变分状态参数已从checkpoint恢复
[2025-09-19 14:08:10] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-19 14:08:10] ==================================================
[2025-09-19 14:08:10] GCNN for Shastry-Sutherland Model
[2025-09-19 14:08:10] ==================================================
[2025-09-19 14:08:10] System parameters:
[2025-09-19 14:08:10]   - System size: L=6, N=144
[2025-09-19 14:08:10]   - System parameters: J1=0.82, J2=1.0, Q=0.0
[2025-09-19 14:08:10] --------------------------------------------------
[2025-09-19 14:08:10] Model parameters:
[2025-09-19 14:08:10]   - Number of layers = 4
[2025-09-19 14:08:10]   - Number of features = 4
[2025-09-19 14:08:10]   - Total parameters = 28252
[2025-09-19 14:08:10] --------------------------------------------------
[2025-09-19 14:08:10] Training parameters:
[2025-09-19 14:08:10]   - Learning rate: 0.015
[2025-09-19 14:08:10]   - Total iterations: 1050
[2025-09-19 14:08:10]   - Annealing cycles: 3
[2025-09-19 14:08:10]   - Initial period: 150
[2025-09-19 14:08:10]   - Period multiplier: 2.0
[2025-09-19 14:08:10]   - Temperature range: 0.0-1.0
[2025-09-19 14:08:10]   - Samples: 4096
[2025-09-19 14:08:10]   - Discarded samples: 0
[2025-09-19 14:08:10]   - Chunk size: 2048
[2025-09-19 14:08:10]   - Diagonal shift: 0.2
[2025-09-19 14:08:10]   - Gradient clipping: 1.0
[2025-09-19 14:08:10]   - Checkpoint enabled: interval=105
[2025-09-19 14:08:10]   - Checkpoint directory: results/L=6/J2=1.00/J1=0.82/training/checkpoints
[2025-09-19 14:08:10] --------------------------------------------------
[2025-09-19 14:08:10] Device status:
[2025-09-19 14:08:10]   - Devices model: NVIDIA H200 NVL
[2025-09-19 14:08:10]   - Number of devices: 1
[2025-09-19 14:08:10]   - Sharding: True
[2025-09-19 14:08:10] ============================================================
[2025-09-19 14:09:56] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -12.862014+0.014510j
[2025-09-19 14:11:08] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -14.409931-0.003185j
[2025-09-19 14:11:40] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -16.445437+0.075731j
[2025-09-19 14:12:12] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -19.591601-0.106968j
[2025-09-19 14:12:44] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -25.031219-0.016957j
[2025-09-19 14:13:16] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -33.132435+0.038189j
[2025-09-19 14:13:48] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -43.908402+0.006695j
[2025-09-19 14:14:20] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -54.679186+0.011351j
[2025-09-19 14:14:53] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -61.806944+0.016104j
[2025-09-19 14:15:25] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -65.263486-0.013122j
[2025-09-19 14:15:57] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -66.184263-0.003885j
[2025-09-19 14:16:29] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -66.327528+0.008001j
[2025-09-19 14:17:01] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -66.378497-0.000426j
[2025-09-19 14:17:33] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -66.414732+0.001268j
[2025-09-19 14:18:06] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -66.412660-0.004327j
[2025-09-19 14:18:38] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -66.410475-0.004098j
[2025-09-19 14:19:10] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -66.405398-0.002930j
[2025-09-19 14:19:42] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -66.390434+0.004751j
[2025-09-19 14:20:14] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -66.409761+0.000988j
[2025-09-19 14:20:46] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -66.400418+0.002337j
[2025-09-19 14:21:18] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -66.398587+0.004654j
[2025-09-19 14:21:49] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -66.412102+0.003149j
[2025-09-19 14:22:21] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -66.409272+0.000574j
[2025-09-19 14:22:53] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -66.403218-0.003134j
[2025-09-19 14:23:25] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -66.398852-0.000307j
[2025-09-19 14:23:58] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -66.408890-0.002115j
[2025-09-19 14:24:30] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -66.401204+0.001752j
[2025-09-19 14:25:02] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -66.410023+0.003309j
[2025-09-19 14:25:34] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -66.400827+0.000225j
[2025-09-19 14:26:06] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -66.412860+0.001787j
[2025-09-19 14:26:39] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -66.419399-0.002350j
[2025-09-19 14:27:11] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -66.391069-0.003825j
[2025-09-19 14:27:43] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -66.417705-0.000025j
[2025-09-19 14:28:15] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -66.403988+0.003581j
[2025-09-19 14:28:47] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -66.416625+0.000398j
[2025-09-19 14:29:20] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -66.403460-0.001530j
[2025-09-19 14:29:52] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -66.414219+0.002047j
[2025-09-19 14:30:24] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -66.397201+0.000931j
[2025-09-19 14:30:56] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -66.398055-0.001091j
[2025-09-19 14:31:28] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -66.405191+0.001326j
[2025-09-19 14:32:00] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -66.413982-0.002587j
[2025-09-19 14:32:33] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -66.410666-0.007807j
[2025-09-19 14:33:05] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -66.422536+0.000145j
[2025-09-19 14:33:37] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -66.407258-0.001568j
[2025-09-19 14:34:09] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -66.410779-0.001165j
[2025-09-19 14:34:41] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -66.404762+0.002859j
[2025-09-19 14:35:13] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -66.414041+0.000675j
[2025-09-19 14:35:46] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -66.388979+0.003182j
[2025-09-19 14:36:18] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -66.408048+0.000090j
[2025-09-19 14:36:50] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -66.422244+0.000618j
[2025-09-19 14:37:22] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -66.407079+0.000772j
[2025-09-19 14:37:54] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -66.402001+0.000356j
[2025-09-19 14:38:26] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -66.416959+0.000650j
[2025-09-19 14:38:59] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -66.387552-0.000481j
[2025-09-19 14:39:31] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -66.393883+0.000289j
[2025-09-19 14:40:03] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -66.416636-0.003643j
[2025-09-19 14:40:35] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -66.399133-0.002230j
[2025-09-19 14:41:07] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -66.393206+0.003261j
[2025-09-19 14:41:40] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -66.410384-0.000286j
[2025-09-19 14:42:12] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -66.413028-0.005061j
[2025-09-19 14:42:44] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -66.391900-0.002487j
[2025-09-19 14:43:16] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -66.384819+0.001904j
[2025-09-19 14:43:48] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -66.414768-0.005913j
[2025-09-19 14:44:21] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -66.389661+0.002857j
[2025-09-19 14:44:53] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -66.411913+0.000054j
[2025-09-19 14:45:25] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -66.396554-0.001372j
[2025-09-19 14:45:57] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -66.401442-0.001728j
[2025-09-19 14:46:29] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -66.390622+0.009944j
[2025-09-19 14:47:01] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -66.406850-0.000658j
[2025-09-19 14:47:34] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -66.406335+0.000212j
[2025-09-19 14:48:06] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -66.421118-0.003225j
[2025-09-19 14:48:38] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -66.423472-0.002367j
[2025-09-19 14:49:10] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -66.407079-0.000015j
[2025-09-19 14:49:42] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -66.402184+0.001186j
[2025-09-19 14:50:14] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -66.413915+0.001635j
[2025-09-19 14:50:47] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -66.396637+0.005657j
[2025-09-19 14:51:19] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -66.408612-0.001019j
[2025-09-19 14:51:51] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -66.411981+0.001460j
[2025-09-19 14:52:23] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -66.412309-0.000919j
[2025-09-19 14:52:55] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -66.407739-0.002404j
[2025-09-19 14:53:27] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -66.414030-0.000769j
[2025-09-19 14:53:59] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -66.423564-0.001324j
[2025-09-19 14:54:32] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -66.413588+0.001716j
[2025-09-19 14:55:04] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -66.429858-0.002643j
[2025-09-19 14:55:36] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -66.406788+0.001859j
[2025-09-19 14:56:08] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -66.409492-0.000791j
[2025-09-19 14:56:40] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -66.408012-0.000164j
[2025-09-19 14:57:13] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -66.424905-0.002903j
[2025-09-19 14:57:45] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -66.405545-0.001772j
[2025-09-19 14:58:17] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -66.410295-0.005745j
[2025-09-19 14:58:49] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -66.398806+0.000523j
[2025-09-19 14:59:21] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -66.397840+0.001971j
[2025-09-19 14:59:54] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -66.405147+0.002927j
[2025-09-19 15:00:26] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -66.396243-0.000161j
[2025-09-19 15:00:58] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -66.425422-0.002815j
[2025-09-19 15:01:30] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -66.421866+0.000505j
[2025-09-19 15:02:02] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -66.412091+0.003199j
[2025-09-19 15:02:34] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -66.406491+0.005268j
[2025-09-19 15:03:07] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -66.404291-0.003748j
[2025-09-19 15:03:39] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -66.392434-0.000450j
[2025-09-19 15:04:11] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -66.394327+0.001113j
[2025-09-19 15:04:43] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -66.412058+0.003005j
[2025-09-19 15:05:15] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -66.406444-0.000272j
[2025-09-19 15:05:47] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -66.409785+0.000764j
[2025-09-19 15:06:19] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -66.413709-0.000860j
[2025-09-19 15:06:19] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-19 15:06:52] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -66.409219+0.001293j
[2025-09-19 15:07:24] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -66.405732+0.002903j
[2025-09-19 15:07:56] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -66.405459-0.006938j
[2025-09-19 15:08:28] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -66.415765-0.003461j
[2025-09-19 15:09:00] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -66.402132-0.000510j
[2025-09-19 15:09:33] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -66.410415-0.000155j
[2025-09-19 15:10:05] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -66.396023+0.000255j
[2025-09-19 15:10:37] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -66.414302-0.003600j
[2025-09-19 15:11:09] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -66.402844-0.000490j
[2025-09-19 15:11:41] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -66.411175-0.001418j
[2025-09-19 15:12:14] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -66.389030+0.002543j
[2025-09-19 15:12:46] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -66.406167-0.007592j
[2025-09-19 15:13:18] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -66.404542+0.001656j
[2025-09-19 15:13:50] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -66.410902+0.000280j
[2025-09-19 15:14:22] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -66.408099-0.002071j
[2025-09-19 15:14:54] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -66.396288-0.003908j
[2025-09-19 15:15:26] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -66.404539+0.001090j
[2025-09-19 15:15:58] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -66.413076-0.000646j
[2025-09-19 15:16:31] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -66.408374-0.000861j
[2025-09-19 15:17:03] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -66.417047-0.001886j
[2025-09-19 15:17:35] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -66.405331-0.001677j
[2025-09-19 15:18:07] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -66.413965-0.005956j
[2025-09-19 15:18:39] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -66.417461+0.003897j
[2025-09-19 15:19:11] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -66.411541+0.001883j
[2025-09-19 15:19:43] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -66.402878-0.000067j
[2025-09-19 15:20:16] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -66.417473-0.000986j
[2025-09-19 15:20:48] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -66.412263-0.001354j
[2025-09-19 15:21:20] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -66.404177-0.001333j
[2025-09-19 15:21:52] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -66.421116-0.002055j
[2025-09-19 15:22:24] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -66.414328-0.002362j
[2025-09-19 15:22:57] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -66.408333-0.002284j
[2025-09-19 15:23:29] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -66.414176+0.001383j
[2025-09-19 15:24:01] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -66.425835+0.001422j
[2025-09-19 15:24:33] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -66.414773+0.004776j
[2025-09-19 15:25:05] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -66.409960+0.000737j
[2025-09-19 15:25:37] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -66.413466-0.000484j
[2025-09-19 15:26:10] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -66.419726-0.002503j
[2025-09-19 15:26:42] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -66.423323+0.000992j
[2025-09-19 15:27:14] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -66.400830+0.001206j
[2025-09-19 15:27:46] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -66.411140-0.000416j
[2025-09-19 15:28:18] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -66.393780-0.005567j
[2025-09-19 15:28:50] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -66.406873-0.001007j
[2025-09-19 15:29:23] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -66.433438+0.003914j
[2025-09-19 15:29:55] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -66.410062+0.002600j
[2025-09-19 15:30:27] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -66.406235-0.002087j
[2025-09-19 15:30:27] RESTART #1 | Period: 300
[2025-09-19 15:30:59] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -66.408195+0.000298j
[2025-09-19 15:31:31] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -66.406706-0.000898j
[2025-09-19 15:32:03] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -66.393093-0.000934j
[2025-09-19 15:32:36] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -66.396776-0.001176j
[2025-09-19 15:33:08] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -66.414426-0.003806j
[2025-09-19 15:33:40] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -66.413644-0.000570j
[2025-09-19 15:34:13] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -66.416187-0.000938j
[2025-09-19 15:34:45] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -66.411582-0.001325j
[2025-09-19 15:35:17] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -66.394843+0.000510j
[2025-09-19 15:35:49] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -66.401436+0.002321j
[2025-09-19 15:36:21] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -66.411622-0.000611j
[2025-09-19 15:36:53] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -66.405063+0.004446j
[2025-09-19 15:37:26] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -66.422315-0.002644j
[2025-09-19 15:37:58] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -66.413391-0.002196j
[2025-09-19 15:38:30] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -66.394512-0.000206j
[2025-09-19 15:39:02] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -66.399458+0.000194j
[2025-09-19 15:39:34] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -66.427790-0.004151j
[2025-09-19 15:40:06] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -66.414671+0.000387j
[2025-09-19 15:40:39] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -66.414025-0.003985j
[2025-09-19 15:41:11] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -66.411530+0.002070j
[2025-09-19 15:41:43] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -66.416479-0.001774j
[2025-09-19 15:42:15] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -66.409238+0.000545j
[2025-09-19 15:42:47] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -66.407367-0.001015j
[2025-09-19 15:43:20] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -66.393034+0.004238j
[2025-09-19 15:43:52] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -66.393465-0.000752j
[2025-09-19 15:44:24] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -66.388381+0.002468j
[2025-09-19 15:44:56] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -66.413535-0.001962j
[2025-09-19 15:45:28] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -66.422028+0.000394j
[2025-09-19 15:46:00] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -66.415430+0.000718j
[2025-09-19 15:46:33] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -66.401556+0.001391j
[2025-09-19 15:47:05] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -66.391246-0.000905j
[2025-09-19 15:47:37] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -66.431156-0.001161j
[2025-09-19 15:48:09] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -66.410716+0.001996j
[2025-09-19 15:48:41] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -66.408601-0.000951j
[2025-09-19 15:49:13] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -66.408691-0.000978j
[2025-09-19 15:49:45] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -66.413060+0.000258j
[2025-09-19 15:50:18] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -66.421862+0.000682j
[2025-09-19 15:50:50] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -66.410252-0.000599j
[2025-09-19 15:51:22] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -66.414648-0.000577j
[2025-09-19 15:51:54] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -66.418093-0.002182j
[2025-09-19 15:52:26] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -66.404333+0.001892j
[2025-09-19 15:52:58] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -66.406854-0.000069j
[2025-09-19 15:53:31] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -66.410709-0.002979j
[2025-09-19 15:54:03] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -66.424529-0.000016j
[2025-09-19 15:54:35] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -66.435867+0.005650j
[2025-09-19 15:55:07] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -66.409964+0.002421j
[2025-09-19 15:55:39] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -66.401247+0.003525j
[2025-09-19 15:56:11] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -66.406906-0.000024j
[2025-09-19 15:56:44] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -66.413607+0.004324j
[2025-09-19 15:57:16] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -66.400250-0.000290j
[2025-09-19 15:57:48] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -66.407884-0.000355j
[2025-09-19 15:58:20] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -66.435886+0.001001j
[2025-09-19 15:58:52] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -66.412458+0.002290j
[2025-09-19 15:59:24] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -66.400812-0.001305j
[2025-09-19 15:59:57] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -66.421841-0.000345j
[2025-09-19 16:00:29] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -66.404774-0.001348j
[2025-09-19 16:01:01] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -66.419058-0.003973j
[2025-09-19 16:01:33] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -66.393320+0.001758j
[2025-09-19 16:02:05] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -66.397376-0.000332j
[2025-09-19 16:02:37] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -66.417907-0.000088j
[2025-09-19 16:02:37] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-19 16:03:10] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -66.407352+0.000339j
[2025-09-19 16:03:42] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -66.414694-0.001985j
[2025-09-19 16:04:14] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -66.410410+0.000255j
[2025-09-19 16:04:46] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -66.413554+0.003309j
[2025-09-19 16:05:18] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -66.430638-0.001213j
[2025-09-19 16:05:50] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -66.410596+0.004050j
[2025-09-19 16:06:23] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -66.399148+0.002630j
[2025-09-19 16:06:55] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -66.406179-0.000199j
[2025-09-19 16:07:27] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -66.439866-0.001513j
[2025-09-19 16:07:59] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -66.412662-0.000747j
[2025-09-19 16:08:31] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -66.405746+0.000999j
[2025-09-19 16:09:03] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -66.424378+0.001025j
[2025-09-19 16:09:36] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -66.412440+0.002800j
[2025-09-19 16:10:08] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -66.399733+0.000716j
[2025-09-19 16:10:40] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -66.429166-0.001753j
[2025-09-19 16:11:12] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -66.411993-0.001215j
[2025-09-19 16:11:44] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -66.398013+0.003530j
[2025-09-19 16:12:17] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -66.416411-0.003454j
[2025-09-19 16:12:49] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -66.410819+0.001899j
[2025-09-19 16:13:21] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -66.394930-0.000324j
[2025-09-19 16:13:53] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -66.416572-0.000484j
[2025-09-19 16:14:25] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -66.421376-0.000632j
[2025-09-19 16:14:57] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -66.397020+0.000564j
[2025-09-19 16:15:30] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -66.428283-0.000409j
[2025-09-19 16:16:02] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -66.413970-0.000082j
[2025-09-19 16:16:34] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -66.405428-0.001046j
[2025-09-19 16:17:06] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -66.405243+0.000955j
[2025-09-19 16:17:38] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -66.402161+0.000116j
[2025-09-19 16:18:11] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -66.413760-0.000261j
[2025-09-19 16:18:43] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -66.410714-0.006458j
[2025-09-19 16:19:15] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -66.397648-0.001101j
[2025-09-19 16:19:47] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -66.422541-0.000349j
[2025-09-19 16:20:19] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -66.396702-0.001857j
[2025-09-19 16:20:51] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -66.388303-0.001282j
[2025-09-19 16:21:23] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -66.399040-0.000713j
[2025-09-19 16:21:55] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -66.399513-0.001250j
[2025-09-19 16:22:28] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -66.396066+0.000464j
[2025-09-19 16:23:00] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -66.430743+0.002607j
[2025-09-19 16:23:32] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -66.410992-0.001490j
[2025-09-19 16:24:04] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -66.424359-0.000466j
[2025-09-19 16:24:36] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -66.427811-0.002009j
[2025-09-19 16:25:08] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -66.403262+0.002468j
[2025-09-19 16:25:41] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -66.422756-0.003322j
[2025-09-19 16:26:13] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -66.409107-0.002199j
[2025-09-19 16:26:45] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -66.407253+0.000862j
[2025-09-19 16:27:17] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -66.396400-0.002970j
[2025-09-19 16:27:49] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -66.402552+0.002090j
[2025-09-19 16:28:21] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -66.411476+0.003545j
[2025-09-19 16:28:54] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -66.410798+0.001254j
[2025-09-19 16:29:26] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -66.428308+0.000572j
[2025-09-19 16:29:58] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -66.395210-0.000152j
[2025-09-19 16:30:30] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -66.400704+0.000798j
[2025-09-19 16:31:02] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -66.414632+0.001188j
[2025-09-19 16:31:34] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -66.409102-0.005279j
[2025-09-19 16:32:07] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -66.410595-0.002788j
[2025-09-19 16:32:39] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -66.404741+0.003088j
[2025-09-19 16:33:11] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -66.413685+0.001801j
[2025-09-19 16:33:43] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -66.404763-0.000145j
[2025-09-19 16:34:15] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -66.401089+0.002369j
[2025-09-19 16:34:47] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -66.418934+0.001993j
[2025-09-19 16:35:20] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -66.407443-0.001614j
[2025-09-19 16:35:52] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -66.405863+0.006256j
[2025-09-19 16:36:24] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -66.405641-0.000014j
[2025-09-19 16:36:56] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -66.407530+0.004452j
[2025-09-19 16:37:28] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -66.421929+0.000919j
[2025-09-19 16:38:00] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -66.414508+0.000994j
[2025-09-19 16:38:33] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -66.414876+0.000567j
[2025-09-19 16:39:05] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -66.427694-0.009956j
[2025-09-19 16:39:37] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -66.419541-0.002578j
[2025-09-19 16:40:09] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -66.416664+0.000918j
[2025-09-19 16:40:41] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -66.405687-0.000526j
[2025-09-19 16:41:14] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -66.408931-0.001103j
[2025-09-19 16:41:46] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -66.400649-0.001943j
[2025-09-19 16:42:18] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -66.403178-0.000313j
[2025-09-19 16:42:50] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -66.405946-0.004576j
[2025-09-19 16:43:22] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -66.428316+0.000232j
[2025-09-19 16:43:54] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -66.428351-0.002739j
[2025-09-19 16:44:27] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -66.413482+0.000281j
[2025-09-19 16:44:59] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -66.412796+0.001506j
[2025-09-19 16:45:31] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -66.423542-0.000440j
[2025-09-19 16:46:03] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -66.410204+0.000548j
[2025-09-19 16:46:35] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -66.403648-0.000081j
[2025-09-19 16:47:07] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -66.410716-0.003295j
[2025-09-19 16:47:40] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -66.422153+0.001789j
[2025-09-19 16:48:12] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -66.403541-0.001270j
[2025-09-19 16:48:44] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -66.414535+0.001042j
[2025-09-19 16:49:16] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -66.393792+0.003445j
[2025-09-19 16:49:48] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -66.405096+0.000861j
[2025-09-19 16:50:21] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -66.414752+0.002877j
[2025-09-19 16:50:53] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -66.431284+0.001686j
[2025-09-19 16:51:25] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -66.406292-0.000116j
[2025-09-19 16:51:57] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -66.401808+0.000975j
[2025-09-19 16:52:29] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -66.419335-0.004087j
[2025-09-19 16:53:01] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -66.400915+0.002949j
[2025-09-19 16:53:34] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -66.404264-0.001957j
[2025-09-19 16:54:06] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -66.411499-0.002416j
[2025-09-19 16:54:38] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -66.432556-0.001354j
[2025-09-19 16:55:10] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -66.389364-0.002454j
[2025-09-19 16:55:42] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -66.385671-0.003877j
[2025-09-19 16:56:14] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -66.380743-0.002956j
[2025-09-19 16:56:46] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -66.410391-0.004228j
[2025-09-19 16:57:19] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -66.416767-0.002825j
[2025-09-19 16:57:51] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -66.430074-0.004067j
[2025-09-19 16:58:23] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -66.422563+0.001371j
[2025-09-19 16:58:55] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -66.408354+0.003955j
[2025-09-19 16:58:55] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-19 16:59:27] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -66.397443+0.000621j
[2025-09-19 16:59:59] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -66.406886-0.001868j
[2025-09-19 17:00:31] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -66.399307-0.001840j
[2025-09-19 17:01:04] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -66.410149-0.000960j
[2025-09-19 17:01:36] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -66.393259+0.002927j
[2025-09-19 17:02:08] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -66.409060-0.001967j
[2025-09-19 17:02:40] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -66.404292+0.000542j
[2025-09-19 17:03:12] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -66.427347-0.001652j
[2025-09-19 17:03:44] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -66.395743+0.003022j
[2025-09-19 17:04:17] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -66.388308-0.000198j
[2025-09-19 17:04:49] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -66.410518+0.002083j
[2025-09-19 17:05:21] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -66.387172-0.000932j
[2025-09-19 17:05:53] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -66.400158-0.003559j
[2025-09-19 17:06:25] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -66.408367-0.002600j
[2025-09-19 17:06:57] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -66.410404-0.002171j
[2025-09-19 17:07:29] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -66.397332+0.001077j
[2025-09-19 17:08:00] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -66.422732+0.002547j
[2025-09-19 17:08:32] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -66.427294-0.001550j
[2025-09-19 17:09:05] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -66.426482-0.003430j
[2025-09-19 17:09:37] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -66.419869-0.000704j
[2025-09-19 17:10:09] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -66.394220-0.003993j
[2025-09-19 17:10:41] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -66.408783+0.001470j
[2025-09-19 17:11:13] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -66.396180+0.002788j
[2025-09-19 17:11:45] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -66.425276-0.001813j
[2025-09-19 17:12:17] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -66.414547+0.002453j
[2025-09-19 17:12:50] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -66.418327+0.000340j
[2025-09-19 17:13:22] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -66.412951+0.003916j
[2025-09-19 17:13:54] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -66.419166+0.000248j
[2025-09-19 17:14:26] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -66.411730+0.001217j
[2025-09-19 17:14:58] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -66.412318-0.000854j
[2025-09-19 17:15:29] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -66.411424+0.002430j
[2025-09-19 17:16:02] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -66.416794+0.001281j
[2025-09-19 17:16:34] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -66.416808+0.008552j
[2025-09-19 17:17:06] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -66.401358-0.004452j
[2025-09-19 17:17:38] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -66.415130+0.004717j
[2025-09-19 17:18:10] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -66.398506-0.003008j
[2025-09-19 17:18:43] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -66.411724+0.005579j
[2025-09-19 17:19:15] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -66.419580+0.001377j
[2025-09-19 17:19:47] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -66.402552-0.005943j
[2025-09-19 17:20:19] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -66.417828-0.002664j
[2025-09-19 17:20:51] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -66.404440+0.001944j
[2025-09-19 17:21:23] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -66.406998+0.000686j
[2025-09-19 17:21:56] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -66.409010+0.001553j
[2025-09-19 17:22:28] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -66.410142-0.000917j
[2025-09-19 17:23:00] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -66.413471-0.001811j
[2025-09-19 17:23:32] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -66.413936-0.001035j
[2025-09-19 17:24:04] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -66.416101+0.003886j
[2025-09-19 17:24:37] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -66.410605-0.000441j
[2025-09-19 17:25:09] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -66.412146+0.001020j
[2025-09-19 17:25:41] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -66.408653-0.000083j
[2025-09-19 17:26:13] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -66.424043+0.003570j
[2025-09-19 17:26:45] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -66.395007-0.001361j
[2025-09-19 17:27:17] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -66.420584-0.000872j
[2025-09-19 17:27:50] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -66.413199+0.000301j
[2025-09-19 17:28:22] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -66.405569+0.002213j
[2025-09-19 17:28:54] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -66.412976-0.001464j
[2025-09-19 17:29:26] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -66.410903-0.000419j
[2025-09-19 17:29:58] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -66.405052+0.001836j
[2025-09-19 17:30:30] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -66.399733-0.000294j
[2025-09-19 17:31:02] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -66.388545+0.001429j
[2025-09-19 17:31:35] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -66.402392-0.002524j
[2025-09-19 17:32:07] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -66.408069+0.000646j
[2025-09-19 17:32:39] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -66.418924-0.001070j
[2025-09-19 17:33:11] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -66.425750+0.002006j
[2025-09-19 17:33:43] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -66.421706-0.002775j
[2025-09-19 17:34:15] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -66.421300-0.003381j
[2025-09-19 17:34:47] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -66.415149-0.000805j
[2025-09-19 17:35:20] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -66.410585+0.000520j
[2025-09-19 17:35:52] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -66.407258+0.000472j
[2025-09-19 17:36:24] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -66.416354+0.000309j
[2025-09-19 17:36:56] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -66.398245+0.000899j
[2025-09-19 17:37:28] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -66.424590+0.002602j
[2025-09-19 17:38:00] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -66.425355+0.002322j
[2025-09-19 17:38:33] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -66.420989-0.004546j
[2025-09-19 17:39:05] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -66.396396+0.001304j
[2025-09-19 17:39:37] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -66.406922+0.002339j
[2025-09-19 17:40:09] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -66.425620-0.000041j
[2025-09-19 17:40:41] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -66.414808-0.000942j
[2025-09-19 17:41:13] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -66.401702+0.004940j
[2025-09-19 17:41:46] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -66.409680+0.001260j
[2025-09-19 17:42:18] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -66.421880-0.002474j
[2025-09-19 17:42:50] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -66.403212-0.002223j
[2025-09-19 17:43:22] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -66.405550-0.002728j
[2025-09-19 17:43:54] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -66.420856+0.004263j
[2025-09-19 17:44:26] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -66.411565-0.000685j
[2025-09-19 17:44:59] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -66.408173+0.000190j
[2025-09-19 17:45:31] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -66.397665-0.001316j
[2025-09-19 17:46:03] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -66.409716-0.002415j
[2025-09-19 17:46:35] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -66.424036+0.001149j
[2025-09-19 17:47:07] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -66.422110-0.000342j
[2025-09-19 17:47:39] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -66.415045+0.006425j
[2025-09-19 17:48:12] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -66.404518-0.000121j
[2025-09-19 17:48:44] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -66.420468+0.001606j
[2025-09-19 17:49:16] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -66.412072-0.002239j
[2025-09-19 17:49:48] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -66.411601+0.002584j
[2025-09-19 17:50:20] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -66.413423+0.004063j
[2025-09-19 17:50:52] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -66.429838-0.003439j
[2025-09-19 17:51:25] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -66.420638-0.000384j
[2025-09-19 17:51:57] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -66.423651-0.000397j
[2025-09-19 17:52:29] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -66.393278-0.000768j
[2025-09-19 17:53:01] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -66.423949+0.002487j
[2025-09-19 17:53:33] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -66.407889+0.000144j
[2025-09-19 17:54:06] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -66.409284+0.001911j
[2025-09-19 17:54:38] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -66.418255+0.001041j
[2025-09-19 17:55:10] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -66.417269+0.002041j
[2025-09-19 17:55:10] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-19 17:55:42] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -66.409956+0.005659j
[2025-09-19 17:56:14] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -66.413376+0.000512j
[2025-09-19 17:56:47] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -66.415181+0.001452j
[2025-09-19 17:57:19] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -66.406442+0.005485j
[2025-09-19 17:57:51] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -66.420924-0.003275j
[2025-09-19 17:58:23] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -66.420336-0.001539j
[2025-09-19 17:58:55] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -66.419578-0.001212j
[2025-09-19 17:59:27] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -66.401588+0.001426j
[2025-09-19 18:00:00] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -66.423800+0.002093j
[2025-09-19 18:00:32] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -66.409497+0.003983j
[2025-09-19 18:01:04] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -66.404270-0.002618j
[2025-09-19 18:01:36] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -66.413360-0.002764j
[2025-09-19 18:02:08] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -66.410827+0.000439j
[2025-09-19 18:02:41] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -66.412058+0.006604j
[2025-09-19 18:03:13] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -66.415450-0.002704j
[2025-09-19 18:03:45] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -66.419194+0.003331j
[2025-09-19 18:04:17] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -66.410028-0.002555j
[2025-09-19 18:04:49] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -66.401367+0.000834j
[2025-09-19 18:05:21] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -66.400504-0.001042j
[2025-09-19 18:05:53] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -66.414988-0.005983j
[2025-09-19 18:06:26] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -66.412554+0.003725j
[2025-09-19 18:06:58] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -66.412860+0.002958j
[2025-09-19 18:07:30] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -66.416466-0.000963j
[2025-09-19 18:08:02] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -66.415414+0.000061j
[2025-09-19 18:08:34] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -66.411929+0.000507j
[2025-09-19 18:09:06] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -66.410150-0.000606j
[2025-09-19 18:09:38] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -66.393595-0.000701j
[2025-09-19 18:10:11] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -66.435025+0.002813j
[2025-09-19 18:10:43] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -66.424841+0.002086j
[2025-09-19 18:11:15] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -66.423203-0.002124j
[2025-09-19 18:11:15] RESTART #2 | Period: 600
[2025-09-19 18:11:47] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -66.415745-0.001865j
[2025-09-19 18:12:19] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -66.413139+0.001450j
[2025-09-19 18:12:51] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -66.407626+0.000649j
[2025-09-19 18:13:24] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -66.401896+0.001905j
[2025-09-19 18:13:56] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -66.420103+0.010112j
[2025-09-19 18:14:28] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -66.416809+0.000451j
[2025-09-19 18:15:00] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -66.413101-0.002548j
[2025-09-19 18:15:32] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -66.398670+0.000702j
[2025-09-19 18:16:04] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -66.415298-0.000388j
[2025-09-19 18:16:36] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -66.422742-0.000140j
[2025-09-19 18:17:09] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -66.403321-0.001907j
[2025-09-19 18:17:41] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -66.399530+0.002885j
[2025-09-19 18:18:13] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -66.407434+0.001585j
[2025-09-19 18:18:45] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -66.407473+0.000211j
[2025-09-19 18:19:17] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -66.415746-0.001277j
[2025-09-19 18:19:49] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -66.408558+0.001630j
[2025-09-19 18:20:21] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -66.417638+0.000591j
[2025-09-19 18:20:54] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -66.408974-0.000133j
[2025-09-19 18:21:26] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -66.422934+0.000856j
[2025-09-19 18:21:58] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -66.408322+0.000520j
[2025-09-19 18:22:30] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -66.410790+0.003028j
[2025-09-19 18:23:02] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -66.408543+0.000588j
[2025-09-19 18:23:35] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -66.410690+0.005362j
[2025-09-19 18:24:07] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -66.401484+0.004334j
[2025-09-19 18:24:39] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -66.385919-0.000223j
[2025-09-19 18:25:11] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -66.416565+0.002784j
[2025-09-19 18:25:43] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -66.419687-0.005689j
[2025-09-19 18:26:16] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -66.400727-0.000693j
[2025-09-19 18:26:48] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -66.412607+0.003251j
[2025-09-19 18:27:20] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -66.428621-0.002706j
[2025-09-19 18:27:52] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -66.410633+0.002149j
[2025-09-19 18:28:24] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -66.408929+0.001226j
[2025-09-19 18:28:56] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -66.417292+0.004148j
[2025-09-19 18:29:29] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -66.415987+0.000544j
[2025-09-19 18:30:01] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -66.412606+0.005849j
[2025-09-19 18:30:33] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -66.409719+0.000090j
[2025-09-19 18:31:05] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -66.409499-0.004715j
[2025-09-19 18:31:37] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -66.422563+0.001539j
[2025-09-19 18:32:10] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -66.414134-0.003626j
[2025-09-19 18:32:42] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -66.412546+0.000066j
[2025-09-19 18:33:14] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -66.399705-0.002722j
[2025-09-19 18:33:46] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -66.411397-0.003056j
[2025-09-19 18:34:18] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -66.422854+0.003322j
[2025-09-19 18:34:50] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -66.412718-0.002407j
[2025-09-19 18:35:23] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -66.406605+0.002692j
[2025-09-19 18:35:55] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -66.409281-0.002310j
[2025-09-19 18:36:27] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -66.406365-0.001616j
[2025-09-19 18:36:59] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -66.395415-0.001101j
[2025-09-19 18:37:31] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -66.430430-0.006883j
[2025-09-19 18:38:03] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -66.422678-0.004001j
[2025-09-19 18:38:36] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -66.419941+0.005975j
[2025-09-19 18:39:08] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -66.414355-0.002598j
[2025-09-19 18:39:40] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -66.403206+0.001673j
[2025-09-19 18:40:12] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -66.413057-0.001022j
[2025-09-19 18:40:44] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -66.423236+0.000726j
[2025-09-19 18:41:16] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -66.427920-0.000446j
[2025-09-19 18:41:48] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -66.397719+0.003937j
[2025-09-19 18:42:20] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -66.424270+0.000092j
[2025-09-19 18:42:53] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -66.401721-0.001289j
[2025-09-19 18:43:24] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -66.417969+0.000696j
[2025-09-19 18:43:56] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -66.423274-0.000687j
[2025-09-19 18:44:28] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -66.405700-0.000518j
[2025-09-19 18:45:00] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -66.410768-0.001580j
[2025-09-19 18:45:32] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -66.420348+0.002961j
[2025-09-19 18:46:04] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -66.412831+0.002547j
[2025-09-19 18:46:36] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -66.422568-0.003603j
[2025-09-19 18:47:09] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -66.410764-0.001593j
[2025-09-19 18:47:41] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -66.421932-0.001765j
[2025-09-19 18:48:13] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -66.418063+0.001759j
[2025-09-19 18:48:45] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -66.417687-0.000388j
[2025-09-19 18:49:17] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -66.413045+0.004644j
[2025-09-19 18:49:49] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -66.420090+0.002386j
[2025-09-19 18:50:22] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -66.406246-0.002509j
[2025-09-19 18:50:54] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -66.433651-0.002540j
[2025-09-19 18:51:26] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -66.425738+0.001020j
[2025-09-19 18:51:26] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-19 18:51:58] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -66.420551-0.002054j
[2025-09-19 18:52:30] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -66.408093+0.000929j
[2025-09-19 18:53:02] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -66.417332-0.001816j
[2025-09-19 18:53:35] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -66.409882+0.001228j
[2025-09-19 18:54:07] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -66.410836-0.002238j
[2025-09-19 18:54:39] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -66.421897-0.003627j
[2025-09-19 18:55:11] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -66.407971-0.000394j
[2025-09-19 18:55:43] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -66.418344+0.001364j
[2025-09-19 18:56:15] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -66.416159+0.000907j
[2025-09-19 18:56:48] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -66.404074+0.000104j
[2025-09-19 18:57:20] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -66.433276-0.001433j
[2025-09-19 18:57:52] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -66.407258+0.001283j
[2025-09-19 18:58:24] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -66.411932-0.002590j
[2025-09-19 18:58:55] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -66.395851-0.000903j
[2025-09-19 18:59:27] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -66.389075+0.003345j
[2025-09-19 18:59:59] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -66.418367-0.000546j
[2025-09-19 19:00:31] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -66.412792-0.001657j
[2025-09-19 19:01:04] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -66.408441+0.003033j
[2025-09-19 19:01:36] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -66.418497-0.002717j
[2025-09-19 19:02:08] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -66.405935+0.001352j
[2025-09-19 19:02:40] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -66.412295+0.000800j
[2025-09-19 19:03:12] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -66.435245+0.003146j
[2025-09-19 19:03:45] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -66.407909+0.000731j
[2025-09-19 19:04:17] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -66.412738-0.001146j
[2025-09-19 19:04:49] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -66.411197-0.001558j
[2025-09-19 19:05:21] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -66.412717+0.001053j
[2025-09-19 19:05:53] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -66.422031-0.003414j
[2025-09-19 19:06:26] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -66.415761+0.001980j
[2025-09-19 19:06:58] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -66.412580-0.001813j
[2025-09-19 19:07:30] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -66.432295+0.000032j
[2025-09-19 19:08:02] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -66.426010+0.002202j
[2025-09-19 19:08:35] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -66.395030+0.000722j
[2025-09-19 19:09:07] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -66.404187-0.000113j
[2025-09-19 19:09:39] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -66.411904-0.001954j
[2025-09-19 19:10:11] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -66.417595+0.005435j
[2025-09-19 19:10:44] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -66.417176-0.002458j
[2025-09-19 19:11:16] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -66.393250-0.000023j
[2025-09-19 19:11:48] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -66.425278-0.000036j
[2025-09-19 19:12:20] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -66.418046+0.001183j
[2025-09-19 19:12:52] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -66.435296+0.001692j
[2025-09-19 19:13:24] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -66.409892+0.000698j
[2025-09-19 19:13:57] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -66.435297+0.001498j
[2025-09-19 19:14:29] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -66.404185-0.000332j
[2025-09-19 19:15:01] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -66.413574+0.001391j
[2025-09-19 19:15:33] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -66.399175-0.004556j
[2025-09-19 19:16:05] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -66.409962+0.001278j
[2025-09-19 19:16:37] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -66.414347-0.001558j
[2025-09-19 19:17:10] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -66.421651+0.002848j
[2025-09-19 19:17:42] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -66.421046-0.000814j
[2025-09-19 19:18:14] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -66.407461-0.000563j
[2025-09-19 19:18:46] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -66.416886-0.003580j
[2025-09-19 19:19:18] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -66.421140+0.000246j
[2025-09-19 19:19:50] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -66.405976+0.000487j
[2025-09-19 19:20:23] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -66.429069-0.002933j
[2025-09-19 19:20:55] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -66.418987-0.000643j
[2025-09-19 19:21:27] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -66.405809+0.004345j
[2025-09-19 19:21:59] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -66.409469-0.001285j
[2025-09-19 19:22:31] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -66.402068-0.001676j
[2025-09-19 19:23:04] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -66.429999-0.006430j
[2025-09-19 19:23:36] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -66.404162+0.003697j
[2025-09-19 19:24:08] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -66.417446-0.001209j
[2025-09-19 19:24:40] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -66.414418+0.000381j
[2025-09-19 19:25:12] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -66.417079+0.001972j
[2025-09-19 19:25:44] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -66.406208-0.000884j
[2025-09-19 19:26:17] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -66.422171-0.002414j
[2025-09-19 19:26:49] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -66.421592+0.003524j
[2025-09-19 19:27:21] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -66.416829-0.005066j
[2025-09-19 19:27:53] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -66.421863+0.003132j
[2025-09-19 19:28:25] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -66.407192+0.000480j
[2025-09-19 19:28:57] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -66.399290-0.001141j
[2025-09-19 19:29:29] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -66.414991-0.003274j
[2025-09-19 19:30:02] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -66.406121+0.001295j
[2025-09-19 19:30:34] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -66.415233-0.000089j
[2025-09-19 19:31:06] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -66.403799+0.003300j
[2025-09-19 19:31:38] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -66.418914+0.001278j
[2025-09-19 19:32:10] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -66.395970-0.002787j
[2025-09-19 19:32:42] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -66.418201-0.000688j
[2025-09-19 19:33:15] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -66.428661+0.000521j
[2025-09-19 19:33:47] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -66.410488-0.004945j
[2025-09-19 19:34:19] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -66.397219-0.004713j
[2025-09-19 19:34:51] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -66.410308+0.000673j
[2025-09-19 19:35:23] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -66.400494-0.000660j
[2025-09-19 19:35:55] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -66.406032+0.002488j
[2025-09-19 19:36:27] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -66.434360-0.003227j
[2025-09-19 19:36:59] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -66.420678-0.010732j
[2025-09-19 19:37:31] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -66.414950+0.002412j
[2025-09-19 19:38:03] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -66.420883-0.002526j
[2025-09-19 19:38:35] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -66.392073+0.001378j
[2025-09-19 19:39:07] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -66.419054-0.000280j
[2025-09-19 19:39:39] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -66.427231+0.001439j
[2025-09-19 19:40:12] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -66.424886+0.004962j
[2025-09-19 19:40:44] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -66.432758+0.001185j
[2025-09-19 19:41:16] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -66.409880+0.002680j
[2025-09-19 19:41:48] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -66.426088-0.004436j
[2025-09-19 19:42:20] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -66.418321+0.001852j
[2025-09-19 19:42:52] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -66.418088+0.000658j
[2025-09-19 19:43:25] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -66.415598-0.000386j
[2025-09-19 19:43:57] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -66.403182+0.000776j
[2025-09-19 19:44:29] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -66.425587-0.001785j
[2025-09-19 19:45:01] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -66.398865-0.003196j
[2025-09-19 19:45:33] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -66.410690+0.001588j
[2025-09-19 19:46:05] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -66.408684+0.000239j
[2025-09-19 19:46:37] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -66.412538+0.004674j
[2025-09-19 19:47:10] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -66.419791+0.000720j
[2025-09-19 19:47:42] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -66.406614+0.002476j
[2025-09-19 19:47:42] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-19 19:48:14] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -66.402227+0.004162j
[2025-09-19 19:48:46] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -66.414924-0.002965j
[2025-09-19 19:49:18] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -66.430791+0.005131j
[2025-09-19 19:49:50] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -66.416097+0.002155j
[2025-09-19 19:50:23] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -66.398565+0.000732j
[2025-09-19 19:50:55] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -66.411723+0.000842j
[2025-09-19 19:51:27] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -66.411967-0.001345j
[2025-09-19 19:51:59] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -66.417733-0.002134j
[2025-09-19 19:52:31] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -66.405588-0.002316j
[2025-09-19 19:53:03] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -66.410418-0.000319j
[2025-09-19 19:53:35] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -66.419357+0.000250j
[2025-09-19 19:54:07] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -66.400741-0.000629j
[2025-09-19 19:54:38] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -66.426393+0.002113j
[2025-09-19 19:55:10] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -66.411494-0.003351j
[2025-09-19 19:55:42] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -66.406961-0.002777j
[2025-09-19 19:56:14] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -66.426258+0.000331j
[2025-09-19 19:56:46] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -66.419013+0.001780j
[2025-09-19 19:57:19] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -66.412186-0.000536j
[2025-09-19 19:57:51] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -66.422776-0.000043j
[2025-09-19 19:58:23] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -66.423626+0.000641j
[2025-09-19 19:58:55] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -66.409632-0.001502j
[2025-09-19 19:59:27] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -66.403762+0.002336j
[2025-09-19 19:59:59] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -66.406888-0.006064j
[2025-09-19 20:00:31] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -66.418292+0.000500j
[2025-09-19 20:01:03] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -66.403449+0.002831j
[2025-09-19 20:01:35] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -66.407308+0.002708j
[2025-09-19 20:02:07] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -66.405182+0.002505j
[2025-09-19 20:02:39] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -66.416076-0.002025j
[2025-09-19 20:03:11] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -66.409353+0.000684j
[2025-09-19 20:03:44] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -66.415984+0.000635j
[2025-09-19 20:04:16] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -66.398907+0.005556j
[2025-09-19 20:04:48] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -66.417957+0.007385j
[2025-09-19 20:05:20] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -66.424222-0.006181j
[2025-09-19 20:05:52] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -66.394735+0.001188j
[2025-09-19 20:06:24] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -66.420311+0.001405j
[2025-09-19 20:06:57] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -66.404396+0.001326j
[2025-09-19 20:07:29] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -66.426790-0.000475j
[2025-09-19 20:08:01] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -66.411230+0.000216j
[2025-09-19 20:08:33] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -66.419960-0.002936j
[2025-09-19 20:09:05] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -66.421354-0.002284j
[2025-09-19 20:09:37] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -66.421991-0.002954j
[2025-09-19 20:10:10] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -66.413576-0.002915j
[2025-09-19 20:10:41] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -66.404893+0.000010j
[2025-09-19 20:11:13] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -66.425759+0.000717j
[2025-09-19 20:11:45] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -66.415378+0.007114j
[2025-09-19 20:12:17] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -66.413717-0.001592j
[2025-09-19 20:12:49] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -66.399455-0.005078j
[2025-09-19 20:13:21] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -66.386326+0.001813j
[2025-09-19 20:13:53] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -66.396219+0.002781j
[2025-09-19 20:14:26] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -66.412029-0.000487j
[2025-09-19 20:14:58] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -66.413662-0.000684j
[2025-09-19 20:15:30] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -66.416494+0.003635j
[2025-09-19 20:16:02] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -66.410340-0.004142j
[2025-09-19 20:16:34] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -66.428007+0.003700j
[2025-09-19 20:17:06] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -66.430586+0.000861j
[2025-09-19 20:17:39] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -66.423377-0.000197j
[2025-09-19 20:18:11] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -66.399591+0.002098j
[2025-09-19 20:18:43] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -66.423782-0.001207j
[2025-09-19 20:19:15] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -66.410099+0.000299j
[2025-09-19 20:19:47] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -66.393353-0.002264j
[2025-09-19 20:20:19] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -66.412829+0.003758j
[2025-09-19 20:20:52] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -66.403523+0.001308j
[2025-09-19 20:21:24] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -66.408216-0.001470j
[2025-09-19 20:21:56] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -66.415108-0.003736j
[2025-09-19 20:22:28] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -66.418806+0.000348j
[2025-09-19 20:23:00] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -66.415431+0.002551j
[2025-09-19 20:23:33] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -66.407588-0.000211j
[2025-09-19 20:24:05] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -66.420072+0.002567j
[2025-09-19 20:24:37] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -66.419803-0.000460j
[2025-09-19 20:25:09] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -66.397809-0.004099j
[2025-09-19 20:25:41] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -66.425293+0.001454j
[2025-09-19 20:26:13] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -66.425123-0.002528j
[2025-09-19 20:26:46] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -66.416388-0.002050j
[2025-09-19 20:27:18] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -66.416845+0.000678j
[2025-09-19 20:27:50] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -66.404923+0.000268j
[2025-09-19 20:28:22] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -66.409298-0.001861j
[2025-09-19 20:28:54] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -66.424851+0.002085j
[2025-09-19 20:29:26] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -66.408632+0.003258j
[2025-09-19 20:29:59] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -66.421888+0.000537j
[2025-09-19 20:30:31] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -66.417547-0.002938j
[2025-09-19 20:31:03] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -66.427976+0.001277j
[2025-09-19 20:31:35] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -66.410587-0.000159j
[2025-09-19 20:32:07] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -66.419946+0.000854j
[2025-09-19 20:32:39] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -66.427135-0.002201j
[2025-09-19 20:33:11] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -66.421008-0.000979j
[2025-09-19 20:33:44] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -66.414989-0.002650j
[2025-09-19 20:34:16] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -66.422511+0.005322j
[2025-09-19 20:34:48] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -66.414172+0.000915j
[2025-09-19 20:35:20] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -66.420082+0.002900j
[2025-09-19 20:35:52] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -66.422059-0.004100j
[2025-09-19 20:36:24] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -66.407742-0.001280j
[2025-09-19 20:36:56] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -66.403612-0.002354j
[2025-09-19 20:37:29] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -66.413146-0.000514j
[2025-09-19 20:38:01] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -66.409762+0.004943j
[2025-09-19 20:38:33] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -66.407347+0.000592j
[2025-09-19 20:39:05] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -66.406643+0.003911j
[2025-09-19 20:39:37] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -66.409995-0.002715j
[2025-09-19 20:40:09] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -66.416791+0.000298j
[2025-09-19 20:40:41] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -66.429073-0.007508j
[2025-09-19 20:41:14] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -66.414716+0.000532j
[2025-09-19 20:41:46] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -66.415411-0.000085j
[2025-09-19 20:42:18] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -66.408206-0.000202j
[2025-09-19 20:42:50] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -66.431910+0.001464j
[2025-09-19 20:43:22] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -66.415726+0.002418j
[2025-09-19 20:43:54] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -66.408860-0.000303j
[2025-09-19 20:43:54] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-19 20:44:27] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -66.413864+0.003575j
[2025-09-19 20:44:59] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -66.420880+0.000460j
[2025-09-19 20:45:31] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -66.415977+0.002361j
[2025-09-19 20:46:03] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -66.420831-0.001321j
[2025-09-19 20:46:35] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -66.411351-0.000219j
[2025-09-19 20:47:07] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -66.413371+0.003372j
[2025-09-19 20:47:40] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -66.416557+0.001143j
[2025-09-19 20:48:12] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -66.413551+0.003239j
[2025-09-19 20:48:44] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -66.404474+0.001280j
[2025-09-19 20:49:16] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -66.419975-0.000665j
[2025-09-19 20:49:48] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -66.399704-0.000638j
[2025-09-19 20:50:20] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -66.419388-0.001928j
[2025-09-19 20:50:53] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -66.432363+0.000162j
[2025-09-19 20:51:25] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -66.405551-0.002145j
[2025-09-19 20:51:57] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -66.401415+0.001196j
[2025-09-19 20:52:29] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -66.409194+0.000656j
[2025-09-19 20:53:01] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -66.403080+0.002406j
[2025-09-19 20:53:34] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -66.419299+0.000794j
[2025-09-19 20:54:06] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -66.403118-0.005837j
[2025-09-19 20:54:38] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -66.407106+0.000729j
[2025-09-19 20:55:10] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -66.414656+0.002741j
[2025-09-19 20:55:42] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -66.417679+0.000552j
[2025-09-19 20:56:15] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -66.420400-0.004066j
[2025-09-19 20:56:47] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -66.415523+0.002688j
[2025-09-19 20:57:19] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -66.416150+0.000848j
[2025-09-19 20:57:51] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -66.427136-0.000158j
[2025-09-19 20:58:23] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -66.417175-0.001876j
[2025-09-19 20:58:55] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -66.426926-0.000859j
[2025-09-19 20:59:28] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -66.417783+0.000290j
[2025-09-19 21:00:00] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -66.407128-0.000633j
[2025-09-19 21:00:32] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -66.418729+0.000135j
[2025-09-19 21:01:04] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -66.421087-0.002972j
[2025-09-19 21:01:36] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -66.419942+0.001044j
[2025-09-19 21:02:08] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -66.424861-0.002567j
[2025-09-19 21:02:41] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -66.408982+0.001147j
[2025-09-19 21:03:13] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -66.427539-0.001032j
[2025-09-19 21:03:45] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -66.415082-0.000207j
[2025-09-19 21:04:17] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -66.424810+0.000834j
[2025-09-19 21:04:49] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -66.412681+0.002617j
[2025-09-19 21:05:21] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -66.416626-0.001291j
[2025-09-19 21:05:52] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -66.408760+0.008239j
[2025-09-19 21:06:24] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -66.401987+0.001125j
[2025-09-19 21:06:56] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -66.407354+0.002332j
[2025-09-19 21:07:29] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -66.413771+0.000307j
[2025-09-19 21:08:01] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -66.420991-0.001427j
[2025-09-19 21:08:33] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -66.411530+0.002681j
[2025-09-19 21:09:05] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -66.423485+0.000191j
[2025-09-19 21:09:37] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -66.402536+0.001217j
[2025-09-19 21:10:09] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -66.425296+0.000493j
[2025-09-19 21:10:41] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -66.412216+0.002673j
[2025-09-19 21:11:14] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -66.414071-0.001746j
[2025-09-19 21:11:46] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -66.399767+0.002967j
[2025-09-19 21:12:18] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -66.413402-0.002415j
[2025-09-19 21:12:50] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -66.425811-0.001519j
[2025-09-19 21:13:22] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -66.413944-0.005226j
[2025-09-19 21:13:54] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -66.412369-0.000961j
[2025-09-19 21:14:27] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -66.404459+0.000146j
[2025-09-19 21:14:59] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -66.419828-0.004266j
[2025-09-19 21:15:31] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -66.412532-0.002285j
[2025-09-19 21:16:02] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -66.411195-0.001390j
[2025-09-19 21:16:34] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -66.419346+0.003868j
[2025-09-19 21:17:06] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -66.412993-0.000393j
[2025-09-19 21:17:38] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -66.402521+0.000651j
[2025-09-19 21:18:11] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -66.420035+0.000260j
[2025-09-19 21:18:43] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -66.411134+0.000150j
[2025-09-19 21:19:15] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -66.416708-0.001814j
[2025-09-19 21:19:47] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -66.423113-0.003205j
[2025-09-19 21:20:19] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -66.413567+0.000610j
[2025-09-19 21:20:51] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -66.423663+0.000949j
[2025-09-19 21:21:24] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -66.418648-0.000942j
[2025-09-19 21:21:56] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -66.430735-0.000275j
[2025-09-19 21:22:28] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -66.419278-0.001391j
[2025-09-19 21:23:00] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -66.423961-0.000905j
[2025-09-19 21:23:32] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -66.420520-0.000660j
[2025-09-19 21:24:05] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -66.427062+0.001616j
[2025-09-19 21:24:37] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -66.406366+0.001202j
[2025-09-19 21:25:09] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -66.424883-0.001970j
[2025-09-19 21:25:41] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -66.408784-0.003410j
[2025-09-19 21:26:13] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -66.430126+0.001739j
[2025-09-19 21:26:45] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -66.408711-0.001726j
[2025-09-19 21:27:18] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -66.434931+0.001650j
[2025-09-19 21:27:50] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -66.417162-0.000116j
[2025-09-19 21:28:22] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -66.410545-0.002241j
[2025-09-19 21:28:54] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -66.434552-0.001024j
[2025-09-19 21:29:26] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -66.412612-0.002639j
[2025-09-19 21:29:59] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -66.419614+0.000981j
[2025-09-19 21:30:30] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -66.422770+0.001470j
[2025-09-19 21:31:02] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -66.425809-0.000631j
[2025-09-19 21:31:34] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -66.403671+0.003382j
[2025-09-19 21:32:06] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -66.403541-0.002357j
[2025-09-19 21:32:38] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -66.417352-0.002111j
[2025-09-19 21:33:10] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -66.412647+0.002504j
[2025-09-19 21:33:43] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -66.417596-0.003098j
[2025-09-19 21:34:15] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -66.445306+0.000042j
[2025-09-19 21:34:47] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -66.405007-0.002729j
[2025-09-19 21:35:19] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -66.414047+0.001365j
[2025-09-19 21:35:51] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -66.417422-0.000688j
[2025-09-19 21:36:23] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -66.408629+0.001513j
[2025-09-19 21:36:56] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -66.388989-0.000460j
[2025-09-19 21:37:28] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -66.418751-0.000385j
[2025-09-19 21:38:00] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -66.404223-0.001553j
[2025-09-19 21:38:32] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -66.411459-0.000087j
[2025-09-19 21:39:04] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -66.420950+0.002057j
[2025-09-19 21:39:37] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -66.421530+0.002091j
[2025-09-19 21:40:09] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -66.412768-0.000980j
[2025-09-19 21:40:09] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-19 21:40:41] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -66.418279+0.001683j
[2025-09-19 21:41:13] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -66.420908-0.003415j
[2025-09-19 21:41:45] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -66.406746+0.001577j
[2025-09-19 21:42:17] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -66.421188-0.001762j
[2025-09-19 21:42:49] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -66.398332-0.000391j
[2025-09-19 21:43:21] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -66.412058-0.000523j
[2025-09-19 21:43:53] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -66.419970+0.000740j
[2025-09-19 21:44:25] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -66.404551-0.010500j
[2025-09-19 21:44:57] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -66.421614-0.000076j
[2025-09-19 21:45:29] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -66.416980+0.001094j
[2025-09-19 21:46:01] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -66.419002+0.003398j
[2025-09-19 21:46:33] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -66.409871-0.000416j
[2025-09-19 21:47:06] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -66.428089-0.001328j
[2025-09-19 21:47:38] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -66.421016+0.001307j
[2025-09-19 21:48:10] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -66.406112-0.002589j
[2025-09-19 21:48:42] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -66.418009-0.002115j
[2025-09-19 21:49:14] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -66.421674-0.000216j
[2025-09-19 21:49:46] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -66.415104-0.001639j
[2025-09-19 21:50:18] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -66.405776+0.002073j
[2025-09-19 21:50:50] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -66.409591+0.003120j
[2025-09-19 21:51:23] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -66.404550+0.000382j
[2025-09-19 21:51:55] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -66.420837+0.001214j
[2025-09-19 21:52:27] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -66.432794-0.005790j
[2025-09-19 21:52:59] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -66.413028+0.001594j
[2025-09-19 21:53:31] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -66.413791-0.001965j
[2025-09-19 21:54:03] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -66.422774-0.000505j
[2025-09-19 21:54:35] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -66.418787+0.002299j
[2025-09-19 21:55:08] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -66.418940+0.000970j
[2025-09-19 21:55:40] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -66.409012-0.002134j
[2025-09-19 21:56:12] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -66.389945+0.001187j
[2025-09-19 21:56:44] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -66.420538-0.002677j
[2025-09-19 21:57:16] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -66.411792+0.001756j
[2025-09-19 21:57:48] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -66.405207+0.005013j
[2025-09-19 21:58:20] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -66.408882+0.001228j
[2025-09-19 21:58:52] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -66.416882-0.003726j
[2025-09-19 21:59:24] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -66.418372-0.000288j
[2025-09-19 21:59:56] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -66.422517+0.000828j
[2025-09-19 22:00:28] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -66.399492-0.002852j
[2025-09-19 22:01:00] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -66.419018-0.000398j
[2025-09-19 22:01:32] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -66.421685+0.000273j
[2025-09-19 22:02:05] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -66.428438+0.002556j
[2025-09-19 22:02:37] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -66.418543-0.003662j
[2025-09-19 22:03:09] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -66.428485-0.000659j
[2025-09-19 22:03:41] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -66.407841+0.001271j
[2025-09-19 22:04:12] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -66.414355+0.000292j
[2025-09-19 22:04:44] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -66.434300+0.002093j
[2025-09-19 22:05:16] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -66.407502-0.000501j
[2025-09-19 22:05:48] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -66.431940+0.000411j
[2025-09-19 22:06:21] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -66.422432+0.001193j
[2025-09-19 22:06:53] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -66.396262-0.000792j
[2025-09-19 22:07:25] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -66.405524+0.000140j
[2025-09-19 22:07:57] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -66.397139+0.001366j
[2025-09-19 22:08:29] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -66.411364+0.001225j
[2025-09-19 22:09:01] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -66.412784+0.000329j
[2025-09-19 22:09:34] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -66.420528-0.000688j
[2025-09-19 22:10:06] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -66.404327-0.000049j
[2025-09-19 22:10:38] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -66.433579+0.004401j
[2025-09-19 22:11:10] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -66.420051-0.000041j
[2025-09-19 22:11:42] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -66.406960+0.004035j
[2025-09-19 22:12:15] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -66.423035+0.002214j
[2025-09-19 22:12:47] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -66.416213+0.000909j
[2025-09-19 22:13:19] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -66.412280+0.002665j
[2025-09-19 22:13:51] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -66.408042-0.001929j
[2025-09-19 22:14:23] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -66.411866-0.001579j
[2025-09-19 22:14:56] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -66.420699+0.001561j
[2025-09-19 22:15:28] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -66.427214+0.003074j
[2025-09-19 22:16:00] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -66.416472-0.003350j
[2025-09-19 22:16:32] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -66.418357-0.000600j
[2025-09-19 22:17:04] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -66.420256+0.001480j
[2025-09-19 22:17:36] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -66.407276+0.001649j
[2025-09-19 22:18:09] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -66.409210+0.003000j
[2025-09-19 22:18:41] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -66.421296+0.000393j
[2025-09-19 22:19:13] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -66.426850+0.000514j
[2025-09-19 22:19:45] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -66.420694-0.000723j
[2025-09-19 22:20:17] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -66.419933-0.001109j
[2025-09-19 22:20:50] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -66.411624+0.000896j
[2025-09-19 22:21:22] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -66.411992+0.005071j
[2025-09-19 22:21:54] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -66.413331+0.001447j
[2025-09-19 22:22:26] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -66.415957-0.001618j
[2025-09-19 22:22:58] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -66.414024-0.002188j
[2025-09-19 22:23:30] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -66.412563+0.000677j
[2025-09-19 22:24:03] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -66.413090+0.001797j
[2025-09-19 22:24:35] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -66.425922+0.000426j
[2025-09-19 22:25:07] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -66.423318+0.002829j
[2025-09-19 22:25:39] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -66.404379-0.006247j
[2025-09-19 22:26:11] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -66.410075+0.001744j
[2025-09-19 22:26:43] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -66.416677+0.001697j
[2025-09-19 22:27:15] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -66.419603-0.000341j
[2025-09-19 22:27:47] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -66.415844-0.001835j
[2025-09-19 22:28:20] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -66.411291+0.003451j
[2025-09-19 22:28:52] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -66.415639+0.000999j
[2025-09-19 22:29:24] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -66.433076+0.000474j
[2025-09-19 22:29:56] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -66.421967+0.000622j
[2025-09-19 22:30:28] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -66.412280-0.000456j
[2025-09-19 22:31:00] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -66.422127-0.000742j
[2025-09-19 22:31:33] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -66.411796+0.000173j
[2025-09-19 22:32:05] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -66.427995-0.001339j
[2025-09-19 22:32:37] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -66.430829+0.000081j
[2025-09-19 22:33:09] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -66.392868+0.000691j
[2025-09-19 22:33:41] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -66.402319+0.002008j
[2025-09-19 22:34:13] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -66.441596+0.000426j
[2025-09-19 22:34:46] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -66.428185-0.001642j
[2025-09-19 22:35:18] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -66.422400-0.001135j
[2025-09-19 22:35:50] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -66.416480-0.002375j
[2025-09-19 22:36:22] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -66.401346+0.001555j
[2025-09-19 22:36:22] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-19 22:36:54] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -66.412843+0.003518j
[2025-09-19 22:37:26] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -66.416561-0.005199j
[2025-09-19 22:37:57] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -66.409482+0.006000j
[2025-09-19 22:38:30] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -66.419991+0.000100j
[2025-09-19 22:39:02] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -66.424215+0.002863j
[2025-09-19 22:39:34] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -66.422252-0.002527j
[2025-09-19 22:40:06] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -66.409688-0.001418j
[2025-09-19 22:40:38] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -66.427121-0.001380j
[2025-09-19 22:41:10] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -66.413342-0.001690j
[2025-09-19 22:41:43] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -66.406341+0.000796j
[2025-09-19 22:42:15] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -66.411778-0.000331j
[2025-09-19 22:42:47] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -66.404371+0.001524j
[2025-09-19 22:43:19] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -66.431859-0.001176j
[2025-09-19 22:43:51] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -66.443070+0.005518j
[2025-09-19 22:44:24] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -66.419120+0.000861j
[2025-09-19 22:44:56] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -66.405974+0.001219j
[2025-09-19 22:45:28] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -66.414527+0.002423j
[2025-09-19 22:46:00] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -66.408555+0.002910j
[2025-09-19 22:46:32] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -66.414468+0.000229j
[2025-09-19 22:47:05] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -66.413729+0.000937j
[2025-09-19 22:47:37] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -66.432336-0.001374j
[2025-09-19 22:48:09] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -66.429144-0.000328j
[2025-09-19 22:48:41] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -66.410048+0.001577j
[2025-09-19 22:49:13] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -66.416172+0.000896j
[2025-09-19 22:49:45] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -66.427057-0.000694j
[2025-09-19 22:50:18] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -66.414961-0.001398j
[2025-09-19 22:50:50] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -66.429104+0.000603j
[2025-09-19 22:51:22] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -66.393805-0.005056j
[2025-09-19 22:51:54] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -66.429045-0.002438j
[2025-09-19 22:52:26] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -66.408288-0.000006j
[2025-09-19 22:52:59] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -66.414576-0.000696j
[2025-09-19 22:53:31] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -66.410982+0.002613j
[2025-09-19 22:54:03] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -66.401459-0.000106j
[2025-09-19 22:54:35] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -66.421060-0.002266j
[2025-09-19 22:55:07] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -66.418587+0.002995j
[2025-09-19 22:55:39] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -66.408663-0.001083j
[2025-09-19 22:56:12] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -66.412272-0.002021j
[2025-09-19 22:56:44] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -66.428363+0.000399j
[2025-09-19 22:57:16] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -66.430736-0.002637j
[2025-09-19 22:57:48] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -66.408084+0.003389j
[2025-09-19 22:58:20] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -66.431761+0.000464j
[2025-09-19 22:58:52] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -66.408144-0.003722j
[2025-09-19 22:59:25] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -66.435179+0.002726j
[2025-09-19 22:59:57] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -66.420295-0.001713j
[2025-09-19 23:00:29] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -66.426896+0.002172j
[2025-09-19 23:01:01] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -66.411701-0.002206j
[2025-09-19 23:01:33] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -66.409340-0.001531j
[2025-09-19 23:02:05] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -66.399721-0.001433j
[2025-09-19 23:02:38] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -66.415667+0.003574j
[2025-09-19 23:03:10] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -66.421938+0.000961j
[2025-09-19 23:03:42] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -66.425658-0.000472j
[2025-09-19 23:04:14] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -66.415738-0.000074j
[2025-09-19 23:04:46] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -66.415183+0.004413j
[2025-09-19 23:05:18] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -66.419464+0.000179j
[2025-09-19 23:05:51] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -66.410604-0.008836j
[2025-09-19 23:06:23] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -66.409905-0.000308j
[2025-09-19 23:06:55] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -66.432093+0.006066j
[2025-09-19 23:07:26] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -66.409582+0.002190j
[2025-09-19 23:07:58] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -66.410755-0.001184j
[2025-09-19 23:08:30] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -66.427996+0.001605j
[2025-09-19 23:09:02] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -66.403313+0.002919j
[2025-09-19 23:09:35] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -66.411096+0.000575j
[2025-09-19 23:10:07] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -66.433004+0.000791j
[2025-09-19 23:10:39] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -66.422864-0.001810j
[2025-09-19 23:11:11] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -66.419608+0.001357j
[2025-09-19 23:11:43] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -66.414142-0.002402j
[2025-09-19 23:12:15] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -66.417539+0.000275j
[2025-09-19 23:12:48] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -66.407141+0.000562j
[2025-09-19 23:13:20] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -66.414045+0.000108j
[2025-09-19 23:13:52] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -66.429823-0.000384j
[2025-09-19 23:14:24] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -66.419815-0.003759j
[2025-09-19 23:14:56] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -66.412787+0.001082j
[2025-09-19 23:15:28] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -66.429986-0.001865j
[2025-09-19 23:16:01] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -66.415003+0.003245j
[2025-09-19 23:16:33] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -66.410104-0.001720j
[2025-09-19 23:17:05] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -66.399698-0.001943j
[2025-09-19 23:17:37] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -66.408153+0.005856j
[2025-09-19 23:18:09] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -66.417295-0.003031j
[2025-09-19 23:18:42] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -66.415274+0.003542j
[2025-09-19 23:19:14] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -66.410596-0.001087j
[2025-09-19 23:19:46] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -66.419216-0.001120j
[2025-09-19 23:20:18] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -66.436519+0.001496j
[2025-09-19 23:20:50] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -66.419841+0.001917j
[2025-09-19 23:21:22] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -66.424329-0.000452j
[2025-09-19 23:21:55] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -66.417646+0.000491j
[2025-09-19 23:22:27] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -66.418903-0.001187j
[2025-09-19 23:22:59] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -66.435907-0.001119j
[2025-09-19 23:23:31] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -66.419775-0.000681j
[2025-09-19 23:24:03] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -66.419279-0.000745j
[2025-09-19 23:24:35] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -66.426102-0.002337j
[2025-09-19 23:25:08] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -66.436826-0.000250j
[2025-09-19 23:25:40] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -66.411918+0.001168j
[2025-09-19 23:26:12] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -66.417219-0.004932j
[2025-09-19 23:26:44] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -66.407605+0.001586j
[2025-09-19 23:27:16] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -66.432243-0.000862j
[2025-09-19 23:27:48] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -66.417667+0.002495j
[2025-09-19 23:28:21] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -66.409876-0.002788j
[2025-09-19 23:28:45] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -66.420653+0.001036j
[2025-09-19 23:28:59] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -66.403419-0.004315j
[2025-09-19 23:29:13] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -66.412021-0.002458j
[2025-09-19 23:29:28] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -66.406487-0.000671j
[2025-09-19 23:29:42] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -66.407966-0.000062j
[2025-09-19 23:29:56] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -66.411016-0.001066j
[2025-09-19 23:30:11] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -66.392087+0.001015j
[2025-09-19 23:30:25] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -66.425732+0.002592j
[2025-09-19 23:30:25] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-19 23:30:25] ✅ Training completed | Restarts: 2
[2025-09-19 23:30:25] ============================================================
[2025-09-19 23:30:25] Training completed | Runtime: 33735.2s
[2025-09-19 23:30:30] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-19 23:30:30] ============================================================
