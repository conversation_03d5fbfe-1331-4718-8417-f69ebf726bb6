[2025-09-20 08:53:38] ✓ 从checkpoint恢复: results/L=6/J2=1.00/J1=0.77/training/checkpoints/final_GCNN.pkl
[2025-09-20 08:53:38]   - 迭代次数: final
[2025-09-20 08:53:38]   - 能量: -61.831875+0.002288j ± 0.009018
[2025-09-20 08:53:38]   - 时间戳: 2025-09-20T08:53:05.294206+08:00
[2025-09-20 08:53:56] ✓ 变分状态参数已从checkpoint恢复
[2025-09-20 08:53:56] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-20 08:53:56] ==================================================
[2025-09-20 08:53:56] GCNN for Shastry-Sutherland Model
[2025-09-20 08:53:56] ==================================================
[2025-09-20 08:53:56] System parameters:
[2025-09-20 08:53:56]   - System size: L=6, N=144
[2025-09-20 08:53:56]   - System parameters: J1=0.76, J2=1.0, Q=0.0
[2025-09-20 08:53:56] --------------------------------------------------
[2025-09-20 08:53:56] Model parameters:
[2025-09-20 08:53:56]   - Number of layers = 4
[2025-09-20 08:53:56]   - Number of features = 4
[2025-09-20 08:53:56]   - Total parameters = 28252
[2025-09-20 08:53:56] --------------------------------------------------
[2025-09-20 08:53:56] Training parameters:
[2025-09-20 08:53:56]   - Learning rate: 0.015
[2025-09-20 08:53:56]   - Total iterations: 1050
[2025-09-20 08:53:56]   - Annealing cycles: 3
[2025-09-20 08:53:56]   - Initial period: 150
[2025-09-20 08:53:56]   - Period multiplier: 2.0
[2025-09-20 08:53:56]   - Temperature range: 0.0-1.0
[2025-09-20 08:53:56]   - Samples: 4096
[2025-09-20 08:53:56]   - Discarded samples: 0
[2025-09-20 08:53:56]   - Chunk size: 2048
[2025-09-20 08:53:56]   - Diagonal shift: 0.2
[2025-09-20 08:53:56]   - Gradient clipping: 1.0
[2025-09-20 08:53:56]   - Checkpoint enabled: interval=105
[2025-09-20 08:53:56]   - Checkpoint directory: results/L=6/J2=1.00/J1=0.76/training/checkpoints
[2025-09-20 08:53:56] --------------------------------------------------
[2025-09-20 08:53:56] Device status:
[2025-09-20 08:53:56]   - Devices model: NVIDIA H200 NVL
[2025-09-20 08:53:56]   - Number of devices: 1
[2025-09-20 08:53:56]   - Sharding: True
[2025-09-20 08:53:56] ============================================================
[2025-09-20 08:55:30] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -9.152194+0.147988j
[2025-09-20 08:56:32] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -11.667197+0.062761j
[2025-09-20 08:57:04] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -13.790872-0.045396j
[2025-09-20 08:57:36] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -15.595561+0.019595j
[2025-09-20 08:58:08] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -17.737427+0.101360j
[2025-09-20 08:58:41] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -20.049585+0.055100j
[2025-09-20 08:59:13] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -22.957315+0.009308j
[2025-09-20 08:59:45] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -26.708154-0.036430j
[2025-09-20 09:00:17] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -31.850472-0.039968j
[2025-09-20 09:00:49] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -38.280283-0.008031j
[2025-09-20 09:01:22] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -45.576281-0.028442j
[2025-09-20 09:01:54] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -52.535121-0.048683j
[2025-09-20 09:02:26] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -57.671553+0.017608j
[2025-09-20 09:02:58] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -59.886655+0.008692j
[2025-09-20 09:03:31] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -60.622857+0.017080j
[2025-09-20 09:04:03] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -60.858421+0.001606j
[2025-09-20 09:04:35] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -60.878273-0.000962j
[2025-09-20 09:05:07] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -60.892215+0.007370j
[2025-09-20 09:05:39] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -60.872279+0.004446j
[2025-09-20 09:06:12] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -60.888678+0.005003j
[2025-09-20 09:06:44] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -60.882228+0.001205j
[2025-09-20 09:07:16] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -60.903496+0.009789j
[2025-09-20 09:07:48] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -60.915337+0.005406j
[2025-09-20 09:08:20] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -60.896109+0.005483j
[2025-09-20 09:08:53] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -60.891920+0.003525j
[2025-09-20 09:09:25] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -60.896774+0.003903j
[2025-09-20 09:09:57] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -60.902760+0.003797j
[2025-09-20 09:10:29] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -60.903753-0.000365j
[2025-09-20 09:11:02] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -60.891597+0.000607j
[2025-09-20 09:11:34] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -60.883177-0.003270j
[2025-09-20 09:12:06] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -60.908841+0.004377j
[2025-09-20 09:12:38] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -60.901960-0.000524j
[2025-09-20 09:13:10] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -60.930111-0.003316j
[2025-09-20 09:13:43] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -60.902840+0.003287j
[2025-09-20 09:14:15] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -60.912640+0.002039j
[2025-09-20 09:14:47] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -60.910822+0.000265j
[2025-09-20 09:15:19] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -60.910740+0.000747j
[2025-09-20 09:15:51] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -60.907191+0.001459j
[2025-09-20 09:16:23] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -60.915696+0.005697j
[2025-09-20 09:16:55] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -60.914708+0.000554j
[2025-09-20 09:17:27] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -60.906100+0.001445j
[2025-09-20 09:17:59] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -60.914848-0.005468j
[2025-09-20 09:18:31] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -60.899864+0.000277j
[2025-09-20 09:19:03] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -60.903893-0.001395j
[2025-09-20 09:19:35] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -60.892963-0.004870j
[2025-09-20 09:20:08] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -60.899711-0.003162j
[2025-09-20 09:20:40] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -60.921266+0.001193j
[2025-09-20 09:21:12] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -60.906293-0.002003j
[2025-09-20 09:21:44] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -60.926083-0.000394j
[2025-09-20 09:22:16] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -60.890063+0.005279j
[2025-09-20 09:22:48] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -60.909974+0.005976j
[2025-09-20 09:23:21] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -60.909897-0.000378j
[2025-09-20 09:23:53] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -60.922263+0.001769j
[2025-09-20 09:24:25] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -60.907303-0.001883j
[2025-09-20 09:24:57] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -60.900874-0.002958j
[2025-09-20 09:25:29] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -60.911542-0.002256j
[2025-09-20 09:26:01] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -60.923488+0.002097j
[2025-09-20 09:26:33] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -60.920635+0.000268j
[2025-09-20 09:27:05] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -60.891609+0.002033j
[2025-09-20 09:27:37] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -60.919733-0.000035j
[2025-09-20 09:28:09] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -60.899198+0.000897j
[2025-09-20 09:28:42] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -60.906771+0.001148j
[2025-09-20 09:29:14] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -60.898231+0.002768j
[2025-09-20 09:29:45] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -60.911140+0.003320j
[2025-09-20 09:30:17] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -60.893256-0.000574j
[2025-09-20 09:30:49] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -60.903717-0.003846j
[2025-09-20 09:31:21] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -60.904838-0.008719j
[2025-09-20 09:31:53] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -60.897310+0.003165j
[2025-09-20 09:32:26] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -60.902424+0.001959j
[2025-09-20 09:32:58] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -60.889511-0.000057j
[2025-09-20 09:33:30] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -60.900142+0.001454j
[2025-09-20 09:34:02] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -60.920930+0.001332j
[2025-09-20 09:34:34] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -60.898815+0.000672j
[2025-09-20 09:35:06] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -60.905028+0.007367j
[2025-09-20 09:35:39] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -60.921615-0.005828j
[2025-09-20 09:36:11] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -60.916688+0.001599j
[2025-09-20 09:36:43] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -60.908007-0.002314j
[2025-09-20 09:37:15] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -60.911575+0.000526j
[2025-09-20 09:37:47] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -60.920800+0.000631j
[2025-09-20 09:38:19] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -60.900338-0.004911j
[2025-09-20 09:38:51] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -60.916681+0.004277j
[2025-09-20 09:39:24] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -60.902575-0.005702j
[2025-09-20 09:39:56] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -60.913662-0.001489j
[2025-09-20 09:40:28] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -60.895446+0.000406j
[2025-09-20 09:41:00] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -60.897158+0.001188j
[2025-09-20 09:41:32] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -60.938265+0.005765j
[2025-09-20 09:42:04] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -60.917455-0.001416j
[2025-09-20 09:42:37] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -60.909283-0.000840j
[2025-09-20 09:43:08] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -60.929402+0.002160j
[2025-09-20 09:43:40] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -60.914635+0.000854j
[2025-09-20 09:44:12] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -60.918047+0.002417j
[2025-09-20 09:44:44] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -60.918343-0.001007j
[2025-09-20 09:45:16] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -60.928436-0.000908j
[2025-09-20 09:45:48] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -60.905805+0.001496j
[2025-09-20 09:46:20] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -60.921165-0.004667j
[2025-09-20 09:46:53] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -60.919085+0.002198j
[2025-09-20 09:47:25] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -60.917770-0.007872j
[2025-09-20 09:47:57] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -60.928877+0.001324j
[2025-09-20 09:48:29] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -60.917343-0.003077j
[2025-09-20 09:49:01] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -60.908667-0.002068j
[2025-09-20 09:49:33] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -60.897838+0.001742j
[2025-09-20 09:50:06] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -60.903060+0.003576j
[2025-09-20 09:50:38] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -60.923028-0.003380j
[2025-09-20 09:51:10] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -60.902844-0.001487j
[2025-09-20 09:51:42] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -60.906927+0.001397j
[2025-09-20 09:51:42] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-20 09:52:14] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -60.914219-0.002699j
[2025-09-20 09:52:46] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -60.918648+0.001547j
[2025-09-20 09:53:19] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -60.916410-0.002595j
[2025-09-20 09:53:51] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -60.932691-0.001303j
[2025-09-20 09:54:23] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -60.908377+0.003609j
[2025-09-20 09:54:55] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -60.926671+0.003640j
[2025-09-20 09:55:26] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -60.926349-0.003470j
[2025-09-20 09:55:58] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -60.900237+0.003842j
[2025-09-20 09:56:30] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -60.912451+0.000684j
[2025-09-20 09:57:02] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -60.929544+0.000861j
[2025-09-20 09:57:34] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -60.921588+0.001565j
[2025-09-20 09:58:07] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -60.899185+0.003908j
[2025-09-20 09:58:39] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -60.922546-0.002654j
[2025-09-20 09:59:11] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -60.900178-0.002416j
[2025-09-20 09:59:43] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -60.934781+0.005341j
[2025-09-20 10:00:15] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -60.930568+0.000086j
[2025-09-20 10:00:47] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -60.913717+0.000416j
[2025-09-20 10:01:20] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -60.921703+0.000189j
[2025-09-20 10:01:52] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -60.927173-0.002628j
[2025-09-20 10:02:24] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -60.901782-0.001543j
[2025-09-20 10:02:56] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -60.920458-0.003348j
[2025-09-20 10:03:28] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -60.906051-0.002688j
[2025-09-20 10:04:01] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -60.923868-0.001286j
[2025-09-20 10:04:33] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -60.923612-0.000914j
[2025-09-20 10:05:05] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -60.914480-0.004218j
[2025-09-20 10:05:37] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -60.914793+0.000098j
[2025-09-20 10:06:09] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -60.923617-0.006813j
[2025-09-20 10:06:40] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -60.903427+0.001288j
[2025-09-20 10:07:13] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -60.926112+0.002153j
[2025-09-20 10:07:45] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -60.906598+0.002932j
[2025-09-20 10:08:17] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -60.933859-0.001145j
[2025-09-20 10:08:50] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -60.920292+0.001167j
[2025-09-20 10:09:22] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -60.912628+0.003252j
[2025-09-20 10:09:54] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -60.922637+0.000654j
[2025-09-20 10:10:26] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -60.919618-0.001295j
[2025-09-20 10:10:58] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -60.916813-0.006327j
[2025-09-20 10:11:31] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -60.908877+0.005426j
[2025-09-20 10:12:03] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -60.910464-0.002328j
[2025-09-20 10:12:35] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -60.905658-0.000893j
[2025-09-20 10:13:07] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -60.906176+0.003106j
[2025-09-20 10:13:39] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -60.899416-0.000122j
[2025-09-20 10:14:11] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -60.905178+0.003587j
[2025-09-20 10:14:44] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -60.890652+0.000825j
[2025-09-20 10:15:16] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -60.893119-0.000592j
[2025-09-20 10:15:48] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -60.912567+0.008378j
[2025-09-20 10:15:48] RESTART #1 | Period: 300
[2025-09-20 10:16:20] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -60.938741+0.002429j
[2025-09-20 10:16:52] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -60.920346+0.003396j
[2025-09-20 10:17:25] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -60.928112-0.001003j
[2025-09-20 10:17:57] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -60.918518+0.000577j
[2025-09-20 10:18:29] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -60.924268+0.001372j
[2025-09-20 10:19:01] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -60.907417+0.002021j
[2025-09-20 10:19:33] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -60.923515-0.001019j
[2025-09-20 10:20:04] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -60.922247-0.000637j
[2025-09-20 10:20:36] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -60.911929-0.008894j
[2025-09-20 10:21:09] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -60.912721-0.002870j
[2025-09-20 10:21:41] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -60.905455+0.000893j
[2025-09-20 10:22:13] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -60.917410+0.000905j
[2025-09-20 10:22:45] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -60.905698+0.000816j
[2025-09-20 10:23:17] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -60.921914-0.002609j
[2025-09-20 10:23:49] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -60.921201+0.001217j
[2025-09-20 10:24:22] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -60.926704-0.004189j
[2025-09-20 10:24:54] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -60.905318+0.000607j
[2025-09-20 10:25:26] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -60.933666-0.002546j
[2025-09-20 10:25:58] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -60.930446+0.000459j
[2025-09-20 10:26:31] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -60.924278-0.000318j
[2025-09-20 10:27:03] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -60.920404+0.000561j
[2025-09-20 10:27:35] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -60.925091+0.000222j
[2025-09-20 10:28:07] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -60.890335+0.001106j
[2025-09-20 10:28:39] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -60.907523-0.000762j
[2025-09-20 10:29:12] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -60.938382-0.000590j
[2025-09-20 10:29:44] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -60.903547-0.000108j
[2025-09-20 10:30:16] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -60.929616-0.000442j
[2025-09-20 10:30:48] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -60.918262+0.003731j
[2025-09-20 10:31:20] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -60.914872-0.002435j
[2025-09-20 10:31:53] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -60.912133-0.004809j
[2025-09-20 10:32:25] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -60.917983+0.002080j
[2025-09-20 10:32:57] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -60.923664+0.001248j
[2025-09-20 10:33:29] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -60.921241+0.001551j
[2025-09-20 10:34:02] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -60.913436+0.000801j
[2025-09-20 10:34:34] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -60.915634-0.002056j
[2025-09-20 10:35:06] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -60.923285-0.000445j
[2025-09-20 10:35:38] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -60.901504-0.003068j
[2025-09-20 10:36:11] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -60.909991-0.001008j
[2025-09-20 10:36:43] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -60.914644-0.001755j
[2025-09-20 10:37:15] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -60.928795+0.006003j
[2025-09-20 10:37:47] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -60.917148+0.005898j
[2025-09-20 10:38:20] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -60.905761+0.002865j
[2025-09-20 10:38:52] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -60.915547-0.001989j
[2025-09-20 10:39:24] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -60.920406-0.002350j
[2025-09-20 10:39:56] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -60.931553+0.001702j
[2025-09-20 10:40:28] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -60.920532+0.000060j
[2025-09-20 10:41:01] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -60.915238-0.002042j
[2025-09-20 10:41:33] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -60.921009+0.000212j
[2025-09-20 10:42:05] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -60.930713+0.001168j
[2025-09-20 10:42:37] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -60.913422-0.004149j
[2025-09-20 10:43:10] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -60.920445+0.002011j
[2025-09-20 10:43:42] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -60.915280-0.001936j
[2025-09-20 10:44:14] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -60.909568-0.001733j
[2025-09-20 10:44:46] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -60.899255+0.001427j
[2025-09-20 10:45:18] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -60.932755-0.003094j
[2025-09-20 10:45:51] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -60.898808-0.001737j
[2025-09-20 10:46:23] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -60.902707+0.001796j
[2025-09-20 10:46:55] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -60.923167+0.004205j
[2025-09-20 10:47:27] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -60.923341-0.000332j
[2025-09-20 10:48:00] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -60.913321-0.001750j
[2025-09-20 10:48:00] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-20 10:48:32] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -60.909030-0.002313j
[2025-09-20 10:49:04] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -60.897752+0.006719j
[2025-09-20 10:49:36] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -60.891681-0.000771j
[2025-09-20 10:50:08] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -60.905909-0.004808j
[2025-09-20 10:50:41] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -60.931618-0.000525j
[2025-09-20 10:51:13] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -60.927330-0.004054j
[2025-09-20 10:51:45] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -60.921482-0.001965j
[2025-09-20 10:52:18] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -60.917822-0.001574j
[2025-09-20 10:52:50] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -60.925254+0.000534j
[2025-09-20 10:53:22] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -60.919207-0.002896j
[2025-09-20 10:53:54] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -60.921234+0.001123j
[2025-09-20 10:54:26] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -60.909141-0.000455j
[2025-09-20 10:54:59] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -60.926142-0.001256j
[2025-09-20 10:55:31] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -60.933224+0.000128j
[2025-09-20 10:56:03] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -60.938586+0.002652j
[2025-09-20 10:56:35] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -60.926734+0.003306j
[2025-09-20 10:57:07] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -60.905145+0.002441j
[2025-09-20 10:57:39] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -60.931985+0.000975j
[2025-09-20 10:58:12] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -60.912747-0.002319j
[2025-09-20 10:58:44] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -60.914939+0.002373j
[2025-09-20 10:59:16] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -60.904237+0.003835j
[2025-09-20 10:59:48] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -60.926001+0.000838j
[2025-09-20 11:00:20] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -60.906541+0.006112j
[2025-09-20 11:00:52] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -60.920991+0.004380j
[2025-09-20 11:01:25] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -60.927813-0.000470j
[2025-09-20 11:01:57] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -60.937965-0.002426j
[2025-09-20 11:02:29] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -60.929215-0.002921j
[2025-09-20 11:03:01] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -60.911299+0.001317j
[2025-09-20 11:03:33] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -60.921752+0.000818j
[2025-09-20 11:04:05] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -60.924819+0.002773j
[2025-09-20 11:04:38] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -60.925000-0.002586j
[2025-09-20 11:05:10] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -60.941091+0.001274j
[2025-09-20 11:05:42] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -60.918482-0.001411j
[2025-09-20 11:06:14] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -60.906900-0.000061j
[2025-09-20 11:06:46] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -60.908259+0.004502j
[2025-09-20 11:07:19] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -60.908167-0.000860j
[2025-09-20 11:07:51] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -60.926553+0.000319j
[2025-09-20 11:08:23] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -60.936027+0.001950j
[2025-09-20 11:08:55] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -60.923808-0.001943j
[2025-09-20 11:09:27] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -60.924287-0.001226j
[2025-09-20 11:10:00] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -60.921638-0.002386j
[2025-09-20 11:10:32] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -60.922825+0.001194j
[2025-09-20 11:11:04] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -60.904942-0.001583j
[2025-09-20 11:11:36] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -60.905511+0.002785j
[2025-09-20 11:12:09] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -60.914302+0.000376j
[2025-09-20 11:12:41] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -60.891620+0.000292j
[2025-09-20 11:13:13] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -60.904046-0.002211j
[2025-09-20 11:13:45] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -60.911588+0.001176j
[2025-09-20 11:14:17] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -60.918638-0.000027j
[2025-09-20 11:14:49] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -60.901808-0.003673j
[2025-09-20 11:15:21] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -60.923003+0.000924j
[2025-09-20 11:15:54] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -60.912017+0.001771j
[2025-09-20 11:16:26] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -60.910617+0.004404j
[2025-09-20 11:16:58] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -60.918165-0.000006j
[2025-09-20 11:17:30] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -60.917580+0.000584j
[2025-09-20 11:18:02] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -60.909175-0.005444j
[2025-09-20 11:18:34] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -60.929286-0.007519j
[2025-09-20 11:19:07] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -60.910621+0.003149j
[2025-09-20 11:19:39] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -60.916959-0.002589j
[2025-09-20 11:20:11] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -60.915881-0.001285j
[2025-09-20 11:20:43] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -60.921475-0.001019j
[2025-09-20 11:21:15] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -60.917637+0.004278j
[2025-09-20 11:21:47] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -60.897772+0.000316j
[2025-09-20 11:22:19] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -60.925522+0.000152j
[2025-09-20 11:22:52] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -60.915399-0.003502j
[2025-09-20 11:23:24] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -60.903849-0.001887j
[2025-09-20 11:23:56] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -60.899689+0.003810j
[2025-09-20 11:24:28] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -60.914758+0.004454j
[2025-09-20 11:25:00] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -60.921342+0.000610j
[2025-09-20 11:25:32] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -60.896862-0.002773j
[2025-09-20 11:26:05] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -60.889422-0.000995j
[2025-09-20 11:26:37] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -60.895275+0.003374j
[2025-09-20 11:27:09] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -60.918154-0.000287j
[2025-09-20 11:27:41] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -60.910889+0.000003j
[2025-09-20 11:28:13] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -60.905944-0.000030j
[2025-09-20 11:28:45] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -60.915093-0.003952j
[2025-09-20 11:29:18] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -60.934816-0.003162j
[2025-09-20 11:29:50] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -60.911412+0.002757j
[2025-09-20 11:30:22] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -60.924069+0.005158j
[2025-09-20 11:30:54] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -60.925714-0.000758j
[2025-09-20 11:31:26] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -60.938949-0.004055j
[2025-09-20 11:31:58] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -60.920154+0.000841j
[2025-09-20 11:32:31] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -60.907579-0.003120j
[2025-09-20 11:33:03] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -60.939032+0.004278j
[2025-09-20 11:33:35] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -60.918810+0.000605j
[2025-09-20 11:34:07] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -60.935894+0.001116j
[2025-09-20 11:34:39] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -60.917682+0.001098j
[2025-09-20 11:35:12] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -60.930578+0.001219j
[2025-09-20 11:35:44] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -60.926165+0.000726j
[2025-09-20 11:36:16] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -60.919376+0.003676j
[2025-09-20 11:36:48] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -60.893614+0.000498j
[2025-09-20 11:37:20] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -60.913886+0.000310j
[2025-09-20 11:37:53] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -60.934872+0.001912j
[2025-09-20 11:38:25] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -60.904411+0.002486j
[2025-09-20 11:38:57] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -60.927612+0.003220j
[2025-09-20 11:39:29] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -60.935374+0.000000j
[2025-09-20 11:40:02] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -60.916898-0.002554j
[2025-09-20 11:40:34] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -60.927092+0.001043j
[2025-09-20 11:41:06] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -60.908173+0.001866j
[2025-09-20 11:41:38] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -60.906975-0.000601j
[2025-09-20 11:42:11] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -60.913107+0.000415j
[2025-09-20 11:42:43] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -60.914284-0.001257j
[2025-09-20 11:43:15] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -60.918312-0.000356j
[2025-09-20 11:43:47] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -60.917655+0.003529j
[2025-09-20 11:44:19] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -60.920245-0.000825j
[2025-09-20 11:44:19] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-20 11:44:52] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -60.898790-0.002568j
[2025-09-20 11:45:24] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -60.917956-0.000249j
[2025-09-20 11:45:56] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -60.924229-0.000388j
[2025-09-20 11:46:28] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -60.911543-0.000052j
[2025-09-20 11:47:00] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -60.907183+0.000983j
[2025-09-20 11:47:33] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -60.910470-0.000410j
[2025-09-20 11:48:05] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -60.934090-0.001593j
[2025-09-20 11:48:37] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -60.920494-0.000705j
[2025-09-20 11:49:09] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -60.919984-0.005523j
[2025-09-20 11:49:41] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -60.921150-0.000827j
[2025-09-20 11:50:13] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -60.928211-0.004260j
[2025-09-20 11:50:46] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -60.938549+0.000271j
[2025-09-20 11:51:18] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -60.913650+0.001039j
[2025-09-20 11:51:50] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -60.917885-0.000939j
[2025-09-20 11:52:22] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -60.921605-0.000807j
[2025-09-20 11:52:54] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -60.915354+0.000514j
[2025-09-20 11:53:27] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -60.920920+0.001562j
[2025-09-20 11:53:59] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -60.922959-0.002878j
[2025-09-20 11:54:31] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -60.926165+0.002142j
[2025-09-20 11:55:03] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -60.924640-0.003809j
[2025-09-20 11:55:35] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -60.911605-0.001691j
[2025-09-20 11:56:07] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -60.914080-0.003099j
[2025-09-20 11:56:39] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -60.913281-0.003083j
[2025-09-20 11:57:12] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -60.923798+0.002961j
[2025-09-20 11:57:44] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -60.908778+0.002444j
[2025-09-20 11:58:16] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -60.921907+0.002633j
[2025-09-20 11:58:48] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -60.929214+0.004493j
[2025-09-20 11:59:21] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -60.902513+0.005524j
[2025-09-20 11:59:53] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -60.934642-0.004238j
[2025-09-20 12:00:25] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -60.933697-0.003024j
[2025-09-20 12:00:57] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -60.938745+0.000340j
[2025-09-20 12:01:29] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -60.935174-0.001067j
[2025-09-20 12:02:02] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -60.924482+0.001995j
[2025-09-20 12:02:34] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -60.912011-0.001746j
[2025-09-20 12:03:06] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -60.912354+0.001367j
[2025-09-20 12:03:38] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -60.897898-0.002150j
[2025-09-20 12:04:10] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -60.929684-0.002459j
[2025-09-20 12:04:42] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -60.913387+0.001036j
[2025-09-20 12:05:15] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -60.922339+0.003523j
[2025-09-20 12:05:47] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -60.920164-0.004836j
[2025-09-20 12:06:19] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -60.925797+0.002967j
[2025-09-20 12:06:51] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -60.924530-0.006658j
[2025-09-20 12:07:23] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -60.919452-0.000843j
[2025-09-20 12:07:56] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -60.909074-0.002698j
[2025-09-20 12:08:28] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -60.927575-0.004216j
[2025-09-20 12:09:00] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -60.924896-0.002169j
[2025-09-20 12:09:32] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -60.917693-0.001828j
[2025-09-20 12:10:04] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -60.923257+0.000491j
[2025-09-20 12:10:36] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -60.934530+0.000399j
[2025-09-20 12:11:09] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -60.921798+0.001793j
[2025-09-20 12:11:41] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -60.937106+0.002743j
[2025-09-20 12:12:13] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -60.924476+0.001312j
[2025-09-20 12:12:45] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -60.913549+0.003228j
[2025-09-20 12:13:17] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -60.925043-0.000517j
[2025-09-20 12:13:49] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -60.948931-0.000855j
[2025-09-20 12:14:22] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -60.923215-0.000936j
[2025-09-20 12:14:54] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -60.914893+0.000534j
[2025-09-20 12:15:26] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -60.917238-0.001473j
[2025-09-20 12:15:58] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -60.929834+0.001403j
[2025-09-20 12:16:30] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -60.928178+0.000622j
[2025-09-20 12:17:03] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -60.921566-0.000098j
[2025-09-20 12:17:35] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -60.903705-0.000240j
[2025-09-20 12:18:07] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -60.918206-0.005195j
[2025-09-20 12:18:39] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -60.938918-0.004038j
[2025-09-20 12:19:11] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -60.923377+0.005206j
[2025-09-20 12:19:44] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -60.937846-0.000098j
[2025-09-20 12:20:16] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -60.923566+0.001649j
[2025-09-20 12:20:48] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -60.922702-0.006108j
[2025-09-20 12:21:20] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -60.924650+0.002536j
[2025-09-20 12:21:52] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -60.922326+0.000579j
[2025-09-20 12:22:25] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -60.919677-0.000102j
[2025-09-20 12:22:57] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -60.928238-0.001025j
[2025-09-20 12:23:29] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -60.897402-0.002593j
[2025-09-20 12:24:01] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -60.900420+0.000934j
[2025-09-20 12:24:33] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -60.932564+0.001279j
[2025-09-20 12:25:05] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -60.923800-0.008172j
[2025-09-20 12:25:38] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -60.906813-0.005998j
[2025-09-20 12:26:10] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -60.903356-0.005476j
[2025-09-20 12:26:42] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -60.927318-0.003021j
[2025-09-20 12:27:14] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -60.932906-0.000528j
[2025-09-20 12:27:47] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -60.909494-0.000696j
[2025-09-20 12:28:19] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -60.923694+0.000966j
[2025-09-20 12:28:51] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -60.923404-0.000575j
[2025-09-20 12:29:23] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -60.909736+0.000136j
[2025-09-20 12:29:55] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -60.905928-0.000179j
[2025-09-20 12:30:28] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -60.921760+0.002206j
[2025-09-20 12:31:00] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -60.928735+0.000771j
[2025-09-20 12:31:32] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -60.924395+0.002954j
[2025-09-20 12:32:04] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -60.920845-0.000013j
[2025-09-20 12:32:37] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -60.923182-0.005592j
[2025-09-20 12:33:09] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -60.920177-0.001127j
[2025-09-20 12:33:41] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -60.917927-0.001099j
[2025-09-20 12:34:13] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -60.923768+0.002712j
[2025-09-20 12:34:45] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -60.913335-0.002522j
[2025-09-20 12:35:18] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -60.912207-0.002934j
[2025-09-20 12:35:50] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -60.943170-0.002617j
[2025-09-20 12:36:22] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -60.939530-0.001812j
[2025-09-20 12:36:54] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -60.928997-0.002206j
[2025-09-20 12:37:27] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -60.933365-0.000890j
[2025-09-20 12:37:59] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -60.925336-0.000443j
[2025-09-20 12:38:31] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -60.928050+0.000103j
[2025-09-20 12:39:03] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -60.903551+0.003380j
[2025-09-20 12:39:35] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -60.912153-0.003138j
[2025-09-20 12:40:08] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -60.905643+0.000963j
[2025-09-20 12:40:40] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -60.922222-0.002302j
[2025-09-20 12:40:40] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-20 12:41:12] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -60.917933-0.002038j
[2025-09-20 12:41:44] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -60.921245-0.002191j
[2025-09-20 12:42:16] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -60.910190-0.000742j
[2025-09-20 12:42:49] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -60.917403+0.003913j
[2025-09-20 12:43:21] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -60.916461-0.000264j
[2025-09-20 12:43:53] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -60.908882+0.002315j
[2025-09-20 12:44:25] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -60.903845+0.000033j
[2025-09-20 12:44:58] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -60.938429-0.002418j
[2025-09-20 12:45:30] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -60.915651+0.001648j
[2025-09-20 12:46:02] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -60.937387+0.000445j
[2025-09-20 12:46:34] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -60.918400-0.003542j
[2025-09-20 12:47:06] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -60.928328-0.000064j
[2025-09-20 12:47:39] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -60.927566+0.003677j
[2025-09-20 12:48:11] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -60.920761-0.000379j
[2025-09-20 12:48:43] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -60.920201+0.002117j
[2025-09-20 12:49:15] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -60.919390+0.001107j
[2025-09-20 12:49:47] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -60.934567-0.002164j
[2025-09-20 12:50:20] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -60.902978-0.003698j
[2025-09-20 12:50:52] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -60.913801+0.010919j
[2025-09-20 12:51:24] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -60.915220-0.000545j
[2025-09-20 12:51:56] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -60.935019-0.001100j
[2025-09-20 12:52:29] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -60.915078+0.004632j
[2025-09-20 12:53:01] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -60.933650-0.000745j
[2025-09-20 12:53:33] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -60.928717-0.002122j
[2025-09-20 12:54:05] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -60.922698-0.004001j
[2025-09-20 12:54:37] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -60.930600-0.004231j
[2025-09-20 12:55:10] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -60.916124-0.000034j
[2025-09-20 12:55:42] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -60.914398+0.000147j
[2025-09-20 12:56:14] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -60.926980+0.001837j
[2025-09-20 12:56:46] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -60.921483+0.001961j
[2025-09-20 12:56:46] RESTART #2 | Period: 600
[2025-09-20 12:57:18] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -60.909981-0.000932j
[2025-09-20 12:57:50] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -60.905548+0.000705j
[2025-09-20 12:58:23] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -60.926993-0.000212j
[2025-09-20 12:58:55] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -60.946480-0.002903j
[2025-09-20 12:59:27] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -60.918011+0.001848j
[2025-09-20 12:59:59] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -60.943441-0.006264j
[2025-09-20 13:00:31] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -60.931626-0.003826j
[2025-09-20 13:01:03] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -60.922437+0.000575j
[2025-09-20 13:01:36] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -60.913576+0.000611j
[2025-09-20 13:02:08] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -60.929401-0.000601j
[2025-09-20 13:02:40] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -60.924257+0.000083j
[2025-09-20 13:03:12] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -60.935097+0.000393j
[2025-09-20 13:03:44] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -60.917686-0.001222j
[2025-09-20 13:04:16] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -60.923670-0.000150j
[2025-09-20 13:04:48] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -60.925934+0.001094j
[2025-09-20 13:05:21] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -60.916688-0.004943j
[2025-09-20 13:05:53] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -60.924420-0.004844j
[2025-09-20 13:06:25] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -60.920786+0.001393j
[2025-09-20 13:06:57] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -60.915529+0.000542j
[2025-09-20 13:07:29] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -60.914971-0.000814j
[2025-09-20 13:08:02] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -60.927933-0.003817j
[2025-09-20 13:08:34] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -60.916937+0.002526j
[2025-09-20 13:09:06] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -60.914437-0.001305j
[2025-09-20 13:09:38] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -60.918139-0.001648j
[2025-09-20 13:10:10] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -60.925077-0.000813j
[2025-09-20 13:10:43] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -60.935486-0.001365j
[2025-09-20 13:11:15] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -60.925665-0.000557j
[2025-09-20 13:11:47] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -60.914543-0.002026j
[2025-09-20 13:12:19] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -60.900406+0.002155j
[2025-09-20 13:12:51] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -60.917476+0.001080j
[2025-09-20 13:13:24] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -60.928907-0.002056j
[2025-09-20 13:13:56] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -60.920094-0.000874j
[2025-09-20 13:14:28] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -60.902563-0.000202j
[2025-09-20 13:15:00] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -60.921021+0.001907j
[2025-09-20 13:15:33] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -60.926017-0.000672j
[2025-09-20 13:16:05] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -60.924707-0.001377j
[2025-09-20 13:16:37] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -60.913626+0.000046j
[2025-09-20 13:17:09] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -60.918039-0.002190j
[2025-09-20 13:17:42] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -60.938241+0.004415j
[2025-09-20 13:18:14] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -60.927435-0.001614j
[2025-09-20 13:18:46] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -60.927883+0.001675j
[2025-09-20 13:19:18] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -60.918846-0.003734j
[2025-09-20 13:19:51] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -60.912661+0.019194j
[2025-09-20 13:20:23] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -60.934486+0.001524j
[2025-09-20 13:20:55] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -60.925760-0.000207j
[2025-09-20 13:21:27] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -60.933480+0.000133j
[2025-09-20 13:21:59] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -60.913479+0.002787j
[2025-09-20 13:22:31] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -60.929270+0.000592j
[2025-09-20 13:23:03] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -60.920732-0.000133j
[2025-09-20 13:23:36] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -60.930510-0.000975j
[2025-09-20 13:24:08] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -60.908820-0.000750j
[2025-09-20 13:24:40] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -60.920817+0.000306j
[2025-09-20 13:25:12] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -60.929625-0.001375j
[2025-09-20 13:25:45] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -60.936948+0.001851j
[2025-09-20 13:26:17] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -60.922508+0.001101j
[2025-09-20 13:26:49] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -60.925194+0.002975j
[2025-09-20 13:27:21] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -60.943558-0.001634j
[2025-09-20 13:27:53] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -60.923089+0.002907j
[2025-09-20 13:28:25] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -60.914329-0.002970j
[2025-09-20 13:28:58] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -60.934269-0.002783j
[2025-09-20 13:29:30] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -60.916057+0.002641j
[2025-09-20 13:30:02] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -60.908358+0.002638j
[2025-09-20 13:30:34] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -60.916377+0.002452j
[2025-09-20 13:31:06] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -60.917810-0.001289j
[2025-09-20 13:31:39] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -60.913447-0.003694j
[2025-09-20 13:32:11] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -60.910173-0.000866j
[2025-09-20 13:32:43] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -60.919307+0.005220j
[2025-09-20 13:33:15] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -60.923765+0.000287j
[2025-09-20 13:33:47] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -60.920664-0.003251j
[2025-09-20 13:34:20] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -60.906038-0.004401j
[2025-09-20 13:34:52] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -60.915357-0.000671j
[2025-09-20 13:35:24] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -60.925351+0.000896j
[2025-09-20 13:35:56] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -60.910799+0.001468j
[2025-09-20 13:36:28] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -60.918253+0.001478j
[2025-09-20 13:37:01] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -60.931085+0.005157j
[2025-09-20 13:37:01] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-20 13:37:33] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -60.944603+0.000844j
[2025-09-20 13:38:05] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -60.921580+0.002382j
[2025-09-20 13:38:37] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -60.928784+0.003502j
[2025-09-20 13:39:09] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -60.918275-0.000073j
[2025-09-20 13:39:41] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -60.940667+0.001248j
[2025-09-20 13:40:14] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -60.910192+0.000546j
[2025-09-20 13:40:46] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -60.926336+0.000095j
[2025-09-20 13:41:18] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -60.917372-0.002618j
[2025-09-20 13:41:50] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -60.917184-0.004252j
[2025-09-20 13:42:22] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -60.937762-0.000922j
[2025-09-20 13:42:54] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -60.912567-0.001750j
[2025-09-20 13:43:27] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -60.909590+0.001487j
[2025-09-20 13:43:59] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -60.929735-0.000974j
[2025-09-20 13:44:31] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -60.909041-0.005101j
[2025-09-20 13:45:03] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -60.925169+0.001035j
[2025-09-20 13:45:35] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -60.926058-0.002866j
[2025-09-20 13:46:07] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -60.907064-0.001026j
[2025-09-20 13:46:39] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -60.913733+0.000596j
[2025-09-20 13:47:11] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -60.928891+0.001046j
[2025-09-20 13:47:44] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -60.919153-0.000201j
[2025-09-20 13:48:16] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -60.922612-0.002647j
[2025-09-20 13:48:48] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -60.910658-0.000647j
[2025-09-20 13:49:20] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -60.925748+0.000739j
[2025-09-20 13:49:52] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -60.920363-0.000560j
[2025-09-20 13:50:24] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -60.922445+0.000000j
[2025-09-20 13:50:57] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -60.932635-0.001934j
[2025-09-20 13:51:29] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -60.943953-0.001565j
[2025-09-20 13:52:01] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -60.933139+0.002249j
[2025-09-20 13:52:33] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -60.933012-0.002902j
[2025-09-20 13:53:05] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -60.914900+0.000705j
[2025-09-20 13:53:37] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -60.931138+0.002806j
[2025-09-20 13:54:09] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -60.934161-0.002228j
[2025-09-20 13:54:42] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -60.923484-0.002092j
[2025-09-20 13:55:14] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -60.913281+0.001184j
[2025-09-20 13:55:46] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -60.931115+0.001816j
[2025-09-20 13:56:18] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -60.935125-0.003984j
[2025-09-20 13:56:50] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -60.914225+0.000779j
[2025-09-20 13:57:22] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -60.916789+0.000150j
[2025-09-20 13:57:54] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -60.914499+0.000758j
[2025-09-20 13:58:27] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -60.927173-0.001702j
[2025-09-20 13:58:59] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -60.937579+0.003275j
[2025-09-20 13:59:31] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -60.923954-0.000819j
[2025-09-20 14:00:03] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -60.930172-0.002619j
[2025-09-20 14:00:35] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -60.933683+0.002817j
[2025-09-20 14:01:08] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -60.917308-0.003186j
[2025-09-20 14:01:40] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -60.922370+0.001340j
[2025-09-20 14:02:12] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -60.929350+0.001845j
[2025-09-20 14:02:44] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -60.923236-0.001856j
[2025-09-20 14:03:16] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -60.918312-0.001394j
[2025-09-20 14:03:49] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -60.913359+0.001373j
[2025-09-20 14:04:21] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -60.935962-0.001829j
[2025-09-20 14:04:53] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -60.926663-0.001212j
[2025-09-20 14:05:25] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -60.940334-0.002527j
[2025-09-20 14:05:58] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -60.927140+0.003657j
[2025-09-20 14:06:30] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -60.935241-0.000843j
[2025-09-20 14:07:02] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -60.915440+0.000539j
[2025-09-20 14:07:34] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -60.915057-0.002780j
[2025-09-20 14:08:07] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -60.912395-0.005724j
[2025-09-20 14:08:39] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -60.933405+0.000564j
[2025-09-20 14:09:11] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -60.924108+0.002996j
[2025-09-20 14:09:43] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -60.914039+0.002689j
[2025-09-20 14:10:15] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -60.928169+0.002707j
[2025-09-20 14:10:48] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -60.913689-0.001818j
[2025-09-20 14:11:20] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -60.915853-0.000732j
[2025-09-20 14:11:52] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -60.936657+0.001040j
[2025-09-20 14:12:24] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -60.903939-0.000021j
[2025-09-20 14:12:57] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -60.910144-0.001114j
[2025-09-20 14:13:29] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -60.925609-0.002384j
[2025-09-20 14:14:01] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -60.901116-0.000070j
[2025-09-20 14:14:33] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -60.899725+0.000773j
[2025-09-20 14:15:05] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -60.918553-0.001775j
[2025-09-20 14:15:38] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -60.904187+0.003110j
[2025-09-20 14:16:10] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -60.895182-0.000702j
[2025-09-20 14:16:42] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -60.921705+0.003913j
[2025-09-20 14:17:14] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -60.898248-0.004512j
[2025-09-20 14:17:46] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -60.903964+0.002757j
[2025-09-20 14:18:19] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -60.925045+0.002253j
[2025-09-20 14:18:51] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -60.890472-0.000916j
[2025-09-20 14:19:23] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -60.898948+0.004184j
[2025-09-20 14:19:55] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -60.908475-0.000046j
[2025-09-20 14:20:27] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -60.906385-0.019338j
[2025-09-20 14:20:59] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -60.941127+0.000733j
[2025-09-20 14:21:32] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -60.922896+0.000980j
[2025-09-20 14:22:04] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -60.906066-0.003247j
[2025-09-20 14:22:36] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -60.912056-0.000575j
[2025-09-20 14:23:08] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -60.933832+0.002202j
[2025-09-20 14:23:40] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -60.919821-0.002430j
[2025-09-20 14:24:13] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -60.908502+0.002443j
[2025-09-20 14:24:45] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -60.920770+0.001003j
[2025-09-20 14:25:17] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -60.943544+0.002532j
[2025-09-20 14:25:49] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -60.947484-0.001366j
[2025-09-20 14:26:21] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -60.918195-0.003289j
[2025-09-20 14:26:53] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -60.926037+0.001725j
[2025-09-20 14:27:26] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -60.919747-0.001117j
[2025-09-20 14:27:58] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -60.918735-0.001264j
[2025-09-20 14:28:30] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -60.930034+0.001244j
[2025-09-20 14:29:02] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -60.921513+0.001372j
[2025-09-20 14:29:34] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -60.925043+0.003564j
[2025-09-20 14:30:06] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -60.925876-0.000897j
[2025-09-20 14:30:39] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -60.911912-0.002988j
[2025-09-20 14:31:11] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -60.938724-0.000233j
[2025-09-20 14:31:43] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -60.932019-0.000383j
[2025-09-20 14:32:15] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -60.926502+0.003641j
[2025-09-20 14:32:47] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -60.916931+0.004026j
[2025-09-20 14:33:19] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -60.924223-0.003107j
[2025-09-20 14:33:19] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-20 14:33:52] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -60.924349+0.000878j
[2025-09-20 14:34:24] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -60.907700+0.001524j
[2025-09-20 14:34:56] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -60.925981+0.005565j
[2025-09-20 14:35:28] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -60.934036+0.001531j
[2025-09-20 14:36:00] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -60.904126-0.002140j
[2025-09-20 14:36:33] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -60.928421-0.000399j
[2025-09-20 14:37:05] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -60.936450-0.003180j
[2025-09-20 14:37:37] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -60.947935+0.003238j
[2025-09-20 14:38:09] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -60.929045-0.001596j
[2025-09-20 14:38:41] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -60.934698-0.001033j
[2025-09-20 14:39:13] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -60.918880+0.003636j
[2025-09-20 14:39:45] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -60.932176-0.006389j
[2025-09-20 14:40:18] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -60.921097-0.000007j
[2025-09-20 14:40:50] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -60.930323-0.001698j
[2025-09-20 14:41:22] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -60.924115+0.001081j
[2025-09-20 14:41:54] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -60.922511-0.003002j
[2025-09-20 14:42:26] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -60.912697-0.003867j
[2025-09-20 14:42:58] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -60.904600-0.002483j
[2025-09-20 14:43:31] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -60.906761-0.004555j
[2025-09-20 14:44:03] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -60.921099+0.002158j
[2025-09-20 14:44:35] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -60.913960-0.002820j
[2025-09-20 14:45:07] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -60.913310-0.003160j
[2025-09-20 14:45:39] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -60.930646-0.001366j
[2025-09-20 14:46:11] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -60.927600+0.002430j
[2025-09-20 14:46:44] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -60.923969+0.004388j
[2025-09-20 14:47:16] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -60.926114+0.003394j
[2025-09-20 14:47:48] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -60.914442-0.000317j
[2025-09-20 14:48:20] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -60.932578-0.000881j
[2025-09-20 14:48:52] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -60.921919+0.002637j
[2025-09-20 14:49:24] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -60.918707-0.002216j
[2025-09-20 14:49:57] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -60.892511-0.001093j
[2025-09-20 14:50:29] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -60.918767-0.004261j
[2025-09-20 14:51:01] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -60.940160+0.001559j
[2025-09-20 14:51:33] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -60.927971+0.004012j
[2025-09-20 14:52:06] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -60.903462+0.004214j
[2025-09-20 14:52:38] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -60.923065-0.002181j
[2025-09-20 14:53:10] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -60.924380-0.002276j
[2025-09-20 14:53:42] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -60.920974+0.002118j
[2025-09-20 14:54:15] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -60.922906+0.002416j
[2025-09-20 14:54:47] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -60.926056-0.004201j
[2025-09-20 14:55:19] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -60.923644-0.003053j
[2025-09-20 14:55:51] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -60.924756+0.000078j
[2025-09-20 14:56:24] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -60.922672+0.003114j
[2025-09-20 14:56:56] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -60.920969+0.001125j
[2025-09-20 14:57:28] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -60.925539-0.000010j
[2025-09-20 14:58:00] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -60.924888+0.000764j
[2025-09-20 14:58:32] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -60.903434+0.000153j
[2025-09-20 14:59:05] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -60.923042+0.001318j
[2025-09-20 14:59:37] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -60.931734-0.000827j
[2025-09-20 15:00:09] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -60.929429-0.000106j
[2025-09-20 15:00:41] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -60.925289-0.003098j
[2025-09-20 15:01:13] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -60.911669-0.001907j
[2025-09-20 15:01:46] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -60.899125-0.002115j
[2025-09-20 15:02:18] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -60.951429-0.001532j
[2025-09-20 15:02:50] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -60.931680+0.005991j
[2025-09-20 15:03:22] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -60.910705+0.002411j
[2025-09-20 15:03:54] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -60.920580-0.001583j
[2025-09-20 15:04:26] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -60.913066+0.000126j
[2025-09-20 15:04:59] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -60.923034-0.000652j
[2025-09-20 15:05:31] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -60.917040-0.001867j
[2025-09-20 15:06:03] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -60.920591+0.002952j
[2025-09-20 15:06:35] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -60.922381+0.000991j
[2025-09-20 15:07:08] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -60.921342+0.000266j
[2025-09-20 15:07:40] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -60.931562-0.000126j
[2025-09-20 15:08:12] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -60.935788+0.002569j
[2025-09-20 15:08:44] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -60.937641+0.001068j
[2025-09-20 15:09:16] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -60.955282-0.005075j
[2025-09-20 15:09:49] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -60.915040-0.004215j
[2025-09-20 15:10:21] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -60.899977-0.000547j
[2025-09-20 15:10:53] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -60.928672-0.002646j
[2025-09-20 15:11:25] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -60.906779+0.001227j
[2025-09-20 15:11:57] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -60.930135+0.000203j
[2025-09-20 15:12:29] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -60.906527-0.001483j
[2025-09-20 15:13:02] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -60.926152-0.001382j
[2025-09-20 15:13:34] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -60.911495+0.004027j
[2025-09-20 15:14:06] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -60.919458+0.002052j
[2025-09-20 15:14:38] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -60.910349+0.001836j
[2025-09-20 15:15:10] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -60.908005+0.001689j
[2025-09-20 15:15:43] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -60.902952-0.001697j
[2025-09-20 15:16:15] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -60.920729-0.002420j
[2025-09-20 15:16:47] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -60.920438+0.001683j
[2025-09-20 15:17:19] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -60.922275+0.001804j
[2025-09-20 15:17:51] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -60.903594-0.001809j
[2025-09-20 15:18:23] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -60.921246-0.000939j
[2025-09-20 15:18:56] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -60.924396+0.001226j
[2025-09-20 15:19:28] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -60.914131-0.000415j
[2025-09-20 15:20:00] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -60.904876+0.000509j
[2025-09-20 15:20:32] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -60.928483-0.000793j
[2025-09-20 15:21:04] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -60.941385+0.002021j
[2025-09-20 15:21:36] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -60.943030+0.002567j
[2025-09-20 15:22:09] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -60.931466-0.002901j
[2025-09-20 15:22:41] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -60.922423+0.003055j
[2025-09-20 15:23:13] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -60.935006-0.001152j
[2025-09-20 15:23:45] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -60.926310-0.002658j
[2025-09-20 15:24:17] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -60.915556+0.001969j
[2025-09-20 15:24:49] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -60.913826-0.000670j
[2025-09-20 15:25:21] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -60.919172-0.002259j
[2025-09-20 15:25:54] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -60.924682-0.000045j
[2025-09-20 15:26:26] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -60.926385+0.003206j
[2025-09-20 15:26:58] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -60.917275-0.002302j
[2025-09-20 15:27:30] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -60.907426+0.000035j
[2025-09-20 15:28:02] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -60.922783-0.002120j
[2025-09-20 15:28:34] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -60.943913-0.001866j
[2025-09-20 15:29:06] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -60.920106-0.000636j
[2025-09-20 15:29:39] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -60.925773-0.001941j
[2025-09-20 15:29:39] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-20 15:30:11] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -60.915534+0.000705j
[2025-09-20 15:30:43] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -60.922183+0.002283j
[2025-09-20 15:31:15] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -60.935594+0.003854j
[2025-09-20 15:31:47] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -60.921968+0.000061j
[2025-09-20 15:32:20] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -60.927209-0.000479j
[2025-09-20 15:32:52] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -60.937720+0.001864j
[2025-09-20 15:33:24] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -60.922935+0.001157j
[2025-09-20 15:33:56] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -60.923391-0.001051j
[2025-09-20 15:34:28] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -60.920727-0.000112j
[2025-09-20 15:35:00] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -60.916505+0.001472j
[2025-09-20 15:35:32] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -60.937925+0.000586j
[2025-09-20 15:36:05] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -60.913093-0.000890j
[2025-09-20 15:36:37] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -60.928155-0.001216j
[2025-09-20 15:37:09] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -60.908936-0.000193j
[2025-09-20 15:37:41] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -60.931048+0.000263j
[2025-09-20 15:38:13] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -60.921213+0.002039j
[2025-09-20 15:38:46] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -60.922002-0.002318j
[2025-09-20 15:39:18] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -60.919964-0.001333j
[2025-09-20 15:39:50] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -60.933556-0.004929j
[2025-09-20 15:40:22] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -60.925788+0.000542j
[2025-09-20 15:40:54] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -60.905258+0.000250j
[2025-09-20 15:41:27] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -60.916044+0.000355j
[2025-09-20 15:41:59] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -60.928223+0.000250j
[2025-09-20 15:42:31] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -60.920259+0.000295j
[2025-09-20 15:43:03] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -60.929452+0.002069j
[2025-09-20 15:43:35] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -60.914012-0.001338j
[2025-09-20 15:44:08] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -60.926235+0.002438j
[2025-09-20 15:44:40] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -60.915216+0.002791j
[2025-09-20 15:45:12] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -60.926697-0.000941j
[2025-09-20 15:45:44] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -60.918024+0.005957j
[2025-09-20 15:46:17] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -60.942134+0.000261j
[2025-09-20 15:46:49] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -60.931360+0.000894j
[2025-09-20 15:47:21] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -60.920989-0.001505j
[2025-09-20 15:47:53] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -60.914065+0.002013j
[2025-09-20 15:48:25] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -60.927179-0.001289j
[2025-09-20 15:48:58] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -60.919258+0.000291j
[2025-09-20 15:49:30] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -60.919964+0.000499j
[2025-09-20 15:50:02] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -60.916047+0.000004j
[2025-09-20 15:50:35] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -60.918964+0.000465j
[2025-09-20 15:51:07] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -60.917238+0.001341j
[2025-09-20 15:51:39] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -60.916000+0.000929j
[2025-09-20 15:52:11] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -60.935868-0.004559j
[2025-09-20 15:52:43] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -60.940250-0.004345j
[2025-09-20 15:53:16] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -60.933445+0.002125j
[2025-09-20 15:53:48] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -60.915817+0.000765j
[2025-09-20 15:54:20] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -60.919088-0.000199j
[2025-09-20 15:54:52] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -60.945405+0.002819j
[2025-09-20 15:55:24] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -60.925664+0.004443j
[2025-09-20 15:55:57] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -60.938758+0.000877j
[2025-09-20 15:56:29] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -60.924206-0.000839j
[2025-09-20 15:57:01] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -60.922596-0.003230j
[2025-09-20 15:57:33] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -60.926917+0.002416j
[2025-09-20 15:58:05] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -60.949078-0.002087j
[2025-09-20 15:58:38] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -60.917436-0.002465j
[2025-09-20 15:59:10] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -60.912608-0.003876j
[2025-09-20 15:59:42] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -60.925733-0.002170j
[2025-09-20 16:00:14] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -60.925019+0.000515j
[2025-09-20 16:00:46] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -60.917536+0.000814j
[2025-09-20 16:01:18] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -60.924147+0.000085j
[2025-09-20 16:01:51] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -60.930054+0.000664j
[2025-09-20 16:02:23] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -60.935163-0.002777j
[2025-09-20 16:02:55] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -60.913117-0.001155j
[2025-09-20 16:03:27] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -60.926502-0.000316j
[2025-09-20 16:03:59] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -60.915729-0.001033j
[2025-09-20 16:04:32] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -60.909162+0.005532j
[2025-09-20 16:05:04] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -60.918570+0.000425j
[2025-09-20 16:05:36] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -60.937884-0.001857j
[2025-09-20 16:06:08] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -60.934046-0.003451j
[2025-09-20 16:06:40] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -60.931116+0.001058j
[2025-09-20 16:07:13] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -60.937201-0.002555j
[2025-09-20 16:07:45] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -60.916528+0.002572j
[2025-09-20 16:08:17] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -60.925450+0.000405j
[2025-09-20 16:08:49] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -60.923750-0.000196j
[2025-09-20 16:09:21] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -60.922424-0.001802j
[2025-09-20 16:09:54] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -60.934804+0.002848j
[2025-09-20 16:10:26] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -60.926812-0.002714j
[2025-09-20 16:10:58] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -60.930666+0.008782j
[2025-09-20 16:11:30] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -60.933782+0.006061j
[2025-09-20 16:12:02] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -60.917815+0.002205j
[2025-09-20 16:12:35] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -60.928227-0.002076j
[2025-09-20 16:13:07] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -60.917118+0.000685j
[2025-09-20 16:13:39] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -60.923651+0.001430j
[2025-09-20 16:14:11] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -60.933620-0.001056j
[2025-09-20 16:14:43] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -60.909650-0.007717j
[2025-09-20 16:15:15] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -60.913251+0.000663j
[2025-09-20 16:15:47] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -60.925749+0.001714j
[2025-09-20 16:16:20] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -60.908771-0.003741j
[2025-09-20 16:16:52] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -60.931882+0.004027j
[2025-09-20 16:17:24] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -60.925897+0.001227j
[2025-09-20 16:17:56] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -60.933932+0.000451j
[2025-09-20 16:18:28] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -60.921509+0.002186j
[2025-09-20 16:19:00] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -60.920404-0.001731j
[2025-09-20 16:19:33] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -60.923277+0.000602j
[2025-09-20 16:20:05] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -60.918716-0.002449j
[2025-09-20 16:20:37] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -60.935992+0.000925j
[2025-09-20 16:21:09] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -60.917397-0.008938j
[2025-09-20 16:21:41] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -60.918887-0.000747j
[2025-09-20 16:22:14] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -60.910330-0.004388j
[2025-09-20 16:22:46] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -60.921578+0.004075j
[2025-09-20 16:23:18] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -60.926732+0.002018j
[2025-09-20 16:23:50] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -60.928345-0.003948j
[2025-09-20 16:24:22] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -60.933805+0.002460j
[2025-09-20 16:24:54] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -60.934930-0.001093j
[2025-09-20 16:25:26] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -60.914698+0.002952j
[2025-09-20 16:25:59] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -60.928565-0.002839j
[2025-09-20 16:25:59] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-20 16:26:31] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -60.940803-0.007440j
[2025-09-20 16:27:03] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -60.924792-0.002852j
[2025-09-20 16:27:35] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -60.925729-0.001136j
[2025-09-20 16:28:07] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -60.906205+0.002726j
[2025-09-20 16:28:40] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -60.917444+0.003548j
[2025-09-20 16:29:12] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -60.912953-0.021449j
[2025-09-20 16:29:44] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -60.913839+0.001521j
[2025-09-20 16:30:16] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -60.919127-0.001430j
[2025-09-20 16:30:48] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -60.923520-0.002158j
[2025-09-20 16:31:21] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -60.925760-0.001329j
[2025-09-20 16:31:53] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -60.908727+0.000368j
[2025-09-20 16:32:25] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -60.922599+0.000742j
[2025-09-20 16:32:57] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -60.900522-0.000613j
[2025-09-20 16:33:29] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -60.926094-0.001473j
[2025-09-20 16:34:02] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -60.935994+0.000529j
[2025-09-20 16:34:34] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -60.920108+0.001700j
[2025-09-20 16:35:06] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -60.900529-0.001618j
[2025-09-20 16:35:38] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -60.926282-0.000041j
[2025-09-20 16:36:11] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -60.925068-0.000277j
[2025-09-20 16:36:43] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -60.922064+0.001375j
[2025-09-20 16:37:15] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -60.924068-0.000057j
[2025-09-20 16:37:47] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -60.940051-0.002046j
[2025-09-20 16:38:19] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -60.928125+0.001801j
[2025-09-20 16:38:52] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -60.940675+0.002656j
[2025-09-20 16:39:24] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -60.919963-0.000610j
[2025-09-20 16:39:56] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -60.931852+0.003020j
[2025-09-20 16:40:28] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -60.921041-0.000015j
[2025-09-20 16:41:00] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -60.923520+0.000170j
[2025-09-20 16:41:33] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -60.931988+0.004461j
[2025-09-20 16:42:05] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -60.906930-0.002035j
[2025-09-20 16:42:37] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -60.915550-0.004808j
[2025-09-20 16:43:09] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -60.946134+0.003955j
[2025-09-20 16:43:42] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -60.940168+0.000407j
[2025-09-20 16:44:14] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -60.914359+0.002379j
[2025-09-20 16:44:46] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -60.918615-0.001950j
[2025-09-20 16:45:18] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -60.934199+0.001105j
[2025-09-20 16:45:50] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -60.910832-0.003658j
[2025-09-20 16:46:23] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -60.906062+0.001735j
[2025-09-20 16:46:55] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -60.939507+0.001692j
[2025-09-20 16:47:27] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -60.923711+0.002346j
[2025-09-20 16:47:59] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -60.903552-0.000318j
[2025-09-20 16:48:31] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -60.926338+0.005841j
[2025-09-20 16:49:03] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -60.923476-0.003419j
[2025-09-20 16:49:35] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -60.929861+0.000336j
[2025-09-20 16:50:08] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -60.907722+0.002833j
[2025-09-20 16:50:40] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -60.910058+0.000455j
[2025-09-20 16:51:12] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -60.920352+0.000228j
[2025-09-20 16:51:45] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -60.916699+0.001009j
[2025-09-20 16:52:17] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -60.927549+0.003038j
[2025-09-20 16:52:49] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -60.918072-0.003035j
[2025-09-20 16:53:21] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -60.918723-0.002195j
[2025-09-20 16:53:53] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -60.914674-0.002605j
[2025-09-20 16:54:25] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -60.931569+0.000486j
[2025-09-20 16:54:58] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -60.952490+0.000095j
[2025-09-20 16:55:30] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -60.928796-0.001087j
[2025-09-20 16:56:02] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -60.912135+0.000767j
[2025-09-20 16:56:34] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -60.921500+0.000149j
[2025-09-20 16:57:06] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -60.911058-0.003140j
[2025-09-20 16:57:39] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -60.935528-0.002309j
[2025-09-20 16:58:11] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -60.926614+0.005555j
[2025-09-20 16:58:43] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -60.917032+0.000917j
[2025-09-20 16:59:15] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -60.921158+0.001776j
[2025-09-20 16:59:47] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -60.927027+0.001370j
[2025-09-20 17:00:19] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -60.911635+0.001250j
[2025-09-20 17:00:51] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -60.926749-0.002725j
[2025-09-20 17:01:23] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -60.930941+0.000276j
[2025-09-20 17:01:55] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -60.900544+0.001879j
[2025-09-20 17:02:28] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -60.937200+0.001797j
[2025-09-20 17:03:00] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -60.918055+0.000188j
[2025-09-20 17:03:32] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -60.914399-0.005429j
[2025-09-20 17:04:04] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -60.927979-0.000125j
[2025-09-20 17:04:36] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -60.926970+0.002167j
[2025-09-20 17:05:09] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -60.917867+0.007478j
[2025-09-20 17:05:41] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -60.905572-0.003933j
[2025-09-20 17:06:13] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -60.920122-0.001149j
[2025-09-20 17:06:45] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -60.926970-0.002451j
[2025-09-20 17:07:17] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -60.930486+0.001604j
[2025-09-20 17:07:49] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -60.936603+0.000616j
[2025-09-20 17:08:22] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -60.933336-0.002037j
[2025-09-20 17:08:54] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -60.920954+0.000954j
[2025-09-20 17:09:26] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -60.931343-0.001755j
[2025-09-20 17:09:58] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -60.923599-0.001722j
[2025-09-20 17:10:30] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -60.917827+0.000922j
[2025-09-20 17:11:02] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -60.965518+0.000085j
[2025-09-20 17:11:35] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -60.915457+0.001561j
[2025-09-20 17:12:07] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -60.917884+0.000511j
[2025-09-20 17:12:39] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -60.919454-0.002917j
[2025-09-20 17:13:11] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -60.931501+0.001867j
[2025-09-20 17:13:43] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -60.932379+0.000615j
[2025-09-20 17:14:15] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -60.920047+0.002165j
[2025-09-20 17:14:48] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -60.924019+0.001177j
[2025-09-20 17:15:20] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -60.928622-0.001405j
[2025-09-20 17:15:52] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -60.926013+0.002434j
[2025-09-20 17:16:24] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -60.904970-0.014592j
[2025-09-20 17:16:57] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -60.937619+0.000803j
[2025-09-20 17:17:29] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -60.937632-0.000195j
[2025-09-20 17:18:02] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -60.926700-0.002839j
[2025-09-20 17:18:34] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -60.922567+0.000368j
[2025-09-20 17:19:06] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -60.926018-0.000668j
[2025-09-20 17:19:38] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -60.924990+0.006107j
[2025-09-20 17:20:10] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -60.938193-0.004162j
[2025-09-20 17:20:42] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -60.935698-0.000564j
[2025-09-20 17:21:15] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -60.933405-0.003793j
[2025-09-20 17:21:47] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -60.939618-0.005442j
[2025-09-20 17:22:19] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -60.914049-0.002255j
[2025-09-20 17:22:19] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-20 17:22:51] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -60.928042-0.001922j
[2025-09-20 17:23:24] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -60.940765-0.003420j
[2025-09-20 17:23:56] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -60.930364+0.002923j
[2025-09-20 17:24:28] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -60.937667-0.002485j
[2025-09-20 17:25:00] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -60.953807+0.001688j
[2025-09-20 17:25:33] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -60.917081-0.003445j
[2025-09-20 17:26:05] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -60.931517+0.003703j
[2025-09-20 17:26:37] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -60.925006-0.001229j
[2025-09-20 17:27:09] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -60.928633-0.000884j
[2025-09-20 17:27:42] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -60.928837-0.001399j
[2025-09-20 17:28:14] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -60.931670+0.003721j
[2025-09-20 17:28:46] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -60.930029+0.002309j
[2025-09-20 17:29:18] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -60.919464+0.002172j
[2025-09-20 17:29:50] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -60.909039+0.002122j
[2025-09-20 17:30:23] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -60.920254-0.001734j
[2025-09-20 17:30:55] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -60.939572-0.005074j
[2025-09-20 17:31:27] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -60.943366-0.003115j
[2025-09-20 17:31:59] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -60.928845-0.000728j
[2025-09-20 17:32:32] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -60.911761-0.001169j
[2025-09-20 17:33:04] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -60.897851+0.001658j
[2025-09-20 17:33:36] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -60.917344+0.000958j
[2025-09-20 17:34:08] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -60.908533+0.000111j
[2025-09-20 17:34:40] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -60.947835-0.002779j
[2025-09-20 17:35:13] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -60.924180+0.000960j
[2025-09-20 17:35:45] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -60.928765-0.000256j
[2025-09-20 17:36:17] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -60.935163+0.000633j
[2025-09-20 17:36:49] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -60.925762-0.001057j
[2025-09-20 17:37:21] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -60.929577-0.004212j
[2025-09-20 17:37:54] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -60.937275-0.002388j
[2025-09-20 17:38:26] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -60.942306+0.001865j
[2025-09-20 17:38:58] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -60.925113+0.003060j
[2025-09-20 17:39:30] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -60.933749-0.002950j
[2025-09-20 17:40:02] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -60.935484+0.001966j
[2025-09-20 17:40:35] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -60.936422+0.003948j
[2025-09-20 17:41:07] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -60.931990+0.001190j
[2025-09-20 17:41:39] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -60.927009-0.002145j
[2025-09-20 17:42:11] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -60.935815+0.010928j
[2025-09-20 17:42:43] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -60.941674-0.003889j
[2025-09-20 17:43:16] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -60.925040+0.000779j
[2025-09-20 17:43:48] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -60.927185-0.007326j
[2025-09-20 17:44:20] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -60.915829-0.001233j
[2025-09-20 17:44:52] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -60.909492+0.000014j
[2025-09-20 17:45:24] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -60.925765+0.000962j
[2025-09-20 17:45:57] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -60.938646-0.001061j
[2025-09-20 17:46:29] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -60.912070+0.000539j
[2025-09-20 17:47:01] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -60.938955-0.002889j
[2025-09-20 17:47:33] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -60.920128-0.001763j
[2025-09-20 17:48:05] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -60.931189-0.001663j
[2025-09-20 17:48:37] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -60.917036+0.001509j
[2025-09-20 17:49:09] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -60.926613-0.005587j
[2025-09-20 17:49:42] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -60.938068-0.002607j
[2025-09-20 17:50:14] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -60.923218-0.005268j
[2025-09-20 17:50:46] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -60.916898-0.000245j
[2025-09-20 17:51:18] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -60.916947+0.000075j
[2025-09-20 17:51:50] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -60.912856+0.000325j
[2025-09-20 17:52:22] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -60.913054+0.005652j
[2025-09-20 17:52:54] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -60.930187-0.000784j
[2025-09-20 17:53:27] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -60.931105+0.003519j
[2025-09-20 17:53:59] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -60.942174-0.000139j
[2025-09-20 17:54:31] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -60.926238+0.000804j
[2025-09-20 17:55:03] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -60.901890+0.001255j
[2025-09-20 17:55:35] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -60.912016-0.000755j
[2025-09-20 17:56:07] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -60.929516+0.000081j
[2025-09-20 17:56:40] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -60.948284+0.004634j
[2025-09-20 17:57:12] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -60.925716-0.001166j
[2025-09-20 17:57:44] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -60.919612+0.000608j
[2025-09-20 17:58:16] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -60.924734+0.000982j
[2025-09-20 17:58:48] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -60.944418-0.001556j
[2025-09-20 17:59:21] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -60.908568-0.003585j
[2025-09-20 17:59:53] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -60.926224-0.001006j
[2025-09-20 18:00:25] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -60.916526-0.002161j
[2025-09-20 18:00:57] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -60.928777-0.001561j
[2025-09-20 18:01:29] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -60.924943+0.001267j
[2025-09-20 18:02:01] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -60.931353+0.001412j
[2025-09-20 18:02:33] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -60.947381+0.000961j
[2025-09-20 18:03:06] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -60.907630-0.003459j
[2025-09-20 18:03:38] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -60.926025-0.001061j
[2025-09-20 18:04:10] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -60.937877-0.001959j
[2025-09-20 18:04:42] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -60.913943-0.003808j
[2025-09-20 18:05:14] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -60.937951+0.000823j
[2025-09-20 18:05:46] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -60.932616-0.002239j
[2025-09-20 18:06:19] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -60.928038-0.001646j
[2025-09-20 18:06:51] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -60.918737-0.001329j
[2025-09-20 18:07:23] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -60.922246+0.000091j
[2025-09-20 18:07:55] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -60.925868+0.001512j
[2025-09-20 18:08:27] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -60.917946+0.002280j
[2025-09-20 18:09:00] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -60.922454-0.002024j
[2025-09-20 18:09:32] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -60.921486-0.000352j
[2025-09-20 18:10:04] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -60.898101-0.000076j
[2025-09-20 18:10:36] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -60.924976+0.005299j
[2025-09-20 18:11:09] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -60.934413-0.002323j
[2025-09-20 18:11:41] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -60.915444-0.000724j
[2025-09-20 18:12:13] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -60.923961+0.001109j
[2025-09-20 18:12:45] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -60.915018-0.004591j
[2025-09-20 18:13:17] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -60.915075+0.000378j
[2025-09-20 18:13:50] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -60.922698-0.001133j
[2025-09-20 18:14:22] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -60.914301+0.003166j
[2025-09-20 18:14:54] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -60.939832-0.001195j
[2025-09-20 18:15:26] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -60.928111-0.004290j
[2025-09-20 18:15:56] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -60.930197-0.000731j
[2025-09-20 18:16:10] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -60.928897-0.000746j
[2025-09-20 18:16:25] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -60.934174-0.000730j
[2025-09-20 18:16:39] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -60.914234+0.001974j
[2025-09-20 18:16:53] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -60.914880-0.000657j
[2025-09-20 18:17:08] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -60.946242-0.000380j
[2025-09-20 18:17:08] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-20 18:17:08] ✅ Training completed | Restarts: 2
[2025-09-20 18:17:08] ============================================================
[2025-09-20 18:17:08] Training completed | Runtime: 33791.7s
[2025-09-20 18:17:13] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-20 18:17:13] ============================================================
