[2025-09-20 08:53:38] ✓ 从checkpoint恢复: results/L=6/J2=1.00/J1=0.83/training/checkpoints/final_GCNN.pkl
[2025-09-20 08:53:38]   - 迭代次数: final
[2025-09-20 08:53:38]   - 能量: -67.346026-0.000072j ± 0.008775
[2025-09-20 08:53:38]   - 时间戳: 2025-09-20T08:53:04.978524+08:00
[2025-09-20 08:53:59] ✓ 变分状态参数已从checkpoint恢复
[2025-09-20 08:53:59] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-20 08:53:59] ==================================================
[2025-09-20 08:53:59] GCNN for Shastry-Sutherland Model
[2025-09-20 08:53:59] ==================================================
[2025-09-20 08:53:59] System parameters:
[2025-09-20 08:53:59]   - System size: L=6, N=144
[2025-09-20 08:53:59]   - System parameters: J1=0.84, J2=1.0, Q=0.0
[2025-09-20 08:53:59] --------------------------------------------------
[2025-09-20 08:53:59] Model parameters:
[2025-09-20 08:53:59]   - Number of layers = 4
[2025-09-20 08:53:59]   - Number of features = 4
[2025-09-20 08:53:59]   - Total parameters = 28252
[2025-09-20 08:54:00] --------------------------------------------------
[2025-09-20 08:54:00] Training parameters:
[2025-09-20 08:54:00]   - Learning rate: 0.015
[2025-09-20 08:54:00]   - Total iterations: 1050
[2025-09-20 08:54:00]   - Annealing cycles: 3
[2025-09-20 08:54:00]   - Initial period: 150
[2025-09-20 08:54:00]   - Period multiplier: 2.0
[2025-09-20 08:54:00]   - Temperature range: 0.0-1.0
[2025-09-20 08:54:00]   - Samples: 4096
[2025-09-20 08:54:00]   - Discarded samples: 0
[2025-09-20 08:54:00]   - Chunk size: 2048
[2025-09-20 08:54:00]   - Diagonal shift: 0.2
[2025-09-20 08:54:00]   - Gradient clipping: 1.0
[2025-09-20 08:54:00]   - Checkpoint enabled: interval=105
[2025-09-20 08:54:00]   - Checkpoint directory: results/L=6/J2=1.00/J1=0.84/training/checkpoints
[2025-09-20 08:54:00] --------------------------------------------------
[2025-09-20 08:54:00] Device status:
[2025-09-20 08:54:00]   - Devices model: NVIDIA H200 NVL
[2025-09-20 08:54:00]   - Number of devices: 1
[2025-09-20 08:54:00]   - Sharding: True
[2025-09-20 08:54:00] ============================================================
[2025-09-20 08:55:32] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -64.795971+0.030165j
[2025-09-20 08:56:38] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -66.145800+0.011881j
[2025-09-20 08:57:10] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -67.430523-0.010460j
[2025-09-20 08:57:41] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -68.071601+0.004323j
[2025-09-20 08:58:13] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -68.222276+0.003527j
[2025-09-20 08:58:45] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -68.261152+0.000687j
[2025-09-20 08:59:17] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -68.275963-0.000266j
[2025-09-20 08:59:49] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -68.269753-0.002457j
[2025-09-20 09:00:21] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -68.267491+0.003113j
[2025-09-20 09:00:53] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -68.268788+0.000158j
[2025-09-20 09:01:25] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -68.269038-0.001049j
[2025-09-20 09:01:57] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -68.280575+0.001944j
[2025-09-20 09:02:29] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -68.283766+0.000416j
[2025-09-20 09:03:01] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -68.266566-0.002397j
[2025-09-20 09:03:33] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -68.281894+0.000051j
[2025-09-20 09:04:05] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -68.274908+0.001510j
[2025-09-20 09:04:37] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -68.278513-0.000486j
[2025-09-20 09:05:09] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -68.282008+0.002492j
[2025-09-20 09:05:41] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -68.262289+0.006406j
[2025-09-20 09:06:13] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -68.271373-0.000546j
[2025-09-20 09:06:45] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -68.291031-0.002113j
[2025-09-20 09:07:17] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -68.287425-0.000269j
[2025-09-20 09:07:49] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -68.287971-0.002284j
[2025-09-20 09:08:21] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -68.288899+0.000362j
[2025-09-20 09:08:53] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -68.274759-0.000653j
[2025-09-20 09:09:25] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -68.277233+0.000785j
[2025-09-20 09:09:57] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -68.266858+0.002820j
[2025-09-20 09:10:29] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -68.287117+0.001301j
[2025-09-20 09:11:01] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -68.278290-0.003342j
[2025-09-20 09:11:33] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -68.293037+0.003437j
[2025-09-20 09:12:05] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -68.273238+0.003133j
[2025-09-20 09:12:37] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -68.273295+0.000296j
[2025-09-20 09:13:09] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -68.280891+0.020821j
[2025-09-20 09:13:41] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -68.269461+0.000137j
[2025-09-20 09:14:13] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -68.286103-0.004025j
[2025-09-20 09:14:45] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -68.271355+0.002783j
[2025-09-20 09:15:17] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -68.295501+0.001383j
[2025-09-20 09:15:49] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -68.260806+0.001007j
[2025-09-20 09:16:21] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -68.274944+0.001832j
[2025-09-20 09:16:53] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -68.261366-0.000988j
[2025-09-20 09:17:25] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -68.261126-0.002076j
[2025-09-20 09:17:57] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -68.273072-0.000800j
[2025-09-20 09:18:29] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -68.277424-0.000441j
[2025-09-20 09:19:01] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -68.283298-0.003024j
[2025-09-20 09:19:33] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -68.269594+0.002853j
[2025-09-20 09:20:05] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -68.284985+0.001163j
[2025-09-20 09:20:37] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -68.277098-0.001850j
[2025-09-20 09:21:09] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -68.278111+0.003208j
[2025-09-20 09:21:41] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -68.290859+0.000764j
[2025-09-20 09:22:13] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -68.270965-0.002990j
[2025-09-20 09:22:45] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -68.293308-0.003597j
[2025-09-20 09:23:17] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -68.277082-0.001959j
[2025-09-20 09:23:49] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -68.280414-0.003251j
[2025-09-20 09:24:21] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -68.272458+0.003680j
[2025-09-20 09:24:53] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -68.272870-0.002350j
[2025-09-20 09:25:25] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -68.275195+0.000729j
[2025-09-20 09:25:56] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -68.288488+0.001518j
[2025-09-20 09:26:29] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -68.303434+0.001451j
[2025-09-20 09:27:01] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -68.274570+0.000169j
[2025-09-20 09:27:32] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -68.271957+0.000980j
[2025-09-20 09:28:04] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -68.277516+0.001199j
[2025-09-20 09:28:36] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -68.290901-0.000223j
[2025-09-20 09:29:08] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -68.280524-0.000844j
[2025-09-20 09:29:40] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -68.257598+0.001286j
[2025-09-20 09:30:12] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -68.283459+0.001464j
[2025-09-20 09:30:44] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -68.279172-0.000763j
[2025-09-20 09:31:16] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -68.301683-0.000478j
[2025-09-20 09:31:48] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -68.290783+0.000767j
[2025-09-20 09:32:20] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -68.268863-0.002310j
[2025-09-20 09:32:52] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -68.276698+0.004416j
[2025-09-20 09:33:24] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -68.278163+0.002008j
[2025-09-20 09:33:56] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -68.291008-0.001557j
[2025-09-20 09:34:28] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -68.287398-0.002077j
[2025-09-20 09:35:00] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -68.286165+0.002615j
[2025-09-20 09:35:32] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -68.282456+0.000318j
[2025-09-20 09:36:04] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -68.274214+0.001913j
[2025-09-20 09:36:36] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -68.284666-0.000512j
[2025-09-20 09:37:08] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -68.295744-0.000655j
[2025-09-20 09:37:40] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -68.269375-0.004338j
[2025-09-20 09:38:12] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -68.263283-0.000443j
[2025-09-20 09:38:44] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -68.279070+0.000307j
[2025-09-20 09:39:16] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -68.260971-0.000504j
[2025-09-20 09:39:48] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -68.282973-0.001576j
[2025-09-20 09:40:20] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -68.279862+0.001999j
[2025-09-20 09:40:52] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -68.267793-0.000726j
[2025-09-20 09:41:24] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -68.276484-0.001321j
[2025-09-20 09:41:56] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -68.276033+0.002640j
[2025-09-20 09:42:28] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -68.286122+0.003459j
[2025-09-20 09:43:00] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -68.295980-0.000517j
[2025-09-20 09:43:32] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -68.261730+0.001963j
[2025-09-20 09:44:04] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -68.296713+0.002332j
[2025-09-20 09:44:36] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -68.282175+0.000055j
[2025-09-20 09:45:08] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -68.279236+0.002798j
[2025-09-20 09:45:40] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -68.283807+0.002757j
[2025-09-20 09:46:12] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -68.295157+0.001501j
[2025-09-20 09:46:44] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -68.272198-0.001419j
[2025-09-20 09:47:16] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -68.264532+0.001465j
[2025-09-20 09:47:48] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -68.276000-0.001489j
[2025-09-20 09:48:20] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -68.265504+0.003818j
[2025-09-20 09:48:52] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -68.268291-0.005783j
[2025-09-20 09:49:24] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -68.282866-0.001037j
[2025-09-20 09:49:56] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -68.253654-0.002675j
[2025-09-20 09:50:28] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -68.285629-0.001195j
[2025-09-20 09:51:00] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -68.280943+0.002522j
[2025-09-20 09:51:32] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -68.273825+0.000606j
[2025-09-20 09:51:32] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-20 09:52:04] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -68.280014+0.003105j
[2025-09-20 09:52:36] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -68.297860+0.000362j
[2025-09-20 09:53:08] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -68.280625+0.002367j
[2025-09-20 09:53:40] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -68.264128-0.000062j
[2025-09-20 09:54:12] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -68.256372-0.000365j
[2025-09-20 09:54:44] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -68.280760+0.002547j
[2025-09-20 09:55:16] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -68.282399+0.000268j
[2025-09-20 09:55:48] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -68.280618+0.000911j
[2025-09-20 09:56:20] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -68.261761+0.001992j
[2025-09-20 09:56:52] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -68.267168-0.002723j
[2025-09-20 09:57:24] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -68.269290+0.001168j
[2025-09-20 09:57:56] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -68.281285+0.001029j
[2025-09-20 09:58:28] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -68.281987+0.000670j
[2025-09-20 09:59:00] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -68.279236+0.000590j
[2025-09-20 09:59:32] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -68.261397+0.000698j
[2025-09-20 10:00:04] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -68.275583-0.002166j
[2025-09-20 10:00:36] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -68.268508+0.001855j
[2025-09-20 10:01:08] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -68.274346+0.003688j
[2025-09-20 10:01:40] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -68.282280+0.001786j
[2025-09-20 10:02:12] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -68.273610-0.001900j
[2025-09-20 10:02:44] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -68.260722+0.001662j
[2025-09-20 10:03:16] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -68.273323-0.000686j
[2025-09-20 10:03:48] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -68.268691+0.000480j
[2025-09-20 10:04:20] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -68.282673+0.002019j
[2025-09-20 10:04:52] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -68.282610+0.003387j
[2025-09-20 10:05:24] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -68.270394-0.001157j
[2025-09-20 10:05:56] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -68.270340+0.000056j
[2025-09-20 10:06:28] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -68.263847-0.000231j
[2025-09-20 10:06:59] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -68.285751-0.000077j
[2025-09-20 10:07:31] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -68.269823-0.001518j
[2025-09-20 10:08:03] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -68.279293-0.000584j
[2025-09-20 10:08:35] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -68.278403+0.002079j
[2025-09-20 10:09:07] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -68.277572-0.000796j
[2025-09-20 10:09:39] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -68.265937+0.000930j
[2025-09-20 10:10:11] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -68.268782-0.000345j
[2025-09-20 10:10:43] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -68.282152+0.001042j
[2025-09-20 10:11:15] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -68.274245+0.004043j
[2025-09-20 10:11:47] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -68.286914-0.000835j
[2025-09-20 10:12:19] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -68.279918+0.000045j
[2025-09-20 10:12:51] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -68.282470-0.003081j
[2025-09-20 10:13:23] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -68.280644-0.000870j
[2025-09-20 10:13:55] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -68.284866-0.004628j
[2025-09-20 10:14:27] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -68.284628+0.000322j
[2025-09-20 10:14:59] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -68.276870+0.001371j
[2025-09-20 10:15:31] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -68.281563+0.001840j
[2025-09-20 10:15:31] RESTART #1 | Period: 300
[2025-09-20 10:16:04] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -68.291414-0.001103j
[2025-09-20 10:16:36] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -68.285559-0.001684j
[2025-09-20 10:17:08] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -68.284501+0.002371j
[2025-09-20 10:17:40] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -68.274712-0.000800j
[2025-09-20 10:18:12] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -68.272600-0.001083j
[2025-09-20 10:18:44] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -68.269076+0.002975j
[2025-09-20 10:19:16] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -68.279494+0.002043j
[2025-09-20 10:19:48] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -68.264496+0.006650j
[2025-09-20 10:20:20] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -68.281532+0.000273j
[2025-09-20 10:20:52] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -68.259291-0.001979j
[2025-09-20 10:21:24] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -68.289890+0.008214j
[2025-09-20 10:21:56] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -68.275935-0.001222j
[2025-09-20 10:22:28] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -68.285260-0.000292j
[2025-09-20 10:23:00] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -68.278090-0.002144j
[2025-09-20 10:23:32] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -68.278698+0.003646j
[2025-09-20 10:24:04] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -68.261707-0.002856j
[2025-09-20 10:24:36] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -68.275137+0.001124j
[2025-09-20 10:25:08] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -68.258269-0.001959j
[2025-09-20 10:25:40] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -68.253422+0.000217j
[2025-09-20 10:26:12] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -68.261413+0.007694j
[2025-09-20 10:26:44] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -68.261376+0.000163j
[2025-09-20 10:27:16] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -68.289828-0.000885j
[2025-09-20 10:27:48] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -68.290763+0.004012j
[2025-09-20 10:28:20] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -68.273119-0.003782j
[2025-09-20 10:28:52] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -68.277707-0.000346j
[2025-09-20 10:29:24] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -68.285197+0.001538j
[2025-09-20 10:29:56] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -68.269740-0.000090j
[2025-09-20 10:30:28] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -68.289137+0.002055j
[2025-09-20 10:31:00] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -68.269491-0.001996j
[2025-09-20 10:31:32] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -68.278929-0.001374j
[2025-09-20 10:32:04] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -68.255219-0.000769j
[2025-09-20 10:32:36] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -68.272218-0.003641j
[2025-09-20 10:33:08] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -68.283050-0.000172j
[2025-09-20 10:33:40] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -68.280703+0.000125j
[2025-09-20 10:34:12] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -68.262168+0.002300j
[2025-09-20 10:34:44] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -68.286241+0.000131j
[2025-09-20 10:35:16] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -68.272683+0.001257j
[2025-09-20 10:35:48] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -68.278451-0.001070j
[2025-09-20 10:36:20] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -68.271579+0.000013j
[2025-09-20 10:36:51] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -68.272007+0.001134j
[2025-09-20 10:37:24] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -68.264172+0.000760j
[2025-09-20 10:37:55] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -68.276557+0.000950j
[2025-09-20 10:38:27] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -68.273049+0.000662j
[2025-09-20 10:38:59] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -68.272983+0.000051j
[2025-09-20 10:39:31] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -68.279501-0.000743j
[2025-09-20 10:40:03] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -68.276481+0.001345j
[2025-09-20 10:40:35] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -68.276417-0.000317j
[2025-09-20 10:41:07] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -68.273764+0.001041j
[2025-09-20 10:41:39] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -68.274289-0.001113j
[2025-09-20 10:42:11] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -68.277393-0.002008j
[2025-09-20 10:42:43] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -68.263329+0.003071j
[2025-09-20 10:43:15] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -68.269378-0.001791j
[2025-09-20 10:43:47] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -68.282508-0.004889j
[2025-09-20 10:44:19] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -68.266323-0.000376j
[2025-09-20 10:44:51] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -68.298907-0.001853j
[2025-09-20 10:45:23] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -68.294368-0.000025j
[2025-09-20 10:45:55] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -68.272275+0.000212j
[2025-09-20 10:46:27] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -68.282999-0.000038j
[2025-09-20 10:46:59] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -68.271449+0.002779j
[2025-09-20 10:47:31] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -68.294124-0.003183j
[2025-09-20 10:47:31] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-20 10:48:03] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -68.273564-0.000175j
[2025-09-20 10:48:35] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -68.252831-0.001913j
[2025-09-20 10:49:07] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -68.267156-0.001304j
[2025-09-20 10:49:39] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -68.264949-0.001280j
[2025-09-20 10:50:11] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -68.263546+0.001024j
[2025-09-20 10:50:43] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -68.288830+0.000686j
[2025-09-20 10:51:15] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -68.284798-0.001694j
[2025-09-20 10:51:46] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -68.270591-0.002139j
[2025-09-20 10:52:18] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -68.280984+0.000601j
[2025-09-20 10:52:50] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -68.288267+0.001852j
[2025-09-20 10:53:22] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -68.261750-0.001098j
[2025-09-20 10:53:54] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -68.272002-0.001800j
[2025-09-20 10:54:26] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -68.255259-0.001345j
[2025-09-20 10:54:58] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -68.262246-0.000852j
[2025-09-20 10:55:30] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -68.285453+0.001849j
[2025-09-20 10:56:02] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -68.274596-0.001730j
[2025-09-20 10:56:34] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -68.273473-0.000494j
[2025-09-20 10:57:06] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -68.290684+0.000190j
[2025-09-20 10:57:38] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -68.280513-0.002606j
[2025-09-20 10:58:10] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -68.283266+0.001007j
[2025-09-20 10:58:43] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -68.274058-0.001260j
[2025-09-20 10:59:15] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -68.261536-0.002983j
[2025-09-20 10:59:47] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -68.285211-0.000979j
[2025-09-20 11:00:19] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -68.272496+0.001895j
[2025-09-20 11:00:51] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -68.262165-0.000781j
[2025-09-20 11:01:23] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -68.276758+0.000092j
[2025-09-20 11:01:55] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -68.279573-0.001167j
[2025-09-20 11:02:27] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -68.271498+0.000611j
[2025-09-20 11:02:59] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -68.287218+0.002887j
[2025-09-20 11:03:31] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -68.273672+0.002204j
[2025-09-20 11:04:03] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -68.268801+0.004166j
[2025-09-20 11:04:35] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -68.286618-0.000327j
[2025-09-20 11:05:07] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -68.278505+0.001219j
[2025-09-20 11:05:39] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -68.279051+0.000839j
[2025-09-20 11:06:11] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -68.270973+0.000553j
[2025-09-20 11:06:43] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -68.281384-0.000994j
[2025-09-20 11:07:15] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -68.267344+0.001419j
[2025-09-20 11:07:47] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -68.283232+0.000358j
[2025-09-20 11:08:19] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -68.278503+0.002696j
[2025-09-20 11:08:51] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -68.290690-0.000487j
[2025-09-20 11:09:23] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -68.289824-0.000175j
[2025-09-20 11:09:55] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -68.279618-0.000952j
[2025-09-20 11:10:27] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -68.297042-0.000434j
[2025-09-20 11:10:59] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -68.273639+0.001570j
[2025-09-20 11:11:31] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -68.287423+0.001344j
[2025-09-20 11:12:03] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -68.280802+0.000037j
[2025-09-20 11:12:35] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -68.279126-0.003322j
[2025-09-20 11:13:07] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -68.283876+0.004828j
[2025-09-20 11:13:39] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -68.274808+0.000373j
[2025-09-20 11:14:11] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -68.268991-0.001380j
[2025-09-20 11:14:43] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -68.273823-0.001322j
[2025-09-20 11:15:15] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -68.264598+0.000472j
[2025-09-20 11:15:47] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -68.273831-0.000018j
[2025-09-20 11:16:19] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -68.278097-0.005340j
[2025-09-20 11:16:51] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -68.275009-0.000513j
[2025-09-20 11:17:23] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -68.280350+0.001377j
[2025-09-20 11:17:55] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -68.269363+0.002456j
[2025-09-20 11:18:27] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -68.292577+0.000768j
[2025-09-20 11:18:59] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -68.282856+0.001767j
[2025-09-20 11:19:31] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -68.292034-0.000983j
[2025-09-20 11:20:03] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -68.282905-0.010282j
[2025-09-20 11:20:35] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -68.268476+0.001017j
[2025-09-20 11:21:07] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -68.283054+0.003348j
[2025-09-20 11:21:39] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -68.279307-0.001484j
[2025-09-20 11:22:11] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -68.274765+0.003503j
[2025-09-20 11:22:43] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -68.286648+0.000415j
[2025-09-20 11:23:15] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -68.273726-0.001023j
[2025-09-20 11:23:47] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -68.276335+0.000318j
[2025-09-20 11:24:19] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -68.278021+0.000302j
[2025-09-20 11:24:51] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -68.286260+0.001992j
[2025-09-20 11:25:23] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -68.274599-0.000702j
[2025-09-20 11:25:55] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -68.272830+0.000443j
[2025-09-20 11:26:27] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -68.288467-0.001406j
[2025-09-20 11:26:59] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -68.286307+0.001695j
[2025-09-20 11:27:31] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -68.272628-0.000221j
[2025-09-20 11:28:03] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -68.268822-0.002883j
[2025-09-20 11:28:35] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -68.268074-0.000202j
[2025-09-20 11:29:07] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -68.270813+0.002996j
[2025-09-20 11:29:39] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -68.288798+0.004951j
[2025-09-20 11:30:11] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -68.267225-0.002488j
[2025-09-20 11:30:44] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -68.269246-0.002870j
[2025-09-20 11:31:16] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -68.265875+0.000829j
[2025-09-20 11:31:48] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -68.262739-0.001148j
[2025-09-20 11:32:20] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -68.273966+0.000432j
[2025-09-20 11:32:52] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -68.277676+0.001737j
[2025-09-20 11:33:24] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -68.279813+0.000516j
[2025-09-20 11:33:56] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -68.268125+0.000299j
[2025-09-20 11:34:28] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -68.274796+0.000870j
[2025-09-20 11:35:00] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -68.280225-0.001937j
[2025-09-20 11:35:32] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -68.295664+0.000069j
[2025-09-20 11:36:04] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -68.270565-0.000293j
[2025-09-20 11:36:36] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -68.283010-0.000390j
[2025-09-20 11:37:08] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -68.267448-0.000973j
[2025-09-20 11:37:40] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -68.268378-0.001971j
[2025-09-20 11:38:12] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -68.280308+0.000077j
[2025-09-20 11:38:44] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -68.280985+0.002804j
[2025-09-20 11:39:16] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -68.264341+0.001274j
[2025-09-20 11:39:48] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -68.280728-0.002358j
[2025-09-20 11:40:20] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -68.281746-0.000391j
[2025-09-20 11:40:52] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -68.281868-0.000919j
[2025-09-20 11:41:24] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -68.272837+0.001704j
[2025-09-20 11:41:56] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -68.285885+0.002133j
[2025-09-20 11:42:27] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -68.283915+0.002804j
[2025-09-20 11:42:59] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -68.283627+0.001318j
[2025-09-20 11:43:31] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -68.279534+0.000068j
[2025-09-20 11:43:32] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-20 11:44:04] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -68.279951+0.001720j
[2025-09-20 11:44:36] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -68.265028+0.001338j
[2025-09-20 11:45:07] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -68.254214-0.001458j
[2025-09-20 11:45:40] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -68.290181+0.000725j
[2025-09-20 11:46:12] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -68.282079+0.003976j
[2025-09-20 11:46:44] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -68.280718+0.004010j
[2025-09-20 11:47:16] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -68.263530+0.004198j
[2025-09-20 11:47:48] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -68.261911+0.001287j
[2025-09-20 11:48:20] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -68.283698+0.002688j
[2025-09-20 11:48:52] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -68.287447-0.000991j
[2025-09-20 11:49:24] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -68.281705-0.000692j
[2025-09-20 11:49:56] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -68.270128-0.001761j
[2025-09-20 11:50:28] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -68.279387-0.003368j
[2025-09-20 11:51:00] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -68.271696-0.000904j
[2025-09-20 11:51:32] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -68.278388-0.003723j
[2025-09-20 11:52:04] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -68.272603-0.002605j
[2025-09-20 11:52:36] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -68.280154-0.002962j
[2025-09-20 11:53:08] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -68.275657+0.000348j
[2025-09-20 11:53:40] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -68.288433+0.005250j
[2025-09-20 11:54:12] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -68.285631-0.002521j
[2025-09-20 11:54:44] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -68.278611+0.001620j
[2025-09-20 11:55:16] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -68.282272+0.000303j
[2025-09-20 11:55:48] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -68.266554-0.000067j
[2025-09-20 11:56:20] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -68.279838-0.003881j
[2025-09-20 11:56:52] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -68.263258+0.001103j
[2025-09-20 11:57:24] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -68.274419+0.000067j
[2025-09-20 11:57:56] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -68.257042+0.000006j
[2025-09-20 11:58:28] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -68.274639-0.000054j
[2025-09-20 11:59:00] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -68.275635-0.000728j
[2025-09-20 11:59:32] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -68.266483+0.000957j
[2025-09-20 12:00:04] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -68.271018+0.000290j
[2025-09-20 12:00:36] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -68.270903+0.000388j
[2025-09-20 12:01:08] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -68.279656+0.002415j
[2025-09-20 12:01:40] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -68.287339+0.002864j
[2025-09-20 12:02:12] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -68.267718+0.001839j
[2025-09-20 12:02:44] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -68.278952-0.001345j
[2025-09-20 12:03:16] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -68.286818+0.000694j
[2025-09-20 12:03:48] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -68.287923+0.005265j
[2025-09-20 12:04:21] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -68.259159-0.000844j
[2025-09-20 12:04:53] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -68.269576-0.000149j
[2025-09-20 12:05:25] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -68.267009-0.021541j
[2025-09-20 12:05:57] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -68.263085+0.001485j
[2025-09-20 12:06:29] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -68.285072-0.001036j
[2025-09-20 12:07:01] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -68.289012+0.000249j
[2025-09-20 12:07:33] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -68.298453+0.000660j
[2025-09-20 12:08:05] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -68.272692+0.000815j
[2025-09-20 12:08:37] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -68.275376+0.002293j
[2025-09-20 12:09:09] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -68.265623+0.001644j
[2025-09-20 12:09:41] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -68.282806-0.002004j
[2025-09-20 12:10:13] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -68.278917+0.000840j
[2025-09-20 12:10:45] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -68.268909-0.002353j
[2025-09-20 12:11:17] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -68.279636-0.001279j
[2025-09-20 12:11:49] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -68.285685-0.003175j
[2025-09-20 12:12:21] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -68.259239-0.001972j
[2025-09-20 12:12:53] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -68.282585+0.000187j
[2025-09-20 12:13:25] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -68.292653-0.001643j
[2025-09-20 12:13:57] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -68.275576-0.001411j
[2025-09-20 12:14:29] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -68.283393+0.000735j
[2025-09-20 12:15:01] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -68.252425+0.001433j
[2025-09-20 12:15:33] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -68.272858+0.000927j
[2025-09-20 12:16:05] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -68.274669-0.001608j
[2025-09-20 12:16:37] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -68.282520+0.001667j
[2025-09-20 12:17:09] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -68.291153+0.003194j
[2025-09-20 12:17:41] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -68.287540+0.000340j
[2025-09-20 12:18:13] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -68.269919+0.000439j
[2025-09-20 12:18:45] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -68.290842+0.001468j
[2025-09-20 12:19:17] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -68.281500-0.000371j
[2025-09-20 12:19:49] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -68.280825+0.000948j
[2025-09-20 12:20:21] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -68.275524-0.002780j
[2025-09-20 12:20:53] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -68.270304+0.000519j
[2025-09-20 12:21:25] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -68.294899-0.000506j
[2025-09-20 12:21:57] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -68.276066-0.002663j
[2025-09-20 12:22:29] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -68.281334+0.001761j
[2025-09-20 12:23:01] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -68.287608+0.001079j
[2025-09-20 12:23:33] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -68.270983-0.001232j
[2025-09-20 12:24:05] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -68.267715-0.001030j
[2025-09-20 12:24:37] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -68.270310-0.003815j
[2025-09-20 12:25:09] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -68.290371+0.001943j
[2025-09-20 12:25:41] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -68.278451-0.000069j
[2025-09-20 12:26:13] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -68.283899-0.002974j
[2025-09-20 12:26:45] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -68.293898-0.003723j
[2025-09-20 12:27:17] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -68.284908+0.001285j
[2025-09-20 12:27:49] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -68.274643+0.000307j
[2025-09-20 12:28:21] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -68.271053-0.001529j
[2025-09-20 12:28:53] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -68.271756+0.001315j
[2025-09-20 12:29:25] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -68.268800+0.000349j
[2025-09-20 12:29:57] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -68.273406-0.003375j
[2025-09-20 12:30:29] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -68.280314-0.001279j
[2025-09-20 12:31:01] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -68.274478+0.000055j
[2025-09-20 12:31:33] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -68.290269+0.001058j
[2025-09-20 12:32:05] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -68.292007+0.000901j
[2025-09-20 12:32:37] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -68.283823-0.000537j
[2025-09-20 12:33:09] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -68.277440+0.003600j
[2025-09-20 12:33:41] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -68.290635-0.001308j
[2025-09-20 12:34:13] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -68.276641-0.006494j
[2025-09-20 12:34:45] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -68.269141+0.000295j
[2025-09-20 12:35:17] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -68.278053+0.000917j
[2025-09-20 12:35:49] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -68.287687+0.001493j
[2025-09-20 12:36:21] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -68.280764-0.000395j
[2025-09-20 12:36:53] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -68.271909-0.000792j
[2025-09-20 12:37:25] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -68.281052-0.000868j
[2025-09-20 12:37:57] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -68.274945-0.001012j
[2025-09-20 12:38:29] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -68.266788-0.000133j
[2025-09-20 12:39:01] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -68.271082+0.001384j
[2025-09-20 12:39:33] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -68.273660+0.000516j
[2025-09-20 12:39:33] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-20 12:40:05] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -68.275074-0.001273j
[2025-09-20 12:40:37] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -68.273756-0.001110j
[2025-09-20 12:41:09] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -68.262545+0.001641j
[2025-09-20 12:41:41] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -68.288377-0.001226j
[2025-09-20 12:42:13] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -68.280310+0.000939j
[2025-09-20 12:42:45] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -68.271064-0.002155j
[2025-09-20 12:43:17] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -68.283823-0.000108j
[2025-09-20 12:43:49] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -68.277716-0.001253j
[2025-09-20 12:44:21] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -68.280793-0.001174j
[2025-09-20 12:44:53] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -68.291039+0.002402j
[2025-09-20 12:45:25] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -68.280774+0.000743j
[2025-09-20 12:45:57] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -68.289200-0.000170j
[2025-09-20 12:46:29] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -68.280423-0.000246j
[2025-09-20 12:47:01] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -68.270257-0.000314j
[2025-09-20 12:47:33] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -68.279960-0.000673j
[2025-09-20 12:48:05] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -68.259929-0.003860j
[2025-09-20 12:48:37] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -68.275659-0.003685j
[2025-09-20 12:49:09] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -68.270383-0.002009j
[2025-09-20 12:49:41] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -68.267961-0.000069j
[2025-09-20 12:50:13] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -68.274965+0.001987j
[2025-09-20 12:50:45] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -68.279316+0.002698j
[2025-09-20 12:51:18] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -68.278739-0.000427j
[2025-09-20 12:51:50] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -68.287007+0.000418j
[2025-09-20 12:52:22] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -68.278434-0.000293j
[2025-09-20 12:52:54] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -68.267799+0.000189j
[2025-09-20 12:53:26] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -68.277404-0.000293j
[2025-09-20 12:53:58] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -68.266234+0.000846j
[2025-09-20 12:54:30] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -68.263442-0.001395j
[2025-09-20 12:55:02] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -68.266112-0.001744j
[2025-09-20 12:55:34] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -68.286265+0.002055j
[2025-09-20 12:55:34] RESTART #2 | Period: 600
[2025-09-20 12:56:06] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -68.282889-0.001318j
[2025-09-20 12:56:38] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -68.268780+0.001399j
[2025-09-20 12:57:10] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -68.281424-0.001823j
[2025-09-20 12:57:42] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -68.275485+0.001516j
[2025-09-20 12:58:14] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -68.275764-0.001909j
[2025-09-20 12:58:46] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -68.291843+0.003284j
[2025-09-20 12:59:18] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -68.272600-0.000959j
[2025-09-20 12:59:50] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -68.289846+0.000138j
[2025-09-20 13:00:22] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -68.269389+0.000779j
[2025-09-20 13:00:54] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -68.278382+0.001463j
[2025-09-20 13:01:26] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -68.277858-0.003813j
[2025-09-20 13:01:58] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -68.292931-0.008147j
[2025-09-20 13:02:30] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -68.255792-0.003336j
[2025-09-20 13:03:02] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -68.278208+0.000674j
[2025-09-20 13:03:34] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -68.268024-0.001205j
[2025-09-20 13:04:06] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -68.272559-0.000446j
[2025-09-20 13:04:38] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -68.263714-0.001527j
[2025-09-20 13:05:10] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -68.271571-0.000295j
[2025-09-20 13:05:42] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -68.276841-0.000225j
[2025-09-20 13:06:14] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -68.277891+0.003309j
[2025-09-20 13:06:46] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -68.270821-0.001073j
[2025-09-20 13:07:18] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -68.273414-0.002201j
[2025-09-20 13:07:50] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -68.272245+0.001700j
[2025-09-20 13:08:22] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -68.286880-0.001713j
[2025-09-20 13:08:54] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -68.278980-0.000933j
[2025-09-20 13:09:26] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -68.273343+0.000897j
[2025-09-20 13:09:58] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -68.274475+0.002416j
[2025-09-20 13:10:30] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -68.280154-0.000191j
[2025-09-20 13:11:02] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -68.284511+0.002438j
[2025-09-20 13:11:34] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -68.265046-0.001154j
[2025-09-20 13:12:06] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -68.269279+0.001521j
[2025-09-20 13:12:38] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -68.279792-0.001788j
[2025-09-20 13:13:10] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -68.290969+0.001306j
[2025-09-20 13:13:42] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -68.274920+0.002274j
[2025-09-20 13:14:14] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -68.271469+0.002892j
[2025-09-20 13:14:46] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -68.273522+0.001568j
[2025-09-20 13:15:18] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -68.269721-0.003109j
[2025-09-20 13:15:50] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -68.291787-0.001131j
[2025-09-20 13:16:22] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -68.274323+0.000081j
[2025-09-20 13:16:54] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -68.280193-0.003163j
[2025-09-20 13:17:26] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -68.259077+0.000905j
[2025-09-20 13:17:58] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -68.270914+0.001971j
[2025-09-20 13:18:30] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -68.267788-0.000394j
[2025-09-20 13:19:02] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -68.276746-0.000321j
[2025-09-20 13:19:34] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -68.280179+0.004209j
[2025-09-20 13:20:06] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -68.277101+0.001456j
[2025-09-20 13:20:38] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -68.278699+0.000188j
[2025-09-20 13:21:10] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -68.275839+0.003482j
[2025-09-20 13:21:43] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -68.270200+0.000703j
[2025-09-20 13:22:15] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -68.274767-0.000856j
[2025-09-20 13:22:47] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -68.278114-0.004225j
[2025-09-20 13:23:19] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -68.264446-0.001795j
[2025-09-20 13:23:51] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -68.283724+0.002133j
[2025-09-20 13:24:23] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -68.282592-0.003178j
[2025-09-20 13:24:55] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -68.274300+0.000307j
[2025-09-20 13:25:27] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -68.280801+0.000513j
[2025-09-20 13:25:59] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -68.244154+0.001129j
[2025-09-20 13:26:31] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -68.273881-0.001172j
[2025-09-20 13:27:03] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -68.283588+0.002629j
[2025-09-20 13:27:35] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -68.276214-0.001588j
[2025-09-20 13:28:07] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -68.292044-0.001749j
[2025-09-20 13:28:39] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -68.284530+0.015625j
[2025-09-20 13:29:11] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -68.283355-0.002470j
[2025-09-20 13:29:43] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -68.276626-0.001120j
[2025-09-20 13:30:15] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -68.282801-0.007133j
[2025-09-20 13:30:47] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -68.283966-0.002832j
[2025-09-20 13:31:19] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -68.277929+0.000238j
[2025-09-20 13:31:51] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -68.272979-0.000004j
[2025-09-20 13:32:23] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -68.285829-0.000018j
[2025-09-20 13:32:55] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -68.296572+0.000392j
[2025-09-20 13:33:27] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -68.273758-0.001310j
[2025-09-20 13:33:59] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -68.284657-0.002010j
[2025-09-20 13:34:31] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -68.279214-0.002251j
[2025-09-20 13:35:03] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -68.265999+0.001735j
[2025-09-20 13:35:35] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -68.275124-0.002786j
[2025-09-20 13:35:35] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-20 13:36:07] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -68.296373+0.003927j
[2025-09-20 13:36:39] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -68.289250+0.000503j
[2025-09-20 13:37:11] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -68.275745+0.000954j
[2025-09-20 13:37:43] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -68.257566-0.002809j
[2025-09-20 13:38:15] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -68.268009+0.003521j
[2025-09-20 13:38:47] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -68.290676+0.000325j
[2025-09-20 13:39:19] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -68.287295+0.001484j
[2025-09-20 13:39:51] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -68.279880-0.002187j
[2025-09-20 13:40:23] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -68.285140+0.001874j
[2025-09-20 13:40:55] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -68.288526+0.001211j
[2025-09-20 13:41:27] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -68.270211-0.000317j
[2025-09-20 13:41:59] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -68.272076-0.004128j
[2025-09-20 13:42:31] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -68.265605+0.002572j
[2025-09-20 13:43:03] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -68.269660-0.001481j
[2025-09-20 13:43:35] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -68.272349+0.002706j
[2025-09-20 13:44:07] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -68.285289+0.005091j
[2025-09-20 13:44:39] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -68.279722-0.000759j
[2025-09-20 13:45:11] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -68.268309+0.000694j
[2025-09-20 13:45:43] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -68.277613+0.002018j
[2025-09-20 13:46:16] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -68.272299+0.000669j
[2025-09-20 13:46:48] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -68.273604+0.001535j
[2025-09-20 13:47:20] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -68.259072+0.002776j
[2025-09-20 13:47:52] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -68.266769-0.001190j
[2025-09-20 13:48:24] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -68.267132-0.003149j
[2025-09-20 13:48:56] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -68.292565-0.002623j
[2025-09-20 13:49:28] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -68.274355-0.002141j
[2025-09-20 13:50:00] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -68.267691-0.001641j
[2025-09-20 13:50:32] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -68.263204-0.000201j
[2025-09-20 13:51:04] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -68.272693+0.000647j
[2025-09-20 13:51:36] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -68.277985-0.001010j
[2025-09-20 13:52:08] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -68.278123+0.000430j
[2025-09-20 13:52:40] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -68.283539+0.002685j
[2025-09-20 13:53:12] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -68.275892-0.003131j
[2025-09-20 13:53:44] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -68.272448-0.000661j
[2025-09-20 13:54:16] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -68.279114-0.001773j
[2025-09-20 13:54:48] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -68.288821+0.001970j
[2025-09-20 13:55:20] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -68.280546-0.002943j
[2025-09-20 13:55:52] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -68.268596-0.002448j
[2025-09-20 13:56:24] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -68.270621+0.003365j
[2025-09-20 13:56:56] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -68.281207-0.003117j
[2025-09-20 13:57:28] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -68.278645-0.003267j
[2025-09-20 13:58:00] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -68.290286-0.001713j
[2025-09-20 13:58:32] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -68.269603-0.000359j
[2025-09-20 13:59:04] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -68.287495-0.000073j
[2025-09-20 13:59:36] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -68.272640-0.000376j
[2025-09-20 14:00:08] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -68.292545+0.000732j
[2025-09-20 14:00:40] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -68.285486-0.003867j
[2025-09-20 14:01:12] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -68.291816-0.001331j
[2025-09-20 14:01:44] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -68.280011-0.002125j
[2025-09-20 14:02:16] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -68.278058-0.002201j
[2025-09-20 14:02:48] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -68.268967-0.003324j
[2025-09-20 14:03:20] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -68.272452-0.001231j
[2025-09-20 14:03:52] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -68.268267-0.003311j
[2025-09-20 14:04:24] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -68.267376-0.002406j
[2025-09-20 14:04:56] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -68.277102-0.002041j
[2025-09-20 14:05:28] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -68.278102+0.000335j
[2025-09-20 14:06:00] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -68.279470-0.000968j
[2025-09-20 14:06:32] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -68.274224-0.000972j
[2025-09-20 14:07:04] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -68.265829+0.000220j
[2025-09-20 14:07:36] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -68.274829+0.001222j
[2025-09-20 14:08:08] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -68.267870+0.000912j
[2025-09-20 14:08:40] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -68.280842+0.004045j
[2025-09-20 14:09:12] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -68.292644-0.002436j
[2025-09-20 14:09:44] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -68.272402-0.000754j
[2025-09-20 14:10:16] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -68.267836+0.001641j
[2025-09-20 14:10:48] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -68.275097+0.002547j
[2025-09-20 14:11:20] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -68.275330+0.000878j
[2025-09-20 14:11:52] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -68.286159+0.000838j
[2025-09-20 14:12:24] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -68.268686-0.000300j
[2025-09-20 14:12:56] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -68.290908-0.002283j
[2025-09-20 14:13:28] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -68.279685+0.001242j
[2025-09-20 14:14:00] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -68.278801-0.006542j
[2025-09-20 14:14:32] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -68.275219-0.000317j
[2025-09-20 14:15:04] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -68.276195+0.001773j
[2025-09-20 14:15:36] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -68.269030+0.001308j
[2025-09-20 14:16:08] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -68.265367+0.001080j
[2025-09-20 14:16:40] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -68.270365-0.000654j
[2025-09-20 14:17:12] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -68.274077-0.000198j
[2025-09-20 14:17:44] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -68.276040+0.000182j
[2025-09-20 14:18:16] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -68.289704+0.004368j
[2025-09-20 14:18:48] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -68.283671-0.000439j
[2025-09-20 14:19:20] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -68.274465-0.001292j
[2025-09-20 14:19:52] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -68.292247+0.001672j
[2025-09-20 14:20:24] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -68.278023+0.001494j
[2025-09-20 14:20:56] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -68.272761-0.000812j
[2025-09-20 14:21:28] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -68.279581-0.001592j
[2025-09-20 14:22:00] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -68.293385+0.001855j
[2025-09-20 14:22:32] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -68.273419-0.004141j
[2025-09-20 14:23:04] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -68.281235-0.008948j
[2025-09-20 14:23:36] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -68.288262-0.000347j
[2025-09-20 14:24:08] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -68.279249-0.000150j
[2025-09-20 14:24:40] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -68.274919+0.000667j
[2025-09-20 14:25:12] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -68.289252+0.003833j
[2025-09-20 14:25:44] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -68.290030-0.006507j
[2025-09-20 14:26:16] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -68.274906+0.000614j
[2025-09-20 14:26:48] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -68.276670-0.001842j
[2025-09-20 14:27:20] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -68.280383-0.000839j
[2025-09-20 14:27:52] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -68.267381+0.000962j
[2025-09-20 14:28:24] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -68.282487+0.001970j
[2025-09-20 14:28:56] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -68.279144+0.000114j
[2025-09-20 14:29:28] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -68.279993+0.002130j
[2025-09-20 14:30:00] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -68.292460-0.001316j
[2025-09-20 14:30:32] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -68.282568+0.002426j
[2025-09-20 14:31:05] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -68.278825+0.002567j
[2025-09-20 14:31:37] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -68.270591-0.000658j
[2025-09-20 14:31:37] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-20 14:32:09] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -68.287645+0.001910j
[2025-09-20 14:32:41] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -68.277452+0.001737j
[2025-09-20 14:33:13] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -68.284472-0.000742j
[2025-09-20 14:33:45] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -68.303307+0.000154j
[2025-09-20 14:34:17] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -68.283684-0.002961j
[2025-09-20 14:34:49] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -68.280703+0.000513j
[2025-09-20 14:35:21] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -68.264431+0.000377j
[2025-09-20 14:35:53] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -68.272920+0.001263j
[2025-09-20 14:36:25] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -68.275975+0.001312j
[2025-09-20 14:36:57] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -68.272688-0.002200j
[2025-09-20 14:37:29] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -68.268916-0.002632j
[2025-09-20 14:38:01] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -68.270150+0.000545j
[2025-09-20 14:38:33] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -68.284316-0.001982j
[2025-09-20 14:39:05] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -68.272134+0.002685j
[2025-09-20 14:39:37] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -68.279068-0.003598j
[2025-09-20 14:40:09] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -68.281360-0.000051j
[2025-09-20 14:40:41] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -68.280190-0.000235j
[2025-09-20 14:41:13] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -68.284009-0.001001j
[2025-09-20 14:41:45] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -68.295476+0.000023j
[2025-09-20 14:42:17] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -68.275707+0.001271j
[2025-09-20 14:42:49] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -68.285283+0.002644j
[2025-09-20 14:43:21] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -68.281080+0.000628j
[2025-09-20 14:43:53] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -68.281113-0.000256j
[2025-09-20 14:44:25] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -68.274058-0.001608j
[2025-09-20 14:44:57] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -68.281986-0.002055j
[2025-09-20 14:45:29] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -68.274838+0.001586j
[2025-09-20 14:46:01] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -68.295059+0.002677j
[2025-09-20 14:46:33] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -68.284298-0.000332j
[2025-09-20 14:47:05] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -68.279422-0.001258j
[2025-09-20 14:47:37] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -68.275966-0.002330j
[2025-09-20 14:48:09] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -68.287111-0.001324j
[2025-09-20 14:48:41] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -68.276075+0.000155j
[2025-09-20 14:49:13] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -68.276940+0.002457j
[2025-09-20 14:49:45] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -68.290964+0.002427j
[2025-09-20 14:50:17] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -68.291592+0.003180j
[2025-09-20 14:50:49] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -68.268397+0.000668j
[2025-09-20 14:51:21] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -68.271231+0.001710j
[2025-09-20 14:51:53] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -68.274835+0.002927j
[2025-09-20 14:52:25] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -68.282382-0.001074j
[2025-09-20 14:52:57] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -68.291852+0.001246j
[2025-09-20 14:53:29] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -68.269414+0.000318j
[2025-09-20 14:54:01] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -68.269823-0.000999j
[2025-09-20 14:54:33] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -68.263372+0.000154j
[2025-09-20 14:55:05] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -68.282380-0.002026j
[2025-09-20 14:55:37] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -68.270740+0.004079j
[2025-09-20 14:56:09] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -68.276644-0.000800j
[2025-09-20 14:56:41] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -68.273356+0.006733j
[2025-09-20 14:57:13] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -68.284854+0.001284j
[2025-09-20 14:57:45] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -68.286032-0.000486j
[2025-09-20 14:58:17] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -68.287518-0.000716j
[2025-09-20 14:58:49] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -68.278865-0.001140j
[2025-09-20 14:59:21] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -68.267215+0.001135j
[2025-09-20 14:59:53] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -68.284000+0.000524j
[2025-09-20 15:00:25] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -68.294757-0.000154j
[2025-09-20 15:00:57] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -68.278427-0.000949j
[2025-09-20 15:01:29] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -68.279179+0.000209j
[2025-09-20 15:02:01] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -68.273556+0.001234j
[2025-09-20 15:02:33] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -68.285115+0.000788j
[2025-09-20 15:03:05] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -68.285182+0.002788j
[2025-09-20 15:03:37] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -68.283840+0.004215j
[2025-09-20 15:04:09] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -68.281863-0.001304j
[2025-09-20 15:04:41] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -68.277788-0.001968j
[2025-09-20 15:05:13] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -68.274690+0.003213j
[2025-09-20 15:05:45] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -68.268447+0.000575j
[2025-09-20 15:06:17] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -68.279574+0.000356j
[2025-09-20 15:06:49] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -68.291569+0.004548j
[2025-09-20 15:07:21] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -68.267969+0.000165j
[2025-09-20 15:07:53] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -68.282148+0.001404j
[2025-09-20 15:08:25] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -68.281314-0.001322j
[2025-09-20 15:08:58] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -68.283800+0.002009j
[2025-09-20 15:09:30] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -68.266819+0.002500j
[2025-09-20 15:10:02] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -68.276328+0.000601j
[2025-09-20 15:10:34] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -68.282912-0.000891j
[2025-09-20 15:11:06] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -68.279830+0.003435j
[2025-09-20 15:11:38] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -68.290133-0.001846j
[2025-09-20 15:12:10] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -68.284192-0.000317j
[2025-09-20 15:12:42] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -68.257628-0.000101j
[2025-09-20 15:13:14] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -68.265455+0.000359j
[2025-09-20 15:13:46] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -68.286561-0.000177j
[2025-09-20 15:14:18] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -68.268325+0.001908j
[2025-09-20 15:14:50] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -68.274672-0.002534j
[2025-09-20 15:15:22] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -68.285288-0.001732j
[2025-09-20 15:15:54] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -68.278435-0.001270j
[2025-09-20 15:16:26] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -68.275748-0.001674j
[2025-09-20 15:16:58] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -68.290005+0.002337j
[2025-09-20 15:17:30] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -68.281129-0.005500j
[2025-09-20 15:18:02] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -68.273554-0.001593j
[2025-09-20 15:18:34] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -68.275072+0.000948j
[2025-09-20 15:19:06] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -68.273186+0.002585j
[2025-09-20 15:19:38] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -68.297565-0.003018j
[2025-09-20 15:20:10] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -68.276417+0.000338j
[2025-09-20 15:20:42] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -68.265159+0.025096j
[2025-09-20 15:21:14] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -68.289558-0.000627j
[2025-09-20 15:21:46] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -68.277821-0.001258j
[2025-09-20 15:22:18] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -68.280950-0.002373j
[2025-09-20 15:22:50] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -68.265054-0.000722j
[2025-09-20 15:23:22] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -68.279316+0.000126j
[2025-09-20 15:23:54] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -68.283866-0.002106j
[2025-09-20 15:24:26] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -68.281964-0.001301j
[2025-09-20 15:24:58] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -68.266418-0.002908j
[2025-09-20 15:25:30] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -68.285120-0.000164j
[2025-09-20 15:26:02] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -68.270908-0.000636j
[2025-09-20 15:26:34] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -68.284142-0.001749j
[2025-09-20 15:27:06] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -68.283305-0.003030j
[2025-09-20 15:27:38] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -68.287541-0.000904j
[2025-09-20 15:27:38] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-20 15:28:10] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -68.279189+0.000459j
[2025-09-20 15:28:42] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -68.280975+0.001501j
[2025-09-20 15:29:14] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -68.286463-0.000865j
[2025-09-20 15:29:46] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -68.272732-0.001675j
[2025-09-20 15:30:18] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -68.276286-0.000512j
[2025-09-20 15:30:50] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -68.279672+0.000426j
[2025-09-20 15:31:22] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -68.279179-0.000083j
[2025-09-20 15:31:54] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -68.276599-0.001288j
[2025-09-20 15:32:26] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -68.277936-0.002716j
[2025-09-20 15:32:58] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -68.283314+0.005178j
[2025-09-20 15:33:30] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -68.276032+0.000041j
[2025-09-20 15:34:02] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -68.277971+0.002223j
[2025-09-20 15:34:34] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -68.269392+0.000820j
[2025-09-20 15:35:07] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -68.278884-0.000017j
[2025-09-20 15:35:39] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -68.279290-0.000465j
[2025-09-20 15:36:11] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -68.278241-0.000458j
[2025-09-20 15:36:43] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -68.287586-0.001381j
[2025-09-20 15:37:15] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -68.278607-0.000370j
[2025-09-20 15:37:47] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -68.273462-0.000414j
[2025-09-20 15:38:19] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -68.305650-0.002490j
[2025-09-20 15:38:51] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -68.283499-0.001951j
[2025-09-20 15:39:23] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -68.271974+0.002984j
[2025-09-20 15:39:55] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -68.271939-0.001295j
[2025-09-20 15:40:27] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -68.279087+0.001484j
[2025-09-20 15:40:59] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -68.282736+0.001700j
[2025-09-20 15:41:31] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -68.284127-0.000311j
[2025-09-20 15:42:03] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -68.287875+0.004919j
[2025-09-20 15:42:35] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -68.277828+0.000119j
[2025-09-20 15:43:07] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -68.276015+0.007628j
[2025-09-20 15:43:39] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -68.279555-0.002753j
[2025-09-20 15:44:10] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -68.280206+0.002937j
[2025-09-20 15:44:42] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -68.283940+0.001155j
[2025-09-20 15:45:14] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -68.278325+0.003233j
[2025-09-20 15:45:46] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -68.255535-0.000458j
[2025-09-20 15:46:18] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -68.269232-0.000374j
[2025-09-20 15:46:50] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -68.270691+0.000010j
[2025-09-20 15:47:22] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -68.280528-0.004127j
[2025-09-20 15:47:54] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -68.273981+0.003384j
[2025-09-20 15:48:26] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -68.280024-0.000102j
[2025-09-20 15:48:58] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -68.276731+0.003174j
[2025-09-20 15:49:30] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -68.263731-0.001870j
[2025-09-20 15:50:02] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -68.257030+0.001178j
[2025-09-20 15:50:34] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -68.277694+0.001196j
[2025-09-20 15:51:06] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -68.290112-0.002301j
[2025-09-20 15:51:38] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -68.280735+0.002452j
[2025-09-20 15:52:10] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -68.271957-0.004223j
[2025-09-20 15:52:42] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -68.282766+0.002140j
[2025-09-20 15:53:14] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -68.280607-0.002578j
[2025-09-20 15:53:46] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -68.277215-0.001075j
[2025-09-20 15:54:18] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -68.289178-0.002102j
[2025-09-20 15:54:50] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -68.262054+0.000115j
[2025-09-20 15:55:22] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -68.266912+0.000194j
[2025-09-20 15:55:54] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -68.298629-0.001178j
[2025-09-20 15:56:26] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -68.289236+0.002250j
[2025-09-20 15:56:58] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -68.271986-0.009844j
[2025-09-20 15:57:30] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -68.274578+0.000588j
[2025-09-20 15:58:02] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -68.282273-0.000175j
[2025-09-20 15:58:34] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -68.267391+0.000268j
[2025-09-20 15:59:06] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -68.269308+0.000950j
[2025-09-20 15:59:38] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -68.273821-0.000222j
[2025-09-20 16:00:10] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -68.269727+0.000355j
[2025-09-20 16:00:42] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -68.273573-0.000205j
[2025-09-20 16:01:14] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -68.282141+0.001827j
[2025-09-20 16:01:46] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -68.284673-0.000630j
[2025-09-20 16:02:18] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -68.276171+0.001937j
[2025-09-20 16:02:50] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -68.271655+0.002494j
[2025-09-20 16:03:22] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -68.264497+0.000180j
[2025-09-20 16:03:54] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -68.280551-0.002056j
[2025-09-20 16:04:26] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -68.277779-0.001738j
[2025-09-20 16:04:59] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -68.289176-0.001813j
[2025-09-20 16:05:31] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -68.298864+0.000256j
[2025-09-20 16:06:03] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -68.285590+0.000439j
[2025-09-20 16:06:35] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -68.273316-0.001252j
[2025-09-20 16:07:07] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -68.283659+0.003080j
[2025-09-20 16:07:39] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -68.263999+0.000656j
[2025-09-20 16:08:11] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -68.282005+0.002245j
[2025-09-20 16:08:43] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -68.287052+0.000767j
[2025-09-20 16:09:15] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -68.282957+0.000071j
[2025-09-20 16:09:47] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -68.284333-0.001549j
[2025-09-20 16:10:19] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -68.281543+0.000824j
[2025-09-20 16:10:51] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -68.281670-0.001300j
[2025-09-20 16:11:23] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -68.282916+0.000782j
[2025-09-20 16:11:55] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -68.272844+0.000245j
[2025-09-20 16:12:27] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -68.287661-0.001379j
[2025-09-20 16:12:59] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -68.288666+0.003013j
[2025-09-20 16:13:31] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -68.274609-0.001273j
[2025-09-20 16:14:03] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -68.286172-0.000974j
[2025-09-20 16:14:35] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -68.287149+0.001054j
[2025-09-20 16:15:07] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -68.288626+0.000680j
[2025-09-20 16:15:39] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -68.266420+0.002583j
[2025-09-20 16:16:11] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -68.272084+0.002054j
[2025-09-20 16:16:43] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -68.273556+0.000798j
[2025-09-20 16:17:15] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -68.297639+0.001368j
[2025-09-20 16:17:47] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -68.281427+0.001390j
[2025-09-20 16:18:19] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -68.275242-0.001085j
[2025-09-20 16:18:51] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -68.266109-0.003837j
[2025-09-20 16:19:23] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -68.281759+0.000779j
[2025-09-20 16:19:55] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -68.273104+0.000651j
[2025-09-20 16:20:27] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -68.270356+0.001554j
[2025-09-20 16:20:59] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -68.272576-0.001163j
[2025-09-20 16:21:31] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -68.278877+0.001849j
[2025-09-20 16:22:03] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -68.280654+0.002079j
[2025-09-20 16:22:35] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -68.274415+0.000060j
[2025-09-20 16:23:07] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -68.277528+0.001305j
[2025-09-20 16:23:39] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -68.250736-0.001621j
[2025-09-20 16:23:39] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-20 16:24:11] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -68.290352+0.000431j
[2025-09-20 16:24:43] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -68.280164+0.000831j
[2025-09-20 16:25:15] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -68.274218+0.000678j
[2025-09-20 16:25:47] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -68.297663+0.001378j
[2025-09-20 16:26:19] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -68.275906-0.007663j
[2025-09-20 16:26:52] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -68.291872-0.000109j
[2025-09-20 16:27:23] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -68.279130-0.000074j
[2025-09-20 16:27:55] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -68.271957+0.001002j
[2025-09-20 16:28:28] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -68.284679+0.002645j
[2025-09-20 16:29:00] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -68.283952+0.000240j
[2025-09-20 16:29:32] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -68.265854-0.002713j
[2025-09-20 16:30:04] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -68.282510-0.001184j
[2025-09-20 16:30:36] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -68.266573-0.001165j
[2025-09-20 16:31:08] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -68.283139+0.000628j
[2025-09-20 16:31:40] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -68.280287-0.002722j
[2025-09-20 16:32:11] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -68.275532+0.002924j
[2025-09-20 16:32:44] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -68.272809-0.000002j
[2025-09-20 16:33:16] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -68.279976+0.001429j
[2025-09-20 16:33:48] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -68.270256+0.000518j
[2025-09-20 16:34:19] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -68.265369+0.001517j
[2025-09-20 16:34:52] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -68.292570-0.001078j
[2025-09-20 16:35:24] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -68.279629+0.000497j
[2025-09-20 16:35:56] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -68.267494-0.001038j
[2025-09-20 16:36:27] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -68.288108+0.001852j
[2025-09-20 16:36:59] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -68.275598-0.001061j
[2025-09-20 16:37:31] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -68.278574+0.000501j
[2025-09-20 16:38:03] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -68.272491-0.000083j
[2025-09-20 16:38:35] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -68.280475-0.000976j
[2025-09-20 16:39:07] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -68.276705-0.000142j
[2025-09-20 16:39:39] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -68.281552-0.000508j
[2025-09-20 16:40:12] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -68.278781-0.002125j
[2025-09-20 16:40:44] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -68.282459-0.000759j
[2025-09-20 16:41:16] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -68.278916-0.000859j
[2025-09-20 16:41:48] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -68.297517+0.003585j
[2025-09-20 16:42:20] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -68.281699+0.003440j
[2025-09-20 16:42:52] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -68.289690-0.001148j
[2025-09-20 16:43:24] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -68.281247-0.000578j
[2025-09-20 16:43:56] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -68.287775+0.002994j
[2025-09-20 16:44:28] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -68.287088+0.005317j
[2025-09-20 16:45:00] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -68.291090-0.000492j
[2025-09-20 16:45:32] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -68.266674+0.002074j
[2025-09-20 16:46:04] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -68.263805+0.000968j
[2025-09-20 16:46:36] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -68.270736+0.001442j
[2025-09-20 16:47:08] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -68.285594+0.001952j
[2025-09-20 16:47:40] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -68.270433+0.000053j
[2025-09-20 16:48:12] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -68.276683-0.001388j
[2025-09-20 16:48:44] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -68.272337-0.001831j
[2025-09-20 16:49:16] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -68.282348+0.000211j
[2025-09-20 16:49:48] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -68.279741+0.002403j
[2025-09-20 16:50:20] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -68.296477-0.001491j
[2025-09-20 16:50:52] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -68.285044-0.001834j
[2025-09-20 16:51:24] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -68.287445-0.000304j
[2025-09-20 16:51:56] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -68.288617+0.003727j
[2025-09-20 16:52:28] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -68.278314+0.001538j
[2025-09-20 16:53:00] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -68.272558-0.001559j
[2025-09-20 16:53:32] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -68.266774-0.005347j
[2025-09-20 16:54:04] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -68.277171+0.000880j
[2025-09-20 16:54:36] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -68.288522-0.000988j
[2025-09-20 16:55:08] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -68.269210-0.000001j
[2025-09-20 16:55:40] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -68.271646-0.000079j
[2025-09-20 16:56:12] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -68.287384-0.001254j
[2025-09-20 16:56:44] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -68.278912-0.000006j
[2025-09-20 16:57:16] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -68.297137-0.000408j
[2025-09-20 16:57:48] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -68.274788+0.000053j
[2025-09-20 16:58:20] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -68.275701-0.001291j
[2025-09-20 16:58:52] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -68.290856-0.004945j
[2025-09-20 16:59:24] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -68.271932-0.000747j
[2025-09-20 16:59:56] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -68.284334-0.000686j
[2025-09-20 17:00:28] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -68.269414-0.002793j
[2025-09-20 17:01:01] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -68.286768+0.001513j
[2025-09-20 17:01:33] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -68.270444-0.001796j
[2025-09-20 17:02:04] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -68.284383-0.001197j
[2025-09-20 17:02:37] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -68.270424-0.000187j
[2025-09-20 17:03:09] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -68.282794+0.001100j
[2025-09-20 17:03:41] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -68.273332-0.000020j
[2025-09-20 17:04:13] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -68.276519-0.003220j
[2025-09-20 17:04:45] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -68.295023-0.004600j
[2025-09-20 17:05:17] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -68.278517+0.001731j
[2025-09-20 17:05:49] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -68.290694+0.000332j
[2025-09-20 17:06:21] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -68.284666+0.001283j
[2025-09-20 17:06:53] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -68.291784-0.000213j
[2025-09-20 17:07:25] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -68.295245+0.000705j
[2025-09-20 17:07:57] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -68.255089+0.002126j
[2025-09-20 17:08:29] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -68.276722+0.001126j
[2025-09-20 17:09:01] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -68.286170-0.004015j
[2025-09-20 17:09:33] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -68.282212-0.001805j
[2025-09-20 17:10:05] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -68.265159+0.013855j
[2025-09-20 17:10:37] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -68.270146+0.000970j
[2025-09-20 17:11:09] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -68.279078+0.001687j
[2025-09-20 17:11:41] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -68.288799-0.000376j
[2025-09-20 17:12:13] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -68.274474-0.001898j
[2025-09-20 17:12:45] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -68.293910-0.000560j
[2025-09-20 17:13:17] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -68.290115-0.003091j
[2025-09-20 17:13:49] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -68.300498-0.000372j
[2025-09-20 17:14:21] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -68.276547-0.002697j
[2025-09-20 17:14:53] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -68.288436+0.000758j
[2025-09-20 17:15:25] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -68.287959-0.001194j
[2025-09-20 17:15:58] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -68.270392-0.002162j
[2025-09-20 17:16:30] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -68.283124-0.002536j
[2025-09-20 17:17:02] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -68.287435+0.003540j
[2025-09-20 17:17:34] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -68.286685+0.000054j
[2025-09-20 17:18:06] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -68.281726-0.000331j
[2025-09-20 17:18:38] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -68.270143+0.000678j
[2025-09-20 17:19:10] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -68.275403-0.001308j
[2025-09-20 17:19:42] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -68.276959-0.003332j
[2025-09-20 17:19:42] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-20 17:20:14] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -68.268529+0.002121j
[2025-09-20 17:20:46] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -68.283523-0.002747j
[2025-09-20 17:21:18] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -68.282158-0.002328j
[2025-09-20 17:21:50] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -68.277772+0.001949j
[2025-09-20 17:22:22] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -68.281390-0.001491j
[2025-09-20 17:22:54] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -68.280979-0.001074j
[2025-09-20 17:23:26] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -68.287032-0.000719j
[2025-09-20 17:23:58] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -68.300042+0.001145j
[2025-09-20 17:24:30] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -68.285681-0.001182j
[2025-09-20 17:25:02] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -68.275154-0.000930j
[2025-09-20 17:25:34] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -68.291059+0.000317j
[2025-09-20 17:26:06] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -68.272583+0.001409j
[2025-09-20 17:26:38] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -68.294772+0.000008j
[2025-09-20 17:27:10] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -68.278620-0.001431j
[2025-09-20 17:27:42] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -68.256364-0.001472j
[2025-09-20 17:28:14] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -68.275001+0.002663j
[2025-09-20 17:28:46] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -68.274098-0.003091j
[2025-09-20 17:29:18] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -68.274134-0.002104j
[2025-09-20 17:29:50] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -68.294042+0.002182j
[2025-09-20 17:30:22] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -68.293393-0.005642j
[2025-09-20 17:30:54] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -68.271327+0.001780j
[2025-09-20 17:31:26] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -68.279072+0.001975j
[2025-09-20 17:31:58] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -68.274738-0.002101j
[2025-09-20 17:32:30] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -68.272113-0.001130j
[2025-09-20 17:33:02] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -68.279791-0.000509j
[2025-09-20 17:33:34] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -68.282941-0.000376j
[2025-09-20 17:34:06] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -68.278630-0.001662j
[2025-09-20 17:34:38] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -68.293930-0.000517j
[2025-09-20 17:35:10] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -68.269210+0.001753j
[2025-09-20 17:35:42] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -68.277075-0.000239j
[2025-09-20 17:36:14] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -68.284750+0.000700j
[2025-09-20 17:36:46] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -68.272694+0.002668j
[2025-09-20 17:37:18] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -68.286937+0.001547j
[2025-09-20 17:37:50] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -68.286906-0.000128j
[2025-09-20 17:38:22] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -68.272002+0.005183j
[2025-09-20 17:38:54] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -68.279906-0.000411j
[2025-09-20 17:39:26] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -68.290142-0.001116j
[2025-09-20 17:39:58] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -68.294397-0.002518j
[2025-09-20 17:40:30] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -68.284569-0.001169j
[2025-09-20 17:41:02] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -68.275052+0.000653j
[2025-09-20 17:41:34] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -68.275932-0.000752j
[2025-09-20 17:42:06] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -68.293963-0.001306j
[2025-09-20 17:42:38] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -68.283417+0.001440j
[2025-09-20 17:43:10] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -68.283775+0.001032j
[2025-09-20 17:43:42] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -68.284583-0.000932j
[2025-09-20 17:44:14] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -68.272558+0.004643j
[2025-09-20 17:44:46] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -68.274288-0.001466j
[2025-09-20 17:45:18] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -68.293196-0.000483j
[2025-09-20 17:45:50] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -68.272777+0.001101j
[2025-09-20 17:46:22] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -68.285269+0.000099j
[2025-09-20 17:46:54] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -68.279600+0.001531j
[2025-09-20 17:47:26] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -68.293826-0.001097j
[2025-09-20 17:47:58] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -68.292966+0.002536j
[2025-09-20 17:48:30] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -68.271465+0.001833j
[2025-09-20 17:49:02] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -68.276083-0.000545j
[2025-09-20 17:49:34] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -68.270413-0.002179j
[2025-09-20 17:50:06] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -68.280680-0.001275j
[2025-09-20 17:50:38] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -68.280731-0.000621j
[2025-09-20 17:51:10] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -68.282805+0.010611j
[2025-09-20 17:51:42] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -68.269215-0.004292j
[2025-09-20 17:52:14] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -68.271802-0.001272j
[2025-09-20 17:52:46] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -68.280033+0.000496j
[2025-09-20 17:53:18] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -68.290042+0.002400j
[2025-09-20 17:53:50] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -68.273816+0.005136j
[2025-09-20 17:54:22] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -68.294731-0.000994j
[2025-09-20 17:54:54] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -68.275526+0.002347j
[2025-09-20 17:55:27] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -68.270456+0.001155j
[2025-09-20 17:55:59] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -68.296810+0.003217j
[2025-09-20 17:56:31] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -68.286845+0.001189j
[2025-09-20 17:57:03] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -68.284047-0.000106j
[2025-09-20 17:57:35] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -68.290464+0.000099j
[2025-09-20 17:58:07] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -68.278432+0.001455j
[2025-09-20 17:58:39] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -68.272956+0.002271j
[2025-09-20 17:59:11] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -68.284550+0.001070j
[2025-09-20 17:59:43] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -68.294669+0.003457j
[2025-09-20 18:00:15] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -68.274689-0.004098j
[2025-09-20 18:00:47] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -68.292112+0.000149j
[2025-09-20 18:01:19] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -68.288465+0.001246j
[2025-09-20 18:01:51] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -68.252340+0.000895j
[2025-09-20 18:02:23] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -68.274196-0.000496j
[2025-09-20 18:02:55] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -68.280337+0.000034j
[2025-09-20 18:03:27] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -68.280780-0.002446j
[2025-09-20 18:03:59] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -68.293517-0.002758j
[2025-09-20 18:04:31] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -68.284846+0.002555j
[2025-09-20 18:05:03] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -68.288876+0.000265j
[2025-09-20 18:05:35] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -68.294209+0.000316j
[2025-09-20 18:06:07] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -68.268174-0.001157j
[2025-09-20 18:06:39] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -68.278756+0.000084j
[2025-09-20 18:07:11] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -68.288340-0.000281j
[2025-09-20 18:07:43] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -68.274991-0.001418j
[2025-09-20 18:08:15] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -68.283411-0.001313j
[2025-09-20 18:08:47] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -68.274513+0.000670j
[2025-09-20 18:09:19] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -68.276711-0.000507j
[2025-09-20 18:09:51] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -68.273986+0.000177j
[2025-09-20 18:10:23] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -68.282223+0.000592j
[2025-09-20 18:10:55] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -68.274411+0.002014j
[2025-09-20 18:11:27] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -68.267104+0.000636j
[2025-09-20 18:11:59] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -68.283551-0.000424j
[2025-09-20 18:12:31] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -68.284073+0.000363j
[2025-09-20 18:13:02] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -68.282430+0.001731j
[2025-09-20 18:13:34] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -68.287106-0.002361j
[2025-09-20 18:14:06] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -68.270921-0.002196j
[2025-09-20 18:14:38] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -68.278223+0.006508j
[2025-09-20 18:15:10] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -68.280867+0.000685j
[2025-09-20 18:15:42] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -68.283299+0.001984j
[2025-09-20 18:15:43] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-20 18:15:43] ✅ Training completed | Restarts: 2
[2025-09-20 18:15:43] ============================================================
[2025-09-20 18:15:43] Training completed | Runtime: 33702.7s
[2025-09-20 18:15:54] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-20 18:15:54] ============================================================
