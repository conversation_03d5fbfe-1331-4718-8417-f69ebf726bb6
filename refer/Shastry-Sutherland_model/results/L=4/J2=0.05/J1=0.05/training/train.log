[2025-09-03 14:33:26] ==================================================
[2025-09-03 14:33:26] GCNN for Shastry-Sutherland Model
[2025-09-03 14:33:26] ==================================================
[2025-09-03 14:33:26] System parameters:
[2025-09-03 14:33:26]   - System size: L=4, N=64
[2025-09-03 14:33:26]   - System parameters: J1=0.05, J2=0.05, Q=0.95
[2025-09-03 14:33:26] --------------------------------------------------
[2025-09-03 14:33:26] Model parameters:
[2025-09-03 14:33:26]   - Number of layers = 4
[2025-09-03 14:33:26]   - Number of features = 4
[2025-09-03 14:33:26]   - Total parameters = 12572
[2025-09-03 14:33:26] --------------------------------------------------
[2025-09-03 14:33:26] Training parameters:
[2025-09-03 14:33:26]   - Learning rate: 0.015
[2025-09-03 14:33:26]   - Total iterations: 2250
[2025-09-03 14:33:26]   - Annealing cycles: 4
[2025-09-03 14:33:26]   - Initial period: 150
[2025-09-03 14:33:26]   - Period multiplier: 2.0
[2025-09-03 14:33:26]   - Temperature range: 0.0-1.0
[2025-09-03 14:33:26]   - Samples: 16384
[2025-09-03 14:33:26]   - Discarded samples: 0
[2025-09-03 14:33:26]   - Chunk size: 2048
[2025-09-03 14:33:26]   - Diagonal shift: 0.2
[2025-09-03 14:33:26]   - Gradient clipping: 1.0
[2025-09-03 14:33:26]   - Checkpoint enabled: interval=250
[2025-09-03 14:33:26]   - Checkpoint directory: results/L=4/J2=0.05/J1=0.05/training/checkpoints
[2025-09-03 14:33:26] --------------------------------------------------
[2025-09-03 14:33:26] Device status:
[2025-09-03 14:33:26]   - Devices model: NVIDIA H200 NVL
[2025-09-03 14:33:26]   - Number of devices: 1
[2025-09-03 14:33:26]   - Sharding: True
[2025-09-03 14:33:26] ============================================================
[2025-09-03 14:34:30] [Iter 1/2250] R0[0/150], Temp: 1.0000, Energy: 1.999447-0.000204j
[2025-09-03 14:35:05] [Iter 2/2250] R0[1/150], Temp: 0.9999, Energy: 1.999181-0.000099j
[2025-09-03 14:36:05] [Iter 3/2250] R0[2/150], Temp: 0.9996, Energy: 1.999051+0.000136j
[2025-09-03 14:37:05] [Iter 4/2250] R0[3/150], Temp: 0.9990, Energy: 1.999250-0.000439j
[2025-09-03 14:38:05] [Iter 5/2250] R0[4/150], Temp: 0.9982, Energy: 1.999254-0.000183j
[2025-09-03 14:39:05] [Iter 6/2250] R0[5/150], Temp: 0.9973, Energy: 1.999246-0.000076j
[2025-09-03 14:40:05] [Iter 7/2250] R0[6/150], Temp: 0.9961, Energy: 1.998957+0.000103j
[2025-09-03 14:41:05] [Iter 8/2250] R0[7/150], Temp: 0.9946, Energy: 1.998956-0.000060j
[2025-09-03 14:42:05] [Iter 9/2250] R0[8/150], Temp: 0.9930, Energy: 1.998902-0.000206j
[2025-09-03 14:43:05] [Iter 10/2250] R0[9/150], Temp: 0.9911, Energy: 1.999033+0.000059j
[2025-09-03 14:44:05] [Iter 11/2250] R0[10/150], Temp: 0.9891, Energy: 1.999290-0.000128j
[2025-09-03 14:45:05] [Iter 12/2250] R0[11/150], Temp: 0.9868, Energy: 1.999120+0.000031j
[2025-09-03 14:46:06] [Iter 13/2250] R0[12/150], Temp: 0.9843, Energy: 1.998825-0.000083j
[2025-09-03 14:47:06] [Iter 14/2250] R0[13/150], Temp: 0.9816, Energy: 1.999158-0.000154j
[2025-09-03 14:48:06] [Iter 15/2250] R0[14/150], Temp: 0.9787, Energy: 1.999190+0.000149j
[2025-09-03 14:49:06] [Iter 16/2250] R0[15/150], Temp: 0.9755, Energy: 1.999101-0.000268j
[2025-09-03 14:50:06] [Iter 17/2250] R0[16/150], Temp: 0.9722, Energy: 1.999202+0.000319j
[2025-09-03 14:51:07] [Iter 18/2250] R0[17/150], Temp: 0.9686, Energy: 1.998889-0.000115j
[2025-09-03 14:52:07] [Iter 19/2250] R0[18/150], Temp: 0.9649, Energy: 1.998793+0.000291j
[2025-09-03 14:53:07] [Iter 20/2250] R0[19/150], Temp: 0.9609, Energy: 1.999006+0.000193j
[2025-09-03 14:54:07] [Iter 21/2250] R0[20/150], Temp: 0.9568, Energy: 1.999125-0.000048j
[2025-09-03 14:55:07] [Iter 22/2250] R0[21/150], Temp: 0.9524, Energy: 1.998981-0.000111j
[2025-09-03 14:56:08] [Iter 23/2250] R0[22/150], Temp: 0.9479, Energy: 1.999116+0.000183j
[2025-09-03 14:57:08] [Iter 24/2250] R0[23/150], Temp: 0.9431, Energy: 1.999019-0.000157j
[2025-09-03 14:58:08] [Iter 25/2250] R0[24/150], Temp: 0.9382, Energy: 1.998683+0.000124j
[2025-09-03 14:59:08] [Iter 26/2250] R0[25/150], Temp: 0.9330, Energy: 1.999059-0.000087j
[2025-09-03 15:00:08] [Iter 27/2250] R0[26/150], Temp: 0.9277, Energy: 1.998546+0.000095j
[2025-09-03 15:01:08] [Iter 28/2250] R0[27/150], Temp: 0.9222, Energy: 1.998572+0.000058j
[2025-09-03 15:02:08] [Iter 29/2250] R0[28/150], Temp: 0.9165, Energy: 1.998698+0.000057j
[2025-09-03 15:03:08] [Iter 30/2250] R0[29/150], Temp: 0.9106, Energy: 1.998665+0.000086j
[2025-09-03 15:04:09] [Iter 31/2250] R0[30/150], Temp: 0.9045, Energy: 1.999267-0.000061j
[2025-09-03 15:05:09] [Iter 32/2250] R0[31/150], Temp: 0.8983, Energy: 1.998054+0.000296j
[2025-09-03 15:06:09] [Iter 33/2250] R0[32/150], Temp: 0.8918, Energy: 1.998328+0.000239j
[2025-09-03 15:07:09] [Iter 34/2250] R0[33/150], Temp: 0.8853, Energy: 1.998597+0.000250j
[2025-09-03 15:08:09] [Iter 35/2250] R0[34/150], Temp: 0.8785, Energy: 1.998222+0.000189j
[2025-09-03 15:09:09] [Iter 36/2250] R0[35/150], Temp: 0.8716, Energy: 1.998565+0.000089j
[2025-09-03 15:10:09] [Iter 37/2250] R0[36/150], Temp: 0.8645, Energy: 1.998539-0.000280j
[2025-09-03 15:11:09] [Iter 38/2250] R0[37/150], Temp: 0.8572, Energy: 1.998555-0.000102j
[2025-09-03 15:12:09] [Iter 39/2250] R0[38/150], Temp: 0.8498, Energy: 1.998856-0.000649j
[2025-09-03 15:13:09] [Iter 40/2250] R0[39/150], Temp: 0.8423, Energy: 1.998125-0.000348j
[2025-09-03 15:14:09] [Iter 41/2250] R0[40/150], Temp: 0.8346, Energy: 1.998584-0.000021j
[2025-09-03 15:15:10] [Iter 42/2250] R0[41/150], Temp: 0.8267, Energy: 1.997920+0.000149j
[2025-09-03 15:16:10] [Iter 43/2250] R0[42/150], Temp: 0.8187, Energy: 1.998348-0.000193j
[2025-09-03 15:17:10] [Iter 44/2250] R0[43/150], Temp: 0.8106, Energy: 1.998173+0.000043j
[2025-09-03 15:18:10] [Iter 45/2250] R0[44/150], Temp: 0.8023, Energy: 1.997812+0.000109j
[2025-09-03 15:19:10] [Iter 46/2250] R0[45/150], Temp: 0.7939, Energy: 1.997833+0.000105j
[2025-09-03 15:20:10] [Iter 47/2250] R0[46/150], Temp: 0.7854, Energy: 1.997982-0.000166j
[2025-09-03 15:21:10] [Iter 48/2250] R0[47/150], Temp: 0.7767, Energy: 1.998744-0.000500j
[2025-09-03 15:22:10] [Iter 49/2250] R0[48/150], Temp: 0.7679, Energy: 1.997472+0.000230j
[2025-09-03 15:23:10] [Iter 50/2250] R0[49/150], Temp: 0.7590, Energy: 1.998039-0.000275j
[2025-09-03 15:24:10] [Iter 51/2250] R0[50/150], Temp: 0.7500, Energy: 1.997479-0.000231j
[2025-09-03 15:25:10] [Iter 52/2250] R0[51/150], Temp: 0.7409, Energy: 1.997314+0.000012j
[2025-09-03 15:26:10] [Iter 53/2250] R0[52/150], Temp: 0.7316, Energy: 1.997709+0.000369j
[2025-09-03 15:27:11] [Iter 54/2250] R0[53/150], Temp: 0.7223, Energy: 1.996855+0.000058j
[2025-09-03 15:28:11] [Iter 55/2250] R0[54/150], Temp: 0.7129, Energy: 1.997917-0.000033j
[2025-09-03 15:29:11] [Iter 56/2250] R0[55/150], Temp: 0.7034, Energy: 1.996978+0.000007j
[2025-09-03 15:30:11] [Iter 57/2250] R0[56/150], Temp: 0.6938, Energy: 1.996265+0.000340j
[2025-09-03 15:31:11] [Iter 58/2250] R0[57/150], Temp: 0.6841, Energy: 1.996646+0.000218j
[2025-09-03 15:32:11] [Iter 59/2250] R0[58/150], Temp: 0.6743, Energy: 1.997634+0.000009j
[2025-09-03 15:33:11] [Iter 60/2250] R0[59/150], Temp: 0.6644, Energy: 1.997141-0.000021j
[2025-09-03 15:34:11] [Iter 61/2250] R0[60/150], Temp: 0.6545, Energy: 1.995691+0.000117j
[2025-09-03 15:35:11] [Iter 62/2250] R0[61/150], Temp: 0.6445, Energy: 1.996155+0.000362j
[2025-09-03 15:36:11] [Iter 63/2250] R0[62/150], Temp: 0.6345, Energy: 1.995529+0.000023j
[2025-09-03 15:37:11] [Iter 64/2250] R0[63/150], Temp: 0.6243, Energy: 1.994660+0.000070j
[2025-09-03 15:38:12] [Iter 65/2250] R0[64/150], Temp: 0.6142, Energy: 1.995829+0.000094j
[2025-09-03 15:39:12] [Iter 66/2250] R0[65/150], Temp: 0.6040, Energy: 1.995646+0.000319j
[2025-09-03 15:40:12] [Iter 67/2250] R0[66/150], Temp: 0.5937, Energy: 1.995264+0.000287j
[2025-09-03 15:41:12] [Iter 68/2250] R0[67/150], Temp: 0.5834, Energy: 1.996318-0.000322j
[2025-09-03 15:42:12] [Iter 69/2250] R0[68/150], Temp: 0.5730, Energy: 1.994356-0.000050j
[2025-09-03 15:43:12] [Iter 70/2250] R0[69/150], Temp: 0.5627, Energy: 1.993077+0.000765j
[2025-09-03 15:44:12] [Iter 71/2250] R0[70/150], Temp: 0.5523, Energy: 1.993988-0.000112j
[2025-09-03 15:45:12] [Iter 72/2250] R0[71/150], Temp: 0.5418, Energy: 1.993987+0.000031j
[2025-09-03 15:46:12] [Iter 73/2250] R0[72/150], Temp: 0.5314, Energy: 1.994080-0.000097j
[2025-09-03 15:47:12] [Iter 74/2250] R0[73/150], Temp: 0.5209, Energy: 1.992186+0.000499j
[2025-09-03 15:48:13] [Iter 75/2250] R0[74/150], Temp: 0.5105, Energy: 1.993921-0.000388j
[2025-09-03 15:49:13] [Iter 76/2250] R0[75/150], Temp: 0.5000, Energy: 1.989988+0.000549j
[2025-09-03 15:50:13] [Iter 77/2250] R0[76/150], Temp: 0.4895, Energy: 1.992254-0.000247j
[2025-09-03 15:51:13] [Iter 78/2250] R0[77/150], Temp: 0.4791, Energy: 1.989475-0.000380j
[2025-09-03 15:52:13] [Iter 79/2250] R0[78/150], Temp: 0.4686, Energy: 1.990795-0.000483j
[2025-09-03 15:53:13] [Iter 80/2250] R0[79/150], Temp: 0.4582, Energy: 1.988506+0.000127j
[2025-09-03 15:54:13] [Iter 81/2250] R0[80/150], Temp: 0.4477, Energy: 1.988450-0.000085j
[2025-09-03 15:55:13] [Iter 82/2250] R0[81/150], Temp: 0.4373, Energy: 1.988386+0.000076j
[2025-09-03 15:56:13] [Iter 83/2250] R0[82/150], Temp: 0.4270, Energy: 1.989814-0.000254j
[2025-09-03 15:57:13] [Iter 84/2250] R0[83/150], Temp: 0.4166, Energy: 1.988105-0.000291j
[2025-09-03 15:58:13] [Iter 85/2250] R0[84/150], Temp: 0.4063, Energy: 1.987650+0.000071j
[2025-09-03 15:59:13] [Iter 86/2250] R0[85/150], Temp: 0.3960, Energy: 1.983420+0.000690j
[2025-09-03 16:00:14] [Iter 87/2250] R0[86/150], Temp: 0.3858, Energy: 1.981669+0.000597j
[2025-09-03 16:01:14] [Iter 88/2250] R0[87/150], Temp: 0.3757, Energy: 1.983615-0.000729j
[2025-09-03 16:02:14] [Iter 89/2250] R0[88/150], Temp: 0.3655, Energy: 1.979654-0.000487j
[2025-09-03 16:03:14] [Iter 90/2250] R0[89/150], Temp: 0.3555, Energy: 1.977696+0.000789j
[2025-09-03 16:04:14] [Iter 91/2250] R0[90/150], Temp: 0.3455, Energy: 1.976635+0.000322j
[2025-09-03 16:05:14] [Iter 92/2250] R0[91/150], Temp: 0.3356, Energy: 1.976466-0.000401j
[2025-09-03 16:06:14] [Iter 93/2250] R0[92/150], Temp: 0.3257, Energy: 1.972764-0.000090j
[2025-09-03 16:07:14] [Iter 94/2250] R0[93/150], Temp: 0.3159, Energy: 1.970772+0.001041j
[2025-09-03 16:08:14] [Iter 95/2250] R0[94/150], Temp: 0.3062, Energy: 1.968084-0.000291j
[2025-09-03 16:09:14] [Iter 96/2250] R0[95/150], Temp: 0.2966, Energy: 1.970683-0.001129j
[2025-09-03 16:10:14] [Iter 97/2250] R0[96/150], Temp: 0.2871, Energy: 1.962219+0.000548j
[2025-09-03 16:11:14] [Iter 98/2250] R0[97/150], Temp: 0.2777, Energy: 1.960658+0.000179j
[2025-09-03 16:12:15] [Iter 99/2250] R0[98/150], Temp: 0.2684, Energy: 1.952778+0.001188j
[2025-09-03 16:13:15] [Iter 100/2250] R0[99/150], Temp: 0.2591, Energy: 1.955472-0.000160j
[2025-09-03 16:14:15] [Iter 101/2250] R0[100/150], Temp: 0.2500, Energy: 1.950269-0.001734j
[2025-09-03 16:15:15] [Iter 102/2250] R0[101/150], Temp: 0.2410, Energy: 1.942685+0.000985j
[2025-09-03 16:16:15] [Iter 103/2250] R0[102/150], Temp: 0.2321, Energy: 1.940611-0.000655j
[2025-09-03 16:17:15] [Iter 104/2250] R0[103/150], Temp: 0.2233, Energy: 1.936234-0.001655j
[2025-09-03 16:18:16] [Iter 105/2250] R0[104/150], Temp: 0.2146, Energy: 1.924698+0.001259j
[2025-09-03 16:19:16] [Iter 106/2250] R0[105/150], Temp: 0.2061, Energy: 1.922043-0.002148j
[2025-09-03 16:20:16] [Iter 107/2250] R0[106/150], Temp: 0.1977, Energy: 1.910807+0.000461j
[2025-09-03 16:21:16] [Iter 108/2250] R0[107/150], Temp: 0.1894, Energy: 1.898791+0.001626j
[2025-09-03 16:22:16] [Iter 109/2250] R0[108/150], Temp: 0.1813, Energy: 1.888510-0.000262j
[2025-09-03 16:23:16] [Iter 110/2250] R0[109/150], Temp: 0.1733, Energy: 1.887161-0.001923j
[2025-09-03 16:24:17] [Iter 111/2250] R0[110/150], Temp: 0.1654, Energy: 1.867673+0.001998j
[2025-09-03 16:25:17] [Iter 112/2250] R0[111/150], Temp: 0.1577, Energy: 1.855703+0.000971j
[2025-09-03 16:26:17] [Iter 113/2250] R0[112/150], Temp: 0.1502, Energy: 1.834906+0.000513j
[2025-09-03 16:27:17] [Iter 114/2250] R0[113/150], Temp: 0.1428, Energy: 1.828890-0.002233j
[2025-09-03 16:28:17] [Iter 115/2250] R0[114/150], Temp: 0.1355, Energy: 1.810994-0.003403j
[2025-09-03 16:29:17] [Iter 116/2250] R0[115/150], Temp: 0.1284, Energy: 1.775617-0.001434j
[2025-09-03 16:30:17] [Iter 117/2250] R0[116/150], Temp: 0.1215, Energy: 1.753855+0.000452j
[2025-09-03 16:31:18] [Iter 118/2250] R0[117/150], Temp: 0.1147, Energy: 1.735776-0.001953j
[2025-09-03 16:32:18] [Iter 119/2250] R0[118/150], Temp: 0.1082, Energy: 1.701742-0.000655j
[2025-09-03 16:33:18] [Iter 120/2250] R0[119/150], Temp: 0.1017, Energy: 1.671554+0.000549j
[2025-09-03 16:34:18] [Iter 121/2250] R0[120/150], Temp: 0.0955, Energy: 1.617729-0.000796j
[2025-09-03 16:35:18] [Iter 122/2250] R0[121/150], Temp: 0.0894, Energy: 1.585877+0.000544j
[2025-09-03 16:36:18] [Iter 123/2250] R0[122/150], Temp: 0.0835, Energy: 1.527934+0.001578j
[2025-09-03 16:37:19] [Iter 124/2250] R0[123/150], Temp: 0.0778, Energy: 1.480114+0.001518j
[2025-09-03 16:38:19] [Iter 125/2250] R0[124/150], Temp: 0.0723, Energy: 1.439287-0.002717j
[2025-09-03 16:39:19] [Iter 126/2250] R0[125/150], Temp: 0.0670, Energy: 1.365797+0.002255j
[2025-09-03 16:40:19] [Iter 127/2250] R0[126/150], Temp: 0.0618, Energy: 1.303281+0.003299j
[2025-09-03 16:41:19] [Iter 128/2250] R0[127/150], Temp: 0.0569, Energy: 1.214056+0.002464j
[2025-09-03 16:42:19] [Iter 129/2250] R0[128/150], Temp: 0.0521, Energy: 1.126474+0.007168j
[2025-09-03 16:43:20] [Iter 130/2250] R0[129/150], Temp: 0.0476, Energy: 1.043131+0.003022j
[2025-09-03 16:44:20] [Iter 131/2250] R0[130/150], Temp: 0.0432, Energy: 0.953267-0.000204j
[2025-09-03 16:45:20] [Iter 132/2250] R0[131/150], Temp: 0.0391, Energy: 0.840741-0.003712j
[2025-09-03 16:46:20] [Iter 133/2250] R0[132/150], Temp: 0.0351, Energy: 0.722695-0.004316j
[2025-09-03 16:47:20] [Iter 134/2250] R0[133/150], Temp: 0.0314, Energy: 0.632606-0.001082j
[2025-09-03 16:48:20] [Iter 135/2250] R0[134/150], Temp: 0.0278, Energy: 0.455630+0.006890j
[2025-09-03 16:49:21] [Iter 136/2250] R0[135/150], Temp: 0.0245, Energy: 0.323559+0.004672j
[2025-09-03 16:50:21] [Iter 137/2250] R0[136/150], Temp: 0.0213, Energy: 0.113395+0.009369j
[2025-09-03 16:51:21] [Iter 138/2250] R0[137/150], Temp: 0.0184, Energy: -0.071358+0.016807j
[2025-09-03 16:52:21] [Iter 139/2250] R0[138/150], Temp: 0.0157, Energy: -0.266037+0.001296j
[2025-09-03 16:53:21] [Iter 140/2250] R0[139/150], Temp: 0.0132, Energy: -0.450392-0.004357j
[2025-09-03 16:54:21] [Iter 141/2250] R0[140/150], Temp: 0.0109, Energy: -0.717079+0.005767j
[2025-09-03 16:55:22] [Iter 142/2250] R0[141/150], Temp: 0.0089, Energy: -0.910944-0.003692j
[2025-09-03 16:56:22] [Iter 143/2250] R0[142/150], Temp: 0.0070, Energy: -1.255749+0.001808j
[2025-09-03 16:57:22] [Iter 144/2250] R0[143/150], Temp: 0.0054, Energy: -1.512828-0.025288j
[2025-09-03 16:58:22] [Iter 145/2250] R0[144/150], Temp: 0.0039, Energy: -1.868854+0.023592j
[2025-09-03 16:59:22] [Iter 146/2250] R0[145/150], Temp: 0.0027, Energy: -2.181189-0.018741j
[2025-09-03 17:00:22] [Iter 147/2250] R0[146/150], Temp: 0.0018, Energy: -2.571917-0.010622j
[2025-09-03 17:01:23] [Iter 148/2250] R0[147/150], Temp: 0.0010, Energy: -2.976091-0.023860j
[2025-09-03 17:02:23] [Iter 149/2250] R0[148/150], Temp: 0.0004, Energy: -3.424678+0.017190j
[2025-09-03 17:03:23] [Iter 150/2250] R0[149/150], Temp: 0.0001, Energy: -3.913803+0.025455j
[2025-09-03 17:03:23] RESTART #1 | Period: 300
[2025-09-03 17:04:23] [Iter 151/2250] R1[0/300], Temp: 1.0000, Energy: -4.360001-0.025805j
[2025-09-03 17:05:23] [Iter 152/2250] R1[1/300], Temp: 1.0000, Energy: -4.857268+0.004999j
[2025-09-03 17:06:23] [Iter 153/2250] R1[2/300], Temp: 0.9999, Energy: -5.421362-0.021828j
[2025-09-03 17:07:24] [Iter 154/2250] R1[3/300], Temp: 0.9998, Energy: -6.056029-0.018849j
[2025-09-03 17:08:24] [Iter 155/2250] R1[4/300], Temp: 0.9996, Energy: -6.545637-0.025181j
[2025-09-03 17:09:24] [Iter 156/2250] R1[5/300], Temp: 0.9993, Energy: -7.232065-0.032592j
[2025-09-03 17:10:24] [Iter 157/2250] R1[6/300], Temp: 0.9990, Energy: -7.932383+0.035096j
[2025-09-03 17:11:24] [Iter 158/2250] R1[7/300], Temp: 0.9987, Energy: -8.598659-0.034532j
[2025-09-03 17:12:25] [Iter 159/2250] R1[8/300], Temp: 0.9982, Energy: -9.411337-0.003300j
[2025-09-03 17:13:25] [Iter 160/2250] R1[9/300], Temp: 0.9978, Energy: -10.101243-0.027973j
[2025-09-03 17:14:25] [Iter 161/2250] R1[10/300], Temp: 0.9973, Energy: -10.984430-0.004029j
[2025-09-03 17:15:25] [Iter 162/2250] R1[11/300], Temp: 0.9967, Energy: -11.839379-0.051403j
[2025-09-03 17:16:25] [Iter 163/2250] R1[12/300], Temp: 0.9961, Energy: -12.654898-0.073978j
[2025-09-03 17:17:25] [Iter 164/2250] R1[13/300], Temp: 0.9954, Energy: -13.598957-0.075828j
[2025-09-03 17:18:26] [Iter 165/2250] R1[14/300], Temp: 0.9946, Energy: -14.576312-0.056763j
[2025-09-03 17:19:26] [Iter 166/2250] R1[15/300], Temp: 0.9938, Energy: -15.479513-0.083161j
[2025-09-03 17:20:26] [Iter 167/2250] R1[16/300], Temp: 0.9930, Energy: -16.426078-0.032945j
[2025-09-03 17:21:26] [Iter 168/2250] R1[17/300], Temp: 0.9921, Energy: -17.523354-0.017257j
[2025-09-03 17:22:26] [Iter 169/2250] R1[18/300], Temp: 0.9911, Energy: -18.517495-0.052137j
[2025-09-03 17:23:26] [Iter 170/2250] R1[19/300], Temp: 0.9901, Energy: -19.601996-0.106399j
[2025-09-03 17:24:27] [Iter 171/2250] R1[20/300], Temp: 0.9891, Energy: -20.624231-0.044911j
[2025-09-03 17:25:27] [Iter 172/2250] R1[21/300], Temp: 0.9880, Energy: -22.008596-0.033122j
[2025-09-03 17:26:27] [Iter 173/2250] R1[22/300], Temp: 0.9868, Energy: -23.155111+0.057599j
[2025-09-03 17:27:27] [Iter 174/2250] R1[23/300], Temp: 0.9856, Energy: -24.724582+0.111910j
[2025-09-03 17:28:27] [Iter 175/2250] R1[24/300], Temp: 0.9843, Energy: -26.477353+0.351427j
[2025-09-03 17:29:27] [Iter 176/2250] R1[25/300], Temp: 0.9830, Energy: -28.375247+0.553615j
[2025-09-03 17:30:28] [Iter 177/2250] R1[26/300], Temp: 0.9816, Energy: -30.603884+0.843468j
[2025-09-03 17:31:28] [Iter 178/2250] R1[27/300], Temp: 0.9801, Energy: -33.099957+1.097657j
[2025-09-03 17:32:28] [Iter 179/2250] R1[28/300], Temp: 0.9787, Energy: -35.946473+1.355642j
[2025-09-03 17:33:28] [Iter 180/2250] R1[29/300], Temp: 0.9771, Energy: -38.864157+1.801499j
[2025-09-03 17:34:28] [Iter 181/2250] R1[30/300], Temp: 0.9755, Energy: -42.024724+1.761264j
[2025-09-03 17:35:28] [Iter 182/2250] R1[31/300], Temp: 0.9739, Energy: -43.973905+1.486706j
[2025-09-03 17:36:29] [Iter 183/2250] R1[32/300], Temp: 0.9722, Energy: -45.406614+1.072745j
[2025-09-03 17:37:29] [Iter 184/2250] R1[33/300], Temp: 0.9704, Energy: -45.962982+0.710940j
[2025-09-03 17:38:29] [Iter 185/2250] R1[34/300], Temp: 0.9686, Energy: -45.920864+0.343047j
[2025-09-03 17:39:29] [Iter 186/2250] R1[35/300], Temp: 0.9668, Energy: -45.987147+0.185016j
[2025-09-03 17:40:29] [Iter 187/2250] R1[36/300], Temp: 0.9649, Energy: -45.946974+0.083234j
[2025-09-03 17:41:29] [Iter 188/2250] R1[37/300], Temp: 0.9629, Energy: -46.061146+0.066834j
[2025-09-03 17:42:30] [Iter 189/2250] R1[38/300], Temp: 0.9609, Energy: -46.211673+0.009485j
[2025-09-03 17:43:30] [Iter 190/2250] R1[39/300], Temp: 0.9589, Energy: -46.366591-0.009593j
[2025-09-03 17:44:30] [Iter 191/2250] R1[40/300], Temp: 0.9568, Energy: -46.522598-0.006287j
[2025-09-03 17:45:30] [Iter 192/2250] R1[41/300], Temp: 0.9546, Energy: -46.703998-0.029406j
[2025-09-03 17:46:30] [Iter 193/2250] R1[42/300], Temp: 0.9524, Energy: -46.914774-0.044445j
[2025-09-03 17:47:30] [Iter 194/2250] R1[43/300], Temp: 0.9502, Energy: -47.079353-0.035102j
[2025-09-03 17:48:30] [Iter 195/2250] R1[44/300], Temp: 0.9479, Energy: -47.283631-0.047834j
[2025-09-03 17:49:31] [Iter 196/2250] R1[45/300], Temp: 0.9455, Energy: -47.487366-0.028999j
[2025-09-03 17:50:31] [Iter 197/2250] R1[46/300], Temp: 0.9431, Energy: -47.707464-0.007083j
[2025-09-03 17:51:31] [Iter 198/2250] R1[47/300], Temp: 0.9407, Energy: -47.832983-0.017422j
[2025-09-03 17:52:31] [Iter 199/2250] R1[48/300], Temp: 0.9382, Energy: -48.024754-0.035607j
[2025-09-03 17:53:31] [Iter 200/2250] R1[49/300], Temp: 0.9356, Energy: -48.193725-0.013768j
[2025-09-03 17:54:32] [Iter 201/2250] R1[50/300], Temp: 0.9330, Energy: -48.310249-0.009023j
[2025-09-03 17:55:32] [Iter 202/2250] R1[51/300], Temp: 0.9304, Energy: -48.495206+0.017753j
[2025-09-03 17:56:32] [Iter 203/2250] R1[52/300], Temp: 0.9277, Energy: -48.572807+0.001888j
[2025-09-03 17:57:32] [Iter 204/2250] R1[53/300], Temp: 0.9249, Energy: -48.752145-0.009777j
[2025-09-03 17:58:32] [Iter 205/2250] R1[54/300], Temp: 0.9222, Energy: -48.805607+0.017601j
[2025-09-03 17:59:32] [Iter 206/2250] R1[55/300], Temp: 0.9193, Energy: -48.964047-0.014583j
[2025-09-03 18:00:33] [Iter 207/2250] R1[56/300], Temp: 0.9165, Energy: -49.094227+0.014384j
[2025-09-03 18:01:33] [Iter 208/2250] R1[57/300], Temp: 0.9135, Energy: -49.129974-0.004662j
[2025-09-03 18:02:33] [Iter 209/2250] R1[58/300], Temp: 0.9106, Energy: -49.230254+0.001518j
[2025-09-03 18:03:33] [Iter 210/2250] R1[59/300], Temp: 0.9076, Energy: -49.327446+0.009175j
[2025-09-03 18:04:33] [Iter 211/2250] R1[60/300], Temp: 0.9045, Energy: -49.442367+0.016555j
[2025-09-03 18:05:33] [Iter 212/2250] R1[61/300], Temp: 0.9014, Energy: -49.479484+0.011220j
[2025-09-03 18:06:34] [Iter 213/2250] R1[62/300], Temp: 0.8983, Energy: -49.519010+0.004469j
[2025-09-03 18:07:34] [Iter 214/2250] R1[63/300], Temp: 0.8951, Energy: -49.693226+0.019014j
[2025-09-03 18:08:34] [Iter 215/2250] R1[64/300], Temp: 0.8918, Energy: -49.710561-0.010798j
[2025-09-03 18:09:34] [Iter 216/2250] R1[65/300], Temp: 0.8886, Energy: -49.757244-0.001541j
[2025-09-03 18:10:34] [Iter 217/2250] R1[66/300], Temp: 0.8853, Energy: -49.787819+0.006962j
[2025-09-03 18:11:34] [Iter 218/2250] R1[67/300], Temp: 0.8819, Energy: -49.807007+0.038166j
[2025-09-03 18:12:35] [Iter 219/2250] R1[68/300], Temp: 0.8785, Energy: -49.879851-0.002061j
[2025-09-03 18:13:35] [Iter 220/2250] R1[69/300], Temp: 0.8751, Energy: -50.013025-0.031854j
[2025-09-03 18:14:35] [Iter 221/2250] R1[70/300], Temp: 0.8716, Energy: -50.103336+0.003588j
[2025-09-03 18:15:35] [Iter 222/2250] R1[71/300], Temp: 0.8680, Energy: -50.155931+0.008100j
[2025-09-03 18:16:35] [Iter 223/2250] R1[72/300], Temp: 0.8645, Energy: -50.165954-0.011581j
[2025-09-03 18:17:35] [Iter 224/2250] R1[73/300], Temp: 0.8609, Energy: -50.212483+0.005449j
[2025-09-03 18:18:36] [Iter 225/2250] R1[74/300], Temp: 0.8572, Energy: -50.293781+0.010008j
[2025-09-03 18:19:36] [Iter 226/2250] R1[75/300], Temp: 0.8536, Energy: -50.330723-0.016888j
[2025-09-03 18:20:36] [Iter 227/2250] R1[76/300], Temp: 0.8498, Energy: -50.355048+0.000918j
[2025-09-03 18:21:36] [Iter 228/2250] R1[77/300], Temp: 0.8461, Energy: -50.362261+0.002295j
[2025-09-03 18:22:36] [Iter 229/2250] R1[78/300], Temp: 0.8423, Energy: -50.385624-0.000363j
[2025-09-03 18:23:36] [Iter 230/2250] R1[79/300], Temp: 0.8384, Energy: -50.414765+0.008323j
[2025-09-03 18:24:37] [Iter 231/2250] R1[80/300], Temp: 0.8346, Energy: -50.458046-0.012235j
[2025-09-03 18:25:37] [Iter 232/2250] R1[81/300], Temp: 0.8307, Energy: -50.525621-0.001990j
[2025-09-03 18:26:37] [Iter 233/2250] R1[82/300], Temp: 0.8267, Energy: -50.563710+0.020782j
[2025-09-03 18:27:02] [Iter 234/2250] R1[83/300], Temp: 0.8227, Energy: -50.522520-0.015157j
[2025-09-03 18:27:23] [Iter 235/2250] R1[84/300], Temp: 0.8187, Energy: -50.534387+0.012118j
[2025-09-03 18:27:50] [Iter 236/2250] R1[85/300], Temp: 0.8147, Energy: -50.656930+0.005019j
[2025-09-03 18:28:20] [Iter 237/2250] R1[86/300], Temp: 0.8106, Energy: -50.680361-0.013369j
[2025-09-03 18:28:52] [Iter 238/2250] R1[87/300], Temp: 0.8065, Energy: -50.697039+0.006117j
[2025-09-03 18:29:52] [Iter 239/2250] R1[88/300], Temp: 0.8023, Energy: -50.718148+0.022003j
[2025-09-03 18:30:52] [Iter 240/2250] R1[89/300], Temp: 0.7981, Energy: -50.795342+0.018423j
[2025-09-03 18:31:52] [Iter 241/2250] R1[90/300], Temp: 0.7939, Energy: -50.776370-0.017690j
[2025-09-03 18:32:53] [Iter 242/2250] R1[91/300], Temp: 0.7896, Energy: -50.806812+0.005425j
[2025-09-03 18:33:53] [Iter 243/2250] R1[92/300], Temp: 0.7854, Energy: -50.775328+0.021568j
[2025-09-03 18:34:53] [Iter 244/2250] R1[93/300], Temp: 0.7810, Energy: -50.955077+0.014580j
[2025-09-03 18:35:53] [Iter 245/2250] R1[94/300], Temp: 0.7767, Energy: -50.967405+0.021499j
[2025-09-03 18:36:53] [Iter 246/2250] R1[95/300], Temp: 0.7723, Energy: -51.004673-0.014196j
[2025-09-03 18:37:53] [Iter 247/2250] R1[96/300], Temp: 0.7679, Energy: -50.986970-0.019260j
[2025-09-03 18:38:54] [Iter 248/2250] R1[97/300], Temp: 0.7635, Energy: -51.063440-0.001133j
[2025-09-03 18:39:54] [Iter 249/2250] R1[98/300], Temp: 0.7590, Energy: -51.094189+0.005050j
[2025-09-03 18:40:54] [Iter 250/2250] R1[99/300], Temp: 0.7545, Energy: -51.021693-0.002996j
[2025-09-03 18:40:54] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-09-03 18:41:54] [Iter 251/2250] R1[100/300], Temp: 0.7500, Energy: -51.089698+0.006333j
[2025-09-03 18:42:54] [Iter 252/2250] R1[101/300], Temp: 0.7455, Energy: -51.067662-0.012147j
[2025-09-03 18:43:55] [Iter 253/2250] R1[102/300], Temp: 0.7409, Energy: -51.164289+0.007860j
[2025-09-03 18:44:55] [Iter 254/2250] R1[103/300], Temp: 0.7363, Energy: -51.159726-0.011535j
[2025-09-03 18:45:55] [Iter 255/2250] R1[104/300], Temp: 0.7316, Energy: -51.107530+0.012153j
[2025-09-03 18:46:55] [Iter 256/2250] R1[105/300], Temp: 0.7270, Energy: -51.271445+0.001516j
[2025-09-03 18:47:55] [Iter 257/2250] R1[106/300], Temp: 0.7223, Energy: -51.286061-0.001652j
[2025-09-03 18:48:55] [Iter 258/2250] R1[107/300], Temp: 0.7176, Energy: -51.279498+0.005033j
[2025-09-03 18:49:56] [Iter 259/2250] R1[108/300], Temp: 0.7129, Energy: -51.304346+0.006313j
[2025-09-03 18:50:56] [Iter 260/2250] R1[109/300], Temp: 0.7081, Energy: -51.268098-0.011884j
[2025-09-03 18:51:56] [Iter 261/2250] R1[110/300], Temp: 0.7034, Energy: -51.270040+0.005439j
[2025-09-03 18:52:56] [Iter 262/2250] R1[111/300], Temp: 0.6986, Energy: -51.222168+0.012661j
[2025-09-03 18:53:56] [Iter 263/2250] R1[112/300], Temp: 0.6938, Energy: -51.265223+0.005477j
[2025-09-03 18:54:56] [Iter 264/2250] R1[113/300], Temp: 0.6889, Energy: -51.257751+0.024442j
[2025-09-03 18:55:57] [Iter 265/2250] R1[114/300], Temp: 0.6841, Energy: -51.283900+0.012270j
[2025-09-03 18:56:57] [Iter 266/2250] R1[115/300], Temp: 0.6792, Energy: -51.428380+0.004453j
[2025-09-03 18:57:57] [Iter 267/2250] R1[116/300], Temp: 0.6743, Energy: -51.422463+0.007668j
[2025-09-03 18:58:57] [Iter 268/2250] R1[117/300], Temp: 0.6694, Energy: -51.437585+0.002705j
[2025-09-03 18:59:57] [Iter 269/2250] R1[118/300], Temp: 0.6644, Energy: -51.439651+0.014978j
[2025-09-03 19:00:58] [Iter 270/2250] R1[119/300], Temp: 0.6595, Energy: -51.451376-0.003141j
[2025-09-03 19:01:58] [Iter 271/2250] R1[120/300], Temp: 0.6545, Energy: -51.509708-0.004253j
[2025-09-03 19:02:58] [Iter 272/2250] R1[121/300], Temp: 0.6495, Energy: -51.513939+0.005656j
[2025-09-03 19:03:58] [Iter 273/2250] R1[122/300], Temp: 0.6445, Energy: -51.474944+0.009073j
[2025-09-03 19:04:58] [Iter 274/2250] R1[123/300], Temp: 0.6395, Energy: -51.529458+0.015005j
[2025-09-03 19:05:58] [Iter 275/2250] R1[124/300], Temp: 0.6345, Energy: -51.492617-0.002034j
[2025-09-03 19:06:59] [Iter 276/2250] R1[125/300], Temp: 0.6294, Energy: -51.519819+0.007468j
[2025-09-03 19:07:59] [Iter 277/2250] R1[126/300], Temp: 0.6243, Energy: -51.508261-0.011167j
[2025-09-03 19:08:59] [Iter 278/2250] R1[127/300], Temp: 0.6193, Energy: -51.554168+0.003551j
[2025-09-03 19:09:59] [Iter 279/2250] R1[128/300], Temp: 0.6142, Energy: -51.561755-0.001373j
[2025-09-03 19:11:00] [Iter 280/2250] R1[129/300], Temp: 0.6091, Energy: -51.629010+0.001845j
[2025-09-03 19:12:00] [Iter 281/2250] R1[130/300], Temp: 0.6040, Energy: -51.599715+0.002345j
[2025-09-03 19:13:00] [Iter 282/2250] R1[131/300], Temp: 0.5988, Energy: -51.594532+0.008063j
[2025-09-03 19:14:00] [Iter 283/2250] R1[132/300], Temp: 0.5937, Energy: -51.520671+0.003697j
[2025-09-03 19:15:01] [Iter 284/2250] R1[133/300], Temp: 0.5885, Energy: -51.580976+0.003471j
[2025-09-03 19:16:01] [Iter 285/2250] R1[134/300], Temp: 0.5834, Energy: -51.555229+0.001304j
[2025-09-03 19:17:01] [Iter 286/2250] R1[135/300], Temp: 0.5782, Energy: -51.594869-0.006077j
[2025-09-03 19:18:01] [Iter 287/2250] R1[136/300], Temp: 0.5730, Energy: -51.524601-0.004826j
[2025-09-03 19:19:01] [Iter 288/2250] R1[137/300], Temp: 0.5679, Energy: -51.609994+0.001535j
[2025-09-03 19:20:01] [Iter 289/2250] R1[138/300], Temp: 0.5627, Energy: -51.657606+0.003277j
[2025-09-03 19:21:02] [Iter 290/2250] R1[139/300], Temp: 0.5575, Energy: -51.601975-0.005819j
[2025-09-03 19:22:02] [Iter 291/2250] R1[140/300], Temp: 0.5523, Energy: -51.542302+0.010138j
[2025-09-03 19:23:02] [Iter 292/2250] R1[141/300], Temp: 0.5471, Energy: -51.615526-0.006358j
[2025-09-03 19:24:02] [Iter 293/2250] R1[142/300], Temp: 0.5418, Energy: -51.667091-0.006485j
[2025-09-03 19:25:02] [Iter 294/2250] R1[143/300], Temp: 0.5366, Energy: -51.594711+0.002875j
[2025-09-03 19:26:03] [Iter 295/2250] R1[144/300], Temp: 0.5314, Energy: -51.620833-0.008358j
[2025-09-03 19:27:03] [Iter 296/2250] R1[145/300], Temp: 0.5262, Energy: -51.617448-0.009365j
[2025-09-03 19:28:03] [Iter 297/2250] R1[146/300], Temp: 0.5209, Energy: -51.602820+0.008119j
[2025-09-03 19:29:03] [Iter 298/2250] R1[147/300], Temp: 0.5157, Energy: -51.631926-0.001577j
[2025-09-03 19:30:03] [Iter 299/2250] R1[148/300], Temp: 0.5105, Energy: -51.619467-0.012864j
[2025-09-03 19:31:03] [Iter 300/2250] R1[149/300], Temp: 0.5052, Energy: -51.646549+0.007059j
[2025-09-03 19:32:03] [Iter 301/2250] R1[150/300], Temp: 0.5000, Energy: -51.619964-0.019132j
[2025-09-03 19:33:04] [Iter 302/2250] R1[151/300], Temp: 0.4948, Energy: -51.644287-0.014110j
[2025-09-03 19:34:04] [Iter 303/2250] R1[152/300], Temp: 0.4895, Energy: -51.663149-0.000885j
[2025-09-03 19:35:04] [Iter 304/2250] R1[153/300], Temp: 0.4843, Energy: -51.729582+0.005074j
[2025-09-03 19:36:04] [Iter 305/2250] R1[154/300], Temp: 0.4791, Energy: -51.690938+0.008027j
[2025-09-03 19:37:04] [Iter 306/2250] R1[155/300], Temp: 0.4738, Energy: -51.608915-0.001294j
[2025-09-03 19:38:04] [Iter 307/2250] R1[156/300], Temp: 0.4686, Energy: -51.624196-0.017506j
[2025-09-03 19:39:05] [Iter 308/2250] R1[157/300], Temp: 0.4634, Energy: -51.618848+0.006316j
[2025-09-03 19:40:05] [Iter 309/2250] R1[158/300], Temp: 0.4582, Energy: -51.678296+0.017655j
[2025-09-03 19:41:05] [Iter 310/2250] R1[159/300], Temp: 0.4529, Energy: -51.619967+0.005949j
[2025-09-03 19:42:05] [Iter 311/2250] R1[160/300], Temp: 0.4477, Energy: -51.698623-0.025747j
[2025-09-03 19:43:05] [Iter 312/2250] R1[161/300], Temp: 0.4425, Energy: -51.704952+0.010197j
[2025-09-03 19:44:06] [Iter 313/2250] R1[162/300], Temp: 0.4373, Energy: -51.708641-0.004965j
[2025-09-03 19:45:06] [Iter 314/2250] R1[163/300], Temp: 0.4321, Energy: -51.781177+0.001350j
[2025-09-03 19:46:06] [Iter 315/2250] R1[164/300], Temp: 0.4270, Energy: -51.748909+0.001073j
[2025-09-03 19:47:06] [Iter 316/2250] R1[165/300], Temp: 0.4218, Energy: -51.783288-0.001590j
[2025-09-03 19:48:06] [Iter 317/2250] R1[166/300], Temp: 0.4166, Energy: -51.700031-0.001760j
[2025-09-03 19:49:06] [Iter 318/2250] R1[167/300], Temp: 0.4115, Energy: -51.716572+0.017494j
[2025-09-03 19:50:07] [Iter 319/2250] R1[168/300], Temp: 0.4063, Energy: -51.727863+0.000186j
[2025-09-03 19:51:07] [Iter 320/2250] R1[169/300], Temp: 0.4012, Energy: -51.742815+0.002692j
[2025-09-03 19:52:07] [Iter 321/2250] R1[170/300], Temp: 0.3960, Energy: -51.746932-0.003589j
[2025-09-03 19:53:07] [Iter 322/2250] R1[171/300], Temp: 0.3909, Energy: -51.755672+0.020048j
[2025-09-03 19:54:07] [Iter 323/2250] R1[172/300], Temp: 0.3858, Energy: -51.812531+0.003800j
[2025-09-03 19:55:07] [Iter 324/2250] R1[173/300], Temp: 0.3807, Energy: -51.739259+0.001142j
[2025-09-03 19:56:08] [Iter 325/2250] R1[174/300], Temp: 0.3757, Energy: -51.744458-0.014146j
[2025-09-03 19:57:08] [Iter 326/2250] R1[175/300], Temp: 0.3706, Energy: -51.687584-0.011112j
[2025-09-03 19:58:08] [Iter 327/2250] R1[176/300], Temp: 0.3655, Energy: -51.743985+0.006234j
[2025-09-03 19:59:08] [Iter 328/2250] R1[177/300], Temp: 0.3605, Energy: -51.712556-0.001395j
[2025-09-03 20:00:08] [Iter 329/2250] R1[178/300], Temp: 0.3555, Energy: -51.789509-0.006360j
[2025-09-03 20:01:08] [Iter 330/2250] R1[179/300], Temp: 0.3505, Energy: -51.733586-0.002207j
[2025-09-03 20:02:08] [Iter 331/2250] R1[180/300], Temp: 0.3455, Energy: -51.718477-0.012207j
[2025-09-03 20:03:09] [Iter 332/2250] R1[181/300], Temp: 0.3405, Energy: -51.768759+0.002674j
[2025-09-03 20:04:09] [Iter 333/2250] R1[182/300], Temp: 0.3356, Energy: -51.786110+0.015219j
[2025-09-03 20:05:09] [Iter 334/2250] R1[183/300], Temp: 0.3306, Energy: -51.730113-0.010972j
[2025-09-03 20:06:09] [Iter 335/2250] R1[184/300], Temp: 0.3257, Energy: -51.770665+0.006259j
[2025-09-03 20:07:09] [Iter 336/2250] R1[185/300], Temp: 0.3208, Energy: -51.774411+0.001804j
[2025-09-03 20:08:09] [Iter 337/2250] R1[186/300], Temp: 0.3159, Energy: -51.778178-0.005725j
[2025-09-03 20:09:10] [Iter 338/2250] R1[187/300], Temp: 0.3111, Energy: -51.731447-0.000083j
[2025-09-03 20:10:10] [Iter 339/2250] R1[188/300], Temp: 0.3062, Energy: -51.810819+0.007947j
[2025-09-03 20:11:10] [Iter 340/2250] R1[189/300], Temp: 0.3014, Energy: -51.818272-0.003909j
[2025-09-03 20:12:10] [Iter 341/2250] R1[190/300], Temp: 0.2966, Energy: -51.841594-0.013392j
[2025-09-03 20:13:10] [Iter 342/2250] R1[191/300], Temp: 0.2919, Energy: -51.793610+0.003693j
[2025-09-03 20:14:10] [Iter 343/2250] R1[192/300], Temp: 0.2871, Energy: -51.824865-0.001119j
[2025-09-03 20:15:11] [Iter 344/2250] R1[193/300], Temp: 0.2824, Energy: -51.769849+0.004365j
[2025-09-03 20:16:11] [Iter 345/2250] R1[194/300], Temp: 0.2777, Energy: -51.793276-0.006120j
[2025-09-03 20:17:11] [Iter 346/2250] R1[195/300], Temp: 0.2730, Energy: -51.832113+0.003715j
[2025-09-03 20:18:11] [Iter 347/2250] R1[196/300], Temp: 0.2684, Energy: -51.885878+0.010918j
[2025-09-03 20:19:11] [Iter 348/2250] R1[197/300], Temp: 0.2637, Energy: -51.896466+0.005460j
[2025-09-03 20:20:11] [Iter 349/2250] R1[198/300], Temp: 0.2591, Energy: -51.835808-0.015077j
[2025-09-03 20:21:12] [Iter 350/2250] R1[199/300], Temp: 0.2545, Energy: -51.822099-0.001850j
[2025-09-03 20:22:12] [Iter 351/2250] R1[200/300], Temp: 0.2500, Energy: -51.878956+0.003583j
[2025-09-03 20:23:12] [Iter 352/2250] R1[201/300], Temp: 0.2455, Energy: -51.853944-0.012076j
[2025-09-03 20:24:12] [Iter 353/2250] R1[202/300], Temp: 0.2410, Energy: -51.876230-0.003305j
[2025-09-03 20:25:12] [Iter 354/2250] R1[203/300], Temp: 0.2365, Energy: -51.875757-0.000285j
[2025-09-03 20:26:12] [Iter 355/2250] R1[204/300], Temp: 0.2321, Energy: -51.757199-0.002515j
[2025-09-03 20:27:13] [Iter 356/2250] R1[205/300], Temp: 0.2277, Energy: -51.825028+0.006428j
[2025-09-03 20:28:13] [Iter 357/2250] R1[206/300], Temp: 0.2233, Energy: -51.761355-0.009503j
[2025-09-03 20:29:13] [Iter 358/2250] R1[207/300], Temp: 0.2190, Energy: -51.811085-0.007890j
[2025-09-03 20:30:13] [Iter 359/2250] R1[208/300], Temp: 0.2146, Energy: -51.792614-0.000729j
[2025-09-03 20:31:13] [Iter 360/2250] R1[209/300], Temp: 0.2104, Energy: -51.845274-0.002408j
[2025-09-03 20:32:13] [Iter 361/2250] R1[210/300], Temp: 0.2061, Energy: -51.821572+0.004966j
[2025-09-03 20:33:14] [Iter 362/2250] R1[211/300], Temp: 0.2019, Energy: -51.904375+0.000673j
[2025-09-03 20:34:14] [Iter 363/2250] R1[212/300], Temp: 0.1977, Energy: -51.841119+0.003774j
[2025-09-03 20:35:14] [Iter 364/2250] R1[213/300], Temp: 0.1935, Energy: -51.885177+0.011180j
[2025-09-03 20:36:14] [Iter 365/2250] R1[214/300], Temp: 0.1894, Energy: -51.887020-0.001303j
[2025-09-03 20:37:14] [Iter 366/2250] R1[215/300], Temp: 0.1853, Energy: -51.866318+0.006542j
[2025-09-03 20:38:14] [Iter 367/2250] R1[216/300], Temp: 0.1813, Energy: -51.846365+0.006315j
[2025-09-03 20:39:15] [Iter 368/2250] R1[217/300], Temp: 0.1773, Energy: -51.846221+0.003156j
[2025-09-03 20:40:15] [Iter 369/2250] R1[218/300], Temp: 0.1733, Energy: -51.881756+0.004308j
[2025-09-03 20:41:15] [Iter 370/2250] R1[219/300], Temp: 0.1693, Energy: -51.864232-0.009121j
[2025-09-03 20:42:15] [Iter 371/2250] R1[220/300], Temp: 0.1654, Energy: -51.863417-0.008342j
[2025-09-03 20:43:15] [Iter 372/2250] R1[221/300], Temp: 0.1616, Energy: -51.925250-0.010930j
[2025-09-03 20:44:15] [Iter 373/2250] R1[222/300], Temp: 0.1577, Energy: -51.872946+0.002592j
[2025-09-03 20:45:16] [Iter 374/2250] R1[223/300], Temp: 0.1539, Energy: -51.827940-0.013789j
[2025-09-03 20:46:16] [Iter 375/2250] R1[224/300], Temp: 0.1502, Energy: -51.848798-0.000531j
[2025-09-03 20:47:16] [Iter 376/2250] R1[225/300], Temp: 0.1464, Energy: -51.884356-0.011176j
[2025-09-03 20:48:16] [Iter 377/2250] R1[226/300], Temp: 0.1428, Energy: -51.838056+0.000001j
[2025-09-03 20:49:16] [Iter 378/2250] R1[227/300], Temp: 0.1391, Energy: -51.861779-0.010519j
[2025-09-03 20:50:16] [Iter 379/2250] R1[228/300], Temp: 0.1355, Energy: -51.884259-0.007043j
[2025-09-03 20:51:16] [Iter 380/2250] R1[229/300], Temp: 0.1320, Energy: -51.836103-0.004644j
[2025-09-03 20:52:17] [Iter 381/2250] R1[230/300], Temp: 0.1284, Energy: -51.875753+0.004337j
[2025-09-03 20:53:17] [Iter 382/2250] R1[231/300], Temp: 0.1249, Energy: -51.846247-0.004793j
[2025-09-03 20:54:17] [Iter 383/2250] R1[232/300], Temp: 0.1215, Energy: -51.905266+0.006962j
[2025-09-03 20:55:17] [Iter 384/2250] R1[233/300], Temp: 0.1181, Energy: -51.760865-0.000181j
[2025-09-03 20:56:17] [Iter 385/2250] R1[234/300], Temp: 0.1147, Energy: -51.846373-0.009895j
[2025-09-03 20:57:17] [Iter 386/2250] R1[235/300], Temp: 0.1114, Energy: -51.848160-0.006412j
[2025-09-03 20:58:18] [Iter 387/2250] R1[236/300], Temp: 0.1082, Energy: -51.808503-0.003215j
[2025-09-03 20:59:18] [Iter 388/2250] R1[237/300], Temp: 0.1049, Energy: -51.831231+0.004719j
[2025-09-03 21:00:18] [Iter 389/2250] R1[238/300], Temp: 0.1017, Energy: -51.856725+0.003747j
[2025-09-03 21:01:18] [Iter 390/2250] R1[239/300], Temp: 0.0986, Energy: -51.851808+0.009422j
[2025-09-03 21:02:18] [Iter 391/2250] R1[240/300], Temp: 0.0955, Energy: -51.852755+0.001577j
[2025-09-03 21:03:18] [Iter 392/2250] R1[241/300], Temp: 0.0924, Energy: -51.938107-0.000157j
[2025-09-03 21:04:19] [Iter 393/2250] R1[242/300], Temp: 0.0894, Energy: -51.875358+0.000917j
[2025-09-03 21:05:19] [Iter 394/2250] R1[243/300], Temp: 0.0865, Energy: -51.896991-0.000597j
[2025-09-03 21:06:19] [Iter 395/2250] R1[244/300], Temp: 0.0835, Energy: -51.861239-0.005825j
[2025-09-03 21:07:19] [Iter 396/2250] R1[245/300], Temp: 0.0807, Energy: -51.852970-0.000493j
[2025-09-03 21:08:19] [Iter 397/2250] R1[246/300], Temp: 0.0778, Energy: -51.855663+0.005087j
[2025-09-03 21:09:19] [Iter 398/2250] R1[247/300], Temp: 0.0751, Energy: -51.889887+0.003328j
[2025-09-03 21:10:20] [Iter 399/2250] R1[248/300], Temp: 0.0723, Energy: -51.855840-0.004440j
[2025-09-03 21:11:20] [Iter 400/2250] R1[249/300], Temp: 0.0696, Energy: -51.930675-0.006747j
[2025-09-03 21:12:20] [Iter 401/2250] R1[250/300], Temp: 0.0670, Energy: -51.939075+0.004591j
[2025-09-03 21:13:20] [Iter 402/2250] R1[251/300], Temp: 0.0644, Energy: -51.938057+0.000703j
[2025-09-03 21:14:20] [Iter 403/2250] R1[252/300], Temp: 0.0618, Energy: -52.021176+0.002094j
[2025-09-03 21:15:20] [Iter 404/2250] R1[253/300], Temp: 0.0593, Energy: -52.013578-0.009070j
[2025-09-03 21:16:21] [Iter 405/2250] R1[254/300], Temp: 0.0569, Energy: -51.927605+0.004025j
[2025-09-03 21:17:21] [Iter 406/2250] R1[255/300], Temp: 0.0545, Energy: -51.926324+0.002968j
[2025-09-03 21:18:21] [Iter 407/2250] R1[256/300], Temp: 0.0521, Energy: -51.920965-0.011976j
[2025-09-03 21:19:21] [Iter 408/2250] R1[257/300], Temp: 0.0498, Energy: -51.955533+0.003902j
[2025-09-03 21:20:21] [Iter 409/2250] R1[258/300], Temp: 0.0476, Energy: -51.841745-0.000800j
[2025-09-03 21:21:21] [Iter 410/2250] R1[259/300], Temp: 0.0454, Energy: -51.895162+0.003301j
[2025-09-03 21:22:22] [Iter 411/2250] R1[260/300], Temp: 0.0432, Energy: -51.934410-0.001796j
[2025-09-03 21:23:22] [Iter 412/2250] R1[261/300], Temp: 0.0411, Energy: -51.988135-0.007080j
[2025-09-03 21:24:22] [Iter 413/2250] R1[262/300], Temp: 0.0391, Energy: -51.974937+0.004867j
[2025-09-03 21:25:22] [Iter 414/2250] R1[263/300], Temp: 0.0371, Energy: -51.953831-0.001400j
[2025-09-03 21:26:22] [Iter 415/2250] R1[264/300], Temp: 0.0351, Energy: -51.916337-0.003754j
[2025-09-03 21:27:22] [Iter 416/2250] R1[265/300], Temp: 0.0332, Energy: -51.905420-0.004500j
[2025-09-03 21:28:22] [Iter 417/2250] R1[266/300], Temp: 0.0314, Energy: -51.940570+0.004600j
[2025-09-03 21:29:23] [Iter 418/2250] R1[267/300], Temp: 0.0296, Energy: -51.950305+0.008017j
[2025-09-03 21:30:23] [Iter 419/2250] R1[268/300], Temp: 0.0278, Energy: -51.960451+0.002940j
[2025-09-03 21:31:23] [Iter 420/2250] R1[269/300], Temp: 0.0261, Energy: -51.975402+0.004876j
[2025-09-03 21:32:23] [Iter 421/2250] R1[270/300], Temp: 0.0245, Energy: -52.020200-0.005234j
[2025-09-03 21:33:23] [Iter 422/2250] R1[271/300], Temp: 0.0229, Energy: -51.970924-0.000658j
[2025-09-03 21:34:23] [Iter 423/2250] R1[272/300], Temp: 0.0213, Energy: -51.923576+0.001323j
[2025-09-03 21:35:23] [Iter 424/2250] R1[273/300], Temp: 0.0199, Energy: -52.021251+0.002770j
[2025-09-03 21:36:24] [Iter 425/2250] R1[274/300], Temp: 0.0184, Energy: -51.923605+0.007302j
[2025-09-03 21:37:24] [Iter 426/2250] R1[275/300], Temp: 0.0170, Energy: -51.975810+0.005911j
[2025-09-03 21:38:24] [Iter 427/2250] R1[276/300], Temp: 0.0157, Energy: -51.953246-0.000794j
[2025-09-03 21:39:24] [Iter 428/2250] R1[277/300], Temp: 0.0144, Energy: -51.938218-0.000912j
[2025-09-03 21:40:24] [Iter 429/2250] R1[278/300], Temp: 0.0132, Energy: -51.892691-0.001236j
[2025-09-03 21:41:24] [Iter 430/2250] R1[279/300], Temp: 0.0120, Energy: -51.922647+0.007099j
[2025-09-03 21:42:25] [Iter 431/2250] R1[280/300], Temp: 0.0109, Energy: -51.969297+0.005032j
[2025-09-03 21:43:25] [Iter 432/2250] R1[281/300], Temp: 0.0099, Energy: -51.883961+0.002647j
[2025-09-03 21:44:25] [Iter 433/2250] R1[282/300], Temp: 0.0089, Energy: -51.940939-0.001922j
[2025-09-03 21:45:25] [Iter 434/2250] R1[283/300], Temp: 0.0079, Energy: -51.978605+0.002614j
[2025-09-03 21:46:25] [Iter 435/2250] R1[284/300], Temp: 0.0070, Energy: -51.972913-0.004074j
[2025-09-03 21:47:25] [Iter 436/2250] R1[285/300], Temp: 0.0062, Energy: -51.978948+0.004442j
[2025-09-03 21:48:26] [Iter 437/2250] R1[286/300], Temp: 0.0054, Energy: -51.971277-0.002101j
[2025-09-03 21:49:26] [Iter 438/2250] R1[287/300], Temp: 0.0046, Energy: -52.043664-0.005738j
[2025-09-03 21:50:26] [Iter 439/2250] R1[288/300], Temp: 0.0039, Energy: -52.033626-0.004589j
[2025-09-03 21:51:26] [Iter 440/2250] R1[289/300], Temp: 0.0033, Energy: -52.032104+0.001519j
[2025-09-03 21:52:26] [Iter 441/2250] R1[290/300], Temp: 0.0027, Energy: -52.041380-0.005252j
[2025-09-03 21:53:26] [Iter 442/2250] R1[291/300], Temp: 0.0022, Energy: -52.045064+0.005850j
[2025-09-03 21:54:27] [Iter 443/2250] R1[292/300], Temp: 0.0018, Energy: -52.017773+0.004999j
[2025-09-03 21:55:27] [Iter 444/2250] R1[293/300], Temp: 0.0013, Energy: -51.980879-0.001145j
[2025-09-03 21:56:27] [Iter 445/2250] R1[294/300], Temp: 0.0010, Energy: -51.934318+0.001989j
[2025-09-03 21:57:27] [Iter 446/2250] R1[295/300], Temp: 0.0007, Energy: -52.000354+0.006764j
[2025-09-03 21:58:27] [Iter 447/2250] R1[296/300], Temp: 0.0004, Energy: -51.947669-0.002575j
[2025-09-03 21:59:27] [Iter 448/2250] R1[297/300], Temp: 0.0002, Energy: -51.887542-0.003149j
[2025-09-03 22:00:27] [Iter 449/2250] R1[298/300], Temp: 0.0001, Energy: -51.947403+0.001001j
[2025-09-03 22:01:27] [Iter 450/2250] R1[299/300], Temp: 0.0000, Energy: -51.937933-0.004536j
[2025-09-03 22:01:27] RESTART #2 | Period: 600
[2025-09-03 22:02:28] [Iter 451/2250] R2[0/600], Temp: 1.0000, Energy: -51.913960-0.006579j
[2025-09-03 22:03:28] [Iter 452/2250] R2[1/600], Temp: 1.0000, Energy: -51.992535-0.005602j
[2025-09-03 22:04:28] [Iter 453/2250] R2[2/600], Temp: 1.0000, Energy: -51.941365+0.003386j
[2025-09-03 22:05:28] [Iter 454/2250] R2[3/600], Temp: 0.9999, Energy: -51.939879+0.001693j
[2025-09-03 22:06:28] [Iter 455/2250] R2[4/600], Temp: 0.9999, Energy: -51.975366+0.001538j
[2025-09-03 22:07:28] [Iter 456/2250] R2[5/600], Temp: 0.9998, Energy: -51.958140-0.002709j
[2025-09-03 22:08:28] [Iter 457/2250] R2[6/600], Temp: 0.9998, Energy: -51.975717-0.002509j
[2025-09-03 22:09:29] [Iter 458/2250] R2[7/600], Temp: 0.9997, Energy: -52.020806-0.000017j
[2025-09-03 22:10:29] [Iter 459/2250] R2[8/600], Temp: 0.9996, Energy: -51.998487+0.000966j
[2025-09-03 22:11:29] [Iter 460/2250] R2[9/600], Temp: 0.9994, Energy: -52.043649+0.004177j
[2025-09-03 22:12:29] [Iter 461/2250] R2[10/600], Temp: 0.9993, Energy: -52.022795-0.002608j
[2025-09-03 22:13:29] [Iter 462/2250] R2[11/600], Temp: 0.9992, Energy: -52.012582-0.003541j
[2025-09-03 22:14:29] [Iter 463/2250] R2[12/600], Temp: 0.9990, Energy: -52.029876-0.001648j
[2025-09-03 22:15:30] [Iter 464/2250] R2[13/600], Temp: 0.9988, Energy: -51.972884-0.004311j
[2025-09-03 22:16:30] [Iter 465/2250] R2[14/600], Temp: 0.9987, Energy: -51.993126+0.003000j
[2025-09-03 22:17:30] [Iter 466/2250] R2[15/600], Temp: 0.9985, Energy: -52.014586-0.000394j
[2025-09-03 22:18:28] [Iter 467/2250] R2[16/600], Temp: 0.9982, Energy: -51.992204-0.001373j
[2025-09-03 22:19:08] [Iter 468/2250] R2[17/600], Temp: 0.9980, Energy: -51.951299-0.000996j
[2025-09-03 22:19:48] [Iter 469/2250] R2[18/600], Temp: 0.9978, Energy: -51.895501-0.001083j
[2025-09-03 22:20:08] [Iter 470/2250] R2[19/600], Temp: 0.9975, Energy: -51.948621-0.003878j
[2025-09-03 22:20:28] [Iter 471/2250] R2[20/600], Temp: 0.9973, Energy: -51.941181+0.003346j
[2025-09-03 22:20:56] [Iter 472/2250] R2[21/600], Temp: 0.9970, Energy: -51.993338+0.003252j
[2025-09-03 22:21:26] [Iter 473/2250] R2[22/600], Temp: 0.9967, Energy: -51.981858-0.000510j
[2025-09-03 22:22:00] [Iter 474/2250] R2[23/600], Temp: 0.9964, Energy: -51.971356+0.002374j
[2025-09-03 22:23:00] [Iter 475/2250] R2[24/600], Temp: 0.9961, Energy: -51.945356+0.002241j
[2025-09-03 22:24:00] [Iter 476/2250] R2[25/600], Temp: 0.9957, Energy: -51.967487+0.003003j
[2025-09-03 22:25:00] [Iter 477/2250] R2[26/600], Temp: 0.9954, Energy: -51.959955+0.000593j
[2025-09-03 22:26:00] [Iter 478/2250] R2[27/600], Temp: 0.9950, Energy: -51.991750-0.000342j
[2025-09-03 22:27:00] [Iter 479/2250] R2[28/600], Temp: 0.9946, Energy: -52.016816-0.007847j
[2025-09-03 22:28:00] [Iter 480/2250] R2[29/600], Temp: 0.9942, Energy: -51.957880-0.000032j
[2025-09-03 22:29:01] [Iter 481/2250] R2[30/600], Temp: 0.9938, Energy: -51.988509-0.007968j
[2025-09-03 22:30:01] [Iter 482/2250] R2[31/600], Temp: 0.9934, Energy: -52.009126-0.003270j
[2025-09-03 22:31:01] [Iter 483/2250] R2[32/600], Temp: 0.9930, Energy: -51.988550+0.000997j
[2025-09-03 22:32:01] [Iter 484/2250] R2[33/600], Temp: 0.9926, Energy: -51.934904-0.000592j
[2025-09-03 22:33:02] [Iter 485/2250] R2[34/600], Temp: 0.9921, Energy: -51.948392-0.000997j
[2025-09-03 22:34:02] [Iter 486/2250] R2[35/600], Temp: 0.9916, Energy: -51.949291+0.000581j
[2025-09-03 22:35:02] [Iter 487/2250] R2[36/600], Temp: 0.9911, Energy: -52.019539+0.004883j
[2025-09-03 22:36:03] [Iter 488/2250] R2[37/600], Temp: 0.9906, Energy: -52.030417+0.001813j
[2025-09-03 22:37:03] [Iter 489/2250] R2[38/600], Temp: 0.9901, Energy: -52.034695+0.004134j
[2025-09-03 22:38:03] [Iter 490/2250] R2[39/600], Temp: 0.9896, Energy: -52.029426+0.002223j
[2025-09-03 22:39:03] [Iter 491/2250] R2[40/600], Temp: 0.9891, Energy: -52.042421+0.002809j
[2025-09-03 22:40:04] [Iter 492/2250] R2[41/600], Temp: 0.9885, Energy: -52.029685-0.002798j
[2025-09-03 22:41:04] [Iter 493/2250] R2[42/600], Temp: 0.9880, Energy: -51.930491+0.006689j
[2025-09-03 22:42:04] [Iter 494/2250] R2[43/600], Temp: 0.9874, Energy: -51.983395+0.001610j
[2025-09-03 22:43:04] [Iter 495/2250] R2[44/600], Temp: 0.9868, Energy: -52.037002-0.000937j
[2025-09-03 22:44:05] [Iter 496/2250] R2[45/600], Temp: 0.9862, Energy: -52.002007-0.000521j
[2025-09-03 22:45:05] [Iter 497/2250] R2[46/600], Temp: 0.9856, Energy: -52.018433-0.000066j
[2025-09-03 22:46:05] [Iter 498/2250] R2[47/600], Temp: 0.9849, Energy: -52.058615+0.003974j
[2025-09-03 22:47:05] [Iter 499/2250] R2[48/600], Temp: 0.9843, Energy: -52.078546-0.004911j
[2025-09-03 22:48:05] [Iter 500/2250] R2[49/600], Temp: 0.9836, Energy: -52.049877-0.000819j
[2025-09-03 22:48:05] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-09-03 22:49:05] [Iter 501/2250] R2[50/600], Temp: 0.9830, Energy: -52.021842-0.001853j
[2025-09-03 22:50:06] [Iter 502/2250] R2[51/600], Temp: 0.9823, Energy: -52.093152-0.001614j
[2025-09-03 22:51:06] [Iter 503/2250] R2[52/600], Temp: 0.9816, Energy: -52.014183+0.002792j
[2025-09-03 22:52:06] [Iter 504/2250] R2[53/600], Temp: 0.9809, Energy: -51.991518+0.000543j
[2025-09-03 22:53:06] [Iter 505/2250] R2[54/600], Temp: 0.9801, Energy: -52.033638-0.001680j
[2025-09-03 22:54:06] [Iter 506/2250] R2[55/600], Temp: 0.9794, Energy: -52.005461-0.000922j
[2025-09-03 22:55:07] [Iter 507/2250] R2[56/600], Temp: 0.9787, Energy: -51.976856-0.000094j
[2025-09-03 22:56:07] [Iter 508/2250] R2[57/600], Temp: 0.9779, Energy: -52.053794+0.005119j
[2025-09-03 22:57:07] [Iter 509/2250] R2[58/600], Temp: 0.9771, Energy: -51.943946-0.000988j
[2025-09-03 22:58:07] [Iter 510/2250] R2[59/600], Temp: 0.9763, Energy: -51.945967+0.001500j
[2025-09-03 22:59:08] [Iter 511/2250] R2[60/600], Temp: 0.9755, Energy: -51.985028+0.000502j
[2025-09-03 23:00:08] [Iter 512/2250] R2[61/600], Temp: 0.9747, Energy: -51.986704-0.004420j
[2025-09-03 23:01:08] [Iter 513/2250] R2[62/600], Temp: 0.9739, Energy: -51.998144-0.000873j
[2025-09-03 23:02:08] [Iter 514/2250] R2[63/600], Temp: 0.9730, Energy: -52.003820+0.002765j
[2025-09-03 23:03:09] [Iter 515/2250] R2[64/600], Temp: 0.9722, Energy: -52.015011+0.004801j
[2025-09-03 23:04:09] [Iter 516/2250] R2[65/600], Temp: 0.9713, Energy: -52.050453+0.003002j
[2025-09-03 23:05:09] [Iter 517/2250] R2[66/600], Temp: 0.9704, Energy: -52.032031-0.003204j
[2025-09-03 23:06:09] [Iter 518/2250] R2[67/600], Temp: 0.9695, Energy: -52.039367+0.001986j
[2025-09-03 23:07:10] [Iter 519/2250] R2[68/600], Temp: 0.9686, Energy: -52.019623-0.003713j
[2025-09-03 23:08:10] [Iter 520/2250] R2[69/600], Temp: 0.9677, Energy: -51.967629+0.001163j
[2025-09-03 23:09:10] [Iter 521/2250] R2[70/600], Temp: 0.9668, Energy: -52.035857-0.002488j
[2025-09-03 23:10:10] [Iter 522/2250] R2[71/600], Temp: 0.9658, Energy: -52.066845-0.000340j
[2025-09-03 23:11:11] [Iter 523/2250] R2[72/600], Temp: 0.9649, Energy: -52.020631+0.003549j
[2025-09-03 23:12:11] [Iter 524/2250] R2[73/600], Temp: 0.9639, Energy: -51.969727+0.001137j
[2025-09-03 23:13:11] [Iter 525/2250] R2[74/600], Temp: 0.9629, Energy: -52.014167+0.001114j
[2025-09-03 23:14:11] [Iter 526/2250] R2[75/600], Temp: 0.9619, Energy: -52.063852-0.005049j
[2025-09-03 23:15:12] [Iter 527/2250] R2[76/600], Temp: 0.9609, Energy: -52.066050+0.001186j
[2025-09-03 23:16:12] [Iter 528/2250] R2[77/600], Temp: 0.9599, Energy: -52.096810+0.003720j
[2025-09-03 23:17:12] [Iter 529/2250] R2[78/600], Temp: 0.9589, Energy: -52.089350-0.001124j
[2025-09-03 23:18:12] [Iter 530/2250] R2[79/600], Temp: 0.9578, Energy: -52.105505+0.003039j
[2025-09-03 23:19:13] [Iter 531/2250] R2[80/600], Temp: 0.9568, Energy: -52.079687-0.001493j
[2025-09-03 23:20:13] [Iter 532/2250] R2[81/600], Temp: 0.9557, Energy: -52.060251-0.000849j
[2025-09-03 23:21:13] [Iter 533/2250] R2[82/600], Temp: 0.9546, Energy: -52.051118+0.001437j
[2025-09-03 23:22:13] [Iter 534/2250] R2[83/600], Temp: 0.9535, Energy: -52.092317-0.009592j
[2025-09-03 23:23:14] [Iter 535/2250] R2[84/600], Temp: 0.9524, Energy: -52.022789+0.002108j
[2025-09-03 23:24:14] [Iter 536/2250] R2[85/600], Temp: 0.9513, Energy: -52.045107-0.000498j
[2025-09-03 23:25:14] [Iter 537/2250] R2[86/600], Temp: 0.9502, Energy: -52.031380-0.002008j
[2025-09-03 23:26:15] [Iter 538/2250] R2[87/600], Temp: 0.9490, Energy: -52.046629-0.004059j
[2025-09-03 23:27:15] [Iter 539/2250] R2[88/600], Temp: 0.9479, Energy: -52.085234-0.000743j
[2025-09-03 23:28:15] [Iter 540/2250] R2[89/600], Temp: 0.9467, Energy: -52.080972-0.000518j
[2025-09-03 23:29:15] [Iter 541/2250] R2[90/600], Temp: 0.9455, Energy: -52.023572+0.002092j
[2025-09-03 23:30:16] [Iter 542/2250] R2[91/600], Temp: 0.9443, Energy: -52.121627-0.001316j
[2025-09-03 23:31:16] [Iter 543/2250] R2[92/600], Temp: 0.9431, Energy: -52.054088+0.005073j
[2025-09-03 23:32:16] [Iter 544/2250] R2[93/600], Temp: 0.9419, Energy: -52.102184-0.001135j
[2025-09-03 23:33:17] [Iter 545/2250] R2[94/600], Temp: 0.9407, Energy: -52.081900+0.005055j
[2025-09-03 23:34:17] [Iter 546/2250] R2[95/600], Temp: 0.9394, Energy: -52.039892+0.003291j
[2025-09-03 23:35:17] [Iter 547/2250] R2[96/600], Temp: 0.9382, Energy: -52.088390+0.000413j
[2025-09-03 23:36:17] [Iter 548/2250] R2[97/600], Temp: 0.9369, Energy: -52.080483-0.000241j
[2025-09-03 23:37:18] [Iter 549/2250] R2[98/600], Temp: 0.9356, Energy: -52.050268-0.003012j
[2025-09-03 23:38:18] [Iter 550/2250] R2[99/600], Temp: 0.9343, Energy: -52.077331-0.001802j
[2025-09-03 23:39:18] [Iter 551/2250] R2[100/600], Temp: 0.9330, Energy: -52.063907+0.000410j
[2025-09-03 23:40:18] [Iter 552/2250] R2[101/600], Temp: 0.9317, Energy: -52.053677-0.005113j
[2025-09-03 23:41:19] [Iter 553/2250] R2[102/600], Temp: 0.9304, Energy: -52.042878+0.002286j
[2025-09-03 23:42:19] [Iter 554/2250] R2[103/600], Temp: 0.9290, Energy: -52.041589+0.001519j
[2025-09-03 23:43:19] [Iter 555/2250] R2[104/600], Temp: 0.9277, Energy: -52.088405-0.001145j
[2025-09-03 23:44:19] [Iter 556/2250] R2[105/600], Temp: 0.9263, Energy: -52.053373-0.000382j
[2025-09-03 23:45:20] [Iter 557/2250] R2[106/600], Temp: 0.9249, Energy: -52.051022-0.003712j
[2025-09-03 23:46:20] [Iter 558/2250] R2[107/600], Temp: 0.9236, Energy: -52.095768-0.002832j
[2025-09-03 23:47:20] [Iter 559/2250] R2[108/600], Temp: 0.9222, Energy: -52.083804-0.004621j
[2025-09-03 23:48:20] [Iter 560/2250] R2[109/600], Temp: 0.9208, Energy: -52.048585+0.000773j
[2025-09-03 23:49:21] [Iter 561/2250] R2[110/600], Temp: 0.9193, Energy: -52.110714+0.000460j
[2025-09-03 23:50:21] [Iter 562/2250] R2[111/600], Temp: 0.9179, Energy: -52.058152+0.001536j
[2025-09-03 23:51:21] [Iter 563/2250] R2[112/600], Temp: 0.9165, Energy: -52.084945-0.001397j
[2025-09-03 23:52:22] [Iter 564/2250] R2[113/600], Temp: 0.9150, Energy: -52.091114-0.000754j
[2025-09-03 23:53:22] [Iter 565/2250] R2[114/600], Temp: 0.9135, Energy: -52.053112+0.003429j
[2025-09-03 23:54:22] [Iter 566/2250] R2[115/600], Temp: 0.9121, Energy: -51.983267+0.000970j
[2025-09-03 23:55:22] [Iter 567/2250] R2[116/600], Temp: 0.9106, Energy: -52.057346-0.000614j
[2025-09-03 23:56:23] [Iter 568/2250] R2[117/600], Temp: 0.9091, Energy: -52.065784-0.002237j
[2025-09-03 23:57:23] [Iter 569/2250] R2[118/600], Temp: 0.9076, Energy: -52.099943+0.002838j
[2025-09-03 23:58:23] [Iter 570/2250] R2[119/600], Temp: 0.9060, Energy: -52.117149-0.004547j
[2025-09-03 23:59:23] [Iter 571/2250] R2[120/600], Temp: 0.9045, Energy: -52.143295-0.001582j
[2025-09-04 00:00:24] [Iter 572/2250] R2[121/600], Temp: 0.9030, Energy: -52.139979-0.000578j
[2025-09-04 00:01:24] [Iter 573/2250] R2[122/600], Temp: 0.9014, Energy: -52.080446-0.000850j
[2025-09-04 00:02:24] [Iter 574/2250] R2[123/600], Temp: 0.8998, Energy: -52.107058+0.002736j
[2025-09-04 00:03:25] [Iter 575/2250] R2[124/600], Temp: 0.8983, Energy: -52.079928-0.001909j
[2025-09-04 00:04:25] [Iter 576/2250] R2[125/600], Temp: 0.8967, Energy: -52.097440+0.000254j
[2025-09-04 00:05:25] [Iter 577/2250] R2[126/600], Temp: 0.8951, Energy: -52.064061-0.000533j
[2025-09-04 00:06:25] [Iter 578/2250] R2[127/600], Temp: 0.8935, Energy: -52.102286-0.000855j
[2025-09-04 00:07:26] [Iter 579/2250] R2[128/600], Temp: 0.8918, Energy: -52.082879+0.004712j
[2025-09-04 00:08:26] [Iter 580/2250] R2[129/600], Temp: 0.8902, Energy: -52.040641+0.004136j
[2025-09-04 00:09:26] [Iter 581/2250] R2[130/600], Temp: 0.8886, Energy: -52.011227+0.001275j
[2025-09-04 00:10:27] [Iter 582/2250] R2[131/600], Temp: 0.8869, Energy: -52.074250+0.000176j
[2025-09-04 00:11:27] [Iter 583/2250] R2[132/600], Temp: 0.8853, Energy: -52.104585-0.001941j
[2025-09-04 00:12:27] [Iter 584/2250] R2[133/600], Temp: 0.8836, Energy: -52.047586+0.001415j
[2025-09-04 00:13:28] [Iter 585/2250] R2[134/600], Temp: 0.8819, Energy: -52.066816+0.000088j
[2025-09-04 00:14:28] [Iter 586/2250] R2[135/600], Temp: 0.8802, Energy: -52.062734-0.000833j
[2025-09-04 00:15:28] [Iter 587/2250] R2[136/600], Temp: 0.8785, Energy: -52.099870-0.002230j
[2025-09-04 00:16:28] [Iter 588/2250] R2[137/600], Temp: 0.8768, Energy: -52.068176-0.004112j
[2025-09-04 00:17:29] [Iter 589/2250] R2[138/600], Temp: 0.8751, Energy: -52.096448-0.000794j
[2025-09-04 00:18:29] [Iter 590/2250] R2[139/600], Temp: 0.8733, Energy: -52.074465+0.000406j
[2025-09-04 00:19:29] [Iter 591/2250] R2[140/600], Temp: 0.8716, Energy: -52.035007+0.001524j
[2025-09-04 00:20:29] [Iter 592/2250] R2[141/600], Temp: 0.8698, Energy: -52.024115-0.003765j
[2025-09-04 00:21:30] [Iter 593/2250] R2[142/600], Temp: 0.8680, Energy: -52.051870-0.001013j
[2025-09-04 00:22:30] [Iter 594/2250] R2[143/600], Temp: 0.8663, Energy: -52.012754+0.002444j
[2025-09-04 00:23:30] [Iter 595/2250] R2[144/600], Temp: 0.8645, Energy: -52.007298+0.001522j
[2025-09-04 00:24:30] [Iter 596/2250] R2[145/600], Temp: 0.8627, Energy: -52.060440+0.003711j
[2025-09-04 00:25:31] [Iter 597/2250] R2[146/600], Temp: 0.8609, Energy: -52.022172-0.001386j
[2025-09-04 00:26:31] [Iter 598/2250] R2[147/600], Temp: 0.8591, Energy: -52.042929-0.000904j
[2025-09-04 00:27:31] [Iter 599/2250] R2[148/600], Temp: 0.8572, Energy: -52.045517-0.000446j
[2025-09-04 00:28:32] [Iter 600/2250] R2[149/600], Temp: 0.8554, Energy: -52.095792-0.000014j
[2025-09-04 00:29:32] [Iter 601/2250] R2[150/600], Temp: 0.8536, Energy: -52.023425-0.004342j
[2025-09-04 00:30:32] [Iter 602/2250] R2[151/600], Temp: 0.8517, Energy: -52.010279+0.002730j
[2025-09-04 00:31:32] [Iter 603/2250] R2[152/600], Temp: 0.8498, Energy: -52.071931-0.001769j
[2025-09-04 00:32:33] [Iter 604/2250] R2[153/600], Temp: 0.8480, Energy: -52.143921-0.002480j
[2025-09-04 00:33:33] [Iter 605/2250] R2[154/600], Temp: 0.8461, Energy: -52.069373+0.008387j
[2025-09-04 00:34:33] [Iter 606/2250] R2[155/600], Temp: 0.8442, Energy: -52.152975-0.003472j
[2025-09-04 00:35:33] [Iter 607/2250] R2[156/600], Temp: 0.8423, Energy: -52.016796-0.001731j
[2025-09-04 00:36:34] [Iter 608/2250] R2[157/600], Temp: 0.8404, Energy: -52.046112-0.002171j
[2025-09-04 00:37:34] [Iter 609/2250] R2[158/600], Temp: 0.8384, Energy: -52.001292-0.006418j
[2025-09-04 00:38:34] [Iter 610/2250] R2[159/600], Temp: 0.8365, Energy: -52.023728+0.000450j
[2025-09-04 00:39:34] [Iter 611/2250] R2[160/600], Temp: 0.8346, Energy: -51.995335-0.004439j
[2025-09-04 00:40:35] [Iter 612/2250] R2[161/600], Temp: 0.8326, Energy: -52.001971+0.003092j
[2025-09-04 00:41:35] [Iter 613/2250] R2[162/600], Temp: 0.8307, Energy: -52.000379+0.001088j
[2025-09-04 00:42:35] [Iter 614/2250] R2[163/600], Temp: 0.8287, Energy: -52.060051+0.002110j
[2025-09-04 00:43:36] [Iter 615/2250] R2[164/600], Temp: 0.8267, Energy: -52.024457+0.001601j
[2025-09-04 00:44:36] [Iter 616/2250] R2[165/600], Temp: 0.8247, Energy: -52.025174+0.001055j
[2025-09-04 00:45:36] [Iter 617/2250] R2[166/600], Temp: 0.8227, Energy: -52.061305-0.001702j
[2025-09-04 00:46:36] [Iter 618/2250] R2[167/600], Temp: 0.8207, Energy: -51.988935+0.001265j
[2025-09-04 00:47:37] [Iter 619/2250] R2[168/600], Temp: 0.8187, Energy: -51.991339+0.003742j
[2025-09-04 00:48:37] [Iter 620/2250] R2[169/600], Temp: 0.8167, Energy: -52.040315-0.000476j
[2025-09-04 00:49:37] [Iter 621/2250] R2[170/600], Temp: 0.8147, Energy: -51.995605-0.000281j
[2025-09-04 00:50:38] [Iter 622/2250] R2[171/600], Temp: 0.8126, Energy: -52.017297-0.002919j
[2025-09-04 00:51:38] [Iter 623/2250] R2[172/600], Temp: 0.8106, Energy: -51.974480+0.001326j
[2025-09-04 00:52:38] [Iter 624/2250] R2[173/600], Temp: 0.8085, Energy: -52.002137+0.002796j
[2025-09-04 00:53:38] [Iter 625/2250] R2[174/600], Temp: 0.8065, Energy: -52.035701-0.003460j
[2025-09-04 00:54:39] [Iter 626/2250] R2[175/600], Temp: 0.8044, Energy: -51.959449-0.002913j
[2025-09-04 00:55:39] [Iter 627/2250] R2[176/600], Temp: 0.8023, Energy: -52.075981-0.001466j
[2025-09-04 00:56:39] [Iter 628/2250] R2[177/600], Temp: 0.8002, Energy: -52.005342+0.001836j
[2025-09-04 00:57:39] [Iter 629/2250] R2[178/600], Temp: 0.7981, Energy: -51.975686-0.000718j
[2025-09-04 00:58:40] [Iter 630/2250] R2[179/600], Temp: 0.7960, Energy: -52.005172-0.003322j
[2025-09-04 00:59:40] [Iter 631/2250] R2[180/600], Temp: 0.7939, Energy: -52.022648+0.005698j
[2025-09-04 01:00:40] [Iter 632/2250] R2[181/600], Temp: 0.7918, Energy: -52.047926+0.000306j
[2025-09-04 01:01:41] [Iter 633/2250] R2[182/600], Temp: 0.7896, Energy: -52.037966-0.004272j
[2025-09-04 01:02:41] [Iter 634/2250] R2[183/600], Temp: 0.7875, Energy: -52.017400-0.001984j
[2025-09-04 01:03:41] [Iter 635/2250] R2[184/600], Temp: 0.7854, Energy: -52.061805+0.001344j
[2025-09-04 01:04:41] [Iter 636/2250] R2[185/600], Temp: 0.7832, Energy: -52.065171-0.003575j
[2025-09-04 01:05:42] [Iter 637/2250] R2[186/600], Temp: 0.7810, Energy: -52.030693+0.003240j
[2025-09-04 01:06:42] [Iter 638/2250] R2[187/600], Temp: 0.7789, Energy: -52.013567+0.004060j
[2025-09-04 01:07:42] [Iter 639/2250] R2[188/600], Temp: 0.7767, Energy: -52.080709-0.001148j
[2025-09-04 01:08:42] [Iter 640/2250] R2[189/600], Temp: 0.7745, Energy: -51.997427+0.001569j
[2025-09-04 01:09:43] [Iter 641/2250] R2[190/600], Temp: 0.7723, Energy: -52.085794-0.000643j
[2025-09-04 01:10:43] [Iter 642/2250] R2[191/600], Temp: 0.7701, Energy: -52.006839+0.003370j
[2025-09-04 01:11:43] [Iter 643/2250] R2[192/600], Temp: 0.7679, Energy: -51.926312-0.001214j
[2025-09-04 01:12:44] [Iter 644/2250] R2[193/600], Temp: 0.7657, Energy: -51.993374+0.002208j
[2025-09-04 01:13:44] [Iter 645/2250] R2[194/600], Temp: 0.7635, Energy: -52.004720+0.001297j
[2025-09-04 01:14:44] [Iter 646/2250] R2[195/600], Temp: 0.7612, Energy: -52.071861-0.002218j
[2025-09-04 01:15:44] [Iter 647/2250] R2[196/600], Temp: 0.7590, Energy: -52.067427+0.001641j
[2025-09-04 01:16:45] [Iter 648/2250] R2[197/600], Temp: 0.7568, Energy: -52.080172+0.002677j
[2025-09-04 01:17:45] [Iter 649/2250] R2[198/600], Temp: 0.7545, Energy: -52.065983-0.000904j
[2025-09-04 01:18:45] [Iter 650/2250] R2[199/600], Temp: 0.7523, Energy: -52.052582+0.004017j
[2025-09-04 01:19:46] [Iter 651/2250] R2[200/600], Temp: 0.7500, Energy: -52.066147-0.001016j
[2025-09-04 01:20:46] [Iter 652/2250] R2[201/600], Temp: 0.7477, Energy: -52.088812+0.004083j
[2025-09-04 01:21:46] [Iter 653/2250] R2[202/600], Temp: 0.7455, Energy: -52.072687-0.002966j
[2025-09-04 01:22:46] [Iter 654/2250] R2[203/600], Temp: 0.7432, Energy: -52.084658+0.002810j
[2025-09-04 01:23:47] [Iter 655/2250] R2[204/600], Temp: 0.7409, Energy: -52.083076+0.000874j
[2025-09-04 01:24:47] [Iter 656/2250] R2[205/600], Temp: 0.7386, Energy: -52.040613+0.001165j
[2025-09-04 01:25:47] [Iter 657/2250] R2[206/600], Temp: 0.7363, Energy: -52.057727-0.000905j
[2025-09-04 01:26:47] [Iter 658/2250] R2[207/600], Temp: 0.7340, Energy: -52.048101+0.001133j
[2025-09-04 01:27:48] [Iter 659/2250] R2[208/600], Temp: 0.7316, Energy: -52.026548+0.001688j
[2025-09-04 01:28:48] [Iter 660/2250] R2[209/600], Temp: 0.7293, Energy: -52.071101-0.000709j
[2025-09-04 01:29:48] [Iter 661/2250] R2[210/600], Temp: 0.7270, Energy: -52.064688-0.004254j
[2025-09-04 01:30:49] [Iter 662/2250] R2[211/600], Temp: 0.7247, Energy: -52.105241+0.001630j
[2025-09-04 01:31:49] [Iter 663/2250] R2[212/600], Temp: 0.7223, Energy: -52.103720-0.000301j
[2025-09-04 01:32:49] [Iter 664/2250] R2[213/600], Temp: 0.7200, Energy: -52.096210+0.002912j
[2025-09-04 01:33:49] [Iter 665/2250] R2[214/600], Temp: 0.7176, Energy: -52.101959+0.002866j
[2025-09-04 01:34:50] [Iter 666/2250] R2[215/600], Temp: 0.7153, Energy: -52.098714+0.000182j
[2025-09-04 01:35:50] [Iter 667/2250] R2[216/600], Temp: 0.7129, Energy: -51.995730+0.003200j
[2025-09-04 01:36:50] [Iter 668/2250] R2[217/600], Temp: 0.7105, Energy: -52.048165-0.001745j
[2025-09-04 01:37:50] [Iter 669/2250] R2[218/600], Temp: 0.7081, Energy: -52.028090+0.001479j
[2025-09-04 01:38:51] [Iter 670/2250] R2[219/600], Temp: 0.7058, Energy: -52.072095-0.001917j
[2025-09-04 01:39:51] [Iter 671/2250] R2[220/600], Temp: 0.7034, Energy: -52.053167-0.001121j
[2025-09-04 01:40:51] [Iter 672/2250] R2[221/600], Temp: 0.7010, Energy: -52.088031+0.003055j
[2025-09-04 01:41:52] [Iter 673/2250] R2[222/600], Temp: 0.6986, Energy: -52.069172-0.001037j
[2025-09-04 01:42:52] [Iter 674/2250] R2[223/600], Temp: 0.6962, Energy: -52.115638-0.001455j
[2025-09-04 01:43:52] [Iter 675/2250] R2[224/600], Temp: 0.6938, Energy: -52.079171-0.004398j
[2025-09-04 01:44:52] [Iter 676/2250] R2[225/600], Temp: 0.6913, Energy: -52.074661+0.000668j
[2025-09-04 01:45:53] [Iter 677/2250] R2[226/600], Temp: 0.6889, Energy: -52.077779-0.001159j
[2025-09-04 01:46:53] [Iter 678/2250] R2[227/600], Temp: 0.6865, Energy: -52.096814-0.000304j
[2025-09-04 01:47:53] [Iter 679/2250] R2[228/600], Temp: 0.6841, Energy: -52.071810+0.001854j
[2025-09-04 01:48:54] [Iter 680/2250] R2[229/600], Temp: 0.6816, Energy: -52.119133+0.003958j
[2025-09-04 01:49:54] [Iter 681/2250] R2[230/600], Temp: 0.6792, Energy: -52.156030-0.000546j
[2025-09-04 01:50:54] [Iter 682/2250] R2[231/600], Temp: 0.6767, Energy: -52.114597+0.002174j
[2025-09-04 01:51:54] [Iter 683/2250] R2[232/600], Temp: 0.6743, Energy: -52.037926-0.000742j
[2025-09-04 01:52:55] [Iter 684/2250] R2[233/600], Temp: 0.6718, Energy: -52.035744-0.000996j
[2025-09-04 01:53:55] [Iter 685/2250] R2[234/600], Temp: 0.6694, Energy: -52.015098-0.003245j
[2025-09-04 01:54:55] [Iter 686/2250] R2[235/600], Temp: 0.6669, Energy: -52.084017-0.000823j
[2025-09-04 01:55:55] [Iter 687/2250] R2[236/600], Temp: 0.6644, Energy: -52.068698+0.002324j
[2025-09-04 01:56:56] [Iter 688/2250] R2[237/600], Temp: 0.6620, Energy: -52.016206+0.001371j
[2025-09-04 01:57:56] [Iter 689/2250] R2[238/600], Temp: 0.6595, Energy: -52.063237+0.004745j
[2025-09-04 01:58:56] [Iter 690/2250] R2[239/600], Temp: 0.6570, Energy: -52.089378+0.002477j
[2025-09-04 01:59:57] [Iter 691/2250] R2[240/600], Temp: 0.6545, Energy: -52.074252-0.000879j
[2025-09-04 02:00:57] [Iter 692/2250] R2[241/600], Temp: 0.6520, Energy: -52.085682-0.002062j
[2025-09-04 02:01:57] [Iter 693/2250] R2[242/600], Temp: 0.6495, Energy: -52.067333+0.000775j
[2025-09-04 02:02:57] [Iter 694/2250] R2[243/600], Temp: 0.6470, Energy: -52.044949-0.004104j
[2025-09-04 02:03:58] [Iter 695/2250] R2[244/600], Temp: 0.6445, Energy: -52.091019+0.002014j
[2025-09-04 02:04:58] [Iter 696/2250] R2[245/600], Temp: 0.6420, Energy: -52.063463-0.000701j
[2025-09-04 02:05:58] [Iter 697/2250] R2[246/600], Temp: 0.6395, Energy: -52.066024-0.002103j
[2025-09-04 02:06:58] [Iter 698/2250] R2[247/600], Temp: 0.6370, Energy: -52.098287-0.002741j
[2025-09-04 02:07:59] [Iter 699/2250] R2[248/600], Temp: 0.6345, Energy: -52.099434+0.000363j
[2025-09-04 02:08:59] [Iter 700/2250] R2[249/600], Temp: 0.6319, Energy: -52.084922-0.000567j
[2025-09-04 02:09:59] [Iter 701/2250] R2[250/600], Temp: 0.6294, Energy: -52.103085+0.002201j
[2025-09-04 02:10:59] [Iter 702/2250] R2[251/600], Temp: 0.6269, Energy: -52.073469-0.002523j
[2025-09-04 02:12:00] [Iter 703/2250] R2[252/600], Temp: 0.6243, Energy: -52.087171+0.001217j
[2025-09-04 02:12:43] [Iter 704/2250] R2[253/600], Temp: 0.6218, Energy: -52.045752+0.000484j
[2025-09-04 02:13:23] [Iter 705/2250] R2[254/600], Temp: 0.6193, Energy: -52.037030+0.001267j
[2025-09-04 02:14:04] [Iter 706/2250] R2[255/600], Temp: 0.6167, Energy: -52.043713-0.000246j
[2025-09-04 02:14:27] [Iter 707/2250] R2[256/600], Temp: 0.6142, Energy: -52.103137+0.000564j
[2025-09-04 02:14:46] [Iter 708/2250] R2[257/600], Temp: 0.6116, Energy: -52.139887-0.006393j
[2025-09-04 02:15:12] [Iter 709/2250] R2[258/600], Temp: 0.6091, Energy: -52.119433-0.000554j
[2025-09-04 02:15:35] [Iter 710/2250] R2[259/600], Temp: 0.6065, Energy: -52.078815+0.001628j
[2025-09-04 02:16:07] [Iter 711/2250] R2[260/600], Temp: 0.6040, Energy: -52.071284-0.000771j
[2025-09-04 02:16:58] [Iter 712/2250] R2[261/600], Temp: 0.6014, Energy: -52.098457-0.001280j
[2025-09-04 02:17:58] [Iter 713/2250] R2[262/600], Temp: 0.5988, Energy: -52.115484+0.000023j
[2025-09-04 02:18:58] [Iter 714/2250] R2[263/600], Temp: 0.5963, Energy: -52.164739-0.000381j
[2025-09-04 02:19:58] [Iter 715/2250] R2[264/600], Temp: 0.5937, Energy: -52.124411-0.000914j
[2025-09-04 02:20:58] [Iter 716/2250] R2[265/600], Temp: 0.5911, Energy: -52.086977-0.000628j
[2025-09-04 02:21:58] [Iter 717/2250] R2[266/600], Temp: 0.5885, Energy: -52.085605-0.003103j
[2025-09-04 02:22:59] [Iter 718/2250] R2[267/600], Temp: 0.5860, Energy: -52.094940+0.001063j
[2025-09-04 02:23:59] [Iter 719/2250] R2[268/600], Temp: 0.5834, Energy: -52.036795-0.004828j
[2025-09-04 02:24:59] [Iter 720/2250] R2[269/600], Temp: 0.5808, Energy: -52.045742-0.002193j
[2025-09-04 02:25:59] [Iter 721/2250] R2[270/600], Temp: 0.5782, Energy: -52.054897-0.000877j
[2025-09-04 02:26:59] [Iter 722/2250] R2[271/600], Temp: 0.5756, Energy: -52.040565+0.002485j
[2025-09-04 02:27:59] [Iter 723/2250] R2[272/600], Temp: 0.5730, Energy: -52.049212-0.001552j
[2025-09-04 02:28:59] [Iter 724/2250] R2[273/600], Temp: 0.5705, Energy: -52.109143+0.000837j
[2025-09-04 02:30:00] [Iter 725/2250] R2[274/600], Temp: 0.5679, Energy: -52.117586+0.000212j
[2025-09-04 02:31:00] [Iter 726/2250] R2[275/600], Temp: 0.5653, Energy: -52.086515+0.000510j
[2025-09-04 02:32:00] [Iter 727/2250] R2[276/600], Temp: 0.5627, Energy: -52.080515-0.002729j
[2025-09-04 02:33:00] [Iter 728/2250] R2[277/600], Temp: 0.5601, Energy: -52.155151-0.002778j
[2025-09-04 02:34:00] [Iter 729/2250] R2[278/600], Temp: 0.5575, Energy: -52.172231-0.001111j
[2025-09-04 02:35:00] [Iter 730/2250] R2[279/600], Temp: 0.5549, Energy: -52.141675+0.000926j
[2025-09-04 02:36:00] [Iter 731/2250] R2[280/600], Temp: 0.5523, Energy: -52.067087+0.000201j
[2025-09-04 02:37:00] [Iter 732/2250] R2[281/600], Temp: 0.5497, Energy: -52.084042-0.000076j
[2025-09-04 02:38:00] [Iter 733/2250] R2[282/600], Temp: 0.5471, Energy: -52.054519-0.000684j
[2025-09-04 02:39:00] [Iter 734/2250] R2[283/600], Temp: 0.5444, Energy: -52.081359+0.001693j
[2025-09-04 02:40:00] [Iter 735/2250] R2[284/600], Temp: 0.5418, Energy: -52.069892-0.000332j
[2025-09-04 02:41:00] [Iter 736/2250] R2[285/600], Temp: 0.5392, Energy: -52.128728+0.000201j
[2025-09-04 02:42:00] [Iter 737/2250] R2[286/600], Temp: 0.5366, Energy: -52.057384+0.000682j
[2025-09-04 02:43:00] [Iter 738/2250] R2[287/600], Temp: 0.5340, Energy: -52.089503+0.002886j
[2025-09-04 02:44:00] [Iter 739/2250] R2[288/600], Temp: 0.5314, Energy: -52.095185+0.004433j
[2025-09-04 02:45:00] [Iter 740/2250] R2[289/600], Temp: 0.5288, Energy: -52.066007+0.002550j
[2025-09-04 02:46:00] [Iter 741/2250] R2[290/600], Temp: 0.5262, Energy: -52.025060+0.000169j
[2025-09-04 02:47:00] [Iter 742/2250] R2[291/600], Temp: 0.5236, Energy: -52.052576+0.003695j
[2025-09-04 02:48:00] [Iter 743/2250] R2[292/600], Temp: 0.5209, Energy: -52.019344+0.001245j
[2025-09-04 02:49:01] [Iter 744/2250] R2[293/600], Temp: 0.5183, Energy: -52.073952-0.001564j
[2025-09-04 02:50:01] [Iter 745/2250] R2[294/600], Temp: 0.5157, Energy: -52.035389+0.001620j
[2025-09-04 02:51:01] [Iter 746/2250] R2[295/600], Temp: 0.5131, Energy: -52.027779+0.003164j
[2025-09-04 02:52:01] [Iter 747/2250] R2[296/600], Temp: 0.5105, Energy: -52.062842+0.005119j
[2025-09-04 02:53:01] [Iter 748/2250] R2[297/600], Temp: 0.5079, Energy: -52.070086+0.000890j
[2025-09-04 02:54:01] [Iter 749/2250] R2[298/600], Temp: 0.5052, Energy: -52.050781+0.003619j
[2025-09-04 02:55:01] [Iter 750/2250] R2[299/600], Temp: 0.5026, Energy: -51.982730-0.001095j
[2025-09-04 02:55:01] ✓ Checkpoint saved: checkpoint_iter_000750.pkl
[2025-09-04 02:56:01] [Iter 751/2250] R2[300/600], Temp: 0.5000, Energy: -52.065770+0.000793j
[2025-09-04 02:57:01] [Iter 752/2250] R2[301/600], Temp: 0.4974, Energy: -52.038784+0.002610j
[2025-09-04 02:58:01] [Iter 753/2250] R2[302/600], Temp: 0.4948, Energy: -52.078780+0.000781j
[2025-09-04 02:59:01] [Iter 754/2250] R2[303/600], Temp: 0.4921, Energy: -52.126164+0.000648j
[2025-09-04 03:00:01] [Iter 755/2250] R2[304/600], Temp: 0.4895, Energy: -52.072092-0.002727j
[2025-09-04 03:01:01] [Iter 756/2250] R2[305/600], Temp: 0.4869, Energy: -52.067648-0.001097j
[2025-09-04 03:02:01] [Iter 757/2250] R2[306/600], Temp: 0.4843, Energy: -52.160021-0.000972j
[2025-09-04 03:03:01] [Iter 758/2250] R2[307/600], Temp: 0.4817, Energy: -52.112208-0.001152j
[2025-09-04 03:04:01] [Iter 759/2250] R2[308/600], Temp: 0.4791, Energy: -52.152716-0.000424j
[2025-09-04 03:05:01] [Iter 760/2250] R2[309/600], Temp: 0.4764, Energy: -52.175628-0.000102j
[2025-09-04 03:06:01] [Iter 761/2250] R2[310/600], Temp: 0.4738, Energy: -52.127968-0.000159j
[2025-09-04 03:07:01] [Iter 762/2250] R2[311/600], Temp: 0.4712, Energy: -52.096121+0.001968j
[2025-09-04 03:08:01] [Iter 763/2250] R2[312/600], Temp: 0.4686, Energy: -52.127930+0.002294j
[2025-09-04 03:09:01] [Iter 764/2250] R2[313/600], Temp: 0.4660, Energy: -52.092062-0.000680j
[2025-09-04 03:10:01] [Iter 765/2250] R2[314/600], Temp: 0.4634, Energy: -52.141957+0.000824j
[2025-09-04 03:11:02] [Iter 766/2250] R2[315/600], Temp: 0.4608, Energy: -52.076512+0.000471j
[2025-09-04 03:12:02] [Iter 767/2250] R2[316/600], Temp: 0.4582, Energy: -52.094391+0.001115j
[2025-09-04 03:13:02] [Iter 768/2250] R2[317/600], Temp: 0.4556, Energy: -52.119788+0.006729j
[2025-09-04 03:14:02] [Iter 769/2250] R2[318/600], Temp: 0.4529, Energy: -52.127447+0.001586j
[2025-09-04 03:15:02] [Iter 770/2250] R2[319/600], Temp: 0.4503, Energy: -52.080378-0.002611j
[2025-09-04 03:16:02] [Iter 771/2250] R2[320/600], Temp: 0.4477, Energy: -52.103502+0.001743j
[2025-09-04 03:17:02] [Iter 772/2250] R2[321/600], Temp: 0.4451, Energy: -52.114314+0.001350j
[2025-09-04 03:18:02] [Iter 773/2250] R2[322/600], Temp: 0.4425, Energy: -52.059757-0.001181j
[2025-09-04 03:19:02] [Iter 774/2250] R2[323/600], Temp: 0.4399, Energy: -52.051893+0.001219j
[2025-09-04 03:20:02] [Iter 775/2250] R2[324/600], Temp: 0.4373, Energy: -52.017451+0.000381j
[2025-09-04 03:21:02] [Iter 776/2250] R2[325/600], Temp: 0.4347, Energy: -52.040968+0.000521j
[2025-09-04 03:22:02] [Iter 777/2250] R2[326/600], Temp: 0.4321, Energy: -52.066788+0.000014j
[2025-09-04 03:23:02] [Iter 778/2250] R2[327/600], Temp: 0.4295, Energy: -52.111711-0.001077j
[2025-09-04 03:24:02] [Iter 779/2250] R2[328/600], Temp: 0.4270, Energy: -52.138700-0.004114j
[2025-09-04 03:25:02] [Iter 780/2250] R2[329/600], Temp: 0.4244, Energy: -52.100730+0.000516j
[2025-09-04 03:26:02] [Iter 781/2250] R2[330/600], Temp: 0.4218, Energy: -52.130786+0.002031j
[2025-09-04 03:27:02] [Iter 782/2250] R2[331/600], Temp: 0.4192, Energy: -52.122187+0.005024j
[2025-09-04 03:28:02] [Iter 783/2250] R2[332/600], Temp: 0.4166, Energy: -52.096495+0.001907j
[2025-09-04 03:29:02] [Iter 784/2250] R2[333/600], Temp: 0.4140, Energy: -52.114127-0.005183j
[2025-09-04 03:30:03] [Iter 785/2250] R2[334/600], Temp: 0.4115, Energy: -52.063792-0.000881j
[2025-09-04 03:31:03] [Iter 786/2250] R2[335/600], Temp: 0.4089, Energy: -52.056710+0.001132j
[2025-09-04 03:32:03] [Iter 787/2250] R2[336/600], Temp: 0.4063, Energy: -52.038856+0.000169j
[2025-09-04 03:33:03] [Iter 788/2250] R2[337/600], Temp: 0.4037, Energy: -52.061868+0.000810j
[2025-09-04 03:34:03] [Iter 789/2250] R2[338/600], Temp: 0.4012, Energy: -52.084962-0.003208j
[2025-09-04 03:35:03] [Iter 790/2250] R2[339/600], Temp: 0.3986, Energy: -52.082733-0.000462j
[2025-09-04 03:36:03] [Iter 791/2250] R2[340/600], Temp: 0.3960, Energy: -52.074188+0.004822j
[2025-09-04 03:37:03] [Iter 792/2250] R2[341/600], Temp: 0.3935, Energy: -52.132563+0.002884j
[2025-09-04 03:38:03] [Iter 793/2250] R2[342/600], Temp: 0.3909, Energy: -52.148664+0.001044j
[2025-09-04 03:39:03] [Iter 794/2250] R2[343/600], Temp: 0.3884, Energy: -52.113020-0.002008j
[2025-09-04 03:40:03] [Iter 795/2250] R2[344/600], Temp: 0.3858, Energy: -52.129091-0.001947j
[2025-09-04 03:41:03] [Iter 796/2250] R2[345/600], Temp: 0.3833, Energy: -52.172555+0.001984j
[2025-09-04 03:42:03] [Iter 797/2250] R2[346/600], Temp: 0.3807, Energy: -52.103316+0.002319j
[2025-09-04 03:43:03] [Iter 798/2250] R2[347/600], Temp: 0.3782, Energy: -52.095131+0.000060j
[2025-09-04 03:44:03] [Iter 799/2250] R2[348/600], Temp: 0.3757, Energy: -52.205352+0.000909j
[2025-09-04 03:45:03] [Iter 800/2250] R2[349/600], Temp: 0.3731, Energy: -52.167254-0.000758j
[2025-09-04 03:46:03] [Iter 801/2250] R2[350/600], Temp: 0.3706, Energy: -52.208279-0.000478j
[2025-09-04 03:47:03] [Iter 802/2250] R2[351/600], Temp: 0.3681, Energy: -52.121628+0.003852j
[2025-09-04 03:48:03] [Iter 803/2250] R2[352/600], Temp: 0.3655, Energy: -52.076011+0.000164j
[2025-09-04 03:49:03] [Iter 804/2250] R2[353/600], Temp: 0.3630, Energy: -52.090685-0.002972j
[2025-09-04 03:50:03] [Iter 805/2250] R2[354/600], Temp: 0.3605, Energy: -52.078022-0.000671j
[2025-09-04 03:51:03] [Iter 806/2250] R2[355/600], Temp: 0.3580, Energy: -52.117110-0.000924j
[2025-09-04 03:52:03] [Iter 807/2250] R2[356/600], Temp: 0.3555, Energy: -52.125813+0.004460j
[2025-09-04 03:53:04] [Iter 808/2250] R2[357/600], Temp: 0.3530, Energy: -52.115889-0.003163j
[2025-09-04 03:54:04] [Iter 809/2250] R2[358/600], Temp: 0.3505, Energy: -52.119545-0.000060j
[2025-09-04 03:55:04] [Iter 810/2250] R2[359/600], Temp: 0.3480, Energy: -52.101864-0.001431j
[2025-09-04 03:56:04] [Iter 811/2250] R2[360/600], Temp: 0.3455, Energy: -52.106701+0.002524j
[2025-09-04 03:57:04] [Iter 812/2250] R2[361/600], Temp: 0.3430, Energy: -52.155459-0.000983j
[2025-09-04 03:58:04] [Iter 813/2250] R2[362/600], Temp: 0.3405, Energy: -52.129367+0.001313j
[2025-09-04 03:59:04] [Iter 814/2250] R2[363/600], Temp: 0.3380, Energy: -52.103789-0.002218j
[2025-09-04 04:00:04] [Iter 815/2250] R2[364/600], Temp: 0.3356, Energy: -52.094197+0.002206j
[2025-09-04 04:01:04] [Iter 816/2250] R2[365/600], Temp: 0.3331, Energy: -52.087389-0.002609j
[2025-09-04 04:02:04] [Iter 817/2250] R2[366/600], Temp: 0.3306, Energy: -52.054842-0.004099j
[2025-09-04 04:03:04] [Iter 818/2250] R2[367/600], Temp: 0.3282, Energy: -52.058289-0.003051j
[2025-09-04 04:04:04] [Iter 819/2250] R2[368/600], Temp: 0.3257, Energy: -52.063881+0.001273j
[2025-09-04 04:05:04] [Iter 820/2250] R2[369/600], Temp: 0.3233, Energy: -52.122621-0.001758j
[2025-09-04 04:06:04] [Iter 821/2250] R2[370/600], Temp: 0.3208, Energy: -52.113249+0.000524j
[2025-09-04 04:07:04] [Iter 822/2250] R2[371/600], Temp: 0.3184, Energy: -52.099121+0.000967j
[2025-09-04 04:08:04] [Iter 823/2250] R2[372/600], Temp: 0.3159, Energy: -52.123431-0.002536j
[2025-09-04 04:09:04] [Iter 824/2250] R2[373/600], Temp: 0.3135, Energy: -52.073914+0.000667j
[2025-09-04 04:10:04] [Iter 825/2250] R2[374/600], Temp: 0.3111, Energy: -52.108335-0.003476j
[2025-09-04 04:11:04] [Iter 826/2250] R2[375/600], Temp: 0.3087, Energy: -52.036047+0.002351j
[2025-09-04 04:12:04] [Iter 827/2250] R2[376/600], Temp: 0.3062, Energy: -52.094894+0.001465j
[2025-09-04 04:13:04] [Iter 828/2250] R2[377/600], Temp: 0.3038, Energy: -52.113877+0.001615j
[2025-09-04 04:14:05] [Iter 829/2250] R2[378/600], Temp: 0.3014, Energy: -52.134335+0.002600j
[2025-09-04 04:15:05] [Iter 830/2250] R2[379/600], Temp: 0.2990, Energy: -52.123400-0.000838j
[2025-09-04 04:16:05] [Iter 831/2250] R2[380/600], Temp: 0.2966, Energy: -52.126342+0.001819j
[2025-09-04 04:17:05] [Iter 832/2250] R2[381/600], Temp: 0.2942, Energy: -52.100474-0.004003j
[2025-09-04 04:18:05] [Iter 833/2250] R2[382/600], Temp: 0.2919, Energy: -52.150023+0.000972j
[2025-09-04 04:19:05] [Iter 834/2250] R2[383/600], Temp: 0.2895, Energy: -52.122562-0.004748j
[2025-09-04 04:20:05] [Iter 835/2250] R2[384/600], Temp: 0.2871, Energy: -52.116983+0.002877j
[2025-09-04 04:21:05] [Iter 836/2250] R2[385/600], Temp: 0.2847, Energy: -52.058465-0.001356j
[2025-09-04 04:22:05] [Iter 837/2250] R2[386/600], Temp: 0.2824, Energy: -52.112658+0.001245j
[2025-09-04 04:23:05] [Iter 838/2250] R2[387/600], Temp: 0.2800, Energy: -52.079868-0.002119j
[2025-09-04 04:24:05] [Iter 839/2250] R2[388/600], Temp: 0.2777, Energy: -52.040222-0.000457j
[2025-09-04 04:25:05] [Iter 840/2250] R2[389/600], Temp: 0.2753, Energy: -52.131057+0.000996j
[2025-09-04 04:26:05] [Iter 841/2250] R2[390/600], Temp: 0.2730, Energy: -52.080367-0.001755j
[2025-09-04 04:27:05] [Iter 842/2250] R2[391/600], Temp: 0.2707, Energy: -52.098424-0.000463j
[2025-09-04 04:28:05] [Iter 843/2250] R2[392/600], Temp: 0.2684, Energy: -52.071416-0.000383j
[2025-09-04 04:29:05] [Iter 844/2250] R2[393/600], Temp: 0.2660, Energy: -52.035411-0.000223j
[2025-09-04 04:30:05] [Iter 845/2250] R2[394/600], Temp: 0.2637, Energy: -52.077754+0.001866j
[2025-09-04 04:31:05] [Iter 846/2250] R2[395/600], Temp: 0.2614, Energy: -52.059155-0.001131j
[2025-09-04 04:32:05] [Iter 847/2250] R2[396/600], Temp: 0.2591, Energy: -52.102659-0.001878j
[2025-09-04 04:33:05] [Iter 848/2250] R2[397/600], Temp: 0.2568, Energy: -52.121708+0.001786j
[2025-09-04 04:34:05] [Iter 849/2250] R2[398/600], Temp: 0.2545, Energy: -52.158574+0.000130j
[2025-09-04 04:35:05] [Iter 850/2250] R2[399/600], Temp: 0.2523, Energy: -52.109454-0.001880j
[2025-09-04 04:36:06] [Iter 851/2250] R2[400/600], Temp: 0.2500, Energy: -52.036667+0.000111j
[2025-09-04 04:37:06] [Iter 852/2250] R2[401/600], Temp: 0.2477, Energy: -52.109278+0.000451j
[2025-09-04 04:38:06] [Iter 853/2250] R2[402/600], Temp: 0.2455, Energy: -52.087059-0.000506j
[2025-09-04 04:39:06] [Iter 854/2250] R2[403/600], Temp: 0.2432, Energy: -52.110983-0.000637j
[2025-09-04 04:40:06] [Iter 855/2250] R2[404/600], Temp: 0.2410, Energy: -52.045492-0.000381j
[2025-09-04 04:41:06] [Iter 856/2250] R2[405/600], Temp: 0.2388, Energy: -52.098483-0.000822j
[2025-09-04 04:42:06] [Iter 857/2250] R2[406/600], Temp: 0.2365, Energy: -52.131464-0.001272j
[2025-09-04 04:43:06] [Iter 858/2250] R2[407/600], Temp: 0.2343, Energy: -52.129302-0.001868j
[2025-09-04 04:44:06] [Iter 859/2250] R2[408/600], Temp: 0.2321, Energy: -52.135783+0.002170j
[2025-09-04 04:45:06] [Iter 860/2250] R2[409/600], Temp: 0.2299, Energy: -52.093598-0.002960j
[2025-09-04 04:46:06] [Iter 861/2250] R2[410/600], Temp: 0.2277, Energy: -52.124969+0.002256j
[2025-09-04 04:47:06] [Iter 862/2250] R2[411/600], Temp: 0.2255, Energy: -52.115701+0.001544j
[2025-09-04 04:48:06] [Iter 863/2250] R2[412/600], Temp: 0.2233, Energy: -52.097059-0.003192j
[2025-09-04 04:49:06] [Iter 864/2250] R2[413/600], Temp: 0.2211, Energy: -52.142145-0.000138j
[2025-09-04 04:50:06] [Iter 865/2250] R2[414/600], Temp: 0.2190, Energy: -52.112631+0.001481j
[2025-09-04 04:51:06] [Iter 866/2250] R2[415/600], Temp: 0.2168, Energy: -52.089561-0.002420j
[2025-09-04 04:52:06] [Iter 867/2250] R2[416/600], Temp: 0.2146, Energy: -52.177947+0.000322j
[2025-09-04 04:52:50] [Iter 868/2250] R2[417/600], Temp: 0.2125, Energy: -52.118067-0.001236j
[2025-09-04 04:53:30] [Iter 869/2250] R2[418/600], Temp: 0.2104, Energy: -52.098872+0.000185j
[2025-09-04 04:54:10] [Iter 870/2250] R2[419/600], Temp: 0.2082, Energy: -52.155243-0.000657j
[2025-09-04 04:54:51] [Iter 871/2250] R2[420/600], Temp: 0.2061, Energy: -52.168342-0.002600j
[2025-09-04 04:55:31] [Iter 872/2250] R2[421/600], Temp: 0.2040, Energy: -52.228440+0.005835j
[2025-09-04 04:56:11] [Iter 873/2250] R2[422/600], Temp: 0.2019, Energy: -52.101292-0.000744j
[2025-09-04 04:56:51] [Iter 874/2250] R2[423/600], Temp: 0.1998, Energy: -52.076221-0.001114j
[2025-09-04 04:57:31] [Iter 875/2250] R2[424/600], Temp: 0.1977, Energy: -52.061900+0.000285j
[2025-09-04 04:58:11] [Iter 876/2250] R2[425/600], Temp: 0.1956, Energy: -52.146913+0.000032j
[2025-09-04 04:58:51] [Iter 877/2250] R2[426/600], Temp: 0.1935, Energy: -52.087095+0.003466j
[2025-09-04 04:59:31] [Iter 878/2250] R2[427/600], Temp: 0.1915, Energy: -52.098515-0.001102j
[2025-09-04 05:00:11] [Iter 879/2250] R2[428/600], Temp: 0.1894, Energy: -52.042446+0.000211j
[2025-09-04 05:00:51] [Iter 880/2250] R2[429/600], Temp: 0.1874, Energy: -52.096885-0.001075j
[2025-09-04 05:01:32] [Iter 881/2250] R2[430/600], Temp: 0.1853, Energy: -52.085437-0.002105j
[2025-09-04 05:02:12] [Iter 882/2250] R2[431/600], Temp: 0.1833, Energy: -52.109013-0.000337j
[2025-09-04 05:02:52] [Iter 883/2250] R2[432/600], Temp: 0.1813, Energy: -52.146909+0.001322j
[2025-09-04 05:03:32] [Iter 884/2250] R2[433/600], Temp: 0.1793, Energy: -52.091448+0.001326j
[2025-09-04 05:04:12] [Iter 885/2250] R2[434/600], Temp: 0.1773, Energy: -52.088783+0.000654j
[2025-09-04 05:04:52] [Iter 886/2250] R2[435/600], Temp: 0.1753, Energy: -52.110991+0.001246j
[2025-09-04 05:05:32] [Iter 887/2250] R2[436/600], Temp: 0.1733, Energy: -52.117916-0.000687j
[2025-09-04 05:06:12] [Iter 888/2250] R2[437/600], Temp: 0.1713, Energy: -52.093869-0.004866j
[2025-09-04 05:06:52] [Iter 889/2250] R2[438/600], Temp: 0.1693, Energy: -52.052798+0.001252j
[2025-09-04 05:07:32] [Iter 890/2250] R2[439/600], Temp: 0.1674, Energy: -52.025037+0.001746j
[2025-09-04 05:08:12] [Iter 891/2250] R2[440/600], Temp: 0.1654, Energy: -52.015748-0.001986j
[2025-09-04 05:08:52] [Iter 892/2250] R2[441/600], Temp: 0.1635, Energy: -52.012511+0.000951j
[2025-09-04 05:09:33] [Iter 893/2250] R2[442/600], Temp: 0.1616, Energy: -52.054529+0.001301j
[2025-09-04 05:10:13] [Iter 894/2250] R2[443/600], Temp: 0.1596, Energy: -52.047309+0.000146j
[2025-09-04 05:10:53] [Iter 895/2250] R2[444/600], Temp: 0.1577, Energy: -52.051569-0.000213j
[2025-09-04 05:11:33] [Iter 896/2250] R2[445/600], Temp: 0.1558, Energy: -52.101128-0.001431j
[2025-09-04 05:12:13] [Iter 897/2250] R2[446/600], Temp: 0.1539, Energy: -52.018528-0.000981j
[2025-09-04 05:12:53] [Iter 898/2250] R2[447/600], Temp: 0.1520, Energy: -52.152344+0.002960j
[2025-09-04 05:13:33] [Iter 899/2250] R2[448/600], Temp: 0.1502, Energy: -52.109060+0.000753j
[2025-09-04 05:14:13] [Iter 900/2250] R2[449/600], Temp: 0.1483, Energy: -52.181687+0.000712j
[2025-09-04 05:14:53] [Iter 901/2250] R2[450/600], Temp: 0.1464, Energy: -52.134788+0.001754j
[2025-09-04 05:15:33] [Iter 902/2250] R2[451/600], Temp: 0.1446, Energy: -52.160810+0.001949j
[2025-09-04 05:16:14] [Iter 903/2250] R2[452/600], Temp: 0.1428, Energy: -52.124703-0.000179j
[2025-09-04 05:16:54] [Iter 904/2250] R2[453/600], Temp: 0.1409, Energy: -52.086497+0.002926j
[2025-09-04 05:17:34] [Iter 905/2250] R2[454/600], Temp: 0.1391, Energy: -52.097147-0.000466j
[2025-09-04 05:18:14] [Iter 906/2250] R2[455/600], Temp: 0.1373, Energy: -52.127550-0.000800j
[2025-09-04 05:18:54] [Iter 907/2250] R2[456/600], Temp: 0.1355, Energy: -52.122974+0.002909j
[2025-09-04 05:19:34] [Iter 908/2250] R2[457/600], Temp: 0.1337, Energy: -52.049537-0.000392j
[2025-09-04 05:20:14] [Iter 909/2250] R2[458/600], Temp: 0.1320, Energy: -52.075269-0.001244j
[2025-09-04 05:20:54] [Iter 910/2250] R2[459/600], Temp: 0.1302, Energy: -52.049206+0.001501j
[2025-09-04 05:21:34] [Iter 911/2250] R2[460/600], Temp: 0.1284, Energy: -52.121484+0.004773j
[2025-09-04 05:22:14] [Iter 912/2250] R2[461/600], Temp: 0.1267, Energy: -52.118307-0.000438j
[2025-09-04 05:22:55] [Iter 913/2250] R2[462/600], Temp: 0.1249, Energy: -52.135993+0.002229j
[2025-09-04 05:23:35] [Iter 914/2250] R2[463/600], Temp: 0.1232, Energy: -52.091576+0.001370j
[2025-09-04 05:24:15] [Iter 915/2250] R2[464/600], Temp: 0.1215, Energy: -52.034801-0.001818j
[2025-09-04 05:24:55] [Iter 916/2250] R2[465/600], Temp: 0.1198, Energy: -52.054675-0.000620j
[2025-09-04 05:25:35] [Iter 917/2250] R2[466/600], Temp: 0.1181, Energy: -52.159737+0.001575j
[2025-09-04 05:26:15] [Iter 918/2250] R2[467/600], Temp: 0.1164, Energy: -52.071573-0.001536j
[2025-09-04 05:26:55] [Iter 919/2250] R2[468/600], Temp: 0.1147, Energy: -52.052852-0.002676j
[2025-09-04 05:27:35] [Iter 920/2250] R2[469/600], Temp: 0.1131, Energy: -52.124468+0.001228j
[2025-09-04 05:28:15] [Iter 921/2250] R2[470/600], Temp: 0.1114, Energy: -52.111465-0.002365j
[2025-09-04 05:28:55] [Iter 922/2250] R2[471/600], Temp: 0.1098, Energy: -52.137937-0.002359j
[2025-09-04 05:29:35] [Iter 923/2250] R2[472/600], Temp: 0.1082, Energy: -52.158272-0.002077j
[2025-09-04 05:30:16] [Iter 924/2250] R2[473/600], Temp: 0.1065, Energy: -52.127121+0.000284j
[2025-09-04 05:30:56] [Iter 925/2250] R2[474/600], Temp: 0.1049, Energy: -52.127028+0.002108j
[2025-09-04 05:31:36] [Iter 926/2250] R2[475/600], Temp: 0.1033, Energy: -52.124799-0.000755j
[2025-09-04 05:32:16] [Iter 927/2250] R2[476/600], Temp: 0.1017, Energy: -52.133872+0.003693j
[2025-09-04 05:32:56] [Iter 928/2250] R2[477/600], Temp: 0.1002, Energy: -52.133494+0.001833j
[2025-09-04 05:33:36] [Iter 929/2250] R2[478/600], Temp: 0.0986, Energy: -52.111280+0.001394j
[2025-09-04 05:34:16] [Iter 930/2250] R2[479/600], Temp: 0.0970, Energy: -52.127001-0.001299j
[2025-09-04 05:34:56] [Iter 931/2250] R2[480/600], Temp: 0.0955, Energy: -52.139832-0.000716j
[2025-09-04 05:35:36] [Iter 932/2250] R2[481/600], Temp: 0.0940, Energy: -52.136922-0.000897j
[2025-09-04 05:36:17] [Iter 933/2250] R2[482/600], Temp: 0.0924, Energy: -52.189712+0.002449j
[2025-09-04 05:36:57] [Iter 934/2250] R2[483/600], Temp: 0.0909, Energy: -52.131637-0.001045j
[2025-09-04 05:37:37] [Iter 935/2250] R2[484/600], Temp: 0.0894, Energy: -52.123391+0.001273j
[2025-09-04 05:38:17] [Iter 936/2250] R2[485/600], Temp: 0.0879, Energy: -52.129739-0.000060j
[2025-09-04 05:38:57] [Iter 937/2250] R2[486/600], Temp: 0.0865, Energy: -52.145833-0.000585j
[2025-09-04 05:39:37] [Iter 938/2250] R2[487/600], Temp: 0.0850, Energy: -52.170336-0.001639j
[2025-09-04 05:40:17] [Iter 939/2250] R2[488/600], Temp: 0.0835, Energy: -52.082966-0.001843j
[2025-09-04 05:40:57] [Iter 940/2250] R2[489/600], Temp: 0.0821, Energy: -52.167817-0.000723j
[2025-09-04 05:41:37] [Iter 941/2250] R2[490/600], Temp: 0.0807, Energy: -52.130384-0.001728j
[2025-09-04 05:42:13] [Iter 942/2250] R2[491/600], Temp: 0.0792, Energy: -52.139359+0.000596j
[2025-09-04 05:42:32] [Iter 943/2250] R2[492/600], Temp: 0.0778, Energy: -52.189015-0.000118j
[2025-09-04 05:42:51] [Iter 944/2250] R2[493/600], Temp: 0.0764, Energy: -52.160131+0.000197j
[2025-09-04 05:43:14] [Iter 945/2250] R2[494/600], Temp: 0.0751, Energy: -52.124121-0.002944j
[2025-09-04 05:43:38] [Iter 946/2250] R2[495/600], Temp: 0.0737, Energy: -52.120536-0.000864j
[2025-09-04 05:44:01] [Iter 947/2250] R2[496/600], Temp: 0.0723, Energy: -52.149748-0.003193j
[2025-09-04 05:44:41] [Iter 948/2250] R2[497/600], Temp: 0.0710, Energy: -52.136348-0.002196j
[2025-09-04 05:45:21] [Iter 949/2250] R2[498/600], Temp: 0.0696, Energy: -52.091585+0.001097j
[2025-09-04 05:46:02] [Iter 950/2250] R2[499/600], Temp: 0.0683, Energy: -52.087479+0.001624j
[2025-09-04 05:46:42] [Iter 951/2250] R2[500/600], Temp: 0.0670, Energy: -52.098425-0.001220j
[2025-09-04 05:47:22] [Iter 952/2250] R2[501/600], Temp: 0.0657, Energy: -52.119980+0.001957j
[2025-09-04 05:48:02] [Iter 953/2250] R2[502/600], Temp: 0.0644, Energy: -52.096243-0.002120j
[2025-09-04 05:48:42] [Iter 954/2250] R2[503/600], Temp: 0.0631, Energy: -52.099625+0.001596j
[2025-09-04 05:49:22] [Iter 955/2250] R2[504/600], Temp: 0.0618, Energy: -52.069370+0.001038j
[2025-09-04 05:50:02] [Iter 956/2250] R2[505/600], Temp: 0.0606, Energy: -52.084049-0.001647j
[2025-09-04 05:50:42] [Iter 957/2250] R2[506/600], Temp: 0.0593, Energy: -52.026774-0.000659j
[2025-09-04 05:51:22] [Iter 958/2250] R2[507/600], Temp: 0.0581, Energy: -52.134711+0.004381j
[2025-09-04 05:52:02] [Iter 959/2250] R2[508/600], Temp: 0.0569, Energy: -52.133951+0.002086j
[2025-09-04 05:52:42] [Iter 960/2250] R2[509/600], Temp: 0.0557, Energy: -52.077669-0.000088j
[2025-09-04 05:53:23] [Iter 961/2250] R2[510/600], Temp: 0.0545, Energy: -52.093642-0.001062j
[2025-09-04 05:54:03] [Iter 962/2250] R2[511/600], Temp: 0.0533, Energy: -52.061387-0.005994j
[2025-09-04 05:54:43] [Iter 963/2250] R2[512/600], Temp: 0.0521, Energy: -52.060637+0.001520j
[2025-09-04 05:55:23] [Iter 964/2250] R2[513/600], Temp: 0.0510, Energy: -52.079385+0.000866j
[2025-09-04 05:56:03] [Iter 965/2250] R2[514/600], Temp: 0.0498, Energy: -52.103321+0.001741j
[2025-09-04 05:56:43] [Iter 966/2250] R2[515/600], Temp: 0.0487, Energy: -52.114499-0.000131j
[2025-09-04 05:57:23] [Iter 967/2250] R2[516/600], Temp: 0.0476, Energy: -52.064877+0.003290j
[2025-09-04 05:58:03] [Iter 968/2250] R2[517/600], Temp: 0.0465, Energy: -52.097467-0.000686j
[2025-09-04 05:58:43] [Iter 969/2250] R2[518/600], Temp: 0.0454, Energy: -52.096173+0.000106j
[2025-09-04 05:59:23] [Iter 970/2250] R2[519/600], Temp: 0.0443, Energy: -52.167795-0.001190j
[2025-09-04 06:00:03] [Iter 971/2250] R2[520/600], Temp: 0.0432, Energy: -52.151487+0.003665j
[2025-09-04 06:00:44] [Iter 972/2250] R2[521/600], Temp: 0.0422, Energy: -52.149188+0.002954j
[2025-09-04 06:01:24] [Iter 973/2250] R2[522/600], Temp: 0.0411, Energy: -52.135554+0.000308j
[2025-09-04 06:02:04] [Iter 974/2250] R2[523/600], Temp: 0.0401, Energy: -52.089775+0.005989j
[2025-09-04 06:02:44] [Iter 975/2250] R2[524/600], Temp: 0.0391, Energy: -52.112305+0.000446j
[2025-09-04 06:03:24] [Iter 976/2250] R2[525/600], Temp: 0.0381, Energy: -52.120981-0.002373j
[2025-09-04 06:04:04] [Iter 977/2250] R2[526/600], Temp: 0.0371, Energy: -52.126775-0.002545j
[2025-09-04 06:04:44] [Iter 978/2250] R2[527/600], Temp: 0.0361, Energy: -52.161750+0.002945j
[2025-09-04 06:05:24] [Iter 979/2250] R2[528/600], Temp: 0.0351, Energy: -52.135126+0.002166j
[2025-09-04 06:06:04] [Iter 980/2250] R2[529/600], Temp: 0.0342, Energy: -52.183402+0.001406j
[2025-09-04 06:06:44] [Iter 981/2250] R2[530/600], Temp: 0.0332, Energy: -52.117504+0.001308j
[2025-09-04 06:07:24] [Iter 982/2250] R2[531/600], Temp: 0.0323, Energy: -52.044042-0.002726j
[2025-09-04 06:08:05] [Iter 983/2250] R2[532/600], Temp: 0.0314, Energy: -52.088513+0.000763j
[2025-09-04 06:08:45] [Iter 984/2250] R2[533/600], Temp: 0.0305, Energy: -52.118616+0.001781j
[2025-09-04 06:09:25] [Iter 985/2250] R2[534/600], Temp: 0.0296, Energy: -52.130698+0.000564j
[2025-09-04 06:10:05] [Iter 986/2250] R2[535/600], Temp: 0.0287, Energy: -52.130349+0.003159j
[2025-09-04 06:10:45] [Iter 987/2250] R2[536/600], Temp: 0.0278, Energy: -52.080874-0.000818j
[2025-09-04 06:11:25] [Iter 988/2250] R2[537/600], Temp: 0.0270, Energy: -52.057128+0.000012j
[2025-09-04 06:12:05] [Iter 989/2250] R2[538/600], Temp: 0.0261, Energy: -52.065169+0.001178j
[2025-09-04 06:12:45] [Iter 990/2250] R2[539/600], Temp: 0.0253, Energy: -52.032769+0.000282j
[2025-09-04 06:13:25] [Iter 991/2250] R2[540/600], Temp: 0.0245, Energy: -52.079221+0.001912j
[2025-09-04 06:14:05] [Iter 992/2250] R2[541/600], Temp: 0.0237, Energy: -52.093799+0.000305j
[2025-09-04 06:14:45] [Iter 993/2250] R2[542/600], Temp: 0.0229, Energy: -52.154611-0.000711j
[2025-09-04 06:15:26] [Iter 994/2250] R2[543/600], Temp: 0.0221, Energy: -52.082536-0.001912j
[2025-09-04 06:16:06] [Iter 995/2250] R2[544/600], Temp: 0.0213, Energy: -52.090595-0.000114j
[2025-09-04 06:16:46] [Iter 996/2250] R2[545/600], Temp: 0.0206, Energy: -52.157346+0.001681j
[2025-09-04 06:17:26] [Iter 997/2250] R2[546/600], Temp: 0.0199, Energy: -52.169604-0.001460j
[2025-09-04 06:18:06] [Iter 998/2250] R2[547/600], Temp: 0.0191, Energy: -52.119321+0.001242j
[2025-09-04 06:18:46] [Iter 999/2250] R2[548/600], Temp: 0.0184, Energy: -52.073425+0.001921j
[2025-09-04 06:19:26] [Iter 1000/2250] R2[549/600], Temp: 0.0177, Energy: -52.122087-0.000113j
[2025-09-04 06:19:26] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-09-04 06:20:06] [Iter 1001/2250] R2[550/600], Temp: 0.0170, Energy: -52.130465-0.002763j
[2025-09-04 06:20:46] [Iter 1002/2250] R2[551/600], Temp: 0.0164, Energy: -52.087375+0.003035j
[2025-09-04 06:21:26] [Iter 1003/2250] R2[552/600], Temp: 0.0157, Energy: -52.068907+0.001113j
[2025-09-04 06:22:06] [Iter 1004/2250] R2[553/600], Temp: 0.0151, Energy: -52.138459+0.000436j
[2025-09-04 06:22:46] [Iter 1005/2250] R2[554/600], Temp: 0.0144, Energy: -52.155143+0.001690j
[2025-09-04 06:23:27] [Iter 1006/2250] R2[555/600], Temp: 0.0138, Energy: -52.187892+0.000640j
[2025-09-04 06:24:07] [Iter 1007/2250] R2[556/600], Temp: 0.0132, Energy: -52.133260-0.000822j
[2025-09-04 06:24:47] [Iter 1008/2250] R2[557/600], Temp: 0.0126, Energy: -52.158299+0.000571j
[2025-09-04 06:25:27] [Iter 1009/2250] R2[558/600], Temp: 0.0120, Energy: -52.132779-0.001841j
[2025-09-04 06:26:07] [Iter 1010/2250] R2[559/600], Temp: 0.0115, Energy: -52.253897+0.000516j
[2025-09-04 06:26:47] [Iter 1011/2250] R2[560/600], Temp: 0.0109, Energy: -52.175543-0.002276j
[2025-09-04 06:27:27] [Iter 1012/2250] R2[561/600], Temp: 0.0104, Energy: -52.136019-0.003946j
[2025-09-04 06:28:07] [Iter 1013/2250] R2[562/600], Temp: 0.0099, Energy: -52.115463-0.002005j
[2025-09-04 06:28:47] [Iter 1014/2250] R2[563/600], Temp: 0.0094, Energy: -52.049384+0.000600j
[2025-09-04 06:29:27] [Iter 1015/2250] R2[564/600], Temp: 0.0089, Energy: -52.099552-0.000265j
[2025-09-04 06:30:08] [Iter 1016/2250] R2[565/600], Temp: 0.0084, Energy: -52.083687-0.001085j
[2025-09-04 06:30:48] [Iter 1017/2250] R2[566/600], Temp: 0.0079, Energy: -52.103983-0.000281j
[2025-09-04 06:31:28] [Iter 1018/2250] R2[567/600], Temp: 0.0074, Energy: -52.172407-0.000990j
[2025-09-04 06:32:08] [Iter 1019/2250] R2[568/600], Temp: 0.0070, Energy: -52.140281+0.002316j
[2025-09-04 06:32:48] [Iter 1020/2250] R2[569/600], Temp: 0.0066, Energy: -52.107636+0.001018j
[2025-09-04 06:33:28] [Iter 1021/2250] R2[570/600], Temp: 0.0062, Energy: -52.116562-0.000990j
[2025-09-04 06:34:08] [Iter 1022/2250] R2[571/600], Temp: 0.0058, Energy: -52.031057-0.000707j
[2025-09-04 06:34:48] [Iter 1023/2250] R2[572/600], Temp: 0.0054, Energy: -52.114283-0.000130j
[2025-09-04 06:35:28] [Iter 1024/2250] R2[573/600], Temp: 0.0050, Energy: -52.134360-0.001113j
[2025-09-04 06:36:08] [Iter 1025/2250] R2[574/600], Temp: 0.0046, Energy: -52.102940-0.000386j
[2025-09-04 06:36:48] [Iter 1026/2250] R2[575/600], Temp: 0.0043, Energy: -52.139036-0.000386j
[2025-09-04 06:37:29] [Iter 1027/2250] R2[576/600], Temp: 0.0039, Energy: -52.155750+0.003742j
[2025-09-04 06:38:09] [Iter 1028/2250] R2[577/600], Temp: 0.0036, Energy: -52.087848+0.000373j
[2025-09-04 06:38:49] [Iter 1029/2250] R2[578/600], Temp: 0.0033, Energy: -52.093952+0.002656j
[2025-09-04 06:39:29] [Iter 1030/2250] R2[579/600], Temp: 0.0030, Energy: -52.070958-0.003158j
[2025-09-04 06:40:09] [Iter 1031/2250] R2[580/600], Temp: 0.0027, Energy: -52.095754+0.005245j
[2025-09-04 06:40:49] [Iter 1032/2250] R2[581/600], Temp: 0.0025, Energy: -52.068916-0.001979j
[2025-09-04 06:41:29] [Iter 1033/2250] R2[582/600], Temp: 0.0022, Energy: -52.058768+0.000778j
[2025-09-04 06:42:09] [Iter 1034/2250] R2[583/600], Temp: 0.0020, Energy: -52.037916+0.001829j
[2025-09-04 06:42:49] [Iter 1035/2250] R2[584/600], Temp: 0.0018, Energy: -52.090731+0.003569j
[2025-09-04 06:43:29] [Iter 1036/2250] R2[585/600], Temp: 0.0015, Energy: -52.059015+0.001107j
[2025-09-04 06:44:10] [Iter 1037/2250] R2[586/600], Temp: 0.0013, Energy: -52.058567-0.003242j
[2025-09-04 06:44:50] [Iter 1038/2250] R2[587/600], Temp: 0.0012, Energy: -52.082452+0.002185j
[2025-09-04 06:45:30] [Iter 1039/2250] R2[588/600], Temp: 0.0010, Energy: -52.108900+0.001575j
[2025-09-04 06:46:10] [Iter 1040/2250] R2[589/600], Temp: 0.0008, Energy: -52.069501-0.000887j
[2025-09-04 06:46:50] [Iter 1041/2250] R2[590/600], Temp: 0.0007, Energy: -52.130435-0.002561j
[2025-09-04 06:47:30] [Iter 1042/2250] R2[591/600], Temp: 0.0006, Energy: -52.062814-0.000241j
[2025-09-04 06:48:10] [Iter 1043/2250] R2[592/600], Temp: 0.0004, Energy: -52.083890+0.000195j
[2025-09-04 06:48:50] [Iter 1044/2250] R2[593/600], Temp: 0.0003, Energy: -52.026857-0.001044j
[2025-09-04 06:49:30] [Iter 1045/2250] R2[594/600], Temp: 0.0002, Energy: -52.027110+0.001661j
[2025-09-04 06:50:10] [Iter 1046/2250] R2[595/600], Temp: 0.0002, Energy: -52.106595-0.001563j
[2025-09-04 06:50:51] [Iter 1047/2250] R2[596/600], Temp: 0.0001, Energy: -52.180630-0.001687j
[2025-09-04 06:51:31] [Iter 1048/2250] R2[597/600], Temp: 0.0001, Energy: -52.126503+0.000477j
[2025-09-04 06:52:11] [Iter 1049/2250] R2[598/600], Temp: 0.0000, Energy: -52.121579-0.004550j
[2025-09-04 06:52:51] [Iter 1050/2250] R2[599/600], Temp: 0.0000, Energy: -52.094677+0.002823j
[2025-09-04 06:52:51] RESTART #3 | Period: 1200
[2025-09-04 06:53:31] [Iter 1051/2250] R3[0/1200], Temp: 1.0000, Energy: -52.158202-0.000341j
[2025-09-04 06:54:11] [Iter 1052/2250] R3[1/1200], Temp: 1.0000, Energy: -52.124603+0.000927j
[2025-09-04 06:54:51] [Iter 1053/2250] R3[2/1200], Temp: 1.0000, Energy: -52.144904-0.000182j
[2025-09-04 06:55:31] [Iter 1054/2250] R3[3/1200], Temp: 1.0000, Energy: -52.091883-0.000040j
[2025-09-04 06:56:11] [Iter 1055/2250] R3[4/1200], Temp: 1.0000, Energy: -52.065565-0.000578j
[2025-09-04 06:56:51] [Iter 1056/2250] R3[5/1200], Temp: 1.0000, Energy: -52.092428+0.000161j
[2025-09-04 06:57:31] [Iter 1057/2250] R3[6/1200], Temp: 0.9999, Energy: -52.056931-0.000423j
[2025-09-04 06:58:12] [Iter 1058/2250] R3[7/1200], Temp: 0.9999, Energy: -52.050261+0.001741j
[2025-09-04 06:58:52] [Iter 1059/2250] R3[8/1200], Temp: 0.9999, Energy: -52.066801-0.000400j
[2025-09-04 06:59:32] [Iter 1060/2250] R3[9/1200], Temp: 0.9999, Energy: -52.103264+0.003108j
[2025-09-04 07:00:12] [Iter 1061/2250] R3[10/1200], Temp: 0.9998, Energy: -52.096784-0.001658j
[2025-09-04 07:00:52] [Iter 1062/2250] R3[11/1200], Temp: 0.9998, Energy: -52.110353-0.000009j
[2025-09-04 07:01:32] [Iter 1063/2250] R3[12/1200], Temp: 0.9998, Energy: -52.156745-0.002591j
[2025-09-04 07:02:12] [Iter 1064/2250] R3[13/1200], Temp: 0.9997, Energy: -52.088375-0.004186j
[2025-09-04 07:02:52] [Iter 1065/2250] R3[14/1200], Temp: 0.9997, Energy: -52.110123+0.000829j
[2025-09-04 07:03:32] [Iter 1066/2250] R3[15/1200], Temp: 0.9996, Energy: -52.072032-0.001686j
[2025-09-04 07:04:12] [Iter 1067/2250] R3[16/1200], Temp: 0.9996, Energy: -52.059689-0.000788j
[2025-09-04 07:04:52] [Iter 1068/2250] R3[17/1200], Temp: 0.9995, Energy: -52.071400+0.000197j
[2025-09-04 07:05:33] [Iter 1069/2250] R3[18/1200], Temp: 0.9994, Energy: -52.122905-0.001385j
[2025-09-04 07:06:13] [Iter 1070/2250] R3[19/1200], Temp: 0.9994, Energy: -52.086788-0.000168j
[2025-09-04 07:06:53] [Iter 1071/2250] R3[20/1200], Temp: 0.9993, Energy: -52.147911-0.002166j
[2025-09-04 07:07:33] [Iter 1072/2250] R3[21/1200], Temp: 0.9992, Energy: -52.104666-0.003988j
[2025-09-04 07:08:13] [Iter 1073/2250] R3[22/1200], Temp: 0.9992, Energy: -52.131143-0.002045j
[2025-09-04 07:08:53] [Iter 1074/2250] R3[23/1200], Temp: 0.9991, Energy: -52.132641-0.001919j
[2025-09-04 07:09:33] [Iter 1075/2250] R3[24/1200], Temp: 0.9990, Energy: -52.129732+0.000150j
[2025-09-04 07:10:13] [Iter 1076/2250] R3[25/1200], Temp: 0.9989, Energy: -52.137200-0.001473j
[2025-09-04 07:10:53] [Iter 1077/2250] R3[26/1200], Temp: 0.9988, Energy: -52.160606+0.000848j
[2025-09-04 07:11:33] [Iter 1078/2250] R3[27/1200], Temp: 0.9988, Energy: -52.093205+0.001862j
[2025-09-04 07:12:13] [Iter 1079/2250] R3[28/1200], Temp: 0.9987, Energy: -52.200578-0.000186j
[2025-09-04 07:12:54] [Iter 1080/2250] R3[29/1200], Temp: 0.9986, Energy: -52.149997-0.000947j
[2025-09-04 07:13:34] [Iter 1081/2250] R3[30/1200], Temp: 0.9985, Energy: -52.119219+0.001098j
[2025-09-04 07:14:14] [Iter 1082/2250] R3[31/1200], Temp: 0.9984, Energy: -52.105356+0.001141j
[2025-09-04 07:14:54] [Iter 1083/2250] R3[32/1200], Temp: 0.9982, Energy: -52.068948-0.002554j
[2025-09-04 07:15:34] [Iter 1084/2250] R3[33/1200], Temp: 0.9981, Energy: -52.047033-0.001952j
[2025-09-04 07:16:14] [Iter 1085/2250] R3[34/1200], Temp: 0.9980, Energy: -52.157944+0.000535j
[2025-09-04 07:16:54] [Iter 1086/2250] R3[35/1200], Temp: 0.9979, Energy: -52.123788-0.000601j
[2025-09-04 07:17:34] [Iter 1087/2250] R3[36/1200], Temp: 0.9978, Energy: -52.076417-0.001405j
[2025-09-04 07:18:14] [Iter 1088/2250] R3[37/1200], Temp: 0.9977, Energy: -52.089426+0.001780j
[2025-09-04 07:18:54] [Iter 1089/2250] R3[38/1200], Temp: 0.9975, Energy: -52.098794+0.000204j
[2025-09-04 07:19:34] [Iter 1090/2250] R3[39/1200], Temp: 0.9974, Energy: -52.121686-0.002046j
[2025-09-04 07:20:15] [Iter 1091/2250] R3[40/1200], Temp: 0.9973, Energy: -52.118375-0.001030j
[2025-09-04 07:20:55] [Iter 1092/2250] R3[41/1200], Temp: 0.9971, Energy: -52.108762+0.001781j
[2025-09-04 07:21:35] [Iter 1093/2250] R3[42/1200], Temp: 0.9970, Energy: -52.121035+0.000005j
[2025-09-04 07:22:15] [Iter 1094/2250] R3[43/1200], Temp: 0.9968, Energy: -52.069115-0.001188j
[2025-09-04 07:22:55] [Iter 1095/2250] R3[44/1200], Temp: 0.9967, Energy: -52.064774-0.000955j
[2025-09-04 07:23:35] [Iter 1096/2250] R3[45/1200], Temp: 0.9965, Energy: -52.121094-0.000722j
[2025-09-04 07:24:15] [Iter 1097/2250] R3[46/1200], Temp: 0.9964, Energy: -52.113133-0.001040j
[2025-09-04 07:24:55] [Iter 1098/2250] R3[47/1200], Temp: 0.9962, Energy: -52.135981-0.000125j
[2025-09-04 07:25:35] [Iter 1099/2250] R3[48/1200], Temp: 0.9961, Energy: -52.155591-0.000823j
[2025-09-04 07:26:15] [Iter 1100/2250] R3[49/1200], Temp: 0.9959, Energy: -52.128470+0.000259j
[2025-09-04 07:26:55] [Iter 1101/2250] R3[50/1200], Temp: 0.9957, Energy: -52.137325-0.001184j
[2025-09-04 07:27:36] [Iter 1102/2250] R3[51/1200], Temp: 0.9955, Energy: -52.116597-0.000875j
[2025-09-04 07:28:16] [Iter 1103/2250] R3[52/1200], Temp: 0.9954, Energy: -52.105542-0.000845j
[2025-09-04 07:28:56] [Iter 1104/2250] R3[53/1200], Temp: 0.9952, Energy: -52.119072+0.001181j
[2025-09-04 07:29:36] [Iter 1105/2250] R3[54/1200], Temp: 0.9950, Energy: -52.042440-0.001782j
[2025-09-04 07:30:16] [Iter 1106/2250] R3[55/1200], Temp: 0.9948, Energy: -52.108792-0.002430j
[2025-09-04 07:30:56] [Iter 1107/2250] R3[56/1200], Temp: 0.9946, Energy: -52.141027-0.001411j
[2025-09-04 07:31:36] [Iter 1108/2250] R3[57/1200], Temp: 0.9944, Energy: -52.136645+0.000196j
[2025-09-04 07:32:16] [Iter 1109/2250] R3[58/1200], Temp: 0.9942, Energy: -52.121843-0.002468j
[2025-09-04 07:32:56] [Iter 1110/2250] R3[59/1200], Temp: 0.9940, Energy: -52.193916-0.000486j
[2025-09-04 07:33:36] [Iter 1111/2250] R3[60/1200], Temp: 0.9938, Energy: -52.176081-0.001400j
[2025-09-04 07:34:17] [Iter 1112/2250] R3[61/1200], Temp: 0.9936, Energy: -52.158482+0.001207j
[2025-09-04 07:34:57] [Iter 1113/2250] R3[62/1200], Temp: 0.9934, Energy: -52.162480-0.001424j
[2025-09-04 07:35:37] [Iter 1114/2250] R3[63/1200], Temp: 0.9932, Energy: -52.190978-0.001542j
[2025-09-04 07:36:17] [Iter 1115/2250] R3[64/1200], Temp: 0.9930, Energy: -52.122382+0.000741j
[2025-09-04 07:36:57] [Iter 1116/2250] R3[65/1200], Temp: 0.9928, Energy: -52.102459+0.000306j
[2025-09-04 07:37:37] [Iter 1117/2250] R3[66/1200], Temp: 0.9926, Energy: -52.206098+0.000125j
[2025-09-04 07:38:17] [Iter 1118/2250] R3[67/1200], Temp: 0.9923, Energy: -52.160000+0.002072j
[2025-09-04 07:38:57] [Iter 1119/2250] R3[68/1200], Temp: 0.9921, Energy: -52.085734-0.003862j
[2025-09-04 07:39:37] [Iter 1120/2250] R3[69/1200], Temp: 0.9919, Energy: -52.123027-0.000950j
[2025-09-04 07:40:18] [Iter 1121/2250] R3[70/1200], Temp: 0.9916, Energy: -52.108730-0.003149j
[2025-09-04 07:40:58] [Iter 1122/2250] R3[71/1200], Temp: 0.9914, Energy: -52.115231+0.002357j
[2025-09-04 07:41:38] [Iter 1123/2250] R3[72/1200], Temp: 0.9911, Energy: -52.104830-0.001863j
[2025-09-04 07:42:18] [Iter 1124/2250] R3[73/1200], Temp: 0.9909, Energy: -52.127513+0.001241j
[2025-09-04 07:42:58] [Iter 1125/2250] R3[74/1200], Temp: 0.9906, Energy: -52.147436+0.000060j
[2025-09-04 07:43:38] [Iter 1126/2250] R3[75/1200], Temp: 0.9904, Energy: -52.154749-0.001255j
[2025-09-04 07:44:18] [Iter 1127/2250] R3[76/1200], Temp: 0.9901, Energy: -52.118789-0.000519j
[2025-09-04 07:44:58] [Iter 1128/2250] R3[77/1200], Temp: 0.9899, Energy: -52.138888+0.000886j
[2025-09-04 07:45:38] [Iter 1129/2250] R3[78/1200], Temp: 0.9896, Energy: -52.166377-0.000915j
[2025-09-04 07:46:18] [Iter 1130/2250] R3[79/1200], Temp: 0.9893, Energy: -52.194444-0.001002j
[2025-09-04 07:46:58] [Iter 1131/2250] R3[80/1200], Temp: 0.9891, Energy: -52.097310-0.000025j
[2025-09-04 07:47:38] [Iter 1132/2250] R3[81/1200], Temp: 0.9888, Energy: -52.122946+0.000449j
[2025-09-04 07:48:19] [Iter 1133/2250] R3[82/1200], Temp: 0.9885, Energy: -52.030919+0.002612j
[2025-09-04 07:48:59] [Iter 1134/2250] R3[83/1200], Temp: 0.9882, Energy: -52.057739+0.001871j
[2025-09-04 07:49:39] [Iter 1135/2250] R3[84/1200], Temp: 0.9880, Energy: -52.103950-0.001995j
[2025-09-04 07:50:19] [Iter 1136/2250] R3[85/1200], Temp: 0.9877, Energy: -52.128420+0.001501j
[2025-09-04 07:50:59] [Iter 1137/2250] R3[86/1200], Temp: 0.9874, Energy: -52.111188+0.001229j
[2025-09-04 07:51:39] [Iter 1138/2250] R3[87/1200], Temp: 0.9871, Energy: -52.117631+0.000826j
[2025-09-04 07:52:19] [Iter 1139/2250] R3[88/1200], Temp: 0.9868, Energy: -52.090548+0.001288j
[2025-09-04 07:52:59] [Iter 1140/2250] R3[89/1200], Temp: 0.9865, Energy: -52.063488-0.000543j
[2025-09-04 07:53:39] [Iter 1141/2250] R3[90/1200], Temp: 0.9862, Energy: -52.077517+0.001401j
[2025-09-04 07:54:19] [Iter 1142/2250] R3[91/1200], Temp: 0.9859, Energy: -52.056846-0.001573j
[2025-09-04 07:55:00] [Iter 1143/2250] R3[92/1200], Temp: 0.9856, Energy: -52.074530-0.000251j
[2025-09-04 07:55:40] [Iter 1144/2250] R3[93/1200], Temp: 0.9853, Energy: -52.145984-0.001073j
[2025-09-04 07:56:20] [Iter 1145/2250] R3[94/1200], Temp: 0.9849, Energy: -52.076460-0.001194j
[2025-09-04 07:57:00] [Iter 1146/2250] R3[95/1200], Temp: 0.9846, Energy: -52.067929+0.000190j
[2025-09-04 07:57:40] [Iter 1147/2250] R3[96/1200], Temp: 0.9843, Energy: -52.166176-0.001294j
[2025-09-04 07:58:20] [Iter 1148/2250] R3[97/1200], Temp: 0.9840, Energy: -52.076879-0.001600j
[2025-09-04 07:59:00] [Iter 1149/2250] R3[98/1200], Temp: 0.9836, Energy: -52.114183-0.000033j
[2025-09-04 07:59:40] [Iter 1150/2250] R3[99/1200], Temp: 0.9833, Energy: -52.173245-0.000825j
[2025-09-04 08:00:20] [Iter 1151/2250] R3[100/1200], Temp: 0.9830, Energy: -52.113184-0.000500j
[2025-09-04 08:01:00] [Iter 1152/2250] R3[101/1200], Temp: 0.9826, Energy: -52.111985+0.001180j
[2025-09-04 08:01:40] [Iter 1153/2250] R3[102/1200], Temp: 0.9823, Energy: -52.060288+0.000918j
[2025-09-04 08:02:21] [Iter 1154/2250] R3[103/1200], Temp: 0.9819, Energy: -52.065703-0.000139j
[2025-09-04 08:03:01] [Iter 1155/2250] R3[104/1200], Temp: 0.9816, Energy: -52.079870-0.000166j
[2025-09-04 08:03:41] [Iter 1156/2250] R3[105/1200], Temp: 0.9812, Energy: -51.986873+0.000305j
[2025-09-04 08:04:21] [Iter 1157/2250] R3[106/1200], Temp: 0.9809, Energy: -52.007022+0.001821j
[2025-09-04 08:05:01] [Iter 1158/2250] R3[107/1200], Temp: 0.9805, Energy: -52.089380+0.001547j
[2025-09-04 08:05:41] [Iter 1159/2250] R3[108/1200], Temp: 0.9801, Energy: -52.125397-0.000383j
[2025-09-04 08:06:21] [Iter 1160/2250] R3[109/1200], Temp: 0.9798, Energy: -52.074541-0.001528j
[2025-09-04 08:07:01] [Iter 1161/2250] R3[110/1200], Temp: 0.9794, Energy: -52.157582+0.000512j
[2025-09-04 08:07:41] [Iter 1162/2250] R3[111/1200], Temp: 0.9790, Energy: -52.165070+0.000273j
[2025-09-04 08:08:21] [Iter 1163/2250] R3[112/1200], Temp: 0.9787, Energy: -52.130803+0.001051j
[2025-09-04 08:09:01] [Iter 1164/2250] R3[113/1200], Temp: 0.9783, Energy: -52.184020-0.002195j
[2025-09-04 08:09:42] [Iter 1165/2250] R3[114/1200], Temp: 0.9779, Energy: -52.185730+0.001322j
[2025-09-04 08:10:22] [Iter 1166/2250] R3[115/1200], Temp: 0.9775, Energy: -52.189347-0.001623j
[2025-09-04 08:11:02] [Iter 1167/2250] R3[116/1200], Temp: 0.9771, Energy: -52.197736+0.000941j
[2025-09-04 08:11:42] [Iter 1168/2250] R3[117/1200], Temp: 0.9767, Energy: -52.171449-0.002047j
[2025-09-04 08:12:22] [Iter 1169/2250] R3[118/1200], Temp: 0.9763, Energy: -52.183077-0.000896j
[2025-09-04 08:13:02] [Iter 1170/2250] R3[119/1200], Temp: 0.9759, Energy: -52.179410-0.002468j
[2025-09-04 08:13:42] [Iter 1171/2250] R3[120/1200], Temp: 0.9755, Energy: -52.212624-0.001746j
[2025-09-04 08:14:22] [Iter 1172/2250] R3[121/1200], Temp: 0.9751, Energy: -52.122892-0.000148j
[2025-09-04 08:15:02] [Iter 1173/2250] R3[122/1200], Temp: 0.9747, Energy: -52.148777-0.001702j
[2025-09-04 08:15:43] [Iter 1174/2250] R3[123/1200], Temp: 0.9743, Energy: -52.099696+0.000027j
[2025-09-04 08:16:23] [Iter 1175/2250] R3[124/1200], Temp: 0.9739, Energy: -52.154769+0.001770j
[2025-09-04 08:17:03] [Iter 1176/2250] R3[125/1200], Temp: 0.9735, Energy: -52.091962+0.003866j
[2025-09-04 08:17:23] [Iter 1177/2250] R3[126/1200], Temp: 0.9730, Energy: -52.097414-0.000881j
[2025-09-04 08:17:42] [Iter 1178/2250] R3[127/1200], Temp: 0.9726, Energy: -52.096018+0.000346j
[2025-09-04 08:18:05] [Iter 1179/2250] R3[128/1200], Temp: 0.9722, Energy: -52.142942+0.001098j
[2025-09-04 08:18:29] [Iter 1180/2250] R3[129/1200], Temp: 0.9718, Energy: -52.068255+0.000112j
[2025-09-04 08:18:52] [Iter 1181/2250] R3[130/1200], Temp: 0.9713, Energy: -52.090822+0.000312j
[2025-09-04 08:19:29] [Iter 1182/2250] R3[131/1200], Temp: 0.9709, Energy: -52.114217+0.002965j
[2025-09-04 08:20:09] [Iter 1183/2250] R3[132/1200], Temp: 0.9704, Energy: -52.143862-0.001993j
[2025-09-04 08:20:49] [Iter 1184/2250] R3[133/1200], Temp: 0.9700, Energy: -52.116197-0.001095j
[2025-09-04 08:21:29] [Iter 1185/2250] R3[134/1200], Temp: 0.9695, Energy: -52.149809+0.000483j
[2025-09-04 08:22:09] [Iter 1186/2250] R3[135/1200], Temp: 0.9691, Energy: -52.113053+0.000647j
[2025-09-04 08:22:49] [Iter 1187/2250] R3[136/1200], Temp: 0.9686, Energy: -52.063248+0.002123j
[2025-09-04 08:23:29] [Iter 1188/2250] R3[137/1200], Temp: 0.9682, Energy: -52.025468-0.000215j
[2025-09-04 08:24:09] [Iter 1189/2250] R3[138/1200], Temp: 0.9677, Energy: -52.151969-0.000747j
[2025-09-04 08:24:49] [Iter 1190/2250] R3[139/1200], Temp: 0.9673, Energy: -52.122925+0.001212j
[2025-09-04 08:25:29] [Iter 1191/2250] R3[140/1200], Temp: 0.9668, Energy: -52.162676-0.001465j
[2025-09-04 08:26:10] [Iter 1192/2250] R3[141/1200], Temp: 0.9663, Energy: -52.148434+0.000071j
[2025-09-04 08:26:50] [Iter 1193/2250] R3[142/1200], Temp: 0.9658, Energy: -52.065840+0.001230j
[2025-09-04 08:27:30] [Iter 1194/2250] R3[143/1200], Temp: 0.9654, Energy: -52.138407+0.000948j
[2025-09-04 08:28:10] [Iter 1195/2250] R3[144/1200], Temp: 0.9649, Energy: -52.135771+0.000375j
[2025-09-04 08:28:50] [Iter 1196/2250] R3[145/1200], Temp: 0.9644, Energy: -52.164657+0.002469j
[2025-09-04 08:29:30] [Iter 1197/2250] R3[146/1200], Temp: 0.9639, Energy: -52.196564-0.001245j
[2025-09-04 08:30:10] [Iter 1198/2250] R3[147/1200], Temp: 0.9634, Energy: -52.217363+0.000569j
[2025-09-04 08:30:50] [Iter 1199/2250] R3[148/1200], Temp: 0.9629, Energy: -52.223170+0.000142j
[2025-09-04 08:31:30] [Iter 1200/2250] R3[149/1200], Temp: 0.9624, Energy: -52.146037-0.000598j
[2025-09-04 08:32:10] [Iter 1201/2250] R3[150/1200], Temp: 0.9619, Energy: -52.198864-0.000124j
[2025-09-04 08:32:50] [Iter 1202/2250] R3[151/1200], Temp: 0.9614, Energy: -52.197968-0.000658j
[2025-09-04 08:33:31] [Iter 1203/2250] R3[152/1200], Temp: 0.9609, Energy: -52.124862+0.000818j
[2025-09-04 08:34:11] [Iter 1204/2250] R3[153/1200], Temp: 0.9604, Energy: -52.140625-0.001895j
[2025-09-04 08:34:51] [Iter 1205/2250] R3[154/1200], Temp: 0.9599, Energy: -52.125378+0.002765j
[2025-09-04 08:35:31] [Iter 1206/2250] R3[155/1200], Temp: 0.9594, Energy: -52.111798+0.000109j
[2025-09-04 08:36:11] [Iter 1207/2250] R3[156/1200], Temp: 0.9589, Energy: -52.177257-0.000547j
[2025-09-04 08:36:51] [Iter 1208/2250] R3[157/1200], Temp: 0.9584, Energy: -52.190559-0.000358j
[2025-09-04 08:37:31] [Iter 1209/2250] R3[158/1200], Temp: 0.9578, Energy: -52.177401+0.003091j
[2025-09-04 08:38:11] [Iter 1210/2250] R3[159/1200], Temp: 0.9573, Energy: -52.187428-0.000900j
[2025-09-04 08:38:51] [Iter 1211/2250] R3[160/1200], Temp: 0.9568, Energy: -52.124531+0.002680j
[2025-09-04 08:39:31] [Iter 1212/2250] R3[161/1200], Temp: 0.9562, Energy: -52.121252-0.001662j
[2025-09-04 08:40:11] [Iter 1213/2250] R3[162/1200], Temp: 0.9557, Energy: -52.117196-0.002503j
[2025-09-04 08:40:51] [Iter 1214/2250] R3[163/1200], Temp: 0.9552, Energy: -52.108906-0.001408j
[2025-09-04 08:41:31] [Iter 1215/2250] R3[164/1200], Temp: 0.9546, Energy: -52.161866+0.002118j
[2025-09-04 08:42:12] [Iter 1216/2250] R3[165/1200], Temp: 0.9541, Energy: -52.136223+0.000251j
[2025-09-04 08:42:52] [Iter 1217/2250] R3[166/1200], Temp: 0.9535, Energy: -52.152378-0.003494j
[2025-09-04 08:43:32] [Iter 1218/2250] R3[167/1200], Temp: 0.9530, Energy: -52.093060-0.000040j
[2025-09-04 08:44:12] [Iter 1219/2250] R3[168/1200], Temp: 0.9524, Energy: -52.114899+0.001568j
[2025-09-04 08:44:52] [Iter 1220/2250] R3[169/1200], Temp: 0.9519, Energy: -52.126182-0.001846j
[2025-09-04 08:45:32] [Iter 1221/2250] R3[170/1200], Temp: 0.9513, Energy: -52.065783-0.002407j
[2025-09-04 08:46:12] [Iter 1222/2250] R3[171/1200], Temp: 0.9507, Energy: -52.040481-0.000306j
[2025-09-04 08:46:52] [Iter 1223/2250] R3[172/1200], Temp: 0.9502, Energy: -52.095613-0.001771j
[2025-09-04 08:47:32] [Iter 1224/2250] R3[173/1200], Temp: 0.9496, Energy: -52.137947+0.001919j
[2025-09-04 08:48:12] [Iter 1225/2250] R3[174/1200], Temp: 0.9490, Energy: -52.065667-0.002017j
[2025-09-04 08:48:53] [Iter 1226/2250] R3[175/1200], Temp: 0.9484, Energy: -52.094728+0.000868j
[2025-09-04 08:49:33] [Iter 1227/2250] R3[176/1200], Temp: 0.9479, Energy: -52.077116+0.000942j
[2025-09-04 08:50:13] [Iter 1228/2250] R3[177/1200], Temp: 0.9473, Energy: -52.093679-0.000025j
[2025-09-04 08:50:53] [Iter 1229/2250] R3[178/1200], Temp: 0.9467, Energy: -52.118527+0.000795j
[2025-09-04 08:51:33] [Iter 1230/2250] R3[179/1200], Temp: 0.9461, Energy: -52.112054-0.000876j
[2025-09-04 08:52:13] [Iter 1231/2250] R3[180/1200], Temp: 0.9455, Energy: -52.135815-0.000626j
[2025-09-04 08:52:53] [Iter 1232/2250] R3[181/1200], Temp: 0.9449, Energy: -52.104805-0.000463j
[2025-09-04 08:53:33] [Iter 1233/2250] R3[182/1200], Temp: 0.9443, Energy: -52.148048-0.002238j
[2025-09-04 08:54:13] [Iter 1234/2250] R3[183/1200], Temp: 0.9437, Energy: -52.114840-0.000119j
[2025-09-04 08:54:53] [Iter 1235/2250] R3[184/1200], Temp: 0.9431, Energy: -52.139538+0.000636j
[2025-09-04 08:55:33] [Iter 1236/2250] R3[185/1200], Temp: 0.9425, Energy: -52.090259+0.002039j
[2025-09-04 08:56:13] [Iter 1237/2250] R3[186/1200], Temp: 0.9419, Energy: -52.123901-0.000155j
[2025-09-04 08:56:54] [Iter 1238/2250] R3[187/1200], Temp: 0.9413, Energy: -52.085118-0.000737j
[2025-09-04 08:57:34] [Iter 1239/2250] R3[188/1200], Temp: 0.9407, Energy: -52.116831-0.000046j
[2025-09-04 08:58:14] [Iter 1240/2250] R3[189/1200], Temp: 0.9400, Energy: -52.085367-0.000504j
[2025-09-04 08:58:54] [Iter 1241/2250] R3[190/1200], Temp: 0.9394, Energy: -52.118919-0.000386j
[2025-09-04 08:59:34] [Iter 1242/2250] R3[191/1200], Temp: 0.9388, Energy: -52.160144+0.002658j
[2025-09-04 09:00:14] [Iter 1243/2250] R3[192/1200], Temp: 0.9382, Energy: -52.197467-0.001636j
[2025-09-04 09:00:54] [Iter 1244/2250] R3[193/1200], Temp: 0.9375, Energy: -52.235342-0.002672j
[2025-09-04 09:01:34] [Iter 1245/2250] R3[194/1200], Temp: 0.9369, Energy: -52.222616+0.000039j
[2025-09-04 09:02:14] [Iter 1246/2250] R3[195/1200], Temp: 0.9362, Energy: -52.206624-0.001649j
[2025-09-04 09:02:54] [Iter 1247/2250] R3[196/1200], Temp: 0.9356, Energy: -52.142458-0.000362j
[2025-09-04 09:03:34] [Iter 1248/2250] R3[197/1200], Temp: 0.9350, Energy: -52.081335-0.002045j
[2025-09-04 09:04:15] [Iter 1249/2250] R3[198/1200], Temp: 0.9343, Energy: -52.137142-0.000178j
[2025-09-04 09:04:55] [Iter 1250/2250] R3[199/1200], Temp: 0.9337, Energy: -52.102178+0.000097j
[2025-09-04 09:04:55] ✓ Checkpoint saved: checkpoint_iter_001250.pkl
[2025-09-04 09:05:35] [Iter 1251/2250] R3[200/1200], Temp: 0.9330, Energy: -52.203980-0.001282j
[2025-09-04 09:06:15] [Iter 1252/2250] R3[201/1200], Temp: 0.9324, Energy: -52.205846+0.000447j
[2025-09-04 09:06:55] [Iter 1253/2250] R3[202/1200], Temp: 0.9317, Energy: -52.150046+0.001520j
[2025-09-04 09:07:35] [Iter 1254/2250] R3[203/1200], Temp: 0.9310, Energy: -52.137631-0.000023j
[2025-09-04 09:08:15] [Iter 1255/2250] R3[204/1200], Temp: 0.9304, Energy: -52.158639+0.002132j
[2025-09-04 09:08:55] [Iter 1256/2250] R3[205/1200], Temp: 0.9297, Energy: -52.127818+0.000850j
[2025-09-04 09:09:35] [Iter 1257/2250] R3[206/1200], Temp: 0.9290, Energy: -52.095600+0.001952j
[2025-09-04 09:10:15] [Iter 1258/2250] R3[207/1200], Temp: 0.9284, Energy: -52.139565-0.000761j
[2025-09-04 09:10:55] [Iter 1259/2250] R3[208/1200], Temp: 0.9277, Energy: -52.147979-0.001681j
[2025-09-04 09:11:36] [Iter 1260/2250] R3[209/1200], Temp: 0.9270, Energy: -52.125048+0.000616j
[2025-09-04 09:12:16] [Iter 1261/2250] R3[210/1200], Temp: 0.9263, Energy: -52.178454-0.000646j
[2025-09-04 09:12:56] [Iter 1262/2250] R3[211/1200], Temp: 0.9256, Energy: -52.105927+0.000796j
[2025-09-04 09:13:36] [Iter 1263/2250] R3[212/1200], Temp: 0.9249, Energy: -52.103213+0.001035j
[2025-09-04 09:14:16] [Iter 1264/2250] R3[213/1200], Temp: 0.9243, Energy: -52.130288-0.001622j
[2025-09-04 09:14:56] [Iter 1265/2250] R3[214/1200], Temp: 0.9236, Energy: -52.099637-0.001017j
[2025-09-04 09:15:36] [Iter 1266/2250] R3[215/1200], Temp: 0.9229, Energy: -52.158245-0.001439j
[2025-09-04 09:16:16] [Iter 1267/2250] R3[216/1200], Temp: 0.9222, Energy: -52.189701-0.000164j
[2025-09-04 09:16:56] [Iter 1268/2250] R3[217/1200], Temp: 0.9215, Energy: -52.087473+0.000162j
[2025-09-04 09:17:36] [Iter 1269/2250] R3[218/1200], Temp: 0.9208, Energy: -52.106731+0.000654j
[2025-09-04 09:18:16] [Iter 1270/2250] R3[219/1200], Temp: 0.9200, Energy: -52.044107-0.001170j
[2025-09-04 09:18:57] [Iter 1271/2250] R3[220/1200], Temp: 0.9193, Energy: -52.089097-0.000163j
[2025-09-04 09:19:37] [Iter 1272/2250] R3[221/1200], Temp: 0.9186, Energy: -52.167050-0.001727j
[2025-09-04 09:20:17] [Iter 1273/2250] R3[222/1200], Temp: 0.9179, Energy: -52.077220-0.000821j
[2025-09-04 09:20:57] [Iter 1274/2250] R3[223/1200], Temp: 0.9172, Energy: -52.138481-0.000311j
[2025-09-04 09:21:37] [Iter 1275/2250] R3[224/1200], Temp: 0.9165, Energy: -52.090672+0.001983j
[2025-09-04 09:22:17] [Iter 1276/2250] R3[225/1200], Temp: 0.9157, Energy: -52.106698+0.000811j
[2025-09-04 09:22:57] [Iter 1277/2250] R3[226/1200], Temp: 0.9150, Energy: -52.127285-0.001176j
[2025-09-04 09:23:37] [Iter 1278/2250] R3[227/1200], Temp: 0.9143, Energy: -52.144854-0.002654j
[2025-09-04 09:24:17] [Iter 1279/2250] R3[228/1200], Temp: 0.9135, Energy: -52.097349+0.000520j
[2025-09-04 09:24:57] [Iter 1280/2250] R3[229/1200], Temp: 0.9128, Energy: -52.146460-0.002072j
[2025-09-04 09:25:38] [Iter 1281/2250] R3[230/1200], Temp: 0.9121, Energy: -52.081018+0.001377j
[2025-09-04 09:26:18] [Iter 1282/2250] R3[231/1200], Temp: 0.9113, Energy: -52.099867+0.001346j
[2025-09-04 09:26:58] [Iter 1283/2250] R3[232/1200], Temp: 0.9106, Energy: -52.096536+0.000739j
[2025-09-04 09:27:38] [Iter 1284/2250] R3[233/1200], Temp: 0.9098, Energy: -52.064711-0.001176j
[2025-09-04 09:28:18] [Iter 1285/2250] R3[234/1200], Temp: 0.9091, Energy: -52.147165-0.001389j
[2025-09-04 09:28:58] [Iter 1286/2250] R3[235/1200], Temp: 0.9083, Energy: -52.152237+0.001582j
[2025-09-04 09:29:38] [Iter 1287/2250] R3[236/1200], Temp: 0.9076, Energy: -52.183312-0.001305j
[2025-09-04 09:30:18] [Iter 1288/2250] R3[237/1200], Temp: 0.9068, Energy: -52.169463+0.001619j
[2025-09-04 09:30:58] [Iter 1289/2250] R3[238/1200], Temp: 0.9060, Energy: -52.125147+0.003753j
[2025-09-04 09:31:38] [Iter 1290/2250] R3[239/1200], Temp: 0.9053, Energy: -52.150383+0.000558j
[2025-09-04 09:32:18] [Iter 1291/2250] R3[240/1200], Temp: 0.9045, Energy: -52.090249-0.002491j
[2025-09-04 09:32:58] [Iter 1292/2250] R3[241/1200], Temp: 0.9037, Energy: -52.145863+0.000371j
[2025-09-04 09:33:38] [Iter 1293/2250] R3[242/1200], Temp: 0.9030, Energy: -52.090196-0.001364j
[2025-09-04 09:34:19] [Iter 1294/2250] R3[243/1200], Temp: 0.9022, Energy: -52.140760+0.001115j
[2025-09-04 09:34:59] [Iter 1295/2250] R3[244/1200], Temp: 0.9014, Energy: -52.126336-0.002623j
[2025-09-04 09:35:39] [Iter 1296/2250] R3[245/1200], Temp: 0.9006, Energy: -52.106357-0.000083j
[2025-09-04 09:36:19] [Iter 1297/2250] R3[246/1200], Temp: 0.8998, Energy: -52.169567-0.001532j
[2025-09-04 09:36:59] [Iter 1298/2250] R3[247/1200], Temp: 0.8991, Energy: -52.084675-0.000541j
[2025-09-04 09:37:39] [Iter 1299/2250] R3[248/1200], Temp: 0.8983, Energy: -52.073216-0.000598j
[2025-09-04 09:38:19] [Iter 1300/2250] R3[249/1200], Temp: 0.8975, Energy: -52.109213+0.001126j
[2025-09-04 09:38:59] [Iter 1301/2250] R3[250/1200], Temp: 0.8967, Energy: -52.108812-0.000248j
[2025-09-04 09:39:39] [Iter 1302/2250] R3[251/1200], Temp: 0.8959, Energy: -52.121956-0.000327j
[2025-09-04 09:40:19] [Iter 1303/2250] R3[252/1200], Temp: 0.8951, Energy: -52.133289+0.000233j
[2025-09-04 09:40:59] [Iter 1304/2250] R3[253/1200], Temp: 0.8943, Energy: -52.149800+0.001041j
[2025-09-04 09:41:40] [Iter 1305/2250] R3[254/1200], Temp: 0.8935, Energy: -52.129701-0.001663j
[2025-09-04 09:42:20] [Iter 1306/2250] R3[255/1200], Temp: 0.8927, Energy: -52.108214+0.000294j
[2025-09-04 09:43:00] [Iter 1307/2250] R3[256/1200], Temp: 0.8918, Energy: -52.157939+0.000492j
[2025-09-04 09:43:40] [Iter 1308/2250] R3[257/1200], Temp: 0.8910, Energy: -52.162629-0.001886j
[2025-09-04 09:44:20] [Iter 1309/2250] R3[258/1200], Temp: 0.8902, Energy: -52.142638-0.000181j
[2025-09-04 09:45:00] [Iter 1310/2250] R3[259/1200], Temp: 0.8894, Energy: -52.120686+0.000445j
[2025-09-04 09:45:40] [Iter 1311/2250] R3[260/1200], Temp: 0.8886, Energy: -52.142079+0.001079j
[2025-09-04 09:46:20] [Iter 1312/2250] R3[261/1200], Temp: 0.8877, Energy: -52.137685-0.000301j
[2025-09-04 09:47:00] [Iter 1313/2250] R3[262/1200], Temp: 0.8869, Energy: -52.104741-0.000772j
[2025-09-04 09:47:40] [Iter 1314/2250] R3[263/1200], Temp: 0.8861, Energy: -52.150832-0.000771j
[2025-09-04 09:48:20] [Iter 1315/2250] R3[264/1200], Temp: 0.8853, Energy: -52.204852-0.000954j
[2025-09-04 09:49:01] [Iter 1316/2250] R3[265/1200], Temp: 0.8844, Energy: -52.173909-0.000254j
[2025-09-04 09:49:41] [Iter 1317/2250] R3[266/1200], Temp: 0.8836, Energy: -52.059750-0.002785j
[2025-09-04 09:50:21] [Iter 1318/2250] R3[267/1200], Temp: 0.8827, Energy: -52.176182-0.000636j
[2025-09-04 09:51:01] [Iter 1319/2250] R3[268/1200], Temp: 0.8819, Energy: -52.157588+0.002000j
[2025-09-04 09:51:41] [Iter 1320/2250] R3[269/1200], Temp: 0.8811, Energy: -52.199874-0.000391j
[2025-09-04 09:52:21] [Iter 1321/2250] R3[270/1200], Temp: 0.8802, Energy: -52.154849-0.001446j
[2025-09-04 09:53:01] [Iter 1322/2250] R3[271/1200], Temp: 0.8794, Energy: -52.158555-0.001426j
[2025-09-04 09:53:41] [Iter 1323/2250] R3[272/1200], Temp: 0.8785, Energy: -52.100589+0.000590j
[2025-09-04 09:54:21] [Iter 1324/2250] R3[273/1200], Temp: 0.8776, Energy: -52.133602-0.000680j
[2025-09-04 09:55:01] [Iter 1325/2250] R3[274/1200], Temp: 0.8768, Energy: -52.174948-0.004684j
[2025-09-04 09:55:42] [Iter 1326/2250] R3[275/1200], Temp: 0.8759, Energy: -52.155603-0.000550j
[2025-09-04 09:56:22] [Iter 1327/2250] R3[276/1200], Temp: 0.8751, Energy: -52.172409-0.000322j
[2025-09-04 09:57:02] [Iter 1328/2250] R3[277/1200], Temp: 0.8742, Energy: -52.171124-0.002661j
[2025-09-04 09:57:42] [Iter 1329/2250] R3[278/1200], Temp: 0.8733, Energy: -52.147220+0.000896j
[2025-09-04 09:58:22] [Iter 1330/2250] R3[279/1200], Temp: 0.8724, Energy: -52.135014+0.000944j
[2025-09-04 09:59:02] [Iter 1331/2250] R3[280/1200], Temp: 0.8716, Energy: -52.126900-0.000334j
[2025-09-04 09:59:42] [Iter 1332/2250] R3[281/1200], Temp: 0.8707, Energy: -52.141543-0.002642j
[2025-09-04 10:00:22] [Iter 1333/2250] R3[282/1200], Temp: 0.8698, Energy: -52.143964+0.000870j
[2025-09-04 10:01:02] [Iter 1334/2250] R3[283/1200], Temp: 0.8689, Energy: -52.107902-0.000705j
[2025-09-04 10:01:42] [Iter 1335/2250] R3[284/1200], Temp: 0.8680, Energy: -52.119715-0.001170j
[2025-09-04 10:02:22] [Iter 1336/2250] R3[285/1200], Temp: 0.8672, Energy: -52.139596-0.002313j
[2025-09-04 10:03:02] [Iter 1337/2250] R3[286/1200], Temp: 0.8663, Energy: -52.159138+0.002649j
[2025-09-04 10:03:42] [Iter 1338/2250] R3[287/1200], Temp: 0.8654, Energy: -52.136581+0.000807j
[2025-09-04 10:04:23] [Iter 1339/2250] R3[288/1200], Temp: 0.8645, Energy: -52.138509-0.001397j
[2025-09-04 10:05:03] [Iter 1340/2250] R3[289/1200], Temp: 0.8636, Energy: -52.149394-0.000482j
[2025-09-04 10:05:43] [Iter 1341/2250] R3[290/1200], Temp: 0.8627, Energy: -52.094362+0.002030j
[2025-09-04 10:06:23] [Iter 1342/2250] R3[291/1200], Temp: 0.8618, Energy: -52.071725+0.003396j
[2025-09-04 10:07:03] [Iter 1343/2250] R3[292/1200], Temp: 0.8609, Energy: -52.050759-0.000787j
[2025-09-04 10:07:43] [Iter 1344/2250] R3[293/1200], Temp: 0.8600, Energy: -52.046747-0.001506j
[2025-09-04 10:08:23] [Iter 1345/2250] R3[294/1200], Temp: 0.8591, Energy: -52.100256-0.001563j
[2025-09-04 10:09:03] [Iter 1346/2250] R3[295/1200], Temp: 0.8582, Energy: -52.110538-0.001149j
[2025-09-04 10:09:43] [Iter 1347/2250] R3[296/1200], Temp: 0.8572, Energy: -52.088409+0.000737j
[2025-09-04 10:10:23] [Iter 1348/2250] R3[297/1200], Temp: 0.8563, Energy: -52.103191-0.000302j
[2025-09-04 10:11:03] [Iter 1349/2250] R3[298/1200], Temp: 0.8554, Energy: -52.130201+0.002756j
[2025-09-04 10:11:43] [Iter 1350/2250] R3[299/1200], Temp: 0.8545, Energy: -52.115202-0.000797j
[2025-09-04 10:12:23] [Iter 1351/2250] R3[300/1200], Temp: 0.8536, Energy: -52.093548+0.000801j
[2025-09-04 10:13:04] [Iter 1352/2250] R3[301/1200], Temp: 0.8526, Energy: -52.131757+0.001298j
[2025-09-04 10:13:44] [Iter 1353/2250] R3[302/1200], Temp: 0.8517, Energy: -52.116759+0.000957j
[2025-09-04 10:14:24] [Iter 1354/2250] R3[303/1200], Temp: 0.8508, Energy: -52.139895-0.001406j
[2025-09-04 10:15:04] [Iter 1355/2250] R3[304/1200], Temp: 0.8498, Energy: -52.120899+0.001493j
[2025-09-04 10:15:44] [Iter 1356/2250] R3[305/1200], Temp: 0.8489, Energy: -52.114949-0.001399j
[2025-09-04 10:16:24] [Iter 1357/2250] R3[306/1200], Temp: 0.8480, Energy: -52.112600+0.000845j
[2025-09-04 10:17:04] [Iter 1358/2250] R3[307/1200], Temp: 0.8470, Energy: -52.145271-0.000882j
[2025-09-04 10:17:44] [Iter 1359/2250] R3[308/1200], Temp: 0.8461, Energy: -52.123943-0.001027j
[2025-09-04 10:18:24] [Iter 1360/2250] R3[309/1200], Temp: 0.8451, Energy: -52.072902-0.001682j
[2025-09-04 10:19:04] [Iter 1361/2250] R3[310/1200], Temp: 0.8442, Energy: -52.127327-0.001635j
[2025-09-04 10:19:44] [Iter 1362/2250] R3[311/1200], Temp: 0.8432, Energy: -52.092210-0.002331j
[2025-09-04 10:20:25] [Iter 1363/2250] R3[312/1200], Temp: 0.8423, Energy: -52.122262-0.001070j
[2025-09-04 10:21:05] [Iter 1364/2250] R3[313/1200], Temp: 0.8413, Energy: -52.107066+0.000909j
[2025-09-04 10:21:45] [Iter 1365/2250] R3[314/1200], Temp: 0.8404, Energy: -52.143297-0.000017j
[2025-09-04 10:22:25] [Iter 1366/2250] R3[315/1200], Temp: 0.8394, Energy: -52.134879+0.001031j
[2025-09-04 10:23:05] [Iter 1367/2250] R3[316/1200], Temp: 0.8384, Energy: -52.237382+0.001438j
[2025-09-04 10:23:45] [Iter 1368/2250] R3[317/1200], Temp: 0.8375, Energy: -52.183477+0.000384j
[2025-09-04 10:24:25] [Iter 1369/2250] R3[318/1200], Temp: 0.8365, Energy: -52.174809+0.003878j
[2025-09-04 10:25:05] [Iter 1370/2250] R3[319/1200], Temp: 0.8355, Energy: -52.103435+0.003751j
[2025-09-04 10:25:45] [Iter 1371/2250] R3[320/1200], Temp: 0.8346, Energy: -52.117501+0.000479j
[2025-09-04 10:26:25] [Iter 1372/2250] R3[321/1200], Temp: 0.8336, Energy: -52.155299+0.001492j
[2025-09-04 10:27:06] [Iter 1373/2250] R3[322/1200], Temp: 0.8326, Energy: -52.135426+0.003358j
[2025-09-04 10:27:46] [Iter 1374/2250] R3[323/1200], Temp: 0.8316, Energy: -52.113225+0.000787j
[2025-09-04 10:28:26] [Iter 1375/2250] R3[324/1200], Temp: 0.8307, Energy: -52.038200+0.001405j
[2025-09-04 10:29:06] [Iter 1376/2250] R3[325/1200], Temp: 0.8297, Energy: -52.102455-0.001229j
[2025-09-04 10:29:46] [Iter 1377/2250] R3[326/1200], Temp: 0.8287, Energy: -52.090645-0.000300j
[2025-09-04 10:30:26] [Iter 1378/2250] R3[327/1200], Temp: 0.8277, Energy: -52.077516-0.002401j
[2025-09-04 10:31:06] [Iter 1379/2250] R3[328/1200], Temp: 0.8267, Energy: -52.138430+0.000492j
[2025-09-04 10:31:46] [Iter 1380/2250] R3[329/1200], Temp: 0.8257, Energy: -52.150934+0.000031j
[2025-09-04 10:32:26] [Iter 1381/2250] R3[330/1200], Temp: 0.8247, Energy: -52.090551-0.000556j
[2025-09-04 10:33:06] [Iter 1382/2250] R3[331/1200], Temp: 0.8237, Energy: -52.121754-0.000286j
[2025-09-04 10:33:47] [Iter 1383/2250] R3[332/1200], Temp: 0.8227, Energy: -52.116578-0.001323j
[2025-09-04 10:34:27] [Iter 1384/2250] R3[333/1200], Temp: 0.8217, Energy: -52.107197+0.000954j
[2025-09-04 10:35:07] [Iter 1385/2250] R3[334/1200], Temp: 0.8207, Energy: -52.114125-0.003744j
[2025-09-04 10:35:47] [Iter 1386/2250] R3[335/1200], Temp: 0.8197, Energy: -52.138107-0.000289j
[2025-09-04 10:36:27] [Iter 1387/2250] R3[336/1200], Temp: 0.8187, Energy: -52.089686+0.001564j
[2025-09-04 10:37:07] [Iter 1388/2250] R3[337/1200], Temp: 0.8177, Energy: -52.086981+0.002079j
[2025-09-04 10:37:47] [Iter 1389/2250] R3[338/1200], Temp: 0.8167, Energy: -52.107014-0.000071j
[2025-09-04 10:38:27] [Iter 1390/2250] R3[339/1200], Temp: 0.8157, Energy: -52.092413-0.001953j
[2025-09-04 10:39:07] [Iter 1391/2250] R3[340/1200], Temp: 0.8147, Energy: -52.018996+0.000120j
[2025-09-04 10:39:47] [Iter 1392/2250] R3[341/1200], Temp: 0.8136, Energy: -52.078503+0.001170j
[2025-09-04 10:40:27] [Iter 1393/2250] R3[342/1200], Temp: 0.8126, Energy: -52.068730+0.004169j
[2025-09-04 10:41:08] [Iter 1394/2250] R3[343/1200], Temp: 0.8116, Energy: -52.102081-0.000909j
[2025-09-04 10:41:48] [Iter 1395/2250] R3[344/1200], Temp: 0.8106, Energy: -52.096152-0.000259j
[2025-09-04 10:42:28] [Iter 1396/2250] R3[345/1200], Temp: 0.8095, Energy: -52.118891+0.001686j
[2025-09-04 10:43:08] [Iter 1397/2250] R3[346/1200], Temp: 0.8085, Energy: -52.076637-0.004193j
[2025-09-04 10:43:48] [Iter 1398/2250] R3[347/1200], Temp: 0.8075, Energy: -52.064993-0.001108j
[2025-09-04 10:44:28] [Iter 1399/2250] R3[348/1200], Temp: 0.8065, Energy: -52.078558+0.000053j
[2025-09-04 10:45:08] [Iter 1400/2250] R3[349/1200], Temp: 0.8054, Energy: -52.139423+0.000116j
[2025-09-04 10:45:48] [Iter 1401/2250] R3[350/1200], Temp: 0.8044, Energy: -52.129047+0.003739j
[2025-09-04 10:46:28] [Iter 1402/2250] R3[351/1200], Temp: 0.8033, Energy: -52.181942+0.004151j
[2025-09-04 10:47:08] [Iter 1403/2250] R3[352/1200], Temp: 0.8023, Energy: -52.120230+0.000394j
[2025-09-04 10:47:48] [Iter 1404/2250] R3[353/1200], Temp: 0.8013, Energy: -52.167501-0.000892j
[2025-09-04 10:48:29] [Iter 1405/2250] R3[354/1200], Temp: 0.8002, Energy: -52.149273-0.000623j
[2025-09-04 10:49:09] [Iter 1406/2250] R3[355/1200], Temp: 0.7992, Energy: -52.049975+0.000586j
[2025-09-04 10:49:49] [Iter 1407/2250] R3[356/1200], Temp: 0.7981, Energy: -52.079411+0.000704j
[2025-09-04 10:50:29] [Iter 1408/2250] R3[357/1200], Temp: 0.7971, Energy: -52.122484+0.001355j
[2025-09-04 10:51:09] [Iter 1409/2250] R3[358/1200], Temp: 0.7960, Energy: -52.110037-0.001060j
[2025-09-04 10:51:49] [Iter 1410/2250] R3[359/1200], Temp: 0.7950, Energy: -52.132987+0.001809j
[2025-09-04 10:52:29] [Iter 1411/2250] R3[360/1200], Temp: 0.7939, Energy: -52.099606-0.000767j
[2025-09-04 10:53:09] [Iter 1412/2250] R3[361/1200], Temp: 0.7928, Energy: -52.036782+0.001153j
[2025-09-04 10:53:49] [Iter 1413/2250] R3[362/1200], Temp: 0.7918, Energy: -52.131181+0.001669j
[2025-09-04 10:54:19] [Iter 1414/2250] R3[363/1200], Temp: 0.7907, Energy: -52.202891+0.000928j
[2025-09-04 10:54:37] [Iter 1415/2250] R3[364/1200], Temp: 0.7896, Energy: -52.185567-0.001964j
[2025-09-04 10:54:55] [Iter 1416/2250] R3[365/1200], Temp: 0.7886, Energy: -52.132746+0.001096j
[2025-09-04 10:55:13] [Iter 1417/2250] R3[366/1200], Temp: 0.7875, Energy: -52.114645-0.000950j
[2025-09-04 10:55:30] [Iter 1418/2250] R3[367/1200], Temp: 0.7864, Energy: -52.172909-0.002327j
[2025-09-04 10:55:48] [Iter 1419/2250] R3[368/1200], Temp: 0.7854, Energy: -52.209316+0.001353j
[2025-09-04 10:56:06] [Iter 1420/2250] R3[369/1200], Temp: 0.7843, Energy: -52.186941+0.001564j
[2025-09-04 10:56:24] [Iter 1421/2250] R3[370/1200], Temp: 0.7832, Energy: -52.131293-0.000972j
[2025-09-04 10:56:42] [Iter 1422/2250] R3[371/1200], Temp: 0.7821, Energy: -52.167938-0.000641j
[2025-09-04 10:57:00] [Iter 1423/2250] R3[372/1200], Temp: 0.7810, Energy: -52.191756+0.001615j
[2025-09-04 10:57:18] [Iter 1424/2250] R3[373/1200], Temp: 0.7800, Energy: -52.130017-0.001556j
[2025-09-04 10:57:36] [Iter 1425/2250] R3[374/1200], Temp: 0.7789, Energy: -52.175260+0.000455j
[2025-09-04 10:57:54] [Iter 1426/2250] R3[375/1200], Temp: 0.7778, Energy: -52.133849+0.001227j
[2025-09-04 10:58:12] [Iter 1427/2250] R3[376/1200], Temp: 0.7767, Energy: -52.120482+0.001369j
[2025-09-04 10:58:30] [Iter 1428/2250] R3[377/1200], Temp: 0.7756, Energy: -52.141622-0.001384j
[2025-09-04 10:58:48] [Iter 1429/2250] R3[378/1200], Temp: 0.7745, Energy: -52.139227+0.003390j
[2025-09-04 10:59:06] [Iter 1430/2250] R3[379/1200], Temp: 0.7734, Energy: -52.165025-0.000569j
[2025-09-04 10:59:24] [Iter 1431/2250] R3[380/1200], Temp: 0.7723, Energy: -52.112776+0.001540j
[2025-09-04 10:59:42] [Iter 1432/2250] R3[381/1200], Temp: 0.7712, Energy: -52.154780-0.000482j
[2025-09-04 11:00:00] [Iter 1433/2250] R3[382/1200], Temp: 0.7701, Energy: -52.179263-0.000956j
[2025-09-04 11:00:18] [Iter 1434/2250] R3[383/1200], Temp: 0.7690, Energy: -52.098984-0.002136j
[2025-09-04 11:00:36] [Iter 1435/2250] R3[384/1200], Temp: 0.7679, Energy: -52.127111-0.001233j
[2025-09-04 11:00:54] [Iter 1436/2250] R3[385/1200], Temp: 0.7668, Energy: -52.110971-0.000937j
[2025-09-04 11:01:12] [Iter 1437/2250] R3[386/1200], Temp: 0.7657, Energy: -52.071432+0.001712j
[2025-09-04 11:01:30] [Iter 1438/2250] R3[387/1200], Temp: 0.7646, Energy: -52.068954+0.000571j
[2025-09-04 11:01:48] [Iter 1439/2250] R3[388/1200], Temp: 0.7635, Energy: -52.104845+0.000728j
[2025-09-04 11:02:06] [Iter 1440/2250] R3[389/1200], Temp: 0.7624, Energy: -52.138590-0.001539j
[2025-09-04 11:02:24] [Iter 1441/2250] R3[390/1200], Temp: 0.7612, Energy: -52.107015+0.001313j
[2025-09-04 11:02:42] [Iter 1442/2250] R3[391/1200], Temp: 0.7601, Energy: -52.124858-0.000417j
[2025-09-04 11:02:59] [Iter 1443/2250] R3[392/1200], Temp: 0.7590, Energy: -52.099837+0.000530j
[2025-09-04 11:03:17] [Iter 1444/2250] R3[393/1200], Temp: 0.7579, Energy: -52.100770-0.000860j
[2025-09-04 11:03:35] [Iter 1445/2250] R3[394/1200], Temp: 0.7568, Energy: -52.089536+0.001506j
[2025-09-04 11:03:53] [Iter 1446/2250] R3[395/1200], Temp: 0.7556, Energy: -52.151710-0.000577j
[2025-09-04 11:04:11] [Iter 1447/2250] R3[396/1200], Temp: 0.7545, Energy: -52.130542+0.001105j
[2025-09-04 11:04:29] [Iter 1448/2250] R3[397/1200], Temp: 0.7534, Energy: -52.145712-0.003014j
[2025-09-04 11:04:47] [Iter 1449/2250] R3[398/1200], Temp: 0.7523, Energy: -52.169188+0.000504j
[2025-09-04 11:05:05] [Iter 1450/2250] R3[399/1200], Temp: 0.7511, Energy: -52.163117+0.002731j
[2025-09-04 11:05:23] [Iter 1451/2250] R3[400/1200], Temp: 0.7500, Energy: -52.138450-0.001741j
[2025-09-04 11:05:41] [Iter 1452/2250] R3[401/1200], Temp: 0.7489, Energy: -52.186817+0.001189j
[2025-09-04 11:05:59] [Iter 1453/2250] R3[402/1200], Temp: 0.7477, Energy: -52.135721-0.000235j
[2025-09-04 11:06:17] [Iter 1454/2250] R3[403/1200], Temp: 0.7466, Energy: -52.071213-0.000990j
[2025-09-04 11:06:35] [Iter 1455/2250] R3[404/1200], Temp: 0.7455, Energy: -52.127944-0.001005j
[2025-09-04 11:06:53] [Iter 1456/2250] R3[405/1200], Temp: 0.7443, Energy: -52.077499+0.000341j
[2025-09-04 11:07:11] [Iter 1457/2250] R3[406/1200], Temp: 0.7432, Energy: -52.103268-0.001276j
[2025-09-04 11:07:29] [Iter 1458/2250] R3[407/1200], Temp: 0.7420, Energy: -52.092002+0.000073j
[2025-09-04 11:07:47] [Iter 1459/2250] R3[408/1200], Temp: 0.7409, Energy: -52.173240-0.003704j
[2025-09-04 11:08:05] [Iter 1460/2250] R3[409/1200], Temp: 0.7397, Energy: -52.108190-0.004706j
[2025-09-04 11:08:23] [Iter 1461/2250] R3[410/1200], Temp: 0.7386, Energy: -52.079358-0.000651j
[2025-09-04 11:08:41] [Iter 1462/2250] R3[411/1200], Temp: 0.7374, Energy: -52.052247-0.000127j
[2025-09-04 11:08:59] [Iter 1463/2250] R3[412/1200], Temp: 0.7363, Energy: -52.089247-0.001494j
[2025-09-04 11:09:17] [Iter 1464/2250] R3[413/1200], Temp: 0.7351, Energy: -52.103949+0.002070j
[2025-09-04 11:09:35] [Iter 1465/2250] R3[414/1200], Temp: 0.7340, Energy: -52.120607+0.000071j
[2025-09-04 11:09:53] [Iter 1466/2250] R3[415/1200], Temp: 0.7328, Energy: -52.113780-0.002379j
[2025-09-04 11:10:10] [Iter 1467/2250] R3[416/1200], Temp: 0.7316, Energy: -52.151370-0.002559j
[2025-09-04 11:10:28] [Iter 1468/2250] R3[417/1200], Temp: 0.7305, Energy: -52.183592+0.002590j
[2025-09-04 11:10:46] [Iter 1469/2250] R3[418/1200], Temp: 0.7293, Energy: -52.169200+0.000263j
[2025-09-04 11:11:04] [Iter 1470/2250] R3[419/1200], Temp: 0.7282, Energy: -52.154305+0.000197j
[2025-09-04 11:11:22] [Iter 1471/2250] R3[420/1200], Temp: 0.7270, Energy: -52.168880+0.000445j
[2025-09-04 11:11:40] [Iter 1472/2250] R3[421/1200], Temp: 0.7258, Energy: -52.139157-0.001759j
[2025-09-04 11:11:58] [Iter 1473/2250] R3[422/1200], Temp: 0.7247, Energy: -52.121267-0.002659j
[2025-09-04 11:12:16] [Iter 1474/2250] R3[423/1200], Temp: 0.7235, Energy: -52.092451-0.001049j
[2025-09-04 11:12:34] [Iter 1475/2250] R3[424/1200], Temp: 0.7223, Energy: -52.085056+0.001410j
[2025-09-04 11:12:52] [Iter 1476/2250] R3[425/1200], Temp: 0.7211, Energy: -52.073684+0.000565j
[2025-09-04 11:13:10] [Iter 1477/2250] R3[426/1200], Temp: 0.7200, Energy: -52.154156-0.002694j
[2025-09-04 11:13:28] [Iter 1478/2250] R3[427/1200], Temp: 0.7188, Energy: -52.147631+0.002584j
[2025-09-04 11:13:46] [Iter 1479/2250] R3[428/1200], Temp: 0.7176, Energy: -52.095124+0.002669j
[2025-09-04 11:14:04] [Iter 1480/2250] R3[429/1200], Temp: 0.7164, Energy: -52.075996-0.000202j
[2025-09-04 11:14:22] [Iter 1481/2250] R3[430/1200], Temp: 0.7153, Energy: -52.155103+0.001099j
[2025-09-04 11:14:40] [Iter 1482/2250] R3[431/1200], Temp: 0.7141, Energy: -52.150916+0.000076j
[2025-09-04 11:14:58] [Iter 1483/2250] R3[432/1200], Temp: 0.7129, Energy: -52.149866+0.002479j
[2025-09-04 11:15:16] [Iter 1484/2250] R3[433/1200], Temp: 0.7117, Energy: -52.165879-0.002405j
[2025-09-04 11:15:34] [Iter 1485/2250] R3[434/1200], Temp: 0.7105, Energy: -52.185735-0.002111j
[2025-09-04 11:15:52] [Iter 1486/2250] R3[435/1200], Temp: 0.7093, Energy: -52.156283+0.001224j
[2025-09-04 11:16:10] [Iter 1487/2250] R3[436/1200], Temp: 0.7081, Energy: -52.137488+0.002812j
[2025-09-04 11:16:28] [Iter 1488/2250] R3[437/1200], Temp: 0.7069, Energy: -52.116076-0.001363j
[2025-09-04 11:16:46] [Iter 1489/2250] R3[438/1200], Temp: 0.7058, Energy: -52.172528-0.000728j
[2025-09-04 11:17:04] [Iter 1490/2250] R3[439/1200], Temp: 0.7046, Energy: -52.180979-0.001169j
[2025-09-04 11:17:22] [Iter 1491/2250] R3[440/1200], Temp: 0.7034, Energy: -52.154506+0.002427j
[2025-09-04 11:17:39] [Iter 1492/2250] R3[441/1200], Temp: 0.7022, Energy: -52.113069+0.000369j
[2025-09-04 11:17:57] [Iter 1493/2250] R3[442/1200], Temp: 0.7010, Energy: -52.116513+0.003193j
[2025-09-04 11:18:15] [Iter 1494/2250] R3[443/1200], Temp: 0.6998, Energy: -52.177102+0.001024j
[2025-09-04 11:18:33] [Iter 1495/2250] R3[444/1200], Temp: 0.6986, Energy: -52.158002-0.000847j
[2025-09-04 11:18:51] [Iter 1496/2250] R3[445/1200], Temp: 0.6974, Energy: -52.114191-0.000581j
[2025-09-04 11:19:09] [Iter 1497/2250] R3[446/1200], Temp: 0.6962, Energy: -52.111511+0.000722j
[2025-09-04 11:19:27] [Iter 1498/2250] R3[447/1200], Temp: 0.6950, Energy: -52.124386-0.000467j
[2025-09-04 11:19:45] [Iter 1499/2250] R3[448/1200], Temp: 0.6938, Energy: -52.108175+0.004229j
[2025-09-04 11:20:03] [Iter 1500/2250] R3[449/1200], Temp: 0.6926, Energy: -52.202195-0.002037j
[2025-09-04 11:20:03] ✓ Checkpoint saved: checkpoint_iter_001500.pkl
[2025-09-04 11:20:21] [Iter 1501/2250] R3[450/1200], Temp: 0.6913, Energy: -52.151386-0.001223j
[2025-09-04 11:20:39] [Iter 1502/2250] R3[451/1200], Temp: 0.6901, Energy: -52.195446+0.001877j
[2025-09-04 11:20:57] [Iter 1503/2250] R3[452/1200], Temp: 0.6889, Energy: -52.181649+0.001748j
[2025-09-04 11:21:15] [Iter 1504/2250] R3[453/1200], Temp: 0.6877, Energy: -52.178216+0.000904j
[2025-09-04 11:21:33] [Iter 1505/2250] R3[454/1200], Temp: 0.6865, Energy: -52.174935-0.002200j
[2025-09-04 11:21:51] [Iter 1506/2250] R3[455/1200], Temp: 0.6853, Energy: -52.145990+0.002019j
[2025-09-04 11:22:09] [Iter 1507/2250] R3[456/1200], Temp: 0.6841, Energy: -52.111411-0.001540j
[2025-09-04 11:22:27] [Iter 1508/2250] R3[457/1200], Temp: 0.6828, Energy: -52.132620-0.000865j
[2025-09-04 11:22:45] [Iter 1509/2250] R3[458/1200], Temp: 0.6816, Energy: -52.148961+0.002518j
[2025-09-04 11:23:03] [Iter 1510/2250] R3[459/1200], Temp: 0.6804, Energy: -52.176125+0.001122j
[2025-09-04 11:23:21] [Iter 1511/2250] R3[460/1200], Temp: 0.6792, Energy: -52.173720-0.001064j
[2025-09-04 11:23:39] [Iter 1512/2250] R3[461/1200], Temp: 0.6780, Energy: -52.087602+0.002321j
[2025-09-04 11:23:57] [Iter 1513/2250] R3[462/1200], Temp: 0.6767, Energy: -52.121232-0.000406j
[2025-09-04 11:24:15] [Iter 1514/2250] R3[463/1200], Temp: 0.6755, Energy: -52.049709+0.000544j
[2025-09-04 11:24:33] [Iter 1515/2250] R3[464/1200], Temp: 0.6743, Energy: -52.112658-0.000387j
[2025-09-04 11:24:51] [Iter 1516/2250] R3[465/1200], Temp: 0.6731, Energy: -52.123784+0.000787j
[2025-09-04 11:25:09] [Iter 1517/2250] R3[466/1200], Temp: 0.6718, Energy: -52.114571-0.003896j
[2025-09-04 11:25:27] [Iter 1518/2250] R3[467/1200], Temp: 0.6706, Energy: -52.163591+0.001640j
[2025-09-04 11:25:45] [Iter 1519/2250] R3[468/1200], Temp: 0.6694, Energy: -52.071825+0.000476j
[2025-09-04 11:26:02] [Iter 1520/2250] R3[469/1200], Temp: 0.6681, Energy: -52.076581+0.001695j
[2025-09-04 11:26:20] [Iter 1521/2250] R3[470/1200], Temp: 0.6669, Energy: -52.052582+0.000038j
[2025-09-04 11:26:38] [Iter 1522/2250] R3[471/1200], Temp: 0.6657, Energy: -52.030702-0.001243j
[2025-09-04 11:26:56] [Iter 1523/2250] R3[472/1200], Temp: 0.6644, Energy: -52.095135-0.000014j
[2025-09-04 11:27:14] [Iter 1524/2250] R3[473/1200], Temp: 0.6632, Energy: -52.114833-0.000322j
[2025-09-04 11:27:32] [Iter 1525/2250] R3[474/1200], Temp: 0.6620, Energy: -52.035826+0.001932j
[2025-09-04 11:27:50] [Iter 1526/2250] R3[475/1200], Temp: 0.6607, Energy: -52.010966-0.001486j
[2025-09-04 11:28:08] [Iter 1527/2250] R3[476/1200], Temp: 0.6595, Energy: -52.043801-0.001601j
[2025-09-04 11:28:26] [Iter 1528/2250] R3[477/1200], Temp: 0.6582, Energy: -52.073632+0.002589j
[2025-09-04 11:28:44] [Iter 1529/2250] R3[478/1200], Temp: 0.6570, Energy: -52.086255-0.002459j
[2025-09-04 11:29:02] [Iter 1530/2250] R3[479/1200], Temp: 0.6558, Energy: -52.074061-0.000849j
[2025-09-04 11:29:20] [Iter 1531/2250] R3[480/1200], Temp: 0.6545, Energy: -52.166656-0.001692j
[2025-09-04 11:29:38] [Iter 1532/2250] R3[481/1200], Temp: 0.6533, Energy: -52.193735-0.000014j
[2025-09-04 11:29:56] [Iter 1533/2250] R3[482/1200], Temp: 0.6520, Energy: -52.149833+0.000756j
[2025-09-04 11:30:14] [Iter 1534/2250] R3[483/1200], Temp: 0.6508, Energy: -52.124600+0.001417j
[2025-09-04 11:30:32] [Iter 1535/2250] R3[484/1200], Temp: 0.6495, Energy: -52.069323+0.000251j
[2025-09-04 11:30:50] [Iter 1536/2250] R3[485/1200], Temp: 0.6483, Energy: -52.063312+0.002302j
[2025-09-04 11:31:08] [Iter 1537/2250] R3[486/1200], Temp: 0.6470, Energy: -52.118689-0.001038j
[2025-09-04 11:31:26] [Iter 1538/2250] R3[487/1200], Temp: 0.6458, Energy: -52.155066+0.001732j
[2025-09-04 11:31:44] [Iter 1539/2250] R3[488/1200], Temp: 0.6445, Energy: -52.181061+0.001429j
[2025-09-04 11:32:02] [Iter 1540/2250] R3[489/1200], Temp: 0.6433, Energy: -52.226615+0.002054j
[2025-09-04 11:32:20] [Iter 1541/2250] R3[490/1200], Temp: 0.6420, Energy: -52.141415-0.000389j
[2025-09-04 11:32:38] [Iter 1542/2250] R3[491/1200], Temp: 0.6408, Energy: -52.170403+0.000728j
[2025-09-04 11:32:56] [Iter 1543/2250] R3[492/1200], Temp: 0.6395, Energy: -52.145693-0.002632j
[2025-09-04 11:33:14] [Iter 1544/2250] R3[493/1200], Temp: 0.6382, Energy: -52.152578+0.000026j
[2025-09-04 11:33:32] [Iter 1545/2250] R3[494/1200], Temp: 0.6370, Energy: -52.154744-0.000214j
[2025-09-04 11:33:49] [Iter 1546/2250] R3[495/1200], Temp: 0.6357, Energy: -52.160151+0.003199j
[2025-09-04 11:34:07] [Iter 1547/2250] R3[496/1200], Temp: 0.6345, Energy: -52.167269-0.002801j
[2025-09-04 11:34:25] [Iter 1548/2250] R3[497/1200], Temp: 0.6332, Energy: -52.181516+0.000905j
[2025-09-04 11:34:43] [Iter 1549/2250] R3[498/1200], Temp: 0.6319, Energy: -52.165979+0.000287j
[2025-09-04 11:35:01] [Iter 1550/2250] R3[499/1200], Temp: 0.6307, Energy: -52.172064+0.000527j
[2025-09-04 11:35:19] [Iter 1551/2250] R3[500/1200], Temp: 0.6294, Energy: -52.113329+0.001468j
[2025-09-04 11:35:37] [Iter 1552/2250] R3[501/1200], Temp: 0.6281, Energy: -52.173048-0.000067j
[2025-09-04 11:35:55] [Iter 1553/2250] R3[502/1200], Temp: 0.6269, Energy: -52.116732-0.001038j
[2025-09-04 11:36:13] [Iter 1554/2250] R3[503/1200], Temp: 0.6256, Energy: -52.111160-0.000518j
[2025-09-04 11:36:31] [Iter 1555/2250] R3[504/1200], Temp: 0.6243, Energy: -52.142940-0.003132j
[2025-09-04 11:36:49] [Iter 1556/2250] R3[505/1200], Temp: 0.6231, Energy: -52.142084-0.000671j
[2025-09-04 11:37:07] [Iter 1557/2250] R3[506/1200], Temp: 0.6218, Energy: -52.179401-0.000865j
[2025-09-04 11:37:25] [Iter 1558/2250] R3[507/1200], Temp: 0.6205, Energy: -52.176060-0.002246j
[2025-09-04 11:37:43] [Iter 1559/2250] R3[508/1200], Temp: 0.6193, Energy: -52.172997-0.001204j
[2025-09-04 11:38:01] [Iter 1560/2250] R3[509/1200], Temp: 0.6180, Energy: -52.111908-0.000805j
[2025-09-04 11:38:19] [Iter 1561/2250] R3[510/1200], Temp: 0.6167, Energy: -52.181273-0.004077j
[2025-09-04 11:38:37] [Iter 1562/2250] R3[511/1200], Temp: 0.6154, Energy: -52.250256+0.001564j
[2025-09-04 11:38:55] [Iter 1563/2250] R3[512/1200], Temp: 0.6142, Energy: -52.111495+0.000296j
[2025-09-04 11:39:13] [Iter 1564/2250] R3[513/1200], Temp: 0.6129, Energy: -52.176643+0.000093j
[2025-09-04 11:39:31] [Iter 1565/2250] R3[514/1200], Temp: 0.6116, Energy: -52.139791-0.000127j
[2025-09-04 11:39:49] [Iter 1566/2250] R3[515/1200], Temp: 0.6103, Energy: -52.139727+0.002335j
[2025-09-04 11:40:07] [Iter 1567/2250] R3[516/1200], Temp: 0.6091, Energy: -52.144856-0.000371j
[2025-09-04 11:40:25] [Iter 1568/2250] R3[517/1200], Temp: 0.6078, Energy: -52.082026+0.002250j
[2025-09-04 11:40:43] [Iter 1569/2250] R3[518/1200], Temp: 0.6065, Energy: -52.115507+0.000003j
[2025-09-04 11:41:00] [Iter 1570/2250] R3[519/1200], Temp: 0.6052, Energy: -52.179905-0.002356j
[2025-09-04 11:41:18] [Iter 1571/2250] R3[520/1200], Temp: 0.6040, Energy: -52.165083-0.001253j
[2025-09-04 11:41:36] [Iter 1572/2250] R3[521/1200], Temp: 0.6027, Energy: -52.167770+0.000743j
[2025-09-04 11:41:54] [Iter 1573/2250] R3[522/1200], Temp: 0.6014, Energy: -52.126102-0.002573j
[2025-09-04 11:42:12] [Iter 1574/2250] R3[523/1200], Temp: 0.6001, Energy: -52.116140+0.001414j
[2025-09-04 11:42:30] [Iter 1575/2250] R3[524/1200], Temp: 0.5988, Energy: -52.190994+0.001297j
[2025-09-04 11:42:48] [Iter 1576/2250] R3[525/1200], Temp: 0.5975, Energy: -52.141332-0.000406j
[2025-09-04 11:43:06] [Iter 1577/2250] R3[526/1200], Temp: 0.5963, Energy: -52.152970+0.000237j
[2025-09-04 11:43:24] [Iter 1578/2250] R3[527/1200], Temp: 0.5950, Energy: -52.141830-0.004401j
[2025-09-04 11:43:42] [Iter 1579/2250] R3[528/1200], Temp: 0.5937, Energy: -52.169027+0.001112j
[2025-09-04 11:44:00] [Iter 1580/2250] R3[529/1200], Temp: 0.5924, Energy: -52.151362-0.000207j
[2025-09-04 11:44:18] [Iter 1581/2250] R3[530/1200], Temp: 0.5911, Energy: -52.157145+0.001740j
[2025-09-04 11:44:36] [Iter 1582/2250] R3[531/1200], Temp: 0.5898, Energy: -52.152177-0.002587j
[2025-09-04 11:44:54] [Iter 1583/2250] R3[532/1200], Temp: 0.5885, Energy: -52.191464+0.001859j
[2025-09-04 11:45:12] [Iter 1584/2250] R3[533/1200], Temp: 0.5873, Energy: -52.119841-0.001425j
[2025-09-04 11:45:30] [Iter 1585/2250] R3[534/1200], Temp: 0.5860, Energy: -52.203304+0.000038j
[2025-09-04 11:45:48] [Iter 1586/2250] R3[535/1200], Temp: 0.5847, Energy: -52.211691+0.000811j
[2025-09-04 11:46:06] [Iter 1587/2250] R3[536/1200], Temp: 0.5834, Energy: -52.199542+0.001557j
[2025-09-04 11:46:24] [Iter 1588/2250] R3[537/1200], Temp: 0.5821, Energy: -52.149598+0.000767j
[2025-09-04 11:46:42] [Iter 1589/2250] R3[538/1200], Temp: 0.5808, Energy: -52.179523-0.002253j
[2025-09-04 11:47:00] [Iter 1590/2250] R3[539/1200], Temp: 0.5795, Energy: -52.170094-0.002077j
[2025-09-04 11:47:18] [Iter 1591/2250] R3[540/1200], Temp: 0.5782, Energy: -52.145169-0.002013j
[2025-09-04 11:47:36] [Iter 1592/2250] R3[541/1200], Temp: 0.5769, Energy: -52.163426-0.000114j
[2025-09-04 11:47:54] [Iter 1593/2250] R3[542/1200], Temp: 0.5756, Energy: -52.180558+0.002840j
[2025-09-04 11:48:12] [Iter 1594/2250] R3[543/1200], Temp: 0.5743, Energy: -52.143668-0.000977j
[2025-09-04 11:48:30] [Iter 1595/2250] R3[544/1200], Temp: 0.5730, Energy: -52.150437+0.001047j
[2025-09-04 11:48:48] [Iter 1596/2250] R3[545/1200], Temp: 0.5717, Energy: -52.116991+0.001714j
[2025-09-04 11:49:05] [Iter 1597/2250] R3[546/1200], Temp: 0.5705, Energy: -52.178602-0.000045j
[2025-09-04 11:49:23] [Iter 1598/2250] R3[547/1200], Temp: 0.5692, Energy: -52.113565-0.002043j
[2025-09-04 11:49:41] [Iter 1599/2250] R3[548/1200], Temp: 0.5679, Energy: -52.144120+0.000112j
[2025-09-04 11:49:59] [Iter 1600/2250] R3[549/1200], Temp: 0.5666, Energy: -52.055331-0.000639j
[2025-09-04 11:50:17] [Iter 1601/2250] R3[550/1200], Temp: 0.5653, Energy: -52.128295-0.000505j
[2025-09-04 11:50:35] [Iter 1602/2250] R3[551/1200], Temp: 0.5640, Energy: -52.103214-0.001057j
[2025-09-04 11:50:53] [Iter 1603/2250] R3[552/1200], Temp: 0.5627, Energy: -52.054895+0.000151j
[2025-09-04 11:51:11] [Iter 1604/2250] R3[553/1200], Temp: 0.5614, Energy: -52.047706-0.000178j
[2025-09-04 11:51:29] [Iter 1605/2250] R3[554/1200], Temp: 0.5601, Energy: -52.057933+0.002505j
[2025-09-04 11:51:47] [Iter 1606/2250] R3[555/1200], Temp: 0.5588, Energy: -52.061286+0.000991j
[2025-09-04 11:52:05] [Iter 1607/2250] R3[556/1200], Temp: 0.5575, Energy: -52.079997-0.000401j
[2025-09-04 11:52:23] [Iter 1608/2250] R3[557/1200], Temp: 0.5562, Energy: -52.089961+0.000098j
[2025-09-04 11:52:41] [Iter 1609/2250] R3[558/1200], Temp: 0.5549, Energy: -52.135220+0.000243j
[2025-09-04 11:52:59] [Iter 1610/2250] R3[559/1200], Temp: 0.5536, Energy: -52.168847+0.000140j
[2025-09-04 11:53:17] [Iter 1611/2250] R3[560/1200], Temp: 0.5523, Energy: -52.184944+0.000554j
[2025-09-04 11:53:35] [Iter 1612/2250] R3[561/1200], Temp: 0.5510, Energy: -52.138815+0.002653j
[2025-09-04 11:53:53] [Iter 1613/2250] R3[562/1200], Temp: 0.5497, Energy: -52.122916+0.000716j
[2025-09-04 11:54:11] [Iter 1614/2250] R3[563/1200], Temp: 0.5484, Energy: -52.141179+0.000421j
[2025-09-04 11:54:29] [Iter 1615/2250] R3[564/1200], Temp: 0.5471, Energy: -52.108181+0.000574j
[2025-09-04 11:54:47] [Iter 1616/2250] R3[565/1200], Temp: 0.5458, Energy: -52.124071-0.001331j
[2025-09-04 11:55:05] [Iter 1617/2250] R3[566/1200], Temp: 0.5444, Energy: -52.214058+0.003335j
[2025-09-04 11:55:23] [Iter 1618/2250] R3[567/1200], Temp: 0.5431, Energy: -52.186575-0.000104j
[2025-09-04 11:55:41] [Iter 1619/2250] R3[568/1200], Temp: 0.5418, Energy: -52.138112-0.000100j
[2025-09-04 11:55:59] [Iter 1620/2250] R3[569/1200], Temp: 0.5405, Energy: -52.100136+0.001036j
[2025-09-04 11:56:17] [Iter 1621/2250] R3[570/1200], Temp: 0.5392, Energy: -52.109428-0.000174j
[2025-09-04 11:56:34] [Iter 1622/2250] R3[571/1200], Temp: 0.5379, Energy: -52.086088+0.000088j
[2025-09-04 11:56:52] [Iter 1623/2250] R3[572/1200], Temp: 0.5366, Energy: -52.089535+0.001303j
[2025-09-04 11:57:10] [Iter 1624/2250] R3[573/1200], Temp: 0.5353, Energy: -52.084857+0.001587j
[2025-09-04 11:57:28] [Iter 1625/2250] R3[574/1200], Temp: 0.5340, Energy: -52.119955+0.000365j
[2025-09-04 11:57:46] [Iter 1626/2250] R3[575/1200], Temp: 0.5327, Energy: -52.168985-0.001802j
[2025-09-04 11:58:04] [Iter 1627/2250] R3[576/1200], Temp: 0.5314, Energy: -52.068386+0.000218j
[2025-09-04 11:58:22] [Iter 1628/2250] R3[577/1200], Temp: 0.5301, Energy: -52.165232-0.000363j
[2025-09-04 11:58:40] [Iter 1629/2250] R3[578/1200], Temp: 0.5288, Energy: -52.139328+0.000414j
[2025-09-04 11:58:58] [Iter 1630/2250] R3[579/1200], Temp: 0.5275, Energy: -52.146189-0.001394j
[2025-09-04 11:59:16] [Iter 1631/2250] R3[580/1200], Temp: 0.5262, Energy: -52.118835+0.000915j
[2025-09-04 11:59:34] [Iter 1632/2250] R3[581/1200], Temp: 0.5249, Energy: -52.134738-0.001617j
[2025-09-04 11:59:52] [Iter 1633/2250] R3[582/1200], Temp: 0.5236, Energy: -52.066444+0.001164j
[2025-09-04 12:00:10] [Iter 1634/2250] R3[583/1200], Temp: 0.5222, Energy: -52.057436-0.000112j
[2025-09-04 12:00:28] [Iter 1635/2250] R3[584/1200], Temp: 0.5209, Energy: -52.142188+0.000933j
[2025-09-04 12:00:46] [Iter 1636/2250] R3[585/1200], Temp: 0.5196, Energy: -52.167594-0.002942j
[2025-09-04 12:01:04] [Iter 1637/2250] R3[586/1200], Temp: 0.5183, Energy: -52.175787-0.000819j
[2025-09-04 12:01:22] [Iter 1638/2250] R3[587/1200], Temp: 0.5170, Energy: -52.128442-0.001133j
[2025-09-04 12:01:40] [Iter 1639/2250] R3[588/1200], Temp: 0.5157, Energy: -52.103470-0.002684j
[2025-09-04 12:01:58] [Iter 1640/2250] R3[589/1200], Temp: 0.5144, Energy: -52.061103+0.001192j
[2025-09-04 12:02:16] [Iter 1641/2250] R3[590/1200], Temp: 0.5131, Energy: -52.037944+0.002646j
[2025-09-04 12:02:34] [Iter 1642/2250] R3[591/1200], Temp: 0.5118, Energy: -52.043114+0.001834j
[2025-09-04 12:02:52] [Iter 1643/2250] R3[592/1200], Temp: 0.5105, Energy: -52.058673+0.000557j
[2025-09-04 12:03:10] [Iter 1644/2250] R3[593/1200], Temp: 0.5092, Energy: -52.191634+0.002269j
[2025-09-04 12:03:28] [Iter 1645/2250] R3[594/1200], Temp: 0.5079, Energy: -52.119848+0.002192j
[2025-09-04 12:03:46] [Iter 1646/2250] R3[595/1200], Temp: 0.5065, Energy: -52.114091+0.002805j
[2025-09-04 12:04:04] [Iter 1647/2250] R3[596/1200], Temp: 0.5052, Energy: -52.154930-0.001082j
[2025-09-04 12:04:22] [Iter 1648/2250] R3[597/1200], Temp: 0.5039, Energy: -52.104629-0.000724j
[2025-09-04 12:04:39] [Iter 1649/2250] R3[598/1200], Temp: 0.5026, Energy: -52.113502+0.004458j
[2025-09-04 12:04:57] [Iter 1650/2250] R3[599/1200], Temp: 0.5013, Energy: -52.069081-0.000121j
[2025-09-04 12:05:15] [Iter 1651/2250] R3[600/1200], Temp: 0.5000, Energy: -52.142573+0.000329j
[2025-09-04 12:05:33] [Iter 1652/2250] R3[601/1200], Temp: 0.4987, Energy: -52.126895-0.000629j
[2025-09-04 12:05:51] [Iter 1653/2250] R3[602/1200], Temp: 0.4974, Energy: -52.112708-0.002707j
[2025-09-04 12:06:09] [Iter 1654/2250] R3[603/1200], Temp: 0.4961, Energy: -52.185294+0.001975j
[2025-09-04 12:06:27] [Iter 1655/2250] R3[604/1200], Temp: 0.4948, Energy: -52.220131-0.000253j
[2025-09-04 12:06:45] [Iter 1656/2250] R3[605/1200], Temp: 0.4935, Energy: -52.147496-0.000991j
[2025-09-04 12:07:03] [Iter 1657/2250] R3[606/1200], Temp: 0.4921, Energy: -52.214678-0.000746j
[2025-09-04 12:07:21] [Iter 1658/2250] R3[607/1200], Temp: 0.4908, Energy: -52.176596+0.000121j
[2025-09-04 12:07:39] [Iter 1659/2250] R3[608/1200], Temp: 0.4895, Energy: -52.119139-0.002560j
[2025-09-04 12:07:57] [Iter 1660/2250] R3[609/1200], Temp: 0.4882, Energy: -52.149880+0.000688j
[2025-09-04 12:08:15] [Iter 1661/2250] R3[610/1200], Temp: 0.4869, Energy: -52.155586-0.001997j
[2025-09-04 12:08:33] [Iter 1662/2250] R3[611/1200], Temp: 0.4856, Energy: -52.182159-0.002078j
[2025-09-04 12:08:51] [Iter 1663/2250] R3[612/1200], Temp: 0.4843, Energy: -52.134151+0.001555j
[2025-09-04 12:09:09] [Iter 1664/2250] R3[613/1200], Temp: 0.4830, Energy: -52.175061+0.001049j
[2025-09-04 12:09:27] [Iter 1665/2250] R3[614/1200], Temp: 0.4817, Energy: -52.123748-0.000202j
[2025-09-04 12:09:45] [Iter 1666/2250] R3[615/1200], Temp: 0.4804, Energy: -52.170516+0.002901j
[2025-09-04 12:10:03] [Iter 1667/2250] R3[616/1200], Temp: 0.4791, Energy: -52.118190-0.000890j
[2025-09-04 12:10:21] [Iter 1668/2250] R3[617/1200], Temp: 0.4778, Energy: -52.144226+0.000005j
[2025-09-04 12:10:39] [Iter 1669/2250] R3[618/1200], Temp: 0.4764, Energy: -52.145047-0.000052j
[2025-09-04 12:10:57] [Iter 1670/2250] R3[619/1200], Temp: 0.4751, Energy: -52.103177-0.000679j
[2025-09-04 12:11:15] [Iter 1671/2250] R3[620/1200], Temp: 0.4738, Energy: -52.100212-0.000419j
[2025-09-04 12:11:33] [Iter 1672/2250] R3[621/1200], Temp: 0.4725, Energy: -52.107686+0.000421j
[2025-09-04 12:11:51] [Iter 1673/2250] R3[622/1200], Temp: 0.4712, Energy: -52.122356-0.002782j
[2025-09-04 12:12:09] [Iter 1674/2250] R3[623/1200], Temp: 0.4699, Energy: -52.146334+0.001239j
[2025-09-04 12:12:27] [Iter 1675/2250] R3[624/1200], Temp: 0.4686, Energy: -52.118416-0.001304j
[2025-09-04 12:12:44] [Iter 1676/2250] R3[625/1200], Temp: 0.4673, Energy: -52.139411-0.000227j
[2025-09-04 12:13:02] [Iter 1677/2250] R3[626/1200], Temp: 0.4660, Energy: -52.164324+0.002004j
[2025-09-04 12:13:20] [Iter 1678/2250] R3[627/1200], Temp: 0.4647, Energy: -52.124419+0.002509j
[2025-09-04 12:13:38] [Iter 1679/2250] R3[628/1200], Temp: 0.4634, Energy: -52.157104-0.003372j
[2025-09-04 12:13:56] [Iter 1680/2250] R3[629/1200], Temp: 0.4621, Energy: -52.111487-0.002657j
[2025-09-04 12:14:14] [Iter 1681/2250] R3[630/1200], Temp: 0.4608, Energy: -52.068508+0.001869j
[2025-09-04 12:14:32] [Iter 1682/2250] R3[631/1200], Temp: 0.4595, Energy: -52.108922-0.000502j
[2025-09-04 12:14:50] [Iter 1683/2250] R3[632/1200], Temp: 0.4582, Energy: -52.051981+0.000824j
[2025-09-04 12:15:08] [Iter 1684/2250] R3[633/1200], Temp: 0.4569, Energy: -52.120970-0.000530j
[2025-09-04 12:15:26] [Iter 1685/2250] R3[634/1200], Temp: 0.4556, Energy: -52.061135-0.000254j
[2025-09-04 12:15:44] [Iter 1686/2250] R3[635/1200], Temp: 0.4542, Energy: -52.090820+0.000208j
[2025-09-04 12:16:02] [Iter 1687/2250] R3[636/1200], Temp: 0.4529, Energy: -52.084965+0.000520j
[2025-09-04 12:16:20] [Iter 1688/2250] R3[637/1200], Temp: 0.4516, Energy: -52.095736-0.000004j
[2025-09-04 12:16:38] [Iter 1689/2250] R3[638/1200], Temp: 0.4503, Energy: -52.105459-0.003833j
[2025-09-04 12:16:56] [Iter 1690/2250] R3[639/1200], Temp: 0.4490, Energy: -52.159367+0.000427j
[2025-09-04 12:17:14] [Iter 1691/2250] R3[640/1200], Temp: 0.4477, Energy: -52.162613+0.001834j
[2025-09-04 12:17:32] [Iter 1692/2250] R3[641/1200], Temp: 0.4464, Energy: -52.148011+0.000516j
[2025-09-04 12:17:50] [Iter 1693/2250] R3[642/1200], Temp: 0.4451, Energy: -52.154028-0.000750j
[2025-09-04 12:18:08] [Iter 1694/2250] R3[643/1200], Temp: 0.4438, Energy: -52.167329-0.001598j
[2025-09-04 12:18:26] [Iter 1695/2250] R3[644/1200], Temp: 0.4425, Energy: -52.158379-0.001146j
[2025-09-04 12:18:44] [Iter 1696/2250] R3[645/1200], Temp: 0.4412, Energy: -52.175841+0.001964j
[2025-09-04 12:19:02] [Iter 1697/2250] R3[646/1200], Temp: 0.4399, Energy: -52.179110-0.000224j
[2025-09-04 12:19:20] [Iter 1698/2250] R3[647/1200], Temp: 0.4386, Energy: -52.123544-0.000022j
[2025-09-04 12:19:38] [Iter 1699/2250] R3[648/1200], Temp: 0.4373, Energy: -52.114789-0.000355j
[2025-09-04 12:19:55] [Iter 1700/2250] R3[649/1200], Temp: 0.4360, Energy: -52.130315+0.003468j
[2025-09-04 12:20:13] [Iter 1701/2250] R3[650/1200], Temp: 0.4347, Energy: -52.169355+0.000976j
[2025-09-04 12:20:31] [Iter 1702/2250] R3[651/1200], Temp: 0.4334, Energy: -52.146577+0.000900j
[2025-09-04 12:20:49] [Iter 1703/2250] R3[652/1200], Temp: 0.4321, Energy: -52.173801+0.000242j
[2025-09-04 12:21:07] [Iter 1704/2250] R3[653/1200], Temp: 0.4308, Energy: -52.187260+0.000394j
[2025-09-04 12:21:25] [Iter 1705/2250] R3[654/1200], Temp: 0.4295, Energy: -52.182782-0.001136j
[2025-09-04 12:21:43] [Iter 1706/2250] R3[655/1200], Temp: 0.4283, Energy: -52.126777-0.004191j
[2025-09-04 12:22:01] [Iter 1707/2250] R3[656/1200], Temp: 0.4270, Energy: -52.152504-0.000192j
[2025-09-04 12:22:19] [Iter 1708/2250] R3[657/1200], Temp: 0.4257, Energy: -52.239761-0.000267j
[2025-09-04 12:22:37] [Iter 1709/2250] R3[658/1200], Temp: 0.4244, Energy: -52.228165-0.002631j
[2025-09-04 12:22:55] [Iter 1710/2250] R3[659/1200], Temp: 0.4231, Energy: -52.187608-0.000896j
[2025-09-04 12:23:13] [Iter 1711/2250] R3[660/1200], Temp: 0.4218, Energy: -52.205499+0.003051j
[2025-09-04 12:23:31] [Iter 1712/2250] R3[661/1200], Temp: 0.4205, Energy: -52.150341-0.002720j
[2025-09-04 12:23:49] [Iter 1713/2250] R3[662/1200], Temp: 0.4192, Energy: -52.088990-0.000812j
[2025-09-04 12:24:07] [Iter 1714/2250] R3[663/1200], Temp: 0.4179, Energy: -52.093148-0.001050j
[2025-09-04 12:24:25] [Iter 1715/2250] R3[664/1200], Temp: 0.4166, Energy: -52.103002+0.000246j
[2025-09-04 12:24:43] [Iter 1716/2250] R3[665/1200], Temp: 0.4153, Energy: -52.118838-0.001953j
[2025-09-04 12:25:01] [Iter 1717/2250] R3[666/1200], Temp: 0.4140, Energy: -52.095604-0.000756j
[2025-09-04 12:25:19] [Iter 1718/2250] R3[667/1200], Temp: 0.4127, Energy: -52.074597-0.003303j
[2025-09-04 12:25:37] [Iter 1719/2250] R3[668/1200], Temp: 0.4115, Energy: -52.076968-0.002073j
[2025-09-04 12:25:55] [Iter 1720/2250] R3[669/1200], Temp: 0.4102, Energy: -52.097508+0.000395j
[2025-09-04 12:26:13] [Iter 1721/2250] R3[670/1200], Temp: 0.4089, Energy: -52.186184-0.000944j
[2025-09-04 12:26:31] [Iter 1722/2250] R3[671/1200], Temp: 0.4076, Energy: -52.140503+0.000204j
[2025-09-04 12:26:49] [Iter 1723/2250] R3[672/1200], Temp: 0.4063, Energy: -52.171039+0.000724j
[2025-09-04 12:27:07] [Iter 1724/2250] R3[673/1200], Temp: 0.4050, Energy: -52.219670+0.000129j
[2025-09-04 12:27:25] [Iter 1725/2250] R3[674/1200], Temp: 0.4037, Energy: -52.142701-0.001542j
[2025-09-04 12:27:43] [Iter 1726/2250] R3[675/1200], Temp: 0.4025, Energy: -52.132720+0.001447j
[2025-09-04 12:28:01] [Iter 1727/2250] R3[676/1200], Temp: 0.4012, Energy: -52.148140+0.003049j
[2025-09-04 12:28:18] [Iter 1728/2250] R3[677/1200], Temp: 0.3999, Energy: -52.164586+0.000779j
[2025-09-04 12:28:36] [Iter 1729/2250] R3[678/1200], Temp: 0.3986, Energy: -52.174113-0.001906j
[2025-09-04 12:28:54] [Iter 1730/2250] R3[679/1200], Temp: 0.3973, Energy: -52.158148-0.000558j
[2025-09-04 12:29:12] [Iter 1731/2250] R3[680/1200], Temp: 0.3960, Energy: -52.166990+0.001415j
[2025-09-04 12:29:30] [Iter 1732/2250] R3[681/1200], Temp: 0.3948, Energy: -52.151487-0.001490j
[2025-09-04 12:29:48] [Iter 1733/2250] R3[682/1200], Temp: 0.3935, Energy: -52.115468-0.000588j
[2025-09-04 12:30:06] [Iter 1734/2250] R3[683/1200], Temp: 0.3922, Energy: -52.128011-0.000677j
[2025-09-04 12:30:24] [Iter 1735/2250] R3[684/1200], Temp: 0.3909, Energy: -52.115525+0.000062j
[2025-09-04 12:30:42] [Iter 1736/2250] R3[685/1200], Temp: 0.3897, Energy: -52.179432-0.001735j
[2025-09-04 12:31:00] [Iter 1737/2250] R3[686/1200], Temp: 0.3884, Energy: -52.164374-0.000243j
[2025-09-04 12:31:18] [Iter 1738/2250] R3[687/1200], Temp: 0.3871, Energy: -52.169323+0.000900j
[2025-09-04 12:31:36] [Iter 1739/2250] R3[688/1200], Temp: 0.3858, Energy: -52.167520-0.000528j
[2025-09-04 12:31:54] [Iter 1740/2250] R3[689/1200], Temp: 0.3846, Energy: -52.182197-0.000175j
[2025-09-04 12:32:12] [Iter 1741/2250] R3[690/1200], Temp: 0.3833, Energy: -52.123978+0.001005j
[2025-09-04 12:32:30] [Iter 1742/2250] R3[691/1200], Temp: 0.3820, Energy: -52.185507+0.000118j
[2025-09-04 12:32:48] [Iter 1743/2250] R3[692/1200], Temp: 0.3807, Energy: -52.152182+0.002507j
[2025-09-04 12:33:06] [Iter 1744/2250] R3[693/1200], Temp: 0.3795, Energy: -52.068995+0.000150j
[2025-09-04 12:33:24] [Iter 1745/2250] R3[694/1200], Temp: 0.3782, Energy: -52.169597+0.000498j
[2025-09-04 12:33:42] [Iter 1746/2250] R3[695/1200], Temp: 0.3769, Energy: -52.159037-0.000478j
[2025-09-04 12:34:00] [Iter 1747/2250] R3[696/1200], Temp: 0.3757, Energy: -52.138580+0.000474j
[2025-09-04 12:34:18] [Iter 1748/2250] R3[697/1200], Temp: 0.3744, Energy: -52.223604+0.002077j
[2025-09-04 12:34:36] [Iter 1749/2250] R3[698/1200], Temp: 0.3731, Energy: -52.153883+0.002355j
[2025-09-04 12:34:54] [Iter 1750/2250] R3[699/1200], Temp: 0.3719, Energy: -52.179012+0.001128j
[2025-09-04 12:34:54] ✓ Checkpoint saved: checkpoint_iter_001750.pkl
[2025-09-04 12:35:12] [Iter 1751/2250] R3[700/1200], Temp: 0.3706, Energy: -52.167496-0.000903j
[2025-09-04 12:35:30] [Iter 1752/2250] R3[701/1200], Temp: 0.3693, Energy: -52.156896-0.001883j
[2025-09-04 12:35:48] [Iter 1753/2250] R3[702/1200], Temp: 0.3681, Energy: -52.187021-0.000775j
[2025-09-04 12:36:05] [Iter 1754/2250] R3[703/1200], Temp: 0.3668, Energy: -52.204232-0.004110j
[2025-09-04 12:36:23] [Iter 1755/2250] R3[704/1200], Temp: 0.3655, Energy: -52.165330+0.002304j
[2025-09-04 12:36:41] [Iter 1756/2250] R3[705/1200], Temp: 0.3643, Energy: -52.163217+0.001773j
[2025-09-04 12:36:59] [Iter 1757/2250] R3[706/1200], Temp: 0.3630, Energy: -52.200165-0.000207j
[2025-09-04 12:37:17] [Iter 1758/2250] R3[707/1200], Temp: 0.3618, Energy: -52.172458-0.000637j
[2025-09-04 12:37:35] [Iter 1759/2250] R3[708/1200], Temp: 0.3605, Energy: -52.174798-0.002713j
[2025-09-04 12:37:53] [Iter 1760/2250] R3[709/1200], Temp: 0.3592, Energy: -52.216964-0.003499j
[2025-09-04 12:38:11] [Iter 1761/2250] R3[710/1200], Temp: 0.3580, Energy: -52.167391+0.000002j
[2025-09-04 12:38:29] [Iter 1762/2250] R3[711/1200], Temp: 0.3567, Energy: -52.152714-0.000910j
[2025-09-04 12:38:47] [Iter 1763/2250] R3[712/1200], Temp: 0.3555, Energy: -52.132108-0.000223j
[2025-09-04 12:39:05] [Iter 1764/2250] R3[713/1200], Temp: 0.3542, Energy: -52.156157+0.000082j
[2025-09-04 12:39:23] [Iter 1765/2250] R3[714/1200], Temp: 0.3530, Energy: -52.091079-0.001079j
[2025-09-04 12:39:41] [Iter 1766/2250] R3[715/1200], Temp: 0.3517, Energy: -52.094945+0.000569j
[2025-09-04 12:39:59] [Iter 1767/2250] R3[716/1200], Temp: 0.3505, Energy: -52.117891-0.000483j
[2025-09-04 12:40:17] [Iter 1768/2250] R3[717/1200], Temp: 0.3492, Energy: -52.107802-0.000419j
[2025-09-04 12:40:35] [Iter 1769/2250] R3[718/1200], Temp: 0.3480, Energy: -52.129318+0.001784j
[2025-09-04 12:40:53] [Iter 1770/2250] R3[719/1200], Temp: 0.3467, Energy: -52.153974+0.000641j
[2025-09-04 12:41:11] [Iter 1771/2250] R3[720/1200], Temp: 0.3455, Energy: -52.160604-0.002251j
[2025-09-04 12:41:29] [Iter 1772/2250] R3[721/1200], Temp: 0.3442, Energy: -52.185483+0.001058j
[2025-09-04 12:41:47] [Iter 1773/2250] R3[722/1200], Temp: 0.3430, Energy: -52.108581-0.000530j
[2025-09-04 12:42:05] [Iter 1774/2250] R3[723/1200], Temp: 0.3418, Energy: -52.177411+0.000521j
[2025-09-04 12:42:23] [Iter 1775/2250] R3[724/1200], Temp: 0.3405, Energy: -52.167340-0.001147j
[2025-09-04 12:42:41] [Iter 1776/2250] R3[725/1200], Temp: 0.3393, Energy: -52.087632-0.000764j
[2025-09-04 12:42:59] [Iter 1777/2250] R3[726/1200], Temp: 0.3380, Energy: -51.999418+0.000850j
[2025-09-04 12:43:17] [Iter 1778/2250] R3[727/1200], Temp: 0.3368, Energy: -52.075250-0.001147j
[2025-09-04 12:43:35] [Iter 1779/2250] R3[728/1200], Temp: 0.3356, Energy: -52.126507-0.002109j
[2025-09-04 12:43:53] [Iter 1780/2250] R3[729/1200], Temp: 0.3343, Energy: -52.192872-0.000704j
[2025-09-04 12:44:11] [Iter 1781/2250] R3[730/1200], Temp: 0.3331, Energy: -52.181057+0.001611j
[2025-09-04 12:44:29] [Iter 1782/2250] R3[731/1200], Temp: 0.3319, Energy: -52.125032-0.001302j
[2025-09-04 12:44:47] [Iter 1783/2250] R3[732/1200], Temp: 0.3306, Energy: -52.089305+0.000077j
[2025-09-04 12:45:05] [Iter 1784/2250] R3[733/1200], Temp: 0.3294, Energy: -52.126785+0.000892j
[2025-09-04 12:45:23] [Iter 1785/2250] R3[734/1200], Temp: 0.3282, Energy: -52.140636+0.001804j
[2025-09-04 12:45:41] [Iter 1786/2250] R3[735/1200], Temp: 0.3269, Energy: -52.177464+0.000300j
[2025-09-04 12:45:59] [Iter 1787/2250] R3[736/1200], Temp: 0.3257, Energy: -52.144040+0.001566j
[2025-09-04 12:46:16] [Iter 1788/2250] R3[737/1200], Temp: 0.3245, Energy: -52.208577+0.000889j
[2025-09-04 12:46:34] [Iter 1789/2250] R3[738/1200], Temp: 0.3233, Energy: -52.169131-0.000255j
[2025-09-04 12:46:52] [Iter 1790/2250] R3[739/1200], Temp: 0.3220, Energy: -52.108717-0.001466j
[2025-09-04 12:47:10] [Iter 1791/2250] R3[740/1200], Temp: 0.3208, Energy: -52.101085+0.001475j
[2025-09-04 12:47:28] [Iter 1792/2250] R3[741/1200], Temp: 0.3196, Energy: -52.118822+0.001023j
[2025-09-04 12:47:46] [Iter 1793/2250] R3[742/1200], Temp: 0.3184, Energy: -52.118904+0.000275j
[2025-09-04 12:48:04] [Iter 1794/2250] R3[743/1200], Temp: 0.3172, Energy: -52.138830-0.000055j
[2025-09-04 12:48:22] [Iter 1795/2250] R3[744/1200], Temp: 0.3159, Energy: -52.190527-0.000519j
[2025-09-04 12:48:40] [Iter 1796/2250] R3[745/1200], Temp: 0.3147, Energy: -52.204453-0.001667j
[2025-09-04 12:48:58] [Iter 1797/2250] R3[746/1200], Temp: 0.3135, Energy: -52.165150-0.000021j
[2025-09-04 12:49:16] [Iter 1798/2250] R3[747/1200], Temp: 0.3123, Energy: -52.157943-0.002182j
[2025-09-04 12:49:34] [Iter 1799/2250] R3[748/1200], Temp: 0.3111, Energy: -52.156988-0.001876j
[2025-09-04 12:49:52] [Iter 1800/2250] R3[749/1200], Temp: 0.3099, Energy: -52.133279+0.000767j
[2025-09-04 12:50:10] [Iter 1801/2250] R3[750/1200], Temp: 0.3087, Energy: -52.133901+0.001545j
[2025-09-04 12:50:28] [Iter 1802/2250] R3[751/1200], Temp: 0.3074, Energy: -52.148798-0.000505j
[2025-09-04 12:50:46] [Iter 1803/2250] R3[752/1200], Temp: 0.3062, Energy: -52.147538-0.000311j
[2025-09-04 12:51:04] [Iter 1804/2250] R3[753/1200], Temp: 0.3050, Energy: -52.132944-0.002592j
[2025-09-04 12:51:22] [Iter 1805/2250] R3[754/1200], Temp: 0.3038, Energy: -52.040456-0.000028j
[2025-09-04 12:51:40] [Iter 1806/2250] R3[755/1200], Temp: 0.3026, Energy: -52.078689+0.000149j
[2025-09-04 12:51:58] [Iter 1807/2250] R3[756/1200], Temp: 0.3014, Energy: -52.157562-0.000851j
[2025-09-04 12:52:16] [Iter 1808/2250] R3[757/1200], Temp: 0.3002, Energy: -52.124876-0.000972j
[2025-09-04 12:52:34] [Iter 1809/2250] R3[758/1200], Temp: 0.2990, Energy: -52.179914+0.000807j
[2025-09-04 12:52:52] [Iter 1810/2250] R3[759/1200], Temp: 0.2978, Energy: -52.151483+0.001823j
[2025-09-04 12:53:10] [Iter 1811/2250] R3[760/1200], Temp: 0.2966, Energy: -52.142237+0.000553j
[2025-09-04 12:53:28] [Iter 1812/2250] R3[761/1200], Temp: 0.2954, Energy: -52.202258-0.000621j
[2025-09-04 12:53:46] [Iter 1813/2250] R3[762/1200], Temp: 0.2942, Energy: -52.156807-0.001222j
[2025-09-04 12:54:03] [Iter 1814/2250] R3[763/1200], Temp: 0.2931, Energy: -52.199693+0.002620j
[2025-09-04 12:54:21] [Iter 1815/2250] R3[764/1200], Temp: 0.2919, Energy: -52.121127+0.001393j
[2025-09-04 12:54:39] [Iter 1816/2250] R3[765/1200], Temp: 0.2907, Energy: -52.126511-0.001301j
[2025-09-04 12:54:57] [Iter 1817/2250] R3[766/1200], Temp: 0.2895, Energy: -52.133818-0.000934j
[2025-09-04 12:55:15] [Iter 1818/2250] R3[767/1200], Temp: 0.2883, Energy: -52.137800-0.001372j
[2025-09-04 12:55:33] [Iter 1819/2250] R3[768/1200], Temp: 0.2871, Energy: -52.122276+0.001341j
[2025-09-04 12:55:51] [Iter 1820/2250] R3[769/1200], Temp: 0.2859, Energy: -52.105436+0.000636j
[2025-09-04 12:56:09] [Iter 1821/2250] R3[770/1200], Temp: 0.2847, Energy: -52.094330-0.002452j
[2025-09-04 12:56:27] [Iter 1822/2250] R3[771/1200], Temp: 0.2836, Energy: -52.166727+0.000442j
[2025-09-04 12:56:45] [Iter 1823/2250] R3[772/1200], Temp: 0.2824, Energy: -52.146177+0.001133j
[2025-09-04 12:57:03] [Iter 1824/2250] R3[773/1200], Temp: 0.2812, Energy: -52.139402-0.001948j
[2025-09-04 12:57:21] [Iter 1825/2250] R3[774/1200], Temp: 0.2800, Energy: -52.158648-0.000413j
[2025-09-04 12:57:39] [Iter 1826/2250] R3[775/1200], Temp: 0.2789, Energy: -52.123565-0.001104j
[2025-09-04 12:57:57] [Iter 1827/2250] R3[776/1200], Temp: 0.2777, Energy: -52.138251-0.001651j
[2025-09-04 12:58:15] [Iter 1828/2250] R3[777/1200], Temp: 0.2765, Energy: -52.151498-0.001614j
[2025-09-04 12:58:33] [Iter 1829/2250] R3[778/1200], Temp: 0.2753, Energy: -52.136190+0.000618j
[2025-09-04 12:58:51] [Iter 1830/2250] R3[779/1200], Temp: 0.2742, Energy: -52.083636+0.002768j
[2025-09-04 12:59:09] [Iter 1831/2250] R3[780/1200], Temp: 0.2730, Energy: -52.179134-0.003125j
[2025-09-04 12:59:27] [Iter 1832/2250] R3[781/1200], Temp: 0.2718, Energy: -52.129245-0.000894j
[2025-09-04 12:59:45] [Iter 1833/2250] R3[782/1200], Temp: 0.2707, Energy: -52.127109-0.000258j
[2025-09-04 13:00:03] [Iter 1834/2250] R3[783/1200], Temp: 0.2695, Energy: -52.114298-0.000867j
[2025-09-04 13:00:21] [Iter 1835/2250] R3[784/1200], Temp: 0.2684, Energy: -52.148759-0.000617j
[2025-09-04 13:00:39] [Iter 1836/2250] R3[785/1200], Temp: 0.2672, Energy: -52.152701-0.000404j
[2025-09-04 13:00:57] [Iter 1837/2250] R3[786/1200], Temp: 0.2660, Energy: -52.132825+0.000609j
[2025-09-04 13:01:15] [Iter 1838/2250] R3[787/1200], Temp: 0.2649, Energy: -52.143124+0.001117j
[2025-09-04 13:01:33] [Iter 1839/2250] R3[788/1200], Temp: 0.2637, Energy: -52.196384-0.000938j
[2025-09-04 13:01:51] [Iter 1840/2250] R3[789/1200], Temp: 0.2626, Energy: -52.209211+0.000364j
[2025-09-04 13:02:08] [Iter 1841/2250] R3[790/1200], Temp: 0.2614, Energy: -52.143266-0.002574j
[2025-09-04 13:02:26] [Iter 1842/2250] R3[791/1200], Temp: 0.2603, Energy: -52.178978-0.000074j
[2025-09-04 13:02:44] [Iter 1843/2250] R3[792/1200], Temp: 0.2591, Energy: -52.155451+0.000722j
[2025-09-04 13:03:02] [Iter 1844/2250] R3[793/1200], Temp: 0.2580, Energy: -52.169406-0.001348j
[2025-09-04 13:03:20] [Iter 1845/2250] R3[794/1200], Temp: 0.2568, Energy: -52.147513-0.000765j
[2025-09-04 13:03:38] [Iter 1846/2250] R3[795/1200], Temp: 0.2557, Energy: -52.152184-0.001161j
[2025-09-04 13:03:56] [Iter 1847/2250] R3[796/1200], Temp: 0.2545, Energy: -52.097075-0.000499j
[2025-09-04 13:04:14] [Iter 1848/2250] R3[797/1200], Temp: 0.2534, Energy: -52.240454+0.000368j
[2025-09-04 13:04:32] [Iter 1849/2250] R3[798/1200], Temp: 0.2523, Energy: -52.170795+0.001826j
[2025-09-04 13:04:50] [Iter 1850/2250] R3[799/1200], Temp: 0.2511, Energy: -52.202470+0.000441j
[2025-09-04 13:05:08] [Iter 1851/2250] R3[800/1200], Temp: 0.2500, Energy: -52.225550+0.002160j
[2025-09-04 13:05:26] [Iter 1852/2250] R3[801/1200], Temp: 0.2489, Energy: -52.219570-0.003580j
[2025-09-04 13:05:44] [Iter 1853/2250] R3[802/1200], Temp: 0.2477, Energy: -52.169408+0.002330j
[2025-09-04 13:06:02] [Iter 1854/2250] R3[803/1200], Temp: 0.2466, Energy: -52.194505-0.001257j
[2025-09-04 13:06:20] [Iter 1855/2250] R3[804/1200], Temp: 0.2455, Energy: -52.161945+0.000268j
[2025-09-04 13:06:38] [Iter 1856/2250] R3[805/1200], Temp: 0.2444, Energy: -52.138127-0.001522j
[2025-09-04 13:06:56] [Iter 1857/2250] R3[806/1200], Temp: 0.2432, Energy: -52.159314-0.000402j
[2025-09-04 13:07:14] [Iter 1858/2250] R3[807/1200], Temp: 0.2421, Energy: -52.131719+0.003023j
[2025-09-04 13:07:32] [Iter 1859/2250] R3[808/1200], Temp: 0.2410, Energy: -52.170046+0.000176j
[2025-09-04 13:07:50] [Iter 1860/2250] R3[809/1200], Temp: 0.2399, Energy: -52.191654-0.001267j
[2025-09-04 13:08:08] [Iter 1861/2250] R3[810/1200], Temp: 0.2388, Energy: -52.155992-0.000480j
[2025-09-04 13:08:26] [Iter 1862/2250] R3[811/1200], Temp: 0.2376, Energy: -52.131469-0.000102j
[2025-09-04 13:08:44] [Iter 1863/2250] R3[812/1200], Temp: 0.2365, Energy: -52.142707+0.001452j
[2025-09-04 13:09:02] [Iter 1864/2250] R3[813/1200], Temp: 0.2354, Energy: -52.125227-0.000601j
[2025-09-04 13:09:20] [Iter 1865/2250] R3[814/1200], Temp: 0.2343, Energy: -52.166253-0.001039j
[2025-09-04 13:09:37] [Iter 1866/2250] R3[815/1200], Temp: 0.2332, Energy: -52.215033+0.000880j
[2025-09-04 13:09:55] [Iter 1867/2250] R3[816/1200], Temp: 0.2321, Energy: -52.201472+0.001131j
[2025-09-04 13:10:13] [Iter 1868/2250] R3[817/1200], Temp: 0.2310, Energy: -52.190973-0.000236j
[2025-09-04 13:10:31] [Iter 1869/2250] R3[818/1200], Temp: 0.2299, Energy: -52.162215-0.002394j
[2025-09-04 13:10:49] [Iter 1870/2250] R3[819/1200], Temp: 0.2288, Energy: -52.161175+0.001252j
[2025-09-04 13:11:07] [Iter 1871/2250] R3[820/1200], Temp: 0.2277, Energy: -52.113209-0.000148j
[2025-09-04 13:11:25] [Iter 1872/2250] R3[821/1200], Temp: 0.2266, Energy: -52.133838+0.000853j
[2025-09-04 13:11:43] [Iter 1873/2250] R3[822/1200], Temp: 0.2255, Energy: -52.136511+0.000020j
[2025-09-04 13:12:01] [Iter 1874/2250] R3[823/1200], Temp: 0.2244, Energy: -52.043663-0.000271j
[2025-09-04 13:12:19] [Iter 1875/2250] R3[824/1200], Temp: 0.2233, Energy: -52.100021+0.001594j
[2025-09-04 13:12:37] [Iter 1876/2250] R3[825/1200], Temp: 0.2222, Energy: -52.152333+0.001259j
[2025-09-04 13:12:55] [Iter 1877/2250] R3[826/1200], Temp: 0.2211, Energy: -52.136082-0.000532j
[2025-09-04 13:13:13] [Iter 1878/2250] R3[827/1200], Temp: 0.2200, Energy: -52.108742-0.001329j
[2025-09-04 13:13:31] [Iter 1879/2250] R3[828/1200], Temp: 0.2190, Energy: -52.092104-0.000408j
[2025-09-04 13:13:49] [Iter 1880/2250] R3[829/1200], Temp: 0.2179, Energy: -52.064551+0.001432j
[2025-09-04 13:14:07] [Iter 1881/2250] R3[830/1200], Temp: 0.2168, Energy: -52.141258-0.000281j
[2025-09-04 13:14:25] [Iter 1882/2250] R3[831/1200], Temp: 0.2157, Energy: -52.160868+0.002561j
[2025-09-04 13:14:43] [Iter 1883/2250] R3[832/1200], Temp: 0.2146, Energy: -52.147360+0.000164j
[2025-09-04 13:15:01] [Iter 1884/2250] R3[833/1200], Temp: 0.2136, Energy: -52.152043-0.001973j
[2025-09-04 13:15:19] [Iter 1885/2250] R3[834/1200], Temp: 0.2125, Energy: -52.164363-0.003441j
[2025-09-04 13:15:37] [Iter 1886/2250] R3[835/1200], Temp: 0.2114, Energy: -52.236389-0.002168j
[2025-09-04 13:15:55] [Iter 1887/2250] R3[836/1200], Temp: 0.2104, Energy: -52.199699-0.001032j
[2025-09-04 13:16:13] [Iter 1888/2250] R3[837/1200], Temp: 0.2093, Energy: -52.209295-0.000632j
[2025-09-04 13:16:31] [Iter 1889/2250] R3[838/1200], Temp: 0.2082, Energy: -52.201431+0.001405j
[2025-09-04 13:16:49] [Iter 1890/2250] R3[839/1200], Temp: 0.2072, Energy: -52.224133+0.002166j
[2025-09-04 13:17:07] [Iter 1891/2250] R3[840/1200], Temp: 0.2061, Energy: -52.218290-0.000676j
[2025-09-04 13:17:24] [Iter 1892/2250] R3[841/1200], Temp: 0.2050, Energy: -52.129650-0.001237j
[2025-09-04 13:17:42] [Iter 1893/2250] R3[842/1200], Temp: 0.2040, Energy: -52.070893-0.000624j
[2025-09-04 13:18:00] [Iter 1894/2250] R3[843/1200], Temp: 0.2029, Energy: -52.198834-0.000515j
[2025-09-04 13:18:18] [Iter 1895/2250] R3[844/1200], Temp: 0.2019, Energy: -52.147452-0.000461j
[2025-09-04 13:18:36] [Iter 1896/2250] R3[845/1200], Temp: 0.2008, Energy: -52.100819+0.000351j
[2025-09-04 13:18:54] [Iter 1897/2250] R3[846/1200], Temp: 0.1998, Energy: -52.096983-0.001150j
[2025-09-04 13:19:12] [Iter 1898/2250] R3[847/1200], Temp: 0.1987, Energy: -52.133310-0.000506j
[2025-09-04 13:19:30] [Iter 1899/2250] R3[848/1200], Temp: 0.1977, Energy: -52.106668+0.001273j
[2025-09-04 13:19:48] [Iter 1900/2250] R3[849/1200], Temp: 0.1967, Energy: -52.140642+0.000920j
[2025-09-04 13:20:06] [Iter 1901/2250] R3[850/1200], Temp: 0.1956, Energy: -52.111958+0.002184j
[2025-09-04 13:20:24] [Iter 1902/2250] R3[851/1200], Temp: 0.1946, Energy: -52.148437+0.000337j
[2025-09-04 13:20:42] [Iter 1903/2250] R3[852/1200], Temp: 0.1935, Energy: -52.162527-0.001371j
[2025-09-04 13:21:00] [Iter 1904/2250] R3[853/1200], Temp: 0.1925, Energy: -52.150122+0.001133j
[2025-09-04 13:21:18] [Iter 1905/2250] R3[854/1200], Temp: 0.1915, Energy: -52.078571+0.000801j
[2025-09-04 13:21:36] [Iter 1906/2250] R3[855/1200], Temp: 0.1905, Energy: -52.181333+0.003564j
[2025-09-04 13:21:54] [Iter 1907/2250] R3[856/1200], Temp: 0.1894, Energy: -52.192531-0.003053j
[2025-09-04 13:22:12] [Iter 1908/2250] R3[857/1200], Temp: 0.1884, Energy: -52.204084-0.000642j
[2025-09-04 13:22:30] [Iter 1909/2250] R3[858/1200], Temp: 0.1874, Energy: -52.180585-0.000485j
[2025-09-04 13:22:48] [Iter 1910/2250] R3[859/1200], Temp: 0.1864, Energy: -52.141685-0.001792j
[2025-09-04 13:23:06] [Iter 1911/2250] R3[860/1200], Temp: 0.1853, Energy: -52.155418-0.000394j
[2025-09-04 13:23:24] [Iter 1912/2250] R3[861/1200], Temp: 0.1843, Energy: -52.145888-0.001673j
[2025-09-04 13:23:42] [Iter 1913/2250] R3[862/1200], Temp: 0.1833, Energy: -52.134824-0.002752j
[2025-09-04 13:24:00] [Iter 1914/2250] R3[863/1200], Temp: 0.1823, Energy: -52.156066+0.001798j
[2025-09-04 13:24:18] [Iter 1915/2250] R3[864/1200], Temp: 0.1813, Energy: -52.146849+0.000465j
[2025-09-04 13:24:36] [Iter 1916/2250] R3[865/1200], Temp: 0.1803, Energy: -52.142705-0.000477j
[2025-09-04 13:24:54] [Iter 1917/2250] R3[866/1200], Temp: 0.1793, Energy: -52.119420-0.001795j
[2025-09-04 13:25:12] [Iter 1918/2250] R3[867/1200], Temp: 0.1783, Energy: -52.099453+0.002016j
[2025-09-04 13:25:29] [Iter 1919/2250] R3[868/1200], Temp: 0.1773, Energy: -52.163265-0.001309j
[2025-09-04 13:25:47] [Iter 1920/2250] R3[869/1200], Temp: 0.1763, Energy: -52.134028+0.001138j
[2025-09-04 13:26:05] [Iter 1921/2250] R3[870/1200], Temp: 0.1753, Energy: -52.154673+0.001706j
[2025-09-04 13:26:23] [Iter 1922/2250] R3[871/1200], Temp: 0.1743, Energy: -52.152463-0.003094j
[2025-09-04 13:26:41] [Iter 1923/2250] R3[872/1200], Temp: 0.1733, Energy: -52.130196+0.001663j
[2025-09-04 13:26:59] [Iter 1924/2250] R3[873/1200], Temp: 0.1723, Energy: -52.102155-0.002351j
[2025-09-04 13:27:17] [Iter 1925/2250] R3[874/1200], Temp: 0.1713, Energy: -52.105461-0.001732j
[2025-09-04 13:27:35] [Iter 1926/2250] R3[875/1200], Temp: 0.1703, Energy: -52.097996+0.000132j
[2025-09-04 13:27:53] [Iter 1927/2250] R3[876/1200], Temp: 0.1693, Energy: -52.126434-0.001303j
[2025-09-04 13:28:11] [Iter 1928/2250] R3[877/1200], Temp: 0.1684, Energy: -52.123055+0.002656j
[2025-09-04 13:28:29] [Iter 1929/2250] R3[878/1200], Temp: 0.1674, Energy: -52.184546-0.002277j
[2025-09-04 13:28:47] [Iter 1930/2250] R3[879/1200], Temp: 0.1664, Energy: -52.171524-0.000874j
[2025-09-04 13:29:05] [Iter 1931/2250] R3[880/1200], Temp: 0.1654, Energy: -52.178980+0.000136j
[2025-09-04 13:29:23] [Iter 1932/2250] R3[881/1200], Temp: 0.1645, Energy: -52.162609-0.000651j
[2025-09-04 13:29:41] [Iter 1933/2250] R3[882/1200], Temp: 0.1635, Energy: -52.098782-0.002190j
[2025-09-04 13:29:59] [Iter 1934/2250] R3[883/1200], Temp: 0.1625, Energy: -52.114844-0.003234j
[2025-09-04 13:30:17] [Iter 1935/2250] R3[884/1200], Temp: 0.1616, Energy: -52.142858+0.000516j
[2025-09-04 13:30:35] [Iter 1936/2250] R3[885/1200], Temp: 0.1606, Energy: -52.103790+0.000917j
[2025-09-04 13:30:53] [Iter 1937/2250] R3[886/1200], Temp: 0.1596, Energy: -52.160186+0.001366j
[2025-09-04 13:31:11] [Iter 1938/2250] R3[887/1200], Temp: 0.1587, Energy: -52.194565-0.001169j
[2025-09-04 13:31:29] [Iter 1939/2250] R3[888/1200], Temp: 0.1577, Energy: -52.182223+0.001905j
[2025-09-04 13:31:47] [Iter 1940/2250] R3[889/1200], Temp: 0.1568, Energy: -52.185615+0.001133j
[2025-09-04 13:32:05] [Iter 1941/2250] R3[890/1200], Temp: 0.1558, Energy: -52.130167+0.002244j
[2025-09-04 13:32:23] [Iter 1942/2250] R3[891/1200], Temp: 0.1549, Energy: -52.105486+0.000330j
[2025-09-04 13:32:41] [Iter 1943/2250] R3[892/1200], Temp: 0.1539, Energy: -52.203134-0.000149j
[2025-09-04 13:32:59] [Iter 1944/2250] R3[893/1200], Temp: 0.1530, Energy: -52.196589-0.001229j
[2025-09-04 13:33:17] [Iter 1945/2250] R3[894/1200], Temp: 0.1520, Energy: -52.204043-0.001616j
[2025-09-04 13:33:34] [Iter 1946/2250] R3[895/1200], Temp: 0.1511, Energy: -52.185081-0.001739j
[2025-09-04 13:33:52] [Iter 1947/2250] R3[896/1200], Temp: 0.1502, Energy: -52.226014-0.000126j
[2025-09-04 13:34:10] [Iter 1948/2250] R3[897/1200], Temp: 0.1492, Energy: -52.164487+0.001539j
[2025-09-04 13:34:28] [Iter 1949/2250] R3[898/1200], Temp: 0.1483, Energy: -52.214960-0.000506j
[2025-09-04 13:34:46] [Iter 1950/2250] R3[899/1200], Temp: 0.1474, Energy: -52.130810-0.000803j
[2025-09-04 13:35:04] [Iter 1951/2250] R3[900/1200], Temp: 0.1464, Energy: -52.175980+0.000469j
[2025-09-04 13:35:22] [Iter 1952/2250] R3[901/1200], Temp: 0.1455, Energy: -52.162162+0.000621j
[2025-09-04 13:35:40] [Iter 1953/2250] R3[902/1200], Temp: 0.1446, Energy: -52.210283+0.001423j
[2025-09-04 13:35:58] [Iter 1954/2250] R3[903/1200], Temp: 0.1437, Energy: -52.215461+0.000610j
[2025-09-04 13:36:16] [Iter 1955/2250] R3[904/1200], Temp: 0.1428, Energy: -52.168169+0.000083j
[2025-09-04 13:36:34] [Iter 1956/2250] R3[905/1200], Temp: 0.1418, Energy: -52.156657-0.002264j
[2025-09-04 13:36:52] [Iter 1957/2250] R3[906/1200], Temp: 0.1409, Energy: -52.175368-0.002224j
[2025-09-04 13:37:10] [Iter 1958/2250] R3[907/1200], Temp: 0.1400, Energy: -52.162958+0.001192j
[2025-09-04 13:37:28] [Iter 1959/2250] R3[908/1200], Temp: 0.1391, Energy: -52.082037-0.000515j
[2025-09-04 13:37:46] [Iter 1960/2250] R3[909/1200], Temp: 0.1382, Energy: -52.119510+0.000352j
[2025-09-04 13:38:04] [Iter 1961/2250] R3[910/1200], Temp: 0.1373, Energy: -52.097683+0.000180j
[2025-09-04 13:38:22] [Iter 1962/2250] R3[911/1200], Temp: 0.1364, Energy: -52.105731+0.000968j
[2025-09-04 13:38:40] [Iter 1963/2250] R3[912/1200], Temp: 0.1355, Energy: -52.153114+0.000606j
[2025-09-04 13:38:58] [Iter 1964/2250] R3[913/1200], Temp: 0.1346, Energy: -52.133383-0.002920j
[2025-09-04 13:39:16] [Iter 1965/2250] R3[914/1200], Temp: 0.1337, Energy: -52.142702+0.001937j
[2025-09-04 13:39:34] [Iter 1966/2250] R3[915/1200], Temp: 0.1328, Energy: -52.169091-0.001470j
[2025-09-04 13:39:52] [Iter 1967/2250] R3[916/1200], Temp: 0.1320, Energy: -52.132568+0.000602j
[2025-09-04 13:40:10] [Iter 1968/2250] R3[917/1200], Temp: 0.1311, Energy: -52.183758+0.000356j
[2025-09-04 13:40:27] [Iter 1969/2250] R3[918/1200], Temp: 0.1302, Energy: -52.183439+0.000616j
[2025-09-04 13:40:45] [Iter 1970/2250] R3[919/1200], Temp: 0.1293, Energy: -52.175430+0.000613j
[2025-09-04 13:41:03] [Iter 1971/2250] R3[920/1200], Temp: 0.1284, Energy: -52.077905-0.000611j
[2025-09-04 13:41:21] [Iter 1972/2250] R3[921/1200], Temp: 0.1276, Energy: -52.074728+0.000441j
[2025-09-04 13:41:39] [Iter 1973/2250] R3[922/1200], Temp: 0.1267, Energy: -52.182609+0.000435j
[2025-09-04 13:41:57] [Iter 1974/2250] R3[923/1200], Temp: 0.1258, Energy: -52.202079-0.002185j
[2025-09-04 13:42:15] [Iter 1975/2250] R3[924/1200], Temp: 0.1249, Energy: -52.138802-0.000407j
[2025-09-04 13:42:33] [Iter 1976/2250] R3[925/1200], Temp: 0.1241, Energy: -52.214563+0.000408j
[2025-09-04 13:42:51] [Iter 1977/2250] R3[926/1200], Temp: 0.1232, Energy: -52.172105+0.001123j
[2025-09-04 13:43:09] [Iter 1978/2250] R3[927/1200], Temp: 0.1224, Energy: -52.168093+0.002510j
[2025-09-04 13:43:27] [Iter 1979/2250] R3[928/1200], Temp: 0.1215, Energy: -52.194748-0.000195j
[2025-09-04 13:43:45] [Iter 1980/2250] R3[929/1200], Temp: 0.1206, Energy: -52.193728+0.001037j
[2025-09-04 13:44:03] [Iter 1981/2250] R3[930/1200], Temp: 0.1198, Energy: -52.180796+0.000929j
[2025-09-04 13:44:21] [Iter 1982/2250] R3[931/1200], Temp: 0.1189, Energy: -52.180831-0.001371j
[2025-09-04 13:44:39] [Iter 1983/2250] R3[932/1200], Temp: 0.1181, Energy: -52.086690-0.002219j
[2025-09-04 13:44:57] [Iter 1984/2250] R3[933/1200], Temp: 0.1173, Energy: -52.051471-0.000029j
[2025-09-04 13:45:15] [Iter 1985/2250] R3[934/1200], Temp: 0.1164, Energy: -52.022961-0.000146j
[2025-09-04 13:45:33] [Iter 1986/2250] R3[935/1200], Temp: 0.1156, Energy: -52.076954+0.000876j
[2025-09-04 13:45:51] [Iter 1987/2250] R3[936/1200], Temp: 0.1147, Energy: -52.063034+0.002985j
[2025-09-04 13:46:09] [Iter 1988/2250] R3[937/1200], Temp: 0.1139, Energy: -52.159595+0.001128j
[2025-09-04 13:46:27] [Iter 1989/2250] R3[938/1200], Temp: 0.1131, Energy: -52.076154-0.000289j
[2025-09-04 13:46:45] [Iter 1990/2250] R3[939/1200], Temp: 0.1123, Energy: -52.068621+0.001524j
[2025-09-04 13:47:03] [Iter 1991/2250] R3[940/1200], Temp: 0.1114, Energy: -52.116944+0.001107j
[2025-09-04 13:47:21] [Iter 1992/2250] R3[941/1200], Temp: 0.1106, Energy: -52.098120-0.000694j
[2025-09-04 13:47:39] [Iter 1993/2250] R3[942/1200], Temp: 0.1098, Energy: -52.103550-0.001689j
[2025-09-04 13:47:57] [Iter 1994/2250] R3[943/1200], Temp: 0.1090, Energy: -52.120690-0.000813j
[2025-09-04 13:48:15] [Iter 1995/2250] R3[944/1200], Temp: 0.1082, Energy: -52.196035-0.000513j
[2025-09-04 13:48:32] [Iter 1996/2250] R3[945/1200], Temp: 0.1073, Energy: -52.165366-0.000832j
[2025-09-04 13:48:50] [Iter 1997/2250] R3[946/1200], Temp: 0.1065, Energy: -52.206326+0.001023j
[2025-09-04 13:49:08] [Iter 1998/2250] R3[947/1200], Temp: 0.1057, Energy: -52.143202+0.001219j
[2025-09-04 13:49:26] [Iter 1999/2250] R3[948/1200], Temp: 0.1049, Energy: -52.163431-0.001323j
[2025-09-04 13:49:44] [Iter 2000/2250] R3[949/1200], Temp: 0.1041, Energy: -52.181672+0.002341j
[2025-09-04 13:49:44] ✓ Checkpoint saved: checkpoint_iter_002000.pkl
[2025-09-04 13:50:02] [Iter 2001/2250] R3[950/1200], Temp: 0.1033, Energy: -52.211207+0.000950j
[2025-09-04 13:50:20] [Iter 2002/2250] R3[951/1200], Temp: 0.1025, Energy: -52.189682+0.000048j
[2025-09-04 13:50:38] [Iter 2003/2250] R3[952/1200], Temp: 0.1017, Energy: -52.153008-0.001672j
[2025-09-04 13:50:56] [Iter 2004/2250] R3[953/1200], Temp: 0.1009, Energy: -52.219261+0.000647j
[2025-09-04 13:51:14] [Iter 2005/2250] R3[954/1200], Temp: 0.1002, Energy: -52.245150-0.002260j
[2025-09-04 13:51:32] [Iter 2006/2250] R3[955/1200], Temp: 0.0994, Energy: -52.157792-0.000981j
[2025-09-04 13:51:50] [Iter 2007/2250] R3[956/1200], Temp: 0.0986, Energy: -52.198171-0.001033j
[2025-09-04 13:52:08] [Iter 2008/2250] R3[957/1200], Temp: 0.0978, Energy: -52.148329-0.000340j
[2025-09-04 13:52:26] [Iter 2009/2250] R3[958/1200], Temp: 0.0970, Energy: -52.139538-0.001491j
[2025-09-04 13:52:44] [Iter 2010/2250] R3[959/1200], Temp: 0.0963, Energy: -52.185959-0.000370j
[2025-09-04 13:53:02] [Iter 2011/2250] R3[960/1200], Temp: 0.0955, Energy: -52.225210+0.002370j
[2025-09-04 13:53:20] [Iter 2012/2250] R3[961/1200], Temp: 0.0947, Energy: -52.221874+0.000392j
[2025-09-04 13:53:38] [Iter 2013/2250] R3[962/1200], Temp: 0.0940, Energy: -52.202296+0.001561j
[2025-09-04 13:53:56] [Iter 2014/2250] R3[963/1200], Temp: 0.0932, Energy: -52.180151+0.000875j
[2025-09-04 13:54:14] [Iter 2015/2250] R3[964/1200], Temp: 0.0924, Energy: -52.155373+0.000215j
[2025-09-04 13:54:32] [Iter 2016/2250] R3[965/1200], Temp: 0.0917, Energy: -52.127506+0.000706j
[2025-09-04 13:54:50] [Iter 2017/2250] R3[966/1200], Temp: 0.0909, Energy: -52.173600+0.000933j
[2025-09-04 13:55:08] [Iter 2018/2250] R3[967/1200], Temp: 0.0902, Energy: -52.182794-0.000501j
[2025-09-04 13:55:26] [Iter 2019/2250] R3[968/1200], Temp: 0.0894, Energy: -52.157842+0.002294j
[2025-09-04 13:55:44] [Iter 2020/2250] R3[969/1200], Temp: 0.0887, Energy: -52.184670-0.000860j
[2025-09-04 13:56:01] [Iter 2021/2250] R3[970/1200], Temp: 0.0879, Energy: -52.130037-0.000179j
[2025-09-04 13:56:19] [Iter 2022/2250] R3[971/1200], Temp: 0.0872, Energy: -52.153586+0.002290j
[2025-09-04 13:56:37] [Iter 2023/2250] R3[972/1200], Temp: 0.0865, Energy: -52.148440+0.004442j
[2025-09-04 13:56:55] [Iter 2024/2250] R3[973/1200], Temp: 0.0857, Energy: -52.112951+0.001314j
[2025-09-04 13:57:13] [Iter 2025/2250] R3[974/1200], Temp: 0.0850, Energy: -52.115108-0.001941j
[2025-09-04 13:57:31] [Iter 2026/2250] R3[975/1200], Temp: 0.0843, Energy: -52.155567+0.000147j
[2025-09-04 13:57:49] [Iter 2027/2250] R3[976/1200], Temp: 0.0835, Energy: -52.178747+0.000129j
[2025-09-04 13:58:07] [Iter 2028/2250] R3[977/1200], Temp: 0.0828, Energy: -52.215906+0.001538j
[2025-09-04 13:58:25] [Iter 2029/2250] R3[978/1200], Temp: 0.0821, Energy: -52.192426+0.000636j
[2025-09-04 13:58:43] [Iter 2030/2250] R3[979/1200], Temp: 0.0814, Energy: -52.115991-0.002061j
[2025-09-04 13:59:01] [Iter 2031/2250] R3[980/1200], Temp: 0.0807, Energy: -52.166507-0.001078j
[2025-09-04 13:59:19] [Iter 2032/2250] R3[981/1200], Temp: 0.0800, Energy: -52.202302-0.000451j
[2025-09-04 13:59:37] [Iter 2033/2250] R3[982/1200], Temp: 0.0792, Energy: -52.181928-0.001994j
[2025-09-04 13:59:55] [Iter 2034/2250] R3[983/1200], Temp: 0.0785, Energy: -52.170998-0.000719j
[2025-09-04 14:00:13] [Iter 2035/2250] R3[984/1200], Temp: 0.0778, Energy: -52.197378+0.000755j
[2025-09-04 14:00:31] [Iter 2036/2250] R3[985/1200], Temp: 0.0771, Energy: -52.263215+0.001206j
[2025-09-04 14:00:49] [Iter 2037/2250] R3[986/1200], Temp: 0.0764, Energy: -52.218045-0.001718j
[2025-09-04 14:01:07] [Iter 2038/2250] R3[987/1200], Temp: 0.0757, Energy: -52.217739-0.000082j
[2025-09-04 14:01:25] [Iter 2039/2250] R3[988/1200], Temp: 0.0751, Energy: -52.129113-0.002154j
[2025-09-04 14:01:43] [Iter 2040/2250] R3[989/1200], Temp: 0.0744, Energy: -52.107131+0.001298j
[2025-09-04 14:02:01] [Iter 2041/2250] R3[990/1200], Temp: 0.0737, Energy: -52.126432+0.002424j
[2025-09-04 14:02:19] [Iter 2042/2250] R3[991/1200], Temp: 0.0730, Energy: -52.155689+0.000297j
[2025-09-04 14:02:37] [Iter 2043/2250] R3[992/1200], Temp: 0.0723, Energy: -52.183628+0.001919j
[2025-09-04 14:02:55] [Iter 2044/2250] R3[993/1200], Temp: 0.0716, Energy: -52.185456+0.001649j
[2025-09-04 14:03:13] [Iter 2045/2250] R3[994/1200], Temp: 0.0710, Energy: -52.195701-0.000429j
[2025-09-04 14:03:31] [Iter 2046/2250] R3[995/1200], Temp: 0.0703, Energy: -52.175940-0.000106j
[2025-09-04 14:03:49] [Iter 2047/2250] R3[996/1200], Temp: 0.0696, Energy: -52.168156+0.002782j
[2025-09-04 14:04:06] [Iter 2048/2250] R3[997/1200], Temp: 0.0690, Energy: -52.164512-0.001106j
[2025-09-04 14:04:24] [Iter 2049/2250] R3[998/1200], Temp: 0.0683, Energy: -52.219730-0.001066j
[2025-09-04 14:04:42] [Iter 2050/2250] R3[999/1200], Temp: 0.0676, Energy: -52.216929+0.001304j
[2025-09-04 14:05:00] [Iter 2051/2250] R3[1000/1200], Temp: 0.0670, Energy: -52.174373+0.001430j
[2025-09-04 14:05:18] [Iter 2052/2250] R3[1001/1200], Temp: 0.0663, Energy: -52.163961-0.001144j
[2025-09-04 14:05:36] [Iter 2053/2250] R3[1002/1200], Temp: 0.0657, Energy: -52.129960-0.001447j
[2025-09-04 14:05:54] [Iter 2054/2250] R3[1003/1200], Temp: 0.0650, Energy: -52.110114+0.002326j
[2025-09-04 14:06:12] [Iter 2055/2250] R3[1004/1200], Temp: 0.0644, Energy: -52.134839-0.000186j
[2025-09-04 14:06:30] [Iter 2056/2250] R3[1005/1200], Temp: 0.0638, Energy: -52.100797-0.000899j
[2025-09-04 14:06:48] [Iter 2057/2250] R3[1006/1200], Temp: 0.0631, Energy: -52.147809-0.000857j
[2025-09-04 14:07:06] [Iter 2058/2250] R3[1007/1200], Temp: 0.0625, Energy: -52.107794+0.000815j
[2025-09-04 14:07:24] [Iter 2059/2250] R3[1008/1200], Temp: 0.0618, Energy: -52.179775+0.001528j
[2025-09-04 14:07:42] [Iter 2060/2250] R3[1009/1200], Temp: 0.0612, Energy: -52.187187-0.000547j
[2025-09-04 14:08:00] [Iter 2061/2250] R3[1010/1200], Temp: 0.0606, Energy: -52.140115+0.001656j
[2025-09-04 14:08:18] [Iter 2062/2250] R3[1011/1200], Temp: 0.0600, Energy: -52.148479-0.000614j
[2025-09-04 14:08:36] [Iter 2063/2250] R3[1012/1200], Temp: 0.0593, Energy: -52.143272+0.000665j
[2025-09-04 14:08:54] [Iter 2064/2250] R3[1013/1200], Temp: 0.0587, Energy: -52.128061+0.003845j
[2025-09-04 14:09:12] [Iter 2065/2250] R3[1014/1200], Temp: 0.0581, Energy: -52.144371+0.002119j
[2025-09-04 14:09:30] [Iter 2066/2250] R3[1015/1200], Temp: 0.0575, Energy: -52.135480-0.003410j
[2025-09-04 14:09:48] [Iter 2067/2250] R3[1016/1200], Temp: 0.0569, Energy: -52.185910+0.000530j
[2025-09-04 14:10:06] [Iter 2068/2250] R3[1017/1200], Temp: 0.0563, Energy: -52.185115-0.000317j
[2025-09-04 14:10:24] [Iter 2069/2250] R3[1018/1200], Temp: 0.0557, Energy: -52.154795+0.000366j
[2025-09-04 14:10:42] [Iter 2070/2250] R3[1019/1200], Temp: 0.0551, Energy: -52.156600+0.001048j
[2025-09-04 14:11:00] [Iter 2071/2250] R3[1020/1200], Temp: 0.0545, Energy: -52.162093-0.001174j
[2025-09-04 14:11:18] [Iter 2072/2250] R3[1021/1200], Temp: 0.0539, Energy: -52.183491+0.001338j
[2025-09-04 14:11:36] [Iter 2073/2250] R3[1022/1200], Temp: 0.0533, Energy: -52.158332+0.002440j
[2025-09-04 14:11:54] [Iter 2074/2250] R3[1023/1200], Temp: 0.0527, Energy: -52.217128-0.000464j
[2025-09-04 14:12:12] [Iter 2075/2250] R3[1024/1200], Temp: 0.0521, Energy: -52.201255+0.000016j
[2025-09-04 14:12:30] [Iter 2076/2250] R3[1025/1200], Temp: 0.0516, Energy: -52.200805-0.000234j
[2025-09-04 14:12:48] [Iter 2077/2250] R3[1026/1200], Temp: 0.0510, Energy: -52.178910-0.001429j
[2025-09-04 14:13:06] [Iter 2078/2250] R3[1027/1200], Temp: 0.0504, Energy: -52.144355+0.002076j
[2025-09-04 14:13:24] [Iter 2079/2250] R3[1028/1200], Temp: 0.0498, Energy: -52.155219+0.000347j
[2025-09-04 14:13:41] [Iter 2080/2250] R3[1029/1200], Temp: 0.0493, Energy: -52.155587+0.002166j
[2025-09-04 14:13:59] [Iter 2081/2250] R3[1030/1200], Temp: 0.0487, Energy: -52.189729-0.000937j
[2025-09-04 14:14:17] [Iter 2082/2250] R3[1031/1200], Temp: 0.0481, Energy: -52.194454+0.001265j
[2025-09-04 14:14:35] [Iter 2083/2250] R3[1032/1200], Temp: 0.0476, Energy: -52.127732+0.000645j
[2025-09-04 14:14:53] [Iter 2084/2250] R3[1033/1200], Temp: 0.0470, Energy: -52.163979+0.000699j
[2025-09-04 14:15:11] [Iter 2085/2250] R3[1034/1200], Temp: 0.0465, Energy: -52.215484-0.001019j
[2025-09-04 14:15:29] [Iter 2086/2250] R3[1035/1200], Temp: 0.0459, Energy: -52.175756-0.001364j
[2025-09-04 14:15:47] [Iter 2087/2250] R3[1036/1200], Temp: 0.0454, Energy: -52.156374-0.000666j
[2025-09-04 14:16:05] [Iter 2088/2250] R3[1037/1200], Temp: 0.0448, Energy: -52.187240-0.002546j
[2025-09-04 14:16:23] [Iter 2089/2250] R3[1038/1200], Temp: 0.0443, Energy: -52.175487+0.000156j
[2025-09-04 14:16:41] [Iter 2090/2250] R3[1039/1200], Temp: 0.0438, Energy: -52.132979+0.000525j
[2025-09-04 14:16:59] [Iter 2091/2250] R3[1040/1200], Temp: 0.0432, Energy: -52.077466+0.001319j
[2025-09-04 14:17:17] [Iter 2092/2250] R3[1041/1200], Temp: 0.0427, Energy: -52.163768-0.000389j
[2025-09-04 14:17:35] [Iter 2093/2250] R3[1042/1200], Temp: 0.0422, Energy: -52.251089+0.000956j
[2025-09-04 14:17:53] [Iter 2094/2250] R3[1043/1200], Temp: 0.0416, Energy: -52.211631-0.001607j
[2025-09-04 14:18:11] [Iter 2095/2250] R3[1044/1200], Temp: 0.0411, Energy: -52.154593+0.001285j
[2025-09-04 14:18:29] [Iter 2096/2250] R3[1045/1200], Temp: 0.0406, Energy: -52.263199-0.001332j
[2025-09-04 14:18:47] [Iter 2097/2250] R3[1046/1200], Temp: 0.0401, Energy: -52.143815+0.000237j
[2025-09-04 14:19:05] [Iter 2098/2250] R3[1047/1200], Temp: 0.0396, Energy: -52.172336-0.000083j
[2025-09-04 14:19:23] [Iter 2099/2250] R3[1048/1200], Temp: 0.0391, Energy: -52.186718-0.001311j
[2025-09-04 14:19:41] [Iter 2100/2250] R3[1049/1200], Temp: 0.0386, Energy: -52.176852-0.000784j
[2025-09-04 14:19:59] [Iter 2101/2250] R3[1050/1200], Temp: 0.0381, Energy: -52.181037+0.002105j
[2025-09-04 14:20:16] [Iter 2102/2250] R3[1051/1200], Temp: 0.0376, Energy: -52.168801-0.001863j
[2025-09-04 14:20:34] [Iter 2103/2250] R3[1052/1200], Temp: 0.0371, Energy: -52.175000-0.000738j
[2025-09-04 14:20:52] [Iter 2104/2250] R3[1053/1200], Temp: 0.0366, Energy: -52.173035-0.001211j
[2025-09-04 14:21:10] [Iter 2105/2250] R3[1054/1200], Temp: 0.0361, Energy: -52.167127-0.001777j
[2025-09-04 14:21:28] [Iter 2106/2250] R3[1055/1200], Temp: 0.0356, Energy: -52.114878-0.000860j
[2025-09-04 14:21:46] [Iter 2107/2250] R3[1056/1200], Temp: 0.0351, Energy: -52.157283+0.001306j
[2025-09-04 14:22:04] [Iter 2108/2250] R3[1057/1200], Temp: 0.0346, Energy: -52.168211-0.001721j
[2025-09-04 14:22:22] [Iter 2109/2250] R3[1058/1200], Temp: 0.0342, Energy: -52.169530+0.000705j
[2025-09-04 14:22:40] [Iter 2110/2250] R3[1059/1200], Temp: 0.0337, Energy: -52.223223-0.003160j
[2025-09-04 14:22:58] [Iter 2111/2250] R3[1060/1200], Temp: 0.0332, Energy: -52.156436-0.000091j
[2025-09-04 14:23:16] [Iter 2112/2250] R3[1061/1200], Temp: 0.0327, Energy: -52.175235+0.001315j
[2025-09-04 14:23:34] [Iter 2113/2250] R3[1062/1200], Temp: 0.0323, Energy: -52.111677+0.001941j
[2025-09-04 14:23:52] [Iter 2114/2250] R3[1063/1200], Temp: 0.0318, Energy: -52.144694-0.001017j
[2025-09-04 14:24:10] [Iter 2115/2250] R3[1064/1200], Temp: 0.0314, Energy: -52.206015+0.000473j
[2025-09-04 14:24:28] [Iter 2116/2250] R3[1065/1200], Temp: 0.0309, Energy: -52.205518-0.001132j
[2025-09-04 14:24:46] [Iter 2117/2250] R3[1066/1200], Temp: 0.0305, Energy: -52.216320-0.000418j
[2025-09-04 14:25:04] [Iter 2118/2250] R3[1067/1200], Temp: 0.0300, Energy: -52.193616+0.000451j
[2025-09-04 14:25:22] [Iter 2119/2250] R3[1068/1200], Temp: 0.0296, Energy: -52.168244-0.000250j
[2025-09-04 14:25:40] [Iter 2120/2250] R3[1069/1200], Temp: 0.0291, Energy: -52.174764-0.001151j
[2025-09-04 14:25:58] [Iter 2121/2250] R3[1070/1200], Temp: 0.0287, Energy: -52.151915+0.000005j
[2025-09-04 14:26:16] [Iter 2122/2250] R3[1071/1200], Temp: 0.0282, Energy: -52.148817+0.001702j
[2025-09-04 14:26:34] [Iter 2123/2250] R3[1072/1200], Temp: 0.0278, Energy: -52.173176+0.000444j
[2025-09-04 14:26:52] [Iter 2124/2250] R3[1073/1200], Temp: 0.0274, Energy: -52.125994-0.000585j
[2025-09-04 14:27:10] [Iter 2125/2250] R3[1074/1200], Temp: 0.0270, Energy: -52.132372+0.001259j
[2025-09-04 14:27:28] [Iter 2126/2250] R3[1075/1200], Temp: 0.0265, Energy: -52.126413+0.002491j
[2025-09-04 14:27:45] [Iter 2127/2250] R3[1076/1200], Temp: 0.0261, Energy: -52.105329-0.001106j
[2025-09-04 14:28:03] [Iter 2128/2250] R3[1077/1200], Temp: 0.0257, Energy: -52.135485-0.001923j
[2025-09-04 14:28:21] [Iter 2129/2250] R3[1078/1200], Temp: 0.0253, Energy: -52.169150-0.000298j
[2025-09-04 14:28:39] [Iter 2130/2250] R3[1079/1200], Temp: 0.0249, Energy: -52.123640-0.002180j
[2025-09-04 14:28:57] [Iter 2131/2250] R3[1080/1200], Temp: 0.0245, Energy: -52.121558-0.002766j
[2025-09-04 14:29:15] [Iter 2132/2250] R3[1081/1200], Temp: 0.0241, Energy: -52.143021-0.000250j
[2025-09-04 14:29:33] [Iter 2133/2250] R3[1082/1200], Temp: 0.0237, Energy: -52.152797-0.002308j
[2025-09-04 14:29:51] [Iter 2134/2250] R3[1083/1200], Temp: 0.0233, Energy: -52.075018-0.001999j
[2025-09-04 14:30:09] [Iter 2135/2250] R3[1084/1200], Temp: 0.0229, Energy: -52.089173-0.001037j
[2025-09-04 14:30:27] [Iter 2136/2250] R3[1085/1200], Temp: 0.0225, Energy: -52.090027+0.001508j
[2025-09-04 14:30:45] [Iter 2137/2250] R3[1086/1200], Temp: 0.0221, Energy: -52.008975-0.001201j
[2025-09-04 14:31:03] [Iter 2138/2250] R3[1087/1200], Temp: 0.0217, Energy: -52.084637+0.000684j
[2025-09-04 14:31:21] [Iter 2139/2250] R3[1088/1200], Temp: 0.0213, Energy: -52.086170-0.000154j
[2025-09-04 14:31:39] [Iter 2140/2250] R3[1089/1200], Temp: 0.0210, Energy: -52.201206+0.000817j
[2025-09-04 14:31:57] [Iter 2141/2250] R3[1090/1200], Temp: 0.0206, Energy: -52.131147-0.002966j
[2025-09-04 14:32:15] [Iter 2142/2250] R3[1091/1200], Temp: 0.0202, Energy: -52.153616+0.000523j
[2025-09-04 14:32:33] [Iter 2143/2250] R3[1092/1200], Temp: 0.0199, Energy: -52.111396-0.000490j
[2025-09-04 14:32:51] [Iter 2144/2250] R3[1093/1200], Temp: 0.0195, Energy: -52.139168+0.001383j
[2025-09-04 14:33:09] [Iter 2145/2250] R3[1094/1200], Temp: 0.0191, Energy: -52.088961+0.000209j
[2025-09-04 14:33:27] [Iter 2146/2250] R3[1095/1200], Temp: 0.0188, Energy: -52.086600+0.002209j
[2025-09-04 14:33:45] [Iter 2147/2250] R3[1096/1200], Temp: 0.0184, Energy: -52.091727-0.000972j
[2025-09-04 14:34:03] [Iter 2148/2250] R3[1097/1200], Temp: 0.0181, Energy: -52.165381-0.000591j
[2025-09-04 14:34:21] [Iter 2149/2250] R3[1098/1200], Temp: 0.0177, Energy: -52.235546+0.002596j
[2025-09-04 14:34:39] [Iter 2150/2250] R3[1099/1200], Temp: 0.0174, Energy: -52.107303-0.001243j
[2025-09-04 14:34:57] [Iter 2151/2250] R3[1100/1200], Temp: 0.0170, Energy: -52.085564-0.000366j
[2025-09-04 14:35:15] [Iter 2152/2250] R3[1101/1200], Temp: 0.0167, Energy: -52.094220+0.000788j
[2025-09-04 14:35:32] [Iter 2153/2250] R3[1102/1200], Temp: 0.0164, Energy: -52.114302+0.000061j
[2025-09-04 14:35:50] [Iter 2154/2250] R3[1103/1200], Temp: 0.0160, Energy: -52.103847+0.000736j
[2025-09-04 14:36:08] [Iter 2155/2250] R3[1104/1200], Temp: 0.0157, Energy: -52.173538+0.000064j
[2025-09-04 14:36:26] [Iter 2156/2250] R3[1105/1200], Temp: 0.0154, Energy: -52.141081+0.001400j
[2025-09-04 14:36:44] [Iter 2157/2250] R3[1106/1200], Temp: 0.0151, Energy: -52.175786-0.001490j
[2025-09-04 14:37:02] [Iter 2158/2250] R3[1107/1200], Temp: 0.0147, Energy: -52.173033+0.001933j
[2025-09-04 14:37:20] [Iter 2159/2250] R3[1108/1200], Temp: 0.0144, Energy: -52.203226-0.000669j
[2025-09-04 14:37:38] [Iter 2160/2250] R3[1109/1200], Temp: 0.0141, Energy: -52.234867+0.000106j
[2025-09-04 14:37:56] [Iter 2161/2250] R3[1110/1200], Temp: 0.0138, Energy: -52.230645+0.000399j
[2025-09-04 14:38:14] [Iter 2162/2250] R3[1111/1200], Temp: 0.0135, Energy: -52.191391-0.000603j
[2025-09-04 14:38:32] [Iter 2163/2250] R3[1112/1200], Temp: 0.0132, Energy: -52.167593-0.000853j
[2025-09-04 14:38:50] [Iter 2164/2250] R3[1113/1200], Temp: 0.0129, Energy: -52.228414-0.000797j
[2025-09-04 14:39:08] [Iter 2165/2250] R3[1114/1200], Temp: 0.0126, Energy: -52.178558-0.001549j
[2025-09-04 14:39:26] [Iter 2166/2250] R3[1115/1200], Temp: 0.0123, Energy: -52.224945+0.001900j
[2025-09-04 14:39:44] [Iter 2167/2250] R3[1116/1200], Temp: 0.0120, Energy: -52.170453-0.000652j
[2025-09-04 14:40:02] [Iter 2168/2250] R3[1117/1200], Temp: 0.0118, Energy: -52.175526-0.002675j
[2025-09-04 14:40:20] [Iter 2169/2250] R3[1118/1200], Temp: 0.0115, Energy: -52.186149+0.000432j
[2025-09-04 14:40:38] [Iter 2170/2250] R3[1119/1200], Temp: 0.0112, Energy: -52.223987-0.000790j
[2025-09-04 14:40:56] [Iter 2171/2250] R3[1120/1200], Temp: 0.0109, Energy: -52.215407-0.000378j
[2025-09-04 14:41:14] [Iter 2172/2250] R3[1121/1200], Temp: 0.0107, Energy: -52.194979-0.000789j
[2025-09-04 14:41:32] [Iter 2173/2250] R3[1122/1200], Temp: 0.0104, Energy: -52.164362+0.000014j
[2025-09-04 14:41:50] [Iter 2174/2250] R3[1123/1200], Temp: 0.0101, Energy: -52.160875+0.000126j
[2025-09-04 14:42:08] [Iter 2175/2250] R3[1124/1200], Temp: 0.0099, Energy: -52.182967-0.000942j
[2025-09-04 14:42:26] [Iter 2176/2250] R3[1125/1200], Temp: 0.0096, Energy: -52.197043-0.002031j
[2025-09-04 14:42:44] [Iter 2177/2250] R3[1126/1200], Temp: 0.0094, Energy: -52.178482+0.000117j
[2025-09-04 14:43:02] [Iter 2178/2250] R3[1127/1200], Temp: 0.0091, Energy: -52.201936+0.000052j
[2025-09-04 14:43:20] [Iter 2179/2250] R3[1128/1200], Temp: 0.0089, Energy: -52.164971+0.000431j
[2025-09-04 14:43:37] [Iter 2180/2250] R3[1129/1200], Temp: 0.0086, Energy: -52.120241-0.001173j
[2025-09-04 14:43:55] [Iter 2181/2250] R3[1130/1200], Temp: 0.0084, Energy: -52.135055+0.002037j
[2025-09-04 14:44:13] [Iter 2182/2250] R3[1131/1200], Temp: 0.0081, Energy: -52.196298+0.001544j
[2025-09-04 14:44:31] [Iter 2183/2250] R3[1132/1200], Temp: 0.0079, Energy: -52.180376-0.000766j
[2025-09-04 14:44:49] [Iter 2184/2250] R3[1133/1200], Temp: 0.0077, Energy: -52.143915+0.003508j
[2025-09-04 14:45:07] [Iter 2185/2250] R3[1134/1200], Temp: 0.0074, Energy: -52.103942+0.001027j
[2025-09-04 14:45:25] [Iter 2186/2250] R3[1135/1200], Temp: 0.0072, Energy: -52.118522-0.000542j
[2025-09-04 14:45:43] [Iter 2187/2250] R3[1136/1200], Temp: 0.0070, Energy: -52.143665+0.000856j
[2025-09-04 14:46:01] [Iter 2188/2250] R3[1137/1200], Temp: 0.0068, Energy: -52.146957-0.001087j
[2025-09-04 14:46:19] [Iter 2189/2250] R3[1138/1200], Temp: 0.0066, Energy: -52.161779-0.001028j
[2025-09-04 14:46:37] [Iter 2190/2250] R3[1139/1200], Temp: 0.0064, Energy: -52.139097-0.000037j
[2025-09-04 14:46:55] [Iter 2191/2250] R3[1140/1200], Temp: 0.0062, Energy: -52.093337+0.000763j
[2025-09-04 14:47:13] [Iter 2192/2250] R3[1141/1200], Temp: 0.0060, Energy: -52.121534-0.000439j
[2025-09-04 14:47:31] [Iter 2193/2250] R3[1142/1200], Temp: 0.0058, Energy: -52.131871-0.001084j
[2025-09-04 14:47:49] [Iter 2194/2250] R3[1143/1200], Temp: 0.0056, Energy: -52.149545-0.003608j
[2025-09-04 14:48:07] [Iter 2195/2250] R3[1144/1200], Temp: 0.0054, Energy: -52.170083+0.001541j
[2025-09-04 14:48:25] [Iter 2196/2250] R3[1145/1200], Temp: 0.0052, Energy: -52.155404-0.002434j
[2025-09-04 14:48:43] [Iter 2197/2250] R3[1146/1200], Temp: 0.0050, Energy: -52.128297+0.001013j
[2025-09-04 14:49:01] [Iter 2198/2250] R3[1147/1200], Temp: 0.0048, Energy: -52.155947-0.000968j
[2025-09-04 14:49:19] [Iter 2199/2250] R3[1148/1200], Temp: 0.0046, Energy: -52.157046+0.002107j
[2025-09-04 14:49:37] [Iter 2200/2250] R3[1149/1200], Temp: 0.0045, Energy: -52.167431+0.000247j
[2025-09-04 14:49:55] [Iter 2201/2250] R3[1150/1200], Temp: 0.0043, Energy: -52.152422-0.001958j
[2025-09-04 14:50:13] [Iter 2202/2250] R3[1151/1200], Temp: 0.0041, Energy: -52.117468+0.001412j
[2025-09-04 14:50:31] [Iter 2203/2250] R3[1152/1200], Temp: 0.0039, Energy: -52.101304-0.000360j
[2025-09-04 14:50:49] [Iter 2204/2250] R3[1153/1200], Temp: 0.0038, Energy: -52.140214+0.000127j
[2025-09-04 14:51:07] [Iter 2205/2250] R3[1154/1200], Temp: 0.0036, Energy: -52.144775+0.001547j
[2025-09-04 14:51:24] [Iter 2206/2250] R3[1155/1200], Temp: 0.0035, Energy: -52.145610+0.000565j
[2025-09-04 14:51:42] [Iter 2207/2250] R3[1156/1200], Temp: 0.0033, Energy: -52.141110+0.000366j
[2025-09-04 14:52:00] [Iter 2208/2250] R3[1157/1200], Temp: 0.0032, Energy: -52.100806-0.000253j
[2025-09-04 14:52:18] [Iter 2209/2250] R3[1158/1200], Temp: 0.0030, Energy: -52.143261+0.001418j
[2025-09-04 14:52:36] [Iter 2210/2250] R3[1159/1200], Temp: 0.0029, Energy: -52.116376+0.001322j
[2025-09-04 14:52:54] [Iter 2211/2250] R3[1160/1200], Temp: 0.0027, Energy: -52.162942+0.001529j
[2025-09-04 14:53:12] [Iter 2212/2250] R3[1161/1200], Temp: 0.0026, Energy: -52.177319+0.000071j
[2025-09-04 14:53:30] [Iter 2213/2250] R3[1162/1200], Temp: 0.0025, Energy: -52.078477-0.001022j
[2025-09-04 14:53:48] [Iter 2214/2250] R3[1163/1200], Temp: 0.0023, Energy: -52.117956-0.002272j
[2025-09-04 14:54:06] [Iter 2215/2250] R3[1164/1200], Temp: 0.0022, Energy: -52.101536-0.000828j
[2025-09-04 14:54:24] [Iter 2216/2250] R3[1165/1200], Temp: 0.0021, Energy: -52.073113+0.000231j
[2025-09-04 14:54:42] [Iter 2217/2250] R3[1166/1200], Temp: 0.0020, Energy: -52.054724-0.000570j
[2025-09-04 14:55:00] [Iter 2218/2250] R3[1167/1200], Temp: 0.0019, Energy: -52.106549+0.000510j
[2025-09-04 14:55:18] [Iter 2219/2250] R3[1168/1200], Temp: 0.0018, Energy: -52.108225-0.000776j
[2025-09-04 14:55:36] [Iter 2220/2250] R3[1169/1200], Temp: 0.0016, Energy: -52.151705+0.000398j
[2025-09-04 14:55:54] [Iter 2221/2250] R3[1170/1200], Temp: 0.0015, Energy: -52.178816-0.000992j
[2025-09-04 14:56:12] [Iter 2222/2250] R3[1171/1200], Temp: 0.0014, Energy: -52.166302-0.000545j
[2025-09-04 14:56:30] [Iter 2223/2250] R3[1172/1200], Temp: 0.0013, Energy: -52.166090-0.003975j
[2025-09-04 14:56:48] [Iter 2224/2250] R3[1173/1200], Temp: 0.0012, Energy: -52.156730-0.002038j
[2025-09-04 14:57:06] [Iter 2225/2250] R3[1174/1200], Temp: 0.0012, Energy: -52.099670-0.001453j
[2025-09-04 14:57:24] [Iter 2226/2250] R3[1175/1200], Temp: 0.0011, Energy: -52.148743-0.001978j
[2025-09-04 14:57:42] [Iter 2227/2250] R3[1176/1200], Temp: 0.0010, Energy: -52.189033-0.001679j
[2025-09-04 14:57:59] [Iter 2228/2250] R3[1177/1200], Temp: 0.0009, Energy: -52.172549-0.002050j
[2025-09-04 14:58:17] [Iter 2229/2250] R3[1178/1200], Temp: 0.0008, Energy: -52.143419-0.000467j
[2025-09-04 14:58:35] [Iter 2230/2250] R3[1179/1200], Temp: 0.0008, Energy: -52.176861+0.000950j
[2025-09-04 14:58:53] [Iter 2231/2250] R3[1180/1200], Temp: 0.0007, Energy: -52.200684-0.000422j
[2025-09-04 14:59:11] [Iter 2232/2250] R3[1181/1200], Temp: 0.0006, Energy: -52.196224-0.000108j
[2025-09-04 14:59:29] [Iter 2233/2250] R3[1182/1200], Temp: 0.0006, Energy: -52.211229+0.000058j
[2025-09-04 14:59:47] [Iter 2234/2250] R3[1183/1200], Temp: 0.0005, Energy: -52.188293+0.001127j
[2025-09-04 15:00:05] [Iter 2235/2250] R3[1184/1200], Temp: 0.0004, Energy: -52.230556-0.000891j
[2025-09-04 15:00:23] [Iter 2236/2250] R3[1185/1200], Temp: 0.0004, Energy: -52.206417+0.002511j
[2025-09-04 15:00:41] [Iter 2237/2250] R3[1186/1200], Temp: 0.0003, Energy: -52.161566+0.000923j
[2025-09-04 15:00:59] [Iter 2238/2250] R3[1187/1200], Temp: 0.0003, Energy: -52.175564-0.002095j
[2025-09-04 15:01:17] [Iter 2239/2250] R3[1188/1200], Temp: 0.0002, Energy: -52.178300+0.000986j
[2025-09-04 15:01:35] [Iter 2240/2250] R3[1189/1200], Temp: 0.0002, Energy: -52.167821-0.001161j
[2025-09-04 15:01:53] [Iter 2241/2250] R3[1190/1200], Temp: 0.0002, Energy: -52.197600-0.001915j
[2025-09-04 15:02:11] [Iter 2242/2250] R3[1191/1200], Temp: 0.0001, Energy: -52.169063+0.002334j
[2025-09-04 15:02:29] [Iter 2243/2250] R3[1192/1200], Temp: 0.0001, Energy: -52.137442+0.001133j
[2025-09-04 15:02:47] [Iter 2244/2250] R3[1193/1200], Temp: 0.0001, Energy: -52.181224-0.001674j
[2025-09-04 15:03:05] [Iter 2245/2250] R3[1194/1200], Temp: 0.0001, Energy: -52.168941-0.000247j
[2025-09-04 15:03:23] [Iter 2246/2250] R3[1195/1200], Temp: 0.0000, Energy: -52.197055-0.000036j
[2025-09-04 15:03:41] [Iter 2247/2250] R3[1196/1200], Temp: 0.0000, Energy: -52.150903+0.001308j
[2025-09-04 15:03:59] [Iter 2248/2250] R3[1197/1200], Temp: 0.0000, Energy: -52.165460-0.001349j
[2025-09-04 15:04:17] [Iter 2249/2250] R3[1198/1200], Temp: 0.0000, Energy: -52.138520+0.000871j
[2025-09-04 15:04:35] [Iter 2250/2250] R3[1199/1200], Temp: 0.0000, Energy: -52.160097+0.001458j
[2025-09-04 15:04:35] ✓ Checkpoint saved: checkpoint_iter_002250.pkl
[2025-09-04 15:04:35] ✅ Training completed | Restarts: 3
[2025-09-04 15:04:35] ============================================================
[2025-09-04 15:04:35] Training completed | Runtime: 88268.8s
[2025-09-04 15:04:42] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-04 15:04:42] ============================================================
