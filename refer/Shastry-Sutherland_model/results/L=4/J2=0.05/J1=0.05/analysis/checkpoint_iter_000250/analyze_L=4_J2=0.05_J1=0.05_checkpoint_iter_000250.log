[2025-09-06 08:26:10] 使用checkpoint文件: results/L=4/J2=0.05/J1=0.05/training/checkpoints/checkpoint_iter_000250.pkl
[2025-09-06 08:26:27] ✓ 从checkpoint加载参数: 250
[2025-09-06 08:26:27]   - 能量: -51.021693-0.002996j ± 0.041196
[2025-09-06 08:26:27] ================================================================================
[2025-09-06 08:26:27] 加载量子态: L=4, J2=0.05, J1=0.05, checkpoint=checkpoint_iter_000250
[2025-09-06 08:26:27] 使用采样数目: 1048576
[2025-09-06 08:26:27] 设置样本数为: 1048576
[2025-09-06 08:26:27] 开始生成共享样本集...
[2025-09-06 08:30:52] 样本生成完成,耗时: 265.211 秒
[2025-09-06 08:30:52] ================================================================================
[2025-09-06 08:30:52] 开始计算自旋结构因子...
[2025-09-06 08:30:52] 初始化操作符缓存...
[2025-09-06 08:30:52] 预构建所有自旋相关操作符...
[2025-09-06 08:30:53] 开始计算自旋相关函数...
[2025-09-06 08:31:06] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 13.439s
[2025-09-06 08:31:24] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 18.536s
[2025-09-06 08:31:39] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 14.013s
[2025-09-06 08:31:53] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 14.068s
[2025-09-06 08:32:07] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 14.056s
[2025-09-06 08:32:21] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 14.049s
[2025-09-06 08:32:35] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 14.010s
[2025-09-06 08:32:49] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 14.062s
[2025-09-06 08:33:03] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 14.009s
[2025-09-06 08:33:17] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 14.048s
[2025-09-06 08:33:31] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 14.016s
[2025-09-06 08:33:45] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 14.061s
[2025-09-06 08:33:59] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 14.013s
[2025-09-06 08:34:13] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 14.056s
[2025-09-06 08:34:27] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 14.061s
[2025-09-06 08:34:41] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 14.033s
[2025-09-06 08:34:55] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 14.056s
[2025-09-06 08:35:09] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 14.054s
[2025-09-06 08:35:23] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 14.013s
[2025-09-06 08:35:37] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 14.048s
[2025-09-06 08:35:51] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 14.053s
[2025-09-06 08:36:05] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 14.059s
[2025-09-06 08:36:19] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 14.026s
[2025-09-06 08:36:34] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 14.059s
[2025-09-06 08:36:48] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 14.025s
[2025-09-06 08:37:02] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 14.063s
[2025-09-06 08:37:16] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 14.022s
[2025-09-06 08:37:30] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 14.058s
[2025-09-06 08:37:44] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 14.016s
[2025-09-06 08:37:58] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 14.019s
[2025-09-06 08:38:12] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 14.014s
[2025-09-06 08:38:26] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 14.049s
[2025-09-06 08:38:40] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 14.056s
[2025-09-06 08:38:54] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 14.058s
[2025-09-06 08:39:08] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 14.019s
[2025-09-06 08:39:22] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 14.063s
[2025-09-06 08:39:36] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 14.012s
[2025-09-06 08:39:50] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 14.056s
[2025-09-06 08:40:04] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 14.014s
[2025-09-06 08:40:18] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 14.053s
[2025-09-06 08:40:32] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 14.030s
[2025-09-06 08:40:46] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 14.015s
[2025-09-06 08:41:00] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 14.037s
[2025-09-06 08:41:14] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 14.053s
[2025-09-06 08:41:28] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 14.024s
[2025-09-06 08:41:42] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 14.012s
[2025-09-06 08:41:56] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 14.065s
[2025-09-06 08:42:10] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 14.025s
[2025-09-06 08:42:25] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 14.066s
[2025-09-06 08:42:39] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 14.060s
[2025-09-06 08:42:53] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 14.025s
[2025-09-06 08:43:07] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 14.063s
[2025-09-06 08:43:21] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 14.026s
[2025-09-06 08:43:35] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 14.062s
[2025-09-06 08:43:49] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 14.011s
[2025-09-06 08:44:03] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 14.061s
[2025-09-06 08:44:17] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 14.012s
[2025-09-06 08:44:31] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 14.022s
[2025-09-06 08:44:45] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 14.013s
[2025-09-06 08:44:59] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 14.052s
[2025-09-06 08:45:13] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 14.035s
[2025-09-06 08:45:27] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 14.011s
[2025-09-06 08:45:41] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 14.059s
[2025-09-06 08:45:55] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 14.015s
[2025-09-06 08:45:55] 自旋相关函数计算完成,总耗时 902.62 秒
[2025-09-06 08:45:57] 计算傅里叶变换...
[2025-09-06 08:46:00] 自旋结构因子计算完成
[2025-09-06 08:46:01] 自旋相关函数平均误差: 0.000721
