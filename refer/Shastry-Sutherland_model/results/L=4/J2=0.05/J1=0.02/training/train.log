[2025-09-05 00:57:52] ✓ 从checkpoint恢复: results/L=4/J2=0.05/J1=0.03/training/checkpoints/final_GCNN.pkl
[2025-09-05 00:57:52]   - 迭代次数: final
[2025-09-05 00:57:52]   - 能量: -51.233486+0.000396j ± 0.081506
[2025-09-05 00:57:52]   - 时间戳: 2025-09-05T00:57:27.999706+08:00
[2025-09-05 00:58:03] ✓ 变分状态参数已从checkpoint恢复
[2025-09-05 00:58:03] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-05 00:58:03] ==================================================
[2025-09-05 00:58:03] GCNN for Shastry-Sutherland Model
[2025-09-05 00:58:03] ==================================================
[2025-09-05 00:58:03] System parameters:
[2025-09-05 00:58:03]   - System size: L=4, N=64
[2025-09-05 00:58:03]   - System parameters: J1=0.02, J2=0.05, Q=0.95
[2025-09-05 00:58:03] --------------------------------------------------
[2025-09-05 00:58:03] Model parameters:
[2025-09-05 00:58:03]   - Number of layers = 4
[2025-09-05 00:58:03]   - Number of features = 4
[2025-09-05 00:58:03]   - Total parameters = 12572
[2025-09-05 00:58:03] --------------------------------------------------
[2025-09-05 00:58:03] Training parameters:
[2025-09-05 00:58:03]   - Learning rate: 0.015
[2025-09-05 00:58:03]   - Total iterations: 1050
[2025-09-05 00:58:03]   - Annealing cycles: 3
[2025-09-05 00:58:03]   - Initial period: 150
[2025-09-05 00:58:03]   - Period multiplier: 2.0
[2025-09-05 00:58:03]   - Temperature range: 0.0-1.0
[2025-09-05 00:58:03]   - Samples: 4096
[2025-09-05 00:58:03]   - Discarded samples: 0
[2025-09-05 00:58:03]   - Chunk size: 2048
[2025-09-05 00:58:03]   - Diagonal shift: 0.2
[2025-09-05 00:58:03]   - Gradient clipping: 1.0
[2025-09-05 00:58:03]   - Checkpoint enabled: interval=105
[2025-09-05 00:58:03]   - Checkpoint directory: results/L=4/J2=0.05/J1=0.02/training/checkpoints
[2025-09-05 00:58:03] --------------------------------------------------
[2025-09-05 00:58:03] Device status:
[2025-09-05 00:58:03]   - Devices model: NVIDIA H200 NVL
[2025-09-05 00:58:03]   - Number of devices: 1
[2025-09-05 00:58:03]   - Sharding: True
[2025-09-05 00:58:03] ============================================================
[2025-09-05 00:58:50] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -51.077966-0.000906j
[2025-09-05 00:59:20] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -51.043762+0.002258j
[2025-09-05 00:59:30] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -51.006784+0.005071j
[2025-09-05 00:59:40] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -51.021176-0.002858j
[2025-09-05 00:59:50] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -51.076901-0.003971j
[2025-09-05 01:00:00] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -51.122472-0.002865j
[2025-09-05 01:00:10] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -51.107583-0.002728j
[2025-09-05 01:00:20] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -51.022417+0.004991j
[2025-09-05 01:00:30] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -51.053645+0.002206j
[2025-09-05 01:00:40] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -50.995131+0.000616j
[2025-09-05 01:00:50] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -50.943873+0.000007j
[2025-09-05 01:01:01] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -51.080437+0.000558j
[2025-09-05 01:01:11] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -50.949300+0.001285j
[2025-09-05 01:01:21] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -51.101869-0.002369j
[2025-09-05 01:01:31] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -51.053075-0.000427j
[2025-09-05 01:01:41] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -51.105977-0.000550j
[2025-09-05 01:01:51] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -50.952394-0.001273j
[2025-09-05 01:02:02] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -50.918569+0.001956j
[2025-09-05 01:02:12] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -50.862789-0.004062j
[2025-09-05 01:02:22] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -50.896822-0.003102j
[2025-09-05 01:02:32] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -50.862862+0.003272j
[2025-09-05 01:02:42] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -50.969868-0.000341j
[2025-09-05 01:02:52] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -51.044963+0.002729j
[2025-09-05 01:03:02] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -51.054836+0.006514j
[2025-09-05 01:03:13] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -51.077093+0.003647j
[2025-09-05 01:03:23] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -51.097833-0.000116j
[2025-09-05 01:03:33] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -51.076745+0.002387j
[2025-09-05 01:03:43] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -51.030748+0.002869j
[2025-09-05 01:03:53] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -51.008811-0.002439j
[2025-09-05 01:04:03] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -51.036073-0.002338j
[2025-09-05 01:04:14] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -51.083586+0.004477j
[2025-09-05 01:04:24] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -50.997133+0.000142j
[2025-09-05 01:04:34] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -50.940790+0.001680j
[2025-09-05 01:04:44] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -50.917838+0.001728j
[2025-09-05 01:04:54] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -51.027606+0.000781j
[2025-09-05 01:05:04] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -51.048059-0.001140j
[2025-09-05 01:05:14] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -51.011479+0.004875j
[2025-09-05 01:05:25] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -50.950123-0.000633j
[2025-09-05 01:05:35] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -50.861070+0.001512j
[2025-09-05 01:05:45] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -51.051091+0.001101j
[2025-09-05 01:05:55] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -50.903292-0.000195j
[2025-09-05 01:06:05] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -51.038094-0.000242j
[2025-09-05 01:06:15] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -50.833228+0.003358j
[2025-09-05 01:06:25] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -50.945135+0.002925j
[2025-09-05 01:06:36] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -51.050582-0.003733j
[2025-09-05 01:06:46] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -50.974210-0.002561j
[2025-09-05 01:06:56] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -51.007095+0.002935j
[2025-09-05 01:07:06] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -50.916862-0.004188j
[2025-09-05 01:07:16] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -50.983430+0.000429j
[2025-09-05 01:07:26] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -51.064162+0.000088j
[2025-09-05 01:07:36] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -50.796001+0.001325j
[2025-09-05 01:07:47] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -50.929529-0.002409j
[2025-09-05 01:07:57] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -50.989045-0.003954j
[2025-09-05 01:08:07] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -50.952414+0.002833j
[2025-09-05 01:08:17] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -51.030403+0.001769j
[2025-09-05 01:08:27] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -50.907891+0.001846j
[2025-09-05 01:08:37] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -50.979959-0.002187j
[2025-09-05 01:08:48] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -50.949166+0.000640j
[2025-09-05 01:08:58] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -50.958774+0.001180j
[2025-09-05 01:09:08] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -50.864182+0.000981j
[2025-09-05 01:09:18] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -51.078738-0.000770j
[2025-09-05 01:09:28] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -51.108811+0.000350j
[2025-09-05 01:09:38] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -50.960085+0.002281j
[2025-09-05 01:09:48] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -51.006186+0.002232j
[2025-09-05 01:09:59] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -51.128694+0.000235j
[2025-09-05 01:10:09] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -50.984214+0.003813j
[2025-09-05 01:10:19] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -50.974946-0.003870j
[2025-09-05 01:10:29] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -50.899546-0.001320j
[2025-09-05 01:10:39] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -50.968704+0.002624j
[2025-09-05 01:10:49] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -51.050859-0.003416j
[2025-09-05 01:11:00] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -50.928148+0.000430j
[2025-09-05 01:11:10] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -50.912863-0.002007j
[2025-09-05 01:11:20] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -50.896096-0.001920j
[2025-09-05 01:11:30] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -50.986272-0.000913j
[2025-09-05 01:11:40] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -50.966854-0.000144j
[2025-09-05 01:11:50] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -50.888135-0.002874j
[2025-09-05 01:12:00] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -50.947484-0.002915j
[2025-09-05 01:12:11] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -51.036818+0.000770j
[2025-09-05 01:12:21] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -50.933315+0.001999j
[2025-09-05 01:12:31] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -50.917278+0.002261j
[2025-09-05 01:12:41] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -50.965158-0.001414j
[2025-09-05 01:12:51] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -50.851418-0.003615j
[2025-09-05 01:13:01] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -50.940949-0.000416j
[2025-09-05 01:13:11] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -50.986427-0.003209j
[2025-09-05 01:13:22] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -51.063292+0.000959j
[2025-09-05 01:13:32] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -50.974393-0.001413j
[2025-09-05 01:13:42] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -50.921208+0.000911j
[2025-09-05 01:13:52] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -50.930807-0.001397j
[2025-09-05 01:14:02] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -50.960042-0.001430j
[2025-09-05 01:14:12] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -50.997787-0.000620j
[2025-09-05 01:14:22] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -51.010439-0.004545j
[2025-09-05 01:14:33] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -50.978974-0.001575j
[2025-09-05 01:14:43] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -50.954336+0.004621j
[2025-09-05 01:14:53] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -50.981001-0.000144j
[2025-09-05 01:15:03] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -51.011419-0.003737j
[2025-09-05 01:15:13] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -50.957931+0.002382j
[2025-09-05 01:15:23] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -51.021464-0.002848j
[2025-09-05 01:15:33] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -51.081195-0.000723j
[2025-09-05 01:15:44] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -51.023203-0.001598j
[2025-09-05 01:15:54] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -51.019828+0.000354j
[2025-09-05 01:16:04] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -50.916264-0.003277j
[2025-09-05 01:16:14] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -50.990637-0.002991j
[2025-09-05 01:16:24] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -51.010719-0.003209j
[2025-09-05 01:16:34] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -51.095298+0.001881j
[2025-09-05 01:16:44] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -51.035330+0.002950j
[2025-09-05 01:16:44] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-05 01:16:55] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -51.011002+0.001620j
[2025-09-05 01:17:05] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -50.906362-0.000406j
[2025-09-05 01:17:15] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -50.901107+0.002511j
[2025-09-05 01:17:25] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -50.964687-0.001923j
[2025-09-05 01:17:35] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -50.907397-0.001239j
[2025-09-05 01:17:45] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -50.941680+0.000416j
[2025-09-05 01:17:55] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -50.957686-0.003026j
[2025-09-05 01:18:06] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -50.968145-0.002197j
[2025-09-05 01:18:16] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -50.984068-0.001407j
[2025-09-05 01:18:26] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -50.984787-0.002779j
[2025-09-05 01:18:36] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -50.931594-0.001911j
[2025-09-05 01:18:46] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -50.927637+0.002420j
[2025-09-05 01:18:56] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -50.908313+0.000576j
[2025-09-05 01:19:06] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -51.081784+0.001110j
[2025-09-05 01:19:17] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -50.992828+0.000281j
[2025-09-05 01:19:27] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -51.053153+0.000104j
[2025-09-05 01:19:37] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -50.965773+0.000291j
[2025-09-05 01:19:47] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -50.992310+0.000322j
[2025-09-05 01:19:57] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -50.879721-0.000929j
[2025-09-05 01:20:07] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -50.855378+0.000268j
[2025-09-05 01:20:17] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -50.824644-0.001750j
[2025-09-05 01:20:28] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -50.892556+0.000739j
[2025-09-05 01:20:38] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -50.849019-0.001159j
[2025-09-05 01:20:48] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -50.874866+0.002357j
[2025-09-05 01:20:58] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -50.842195+0.001150j
[2025-09-05 01:21:08] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -50.868769+0.003948j
[2025-09-05 01:21:18] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -50.882274-0.001672j
[2025-09-05 01:21:28] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -51.075214+0.001170j
[2025-09-05 01:21:39] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -50.972467-0.000555j
[2025-09-05 01:21:49] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -50.981533+0.002423j
[2025-09-05 01:21:59] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -50.981465-0.001353j
[2025-09-05 01:22:09] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -51.044792+0.000772j
[2025-09-05 01:22:19] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -51.038442-0.007249j
[2025-09-05 01:22:29] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -51.058523+0.000528j
[2025-09-05 01:22:40] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -51.001526+0.003539j
[2025-09-05 01:22:50] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -51.047007-0.000443j
[2025-09-05 01:23:00] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -51.040155-0.001866j
[2025-09-05 01:23:10] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -51.017062+0.000140j
[2025-09-05 01:23:20] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -50.934808+0.002953j
[2025-09-05 01:23:30] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -50.997344+0.003966j
[2025-09-05 01:23:40] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -50.894486-0.002743j
[2025-09-05 01:23:51] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -50.980471-0.000971j
[2025-09-05 01:24:01] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -50.965051-0.003848j
[2025-09-05 01:24:11] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -50.999706+0.000249j
[2025-09-05 01:24:21] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -50.956508-0.000373j
[2025-09-05 01:24:21] RESTART #1 | Period: 300
[2025-09-05 01:24:31] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -50.929240-0.001430j
[2025-09-05 01:24:41] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -50.899164+0.002726j
[2025-09-05 01:24:51] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -50.955566-0.002395j
[2025-09-05 01:25:02] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -51.030509+0.002380j
[2025-09-05 01:25:12] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -51.027792+0.001841j
[2025-09-05 01:25:22] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -50.956505-0.000109j
[2025-09-05 01:25:32] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -50.904074-0.000796j
[2025-09-05 01:25:42] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -51.034001+0.004231j
[2025-09-05 01:25:52] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -51.005581-0.001046j
[2025-09-05 01:26:02] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -50.904291-0.000682j
[2025-09-05 01:26:13] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -51.030071+0.000155j
[2025-09-05 01:26:23] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -50.990858-0.001340j
[2025-09-05 01:26:33] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -51.153119-0.001266j
[2025-09-05 01:26:43] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -51.159413-0.000803j
[2025-09-05 01:26:53] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -51.135556-0.000258j
[2025-09-05 01:27:03] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -51.093607+0.003801j
[2025-09-05 01:27:13] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -50.978223+0.002005j
[2025-09-05 01:27:24] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -50.990031-0.000160j
[2025-09-05 01:27:34] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -51.018723-0.001564j
[2025-09-05 01:27:44] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -51.046169+0.001231j
[2025-09-05 01:27:54] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -51.204109+0.000273j
[2025-09-05 01:28:04] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -51.115619-0.001699j
[2025-09-05 01:28:14] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -51.077929+0.000662j
[2025-09-05 01:28:25] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -50.978645+0.000152j
[2025-09-05 01:28:35] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -50.899330+0.001284j
[2025-09-05 01:28:45] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -50.920649+0.000238j
[2025-09-05 01:28:55] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -50.831459+0.000697j
[2025-09-05 01:29:05] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -50.869420-0.004428j
[2025-09-05 01:29:15] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -50.875844-0.003149j
[2025-09-05 01:29:25] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -50.948390-0.004429j
[2025-09-05 01:29:36] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -50.923994+0.001434j
[2025-09-05 01:29:46] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -50.846769-0.002000j
[2025-09-05 01:29:56] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -50.832051+0.001480j
[2025-09-05 01:30:06] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -50.862375-0.001218j
[2025-09-05 01:30:16] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -50.783153-0.001015j
[2025-09-05 01:30:26] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -50.942685-0.001525j
[2025-09-05 01:30:36] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -50.895623-0.003099j
[2025-09-05 01:30:47] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -50.979768+0.000666j
[2025-09-05 01:30:57] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -50.845071-0.001612j
[2025-09-05 01:31:07] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -50.857649-0.004613j
[2025-09-05 01:31:17] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -50.972062-0.002872j
[2025-09-05 01:31:27] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -51.021918-0.000545j
[2025-09-05 01:31:37] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -51.025903-0.002456j
[2025-09-05 01:31:48] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -51.032759+0.000520j
[2025-09-05 01:31:58] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -51.080535-0.001688j
[2025-09-05 01:32:08] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -51.082322-0.000112j
[2025-09-05 01:32:18] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -51.006757-0.001285j
[2025-09-05 01:32:28] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -51.016925-0.002580j
[2025-09-05 01:32:38] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -50.894532+0.001631j
[2025-09-05 01:32:48] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -50.911607+0.000191j
[2025-09-05 01:32:59] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -50.897301-0.000199j
[2025-09-05 01:33:09] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -50.978308-0.000090j
[2025-09-05 01:33:19] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -50.910271+0.001653j
[2025-09-05 01:33:29] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -50.880869-0.000686j
[2025-09-05 01:33:39] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -50.972557+0.001237j
[2025-09-05 01:33:49] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -50.848474-0.003811j
[2025-09-05 01:33:59] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -50.904813-0.002706j
[2025-09-05 01:34:10] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -50.753746-0.002110j
[2025-09-05 01:34:20] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -50.717152+0.001357j
[2025-09-05 01:34:30] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -50.752066+0.005375j
[2025-09-05 01:34:30] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-05 01:34:40] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -50.812951+0.000214j
[2025-09-05 01:34:50] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -50.958663+0.003578j
[2025-09-05 01:35:00] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -50.855673+0.000463j
[2025-09-05 01:35:11] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -50.795027+0.003408j
[2025-09-05 01:35:21] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -50.960724-0.003414j
[2025-09-05 01:35:31] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -50.997251-0.000132j
[2025-09-05 01:35:41] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -50.887709-0.002874j
[2025-09-05 01:35:51] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -50.991613-0.000147j
[2025-09-05 01:36:01] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -51.068003-0.004508j
[2025-09-05 01:36:11] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -51.067082-0.001334j
[2025-09-05 01:36:22] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -50.972925+0.000621j
[2025-09-05 01:36:32] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -50.970665+0.002521j
[2025-09-05 01:36:42] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -51.012782-0.000321j
[2025-09-05 01:36:52] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -50.956898-0.002600j
[2025-09-05 01:37:02] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -50.989960+0.000156j
[2025-09-05 01:37:12] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -51.007981-0.004613j
[2025-09-05 01:37:22] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -50.958798-0.001704j
[2025-09-05 01:37:33] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -51.044198-0.000570j
[2025-09-05 01:37:43] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -51.085103-0.000127j
[2025-09-05 01:37:53] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -51.019424-0.001704j
[2025-09-05 01:38:03] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -51.022307+0.001543j
[2025-09-05 01:38:13] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -50.998200+0.000873j
[2025-09-05 01:38:23] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -50.949504-0.004914j
[2025-09-05 01:38:33] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -51.030569+0.002588j
[2025-09-05 01:38:44] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -51.126709+0.003691j
[2025-09-05 01:38:54] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -50.999706+0.002440j
[2025-09-05 01:39:04] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -51.027317+0.000633j
[2025-09-05 01:39:14] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -50.997411+0.000883j
[2025-09-05 01:39:24] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -51.091947+0.001972j
[2025-09-05 01:39:34] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -51.129454-0.003453j
[2025-09-05 01:39:45] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -51.065363-0.000617j
[2025-09-05 01:39:55] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -51.062742-0.000051j
[2025-09-05 01:40:05] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -51.040232-0.000779j
[2025-09-05 01:40:15] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -51.080798-0.004765j
[2025-09-05 01:40:25] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -51.059190+0.000522j
[2025-09-05 01:40:35] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -51.170775-0.001366j
[2025-09-05 01:40:45] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -51.069473+0.003460j
[2025-09-05 01:40:56] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -51.052172+0.000867j
[2025-09-05 01:41:06] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -51.116027-0.000147j
[2025-09-05 01:41:16] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -51.004953+0.000381j
[2025-09-05 01:41:26] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -50.946952-0.000509j
[2025-09-05 01:41:36] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -51.024117+0.003392j
[2025-09-05 01:41:46] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -50.930324-0.000552j
[2025-09-05 01:41:57] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -51.014400+0.002816j
[2025-09-05 01:42:07] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -50.991835-0.002143j
[2025-09-05 01:42:17] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -50.977077-0.000591j
[2025-09-05 01:42:27] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -50.836282+0.002072j
[2025-09-05 01:42:37] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -50.850821+0.005146j
[2025-09-05 01:42:47] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -50.780934+0.000976j
[2025-09-05 01:42:57] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -50.836456+0.000785j
[2025-09-05 01:43:08] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -50.955033+0.002095j
[2025-09-05 01:43:18] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -50.993982+0.001419j
[2025-09-05 01:43:28] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -50.908519-0.004708j
[2025-09-05 01:43:38] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -50.959101+0.002775j
[2025-09-05 01:43:48] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -50.882663-0.000599j
[2025-09-05 01:43:58] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -51.056031+0.002240j
[2025-09-05 01:44:09] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -50.984029+0.000167j
[2025-09-05 01:44:19] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -50.955837+0.004633j
[2025-09-05 01:44:29] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -50.881493-0.001430j
[2025-09-05 01:44:39] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -51.076809-0.000410j
[2025-09-05 01:44:49] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -50.976616+0.002182j
[2025-09-05 01:44:59] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -50.950093+0.004821j
[2025-09-05 01:45:09] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -51.021079+0.001927j
[2025-09-05 01:45:20] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -50.950342+0.000613j
[2025-09-05 01:45:30] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -51.029950+0.002698j
[2025-09-05 01:45:40] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -50.958506+0.004527j
[2025-09-05 01:45:50] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -51.082767-0.000233j
[2025-09-05 01:46:00] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -51.057431-0.004878j
[2025-09-05 01:46:10] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -51.051214+0.001178j
[2025-09-05 01:46:21] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -51.083031+0.002346j
[2025-09-05 01:46:31] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -51.101603-0.000261j
[2025-09-05 01:46:41] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -51.106258+0.001864j
[2025-09-05 01:46:51] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -50.989773-0.000111j
[2025-09-05 01:47:01] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -51.020305-0.003618j
[2025-09-05 01:47:11] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -51.058329-0.005000j
[2025-09-05 01:47:21] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -51.042271+0.001594j
[2025-09-05 01:47:32] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -50.965854-0.002856j
[2025-09-05 01:47:42] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -50.932465+0.000326j
[2025-09-05 01:47:52] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -51.008850-0.004865j
[2025-09-05 01:48:02] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -51.050797+0.001629j
[2025-09-05 01:48:12] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -51.057555+0.004468j
[2025-09-05 01:48:22] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -51.026422-0.001557j
[2025-09-05 01:48:33] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -51.122721-0.000232j
[2025-09-05 01:48:43] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -51.090052-0.004037j
[2025-09-05 01:48:53] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -50.948385+0.006001j
[2025-09-05 01:49:03] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -50.992295-0.002457j
[2025-09-05 01:49:13] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -50.937197-0.002398j
[2025-09-05 01:49:23] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -50.897991+0.002971j
[2025-09-05 01:49:33] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -51.060884-0.001418j
[2025-09-05 01:49:44] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -51.112862+0.001173j
[2025-09-05 01:49:54] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -51.081358-0.001517j
[2025-09-05 01:50:04] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -51.257740+0.001491j
[2025-09-05 01:50:14] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -51.142853+0.003516j
[2025-09-05 01:50:24] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -51.146431+0.001257j
[2025-09-05 01:50:34] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -51.026968+0.001549j
[2025-09-05 01:50:44] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -50.915006-0.002968j
[2025-09-05 01:50:55] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -51.007876-0.000423j
[2025-09-05 01:51:05] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -50.995442+0.000577j
[2025-09-05 01:51:15] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -50.894988+0.003784j
[2025-09-05 01:51:25] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -50.842254-0.002071j
[2025-09-05 01:51:35] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -50.994456-0.003058j
[2025-09-05 01:51:45] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -50.944772-0.000945j
[2025-09-05 01:51:56] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -51.035210+0.001943j
[2025-09-05 01:52:06] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -50.980501-0.000999j
[2025-09-05 01:52:16] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -50.997203-0.004489j
[2025-09-05 01:52:16] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-05 01:52:26] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -50.992042+0.001330j
[2025-09-05 01:52:36] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -50.894002+0.002376j
[2025-09-05 01:52:46] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -50.963569+0.000284j
[2025-09-05 01:52:56] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -50.857193-0.000145j
[2025-09-05 01:53:07] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -50.891248-0.000112j
[2025-09-05 01:53:17] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -50.927955-0.001745j
[2025-09-05 01:53:27] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -50.987434-0.001937j
[2025-09-05 01:53:37] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -50.931333+0.002065j
[2025-09-05 01:53:47] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -50.995017+0.001879j
[2025-09-05 01:53:57] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -51.013291+0.002712j
[2025-09-05 01:54:07] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -51.028348+0.005920j
[2025-09-05 01:54:18] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -51.087922-0.003737j
[2025-09-05 01:54:28] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -50.900795+0.002031j
[2025-09-05 01:54:38] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -50.941561+0.008373j
[2025-09-05 01:54:48] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -50.967803+0.004559j
[2025-09-05 01:54:58] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -50.913630+0.002073j
[2025-09-05 01:55:08] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -50.850788+0.000236j
[2025-09-05 01:55:19] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -50.837726-0.001508j
[2025-09-05 01:55:29] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -50.862014-0.001369j
[2025-09-05 01:55:39] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -50.920747+0.007018j
[2025-09-05 01:55:49] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -50.931878-0.004025j
[2025-09-05 01:55:59] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -50.922027+0.000199j
[2025-09-05 01:56:09] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -51.034124+0.004583j
[2025-09-05 01:56:19] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -50.995173+0.003964j
[2025-09-05 01:56:30] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -50.928198-0.000925j
[2025-09-05 01:56:40] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -51.046398+0.001635j
[2025-09-05 01:56:50] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -51.016055+0.002265j
[2025-09-05 01:57:00] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -51.032541+0.003379j
[2025-09-05 01:57:10] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -50.959971+0.001706j
[2025-09-05 01:57:20] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -51.014896-0.001852j
[2025-09-05 01:57:30] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -50.974554-0.001397j
[2025-09-05 01:57:41] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -50.901773-0.000370j
[2025-09-05 01:57:51] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -50.781833-0.000961j
[2025-09-05 01:58:01] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -50.887517+0.001294j
[2025-09-05 01:58:11] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -50.848524-0.001796j
[2025-09-05 01:58:21] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -51.061446-0.003498j
[2025-09-05 01:58:31] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -50.974066+0.001130j
[2025-09-05 01:58:42] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -50.966344+0.002041j
[2025-09-05 01:58:52] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -50.956013-0.003172j
[2025-09-05 01:59:02] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -50.947379-0.000666j
[2025-09-05 01:59:12] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -50.971172+0.001713j
[2025-09-05 01:59:22] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -50.973337-0.001278j
[2025-09-05 01:59:32] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -50.942136-0.005873j
[2025-09-05 01:59:42] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -50.874092+0.000472j
[2025-09-05 01:59:53] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -50.907037+0.003047j
[2025-09-05 02:00:03] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -50.896975-0.002180j
[2025-09-05 02:00:13] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -50.988433+0.001247j
[2025-09-05 02:00:23] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -51.009163+0.001563j
[2025-09-05 02:00:33] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -51.038891-0.000601j
[2025-09-05 02:00:43] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -50.972725-0.002048j
[2025-09-05 02:00:53] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -50.872356-0.000220j
[2025-09-05 02:01:04] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -50.953820-0.002870j
[2025-09-05 02:01:14] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -50.918014+0.001704j
[2025-09-05 02:01:24] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -50.898319+0.000224j
[2025-09-05 02:01:34] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -50.870877+0.000182j
[2025-09-05 02:01:44] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -50.913882+0.000194j
[2025-09-05 02:01:54] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -50.939391+0.000984j
[2025-09-05 02:02:05] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -50.984189+0.004814j
[2025-09-05 02:02:15] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -50.896650+0.000319j
[2025-09-05 02:02:25] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -50.918480+0.000606j
[2025-09-05 02:02:35] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -50.841785-0.000368j
[2025-09-05 02:02:45] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -50.996110+0.001269j
[2025-09-05 02:02:55] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -51.028349+0.000363j
[2025-09-05 02:03:05] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -50.934602-0.000580j
[2025-09-05 02:03:16] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -50.844937+0.000543j
[2025-09-05 02:03:26] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -50.920689-0.001872j
[2025-09-05 02:03:36] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -50.945964-0.000846j
[2025-09-05 02:03:46] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -50.887389-0.002168j
[2025-09-05 02:03:56] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -50.792999+0.000339j
[2025-09-05 02:04:06] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -50.866139-0.001583j
[2025-09-05 02:04:17] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -50.902956-0.000296j
[2025-09-05 02:04:27] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -50.813639+0.002263j
[2025-09-05 02:04:37] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -50.894566+0.000934j
[2025-09-05 02:04:47] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -51.058059-0.000884j
[2025-09-05 02:04:57] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -51.104871+0.000574j
[2025-09-05 02:05:07] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -50.990071-0.001284j
[2025-09-05 02:05:18] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -50.958084-0.002687j
[2025-09-05 02:05:28] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -51.022182+0.002008j
[2025-09-05 02:05:38] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -50.846058+0.002421j
[2025-09-05 02:05:48] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -50.900650-0.003268j
[2025-09-05 02:05:57] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -51.043274+0.001818j
[2025-09-05 02:06:07] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -50.964195+0.003669j
[2025-09-05 02:06:17] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -50.871193-0.001583j
[2025-09-05 02:06:28] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -50.818067+0.002355j
[2025-09-05 02:06:38] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -50.871331-0.000534j
[2025-09-05 02:06:48] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -50.916838+0.003008j
[2025-09-05 02:06:58] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -50.995192+0.000252j
[2025-09-05 02:07:08] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -51.044811-0.003853j
[2025-09-05 02:07:18] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -51.072411-0.002114j
[2025-09-05 02:07:29] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -51.022266+0.001321j
[2025-09-05 02:07:39] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -50.995035-0.000634j
[2025-09-05 02:07:49] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -51.054398+0.006998j
[2025-09-05 02:07:59] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -51.077067-0.000520j
[2025-09-05 02:08:09] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -51.064516+0.003941j
[2025-09-05 02:08:19] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -50.870335-0.000910j
[2025-09-05 02:08:29] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -50.937098+0.000469j
[2025-09-05 02:08:40] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -50.840287-0.003572j
[2025-09-05 02:08:50] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -50.989785-0.000440j
[2025-09-05 02:09:00] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -50.930645+0.002345j
[2025-09-05 02:09:10] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -51.000475-0.000089j
[2025-09-05 02:09:20] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -50.919665-0.001238j
[2025-09-05 02:09:30] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -50.864399-0.001199j
[2025-09-05 02:09:41] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -50.990736+0.000160j
[2025-09-05 02:09:51] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -50.969937-0.000239j
[2025-09-05 02:10:01] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -50.945548+0.000456j
[2025-09-05 02:10:01] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-05 02:10:11] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -51.100613-0.002315j
[2025-09-05 02:10:21] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -50.974096-0.002166j
[2025-09-05 02:10:31] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -50.997545+0.003712j
[2025-09-05 02:10:42] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -50.888524-0.000266j
[2025-09-05 02:10:52] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -50.918310-0.001870j
[2025-09-05 02:11:02] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -50.945546+0.002073j
[2025-09-05 02:11:12] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -50.921039+0.003465j
[2025-09-05 02:11:22] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -50.910101-0.000723j
[2025-09-05 02:11:32] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -50.861172+0.002670j
[2025-09-05 02:11:42] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -50.947015-0.001297j
[2025-09-05 02:11:53] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -50.999547+0.001086j
[2025-09-05 02:12:03] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -50.912017+0.001096j
[2025-09-05 02:12:13] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -51.033083-0.001300j
[2025-09-05 02:12:23] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -51.043666-0.003199j
[2025-09-05 02:12:33] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -50.961073-0.004713j
[2025-09-05 02:12:43] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -50.885186+0.002986j
[2025-09-05 02:12:54] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -50.923237+0.002656j
[2025-09-05 02:13:04] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -50.915855+0.005269j
[2025-09-05 02:13:14] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -50.882826+0.001753j
[2025-09-05 02:13:24] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -50.949861+0.000547j
[2025-09-05 02:13:34] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -50.872721+0.001571j
[2025-09-05 02:13:44] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -50.989056+0.000429j
[2025-09-05 02:13:54] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -50.982780+0.002633j
[2025-09-05 02:14:05] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -50.920352-0.001693j
[2025-09-05 02:14:15] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -50.906910+0.000772j
[2025-09-05 02:14:25] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -50.797523-0.002001j
[2025-09-05 02:14:35] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -50.835169-0.000449j
[2025-09-05 02:14:45] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -50.786141-0.000222j
[2025-09-05 02:14:55] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -50.746810+0.002336j
[2025-09-05 02:15:06] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -50.918860+0.003799j
[2025-09-05 02:15:06] RESTART #2 | Period: 600
[2025-09-05 02:15:16] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -50.839350-0.001373j
[2025-09-05 02:15:26] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -50.807408-0.000425j
[2025-09-05 02:15:36] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -50.847136-0.001399j
[2025-09-05 02:15:46] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -50.998721+0.003530j
[2025-09-05 02:15:56] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -50.858608-0.004467j
[2025-09-05 02:16:06] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -50.935821-0.002572j
[2025-09-05 02:16:17] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -51.016232+0.002895j
[2025-09-05 02:16:27] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -51.030610-0.003178j
[2025-09-05 02:16:37] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -50.995178-0.006710j
[2025-09-05 02:16:47] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -50.939546-0.000326j
[2025-09-05 02:16:57] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -51.000849-0.000118j
[2025-09-05 02:17:07] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -51.001008+0.004963j
[2025-09-05 02:17:18] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -51.012655-0.000718j
[2025-09-05 02:17:28] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -50.899828-0.002274j
[2025-09-05 02:17:38] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -50.919550-0.004239j
[2025-09-05 02:17:48] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -50.873136+0.001562j
[2025-09-05 02:17:58] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -50.802371+0.005849j
[2025-09-05 02:18:08] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -50.901248+0.003162j
[2025-09-05 02:18:19] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -50.778144+0.001848j
[2025-09-05 02:18:29] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -50.956320+0.000468j
[2025-09-05 02:18:39] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -50.786625+0.002294j
[2025-09-05 02:18:49] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -50.790074+0.000022j
[2025-09-05 02:18:59] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -50.887828+0.003107j
[2025-09-05 02:19:09] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -50.939722-0.000116j
[2025-09-05 02:19:19] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -50.946035-0.002315j
[2025-09-05 02:19:30] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -50.985869+0.004763j
[2025-09-05 02:19:40] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -50.966424+0.000859j
[2025-09-05 02:19:50] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -51.000042+0.000395j
[2025-09-05 02:20:00] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -50.943877+0.000216j
[2025-09-05 02:20:10] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -50.843499+0.000414j
[2025-09-05 02:20:20] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -50.929238+0.001906j
[2025-09-05 02:20:31] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -51.004236-0.001470j
[2025-09-05 02:20:41] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -50.958646+0.001553j
[2025-09-05 02:20:51] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -50.950674-0.005275j
[2025-09-05 02:21:01] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -50.970164-0.006340j
[2025-09-05 02:21:11] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -50.989072-0.002869j
[2025-09-05 02:21:21] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -50.922138+0.000965j
[2025-09-05 02:21:31] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -50.913457+0.003468j
[2025-09-05 02:21:42] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -50.883988+0.002551j
[2025-09-05 02:21:52] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -50.924527-0.000448j
[2025-09-05 02:22:02] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -50.781783+0.000625j
[2025-09-05 02:22:12] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -50.812744-0.002105j
[2025-09-05 02:22:22] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -50.805617+0.000217j
[2025-09-05 02:22:32] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -50.888438-0.001687j
[2025-09-05 02:22:43] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -50.957190+0.000509j
[2025-09-05 02:22:53] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -50.948731+0.001962j
[2025-09-05 02:23:03] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -51.085332+0.000321j
[2025-09-05 02:23:13] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -51.029508+0.002514j
[2025-09-05 02:23:23] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -51.010581-0.002894j
[2025-09-05 02:23:33] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -51.089950-0.001932j
[2025-09-05 02:23:43] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -51.001760-0.001255j
[2025-09-05 02:23:54] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -51.007758+0.000724j
[2025-09-05 02:24:04] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -50.987736-0.001527j
[2025-09-05 02:24:14] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -51.107767-0.000736j
[2025-09-05 02:24:24] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -51.022906-0.001046j
[2025-09-05 02:24:34] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -51.043398-0.001061j
[2025-09-05 02:24:44] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -50.975576+0.000212j
[2025-09-05 02:24:54] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -51.038275-0.000371j
[2025-09-05 02:25:05] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -50.989342-0.001979j
[2025-09-05 02:25:15] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -50.918013+0.000668j
[2025-09-05 02:25:25] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -50.931478-0.000609j
[2025-09-05 02:25:34] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -50.965786-0.001688j
[2025-09-05 02:25:44] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -50.933101+0.004607j
[2025-09-05 02:25:54] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -50.895574+0.001579j
[2025-09-05 02:26:05] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -50.904782-0.000742j
[2025-09-05 02:26:15] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -51.011823-0.003525j
[2025-09-05 02:26:25] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -50.959406+0.000512j
[2025-09-05 02:26:35] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -51.022331+0.000105j
[2025-09-05 02:26:45] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -51.011709-0.001386j
[2025-09-05 02:26:55] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -50.926835-0.000973j
[2025-09-05 02:27:05] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -50.979360-0.003927j
[2025-09-05 02:27:16] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -51.064853+0.005075j
[2025-09-05 02:27:26] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -50.908614-0.001507j
[2025-09-05 02:27:36] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -50.949687+0.002220j
[2025-09-05 02:27:46] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -50.880509+0.000218j
[2025-09-05 02:27:46] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-05 02:27:56] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -50.804923-0.003903j
[2025-09-05 02:28:06] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -50.823665-0.000816j
[2025-09-05 02:28:16] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -50.886588-0.001677j
[2025-09-05 02:28:27] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -50.861376-0.001675j
[2025-09-05 02:28:37] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -50.841666+0.000857j
[2025-09-05 02:28:47] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -50.960327-0.001112j
[2025-09-05 02:28:57] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -50.948321-0.003606j
[2025-09-05 02:29:07] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -50.991440-0.001858j
[2025-09-05 02:29:17] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -50.951215+0.000492j
[2025-09-05 02:29:28] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -50.881036+0.003245j
[2025-09-05 02:29:38] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -51.004849+0.001305j
[2025-09-05 02:29:48] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -50.905541-0.001048j
[2025-09-05 02:29:58] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -50.875510+0.003057j
[2025-09-05 02:30:08] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -50.980215+0.005727j
[2025-09-05 02:30:18] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -51.051468+0.003451j
[2025-09-05 02:30:28] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -51.056800-0.000823j
[2025-09-05 02:30:39] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -51.079418-0.002872j
[2025-09-05 02:30:49] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -50.941774+0.001942j
[2025-09-05 02:30:59] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -50.980156-0.001540j
[2025-09-05 02:31:09] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -50.971379-0.000461j
[2025-09-05 02:31:19] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -50.943096+0.002986j
[2025-09-05 02:31:29] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -50.955620-0.001620j
[2025-09-05 02:31:40] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -50.921351+0.001177j
[2025-09-05 02:31:50] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -50.914520-0.000235j
[2025-09-05 02:32:00] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -50.950422+0.004255j
[2025-09-05 02:32:10] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -51.119173+0.001428j
[2025-09-05 02:32:20] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -50.969630-0.000757j
[2025-09-05 02:32:30] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -50.993557+0.003541j
[2025-09-05 02:32:40] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -51.084595-0.001112j
[2025-09-05 02:32:50] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -50.979454-0.005738j
[2025-09-05 02:33:00] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -51.002474-0.003299j
[2025-09-05 02:33:11] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -51.061177+0.002070j
[2025-09-05 02:33:21] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -51.048313-0.001836j
[2025-09-05 02:33:31] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -50.881253-0.001120j
[2025-09-05 02:33:41] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -50.932889-0.003620j
[2025-09-05 02:33:51] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -51.083913+0.002396j
[2025-09-05 02:34:01] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -51.142711-0.005704j
[2025-09-05 02:34:12] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -50.992774+0.002358j
[2025-09-05 02:34:22] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -50.996755+0.002885j
[2025-09-05 02:34:32] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -51.136286-0.002456j
[2025-09-05 02:34:42] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -51.015908-0.002895j
[2025-09-05 02:34:52] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -51.064150+0.001283j
[2025-09-05 02:35:02] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -51.011764+0.001054j
[2025-09-05 02:35:12] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -51.028162+0.001890j
[2025-09-05 02:35:23] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -50.896739+0.000497j
[2025-09-05 02:35:33] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -50.874035+0.003559j
[2025-09-05 02:35:43] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -50.944220+0.004385j
[2025-09-05 02:35:53] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -50.868416+0.001520j
[2025-09-05 02:36:03] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -50.936309+0.000017j
[2025-09-05 02:36:13] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -50.991262+0.000264j
[2025-09-05 02:36:23] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -50.970485+0.003557j
[2025-09-05 02:36:34] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -51.005675-0.000355j
[2025-09-05 02:36:44] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -50.862982+0.001536j
[2025-09-05 02:36:54] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -50.965268-0.000970j
[2025-09-05 02:37:04] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -50.999661+0.006136j
[2025-09-05 02:37:14] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -50.955465+0.004428j
[2025-09-05 02:37:24] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -51.029462-0.000206j
[2025-09-05 02:37:35] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -50.929707+0.000107j
[2025-09-05 02:37:45] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -50.968306-0.001428j
[2025-09-05 02:37:55] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -51.003337+0.000522j
[2025-09-05 02:38:05] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -50.964141-0.002764j
[2025-09-05 02:38:15] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -50.862158-0.003024j
[2025-09-05 02:38:25] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -50.982765-0.000279j
[2025-09-05 02:38:35] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -50.880674+0.002778j
[2025-09-05 02:38:46] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -50.831653-0.000467j
[2025-09-05 02:38:56] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -50.825911-0.003683j
[2025-09-05 02:39:06] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -50.771406+0.000416j
[2025-09-05 02:39:16] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -50.852633-0.001941j
[2025-09-05 02:39:26] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -50.958493-0.001187j
[2025-09-05 02:39:36] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -51.005357-0.002241j
[2025-09-05 02:39:46] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -50.955384+0.000160j
[2025-09-05 02:39:57] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -51.055278-0.000587j
[2025-09-05 02:40:07] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -50.876212-0.000100j
[2025-09-05 02:40:17] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -50.943311-0.000319j
[2025-09-05 02:40:27] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -50.853066+0.003504j
[2025-09-05 02:40:37] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -50.918245+0.003894j
[2025-09-05 02:40:47] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -50.953911+0.000481j
[2025-09-05 02:40:58] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -51.028642+0.001584j
[2025-09-05 02:41:08] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -51.096419-0.000386j
[2025-09-05 02:41:18] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -50.962632-0.002449j
[2025-09-05 02:41:28] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -50.933699-0.000648j
[2025-09-05 02:41:38] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -51.060771+0.000757j
[2025-09-05 02:41:48] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -51.061684-0.001892j
[2025-09-05 02:41:58] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -51.016141+0.001478j
[2025-09-05 02:42:09] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -50.957645-0.002617j
[2025-09-05 02:42:19] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -50.958750+0.002315j
[2025-09-05 02:42:29] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -50.996860-0.002754j
[2025-09-05 02:42:39] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -51.053534-0.001050j
[2025-09-05 02:42:49] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -50.966603-0.002341j
[2025-09-05 02:42:59] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -50.934731+0.001667j
[2025-09-05 02:43:09] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -50.971281-0.002244j
[2025-09-05 02:43:20] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -50.967443-0.002842j
[2025-09-05 02:43:30] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -50.961787-0.002321j
[2025-09-05 02:43:40] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -50.998080-0.000737j
[2025-09-05 02:43:50] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -50.955817+0.003303j
[2025-09-05 02:44:00] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -50.977971+0.001278j
[2025-09-05 02:44:10] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -50.853506-0.002484j
[2025-09-05 02:44:20] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -50.900664-0.003193j
[2025-09-05 02:44:31] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -51.016214+0.001978j
[2025-09-05 02:44:41] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -51.038472+0.000859j
[2025-09-05 02:44:51] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -51.098742-0.000018j
[2025-09-05 02:45:01] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -51.103625-0.000281j
[2025-09-05 02:45:11] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -50.945100-0.001988j
[2025-09-05 02:45:21] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -51.040833+0.003713j
[2025-09-05 02:45:31] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -50.921335-0.000712j
[2025-09-05 02:45:32] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-05 02:45:42] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -51.002529+0.000186j
[2025-09-05 02:45:52] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -51.064841+0.000532j
[2025-09-05 02:46:02] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -50.984919-0.002721j
[2025-09-05 02:46:12] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -51.027311+0.002079j
[2025-09-05 02:46:22] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -51.061121+0.002337j
[2025-09-05 02:46:32] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -50.904818-0.001275j
[2025-09-05 02:46:43] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -50.942701+0.004022j
[2025-09-05 02:46:53] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -51.043308+0.002458j
[2025-09-05 02:47:03] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -50.984806-0.002131j
[2025-09-05 02:47:13] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -50.993190+0.000237j
[2025-09-05 02:47:23] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -50.855129+0.001164j
[2025-09-05 02:47:33] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -50.869762+0.001036j
[2025-09-05 02:47:44] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -50.935579+0.003902j
[2025-09-05 02:47:54] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -51.088165+0.000707j
[2025-09-05 02:48:04] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -50.987680+0.000599j
[2025-09-05 02:48:14] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -51.011386+0.003480j
[2025-09-05 02:48:24] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -50.882819+0.002292j
[2025-09-05 02:48:34] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -50.874351+0.000245j
[2025-09-05 02:48:44] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -50.971420+0.003790j
[2025-09-05 02:48:55] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -50.903856-0.001261j
[2025-09-05 02:49:05] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -50.933053-0.001341j
[2025-09-05 02:49:15] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -50.977232+0.000543j
[2025-09-05 02:49:25] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -50.879241-0.000581j
[2025-09-05 02:49:35] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -50.934525+0.001004j
[2025-09-05 02:49:45] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -51.093822+0.000121j
[2025-09-05 02:49:56] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -50.959277-0.003031j
[2025-09-05 02:50:06] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -50.882964-0.000446j
[2025-09-05 02:50:16] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -50.976400+0.003289j
[2025-09-05 02:50:26] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -50.944787+0.002805j
[2025-09-05 02:50:36] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -51.006902-0.002805j
[2025-09-05 02:50:46] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -50.916237+0.002130j
[2025-09-05 02:50:56] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -50.979632+0.001367j
[2025-09-05 02:51:07] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -50.938244+0.001233j
[2025-09-05 02:51:17] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -50.978960-0.000570j
[2025-09-05 02:51:27] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -50.908328+0.000146j
[2025-09-05 02:51:37] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -50.975249+0.000160j
[2025-09-05 02:51:47] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -51.080376+0.002981j
[2025-09-05 02:51:57] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -50.982262+0.001026j
[2025-09-05 02:52:08] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -50.876399+0.002172j
[2025-09-05 02:52:18] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -50.851373+0.002076j
[2025-09-05 02:52:28] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -50.871856-0.005739j
[2025-09-05 02:52:38] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -50.809216-0.002534j
[2025-09-05 02:52:48] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -50.869720-0.001260j
[2025-09-05 02:52:58] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -50.826689-0.000806j
[2025-09-05 02:53:09] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -50.943823-0.001106j
[2025-09-05 02:53:19] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -50.853683+0.000196j
[2025-09-05 02:53:29] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -50.986527-0.000902j
[2025-09-05 02:53:39] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -50.945962-0.000686j
[2025-09-05 02:53:49] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -50.988352+0.004097j
[2025-09-05 02:53:59] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -51.111136+0.002065j
[2025-09-05 02:54:09] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -51.080786+0.003727j
[2025-09-05 02:54:20] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -51.009804+0.001497j
[2025-09-05 02:54:30] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -50.967644-0.000544j
[2025-09-05 02:54:40] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -50.936626-0.001867j
[2025-09-05 02:54:50] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -50.950954-0.000488j
[2025-09-05 02:55:00] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -50.791292+0.000811j
[2025-09-05 02:55:10] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -50.934934-0.000344j
[2025-09-05 02:55:20] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -50.826907-0.005155j
[2025-09-05 02:55:31] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -50.886278+0.000821j
[2025-09-05 02:55:41] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -50.986201+0.001280j
[2025-09-05 02:55:51] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -50.897249-0.001934j
[2025-09-05 02:56:01] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -50.914308-0.004093j
[2025-09-05 02:56:11] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -50.945139+0.003253j
[2025-09-05 02:56:21] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -50.877256-0.000012j
[2025-09-05 02:56:32] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -50.968347-0.003677j
[2025-09-05 02:56:42] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -50.761354+0.003975j
[2025-09-05 02:56:52] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -50.913860-0.002124j
[2025-09-05 02:57:02] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -50.880298-0.001060j
[2025-09-05 02:57:12] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -50.890996-0.002645j
[2025-09-05 02:57:22] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -50.909353+0.001706j
[2025-09-05 02:57:32] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -50.901783+0.000921j
[2025-09-05 02:57:43] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -50.897380+0.003465j
[2025-09-05 02:57:53] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -50.849546-0.000401j
[2025-09-05 02:58:03] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -50.944100-0.000884j
[2025-09-05 02:58:13] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -50.919284-0.000162j
[2025-09-05 02:58:23] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -50.845951-0.000270j
[2025-09-05 02:58:33] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -50.865790+0.001660j
[2025-09-05 02:58:44] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -50.925084+0.000314j
[2025-09-05 02:58:54] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -51.022741-0.001578j
[2025-09-05 02:59:04] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -51.175522-0.002003j
[2025-09-05 02:59:14] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -51.095324-0.000820j
[2025-09-05 02:59:24] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -50.976709+0.001209j
[2025-09-05 02:59:34] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -51.040190+0.002675j
[2025-09-05 02:59:45] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -51.153779-0.002144j
[2025-09-05 02:59:55] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -51.008286-0.003650j
[2025-09-05 03:00:05] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -50.978253-0.005784j
[2025-09-05 03:00:15] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -51.021811-0.004228j
[2025-09-05 03:00:25] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -50.939374+0.001519j
[2025-09-05 03:00:35] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -51.032180+0.000294j
[2025-09-05 03:00:45] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -50.958146-0.000720j
[2025-09-05 03:00:56] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -50.952725-0.002598j
[2025-09-05 03:01:06] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -50.984834+0.001702j
[2025-09-05 03:01:16] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -50.855421+0.003167j
[2025-09-05 03:01:26] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -50.989394+0.001350j
[2025-09-05 03:01:36] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -51.077256-0.002069j
[2025-09-05 03:01:46] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -51.070983-0.001883j
[2025-09-05 03:01:56] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -51.148772+0.002877j
[2025-09-05 03:02:07] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -51.002002+0.002117j
[2025-09-05 03:02:17] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -51.085881-0.005454j
[2025-09-05 03:02:27] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -50.964900+0.001229j
[2025-09-05 03:02:37] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -50.881476+0.000984j
[2025-09-05 03:02:47] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -50.916495+0.000601j
[2025-09-05 03:02:57] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -50.936481+0.002827j
[2025-09-05 03:03:08] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -50.933864+0.005091j
[2025-09-05 03:03:18] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -50.940245+0.002996j
[2025-09-05 03:03:18] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-05 03:03:28] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -50.998481-0.004543j
[2025-09-05 03:03:38] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -51.082720+0.003845j
[2025-09-05 03:03:48] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -51.150936+0.000277j
[2025-09-05 03:03:58] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -51.078821-0.002741j
[2025-09-05 03:04:08] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -50.933399+0.000856j
[2025-09-05 03:04:19] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -50.873757-0.004043j
[2025-09-05 03:04:29] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -50.796157-0.003341j
[2025-09-05 03:04:39] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -50.817206+0.003167j
[2025-09-05 03:04:49] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -50.852889-0.000135j
[2025-09-05 03:04:59] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -50.951768+0.000210j
[2025-09-05 03:05:09] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -51.013628+0.000851j
[2025-09-05 03:05:20] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -51.026652+0.004645j
[2025-09-05 03:05:30] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -50.817586+0.001794j
[2025-09-05 03:05:40] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -50.864395-0.003403j
[2025-09-05 03:05:50] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -50.968309-0.001606j
[2025-09-05 03:06:00] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -50.891670-0.003076j
[2025-09-05 03:06:10] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -51.042758-0.000581j
[2025-09-05 03:06:21] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -50.940997-0.000029j
[2025-09-05 03:06:31] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -51.019457-0.003165j
[2025-09-05 03:06:41] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -50.941417+0.001652j
[2025-09-05 03:06:51] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -50.924794+0.000292j
[2025-09-05 03:07:01] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -51.033540-0.001735j
[2025-09-05 03:07:11] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -50.968110+0.004604j
[2025-09-05 03:07:21] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -51.026230+0.000986j
[2025-09-05 03:07:32] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -50.869987-0.002705j
[2025-09-05 03:07:42] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -50.975833+0.000246j
[2025-09-05 03:07:52] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -51.143502-0.002365j
[2025-09-05 03:08:02] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -51.103959-0.001156j
[2025-09-05 03:08:12] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -51.019182-0.001530j
[2025-09-05 03:08:22] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -51.000740+0.001653j
[2025-09-05 03:08:32] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -51.021864+0.004191j
[2025-09-05 03:08:43] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -51.032755-0.000712j
[2025-09-05 03:08:53] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -51.085982+0.001180j
[2025-09-05 03:09:03] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -51.138529+0.000548j
[2025-09-05 03:09:13] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -51.058167-0.001811j
[2025-09-05 03:09:23] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -51.063899+0.001614j
[2025-09-05 03:09:33] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -51.020527-0.002408j
[2025-09-05 03:09:44] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -51.120160+0.000237j
[2025-09-05 03:09:54] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -51.035107-0.003485j
[2025-09-05 03:10:04] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -50.946719-0.002101j
[2025-09-05 03:10:14] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -50.967500+0.002716j
[2025-09-05 03:10:24] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -50.971459+0.000433j
[2025-09-05 03:10:34] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -50.822601+0.000886j
[2025-09-05 03:10:44] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -50.878352-0.000481j
[2025-09-05 03:10:55] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -50.980100-0.000782j
[2025-09-05 03:11:05] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -50.871457-0.000749j
[2025-09-05 03:11:15] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -50.910280+0.002211j
[2025-09-05 03:11:25] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -50.928294-0.004335j
[2025-09-05 03:11:35] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -51.016553-0.000651j
[2025-09-05 03:11:45] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -50.852440+0.000813j
[2025-09-05 03:11:56] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -50.828849+0.005986j
[2025-09-05 03:12:06] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -50.897245+0.004656j
[2025-09-05 03:12:16] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -50.953774+0.000782j
[2025-09-05 03:12:26] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -50.880409+0.001648j
[2025-09-05 03:12:36] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -50.953069-0.000943j
[2025-09-05 03:12:46] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -51.047181-0.002272j
[2025-09-05 03:12:56] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -51.051733-0.001061j
[2025-09-05 03:13:07] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -51.008685-0.003733j
[2025-09-05 03:13:17] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -50.969008-0.001919j
[2025-09-05 03:13:27] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -50.891313+0.001512j
[2025-09-05 03:13:37] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -50.885993-0.002052j
[2025-09-05 03:13:47] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -50.984521+0.004098j
[2025-09-05 03:13:57] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -51.006008+0.001291j
[2025-09-05 03:14:08] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -50.971364-0.003681j
[2025-09-05 03:14:18] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -51.021130-0.004119j
[2025-09-05 03:14:28] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -50.934648-0.001505j
[2025-09-05 03:14:38] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -50.837827-0.000091j
[2025-09-05 03:14:48] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -50.923611+0.000588j
[2025-09-05 03:14:58] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -51.104687-0.002097j
[2025-09-05 03:15:08] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -50.983544+0.002192j
[2025-09-05 03:15:19] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -50.994305-0.001243j
[2025-09-05 03:15:29] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -50.999254+0.001640j
[2025-09-05 03:15:39] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -50.923435+0.001936j
[2025-09-05 03:15:49] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -50.902286+0.000327j
[2025-09-05 03:15:59] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -51.027422-0.001592j
[2025-09-05 03:16:09] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -50.907647-0.003983j
[2025-09-05 03:16:20] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -50.936095-0.005041j
[2025-09-05 03:16:30] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -51.019477+0.001185j
[2025-09-05 03:16:40] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -50.995606+0.000453j
[2025-09-05 03:16:50] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -50.966709-0.001671j
[2025-09-05 03:17:00] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -51.000726-0.003784j
[2025-09-05 03:17:10] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -50.903167+0.004843j
[2025-09-05 03:17:20] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -50.837654-0.001770j
[2025-09-05 03:17:31] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -51.062846+0.000113j
[2025-09-05 03:17:41] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -51.061247+0.000258j
[2025-09-05 03:17:51] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -50.980308+0.002183j
[2025-09-05 03:18:01] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -50.891963-0.003003j
[2025-09-05 03:18:11] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -51.050922-0.002388j
[2025-09-05 03:18:21] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -50.974278-0.002457j
[2025-09-05 03:18:31] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -50.964409-0.002757j
[2025-09-05 03:18:42] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -50.912937-0.001405j
[2025-09-05 03:18:52] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -50.925949-0.001517j
[2025-09-05 03:19:02] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -51.042046+0.001794j
[2025-09-05 03:19:12] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -51.015238-0.003760j
[2025-09-05 03:19:22] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -51.024124-0.000901j
[2025-09-05 03:19:32] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -51.223577+0.001468j
[2025-09-05 03:19:42] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -51.207356+0.003377j
[2025-09-05 03:19:53] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -51.052705-0.002998j
[2025-09-05 03:20:03] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -50.994678+0.000702j
[2025-09-05 03:20:13] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -51.000080+0.003392j
[2025-09-05 03:20:23] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -50.824100+0.000939j
[2025-09-05 03:20:33] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -50.916920-0.002052j
[2025-09-05 03:20:43] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -50.916106-0.001627j
[2025-09-05 03:20:53] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -50.934367+0.003557j
[2025-09-05 03:21:04] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -50.932584+0.002089j
[2025-09-05 03:21:04] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-05 03:21:14] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -50.997037+0.002554j
[2025-09-05 03:21:24] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -51.110626+0.000725j
[2025-09-05 03:21:34] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -51.084157+0.002350j
[2025-09-05 03:21:44] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -51.070185+0.002480j
[2025-09-05 03:21:54] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -51.053162-0.000864j
[2025-09-05 03:22:05] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -50.877827-0.000248j
[2025-09-05 03:22:15] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -50.933716-0.001482j
[2025-09-05 03:22:25] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -50.979619-0.000436j
[2025-09-05 03:22:35] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -50.904741+0.003583j
[2025-09-05 03:22:45] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -51.087363-0.001662j
[2025-09-05 03:22:55] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -51.003289-0.002999j
[2025-09-05 03:23:05] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -50.939619+0.002845j
[2025-09-05 03:23:16] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -50.964139+0.001777j
[2025-09-05 03:23:26] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -50.919530-0.001324j
[2025-09-05 03:23:36] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -50.931440-0.000851j
[2025-09-05 03:23:46] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -50.957638+0.001604j
[2025-09-05 03:23:56] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -50.881607-0.002335j
[2025-09-05 03:24:06] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -51.012414+0.001329j
[2025-09-05 03:24:17] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -50.950894-0.000070j
[2025-09-05 03:24:27] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -51.132663-0.002035j
[2025-09-05 03:24:37] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -51.076201+0.001495j
[2025-09-05 03:24:47] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -51.101982+0.001222j
[2025-09-05 03:24:57] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -50.988371-0.004419j
[2025-09-05 03:25:07] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -51.166671+0.005036j
[2025-09-05 03:25:18] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -51.017795+0.002607j
[2025-09-05 03:25:28] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -51.000497+0.002755j
[2025-09-05 03:25:38] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -50.937731+0.000379j
[2025-09-05 03:25:48] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -50.955852-0.000498j
[2025-09-05 03:25:58] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -50.972717+0.003002j
[2025-09-05 03:26:08] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -50.851973+0.000974j
[2025-09-05 03:26:19] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -50.815124+0.001087j
[2025-09-05 03:26:29] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -50.930465-0.003056j
[2025-09-05 03:26:38] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -50.990621-0.002475j
[2025-09-05 03:26:49] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -50.963337+0.003120j
[2025-09-05 03:26:59] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -50.896061+0.004063j
[2025-09-05 03:27:09] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -50.785948+0.001029j
[2025-09-05 03:27:19] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -50.806458-0.000634j
[2025-09-05 03:27:29] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -50.924203+0.002329j
[2025-09-05 03:27:39] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -50.854208+0.001588j
[2025-09-05 03:27:49] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -50.954109-0.002187j
[2025-09-05 03:28:00] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -50.985507-0.001410j
[2025-09-05 03:28:10] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -51.012501-0.002792j
[2025-09-05 03:28:20] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -50.878423-0.001087j
[2025-09-05 03:28:30] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -51.044087+0.002457j
[2025-09-05 03:28:40] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -50.980644+0.000853j
[2025-09-05 03:28:50] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -50.824660-0.000822j
[2025-09-05 03:29:01] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -51.005706-0.001450j
[2025-09-05 03:29:11] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -50.980008-0.007700j
[2025-09-05 03:29:21] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -51.027135-0.000142j
[2025-09-05 03:29:31] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -50.898637+0.000229j
[2025-09-05 03:29:41] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -50.945961-0.000443j
[2025-09-05 03:29:51] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -50.959930-0.000153j
[2025-09-05 03:30:01] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -51.037710+0.000919j
[2025-09-05 03:30:12] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -50.903533+0.003965j
[2025-09-05 03:30:22] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -50.973457+0.004547j
[2025-09-05 03:30:32] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -51.034280-0.000326j
[2025-09-05 03:30:42] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -50.986232-0.000871j
[2025-09-05 03:30:52] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -51.096506-0.002540j
[2025-09-05 03:31:02] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -51.067372+0.003735j
[2025-09-05 03:31:13] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -51.099663-0.003627j
[2025-09-05 03:31:23] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -51.158087-0.002727j
[2025-09-05 03:31:33] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -51.119647+0.000107j
[2025-09-05 03:31:43] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -51.024768+0.000016j
[2025-09-05 03:31:53] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -51.071096-0.001625j
[2025-09-05 03:32:03] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -50.985177-0.000571j
[2025-09-05 03:32:13] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -51.026954-0.003623j
[2025-09-05 03:32:24] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -50.945920+0.004556j
[2025-09-05 03:32:34] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -51.052227+0.001368j
[2025-09-05 03:32:44] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -50.933254-0.000477j
[2025-09-05 03:32:54] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -51.075441-0.001679j
[2025-09-05 03:33:04] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -50.991371+0.001300j
[2025-09-05 03:33:14] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -51.022803-0.000522j
[2025-09-05 03:33:24] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -51.087433-0.001304j
[2025-09-05 03:33:35] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -51.054100+0.001410j
[2025-09-05 03:33:45] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -51.070891+0.002680j
[2025-09-05 03:33:55] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -50.924146-0.002528j
[2025-09-05 03:34:05] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -50.998855+0.000032j
[2025-09-05 03:34:15] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -51.079587-0.001025j
[2025-09-05 03:34:25] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -50.942848+0.000580j
[2025-09-05 03:34:36] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -50.875626-0.003959j
[2025-09-05 03:34:46] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -50.854539+0.002736j
[2025-09-05 03:34:56] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -50.911440-0.002137j
[2025-09-05 03:35:06] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -50.937944+0.001443j
[2025-09-05 03:35:16] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -50.886486+0.000343j
[2025-09-05 03:35:26] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -50.909426-0.000797j
[2025-09-05 03:35:37] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -50.978904+0.001685j
[2025-09-05 03:35:47] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -51.098373-0.003060j
[2025-09-05 03:35:57] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -51.000170+0.000288j
[2025-09-05 03:36:07] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -51.046785-0.000937j
[2025-09-05 03:36:17] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -51.004136+0.002325j
[2025-09-05 03:36:27] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -50.991373-0.001136j
[2025-09-05 03:36:37] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -51.088670-0.000221j
[2025-09-05 03:36:48] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -51.141350+0.001952j
[2025-09-05 03:36:58] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -51.075075+0.001433j
[2025-09-05 03:37:08] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -51.037897+0.000283j
[2025-09-05 03:37:18] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -50.993067+0.002198j
[2025-09-05 03:37:28] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -50.939383-0.001430j
[2025-09-05 03:37:38] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -50.891458-0.002798j
[2025-09-05 03:37:48] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -50.887286+0.001346j
[2025-09-05 03:37:59] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -50.925526+0.000424j
[2025-09-05 03:38:09] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -50.896360-0.001852j
[2025-09-05 03:38:19] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -50.816821+0.001509j
[2025-09-05 03:38:29] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -50.976192+0.002668j
[2025-09-05 03:38:39] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -50.929198+0.001769j
[2025-09-05 03:38:49] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -50.908089+0.002974j
[2025-09-05 03:38:49] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-05 03:39:00] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -50.994006+0.000602j
[2025-09-05 03:39:10] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -50.791548+0.002071j
[2025-09-05 03:39:20] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -50.959556-0.004836j
[2025-09-05 03:39:30] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -50.974903-0.000021j
[2025-09-05 03:39:40] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -50.993253-0.001952j
[2025-09-05 03:39:50] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -50.946494+0.003614j
[2025-09-05 03:40:00] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -51.023994+0.002917j
[2025-09-05 03:40:11] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -51.002635+0.001385j
[2025-09-05 03:40:21] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -50.997424+0.000961j
[2025-09-05 03:40:31] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -51.028425-0.001489j
[2025-09-05 03:40:41] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -51.062400-0.002402j
[2025-09-05 03:40:51] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -51.140128-0.000115j
[2025-09-05 03:41:01] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -51.026377-0.004352j
[2025-09-05 03:41:12] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -50.952357-0.002202j
[2025-09-05 03:41:22] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -50.973037+0.000460j
[2025-09-05 03:41:32] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -50.915180+0.000979j
[2025-09-05 03:41:42] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -51.054374-0.002075j
[2025-09-05 03:41:52] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -51.080363-0.001707j
[2025-09-05 03:42:02] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -51.051306-0.002672j
[2025-09-05 03:42:12] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -50.971557+0.003291j
[2025-09-05 03:42:23] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -50.985920+0.000063j
[2025-09-05 03:42:33] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -50.916535+0.001792j
[2025-09-05 03:42:43] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -50.977223+0.001307j
[2025-09-05 03:42:53] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -50.884398-0.000752j
[2025-09-05 03:43:03] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -50.868613-0.001273j
[2025-09-05 03:43:13] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -50.866886+0.001782j
[2025-09-05 03:43:24] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -50.878368+0.000937j
[2025-09-05 03:43:34] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -51.036038+0.002080j
[2025-09-05 03:43:44] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -51.001433+0.002374j
[2025-09-05 03:43:54] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -50.958882-0.002600j
[2025-09-05 03:44:04] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -51.019451+0.002529j
[2025-09-05 03:44:14] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -51.026591+0.001983j
[2025-09-05 03:44:24] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -50.958173+0.001526j
[2025-09-05 03:44:35] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -50.978734-0.002990j
[2025-09-05 03:44:45] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -50.926407-0.000370j
[2025-09-05 03:44:55] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -51.016005-0.000154j
[2025-09-05 03:45:05] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -50.957791-0.002821j
[2025-09-05 03:45:15] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -50.933315-0.001392j
[2025-09-05 03:45:25] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -50.927721-0.000041j
[2025-09-05 03:45:36] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -50.899093+0.001990j
[2025-09-05 03:45:46] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -50.833503-0.000811j
[2025-09-05 03:45:56] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -50.876430+0.001038j
[2025-09-05 03:46:06] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -50.821002+0.000439j
[2025-09-05 03:46:16] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -50.815173+0.002824j
[2025-09-05 03:46:26] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -51.035040-0.003510j
[2025-09-05 03:46:36] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -51.046456+0.003611j
[2025-09-05 03:46:47] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -50.928766+0.001506j
[2025-09-05 03:46:57] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -50.970875+0.003648j
[2025-09-05 03:47:07] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -50.925932+0.001688j
[2025-09-05 03:47:17] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -51.000791+0.004685j
[2025-09-05 03:47:27] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -51.100143-0.001965j
[2025-09-05 03:47:37] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -50.959353-0.004667j
[2025-09-05 03:47:47] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -50.897427-0.003504j
[2025-09-05 03:47:58] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -51.033320+0.001527j
[2025-09-05 03:48:08] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -50.930654+0.000665j
[2025-09-05 03:48:18] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -51.068705-0.001118j
[2025-09-05 03:48:28] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -51.057033-0.001939j
[2025-09-05 03:48:38] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -51.157617+0.000975j
[2025-09-05 03:48:48] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -50.986855-0.006494j
[2025-09-05 03:48:59] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -50.864018+0.000976j
[2025-09-05 03:49:09] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -51.047237-0.000298j
[2025-09-05 03:49:19] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -51.029578-0.000582j
[2025-09-05 03:49:29] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -50.979880+0.000121j
[2025-09-05 03:49:39] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -51.026022-0.000316j
[2025-09-05 03:49:49] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -51.043744-0.000485j
[2025-09-05 03:49:59] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -51.020782+0.000533j
[2025-09-05 03:50:10] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -50.937720+0.002866j
[2025-09-05 03:50:20] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -50.838700-0.000660j
[2025-09-05 03:50:30] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -50.830378-0.002155j
[2025-09-05 03:50:40] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -50.865563+0.001716j
[2025-09-05 03:50:50] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -50.928553-0.000881j
[2025-09-05 03:51:00] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -51.108767+0.001318j
[2025-09-05 03:51:10] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -50.917441+0.000239j
[2025-09-05 03:51:21] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -51.059631+0.004712j
[2025-09-05 03:51:31] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -51.044390-0.004673j
[2025-09-05 03:51:41] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -50.943315-0.002473j
[2025-09-05 03:51:51] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -50.783332-0.002142j
[2025-09-05 03:52:01] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -50.926646-0.002303j
[2025-09-05 03:52:11] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -50.974353+0.000158j
[2025-09-05 03:52:22] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -50.961623-0.000086j
[2025-09-05 03:52:32] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -50.853919+0.002388j
[2025-09-05 03:52:42] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -50.973572-0.004797j
[2025-09-05 03:52:52] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -51.017007-0.004227j
[2025-09-05 03:53:02] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -51.021543+0.002141j
[2025-09-05 03:53:12] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -51.036754+0.002490j
[2025-09-05 03:53:22] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -50.872975+0.001170j
[2025-09-05 03:53:33] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -50.964784+0.000016j
[2025-09-05 03:53:43] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -51.036586+0.000705j
[2025-09-05 03:53:53] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -50.964737+0.000918j
[2025-09-05 03:54:03] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -50.941271+0.002303j
[2025-09-05 03:54:13] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -50.811416+0.002109j
[2025-09-05 03:54:23] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -50.874320-0.000274j
[2025-09-05 03:54:34] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -50.927603+0.000406j
[2025-09-05 03:54:44] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -50.971626+0.003307j
[2025-09-05 03:54:54] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -50.886939+0.001464j
[2025-09-05 03:55:04] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -51.119882-0.002300j
[2025-09-05 03:55:14] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -50.929240-0.001391j
[2025-09-05 03:55:24] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -50.987736+0.004825j
[2025-09-05 03:55:34] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -51.003527+0.001410j
[2025-09-05 03:55:45] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -51.129880-0.002741j
[2025-09-05 03:55:55] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -51.071329-0.001436j
[2025-09-05 03:55:59] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -50.995469-0.002251j
[2025-09-05 03:56:04] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -51.069611-0.000221j
[2025-09-05 03:56:08] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -51.082990-0.000406j
[2025-09-05 03:56:13] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -50.961333-0.001711j
[2025-09-05 03:56:13] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-05 03:56:13] ✅ Training completed | Restarts: 2
[2025-09-05 03:56:13] ============================================================
[2025-09-05 03:56:13] Training completed | Runtime: 10689.7s
[2025-09-05 03:56:15] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-05 03:56:15] ============================================================
