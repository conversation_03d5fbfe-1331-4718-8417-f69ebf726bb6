[2025-09-05 22:54:35] 使用checkpoint文件: results/L=4/J2=0.05/J1=0.02/training/checkpoints/checkpoint_iter_000525.pkl
[2025-09-05 22:54:51] ✓ 从checkpoint加载参数: 525
[2025-09-05 22:54:51]   - 能量: -50.880509+0.000218j ± 0.082442
[2025-09-05 22:54:51] ================================================================================
[2025-09-05 22:54:51] 加载量子态: L=4, J2=0.05, J1=0.02, checkpoint=checkpoint_iter_000525
[2025-09-05 22:54:51] 使用采样数目: 1048576
[2025-09-05 22:54:51] 设置样本数为: 1048576
[2025-09-05 22:54:51] 开始生成共享样本集...
[2025-09-05 22:59:20] 样本生成完成,耗时: 268.431 秒
[2025-09-05 22:59:20] ================================================================================
[2025-09-05 22:59:20] 开始计算自旋结构因子...
[2025-09-05 22:59:20] 初始化操作符缓存...
[2025-09-05 22:59:20] 预构建所有自旋相关操作符...
[2025-09-05 22:59:20] 开始计算自旋相关函数...
[2025-09-05 22:59:33] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 13.424s
[2025-09-05 22:59:51] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 18.283s
[2025-09-05 23:00:05] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 13.997s
[2025-09-05 23:00:19] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 14.002s
[2025-09-05 23:00:33] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 13.998s
[2025-09-05 23:00:47] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 14.000s
[2025-09-05 23:01:01] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 13.994s
[2025-09-05 23:01:15] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 13.997s
[2025-09-05 23:01:29] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 13.995s
[2025-09-05 23:01:43] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 14.002s
[2025-09-05 23:01:57] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 14.001s
[2025-09-05 23:02:11] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 13.999s
[2025-09-05 23:02:25] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 13.997s
[2025-09-05 23:02:39] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 13.989s
[2025-09-05 23:02:53] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 14.002s
[2025-09-05 23:03:07] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 13.983s
[2025-09-05 23:03:21] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 14.007s
[2025-09-05 23:03:35] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 14.000s
[2025-09-05 23:03:49] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 13.992s
[2025-09-05 23:04:03] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 13.979s
[2025-09-05 23:04:16] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 12.470s
[2025-09-05 23:04:25] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.325s
[2025-09-05 23:04:35] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.310s
[2025-09-05 23:04:44] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.326s
[2025-09-05 23:04:53] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.308s
[2025-09-05 23:05:03] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.327s
[2025-09-05 23:05:12] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.305s
[2025-09-05 23:05:21] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.340s
[2025-09-05 23:05:31] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.320s
[2025-09-05 23:05:40] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.305s
[2025-09-05 23:05:49] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.310s
[2025-09-05 23:05:59] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.332s
[2025-09-05 23:06:08] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.326s
[2025-09-05 23:06:17] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.321s
[2025-09-05 23:06:26] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.310s
[2025-09-05 23:06:36] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.328s
[2025-09-05 23:06:45] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.315s
[2025-09-05 23:06:54] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.314s
[2025-09-05 23:07:04] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.330s
[2025-09-05 23:07:13] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.325s
[2025-09-05 23:07:22] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.304s
[2025-09-05 23:07:32] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.318s
[2025-09-05 23:07:41] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.316s
[2025-09-05 23:07:50] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.324s
[2025-09-05 23:08:00] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.253s
[2025-09-05 23:08:06] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 6.706s
[2025-09-05 23:08:11] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 4.249s
[2025-09-05 23:08:15] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.310s
[2025-09-05 23:08:20] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.854s
[2025-09-05 23:08:24] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.654s
[2025-09-05 23:08:29] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.278s
[2025-09-05 23:08:33] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 4.303s
[2025-09-05 23:08:39] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 5.837s
[2025-09-05 23:08:44] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 5.286s
[2025-09-05 23:08:56] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 11.901s
[2025-09-05 23:09:00] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.254s
[2025-09-05 23:09:05] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.270s
[2025-09-05 23:09:10] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 5.058s
[2025-09-05 23:09:16] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 5.849s
[2025-09-05 23:09:25] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.135s
[2025-09-05 23:09:34] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.650s
[2025-09-05 23:09:39] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 4.270s
[2025-09-05 23:09:52] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 12.952s
[2025-09-05 23:09:56] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 4.258s
[2025-09-05 23:09:56] 自旋相关函数计算完成,总耗时 636.07 秒
[2025-09-05 23:09:56] 计算傅里叶变换...
[2025-09-05 23:09:57] 自旋结构因子计算完成
[2025-09-05 23:09:58] 自旋相关函数平均误差: 0.000678
