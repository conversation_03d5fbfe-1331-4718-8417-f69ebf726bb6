[2025-09-05 21:54:40] 使用checkpoint文件: results/L=4/J2=0.05/J1=0.02/training/checkpoints/checkpoint_iter_000210.pkl
[2025-09-05 21:54:54] ✓ 从checkpoint加载参数: 210
[2025-09-05 21:54:54]   - 能量: -50.752066+0.005375j ± 0.081872
[2025-09-05 21:54:54] ================================================================================
[2025-09-05 21:54:54] 加载量子态: L=4, J2=0.05, J1=0.02, checkpoint=checkpoint_iter_000210
[2025-09-05 21:54:54] 使用采样数目: 1048576
[2025-09-05 21:54:54] 设置样本数为: 1048576
[2025-09-05 21:54:54] 开始生成共享样本集...
[2025-09-05 21:59:22] 样本生成完成,耗时: 267.833 秒
[2025-09-05 21:59:22] ================================================================================
[2025-09-05 21:59:22] 开始计算自旋结构因子...
[2025-09-05 21:59:22] 初始化操作符缓存...
[2025-09-05 21:59:22] 预构建所有自旋相关操作符...
[2025-09-05 21:59:22] 开始计算自旋相关函数...
[2025-09-05 21:59:36] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 13.537s
[2025-09-05 21:59:54] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 18.516s
[2025-09-05 22:00:08] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 14.002s
[2025-09-05 22:00:22] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 14.004s
[2025-09-05 22:00:36] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 13.999s
[2025-09-05 22:00:50] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 14.009s
[2025-09-05 22:01:04] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 13.996s
[2025-09-05 22:01:18] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 13.998s
[2025-09-05 22:01:32] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 13.998s
[2025-09-05 22:01:46] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 14.002s
[2025-09-05 22:02:00] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 14.007s
[2025-09-05 22:02:14] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 13.993s
[2025-09-05 22:02:28] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 14.005s
[2025-09-05 22:02:42] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 13.999s
[2025-09-05 22:02:56] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 13.999s
[2025-09-05 22:03:10] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 14.006s
[2025-09-05 22:03:24] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 14.012s
[2025-09-05 22:03:38] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 13.992s
[2025-09-05 22:03:52] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 14.000s
[2025-09-05 22:04:06] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 13.997s
[2025-09-05 22:04:20] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 14.000s
[2025-09-05 22:04:34] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 13.987s
[2025-09-05 22:04:48] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 14.005s
[2025-09-05 22:05:02] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 13.996s
[2025-09-05 22:05:16] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 14.001s
[2025-09-05 22:05:30] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 14.001s
[2025-09-05 22:05:44] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 14.009s
[2025-09-05 22:05:58] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 13.995s
[2025-09-05 22:06:12] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 14.009s
[2025-09-05 22:06:26] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 14.002s
[2025-09-05 22:06:40] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 14.002s
[2025-09-05 22:06:54] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 13.998s
[2025-09-05 22:07:08] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 13.991s
[2025-09-05 22:07:22] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 13.999s
[2025-09-05 22:07:36] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 13.997s
[2025-09-05 22:07:50] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 13.997s
[2025-09-05 22:08:04] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 14.005s
[2025-09-05 22:08:18] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 13.989s
[2025-09-05 22:08:32] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 14.006s
[2025-09-05 22:08:46] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 13.998s
[2025-09-05 22:09:00] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 14.014s
[2025-09-05 22:09:14] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 13.992s
[2025-09-05 22:09:28] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 14.004s
[2025-09-05 22:09:42] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 13.996s
[2025-09-05 22:09:56] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 14.001s
[2025-09-05 22:10:10] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 14.004s
[2025-09-05 22:10:24] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 13.999s
[2025-09-05 22:10:38] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 14.006s
[2025-09-05 22:10:52] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 13.998s
[2025-09-05 22:11:06] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 14.001s
[2025-09-05 22:11:20] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 14.004s
[2025-09-05 22:11:34] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 13.964s
[2025-09-05 22:11:48] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 14.002s
[2025-09-05 22:12:02] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 13.998s
[2025-09-05 22:12:16] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 14.002s
[2025-09-05 22:12:30] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 13.984s
[2025-09-05 22:12:44] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 13.995s
[2025-09-05 22:12:58] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 14.008s
[2025-09-05 22:13:12] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 14.000s
[2025-09-05 22:13:26] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 14.000s
[2025-09-05 22:13:40] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 14.007s
[2025-09-05 22:13:54] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 14.003s
[2025-09-05 22:14:08] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 13.994s
[2025-09-05 22:14:22] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 14.000s
[2025-09-05 22:14:22] 自旋相关函数计算完成,总耗时 900.35 秒
[2025-09-05 22:14:24] 计算傅里叶变换...
[2025-09-05 22:14:27] 自旋结构因子计算完成
[2025-09-05 22:14:28] 自旋相关函数平均误差: 0.000685
