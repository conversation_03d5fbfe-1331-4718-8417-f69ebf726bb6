[2025-09-05 23:50:01] 使用checkpoint文件: results/L=4/J2=0.05/J1=0.02/training/checkpoints/checkpoint_iter_000840.pkl
[2025-09-05 23:50:18] ✓ 从checkpoint加载参数: 840
[2025-09-05 23:50:18]   - 能量: -50.932584+0.002089j ± 0.083059
[2025-09-05 23:50:18] ================================================================================
[2025-09-05 23:50:18] 加载量子态: L=4, J2=0.05, J1=0.02, checkpoint=checkpoint_iter_000840
[2025-09-05 23:50:18] 使用采样数目: 1048576
[2025-09-05 23:50:18] 设置样本数为: 1048576
[2025-09-05 23:50:18] 开始生成共享样本集...
[2025-09-05 23:54:43] 样本生成完成,耗时: 264.931 秒
[2025-09-05 23:54:43] ================================================================================
[2025-09-05 23:54:43] 开始计算自旋结构因子...
[2025-09-05 23:54:43] 初始化操作符缓存...
[2025-09-05 23:54:43] 预构建所有自旋相关操作符...
[2025-09-05 23:54:43] 开始计算自旋相关函数...
[2025-09-05 23:54:56] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 13.443s
[2025-09-05 23:55:15] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 18.565s
[2025-09-05 23:55:29] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 14.016s
[2025-09-05 23:55:43] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 14.059s
[2025-09-05 23:55:57] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 14.031s
[2025-09-05 23:56:11] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 14.034s
[2025-09-05 23:56:25] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 14.024s
[2025-09-05 23:56:39] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 14.039s
[2025-09-05 23:56:53] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 14.031s
[2025-09-05 23:57:07] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 14.036s
[2025-09-05 23:57:21] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 14.027s
[2025-09-05 23:57:35] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 14.069s
[2025-09-05 23:57:49] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 14.023s
[2025-09-05 23:58:03] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 14.035s
[2025-09-05 23:58:17] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 14.063s
[2025-09-05 23:58:31] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 14.020s
[2025-09-05 23:58:45] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 14.039s
[2025-09-05 23:58:59] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 14.062s
[2025-09-05 23:59:13] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 14.027s
[2025-09-05 23:59:27] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 14.044s
[2025-09-05 23:59:42] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 14.061s
[2025-09-05 23:59:56] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 14.037s
[2025-09-06 00:00:10] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 14.024s
[2025-09-06 00:00:24] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 14.038s
[2025-09-06 00:00:38] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 14.014s
[2025-09-06 00:00:52] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 14.057s
[2025-09-06 00:01:06] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 14.029s
[2025-09-06 00:01:20] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 14.034s
[2025-09-06 00:01:34] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 14.018s
[2025-09-06 00:01:48] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 14.028s
[2025-09-06 00:02:02] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 13.960s
[2025-09-06 00:02:16] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 13.994s
[2025-09-06 00:02:30] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 14.054s
[2025-09-06 00:02:44] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 14.050s
[2025-09-06 00:02:58] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 14.024s
[2025-09-06 00:03:12] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 14.063s
[2025-09-06 00:03:26] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 14.026s
[2025-09-06 00:03:40] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 14.052s
[2025-09-06 00:03:54] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 14.030s
[2025-09-06 00:04:08] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 14.038s
[2025-09-06 00:04:22] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 14.026s
[2025-09-06 00:04:36] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 14.022s
[2025-09-06 00:04:50] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 14.023s
[2025-09-06 00:05:04] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 14.063s
[2025-09-06 00:05:18] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 14.019s
[2025-09-06 00:05:32] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 14.024s
[2025-09-06 00:05:46] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 14.035s
[2025-09-06 00:06:00] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 14.027s
[2025-09-06 00:06:15] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 14.034s
[2025-09-06 00:06:29] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 14.062s
[2025-09-06 00:06:43] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 14.025s
[2025-09-06 00:06:57] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 14.036s
[2025-09-06 00:07:11] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 14.031s
[2025-09-06 00:07:25] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 14.038s
[2025-09-06 00:07:39] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 14.031s
[2025-09-06 00:07:53] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 14.033s
[2025-09-06 00:08:07] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 14.033s
[2025-09-06 00:08:21] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 14.031s
[2025-09-06 00:08:35] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 14.029s
[2025-09-06 00:08:49] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 14.068s
[2025-09-06 00:09:03] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 14.019s
[2025-09-06 00:09:17] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 14.027s
[2025-09-06 00:09:31] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 14.055s
[2025-09-06 00:09:45] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 14.015s
[2025-09-06 00:09:45] 自旋相关函数计算完成,总耗时 902.44 秒
[2025-09-06 00:09:47] 计算傅里叶变换...
[2025-09-06 00:09:50] 自旋结构因子计算完成
[2025-09-06 00:09:51] 自旋相关函数平均误差: 0.000681
