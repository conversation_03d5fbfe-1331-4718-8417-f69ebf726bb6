[2025-09-05 21:34:44] 使用checkpoint文件: results/L=4/J2=0.05/J1=0.02/training/checkpoints/checkpoint_iter_000105.pkl
[2025-09-05 21:34:59] ✓ 从checkpoint加载参数: 105
[2025-09-05 21:34:59]   - 能量: -51.035330+0.002950j ± 0.084143
[2025-09-05 21:34:59] ================================================================================
[2025-09-05 21:34:59] 加载量子态: L=4, J2=0.05, J1=0.02, checkpoint=checkpoint_iter_000105
[2025-09-05 21:34:59] 使用采样数目: 1048576
[2025-09-05 21:34:59] 设置样本数为: 1048576
[2025-09-05 21:34:59] 开始生成共享样本集...
[2025-09-05 21:39:25] 样本生成完成,耗时: 266.061 秒
[2025-09-05 21:39:25] ================================================================================
[2025-09-05 21:39:25] 开始计算自旋结构因子...
[2025-09-05 21:39:25] 初始化操作符缓存...
[2025-09-05 21:39:25] 预构建所有自旋相关操作符...
[2025-09-05 21:39:25] 开始计算自旋相关函数...
[2025-09-05 21:39:39] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 13.544s
[2025-09-05 21:39:57] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 18.504s
[2025-09-05 21:40:11] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 14.001s
[2025-09-05 21:40:26] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 14.011s
[2025-09-05 21:40:40] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 14.009s
[2025-09-05 21:40:54] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 14.016s
[2025-09-05 21:41:08] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 13.992s
[2025-09-05 21:41:22] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 14.014s
[2025-09-05 21:41:36] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 14.013s
[2025-09-05 21:41:50] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 14.022s
[2025-09-05 21:42:04] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 14.008s
[2025-09-05 21:42:18] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 14.017s
[2025-09-05 21:42:32] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 14.006s
[2025-09-05 21:42:46] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 14.016s
[2025-09-05 21:43:00] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 14.016s
[2025-09-05 21:43:14] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 14.005s
[2025-09-05 21:43:28] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 14.015s
[2025-09-05 21:43:42] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 14.008s
[2025-09-05 21:43:56] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 14.003s
[2025-09-05 21:44:10] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 14.019s
[2025-09-05 21:44:24] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 14.020s
[2025-09-05 21:44:38] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 14.014s
[2025-09-05 21:44:52] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 14.005s
[2025-09-05 21:45:06] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 14.014s
[2025-09-05 21:45:20] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 14.004s
[2025-09-05 21:45:34] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 14.012s
[2025-09-05 21:45:48] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 13.993s
[2025-09-05 21:46:02] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 14.015s
[2025-09-05 21:46:16] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 14.001s
[2025-09-05 21:46:30] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 13.995s
[2025-09-05 21:46:44] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 14.013s
[2025-09-05 21:46:58] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 14.020s
[2025-09-05 21:47:12] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 14.017s
[2025-09-05 21:47:26] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 14.021s
[2025-09-05 21:47:40] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 14.003s
[2025-09-05 21:47:54] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 14.016s
[2025-09-05 21:48:08] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 14.009s
[2025-09-05 21:48:22] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 14.014s
[2025-09-05 21:48:36] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 14.002s
[2025-09-05 21:48:50] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 14.015s
[2025-09-05 21:49:04] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 14.004s
[2025-09-05 21:49:18] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 14.004s
[2025-09-05 21:49:32] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 14.003s
[2025-09-05 21:49:46] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 14.018s
[2025-09-05 21:50:00] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 14.001s
[2025-09-05 21:50:14] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 14.013s
[2025-09-05 21:50:28] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 14.011s
[2025-09-05 21:50:42] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 14.000s
[2025-09-05 21:50:56] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 14.005s
[2025-09-05 21:51:10] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 14.021s
[2025-09-05 21:51:24] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 14.004s
[2025-09-05 21:51:38] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 14.012s
[2025-09-05 21:51:52] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 14.007s
[2025-09-05 21:52:06] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 14.011s
[2025-09-05 21:52:20] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 14.005s
[2025-09-05 21:52:34] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 14.019s
[2025-09-05 21:52:48] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 14.001s
[2025-09-05 21:53:02] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 14.011s
[2025-09-05 21:53:16] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 13.998s
[2025-09-05 21:53:30] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 14.016s
[2025-09-05 21:53:44] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 14.001s
[2025-09-05 21:53:58] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 13.998s
[2025-09-05 21:54:12] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 14.022s
[2025-09-05 21:54:26] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 13.998s
[2025-09-05 21:54:26] 自旋相关函数计算完成,总耗时 900.93 秒
[2025-09-05 21:54:28] 计算傅里叶变换...
[2025-09-05 21:54:31] 自旋结构因子计算完成
[2025-09-05 21:54:32] 自旋相关函数平均误差: 0.000682
