[2025-09-07 01:09:51] 使用checkpoint文件: results/L=4/J2=0.05/J1=0.10/training/checkpoints/checkpoint_iter_000105.pkl
[2025-09-07 01:10:05] ✓ 从checkpoint加载参数: 105
[2025-09-07 01:10:05]   - 能量: -54.166965+0.001186j ± 0.079012
[2025-09-07 01:10:05] ================================================================================
[2025-09-07 01:10:05] 加载量子态: L=4, J2=0.05, J1=0.10, checkpoint=checkpoint_iter_000105
[2025-09-07 01:10:05] 使用采样数目: 1048576
[2025-09-07 01:10:05] 设置样本数为: 1048576
[2025-09-07 01:10:05] 开始生成共享样本集...
[2025-09-07 01:13:04] 样本生成完成,耗时: 178.693 秒
[2025-09-07 01:13:04] ================================================================================
[2025-09-07 01:13:04] 开始计算自旋结构因子...
[2025-09-07 01:13:04] 初始化操作符缓存...
[2025-09-07 01:13:04] 预构建所有自旋相关操作符...
[2025-09-07 01:13:04] 开始计算自旋相关函数...
[2025-09-07 01:13:15] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 10.677s
[2025-09-07 01:13:29] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 13.976s
[2025-09-07 01:13:38] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.387s
[2025-09-07 01:13:47] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.388s
[2025-09-07 01:13:57] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.373s
[2025-09-07 01:14:06] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.374s
[2025-09-07 01:14:16] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.385s
[2025-09-07 01:14:25] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.374s
[2025-09-07 01:14:34] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.382s
[2025-09-07 01:14:44] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.370s
[2025-09-07 01:14:53] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.384s
[2025-09-07 01:15:03] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.361s
[2025-09-07 01:15:12] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.390s
[2025-09-07 01:15:21] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.362s
[2025-09-07 01:15:31] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.358s
[2025-09-07 01:15:40] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.377s
[2025-09-07 01:15:49] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.359s
[2025-09-07 01:15:59] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.356s
[2025-09-07 01:16:08] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.364s
[2025-09-07 01:16:17] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.360s
[2025-09-07 01:16:27] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.358s
[2025-09-07 01:16:36] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.366s
[2025-09-07 01:16:46] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.382s
[2025-09-07 01:16:55] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.386s
[2025-09-07 01:17:04] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.375s
[2025-09-07 01:17:14] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.385s
[2025-09-07 01:17:23] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.374s
[2025-09-07 01:17:32] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.364s
[2025-09-07 01:17:42] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.366s
[2025-09-07 01:17:51] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.377s
[2025-09-07 01:18:01] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.379s
[2025-09-07 01:18:10] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.362s
[2025-09-07 01:18:19] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.354s
[2025-09-07 01:18:29] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.374s
[2025-09-07 01:18:38] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.392s
[2025-09-07 01:18:47] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.353s
[2025-09-07 01:18:57] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.367s
[2025-09-07 01:19:06] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.374s
[2025-09-07 01:19:16] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.372s
[2025-09-07 01:19:25] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.371s
[2025-09-07 01:19:34] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.380s
[2025-09-07 01:19:44] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.373s
[2025-09-07 01:19:53] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.370s
[2025-09-07 01:20:02] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.377s
[2025-09-07 01:20:12] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.370s
[2025-09-07 01:20:21] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.378s
[2025-09-07 01:20:31] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.357s
[2025-09-07 01:20:40] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.382s
[2025-09-07 01:20:49] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.361s
[2025-09-07 01:20:59] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.372s
[2025-09-07 01:21:08] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.371s
[2025-09-07 01:21:17] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.352s
[2025-09-07 01:21:27] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.371s
[2025-09-07 01:21:36] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.361s
[2025-09-07 01:21:46] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.376s
[2025-09-07 01:21:55] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.369s
[2025-09-07 01:22:04] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.392s
[2025-09-07 01:22:14] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.376s
[2025-09-07 01:22:23] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.386s
[2025-09-07 01:22:32] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.357s
[2025-09-07 01:22:42] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.371s
[2025-09-07 01:22:51] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.380s
[2025-09-07 01:23:01] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.362s
[2025-09-07 01:23:10] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.366s
[2025-09-07 01:23:10] 自旋相关函数计算完成,总耗时 605.91 秒
[2025-09-07 01:23:11] 计算傅里叶变换...
[2025-09-07 01:23:13] 自旋结构因子计算完成
[2025-09-07 01:23:14] 自旋相关函数平均误差: 0.000655
