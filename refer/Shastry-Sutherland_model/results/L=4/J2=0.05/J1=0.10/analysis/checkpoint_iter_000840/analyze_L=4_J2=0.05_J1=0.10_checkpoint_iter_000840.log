[2025-09-07 02:44:01] 使用checkpoint文件: results/L=4/J2=0.05/J1=0.10/training/checkpoints/checkpoint_iter_000840.pkl
[2025-09-07 02:44:15] ✓ 从checkpoint加载参数: 840
[2025-09-07 02:44:15]   - 能量: -53.962789-0.000275j ± 0.081881
[2025-09-07 02:44:15] ================================================================================
[2025-09-07 02:44:15] 加载量子态: L=4, J2=0.05, J1=0.10, checkpoint=checkpoint_iter_000840
[2025-09-07 02:44:15] 使用采样数目: 1048576
[2025-09-07 02:44:15] 设置样本数为: 1048576
[2025-09-07 02:44:15] 开始生成共享样本集...
[2025-09-07 02:47:15] 样本生成完成,耗时: 179.408 秒
[2025-09-07 02:47:15] ================================================================================
[2025-09-07 02:47:15] 开始计算自旋结构因子...
[2025-09-07 02:47:15] 初始化操作符缓存...
[2025-09-07 02:47:15] 预构建所有自旋相关操作符...
[2025-09-07 02:47:15] 开始计算自旋相关函数...
[2025-09-07 02:47:26] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 10.773s
[2025-09-07 02:47:40] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 13.965s
[2025-09-07 02:47:49] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.327s
[2025-09-07 02:47:58] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.339s
[2025-09-07 02:48:08] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.322s
[2025-09-07 02:48:17] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.323s
[2025-09-07 02:48:26] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.333s
[2025-09-07 02:48:36] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.319s
[2025-09-07 02:48:45] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.328s
[2025-09-07 02:48:54] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.312s
[2025-09-07 02:49:04] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.334s
[2025-09-07 02:49:13] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.311s
[2025-09-07 02:49:22] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.326s
[2025-09-07 02:49:32] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.732s
[2025-09-07 02:49:41] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.313s
[2025-09-07 02:49:51] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.332s
[2025-09-07 02:50:00] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.314s
[2025-09-07 02:50:09] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.317s
[2025-09-07 02:50:19] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.336s
[2025-09-07 02:50:28] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.326s
[2025-09-07 02:50:37] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.309s
[2025-09-07 02:50:47] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.322s
[2025-09-07 02:50:56] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.334s
[2025-09-07 02:51:05] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.313s
[2025-09-07 02:51:15] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.330s
[2025-09-07 02:51:24] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.312s
[2025-09-07 02:51:33] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.329s
[2025-09-07 02:51:42] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.313s
[2025-09-07 02:51:52] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.340s
[2025-09-07 02:52:01] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.334s
[2025-09-07 02:52:10] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.343s
[2025-09-07 02:52:20] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.314s
[2025-09-07 02:52:29] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.311s
[2025-09-07 02:52:38] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.321s
[2025-09-07 02:52:48] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.333s
[2025-09-07 02:52:57] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.313s
[2025-09-07 02:53:06] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.325s
[2025-09-07 02:53:16] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.322s
[2025-09-07 02:53:25] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.211s
[2025-09-07 02:53:34] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.328s
[2025-09-07 02:53:44] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.331s
[2025-09-07 02:53:53] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.340s
[2025-09-07 02:54:02] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.330s
[2025-09-07 02:54:12] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.321s
[2025-09-07 02:54:21] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.329s
[2025-09-07 02:54:30] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.326s
[2025-09-07 02:54:40] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.313s
[2025-09-07 02:54:49] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.327s
[2025-09-07 02:54:58] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.323s
[2025-09-07 02:55:08] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.315s
[2025-09-07 02:55:17] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.334s
[2025-09-07 02:55:26] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.312s
[2025-09-07 02:55:36] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.334s
[2025-09-07 02:55:45] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.327s
[2025-09-07 02:55:54] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.325s
[2025-09-07 02:56:04] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.321s
[2025-09-07 02:56:13] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.327s
[2025-09-07 02:56:22] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.333s
[2025-09-07 02:56:32] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.324s
[2025-09-07 02:56:41] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.322s
[2025-09-07 02:56:50] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.330s
[2025-09-07 02:57:00] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.337s
[2025-09-07 02:57:09] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.315s
[2025-09-07 02:57:18] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.339s
[2025-09-07 02:57:18] 自旋相关函数计算完成,总耗时 603.44 秒
[2025-09-07 02:57:19] 计算傅里叶变换...
[2025-09-07 02:57:21] 自旋结构因子计算完成
[2025-09-07 02:57:22] 自旋相关函数平均误差: 0.000657
