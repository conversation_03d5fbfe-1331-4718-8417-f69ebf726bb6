[2025-09-07 02:30:32] 使用checkpoint文件: results/L=4/J2=0.05/J1=0.10/training/checkpoints/checkpoint_iter_000735.pkl
[2025-09-07 02:30:46] ✓ 从checkpoint加载参数: 735
[2025-09-07 02:30:46]   - 能量: -53.990968+0.002025j ± 0.081063
[2025-09-07 02:30:46] ================================================================================
[2025-09-07 02:30:46] 加载量子态: L=4, J2=0.05, J1=0.10, checkpoint=checkpoint_iter_000735
[2025-09-07 02:30:46] 使用采样数目: 1048576
[2025-09-07 02:30:46] 设置样本数为: 1048576
[2025-09-07 02:30:46] 开始生成共享样本集...
[2025-09-07 02:33:45] 样本生成完成,耗时: 178.569 秒
[2025-09-07 02:33:45] ================================================================================
[2025-09-07 02:33:45] 开始计算自旋结构因子...
[2025-09-07 02:33:45] 初始化操作符缓存...
[2025-09-07 02:33:45] 预构建所有自旋相关操作符...
[2025-09-07 02:33:45] 开始计算自旋相关函数...
[2025-09-07 02:33:55] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 10.678s
[2025-09-07 02:34:09] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 14.011s
[2025-09-07 02:34:19] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.374s
[2025-09-07 02:34:28] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.362s
[2025-09-07 02:34:37] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.373s
[2025-09-07 02:34:47] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.352s
[2025-09-07 02:34:56] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.377s
[2025-09-07 02:35:06] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.344s
[2025-09-07 02:35:15] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.372s
[2025-09-07 02:35:24] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.347s
[2025-09-07 02:35:34] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.376s
[2025-09-07 02:35:43] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.346s
[2025-09-07 02:35:52] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.374s
[2025-09-07 02:36:02] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.355s
[2025-09-07 02:36:11] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.348s
[2025-09-07 02:36:21] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.381s
[2025-09-07 02:36:30] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.351s
[2025-09-07 02:36:39] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.360s
[2025-09-07 02:36:49] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.378s
[2025-09-07 02:36:58] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.347s
[2025-09-07 02:37:07] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.349s
[2025-09-07 02:37:17] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.349s
[2025-09-07 02:37:26] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.376s
[2025-09-07 02:37:35] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.347s
[2025-09-07 02:37:45] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.377s
[2025-09-07 02:37:54] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.355s
[2025-09-07 02:38:04] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.379s
[2025-09-07 02:38:13] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.347s
[2025-09-07 02:38:22] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.387s
[2025-09-07 02:38:32] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.377s
[2025-09-07 02:38:41] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.375s
[2025-09-07 02:38:50] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.351s
[2025-09-07 02:39:00] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.347s
[2025-09-07 02:39:09] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.367s
[2025-09-07 02:39:18] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.376s
[2025-09-07 02:39:28] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.350s
[2025-09-07 02:39:37] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.378s
[2025-09-07 02:39:47] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.358s
[2025-09-07 02:39:56] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.374s
[2025-09-07 02:40:05] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.378s
[2025-09-07 02:40:15] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.374s
[2025-09-07 02:40:24] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.376s
[2025-09-07 02:40:33] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.380s
[2025-09-07 02:40:43] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.362s
[2025-09-07 02:40:52] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.375s
[2025-09-07 02:41:02] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.374s
[2025-09-07 02:41:11] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.339s
[2025-09-07 02:41:20] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.375s
[2025-09-07 02:41:30] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.350s
[2025-09-07 02:41:39] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.349s
[2025-09-07 02:41:48] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.388s
[2025-09-07 02:41:58] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.343s
[2025-09-07 02:42:07] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.374s
[2025-09-07 02:42:16] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.345s
[2025-09-07 02:42:26] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.372s
[2025-09-07 02:42:35] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.360s
[2025-09-07 02:42:45] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.375s
[2025-09-07 02:42:54] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.373s
[2025-09-07 02:43:03] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.372s
[2025-09-07 02:43:13] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.361s
[2025-09-07 02:43:22] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.377s
[2025-09-07 02:43:31] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.390s
[2025-09-07 02:43:41] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.348s
[2025-09-07 02:43:50] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.377s
[2025-09-07 02:43:50] 自旋相关函数计算完成,总耗时 605.54 秒
[2025-09-07 02:43:51] 计算傅里叶变换...
[2025-09-07 02:43:53] 自旋结构因子计算完成
[2025-09-07 02:43:54] 自旋相关函数平均误差: 0.000667
