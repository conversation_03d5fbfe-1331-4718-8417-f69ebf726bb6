[2025-09-07 03:10:58] 使用checkpoint文件: results/L=4/J2=0.05/J1=0.10/training/checkpoints/checkpoint_iter_001050.pkl
[2025-09-07 03:11:14] ✓ 从checkpoint加载参数: 1050
[2025-09-07 03:11:14]   - 能量: -54.174736-0.001882j ± 0.079986
[2025-09-07 03:11:14] ================================================================================
[2025-09-07 03:11:14] 加载量子态: L=4, J2=0.05, J1=0.10, checkpoint=checkpoint_iter_001050
[2025-09-07 03:11:14] 使用采样数目: 1048576
[2025-09-07 03:11:14] 设置样本数为: 1048576
[2025-09-07 03:11:14] 开始生成共享样本集...
[2025-09-07 03:14:13] 样本生成完成,耗时: 179.785 秒
[2025-09-07 03:14:13] ================================================================================
[2025-09-07 03:14:13] 开始计算自旋结构因子...
[2025-09-07 03:14:13] 初始化操作符缓存...
[2025-09-07 03:14:13] 预构建所有自旋相关操作符...
[2025-09-07 03:14:13] 开始计算自旋相关函数...
[2025-09-07 03:14:24] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 10.693s
[2025-09-07 03:14:38] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 13.950s
[2025-09-07 03:14:47] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.322s
[2025-09-07 03:14:57] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.321s
[2025-09-07 03:15:06] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.333s
[2025-09-07 03:15:15] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.312s
[2025-09-07 03:15:25] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.328s
[2025-09-07 03:15:34] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.317s
[2025-09-07 03:15:43] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.331s
[2025-09-07 03:15:53] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.312s
[2025-09-07 03:16:02] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.326s
[2025-09-07 03:16:11] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.307s
[2025-09-07 03:16:21] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.325s
[2025-09-07 03:16:30] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.320s
[2025-09-07 03:16:39] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.306s
[2025-09-07 03:16:49] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.340s
[2025-09-07 03:16:58] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.312s
[2025-09-07 03:17:07] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 8.986s
[2025-09-07 03:17:16] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.332s
[2025-09-07 03:17:26] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.313s
[2025-09-07 03:17:35] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.320s
[2025-09-07 03:17:44] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.316s
[2025-09-07 03:17:54] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.325s
[2025-09-07 03:18:03] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.315s
[2025-09-07 03:18:12] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.329s
[2025-09-07 03:18:22] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.326s
[2025-09-07 03:18:31] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.331s
[2025-09-07 03:18:40] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.316s
[2025-09-07 03:18:50] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.320s
[2025-09-07 03:18:59] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.326s
[2025-09-07 03:19:08] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.331s
[2025-09-07 03:19:18] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.319s
[2025-09-07 03:19:27] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.307s
[2025-09-07 03:19:36] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.317s
[2025-09-07 03:19:45] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.331s
[2025-09-07 03:19:55] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.314s
[2025-09-07 03:20:04] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.329s
[2025-09-07 03:20:13] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.331s
[2025-09-07 03:20:23] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.328s
[2025-09-07 03:20:32] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.320s
[2025-09-07 03:20:41] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.327s
[2025-09-07 03:20:51] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.324s
[2025-09-07 03:21:00] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.319s
[2025-09-07 03:21:09] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.319s
[2025-09-07 03:21:19] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.328s
[2025-09-07 03:21:28] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.323s
[2025-09-07 03:21:37] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.312s
[2025-09-07 03:21:47] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.330s
[2025-09-07 03:21:56] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.333s
[2025-09-07 03:22:05] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.318s
[2025-09-07 03:22:15] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.325s
[2025-09-07 03:22:24] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.310s
[2025-09-07 03:22:33] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.329s
[2025-09-07 03:22:43] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.318s
[2025-09-07 03:22:52] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.327s
[2025-09-07 03:23:01] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.317s
[2025-09-07 03:23:11] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.320s
[2025-09-07 03:23:20] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.327s
[2025-09-07 03:23:29] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.329s
[2025-09-07 03:23:39] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.331s
[2025-09-07 03:23:48] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.325s
[2025-09-07 03:23:57] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.332s
[2025-09-07 03:24:07] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.301s
[2025-09-07 03:24:16] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.331s
[2025-09-07 03:24:16] 自旋相关函数计算完成,总耗时 602.53 秒
[2025-09-07 03:24:17] 计算傅里叶变换...
[2025-09-07 03:24:19] 自旋结构因子计算完成
[2025-09-07 03:24:20] 自旋相关函数平均误差: 0.000649
