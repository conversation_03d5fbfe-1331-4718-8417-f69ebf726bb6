[2025-09-05 06:54:53] ✓ 从checkpoint恢复: results/L=4/J2=0.05/J1=0.09/training/checkpoints/final_GCNN.pkl
[2025-09-05 06:54:53]   - 迭代次数: final
[2025-09-05 06:54:53]   - 能量: -53.700410-0.001197j ± 0.082965
[2025-09-05 06:54:53]   - 时间戳: 2025-09-05T06:54:43.901258+08:00
[2025-09-05 06:55:05] ✓ 变分状态参数已从checkpoint恢复
[2025-09-05 06:55:05] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-05 06:55:05] ==================================================
[2025-09-05 06:55:05] GCNN for Shastry-Sutherland Model
[2025-09-05 06:55:05] ==================================================
[2025-09-05 06:55:05] System parameters:
[2025-09-05 06:55:05]   - System size: L=4, N=64
[2025-09-05 06:55:05]   - System parameters: J1=0.1, J2=0.05, Q=0.95
[2025-09-05 06:55:05] --------------------------------------------------
[2025-09-05 06:55:05] Model parameters:
[2025-09-05 06:55:05]   - Number of layers = 4
[2025-09-05 06:55:05]   - Number of features = 4
[2025-09-05 06:55:05]   - Total parameters = 12572
[2025-09-05 06:55:05] --------------------------------------------------
[2025-09-05 06:55:05] Training parameters:
[2025-09-05 06:55:05]   - Learning rate: 0.015
[2025-09-05 06:55:05]   - Total iterations: 1050
[2025-09-05 06:55:05]   - Annealing cycles: 3
[2025-09-05 06:55:05]   - Initial period: 150
[2025-09-05 06:55:05]   - Period multiplier: 2.0
[2025-09-05 06:55:05]   - Temperature range: 0.0-1.0
[2025-09-05 06:55:05]   - Samples: 4096
[2025-09-05 06:55:05]   - Discarded samples: 0
[2025-09-05 06:55:05]   - Chunk size: 2048
[2025-09-05 06:55:05]   - Diagonal shift: 0.2
[2025-09-05 06:55:05]   - Gradient clipping: 1.0
[2025-09-05 06:55:05]   - Checkpoint enabled: interval=105
[2025-09-05 06:55:05]   - Checkpoint directory: results/L=4/J2=0.05/J1=0.10/training/checkpoints
[2025-09-05 06:55:05] --------------------------------------------------
[2025-09-05 06:55:05] Device status:
[2025-09-05 06:55:05]   - Devices model: NVIDIA H200 NVL
[2025-09-05 06:55:05]   - Number of devices: 1
[2025-09-05 06:55:05]   - Sharding: True
[2025-09-05 06:55:05] ============================================================
[2025-09-05 06:55:48] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -54.119279-0.004789j
[2025-09-05 06:56:15] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -54.318952-0.003311j
[2025-09-05 06:56:25] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -54.263387+0.001276j
[2025-09-05 06:56:35] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -54.286951+0.000540j
[2025-09-05 06:56:45] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -54.175781+0.000825j
[2025-09-05 06:56:56] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -54.187489+0.000798j
[2025-09-05 06:57:06] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -54.180542+0.001468j
[2025-09-05 06:57:16] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -54.110750+0.001968j
[2025-09-05 06:57:26] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -54.149813-0.002596j
[2025-09-05 06:57:36] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -54.123956+0.000390j
[2025-09-05 06:57:46] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -54.133261-0.002960j
[2025-09-05 06:57:57] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -54.194691-0.004243j
[2025-09-05 06:58:07] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -54.167516+0.000114j
[2025-09-05 06:58:17] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -54.217621-0.000852j
[2025-09-05 06:58:27] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -54.166584+0.000709j
[2025-09-05 06:58:37] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -54.268864-0.001304j
[2025-09-05 06:58:47] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -54.084237+0.000314j
[2025-09-05 06:58:58] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -54.077714-0.001331j
[2025-09-05 06:59:08] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -54.113621-0.004501j
[2025-09-05 06:59:18] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -54.140048+0.003997j
[2025-09-05 06:59:28] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -54.191686-0.001444j
[2025-09-05 06:59:38] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -54.313802-0.002735j
[2025-09-05 06:59:48] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -54.275975+0.000397j
[2025-09-05 06:59:58] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -54.093718-0.001288j
[2025-09-05 07:00:09] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -54.169151+0.001123j
[2025-09-05 07:00:19] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -54.141098-0.001512j
[2025-09-05 07:00:29] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -54.210211+0.000353j
[2025-09-05 07:00:39] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -54.226638+0.000554j
[2025-09-05 07:00:49] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -54.288098-0.001792j
[2025-09-05 07:00:59] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -54.291990+0.002150j
[2025-09-05 07:01:10] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -54.163960+0.000633j
[2025-09-05 07:01:20] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -54.105791-0.004253j
[2025-09-05 07:01:30] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -54.130804-0.001748j
[2025-09-05 07:01:40] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -54.140626+0.001137j
[2025-09-05 07:01:50] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -54.057918-0.000600j
[2025-09-05 07:02:00] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -54.081246+0.002781j
[2025-09-05 07:02:11] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -54.153835-0.000529j
[2025-09-05 07:02:21] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -54.255662+0.000979j
[2025-09-05 07:02:31] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -54.173032+0.000356j
[2025-09-05 07:02:41] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -54.064883-0.002304j
[2025-09-05 07:02:51] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -54.146419+0.000377j
[2025-09-05 07:03:01] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -54.046999-0.002597j
[2025-09-05 07:03:12] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -54.141303+0.001237j
[2025-09-05 07:03:22] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -54.178119-0.001312j
[2025-09-05 07:03:32] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -54.214293+0.000689j
[2025-09-05 07:03:42] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -54.141412-0.002445j
[2025-09-05 07:03:52] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -54.136757+0.001258j
[2025-09-05 07:04:02] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -54.188299+0.004653j
[2025-09-05 07:04:13] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -54.129701+0.003163j
[2025-09-05 07:04:23] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -54.101940+0.001739j
[2025-09-05 07:04:33] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -54.187471-0.001081j
[2025-09-05 07:04:43] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -54.210564-0.001092j
[2025-09-05 07:04:53] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -54.243540-0.001941j
[2025-09-05 07:05:03] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -54.166363-0.000931j
[2025-09-05 07:05:14] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -54.157204-0.001834j
[2025-09-05 07:05:24] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -54.083860+0.003009j
[2025-09-05 07:05:34] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -54.184126+0.000761j
[2025-09-05 07:05:44] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -54.069242-0.000483j
[2025-09-05 07:05:54] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -54.194702-0.001835j
[2025-09-05 07:06:04] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -54.090130+0.000980j
[2025-09-05 07:06:15] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -54.085773+0.001716j
[2025-09-05 07:06:25] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -54.116294+0.000417j
[2025-09-05 07:06:35] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -54.252273+0.002560j
[2025-09-05 07:06:45] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -54.139092+0.001088j
[2025-09-05 07:06:55] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -54.139514-0.001820j
[2025-09-05 07:07:05] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -54.085923+0.000207j
[2025-09-05 07:07:16] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -54.157104+0.000025j
[2025-09-05 07:07:26] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -54.241629-0.000508j
[2025-09-05 07:07:36] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -54.239249-0.002253j
[2025-09-05 07:07:46] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -54.242891-0.000571j
[2025-09-05 07:07:56] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -54.235001-0.003824j
[2025-09-05 07:08:06] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -54.249671-0.001969j
[2025-09-05 07:08:16] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -54.208354-0.002701j
[2025-09-05 07:08:27] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -54.051782+0.000795j
[2025-09-05 07:08:37] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -54.098716+0.001492j
[2025-09-05 07:08:47] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -54.050668-0.001934j
[2025-09-05 07:08:57] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -54.114925-0.000933j
[2025-09-05 07:09:07] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -53.995512+0.001612j
[2025-09-05 07:09:17] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -54.142828+0.002007j
[2025-09-05 07:09:28] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -54.119203+0.000693j
[2025-09-05 07:09:38] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -54.069755+0.000466j
[2025-09-05 07:09:48] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -54.184739-0.003506j
[2025-09-05 07:09:58] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -54.197234-0.001142j
[2025-09-05 07:10:08] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -54.281265+0.000178j
[2025-09-05 07:10:18] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -54.210228+0.001279j
[2025-09-05 07:10:29] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -54.175618+0.000032j
[2025-09-05 07:10:39] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -54.160107-0.000309j
[2025-09-05 07:10:49] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -54.060531-0.000720j
[2025-09-05 07:10:59] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -54.108226-0.000884j
[2025-09-05 07:11:09] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -54.153360-0.000713j
[2025-09-05 07:11:19] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -54.070072-0.002540j
[2025-09-05 07:11:29] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -54.022761+0.001536j
[2025-09-05 07:11:40] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -53.991688-0.003079j
[2025-09-05 07:11:50] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -53.974968-0.001119j
[2025-09-05 07:12:00] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -53.875470+0.001482j
[2025-09-05 07:12:10] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -53.987502-0.002348j
[2025-09-05 07:12:20] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -54.057721-0.003057j
[2025-09-05 07:12:30] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -54.056872-0.000215j
[2025-09-05 07:12:41] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -54.023976+0.000405j
[2025-09-05 07:12:51] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -54.062643-0.000129j
[2025-09-05 07:13:01] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -54.065167-0.001077j
[2025-09-05 07:13:11] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -54.137119-0.001981j
[2025-09-05 07:13:21] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -54.153950+0.001506j
[2025-09-05 07:13:31] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -54.223083+0.003128j
[2025-09-05 07:13:42] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -54.166965+0.001186j
[2025-09-05 07:13:42] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-05 07:13:52] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -54.184147-0.000475j
[2025-09-05 07:14:02] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -54.297220+0.000738j
[2025-09-05 07:14:12] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -54.197260+0.003079j
[2025-09-05 07:14:22] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -54.206390+0.002416j
[2025-09-05 07:14:32] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -54.125733+0.001079j
[2025-09-05 07:14:43] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -54.143284+0.000048j
[2025-09-05 07:14:53] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -54.103541-0.001586j
[2025-09-05 07:15:03] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -54.175154-0.000055j
[2025-09-05 07:15:13] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -54.250932+0.001089j
[2025-09-05 07:15:23] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -54.114700+0.000623j
[2025-09-05 07:15:33] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -54.168050+0.002460j
[2025-09-05 07:15:44] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -54.251698-0.003538j
[2025-09-05 07:15:54] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -54.231087-0.000741j
[2025-09-05 07:16:04] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -54.210198+0.000048j
[2025-09-05 07:16:14] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -54.093210+0.000451j
[2025-09-05 07:16:24] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -54.124646-0.000613j
[2025-09-05 07:16:34] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -54.190266+0.000078j
[2025-09-05 07:16:45] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -54.193285-0.000428j
[2025-09-05 07:16:55] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -54.144724+0.002382j
[2025-09-05 07:17:05] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -53.990561+0.001892j
[2025-09-05 07:17:15] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -54.026102-0.003447j
[2025-09-05 07:17:25] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -54.100947+0.001218j
[2025-09-05 07:17:35] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -54.133365-0.000729j
[2025-09-05 07:17:46] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -54.052600-0.001788j
[2025-09-05 07:17:56] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -54.059119+0.000577j
[2025-09-05 07:18:06] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -54.163576-0.002349j
[2025-09-05 07:18:16] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -54.206168-0.001118j
[2025-09-05 07:18:26] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -54.154625+0.003811j
[2025-09-05 07:18:36] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -54.124773+0.001321j
[2025-09-05 07:18:46] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -54.103768+0.002750j
[2025-09-05 07:18:56] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -54.091257+0.000236j
[2025-09-05 07:19:07] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -54.220348-0.002331j
[2025-09-05 07:19:17] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -54.179112+0.000990j
[2025-09-05 07:19:27] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -54.282137-0.000319j
[2025-09-05 07:19:37] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -54.289915+0.004783j
[2025-09-05 07:19:47] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -54.174923-0.000843j
[2025-09-05 07:19:57] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -54.010967+0.000137j
[2025-09-05 07:20:08] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -54.065677+0.000887j
[2025-09-05 07:20:18] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -54.081820+0.001818j
[2025-09-05 07:20:28] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -54.190790-0.002390j
[2025-09-05 07:20:38] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -54.167551-0.000838j
[2025-09-05 07:20:48] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -54.199855+0.000387j
[2025-09-05 07:20:58] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -54.059670+0.002142j
[2025-09-05 07:21:09] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -54.094075-0.000617j
[2025-09-05 07:21:19] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -54.287946+0.003218j
[2025-09-05 07:21:19] RESTART #1 | Period: 300
[2025-09-05 07:21:29] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -54.187295+0.002069j
[2025-09-05 07:21:39] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -54.068497+0.000756j
[2025-09-05 07:21:49] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -54.260814-0.000089j
[2025-09-05 07:21:59] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -54.204516+0.005129j
[2025-09-05 07:22:09] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -54.079663-0.004132j
[2025-09-05 07:22:20] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -54.167094+0.003507j
[2025-09-05 07:22:30] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -54.276218-0.001291j
[2025-09-05 07:22:40] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -54.185637-0.003700j
[2025-09-05 07:22:50] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -54.154444-0.001099j
[2025-09-05 07:23:00] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -54.086422+0.001258j
[2025-09-05 07:23:10] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -54.211656-0.002812j
[2025-09-05 07:23:21] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -54.100788-0.000728j
[2025-09-05 07:23:31] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -54.224929+0.000636j
[2025-09-05 07:23:41] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -54.128080-0.000772j
[2025-09-05 07:23:51] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -54.283817+0.001956j
[2025-09-05 07:24:01] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -54.218831+0.002781j
[2025-09-05 07:24:11] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -54.100159-0.000559j
[2025-09-05 07:24:22] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -53.931481-0.000841j
[2025-09-05 07:24:32] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -54.241679-0.004087j
[2025-09-05 07:24:42] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -54.059202+0.004096j
[2025-09-05 07:24:52] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -54.160721+0.001213j
[2025-09-05 07:25:02] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -54.081792+0.000985j
[2025-09-05 07:25:12] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -54.050863+0.000966j
[2025-09-05 07:25:22] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -54.136555-0.000663j
[2025-09-05 07:25:33] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -54.114942-0.000924j
[2025-09-05 07:25:43] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -54.264956+0.002192j
[2025-09-05 07:25:53] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -54.220139+0.000904j
[2025-09-05 07:26:03] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -54.155652+0.004184j
[2025-09-05 07:26:13] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -54.223322+0.000116j
[2025-09-05 07:26:23] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -54.223718-0.000110j
[2025-09-05 07:26:34] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -54.015264+0.001209j
[2025-09-05 07:26:44] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -54.079560+0.000357j
[2025-09-05 07:26:54] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -54.213867-0.000102j
[2025-09-05 07:27:04] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -54.219440-0.003437j
[2025-09-05 07:27:14] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -54.134238+0.002747j
[2025-09-05 07:27:24] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -54.114323+0.002636j
[2025-09-05 07:27:35] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -54.224680-0.000887j
[2025-09-05 07:27:45] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -54.211642-0.000053j
[2025-09-05 07:27:55] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -54.274205-0.001763j
[2025-09-05 07:28:05] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -54.210083+0.000646j
[2025-09-05 07:28:15] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -54.126869+0.000602j
[2025-09-05 07:28:25] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -54.067159+0.001897j
[2025-09-05 07:28:36] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -54.059055+0.000434j
[2025-09-05 07:28:46] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -53.978888-0.000630j
[2025-09-05 07:28:56] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -54.087415+0.002748j
[2025-09-05 07:29:06] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -54.066312-0.000469j
[2025-09-05 07:29:16] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -54.196419+0.001380j
[2025-09-05 07:29:26] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -54.136277+0.000329j
[2025-09-05 07:29:37] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -54.084554+0.001662j
[2025-09-05 07:29:47] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -54.174794+0.000706j
[2025-09-05 07:29:57] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -54.208300+0.000187j
[2025-09-05 07:30:07] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -54.140217-0.001273j
[2025-09-05 07:30:17] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -54.117928+0.003304j
[2025-09-05 07:30:27] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -54.189265+0.001130j
[2025-09-05 07:30:37] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -54.173364+0.004249j
[2025-09-05 07:30:48] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -54.271836+0.004136j
[2025-09-05 07:30:58] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -54.274029-0.002076j
[2025-09-05 07:31:08] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -54.173102+0.000557j
[2025-09-05 07:31:18] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -54.216806-0.000023j
[2025-09-05 07:31:28] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -54.294322-0.001048j
[2025-09-05 07:31:28] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-05 07:31:38] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -54.328368-0.000989j
[2025-09-05 07:31:49] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -54.224429-0.001903j
[2025-09-05 07:31:59] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -54.092246+0.003005j
[2025-09-05 07:32:09] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -54.132615-0.004717j
[2025-09-05 07:32:19] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -54.157051+0.001763j
[2025-09-05 07:32:29] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -54.120431+0.000244j
[2025-09-05 07:32:39] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -54.317189+0.000682j
[2025-09-05 07:32:50] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -54.288855-0.001485j
[2025-09-05 07:33:00] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -54.172598+0.000401j
[2025-09-05 07:33:10] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -54.129731+0.001884j
[2025-09-05 07:33:20] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -54.081405+0.002747j
[2025-09-05 07:33:30] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -54.013673-0.000232j
[2025-09-05 07:33:40] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -54.243195-0.000745j
[2025-09-05 07:33:51] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -54.154304-0.002177j
[2025-09-05 07:34:01] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -54.053504-0.000536j
[2025-09-05 07:34:11] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -54.216251-0.000717j
[2025-09-05 07:34:21] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -54.128698-0.000404j
[2025-09-05 07:34:31] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -54.155773-0.000431j
[2025-09-05 07:34:41] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -54.225911-0.001255j
[2025-09-05 07:34:52] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -54.179589-0.000069j
[2025-09-05 07:35:02] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -54.140285+0.000169j
[2025-09-05 07:35:12] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -54.263253+0.000037j
[2025-09-05 07:35:22] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -54.279954-0.000117j
[2025-09-05 07:35:32] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -54.083255-0.000427j
[2025-09-05 07:35:42] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -54.275853+0.002145j
[2025-09-05 07:35:53] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -54.154470+0.000305j
[2025-09-05 07:36:03] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -54.205150-0.001282j
[2025-09-05 07:36:13] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -53.968756-0.000556j
[2025-09-05 07:36:23] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -54.064481+0.001103j
[2025-09-05 07:36:33] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -54.040507+0.004106j
[2025-09-05 07:36:43] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -54.124050-0.001549j
[2025-09-05 07:36:53] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -54.164072-0.003600j
[2025-09-05 07:37:04] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -54.281053-0.000552j
[2025-09-05 07:37:14] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -54.167555+0.001355j
[2025-09-05 07:37:24] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -54.053291-0.003768j
[2025-09-05 07:37:34] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -54.113881-0.001972j
[2025-09-05 07:37:44] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -54.149260-0.004974j
[2025-09-05 07:37:54] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -54.131456-0.002130j
[2025-09-05 07:38:05] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -54.132495+0.001632j
[2025-09-05 07:38:15] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -54.256812+0.002021j
[2025-09-05 07:38:25] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -54.254297-0.000860j
[2025-09-05 07:38:35] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -54.213148+0.000785j
[2025-09-05 07:38:45] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -54.103709-0.002448j
[2025-09-05 07:38:55] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -54.194890+0.003862j
[2025-09-05 07:39:06] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -54.150264+0.001430j
[2025-09-05 07:39:16] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -54.179942-0.001372j
[2025-09-05 07:39:26] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -54.169177-0.000811j
[2025-09-05 07:39:36] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -54.067380-0.004513j
[2025-09-05 07:39:46] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -54.228426+0.001029j
[2025-09-05 07:39:56] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -54.232869+0.002304j
[2025-09-05 07:40:07] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -54.190578+0.000863j
[2025-09-05 07:40:17] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -54.063197+0.000889j
[2025-09-05 07:40:27] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -54.096777+0.000644j
[2025-09-05 07:40:37] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -54.063688+0.000519j
[2025-09-05 07:40:47] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -54.193075+0.001479j
[2025-09-05 07:40:57] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -54.017461-0.000202j
[2025-09-05 07:41:08] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -54.030694-0.002268j
[2025-09-05 07:41:18] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -54.010112+0.001131j
[2025-09-05 07:41:28] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -54.144597-0.000112j
[2025-09-05 07:41:38] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -54.169621+0.001551j
[2025-09-05 07:41:48] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -53.941984+0.002346j
[2025-09-05 07:41:58] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -54.091002-0.001956j
[2025-09-05 07:42:09] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -54.240252+0.000089j
[2025-09-05 07:42:19] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -54.197514+0.000192j
[2025-09-05 07:42:29] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -54.193030-0.001319j
[2025-09-05 07:42:39] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -54.200308+0.000712j
[2025-09-05 07:42:49] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -54.199352-0.000866j
[2025-09-05 07:42:59] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -54.121629-0.001309j
[2025-09-05 07:43:10] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -54.078131+0.001361j
[2025-09-05 07:43:20] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -54.154607-0.001018j
[2025-09-05 07:43:30] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -54.198767-0.001219j
[2025-09-05 07:43:40] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -54.113090-0.000619j
[2025-09-05 07:43:50] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -54.154245-0.001680j
[2025-09-05 07:44:00] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -54.115913+0.001168j
[2025-09-05 07:44:11] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -54.079290+0.001556j
[2025-09-05 07:44:21] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -54.032403-0.000332j
[2025-09-05 07:44:31] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -54.086224+0.001372j
[2025-09-05 07:44:41] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -54.088853-0.001960j
[2025-09-05 07:44:51] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -54.097475-0.002031j
[2025-09-05 07:45:01] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -53.972791-0.000626j
[2025-09-05 07:45:12] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -54.068708+0.001229j
[2025-09-05 07:45:22] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -54.102110-0.003931j
[2025-09-05 07:45:32] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -54.296001-0.001020j
[2025-09-05 07:45:42] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -54.228125-0.000073j
[2025-09-05 07:45:52] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -54.075343+0.000866j
[2025-09-05 07:46:02] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -54.157494-0.001388j
[2025-09-05 07:46:13] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -54.181008-0.000097j
[2025-09-05 07:46:23] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -54.145128+0.000978j
[2025-09-05 07:46:33] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -54.167901-0.002805j
[2025-09-05 07:46:43] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -54.165431-0.000685j
[2025-09-05 07:46:53] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -54.295451+0.001100j
[2025-09-05 07:47:03] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -54.310039-0.000799j
[2025-09-05 07:47:14] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -54.303595+0.001327j
[2025-09-05 07:47:24] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -54.187474+0.002143j
[2025-09-05 07:47:34] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -54.100920-0.000028j
[2025-09-05 07:47:44] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -54.074372+0.000262j
[2025-09-05 07:47:54] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -54.002848+0.003668j
[2025-09-05 07:48:04] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -53.957451+0.001726j
[2025-09-05 07:48:14] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -53.926599+0.002291j
[2025-09-05 07:48:25] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -54.080661-0.000091j
[2025-09-05 07:48:35] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -54.102249-0.001127j
[2025-09-05 07:48:45] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -54.198381+0.001188j
[2025-09-05 07:48:55] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -54.129112-0.001698j
[2025-09-05 07:49:05] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -54.154515-0.000797j
[2025-09-05 07:49:15] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -54.227097-0.002100j
[2025-09-05 07:49:15] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-05 07:49:26] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -54.228592+0.002996j
[2025-09-05 07:49:36] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -54.172303+0.000347j
[2025-09-05 07:49:46] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -54.160044-0.002135j
[2025-09-05 07:49:56] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -54.162883+0.002572j
[2025-09-05 07:50:06] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -54.173180+0.003902j
[2025-09-05 07:50:16] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -54.081032-0.001502j
[2025-09-05 07:50:27] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -54.022419-0.002506j
[2025-09-05 07:50:37] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -54.058641-0.002986j
[2025-09-05 07:50:47] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -54.095121+0.001833j
[2025-09-05 07:50:57] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -54.049736-0.000775j
[2025-09-05 07:51:07] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -54.026186-0.000863j
[2025-09-05 07:51:17] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -53.937537-0.002089j
[2025-09-05 07:51:27] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -53.978488-0.001833j
[2025-09-05 07:51:38] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -54.132813-0.003934j
[2025-09-05 07:51:48] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -54.076227+0.004128j
[2025-09-05 07:51:58] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -54.237340+0.000992j
[2025-09-05 07:52:08] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -54.275762+0.002075j
[2025-09-05 07:52:18] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -54.046485+0.003331j
[2025-09-05 07:52:28] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -54.185617-0.002221j
[2025-09-05 07:52:39] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -54.152608+0.002669j
[2025-09-05 07:52:49] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -54.164900-0.000439j
[2025-09-05 07:52:59] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -54.266057-0.001491j
[2025-09-05 07:53:09] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -54.076862+0.001560j
[2025-09-05 07:53:19] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -54.075717+0.000310j
[2025-09-05 07:53:29] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -54.093322+0.000447j
[2025-09-05 07:53:40] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -54.098859+0.001656j
[2025-09-05 07:53:50] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -54.157832-0.002595j
[2025-09-05 07:54:00] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -54.108640-0.000007j
[2025-09-05 07:54:10] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -53.980024+0.001077j
[2025-09-05 07:54:20] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -54.104701-0.000095j
[2025-09-05 07:54:30] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -54.197187-0.002338j
[2025-09-05 07:54:41] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -54.205859-0.001826j
[2025-09-05 07:54:51] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -54.012545+0.001096j
[2025-09-05 07:55:01] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -54.063510-0.003542j
[2025-09-05 07:55:11] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -53.990229-0.002135j
[2025-09-05 07:55:21] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -54.149821+0.000945j
[2025-09-05 07:55:31] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -54.215190-0.001990j
[2025-09-05 07:55:42] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -54.085009+0.003624j
[2025-09-05 07:55:52] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -54.092904-0.001275j
[2025-09-05 07:56:02] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -54.053332-0.002294j
[2025-09-05 07:56:12] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -54.111978+0.001326j
[2025-09-05 07:56:22] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -54.037028+0.001065j
[2025-09-05 07:56:32] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -54.200880-0.000052j
[2025-09-05 07:56:43] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -54.183470-0.001934j
[2025-09-05 07:56:53] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -54.121381-0.000997j
[2025-09-05 07:57:03] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -54.095878+0.000098j
[2025-09-05 07:57:13] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -54.179915+0.000288j
[2025-09-05 07:57:23] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -54.111623-0.001820j
[2025-09-05 07:57:33] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -54.172047-0.000069j
[2025-09-05 07:57:43] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -54.084873-0.002704j
[2025-09-05 07:57:54] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -54.060311-0.000856j
[2025-09-05 07:58:04] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -54.118665-0.001039j
[2025-09-05 07:58:14] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -54.076383+0.002084j
[2025-09-05 07:58:24] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -54.176338-0.000714j
[2025-09-05 07:58:34] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -54.103535+0.000367j
[2025-09-05 07:58:44] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -54.138816-0.001088j
[2025-09-05 07:58:55] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -54.141927+0.000189j
[2025-09-05 07:59:05] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -54.206067-0.001795j
[2025-09-05 07:59:15] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -54.089660+0.000550j
[2025-09-05 07:59:25] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -54.162422+0.002147j
[2025-09-05 07:59:35] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -54.081012-0.000396j
[2025-09-05 07:59:45] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -54.051862+0.000579j
[2025-09-05 07:59:55] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -54.039434-0.002240j
[2025-09-05 08:00:05] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -54.088521-0.000073j
[2025-09-05 08:00:15] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -54.092098-0.000227j
[2025-09-05 08:00:25] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -54.166405+0.004639j
[2025-09-05 08:00:36] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -54.185548-0.000282j
[2025-09-05 08:00:46] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -54.280924+0.000627j
[2025-09-05 08:00:56] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -54.093026-0.000948j
[2025-09-05 08:01:06] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -54.007688+0.003848j
[2025-09-05 08:01:16] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -54.068812+0.001139j
[2025-09-05 08:01:26] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -54.037557+0.001057j
[2025-09-05 08:01:37] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -54.003543-0.001088j
[2025-09-05 08:01:47] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -54.111303-0.000380j
[2025-09-05 08:01:57] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -54.070005-0.000770j
[2025-09-05 08:02:07] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -54.001890-0.000506j
[2025-09-05 08:02:17] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -54.283459+0.001687j
[2025-09-05 08:02:27] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -54.145034+0.000694j
[2025-09-05 08:02:38] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -54.276687-0.002038j
[2025-09-05 08:02:48] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -54.212638+0.001697j
[2025-09-05 08:02:58] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -54.215542-0.002931j
[2025-09-05 08:03:08] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -54.120623-0.002843j
[2025-09-05 08:03:18] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -54.187280+0.001509j
[2025-09-05 08:03:28] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -54.171884+0.000437j
[2025-09-05 08:03:38] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -54.070189+0.000195j
[2025-09-05 08:03:49] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -54.168574-0.000541j
[2025-09-05 08:03:59] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -54.019060-0.000985j
[2025-09-05 08:04:09] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -54.150838-0.000425j
[2025-09-05 08:04:19] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -54.107565-0.002549j
[2025-09-05 08:04:29] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -54.183272+0.001827j
[2025-09-05 08:04:39] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -54.108524+0.003100j
[2025-09-05 08:04:50] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -54.290236-0.002108j
[2025-09-05 08:05:00] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -54.195083-0.000983j
[2025-09-05 08:05:10] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -54.051220-0.001450j
[2025-09-05 08:05:20] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -54.107576+0.001152j
[2025-09-05 08:05:30] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -54.258978-0.001554j
[2025-09-05 08:05:40] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -54.184723+0.000682j
[2025-09-05 08:05:51] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -54.184322+0.002103j
[2025-09-05 08:06:01] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -54.139038+0.000822j
[2025-09-05 08:06:11] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -54.087826+0.002343j
[2025-09-05 08:06:21] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -54.103483+0.000537j
[2025-09-05 08:06:31] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -54.174644-0.001514j
[2025-09-05 08:06:41] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -54.175855+0.001348j
[2025-09-05 08:06:51] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -54.235919-0.002136j
[2025-09-05 08:07:02] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -54.112522-0.000904j
[2025-09-05 08:07:02] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-05 08:07:12] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -54.053836-0.000476j
[2025-09-05 08:07:22] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -54.117388+0.001102j
[2025-09-05 08:07:32] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -54.265781+0.000540j
[2025-09-05 08:07:42] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -54.099825+0.000125j
[2025-09-05 08:07:52] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -54.052025+0.000402j
[2025-09-05 08:08:03] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -53.945971+0.000326j
[2025-09-05 08:08:13] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -54.104860+0.001707j
[2025-09-05 08:08:23] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -54.138640-0.002307j
[2025-09-05 08:08:33] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -54.145539+0.000154j
[2025-09-05 08:08:43] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -54.108049-0.001471j
[2025-09-05 08:08:53] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -54.066816+0.002036j
[2025-09-05 08:09:04] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -54.123998-0.003478j
[2025-09-05 08:09:14] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -54.089999+0.001614j
[2025-09-05 08:09:24] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -53.929486+0.000769j
[2025-09-05 08:09:34] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -54.053866-0.000330j
[2025-09-05 08:09:44] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -54.066297+0.001392j
[2025-09-05 08:09:54] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -54.083734+0.000152j
[2025-09-05 08:10:05] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -54.079645-0.000273j
[2025-09-05 08:10:15] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -54.138488+0.000794j
[2025-09-05 08:10:25] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -54.065784-0.000937j
[2025-09-05 08:10:35] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -54.180171+0.002289j
[2025-09-05 08:10:45] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -54.084168+0.000291j
[2025-09-05 08:10:55] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -54.165507-0.000173j
[2025-09-05 08:11:05] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -54.180090+0.002070j
[2025-09-05 08:11:16] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -54.126173+0.000342j
[2025-09-05 08:11:26] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -54.189136-0.002697j
[2025-09-05 08:11:36] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -54.210368+0.000226j
[2025-09-05 08:11:46] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -54.220243+0.000223j
[2025-09-05 08:11:56] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -54.202316+0.000677j
[2025-09-05 08:12:06] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -54.133405-0.000845j
[2025-09-05 08:12:06] RESTART #2 | Period: 600
[2025-09-05 08:12:17] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -54.132827+0.000348j
[2025-09-05 08:12:27] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -54.206626-0.001574j
[2025-09-05 08:12:37] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -54.174201+0.000681j
[2025-09-05 08:12:47] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -54.100534-0.000924j
[2025-09-05 08:12:57] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -54.065426-0.000944j
[2025-09-05 08:13:07] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -54.026694+0.000840j
[2025-09-05 08:13:17] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -54.118128-0.000187j
[2025-09-05 08:13:28] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -54.127160+0.000015j
[2025-09-05 08:13:38] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -54.041593-0.001737j
[2025-09-05 08:13:48] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -53.986913-0.000217j
[2025-09-05 08:13:58] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -54.184038-0.002039j
[2025-09-05 08:14:08] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -54.211811+0.000443j
[2025-09-05 08:14:18] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -54.123247+0.001964j
[2025-09-05 08:14:28] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -54.198837+0.001500j
[2025-09-05 08:14:38] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -54.115614-0.001615j
[2025-09-05 08:14:49] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -54.140601-0.000320j
[2025-09-05 08:14:59] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -54.196654-0.003240j
[2025-09-05 08:15:09] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -54.183545-0.001217j
[2025-09-05 08:15:19] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -54.068255+0.000469j
[2025-09-05 08:15:29] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -54.044973-0.001680j
[2025-09-05 08:15:39] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -53.963968-0.000326j
[2025-09-05 08:15:50] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -54.079407+0.000291j
[2025-09-05 08:16:00] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -54.182419+0.000460j
[2025-09-05 08:16:10] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -54.225548-0.002769j
[2025-09-05 08:16:20] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -54.218231-0.000413j
[2025-09-05 08:16:30] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -54.147034-0.000016j
[2025-09-05 08:16:40] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -54.030300-0.000933j
[2025-09-05 08:16:51] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -54.112878+0.000311j
[2025-09-05 08:17:01] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -54.141408-0.002790j
[2025-09-05 08:17:11] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -54.081138-0.001344j
[2025-09-05 08:17:21] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -54.206230-0.001246j
[2025-09-05 08:17:31] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -54.108523-0.000716j
[2025-09-05 08:17:41] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -54.019727-0.001220j
[2025-09-05 08:17:51] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -53.983126+0.000569j
[2025-09-05 08:18:02] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -54.129775-0.000809j
[2025-09-05 08:18:12] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -54.181667+0.000027j
[2025-09-05 08:18:22] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -54.195293-0.000248j
[2025-09-05 08:18:32] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -54.273354-0.001492j
[2025-09-05 08:18:42] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -54.192676+0.001388j
[2025-09-05 08:18:52] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -54.168275-0.002063j
[2025-09-05 08:19:03] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -54.103465-0.004045j
[2025-09-05 08:19:13] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -54.157357+0.002995j
[2025-09-05 08:19:23] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -54.137087+0.001840j
[2025-09-05 08:19:33] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -54.111342+0.001372j
[2025-09-05 08:19:43] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -54.219342-0.001129j
[2025-09-05 08:19:53] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -54.124252-0.001315j
[2025-09-05 08:20:04] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -54.163296+0.000845j
[2025-09-05 08:20:14] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -54.203158+0.001302j
[2025-09-05 08:20:24] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -54.091893-0.000108j
[2025-09-05 08:20:34] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -54.210285+0.001291j
[2025-09-05 08:20:44] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -54.115131-0.001282j
[2025-09-05 08:20:54] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -54.260139+0.002162j
[2025-09-05 08:21:04] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -54.204816+0.001002j
[2025-09-05 08:21:15] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -54.340824+0.000978j
[2025-09-05 08:21:25] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -54.200554-0.000210j
[2025-09-05 08:21:35] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -54.204278-0.002457j
[2025-09-05 08:21:45] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -54.123997+0.000280j
[2025-09-05 08:21:55] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -54.232937-0.000485j
[2025-09-05 08:22:05] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -53.948252+0.001366j
[2025-09-05 08:22:16] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -54.056482+0.002391j
[2025-09-05 08:22:26] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -54.142603+0.000662j
[2025-09-05 08:22:36] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -54.007665-0.001118j
[2025-09-05 08:22:46] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -54.017687+0.002268j
[2025-09-05 08:22:56] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -54.049191-0.001531j
[2025-09-05 08:23:06] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -54.145578-0.002968j
[2025-09-05 08:23:17] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -54.230554+0.000510j
[2025-09-05 08:23:27] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -54.154426+0.001147j
[2025-09-05 08:23:37] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -54.363931-0.002711j
[2025-09-05 08:23:47] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -54.241431+0.001789j
[2025-09-05 08:23:57] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -54.136636-0.000702j
[2025-09-05 08:24:07] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -54.197700+0.000291j
[2025-09-05 08:24:17] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -54.270281+0.001465j
[2025-09-05 08:24:28] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -54.241786+0.002168j
[2025-09-05 08:24:38] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -54.266539+0.004794j
[2025-09-05 08:24:48] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -54.253226-0.003173j
[2025-09-05 08:24:48] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-05 08:24:58] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -54.260872+0.001888j
[2025-09-05 08:25:08] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -54.266833-0.001000j
[2025-09-05 08:25:18] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -54.170732+0.000670j
[2025-09-05 08:25:29] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -54.185437-0.000571j
[2025-09-05 08:25:39] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -54.140885+0.000160j
[2025-09-05 08:25:49] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -54.103434+0.001585j
[2025-09-05 08:25:59] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -54.170456-0.001816j
[2025-09-05 08:26:09] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -54.243608+0.000591j
[2025-09-05 08:26:19] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -54.282169-0.000720j
[2025-09-05 08:26:29] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -54.371161-0.001613j
[2025-09-05 08:26:40] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -54.208622-0.003423j
[2025-09-05 08:26:50] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -54.285335-0.001568j
[2025-09-05 08:27:00] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -54.260884-0.001495j
[2025-09-05 08:27:10] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -54.157622-0.001238j
[2025-09-05 08:27:20] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -54.244215-0.000932j
[2025-09-05 08:27:30] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -54.290064+0.001128j
[2025-09-05 08:27:40] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -54.217432-0.001644j
[2025-09-05 08:27:50] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -54.209576-0.001540j
[2025-09-05 08:28:01] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -54.040396-0.002393j
[2025-09-05 08:28:11] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -54.232259-0.002098j
[2025-09-05 08:28:21] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -54.101454-0.001726j
[2025-09-05 08:28:31] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -54.040014+0.000297j
[2025-09-05 08:28:41] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -54.004485-0.001762j
[2025-09-05 08:28:51] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -54.020189-0.001402j
[2025-09-05 08:29:02] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -54.054558-0.002380j
[2025-09-05 08:29:12] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -54.004181+0.000145j
[2025-09-05 08:29:22] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -54.190366+0.001574j
[2025-09-05 08:29:32] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -54.114425+0.000831j
[2025-09-05 08:29:42] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -54.087346-0.001525j
[2025-09-05 08:29:52] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -53.997327-0.002703j
[2025-09-05 08:30:03] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -53.991878+0.001732j
[2025-09-05 08:30:13] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -54.140760-0.001387j
[2025-09-05 08:30:23] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -54.114130+0.002814j
[2025-09-05 08:30:33] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -53.965473-0.000995j
[2025-09-05 08:30:43] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -54.166548-0.002673j
[2025-09-05 08:30:53] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -54.222724-0.000833j
[2025-09-05 08:31:04] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -54.213855+0.003450j
[2025-09-05 08:31:14] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -54.226951+0.004532j
[2025-09-05 08:31:24] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -54.165670-0.000270j
[2025-09-05 08:31:34] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -54.160995-0.002497j
[2025-09-05 08:31:44] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -54.067916-0.001433j
[2025-09-05 08:31:54] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -54.165488-0.000508j
[2025-09-05 08:32:04] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -54.082775+0.002680j
[2025-09-05 08:32:15] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -54.230870-0.002231j
[2025-09-05 08:32:25] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -54.014714-0.001846j
[2025-09-05 08:32:35] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -53.958508+0.000150j
[2025-09-05 08:32:45] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -54.062840-0.001925j
[2025-09-05 08:32:55] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -54.029252+0.001218j
[2025-09-05 08:33:05] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -54.077792-0.000168j
[2025-09-05 08:33:16] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -54.020636+0.000771j
[2025-09-05 08:33:26] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -54.152366+0.000347j
[2025-09-05 08:33:36] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -54.170643-0.000746j
[2025-09-05 08:33:46] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -54.011791+0.000644j
[2025-09-05 08:33:56] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -54.130006-0.002587j
[2025-09-05 08:34:06] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -54.017727+0.000695j
[2025-09-05 08:34:17] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -54.110123+0.002291j
[2025-09-05 08:34:27] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -54.041259+0.002001j
[2025-09-05 08:34:37] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -54.001505+0.001037j
[2025-09-05 08:34:47] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -54.097179+0.000370j
[2025-09-05 08:34:57] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -54.043713+0.000918j
[2025-09-05 08:35:07] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -54.036917-0.000714j
[2025-09-05 08:35:18] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -54.006529+0.001191j
[2025-09-05 08:35:28] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -54.022772-0.001284j
[2025-09-05 08:35:38] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -54.117053-0.002954j
[2025-09-05 08:35:48] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -54.129321+0.000473j
[2025-09-05 08:35:58] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -54.184542-0.000690j
[2025-09-05 08:36:08] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -54.264823-0.002488j
[2025-09-05 08:36:18] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -54.192396+0.001163j
[2025-09-05 08:36:29] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -54.162945+0.000289j
[2025-09-05 08:36:39] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -54.124230+0.000887j
[2025-09-05 08:36:49] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -54.117457+0.000932j
[2025-09-05 08:36:59] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -54.052334+0.001986j
[2025-09-05 08:37:09] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -53.984286+0.000038j
[2025-09-05 08:37:19] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -53.953045-0.000475j
[2025-09-05 08:37:30] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -54.124559-0.001807j
[2025-09-05 08:37:40] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -54.051392-0.000872j
[2025-09-05 08:37:50] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -53.984110-0.002028j
[2025-09-05 08:38:00] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -54.027078+0.002473j
[2025-09-05 08:38:10] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -54.061639-0.000057j
[2025-09-05 08:38:20] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -53.955631-0.000830j
[2025-09-05 08:38:30] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -53.919437-0.001036j
[2025-09-05 08:38:41] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -54.082538+0.000314j
[2025-09-05 08:38:51] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -54.118872+0.000043j
[2025-09-05 08:39:01] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -54.089472+0.000665j
[2025-09-05 08:39:11] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -54.135939-0.002480j
[2025-09-05 08:39:21] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -54.061476-0.000172j
[2025-09-05 08:39:31] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -54.054479-0.000380j
[2025-09-05 08:39:42] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -54.030157-0.002006j
[2025-09-05 08:39:52] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -54.092164-0.000357j
[2025-09-05 08:40:02] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -54.106013-0.001393j
[2025-09-05 08:40:12] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -54.155061+0.001027j
[2025-09-05 08:40:22] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -54.211065+0.002425j
[2025-09-05 08:40:32] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -54.112594+0.000558j
[2025-09-05 08:40:42] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -54.134652-0.001960j
[2025-09-05 08:40:52] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -54.212438-0.002851j
[2025-09-05 08:41:02] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -54.179155+0.001720j
[2025-09-05 08:41:13] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -54.150840+0.000152j
[2025-09-05 08:41:23] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -54.137343+0.001592j
[2025-09-05 08:41:33] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -54.026141-0.000371j
[2025-09-05 08:41:43] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -54.213739+0.001087j
[2025-09-05 08:41:53] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -54.094384+0.000730j
[2025-09-05 08:42:03] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -54.032329+0.003426j
[2025-09-05 08:42:14] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -54.008152+0.000886j
[2025-09-05 08:42:24] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -54.081716+0.004843j
[2025-09-05 08:42:34] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -54.160828+0.001954j
[2025-09-05 08:42:34] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-05 08:42:44] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -54.138014+0.000264j
[2025-09-05 08:42:54] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -54.232016-0.000257j
[2025-09-05 08:43:04] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -54.145595-0.000746j
[2025-09-05 08:43:15] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -54.258649-0.001789j
[2025-09-05 08:43:25] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -54.195088+0.003319j
[2025-09-05 08:43:35] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -54.167933+0.000403j
[2025-09-05 08:43:45] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -54.104644+0.001676j
[2025-09-05 08:43:55] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -54.128509-0.002708j
[2025-09-05 08:44:05] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -54.077111-0.001158j
[2025-09-05 08:44:15] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -54.118571+0.002766j
[2025-09-05 08:44:26] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -54.118553-0.001226j
[2025-09-05 08:44:36] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -54.223735-0.001799j
[2025-09-05 08:44:46] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -54.085171+0.001375j
[2025-09-05 08:44:56] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -54.065321+0.001746j
[2025-09-05 08:45:06] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -53.997383+0.000426j
[2025-09-05 08:45:16] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -54.070003+0.001890j
[2025-09-05 08:45:27] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -54.243783+0.002450j
[2025-09-05 08:45:37] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -54.186507+0.001226j
[2025-09-05 08:45:47] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -54.197965-0.000231j
[2025-09-05 08:45:57] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -54.104037-0.000186j
[2025-09-05 08:46:07] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -54.221754+0.001003j
[2025-09-05 08:46:17] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -54.233530+0.003941j
[2025-09-05 08:46:28] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -54.084078-0.001871j
[2025-09-05 08:46:38] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -54.028453+0.002849j
[2025-09-05 08:46:48] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -54.116896+0.002046j
[2025-09-05 08:46:58] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -54.182283+0.002297j
[2025-09-05 08:47:08] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -54.186988+0.001842j
[2025-09-05 08:47:18] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -54.149758-0.000066j
[2025-09-05 08:47:29] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -54.254725-0.002615j
[2025-09-05 08:47:39] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -54.303790-0.002073j
[2025-09-05 08:47:49] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -54.187608-0.004222j
[2025-09-05 08:47:59] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -54.216754-0.000762j
[2025-09-05 08:48:09] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -54.213721-0.000280j
[2025-09-05 08:48:19] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -54.286001-0.001450j
[2025-09-05 08:48:30] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -54.330595-0.000013j
[2025-09-05 08:48:40] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -54.347514+0.001222j
[2025-09-05 08:48:50] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -54.273760+0.000036j
[2025-09-05 08:49:00] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -54.191189+0.002487j
[2025-09-05 08:49:10] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -54.239786-0.000086j
[2025-09-05 08:49:20] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -54.208708+0.000600j
[2025-09-05 08:49:31] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -54.124094+0.001823j
[2025-09-05 08:49:41] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -54.053062+0.001937j
[2025-09-05 08:49:51] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -54.193765+0.001150j
[2025-09-05 08:50:01] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -54.150968-0.002704j
[2025-09-05 08:50:11] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -54.073732-0.000232j
[2025-09-05 08:50:21] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -54.084455+0.000085j
[2025-09-05 08:50:32] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -54.084785+0.003269j
[2025-09-05 08:50:42] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -54.200683+0.004609j
[2025-09-05 08:50:52] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -54.082993+0.002158j
[2025-09-05 08:51:02] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -54.125363+0.002241j
[2025-09-05 08:51:12] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -54.184804+0.002268j
[2025-09-05 08:51:22] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -54.267079+0.002631j
[2025-09-05 08:51:33] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -54.211053-0.000291j
[2025-09-05 08:51:43] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -54.138292-0.000258j
[2025-09-05 08:51:53] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -54.137794+0.001175j
[2025-09-05 08:52:03] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -53.927108+0.001079j
[2025-09-05 08:52:13] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -54.014718-0.002027j
[2025-09-05 08:52:23] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -54.037843-0.000529j
[2025-09-05 08:52:34] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -54.219573+0.002545j
[2025-09-05 08:52:44] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -54.277592+0.001253j
[2025-09-05 08:52:54] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -54.209664+0.002229j
[2025-09-05 08:53:04] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -54.101889+0.000854j
[2025-09-05 08:53:14] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -54.101493-0.001899j
[2025-09-05 08:53:24] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -54.198514+0.000414j
[2025-09-05 08:53:35] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -54.104879-0.001202j
[2025-09-05 08:53:45] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -54.032278-0.002078j
[2025-09-05 08:53:55] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -53.937392-0.001171j
[2025-09-05 08:54:05] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -54.096821+0.000685j
[2025-09-05 08:54:15] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -54.159090-0.001890j
[2025-09-05 08:54:25] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -54.189594-0.001273j
[2025-09-05 08:54:35] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -54.177626-0.002878j
[2025-09-05 08:54:46] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -54.193917+0.002142j
[2025-09-05 08:54:56] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -54.067280-0.001113j
[2025-09-05 08:55:06] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -54.151904-0.000181j
[2025-09-05 08:55:16] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -54.015060-0.001936j
[2025-09-05 08:55:26] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -54.048187+0.002422j
[2025-09-05 08:55:36] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -54.032990+0.000268j
[2025-09-05 08:55:47] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -54.045573-0.000851j
[2025-09-05 08:55:57] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -54.114400+0.001061j
[2025-09-05 08:56:07] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -54.029519-0.002013j
[2025-09-05 08:56:17] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -54.133900-0.000784j
[2025-09-05 08:56:27] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -54.171538-0.003437j
[2025-09-05 08:56:37] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -54.170652-0.000136j
[2025-09-05 08:56:47] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -54.188258-0.002081j
[2025-09-05 08:56:58] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -54.222354-0.000281j
[2025-09-05 08:57:08] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -54.201562+0.000714j
[2025-09-05 08:57:18] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -54.136702-0.000906j
[2025-09-05 08:57:28] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -54.252289+0.000226j
[2025-09-05 08:57:38] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -54.239728+0.002342j
[2025-09-05 08:57:48] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -54.198115+0.000192j
[2025-09-05 08:57:59] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -54.237888+0.000099j
[2025-09-05 08:58:09] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -54.158394+0.001725j
[2025-09-05 08:58:19] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -54.302875-0.001578j
[2025-09-05 08:58:29] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -54.250751+0.001035j
[2025-09-05 08:58:39] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -54.103084-0.000301j
[2025-09-05 08:58:49] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -54.070441+0.000069j
[2025-09-05 08:59:00] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -54.126107-0.000519j
[2025-09-05 08:59:10] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -54.052188-0.000880j
[2025-09-05 08:59:20] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -54.081539-0.000302j
[2025-09-05 08:59:30] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -54.250358-0.000558j
[2025-09-05 08:59:40] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -54.325138-0.003827j
[2025-09-05 08:59:50] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -54.146656-0.000505j
[2025-09-05 09:00:01] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -54.144125-0.000683j
[2025-09-05 09:00:07] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -53.989871-0.000907j
[2025-09-05 09:00:12] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -53.990968+0.002025j
[2025-09-05 09:00:12] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-05 09:00:16] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -54.093825-0.000002j
[2025-09-05 09:00:21] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -54.050770+0.000233j
[2025-09-05 09:00:26] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -54.063678+0.000251j
[2025-09-05 09:00:30] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -54.100379+0.001632j
[2025-09-05 09:00:35] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -54.076686+0.001586j
[2025-09-05 09:00:39] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -53.946194-0.002929j
[2025-09-05 09:00:44] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -54.127660+0.000790j
[2025-09-05 09:00:48] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -54.219926-0.000170j
[2025-09-05 09:00:53] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -54.115410+0.002453j
[2025-09-05 09:00:58] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -54.146127+0.000291j
[2025-09-05 09:01:02] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -54.125449-0.001042j
[2025-09-05 09:01:07] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -54.105646-0.000098j
[2025-09-05 09:01:11] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -54.179768-0.002029j
[2025-09-05 09:01:16] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -54.029411+0.001236j
[2025-09-05 09:01:20] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -54.091167-0.000299j
[2025-09-05 09:01:25] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -54.074329-0.001959j
[2025-09-05 09:01:29] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -54.193553+0.000354j
[2025-09-05 09:01:34] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -54.166505-0.001347j
[2025-09-05 09:01:39] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -54.098784+0.000519j
[2025-09-05 09:01:43] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -54.169381-0.003695j
[2025-09-05 09:01:48] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -54.209793-0.000933j
[2025-09-05 09:01:52] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -54.231197-0.000650j
[2025-09-05 09:01:57] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -54.311645-0.000255j
[2025-09-05 09:02:01] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -54.400614-0.001097j
[2025-09-05 09:02:06] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -54.162074-0.001278j
[2025-09-05 09:02:11] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -54.013460-0.002998j
[2025-09-05 09:02:15] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -54.086375+0.000521j
[2025-09-05 09:02:20] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -54.094829+0.001944j
[2025-09-05 09:02:24] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -54.208217-0.000825j
[2025-09-05 09:02:29] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -54.154622-0.000086j
[2025-09-05 09:02:33] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -54.071208+0.000267j
[2025-09-05 09:02:38] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -53.972123-0.002545j
[2025-09-05 09:02:43] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -54.033534-0.001933j
[2025-09-05 09:02:47] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -54.051024+0.001173j
[2025-09-05 09:02:52] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -53.991827-0.004432j
[2025-09-05 09:02:56] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -54.232417-0.002763j
[2025-09-05 09:03:01] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -54.076914+0.000980j
[2025-09-05 09:03:05] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -54.184701-0.000177j
[2025-09-05 09:03:10] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -54.178703+0.002162j
[2025-09-05 09:03:15] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -54.170806-0.000346j
[2025-09-05 09:03:19] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -54.139622+0.001472j
[2025-09-05 09:03:24] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -54.011336-0.000471j
[2025-09-05 09:03:28] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -54.133053+0.001265j
[2025-09-05 09:03:33] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -54.052325-0.003480j
[2025-09-05 09:03:37] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -54.238460-0.000710j
[2025-09-05 09:03:42] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -54.143251-0.001690j
[2025-09-05 09:03:46] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -54.211104+0.001940j
[2025-09-05 09:03:51] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -54.236973-0.002131j
[2025-09-05 09:03:56] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -54.146385-0.001121j
[2025-09-05 09:04:00] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -54.081930+0.000613j
[2025-09-05 09:04:05] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -54.078247-0.001976j
[2025-09-05 09:04:09] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -54.163998+0.000523j
[2025-09-05 09:04:14] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -54.163264+0.001390j
[2025-09-05 09:04:18] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -54.178393+0.000493j
[2025-09-05 09:04:23] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -54.120961+0.001776j
[2025-09-05 09:04:28] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -54.154196+0.001488j
[2025-09-05 09:04:32] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -54.271956+0.000490j
[2025-09-05 09:04:37] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -54.258115-0.002495j
[2025-09-05 09:04:41] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -54.229677-0.000353j
[2025-09-05 09:04:46] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -54.128921+0.003239j
[2025-09-05 09:04:50] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -54.285059-0.000058j
[2025-09-05 09:04:55] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -54.283403-0.002112j
[2025-09-05 09:05:00] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -54.080003+0.002367j
[2025-09-05 09:05:04] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -54.098548+0.001631j
[2025-09-05 09:05:09] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -54.128572+0.001357j
[2025-09-05 09:05:13] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -54.057532-0.000375j
[2025-09-05 09:05:18] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -54.238972+0.000609j
[2025-09-05 09:05:22] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -54.134775-0.000903j
[2025-09-05 09:05:27] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -54.169760-0.003633j
[2025-09-05 09:05:32] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -54.154143-0.001209j
[2025-09-05 09:05:36] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -54.078553+0.000921j
[2025-09-05 09:05:41] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -54.038205+0.002446j
[2025-09-05 09:05:45] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -54.006014-0.000993j
[2025-09-05 09:05:50] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -54.090412+0.003039j
[2025-09-05 09:05:54] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -54.185089-0.000078j
[2025-09-05 09:05:59] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -54.186089+0.001275j
[2025-09-05 09:06:04] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -54.220765+0.000037j
[2025-09-05 09:06:08] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -54.249964+0.002762j
[2025-09-05 09:06:13] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -54.192788-0.002475j
[2025-09-05 09:06:17] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -54.229312+0.000086j
[2025-09-05 09:06:22] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -54.071483+0.000147j
[2025-09-05 09:06:26] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -54.194880-0.001373j
[2025-09-05 09:06:31] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -54.240591-0.000821j
[2025-09-05 09:06:35] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -54.274692+0.000995j
[2025-09-05 09:06:40] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -54.262444+0.000558j
[2025-09-05 09:06:45] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -54.240408-0.000706j
[2025-09-05 09:06:49] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -54.081411+0.001836j
[2025-09-05 09:06:54] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -54.021035-0.001980j
[2025-09-05 09:06:58] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -54.020109+0.000906j
[2025-09-05 09:07:03] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -54.149123-0.000886j
[2025-09-05 09:07:07] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -54.157169+0.000384j
[2025-09-05 09:07:12] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -54.130321-0.000927j
[2025-09-05 09:07:17] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -54.121550-0.001176j
[2025-09-05 09:07:21] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -54.059670+0.000551j
[2025-09-05 09:07:26] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -54.128492+0.002116j
[2025-09-05 09:07:30] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -54.186810-0.000271j
[2025-09-05 09:07:35] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -54.246238+0.000491j
[2025-09-05 09:07:39] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -54.231297-0.000947j
[2025-09-05 09:07:44] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -54.197277-0.000934j
[2025-09-05 09:07:49] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -54.154716-0.000276j
[2025-09-05 09:07:53] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -54.270423-0.000192j
[2025-09-05 09:07:58] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -54.169356-0.001262j
[2025-09-05 09:08:02] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -54.191863+0.000343j
[2025-09-05 09:08:07] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -54.137234-0.001298j
[2025-09-05 09:08:12] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -53.962789-0.000275j
[2025-09-05 09:08:12] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-05 09:08:16] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -54.089669+0.002944j
[2025-09-05 09:08:21] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -54.091619+0.003060j
[2025-09-05 09:08:25] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -54.102875-0.000918j
[2025-09-05 09:08:30] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -53.978908-0.001224j
[2025-09-05 09:08:35] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -54.190898+0.001179j
[2025-09-05 09:08:39] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -54.167896+0.000686j
[2025-09-05 09:08:44] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -54.062936-0.000144j
[2025-09-05 09:08:48] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -54.134600+0.002087j
[2025-09-05 09:08:53] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -54.158357-0.000509j
[2025-09-05 09:08:57] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -54.163941+0.002308j
[2025-09-05 09:09:02] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -54.007621-0.000286j
[2025-09-05 09:09:06] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -53.995991-0.002454j
[2025-09-05 09:09:11] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -54.055292+0.003687j
[2025-09-05 09:09:16] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -53.989620-0.001623j
[2025-09-05 09:09:20] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -54.017589-0.002070j
[2025-09-05 09:09:25] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -54.118347-0.002285j
[2025-09-05 09:09:29] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -54.153908-0.000589j
[2025-09-05 09:09:34] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -54.116278-0.001680j
[2025-09-05 09:09:38] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -54.069667-0.001732j
[2025-09-05 09:09:43] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -54.188926-0.000567j
[2025-09-05 09:09:48] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -54.081110-0.003046j
[2025-09-05 09:09:52] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -54.167210+0.001166j
[2025-09-05 09:09:57] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -54.256291+0.002033j
[2025-09-05 09:10:01] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -54.047627-0.001671j
[2025-09-05 09:10:06] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -54.123635-0.000093j
[2025-09-05 09:10:10] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -54.038407-0.000950j
[2025-09-05 09:10:15] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -53.917374+0.000581j
[2025-09-05 09:10:20] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -54.012968-0.001309j
[2025-09-05 09:10:24] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -54.096348+0.002220j
[2025-09-05 09:10:29] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -54.102311+0.000626j
[2025-09-05 09:10:33] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -54.123094+0.000370j
[2025-09-05 09:10:38] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -54.112535-0.002255j
[2025-09-05 09:10:42] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -54.067723+0.000770j
[2025-09-05 09:10:47] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -54.194649+0.000554j
[2025-09-05 09:10:51] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -54.242188-0.003605j
[2025-09-05 09:10:56] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -54.114627-0.001020j
[2025-09-05 09:11:01] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -54.094771-0.000184j
[2025-09-05 09:11:05] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -54.056904-0.002168j
[2025-09-05 09:11:10] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -54.083987-0.003047j
[2025-09-05 09:11:14] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -54.080528-0.000001j
[2025-09-05 09:11:19] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -54.094699+0.003075j
[2025-09-05 09:11:23] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -54.174517-0.001958j
[2025-09-05 09:11:28] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -54.127996-0.000197j
[2025-09-05 09:11:33] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -54.088017-0.003522j
[2025-09-05 09:11:37] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -54.115042+0.003231j
[2025-09-05 09:11:42] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -54.190840+0.003065j
[2025-09-05 09:11:46] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -54.085211-0.000025j
[2025-09-05 09:11:51] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -54.029702+0.000726j
[2025-09-05 09:11:55] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -54.043734-0.000595j
[2025-09-05 09:12:00] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -54.019318-0.000530j
[2025-09-05 09:12:05] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -54.147904-0.001351j
[2025-09-05 09:12:09] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -54.045209-0.000656j
[2025-09-05 09:12:14] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -54.093627+0.002337j
[2025-09-05 09:12:18] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -54.114154-0.000743j
[2025-09-05 09:12:23] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -54.064849+0.002391j
[2025-09-05 09:12:27] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -54.180900+0.003117j
[2025-09-05 09:12:32] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -54.153142-0.000483j
[2025-09-05 09:12:37] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -54.136036-0.000488j
[2025-09-05 09:12:41] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -54.123276+0.000718j
[2025-09-05 09:12:46] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -54.152059+0.000984j
[2025-09-05 09:12:50] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -54.241206-0.001812j
[2025-09-05 09:12:55] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -54.257867-0.002347j
[2025-09-05 09:12:59] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -54.183297+0.002623j
[2025-09-05 09:13:04] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -54.162892-0.000527j
[2025-09-05 09:13:08] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -54.017730+0.000225j
[2025-09-05 09:13:13] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -54.094921-0.001124j
[2025-09-05 09:13:18] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -54.150589-0.001349j
[2025-09-05 09:13:22] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -54.093812+0.002378j
[2025-09-05 09:13:27] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -54.229734+0.001177j
[2025-09-05 09:13:31] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -54.208304-0.001340j
[2025-09-05 09:13:36] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -54.245034-0.001670j
[2025-09-05 09:13:40] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -54.242972-0.001416j
[2025-09-05 09:13:45] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -54.270431-0.000631j
[2025-09-05 09:13:50] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -54.021930+0.000346j
[2025-09-05 09:13:54] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -53.917262+0.001605j
[2025-09-05 09:13:59] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -54.093281+0.000943j
[2025-09-05 09:14:03] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -53.982763+0.003621j
[2025-09-05 09:14:08] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -53.992500-0.000344j
[2025-09-05 09:14:12] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -54.071559-0.001127j
[2025-09-05 09:14:17] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -54.047472+0.004361j
[2025-09-05 09:14:22] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -54.167574+0.003589j
[2025-09-05 09:14:26] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -54.099889-0.003395j
[2025-09-05 09:14:31] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -54.043040+0.000844j
[2025-09-05 09:14:35] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -54.133485+0.002095j
[2025-09-05 09:14:40] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -54.116837+0.004137j
[2025-09-05 09:14:44] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -54.107412+0.002490j
[2025-09-05 09:14:49] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -54.004305-0.002591j
[2025-09-05 09:14:54] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -54.119805+0.000835j
[2025-09-05 09:14:58] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -54.094312-0.000875j
[2025-09-05 09:15:03] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -54.294157+0.000056j
[2025-09-05 09:15:07] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -54.202439+0.000336j
[2025-09-05 09:15:12] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -54.227887+0.001843j
[2025-09-05 09:15:16] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -54.226009+0.001272j
[2025-09-05 09:15:21] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -54.191401+0.000221j
[2025-09-05 09:15:25] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -54.232485-0.000611j
[2025-09-05 09:15:30] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -54.230535-0.001718j
[2025-09-05 09:15:35] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -54.201401+0.001362j
[2025-09-05 09:15:39] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -54.235363+0.001803j
[2025-09-05 09:15:44] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -54.160281-0.000486j
[2025-09-05 09:15:48] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -54.171220+0.001283j
[2025-09-05 09:15:53] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -54.185619+0.006361j
[2025-09-05 09:15:57] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -54.172861+0.002563j
[2025-09-05 09:16:02] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -54.184955+0.001903j
[2025-09-05 09:16:07] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -54.221882-0.000175j
[2025-09-05 09:16:11] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -54.170027+0.001119j
[2025-09-05 09:16:11] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-05 09:16:16] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -54.118637+0.001685j
[2025-09-05 09:16:20] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -54.210230+0.001936j
[2025-09-05 09:16:25] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -54.116164-0.000373j
[2025-09-05 09:16:29] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -54.062051-0.002471j
[2025-09-05 09:16:34] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -54.057841+0.002158j
[2025-09-05 09:16:39] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -54.041295+0.000249j
[2025-09-05 09:16:43] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -54.151697+0.000244j
[2025-09-05 09:16:48] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -54.207506+0.001464j
[2025-09-05 09:16:52] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -54.155093+0.001354j
[2025-09-05 09:16:57] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -54.004492-0.005517j
[2025-09-05 09:17:01] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -54.102173-0.003827j
[2025-09-05 09:17:06] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -54.000531-0.000440j
[2025-09-05 09:17:11] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -54.020236+0.000655j
[2025-09-05 09:17:15] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -54.047866+0.002422j
[2025-09-05 09:17:20] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -54.165740-0.000432j
[2025-09-05 09:17:24] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -54.007666+0.003008j
[2025-09-05 09:17:29] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -54.031275-0.000808j
[2025-09-05 09:17:33] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -53.999900-0.002407j
[2025-09-05 09:17:38] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -54.046783-0.002479j
[2025-09-05 09:17:43] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -54.108319+0.000164j
[2025-09-05 09:17:47] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -54.080008-0.000361j
[2025-09-05 09:17:52] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -53.995707+0.000844j
[2025-09-05 09:17:56] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -54.065366-0.000820j
[2025-09-05 09:18:01] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -54.122715-0.000026j
[2025-09-05 09:18:05] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -54.124291-0.002025j
[2025-09-05 09:18:10] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -54.107287+0.000304j
[2025-09-05 09:18:15] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -54.084911+0.000334j
[2025-09-05 09:18:19] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -54.102147+0.002151j
[2025-09-05 09:18:24] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -54.101054+0.000631j
[2025-09-05 09:18:28] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -54.008591+0.002759j
[2025-09-05 09:18:33] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -54.024392+0.000914j
[2025-09-05 09:18:37] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -54.064782+0.000108j
[2025-09-05 09:18:42] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -54.017905-0.000744j
[2025-09-05 09:18:46] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -53.944531-0.000887j
[2025-09-05 09:18:51] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -54.057171+0.001586j
[2025-09-05 09:18:56] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -54.094847+0.000255j
[2025-09-05 09:19:00] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -54.167073+0.000988j
[2025-09-05 09:19:05] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -54.132046+0.000433j
[2025-09-05 09:19:09] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -54.224806+0.000213j
[2025-09-05 09:19:14] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -54.178247+0.000047j
[2025-09-05 09:19:18] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -54.115766-0.000165j
[2025-09-05 09:19:23] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -54.142518+0.002352j
[2025-09-05 09:19:28] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -54.237470-0.002046j
[2025-09-05 09:19:32] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -54.207048-0.000039j
[2025-09-05 09:19:37] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -54.160474+0.001766j
[2025-09-05 09:19:41] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -54.224606-0.003029j
[2025-09-05 09:19:46] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -54.201857+0.001456j
[2025-09-05 09:19:50] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -54.083370-0.000544j
[2025-09-05 09:19:55] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -54.152085+0.000793j
[2025-09-05 09:20:00] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -54.153435-0.000180j
[2025-09-05 09:20:04] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -54.240498+0.003002j
[2025-09-05 09:20:09] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -54.197862-0.000013j
[2025-09-05 09:20:13] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -53.974152-0.001679j
[2025-09-05 09:20:18] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -54.009518-0.004747j
[2025-09-05 09:20:22] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -54.015398-0.001475j
[2025-09-05 09:20:27] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -54.234867-0.000697j
[2025-09-05 09:20:31] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -54.207046-0.004152j
[2025-09-05 09:20:36] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -54.304099-0.000343j
[2025-09-05 09:20:41] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -54.187267+0.001875j
[2025-09-05 09:20:45] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -54.198839-0.001069j
[2025-09-05 09:20:50] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -54.215389+0.000964j
[2025-09-05 09:20:54] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -54.221353+0.001580j
[2025-09-05 09:20:59] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -54.166564-0.001713j
[2025-09-05 09:21:03] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -54.079776+0.000655j
[2025-09-05 09:21:08] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -54.097029+0.001040j
[2025-09-05 09:21:13] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -54.067538-0.002951j
[2025-09-05 09:21:17] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -53.931354+0.003425j
[2025-09-05 09:21:22] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -54.101312-0.001506j
[2025-09-05 09:21:26] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -54.137849+0.001428j
[2025-09-05 09:21:31] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -54.072153+0.000501j
[2025-09-05 09:21:35] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -54.149530-0.001730j
[2025-09-05 09:21:40] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -54.206168+0.000800j
[2025-09-05 09:21:45] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -54.246682-0.000886j
[2025-09-05 09:21:49] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -54.256683-0.001501j
[2025-09-05 09:21:54] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -54.250313-0.001242j
[2025-09-05 09:21:59] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -54.119778-0.000231j
[2025-09-05 09:22:03] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -54.118140+0.000395j
[2025-09-05 09:22:08] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -54.204811+0.000470j
[2025-09-05 09:22:12] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -54.365546+0.000852j
[2025-09-05 09:22:17] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -54.320231+0.002058j
[2025-09-05 09:22:21] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -54.121742+0.003175j
[2025-09-05 09:22:26] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -54.048203+0.001520j
[2025-09-05 09:22:31] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -54.169049-0.000402j
[2025-09-05 09:22:35] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -54.155396+0.003081j
[2025-09-05 09:22:40] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -54.060315-0.001735j
[2025-09-05 09:22:44] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -54.116818+0.001959j
[2025-09-05 09:22:49] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -54.152336-0.001208j
[2025-09-05 09:22:53] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -54.220251+0.000850j
[2025-09-05 09:22:58] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -54.172322-0.001793j
[2025-09-05 09:23:03] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -54.180191+0.001824j
[2025-09-05 09:23:07] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -54.158882-0.004463j
[2025-09-05 09:23:12] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -54.068172-0.002628j
[2025-09-05 09:23:16] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -53.990684-0.001493j
[2025-09-05 09:23:21] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -54.026301+0.002355j
[2025-09-05 09:23:25] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -54.027097-0.001834j
[2025-09-05 09:23:30] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -54.138929+0.001701j
[2025-09-05 09:23:34] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -54.102594-0.001471j
[2025-09-05 09:23:39] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -54.100664+0.000870j
[2025-09-05 09:23:44] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -54.197807-0.001482j
[2025-09-05 09:23:48] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -54.093494-0.000652j
[2025-09-05 09:23:53] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -54.097588+0.001293j
[2025-09-05 09:23:57] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -54.137447-0.002054j
[2025-09-05 09:24:02] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -54.122993+0.000191j
[2025-09-05 09:24:06] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -54.077142+0.000610j
[2025-09-05 09:24:11] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -54.174736-0.001882j
[2025-09-05 09:24:11] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-05 09:24:11] ✅ Training completed | Restarts: 2
[2025-09-05 09:24:11] ============================================================
[2025-09-05 09:24:11] Training completed | Runtime: 8946.1s
[2025-09-05 09:24:13] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-05 09:24:13] ============================================================
