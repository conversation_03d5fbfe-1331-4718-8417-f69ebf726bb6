[2025-09-06 13:45:26] 使用checkpoint文件: results/L=4/J2=0.05/J1=0.06/training/checkpoints/checkpoint_iter_000735.pkl
[2025-09-06 13:45:40] ✓ 从checkpoint加载参数: 735
[2025-09-06 13:45:40]   - 能量: -52.500860-0.000081j ± 0.080769
[2025-09-06 13:45:40] ================================================================================
[2025-09-06 13:45:40] 加载量子态: L=4, J2=0.05, J1=0.06, checkpoint=checkpoint_iter_000735
[2025-09-06 13:45:40] 使用采样数目: 1048576
[2025-09-06 13:45:40] 设置样本数为: 1048576
[2025-09-06 13:45:40] 开始生成共享样本集...
[2025-09-06 13:50:05] 样本生成完成,耗时: 265.157 秒
[2025-09-06 13:50:05] ================================================================================
[2025-09-06 13:50:05] 开始计算自旋结构因子...
[2025-09-06 13:50:05] 初始化操作符缓存...
[2025-09-06 13:50:05] 预构建所有自旋相关操作符...
[2025-09-06 13:50:05] 开始计算自旋相关函数...
[2025-09-06 13:50:19] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 13.348s
[2025-09-06 13:50:37] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 18.400s
[2025-09-06 13:50:51] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 14.029s
[2025-09-06 13:51:05] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 14.060s
[2025-09-06 13:51:19] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 14.063s
[2025-09-06 13:51:33] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 14.056s
[2025-09-06 13:51:47] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 14.039s
[2025-09-06 13:52:01] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 14.056s
[2025-09-06 13:52:15] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 14.024s
[2025-09-06 13:52:30] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 14.053s
[2025-09-06 13:52:44] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 14.024s
[2025-09-06 13:52:58] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 14.048s
[2025-09-06 13:53:12] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 14.023s
[2025-09-06 13:53:26] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 14.072s
[2025-09-06 13:53:40] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 14.058s
[2025-09-06 13:53:54] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 14.033s
[2025-09-06 13:54:08] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 14.048s
[2025-09-06 13:54:22] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 14.057s
[2025-09-06 13:54:36] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 14.018s
[2025-09-06 13:54:50] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 14.058s
[2025-09-06 13:55:04] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 14.044s
[2025-09-06 13:55:18] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 14.055s
[2025-09-06 13:55:32] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 14.030s
[2025-09-06 13:55:46] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 14.058s
[2025-09-06 13:56:00] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 14.027s
[2025-09-06 13:56:14] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 14.062s
[2025-09-06 13:56:28] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 14.038s
[2025-09-06 13:56:42] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 14.059s
[2025-09-06 13:56:56] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 14.023s
[2025-09-06 13:57:10] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 14.026s
[2025-09-06 13:57:24] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 14.029s
[2025-09-06 13:57:39] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 14.058s
[2025-09-06 13:57:53] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 14.059s
[2025-09-06 13:58:07] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 14.054s
[2025-09-06 13:58:21] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 14.030s
[2025-09-06 13:58:35] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 14.063s
[2025-09-06 13:58:49] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 14.032s
[2025-09-06 13:59:03] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 14.071s
[2025-09-06 13:59:17] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 14.021s
[2025-09-06 13:59:31] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 14.056s
[2025-09-06 13:59:45] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 14.025s
[2025-09-06 13:59:59] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 14.030s
[2025-09-06 14:00:13] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 14.042s
[2025-09-06 14:00:27] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 14.055s
[2025-09-06 14:00:41] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 14.030s
[2025-09-06 14:00:55] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 14.028s
[2025-09-06 14:01:09] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 14.058s
[2025-09-06 14:01:23] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 14.029s
[2025-09-06 14:01:37] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 14.062s
[2025-09-06 14:01:51] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 14.063s
[2025-09-06 14:02:05] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 14.032s
[2025-09-06 14:02:20] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 14.058s
[2025-09-06 14:02:34] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 14.031s
[2025-09-06 14:02:48] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 14.061s
[2025-09-06 14:03:02] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 14.030s
[2025-09-06 14:03:16] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 14.049s
[2025-09-06 14:03:30] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 14.024s
[2025-09-06 14:03:44] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 14.029s
[2025-09-06 14:03:58] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 14.030s
[2025-09-06 14:04:12] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 14.055s
[2025-09-06 14:04:26] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 14.045s
[2025-09-06 14:04:40] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 14.029s
[2025-09-06 14:04:54] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 14.067s
[2025-09-06 14:05:08] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 14.029s
[2025-09-06 14:05:08] 自旋相关函数计算完成,总耗时 902.71 秒
[2025-09-06 14:05:10] 计算傅里叶变换...
[2025-09-06 14:05:13] 自旋结构因子计算完成
[2025-09-06 14:05:14] 自旋相关函数平均误差: 0.000665
