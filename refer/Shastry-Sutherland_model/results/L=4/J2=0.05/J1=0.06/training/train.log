[2025-09-04 18:59:26] ✓ 从checkpoint恢复: results/L=4/J2=0.05/J1=0.05/training/checkpoints/final_GCNN.pkl
[2025-09-04 18:59:26]   - 迭代次数: final
[2025-09-04 18:59:26]   - 能量: -52.160097+0.001458j ± 0.041241
[2025-09-04 18:59:26]   - 时间戳: 2025-09-04T15:04:42.590402+08:00
[2025-09-04 18:59:39] ✓ 变分状态参数已从checkpoint恢复
[2025-09-04 18:59:39] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-04 18:59:39] ==================================================
[2025-09-04 18:59:39] GCNN for Shastry-Sutherland Model
[2025-09-04 18:59:39] ==================================================
[2025-09-04 18:59:39] System parameters:
[2025-09-04 18:59:39]   - System size: L=4, N=64
[2025-09-04 18:59:39]   - System parameters: J1=0.06, J2=0.05, Q=0.95
[2025-09-04 18:59:39] --------------------------------------------------
[2025-09-04 18:59:39] Model parameters:
[2025-09-04 18:59:39]   - Number of layers = 4
[2025-09-04 18:59:39]   - Number of features = 4
[2025-09-04 18:59:39]   - Total parameters = 12572
[2025-09-04 18:59:39] --------------------------------------------------
[2025-09-04 18:59:39] Training parameters:
[2025-09-04 18:59:39]   - Learning rate: 0.015
[2025-09-04 18:59:39]   - Total iterations: 1050
[2025-09-04 18:59:39]   - Annealing cycles: 3
[2025-09-04 18:59:39]   - Initial period: 150
[2025-09-04 18:59:39]   - Period multiplier: 2.0
[2025-09-04 18:59:39]   - Temperature range: 0.0-1.0
[2025-09-04 18:59:39]   - Samples: 4096
[2025-09-04 18:59:39]   - Discarded samples: 0
[2025-09-04 18:59:39]   - Chunk size: 2048
[2025-09-04 18:59:39]   - Diagonal shift: 0.2
[2025-09-04 18:59:39]   - Gradient clipping: 1.0
[2025-09-04 18:59:39]   - Checkpoint enabled: interval=105
[2025-09-04 18:59:39]   - Checkpoint directory: results/L=4/J2=0.05/J1=0.06/training/checkpoints
[2025-09-04 18:59:39] --------------------------------------------------
[2025-09-04 18:59:39] Device status:
[2025-09-04 18:59:39]   - Devices model: NVIDIA H200 NVL
[2025-09-04 18:59:39]   - Number of devices: 1
[2025-09-04 18:59:39]   - Sharding: True
[2025-09-04 18:59:39] ============================================================
[2025-09-04 19:00:24] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -52.561906+0.000330j
[2025-09-04 19:00:53] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -52.604427-0.005238j
[2025-09-04 19:01:03] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -52.550399+0.003188j
[2025-09-04 19:01:13] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -52.614744+0.002726j
[2025-09-04 19:01:23] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -52.623519-0.000946j
[2025-09-04 19:01:33] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -52.642519-0.000145j
[2025-09-04 19:01:44] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -52.564467+0.001782j
[2025-09-04 19:01:54] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -52.684784+0.003661j
[2025-09-04 19:02:04] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -52.550147+0.001641j
[2025-09-04 19:02:14] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -52.511377+0.001459j
[2025-09-04 19:02:24] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -52.660690-0.003477j
[2025-09-04 19:02:34] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -52.667564-0.000643j
[2025-09-04 19:02:45] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -52.497521+0.003979j
[2025-09-04 19:02:55] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -52.443374+0.000322j
[2025-09-04 19:03:05] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -52.448968+0.009679j
[2025-09-04 19:03:15] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -52.396339-0.003478j
[2025-09-04 19:03:25] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -52.481940-0.000002j
[2025-09-04 19:03:35] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -52.590402+0.006179j
[2025-09-04 19:03:46] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -52.387430+0.000814j
[2025-09-04 19:03:56] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -52.425734+0.001414j
[2025-09-04 19:04:06] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -52.561111+0.002039j
[2025-09-04 19:04:16] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -52.547189-0.001648j
[2025-09-04 19:04:26] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -52.569719-0.002518j
[2025-09-04 19:04:36] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -52.471639-0.001991j
[2025-09-04 19:04:47] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -52.467731+0.001306j
[2025-09-04 19:04:57] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -52.548187-0.000028j
[2025-09-04 19:05:07] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -52.602220+0.003337j
[2025-09-04 19:05:17] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -52.656508+0.001801j
[2025-09-04 19:05:27] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -52.593994-0.001598j
[2025-09-04 19:05:37] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -52.559507+0.000384j
[2025-09-04 19:05:48] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -52.516314+0.007201j
[2025-09-04 19:05:58] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -52.747421-0.001662j
[2025-09-04 19:06:08] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -52.626465+0.000234j
[2025-09-04 19:06:18] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -52.559956-0.001260j
[2025-09-04 19:06:28] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -52.550680-0.002090j
[2025-09-04 19:06:38] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -52.528468+0.001319j
[2025-09-04 19:06:48] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -52.507140-0.000809j
[2025-09-04 19:06:59] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -52.562500+0.002843j
[2025-09-04 19:07:09] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -52.431730-0.003976j
[2025-09-04 19:07:19] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -52.465468-0.002344j
[2025-09-04 19:07:29] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -52.512433+0.005459j
[2025-09-04 19:07:39] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -52.471243-0.001900j
[2025-09-04 19:07:50] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -52.435819-0.002140j
[2025-09-04 19:08:00] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -52.294819-0.004065j
[2025-09-04 19:08:10] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -52.429130-0.003879j
[2025-09-04 19:08:20] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -52.471991+0.000431j
[2025-09-04 19:08:30] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -52.612289-0.000463j
[2025-09-04 19:08:41] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -52.570116-0.001520j
[2025-09-04 19:08:51] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -52.500662+0.000042j
[2025-09-04 19:09:01] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -52.580280-0.002269j
[2025-09-04 19:09:11] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -52.579662+0.000176j
[2025-09-04 19:09:22] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -52.507260-0.003801j
[2025-09-04 19:09:32] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -52.418133+0.007650j
[2025-09-04 19:09:42] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -52.523856+0.000813j
[2025-09-04 19:09:52] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -52.548711+0.002005j
[2025-09-04 19:10:02] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -52.561711-0.000910j
[2025-09-04 19:10:13] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -52.577338+0.001799j
[2025-09-04 19:10:23] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -52.599870+0.000426j
[2025-09-04 19:10:33] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -52.675059-0.006684j
[2025-09-04 19:10:43] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -52.689425-0.006230j
[2025-09-04 19:10:53] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -52.640609-0.000215j
[2025-09-04 19:11:04] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -52.425821-0.001136j
[2025-09-04 19:11:14] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -52.485362-0.001163j
[2025-09-04 19:11:24] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -52.560933-0.000619j
[2025-09-04 19:11:34] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -52.619519+0.001872j
[2025-09-04 19:11:45] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -52.463761+0.004378j
[2025-09-04 19:11:55] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -52.460754+0.004937j
[2025-09-04 19:12:05] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -52.506439+0.001429j
[2025-09-04 19:12:15] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -52.516029+0.002314j
[2025-09-04 19:12:25] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -52.543938+0.001071j
[2025-09-04 19:12:36] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -52.610734+0.000980j
[2025-09-04 19:12:46] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -52.572793-0.001358j
[2025-09-04 19:12:56] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -52.554793-0.002632j
[2025-09-04 19:13:06] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -52.463078+0.003180j
[2025-09-04 19:13:16] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -52.526743+0.001125j
[2025-09-04 19:13:27] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -52.513455-0.001773j
[2025-09-04 19:13:37] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -52.477533+0.000925j
[2025-09-04 19:13:47] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -52.418605+0.000622j
[2025-09-04 19:13:57] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -52.526415+0.000520j
[2025-09-04 19:14:08] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -52.429789+0.003281j
[2025-09-04 19:14:18] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -52.436500-0.004462j
[2025-09-04 19:14:28] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -52.517906+0.000764j
[2025-09-04 19:14:38] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -52.647143-0.002226j
[2025-09-04 19:14:48] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -52.665717-0.000999j
[2025-09-04 19:14:59] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -52.657505-0.001654j
[2025-09-04 19:15:09] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -52.501530-0.001671j
[2025-09-04 19:15:19] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -52.488582-0.006234j
[2025-09-04 19:15:29] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -52.490450-0.003109j
[2025-09-04 19:15:39] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -52.488270-0.003663j
[2025-09-04 19:15:50] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -52.451089-0.008406j
[2025-09-04 19:16:00] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -52.494318+0.003224j
[2025-09-04 19:16:10] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -52.424022+0.000419j
[2025-09-04 19:16:20] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -52.527503-0.004005j
[2025-09-04 19:16:31] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -52.515008-0.002133j
[2025-09-04 19:16:41] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -52.570431+0.001525j
[2025-09-04 19:16:51] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -52.455066+0.000813j
[2025-09-04 19:17:01] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -52.614307-0.001434j
[2025-09-04 19:17:10] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -52.562985+0.002365j
[2025-09-04 19:17:21] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -52.440557+0.000097j
[2025-09-04 19:17:31] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -52.450228-0.004598j
[2025-09-04 19:17:41] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -52.389901-0.002564j
[2025-09-04 19:17:51] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -52.465534+0.002405j
[2025-09-04 19:18:02] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -52.410600+0.001139j
[2025-09-04 19:18:12] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -52.518606+0.002038j
[2025-09-04 19:18:22] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -52.483940-0.003931j
[2025-09-04 19:18:22] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-04 19:18:32] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -52.541745-0.001420j
[2025-09-04 19:18:42] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -52.563341+0.003575j
[2025-09-04 19:18:53] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -52.613731+0.004185j
[2025-09-04 19:19:03] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -52.543493+0.000419j
[2025-09-04 19:19:13] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -52.575649-0.000171j
[2025-09-04 19:19:23] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -52.436570+0.004726j
[2025-09-04 19:19:34] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -52.437060+0.002754j
[2025-09-04 19:19:44] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -52.514864+0.002284j
[2025-09-04 19:19:54] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -52.458845+0.003386j
[2025-09-04 19:20:04] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -52.543418-0.000337j
[2025-09-04 19:20:14] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -52.437343-0.004939j
[2025-09-04 19:20:25] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -52.415644+0.002785j
[2025-09-04 19:20:35] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -52.405534-0.002075j
[2025-09-04 19:20:45] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -52.531105+0.000688j
[2025-09-04 19:20:55] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -52.381301-0.002397j
[2025-09-04 19:21:05] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -52.416267+0.003313j
[2025-09-04 19:21:16] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -52.454277+0.008926j
[2025-09-04 19:21:26] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -52.511234-0.003158j
[2025-09-04 19:21:36] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -52.509272-0.000506j
[2025-09-04 19:21:46] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -52.441711-0.005136j
[2025-09-04 19:21:57] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -52.467534-0.003165j
[2025-09-04 19:22:07] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -52.546066+0.002043j
[2025-09-04 19:22:17] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -52.583539-0.000746j
[2025-09-04 19:22:27] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -52.395582+0.000769j
[2025-09-04 19:22:37] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -52.551939+0.001547j
[2025-09-04 19:22:48] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -52.683109+0.000052j
[2025-09-04 19:22:58] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -52.600197+0.002019j
[2025-09-04 19:23:08] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -52.647015+0.003295j
[2025-09-04 19:23:18] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -52.521093+0.004173j
[2025-09-04 19:23:28] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -52.504116+0.004459j
[2025-09-04 19:23:39] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -52.686404+0.002256j
[2025-09-04 19:23:49] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -52.500537-0.001554j
[2025-09-04 19:23:59] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -52.555042-0.004123j
[2025-09-04 19:24:09] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -52.585375-0.006581j
[2025-09-04 19:24:19] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -52.519974+0.000417j
[2025-09-04 19:24:30] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -52.583215+0.000439j
[2025-09-04 19:24:40] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -52.618846+0.000545j
[2025-09-04 19:24:50] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -52.440670-0.006012j
[2025-09-04 19:25:00] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -52.594704-0.000054j
[2025-09-04 19:25:11] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -52.520469-0.000694j
[2025-09-04 19:25:21] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -52.531115+0.002394j
[2025-09-04 19:25:31] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -52.555261+0.002233j
[2025-09-04 19:25:41] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -52.512452+0.002285j
[2025-09-04 19:25:51] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -52.500486+0.001822j
[2025-09-04 19:26:02] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -52.497268-0.000866j
[2025-09-04 19:26:02] RESTART #1 | Period: 300
[2025-09-04 19:26:12] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -52.525381-0.000076j
[2025-09-04 19:26:22] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -52.549022-0.001436j
[2025-09-04 19:26:32] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -52.602429+0.001565j
[2025-09-04 19:26:42] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -52.557413-0.004050j
[2025-09-04 19:26:53] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -52.650114+0.001105j
[2025-09-04 19:27:03] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -52.474679+0.000076j
[2025-09-04 19:27:13] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -52.534913-0.000705j
[2025-09-04 19:27:23] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -52.652099+0.002309j
[2025-09-04 19:27:33] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -52.581871-0.000774j
[2025-09-04 19:27:44] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -52.522106-0.001688j
[2025-09-04 19:27:54] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -52.548019-0.001904j
[2025-09-04 19:28:04] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -52.474099+0.003889j
[2025-09-04 19:28:14] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -52.524751-0.007802j
[2025-09-04 19:28:25] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -52.464156+0.001307j
[2025-09-04 19:28:35] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -52.496692-0.005125j
[2025-09-04 19:28:45] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -52.388060+0.005612j
[2025-09-04 19:28:55] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -52.526502+0.000418j
[2025-09-04 19:29:05] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -52.435297+0.000690j
[2025-09-04 19:29:16] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -52.583542-0.003115j
[2025-09-04 19:29:26] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -52.639269+0.000579j
[2025-09-04 19:29:36] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -52.529223-0.001497j
[2025-09-04 19:29:46] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -52.542110-0.002393j
[2025-09-04 19:29:56] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -52.654506-0.005780j
[2025-09-04 19:30:07] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -52.657979+0.005217j
[2025-09-04 19:30:17] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -52.534936+0.002262j
[2025-09-04 19:30:27] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -52.645141-0.002642j
[2025-09-04 19:30:37] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -52.696763-0.002083j
[2025-09-04 19:30:47] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -52.548717+0.000456j
[2025-09-04 19:30:58] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -52.458907+0.004881j
[2025-09-04 19:31:08] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -52.582940-0.003570j
[2025-09-04 19:31:18] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -52.534476+0.000933j
[2025-09-04 19:31:28] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -52.559902-0.001478j
[2025-09-04 19:31:39] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -52.405328-0.002272j
[2025-09-04 19:31:49] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -52.456371+0.000963j
[2025-09-04 19:31:59] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -52.437332-0.000650j
[2025-09-04 19:32:09] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -52.297064+0.001334j
[2025-09-04 19:32:19] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -52.512886-0.000455j
[2025-09-04 19:32:30] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -52.533452+0.000000j
[2025-09-04 19:32:40] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -52.589677-0.003189j
[2025-09-04 19:32:50] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -52.586781+0.002370j
[2025-09-04 19:33:00] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -52.576537-0.001229j
[2025-09-04 19:33:10] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -52.524419+0.002334j
[2025-09-04 19:33:21] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -52.531125+0.003075j
[2025-09-04 19:33:31] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -52.527359+0.005623j
[2025-09-04 19:33:41] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -52.473856-0.002716j
[2025-09-04 19:33:51] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -52.581170-0.000443j
[2025-09-04 19:34:02] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -52.635179+0.000865j
[2025-09-04 19:34:12] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -52.577645-0.004127j
[2025-09-04 19:34:22] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -52.500052-0.003691j
[2025-09-04 19:34:32] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -52.489070-0.004519j
[2025-09-04 19:34:42] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -52.496197+0.003438j
[2025-09-04 19:34:53] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -52.567442+0.001839j
[2025-09-04 19:35:03] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -52.658252-0.001560j
[2025-09-04 19:35:13] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -52.578067-0.003819j
[2025-09-04 19:35:23] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -52.661823-0.003133j
[2025-09-04 19:35:33] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -52.653301-0.001216j
[2025-09-04 19:35:44] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -52.591613-0.001013j
[2025-09-04 19:35:54] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -52.656669+0.000290j
[2025-09-04 19:36:04] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -52.666149+0.000557j
[2025-09-04 19:36:14] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -52.657323-0.003522j
[2025-09-04 19:36:14] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-04 19:36:24] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -52.575854+0.002359j
[2025-09-04 19:36:35] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -52.409942-0.001680j
[2025-09-04 19:36:45] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -52.553038+0.005280j
[2025-09-04 19:36:55] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -52.541877-0.000451j
[2025-09-04 19:37:05] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -52.505157+0.005698j
[2025-09-04 19:37:15] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -52.567396-0.004011j
[2025-09-04 19:37:26] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -52.498995-0.007668j
[2025-09-04 19:37:36] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -52.462590-0.004262j
[2025-09-04 19:37:46] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -52.486120-0.001641j
[2025-09-04 19:37:56] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -52.522100+0.001605j
[2025-09-04 19:38:06] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -52.542352+0.002959j
[2025-09-04 19:38:17] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -52.573393-0.002232j
[2025-09-04 19:38:27] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -52.522073+0.002573j
[2025-09-04 19:38:37] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -52.491729+0.003627j
[2025-09-04 19:38:47] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -52.545516-0.001081j
[2025-09-04 19:38:58] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -52.474319+0.000120j
[2025-09-04 19:39:08] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -52.492853+0.000703j
[2025-09-04 19:39:18] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -52.491156+0.000156j
[2025-09-04 19:39:28] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -52.446045-0.001795j
[2025-09-04 19:39:38] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -52.431268-0.003202j
[2025-09-04 19:39:49] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -52.538453-0.000101j
[2025-09-04 19:39:59] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -52.441341+0.004521j
[2025-09-04 19:40:09] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -52.675520+0.001412j
[2025-09-04 19:40:19] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -52.616923+0.002374j
[2025-09-04 19:40:30] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -52.598705-0.002096j
[2025-09-04 19:40:40] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -52.568761+0.000728j
[2025-09-04 19:40:50] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -52.604646-0.001060j
[2025-09-04 19:41:00] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -52.590250+0.000030j
[2025-09-04 19:41:10] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -52.583189-0.001724j
[2025-09-04 19:41:21] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -52.607904+0.002589j
[2025-09-04 19:41:31] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -52.781927+0.003765j
[2025-09-04 19:41:41] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -52.650570-0.006527j
[2025-09-04 19:41:51] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -52.636738-0.003354j
[2025-09-04 19:42:01] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -52.655600-0.004132j
[2025-09-04 19:42:12] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -52.591213-0.002608j
[2025-09-04 19:42:22] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -52.536492-0.001722j
[2025-09-04 19:42:32] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -52.434911-0.000813j
[2025-09-04 19:42:42] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -52.281737-0.002837j
[2025-09-04 19:42:53] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -52.414426+0.001182j
[2025-09-04 19:43:03] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -52.399145+0.001292j
[2025-09-04 19:43:13] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -52.453037-0.001533j
[2025-09-04 19:43:23] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -52.307401+0.000627j
[2025-09-04 19:43:33] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -52.320405+0.007254j
[2025-09-04 19:43:44] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -52.472809-0.001406j
[2025-09-04 19:43:54] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -52.452894-0.002473j
[2025-09-04 19:44:04] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -52.583414+0.001160j
[2025-09-04 19:44:14] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -52.612665-0.001160j
[2025-09-04 19:44:24] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -52.478617+0.002097j
[2025-09-04 19:44:35] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -52.484679-0.002160j
[2025-09-04 19:44:45] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -52.568573-0.001742j
[2025-09-04 19:44:55] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -52.542584+0.005512j
[2025-09-04 19:45:05] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -52.651742-0.007071j
[2025-09-04 19:45:15] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -52.651055+0.002949j
[2025-09-04 19:45:26] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -52.670131-0.001906j
[2025-09-04 19:45:36] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -52.672309+0.002195j
[2025-09-04 19:45:46] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -52.627886-0.002063j
[2025-09-04 19:45:56] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -52.538744-0.002546j
[2025-09-04 19:46:07] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -52.566104+0.000066j
[2025-09-04 19:46:17] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -52.587724+0.000507j
[2025-09-04 19:46:27] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -52.543930+0.001913j
[2025-09-04 19:46:37] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -52.566207+0.003188j
[2025-09-04 19:46:47] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -52.671521-0.000779j
[2025-09-04 19:46:58] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -52.502947-0.000256j
[2025-09-04 19:47:08] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -52.598588-0.000628j
[2025-09-04 19:47:18] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -52.671213+0.009867j
[2025-09-04 19:47:28] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -52.613673-0.000253j
[2025-09-04 19:47:38] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -52.604613-0.000184j
[2025-09-04 19:47:49] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -52.540766-0.001960j
[2025-09-04 19:47:59] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -52.495545+0.001154j
[2025-09-04 19:48:09] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -52.530783-0.001892j
[2025-09-04 19:48:19] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -52.450206+0.003281j
[2025-09-04 19:48:30] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -52.596999+0.000954j
[2025-09-04 19:48:40] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -52.563656+0.007059j
[2025-09-04 19:48:50] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -52.564232+0.006986j
[2025-09-04 19:49:00] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -52.461818-0.001450j
[2025-09-04 19:49:10] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -52.530622+0.003249j
[2025-09-04 19:49:21] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -52.543436-0.000131j
[2025-09-04 19:49:31] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -52.539165-0.002636j
[2025-09-04 19:49:41] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -52.643021+0.000737j
[2025-09-04 19:49:51] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -52.511592+0.001363j
[2025-09-04 19:50:01] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -52.519262-0.002015j
[2025-09-04 19:50:12] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -52.561646-0.004715j
[2025-09-04 19:50:22] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -52.587107+0.001894j
[2025-09-04 19:50:32] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -52.551913-0.000760j
[2025-09-04 19:50:42] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -52.634308+0.005488j
[2025-09-04 19:50:53] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -52.578264-0.000908j
[2025-09-04 19:51:03] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -52.494867+0.002975j
[2025-09-04 19:51:13] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -52.633081+0.002417j
[2025-09-04 19:51:23] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -52.551194-0.001012j
[2025-09-04 19:51:33] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -52.597202+0.004900j
[2025-09-04 19:51:44] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -52.610213+0.002873j
[2025-09-04 19:51:54] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -52.537336-0.001162j
[2025-09-04 19:52:04] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -52.503697-0.001170j
[2025-09-04 19:52:14] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -52.377656-0.000544j
[2025-09-04 19:52:24] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -52.509213-0.001934j
[2025-09-04 19:52:35] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -52.677952+0.000036j
[2025-09-04 19:52:45] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -52.544214-0.001773j
[2025-09-04 19:52:55] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -52.394899+0.002958j
[2025-09-04 19:53:05] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -52.394872+0.001129j
[2025-09-04 19:53:16] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -52.455629-0.000921j
[2025-09-04 19:53:26] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -52.422080-0.001071j
[2025-09-04 19:53:36] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -52.569589-0.002559j
[2025-09-04 19:53:46] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -52.385287-0.002463j
[2025-09-04 19:53:56] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -52.531907-0.002068j
[2025-09-04 19:54:07] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -52.602156-0.000570j
[2025-09-04 19:54:07] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-04 19:54:17] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -52.570063+0.003893j
[2025-09-04 19:54:27] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -52.415891+0.001520j
[2025-09-04 19:54:37] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -52.525755-0.000946j
[2025-09-04 19:54:47] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -52.488993+0.003453j
[2025-09-04 19:54:58] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -52.424706-0.002226j
[2025-09-04 19:55:08] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -52.572356+0.000739j
[2025-09-04 19:55:18] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -52.720035+0.001153j
[2025-09-04 19:55:28] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -52.597713-0.002183j
[2025-09-04 19:55:39] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -52.610170+0.000425j
[2025-09-04 19:55:49] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -52.566819-0.003545j
[2025-09-04 19:55:59] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -52.455748-0.002289j
[2025-09-04 19:56:10] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -52.548759+0.001376j
[2025-09-04 19:56:20] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -52.558438+0.001234j
[2025-09-04 19:56:30] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -52.532585-0.001726j
[2025-09-04 19:56:40] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -52.399498-0.000569j
[2025-09-04 19:56:50] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -52.338362+0.002124j
[2025-09-04 19:57:01] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -52.409504+0.002977j
[2025-09-04 19:57:11] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -52.569124-0.003919j
[2025-09-04 19:57:21] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -52.628792+0.000564j
[2025-09-04 19:57:32] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -52.645716-0.005231j
[2025-09-04 19:57:42] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -52.609127-0.000250j
[2025-09-04 19:57:52] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -52.569631-0.004250j
[2025-09-04 19:58:02] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -52.616343-0.002430j
[2025-09-04 19:58:12] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -52.522301+0.000835j
[2025-09-04 19:58:23] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -52.523090-0.004463j
[2025-09-04 19:58:33] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -52.741543-0.001406j
[2025-09-04 19:58:43] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -52.509370-0.001279j
[2025-09-04 19:58:53] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -52.543707+0.002645j
[2025-09-04 19:59:03] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -52.379819-0.004756j
[2025-09-04 19:59:14] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -52.422548+0.000909j
[2025-09-04 19:59:24] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -52.462222-0.000549j
[2025-09-04 19:59:34] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -52.486414+0.006576j
[2025-09-04 19:59:44] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -52.621849+0.002974j
[2025-09-04 19:59:55] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -52.629242-0.001352j
[2025-09-04 20:00:05] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -52.614251-0.000093j
[2025-09-04 20:00:15] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -52.481782-0.000092j
[2025-09-04 20:00:25] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -52.435272-0.004795j
[2025-09-04 20:00:35] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -52.412206+0.001339j
[2025-09-04 20:00:46] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -52.440857-0.000384j
[2025-09-04 20:00:56] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -52.424613+0.001751j
[2025-09-04 20:01:06] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -52.434443-0.003786j
[2025-09-04 20:01:16] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -52.429224-0.000018j
[2025-09-04 20:01:26] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -52.507113+0.001522j
[2025-09-04 20:01:37] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -52.480534+0.001571j
[2025-09-04 20:01:47] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -52.561165+0.001113j
[2025-09-04 20:01:57] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -52.589195+0.003739j
[2025-09-04 20:02:07] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -52.684874+0.005196j
[2025-09-04 20:02:17] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -52.581830-0.002251j
[2025-09-04 20:02:28] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -52.528743+0.002014j
[2025-09-04 20:02:38] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -52.532192-0.001336j
[2025-09-04 20:02:48] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -52.407041+0.001185j
[2025-09-04 20:02:58] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -52.314588-0.001150j
[2025-09-04 20:03:08] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -52.430564-0.000818j
[2025-09-04 20:03:19] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -52.494474-0.000695j
[2025-09-04 20:03:29] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -52.406642+0.002474j
[2025-09-04 20:03:39] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -52.489284+0.001557j
[2025-09-04 20:03:49] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -52.500167+0.001192j
[2025-09-04 20:03:59] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -52.407871+0.000539j
[2025-09-04 20:04:10] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -52.603391-0.004039j
[2025-09-04 20:04:20] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -52.518316+0.001362j
[2025-09-04 20:04:30] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -52.453862-0.002077j
[2025-09-04 20:04:40] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -52.515535-0.003387j
[2025-09-04 20:04:51] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -52.538117-0.001595j
[2025-09-04 20:05:01] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -52.567869+0.002767j
[2025-09-04 20:05:11] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -52.508668+0.000062j
[2025-09-04 20:05:21] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -52.580404-0.000574j
[2025-09-04 20:05:31] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -52.526967+0.002124j
[2025-09-04 20:05:42] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -52.495025-0.001784j
[2025-09-04 20:05:52] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -52.397079+0.000646j
[2025-09-04 20:06:02] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -52.336873+0.003276j
[2025-09-04 20:06:12] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -52.402428+0.002854j
[2025-09-04 20:06:23] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -52.617241+0.000669j
[2025-09-04 20:06:33] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -52.569682-0.000520j
[2025-09-04 20:06:43] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -52.528141-0.003277j
[2025-09-04 20:06:53] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -52.406854+0.001749j
[2025-09-04 20:07:03] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -52.463107+0.005091j
[2025-09-04 20:07:14] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -52.555350+0.002368j
[2025-09-04 20:07:24] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -52.537197-0.001695j
[2025-09-04 20:07:34] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -52.488994-0.001638j
[2025-09-04 20:07:44] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -52.517989-0.002896j
[2025-09-04 20:07:54] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -52.571954+0.005782j
[2025-09-04 20:08:05] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -52.656548-0.000152j
[2025-09-04 20:08:15] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -52.552775-0.001968j
[2025-09-04 20:08:25] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -52.549766-0.000370j
[2025-09-04 20:08:35] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -52.506173+0.001625j
[2025-09-04 20:08:45] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -52.527893+0.002021j
[2025-09-04 20:08:56] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -52.537319+0.003390j
[2025-09-04 20:09:06] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -52.472315+0.004425j
[2025-09-04 20:09:16] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -52.503109-0.002311j
[2025-09-04 20:09:26] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -52.578312-0.003133j
[2025-09-04 20:09:37] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -52.677420-0.000260j
[2025-09-04 20:09:47] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -52.588270+0.005294j
[2025-09-04 20:09:57] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -52.617657+0.001258j
[2025-09-04 20:10:07] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -52.668531+0.002894j
[2025-09-04 20:10:18] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -52.590364+0.000659j
[2025-09-04 20:10:28] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -52.605046+0.000428j
[2025-09-04 20:10:38] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -52.611833+0.000155j
[2025-09-04 20:10:48] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -52.628494+0.002560j
[2025-09-04 20:10:59] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -52.512906+0.000728j
[2025-09-04 20:11:09] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -52.680640+0.005955j
[2025-09-04 20:11:19] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -52.674630-0.000290j
[2025-09-04 20:11:29] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -52.515813-0.002941j
[2025-09-04 20:11:39] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -52.697163-0.002154j
[2025-09-04 20:11:50] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -52.664519-0.000423j
[2025-09-04 20:12:00] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -52.554520+0.006881j
[2025-09-04 20:12:00] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-04 20:12:10] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -52.601563+0.002757j
[2025-09-04 20:12:20] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -52.590266+0.005433j
[2025-09-04 20:12:30] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -52.507485+0.005976j
[2025-09-04 20:12:41] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -52.391417+0.002188j
[2025-09-04 20:12:51] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -52.411440+0.002903j
[2025-09-04 20:13:01] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -52.424787+0.007697j
[2025-09-04 20:13:11] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -52.505086-0.000097j
[2025-09-04 20:13:21] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -52.452190+0.001237j
[2025-09-04 20:13:32] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -52.418500+0.003370j
[2025-09-04 20:13:42] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -52.392796-0.005025j
[2025-09-04 20:13:52] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -52.356485-0.001757j
[2025-09-04 20:14:02] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -52.434658+0.001083j
[2025-09-04 20:14:13] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -52.490167-0.001794j
[2025-09-04 20:14:23] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -52.545699+0.001356j
[2025-09-04 20:14:33] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -52.462588-0.003667j
[2025-09-04 20:14:43] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -52.412110+0.001051j
[2025-09-04 20:14:53] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -52.459652-0.002463j
[2025-09-04 20:15:04] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -52.470033+0.000288j
[2025-09-04 20:15:14] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -52.632928-0.000382j
[2025-09-04 20:15:24] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -52.567338+0.004281j
[2025-09-04 20:15:34] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -52.637631+0.001643j
[2025-09-04 20:15:44] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -52.631942+0.002254j
[2025-09-04 20:15:55] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -52.611273-0.000782j
[2025-09-04 20:16:05] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -52.523582+0.003201j
[2025-09-04 20:16:15] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -52.450484-0.002870j
[2025-09-04 20:16:25] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -52.522368+0.001453j
[2025-09-04 20:16:36] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -52.441564-0.002738j
[2025-09-04 20:16:46] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -52.474106-0.002997j
[2025-09-04 20:16:56] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -52.464354-0.001140j
[2025-09-04 20:17:06] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -52.496004+0.003176j
[2025-09-04 20:17:06] RESTART #2 | Period: 600
[2025-09-04 20:17:16] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -52.427080-0.001865j
[2025-09-04 20:17:27] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -52.523778-0.000968j
[2025-09-04 20:17:37] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -52.461862-0.003791j
[2025-09-04 20:17:47] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -52.527252-0.000691j
[2025-09-04 20:17:57] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -52.467183+0.003736j
[2025-09-04 20:18:08] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -52.539575-0.000699j
[2025-09-04 20:18:18] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -52.490853+0.001384j
[2025-09-04 20:18:28] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -52.542275-0.002555j
[2025-09-04 20:18:38] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -52.517846-0.000505j
[2025-09-04 20:18:48] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -52.523887+0.000154j
[2025-09-04 20:18:59] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -52.494784-0.000461j
[2025-09-04 20:19:09] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -52.584145-0.000935j
[2025-09-04 20:19:19] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -52.614899+0.000854j
[2025-09-04 20:19:29] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -52.628592+0.000497j
[2025-09-04 20:19:39] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -52.505415+0.004748j
[2025-09-04 20:19:50] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -52.476467+0.000818j
[2025-09-04 20:20:00] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -52.635840+0.000542j
[2025-09-04 20:20:10] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -52.503112-0.001003j
[2025-09-04 20:20:20] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -52.612793+0.000374j
[2025-09-04 20:20:31] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -52.480720-0.003218j
[2025-09-04 20:20:41] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -52.593726+0.001141j
[2025-09-04 20:20:51] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -52.527693-0.001967j
[2025-09-04 20:21:01] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -52.540859+0.001071j
[2025-09-04 20:21:11] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -52.468847-0.002890j
[2025-09-04 20:21:22] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -52.608912+0.004242j
[2025-09-04 20:21:32] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -52.552957+0.000344j
[2025-09-04 20:21:42] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -52.500289+0.002855j
[2025-09-04 20:21:52] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -52.596245-0.006284j
[2025-09-04 20:22:02] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -52.530981-0.003574j
[2025-09-04 20:22:13] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -52.496316+0.002092j
[2025-09-04 20:22:23] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -52.602247+0.000799j
[2025-09-04 20:22:33] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -52.600787-0.003094j
[2025-09-04 20:22:43] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -52.511661+0.000018j
[2025-09-04 20:22:54] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -52.433341+0.002952j
[2025-09-04 20:23:04] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -52.426944+0.002165j
[2025-09-04 20:23:14] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -52.452148+0.000327j
[2025-09-04 20:23:24] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -52.495112-0.001600j
[2025-09-04 20:23:34] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -52.646030-0.001085j
[2025-09-04 20:23:45] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -52.480348-0.001762j
[2025-09-04 20:23:55] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -52.414767+0.001982j
[2025-09-04 20:24:05] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -52.484385+0.001920j
[2025-09-04 20:24:15] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -52.527193+0.003489j
[2025-09-04 20:24:25] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -52.249839+0.001603j
[2025-09-04 20:24:36] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -52.534550+0.000070j
[2025-09-04 20:24:46] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -52.379671-0.000553j
[2025-09-04 20:24:56] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -52.528065-0.001270j
[2025-09-04 20:25:06] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -52.548546+0.005253j
[2025-09-04 20:25:17] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -52.700429-0.002505j
[2025-09-04 20:25:27] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -52.604257+0.001629j
[2025-09-04 20:25:37] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -52.632617+0.000611j
[2025-09-04 20:25:47] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -52.520767+0.000181j
[2025-09-04 20:25:57] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -52.534750+0.002038j
[2025-09-04 20:26:08] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -52.495726-0.003460j
[2025-09-04 20:26:18] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -52.439505+0.004480j
[2025-09-04 20:26:28] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -52.383584+0.003158j
[2025-09-04 20:26:38] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -52.534560-0.001334j
[2025-09-04 20:26:48] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -52.621765+0.001635j
[2025-09-04 20:26:59] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -52.520880+0.000939j
[2025-09-04 20:27:09] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -52.564697+0.000603j
[2025-09-04 20:27:19] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -52.547471-0.000312j
[2025-09-04 20:27:29] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -52.553397-0.000450j
[2025-09-04 20:27:39] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -52.447562-0.000616j
[2025-09-04 20:27:50] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -52.502379+0.000764j
[2025-09-04 20:28:00] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -52.484325+0.001255j
[2025-09-04 20:28:10] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -52.433346-0.000569j
[2025-09-04 20:28:20] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -52.461793-0.004280j
[2025-09-04 20:28:30] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -52.491853-0.000741j
[2025-09-04 20:28:41] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -52.428933+0.007413j
[2025-09-04 20:28:51] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -52.421959-0.000461j
[2025-09-04 20:29:01] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -52.455808+0.000631j
[2025-09-04 20:29:11] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -52.504187+0.007337j
[2025-09-04 20:29:21] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -52.479655+0.001366j
[2025-09-04 20:29:32] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -52.476427+0.001745j
[2025-09-04 20:29:42] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -52.482497+0.002891j
[2025-09-04 20:29:52] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -52.495370+0.002240j
[2025-09-04 20:29:52] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-04 20:30:02] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -52.437032-0.001981j
[2025-09-04 20:30:13] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -52.464352+0.000297j
[2025-09-04 20:30:23] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -52.545517-0.000962j
[2025-09-04 20:30:33] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -52.567589-0.000808j
[2025-09-04 20:30:43] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -52.562521-0.000019j
[2025-09-04 20:30:53] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -52.576548+0.001242j
[2025-09-04 20:31:04] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -52.522829-0.004499j
[2025-09-04 20:31:14] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -52.654541+0.001687j
[2025-09-04 20:31:24] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -52.680005+0.002302j
[2025-09-04 20:31:34] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -52.555768-0.000809j
[2025-09-04 20:31:44] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -52.554472-0.003747j
[2025-09-04 20:31:55] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -52.499677-0.001191j
[2025-09-04 20:32:05] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -52.543195-0.002056j
[2025-09-04 20:32:15] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -52.580677-0.000638j
[2025-09-04 20:32:25] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -52.543022+0.001905j
[2025-09-04 20:32:36] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -52.484814-0.003988j
[2025-09-04 20:32:46] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -52.540944+0.002543j
[2025-09-04 20:32:56] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -52.604192-0.000581j
[2025-09-04 20:33:06] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -52.655460+0.004642j
[2025-09-04 20:33:16] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -52.614936-0.000751j
[2025-09-04 20:33:27] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -52.553352-0.000325j
[2025-09-04 20:33:37] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -52.535025-0.000945j
[2025-09-04 20:33:47] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -52.481517+0.006119j
[2025-09-04 20:33:57] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -52.483261+0.004700j
[2025-09-04 20:34:07] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -52.416479+0.003352j
[2025-09-04 20:34:18] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -52.594877-0.001364j
[2025-09-04 20:34:28] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -52.520785-0.006119j
[2025-09-04 20:34:38] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -52.422326+0.005069j
[2025-09-04 20:34:48] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -52.430516+0.000221j
[2025-09-04 20:34:59] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -52.455620+0.000849j
[2025-09-04 20:35:09] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -52.597717-0.004423j
[2025-09-04 20:35:19] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -52.529370+0.003305j
[2025-09-04 20:35:29] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -52.594427-0.004175j
[2025-09-04 20:35:39] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -52.585893-0.002964j
[2025-09-04 20:35:50] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -52.574238-0.001453j
[2025-09-04 20:36:00] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -52.600932-0.003696j
[2025-09-04 20:36:10] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -52.564593-0.005482j
[2025-09-04 20:36:20] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -52.574562-0.003898j
[2025-09-04 20:36:30] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -52.543995-0.004845j
[2025-09-04 20:36:41] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -52.502098-0.006371j
[2025-09-04 20:36:51] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -52.554318-0.004940j
[2025-09-04 20:37:01] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -52.484389-0.003448j
[2025-09-04 20:37:11] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -52.563222+0.002440j
[2025-09-04 20:37:22] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -52.435424+0.000880j
[2025-09-04 20:37:32] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -52.563582-0.001140j
[2025-09-04 20:37:42] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -52.629384+0.004009j
[2025-09-04 20:37:52] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -52.520369-0.001388j
[2025-09-04 20:38:02] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -52.578697-0.001358j
[2025-09-04 20:38:13] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -52.543161+0.006869j
[2025-09-04 20:38:23] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -52.543924+0.001904j
[2025-09-04 20:38:33] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -52.602758-0.001573j
[2025-09-04 20:38:43] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -52.612468+0.000310j
[2025-09-04 20:38:54] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -52.566896+0.000693j
[2025-09-04 20:39:04] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -52.555898+0.001337j
[2025-09-04 20:39:14] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -52.508405+0.002425j
[2025-09-04 20:39:24] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -52.498120-0.002347j
[2025-09-04 20:39:34] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -52.625441+0.002104j
[2025-09-04 20:39:45] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -52.519493+0.003528j
[2025-09-04 20:39:55] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -52.569478-0.006805j
[2025-09-04 20:40:05] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -52.490993-0.004390j
[2025-09-04 20:40:15] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -52.391982+0.003074j
[2025-09-04 20:40:25] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -52.483546+0.000375j
[2025-09-04 20:40:36] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -52.505419-0.006098j
[2025-09-04 20:40:46] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -52.569224-0.002199j
[2025-09-04 20:40:56] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -52.540524-0.001248j
[2025-09-04 20:41:06] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -52.667064-0.000382j
[2025-09-04 20:41:16] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -52.570993-0.000062j
[2025-09-04 20:41:27] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -52.550543-0.001135j
[2025-09-04 20:41:37] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -52.441884+0.001305j
[2025-09-04 20:41:47] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -52.461146-0.000827j
[2025-09-04 20:41:57] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -52.479773-0.000392j
[2025-09-04 20:42:07] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -52.512133+0.001315j
[2025-09-04 20:42:18] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -52.444660+0.002429j
[2025-09-04 20:42:28] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -52.471972-0.000764j
[2025-09-04 20:42:38] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -52.605805+0.003912j
[2025-09-04 20:42:48] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -52.572566-0.000022j
[2025-09-04 20:42:58] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -52.707689+0.000991j
[2025-09-04 20:43:09] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -52.579012+0.005552j
[2025-09-04 20:43:19] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -52.454744-0.003678j
[2025-09-04 20:43:29] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -52.508818+0.001027j
[2025-09-04 20:43:39] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -52.561774+0.003114j
[2025-09-04 20:43:50] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -52.567691+0.000622j
[2025-09-04 20:44:00] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -52.404781-0.001830j
[2025-09-04 20:44:10] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -52.438871+0.000798j
[2025-09-04 20:44:20] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -52.542747+0.005773j
[2025-09-04 20:44:30] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -52.401991+0.001088j
[2025-09-04 20:44:41] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -52.399832-0.002220j
[2025-09-04 20:44:51] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -52.299979-0.000697j
[2025-09-04 20:45:01] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -52.347981+0.007450j
[2025-09-04 20:45:11] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -52.468196-0.000696j
[2025-09-04 20:45:21] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -52.540201-0.002894j
[2025-09-04 20:45:32] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -52.523658+0.002704j
[2025-09-04 20:45:42] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -52.565930+0.001774j
[2025-09-04 20:45:52] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -52.532876-0.003748j
[2025-09-04 20:46:02] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -52.666182-0.001032j
[2025-09-04 20:46:12] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -52.682500-0.001348j
[2025-09-04 20:46:23] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -52.534342-0.003646j
[2025-09-04 20:46:33] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -52.449059+0.000650j
[2025-09-04 20:46:43] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -52.442269-0.000102j
[2025-09-04 20:46:53] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -52.623315-0.000506j
[2025-09-04 20:47:04] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -52.614653+0.000608j
[2025-09-04 20:47:14] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -52.679990+0.001637j
[2025-09-04 20:47:24] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -52.584698-0.000142j
[2025-09-04 20:47:34] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -52.573434+0.007355j
[2025-09-04 20:47:44] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -52.585569+0.000926j
[2025-09-04 20:47:44] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-04 20:47:55] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -52.551311+0.002037j
[2025-09-04 20:48:05] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -52.454075+0.001045j
[2025-09-04 20:48:15] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -52.458928+0.000865j
[2025-09-04 20:48:25] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -52.591962-0.002808j
[2025-09-04 20:48:35] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -52.567458-0.000789j
[2025-09-04 20:48:46] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -52.488161+0.005161j
[2025-09-04 20:48:56] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -52.469556-0.001507j
[2025-09-04 20:49:06] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -52.412645+0.001144j
[2025-09-04 20:49:16] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -52.512617-0.003828j
[2025-09-04 20:49:27] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -52.370500+0.001150j
[2025-09-04 20:49:37] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -52.510450+0.001807j
[2025-09-04 20:49:47] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -52.650270+0.005790j
[2025-09-04 20:49:57] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -52.663709-0.001685j
[2025-09-04 20:50:07] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -52.755112+0.000040j
[2025-09-04 20:50:18] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -52.610629-0.001380j
[2025-09-04 20:50:28] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -52.559793+0.004539j
[2025-09-04 20:50:38] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -52.572737+0.001352j
[2025-09-04 20:50:48] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -52.712899+0.000182j
[2025-09-04 20:50:58] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -52.593690-0.002934j
[2025-09-04 20:51:09] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -52.713681-0.001922j
[2025-09-04 20:51:19] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -52.530426-0.004617j
[2025-09-04 20:51:29] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -52.550079-0.001663j
[2025-09-04 20:51:39] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -52.682761-0.004678j
[2025-09-04 20:51:50] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -52.630509-0.001188j
[2025-09-04 20:52:00] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -52.625609+0.002333j
[2025-09-04 20:52:10] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -52.502701+0.004137j
[2025-09-04 20:52:20] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -52.552395+0.002137j
[2025-09-04 20:52:30] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -52.563300+0.003016j
[2025-09-04 20:52:41] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -52.573694+0.000601j
[2025-09-04 20:52:51] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -52.533207-0.003700j
[2025-09-04 20:53:01] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -52.605283+0.005057j
[2025-09-04 20:53:11] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -52.540640+0.000739j
[2025-09-04 20:53:21] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -52.529041+0.005127j
[2025-09-04 20:53:32] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -52.500210+0.002997j
[2025-09-04 20:53:42] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -52.540410+0.001268j
[2025-09-04 20:53:52] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -52.479838+0.001873j
[2025-09-04 20:54:02] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -52.454354-0.003218j
[2025-09-04 20:54:12] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -52.501729+0.002386j
[2025-09-04 20:54:23] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -52.719180-0.003133j
[2025-09-04 20:54:33] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -52.698946+0.002513j
[2025-09-04 20:54:43] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -52.566345+0.000851j
[2025-09-04 20:54:53] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -52.536388+0.001446j
[2025-09-04 20:55:04] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -52.581318+0.000997j
[2025-09-04 20:55:14] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -52.489520-0.004363j
[2025-09-04 20:55:24] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -52.579974+0.002196j
[2025-09-04 20:55:34] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -52.610995-0.000296j
[2025-09-04 20:55:44] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -52.559667+0.002519j
[2025-09-04 20:55:55] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -52.536420-0.002486j
[2025-09-04 20:56:05] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -52.649266+0.001316j
[2025-09-04 20:56:15] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -52.634029+0.000020j
[2025-09-04 20:56:25] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -52.578675+0.000848j
[2025-09-04 20:56:35] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -52.547286+0.002003j
[2025-09-04 20:56:46] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -52.531955+0.004080j
[2025-09-04 20:56:56] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -52.444836+0.000073j
[2025-09-04 20:57:06] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -52.603742-0.002497j
[2025-09-04 20:57:16] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -52.591756+0.000023j
[2025-09-04 20:57:26] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -52.592228-0.002063j
[2025-09-04 20:57:37] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -52.462484-0.002953j
[2025-09-04 20:57:47] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -52.424511-0.003310j
[2025-09-04 20:57:57] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -52.503476-0.003567j
[2025-09-04 20:58:07] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -52.584649-0.003202j
[2025-09-04 20:58:18] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -52.588792-0.004433j
[2025-09-04 20:58:28] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -52.373705+0.001843j
[2025-09-04 20:58:38] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -52.494168-0.000817j
[2025-09-04 20:58:48] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -52.529992+0.002152j
[2025-09-04 20:58:58] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -52.581786+0.000322j
[2025-09-04 20:59:09] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -52.674360+0.001384j
[2025-09-04 20:59:19] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -52.806709+0.002906j
[2025-09-04 20:59:29] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -52.707919+0.004673j
[2025-09-04 20:59:39] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -52.673355+0.001544j
[2025-09-04 20:59:50] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -52.615950-0.000726j
[2025-09-04 21:00:00] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -52.473512+0.000169j
[2025-09-04 21:00:10] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -52.442909-0.000444j
[2025-09-04 21:00:20] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -52.495535-0.000474j
[2025-09-04 21:00:30] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -52.601473-0.004456j
[2025-09-04 21:00:41] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -52.441301+0.003586j
[2025-09-04 21:00:51] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -52.442782-0.000478j
[2025-09-04 21:01:01] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -52.502245+0.001863j
[2025-09-04 21:01:11] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -52.454803-0.002006j
[2025-09-04 21:01:22] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -52.451557+0.000536j
[2025-09-04 21:01:32] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -52.414485-0.001890j
[2025-09-04 21:01:42] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -52.464368+0.001103j
[2025-09-04 21:01:52] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -52.519275-0.000449j
[2025-09-04 21:02:02] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -52.596740-0.004042j
[2025-09-04 21:02:13] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -52.606325-0.000498j
[2025-09-04 21:02:23] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -52.600525-0.001018j
[2025-09-04 21:02:33] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -52.712244-0.000464j
[2025-09-04 21:02:43] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -52.674128+0.000254j
[2025-09-04 21:02:53] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -52.645773-0.002487j
[2025-09-04 21:03:04] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -52.522166-0.001902j
[2025-09-04 21:03:14] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -52.537779+0.001257j
[2025-09-04 21:03:24] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -52.466284-0.004259j
[2025-09-04 21:03:34] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -52.450909-0.004874j
[2025-09-04 21:03:44] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -52.513539+0.012324j
[2025-09-04 21:03:55] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -52.386816+0.002010j
[2025-09-04 21:04:05] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -52.473891+0.001024j
[2025-09-04 21:04:15] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -52.568096-0.002012j
[2025-09-04 21:04:25] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -52.574568-0.004418j
[2025-09-04 21:04:36] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -52.436758-0.001025j
[2025-09-04 21:04:46] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -52.505189-0.000062j
[2025-09-04 21:04:56] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -52.552983-0.004002j
[2025-09-04 21:05:06] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -52.661097-0.051941j
[2025-09-04 21:05:16] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -52.722505+0.000775j
[2025-09-04 21:05:27] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -52.583940-0.000067j
[2025-09-04 21:05:37] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -52.500860-0.000081j
[2025-09-04 21:05:37] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-04 21:05:47] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -52.540977-0.000094j
[2025-09-04 21:05:57] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -52.585236-0.000812j
[2025-09-04 21:06:07] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -52.503025+0.006568j
[2025-09-04 21:06:18] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -52.554338+0.001713j
[2025-09-04 21:06:28] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -52.530715+0.000213j
[2025-09-04 21:06:38] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -52.654602-0.001244j
[2025-09-04 21:06:48] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -52.579889+0.001988j
[2025-09-04 21:06:58] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -52.590509-0.000992j
[2025-09-04 21:07:09] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -52.513211-0.001235j
[2025-09-04 21:07:19] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -52.498925-0.005331j
[2025-09-04 21:07:29] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -52.444391+0.001501j
[2025-09-04 21:07:39] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -52.595244-0.003464j
[2025-09-04 21:07:50] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -52.423530+0.001041j
[2025-09-04 21:08:00] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -52.350358+0.001806j
[2025-09-04 21:08:10] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -52.314248-0.004707j
[2025-09-04 21:08:20] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -52.360773+0.002259j
[2025-09-04 21:08:30] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -52.466933+0.002910j
[2025-09-04 21:08:41] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -52.445828-0.003006j
[2025-09-04 21:08:51] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -52.550131-0.006811j
[2025-09-04 21:09:01] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -52.509786-0.004108j
[2025-09-04 21:09:11] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -52.636023+0.003170j
[2025-09-04 21:09:21] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -52.577275-0.000215j
[2025-09-04 21:09:32] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -52.486431+0.000397j
[2025-09-04 21:09:42] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -52.452581+0.001717j
[2025-09-04 21:09:52] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -52.449501+0.002491j
[2025-09-04 21:10:02] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -52.587320-0.000844j
[2025-09-04 21:10:12] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -52.686980-0.003434j
[2025-09-04 21:10:23] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -52.587842-0.000381j
[2025-09-04 21:10:33] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -52.684569-0.005514j
[2025-09-04 21:10:43] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -52.623937+0.002862j
[2025-09-04 21:10:53] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -52.539091-0.001934j
[2025-09-04 21:11:03] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -52.503648-0.001032j
[2025-09-04 21:11:14] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -52.474037+0.001911j
[2025-09-04 21:11:24] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -52.550016+0.000251j
[2025-09-04 21:11:34] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -52.596537-0.003866j
[2025-09-04 21:11:44] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -52.649335+0.004992j
[2025-09-04 21:11:55] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -52.457935-0.001296j
[2025-09-04 21:12:05] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -52.541289-0.001022j
[2025-09-04 21:12:15] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -52.488535-0.001779j
[2025-09-04 21:12:25] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -52.556551-0.002501j
[2025-09-04 21:12:35] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -52.497801-0.000012j
[2025-09-04 21:12:46] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -52.628031+0.003669j
[2025-09-04 21:12:56] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -52.575685-0.000100j
[2025-09-04 21:13:06] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -52.636152-0.000916j
[2025-09-04 21:13:16] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -52.548505-0.003086j
[2025-09-04 21:13:26] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -52.611702+0.000655j
[2025-09-04 21:13:37] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -52.721908-0.000393j
[2025-09-04 21:13:47] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -52.689469-0.002873j
[2025-09-04 21:13:57] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -52.732365+0.004188j
[2025-09-04 21:14:07] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -52.695617+0.000744j
[2025-09-04 21:14:18] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -52.692769-0.002260j
[2025-09-04 21:14:28] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -52.605975+0.002250j
[2025-09-04 21:14:38] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -52.600512-0.005729j
[2025-09-04 21:14:48] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -52.465632+0.003704j
[2025-09-04 21:14:58] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -52.442981-0.003329j
[2025-09-04 21:15:09] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -52.654605+0.000265j
[2025-09-04 21:15:19] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -52.561014-0.001981j
[2025-09-04 21:15:29] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -52.504855+0.001752j
[2025-09-04 21:15:39] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -52.455200+0.000629j
[2025-09-04 21:15:49] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -52.479452-0.000057j
[2025-09-04 21:16:00] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -52.466309-0.007729j
[2025-09-04 21:16:10] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -52.451044-0.002870j
[2025-09-04 21:16:20] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -52.408315-0.000551j
[2025-09-04 21:16:30] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -52.417001+0.001649j
[2025-09-04 21:16:40] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -52.449801-0.001583j
[2025-09-04 21:16:51] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -52.484366-0.000259j
[2025-09-04 21:17:01] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -52.318610+0.002021j
[2025-09-04 21:17:11] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -52.520247+0.003776j
[2025-09-04 21:17:21] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -52.360886+0.002358j
[2025-09-04 21:17:31] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -52.391323-0.002824j
[2025-09-04 21:17:42] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -52.425211-0.001581j
[2025-09-04 21:17:52] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -52.608550+0.000764j
[2025-09-04 21:18:02] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -52.373705+0.003652j
[2025-09-04 21:18:13] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -52.489489-0.003532j
[2025-09-04 21:18:23] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -52.400205+0.000612j
[2025-09-04 21:18:33] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -52.456987-0.000010j
[2025-09-04 21:18:43] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -52.458422-0.002174j
[2025-09-04 21:18:53] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -52.425133+0.001188j
[2025-09-04 21:19:04] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -52.549461+0.003928j
[2025-09-04 21:19:14] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -52.577282+0.002410j
[2025-09-04 21:19:24] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -52.601364-0.003447j
[2025-09-04 21:19:34] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -52.594221-0.006269j
[2025-09-04 21:19:44] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -52.639866+0.003011j
[2025-09-04 21:19:55] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -52.545233+0.001237j
[2025-09-04 21:20:05] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -52.675226-0.000050j
[2025-09-04 21:20:15] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -52.741235-0.002104j
[2025-09-04 21:20:25] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -52.707489-0.000710j
[2025-09-04 21:20:35] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -52.706800+0.000082j
[2025-09-04 21:20:46] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -52.722681-0.000183j
[2025-09-04 21:20:56] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -52.647974+0.004321j
[2025-09-04 21:21:06] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -52.574619+0.005748j
[2025-09-04 21:21:16] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -52.572912+0.001153j
[2025-09-04 21:21:27] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -52.620710-0.001064j
[2025-09-04 21:21:37] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -52.566800+0.002052j
[2025-09-04 21:21:47] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -52.481791+0.002733j
[2025-09-04 21:21:57] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -52.550396+0.002000j
[2025-09-04 21:22:07] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -52.592354+0.001312j
[2025-09-04 21:22:18] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -52.535226+0.002433j
[2025-09-04 21:22:28] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -52.629606-0.002754j
[2025-09-04 21:22:38] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -52.567145+0.001125j
[2025-09-04 21:22:48] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -52.402138-0.003952j
[2025-09-04 21:22:58] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -52.370637+0.000147j
[2025-09-04 21:23:09] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -52.468819+0.005699j
[2025-09-04 21:23:19] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -52.505295-0.001388j
[2025-09-04 21:23:29] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -52.494460-0.001903j
[2025-09-04 21:23:29] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-04 21:23:39] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -52.521739-0.000994j
[2025-09-04 21:23:50] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -52.523153+0.002171j
[2025-09-04 21:24:00] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -52.466489+0.001149j
[2025-09-04 21:24:10] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -52.485895-0.001690j
[2025-09-04 21:24:20] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -52.547103+0.000203j
[2025-09-04 21:24:30] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -52.570162+0.000922j
[2025-09-04 21:24:41] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -52.533433-0.003129j
[2025-09-04 21:24:51] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -52.551247+0.001124j
[2025-09-04 21:25:01] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -52.528289+0.002900j
[2025-09-04 21:25:11] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -52.508600+0.004058j
[2025-09-04 21:25:21] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -52.665167-0.006579j
[2025-09-04 21:25:32] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -52.584517+0.006367j
[2025-09-04 21:25:42] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -52.584406-0.000766j
[2025-09-04 21:25:52] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -52.620767-0.000623j
[2025-09-04 21:26:02] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -52.488636-0.001499j
[2025-09-04 21:26:13] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -52.475306-0.000586j
[2025-09-04 21:26:23] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -52.422681-0.006410j
[2025-09-04 21:26:33] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -52.506305-0.003906j
[2025-09-04 21:26:43] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -52.535757+0.002228j
[2025-09-04 21:26:53] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -52.501819+0.000864j
[2025-09-04 21:27:04] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -52.375194+0.001703j
[2025-09-04 21:27:14] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -52.365305+0.001188j
[2025-09-04 21:27:24] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -52.460984+0.002704j
[2025-09-04 21:27:34] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -52.569259+0.000187j
[2025-09-04 21:27:44] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -52.616475+0.002892j
[2025-09-04 21:27:55] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -52.696136+0.001217j
[2025-09-04 21:28:05] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -52.670858-0.003060j
[2025-09-04 21:28:15] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -52.678721+0.002548j
[2025-09-04 21:28:25] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -52.591901+0.000783j
[2025-09-04 21:28:36] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -52.492797-0.000442j
[2025-09-04 21:28:46] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -52.607127+0.002000j
[2025-09-04 21:28:56] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -52.416041-0.000663j
[2025-09-04 21:29:06] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -52.449646-0.003970j
[2025-09-04 21:29:16] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -52.601249-0.000215j
[2025-09-04 21:29:27] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -52.540385+0.007575j
[2025-09-04 21:29:37] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -52.554552-0.004713j
[2025-09-04 21:29:47] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -52.672623-0.004575j
[2025-09-04 21:29:57] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -52.623228-0.004996j
[2025-09-04 21:30:07] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -52.605605-0.006362j
[2025-09-04 21:30:18] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -52.403888+0.004281j
[2025-09-04 21:30:28] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -52.437958-0.000951j
[2025-09-04 21:30:38] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -52.477330-0.001931j
[2025-09-04 21:30:48] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -52.435507-0.002876j
[2025-09-04 21:30:58] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -52.474057+0.002595j
[2025-09-04 21:31:09] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -52.398175-0.000024j
[2025-09-04 21:31:19] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -52.562080+0.005139j
[2025-09-04 21:31:29] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -52.581384-0.004292j
[2025-09-04 21:31:39] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -52.559558+0.004030j
[2025-09-04 21:31:49] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -52.530088-0.000551j
[2025-09-04 21:32:00] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -52.567487+0.002585j
[2025-09-04 21:32:10] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -52.639752-0.001905j
[2025-09-04 21:32:20] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -52.575435+0.004045j
[2025-09-04 21:32:30] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -52.519447-0.001289j
[2025-09-04 21:32:41] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -52.583317+0.000518j
[2025-09-04 21:32:51] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -52.628090+0.000675j
[2025-09-04 21:33:01] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -52.516474-0.001176j
[2025-09-04 21:33:11] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -52.580348-0.003311j
[2025-09-04 21:33:21] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -52.453326-0.000257j
[2025-09-04 21:33:32] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -52.575244-0.001993j
[2025-09-04 21:33:42] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -52.627384+0.004035j
[2025-09-04 21:33:52] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -52.555486-0.000650j
[2025-09-04 21:34:02] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -52.472192-0.000391j
[2025-09-04 21:34:12] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -52.477346-0.000747j
[2025-09-04 21:34:23] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -52.513185+0.000107j
[2025-09-04 21:34:33] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -52.591295+0.001031j
[2025-09-04 21:34:43] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -52.581122+0.002758j
[2025-09-04 21:34:53] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -52.695540-0.002515j
[2025-09-04 21:35:04] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -52.546752+0.000742j
[2025-09-04 21:35:14] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -52.535999-0.000723j
[2025-09-04 21:35:24] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -52.474164-0.000760j
[2025-09-04 21:35:34] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -52.562392-0.001942j
[2025-09-04 21:35:44] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -52.596075+0.002139j
[2025-09-04 21:35:55] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -52.664518+0.003198j
[2025-09-04 21:36:05] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -52.522388-0.001512j
[2025-09-04 21:36:15] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -52.646827-0.002930j
[2025-09-04 21:36:25] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -52.488658-0.000412j
[2025-09-04 21:36:35] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -52.510922-0.001440j
[2025-09-04 21:36:46] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -52.435661+0.002125j
[2025-09-04 21:36:56] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -52.330410+0.001094j
[2025-09-04 21:37:06] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -52.582819-0.000621j
[2025-09-04 21:37:16] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -52.491515+0.001260j
[2025-09-04 21:37:26] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -52.673096+0.003401j
[2025-09-04 21:37:37] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -52.572785-0.003173j
[2025-09-04 21:37:47] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -52.557477-0.002597j
[2025-09-04 21:37:57] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -52.520970-0.002866j
[2025-09-04 21:38:07] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -52.453717-0.002947j
[2025-09-04 21:38:17] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -52.480755-0.000139j
[2025-09-04 21:38:28] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -52.485750-0.000584j
[2025-09-04 21:38:38] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -52.528419-0.003104j
[2025-09-04 21:38:48] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -52.465202-0.002870j
[2025-09-04 21:38:58] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -52.479892-0.000138j
[2025-09-04 21:39:08] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -52.490651-0.000108j
[2025-09-04 21:39:19] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -52.445401-0.000875j
[2025-09-04 21:39:29] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -52.514762-0.004081j
[2025-09-04 21:39:39] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -52.477042+0.000879j
[2025-09-04 21:39:49] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -52.585607-0.004284j
[2025-09-04 21:40:00] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -52.422794+0.000360j
[2025-09-04 21:40:10] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -52.434875+0.001511j
[2025-09-04 21:40:20] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -52.396706-0.005623j
[2025-09-04 21:40:30] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -52.432246+0.000075j
[2025-09-04 21:40:40] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -52.402732-0.000353j
[2025-09-04 21:40:51] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -52.539203+0.002424j
[2025-09-04 21:41:01] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -52.520301-0.003551j
[2025-09-04 21:41:11] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -52.731412-0.000070j
[2025-09-04 21:41:21] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -52.574587+0.000055j
[2025-09-04 21:41:21] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-04 21:41:31] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -52.558879+0.001894j
[2025-09-04 21:41:42] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -52.453116-0.005637j
[2025-09-04 21:41:52] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -52.486520+0.002478j
[2025-09-04 21:42:02] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -52.432073-0.000786j
[2025-09-04 21:42:12] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -52.509556+0.000178j
[2025-09-04 21:42:22] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -52.497216-0.005060j
[2025-09-04 21:42:33] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -52.609659+0.005271j
[2025-09-04 21:42:43] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -52.442794+0.002399j
[2025-09-04 21:42:53] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -52.491998+0.001865j
[2025-09-04 21:43:03] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -52.402297-0.001192j
[2025-09-04 21:43:14] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -52.545105-0.002317j
[2025-09-04 21:43:24] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -52.549422-0.001161j
[2025-09-04 21:43:34] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -52.638469+0.003604j
[2025-09-04 21:43:45] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -52.590282-0.003541j
[2025-09-04 21:43:55] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -52.660211+0.002915j
[2025-09-04 21:44:05] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -52.502657-0.001360j
[2025-09-04 21:44:15] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -52.591479-0.001828j
[2025-09-04 21:44:26] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -52.540219-0.000882j
[2025-09-04 21:44:36] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -52.444631-0.001490j
[2025-09-04 21:44:46] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -52.488953+0.002781j
[2025-09-04 21:44:56] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -52.559887+0.004768j
[2025-09-04 21:45:06] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -52.459932+0.004704j
[2025-09-04 21:45:17] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -52.412969-0.000778j
[2025-09-04 21:45:27] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -52.551294+0.002581j
[2025-09-04 21:45:37] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -52.546557-0.001893j
[2025-09-04 21:45:47] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -52.488907+0.001474j
[2025-09-04 21:45:58] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -52.578085-0.001207j
[2025-09-04 21:46:08] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -52.472596-0.001889j
[2025-09-04 21:46:18] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -52.481243-0.001400j
[2025-09-04 21:46:28] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -52.620884-0.000215j
[2025-09-04 21:46:38] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -52.551702+0.000221j
[2025-09-04 21:46:49] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -52.483332+0.000106j
[2025-09-04 21:46:59] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -52.516951-0.003472j
[2025-09-04 21:47:09] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -52.369978+0.002438j
[2025-09-04 21:47:19] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -52.518496+0.002192j
[2025-09-04 21:47:29] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -52.521250+0.001752j
[2025-09-04 21:47:40] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -52.615164+0.005273j
[2025-09-04 21:47:50] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -52.677718+0.003479j
[2025-09-04 21:48:00] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -52.599294-0.002082j
[2025-09-04 21:48:10] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -52.554255-0.000085j
[2025-09-04 21:48:20] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -52.445174+0.008281j
[2025-09-04 21:48:31] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -52.440793-0.001960j
[2025-09-04 21:48:41] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -52.458322+0.000549j
[2025-09-04 21:48:51] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -52.451776-0.003167j
[2025-09-04 21:49:01] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -52.462150-0.002721j
[2025-09-04 21:49:12] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -52.624594-0.008411j
[2025-09-04 21:49:22] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -52.540119+0.003372j
[2025-09-04 21:49:32] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -52.553622-0.002085j
[2025-09-04 21:49:42] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -52.566544-0.002296j
[2025-09-04 21:49:52] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -52.618420+0.001690j
[2025-09-04 21:50:03] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -52.622929-0.005030j
[2025-09-04 21:50:13] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -52.666493-0.001516j
[2025-09-04 21:50:23] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -52.631726-0.004545j
[2025-09-04 21:50:33] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -52.651293+0.004755j
[2025-09-04 21:50:43] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -52.543258+0.000235j
[2025-09-04 21:50:54] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -52.510154+0.003181j
[2025-09-04 21:51:04] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -52.452523-0.003378j
[2025-09-04 21:51:14] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -52.583248+0.004785j
[2025-09-04 21:51:24] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -52.614226-0.000619j
[2025-09-04 21:51:34] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -52.495183-0.002195j
[2025-09-04 21:51:45] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -52.585720+0.002494j
[2025-09-04 21:51:55] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -52.534919+0.001754j
[2025-09-04 21:52:05] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -52.641285-0.000545j
[2025-09-04 21:52:15] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -52.495308+0.002411j
[2025-09-04 21:52:26] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -52.460538+0.002220j
[2025-09-04 21:52:36] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -52.555267+0.000681j
[2025-09-04 21:52:46] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -52.595355-0.010170j
[2025-09-04 21:52:56] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -52.585555+0.000502j
[2025-09-04 21:53:06] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -52.654699-0.002461j
[2025-09-04 21:53:17] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -52.738500-0.002946j
[2025-09-04 21:53:27] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -52.686043-0.001605j
[2025-09-04 21:53:37] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -52.645473-0.000319j
[2025-09-04 21:53:47] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -52.686267-0.005844j
[2025-09-04 21:53:57] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -52.622134+0.000668j
[2025-09-04 21:54:08] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -52.636160-0.001900j
[2025-09-04 21:54:18] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -52.612575+0.001243j
[2025-09-04 21:54:28] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -52.484844-0.000377j
[2025-09-04 21:54:38] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -52.555311+0.003554j
[2025-09-04 21:54:49] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -52.524289-0.004492j
[2025-09-04 21:54:59] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -52.571779+0.000850j
[2025-09-04 21:55:09] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -52.530553+0.000126j
[2025-09-04 21:55:19] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -52.567558+0.004988j
[2025-09-04 21:55:29] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -52.676354-0.001189j
[2025-09-04 21:55:40] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -52.759951+0.001232j
[2025-09-04 21:55:50] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -52.705837+0.002254j
[2025-09-04 21:56:00] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -52.646888-0.002411j
[2025-09-04 21:56:10] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -52.616491-0.003868j
[2025-09-04 21:56:20] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -52.551254-0.002638j
[2025-09-04 21:56:31] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -52.616957+0.002954j
[2025-09-04 21:56:41] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -52.758353+0.000038j
[2025-09-04 21:56:51] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -52.509782-0.001600j
[2025-09-04 21:57:01] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -52.396042+0.004182j
[2025-09-04 21:57:08] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -52.499788-0.001595j
[2025-09-04 21:57:13] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -52.556068-0.002101j
[2025-09-04 21:57:17] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -52.631091-0.004256j
[2025-09-04 21:57:22] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -52.549193+0.001701j
[2025-09-04 21:57:26] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -52.630081+0.001900j
[2025-09-04 21:57:31] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -52.583941+0.000450j
[2025-09-04 21:57:36] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -52.669731+0.003826j
[2025-09-04 21:57:40] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -52.607705-0.004578j
[2025-09-04 21:57:45] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -52.626133+0.000315j
[2025-09-04 21:57:49] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -52.658991+0.005446j
[2025-09-04 21:57:54] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -52.594265-0.000525j
[2025-09-04 21:57:59] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -52.563339-0.002361j
[2025-09-04 21:58:03] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -52.550134+0.002030j
[2025-09-04 21:58:03] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-04 21:58:03] ✅ Training completed | Restarts: 2
[2025-09-04 21:58:03] ============================================================
[2025-09-04 21:58:03] Training completed | Runtime: 10704.3s
[2025-09-04 21:58:05] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-04 21:58:05] ============================================================
