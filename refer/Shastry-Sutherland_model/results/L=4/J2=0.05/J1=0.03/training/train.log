[2025-09-04 21:58:15] ✓ 从checkpoint恢复: results/L=4/J2=0.05/J1=0.04/training/checkpoints/final_GCNN.pkl
[2025-09-04 21:58:15]   - 迭代次数: final
[2025-09-04 21:58:15]   - 能量: -51.644403+0.000163j ± 0.081677
[2025-09-04 21:58:15]   - 时间戳: 2025-09-04T21:57:06.010680+08:00
[2025-09-04 21:58:24] ✓ 变分状态参数已从checkpoint恢复
[2025-09-04 21:58:24] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-04 21:58:24] ==================================================
[2025-09-04 21:58:24] GCNN for Shastry-Sutherland Model
[2025-09-04 21:58:24] ==================================================
[2025-09-04 21:58:24] System parameters:
[2025-09-04 21:58:24]   - System size: L=4, N=64
[2025-09-04 21:58:24]   - System parameters: J1=0.03, J2=0.05, Q=0.95
[2025-09-04 21:58:24] --------------------------------------------------
[2025-09-04 21:58:24] Model parameters:
[2025-09-04 21:58:24]   - Number of layers = 4
[2025-09-04 21:58:24]   - Number of features = 4
[2025-09-04 21:58:24]   - Total parameters = 12572
[2025-09-04 21:58:24] --------------------------------------------------
[2025-09-04 21:58:24] Training parameters:
[2025-09-04 21:58:24]   - Learning rate: 0.015
[2025-09-04 21:58:24]   - Total iterations: 1050
[2025-09-04 21:58:24]   - Annealing cycles: 3
[2025-09-04 21:58:24]   - Initial period: 150
[2025-09-04 21:58:24]   - Period multiplier: 2.0
[2025-09-04 21:58:24]   - Temperature range: 0.0-1.0
[2025-09-04 21:58:24]   - Samples: 4096
[2025-09-04 21:58:24]   - Discarded samples: 0
[2025-09-04 21:58:24]   - Chunk size: 2048
[2025-09-04 21:58:24]   - Diagonal shift: 0.2
[2025-09-04 21:58:24]   - Gradient clipping: 1.0
[2025-09-04 21:58:24]   - Checkpoint enabled: interval=105
[2025-09-04 21:58:24]   - Checkpoint directory: results/L=4/J2=0.05/J1=0.03/training/checkpoints
[2025-09-04 21:58:24] --------------------------------------------------
[2025-09-04 21:58:24] Device status:
[2025-09-04 21:58:24]   - Devices model: NVIDIA H200 NVL
[2025-09-04 21:58:24]   - Number of devices: 1
[2025-09-04 21:58:24]   - Sharding: True
[2025-09-04 21:58:24] ============================================================
[2025-09-04 21:59:09] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -51.390994+0.009196j
[2025-09-04 21:59:38] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -51.445908+0.000586j
[2025-09-04 21:59:48] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -51.295258+0.000460j
[2025-09-04 21:59:58] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -51.390294-0.006008j
[2025-09-04 22:00:08] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -51.281135-0.002385j
[2025-09-04 22:00:18] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -51.237499+0.002546j
[2025-09-04 22:00:29] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -51.315444+0.003444j
[2025-09-04 22:00:39] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -51.262569+0.002998j
[2025-09-04 22:00:49] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -51.360786+0.005258j
[2025-09-04 22:00:59] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -51.460466+0.005326j
[2025-09-04 22:01:09] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -51.397653-0.001546j
[2025-09-04 22:01:19] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -51.532657-0.000114j
[2025-09-04 22:01:30] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -51.318713-0.002162j
[2025-09-04 22:01:40] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -51.439340+0.002185j
[2025-09-04 22:01:50] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -51.249468-0.001371j
[2025-09-04 22:02:00] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -51.374349+0.004056j
[2025-09-04 22:02:10] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -51.313604+0.003939j
[2025-09-04 22:02:20] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -51.344224-0.001090j
[2025-09-04 22:02:31] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -51.285558-0.002630j
[2025-09-04 22:02:41] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -51.325369+0.000669j
[2025-09-04 22:02:51] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -51.337909+0.001474j
[2025-09-04 22:03:01] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -51.307639-0.000678j
[2025-09-04 22:03:11] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -51.390412-0.000144j
[2025-09-04 22:03:22] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -51.395276-0.001767j
[2025-09-04 22:03:32] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -51.256402-0.001592j
[2025-09-04 22:03:42] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -51.435939+0.001865j
[2025-09-04 22:03:52] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -51.299028-0.002960j
[2025-09-04 22:04:02] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -51.414458+0.003396j
[2025-09-04 22:04:12] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -51.421920+0.001561j
[2025-09-04 22:04:23] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -51.309031-0.001938j
[2025-09-04 22:04:33] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -51.387994+0.002211j
[2025-09-04 22:04:43] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -51.312760+0.002142j
[2025-09-04 22:04:53] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -51.292089-0.002705j
[2025-09-04 22:05:03] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -51.225943-0.003167j
[2025-09-04 22:05:13] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -51.264140+0.034265j
[2025-09-04 22:05:24] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -51.251312-0.002211j
[2025-09-04 22:05:34] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -51.278338+0.004312j
[2025-09-04 22:05:44] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -51.271743+0.000235j
[2025-09-04 22:05:54] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -51.371589-0.002147j
[2025-09-04 22:06:04] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -51.301902+0.000533j
[2025-09-04 22:06:14] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -51.299836+0.004621j
[2025-09-04 22:06:25] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -51.302350+0.002416j
[2025-09-04 22:06:35] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -51.373603+0.002577j
[2025-09-04 22:06:45] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -51.336168+0.003919j
[2025-09-04 22:06:55] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -51.354200+0.000376j
[2025-09-04 22:07:05] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -51.479481-0.000602j
[2025-09-04 22:07:16] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -51.325881-0.001776j
[2025-09-04 22:07:26] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -51.415399-0.003078j
[2025-09-04 22:07:36] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -51.355486-0.002738j
[2025-09-04 22:07:46] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -51.317415-0.000965j
[2025-09-04 22:07:56] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -51.422400-0.000322j
[2025-09-04 22:08:06] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -51.448898+0.003428j
[2025-09-04 22:08:17] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -51.431153+0.000970j
[2025-09-04 22:08:27] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -51.242019-0.002973j
[2025-09-04 22:08:37] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -51.440805-0.000512j
[2025-09-04 22:08:47] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -51.276601+0.001649j
[2025-09-04 22:08:57] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -51.458170-0.002445j
[2025-09-04 22:09:07] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -51.335107+0.002063j
[2025-09-04 22:09:18] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -51.263621+0.002604j
[2025-09-04 22:09:28] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -51.342936-0.004372j
[2025-09-04 22:09:38] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -51.432047-0.003864j
[2025-09-04 22:09:48] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -51.325033-0.004673j
[2025-09-04 22:09:58] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -51.269194-0.000587j
[2025-09-04 22:10:08] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -51.279532-0.006932j
[2025-09-04 22:10:19] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -51.418264+0.002254j
[2025-09-04 22:10:29] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -51.309654+0.000575j
[2025-09-04 22:10:39] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -51.384000-0.003283j
[2025-09-04 22:10:49] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -51.283296-0.002548j
[2025-09-04 22:10:59] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -51.380513+0.003177j
[2025-09-04 22:11:09] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -51.177879-0.002437j
[2025-09-04 22:11:20] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -51.227456-0.004084j
[2025-09-04 22:11:30] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -51.325492-0.000094j
[2025-09-04 22:11:40] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -51.239514-0.002974j
[2025-09-04 22:11:50] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -51.369891-0.000667j
[2025-09-04 22:12:00] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -51.430248-0.001254j
[2025-09-04 22:12:10] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -51.396944-0.006787j
[2025-09-04 22:12:21] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -51.361751+0.000259j
[2025-09-04 22:12:31] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -51.313888+0.002585j
[2025-09-04 22:12:41] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -51.185059+0.003755j
[2025-09-04 22:12:51] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -51.295386+0.006445j
[2025-09-04 22:13:01] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -51.259523+0.005580j
[2025-09-04 22:13:11] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -51.251025-0.006314j
[2025-09-04 22:13:22] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -51.211599+0.000192j
[2025-09-04 22:13:32] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -51.280269+0.003583j
[2025-09-04 22:13:42] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -51.176926+0.004941j
[2025-09-04 22:13:52] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -51.206591-0.003658j
[2025-09-04 22:14:02] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -51.210876-0.002373j
[2025-09-04 22:14:12] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -51.319544+0.000735j
[2025-09-04 22:14:23] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -51.442110+0.002480j
[2025-09-04 22:14:33] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -51.361197-0.002491j
[2025-09-04 22:14:43] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -51.306948+0.001049j
[2025-09-04 22:14:53] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -51.469702+0.000935j
[2025-09-04 22:15:03] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -51.464022+0.004596j
[2025-09-04 22:15:13] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -51.573387+0.000611j
[2025-09-04 22:15:24] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -51.515580-0.002089j
[2025-09-04 22:15:34] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -51.445050-0.003954j
[2025-09-04 22:15:44] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -51.385929+0.002042j
[2025-09-04 22:15:54] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -51.444660-0.001221j
[2025-09-04 22:16:04] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -51.381679+0.003452j
[2025-09-04 22:16:15] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -51.343400+0.000408j
[2025-09-04 22:16:25] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -51.365387+0.001136j
[2025-09-04 22:16:35] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -51.508927-0.000653j
[2025-09-04 22:16:45] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -51.380852+0.002470j
[2025-09-04 22:16:55] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -51.435455+0.003398j
[2025-09-04 22:17:05] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -51.537889-0.000654j
[2025-09-04 22:17:05] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-04 22:17:16] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -51.376417+0.000999j
[2025-09-04 22:17:26] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -51.411065+0.001522j
[2025-09-04 22:17:36] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -51.361349-0.002280j
[2025-09-04 22:17:46] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -51.377309+0.002412j
[2025-09-04 22:17:56] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -51.351381+0.002693j
[2025-09-04 22:18:06] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -51.356153+0.002111j
[2025-09-04 22:18:17] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -51.527754-0.000020j
[2025-09-04 22:18:27] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -51.330453+0.001850j
[2025-09-04 22:18:37] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -51.358368+0.004499j
[2025-09-04 22:18:47] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -51.524068+0.007216j
[2025-09-04 22:18:57] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -51.251682-0.000118j
[2025-09-04 22:19:08] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -51.411449-0.003566j
[2025-09-04 22:19:18] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -51.342233-0.001220j
[2025-09-04 22:19:28] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -51.349697-0.000749j
[2025-09-04 22:19:38] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -51.365483+0.005578j
[2025-09-04 22:19:48] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -51.367981+0.001880j
[2025-09-04 22:19:58] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -51.276169-0.004439j
[2025-09-04 22:20:09] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -51.284040-0.002116j
[2025-09-04 22:20:19] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -51.336902+0.000807j
[2025-09-04 22:20:29] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -51.352629+0.001097j
[2025-09-04 22:20:39] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -51.366815-0.008327j
[2025-09-04 22:20:49] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -51.225274+0.004447j
[2025-09-04 22:20:59] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -51.337345-0.000172j
[2025-09-04 22:21:10] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -51.368307-0.005151j
[2025-09-04 22:21:20] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -51.264425+0.000414j
[2025-09-04 22:21:30] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -51.393310-0.004687j
[2025-09-04 22:21:40] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -51.253374-0.001768j
[2025-09-04 22:21:50] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -51.369823+0.000421j
[2025-09-04 22:22:01] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -51.312032-0.000242j
[2025-09-04 22:22:11] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -51.413029-0.001383j
[2025-09-04 22:22:21] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -51.373737+0.000862j
[2025-09-04 22:22:31] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -51.348913-0.002568j
[2025-09-04 22:22:41] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -51.360009-0.001652j
[2025-09-04 22:22:52] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -51.366052+0.003968j
[2025-09-04 22:23:02] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -51.454208-0.003245j
[2025-09-04 22:23:12] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -51.277917+0.003628j
[2025-09-04 22:23:22] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -51.256308+0.000284j
[2025-09-04 22:23:32] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -51.405837+0.003506j
[2025-09-04 22:23:42] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -51.293254-0.002340j
[2025-09-04 22:23:53] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -51.318117-0.001404j
[2025-09-04 22:24:03] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -51.381277+0.002546j
[2025-09-04 22:24:13] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -51.293713+0.002412j
[2025-09-04 22:24:23] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -51.331937+0.001366j
[2025-09-04 22:24:33] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -51.288588-0.000865j
[2025-09-04 22:24:44] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -51.388891-0.000935j
[2025-09-04 22:24:44] RESTART #1 | Period: 300
[2025-09-04 22:24:54] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -51.524793-0.000860j
[2025-09-04 22:25:04] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -51.369941-0.002427j
[2025-09-04 22:25:14] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -51.407912-0.004934j
[2025-09-04 22:25:24] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -51.367642+0.000614j
[2025-09-04 22:25:34] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -51.400988+0.000071j
[2025-09-04 22:25:45] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -51.348872-0.002372j
[2025-09-04 22:25:55] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -51.362795+0.001511j
[2025-09-04 22:26:05] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -51.402729+0.002815j
[2025-09-04 22:26:15] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -51.437163-0.001617j
[2025-09-04 22:26:25] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -51.386301+0.001069j
[2025-09-04 22:26:35] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -51.471714+0.001532j
[2025-09-04 22:26:46] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -51.337026-0.005386j
[2025-09-04 22:26:56] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -51.379195+0.000298j
[2025-09-04 22:27:06] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -51.419927-0.001774j
[2025-09-04 22:27:16] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -51.257122+0.003291j
[2025-09-04 22:27:26] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -51.349302+0.003326j
[2025-09-04 22:27:36] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -51.363697-0.001453j
[2025-09-04 22:27:47] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -51.312865+0.001228j
[2025-09-04 22:27:57] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -51.339590-0.003810j
[2025-09-04 22:28:07] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -51.272508-0.002223j
[2025-09-04 22:28:17] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -51.354535-0.002235j
[2025-09-04 22:28:27] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -51.348207-0.001125j
[2025-09-04 22:28:38] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -51.281579-0.000038j
[2025-09-04 22:28:48] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -51.266832-0.000225j
[2025-09-04 22:28:58] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -51.307832+0.003447j
[2025-09-04 22:29:08] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -51.316269-0.000704j
[2025-09-04 22:29:18] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -51.329507-0.001260j
[2025-09-04 22:29:29] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -51.190941+0.001833j
[2025-09-04 22:29:39] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -51.236659-0.003104j
[2025-09-04 22:29:49] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -51.285212-0.001743j
[2025-09-04 22:29:59] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -51.273656-0.000372j
[2025-09-04 22:30:09] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -51.303454-0.002970j
[2025-09-04 22:30:19] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -51.150842+0.001087j
[2025-09-04 22:30:30] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -51.165685+0.002320j
[2025-09-04 22:30:40] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -51.308924+0.002459j
[2025-09-04 22:30:50] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -51.347095+0.001599j
[2025-09-04 22:31:00] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -51.410726-0.001228j
[2025-09-04 22:31:10] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -51.422988+0.003608j
[2025-09-04 22:31:20] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -51.338055-0.001250j
[2025-09-04 22:31:31] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -51.305376+0.003613j
[2025-09-04 22:31:41] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -51.310625+0.001896j
[2025-09-04 22:31:51] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -51.451760-0.001107j
[2025-09-04 22:32:01] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -51.439660-0.002004j
[2025-09-04 22:32:11] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -51.211792+0.000262j
[2025-09-04 22:32:22] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -51.268185+0.001180j
[2025-09-04 22:32:32] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -51.272135-0.000108j
[2025-09-04 22:32:42] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -51.294635-0.001780j
[2025-09-04 22:32:52] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -51.358773+0.002728j
[2025-09-04 22:33:02] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -51.328591-0.000430j
[2025-09-04 22:33:12] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -51.265518-0.002958j
[2025-09-04 22:33:23] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -51.327512-0.000788j
[2025-09-04 22:33:33] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -51.412230-0.000132j
[2025-09-04 22:33:43] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -51.368452-0.000825j
[2025-09-04 22:33:53] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -51.386602-0.003190j
[2025-09-04 22:34:03] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -51.389875-0.001972j
[2025-09-04 22:34:13] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -51.256821-0.005043j
[2025-09-04 22:34:24] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -51.441508-0.001708j
[2025-09-04 22:34:34] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -51.294928-0.001300j
[2025-09-04 22:34:44] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -51.284366+0.003532j
[2025-09-04 22:34:54] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -51.306925+0.005181j
[2025-09-04 22:34:54] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-04 22:35:04] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -51.280422-0.000335j
[2025-09-04 22:35:14] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -51.396732-0.000978j
[2025-09-04 22:35:25] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -51.393026+0.000645j
[2025-09-04 22:35:35] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -51.304313+0.002959j
[2025-09-04 22:35:45] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -51.348253+0.007527j
[2025-09-04 22:35:55] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -51.383165-0.001016j
[2025-09-04 22:36:05] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -51.344245-0.003739j
[2025-09-04 22:36:16] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -51.403147+0.000335j
[2025-09-04 22:36:26] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -51.298644+0.000554j
[2025-09-04 22:36:36] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -51.294102-0.004161j
[2025-09-04 22:36:46] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -51.323601-0.001471j
[2025-09-04 22:36:56] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -51.252388+0.006812j
[2025-09-04 22:37:06] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -51.311242-0.001438j
[2025-09-04 22:37:16] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -51.320693+0.003612j
[2025-09-04 22:37:27] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -51.251699+0.004484j
[2025-09-04 22:37:37] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -51.404356-0.000011j
[2025-09-04 22:37:47] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -51.235526-0.003073j
[2025-09-04 22:37:57] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -51.324971+0.003671j
[2025-09-04 22:38:07] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -51.358373+0.000121j
[2025-09-04 22:38:17] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -51.456433+0.002409j
[2025-09-04 22:38:28] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -51.392045-0.000646j
[2025-09-04 22:38:38] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -51.335992+0.004223j
[2025-09-04 22:38:48] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -51.346510+0.003695j
[2025-09-04 22:38:58] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -51.252649+0.004369j
[2025-09-04 22:39:08] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -51.325745+0.000762j
[2025-09-04 22:39:18] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -51.310393+0.005164j
[2025-09-04 22:39:29] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -51.354548-0.002395j
[2025-09-04 22:39:39] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -51.322917+0.003242j
[2025-09-04 22:39:49] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -51.402388+0.000310j
[2025-09-04 22:39:59] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -51.189534+0.004856j
[2025-09-04 22:40:09] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -51.153839+0.005789j
[2025-09-04 22:40:19] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -51.161164+0.001187j
[2025-09-04 22:40:29] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -51.245185+0.000920j
[2025-09-04 22:40:40] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -51.203583+0.002201j
[2025-09-04 22:40:50] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -51.314623-0.007036j
[2025-09-04 22:41:00] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -51.217516-0.000359j
[2025-09-04 22:41:10] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -51.245682-0.002234j
[2025-09-04 22:41:20] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -51.317581+0.003111j
[2025-09-04 22:41:30] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -51.429354+0.001045j
[2025-09-04 22:41:41] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -51.415745+0.002207j
[2025-09-04 22:41:51] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -51.326574+0.000600j
[2025-09-04 22:42:01] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -51.389548+0.001463j
[2025-09-04 22:42:11] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -51.329000-0.001171j
[2025-09-04 22:42:21] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -51.288387-0.003008j
[2025-09-04 22:42:31] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -51.244648-0.001222j
[2025-09-04 22:42:42] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -51.201480+0.000679j
[2025-09-04 22:42:52] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -51.253347-0.001391j
[2025-09-04 22:43:02] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -51.315050-0.000850j
[2025-09-04 22:43:12] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -51.298694+0.000572j
[2025-09-04 22:43:22] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -51.327787-0.001703j
[2025-09-04 22:43:33] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -51.181432+0.001992j
[2025-09-04 22:43:43] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -51.232220-0.004268j
[2025-09-04 22:43:53] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -51.168100-0.000448j
[2025-09-04 22:44:03] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -51.113339+0.001273j
[2025-09-04 22:44:13] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -51.300694-0.000923j
[2025-09-04 22:44:23] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -51.209970+0.002537j
[2025-09-04 22:44:34] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -51.254954-0.000222j
[2025-09-04 22:44:44] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -51.184398-0.002539j
[2025-09-04 22:44:54] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -51.266720-0.000178j
[2025-09-04 22:45:04] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -51.174277-0.003853j
[2025-09-04 22:45:14] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -51.315853+0.000565j
[2025-09-04 22:45:24] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -51.258620+0.000722j
[2025-09-04 22:45:35] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -51.532399+0.003253j
[2025-09-04 22:45:45] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -51.380327-0.001228j
[2025-09-04 22:45:55] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -51.526681-0.002709j
[2025-09-04 22:46:05] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -51.461968+0.000293j
[2025-09-04 22:46:15] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -51.390517-0.000747j
[2025-09-04 22:46:26] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -51.453256-0.005834j
[2025-09-04 22:46:36] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -51.429526+0.001671j
[2025-09-04 22:46:46] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -51.542295-0.000214j
[2025-09-04 22:46:56] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -51.452253+0.001803j
[2025-09-04 22:47:06] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -51.510214-0.000699j
[2025-09-04 22:47:16] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -51.373398+0.001661j
[2025-09-04 22:47:27] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -51.376032-0.002185j
[2025-09-04 22:47:37] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -51.332120+0.001559j
[2025-09-04 22:47:47] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -51.240449+0.000474j
[2025-09-04 22:47:57] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -51.328342-0.006197j
[2025-09-04 22:48:07] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -51.342104-0.002965j
[2025-09-04 22:48:17] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -51.371391+0.000795j
[2025-09-04 22:48:28] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -51.451931+0.001158j
[2025-09-04 22:48:38] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -51.499199-0.000821j
[2025-09-04 22:48:48] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -51.307830-0.001430j
[2025-09-04 22:48:58] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -51.235024-0.000133j
[2025-09-04 22:49:08] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -51.225991+0.001553j
[2025-09-04 22:49:19] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -51.376744+0.004007j
[2025-09-04 22:49:29] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -51.331680-0.001341j
[2025-09-04 22:49:39] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -51.345003-0.000217j
[2025-09-04 22:49:49] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -51.358929-0.002353j
[2025-09-04 22:49:59] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -51.323835+0.002042j
[2025-09-04 22:50:09] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -51.415488-0.003474j
[2025-09-04 22:50:20] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -51.380899+0.000259j
[2025-09-04 22:50:30] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -51.338294+0.000029j
[2025-09-04 22:50:40] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -51.409674+0.002901j
[2025-09-04 22:50:50] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -51.503588-0.000609j
[2025-09-04 22:51:00] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -51.358055+0.000478j
[2025-09-04 22:51:10] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -51.525653+0.002549j
[2025-09-04 22:51:21] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -51.406735+0.000743j
[2025-09-04 22:51:31] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -51.385479+0.015907j
[2025-09-04 22:51:41] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -51.445081-0.002677j
[2025-09-04 22:51:51] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -51.405616-0.000977j
[2025-09-04 22:52:01] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -51.415658-0.002746j
[2025-09-04 22:52:12] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -51.311706+0.001067j
[2025-09-04 22:52:22] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -51.471067+0.003171j
[2025-09-04 22:52:32] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -51.425479-0.001371j
[2025-09-04 22:52:42] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -51.421821+0.000522j
[2025-09-04 22:52:42] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-04 22:52:52] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -51.451011+0.001336j
[2025-09-04 22:53:02] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -51.304691+0.003161j
[2025-09-04 22:53:13] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -51.385182+0.006318j
[2025-09-04 22:53:23] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -51.234086-0.001669j
[2025-09-04 22:53:33] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -51.308075+0.000185j
[2025-09-04 22:53:43] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -51.364130+0.001361j
[2025-09-04 22:53:53] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -51.479506+0.001952j
[2025-09-04 22:54:04] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -51.342068-0.001845j
[2025-09-04 22:54:14] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -51.270671+0.003151j
[2025-09-04 22:54:24] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -51.335232+0.003773j
[2025-09-04 22:54:34] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -51.344587-0.004076j
[2025-09-04 22:54:44] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -51.451460+0.002817j
[2025-09-04 22:54:54] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -51.353649+0.001045j
[2025-09-04 22:55:05] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -51.370076+0.001866j
[2025-09-04 22:55:15] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -51.377029+0.003564j
[2025-09-04 22:55:25] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -51.393472-0.002314j
[2025-09-04 22:55:35] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -51.412745+0.000101j
[2025-09-04 22:55:45] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -51.491334-0.004664j
[2025-09-04 22:55:56] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -51.490675+0.001047j
[2025-09-04 22:56:06] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -51.456829+0.002218j
[2025-09-04 22:56:16] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -51.579679+0.001488j
[2025-09-04 22:56:26] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -51.540258-0.001165j
[2025-09-04 22:56:36] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -51.374480-0.006778j
[2025-09-04 22:56:46] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -51.442567-0.001803j
[2025-09-04 22:56:57] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -51.579478+0.000357j
[2025-09-04 22:57:07] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -51.522191+0.002156j
[2025-09-04 22:57:17] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -51.548289+0.000112j
[2025-09-04 22:57:27] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -51.540874+0.002089j
[2025-09-04 22:57:37] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -51.516461+0.003922j
[2025-09-04 22:57:47] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -51.405731+0.000083j
[2025-09-04 22:57:58] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -51.447583+0.001464j
[2025-09-04 22:58:08] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -51.406871-0.001273j
[2025-09-04 22:58:18] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -51.428383-0.002218j
[2025-09-04 22:58:28] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -51.441996-0.003224j
[2025-09-04 22:58:38] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -51.447724+0.002995j
[2025-09-04 22:58:49] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -51.353185+0.000191j
[2025-09-04 22:58:59] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -51.295189+0.002389j
[2025-09-04 22:59:09] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -51.383647-0.002264j
[2025-09-04 22:59:19] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -51.465573-0.001462j
[2025-09-04 22:59:29] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -51.379234-0.000117j
[2025-09-04 22:59:39] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -51.511753-0.000279j
[2025-09-04 22:59:50] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -51.469238-0.004286j
[2025-09-04 23:00:00] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -51.401578+0.000235j
[2025-09-04 23:00:10] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -51.398586-0.002244j
[2025-09-04 23:00:20] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -51.370799-0.002320j
[2025-09-04 23:00:30] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -51.399575+0.004236j
[2025-09-04 23:00:41] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -51.376371-0.003015j
[2025-09-04 23:00:51] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -51.381500+0.000660j
[2025-09-04 23:01:01] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -51.404500-0.000926j
[2025-09-04 23:01:11] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -51.353914-0.000286j
[2025-09-04 23:01:21] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -51.520578+0.000192j
[2025-09-04 23:01:31] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -51.565499-0.007244j
[2025-09-04 23:01:42] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -51.469968+0.001886j
[2025-09-04 23:01:52] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -51.288755-0.003514j
[2025-09-04 23:02:02] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -51.378623+0.001205j
[2025-09-04 23:02:12] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -51.537149-0.002329j
[2025-09-04 23:02:22] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -51.485967+0.000913j
[2025-09-04 23:02:32] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -51.492903+0.003514j
[2025-09-04 23:02:43] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -51.538993-0.005432j
[2025-09-04 23:02:53] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -51.472085-0.002456j
[2025-09-04 23:03:03] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -51.471000+0.000825j
[2025-09-04 23:03:13] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -51.374394+0.002314j
[2025-09-04 23:03:23] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -51.361273+0.002678j
[2025-09-04 23:03:33] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -51.266082+0.002428j
[2025-09-04 23:03:44] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -51.356337-0.003702j
[2025-09-04 23:03:54] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -51.391596+0.000530j
[2025-09-04 23:04:04] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -51.402501-0.001499j
[2025-09-04 23:04:14] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -51.302668+0.002595j
[2025-09-04 23:04:24] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -51.362622+0.004580j
[2025-09-04 23:04:35] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -51.455956+0.000295j
[2025-09-04 23:04:45] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -51.444052-0.001286j
[2025-09-04 23:04:55] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -51.414104-0.001715j
[2025-09-04 23:05:05] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -51.325678-0.003854j
[2025-09-04 23:05:15] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -51.340893+0.000352j
[2025-09-04 23:05:25] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -51.391251-0.000358j
[2025-09-04 23:05:36] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -51.575591+0.000775j
[2025-09-04 23:05:46] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -51.496159+0.002598j
[2025-09-04 23:05:56] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -51.438801+0.002293j
[2025-09-04 23:06:06] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -51.420086+0.001177j
[2025-09-04 23:06:16] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -51.334186-0.000224j
[2025-09-04 23:06:26] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -51.385531-0.002108j
[2025-09-04 23:06:37] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -51.271820+0.001846j
[2025-09-04 23:06:47] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -51.363836-0.004015j
[2025-09-04 23:06:57] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -51.285028-0.000888j
[2025-09-04 23:07:07] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -51.325309-0.001027j
[2025-09-04 23:07:17] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -51.311444+0.001121j
[2025-09-04 23:07:28] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -51.365214-0.000989j
[2025-09-04 23:07:38] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -51.366501-0.001182j
[2025-09-04 23:07:48] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -51.277938-0.003574j
[2025-09-04 23:07:58] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -51.334577+0.002404j
[2025-09-04 23:08:08] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -51.345927-0.002882j
[2025-09-04 23:08:19] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -51.347503+0.004803j
[2025-09-04 23:08:29] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -51.276868-0.002658j
[2025-09-04 23:08:39] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -51.267111+0.000481j
[2025-09-04 23:08:49] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -51.331927+0.003080j
[2025-09-04 23:08:59] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -51.370129-0.001844j
[2025-09-04 23:09:09] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -51.361927-0.001764j
[2025-09-04 23:09:20] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -51.277721+0.001617j
[2025-09-04 23:09:30] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -51.364853+0.001784j
[2025-09-04 23:09:40] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -51.396920-0.000006j
[2025-09-04 23:09:50] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -51.318861+0.002364j
[2025-09-04 23:10:00] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -51.291730-0.002317j
[2025-09-04 23:10:10] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -51.327609-0.001138j
[2025-09-04 23:10:21] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -51.498594-0.001069j
[2025-09-04 23:10:31] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -51.471109-0.003738j
[2025-09-04 23:10:31] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-04 23:10:41] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -51.468634+0.001997j
[2025-09-04 23:10:51] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -51.513011-0.000963j
[2025-09-04 23:11:01] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -51.477759+0.005347j
[2025-09-04 23:11:12] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -51.515364-0.003527j
[2025-09-04 23:11:22] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -51.494471+0.003495j
[2025-09-04 23:11:32] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -51.447790-0.002018j
[2025-09-04 23:11:42] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -51.414328+0.003269j
[2025-09-04 23:11:52] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -51.345255-0.000250j
[2025-09-04 23:12:02] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -51.376662-0.001206j
[2025-09-04 23:12:13] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -51.351737-0.003816j
[2025-09-04 23:12:23] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -51.345979-0.001512j
[2025-09-04 23:12:33] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -51.240441+0.002429j
[2025-09-04 23:12:43] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -51.303561-0.003558j
[2025-09-04 23:12:53] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -51.281859+0.004105j
[2025-09-04 23:13:04] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -51.222515-0.003195j
[2025-09-04 23:13:14] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -51.289165+0.004809j
[2025-09-04 23:13:24] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -51.352885-0.002069j
[2025-09-04 23:13:34] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -51.415496+0.004060j
[2025-09-04 23:13:44] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -51.288354-0.003260j
[2025-09-04 23:13:54] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -51.231212-0.000583j
[2025-09-04 23:14:05] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -51.250423+0.000095j
[2025-09-04 23:14:15] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -51.256128+0.003321j
[2025-09-04 23:14:25] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -51.214093-0.001019j
[2025-09-04 23:14:35] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -51.306707+0.005546j
[2025-09-04 23:14:45] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -51.522974+0.001477j
[2025-09-04 23:14:56] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -51.489315-0.006504j
[2025-09-04 23:15:06] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -51.508452+0.000821j
[2025-09-04 23:15:16] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -51.308286+0.000345j
[2025-09-04 23:15:26] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -51.399575+0.002376j
[2025-09-04 23:15:36] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -51.348694-0.000719j
[2025-09-04 23:15:36] RESTART #2 | Period: 600
[2025-09-04 23:15:46] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -51.422241+0.003274j
[2025-09-04 23:15:57] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -51.345058-0.005393j
[2025-09-04 23:16:07] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -51.474324-0.003300j
[2025-09-04 23:16:17] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -51.390098-0.000106j
[2025-09-04 23:16:27] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -51.383531-0.000153j
[2025-09-04 23:16:37] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -51.354267+0.000571j
[2025-09-04 23:16:48] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -51.455570+0.001138j
[2025-09-04 23:16:58] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -51.375641+0.000632j
[2025-09-04 23:17:08] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -51.469638+0.000062j
[2025-09-04 23:17:18] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -51.413069-0.002408j
[2025-09-04 23:17:28] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -51.525500-0.002011j
[2025-09-04 23:17:38] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -51.464279-0.001656j
[2025-09-04 23:17:49] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -51.557054-0.004050j
[2025-09-04 23:17:59] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -51.546851+0.002570j
[2025-09-04 23:18:09] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -51.476746-0.004025j
[2025-09-04 23:18:19] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -51.427875-0.000335j
[2025-09-04 23:18:29] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -51.424269+0.000029j
[2025-09-04 23:18:40] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -51.359435-0.003859j
[2025-09-04 23:18:50] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -51.350106+0.001976j
[2025-09-04 23:19:00] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -51.498936+0.002543j
[2025-09-04 23:19:10] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -51.323227+0.002442j
[2025-09-04 23:19:20] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -51.256677+0.002313j
[2025-09-04 23:19:31] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -51.285937+0.003429j
[2025-09-04 23:19:41] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -51.278260-0.000913j
[2025-09-04 23:19:51] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -51.231572+0.001962j
[2025-09-04 23:20:01] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -51.293568-0.005841j
[2025-09-04 23:20:11] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -51.337199+0.001260j
[2025-09-04 23:20:21] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -51.321293+0.002540j
[2025-09-04 23:20:32] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -51.351022-0.001156j
[2025-09-04 23:20:42] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -51.354531+0.001192j
[2025-09-04 23:20:52] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -51.425990+0.001704j
[2025-09-04 23:21:03] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -51.429097+0.001278j
[2025-09-04 23:21:13] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -51.248167-0.003552j
[2025-09-04 23:21:23] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -51.275037-0.000108j
[2025-09-04 23:21:33] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -51.221674-0.001316j
[2025-09-04 23:21:43] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -51.285037+0.000103j
[2025-09-04 23:21:53] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -51.424364-0.000827j
[2025-09-04 23:22:04] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -51.482879-0.001605j
[2025-09-04 23:22:14] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -51.354665+0.001966j
[2025-09-04 23:22:24] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -51.341010-0.000242j
[2025-09-04 23:22:34] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -51.431726-0.002336j
[2025-09-04 23:22:44] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -51.328203-0.000356j
[2025-09-04 23:22:54] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -51.359672+0.000556j
[2025-09-04 23:23:05] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -51.319197-0.000055j
[2025-09-04 23:23:15] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -51.370609+0.001469j
[2025-09-04 23:23:25] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -51.250704+0.004146j
[2025-09-04 23:23:35] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -51.421546+0.000188j
[2025-09-04 23:23:45] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -51.290267-0.000515j
[2025-09-04 23:23:56] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -51.125127-0.000850j
[2025-09-04 23:24:06] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -51.258655-0.000591j
[2025-09-04 23:24:16] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -51.304363+0.000835j
[2025-09-04 23:24:26] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -51.411014-0.003721j
[2025-09-04 23:24:36] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -51.512843-0.002513j
[2025-09-04 23:24:46] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -51.365299-0.001542j
[2025-09-04 23:24:57] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -51.360842+0.000126j
[2025-09-04 23:25:07] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -51.393792-0.002470j
[2025-09-04 23:25:17] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -51.371781+0.000110j
[2025-09-04 23:25:27] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -51.466809+0.001054j
[2025-09-04 23:25:37] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -51.550822+0.000910j
[2025-09-04 23:25:47] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -51.495150+0.004704j
[2025-09-04 23:25:58] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -51.528334-0.003148j
[2025-09-04 23:26:08] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -51.423147+0.003386j
[2025-09-04 23:26:18] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -51.483997+0.000315j
[2025-09-04 23:26:28] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -51.406476+0.001960j
[2025-09-04 23:26:38] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -51.372378+0.005879j
[2025-09-04 23:26:49] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -51.358380-0.000475j
[2025-09-04 23:26:59] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -51.307483+0.005185j
[2025-09-04 23:27:09] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -51.286191-0.001825j
[2025-09-04 23:27:19] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -51.320167+0.006898j
[2025-09-04 23:27:29] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -51.334290+0.000168j
[2025-09-04 23:27:39] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -51.324168+0.002750j
[2025-09-04 23:27:50] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -51.430443+0.000214j
[2025-09-04 23:28:00] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -51.317862-0.001981j
[2025-09-04 23:28:10] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -51.361543-0.002176j
[2025-09-04 23:28:20] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -51.294326-0.002073j
[2025-09-04 23:28:20] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-04 23:28:30] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -51.385898-0.000807j
[2025-09-04 23:28:40] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -51.495234+0.000995j
[2025-09-04 23:28:51] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -51.403903-0.004430j
[2025-09-04 23:29:01] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -51.215102+0.001708j
[2025-09-04 23:29:11] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -51.266800+0.004553j
[2025-09-04 23:29:21] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -51.297735+0.001004j
[2025-09-04 23:29:31] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -51.241117+0.002708j
[2025-09-04 23:29:42] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -51.301230+0.002291j
[2025-09-04 23:29:52] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -51.320219-0.001544j
[2025-09-04 23:30:02] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -51.407263-0.001627j
[2025-09-04 23:30:12] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -51.270996-0.001578j
[2025-09-04 23:30:22] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -51.362266+0.000345j
[2025-09-04 23:30:32] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -51.365121+0.002242j
[2025-09-04 23:30:43] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -51.242780+0.001447j
[2025-09-04 23:30:53] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -51.260267+0.002815j
[2025-09-04 23:31:03] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -51.285247-0.002239j
[2025-09-04 23:31:13] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -51.327919+0.000058j
[2025-09-04 23:31:23] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -51.385949+0.000997j
[2025-09-04 23:31:34] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -51.336363-0.004355j
[2025-09-04 23:31:44] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -51.429560-0.000538j
[2025-09-04 23:31:54] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -51.494604-0.002438j
[2025-09-04 23:32:04] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -51.343274+0.000406j
[2025-09-04 23:32:14] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -51.372560-0.001250j
[2025-09-04 23:32:24] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -51.402510+0.002861j
[2025-09-04 23:32:35] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -51.375010+0.001850j
[2025-09-04 23:32:45] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -51.349155+0.000435j
[2025-09-04 23:32:55] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -51.281322-0.009680j
[2025-09-04 23:33:05] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -51.335793-0.003652j
[2025-09-04 23:33:15] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -51.426259+0.001206j
[2025-09-04 23:33:26] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -51.451869+0.001548j
[2025-09-04 23:33:36] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -51.301325+0.003472j
[2025-09-04 23:33:46] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -51.210354-0.000124j
[2025-09-04 23:33:56] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -51.247867-0.000713j
[2025-09-04 23:34:06] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -51.296744+0.000609j
[2025-09-04 23:34:16] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -51.448818-0.004618j
[2025-09-04 23:34:27] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -51.240399-0.001114j
[2025-09-04 23:34:37] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -51.258931+0.003090j
[2025-09-04 23:34:47] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -51.252855-0.002329j
[2025-09-04 23:34:57] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -51.223137+0.003943j
[2025-09-04 23:35:08] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -51.185806-0.004562j
[2025-09-04 23:35:18] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -51.342921+0.001244j
[2025-09-04 23:35:28] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -51.492011-0.002025j
[2025-09-04 23:35:38] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -51.437918+0.001608j
[2025-09-04 23:35:48] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -51.367681-0.001264j
[2025-09-04 23:35:58] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -51.404019+0.001394j
[2025-09-04 23:36:09] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -51.290657+0.003527j
[2025-09-04 23:36:19] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -51.489814-0.001033j
[2025-09-04 23:36:29] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -51.375471-0.000286j
[2025-09-04 23:36:39] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -51.361555-0.001639j
[2025-09-04 23:36:49] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -51.357055+0.004672j
[2025-09-04 23:36:59] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -51.284999-0.000159j
[2025-09-04 23:37:10] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -51.348916-0.002917j
[2025-09-04 23:37:20] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -51.305399-0.004166j
[2025-09-04 23:37:30] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -51.321088+0.001918j
[2025-09-04 23:37:40] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -51.326415-0.002840j
[2025-09-04 23:37:50] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -51.425918+0.000666j
[2025-09-04 23:38:00] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -51.386302+0.004326j
[2025-09-04 23:38:11] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -51.436414-0.001136j
[2025-09-04 23:38:21] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -51.448008+0.000000j
[2025-09-04 23:38:31] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -51.381847+0.000167j
[2025-09-04 23:38:41] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -51.381021-0.000136j
[2025-09-04 23:38:51] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -51.302333+0.001983j
[2025-09-04 23:39:02] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -51.421509-0.000202j
[2025-09-04 23:39:12] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -51.397338+0.000135j
[2025-09-04 23:39:22] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -51.477191-0.002254j
[2025-09-04 23:39:32] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -51.425612-0.001611j
[2025-09-04 23:39:42] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -51.404899+0.003279j
[2025-09-04 23:39:52] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -51.342698+0.002223j
[2025-09-04 23:40:03] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -51.291789+0.000044j
[2025-09-04 23:40:13] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -51.262042+0.004699j
[2025-09-04 23:40:23] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -51.411917-0.002314j
[2025-09-04 23:40:33] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -51.262443-0.002987j
[2025-09-04 23:40:43] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -51.300786+0.002033j
[2025-09-04 23:40:53] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -51.329373+0.000314j
[2025-09-04 23:41:03] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -51.454424+0.000811j
[2025-09-04 23:41:14] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -51.450320-0.000675j
[2025-09-04 23:41:24] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -51.321807+0.004942j
[2025-09-04 23:41:34] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -51.457804+0.000586j
[2025-09-04 23:41:44] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -51.410923-0.000712j
[2025-09-04 23:41:54] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -51.438883+0.001078j
[2025-09-04 23:42:05] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -51.484751-0.001051j
[2025-09-04 23:42:15] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -51.469008-0.000576j
[2025-09-04 23:42:25] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -51.327107+0.003508j
[2025-09-04 23:42:35] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -51.288546+0.000557j
[2025-09-04 23:42:45] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -51.301934-0.002393j
[2025-09-04 23:42:55] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -51.218560+0.001059j
[2025-09-04 23:43:06] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -51.124093+0.001761j
[2025-09-04 23:43:16] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -51.220533+0.001010j
[2025-09-04 23:43:26] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -51.282671-0.003606j
[2025-09-04 23:43:36] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -51.291438+0.008118j
[2025-09-04 23:43:46] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -51.346954+0.005636j
[2025-09-04 23:43:56] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -51.306961+0.002861j
[2025-09-04 23:44:07] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -51.216737+0.003032j
[2025-09-04 23:44:17] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -51.361681+0.001827j
[2025-09-04 23:44:27] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -51.097418-0.001484j
[2025-09-04 23:44:37] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -51.260350-0.002336j
[2025-09-04 23:44:47] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -51.227114+0.002117j
[2025-09-04 23:44:58] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -51.251979-0.004700j
[2025-09-04 23:45:08] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -51.161550+0.003078j
[2025-09-04 23:45:18] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -51.245485+0.000513j
[2025-09-04 23:45:28] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -51.214935+0.002006j
[2025-09-04 23:45:38] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -51.259857+0.000646j
[2025-09-04 23:45:48] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -51.325293-0.004189j
[2025-09-04 23:45:59] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -51.386275-0.002525j
[2025-09-04 23:46:09] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -51.317814+0.004300j
[2025-09-04 23:46:09] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-04 23:46:19] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -51.382438+0.000013j
[2025-09-04 23:46:29] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -51.412617-0.002377j
[2025-09-04 23:46:39] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -51.277798+0.002047j
[2025-09-04 23:46:49] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -51.309809+0.001035j
[2025-09-04 23:47:00] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -51.279097-0.000542j
[2025-09-04 23:47:10] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -51.386137+0.000330j
[2025-09-04 23:47:20] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -51.347228+0.000711j
[2025-09-04 23:47:30] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -51.354203-0.002782j
[2025-09-04 23:47:40] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -51.412342+0.002521j
[2025-09-04 23:47:50] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -51.392900+0.000471j
[2025-09-04 23:48:01] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -51.373947+0.001569j
[2025-09-04 23:48:11] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -51.344688-0.000979j
[2025-09-04 23:48:21] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -51.428084+0.000985j
[2025-09-04 23:48:31] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -51.471025-0.001079j
[2025-09-04 23:48:41] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -51.324876-0.007054j
[2025-09-04 23:48:51] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -51.300970+0.003048j
[2025-09-04 23:49:02] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -51.393311-0.001965j
[2025-09-04 23:49:12] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -51.412766-0.004763j
[2025-09-04 23:49:22] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -51.464667+0.000142j
[2025-09-04 23:49:32] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -51.322436-0.001453j
[2025-09-04 23:49:42] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -51.468706-0.000689j
[2025-09-04 23:49:53] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -51.362541-0.006305j
[2025-09-04 23:50:03] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -51.267559-0.001817j
[2025-09-04 23:50:13] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -51.364021+0.000397j
[2025-09-04 23:50:23] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -51.307766-0.000319j
[2025-09-04 23:50:33] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -51.353804-0.000915j
[2025-09-04 23:50:43] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -51.319161+0.002730j
[2025-09-04 23:50:54] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -51.375536+0.000927j
[2025-09-04 23:51:04] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -51.462428-0.000972j
[2025-09-04 23:51:14] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -51.410123+0.000182j
[2025-09-04 23:51:24] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -51.403359+0.003501j
[2025-09-04 23:51:34] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -51.432076+0.002729j
[2025-09-04 23:51:44] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -51.495845+0.000057j
[2025-09-04 23:51:55] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -51.433737+0.002885j
[2025-09-04 23:52:05] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -51.429550-0.002029j
[2025-09-04 23:52:15] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -51.424018+0.003482j
[2025-09-04 23:52:25] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -51.475836+0.000771j
[2025-09-04 23:52:35] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -51.291505-0.002028j
[2025-09-04 23:52:46] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -51.329702-0.004699j
[2025-09-04 23:52:56] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -51.256654-0.004265j
[2025-09-04 23:53:06] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -51.363221-0.002607j
[2025-09-04 23:53:16] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -51.343886-0.001869j
[2025-09-04 23:53:26] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -51.209205-0.003866j
[2025-09-04 23:53:36] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -51.434337-0.002845j
[2025-09-04 23:53:47] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -51.349595-0.002807j
[2025-09-04 23:53:57] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -51.421608-0.002292j
[2025-09-04 23:54:07] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -51.415531+0.002287j
[2025-09-04 23:54:17] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -51.509201-0.002015j
[2025-09-04 23:54:27] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -51.413314-0.001741j
[2025-09-04 23:54:37] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -51.371999+0.004570j
[2025-09-04 23:54:48] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -51.366453-0.003049j
[2025-09-04 23:54:58] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -51.283814-0.002760j
[2025-09-04 23:55:08] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -51.410704-0.002319j
[2025-09-04 23:55:18] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -51.413673-0.004377j
[2025-09-04 23:55:28] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -51.246043-0.002278j
[2025-09-04 23:55:39] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -51.251500+0.002406j
[2025-09-04 23:55:49] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -51.281260-0.001640j
[2025-09-04 23:55:59] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -51.252885+0.003884j
[2025-09-04 23:56:09] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -51.332436+0.001339j
[2025-09-04 23:56:19] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -51.320916-0.001401j
[2025-09-04 23:56:29] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -51.404264-0.003132j
[2025-09-04 23:56:40] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -51.439508-0.000821j
[2025-09-04 23:56:50] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -51.384836-0.003108j
[2025-09-04 23:57:00] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -51.421580-0.003081j
[2025-09-04 23:57:10] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -51.442251-0.000864j
[2025-09-04 23:57:20] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -51.480766+0.005489j
[2025-09-04 23:57:31] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -51.485099-0.000400j
[2025-09-04 23:57:41] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -51.384995-0.001499j
[2025-09-04 23:57:51] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -51.424934+0.001284j
[2025-09-04 23:58:01] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -51.413697-0.001476j
[2025-09-04 23:58:11] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -51.334635-0.000729j
[2025-09-04 23:58:21] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -51.384765-0.002436j
[2025-09-04 23:58:32] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -51.382432-0.002163j
[2025-09-04 23:58:42] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -51.366872+0.004602j
[2025-09-04 23:58:52] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -51.245356+0.000328j
[2025-09-04 23:59:02] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -51.322256-0.004102j
[2025-09-04 23:59:12] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -51.314027+0.000674j
[2025-09-04 23:59:23] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -51.378056-0.000506j
[2025-09-04 23:59:33] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -51.342995+0.002614j
[2025-09-04 23:59:43] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -51.313957-0.000172j
[2025-09-04 23:59:53] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -51.372103+0.000284j
[2025-09-05 00:00:03] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -51.383540+0.000090j
[2025-09-05 00:00:14] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -51.346513-0.001787j
[2025-09-05 00:00:24] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -51.379491+0.001611j
[2025-09-05 00:00:34] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -51.331923-0.000507j
[2025-09-05 00:00:44] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -51.253897+0.001898j
[2025-09-05 00:00:54] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -51.300173-0.001914j
[2025-09-05 00:01:04] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -51.168299+0.000702j
[2025-09-05 00:01:15] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -51.081959+0.001011j
[2025-09-05 00:01:25] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -51.241492+0.002866j
[2025-09-05 00:01:35] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -51.376712-0.000942j
[2025-09-05 00:01:46] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -51.503077-0.001614j
[2025-09-05 00:01:56] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -51.480990+0.002497j
[2025-09-05 00:02:06] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -51.398272+0.000179j
[2025-09-05 00:02:16] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -51.380173+0.001904j
[2025-09-05 00:02:26] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -51.356607+0.007700j
[2025-09-05 00:02:36] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -51.351244+0.005012j
[2025-09-05 00:02:47] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -51.340378+0.000130j
[2025-09-05 00:02:57] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -51.486611-0.000929j
[2025-09-05 00:03:07] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -51.414448+0.001335j
[2025-09-05 00:03:17] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -51.287991-0.001300j
[2025-09-05 00:03:27] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -51.245189-0.003621j
[2025-09-05 00:03:38] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -51.339830-0.000127j
[2025-09-05 00:03:48] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -51.316256-0.003765j
[2025-09-05 00:03:58] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -51.287272-0.001538j
[2025-09-05 00:03:58] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-05 00:04:08] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -51.217192-0.000418j
[2025-09-05 00:04:18] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -51.392650-0.004725j
[2025-09-05 00:04:28] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -51.401851-0.003563j
[2025-09-05 00:04:39] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -51.392952+0.000759j
[2025-09-05 00:04:49] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -51.367046-0.001223j
[2025-09-05 00:04:59] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -51.411167-0.002374j
[2025-09-05 00:05:09] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -51.363964-0.002654j
[2025-09-05 00:05:19] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -51.363994-0.001341j
[2025-09-05 00:05:29] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -51.351060+0.000121j
[2025-09-05 00:05:40] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -51.289027+0.002550j
[2025-09-05 00:05:50] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -51.326069+0.004399j
[2025-09-05 00:06:00] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -51.322098-0.000347j
[2025-09-05 00:06:10] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -51.418377+0.003960j
[2025-09-05 00:06:20] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -51.256358+0.005362j
[2025-09-05 00:06:31] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -51.286642-0.002540j
[2025-09-05 00:06:41] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -51.296949-0.002779j
[2025-09-05 00:06:51] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -51.411065-0.000418j
[2025-09-05 00:07:01] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -51.415926-0.001989j
[2025-09-05 00:07:11] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -51.442580+0.005671j
[2025-09-05 00:07:22] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -51.497614-0.000211j
[2025-09-05 00:07:32] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -51.410644+0.004972j
[2025-09-05 00:07:42] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -51.450954+0.000602j
[2025-09-05 00:07:52] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -51.473968-0.003975j
[2025-09-05 00:08:02] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -51.349492-0.000130j
[2025-09-05 00:08:12] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -51.520497-0.000962j
[2025-09-05 00:08:23] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -51.462795-0.002066j
[2025-09-05 00:08:33] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -51.383208-0.001978j
[2025-09-05 00:08:43] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -51.423505-0.001556j
[2025-09-05 00:08:53] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -51.443671+0.001596j
[2025-09-05 00:09:03] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -51.498534+0.005853j
[2025-09-05 00:09:14] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -51.351205+0.001487j
[2025-09-05 00:09:24] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -51.463621+0.007297j
[2025-09-05 00:09:34] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -51.523217+0.002185j
[2025-09-05 00:09:44] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -51.516386-0.001934j
[2025-09-05 00:09:54] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -51.401406-0.000153j
[2025-09-05 00:10:04] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -51.307691-0.001820j
[2025-09-05 00:10:15] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -51.360539-0.001329j
[2025-09-05 00:10:25] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -51.329058-0.001936j
[2025-09-05 00:10:35] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -51.418060+0.001812j
[2025-09-05 00:10:45] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -51.364047-0.000319j
[2025-09-05 00:10:55] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -51.302272+0.001406j
[2025-09-05 00:11:05] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -51.303017+0.000304j
[2025-09-05 00:11:16] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -51.373570-0.001584j
[2025-09-05 00:11:26] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -51.420467-0.001172j
[2025-09-05 00:11:36] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -51.326638-0.003733j
[2025-09-05 00:11:46] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -51.344329+0.001873j
[2025-09-05 00:11:56] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -51.352653-0.000693j
[2025-09-05 00:12:06] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -51.422140-0.000445j
[2025-09-05 00:12:17] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -51.353163+0.000305j
[2025-09-05 00:12:27] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -51.301507-0.002982j
[2025-09-05 00:12:37] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -51.417635-0.000621j
[2025-09-05 00:12:47] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -51.405525-0.002984j
[2025-09-05 00:12:57] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -51.372738+0.000481j
[2025-09-05 00:13:08] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -51.386170+0.000604j
[2025-09-05 00:13:18] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -51.423770+0.000976j
[2025-09-05 00:13:28] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -51.472333+0.000513j
[2025-09-05 00:13:38] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -51.313614+0.001828j
[2025-09-05 00:13:48] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -51.475123+0.000804j
[2025-09-05 00:13:58] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -51.268339-0.000084j
[2025-09-05 00:14:09] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -51.201070-0.000273j
[2025-09-05 00:14:19] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -51.295616+0.000038j
[2025-09-05 00:14:29] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -51.340311+0.003614j
[2025-09-05 00:14:39] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -51.448257+0.001240j
[2025-09-05 00:14:49] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -51.394484-0.003088j
[2025-09-05 00:15:00] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -51.383649+0.001605j
[2025-09-05 00:15:10] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -51.241335+0.001226j
[2025-09-05 00:15:20] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -51.222989+0.001693j
[2025-09-05 00:15:30] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -51.375436+0.001240j
[2025-09-05 00:15:40] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -51.406106-0.001229j
[2025-09-05 00:15:50] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -51.318737-0.001170j
[2025-09-05 00:16:01] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -51.448331+0.003647j
[2025-09-05 00:16:11] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -51.379730+0.001578j
[2025-09-05 00:16:21] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -51.450596-0.004124j
[2025-09-05 00:16:31] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -51.488774+0.000246j
[2025-09-05 00:16:41] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -51.398896-0.004486j
[2025-09-05 00:16:51] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -51.345845-0.001211j
[2025-09-05 00:17:02] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -51.356614+0.001155j
[2025-09-05 00:17:12] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -51.450058+0.003167j
[2025-09-05 00:17:22] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -51.410433-0.001024j
[2025-09-05 00:17:32] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -51.328916-0.001243j
[2025-09-05 00:17:42] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -51.304148+0.004400j
[2025-09-05 00:17:53] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -51.277525+0.001876j
[2025-09-05 00:18:03] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -51.233013+0.000139j
[2025-09-05 00:18:13] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -51.229515-0.004713j
[2025-09-05 00:18:23] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -51.448576+0.002631j
[2025-09-05 00:18:33] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -51.344540+0.004319j
[2025-09-05 00:18:43] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -51.450366-0.001510j
[2025-09-05 00:18:54] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -51.277736-0.000034j
[2025-09-05 00:19:04] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -51.371686-0.003124j
[2025-09-05 00:19:14] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -51.424720-0.001000j
[2025-09-05 00:19:24] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -51.461058+0.001416j
[2025-09-05 00:19:34] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -51.442942+0.003945j
[2025-09-05 00:19:45] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -51.386213+0.000868j
[2025-09-05 00:19:55] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -51.472647+0.001202j
[2025-09-05 00:20:05] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -51.231472+0.000928j
[2025-09-05 00:20:15] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -51.395765+0.000176j
[2025-09-05 00:20:25] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -51.407051+0.000531j
[2025-09-05 00:20:36] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -51.311666-0.005039j
[2025-09-05 00:20:46] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -51.345209-0.001732j
[2025-09-05 00:20:56] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -51.321511+0.001135j
[2025-09-05 00:21:06] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -51.399914-0.002069j
[2025-09-05 00:21:16] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -51.245781+0.002245j
[2025-09-05 00:21:27] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -51.376847-0.002978j
[2025-09-05 00:21:37] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -51.471703-0.002092j
[2025-09-05 00:21:47] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -51.544205-0.001058j
[2025-09-05 00:21:47] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-05 00:21:57] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -51.459580+0.002454j
[2025-09-05 00:22:07] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -51.410708+0.000039j
[2025-09-05 00:22:17] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -51.252211-0.003110j
[2025-09-05 00:22:28] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -51.263623+0.004555j
[2025-09-05 00:22:38] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -51.197027+0.000276j
[2025-09-05 00:22:48] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -51.328878-0.004439j
[2025-09-05 00:22:58] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -51.399437-0.002835j
[2025-09-05 00:23:08] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -51.431869-0.002814j
[2025-09-05 00:23:19] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -51.415220-0.000065j
[2025-09-05 00:23:29] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -51.340664-0.003386j
[2025-09-05 00:23:39] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -51.424503-0.001735j
[2025-09-05 00:23:49] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -51.388882+0.000771j
[2025-09-05 00:23:59] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -51.293911+0.003167j
[2025-09-05 00:24:09] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -51.451169-0.005317j
[2025-09-05 00:24:20] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -51.495042+0.001384j
[2025-09-05 00:24:30] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -51.412920-0.001813j
[2025-09-05 00:24:40] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -51.405882+0.005231j
[2025-09-05 00:24:50] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -51.459855+0.003015j
[2025-09-05 00:25:00] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -51.366413-0.000395j
[2025-09-05 00:25:11] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -51.486759+0.003036j
[2025-09-05 00:25:21] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -51.432053+0.002548j
[2025-09-05 00:25:31] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -51.355926+0.000273j
[2025-09-05 00:25:41] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -51.470215-0.003654j
[2025-09-05 00:25:51] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -51.369614+0.000181j
[2025-09-05 00:26:01] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -51.327241-0.004540j
[2025-09-05 00:26:12] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -51.347925-0.003827j
[2025-09-05 00:26:22] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -51.243517-0.006408j
[2025-09-05 00:26:32] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -51.271162+0.005029j
[2025-09-05 00:26:42] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -51.315243-0.001524j
[2025-09-05 00:26:51] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -51.333426-0.000845j
[2025-09-05 00:27:01] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -51.329655+0.001454j
[2025-09-05 00:27:12] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -51.392084-0.005231j
[2025-09-05 00:27:22] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -51.361426+0.000921j
[2025-09-05 00:27:32] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -51.431541+0.002898j
[2025-09-05 00:27:42] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -51.457347+0.000201j
[2025-09-05 00:27:52] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -51.395568-0.000178j
[2025-09-05 00:28:02] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -51.469131+0.000553j
[2025-09-05 00:28:13] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -51.361818-0.000963j
[2025-09-05 00:28:23] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -51.487548+0.002612j
[2025-09-05 00:28:33] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -51.320646-0.002171j
[2025-09-05 00:28:43] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -51.387775+0.005683j
[2025-09-05 00:28:53] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -51.265933-0.004803j
[2025-09-05 00:29:04] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -51.453315+0.005545j
[2025-09-05 00:29:14] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -51.387633+0.000603j
[2025-09-05 00:29:24] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -51.283945+0.002285j
[2025-09-05 00:29:34] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -51.193673-0.001658j
[2025-09-05 00:29:44] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -51.281711-0.001271j
[2025-09-05 00:29:54] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -51.198255-0.000209j
[2025-09-05 00:30:04] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -51.321343+0.002711j
[2025-09-05 00:30:15] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -51.309258+0.005203j
[2025-09-05 00:30:25] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -51.251434-0.004887j
[2025-09-05 00:30:35] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -51.376482-0.002834j
[2025-09-05 00:30:45] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -51.266904-0.002402j
[2025-09-05 00:30:55] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -51.305800+0.002433j
[2025-09-05 00:31:06] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -51.329500-0.000123j
[2025-09-05 00:31:16] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -51.385297+0.000287j
[2025-09-05 00:31:26] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -51.310688-0.001514j
[2025-09-05 00:31:36] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -51.294137+0.000864j
[2025-09-05 00:31:47] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -51.343089+0.006954j
[2025-09-05 00:31:57] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -51.336553-0.002042j
[2025-09-05 00:32:07] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -51.404286+0.000953j
[2025-09-05 00:32:17] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -51.470786-0.001336j
[2025-09-05 00:32:27] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -51.367536-0.000291j
[2025-09-05 00:32:38] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -51.373993-0.001489j
[2025-09-05 00:32:48] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -51.385102+0.000996j
[2025-09-05 00:32:58] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -51.471044-0.000130j
[2025-09-05 00:33:08] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -51.417352-0.003890j
[2025-09-05 00:33:18] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -51.391301-0.004707j
[2025-09-05 00:33:28] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -51.388342+0.000023j
[2025-09-05 00:33:39] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -51.506218+0.000285j
[2025-09-05 00:33:49] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -51.549169+0.001815j
[2025-09-05 00:33:59] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -51.531337+0.000237j
[2025-09-05 00:34:09] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -51.462443-0.003152j
[2025-09-05 00:34:19] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -51.433487+0.001997j
[2025-09-05 00:34:29] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -51.420636-0.001157j
[2025-09-05 00:34:40] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -51.410550+0.002832j
[2025-09-05 00:34:50] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -51.235368-0.000287j
[2025-09-05 00:35:00] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -51.453710+0.002739j
[2025-09-05 00:35:10] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -51.414048-0.002772j
[2025-09-05 00:35:20] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -51.341542+0.000933j
[2025-09-05 00:35:30] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -51.323756+0.000361j
[2025-09-05 00:35:41] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -51.245870-0.001075j
[2025-09-05 00:35:51] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -51.368601+0.005729j
[2025-09-05 00:36:01] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -51.441740+0.003045j
[2025-09-05 00:36:11] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -51.346313-0.000493j
[2025-09-05 00:36:21] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -51.382046-0.003031j
[2025-09-05 00:36:31] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -51.429868-0.002287j
[2025-09-05 00:36:42] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -51.428548-0.001760j
[2025-09-05 00:36:52] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -51.322683+0.000871j
[2025-09-05 00:37:02] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -51.329971+0.000738j
[2025-09-05 00:37:12] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -51.420279+0.001149j
[2025-09-05 00:37:22] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -51.349934+0.001654j
[2025-09-05 00:37:33] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -51.268748+0.000768j
[2025-09-05 00:37:43] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -51.341433-0.002062j
[2025-09-05 00:37:53] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -51.385503+0.000517j
[2025-09-05 00:38:03] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -51.444247+0.000074j
[2025-09-05 00:38:13] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -51.423935-0.000613j
[2025-09-05 00:38:23] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -51.336712-0.002426j
[2025-09-05 00:38:34] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -51.274446-0.004890j
[2025-09-05 00:38:44] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -51.380869-0.001324j
[2025-09-05 00:38:54] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -51.427300+0.000890j
[2025-09-05 00:39:04] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -51.349355+0.004397j
[2025-09-05 00:39:14] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -51.439013+0.001398j
[2025-09-05 00:39:24] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -51.372982+0.000427j
[2025-09-05 00:39:35] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -51.347762-0.001050j
[2025-09-05 00:39:35] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-05 00:39:45] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -51.270521-0.002752j
[2025-09-05 00:39:55] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -51.265011-0.002276j
[2025-09-05 00:40:05] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -51.369213+0.002084j
[2025-09-05 00:40:15] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -51.349052+0.001924j
[2025-09-05 00:40:26] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -51.379340-0.001183j
[2025-09-05 00:40:36] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -51.476697-0.003041j
[2025-09-05 00:40:46] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -51.349816+0.000003j
[2025-09-05 00:40:56] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -51.278124-0.000267j
[2025-09-05 00:41:06] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -51.456266+0.004019j
[2025-09-05 00:41:16] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -51.420316-0.000483j
[2025-09-05 00:41:27] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -51.387586-0.001488j
[2025-09-05 00:41:37] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -51.324569+0.000755j
[2025-09-05 00:41:47] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -51.251860+0.001941j
[2025-09-05 00:41:57] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -51.211779-0.000215j
[2025-09-05 00:42:07] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -51.306182-0.007438j
[2025-09-05 00:42:17] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -51.384607-0.003741j
[2025-09-05 00:42:28] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -51.351656-0.006265j
[2025-09-05 00:42:38] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -51.313613-0.001387j
[2025-09-05 00:42:48] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -51.344459+0.000409j
[2025-09-05 00:42:58] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -51.394352-0.000004j
[2025-09-05 00:43:08] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -51.314447+0.002921j
[2025-09-05 00:43:18] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -51.261152+0.004669j
[2025-09-05 00:43:29] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -51.405065+0.004865j
[2025-09-05 00:43:39] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -51.334251+0.001127j
[2025-09-05 00:43:49] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -51.160090-0.002197j
[2025-09-05 00:43:59] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -51.363184-0.001513j
[2025-09-05 00:44:09] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -51.305732+0.002244j
[2025-09-05 00:44:20] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -51.318643-0.000049j
[2025-09-05 00:44:30] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -51.379493-0.002371j
[2025-09-05 00:44:40] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -51.293681+0.005110j
[2025-09-05 00:44:50] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -51.366450+0.000568j
[2025-09-05 00:45:00] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -51.254147-0.003147j
[2025-09-05 00:45:10] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -51.301365+0.000015j
[2025-09-05 00:45:21] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -51.235578-0.004020j
[2025-09-05 00:45:31] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -51.206942+0.002746j
[2025-09-05 00:45:41] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -51.307553+0.002105j
[2025-09-05 00:45:51] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -51.300603-0.001268j
[2025-09-05 00:46:01] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -51.330569-0.000625j
[2025-09-05 00:46:11] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -51.320631+0.002206j
[2025-09-05 00:46:22] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -51.309498+0.003070j
[2025-09-05 00:46:32] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -51.228102-0.002753j
[2025-09-05 00:46:42] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -51.231942-0.000169j
[2025-09-05 00:46:52] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -51.182367-0.000111j
[2025-09-05 00:47:02] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -51.284545-0.000896j
[2025-09-05 00:47:13] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -51.314516-0.002905j
[2025-09-05 00:47:23] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -51.509212-0.002262j
[2025-09-05 00:47:33] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -51.422161+0.000301j
[2025-09-05 00:47:43] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -51.541799-0.001539j
[2025-09-05 00:47:53] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -51.341434-0.004184j
[2025-09-05 00:48:03] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -51.299009+0.001078j
[2025-09-05 00:48:14] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -51.347978+0.002119j
[2025-09-05 00:48:24] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -51.394648+0.002485j
[2025-09-05 00:48:34] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -51.393137+0.000896j
[2025-09-05 00:48:44] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -51.419405-0.007284j
[2025-09-05 00:48:54] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -51.524440-0.001523j
[2025-09-05 00:49:04] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -51.420669+0.004552j
[2025-09-05 00:49:15] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -51.377932-0.003187j
[2025-09-05 00:49:25] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -51.481505+0.003872j
[2025-09-05 00:49:35] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -51.467548-0.000968j
[2025-09-05 00:49:45] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -51.488879+0.003000j
[2025-09-05 00:49:55] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -51.527106+0.001710j
[2025-09-05 00:50:05] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -51.470261+0.003073j
[2025-09-05 00:50:16] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -51.374680-0.000260j
[2025-09-05 00:50:26] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -51.312127+0.001309j
[2025-09-05 00:50:36] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -51.338985+0.002177j
[2025-09-05 00:50:46] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -51.330312-0.000504j
[2025-09-05 00:50:56] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -51.258504+0.004728j
[2025-09-05 00:51:06] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -51.282374+0.001364j
[2025-09-05 00:51:17] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -51.222022+0.000685j
[2025-09-05 00:51:27] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -51.213719-0.000290j
[2025-09-05 00:51:37] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -51.298169-0.001729j
[2025-09-05 00:51:47] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -51.232061-0.001303j
[2025-09-05 00:51:57] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -51.335565-0.000481j
[2025-09-05 00:52:08] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -51.346210-0.000370j
[2025-09-05 00:52:18] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -51.262411-0.002224j
[2025-09-05 00:52:28] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -51.318395+0.003398j
[2025-09-05 00:52:38] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -51.217139+0.001931j
[2025-09-05 00:52:48] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -51.234486+0.004600j
[2025-09-05 00:52:58] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -51.270965+0.002999j
[2025-09-05 00:53:09] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -51.276257+0.001922j
[2025-09-05 00:53:19] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -51.320420-0.002375j
[2025-09-05 00:53:29] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -51.316764+0.000368j
[2025-09-05 00:53:39] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -51.423168-0.001786j
[2025-09-05 00:53:49] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -51.212596-0.001719j
[2025-09-05 00:54:00] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -51.343628-0.001562j
[2025-09-05 00:54:10] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -51.429201-0.000594j
[2025-09-05 00:54:20] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -51.324425-0.000541j
[2025-09-05 00:54:30] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -51.173369-0.006065j
[2025-09-05 00:54:40] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -51.161569+0.000447j
[2025-09-05 00:54:50] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -51.236197-0.002796j
[2025-09-05 00:55:01] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -51.248378-0.001507j
[2025-09-05 00:55:11] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -51.323729+0.002895j
[2025-09-05 00:55:21] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -51.364700+0.002946j
[2025-09-05 00:55:31] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -51.253680+0.000095j
[2025-09-05 00:55:41] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -51.398176-0.003542j
[2025-09-05 00:55:51] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -51.225309+0.000762j
[2025-09-05 00:56:02] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -51.384495+0.006476j
[2025-09-05 00:56:12] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -51.376325-0.000111j
[2025-09-05 00:56:22] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -51.339593+0.000491j
[2025-09-05 00:56:32] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -51.502459-0.000796j
[2025-09-05 00:56:42] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -51.335211-0.001931j
[2025-09-05 00:56:53] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -51.405875-0.001270j
[2025-09-05 00:57:03] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -51.393375-0.001458j
[2025-09-05 00:57:13] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -51.388616-0.002349j
[2025-09-05 00:57:23] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -51.233486+0.000396j
[2025-09-05 00:57:23] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-05 00:57:23] ✅ Training completed | Restarts: 2
[2025-09-05 00:57:23] ============================================================
[2025-09-05 00:57:23] Training completed | Runtime: 10739.2s
[2025-09-05 00:57:28] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-05 00:57:28] ============================================================
