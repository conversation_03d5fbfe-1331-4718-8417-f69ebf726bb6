[2025-09-06 01:29:54] 使用checkpoint文件: results/L=4/J2=0.05/J1=0.03/training/checkpoints/checkpoint_iter_000210.pkl
[2025-09-06 01:30:12] ✓ 从checkpoint加载参数: 210
[2025-09-06 01:30:12]   - 能量: -51.306925+0.005181j ± 0.081181
[2025-09-06 01:30:12] ================================================================================
[2025-09-06 01:30:12] 加载量子态: L=4, J2=0.05, J1=0.03, checkpoint=checkpoint_iter_000210
[2025-09-06 01:30:12] 使用采样数目: 1048576
[2025-09-06 01:30:12] 设置样本数为: 1048576
[2025-09-06 01:30:12] 开始生成共享样本集...
[2025-09-06 01:34:39] 样本生成完成,耗时: 266.768 秒
[2025-09-06 01:34:39] ================================================================================
[2025-09-06 01:34:39] 开始计算自旋结构因子...
[2025-09-06 01:34:39] 初始化操作符缓存...
[2025-09-06 01:34:39] 预构建所有自旋相关操作符...
[2025-09-06 01:34:39] 开始计算自旋相关函数...
[2025-09-06 01:34:52] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 13.509s
[2025-09-06 01:35:11] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 18.428s
[2025-09-06 01:35:25] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 13.993s
[2025-09-06 01:35:39] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 14.005s
[2025-09-06 01:35:53] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 14.009s
[2025-09-06 01:36:07] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 14.002s
[2025-09-06 01:36:21] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 13.998s
[2025-09-06 01:36:35] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 14.001s
[2025-09-06 01:36:49] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 13.990s
[2025-09-06 01:37:03] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 14.000s
[2025-09-06 01:37:17] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 13.988s
[2025-09-06 01:37:31] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 14.004s
[2025-09-06 01:37:45] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 13.987s
[2025-09-06 01:37:59] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 14.002s
[2025-09-06 01:38:13] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 14.000s
[2025-09-06 01:38:27] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 13.990s
[2025-09-06 01:38:41] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 14.015s
[2025-09-06 01:38:55] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 13.999s
[2025-09-06 01:39:09] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 13.986s
[2025-09-06 01:39:23] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 13.999s
[2025-09-06 01:39:37] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 14.004s
[2025-09-06 01:39:51] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 14.002s
[2025-09-06 01:40:05] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 14.006s
[2025-09-06 01:40:19] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 13.998s
[2025-09-06 01:40:33] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 13.986s
[2025-09-06 01:40:47] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 14.002s
[2025-09-06 01:41:01] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 14.007s
[2025-09-06 01:41:15] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 14.010s
[2025-09-06 01:41:29] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 13.988s
[2025-09-06 01:41:43] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 13.989s
[2025-09-06 01:41:57] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 13.993s
[2025-09-06 01:42:11] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 14.000s
[2025-09-06 01:42:25] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 14.000s
[2025-09-06 01:42:39] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 14.005s
[2025-09-06 01:42:53] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 13.993s
[2025-09-06 01:43:07] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 14.004s
[2025-09-06 01:43:21] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 13.986s
[2025-09-06 01:43:35] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 13.998s
[2025-09-06 01:43:49] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 14.003s
[2025-09-06 01:44:03] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 14.006s
[2025-09-06 01:44:17] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 14.011s
[2025-09-06 01:44:31] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 13.996s
[2025-09-06 01:44:45] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 13.988s
[2025-09-06 01:44:59] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 14.000s
[2025-09-06 01:45:13] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 13.988s
[2025-09-06 01:45:27] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 13.988s
[2025-09-06 01:45:41] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 14.002s
[2025-09-06 01:45:55] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 13.988s
[2025-09-06 01:46:09] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 14.001s
[2025-09-06 01:46:23] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 14.001s
[2025-09-06 01:46:37] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 14.002s
[2025-09-06 01:46:51] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 14.004s
[2025-09-06 01:47:05] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 14.010s
[2025-09-06 01:47:19] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 13.998s
[2025-09-06 01:47:33] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 13.916s
[2025-09-06 01:47:47] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 13.977s
[2025-09-06 01:48:01] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 14.002s
[2025-09-06 01:48:15] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 14.002s
[2025-09-06 01:48:29] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 14.001s
[2025-09-06 01:48:43] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 14.000s
[2025-09-06 01:48:57] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 13.988s
[2025-09-06 01:49:11] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 14.003s
[2025-09-06 01:49:25] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 14.003s
[2025-09-06 01:49:39] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 13.998s
[2025-09-06 01:49:39] 自旋相关函数计算完成,总耗时 900.04 秒
[2025-09-06 01:49:41] 计算傅里叶变换...
[2025-09-06 01:49:44] 自旋结构因子计算完成
[2025-09-06 01:49:45] 自旋相关函数平均误差: 0.000674
