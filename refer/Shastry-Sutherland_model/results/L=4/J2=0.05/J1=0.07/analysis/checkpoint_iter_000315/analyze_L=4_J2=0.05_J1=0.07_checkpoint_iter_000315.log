[2025-09-06 16:05:04] 使用checkpoint文件: results/L=4/J2=0.05/J1=0.07/training/checkpoints/checkpoint_iter_000315.pkl
[2025-09-06 16:05:20] ✓ 从checkpoint加载参数: 315
[2025-09-06 16:05:20]   - 能量: -52.819649-0.003941j ± 0.080914
[2025-09-06 16:05:20] ================================================================================
[2025-09-06 16:05:20] 加载量子态: L=4, J2=0.05, J1=0.07, checkpoint=checkpoint_iter_000315
[2025-09-06 16:05:20] 使用采样数目: 1048576
[2025-09-06 16:05:20] 设置样本数为: 1048576
[2025-09-06 16:05:20] 开始生成共享样本集...
[2025-09-06 16:09:46] 样本生成完成,耗时: 265.842 秒
[2025-09-06 16:09:46] ================================================================================
[2025-09-06 16:09:46] 开始计算自旋结构因子...
[2025-09-06 16:09:46] 初始化操作符缓存...
[2025-09-06 16:09:46] 预构建所有自旋相关操作符...
[2025-09-06 16:09:46] 开始计算自旋相关函数...
[2025-09-06 16:09:59] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 13.422s
[2025-09-06 16:10:18] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 18.613s
[2025-09-06 16:10:32] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 14.018s
[2025-09-06 16:10:46] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 14.044s
[2025-09-06 16:11:00] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 14.047s
[2025-09-06 16:11:14] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 14.022s
[2025-09-06 16:11:28] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 14.014s
[2025-09-06 16:11:42] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 14.062s
[2025-09-06 16:11:56] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 14.017s
[2025-09-06 16:12:10] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 14.038s
[2025-09-06 16:12:24] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 14.025s
[2025-09-06 16:12:38] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 14.032s
[2025-09-06 16:12:52] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 14.015s
[2025-09-06 16:13:06] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 14.026s
[2025-09-06 16:13:20] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 14.025s
[2025-09-06 16:13:34] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 14.030s
[2025-09-06 16:13:48] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 14.026s
[2025-09-06 16:14:02] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 14.028s
[2025-09-06 16:14:16] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 14.015s
[2025-09-06 16:14:30] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 14.030s
[2025-09-06 16:14:44] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 14.024s
[2025-09-06 16:14:59] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 14.064s
[2025-09-06 16:15:13] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 14.024s
[2025-09-06 16:15:27] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 14.028s
[2025-09-06 16:15:41] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 14.025s
[2025-09-06 16:15:55] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 14.054s
[2025-09-06 16:16:09] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 14.012s
[2025-09-06 16:16:23] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 14.058s
[2025-09-06 16:16:37] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 14.014s
[2025-09-06 16:16:51] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 14.021s
[2025-09-06 16:17:05] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 14.026s
[2025-09-06 16:17:19] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 14.025s
[2025-09-06 16:17:33] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 14.031s
[2025-09-06 16:17:47] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 14.031s
[2025-09-06 16:18:01] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 14.024s
[2025-09-06 16:18:15] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 14.022s
[2025-09-06 16:18:29] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 14.009s
[2025-09-06 16:18:43] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 14.025s
[2025-09-06 16:18:57] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 14.014s
[2025-09-06 16:19:11] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 14.052s
[2025-09-06 16:19:25] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 14.018s
[2025-09-06 16:19:39] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 14.019s
[2025-09-06 16:19:53] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 14.035s
[2025-09-06 16:20:07] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 14.031s
[2025-09-06 16:20:21] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 14.026s
[2025-09-06 16:20:35] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 14.016s
[2025-09-06 16:20:49] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 14.058s
[2025-09-06 16:21:03] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 14.020s
[2025-09-06 16:21:17] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 14.046s
[2025-09-06 16:21:31] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 14.057s
[2025-09-06 16:21:45] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 14.022s
[2025-09-06 16:22:00] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 14.055s
[2025-09-06 16:22:14] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 14.023s
[2025-09-06 16:22:28] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 14.063s
[2025-09-06 16:22:42] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 14.018s
[2025-09-06 16:22:56] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 14.052s
[2025-09-06 16:23:10] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 14.017s
[2025-09-06 16:23:24] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 14.020s
[2025-09-06 16:23:38] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 14.009s
[2025-09-06 16:23:52] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 14.059s
[2025-09-06 16:24:06] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 14.027s
[2025-09-06 16:24:20] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 14.016s
[2025-09-06 16:24:34] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 14.030s
[2025-09-06 16:24:48] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 14.018s
[2025-09-06 16:24:48] 自旋相关函数计算完成,总耗时 902.16 秒
[2025-09-06 16:24:50] 计算傅里叶变换...
[2025-09-06 16:24:53] 自旋结构因子计算完成
[2025-09-06 16:24:54] 自旋相关函数平均误差: 0.000669
