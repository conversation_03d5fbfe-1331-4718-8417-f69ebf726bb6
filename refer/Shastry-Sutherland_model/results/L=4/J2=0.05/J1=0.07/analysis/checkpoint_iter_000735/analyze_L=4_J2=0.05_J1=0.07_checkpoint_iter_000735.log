[2025-09-06 17:21:10] 使用checkpoint文件: results/L=4/J2=0.05/J1=0.07/training/checkpoints/checkpoint_iter_000735.pkl
[2025-09-06 17:21:24] ✓ 从checkpoint加载参数: 735
[2025-09-06 17:21:24]   - 能量: -52.928616+0.000201j ± 0.083713
[2025-09-06 17:21:24] ================================================================================
[2025-09-06 17:21:24] 加载量子态: L=4, J2=0.05, J1=0.07, checkpoint=checkpoint_iter_000735
[2025-09-06 17:21:24] 使用采样数目: 1048576
[2025-09-06 17:21:24] 设置样本数为: 1048576
[2025-09-06 17:21:24] 开始生成共享样本集...
[2025-09-06 17:25:50] 样本生成完成,耗时: 265.834 秒
[2025-09-06 17:25:50] ================================================================================
[2025-09-06 17:25:50] 开始计算自旋结构因子...
[2025-09-06 17:25:50] 初始化操作符缓存...
[2025-09-06 17:25:50] 预构建所有自旋相关操作符...
[2025-09-06 17:25:50] 开始计算自旋相关函数...
[2025-09-06 17:26:03] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 13.396s
[2025-09-06 17:26:22] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 18.464s
[2025-09-06 17:26:36] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 14.055s
[2025-09-06 17:26:50] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 14.016s
[2025-09-06 17:27:04] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 14.012s
[2025-09-06 17:27:18] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 14.030s
[2025-09-06 17:27:32] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 14.069s
[2025-09-06 17:27:46] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 14.017s
[2025-09-06 17:28:00] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 14.059s
[2025-09-06 17:28:14] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 14.028s
[2025-09-06 17:28:28] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 14.055s
[2025-09-06 17:28:42] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 14.024s
[2025-09-06 17:28:56] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 14.047s
[2025-09-06 17:29:10] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 14.010s
[2025-09-06 17:29:24] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 14.015s
[2025-09-06 17:29:38] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 14.052s
[2025-09-06 17:29:52] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 14.035s
[2025-09-06 17:30:07] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 14.011s
[2025-09-06 17:30:21] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 14.062s
[2025-09-06 17:30:35] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 14.028s
[2025-09-06 17:30:49] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 14.014s
[2025-09-06 17:31:03] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 14.015s
[2025-09-06 17:31:17] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 14.056s
[2025-09-06 17:31:31] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 14.020s
[2025-09-06 17:31:45] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 14.056s
[2025-09-06 17:31:59] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 14.018s
[2025-09-06 17:32:13] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 14.046s
[2025-09-06 17:32:27] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 14.015s
[2025-09-06 17:32:41] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 14.066s
[2025-09-06 17:32:55] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 14.050s
[2025-09-06 17:33:09] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 14.061s
[2025-09-06 17:33:23] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 14.017s
[2025-09-06 17:33:37] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 14.022s
[2025-09-06 17:33:51] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 14.010s
[2025-09-06 17:34:05] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 14.055s
[2025-09-06 17:34:19] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 14.016s
[2025-09-06 17:34:33] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 14.061s
[2025-09-06 17:34:47] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 14.015s
[2025-09-06 17:35:01] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 14.055s
[2025-09-06 17:35:15] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 14.018s
[2025-09-06 17:35:29] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 14.050s
[2025-09-06 17:35:44] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 14.056s
[2025-09-06 17:35:58] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 14.059s
[2025-09-06 17:36:12] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 14.016s
[2025-09-06 17:36:26] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 14.049s
[2025-09-06 17:36:40] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 14.061s
[2025-09-06 17:36:54] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 14.014s
[2025-09-06 17:37:08] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 14.054s
[2025-09-06 17:37:22] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 14.022s
[2025-09-06 17:37:36] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 14.031s
[2025-09-06 17:37:50] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 14.055s
[2025-09-06 17:38:04] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 14.040s
[2025-09-06 17:38:18] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 14.048s
[2025-09-06 17:38:32] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 14.019s
[2025-09-06 17:38:46] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 14.062s
[2025-09-06 17:39:00] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 14.037s
[2025-09-06 17:39:14] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 14.057s
[2025-09-06 17:39:28] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 14.059s
[2025-09-06 17:39:42] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 14.053s
[2025-09-06 17:39:56] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 14.026s
[2025-09-06 17:40:10] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 14.061s
[2025-09-06 17:40:24] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 14.051s
[2025-09-06 17:40:38] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 14.015s
[2025-09-06 17:40:53] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 14.062s
[2025-09-06 17:40:53] 自旋相关函数计算完成,总耗时 902.53 秒
[2025-09-06 17:40:54] 计算傅里叶变换...
[2025-09-06 17:40:58] 自旋结构因子计算完成
[2025-09-06 17:40:59] 自旋相关函数平均误差: 0.000661
