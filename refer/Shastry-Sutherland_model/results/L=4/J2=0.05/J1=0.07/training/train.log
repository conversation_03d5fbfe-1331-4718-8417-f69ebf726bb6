[2025-09-04 21:58:15] ✓ 从checkpoint恢复: results/L=4/J2=0.05/J1=0.06/training/checkpoints/final_GCNN.pkl
[2025-09-04 21:58:15]   - 迭代次数: final
[2025-09-04 21:58:15]   - 能量: -52.550134+0.002030j ± 0.085392
[2025-09-04 21:58:15]   - 时间戳: 2025-09-04T21:58:05.567173+08:00
[2025-09-04 21:58:24] ✓ 变分状态参数已从checkpoint恢复
[2025-09-04 21:58:24] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-04 21:58:24] ==================================================
[2025-09-04 21:58:24] GCNN for Shastry-Sutherland Model
[2025-09-04 21:58:24] ==================================================
[2025-09-04 21:58:24] System parameters:
[2025-09-04 21:58:24]   - System size: L=4, N=64
[2025-09-04 21:58:24]   - System parameters: J1=0.07, J2=0.05, Q=0.95
[2025-09-04 21:58:24] --------------------------------------------------
[2025-09-04 21:58:24] Model parameters:
[2025-09-04 21:58:24]   - Number of layers = 4
[2025-09-04 21:58:24]   - Number of features = 4
[2025-09-04 21:58:24]   - Total parameters = 12572
[2025-09-04 21:58:24] --------------------------------------------------
[2025-09-04 21:58:24] Training parameters:
[2025-09-04 21:58:24]   - Learning rate: 0.015
[2025-09-04 21:58:24]   - Total iterations: 1050
[2025-09-04 21:58:24]   - Annealing cycles: 3
[2025-09-04 21:58:24]   - Initial period: 150
[2025-09-04 21:58:24]   - Period multiplier: 2.0
[2025-09-04 21:58:24]   - Temperature range: 0.0-1.0
[2025-09-04 21:58:24]   - Samples: 4096
[2025-09-04 21:58:24]   - Discarded samples: 0
[2025-09-04 21:58:24]   - Chunk size: 2048
[2025-09-04 21:58:24]   - Diagonal shift: 0.2
[2025-09-04 21:58:24]   - Gradient clipping: 1.0
[2025-09-04 21:58:24]   - Checkpoint enabled: interval=105
[2025-09-04 21:58:24]   - Checkpoint directory: results/L=4/J2=0.05/J1=0.07/training/checkpoints
[2025-09-04 21:58:24] --------------------------------------------------
[2025-09-04 21:58:24] Device status:
[2025-09-04 21:58:24]   - Devices model: NVIDIA H200 NVL
[2025-09-04 21:58:24]   - Number of devices: 1
[2025-09-04 21:58:24]   - Sharding: True
[2025-09-04 21:58:24] ============================================================
[2025-09-04 21:59:10] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -52.967291-0.008692j
[2025-09-04 21:59:38] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -52.900103+0.003071j
[2025-09-04 21:59:49] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -52.733868-0.004957j
[2025-09-04 21:59:59] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -52.921369-0.004333j
[2025-09-04 22:00:09] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -52.885431-0.000940j
[2025-09-04 22:00:19] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -52.997915-0.000748j
[2025-09-04 22:00:29] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -52.841950-0.006199j
[2025-09-04 22:00:39] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -52.910279-0.002928j
[2025-09-04 22:00:50] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -52.994353+0.000375j
[2025-09-04 22:01:00] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -52.941171-0.004712j
[2025-09-04 22:01:10] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -52.977882-0.002760j
[2025-09-04 22:01:20] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -52.885041-0.002888j
[2025-09-04 22:01:30] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -53.011496-0.001602j
[2025-09-04 22:01:40] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -53.017694-0.000407j
[2025-09-04 22:01:51] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -52.978890-0.001259j
[2025-09-04 22:02:01] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -53.070311-0.000383j
[2025-09-04 22:02:11] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -52.883051-0.007978j
[2025-09-04 22:02:21] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -53.056778-0.002263j
[2025-09-04 22:02:31] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -53.069965+0.004891j
[2025-09-04 22:02:42] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -53.052889-0.002035j
[2025-09-04 22:02:52] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -53.057387-0.000667j
[2025-09-04 22:03:02] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -52.996234+0.000443j
[2025-09-04 22:03:12] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -52.987440+0.002045j
[2025-09-04 22:03:22] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -53.014477+0.005718j
[2025-09-04 22:03:33] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -52.860143+0.000664j
[2025-09-04 22:03:43] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -52.989614-0.001456j
[2025-09-04 22:03:53] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -52.980069-0.001056j
[2025-09-04 22:04:03] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -52.957683-0.003521j
[2025-09-04 22:04:14] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -53.080928-0.004546j
[2025-09-04 22:04:24] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -52.888093-0.002842j
[2025-09-04 22:04:34] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -52.977100-0.000219j
[2025-09-04 22:04:44] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -52.941415+0.002885j
[2025-09-04 22:04:54] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -52.943393-0.002555j
[2025-09-04 22:05:04] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -52.952353-0.003297j
[2025-09-04 22:05:15] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -53.007992+0.000035j
[2025-09-04 22:05:25] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -52.998139-0.000743j
[2025-09-04 22:05:35] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -53.049572+0.006222j
[2025-09-04 22:05:45] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -52.942581-0.002344j
[2025-09-04 22:05:56] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -52.895032+0.005412j
[2025-09-04 22:06:06] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -53.063653+0.006552j
[2025-09-04 22:06:16] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -53.034719+0.001016j
[2025-09-04 22:06:26] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -52.831380+0.001711j
[2025-09-04 22:06:36] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -52.840283+0.003657j
[2025-09-04 22:06:47] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -52.939083+0.003455j
[2025-09-04 22:06:57] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -52.977357-0.003129j
[2025-09-04 22:07:07] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -52.973921+0.000281j
[2025-09-04 22:07:17] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -52.896445+0.001798j
[2025-09-04 22:07:27] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -52.956176-0.004894j
[2025-09-04 22:07:38] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -52.956318+0.001970j
[2025-09-04 22:07:48] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -52.900270+0.000066j
[2025-09-04 22:07:58] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -52.922927-0.001351j
[2025-09-04 22:08:08] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -52.957134-0.004029j
[2025-09-04 22:08:18] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -52.957405-0.003179j
[2025-09-04 22:08:29] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -52.828649+0.001171j
[2025-09-04 22:08:39] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -52.903181+0.005385j
[2025-09-04 22:08:49] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -52.914871+0.002104j
[2025-09-04 22:08:59] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -52.887962-0.001114j
[2025-09-04 22:09:09] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -52.906932+0.000141j
[2025-09-04 22:09:20] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -52.888207+0.002078j
[2025-09-04 22:09:30] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -52.853600-0.002739j
[2025-09-04 22:09:40] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -52.940009+0.002971j
[2025-09-04 22:09:50] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -52.936266+0.003989j
[2025-09-04 22:10:00] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -53.079652-0.003466j
[2025-09-04 22:10:11] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -52.950104+0.000203j
[2025-09-04 22:10:21] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -53.120644+0.001043j
[2025-09-04 22:10:31] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -53.083150+0.004446j
[2025-09-04 22:10:41] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -52.998336-0.004527j
[2025-09-04 22:10:51] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -53.149014+0.000885j
[2025-09-04 22:11:02] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -53.143407-0.001379j
[2025-09-04 22:11:12] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -53.069138-0.000673j
[2025-09-04 22:11:22] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -52.944732-0.003284j
[2025-09-04 22:11:32] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -52.903308-0.000624j
[2025-09-04 22:11:42] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -53.022374-0.002697j
[2025-09-04 22:11:53] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -52.966805-0.000993j
[2025-09-04 22:12:03] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -52.959589+0.000367j
[2025-09-04 22:12:13] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -52.932219-0.000558j
[2025-09-04 22:12:23] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -52.927426-0.003854j
[2025-09-04 22:12:33] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -52.986658-0.003372j
[2025-09-04 22:12:44] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -53.015221+0.002108j
[2025-09-04 22:12:54] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -52.937532+0.002969j
[2025-09-04 22:13:04] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -52.973501-0.000350j
[2025-09-04 22:13:14] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -52.977772+0.000945j
[2025-09-04 22:13:24] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -52.938906-0.002212j
[2025-09-04 22:13:35] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -52.996948+0.002027j
[2025-09-04 22:13:45] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -52.982409-0.002284j
[2025-09-04 22:13:55] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -53.007178+0.000803j
[2025-09-04 22:14:05] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -52.894182-0.001617j
[2025-09-04 22:14:15] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -52.852379+0.003392j
[2025-09-04 22:14:26] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -52.936267+0.001066j
[2025-09-04 22:14:36] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -52.896034+0.002899j
[2025-09-04 22:14:46] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -52.869658-0.004488j
[2025-09-04 22:14:56] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -52.863728+0.000538j
[2025-09-04 22:15:06] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -52.895730+0.000815j
[2025-09-04 22:15:16] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -52.962664-0.002600j
[2025-09-04 22:15:27] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -52.931963-0.002759j
[2025-09-04 22:15:37] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -52.930241+0.000526j
[2025-09-04 22:15:47] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -53.009892+0.000605j
[2025-09-04 22:15:57] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -52.994765-0.005831j
[2025-09-04 22:16:07] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -53.055670+0.000903j
[2025-09-04 22:16:18] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -53.020120-0.001799j
[2025-09-04 22:16:28] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -52.986306-0.006642j
[2025-09-04 22:16:38] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -53.012337+0.000306j
[2025-09-04 22:16:48] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -52.991590+0.003726j
[2025-09-04 22:16:58] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -52.982930+0.000821j
[2025-09-04 22:17:09] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -53.084021+0.000612j
[2025-09-04 22:17:09] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-04 22:17:19] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -53.102697+0.004187j
[2025-09-04 22:17:29] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -52.988934+0.002803j
[2025-09-04 22:17:39] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -52.991810-0.000576j
[2025-09-04 22:17:50] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -53.036317-0.006151j
[2025-09-04 22:18:00] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -52.891601-0.003517j
[2025-09-04 22:18:10] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -52.833539-0.003364j
[2025-09-04 22:18:20] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -52.767870-0.000223j
[2025-09-04 22:18:30] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -52.687816+0.000194j
[2025-09-04 22:18:41] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -52.954265+0.000356j
[2025-09-04 22:18:51] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -52.904935+0.000543j
[2025-09-04 22:19:01] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -52.932317+0.001144j
[2025-09-04 22:19:11] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -52.911037-0.000159j
[2025-09-04 22:19:21] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -52.925511-0.002201j
[2025-09-04 22:19:32] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -52.968797-0.002026j
[2025-09-04 22:19:42] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -52.963004+0.002896j
[2025-09-04 22:19:52] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -53.053789+0.004660j
[2025-09-04 22:20:02] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -52.889951+0.001511j
[2025-09-04 22:20:12] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -52.814388+0.000893j
[2025-09-04 22:20:23] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -52.968999+0.001477j
[2025-09-04 22:20:33] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -52.822298+0.000427j
[2025-09-04 22:20:43] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -52.980438+0.002070j
[2025-09-04 22:20:53] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -53.054551-0.002556j
[2025-09-04 22:21:03] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -52.954826-0.004074j
[2025-09-04 22:21:14] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -52.931156-0.004055j
[2025-09-04 22:21:24] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -52.993851+0.000851j
[2025-09-04 22:21:34] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -52.917891+0.000419j
[2025-09-04 22:21:44] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -53.008563+0.002020j
[2025-09-04 22:21:54] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -52.980950-0.004068j
[2025-09-04 22:22:05] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -52.868075+0.005207j
[2025-09-04 22:22:15] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -52.865540-0.000182j
[2025-09-04 22:22:25] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -53.081312+0.003067j
[2025-09-04 22:22:35] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -52.971478-0.004177j
[2025-09-04 22:22:45] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -53.018951+0.001580j
[2025-09-04 22:22:56] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -52.894608-0.002065j
[2025-09-04 22:23:06] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -52.867233+0.000228j
[2025-09-04 22:23:16] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -52.890021-0.002246j
[2025-09-04 22:23:26] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -52.983179-0.001487j
[2025-09-04 22:23:36] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -52.987722+0.003180j
[2025-09-04 22:23:47] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -52.862260-0.000678j
[2025-09-04 22:23:57] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -52.947212+0.002246j
[2025-09-04 22:24:07] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -52.899931+0.000286j
[2025-09-04 22:24:17] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -52.937576-0.000739j
[2025-09-04 22:24:27] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -52.974158+0.000987j
[2025-09-04 22:24:38] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -53.125650-0.002356j
[2025-09-04 22:24:48] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -53.047653-0.006666j
[2025-09-04 22:24:48] RESTART #1 | Period: 300
[2025-09-04 22:24:58] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -52.967869+0.003602j
[2025-09-04 22:25:08] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -53.042942+0.001487j
[2025-09-04 22:25:18] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -52.895714+0.005701j
[2025-09-04 22:25:29] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -52.875997+0.003346j
[2025-09-04 22:25:39] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -52.934615+0.001323j
[2025-09-04 22:25:49] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -53.073266-0.001347j
[2025-09-04 22:25:59] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -52.989585+0.002894j
[2025-09-04 22:26:09] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -53.042574-0.000694j
[2025-09-04 22:26:20] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -53.041539-0.000126j
[2025-09-04 22:26:30] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -52.750291-0.003263j
[2025-09-04 22:26:40] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -52.826419-0.002790j
[2025-09-04 22:26:50] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -52.884452-0.001891j
[2025-09-04 22:27:00] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -52.932094+0.001700j
[2025-09-04 22:27:11] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -52.919935+0.000369j
[2025-09-04 22:27:21] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -52.873816-0.002818j
[2025-09-04 22:27:31] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -52.906277-0.001546j
[2025-09-04 22:27:41] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -52.888201+0.000568j
[2025-09-04 22:27:51] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -52.827323+0.001935j
[2025-09-04 22:28:02] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -52.823566+0.003155j
[2025-09-04 22:28:12] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -52.729045+0.002121j
[2025-09-04 22:28:22] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -52.885970+0.001274j
[2025-09-04 22:28:32] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -52.878729+0.000963j
[2025-09-04 22:28:42] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -52.991092-0.003434j
[2025-09-04 22:28:53] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -52.959343-0.002025j
[2025-09-04 22:29:03] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -53.018040-0.002803j
[2025-09-04 22:29:13] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -52.923391+0.004144j
[2025-09-04 22:29:23] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -52.747380+0.003546j
[2025-09-04 22:29:33] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -52.839019+0.003421j
[2025-09-04 22:29:44] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -52.824228-0.003225j
[2025-09-04 22:29:54] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -52.894929-0.000108j
[2025-09-04 22:30:04] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -52.964722-0.003993j
[2025-09-04 22:30:14] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -52.975543-0.000780j
[2025-09-04 22:30:24] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -52.982663-0.002813j
[2025-09-04 22:30:35] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -52.956649+0.001219j
[2025-09-04 22:30:45] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -52.952670+0.002359j
[2025-09-04 22:30:55] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -53.048428-0.001715j
[2025-09-04 22:31:05] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -52.960115+0.002153j
[2025-09-04 22:31:15] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -52.892820+0.003266j
[2025-09-04 22:31:26] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -52.820011+0.000924j
[2025-09-04 22:31:36] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -52.879423-0.000529j
[2025-09-04 22:31:46] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -52.994592-0.003376j
[2025-09-04 22:31:56] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -52.868156-0.000371j
[2025-09-04 22:32:06] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -53.020994+0.002370j
[2025-09-04 22:32:17] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -52.930687+0.000480j
[2025-09-04 22:32:27] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -52.951179-0.001668j
[2025-09-04 22:32:37] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -52.889547-0.005279j
[2025-09-04 22:32:47] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -52.986975+0.002368j
[2025-09-04 22:32:57] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -52.919393+0.001952j
[2025-09-04 22:33:08] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -52.878326-0.000310j
[2025-09-04 22:33:18] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -53.015365-0.001048j
[2025-09-04 22:33:28] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -53.135306+0.001260j
[2025-09-04 22:33:38] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -53.084621+0.003650j
[2025-09-04 22:33:48] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -53.008683+0.000076j
[2025-09-04 22:33:59] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -52.906113+0.000355j
[2025-09-04 22:34:09] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -53.022318+0.002737j
[2025-09-04 22:34:19] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -52.990152-0.003149j
[2025-09-04 22:34:29] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -53.066865-0.001077j
[2025-09-04 22:34:39] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -53.188788+0.005348j
[2025-09-04 22:34:50] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -53.045495+0.002976j
[2025-09-04 22:35:00] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -52.968211-0.000530j
[2025-09-04 22:35:00] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-04 22:35:10] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -52.967266+0.001156j
[2025-09-04 22:35:20] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -53.005948-0.002139j
[2025-09-04 22:35:30] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -52.999883+0.000899j
[2025-09-04 22:35:41] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -52.913721-0.004036j
[2025-09-04 22:35:51] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -52.940523-0.002383j
[2025-09-04 22:36:01] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -52.909605-0.001789j
[2025-09-04 22:36:11] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -52.980979+0.002255j
[2025-09-04 22:36:22] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -52.922477+0.002236j
[2025-09-04 22:36:32] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -52.945165-0.004898j
[2025-09-04 22:36:42] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -52.861003+0.001402j
[2025-09-04 22:36:52] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -53.017717-0.001064j
[2025-09-04 22:37:02] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -52.975282-0.000121j
[2025-09-04 22:37:13] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -52.941544+0.002005j
[2025-09-04 22:37:23] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -53.000519-0.000850j
[2025-09-04 22:37:33] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -52.971755+0.007232j
[2025-09-04 22:37:43] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -52.950464-0.026411j
[2025-09-04 22:37:53] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -52.883171+0.002344j
[2025-09-04 22:38:04] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -52.906852+0.001128j
[2025-09-04 22:38:14] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -52.806008-0.004481j
[2025-09-04 22:38:24] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -52.827496+0.002353j
[2025-09-04 22:38:34] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -52.930310-0.000227j
[2025-09-04 22:38:45] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -52.897957+0.002545j
[2025-09-04 22:38:55] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -52.796879+0.001546j
[2025-09-04 22:39:05] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -52.933985-0.001239j
[2025-09-04 22:39:15] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -52.948679+0.003913j
[2025-09-04 22:39:25] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -53.130825-0.000872j
[2025-09-04 22:39:36] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -53.103439-0.000879j
[2025-09-04 22:39:46] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -52.974060+0.004037j
[2025-09-04 22:39:56] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -52.967270-0.001862j
[2025-09-04 22:40:07] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -52.984375+0.003266j
[2025-09-04 22:40:17] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -53.084611+0.003589j
[2025-09-04 22:40:27] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -52.799947+0.000132j
[2025-09-04 22:40:37] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -52.903534-0.000241j
[2025-09-04 22:40:47] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -52.866328-0.000915j
[2025-09-04 22:40:58] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -52.866283-0.004136j
[2025-09-04 22:41:08] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -52.878284+0.001480j
[2025-09-04 22:41:18] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -52.934453+0.000536j
[2025-09-04 22:41:28] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -52.890657+0.001892j
[2025-09-04 22:41:38] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -52.864000-0.002361j
[2025-09-04 22:41:49] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -52.998603-0.001340j
[2025-09-04 22:41:59] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -52.839742-0.004304j
[2025-09-04 22:42:09] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -52.915673-0.011289j
[2025-09-04 22:42:19] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -52.843716-0.000670j
[2025-09-04 22:42:29] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -52.734360-0.002827j
[2025-09-04 22:42:40] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -52.769495+0.002745j
[2025-09-04 22:42:50] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -52.861476+0.003476j
[2025-09-04 22:43:00] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -52.865471+0.001402j
[2025-09-04 22:43:10] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -52.797519-0.002761j
[2025-09-04 22:43:21] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -52.832748-0.000756j
[2025-09-04 22:43:31] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -52.913379+0.000358j
[2025-09-04 22:43:41] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -53.032359+0.001207j
[2025-09-04 22:43:51] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -53.014180+0.001359j
[2025-09-04 22:44:01] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -52.889000+0.003872j
[2025-09-04 22:44:12] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -52.979291-0.002934j
[2025-09-04 22:44:22] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -52.941550-0.002456j
[2025-09-04 22:44:32] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -52.997216-0.000824j
[2025-09-04 22:44:42] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -52.972946+0.003998j
[2025-09-04 22:44:52] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -52.999808-0.000656j
[2025-09-04 22:45:03] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -52.964850-0.001447j
[2025-09-04 22:45:13] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -52.983942+0.000331j
[2025-09-04 22:45:23] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -53.099212+0.000736j
[2025-09-04 22:45:33] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -53.020533-0.000897j
[2025-09-04 22:45:43] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -53.011954-0.000613j
[2025-09-04 22:45:54] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -53.048803+0.003363j
[2025-09-04 22:46:04] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -53.135093-0.003399j
[2025-09-04 22:46:14] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -53.059130-0.000682j
[2025-09-04 22:46:24] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -52.974624+0.000174j
[2025-09-04 22:46:34] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -52.993605-0.000962j
[2025-09-04 22:46:45] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -52.933490-0.004305j
[2025-09-04 22:46:55] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -52.799188+0.000011j
[2025-09-04 22:47:05] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -52.778338+0.000420j
[2025-09-04 22:47:15] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -52.775866+0.001968j
[2025-09-04 22:47:26] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -52.989168-0.001359j
[2025-09-04 22:47:36] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -53.112431-0.001760j
[2025-09-04 22:47:46] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -53.045711-0.004004j
[2025-09-04 22:47:56] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -52.884966+0.003782j
[2025-09-04 22:48:06] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -52.978361-0.003105j
[2025-09-04 22:48:17] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -52.816290-0.001605j
[2025-09-04 22:48:27] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -52.808367+0.003139j
[2025-09-04 22:48:37] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -52.811965+0.000268j
[2025-09-04 22:48:47] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -52.817636-0.006676j
[2025-09-04 22:48:57] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -52.839011-0.002437j
[2025-09-04 22:49:08] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -52.865940-0.000799j
[2025-09-04 22:49:18] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -52.918349+0.000517j
[2025-09-04 22:49:28] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -52.798667-0.006994j
[2025-09-04 22:49:38] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -52.990732-0.001796j
[2025-09-04 22:49:49] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -53.030783+0.000756j
[2025-09-04 22:49:59] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -52.984501-0.003718j
[2025-09-04 22:50:09] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -52.940318-0.000316j
[2025-09-04 22:50:19] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -52.986929-0.002098j
[2025-09-04 22:50:29] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -52.877259+0.002545j
[2025-09-04 22:50:40] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -52.951974+0.001719j
[2025-09-04 22:50:50] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -52.957878-0.001374j
[2025-09-04 22:51:00] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -52.861004+0.001761j
[2025-09-04 22:51:10] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -53.021153+0.003175j
[2025-09-04 22:51:20] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -52.841787-0.002079j
[2025-09-04 22:51:31] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -53.006057-0.001766j
[2025-09-04 22:51:41] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -53.120877-0.000813j
[2025-09-04 22:51:51] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -53.029446-0.001673j
[2025-09-04 22:52:01] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -53.069322-0.003554j
[2025-09-04 22:52:11] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -52.961932-0.001766j
[2025-09-04 22:52:22] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -52.876736-0.002603j
[2025-09-04 22:52:32] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -52.901450+0.003688j
[2025-09-04 22:52:42] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -52.831628-0.006598j
[2025-09-04 22:52:52] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -52.819649-0.003941j
[2025-09-04 22:52:52] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-04 22:53:03] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -52.846177-0.002069j
[2025-09-04 22:53:13] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -52.917863+0.000891j
[2025-09-04 22:53:23] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -52.917266-0.004670j
[2025-09-04 22:53:33] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -53.078845-0.000320j
[2025-09-04 22:53:43] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -52.930297-0.001032j
[2025-09-04 22:53:54] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -53.007447+0.001002j
[2025-09-04 22:54:04] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -52.918746+0.003170j
[2025-09-04 22:54:14] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -52.976770+0.000248j
[2025-09-04 22:54:24] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -52.827416+0.004677j
[2025-09-04 22:54:34] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -52.920561+0.003455j
[2025-09-04 22:54:45] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -52.874302+0.000809j
[2025-09-04 22:54:55] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -52.913262+0.000649j
[2025-09-04 22:55:05] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -52.927070-0.007118j
[2025-09-04 22:55:15] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -52.870013-0.001038j
[2025-09-04 22:55:25] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -52.906842+0.002090j
[2025-09-04 22:55:36] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -52.991345+0.003327j
[2025-09-04 22:55:46] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -52.895385+0.000726j
[2025-09-04 22:55:56] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -52.867381-0.000967j
[2025-09-04 22:56:06] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -52.949933-0.002059j
[2025-09-04 22:56:16] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -52.909961-0.001913j
[2025-09-04 22:56:27] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -52.946890+0.000108j
[2025-09-04 22:56:37] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -53.035204-0.001747j
[2025-09-04 22:56:47] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -53.130331-0.001382j
[2025-09-04 22:56:57] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -52.994954+0.002943j
[2025-09-04 22:57:08] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -53.002116-0.003393j
[2025-09-04 22:57:18] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -52.981909-0.000285j
[2025-09-04 22:57:28] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -52.908437+0.000029j
[2025-09-04 22:57:38] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -52.872578-0.002488j
[2025-09-04 22:57:48] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -52.907303-0.000604j
[2025-09-04 22:57:58] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -52.809767+0.003245j
[2025-09-04 22:58:09] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -52.793336+0.000870j
[2025-09-04 22:58:19] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -52.793916+0.002644j
[2025-09-04 22:58:29] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -52.823475-0.002546j
[2025-09-04 22:58:39] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -52.898330-0.002026j
[2025-09-04 22:58:50] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -52.791916+0.003557j
[2025-09-04 22:59:00] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -52.770603+0.000492j
[2025-09-04 22:59:10] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -52.822615+0.004536j
[2025-09-04 22:59:20] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -52.858413-0.001699j
[2025-09-04 22:59:30] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -53.005705+0.002925j
[2025-09-04 22:59:41] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -53.031779+0.001406j
[2025-09-04 22:59:51] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -52.871587+0.000700j
[2025-09-04 23:00:01] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -52.881099+0.002351j
[2025-09-04 23:00:11] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -52.896601+0.003405j
[2025-09-04 23:00:21] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -52.937026+0.002089j
[2025-09-04 23:00:32] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -53.040099-0.003917j
[2025-09-04 23:00:42] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -53.009820+0.004504j
[2025-09-04 23:00:52] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -52.954138+0.000426j
[2025-09-04 23:01:02] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -53.008845-0.000643j
[2025-09-04 23:01:12] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -52.989817-0.002827j
[2025-09-04 23:01:23] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -53.053582+0.001959j
[2025-09-04 23:01:33] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -53.053581+0.004225j
[2025-09-04 23:01:43] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -52.954183+0.001956j
[2025-09-04 23:01:53] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -52.933033-0.002082j
[2025-09-04 23:02:03] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -52.962387+0.001885j
[2025-09-04 23:02:14] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -52.915403-0.002357j
[2025-09-04 23:02:24] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -52.939308-0.000719j
[2025-09-04 23:02:34] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -53.134679+0.001453j
[2025-09-04 23:02:44] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -53.005694+0.000166j
[2025-09-04 23:02:54] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -53.098542+0.000448j
[2025-09-04 23:03:05] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -52.986001-0.001071j
[2025-09-04 23:03:15] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -53.063216+0.000438j
[2025-09-04 23:03:25] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -52.958504+0.004653j
[2025-09-04 23:03:35] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -52.930241-0.001203j
[2025-09-04 23:03:45] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -52.958537-0.001434j
[2025-09-04 23:03:56] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -53.073915+0.001293j
[2025-09-04 23:04:06] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -52.849170+0.003368j
[2025-09-04 23:04:16] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -52.984503+0.004632j
[2025-09-04 23:04:26] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -52.968307-0.000445j
[2025-09-04 23:04:36] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -52.809085-0.004271j
[2025-09-04 23:04:47] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -52.844188-0.002637j
[2025-09-04 23:04:57] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -52.813593+0.001530j
[2025-09-04 23:05:07] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -52.910862+0.000125j
[2025-09-04 23:05:17] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -52.903322+0.004232j
[2025-09-04 23:05:27] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -52.854470+0.001346j
[2025-09-04 23:05:38] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -52.911907+0.003089j
[2025-09-04 23:05:48] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -53.058542+0.002619j
[2025-09-04 23:05:58] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -53.009559-0.001325j
[2025-09-04 23:06:08] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -52.964202-0.000973j
[2025-09-04 23:06:19] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -53.014604+0.004629j
[2025-09-04 23:06:29] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -52.979365+0.002348j
[2025-09-04 23:06:39] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -52.946498+0.001414j
[2025-09-04 23:06:49] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -53.049496+0.004427j
[2025-09-04 23:06:59] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -52.948990+0.000668j
[2025-09-04 23:07:10] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -52.944153+0.001349j
[2025-09-04 23:07:20] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -52.936443+0.001270j
[2025-09-04 23:07:30] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -52.982477+0.003829j
[2025-09-04 23:07:40] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -52.906500-0.000519j
[2025-09-04 23:07:50] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -53.089312+0.001731j
[2025-09-04 23:08:01] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -53.075970+0.001290j
[2025-09-04 23:08:11] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -52.989362-0.002621j
[2025-09-04 23:08:21] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -52.989679+0.000966j
[2025-09-04 23:08:31] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -52.998254-0.003952j
[2025-09-04 23:08:41] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -52.942268+0.004308j
[2025-09-04 23:08:52] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -52.943010+0.003444j
[2025-09-04 23:09:02] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -52.924675+0.005908j
[2025-09-04 23:09:12] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -52.995415+0.000025j
[2025-09-04 23:09:22] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -53.018939+0.000466j
[2025-09-04 23:09:32] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -52.948616-0.002057j
[2025-09-04 23:09:43] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -53.077949-0.000857j
[2025-09-04 23:09:53] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -52.867011-0.003630j
[2025-09-04 23:10:03] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -52.967884-0.001905j
[2025-09-04 23:10:13] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -52.894521+0.003298j
[2025-09-04 23:10:23] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -52.991875+0.001366j
[2025-09-04 23:10:34] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -52.978420-0.001537j
[2025-09-04 23:10:44] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -52.926900-0.001751j
[2025-09-04 23:10:44] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-04 23:10:54] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -52.905724-0.001291j
[2025-09-04 23:11:04] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -52.944730-0.002938j
[2025-09-04 23:11:14] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -52.922348-0.000332j
[2025-09-04 23:11:25] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -52.951606-0.001857j
[2025-09-04 23:11:35] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -52.950525+0.003766j
[2025-09-04 23:11:45] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -52.931700+0.002041j
[2025-09-04 23:11:55] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -53.062931-0.001362j
[2025-09-04 23:12:05] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -53.014757+0.001938j
[2025-09-04 23:12:16] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -53.073397-0.003703j
[2025-09-04 23:12:26] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -53.004812-0.003264j
[2025-09-04 23:12:36] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -53.026959-0.002047j
[2025-09-04 23:12:46] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -52.952269+0.002380j
[2025-09-04 23:12:56] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -52.970966+0.002930j
[2025-09-04 23:13:07] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -52.859620+0.003529j
[2025-09-04 23:13:17] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -52.962451-0.002203j
[2025-09-04 23:13:27] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -52.967307+0.003076j
[2025-09-04 23:13:37] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -52.957607-0.003212j
[2025-09-04 23:13:48] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -52.950563+0.003784j
[2025-09-04 23:13:58] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -53.013393+0.002532j
[2025-09-04 23:14:08] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -52.969366-0.001140j
[2025-09-04 23:14:18] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -52.878300+0.000158j
[2025-09-04 23:14:28] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -53.070600+0.000969j
[2025-09-04 23:14:39] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -53.016349-0.003720j
[2025-09-04 23:14:49] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -52.981622+0.002067j
[2025-09-04 23:14:59] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -53.036528-0.001427j
[2025-09-04 23:15:09] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -53.085358+0.000166j
[2025-09-04 23:15:19] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -52.881390-0.001672j
[2025-09-04 23:15:30] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -52.902227+0.000223j
[2025-09-04 23:15:40] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -52.932105-0.000454j
[2025-09-04 23:15:50] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -52.888211+0.002483j
[2025-09-04 23:15:50] RESTART #2 | Period: 600
[2025-09-04 23:16:00] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -53.011568+0.001184j
[2025-09-04 23:16:10] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -52.894986-0.000465j
[2025-09-04 23:16:21] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -52.902138-0.001811j
[2025-09-04 23:16:31] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -52.986625+0.003402j
[2025-09-04 23:16:41] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -52.816145-0.003345j
[2025-09-04 23:16:51] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -52.942194+0.000315j
[2025-09-04 23:17:01] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -53.071566-0.000505j
[2025-09-04 23:17:12] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -53.078428+0.001289j
[2025-09-04 23:17:22] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -52.902304+0.002764j
[2025-09-04 23:17:32] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -52.979330+0.000065j
[2025-09-04 23:17:42] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -53.047818+0.002211j
[2025-09-04 23:17:52] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -53.030011+0.002461j
[2025-09-04 23:18:03] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -52.944022+0.002777j
[2025-09-04 23:18:13] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -53.112209-0.002139j
[2025-09-04 23:18:23] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -52.981993-0.000296j
[2025-09-04 23:18:33] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -52.912672-0.003016j
[2025-09-04 23:18:43] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -53.044592+0.003541j
[2025-09-04 23:18:54] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -52.950197+0.003947j
[2025-09-04 23:19:04] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -52.997420-0.000121j
[2025-09-04 23:19:14] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -52.924065-0.000518j
[2025-09-04 23:19:24] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -53.028659+0.000266j
[2025-09-04 23:19:35] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -52.979930+0.002840j
[2025-09-04 23:19:45] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -53.038417-0.000295j
[2025-09-04 23:19:55] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -53.028757+0.001683j
[2025-09-04 23:20:05] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -53.054587-0.000705j
[2025-09-04 23:20:15] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -52.968522+0.003895j
[2025-09-04 23:20:26] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -52.893158+0.003881j
[2025-09-04 23:20:36] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -52.961377-0.002183j
[2025-09-04 23:20:45] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -52.899222-0.000408j
[2025-09-04 23:20:56] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -52.858441+0.002289j
[2025-09-04 23:21:06] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -52.969291+0.001331j
[2025-09-04 23:21:16] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -52.869176-0.002871j
[2025-09-04 23:21:26] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -52.904448+0.001248j
[2025-09-04 23:21:36] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -52.930485+0.000826j
[2025-09-04 23:21:47] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -52.962716+0.002627j
[2025-09-04 23:21:57] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -52.943243-0.003119j
[2025-09-04 23:22:07] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -53.000271-0.003532j
[2025-09-04 23:22:17] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -53.058679+0.001483j
[2025-09-04 23:22:27] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -53.041279-0.003920j
[2025-09-04 23:22:38] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -52.872978-0.001038j
[2025-09-04 23:22:48] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -52.921411+0.002112j
[2025-09-04 23:22:58] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -52.906957+0.000590j
[2025-09-04 23:23:08] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -52.842748+0.003345j
[2025-09-04 23:23:18] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -52.804598+0.002531j
[2025-09-04 23:23:29] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -52.919420-0.004908j
[2025-09-04 23:23:39] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -52.904258-0.002016j
[2025-09-04 23:23:49] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -53.056928-0.001772j
[2025-09-04 23:23:59] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -53.045391-0.002700j
[2025-09-04 23:24:09] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -53.036134-0.003886j
[2025-09-04 23:24:20] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -52.999027+0.002368j
[2025-09-04 23:24:30] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -53.119101-0.001311j
[2025-09-04 23:24:40] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -52.992898+0.003599j
[2025-09-04 23:24:50] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -52.942062+0.002710j
[2025-09-04 23:25:00] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -52.955850-0.001715j
[2025-09-04 23:25:11] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -52.793016-0.000715j
[2025-09-04 23:25:21] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -52.777870+0.004966j
[2025-09-04 23:25:31] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -52.983393+0.001521j
[2025-09-04 23:25:41] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -53.019955+0.000447j
[2025-09-04 23:25:51] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -53.004875+0.000145j
[2025-09-04 23:26:02] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -52.969435-0.001908j
[2025-09-04 23:26:12] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -52.846441+0.005036j
[2025-09-04 23:26:22] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -52.838719+0.001967j
[2025-09-04 23:26:32] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -52.849725-0.001358j
[2025-09-04 23:26:42] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -52.855913+0.008847j
[2025-09-04 23:26:53] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -52.979043-0.002667j
[2025-09-04 23:27:03] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -53.022376-0.004026j
[2025-09-04 23:27:13] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -53.000864-0.003554j
[2025-09-04 23:27:23] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -52.967051-0.001165j
[2025-09-04 23:27:33] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -52.964604+0.003875j
[2025-09-04 23:27:44] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -53.035892+0.004732j
[2025-09-04 23:27:54] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -52.957923-0.000386j
[2025-09-04 23:28:04] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -52.930951-0.002281j
[2025-09-04 23:28:14] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -52.964739+0.001742j
[2025-09-04 23:28:24] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -52.908086+0.002621j
[2025-09-04 23:28:35] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -52.958987-0.006445j
[2025-09-04 23:28:35] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-04 23:28:45] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -53.060373-0.001471j
[2025-09-04 23:28:55] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -52.919889-0.001927j
[2025-09-04 23:29:05] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -52.875534+0.003962j
[2025-09-04 23:29:15] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -52.893185+0.000239j
[2025-09-04 23:29:26] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -52.940583+0.002678j
[2025-09-04 23:29:36] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -52.860286+0.000438j
[2025-09-04 23:29:46] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -52.969859-0.001616j
[2025-09-04 23:29:56] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -52.909781+0.000940j
[2025-09-04 23:30:07] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -52.826399+0.001246j
[2025-09-04 23:30:17] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -52.865812-0.000325j
[2025-09-04 23:30:27] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -53.073843-0.003638j
[2025-09-04 23:30:37] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -52.936029-0.002815j
[2025-09-04 23:30:47] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -52.880674+0.005807j
[2025-09-04 23:30:58] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -52.934117-0.003092j
[2025-09-04 23:31:08] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -52.920341+0.001714j
[2025-09-04 23:31:18] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -52.875524-0.003834j
[2025-09-04 23:31:28] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -53.041366+0.002341j
[2025-09-04 23:31:38] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -52.931536+0.002315j
[2025-09-04 23:31:49] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -52.878289+0.000538j
[2025-09-04 23:31:59] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -52.880082+0.003098j
[2025-09-04 23:32:09] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -52.964515+0.001279j
[2025-09-04 23:32:19] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -52.869579-0.000986j
[2025-09-04 23:32:29] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -52.859396+0.000963j
[2025-09-04 23:32:40] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -52.820599+0.003656j
[2025-09-04 23:32:50] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -52.781266-0.000944j
[2025-09-04 23:33:00] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -52.761216+0.000327j
[2025-09-04 23:33:10] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -52.763479-0.004988j
[2025-09-04 23:33:20] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -52.821250-0.000392j
[2025-09-04 23:33:31] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -52.908842+0.001534j
[2025-09-04 23:33:41] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -52.848839+0.003121j
[2025-09-04 23:33:51] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -52.777126+0.003464j
[2025-09-04 23:34:01] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -52.822500-0.000984j
[2025-09-04 23:34:11] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -52.953404+0.000323j
[2025-09-04 23:34:22] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -52.894520+0.000548j
[2025-09-04 23:34:32] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -53.013765+0.005293j
[2025-09-04 23:34:42] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -52.875473+0.003201j
[2025-09-04 23:34:52] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -52.846489+0.000212j
[2025-09-04 23:35:03] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -52.811916+0.001162j
[2025-09-04 23:35:13] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -52.844954+0.001798j
[2025-09-04 23:35:23] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -52.875592+0.001464j
[2025-09-04 23:35:33] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -53.009081+0.000706j
[2025-09-04 23:35:44] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -52.973139+0.000483j
[2025-09-04 23:35:54] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -52.932831-0.002010j
[2025-09-04 23:36:04] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -52.929356-0.004122j
[2025-09-04 23:36:14] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -52.798481-0.001027j
[2025-09-04 23:36:24] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -53.043772+0.002366j
[2025-09-04 23:36:35] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -53.066672-0.003704j
[2025-09-04 23:36:45] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -52.992105+0.002306j
[2025-09-04 23:36:55] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -53.026760+0.000266j
[2025-09-04 23:37:05] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -52.901412+0.001724j
[2025-09-04 23:37:15] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -52.950627-0.000615j
[2025-09-04 23:37:26] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -53.027276+0.005669j
[2025-09-04 23:37:36] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -52.943341+0.003954j
[2025-09-04 23:37:46] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -52.858490+0.001835j
[2025-09-04 23:37:56] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -52.849930+0.000191j
[2025-09-04 23:38:07] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -52.863347-0.001583j
[2025-09-04 23:38:17] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -52.833451+0.000506j
[2025-09-04 23:38:27] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -52.889350+0.001100j
[2025-09-04 23:38:37] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -52.993875-0.004190j
[2025-09-04 23:38:47] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -52.854346+0.004226j
[2025-09-04 23:38:58] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -52.803659-0.000068j
[2025-09-04 23:39:08] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -52.798915-0.000081j
[2025-09-04 23:39:18] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -52.789986-0.004974j
[2025-09-04 23:39:28] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -52.868227-0.001235j
[2025-09-04 23:39:38] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -52.977851-0.000297j
[2025-09-04 23:39:49] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -52.964826-0.003519j
[2025-09-04 23:39:59] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -52.852874-0.003361j
[2025-09-04 23:40:09] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -52.874941-0.001126j
[2025-09-04 23:40:19] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -52.898606+0.001742j
[2025-09-04 23:40:29] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -52.909358-0.000539j
[2025-09-04 23:40:40] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -52.984333-0.003962j
[2025-09-04 23:40:50] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -52.843779+0.002909j
[2025-09-04 23:41:00] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -52.977870-0.002967j
[2025-09-04 23:41:10] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -52.828435-0.000416j
[2025-09-04 23:41:20] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -52.905513+0.000337j
[2025-09-04 23:41:31] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -52.776031-0.002808j
[2025-09-04 23:41:41] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -52.788451+0.003363j
[2025-09-04 23:41:51] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -52.965127-0.000127j
[2025-09-04 23:42:01] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -52.967525+0.000935j
[2025-09-04 23:42:11] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -52.933095-0.000392j
[2025-09-04 23:42:22] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -52.759724-0.001692j
[2025-09-04 23:42:32] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -52.850278+0.002344j
[2025-09-04 23:42:42] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -52.944385-0.004985j
[2025-09-04 23:42:52] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -52.970590-0.000127j
[2025-09-04 23:43:02] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -52.952857-0.004490j
[2025-09-04 23:43:13] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -52.929649+0.005494j
[2025-09-04 23:43:23] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -52.926093-0.004221j
[2025-09-04 23:43:33] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -52.860277+0.002342j
[2025-09-04 23:43:43] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -52.849875-0.002500j
[2025-09-04 23:43:53] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -52.995301+0.000299j
[2025-09-04 23:44:04] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -52.944122-0.001858j
[2025-09-04 23:44:14] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -52.915932-0.002692j
[2025-09-04 23:44:24] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -52.894853-0.002715j
[2025-09-04 23:44:34] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -52.916982-0.000243j
[2025-09-04 23:44:44] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -52.955099-0.004200j
[2025-09-04 23:44:55] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -53.019257-0.001555j
[2025-09-04 23:45:05] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -52.879632+0.003340j
[2025-09-04 23:45:15] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -53.006177+0.002086j
[2025-09-04 23:45:25] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -52.882814+0.001862j
[2025-09-04 23:45:36] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -52.899217-0.000990j
[2025-09-04 23:45:46] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -52.907791-0.002200j
[2025-09-04 23:45:56] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -52.849877+0.006753j
[2025-09-04 23:46:06] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -52.948468+0.002246j
[2025-09-04 23:46:16] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -53.127855+0.000057j
[2025-09-04 23:46:27] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -53.008314-0.000255j
[2025-09-04 23:46:27] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-04 23:46:37] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -53.058918+0.002363j
[2025-09-04 23:46:47] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -53.015928-0.003684j
[2025-09-04 23:46:57] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -53.077815-0.004162j
[2025-09-04 23:47:07] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -52.977555-0.000107j
[2025-09-04 23:47:18] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -52.996968-0.002968j
[2025-09-04 23:47:28] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -52.836697+0.002118j
[2025-09-04 23:47:38] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -52.931245+0.000123j
[2025-09-04 23:47:48] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -52.781353+0.000750j
[2025-09-04 23:47:58] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -52.922732-0.001665j
[2025-09-04 23:48:09] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -52.950129+0.000503j
[2025-09-04 23:48:19] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -52.962132+0.002240j
[2025-09-04 23:48:29] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -53.011168-0.003162j
[2025-09-04 23:48:39] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -52.977673+0.000804j
[2025-09-04 23:48:49] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -53.040149-0.000380j
[2025-09-04 23:49:00] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -53.091528+0.002713j
[2025-09-04 23:49:10] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -53.029423+0.001515j
[2025-09-04 23:49:20] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -53.084101+0.001608j
[2025-09-04 23:49:30] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -53.075163+0.000652j
[2025-09-04 23:49:40] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -53.252682+0.001623j
[2025-09-04 23:49:51] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -53.150457-0.002831j
[2025-09-04 23:50:01] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -52.986176+0.000062j
[2025-09-04 23:50:11] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -52.937982+0.002020j
[2025-09-04 23:50:21] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -52.963523-0.006270j
[2025-09-04 23:50:32] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -52.982842+0.000775j
[2025-09-04 23:50:42] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -52.843055+0.001214j
[2025-09-04 23:50:52] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -52.972621+0.003861j
[2025-09-04 23:51:02] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -52.861548-0.001850j
[2025-09-04 23:51:12] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -52.931962-0.003012j
[2025-09-04 23:51:23] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -52.975459-0.004368j
[2025-09-04 23:51:33] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -52.912679-0.001458j
[2025-09-04 23:51:43] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -53.064500-0.002156j
[2025-09-04 23:51:53] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -53.000521-0.004098j
[2025-09-04 23:52:03] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -53.031852-0.001919j
[2025-09-04 23:52:14] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -52.874797+0.002618j
[2025-09-04 23:52:24] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -52.828092-0.000798j
[2025-09-04 23:52:34] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -52.961397-0.000095j
[2025-09-04 23:52:44] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -52.909288-0.000661j
[2025-09-04 23:52:54] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -52.809417-0.000900j
[2025-09-04 23:53:05] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -52.824954+0.001813j
[2025-09-04 23:53:15] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -52.847116+0.000693j
[2025-09-04 23:53:25] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -52.748968+0.002319j
[2025-09-04 23:53:35] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -52.693445+0.002605j
[2025-09-04 23:53:45] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -52.779387+0.004871j
[2025-09-04 23:53:56] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -53.032807-0.002017j
[2025-09-04 23:54:06] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -52.897732-0.001795j
[2025-09-04 23:54:16] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -52.845562+0.003025j
[2025-09-04 23:54:26] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -52.930391+0.004828j
[2025-09-04 23:54:37] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -53.067479+0.002121j
[2025-09-04 23:54:47] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -52.968742-0.000357j
[2025-09-04 23:54:57] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -52.989054+0.000966j
[2025-09-04 23:55:07] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -52.961614-0.001505j
[2025-09-04 23:55:17] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -53.036571-0.004167j
[2025-09-04 23:55:28] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -52.786549+0.003373j
[2025-09-04 23:55:38] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -52.842762+0.001254j
[2025-09-04 23:55:48] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -52.893026-0.000936j
[2025-09-04 23:55:58] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -52.990826-0.000125j
[2025-09-04 23:56:08] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -52.904918+0.004626j
[2025-09-04 23:56:19] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -52.956408+0.003922j
[2025-09-04 23:56:29] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -52.990176-0.001889j
[2025-09-04 23:56:39] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -52.955982+0.005011j
[2025-09-04 23:56:49] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -52.996591-0.000178j
[2025-09-04 23:57:00] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -52.918752-0.002052j
[2025-09-04 23:57:10] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -53.014419-0.000448j
[2025-09-04 23:57:20] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -53.062413-0.001979j
[2025-09-04 23:57:30] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -52.984240-0.000693j
[2025-09-04 23:57:40] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -53.036790-0.002845j
[2025-09-04 23:57:51] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -52.933500+0.005806j
[2025-09-04 23:58:01] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -53.015634-0.003430j
[2025-09-04 23:58:11] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -53.047801+0.005603j
[2025-09-04 23:58:21] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -52.917642+0.000146j
[2025-09-04 23:58:31] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -53.030834-0.000545j
[2025-09-04 23:58:42] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -52.915752-0.000651j
[2025-09-04 23:58:52] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -53.055547+0.001457j
[2025-09-04 23:59:02] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -52.965163+0.002373j
[2025-09-04 23:59:12] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -52.838083-0.000967j
[2025-09-04 23:59:23] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -52.951298-0.002737j
[2025-09-04 23:59:33] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -52.940277-0.000306j
[2025-09-04 23:59:43] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -52.977308-0.001549j
[2025-09-04 23:59:53] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -52.910424-0.000359j
[2025-09-05 00:00:03] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -53.147153-0.001806j
[2025-09-05 00:00:14] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -52.949792+0.000938j
[2025-09-05 00:00:24] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -53.033367-0.003896j
[2025-09-05 00:00:34] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -53.052760-0.000861j
[2025-09-05 00:00:44] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -53.014680+0.000857j
[2025-09-05 00:00:54] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -52.919419-0.001853j
[2025-09-05 00:01:05] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -52.755388+0.001449j
[2025-09-05 00:01:15] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -52.781989-0.006992j
[2025-09-05 00:01:25] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -52.745934+0.000094j
[2025-09-05 00:01:35] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -52.875738+0.000698j
[2025-09-05 00:01:46] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -52.845589-0.000613j
[2025-09-05 00:01:56] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -52.821075+0.004089j
[2025-09-05 00:02:06] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -52.885195-0.000958j
[2025-09-05 00:02:16] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -52.922806-0.001131j
[2025-09-05 00:02:27] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -52.947915+0.000755j
[2025-09-05 00:02:37] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -52.926857+0.003320j
[2025-09-05 00:02:47] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -52.817468+0.000307j
[2025-09-05 00:02:57] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -52.950236-0.000797j
[2025-09-05 00:03:07] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -52.878515+0.001194j
[2025-09-05 00:03:18] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -52.968891+0.001743j
[2025-09-05 00:03:28] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -53.124503+0.000547j
[2025-09-05 00:03:38] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -53.004368-0.001402j
[2025-09-05 00:03:48] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -52.981508-0.001855j
[2025-09-05 00:03:58] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -52.966530-0.004292j
[2025-09-05 00:04:09] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -52.838850+0.000635j
[2025-09-05 00:04:19] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -52.928616+0.000201j
[2025-09-05 00:04:19] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-05 00:04:29] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -53.019734-0.002028j
[2025-09-05 00:04:39] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -52.979780+0.001804j
[2025-09-05 00:04:49] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -52.872580+0.000703j
[2025-09-05 00:05:00] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -52.827092-0.000263j
[2025-09-05 00:05:10] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -52.740469-0.003384j
[2025-09-05 00:05:20] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -52.786313-0.001366j
[2025-09-05 00:05:30] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -52.835258-0.002050j
[2025-09-05 00:05:40] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -52.905301+0.000344j
[2025-09-05 00:05:51] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -52.819549+0.001270j
[2025-09-05 00:06:01] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -52.873246+0.002714j
[2025-09-05 00:06:11] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -52.954477-0.002078j
[2025-09-05 00:06:21] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -52.849459+0.003871j
[2025-09-05 00:06:31] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -52.964691-0.001510j
[2025-09-05 00:06:42] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -52.953620-0.000811j
[2025-09-05 00:06:52] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -52.956801+0.003409j
[2025-09-05 00:07:02] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -52.942228+0.000192j
[2025-09-05 00:07:12] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -53.073443-0.004011j
[2025-09-05 00:07:23] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -53.123375-0.000370j
[2025-09-05 00:07:33] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -53.059218-0.003416j
[2025-09-05 00:07:43] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -52.960006+0.000887j
[2025-09-05 00:07:53] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -53.039164+0.001479j
[2025-09-05 00:08:03] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -53.027034-0.003242j
[2025-09-05 00:08:14] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -52.955535-0.003254j
[2025-09-05 00:08:24] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -53.125891+0.000697j
[2025-09-05 00:08:34] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -52.966462-0.000748j
[2025-09-05 00:08:44] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -52.857765+0.002430j
[2025-09-05 00:08:54] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -52.908105+0.001763j
[2025-09-05 00:09:05] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -52.875078+0.002359j
[2025-09-05 00:09:15] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -52.924008-0.002680j
[2025-09-05 00:09:25] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -52.945678-0.002080j
[2025-09-05 00:09:35] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -52.916541+0.006261j
[2025-09-05 00:09:45] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -52.918886+0.000719j
[2025-09-05 00:09:56] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -52.918786-0.001347j
[2025-09-05 00:10:06] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -52.855275+0.004011j
[2025-09-05 00:10:16] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -52.916248-0.000500j
[2025-09-05 00:10:26] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -52.872040-0.000586j
[2025-09-05 00:10:36] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -52.815576+0.004149j
[2025-09-05 00:10:47] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -52.853864-0.000065j
[2025-09-05 00:10:57] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -52.906801+0.002341j
[2025-09-05 00:11:07] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -52.839380+0.000562j
[2025-09-05 00:11:17] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -52.851036-0.001223j
[2025-09-05 00:11:28] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -52.654213-0.003174j
[2025-09-05 00:11:38] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -52.735945+0.004371j
[2025-09-05 00:11:48] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -52.685308+0.000252j
[2025-09-05 00:11:58] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -52.789601+0.000192j
[2025-09-05 00:12:08] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -52.952513+0.002184j
[2025-09-05 00:12:19] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -52.985012+0.001897j
[2025-09-05 00:12:29] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -52.922512-0.001409j
[2025-09-05 00:12:39] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -52.961112+0.000519j
[2025-09-05 00:12:49] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -52.927904+0.000227j
[2025-09-05 00:12:59] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -52.953964-0.000702j
[2025-09-05 00:13:10] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -52.917937-0.002274j
[2025-09-05 00:13:20] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -52.887146+0.000396j
[2025-09-05 00:13:30] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -52.943198-0.000376j
[2025-09-05 00:13:40] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -52.948613-0.003205j
[2025-09-05 00:13:50] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -52.889687-0.001902j
[2025-09-05 00:14:01] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -52.975242+0.003155j
[2025-09-05 00:14:11] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -52.882194-0.004363j
[2025-09-05 00:14:21] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -53.003381+0.003342j
[2025-09-05 00:14:31] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -53.000227-0.000336j
[2025-09-05 00:14:41] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -52.986119-0.002513j
[2025-09-05 00:14:52] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -52.995979+0.000209j
[2025-09-05 00:15:02] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -52.895952+0.003755j
[2025-09-05 00:15:12] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -52.978245+0.000750j
[2025-09-05 00:15:22] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -52.942519+0.000897j
[2025-09-05 00:15:32] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -52.986809-0.002744j
[2025-09-05 00:15:43] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -52.913583+0.002534j
[2025-09-05 00:15:53] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -52.891226-0.004536j
[2025-09-05 00:16:03] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -52.826208+0.001653j
[2025-09-05 00:16:13] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -52.911465+0.001309j
[2025-09-05 00:16:23] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -53.022905-0.001217j
[2025-09-05 00:16:34] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -53.078882+0.004194j
[2025-09-05 00:16:44] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -53.065203+0.001521j
[2025-09-05 00:16:54] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -52.908560+0.001636j
[2025-09-05 00:17:04] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -52.982172-0.000427j
[2025-09-05 00:17:14] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -52.963942-0.003438j
[2025-09-05 00:17:25] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -53.030777-0.004079j
[2025-09-05 00:17:35] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -53.088752-0.001750j
[2025-09-05 00:17:45] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -53.017525-0.002682j
[2025-09-05 00:17:55] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -53.038477+0.001210j
[2025-09-05 00:18:05] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -53.065140-0.002450j
[2025-09-05 00:18:16] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -53.069800+0.001981j
[2025-09-05 00:18:26] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -53.042472-0.001289j
[2025-09-05 00:18:36] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -52.933543-0.004218j
[2025-09-05 00:18:46] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -53.000465-0.002111j
[2025-09-05 00:18:56] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -53.058210+0.005464j
[2025-09-05 00:19:07] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -53.093550+0.001105j
[2025-09-05 00:19:17] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -53.046204+0.001448j
[2025-09-05 00:19:27] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -53.021251+0.002984j
[2025-09-05 00:19:37] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -52.998213+0.001950j
[2025-09-05 00:19:47] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -53.067612-0.000315j
[2025-09-05 00:19:57] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -53.105192+0.001174j
[2025-09-05 00:20:07] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -53.009094-0.004425j
[2025-09-05 00:20:18] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -53.038884+0.004348j
[2025-09-05 00:20:28] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -52.982523+0.000292j
[2025-09-05 00:20:38] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -52.983661+0.003299j
[2025-09-05 00:20:48] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -52.896497+0.001986j
[2025-09-05 00:20:58] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -53.016891-0.003380j
[2025-09-05 00:21:09] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -52.976304-0.003334j
[2025-09-05 00:21:19] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -52.919363-0.000725j
[2025-09-05 00:21:29] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -52.960753-0.001004j
[2025-09-05 00:21:39] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -52.851931+0.004635j
[2025-09-05 00:21:49] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -52.929725-0.001299j
[2025-09-05 00:22:00] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -52.901722+0.001790j
[2025-09-05 00:22:10] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -53.024735-0.004287j
[2025-09-05 00:22:10] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-05 00:22:20] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -52.875754-0.000861j
[2025-09-05 00:22:30] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -52.915585-0.002475j
[2025-09-05 00:22:40] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -52.882194+0.000283j
[2025-09-05 00:22:51] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -53.012470+0.000771j
[2025-09-05 00:23:01] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -52.928655+0.003484j
[2025-09-05 00:23:11] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -53.099737-0.000085j
[2025-09-05 00:23:21] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -53.067463+0.001146j
[2025-09-05 00:23:31] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -53.052376-0.001152j
[2025-09-05 00:23:42] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -52.950240+0.003834j
[2025-09-05 00:23:52] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -52.837200-0.001289j
[2025-09-05 00:24:02] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -52.885791+0.001784j
[2025-09-05 00:24:12] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -52.904736-0.000652j
[2025-09-05 00:24:22] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -53.091030-0.000779j
[2025-09-05 00:24:33] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -53.056965-0.000763j
[2025-09-05 00:24:43] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -53.074023-0.002624j
[2025-09-05 00:24:53] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -52.952908+0.001406j
[2025-09-05 00:25:03] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -53.022432+0.001551j
[2025-09-05 00:25:14] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -52.916647+0.000989j
[2025-09-05 00:25:24] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -52.967705-0.001685j
[2025-09-05 00:25:34] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -52.915413+0.001866j
[2025-09-05 00:25:44] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -52.963662+0.000376j
[2025-09-05 00:25:54] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -52.925466-0.002030j
[2025-09-05 00:26:05] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -52.962354-0.000946j
[2025-09-05 00:26:15] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -52.828867+0.001402j
[2025-09-05 00:26:25] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -52.944909-0.000727j
[2025-09-05 00:26:35] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -52.935829+0.000026j
[2025-09-05 00:26:45] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -53.030494+0.001504j
[2025-09-05 00:26:56] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -53.065736-0.001482j
[2025-09-05 00:27:07] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -53.101646+0.001077j
[2025-09-05 00:27:17] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -53.016494-0.004229j
[2025-09-05 00:27:27] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -53.035363+0.001536j
[2025-09-05 00:27:37] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -53.032068-0.000611j
[2025-09-05 00:27:48] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -53.034060+0.002294j
[2025-09-05 00:27:58] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -52.951193-0.000416j
[2025-09-05 00:28:08] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -52.973061+0.004124j
[2025-09-05 00:28:18] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -52.991181-0.003736j
[2025-09-05 00:28:28] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -52.988813+0.002162j
[2025-09-05 00:28:39] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -52.884074-0.003958j
[2025-09-05 00:28:49] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -52.943640+0.000984j
[2025-09-05 00:28:59] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -52.913583+0.001661j
[2025-09-05 00:29:09] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -52.993888+0.002575j
[2025-09-05 00:29:19] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -52.968112-0.000443j
[2025-09-05 00:29:30] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -53.070756+0.004145j
[2025-09-05 00:29:40] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -52.985680-0.008770j
[2025-09-05 00:29:50] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -53.027466+0.001547j
[2025-09-05 00:30:00] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -52.982995+0.004772j
[2025-09-05 00:30:10] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -52.928645-0.000293j
[2025-09-05 00:30:21] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -52.927682-0.004798j
[2025-09-05 00:30:31] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -52.990542-0.002300j
[2025-09-05 00:30:41] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -53.042120+0.000175j
[2025-09-05 00:30:51] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -52.841306+0.000672j
[2025-09-05 00:31:01] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -52.890057+0.005343j
[2025-09-05 00:31:11] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -52.908683-0.001034j
[2025-09-05 00:31:21] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -52.898813+0.004159j
[2025-09-05 00:31:32] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -52.940336+0.002729j
[2025-09-05 00:31:42] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -53.011882-0.000720j
[2025-09-05 00:31:52] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -52.945913+0.000378j
[2025-09-05 00:32:02] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -52.954526+0.000039j
[2025-09-05 00:32:12] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -53.107166-0.002837j
[2025-09-05 00:32:23] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -53.019919+0.003670j
[2025-09-05 00:32:33] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -53.039844-0.004429j
[2025-09-05 00:32:43] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -52.934606+0.001030j
[2025-09-05 00:32:53] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -52.907003-0.001431j
[2025-09-05 00:33:03] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -53.015381+0.001454j
[2025-09-05 00:33:14] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -53.004730+0.000999j
[2025-09-05 00:33:24] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -53.011742+0.000143j
[2025-09-05 00:33:34] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -52.914315+0.000966j
[2025-09-05 00:33:44] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -52.929128+0.002080j
[2025-09-05 00:33:55] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -52.929097-0.001494j
[2025-09-05 00:34:05] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -52.817380+0.002940j
[2025-09-05 00:34:15] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -52.928809+0.001789j
[2025-09-05 00:34:25] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -52.995277-0.000797j
[2025-09-05 00:34:35] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -52.899106+0.002197j
[2025-09-05 00:34:46] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -52.916788-0.000672j
[2025-09-05 00:34:56] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -52.965884-0.002420j
[2025-09-05 00:35:06] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -52.925959+0.001160j
[2025-09-05 00:35:16] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -52.942876-0.001899j
[2025-09-05 00:35:27] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -53.044629+0.001025j
[2025-09-05 00:35:37] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -52.877112-0.000447j
[2025-09-05 00:35:47] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -52.926118-0.003902j
[2025-09-05 00:35:57] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -53.040920-0.003452j
[2025-09-05 00:36:07] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -52.855083+0.001728j
[2025-09-05 00:36:18] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -52.872620-0.001334j
[2025-09-05 00:36:28] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -52.822127-0.000014j
[2025-09-05 00:36:38] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -52.855206-0.005921j
[2025-09-05 00:36:48] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -52.891063+0.002535j
[2025-09-05 00:36:58] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -52.916695-0.002630j
[2025-09-05 00:37:09] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -52.986264-0.001907j
[2025-09-05 00:37:19] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -52.966112+0.000921j
[2025-09-05 00:37:29] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -52.966069-0.001816j
[2025-09-05 00:37:39] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -53.046713+0.002367j
[2025-09-05 00:37:49] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -52.975788+0.000703j
[2025-09-05 00:38:00] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -53.158472-0.001630j
[2025-09-05 00:38:10] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -53.050719+0.002258j
[2025-09-05 00:38:20] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -52.995275+0.002338j
[2025-09-05 00:38:30] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -53.025132-0.000893j
[2025-09-05 00:38:40] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -53.010822-0.001090j
[2025-09-05 00:38:51] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -52.991004-0.000034j
[2025-09-05 00:39:01] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -52.957575-0.001510j
[2025-09-05 00:39:11] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -52.980416-0.000021j
[2025-09-05 00:39:21] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -52.964079+0.003256j
[2025-09-05 00:39:31] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -53.002633+0.002497j
[2025-09-05 00:39:42] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -52.969049+0.000581j
[2025-09-05 00:39:52] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -52.925253-0.003768j
[2025-09-05 00:40:02] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -52.941798+0.000941j
[2025-09-05 00:40:02] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-05 00:40:12] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -52.882042+0.001862j
[2025-09-05 00:40:22] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -52.992301-0.002201j
[2025-09-05 00:40:33] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -52.925250+0.001358j
[2025-09-05 00:40:43] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -52.845336+0.000427j
[2025-09-05 00:40:53] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -52.937154+0.002151j
[2025-09-05 00:41:03] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -52.961210+0.000345j
[2025-09-05 00:41:13] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -52.932082-0.002868j
[2025-09-05 00:41:24] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -53.006669+0.001952j
[2025-09-05 00:41:34] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -53.106172+0.002471j
[2025-09-05 00:41:44] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -53.004864-0.001201j
[2025-09-05 00:41:54] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -52.985188-0.002330j
[2025-09-05 00:42:05] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -53.048443+0.000240j
[2025-09-05 00:42:15] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -53.150236+0.000334j
[2025-09-05 00:42:25] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -53.064978-0.002319j
[2025-09-05 00:42:35] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -52.954478-0.000012j
[2025-09-05 00:42:45] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -52.958582+0.002919j
[2025-09-05 00:42:56] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -52.967475+0.002547j
[2025-09-05 00:43:06] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -53.024344+0.000448j
[2025-09-05 00:43:16] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -52.950693+0.001570j
[2025-09-05 00:43:26] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -52.836662-0.001867j
[2025-09-05 00:43:36] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -52.946218-0.002411j
[2025-09-05 00:43:47] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -53.018842-0.000732j
[2025-09-05 00:43:57] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -53.054071-0.002782j
[2025-09-05 00:44:07] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -52.981560-0.001888j
[2025-09-05 00:44:17] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -53.122999+0.000492j
[2025-09-05 00:44:27] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -52.994908+0.001776j
[2025-09-05 00:44:38] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -53.074279-0.001867j
[2025-09-05 00:44:48] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -52.970487-0.001707j
[2025-09-05 00:44:58] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -52.933642-0.006124j
[2025-09-05 00:45:08] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -52.839863+0.001764j
[2025-09-05 00:45:18] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -52.886669+0.001794j
[2025-09-05 00:45:29] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -52.812439+0.001011j
[2025-09-05 00:45:39] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -52.980966-0.000930j
[2025-09-05 00:45:49] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -52.934927-0.001804j
[2025-09-05 00:45:59] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -52.889501-0.003957j
[2025-09-05 00:46:09] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -52.771909+0.000701j
[2025-09-05 00:46:20] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -52.871643+0.000539j
[2025-09-05 00:46:30] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -52.828766+0.000999j
[2025-09-05 00:46:40] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -53.002442-0.002096j
[2025-09-05 00:46:50] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -52.971292+0.000690j
[2025-09-05 00:47:01] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -52.995118-0.003567j
[2025-09-05 00:47:11] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -52.867400-0.002440j
[2025-09-05 00:47:21] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -52.928770+0.002514j
[2025-09-05 00:47:31] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -52.961936+0.002076j
[2025-09-05 00:47:41] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -53.023946+0.003002j
[2025-09-05 00:47:52] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -52.946042+0.003215j
[2025-09-05 00:48:02] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -53.003795+0.001082j
[2025-09-05 00:48:12] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -53.050383-0.000600j
[2025-09-05 00:48:22] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -52.989014+0.001766j
[2025-09-05 00:48:32] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -52.977494+0.001765j
[2025-09-05 00:48:43] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -52.956546-0.001157j
[2025-09-05 00:48:53] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -52.998643-0.003318j
[2025-09-05 00:49:03] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -53.037670-0.003667j
[2025-09-05 00:49:13] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -53.052800-0.001731j
[2025-09-05 00:49:23] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -52.948649+0.003146j
[2025-09-05 00:49:34] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -52.971622-0.000152j
[2025-09-05 00:49:44] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -53.002950+0.001002j
[2025-09-05 00:49:54] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -52.969768-0.001350j
[2025-09-05 00:50:04] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -52.969386-0.004344j
[2025-09-05 00:50:14] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -52.934571+0.001483j
[2025-09-05 00:50:25] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -52.928089-0.000219j
[2025-09-05 00:50:35] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -53.011783+0.000207j
[2025-09-05 00:50:45] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -52.868726+0.006533j
[2025-09-05 00:50:55] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -52.900054+0.000190j
[2025-09-05 00:51:05] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -52.844372+0.000113j
[2025-09-05 00:51:16] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -52.759528+0.003075j
[2025-09-05 00:51:26] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -52.859483+0.000016j
[2025-09-05 00:51:36] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -52.835805+0.003930j
[2025-09-05 00:51:46] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -52.871953-0.001007j
[2025-09-05 00:51:57] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -52.801853-0.003896j
[2025-09-05 00:52:07] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -52.889799-0.003366j
[2025-09-05 00:52:17] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -52.774180+0.002321j
[2025-09-05 00:52:27] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -52.875137-0.001804j
[2025-09-05 00:52:37] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -52.736540-0.002738j
[2025-09-05 00:52:48] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -52.756243+0.001030j
[2025-09-05 00:52:58] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -52.855218+0.001870j
[2025-09-05 00:53:08] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -52.880825-0.000488j
[2025-09-05 00:53:18] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -52.872924-0.002756j
[2025-09-05 00:53:28] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -52.949754+0.001115j
[2025-09-05 00:53:39] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -52.956986-0.005004j
[2025-09-05 00:53:49] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -52.838623-0.002220j
[2025-09-05 00:53:59] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -52.906282-0.000786j
[2025-09-05 00:54:09] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -52.951454+0.002208j
[2025-09-05 00:54:19] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -52.919405-0.002591j
[2025-09-05 00:54:30] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -53.052694-0.001608j
[2025-09-05 00:54:40] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -52.961872-0.006023j
[2025-09-05 00:54:50] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -52.973777-0.002481j
[2025-09-05 00:55:00] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -52.997674+0.002805j
[2025-09-05 00:55:11] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -52.972659-0.002407j
[2025-09-05 00:55:21] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -53.040967-0.003059j
[2025-09-05 00:55:31] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -53.048252-0.000331j
[2025-09-05 00:55:41] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -52.959671+0.001684j
[2025-09-05 00:55:51] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -52.942332-0.001595j
[2025-09-05 00:56:02] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -52.894055-0.001692j
[2025-09-05 00:56:12] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -53.014117+0.001034j
[2025-09-05 00:56:22] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -52.895071-0.002853j
[2025-09-05 00:56:32] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -53.041734+0.001087j
[2025-09-05 00:56:42] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -52.960325+0.001549j
[2025-09-05 00:56:53] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -52.911349+0.002031j
[2025-09-05 00:57:03] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -52.963256+0.001581j
[2025-09-05 00:57:13] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -53.116411+0.001008j
[2025-09-05 00:57:23] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -53.055038-0.000502j
[2025-09-05 00:57:30] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -53.099726+0.001373j
[2025-09-05 00:57:35] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -53.015148-0.001480j
[2025-09-05 00:57:39] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -53.050951+0.001036j
[2025-09-05 00:57:39] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-05 00:57:39] ✅ Training completed | Restarts: 2
[2025-09-05 00:57:39] ============================================================
[2025-09-05 00:57:39] Training completed | Runtime: 10755.1s
[2025-09-05 00:57:41] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-05 00:57:41] ============================================================
