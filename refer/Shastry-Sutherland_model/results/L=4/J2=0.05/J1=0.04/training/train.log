[2025-09-04 18:59:26] ✓ 从checkpoint恢复: results/L=4/J2=0.05/J1=0.05/training/checkpoints/final_GCNN.pkl
[2025-09-04 18:59:26]   - 迭代次数: final
[2025-09-04 18:59:26]   - 能量: -52.160097+0.001458j ± 0.041241
[2025-09-04 18:59:26]   - 时间戳: 2025-09-04T15:04:42.590402+08:00
[2025-09-04 18:59:39] ✓ 变分状态参数已从checkpoint恢复
[2025-09-04 18:59:39] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-04 18:59:39] ==================================================
[2025-09-04 18:59:39] GCNN for Shastry-Sutherland Model
[2025-09-04 18:59:39] ==================================================
[2025-09-04 18:59:39] System parameters:
[2025-09-04 18:59:39]   - System size: L=4, N=64
[2025-09-04 18:59:39]   - System parameters: J1=0.04, J2=0.05, Q=0.95
[2025-09-04 18:59:39] --------------------------------------------------
[2025-09-04 18:59:39] Model parameters:
[2025-09-04 18:59:39]   - Number of layers = 4
[2025-09-04 18:59:39]   - Number of features = 4
[2025-09-04 18:59:39]   - Total parameters = 12572
[2025-09-04 18:59:39] --------------------------------------------------
[2025-09-04 18:59:39] Training parameters:
[2025-09-04 18:59:39]   - Learning rate: 0.015
[2025-09-04 18:59:39]   - Total iterations: 1050
[2025-09-04 18:59:39]   - Annealing cycles: 3
[2025-09-04 18:59:39]   - Initial period: 150
[2025-09-04 18:59:39]   - Period multiplier: 2.0
[2025-09-04 18:59:39]   - Temperature range: 0.0-1.0
[2025-09-04 18:59:39]   - Samples: 4096
[2025-09-04 18:59:39]   - Discarded samples: 0
[2025-09-04 18:59:39]   - Chunk size: 2048
[2025-09-04 18:59:39]   - Diagonal shift: 0.2
[2025-09-04 18:59:39]   - Gradient clipping: 1.0
[2025-09-04 18:59:39]   - Checkpoint enabled: interval=105
[2025-09-04 18:59:39]   - Checkpoint directory: results/L=4/J2=0.05/J1=0.04/training/checkpoints
[2025-09-04 18:59:39] --------------------------------------------------
[2025-09-04 18:59:39] Device status:
[2025-09-04 18:59:39]   - Devices model: NVIDIA H200 NVL
[2025-09-04 18:59:39]   - Number of devices: 1
[2025-09-04 18:59:39]   - Sharding: True
[2025-09-04 18:59:39] ============================================================
[2025-09-04 19:00:24] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -51.747856+0.004973j
[2025-09-04 19:00:52] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -51.685556-0.002614j
[2025-09-04 19:01:02] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -51.586749+0.000650j
[2025-09-04 19:01:12] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -51.661293+0.004186j
[2025-09-04 19:01:22] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -51.599570+0.002631j
[2025-09-04 19:01:33] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -51.626255+0.001083j
[2025-09-04 19:01:43] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -51.579931+0.000773j
[2025-09-04 19:01:53] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -51.610207+0.000376j
[2025-09-04 19:02:03] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -51.595268+0.001271j
[2025-09-04 19:02:13] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -51.658429-0.000857j
[2025-09-04 19:02:23] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -51.745835-0.001086j
[2025-09-04 19:02:33] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -51.627062-0.005109j
[2025-09-04 19:02:43] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -51.727270+0.003680j
[2025-09-04 19:02:53] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -51.779324+0.006079j
[2025-09-04 19:03:03] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -51.673394-0.004149j
[2025-09-04 19:03:13] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -51.693952-0.003834j
[2025-09-04 19:03:23] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -51.829713+0.001803j
[2025-09-04 19:03:33] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -51.863997+0.002633j
[2025-09-04 19:03:43] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -51.839680+0.001987j
[2025-09-04 19:03:53] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -51.857553+0.001708j
[2025-09-04 19:04:04] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -51.835265+0.000381j
[2025-09-04 19:04:14] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -51.720439-0.002505j
[2025-09-04 19:04:24] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -51.806644-0.000048j
[2025-09-04 19:04:34] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -51.734749+0.001906j
[2025-09-04 19:04:44] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -51.849807-0.001541j
[2025-09-04 19:04:54] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -51.863388-0.001650j
[2025-09-04 19:05:04] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -51.822552+0.000791j
[2025-09-04 19:05:14] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -51.779710+0.001519j
[2025-09-04 19:05:24] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -51.827879-0.000523j
[2025-09-04 19:05:34] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -51.774884+0.003597j
[2025-09-04 19:05:44] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -51.700408-0.002543j
[2025-09-04 19:05:54] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -51.698738+0.000434j
[2025-09-04 19:06:04] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -51.797304-0.004158j
[2025-09-04 19:06:14] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -51.781892+0.002792j
[2025-09-04 19:06:24] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -51.677400+0.001816j
[2025-09-04 19:06:35] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -51.801149+0.004618j
[2025-09-04 19:06:45] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -51.812994+0.000592j
[2025-09-04 19:06:55] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -51.789062+0.005315j
[2025-09-04 19:07:05] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -51.887544+0.005107j
[2025-09-04 19:07:15] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -51.787895-0.002803j
[2025-09-04 19:07:25] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -51.773176+0.001165j
[2025-09-04 19:07:35] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -51.778200+0.005209j
[2025-09-04 19:07:45] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -51.703634+0.000640j
[2025-09-04 19:07:55] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -51.800287-0.001977j
[2025-09-04 19:08:05] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -51.614550+0.004621j
[2025-09-04 19:08:15] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -51.766877+0.002413j
[2025-09-04 19:08:25] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -51.890316+0.003906j
[2025-09-04 19:08:35] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -51.841531-0.000498j
[2025-09-04 19:08:46] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -51.763214+0.001556j
[2025-09-04 19:08:56] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -51.736847-0.004045j
[2025-09-04 19:09:06] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -51.790363+0.002267j
[2025-09-04 19:09:16] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -51.780379+0.008455j
[2025-09-04 19:09:26] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -51.743042+0.011892j
[2025-09-04 19:09:36] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -51.844966+0.000383j
[2025-09-04 19:09:46] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -51.872907-0.000810j
[2025-09-04 19:09:56] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -51.734204+0.000635j
[2025-09-04 19:10:06] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -51.746763-0.003307j
[2025-09-04 19:10:16] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -51.685394-0.000610j
[2025-09-04 19:10:26] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -51.752140-0.001048j
[2025-09-04 19:10:36] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -51.752934-0.000369j
[2025-09-04 19:10:47] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -51.679031+0.002991j
[2025-09-04 19:10:57] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -51.736616+0.001417j
[2025-09-04 19:11:07] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -51.798337-0.007242j
[2025-09-04 19:11:17] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -51.819508+0.000425j
[2025-09-04 19:11:27] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -51.725206+0.004467j
[2025-09-04 19:11:37] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -51.756773-0.000348j
[2025-09-04 19:11:47] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -51.690451-0.001126j
[2025-09-04 19:11:57] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -51.675540+0.001000j
[2025-09-04 19:12:07] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -51.698349+0.003699j
[2025-09-04 19:12:17] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -51.729658-0.003664j
[2025-09-04 19:12:27] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -51.598222-0.002344j
[2025-09-04 19:12:37] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -51.675699+0.002140j
[2025-09-04 19:12:47] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -51.659214+0.003414j
[2025-09-04 19:12:58] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -51.646373+0.004017j
[2025-09-04 19:13:08] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -51.556173-0.002605j
[2025-09-04 19:13:18] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -51.553472+0.004535j
[2025-09-04 19:13:28] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -51.621189-0.001496j
[2025-09-04 19:13:38] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -51.650720-0.002162j
[2025-09-04 19:13:48] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -51.629835-0.005240j
[2025-09-04 19:13:58] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -51.846931+0.004621j
[2025-09-04 19:14:08] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -51.786345+0.003268j
[2025-09-04 19:14:18] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -51.676406-0.002375j
[2025-09-04 19:14:28] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -51.627124-0.003943j
[2025-09-04 19:14:38] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -51.584151-0.003010j
[2025-09-04 19:14:48] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -51.733694-0.003711j
[2025-09-04 19:14:59] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -51.658221+0.001554j
[2025-09-04 19:15:09] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -51.723090+0.000077j
[2025-09-04 19:15:19] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -51.660111+0.000293j
[2025-09-04 19:15:29] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -51.730064+0.001729j
[2025-09-04 19:15:39] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -51.720775-0.003715j
[2025-09-04 19:15:49] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -51.817227+0.005005j
[2025-09-04 19:15:59] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -51.780487-0.006116j
[2025-09-04 19:16:09] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -51.707896+0.000239j
[2025-09-04 19:16:19] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -51.623057+0.001373j
[2025-09-04 19:16:29] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -51.623244+0.001799j
[2025-09-04 19:16:39] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -51.895548+0.001119j
[2025-09-04 19:16:49] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -51.716921-0.002178j
[2025-09-04 19:17:00] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -51.758950+0.004428j
[2025-09-04 19:17:10] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -51.637515-0.002943j
[2025-09-04 19:17:21] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -51.710334-0.003706j
[2025-09-04 19:17:31] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -51.731400-0.001868j
[2025-09-04 19:17:41] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -51.767230-0.002008j
[2025-09-04 19:17:51] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -51.690095-0.003668j
[2025-09-04 19:18:01] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -51.736124+0.003193j
[2025-09-04 19:18:11] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -51.562191-0.000793j
[2025-09-04 19:18:11] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-04 19:18:21] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -51.455604+0.000814j
[2025-09-04 19:18:31] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -51.496473-0.002368j
[2025-09-04 19:18:41] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -51.643149+0.000356j
[2025-09-04 19:18:51] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -51.750018-0.006878j
[2025-09-04 19:19:01] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -51.761596-0.002369j
[2025-09-04 19:19:12] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -51.745421+0.006637j
[2025-09-04 19:19:22] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -51.765437+0.005443j
[2025-09-04 19:19:32] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -51.835428-0.004065j
[2025-09-04 19:19:42] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -51.870324+0.001213j
[2025-09-04 19:19:52] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -51.793545-0.003190j
[2025-09-04 19:20:02] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -51.780313-0.001316j
[2025-09-04 19:20:12] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -51.799641+0.004004j
[2025-09-04 19:20:22] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -51.708946-0.001073j
[2025-09-04 19:20:32] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -51.750373+0.001852j
[2025-09-04 19:20:42] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -51.701280+0.002007j
[2025-09-04 19:20:52] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -51.750223-0.003174j
[2025-09-04 19:21:02] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -51.710769-0.006840j
[2025-09-04 19:21:13] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -51.754562-0.002762j
[2025-09-04 19:21:23] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -51.742793+0.002541j
[2025-09-04 19:21:33] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -51.823528-0.004963j
[2025-09-04 19:21:43] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -51.752532-0.003448j
[2025-09-04 19:21:53] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -51.805127-0.002748j
[2025-09-04 19:22:03] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -51.758630-0.000551j
[2025-09-04 19:22:13] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -51.812872+0.000131j
[2025-09-04 19:22:23] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -51.669692-0.001603j
[2025-09-04 19:22:33] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -51.672393-0.001860j
[2025-09-04 19:22:43] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -51.796924-0.004200j
[2025-09-04 19:22:53] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -51.701143-0.000119j
[2025-09-04 19:23:03] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -51.794385+0.004401j
[2025-09-04 19:23:14] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -51.851561+0.002304j
[2025-09-04 19:23:24] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -51.784055-0.001045j
[2025-09-04 19:23:34] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -51.924344-0.000049j
[2025-09-04 19:23:44] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -51.791029-0.001255j
[2025-09-04 19:23:54] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -51.778797-0.003853j
[2025-09-04 19:24:04] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -51.789014+0.002076j
[2025-09-04 19:24:14] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -51.814654-0.003715j
[2025-09-04 19:24:24] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -51.852454-0.006184j
[2025-09-04 19:24:34] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -51.848023+0.000078j
[2025-09-04 19:24:44] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -51.775798-0.000419j
[2025-09-04 19:24:54] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -51.815449+0.002191j
[2025-09-04 19:25:05] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -51.687703-0.004010j
[2025-09-04 19:25:15] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -51.607571-0.002129j
[2025-09-04 19:25:25] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -51.619169+0.001319j
[2025-09-04 19:25:35] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -51.758185+0.001127j
[2025-09-04 19:25:45] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -51.758817-0.002801j
[2025-09-04 19:25:45] RESTART #1 | Period: 300
[2025-09-04 19:25:55] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -51.764366+0.005203j
[2025-09-04 19:26:05] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -51.735269-0.002272j
[2025-09-04 19:26:15] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -51.741514+0.001505j
[2025-09-04 19:26:25] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -51.894589-0.000297j
[2025-09-04 19:26:35] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -51.854640-0.004920j
[2025-09-04 19:26:45] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -51.792538+0.005685j
[2025-09-04 19:26:55] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -51.797014-0.005434j
[2025-09-04 19:27:06] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -51.689370+0.002021j
[2025-09-04 19:27:16] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -51.707860+0.001224j
[2025-09-04 19:27:26] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -51.700943+0.003263j
[2025-09-04 19:27:36] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -51.745605-0.004389j
[2025-09-04 19:27:46] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -51.725869-0.004819j
[2025-09-04 19:27:56] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -51.656791-0.001253j
[2025-09-04 19:28:06] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -51.748472+0.001227j
[2025-09-04 19:28:16] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -51.803552+0.001409j
[2025-09-04 19:28:26] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -51.846711+0.001236j
[2025-09-04 19:28:36] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -51.915821+0.006305j
[2025-09-04 19:28:46] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -51.915367-0.002875j
[2025-09-04 19:28:56] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -51.965797+0.003092j
[2025-09-04 19:29:07] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -51.943471+0.000276j
[2025-09-04 19:29:17] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -51.877073-0.000502j
[2025-09-04 19:29:27] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -51.880793+0.000665j
[2025-09-04 19:29:37] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -51.892236-0.001210j
[2025-09-04 19:29:47] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -51.850996+0.003777j
[2025-09-04 19:29:57] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -51.722530+0.000887j
[2025-09-04 19:30:07] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -51.740754-0.002700j
[2025-09-04 19:30:17] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -51.755551+0.004155j
[2025-09-04 19:30:27] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -51.688551+0.000781j
[2025-09-04 19:30:37] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -51.800298+0.000733j
[2025-09-04 19:30:47] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -51.887242+0.001183j
[2025-09-04 19:30:58] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -51.668259-0.006052j
[2025-09-04 19:31:08] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -51.743645+0.003981j
[2025-09-04 19:31:18] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -51.667242-0.003375j
[2025-09-04 19:31:28] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -51.756360+0.008832j
[2025-09-04 19:31:38] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -51.760375+0.003417j
[2025-09-04 19:31:48] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -51.754734-0.003164j
[2025-09-04 19:31:58] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -51.777399+0.000656j
[2025-09-04 19:32:08] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -51.838663+0.000416j
[2025-09-04 19:32:18] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -51.642688+0.000371j
[2025-09-04 19:32:28] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -51.689668-0.000454j
[2025-09-04 19:32:38] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -51.721040-0.003186j
[2025-09-04 19:32:48] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -51.740476-0.000847j
[2025-09-04 19:32:59] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -51.556719+0.001461j
[2025-09-04 19:33:09] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -51.589006+0.002482j
[2025-09-04 19:33:19] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -51.493732+0.001682j
[2025-09-04 19:33:29] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -51.671816-0.000125j
[2025-09-04 19:33:39] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -51.626012+0.002768j
[2025-09-04 19:33:49] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -51.536523+0.002234j
[2025-09-04 19:33:59] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -51.588064+0.001032j
[2025-09-04 19:34:09] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -51.654233+0.004127j
[2025-09-04 19:34:19] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -51.760691-0.001369j
[2025-09-04 19:34:29] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -51.708763+0.001442j
[2025-09-04 19:34:39] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -51.713204-0.004811j
[2025-09-04 19:34:49] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -51.644651+0.004410j
[2025-09-04 19:35:00] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -51.728564+0.004633j
[2025-09-04 19:35:10] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -51.824884+0.001733j
[2025-09-04 19:35:20] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -51.742827+0.001385j
[2025-09-04 19:35:30] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -51.771460-0.000978j
[2025-09-04 19:35:40] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -51.612137-0.002526j
[2025-09-04 19:35:50] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -51.658188-0.002044j
[2025-09-04 19:35:50] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-04 19:36:00] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -51.684318-0.000775j
[2025-09-04 19:36:10] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -51.760754+0.000508j
[2025-09-04 19:36:20] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -51.819692+0.000552j
[2025-09-04 19:36:30] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -51.551564+0.000750j
[2025-09-04 19:36:40] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -51.638121-0.000804j
[2025-09-04 19:36:50] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -51.684791-0.000942j
[2025-09-04 19:37:01] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -51.764803-0.006530j
[2025-09-04 19:37:11] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -51.628824+0.000503j
[2025-09-04 19:37:21] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -51.764924-0.000027j
[2025-09-04 19:37:31] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -51.728969+0.001594j
[2025-09-04 19:37:41] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -51.733544+0.005797j
[2025-09-04 19:37:51] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -51.770342-0.001200j
[2025-09-04 19:38:01] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -51.846769+0.000683j
[2025-09-04 19:38:11] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -51.845233-0.001323j
[2025-09-04 19:38:21] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -51.924736-0.008006j
[2025-09-04 19:38:31] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -51.895409-0.002290j
[2025-09-04 19:38:42] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -51.802275-0.007952j
[2025-09-04 19:38:52] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -51.834655-0.005211j
[2025-09-04 19:39:02] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -51.804264-0.001749j
[2025-09-04 19:39:12] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -51.753271+0.002020j
[2025-09-04 19:39:22] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -51.901885+0.001010j
[2025-09-04 19:39:32] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -51.733052-0.001368j
[2025-09-04 19:39:42] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -51.726965+0.001672j
[2025-09-04 19:39:52] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -51.756351+0.002482j
[2025-09-04 19:40:02] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -51.698937+0.005650j
[2025-09-04 19:40:12] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -51.783136+0.003390j
[2025-09-04 19:40:22] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -51.892905+0.002710j
[2025-09-04 19:40:32] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -51.726764+0.003474j
[2025-09-04 19:40:43] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -51.672200-0.002956j
[2025-09-04 19:40:53] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -51.650766+0.003189j
[2025-09-04 19:41:03] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -51.642088+0.004487j
[2025-09-04 19:41:13] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -51.825949-0.002586j
[2025-09-04 19:41:23] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -51.802656-0.001453j
[2025-09-04 19:41:33] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -51.720728-0.000097j
[2025-09-04 19:41:43] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -51.693365+0.002490j
[2025-09-04 19:41:53] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -51.727165+0.000670j
[2025-09-04 19:42:03] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -51.745831-0.002930j
[2025-09-04 19:42:13] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -51.667659-0.005038j
[2025-09-04 19:42:23] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -51.650575-0.001207j
[2025-09-04 19:42:33] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -51.622967+0.002655j
[2025-09-04 19:42:44] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -51.709379-0.002567j
[2025-09-04 19:42:54] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -51.726153+0.002088j
[2025-09-04 19:43:04] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -51.784847+0.004894j
[2025-09-04 19:43:14] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -51.763345-0.001008j
[2025-09-04 19:43:24] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -51.706471+0.000375j
[2025-09-04 19:43:34] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -51.803166-0.002711j
[2025-09-04 19:43:44] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -51.786604-0.002231j
[2025-09-04 19:43:54] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -51.837861-0.002454j
[2025-09-04 19:44:04] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -51.847899+0.004090j
[2025-09-04 19:44:14] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -51.774948+0.001568j
[2025-09-04 19:44:24] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -51.717746-0.001429j
[2025-09-04 19:44:35] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -51.715187+0.000226j
[2025-09-04 19:44:45] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -51.700011-0.000446j
[2025-09-04 19:44:55] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -51.659057-0.000768j
[2025-09-04 19:45:05] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -51.598740+0.004068j
[2025-09-04 19:45:15] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -51.751914+0.004387j
[2025-09-04 19:45:25] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -51.748948+0.004210j
[2025-09-04 19:45:35] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -51.720267+0.004157j
[2025-09-04 19:45:45] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -51.734744+0.003640j
[2025-09-04 19:45:55] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -51.929528-0.000127j
[2025-09-04 19:46:05] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -51.807923+0.002334j
[2025-09-04 19:46:15] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -51.955493+0.000837j
[2025-09-04 19:46:25] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -51.952673+0.005879j
[2025-09-04 19:46:36] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -51.882817-0.002140j
[2025-09-04 19:46:46] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -51.916043-0.004206j
[2025-09-04 19:46:56] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -51.835134+0.005008j
[2025-09-04 19:47:06] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -51.905631+0.001719j
[2025-09-04 19:47:16] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -51.957795-0.000100j
[2025-09-04 19:47:26] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -51.881009-0.003775j
[2025-09-04 19:47:36] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -51.740739-0.004488j
[2025-09-04 19:47:46] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -51.641323-0.002712j
[2025-09-04 19:47:56] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -51.818371-0.001069j
[2025-09-04 19:48:06] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -51.692966-0.006847j
[2025-09-04 19:48:16] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -51.829959-0.003613j
[2025-09-04 19:48:26] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -51.802339+0.003263j
[2025-09-04 19:48:37] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -51.767495+0.001307j
[2025-09-04 19:48:47] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -51.779124+0.001459j
[2025-09-04 19:48:57] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -51.795453+0.001043j
[2025-09-04 19:49:07] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -51.788188-0.002864j
[2025-09-04 19:49:17] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -51.662850-0.001638j
[2025-09-04 19:49:27] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -51.609009+0.001512j
[2025-09-04 19:49:37] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -51.815467+0.000473j
[2025-09-04 19:49:47] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -51.665992+0.001830j
[2025-09-04 19:49:57] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -51.612856-0.005074j
[2025-09-04 19:50:07] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -51.632070+0.004058j
[2025-09-04 19:50:17] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -51.793581-0.000963j
[2025-09-04 19:50:28] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -51.667177-0.001723j
[2025-09-04 19:50:38] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -51.810702-0.006346j
[2025-09-04 19:50:48] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -51.843596+0.001935j
[2025-09-04 19:50:58] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -51.900723-0.003475j
[2025-09-04 19:51:08] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -51.912826-0.000086j
[2025-09-04 19:51:18] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -51.935506+0.001800j
[2025-09-04 19:51:28] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -51.788009-0.000465j
[2025-09-04 19:51:38] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -51.735754-0.003837j
[2025-09-04 19:51:48] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -51.721185-0.000068j
[2025-09-04 19:51:58] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -51.802102+0.000056j
[2025-09-04 19:52:08] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -51.621992+0.006757j
[2025-09-04 19:52:18] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -51.661925+0.004137j
[2025-09-04 19:52:29] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -51.732659+0.000369j
[2025-09-04 19:52:39] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -51.749481+0.002003j
[2025-09-04 19:52:49] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -51.722730-0.001430j
[2025-09-04 19:52:59] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -51.809727+0.003391j
[2025-09-04 19:53:09] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -51.757574-0.004603j
[2025-09-04 19:53:19] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -51.747107-0.004942j
[2025-09-04 19:53:29] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -51.789709+0.002215j
[2025-09-04 19:53:29] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-04 19:53:39] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -51.663359+0.004868j
[2025-09-04 19:53:49] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -51.665477+0.008413j
[2025-09-04 19:53:59] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -51.665423-0.000284j
[2025-09-04 19:54:09] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -51.669806-0.004865j
[2025-09-04 19:54:20] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -51.651705+0.002252j
[2025-09-04 19:54:30] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -51.644238+0.000856j
[2025-09-04 19:54:40] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -51.621093+0.000480j
[2025-09-04 19:54:50] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -51.740235-0.006610j
[2025-09-04 19:55:00] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -51.930332+0.001607j
[2025-09-04 19:55:10] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -51.764498+0.002166j
[2025-09-04 19:55:20] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -51.745906+0.001144j
[2025-09-04 19:55:30] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -51.791314-0.003574j
[2025-09-04 19:55:40] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -51.665811+0.001845j
[2025-09-04 19:55:50] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -51.639007-0.000281j
[2025-09-04 19:56:00] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -51.707752-0.002694j
[2025-09-04 19:56:10] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -51.643173+0.007764j
[2025-09-04 19:56:20] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -51.797513+0.001572j
[2025-09-04 19:56:30] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -51.675008-0.000413j
[2025-09-04 19:56:40] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -51.726399+0.004383j
[2025-09-04 19:56:50] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -51.935757-0.000455j
[2025-09-04 19:57:00] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -51.843229+0.005105j
[2025-09-04 19:57:11] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -51.705523-0.001067j
[2025-09-04 19:57:21] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -51.713325+0.001124j
[2025-09-04 19:57:30] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -51.693643-0.002177j
[2025-09-04 19:57:41] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -51.720330+0.001698j
[2025-09-04 19:57:51] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -51.487545+0.004793j
[2025-09-04 19:58:01] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -51.626673+0.007436j
[2025-09-04 19:58:11] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -51.624275-0.002495j
[2025-09-04 19:58:21] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -51.618845+0.005379j
[2025-09-04 19:58:31] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -51.734938-0.003543j
[2025-09-04 19:58:41] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -51.810759-0.000780j
[2025-09-04 19:58:51] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -51.683241+0.003378j
[2025-09-04 19:59:01] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -51.683644+0.000796j
[2025-09-04 19:59:11] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -51.659445+0.004524j
[2025-09-04 19:59:21] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -51.751225+0.002254j
[2025-09-04 19:59:31] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -51.795979-0.002500j
[2025-09-04 19:59:42] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -51.691546-0.000723j
[2025-09-04 19:59:52] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -51.754627-0.002057j
[2025-09-04 20:00:02] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -51.812296+0.002634j
[2025-09-04 20:00:12] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -51.676285+0.006360j
[2025-09-04 20:00:22] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -51.799670+0.004080j
[2025-09-04 20:00:32] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -51.854653+0.000576j
[2025-09-04 20:00:42] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -51.788203-0.004895j
[2025-09-04 20:00:52] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -51.691245-0.006435j
[2025-09-04 20:01:02] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -51.714123+0.003105j
[2025-09-04 20:01:12] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -51.699087-0.000255j
[2025-09-04 20:01:22] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -51.651368+0.002701j
[2025-09-04 20:01:32] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -51.618732+0.002382j
[2025-09-04 20:01:43] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -51.698278+0.001593j
[2025-09-04 20:01:53] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -51.699361-0.001766j
[2025-09-04 20:02:03] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -51.803014-0.006310j
[2025-09-04 20:02:13] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -51.823138+0.001486j
[2025-09-04 20:02:23] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -51.665990+0.001868j
[2025-09-04 20:02:33] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -51.640047-0.001763j
[2025-09-04 20:02:43] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -51.706602-0.003268j
[2025-09-04 20:02:53] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -51.758008+0.003340j
[2025-09-04 20:03:03] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -51.853387+0.002235j
[2025-09-04 20:03:13] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -51.691934+0.000598j
[2025-09-04 20:03:23] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -51.724113-0.003498j
[2025-09-04 20:03:34] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -51.734772-0.000910j
[2025-09-04 20:03:44] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -51.703934-0.003319j
[2025-09-04 20:03:54] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -51.693128+0.002011j
[2025-09-04 20:04:04] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -51.679573-0.003922j
[2025-09-04 20:04:14] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -51.704620+0.006624j
[2025-09-04 20:04:24] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -51.824333+0.002373j
[2025-09-04 20:04:34] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -51.766924-0.002571j
[2025-09-04 20:04:44] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -51.832636+0.001724j
[2025-09-04 20:04:54] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -51.765229-0.003921j
[2025-09-04 20:05:04] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -51.887837+0.000047j
[2025-09-04 20:05:14] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -51.667481+0.000547j
[2025-09-04 20:05:24] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -51.784525-0.002514j
[2025-09-04 20:05:35] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -51.727948-0.005360j
[2025-09-04 20:05:45] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -51.783662+0.000730j
[2025-09-04 20:05:55] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -51.657078+0.000620j
[2025-09-04 20:06:05] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -51.755868+0.004111j
[2025-09-04 20:06:15] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -51.698465-0.005353j
[2025-09-04 20:06:25] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -51.784580+0.004676j
[2025-09-04 20:06:35] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -51.879389+0.003552j
[2025-09-04 20:06:45] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -51.682564+0.000133j
[2025-09-04 20:06:55] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -51.619984-0.001211j
[2025-09-04 20:07:05] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -51.736311+0.002726j
[2025-09-04 20:07:15] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -51.729562-0.004008j
[2025-09-04 20:07:25] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -51.930056-0.005507j
[2025-09-04 20:07:36] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -51.798701-0.000010j
[2025-09-04 20:07:46] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -51.838062-0.000364j
[2025-09-04 20:07:56] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -51.761335+0.003491j
[2025-09-04 20:08:06] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -51.753217+0.001220j
[2025-09-04 20:08:16] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -51.898262+0.001331j
[2025-09-04 20:08:26] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -51.650384-0.001249j
[2025-09-04 20:08:36] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -51.709621+0.002163j
[2025-09-04 20:08:46] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -51.736818+0.000777j
[2025-09-04 20:08:56] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -51.797427-0.000907j
[2025-09-04 20:09:06] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -51.634445-0.000287j
[2025-09-04 20:09:16] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -51.710085+0.000115j
[2025-09-04 20:09:26] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -51.661517+0.002840j
[2025-09-04 20:09:37] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -51.695478-0.002145j
[2025-09-04 20:09:47] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -51.681245-0.001203j
[2025-09-04 20:09:57] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -51.662534+0.003967j
[2025-09-04 20:10:07] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -51.764212+0.000712j
[2025-09-04 20:10:17] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -51.701692+0.003375j
[2025-09-04 20:10:27] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -51.697648+0.003552j
[2025-09-04 20:10:37] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -51.708597-0.001446j
[2025-09-04 20:10:47] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -51.734724+0.000746j
[2025-09-04 20:10:58] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -51.660451-0.003903j
[2025-09-04 20:11:08] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -51.813754-0.002160j
[2025-09-04 20:11:08] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-04 20:11:18] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -51.859939+0.002086j
[2025-09-04 20:11:28] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -51.818783-0.003567j
[2025-09-04 20:11:38] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -51.648500-0.000103j
[2025-09-04 20:11:48] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -51.607147+0.000901j
[2025-09-04 20:11:58] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -51.617271-0.002885j
[2025-09-04 20:12:08] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -51.593748-0.006261j
[2025-09-04 20:12:18] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -51.632334+0.001532j
[2025-09-04 20:12:28] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -51.652540-0.002290j
[2025-09-04 20:12:38] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -51.630530+0.001041j
[2025-09-04 20:12:48] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -51.683796-0.000155j
[2025-09-04 20:12:59] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -51.620861-0.002233j
[2025-09-04 20:13:09] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -51.615678-0.001119j
[2025-09-04 20:13:19] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -51.637149-0.002380j
[2025-09-04 20:13:29] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -51.603942-0.001704j
[2025-09-04 20:13:39] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -51.669309-0.003586j
[2025-09-04 20:13:49] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -51.704992-0.002044j
[2025-09-04 20:13:59] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -51.620327+0.002214j
[2025-09-04 20:14:09] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -51.754701-0.003677j
[2025-09-04 20:14:19] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -51.785511+0.000217j
[2025-09-04 20:14:29] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -51.892563+0.004759j
[2025-09-04 20:14:39] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -51.708805+0.002148j
[2025-09-04 20:14:49] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -51.764976+0.001128j
[2025-09-04 20:14:59] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -51.679066-0.006729j
[2025-09-04 20:15:10] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -51.548023-0.003065j
[2025-09-04 20:15:20] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -51.623812+0.002756j
[2025-09-04 20:15:30] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -51.686856-0.002069j
[2025-09-04 20:15:40] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -51.616896+0.000420j
[2025-09-04 20:15:50] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -51.696530-0.003337j
[2025-09-04 20:16:00] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -51.614452+0.003773j
[2025-09-04 20:16:10] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -51.710610-0.001333j
[2025-09-04 20:16:10] RESTART #2 | Period: 600
[2025-09-04 20:16:20] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -51.756449-0.005111j
[2025-09-04 20:16:30] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -51.724713-0.003215j
[2025-09-04 20:16:40] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -51.705365-0.007907j
[2025-09-04 20:16:50] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -51.812377-0.004447j
[2025-09-04 20:17:01] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -51.749953+0.004764j
[2025-09-04 20:17:11] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -51.737071-0.000679j
[2025-09-04 20:17:21] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -51.574041-0.001850j
[2025-09-04 20:17:31] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -51.817078-0.000613j
[2025-09-04 20:17:41] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -51.861631+0.001800j
[2025-09-04 20:17:51] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -51.792189+0.001035j
[2025-09-04 20:18:01] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -51.805865-0.001061j
[2025-09-04 20:18:11] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -51.781019-0.003523j
[2025-09-04 20:18:21] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -51.813945+0.000857j
[2025-09-04 20:18:31] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -51.666198+0.000034j
[2025-09-04 20:18:41] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -51.766149-0.000869j
[2025-09-04 20:18:51] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -51.658035-0.002334j
[2025-09-04 20:19:02] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -51.724533-0.003322j
[2025-09-04 20:19:12] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -51.600397+0.000684j
[2025-09-04 20:19:22] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -51.768939-0.000700j
[2025-09-04 20:19:32] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -51.710460-0.005702j
[2025-09-04 20:19:42] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -51.798717+0.003050j
[2025-09-04 20:19:52] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -51.785349-0.001903j
[2025-09-04 20:20:02] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -51.878106+0.001564j
[2025-09-04 20:20:12] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -51.897371-0.000162j
[2025-09-04 20:20:22] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -51.824090+0.001420j
[2025-09-04 20:20:32] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -51.866333-0.001872j
[2025-09-04 20:20:42] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -51.796738+0.000666j
[2025-09-04 20:20:52] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -51.867473-0.000245j
[2025-09-04 20:21:03] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -51.717460+0.000786j
[2025-09-04 20:21:13] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -51.677730-0.003449j
[2025-09-04 20:21:23] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -51.681474-0.002774j
[2025-09-04 20:21:33] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -51.654366-0.004870j
[2025-09-04 20:21:43] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -51.539552+0.001484j
[2025-09-04 20:21:53] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -51.744470-0.000518j
[2025-09-04 20:22:03] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -51.752240+0.001810j
[2025-09-04 20:22:13] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -51.863785-0.003851j
[2025-09-04 20:22:23] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -51.685370+0.006403j
[2025-09-04 20:22:33] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -51.704403-0.000977j
[2025-09-04 20:22:43] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -51.764357+0.001603j
[2025-09-04 20:22:54] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -51.720124+0.000620j
[2025-09-04 20:23:04] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -51.767000+0.001852j
[2025-09-04 20:23:14] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -51.809181-0.002673j
[2025-09-04 20:23:24] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -51.693620+0.004101j
[2025-09-04 20:23:34] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -51.775722+0.004296j
[2025-09-04 20:23:44] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -51.836283+0.002491j
[2025-09-04 20:23:54] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -51.746715-0.002218j
[2025-09-04 20:24:04] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -51.649646-0.005196j
[2025-09-04 20:24:14] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -51.791934-0.000610j
[2025-09-04 20:24:24] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -51.755574-0.001676j
[2025-09-04 20:24:34] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -51.742922+0.001886j
[2025-09-04 20:24:44] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -51.812400-0.000636j
[2025-09-04 20:24:55] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -51.751454-0.001513j
[2025-09-04 20:25:05] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -51.912430+0.003290j
[2025-09-04 20:25:15] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -51.845075-0.006395j
[2025-09-04 20:25:25] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -51.875513-0.004930j
[2025-09-04 20:25:35] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -51.784374+0.001723j
[2025-09-04 20:25:45] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -51.850053+0.001919j
[2025-09-04 20:25:55] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -51.750274-0.001165j
[2025-09-04 20:26:05] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -51.809345+0.001033j
[2025-09-04 20:26:15] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -51.793380+0.005920j
[2025-09-04 20:26:25] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -51.625581-0.002482j
[2025-09-04 20:26:35] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -51.765012-0.000265j
[2025-09-04 20:26:46] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -51.730870-0.002272j
[2025-09-04 20:26:56] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -51.806161+0.001295j
[2025-09-04 20:27:06] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -51.777213-0.000792j
[2025-09-04 20:27:16] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -51.784272-0.002830j
[2025-09-04 20:27:26] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -51.874883-0.000056j
[2025-09-04 20:27:36] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -51.913968-0.002262j
[2025-09-04 20:27:46] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -51.746613-0.000771j
[2025-09-04 20:27:56] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -51.797556+0.003307j
[2025-09-04 20:28:06] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -51.700529-0.007027j
[2025-09-04 20:28:16] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -51.796688-0.002161j
[2025-09-04 20:28:26] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -51.787262+0.002092j
[2025-09-04 20:28:36] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -51.828047-0.004872j
[2025-09-04 20:28:47] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -51.831916+0.006636j
[2025-09-04 20:28:47] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-04 20:28:57] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -51.862643-0.002724j
[2025-09-04 20:29:07] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -51.853662+0.000147j
[2025-09-04 20:29:17] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -51.867725+0.002024j
[2025-09-04 20:29:27] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -51.829643+0.000667j
[2025-09-04 20:29:37] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -51.926992-0.002143j
[2025-09-04 20:29:47] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -51.741615-0.002483j
[2025-09-04 20:29:57] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -51.863382+0.002366j
[2025-09-04 20:30:07] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -51.714550-0.000881j
[2025-09-04 20:30:17] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -51.746038-0.005417j
[2025-09-04 20:30:27] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -51.774836-0.004774j
[2025-09-04 20:30:37] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -51.775500+0.000399j
[2025-09-04 20:30:48] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -51.711325-0.006061j
[2025-09-04 20:30:58] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -51.748983+0.002000j
[2025-09-04 20:31:08] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -51.693815+0.003187j
[2025-09-04 20:31:18] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -51.700614+0.000387j
[2025-09-04 20:31:28] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -51.629152-0.001187j
[2025-09-04 20:31:38] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -51.830382-0.000570j
[2025-09-04 20:31:48] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -51.801536-0.003320j
[2025-09-04 20:31:58] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -51.831812-0.001238j
[2025-09-04 20:32:08] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -51.832097-0.003980j
[2025-09-04 20:32:18] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -51.789834+0.007809j
[2025-09-04 20:32:28] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -51.790939+0.004314j
[2025-09-04 20:32:38] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -51.771755-0.001552j
[2025-09-04 20:32:49] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -51.729816-0.000625j
[2025-09-04 20:32:59] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -51.755613-0.002424j
[2025-09-04 20:33:09] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -51.883201+0.001957j
[2025-09-04 20:33:19] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -51.644353+0.003885j
[2025-09-04 20:33:29] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -51.875139+0.002320j
[2025-09-04 20:33:39] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -51.668470-0.000022j
[2025-09-04 20:33:49] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -51.659267-0.005851j
[2025-09-04 20:33:59] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -51.713503+0.001126j
[2025-09-04 20:34:09] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -51.752659+0.000602j
[2025-09-04 20:34:19] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -51.720150+0.000486j
[2025-09-04 20:34:29] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -51.744482-0.001414j
[2025-09-04 20:34:40] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -51.624380+0.000933j
[2025-09-04 20:34:50] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -51.702970+0.001852j
[2025-09-04 20:35:00] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -51.745470-0.001968j
[2025-09-04 20:35:10] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -51.863470-0.001120j
[2025-09-04 20:35:20] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -51.683737+0.001721j
[2025-09-04 20:35:30] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -51.670914-0.005539j
[2025-09-04 20:35:40] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -51.760911+0.000334j
[2025-09-04 20:35:50] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -51.900398-0.004449j
[2025-09-04 20:36:00] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -51.875502+0.001773j
[2025-09-04 20:36:10] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -51.782517+0.001560j
[2025-09-04 20:36:20] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -51.857165-0.001985j
[2025-09-04 20:36:30] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -51.846588+0.000941j
[2025-09-04 20:36:41] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -51.873379-0.001548j
[2025-09-04 20:36:51] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -51.864904+0.004596j
[2025-09-04 20:37:01] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -51.828788+0.002284j
[2025-09-04 20:37:11] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -51.774994-0.001970j
[2025-09-04 20:37:21] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -51.796213+0.001748j
[2025-09-04 20:37:31] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -51.691116-0.002698j
[2025-09-04 20:37:41] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -51.697214-0.003994j
[2025-09-04 20:37:51] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -51.791910-0.001091j
[2025-09-04 20:38:02] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -51.640859-0.000042j
[2025-09-04 20:38:12] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -51.810586+0.003481j
[2025-09-04 20:38:22] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -51.718072+0.000280j
[2025-09-04 20:38:32] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -51.550043-0.003556j
[2025-09-04 20:38:42] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -51.657507-0.004330j
[2025-09-04 20:38:52] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -51.608310-0.002477j
[2025-09-04 20:39:02] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -51.747366-0.000707j
[2025-09-04 20:39:12] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -51.777083-0.004810j
[2025-09-04 20:39:22] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -51.724884-0.001899j
[2025-09-04 20:39:32] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -51.787418+0.000386j
[2025-09-04 20:39:42] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -51.793135-0.002311j
[2025-09-04 20:39:53] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -51.801527-0.003550j
[2025-09-04 20:40:03] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -51.773428-0.000170j
[2025-09-04 20:40:13] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -51.789594-0.008467j
[2025-09-04 20:40:23] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -51.792154-0.000531j
[2025-09-04 20:40:33] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -51.854968-0.005147j
[2025-09-04 20:40:43] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -51.724890+0.000866j
[2025-09-04 20:40:53] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -51.577559-0.000829j
[2025-09-04 20:41:03] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -51.613451-0.004546j
[2025-09-04 20:41:13] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -51.738160-0.001323j
[2025-09-04 20:41:23] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -51.797396+0.002143j
[2025-09-04 20:41:33] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -51.754318-0.001777j
[2025-09-04 20:41:43] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -51.725638-0.002316j
[2025-09-04 20:41:54] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -51.621389+0.000640j
[2025-09-04 20:42:04] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -51.651476-0.002469j
[2025-09-04 20:42:14] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -51.721055+0.001294j
[2025-09-04 20:42:24] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -51.707230-0.001011j
[2025-09-04 20:42:34] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -51.645353-0.000598j
[2025-09-04 20:42:44] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -51.608835-0.002540j
[2025-09-04 20:42:54] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -51.610825+0.001626j
[2025-09-04 20:43:04] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -51.737745+0.005392j
[2025-09-04 20:43:14] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -51.643605-0.006997j
[2025-09-04 20:43:24] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -51.694995-0.000242j
[2025-09-04 20:43:34] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -51.719034+0.002226j
[2025-09-04 20:43:45] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -51.742858-0.001550j
[2025-09-04 20:43:55] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -51.707874-0.006092j
[2025-09-04 20:44:05] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -51.902306+0.000131j
[2025-09-04 20:44:15] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -51.810955+0.001130j
[2025-09-04 20:44:25] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -51.717933-0.000315j
[2025-09-04 20:44:35] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -51.658621+0.005194j
[2025-09-04 20:44:45] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -51.726862-0.000610j
[2025-09-04 20:44:55] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -51.743023-0.000336j
[2025-09-04 20:45:05] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -51.730216-0.001590j
[2025-09-04 20:45:15] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -51.712921-0.000788j
[2025-09-04 20:45:25] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -51.759919+0.003274j
[2025-09-04 20:45:35] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -51.800180-0.002146j
[2025-09-04 20:45:46] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -51.745354+0.001778j
[2025-09-04 20:45:56] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -51.775800-0.001320j
[2025-09-04 20:46:06] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -51.847085+0.003046j
[2025-09-04 20:46:16] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -51.814787+0.000323j
[2025-09-04 20:46:26] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -51.755249-0.004411j
[2025-09-04 20:46:26] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-04 20:46:36] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -51.826971-0.003218j
[2025-09-04 20:46:46] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -51.849066+0.005681j
[2025-09-04 20:46:56] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -51.694022-0.001150j
[2025-09-04 20:47:06] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -51.711127+0.000840j
[2025-09-04 20:47:16] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -51.759058+0.006197j
[2025-09-04 20:47:26] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -51.775033-0.000282j
[2025-09-04 20:47:36] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -51.649492-0.000730j
[2025-09-04 20:47:47] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -51.646902+0.006257j
[2025-09-04 20:47:57] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -51.677487-0.005658j
[2025-09-04 20:48:07] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -51.663815+0.000556j
[2025-09-04 20:48:17] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -51.827566-0.002643j
[2025-09-04 20:48:27] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -51.748052+0.003148j
[2025-09-04 20:48:37] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -51.679549-0.000125j
[2025-09-04 20:48:47] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -51.811984-0.002511j
[2025-09-04 20:48:57] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -51.898679-0.000071j
[2025-09-04 20:49:07] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -51.731067-0.001031j
[2025-09-04 20:49:17] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -51.594608+0.001899j
[2025-09-04 20:49:27] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -51.691513+0.007507j
[2025-09-04 20:49:37] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -51.700287+0.002346j
[2025-09-04 20:49:48] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -51.731167-0.000776j
[2025-09-04 20:49:58] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -51.700303+0.001937j
[2025-09-04 20:50:08] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -51.754147+0.001448j
[2025-09-04 20:50:18] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -51.841613+0.003253j
[2025-09-04 20:50:28] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -51.755884-0.003127j
[2025-09-04 20:50:38] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -51.782906-0.003488j
[2025-09-04 20:50:48] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -51.751981-0.002516j
[2025-09-04 20:50:58] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -51.720029-0.000478j
[2025-09-04 20:51:08] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -51.660212-0.000478j
[2025-09-04 20:51:18] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -51.697113-0.000470j
[2025-09-04 20:51:28] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -51.749985+0.005274j
[2025-09-04 20:51:39] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -51.792866-0.008876j
[2025-09-04 20:51:49] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -51.754191-0.001828j
[2025-09-04 20:51:59] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -51.795341+0.005696j
[2025-09-04 20:52:09] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -51.717170+0.005963j
[2025-09-04 20:52:19] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -51.749977+0.000454j
[2025-09-04 20:52:29] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -51.822919-0.003420j
[2025-09-04 20:52:39] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -51.780766+0.002863j
[2025-09-04 20:52:49] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -51.733952+0.005532j
[2025-09-04 20:52:59] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -51.773939+0.003203j
[2025-09-04 20:53:09] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -51.717401+0.000471j
[2025-09-04 20:53:19] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -51.771454-0.003120j
[2025-09-04 20:53:29] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -51.830559+0.002388j
[2025-09-04 20:53:40] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -51.820111+0.001223j
[2025-09-04 20:53:50] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -51.789009-0.004150j
[2025-09-04 20:54:00] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -51.816384+0.004766j
[2025-09-04 20:54:10] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -51.697190+0.001541j
[2025-09-04 20:54:20] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -51.753581-0.000248j
[2025-09-04 20:54:30] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -51.729787-0.001055j
[2025-09-04 20:54:40] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -51.773981+0.001584j
[2025-09-04 20:54:50] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -51.805793+0.000802j
[2025-09-04 20:55:00] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -51.816982+0.005329j
[2025-09-04 20:55:10] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -51.826702-0.003936j
[2025-09-04 20:55:20] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -51.693658+0.000582j
[2025-09-04 20:55:30] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -51.777941-0.000640j
[2025-09-04 20:55:41] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -51.716215-0.000945j
[2025-09-04 20:55:51] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -51.717156-0.000159j
[2025-09-04 20:56:01] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -51.644374+0.003091j
[2025-09-04 20:56:11] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -51.736707-0.003936j
[2025-09-04 20:56:21] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -51.523360-0.000690j
[2025-09-04 20:56:31] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -51.625967-0.006818j
[2025-09-04 20:56:41] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -51.632371-0.005635j
[2025-09-04 20:56:51] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -51.716033+0.002941j
[2025-09-04 20:57:01] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -51.610657+0.005125j
[2025-09-04 20:57:11] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -51.589633+0.003359j
[2025-09-04 20:57:21] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -51.714037-0.006253j
[2025-09-04 20:57:32] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -51.568044-0.001480j
[2025-09-04 20:57:42] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -51.593270-0.001480j
[2025-09-04 20:57:52] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -51.777958-0.001167j
[2025-09-04 20:58:02] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -51.676880-0.000102j
[2025-09-04 20:58:12] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -51.785379-0.003158j
[2025-09-04 20:58:22] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -51.753616-0.002826j
[2025-09-04 20:58:32] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -51.787583-0.000530j
[2025-09-04 20:58:42] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -51.730733+0.001920j
[2025-09-04 20:58:52] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -51.804180+0.002548j
[2025-09-04 20:59:02] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -51.742790-0.000938j
[2025-09-04 20:59:12] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -51.789419-0.000473j
[2025-09-04 20:59:22] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -51.849189-0.011740j
[2025-09-04 20:59:33] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -51.723836-0.000639j
[2025-09-04 20:59:43] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -51.581746-0.001574j
[2025-09-04 20:59:53] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -51.630252-0.002644j
[2025-09-04 21:00:03] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -51.575445-0.000714j
[2025-09-04 21:00:13] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -51.575485-0.003026j
[2025-09-04 21:00:23] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -51.818235+0.007751j
[2025-09-04 21:00:33] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -51.763085+0.000311j
[2025-09-04 21:00:43] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -51.778547+0.000414j
[2025-09-04 21:00:53] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -51.804193-0.001008j
[2025-09-04 21:01:03] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -51.645352-0.001479j
[2025-09-04 21:01:13] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -51.708856-0.002309j
[2025-09-04 21:01:23] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -51.625149-0.001051j
[2025-09-04 21:01:33] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -51.756374-0.001036j
[2025-09-04 21:01:44] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -51.763657-0.000065j
[2025-09-04 21:01:54] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -51.833049-0.001116j
[2025-09-04 21:02:04] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -51.841704+0.002885j
[2025-09-04 21:02:14] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -51.829300+0.000534j
[2025-09-04 21:02:24] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -51.826630+0.003120j
[2025-09-04 21:02:34] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -51.765787-0.002752j
[2025-09-04 21:02:44] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -51.931464-0.003384j
[2025-09-04 21:02:54] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -51.954948+0.000593j
[2025-09-04 21:03:04] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -51.853176-0.001004j
[2025-09-04 21:03:14] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -51.734414+0.001149j
[2025-09-04 21:03:24] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -51.784761-0.003093j
[2025-09-04 21:03:35] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -51.747561-0.001266j
[2025-09-04 21:03:45] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -51.757896-0.002991j
[2025-09-04 21:03:55] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -51.818032-0.001626j
[2025-09-04 21:04:05] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -51.682275-0.005946j
[2025-09-04 21:04:05] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-04 21:04:15] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -51.727450-0.003006j
[2025-09-04 21:04:25] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -51.791494-0.000750j
[2025-09-04 21:04:35] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -51.727220-0.001264j
[2025-09-04 21:04:45] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -51.706041+0.000091j
[2025-09-04 21:04:55] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -51.782984+0.002242j
[2025-09-04 21:05:05] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -51.784744+0.000492j
[2025-09-04 21:05:15] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -51.762167+0.001606j
[2025-09-04 21:05:26] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -51.866419-0.000631j
[2025-09-04 21:05:36] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -51.768086-0.002949j
[2025-09-04 21:05:46] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -51.797439-0.001416j
[2025-09-04 21:05:56] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -51.736768-0.002211j
[2025-09-04 21:06:06] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -51.646889-0.002517j
[2025-09-04 21:06:16] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -51.716863+0.003118j
[2025-09-04 21:06:26] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -51.710217+0.000943j
[2025-09-04 21:06:36] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -51.676572+0.002865j
[2025-09-04 21:06:46] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -51.619568-0.001597j
[2025-09-04 21:06:56] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -51.629300+0.002535j
[2025-09-04 21:07:06] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -51.625871-0.002046j
[2025-09-04 21:07:17] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -51.600101+0.002028j
[2025-09-04 21:07:27] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -51.712496+0.000181j
[2025-09-04 21:07:37] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -51.712329-0.002560j
[2025-09-04 21:07:47] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -51.699657+0.000683j
[2025-09-04 21:07:57] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -51.824629-0.004062j
[2025-09-04 21:08:07] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -51.690032+0.003326j
[2025-09-04 21:08:17] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -51.772046+0.000853j
[2025-09-04 21:08:27] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -51.676021-0.002504j
[2025-09-04 21:08:37] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -51.620904-0.002077j
[2025-09-04 21:08:47] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -51.728157-0.004425j
[2025-09-04 21:08:57] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -51.704495+0.000639j
[2025-09-04 21:09:07] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -51.663332-0.000585j
[2025-09-04 21:09:18] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -51.835987-0.001685j
[2025-09-04 21:09:28] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -51.777164+0.003178j
[2025-09-04 21:09:38] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -51.761147-0.006502j
[2025-09-04 21:09:48] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -51.727078+0.002817j
[2025-09-04 21:09:58] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -51.739307-0.000547j
[2025-09-04 21:10:08] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -51.819429-0.009212j
[2025-09-04 21:10:18] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -51.731066-0.003296j
[2025-09-04 21:10:28] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -51.739296+0.000809j
[2025-09-04 21:10:38] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -51.840071-0.004332j
[2025-09-04 21:10:48] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -51.767815+0.000699j
[2025-09-04 21:10:58] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -51.770888-0.000394j
[2025-09-04 21:11:09] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -51.767503-0.005033j
[2025-09-04 21:11:19] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -51.820473-0.001404j
[2025-09-04 21:11:29] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -51.759033-0.003723j
[2025-09-04 21:11:39] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -51.626689+0.000624j
[2025-09-04 21:11:49] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -51.699399-0.002509j
[2025-09-04 21:11:59] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -51.668102+0.001129j
[2025-09-04 21:12:09] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -51.750693+0.003861j
[2025-09-04 21:12:19] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -51.704009-0.002660j
[2025-09-04 21:12:29] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -51.719113+0.002176j
[2025-09-04 21:12:39] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -51.718113-0.001278j
[2025-09-04 21:12:49] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -51.617056+0.000053j
[2025-09-04 21:12:59] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -51.671644-0.000238j
[2025-09-04 21:13:10] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -51.758043+0.003220j
[2025-09-04 21:13:20] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -51.757473-0.003284j
[2025-09-04 21:13:30] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -51.716747+0.003476j
[2025-09-04 21:13:40] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -51.689884+0.000090j
[2025-09-04 21:13:50] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -51.640873-0.002912j
[2025-09-04 21:14:00] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -51.686114+0.001404j
[2025-09-04 21:14:10] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -51.799751-0.001111j
[2025-09-04 21:14:20] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -51.700887+0.001921j
[2025-09-04 21:14:30] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -51.849329+0.003156j
[2025-09-04 21:14:40] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -51.783652-0.000351j
[2025-09-04 21:14:50] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -51.798890-0.002226j
[2025-09-04 21:15:00] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -51.701454+0.004961j
[2025-09-04 21:15:11] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -51.802160-0.004141j
[2025-09-04 21:15:21] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -51.835815+0.000194j
[2025-09-04 21:15:31] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -51.946190-0.001637j
[2025-09-04 21:15:41] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -51.870036+0.006138j
[2025-09-04 21:15:51] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -51.798804+0.003568j
[2025-09-04 21:16:01] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -51.845287+0.003807j
[2025-09-04 21:16:11] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -51.856028+0.003187j
[2025-09-04 21:16:21] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -51.666556+0.004427j
[2025-09-04 21:16:31] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -51.684943+0.000916j
[2025-09-04 21:16:41] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -51.529430-0.000777j
[2025-09-04 21:16:51] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -51.624956-0.000819j
[2025-09-04 21:17:01] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -51.787906+0.003726j
[2025-09-04 21:17:12] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -51.595167+0.000930j
[2025-09-04 21:17:22] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -51.732809-0.001806j
[2025-09-04 21:17:32] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -51.649453-0.001197j
[2025-09-04 21:17:42] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -51.680605+0.001922j
[2025-09-04 21:17:52] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -51.772363+0.004083j
[2025-09-04 21:18:02] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -51.766210+0.001464j
[2025-09-04 21:18:12] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -51.823902-0.006295j
[2025-09-04 21:18:22] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -51.846580+0.001945j
[2025-09-04 21:18:32] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -51.788749+0.000204j
[2025-09-04 21:18:42] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -51.773885+0.002575j
[2025-09-04 21:18:53] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -51.755187+0.000292j
[2025-09-04 21:19:03] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -51.705688-0.001978j
[2025-09-04 21:19:13] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -51.770446-0.002526j
[2025-09-04 21:19:23] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -51.691001-0.001709j
[2025-09-04 21:19:33] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -51.739600-0.001747j
[2025-09-04 21:19:43] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -51.739410-0.000521j
[2025-09-04 21:19:53] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -51.700394-0.001520j
[2025-09-04 21:20:03] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -51.687870+0.002996j
[2025-09-04 21:20:13] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -51.650795-0.000580j
[2025-09-04 21:20:23] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -51.735051-0.003190j
[2025-09-04 21:20:33] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -51.727522-0.001825j
[2025-09-04 21:20:43] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -51.639516+0.002579j
[2025-09-04 21:20:54] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -51.601138+0.003108j
[2025-09-04 21:21:04] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -51.602982-0.001803j
[2025-09-04 21:21:14] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -51.708466-0.004488j
[2025-09-04 21:21:24] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -51.682502-0.003666j
[2025-09-04 21:21:34] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -51.737217-0.005265j
[2025-09-04 21:21:44] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -51.717059+0.001577j
[2025-09-04 21:21:44] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-04 21:21:54] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -51.732436+0.000763j
[2025-09-04 21:22:04] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -51.696758+0.004749j
[2025-09-04 21:22:14] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -51.709069+0.002618j
[2025-09-04 21:22:24] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -51.713863-0.004793j
[2025-09-04 21:22:34] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -51.610625-0.002290j
[2025-09-04 21:22:45] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -51.571770-0.006008j
[2025-09-04 21:22:55] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -51.723400+0.000508j
[2025-09-04 21:23:05] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -51.619779+0.001244j
[2025-09-04 21:23:15] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -51.776391-0.003327j
[2025-09-04 21:23:25] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -51.865775+0.000788j
[2025-09-04 21:23:35] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -51.823692+0.002925j
[2025-09-04 21:23:45] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -51.743387-0.003291j
[2025-09-04 21:23:55] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -51.722672-0.004105j
[2025-09-04 21:24:05] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -51.745740-0.000910j
[2025-09-04 21:24:15] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -51.673363-0.000830j
[2025-09-04 21:24:25] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -51.665566-0.000506j
[2025-09-04 21:24:35] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -51.713812-0.003282j
[2025-09-04 21:24:45] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -51.709964-0.000076j
[2025-09-04 21:24:56] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -51.793646+0.001720j
[2025-09-04 21:25:06] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -51.858640-0.001226j
[2025-09-04 21:25:16] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -51.659832-0.003073j
[2025-09-04 21:25:26] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -51.687455+0.001606j
[2025-09-04 21:25:36] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -51.642900-0.003782j
[2025-09-04 21:25:46] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -51.683998-0.000920j
[2025-09-04 21:25:56] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -51.742720-0.001686j
[2025-09-04 21:26:06] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -51.755180+0.001943j
[2025-09-04 21:26:16] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -51.743881-0.002177j
[2025-09-04 21:26:26] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -51.802421+0.000687j
[2025-09-04 21:26:36] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -51.735297+0.002066j
[2025-09-04 21:26:46] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -51.868356-0.002312j
[2025-09-04 21:26:57] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -51.681764+0.001352j
[2025-09-04 21:27:07] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -51.714203+0.001356j
[2025-09-04 21:27:17] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -51.766207+0.001736j
[2025-09-04 21:27:27] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -51.781984+0.001816j
[2025-09-04 21:27:37] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -51.734153-0.000221j
[2025-09-04 21:27:47] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -51.684500+0.001186j
[2025-09-04 21:27:57] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -51.646660-0.003378j
[2025-09-04 21:28:07] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -51.566950+0.002741j
[2025-09-04 21:28:17] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -51.596691-0.002265j
[2025-09-04 21:28:27] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -51.752808+0.002095j
[2025-09-04 21:28:37] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -51.745742-0.001204j
[2025-09-04 21:28:47] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -51.659156-0.001478j
[2025-09-04 21:28:58] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -51.565435+0.000522j
[2025-09-04 21:29:08] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -51.596408-0.004337j
[2025-09-04 21:29:18] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -51.679400-0.002974j
[2025-09-04 21:29:28] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -51.594677+0.000243j
[2025-09-04 21:29:38] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -51.675754-0.001964j
[2025-09-04 21:29:48] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -51.775794-0.000527j
[2025-09-04 21:29:58] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -51.621604+0.000763j
[2025-09-04 21:30:08] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -51.684941-0.000365j
[2025-09-04 21:30:18] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -51.646334+0.002097j
[2025-09-04 21:30:28] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -51.729541+0.001067j
[2025-09-04 21:30:38] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -51.658099-0.000660j
[2025-09-04 21:30:48] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -51.686468-0.000839j
[2025-09-04 21:30:59] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -51.699979-0.002211j
[2025-09-04 21:31:09] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -51.727413-0.001538j
[2025-09-04 21:31:19] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -51.729575-0.002949j
[2025-09-04 21:31:29] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -51.589966+0.001216j
[2025-09-04 21:31:39] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -51.620665+0.003974j
[2025-09-04 21:31:49] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -51.832451-0.003653j
[2025-09-04 21:31:59] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -51.797842-0.001087j
[2025-09-04 21:32:09] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -51.880909+0.002429j
[2025-09-04 21:32:19] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -51.793269-0.002954j
[2025-09-04 21:32:30] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -51.807674-0.001904j
[2025-09-04 21:32:40] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -51.878551+0.001486j
[2025-09-04 21:32:50] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -51.832637-0.000665j
[2025-09-04 21:33:00] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -51.867936-0.006035j
[2025-09-04 21:33:10] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -51.690587+0.000285j
[2025-09-04 21:33:20] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -51.804336+0.001879j
[2025-09-04 21:33:30] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -51.823159+0.005061j
[2025-09-04 21:33:40] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -51.815925-0.000883j
[2025-09-04 21:33:50] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -51.811832-0.001434j
[2025-09-04 21:34:00] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -51.794401+0.002885j
[2025-09-04 21:34:10] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -51.800729-0.001476j
[2025-09-04 21:34:20] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -51.797574-0.000270j
[2025-09-04 21:34:30] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -51.616272-0.002430j
[2025-09-04 21:34:41] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -51.656440-0.001196j
[2025-09-04 21:34:51] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -51.803441+0.004511j
[2025-09-04 21:35:01] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -51.941750+0.005855j
[2025-09-04 21:35:11] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -51.918502+0.000401j
[2025-09-04 21:35:21] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -51.942735+0.005876j
[2025-09-04 21:35:31] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -51.887370+0.004904j
[2025-09-04 21:35:41] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -51.813279-0.003692j
[2025-09-04 21:35:51] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -51.891660-0.001749j
[2025-09-04 21:36:01] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -51.886782+0.001689j
[2025-09-04 21:36:11] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -51.825964+0.002840j
[2025-09-04 21:36:21] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -51.894530-0.002157j
[2025-09-04 21:36:32] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -51.744636-0.002527j
[2025-09-04 21:36:42] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -51.696575+0.000928j
[2025-09-04 21:36:52] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -51.626268+0.001127j
[2025-09-04 21:37:02] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -51.600665-0.000464j
[2025-09-04 21:37:12] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -51.699093+0.003960j
[2025-09-04 21:37:22] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -51.563786-0.000864j
[2025-09-04 21:37:32] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -51.576117+0.003419j
[2025-09-04 21:37:42] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -51.609175+0.002461j
[2025-09-04 21:37:52] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -51.623169-0.000445j
[2025-09-04 21:38:02] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -51.696976-0.000078j
[2025-09-04 21:38:12] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -51.834101-0.002212j
[2025-09-04 21:38:23] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -51.558743+0.004081j
[2025-09-04 21:38:33] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -51.567372+0.000741j
[2025-09-04 21:38:43] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -51.767613-0.001796j
[2025-09-04 21:38:53] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -51.713972-0.001995j
[2025-09-04 21:39:03] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -51.655427+0.000415j
[2025-09-04 21:39:13] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -51.653733+0.001975j
[2025-09-04 21:39:23] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -51.697441+0.000506j
[2025-09-04 21:39:23] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-04 21:39:33] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -51.743320-0.001727j
[2025-09-04 21:39:43] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -51.678138+0.006112j
[2025-09-04 21:39:53] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -51.811314-0.001702j
[2025-09-04 21:40:03] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -51.598427+0.001393j
[2025-09-04 21:40:13] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -51.705874-0.001055j
[2025-09-04 21:40:24] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -51.801926+0.005237j
[2025-09-04 21:40:34] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -51.759225+0.001212j
[2025-09-04 21:40:44] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -51.725371-0.005623j
[2025-09-04 21:40:54] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -51.832548-0.001781j
[2025-09-04 21:41:04] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -51.930112-0.002639j
[2025-09-04 21:41:14] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -51.753712-0.006012j
[2025-09-04 21:41:24] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -51.688752+0.001379j
[2025-09-04 21:41:34] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -51.851029+0.000120j
[2025-09-04 21:41:44] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -51.838090-0.004147j
[2025-09-04 21:41:54] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -51.880687-0.000250j
[2025-09-04 21:42:04] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -51.770562+0.007167j
[2025-09-04 21:42:14] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -51.762088+0.000176j
[2025-09-04 21:42:25] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -51.798710-0.000650j
[2025-09-04 21:42:35] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -51.810802+0.003712j
[2025-09-04 21:42:45] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -51.754572+0.003330j
[2025-09-04 21:42:55] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -51.811689-0.010371j
[2025-09-04 21:43:05] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -51.819052+0.001426j
[2025-09-04 21:43:15] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -51.776893-0.000066j
[2025-09-04 21:43:25] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -51.692757+0.003034j
[2025-09-04 21:43:34] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -51.699138-0.000481j
[2025-09-04 21:43:45] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -51.697615+0.004262j
[2025-09-04 21:43:55] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -51.750049+0.004224j
[2025-09-04 21:44:05] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -51.820062-0.003010j
[2025-09-04 21:44:15] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -51.835952+0.002593j
[2025-09-04 21:44:25] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -51.919245-0.004480j
[2025-09-04 21:44:35] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -51.914162+0.002505j
[2025-09-04 21:44:45] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -51.808085-0.004083j
[2025-09-04 21:44:55] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -51.679026+0.001645j
[2025-09-04 21:45:05] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -51.740597+0.001141j
[2025-09-04 21:45:15] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -51.783568+0.000234j
[2025-09-04 21:45:25] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -51.750957-0.002917j
[2025-09-04 21:45:36] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -51.809367+0.002016j
[2025-09-04 21:45:46] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -51.828581-0.001286j
[2025-09-04 21:45:56] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -51.698711+0.003999j
[2025-09-04 21:46:06] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -51.707514+0.002076j
[2025-09-04 21:46:16] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -51.841638-0.001573j
[2025-09-04 21:46:26] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -51.679252-0.000448j
[2025-09-04 21:46:36] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -51.656704+0.001486j
[2025-09-04 21:46:46] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -51.659113+0.002124j
[2025-09-04 21:46:56] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -51.658470+0.000922j
[2025-09-04 21:47:06] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -51.678560+0.000306j
[2025-09-04 21:47:16] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -51.689160+0.000493j
[2025-09-04 21:47:26] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -51.738847+0.000048j
[2025-09-04 21:47:37] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -51.793569-0.004651j
[2025-09-04 21:47:47] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -51.832340-0.000016j
[2025-09-04 21:47:57] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -51.851124-0.002861j
[2025-09-04 21:48:07] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -51.875596+0.000724j
[2025-09-04 21:48:17] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -51.824464-0.004808j
[2025-09-04 21:48:27] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -51.967144-0.001792j
[2025-09-04 21:48:37] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -51.782053-0.000068j
[2025-09-04 21:48:47] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -51.825547+0.001708j
[2025-09-04 21:48:57] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -51.759744+0.001499j
[2025-09-04 21:49:07] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -51.950301-0.000882j
[2025-09-04 21:49:17] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -51.926822+0.001532j
[2025-09-04 21:49:27] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -51.832371+0.000020j
[2025-09-04 21:49:38] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -51.889654+0.000145j
[2025-09-04 21:49:48] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -51.769300-0.002817j
[2025-09-04 21:49:58] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -51.811224+0.002694j
[2025-09-04 21:50:08] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -51.854756-0.001363j
[2025-09-04 21:50:18] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -51.864273+0.002391j
[2025-09-04 21:50:28] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -51.875259+0.002929j
[2025-09-04 21:50:38] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -51.856317-0.001216j
[2025-09-04 21:50:48] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -51.692147-0.005643j
[2025-09-04 21:50:58] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -51.795709-0.001938j
[2025-09-04 21:51:08] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -51.728846-0.000351j
[2025-09-04 21:51:18] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -51.803546-0.007492j
[2025-09-04 21:51:28] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -51.799016-0.005102j
[2025-09-04 21:51:39] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -51.807366-0.000659j
[2025-09-04 21:51:49] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -51.708886-0.001364j
[2025-09-04 21:51:59] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -51.763272-0.004674j
[2025-09-04 21:52:09] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -51.631970-0.004661j
[2025-09-04 21:52:19] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -51.664480+0.004052j
[2025-09-04 21:52:29] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -51.720804-0.004062j
[2025-09-04 21:52:39] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -51.668627+0.003633j
[2025-09-04 21:52:49] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -51.722384+0.002903j
[2025-09-04 21:52:59] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -51.777591+0.000977j
[2025-09-04 21:53:09] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -51.843520+0.000146j
[2025-09-04 21:53:19] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -51.862650+0.000519j
[2025-09-04 21:53:30] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -51.772985+0.000032j
[2025-09-04 21:53:40] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -51.791233+0.003390j
[2025-09-04 21:53:50] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -51.914989+0.001300j
[2025-09-04 21:54:00] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -51.759173-0.001253j
[2025-09-04 21:54:10] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -51.762376-0.001676j
[2025-09-04 21:54:20] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -51.730897+0.002312j
[2025-09-04 21:54:30] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -51.690229-0.000930j
[2025-09-04 21:54:40] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -51.649025-0.001109j
[2025-09-04 21:54:50] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -51.632354-0.001030j
[2025-09-04 21:55:00] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -51.732394+0.003618j
[2025-09-04 21:55:10] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -51.630446+0.004499j
[2025-09-04 21:55:20] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -51.643288+0.002510j
[2025-09-04 21:55:30] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -51.811451+0.000604j
[2025-09-04 21:55:41] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -51.933885-0.000793j
[2025-09-04 21:55:51] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -51.729325-0.006721j
[2025-09-04 21:56:01] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -51.709090+0.002345j
[2025-09-04 21:56:11] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -51.716088+0.002143j
[2025-09-04 21:56:21] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -51.613679-0.003415j
[2025-09-04 21:56:31] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -51.670990-0.004162j
[2025-09-04 21:56:41] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -51.625573+0.000245j
[2025-09-04 21:56:51] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -51.613136-0.002299j
[2025-09-04 21:57:01] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -51.644403+0.000163j
[2025-09-04 21:57:01] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-04 21:57:01] ✅ Training completed | Restarts: 2
[2025-09-04 21:57:01] ============================================================
[2025-09-04 21:57:01] Training completed | Runtime: 10642.6s
[2025-09-04 21:57:06] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-04 21:57:06] ============================================================
