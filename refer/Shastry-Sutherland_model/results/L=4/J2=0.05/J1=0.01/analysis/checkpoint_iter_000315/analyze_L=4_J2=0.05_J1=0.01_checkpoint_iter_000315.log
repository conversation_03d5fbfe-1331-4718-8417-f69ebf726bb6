[2025-09-05 18:34:39] 使用checkpoint文件: results/L=4/J2=0.05/J1=0.01/training/checkpoints/checkpoint_iter_000315.pkl
[2025-09-05 18:34:56] ✓ 从checkpoint加载参数: 315
[2025-09-05 18:34:56]   - 能量: -50.502986-0.002328j ± 0.082530
[2025-09-05 18:34:56] ================================================================================
[2025-09-05 18:34:56] 加载量子态: L=4, J2=0.05, J1=0.01, checkpoint=checkpoint_iter_000315
[2025-09-05 18:34:56] 使用采样数目: 1048576
[2025-09-05 18:34:56] 设置样本数为: 1048576
[2025-09-05 18:34:56] 开始生成共享样本集...
[2025-09-05 18:39:22] 样本生成完成,耗时: 265.529 秒
[2025-09-05 18:39:22] ================================================================================
[2025-09-05 18:39:22] 开始计算自旋结构因子...
[2025-09-05 18:39:22] 初始化操作符缓存...
[2025-09-05 18:39:22] 预构建所有自旋相关操作符...
[2025-09-05 18:39:22] 开始计算自旋相关函数...
[2025-09-05 18:39:36] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 14.014s
[2025-09-05 18:39:54] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 18.519s
[2025-09-05 18:40:08] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 14.057s
[2025-09-05 18:40:22] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 14.057s
[2025-09-05 18:40:36] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 14.060s
[2025-09-05 18:40:50] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 14.062s
[2025-09-05 18:41:04] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 14.008s
[2025-09-05 18:41:19] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 14.068s
[2025-09-05 18:41:33] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 14.051s
[2025-09-05 18:41:47] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 14.061s
[2025-09-05 18:42:01] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 14.060s
[2025-09-05 18:42:15] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 14.057s
[2025-09-05 18:42:29] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 14.053s
[2025-09-05 18:42:43] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 14.065s
[2025-09-05 18:42:57] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 14.047s
[2025-09-05 18:43:11] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 14.055s
[2025-09-05 18:43:25] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 14.071s
[2025-09-05 18:43:39] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 14.047s
[2025-09-05 18:43:53] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 14.051s
[2025-09-05 18:44:07] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 14.011s
[2025-09-05 18:44:21] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 14.064s
[2025-09-05 18:44:35] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 14.064s
[2025-09-05 18:44:49] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 14.060s
[2025-09-05 18:45:03] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 14.062s
[2025-09-05 18:45:18] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 14.049s
[2025-09-05 18:45:32] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 14.059s
[2025-09-05 18:45:46] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 14.040s
[2025-09-05 18:46:00] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 14.071s
[2025-09-05 18:46:14] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 14.045s
[2025-09-05 18:46:28] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 14.051s
[2025-09-05 18:46:42] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 14.052s
[2025-09-05 18:46:56] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 14.067s
[2025-09-05 18:47:10] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 14.053s
[2025-09-05 18:47:24] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 14.067s
[2025-09-05 18:47:38] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 14.061s
[2025-09-05 18:47:52] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 14.056s
[2025-09-05 18:48:06] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 14.058s
[2025-09-05 18:48:20] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 14.049s
[2025-09-05 18:48:34] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 14.042s
[2025-09-05 18:48:48] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 14.044s
[2025-09-05 18:49:02] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 14.049s
[2025-09-05 18:49:17] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 14.037s
[2025-09-05 18:49:31] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 14.055s
[2025-09-05 18:49:45] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 14.065s
[2025-09-05 18:49:59] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 14.051s
[2025-09-05 18:50:13] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 14.045s
[2025-09-05 18:50:27] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 14.075s
[2025-09-05 18:50:41] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 14.054s
[2025-09-05 18:50:55] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 14.052s
[2025-09-05 18:51:09] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 14.060s
[2025-09-05 18:51:23] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 14.055s
[2025-09-05 18:51:37] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 14.057s
[2025-09-05 18:51:51] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 14.054s
[2025-09-05 18:52:05] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 14.066s
[2025-09-05 18:52:19] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 14.057s
[2025-09-05 18:52:33] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 14.067s
[2025-09-05 18:52:47] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 14.051s
[2025-09-05 18:53:01] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 14.059s
[2025-09-05 18:53:16] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 14.048s
[2025-09-05 18:53:30] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 14.069s
[2025-09-05 18:53:44] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 14.057s
[2025-09-05 18:53:58] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 14.052s
[2025-09-05 18:54:12] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 14.055s
[2025-09-05 18:54:26] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 14.045s
[2025-09-05 18:54:26] 自旋相关函数计算完成,总耗时 904.37 秒
[2025-09-05 18:54:28] 计算傅里叶变换...
[2025-09-05 18:54:31] 自旋结构因子计算完成
[2025-09-05 18:54:32] 自旋相关函数平均误差: 0.000682
