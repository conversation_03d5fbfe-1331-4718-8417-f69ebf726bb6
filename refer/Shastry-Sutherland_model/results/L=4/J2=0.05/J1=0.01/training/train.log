[2025-09-05 03:56:25] ✓ 从checkpoint恢复: results/L=4/J2=0.05/J1=0.02/training/checkpoints/final_GCNN.pkl
[2025-09-05 03:56:25]   - 迭代次数: final
[2025-09-05 03:56:25]   - 能量: -50.961333-0.001711j ± 0.084809
[2025-09-05 03:56:25]   - 时间戳: 2025-09-05T03:56:15.085147+08:00
[2025-09-05 03:56:36] ✓ 变分状态参数已从checkpoint恢复
[2025-09-05 03:56:36] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-05 03:56:36] ==================================================
[2025-09-05 03:56:36] GCNN for Shastry-Sutherland Model
[2025-09-05 03:56:36] ==================================================
[2025-09-05 03:56:36] System parameters:
[2025-09-05 03:56:36]   - System size: L=4, N=64
[2025-09-05 03:56:36]   - System parameters: J1=0.01, J2=0.05, Q=0.95
[2025-09-05 03:56:36] --------------------------------------------------
[2025-09-05 03:56:36] Model parameters:
[2025-09-05 03:56:36]   - Number of layers = 4
[2025-09-05 03:56:36]   - Number of features = 4
[2025-09-05 03:56:36]   - Total parameters = 12572
[2025-09-05 03:56:36] --------------------------------------------------
[2025-09-05 03:56:36] Training parameters:
[2025-09-05 03:56:36]   - Learning rate: 0.015
[2025-09-05 03:56:36]   - Total iterations: 1050
[2025-09-05 03:56:36]   - Annealing cycles: 3
[2025-09-05 03:56:36]   - Initial period: 150
[2025-09-05 03:56:36]   - Period multiplier: 2.0
[2025-09-05 03:56:36]   - Temperature range: 0.0-1.0
[2025-09-05 03:56:36]   - Samples: 4096
[2025-09-05 03:56:36]   - Discarded samples: 0
[2025-09-05 03:56:36]   - Chunk size: 2048
[2025-09-05 03:56:36]   - Diagonal shift: 0.2
[2025-09-05 03:56:36]   - Gradient clipping: 1.0
[2025-09-05 03:56:36]   - Checkpoint enabled: interval=105
[2025-09-05 03:56:36]   - Checkpoint directory: results/L=4/J2=0.05/J1=0.01/training/checkpoints
[2025-09-05 03:56:36] --------------------------------------------------
[2025-09-05 03:56:36] Device status:
[2025-09-05 03:56:36]   - Devices model: NVIDIA H200 NVL
[2025-09-05 03:56:36]   - Number of devices: 1
[2025-09-05 03:56:36]   - Sharding: True
[2025-09-05 03:56:36] ============================================================
[2025-09-05 03:57:17] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -50.611766-0.005087j
[2025-09-05 03:57:44] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -50.869790-0.000504j
[2025-09-05 03:57:54] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -50.730753-0.000353j
[2025-09-05 03:58:05] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -50.542887+0.002919j
[2025-09-05 03:58:15] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -50.646634-0.003693j
[2025-09-05 03:58:25] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -50.527082-0.001236j
[2025-09-05 03:58:35] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -50.422313+0.002169j
[2025-09-05 03:58:45] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -50.483965-0.001127j
[2025-09-05 03:58:55] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -50.430386-0.000270j
[2025-09-05 03:59:05] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -50.618441+0.001675j
[2025-09-05 03:59:15] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -50.628557-0.003781j
[2025-09-05 03:59:25] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -50.591400-0.000192j
[2025-09-05 03:59:35] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -50.496574+0.000928j
[2025-09-05 03:59:45] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -50.464557+0.000265j
[2025-09-05 03:59:55] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -50.668190+0.001118j
[2025-09-05 04:00:05] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -50.534754-0.002365j
[2025-09-05 04:00:15] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -50.680751-0.002712j
[2025-09-05 04:00:26] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -50.752615-0.006014j
[2025-09-05 04:00:36] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -50.733288-0.000792j
[2025-09-05 04:00:46] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -50.668573-0.000063j
[2025-09-05 04:00:56] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -50.667149-0.002537j
[2025-09-05 04:01:06] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -50.672541-0.000048j
[2025-09-05 04:01:16] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -50.694374-0.001384j
[2025-09-05 04:01:26] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -50.582466-0.000196j
[2025-09-05 04:01:36] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -50.583679+0.002279j
[2025-09-05 04:01:46] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -50.445977-0.001658j
[2025-09-05 04:01:56] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -50.479330-0.000050j
[2025-09-05 04:02:06] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -50.515255+0.001975j
[2025-09-05 04:02:16] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -50.561909+0.001995j
[2025-09-05 04:02:26] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -50.628104+0.003008j
[2025-09-05 04:02:36] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -50.667716-0.001973j
[2025-09-05 04:02:47] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -50.620010+0.001904j
[2025-09-05 04:02:57] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -50.622902-0.002823j
[2025-09-05 04:03:07] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -50.508980-0.001761j
[2025-09-05 04:03:17] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -50.507858-0.000063j
[2025-09-05 04:03:27] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -50.477637-0.000627j
[2025-09-05 04:03:37] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -50.471070+0.002597j
[2025-09-05 04:03:47] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -50.619549+0.000121j
[2025-09-05 04:03:57] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -50.457738-0.004499j
[2025-09-05 04:04:07] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -50.547972-0.001031j
[2025-09-05 04:04:17] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -50.484630-0.001686j
[2025-09-05 04:04:27] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -50.466002+0.001825j
[2025-09-05 04:04:37] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -50.498288+0.000266j
[2025-09-05 04:04:47] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -50.471121-0.001194j
[2025-09-05 04:04:58] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -50.422500-0.001101j
[2025-09-05 04:05:08] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -50.486126+0.000778j
[2025-09-05 04:05:18] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -50.382221-0.000674j
[2025-09-05 04:05:28] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -50.507489+0.001792j
[2025-09-05 04:05:38] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -50.579234-0.000799j
[2025-09-05 04:05:48] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -50.543789+0.003263j
[2025-09-05 04:05:58] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -50.356191-0.001950j
[2025-09-05 04:06:08] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -50.503702+0.001366j
[2025-09-05 04:06:18] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -50.456003+0.005260j
[2025-09-05 04:06:28] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -50.535551+0.002326j
[2025-09-05 04:06:38] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -50.489256+0.001355j
[2025-09-05 04:06:48] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -50.635372+0.000313j
[2025-09-05 04:06:59] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -50.529938-0.001100j
[2025-09-05 04:07:09] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -50.472473-0.000167j
[2025-09-05 04:07:19] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -50.584714+0.003925j
[2025-09-05 04:07:29] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -50.506400-0.002107j
[2025-09-05 04:07:39] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -50.572867+0.002753j
[2025-09-05 04:07:49] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -50.471326+0.000146j
[2025-09-05 04:07:59] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -50.588632+0.000205j
[2025-09-05 04:08:09] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -50.641387+0.001901j
[2025-09-05 04:08:19] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -50.570219-0.000085j
[2025-09-05 04:08:29] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -50.519938+0.001396j
[2025-09-05 04:08:39] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -50.641269-0.001198j
[2025-09-05 04:08:49] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -50.745317+0.002179j
[2025-09-05 04:09:00] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -50.791592+0.001207j
[2025-09-05 04:09:10] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -50.845920-0.003915j
[2025-09-05 04:09:20] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -50.655077+0.000882j
[2025-09-05 04:09:30] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -50.694594+0.003529j
[2025-09-05 04:09:40] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -50.602539+0.001940j
[2025-09-05 04:09:50] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -50.627816-0.001377j
[2025-09-05 04:10:00] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -50.638069+0.001212j
[2025-09-05 04:10:10] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -50.661410-0.000756j
[2025-09-05 04:10:20] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -50.576500-0.001600j
[2025-09-05 04:10:30] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -50.616637-0.001290j
[2025-09-05 04:10:40] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -50.536231+0.001886j
[2025-09-05 04:10:50] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -50.433710+0.002027j
[2025-09-05 04:11:00] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -50.572717-0.005127j
[2025-09-05 04:11:10] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -50.574241+0.004309j
[2025-09-05 04:11:21] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -50.586209+0.001904j
[2025-09-05 04:11:31] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -50.603727+0.001441j
[2025-09-05 04:11:41] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -50.629811-0.000655j
[2025-09-05 04:11:51] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -50.630200+0.001071j
[2025-09-05 04:12:01] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -50.593793-0.001543j
[2025-09-05 04:12:11] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -50.505736+0.002985j
[2025-09-05 04:12:21] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -50.507418-0.005501j
[2025-09-05 04:12:31] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -50.569927+0.001638j
[2025-09-05 04:12:41] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -50.590938+0.002272j
[2025-09-05 04:12:51] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -50.607009-0.003974j
[2025-09-05 04:13:01] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -50.626170+0.002583j
[2025-09-05 04:13:11] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -50.623790+0.000891j
[2025-09-05 04:13:21] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -50.465459-0.000820j
[2025-09-05 04:13:32] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -50.535739-0.000559j
[2025-09-05 04:13:42] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -50.597898-0.000668j
[2025-09-05 04:13:52] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -50.485709+0.001980j
[2025-09-05 04:14:02] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -50.483886-0.003412j
[2025-09-05 04:14:12] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -50.509711+0.001278j
[2025-09-05 04:14:22] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -50.514667+0.000372j
[2025-09-05 04:14:32] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -50.445578+0.000013j
[2025-09-05 04:14:42] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -50.459261-0.001699j
[2025-09-05 04:14:52] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -50.528805+0.000029j
[2025-09-05 04:15:02] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -50.572752+0.003962j
[2025-09-05 04:15:02] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-05 04:15:12] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -50.427819-0.003786j
[2025-09-05 04:15:22] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -50.513816+0.004358j
[2025-09-05 04:15:33] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -50.565964-0.001715j
[2025-09-05 04:15:43] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -50.640965+0.004824j
[2025-09-05 04:15:53] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -50.561145-0.001602j
[2025-09-05 04:16:03] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -50.655947-0.001064j
[2025-09-05 04:16:13] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -50.526418+0.000814j
[2025-09-05 04:16:23] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -50.461770-0.002634j
[2025-09-05 04:16:33] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -50.608506+0.001585j
[2025-09-05 04:16:43] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -50.599449-0.003654j
[2025-09-05 04:16:53] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -50.617944+0.001767j
[2025-09-05 04:17:03] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -50.674274+0.001424j
[2025-09-05 04:17:13] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -50.678279+0.003160j
[2025-09-05 04:17:23] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -50.627887+0.001074j
[2025-09-05 04:17:34] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -50.562392-0.000110j
[2025-09-05 04:17:44] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -50.675850+0.001830j
[2025-09-05 04:17:54] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -50.540351+0.001165j
[2025-09-05 04:18:04] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -50.468991-0.000177j
[2025-09-05 04:18:14] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -50.413871+0.001060j
[2025-09-05 04:18:24] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -50.375527+0.000993j
[2025-09-05 04:18:34] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -50.392864-0.000814j
[2025-09-05 04:18:44] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -50.502941-0.004668j
[2025-09-05 04:18:54] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -50.513122-0.001105j
[2025-09-05 04:19:04] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -50.405396+0.000772j
[2025-09-05 04:19:14] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -50.477953+0.001331j
[2025-09-05 04:19:24] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -50.528898-0.000392j
[2025-09-05 04:19:34] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -50.627849+0.000326j
[2025-09-05 04:19:45] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -50.630847-0.003905j
[2025-09-05 04:19:55] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -50.431170+0.001265j
[2025-09-05 04:20:05] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -50.506138-0.002914j
[2025-09-05 04:20:15] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -50.624887+0.001744j
[2025-09-05 04:20:25] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -50.484075+0.006435j
[2025-09-05 04:20:35] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -50.654164+0.003561j
[2025-09-05 04:20:45] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -50.738715+0.001794j
[2025-09-05 04:20:55] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -50.653902-0.000293j
[2025-09-05 04:21:05] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -50.631100-0.000299j
[2025-09-05 04:21:15] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -50.726987-0.003516j
[2025-09-05 04:21:25] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -50.632391-0.001231j
[2025-09-05 04:21:35] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -50.633605-0.002338j
[2025-09-05 04:21:46] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -50.577332+0.000142j
[2025-09-05 04:21:56] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -50.582259+0.000207j
[2025-09-05 04:22:06] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -50.530587-0.004933j
[2025-09-05 04:22:16] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -50.574821-0.002454j
[2025-09-05 04:22:26] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -50.692313+0.002542j
[2025-09-05 04:22:36] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -50.539246+0.000975j
[2025-09-05 04:22:36] RESTART #1 | Period: 300
[2025-09-05 04:22:46] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -50.548670+0.002319j
[2025-09-05 04:22:56] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -50.715664+0.002045j
[2025-09-05 04:23:06] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -50.613166+0.001928j
[2025-09-05 04:23:16] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -50.517275+0.003652j
[2025-09-05 04:23:26] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -50.506373-0.000904j
[2025-09-05 04:23:36] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -50.446697+0.001928j
[2025-09-05 04:23:47] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -50.467541+0.002864j
[2025-09-05 04:23:57] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -50.514963-0.000150j
[2025-09-05 04:24:07] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -50.493684+0.003452j
[2025-09-05 04:24:17] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -50.513650-0.003701j
[2025-09-05 04:24:27] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -50.533560-0.001250j
[2025-09-05 04:24:37] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -50.482869+0.001020j
[2025-09-05 04:24:47] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -50.505023+0.001754j
[2025-09-05 04:24:57] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -50.479608-0.000578j
[2025-09-05 04:25:07] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -50.546174-0.003267j
[2025-09-05 04:25:17] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -50.432212+0.004912j
[2025-09-05 04:25:27] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -50.468703+0.001414j
[2025-09-05 04:25:38] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -50.515184-0.003131j
[2025-09-05 04:25:48] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -50.577399+0.000209j
[2025-09-05 04:25:58] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -50.593296+0.001659j
[2025-09-05 04:26:08] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -50.524027-0.000764j
[2025-09-05 04:26:18] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -50.567397+0.000118j
[2025-09-05 04:26:28] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -50.476281+0.001658j
[2025-09-05 04:26:38] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -50.441036-0.005233j
[2025-09-05 04:26:48] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -50.570912-0.003615j
[2025-09-05 04:26:58] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -50.549887+0.000581j
[2025-09-05 04:27:08] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -50.623102+0.004327j
[2025-09-05 04:27:18] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -50.614276+0.000168j
[2025-09-05 04:27:28] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -50.661238-0.000140j
[2025-09-05 04:27:38] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -50.594257+0.001797j
[2025-09-05 04:27:49] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -50.618392-0.003026j
[2025-09-05 04:27:59] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -50.668319+0.003453j
[2025-09-05 04:28:09] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -50.646370+0.000930j
[2025-09-05 04:28:19] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -50.601759+0.003393j
[2025-09-05 04:28:29] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -50.589294-0.000253j
[2025-09-05 04:28:39] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -50.638215+0.002019j
[2025-09-05 04:28:49] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -50.500895-0.000624j
[2025-09-05 04:28:59] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -50.522948-0.003220j
[2025-09-05 04:29:09] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -50.536636-0.002932j
[2025-09-05 04:29:19] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -50.510259+0.000634j
[2025-09-05 04:29:29] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -50.470445+0.001485j
[2025-09-05 04:29:39] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -50.502498+0.000244j
[2025-09-05 04:29:50] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -50.554964-0.002834j
[2025-09-05 04:30:00] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -50.510492+0.000076j
[2025-09-05 04:30:10] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -50.527319+0.001647j
[2025-09-05 04:30:20] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -50.591368-0.001869j
[2025-09-05 04:30:30] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -50.586919-0.003323j
[2025-09-05 04:30:40] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -50.590100-0.001322j
[2025-09-05 04:30:50] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -50.475347-0.001612j
[2025-09-05 04:31:00] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -50.519550+0.002548j
[2025-09-05 04:31:10] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -50.561523+0.002866j
[2025-09-05 04:31:20] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -50.564040-0.000871j
[2025-09-05 04:31:30] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -50.548164-0.000136j
[2025-09-05 04:31:41] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -50.487946+0.000485j
[2025-09-05 04:31:51] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -50.601277-0.001537j
[2025-09-05 04:32:01] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -50.616112-0.003159j
[2025-09-05 04:32:11] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -50.570146+0.001545j
[2025-09-05 04:32:21] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -50.694742+0.001410j
[2025-09-05 04:32:31] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -50.535285-0.001955j
[2025-09-05 04:32:41] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -50.581072+0.000047j
[2025-09-05 04:32:41] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-05 04:32:51] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -50.620846-0.001898j
[2025-09-05 04:33:01] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -50.537848+0.003712j
[2025-09-05 04:33:11] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -50.720943-0.002446j
[2025-09-05 04:33:21] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -50.660312-0.001100j
[2025-09-05 04:33:31] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -50.580835+0.000301j
[2025-09-05 04:33:42] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -50.601655-0.003460j
[2025-09-05 04:33:52] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -50.624454+0.005559j
[2025-09-05 04:34:02] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -50.578858-0.002991j
[2025-09-05 04:34:12] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -50.554118+0.003117j
[2025-09-05 04:34:22] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -50.586307-0.001871j
[2025-09-05 04:34:32] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -50.618609+0.004551j
[2025-09-05 04:34:42] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -50.702579+0.000020j
[2025-09-05 04:34:52] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -50.719456-0.001209j
[2025-09-05 04:35:02] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -50.608066-0.000397j
[2025-09-05 04:35:12] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -50.531774+0.000586j
[2025-09-05 04:35:22] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -50.531738-0.000700j
[2025-09-05 04:35:32] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -50.567664-0.000004j
[2025-09-05 04:35:43] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -50.632823-0.000388j
[2025-09-05 04:35:53] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -50.566914+0.002717j
[2025-09-05 04:36:03] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -50.469345+0.002091j
[2025-09-05 04:36:13] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -50.436176-0.001193j
[2025-09-05 04:36:23] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -50.346681-0.000104j
[2025-09-05 04:36:33] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -50.333422-0.000950j
[2025-09-05 04:36:43] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -50.411236-0.000073j
[2025-09-05 04:36:53] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -50.425450+0.000591j
[2025-09-05 04:37:03] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -50.491439-0.002360j
[2025-09-05 04:37:13] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -50.546940+0.003017j
[2025-09-05 04:37:23] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -50.620418-0.000420j
[2025-09-05 04:37:33] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -50.540991+0.000437j
[2025-09-05 04:37:44] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -50.584507+0.002846j
[2025-09-05 04:37:54] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -50.543041+0.001080j
[2025-09-05 04:38:04] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -50.534236-0.001587j
[2025-09-05 04:38:14] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -50.577290+0.000315j
[2025-09-05 04:38:24] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -50.469324-0.000816j
[2025-09-05 04:38:34] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -50.505282-0.001471j
[2025-09-05 04:38:44] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -50.483725+0.004023j
[2025-09-05 04:38:54] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -50.431323+0.001131j
[2025-09-05 04:39:04] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -50.478326+0.001153j
[2025-09-05 04:39:14] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -50.498664+0.001146j
[2025-09-05 04:39:24] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -50.644138-0.001140j
[2025-09-05 04:39:35] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -50.555278+0.004256j
[2025-09-05 04:39:45] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -50.537777+0.000711j
[2025-09-05 04:39:55] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -50.532497+0.001490j
[2025-09-05 04:40:05] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -50.522939+0.001747j
[2025-09-05 04:40:15] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -50.527457-0.000158j
[2025-09-05 04:40:25] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -50.649405+0.001004j
[2025-09-05 04:40:35] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -50.572222+0.000766j
[2025-09-05 04:40:45] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -50.534263-0.003951j
[2025-09-05 04:40:55] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -50.584179+0.004881j
[2025-09-05 04:41:05] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -50.590090+0.002529j
[2025-09-05 04:41:15] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -50.593225-0.002533j
[2025-09-05 04:41:26] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -50.677495+0.002460j
[2025-09-05 04:41:36] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -50.653637+0.000685j
[2025-09-05 04:41:46] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -50.703118+0.001712j
[2025-09-05 04:41:56] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -50.706413-0.000903j
[2025-09-05 04:42:06] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -50.587977+0.001265j
[2025-09-05 04:42:16] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -50.581219+0.002036j
[2025-09-05 04:42:26] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -50.628617-0.002455j
[2025-09-05 04:42:36] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -50.574735+0.002474j
[2025-09-05 04:42:46] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -50.660784+0.001745j
[2025-09-05 04:42:56] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -50.608820+0.001539j
[2025-09-05 04:43:06] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -50.566954-0.001699j
[2025-09-05 04:43:16] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -50.520713-0.002969j
[2025-09-05 04:43:27] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -50.319595-0.001928j
[2025-09-05 04:43:37] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -50.464990-0.001006j
[2025-09-05 04:43:47] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -50.491668-0.002981j
[2025-09-05 04:43:57] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -50.531519+0.000747j
[2025-09-05 04:44:07] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -50.583740+0.000670j
[2025-09-05 04:44:17] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -50.551201-0.000699j
[2025-09-05 04:44:27] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -50.498193-0.001756j
[2025-09-05 04:44:37] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -50.498490+0.003327j
[2025-09-05 04:44:47] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -50.420954-0.004288j
[2025-09-05 04:44:57] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -50.545531+0.001411j
[2025-09-05 04:45:07] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -50.590431+0.003681j
[2025-09-05 04:45:17] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -50.511555+0.001705j
[2025-09-05 04:45:28] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -50.613997+0.000464j
[2025-09-05 04:45:38] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -50.671927+0.002398j
[2025-09-05 04:45:48] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -50.606928+0.003237j
[2025-09-05 04:45:58] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -50.629513-0.001599j
[2025-09-05 04:46:08] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -50.609099-0.001249j
[2025-09-05 04:46:18] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -50.624358+0.000067j
[2025-09-05 04:46:28] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -50.601014-0.002856j
[2025-09-05 04:46:38] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -50.576332-0.001185j
[2025-09-05 04:46:48] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -50.583156-0.000509j
[2025-09-05 04:46:58] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -50.519492+0.002133j
[2025-09-05 04:47:08] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -50.586665+0.002193j
[2025-09-05 04:47:18] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -50.602146-0.000658j
[2025-09-05 04:47:29] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -50.631234-0.001094j
[2025-09-05 04:47:39] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -50.613142-0.001444j
[2025-09-05 04:47:49] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -50.689455+0.001613j
[2025-09-05 04:47:59] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -50.674609+0.001269j
[2025-09-05 04:48:09] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -50.551841-0.003212j
[2025-09-05 04:48:19] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -50.550831+0.001072j
[2025-09-05 04:48:29] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -50.568055+0.002436j
[2025-09-05 04:48:39] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -50.380144-0.000909j
[2025-09-05 04:48:49] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -50.486919-0.004115j
[2025-09-05 04:48:59] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -50.505753+0.003108j
[2025-09-05 04:49:09] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -50.479028-0.001041j
[2025-09-05 04:49:20] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -50.354690+0.000129j
[2025-09-05 04:49:30] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -50.392371+0.002721j
[2025-09-05 04:49:40] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -50.399444+0.000199j
[2025-09-05 04:49:50] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -50.298684+0.001584j
[2025-09-05 04:50:00] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -50.401424+0.000067j
[2025-09-05 04:50:10] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -50.463232-0.000691j
[2025-09-05 04:50:20] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -50.502986-0.002328j
[2025-09-05 04:50:20] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-05 04:50:30] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -50.379071-0.001457j
[2025-09-05 04:50:40] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -50.344197+0.002487j
[2025-09-05 04:50:50] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -50.400230-0.001578j
[2025-09-05 04:51:00] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -50.497197-0.000340j
[2025-09-05 04:51:10] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -50.610669-0.001185j
[2025-09-05 04:51:21] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -50.489975-0.000352j
[2025-09-05 04:51:31] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -50.522752-0.000034j
[2025-09-05 04:51:41] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -50.492989+0.005631j
[2025-09-05 04:51:51] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -50.532981+0.002387j
[2025-09-05 04:52:01] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -50.584768-0.002653j
[2025-09-05 04:52:11] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -50.525913-0.001090j
[2025-09-05 04:52:21] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -50.536822-0.000089j
[2025-09-05 04:52:31] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -50.520093-0.000065j
[2025-09-05 04:52:41] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -50.436371-0.000354j
[2025-09-05 04:52:51] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -50.489607+0.000807j
[2025-09-05 04:53:01] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -50.512142+0.002624j
[2025-09-05 04:53:11] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -50.479795+0.002728j
[2025-09-05 04:53:22] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -50.507423-0.000167j
[2025-09-05 04:53:32] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -50.676329+0.001122j
[2025-09-05 04:53:42] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -50.704244+0.000303j
[2025-09-05 04:53:52] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -50.714856+0.003382j
[2025-09-05 04:54:02] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -50.808871-0.000483j
[2025-09-05 04:54:12] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -50.738955-0.001445j
[2025-09-05 04:54:22] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -50.753488+0.001388j
[2025-09-05 04:54:32] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -50.642090+0.001303j
[2025-09-05 04:54:42] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -50.658037+0.002108j
[2025-09-05 04:54:52] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -50.560924+0.005865j
[2025-09-05 04:55:02] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -50.594189+0.000231j
[2025-09-05 04:55:13] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -50.535405+0.000860j
[2025-09-05 04:55:23] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -50.516125-0.001366j
[2025-09-05 04:55:33] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -50.547395+0.000031j
[2025-09-05 04:55:43] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -50.624324+0.002116j
[2025-09-05 04:55:53] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -50.532013-0.003487j
[2025-09-05 04:56:03] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -50.685344+0.001594j
[2025-09-05 04:56:13] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -50.733054+0.003486j
[2025-09-05 04:56:23] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -50.556201-0.001268j
[2025-09-05 04:56:33] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -50.574052+0.004778j
[2025-09-05 04:56:43] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -50.473493-0.003843j
[2025-09-05 04:56:53] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -50.477621+0.000403j
[2025-09-05 04:57:04] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -50.517366-0.004515j
[2025-09-05 04:57:14] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -50.609400-0.000054j
[2025-09-05 04:57:24] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -50.598130+0.002885j
[2025-09-05 04:57:34] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -50.564313-0.000643j
[2025-09-05 04:57:44] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -50.470691+0.003570j
[2025-09-05 04:57:54] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -50.464164+0.002431j
[2025-09-05 04:58:04] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -50.513975-0.002037j
[2025-09-05 04:58:14] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -50.541087+0.000691j
[2025-09-05 04:58:24] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -50.448808-0.000628j
[2025-09-05 04:58:34] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -50.579620-0.004896j
[2025-09-05 04:58:44] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -50.418085+0.000944j
[2025-09-05 04:58:54] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -50.476401-0.000383j
[2025-09-05 04:59:04] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -50.545201-0.001799j
[2025-09-05 04:59:15] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -50.505798-0.000941j
[2025-09-05 04:59:25] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -50.613963-0.001723j
[2025-09-05 04:59:35] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -50.537423+0.001352j
[2025-09-05 04:59:45] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -50.492323-0.001997j
[2025-09-05 04:59:55] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -50.673041+0.001751j
[2025-09-05 05:00:05] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -50.610959+0.001560j
[2025-09-05 05:00:15] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -50.513002-0.002605j
[2025-09-05 05:00:25] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -50.523007+0.002776j
[2025-09-05 05:00:35] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -50.415791+0.001174j
[2025-09-05 05:00:45] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -50.426321+0.000525j
[2025-09-05 05:00:55] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -50.514813+0.002215j
[2025-09-05 05:01:06] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -50.513065+0.000054j
[2025-09-05 05:01:16] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -50.420406-0.001844j
[2025-09-05 05:01:26] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -50.443461+0.003253j
[2025-09-05 05:01:36] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -50.416064+0.001974j
[2025-09-05 05:01:46] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -50.526343+0.001824j
[2025-09-05 05:01:56] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -50.420015+0.002139j
[2025-09-05 05:02:06] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -50.502367-0.002784j
[2025-09-05 05:02:16] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -50.528096+0.001467j
[2025-09-05 05:02:26] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -50.714069+0.004726j
[2025-09-05 05:02:36] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -50.652550-0.003026j
[2025-09-05 05:02:46] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -50.629814-0.001922j
[2025-09-05 05:02:56] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -50.601438+0.004142j
[2025-09-05 05:03:06] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -50.621195+0.004950j
[2025-09-05 05:03:17] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -50.647367-0.002696j
[2025-09-05 05:03:27] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -50.652678-0.003227j
[2025-09-05 05:03:37] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -50.605752+0.001671j
[2025-09-05 05:03:47] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -50.520856-0.001590j
[2025-09-05 05:03:57] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -50.530530-0.003405j
[2025-09-05 05:04:07] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -50.625636+0.002062j
[2025-09-05 05:04:17] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -50.596569-0.000363j
[2025-09-05 05:04:27] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -50.559579-0.002903j
[2025-09-05 05:04:37] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -50.612494-0.000008j
[2025-09-05 05:04:47] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -50.563877+0.000965j
[2025-09-05 05:04:57] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -50.621375-0.001025j
[2025-09-05 05:05:08] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -50.481598+0.002677j
[2025-09-05 05:05:18] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -50.527876+0.000096j
[2025-09-05 05:05:28] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -50.562182-0.001244j
[2025-09-05 05:05:38] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -50.590330+0.002564j
[2025-09-05 05:05:48] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -50.606642+0.001582j
[2025-09-05 05:05:58] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -50.704270+0.002608j
[2025-09-05 05:06:08] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -50.665780+0.001937j
[2025-09-05 05:06:18] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -50.661063+0.003018j
[2025-09-05 05:06:28] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -50.672317+0.000777j
[2025-09-05 05:06:38] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -50.587447+0.001193j
[2025-09-05 05:06:48] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -50.581821-0.001315j
[2025-09-05 05:06:58] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -50.519535+0.000504j
[2025-09-05 05:07:09] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -50.524674-0.002334j
[2025-09-05 05:07:19] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -50.528973+0.000668j
[2025-09-05 05:07:29] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -50.494686+0.006916j
[2025-09-05 05:07:39] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -50.449782-0.000743j
[2025-09-05 05:07:49] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -50.513170-0.000371j
[2025-09-05 05:07:59] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -50.527133-0.001332j
[2025-09-05 05:07:59] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-05 05:08:09] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -50.515261-0.000405j
[2025-09-05 05:08:19] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -50.553305-0.000083j
[2025-09-05 05:08:29] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -50.432392-0.004941j
[2025-09-05 05:08:39] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -50.440264+0.001248j
[2025-09-05 05:08:49] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -50.478739+0.000789j
[2025-09-05 05:08:59] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -50.479044+0.000414j
[2025-09-05 05:09:10] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -50.462267-0.000254j
[2025-09-05 05:09:20] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -50.531598-0.000504j
[2025-09-05 05:09:30] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -50.557649+0.001059j
[2025-09-05 05:09:40] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -50.585177+0.003054j
[2025-09-05 05:09:50] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -50.568393-0.001822j
[2025-09-05 05:10:00] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -50.524226-0.000817j
[2025-09-05 05:10:10] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -50.454287+0.002781j
[2025-09-05 05:10:20] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -50.467447-0.001377j
[2025-09-05 05:10:30] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -50.482828-0.002149j
[2025-09-05 05:10:40] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -50.551622-0.004264j
[2025-09-05 05:10:50] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -50.600943-0.000975j
[2025-09-05 05:11:00] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -50.612624-0.000873j
[2025-09-05 05:11:11] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -50.760451+0.001857j
[2025-09-05 05:11:21] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -50.654782-0.001067j
[2025-09-05 05:11:31] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -50.600905-0.003733j
[2025-09-05 05:11:41] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -50.455417-0.000759j
[2025-09-05 05:11:51] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -50.581711+0.003087j
[2025-09-05 05:12:01] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -50.529526+0.001350j
[2025-09-05 05:12:11] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -50.473880+0.004018j
[2025-09-05 05:12:21] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -50.551385-0.000960j
[2025-09-05 05:12:31] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -50.541970-0.002701j
[2025-09-05 05:12:41] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -50.515036-0.000366j
[2025-09-05 05:12:51] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -50.438577-0.000885j
[2025-09-05 05:13:02] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -50.455144+0.000921j
[2025-09-05 05:13:02] RESTART #2 | Period: 600
[2025-09-05 05:13:12] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -50.487665-0.000230j
[2025-09-05 05:13:22] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -50.443172-0.001075j
[2025-09-05 05:13:32] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -50.374097-0.003635j
[2025-09-05 05:13:42] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -50.499566-0.000859j
[2025-09-05 05:13:52] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -50.430329+0.000144j
[2025-09-05 05:14:02] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -50.581250-0.000961j
[2025-09-05 05:14:12] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -50.546226-0.002212j
[2025-09-05 05:14:22] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -50.605225-0.000822j
[2025-09-05 05:14:32] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -50.384316+0.004219j
[2025-09-05 05:14:42] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -50.476220+0.000635j
[2025-09-05 05:14:52] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -50.639790-0.002540j
[2025-09-05 05:15:03] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -50.654291-0.002440j
[2025-09-05 05:15:13] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -50.576709-0.001296j
[2025-09-05 05:15:23] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -50.612856-0.001468j
[2025-09-05 05:15:33] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -50.660070-0.000114j
[2025-09-05 05:15:43] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -50.627623+0.001725j
[2025-09-05 05:15:53] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -50.581588-0.003390j
[2025-09-05 05:16:03] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -50.561301+0.002311j
[2025-09-05 05:16:13] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -50.668390+0.002253j
[2025-09-05 05:16:23] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -50.703474-0.000585j
[2025-09-05 05:16:34] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -50.714872+0.002658j
[2025-09-05 05:16:44] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -50.718779+0.000763j
[2025-09-05 05:16:54] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -50.719167-0.002463j
[2025-09-05 05:17:04] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -50.716019-0.001923j
[2025-09-05 05:17:14] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -50.707797+0.000273j
[2025-09-05 05:17:24] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -50.629577-0.002590j
[2025-09-05 05:17:34] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -50.642726-0.002465j
[2025-09-05 05:17:44] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -50.517675-0.001349j
[2025-09-05 05:17:54] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -50.617403-0.000421j
[2025-09-05 05:18:04] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -50.499818-0.001627j
[2025-09-05 05:18:14] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -50.455470-0.000283j
[2025-09-05 05:18:24] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -50.526002+0.003604j
[2025-09-05 05:18:35] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -50.415861+0.001902j
[2025-09-05 05:18:45] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -50.360796+0.002966j
[2025-09-05 05:18:55] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -50.494149+0.001635j
[2025-09-05 05:19:05] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -50.422185+0.000985j
[2025-09-05 05:19:15] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -50.569379+0.000627j
[2025-09-05 05:19:25] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -50.621846+0.001462j
[2025-09-05 05:19:35] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -50.494917-0.002935j
[2025-09-05 05:19:45] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -50.539313+0.001078j
[2025-09-05 05:19:55] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -50.566112-0.000790j
[2025-09-05 05:20:05] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -50.587280+0.000908j
[2025-09-05 05:20:15] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -50.544824+0.000524j
[2025-09-05 05:20:25] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -50.542468+0.000810j
[2025-09-05 05:20:36] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -50.633547+0.002903j
[2025-09-05 05:20:46] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -50.547392+0.003080j
[2025-09-05 05:20:56] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -50.473372+0.002611j
[2025-09-05 05:21:06] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -50.624302+0.001978j
[2025-09-05 05:21:16] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -50.553566+0.001151j
[2025-09-05 05:21:26] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -50.498216-0.000121j
[2025-09-05 05:21:36] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -50.528257+0.000797j
[2025-09-05 05:21:46] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -50.551376-0.002211j
[2025-09-05 05:21:56] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -50.535726+0.000795j
[2025-09-05 05:22:06] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -50.555024+0.001933j
[2025-09-05 05:22:16] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -50.747549-0.003667j
[2025-09-05 05:22:26] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -50.583721+0.002244j
[2025-09-05 05:22:37] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -50.466632-0.003661j
[2025-09-05 05:22:47] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -50.637995-0.001981j
[2025-09-05 05:22:57] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -50.566903-0.000187j
[2025-09-05 05:23:07] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -50.580986+0.004493j
[2025-09-05 05:23:17] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -50.537416+0.001258j
[2025-09-05 05:23:27] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -50.631463+0.000158j
[2025-09-05 05:23:37] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -50.473215-0.000567j
[2025-09-05 05:23:47] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -50.514764+0.002627j
[2025-09-05 05:23:57] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -50.628824+0.002206j
[2025-09-05 05:24:07] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -50.655068+0.004010j
[2025-09-05 05:24:17] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -50.591491+0.000772j
[2025-09-05 05:24:27] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -50.625579-0.002693j
[2025-09-05 05:24:38] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -50.547750+0.000687j
[2025-09-05 05:24:48] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -50.590640+0.001014j
[2025-09-05 05:24:58] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -50.458230-0.000317j
[2025-09-05 05:25:08] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -50.464146+0.002486j
[2025-09-05 05:25:18] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -50.545974+0.003571j
[2025-09-05 05:25:28] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -50.585359+0.003806j
[2025-09-05 05:25:38] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -50.524985+0.004682j
[2025-09-05 05:25:38] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-05 05:25:48] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -50.567103+0.003217j
[2025-09-05 05:25:58] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -50.666756-0.001334j
[2025-09-05 05:26:08] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -50.735020+0.000740j
[2025-09-05 05:26:18] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -50.577072+0.001478j
[2025-09-05 05:26:28] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -50.596806-0.001005j
[2025-09-05 05:26:39] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -50.612807+0.002280j
[2025-09-05 05:26:49] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -50.656653+0.000777j
[2025-09-05 05:26:59] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -50.582492+0.001059j
[2025-09-05 05:27:09] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -50.640784+0.002102j
[2025-09-05 05:27:19] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -50.640496-0.000210j
[2025-09-05 05:27:29] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -50.618601+0.000851j
[2025-09-05 05:27:39] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -50.572122-0.000980j
[2025-09-05 05:27:49] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -50.528290-0.000568j
[2025-09-05 05:27:59] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -50.694506-0.000252j
[2025-09-05 05:28:09] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -50.668816-0.003513j
[2025-09-05 05:28:19] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -50.606344-0.001391j
[2025-09-05 05:28:30] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -50.589188+0.007384j
[2025-09-05 05:28:40] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -50.677995-0.001395j
[2025-09-05 05:28:50] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -50.703840-0.000716j
[2025-09-05 05:29:00] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -50.657311-0.002464j
[2025-09-05 05:29:10] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -50.642413-0.001917j
[2025-09-05 05:29:20] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -50.612898-0.004417j
[2025-09-05 05:29:30] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -50.593624-0.002756j
[2025-09-05 05:29:40] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -50.523899+0.001207j
[2025-09-05 05:29:50] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -50.498285+0.000896j
[2025-09-05 05:30:00] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -50.527646-0.001609j
[2025-09-05 05:30:10] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -50.522003+0.002125j
[2025-09-05 05:30:20] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -50.580436+0.004259j
[2025-09-05 05:30:31] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -50.576764+0.002853j
[2025-09-05 05:30:41] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -50.503816+0.001318j
[2025-09-05 05:30:51] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -50.600854+0.003750j
[2025-09-05 05:31:01] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -50.507010-0.003110j
[2025-09-05 05:31:11] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -50.499454+0.001149j
[2025-09-05 05:31:21] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -50.552827+0.002254j
[2025-09-05 05:31:31] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -50.541869+0.000374j
[2025-09-05 05:31:41] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -50.390459-0.001378j
[2025-09-05 05:31:51] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -50.484466-0.002647j
[2025-09-05 05:32:01] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -50.488021+0.000443j
[2025-09-05 05:32:11] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -50.440811+0.001235j
[2025-09-05 05:32:21] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -50.484494-0.002891j
[2025-09-05 05:32:32] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -50.633697-0.001506j
[2025-09-05 05:32:42] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -50.611517-0.000339j
[2025-09-05 05:32:52] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -50.505278-0.001961j
[2025-09-05 05:33:02] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -50.465864-0.000347j
[2025-09-05 05:33:12] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -50.591584+0.001097j
[2025-09-05 05:33:22] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -50.563429-0.001409j
[2025-09-05 05:33:32] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -50.472071-0.000623j
[2025-09-05 05:33:42] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -50.502275+0.000257j
[2025-09-05 05:33:52] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -50.626836-0.000739j
[2025-09-05 05:34:02] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -50.682047+0.003319j
[2025-09-05 05:34:12] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -50.544849+0.000019j
[2025-09-05 05:34:22] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -50.752796+0.001306j
[2025-09-05 05:34:33] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -50.585893+0.004307j
[2025-09-05 05:34:43] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -50.567126-0.000269j
[2025-09-05 05:34:53] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -50.579831-0.001459j
[2025-09-05 05:35:03] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -50.621730-0.004701j
[2025-09-05 05:35:13] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -50.584572+0.002693j
[2025-09-05 05:35:23] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -50.629009+0.001114j
[2025-09-05 05:35:33] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -50.551771-0.002647j
[2025-09-05 05:35:43] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -50.554197-0.000972j
[2025-09-05 05:35:53] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -50.652971+0.002231j
[2025-09-05 05:36:03] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -50.651598-0.000518j
[2025-09-05 05:36:13] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -50.538132+0.001389j
[2025-09-05 05:36:24] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -50.508086-0.002219j
[2025-09-05 05:36:34] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -50.599557+0.000431j
[2025-09-05 05:36:44] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -50.518797-0.001068j
[2025-09-05 05:36:54] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -50.465991-0.000482j
[2025-09-05 05:37:04] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -50.578657+0.002827j
[2025-09-05 05:37:14] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -50.655149+0.000876j
[2025-09-05 05:37:24] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -50.608532+0.000384j
[2025-09-05 05:37:34] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -50.711178+0.001126j
[2025-09-05 05:37:44] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -50.569242-0.000676j
[2025-09-05 05:37:54] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -50.497532-0.002746j
[2025-09-05 05:38:04] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -50.606288+0.002041j
[2025-09-05 05:38:14] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -50.593364-0.000741j
[2025-09-05 05:38:24] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -50.490177+0.002224j
[2025-09-05 05:38:35] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -50.476636-0.002559j
[2025-09-05 05:38:45] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -50.567041-0.002834j
[2025-09-05 05:38:55] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -50.626196-0.003581j
[2025-09-05 05:39:05] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -50.596505-0.004737j
[2025-09-05 05:39:15] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -50.714201-0.002539j
[2025-09-05 05:39:25] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -50.721020+0.003120j
[2025-09-05 05:39:35] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -50.609377+0.001648j
[2025-09-05 05:39:45] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -50.653371+0.002793j
[2025-09-05 05:39:55] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -50.607405-0.000305j
[2025-09-05 05:40:05] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -50.391250+0.002434j
[2025-09-05 05:40:15] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -50.586397-0.002251j
[2025-09-05 05:40:26] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -50.586555+0.002095j
[2025-09-05 05:40:36] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -50.529733+0.001165j
[2025-09-05 05:40:46] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -50.678702-0.004246j
[2025-09-05 05:40:56] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -50.628908-0.002467j
[2025-09-05 05:41:06] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -50.619934-0.003587j
[2025-09-05 05:41:16] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -50.545238+0.000709j
[2025-09-05 05:41:26] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -50.709491-0.003170j
[2025-09-05 05:41:36] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -50.671471-0.003120j
[2025-09-05 05:41:46] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -50.666959+0.000972j
[2025-09-05 05:41:56] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -50.574936-0.001734j
[2025-09-05 05:42:06] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -50.431575-0.002948j
[2025-09-05 05:42:16] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -50.612575+0.003531j
[2025-09-05 05:42:27] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -50.649316-0.001348j
[2025-09-05 05:42:37] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -50.635613+0.005589j
[2025-09-05 05:42:47] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -50.698807-0.003941j
[2025-09-05 05:42:57] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -50.662133+0.003647j
[2025-09-05 05:43:07] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -50.600202+0.003369j
[2025-09-05 05:43:17] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -50.499359+0.002655j
[2025-09-05 05:43:17] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-05 05:43:27] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -50.401870+0.001753j
[2025-09-05 05:43:37] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -50.456061+0.001010j
[2025-09-05 05:43:47] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -50.462260-0.001579j
[2025-09-05 05:43:57] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -50.548215-0.000094j
[2025-09-05 05:44:07] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -50.536465-0.001165j
[2025-09-05 05:44:17] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -50.522348+0.001552j
[2025-09-05 05:44:27] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -50.640935+0.003771j
[2025-09-05 05:44:37] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -50.666514+0.001894j
[2025-09-05 05:44:47] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -50.586442+0.000308j
[2025-09-05 05:44:57] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -50.552539+0.001295j
[2025-09-05 05:45:07] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -50.520872+0.001465j
[2025-09-05 05:45:17] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -50.673429-0.004432j
[2025-09-05 05:45:28] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -50.521454+0.002195j
[2025-09-05 05:45:38] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -50.447236-0.000991j
[2025-09-05 05:45:48] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -50.477806-0.001511j
[2025-09-05 05:45:58] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -50.589174+0.001434j
[2025-09-05 05:46:08] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -50.670010+0.001816j
[2025-09-05 05:46:18] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -50.594483-0.001681j
[2025-09-05 05:46:28] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -50.461517-0.002138j
[2025-09-05 05:46:38] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -50.437497+0.002728j
[2025-09-05 05:46:48] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -50.574141-0.000963j
[2025-09-05 05:46:58] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -50.483324-0.000845j
[2025-09-05 05:47:08] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -50.561258+0.004816j
[2025-09-05 05:47:18] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -50.459167-0.002238j
[2025-09-05 05:47:29] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -50.387440+0.000932j
[2025-09-05 05:47:39] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -50.469545-0.000008j
[2025-09-05 05:47:49] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -50.535873+0.000909j
[2025-09-05 05:47:59] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -50.545493-0.003674j
[2025-09-05 05:48:09] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -50.540106-0.000700j
[2025-09-05 05:48:19] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -50.511238+0.003235j
[2025-09-05 05:48:29] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -50.494329-0.000075j
[2025-09-05 05:48:39] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -50.486355-0.002611j
[2025-09-05 05:48:49] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -50.502841+0.000468j
[2025-09-05 05:48:59] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -50.468505-0.000718j
[2025-09-05 05:49:09] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -50.442920+0.004621j
[2025-09-05 05:49:19] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -50.532268+0.002617j
[2025-09-05 05:49:29] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -50.510063+0.002670j
[2025-09-05 05:49:40] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -50.611850+0.001971j
[2025-09-05 05:49:50] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -50.575684+0.001129j
[2025-09-05 05:50:00] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -50.496079-0.000000j
[2025-09-05 05:50:10] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -50.543686+0.003899j
[2025-09-05 05:50:20] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -50.691222+0.004833j
[2025-09-05 05:50:30] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -50.678647-0.003126j
[2025-09-05 05:50:40] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -50.521863-0.003082j
[2025-09-05 05:50:50] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -50.560901-0.001348j
[2025-09-05 05:51:00] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -50.512355+0.001291j
[2025-09-05 05:51:10] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -50.541384-0.001108j
[2025-09-05 05:51:20] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -50.591239-0.001137j
[2025-09-05 05:51:30] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -50.566134-0.000453j
[2025-09-05 05:51:41] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -50.594779+0.004262j
[2025-09-05 05:51:51] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -50.581313+0.000473j
[2025-09-05 05:52:01] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -50.572707+0.002143j
[2025-09-05 05:52:11] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -50.553626+0.001053j
[2025-09-05 05:52:21] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -50.586543+0.001389j
[2025-09-05 05:52:31] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -50.692205-0.002239j
[2025-09-05 05:52:41] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -50.663198-0.001393j
[2025-09-05 05:52:51] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -50.642902-0.000832j
[2025-09-05 05:53:01] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -50.502910+0.005541j
[2025-09-05 05:53:11] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -50.554596+0.001803j
[2025-09-05 05:53:21] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -50.451218+0.002546j
[2025-09-05 05:53:31] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -50.333033-0.001002j
[2025-09-05 05:53:41] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -50.495038-0.002524j
[2025-09-05 05:53:52] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -50.501298-0.000770j
[2025-09-05 05:54:02] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -50.727542+0.002733j
[2025-09-05 05:54:12] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -50.679219+0.000875j
[2025-09-05 05:54:22] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -50.685340-0.004755j
[2025-09-05 05:54:32] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -50.656874-0.001351j
[2025-09-05 05:54:42] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -50.775979-0.001614j
[2025-09-05 05:54:52] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -50.565399+0.000768j
[2025-09-05 05:55:02] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -50.572551-0.002403j
[2025-09-05 05:55:12] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -50.582714+0.002651j
[2025-09-05 05:55:22] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -50.680369+0.000708j
[2025-09-05 05:55:32] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -50.749444-0.001566j
[2025-09-05 05:55:42] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -50.637941-0.001060j
[2025-09-05 05:55:53] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -50.670555+0.000422j
[2025-09-05 05:56:03] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -50.603759-0.004373j
[2025-09-05 05:56:13] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -50.690718+0.001755j
[2025-09-05 05:56:23] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -50.587730-0.004510j
[2025-09-05 05:56:33] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -50.554623+0.000676j
[2025-09-05 05:56:43] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -50.730031+0.001582j
[2025-09-05 05:56:53] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -50.727631+0.000121j
[2025-09-05 05:57:03] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -50.629569-0.008231j
[2025-09-05 05:57:13] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -50.695538+0.001255j
[2025-09-05 05:57:23] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -50.635872-0.000902j
[2025-09-05 05:57:33] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -50.710139-0.001742j
[2025-09-05 05:57:43] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -50.552405+0.002162j
[2025-09-05 05:57:54] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -50.538620-0.001314j
[2025-09-05 05:58:04] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -50.642257-0.000055j
[2025-09-05 05:58:14] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -50.601545-0.001202j
[2025-09-05 05:58:24] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -50.572814-0.001049j
[2025-09-05 05:58:34] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -50.588686+0.000557j
[2025-09-05 05:58:44] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -50.639209-0.003001j
[2025-09-05 05:58:54] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -50.594936+0.002748j
[2025-09-05 05:59:04] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -50.620482-0.001779j
[2025-09-05 05:59:14] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -50.657435-0.001353j
[2025-09-05 05:59:24] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -50.690001-0.002568j
[2025-09-05 05:59:34] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -50.667697+0.000763j
[2025-09-05 05:59:44] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -50.636339-0.003627j
[2025-09-05 05:59:55] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -50.670041+0.001677j
[2025-09-05 06:00:05] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -50.731142+0.001152j
[2025-09-05 06:00:15] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -50.646823-0.001609j
[2025-09-05 06:00:25] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -50.683756+0.001178j
[2025-09-05 06:00:35] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -50.541180+0.000447j
[2025-09-05 06:00:45] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -50.585143-0.003245j
[2025-09-05 06:00:55] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -50.518974-0.002215j
[2025-09-05 06:00:55] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-05 06:01:05] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -50.530333-0.000517j
[2025-09-05 06:01:15] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -50.596632-0.000553j
[2025-09-05 06:01:25] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -50.632956+0.001798j
[2025-09-05 06:01:35] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -50.558408+0.001149j
[2025-09-05 06:01:45] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -50.619679-0.001886j
[2025-09-05 06:01:56] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -50.620072+0.003310j
[2025-09-05 06:02:06] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -50.652753-0.001931j
[2025-09-05 06:02:16] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -50.669293-0.004187j
[2025-09-05 06:02:26] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -50.581234+0.002197j
[2025-09-05 06:02:36] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -50.638910-0.003771j
[2025-09-05 06:02:46] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -50.651169-0.000147j
[2025-09-05 06:02:56] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -50.480360-0.000984j
[2025-09-05 06:03:06] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -50.453558-0.002589j
[2025-09-05 06:03:16] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -50.560138-0.000685j
[2025-09-05 06:03:26] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -50.575976-0.000451j
[2025-09-05 06:03:36] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -50.715934+0.001507j
[2025-09-05 06:03:46] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -50.557779-0.002390j
[2025-09-05 06:03:57] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -50.687490-0.001576j
[2025-09-05 06:04:07] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -50.709806-0.000278j
[2025-09-05 06:04:17] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -50.724269-0.000058j
[2025-09-05 06:04:27] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -50.689409-0.003218j
[2025-09-05 06:04:37] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -50.729431-0.001510j
[2025-09-05 06:04:47] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -50.565716-0.002005j
[2025-09-05 06:04:57] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -50.580868+0.002152j
[2025-09-05 06:05:07] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -50.505663+0.003105j
[2025-09-05 06:05:17] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -50.454521+0.000480j
[2025-09-05 06:05:27] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -50.481975-0.000261j
[2025-09-05 06:05:37] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -50.545801+0.001186j
[2025-09-05 06:05:47] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -50.622676+0.000104j
[2025-09-05 06:05:58] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -50.523847-0.001753j
[2025-09-05 06:06:08] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -50.664087-0.000920j
[2025-09-05 06:06:18] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -50.647555-0.003204j
[2025-09-05 06:06:28] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -50.630870-0.000268j
[2025-09-05 06:06:38] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -50.522709-0.003162j
[2025-09-05 06:06:48] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -50.607147+0.001925j
[2025-09-05 06:06:58] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -50.693144-0.001107j
[2025-09-05 06:07:08] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -50.706297-0.000078j
[2025-09-05 06:07:18] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -50.686475-0.000537j
[2025-09-05 06:07:28] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -50.635738-0.000327j
[2025-09-05 06:07:38] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -50.595429+0.002311j
[2025-09-05 06:07:48] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -50.477109+0.000694j
[2025-09-05 06:07:59] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -50.560848+0.001725j
[2025-09-05 06:08:09] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -50.512850+0.000030j
[2025-09-05 06:08:19] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -50.544912-0.003231j
[2025-09-05 06:08:29] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -50.608167+0.001338j
[2025-09-05 06:08:39] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -50.667558-0.004336j
[2025-09-05 06:08:49] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -50.562191+0.002343j
[2025-09-05 06:08:59] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -50.606724+0.000383j
[2025-09-05 06:09:09] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -50.503893-0.001847j
[2025-09-05 06:09:19] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -50.470020-0.000535j
[2025-09-05 06:09:29] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -50.535802-0.002511j
[2025-09-05 06:09:39] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -50.552497+0.000415j
[2025-09-05 06:09:49] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -50.436276+0.001482j
[2025-09-05 06:10:00] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -50.451025-0.001057j
[2025-09-05 06:10:10] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -50.580592+0.000978j
[2025-09-05 06:10:20] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -50.606659-0.000043j
[2025-09-05 06:10:30] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -50.555787-0.002031j
[2025-09-05 06:10:40] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -50.654768+0.001954j
[2025-09-05 06:10:50] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -50.546789+0.003159j
[2025-09-05 06:11:00] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -50.447845+0.000290j
[2025-09-05 06:11:10] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -50.575086+0.001076j
[2025-09-05 06:11:20] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -50.581413+0.004600j
[2025-09-05 06:11:30] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -50.705926-0.005606j
[2025-09-05 06:11:40] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -50.546504-0.000725j
[2025-09-05 06:11:50] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -50.585700+0.003048j
[2025-09-05 06:12:00] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -50.670429+0.003032j
[2025-09-05 06:12:11] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -50.657395-0.001522j
[2025-09-05 06:12:21] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -50.553861-0.000366j
[2025-09-05 06:12:31] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -50.581951-0.002964j
[2025-09-05 06:12:41] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -50.660494-0.001018j
[2025-09-05 06:12:51] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -50.583254+0.000786j
[2025-09-05 06:13:01] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -50.578234-0.000603j
[2025-09-05 06:13:11] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -50.577443+0.001149j
[2025-09-05 06:13:21] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -50.627677+0.002408j
[2025-09-05 06:13:31] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -50.565000-0.000453j
[2025-09-05 06:13:41] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -50.565022-0.003054j
[2025-09-05 06:13:51] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -50.504320+0.001234j
[2025-09-05 06:14:02] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -50.524651+0.002472j
[2025-09-05 06:14:12] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -50.486026-0.001216j
[2025-09-05 06:14:22] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -50.621754+0.001472j
[2025-09-05 06:14:32] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -50.621891-0.002420j
[2025-09-05 06:14:42] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -50.644888+0.003779j
[2025-09-05 06:14:52] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -50.586471+0.000640j
[2025-09-05 06:15:02] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -50.645829+0.001532j
[2025-09-05 06:15:12] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -50.557483-0.000466j
[2025-09-05 06:15:22] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -50.604999-0.002456j
[2025-09-05 06:15:32] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -50.502561+0.002709j
[2025-09-05 06:15:42] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -50.601108+0.002352j
[2025-09-05 06:15:52] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -50.481803-0.000347j
[2025-09-05 06:16:03] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -50.538541-0.002196j
[2025-09-05 06:16:13] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -50.633669+0.002748j
[2025-09-05 06:16:23] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -50.631924-0.001178j
[2025-09-05 06:16:33] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -50.519294+0.004132j
[2025-09-05 06:16:43] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -50.534222-0.003221j
[2025-09-05 06:16:53] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -50.599945-0.001948j
[2025-09-05 06:17:03] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -50.589050+0.000876j
[2025-09-05 06:17:13] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -50.575431+0.002167j
[2025-09-05 06:17:23] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -50.621833+0.001854j
[2025-09-05 06:17:33] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -50.714502+0.000679j
[2025-09-05 06:17:43] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -50.582431-0.001373j
[2025-09-05 06:17:53] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -50.600752-0.004824j
[2025-09-05 06:18:04] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -50.642614-0.002993j
[2025-09-05 06:18:14] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -50.690967-0.000849j
[2025-09-05 06:18:24] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -50.674092-0.001421j
[2025-09-05 06:18:34] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -50.651393+0.000062j
[2025-09-05 06:18:34] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-05 06:18:44] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -50.623822+0.001696j
[2025-09-05 06:18:54] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -50.568132-0.003943j
[2025-09-05 06:19:04] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -50.524652-0.000191j
[2025-09-05 06:19:14] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -50.608142+0.001009j
[2025-09-05 06:19:24] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -50.634807+0.000005j
[2025-09-05 06:19:34] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -50.583873-0.005814j
[2025-09-05 06:19:44] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -50.622245-0.001516j
[2025-09-05 06:19:54] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -50.640499-0.000567j
[2025-09-05 06:20:05] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -50.652614-0.001655j
[2025-09-05 06:20:15] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -50.693334-0.000096j
[2025-09-05 06:20:25] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -50.703918+0.003575j
[2025-09-05 06:20:35] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -50.682667-0.001324j
[2025-09-05 06:20:45] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -50.581296+0.002015j
[2025-09-05 06:20:55] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -50.486434+0.003694j
[2025-09-05 06:21:05] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -50.408909-0.002260j
[2025-09-05 06:21:15] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -50.369257-0.000378j
[2025-09-05 06:21:25] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -50.472983-0.002994j
[2025-09-05 06:21:35] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -50.443138+0.000656j
[2025-09-05 06:21:45] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -50.450045-0.003128j
[2025-09-05 06:21:55] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -50.591561-0.001147j
[2025-09-05 06:22:06] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -50.602197+0.002421j
[2025-09-05 06:22:16] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -50.748322+0.001419j
[2025-09-05 06:22:26] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -50.658911-0.001929j
[2025-09-05 06:22:36] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -50.617470-0.002726j
[2025-09-05 06:22:46] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -50.517845+0.003285j
[2025-09-05 06:22:56] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -50.622796+0.003134j
[2025-09-05 06:23:06] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -50.667159+0.001938j
[2025-09-05 06:23:16] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -50.733084-0.002205j
[2025-09-05 06:23:26] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -50.726285+0.000110j
[2025-09-05 06:23:36] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -50.609696+0.002411j
[2025-09-05 06:23:46] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -50.445789-0.000025j
[2025-09-05 06:23:56] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -50.420016+0.002475j
[2025-09-05 06:24:07] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -50.471503+0.000358j
[2025-09-05 06:24:17] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -50.457431-0.000391j
[2025-09-05 06:24:27] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -50.531590+0.004313j
[2025-09-05 06:24:37] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -50.556244+0.000610j
[2025-09-05 06:24:47] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -50.627536-0.000995j
[2025-09-05 06:24:57] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -50.515975-0.001800j
[2025-09-05 06:25:07] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -50.619449+0.002632j
[2025-09-05 06:25:17] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -50.494574+0.001926j
[2025-09-05 06:25:27] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -50.595301+0.000601j
[2025-09-05 06:25:37] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -50.493302+0.000922j
[2025-09-05 06:25:47] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -50.494733-0.000518j
[2025-09-05 06:25:57] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -50.538574-0.005110j
[2025-09-05 06:26:08] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -50.617027+0.001347j
[2025-09-05 06:26:18] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -50.535869+0.000601j
[2025-09-05 06:26:28] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -50.499485-0.000278j
[2025-09-05 06:26:38] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -50.563238+0.000382j
[2025-09-05 06:26:48] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -50.449147+0.000237j
[2025-09-05 06:26:58] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -50.593612-0.001300j
[2025-09-05 06:27:08] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -50.615980-0.003019j
[2025-09-05 06:27:18] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -50.629439+0.000193j
[2025-09-05 06:27:28] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -50.535764-0.000472j
[2025-09-05 06:27:38] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -50.594264-0.001565j
[2025-09-05 06:27:48] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -50.642001+0.000207j
[2025-09-05 06:27:58] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -50.583876-0.002500j
[2025-09-05 06:28:08] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -50.550390-0.001350j
[2025-09-05 06:28:19] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -50.496893+0.011360j
[2025-09-05 06:28:29] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -50.640630-0.003815j
[2025-09-05 06:28:39] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -50.596680+0.002472j
[2025-09-05 06:28:49] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -50.588139+0.001497j
[2025-09-05 06:28:59] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -50.515565-0.001271j
[2025-09-05 06:29:09] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -50.534379-0.000027j
[2025-09-05 06:29:19] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -50.548307-0.001953j
[2025-09-05 06:29:29] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -50.687256-0.001760j
[2025-09-05 06:29:39] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -50.721301-0.001222j
[2025-09-05 06:29:49] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -50.771926+0.001721j
[2025-09-05 06:29:59] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -50.640778-0.002718j
[2025-09-05 06:30:10] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -50.602823-0.000959j
[2025-09-05 06:30:20] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -50.543648+0.001541j
[2025-09-05 06:30:30] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -50.598098+0.000524j
[2025-09-05 06:30:40] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -50.666911-0.000354j
[2025-09-05 06:30:50] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -50.622328+0.000189j
[2025-09-05 06:31:00] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -50.592084+0.000934j
[2025-09-05 06:31:10] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -50.607670+0.000151j
[2025-09-05 06:31:20] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -50.506910+0.000513j
[2025-09-05 06:31:30] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -50.558019-0.001000j
[2025-09-05 06:31:40] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -50.477041+0.001345j
[2025-09-05 06:31:50] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -50.478515+0.000231j
[2025-09-05 06:32:01] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -50.495616+0.002315j
[2025-09-05 06:32:11] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -50.551861+0.002775j
[2025-09-05 06:32:21] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -50.407538+0.000578j
[2025-09-05 06:32:31] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -50.479891-0.000274j
[2025-09-05 06:32:41] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -50.436416-0.000661j
[2025-09-05 06:32:51] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -50.407057-0.000745j
[2025-09-05 06:33:01] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -50.494919-0.004842j
[2025-09-05 06:33:11] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -50.466186+0.002058j
[2025-09-05 06:33:21] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -50.546556-0.001144j
[2025-09-05 06:33:31] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -50.470950+0.002764j
[2025-09-05 06:33:41] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -50.528624+0.001133j
[2025-09-05 06:33:51] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -50.537715+0.001685j
[2025-09-05 06:34:02] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -50.471774+0.001733j
[2025-09-05 06:34:12] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -50.564697-0.002206j
[2025-09-05 06:34:22] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -50.448133-0.000598j
[2025-09-05 06:34:32] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -50.570138+0.000914j
[2025-09-05 06:34:42] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -50.575552-0.001337j
[2025-09-05 06:34:52] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -50.625671+0.000011j
[2025-09-05 06:35:02] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -50.554409+0.000615j
[2025-09-05 06:35:12] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -50.604860-0.001767j
[2025-09-05 06:35:22] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -50.551002+0.002298j
[2025-09-05 06:35:32] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -50.542348+0.002050j
[2025-09-05 06:35:42] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -50.504022+0.002289j
[2025-09-05 06:35:52] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -50.515847+0.003864j
[2025-09-05 06:36:03] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -50.578914-0.000121j
[2025-09-05 06:36:13] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -50.473456-0.002464j
[2025-09-05 06:36:13] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-05 06:36:23] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -50.429681+0.001658j
[2025-09-05 06:36:33] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -50.523169-0.000996j
[2025-09-05 06:36:43] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -50.566305+0.001160j
[2025-09-05 06:36:53] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -50.405625-0.000500j
[2025-09-05 06:37:03] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -50.492367-0.000508j
[2025-09-05 06:37:13] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -50.494845-0.000578j
[2025-09-05 06:37:23] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -50.479679-0.002472j
[2025-09-05 06:37:33] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -50.476394+0.000559j
[2025-09-05 06:37:43] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -50.476573+0.000875j
[2025-09-05 06:37:53] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -50.521053-0.003582j
[2025-09-05 06:38:04] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -50.472536+0.004663j
[2025-09-05 06:38:14] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -50.552527-0.001081j
[2025-09-05 06:38:24] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -50.495962-0.000271j
[2025-09-05 06:38:34] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -50.566804+0.001321j
[2025-09-05 06:38:44] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -50.456348+0.000565j
[2025-09-05 06:38:54] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -50.436779+0.001577j
[2025-09-05 06:39:04] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -50.482737+0.005796j
[2025-09-05 06:39:14] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -50.580123-0.001211j
[2025-09-05 06:39:24] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -50.481019+0.000419j
[2025-09-05 06:39:34] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -50.610737+0.002354j
[2025-09-05 06:39:44] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -50.563646+0.000860j
[2025-09-05 06:39:54] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -50.613756+0.006225j
[2025-09-05 06:40:05] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -50.581201-0.000363j
[2025-09-05 06:40:15] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -50.654399+0.002964j
[2025-09-05 06:40:25] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -50.677056-0.004613j
[2025-09-05 06:40:35] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -50.647305-0.001624j
[2025-09-05 06:40:45] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -50.672498-0.001592j
[2025-09-05 06:40:55] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -50.502835-0.002090j
[2025-09-05 06:41:05] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -50.552353-0.001906j
[2025-09-05 06:41:15] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -50.584707+0.003536j
[2025-09-05 06:41:25] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -50.473395+0.002030j
[2025-09-05 06:41:35] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -50.454181+0.000802j
[2025-09-05 06:41:45] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -50.583603-0.000326j
[2025-09-05 06:41:56] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -50.600719+0.001619j
[2025-09-05 06:42:06] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -50.617841+0.001986j
[2025-09-05 06:42:16] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -50.589168+0.001373j
[2025-09-05 06:42:26] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -50.396583-0.002636j
[2025-09-05 06:42:36] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -50.462514-0.001543j
[2025-09-05 06:42:46] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -50.550114-0.001381j
[2025-09-05 06:42:56] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -50.371155+0.004206j
[2025-09-05 06:43:06] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -50.444680+0.002477j
[2025-09-05 06:43:16] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -50.505913-0.000868j
[2025-09-05 06:43:26] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -50.635798-0.001000j
[2025-09-05 06:43:36] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -50.573920+0.001491j
[2025-09-05 06:43:46] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -50.509793+0.001025j
[2025-09-05 06:43:57] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -50.544273+0.001791j
[2025-09-05 06:44:07] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -50.538836-0.003041j
[2025-09-05 06:44:17] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -50.549854-0.003391j
[2025-09-05 06:44:27] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -50.461979+0.000743j
[2025-09-05 06:44:37] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -50.587486+0.000286j
[2025-09-05 06:44:47] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -50.477999+0.006918j
[2025-09-05 06:44:57] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -50.561714-0.002149j
[2025-09-05 06:45:07] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -50.678671-0.000785j
[2025-09-05 06:45:17] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -50.599500-0.002946j
[2025-09-05 06:45:27] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -50.573507-0.002323j
[2025-09-05 06:45:37] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -50.589388-0.002528j
[2025-09-05 06:45:47] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -50.629988-0.001141j
[2025-09-05 06:45:58] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -50.689458-0.001475j
[2025-09-05 06:46:08] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -50.588000+0.002424j
[2025-09-05 06:46:18] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -50.547901+0.000469j
[2025-09-05 06:46:28] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -50.558606+0.001349j
[2025-09-05 06:46:38] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -50.551486+0.003542j
[2025-09-05 06:46:48] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -50.682535-0.001924j
[2025-09-05 06:46:58] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -50.663121+0.002401j
[2025-09-05 06:47:08] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -50.546749-0.000439j
[2025-09-05 06:47:18] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -50.576974-0.003551j
[2025-09-05 06:47:28] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -50.458209-0.000103j
[2025-09-05 06:47:39] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -50.361244+0.001279j
[2025-09-05 06:47:49] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -50.535934-0.002702j
[2025-09-05 06:47:59] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -50.539545+0.001355j
[2025-09-05 06:48:09] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -50.563498-0.003296j
[2025-09-05 06:48:19] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -50.565055-0.000960j
[2025-09-05 06:48:29] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -50.624335-0.000663j
[2025-09-05 06:48:39] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -50.672264-0.003392j
[2025-09-05 06:48:49] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -50.534390+0.001595j
[2025-09-05 06:48:59] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -50.647717-0.002715j
[2025-09-05 06:49:09] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -50.664634-0.000284j
[2025-09-05 06:49:19] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -50.736756-0.000348j
[2025-09-05 06:49:29] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -50.670180+0.000770j
[2025-09-05 06:49:40] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -50.530776-0.003257j
[2025-09-05 06:49:50] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -50.576649-0.000399j
[2025-09-05 06:50:00] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -50.519901+0.003590j
[2025-09-05 06:50:10] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -50.408142-0.000083j
[2025-09-05 06:50:20] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -50.423969+0.000835j
[2025-09-05 06:50:30] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -50.560129+0.001428j
[2025-09-05 06:50:40] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -50.558550-0.001653j
[2025-09-05 06:50:50] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -50.538838+0.001300j
[2025-09-05 06:51:00] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -50.569245-0.003040j
[2025-09-05 06:51:10] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -50.600880+0.004546j
[2025-09-05 06:51:20] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -50.638761+0.001188j
[2025-09-05 06:51:30] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -50.670949+0.001894j
[2025-09-05 06:51:40] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -50.564452+0.002139j
[2025-09-05 06:51:50] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -50.567373-0.000643j
[2025-09-05 06:52:00] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -50.451571+0.002532j
[2025-09-05 06:52:10] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -50.554999+0.001154j
[2025-09-05 06:52:20] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -50.590919-0.001672j
[2025-09-05 06:52:30] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -50.628557+0.000292j
[2025-09-05 06:52:41] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -50.602076-0.000479j
[2025-09-05 06:52:51] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -50.550690-0.002038j
[2025-09-05 06:53:01] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -50.600200-0.004054j
[2025-09-05 06:53:11] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -50.512621-0.000756j
[2025-09-05 06:53:21] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -50.565985+0.002066j
[2025-09-05 06:53:31] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -50.596486+0.001195j
[2025-09-05 06:53:41] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -50.542366+0.000001j
[2025-09-05 06:53:51] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -50.619205+0.000173j
[2025-09-05 06:53:51] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-05 06:53:51] ✅ Training completed | Restarts: 2
[2025-09-05 06:53:51] ============================================================
[2025-09-05 06:53:51] Training completed | Runtime: 10635.4s
[2025-09-05 06:53:55] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-05 06:53:55] ============================================================
