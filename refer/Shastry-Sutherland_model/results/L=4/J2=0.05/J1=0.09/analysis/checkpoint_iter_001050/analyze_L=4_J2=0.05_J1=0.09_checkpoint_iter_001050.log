[2025-09-07 00:42:55] 使用checkpoint文件: results/L=4/J2=0.05/J1=0.09/training/checkpoints/checkpoint_iter_001050.pkl
[2025-09-07 00:43:09] ✓ 从checkpoint加载参数: 1050
[2025-09-07 00:43:09]   - 能量: -53.700410-0.001197j ± 0.082965
[2025-09-07 00:43:09] ================================================================================
[2025-09-07 00:43:09] 加载量子态: L=4, J2=0.05, J1=0.09, checkpoint=checkpoint_iter_001050
[2025-09-07 00:43:09] 使用采样数目: 1048576
[2025-09-07 00:43:09] 设置样本数为: 1048576
[2025-09-07 00:43:09] 开始生成共享样本集...
[2025-09-07 00:46:07] 样本生成完成,耗时: 178.214 秒
[2025-09-07 00:46:08] ================================================================================
[2025-09-07 00:46:08] 开始计算自旋结构因子...
[2025-09-07 00:46:08] 初始化操作符缓存...
[2025-09-07 00:46:08] 预构建所有自旋相关操作符...
[2025-09-07 00:46:08] 开始计算自旋相关函数...
[2025-09-07 00:46:18] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 10.711s
[2025-09-07 00:46:32] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 14.044s
[2025-09-07 00:46:42] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.370s
[2025-09-07 00:46:51] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.377s
[2025-09-07 00:47:01] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.392s
[2025-09-07 00:47:10] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.342s
[2025-09-07 00:47:19] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.372s
[2025-09-07 00:47:29] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.385s
[2025-09-07 00:47:38] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.379s
[2025-09-07 00:47:47] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.349s
[2025-09-07 00:47:57] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.375s
[2025-09-07 00:48:06] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.340s
[2025-09-07 00:48:15] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.376s
[2025-09-07 00:48:25] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.378s
[2025-09-07 00:48:34] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.347s
[2025-09-07 00:48:44] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.390s
[2025-09-07 00:48:53] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.347s
[2025-09-07 00:49:02] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.344s
[2025-09-07 00:49:12] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.386s
[2025-09-07 00:49:21] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.348s
[2025-09-07 00:49:30] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.349s
[2025-09-07 00:49:40] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.352s
[2025-09-07 00:49:49] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.381s
[2025-09-07 00:49:58] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.355s
[2025-09-07 00:50:08] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.383s
[2025-09-07 00:50:17] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.378s
[2025-09-07 00:50:27] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.393s
[2025-09-07 00:50:36] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.382s
[2025-09-07 00:50:45] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.377s
[2025-09-07 00:50:55] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.382s
[2025-09-07 00:51:04] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.376s
[2025-09-07 00:51:14] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.345s
[2025-09-07 00:51:23] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.342s
[2025-09-07 00:51:32] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.375s
[2025-09-07 00:51:42] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.377s
[2025-09-07 00:51:51] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.344s
[2025-09-07 00:52:00] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.384s
[2025-09-07 00:52:10] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.363s
[2025-09-07 00:52:19] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.380s
[2025-09-07 00:52:29] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.378s
[2025-09-07 00:52:38] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.383s
[2025-09-07 00:52:47] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.382s
[2025-09-07 00:52:57] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.381s
[2025-09-07 00:53:06] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.376s
[2025-09-07 00:53:15] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.381s
[2025-09-07 00:53:25] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.378s
[2025-09-07 00:53:34] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.347s
[2025-09-07 00:53:44] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.383s
[2025-09-07 00:53:53] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.368s
[2025-09-07 00:54:02] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.368s
[2025-09-07 00:54:12] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.378s
[2025-09-07 00:54:21] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.335s
[2025-09-07 00:54:30] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.382s
[2025-09-07 00:54:40] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.350s
[2025-09-07 00:54:49] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.381s
[2025-09-07 00:54:59] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.378s
[2025-09-07 00:55:08] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.380s
[2025-09-07 00:55:17] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.382s
[2025-09-07 00:55:27] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.383s
[2025-09-07 00:55:36] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.392s
[2025-09-07 00:55:45] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.381s
[2025-09-07 00:55:55] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.378s
[2025-09-07 00:56:04] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.342s
[2025-09-07 00:56:14] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.381s
[2025-09-07 00:56:14] 自旋相关函数计算完成,总耗时 605.98 秒
[2025-09-07 00:56:15] 计算傅里叶变换...
[2025-09-07 00:56:16] 自旋结构因子计算完成
[2025-09-07 00:56:17] 自旋相关函数平均误差: 0.000660
