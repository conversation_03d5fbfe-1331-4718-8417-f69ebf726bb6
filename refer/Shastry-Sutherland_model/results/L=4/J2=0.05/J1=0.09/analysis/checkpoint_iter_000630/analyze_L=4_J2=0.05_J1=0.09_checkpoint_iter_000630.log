[2025-09-06 23:50:23] 使用checkpoint文件: results/L=4/J2=0.05/J1=0.09/training/checkpoints/checkpoint_iter_000630.pkl
[2025-09-06 23:50:35] ✓ 从checkpoint加载参数: 630
[2025-09-06 23:50:35]   - 能量: -53.481605-0.000997j ± 0.081528
[2025-09-06 23:50:35] ================================================================================
[2025-09-06 23:50:35] 加载量子态: L=4, J2=0.05, J1=0.09, checkpoint=checkpoint_iter_000630
[2025-09-06 23:50:35] 使用采样数目: 1048576
[2025-09-06 23:50:35] 设置样本数为: 1048576
[2025-09-06 23:50:35] 开始生成共享样本集...
[2025-09-06 23:53:34] 样本生成完成,耗时: 178.318 秒
[2025-09-06 23:53:34] ================================================================================
[2025-09-06 23:53:34] 开始计算自旋结构因子...
[2025-09-06 23:53:34] 初始化操作符缓存...
[2025-09-06 23:53:34] 预构建所有自旋相关操作符...
[2025-09-06 23:53:34] 开始计算自旋相关函数...
[2025-09-06 23:53:44] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 10.697s
[2025-09-06 23:53:58] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 13.955s
[2025-09-06 23:54:08] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.394s
[2025-09-06 23:54:17] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.392s
[2025-09-06 23:54:27] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.379s
[2025-09-06 23:54:36] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.386s
[2025-09-06 23:54:45] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.378s
[2025-09-06 23:54:55] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.383s
[2025-09-06 23:55:04] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.380s
[2025-09-06 23:55:13] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.385s
[2025-09-06 23:55:23] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.380s
[2025-09-06 23:55:32] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.388s
[2025-09-06 23:55:42] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.387s
[2025-09-06 23:55:51] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.379s
[2025-09-06 23:56:00] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.391s
[2025-09-06 23:56:10] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.383s
[2025-09-06 23:56:19] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.388s
[2025-09-06 23:56:29] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.386s
[2025-09-06 23:56:38] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.380s
[2025-09-06 23:56:47] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.386s
[2025-09-06 23:56:57] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.381s
[2025-09-06 23:57:06] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.385s
[2025-09-06 23:57:16] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.382s
[2025-09-06 23:57:25] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.383s
[2025-09-06 23:57:34] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.377s
[2025-09-06 23:57:44] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.388s
[2025-09-06 23:57:53] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.394s
[2025-09-06 23:58:02] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.382s
[2025-09-06 23:58:12] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.381s
[2025-09-06 23:58:21] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.388s
[2025-09-06 23:58:31] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.375s
[2025-09-06 23:58:40] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.387s
[2025-09-06 23:58:49] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.380s
[2025-09-06 23:58:59] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.383s
[2025-09-06 23:59:08] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.378s
[2025-09-06 23:59:18] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.386s
[2025-09-06 23:59:27] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.384s
[2025-09-06 23:59:36] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.381s
[2025-09-06 23:59:46] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.383s
[2025-09-06 23:59:55] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.388s
[2025-09-07 00:00:05] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.388s
[2025-09-07 00:00:14] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.380s
[2025-09-07 00:00:23] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.389s
[2025-09-07 00:00:33] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.383s
[2025-09-07 00:00:42] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.375s
[2025-09-07 00:00:51] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.381s
[2025-09-07 00:01:01] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.385s
[2025-09-07 00:01:10] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.373s
[2025-09-07 00:01:20] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.387s
[2025-09-07 00:01:29] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.387s
[2025-09-07 00:01:38] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.382s
[2025-09-07 00:01:48] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.394s
[2025-09-07 00:01:57] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.377s
[2025-09-07 00:02:07] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.350s
[2025-09-07 00:02:16] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.378s
[2025-09-07 00:02:25] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.388s
[2025-09-07 00:02:35] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.379s
[2025-09-07 00:02:44] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.380s
[2025-09-07 00:02:53] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.376s
[2025-09-07 00:03:03] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.380s
[2025-09-07 00:03:12] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.377s
[2025-09-07 00:03:22] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.371s
[2025-09-07 00:03:31] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.376s
[2025-09-07 00:03:40] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.387s
[2025-09-07 00:03:40] 自旋相关函数计算完成,总耗时 606.61 秒
[2025-09-07 00:03:41] 计算傅里叶变换...
[2025-09-07 00:03:43] 自旋结构因子计算完成
[2025-09-07 00:03:44] 自旋相关函数平均误差: 0.000663
