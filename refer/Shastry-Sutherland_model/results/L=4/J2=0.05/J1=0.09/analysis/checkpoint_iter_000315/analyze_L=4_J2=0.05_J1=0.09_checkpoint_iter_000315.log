[2025-09-06 23:09:56] 使用checkpoint文件: results/L=4/J2=0.05/J1=0.09/training/checkpoints/checkpoint_iter_000315.pkl
[2025-09-06 23:10:10] ✓ 从checkpoint加载参数: 315
[2025-09-06 23:10:10]   - 能量: -53.703156+0.000977j ± 0.078822
[2025-09-06 23:10:10] ================================================================================
[2025-09-06 23:10:10] 加载量子态: L=4, J2=0.05, J1=0.09, checkpoint=checkpoint_iter_000315
[2025-09-06 23:10:10] 使用采样数目: 1048576
[2025-09-06 23:10:10] 设置样本数为: 1048576
[2025-09-06 23:10:10] 开始生成共享样本集...
[2025-09-06 23:13:10] 样本生成完成,耗时: 180.120 秒
[2025-09-06 23:13:10] ================================================================================
[2025-09-06 23:13:10] 开始计算自旋结构因子...
[2025-09-06 23:13:10] 初始化操作符缓存...
[2025-09-06 23:13:10] 预构建所有自旋相关操作符...
[2025-09-06 23:13:10] 开始计算自旋相关函数...
[2025-09-06 23:13:21] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 10.770s
[2025-09-06 23:13:35] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 13.954s
[2025-09-06 23:13:44] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.320s
[2025-09-06 23:13:54] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.320s
[2025-09-06 23:14:03] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.323s
[2025-09-06 23:14:12] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.310s
[2025-09-06 23:14:22] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.315s
[2025-09-06 23:14:31] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.316s
[2025-09-06 23:14:40] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.321s
[2025-09-06 23:14:50] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.322s
[2025-09-06 23:14:59] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.325s
[2025-09-06 23:15:08] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.312s
[2025-09-06 23:15:18] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.331s
[2025-09-06 23:15:27] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.312s
[2025-09-06 23:15:36] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.321s
[2025-09-06 23:15:45] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.323s
[2025-09-06 23:15:55] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.321s
[2025-09-06 23:16:04] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.321s
[2025-09-06 23:16:13] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.324s
[2025-09-06 23:16:23] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.310s
[2025-09-06 23:16:32] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.311s
[2025-09-06 23:16:41] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.320s
[2025-09-06 23:16:51] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.326s
[2025-09-06 23:17:00] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.300s
[2025-09-06 23:17:09] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.327s
[2025-09-06 23:17:19] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.323s
[2025-09-06 23:17:28] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.318s
[2025-09-06 23:17:37] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.320s
[2025-09-06 23:17:47] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.325s
[2025-09-06 23:17:56] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.319s
[2025-09-06 23:18:05] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.320s
[2025-09-06 23:18:15] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.311s
[2025-09-06 23:18:24] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.309s
[2025-09-06 23:18:33] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.323s
[2025-09-06 23:18:43] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.324s
[2025-09-06 23:18:52] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.315s
[2025-09-06 23:19:01] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.333s
[2025-09-06 23:19:11] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.319s
[2025-09-06 23:19:20] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.321s
[2025-09-06 23:19:29] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.317s
[2025-09-06 23:19:39] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.321s
[2025-09-06 23:19:48] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.323s
[2025-09-06 23:19:57] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.321s
[2025-09-06 23:20:07] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.321s
[2025-09-06 23:20:16] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.321s
[2025-09-06 23:20:25] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.316s
[2025-09-06 23:20:34] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.317s
[2025-09-06 23:20:44] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.324s
[2025-09-06 23:20:53] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.319s
[2025-09-06 23:21:02] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.325s
[2025-09-06 23:21:12] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.324s
[2025-09-06 23:21:21] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.314s
[2025-09-06 23:21:30] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.304s
[2025-09-06 23:21:40] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.314s
[2025-09-06 23:21:49] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.323s
[2025-09-06 23:21:58] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.312s
[2025-09-06 23:22:08] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.322s
[2025-09-06 23:22:17] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.322s
[2025-09-06 23:22:26] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.317s
[2025-09-06 23:22:36] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.325s
[2025-09-06 23:22:45] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.336s
[2025-09-06 23:22:54] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.322s
[2025-09-06 23:23:04] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.320s
[2025-09-06 23:23:13] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.320s
[2025-09-06 23:23:13] 自旋相关函数计算完成,总耗时 602.80 秒
[2025-09-06 23:23:14] 计算傅里叶变换...
[2025-09-06 23:23:16] 自旋结构因子计算完成
[2025-09-06 23:23:17] 自旋相关函数平均误差: 0.000660
