[2025-09-05 03:56:25] ✓ 从checkpoint恢复: results/L=4/J2=0.05/J1=0.08/training/checkpoints/final_GCNN.pkl
[2025-09-05 03:56:25]   - 迭代次数: final
[2025-09-05 03:56:25]   - 能量: -53.392012+0.002581j ± 0.080707
[2025-09-05 03:56:25]   - 时间戳: 2025-09-05T03:55:54.904826+08:00
[2025-09-05 03:56:34] ✓ 变分状态参数已从checkpoint恢复
[2025-09-05 03:56:34] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-05 03:56:34] ==================================================
[2025-09-05 03:56:34] GCNN for Shastry-Sutherland Model
[2025-09-05 03:56:34] ==================================================
[2025-09-05 03:56:34] System parameters:
[2025-09-05 03:56:34]   - System size: L=4, N=64
[2025-09-05 03:56:34]   - System parameters: J1=0.09, J2=0.05, Q=0.95
[2025-09-05 03:56:34] --------------------------------------------------
[2025-09-05 03:56:34] Model parameters:
[2025-09-05 03:56:34]   - Number of layers = 4
[2025-09-05 03:56:34]   - Number of features = 4
[2025-09-05 03:56:34]   - Total parameters = 12572
[2025-09-05 03:56:34] --------------------------------------------------
[2025-09-05 03:56:34] Training parameters:
[2025-09-05 03:56:34]   - Learning rate: 0.015
[2025-09-05 03:56:34]   - Total iterations: 1050
[2025-09-05 03:56:34]   - Annealing cycles: 3
[2025-09-05 03:56:34]   - Initial period: 150
[2025-09-05 03:56:34]   - Period multiplier: 2.0
[2025-09-05 03:56:34]   - Temperature range: 0.0-1.0
[2025-09-05 03:56:34]   - Samples: 4096
[2025-09-05 03:56:34]   - Discarded samples: 0
[2025-09-05 03:56:34]   - Chunk size: 2048
[2025-09-05 03:56:34]   - Diagonal shift: 0.2
[2025-09-05 03:56:34]   - Gradient clipping: 1.0
[2025-09-05 03:56:34]   - Checkpoint enabled: interval=105
[2025-09-05 03:56:34]   - Checkpoint directory: results/L=4/J2=0.05/J1=0.09/training/checkpoints
[2025-09-05 03:56:34] --------------------------------------------------
[2025-09-05 03:56:34] Device status:
[2025-09-05 03:56:34]   - Devices model: NVIDIA H200 NVL
[2025-09-05 03:56:34]   - Number of devices: 1
[2025-09-05 03:56:34]   - Sharding: True
[2025-09-05 03:56:34] ============================================================
[2025-09-05 03:57:15] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -53.738424+0.002547j
[2025-09-05 03:57:40] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -53.713386-0.000719j
[2025-09-05 03:57:50] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -53.730872+0.002736j
[2025-09-05 03:58:00] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -53.850689+0.002052j
[2025-09-05 03:58:10] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -53.726917-0.003472j
[2025-09-05 03:58:20] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -53.945967+0.002389j
[2025-09-05 03:58:30] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -53.942970+0.000628j
[2025-09-05 03:58:41] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -53.889033-0.001720j
[2025-09-05 03:58:51] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -53.870829+0.001030j
[2025-09-05 03:59:01] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -53.802613-0.000085j
[2025-09-05 03:59:11] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -53.794879-0.002630j
[2025-09-05 03:59:21] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -53.843545+0.001577j
[2025-09-05 03:59:31] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -53.699861-0.002264j
[2025-09-05 03:59:42] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -53.685768-0.000515j
[2025-09-05 03:59:52] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -53.753530+0.001177j
[2025-09-05 04:00:02] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -53.712636+0.000203j
[2025-09-05 04:00:12] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -53.793462-0.002919j
[2025-09-05 04:00:22] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -53.519823-0.002566j
[2025-09-05 04:00:32] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -53.636930+0.000421j
[2025-09-05 04:00:43] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -53.674122-0.001542j
[2025-09-05 04:00:53] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -53.774796-0.000093j
[2025-09-05 04:01:03] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -53.754854-0.002451j
[2025-09-05 04:01:13] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -53.733887-0.000252j
[2025-09-05 04:01:23] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -53.699412+0.002759j
[2025-09-05 04:01:33] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -53.769543-0.002629j
[2025-09-05 04:01:43] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -53.813063-0.001528j
[2025-09-05 04:01:54] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -53.671227+0.000589j
[2025-09-05 04:02:04] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -53.737180-0.001549j
[2025-09-05 04:02:14] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -53.787488-0.001595j
[2025-09-05 04:02:24] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -53.713907+0.000716j
[2025-09-05 04:02:34] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -53.732369-0.001859j
[2025-09-05 04:02:44] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -53.616480+0.000334j
[2025-09-05 04:02:55] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -53.676545+0.001828j
[2025-09-05 04:03:05] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -53.741982-0.001465j
[2025-09-05 04:03:15] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -53.740102+0.001152j
[2025-09-05 04:03:25] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -53.835672+0.000754j
[2025-09-05 04:03:35] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -53.764808-0.000619j
[2025-09-05 04:03:45] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -53.889406+0.004672j
[2025-09-05 04:03:56] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -53.809247-0.001102j
[2025-09-05 04:04:06] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -53.731937-0.005566j
[2025-09-05 04:04:16] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -53.741054-0.000638j
[2025-09-05 04:04:26] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -53.773126+0.001703j
[2025-09-05 04:04:36] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -53.843913+0.000658j
[2025-09-05 04:04:46] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -53.720731+0.000220j
[2025-09-05 04:04:57] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -53.727068-0.000678j
[2025-09-05 04:05:07] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -53.872728+0.000219j
[2025-09-05 04:05:17] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -53.782152+0.000315j
[2025-09-05 04:05:27] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -53.837687-0.004993j
[2025-09-05 04:05:37] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -53.721561+0.001455j
[2025-09-05 04:05:47] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -53.815867-0.002059j
[2025-09-05 04:05:57] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -53.791397+0.001231j
[2025-09-05 04:06:08] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -53.723704-0.000084j
[2025-09-05 04:06:18] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -53.714234-0.002085j
[2025-09-05 04:06:28] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -53.788029+0.001480j
[2025-09-05 04:06:38] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -53.662031-0.002227j
[2025-09-05 04:06:48] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -53.770420-0.002357j
[2025-09-05 04:06:58] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -53.762649+0.005298j
[2025-09-05 04:07:08] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -53.876958-0.001534j
[2025-09-05 04:07:19] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -53.827177+0.000756j
[2025-09-05 04:07:29] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -53.708048-0.003745j
[2025-09-05 04:07:39] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -53.724679-0.000258j
[2025-09-05 04:07:49] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -53.835023-0.002554j
[2025-09-05 04:07:59] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -53.640182-0.000618j
[2025-09-05 04:08:09] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -53.825457-0.004587j
[2025-09-05 04:08:20] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -53.815327-0.000795j
[2025-09-05 04:08:30] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -53.812280+0.002364j
[2025-09-05 04:08:40] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -53.720992+0.002406j
[2025-09-05 04:08:50] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -53.707269-0.005735j
[2025-09-05 04:09:00] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -53.731296-0.000818j
[2025-09-05 04:09:10] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -53.833487+0.000764j
[2025-09-05 04:09:21] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -53.849369+0.001186j
[2025-09-05 04:09:31] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -53.791062-0.000862j
[2025-09-05 04:09:41] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -53.687216-0.001254j
[2025-09-05 04:09:51] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -53.729503-0.003262j
[2025-09-05 04:10:01] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -53.708238-0.000917j
[2025-09-05 04:10:11] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -53.768521+0.001039j
[2025-09-05 04:10:22] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -53.727560+0.001324j
[2025-09-05 04:10:32] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -53.804888-0.001334j
[2025-09-05 04:10:42] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -53.841166+0.000091j
[2025-09-05 04:10:52] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -53.695046-0.002145j
[2025-09-05 04:11:02] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -53.752022-0.002184j
[2025-09-05 04:11:12] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -53.686054-0.001017j
[2025-09-05 04:11:22] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -53.700460-0.001507j
[2025-09-05 04:11:33] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -53.833692-0.000149j
[2025-09-05 04:11:43] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -53.677370-0.002037j
[2025-09-05 04:11:53] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -53.851649+0.003337j
[2025-09-05 04:12:03] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -53.789974+0.001268j
[2025-09-05 04:12:13] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -53.672332+0.000927j
[2025-09-05 04:12:23] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -53.751866+0.001107j
[2025-09-05 04:12:34] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -53.800929+0.001706j
[2025-09-05 04:12:44] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -53.812769+0.001317j
[2025-09-05 04:12:54] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -53.725138+0.000794j
[2025-09-05 04:13:04] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -53.703456+0.000871j
[2025-09-05 04:13:14] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -53.659881+0.001519j
[2025-09-05 04:13:25] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -53.643524-0.000636j
[2025-09-05 04:13:35] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -53.658039+0.000216j
[2025-09-05 04:13:45] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -53.805750+0.000942j
[2025-09-05 04:13:55] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -53.646989+0.002395j
[2025-09-05 04:14:05] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -53.735871-0.001440j
[2025-09-05 04:14:15] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -53.703027-0.002398j
[2025-09-05 04:14:26] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -53.709137+0.000265j
[2025-09-05 04:14:36] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -53.683675+0.001858j
[2025-09-05 04:14:46] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -53.665934-0.000273j
[2025-09-05 04:14:56] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -53.692916+0.002134j
[2025-09-05 04:15:06] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -53.793751-0.001683j
[2025-09-05 04:15:06] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-05 04:15:17] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -53.789524-0.000555j
[2025-09-05 04:15:27] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -53.599115+0.001354j
[2025-09-05 04:15:37] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -53.716711+0.000028j
[2025-09-05 04:15:47] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -53.749690+0.000060j
[2025-09-05 04:15:57] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -53.821651-0.004399j
[2025-09-05 04:16:08] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -53.810415+0.001780j
[2025-09-05 04:16:18] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -53.832512+0.002278j
[2025-09-05 04:16:28] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -53.725878+0.000731j
[2025-09-05 04:16:38] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -53.804082-0.000182j
[2025-09-05 04:16:48] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -53.677497-0.002655j
[2025-09-05 04:16:59] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -53.656929+0.003690j
[2025-09-05 04:17:09] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -53.668233-0.000498j
[2025-09-05 04:17:19] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -53.664987-0.001316j
[2025-09-05 04:17:29] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -53.731695+0.001613j
[2025-09-05 04:17:39] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -53.760994+0.005569j
[2025-09-05 04:17:50] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -53.620645+0.000024j
[2025-09-05 04:18:00] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -53.669440+0.000943j
[2025-09-05 04:18:10] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -53.648155-0.001246j
[2025-09-05 04:18:20] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -53.701394+0.000971j
[2025-09-05 04:18:30] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -53.802738-0.002019j
[2025-09-05 04:18:40] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -53.922534-0.000786j
[2025-09-05 04:18:51] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -53.778396+0.002830j
[2025-09-05 04:19:01] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -53.795104-0.000851j
[2025-09-05 04:19:11] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -53.768760+0.003013j
[2025-09-05 04:19:21] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -53.697795+0.000474j
[2025-09-05 04:19:31] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -53.758889-0.002219j
[2025-09-05 04:19:42] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -53.794812+0.002817j
[2025-09-05 04:19:52] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -53.730006+0.002008j
[2025-09-05 04:20:02] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -53.735255+0.000872j
[2025-09-05 04:20:12] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -53.732911+0.003711j
[2025-09-05 04:20:22] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -53.694671+0.001646j
[2025-09-05 04:20:33] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -53.792701-0.002047j
[2025-09-05 04:20:43] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -53.660215-0.002991j
[2025-09-05 04:20:53] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -53.921292+0.001868j
[2025-09-05 04:21:03] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -53.820110-0.001754j
[2025-09-05 04:21:13] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -53.805617+0.000083j
[2025-09-05 04:21:24] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -53.667248-0.001439j
[2025-09-05 04:21:34] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -53.686511-0.002915j
[2025-09-05 04:21:44] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -53.830295-0.001760j
[2025-09-05 04:21:54] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -53.842325-0.000444j
[2025-09-05 04:22:04] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -53.872722+0.003446j
[2025-09-05 04:22:15] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -53.829345+0.001388j
[2025-09-05 04:22:25] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -53.764148+0.001361j
[2025-09-05 04:22:35] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -53.721541-0.002392j
[2025-09-05 04:22:45] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -53.882792-0.000358j
[2025-09-05 04:22:45] RESTART #1 | Period: 300
[2025-09-05 04:22:55] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -53.721261+0.000363j
[2025-09-05 04:23:05] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -53.579778+0.002840j
[2025-09-05 04:23:16] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -53.705391-0.002621j
[2025-09-05 04:23:26] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -53.736232+0.000637j
[2025-09-05 04:23:36] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -53.572139-0.001698j
[2025-09-05 04:23:46] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -53.651614+0.001192j
[2025-09-05 04:23:56] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -53.730688-0.000555j
[2025-09-05 04:24:07] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -53.793491-0.000514j
[2025-09-05 04:24:17] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -53.732748-0.001547j
[2025-09-05 04:24:27] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -53.754407-0.000103j
[2025-09-05 04:24:37] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -53.590312-0.000143j
[2025-09-05 04:24:47] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -53.648791+0.000456j
[2025-09-05 04:24:57] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -53.699862+0.002114j
[2025-09-05 04:25:08] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -53.564045+0.001515j
[2025-09-05 04:25:18] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -53.621816+0.003203j
[2025-09-05 04:25:28] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -53.634106+0.000217j
[2025-09-05 04:25:38] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -53.656422-0.000881j
[2025-09-05 04:25:49] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -53.750191+0.000144j
[2025-09-05 04:25:59] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -53.803455-0.002144j
[2025-09-05 04:26:09] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -53.824618+0.002369j
[2025-09-05 04:26:19] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -53.843806-0.000334j
[2025-09-05 04:26:29] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -53.845585-0.000723j
[2025-09-05 04:26:39] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -53.705108+0.000785j
[2025-09-05 04:26:50] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -53.684420-0.005598j
[2025-09-05 04:27:00] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -53.665630-0.000291j
[2025-09-05 04:27:10] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -53.610276+0.000910j
[2025-09-05 04:27:20] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -53.684748+0.000287j
[2025-09-05 04:27:30] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -53.628880-0.002874j
[2025-09-05 04:27:41] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -53.717551-0.006188j
[2025-09-05 04:27:51] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -53.705300-0.002329j
[2025-09-05 04:28:01] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -53.753685+0.002486j
[2025-09-05 04:28:11] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -53.806256+0.000131j
[2025-09-05 04:28:21] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -53.688958-0.001883j
[2025-09-05 04:28:32] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -53.722582-0.001254j
[2025-09-05 04:28:42] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -53.889932-0.002699j
[2025-09-05 04:28:52] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -53.821378+0.000217j
[2025-09-05 04:29:02] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -53.948117-0.001316j
[2025-09-05 04:29:12] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -53.862830-0.001847j
[2025-09-05 04:29:22] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -53.914103+0.001617j
[2025-09-05 04:29:33] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -53.933336+0.002430j
[2025-09-05 04:29:43] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -53.848576-0.002000j
[2025-09-05 04:29:53] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -53.869822+0.002539j
[2025-09-05 04:30:03] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -53.736515+0.000721j
[2025-09-05 04:30:13] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -53.728050+0.000459j
[2025-09-05 04:30:24] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -53.827020+0.001550j
[2025-09-05 04:30:34] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -53.831215-0.001526j
[2025-09-05 04:30:44] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -53.891464+0.000647j
[2025-09-05 04:30:54] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -53.882122-0.004575j
[2025-09-05 04:31:04] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -53.787017-0.000846j
[2025-09-05 04:31:15] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -53.749058+0.001182j
[2025-09-05 04:31:25] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -53.752758+0.004372j
[2025-09-05 04:31:35] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -53.824968+0.001267j
[2025-09-05 04:31:45] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -53.802049-0.000765j
[2025-09-05 04:31:55] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -53.865476+0.002817j
[2025-09-05 04:32:06] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -53.788568-0.001455j
[2025-09-05 04:32:16] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -53.853883+0.001921j
[2025-09-05 04:32:26] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -53.794884-0.003017j
[2025-09-05 04:32:36] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -53.752550-0.001910j
[2025-09-05 04:32:46] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -53.805228-0.002240j
[2025-09-05 04:32:56] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -53.781561-0.000644j
[2025-09-05 04:32:56] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-05 04:33:07] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -53.755585+0.000458j
[2025-09-05 04:33:17] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -53.740096+0.001413j
[2025-09-05 04:33:27] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -53.675768-0.000222j
[2025-09-05 04:33:37] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -53.675038-0.001167j
[2025-09-05 04:33:47] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -53.595341-0.001159j
[2025-09-05 04:33:58] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -53.615525+0.001225j
[2025-09-05 04:34:08] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -53.649507+0.001342j
[2025-09-05 04:34:18] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -53.797958-0.004338j
[2025-09-05 04:34:28] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -53.755185-0.004948j
[2025-09-05 04:34:38] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -53.669821-0.001887j
[2025-09-05 04:34:49] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -53.656572-0.000729j
[2025-09-05 04:34:59] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -53.633274-0.001413j
[2025-09-05 04:35:09] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -53.513551-0.003211j
[2025-09-05 04:35:19] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -53.604322+0.003760j
[2025-09-05 04:35:29] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -53.720535+0.001438j
[2025-09-05 04:35:40] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -53.624611+0.001134j
[2025-09-05 04:35:50] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -53.602975-0.001177j
[2025-09-05 04:36:00] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -53.682190+0.002632j
[2025-09-05 04:36:10] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -53.638543+0.004415j
[2025-09-05 04:36:20] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -53.703660+0.000194j
[2025-09-05 04:36:31] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -53.770171+0.000763j
[2025-09-05 04:36:41] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -53.782399+0.000320j
[2025-09-05 04:36:51] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -53.670635+0.000102j
[2025-09-05 04:37:01] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -53.817676+0.001074j
[2025-09-05 04:37:11] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -53.892661+0.001683j
[2025-09-05 04:37:22] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -53.790530-0.000485j
[2025-09-05 04:37:32] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -53.899609-0.000528j
[2025-09-05 04:37:42] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -53.888974-0.000892j
[2025-09-05 04:37:52] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -53.780595+0.001792j
[2025-09-05 04:38:02] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -53.771248+0.002200j
[2025-09-05 04:38:13] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -53.915548+0.002391j
[2025-09-05 04:38:23] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -53.770833+0.002277j
[2025-09-05 04:38:33] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -53.776276+0.000408j
[2025-09-05 04:38:43] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -53.765008-0.001147j
[2025-09-05 04:38:53] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -53.610418-0.001158j
[2025-09-05 04:39:04] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -53.645646+0.001054j
[2025-09-05 04:39:14] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -53.546472-0.000445j
[2025-09-05 04:39:24] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -53.776769-0.002449j
[2025-09-05 04:39:34] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -53.719565-0.003332j
[2025-09-05 04:39:44] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -53.734570-0.003050j
[2025-09-05 04:39:54] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -53.754758+0.004756j
[2025-09-05 04:40:05] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -53.726198-0.001265j
[2025-09-05 04:40:15] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -53.716817-0.004450j
[2025-09-05 04:40:25] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -53.687939-0.000848j
[2025-09-05 04:40:35] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -53.786390+0.003310j
[2025-09-05 04:40:45] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -53.835592+0.000325j
[2025-09-05 04:40:56] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -53.655096+0.002326j
[2025-09-05 04:41:06] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -53.619948+0.002048j
[2025-09-05 04:41:16] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -53.591070+0.001412j
[2025-09-05 04:41:26] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -53.773656-0.002547j
[2025-09-05 04:41:36] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -53.796227-0.000871j
[2025-09-05 04:41:47] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -53.844753-0.000495j
[2025-09-05 04:41:57] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -53.797698-0.003529j
[2025-09-05 04:42:07] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -53.788405-0.000854j
[2025-09-05 04:42:17] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -53.751905+0.000836j
[2025-09-05 04:42:27] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -53.789632+0.000909j
[2025-09-05 04:42:38] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -53.790845-0.000234j
[2025-09-05 04:42:48] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -53.849519+0.004214j
[2025-09-05 04:42:58] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -53.837627-0.004005j
[2025-09-05 04:43:08] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -53.715822+0.000151j
[2025-09-05 04:43:18] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -53.655537-0.001308j
[2025-09-05 04:43:29] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -53.746648-0.000670j
[2025-09-05 04:43:39] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -53.640591+0.001537j
[2025-09-05 04:43:49] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -53.708319-0.000045j
[2025-09-05 04:43:59] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -53.814774+0.002719j
[2025-09-05 04:44:09] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -53.889279-0.002165j
[2025-09-05 04:44:20] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -53.692298+0.004473j
[2025-09-05 04:44:30] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -53.754586+0.001078j
[2025-09-05 04:44:40] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -53.696000-0.003097j
[2025-09-05 04:44:50] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -53.556321-0.000872j
[2025-09-05 04:45:00] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -53.724735+0.000949j
[2025-09-05 04:45:11] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -53.586207-0.001959j
[2025-09-05 04:45:21] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -53.534279+0.000211j
[2025-09-05 04:45:31] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -53.545950+0.002152j
[2025-09-05 04:45:41] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -53.646326-0.000829j
[2025-09-05 04:45:51] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -53.672278+0.006275j
[2025-09-05 04:46:02] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -53.601004+0.000127j
[2025-09-05 04:46:12] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -53.710821+0.001991j
[2025-09-05 04:46:22] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -53.801865+0.002292j
[2025-09-05 04:46:32] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -53.782132-0.001217j
[2025-09-05 04:46:42] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -54.014159+0.000862j
[2025-09-05 04:46:53] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -53.858820-0.003911j
[2025-09-05 04:47:03] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -53.766632+0.001419j
[2025-09-05 04:47:13] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -53.708499+0.001973j
[2025-09-05 04:47:23] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -53.787918+0.005057j
[2025-09-05 04:47:33] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -53.780289-0.000949j
[2025-09-05 04:47:43] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -53.808532+0.002842j
[2025-09-05 04:47:54] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -53.805951+0.002376j
[2025-09-05 04:48:04] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -53.626249+0.001689j
[2025-09-05 04:48:14] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -53.582797+0.000171j
[2025-09-05 04:48:24] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -53.723104-0.002724j
[2025-09-05 04:48:34] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -53.682980+0.004035j
[2025-09-05 04:48:45] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -53.601629+0.001763j
[2025-09-05 04:48:55] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -53.674199+0.000269j
[2025-09-05 04:49:05] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -53.593529+0.001802j
[2025-09-05 04:49:15] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -53.779311+0.000142j
[2025-09-05 04:49:25] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -53.674382-0.001734j
[2025-09-05 04:49:36] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -53.713713-0.000441j
[2025-09-05 04:49:46] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -53.761482-0.000067j
[2025-09-05 04:49:56] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -53.840534-0.002700j
[2025-09-05 04:50:06] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -53.685812-0.003766j
[2025-09-05 04:50:16] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -53.762887+0.000186j
[2025-09-05 04:50:27] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -53.656021+0.000176j
[2025-09-05 04:50:37] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -53.737858-0.001924j
[2025-09-05 04:50:47] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -53.703156+0.000977j
[2025-09-05 04:50:47] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-05 04:50:57] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -53.753134-0.000128j
[2025-09-05 04:51:07] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -53.832977+0.001939j
[2025-09-05 04:51:17] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -53.790605+0.002277j
[2025-09-05 04:51:28] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -53.594680+0.001416j
[2025-09-05 04:51:38] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -53.724859-0.002997j
[2025-09-05 04:51:48] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -53.843046+0.000295j
[2025-09-05 04:51:58] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -53.796058-0.001589j
[2025-09-05 04:52:08] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -53.908001-0.002076j
[2025-09-05 04:52:19] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -53.854862-0.001212j
[2025-09-05 04:52:29] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -53.820827+0.001933j
[2025-09-05 04:52:39] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -53.631096+0.000053j
[2025-09-05 04:52:49] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -53.815427+0.002026j
[2025-09-05 04:52:59] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -53.742770-0.002894j
[2025-09-05 04:53:10] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -53.803990-0.000757j
[2025-09-05 04:53:20] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -53.652265-0.000065j
[2025-09-05 04:53:30] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -53.656398-0.002271j
[2025-09-05 04:53:40] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -53.628935-0.000268j
[2025-09-05 04:53:50] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -53.688467+0.003097j
[2025-09-05 04:54:01] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -53.777989+0.001428j
[2025-09-05 04:54:11] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -53.778557-0.005266j
[2025-09-05 04:54:21] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -53.727938-0.002741j
[2025-09-05 04:54:31] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -53.736724-0.001072j
[2025-09-05 04:54:41] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -53.662322+0.000769j
[2025-09-05 04:54:52] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -53.785752-0.001268j
[2025-09-05 04:55:02] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -53.731377-0.006140j
[2025-09-05 04:55:12] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -53.704380+0.000121j
[2025-09-05 04:55:22] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -53.861215+0.000924j
[2025-09-05 04:55:32] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -53.927153-0.000479j
[2025-09-05 04:55:42] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -53.827285+0.001153j
[2025-09-05 04:55:53] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -53.802490-0.001319j
[2025-09-05 04:56:03] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -53.762081+0.002506j
[2025-09-05 04:56:13] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -53.692088+0.000416j
[2025-09-05 04:56:23] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -53.817306-0.001561j
[2025-09-05 04:56:33] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -53.857352+0.000293j
[2025-09-05 04:56:44] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -53.757875-0.000896j
[2025-09-05 04:56:54] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -53.656611-0.001412j
[2025-09-05 04:57:04] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -53.503960-0.000310j
[2025-09-05 04:57:14] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -53.493599-0.001726j
[2025-09-05 04:57:24] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -53.589494+0.002115j
[2025-09-05 04:57:35] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -53.799132-0.000336j
[2025-09-05 04:57:45] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -53.761459+0.002746j
[2025-09-05 04:57:55] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -53.710279-0.003545j
[2025-09-05 04:58:05] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -53.678562-0.000865j
[2025-09-05 04:58:15] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -53.669809+0.000487j
[2025-09-05 04:58:26] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -53.683047+0.000177j
[2025-09-05 04:58:36] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -53.713636-0.001625j
[2025-09-05 04:58:46] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -53.697975-0.000099j
[2025-09-05 04:58:56] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -53.761035-0.000605j
[2025-09-05 04:59:06] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -53.688682-0.000083j
[2025-09-05 04:59:17] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -53.737652-0.002630j
[2025-09-05 04:59:27] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -53.688561-0.001988j
[2025-09-05 04:59:37] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -53.608198+0.002505j
[2025-09-05 04:59:47] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -53.551875+0.000082j
[2025-09-05 04:59:57] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -53.609869-0.000784j
[2025-09-05 05:00:08] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -53.600327-0.001049j
[2025-09-05 05:00:18] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -53.645646+0.001611j
[2025-09-05 05:00:28] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -53.719919+0.000929j
[2025-09-05 05:00:38] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -53.677630-0.000580j
[2025-09-05 05:00:48] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -53.676505-0.000747j
[2025-09-05 05:00:58] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -53.633476+0.001018j
[2025-09-05 05:01:09] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -53.567082+0.004488j
[2025-09-05 05:01:19] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -53.581399+0.000864j
[2025-09-05 05:01:29] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -53.741553-0.001777j
[2025-09-05 05:01:39] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -53.835222+0.000504j
[2025-09-05 05:01:49] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -53.783499-0.002861j
[2025-09-05 05:02:00] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -53.903373+0.001401j
[2025-09-05 05:02:10] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -53.738242-0.001742j
[2025-09-05 05:02:20] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -53.826104+0.000596j
[2025-09-05 05:02:30] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -53.738430-0.001476j
[2025-09-05 05:02:40] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -53.543680+0.000001j
[2025-09-05 05:02:51] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -53.590139-0.000034j
[2025-09-05 05:03:01] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -53.676663-0.001034j
[2025-09-05 05:03:11] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -53.794066-0.001801j
[2025-09-05 05:03:21] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -53.858664-0.000533j
[2025-09-05 05:03:31] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -53.891535-0.001041j
[2025-09-05 05:03:42] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -53.741573-0.000427j
[2025-09-05 05:03:52] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -53.792316+0.000442j
[2025-09-05 05:04:02] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -53.736999-0.000218j
[2025-09-05 05:04:12] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -53.731448+0.002667j
[2025-09-05 05:04:22] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -53.807879-0.000170j
[2025-09-05 05:04:33] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -53.651210+0.000410j
[2025-09-05 05:04:43] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -53.590818+0.004362j
[2025-09-05 05:04:53] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -53.595738-0.000337j
[2025-09-05 05:05:03] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -53.746410-0.000628j
[2025-09-05 05:05:13] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -53.674570+0.000485j
[2025-09-05 05:05:24] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -53.831356+0.001042j
[2025-09-05 05:05:34] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -53.711107+0.000895j
[2025-09-05 05:05:44] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -53.745833+0.000206j
[2025-09-05 05:05:54] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -53.850502-0.000960j
[2025-09-05 05:06:04] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -53.786096-0.001321j
[2025-09-05 05:06:14] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -53.778809+0.001056j
[2025-09-05 05:06:25] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -53.732048-0.003919j
[2025-09-05 05:06:35] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -53.682406+0.000031j
[2025-09-05 05:06:45] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -53.707381-0.000993j
[2025-09-05 05:06:55] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -53.670369+0.001759j
[2025-09-05 05:07:05] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -53.643434-0.000970j
[2025-09-05 05:07:16] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -53.777305-0.003020j
[2025-09-05 05:07:26] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -53.865039+0.000173j
[2025-09-05 05:07:36] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -53.643326-0.000832j
[2025-09-05 05:07:46] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -53.718829+0.001187j
[2025-09-05 05:07:56] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -53.760897-0.001863j
[2025-09-05 05:08:07] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -53.644686+0.000241j
[2025-09-05 05:08:17] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -53.861343-0.002255j
[2025-09-05 05:08:27] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -53.710145+0.001402j
[2025-09-05 05:08:37] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -53.614202+0.001445j
[2025-09-05 05:08:37] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-05 05:08:47] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -53.675142+0.000706j
[2025-09-05 05:08:57] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -53.800714-0.000264j
[2025-09-05 05:09:08] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -53.881918+0.001122j
[2025-09-05 05:09:18] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -53.880752+0.000403j
[2025-09-05 05:09:28] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -53.866111-0.002967j
[2025-09-05 05:09:38] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -53.818769+0.000005j
[2025-09-05 05:09:48] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -53.712260-0.003506j
[2025-09-05 05:09:59] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -53.627104+0.002171j
[2025-09-05 05:10:09] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -53.733034-0.001736j
[2025-09-05 05:10:19] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -53.708154+0.002900j
[2025-09-05 05:10:29] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -53.758512-0.000407j
[2025-09-05 05:10:39] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -53.760660-0.000091j
[2025-09-05 05:10:50] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -53.777297+0.001277j
[2025-09-05 05:11:00] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -53.704782+0.002537j
[2025-09-05 05:11:10] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -53.684231-0.000019j
[2025-09-05 05:11:20] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -53.640222-0.000440j
[2025-09-05 05:11:30] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -53.716180-0.001082j
[2025-09-05 05:11:41] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -53.657804+0.002810j
[2025-09-05 05:11:51] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -53.666877-0.001418j
[2025-09-05 05:12:01] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -53.636186-0.001870j
[2025-09-05 05:12:11] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -53.578207+0.001699j
[2025-09-05 05:12:21] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -53.663520+0.001358j
[2025-09-05 05:12:32] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -53.747164+0.000282j
[2025-09-05 05:12:42] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -53.921288-0.001568j
[2025-09-05 05:12:52] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -53.828317+0.000871j
[2025-09-05 05:13:02] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -53.878121-0.001029j
[2025-09-05 05:13:12] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -53.779128-0.002894j
[2025-09-05 05:13:23] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -53.742827+0.001625j
[2025-09-05 05:13:33] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -53.779763-0.003958j
[2025-09-05 05:13:43] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -53.877888-0.000351j
[2025-09-05 05:13:43] RESTART #2 | Period: 600
[2025-09-05 05:13:53] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -53.743424-0.003638j
[2025-09-05 05:14:03] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -53.796974+0.002115j
[2025-09-05 05:14:13] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -53.672329+0.001387j
[2025-09-05 05:14:24] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -53.841944-0.001264j
[2025-09-05 05:14:34] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -53.677519-0.000314j
[2025-09-05 05:14:44] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -53.650378+0.002445j
[2025-09-05 05:14:54] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -53.782604-0.004477j
[2025-09-05 05:15:04] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -53.628521-0.001144j
[2025-09-05 05:15:15] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -53.629168+0.002097j
[2025-09-05 05:15:25] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -53.655716-0.000077j
[2025-09-05 05:15:35] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -53.711475-0.000723j
[2025-09-05 05:15:45] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -53.794945+0.000906j
[2025-09-05 05:15:55] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -53.864732+0.001051j
[2025-09-05 05:16:05] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -53.822000-0.000102j
[2025-09-05 05:16:16] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -53.808491-0.003366j
[2025-09-05 05:16:26] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -53.773886-0.000567j
[2025-09-05 05:16:36] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -53.748780+0.000094j
[2025-09-05 05:16:46] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -53.770712+0.001571j
[2025-09-05 05:16:56] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -53.725102+0.002646j
[2025-09-05 05:17:07] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -53.738492+0.000364j
[2025-09-05 05:17:17] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -53.772779+0.002387j
[2025-09-05 05:17:27] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -53.649266+0.000773j
[2025-09-05 05:17:37] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -53.681276+0.004476j
[2025-09-05 05:17:47] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -53.774904-0.001902j
[2025-09-05 05:17:57] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -53.828217-0.001583j
[2025-09-05 05:18:08] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -53.766769-0.002361j
[2025-09-05 05:18:18] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -53.751057-0.001492j
[2025-09-05 05:18:28] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -53.592900+0.003357j
[2025-09-05 05:18:38] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -53.634983-0.000634j
[2025-09-05 05:18:48] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -53.643177+0.002908j
[2025-09-05 05:18:59] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -53.649037+0.001064j
[2025-09-05 05:19:09] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -53.708441-0.000127j
[2025-09-05 05:19:19] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -53.731987+0.000878j
[2025-09-05 05:19:29] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -53.691674+0.000201j
[2025-09-05 05:19:39] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -53.629121-0.001455j
[2025-09-05 05:19:50] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -53.635886-0.002235j
[2025-09-05 05:20:00] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -53.713802-0.001067j
[2025-09-05 05:20:10] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -53.605665+0.002530j
[2025-09-05 05:20:20] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -53.731288+0.000926j
[2025-09-05 05:20:30] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -53.669834+0.001269j
[2025-09-05 05:20:40] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -53.713170-0.000036j
[2025-09-05 05:20:51] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -53.786896-0.000667j
[2025-09-05 05:21:01] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -53.794979+0.000944j
[2025-09-05 05:21:11] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -53.813065-0.000272j
[2025-09-05 05:21:21] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -53.715949+0.002481j
[2025-09-05 05:21:31] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -53.682502-0.000156j
[2025-09-05 05:21:42] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -53.816222+0.000372j
[2025-09-05 05:21:52] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -53.834131-0.001499j
[2025-09-05 05:22:02] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -53.841757-0.002729j
[2025-09-05 05:22:12] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -53.794245-0.001270j
[2025-09-05 05:22:22] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -53.753679-0.002024j
[2025-09-05 05:22:33] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -53.849049-0.000173j
[2025-09-05 05:22:43] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -53.863835+0.000934j
[2025-09-05 05:22:53] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -53.755557+0.000066j
[2025-09-05 05:23:03] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -53.764307-0.003078j
[2025-09-05 05:23:13] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -53.741001+0.003680j
[2025-09-05 05:23:24] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -53.695518-0.000971j
[2025-09-05 05:23:34] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -53.754356-0.000264j
[2025-09-05 05:23:44] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -53.751444+0.001737j
[2025-09-05 05:23:54] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -53.810594+0.001664j
[2025-09-05 05:24:04] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -53.752358+0.002963j
[2025-09-05 05:24:15] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -53.781780+0.001463j
[2025-09-05 05:24:25] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -53.729920-0.001532j
[2025-09-05 05:24:35] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -53.600502+0.002250j
[2025-09-05 05:24:45] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -53.495194-0.001571j
[2025-09-05 05:24:55] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -53.569034+0.000152j
[2025-09-05 05:25:05] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -53.579387+0.002055j
[2025-09-05 05:25:16] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -53.738351-0.001737j
[2025-09-05 05:25:26] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -53.710295-0.002773j
[2025-09-05 05:25:36] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -53.603448-0.000377j
[2025-09-05 05:25:46] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -53.684894+0.001830j
[2025-09-05 05:25:56] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -53.724847+0.001518j
[2025-09-05 05:26:07] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -53.807240+0.001508j
[2025-09-05 05:26:17] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -53.877554+0.001802j
[2025-09-05 05:26:27] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -53.736301+0.004783j
[2025-09-05 05:26:27] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-05 05:26:37] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -53.706222-0.001311j
[2025-09-05 05:26:47] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -53.621077+0.001400j
[2025-09-05 05:26:58] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -53.747276-0.002546j
[2025-09-05 05:27:08] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -53.637811-0.000998j
[2025-09-05 05:27:18] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -53.686223+0.002458j
[2025-09-05 05:27:28] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -53.734929+0.003489j
[2025-09-05 05:27:38] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -53.757969-0.001008j
[2025-09-05 05:27:49] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -53.555770-0.000422j
[2025-09-05 05:27:59] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -53.568564-0.001203j
[2025-09-05 05:28:09] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -53.643280-0.000524j
[2025-09-05 05:28:19] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -53.669411+0.001251j
[2025-09-05 05:28:29] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -53.693075+0.002391j
[2025-09-05 05:28:39] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -53.732392+0.003055j
[2025-09-05 05:28:50] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -53.848537+0.000074j
[2025-09-05 05:29:00] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -53.715813+0.001921j
[2025-09-05 05:29:10] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -53.685729-0.000362j
[2025-09-05 05:29:20] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -53.767964-0.001992j
[2025-09-05 05:29:30] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -53.604892+0.000612j
[2025-09-05 05:29:41] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -53.731121-0.002213j
[2025-09-05 05:29:51] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -53.672713-0.000818j
[2025-09-05 05:30:01] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -53.671739+0.000953j
[2025-09-05 05:30:11] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -53.774993-0.003518j
[2025-09-05 05:30:21] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -53.721202-0.000808j
[2025-09-05 05:30:32] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -53.703544-0.003886j
[2025-09-05 05:30:42] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -53.733021+0.000061j
[2025-09-05 05:30:52] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -53.702687+0.000506j
[2025-09-05 05:31:02] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -53.732168+0.005061j
[2025-09-05 05:31:12] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -53.741854+0.001545j
[2025-09-05 05:31:23] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -53.691344-0.001417j
[2025-09-05 05:31:33] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -53.778499+0.000932j
[2025-09-05 05:31:43] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -53.729984-0.003015j
[2025-09-05 05:31:53] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -53.734832+0.000578j
[2025-09-05 05:32:03] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -53.732405-0.001257j
[2025-09-05 05:32:14] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -53.697470-0.001085j
[2025-09-05 05:32:24] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -53.693460-0.001028j
[2025-09-05 05:32:34] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -53.622860-0.002320j
[2025-09-05 05:32:44] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -53.506710-0.000917j
[2025-09-05 05:32:54] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -53.601413-0.001484j
[2025-09-05 05:33:05] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -53.540481+0.003736j
[2025-09-05 05:33:15] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -53.599630+0.004360j
[2025-09-05 05:33:25] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -53.783944+0.001787j
[2025-09-05 05:33:35] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -53.679107-0.002486j
[2025-09-05 05:33:45] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -53.610818-0.000096j
[2025-09-05 05:33:56] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -53.658516-0.000722j
[2025-09-05 05:34:06] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -53.710355-0.001375j
[2025-09-05 05:34:16] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -53.794073-0.000197j
[2025-09-05 05:34:26] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -53.763383-0.002395j
[2025-09-05 05:34:36] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -53.735221+0.000195j
[2025-09-05 05:34:46] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -53.619970+0.002162j
[2025-09-05 05:34:57] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -53.647407-0.000608j
[2025-09-05 05:35:07] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -53.664815-0.000983j
[2025-09-05 05:35:17] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -53.682531+0.000113j
[2025-09-05 05:35:27] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -53.785644+0.001778j
[2025-09-05 05:35:37] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -53.710779+0.003263j
[2025-09-05 05:35:48] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -53.728635+0.000979j
[2025-09-05 05:35:58] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -53.728007-0.001494j
[2025-09-05 05:36:08] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -53.726338-0.003301j
[2025-09-05 05:36:18] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -53.765370+0.001169j
[2025-09-05 05:36:28] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -53.649485+0.000428j
[2025-09-05 05:36:39] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -53.814480+0.002649j
[2025-09-05 05:36:49] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -53.769100+0.003447j
[2025-09-05 05:36:59] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -53.833774+0.000321j
[2025-09-05 05:37:09] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -53.746057-0.000806j
[2025-09-05 05:37:19] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -53.646663+0.000689j
[2025-09-05 05:37:29] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -53.710534-0.001095j
[2025-09-05 05:37:40] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -53.592969+0.003795j
[2025-09-05 05:37:50] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -53.625870-0.002747j
[2025-09-05 05:38:00] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -53.625817+0.000412j
[2025-09-05 05:38:10] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -53.736316-0.002763j
[2025-09-05 05:38:20] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -53.778547-0.002102j
[2025-09-05 05:38:31] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -53.782121-0.001538j
[2025-09-05 05:38:41] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -53.701941+0.003171j
[2025-09-05 05:38:51] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -53.641723+0.000271j
[2025-09-05 05:39:01] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -53.700354+0.000442j
[2025-09-05 05:39:11] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -53.729209-0.003121j
[2025-09-05 05:39:22] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -53.676366-0.002778j
[2025-09-05 05:39:32] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -53.664789+0.000068j
[2025-09-05 05:39:42] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -53.685487+0.001483j
[2025-09-05 05:39:52] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -53.708432+0.000567j
[2025-09-05 05:40:02] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -53.661959-0.002563j
[2025-09-05 05:40:13] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -53.676418+0.000817j
[2025-09-05 05:40:23] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -53.826407-0.000631j
[2025-09-05 05:40:33] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -53.661127-0.001812j
[2025-09-05 05:40:43] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -53.698378-0.000495j
[2025-09-05 05:40:53] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -53.667652+0.000740j
[2025-09-05 05:41:04] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -53.839458+0.001744j
[2025-09-05 05:41:14] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -53.838991+0.000169j
[2025-09-05 05:41:24] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -53.899992+0.000029j
[2025-09-05 05:41:34] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -53.808570-0.000014j
[2025-09-05 05:41:44] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -53.739772-0.001306j
[2025-09-05 05:41:54] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -53.735183-0.000136j
[2025-09-05 05:42:05] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -53.731157-0.003056j
[2025-09-05 05:42:15] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -53.627130+0.002081j
[2025-09-05 05:42:25] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -53.596041+0.000624j
[2025-09-05 05:42:35] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -53.769707+0.000554j
[2025-09-05 05:42:45] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -53.858871+0.000010j
[2025-09-05 05:42:56] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -53.763798-0.003547j
[2025-09-05 05:43:06] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -53.734433+0.002867j
[2025-09-05 05:43:16] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -53.865339-0.000039j
[2025-09-05 05:43:27] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -53.686173+0.002098j
[2025-09-05 05:43:37] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -53.600721-0.000132j
[2025-09-05 05:43:47] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -53.705319+0.000732j
[2025-09-05 05:43:57] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -53.775776+0.002429j
[2025-09-05 05:44:07] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -53.655496+0.000674j
[2025-09-05 05:44:18] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -53.481605-0.000997j
[2025-09-05 05:44:18] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-05 05:44:28] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -53.574209+0.001556j
[2025-09-05 05:44:38] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -53.596099+0.000494j
[2025-09-05 05:44:48] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -53.756061-0.001062j
[2025-09-05 05:44:58] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -53.735010+0.000598j
[2025-09-05 05:45:09] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -53.696713+0.001759j
[2025-09-05 05:45:19] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -53.701412+0.001238j
[2025-09-05 05:45:29] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -53.768213+0.001483j
[2025-09-05 05:45:39] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -53.654537+0.000609j
[2025-09-05 05:45:49] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -53.652569-0.001824j
[2025-09-05 05:46:00] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -53.889363-0.000599j
[2025-09-05 05:46:10] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -53.701153-0.000189j
[2025-09-05 05:46:20] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -53.725704-0.001258j
[2025-09-05 05:46:30] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -53.835684-0.002502j
[2025-09-05 05:46:40] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -53.822146-0.000018j
[2025-09-05 05:46:51] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -53.766989+0.000530j
[2025-09-05 05:47:01] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -53.712525+0.000623j
[2025-09-05 05:47:11] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -53.664935-0.000689j
[2025-09-05 05:47:21] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -53.626694+0.001453j
[2025-09-05 05:47:31] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -53.753796+0.003426j
[2025-09-05 05:47:41] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -53.763147+0.001842j
[2025-09-05 05:47:52] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -53.711099+0.003276j
[2025-09-05 05:48:02] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -53.932971-0.001870j
[2025-09-05 05:48:12] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -53.829852-0.001612j
[2025-09-05 05:48:22] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -53.662749+0.000671j
[2025-09-05 05:48:32] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -53.638639+0.000248j
[2025-09-05 05:48:43] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -53.683039-0.001563j
[2025-09-05 05:48:53] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -53.755652+0.000991j
[2025-09-05 05:49:03] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -53.730112-0.000766j
[2025-09-05 05:49:13] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -53.647680-0.001270j
[2025-09-05 05:49:23] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -53.636180-0.003392j
[2025-09-05 05:49:34] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -53.758989+0.001604j
[2025-09-05 05:49:44] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -53.710723-0.002795j
[2025-09-05 05:49:54] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -53.791918+0.000549j
[2025-09-05 05:50:04] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -53.793977+0.001220j
[2025-09-05 05:50:14] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -53.741370-0.000563j
[2025-09-05 05:50:25] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -53.813098-0.002293j
[2025-09-05 05:50:35] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -53.889644-0.000923j
[2025-09-05 05:50:45] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -53.846674-0.001597j
[2025-09-05 05:50:55] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -53.792870-0.000158j
[2025-09-05 05:51:05] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -53.702765-0.001333j
[2025-09-05 05:51:15] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -53.665678-0.002243j
[2025-09-05 05:51:26] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -53.747444-0.001631j
[2025-09-05 05:51:36] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -53.674771+0.000990j
[2025-09-05 05:51:46] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -53.637569-0.001385j
[2025-09-05 05:51:56] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -53.815794+0.001823j
[2025-09-05 05:52:06] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -53.676574+0.000617j
[2025-09-05 05:52:17] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -53.732627-0.001197j
[2025-09-05 05:52:27] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -53.711360+0.001916j
[2025-09-05 05:52:37] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -53.739295-0.001269j
[2025-09-05 05:52:47] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -53.735224+0.000536j
[2025-09-05 05:52:57] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -53.802854-0.002150j
[2025-09-05 05:53:08] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -53.833263-0.001142j
[2025-09-05 05:53:18] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -53.725527-0.001734j
[2025-09-05 05:53:28] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -53.722960-0.000992j
[2025-09-05 05:53:38] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -53.732226+0.002043j
[2025-09-05 05:53:48] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -53.728182-0.001326j
[2025-09-05 05:53:59] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -53.802175-0.001451j
[2025-09-05 05:54:09] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -53.710377-0.000349j
[2025-09-05 05:54:19] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -53.769792+0.001140j
[2025-09-05 05:54:29] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -53.765298-0.001874j
[2025-09-05 05:54:39] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -53.841499+0.010722j
[2025-09-05 05:54:50] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -53.947598+0.002877j
[2025-09-05 05:55:00] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -53.829080+0.000884j
[2025-09-05 05:55:10] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -53.761955-0.000028j
[2025-09-05 05:55:20] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -53.636243-0.000884j
[2025-09-05 05:55:30] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -53.663456-0.001217j
[2025-09-05 05:55:41] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -53.583871+0.001886j
[2025-09-05 05:55:51] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -53.758488+0.000722j
[2025-09-05 05:56:01] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -53.698781-0.000510j
[2025-09-05 05:56:11] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -53.686496-0.001744j
[2025-09-05 05:56:21] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -53.729269+0.000799j
[2025-09-05 05:56:32] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -53.700793-0.000790j
[2025-09-05 05:56:42] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -53.760061+0.001200j
[2025-09-05 05:56:52] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -53.738937+0.001200j
[2025-09-05 05:57:02] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -53.688104-0.001070j
[2025-09-05 05:57:12] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -53.741510-0.002673j
[2025-09-05 05:57:22] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -53.712103+0.003105j
[2025-09-05 05:57:33] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -53.780923+0.001609j
[2025-09-05 05:57:43] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -53.824031-0.001480j
[2025-09-05 05:57:53] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -53.775370+0.002027j
[2025-09-05 05:58:03] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -53.792186-0.000430j
[2025-09-05 05:58:13] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -53.679718-0.001588j
[2025-09-05 05:58:24] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -53.697804+0.001405j
[2025-09-05 05:58:34] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -53.804855+0.005205j
[2025-09-05 05:58:44] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -53.844519+0.001751j
[2025-09-05 05:58:54] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -53.902948-0.000770j
[2025-09-05 05:59:04] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -53.831941+0.001282j
[2025-09-05 05:59:15] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -53.802324+0.001540j
[2025-09-05 05:59:25] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -53.772796+0.000035j
[2025-09-05 05:59:35] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -53.940008+0.000022j
[2025-09-05 05:59:45] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -53.734951+0.000409j
[2025-09-05 05:59:55] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -53.835386-0.002006j
[2025-09-05 06:00:06] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -53.800298+0.001812j
[2025-09-05 06:00:16] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -53.673849-0.001214j
[2025-09-05 06:00:26] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -53.732549+0.001852j
[2025-09-05 06:00:36] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -53.701477+0.000591j
[2025-09-05 06:00:46] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -53.626246-0.005809j
[2025-09-05 06:00:57] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -53.720548+0.000693j
[2025-09-05 06:01:07] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -53.788379+0.001925j
[2025-09-05 06:01:17] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -53.742344-0.000621j
[2025-09-05 06:01:27] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -53.781555+0.001177j
[2025-09-05 06:01:37] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -53.817403-0.004527j
[2025-09-05 06:01:47] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -53.800026-0.002554j
[2025-09-05 06:01:58] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -53.797250+0.004047j
[2025-09-05 06:02:08] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -53.776790+0.000970j
[2025-09-05 06:02:08] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-05 06:02:18] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -53.866162-0.000407j
[2025-09-05 06:02:28] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -53.924501+0.001037j
[2025-09-05 06:02:38] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -53.784648-0.002072j
[2025-09-05 06:02:49] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -53.758022+0.000003j
[2025-09-05 06:02:59] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -53.730813+0.000271j
[2025-09-05 06:03:09] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -53.799622+0.000206j
[2025-09-05 06:03:19] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -53.709661-0.000752j
[2025-09-05 06:03:29] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -53.727107+0.001386j
[2025-09-05 06:03:40] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -53.667504+0.001393j
[2025-09-05 06:03:50] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -53.732775+0.000545j
[2025-09-05 06:04:00] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -53.836921-0.000820j
[2025-09-05 06:04:10] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -53.850337+0.000915j
[2025-09-05 06:04:20] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -53.719306-0.002242j
[2025-09-05 06:04:31] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -53.719930+0.000327j
[2025-09-05 06:04:41] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -53.804064+0.002208j
[2025-09-05 06:04:51] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -53.619578-0.002459j
[2025-09-05 06:05:01] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -53.758905+0.000295j
[2025-09-05 06:05:11] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -53.740756-0.001601j
[2025-09-05 06:05:22] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -53.771164-0.000844j
[2025-09-05 06:05:32] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -53.817061-0.000459j
[2025-09-05 06:05:42] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -53.717785-0.001918j
[2025-09-05 06:05:52] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -53.814257+0.000384j
[2025-09-05 06:06:02] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -53.708661+0.001140j
[2025-09-05 06:06:12] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -53.719896+0.002860j
[2025-09-05 06:06:23] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -53.749525+0.001554j
[2025-09-05 06:06:33] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -53.823717+0.000784j
[2025-09-05 06:06:43] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -53.788455-0.000192j
[2025-09-05 06:06:53] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -53.775822-0.000087j
[2025-09-05 06:07:03] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -53.748563+0.000962j
[2025-09-05 06:07:13] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -53.806153-0.001784j
[2025-09-05 06:07:24] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -53.689640+0.003202j
[2025-09-05 06:07:34] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -53.751844-0.000324j
[2025-09-05 06:07:44] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -53.705422-0.000424j
[2025-09-05 06:07:54] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -53.753032+0.005445j
[2025-09-05 06:08:05] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -53.779524-0.002738j
[2025-09-05 06:08:15] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -53.807676+0.001253j
[2025-09-05 06:08:25] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -53.755315+0.001104j
[2025-09-05 06:08:35] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -53.802618-0.001665j
[2025-09-05 06:08:45] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -53.782292-0.001000j
[2025-09-05 06:08:55] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -53.818108-0.000186j
[2025-09-05 06:09:06] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -53.732937-0.001270j
[2025-09-05 06:09:16] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -53.720599+0.000480j
[2025-09-05 06:09:26] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -53.600141-0.002193j
[2025-09-05 06:09:36] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -53.719998-0.002059j
[2025-09-05 06:09:46] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -53.814468-0.001453j
[2025-09-05 06:09:57] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -53.748276+0.002153j
[2025-09-05 06:10:07] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -53.782155-0.001862j
[2025-09-05 06:10:17] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -53.657959+0.001819j
[2025-09-05 06:10:27] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -53.765705+0.000519j
[2025-09-05 06:10:37] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -53.775056+0.001216j
[2025-09-05 06:10:48] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -53.793983+0.002304j
[2025-09-05 06:10:58] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -53.711041-0.001338j
[2025-09-05 06:11:08] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -53.682291+0.000703j
[2025-09-05 06:11:18] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -53.588347-0.002924j
[2025-09-05 06:11:28] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -53.733755-0.000948j
[2025-09-05 06:11:39] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -53.831113+0.000139j
[2025-09-05 06:11:49] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -53.745764+0.001979j
[2025-09-05 06:11:59] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -53.759047+0.002826j
[2025-09-05 06:12:09] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -53.758085+0.000773j
[2025-09-05 06:12:19] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -53.762525+0.000926j
[2025-09-05 06:12:30] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -53.741247+0.000351j
[2025-09-05 06:12:40] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -53.770107-0.000136j
[2025-09-05 06:12:50] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -53.721926-0.001953j
[2025-09-05 06:13:00] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -53.705387-0.000193j
[2025-09-05 06:13:10] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -53.778817-0.003804j
[2025-09-05 06:13:20] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -53.782959+0.003144j
[2025-09-05 06:13:31] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -53.837667+0.001128j
[2025-09-05 06:13:41] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -53.773886-0.000487j
[2025-09-05 06:13:51] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -53.774557+0.002465j
[2025-09-05 06:14:01] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -53.792142+0.003973j
[2025-09-05 06:14:11] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -53.763918-0.001049j
[2025-09-05 06:14:21] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -53.847031-0.001568j
[2025-09-05 06:14:32] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -53.827979-0.000100j
[2025-09-05 06:14:42] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -53.773226-0.002641j
[2025-09-05 06:14:52] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -53.750233-0.000063j
[2025-09-05 06:15:02] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -53.697804+0.002354j
[2025-09-05 06:15:12] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -53.716894-0.001862j
[2025-09-05 06:15:23] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -53.789861-0.004004j
[2025-09-05 06:15:33] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -53.702250+0.004374j
[2025-09-05 06:15:43] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -53.657669+0.003522j
[2025-09-05 06:15:53] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -53.664299+0.003380j
[2025-09-05 06:16:03] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -53.587335+0.001380j
[2025-09-05 06:16:14] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -53.708342-0.000677j
[2025-09-05 06:16:24] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -53.716009-0.001867j
[2025-09-05 06:16:34] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -53.788868-0.001591j
[2025-09-05 06:16:44] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -53.901880+0.001660j
[2025-09-05 06:16:54] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -53.721718-0.000350j
[2025-09-05 06:17:05] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -53.840924-0.001295j
[2025-09-05 06:17:15] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -53.721032+0.001960j
[2025-09-05 06:17:25] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -53.675279+0.001186j
[2025-09-05 06:17:35] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -53.726070-0.001909j
[2025-09-05 06:17:45] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -53.774712-0.002806j
[2025-09-05 06:17:56] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -53.768094-0.000553j
[2025-09-05 06:18:06] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -53.846456+0.002310j
[2025-09-05 06:18:16] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -53.718801+0.001978j
[2025-09-05 06:18:26] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -53.748183+0.003059j
[2025-09-05 06:18:36] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -53.814464+0.000987j
[2025-09-05 06:18:46] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -53.804532+0.004726j
[2025-09-05 06:18:57] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -53.622756-0.002047j
[2025-09-05 06:19:07] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -53.551225-0.001194j
[2025-09-05 06:19:17] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -53.571875+0.000480j
[2025-09-05 06:19:27] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -53.582042-0.002548j
[2025-09-05 06:19:37] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -53.596255-0.000185j
[2025-09-05 06:19:48] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -53.633959+0.002933j
[2025-09-05 06:19:58] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -53.848246+0.000839j
[2025-09-05 06:19:58] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-05 06:20:08] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -53.748146+0.000288j
[2025-09-05 06:20:18] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -53.777914-0.000177j
[2025-09-05 06:20:28] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -53.766267-0.001479j
[2025-09-05 06:20:39] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -53.718531+0.000877j
[2025-09-05 06:20:49] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -53.731196+0.001360j
[2025-09-05 06:20:59] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -53.730287-0.001839j
[2025-09-05 06:21:09] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -53.740214-0.000962j
[2025-09-05 06:21:19] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -53.824812+0.000838j
[2025-09-05 06:21:30] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -53.800079-0.004046j
[2025-09-05 06:21:40] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -53.733520-0.001763j
[2025-09-05 06:21:50] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -53.695813-0.001281j
[2025-09-05 06:22:00] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -53.691783+0.000040j
[2025-09-05 06:22:10] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -53.751960+0.000151j
[2025-09-05 06:22:21] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -53.677778+0.002922j
[2025-09-05 06:22:31] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -53.726944+0.001787j
[2025-09-05 06:22:41] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -53.693240-0.000503j
[2025-09-05 06:22:51] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -53.697457-0.001908j
[2025-09-05 06:23:01] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -53.687939+0.001689j
[2025-09-05 06:23:12] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -53.833582+0.001262j
[2025-09-05 06:23:22] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -53.720641+0.001817j
[2025-09-05 06:23:32] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -53.706774+0.002774j
[2025-09-05 06:23:42] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -53.694015-0.001268j
[2025-09-05 06:23:52] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -53.756425+0.001136j
[2025-09-05 06:24:02] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -53.678054+0.000616j
[2025-09-05 06:24:13] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -53.709666-0.001857j
[2025-09-05 06:24:23] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -53.723461-0.001247j
[2025-09-05 06:24:33] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -53.816174-0.000117j
[2025-09-05 06:24:43] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -53.775047-0.001626j
[2025-09-05 06:24:53] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -53.650482+0.000500j
[2025-09-05 06:25:04] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -53.701067+0.000923j
[2025-09-05 06:25:14] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -53.544632-0.002115j
[2025-09-05 06:25:24] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -53.526760-0.000875j
[2025-09-05 06:25:34] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -53.582960+0.002569j
[2025-09-05 06:25:44] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -53.820035+0.002704j
[2025-09-05 06:25:55] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -53.857564+0.000142j
[2025-09-05 06:26:05] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -53.853021-0.001384j
[2025-09-05 06:26:15] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -53.833821+0.003109j
[2025-09-05 06:26:25] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -53.701975-0.001172j
[2025-09-05 06:26:35] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -53.736062-0.000606j
[2025-09-05 06:26:46] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -53.672839+0.002663j
[2025-09-05 06:26:56] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -53.658316+0.001312j
[2025-09-05 06:27:06] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -53.704843+0.000965j
[2025-09-05 06:27:16] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -53.695888-0.002539j
[2025-09-05 06:27:26] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -53.801422-0.001459j
[2025-09-05 06:27:37] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -53.719475-0.001515j
[2025-09-05 06:27:47] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -53.818794+0.001607j
[2025-09-05 06:27:57] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -53.707819+0.000464j
[2025-09-05 06:28:07] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -53.745847+0.002890j
[2025-09-05 06:28:17] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -53.765339+0.002561j
[2025-09-05 06:28:27] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -53.689336+0.002862j
[2025-09-05 06:28:38] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -53.798456-0.000220j
[2025-09-05 06:28:48] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -53.844272+0.000564j
[2025-09-05 06:28:58] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -53.699790+0.001829j
[2025-09-05 06:29:08] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -53.762682+0.002685j
[2025-09-05 06:29:18] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -53.813269+0.000900j
[2025-09-05 06:29:29] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -53.724983-0.000272j
[2025-09-05 06:29:39] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -53.754990+0.003009j
[2025-09-05 06:29:49] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -53.787920-0.000908j
[2025-09-05 06:29:59] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -53.731976-0.000027j
[2025-09-05 06:30:09] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -53.763245+0.000272j
[2025-09-05 06:30:19] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -53.739517+0.003468j
[2025-09-05 06:30:30] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -53.772956+0.003376j
[2025-09-05 06:30:40] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -53.572281+0.000945j
[2025-09-05 06:30:50] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -53.755167+0.000789j
[2025-09-05 06:31:00] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -53.707433+0.002363j
[2025-09-05 06:31:10] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -53.750420-0.000802j
[2025-09-05 06:31:21] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -53.881718-0.000003j
[2025-09-05 06:31:31] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -53.739974+0.001847j
[2025-09-05 06:31:41] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -53.745983-0.000264j
[2025-09-05 06:31:51] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -53.738811+0.003587j
[2025-09-05 06:32:01] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -53.774972-0.000045j
[2025-09-05 06:32:12] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -53.724563-0.001976j
[2025-09-05 06:32:22] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -53.750051+0.001998j
[2025-09-05 06:32:32] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -53.775635-0.000799j
[2025-09-05 06:32:42] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -53.824328+0.001667j
[2025-09-05 06:32:52] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -53.685988-0.000959j
[2025-09-05 06:33:03] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -53.746309-0.000067j
[2025-09-05 06:33:13] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -53.678434-0.003584j
[2025-09-05 06:33:23] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -53.719146+0.002555j
[2025-09-05 06:33:33] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -53.709730+0.001182j
[2025-09-05 06:33:43] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -53.546104+0.000128j
[2025-09-05 06:33:54] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -53.656753+0.001342j
[2025-09-05 06:34:04] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -53.808240-0.000875j
[2025-09-05 06:34:14] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -53.733702-0.000983j
[2025-09-05 06:34:24] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -53.604504-0.001463j
[2025-09-05 06:34:34] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -53.720172+0.001460j
[2025-09-05 06:34:45] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -53.776139+0.000817j
[2025-09-05 06:34:55] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -53.722520+0.003174j
[2025-09-05 06:35:05] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -53.833141+0.000343j
[2025-09-05 06:35:15] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -53.831660-0.000554j
[2025-09-05 06:35:25] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -53.691624-0.002146j
[2025-09-05 06:35:36] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -53.757207-0.000334j
[2025-09-05 06:35:46] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -53.698950+0.000821j
[2025-09-05 06:35:56] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -53.688048+0.000144j
[2025-09-05 06:36:06] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -53.767183-0.001520j
[2025-09-05 06:36:16] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -53.657227-0.000067j
[2025-09-05 06:36:26] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -53.716094+0.000038j
[2025-09-05 06:36:37] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -53.730745+0.000953j
[2025-09-05 06:36:47] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -53.751533-0.000611j
[2025-09-05 06:36:57] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -53.831983+0.002028j
[2025-09-05 06:37:07] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -53.738010+0.000956j
[2025-09-05 06:37:17] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -53.635734+0.000929j
[2025-09-05 06:37:28] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -53.739014+0.001206j
[2025-09-05 06:37:38] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -53.863044-0.001830j
[2025-09-05 06:37:48] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -53.809154+0.000282j
[2025-09-05 06:37:48] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-05 06:37:58] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -53.900758-0.003981j
[2025-09-05 06:38:08] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -53.799316+0.000396j
[2025-09-05 06:38:19] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -53.810161-0.005543j
[2025-09-05 06:38:29] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -53.711089-0.001673j
[2025-09-05 06:38:39] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -53.648448+0.001285j
[2025-09-05 06:38:49] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -53.670599+0.001618j
[2025-09-05 06:38:59] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -53.634297+0.002159j
[2025-09-05 06:39:10] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -53.645961+0.001439j
[2025-09-05 06:39:20] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -53.755483-0.000138j
[2025-09-05 06:39:30] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -53.665064+0.000838j
[2025-09-05 06:39:40] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -53.753990+0.002663j
[2025-09-05 06:39:50] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -53.659308-0.002017j
[2025-09-05 06:40:01] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -53.700166+0.001990j
[2025-09-05 06:40:11] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -53.634270+0.000304j
[2025-09-05 06:40:21] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -53.713583+0.000640j
[2025-09-05 06:40:31] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -53.749838-0.000039j
[2025-09-05 06:40:41] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -53.707158+0.003078j
[2025-09-05 06:40:52] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -53.762551-0.002239j
[2025-09-05 06:41:02] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -53.694095-0.004446j
[2025-09-05 06:41:12] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -53.634068+0.000354j
[2025-09-05 06:41:22] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -53.735867-0.001128j
[2025-09-05 06:41:32] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -53.803248-0.001663j
[2025-09-05 06:41:42] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -53.807239-0.002677j
[2025-09-05 06:41:53] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -53.749115+0.001507j
[2025-09-05 06:42:03] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -53.675918+0.000438j
[2025-09-05 06:42:13] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -53.714877+0.000970j
[2025-09-05 06:42:23] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -53.673680-0.000563j
[2025-09-05 06:42:33] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -53.691659+0.001271j
[2025-09-05 06:42:44] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -53.755470-0.001669j
[2025-09-05 06:42:54] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -53.745586-0.001102j
[2025-09-05 06:43:04] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -53.714777-0.001688j
[2025-09-05 06:43:14] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -53.709176+0.000180j
[2025-09-05 06:43:24] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -53.592490-0.001800j
[2025-09-05 06:43:35] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -53.694686+0.002770j
[2025-09-05 06:43:45] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -53.696096+0.002175j
[2025-09-05 06:43:55] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -53.650909-0.001124j
[2025-09-05 06:44:05] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -53.609145-0.001293j
[2025-09-05 06:44:15] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -53.639812+0.002329j
[2025-09-05 06:44:26] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -53.668696+0.000370j
[2025-09-05 06:44:36] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -53.692929-0.000784j
[2025-09-05 06:44:46] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -53.689380-0.001823j
[2025-09-05 06:44:56] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -53.673387+0.002695j
[2025-09-05 06:45:06] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -53.586173+0.000573j
[2025-09-05 06:45:16] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -53.752173-0.002283j
[2025-09-05 06:45:27] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -53.517264-0.002261j
[2025-09-05 06:45:37] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -53.607386-0.005210j
[2025-09-05 06:45:47] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -53.592555+0.001493j
[2025-09-05 06:45:57] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -53.683573-0.001837j
[2025-09-05 06:46:07] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -53.712260-0.000090j
[2025-09-05 06:46:18] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -53.673958-0.000672j
[2025-09-05 06:46:28] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -53.689396+0.002453j
[2025-09-05 06:46:38] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -53.714152+0.002186j
[2025-09-05 06:46:48] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -53.745314-0.000956j
[2025-09-05 06:46:58] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -53.783397+0.001276j
[2025-09-05 06:47:08] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -53.952866-0.001542j
[2025-09-05 06:47:19] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -53.694306-0.002074j
[2025-09-05 06:47:29] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -53.788752+0.000241j
[2025-09-05 06:47:39] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -53.712216-0.000564j
[2025-09-05 06:47:49] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -53.733888+0.000412j
[2025-09-05 06:47:59] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -53.735500+0.004145j
[2025-09-05 06:48:10] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -53.645495+0.000136j
[2025-09-05 06:48:20] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -53.536203+0.000954j
[2025-09-05 06:48:30] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -53.524922+0.000145j
[2025-09-05 06:48:40] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -53.536727+0.002240j
[2025-09-05 06:48:50] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -53.668186-0.000604j
[2025-09-05 06:49:01] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -53.751078+0.002172j
[2025-09-05 06:49:11] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -53.761876+0.000478j
[2025-09-05 06:49:21] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -53.793689-0.002176j
[2025-09-05 06:49:31] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -53.911166+0.003101j
[2025-09-05 06:49:41] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -53.873314+0.001634j
[2025-09-05 06:49:52] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -53.970551-0.001696j
[2025-09-05 06:50:02] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -53.789238+0.000511j
[2025-09-05 06:50:12] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -53.748425-0.001637j
[2025-09-05 06:50:22] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -53.790181-0.002590j
[2025-09-05 06:50:32] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -53.795187+0.001320j
[2025-09-05 06:50:42] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -53.742833-0.000584j
[2025-09-05 06:50:53] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -53.751024+0.001165j
[2025-09-05 06:51:03] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -53.690451-0.000799j
[2025-09-05 06:51:13] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -53.716377+0.000589j
[2025-09-05 06:51:23] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -53.805260+0.002009j
[2025-09-05 06:51:34] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -53.842313-0.002382j
[2025-09-05 06:51:44] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -53.708622-0.001492j
[2025-09-05 06:51:54] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -53.781249+0.000771j
[2025-09-05 06:52:04] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -53.746903-0.002036j
[2025-09-05 06:52:15] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -53.749854+0.000349j
[2025-09-05 06:52:25] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -53.696597+0.000391j
[2025-09-05 06:52:35] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -53.849068-0.001367j
[2025-09-05 06:52:45] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -53.731400-0.001859j
[2025-09-05 06:52:55] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -53.675322-0.002548j
[2025-09-05 06:53:06] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -53.691135-0.003797j
[2025-09-05 06:53:16] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -53.755562-0.002785j
[2025-09-05 06:53:26] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -53.797798+0.000182j
[2025-09-05 06:53:36] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -53.818851-0.002627j
[2025-09-05 06:53:46] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -53.696377+0.001085j
[2025-09-05 06:53:56] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -53.702967-0.000284j
[2025-09-05 06:54:00] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -53.766764+0.003634j
[2025-09-05 06:54:05] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -53.893888+0.002779j
[2025-09-05 06:54:10] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -53.823939+0.001475j
[2025-09-05 06:54:14] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -53.825815+0.002339j
[2025-09-05 06:54:19] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -53.753456+0.001010j
[2025-09-05 06:54:23] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -53.688536+0.000620j
[2025-09-05 06:54:28] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -53.757477-0.000455j
[2025-09-05 06:54:32] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -53.822595-0.002135j
[2025-09-05 06:54:37] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -53.693416-0.002819j
[2025-09-05 06:54:41] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -53.700410-0.001197j
[2025-09-05 06:54:42] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-05 06:54:42] ✅ Training completed | Restarts: 2
[2025-09-05 06:54:42] ============================================================
[2025-09-05 06:54:42] Training completed | Runtime: 10687.9s
[2025-09-05 06:54:43] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-05 06:54:43] ============================================================
