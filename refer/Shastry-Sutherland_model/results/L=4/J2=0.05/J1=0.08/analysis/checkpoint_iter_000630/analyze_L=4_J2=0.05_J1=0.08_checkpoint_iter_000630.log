[2025-09-06 20:40:45] 使用checkpoint文件: results/L=4/J2=0.05/J1=0.08/training/checkpoints/checkpoint_iter_000630.pkl
[2025-09-06 20:41:01] ✓ 从checkpoint加载参数: 630
[2025-09-06 20:41:01]   - 能量: -53.344022-0.001184j ± 0.083517
[2025-09-06 20:41:01] ================================================================================
[2025-09-06 20:41:01] 加载量子态: L=4, J2=0.05, J1=0.08, checkpoint=checkpoint_iter_000630
[2025-09-06 20:41:01] 使用采样数目: 1048576
[2025-09-06 20:41:01] 设置样本数为: 1048576
[2025-09-06 20:41:01] 开始生成共享样本集...
[2025-09-06 20:45:26] 样本生成完成,耗时: 265.377 秒
[2025-09-06 20:45:26] ================================================================================
[2025-09-06 20:45:26] 开始计算自旋结构因子...
[2025-09-06 20:45:26] 初始化操作符缓存...
[2025-09-06 20:45:26] 预构建所有自旋相关操作符...
[2025-09-06 20:45:26] 开始计算自旋相关函数...
[2025-09-06 20:45:40] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 13.443s
[2025-09-06 20:45:58] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 18.439s
[2025-09-06 20:46:12] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 14.061s
[2025-09-06 20:46:26] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 14.062s
[2025-09-06 20:46:40] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 14.022s
[2025-09-06 20:46:54] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 14.069s
[2025-09-06 20:47:08] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 14.052s
[2025-09-06 20:47:22] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 14.019s
[2025-09-06 20:47:37] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 14.060s
[2025-09-06 20:47:51] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 14.051s
[2025-09-06 20:48:05] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 14.054s
[2025-09-06 20:48:19] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 14.049s
[2025-09-06 20:48:33] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 14.052s
[2025-09-06 20:48:47] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 14.025s
[2025-09-06 20:49:01] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 14.053s
[2025-09-06 20:49:15] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 14.053s
[2025-09-06 20:49:29] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 14.048s
[2025-09-06 20:49:43] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 14.062s
[2025-09-06 20:49:57] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 14.058s
[2025-09-06 20:50:11] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 14.055s
[2025-09-06 20:50:25] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 14.038s
[2025-09-06 20:50:39] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 14.031s
[2025-09-06 20:50:53] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 14.046s
[2025-09-06 20:51:07] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 14.056s
[2025-09-06 20:51:21] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 14.051s
[2025-09-06 20:51:35] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 14.051s
[2025-09-06 20:51:50] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 14.056s
[2025-09-06 20:52:04] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 14.017s
[2025-09-06 20:52:18] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 14.056s
[2025-09-06 20:52:32] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 14.065s
[2025-09-06 20:52:46] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 14.051s
[2025-09-06 20:53:00] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 14.035s
[2025-09-06 20:53:14] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 14.058s
[2025-09-06 20:53:28] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 14.017s
[2025-09-06 20:53:42] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 14.055s
[2025-09-06 20:53:56] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 14.053s
[2025-09-06 20:54:10] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 14.048s
[2025-09-06 20:54:24] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 14.050s
[2025-09-06 20:54:38] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 14.060s
[2025-09-06 20:54:52] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 14.038s
[2025-09-06 20:55:06] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 14.059s
[2025-09-06 20:55:20] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 14.062s
[2025-09-06 20:55:34] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 14.052s
[2025-09-06 20:55:48] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 14.019s
[2025-09-06 20:56:02] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 14.054s
[2025-09-06 20:56:16] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 14.053s
[2025-09-06 20:56:31] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 14.032s
[2025-09-06 20:56:45] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 14.056s
[2025-09-06 20:56:59] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 14.049s
[2025-09-06 20:57:13] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 14.056s
[2025-09-06 20:57:27] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 14.058s
[2025-09-06 20:57:41] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 14.048s
[2025-09-06 20:57:55] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 14.060s
[2025-09-06 20:58:09] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 14.038s
[2025-09-06 20:58:23] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 14.047s
[2025-09-06 20:58:37] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 14.052s
[2025-09-06 20:58:51] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 14.051s
[2025-09-06 20:59:05] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 14.051s
[2025-09-06 20:59:19] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 14.055s
[2025-09-06 20:59:33] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 14.046s
[2025-09-06 20:59:47] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 14.055s
[2025-09-06 21:00:01] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 14.052s
[2025-09-06 21:00:15] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 14.028s
[2025-09-06 21:00:29] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 14.028s
[2025-09-06 21:00:29] 自旋相关函数计算完成,总耗时 903.13 秒
[2025-09-06 21:00:31] 计算傅里叶变换...
[2025-09-06 21:00:34] 自旋结构因子计算完成
[2025-09-06 21:00:35] 自旋相关函数平均误差: 0.000670
