[2025-09-05 00:57:52] ✓ 从checkpoint恢复: results/L=4/J2=0.05/J1=0.07/training/checkpoints/final_GCNN.pkl
[2025-09-05 00:57:52]   - 迭代次数: final
[2025-09-05 00:57:52]   - 能量: -53.050951+0.001036j ± 0.082072
[2025-09-05 00:57:52]   - 时间戳: 2025-09-05T00:57:41.739419+08:00
[2025-09-05 00:58:03] ✓ 变分状态参数已从checkpoint恢复
[2025-09-05 00:58:03] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-05 00:58:03] ==================================================
[2025-09-05 00:58:03] GCNN for Shastry-Sutherland Model
[2025-09-05 00:58:03] ==================================================
[2025-09-05 00:58:03] System parameters:
[2025-09-05 00:58:03]   - System size: L=4, N=64
[2025-09-05 00:58:03]   - System parameters: J1=0.08, J2=0.05, Q=0.95
[2025-09-05 00:58:03] --------------------------------------------------
[2025-09-05 00:58:03] Model parameters:
[2025-09-05 00:58:03]   - Number of layers = 4
[2025-09-05 00:58:03]   - Number of features = 4
[2025-09-05 00:58:03]   - Total parameters = 12572
[2025-09-05 00:58:03] --------------------------------------------------
[2025-09-05 00:58:03] Training parameters:
[2025-09-05 00:58:03]   - Learning rate: 0.015
[2025-09-05 00:58:03]   - Total iterations: 1050
[2025-09-05 00:58:03]   - Annealing cycles: 3
[2025-09-05 00:58:03]   - Initial period: 150
[2025-09-05 00:58:03]   - Period multiplier: 2.0
[2025-09-05 00:58:03]   - Temperature range: 0.0-1.0
[2025-09-05 00:58:03]   - Samples: 4096
[2025-09-05 00:58:03]   - Discarded samples: 0
[2025-09-05 00:58:03]   - Chunk size: 2048
[2025-09-05 00:58:03]   - Diagonal shift: 0.2
[2025-09-05 00:58:03]   - Gradient clipping: 1.0
[2025-09-05 00:58:03]   - Checkpoint enabled: interval=105
[2025-09-05 00:58:03]   - Checkpoint directory: results/L=4/J2=0.05/J1=0.08/training/checkpoints
[2025-09-05 00:58:03] --------------------------------------------------
[2025-09-05 00:58:03] Device status:
[2025-09-05 00:58:03]   - Devices model: NVIDIA H200 NVL
[2025-09-05 00:58:03]   - Number of devices: 1
[2025-09-05 00:58:03]   - Sharding: True
[2025-09-05 00:58:03] ============================================================
[2025-09-05 00:58:50] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -53.275059-0.005812j
[2025-09-05 00:59:19] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -53.102996-0.004385j
[2025-09-05 00:59:29] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -53.229736-0.001455j
[2025-09-05 00:59:39] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -53.231611-0.000088j
[2025-09-05 00:59:50] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -53.289138-0.001328j
[2025-09-05 01:00:00] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -53.414991+0.002343j
[2025-09-05 01:00:10] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -53.288692+0.004937j
[2025-09-05 01:00:20] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -53.346024+0.000460j
[2025-09-05 01:00:30] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -53.301069+0.002116j
[2025-09-05 01:00:40] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -53.223854+0.001318j
[2025-09-05 01:00:50] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -53.328502+0.002757j
[2025-09-05 01:01:00] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -53.495931-0.001601j
[2025-09-05 01:01:10] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -53.281369+0.001094j
[2025-09-05 01:01:20] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -53.438014+0.001160j
[2025-09-05 01:01:30] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -53.346426-0.000129j
[2025-09-05 01:01:41] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -53.355441+0.000657j
[2025-09-05 01:01:51] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -53.383648-0.000151j
[2025-09-05 01:02:01] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -53.413117-0.001516j
[2025-09-05 01:02:11] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -53.337831+0.001870j
[2025-09-05 01:02:21] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -53.264009-0.003270j
[2025-09-05 01:02:31] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -53.328294-0.000262j
[2025-09-05 01:02:41] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -53.291100+0.002625j
[2025-09-05 01:02:51] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -53.184714+0.002493j
[2025-09-05 01:03:01] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -53.380078+0.003773j
[2025-09-05 01:03:12] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -53.275874-0.001366j
[2025-09-05 01:03:22] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -53.335924-0.004281j
[2025-09-05 01:03:32] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -53.333692+0.000322j
[2025-09-05 01:03:42] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -53.250446+0.001475j
[2025-09-05 01:03:52] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -53.328961+0.001319j
[2025-09-05 01:04:02] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -53.284372-0.001981j
[2025-09-05 01:04:12] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -53.438814+0.001455j
[2025-09-05 01:04:22] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -53.243782-0.000335j
[2025-09-05 01:04:32] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -53.378934+0.003453j
[2025-09-05 01:04:43] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -53.282244-0.001251j
[2025-09-05 01:04:53] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -53.353009+0.000100j
[2025-09-05 01:05:03] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -53.275217-0.001717j
[2025-09-05 01:05:13] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -53.363378+0.000713j
[2025-09-05 01:05:23] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -53.363355-0.001821j
[2025-09-05 01:05:33] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -53.389466-0.001550j
[2025-09-05 01:05:43] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -53.237049+0.001833j
[2025-09-05 01:05:53] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -53.178589+0.000171j
[2025-09-05 01:06:03] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -53.420456+0.001182j
[2025-09-05 01:06:14] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -53.434719-0.000108j
[2025-09-05 01:06:24] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -53.365106+0.003549j
[2025-09-05 01:06:34] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -53.318685+0.004513j
[2025-09-05 01:06:44] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -53.260958+0.001177j
[2025-09-05 01:06:54] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -53.251559+0.001446j
[2025-09-05 01:07:04] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -53.477667+0.000707j
[2025-09-05 01:07:14] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -53.501743+0.001284j
[2025-09-05 01:07:24] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -53.392511-0.001822j
[2025-09-05 01:07:34] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -53.409677-0.002795j
[2025-09-05 01:07:45] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -53.369799-0.000050j
[2025-09-05 01:07:55] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -53.289639+0.002300j
[2025-09-05 01:08:05] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -53.370059-0.003731j
[2025-09-05 01:08:15] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -53.299171-0.002691j
[2025-09-05 01:08:25] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -53.341778-0.002837j
[2025-09-05 01:08:35] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -53.209054+0.002205j
[2025-09-05 01:08:45] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -53.374551-0.000863j
[2025-09-05 01:08:55] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -53.395185-0.001509j
[2025-09-05 01:09:05] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -53.374979+0.000660j
[2025-09-05 01:09:16] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -53.330029+0.000420j
[2025-09-05 01:09:26] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -53.327900-0.001132j
[2025-09-05 01:09:36] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -53.341050+0.004376j
[2025-09-05 01:09:46] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -53.286931-0.001759j
[2025-09-05 01:09:56] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -53.253919-0.000649j
[2025-09-05 01:10:06] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -53.283923-0.001544j
[2025-09-05 01:10:16] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -53.345898-0.000010j
[2025-09-05 01:10:26] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -53.205748+0.003158j
[2025-09-05 01:10:36] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -53.286787-0.001584j
[2025-09-05 01:10:47] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -53.380628-0.005101j
[2025-09-05 01:10:57] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -53.386316-0.004120j
[2025-09-05 01:11:07] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -53.529997-0.001435j
[2025-09-05 01:11:17] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -53.397138+0.000208j
[2025-09-05 01:11:27] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -53.396417-0.002058j
[2025-09-05 01:11:37] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -53.379209-0.000036j
[2025-09-05 01:11:47] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -53.388779-0.000017j
[2025-09-05 01:11:57] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -53.418658-0.000944j
[2025-09-05 01:12:07] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -53.564164-0.000378j
[2025-09-05 01:12:18] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -53.375823+0.003098j
[2025-09-05 01:12:28] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -53.369941+0.003362j
[2025-09-05 01:12:38] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -53.290818-0.002649j
[2025-09-05 01:12:48] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -53.371309+0.001071j
[2025-09-05 01:12:58] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -53.415948-0.001181j
[2025-09-05 01:13:08] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -53.262191+0.003853j
[2025-09-05 01:13:18] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -53.415827-0.000984j
[2025-09-05 01:13:28] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -53.303397-0.000473j
[2025-09-05 01:13:38] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -53.397216-0.003280j
[2025-09-05 01:13:49] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -53.364690+0.000317j
[2025-09-05 01:13:59] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -53.313291-0.002562j
[2025-09-05 01:14:09] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -53.375092-0.001925j
[2025-09-05 01:14:19] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -53.245128-0.003228j
[2025-09-05 01:14:29] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -53.321811+0.002333j
[2025-09-05 01:14:39] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -53.352407+0.006715j
[2025-09-05 01:14:49] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -53.330114-0.000664j
[2025-09-05 01:14:59] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -53.312405+0.000096j
[2025-09-05 01:15:09] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -53.330501-0.000814j
[2025-09-05 01:15:20] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -53.314421+0.000670j
[2025-09-05 01:15:30] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -53.253553+0.001060j
[2025-09-05 01:15:40] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -53.222214-0.002026j
[2025-09-05 01:15:50] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -53.274226+0.000179j
[2025-09-05 01:16:00] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -53.121100+0.000006j
[2025-09-05 01:16:10] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -53.257234-0.002266j
[2025-09-05 01:16:20] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -53.280226+0.002813j
[2025-09-05 01:16:30] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -53.337315-0.000447j
[2025-09-05 01:16:40] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -53.291982-0.000365j
[2025-09-05 01:16:41] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-05 01:16:51] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -53.405505-0.002521j
[2025-09-05 01:17:01] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -53.351857+0.002047j
[2025-09-05 01:17:11] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -53.436461-0.000592j
[2025-09-05 01:17:21] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -53.341632+0.001431j
[2025-09-05 01:17:31] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -53.407421-0.000534j
[2025-09-05 01:17:41] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -53.287883-0.000840j
[2025-09-05 01:17:51] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -53.288692-0.001663j
[2025-09-05 01:18:01] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -53.338325-0.004082j
[2025-09-05 01:18:12] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -53.279507-0.000496j
[2025-09-05 01:18:22] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -53.325024-0.000126j
[2025-09-05 01:18:32] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -53.330071-0.000952j
[2025-09-05 01:18:42] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -53.334942+0.001018j
[2025-09-05 01:18:52] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -53.298069+0.001352j
[2025-09-05 01:19:02] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -53.333506-0.002566j
[2025-09-05 01:19:12] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -53.337556-0.003125j
[2025-09-05 01:19:22] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -53.328606-0.001016j
[2025-09-05 01:19:32] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -53.267149-0.004047j
[2025-09-05 01:19:43] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -53.336183-0.002030j
[2025-09-05 01:19:53] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -53.315994+0.000579j
[2025-09-05 01:20:03] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -53.269648+0.002200j
[2025-09-05 01:20:13] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -53.376569-0.000530j
[2025-09-05 01:20:23] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -53.379979+0.001143j
[2025-09-05 01:20:33] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -53.477540-0.003591j
[2025-09-05 01:20:43] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -53.391534+0.002922j
[2025-09-05 01:20:53] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -53.352567+0.001090j
[2025-09-05 01:21:03] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -53.389911-0.000038j
[2025-09-05 01:21:14] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -53.381265-0.003319j
[2025-09-05 01:21:24] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -53.291264-0.001274j
[2025-09-05 01:21:34] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -53.365176-0.003408j
[2025-09-05 01:21:44] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -53.336632-0.001393j
[2025-09-05 01:21:54] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -53.290956+0.000278j
[2025-09-05 01:22:04] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -53.264458-0.000343j
[2025-09-05 01:22:14] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -53.451815-0.001538j
[2025-09-05 01:22:24] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -53.376235-0.002271j
[2025-09-05 01:22:34] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -53.494392-0.000566j
[2025-09-05 01:22:45] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -53.252846-0.003595j
[2025-09-05 01:22:55] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -53.301079-0.003636j
[2025-09-05 01:23:05] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -53.309748+0.002619j
[2025-09-05 01:23:15] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -53.299070-0.000443j
[2025-09-05 01:23:25] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -53.381052+0.002620j
[2025-09-05 01:23:35] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -53.339669+0.001772j
[2025-09-05 01:23:45] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -53.345797+0.000803j
[2025-09-05 01:23:55] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -53.328420-0.000682j
[2025-09-05 01:24:05] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -53.373725-0.004721j
[2025-09-05 01:24:15] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -53.396891+0.001040j
[2025-09-05 01:24:15] RESTART #1 | Period: 300
[2025-09-05 01:24:26] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -53.258977-0.000262j
[2025-09-05 01:24:36] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -53.389091+0.001709j
[2025-09-05 01:24:46] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -53.347660+0.002506j
[2025-09-05 01:24:56] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -53.440804+0.000292j
[2025-09-05 01:25:06] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -53.355569+0.003437j
[2025-09-05 01:25:16] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -53.302844+0.000009j
[2025-09-05 01:25:26] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -53.315596-0.001238j
[2025-09-05 01:25:36] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -53.243766+0.001492j
[2025-09-05 01:25:46] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -53.162306-0.001446j
[2025-09-05 01:25:56] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -53.351397-0.002850j
[2025-09-05 01:26:07] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -53.345527+0.001408j
[2025-09-05 01:26:17] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -53.326311-0.001844j
[2025-09-05 01:26:27] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -53.222153+0.001720j
[2025-09-05 01:26:37] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -53.265785+0.001811j
[2025-09-05 01:26:47] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -53.342564-0.002527j
[2025-09-05 01:26:57] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -53.397047+0.001003j
[2025-09-05 01:27:07] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -53.376791+0.002067j
[2025-09-05 01:27:17] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -53.391903+0.000562j
[2025-09-05 01:27:27] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -53.352111-0.001915j
[2025-09-05 01:27:37] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -53.320322+0.000810j
[2025-09-05 01:27:47] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -53.369532+0.004883j
[2025-09-05 01:27:58] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -53.416256-0.004609j
[2025-09-05 01:28:08] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -53.162555-0.001773j
[2025-09-05 01:28:18] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -53.323994+0.001148j
[2025-09-05 01:28:28] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -53.351432-0.001097j
[2025-09-05 01:28:38] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -53.272023-0.001770j
[2025-09-05 01:28:48] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -53.285037-0.002513j
[2025-09-05 01:28:58] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -53.389332+0.000613j
[2025-09-05 01:29:08] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -53.200391-0.000224j
[2025-09-05 01:29:18] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -53.183918-0.003316j
[2025-09-05 01:29:28] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -53.289442+0.001888j
[2025-09-05 01:29:39] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -53.302499-0.001645j
[2025-09-05 01:29:49] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -53.396617-0.000620j
[2025-09-05 01:29:59] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -53.387826-0.000244j
[2025-09-05 01:30:09] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -53.406054+0.003170j
[2025-09-05 01:30:19] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -53.259380-0.000964j
[2025-09-05 01:30:29] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -53.353902-0.000560j
[2025-09-05 01:30:39] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -53.352037-0.002675j
[2025-09-05 01:30:49] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -53.326663+0.000584j
[2025-09-05 01:30:59] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -53.392473+0.000782j
[2025-09-05 01:31:09] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -53.501040+0.004286j
[2025-09-05 01:31:19] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -53.423367+0.000106j
[2025-09-05 01:31:30] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -53.357639+0.001587j
[2025-09-05 01:31:40] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -53.443503-0.001770j
[2025-09-05 01:31:50] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -53.345419-0.000769j
[2025-09-05 01:32:00] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -53.251464+0.002905j
[2025-09-05 01:32:10] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -53.309885+0.001560j
[2025-09-05 01:32:20] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -53.529031-0.001181j
[2025-09-05 01:32:30] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -53.447277-0.001797j
[2025-09-05 01:32:40] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -53.241333+0.005194j
[2025-09-05 01:32:50] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -53.386126+0.002500j
[2025-09-05 01:33:00] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -53.428222-0.003233j
[2025-09-05 01:33:10] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -53.433169+0.000250j
[2025-09-05 01:33:21] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -53.251971-0.003219j
[2025-09-05 01:33:31] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -53.392059-0.002533j
[2025-09-05 01:33:41] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -53.265301+0.002132j
[2025-09-05 01:33:51] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -53.319365-0.001081j
[2025-09-05 01:34:01] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -53.388962+0.000240j
[2025-09-05 01:34:11] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -53.429491-0.000129j
[2025-09-05 01:34:21] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -53.412223+0.000567j
[2025-09-05 01:34:21] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-05 01:34:31] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -53.325511-0.002713j
[2025-09-05 01:34:41] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -53.297204-0.006167j
[2025-09-05 01:34:51] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -53.274863-0.000020j
[2025-09-05 01:35:02] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -53.284758-0.002754j
[2025-09-05 01:35:12] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -53.238425-0.001054j
[2025-09-05 01:35:22] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -53.234754-0.001583j
[2025-09-05 01:35:32] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -53.295515+0.001431j
[2025-09-05 01:35:42] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -53.209833-0.001082j
[2025-09-05 01:35:52] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -53.218992-0.001496j
[2025-09-05 01:36:02] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -53.273885-0.003074j
[2025-09-05 01:36:12] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -53.274227-0.000275j
[2025-09-05 01:36:22] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -53.315885+0.000810j
[2025-09-05 01:36:32] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -53.268511+0.001729j
[2025-09-05 01:36:42] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -53.303116+0.000067j
[2025-09-05 01:36:53] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -53.310832-0.000310j
[2025-09-05 01:37:03] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -53.340606-0.002224j
[2025-09-05 01:37:13] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -53.294963+0.002801j
[2025-09-05 01:37:23] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -53.266781+0.000959j
[2025-09-05 01:37:33] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -53.244873-0.002036j
[2025-09-05 01:37:43] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -53.225996-0.001822j
[2025-09-05 01:37:53] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -53.241468+0.000495j
[2025-09-05 01:38:03] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -53.328363+0.000507j
[2025-09-05 01:38:13] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -53.455583-0.001433j
[2025-09-05 01:38:24] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -53.477936+0.001647j
[2025-09-05 01:38:34] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -53.428558+0.001253j
[2025-09-05 01:38:44] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -53.287133+0.000757j
[2025-09-05 01:38:54] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -53.407465+0.001815j
[2025-09-05 01:39:04] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -53.350459-0.000599j
[2025-09-05 01:39:14] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -53.298764-0.001257j
[2025-09-05 01:39:24] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -53.350278-0.002509j
[2025-09-05 01:39:34] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -53.408690-0.001296j
[2025-09-05 01:39:44] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -53.325891-0.002156j
[2025-09-05 01:39:55] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -53.362169-0.001165j
[2025-09-05 01:40:05] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -53.326759-0.000007j
[2025-09-05 01:40:15] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -53.439911-0.001598j
[2025-09-05 01:40:25] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -53.381252+0.001065j
[2025-09-05 01:40:35] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -53.380426-0.000488j
[2025-09-05 01:40:45] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -53.378248-0.001667j
[2025-09-05 01:40:55] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -53.401184-0.000414j
[2025-09-05 01:41:05] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -53.434989-0.001681j
[2025-09-05 01:41:15] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -53.349739-0.002647j
[2025-09-05 01:41:25] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -53.407904+0.001616j
[2025-09-05 01:41:36] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -53.368902-0.000144j
[2025-09-05 01:41:46] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -53.346169-0.002350j
[2025-09-05 01:41:56] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -53.370463-0.001849j
[2025-09-05 01:42:06] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -53.331576-0.001956j
[2025-09-05 01:42:16] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -53.404322-0.000671j
[2025-09-05 01:42:26] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -53.349587-0.001170j
[2025-09-05 01:42:36] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -53.322594-0.003666j
[2025-09-05 01:42:46] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -53.293645+0.000375j
[2025-09-05 01:42:56] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -53.421760-0.000087j
[2025-09-05 01:43:06] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -53.506250-0.001499j
[2025-09-05 01:43:17] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -53.535509+0.001610j
[2025-09-05 01:43:27] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -53.411920-0.000132j
[2025-09-05 01:43:37] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -53.479509-0.001911j
[2025-09-05 01:43:47] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -53.559677+0.003954j
[2025-09-05 01:43:57] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -53.398530-0.000190j
[2025-09-05 01:44:07] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -53.354157+0.001623j
[2025-09-05 01:44:17] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -53.327409-0.001028j
[2025-09-05 01:44:27] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -53.383283+0.000587j
[2025-09-05 01:44:37] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -53.307325+0.002859j
[2025-09-05 01:44:48] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -53.393568+0.003402j
[2025-09-05 01:44:58] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -53.300326-0.000078j
[2025-09-05 01:45:08] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -53.481495-0.000617j
[2025-09-05 01:45:18] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -53.421935-0.000651j
[2025-09-05 01:45:28] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -53.542938+0.001026j
[2025-09-05 01:45:38] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -53.405059+0.001279j
[2025-09-05 01:45:48] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -53.369990+0.000854j
[2025-09-05 01:45:58] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -53.345773-0.001199j
[2025-09-05 01:46:08] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -53.424840-0.000191j
[2025-09-05 01:46:19] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -53.457101-0.000615j
[2025-09-05 01:46:29] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -53.454222-0.002808j
[2025-09-05 01:46:39] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -53.348452+0.005608j
[2025-09-05 01:46:49] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -53.309080-0.000206j
[2025-09-05 01:46:59] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -53.324985+0.001463j
[2025-09-05 01:47:09] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -53.370071-0.001678j
[2025-09-05 01:47:19] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -53.234190-0.000489j
[2025-09-05 01:47:29] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -53.334953+0.002544j
[2025-09-05 01:47:39] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -53.237796+0.005016j
[2025-09-05 01:47:49] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -53.304786+0.000092j
[2025-09-05 01:48:00] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -53.339711-0.000020j
[2025-09-05 01:48:10] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -53.378145-0.000932j
[2025-09-05 01:48:20] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -53.351827-0.005068j
[2025-09-05 01:48:30] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -53.375000+0.003476j
[2025-09-05 01:48:40] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -53.345745-0.002810j
[2025-09-05 01:48:50] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -53.207237-0.004222j
[2025-09-05 01:49:00] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -53.214622+0.002558j
[2025-09-05 01:49:10] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -53.220234-0.003839j
[2025-09-05 01:49:21] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -53.372550-0.004037j
[2025-09-05 01:49:31] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -53.354632-0.000495j
[2025-09-05 01:49:41] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -53.297721+0.000347j
[2025-09-05 01:49:51] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -53.379887-0.003288j
[2025-09-05 01:50:01] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -53.482717+0.000864j
[2025-09-05 01:50:11] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -53.530337+0.002702j
[2025-09-05 01:50:21] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -53.611294+0.005140j
[2025-09-05 01:50:31] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -53.518188+0.001458j
[2025-09-05 01:50:41] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -53.491739+0.002389j
[2025-09-05 01:50:52] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -53.584090-0.000729j
[2025-09-05 01:51:02] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -53.541642-0.001124j
[2025-09-05 01:51:12] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -53.479071+0.003346j
[2025-09-05 01:51:22] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -53.399168-0.001366j
[2025-09-05 01:51:32] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -53.506444+0.000335j
[2025-09-05 01:51:42] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -53.447133-0.001129j
[2025-09-05 01:51:52] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -53.494081+0.000215j
[2025-09-05 01:52:02] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -53.384448+0.001748j
[2025-09-05 01:52:02] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-05 01:52:12] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -53.463724-0.002658j
[2025-09-05 01:52:23] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -53.308519-0.000792j
[2025-09-05 01:52:33] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -53.224633+0.000708j
[2025-09-05 01:52:43] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -53.317006-0.004691j
[2025-09-05 01:52:53] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -53.383580-0.002796j
[2025-09-05 01:53:03] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -53.217180-0.000597j
[2025-09-05 01:53:13] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -53.168054-0.004617j
[2025-09-05 01:53:23] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -53.263830-0.004853j
[2025-09-05 01:53:33] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -53.309647-0.002888j
[2025-09-05 01:53:43] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -53.451031+0.002266j
[2025-09-05 01:53:53] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -53.365590+0.001961j
[2025-09-05 01:54:04] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -53.415041-0.003785j
[2025-09-05 01:54:14] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -53.320672+0.001871j
[2025-09-05 01:54:24] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -53.212104-0.002016j
[2025-09-05 01:54:34] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -53.261693+0.001444j
[2025-09-05 01:54:44] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -53.351455-0.001047j
[2025-09-05 01:54:54] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -53.301063+0.002631j
[2025-09-05 01:55:04] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -53.457340-0.003019j
[2025-09-05 01:55:14] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -53.366312+0.000759j
[2025-09-05 01:55:24] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -53.399954+0.002359j
[2025-09-05 01:55:35] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -53.410591-0.003675j
[2025-09-05 01:55:45] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -53.426108-0.001947j
[2025-09-05 01:55:55] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -53.461312+0.002795j
[2025-09-05 01:56:05] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -53.319790-0.002084j
[2025-09-05 01:56:15] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -53.334527-0.002087j
[2025-09-05 01:56:25] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -53.342962-0.002953j
[2025-09-05 01:56:35] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -53.384779+0.005594j
[2025-09-05 01:56:45] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -53.497283+0.001026j
[2025-09-05 01:56:55] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -53.476655+0.002490j
[2025-09-05 01:57:06] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -53.421150+0.000437j
[2025-09-05 01:57:16] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -53.351474-0.002202j
[2025-09-05 01:57:26] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -53.368881-0.000601j
[2025-09-05 01:57:36] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -53.427765+0.003778j
[2025-09-05 01:57:46] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -53.429666+0.004145j
[2025-09-05 01:57:56] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -53.308054+0.001406j
[2025-09-05 01:58:06] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -53.262128+0.000951j
[2025-09-05 01:58:16] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -53.318575-0.001479j
[2025-09-05 01:58:27] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -53.382094+0.002741j
[2025-09-05 01:58:37] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -53.383449-0.003427j
[2025-09-05 01:58:47] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -53.540893+0.001620j
[2025-09-05 01:58:57] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -53.448515-0.001568j
[2025-09-05 01:59:07] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -53.531313-0.000837j
[2025-09-05 01:59:17] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -53.465126+0.001180j
[2025-09-05 01:59:27] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -53.503582+0.000740j
[2025-09-05 01:59:37] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -53.266430+0.002979j
[2025-09-05 01:59:47] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -53.227135-0.001576j
[2025-09-05 01:59:57] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -53.225464-0.002387j
[2025-09-05 02:00:08] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -53.335354-0.003761j
[2025-09-05 02:00:18] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -53.361612-0.001005j
[2025-09-05 02:00:28] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -53.483203-0.000361j
[2025-09-05 02:00:38] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -53.366577-0.002064j
[2025-09-05 02:00:48] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -53.324087-0.000402j
[2025-09-05 02:00:58] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -53.463897-0.001624j
[2025-09-05 02:01:08] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -53.283095-0.001756j
[2025-09-05 02:01:18] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -53.363026+0.001018j
[2025-09-05 02:01:28] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -53.352700-0.003156j
[2025-09-05 02:01:38] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -53.236714+0.003245j
[2025-09-05 02:01:48] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -53.264515-0.005867j
[2025-09-05 02:01:59] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -53.238317-0.002078j
[2025-09-05 02:02:09] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -53.290691+0.000351j
[2025-09-05 02:02:19] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -53.392817+0.000169j
[2025-09-05 02:02:29] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -53.425849+0.001768j
[2025-09-05 02:02:39] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -53.473370+0.003846j
[2025-09-05 02:02:49] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -53.397469-0.002424j
[2025-09-05 02:02:59] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -53.375127-0.001115j
[2025-09-05 02:03:09] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -53.376829+0.003086j
[2025-09-05 02:03:19] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -53.415461+0.002567j
[2025-09-05 02:03:29] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -53.385827-0.004865j
[2025-09-05 02:03:40] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -53.416177-0.004814j
[2025-09-05 02:03:50] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -53.460259-0.001574j
[2025-09-05 02:04:00] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -53.441641+0.001372j
[2025-09-05 02:04:10] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -53.400407+0.001147j
[2025-09-05 02:04:20] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -53.484427-0.001265j
[2025-09-05 02:04:30] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -53.408077-0.002106j
[2025-09-05 02:04:40] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -53.363982+0.002290j
[2025-09-05 02:04:50] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -53.344132-0.000137j
[2025-09-05 02:05:00] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -53.496866+0.000644j
[2025-09-05 02:05:10] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -53.508850+0.001628j
[2025-09-05 02:05:20] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -53.413641-0.005377j
[2025-09-05 02:05:31] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -53.386445-0.001833j
[2025-09-05 02:05:41] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -53.421810+0.001285j
[2025-09-05 02:05:51] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -53.256691+0.002379j
[2025-09-05 02:06:02] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -53.340502+0.000632j
[2025-09-05 02:06:12] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -53.306075-0.000778j
[2025-09-05 02:06:22] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -53.307177-0.003911j
[2025-09-05 02:06:32] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -53.388936+0.001249j
[2025-09-05 02:06:42] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -53.297605+0.001012j
[2025-09-05 02:06:52] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -53.224154-0.000396j
[2025-09-05 02:07:02] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -53.237050-0.000691j
[2025-09-05 02:07:12] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -53.321729-0.000139j
[2025-09-05 02:07:22] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -53.329078-0.001655j
[2025-09-05 02:07:32] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -53.301536+0.001411j
[2025-09-05 02:07:43] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -53.313659+0.000883j
[2025-09-05 02:07:53] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -53.342680-0.000745j
[2025-09-05 02:08:03] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -53.315996-0.002191j
[2025-09-05 02:08:13] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -53.354927+0.003138j
[2025-09-05 02:08:23] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -53.376701+0.002240j
[2025-09-05 02:08:33] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -53.262098+0.003992j
[2025-09-05 02:08:43] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -53.222674-0.001774j
[2025-09-05 02:08:53] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -53.209392-0.003135j
[2025-09-05 02:09:03] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -53.191612-0.003376j
[2025-09-05 02:09:13] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -53.246839+0.000316j
[2025-09-05 02:09:23] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -53.085312+0.001356j
[2025-09-05 02:09:33] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -53.208263-0.000648j
[2025-09-05 02:09:44] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -53.195574-0.000238j
[2025-09-05 02:09:44] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-05 02:09:54] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -53.256571+0.000147j
[2025-09-05 02:10:04] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -53.256075-0.002313j
[2025-09-05 02:10:14] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -53.396821-0.003025j
[2025-09-05 02:10:24] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -53.251271+0.003231j
[2025-09-05 02:10:34] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -53.345226+0.000056j
[2025-09-05 02:10:44] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -53.351244-0.002468j
[2025-09-05 02:10:54] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -53.322965+0.000462j
[2025-09-05 02:11:04] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -53.291562+0.003247j
[2025-09-05 02:11:14] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -53.363417+0.001874j
[2025-09-05 02:11:25] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -53.288979-0.001666j
[2025-09-05 02:11:35] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -53.327284+0.000782j
[2025-09-05 02:11:45] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -53.259281+0.002646j
[2025-09-05 02:11:55] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -53.364309-0.003074j
[2025-09-05 02:12:05] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -53.204992+0.000056j
[2025-09-05 02:12:15] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -53.236251-0.002190j
[2025-09-05 02:12:25] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -53.252755-0.000148j
[2025-09-05 02:12:35] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -53.374670-0.004124j
[2025-09-05 02:12:45] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -53.463355-0.002420j
[2025-09-05 02:12:55] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -53.317768-0.003268j
[2025-09-05 02:13:06] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -53.288333-0.002561j
[2025-09-05 02:13:16] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -53.255154+0.000199j
[2025-09-05 02:13:26] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -53.297310+0.000099j
[2025-09-05 02:13:36] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -53.365116-0.000259j
[2025-09-05 02:13:46] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -53.331556+0.001070j
[2025-09-05 02:13:56] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -53.323639-0.001498j
[2025-09-05 02:14:06] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -53.458274+0.000922j
[2025-09-05 02:14:16] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -53.310449+0.002225j
[2025-09-05 02:14:26] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -53.312499-0.001757j
[2025-09-05 02:14:36] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -53.303086-0.000667j
[2025-09-05 02:14:46] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -53.218770+0.003619j
[2025-09-05 02:14:46] RESTART #2 | Period: 600
[2025-09-05 02:14:57] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -53.204625-0.001612j
[2025-09-05 02:15:07] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -53.244631-0.000161j
[2025-09-05 02:15:17] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -53.344191+0.000414j
[2025-09-05 02:15:27] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -53.338844-0.002458j
[2025-09-05 02:15:37] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -53.155489+0.002134j
[2025-09-05 02:15:47] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -53.200311+0.000067j
[2025-09-05 02:15:57] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -53.251182+0.000982j
[2025-09-05 02:16:07] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -53.173261+0.002824j
[2025-09-05 02:16:17] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -53.184108-0.001537j
[2025-09-05 02:16:27] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -53.311382-0.001051j
[2025-09-05 02:16:38] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -53.250636+0.003264j
[2025-09-05 02:16:48] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -53.338094-0.001792j
[2025-09-05 02:16:58] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -53.479462-0.001751j
[2025-09-05 02:17:08] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -53.394760+0.002617j
[2025-09-05 02:17:18] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -53.366851-0.001403j
[2025-09-05 02:17:28] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -53.512719-0.001866j
[2025-09-05 02:17:38] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -53.378821+0.000941j
[2025-09-05 02:17:48] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -53.285588+0.001039j
[2025-09-05 02:17:58] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -53.268357+0.002141j
[2025-09-05 02:18:09] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -53.238196+0.002379j
[2025-09-05 02:18:19] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -53.218148-0.001994j
[2025-09-05 02:18:29] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -53.154317+0.001944j
[2025-09-05 02:18:39] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -53.312282-0.002106j
[2025-09-05 02:18:49] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -53.136260+0.000650j
[2025-09-05 02:18:59] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -53.154404+0.000411j
[2025-09-05 02:19:09] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -53.301303+0.002987j
[2025-09-05 02:19:19] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -53.343316-0.001503j
[2025-09-05 02:19:29] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -53.275430+0.000307j
[2025-09-05 02:19:40] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -53.393305-0.000997j
[2025-09-05 02:19:50] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -53.372775-0.000996j
[2025-09-05 02:20:00] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -53.326344-0.001725j
[2025-09-05 02:20:10] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -53.357150-0.001042j
[2025-09-05 02:20:20] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -53.516749-0.004090j
[2025-09-05 02:20:30] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -53.440514-0.002872j
[2025-09-05 02:20:40] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -53.509338+0.003776j
[2025-09-05 02:20:50] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -53.550736+0.000595j
[2025-09-05 02:21:00] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -53.648396+0.001796j
[2025-09-05 02:21:10] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -53.385047-0.000139j
[2025-09-05 02:21:21] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -53.378202+0.003806j
[2025-09-05 02:21:31] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -53.372895-0.000940j
[2025-09-05 02:21:41] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -53.382535-0.001324j
[2025-09-05 02:21:51] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -53.500159-0.001760j
[2025-09-05 02:22:01] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -53.388765-0.001738j
[2025-09-05 02:22:11] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -53.344746-0.004450j
[2025-09-05 02:22:21] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -53.304066+0.002255j
[2025-09-05 02:22:31] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -53.379940-0.001072j
[2025-09-05 02:22:41] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -53.326435-0.002407j
[2025-09-05 02:22:52] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -53.261782-0.001854j
[2025-09-05 02:23:02] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -53.180273-0.006343j
[2025-09-05 02:23:12] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -53.243192+0.004016j
[2025-09-05 02:23:22] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -53.214039-0.000424j
[2025-09-05 02:23:32] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -53.181370-0.000475j
[2025-09-05 02:23:42] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -53.184984+0.000725j
[2025-09-05 02:23:52] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -53.402196-0.001363j
[2025-09-05 02:24:02] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -53.371129+0.001556j
[2025-09-05 02:24:12] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -53.291907+0.000285j
[2025-09-05 02:24:23] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -53.339270+0.000812j
[2025-09-05 02:24:33] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -53.410326-0.004250j
[2025-09-05 02:24:43] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -53.395436+0.002347j
[2025-09-05 02:24:53] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -53.524938+0.000747j
[2025-09-05 02:25:03] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -53.491187+0.003123j
[2025-09-05 02:25:13] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -53.315401+0.000773j
[2025-09-05 02:25:23] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -53.232630-0.003037j
[2025-09-05 02:25:33] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -53.325621+0.002354j
[2025-09-05 02:25:44] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -53.316240+0.000504j
[2025-09-05 02:25:54] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -53.222849+0.001255j
[2025-09-05 02:26:04] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -53.279551-0.001047j
[2025-09-05 02:26:14] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -53.191568+0.000340j
[2025-09-05 02:26:25] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -53.317034+0.002224j
[2025-09-05 02:26:35] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -53.376344-0.002242j
[2025-09-05 02:26:45] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -53.349949-0.003608j
[2025-09-05 02:26:55] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -53.383732-0.001843j
[2025-09-05 02:27:05] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -53.382359+0.002563j
[2025-09-05 02:27:15] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -53.426894-0.001747j
[2025-09-05 02:27:25] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -53.408948-0.001212j
[2025-09-05 02:27:25] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-05 02:27:35] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -53.414784-0.004053j
[2025-09-05 02:27:45] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -53.429208-0.001014j
[2025-09-05 02:27:56] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -53.518117-0.001687j
[2025-09-05 02:28:06] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -53.446896-0.000235j
[2025-09-05 02:28:16] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -53.603428+0.001398j
[2025-09-05 02:28:26] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -53.534443+0.002165j
[2025-09-05 02:28:36] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -53.550699-0.000796j
[2025-09-05 02:28:46] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -53.392315+0.001520j
[2025-09-05 02:28:56] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -53.270239+0.000304j
[2025-09-05 02:29:06] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -53.394861-0.000054j
[2025-09-05 02:29:16] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -53.446178-0.000897j
[2025-09-05 02:29:26] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -53.410110+0.005117j
[2025-09-05 02:29:37] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -53.448946-0.000397j
[2025-09-05 02:29:47] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -53.383745-0.002860j
[2025-09-05 02:29:57] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -53.412206+0.001358j
[2025-09-05 02:30:07] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -53.366147-0.001982j
[2025-09-05 02:30:17] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -53.383441-0.005140j
[2025-09-05 02:30:27] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -53.226128+0.000061j
[2025-09-05 02:30:37] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -53.396936-0.000552j
[2025-09-05 02:30:47] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -53.498882-0.000250j
[2025-09-05 02:30:57] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -53.318142+0.002260j
[2025-09-05 02:31:07] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -53.315402+0.002897j
[2025-09-05 02:31:18] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -53.310137+0.001184j
[2025-09-05 02:31:28] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -53.357120-0.001061j
[2025-09-05 02:31:38] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -53.257607+0.002144j
[2025-09-05 02:31:48] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -53.352162-0.000686j
[2025-09-05 02:31:58] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -53.492140-0.001627j
[2025-09-05 02:32:08] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -53.456840+0.001934j
[2025-09-05 02:32:18] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -53.392208-0.001119j
[2025-09-05 02:32:29] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -53.392739+0.000575j
[2025-09-05 02:32:39] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -53.490812+0.002565j
[2025-09-05 02:32:49] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -53.406976+0.001060j
[2025-09-05 02:32:59] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -53.405797-0.003452j
[2025-09-05 02:33:09] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -53.157646-0.001534j
[2025-09-05 02:33:19] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -53.247323+0.000218j
[2025-09-05 02:33:29] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -53.397260-0.000438j
[2025-09-05 02:33:39] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -53.354958-0.000153j
[2025-09-05 02:33:49] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -53.366142+0.000360j
[2025-09-05 02:33:59] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -53.422877+0.000923j
[2025-09-05 02:34:10] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -53.293282-0.001019j
[2025-09-05 02:34:20] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -53.316064+0.003632j
[2025-09-05 02:34:30] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -53.290002+0.003244j
[2025-09-05 02:34:40] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -53.360703-0.000382j
[2025-09-05 02:34:50] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -53.436103+0.000991j
[2025-09-05 02:35:00] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -53.499921+0.000945j
[2025-09-05 02:35:10] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -53.389237-0.001555j
[2025-09-05 02:35:20] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -53.397913+0.000898j
[2025-09-05 02:35:30] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -53.354408+0.000537j
[2025-09-05 02:35:40] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -53.418030-0.001113j
[2025-09-05 02:35:50] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -53.448911-0.000311j
[2025-09-05 02:36:01] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -53.447347-0.000883j
[2025-09-05 02:36:11] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -53.382291+0.001135j
[2025-09-05 02:36:21] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -53.435238-0.003994j
[2025-09-05 02:36:31] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -53.420747-0.002768j
[2025-09-05 02:36:41] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -53.277568+0.002051j
[2025-09-05 02:36:51] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -53.273805-0.000978j
[2025-09-05 02:37:01] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -53.256004-0.001557j
[2025-09-05 02:37:11] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -53.322182-0.001301j
[2025-09-05 02:37:21] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -53.390410+0.003080j
[2025-09-05 02:37:31] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -53.383478+0.000475j
[2025-09-05 02:37:42] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -53.341808+0.004909j
[2025-09-05 02:37:52] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -53.314716+0.003810j
[2025-09-05 02:38:02] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -53.413674-0.004445j
[2025-09-05 02:38:12] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -53.435682-0.002677j
[2025-09-05 02:38:22] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -53.294912+0.000874j
[2025-09-05 02:38:32] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -53.404947+0.001289j
[2025-09-05 02:38:42] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -53.370149-0.001324j
[2025-09-05 02:38:52] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -53.327999-0.001078j
[2025-09-05 02:39:02] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -53.399101+0.003020j
[2025-09-05 02:39:12] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -53.338149+0.001922j
[2025-09-05 02:39:23] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -53.355118-0.000427j
[2025-09-05 02:39:33] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -53.391105+0.002909j
[2025-09-05 02:39:43] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -53.333777-0.001945j
[2025-09-05 02:39:53] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -53.249711+0.001584j
[2025-09-05 02:40:03] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -53.252410+0.001062j
[2025-09-05 02:40:13] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -53.253673-0.001794j
[2025-09-05 02:40:23] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -53.310402-0.001729j
[2025-09-05 02:40:33] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -53.298179+0.002402j
[2025-09-05 02:40:43] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -53.345754-0.001150j
[2025-09-05 02:40:53] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -53.314308+0.003632j
[2025-09-05 02:41:03] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -53.269767+0.001082j
[2025-09-05 02:41:14] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -53.315282+0.001368j
[2025-09-05 02:41:24] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -53.277335-0.000952j
[2025-09-05 02:41:34] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -53.368908+0.001761j
[2025-09-05 02:41:44] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -53.252105+0.000060j
[2025-09-05 02:41:54] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -53.367882+0.005665j
[2025-09-05 02:42:04] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -53.264077+0.001062j
[2025-09-05 02:42:14] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -53.360345+0.003101j
[2025-09-05 02:42:24] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -53.381915-0.000768j
[2025-09-05 02:42:35] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -53.370721-0.000780j
[2025-09-05 02:42:45] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -53.379187-0.000611j
[2025-09-05 02:42:55] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -53.413575-0.001079j
[2025-09-05 02:43:05] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -53.438006+0.001578j
[2025-09-05 02:43:15] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -53.397473+0.002813j
[2025-09-05 02:43:25] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -53.419003+0.000346j
[2025-09-05 02:43:35] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -53.334427-0.000111j
[2025-09-05 02:43:45] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -53.250866-0.000386j
[2025-09-05 02:43:55] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -53.439593-0.003985j
[2025-09-05 02:44:05] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -53.251883-0.001686j
[2025-09-05 02:44:16] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -53.399254-0.001945j
[2025-09-05 02:44:26] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -53.431559-0.000271j
[2025-09-05 02:44:36] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -53.319652-0.004274j
[2025-09-05 02:44:46] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -53.339921-0.002769j
[2025-09-05 02:44:56] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -53.376708-0.003487j
[2025-09-05 02:45:06] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -53.344022-0.001184j
[2025-09-05 02:45:06] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-05 02:45:16] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -53.262501-0.001263j
[2025-09-05 02:45:26] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -53.318065+0.000149j
[2025-09-05 02:45:36] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -53.355682+0.003742j
[2025-09-05 02:45:46] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -53.280074+0.003522j
[2025-09-05 02:45:57] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -53.257504+0.001619j
[2025-09-05 02:46:07] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -53.326583+0.000333j
[2025-09-05 02:46:17] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -53.352025+0.003940j
[2025-09-05 02:46:27] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -53.266423-0.000236j
[2025-09-05 02:46:37] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -53.357521-0.000151j
[2025-09-05 02:46:47] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -53.352036+0.000098j
[2025-09-05 02:46:57] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -53.348630+0.000292j
[2025-09-05 02:47:07] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -53.310946-0.000877j
[2025-09-05 02:47:17] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -53.472776+0.005340j
[2025-09-05 02:47:27] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -53.387212+0.000969j
[2025-09-05 02:47:38] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -53.382451-0.002089j
[2025-09-05 02:47:48] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -53.518225+0.002785j
[2025-09-05 02:47:58] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -53.333186+0.000952j
[2025-09-05 02:48:08] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -53.289677-0.000382j
[2025-09-05 02:48:18] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -53.276410+0.002325j
[2025-09-05 02:48:28] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -53.227402+0.001186j
[2025-09-05 02:48:38] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -53.293646-0.000438j
[2025-09-05 02:48:48] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -53.363974-0.000859j
[2025-09-05 02:48:58] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -53.162852-0.001949j
[2025-09-05 02:49:09] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -53.317838-0.002863j
[2025-09-05 02:49:19] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -53.350832+0.001669j
[2025-09-05 02:49:29] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -53.377983-0.001000j
[2025-09-05 02:49:39] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -53.388903-0.000704j
[2025-09-05 02:49:49] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -53.346728+0.002121j
[2025-09-05 02:49:59] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -53.312992-0.001059j
[2025-09-05 02:50:09] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -53.348386+0.001982j
[2025-09-05 02:50:19] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -53.452127-0.000818j
[2025-09-05 02:50:29] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -53.412357-0.003385j
[2025-09-05 02:50:40] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -53.409880-0.000221j
[2025-09-05 02:50:50] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -53.384335+0.000093j
[2025-09-05 02:51:00] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -53.393500+0.002571j
[2025-09-05 02:51:10] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -53.301304-0.000178j
[2025-09-05 02:51:20] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -53.404467+0.002851j
[2025-09-05 02:51:30] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -53.312362-0.001398j
[2025-09-05 02:51:40] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -53.261340-0.000352j
[2025-09-05 02:51:50] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -53.298845-0.000606j
[2025-09-05 02:52:00] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -53.366038+0.001384j
[2025-09-05 02:52:10] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -53.295133-0.001585j
[2025-09-05 02:52:21] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -53.289566-0.001808j
[2025-09-05 02:52:31] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -53.448051+0.001551j
[2025-09-05 02:52:41] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -53.395099+0.003567j
[2025-09-05 02:52:51] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -53.403079+0.005332j
[2025-09-05 02:53:01] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -53.232545-0.000425j
[2025-09-05 02:53:11] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -53.199173+0.001492j
[2025-09-05 02:53:21] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -53.225625-0.002969j
[2025-09-05 02:53:31] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -53.173177+0.000375j
[2025-09-05 02:53:41] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -53.239625+0.002347j
[2025-09-05 02:53:52] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -53.299229+0.000224j
[2025-09-05 02:54:02] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -53.216692+0.002987j
[2025-09-05 02:54:12] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -53.414250-0.000239j
[2025-09-05 02:54:22] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -53.436612-0.001452j
[2025-09-05 02:54:32] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -53.281997-0.002238j
[2025-09-05 02:54:42] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -53.512949-0.001250j
[2025-09-05 02:54:52] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -53.548830-0.001579j
[2025-09-05 02:55:02] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -53.346244+0.001756j
[2025-09-05 02:55:13] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -53.357675-0.003294j
[2025-09-05 02:55:23] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -53.289780-0.000229j
[2025-09-05 02:55:33] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -53.324518-0.001604j
[2025-09-05 02:55:43] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -53.257400+0.000695j
[2025-09-05 02:55:53] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -53.231213-0.001556j
[2025-09-05 02:56:03] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -53.172941-0.002135j
[2025-09-05 02:56:13] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -53.222960+0.000166j
[2025-09-05 02:56:23] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -53.352238+0.004793j
[2025-09-05 02:56:33] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -53.318354-0.000341j
[2025-09-05 02:56:43] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -53.380358-0.001544j
[2025-09-05 02:56:54] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -53.359339+0.002067j
[2025-09-05 02:57:04] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -53.336874+0.002696j
[2025-09-05 02:57:14] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -53.327723-0.001933j
[2025-09-05 02:57:24] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -53.245176+0.002521j
[2025-09-05 02:57:34] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -53.219475-0.001329j
[2025-09-05 02:57:44] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -53.110534-0.001011j
[2025-09-05 02:57:54] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -53.203329+0.000906j
[2025-09-05 02:58:04] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -53.217167-0.004220j
[2025-09-05 02:58:14] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -53.232090-0.003034j
[2025-09-05 02:58:25] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -53.225891-0.004681j
[2025-09-05 02:58:35] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -53.285898-0.002334j
[2025-09-05 02:58:45] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -53.331688-0.000345j
[2025-09-05 02:58:55] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -53.396389+0.000057j
[2025-09-05 02:59:05] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -53.379364+0.003669j
[2025-09-05 02:59:15] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -53.340716-0.001224j
[2025-09-05 02:59:25] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -53.297662+0.001243j
[2025-09-05 02:59:35] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -53.448209+0.001015j
[2025-09-05 02:59:45] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -53.451393-0.000879j
[2025-09-05 02:59:56] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -53.444759+0.003918j
[2025-09-05 03:00:06] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -53.387563+0.000497j
[2025-09-05 03:00:16] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -53.395612-0.002642j
[2025-09-05 03:00:26] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -53.422999-0.000764j
[2025-09-05 03:00:36] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -53.322750+0.001089j
[2025-09-05 03:00:46] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -53.310572-0.001073j
[2025-09-05 03:00:56] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -53.327636-0.000320j
[2025-09-05 03:01:06] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -53.230192-0.002759j
[2025-09-05 03:01:16] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -53.093761+0.001047j
[2025-09-05 03:01:26] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -53.261381-0.001737j
[2025-09-05 03:01:37] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -53.435652+0.000805j
[2025-09-05 03:01:47] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -53.357092+0.003715j
[2025-09-05 03:01:57] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -53.300250-0.003013j
[2025-09-05 03:02:07] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -53.420082-0.001559j
[2025-09-05 03:02:17] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -53.393031+0.001232j
[2025-09-05 03:02:27] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -53.295242+0.003587j
[2025-09-05 03:02:37] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -53.266674-0.002611j
[2025-09-05 03:02:47] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -53.403806-0.000055j
[2025-09-05 03:02:47] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-05 03:02:57] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -53.420511-0.000880j
[2025-09-05 03:03:08] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -53.424517-0.003266j
[2025-09-05 03:03:18] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -53.451751-0.005292j
[2025-09-05 03:03:28] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -53.382898+0.001525j
[2025-09-05 03:03:38] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -53.326058-0.002193j
[2025-09-05 03:03:48] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -53.416947-0.000487j
[2025-09-05 03:03:58] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -53.372450+0.000761j
[2025-09-05 03:04:08] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -53.306499-0.001964j
[2025-09-05 03:04:18] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -53.355098+0.000010j
[2025-09-05 03:04:28] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -53.327986-0.000406j
[2025-09-05 03:04:38] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -53.219100-0.000854j
[2025-09-05 03:04:49] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -53.246501+0.000685j
[2025-09-05 03:04:59] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -53.412754+0.003073j
[2025-09-05 03:05:09] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -53.415765+0.005597j
[2025-09-05 03:05:19] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -53.396006-0.000010j
[2025-09-05 03:05:29] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -53.278534+0.005206j
[2025-09-05 03:05:39] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -53.297139-0.000370j
[2025-09-05 03:05:49] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -53.283201-0.003394j
[2025-09-05 03:05:59] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -53.238536-0.002139j
[2025-09-05 03:06:09] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -53.320980-0.002598j
[2025-09-05 03:06:19] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -53.463916-0.001313j
[2025-09-05 03:06:30] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -53.373132-0.002605j
[2025-09-05 03:06:40] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -53.426035-0.001777j
[2025-09-05 03:06:50] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -53.416074+0.003551j
[2025-09-05 03:07:00] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -53.345964-0.000755j
[2025-09-05 03:07:10] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -53.430215-0.000920j
[2025-09-05 03:07:20] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -53.327119-0.001565j
[2025-09-05 03:07:30] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -53.427650+0.002992j
[2025-09-05 03:07:40] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -53.316799-0.001391j
[2025-09-05 03:07:51] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -53.281202-0.001916j
[2025-09-05 03:08:01] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -53.226724-0.000178j
[2025-09-05 03:08:11] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -53.332524-0.003234j
[2025-09-05 03:08:21] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -53.185773+0.000954j
[2025-09-05 03:08:31] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -53.378383+0.002279j
[2025-09-05 03:08:41] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -53.520221+0.000468j
[2025-09-05 03:08:51] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -53.400134+0.002773j
[2025-09-05 03:09:01] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -53.416930+0.000434j
[2025-09-05 03:09:11] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -53.359439+0.001844j
[2025-09-05 03:09:22] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -53.492949+0.001569j
[2025-09-05 03:09:32] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -53.367964+0.001723j
[2025-09-05 03:09:42] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -53.420826-0.000433j
[2025-09-05 03:09:52] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -53.385897+0.002613j
[2025-09-05 03:10:02] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -53.400626-0.002992j
[2025-09-05 03:10:12] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -53.308842+0.001489j
[2025-09-05 03:10:22] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -53.374112-0.001612j
[2025-09-05 03:10:32] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -53.552105+0.000542j
[2025-09-05 03:10:42] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -53.412084+0.000972j
[2025-09-05 03:10:53] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -53.374437-0.000776j
[2025-09-05 03:11:03] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -53.293809-0.003530j
[2025-09-05 03:11:13] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -53.231540+0.000244j
[2025-09-05 03:11:23] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -53.338746-0.000918j
[2025-09-05 03:11:33] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -53.356594-0.002201j
[2025-09-05 03:11:43] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -53.362723+0.000452j
[2025-09-05 03:11:53] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -53.448194+0.003343j
[2025-09-05 03:12:03] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -53.441480+0.002202j
[2025-09-05 03:12:13] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -53.332225-0.001850j
[2025-09-05 03:12:24] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -53.280187+0.004303j
[2025-09-05 03:12:34] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -53.357241+0.000893j
[2025-09-05 03:12:44] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -53.352745-0.000900j
[2025-09-05 03:12:54] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -53.321302+0.001349j
[2025-09-05 03:13:04] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -53.327056-0.000647j
[2025-09-05 03:13:14] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -53.215537-0.000853j
[2025-09-05 03:13:24] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -53.209351-0.000207j
[2025-09-05 03:13:34] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -53.228166-0.003092j
[2025-09-05 03:13:44] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -53.243770+0.000935j
[2025-09-05 03:13:54] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -53.253651+0.000391j
[2025-09-05 03:14:05] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -53.368923-0.000844j
[2025-09-05 03:14:15] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -53.282590-0.001561j
[2025-09-05 03:14:25] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -53.412276-0.004346j
[2025-09-05 03:14:35] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -53.320506+0.002676j
[2025-09-05 03:14:45] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -53.303491+0.001873j
[2025-09-05 03:14:55] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -53.380082+0.000310j
[2025-09-05 03:15:05] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -53.421993-0.005311j
[2025-09-05 03:15:15] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -53.301349+0.001058j
[2025-09-05 03:15:25] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -53.288156+0.002889j
[2025-09-05 03:15:36] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -53.381585+0.001226j
[2025-09-05 03:15:46] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -53.397008+0.001225j
[2025-09-05 03:15:56] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -53.371259+0.000020j
[2025-09-05 03:16:06] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -53.376515+0.000482j
[2025-09-05 03:16:16] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -53.403045+0.002337j
[2025-09-05 03:16:26] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -53.516250-0.002656j
[2025-09-05 03:16:36] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -53.400277-0.000398j
[2025-09-05 03:16:46] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -53.399309-0.001390j
[2025-09-05 03:16:56] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -53.332331+0.001504j
[2025-09-05 03:17:07] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -53.360846-0.000292j
[2025-09-05 03:17:17] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -53.331063+0.002853j
[2025-09-05 03:17:27] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -53.364554-0.000369j
[2025-09-05 03:17:37] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -53.302696-0.004014j
[2025-09-05 03:17:47] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -53.397217-0.000910j
[2025-09-05 03:17:57] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -53.266352-0.000392j
[2025-09-05 03:18:07] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -53.349485+0.001612j
[2025-09-05 03:18:17] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -53.255293+0.003061j
[2025-09-05 03:18:27] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -53.135264+0.002868j
[2025-09-05 03:18:38] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -53.226815-0.001768j
[2025-09-05 03:18:48] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -53.232741+0.000810j
[2025-09-05 03:18:58] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -53.335404-0.005529j
[2025-09-05 03:19:08] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -53.306222+0.001801j
[2025-09-05 03:19:18] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -53.276949-0.001923j
[2025-09-05 03:19:28] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -53.450108-0.003042j
[2025-09-05 03:19:38] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -53.321441-0.000245j
[2025-09-05 03:19:48] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -53.316421-0.004935j
[2025-09-05 03:19:58] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -53.284721-0.001459j
[2025-09-05 03:20:08] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -53.261979-0.000415j
[2025-09-05 03:20:19] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -53.237337-0.004420j
[2025-09-05 03:20:29] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -53.269545-0.000167j
[2025-09-05 03:20:29] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-05 03:20:39] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -53.277438+0.001545j
[2025-09-05 03:20:49] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -53.295927-0.003449j
[2025-09-05 03:20:59] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -53.366192-0.001646j
[2025-09-05 03:21:09] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -53.279627-0.002666j
[2025-09-05 03:21:19] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -53.163381+0.001391j
[2025-09-05 03:21:29] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -53.261499-0.000451j
[2025-09-05 03:21:39] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -53.198349+0.000983j
[2025-09-05 03:21:50] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -53.269304-0.003389j
[2025-09-05 03:22:00] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -53.260271+0.000977j
[2025-09-05 03:22:10] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -53.107055+0.005657j
[2025-09-05 03:22:20] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -53.242349-0.000044j
[2025-09-05 03:22:30] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -53.229818+0.002528j
[2025-09-05 03:22:40] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -53.183193+0.002140j
[2025-09-05 03:22:50] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -53.124291+0.003770j
[2025-09-05 03:23:00] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -53.404941+0.000516j
[2025-09-05 03:23:10] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -53.412223-0.001422j
[2025-09-05 03:23:20] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -53.457541-0.002033j
[2025-09-05 03:23:31] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -53.408811+0.002578j
[2025-09-05 03:23:41] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -53.412238+0.000105j
[2025-09-05 03:23:51] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -53.326112-0.001541j
[2025-09-05 03:24:01] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -53.290367-0.000554j
[2025-09-05 03:24:11] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -53.241200-0.000322j
[2025-09-05 03:24:21] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -53.313465-0.001161j
[2025-09-05 03:24:31] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -53.279617-0.000221j
[2025-09-05 03:24:41] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -53.329550+0.000515j
[2025-09-05 03:24:51] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -53.158792-0.000133j
[2025-09-05 03:25:01] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -53.290670-0.000932j
[2025-09-05 03:25:11] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -53.221499-0.002276j
[2025-09-05 03:25:22] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -53.372992-0.002636j
[2025-09-05 03:25:32] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -53.373502-0.000534j
[2025-09-05 03:25:42] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -53.265669+0.004242j
[2025-09-05 03:25:52] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -53.344157+0.000428j
[2025-09-05 03:26:02] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -53.273692-0.000651j
[2025-09-05 03:26:12] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -53.264343+0.001945j
[2025-09-05 03:26:22] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -53.383810+0.000564j
[2025-09-05 03:26:32] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -53.325253-0.000372j
[2025-09-05 03:26:43] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -53.503634-0.001277j
[2025-09-05 03:26:53] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -53.381190+0.002247j
[2025-09-05 03:27:03] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -53.404382+0.000583j
[2025-09-05 03:27:13] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -53.257420+0.001901j
[2025-09-05 03:27:23] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -53.391259-0.002250j
[2025-09-05 03:27:33] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -53.396432-0.002235j
[2025-09-05 03:27:43] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -53.370329+0.005546j
[2025-09-05 03:27:53] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -53.412816-0.002606j
[2025-09-05 03:28:03] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -53.275047+0.001647j
[2025-09-05 03:28:13] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -53.390182+0.000384j
[2025-09-05 03:28:24] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -53.466647-0.001021j
[2025-09-05 03:28:34] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -53.436390+0.000342j
[2025-09-05 03:28:44] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -53.455522+0.001743j
[2025-09-05 03:28:54] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -53.309315-0.001622j
[2025-09-05 03:29:04] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -53.338881-0.000290j
[2025-09-05 03:29:14] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -53.361958-0.000728j
[2025-09-05 03:29:24] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -53.281804+0.001490j
[2025-09-05 03:29:34] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -53.318665+0.000243j
[2025-09-05 03:29:44] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -53.428039-0.001742j
[2025-09-05 03:29:54] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -53.257186+0.000291j
[2025-09-05 03:30:04] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -53.348488+0.002145j
[2025-09-05 03:30:15] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -53.307561-0.001508j
[2025-09-05 03:30:25] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -53.331049-0.001549j
[2025-09-05 03:30:35] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -53.365779+0.000959j
[2025-09-05 03:30:45] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -53.409763+0.000287j
[2025-09-05 03:30:55] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -53.428318+0.002679j
[2025-09-05 03:31:05] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -53.285406-0.000268j
[2025-09-05 03:31:15] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -53.177880+0.001262j
[2025-09-05 03:31:25] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -53.328385+0.002017j
[2025-09-05 03:31:35] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -53.408205+0.001789j
[2025-09-05 03:31:45] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -53.413312+0.001541j
[2025-09-05 03:31:55] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -53.296485-0.001695j
[2025-09-05 03:32:06] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -53.398612-0.002554j
[2025-09-05 03:32:16] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -53.430314-0.001472j
[2025-09-05 03:32:26] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -53.333428+0.000802j
[2025-09-05 03:32:36] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -53.443471+0.005273j
[2025-09-05 03:32:46] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -53.481987+0.003404j
[2025-09-05 03:32:56] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -53.225387-0.000327j
[2025-09-05 03:33:06] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -53.363312+0.001427j
[2025-09-05 03:33:16] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -53.335413-0.000937j
[2025-09-05 03:33:26] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -53.351615-0.001957j
[2025-09-05 03:33:36] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -53.344366-0.003431j
[2025-09-05 03:33:46] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -53.351521-0.001101j
[2025-09-05 03:33:57] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -53.422935+0.001298j
[2025-09-05 03:34:07] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -53.372821+0.000215j
[2025-09-05 03:34:17] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -53.433090-0.000665j
[2025-09-05 03:34:27] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -53.418019+0.000593j
[2025-09-05 03:34:37] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -53.338012+0.001775j
[2025-09-05 03:34:47] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -53.400951-0.000704j
[2025-09-05 03:34:57] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -53.218458+0.001137j
[2025-09-05 03:35:07] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -53.247113+0.002221j
[2025-09-05 03:35:17] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -53.284156-0.000868j
[2025-09-05 03:35:27] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -53.383647+0.001614j
[2025-09-05 03:35:37] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -53.452242-0.001545j
[2025-09-05 03:35:48] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -53.327025-0.000946j
[2025-09-05 03:35:58] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -53.370962-0.004649j
[2025-09-05 03:36:08] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -53.292119+0.000355j
[2025-09-05 03:36:18] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -53.364688+0.000126j
[2025-09-05 03:36:28] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -53.457925+0.001896j
[2025-09-05 03:36:38] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -53.400967-0.001414j
[2025-09-05 03:36:48] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -53.339136-0.002035j
[2025-09-05 03:36:58] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -53.383134-0.000583j
[2025-09-05 03:37:08] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -53.286179-0.001544j
[2025-09-05 03:37:19] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -53.446721+0.000818j
[2025-09-05 03:37:29] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -53.474721-0.001665j
[2025-09-05 03:37:39] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -53.446793+0.000556j
[2025-09-05 03:37:49] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -53.397436+0.004179j
[2025-09-05 03:37:59] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -53.294473-0.000558j
[2025-09-05 03:38:09] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -53.315381+0.001784j
[2025-09-05 03:38:09] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-05 03:38:19] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -53.330315+0.002219j
[2025-09-05 03:38:29] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -53.297963+0.002690j
[2025-09-05 03:38:39] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -53.380622-0.001631j
[2025-09-05 03:38:50] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -53.385280-0.005418j
[2025-09-05 03:39:00] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -53.304460-0.003226j
[2025-09-05 03:39:10] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -53.229607-0.000270j
[2025-09-05 03:39:20] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -53.333353-0.001298j
[2025-09-05 03:39:30] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -53.251029+0.002577j
[2025-09-05 03:39:40] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -53.275513+0.001556j
[2025-09-05 03:39:50] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -53.326519+0.003462j
[2025-09-05 03:40:00] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -53.237955-0.001899j
[2025-09-05 03:40:10] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -53.399916-0.000042j
[2025-09-05 03:40:20] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -53.224644+0.000954j
[2025-09-05 03:40:31] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -53.393109+0.002010j
[2025-09-05 03:40:41] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -53.329776+0.001973j
[2025-09-05 03:40:51] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -53.340253-0.001745j
[2025-09-05 03:41:01] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -53.343972-0.002136j
[2025-09-05 03:41:11] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -53.408970+0.000818j
[2025-09-05 03:41:21] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -53.319948-0.001459j
[2025-09-05 03:41:31] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -53.224048+0.000818j
[2025-09-05 03:41:41] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -53.262150+0.000366j
[2025-09-05 03:41:51] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -53.271712-0.000938j
[2025-09-05 03:42:02] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -53.344391-0.001537j
[2025-09-05 03:42:12] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -53.337854+0.001334j
[2025-09-05 03:42:22] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -53.329592-0.001310j
[2025-09-05 03:42:32] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -53.303623-0.001077j
[2025-09-05 03:42:42] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -53.267568-0.004547j
[2025-09-05 03:42:52] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -53.359182+0.000486j
[2025-09-05 03:43:02] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -53.307076-0.001864j
[2025-09-05 03:43:12] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -53.436820+0.000912j
[2025-09-05 03:43:22] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -53.393917-0.003064j
[2025-09-05 03:43:32] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -53.438021-0.003061j
[2025-09-05 03:43:43] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -53.400520-0.001444j
[2025-09-05 03:43:53] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -53.444485+0.002393j
[2025-09-05 03:44:03] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -53.408984+0.003177j
[2025-09-05 03:44:13] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -53.315660+0.000458j
[2025-09-05 03:44:23] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -53.360605+0.001260j
[2025-09-05 03:44:33] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -53.317440-0.001418j
[2025-09-05 03:44:43] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -53.341450-0.001043j
[2025-09-05 03:44:53] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -53.340643-0.002549j
[2025-09-05 03:45:03] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -53.407118+0.000663j
[2025-09-05 03:45:13] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -53.380191-0.001161j
[2025-09-05 03:45:24] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -53.433764-0.001230j
[2025-09-05 03:45:34] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -53.458276+0.000816j
[2025-09-05 03:45:44] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -53.502449-0.000735j
[2025-09-05 03:45:54] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -53.532994-0.002311j
[2025-09-05 03:46:04] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -53.566532-0.000454j
[2025-09-05 03:46:14] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -53.419805+0.003283j
[2025-09-05 03:46:24] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -53.323006+0.002345j
[2025-09-05 03:46:34] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -53.382333+0.003599j
[2025-09-05 03:46:44] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -53.363042-0.001295j
[2025-09-05 03:46:55] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -53.454966+0.001867j
[2025-09-05 03:47:05] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -53.403737-0.002145j
[2025-09-05 03:47:15] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -53.348515-0.001062j
[2025-09-05 03:47:25] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -53.397202+0.004209j
[2025-09-05 03:47:35] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -53.495997+0.001218j
[2025-09-05 03:47:45] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -53.324027+0.001501j
[2025-09-05 03:47:55] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -53.301915-0.001086j
[2025-09-05 03:48:05] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -53.362552-0.001087j
[2025-09-05 03:48:15] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -53.436720+0.002242j
[2025-09-05 03:48:26] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -53.394916+0.000038j
[2025-09-05 03:48:36] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -53.260081-0.000363j
[2025-09-05 03:48:46] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -53.178742-0.001059j
[2025-09-05 03:48:56] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -53.230681-0.000846j
[2025-09-05 03:49:06] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -53.211773+0.000575j
[2025-09-05 03:49:16] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -53.340956+0.004267j
[2025-09-05 03:49:26] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -53.255900-0.000679j
[2025-09-05 03:49:36] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -53.264051-0.002973j
[2025-09-05 03:49:46] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -53.283134-0.000900j
[2025-09-05 03:49:56] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -53.156235+0.001262j
[2025-09-05 03:50:07] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -53.321702-0.001903j
[2025-09-05 03:50:17] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -53.448213+0.001741j
[2025-09-05 03:50:27] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -53.466878-0.001566j
[2025-09-05 03:50:37] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -53.477482-0.003126j
[2025-09-05 03:50:47] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -53.432097-0.001167j
[2025-09-05 03:50:57] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -53.367138-0.001533j
[2025-09-05 03:51:07] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -53.317943-0.001874j
[2025-09-05 03:51:17] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -53.341415-0.001135j
[2025-09-05 03:51:27] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -53.241518-0.000551j
[2025-09-05 03:51:38] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -53.326584-0.004121j
[2025-09-05 03:51:48] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -53.318476+0.000820j
[2025-09-05 03:51:58] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -53.322059-0.002542j
[2025-09-05 03:52:08] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -53.285683+0.001071j
[2025-09-05 03:52:18] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -53.303312-0.001994j
[2025-09-05 03:52:28] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -53.270900+0.002693j
[2025-09-05 03:52:38] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -53.312935-0.001144j
[2025-09-05 03:52:48] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -53.258633+0.000969j
[2025-09-05 03:52:58] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -53.405046-0.002180j
[2025-09-05 03:53:08] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -53.302477+0.003231j
[2025-09-05 03:53:19] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -53.387286-0.001779j
[2025-09-05 03:53:29] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -53.376623-0.002528j
[2025-09-05 03:53:39] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -53.413195+0.001165j
[2025-09-05 03:53:49] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -53.271730-0.001004j
[2025-09-05 03:53:59] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -53.257782+0.001641j
[2025-09-05 03:54:09] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -53.429143-0.000008j
[2025-09-05 03:54:19] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -53.489207+0.001263j
[2025-09-05 03:54:29] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -53.398313+0.001173j
[2025-09-05 03:54:39] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -53.424883+0.000494j
[2025-09-05 03:54:50] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -53.248884+0.003657j
[2025-09-05 03:55:00] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -53.399967-0.002105j
[2025-09-05 03:55:10] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -53.232656-0.001223j
[2025-09-05 03:55:20] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -53.257710+0.000362j
[2025-09-05 03:55:30] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -53.325947+0.001339j
[2025-09-05 03:55:40] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -53.337927+0.002919j
[2025-09-05 03:55:50] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -53.392012+0.002581j
[2025-09-05 03:55:50] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-05 03:55:50] ✅ Training completed | Restarts: 2
[2025-09-05 03:55:50] ============================================================
[2025-09-05 03:55:50] Training completed | Runtime: 10667.2s
[2025-09-05 03:55:54] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-05 03:55:54] ============================================================
