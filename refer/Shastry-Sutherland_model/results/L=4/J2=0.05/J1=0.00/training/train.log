[2025-09-05 06:54:53] ✓ 从checkpoint恢复: results/L=4/J2=0.05/J1=0.01/training/checkpoints/final_GCNN.pkl
[2025-09-05 06:54:53]   - 迭代次数: final
[2025-09-05 06:54:53]   - 能量: -50.619205+0.000173j ± 0.083078
[2025-09-05 06:54:53]   - 时间戳: 2025-09-05T06:53:55.869798+08:00
[2025-09-05 06:55:06] ✓ 变分状态参数已从checkpoint恢复
[2025-09-05 06:55:06] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-05 06:55:06] ==================================================
[2025-09-05 06:55:06] GCNN for Shastry-Sutherland Model
[2025-09-05 06:55:06] ==================================================
[2025-09-05 06:55:06] System parameters:
[2025-09-05 06:55:06]   - System size: L=4, N=64
[2025-09-05 06:55:06]   - System parameters: J1=-0.0, J2=0.05, Q=0.95
[2025-09-05 06:55:06] --------------------------------------------------
[2025-09-05 06:55:06] Model parameters:
[2025-09-05 06:55:06]   - Number of layers = 4
[2025-09-05 06:55:06]   - Number of features = 4
[2025-09-05 06:55:06]   - Total parameters = 12572
[2025-09-05 06:55:06] --------------------------------------------------
[2025-09-05 06:55:06] Training parameters:
[2025-09-05 06:55:06]   - Learning rate: 0.015
[2025-09-05 06:55:06]   - Total iterations: 1050
[2025-09-05 06:55:06]   - Annealing cycles: 3
[2025-09-05 06:55:06]   - Initial period: 150
[2025-09-05 06:55:06]   - Period multiplier: 2.0
[2025-09-05 06:55:06]   - Temperature range: 0.0-1.0
[2025-09-05 06:55:06]   - Samples: 4096
[2025-09-05 06:55:06]   - Discarded samples: 0
[2025-09-05 06:55:06]   - Chunk size: 2048
[2025-09-05 06:55:06]   - Diagonal shift: 0.2
[2025-09-05 06:55:06]   - Gradient clipping: 1.0
[2025-09-05 06:55:06]   - Checkpoint enabled: interval=105
[2025-09-05 06:55:06]   - Checkpoint directory: results/L=4/J2=0.05/J1=-0.00/training/checkpoints
[2025-09-05 06:55:06] --------------------------------------------------
[2025-09-05 06:55:06] Device status:
[2025-09-05 06:55:06]   - Devices model: NVIDIA H200 NVL
[2025-09-05 06:55:06]   - Number of devices: 1
[2025-09-05 06:55:06]   - Sharding: True
[2025-09-05 06:55:06] ============================================================
[2025-09-05 06:55:47] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -50.093040+0.000082j
[2025-09-05 06:56:09] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -50.332256-0.002516j
[2025-09-05 06:56:15] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -50.274564+0.001982j
[2025-09-05 06:56:22] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -50.286068-0.002320j
[2025-09-05 06:56:29] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -50.233836+0.000487j
[2025-09-05 06:56:36] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -50.301430+0.004256j
[2025-09-05 06:56:43] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -50.272299+0.000403j
[2025-09-05 06:56:50] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -50.236375+0.003840j
[2025-09-05 06:56:57] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -50.189016+0.000550j
[2025-09-05 06:57:04] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -50.196747-0.000138j
[2025-09-05 06:57:11] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -50.077102+0.003497j
[2025-09-05 06:57:18] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -50.128004+0.000899j
[2025-09-05 06:57:26] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -50.250791-0.001462j
[2025-09-05 06:57:33] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -50.151566+0.001337j
[2025-09-05 06:57:40] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -50.124204+0.002974j
[2025-09-05 06:57:47] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -50.215337+0.000579j
[2025-09-05 06:57:54] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -50.304634-0.001200j
[2025-09-05 06:58:01] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -50.257858+0.004232j
[2025-09-05 06:58:08] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -50.179846+0.001616j
[2025-09-05 06:58:15] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -50.174538+0.004100j
[2025-09-05 06:58:22] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -50.077792+0.000464j
[2025-09-05 06:58:29] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -50.059137+0.000306j
[2025-09-05 06:58:36] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -49.992814+0.002541j
[2025-09-05 06:58:44] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -50.033341-0.002148j
[2025-09-05 06:58:51] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -49.987946-0.001027j
[2025-09-05 06:58:58] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -50.099821+0.002226j
[2025-09-05 06:59:05] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -50.176612-0.000133j
[2025-09-05 06:59:12] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -50.166593-0.000618j
[2025-09-05 06:59:19] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -50.086453-0.001998j
[2025-09-05 06:59:26] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -50.088895-0.002867j
[2025-09-05 06:59:33] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -50.221736+0.000490j
[2025-09-05 06:59:40] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -50.173082-0.004455j
[2025-09-05 06:59:47] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -50.171336-0.002873j
[2025-09-05 06:59:54] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -50.209865+0.000667j
[2025-09-05 07:00:02] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -50.181349-0.003843j
[2025-09-05 07:00:09] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -50.142548-0.001534j
[2025-09-05 07:00:16] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -50.123191-0.002294j
[2025-09-05 07:00:23] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -50.164318-0.001563j
[2025-09-05 07:00:30] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -50.093993-0.000326j
[2025-09-05 07:00:37] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -50.209917+0.000261j
[2025-09-05 07:00:44] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -50.134205+0.002422j
[2025-09-05 07:00:51] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -50.126519-0.000934j
[2025-09-05 07:00:58] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -50.122731-0.000264j
[2025-09-05 07:01:05] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -50.123480-0.002320j
[2025-09-05 07:01:13] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -50.081059-0.000294j
[2025-09-05 07:01:20] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -50.100678+0.000726j
[2025-09-05 07:01:27] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -50.186937+0.002862j
[2025-09-05 07:01:34] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -50.130456-0.001260j
[2025-09-05 07:01:41] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -50.218285-0.001042j
[2025-09-05 07:01:48] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -50.092563-0.001897j
[2025-09-05 07:01:55] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -50.123061-0.002089j
[2025-09-05 07:02:02] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -50.212223-0.000063j
[2025-09-05 07:02:09] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -50.230174-0.002649j
[2025-09-05 07:02:16] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -50.201455-0.001148j
[2025-09-05 07:02:23] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -50.233621-0.001918j
[2025-09-05 07:02:31] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -50.191767+0.001029j
[2025-09-05 07:02:38] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -50.145853-0.000288j
[2025-09-05 07:02:45] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -50.122500+0.000986j
[2025-09-05 07:02:52] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -50.086884+0.000344j
[2025-09-05 07:02:59] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -50.260126+0.000286j
[2025-09-05 07:03:06] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -50.230135+0.001595j
[2025-09-05 07:03:13] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -50.185860+0.001350j
[2025-09-05 07:03:20] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -50.103325+0.001644j
[2025-09-05 07:03:27] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -50.130618-0.002700j
[2025-09-05 07:03:34] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -50.167672+0.000988j
[2025-09-05 07:03:41] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -50.189278+0.001800j
[2025-09-05 07:03:49] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -50.124752-0.002399j
[2025-09-05 07:03:56] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -50.186115+0.001203j
[2025-09-05 07:04:03] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -50.062879-0.001671j
[2025-09-05 07:04:10] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -50.092649+0.001737j
[2025-09-05 07:04:17] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -50.123479+0.001062j
[2025-09-05 07:04:24] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -50.119215+0.005058j
[2025-09-05 07:04:31] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -50.078798+0.004662j
[2025-09-05 07:04:38] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -50.093822+0.000592j
[2025-09-05 07:04:45] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -50.237752-0.000509j
[2025-09-05 07:04:52] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -50.360435-0.000478j
[2025-09-05 07:05:00] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -50.261608+0.000121j
[2025-09-05 07:05:07] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -50.246875+0.000010j
[2025-09-05 07:05:14] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -50.170526-0.001253j
[2025-09-05 07:05:21] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -50.210243+0.003997j
[2025-09-05 07:05:28] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -50.098549+0.000869j
[2025-09-05 07:05:35] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -50.197303-0.002142j
[2025-09-05 07:05:42] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -50.188088-0.000220j
[2025-09-05 07:05:49] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -50.176656+0.001485j
[2025-09-05 07:05:56] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -50.402371-0.002125j
[2025-09-05 07:06:03] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -50.289182-0.001141j
[2025-09-05 07:06:11] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -50.268762+0.001073j
[2025-09-05 07:06:18] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -50.288952+0.000217j
[2025-09-05 07:06:25] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -50.266266+0.002656j
[2025-09-05 07:06:32] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -50.196220-0.001552j
[2025-09-05 07:06:39] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -50.243621+0.002127j
[2025-09-05 07:06:46] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -50.109456+0.001701j
[2025-09-05 07:06:53] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -50.237888-0.004055j
[2025-09-05 07:07:00] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -50.188735+0.000667j
[2025-09-05 07:07:07] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -50.198204+0.001310j
[2025-09-05 07:07:14] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -50.205550+0.000146j
[2025-09-05 07:07:21] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -50.309805+0.002876j
[2025-09-05 07:07:29] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -50.175794+0.002457j
[2025-09-05 07:07:36] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -50.266409+0.002661j
[2025-09-05 07:07:43] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -50.173807+0.001269j
[2025-09-05 07:07:50] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -50.254450-0.001911j
[2025-09-05 07:07:57] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -50.149525+0.000865j
[2025-09-05 07:08:04] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -50.170497+0.001808j
[2025-09-05 07:08:11] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -50.169426-0.000738j
[2025-09-05 07:08:18] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -50.224045+0.001889j
[2025-09-05 07:08:18] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-05 07:08:25] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -50.403153+0.002337j
[2025-09-05 07:08:32] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -50.340328+0.000284j
[2025-09-05 07:08:39] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -50.324472+0.001131j
[2025-09-05 07:08:47] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -50.253181-0.001773j
[2025-09-05 07:08:54] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -50.218609-0.000527j
[2025-09-05 07:09:01] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -50.236255+0.002743j
[2025-09-05 07:09:08] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -50.221599-0.000349j
[2025-09-05 07:09:15] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -50.150340-0.001708j
[2025-09-05 07:09:22] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -50.131264-0.001555j
[2025-09-05 07:09:29] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -50.054696-0.001696j
[2025-09-05 07:09:36] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -50.208797+0.000062j
[2025-09-05 07:09:43] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -50.274125-0.002022j
[2025-09-05 07:09:50] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -50.234511-0.001460j
[2025-09-05 07:09:58] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -50.193650+0.000315j
[2025-09-05 07:10:05] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -50.358568+0.000666j
[2025-09-05 07:10:12] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -50.217620-0.000128j
[2025-09-05 07:10:19] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -50.246033-0.000065j
[2025-09-05 07:10:26] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -50.144351-0.001353j
[2025-09-05 07:10:33] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -50.182638+0.000859j
[2025-09-05 07:10:40] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -50.102176-0.000787j
[2025-09-05 07:10:47] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -50.097257+0.002129j
[2025-09-05 07:10:54] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -50.127685+0.000170j
[2025-09-05 07:11:01] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -50.178996-0.000599j
[2025-09-05 07:11:08] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -50.138881-0.001026j
[2025-09-05 07:11:16] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -50.137453-0.000917j
[2025-09-05 07:11:23] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -50.205089-0.003003j
[2025-09-05 07:11:30] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -50.252081+0.000533j
[2025-09-05 07:11:37] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -50.277513-0.003639j
[2025-09-05 07:11:44] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -50.228379+0.004101j
[2025-09-05 07:11:51] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -50.271891+0.001543j
[2025-09-05 07:11:58] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -50.296034+0.001735j
[2025-09-05 07:12:05] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -50.212854-0.001123j
[2025-09-05 07:12:12] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -50.257067+0.002038j
[2025-09-05 07:12:19] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -50.216994+0.001042j
[2025-09-05 07:12:26] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -50.227704+0.001968j
[2025-09-05 07:12:34] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -50.195395-0.001176j
[2025-09-05 07:12:41] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -50.236841-0.002900j
[2025-09-05 07:12:48] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -50.210717+0.000394j
[2025-09-05 07:12:55] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -50.311460+0.002364j
[2025-09-05 07:13:02] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -50.302044+0.003797j
[2025-09-05 07:13:09] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -50.346713+0.000509j
[2025-09-05 07:13:16] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -50.330468+0.000580j
[2025-09-05 07:13:23] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -50.268554+0.000858j
[2025-09-05 07:13:30] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -50.238954+0.002234j
[2025-09-05 07:13:37] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -50.233235+0.001573j
[2025-09-05 07:13:37] RESTART #1 | Period: 300
[2025-09-05 07:13:44] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -50.140267+0.001403j
[2025-09-05 07:13:52] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -50.105928+0.002828j
[2025-09-05 07:13:59] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -50.132102-0.003817j
[2025-09-05 07:14:06] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -50.060500+0.000274j
[2025-09-05 07:14:13] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -50.158138+0.002711j
[2025-09-05 07:14:20] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -50.177661-0.003672j
[2025-09-05 07:14:27] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -50.119334+0.000624j
[2025-09-05 07:14:34] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -50.046806-0.000164j
[2025-09-05 07:14:41] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -50.145394-0.005378j
[2025-09-05 07:14:48] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -50.007833-0.000532j
[2025-09-05 07:14:55] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -50.106179-0.000391j
[2025-09-05 07:15:02] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -50.125520+0.000478j
[2025-09-05 07:15:10] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -50.220677-0.005979j
[2025-09-05 07:15:17] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -50.201232-0.002919j
[2025-09-05 07:15:24] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -50.253089-0.000243j
[2025-09-05 07:15:31] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -50.229712+0.001491j
[2025-09-05 07:15:38] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -50.175268-0.001419j
[2025-09-05 07:15:45] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -50.152414+0.000683j
[2025-09-05 07:15:52] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -50.108617-0.001944j
[2025-09-05 07:15:59] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -50.158991-0.003669j
[2025-09-05 07:16:06] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -50.076257+0.002078j
[2025-09-05 07:16:13] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -50.160057-0.000803j
[2025-09-05 07:16:20] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -50.224505-0.000647j
[2025-09-05 07:16:28] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -50.178228+0.003911j
[2025-09-05 07:16:35] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -50.209834+0.000299j
[2025-09-05 07:16:42] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -50.218817+0.001213j
[2025-09-05 07:16:49] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -50.117534+0.001796j
[2025-09-05 07:16:56] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -50.080907+0.000467j
[2025-09-05 07:17:03] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -50.166710+0.000386j
[2025-09-05 07:17:10] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -50.054895-0.000327j
[2025-09-05 07:17:17] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -50.172352-0.000103j
[2025-09-05 07:17:24] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -50.185086+0.001170j
[2025-09-05 07:17:31] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -50.114324+0.001683j
[2025-09-05 07:17:38] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -50.106506-0.002165j
[2025-09-05 07:17:46] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -50.199666+0.001188j
[2025-09-05 07:17:53] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -50.100743-0.003059j
[2025-09-05 07:18:00] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -50.133648+0.000497j
[2025-09-05 07:18:07] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -50.099574+0.001307j
[2025-09-05 07:18:14] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -50.087151-0.001419j
[2025-09-05 07:18:21] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -50.113111+0.000549j
[2025-09-05 07:18:28] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -50.094748-0.001036j
[2025-09-05 07:18:35] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -50.172286-0.003045j
[2025-09-05 07:18:42] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -50.157697-0.001334j
[2025-09-05 07:18:49] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -50.168090-0.000396j
[2025-09-05 07:18:57] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -50.163170+0.000112j
[2025-09-05 07:19:04] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -50.103205+0.005858j
[2025-09-05 07:19:11] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -50.060974+0.003404j
[2025-09-05 07:19:18] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -50.062364+0.000478j
[2025-09-05 07:19:25] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -50.140377+0.003745j
[2025-09-05 07:19:32] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -50.107175+0.002195j
[2025-09-05 07:19:39] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -50.091966+0.000903j
[2025-09-05 07:19:46] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -50.156073+0.000940j
[2025-09-05 07:19:53] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -50.224063-0.003863j
[2025-09-05 07:20:00] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -50.084611-0.000032j
[2025-09-05 07:20:07] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -50.193690+0.000763j
[2025-09-05 07:20:15] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -50.178042+0.001337j
[2025-09-05 07:20:22] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -50.077047+0.001955j
[2025-09-05 07:20:29] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -50.217085+0.001841j
[2025-09-05 07:20:36] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -50.279942-0.001782j
[2025-09-05 07:20:43] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -50.195840-0.000783j
[2025-09-05 07:20:43] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-05 07:20:50] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -50.181520-0.000621j
[2025-09-05 07:20:57] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -50.255601-0.000149j
[2025-09-05 07:21:04] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -50.304235-0.000869j
[2025-09-05 07:21:11] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -50.212573-0.001618j
[2025-09-05 07:21:18] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -50.250775-0.001098j
[2025-09-05 07:21:25] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -50.216658-0.002090j
[2025-09-05 07:21:33] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -50.305492-0.003268j
[2025-09-05 07:21:40] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -50.271145-0.002527j
[2025-09-05 07:21:47] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -50.280012+0.000364j
[2025-09-05 07:21:54] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -50.259386+0.002669j
[2025-09-05 07:22:01] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -50.261226+0.000448j
[2025-09-05 07:22:08] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -50.175661-0.000254j
[2025-09-05 07:22:15] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -50.181957-0.000458j
[2025-09-05 07:22:22] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -50.076654-0.001782j
[2025-09-05 07:22:29] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -50.129251+0.000725j
[2025-09-05 07:22:36] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -50.154537-0.000579j
[2025-09-05 07:22:43] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -50.246691-0.003098j
[2025-09-05 07:22:51] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -50.199574-0.001411j
[2025-09-05 07:22:58] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -50.240432-0.000081j
[2025-09-05 07:23:05] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -50.217700+0.003617j
[2025-09-05 07:23:12] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -50.199395-0.000480j
[2025-09-05 07:23:19] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -50.139677-0.000210j
[2025-09-05 07:23:26] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -50.087644-0.000725j
[2025-09-05 07:23:33] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -50.233193-0.002030j
[2025-09-05 07:23:40] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -50.115507+0.002801j
[2025-09-05 07:23:47] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -50.126262-0.001212j
[2025-09-05 07:23:54] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -50.041009+0.001316j
[2025-09-05 07:24:01] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -50.192686-0.001309j
[2025-09-05 07:24:09] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -50.157057+0.005835j
[2025-09-05 07:24:16] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -50.148312-0.002412j
[2025-09-05 07:24:23] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -50.189772-0.001520j
[2025-09-05 07:24:30] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -50.174075-0.000454j
[2025-09-05 07:24:37] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -50.164500-0.003768j
[2025-09-05 07:24:44] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -50.165857+0.002047j
[2025-09-05 07:24:51] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -50.057926+0.001202j
[2025-09-05 07:24:58] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -50.113431+0.000672j
[2025-09-05 07:25:05] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -50.099781+0.001310j
[2025-09-05 07:25:12] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -50.233305+0.002341j
[2025-09-05 07:25:19] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -50.290875-0.004078j
[2025-09-05 07:25:27] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -50.061454+0.000044j
[2025-09-05 07:25:34] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -50.135571+0.002934j
[2025-09-05 07:25:41] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -50.237207-0.002156j
[2025-09-05 07:25:48] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -50.275791-0.001674j
[2025-09-05 07:25:55] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -50.349301-0.001506j
[2025-09-05 07:26:02] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -50.390389-0.002344j
[2025-09-05 07:26:09] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -50.417273+0.002704j
[2025-09-05 07:26:16] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -50.189163-0.000448j
[2025-09-05 07:26:23] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -50.242053-0.000143j
[2025-09-05 07:26:30] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -50.164864-0.000171j
[2025-09-05 07:26:38] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -50.286979-0.000720j
[2025-09-05 07:26:45] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -50.224489-0.000502j
[2025-09-05 07:26:52] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -50.280898-0.000315j
[2025-09-05 07:26:59] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -50.206934-0.000224j
[2025-09-05 07:27:06] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -50.191734+0.000448j
[2025-09-05 07:27:13] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -50.127225+0.001567j
[2025-09-05 07:27:20] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -50.189200-0.000096j
[2025-09-05 07:27:27] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -50.176405+0.003079j
[2025-09-05 07:27:34] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -50.154355+0.003314j
[2025-09-05 07:27:41] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -50.078992+0.000026j
[2025-09-05 07:27:48] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -50.146153+0.001433j
[2025-09-05 07:27:56] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -50.224765-0.000949j
[2025-09-05 07:28:03] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -50.215379-0.001320j
[2025-09-05 07:28:10] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -50.263254-0.000375j
[2025-09-05 07:28:17] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -50.291886-0.002920j
[2025-09-05 07:28:24] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -50.285930-0.000388j
[2025-09-05 07:28:31] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -50.240511+0.001182j
[2025-09-05 07:28:38] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -50.227043+0.002177j
[2025-09-05 07:28:45] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -50.240245+0.000950j
[2025-09-05 07:28:52] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -50.164196-0.003134j
[2025-09-05 07:28:59] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -50.088547-0.001967j
[2025-09-05 07:29:06] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -50.223457+0.000678j
[2025-09-05 07:29:14] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -50.182687+0.002497j
[2025-09-05 07:29:21] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -50.228101+0.000737j
[2025-09-05 07:29:28] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -50.266075-0.002681j
[2025-09-05 07:29:35] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -50.338718-0.000126j
[2025-09-05 07:29:42] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -50.336612-0.005299j
[2025-09-05 07:29:49] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -50.233575+0.001026j
[2025-09-05 07:29:56] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -50.190954-0.000771j
[2025-09-05 07:30:03] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -50.166736+0.000031j
[2025-09-05 07:30:10] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -50.160511+0.000918j
[2025-09-05 07:30:17] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -50.138022-0.001602j
[2025-09-05 07:30:25] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -50.092785+0.002059j
[2025-09-05 07:30:32] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -50.069841+0.004971j
[2025-09-05 07:30:39] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -50.159855+0.001434j
[2025-09-05 07:30:46] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -50.245045-0.000613j
[2025-09-05 07:30:53] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -50.310494-0.001537j
[2025-09-05 07:31:00] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -50.171212+0.000660j
[2025-09-05 07:31:07] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -50.340670-0.002450j
[2025-09-05 07:31:14] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -50.222403-0.002504j
[2025-09-05 07:31:21] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -50.161074-0.000137j
[2025-09-05 07:31:28] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -50.170531+0.003661j
[2025-09-05 07:31:35] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -50.227819-0.000741j
[2025-09-05 07:31:42] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -50.452214+0.003313j
[2025-09-05 07:31:50] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -50.485745+0.002951j
[2025-09-05 07:31:57] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -50.359447+0.000769j
[2025-09-05 07:32:04] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -50.232025-0.001801j
[2025-09-05 07:32:11] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -50.186579+0.001755j
[2025-09-05 07:32:18] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -50.266996+0.001910j
[2025-09-05 07:32:25] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -50.335308+0.000547j
[2025-09-05 07:32:32] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -50.316274-0.001521j
[2025-09-05 07:32:39] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -50.334231-0.000396j
[2025-09-05 07:32:46] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -50.293655-0.003387j
[2025-09-05 07:32:53] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -50.267418-0.001012j
[2025-09-05 07:33:01] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -50.330304-0.001533j
[2025-09-05 07:33:08] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -50.283122+0.001707j
[2025-09-05 07:33:08] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-05 07:33:15] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -50.392696+0.001127j
[2025-09-05 07:33:22] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -50.172699+0.000204j
[2025-09-05 07:33:29] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -50.227471-0.000003j
[2025-09-05 07:33:36] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -50.223654-0.002498j
[2025-09-05 07:33:43] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -50.061634-0.001788j
[2025-09-05 07:33:50] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -50.188830-0.005655j
[2025-09-05 07:33:57] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -50.180347-0.001305j
[2025-09-05 07:34:04] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -50.130034+0.002780j
[2025-09-05 07:34:11] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -50.098616-0.003229j
[2025-09-05 07:34:19] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -50.159647-0.005093j
[2025-09-05 07:34:26] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -50.113910+0.006211j
[2025-09-05 07:34:33] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -50.130244+0.001641j
[2025-09-05 07:34:40] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -50.180543-0.004367j
[2025-09-05 07:34:47] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -50.138015+0.003592j
[2025-09-05 07:34:54] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -50.190024+0.000351j
[2025-09-05 07:35:01] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -50.279408+0.000036j
[2025-09-05 07:35:08] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -50.228429+0.003557j
[2025-09-05 07:35:15] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -50.090673+0.001584j
[2025-09-05 07:35:22] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -50.136104-0.001354j
[2025-09-05 07:35:29] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -50.092641+0.001545j
[2025-09-05 07:35:37] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -50.122344-0.000105j
[2025-09-05 07:35:44] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -50.133312-0.002732j
[2025-09-05 07:35:51] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -50.178081+0.000007j
[2025-09-05 07:35:58] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -50.047368+0.000616j
[2025-09-05 07:36:05] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -50.094161+0.000365j
[2025-09-05 07:36:12] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -50.140409+0.000737j
[2025-09-05 07:36:19] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -50.186612-0.000665j
[2025-09-05 07:36:26] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -50.224624-0.000907j
[2025-09-05 07:36:33] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -50.177909+0.002158j
[2025-09-05 07:36:40] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -50.131339+0.000177j
[2025-09-05 07:36:47] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -50.132180-0.001711j
[2025-09-05 07:36:55] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -50.020611-0.002965j
[2025-09-05 07:37:02] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -50.090880-0.000532j
[2025-09-05 07:37:09] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -50.138614-0.003137j
[2025-09-05 07:37:16] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -50.168058+0.005965j
[2025-09-05 07:37:23] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -50.197600-0.000356j
[2025-09-05 07:37:30] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -50.189255+0.000761j
[2025-09-05 07:37:37] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -50.143352-0.000139j
[2025-09-05 07:37:44] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -50.147166-0.003190j
[2025-09-05 07:37:51] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -50.139453-0.004355j
[2025-09-05 07:37:58] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -50.240968+0.001776j
[2025-09-05 07:38:05] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -50.256591+0.000479j
[2025-09-05 07:38:13] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -50.350110-0.000453j
[2025-09-05 07:38:20] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -50.158536-0.001703j
[2025-09-05 07:38:27] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -50.142081+0.002655j
[2025-09-05 07:38:34] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -50.129266-0.001163j
[2025-09-05 07:38:41] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -50.060753-0.001849j
[2025-09-05 07:38:48] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -50.046037-0.001072j
[2025-09-05 07:38:55] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -50.243175-0.005671j
[2025-09-05 07:39:02] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -50.229440+0.000495j
[2025-09-05 07:39:09] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -50.133650+0.001406j
[2025-09-05 07:39:16] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -50.251585-0.004014j
[2025-09-05 07:39:23] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -50.242415+0.000624j
[2025-09-05 07:39:31] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -50.178110-0.000379j
[2025-09-05 07:39:38] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -50.207388+0.000696j
[2025-09-05 07:39:45] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -50.165120-0.001067j
[2025-09-05 07:39:52] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -50.175051-0.002231j
[2025-09-05 07:39:59] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -50.205952+0.001747j
[2025-09-05 07:40:06] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -50.143321+0.000921j
[2025-09-05 07:40:13] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -50.210281+0.001034j
[2025-09-05 07:40:20] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -50.157128+0.002150j
[2025-09-05 07:40:27] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -50.276862+0.001086j
[2025-09-05 07:40:34] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -50.217545-0.001679j
[2025-09-05 07:40:41] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -50.141646-0.001978j
[2025-09-05 07:40:49] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -50.199712-0.003085j
[2025-09-05 07:40:56] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -50.218042-0.002320j
[2025-09-05 07:41:03] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -50.174070+0.000906j
[2025-09-05 07:41:10] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -50.195097-0.001748j
[2025-09-05 07:41:17] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -50.161272+0.000448j
[2025-09-05 07:41:24] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -50.166933+0.000013j
[2025-09-05 07:41:31] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -50.079848-0.000136j
[2025-09-05 07:41:38] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -50.129458+0.001407j
[2025-09-05 07:41:45] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -50.166892+0.000210j
[2025-09-05 07:41:52] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -50.155923+0.001236j
[2025-09-05 07:42:00] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -50.004364+0.001853j
[2025-09-05 07:42:07] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -49.997650+0.003300j
[2025-09-05 07:42:14] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -49.963076+0.003783j
[2025-09-05 07:42:21] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -50.182590+0.003694j
[2025-09-05 07:42:28] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -50.270827+0.001185j
[2025-09-05 07:42:35] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -50.146666+0.003261j
[2025-09-05 07:42:42] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -50.187778-0.001030j
[2025-09-05 07:42:49] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -50.019116+0.000948j
[2025-09-05 07:42:56] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -50.273640+0.000676j
[2025-09-05 07:43:03] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -50.216227-0.001713j
[2025-09-05 07:43:10] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -50.235629+0.000467j
[2025-09-05 07:43:18] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -50.154050-0.000732j
[2025-09-05 07:43:25] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -50.270372-0.002061j
[2025-09-05 07:43:32] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -50.260827-0.001736j
[2025-09-05 07:43:39] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -50.270739+0.002668j
[2025-09-05 07:43:46] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -50.293625-0.003879j
[2025-09-05 07:43:53] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -50.247440-0.001267j
[2025-09-05 07:44:00] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -50.295810-0.001792j
[2025-09-05 07:44:07] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -50.268410-0.003264j
[2025-09-05 07:44:14] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -50.309528-0.007217j
[2025-09-05 07:44:21] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -50.110481-0.003011j
[2025-09-05 07:44:29] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -50.145243-0.004748j
[2025-09-05 07:44:36] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -50.296248+0.001588j
[2025-09-05 07:44:43] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -50.349575+0.000160j
[2025-09-05 07:44:50] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -50.142813-0.001716j
[2025-09-05 07:44:57] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -50.147322-0.004186j
[2025-09-05 07:45:04] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -50.184335-0.003127j
[2025-09-05 07:45:11] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -50.221550+0.000269j
[2025-09-05 07:45:18] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -50.267357+0.000591j
[2025-09-05 07:45:25] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -50.326933-0.000696j
[2025-09-05 07:45:32] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -50.271015+0.002172j
[2025-09-05 07:45:32] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-05 07:45:39] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -50.170360+0.001538j
[2025-09-05 07:45:47] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -50.228219-0.001066j
[2025-09-05 07:45:54] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -50.287669-0.001783j
[2025-09-05 07:46:01] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -50.266847+0.000769j
[2025-09-05 07:46:08] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -50.255199+0.000661j
[2025-09-05 07:46:15] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -50.218099+0.002743j
[2025-09-05 07:46:22] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -50.252674+0.003908j
[2025-09-05 07:46:29] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -50.204696+0.002031j
[2025-09-05 07:46:36] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -50.143938+0.000120j
[2025-09-05 07:46:43] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -50.205549+0.000482j
[2025-09-05 07:46:50] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -50.192879-0.001857j
[2025-09-05 07:46:57] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -50.182774+0.000468j
[2025-09-05 07:47:05] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -50.147566-0.001323j
[2025-09-05 07:47:12] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -50.234034-0.000556j
[2025-09-05 07:47:19] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -50.143358-0.004000j
[2025-09-05 07:47:26] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -50.220097-0.000699j
[2025-09-05 07:47:33] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -50.186654-0.001791j
[2025-09-05 07:47:40] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -50.186775-0.000868j
[2025-09-05 07:47:47] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -50.089118-0.002685j
[2025-09-05 07:47:54] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -50.160676+0.001221j
[2025-09-05 07:48:01] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -50.040042+0.000638j
[2025-09-05 07:48:08] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -50.054360-0.001890j
[2025-09-05 07:48:15] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -50.183170-0.002810j
[2025-09-05 07:48:23] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -50.241256-0.002946j
[2025-09-05 07:48:30] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -50.300879-0.000565j
[2025-09-05 07:48:37] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -50.294411+0.000978j
[2025-09-05 07:48:44] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -50.349363+0.001611j
[2025-09-05 07:48:51] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -50.184337-0.001187j
[2025-09-05 07:48:58] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -50.164628+0.001497j
[2025-09-05 07:49:05] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -50.234865+0.000897j
[2025-09-05 07:49:05] RESTART #2 | Period: 600
[2025-09-05 07:49:12] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -50.262287-0.003880j
[2025-09-05 07:49:19] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -50.217343-0.000707j
[2025-09-05 07:49:26] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -50.110638+0.000504j
[2025-09-05 07:49:33] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -50.038099+0.004365j
[2025-09-05 07:49:41] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -49.982413-0.002347j
[2025-09-05 07:49:48] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -49.930822-0.003116j
[2025-09-05 07:49:55] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -50.023619-0.002376j
[2025-09-05 07:50:02] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -49.998631+0.001476j
[2025-09-05 07:50:09] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -50.055489-0.000098j
[2025-09-05 07:50:16] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -50.169956-0.003096j
[2025-09-05 07:50:23] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -50.187154+0.000551j
[2025-09-05 07:50:30] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -50.025023+0.001475j
[2025-09-05 07:50:37] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -50.013622+0.001617j
[2025-09-05 07:50:44] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -50.057570-0.002048j
[2025-09-05 07:50:51] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -50.088709+0.004397j
[2025-09-05 07:50:59] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -50.110282+0.001963j
[2025-09-05 07:51:06] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -50.155900+0.002379j
[2025-09-05 07:51:13] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -50.173754+0.000428j
[2025-09-05 07:51:20] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -50.314411-0.003780j
[2025-09-05 07:51:27] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -50.322516+0.003101j
[2025-09-05 07:51:34] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -50.228549+0.000366j
[2025-09-05 07:51:41] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -50.121897+0.000649j
[2025-09-05 07:51:48] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -50.195715-0.001741j
[2025-09-05 07:51:55] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -50.170559+0.001053j
[2025-09-05 07:52:02] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -50.167131+0.004980j
[2025-09-05 07:52:10] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -50.205698+0.001776j
[2025-09-05 07:52:17] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -50.089526-0.004273j
[2025-09-05 07:52:24] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -50.149467+0.002226j
[2025-09-05 07:52:31] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -50.251201+0.002450j
[2025-09-05 07:52:38] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -50.181856+0.000568j
[2025-09-05 07:52:45] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -50.043040+0.000917j
[2025-09-05 07:52:52] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -50.146084-0.000184j
[2025-09-05 07:52:59] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -50.159430+0.000621j
[2025-09-05 07:53:06] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -50.301855+0.000098j
[2025-09-05 07:53:13] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -50.196777+0.002311j
[2025-09-05 07:53:20] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -50.124447-0.000463j
[2025-09-05 07:53:28] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -50.045167+0.000181j
[2025-09-05 07:53:35] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -50.154355+0.001573j
[2025-09-05 07:53:42] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -50.206270-0.000065j
[2025-09-05 07:53:49] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -50.274200-0.001762j
[2025-09-05 07:53:56] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -50.308580-0.003588j
[2025-09-05 07:54:03] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -50.224253+0.002595j
[2025-09-05 07:54:10] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -50.318725-0.001936j
[2025-09-05 07:54:17] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -50.256655-0.000117j
[2025-09-05 07:54:24] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -50.196352-0.000535j
[2025-09-05 07:54:31] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -50.123759+0.000459j
[2025-09-05 07:54:38] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -50.175636+0.001092j
[2025-09-05 07:54:46] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -50.081981-0.002000j
[2025-09-05 07:54:53] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -50.211341-0.000203j
[2025-09-05 07:55:00] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -50.003521+0.002755j
[2025-09-05 07:55:07] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -50.182117-0.000600j
[2025-09-05 07:55:14] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -50.114429-0.000719j
[2025-09-05 07:55:21] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -50.064014+0.000471j
[2025-09-05 07:55:28] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -50.060984+0.000280j
[2025-09-05 07:55:35] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -50.196131-0.000837j
[2025-09-05 07:55:42] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -50.102010-0.002572j
[2025-09-05 07:55:49] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -50.237174+0.001808j
[2025-09-05 07:55:57] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -50.176062+0.001762j
[2025-09-05 07:56:04] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -50.031985+0.003171j
[2025-09-05 07:56:11] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -50.168775+0.000654j
[2025-09-05 07:56:18] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -50.189515-0.000324j
[2025-09-05 07:56:25] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -50.190623+0.000304j
[2025-09-05 07:56:32] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -50.248931+0.000467j
[2025-09-05 07:56:39] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -50.300072-0.002083j
[2025-09-05 07:56:46] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -50.274167-0.000575j
[2025-09-05 07:56:53] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -50.329804-0.003912j
[2025-09-05 07:57:00] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -50.257976-0.001271j
[2025-09-05 07:57:07] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -50.240604-0.001008j
[2025-09-05 07:57:15] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -50.103356+0.002489j
[2025-09-05 07:57:22] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -50.160130-0.000248j
[2025-09-05 07:57:29] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -49.999538-0.003199j
[2025-09-05 07:57:36] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -50.058570+0.000770j
[2025-09-05 07:57:43] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -50.176574+0.000768j
[2025-09-05 07:57:50] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -50.215379-0.001296j
[2025-09-05 07:57:57] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -50.221105+0.002365j
[2025-09-05 07:57:57] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-05 07:58:04] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -50.394994+0.001138j
[2025-09-05 07:58:11] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -50.267456+0.000120j
[2025-09-05 07:58:18] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -50.157874+0.001692j
[2025-09-05 07:58:25] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -50.127580-0.001320j
[2025-09-05 07:58:33] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -50.162256-0.003819j
[2025-09-05 07:58:40] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -50.166579+0.001796j
[2025-09-05 07:58:47] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -50.180828-0.000942j
[2025-09-05 07:58:54] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -50.101030-0.000612j
[2025-09-05 07:59:01] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -50.195799+0.000274j
[2025-09-05 07:59:08] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -50.083579+0.001506j
[2025-09-05 07:59:15] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -50.223589+0.001810j
[2025-09-05 07:59:22] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -50.348010-0.003427j
[2025-09-05 07:59:29] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -50.173259+0.001255j
[2025-09-05 07:59:36] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -50.291361-0.000427j
[2025-09-05 07:59:44] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -50.272825+0.003416j
[2025-09-05 07:59:51] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -50.190472-0.001113j
[2025-09-05 07:59:58] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -50.303123-0.002690j
[2025-09-05 08:00:05] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -50.146822+0.001068j
[2025-09-05 08:00:12] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -50.244779+0.002154j
[2025-09-05 08:00:19] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -50.280536+0.000181j
[2025-09-05 08:00:27] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -50.212821+0.001290j
[2025-09-05 08:00:34] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -50.356283+0.003298j
[2025-09-05 08:00:41] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -50.284834+0.000581j
[2025-09-05 08:00:48] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -50.223691-0.000336j
[2025-09-05 08:00:55] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -50.217599+0.001577j
[2025-09-05 08:01:02] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -50.153148+0.000231j
[2025-09-05 08:01:09] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -50.198350+0.002728j
[2025-09-05 08:01:16] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -50.171542-0.001555j
[2025-09-05 08:01:23] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -50.250163+0.001998j
[2025-09-05 08:01:30] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -50.288711+0.001461j
[2025-09-05 08:01:37] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -50.195133-0.001119j
[2025-09-05 08:01:45] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -50.140984-0.000140j
[2025-09-05 08:01:52] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -50.157135-0.001358j
[2025-09-05 08:01:59] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -50.069094-0.001646j
[2025-09-05 08:02:06] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -50.182207-0.000135j
[2025-09-05 08:02:13] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -50.162459-0.003393j
[2025-09-05 08:02:20] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -50.241173+0.000556j
[2025-09-05 08:02:27] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -50.159890+0.000142j
[2025-09-05 08:02:34] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -50.144114-0.000586j
[2025-09-05 08:02:41] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -50.280255-0.000104j
[2025-09-05 08:02:48] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -50.284209-0.004208j
[2025-09-05 08:02:55] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -50.140706+0.002711j
[2025-09-05 08:03:03] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -50.033623+0.003185j
[2025-09-05 08:03:10] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -50.022557-0.001792j
[2025-09-05 08:03:17] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -50.120215+0.001093j
[2025-09-05 08:03:24] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -50.012760-0.000099j
[2025-09-05 08:03:31] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -50.098374-0.000610j
[2025-09-05 08:03:38] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -50.100632+0.002447j
[2025-09-05 08:03:45] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -50.176772+0.000003j
[2025-09-05 08:03:52] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -50.265012-0.000792j
[2025-09-05 08:03:59] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -50.319389-0.003117j
[2025-09-05 08:04:06] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -50.215757-0.000869j
[2025-09-05 08:04:13] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -50.251474-0.000313j
[2025-09-05 08:04:21] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -50.249257+0.000149j
[2025-09-05 08:04:28] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -50.304588+0.000648j
[2025-09-05 08:04:35] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -50.252039+0.000291j
[2025-09-05 08:04:42] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -50.267203+0.002549j
[2025-09-05 08:04:49] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -50.309466-0.004837j
[2025-09-05 08:04:56] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -50.287338-0.001614j
[2025-09-05 08:05:03] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -50.285559+0.000158j
[2025-09-05 08:05:10] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -50.222647+0.000038j
[2025-09-05 08:05:17] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -50.240791-0.000805j
[2025-09-05 08:05:24] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -50.218237-0.002371j
[2025-09-05 08:05:31] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -50.236736+0.000160j
[2025-09-05 08:05:39] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -50.286564-0.001674j
[2025-09-05 08:05:46] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -50.228530-0.005232j
[2025-09-05 08:05:53] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -50.155106+0.001714j
[2025-09-05 08:06:00] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -49.943766+0.004210j
[2025-09-05 08:06:07] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -50.075265+0.000051j
[2025-09-05 08:06:14] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -50.094647-0.000910j
[2025-09-05 08:06:21] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -50.152224-0.000166j
[2025-09-05 08:06:28] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -50.238451-0.001999j
[2025-09-05 08:06:35] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -50.118337+0.001395j
[2025-09-05 08:06:42] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -50.260673-0.002652j
[2025-09-05 08:06:49] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -50.155657-0.002767j
[2025-09-05 08:06:57] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -50.157038-0.000168j
[2025-09-05 08:07:04] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -50.120493-0.001333j
[2025-09-05 08:07:11] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -50.217748-0.002200j
[2025-09-05 08:07:18] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -50.126172-0.000579j
[2025-09-05 08:07:25] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -50.085543-0.001608j
[2025-09-05 08:07:32] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -49.973861+0.001301j
[2025-09-05 08:07:39] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -50.143592-0.001477j
[2025-09-05 08:07:46] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -50.139596+0.000114j
[2025-09-05 08:07:53] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -50.104448-0.001352j
[2025-09-05 08:08:00] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -50.091588+0.000421j
[2025-09-05 08:08:07] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -50.118418+0.000232j
[2025-09-05 08:08:15] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -50.074999+0.001905j
[2025-09-05 08:08:22] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -50.028834-0.001358j
[2025-09-05 08:08:29] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -50.211692+0.001206j
[2025-09-05 08:08:36] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -50.099595+0.002555j
[2025-09-05 08:08:43] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -50.266404+0.001892j
[2025-09-05 08:08:50] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -50.204165-0.001119j
[2025-09-05 08:08:57] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -50.264653-0.000216j
[2025-09-05 08:09:04] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -50.282108+0.001616j
[2025-09-05 08:09:11] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -50.273803-0.001296j
[2025-09-05 08:09:18] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -50.220513+0.002578j
[2025-09-05 08:09:25] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -50.147149+0.000775j
[2025-09-05 08:09:33] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -50.053656-0.002470j
[2025-09-05 08:09:40] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -50.198759-0.000050j
[2025-09-05 08:09:47] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -50.265343+0.001531j
[2025-09-05 08:09:54] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -50.236776-0.001480j
[2025-09-05 08:10:01] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -50.267016-0.002736j
[2025-09-05 08:10:08] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -50.241404-0.000575j
[2025-09-05 08:10:15] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -50.306179-0.003276j
[2025-09-05 08:10:22] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -50.180551+0.002502j
[2025-09-05 08:10:22] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-05 08:10:29] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -50.196943-0.000021j
[2025-09-05 08:10:36] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -50.125487+0.003196j
[2025-09-05 08:10:44] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -50.163793-0.002267j
[2025-09-05 08:10:51] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -50.087066-0.002515j
[2025-09-05 08:10:58] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -50.154853+0.000923j
[2025-09-05 08:11:05] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -50.203811+0.001183j
[2025-09-05 08:11:12] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -50.178881-0.002928j
[2025-09-05 08:11:19] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -50.333389-0.000023j
[2025-09-05 08:11:26] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -50.367600-0.000861j
[2025-09-05 08:11:33] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -50.252014+0.003096j
[2025-09-05 08:11:40] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -50.194037+0.004514j
[2025-09-05 08:11:47] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -50.206437-0.001462j
[2025-09-05 08:11:54] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -50.094511-0.000323j
[2025-09-05 08:12:02] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -50.161365+0.000428j
[2025-09-05 08:12:09] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -50.028448+0.000924j
[2025-09-05 08:12:16] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -50.126719+0.001231j
[2025-09-05 08:12:23] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -50.151671-0.000128j
[2025-09-05 08:12:30] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -50.154533+0.001440j
[2025-09-05 08:12:37] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -50.087599-0.002117j
[2025-09-05 08:12:44] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -50.171210+0.000632j
[2025-09-05 08:12:51] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -50.187686-0.001238j
[2025-09-05 08:12:58] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -50.188944+0.000767j
[2025-09-05 08:13:05] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -50.210607-0.001375j
[2025-09-05 08:13:12] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -50.168846-0.000681j
[2025-09-05 08:13:20] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -50.217620-0.003666j
[2025-09-05 08:13:27] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -50.224705+0.001080j
[2025-09-05 08:13:34] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -50.082416-0.000941j
[2025-09-05 08:13:41] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -50.238477-0.002871j
[2025-09-05 08:13:48] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -50.178014-0.000385j
[2025-09-05 08:13:55] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -50.114759+0.001017j
[2025-09-05 08:14:02] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -50.230022-0.001362j
[2025-09-05 08:14:09] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -50.280148+0.000720j
[2025-09-05 08:14:16] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -50.387455+0.000940j
[2025-09-05 08:14:24] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -50.341370+0.002561j
[2025-09-05 08:14:31] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -50.236028+0.002858j
[2025-09-05 08:14:38] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -50.321521+0.000812j
[2025-09-05 08:14:45] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -50.323462-0.000331j
[2025-09-05 08:14:52] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -50.329503+0.001479j
[2025-09-05 08:14:59] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -50.434047-0.003454j
[2025-09-05 08:15:06] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -50.291909+0.002612j
[2025-09-05 08:15:13] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -50.195535+0.000518j
[2025-09-05 08:15:20] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -50.244031+0.000010j
[2025-09-05 08:15:27] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -50.258249+0.002343j
[2025-09-05 08:15:34] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -50.145724-0.002603j
[2025-09-05 08:15:42] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -50.255427-0.000163j
[2025-09-05 08:15:49] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -50.245285+0.001259j
[2025-09-05 08:15:56] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -50.241001+0.001302j
[2025-09-05 08:16:03] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -50.300075-0.001326j
[2025-09-05 08:16:10] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -50.163431+0.002182j
[2025-09-05 08:16:17] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -50.185834-0.003872j
[2025-09-05 08:16:24] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -50.137697+0.001670j
[2025-09-05 08:16:31] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -50.198691-0.000144j
[2025-09-05 08:16:38] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -50.132298+0.002745j
[2025-09-05 08:16:45] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -50.201711-0.003228j
[2025-09-05 08:16:53] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -50.213018-0.001075j
[2025-09-05 08:17:00] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -50.215405-0.003710j
[2025-09-05 08:17:07] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -50.100532+0.000571j
[2025-09-05 08:17:14] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -50.186987-0.003043j
[2025-09-05 08:17:21] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -50.280707-0.001363j
[2025-09-05 08:17:28] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -50.149363-0.001958j
[2025-09-05 08:17:35] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -50.191894+0.000630j
[2025-09-05 08:17:42] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -50.196862+0.001416j
[2025-09-05 08:17:49] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -50.171433-0.002984j
[2025-09-05 08:17:56] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -50.144433+0.001854j
[2025-09-05 08:18:03] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -50.291863+0.001487j
[2025-09-05 08:18:11] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -50.275538-0.000075j
[2025-09-05 08:18:18] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -50.187248-0.004272j
[2025-09-05 08:18:25] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -50.118055+0.000574j
[2025-09-05 08:18:32] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -50.109713+0.000541j
[2025-09-05 08:18:39] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -50.149128+0.000678j
[2025-09-05 08:18:46] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -50.059730-0.002170j
[2025-09-05 08:18:53] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -50.148608+0.002984j
[2025-09-05 08:19:00] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -50.146136-0.001127j
[2025-09-05 08:19:07] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -50.091345+0.003387j
[2025-09-05 08:19:14] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -50.119347+0.002084j
[2025-09-05 08:19:21] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -50.122435-0.002734j
[2025-09-05 08:19:29] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -50.224322+0.000140j
[2025-09-05 08:19:36] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -50.113811+0.000602j
[2025-09-05 08:19:43] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -50.149706+0.001973j
[2025-09-05 08:19:50] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -50.236352-0.000914j
[2025-09-05 08:19:57] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -50.273272+0.000466j
[2025-09-05 08:20:04] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -50.159284+0.003130j
[2025-09-05 08:20:11] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -50.107285+0.001262j
[2025-09-05 08:20:18] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -50.218417-0.002549j
[2025-09-05 08:20:25] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -50.199872-0.002154j
[2025-09-05 08:20:32] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -50.126724+0.000257j
[2025-09-05 08:20:39] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -50.236370-0.000595j
[2025-09-05 08:20:47] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -50.225586-0.000810j
[2025-09-05 08:20:54] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -50.129322+0.000900j
[2025-09-05 08:21:01] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -50.136246-0.002084j
[2025-09-05 08:21:08] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -50.222325+0.001925j
[2025-09-05 08:21:15] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -50.142120+0.000455j
[2025-09-05 08:21:22] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -50.072987-0.000649j
[2025-09-05 08:21:29] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -50.279689-0.000396j
[2025-09-05 08:21:36] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -50.300666+0.001778j
[2025-09-05 08:21:43] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -50.245496-0.005795j
[2025-09-05 08:21:50] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -50.172922+0.000516j
[2025-09-05 08:21:58] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -50.173316-0.002711j
[2025-09-05 08:22:05] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -50.121383-0.003232j
[2025-09-05 08:22:12] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -50.169765-0.000226j
[2025-09-05 08:22:19] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -50.227385-0.002982j
[2025-09-05 08:22:26] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -50.228079+0.003724j
[2025-09-05 08:22:33] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -50.123628-0.000476j
[2025-09-05 08:22:40] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -50.179030-0.000401j
[2025-09-05 08:22:47] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -50.124354+0.000391j
[2025-09-05 08:22:47] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-05 08:22:54] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -50.091920-0.001788j
[2025-09-05 08:23:01] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -50.018745+0.002165j
[2025-09-05 08:23:08] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -50.166453+0.002605j
[2025-09-05 08:23:16] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -50.050133-0.002093j
[2025-09-05 08:23:23] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -50.187106+0.000725j
[2025-09-05 08:23:30] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -50.221889-0.000357j
[2025-09-05 08:23:37] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -50.202902-0.001405j
[2025-09-05 08:23:44] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -50.141914+0.002737j
[2025-09-05 08:23:51] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -50.249467-0.002121j
[2025-09-05 08:23:58] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -50.225527+0.000720j
[2025-09-05 08:24:05] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -50.194953-0.000638j
[2025-09-05 08:24:12] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -50.182927+0.001070j
[2025-09-05 08:24:19] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -50.110217-0.000643j
[2025-09-05 08:24:26] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -49.995062-0.001837j
[2025-09-05 08:24:34] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -50.019893-0.002174j
[2025-09-05 08:24:41] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -50.163920+0.000435j
[2025-09-05 08:24:48] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -50.138114-0.002513j
[2025-09-05 08:24:55] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -50.203932+0.000912j
[2025-09-05 08:25:02] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -50.205295-0.001582j
[2025-09-05 08:25:09] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -50.198747-0.000033j
[2025-09-05 08:25:16] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -50.152839+0.000989j
[2025-09-05 08:25:23] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -50.247801+0.000358j
[2025-09-05 08:25:30] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -50.156176+0.002146j
[2025-09-05 08:25:37] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -50.150507-0.001873j
[2025-09-05 08:25:44] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -50.155592+0.001862j
[2025-09-05 08:25:52] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -50.142032+0.002126j
[2025-09-05 08:25:59] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -49.999922-0.001225j
[2025-09-05 08:26:06] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -50.140109-0.000822j
[2025-09-05 08:26:13] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -50.161014-0.000035j
[2025-09-05 08:26:20] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -50.153974+0.003174j
[2025-09-05 08:26:27] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -50.154469+0.001801j
[2025-09-05 08:26:34] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -50.084121-0.002077j
[2025-09-05 08:26:41] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -50.190361-0.001759j
[2025-09-05 08:26:48] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -50.237161+0.000850j
[2025-09-05 08:26:55] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -50.171341+0.001501j
[2025-09-05 08:27:02] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -50.161741-0.001545j
[2025-09-05 08:27:10] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -50.043911+0.001676j
[2025-09-05 08:27:17] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -50.159174+0.001521j
[2025-09-05 08:27:24] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -50.074889+0.000010j
[2025-09-05 08:27:31] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -50.154022+0.002188j
[2025-09-05 08:27:38] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -50.201197+0.001065j
[2025-09-05 08:27:45] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -50.196321+0.000493j
[2025-09-05 08:27:52] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -50.180409+0.000967j
[2025-09-05 08:27:59] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -50.223722+0.002927j
[2025-09-05 08:28:07] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -50.349699-0.003214j
[2025-09-05 08:28:14] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -50.308567-0.002036j
[2025-09-05 08:28:21] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -50.069623-0.000218j
[2025-09-05 08:28:28] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -50.103034+0.002068j
[2025-09-05 08:28:35] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -50.153908-0.000309j
[2025-09-05 08:28:42] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -50.323375+0.004019j
[2025-09-05 08:28:49] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -50.210795-0.000224j
[2025-09-05 08:28:56] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -50.301840-0.001961j
[2025-09-05 08:29:03] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -50.237659+0.001609j
[2025-09-05 08:29:10] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -50.196600-0.001746j
[2025-09-05 08:29:17] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -50.090066-0.002221j
[2025-09-05 08:29:25] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -50.175650+0.000281j
[2025-09-05 08:29:32] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -50.164393+0.000513j
[2025-09-05 08:29:39] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -50.297103-0.001564j
[2025-09-05 08:29:46] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -50.312242-0.001238j
[2025-09-05 08:29:53] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -50.288145+0.000818j
[2025-09-05 08:30:00] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -50.238452+0.001051j
[2025-09-05 08:30:07] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -50.215920+0.005422j
[2025-09-05 08:30:14] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -50.221249-0.002165j
[2025-09-05 08:30:21] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -50.209193+0.003860j
[2025-09-05 08:30:28] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -50.157860-0.001578j
[2025-09-05 08:30:35] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -50.127982+0.001310j
[2025-09-05 08:30:43] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -50.197357+0.000114j
[2025-09-05 08:30:50] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -50.308096+0.003150j
[2025-09-05 08:30:57] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -50.263462-0.000906j
[2025-09-05 08:31:04] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -50.326104-0.000764j
[2025-09-05 08:31:11] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -50.263609-0.001330j
[2025-09-05 08:31:18] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -50.240341+0.001459j
[2025-09-05 08:31:25] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -50.143436-0.001113j
[2025-09-05 08:31:32] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -50.237470+0.002134j
[2025-09-05 08:31:39] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -50.158081-0.001399j
[2025-09-05 08:31:46] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -50.164318-0.003674j
[2025-09-05 08:31:53] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -50.211513-0.001027j
[2025-09-05 08:32:01] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -50.237650-0.000185j
[2025-09-05 08:32:08] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -50.180363+0.003658j
[2025-09-05 08:32:15] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -50.220004+0.001405j
[2025-09-05 08:32:22] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -50.296249+0.000027j
[2025-09-05 08:32:29] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -50.149748-0.003125j
[2025-09-05 08:32:36] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -49.999526+0.001407j
[2025-09-05 08:32:43] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -50.057312+0.001245j
[2025-09-05 08:32:50] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -50.226138-0.000072j
[2025-09-05 08:32:57] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -50.309248-0.000750j
[2025-09-05 08:33:04] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -50.329432-0.001166j
[2025-09-05 08:33:11] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -50.316087-0.000815j
[2025-09-05 08:33:19] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -50.180842+0.000488j
[2025-09-05 08:33:26] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -50.095112+0.002570j
[2025-09-05 08:33:33] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -50.024880+0.002676j
[2025-09-05 08:33:40] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -50.047867-0.002475j
[2025-09-05 08:33:47] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -50.019525+0.002093j
[2025-09-05 08:33:54] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -50.178661+0.001168j
[2025-09-05 08:34:01] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -50.161345+0.002111j
[2025-09-05 08:34:08] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -50.141145+0.000958j
[2025-09-05 08:34:15] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -50.185116-0.002819j
[2025-09-05 08:34:22] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -50.237984-0.002391j
[2025-09-05 08:34:29] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -50.142315-0.003376j
[2025-09-05 08:34:37] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -50.204354-0.000530j
[2025-09-05 08:34:44] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -50.242788+0.000559j
[2025-09-05 08:34:51] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -50.116268-0.000269j
[2025-09-05 08:34:58] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -50.144857-0.000289j
[2025-09-05 08:35:05] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -50.286240+0.001059j
[2025-09-05 08:35:12] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -50.020567+0.000118j
[2025-09-05 08:35:12] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-05 08:35:19] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -50.058065-0.000144j
[2025-09-05 08:35:26] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -50.094687-0.001575j
[2025-09-05 08:35:33] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -50.046528-0.000392j
[2025-09-05 08:35:40] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -50.132302-0.003268j
[2025-09-05 08:35:47] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -50.170465-0.001074j
[2025-09-05 08:35:55] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -50.105964+0.003717j
[2025-09-05 08:36:02] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -50.035759+0.002341j
[2025-09-05 08:36:09] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -50.049027-0.000357j
[2025-09-05 08:36:16] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -50.051808+0.004996j
[2025-09-05 08:36:23] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -50.097074+0.000256j
[2025-09-05 08:36:30] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -50.104546-0.002049j
[2025-09-05 08:36:37] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -50.184856+0.000091j
[2025-09-05 08:36:44] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -50.067325+0.003307j
[2025-09-05 08:36:51] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -50.111980+0.000569j
[2025-09-05 08:36:58] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -50.121203-0.000597j
[2025-09-05 08:37:06] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -50.087280-0.002054j
[2025-09-05 08:37:13] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -50.168391+0.001822j
[2025-09-05 08:37:20] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -50.198869+0.002565j
[2025-09-05 08:37:27] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -50.201130+0.002932j
[2025-09-05 08:37:34] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -50.281878+0.004536j
[2025-09-05 08:37:41] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -50.264801+0.000140j
[2025-09-05 08:37:48] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -50.247869-0.000903j
[2025-09-05 08:37:55] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -50.196684+0.002161j
[2025-09-05 08:38:02] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -50.278162-0.001192j
[2025-09-05 08:38:09] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -50.274108-0.000403j
[2025-09-05 08:38:16] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -50.286872+0.000589j
[2025-09-05 08:38:24] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -50.309622+0.000898j
[2025-09-05 08:38:31] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -50.145154+0.004132j
[2025-09-05 08:38:38] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -50.243939-0.001867j
[2025-09-05 08:38:45] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -50.250913-0.002716j
[2025-09-05 08:38:52] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -50.143911-0.001025j
[2025-09-05 08:38:59] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -50.057609+0.001733j
[2025-09-05 08:39:06] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -50.027008-0.001464j
[2025-09-05 08:39:13] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -50.127332-0.002139j
[2025-09-05 08:39:20] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -50.149922+0.000596j
[2025-09-05 08:39:27] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -50.114062-0.001000j
[2025-09-05 08:39:34] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -50.272365+0.000256j
[2025-09-05 08:39:42] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -50.142379-0.001411j
[2025-09-05 08:39:49] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -50.238194-0.001691j
[2025-09-05 08:39:56] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -49.996960-0.000500j
[2025-09-05 08:40:03] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -50.099258+0.001790j
[2025-09-05 08:40:10] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -50.101726-0.000599j
[2025-09-05 08:40:17] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -50.138922+0.000031j
[2025-09-05 08:40:24] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -50.149058-0.000941j
[2025-09-05 08:40:31] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -50.026475-0.003733j
[2025-09-05 08:40:38] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -50.137651-0.000320j
[2025-09-05 08:40:46] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -50.125870+0.004213j
[2025-09-05 08:40:53] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -50.183790+0.002556j
[2025-09-05 08:41:00] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -50.111838+0.001023j
[2025-09-05 08:41:07] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -50.100123-0.003578j
[2025-09-05 08:41:14] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -50.098160-0.000619j
[2025-09-05 08:41:21] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -50.145111-0.000964j
[2025-09-05 08:41:28] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -50.244241-0.003346j
[2025-09-05 08:41:35] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -50.185468+0.002481j
[2025-09-05 08:41:42] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -50.166234-0.001655j
[2025-09-05 08:41:50] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -50.200117+0.001042j
[2025-09-05 08:41:57] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -50.156963+0.000147j
[2025-09-05 08:42:04] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -50.401405-0.002083j
[2025-09-05 08:42:11] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -50.279935-0.000738j
[2025-09-05 08:42:18] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -50.251484-0.002361j
[2025-09-05 08:42:25] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -50.208743-0.001211j
[2025-09-05 08:42:32] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -50.248044-0.002715j
[2025-09-05 08:42:39] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -50.302258-0.002501j
[2025-09-05 08:42:46] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -50.341161-0.000796j
[2025-09-05 08:42:53] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -50.278520-0.000557j
[2025-09-05 08:43:00] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -50.193677-0.000037j
[2025-09-05 08:43:07] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -50.164711-0.000386j
[2025-09-05 08:43:15] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -50.192979+0.000199j
[2025-09-05 08:43:22] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -50.272391-0.003932j
[2025-09-05 08:43:29] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -50.389297+0.002240j
[2025-09-05 08:43:36] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -50.308162-0.000192j
[2025-09-05 08:43:43] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -50.143856+0.003045j
[2025-09-05 08:43:50] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -50.068888-0.000671j
[2025-09-05 08:43:57] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -50.132959-0.002666j
[2025-09-05 08:44:04] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -50.230239-0.002206j
[2025-09-05 08:44:11] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -50.294418+0.000844j
[2025-09-05 08:44:18] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -50.257307+0.001946j
[2025-09-05 08:44:26] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -50.191739-0.003043j
[2025-09-05 08:44:33] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -50.260631-0.002232j
[2025-09-05 08:44:40] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -50.322456-0.000568j
[2025-09-05 08:44:47] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -50.285524-0.000373j
[2025-09-05 08:44:54] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -50.307776-0.002452j
[2025-09-05 08:45:01] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -50.143675+0.000351j
[2025-09-05 08:45:08] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -50.180553+0.003750j
[2025-09-05 08:45:15] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -50.162116+0.002300j
[2025-09-05 08:45:22] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -50.219535-0.000249j
[2025-09-05 08:45:29] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -50.021454-0.001584j
[2025-09-05 08:45:36] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -50.066817-0.000127j
[2025-09-05 08:45:44] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -50.104490-0.001726j
[2025-09-05 08:45:51] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -50.192682-0.001360j
[2025-09-05 08:45:58] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -50.066145-0.001656j
[2025-09-05 08:46:05] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -50.175946-0.000299j
[2025-09-05 08:46:12] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -50.200821+0.001252j
[2025-09-05 08:46:19] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -50.157629+0.001185j
[2025-09-05 08:46:26] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -50.221921+0.001449j
[2025-09-05 08:46:33] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -50.202516+0.000716j
[2025-09-05 08:46:40] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -50.271637-0.002399j
[2025-09-05 08:46:47] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -50.143108-0.000683j
[2025-09-05 08:46:54] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -50.190762+0.000098j
[2025-09-05 08:47:02] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -50.098159-0.000756j
[2025-09-05 08:47:09] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -50.112938-0.003893j
[2025-09-05 08:47:16] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -50.079535+0.001766j
[2025-09-05 08:47:23] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -50.234524+0.003188j
[2025-09-05 08:47:30] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -50.107806-0.000404j
[2025-09-05 08:47:37] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -50.151702-0.001937j
[2025-09-05 08:47:37] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-05 08:47:44] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -50.254359-0.000891j
[2025-09-05 08:47:51] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -50.138114-0.000501j
[2025-09-05 08:47:58] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -50.246878+0.000773j
[2025-09-05 08:48:05] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -50.160883-0.002712j
[2025-09-05 08:48:13] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -50.132378-0.002932j
[2025-09-05 08:48:20] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -50.165799-0.002280j
[2025-09-05 08:48:27] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -50.117253+0.000155j
[2025-09-05 08:48:34] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -50.190650+0.001569j
[2025-09-05 08:48:41] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -50.155353+0.001110j
[2025-09-05 08:48:48] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -50.165334-0.000281j
[2025-09-05 08:48:55] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -50.167816+0.001842j
[2025-09-05 08:49:02] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -50.167512-0.001689j
[2025-09-05 08:49:09] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -50.253884+0.000271j
[2025-09-05 08:49:16] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -50.177813-0.000310j
[2025-09-05 08:49:23] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -50.222516-0.001020j
[2025-09-05 08:49:31] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -50.124921-0.001581j
[2025-09-05 08:49:38] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -50.185507+0.000430j
[2025-09-05 08:49:45] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -50.307849+0.000380j
[2025-09-05 08:49:52] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -50.273267-0.001763j
[2025-09-05 08:49:59] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -50.304446-0.000632j
[2025-09-05 08:50:06] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -50.217103+0.000833j
[2025-09-05 08:50:13] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -50.234307-0.002834j
[2025-09-05 08:50:20] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -50.280207-0.000281j
[2025-09-05 08:50:27] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -50.226456-0.002319j
[2025-09-05 08:50:34] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -50.251051-0.000212j
[2025-09-05 08:50:41] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -50.113738-0.001245j
[2025-09-05 08:50:49] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -50.266166-0.003076j
[2025-09-05 08:50:56] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -50.252617-0.000981j
[2025-09-05 08:51:03] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -50.148984-0.002046j
[2025-09-05 08:51:10] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -50.219127+0.002506j
[2025-09-05 08:51:17] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -50.306145+0.002189j
[2025-09-05 08:51:24] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -50.379032+0.001633j
[2025-09-05 08:51:31] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -50.479814-0.004958j
[2025-09-05 08:51:38] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -50.312063-0.005262j
[2025-09-05 08:51:45] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -50.415715+0.001417j
[2025-09-05 08:51:52] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -50.427127-0.003447j
[2025-09-05 08:52:00] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -50.291713-0.000474j
[2025-09-05 08:52:07] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -50.316528-0.001923j
[2025-09-05 08:52:14] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -50.275518+0.001674j
[2025-09-05 08:52:21] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -50.218942+0.000246j
[2025-09-05 08:52:28] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -50.221098-0.000968j
[2025-09-05 08:52:35] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -50.267891+0.002155j
[2025-09-05 08:52:42] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -50.277661+0.002416j
[2025-09-05 08:52:49] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -50.042484+0.001823j
[2025-09-05 08:52:56] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -50.120460+0.002020j
[2025-09-05 08:53:03] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -50.128454-0.000982j
[2025-09-05 08:53:10] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -50.061875-0.000645j
[2025-09-05 08:53:18] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -50.071277-0.001713j
[2025-09-05 08:53:25] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -50.119859-0.001965j
[2025-09-05 08:53:32] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -50.246357+0.000798j
[2025-09-05 08:53:39] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -50.250240-0.001512j
[2025-09-05 08:53:46] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -50.105025+0.000837j
[2025-09-05 08:53:53] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -50.240064-0.001341j
[2025-09-05 08:54:00] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -50.132001+0.001555j
[2025-09-05 08:54:07] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -50.203847-0.001937j
[2025-09-05 08:54:14] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -50.265409-0.000053j
[2025-09-05 08:54:21] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -50.189145+0.000637j
[2025-09-05 08:54:28] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -50.258710+0.001677j
[2025-09-05 08:54:36] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -50.234524-0.003684j
[2025-09-05 08:54:43] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -50.270273-0.001852j
[2025-09-05 08:54:50] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -50.190407+0.000355j
[2025-09-05 08:54:57] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -50.185617+0.000817j
[2025-09-05 08:55:04] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -50.133217-0.000084j
[2025-09-05 08:55:11] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -50.079906+0.001204j
[2025-09-05 08:55:18] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -50.117695-0.000256j
[2025-09-05 08:55:25] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -50.176266+0.001773j
[2025-09-05 08:55:32] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -50.102416+0.000225j
[2025-09-05 08:55:39] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -50.249852+0.003018j
[2025-09-05 08:55:47] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -50.159075+0.002349j
[2025-09-05 08:55:54] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -50.113219-0.000167j
[2025-09-05 08:56:01] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -50.230459-0.001532j
[2025-09-05 08:56:08] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -50.150058+0.001511j
[2025-09-05 08:56:15] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -50.243434+0.000814j
[2025-09-05 08:56:22] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -50.094854-0.000441j
[2025-09-05 08:56:29] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -50.089553-0.001283j
[2025-09-05 08:56:36] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -50.025120-0.000561j
[2025-09-05 08:56:43] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -50.095143+0.002121j
[2025-09-05 08:56:50] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -50.060256-0.003259j
[2025-09-05 08:56:57] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -50.055121-0.000430j
[2025-09-05 08:57:05] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -50.236528+0.000944j
[2025-09-05 08:57:12] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -50.057840+0.001920j
[2025-09-05 08:57:19] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -49.994407-0.001124j
[2025-09-05 08:57:26] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -50.159830+0.001300j
[2025-09-05 08:57:33] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -50.282664+0.000469j
[2025-09-05 08:57:40] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -50.207970+0.000612j
[2025-09-05 08:57:47] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -50.300969+0.001560j
[2025-09-05 08:57:54] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -50.195556+0.001327j
[2025-09-05 08:58:01] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -50.164684+0.002018j
[2025-09-05 08:58:08] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -50.085693+0.001479j
[2025-09-05 08:58:15] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -50.063970-0.001838j
[2025-09-05 08:58:23] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -50.044052-0.000727j
[2025-09-05 08:58:30] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -50.107706-0.001025j
[2025-09-05 08:58:37] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -50.346037-0.001307j
[2025-09-05 08:58:44] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -50.291616-0.000045j
[2025-09-05 08:58:51] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -50.278224-0.003875j
[2025-09-05 08:58:58] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -50.234112+0.000960j
[2025-09-05 08:59:05] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -50.247968+0.003474j
[2025-09-05 08:59:12] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -50.306543-0.001713j
[2025-09-05 08:59:19] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -50.337064-0.000295j
[2025-09-05 08:59:26] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -50.382907-0.000118j
[2025-09-05 08:59:33] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -50.373964-0.004480j
[2025-09-05 08:59:41] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -50.366928-0.001834j
[2025-09-05 08:59:48] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -50.246794-0.001321j
[2025-09-05 08:59:55] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -50.352358+0.000338j
[2025-09-05 09:00:02] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -50.284118-0.001542j
[2025-09-05 09:00:02] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-05 09:00:02] ✅ Training completed | Restarts: 2
[2025-09-05 09:00:02] ============================================================
[2025-09-05 09:00:02] Training completed | Runtime: 7496.0s
[2025-09-05 09:00:05] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-05 09:00:05] ============================================================
