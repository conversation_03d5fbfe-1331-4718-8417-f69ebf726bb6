[2025-09-05 14:34:57] 使用checkpoint文件: results/L=4/J2=0.05/J1=0.00/training/checkpoints/checkpoint_iter_000210.pkl
[2025-09-05 14:35:14] ✓ 从checkpoint加载参数: 210
[2025-09-05 14:35:14]   - 能量: -50.195840-0.000783j ± 0.083098
[2025-09-05 14:35:14] ================================================================================
[2025-09-05 14:35:14] 加载量子态: L=4, J2=0.05, J1=0.00, checkpoint=checkpoint_iter_000210
[2025-09-05 14:35:14] 使用采样数目: 1048576
[2025-09-05 14:35:14] 设置样本数为: 1048576
[2025-09-05 14:35:14] 开始生成共享样本集...
[2025-09-05 14:39:40] 样本生成完成,耗时: 265.865 秒
[2025-09-05 14:39:40] ================================================================================
[2025-09-05 14:39:40] 开始计算自旋结构因子...
[2025-09-05 14:39:40] 初始化操作符缓存...
[2025-09-05 14:39:40] 预构建所有自旋相关操作符...
[2025-09-05 14:39:40] 开始计算自旋相关函数...
[2025-09-05 14:39:53] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 13.602s
[2025-09-05 14:40:12] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 18.550s
[2025-09-05 14:40:26] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 14.078s
[2025-09-05 14:40:40] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 14.079s
[2025-09-05 14:40:54] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 14.078s
[2025-09-05 14:41:08] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 14.077s
[2025-09-05 14:41:22] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 14.082s
[2025-09-05 14:41:36] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 14.071s
[2025-09-05 14:41:50] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 14.100s
[2025-09-05 14:42:05] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 14.088s
[2025-09-05 14:42:19] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 14.093s
[2025-09-05 14:42:33] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 14.070s
[2025-09-05 14:42:47] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 14.089s
[2025-09-05 14:43:01] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 14.075s
[2025-09-05 14:43:15] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 14.065s
[2025-09-05 14:43:29] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 14.086s
[2025-09-05 14:43:43] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 14.070s
[2025-09-05 14:43:57] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 14.073s
[2025-09-05 14:44:11] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 14.088s
[2025-09-05 14:44:25] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 14.088s
[2025-09-05 14:44:40] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 14.074s
[2025-09-05 14:44:54] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 14.072s
[2025-09-05 14:45:08] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 14.101s
[2025-09-05 14:45:22] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 14.071s
[2025-09-05 14:45:36] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 14.093s
[2025-09-05 14:45:50] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 14.076s
[2025-09-05 14:46:04] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 14.086s
[2025-09-05 14:46:18] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 14.077s
[2025-09-05 14:46:32] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 14.079s
[2025-09-05 14:46:46] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 14.090s
[2025-09-05 14:47:00] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 14.091s
[2025-09-05 14:47:14] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 14.083s
[2025-09-05 14:47:29] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 14.068s
[2025-09-05 14:47:43] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 14.081s
[2025-09-05 14:47:57] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 14.092s
[2025-09-05 14:48:11] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 14.074s
[2025-09-05 14:48:25] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 14.082s
[2025-09-05 14:48:39] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 14.063s
[2025-09-05 14:48:53] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 14.097s
[2025-09-05 14:49:07] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 14.080s
[2025-09-05 14:49:21] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 14.085s
[2025-09-05 14:49:36] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 14.031s
[2025-09-05 14:49:50] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 14.061s
[2025-09-05 14:50:04] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 14.047s
[2025-09-05 14:50:18] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 14.050s
[2025-09-05 14:50:32] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 14.067s
[2025-09-05 14:50:46] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 14.044s
[2025-09-05 14:51:00] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 14.055s
[2025-09-05 14:51:14] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 14.046s
[2025-09-05 14:51:28] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 14.035s
[2025-09-05 14:51:42] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 14.062s
[2025-09-05 14:51:56] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 14.047s
[2025-09-05 14:52:10] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 14.047s
[2025-09-05 14:52:24] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 14.037s
[2025-09-05 14:52:38] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 14.048s
[2025-09-05 14:52:52] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 14.033s
[2025-09-05 14:53:06] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 14.036s
[2025-09-05 14:53:20] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 14.057s
[2025-09-05 14:53:34] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 14.035s
[2025-09-05 14:53:48] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 14.047s
[2025-09-05 14:54:03] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 14.058s
[2025-09-05 14:54:17] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 14.069s
[2025-09-05 14:54:31] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 14.074s
[2025-09-05 14:54:45] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 14.090s
[2025-09-05 14:54:45] 自旋相关函数计算完成,总耗时 905.07 秒
[2025-09-05 14:54:47] 计算傅里叶变换...
[2025-09-05 14:54:50] 自旋结构因子计算完成
[2025-09-05 14:54:51] 自旋相关函数平均误差: 0.000685
