[2025-08-08 13:46:42] 使用checkpoint文件: results/L=4/J2=0.00/J1=0.07/training/checkpoints/checkpoint_iter_001000.pkl
[2025-08-08 13:46:50] ✓ 从checkpoint加载参数: 1000
[2025-08-08 13:46:50]   - 能量: -55.649627-0.001609j ± 0.082667
[2025-08-08 13:46:50] ================================================================================
[2025-08-08 13:46:50] 加载量子态: L=4, J2=0.00, J1=0.07, checkpoint=checkpoint_iter_001000
[2025-08-08 13:46:50] 设置样本数为: 1048576
[2025-08-08 13:46:50] 开始生成共享样本集...
[2025-08-08 13:49:22] 样本生成完成,耗时: 152.237 秒
[2025-08-08 13:49:22] ================================================================================
[2025-08-08 13:49:22] 开始计算自旋结构因子...
[2025-08-08 13:49:22] 初始化操作符缓存...
[2025-08-08 13:49:22] 预构建所有自旋相关操作符...
[2025-08-08 13:49:22] 开始计算自旋相关函数...
[2025-08-08 13:49:33] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 10.980s
[2025-08-08 13:49:47] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 13.949s
[2025-08-08 13:49:55] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 8.220s
[2025-08-08 13:50:04] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 8.209s
[2025-08-08 13:50:12] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 8.227s
[2025-08-08 13:50:20] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 8.213s
[2025-08-08 13:50:28] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 8.202s
[2025-08-08 13:50:37] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 8.225s
[2025-08-08 13:50:45] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 8.201s
[2025-08-08 13:50:53] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 8.208s
[2025-08-08 13:51:01] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 8.221s
[2025-08-08 13:51:09] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 8.210s
[2025-08-08 13:51:18] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 8.229s
[2025-08-08 13:51:26] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 8.203s
[2025-08-08 13:51:34] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 8.223s
[2025-08-08 13:51:42] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 8.201s
[2025-08-08 13:51:50] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 8.226s
[2025-08-08 13:51:59] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 8.217s
[2025-08-08 13:52:07] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 8.200s
[2025-08-08 13:52:15] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 8.225s
[2025-08-08 13:52:23] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 8.209s
[2025-08-08 13:52:32] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 8.202s
[2025-08-08 13:52:40] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 8.222s
[2025-08-08 13:52:48] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 8.199s
[2025-08-08 13:52:56] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 8.209s
[2025-08-08 13:53:04] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 8.221s
[2025-08-08 13:53:13] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 8.202s
[2025-08-08 13:53:21] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 8.223s
[2025-08-08 13:53:29] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 8.200s
[2025-08-08 13:53:37] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 8.206s
[2025-08-08 13:53:45] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 8.226s
[2025-08-08 13:53:54] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 8.284s
[2025-08-08 13:54:02] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 8.203s
[2025-08-08 13:54:10] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 8.202s
[2025-08-08 13:54:18] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 8.222s
[2025-08-08 13:54:27] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 8.203s
[2025-08-08 13:54:35] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 8.224s
[2025-08-08 13:54:43] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 8.227s
[2025-08-08 13:54:51] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 8.206s
[2025-08-08 13:54:59] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 8.232s
[2025-08-08 13:55:08] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 8.207s
[2025-08-08 13:55:16] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 8.202s
[2025-08-08 13:55:24] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 8.223s
[2025-08-08 13:55:32] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 8.201s
[2025-08-08 13:55:41] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 8.226s
[2025-08-08 13:55:49] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 8.228s
[2025-08-08 13:55:57] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 8.210s
[2025-08-08 13:56:05] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 8.234s
[2025-08-08 13:56:13] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 8.207s
[2025-08-08 13:56:22] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 8.200s
[2025-08-08 13:56:30] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 8.216s
[2025-08-08 13:56:38] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 8.200s
[2025-08-08 13:56:46] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 8.200s
[2025-08-08 13:56:54] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 8.205s
[2025-08-08 13:57:03] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 8.224s
[2025-08-08 13:57:11] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 8.207s
[2025-08-08 13:57:19] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 8.234s
[2025-08-08 13:57:27] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 8.224s
[2025-08-08 13:57:36] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 8.199s
[2025-08-08 13:57:44] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 8.224s
[2025-08-08 13:57:52] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 8.203s
[2025-08-08 13:58:00] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 8.209s
[2025-08-08 13:58:08] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 8.226s
[2025-08-08 13:58:17] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 8.207s
[2025-08-08 13:58:17] 自旋相关函数计算完成,总耗时 534.30 秒
[2025-08-08 13:58:17] 计算傅里叶变换...
[2025-08-08 13:58:17] 自旋结构因子计算完成
[2025-08-08 13:58:18] 自旋相关函数平均误差: 0.000647
