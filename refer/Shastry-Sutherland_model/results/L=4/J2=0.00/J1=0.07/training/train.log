[2025-08-07 23:12:51] ✓ 从checkpoint恢复: results/L=4/J2=0.00/J1=0.06/training/checkpoints/final_GCNN.pkl
[2025-08-07 23:12:51]   - 迭代次数: final
[2025-08-07 23:12:51]   - 能量: -55.619015+0.001275j ± 0.088192
[2025-08-07 23:12:51]   - 时间戳: 2025-08-07T18:30:04.862489+08:00
[2025-08-07 23:12:59] ✓ 变分状态参数已从checkpoint恢复
[2025-08-07 23:12:59] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-07 23:12:59] ==================================================
[2025-08-07 23:12:59] GCNN for Shastry-Sutherland Model
[2025-08-07 23:12:59] ==================================================
[2025-08-07 23:12:59] System parameters:
[2025-08-07 23:12:59]   - System size: L=4, N=64
[2025-08-07 23:12:59]   - System parameters: J1=0.07, J2=0.0, Q=1.0
[2025-08-07 23:12:59] --------------------------------------------------
[2025-08-07 23:12:59] Model parameters:
[2025-08-07 23:12:59]   - Number of layers = 4
[2025-08-07 23:12:59]   - Number of features = 4
[2025-08-07 23:12:59]   - Total parameters = 12572
[2025-08-07 23:12:59] --------------------------------------------------
[2025-08-07 23:12:59] Training parameters:
[2025-08-07 23:12:59]   - Learning rate: 0.015
[2025-08-07 23:12:59]   - Total iterations: 1050
[2025-08-07 23:12:59]   - Annealing cycles: 3
[2025-08-07 23:12:59]   - Initial period: 150
[2025-08-07 23:12:59]   - Period multiplier: 2.0
[2025-08-07 23:12:59]   - Temperature range: 0.0-1.0
[2025-08-07 23:12:59]   - Samples: 4096
[2025-08-07 23:12:59]   - Discarded samples: 0
[2025-08-07 23:12:59]   - Chunk size: 2048
[2025-08-07 23:12:59]   - Diagonal shift: 0.2
[2025-08-07 23:12:59]   - Gradient clipping: 1.0
[2025-08-07 23:12:59]   - Checkpoint enabled: interval=100
[2025-08-07 23:12:59]   - Checkpoint directory: results/L=4/J2=0.00/J1=0.07/training/checkpoints
[2025-08-07 23:12:59] --------------------------------------------------
[2025-08-07 23:12:59] Device status:
[2025-08-07 23:12:59]   - Devices model: A100
[2025-08-07 23:12:59]   - Number of devices: 1
[2025-08-07 23:12:59]   - Sharding: True
[2025-08-07 23:12:59] ============================================================
[2025-08-07 23:13:30] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -55.849907+0.013603j
[2025-08-07 23:13:49] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -55.729587-0.005034j
[2025-08-07 23:13:53] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -55.726574-0.001795j
[2025-08-07 23:13:57] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -55.714191-0.001749j
[2025-08-07 23:14:01] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -55.804497-0.005534j
[2025-08-07 23:14:05] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -55.703485+0.001130j
[2025-08-07 23:14:09] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -55.778606-0.002964j
[2025-08-07 23:14:13] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -55.719316+0.002040j
[2025-08-07 23:14:17] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -55.822552-0.006150j
[2025-08-07 23:14:22] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -55.914266+0.001249j
[2025-08-07 23:14:26] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -55.790383+0.000725j
[2025-08-07 23:14:30] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -55.742789-0.001968j
[2025-08-07 23:14:34] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -55.683928-0.002602j
[2025-08-07 23:14:38] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -55.662958+0.002117j
[2025-08-07 23:14:42] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -55.780703+0.003419j
[2025-08-07 23:14:46] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -55.770020+0.006603j
[2025-08-07 23:14:50] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -55.818020+0.002377j
[2025-08-07 23:14:55] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -55.883196-0.002839j
[2025-08-07 23:14:59] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -55.862471+0.000373j
[2025-08-07 23:15:03] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -55.889635-0.005780j
[2025-08-07 23:15:07] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -55.949945+0.002903j
[2025-08-07 23:15:11] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -55.900674+0.001269j
[2025-08-07 23:15:15] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -55.817700+0.000171j
[2025-08-07 23:15:19] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -55.794307-0.002149j
[2025-08-07 23:15:24] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -55.758208+0.000331j
[2025-08-07 23:15:28] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -55.872186-0.001267j
[2025-08-07 23:15:32] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -55.859367-0.000146j
[2025-08-07 23:15:36] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -55.827504-0.000297j
[2025-08-07 23:15:40] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -55.780233-0.003346j
[2025-08-07 23:15:44] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -55.823641+0.001469j
[2025-08-07 23:15:48] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -55.882068+0.003351j
[2025-08-07 23:15:52] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -55.868478-0.002257j
[2025-08-07 23:15:57] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -55.817808-0.006266j
[2025-08-07 23:16:01] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -55.788930-0.006710j
[2025-08-07 23:16:05] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -55.853094-0.003958j
[2025-08-07 23:16:09] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -55.762285+0.003684j
[2025-08-07 23:16:13] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -55.849205-0.001937j
[2025-08-07 23:16:17] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -55.783354+0.001219j
[2025-08-07 23:16:21] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -55.690624-0.004665j
[2025-08-07 23:16:25] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -55.843384+0.000114j
[2025-08-07 23:16:30] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -55.829609+0.000116j
[2025-08-07 23:16:34] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -55.944296+0.001097j
[2025-08-07 23:16:38] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -55.990957+0.002185j
[2025-08-07 23:16:42] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -55.903046+0.000532j
[2025-08-07 23:16:46] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -56.051590+0.000293j
[2025-08-07 23:16:50] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -56.066572-0.002107j
[2025-08-07 23:16:54] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -56.007308+0.001954j
[2025-08-07 23:16:58] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -56.095567-0.002523j
[2025-08-07 23:17:03] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -56.144368-0.004344j
[2025-08-07 23:17:07] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -55.999520+0.006538j
[2025-08-07 23:17:11] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -55.918059-0.000166j
[2025-08-07 23:17:15] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -55.722339-0.000257j
[2025-08-07 23:17:19] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -55.856652-0.004373j
[2025-08-07 23:17:23] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -55.804525+0.000993j
[2025-08-07 23:17:27] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -55.879226+0.002919j
[2025-08-07 23:17:31] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -55.861943-0.001881j
[2025-08-07 23:17:36] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -55.712775+0.000419j
[2025-08-07 23:17:40] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -55.841573+0.002223j
[2025-08-07 23:17:44] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -55.852428+0.000243j
[2025-08-07 23:17:48] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -55.867556+0.005507j
[2025-08-07 23:17:52] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -55.772606+0.005262j
[2025-08-07 23:17:56] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -55.929091+0.003269j
[2025-08-07 23:18:00] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -55.827431+0.001623j
[2025-08-07 23:18:04] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -55.896498+0.001500j
[2025-08-07 23:18:09] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -55.827721+0.000248j
[2025-08-07 23:18:13] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -55.936958-0.001623j
[2025-08-07 23:18:17] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -55.906938+0.000478j
[2025-08-07 23:18:21] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -55.891547+0.001417j
[2025-08-07 23:18:25] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -55.901188-0.001334j
[2025-08-07 23:18:29] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -55.844709+0.002100j
[2025-08-07 23:18:34] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -55.860147+0.000980j
[2025-08-07 23:18:38] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -55.792261+0.003651j
[2025-08-07 23:18:42] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -55.704911+0.004655j
[2025-08-07 23:18:46] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -55.761279-0.003063j
[2025-08-07 23:18:50] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -55.751561+0.004175j
[2025-08-07 23:18:54] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -55.864861+0.003485j
[2025-08-07 23:18:58] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -55.698215+0.001769j
[2025-08-07 23:19:02] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -55.785246+0.001235j
[2025-08-07 23:19:07] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -55.837651-0.004887j
[2025-08-07 23:19:11] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -55.796760+0.000421j
[2025-08-07 23:19:15] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -55.755467+0.000530j
[2025-08-07 23:19:19] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -55.804742+0.002727j
[2025-08-07 23:19:23] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -55.720000+0.000791j
[2025-08-07 23:19:27] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -55.817003-0.000307j
[2025-08-07 23:19:31] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -55.755481-0.008287j
[2025-08-07 23:19:35] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -55.837483-0.005304j
[2025-08-07 23:19:40] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -55.979017+0.001392j
[2025-08-07 23:19:44] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -55.956090-0.000485j
[2025-08-07 23:19:48] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -55.785995+0.000650j
[2025-08-07 23:19:52] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -55.771648+0.005021j
[2025-08-07 23:19:56] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -55.759202+0.002344j
[2025-08-07 23:20:00] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -55.792783-0.001567j
[2025-08-07 23:20:04] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -55.819245-0.000829j
[2025-08-07 23:20:08] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -55.665542+0.002689j
[2025-08-07 23:20:13] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -55.750279-0.002905j
[2025-08-07 23:20:17] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -55.873849+0.002705j
[2025-08-07 23:20:21] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -55.915137-0.001948j
[2025-08-07 23:20:25] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -55.884812+0.001462j
[2025-08-07 23:20:29] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -55.817673-0.002027j
[2025-08-07 23:20:33] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -55.828394+0.003250j
[2025-08-07 23:20:33] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-07 23:20:37] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -55.721434-0.000297j
[2025-08-07 23:20:41] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -55.782013-0.003163j
[2025-08-07 23:20:46] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -55.837876-0.007735j
[2025-08-07 23:20:50] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -55.861146+0.002255j
[2025-08-07 23:20:54] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -55.841106-0.004534j
[2025-08-07 23:20:58] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -55.854724-0.001226j
[2025-08-07 23:21:02] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -55.774983-0.002789j
[2025-08-07 23:21:06] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -55.799989-0.003025j
[2025-08-07 23:21:10] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -55.783691+0.005402j
[2025-08-07 23:21:14] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -55.927458-0.003575j
[2025-08-07 23:21:19] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -55.842197-0.001302j
[2025-08-07 23:21:23] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -55.749814+0.004329j
[2025-08-07 23:21:27] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -55.720084+0.000223j
[2025-08-07 23:21:31] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -55.750021+0.000945j
[2025-08-07 23:21:35] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -55.762458-0.002653j
[2025-08-07 23:21:39] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -55.651107+0.001863j
[2025-08-07 23:21:43] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -55.762937+0.006221j
[2025-08-07 23:21:47] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -55.659528+0.005811j
[2025-08-07 23:21:52] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -55.788576+0.005212j
[2025-08-07 23:21:56] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -55.739335+0.003168j
[2025-08-07 23:22:00] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -55.786543+0.000285j
[2025-08-07 23:22:04] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -55.772151-0.002528j
[2025-08-07 23:22:08] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -55.794909-0.001141j
[2025-08-07 23:22:12] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -55.727085+0.005224j
[2025-08-07 23:22:16] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -55.713176-0.003187j
[2025-08-07 23:22:20] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -55.804948+0.002486j
[2025-08-07 23:22:25] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -55.752111-0.000406j
[2025-08-07 23:22:29] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -55.714668+0.002145j
[2025-08-07 23:22:33] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -55.687339+0.001959j
[2025-08-07 23:22:37] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -55.714204+0.001222j
[2025-08-07 23:22:41] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -55.733895-0.000967j
[2025-08-07 23:22:45] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -55.565674+0.000865j
[2025-08-07 23:22:49] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -55.588218+0.001652j
[2025-08-07 23:22:54] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -55.631154+0.006440j
[2025-08-07 23:22:58] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -55.692925+0.001941j
[2025-08-07 23:23:02] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -55.765524+0.005342j
[2025-08-07 23:23:06] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -55.827621-0.002503j
[2025-08-07 23:23:10] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -55.830905+0.003781j
[2025-08-07 23:23:14] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -55.639500+0.001431j
[2025-08-07 23:23:18] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -55.662059-0.005653j
[2025-08-07 23:23:22] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -55.824357+0.000437j
[2025-08-07 23:23:26] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -55.822681+0.002450j
[2025-08-07 23:23:31] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -55.868601-0.004255j
[2025-08-07 23:23:35] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -55.781118-0.002895j
[2025-08-07 23:23:39] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -55.872872-0.002754j
[2025-08-07 23:23:43] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -55.875188+0.001415j
[2025-08-07 23:23:47] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -55.739432-0.003904j
[2025-08-07 23:23:51] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -55.651819-0.003622j
[2025-08-07 23:23:55] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -55.745909+0.001709j
[2025-08-07 23:24:00] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -55.715607+0.004199j
[2025-08-07 23:24:00] RESTART #1 | Period: 300
[2025-08-07 23:24:04] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -55.789012-0.007559j
[2025-08-07 23:24:08] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -55.784799-0.001811j
[2025-08-07 23:24:12] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -55.691621-0.006059j
[2025-08-07 23:24:16] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -55.733639+0.001393j
[2025-08-07 23:24:20] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -55.793592-0.001465j
[2025-08-07 23:24:24] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -55.835236+0.001341j
[2025-08-07 23:24:29] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -55.811005-0.000289j
[2025-08-07 23:24:33] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -55.780994+0.002879j
[2025-08-07 23:24:37] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -55.812026-0.000348j
[2025-08-07 23:24:41] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -55.846213-0.001621j
[2025-08-07 23:24:45] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -55.851682+0.000389j
[2025-08-07 23:24:49] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -55.870609+0.003639j
[2025-08-07 23:24:53] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -55.922370-0.007007j
[2025-08-07 23:24:57] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -55.806160-0.002211j
[2025-08-07 23:25:01] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -55.836347+0.000987j
[2025-08-07 23:25:06] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -55.838516+0.001437j
[2025-08-07 23:25:10] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -55.858973+0.003976j
[2025-08-07 23:25:14] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -55.731472+0.002691j
[2025-08-07 23:25:18] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -55.777252+0.000042j
[2025-08-07 23:25:22] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -55.763057-0.005394j
[2025-08-07 23:25:26] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -55.661673+0.001462j
[2025-08-07 23:25:30] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -55.691034-0.002823j
[2025-08-07 23:25:34] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -55.672002+0.000034j
[2025-08-07 23:25:39] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -55.659559-0.005330j
[2025-08-07 23:25:43] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -55.768489+0.000767j
[2025-08-07 23:25:47] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -55.726041-0.002027j
[2025-08-07 23:25:51] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -55.898353-0.000039j
[2025-08-07 23:25:55] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -55.922332-0.002615j
[2025-08-07 23:25:59] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -55.808740-0.008648j
[2025-08-07 23:26:03] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -55.729560-0.000645j
[2025-08-07 23:26:07] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -55.768863-0.000129j
[2025-08-07 23:26:12] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -55.687750+0.003318j
[2025-08-07 23:26:16] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -55.743566+0.000865j
[2025-08-07 23:26:20] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -55.792314+0.001323j
[2025-08-07 23:26:24] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -55.748791+0.003986j
[2025-08-07 23:26:28] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -55.735827-0.000248j
[2025-08-07 23:26:32] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -55.694174-0.001761j
[2025-08-07 23:26:36] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -55.828912+0.005044j
[2025-08-07 23:26:40] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -55.792268+0.004343j
[2025-08-07 23:26:45] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -55.808372-0.006691j
[2025-08-07 23:26:49] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -55.860551+0.001602j
[2025-08-07 23:26:53] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -55.728376+0.000504j
[2025-08-07 23:26:57] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -55.718463-0.001144j
[2025-08-07 23:27:01] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -55.694433-0.003037j
[2025-08-07 23:27:05] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -55.743529-0.006697j
[2025-08-07 23:27:09] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -55.739438-0.002789j
[2025-08-07 23:27:13] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -55.707623+0.003885j
[2025-08-07 23:27:18] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -55.599125+0.000404j
[2025-08-07 23:27:22] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -55.807289-0.007769j
[2025-08-07 23:27:26] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -55.649866-0.004368j
[2025-08-07 23:27:26] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-07 23:27:30] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -55.744834+0.010203j
[2025-08-07 23:27:34] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -55.676632-0.003417j
[2025-08-07 23:27:38] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -55.767780-0.005543j
[2025-08-07 23:27:42] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -55.710261-0.002966j
[2025-08-07 23:27:46] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -55.749032-0.003842j
[2025-08-07 23:27:51] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -55.773419-0.007048j
[2025-08-07 23:27:55] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -55.993233-0.000125j
[2025-08-07 23:27:59] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -55.863065+0.000441j
[2025-08-07 23:28:03] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -55.795028-0.001338j
[2025-08-07 23:28:07] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -55.730852-0.003968j
[2025-08-07 23:28:11] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -55.796077+0.004883j
[2025-08-07 23:28:15] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -55.801926-0.000363j
[2025-08-07 23:28:19] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -55.780809-0.001276j
[2025-08-07 23:28:24] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -55.837933-0.000144j
[2025-08-07 23:28:28] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -55.699012-0.000820j
[2025-08-07 23:28:32] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -55.809061+0.004168j
[2025-08-07 23:28:36] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -55.787609+0.000301j
[2025-08-07 23:28:40] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -55.787457-0.000131j
[2025-08-07 23:28:44] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -55.783831+0.001099j
[2025-08-07 23:28:48] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -55.706932+0.003314j
[2025-08-07 23:28:52] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -55.680796+0.003236j
[2025-08-07 23:28:57] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -55.784881-0.002165j
[2025-08-07 23:29:01] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -55.732470-0.000708j
[2025-08-07 23:29:05] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -55.776974+0.001579j
[2025-08-07 23:29:09] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -55.784704+0.001019j
[2025-08-07 23:29:13] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -55.867130-0.000931j
[2025-08-07 23:29:17] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -55.974568+0.004616j
[2025-08-07 23:29:21] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -55.888132+0.001245j
[2025-08-07 23:29:25] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -55.809766+0.001699j
[2025-08-07 23:29:30] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -55.955623+0.002919j
[2025-08-07 23:29:34] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -55.900765+0.001725j
[2025-08-07 23:29:38] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -55.865732+0.000128j
[2025-08-07 23:29:42] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -55.819265+0.001508j
[2025-08-07 23:29:46] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -55.796891-0.003949j
[2025-08-07 23:29:50] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -55.959734+0.002615j
[2025-08-07 23:29:54] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -55.859607-0.003107j
[2025-08-07 23:29:58] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -55.853168-0.006075j
[2025-08-07 23:30:03] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -55.849298-0.002067j
[2025-08-07 23:30:07] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -55.711878-0.006863j
[2025-08-07 23:30:11] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -55.901074-0.000606j
[2025-08-07 23:30:15] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -55.822000-0.000503j
[2025-08-07 23:30:19] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -55.841807-0.004880j
[2025-08-07 23:30:23] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -55.727318-0.001137j
[2025-08-07 23:30:27] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -55.678780-0.003004j
[2025-08-07 23:30:31] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -55.743478-0.001984j
[2025-08-07 23:30:36] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -55.780549-0.004019j
[2025-08-07 23:30:40] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -55.845098-0.000385j
[2025-08-07 23:30:44] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -55.733172+0.005135j
[2025-08-07 23:30:48] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -55.810321-0.000990j
[2025-08-07 23:30:52] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -55.767589-0.001422j
[2025-08-07 23:30:56] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -55.871984+0.006871j
[2025-08-07 23:31:00] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -55.836445+0.001352j
[2025-08-07 23:31:04] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -55.980452+0.001100j
[2025-08-07 23:31:09] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -55.955632-0.002464j
[2025-08-07 23:31:13] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -55.985901-0.004442j
[2025-08-07 23:31:17] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -55.962506-0.002497j
[2025-08-07 23:31:21] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -55.933181-0.003808j
[2025-08-07 23:31:25] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -55.836581+0.001319j
[2025-08-07 23:31:29] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -55.795345+0.002007j
[2025-08-07 23:31:33] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -55.675623-0.002399j
[2025-08-07 23:31:38] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -55.745526-0.001779j
[2025-08-07 23:31:42] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -55.768462+0.004257j
[2025-08-07 23:31:46] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -55.814640+0.004315j
[2025-08-07 23:31:50] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -55.797035+0.000466j
[2025-08-07 23:31:54] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -55.893944+0.000660j
[2025-08-07 23:31:58] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -55.868633+0.001788j
[2025-08-07 23:32:02] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -55.913790+0.002146j
[2025-08-07 23:32:06] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -55.897212+0.002699j
[2025-08-07 23:32:11] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -55.960087-0.002912j
[2025-08-07 23:32:15] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -55.890905+0.001424j
[2025-08-07 23:32:19] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -55.790898-0.007586j
[2025-08-07 23:32:23] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -55.867970-0.002248j
[2025-08-07 23:32:27] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -55.905622+0.001547j
[2025-08-07 23:32:31] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -55.835903+0.001512j
[2025-08-07 23:32:35] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -55.942141-0.001951j
[2025-08-07 23:32:39] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -55.945075-0.003569j
[2025-08-07 23:32:44] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -55.920341-0.004767j
[2025-08-07 23:32:48] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -55.896844+0.000373j
[2025-08-07 23:32:52] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -55.786834-0.004991j
[2025-08-07 23:32:56] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -55.807754-0.000647j
[2025-08-07 23:33:00] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -55.872233+0.000349j
[2025-08-07 23:33:04] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -55.820187+0.000525j
[2025-08-07 23:33:08] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -55.804627+0.005035j
[2025-08-07 23:33:12] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -55.795539-0.002493j
[2025-08-07 23:33:17] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -55.811489+0.000607j
[2025-08-07 23:33:21] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -55.886348-0.007399j
[2025-08-07 23:33:25] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -55.810652+0.000667j
[2025-08-07 23:33:29] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -55.864642-0.000900j
[2025-08-07 23:33:33] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -55.865548-0.000873j
[2025-08-07 23:33:37] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -55.871665-0.003846j
[2025-08-07 23:33:41] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -55.883350-0.000226j
[2025-08-07 23:33:46] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -55.803892+0.001286j
[2025-08-07 23:33:50] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -55.875034-0.000157j
[2025-08-07 23:33:54] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -55.828554+0.000499j
[2025-08-07 23:33:58] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -55.805127+0.002888j
[2025-08-07 23:34:02] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -55.747344-0.000782j
[2025-08-07 23:34:06] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -55.724209-0.001340j
[2025-08-07 23:34:10] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -55.645706-0.003584j
[2025-08-07 23:34:14] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -55.820271+0.000383j
[2025-08-07 23:34:19] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -55.783652-0.004914j
[2025-08-07 23:34:19] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-07 23:34:23] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -55.944777+0.000998j
[2025-08-07 23:34:27] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -55.832589+0.001229j
[2025-08-07 23:34:31] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -55.891500-0.001940j
[2025-08-07 23:34:35] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -55.821131+0.004483j
[2025-08-07 23:34:39] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -55.725061-0.001190j
[2025-08-07 23:34:43] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -55.723519+0.002491j
[2025-08-07 23:34:47] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -55.753199+0.003846j
[2025-08-07 23:34:52] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -55.814997+0.003612j
[2025-08-07 23:34:56] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -55.795822-0.006030j
[2025-08-07 23:35:00] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -55.719703-0.002630j
[2025-08-07 23:35:04] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -55.802787-0.002510j
[2025-08-07 23:35:08] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -55.728607+0.003709j
[2025-08-07 23:35:12] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -55.707927+0.003755j
[2025-08-07 23:35:16] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -55.671195+0.002046j
[2025-08-07 23:35:20] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -55.750967-0.003492j
[2025-08-07 23:35:25] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -55.836138-0.000534j
[2025-08-07 23:35:29] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -55.901297+0.000085j
[2025-08-07 23:35:33] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -55.870092-0.000882j
[2025-08-07 23:35:37] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -55.829784+0.003439j
[2025-08-07 23:35:41] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -55.817977+0.000003j
[2025-08-07 23:35:45] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -55.734837-0.004283j
[2025-08-07 23:35:49] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -55.762393+0.000878j
[2025-08-07 23:35:53] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -55.725458+0.001357j
[2025-08-07 23:35:58] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -55.756920-0.001205j
[2025-08-07 23:36:02] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -55.782222+0.000626j
[2025-08-07 23:36:06] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -55.779773-0.002633j
[2025-08-07 23:36:10] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -55.910461-0.002662j
[2025-08-07 23:36:14] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -55.835540+0.000597j
[2025-08-07 23:36:18] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -55.801639-0.006292j
[2025-08-07 23:36:22] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -55.781334-0.000112j
[2025-08-07 23:36:26] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -55.716418+0.003553j
[2025-08-07 23:36:31] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -55.827268-0.001108j
[2025-08-07 23:36:35] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -55.893911+0.001419j
[2025-08-07 23:36:39] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -55.855270-0.001051j
[2025-08-07 23:36:43] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -55.906916+0.004242j
[2025-08-07 23:36:47] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -55.883331-0.003126j
[2025-08-07 23:36:51] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -55.769784-0.000535j
[2025-08-07 23:36:55] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -55.898672-0.000743j
[2025-08-07 23:37:00] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -55.977754+0.000255j
[2025-08-07 23:37:04] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -56.045499+0.002317j
[2025-08-07 23:37:08] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -55.910708-0.004823j
[2025-08-07 23:37:12] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -55.837506+0.003011j
[2025-08-07 23:37:16] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -55.853342+0.004972j
[2025-08-07 23:37:20] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -55.861059-0.002438j
[2025-08-07 23:37:24] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -55.866727+0.001597j
[2025-08-07 23:37:28] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -55.757754-0.000096j
[2025-08-07 23:37:33] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -55.844701-0.003329j
[2025-08-07 23:37:37] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -55.861395+0.005011j
[2025-08-07 23:37:41] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -55.813350+0.004732j
[2025-08-07 23:37:45] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -55.790319-0.002184j
[2025-08-07 23:37:49] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -55.719753-0.000951j
[2025-08-07 23:37:53] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -55.763098-0.002841j
[2025-08-07 23:37:57] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -55.716407+0.000978j
[2025-08-07 23:38:01] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -55.754800-0.005265j
[2025-08-07 23:38:05] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -55.805601+0.000862j
[2025-08-07 23:38:10] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -55.842172-0.003869j
[2025-08-07 23:38:14] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -55.869874-0.004779j
[2025-08-07 23:38:18] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -55.698406+0.001791j
[2025-08-07 23:38:22] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -55.803806-0.003315j
[2025-08-07 23:38:26] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -55.811215+0.001213j
[2025-08-07 23:38:30] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -55.860003+0.004525j
[2025-08-07 23:38:34] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -55.846878+0.000482j
[2025-08-07 23:38:38] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -55.837707+0.003291j
[2025-08-07 23:38:43] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -55.910834+0.003689j
[2025-08-07 23:38:47] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -55.912825-0.002969j
[2025-08-07 23:38:51] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -55.822404+0.003858j
[2025-08-07 23:38:55] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -55.805620+0.003422j
[2025-08-07 23:38:59] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -55.873802+0.001920j
[2025-08-07 23:39:03] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -55.880342-0.001704j
[2025-08-07 23:39:07] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -55.830968-0.000170j
[2025-08-07 23:39:11] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -55.726490+0.001228j
[2025-08-07 23:39:16] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -55.652892-0.005455j
[2025-08-07 23:39:20] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -55.725050+0.000190j
[2025-08-07 23:39:24] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -55.658412-0.001806j
[2025-08-07 23:39:28] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -55.718107+0.001287j
[2025-08-07 23:39:32] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -55.819213-0.003313j
[2025-08-07 23:39:36] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -55.760237-0.001038j
[2025-08-07 23:39:40] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -55.832435+0.004082j
[2025-08-07 23:39:44] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -55.789086+0.001775j
[2025-08-07 23:39:49] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -55.695656+0.001103j
[2025-08-07 23:39:53] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -55.753477+0.002679j
[2025-08-07 23:39:57] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -55.775991+0.001571j
[2025-08-07 23:40:01] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -55.827927+0.001144j
[2025-08-07 23:40:05] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -55.824768-0.001648j
[2025-08-07 23:40:09] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -55.832359-0.000456j
[2025-08-07 23:40:13] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -55.798443+0.000801j
[2025-08-07 23:40:17] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -55.752972-0.001508j
[2025-08-07 23:40:22] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -55.834676-0.000525j
[2025-08-07 23:40:26] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -55.841735-0.002336j
[2025-08-07 23:40:30] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -55.699464+0.000690j
[2025-08-07 23:40:34] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -55.863861+0.001524j
[2025-08-07 23:40:38] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -55.842506-0.009647j
[2025-08-07 23:40:42] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -55.865892-0.003441j
[2025-08-07 23:40:46] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -55.881010-0.001473j
[2025-08-07 23:40:50] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -55.889204-0.001457j
[2025-08-07 23:40:55] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -55.885243+0.001326j
[2025-08-07 23:40:59] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -55.739578-0.003677j
[2025-08-07 23:41:03] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -55.792305-0.001163j
[2025-08-07 23:41:07] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -55.802584-0.000681j
[2025-08-07 23:41:11] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -55.753275-0.001079j
[2025-08-07 23:41:11] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-07 23:41:15] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -55.800599-0.002177j
[2025-08-07 23:41:19] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -55.899362+0.003346j
[2025-08-07 23:41:23] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -55.860481-0.000448j
[2025-08-07 23:41:27] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -55.728373-0.002089j
[2025-08-07 23:41:32] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -55.787673+0.003253j
[2025-08-07 23:41:36] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -55.777688-0.000463j
[2025-08-07 23:41:40] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -55.913345+0.001164j
[2025-08-07 23:41:44] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -55.887209-0.004136j
[2025-08-07 23:41:48] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -55.942339-0.002187j
[2025-08-07 23:41:52] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -55.757097+0.001100j
[2025-08-07 23:41:56] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -55.755835+0.006722j
[2025-08-07 23:42:00] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -55.808941+0.006013j
[2025-08-07 23:42:05] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -55.833929+0.001956j
[2025-08-07 23:42:09] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -55.742805+0.002494j
[2025-08-07 23:42:13] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -55.778847+0.000770j
[2025-08-07 23:42:17] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -55.831533-0.002143j
[2025-08-07 23:42:21] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -55.887015-0.000439j
[2025-08-07 23:42:25] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -55.712528+0.000845j
[2025-08-07 23:42:29] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -55.719691-0.004722j
[2025-08-07 23:42:34] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -55.728947-0.001948j
[2025-08-07 23:42:38] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -55.666535+0.003121j
[2025-08-07 23:42:42] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -55.730723+0.003642j
[2025-08-07 23:42:46] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -55.883016-0.005638j
[2025-08-07 23:42:50] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -55.977808+0.000521j
[2025-08-07 23:42:54] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -55.841604+0.001222j
[2025-08-07 23:42:58] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -55.790598-0.000170j
[2025-08-07 23:43:02] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -55.924330+0.000389j
[2025-08-07 23:43:06] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -55.879464-0.002681j
[2025-08-07 23:43:11] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -55.756010+0.004007j
[2025-08-07 23:43:15] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -55.769417-0.007240j
[2025-08-07 23:43:19] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -55.781653-0.001993j
[2025-08-07 23:43:23] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -55.823094+0.000423j
[2025-08-07 23:43:27] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -55.799042-0.000529j
[2025-08-07 23:43:31] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -55.835039+0.005898j
[2025-08-07 23:43:35] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -55.641913+0.001059j
[2025-08-07 23:43:39] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -55.728878+0.001521j
[2025-08-07 23:43:44] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -55.674768+0.002071j
[2025-08-07 23:43:48] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -55.721097+0.004049j
[2025-08-07 23:43:52] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -55.732292-0.002551j
[2025-08-07 23:43:56] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -55.857461+0.000377j
[2025-08-07 23:44:00] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -55.817319+0.003330j
[2025-08-07 23:44:04] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -55.800000+0.003304j
[2025-08-07 23:44:08] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -55.804129+0.003244j
[2025-08-07 23:44:12] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -55.786850+0.002396j
[2025-08-07 23:44:17] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -55.738263-0.003827j
[2025-08-07 23:44:21] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -55.826792-0.001700j
[2025-08-07 23:44:25] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -55.819884-0.004563j
[2025-08-07 23:44:29] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -55.780981+0.001761j
[2025-08-07 23:44:33] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -55.710912+0.001013j
[2025-08-07 23:44:37] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -55.753427+0.000396j
[2025-08-07 23:44:37] RESTART #2 | Period: 600
[2025-08-07 23:44:41] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -55.657284+0.000005j
[2025-08-07 23:44:45] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -55.593101+0.001888j
[2025-08-07 23:44:50] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -55.568899-0.001193j
[2025-08-07 23:44:54] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -55.677978+0.008007j
[2025-08-07 23:44:58] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -55.722415+0.000446j
[2025-08-07 23:45:02] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -55.909345-0.006845j
[2025-08-07 23:45:06] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -55.911505+0.001107j
[2025-08-07 23:45:10] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -55.928696+0.001891j
[2025-08-07 23:45:14] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -55.799499+0.002639j
[2025-08-07 23:45:18] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -55.913246+0.003054j
[2025-08-07 23:45:23] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -55.848083-0.000542j
[2025-08-07 23:45:27] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -55.779943+0.000565j
[2025-08-07 23:45:31] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -55.774156-0.000035j
[2025-08-07 23:45:35] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -55.811202-0.002172j
[2025-08-07 23:45:39] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -55.720215+0.005062j
[2025-08-07 23:45:43] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -55.811881-0.000593j
[2025-08-07 23:45:47] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -55.698715+0.001313j
[2025-08-07 23:45:51] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -55.680889+0.003743j
[2025-08-07 23:45:56] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -55.782493-0.000719j
[2025-08-07 23:46:00] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -55.921410+0.002282j
[2025-08-07 23:46:04] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -55.862316-0.000078j
[2025-08-07 23:46:08] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -55.814533-0.001224j
[2025-08-07 23:46:12] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -55.834281+0.001011j
[2025-08-07 23:46:16] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -55.786275+0.001872j
[2025-08-07 23:46:20] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -55.855607+0.001510j
[2025-08-07 23:46:25] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -55.838739-0.004485j
[2025-08-07 23:46:29] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -55.801243+0.003034j
[2025-08-07 23:46:33] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -55.765403+0.001233j
[2025-08-07 23:46:37] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -55.767548-0.003698j
[2025-08-07 23:46:41] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -55.813585-0.002300j
[2025-08-07 23:46:45] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -55.746541+0.003822j
[2025-08-07 23:46:49] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -55.775459+0.000872j
[2025-08-07 23:46:53] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -55.823873+0.004340j
[2025-08-07 23:46:57] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -55.856627-0.000507j
[2025-08-07 23:47:02] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -55.779284+0.001511j
[2025-08-07 23:47:06] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -55.785528+0.005604j
[2025-08-07 23:47:10] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -55.823957-0.004169j
[2025-08-07 23:47:14] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -55.804959-0.002448j
[2025-08-07 23:47:18] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -55.679549-0.003140j
[2025-08-07 23:47:22] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -55.720393+0.000126j
[2025-08-07 23:47:26] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -55.789035+0.002385j
[2025-08-07 23:47:30] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -55.735967-0.001914j
[2025-08-07 23:47:35] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -55.792878-0.000241j
[2025-08-07 23:47:39] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -55.867964+0.000588j
[2025-08-07 23:47:43] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -55.877818-0.000582j
[2025-08-07 23:47:47] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -55.899798+0.000844j
[2025-08-07 23:47:51] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -55.790384+0.003830j
[2025-08-07 23:47:55] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -55.872202-0.001074j
[2025-08-07 23:47:59] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -55.870489+0.001276j
[2025-08-07 23:48:03] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -55.899468-0.000279j
[2025-08-07 23:48:03] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-07 23:48:08] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -55.872621+0.005416j
[2025-08-07 23:48:12] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -55.835979+0.001660j
[2025-08-07 23:48:16] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -55.831925-0.000139j
[2025-08-07 23:48:20] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -55.905610+0.001773j
[2025-08-07 23:48:24] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -55.856290-0.002350j
[2025-08-07 23:48:28] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -55.785463-0.002000j
[2025-08-07 23:48:32] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -55.798151-0.001009j
[2025-08-07 23:48:36] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -55.734671-0.001558j
[2025-08-07 23:48:41] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -55.737134+0.004689j
[2025-08-07 23:48:45] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -55.770337-0.000643j
[2025-08-07 23:48:49] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -55.740138-0.001906j
[2025-08-07 23:48:53] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -55.842910-0.000555j
[2025-08-07 23:48:57] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -55.738718+0.008257j
[2025-08-07 23:49:01] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -55.620824+0.001408j
[2025-08-07 23:49:05] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -55.683051+0.001790j
[2025-08-07 23:49:09] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -55.706840+0.003292j
[2025-08-07 23:49:14] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -55.733785+0.000996j
[2025-08-07 23:49:18] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -55.724186+0.002294j
[2025-08-07 23:49:22] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -55.692790-0.001059j
[2025-08-07 23:49:26] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -55.802734+0.002479j
[2025-08-07 23:49:30] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -55.768510-0.000286j
[2025-08-07 23:49:34] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -55.844954-0.005065j
[2025-08-07 23:49:38] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -55.857602-0.003199j
[2025-08-07 23:49:42] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -55.775455+0.002492j
[2025-08-07 23:49:47] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -55.797832-0.001603j
[2025-08-07 23:49:51] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -55.771562+0.004732j
[2025-08-07 23:49:55] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -55.798653-0.003605j
[2025-08-07 23:49:59] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -55.830011-0.002318j
[2025-08-07 23:50:03] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -55.822102+0.000381j
[2025-08-07 23:50:07] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -55.756939-0.003773j
[2025-08-07 23:50:11] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -55.760569+0.000349j
[2025-08-07 23:50:15] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -55.797480+0.007930j
[2025-08-07 23:50:19] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -55.812704-0.001643j
[2025-08-07 23:50:24] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -55.888762-0.001141j
[2025-08-07 23:50:28] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -55.882268-0.000765j
[2025-08-07 23:50:32] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -55.871478-0.002548j
[2025-08-07 23:50:36] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -55.781477+0.000083j
[2025-08-07 23:50:40] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -55.816928+0.001760j
[2025-08-07 23:50:44] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -55.875443-0.001072j
[2025-08-07 23:50:48] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -55.789685+0.001364j
[2025-08-07 23:50:52] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -55.895841+0.003111j
[2025-08-07 23:50:57] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -55.750359+0.000478j
[2025-08-07 23:51:01] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -55.754573+0.001076j
[2025-08-07 23:51:05] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -55.768436-0.008144j
[2025-08-07 23:51:09] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -55.758368-0.004554j
[2025-08-07 23:51:13] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -55.717485-0.000872j
[2025-08-07 23:51:17] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -55.765039-0.000920j
[2025-08-07 23:51:21] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -55.758313-0.001761j
[2025-08-07 23:51:25] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -55.815898+0.001671j
[2025-08-07 23:51:30] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -55.821066+0.003136j
[2025-08-07 23:51:34] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -55.815962+0.000875j
[2025-08-07 23:51:38] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -55.822805+0.005387j
[2025-08-07 23:51:42] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -55.924544+0.006277j
[2025-08-07 23:51:46] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -56.038501-0.001287j
[2025-08-07 23:51:50] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -55.889897-0.000797j
[2025-08-07 23:51:54] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -55.784645-0.002427j
[2025-08-07 23:51:59] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -55.935390-0.001432j
[2025-08-07 23:52:03] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -55.892635-0.004525j
[2025-08-07 23:52:07] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -55.822189+0.006779j
[2025-08-07 23:52:11] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -55.656511+0.003689j
[2025-08-07 23:52:15] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -55.865604-0.000557j
[2025-08-07 23:52:19] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -55.811402+0.002091j
[2025-08-07 23:52:23] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -55.803964+0.004031j
[2025-08-07 23:52:27] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -55.721877+0.003103j
[2025-08-07 23:52:31] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -55.745986-0.002928j
[2025-08-07 23:52:36] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -55.689981+0.002724j
[2025-08-07 23:52:40] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -55.644903-0.005300j
[2025-08-07 23:52:44] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -55.807736-0.002671j
[2025-08-07 23:52:48] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -55.764299+0.000382j
[2025-08-07 23:52:52] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -55.766033-0.006782j
[2025-08-07 23:52:56] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -55.865651+0.000419j
[2025-08-07 23:53:00] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -55.999017+0.005477j
[2025-08-07 23:53:04] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -55.884211+0.000177j
[2025-08-07 23:53:09] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -55.765754-0.001238j
[2025-08-07 23:53:13] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -55.793553+0.003080j
[2025-08-07 23:53:17] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -55.810338-0.005413j
[2025-08-07 23:53:21] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -55.783413+0.004214j
[2025-08-07 23:53:25] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -55.877809-0.003216j
[2025-08-07 23:53:29] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -55.830864-0.003173j
[2025-08-07 23:53:33] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -55.972720+0.001140j
[2025-08-07 23:53:37] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -55.943216+0.000401j
[2025-08-07 23:53:42] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -55.883910+0.001548j
[2025-08-07 23:53:46] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -55.910988+0.004546j
[2025-08-07 23:53:50] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -55.954540-0.005213j
[2025-08-07 23:53:54] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -55.822981-0.005177j
[2025-08-07 23:53:58] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -55.790668-0.008021j
[2025-08-07 23:54:02] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -55.844116-0.000078j
[2025-08-07 23:54:06] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -55.747782+0.001699j
[2025-08-07 23:54:10] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -55.837723-0.000319j
[2025-08-07 23:54:15] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -55.777022+0.002197j
[2025-08-07 23:54:19] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -55.735994-0.001578j
[2025-08-07 23:54:23] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -55.820619-0.002503j
[2025-08-07 23:54:27] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -55.686089-0.000018j
[2025-08-07 23:54:31] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -55.800066+0.003469j
[2025-08-07 23:54:35] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -55.757418+0.000053j
[2025-08-07 23:54:39] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -55.661139+0.003214j
[2025-08-07 23:54:43] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -55.722063-0.001966j
[2025-08-07 23:54:48] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -55.755174+0.002702j
[2025-08-07 23:54:52] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -55.893834-0.002294j
[2025-08-07 23:54:56] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -55.832250+0.002445j
[2025-08-07 23:54:56] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-08-07 23:55:00] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -55.796646+0.001299j
[2025-08-07 23:55:04] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -55.750706+0.000933j
[2025-08-07 23:55:08] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -55.851306-0.001849j
[2025-08-07 23:55:12] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -55.797464-0.004965j
[2025-08-07 23:55:16] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -55.852740-0.002912j
[2025-08-07 23:55:20] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -55.754130+0.001372j
[2025-08-07 23:55:25] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -55.760768-0.001611j
[2025-08-07 23:55:29] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -55.875329-0.003544j
[2025-08-07 23:55:33] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -55.843675-0.001072j
[2025-08-07 23:55:37] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -55.883113+0.002663j
[2025-08-07 23:55:41] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -55.935074+0.000291j
[2025-08-07 23:55:45] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -55.939874-0.000050j
[2025-08-07 23:55:49] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -55.906557-0.006908j
[2025-08-07 23:55:54] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -55.864369-0.000098j
[2025-08-07 23:55:58] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -55.911165-0.006087j
[2025-08-07 23:56:02] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -55.795192+0.003425j
[2025-08-07 23:56:06] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -55.834128-0.000625j
[2025-08-07 23:56:10] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -55.860805+0.000154j
[2025-08-07 23:56:14] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -55.894461-0.001620j
[2025-08-07 23:56:18] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -55.764679-0.005284j
[2025-08-07 23:56:22] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -55.859441+0.004019j
[2025-08-07 23:56:26] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -55.802044-0.005361j
[2025-08-07 23:56:31] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -55.802550-0.007608j
[2025-08-07 23:56:35] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -55.769167-0.002719j
[2025-08-07 23:56:39] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -55.762868+0.002670j
[2025-08-07 23:56:43] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -55.757509-0.000962j
[2025-08-07 23:56:47] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -55.751089-0.002494j
[2025-08-07 23:56:51] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -55.802249-0.002721j
[2025-08-07 23:56:55] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -55.768926+0.001805j
[2025-08-07 23:56:59] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -55.729353+0.000355j
[2025-08-07 23:57:04] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -55.685212+0.003959j
[2025-08-07 23:57:08] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -55.769288-0.003063j
[2025-08-07 23:57:12] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -55.735487+0.000852j
[2025-08-07 23:57:16] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -55.774891+0.000759j
[2025-08-07 23:57:20] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -55.648218+0.001814j
[2025-08-07 23:57:24] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -55.724554+0.001298j
[2025-08-07 23:57:28] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -55.931335+0.004536j
[2025-08-07 23:57:32] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -55.897401-0.001847j
[2025-08-07 23:57:37] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -55.851082-0.000294j
[2025-08-07 23:57:41] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -55.795636+0.004225j
[2025-08-07 23:57:45] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -55.842964-0.002552j
[2025-08-07 23:57:49] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -55.772224-0.001362j
[2025-08-07 23:57:53] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -55.842630-0.002625j
[2025-08-07 23:57:57] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -55.824824+0.005293j
[2025-08-07 23:58:01] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -55.768164+0.003809j
[2025-08-07 23:58:05] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -55.891168-0.002444j
[2025-08-07 23:58:10] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -55.917980+0.002252j
[2025-08-07 23:58:14] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -55.938683+0.000045j
[2025-08-07 23:58:18] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -55.860580+0.002912j
[2025-08-07 23:58:22] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -55.895004-0.000340j
[2025-08-07 23:58:26] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -55.911337-0.001563j
[2025-08-07 23:58:30] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -55.744961+0.005209j
[2025-08-07 23:58:34] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -55.602594+0.001874j
[2025-08-07 23:58:38] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -55.713347+0.005057j
[2025-08-07 23:58:43] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -55.746035+0.000406j
[2025-08-07 23:58:47] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -55.810402-0.007685j
[2025-08-07 23:58:51] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -55.817267+0.000478j
[2025-08-07 23:58:55] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -55.900789+0.001476j
[2025-08-07 23:58:59] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -55.838778+0.000767j
[2025-08-07 23:59:03] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -55.824014-0.001734j
[2025-08-07 23:59:07] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -55.886616-0.000561j
[2025-08-07 23:59:11] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -55.738130+0.003994j
[2025-08-07 23:59:16] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -55.971422+0.003146j
[2025-08-07 23:59:20] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -55.853017-0.001029j
[2025-08-07 23:59:24] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -55.728289+0.000309j
[2025-08-07 23:59:28] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -55.708770-0.002810j
[2025-08-07 23:59:32] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -55.645126+0.000144j
[2025-08-07 23:59:36] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -55.696637-0.005241j
[2025-08-07 23:59:40] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -55.661047-0.003391j
[2025-08-07 23:59:44] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -55.853194-0.003229j
[2025-08-07 23:59:49] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -55.883514+0.002028j
[2025-08-07 23:59:53] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -55.921347-0.000545j
[2025-08-07 23:59:57] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -55.709346-0.002241j
[2025-08-08 00:00:01] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -55.833362+0.001397j
[2025-08-08 00:00:05] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -55.697501+0.000934j
[2025-08-08 00:00:09] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -55.774741+0.005597j
[2025-08-08 00:00:13] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -55.744903+0.002308j
[2025-08-08 00:00:17] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -55.742710-0.005726j
[2025-08-08 00:00:22] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -55.796937-0.000232j
[2025-08-08 00:00:26] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -55.845195-0.000800j
[2025-08-08 00:00:30] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -55.873224-0.001204j
[2025-08-08 00:00:34] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -55.827422-0.006181j
[2025-08-08 00:00:38] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -55.766199-0.002812j
[2025-08-08 00:00:42] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -55.752601-0.005380j
[2025-08-08 00:00:46] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -55.824664+0.000065j
[2025-08-08 00:00:50] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -55.791580+0.001659j
[2025-08-08 00:00:55] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -55.814969+0.001987j
[2025-08-08 00:00:59] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -55.850799-0.001015j
[2025-08-08 00:01:03] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -55.828924+0.003157j
[2025-08-08 00:01:07] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -55.871139+0.001723j
[2025-08-08 00:01:11] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -55.863739+0.000027j
[2025-08-08 00:01:15] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -55.878056+0.004617j
[2025-08-08 00:01:19] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -55.897822+0.001396j
[2025-08-08 00:01:23] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -55.875939+0.000461j
[2025-08-08 00:01:28] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -55.871663+0.004313j
[2025-08-08 00:01:32] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -55.661369+0.002518j
[2025-08-08 00:01:36] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -55.824790+0.003375j
[2025-08-08 00:01:40] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -55.815712+0.003333j
[2025-08-08 00:01:44] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -55.659109-0.000117j
[2025-08-08 00:01:48] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -55.813518-0.004580j
[2025-08-08 00:01:48] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-08-08 00:01:52] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -55.723650+0.000213j
[2025-08-08 00:01:56] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -55.832925+0.001795j
[2025-08-08 00:02:01] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -55.917626+0.003329j
[2025-08-08 00:02:05] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -55.814303+0.001547j
[2025-08-08 00:02:09] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -55.801390+0.002882j
[2025-08-08 00:02:13] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -55.809302-0.002901j
[2025-08-08 00:02:17] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -55.778149-0.001269j
[2025-08-08 00:02:21] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -55.845784-0.002952j
[2025-08-08 00:02:25] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -55.971748-0.003735j
[2025-08-08 00:02:29] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -55.969887-0.000623j
[2025-08-08 00:02:34] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -55.818679+0.001156j
[2025-08-08 00:02:38] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -55.720334-0.003647j
[2025-08-08 00:02:42] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -55.883411-0.001654j
[2025-08-08 00:02:46] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -55.629468+0.000864j
[2025-08-08 00:02:50] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -55.735198+0.003717j
[2025-08-08 00:02:54] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -55.793591+0.002463j
[2025-08-08 00:02:58] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -55.850820+0.000414j
[2025-08-08 00:03:03] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -55.841781+0.005183j
[2025-08-08 00:03:07] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -55.928031+0.000348j
[2025-08-08 00:03:11] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -55.908251+0.001214j
[2025-08-08 00:03:15] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -55.951707+0.002693j
[2025-08-08 00:03:19] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -55.776668-0.003068j
[2025-08-08 00:03:23] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -55.772256-0.006027j
[2025-08-08 00:03:27] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -55.708693+0.002817j
[2025-08-08 00:03:31] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -55.562782+0.004985j
[2025-08-08 00:03:35] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -55.629583-0.000677j
[2025-08-08 00:03:40] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -55.701138-0.002249j
[2025-08-08 00:03:44] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -55.629253-0.003645j
[2025-08-08 00:03:48] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -55.670385-0.000049j
[2025-08-08 00:03:52] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -55.738767-0.000741j
[2025-08-08 00:03:56] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -55.638437+0.001594j
[2025-08-08 00:04:00] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -55.614423-0.000539j
[2025-08-08 00:04:04] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -55.661201+0.005980j
[2025-08-08 00:04:08] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -55.726546-0.001756j
[2025-08-08 00:04:13] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -55.708945-0.003759j
[2025-08-08 00:04:17] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -55.779854-0.001296j
[2025-08-08 00:04:21] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -55.637450-0.003071j
[2025-08-08 00:04:25] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -55.780253-0.000117j
[2025-08-08 00:04:29] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -55.640426-0.001988j
[2025-08-08 00:04:33] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -55.753075-0.000881j
[2025-08-08 00:04:37] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -55.825327+0.002267j
[2025-08-08 00:04:41] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -55.819713-0.004387j
[2025-08-08 00:04:46] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -55.892390+0.004856j
[2025-08-08 00:04:50] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -55.964046+0.003375j
[2025-08-08 00:04:54] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -55.807640-0.004020j
[2025-08-08 00:04:58] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -55.852448+0.004566j
[2025-08-08 00:05:02] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -55.772342-0.000935j
[2025-08-08 00:05:06] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -55.837912-0.003537j
[2025-08-08 00:05:10] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -55.931003+0.000759j
[2025-08-08 00:05:14] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -55.840371-0.000145j
[2025-08-08 00:05:19] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -55.805655+0.001671j
[2025-08-08 00:05:23] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -55.840782-0.002882j
[2025-08-08 00:05:27] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -55.834414-0.001610j
[2025-08-08 00:05:31] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -55.948570-0.003883j
[2025-08-08 00:05:35] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -55.930586-0.001603j
[2025-08-08 00:05:39] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -55.937361-0.001237j
[2025-08-08 00:05:43] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -55.987836-0.003125j
[2025-08-08 00:05:47] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -55.827396+0.000682j
[2025-08-08 00:05:52] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -55.807706-0.002788j
[2025-08-08 00:05:56] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -55.750656-0.001291j
[2025-08-08 00:06:00] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -55.735799+0.002432j
[2025-08-08 00:06:04] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -55.776028-0.003648j
[2025-08-08 00:06:08] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -55.828976-0.000656j
[2025-08-08 00:06:12] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -55.786318+0.001449j
[2025-08-08 00:06:16] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -55.871478-0.001477j
[2025-08-08 00:06:20] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -55.911629-0.001322j
[2025-08-08 00:06:25] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -55.944140+0.001681j
[2025-08-08 00:06:29] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -55.883296-0.001207j
[2025-08-08 00:06:33] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -55.802537-0.000956j
[2025-08-08 00:06:37] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -55.881010+0.002479j
[2025-08-08 00:06:41] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -55.811058-0.001227j
[2025-08-08 00:06:45] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -55.940119+0.007254j
[2025-08-08 00:06:49] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -55.838410+0.002404j
[2025-08-08 00:06:53] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -55.732649+0.001855j
[2025-08-08 00:06:57] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -55.626791+0.002532j
[2025-08-08 00:07:02] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -55.763108+0.001960j
[2025-08-08 00:07:06] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -55.771076+0.001956j
[2025-08-08 00:07:10] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -55.814513+0.000343j
[2025-08-08 00:07:14] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -55.897501+0.000476j
[2025-08-08 00:07:18] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -55.993434-0.002841j
[2025-08-08 00:07:22] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -55.913952-0.000616j
[2025-08-08 00:07:26] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -55.841656-0.002136j
[2025-08-08 00:07:30] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -55.866503+0.001027j
[2025-08-08 00:07:35] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -55.779255+0.004978j
[2025-08-08 00:07:39] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -55.760548+0.001710j
[2025-08-08 00:07:43] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -55.763346-0.005049j
[2025-08-08 00:07:47] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -55.670219+0.000754j
[2025-08-08 00:07:51] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -55.700208+0.003160j
[2025-08-08 00:07:55] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -55.748414-0.003638j
[2025-08-08 00:07:59] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -55.712032+0.007715j
[2025-08-08 00:08:03] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -55.692375+0.002547j
[2025-08-08 00:08:08] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -55.840494+0.000155j
[2025-08-08 00:08:12] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -55.730875+0.001708j
[2025-08-08 00:08:16] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -55.757425+0.005370j
[2025-08-08 00:08:20] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -55.796749+0.000042j
[2025-08-08 00:08:24] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -55.756914-0.000041j
[2025-08-08 00:08:28] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -55.773034+0.004248j
[2025-08-08 00:08:32] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -55.800447+0.002748j
[2025-08-08 00:08:36] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -55.846431-0.000778j
[2025-08-08 00:08:41] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -55.749420+0.001414j
[2025-08-08 00:08:41] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-08-08 00:08:45] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -55.798901+0.002350j
[2025-08-08 00:08:49] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -55.736720+0.000098j
[2025-08-08 00:08:53] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -55.861975+0.000626j
[2025-08-08 00:08:57] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -55.867658+0.001086j
[2025-08-08 00:09:01] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -55.908092-0.005282j
[2025-08-08 00:09:05] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -55.855292+0.000307j
[2025-08-08 00:09:09] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -55.925922+0.002165j
[2025-08-08 00:09:14] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -55.923877+0.002677j
[2025-08-08 00:09:18] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -55.929160-0.001558j
[2025-08-08 00:09:22] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -55.772549-0.001547j
[2025-08-08 00:09:26] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -55.924427+0.000360j
[2025-08-08 00:09:30] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -55.852438-0.003416j
[2025-08-08 00:09:34] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -55.903171-0.004514j
[2025-08-08 00:09:38] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -55.900966+0.002815j
[2025-08-08 00:09:42] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -55.888167-0.003293j
[2025-08-08 00:09:47] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -55.887532+0.000122j
[2025-08-08 00:09:51] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -55.858895-0.004780j
[2025-08-08 00:09:55] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -55.814545+0.003511j
[2025-08-08 00:09:59] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -55.878691+0.003650j
[2025-08-08 00:10:03] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -55.763334-0.004364j
[2025-08-08 00:10:07] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -55.764423-0.004626j
[2025-08-08 00:10:11] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -55.810774+0.001634j
[2025-08-08 00:10:15] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -55.851450+0.001153j
[2025-08-08 00:10:20] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -55.800107+0.001031j
[2025-08-08 00:10:24] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -55.960340-0.000602j
[2025-08-08 00:10:28] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -55.968377-0.000447j
[2025-08-08 00:10:32] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -55.966996+0.006257j
[2025-08-08 00:10:36] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -55.852914+0.004680j
[2025-08-08 00:10:40] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -55.911699+0.000140j
[2025-08-08 00:10:44] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -55.847302-0.002172j
[2025-08-08 00:10:48] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -55.826796+0.001672j
[2025-08-08 00:10:53] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -55.880520+0.000733j
[2025-08-08 00:10:57] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -55.776393+0.001882j
[2025-08-08 00:11:01] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -55.820123+0.004236j
[2025-08-08 00:11:05] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -55.799759+0.001352j
[2025-08-08 00:11:09] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -55.827363+0.000179j
[2025-08-08 00:11:13] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -55.737044+0.000676j
[2025-08-08 00:11:17] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -55.833755+0.003934j
[2025-08-08 00:11:21] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -55.758295-0.004257j
[2025-08-08 00:11:26] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -55.855703+0.004454j
[2025-08-08 00:11:30] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -55.902046-0.005990j
[2025-08-08 00:11:34] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -55.896708-0.004852j
[2025-08-08 00:11:38] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -55.977647-0.003845j
[2025-08-08 00:11:42] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -55.836568+0.001861j
[2025-08-08 00:11:46] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -55.851883+0.007855j
[2025-08-08 00:11:50] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -55.921759+0.000348j
[2025-08-08 00:11:55] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -55.811847+0.003441j
[2025-08-08 00:11:59] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -55.834226-0.001729j
[2025-08-08 00:12:03] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -55.910552+0.001309j
[2025-08-08 00:12:07] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -55.901574-0.003691j
[2025-08-08 00:12:11] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -55.905869-0.001343j
[2025-08-08 00:12:15] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -55.946629-0.004879j
[2025-08-08 00:12:19] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -56.021831-0.004797j
[2025-08-08 00:12:23] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -55.827359-0.003610j
[2025-08-08 00:12:27] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -55.942848+0.000788j
[2025-08-08 00:12:32] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -55.928413+0.003425j
[2025-08-08 00:12:36] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -55.796853-0.000034j
[2025-08-08 00:12:40] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -55.770887+0.005229j
[2025-08-08 00:12:44] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -55.773769-0.004952j
[2025-08-08 00:12:48] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -55.812519+0.000883j
[2025-08-08 00:12:52] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -55.759173+0.004574j
[2025-08-08 00:12:56] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -55.767534+0.000855j
[2025-08-08 00:13:00] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -55.815336+0.000405j
[2025-08-08 00:13:05] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -55.759902+0.000216j
[2025-08-08 00:13:09] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -55.853975+0.003687j
[2025-08-08 00:13:13] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -55.829697+0.000387j
[2025-08-08 00:13:17] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -55.845159+0.001225j
[2025-08-08 00:13:21] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -55.915837+0.000933j
[2025-08-08 00:13:25] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -55.931244+0.000623j
[2025-08-08 00:13:29] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -55.867294-0.000338j
[2025-08-08 00:13:33] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -55.888040-0.000519j
[2025-08-08 00:13:38] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -55.859571-0.000758j
[2025-08-08 00:13:42] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -55.822205+0.000900j
[2025-08-08 00:13:46] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -55.872662-0.003620j
[2025-08-08 00:13:50] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -55.858583-0.000789j
[2025-08-08 00:13:54] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -55.786524+0.003344j
[2025-08-08 00:13:58] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -55.901239-0.001639j
[2025-08-08 00:14:02] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -55.812767-0.006820j
[2025-08-08 00:14:06] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -55.854351-0.004810j
[2025-08-08 00:14:11] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -55.886258+0.001521j
[2025-08-08 00:14:15] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -55.840888-0.003160j
[2025-08-08 00:14:19] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -55.784771-0.000694j
[2025-08-08 00:14:23] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -55.790809+0.000754j
[2025-08-08 00:14:27] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -55.670768+0.000692j
[2025-08-08 00:14:31] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -55.865287+0.005372j
[2025-08-08 00:14:35] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -55.694101+0.001004j
[2025-08-08 00:14:39] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -55.769795+0.002354j
[2025-08-08 00:14:44] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -55.664626+0.005398j
[2025-08-08 00:14:48] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -55.733465-0.006257j
[2025-08-08 00:14:52] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -55.731887-0.002177j
[2025-08-08 00:14:56] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -55.894766+0.000020j
[2025-08-08 00:15:00] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -55.843217-0.001987j
[2025-08-08 00:15:04] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -55.902259-0.000805j
[2025-08-08 00:15:08] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -55.868430+0.000890j
[2025-08-08 00:15:12] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -55.800737+0.000972j
[2025-08-08 00:15:17] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -55.889667-0.001517j
[2025-08-08 00:15:21] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -55.814932-0.001747j
[2025-08-08 00:15:25] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -55.865322-0.000114j
[2025-08-08 00:15:29] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -55.862504+0.001168j
[2025-08-08 00:15:33] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -55.856909-0.002880j
[2025-08-08 00:15:33] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-08-08 00:15:37] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -55.926703+0.005793j
[2025-08-08 00:15:41] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -55.882779-0.004865j
[2025-08-08 00:15:45] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -55.980736-0.004072j
[2025-08-08 00:15:49] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -55.983045+0.000789j
[2025-08-08 00:15:54] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -55.909767+0.000207j
[2025-08-08 00:15:58] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -56.004583-0.007533j
[2025-08-08 00:16:02] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -56.022893-0.002589j
[2025-08-08 00:16:06] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -55.771083+0.007824j
[2025-08-08 00:16:10] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -55.879506+0.001620j
[2025-08-08 00:16:14] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -55.826591-0.000531j
[2025-08-08 00:16:18] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -55.915329+0.000146j
[2025-08-08 00:16:22] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -55.800780+0.001833j
[2025-08-08 00:16:27] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -55.834709+0.000557j
[2025-08-08 00:16:31] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -55.877477-0.001434j
[2025-08-08 00:16:35] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -55.891310+0.005197j
[2025-08-08 00:16:39] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -55.739040+0.000265j
[2025-08-08 00:16:43] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -55.820524-0.003183j
[2025-08-08 00:16:47] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -55.762894+0.003332j
[2025-08-08 00:16:51] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -55.878249+0.005629j
[2025-08-08 00:16:55] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -55.859477-0.003534j
[2025-08-08 00:17:00] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -55.874446+0.003981j
[2025-08-08 00:17:04] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -55.815350-0.003368j
[2025-08-08 00:17:08] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -55.844946-0.001932j
[2025-08-08 00:17:12] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -55.680500-0.000356j
[2025-08-08 00:17:16] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -55.742615+0.006288j
[2025-08-08 00:17:20] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -55.758185+0.001431j
[2025-08-08 00:17:24] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -55.812143-0.001501j
[2025-08-08 00:17:28] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -55.846295+0.003407j
[2025-08-08 00:17:33] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -55.790564+0.001974j
[2025-08-08 00:17:37] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -55.851297-0.004996j
[2025-08-08 00:17:41] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -55.741349-0.004882j
[2025-08-08 00:17:45] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -55.712613-0.001396j
[2025-08-08 00:17:49] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -55.725648-0.000568j
[2025-08-08 00:17:53] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -55.744831-0.001272j
[2025-08-08 00:17:57] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -55.761679+0.003150j
[2025-08-08 00:18:01] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -55.740975-0.000202j
[2025-08-08 00:18:06] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -55.805558+0.003374j
[2025-08-08 00:18:10] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -55.730455+0.002274j
[2025-08-08 00:18:14] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -55.638167-0.002676j
[2025-08-08 00:18:18] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -55.582140-0.002860j
[2025-08-08 00:18:22] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -55.678066-0.000444j
[2025-08-08 00:18:26] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -55.719155-0.004097j
[2025-08-08 00:18:30] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -55.798192+0.004743j
[2025-08-08 00:18:34] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -55.737237-0.004157j
[2025-08-08 00:18:39] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -55.758122-0.000901j
[2025-08-08 00:18:43] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -55.732299+0.004110j
[2025-08-08 00:18:47] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -55.824315+0.003060j
[2025-08-08 00:18:51] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -55.813724-0.000697j
[2025-08-08 00:18:55] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -55.794815-0.000536j
[2025-08-08 00:18:59] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -55.823583-0.000481j
[2025-08-08 00:19:03] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -55.737799-0.002196j
[2025-08-08 00:19:07] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -55.847755+0.000640j
[2025-08-08 00:19:12] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -55.793147+0.001726j
[2025-08-08 00:19:16] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -55.815619-0.001834j
[2025-08-08 00:19:20] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -55.753115-0.000977j
[2025-08-08 00:19:24] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -55.803625-0.001834j
[2025-08-08 00:19:28] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -55.626552-0.001640j
[2025-08-08 00:19:32] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -55.704854-0.002087j
[2025-08-08 00:19:36] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -55.672205+0.007124j
[2025-08-08 00:19:40] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -55.814816-0.000435j
[2025-08-08 00:19:45] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -55.718776+0.002202j
[2025-08-08 00:19:49] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -55.825221-0.000305j
[2025-08-08 00:19:53] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -55.769018+0.004594j
[2025-08-08 00:19:57] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -55.766784-0.004130j
[2025-08-08 00:20:01] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -55.706582-0.006905j
[2025-08-08 00:20:05] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -55.682340-0.000433j
[2025-08-08 00:20:09] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -55.636871-0.004807j
[2025-08-08 00:20:13] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -55.618002-0.001279j
[2025-08-08 00:20:18] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -55.691310+0.001288j
[2025-08-08 00:20:22] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -55.742271-0.000131j
[2025-08-08 00:20:26] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -55.695380-0.002632j
[2025-08-08 00:20:30] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -55.774130-0.004924j
[2025-08-08 00:20:34] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -55.705608-0.000652j
[2025-08-08 00:20:38] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -55.769801-0.007387j
[2025-08-08 00:20:42] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -55.649509+0.000185j
[2025-08-08 00:20:46] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -55.688448+0.000434j
[2025-08-08 00:20:50] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -55.618215-0.001558j
[2025-08-08 00:20:55] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -55.706069-0.004067j
[2025-08-08 00:20:59] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -55.689543-0.003844j
[2025-08-08 00:21:03] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -55.701727-0.001999j
[2025-08-08 00:21:07] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -55.786668-0.000446j
[2025-08-08 00:21:11] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -55.854870-0.002821j
[2025-08-08 00:21:15] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -55.747992+0.000485j
[2025-08-08 00:21:19] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -55.853261+0.001296j
[2025-08-08 00:21:24] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -55.774222+0.003043j
[2025-08-08 00:21:28] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -55.882447-0.006871j
[2025-08-08 00:21:32] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -55.778543+0.001610j
[2025-08-08 00:21:36] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -55.747015-0.001680j
[2025-08-08 00:21:40] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -55.798145-0.002812j
[2025-08-08 00:21:44] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -55.894239-0.000450j
[2025-08-08 00:21:48] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -55.857139+0.000509j
[2025-08-08 00:21:52] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -55.881358-0.001402j
[2025-08-08 00:21:56] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -55.944063-0.001167j
[2025-08-08 00:22:01] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -55.887104-0.000463j
[2025-08-08 00:22:05] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -55.840102+0.003359j
[2025-08-08 00:22:09] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -55.707629-0.002743j
[2025-08-08 00:22:13] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -55.651374-0.005845j
[2025-08-08 00:22:17] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -55.699082+0.002887j
[2025-08-08 00:22:21] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -55.633977+0.000198j
[2025-08-08 00:22:25] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -55.649627-0.001609j
[2025-08-08 00:22:25] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-08 00:22:30] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -55.725696-0.003713j
[2025-08-08 00:22:34] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -55.757603+0.001517j
[2025-08-08 00:22:38] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -55.749146-0.000148j
[2025-08-08 00:22:42] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -55.643404+0.000453j
[2025-08-08 00:22:46] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -55.747719-0.000221j
[2025-08-08 00:22:50] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -55.785256-0.004622j
[2025-08-08 00:22:54] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -55.837999+0.002318j
[2025-08-08 00:22:58] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -55.861212-0.001654j
[2025-08-08 00:23:03] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -55.790977+0.005397j
[2025-08-08 00:23:07] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -55.798597+0.002522j
[2025-08-08 00:23:11] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -55.726284-0.002780j
[2025-08-08 00:23:15] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -55.756385-0.000420j
[2025-08-08 00:23:19] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -55.853449-0.003870j
[2025-08-08 00:23:23] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -55.762399+0.000612j
[2025-08-08 00:23:27] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -55.726890+0.001173j
[2025-08-08 00:23:31] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -55.717873-0.001120j
[2025-08-08 00:23:35] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -55.772351-0.002269j
[2025-08-08 00:23:40] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -55.735617+0.001187j
[2025-08-08 00:23:44] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -55.770782+0.000788j
[2025-08-08 00:23:48] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -55.854397-0.000844j
[2025-08-08 00:23:52] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -55.806719+0.004005j
[2025-08-08 00:23:56] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -55.780048+0.000849j
[2025-08-08 00:24:00] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -55.787809+0.003294j
[2025-08-08 00:24:04] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -55.726070+0.000007j
[2025-08-08 00:24:08] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -55.780896+0.000955j
[2025-08-08 00:24:13] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -55.744606+0.004946j
[2025-08-08 00:24:17] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -55.820570-0.002224j
[2025-08-08 00:24:21] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -55.746125+0.002296j
[2025-08-08 00:24:25] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -55.786735+0.003696j
[2025-08-08 00:24:29] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -55.763812-0.005220j
[2025-08-08 00:24:33] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -55.840913-0.007285j
[2025-08-08 00:24:37] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -55.908611+0.002630j
[2025-08-08 00:24:41] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -55.835456-0.004824j
[2025-08-08 00:24:46] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -55.874332-0.000458j
[2025-08-08 00:24:50] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -55.892977+0.000125j
[2025-08-08 00:24:54] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -55.879738+0.002341j
[2025-08-08 00:24:58] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -55.889129-0.001680j
[2025-08-08 00:25:02] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -55.951995+0.000704j
[2025-08-08 00:25:06] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -56.004298+0.003213j
[2025-08-08 00:25:10] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -55.921076+0.005165j
[2025-08-08 00:25:14] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -55.772142-0.000145j
[2025-08-08 00:25:19] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -55.802159+0.000263j
[2025-08-08 00:25:23] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -55.789098+0.004971j
[2025-08-08 00:25:27] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -55.837324+0.001808j
[2025-08-08 00:25:31] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -55.949643+0.004712j
[2025-08-08 00:25:35] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -55.791442+0.002300j
[2025-08-08 00:25:39] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -55.847673+0.002379j
[2025-08-08 00:25:43] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -55.796379+0.003466j
[2025-08-08 00:25:47] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -55.800240+0.001121j
[2025-08-08 00:25:52] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -55.905034+0.000681j
[2025-08-08 00:25:52] ✅ Training completed | Restarts: 2
[2025-08-08 00:25:52] ============================================================
[2025-08-08 00:25:52] Training completed | Runtime: 4372.7s
[2025-08-08 00:26:04] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-08 00:26:04] ============================================================
