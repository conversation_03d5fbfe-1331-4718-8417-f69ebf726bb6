[2025-08-28 10:35:35] 使用checkpoint文件: results/L=4/J2=0.00/J1=0.00/training/checkpoints/checkpoint_iter_000700.pkl
[2025-08-28 10:35:46] ✓ 从checkpoint加载参数: 700
[2025-08-28 10:35:46]   - 能量: -52.986439+0.001432j ± 0.088834
[2025-08-28 10:35:46] ================================================================================
[2025-08-28 10:35:46] 加载量子态: L=4, J2=0.00, J1=0.00, checkpoint=checkpoint_iter_000700
[2025-08-28 10:35:46] 设置样本数为: 1048576
[2025-08-28 10:35:46] 开始生成共享样本集...
[2025-08-28 10:37:09] 样本生成完成,耗时: 82.790 秒
[2025-08-28 10:37:09] ================================================================================
[2025-08-28 10:37:09] 开始计算自旋结构因子...
[2025-08-28 10:37:09] 初始化操作符缓存...
[2025-08-28 10:37:09] 预构建所有自旋相关操作符...
[2025-08-28 10:37:09] 开始计算自旋相关函数...
[2025-08-28 10:37:17] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 8.010s
[2025-08-28 10:37:26] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.969s
[2025-08-28 10:37:30] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 4.246s
[2025-08-28 10:37:34] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 4.270s
[2025-08-28 10:37:39] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 4.279s
[2025-08-28 10:37:43] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 4.265s
[2025-08-28 10:37:47] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 4.247s
[2025-08-28 10:37:51] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 4.281s
[2025-08-28 10:37:56] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 4.248s
[2025-08-28 10:38:00] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 4.283s
[2025-08-28 10:38:04] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 4.253s
[2025-08-28 10:38:09] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 4.283s
[2025-08-28 10:38:13] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 4.248s
[2025-08-28 10:38:17] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 4.268s
[2025-08-28 10:38:21] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 4.282s
[2025-08-28 10:38:26] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 4.249s
[2025-08-28 10:38:30] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 4.267s
[2025-08-28 10:38:34] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 4.281s
[2025-08-28 10:38:38] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 4.247s
[2025-08-28 10:38:43] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 4.281s
[2025-08-28 10:38:47] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.269s
[2025-08-28 10:38:51] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.281s
[2025-08-28 10:38:56] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 4.247s
[2025-08-28 10:39:00] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 4.266s
[2025-08-28 10:39:04] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 4.249s
[2025-08-28 10:39:08] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 4.266s
[2025-08-28 10:39:13] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 4.248s
[2025-08-28 10:39:17] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 4.281s
[2025-08-28 10:39:21] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 4.250s
[2025-08-28 10:39:25] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 4.247s
[2025-08-28 10:39:30] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 4.247s
[2025-08-28 10:39:34] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 4.266s
[2025-08-28 10:39:38] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 4.259s
[2025-08-28 10:39:42] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 4.263s
[2025-08-28 10:39:47] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 4.249s
[2025-08-28 10:39:51] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 4.265s
[2025-08-28 10:39:55] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 4.248s
[2025-08-28 10:39:59] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 4.266s
[2025-08-28 10:40:04] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 4.246s
[2025-08-28 10:40:08] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 4.279s
[2025-08-28 10:40:12] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 4.248s
[2025-08-28 10:40:17] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 4.248s
[2025-08-28 10:40:21] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 4.248s
[2025-08-28 10:40:25] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 4.266s
[2025-08-28 10:40:29] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 4.247s
[2025-08-28 10:40:34] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 4.248s
[2025-08-28 10:40:38] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 4.279s
[2025-08-28 10:40:42] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.247s
[2025-08-28 10:40:46] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.280s
[2025-08-28 10:40:51] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.280s
[2025-08-28 10:40:55] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.247s
[2025-08-28 10:40:59] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 4.281s
[2025-08-28 10:41:03] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 4.249s
[2025-08-28 10:41:08] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.266s
[2025-08-28 10:41:12] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.247s
[2025-08-28 10:41:16] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.282s
[2025-08-28 10:41:21] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.248s
[2025-08-28 10:41:25] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 4.247s
[2025-08-28 10:41:29] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 4.247s
[2025-08-28 10:41:33] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 4.283s
[2025-08-28 10:41:38] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.250s
[2025-08-28 10:41:42] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 4.247s
[2025-08-28 10:41:46] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.280s
[2025-08-28 10:41:50] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 4.250s
[2025-08-28 10:41:50] 自旋相关函数计算完成,总耗时 281.44 秒
[2025-08-28 10:41:50] 计算傅里叶变换...
[2025-08-28 10:41:51] 自旋结构因子计算完成
[2025-08-28 10:41:52] 自旋相关函数平均误差: 0.000677
