[2025-08-28 10:54:44] 使用checkpoint文件: results/L=4/J2=0.00/J1=0.00/training/checkpoints/checkpoint_iter_001000.pkl
[2025-08-28 10:54:55] ✓ 从checkpoint加载参数: 1000
[2025-08-28 10:54:55]   - 能量: -52.977018-0.003831j ± 0.087842
[2025-08-28 10:54:55] ================================================================================
[2025-08-28 10:54:55] 加载量子态: L=4, J2=0.00, J1=0.00, checkpoint=checkpoint_iter_001000
[2025-08-28 10:54:55] 设置样本数为: 1048576
[2025-08-28 10:54:55] 开始生成共享样本集...
[2025-08-28 10:56:17] 样本生成完成,耗时: 82.659 秒
[2025-08-28 10:56:17] ================================================================================
[2025-08-28 10:56:17] 开始计算自旋结构因子...
[2025-08-28 10:56:17] 初始化操作符缓存...
[2025-08-28 10:56:17] 预构建所有自旋相关操作符...
[2025-08-28 10:56:18] 开始计算自旋相关函数...
[2025-08-28 10:56:25] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.648s
[2025-08-28 10:56:34] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.984s
[2025-08-28 10:56:38] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 4.245s
[2025-08-28 10:56:43] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 4.269s
[2025-08-28 10:56:47] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 4.273s
[2025-08-28 10:56:51] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 4.267s
[2025-08-28 10:56:56] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 4.243s
[2025-08-28 10:57:00] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 4.287s
[2025-08-28 10:57:04] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 4.243s
[2025-08-28 10:57:08] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 4.292s
[2025-08-28 10:57:13] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 4.258s
[2025-08-28 10:57:17] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 4.274s
[2025-08-28 10:57:21] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 4.245s
[2025-08-28 10:57:25] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 4.268s
[2025-08-28 10:57:30] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 4.273s
[2025-08-28 10:57:34] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 4.253s
[2025-08-28 10:57:38] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 4.266s
[2025-08-28 10:57:42] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 4.287s
[2025-08-28 10:57:47] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 4.247s
[2025-08-28 10:57:51] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 4.290s
[2025-08-28 10:57:55] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.271s
[2025-08-28 10:58:00] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.274s
[2025-08-28 10:58:04] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 4.246s
[2025-08-28 10:58:08] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 4.267s
[2025-08-28 10:58:12] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 4.256s
[2025-08-28 10:58:17] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 4.268s
[2025-08-28 10:58:21] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 4.248s
[2025-08-28 10:58:25] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 4.292s
[2025-08-28 10:58:29] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 4.257s
[2025-08-28 10:58:34] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 4.244s
[2025-08-28 10:58:38] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 4.246s
[2025-08-28 10:58:42] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 4.269s
[2025-08-28 10:58:47] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 4.263s
[2025-08-28 10:58:51] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 4.267s
[2025-08-28 10:58:55] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 4.257s
[2025-08-28 10:58:59] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 4.267s
[2025-08-28 10:59:04] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 4.248s
[2025-08-28 10:59:08] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 4.269s
[2025-08-28 10:59:12] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 4.248s
[2025-08-28 10:59:16] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 4.275s
[2025-08-28 10:59:21] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 4.248s
[2025-08-28 10:59:25] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 4.256s
[2025-08-28 10:59:29] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 4.246s
[2025-08-28 10:59:33] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 4.269s
[2025-08-28 10:59:38] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 4.245s
[2025-08-28 10:59:42] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 4.245s
[2025-08-28 10:59:46] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 4.281s
[2025-08-28 10:59:50] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.247s
[2025-08-28 10:59:55] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.278s
[2025-08-28 10:59:59] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.287s
[2025-08-28 11:00:03] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.245s
[2025-08-28 11:00:08] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 4.290s
[2025-08-28 11:00:12] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 4.254s
[2025-08-28 11:00:16] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.268s
[2025-08-28 11:00:20] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.244s
[2025-08-28 11:00:25] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.290s
[2025-08-28 11:00:29] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.244s
[2025-08-28 11:00:33] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 4.244s
[2025-08-28 11:00:37] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 4.245s
[2025-08-28 11:00:42] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 4.287s
[2025-08-28 11:00:46] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.253s
[2025-08-28 11:00:50] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 4.245s
[2025-08-28 11:00:54] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.283s
[2025-08-28 11:00:59] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 4.253s
[2025-08-28 11:00:59] 自旋相关函数计算完成,总耗时 281.18 秒
[2025-08-28 11:00:59] 计算傅里叶变换...
[2025-08-28 11:01:00] 自旋结构因子计算完成
[2025-08-28 11:01:01] 自旋相关函数平均误差: 0.000682
