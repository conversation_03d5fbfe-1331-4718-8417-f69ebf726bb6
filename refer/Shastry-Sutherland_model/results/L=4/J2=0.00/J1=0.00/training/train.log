[2025-08-22 17:43:51] ✓ 从checkpoint恢复: results/L=4/J2=0.00/J1=0.01/training/checkpoints/final_GCNN.pkl
[2025-08-22 17:43:51]   - 迭代次数: final
[2025-08-22 17:43:51]   - 能量: -53.457593+0.001254j ± 0.086867
[2025-08-22 17:43:51]   - 时间戳: 2025-08-08T00:27:52.685025+08:00
[2025-08-22 17:44:01] ✓ 变分状态参数已从checkpoint恢复
[2025-08-22 17:44:01] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-22 17:44:01] ==================================================
[2025-08-22 17:44:01] GCNN for Shastry-Sutherland Model
[2025-08-22 17:44:01] ==================================================
[2025-08-22 17:44:01] System parameters:
[2025-08-22 17:44:01]   - System size: L=4, N=64
[2025-08-22 17:44:01]   - System parameters: J1=0.0, J2=0.0, Q=1.0
[2025-08-22 17:44:01] --------------------------------------------------
[2025-08-22 17:44:01] Model parameters:
[2025-08-22 17:44:01]   - Number of layers = 4
[2025-08-22 17:44:01]   - Number of features = 4
[2025-08-22 17:44:01]   - Total parameters = 12572
[2025-08-22 17:44:01] --------------------------------------------------
[2025-08-22 17:44:01] Training parameters:
[2025-08-22 17:44:01]   - Learning rate: 0.015
[2025-08-22 17:44:01]   - Total iterations: 1050
[2025-08-22 17:44:01]   - Annealing cycles: 3
[2025-08-22 17:44:01]   - Initial period: 150
[2025-08-22 17:44:01]   - Period multiplier: 2.0
[2025-08-22 17:44:01]   - Temperature range: 0.0-1.0
[2025-08-22 17:44:01]   - Samples: 4096
[2025-08-22 17:44:01]   - Discarded samples: 0
[2025-08-22 17:44:01]   - Chunk size: 2048
[2025-08-22 17:44:01]   - Diagonal shift: 0.2
[2025-08-22 17:44:01]   - Gradient clipping: 1.0
[2025-08-22 17:44:01]   - Checkpoint enabled: interval=100
[2025-08-22 17:44:01]   - Checkpoint directory: results/L=4/J2=0.00/J1=0.00/training/checkpoints
[2025-08-22 17:44:01] --------------------------------------------------
[2025-08-22 17:44:01] Device status:
[2025-08-22 17:44:01]   - Devices model: NVIDIA H200 NVL
[2025-08-22 17:44:01]   - Number of devices: 1
[2025-08-22 17:44:01]   - Sharding: True
[2025-08-22 17:44:01] ============================================================
[2025-08-22 17:44:39] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -53.022441-0.003842j
[2025-08-22 17:45:00] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -53.041258-0.004393j
[2025-08-22 17:45:03] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -53.094597+0.000353j
[2025-08-22 17:45:05] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -53.175715-0.001966j
[2025-08-22 17:45:08] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -53.061347-0.002197j
[2025-08-22 17:45:11] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -53.234008-0.008494j
[2025-08-22 17:45:14] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -53.199900-0.006775j
[2025-08-22 17:45:17] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -53.302889+0.003920j
[2025-08-22 17:45:20] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -53.167893+0.000065j
[2025-08-22 17:45:23] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -53.209359+0.001249j
[2025-08-22 17:45:25] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -53.252539-0.001216j
[2025-08-22 17:45:28] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -53.153232+0.001029j
[2025-08-22 17:45:31] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -53.094156-0.000116j
[2025-08-22 17:45:34] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -52.966888+0.004131j
[2025-08-22 17:45:37] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -53.090173+0.001399j
[2025-08-22 17:45:40] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -53.072447+0.002692j
[2025-08-22 17:45:43] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -53.187604-0.001888j
[2025-08-22 17:45:45] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -53.101528+0.000055j
[2025-08-22 17:45:48] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -53.098471-0.001369j
[2025-08-22 17:45:51] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -53.111810-0.001302j
[2025-08-22 17:45:54] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -53.072198-0.000727j
[2025-08-22 17:45:57] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -53.050888-0.000987j
[2025-08-22 17:46:00] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -52.959347+0.001105j
[2025-08-22 17:46:03] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -52.952530-0.000150j
[2025-08-22 17:46:05] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -53.046553-0.003709j
[2025-08-22 17:46:09] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -52.973039-0.007716j
[2025-08-22 17:46:11] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -53.084232-0.003629j
[2025-08-22 17:46:14] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -53.001529+0.002833j
[2025-08-22 17:46:17] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -52.982176-0.005700j
[2025-08-22 17:46:20] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -52.988005+0.000381j
[2025-08-22 17:46:23] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -52.998574+0.004828j
[2025-08-22 17:46:26] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -53.058814-0.002784j
[2025-08-22 17:46:29] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -53.088553+0.004848j
[2025-08-22 17:46:31] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -53.055435+0.002873j
[2025-08-22 17:46:34] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -53.000802-0.000084j
[2025-08-22 17:46:37] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -53.006771-0.003602j
[2025-08-22 17:46:40] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -53.135982+0.000153j
[2025-08-22 17:46:43] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -53.017795+0.001963j
[2025-08-22 17:46:46] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -52.979299+0.003119j
[2025-08-22 17:46:49] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -53.188475-0.002080j
[2025-08-22 17:46:51] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -53.098056-0.004522j
[2025-08-22 17:46:54] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -53.087719-0.002842j
[2025-08-22 17:46:57] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -52.960383+0.002093j
[2025-08-22 17:47:00] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -53.113745-0.002907j
[2025-08-22 17:47:03] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -53.132829-0.002221j
[2025-08-22 17:47:06] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -53.025816+0.003485j
[2025-08-22 17:47:09] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -53.178525+0.002821j
[2025-08-22 17:47:11] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -53.078240+0.003502j
[2025-08-22 17:47:14] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -53.030295+0.002344j
[2025-08-22 17:47:17] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -53.138680+0.001640j
[2025-08-22 17:47:20] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -53.043902-0.000099j
[2025-08-22 17:47:23] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -53.045534-0.000750j
[2025-08-22 17:47:26] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -53.142083-0.005568j
[2025-08-22 17:47:29] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -53.154946-0.001391j
[2025-08-22 17:47:31] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -53.193507+0.000628j
[2025-08-22 17:47:34] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -53.198385-0.003589j
[2025-08-22 17:47:37] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -53.167417-0.004935j
[2025-08-22 17:47:40] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -53.041432+0.002365j
[2025-08-22 17:47:43] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -53.102105+0.000774j
[2025-08-22 17:47:46] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -53.031935+0.003766j
[2025-08-22 17:47:49] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -53.047558+0.000900j
[2025-08-22 17:47:51] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -53.004421-0.001513j
[2025-08-22 17:47:54] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -52.966373+0.001865j
[2025-08-22 17:47:57] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -53.029380+0.004840j
[2025-08-22 17:48:00] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -53.011909-0.002889j
[2025-08-22 17:48:03] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -53.060403+0.002574j
[2025-08-22 17:48:06] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -53.077473-0.002208j
[2025-08-22 17:48:09] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -53.061441-0.004985j
[2025-08-22 17:48:11] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -53.060480-0.003424j
[2025-08-22 17:48:14] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -53.068397-0.006012j
[2025-08-22 17:48:17] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -52.991044-0.000480j
[2025-08-22 17:48:20] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -52.919385-0.005891j
[2025-08-22 17:48:23] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -52.994874-0.003786j
[2025-08-22 17:48:26] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -52.928563-0.001652j
[2025-08-22 17:48:29] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -53.001486-0.007034j
[2025-08-22 17:48:31] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -53.032604-0.001064j
[2025-08-22 17:48:34] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -52.955019-0.000146j
[2025-08-22 17:48:37] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -52.887532-0.002048j
[2025-08-22 17:48:40] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -52.910463+0.004988j
[2025-08-22 17:48:43] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -53.056343-0.001055j
[2025-08-22 17:48:46] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -53.157811-0.000982j
[2025-08-22 17:48:49] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -52.950084+0.016325j
[2025-08-22 17:48:51] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -52.901836+0.004112j
[2025-08-22 17:48:54] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -52.952440+0.000066j
[2025-08-22 17:48:57] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -53.041213-0.001189j
[2025-08-22 17:49:00] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -53.130528+0.002806j
[2025-08-22 17:49:03] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -52.966354+0.004862j
[2025-08-22 17:49:06] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -52.990637-0.000789j
[2025-08-22 17:49:09] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -53.047036-0.000078j
[2025-08-22 17:49:12] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -53.143025-0.000076j
[2025-08-22 17:49:14] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -53.043970-0.003803j
[2025-08-22 17:49:17] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -52.936939+0.000919j
[2025-08-22 17:49:20] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -53.084931+0.000510j
[2025-08-22 17:49:23] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -53.033818+0.001550j
[2025-08-22 17:49:26] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -52.987014-0.002602j
[2025-08-22 17:49:29] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -53.124867-0.001597j
[2025-08-22 17:49:32] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -53.006318-0.000126j
[2025-08-22 17:49:34] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -53.071188-0.003407j
[2025-08-22 17:49:37] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -52.995130+0.003010j
[2025-08-22 17:49:40] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -53.082139+0.002540j
[2025-08-22 17:49:40] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-22 17:49:43] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -52.984322+0.002884j
[2025-08-22 17:49:46] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -53.157821-0.000113j
[2025-08-22 17:49:49] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -53.054447-0.004198j
[2025-08-22 17:49:52] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -53.038304+0.004234j
[2025-08-22 17:49:54] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -53.063700-0.001492j
[2025-08-22 17:49:57] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -53.034975+0.002831j
[2025-08-22 17:50:00] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -53.080378-0.000159j
[2025-08-22 17:50:03] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -52.986166-0.003558j
[2025-08-22 17:50:06] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -53.026560-0.003770j
[2025-08-22 17:50:09] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -53.034567+0.002007j
[2025-08-22 17:50:12] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -53.069181+0.002445j
[2025-08-22 17:50:14] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -53.149888+0.001634j
[2025-08-22 17:50:17] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -53.214605+0.005711j
[2025-08-22 17:50:20] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -53.019811+0.001058j
[2025-08-22 17:50:23] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -52.956667+0.000702j
[2025-08-22 17:50:26] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -52.908765-0.001618j
[2025-08-22 17:50:29] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -53.057312+0.000775j
[2025-08-22 17:50:32] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -53.137034+0.004489j
[2025-08-22 17:50:34] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -52.911413-0.000234j
[2025-08-22 17:50:37] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -52.926145+0.000376j
[2025-08-22 17:50:40] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -53.003978-0.002836j
[2025-08-22 17:50:43] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -53.078677-0.002070j
[2025-08-22 17:50:46] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -52.994438+0.000334j
[2025-08-22 17:50:49] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -53.084651-0.000555j
[2025-08-22 17:50:52] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -53.105926-0.002472j
[2025-08-22 17:50:55] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -52.922523+0.004530j
[2025-08-22 17:50:58] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -53.034717+0.001699j
[2025-08-22 17:51:01] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -53.120574+0.003285j
[2025-08-22 17:51:03] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -53.136191+0.003476j
[2025-08-22 17:51:06] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -53.117714-0.004915j
[2025-08-22 17:51:09] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -53.077869-0.002949j
[2025-08-22 17:51:12] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -53.073619+0.004543j
[2025-08-22 17:51:15] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -53.013609+0.000245j
[2025-08-22 17:51:18] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -53.009175-0.000923j
[2025-08-22 17:51:21] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -53.018598-0.003221j
[2025-08-22 17:51:23] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -52.919581+0.001849j
[2025-08-22 17:51:26] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -53.055909+0.001585j
[2025-08-22 17:51:29] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -52.981705-0.001269j
[2025-08-22 17:51:32] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -53.085433+0.000125j
[2025-08-22 17:51:35] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -52.943594-0.002673j
[2025-08-22 17:51:38] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -52.867787+0.001985j
[2025-08-22 17:51:41] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -52.894812-0.004231j
[2025-08-22 17:51:43] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -53.015343+0.004550j
[2025-08-22 17:51:46] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -52.925168+0.008097j
[2025-08-22 17:51:49] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -52.901769+0.006056j
[2025-08-22 17:51:52] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -52.919386-0.000472j
[2025-08-22 17:51:55] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -52.895113+0.001496j
[2025-08-22 17:51:58] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -52.872963+0.004932j
[2025-08-22 17:52:01] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -53.010747-0.001574j
[2025-08-22 17:52:03] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -53.104453+0.002117j
[2025-08-22 17:52:03] RESTART #1 | Period: 300
[2025-08-22 17:52:06] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -53.017749+0.003499j
[2025-08-22 17:52:09] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -52.983045+0.004344j
[2025-08-22 17:52:12] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -52.963573+0.000302j
[2025-08-22 17:52:15] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -53.008825+0.002096j
[2025-08-22 17:52:18] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -53.127746-0.000740j
[2025-08-22 17:52:21] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -53.094225-0.000137j
[2025-08-22 17:52:23] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -53.159274+0.002907j
[2025-08-22 17:52:26] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -52.946910-0.004314j
[2025-08-22 17:52:29] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -53.102275+0.002365j
[2025-08-22 17:52:32] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -53.092175-0.001944j
[2025-08-22 17:52:35] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -53.094719+0.002970j
[2025-08-22 17:52:38] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -52.945840+0.001360j
[2025-08-22 17:52:41] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -53.023100+0.000330j
[2025-08-22 17:52:43] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -52.911068-0.000221j
[2025-08-22 17:52:46] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -53.152959-0.005192j
[2025-08-22 17:52:49] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -53.206898-0.000843j
[2025-08-22 17:52:52] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -53.192811-0.003929j
[2025-08-22 17:52:55] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -53.011325-0.008658j
[2025-08-22 17:52:58] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -53.128763+0.006109j
[2025-08-22 17:53:01] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -53.174405+0.000487j
[2025-08-22 17:53:03] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -53.126067-0.002483j
[2025-08-22 17:53:06] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -52.892397-0.003929j
[2025-08-22 17:53:09] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -52.992905+0.002463j
[2025-08-22 17:53:12] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -53.096662+0.001404j
[2025-08-22 17:53:15] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -53.001101-0.005272j
[2025-08-22 17:53:18] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -53.027808+0.006908j
[2025-08-22 17:53:21] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -52.991622-0.001798j
[2025-08-22 17:53:23] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -52.946632+0.001942j
[2025-08-22 17:53:26] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -52.982546-0.002666j
[2025-08-22 17:53:29] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -52.963272+0.000701j
[2025-08-22 17:53:32] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -52.985614+0.004872j
[2025-08-22 17:53:35] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -53.126216-0.002227j
[2025-08-22 17:53:38] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -53.205103-0.000741j
[2025-08-22 17:53:41] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -53.036339+0.004395j
[2025-08-22 17:53:43] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -53.004134-0.000854j
[2025-08-22 17:53:46] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -52.910304+0.001624j
[2025-08-22 17:53:49] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -53.025724+0.004354j
[2025-08-22 17:53:52] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -52.878369+0.009604j
[2025-08-22 17:53:55] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -52.958881+0.000931j
[2025-08-22 17:53:58] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -53.027959+0.007272j
[2025-08-22 17:54:01] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -52.987833+0.001942j
[2025-08-22 17:54:03] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -53.005596-0.003329j
[2025-08-22 17:54:06] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -53.032896+0.004503j
[2025-08-22 17:54:09] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -53.041820+0.003101j
[2025-08-22 17:54:12] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -53.100877+0.000976j
[2025-08-22 17:54:15] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -53.152467+0.000465j
[2025-08-22 17:54:18] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -53.230042-0.005093j
[2025-08-22 17:54:21] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -53.120387-0.001705j
[2025-08-22 17:54:23] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -53.258773+0.002275j
[2025-08-22 17:54:26] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -53.203695+0.000180j
[2025-08-22 17:54:26] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-22 17:54:29] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -53.093631-0.000741j
[2025-08-22 17:54:32] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -53.126797+0.000169j
[2025-08-22 17:54:35] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -53.129252-0.009182j
[2025-08-22 17:54:38] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -52.980534-0.003698j
[2025-08-22 17:54:41] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -53.238735-0.003304j
[2025-08-22 17:54:43] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -52.997840-0.000326j
[2025-08-22 17:54:46] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -52.889840+0.000882j
[2025-08-22 17:54:49] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -52.996567-0.002544j
[2025-08-22 17:54:52] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -52.929359+0.003840j
[2025-08-22 17:54:55] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -53.009891-0.000876j
[2025-08-22 17:54:58] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -52.989730-0.008331j
[2025-08-22 17:55:01] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -53.233782-0.004614j
[2025-08-22 17:55:03] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -53.015701-0.001156j
[2025-08-22 17:55:06] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -52.978026+0.005052j
[2025-08-22 17:55:09] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -53.076844+0.003614j
[2025-08-22 17:55:12] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -52.907824-0.004823j
[2025-08-22 17:55:15] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -52.922689-0.005907j
[2025-08-22 17:55:18] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -52.903875+0.003218j
[2025-08-22 17:55:20] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -53.071450-0.002258j
[2025-08-22 17:55:23] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -52.979134-0.000988j
[2025-08-22 17:55:26] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -53.095699-0.003465j
[2025-08-22 17:55:29] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -53.064272+0.001014j
[2025-08-22 17:55:32] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -53.074805+0.001761j
[2025-08-22 17:55:35] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -52.968798-0.001056j
[2025-08-22 17:55:38] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -53.119817-0.000285j
[2025-08-22 17:55:40] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -53.217688+0.000141j
[2025-08-22 17:55:43] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -53.050704+0.003317j
[2025-08-22 17:55:46] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -52.994052+0.001291j
[2025-08-22 17:55:49] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -53.076277-0.003282j
[2025-08-22 17:55:52] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -53.070716-0.001789j
[2025-08-22 17:55:55] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -53.091644+0.003523j
[2025-08-22 17:55:58] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -53.144126-0.003253j
[2025-08-22 17:56:00] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -53.060547+0.000123j
[2025-08-22 17:56:03] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -53.078802-0.001939j
[2025-08-22 17:56:06] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -53.235687-0.004582j
[2025-08-22 17:56:09] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -53.032400-0.000967j
[2025-08-22 17:56:12] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -53.067348+0.002361j
[2025-08-22 17:56:15] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -53.082880+0.003317j
[2025-08-22 17:56:18] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -53.105284-0.002365j
[2025-08-22 17:56:20] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -53.201536-0.002793j
[2025-08-22 17:56:23] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -53.015548+0.004409j
[2025-08-22 17:56:26] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -53.133921-0.002452j
[2025-08-22 17:56:29] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -53.162890-0.002782j
[2025-08-22 17:56:32] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -53.110625-0.000644j
[2025-08-22 17:56:35] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -52.978605+0.002538j
[2025-08-22 17:56:38] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -53.027380-0.002590j
[2025-08-22 17:56:40] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -52.967477+0.001930j
[2025-08-22 17:56:43] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -52.931347-0.000235j
[2025-08-22 17:56:46] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -52.895570-0.005125j
[2025-08-22 17:56:49] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -52.942173+0.002959j
[2025-08-22 17:56:52] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -52.928291+0.002978j
[2025-08-22 17:56:55] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -52.944182+0.005134j
[2025-08-22 17:56:57] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -52.939255-0.000136j
[2025-08-22 17:57:00] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -52.979290+0.000458j
[2025-08-22 17:57:03] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -52.951106+0.003450j
[2025-08-22 17:57:06] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -53.032910+0.005029j
[2025-08-22 17:57:09] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -53.086873-0.001879j
[2025-08-22 17:57:12] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -53.041447-0.002056j
[2025-08-22 17:57:15] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -52.872513+0.001892j
[2025-08-22 17:57:17] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -52.915737+0.000123j
[2025-08-22 17:57:20] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -52.916259-0.006036j
[2025-08-22 17:57:23] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -53.005888-0.005792j
[2025-08-22 17:57:26] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -53.099872+0.003666j
[2025-08-22 17:57:29] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -52.965124-0.001500j
[2025-08-22 17:57:32] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -52.914299-0.000830j
[2025-08-22 17:57:35] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -53.042705-0.002915j
[2025-08-22 17:57:37] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -52.925706+0.003927j
[2025-08-22 17:57:40] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -52.832318-0.006646j
[2025-08-22 17:57:43] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -52.961546-0.003854j
[2025-08-22 17:57:46] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -53.025452+0.002480j
[2025-08-22 17:57:49] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -53.177367+0.006177j
[2025-08-22 17:57:52] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -53.056636-0.002611j
[2025-08-22 17:57:55] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -53.102660+0.003149j
[2025-08-22 17:57:57] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -53.128931-0.002886j
[2025-08-22 17:58:00] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -53.014807+0.001545j
[2025-08-22 17:58:03] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -53.160297+0.001150j
[2025-08-22 17:58:06] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -53.153410-0.001562j
[2025-08-22 17:58:09] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -53.082997+0.001840j
[2025-08-22 17:58:12] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -53.027158-0.000501j
[2025-08-22 17:58:15] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -52.945749+0.002524j
[2025-08-22 17:58:17] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -53.120679-0.000436j
[2025-08-22 17:58:20] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -53.178359+0.001793j
[2025-08-22 17:58:23] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -53.214652-0.000034j
[2025-08-22 17:58:26] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -53.085042-0.001963j
[2025-08-22 17:58:29] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -53.153439+0.000009j
[2025-08-22 17:58:32] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -53.032675-0.000154j
[2025-08-22 17:58:35] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -53.111365-0.004765j
[2025-08-22 17:58:37] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -53.048150+0.000471j
[2025-08-22 17:58:40] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -52.872401+0.005193j
[2025-08-22 17:58:43] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -53.036511-0.006850j
[2025-08-22 17:58:46] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -53.059066-0.005275j
[2025-08-22 17:58:49] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -53.132998+0.003037j
[2025-08-22 17:58:52] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -53.030303-0.002581j
[2025-08-22 17:58:55] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -52.976195+0.006426j
[2025-08-22 17:58:57] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -52.978846+0.003701j
[2025-08-22 17:59:00] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -52.984126+0.002946j
[2025-08-22 17:59:03] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -52.967667-0.003030j
[2025-08-22 17:59:06] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -53.054128-0.004050j
[2025-08-22 17:59:09] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -52.963481+0.002862j
[2025-08-22 17:59:12] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -53.086389-0.000726j
[2025-08-22 17:59:12] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-22 17:59:15] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -53.236730-0.000739j
[2025-08-22 17:59:17] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -53.087665+0.001936j
[2025-08-22 17:59:20] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -53.038490+0.002295j
[2025-08-22 17:59:23] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -53.048490-0.000025j
[2025-08-22 17:59:26] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -53.116334-0.001066j
[2025-08-22 17:59:29] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -53.110475+0.002012j
[2025-08-22 17:59:32] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -52.880257+0.001479j
[2025-08-22 17:59:35] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -52.981517+0.002358j
[2025-08-22 17:59:37] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -52.888173+0.001592j
[2025-08-22 17:59:40] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -53.011766-0.006740j
[2025-08-22 17:59:43] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -52.935765+0.001076j
[2025-08-22 17:59:46] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -52.883031+0.005018j
[2025-08-22 17:59:49] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -52.940730+0.004773j
[2025-08-22 17:59:52] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -52.985849-0.002877j
[2025-08-22 17:59:55] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -53.050686+0.007306j
[2025-08-22 17:59:57] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -53.046721+0.002747j
[2025-08-22 18:00:00] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -53.016300-0.001238j
[2025-08-22 18:00:03] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -52.990817-0.001118j
[2025-08-22 18:00:06] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -52.980423-0.001623j
[2025-08-22 18:00:09] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -52.988379+0.000997j
[2025-08-22 18:00:12] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -52.894261+0.000081j
[2025-08-22 18:00:14] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -52.962461-0.005008j
[2025-08-22 18:00:17] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -53.029179-0.002046j
[2025-08-22 18:00:20] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -52.943160+0.001376j
[2025-08-22 18:00:23] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -52.989678+0.001573j
[2025-08-22 18:00:26] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -52.966200+0.000868j
[2025-08-22 18:00:29] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -53.041665+0.001703j
[2025-08-22 18:00:32] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -52.996910+0.004412j
[2025-08-22 18:00:34] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -53.131955+0.001871j
[2025-08-22 18:00:37] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -53.044093+0.000232j
[2025-08-22 18:00:40] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -53.179165+0.004131j
[2025-08-22 18:00:43] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -53.157933+0.002226j
[2025-08-22 18:00:46] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -53.072315+0.001099j
[2025-08-22 18:00:49] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -52.997548+0.000927j
[2025-08-22 18:00:52] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -53.216373+0.000811j
[2025-08-22 18:00:54] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -53.175168+0.003098j
[2025-08-22 18:00:57] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -53.019834+0.001053j
[2025-08-22 18:01:00] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -52.975269-0.003061j
[2025-08-22 18:01:03] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -53.141879-0.001047j
[2025-08-22 18:01:06] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -53.099302+0.003040j
[2025-08-22 18:01:09] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -52.989638+0.007521j
[2025-08-22 18:01:12] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -53.021170+0.000613j
[2025-08-22 18:01:14] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -53.047956+0.001251j
[2025-08-22 18:01:17] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -52.939339-0.000793j
[2025-08-22 18:01:20] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -52.996018-0.001256j
[2025-08-22 18:01:23] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -52.862745+0.000587j
[2025-08-22 18:01:26] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -52.963993+0.001146j
[2025-08-22 18:01:29] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -53.031883-0.005194j
[2025-08-22 18:01:32] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -52.985324-0.004583j
[2025-08-22 18:01:34] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -53.148069+0.002644j
[2025-08-22 18:01:37] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -53.176836-0.001566j
[2025-08-22 18:01:40] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -53.031099-0.003107j
[2025-08-22 18:01:43] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -53.012406+0.001742j
[2025-08-22 18:01:46] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -53.010350-0.004188j
[2025-08-22 18:01:49] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -53.105742+0.006728j
[2025-08-22 18:01:52] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -53.037547+0.000627j
[2025-08-22 18:01:54] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -52.936808-0.002933j
[2025-08-22 18:01:57] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -52.900256+0.000075j
[2025-08-22 18:02:00] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -52.973415+0.002095j
[2025-08-22 18:02:03] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -53.010869-0.001473j
[2025-08-22 18:02:06] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -52.955887-0.000061j
[2025-08-22 18:02:09] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -52.888635+0.002054j
[2025-08-22 18:02:11] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -52.930399+0.005159j
[2025-08-22 18:02:14] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -52.942696+0.001991j
[2025-08-22 18:02:17] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -52.997514-0.001926j
[2025-08-22 18:02:20] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -53.059897-0.002433j
[2025-08-22 18:02:23] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -53.039267-0.000912j
[2025-08-22 18:02:26] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -53.027636-0.002259j
[2025-08-22 18:02:29] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -53.018987+0.000010j
[2025-08-22 18:02:31] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -52.949676-0.000757j
[2025-08-22 18:02:34] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -53.002110+0.000870j
[2025-08-22 18:02:37] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -53.081071-0.001785j
[2025-08-22 18:02:40] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -53.081084+0.000016j
[2025-08-22 18:02:43] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -53.012637+0.002515j
[2025-08-22 18:02:46] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -52.987751+0.001527j
[2025-08-22 18:02:49] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -53.109976+0.001183j
[2025-08-22 18:02:51] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -53.032652-0.000129j
[2025-08-22 18:02:54] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -53.165589+0.007451j
[2025-08-22 18:02:57] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -53.121338-0.002030j
[2025-08-22 18:03:00] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -52.949194+0.003686j
[2025-08-22 18:03:03] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -53.116441+0.000176j
[2025-08-22 18:03:06] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -53.110409-0.003935j
[2025-08-22 18:03:09] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -53.113749+0.003484j
[2025-08-22 18:03:11] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -53.023079+0.001302j
[2025-08-22 18:03:14] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -53.076345+0.002572j
[2025-08-22 18:03:17] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -53.093292+0.000083j
[2025-08-22 18:03:20] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -53.167108+0.001624j
[2025-08-22 18:03:23] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -53.071451+0.001774j
[2025-08-22 18:03:26] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -53.169122+0.003996j
[2025-08-22 18:03:29] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -53.000488+0.002711j
[2025-08-22 18:03:31] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -53.009941+0.000639j
[2025-08-22 18:03:34] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -53.075211-0.003017j
[2025-08-22 18:03:37] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -52.991234-0.005821j
[2025-08-22 18:03:40] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -53.031781-0.003066j
[2025-08-22 18:03:43] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -53.014641+0.004273j
[2025-08-22 18:03:46] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -53.127697-0.003190j
[2025-08-22 18:03:49] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -53.047463-0.004778j
[2025-08-22 18:03:51] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -52.946369+0.000166j
[2025-08-22 18:03:54] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -52.942732-0.003226j
[2025-08-22 18:03:57] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -52.934085-0.001321j
[2025-08-22 18:03:57] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-22 18:04:00] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -53.073162+0.001833j
[2025-08-22 18:04:03] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -53.092290-0.000428j
[2025-08-22 18:04:06] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -53.031937+0.002608j
[2025-08-22 18:04:09] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -52.994263+0.002184j
[2025-08-22 18:04:11] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -52.879791-0.004395j
[2025-08-22 18:04:14] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -53.163822-0.002994j
[2025-08-22 18:04:17] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -53.009108+0.000453j
[2025-08-22 18:04:20] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -53.080302+0.002090j
[2025-08-22 18:04:23] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -52.980844-0.000111j
[2025-08-22 18:04:26] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -53.023894-0.002252j
[2025-08-22 18:04:29] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -53.094880+0.000635j
[2025-08-22 18:04:31] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -53.059364-0.001258j
[2025-08-22 18:04:34] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -53.022496-0.000820j
[2025-08-22 18:04:37] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -53.036170+0.008544j
[2025-08-22 18:04:40] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -53.129012+0.002888j
[2025-08-22 18:04:43] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -53.038893-0.003490j
[2025-08-22 18:04:46] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -53.065573+0.000811j
[2025-08-22 18:04:49] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -53.138766-0.001087j
[2025-08-22 18:04:51] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -52.923621-0.000465j
[2025-08-22 18:04:54] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -53.019535-0.000415j
[2025-08-22 18:04:57] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -53.061170-0.000817j
[2025-08-22 18:05:00] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -53.120166+0.004017j
[2025-08-22 18:05:03] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -53.005407+0.002342j
[2025-08-22 18:05:06] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -53.084170-0.003115j
[2025-08-22 18:05:08] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -53.115013+0.002479j
[2025-08-22 18:05:11] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -53.151379-0.003377j
[2025-08-22 18:05:14] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -53.272566+0.001979j
[2025-08-22 18:05:17] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -53.201257+0.001192j
[2025-08-22 18:05:20] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -53.161234-0.003919j
[2025-08-22 18:05:23] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -53.202168+0.001130j
[2025-08-22 18:05:26] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -53.150366+0.002486j
[2025-08-22 18:05:28] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -53.033771+0.002032j
[2025-08-22 18:05:31] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -52.927456-0.002782j
[2025-08-22 18:05:34] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -52.960755-0.001220j
[2025-08-22 18:05:37] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -53.045006-0.000616j
[2025-08-22 18:05:40] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -52.974442-0.001819j
[2025-08-22 18:05:43] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -53.098281-0.000069j
[2025-08-22 18:05:46] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -53.035965-0.004824j
[2025-08-22 18:05:48] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -52.991957-0.002774j
[2025-08-22 18:05:51] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -52.889125+0.005635j
[2025-08-22 18:05:54] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -53.097361+0.002332j
[2025-08-22 18:05:57] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -53.029796+0.002189j
[2025-08-22 18:06:00] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -53.030030-0.000120j
[2025-08-22 18:06:03] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -53.136160+0.006155j
[2025-08-22 18:06:06] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -52.990390-0.000691j
[2025-08-22 18:06:08] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -52.958931+0.000205j
[2025-08-22 18:06:11] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -53.156311-0.004897j
[2025-08-22 18:06:14] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -53.300882+0.000510j
[2025-08-22 18:06:17] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -53.270394-0.001041j
[2025-08-22 18:06:20] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -53.181945+0.000507j
[2025-08-22 18:06:20] RESTART #2 | Period: 600
[2025-08-22 18:06:23] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -53.029843+0.002424j
[2025-08-22 18:06:26] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -53.032796-0.003039j
[2025-08-22 18:06:28] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -53.114544-0.003404j
[2025-08-22 18:06:31] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -53.049467-0.002494j
[2025-08-22 18:06:34] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -52.999358+0.001900j
[2025-08-22 18:06:37] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -53.087769-0.000657j
[2025-08-22 18:06:40] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -53.158431-0.000409j
[2025-08-22 18:06:43] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -53.139028+0.003467j
[2025-08-22 18:06:46] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -53.187815-0.004370j
[2025-08-22 18:06:48] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -53.129141-0.006719j
[2025-08-22 18:06:51] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -53.308314+0.001674j
[2025-08-22 18:06:54] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -53.112914-0.000507j
[2025-08-22 18:06:57] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -53.121273-0.002102j
[2025-08-22 18:07:00] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -53.149688-0.003447j
[2025-08-22 18:07:03] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -53.098527-0.001473j
[2025-08-22 18:07:06] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -53.044604-0.004005j
[2025-08-22 18:07:08] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -53.149708-0.003959j
[2025-08-22 18:07:11] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -53.012215-0.000720j
[2025-08-22 18:07:14] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -53.104915-0.002536j
[2025-08-22 18:07:17] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -53.034649-0.001959j
[2025-08-22 18:07:20] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -53.106363-0.006583j
[2025-08-22 18:07:23] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -53.101047-0.001577j
[2025-08-22 18:07:25] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -53.085448-0.005904j
[2025-08-22 18:07:28] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -53.036598-0.002501j
[2025-08-22 18:07:31] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -53.050269+0.002781j
[2025-08-22 18:07:34] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -53.018746+0.000076j
[2025-08-22 18:07:37] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -52.970426+0.010104j
[2025-08-22 18:07:40] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -52.961036+0.002276j
[2025-08-22 18:07:43] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -52.961443+0.001595j
[2025-08-22 18:07:45] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -53.083031-0.001948j
[2025-08-22 18:07:48] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -52.902116+0.003988j
[2025-08-22 18:07:51] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -52.998217-0.001656j
[2025-08-22 18:07:54] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -52.851324-0.001698j
[2025-08-22 18:07:57] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -52.951139-0.002406j
[2025-08-22 18:08:00] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -53.058737+0.003585j
[2025-08-22 18:08:03] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -52.872000+0.000143j
[2025-08-22 18:08:05] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -52.893993-0.003672j
[2025-08-22 18:08:08] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -52.940336-0.001737j
[2025-08-22 18:08:11] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -53.062038+0.004823j
[2025-08-22 18:08:14] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -53.081460+0.000670j
[2025-08-22 18:08:17] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -53.101433-0.000823j
[2025-08-22 18:08:20] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -53.036175+0.007452j
[2025-08-22 18:08:23] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -53.080797+0.003168j
[2025-08-22 18:08:25] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -53.128491-0.000539j
[2025-08-22 18:08:28] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -53.129002+0.004024j
[2025-08-22 18:08:31] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -53.082534+0.002750j
[2025-08-22 18:08:34] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -52.999805+0.004999j
[2025-08-22 18:08:37] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -52.940869+0.000432j
[2025-08-22 18:08:40] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -52.875455-0.000866j
[2025-08-22 18:08:43] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -53.006710+0.003623j
[2025-08-22 18:08:43] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-22 18:08:45] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -52.979191+0.001087j
[2025-08-22 18:08:48] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -52.919740+0.003208j
[2025-08-22 18:08:51] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -53.005771+0.000923j
[2025-08-22 18:08:54] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -52.995456+0.002298j
[2025-08-22 18:08:57] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -53.024544-0.002404j
[2025-08-22 18:09:00] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -53.050496-0.004908j
[2025-08-22 18:09:03] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -53.135032-0.003310j
[2025-08-22 18:09:05] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -53.098720-0.001796j
[2025-08-22 18:09:08] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -53.023408-0.001674j
[2025-08-22 18:09:11] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -52.940449+0.004601j
[2025-08-22 18:09:14] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -53.038413-0.001622j
[2025-08-22 18:09:17] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -53.041955-0.003244j
[2025-08-22 18:09:20] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -52.967781-0.000495j
[2025-08-22 18:09:23] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -53.041476+0.002019j
[2025-08-22 18:09:25] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -53.017387-0.004263j
[2025-08-22 18:09:28] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -53.036826+0.000073j
[2025-08-22 18:09:31] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -53.008193+0.006311j
[2025-08-22 18:09:34] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -53.035031+0.000019j
[2025-08-22 18:09:37] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -53.037398+0.001170j
[2025-08-22 18:09:40] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -52.981360-0.001427j
[2025-08-22 18:09:42] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -53.052339-0.006875j
[2025-08-22 18:09:45] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -53.015184+0.001694j
[2025-08-22 18:09:48] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -53.062895-0.000179j
[2025-08-22 18:09:51] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -53.144240+0.000197j
[2025-08-22 18:09:54] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -53.249947-0.001599j
[2025-08-22 18:09:57] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -53.178573-0.000187j
[2025-08-22 18:10:00] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -53.138881+0.001700j
[2025-08-22 18:10:02] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -53.229605-0.001029j
[2025-08-22 18:10:05] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -53.238006-0.004725j
[2025-08-22 18:10:08] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -53.173047+0.001825j
[2025-08-22 18:10:11] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -53.149886-0.000475j
[2025-08-22 18:10:14] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -53.003312-0.004198j
[2025-08-22 18:10:17] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -53.041699+0.000920j
[2025-08-22 18:10:20] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -53.132597+0.003224j
[2025-08-22 18:10:22] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -52.907378+0.003265j
[2025-08-22 18:10:25] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -53.024226-0.001023j
[2025-08-22 18:10:28] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -52.966110+0.000523j
[2025-08-22 18:10:31] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -52.976987+0.007530j
[2025-08-22 18:10:34] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -53.133652-0.000205j
[2025-08-22 18:10:37] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -53.027108+0.004983j
[2025-08-22 18:10:40] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -53.052216-0.005574j
[2025-08-22 18:10:42] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -52.925436-0.005250j
[2025-08-22 18:10:45] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -52.975495-0.001520j
[2025-08-22 18:10:48] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -52.917165+0.003190j
[2025-08-22 18:10:51] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -53.029839-0.000745j
[2025-08-22 18:10:54] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -53.007491+0.000034j
[2025-08-22 18:10:57] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -52.924154+0.001103j
[2025-08-22 18:11:00] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -52.999148+0.003190j
[2025-08-22 18:11:02] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -53.031354+0.004444j
[2025-08-22 18:11:05] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -53.019029-0.002762j
[2025-08-22 18:11:08] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -53.109189+0.000239j
[2025-08-22 18:11:11] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -52.935392-0.000278j
[2025-08-22 18:11:14] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -53.069960-0.003591j
[2025-08-22 18:11:17] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -52.964161+0.003141j
[2025-08-22 18:11:20] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -52.997185+0.002624j
[2025-08-22 18:11:22] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -53.042878-0.002859j
[2025-08-22 18:11:25] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -53.017041+0.000622j
[2025-08-22 18:11:28] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -52.946483-0.000171j
[2025-08-22 18:11:31] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -53.151027+0.004095j
[2025-08-22 18:11:34] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -53.087334-0.002558j
[2025-08-22 18:11:37] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -52.934527+0.002252j
[2025-08-22 18:11:40] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -53.050336-0.001855j
[2025-08-22 18:11:42] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -53.071595-0.003190j
[2025-08-22 18:11:45] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -53.143696-0.008363j
[2025-08-22 18:11:48] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -53.116246-0.003656j
[2025-08-22 18:11:51] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -53.049383-0.001411j
[2025-08-22 18:11:54] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -52.992335+0.002954j
[2025-08-22 18:11:57] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -53.057969-0.000759j
[2025-08-22 18:12:00] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -53.041865+0.001335j
[2025-08-22 18:12:02] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -53.097119+0.002797j
[2025-08-22 18:12:05] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -53.095582-0.004946j
[2025-08-22 18:12:08] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -53.066885+0.007501j
[2025-08-22 18:12:11] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -53.091461-0.001126j
[2025-08-22 18:12:14] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -52.909357+0.003802j
[2025-08-22 18:12:17] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -53.118705+0.001583j
[2025-08-22 18:12:20] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -53.129909+0.001229j
[2025-08-22 18:12:22] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -53.110432-0.001394j
[2025-08-22 18:12:25] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -53.138696+0.002526j
[2025-08-22 18:12:28] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -53.016682+0.001730j
[2025-08-22 18:12:31] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -52.978644-0.003694j
[2025-08-22 18:12:34] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -53.273128+0.001921j
[2025-08-22 18:12:37] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -53.209134+0.003205j
[2025-08-22 18:12:39] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -53.093299+0.000448j
[2025-08-22 18:12:42] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -53.178303-0.000741j
[2025-08-22 18:12:45] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -52.959638-0.000442j
[2025-08-22 18:12:48] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -52.998884-0.003213j
[2025-08-22 18:12:51] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -53.106431-0.002559j
[2025-08-22 18:12:54] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -52.969659+0.001148j
[2025-08-22 18:12:57] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -53.022309+0.005410j
[2025-08-22 18:12:59] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -52.963904-0.000361j
[2025-08-22 18:13:02] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -53.100737+0.001898j
[2025-08-22 18:13:05] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -52.962996+0.003256j
[2025-08-22 18:13:08] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -53.050325-0.001797j
[2025-08-22 18:13:11] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -53.083416+0.003210j
[2025-08-22 18:13:14] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -53.062772-0.002866j
[2025-08-22 18:13:17] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -53.058892-0.004597j
[2025-08-22 18:13:19] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -53.028085+0.002231j
[2025-08-22 18:13:22] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -53.186782-0.000123j
[2025-08-22 18:13:25] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -53.152178+0.004308j
[2025-08-22 18:13:28] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -53.045966-0.006046j
[2025-08-22 18:13:28] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-08-22 18:13:31] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -52.994885+0.000844j
[2025-08-22 18:13:34] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -52.986710-0.004301j
[2025-08-22 18:13:37] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -52.965217+0.001651j
[2025-08-22 18:13:39] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -52.997008+0.002837j
[2025-08-22 18:13:42] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -53.053555+0.001529j
[2025-08-22 18:13:45] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -52.991695-0.001548j
[2025-08-22 18:13:48] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -52.977753-0.001447j
[2025-08-22 18:13:51] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -52.926350+0.002022j
[2025-08-22 18:13:54] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -52.969765+0.001464j
[2025-08-22 18:13:57] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -52.978490-0.000732j
[2025-08-22 18:13:59] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -52.864144+0.000583j
[2025-08-22 18:14:02] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -52.928798-0.003538j
[2025-08-22 18:14:05] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -52.805615+0.003575j
[2025-08-22 18:14:08] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -52.813372-0.001251j
[2025-08-22 18:14:11] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -52.946141-0.010379j
[2025-08-22 18:14:14] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -53.044882+0.000218j
[2025-08-22 18:14:17] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -53.065747-0.006287j
[2025-08-22 18:14:19] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -53.100986+0.000757j
[2025-08-22 18:14:22] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -53.135761-0.003455j
[2025-08-22 18:14:25] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -53.161313-0.003332j
[2025-08-22 18:14:28] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -53.034715+0.006144j
[2025-08-22 18:14:31] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -53.123638-0.002487j
[2025-08-22 18:14:34] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -53.103009+0.003456j
[2025-08-22 18:14:37] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -53.180274+0.003311j
[2025-08-22 18:14:39] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -53.141449+0.002607j
[2025-08-22 18:14:42] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -53.029153-0.001907j
[2025-08-22 18:14:45] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -53.087485-0.006107j
[2025-08-22 18:14:48] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -53.048895-0.005171j
[2025-08-22 18:14:51] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -53.078952-0.000296j
[2025-08-22 18:14:54] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -52.993796+0.002378j
[2025-08-22 18:14:57] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -52.976136-0.004472j
[2025-08-22 18:14:59] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -53.112260+0.003234j
[2025-08-22 18:15:02] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -52.972294-0.001448j
[2025-08-22 18:15:05] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -52.993206-0.002418j
[2025-08-22 18:15:08] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -52.996896-0.001430j
[2025-08-22 18:15:11] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -53.031199-0.002164j
[2025-08-22 18:15:14] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -53.039836-0.004304j
[2025-08-22 18:15:16] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -53.099470-0.000998j
[2025-08-22 18:15:19] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -53.035549+0.000126j
[2025-08-22 18:15:22] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -53.015253+0.001659j
[2025-08-22 18:15:25] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -53.063905-0.002211j
[2025-08-22 18:15:28] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -53.027055+0.000742j
[2025-08-22 18:15:31] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -53.038745+0.004457j
[2025-08-22 18:15:34] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -53.073503+0.000599j
[2025-08-22 18:15:36] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -52.860421-0.003706j
[2025-08-22 18:15:39] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -53.024128+0.000445j
[2025-08-22 18:15:42] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -53.034950+0.005989j
[2025-08-22 18:15:45] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -53.116214-0.000807j
[2025-08-22 18:15:48] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -52.946049-0.003641j
[2025-08-22 18:15:51] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -53.096070+0.003056j
[2025-08-22 18:15:54] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -53.002699-0.003024j
[2025-08-22 18:15:56] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -53.135035+0.004691j
[2025-08-22 18:15:59] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -53.007658-0.002662j
[2025-08-22 18:16:02] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -53.055638-0.001656j
[2025-08-22 18:16:05] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -53.018672-0.000299j
[2025-08-22 18:16:08] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -53.150996-0.001464j
[2025-08-22 18:16:11] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -53.100559-0.000431j
[2025-08-22 18:16:14] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -52.990285+0.000435j
[2025-08-22 18:16:16] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -53.079292+0.001235j
[2025-08-22 18:16:19] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -53.124601+0.001388j
[2025-08-22 18:16:22] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -53.111812+0.003621j
[2025-08-22 18:16:25] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -53.130478+0.000110j
[2025-08-22 18:16:28] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -53.071555-0.002305j
[2025-08-22 18:16:31] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -52.966533+0.004684j
[2025-08-22 18:16:34] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -53.033629-0.001762j
[2025-08-22 18:16:36] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -53.000896+0.003309j
[2025-08-22 18:16:39] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -53.060230-0.002750j
[2025-08-22 18:16:42] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -53.060283-0.003698j
[2025-08-22 18:16:45] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -53.053450+0.005048j
[2025-08-22 18:16:48] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -53.114955-0.003310j
[2025-08-22 18:16:51] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -53.089043-0.001873j
[2025-08-22 18:16:54] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -53.025293+0.007083j
[2025-08-22 18:16:56] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -53.036716+0.002622j
[2025-08-22 18:16:59] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -53.070157+0.000429j
[2025-08-22 18:17:02] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -52.978592-0.004213j
[2025-08-22 18:17:05] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -52.968367+0.001337j
[2025-08-22 18:17:08] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -53.271947+0.005316j
[2025-08-22 18:17:11] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -53.162366+0.003262j
[2025-08-22 18:17:13] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -52.978387+0.001477j
[2025-08-22 18:17:16] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -52.996632-0.001492j
[2025-08-22 18:17:19] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -53.049392+0.002745j
[2025-08-22 18:17:22] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -53.041011+0.002105j
[2025-08-22 18:17:25] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -53.162224+0.005197j
[2025-08-22 18:17:28] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -53.083162-0.000538j
[2025-08-22 18:17:31] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -53.087561+0.000259j
[2025-08-22 18:17:33] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -53.066416+0.003832j
[2025-08-22 18:17:36] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -52.987826+0.000537j
[2025-08-22 18:17:39] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -53.111340+0.000846j
[2025-08-22 18:17:42] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -53.161249+0.001394j
[2025-08-22 18:17:45] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -53.146806+0.003261j
[2025-08-22 18:17:48] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -53.011712+0.000649j
[2025-08-22 18:17:51] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -53.043574+0.002011j
[2025-08-22 18:17:53] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -52.984183-0.006818j
[2025-08-22 18:17:56] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -53.174876-0.000102j
[2025-08-22 18:17:59] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -53.177072+0.004538j
[2025-08-22 18:18:02] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -53.157082-0.002832j
[2025-08-22 18:18:05] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -53.030411-0.000490j
[2025-08-22 18:18:08] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -53.082447-0.001159j
[2025-08-22 18:18:11] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -53.094103-0.004910j
[2025-08-22 18:18:13] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -52.986439+0.001432j
[2025-08-22 18:18:13] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-08-22 18:18:16] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -52.952324-0.000862j
[2025-08-22 18:18:19] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -52.985578-0.002104j
[2025-08-22 18:18:22] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -52.990304+0.001962j
[2025-08-22 18:18:25] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -52.999768-0.002247j
[2025-08-22 18:18:28] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -53.072863+0.001377j
[2025-08-22 18:18:31] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -53.085046-0.000237j
[2025-08-22 18:18:33] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -52.998045+0.004013j
[2025-08-22 18:18:36] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -52.981784+0.002008j
[2025-08-22 18:18:39] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -52.995399+0.000197j
[2025-08-22 18:18:42] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -53.028466+0.000742j
[2025-08-22 18:18:45] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -52.976787+0.000223j
[2025-08-22 18:18:48] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -52.946083+0.000556j
[2025-08-22 18:18:51] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -53.069062+0.004229j
[2025-08-22 18:18:53] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -53.178235+0.000283j
[2025-08-22 18:18:56] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -53.030019-0.003897j
[2025-08-22 18:18:59] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -53.019286+0.001062j
[2025-08-22 18:19:02] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -53.103376-0.003581j
[2025-08-22 18:19:05] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -53.062204+0.002834j
[2025-08-22 18:19:08] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -53.115430-0.000027j
[2025-08-22 18:19:11] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -53.039742-0.002965j
[2025-08-22 18:19:13] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -53.073477-0.001563j
[2025-08-22 18:19:16] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -53.040399+0.004010j
[2025-08-22 18:19:19] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -53.038283+0.000638j
[2025-08-22 18:19:22] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -52.984053-0.001927j
[2025-08-22 18:19:25] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -53.004930-0.002952j
[2025-08-22 18:19:28] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -53.033497-0.000700j
[2025-08-22 18:19:30] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -52.968223+0.001973j
[2025-08-22 18:19:33] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -52.935000+0.001113j
[2025-08-22 18:19:36] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -53.020133-0.005727j
[2025-08-22 18:19:39] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -53.077049+0.001659j
[2025-08-22 18:19:42] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -53.034225-0.001202j
[2025-08-22 18:19:45] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -53.053427-0.009958j
[2025-08-22 18:19:48] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -53.085002-0.001941j
[2025-08-22 18:19:50] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -53.023945+0.000620j
[2025-08-22 18:19:53] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -53.002256+0.003747j
[2025-08-22 18:19:56] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -53.075956+0.002950j
[2025-08-22 18:19:59] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -53.081241-0.003062j
[2025-08-22 18:20:02] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -53.035888+0.000811j
[2025-08-22 18:20:05] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -52.978263+0.001353j
[2025-08-22 18:20:08] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -53.082538+0.005297j
[2025-08-22 18:20:10] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -53.049217-0.003669j
[2025-08-22 18:20:13] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -53.142115+0.001999j
[2025-08-22 18:20:16] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -53.155158+0.003887j
[2025-08-22 18:20:19] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -53.073746-0.001659j
[2025-08-22 18:20:22] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -53.051841-0.002634j
[2025-08-22 18:20:25] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -52.965795-0.001853j
[2025-08-22 18:20:28] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -53.091224-0.001579j
[2025-08-22 18:20:30] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -53.153440+0.002143j
[2025-08-22 18:20:33] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -53.141390+0.001170j
[2025-08-22 18:20:36] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -53.156474+0.000277j
[2025-08-22 18:20:39] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -52.995715+0.001461j
[2025-08-22 18:20:42] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -53.018133-0.000791j
[2025-08-22 18:20:45] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -53.019561-0.000389j
[2025-08-22 18:20:48] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -53.006641+0.000418j
[2025-08-22 18:20:50] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -52.991923-0.002664j
[2025-08-22 18:20:53] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -53.151181-0.001154j
[2025-08-22 18:20:56] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -53.037550+0.004321j
[2025-08-22 18:20:59] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -53.156326+0.001911j
[2025-08-22 18:21:02] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -53.127749-0.000666j
[2025-08-22 18:21:05] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -53.018872-0.000467j
[2025-08-22 18:21:08] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -53.050924+0.002795j
[2025-08-22 18:21:10] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -53.013749-0.007770j
[2025-08-22 18:21:13] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -53.054796-0.000032j
[2025-08-22 18:21:16] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -53.168489-0.000969j
[2025-08-22 18:21:19] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -53.102227-0.003625j
[2025-08-22 18:21:22] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -53.250312-0.002243j
[2025-08-22 18:21:25] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -53.288283-0.003446j
[2025-08-22 18:21:28] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -53.129700+0.004109j
[2025-08-22 18:21:30] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -53.171109+0.000264j
[2025-08-22 18:21:33] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -53.103626-0.000577j
[2025-08-22 18:21:36] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -53.042057+0.000830j
[2025-08-22 18:21:39] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -53.072547+0.003536j
[2025-08-22 18:21:42] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -52.981843-0.010522j
[2025-08-22 18:21:45] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -52.970867+0.003458j
[2025-08-22 18:21:47] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -52.937371-0.004392j
[2025-08-22 18:21:50] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -53.165869-0.002519j
[2025-08-22 18:21:53] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -53.043906+0.000087j
[2025-08-22 18:21:56] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -53.010897+0.003361j
[2025-08-22 18:21:59] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -53.040949+0.001273j
[2025-08-22 18:22:02] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -53.093961+0.001253j
[2025-08-22 18:22:05] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -53.087509+0.001124j
[2025-08-22 18:22:07] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -52.968831-0.002111j
[2025-08-22 18:22:10] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -53.017447+0.001149j
[2025-08-22 18:22:13] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -53.095480+0.002070j
[2025-08-22 18:22:16] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -52.976515-0.000159j
[2025-08-22 18:22:19] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -53.001265-0.004225j
[2025-08-22 18:22:22] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -53.021338-0.000850j
[2025-08-22 18:22:25] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -53.084855-0.003101j
[2025-08-22 18:22:27] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -53.068658+0.002313j
[2025-08-22 18:22:30] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -53.126977+0.000460j
[2025-08-22 18:22:33] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -53.088739+0.004061j
[2025-08-22 18:22:36] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -53.047583-0.001926j
[2025-08-22 18:22:39] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -53.077360-0.001702j
[2025-08-22 18:22:42] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -53.198137-0.004870j
[2025-08-22 18:22:45] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -53.104218+0.002680j
[2025-08-22 18:22:47] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -53.159527+0.002682j
[2025-08-22 18:22:50] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -53.172200-0.001726j
[2025-08-22 18:22:53] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -53.070963-0.002517j
[2025-08-22 18:22:56] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -53.133949-0.000805j
[2025-08-22 18:22:59] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -52.960123+0.001515j
[2025-08-22 18:22:59] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-08-22 18:23:02] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -52.996611+0.003420j
[2025-08-22 18:23:05] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -52.998859+0.001128j
[2025-08-22 18:23:07] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -52.858007-0.002506j
[2025-08-22 18:23:10] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -52.967658+0.000687j
[2025-08-22 18:23:13] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -53.019491-0.003742j
[2025-08-22 18:23:16] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -53.029778+0.000251j
[2025-08-22 18:23:19] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -52.998567-0.000660j
[2025-08-22 18:23:22] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -52.927444-0.003979j
[2025-08-22 18:23:25] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -53.027116+0.001359j
[2025-08-22 18:23:27] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -53.047402-0.003302j
[2025-08-22 18:23:30] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -53.132392-0.005720j
[2025-08-22 18:23:33] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -52.942729-0.001068j
[2025-08-22 18:23:36] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -52.977557+0.002531j
[2025-08-22 18:23:39] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -52.993977-0.015690j
[2025-08-22 18:23:42] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -52.998268+0.001037j
[2025-08-22 18:23:45] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -52.938615+0.002896j
[2025-08-22 18:23:47] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -53.059739-0.001707j
[2025-08-22 18:23:50] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -53.095632-0.002708j
[2025-08-22 18:23:53] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -53.046129-0.001167j
[2025-08-22 18:23:56] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -52.993487-0.001835j
[2025-08-22 18:23:59] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -52.963172-0.004890j
[2025-08-22 18:24:02] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -52.960778-0.003508j
[2025-08-22 18:24:05] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -52.936815-0.004869j
[2025-08-22 18:24:07] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -52.950265+0.001489j
[2025-08-22 18:24:10] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -52.904073+0.003770j
[2025-08-22 18:24:13] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -52.939327+0.000330j
[2025-08-22 18:24:16] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -52.915354-0.001812j
[2025-08-22 18:24:19] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -53.018032-0.002832j
[2025-08-22 18:24:22] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -52.917009+0.003959j
[2025-08-22 18:24:24] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -52.936143-0.001074j
[2025-08-22 18:24:27] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -52.972904+0.002133j
[2025-08-22 18:24:30] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -53.012081-0.001296j
[2025-08-22 18:24:33] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -53.038655-0.000613j
[2025-08-22 18:24:36] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -52.996206-0.000562j
[2025-08-22 18:24:39] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -53.058735-0.002856j
[2025-08-22 18:24:42] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -52.995250-0.002230j
[2025-08-22 18:24:44] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -53.046876-0.002904j
[2025-08-22 18:24:47] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -53.130647+0.001744j
[2025-08-22 18:24:50] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -53.098320-0.001951j
[2025-08-22 18:24:53] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -53.099964-0.000766j
[2025-08-22 18:24:56] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -53.009374+0.000324j
[2025-08-22 18:24:59] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -53.087330+0.004502j
[2025-08-22 18:25:02] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -53.158831+0.000680j
[2025-08-22 18:25:04] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -53.039057-0.009345j
[2025-08-22 18:25:07] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -53.119852+0.004823j
[2025-08-22 18:25:10] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -53.141882+0.000521j
[2025-08-22 18:25:13] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -53.025428+0.004726j
[2025-08-22 18:25:16] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -53.111393-0.004180j
[2025-08-22 18:25:19] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -53.006962+0.001541j
[2025-08-22 18:25:22] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -53.075461+0.000591j
[2025-08-22 18:25:24] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -53.122876+0.002625j
[2025-08-22 18:25:27] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -52.970523-0.000052j
[2025-08-22 18:25:30] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -52.969457-0.000205j
[2025-08-22 18:25:33] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -52.997867-0.002074j
[2025-08-22 18:25:36] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -53.071536+0.001863j
[2025-08-22 18:25:39] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -53.106755+0.000901j
[2025-08-22 18:25:42] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -53.013966+0.001817j
[2025-08-22 18:25:44] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -53.058385+0.000891j
[2025-08-22 18:25:47] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -52.937526+0.003918j
[2025-08-22 18:25:50] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -52.948287+0.003912j
[2025-08-22 18:25:53] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -53.001057+0.001603j
[2025-08-22 18:25:56] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -52.978133-0.001232j
[2025-08-22 18:25:59] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -53.038282+0.001753j
[2025-08-22 18:26:02] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -52.973952+0.000621j
[2025-08-22 18:26:04] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -53.083132-0.001049j
[2025-08-22 18:26:07] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -53.111266-0.000046j
[2025-08-22 18:26:10] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -53.057011+0.002738j
[2025-08-22 18:26:13] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -53.024858+0.005443j
[2025-08-22 18:26:16] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -53.055387+0.003646j
[2025-08-22 18:26:19] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -53.022918+0.000854j
[2025-08-22 18:26:22] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -53.112550-0.001174j
[2025-08-22 18:26:24] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -52.956738+0.001247j
[2025-08-22 18:26:27] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -53.083661-0.001447j
[2025-08-22 18:26:30] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -53.140100+0.006651j
[2025-08-22 18:26:33] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -53.169654+0.003035j
[2025-08-22 18:26:36] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -53.102371-0.000068j
[2025-08-22 18:26:39] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -53.057804-0.002773j
[2025-08-22 18:26:42] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -53.109277-0.001660j
[2025-08-22 18:26:44] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -53.009022+0.001886j
[2025-08-22 18:26:47] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -53.087455+0.001964j
[2025-08-22 18:26:50] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -53.122045-0.000734j
[2025-08-22 18:26:53] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -53.161429-0.003725j
[2025-08-22 18:26:56] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -53.185336-0.002821j
[2025-08-22 18:26:59] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -53.128639+0.000536j
[2025-08-22 18:27:01] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -53.078627+0.000553j
[2025-08-22 18:27:04] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -53.032389-0.005073j
[2025-08-22 18:27:07] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -52.903047-0.003855j
[2025-08-22 18:27:10] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -53.000635+0.000004j
[2025-08-22 18:27:13] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -52.959564+0.001606j
[2025-08-22 18:27:16] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -53.011549-0.000920j
[2025-08-22 18:27:19] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -52.982238+0.002482j
[2025-08-22 18:27:21] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -53.087536-0.001877j
[2025-08-22 18:27:24] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -52.942406-0.000037j
[2025-08-22 18:27:27] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -53.028823-0.001105j
[2025-08-22 18:27:30] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -53.044089+0.000633j
[2025-08-22 18:27:33] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -53.003674+0.001269j
[2025-08-22 18:27:36] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -52.972426-0.004937j
[2025-08-22 18:27:39] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -53.029593+0.002752j
[2025-08-22 18:27:41] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -53.120856+0.001090j
[2025-08-22 18:27:44] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -53.121294-0.002670j
[2025-08-22 18:27:44] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-08-22 18:27:47] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -53.215989-0.000661j
[2025-08-22 18:27:50] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -53.141281+0.000254j
[2025-08-22 18:27:53] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -53.158261-0.000341j
[2025-08-22 18:27:56] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -53.208574-0.000474j
[2025-08-22 18:27:59] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -53.282934-0.001210j
[2025-08-22 18:28:01] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -53.128820-0.001886j
[2025-08-22 18:28:04] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -53.060546-0.000541j
[2025-08-22 18:28:07] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -53.088905-0.006551j
[2025-08-22 18:28:10] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -53.131535+0.002892j
[2025-08-22 18:28:13] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -53.058079+0.001311j
[2025-08-22 18:28:16] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -53.081178-0.000330j
[2025-08-22 18:28:19] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -53.118465-0.002461j
[2025-08-22 18:28:21] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -53.054449-0.003189j
[2025-08-22 18:28:24] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -53.092827+0.000580j
[2025-08-22 18:28:27] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -52.902174-0.000876j
[2025-08-22 18:28:30] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -52.996699-0.004159j
[2025-08-22 18:28:33] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -53.027916-0.003591j
[2025-08-22 18:28:36] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -53.173646+0.002366j
[2025-08-22 18:28:39] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -52.978234-0.001115j
[2025-08-22 18:28:41] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -53.025072-0.003467j
[2025-08-22 18:28:44] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -53.048870+0.000800j
[2025-08-22 18:28:47] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -53.011042+0.005541j
[2025-08-22 18:28:50] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -52.992919+0.002001j
[2025-08-22 18:28:53] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -52.995585+0.000240j
[2025-08-22 18:28:56] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -52.970095-0.002339j
[2025-08-22 18:28:59] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -52.957787-0.002780j
[2025-08-22 18:29:01] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -52.965567-0.002325j
[2025-08-22 18:29:04] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -53.040705-0.007389j
[2025-08-22 18:29:07] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -53.060121-0.000534j
[2025-08-22 18:29:10] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -53.101457-0.000612j
[2025-08-22 18:29:13] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -53.238100+0.001090j
[2025-08-22 18:29:16] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -53.098810-0.002351j
[2025-08-22 18:29:19] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -53.050942-0.003711j
[2025-08-22 18:29:21] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -53.105090-0.001326j
[2025-08-22 18:29:24] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -53.136753-0.004068j
[2025-08-22 18:29:27] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -53.094161+0.000692j
[2025-08-22 18:29:30] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -53.165466-0.000082j
[2025-08-22 18:29:33] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -53.034551-0.000945j
[2025-08-22 18:29:36] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -53.004955+0.000853j
[2025-08-22 18:29:39] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -53.091835-0.005973j
[2025-08-22 18:29:41] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -53.166605-0.001040j
[2025-08-22 18:29:44] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -53.071445+0.001484j
[2025-08-22 18:29:47] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -52.933914-0.004375j
[2025-08-22 18:29:50] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -53.047994-0.001252j
[2025-08-22 18:29:53] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -52.984105-0.000617j
[2025-08-22 18:29:56] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -53.087356-0.000954j
[2025-08-22 18:29:58] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -53.040443-0.003516j
[2025-08-22 18:30:01] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -52.993949+0.000017j
[2025-08-22 18:30:04] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -53.023709-0.001207j
[2025-08-22 18:30:07] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -53.012872-0.005958j
[2025-08-22 18:30:10] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -52.952445+0.003474j
[2025-08-22 18:30:13] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -52.910116-0.004495j
[2025-08-22 18:30:16] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -52.929337+0.000953j
[2025-08-22 18:30:18] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -53.016311-0.001772j
[2025-08-22 18:30:21] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -53.128235-0.001977j
[2025-08-22 18:30:24] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -53.028912+0.000962j
[2025-08-22 18:30:27] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -53.004455+0.003746j
[2025-08-22 18:30:30] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -53.136256-0.000138j
[2025-08-22 18:30:33] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -53.001875+0.005650j
[2025-08-22 18:30:36] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -53.028776+0.002117j
[2025-08-22 18:30:38] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -52.968093+0.005200j
[2025-08-22 18:30:41] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -53.049535-0.003038j
[2025-08-22 18:30:44] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -53.124040+0.000619j
[2025-08-22 18:30:47] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -53.136880-0.000570j
[2025-08-22 18:30:50] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -53.133673+0.002842j
[2025-08-22 18:30:53] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -53.136906+0.001189j
[2025-08-22 18:30:56] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -53.085261+0.001071j
[2025-08-22 18:30:58] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -53.095057-0.000264j
[2025-08-22 18:31:01] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -53.065245+0.004627j
[2025-08-22 18:31:04] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -52.996083-0.002086j
[2025-08-22 18:31:07] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -53.038535+0.003364j
[2025-08-22 18:31:10] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -53.053331-0.002180j
[2025-08-22 18:31:13] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -53.041694+0.004017j
[2025-08-22 18:31:16] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -53.208020+0.000924j
[2025-08-22 18:31:18] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -53.145770+0.002312j
[2025-08-22 18:31:21] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -53.117399+0.004140j
[2025-08-22 18:31:24] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -52.995645-0.000747j
[2025-08-22 18:31:27] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -53.032127-0.000856j
[2025-08-22 18:31:30] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -53.050629+0.000080j
[2025-08-22 18:31:33] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -53.012080+0.003392j
[2025-08-22 18:31:36] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -53.067971+0.004272j
[2025-08-22 18:31:38] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -53.018535+0.000468j
[2025-08-22 18:31:41] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -53.093094-0.000481j
[2025-08-22 18:31:44] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -53.060797-0.000314j
[2025-08-22 18:31:47] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -53.126352-0.001149j
[2025-08-22 18:31:50] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -53.021071+0.000750j
[2025-08-22 18:31:53] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -53.160162+0.002056j
[2025-08-22 18:31:56] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -53.057063+0.002333j
[2025-08-22 18:31:58] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -52.935308-0.002849j
[2025-08-22 18:32:01] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -53.042793-0.006907j
[2025-08-22 18:32:04] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -53.022336+0.002551j
[2025-08-22 18:32:07] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -53.118751+0.001124j
[2025-08-22 18:32:10] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -53.103533+0.011688j
[2025-08-22 18:32:13] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -53.090199-0.000074j
[2025-08-22 18:32:15] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -53.040924+0.000206j
[2025-08-22 18:32:18] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -52.839330+0.001893j
[2025-08-22 18:32:21] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -53.043136-0.000560j
[2025-08-22 18:32:24] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -52.980168-0.002838j
[2025-08-22 18:32:27] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -53.017997-0.000369j
[2025-08-22 18:32:30] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -52.977018-0.003831j
[2025-08-22 18:32:30] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-22 18:32:33] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -53.029650-0.004805j
[2025-08-22 18:32:35] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -52.941071-0.003914j
[2025-08-22 18:32:38] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -53.006527+0.000252j
[2025-08-22 18:32:41] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -53.159029-0.000720j
[2025-08-22 18:32:44] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -53.005317+0.001400j
[2025-08-22 18:32:47] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -53.043836+0.005313j
[2025-08-22 18:32:50] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -52.972403-0.003369j
[2025-08-22 18:32:53] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -53.096143-0.003232j
[2025-08-22 18:32:55] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -52.981866-0.003889j
[2025-08-22 18:32:58] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -53.171449+0.002147j
[2025-08-22 18:33:01] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -53.000812+0.002473j
[2025-08-22 18:33:04] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -53.086673-0.000240j
[2025-08-22 18:33:07] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -53.158266+0.002685j
[2025-08-22 18:33:10] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -53.164268+0.004889j
[2025-08-22 18:33:13] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -53.158212+0.000770j
[2025-08-22 18:33:15] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -53.112161-0.002360j
[2025-08-22 18:33:18] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -53.073383-0.007255j
[2025-08-22 18:33:21] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -52.981605+0.000223j
[2025-08-22 18:33:24] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -53.039035-0.002031j
[2025-08-22 18:33:27] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -52.873138+0.000810j
[2025-08-22 18:33:30] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -52.782676+0.002791j
[2025-08-22 18:33:33] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -52.858894+0.006060j
[2025-08-22 18:33:35] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -52.942584+0.002099j
[2025-08-22 18:33:38] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -52.989446-0.002236j
[2025-08-22 18:33:41] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -53.064615-0.001775j
[2025-08-22 18:33:44] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -52.997725-0.003450j
[2025-08-22 18:33:47] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -53.013019+0.000346j
[2025-08-22 18:33:50] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -53.083869+0.002469j
[2025-08-22 18:33:53] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -53.064657-0.000043j
[2025-08-22 18:33:55] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -53.086178-0.001342j
[2025-08-22 18:33:58] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -53.232634-0.000511j
[2025-08-22 18:34:01] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -53.112844+0.003872j
[2025-08-22 18:34:04] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -53.087360-0.001341j
[2025-08-22 18:34:07] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -53.020841-0.005666j
[2025-08-22 18:34:10] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -53.087391+0.003307j
[2025-08-22 18:34:13] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -53.127692-0.005637j
[2025-08-22 18:34:15] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -53.066726-0.000156j
[2025-08-22 18:34:18] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -53.228005-0.002235j
[2025-08-22 18:34:21] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -53.039206-0.002787j
[2025-08-22 18:34:24] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -53.184818-0.003161j
[2025-08-22 18:34:27] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -53.095206-0.000426j
[2025-08-22 18:34:30] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -53.038132-0.000061j
[2025-08-22 18:34:33] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -53.062910-0.002426j
[2025-08-22 18:34:35] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -53.117934+0.000141j
[2025-08-22 18:34:38] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -53.070693+0.001131j
[2025-08-22 18:34:41] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -53.065823-0.004463j
[2025-08-22 18:34:44] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -53.087611+0.002528j
[2025-08-22 18:34:47] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -53.012268-0.001174j
[2025-08-22 18:34:50] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -53.031790-0.002564j
[2025-08-22 18:34:52] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -53.107287-0.002309j
[2025-08-22 18:34:52] ✅ Training completed | Restarts: 2
[2025-08-22 18:34:52] ============================================================
[2025-08-22 18:34:52] Training completed | Runtime: 3051.6s
[2025-08-22 18:34:54] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-22 18:34:54] ============================================================
