[2025-08-06 01:30:43] 使用checkpoint文件: results/L=4/J2=0.00/J1=0.04/training/checkpoints/checkpoint_iter_001500.pkl
[2025-08-06 01:30:50] ✓ 从checkpoint加载参数: 1500
[2025-08-06 01:30:50]   - 能量: -54.580775+0.000423j ± 0.042200
[2025-08-06 01:30:50] ================================================================================
[2025-08-06 01:30:50] 加载量子态: L=4, J2=0.00, J1=0.04, checkpoint=checkpoint_iter_001500
[2025-08-06 01:30:50] 设置样本数为: 1048576
[2025-08-06 01:30:50] 开始生成共享样本集...
[2025-08-06 01:33:22] 样本生成完成,耗时: 151.742 秒
[2025-08-06 01:33:22] ================================================================================
[2025-08-06 01:33:22] 开始计算自旋结构因子...
[2025-08-06 01:33:22] 初始化操作符缓存...
[2025-08-06 01:33:22] 预构建所有自旋相关操作符...
[2025-08-06 01:33:22] 开始计算自旋相关函数...
[2025-08-06 01:33:33] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 10.249s
[2025-08-06 01:33:46] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 13.264s
[2025-08-06 01:33:54] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 8.125s
[2025-08-06 01:34:02] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 8.148s
[2025-08-06 01:34:10] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 8.127s
[2025-08-06 01:34:18] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 8.119s
[2025-08-06 01:34:26] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 8.103s
[2025-08-06 01:34:35] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 8.125s
[2025-08-06 01:34:43] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 8.114s
[2025-08-06 01:34:51] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 8.108s
[2025-08-06 01:34:59] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 8.125s
[2025-08-06 01:35:07] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 8.108s
[2025-08-06 01:35:15] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 8.127s
[2025-08-06 01:35:23] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 8.106s
[2025-08-06 01:35:31] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 8.125s
[2025-08-06 01:35:40] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 8.105s
[2025-08-06 01:35:48] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 8.128s
[2025-08-06 01:35:56] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 8.122s
[2025-08-06 01:36:04] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 8.103s
[2025-08-06 01:36:12] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 8.124s
[2025-08-06 01:36:20] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 8.108s
[2025-08-06 01:36:28] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 8.105s
[2025-08-06 01:36:36] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 8.124s
[2025-08-06 01:36:44] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 8.103s
[2025-08-06 01:36:53] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 8.111s
[2025-08-06 01:37:01] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 8.125s
[2025-08-06 01:37:09] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 8.103s
[2025-08-06 01:37:17] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 8.124s
[2025-08-06 01:37:25] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 8.106s
[2025-08-06 01:37:33] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 8.107s
[2025-08-06 01:37:41] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 8.128s
[2025-08-06 01:37:49] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 8.126s
[2025-08-06 01:37:58] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 8.104s
[2025-08-06 01:38:06] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 8.105s
[2025-08-06 01:38:14] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 8.125s
[2025-08-06 01:38:22] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 8.106s
[2025-08-06 01:38:30] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 8.125s
[2025-08-06 01:38:38] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 8.128s
[2025-08-06 01:38:46] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 8.107s
[2025-08-06 01:38:54] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 8.128s
[2025-08-06 01:39:02] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 8.107s
[2025-08-06 01:39:11] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 8.103s
[2025-08-06 01:39:19] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 8.125s
[2025-08-06 01:39:27] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 8.105s
[2025-08-06 01:39:35] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 8.127s
[2025-08-06 01:39:43] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 8.127s
[2025-08-06 01:39:51] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 8.108s
[2025-08-06 01:39:59] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 8.136s
[2025-08-06 01:40:07] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 8.108s
[2025-08-06 01:40:16] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 8.103s
[2025-08-06 01:40:24] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 8.121s
[2025-08-06 01:40:32] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 8.150s
[2025-08-06 01:40:40] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 8.103s
[2025-08-06 01:40:48] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 8.107s
[2025-08-06 01:40:56] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 8.126s
[2025-08-06 01:41:04] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 8.107s
[2025-08-06 01:41:12] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 8.136s
[2025-08-06 01:41:20] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 8.124s
[2025-08-06 01:41:29] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 8.103s
[2025-08-06 01:41:37] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 8.125s
[2025-08-06 01:41:45] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 8.106s
[2025-08-06 01:41:53] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 8.108s
[2025-08-06 01:42:01] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 8.125s
[2025-08-06 01:42:09] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 8.107s
[2025-08-06 01:42:09] 自旋相关函数计算完成,总耗时 526.82 秒
[2025-08-06 01:42:09] 计算傅里叶变换...
[2025-08-06 01:42:10] 自旋结构因子计算完成
[2025-08-06 01:42:11] 自旋相关函数平均误差: 0.000663
