[2025-08-06 01:53:55] 使用checkpoint文件: results/L=4/J2=0.00/J1=0.04/training/checkpoints/checkpoint_iter_002500.pkl
[2025-08-06 01:54:03] ✓ 从checkpoint加载参数: 2500
[2025-08-06 01:54:03]   - 能量: -54.591014+0.000464j ± 0.042389
[2025-08-06 01:54:03] ================================================================================
[2025-08-06 01:54:03] 加载量子态: L=4, J2=0.00, J1=0.04, checkpoint=checkpoint_iter_002500
[2025-08-06 01:54:03] 设置样本数为: 1048576
[2025-08-06 01:54:03] 开始生成共享样本集...
[2025-08-06 01:56:35] 样本生成完成,耗时: 152.188 秒
[2025-08-06 01:56:35] ================================================================================
[2025-08-06 01:56:35] 开始计算自旋结构因子...
[2025-08-06 01:56:35] 初始化操作符缓存...
[2025-08-06 01:56:35] 预构建所有自旋相关操作符...
[2025-08-06 01:56:35] 开始计算自旋相关函数...
[2025-08-06 01:56:46] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 10.362s
[2025-08-06 01:56:59] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 13.409s
[2025-08-06 01:57:07] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 8.216s
[2025-08-06 01:57:16] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 8.175s
[2025-08-06 01:57:24] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 8.215s
[2025-08-06 01:57:32] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 8.206s
[2025-08-06 01:57:40] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 8.177s
[2025-08-06 01:57:48] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 8.218s
[2025-08-06 01:57:57] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 8.177s
[2025-08-06 01:58:05] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 8.175s
[2025-08-06 01:58:13] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 8.217s
[2025-08-06 01:58:21] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 8.176s
[2025-08-06 01:58:29] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 8.213s
[2025-08-06 01:58:37] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 8.176s
[2025-08-06 01:58:46] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 8.217s
[2025-08-06 01:58:54] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 8.177s
[2025-08-06 01:59:02] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 8.263s
[2025-08-06 01:59:10] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 8.209s
[2025-08-06 01:59:19] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 8.178s
[2025-08-06 01:59:27] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 8.214s
[2025-08-06 01:59:35] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 8.176s
[2025-08-06 01:59:43] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 8.176s
[2025-08-06 01:59:51] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 8.218s
[2025-08-06 02:00:00] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 8.179s
[2025-08-06 02:00:08] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 8.201s
[2025-08-06 02:00:16] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 8.216s
[2025-08-06 02:00:24] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 8.177s
[2025-08-06 02:00:32] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 8.218s
[2025-08-06 02:00:41] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 8.176s
[2025-08-06 02:00:49] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 8.175s
[2025-08-06 02:00:57] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 8.215s
[2025-08-06 02:01:05] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 8.216s
[2025-08-06 02:01:13] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 8.177s
[2025-08-06 02:01:21] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 8.177s
[2025-08-06 02:01:30] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 8.218s
[2025-08-06 02:01:38] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 8.176s
[2025-08-06 02:01:46] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 8.216s
[2025-08-06 02:01:54] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 8.215s
[2025-08-06 02:02:02] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 8.175s
[2025-08-06 02:02:11] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 8.214s
[2025-08-06 02:02:19] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 8.175s
[2025-08-06 02:02:27] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 8.178s
[2025-08-06 02:02:35] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 8.218s
[2025-08-06 02:02:43] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 8.177s
[2025-08-06 02:02:52] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 8.215s
[2025-08-06 02:03:00] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 8.214s
[2025-08-06 02:03:08] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 8.177s
[2025-08-06 02:03:16] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 8.214s
[2025-08-06 02:03:24] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 8.176s
[2025-08-06 02:03:33] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 8.178s
[2025-08-06 02:03:41] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 8.210s
[2025-08-06 02:03:49] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 8.178s
[2025-08-06 02:03:57] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 8.178s
[2025-08-06 02:04:05] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 8.176s
[2025-08-06 02:04:14] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 8.216s
[2025-08-06 02:04:22] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 8.176s
[2025-08-06 02:04:30] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 8.214s
[2025-08-06 02:04:38] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 8.216s
[2025-08-06 02:04:46] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 8.178s
[2025-08-06 02:04:55] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 8.218s
[2025-08-06 02:05:03] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 8.177s
[2025-08-06 02:05:11] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 8.186s
[2025-08-06 02:05:19] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 8.215s
[2025-08-06 02:05:27] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 8.176s
[2025-08-06 02:05:27] 自旋相关函数计算完成,总耗时 531.98 秒
[2025-08-06 02:05:27] 计算傅里叶变换...
[2025-08-06 02:05:28] 自旋结构因子计算完成
[2025-08-06 02:05:29] 自旋相关函数平均误差: 0.000664
