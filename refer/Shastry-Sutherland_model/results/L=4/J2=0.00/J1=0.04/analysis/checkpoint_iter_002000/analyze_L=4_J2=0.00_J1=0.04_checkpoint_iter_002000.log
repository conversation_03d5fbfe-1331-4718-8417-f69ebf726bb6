[2025-08-06 01:42:16] 使用checkpoint文件: results/L=4/J2=0.00/J1=0.04/training/checkpoints/checkpoint_iter_002000.pkl
[2025-08-06 01:42:24] ✓ 从checkpoint加载参数: 2000
[2025-08-06 01:42:24]   - 能量: -54.580393-0.000268j ± 0.043469
[2025-08-06 01:42:24] ================================================================================
[2025-08-06 01:42:24] 加载量子态: L=4, J2=0.00, J1=0.04, checkpoint=checkpoint_iter_002000
[2025-08-06 01:42:24] 设置样本数为: 1048576
[2025-08-06 01:42:24] 开始生成共享样本集...
[2025-08-06 01:44:56] 样本生成完成,耗时: 151.774 秒
[2025-08-06 01:44:56] ================================================================================
[2025-08-06 01:44:56] 开始计算自旋结构因子...
[2025-08-06 01:44:56] 初始化操作符缓存...
[2025-08-06 01:44:56] 预构建所有自旋相关操作符...
[2025-08-06 01:44:56] 开始计算自旋相关函数...
[2025-08-06 01:45:06] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 10.158s
[2025-08-06 01:45:20] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 13.482s
[2025-08-06 01:45:28] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 8.225s
[2025-08-06 01:45:36] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 8.183s
[2025-08-06 01:45:44] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 8.224s
[2025-08-06 01:45:52] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 8.209s
[2025-08-06 01:46:01] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 8.182s
[2025-08-06 01:46:09] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 8.223s
[2025-08-06 01:46:17] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 8.185s
[2025-08-06 01:46:25] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 8.184s
[2025-08-06 01:46:33] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 8.223s
[2025-08-06 01:46:42] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 8.183s
[2025-08-06 01:46:50] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 8.223s
[2025-08-06 01:46:58] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 8.185s
[2025-08-06 01:47:06] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 8.224s
[2025-08-06 01:47:14] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 8.186s
[2025-08-06 01:47:23] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 8.225s
[2025-08-06 01:47:31] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 8.246s
[2025-08-06 01:47:39] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 8.184s
[2025-08-06 01:47:47] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 8.213s
[2025-08-06 01:47:55] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 8.183s
[2025-08-06 01:48:04] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 8.185s
[2025-08-06 01:48:12] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 8.220s
[2025-08-06 01:48:20] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 8.185s
[2025-08-06 01:48:28] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 8.205s
[2025-08-06 01:48:36] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 8.225s
[2025-08-06 01:48:45] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 8.182s
[2025-08-06 01:48:53] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 8.221s
[2025-08-06 01:49:01] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 8.185s
[2025-08-06 01:49:09] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 8.184s
[2025-08-06 01:49:17] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 8.225s
[2025-08-06 01:49:26] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 8.225s
[2025-08-06 01:49:34] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 8.185s
[2025-08-06 01:49:42] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 8.185s
[2025-08-06 01:49:50] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 8.220s
[2025-08-06 01:49:58] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 8.185s
[2025-08-06 01:50:07] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 8.229s
[2025-08-06 01:50:15] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 8.225s
[2025-08-06 01:50:23] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 8.184s
[2025-08-06 01:50:31] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 8.222s
[2025-08-06 01:50:40] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 8.184s
[2025-08-06 01:50:48] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 8.182s
[2025-08-06 01:50:56] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 8.224s
[2025-08-06 01:51:04] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 8.186s
[2025-08-06 01:51:12] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 8.224s
[2025-08-06 01:51:21] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 8.223s
[2025-08-06 01:51:29] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 8.183s
[2025-08-06 01:51:37] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 8.221s
[2025-08-06 01:51:45] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 8.183s
[2025-08-06 01:51:53] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 8.183s
[2025-08-06 01:52:02] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 8.211s
[2025-08-06 01:52:10] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 8.186s
[2025-08-06 01:52:18] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 8.183s
[2025-08-06 01:52:26] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 8.184s
[2025-08-06 01:52:34] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 8.226s
[2025-08-06 01:52:43] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 8.184s
[2025-08-06 01:52:51] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 8.220s
[2025-08-06 01:52:59] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 8.215s
[2025-08-06 01:53:07] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 8.185s
[2025-08-06 01:53:15] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 8.225s
[2025-08-06 01:53:24] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 8.190s
[2025-08-06 01:53:32] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 8.229s
[2025-08-06 01:53:40] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 8.222s
[2025-08-06 01:53:48] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 8.184s
[2025-08-06 01:53:48] 自旋相关函数计算完成,总耗时 532.31 秒
[2025-08-06 01:53:48] 计算傅里叶变换...
[2025-08-06 01:53:49] 自旋结构因子计算完成
[2025-08-06 01:53:50] 自旋相关函数平均误差: 0.000668
