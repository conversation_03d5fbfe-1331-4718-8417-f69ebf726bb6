[2025-08-06 02:28:46] 使用checkpoint文件: results/L=4/J2=0.00/J1=0.04/training/checkpoints/checkpoint_iter_004000.pkl
[2025-08-06 02:28:54] ✓ 从checkpoint加载参数: 4000
[2025-08-06 02:28:54]   - 能量: -54.605425+0.001204j ± 0.043398
[2025-08-06 02:28:54] ================================================================================
[2025-08-06 02:28:54] 加载量子态: L=4, J2=0.00, J1=0.04, checkpoint=checkpoint_iter_004000
[2025-08-06 02:28:54] 设置样本数为: 1048576
[2025-08-06 02:28:54] 开始生成共享样本集...
[2025-08-06 02:31:25] 样本生成完成,耗时: 151.206 秒
[2025-08-06 02:31:25] ================================================================================
[2025-08-06 02:31:25] 开始计算自旋结构因子...
[2025-08-06 02:31:25] 初始化操作符缓存...
[2025-08-06 02:31:25] 预构建所有自旋相关操作符...
[2025-08-06 02:31:25] 开始计算自旋相关函数...
[2025-08-06 02:31:36] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 10.310s
[2025-08-06 02:31:49] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 13.328s
[2025-08-06 02:31:57] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 8.134s
[2025-08-06 02:32:05] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 8.099s
[2025-08-06 02:32:13] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 8.135s
[2025-08-06 02:32:22] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 8.115s
[2025-08-06 02:32:30] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 8.091s
[2025-08-06 02:32:38] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 8.131s
[2025-08-06 02:32:46] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 8.093s
[2025-08-06 02:32:54] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 8.096s
[2025-08-06 02:33:02] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 8.192s
[2025-08-06 02:33:10] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 8.099s
[2025-08-06 02:33:18] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 8.140s
[2025-08-06 02:33:26] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 8.093s
[2025-08-06 02:33:35] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 8.133s
[2025-08-06 02:33:43] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 8.091s
[2025-08-06 02:33:51] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 8.136s
[2025-08-06 02:33:59] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 8.119s
[2025-08-06 02:34:07] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 8.091s
[2025-08-06 02:34:15] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 8.123s
[2025-08-06 02:34:23] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 8.098s
[2025-08-06 02:34:31] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 8.092s
[2025-08-06 02:34:40] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 8.129s
[2025-08-06 02:34:48] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 8.092s
[2025-08-06 02:34:56] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 8.112s
[2025-08-06 02:35:04] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 8.135s
[2025-08-06 02:35:12] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 8.091s
[2025-08-06 02:35:20] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 8.130s
[2025-08-06 02:35:28] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 8.094s
[2025-08-06 02:35:36] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 8.095s
[2025-08-06 02:35:44] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 8.136s
[2025-08-06 02:35:53] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 8.135s
[2025-08-06 02:36:01] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 8.092s
[2025-08-06 02:36:09] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 8.093s
[2025-08-06 02:36:17] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 8.130s
[2025-08-06 02:36:25] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 8.094s
[2025-08-06 02:36:33] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 8.134s
[2025-08-06 02:36:41] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 8.136s
[2025-08-06 02:36:49] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 8.096s
[2025-08-06 02:36:57] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 8.141s
[2025-08-06 02:37:06] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 8.096s
[2025-08-06 02:37:14] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 8.091s
[2025-08-06 02:37:22] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 8.133s
[2025-08-06 02:37:30] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 8.092s
[2025-08-06 02:37:38] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 8.136s
[2025-08-06 02:37:46] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 8.140s
[2025-08-06 02:37:54] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 8.100s
[2025-08-06 02:38:02] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 8.190s
[2025-08-06 02:38:11] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 8.096s
[2025-08-06 02:38:19] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 8.090s
[2025-08-06 02:38:27] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 8.118s
[2025-08-06 02:38:35] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 8.092s
[2025-08-06 02:38:43] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 8.091s
[2025-08-06 02:38:51] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 8.095s
[2025-08-06 02:38:59] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 8.134s
[2025-08-06 02:39:07] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 8.097s
[2025-08-06 02:39:15] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 8.138s
[2025-08-06 02:39:24] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 8.125s
[2025-08-06 02:39:32] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 8.091s
[2025-08-06 02:39:40] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 8.133s
[2025-08-06 02:39:48] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 8.093s
[2025-08-06 02:39:56] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 8.099s
[2025-08-06 02:40:04] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 8.137s
[2025-08-06 02:40:12] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 8.097s
[2025-08-06 02:40:12] 自旋相关函数计算完成,总耗时 526.79 秒
[2025-08-06 02:40:12] 计算傅里叶变换...
[2025-08-06 02:40:13] 自旋结构因子计算完成
[2025-08-06 02:40:14] 自旋相关函数平均误差: 0.000666
