[2025-08-07 18:13:59] 使用checkpoint文件: results/L=4/J2=0.00/J1=0.03/training/checkpoints/checkpoint_iter_000600.pkl
[2025-08-07 18:14:07] ✓ 从checkpoint加载参数: 600
[2025-08-07 18:14:07]   - 能量: -54.244536+0.001952j ± 0.087753
[2025-08-07 18:14:07] ================================================================================
[2025-08-07 18:14:07] 加载量子态: L=4, J2=0.00, J1=0.03, checkpoint=checkpoint_iter_000600
[2025-08-07 18:14:07] 设置样本数为: 1048576
[2025-08-07 18:14:07] 开始生成共享样本集...
[2025-08-07 18:16:38] 样本生成完成,耗时: 151.344 秒
[2025-08-07 18:16:38] ================================================================================
[2025-08-07 18:16:38] 开始计算自旋结构因子...
[2025-08-07 18:16:38] 初始化操作符缓存...
[2025-08-07 18:16:38] 预构建所有自旋相关操作符...
[2025-08-07 18:16:38] 开始计算自旋相关函数...
[2025-08-07 18:16:48] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 10.128s
[2025-08-07 18:17:01] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 13.207s
[2025-08-07 18:17:10] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 8.174s
[2025-08-07 18:17:18] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 8.157s
[2025-08-07 18:17:26] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 8.176s
[2025-08-07 18:17:34] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 8.176s
[2025-08-07 18:17:42] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 8.165s
[2025-08-07 18:17:50] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 8.174s
[2025-08-07 18:17:59] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 8.156s
[2025-08-07 18:18:07] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 8.157s
[2025-08-07 18:18:15] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 8.175s
[2025-08-07 18:18:23] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 8.157s
[2025-08-07 18:18:31] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 8.176s
[2025-08-07 18:18:39] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 8.157s
[2025-08-07 18:18:48] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 8.176s
[2025-08-07 18:18:56] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 8.158s
[2025-08-07 18:19:04] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 8.175s
[2025-08-07 18:19:12] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 8.177s
[2025-08-07 18:19:20] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 8.162s
[2025-08-07 18:19:28] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 8.176s
[2025-08-07 18:19:37] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 8.157s
[2025-08-07 18:19:45] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 8.157s
[2025-08-07 18:19:53] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 8.178s
[2025-08-07 18:20:01] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 8.159s
[2025-08-07 18:20:09] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 8.195s
[2025-08-07 18:20:17] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 8.175s
[2025-08-07 18:20:26] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 8.165s
[2025-08-07 18:20:34] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 8.175s
[2025-08-07 18:20:42] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 8.157s
[2025-08-07 18:20:50] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 8.161s
[2025-08-07 18:20:58] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 8.177s
[2025-08-07 18:21:06] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 8.175s
[2025-08-07 18:21:15] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 8.157s
[2025-08-07 18:21:23] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 8.158s
[2025-08-07 18:21:31] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 8.178s
[2025-08-07 18:21:39] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 8.204s
[2025-08-07 18:21:47] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 8.176s
[2025-08-07 18:21:55] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 8.177s
[2025-08-07 18:22:04] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 8.160s
[2025-08-07 18:22:12] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 8.176s
[2025-08-07 18:22:20] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 8.157s
[2025-08-07 18:22:28] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 8.165s
[2025-08-07 18:22:36] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 8.176s
[2025-08-07 18:22:44] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 8.163s
[2025-08-07 18:22:53] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 8.177s
[2025-08-07 18:23:01] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 8.177s
[2025-08-07 18:23:09] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 8.158s
[2025-08-07 18:23:17] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 8.177s
[2025-08-07 18:23:25] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 8.158s
[2025-08-07 18:23:34] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 8.161s
[2025-08-07 18:23:42] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 8.176s
[2025-08-07 18:23:50] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 8.163s
[2025-08-07 18:23:58] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 8.161s
[2025-08-07 18:24:06] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 8.161s
[2025-08-07 18:24:14] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 8.179s
[2025-08-07 18:24:23] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 8.158s
[2025-08-07 18:24:31] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 8.176s
[2025-08-07 18:24:39] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 8.261s
[2025-08-07 18:24:47] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 8.157s
[2025-08-07 18:24:55] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 8.179s
[2025-08-07 18:25:03] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 8.157s
[2025-08-07 18:25:12] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 8.157s
[2025-08-07 18:25:20] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 8.176s
[2025-08-07 18:25:28] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 8.163s
[2025-08-07 18:25:28] 自旋相关函数计算完成,总耗时 529.95 秒
[2025-08-07 18:25:28] 计算傅里叶变换...
[2025-08-07 18:25:28] 自旋结构因子计算完成
[2025-08-07 18:25:29] 自旋相关函数平均误差: 0.000668
