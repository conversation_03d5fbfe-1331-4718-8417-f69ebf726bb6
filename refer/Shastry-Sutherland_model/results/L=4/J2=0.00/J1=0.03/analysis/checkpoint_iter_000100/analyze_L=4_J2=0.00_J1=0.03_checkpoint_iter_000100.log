[2025-08-07 17:14:39] 使用checkpoint文件: results/L=4/J2=0.00/J1=0.03/training/checkpoints/checkpoint_iter_000100.pkl
[2025-08-07 17:14:47] ✓ 从checkpoint加载参数: 100
[2025-08-07 17:14:47]   - 能量: -54.153091+0.002594j ± 0.085762
[2025-08-07 17:14:47] ================================================================================
[2025-08-07 17:14:47] 加载量子态: L=4, J2=0.00, J1=0.03, checkpoint=checkpoint_iter_000100
[2025-08-07 17:14:47] 设置样本数为: 1048576
[2025-08-07 17:14:47] 开始生成共享样本集...
[2025-08-07 17:17:19] 样本生成完成,耗时: 151.834 秒
[2025-08-07 17:17:19] ================================================================================
[2025-08-07 17:17:19] 开始计算自旋结构因子...
[2025-08-07 17:17:19] 初始化操作符缓存...
[2025-08-07 17:17:19] 预构建所有自旋相关操作符...
[2025-08-07 17:17:19] 开始计算自旋相关函数...
[2025-08-07 17:17:29] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 10.098s
[2025-08-07 17:17:42] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 13.091s
[2025-08-07 17:17:50] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 8.148s
[2025-08-07 17:17:59] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 8.143s
[2025-08-07 17:18:07] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 8.156s
[2025-08-07 17:18:15] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 8.150s
[2025-08-07 17:18:23] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 8.135s
[2025-08-07 17:18:31] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 8.148s
[2025-08-07 17:18:39] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 8.143s
[2025-08-07 17:18:47] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 8.143s
[2025-08-07 17:18:56] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 8.147s
[2025-08-07 17:19:04] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 8.144s
[2025-08-07 17:19:12] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 8.146s
[2025-08-07 17:19:20] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 8.143s
[2025-08-07 17:19:28] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 8.154s
[2025-08-07 17:19:36] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 8.142s
[2025-08-07 17:19:45] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 8.149s
[2025-08-07 17:19:53] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 8.146s
[2025-08-07 17:20:01] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 8.136s
[2025-08-07 17:21:12] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 8.201s
[2025-08-07 17:21:20] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 8.147s
[2025-08-07 17:21:28] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 8.148s
[2025-08-07 17:21:37] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 8.164s
[2025-08-07 17:21:45] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 8.140s
[2025-08-07 17:21:53] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 8.150s
[2025-08-07 17:22:01] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 8.150s
[2025-08-07 17:22:09] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 8.205s
[2025-08-07 17:22:17] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 8.150s
[2025-08-07 17:22:26] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 8.146s
[2025-08-07 17:22:34] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 8.145s
[2025-08-07 17:22:42] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 8.157s
[2025-08-07 17:22:50] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 8.150s
[2025-08-07 17:22:58] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 8.141s
[2025-08-07 17:23:06] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 8.144s
[2025-08-07 17:23:14] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 8.160s
[2025-08-07 17:23:23] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 8.143s
[2025-08-07 17:23:31] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 8.156s
[2025-08-07 17:23:39] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 8.210s
[2025-08-07 17:23:47] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 8.144s
[2025-08-07 17:23:55] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 8.146s
[2025-08-07 17:24:03] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 8.142s
[2025-08-07 17:24:12] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 8.134s
[2025-08-07 17:24:20] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 8.153s
[2025-08-07 17:24:28] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 8.143s
[2025-08-07 17:24:36] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 8.146s
[2025-08-07 17:24:44] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 8.146s
[2025-08-07 17:24:52] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 8.142s
[2025-08-07 17:25:00] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 8.145s
[2025-08-07 17:25:09] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 8.151s
[2025-08-07 17:25:17] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 8.134s
[2025-08-07 17:25:25] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 8.147s
[2025-08-07 17:25:33] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 8.142s
[2025-08-07 17:25:41] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 8.134s
[2025-08-07 17:25:49] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 8.153s
[2025-08-07 17:25:57] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 8.161s
[2025-08-07 17:26:06] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 8.141s
[2025-08-07 17:26:14] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 8.145s
[2025-08-07 17:26:22] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 8.146s
[2025-08-07 17:26:30] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 8.137s
[2025-08-07 17:26:38] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 8.153s
[2025-08-07 17:26:46] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 8.141s
[2025-08-07 17:26:54] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 8.143s
[2025-08-07 17:27:03] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 8.146s
[2025-08-07 17:27:11] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 8.142s
[2025-08-07 17:27:11] 自旋相关函数计算完成,总耗时 591.64 秒
[2025-08-07 17:27:11] 计算傅里叶变换...
[2025-08-07 17:27:11] 自旋结构因子计算完成
[2025-08-07 17:27:12] 自旋相关函数平均误差: 0.000660
