[2025-08-08 02:28:17] 使用checkpoint文件: results/L=4/J2=0.00/J1=0.06/training/checkpoints/checkpoint_iter_000600.pkl
[2025-08-08 02:28:25] ✓ 从checkpoint加载参数: 600
[2025-08-08 02:28:25]   - 能量: -55.509159-0.000403j ± 0.084954
[2025-08-08 02:28:25] ================================================================================
[2025-08-08 02:28:25] 加载量子态: L=4, J2=0.00, J1=0.06, checkpoint=checkpoint_iter_000600
[2025-08-08 02:28:25] 设置样本数为: 1048576
[2025-08-08 02:28:25] 开始生成共享样本集...
[2025-08-08 02:30:57] 样本生成完成,耗时: 151.501 秒
[2025-08-08 02:30:57] ================================================================================
[2025-08-08 02:30:57] 开始计算自旋结构因子...
[2025-08-08 02:30:57] 初始化操作符缓存...
[2025-08-08 02:30:57] 预构建所有自旋相关操作符...
[2025-08-08 02:30:57] 开始计算自旋相关函数...
[2025-08-08 02:31:07] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 10.290s
[2025-08-08 02:31:20] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 13.265s
[2025-08-08 02:31:29] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 8.150s
[2025-08-08 02:31:37] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 8.132s
[2025-08-08 02:31:45] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 8.151s
[2025-08-08 02:31:53] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 8.140s
[2025-08-08 02:32:01] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 8.128s
[2025-08-08 02:32:09] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 8.150s
[2025-08-08 02:32:17] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 8.132s
[2025-08-08 02:32:26] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 8.131s
[2025-08-08 02:32:34] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 8.149s
[2025-08-08 02:32:42] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 8.131s
[2025-08-08 02:32:50] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 8.147s
[2025-08-08 02:32:58] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 8.132s
[2025-08-08 02:33:06] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 8.150s
[2025-08-08 02:33:14] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 8.130s
[2025-08-08 02:33:23] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 8.151s
[2025-08-08 02:33:31] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 8.144s
[2025-08-08 02:33:39] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 8.128s
[2025-08-08 02:33:47] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 8.147s
[2025-08-08 02:33:55] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 8.132s
[2025-08-08 02:34:03] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 8.131s
[2025-08-08 02:34:11] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 8.149s
[2025-08-08 02:34:20] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 8.130s
[2025-08-08 02:34:28] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 8.136s
[2025-08-08 02:34:36] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 8.150s
[2025-08-08 02:34:44] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 8.127s
[2025-08-08 02:34:52] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 8.149s
[2025-08-08 02:35:00] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 8.131s
[2025-08-08 02:35:08] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 8.132s
[2025-08-08 02:35:17] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 8.151s
[2025-08-08 02:35:25] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 8.150s
[2025-08-08 02:35:33] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 8.131s
[2025-08-08 02:35:41] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 8.132s
[2025-08-08 02:35:49] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 8.149s
[2025-08-08 02:35:57] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 8.131s
[2025-08-08 02:36:05] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 8.150s
[2025-08-08 02:36:14] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 8.151s
[2025-08-08 02:36:22] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 8.132s
[2025-08-08 02:36:30] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 8.149s
[2025-08-08 02:36:38] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 8.132s
[2025-08-08 02:36:46] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 8.128s
[2025-08-08 02:36:54] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 8.150s
[2025-08-08 02:37:02] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 8.131s
[2025-08-08 02:37:11] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 8.151s
[2025-08-08 02:37:19] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 8.147s
[2025-08-08 02:37:27] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 8.132s
[2025-08-08 02:37:35] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 8.150s
[2025-08-08 02:37:43] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 8.130s
[2025-08-08 02:37:51] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 8.128s
[2025-08-08 02:37:59] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 8.143s
[2025-08-08 02:38:08] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 8.131s
[2025-08-08 02:38:16] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 8.128s
[2025-08-08 02:38:24] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 8.132s
[2025-08-08 02:38:32] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 8.151s
[2025-08-08 02:38:40] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 8.132s
[2025-08-08 02:38:48] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 8.149s
[2025-08-08 02:38:56] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 8.148s
[2025-08-08 02:39:05] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 8.129s
[2025-08-08 02:39:13] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 8.150s
[2025-08-08 02:39:21] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 8.131s
[2025-08-08 02:39:29] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 8.132s
[2025-08-08 02:39:37] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 8.139s
[2025-08-08 02:39:45] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 8.132s
[2025-08-08 02:39:45] 自旋相关函数计算完成,总耗时 528.27 秒
[2025-08-08 02:39:45] 计算傅里叶变换...
[2025-08-08 02:39:46] 自旋结构因子计算完成
[2025-08-08 02:39:47] 自旋相关函数平均误差: 0.000657
