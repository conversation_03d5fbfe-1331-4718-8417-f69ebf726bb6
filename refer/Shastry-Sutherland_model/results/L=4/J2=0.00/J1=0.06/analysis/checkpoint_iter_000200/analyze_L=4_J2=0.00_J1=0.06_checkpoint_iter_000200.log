[2025-08-08 01:41:24] 使用checkpoint文件: results/L=4/J2=0.00/J1=0.06/training/checkpoints/checkpoint_iter_000200.pkl
[2025-08-08 01:41:32] ✓ 从checkpoint加载参数: 200
[2025-08-08 01:41:32]   - 能量: -55.361547+0.006741j ± 0.085186
[2025-08-08 01:41:32] ================================================================================
[2025-08-08 01:41:32] 加载量子态: L=4, J2=0.00, J1=0.06, checkpoint=checkpoint_iter_000200
[2025-08-08 01:41:32] 设置样本数为: 1048576
[2025-08-08 01:41:32] 开始生成共享样本集...
[2025-08-08 01:44:04] 样本生成完成,耗时: 152.130 秒
[2025-08-08 01:44:04] ================================================================================
[2025-08-08 01:44:04] 开始计算自旋结构因子...
[2025-08-08 01:44:04] 初始化操作符缓存...
[2025-08-08 01:44:04] 预构建所有自旋相关操作符...
[2025-08-08 01:44:05] 开始计算自旋相关函数...
[2025-08-08 01:44:15] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 10.274s
[2025-08-08 01:44:28] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 13.647s
[2025-08-08 01:44:37] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 8.245s
[2025-08-08 01:44:45] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 8.232s
[2025-08-08 01:44:53] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 8.249s
[2025-08-08 01:45:01] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 8.243s
[2025-08-08 01:45:10] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 8.233s
[2025-08-08 01:45:18] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 8.245s
[2025-08-08 01:45:26] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 8.232s
[2025-08-08 01:45:34] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 8.232s
[2025-08-08 01:45:43] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 8.245s
[2025-08-08 01:45:51] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 8.232s
[2025-08-08 01:45:59] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 8.244s
[2025-08-08 01:46:07] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 8.234s
[2025-08-08 01:46:16] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 8.246s
[2025-08-08 01:46:24] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 8.233s
[2025-08-08 01:46:32] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 8.247s
[2025-08-08 01:46:40] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 8.244s
[2025-08-08 01:46:49] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 8.232s
[2025-08-08 01:46:57] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 8.245s
[2025-08-08 01:47:05] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 8.232s
[2025-08-08 01:47:13] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 8.233s
[2025-08-08 01:47:22] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 8.245s
[2025-08-08 01:47:30] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 8.233s
[2025-08-08 01:47:38] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 8.243s
[2025-08-08 01:47:46] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 8.245s
[2025-08-08 01:47:54] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 8.233s
[2025-08-08 01:48:03] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 8.245s
[2025-08-08 01:48:11] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 8.232s
[2025-08-08 01:48:19] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 8.231s
[2025-08-08 01:48:27] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 8.249s
[2025-08-08 01:48:36] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 8.246s
[2025-08-08 01:48:44] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 8.233s
[2025-08-08 01:48:52] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 8.233s
[2025-08-08 01:49:00] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 8.244s
[2025-08-08 01:49:09] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 8.231s
[2025-08-08 01:49:17] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 8.245s
[2025-08-08 01:49:25] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 8.249s
[2025-08-08 01:49:33] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 8.231s
[2025-08-08 01:49:42] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 8.244s
[2025-08-08 01:49:50] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 8.232s
[2025-08-08 01:49:58] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 8.232s
[2025-08-08 01:50:06] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 8.253s
[2025-08-08 01:50:15] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 8.233s
[2025-08-08 01:50:23] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 8.249s
[2025-08-08 01:50:31] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 8.244s
[2025-08-08 01:50:39] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 8.232s
[2025-08-08 01:50:48] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 8.246s
[2025-08-08 01:50:56] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 8.233s
[2025-08-08 01:51:04] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 8.232s
[2025-08-08 01:51:12] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 8.244s
[2025-08-08 01:51:21] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 8.233s
[2025-08-08 01:51:29] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 8.331s
[2025-08-08 01:51:37] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 8.232s
[2025-08-08 01:51:45] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 8.247s
[2025-08-08 01:51:54] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 8.234s
[2025-08-08 01:52:02] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 8.276s
[2025-08-08 01:52:10] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 8.245s
[2025-08-08 01:52:18] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 8.233s
[2025-08-08 01:52:27] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 8.246s
[2025-08-08 01:52:35] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 8.232s
[2025-08-08 01:52:43] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 8.232s
[2025-08-08 01:52:51] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 8.243s
[2025-08-08 01:52:59] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 8.233s
[2025-08-08 01:53:00] 自旋相关函数计算完成,总耗时 534.96 秒
[2025-08-08 01:53:00] 计算傅里叶变换...
[2025-08-08 01:53:00] 自旋结构因子计算完成
[2025-08-08 01:53:01] 自旋相关函数平均误差: 0.000653
