[2025-08-23 13:44:52] 使用checkpoint文件: results/L=4/J2=0.00/J1=0.09/training/checkpoints/checkpoint_iter_000900.pkl
[2025-08-23 13:45:02] ✓ 从checkpoint加载参数: 900
[2025-08-23 13:45:02]   - 能量: -56.561587-0.001229j ± 0.085250
[2025-08-23 13:45:02] ================================================================================
[2025-08-23 13:45:02] 加载量子态: L=4, J2=0.00, J1=0.09, checkpoint=checkpoint_iter_000900
[2025-08-23 13:45:02] 设置样本数为: 1048576
[2025-08-23 13:45:02] 开始生成共享样本集...
[2025-08-23 13:46:25] 样本生成完成,耗时: 82.746 秒
[2025-08-23 13:46:25] ================================================================================
[2025-08-23 13:46:25] 开始计算自旋结构因子...
[2025-08-23 13:46:25] 初始化操作符缓存...
[2025-08-23 13:46:25] 预构建所有自旋相关操作符...
[2025-08-23 13:46:25] 开始计算自旋相关函数...
[2025-08-23 13:46:33] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.815s
[2025-08-23 13:46:42] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.928s
[2025-08-23 13:46:46] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 4.249s
[2025-08-23 13:46:50] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 4.271s
[2025-08-23 13:46:55] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 4.281s
[2025-08-23 13:46:59] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 4.269s
[2025-08-23 13:47:03] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 4.250s
[2025-08-23 13:47:08] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 4.282s
[2025-08-23 13:47:12] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 4.248s
[2025-08-23 13:47:16] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 4.281s
[2025-08-23 13:47:20] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 4.256s
[2025-08-23 13:47:25] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 4.281s
[2025-08-23 13:47:29] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 4.249s
[2025-08-23 13:47:33] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 4.272s
[2025-08-23 13:47:37] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 4.281s
[2025-08-23 13:47:42] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 4.250s
[2025-08-23 13:47:46] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 4.269s
[2025-08-23 13:47:50] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 4.281s
[2025-08-23 13:47:55] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 4.250s
[2025-08-23 13:47:59] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 4.283s
[2025-08-23 13:48:03] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.271s
[2025-08-23 13:48:07] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.283s
[2025-08-23 13:48:12] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 4.250s
[2025-08-23 13:48:16] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 4.270s
[2025-08-23 13:48:20] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 4.253s
[2025-08-23 13:48:24] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 4.271s
[2025-08-23 13:48:29] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 4.250s
[2025-08-23 13:48:33] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 4.283s
[2025-08-23 13:48:37] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 4.253s
[2025-08-23 13:48:42] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 4.250s
[2025-08-23 13:48:46] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 4.251s
[2025-08-23 13:48:50] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 4.270s
[2025-08-23 13:48:54] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 4.265s
[2025-08-23 13:48:59] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 4.269s
[2025-08-23 13:49:03] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 4.252s
[2025-08-23 13:49:07] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 4.268s
[2025-08-23 13:49:11] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 4.251s
[2025-08-23 13:49:16] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 4.267s
[2025-08-23 13:49:20] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 4.251s
[2025-08-23 13:49:24] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 4.282s
[2025-08-23 13:49:28] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 4.250s
[2025-08-23 13:49:33] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 4.253s
[2025-08-23 13:49:37] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 4.251s
[2025-08-23 13:49:41] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 4.271s
[2025-08-23 13:49:46] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 4.250s
[2025-08-23 13:49:50] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 4.251s
[2025-08-23 13:49:54] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 4.282s
[2025-08-23 13:49:58] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.251s
[2025-08-23 13:50:03] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.281s
[2025-08-23 13:50:07] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.283s
[2025-08-23 13:50:11] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.251s
[2025-08-23 13:50:15] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 4.283s
[2025-08-23 13:50:20] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 4.252s
[2025-08-23 13:50:24] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.270s
[2025-08-23 13:50:28] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.249s
[2025-08-23 13:50:32] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.283s
[2025-08-23 13:50:37] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.251s
[2025-08-23 13:50:41] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 4.250s
[2025-08-23 13:50:45] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 4.250s
[2025-08-23 13:50:50] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 4.282s
[2025-08-23 13:50:54] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.251s
[2025-08-23 13:50:58] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 4.250s
[2025-08-23 13:51:02] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.281s
[2025-08-23 13:51:07] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 4.251s
[2025-08-23 13:51:07] 自旋相关函数计算完成,总耗时 281.40 秒
[2025-08-23 13:51:07] 计算傅里叶变换...
[2025-08-23 13:51:08] 自旋结构因子计算完成
[2025-08-23 13:51:08] 自旋相关函数平均误差: 0.000649
