[2025-08-26 13:13:04] 使用checkpoint文件: results/L=4/J2=1.00/J1=0.78/training/checkpoints/checkpoint_iter_000700.pkl
[2025-08-26 13:13:15] ✓ 从checkpoint加载参数: 700
[2025-08-26 13:13:15]   - 能量: -27.952946-0.001964j ± 0.007209
[2025-08-26 13:13:15] ================================================================================
[2025-08-26 13:13:15] 加载量子态: L=4, J2=1.00, J1=0.78, checkpoint=checkpoint_iter_000700
[2025-08-26 13:13:15] 设置样本数为: 1048576
[2025-08-26 13:13:15] 开始生成共享样本集...
[2025-08-26 13:14:37] 样本生成完成,耗时: 82.718 秒
[2025-08-26 13:14:37] ================================================================================
[2025-08-26 13:14:37] 开始计算自旋结构因子...
[2025-08-26 13:14:37] 初始化操作符缓存...
[2025-08-26 13:14:37] 预构建所有自旋相关操作符...
[2025-08-26 13:14:38] 开始计算自旋相关函数...
[2025-08-26 13:14:45] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.505s
[2025-08-26 13:14:54] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.903s
[2025-08-26 13:14:58] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 4.242s
[2025-08-26 13:15:02] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 4.263s
[2025-08-26 13:15:07] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 4.280s
[2025-08-26 13:15:11] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 4.261s
[2025-08-26 13:15:15] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 4.241s
[2025-08-26 13:15:20] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 4.282s
[2025-08-26 13:15:24] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 4.242s
[2025-08-26 13:15:28] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 4.283s
[2025-08-26 13:15:32] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 4.258s
[2025-08-26 13:15:37] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 4.280s
[2025-08-26 13:15:41] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 4.243s
[2025-08-26 13:15:45] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 4.261s
[2025-08-26 13:15:49] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 4.279s
[2025-08-26 13:15:54] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 4.249s
[2025-08-26 13:15:58] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 4.259s
[2025-08-26 13:16:02] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 4.283s
[2025-08-26 13:16:06] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 4.244s
[2025-08-26 13:16:11] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 4.282s
[2025-08-26 13:16:15] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.262s
[2025-08-26 13:16:19] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.281s
[2025-08-26 13:16:24] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 4.243s
[2025-08-26 13:16:28] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 4.261s
[2025-08-26 13:16:32] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 4.251s
[2025-08-26 13:16:36] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 4.262s
[2025-08-26 13:16:41] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 4.244s
[2025-08-26 13:16:45] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 4.283s
[2025-08-26 13:16:49] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 4.252s
[2025-08-26 13:16:53] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 4.242s
[2025-08-26 13:16:58] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 4.243s
[2025-08-26 13:17:02] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 4.259s
[2025-08-26 13:17:06] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 4.259s
[2025-08-26 13:17:10] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 4.260s
[2025-08-26 13:17:15] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 4.250s
[2025-08-26 13:17:19] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 4.261s
[2025-08-26 13:17:23] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 4.244s
[2025-08-26 13:17:27] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 4.261s
[2025-08-26 13:17:32] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 4.244s
[2025-08-26 13:17:36] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 4.281s
[2025-08-26 13:17:40] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 4.243s
[2025-08-26 13:17:45] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 4.250s
[2025-08-26 13:17:49] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 4.243s
[2025-08-26 13:17:53] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 4.263s
[2025-08-26 13:17:57] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 4.243s
[2025-08-26 13:18:02] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 4.243s
[2025-08-26 13:18:06] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 4.280s
[2025-08-26 13:18:10] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.244s
[2025-08-26 13:18:14] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.280s
[2025-08-26 13:18:19] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.282s
[2025-08-26 13:18:23] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.242s
[2025-08-26 13:18:27] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 4.282s
[2025-08-26 13:18:31] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 4.250s
[2025-08-26 13:18:36] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.262s
[2025-08-26 13:18:40] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.243s
[2025-08-26 13:18:44] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.283s
[2025-08-26 13:18:48] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.242s
[2025-08-26 13:18:53] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 4.241s
[2025-08-26 13:18:57] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 4.243s
[2025-08-26 13:19:01] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 4.282s
[2025-08-26 13:19:06] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.249s
[2025-08-26 13:19:10] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 4.242s
[2025-08-26 13:19:14] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.281s
[2025-08-26 13:19:18] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 4.249s
[2025-08-26 13:19:18] 自旋相关函数计算完成,总耗时 280.80 秒
[2025-08-26 13:19:18] 计算傅里叶变换...
[2025-08-26 13:19:19] 自旋结构因子计算完成
[2025-08-26 13:19:20] 自旋相关函数平均误差: 0.000552
