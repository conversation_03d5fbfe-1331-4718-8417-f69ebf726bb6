[2025-08-26 15:55:00] 使用checkpoint文件: results/L=4/J2=1.00/J1=0.80/training/checkpoints/final_GCNN.pkl
[2025-08-26 15:55:11] ✓ 从checkpoint加载参数: final
[2025-08-26 15:55:11]   - 能量: -28.760945-0.000622j ± 0.003109
[2025-08-26 15:55:11] ================================================================================
[2025-08-26 15:55:11] 加载量子态: L=4, J2=1.00, J1=0.80, checkpoint=final_GCNN
[2025-08-26 15:55:11] 设置样本数为: 1048576
[2025-08-26 15:55:11] 开始生成共享样本集...
[2025-08-26 15:56:34] 样本生成完成,耗时: 82.783 秒
[2025-08-26 15:56:34] ================================================================================
[2025-08-26 15:56:34] 开始计算自旋结构因子...
[2025-08-26 15:56:34] 初始化操作符缓存...
[2025-08-26 15:56:34] 预构建所有自旋相关操作符...
[2025-08-26 15:56:34] 开始计算自旋相关函数...
[2025-08-26 15:56:41] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.708s
[2025-08-26 15:56:50] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.952s
[2025-08-26 15:56:55] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 4.243s
[2025-08-26 15:56:59] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 4.260s
[2025-08-26 15:57:03] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 4.277s
[2025-08-26 15:57:07] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 4.258s
[2025-08-26 15:57:12] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 4.240s
[2025-08-26 15:57:16] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 4.278s
[2025-08-26 15:57:20] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 4.240s
[2025-08-26 15:57:24] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 4.279s
[2025-08-26 15:57:29] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 4.257s
[2025-08-26 15:57:33] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 4.277s
[2025-08-26 15:57:37] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 4.242s
[2025-08-26 15:57:42] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 4.261s
[2025-08-26 15:57:46] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 4.277s
[2025-08-26 15:57:50] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 4.245s
[2025-08-26 15:57:54] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 4.258s
[2025-08-26 15:57:59] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 4.277s
[2025-08-26 15:58:03] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 4.242s
[2025-08-26 15:58:07] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 4.278s
[2025-08-26 15:58:11] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.259s
[2025-08-26 15:58:16] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.278s
[2025-08-26 15:58:20] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 4.242s
[2025-08-26 15:58:24] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 4.259s
[2025-08-26 15:58:28] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 4.246s
[2025-08-26 15:58:33] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 4.259s
[2025-08-26 15:58:37] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 4.242s
[2025-08-26 15:58:41] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 4.280s
[2025-08-26 15:58:45] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 4.251s
[2025-08-26 15:58:50] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 4.240s
[2025-08-26 15:58:54] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 4.242s
[2025-08-26 15:58:58] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 4.257s
[2025-08-26 15:59:03] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 4.257s
[2025-08-26 15:59:07] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 4.258s
[2025-08-26 15:59:11] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 4.247s
[2025-08-26 15:59:15] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 4.259s
[2025-08-26 15:59:20] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 4.244s
[2025-08-26 15:59:24] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 4.259s
[2025-08-26 15:59:28] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 4.243s
[2025-08-26 15:59:32] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 4.277s
[2025-08-26 15:59:37] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 4.242s
[2025-08-26 15:59:41] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 4.248s
[2025-08-26 15:59:45] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 4.242s
[2025-08-26 15:59:49] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 4.260s
[2025-08-26 15:59:54] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 4.242s
[2025-08-26 15:59:58] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 4.242s
[2025-08-26 16:00:02] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 4.277s
[2025-08-26 16:00:06] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.242s
[2025-08-26 16:00:11] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.276s
[2025-08-26 16:00:15] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.277s
[2025-08-26 16:00:19] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.242s
[2025-08-26 16:00:23] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 4.278s
[2025-08-26 16:00:28] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 4.245s
[2025-08-26 16:00:32] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.259s
[2025-08-26 16:00:36] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.241s
[2025-08-26 16:00:40] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.279s
[2025-08-26 16:00:45] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.241s
[2025-08-26 16:00:49] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 4.240s
[2025-08-26 16:00:53] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 4.241s
[2025-08-26 16:00:57] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 4.278s
[2025-08-26 16:01:02] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.244s
[2025-08-26 16:01:06] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 4.242s
[2025-08-26 16:01:10] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.277s
[2025-08-26 16:01:15] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 4.245s
[2025-08-26 16:01:15] 自旋相关函数计算完成,总耗时 280.84 秒
[2025-08-26 16:01:15] 计算傅里叶变换...
[2025-08-26 16:01:15] 自旋结构因子计算完成
[2025-08-26 16:01:16] 自旋相关函数平均误差: 0.000549
