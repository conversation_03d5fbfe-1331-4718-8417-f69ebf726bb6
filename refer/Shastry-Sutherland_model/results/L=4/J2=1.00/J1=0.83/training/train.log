[2025-08-25 21:50:12] ✓ 从checkpoint恢复: results/L=4/J2=1.00/J1=0.82/training/checkpoints/final_GCNN.pkl
[2025-08-25 21:50:12]   - 迭代次数: final
[2025-08-25 21:50:12]   - 能量: -29.583995-0.001544j ± 0.005818
[2025-08-25 21:50:12]   - 时间戳: 2025-08-25T21:49:38.868096+08:00
[2025-08-25 21:50:24] ✓ 变分状态参数已从checkpoint恢复
[2025-08-25 21:50:24] ✓ 从final状态恢复, 重置迭代计数为0
[2025-08-25 21:50:24] ==================================================
[2025-08-25 21:50:24] GCNN for Shastry-Sutherland Model
[2025-08-25 21:50:24] ==================================================
[2025-08-25 21:50:24] System parameters:
[2025-08-25 21:50:24]   - System size: L=4, N=64
[2025-08-25 21:50:24]   - System parameters: J1=0.83, J2=1.0, Q=0.0
[2025-08-25 21:50:24] --------------------------------------------------
[2025-08-25 21:50:24] Model parameters:
[2025-08-25 21:50:24]   - Number of layers = 4
[2025-08-25 21:50:24]   - Number of features = 4
[2025-08-25 21:50:24]   - Total parameters = 12572
[2025-08-25 21:50:24] --------------------------------------------------
[2025-08-25 21:50:24] Training parameters:
[2025-08-25 21:50:24]   - Learning rate: 0.01
[2025-08-25 21:50:24]   - Total iterations: 1050
[2025-08-25 21:50:24]   - Annealing cycles: 3
[2025-08-25 21:50:24]   - Initial period: 150
[2025-08-25 21:50:24]   - Period multiplier: 2.0
[2025-08-25 21:50:24]   - Temperature range: 0.0-1.0
[2025-08-25 21:50:24]   - Samples: 4096
[2025-08-25 21:50:24]   - Discarded samples: 0
[2025-08-25 21:50:24]   - Chunk size: 2048
[2025-08-25 21:50:24]   - Diagonal shift: 0.2
[2025-08-25 21:50:24]   - Gradient clipping: 1.0
[2025-08-25 21:50:24]   - Checkpoint enabled: interval=100
[2025-08-25 21:50:24]   - Checkpoint directory: results/L=4/J2=1.00/J1=0.83/training/checkpoints
[2025-08-25 21:50:24] --------------------------------------------------
[2025-08-25 21:50:24] Device status:
[2025-08-25 21:50:24]   - Devices model: NVIDIA H200 NVL
[2025-08-25 21:50:24]   - Number of devices: 1
[2025-08-25 21:50:24]   - Sharding: True
[2025-08-25 21:50:24] ============================================================
[2025-08-25 21:51:04] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -30.002418+0.003411j
[2025-08-25 21:51:28] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -29.989506-0.003059j
[2025-08-25 21:51:34] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -29.996219-0.000721j
[2025-08-25 21:51:39] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -29.995425+0.004966j
[2025-08-25 21:51:45] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -29.998389-0.000770j
[2025-08-25 21:51:51] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -30.008366-0.000094j
[2025-08-25 21:51:57] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -29.994834-0.000979j
[2025-08-25 21:52:03] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -29.989321-0.001627j
[2025-08-25 21:52:08] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -29.992432-0.001497j
[2025-08-25 21:52:14] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -30.005255-0.000133j
[2025-08-25 21:52:20] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -29.995796+0.002752j
[2025-08-25 21:52:26] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -29.992791+0.003970j
[2025-08-25 21:52:32] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -29.995165-0.000452j
[2025-08-25 21:52:37] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -29.999545+0.000639j
[2025-08-25 21:52:43] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -29.997131-0.001762j
[2025-08-25 21:52:49] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -29.994820-0.003610j
[2025-08-25 21:52:55] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -29.989904+0.000064j
[2025-08-25 21:53:01] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -29.992910-0.000114j
[2025-08-25 21:53:07] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -29.990215+0.000756j
[2025-08-25 21:53:12] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -29.993407+0.001225j
[2025-08-25 21:53:18] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -30.000409+0.000916j
[2025-08-25 21:53:24] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -30.004585-0.001771j
[2025-08-25 21:53:30] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -29.999708-0.004124j
[2025-08-25 21:53:36] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -29.999872-0.001494j
[2025-08-25 21:53:41] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -29.997754-0.001157j
[2025-08-25 21:53:47] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -29.998147+0.002336j
[2025-08-25 21:53:53] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -29.996924-0.005307j
[2025-08-25 21:53:59] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -30.003814-0.000492j
[2025-08-25 21:54:05] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -30.000208+0.000659j
[2025-08-25 21:54:10] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -29.989058+0.000753j
[2025-08-25 21:54:16] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -29.995431+0.000662j
[2025-08-25 21:54:22] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -29.996606-0.001481j
[2025-08-25 21:54:28] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -29.997991-0.003463j
[2025-08-25 21:54:34] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -30.013733-0.001833j
[2025-08-25 21:54:39] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -29.992636+0.000591j
[2025-08-25 21:54:45] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -29.999323-0.000455j
[2025-08-25 21:54:51] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -29.991290-0.001561j
[2025-08-25 21:54:57] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -29.997598+0.000096j
[2025-08-25 21:55:03] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -30.001298-0.002649j
[2025-08-25 21:55:09] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -29.995771+0.004494j
[2025-08-25 21:55:14] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -29.989074-0.000209j
[2025-08-25 21:55:20] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -29.990398+0.003825j
[2025-08-25 21:55:26] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -30.001580+0.000848j
[2025-08-25 21:55:32] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -29.993443+0.001241j
[2025-08-25 21:55:38] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -29.997428-0.000273j
[2025-08-25 21:55:43] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -30.006864-0.002090j
[2025-08-25 21:55:49] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -29.986152+0.001037j
[2025-08-25 21:55:55] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -30.001485+0.001325j
[2025-08-25 21:56:01] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -30.001658-0.002519j
[2025-08-25 21:56:07] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -29.997897-0.001266j
[2025-08-25 21:56:12] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -30.007163+0.000558j
[2025-08-25 21:56:18] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -30.001608+0.002073j
[2025-08-25 21:56:24] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -30.001108+0.000902j
[2025-08-25 21:56:30] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -29.997976+0.002338j
[2025-08-25 21:56:36] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -29.996301+0.002055j
[2025-08-25 21:56:41] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -30.002669+0.002970j
[2025-08-25 21:56:47] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -30.009133-0.001312j
[2025-08-25 21:56:53] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -29.988905+0.001166j
[2025-08-25 21:56:59] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -30.003685-0.001375j
[2025-08-25 21:57:05] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -30.002118-0.002117j
[2025-08-25 21:57:10] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -29.995485+0.001463j
[2025-08-25 21:57:16] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -30.008868-0.002653j
[2025-08-25 21:57:22] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -30.007001+0.001784j
[2025-08-25 21:57:28] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -29.988578+0.002039j
[2025-08-25 21:57:34] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -30.002737+0.000334j
[2025-08-25 21:57:39] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -30.001931-0.000715j
[2025-08-25 21:57:45] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -30.000646+0.001306j
[2025-08-25 21:57:51] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -29.999645+0.002740j
[2025-08-25 21:57:57] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -30.005529+0.000903j
[2025-08-25 21:58:03] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -30.006592+0.000063j
[2025-08-25 21:58:08] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -29.996027-0.003665j
[2025-08-25 21:58:14] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -29.993684-0.000208j
[2025-08-25 21:58:20] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -30.006006-0.001073j
[2025-08-25 21:58:26] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -30.010761-0.001140j
[2025-08-25 21:58:32] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -29.992182+0.001308j
[2025-08-25 21:58:37] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -30.002973-0.000796j
[2025-08-25 21:58:43] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -29.994040+0.001252j
[2025-08-25 21:58:49] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -30.001620+0.000029j
[2025-08-25 21:58:55] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -29.998450-0.002905j
[2025-08-25 21:59:01] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -29.989940+0.000366j
[2025-08-25 21:59:06] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -30.003119-0.000459j
[2025-08-25 21:59:12] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -29.999578-0.000580j
[2025-08-25 21:59:18] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -30.009363+0.000926j
[2025-08-25 21:59:23] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -29.998918+0.001537j
[2025-08-25 21:59:29] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -29.994872+0.000893j
[2025-08-25 21:59:35] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -30.001562+0.000991j
[2025-08-25 21:59:41] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -29.996216+0.000175j
[2025-08-25 21:59:47] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -30.000265+0.001422j
[2025-08-25 21:59:52] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -30.002819-0.001802j
[2025-08-25 21:59:58] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -30.004532+0.002673j
[2025-08-25 22:00:04] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -29.993247+0.000003j
[2025-08-25 22:00:10] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -29.995337-0.001509j
[2025-08-25 22:00:16] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -29.996925+0.002502j
[2025-08-25 22:00:21] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -29.994097-0.005134j
[2025-08-25 22:00:27] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -29.988264-0.000412j
[2025-08-25 22:00:33] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -30.000488+0.001645j
[2025-08-25 22:00:39] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -29.994815-0.000220j
[2025-08-25 22:00:45] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -30.000788-0.002213j
[2025-08-25 22:00:50] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -29.998850+0.000942j
[2025-08-25 22:00:56] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -29.996781-0.003700j
[2025-08-25 22:00:56] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-08-25 22:01:02] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -29.997362+0.000744j
[2025-08-25 22:01:08] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -29.991456+0.001700j
[2025-08-25 22:01:14] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -29.990963-0.001489j
[2025-08-25 22:01:19] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -29.993348+0.000810j
[2025-08-25 22:01:25] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -29.991312+0.000518j
[2025-08-25 22:01:31] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -29.994273-0.000958j
[2025-08-25 22:01:37] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -30.004837-0.000551j
[2025-08-25 22:01:43] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -29.994930-0.003231j
[2025-08-25 22:01:48] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -30.003682+0.001424j
[2025-08-25 22:01:54] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -30.001355+0.002860j
[2025-08-25 22:02:00] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -30.001046-0.000709j
[2025-08-25 22:02:06] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -30.003402+0.000295j
[2025-08-25 22:02:12] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -30.011795-0.001475j
[2025-08-25 22:02:17] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -29.996789-0.000589j
[2025-08-25 22:02:23] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -30.000167+0.001170j
[2025-08-25 22:02:29] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -30.006290+0.000523j
[2025-08-25 22:02:35] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -29.996468-0.000245j
[2025-08-25 22:02:41] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -29.993844+0.003463j
[2025-08-25 22:02:47] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -29.992878-0.000371j
[2025-08-25 22:02:52] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -29.993056-0.001154j
[2025-08-25 22:02:58] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -29.991792-0.000512j
[2025-08-25 22:03:04] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -29.995015-0.000990j
[2025-08-25 22:03:10] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -30.007790-0.000708j
[2025-08-25 22:03:16] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -29.996183-0.003494j
[2025-08-25 22:03:21] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -29.987090+0.000474j
[2025-08-25 22:03:27] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -29.999536-0.002246j
[2025-08-25 22:03:33] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -29.997309+0.000078j
[2025-08-25 22:03:39] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -29.994563-0.003041j
[2025-08-25 22:03:45] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -29.997860-0.001812j
[2025-08-25 22:03:50] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -29.997627-0.000924j
[2025-08-25 22:03:56] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -29.998578+0.006090j
[2025-08-25 22:04:02] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -29.998659+0.003964j
[2025-08-25 22:04:07] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -29.999254+0.003332j
[2025-08-25 22:04:13] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -30.002362-0.001090j
[2025-08-25 22:04:19] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -29.999996+0.000469j
[2025-08-25 22:04:25] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -30.006442+0.000131j
[2025-08-25 22:04:31] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -29.993832-0.004797j
[2025-08-25 22:04:36] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -29.999384+0.004498j
[2025-08-25 22:04:42] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -29.997249-0.002975j
[2025-08-25 22:04:48] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -30.005196+0.003263j
[2025-08-25 22:04:54] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -29.996981-0.001601j
[2025-08-25 22:05:00] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -29.989643+0.003531j
[2025-08-25 22:05:05] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -29.994854+0.001913j
[2025-08-25 22:05:11] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -30.002145-0.002620j
[2025-08-25 22:05:17] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -29.998497+0.000733j
[2025-08-25 22:05:23] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -30.003800+0.001273j
[2025-08-25 22:05:29] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -30.001938+0.000702j
[2025-08-25 22:05:34] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -29.993490+0.000369j
[2025-08-25 22:05:40] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -30.004909-0.000355j
[2025-08-25 22:05:46] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -29.999688+0.000774j
[2025-08-25 22:05:46] RESTART #1 | Period: 300
[2025-08-25 22:05:52] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -30.003233+0.000925j
[2025-08-25 22:05:58] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -30.004958-0.001164j
[2025-08-25 22:06:04] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -30.003633-0.001222j
[2025-08-25 22:06:09] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -30.002377-0.002038j
[2025-08-25 22:06:15] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -29.994952-0.001071j
[2025-08-25 22:06:21] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -30.001357-0.004234j
[2025-08-25 22:06:27] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -29.997125-0.005503j
[2025-08-25 22:06:33] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -30.000995+0.001917j
[2025-08-25 22:06:39] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -29.997395-0.002965j
[2025-08-25 22:06:44] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -29.986267+0.000880j
[2025-08-25 22:06:50] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -30.003699+0.000396j
[2025-08-25 22:06:56] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -29.997995-0.001917j
[2025-08-25 22:07:02] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -29.995070+0.002998j
[2025-08-25 22:07:08] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -29.994359-0.000078j
[2025-08-25 22:07:13] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -29.996851+0.002802j
[2025-08-25 22:07:19] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -30.001201+0.000350j
[2025-08-25 22:07:25] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -30.007648-0.002784j
[2025-08-25 22:07:31] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -30.006535+0.000782j
[2025-08-25 22:07:37] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -29.995680-0.000622j
[2025-08-25 22:07:42] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -29.991569+0.000226j
[2025-08-25 22:07:48] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -29.999717-0.000894j
[2025-08-25 22:07:54] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -30.000519-0.003056j
[2025-08-25 22:08:00] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -30.001871-0.002191j
[2025-08-25 22:08:06] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -29.997329+0.001881j
[2025-08-25 22:08:12] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -30.005927+0.001709j
[2025-08-25 22:08:17] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -30.002419+0.002273j
[2025-08-25 22:08:23] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -29.988108+0.000139j
[2025-08-25 22:08:29] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -30.002394-0.000417j
[2025-08-25 22:08:35] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -29.995517-0.001295j
[2025-08-25 22:08:41] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -30.006050-0.000179j
[2025-08-25 22:08:46] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -29.994670+0.002399j
[2025-08-25 22:08:52] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -29.997162-0.003294j
[2025-08-25 22:08:58] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -30.005397-0.002359j
[2025-08-25 22:09:04] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -29.996803+0.002091j
[2025-08-25 22:09:10] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -29.995401+0.001127j
[2025-08-25 22:09:16] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -30.000648+0.000331j
[2025-08-25 22:09:21] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -29.996145+0.002604j
[2025-08-25 22:09:27] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -29.997990-0.000667j
[2025-08-25 22:09:33] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -30.004138-0.002001j
[2025-08-25 22:09:39] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -30.002882+0.002990j
[2025-08-25 22:09:45] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -29.999759+0.003270j
[2025-08-25 22:09:50] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -30.005732-0.000538j
[2025-08-25 22:09:56] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -29.998949+0.002099j
[2025-08-25 22:10:02] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -29.994890+0.000058j
[2025-08-25 22:10:08] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -29.997624+0.000366j
[2025-08-25 22:10:14] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -29.993859+0.003582j
[2025-08-25 22:10:19] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -30.007621+0.001026j
[2025-08-25 22:10:25] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -30.000918+0.003769j
[2025-08-25 22:10:31] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -29.980974+0.001143j
[2025-08-25 22:10:37] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -30.000349+0.002980j
[2025-08-25 22:10:37] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-08-25 22:10:43] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -30.004058+0.000966j
[2025-08-25 22:10:48] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -30.007222+0.002956j
[2025-08-25 22:10:54] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -30.004620-0.000087j
[2025-08-25 22:11:00] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -30.000142-0.002039j
[2025-08-25 22:11:06] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -29.999688-0.001090j
[2025-08-25 22:11:12] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -30.005118-0.000296j
[2025-08-25 22:11:18] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -29.997173+0.002509j
[2025-08-25 22:11:23] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -29.995840-0.001055j
[2025-08-25 22:11:29] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -30.004789-0.001468j
[2025-08-25 22:11:35] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -29.997748-0.001399j
[2025-08-25 22:11:41] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -29.996865-0.003820j
[2025-08-25 22:11:47] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -29.999295+0.001732j
[2025-08-25 22:11:52] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -29.988138-0.000788j
[2025-08-25 22:11:58] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -29.995162+0.001482j
[2025-08-25 22:12:04] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -30.002886-0.001697j
[2025-08-25 22:12:10] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -29.997893-0.000136j
[2025-08-25 22:12:16] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -29.996099-0.000909j
[2025-08-25 22:12:22] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -29.997370+0.003976j
[2025-08-25 22:12:27] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -29.999848-0.001689j
[2025-08-25 22:12:33] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -29.991148+0.001909j
[2025-08-25 22:12:39] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -29.999230+0.000501j
[2025-08-25 22:12:45] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -29.999272-0.000524j
[2025-08-25 22:12:51] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -29.999273+0.002753j
[2025-08-25 22:12:56] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -29.988120+0.002819j
[2025-08-25 22:13:02] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -29.989363-0.000760j
[2025-08-25 22:13:08] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -29.999817-0.000931j
[2025-08-25 22:13:14] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -29.987794-0.001627j
[2025-08-25 22:13:20] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -30.001009-0.003409j
[2025-08-25 22:13:25] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -29.996494-0.004020j
[2025-08-25 22:13:31] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -30.000201-0.000541j
[2025-08-25 22:13:37] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -29.995615+0.001445j
[2025-08-25 22:13:43] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -30.007915+0.003949j
[2025-08-25 22:13:49] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -30.001640+0.000076j
[2025-08-25 22:13:54] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -30.002269+0.000743j
[2025-08-25 22:14:00] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -30.002547-0.000538j
[2025-08-25 22:14:06] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -29.990114+0.000650j
[2025-08-25 22:14:12] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -29.986224-0.000679j
[2025-08-25 22:14:18] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -29.996362-0.002468j
[2025-08-25 22:14:23] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -29.989866-0.000052j
[2025-08-25 22:14:29] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -29.992054+0.002316j
[2025-08-25 22:14:35] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -29.997472+0.001905j
[2025-08-25 22:14:41] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -29.996859-0.000952j
[2025-08-25 22:14:47] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -30.000685+0.001323j
[2025-08-25 22:14:52] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -29.997178+0.002020j
[2025-08-25 22:14:58] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -29.996042-0.000514j
[2025-08-25 22:15:04] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -29.991859+0.000937j
[2025-08-25 22:15:10] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -29.992558-0.001270j
[2025-08-25 22:15:16] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -29.996209-0.000369j
[2025-08-25 22:15:22] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -30.010201+0.000588j
[2025-08-25 22:15:27] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -30.009179+0.000147j
[2025-08-25 22:15:33] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -30.000078-0.000754j
[2025-08-25 22:15:39] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -29.999711+0.000771j
[2025-08-25 22:15:45] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -30.004893-0.007255j
[2025-08-25 22:15:51] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -29.999534+0.001001j
[2025-08-25 22:15:56] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -29.991370-0.000135j
[2025-08-25 22:16:02] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -29.993886+0.001815j
[2025-08-25 22:16:08] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -29.995384-0.002833j
[2025-08-25 22:16:14] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -29.996369-0.002193j
[2025-08-25 22:16:20] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -29.999261+0.000088j
[2025-08-25 22:16:26] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -29.993247+0.000491j
[2025-08-25 22:16:31] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -29.999854+0.004368j
[2025-08-25 22:16:37] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -29.996443-0.001692j
[2025-08-25 22:16:43] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -30.007288+0.003261j
[2025-08-25 22:16:49] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -29.998349-0.000616j
[2025-08-25 22:16:55] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -29.984459-0.000162j
[2025-08-25 22:17:00] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -29.997039+0.001840j
[2025-08-25 22:17:06] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -29.989392-0.001927j
[2025-08-25 22:17:12] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -30.002793+0.001431j
[2025-08-25 22:17:18] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -29.995027+0.000189j
[2025-08-25 22:17:24] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -30.002441+0.001846j
[2025-08-25 22:17:29] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -29.996400+0.002998j
[2025-08-25 22:17:35] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -30.012249+0.001015j
[2025-08-25 22:17:41] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -29.996099+0.001666j
[2025-08-25 22:17:47] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -29.992502-0.004825j
[2025-08-25 22:17:53] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -29.997641+0.000076j
[2025-08-25 22:17:58] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -29.998414+0.001350j
[2025-08-25 22:18:04] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -30.004490-0.000939j
[2025-08-25 22:18:10] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -29.989181+0.001515j
[2025-08-25 22:18:16] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -30.000095+0.001132j
[2025-08-25 22:18:22] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -29.994258-0.001488j
[2025-08-25 22:18:27] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -29.998228-0.000754j
[2025-08-25 22:18:33] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -29.991975-0.001553j
[2025-08-25 22:18:39] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -30.001490+0.001192j
[2025-08-25 22:18:45] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -29.998785-0.002925j
[2025-08-25 22:18:51] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -29.998308-0.001558j
[2025-08-25 22:18:56] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -30.007546-0.002604j
[2025-08-25 22:19:02] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -29.991986-0.001366j
[2025-08-25 22:19:08] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -30.001659-0.000740j
[2025-08-25 22:19:14] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -30.000820+0.000708j
[2025-08-25 22:19:20] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -29.997129+0.001140j
[2025-08-25 22:19:26] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -29.996664+0.002589j
[2025-08-25 22:19:31] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -29.998694+0.001304j
[2025-08-25 22:19:37] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -29.995914+0.001876j
[2025-08-25 22:19:43] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -30.001209-0.006288j
[2025-08-25 22:19:49] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -30.000899-0.000622j
[2025-08-25 22:19:55] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -29.995113-0.000473j
[2025-08-25 22:20:00] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -29.995527-0.000513j
[2025-08-25 22:20:06] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -29.998190-0.000316j
[2025-08-25 22:20:12] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -29.999556-0.001692j
[2025-08-25 22:20:18] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -29.994444-0.001326j
[2025-08-25 22:20:18] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-08-25 22:20:24] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -29.996277-0.000169j
[2025-08-25 22:20:29] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -30.008352-0.002193j
[2025-08-25 22:20:35] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -29.998319-0.001472j
[2025-08-25 22:20:41] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -29.996631-0.000285j
[2025-08-25 22:20:47] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -30.002554+0.000691j
[2025-08-25 22:20:53] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -29.995966+0.001633j
[2025-08-25 22:20:59] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -29.993714+0.003000j
[2025-08-25 22:21:04] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -30.003439-0.001359j
[2025-08-25 22:21:10] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -30.001967-0.001659j
[2025-08-25 22:21:16] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -29.992142-0.003666j
[2025-08-25 22:21:22] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -30.003315+0.000288j
[2025-08-25 22:21:28] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -29.987316-0.002225j
[2025-08-25 22:21:33] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -29.997198-0.000039j
[2025-08-25 22:21:39] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -30.004122+0.002141j
[2025-08-25 22:21:45] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -30.001223-0.001998j
[2025-08-25 22:21:51] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -29.999255+0.002747j
[2025-08-25 22:21:57] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -29.999659-0.001924j
[2025-08-25 22:22:03] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -29.994919-0.002333j
[2025-08-25 22:22:08] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -29.999958-0.002317j
[2025-08-25 22:22:14] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -29.997298+0.001237j
[2025-08-25 22:22:20] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -29.995943+0.001378j
[2025-08-25 22:22:26] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -29.996659+0.000383j
[2025-08-25 22:22:32] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -30.001244-0.001245j
[2025-08-25 22:22:37] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -29.997303-0.001652j
[2025-08-25 22:22:43] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -29.997632+0.001460j
[2025-08-25 22:22:49] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -30.000602-0.003018j
[2025-08-25 22:22:55] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -29.989294-0.002847j
[2025-08-25 22:23:01] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -29.995729-0.001646j
[2025-08-25 22:23:06] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -29.989489+0.000311j
[2025-08-25 22:23:12] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -29.998809-0.001577j
[2025-08-25 22:23:18] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -30.003933-0.000226j
[2025-08-25 22:23:24] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -29.999132-0.001017j
[2025-08-25 22:23:30] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -29.996488-0.004587j
[2025-08-25 22:23:36] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -29.996440+0.001192j
[2025-08-25 22:23:41] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -30.006587-0.002262j
[2025-08-25 22:23:47] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -29.997787-0.002328j
[2025-08-25 22:23:53] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -30.001299+0.002224j
[2025-08-25 22:23:59] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -29.995921-0.000303j
[2025-08-25 22:24:05] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -29.993814+0.000997j
[2025-08-25 22:24:10] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -29.994128+0.000872j
[2025-08-25 22:24:16] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -30.001592-0.003183j
[2025-08-25 22:24:22] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -30.004099-0.001930j
[2025-08-25 22:24:28] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -29.998512+0.000960j
[2025-08-25 22:24:34] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -30.006773-0.002900j
[2025-08-25 22:24:39] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -29.999597-0.000903j
[2025-08-25 22:24:45] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -29.989120+0.003791j
[2025-08-25 22:24:51] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -30.002649+0.001716j
[2025-08-25 22:24:57] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -29.989523-0.001669j
[2025-08-25 22:25:03] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -29.991875+0.001682j
[2025-08-25 22:25:08] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -30.004741+0.000905j
[2025-08-25 22:25:14] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -29.998744+0.000567j
[2025-08-25 22:25:20] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -29.997516+0.001570j
[2025-08-25 22:25:26] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -30.005914+0.002135j
[2025-08-25 22:25:32] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -30.004233+0.002712j
[2025-08-25 22:25:37] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -29.996896-0.002394j
[2025-08-25 22:25:43] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -29.994300+0.000917j
[2025-08-25 22:25:49] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -30.001228+0.000010j
[2025-08-25 22:25:55] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -30.003394-0.001303j
[2025-08-25 22:26:01] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -30.006156-0.000703j
[2025-08-25 22:26:07] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -29.998490+0.002885j
[2025-08-25 22:26:12] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -30.000753-0.004664j
[2025-08-25 22:26:18] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -29.999486-0.000099j
[2025-08-25 22:26:24] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -29.993142-0.000519j
[2025-08-25 22:26:30] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -29.992732+0.002247j
[2025-08-25 22:26:36] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -29.988303-0.001911j
[2025-08-25 22:26:41] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -29.993460+0.000119j
[2025-08-25 22:26:47] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -29.992165-0.000217j
[2025-08-25 22:26:53] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -29.990322-0.003466j
[2025-08-25 22:26:59] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -29.986558+0.000328j
[2025-08-25 22:27:05] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -29.992720+0.001359j
[2025-08-25 22:27:10] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -29.998817-0.002342j
[2025-08-25 22:27:16] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -30.000006-0.003229j
[2025-08-25 22:27:22] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -30.002694+0.000616j
[2025-08-25 22:27:28] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -30.006649-0.003300j
[2025-08-25 22:27:34] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -30.000016-0.004131j
[2025-08-25 22:27:40] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -30.001552+0.000121j
[2025-08-25 22:27:45] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -30.004276+0.001654j
[2025-08-25 22:27:51] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -29.996229-0.000816j
[2025-08-25 22:27:57] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -30.003816+0.002931j
[2025-08-25 22:28:03] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -29.999395-0.000088j
[2025-08-25 22:28:09] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -29.993650+0.000999j
[2025-08-25 22:28:14] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -29.993166+0.000454j
[2025-08-25 22:28:20] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -30.001122+0.001527j
[2025-08-25 22:28:26] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -29.992492+0.002433j
[2025-08-25 22:28:32] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -29.995015+0.004216j
[2025-08-25 22:28:38] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -29.996282-0.001078j
[2025-08-25 22:28:43] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -29.997224+0.000669j
[2025-08-25 22:28:49] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -30.003851-0.002390j
[2025-08-25 22:28:55] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -30.000864-0.002206j
[2025-08-25 22:29:01] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -30.000589+0.002250j
[2025-08-25 22:29:07] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -30.003492-0.002427j
[2025-08-25 22:29:12] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -29.997649+0.001322j
[2025-08-25 22:29:18] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -29.995291-0.002501j
[2025-08-25 22:29:24] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -29.997707+0.001047j
[2025-08-25 22:29:30] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -29.991208-0.000135j
[2025-08-25 22:29:36] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -29.993305+0.002709j
[2025-08-25 22:29:42] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -30.004286+0.000790j
[2025-08-25 22:29:47] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -29.998505-0.001495j
[2025-08-25 22:29:53] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -29.985808+0.001403j
[2025-08-25 22:29:59] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -29.999112-0.001180j
[2025-08-25 22:29:59] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-08-25 22:30:05] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -29.998525-0.001311j
[2025-08-25 22:30:11] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -29.990644+0.000831j
[2025-08-25 22:30:16] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -29.990154+0.001100j
[2025-08-25 22:30:22] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -29.998387+0.001704j
[2025-08-25 22:30:28] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -30.000432-0.000969j
[2025-08-25 22:30:34] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -30.003363+0.002200j
[2025-08-25 22:30:39] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -29.994599-0.000668j
[2025-08-25 22:30:45] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -30.002596-0.001821j
[2025-08-25 22:30:51] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -30.005385+0.001270j
[2025-08-25 22:30:57] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -30.001901-0.001443j
[2025-08-25 22:31:03] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -30.000035+0.001788j
[2025-08-25 22:31:09] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -29.991095+0.002575j
[2025-08-25 22:31:14] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -29.996004-0.001807j
[2025-08-25 22:31:20] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -29.997346-0.002158j
[2025-08-25 22:31:26] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -30.001459-0.000271j
[2025-08-25 22:31:32] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -29.998675+0.001170j
[2025-08-25 22:31:38] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -29.997670+0.001586j
[2025-08-25 22:31:43] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -29.999600+0.000859j
[2025-08-25 22:31:49] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -29.996799-0.001128j
[2025-08-25 22:31:55] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -29.996821-0.000767j
[2025-08-25 22:32:01] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -29.985844+0.001738j
[2025-08-25 22:32:07] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -30.005473-0.004633j
[2025-08-25 22:32:12] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -29.992384+0.007513j
[2025-08-25 22:32:18] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -30.000052+0.000910j
[2025-08-25 22:32:24] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -29.988744-0.001554j
[2025-08-25 22:32:30] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -29.989909-0.002104j
[2025-08-25 22:32:36] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -30.005503+0.000132j
[2025-08-25 22:32:41] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -30.005985-0.000397j
[2025-08-25 22:32:47] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -30.002384+0.002826j
[2025-08-25 22:32:53] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -29.985462-0.004715j
[2025-08-25 22:32:59] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -30.003059-0.001243j
[2025-08-25 22:33:05] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -29.993933-0.001582j
[2025-08-25 22:33:10] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -30.001232-0.000391j
[2025-08-25 22:33:16] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -30.000568+0.002175j
[2025-08-25 22:33:22] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -29.995746-0.001625j
[2025-08-25 22:33:28] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -29.996666+0.000324j
[2025-08-25 22:33:34] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -29.998732+0.001039j
[2025-08-25 22:33:39] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -29.996516-0.000711j
[2025-08-25 22:33:45] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -30.005021-0.002508j
[2025-08-25 22:33:51] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -29.991355-0.000060j
[2025-08-25 22:33:57] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -30.005352-0.000335j
[2025-08-25 22:34:03] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -30.003948-0.000611j
[2025-08-25 22:34:08] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -29.997057+0.006465j
[2025-08-25 22:34:14] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -30.000487+0.001006j
[2025-08-25 22:34:20] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -29.996056-0.000212j
[2025-08-25 22:34:26] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -29.991531+0.004527j
[2025-08-25 22:34:32] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -29.995499+0.001942j
[2025-08-25 22:34:37] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -30.001776+0.002929j
[2025-08-25 22:34:43] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -30.001603+0.000675j
[2025-08-25 22:34:49] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -29.998743-0.000834j
[2025-08-25 22:34:49] RESTART #2 | Period: 600
[2025-08-25 22:34:55] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -30.003120+0.000083j
[2025-08-25 22:35:01] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -29.998428+0.000967j
[2025-08-25 22:35:06] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -30.005732-0.002481j
[2025-08-25 22:35:12] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -29.996631-0.001163j
[2025-08-25 22:35:18] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -29.999614-0.001364j
[2025-08-25 22:35:24] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -29.997477+0.004283j
[2025-08-25 22:35:30] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -29.999303+0.000814j
[2025-08-25 22:35:35] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -29.999694+0.000684j
[2025-08-25 22:35:41] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -29.998171+0.003575j
[2025-08-25 22:35:47] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -29.992907-0.000276j
[2025-08-25 22:35:53] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -30.007091+0.000898j
[2025-08-25 22:35:59] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -30.000691-0.000368j
[2025-08-25 22:36:04] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -29.989610+0.000158j
[2025-08-25 22:36:10] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -30.003438-0.002278j
[2025-08-25 22:36:16] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -29.998865-0.002363j
[2025-08-25 22:36:22] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -29.996981+0.006186j
[2025-08-25 22:36:28] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -29.995837+0.003189j
[2025-08-25 22:36:34] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -30.000655+0.002348j
[2025-08-25 22:36:39] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -29.994736+0.003648j
[2025-08-25 22:36:45] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -29.994187+0.000935j
[2025-08-25 22:36:51] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -29.991053+0.000350j
[2025-08-25 22:36:57] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -30.009172+0.000224j
[2025-08-25 22:37:03] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -29.985940-0.001592j
[2025-08-25 22:37:08] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -30.004194-0.000848j
[2025-08-25 22:37:14] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -29.999199-0.002869j
[2025-08-25 22:37:20] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -30.006769-0.004164j
[2025-08-25 22:37:26] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -30.003687+0.001783j
[2025-08-25 22:37:32] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -29.999482+0.002422j
[2025-08-25 22:37:38] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -29.991180+0.000466j
[2025-08-25 22:37:43] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -30.000103-0.002048j
[2025-08-25 22:37:49] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -30.001730+0.000520j
[2025-08-25 22:37:55] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -30.010826-0.000332j
[2025-08-25 22:38:01] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -29.997689-0.002584j
[2025-08-25 22:38:07] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -29.990777-0.000365j
[2025-08-25 22:38:12] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -30.007213+0.000438j
[2025-08-25 22:38:18] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -30.004302+0.000072j
[2025-08-25 22:38:24] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -30.000487+0.001775j
[2025-08-25 22:38:30] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -29.994323-0.000668j
[2025-08-25 22:38:36] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -29.987573+0.000134j
[2025-08-25 22:38:41] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -29.995081-0.002272j
[2025-08-25 22:38:47] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -29.995732-0.001442j
[2025-08-25 22:38:53] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -29.994328-0.000534j
[2025-08-25 22:38:59] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -29.991172-0.002121j
[2025-08-25 22:39:05] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -29.998579+0.002017j
[2025-08-25 22:39:11] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -29.999081+0.000414j
[2025-08-25 22:39:16] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -29.993609-0.000495j
[2025-08-25 22:39:22] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -29.999858-0.002448j
[2025-08-25 22:39:28] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -30.005164+0.000420j
[2025-08-25 22:39:34] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -30.000426-0.000866j
[2025-08-25 22:39:40] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -29.995425+0.001804j
[2025-08-25 22:39:40] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-08-25 22:39:45] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -29.996497+0.001714j
[2025-08-25 22:39:51] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -29.992022+0.002642j
[2025-08-25 22:39:57] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -29.993822+0.000237j
[2025-08-25 22:40:03] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -29.999183-0.000975j
[2025-08-25 22:40:09] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -29.996552+0.001631j
[2025-08-25 22:40:15] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -29.998244-0.000854j
[2025-08-25 22:40:20] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -29.997927-0.000544j
[2025-08-25 22:40:26] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -29.981497+0.001275j
[2025-08-25 22:40:32] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -29.999910+0.000300j
[2025-08-25 22:40:38] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -30.000331+0.000120j
[2025-08-25 22:40:44] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -30.004103+0.000306j
[2025-08-25 22:40:49] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -30.014350+0.004500j
[2025-08-25 22:40:55] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -30.000044+0.000258j
[2025-08-25 22:41:01] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -30.003191-0.000819j
[2025-08-25 22:41:07] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -29.990765+0.001174j
[2025-08-25 22:41:13] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -29.998845+0.003823j
[2025-08-25 22:41:18] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -30.001322+0.000044j
[2025-08-25 22:41:24] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -30.005433-0.001741j
[2025-08-25 22:41:30] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -29.998773+0.001726j
[2025-08-25 22:41:36] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -29.998784-0.003721j
[2025-08-25 22:41:42] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -29.997763+0.000783j
[2025-08-25 22:41:47] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -30.000186-0.001109j
[2025-08-25 22:41:53] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -30.005283-0.001214j
[2025-08-25 22:41:59] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -29.997336+0.001269j
[2025-08-25 22:42:05] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -29.996265-0.000291j
[2025-08-25 22:42:11] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -30.002954-0.002595j
[2025-08-25 22:42:16] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -29.996554-0.000676j
[2025-08-25 22:42:22] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -30.004617+0.000313j
[2025-08-25 22:42:28] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -29.992369-0.001931j
[2025-08-25 22:42:34] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -30.005857-0.001622j
[2025-08-25 22:42:40] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -29.995331-0.001370j
[2025-08-25 22:42:45] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -29.995236+0.001655j
[2025-08-25 22:42:51] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -29.985315-0.000707j
[2025-08-25 22:42:57] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -29.999545-0.000704j
[2025-08-25 22:43:03] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -29.997270-0.004348j
[2025-08-25 22:43:09] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -30.005734-0.000401j
[2025-08-25 22:43:15] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -29.996499+0.001870j
[2025-08-25 22:43:20] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -30.005063-0.002259j
[2025-08-25 22:43:26] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -29.995549+0.000770j
[2025-08-25 22:43:32] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -30.001698-0.000979j
[2025-08-25 22:43:38] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -29.995808+0.001762j
[2025-08-25 22:43:44] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -29.992329-0.000895j
[2025-08-25 22:43:49] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -29.998903-0.000021j
[2025-08-25 22:43:55] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -30.003781-0.000838j
[2025-08-25 22:44:01] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -29.990189-0.000155j
[2025-08-25 22:44:07] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -29.994832-0.001934j
[2025-08-25 22:44:13] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -29.997698-0.000584j
[2025-08-25 22:44:18] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -30.000942+0.002064j
[2025-08-25 22:44:24] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -30.001559+0.000056j
[2025-08-25 22:44:30] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -30.000396+0.002333j
[2025-08-25 22:44:36] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -30.000882-0.002066j
[2025-08-25 22:44:42] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -29.995553-0.001769j
[2025-08-25 22:44:48] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -30.008442-0.004884j
[2025-08-25 22:44:53] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -29.997019-0.000522j
[2025-08-25 22:44:59] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -30.003736+0.000965j
[2025-08-25 22:45:05] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -30.005946-0.001833j
[2025-08-25 22:45:11] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -29.992638+0.002600j
[2025-08-25 22:45:17] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -30.000087+0.001464j
[2025-08-25 22:45:22] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -30.001168-0.000750j
[2025-08-25 22:45:28] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -29.996816+0.003828j
[2025-08-25 22:45:34] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -30.006101+0.001494j
[2025-08-25 22:45:40] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -30.001990+0.000108j
[2025-08-25 22:45:46] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -29.992785-0.002176j
[2025-08-25 22:45:51] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -29.990547-0.004221j
[2025-08-25 22:45:57] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -30.000065-0.002348j
[2025-08-25 22:46:03] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -29.998411-0.000260j
[2025-08-25 22:46:09] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -29.996819-0.001489j
[2025-08-25 22:46:15] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -29.995748+0.000038j
[2025-08-25 22:46:20] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -30.001216-0.000075j
[2025-08-25 22:46:26] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -30.006535+0.001800j
[2025-08-25 22:46:32] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -29.991404-0.001036j
[2025-08-25 22:46:38] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -30.003440-0.005014j
[2025-08-25 22:46:44] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -30.009317-0.001372j
[2025-08-25 22:46:49] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -30.006733-0.000334j
[2025-08-25 22:46:55] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -29.998385+0.001030j
[2025-08-25 22:47:01] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -29.996350+0.004730j
[2025-08-25 22:47:07] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -30.001116-0.003834j
[2025-08-25 22:47:13] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -30.003166-0.000097j
[2025-08-25 22:47:18] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -29.992516+0.011944j
[2025-08-25 22:47:24] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -29.989125-0.000010j
[2025-08-25 22:47:30] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -30.001137+0.001035j
[2025-08-25 22:47:36] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -29.994021+0.003576j
[2025-08-25 22:47:42] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -29.999236+0.001504j
[2025-08-25 22:47:48] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -30.001507-0.000913j
[2025-08-25 22:47:53] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -29.996215+0.001013j
[2025-08-25 22:47:59] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -29.997256-0.003724j
[2025-08-25 22:48:05] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -30.000260+0.002659j
[2025-08-25 22:48:11] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -29.997833-0.002724j
[2025-08-25 22:48:17] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -30.006327+0.000501j
[2025-08-25 22:48:22] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -29.982440-0.000749j
[2025-08-25 22:48:28] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -29.993412-0.001353j
[2025-08-25 22:48:34] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -29.996864-0.001619j
[2025-08-25 22:48:40] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -29.998957+0.000712j
[2025-08-25 22:48:46] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -29.994386+0.001444j
[2025-08-25 22:48:51] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -29.997515-0.002766j
[2025-08-25 22:48:57] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -29.998709-0.001461j
[2025-08-25 22:49:03] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -29.990424-0.000137j
[2025-08-25 22:49:09] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -30.004868+0.004432j
[2025-08-25 22:49:15] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -29.994810-0.001788j
[2025-08-25 22:49:20] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -30.003502+0.001061j
[2025-08-25 22:49:20] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-08-25 22:49:26] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -29.990847+0.003171j
[2025-08-25 22:49:32] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -29.993182-0.001393j
[2025-08-25 22:49:38] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -30.000833-0.000771j
[2025-08-25 22:49:44] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -29.989249-0.000236j
[2025-08-25 22:49:49] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -29.996231+0.000996j
[2025-08-25 22:49:55] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -29.996895-0.000506j
[2025-08-25 22:50:01] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -29.997581+0.001624j
[2025-08-25 22:50:07] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -29.998243-0.002087j
[2025-08-25 22:50:13] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -30.005364+0.002360j
[2025-08-25 22:50:18] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -29.993852+0.002555j
[2025-08-25 22:50:24] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -29.998842+0.002580j
[2025-08-25 22:50:30] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -30.004560+0.000783j
[2025-08-25 22:50:36] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -29.999280-0.001143j
[2025-08-25 22:50:42] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -30.008020+0.001545j
[2025-08-25 22:50:47] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -29.992649+0.001159j
[2025-08-25 22:50:53] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -29.991470+0.000275j
[2025-08-25 22:50:59] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -29.998624+0.001202j
[2025-08-25 22:51:05] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -30.005054-0.003315j
[2025-08-25 22:51:11] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -30.003716-0.000807j
[2025-08-25 22:51:16] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -29.996388+0.001421j
[2025-08-25 22:51:22] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -30.004927+0.000590j
[2025-08-25 22:51:28] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -29.999196-0.001775j
[2025-08-25 22:51:34] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -29.993476+0.001762j
[2025-08-25 22:51:40] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -30.000256+0.002248j
[2025-08-25 22:51:45] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -30.010699-0.002036j
[2025-08-25 22:51:51] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -30.006465+0.003203j
[2025-08-25 22:51:57] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -29.996399+0.001908j
[2025-08-25 22:52:03] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -29.989803-0.002738j
[2025-08-25 22:52:09] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -30.003384-0.000147j
[2025-08-25 22:52:14] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -30.000866+0.000848j
[2025-08-25 22:52:20] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -30.000826-0.001164j
[2025-08-25 22:52:26] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -29.997863-0.000059j
[2025-08-25 22:52:32] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -29.999190+0.002760j
[2025-08-25 22:52:38] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -29.993434+0.001248j
[2025-08-25 22:52:43] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -30.002968+0.001166j
[2025-08-25 22:52:49] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -30.007116-0.003170j
[2025-08-25 22:52:55] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -30.004841-0.003836j
[2025-08-25 22:53:01] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -29.996794-0.002249j
[2025-08-25 22:53:07] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -30.000117-0.000623j
[2025-08-25 22:53:12] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -29.997176+0.002009j
[2025-08-25 22:53:18] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -30.003419+0.000006j
[2025-08-25 22:53:24] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -30.007011+0.000544j
[2025-08-25 22:53:30] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -29.989430+0.002433j
[2025-08-25 22:53:36] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -29.997252+0.000811j
[2025-08-25 22:53:41] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -30.000159-0.001070j
[2025-08-25 22:53:47] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -30.004015-0.001865j
[2025-08-25 22:53:53] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -30.001036+0.000053j
[2025-08-25 22:53:59] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -29.991489-0.000659j
[2025-08-25 22:54:05] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -29.994304-0.002714j
[2025-08-25 22:54:10] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -29.998310-0.001176j
[2025-08-25 22:54:16] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -30.001653-0.001574j
[2025-08-25 22:54:22] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -30.002400+0.001143j
[2025-08-25 22:54:28] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -29.998740-0.000930j
[2025-08-25 22:54:34] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -29.990633+0.000684j
[2025-08-25 22:54:39] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -29.996436+0.003235j
[2025-08-25 22:54:45] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -29.996883+0.002147j
[2025-08-25 22:54:51] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -29.999394-0.003864j
[2025-08-25 22:54:57] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -29.999441-0.000312j
[2025-08-25 22:55:03] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -29.988793+0.001515j
[2025-08-25 22:55:08] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -30.003010-0.000159j
[2025-08-25 22:55:14] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -30.003015+0.002387j
[2025-08-25 22:55:20] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -29.995357-0.001081j
[2025-08-25 22:55:26] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -29.997790+0.002603j
[2025-08-25 22:55:32] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -30.006161+0.001084j
[2025-08-25 22:55:38] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -30.000547+0.000319j
[2025-08-25 22:55:43] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -30.002997+0.000366j
[2025-08-25 22:55:49] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -30.006972-0.000395j
[2025-08-25 22:55:55] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -29.999388-0.006026j
[2025-08-25 22:56:01] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -30.022392+0.010439j
[2025-08-25 22:56:07] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -29.992712+0.003679j
[2025-08-25 22:56:12] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -30.002705+0.000800j
[2025-08-25 22:56:18] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -29.994138-0.002301j
[2025-08-25 22:56:24] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -29.999743-0.005551j
[2025-08-25 22:56:30] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -29.989660+0.005208j
[2025-08-25 22:56:36] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -29.989914+0.001539j
[2025-08-25 22:56:42] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -30.004213+0.003270j
[2025-08-25 22:56:47] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -30.000717-0.000195j
[2025-08-25 22:56:53] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -30.001877-0.000851j
[2025-08-25 22:56:59] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -29.988496-0.002702j
[2025-08-25 22:57:05] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -30.016207-0.003235j
[2025-08-25 22:57:11] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -29.995633+0.001643j
[2025-08-25 22:57:16] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -29.992399-0.002632j
[2025-08-25 22:57:22] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -30.007240+0.002766j
[2025-08-25 22:57:28] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -29.994628-0.002252j
[2025-08-25 22:57:34] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -29.993383-0.001180j
[2025-08-25 22:57:40] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -29.995523+0.003414j
[2025-08-25 22:57:46] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -30.001881-0.001303j
[2025-08-25 22:57:51] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -30.002186+0.003506j
[2025-08-25 22:57:57] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -30.000709+0.001206j
[2025-08-25 22:58:03] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -30.002463+0.001703j
[2025-08-25 22:58:09] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -30.002278+0.000224j
[2025-08-25 22:58:15] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -29.998399-0.003403j
[2025-08-25 22:58:20] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -30.003614-0.001824j
[2025-08-25 22:58:26] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -30.007266-0.000821j
[2025-08-25 22:58:32] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -29.986405+0.003516j
[2025-08-25 22:58:38] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -29.994834-0.000040j
[2025-08-25 22:58:44] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -29.994706-0.000015j
[2025-08-25 22:58:49] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -30.011741+0.001596j
[2025-08-25 22:58:55] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -29.991049+0.002530j
[2025-08-25 22:59:01] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -29.996381+0.001030j
[2025-08-25 22:59:01] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-08-25 22:59:07] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -29.994993+0.000462j
[2025-08-25 22:59:13] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -29.995348-0.001918j
[2025-08-25 22:59:19] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -29.999407-0.000549j
[2025-08-25 22:59:24] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -30.005295-0.002931j
[2025-08-25 22:59:30] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -30.006606-0.000303j
[2025-08-25 22:59:36] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -29.994679-0.000529j
[2025-08-25 22:59:42] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -29.999167+0.000278j
[2025-08-25 22:59:48] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -30.001848-0.000867j
[2025-08-25 22:59:53] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -30.003177-0.004431j
[2025-08-25 22:59:59] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -30.001877+0.000420j
[2025-08-25 23:00:05] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -29.993124-0.003698j
[2025-08-25 23:00:11] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -30.005297-0.000602j
[2025-08-25 23:00:17] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -29.990063+0.000821j
[2025-08-25 23:00:22] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -29.998441-0.006499j
[2025-08-25 23:00:28] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -29.995160-0.004193j
[2025-08-25 23:00:34] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -29.996004+0.001112j
[2025-08-25 23:00:40] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -29.999916+0.002142j
[2025-08-25 23:00:46] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -30.008405-0.000207j
[2025-08-25 23:00:51] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -29.990413-0.002323j
[2025-08-25 23:00:57] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -29.998954-0.001401j
[2025-08-25 23:01:03] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -29.991190+0.000221j
[2025-08-25 23:01:09] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -29.992332+0.001346j
[2025-08-25 23:01:15] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -29.999537+0.004242j
[2025-08-25 23:01:20] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -30.004051+0.002760j
[2025-08-25 23:01:26] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -29.990042-0.002833j
[2025-08-25 23:01:32] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -30.004415+0.000069j
[2025-08-25 23:01:38] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -30.004605+0.001366j
[2025-08-25 23:01:44] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -30.003504-0.002738j
[2025-08-25 23:01:50] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -29.991671+0.001930j
[2025-08-25 23:01:55] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -29.994455-0.001416j
[2025-08-25 23:02:01] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -30.001369+0.000257j
[2025-08-25 23:02:07] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -30.002934-0.002195j
[2025-08-25 23:02:13] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -29.995788+0.003538j
[2025-08-25 23:02:19] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -30.000875-0.001364j
[2025-08-25 23:02:24] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -30.004866-0.000357j
[2025-08-25 23:02:30] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -29.999754+0.003309j
[2025-08-25 23:02:36] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -30.005773+0.000512j
[2025-08-25 23:02:42] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -30.005487+0.001197j
[2025-08-25 23:02:48] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -29.997493-0.000045j
[2025-08-25 23:02:54] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -29.999408+0.000189j
[2025-08-25 23:02:59] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -29.997779-0.001502j
[2025-08-25 23:03:05] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -29.999957-0.002017j
[2025-08-25 23:03:11] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -29.988108-0.000503j
[2025-08-25 23:03:17] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -29.998887-0.003540j
[2025-08-25 23:03:23] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -29.997392+0.000360j
[2025-08-25 23:03:28] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -29.994752+0.000002j
[2025-08-25 23:03:34] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -30.006424-0.000391j
[2025-08-25 23:03:40] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -29.997483-0.001220j
[2025-08-25 23:03:46] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -29.996435+0.001326j
[2025-08-25 23:03:52] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -30.006855-0.001713j
[2025-08-25 23:03:57] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -30.003779-0.000953j
[2025-08-25 23:04:03] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -29.997250+0.000137j
[2025-08-25 23:04:09] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -30.005510-0.001363j
[2025-08-25 23:04:15] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -29.995032+0.001393j
[2025-08-25 23:04:21] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -30.001193+0.001386j
[2025-08-25 23:04:27] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -29.991337+0.003521j
[2025-08-25 23:04:32] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -30.008321-0.000430j
[2025-08-25 23:04:38] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -30.005183-0.000709j
[2025-08-25 23:04:44] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -29.991436-0.001057j
[2025-08-25 23:04:50] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -29.998292+0.003064j
[2025-08-25 23:04:56] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -30.006217+0.001635j
[2025-08-25 23:05:01] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -29.993624-0.000131j
[2025-08-25 23:05:07] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -29.997145-0.003924j
[2025-08-25 23:05:13] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -29.994015-0.001350j
[2025-08-25 23:05:19] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -30.006840-0.000149j
[2025-08-25 23:05:25] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -29.992221+0.001038j
[2025-08-25 23:05:30] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -29.998245+0.004033j
[2025-08-25 23:05:36] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -29.992822-0.003031j
[2025-08-25 23:05:42] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -30.005559-0.001804j
[2025-08-25 23:05:48] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -29.988502-0.000150j
[2025-08-25 23:05:54] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -30.002236-0.001362j
[2025-08-25 23:05:59] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -30.005708-0.000145j
[2025-08-25 23:06:05] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -29.996297-0.002002j
[2025-08-25 23:06:11] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -29.999660-0.003694j
[2025-08-25 23:06:17] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -30.003507-0.000797j
[2025-08-25 23:06:23] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -29.998367-0.000182j
[2025-08-25 23:06:28] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -30.001233-0.003127j
[2025-08-25 23:06:34] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -29.996518-0.000153j
[2025-08-25 23:06:40] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -30.002739-0.003041j
[2025-08-25 23:06:46] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -30.007364+0.002022j
[2025-08-25 23:06:52] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -29.997268-0.001771j
[2025-08-25 23:06:57] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -29.997463-0.002451j
[2025-08-25 23:07:03] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -30.011329+0.000514j
[2025-08-25 23:07:09] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -30.002228-0.000083j
[2025-08-25 23:07:15] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -29.996289+0.000738j
[2025-08-25 23:07:21] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -29.997703+0.003387j
[2025-08-25 23:07:26] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -30.002275+0.001746j
[2025-08-25 23:07:32] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -29.989145-0.002595j
[2025-08-25 23:07:38] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -30.006616-0.000026j
[2025-08-25 23:07:44] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -29.999005+0.000655j
[2025-08-25 23:07:50] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -30.007358+0.000814j
[2025-08-25 23:07:55] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -29.991571+0.000310j
[2025-08-25 23:08:01] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -29.996651+0.001682j
[2025-08-25 23:08:07] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -30.000719-0.002561j
[2025-08-25 23:08:13] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -30.009289-0.001391j
[2025-08-25 23:08:19] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -29.997560-0.000830j
[2025-08-25 23:08:24] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -30.001460+0.000034j
[2025-08-25 23:08:30] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -29.990477-0.001250j
[2025-08-25 23:08:36] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -30.001057+0.003937j
[2025-08-25 23:08:42] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -30.007385+0.003275j
[2025-08-25 23:08:42] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-08-25 23:08:48] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -30.000094+0.002678j
[2025-08-25 23:08:54] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -29.996621-0.000762j
[2025-08-25 23:08:59] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -29.992603+0.001557j
[2025-08-25 23:09:05] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -30.000785-0.001101j
[2025-08-25 23:09:11] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -30.003899-0.000904j
[2025-08-25 23:09:17] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -29.984307-0.002654j
[2025-08-25 23:09:23] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -29.993565+0.000628j
[2025-08-25 23:09:28] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -30.003682-0.003059j
[2025-08-25 23:09:34] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -30.000376-0.002314j
[2025-08-25 23:09:40] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -29.990837-0.001836j
[2025-08-25 23:09:46] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -30.000160+0.000122j
[2025-08-25 23:09:52] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -30.000147-0.002533j
[2025-08-25 23:09:57] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -29.996269-0.001311j
[2025-08-25 23:10:03] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -29.994602-0.002483j
[2025-08-25 23:10:09] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -29.993804-0.000374j
[2025-08-25 23:10:15] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -29.997522-0.000719j
[2025-08-25 23:10:21] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -29.998141+0.001452j
[2025-08-25 23:10:26] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -30.002334+0.001831j
[2025-08-25 23:10:32] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -30.000298+0.001061j
[2025-08-25 23:10:38] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -30.003909+0.000843j
[2025-08-25 23:10:44] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -29.985071+0.000479j
[2025-08-25 23:10:50] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -30.003421+0.001230j
[2025-08-25 23:10:56] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -30.003406+0.000031j
[2025-08-25 23:11:01] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -30.003167-0.003804j
[2025-08-25 23:11:07] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -30.009541-0.000858j
[2025-08-25 23:11:13] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -30.008771-0.002515j
[2025-08-25 23:11:19] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -30.000446-0.001111j
[2025-08-25 23:11:25] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -29.996061+0.000923j
[2025-08-25 23:11:30] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -29.998658-0.000471j
[2025-08-25 23:11:36] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -29.996411-0.001282j
[2025-08-25 23:11:42] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -29.994690-0.003311j
[2025-08-25 23:11:48] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -29.987572+0.000551j
[2025-08-25 23:11:54] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -30.004194-0.002345j
[2025-08-25 23:11:59] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -29.986426-0.003683j
[2025-08-25 23:12:05] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -30.004101+0.001194j
[2025-08-25 23:12:11] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -29.991952-0.002781j
[2025-08-25 23:12:17] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -30.009751-0.000941j
[2025-08-25 23:12:23] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -29.999043+0.001358j
[2025-08-25 23:12:29] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -29.997226-0.000287j
[2025-08-25 23:12:34] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -30.006561-0.001547j
[2025-08-25 23:12:40] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -29.989683-0.000739j
[2025-08-25 23:12:46] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -30.000607+0.002109j
[2025-08-25 23:12:52] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -29.998864+0.000030j
[2025-08-25 23:12:58] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -30.000347-0.001667j
[2025-08-25 23:13:03] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -29.986703+0.002201j
[2025-08-25 23:13:09] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -29.994000+0.001562j
[2025-08-25 23:13:15] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -30.000623+0.003248j
[2025-08-25 23:13:21] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -30.003569-0.000214j
[2025-08-25 23:13:27] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -30.004186+0.002234j
[2025-08-25 23:13:33] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -30.002655-0.002437j
[2025-08-25 23:13:38] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -30.004634-0.001171j
[2025-08-25 23:13:44] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -29.997961+0.001212j
[2025-08-25 23:13:50] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -29.997501+0.001902j
[2025-08-25 23:13:56] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -29.991836+0.000169j
[2025-08-25 23:14:02] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -30.004655-0.000286j
[2025-08-25 23:14:07] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -30.008361-0.005094j
[2025-08-25 23:14:13] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -29.992530-0.002532j
[2025-08-25 23:14:19] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -29.994688-0.000713j
[2025-08-25 23:14:25] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -29.998033-0.002629j
[2025-08-25 23:14:31] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -29.998044-0.001407j
[2025-08-25 23:14:37] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -29.994634+0.001793j
[2025-08-25 23:14:42] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -30.002596-0.001202j
[2025-08-25 23:14:48] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -30.004172-0.000260j
[2025-08-25 23:14:54] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -30.011112-0.001260j
[2025-08-25 23:15:00] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -30.006059-0.006435j
[2025-08-25 23:15:06] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -30.005200+0.000038j
[2025-08-25 23:15:11] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -30.006340+0.000296j
[2025-08-25 23:15:17] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -29.995958+0.000496j
[2025-08-25 23:15:23] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -30.006991+0.000673j
[2025-08-25 23:15:29] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -30.002485+0.004431j
[2025-08-25 23:15:35] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -29.998632+0.000188j
[2025-08-25 23:15:40] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -29.996537+0.001324j
[2025-08-25 23:15:46] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -29.996384-0.000704j
[2025-08-25 23:15:52] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -29.995905+0.001211j
[2025-08-25 23:15:58] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -30.009475+0.003637j
[2025-08-25 23:16:04] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -29.993115+0.002809j
[2025-08-25 23:16:09] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -29.993415-0.001414j
[2025-08-25 23:16:15] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -30.000583+0.001360j
[2025-08-25 23:16:21] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -30.004752-0.002658j
[2025-08-25 23:16:27] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -29.998572-0.001201j
[2025-08-25 23:16:33] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -29.991851-0.001506j
[2025-08-25 23:16:39] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -30.001357-0.001246j
[2025-08-25 23:16:44] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -29.992604+0.002819j
[2025-08-25 23:16:50] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -30.003956+0.001344j
[2025-08-25 23:16:56] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -30.001089-0.000459j
[2025-08-25 23:17:02] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -29.993618-0.003046j
[2025-08-25 23:17:08] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -30.002037+0.003184j
[2025-08-25 23:17:13] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -30.001169+0.001181j
[2025-08-25 23:17:19] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -30.005386-0.002190j
[2025-08-25 23:17:25] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -29.989392-0.001508j
[2025-08-25 23:17:31] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -29.991073-0.000076j
[2025-08-25 23:17:37] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -30.009306+0.001421j
[2025-08-25 23:17:43] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -30.005237-0.001254j
[2025-08-25 23:17:48] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -30.004012+0.000273j
[2025-08-25 23:17:54] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -29.993830+0.001277j
[2025-08-25 23:18:00] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -30.002345+0.002647j
[2025-08-25 23:18:06] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -29.994208-0.001363j
[2025-08-25 23:18:12] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -29.995766+0.000589j
[2025-08-25 23:18:17] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -30.002672+0.000310j
[2025-08-25 23:18:23] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -29.995256+0.000647j
[2025-08-25 23:18:23] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-08-25 23:18:29] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -29.995458-0.001560j
[2025-08-25 23:18:35] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -29.995916-0.000872j
[2025-08-25 23:18:41] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -30.011616-0.001577j
[2025-08-25 23:18:46] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -30.005433-0.002792j
[2025-08-25 23:18:52] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -30.001680-0.003464j
[2025-08-25 23:18:58] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -29.990158-0.004981j
[2025-08-25 23:19:04] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -29.998662-0.000821j
[2025-08-25 23:19:10] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -29.992566+0.000307j
[2025-08-25 23:19:16] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -29.991060-0.000425j
[2025-08-25 23:19:21] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -30.004892-0.003452j
[2025-08-25 23:19:27] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -30.001322+0.000067j
[2025-08-25 23:19:33] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -30.002395-0.001140j
[2025-08-25 23:19:39] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -29.991485-0.000415j
[2025-08-25 23:19:45] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -29.997880+0.000678j
[2025-08-25 23:19:50] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -30.004320+0.000394j
[2025-08-25 23:19:56] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -30.005515-0.001072j
[2025-08-25 23:20:02] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -29.997675+0.004589j
[2025-08-25 23:20:08] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -29.993902-0.000798j
[2025-08-25 23:20:14] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -29.995718+0.003563j
[2025-08-25 23:20:19] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -29.998199+0.002456j
[2025-08-25 23:20:25] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -29.995409-0.001813j
[2025-08-25 23:20:31] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -29.988424+0.002499j
[2025-08-25 23:20:37] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -29.990385-0.001845j
[2025-08-25 23:20:43] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -30.008232+0.000234j
[2025-08-25 23:20:49] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -29.990912-0.001731j
[2025-08-25 23:20:54] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -30.007371+0.000350j
[2025-08-25 23:21:00] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -29.998721+0.002902j
[2025-08-25 23:21:06] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -29.992534+0.001483j
[2025-08-25 23:21:12] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -29.995648-0.002163j
[2025-08-25 23:21:18] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -30.001794-0.003467j
[2025-08-25 23:21:23] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -29.994782+0.000589j
[2025-08-25 23:21:29] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -30.005027-0.002587j
[2025-08-25 23:21:35] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -30.004242+0.000022j
[2025-08-25 23:21:41] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -30.011517+0.000326j
[2025-08-25 23:21:47] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -30.003536+0.000491j
[2025-08-25 23:21:52] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -29.998454-0.000737j
[2025-08-25 23:21:58] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -29.995766-0.001455j
[2025-08-25 23:22:04] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -29.994002+0.000582j
[2025-08-25 23:22:10] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -30.003603+0.000155j
[2025-08-25 23:22:16] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -29.999715+0.003075j
[2025-08-25 23:22:21] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -30.008285-0.000390j
[2025-08-25 23:22:27] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -30.000490-0.002714j
[2025-08-25 23:22:33] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -29.992472-0.001023j
[2025-08-25 23:22:39] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -29.998379-0.000886j
[2025-08-25 23:22:45] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -29.999706+0.001152j
[2025-08-25 23:22:50] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -30.003167+0.000528j
[2025-08-25 23:22:56] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -29.995834+0.003369j
[2025-08-25 23:23:02] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -29.999908+0.000523j
[2025-08-25 23:23:08] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -30.002192-0.000770j
[2025-08-25 23:23:14] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -29.999128+0.000985j
[2025-08-25 23:23:19] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -29.995483-0.001230j
[2025-08-25 23:23:25] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -30.000363+0.000460j
[2025-08-25 23:23:31] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -29.995639+0.001067j
[2025-08-25 23:23:37] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -29.992287+0.000238j
[2025-08-25 23:23:43] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -30.009238-0.002321j
[2025-08-25 23:23:49] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -30.009395+0.000572j
[2025-08-25 23:23:54] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -29.999687-0.004307j
[2025-08-25 23:24:00] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -29.996852-0.000720j
[2025-08-25 23:24:06] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -30.000252+0.000837j
[2025-08-25 23:24:12] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -29.985330-0.002245j
[2025-08-25 23:24:18] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -30.003740-0.003943j
[2025-08-25 23:24:23] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -30.009247-0.000554j
[2025-08-25 23:24:29] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -30.006672-0.002523j
[2025-08-25 23:24:35] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -29.996544-0.003029j
[2025-08-25 23:24:39] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -29.991419-0.000132j
[2025-08-25 23:24:45] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -29.999482-0.000127j
[2025-08-25 23:24:51] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -29.999272+0.001131j
[2025-08-25 23:24:57] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -29.996104-0.000996j
[2025-08-25 23:25:02] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -29.997861+0.001662j
[2025-08-25 23:25:08] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -30.010152-0.004127j
[2025-08-25 23:25:14] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -29.992986+0.001000j
[2025-08-25 23:25:20] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -30.010862-0.001283j
[2025-08-25 23:25:26] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -29.999159-0.000228j
[2025-08-25 23:25:32] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -30.006942-0.001482j
[2025-08-25 23:25:37] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -30.002701+0.001597j
[2025-08-25 23:25:43] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -29.996212+0.000264j
[2025-08-25 23:25:49] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -30.000330-0.001125j
[2025-08-25 23:25:55] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -29.998923+0.004390j
[2025-08-25 23:26:01] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -29.996473+0.000245j
[2025-08-25 23:26:06] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -29.992968-0.000759j
[2025-08-25 23:26:12] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -30.010413+0.002958j
[2025-08-25 23:26:18] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -30.003778-0.000676j
[2025-08-25 23:26:24] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -30.002754-0.001193j
[2025-08-25 23:26:30] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -30.002298+0.001889j
[2025-08-25 23:26:36] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -29.993361+0.000245j
[2025-08-25 23:26:41] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -30.000090-0.001341j
[2025-08-25 23:26:47] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -29.993858+0.003841j
[2025-08-25 23:26:53] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -30.007319+0.001868j
[2025-08-25 23:26:59] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -29.997386+0.001978j
[2025-08-25 23:27:05] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -30.003692-0.001349j
[2025-08-25 23:27:10] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -29.994066+0.002158j
[2025-08-25 23:27:16] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -29.998758-0.002638j
[2025-08-25 23:27:22] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -30.006162-0.002527j
[2025-08-25 23:27:28] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -30.004120+0.002097j
[2025-08-25 23:27:34] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -29.994968-0.001826j
[2025-08-25 23:27:39] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -30.001363-0.002734j
[2025-08-25 23:27:45] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -29.996203-0.002214j
[2025-08-25 23:27:51] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -29.991457-0.004538j
[2025-08-25 23:27:57] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -30.006836+0.000598j
[2025-08-25 23:28:03] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -29.998093-0.001230j
[2025-08-25 23:28:03] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-08-25 23:28:08] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -29.992961+0.001300j
[2025-08-25 23:28:14] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -29.992195-0.001343j
[2025-08-25 23:28:20] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -30.003111-0.000142j
[2025-08-25 23:28:26] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -30.008456+0.000618j
[2025-08-25 23:28:32] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -30.003661-0.001096j
[2025-08-25 23:28:37] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -29.990829+0.001599j
[2025-08-25 23:28:43] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -29.999894+0.000019j
[2025-08-25 23:28:49] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -29.989119+0.000339j
[2025-08-25 23:28:55] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -29.994447+0.001497j
[2025-08-25 23:29:01] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -29.993949+0.000915j
[2025-08-25 23:29:07] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -30.007016+0.001046j
[2025-08-25 23:29:12] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -30.003685+0.001254j
[2025-08-25 23:29:18] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -30.007436+0.002633j
[2025-08-25 23:29:24] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -29.998496-0.001548j
[2025-08-25 23:29:30] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -30.003044+0.001104j
[2025-08-25 23:29:36] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -29.991340-0.001571j
[2025-08-25 23:29:41] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -29.997464-0.000817j
[2025-08-25 23:29:47] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -29.995520+0.000448j
[2025-08-25 23:29:53] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -30.002059+0.003511j
[2025-08-25 23:29:59] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -29.998067+0.002658j
[2025-08-25 23:30:05] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -30.012673-0.003140j
[2025-08-25 23:30:10] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -29.996361+0.001156j
[2025-08-25 23:30:16] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -29.997307-0.002016j
[2025-08-25 23:30:22] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -29.989740-0.000507j
[2025-08-25 23:30:28] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -30.001200-0.003303j
[2025-08-25 23:30:34] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -30.004564-0.001575j
[2025-08-25 23:30:39] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -30.001244-0.006370j
[2025-08-25 23:30:45] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -30.008346+0.007413j
[2025-08-25 23:30:51] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -29.997697-0.003881j
[2025-08-25 23:30:57] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -30.005725+0.002277j
[2025-08-25 23:31:03] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -29.990593+0.003691j
[2025-08-25 23:31:08] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -30.001801-0.002641j
[2025-08-25 23:31:14] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -29.992368+0.000547j
[2025-08-25 23:31:20] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -29.996337-0.002874j
[2025-08-25 23:31:26] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -29.994187-0.001285j
[2025-08-25 23:31:32] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -29.998812-0.003035j
[2025-08-25 23:31:37] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -30.010599-0.006942j
[2025-08-25 23:31:43] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -30.000722+0.000822j
[2025-08-25 23:31:49] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -30.004684+0.001081j
[2025-08-25 23:31:55] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -30.000788-0.001239j
[2025-08-25 23:32:01] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -29.990968+0.000932j
[2025-08-25 23:32:06] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -30.006593-0.000613j
[2025-08-25 23:32:12] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -30.004772+0.002468j
[2025-08-25 23:32:18] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -30.000592+0.000621j
[2025-08-25 23:32:24] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -29.999174-0.000201j
[2025-08-25 23:32:26] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -29.993810-0.001021j
[2025-08-25 23:32:29] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -29.999003-0.002400j
[2025-08-25 23:32:32] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -30.001857-0.000024j
[2025-08-25 23:32:34] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -29.999467+0.000447j
[2025-08-25 23:32:37] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -29.991501-0.000041j
[2025-08-25 23:32:37] ✅ Training completed | Restarts: 2
[2025-08-25 23:32:37] ============================================================
[2025-08-25 23:32:37] Training completed | Runtime: 6132.6s
[2025-08-25 23:32:38] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-08-25 23:32:38] ============================================================
