[2025-08-26 19:19:03] 使用checkpoint文件: results/L=4/J2=1.00/J1=0.83/training/checkpoints/checkpoint_iter_001000.pkl
[2025-08-26 19:19:15] ✓ 从checkpoint加载参数: 1000
[2025-08-26 19:19:15]   - 能量: -29.998093-0.001230j ± 0.006425
[2025-08-26 19:19:15] ================================================================================
[2025-08-26 19:19:15] 加载量子态: L=4, J2=1.00, J1=0.83, checkpoint=checkpoint_iter_001000
[2025-08-26 19:19:15] 设置样本数为: 1048576
[2025-08-26 19:19:15] 开始生成共享样本集...
[2025-08-26 19:20:37] 样本生成完成,耗时: 82.621 秒
[2025-08-26 19:20:37] ================================================================================
[2025-08-26 19:20:37] 开始计算自旋结构因子...
[2025-08-26 19:20:37] 初始化操作符缓存...
[2025-08-26 19:20:37] 预构建所有自旋相关操作符...
[2025-08-26 19:20:37] 开始计算自旋相关函数...
[2025-08-26 19:20:45] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 7.498s
[2025-08-26 19:20:54] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 8.915s
[2025-08-26 19:20:58] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 4.243s
[2025-08-26 19:21:02] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 4.266s
[2025-08-26 19:21:07] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 4.269s
[2025-08-26 19:21:11] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 4.265s
[2025-08-26 19:21:15] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 4.242s
[2025-08-26 19:21:19] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 4.275s
[2025-08-26 19:21:24] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 4.241s
[2025-08-26 19:21:28] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 4.287s
[2025-08-26 19:21:32] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 4.265s
[2025-08-26 19:21:37] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 4.269s
[2025-08-26 19:21:41] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 4.243s
[2025-08-26 19:21:45] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 4.265s
[2025-08-26 19:21:49] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 4.268s
[2025-08-26 19:21:54] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 4.249s
[2025-08-26 19:21:58] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 4.263s
[2025-08-26 19:22:02] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 4.274s
[2025-08-26 19:22:06] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 4.242s
[2025-08-26 19:22:11] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 4.280s
[2025-08-26 19:22:15] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 4.264s
[2025-08-26 19:22:19] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 4.270s
[2025-08-26 19:22:23] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 4.242s
[2025-08-26 19:22:28] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 4.265s
[2025-08-26 19:22:32] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 4.252s
[2025-08-26 19:22:36] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 4.266s
[2025-08-26 19:22:40] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 4.242s
[2025-08-26 19:22:45] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 4.285s
[2025-08-26 19:22:49] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 4.252s
[2025-08-26 19:22:53] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 4.241s
[2025-08-26 19:22:57] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 4.241s
[2025-08-26 19:23:02] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 4.265s
[2025-08-26 19:23:06] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 4.255s
[2025-08-26 19:23:10] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 4.264s
[2025-08-26 19:23:15] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 4.252s
[2025-08-26 19:23:19] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 4.265s
[2025-08-26 19:23:23] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 4.243s
[2025-08-26 19:23:27] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 4.266s
[2025-08-26 19:23:32] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 4.242s
[2025-08-26 19:23:36] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 4.269s
[2025-08-26 19:23:40] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 4.243s
[2025-08-26 19:23:44] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 4.252s
[2025-08-26 19:23:49] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 4.242s
[2025-08-26 19:23:53] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 4.266s
[2025-08-26 19:23:57] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 4.242s
[2025-08-26 19:24:01] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 4.242s
[2025-08-26 19:24:06] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 4.270s
[2025-08-26 19:24:10] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 4.243s
[2025-08-26 19:24:14] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 4.268s
[2025-08-26 19:24:18] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 4.275s
[2025-08-26 19:24:23] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 4.242s
[2025-08-26 19:24:27] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 4.282s
[2025-08-26 19:24:31] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 4.252s
[2025-08-26 19:24:35] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 4.268s
[2025-08-26 19:24:40] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 4.242s
[2025-08-26 19:24:44] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 4.284s
[2025-08-26 19:24:48] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 4.243s
[2025-08-26 19:24:52] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 4.243s
[2025-08-26 19:24:57] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 4.243s
[2025-08-26 19:25:01] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 4.276s
[2025-08-26 19:25:05] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 4.250s
[2025-08-26 19:25:10] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 4.242s
[2025-08-26 19:25:14] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 4.271s
[2025-08-26 19:25:18] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 4.251s
[2025-08-26 19:25:18] 自旋相关函数计算完成,总耗时 280.59 秒
[2025-08-26 19:25:18] 计算傅里叶变换...
[2025-08-26 19:25:19] 自旋结构因子计算完成
[2025-08-26 19:25:20] 自旋相关函数平均误差: 0.000537
