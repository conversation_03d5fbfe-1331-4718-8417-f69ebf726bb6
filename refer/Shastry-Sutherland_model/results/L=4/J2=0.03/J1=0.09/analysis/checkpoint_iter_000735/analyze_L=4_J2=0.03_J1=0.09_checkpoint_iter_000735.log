[2025-09-13 16:32:39] 使用checkpoint文件: results/L=4/J2=0.03/J1=0.09/training/checkpoints/checkpoint_iter_000735.pkl
[2025-09-13 16:33:00] ✓ 从checkpoint加载参数: 735
[2025-09-13 16:33:00]   - 能量: -54.803939+0.000220j ± 0.081721
[2025-09-13 16:33:00] ================================================================================
[2025-09-13 16:33:00] 加载量子态: L=4, J2=0.03, J1=0.09, checkpoint=checkpoint_iter_000735
[2025-09-13 16:33:00] 使用采样数目: 1048576
[2025-09-13 16:33:00] 设置样本数为: 1048576
[2025-09-13 16:33:00] 开始生成共享样本集...
[2025-09-13 16:36:00] 样本生成完成,耗时: 180.532 秒
[2025-09-13 16:36:00] ================================================================================
[2025-09-13 16:36:00] 开始计算自旋结构因子...
[2025-09-13 16:36:00] 初始化操作符缓存...
[2025-09-13 16:36:00] 预构建所有自旋相关操作符...
[2025-09-13 16:36:00] 开始计算自旋相关函数...
[2025-09-13 16:36:15] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 14.494s
[2025-09-13 16:36:33] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 17.742s
[2025-09-13 16:36:42] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.389s
[2025-09-13 16:36:51] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.376s
[2025-09-13 16:37:01] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.354s
[2025-09-13 16:37:10] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.442s
[2025-09-13 16:37:20] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.283s
[2025-09-13 16:37:29] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.390s
[2025-09-13 16:37:38] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.357s
[2025-09-13 16:37:48] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.388s
[2025-09-13 16:37:57] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.344s
[2025-09-13 16:38:07] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.395s
[2025-09-13 16:38:16] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.388s
[2025-09-13 16:38:25] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.393s
[2025-09-13 16:38:35] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.389s
[2025-09-13 16:38:44] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.368s
[2025-09-13 16:38:54] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.370s
[2025-09-13 16:39:03] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.407s
[2025-09-13 16:39:12] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.395s
[2025-09-13 16:39:22] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.390s
[2025-09-13 16:39:31] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.392s
[2025-09-13 16:39:41] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.380s
[2025-09-13 16:39:50] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.372s
[2025-09-13 16:39:59] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.392s
[2025-09-13 16:40:09] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.395s
[2025-09-13 16:40:18] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.394s
[2025-09-13 16:40:27] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.380s
[2025-09-13 16:40:37] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.371s
[2025-09-13 16:40:46] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.382s
[2025-09-13 16:40:56] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.367s
[2025-09-13 16:41:05] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.374s
[2025-09-13 16:41:14] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.392s
[2025-09-13 16:41:24] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.385s
[2025-09-13 16:41:33] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.385s
[2025-09-13 16:41:43] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.389s
[2025-09-13 16:41:52] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.389s
[2025-09-13 16:42:01] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.395s
[2025-09-13 16:42:11] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.387s
[2025-09-13 16:42:20] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.357s
[2025-09-13 16:42:30] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.395s
[2025-09-13 16:42:39] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.392s
[2025-09-13 16:42:48] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.391s
[2025-09-13 16:42:58] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.383s
[2025-09-13 16:43:07] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.384s
[2025-09-13 16:43:16] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.374s
[2025-09-13 16:43:26] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.361s
[2025-09-13 16:43:35] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.393s
[2025-09-13 16:43:45] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.368s
[2025-09-13 16:43:54] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.405s
[2025-09-13 16:44:03] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.372s
[2025-09-13 16:44:13] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.358s
[2025-09-13 16:44:22] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.385s
[2025-09-13 16:44:32] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.392s
[2025-09-13 16:44:41] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.389s
[2025-09-13 16:44:50] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.367s
[2025-09-13 16:45:00] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.395s
[2025-09-13 16:45:09] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.365s
[2025-09-13 16:45:18] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.396s
[2025-09-13 16:45:28] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.385s
[2025-09-13 16:45:37] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.415s
[2025-09-13 16:45:47] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.362s
[2025-09-13 16:45:56] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.373s
[2025-09-13 16:46:05] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.394s
[2025-09-13 16:46:15] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.392s
[2025-09-13 16:46:15] 自旋相关函数计算完成,总耗时 614.43 秒
[2025-09-13 16:46:16] 计算傅里叶变换...
[2025-09-13 16:46:18] 自旋结构因子计算完成
[2025-09-13 16:46:20] 自旋相关函数平均误差: 0.000652
