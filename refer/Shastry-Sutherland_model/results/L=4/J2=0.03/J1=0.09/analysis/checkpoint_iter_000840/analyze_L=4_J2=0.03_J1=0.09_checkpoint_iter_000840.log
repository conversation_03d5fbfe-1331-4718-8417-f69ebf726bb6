[2025-09-13 16:46:27] 使用checkpoint文件: results/L=4/J2=0.03/J1=0.09/training/checkpoints/checkpoint_iter_000840.pkl
[2025-09-13 16:46:44] ✓ 从checkpoint加载参数: 840
[2025-09-13 16:46:44]   - 能量: -54.963965+0.003240j ± 0.083833
[2025-09-13 16:46:44] ================================================================================
[2025-09-13 16:46:44] 加载量子态: L=4, J2=0.03, J1=0.09, checkpoint=checkpoint_iter_000840
[2025-09-13 16:46:45] 使用采样数目: 1048576
[2025-09-13 16:46:45] 设置样本数为: 1048576
[2025-09-13 16:46:45] 开始生成共享样本集...
[2025-09-13 16:49:47] 样本生成完成,耗时: 182.410 秒
[2025-09-13 16:49:47] ================================================================================
[2025-09-13 16:49:47] 开始计算自旋结构因子...
[2025-09-13 16:49:47] 初始化操作符缓存...
[2025-09-13 16:49:47] 预构建所有自旋相关操作符...
[2025-09-13 16:49:47] 开始计算自旋相关函数...
[2025-09-13 16:50:02] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 14.510s
[2025-09-13 16:50:20] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 18.082s
[2025-09-13 16:50:29] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.320s
[2025-09-13 16:50:38] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.312s
[2025-09-13 16:50:48] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.322s
[2025-09-13 16:50:57] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.327s
[2025-09-13 16:51:06] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.327s
[2025-09-13 16:51:16] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.328s
[2025-09-13 16:51:25] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.325s
[2025-09-13 16:51:34] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.241s
[2025-09-13 16:51:44] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.324s
[2025-09-13 16:51:53] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.327s
[2025-09-13 16:52:02] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.325s
[2025-09-13 16:52:12] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.326s
[2025-09-13 16:52:21] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.312s
[2025-09-13 16:52:30] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.323s
[2025-09-13 16:52:40] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.321s
[2025-09-13 16:52:49] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.326s
[2025-09-13 16:52:58] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.314s
[2025-09-13 16:53:08] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.366s
[2025-09-13 16:53:17] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.256s
[2025-09-13 16:53:26] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.330s
[2025-09-13 16:53:36] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.325s
[2025-09-13 16:53:45] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.327s
[2025-09-13 16:53:54] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.314s
[2025-09-13 16:54:04] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.360s
[2025-09-13 16:54:13] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.324s
[2025-09-13 16:54:22] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.324s
[2025-09-13 16:54:32] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.320s
[2025-09-13 16:54:41] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.333s
[2025-09-13 16:54:50] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.407s
[2025-09-13 16:55:00] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.249s
[2025-09-13 16:55:09] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.330s
[2025-09-13 16:55:18] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.326s
[2025-09-13 16:55:28] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.321s
[2025-09-13 16:55:37] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.312s
[2025-09-13 16:55:46] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.324s
[2025-09-13 16:55:56] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.326s
[2025-09-13 16:56:05] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.334s
[2025-09-13 16:56:14] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.324s
[2025-09-13 16:56:24] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.304s
[2025-09-13 16:56:33] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.316s
[2025-09-13 16:56:42] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.343s
[2025-09-13 16:56:52] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.327s
[2025-09-13 16:57:01] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.330s
[2025-09-13 16:57:10] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.327s
[2025-09-13 16:57:20] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.311s
[2025-09-13 16:57:29] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.322s
[2025-09-13 16:57:38] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.324s
[2025-09-13 16:57:48] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.324s
[2025-09-13 16:57:57] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.325s
[2025-09-13 16:58:06] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.325s
[2025-09-13 16:58:16] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.339s
[2025-09-13 16:58:25] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.327s
[2025-09-13 16:58:34] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.326s
[2025-09-13 16:58:44] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.324s
[2025-09-13 16:58:53] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.314s
[2025-09-13 16:59:02] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.315s
[2025-09-13 16:59:12] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.350s
[2025-09-13 16:59:21] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.319s
[2025-09-13 16:59:30] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.321s
[2025-09-13 16:59:40] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.324s
[2025-09-13 16:59:49] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.325s
[2025-09-13 16:59:58] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.327s
[2025-09-13 16:59:58] 自旋相关函数计算完成,总耗时 611.15 秒
[2025-09-13 16:59:59] 计算傅里叶变换...
[2025-09-13 17:00:01] 自旋结构因子计算完成
[2025-09-13 17:00:02] 自旋相关函数平均误差: 0.000663
