[2025-09-12 04:57:28] ✓ 从checkpoint恢复: results/L=4/J2=0.03/J1=0.08/training/checkpoints/final_GCNN.pkl
[2025-09-12 04:57:28]   - 迭代次数: final
[2025-09-12 04:57:28]   - 能量: -54.513163-0.001287j ± 0.083512
[2025-09-12 04:57:28]   - 时间戳: 2025-09-12T04:57:00.787781+08:00
[2025-09-12 04:57:53] ✓ 变分状态参数已从checkpoint恢复
[2025-09-12 04:57:53] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-12 04:57:53] ==================================================
[2025-09-12 04:57:53] GCNN for Shastry-Sutherland Model
[2025-09-12 04:57:54] ==================================================
[2025-09-12 04:57:54] System parameters:
[2025-09-12 04:57:54]   - System size: L=4, N=64
[2025-09-12 04:57:54]   - System parameters: J1=0.09, J2=0.03, Q=0.97
[2025-09-12 04:57:54] --------------------------------------------------
[2025-09-12 04:57:54] Model parameters:
[2025-09-12 04:57:54]   - Number of layers = 4
[2025-09-12 04:57:54]   - Number of features = 4
[2025-09-12 04:57:54]   - Total parameters = 12572
[2025-09-12 04:57:54] --------------------------------------------------
[2025-09-12 04:57:54] Training parameters:
[2025-09-12 04:57:54]   - Learning rate: 0.015
[2025-09-12 04:57:54]   - Total iterations: 1050
[2025-09-12 04:57:54]   - Annealing cycles: 3
[2025-09-12 04:57:54]   - Initial period: 150
[2025-09-12 04:57:54]   - Period multiplier: 2.0
[2025-09-12 04:57:54]   - Temperature range: 0.0-1.0
[2025-09-12 04:57:54]   - Samples: 4096
[2025-09-12 04:57:54]   - Discarded samples: 0
[2025-09-12 04:57:54]   - Chunk size: 2048
[2025-09-12 04:57:54]   - Diagonal shift: 0.2
[2025-09-12 04:57:54]   - Gradient clipping: 1.0
[2025-09-12 04:57:54]   - Checkpoint enabled: interval=105
[2025-09-12 04:57:54]   - Checkpoint directory: results/L=4/J2=0.03/J1=0.09/training/checkpoints
[2025-09-12 04:57:54] --------------------------------------------------
[2025-09-12 04:57:54] Device status:
[2025-09-12 04:57:54]   - Devices model: NVIDIA H200 NVL
[2025-09-12 04:57:54]   - Number of devices: 1
[2025-09-12 04:57:54]   - Sharding: True
[2025-09-12 04:57:54] ============================================================
[2025-09-12 04:59:49] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -54.890229+0.005697j
[2025-09-12 05:00:57] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -54.940453+0.002343j
[2025-09-12 05:01:12] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -54.873266+0.005779j
[2025-09-12 05:01:28] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -54.957891+0.003183j
[2025-09-12 05:01:43] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -54.939562+0.003573j
[2025-09-12 05:01:58] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -54.905780-0.000398j
[2025-09-12 05:02:14] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -54.958875+0.002404j
[2025-09-12 05:02:29] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -54.926240+0.000446j
[2025-09-12 05:02:45] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -54.883371+0.002975j
[2025-09-12 05:03:00] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -54.840485+0.004168j
[2025-09-12 05:03:15] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -54.903666+0.001053j
[2025-09-12 05:03:31] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -54.998543+0.001395j
[2025-09-12 05:03:46] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -54.925867+0.000600j
[2025-09-12 05:04:02] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -54.997735-0.001055j
[2025-09-12 05:04:17] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -54.854802-0.000373j
[2025-09-12 05:04:33] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -54.933460+0.000473j
[2025-09-12 05:04:48] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -54.946238+0.001450j
[2025-09-12 05:05:03] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -54.934859+0.000002j
[2025-09-12 05:05:19] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -55.008784+0.000120j
[2025-09-12 05:05:34] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -54.907853+0.000263j
[2025-09-12 05:05:49] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -55.067437-0.001208j
[2025-09-12 05:06:05] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -55.137912-0.001260j
[2025-09-12 05:06:20] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -55.140721-0.000407j
[2025-09-12 05:06:36] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -54.976365-0.004326j
[2025-09-12 05:06:51] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -54.988211-0.000881j
[2025-09-12 05:07:06] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -54.893598-0.003121j
[2025-09-12 05:07:22] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -54.867472-0.000600j
[2025-09-12 05:07:37] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -54.882808+0.000717j
[2025-09-12 05:07:53] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -54.854732-0.003758j
[2025-09-12 05:08:08] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -54.832815+0.000137j
[2025-09-12 05:08:23] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -54.806579+0.000855j
[2025-09-12 05:08:39] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -54.925480-0.001525j
[2025-09-12 05:08:54] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -54.917527-0.000713j
[2025-09-12 05:09:09] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -54.798679+0.000633j
[2025-09-12 05:09:25] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -54.761932+0.001194j
[2025-09-12 05:09:40] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -54.869829-0.001666j
[2025-09-12 05:09:56] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -54.861699+0.001974j
[2025-09-12 05:10:11] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -54.956616+0.001035j
[2025-09-12 05:10:26] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -54.890716+0.001332j
[2025-09-12 05:10:42] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -54.895759-0.002483j
[2025-09-12 05:10:57] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -55.021515-0.002820j
[2025-09-12 05:11:13] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -54.984912-0.001547j
[2025-09-12 05:11:28] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -55.077100+0.001666j
[2025-09-12 05:11:43] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -55.022951-0.002391j
[2025-09-12 05:11:59] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -54.986580-0.001479j
[2025-09-12 05:12:14] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -54.956623-0.000481j
[2025-09-12 05:12:29] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -54.989715-0.000136j
[2025-09-12 05:12:45] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -54.981549+0.002871j
[2025-09-12 05:13:00] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -54.921411-0.000192j
[2025-09-12 05:13:15] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -54.850548-0.000778j
[2025-09-12 05:13:31] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -54.993487-0.001478j
[2025-09-12 05:13:46] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -54.887529+0.000637j
[2025-09-12 05:14:02] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -54.793987-0.000207j
[2025-09-12 05:14:17] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -54.818939-0.000035j
[2025-09-12 05:14:32] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -54.829504-0.000741j
[2025-09-12 05:14:48] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -54.736342-0.001024j
[2025-09-12 05:15:03] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -54.849116-0.002499j
[2025-09-12 05:15:19] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -54.919980-0.001912j
[2025-09-12 05:15:34] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -54.981277-0.002369j
[2025-09-12 05:15:49] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -54.923074+0.001223j
[2025-09-12 05:16:05] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -54.911786-0.001175j
[2025-09-12 05:16:20] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -54.842503+0.001706j
[2025-09-12 05:16:36] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -54.917022-0.000702j
[2025-09-12 05:16:51] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -54.934497+0.001398j
[2025-09-12 05:17:06] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -54.963906+0.000411j
[2025-09-12 05:17:22] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -54.907730+0.002820j
[2025-09-12 05:17:37] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -54.921423-0.002023j
[2025-09-12 05:17:53] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -54.964408-0.000629j
[2025-09-12 05:18:08] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -54.931611+0.000185j
[2025-09-12 05:18:23] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -54.826553+0.001460j
[2025-09-12 05:18:39] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -54.775080-0.000583j
[2025-09-12 05:18:54] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -54.945277-0.000347j
[2025-09-12 05:19:10] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -54.906883-0.000957j
[2025-09-12 05:19:25] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -54.841696+0.000202j
[2025-09-12 05:19:40] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -54.851668+0.002084j
[2025-09-12 05:19:56] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -54.860206+0.001834j
[2025-09-12 05:20:11] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -54.801361-0.002271j
[2025-09-12 05:20:26] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -54.851909+0.000963j
[2025-09-12 05:20:42] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -54.910063-0.000205j
[2025-09-12 05:20:57] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -54.780477+0.002321j
[2025-09-12 05:21:13] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -54.795539+0.002360j
[2025-09-12 05:21:28] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -54.765981-0.000033j
[2025-09-12 05:21:43] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -54.819435+0.004308j
[2025-09-12 05:21:59] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -54.931132-0.000479j
[2025-09-12 05:22:14] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -54.870201+0.001724j
[2025-09-12 05:22:30] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -54.976622-0.002212j
[2025-09-12 05:22:45] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -54.985451-0.001134j
[2025-09-12 05:23:00] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -54.851857+0.001102j
[2025-09-12 05:23:16] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -54.985327-0.001720j
[2025-09-12 05:23:31] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -55.036205+0.000164j
[2025-09-12 05:23:46] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -55.009330-0.002412j
[2025-09-12 05:24:02] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -54.805694+0.000199j
[2025-09-12 05:24:17] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -54.825499-0.001482j
[2025-09-12 05:24:33] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -55.022128-0.003686j
[2025-09-12 05:24:45] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -54.900643+0.001267j
[2025-09-12 05:24:55] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -54.888055+0.002670j
[2025-09-12 05:25:05] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -54.857529+0.000128j
[2025-09-12 05:25:16] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -54.907363-0.000991j
[2025-09-12 05:25:26] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -54.866979+0.001216j
[2025-09-12 05:25:37] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -54.894339-0.002891j
[2025-09-12 05:25:49] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -54.890893-0.002314j
[2025-09-12 05:26:04] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -54.843532-0.000874j
[2025-09-12 05:26:20] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -54.700760-0.000349j
[2025-09-12 05:26:35] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -54.849919+0.001565j
[2025-09-12 05:26:51] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -54.802274-0.000753j
[2025-09-12 05:26:51] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-12 05:27:06] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -54.839385-0.002225j
[2025-09-12 05:27:22] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -54.881617+0.001671j
[2025-09-12 05:27:37] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -54.753600+0.001505j
[2025-09-12 05:27:52] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -54.786429-0.001945j
[2025-09-12 05:28:08] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -54.843677+0.001138j
[2025-09-12 05:28:23] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -54.874456-0.003215j
[2025-09-12 05:28:39] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -54.924168-0.001108j
[2025-09-12 05:28:54] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -54.803256+0.002094j
[2025-09-12 05:29:10] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -54.847107-0.003457j
[2025-09-12 05:29:25] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -55.096076+0.002043j
[2025-09-12 05:29:40] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -54.767495-0.000197j
[2025-09-12 05:29:56] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -54.888051-0.001042j
[2025-09-12 05:30:11] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -54.741280-0.003554j
[2025-09-12 05:30:27] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -54.785691-0.000349j
[2025-09-12 05:30:42] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -54.866195+0.001118j
[2025-09-12 05:30:58] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -54.883702-0.000904j
[2025-09-12 05:31:13] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -54.921976-0.001676j
[2025-09-12 05:31:29] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -54.873808+0.000081j
[2025-09-12 05:31:44] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -54.859870+0.002040j
[2025-09-12 05:31:59] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -54.835960-0.001996j
[2025-09-12 05:32:15] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -54.847651-0.002104j
[2025-09-12 05:32:30] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -54.867834-0.004890j
[2025-09-12 05:32:46] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -54.809956-0.000007j
[2025-09-12 05:33:01] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -54.910180+0.003699j
[2025-09-12 05:33:17] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -54.911980+0.000119j
[2025-09-12 05:33:32] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -54.924781-0.000250j
[2025-09-12 05:33:48] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -54.933511+0.000811j
[2025-09-12 05:34:03] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -54.850292+0.000670j
[2025-09-12 05:34:18] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -54.837549-0.002756j
[2025-09-12 05:34:34] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -54.842705+0.000102j
[2025-09-12 05:34:49] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -54.865967+0.000034j
[2025-09-12 05:35:05] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -54.823319-0.001004j
[2025-09-12 05:35:18] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -54.912467-0.000653j
[2025-09-12 05:35:29] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -54.880783+0.002889j
[2025-09-12 05:35:43] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -54.848032-0.003041j
[2025-09-12 05:35:53] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -54.969941+0.000709j
[2025-09-12 05:36:09] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -54.884557-0.000515j
[2025-09-12 05:36:24] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -54.908792+0.001118j
[2025-09-12 05:36:39] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -54.846742-0.000065j
[2025-09-12 05:36:55] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -54.917052-0.005802j
[2025-09-12 05:37:10] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -54.836841+0.002921j
[2025-09-12 05:37:25] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -54.924498-0.002446j
[2025-09-12 05:37:41] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -54.779152+0.005639j
[2025-09-12 05:37:56] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -54.984500-0.000896j
[2025-09-12 05:38:11] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -54.882054+0.004186j
[2025-09-12 05:38:12] RESTART #1 | Period: 300
[2025-09-12 05:38:27] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -54.971792-0.002605j
[2025-09-12 05:38:42] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -54.928407+0.000788j
[2025-09-12 05:38:58] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -54.916803-0.000531j
[2025-09-12 05:39:13] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -54.934213+0.000534j
[2025-09-12 05:39:28] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -55.011135-0.001171j
[2025-09-12 05:39:44] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -54.901521-0.000993j
[2025-09-12 05:39:59] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -54.903744+0.003468j
[2025-09-12 05:40:15] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -54.842523-0.000310j
[2025-09-12 05:40:30] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -54.904376+0.001269j
[2025-09-12 05:40:45] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -54.843040+0.000895j
[2025-09-12 05:41:01] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -54.773713-0.003074j
[2025-09-12 05:41:16] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -54.896209+0.002716j
[2025-09-12 05:41:31] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -54.886311+0.000557j
[2025-09-12 05:41:47] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -55.021074+0.004001j
[2025-09-12 05:42:02] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -54.982886+0.001832j
[2025-09-12 05:42:18] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -54.982010-0.000267j
[2025-09-12 05:42:33] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -54.940828-0.003564j
[2025-09-12 05:42:48] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -54.960414+0.000674j
[2025-09-12 05:43:04] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -54.953023-0.000382j
[2025-09-12 05:43:19] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -54.991195-0.002973j
[2025-09-12 05:43:34] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -55.055637+0.001837j
[2025-09-12 05:43:50] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -54.883328+0.001473j
[2025-09-12 05:44:05] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -55.055597-0.002138j
[2025-09-12 05:44:21] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -54.937153-0.002709j
[2025-09-12 05:44:36] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -54.916301+0.002224j
[2025-09-12 05:44:52] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -54.833312-0.002310j
[2025-09-12 05:45:07] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -54.830978-0.000038j
[2025-09-12 05:45:22] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -54.853841+0.000728j
[2025-09-12 05:45:38] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -54.870841+0.002516j
[2025-09-12 05:45:53] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -54.872117+0.002583j
[2025-09-12 05:46:08] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -54.839233-0.000486j
[2025-09-12 05:46:24] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -54.880731-0.000955j
[2025-09-12 05:46:39] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -54.910002-0.000366j
[2025-09-12 05:46:55] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -54.776919-0.000248j
[2025-09-12 05:47:10] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -54.887863+0.000506j
[2025-09-12 05:47:25] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -54.857890+0.001547j
[2025-09-12 05:47:41] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -54.730286-0.002045j
[2025-09-12 05:47:56] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -54.895546+0.002779j
[2025-09-12 05:48:12] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -54.963715-0.003181j
[2025-09-12 05:48:27] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -54.919188+0.002068j
[2025-09-12 05:48:43] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -54.886547+0.000045j
[2025-09-12 05:48:58] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -54.973513-0.001041j
[2025-09-12 05:49:13] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -54.900062+0.001062j
[2025-09-12 05:49:29] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -55.005688+0.002552j
[2025-09-12 05:49:44] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -55.064645-0.000165j
[2025-09-12 05:49:59] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -54.948520+0.001759j
[2025-09-12 05:50:15] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -54.819764-0.000045j
[2025-09-12 05:50:30] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -54.963842-0.001638j
[2025-09-12 05:50:45] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -55.090160+0.000158j
[2025-09-12 05:51:01] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -54.963709-0.001139j
[2025-09-12 05:51:16] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -54.916777-0.002054j
[2025-09-12 05:51:32] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -54.881297+0.000572j
[2025-09-12 05:51:47] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -54.913999-0.004272j
[2025-09-12 05:52:02] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -54.932358+0.001226j
[2025-09-12 05:52:18] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -54.860383+0.001184j
[2025-09-12 05:52:33] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -54.953594+0.000217j
[2025-09-12 05:52:49] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -54.916631+0.001652j
[2025-09-12 05:53:04] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -55.031012+0.001679j
[2025-09-12 05:53:20] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -54.906670+0.001812j
[2025-09-12 05:53:35] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -54.838965+0.001397j
[2025-09-12 05:53:35] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-12 05:53:50] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -54.864096-0.000527j
[2025-09-12 05:54:06] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -54.789702-0.000866j
[2025-09-12 05:54:21] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -55.015338+0.000406j
[2025-09-12 05:54:37] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -54.869863-0.001181j
[2025-09-12 05:54:52] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -54.825980+0.002117j
[2025-09-12 05:55:07] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -54.823481+0.000017j
[2025-09-12 05:55:23] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -54.855986+0.001210j
[2025-09-12 05:55:38] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -54.866005-0.001106j
[2025-09-12 05:55:54] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -54.832645+0.003529j
[2025-09-12 05:56:09] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -54.815915+0.000041j
[2025-09-12 05:56:24] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -54.979434-0.000554j
[2025-09-12 05:56:40] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -54.920453-0.001498j
[2025-09-12 05:56:55] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -54.863352-0.002026j
[2025-09-12 05:57:11] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -54.962268-0.003618j
[2025-09-12 05:57:26] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -54.853875-0.000276j
[2025-09-12 05:57:41] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -54.836515+0.000031j
[2025-09-12 05:57:57] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -54.882157-0.001690j
[2025-09-12 05:58:12] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -54.807970+0.001891j
[2025-09-12 05:58:28] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -54.961461-0.000879j
[2025-09-12 05:58:43] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -55.079803+0.000929j
[2025-09-12 05:58:58] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -54.989600-0.000069j
[2025-09-12 05:59:14] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -54.770674-0.000941j
[2025-09-12 05:59:29] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -54.724564-0.000416j
[2025-09-12 05:59:45] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -54.901778-0.000359j
[2025-09-12 06:00:00] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -54.895084+0.001943j
[2025-09-12 06:00:16] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -54.795093+0.001133j
[2025-09-12 06:00:31] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -54.917219+0.000304j
[2025-09-12 06:00:46] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -54.921663-0.002527j
[2025-09-12 06:01:02] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -54.929127-0.000456j
[2025-09-12 06:01:17] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -54.939636-0.000267j
[2025-09-12 06:01:32] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -55.017632+0.000753j
[2025-09-12 06:01:48] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -54.917873+0.002742j
[2025-09-12 06:02:03] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -54.833053-0.002747j
[2025-09-12 06:02:19] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -54.855713+0.004193j
[2025-09-12 06:02:34] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -54.861050-0.001786j
[2025-09-12 06:02:49] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -54.915058+0.000803j
[2025-09-12 06:03:05] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -54.765234-0.000871j
[2025-09-12 06:03:20] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -55.021644-0.002572j
[2025-09-12 06:03:36] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -54.913136-0.000298j
[2025-09-12 06:03:51] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -54.900177-0.001377j
[2025-09-12 06:04:06] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -54.907591-0.000974j
[2025-09-12 06:04:22] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -54.890997+0.002021j
[2025-09-12 06:04:37] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -54.919699-0.000821j
[2025-09-12 06:04:52] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -54.771513+0.002076j
[2025-09-12 06:05:08] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -54.911339-0.000989j
[2025-09-12 06:05:23] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -54.800241+0.001276j
[2025-09-12 06:05:39] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -54.927671-0.000449j
[2025-09-12 06:05:54] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -55.021895-0.001292j
[2025-09-12 06:06:10] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -54.985539+0.000061j
[2025-09-12 06:06:25] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -54.956157-0.002087j
[2025-09-12 06:06:40] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -54.863975-0.000889j
[2025-09-12 06:06:56] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -54.826330+0.002486j
[2025-09-12 06:07:11] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -54.881500+0.002330j
[2025-09-12 06:07:27] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -54.792626-0.001663j
[2025-09-12 06:07:42] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -54.988478-0.000329j
[2025-09-12 06:07:57] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -54.857236+0.000887j
[2025-09-12 06:08:13] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -54.873722+0.001183j
[2025-09-12 06:08:28] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -54.900538+0.000812j
[2025-09-12 06:08:43] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -54.855372-0.001028j
[2025-09-12 06:08:59] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -54.847240-0.000859j
[2025-09-12 06:09:14] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -54.734202+0.002746j
[2025-09-12 06:09:30] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -54.827617-0.000074j
[2025-09-12 06:09:43] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -54.864514-0.000426j
[2025-09-12 06:09:54] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -54.869286+0.001565j
[2025-09-12 06:10:04] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -54.833991-0.000932j
[2025-09-12 06:10:14] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -54.959116-0.002078j
[2025-09-12 06:10:25] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -54.859961+0.003727j
[2025-09-12 06:10:35] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -54.878517-0.000155j
[2025-09-12 06:10:45] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -54.918956-0.000881j
[2025-09-12 06:11:00] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -54.782051+0.000802j
[2025-09-12 06:11:15] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -54.799660-0.000894j
[2025-09-12 06:11:30] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -54.847021-0.003911j
[2025-09-12 06:11:46] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -54.872332-0.000464j
[2025-09-12 06:12:01] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -54.917987+0.001134j
[2025-09-12 06:12:17] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -54.855642+0.002677j
[2025-09-12 06:12:32] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -54.981307+0.000763j
[2025-09-12 06:12:48] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -55.014586-0.003252j
[2025-09-12 06:13:03] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -54.939987-0.003117j
[2025-09-12 06:13:18] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -54.755762-0.000249j
[2025-09-12 06:13:34] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -54.847317+0.003357j
[2025-09-12 06:13:49] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -54.916547-0.001746j
[2025-09-12 06:14:04] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -54.884122-0.000994j
[2025-09-12 06:14:20] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -55.008148+0.000318j
[2025-09-12 06:14:35] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -55.006829+0.002355j
[2025-09-12 06:14:50] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -54.819873+0.002280j
[2025-09-12 06:15:06] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -54.902716+0.000594j
[2025-09-12 06:15:21] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -54.936915+0.000612j
[2025-09-12 06:15:37] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -55.006512+0.000142j
[2025-09-12 06:15:52] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -54.906922+0.000178j
[2025-09-12 06:16:07] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -54.943429-0.000445j
[2025-09-12 06:16:23] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -54.922258-0.000707j
[2025-09-12 06:16:38] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -54.925759+0.001470j
[2025-09-12 06:16:54] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -54.836356+0.000746j
[2025-09-12 06:17:09] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -54.893833+0.003401j
[2025-09-12 06:17:25] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -54.972761+0.002122j
[2025-09-12 06:17:40] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -54.842789-0.000122j
[2025-09-12 06:17:56] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -54.875250-0.001199j
[2025-09-12 06:18:11] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -55.102806-0.001493j
[2025-09-12 06:18:26] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -55.053501-0.002320j
[2025-09-12 06:18:42] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -54.851584+0.000379j
[2025-09-12 06:18:57] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -54.917066+0.000142j
[2025-09-12 06:19:13] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -54.801756+0.003311j
[2025-09-12 06:19:28] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -54.814558+0.000984j
[2025-09-12 06:19:44] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -54.884131-0.000399j
[2025-09-12 06:19:59] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -54.891615-0.000138j
[2025-09-12 06:19:59] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-12 06:20:15] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -54.941227+0.001002j
[2025-09-12 06:20:26] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -54.723501+0.000265j
[2025-09-12 06:20:38] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -54.738097+0.000160j
[2025-09-12 06:20:51] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -54.855974-0.000657j
[2025-09-12 06:21:03] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -54.986796-0.000443j
[2025-09-12 06:21:18] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -54.858707+0.001945j
[2025-09-12 06:21:34] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -55.060185-0.000630j
[2025-09-12 06:21:49] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -55.005965+0.000710j
[2025-09-12 06:22:04] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -54.865689-0.000669j
[2025-09-12 06:22:20] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -54.950317-0.002549j
[2025-09-12 06:22:35] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -54.974934+0.000334j
[2025-09-12 06:22:51] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -54.865715+0.000313j
[2025-09-12 06:23:06] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -54.966463+0.002389j
[2025-09-12 06:23:21] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -54.933085-0.000189j
[2025-09-12 06:23:37] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -54.913549-0.002240j
[2025-09-12 06:23:52] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -54.948111+0.004601j
[2025-09-12 06:24:07] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -54.896962-0.000451j
[2025-09-12 06:24:23] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -54.948663+0.000836j
[2025-09-12 06:24:38] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -55.000632-0.001567j
[2025-09-12 06:24:54] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -54.991405-0.002471j
[2025-09-12 06:25:09] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -54.960261+0.001739j
[2025-09-12 06:25:24] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -54.891678+0.000006j
[2025-09-12 06:25:40] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -54.932240+0.001005j
[2025-09-12 06:25:55] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -54.854606+0.001758j
[2025-09-12 06:26:10] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -54.825191+0.000270j
[2025-09-12 06:26:26] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -54.861196+0.004442j
[2025-09-12 06:26:41] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -54.955191-0.001956j
[2025-09-12 06:26:56] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -54.841145+0.001837j
[2025-09-12 06:27:12] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -54.970731+0.004535j
[2025-09-12 06:27:27] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -55.107775+0.000766j
[2025-09-12 06:27:43] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -54.977999+0.000677j
[2025-09-12 06:27:58] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -54.943912+0.000659j
[2025-09-12 06:28:13] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -54.729605-0.002922j
[2025-09-12 06:28:29] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -54.831044-0.000392j
[2025-09-12 06:28:44] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -54.764559+0.000978j
[2025-09-12 06:28:59] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -54.796903+0.003681j
[2025-09-12 06:29:15] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -54.808547-0.001293j
[2025-09-12 06:29:30] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -54.875899-0.000798j
[2025-09-12 06:29:45] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -54.935866+0.001127j
[2025-09-12 06:30:01] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -54.948225+0.002016j
[2025-09-12 06:30:16] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -54.943214+0.000090j
[2025-09-12 06:30:31] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -54.945197-0.001196j
[2025-09-12 06:30:47] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -54.812732-0.003091j
[2025-09-12 06:31:02] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -54.759458-0.002947j
[2025-09-12 06:31:18] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -54.809290-0.002325j
[2025-09-12 06:31:33] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -54.979835+0.001698j
[2025-09-12 06:31:48] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -54.962964+0.001646j
[2025-09-12 06:32:04] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -54.851666-0.000468j
[2025-09-12 06:32:19] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -54.814581-0.002756j
[2025-09-12 06:32:34] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -54.764451+0.000055j
[2025-09-12 06:32:50] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -54.783570-0.003368j
[2025-09-12 06:33:05] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -54.847662+0.000751j
[2025-09-12 06:33:21] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -54.734301+0.000934j
[2025-09-12 06:33:36] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -54.864107-0.000882j
[2025-09-12 06:33:51] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -54.917525+0.001846j
[2025-09-12 06:34:07] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -54.983767+0.001101j
[2025-09-12 06:34:22] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -54.909767+0.000131j
[2025-09-12 06:34:38] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -54.837498-0.000902j
[2025-09-12 06:34:53] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -54.850250+0.002077j
[2025-09-12 06:35:09] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -54.863491-0.001880j
[2025-09-12 06:35:24] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -54.946905-0.002761j
[2025-09-12 06:35:39] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -54.888201+0.001417j
[2025-09-12 06:35:55] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -54.896982-0.001928j
[2025-09-12 06:36:10] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -54.999830-0.002285j
[2025-09-12 06:36:25] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -54.880602+0.002990j
[2025-09-12 06:36:41] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -54.913054+0.001458j
[2025-09-12 06:36:56] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -54.840532+0.000451j
[2025-09-12 06:37:12] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -54.910361-0.000284j
[2025-09-12 06:37:27] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -55.021549+0.000648j
[2025-09-12 06:37:42] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -55.037903+0.000159j
[2025-09-12 06:37:58] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -54.929083+0.002605j
[2025-09-12 06:38:13] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -55.011721-0.002021j
[2025-09-12 06:38:29] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -54.846869-0.001578j
[2025-09-12 06:38:44] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -54.912084-0.001346j
[2025-09-12 06:38:59] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -54.971104+0.000896j
[2025-09-12 06:39:15] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -54.946811+0.002069j
[2025-09-12 06:39:30] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -54.855850+0.000045j
[2025-09-12 06:39:45] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -54.912580-0.000051j
[2025-09-12 06:40:01] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -54.947613-0.001370j
[2025-09-12 06:40:16] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -54.932748+0.002267j
[2025-09-12 06:40:32] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -54.918595+0.000592j
[2025-09-12 06:40:47] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -55.001721-0.001728j
[2025-09-12 06:41:02] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -55.000674-0.002644j
[2025-09-12 06:41:18] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -54.908097+0.001352j
[2025-09-12 06:41:33] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -54.891691-0.003420j
[2025-09-12 06:41:48] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -54.856348+0.000536j
[2025-09-12 06:42:04] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -54.902616-0.001055j
[2025-09-12 06:42:19] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -54.896693-0.001816j
[2025-09-12 06:42:34] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -54.914684+0.001225j
[2025-09-12 06:42:50] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -54.797108+0.001660j
[2025-09-12 06:43:05] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -54.797594+0.000935j
[2025-09-12 06:43:20] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -54.774086-0.001741j
[2025-09-12 06:43:36] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -54.693561+0.000323j
[2025-09-12 06:43:51] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -54.928768+0.001806j
[2025-09-12 06:44:07] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -54.980093-0.000755j
[2025-09-12 06:44:22] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -55.007361-0.003053j
[2025-09-12 06:44:37] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -54.888451-0.000178j
[2025-09-12 06:44:53] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -54.808554-0.003694j
[2025-09-12 06:45:08] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -54.773119-0.001215j
[2025-09-12 06:45:23] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -54.790023+0.003838j
[2025-09-12 06:45:39] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -54.787978+0.000658j
[2025-09-12 06:45:54] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -54.858455-0.002762j
[2025-09-12 06:46:09] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -54.847247-0.000601j
[2025-09-12 06:46:25] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -54.830833-0.002264j
[2025-09-12 06:46:40] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -54.972107+0.001443j
[2025-09-12 06:46:40] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-12 06:46:56] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -54.889358+0.000036j
[2025-09-12 06:47:11] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -54.858006+0.000430j
[2025-09-12 06:47:27] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -54.781493-0.004441j
[2025-09-12 06:47:42] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -54.820084-0.001099j
[2025-09-12 06:47:57] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -54.920974+0.001528j
[2025-09-12 06:48:13] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -54.687345+0.002436j
[2025-09-12 06:48:28] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -54.907349-0.000323j
[2025-09-12 06:48:43] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -55.003120+0.001621j
[2025-09-12 06:48:59] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -55.011507+0.002150j
[2025-09-12 06:49:14] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -54.900985+0.002003j
[2025-09-12 06:49:30] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -54.907914-0.000374j
[2025-09-12 06:49:45] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -54.741751-0.000842j
[2025-09-12 06:50:00] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -54.733688+0.000444j
[2025-09-12 06:50:16] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -54.773929+0.000448j
[2025-09-12 06:50:31] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -54.846511-0.002510j
[2025-09-12 06:50:46] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -54.789788-0.000388j
[2025-09-12 06:51:02] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -54.790973-0.000832j
[2025-09-12 06:51:17] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -54.708234-0.002968j
[2025-09-12 06:51:33] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -54.855866-0.000246j
[2025-09-12 06:51:48] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -54.941422+0.003626j
[2025-09-12 06:52:03] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -54.896179-0.001884j
[2025-09-12 06:52:19] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -54.961286-0.003728j
[2025-09-12 06:52:34] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -54.991052-0.001799j
[2025-09-12 06:52:50] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -55.105677-0.002603j
[2025-09-12 06:53:05] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -55.125079-0.002102j
[2025-09-12 06:53:20] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -54.908042-0.000819j
[2025-09-12 06:53:36] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -55.039230+0.000444j
[2025-09-12 06:53:51] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -54.927713+0.003945j
[2025-09-12 06:54:06] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -54.876149-0.003180j
[2025-09-12 06:54:22] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -54.880438-0.000503j
[2025-09-12 06:54:22] RESTART #2 | Period: 600
[2025-09-12 06:54:37] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -54.959153+0.000403j
[2025-09-12 06:54:50] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -54.803179-0.001048j
[2025-09-12 06:55:00] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -54.919912+0.001059j
[2025-09-12 06:55:10] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -54.871068-0.000291j
[2025-09-12 06:55:21] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -54.936130-0.000767j
[2025-09-12 06:55:31] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -54.968876+0.000213j
[2025-09-12 06:55:41] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -54.913233-0.003221j
[2025-09-12 06:55:51] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -54.867457-0.000744j
[2025-09-12 06:56:04] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -54.880879+0.002812j
[2025-09-12 06:56:20] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -55.072137+0.001260j
[2025-09-12 06:56:35] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -54.938193-0.000545j
[2025-09-12 06:56:51] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -54.965682-0.002005j
[2025-09-12 06:57:06] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -54.990160-0.001636j
[2025-09-12 06:57:22] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -54.974842+0.002181j
[2025-09-12 06:57:37] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -55.022953+0.001281j
[2025-09-12 06:57:52] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -55.011424+0.002839j
[2025-09-12 06:58:08] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -54.941385-0.000162j
[2025-09-12 06:58:23] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -55.057921-0.001556j
[2025-09-12 06:58:39] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -54.968704+0.001647j
[2025-09-12 06:58:54] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -54.933543-0.000219j
[2025-09-12 06:59:10] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -54.984390+0.000651j
[2025-09-12 06:59:25] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -55.007806-0.001434j
[2025-09-12 06:59:41] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -54.904324-0.001048j
[2025-09-12 06:59:56] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -54.882172-0.000377j
[2025-09-12 07:00:11] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -54.932934-0.000401j
[2025-09-12 07:00:27] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -54.969382-0.000354j
[2025-09-12 07:00:42] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -55.008573-0.001914j
[2025-09-12 07:00:58] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -54.785700+0.001234j
[2025-09-12 07:01:13] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -54.971109+0.001621j
[2025-09-12 07:01:29] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -54.959200-0.003371j
[2025-09-12 07:01:44] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -54.863441+0.000155j
[2025-09-12 07:02:00] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -54.829822-0.002806j
[2025-09-12 07:02:15] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -54.894030-0.000671j
[2025-09-12 07:02:30] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -54.922783+0.000148j
[2025-09-12 07:02:46] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -54.906680+0.000365j
[2025-09-12 07:03:01] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -54.926243-0.002479j
[2025-09-12 07:03:17] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -54.922741-0.002750j
[2025-09-12 07:03:32] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -54.926503-0.000643j
[2025-09-12 07:03:48] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -54.969340-0.001665j
[2025-09-12 07:04:03] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -55.007662+0.001390j
[2025-09-12 07:04:18] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -55.031566-0.000914j
[2025-09-12 07:04:34] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -54.914682-0.001129j
[2025-09-12 07:04:49] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -54.918779+0.000796j
[2025-09-12 07:05:04] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -54.882307-0.000345j
[2025-09-12 07:05:20] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -55.011710+0.000398j
[2025-09-12 07:05:32] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -54.906045-0.001547j
[2025-09-12 07:05:44] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -54.919686+0.003395j
[2025-09-12 07:05:57] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -54.964377-0.000442j
[2025-09-12 07:06:08] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -54.849744-0.000132j
[2025-09-12 07:06:24] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -54.937926-0.002293j
[2025-09-12 07:06:39] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -54.932087+0.002384j
[2025-09-12 07:06:54] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -54.953766-0.002277j
[2025-09-12 07:07:10] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -54.908912+0.000459j
[2025-09-12 07:07:25] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -54.912363-0.001273j
[2025-09-12 07:07:41] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -54.902031+0.000184j
[2025-09-12 07:07:56] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -54.732771+0.000465j
[2025-09-12 07:08:11] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -54.743129+0.004682j
[2025-09-12 07:08:27] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -54.766358+0.001487j
[2025-09-12 07:08:42] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -54.737056-0.001374j
[2025-09-12 07:08:57] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -54.939279+0.003184j
[2025-09-12 07:09:13] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -54.939313-0.000017j
[2025-09-12 07:09:28] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -54.868260+0.000868j
[2025-09-12 07:09:44] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -54.849474-0.002591j
[2025-09-12 07:09:59] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -54.834214+0.001044j
[2025-09-12 07:10:15] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -54.881692+0.001988j
[2025-09-12 07:10:30] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -54.742839+0.000418j
[2025-09-12 07:10:45] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -54.798654+0.001576j
[2025-09-12 07:11:01] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -54.780436+0.003351j
[2025-09-12 07:11:16] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -54.859351+0.001014j
[2025-09-12 07:11:32] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -54.910146+0.001202j
[2025-09-12 07:11:47] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -54.905137+0.000281j
[2025-09-12 07:12:02] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -54.892087-0.001880j
[2025-09-12 07:12:18] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -54.847709+0.000537j
[2025-09-12 07:12:33] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -54.925675+0.001049j
[2025-09-12 07:12:49] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -54.900547-0.003864j
[2025-09-12 07:12:49] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-12 07:13:04] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -54.875080+0.002172j
[2025-09-12 07:13:19] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -54.901505-0.003943j
[2025-09-12 07:13:35] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -54.768523+0.002485j
[2025-09-12 07:13:50] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -54.704254+0.000469j
[2025-09-12 07:14:06] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -54.829858+0.001971j
[2025-09-12 07:14:21] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -54.903910-0.002123j
[2025-09-12 07:14:36] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -54.780100+0.002004j
[2025-09-12 07:14:52] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -54.825222-0.001173j
[2025-09-12 07:15:07] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -54.736149+0.000041j
[2025-09-12 07:15:22] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -54.830165+0.001027j
[2025-09-12 07:15:38] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -54.728299+0.000541j
[2025-09-12 07:15:53] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -54.901716+0.001856j
[2025-09-12 07:16:09] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -54.839607-0.000200j
[2025-09-12 07:16:24] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -54.937915+0.000637j
[2025-09-12 07:16:39] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -54.950900-0.000634j
[2025-09-12 07:16:55] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -54.857648+0.001320j
[2025-09-12 07:17:10] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -54.980138-0.000639j
[2025-09-12 07:17:25] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -54.903502-0.003742j
[2025-09-12 07:17:41] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -54.898565+0.001971j
[2025-09-12 07:17:56] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -55.053626+0.003439j
[2025-09-12 07:18:11] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -54.973239+0.000743j
[2025-09-12 07:18:27] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -54.960342-0.001196j
[2025-09-12 07:18:42] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -55.016957+0.000887j
[2025-09-12 07:18:57] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -54.982149+0.001401j
[2025-09-12 07:19:13] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -54.959390+0.000504j
[2025-09-12 07:19:28] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -54.933855+0.000871j
[2025-09-12 07:19:44] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -54.827836-0.001678j
[2025-09-12 07:19:59] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -54.762081-0.002044j
[2025-09-12 07:20:14] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -54.835655-0.001184j
[2025-09-12 07:20:30] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -54.976476+0.000349j
[2025-09-12 07:20:45] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -54.892613+0.000993j
[2025-09-12 07:21:01] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -54.956857-0.001412j
[2025-09-12 07:21:16] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -55.042636-0.001443j
[2025-09-12 07:21:32] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -55.086339+0.006259j
[2025-09-12 07:21:47] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -55.025737-0.000584j
[2025-09-12 07:22:02] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -55.019299+0.001124j
[2025-09-12 07:22:18] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -54.993528+0.000393j
[2025-09-12 07:22:33] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -54.923864+0.000925j
[2025-09-12 07:22:48] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -54.874815-0.000409j
[2025-09-12 07:23:04] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -54.739201-0.000939j
[2025-09-12 07:23:19] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -54.886584+0.002855j
[2025-09-12 07:23:35] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -54.832939+0.000490j
[2025-09-12 07:23:50] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -54.879178-0.003457j
[2025-09-12 07:24:05] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -54.993356-0.000399j
[2025-09-12 07:24:21] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -54.846974+0.000856j
[2025-09-12 07:24:36] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -54.917097-0.001542j
[2025-09-12 07:24:52] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -54.820101-0.000105j
[2025-09-12 07:25:07] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -54.823735+0.000413j
[2025-09-12 07:25:23] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -54.894668-0.000267j
[2025-09-12 07:25:38] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -54.799577-0.002120j
[2025-09-12 07:25:53] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -54.895214+0.002333j
[2025-09-12 07:26:09] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -54.820231-0.002688j
[2025-09-12 07:26:24] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -54.934514-0.001233j
[2025-09-12 07:26:39] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -54.865242-0.001865j
[2025-09-12 07:26:55] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -54.866795-0.000330j
[2025-09-12 07:27:10] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -54.844381-0.001285j
[2025-09-12 07:27:25] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -54.885321+0.000702j
[2025-09-12 07:27:41] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -54.975285-0.000562j
[2025-09-12 07:27:56] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -54.962616+0.000762j
[2025-09-12 07:28:11] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -54.950573+0.000577j
[2025-09-12 07:28:27] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -54.851381-0.000217j
[2025-09-12 07:28:42] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -54.967582-0.000930j
[2025-09-12 07:28:57] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -55.003008-0.000994j
[2025-09-12 07:29:13] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -54.957583+0.000697j
[2025-09-12 07:29:28] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -55.043621-0.002792j
[2025-09-12 07:29:43] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -54.995543+0.003908j
[2025-09-12 07:29:59] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -54.849966-0.000033j
[2025-09-12 07:30:14] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -54.921153-0.000105j
[2025-09-12 07:30:29] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -54.889793-0.000955j
[2025-09-12 07:30:45] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -54.868367-0.000626j
[2025-09-12 07:31:00] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -54.877310+0.000453j
[2025-09-12 07:31:16] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -54.908669+0.000720j
[2025-09-12 07:31:31] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -54.735538+0.000113j
[2025-09-12 07:31:46] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -54.915978+0.002726j
[2025-09-12 07:32:02] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -54.888256-0.001220j
[2025-09-12 07:32:17] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -54.888987+0.000764j
[2025-09-12 07:32:33] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -54.969802-0.001122j
[2025-09-12 07:32:48] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -54.840306-0.000448j
[2025-09-12 07:33:03] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -54.989358+0.001158j
[2025-09-12 07:33:19] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -54.904985-0.003262j
[2025-09-12 07:33:34] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -54.971743+0.002038j
[2025-09-12 07:33:50] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -54.960306-0.005021j
[2025-09-12 07:34:05] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -54.958530+0.002556j
[2025-09-12 07:34:20] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -54.893389+0.001869j
[2025-09-12 07:34:36] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -54.904135-0.000856j
[2025-09-12 07:34:51] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -54.896935-0.001137j
[2025-09-12 07:35:07] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -54.848355+0.000619j
[2025-09-12 07:35:22] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -54.827193+0.001010j
[2025-09-12 07:35:37] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -54.855248+0.000066j
[2025-09-12 07:35:53] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -54.745348+0.000879j
[2025-09-12 07:36:08] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -54.880755-0.001648j
[2025-09-12 07:36:24] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -54.905283-0.000542j
[2025-09-12 07:36:39] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -54.765899+0.000003j
[2025-09-12 07:36:54] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -54.717643+0.003220j
[2025-09-12 07:37:10] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -54.838679-0.001833j
[2025-09-12 07:37:25] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -55.060377-0.001234j
[2025-09-12 07:37:41] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -54.924350+0.001237j
[2025-09-12 07:37:56] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -54.922790-0.000539j
[2025-09-12 07:38:11] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -54.834512+0.001380j
[2025-09-12 07:38:27] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -54.958543-0.000859j
[2025-09-12 07:38:42] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -54.984947+0.000116j
[2025-09-12 07:38:58] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -54.994380-0.000980j
[2025-09-12 07:39:13] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -54.746352-0.001368j
[2025-09-12 07:39:27] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -54.793155-0.000146j
[2025-09-12 07:39:37] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -55.055682+0.002800j
[2025-09-12 07:39:37] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-12 07:39:48] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -54.942991+0.000428j
[2025-09-12 07:39:58] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -54.855421+0.000017j
[2025-09-12 07:40:08] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -54.832866+0.000697j
[2025-09-12 07:40:18] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -54.948136+0.001997j
[2025-09-12 07:40:29] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -54.948139-0.001137j
[2025-09-12 07:40:44] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -54.838806-0.000582j
[2025-09-12 07:40:59] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -54.830504+0.002143j
[2025-09-12 07:41:15] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -54.905654+0.000316j
[2025-09-12 07:41:30] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -54.772970+0.000254j
[2025-09-12 07:41:46] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -54.794377-0.003945j
[2025-09-12 07:42:01] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -54.780491-0.000904j
[2025-09-12 07:42:17] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -54.827083+0.002137j
[2025-09-12 07:42:32] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -54.872016+0.004573j
[2025-09-12 07:42:48] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -54.905672-0.001443j
[2025-09-12 07:43:03] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -54.800117+0.000714j
[2025-09-12 07:43:18] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -54.860477+0.001635j
[2025-09-12 07:43:34] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -54.851789+0.000447j
[2025-09-12 07:43:49] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -54.819405-0.001065j
[2025-09-12 07:44:05] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -54.761881-0.000680j
[2025-09-12 07:44:20] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -54.731748+0.001876j
[2025-09-12 07:44:35] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -54.747917-0.001260j
[2025-09-12 07:44:51] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -54.827116-0.003000j
[2025-09-12 07:45:06] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -54.841061+0.001589j
[2025-09-12 07:45:22] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -54.806891-0.001815j
[2025-09-12 07:45:37] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -55.004537-0.005424j
[2025-09-12 07:45:53] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -54.950093+0.005363j
[2025-09-12 07:46:08] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -55.003293+0.002126j
[2025-09-12 07:46:24] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -54.913702+0.000969j
[2025-09-12 07:46:39] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -54.945061+0.000820j
[2025-09-12 07:46:54] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -54.867041+0.001816j
[2025-09-12 07:47:10] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -55.019841+0.001880j
[2025-09-12 07:47:25] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -54.991197+0.000859j
[2025-09-12 07:47:41] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -55.001490-0.000517j
[2025-09-12 07:47:56] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -54.940117-0.002883j
[2025-09-12 07:48:12] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -54.920090-0.001082j
[2025-09-12 07:48:27] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -54.751283-0.001669j
[2025-09-12 07:48:42] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -54.767042+0.000604j
[2025-09-12 07:48:58] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -54.776639+0.000588j
[2025-09-12 07:49:13] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -54.883898-0.000320j
[2025-09-12 07:49:29] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -54.756376-0.000057j
[2025-09-12 07:49:44] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -54.722784-0.000111j
[2025-09-12 07:50:00] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -54.806508-0.001518j
[2025-09-12 07:50:11] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -54.970527+0.004047j
[2025-09-12 07:50:24] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -54.815108-0.001142j
[2025-09-12 07:50:36] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -54.963874-0.004066j
[2025-09-12 07:50:48] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -55.000145+0.000896j
[2025-09-12 07:51:04] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -55.000957-0.000922j
[2025-09-12 07:51:19] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -54.951422+0.001780j
[2025-09-12 07:51:34] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -54.889148-0.002405j
[2025-09-12 07:51:50] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -55.005527-0.000899j
[2025-09-12 07:52:05] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -54.965125-0.002703j
[2025-09-12 07:52:21] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -54.939763+0.000354j
[2025-09-12 07:52:36] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -54.971658+0.000272j
[2025-09-12 07:52:51] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -54.815377-0.001589j
[2025-09-12 07:53:07] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -54.983559+0.001855j
[2025-09-12 07:53:22] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -54.931449+0.001001j
[2025-09-12 07:53:37] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -54.825238+0.002803j
[2025-09-12 07:53:53] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -54.812713+0.001694j
[2025-09-12 07:54:08] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -54.876275+0.003838j
[2025-09-12 07:54:24] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -54.957383+0.000556j
[2025-09-12 07:54:39] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -55.016405-0.001333j
[2025-09-12 07:54:54] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -54.893147-0.000224j
[2025-09-12 07:55:10] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -54.932281-0.000732j
[2025-09-12 07:55:25] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -54.772729-0.000562j
[2025-09-12 07:55:41] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -54.861034+0.000091j
[2025-09-12 07:55:56] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -54.854234-0.001282j
[2025-09-12 07:56:11] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -54.934401+0.002233j
[2025-09-12 07:56:27] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -54.894726-0.004331j
[2025-09-12 07:56:42] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -54.956016-0.000188j
[2025-09-12 07:56:58] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -54.859260+0.000508j
[2025-09-12 07:57:13] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -54.873356+0.001893j
[2025-09-12 07:57:29] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -54.935842+0.001992j
[2025-09-12 07:57:44] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -54.893981+0.000035j
[2025-09-12 07:58:00] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -54.860593+0.002274j
[2025-09-12 07:58:15] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -54.927202-0.000337j
[2025-09-12 07:58:31] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -55.052293-0.000919j
[2025-09-12 07:58:46] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -54.827236-0.000335j
[2025-09-12 07:59:01] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -54.878490-0.001215j
[2025-09-12 07:59:17] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -54.809683+0.003211j
[2025-09-12 07:59:32] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -54.804511+0.000022j
[2025-09-12 07:59:48] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -54.980602-0.000023j
[2025-09-12 08:00:03] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -54.847647+0.001599j
[2025-09-12 08:00:19] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -54.809080+0.002466j
[2025-09-12 08:00:34] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -54.932566+0.001979j
[2025-09-12 08:00:49] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -54.813901-0.003430j
[2025-09-12 08:01:05] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -54.823634+0.003837j
[2025-09-12 08:01:20] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -54.799875+0.002228j
[2025-09-12 08:01:36] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -54.953920-0.000763j
[2025-09-12 08:01:51] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -54.910539-0.003142j
[2025-09-12 08:02:06] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -54.900168+0.001108j
[2025-09-12 08:02:22] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -54.729072-0.000053j
[2025-09-12 08:02:37] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -54.825172-0.001460j
[2025-09-12 08:02:52] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -54.868790+0.000803j
[2025-09-12 08:03:08] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -54.825265-0.000542j
[2025-09-12 08:03:23] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -54.967815-0.000567j
[2025-09-12 08:03:39] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -54.920470+0.000793j
[2025-09-12 08:03:54] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -54.783461+0.000874j
[2025-09-12 08:04:10] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -54.854504+0.000571j
[2025-09-12 08:04:25] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -54.817431-0.001097j
[2025-09-12 08:04:41] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -54.911470+0.001382j
[2025-09-12 08:04:56] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -54.807775+0.002066j
[2025-09-12 08:05:11] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -54.734754-0.000286j
[2025-09-12 08:05:27] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -54.899921-0.001334j
[2025-09-12 08:05:42] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -54.948910+0.000808j
[2025-09-12 08:05:58] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -54.803939+0.000220j
[2025-09-12 08:05:58] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-12 08:06:13] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -54.838357-0.001160j
[2025-09-12 08:06:29] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -54.947506-0.003222j
[2025-09-12 08:06:44] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -55.115373+0.000999j
[2025-09-12 08:06:59] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -54.978826-0.000875j
[2025-09-12 08:07:15] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -54.776518+0.001171j
[2025-09-12 08:07:30] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -54.932155-0.001054j
[2025-09-12 08:07:46] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -54.850981+0.001141j
[2025-09-12 08:08:01] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -54.958865+0.002273j
[2025-09-12 08:08:16] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -54.939620-0.001262j
[2025-09-12 08:08:32] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -54.920253+0.002708j
[2025-09-12 08:08:47] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -54.942944+0.000160j
[2025-09-12 08:09:03] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -54.840542+0.001738j
[2025-09-12 08:09:18] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -54.896354+0.001303j
[2025-09-12 08:09:33] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -54.955945-0.001604j
[2025-09-12 08:09:49] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -54.936764+0.002645j
[2025-09-12 08:10:04] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -54.896422+0.000494j
[2025-09-12 08:10:20] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -54.940489+0.003841j
[2025-09-12 08:10:35] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -54.802483-0.001026j
[2025-09-12 08:10:50] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -54.909093-0.000890j
[2025-09-12 08:11:06] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -54.862197+0.001822j
[2025-09-12 08:11:21] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -54.877975+0.002088j
[2025-09-12 08:11:37] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -55.078670-0.000523j
[2025-09-12 08:11:52] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -55.028688+0.000758j
[2025-09-12 08:12:08] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -54.998316+0.002676j
[2025-09-12 08:12:23] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -54.892830-0.001069j
[2025-09-12 08:12:39] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -54.966704+0.001771j
[2025-09-12 08:12:54] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -55.065747-0.000545j
[2025-09-12 08:13:09] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -55.122403+0.001544j
[2025-09-12 08:13:25] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -55.091022+0.003985j
[2025-09-12 08:13:40] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -54.881822+0.002187j
[2025-09-12 08:13:55] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -54.924113+0.001195j
[2025-09-12 08:14:11] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -54.856958-0.001467j
[2025-09-12 08:14:26] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -54.912113+0.000113j
[2025-09-12 08:14:42] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -54.846820-0.004537j
[2025-09-12 08:14:57] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -54.828520+0.000125j
[2025-09-12 08:15:13] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -54.999885+0.003758j
[2025-09-12 08:15:28] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -54.992790+0.002390j
[2025-09-12 08:15:43] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -55.102297+0.001042j
[2025-09-12 08:15:59] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -54.861758+0.002277j
[2025-09-12 08:16:14] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -54.940069+0.000750j
[2025-09-12 08:16:30] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -54.953492+0.000514j
[2025-09-12 08:16:45] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -54.772469+0.000649j
[2025-09-12 08:17:01] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -54.910728+0.002946j
[2025-09-12 08:17:16] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -55.031822+0.000986j
[2025-09-12 08:17:31] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -54.848493+0.002979j
[2025-09-12 08:17:47] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -54.880268-0.000588j
[2025-09-12 08:18:02] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -54.903576-0.000359j
[2025-09-12 08:18:18] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -54.871929+0.004186j
[2025-09-12 08:18:33] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -54.884808+0.002275j
[2025-09-12 08:18:48] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -54.777858+0.000559j
[2025-09-12 08:19:04] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -54.766476+0.001300j
[2025-09-12 08:19:19] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -54.864628+0.000315j
[2025-09-12 08:19:35] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -54.824600-0.000497j
[2025-09-12 08:19:50] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -54.884126-0.000570j
[2025-09-12 08:20:06] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -54.983748-0.001786j
[2025-09-12 08:20:21] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -54.988502-0.000404j
[2025-09-12 08:20:36] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -55.072728+0.001368j
[2025-09-12 08:20:52] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -54.938968+0.001524j
[2025-09-12 08:21:07] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -54.887602-0.000386j
[2025-09-12 08:21:23] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -54.927766-0.000813j
[2025-09-12 08:21:38] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -55.028124-0.004047j
[2025-09-12 08:21:54] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -55.014571-0.001641j
[2025-09-12 08:22:09] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -55.078851-0.000237j
[2025-09-12 08:22:24] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -55.148520+0.000709j
[2025-09-12 08:22:40] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -54.973952+0.000336j
[2025-09-12 08:22:55] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -55.087139-0.000728j
[2025-09-12 08:23:11] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -55.085791-0.002183j
[2025-09-12 08:23:26] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -54.959016+0.000617j
[2025-09-12 08:23:41] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -54.907162+0.001579j
[2025-09-12 08:23:57] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -54.844974-0.000519j
[2025-09-12 08:24:12] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -54.819993+0.001251j
[2025-09-12 08:24:28] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -55.065224-0.000467j
[2025-09-12 08:24:42] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -54.948260+0.000529j
[2025-09-12 08:24:52] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -54.867359+0.000425j
[2025-09-12 08:25:02] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -54.863942+0.000847j
[2025-09-12 08:25:13] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -54.982256+0.000872j
[2025-09-12 08:25:23] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -54.972304-0.001696j
[2025-09-12 08:25:33] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -55.040967-0.000004j
[2025-09-12 08:25:43] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -54.989699-0.000736j
[2025-09-12 08:25:57] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -55.062726-0.002800j
[2025-09-12 08:26:13] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -55.012347-0.003486j
[2025-09-12 08:26:28] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -54.868352-0.000406j
[2025-09-12 08:26:44] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -54.972395+0.000306j
[2025-09-12 08:26:59] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -54.930688+0.000824j
[2025-09-12 08:27:15] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -54.931812+0.003069j
[2025-09-12 08:27:30] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -54.894287-0.002369j
[2025-09-12 08:27:45] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -54.858130+0.000865j
[2025-09-12 08:28:01] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -54.854273+0.000820j
[2025-09-12 08:28:16] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -54.895341-0.001416j
[2025-09-12 08:28:32] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -54.896639+0.001112j
[2025-09-12 08:28:47] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -54.938319+0.003415j
[2025-09-12 08:29:03] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -54.796702-0.002474j
[2025-09-12 08:29:18] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -54.833194-0.000909j
[2025-09-12 08:29:34] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -54.864629+0.000267j
[2025-09-12 08:29:49] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -54.841066+0.000053j
[2025-09-12 08:30:04] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -54.808612-0.002081j
[2025-09-12 08:30:20] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -54.823702-0.000050j
[2025-09-12 08:30:35] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -54.881660-0.000034j
[2025-09-12 08:30:51] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -54.973346+0.000651j
[2025-09-12 08:31:06] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -54.984965+0.000975j
[2025-09-12 08:31:22] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -55.124229+0.001532j
[2025-09-12 08:31:37] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -55.078988-0.001746j
[2025-09-12 08:31:52] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -55.017025-0.001420j
[2025-09-12 08:32:08] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -54.947734-0.000231j
[2025-09-12 08:32:23] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -54.963965+0.003240j
[2025-09-12 08:32:23] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-12 08:32:39] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -54.967162-0.000269j
[2025-09-12 08:32:54] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -55.003806-0.000962j
[2025-09-12 08:33:10] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -54.885674+0.001982j
[2025-09-12 08:33:25] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -54.851156+0.000211j
[2025-09-12 08:33:41] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -54.825752-0.001995j
[2025-09-12 08:33:56] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -54.792222+0.000370j
[2025-09-12 08:34:12] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -54.903502+0.001146j
[2025-09-12 08:34:27] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -54.969632+0.000316j
[2025-09-12 08:34:43] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -54.951045-0.002383j
[2025-09-12 08:34:58] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -54.961189-0.002465j
[2025-09-12 08:35:13] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -54.937343-0.000612j
[2025-09-12 08:35:25] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -54.869500+0.001295j
[2025-09-12 08:35:37] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -54.837736-0.003773j
[2025-09-12 08:35:50] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -54.859557+0.001930j
[2025-09-12 08:36:02] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -54.970116+0.001967j
[2025-09-12 08:36:17] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -54.998668-0.001941j
[2025-09-12 08:36:33] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -55.133066-0.000509j
[2025-09-12 08:36:48] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -55.032962+0.000759j
[2025-09-12 08:37:03] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -55.036587-0.001583j
[2025-09-12 08:37:19] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -55.051669-0.000293j
[2025-09-12 08:37:34] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -54.969707-0.000467j
[2025-09-12 08:37:49] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -54.991818+0.000993j
[2025-09-12 08:38:05] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -55.047171-0.000897j
[2025-09-12 08:38:20] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -54.918666+0.001773j
[2025-09-12 08:38:36] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -54.880141+0.001679j
[2025-09-12 08:38:51] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -54.984362-0.000345j
[2025-09-12 08:39:06] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -54.876884+0.001425j
[2025-09-12 08:39:22] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -54.905890-0.000594j
[2025-09-12 08:39:37] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -54.957976-0.000590j
[2025-09-12 08:39:53] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -54.958734+0.000384j
[2025-09-12 08:40:08] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -54.944165+0.001708j
[2025-09-12 08:40:24] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -55.007546-0.000514j
[2025-09-12 08:40:39] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -55.041754-0.001650j
[2025-09-12 08:40:54] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -55.013155+0.000129j
[2025-09-12 08:41:10] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -55.004046-0.001858j
[2025-09-12 08:41:25] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -55.022416+0.000385j
[2025-09-12 08:41:41] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -54.888426-0.000886j
[2025-09-12 08:41:56] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -54.903994-0.000697j
[2025-09-12 08:42:11] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -54.861788-0.002236j
[2025-09-12 08:42:27] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -54.763915-0.000336j
[2025-09-12 08:42:42] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -54.836540+0.000452j
[2025-09-12 08:42:58] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -54.785336-0.000057j
[2025-09-12 08:43:13] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -54.955459-0.000664j
[2025-09-12 08:43:28] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -54.925741-0.000550j
[2025-09-12 08:43:44] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -54.901734-0.000236j
[2025-09-12 08:43:59] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -54.863989-0.003317j
[2025-09-12 08:44:15] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -54.986597+0.000512j
[2025-09-12 08:44:30] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -54.949869+0.001581j
[2025-09-12 08:44:46] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -54.993488-0.000389j
[2025-09-12 08:45:01] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -55.046152+0.000545j
[2025-09-12 08:45:16] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -55.031276-0.002791j
[2025-09-12 08:45:32] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -55.008482+0.001763j
[2025-09-12 08:45:47] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -55.085493-0.000891j
[2025-09-12 08:46:03] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -55.138606+0.001477j
[2025-09-12 08:46:18] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -55.025355-0.002913j
[2025-09-12 08:46:33] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -55.005518+0.000164j
[2025-09-12 08:46:49] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -54.955750+0.002429j
[2025-09-12 08:47:04] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -54.907634-0.000026j
[2025-09-12 08:47:19] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -54.847045+0.002220j
[2025-09-12 08:47:35] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -54.938505-0.001527j
[2025-09-12 08:47:50] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -54.819834+0.002043j
[2025-09-12 08:48:06] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -54.924195+0.003102j
[2025-09-12 08:48:21] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -54.937858-0.000706j
[2025-09-12 08:48:36] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -54.960518+0.000760j
[2025-09-12 08:48:52] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -54.916457-0.002009j
[2025-09-12 08:49:07] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -54.810650+0.001226j
[2025-09-12 08:49:23] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -54.895651+0.000412j
[2025-09-12 08:49:38] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -54.963376-0.001397j
[2025-09-12 08:49:53] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -54.863181-0.002278j
[2025-09-12 08:50:09] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -54.897077-0.000322j
[2025-09-12 08:50:24] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -54.911930+0.000816j
[2025-09-12 08:50:40] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -54.911461+0.001317j
[2025-09-12 08:50:55] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -54.806827-0.001443j
[2025-09-12 08:51:11] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -54.691833-0.001217j
[2025-09-12 08:51:26] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -54.735309-0.001446j
[2025-09-12 08:51:41] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -54.817355-0.000964j
[2025-09-12 08:51:57] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -54.887613-0.003385j
[2025-09-12 08:52:12] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -54.914775+0.000956j
[2025-09-12 08:52:28] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -54.954099+0.000474j
[2025-09-12 08:52:43] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -55.011189+0.015013j
[2025-09-12 08:52:58] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -54.943673-0.001708j
[2025-09-12 08:53:14] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -54.961948-0.003001j
[2025-09-12 08:53:29] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -55.024002-0.000069j
[2025-09-12 08:53:45] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -54.992883-0.000756j
[2025-09-12 08:54:00] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -54.966234-0.001529j
[2025-09-12 08:54:16] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -54.976654+0.001161j
[2025-09-12 08:54:31] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -55.001412+0.002322j
[2025-09-12 08:54:46] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -54.991341-0.000753j
[2025-09-12 08:55:02] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -54.940636-0.000651j
[2025-09-12 08:55:17] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -54.917787+0.001354j
[2025-09-12 08:55:33] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -54.923607+0.002182j
[2025-09-12 08:55:48] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -54.936410-0.003790j
[2025-09-12 08:56:03] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -54.933760-0.000555j
[2025-09-12 08:56:19] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -54.965653-0.000564j
[2025-09-12 08:56:34] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -54.903771-0.004615j
[2025-09-12 08:56:50] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -54.876637+0.002286j
[2025-09-12 08:57:05] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -55.017916-0.002363j
[2025-09-12 08:57:20] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -54.851123-0.001367j
[2025-09-12 08:57:36] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -54.979602-0.005226j
[2025-09-12 08:57:51] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -54.947659+0.000659j
[2025-09-12 08:58:06] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -54.996377-0.000686j
[2025-09-12 08:58:22] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -54.997255-0.001974j
[2025-09-12 08:58:37] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -54.843824+0.001226j
[2025-09-12 08:58:53] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -54.916352+0.000345j
[2025-09-12 08:59:08] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -54.896741+0.001949j
[2025-09-12 08:59:08] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-12 08:59:23] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -54.867538+0.003364j
[2025-09-12 08:59:39] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -54.866463-0.000380j
[2025-09-12 08:59:54] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -54.824790+0.001110j
[2025-09-12 09:00:10] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -54.785960+0.000365j
[2025-09-12 09:00:25] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -54.720550+0.001445j
[2025-09-12 09:00:40] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -54.972734+0.000111j
[2025-09-12 09:00:56] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -54.865992-0.000997j
[2025-09-12 09:01:11] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -54.858867-0.002564j
[2025-09-12 09:01:27] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -54.778715+0.001213j
[2025-09-12 09:01:42] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -54.830404+0.000877j
[2025-09-12 09:01:57] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -54.834679+0.001360j
[2025-09-12 09:02:13] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -54.880259+0.000029j
[2025-09-12 09:02:28] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -54.840210-0.000596j
[2025-09-12 09:02:44] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -54.907751+0.002304j
[2025-09-12 09:02:59] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -54.879479+0.002759j
[2025-09-12 09:03:14] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -54.895286+0.001451j
[2025-09-12 09:03:30] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -54.764983+0.001566j
[2025-09-12 09:03:45] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -54.765638+0.000333j
[2025-09-12 09:04:01] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -54.908750-0.000618j
[2025-09-12 09:04:16] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -54.881065+0.002642j
[2025-09-12 09:04:31] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -54.922606+0.002824j
[2025-09-12 09:04:47] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -54.945615+0.003528j
[2025-09-12 09:05:02] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -54.848940+0.000184j
[2025-09-12 09:05:18] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -54.871333-0.000349j
[2025-09-12 09:05:33] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -54.920168-0.000498j
[2025-09-12 09:05:49] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -54.893965+0.000377j
[2025-09-12 09:06:04] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -55.004854+0.000719j
[2025-09-12 09:06:19] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -55.051462-0.001249j
[2025-09-12 09:06:35] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -54.823030+0.001196j
[2025-09-12 09:06:50] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -54.861692-0.000678j
[2025-09-12 09:07:06] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -54.853432-0.001804j
[2025-09-12 09:07:21] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -54.799183-0.000047j
[2025-09-12 09:07:36] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -54.836169+0.000719j
[2025-09-12 09:07:52] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -54.802111+0.001508j
[2025-09-12 09:08:07] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -54.835676+0.000014j
[2025-09-12 09:08:22] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -54.874623+0.003240j
[2025-09-12 09:08:38] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -54.967467+0.000714j
[2025-09-12 09:08:53] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -55.015381-0.000793j
[2025-09-12 09:09:09] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -54.953701+0.001243j
[2025-09-12 09:09:24] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -54.837936-0.001069j
[2025-09-12 09:09:39] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -54.933428-0.000759j
[2025-09-12 09:09:52] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -54.945638-0.001233j
[2025-09-12 09:10:02] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -54.757511-0.000456j
[2025-09-12 09:10:12] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -54.839752-0.001162j
[2025-09-12 09:10:23] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -54.914460+0.000361j
[2025-09-12 09:10:33] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -54.953275+0.002822j
[2025-09-12 09:10:43] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -54.976155-0.000673j
[2025-09-12 09:10:54] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -54.964740-0.001509j
[2025-09-12 09:11:07] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -54.957084+0.002537j
[2025-09-12 09:11:23] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -54.950937-0.000314j
[2025-09-12 09:11:38] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -54.908907-0.002529j
[2025-09-12 09:11:53] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -54.883930-0.001022j
[2025-09-12 09:12:09] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -54.879252+0.002786j
[2025-09-12 09:12:24] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -54.717674+0.000817j
[2025-09-12 09:12:40] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -54.899695-0.000880j
[2025-09-12 09:12:55] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -54.834709-0.000527j
[2025-09-12 09:13:11] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -54.752208+0.000313j
[2025-09-12 09:13:26] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -54.757823-0.001774j
[2025-09-12 09:13:42] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -54.768153-0.000399j
[2025-09-12 09:13:57] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -54.721134-0.000477j
[2025-09-12 09:14:12] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -54.764123+0.001109j
[2025-09-12 09:14:28] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -54.687074+0.000952j
[2025-09-12 09:14:43] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -54.755945-0.001780j
[2025-09-12 09:14:59] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -54.834935+0.000781j
[2025-09-12 09:15:14] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -54.827436-0.000854j
[2025-09-12 09:15:30] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -54.899689-0.000912j
[2025-09-12 09:15:45] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -54.877294+0.000037j
[2025-09-12 09:16:01] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -54.925710+0.001508j
[2025-09-12 09:16:16] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -54.907370+0.001896j
[2025-09-12 09:16:31] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -54.729659+0.000635j
[2025-09-12 09:16:47] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -54.662017+0.000799j
[2025-09-12 09:17:02] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -54.714109-0.002335j
[2025-09-12 09:17:17] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -54.823367-0.001162j
[2025-09-12 09:17:33] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -54.821229+0.000803j
[2025-09-12 09:17:48] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -54.887510+0.000844j
[2025-09-12 09:18:04] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -54.905243+0.002118j
[2025-09-12 09:18:19] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -54.871450+0.000167j
[2025-09-12 09:18:35] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -54.929951-0.000395j
[2025-09-12 09:18:50] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -54.917022-0.001598j
[2025-09-12 09:19:05] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -54.907026+0.004023j
[2025-09-12 09:19:21] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -54.812030+0.002189j
[2025-09-12 09:19:36] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -54.857882+0.001445j
[2025-09-12 09:19:52] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -54.778307+0.004930j
[2025-09-12 09:20:07] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -54.691616-0.000982j
[2025-09-12 09:20:23] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -54.730209-0.002092j
[2025-09-12 09:20:34] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -54.881790+0.002413j
[2025-09-12 09:20:47] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -54.802289-0.002938j
[2025-09-12 09:20:59] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -54.875665+0.001391j
[2025-09-12 09:21:11] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -54.755131+0.000554j
[2025-09-12 09:21:27] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -54.854645-0.001540j
[2025-09-12 09:21:42] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -54.922858+0.000420j
[2025-09-12 09:21:58] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -54.850573-0.000164j
[2025-09-12 09:22:13] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -54.808629+0.003656j
[2025-09-12 09:22:28] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -54.893520+0.002125j
[2025-09-12 09:22:40] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -54.696251+0.001443j
[2025-09-12 09:22:50] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -54.761773-0.001044j
[2025-09-12 09:23:00] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -54.841145+0.002458j
[2025-09-12 09:23:11] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -54.816903+0.002839j
[2025-09-12 09:23:21] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -54.816292-0.001783j
[2025-09-12 09:23:31] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -54.791835-0.000370j
[2025-09-12 09:23:41] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -54.890796-0.003680j
[2025-09-12 09:23:51] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -54.827742-0.003807j
[2025-09-12 09:24:02] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -54.692062-0.000974j
[2025-09-12 09:24:12] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -54.863793+0.000190j
[2025-09-12 09:24:22] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -54.835234-0.001149j
[2025-09-12 09:24:22] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-12 09:24:22] ✅ Training completed | Restarts: 2
[2025-09-12 09:24:22] ============================================================
[2025-09-12 09:24:22] Training completed | Runtime: 15988.4s
[2025-09-12 09:24:26] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-12 09:24:26] ============================================================
