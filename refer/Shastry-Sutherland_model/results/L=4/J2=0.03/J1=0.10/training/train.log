[2025-09-12 09:24:38] ✓ 从checkpoint恢复: results/L=4/J2=0.03/J1=0.09/training/checkpoints/final_GCNN.pkl
[2025-09-12 09:24:38]   - 迭代次数: final
[2025-09-12 09:24:38]   - 能量: -54.835234-0.001149j ± 0.080950
[2025-09-12 09:24:38]   - 时间戳: 2025-09-12T09:24:26.674731+08:00
[2025-09-12 09:25:04] ✓ 变分状态参数已从checkpoint恢复
[2025-09-12 09:25:04] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-12 09:25:04] ==================================================
[2025-09-12 09:25:04] GCNN for Shastry-Sutherland Model
[2025-09-12 09:25:04] ==================================================
[2025-09-12 09:25:04] System parameters:
[2025-09-12 09:25:04]   - System size: L=4, N=64
[2025-09-12 09:25:04]   - System parameters: J1=0.1, J2=0.03, Q=0.97
[2025-09-12 09:25:04] --------------------------------------------------
[2025-09-12 09:25:04] Model parameters:
[2025-09-12 09:25:04]   - Number of layers = 4
[2025-09-12 09:25:04]   - Number of features = 4
[2025-09-12 09:25:04]   - Total parameters = 12572
[2025-09-12 09:25:04] --------------------------------------------------
[2025-09-12 09:25:04] Training parameters:
[2025-09-12 09:25:04]   - Learning rate: 0.015
[2025-09-12 09:25:04]   - Total iterations: 1050
[2025-09-12 09:25:04]   - Annealing cycles: 3
[2025-09-12 09:25:04]   - Initial period: 150
[2025-09-12 09:25:04]   - Period multiplier: 2.0
[2025-09-12 09:25:04]   - Temperature range: 0.0-1.0
[2025-09-12 09:25:04]   - Samples: 4096
[2025-09-12 09:25:04]   - Discarded samples: 0
[2025-09-12 09:25:04]   - Chunk size: 2048
[2025-09-12 09:25:04]   - Diagonal shift: 0.2
[2025-09-12 09:25:04]   - Gradient clipping: 1.0
[2025-09-12 09:25:04]   - Checkpoint enabled: interval=105
[2025-09-12 09:25:04]   - Checkpoint directory: results/L=4/J2=0.03/J1=0.10/training/checkpoints
[2025-09-12 09:25:04] --------------------------------------------------
[2025-09-12 09:25:04] Device status:
[2025-09-12 09:25:04]   - Devices model: NVIDIA H200 NVL
[2025-09-12 09:25:04]   - Number of devices: 1
[2025-09-12 09:25:04]   - Sharding: True
[2025-09-12 09:25:04] ============================================================
[2025-09-12 09:26:55] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -55.169828+0.006586j
[2025-09-12 09:28:01] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -55.032235+0.002694j
[2025-09-12 09:28:16] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -55.292133-0.002401j
[2025-09-12 09:28:31] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -55.167012+0.001941j
[2025-09-12 09:28:46] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -55.161311+0.002054j
[2025-09-12 09:29:02] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -55.281965+0.002275j
[2025-09-12 09:29:17] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -55.102277+0.000722j
[2025-09-12 09:29:32] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -55.074265+0.000023j
[2025-09-12 09:29:48] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -55.311479-0.001391j
[2025-09-12 09:30:03] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -55.283331-0.001818j
[2025-09-12 09:30:18] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -55.294921+0.002173j
[2025-09-12 09:30:34] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -55.249097+0.001654j
[2025-09-12 09:30:49] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -55.391479-0.002685j
[2025-09-12 09:31:04] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -55.357778+0.000502j
[2025-09-12 09:31:20] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -55.293663+0.003560j
[2025-09-12 09:31:35] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -55.348285-0.001225j
[2025-09-12 09:31:50] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -55.318876+0.004256j
[2025-09-12 09:32:06] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -55.352664+0.001625j
[2025-09-12 09:32:21] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -55.198518-0.003317j
[2025-09-12 09:32:36] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -55.206135+0.001604j
[2025-09-12 09:32:52] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -55.180507-0.000814j
[2025-09-12 09:33:07] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -55.214565+0.001253j
[2025-09-12 09:33:22] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -55.186360+0.000514j
[2025-09-12 09:33:38] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -55.147685+0.000978j
[2025-09-12 09:33:53] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -55.226373+0.002621j
[2025-09-12 09:34:08] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -55.365617-0.001069j
[2025-09-12 09:34:24] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -55.322467-0.001293j
[2025-09-12 09:34:39] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -55.353902+0.000004j
[2025-09-12 09:34:54] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -55.324297-0.002214j
[2025-09-12 09:35:10] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -55.338445-0.001127j
[2025-09-12 09:35:25] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -55.361811-0.001923j
[2025-09-12 09:35:40] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -55.303650+0.004085j
[2025-09-12 09:35:56] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -55.340123-0.001652j
[2025-09-12 09:36:11] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -55.218624+0.001928j
[2025-09-12 09:36:26] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -55.281334+0.000032j
[2025-09-12 09:36:42] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -55.258494-0.000851j
[2025-09-12 09:36:57] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -55.247771-0.001086j
[2025-09-12 09:37:12] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -55.389770+0.001433j
[2025-09-12 09:37:28] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -55.292734+0.002229j
[2025-09-12 09:37:43] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -55.329732+0.002255j
[2025-09-12 09:37:59] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -55.266352-0.001849j
[2025-09-12 09:38:14] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -55.298602-0.000942j
[2025-09-12 09:38:29] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -55.328647-0.001076j
[2025-09-12 09:38:45] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -55.250619+0.000093j
[2025-09-12 09:39:00] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -55.324983-0.002153j
[2025-09-12 09:39:15] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -55.470404+0.002009j
[2025-09-12 09:39:31] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -55.434287+0.000478j
[2025-09-12 09:39:46] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -55.273508+0.001527j
[2025-09-12 09:40:01] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -55.254359-0.001032j
[2025-09-12 09:40:17] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -55.222912-0.000415j
[2025-09-12 09:40:32] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -55.181693-0.000819j
[2025-09-12 09:40:47] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -55.127280-0.001890j
[2025-09-12 09:41:03] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -55.115541-0.001601j
[2025-09-12 09:41:18] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -55.137507-0.000406j
[2025-09-12 09:41:33] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -55.265371-0.001470j
[2025-09-12 09:41:49] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -55.116821-0.000069j
[2025-09-12 09:42:04] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -55.181145-0.002057j
[2025-09-12 09:42:19] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -55.247520-0.001925j
[2025-09-12 09:42:35] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -55.196908+0.005938j
[2025-09-12 09:42:50] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -55.085203-0.001307j
[2025-09-12 09:43:05] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -55.032976+0.002668j
[2025-09-12 09:43:21] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -55.122387+0.001186j
[2025-09-12 09:43:36] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -55.185991-0.001485j
[2025-09-12 09:43:51] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -55.277894+0.000735j
[2025-09-12 09:44:07] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -55.196671-0.000274j
[2025-09-12 09:44:22] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -55.349731-0.000177j
[2025-09-12 09:44:37] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -55.325696+0.002831j
[2025-09-12 09:44:53] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -55.344947-0.001472j
[2025-09-12 09:45:08] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -55.325602+0.002693j
[2025-09-12 09:45:23] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -55.416856-0.001213j
[2025-09-12 09:45:39] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -55.370432-0.001002j
[2025-09-12 09:45:54] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -55.276267-0.001194j
[2025-09-12 09:46:09] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -55.314963+0.001191j
[2025-09-12 09:46:25] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -55.237794-0.000197j
[2025-09-12 09:46:40] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -55.151968-0.001105j
[2025-09-12 09:46:55] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -55.132452-0.000683j
[2025-09-12 09:47:11] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -55.293339-0.002515j
[2025-09-12 09:47:21] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -55.192441+0.000236j
[2025-09-12 09:47:31] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -55.214037-0.002173j
[2025-09-12 09:47:41] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -55.276255+0.000219j
[2025-09-12 09:47:52] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -55.244555+0.001026j
[2025-09-12 09:48:02] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -55.282153-0.001048j
[2025-09-12 09:48:12] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -55.225182+0.002433j
[2025-09-12 09:48:27] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -55.333996+0.002406j
[2025-09-12 09:48:42] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -55.305851-0.002064j
[2025-09-12 09:48:57] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -55.324397-0.001569j
[2025-09-12 09:49:13] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -55.403071-0.002479j
[2025-09-12 09:49:28] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -55.417886-0.001096j
[2025-09-12 09:49:43] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -55.330208-0.000616j
[2025-09-12 09:49:59] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -55.504745+0.001281j
[2025-09-12 09:50:14] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -55.311945+0.000526j
[2025-09-12 09:50:29] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -55.329764-0.000713j
[2025-09-12 09:50:45] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -55.328441-0.000705j
[2025-09-12 09:51:00] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -55.411785+0.000819j
[2025-09-12 09:51:16] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -55.332793-0.001997j
[2025-09-12 09:51:31] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -55.342236-0.000244j
[2025-09-12 09:51:46] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -55.347172-0.000739j
[2025-09-12 09:52:02] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -55.398312+0.002180j
[2025-09-12 09:52:17] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -55.354893-0.000144j
[2025-09-12 09:52:33] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -55.303604-0.000962j
[2025-09-12 09:52:48] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -55.300899-0.001843j
[2025-09-12 09:53:03] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -55.253969+0.001198j
[2025-09-12 09:53:19] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -55.397795-0.002155j
[2025-09-12 09:53:34] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -55.205557-0.000860j
[2025-09-12 09:53:50] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -55.260055-0.001066j
[2025-09-12 09:53:50] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-12 09:54:05] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -55.211013-0.001309j
[2025-09-12 09:54:20] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -55.153353-0.000212j
[2025-09-12 09:54:36] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -55.224143-0.003098j
[2025-09-12 09:54:51] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -55.300345-0.001385j
[2025-09-12 09:55:06] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -55.221679-0.000921j
[2025-09-12 09:55:22] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -55.202288-0.000930j
[2025-09-12 09:55:37] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -55.114500-0.000627j
[2025-09-12 09:55:53] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -55.153118-0.001951j
[2025-09-12 09:56:08] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -55.165989+0.000731j
[2025-09-12 09:56:23] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -55.171926+0.001055j
[2025-09-12 09:56:39] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -55.185444+0.004058j
[2025-09-12 09:56:54] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -55.137983+0.002740j
[2025-09-12 09:57:09] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -55.175765-0.002526j
[2025-09-12 09:57:25] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -55.135445+0.001331j
[2025-09-12 09:57:40] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -55.248572+0.002205j
[2025-09-12 09:57:51] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -55.251216-0.001679j
[2025-09-12 09:58:04] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -55.141065+0.000415j
[2025-09-12 09:58:15] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -55.261484-0.001014j
[2025-09-12 09:58:28] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -55.389810+0.000831j
[2025-09-12 09:58:43] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -55.297756-0.000229j
[2025-09-12 09:58:59] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -55.305954+0.001337j
[2025-09-12 09:59:14] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -55.316007+0.000357j
[2025-09-12 09:59:29] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -55.382745-0.000427j
[2025-09-12 09:59:44] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -55.257832+0.001746j
[2025-09-12 10:00:00] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -55.320343+0.000540j
[2025-09-12 10:00:15] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -55.179850-0.000256j
[2025-09-12 10:00:30] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -55.285715+0.000359j
[2025-09-12 10:00:46] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -55.312117+0.003016j
[2025-09-12 10:01:01] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -55.296920+0.000172j
[2025-09-12 10:01:16] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -55.276090-0.001224j
[2025-09-12 10:01:32] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -55.224901+0.002642j
[2025-09-12 10:01:47] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -55.205021+0.002420j
[2025-09-12 10:02:02] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -55.223792+0.000858j
[2025-09-12 10:02:18] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -55.211390+0.002628j
[2025-09-12 10:02:33] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -55.174172+0.001431j
[2025-09-12 10:02:48] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -55.204447-0.000638j
[2025-09-12 10:03:04] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -55.221361+0.002912j
[2025-09-12 10:03:19] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -55.304195+0.001178j
[2025-09-12 10:03:34] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -55.258995-0.002538j
[2025-09-12 10:03:49] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -55.281258-0.000132j
[2025-09-12 10:04:05] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -55.326439-0.000535j
[2025-09-12 10:04:20] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -55.427731-0.000007j
[2025-09-12 10:04:35] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -55.309386-0.003140j
[2025-09-12 10:04:51] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -55.292990+0.001858j
[2025-09-12 10:05:06] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -55.245983+0.000228j
[2025-09-12 10:05:06] RESTART #1 | Period: 300
[2025-09-12 10:05:21] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -55.350367+0.003025j
[2025-09-12 10:05:37] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -55.331232-0.003669j
[2025-09-12 10:05:52] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -55.243056+0.002592j
[2025-09-12 10:06:07] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -55.192308+0.002133j
[2025-09-12 10:06:23] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -55.197076-0.001554j
[2025-09-12 10:06:38] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -55.183461+0.001130j
[2025-09-12 10:06:53] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -55.193604-0.000376j
[2025-09-12 10:07:08] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -55.183871+0.001297j
[2025-09-12 10:07:24] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -55.266182-0.001266j
[2025-09-12 10:07:39] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -55.236844-0.001134j
[2025-09-12 10:07:54] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -55.360328+0.002163j
[2025-09-12 10:08:10] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -55.178386-0.000139j
[2025-09-12 10:08:25] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -55.308512-0.000078j
[2025-09-12 10:08:40] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -55.298443-0.000627j
[2025-09-12 10:08:56] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -55.259523+0.001300j
[2025-09-12 10:09:11] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -55.288581-0.002040j
[2025-09-12 10:09:26] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -55.293862-0.001569j
[2025-09-12 10:09:42] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -55.301400-0.000318j
[2025-09-12 10:09:57] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -55.357879-0.000139j
[2025-09-12 10:10:12] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -55.270670+0.001201j
[2025-09-12 10:10:27] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -55.319469-0.001981j
[2025-09-12 10:10:43] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -55.335120-0.000080j
[2025-09-12 10:10:58] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -55.425522-0.000826j
[2025-09-12 10:11:13] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -55.286792+0.000887j
[2025-09-12 10:11:29] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -55.262944+0.000212j
[2025-09-12 10:11:44] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -55.356350-0.003856j
[2025-09-12 10:11:59] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -55.361237+0.001660j
[2025-09-12 10:12:15] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -55.308358-0.001380j
[2025-09-12 10:12:30] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -55.228423+0.003544j
[2025-09-12 10:12:45] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -55.170013-0.000468j
[2025-09-12 10:13:01] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -55.138947+0.001227j
[2025-09-12 10:13:16] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -55.172308-0.001185j
[2025-09-12 10:13:31] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -55.282972-0.002680j
[2025-09-12 10:13:47] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -55.369766-0.000369j
[2025-09-12 10:14:02] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -55.450336-0.000312j
[2025-09-12 10:14:17] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -55.285712-0.000959j
[2025-09-12 10:14:33] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -55.226035-0.002157j
[2025-09-12 10:14:48] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -55.420275+0.000887j
[2025-09-12 10:15:03] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -55.422840-0.000863j
[2025-09-12 10:15:19] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -55.319509+0.000460j
[2025-09-12 10:15:34] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -55.272711+0.002976j
[2025-09-12 10:15:49] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -55.320341-0.000725j
[2025-09-12 10:16:05] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -55.290936+0.001317j
[2025-09-12 10:16:20] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -55.301695+0.000519j
[2025-09-12 10:16:35] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -55.313758+0.003796j
[2025-09-12 10:16:51] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -55.183109-0.000272j
[2025-09-12 10:17:06] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -55.310900-0.000551j
[2025-09-12 10:17:21] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -55.389203-0.001454j
[2025-09-12 10:17:36] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -55.372408+0.000045j
[2025-09-12 10:17:52] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -55.298503-0.000361j
[2025-09-12 10:18:07] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -55.403896+0.001323j
[2025-09-12 10:18:22] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -55.333525+0.001654j
[2025-09-12 10:18:38] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -55.312512-0.002092j
[2025-09-12 10:18:53] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -55.240617-0.002505j
[2025-09-12 10:19:08] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -55.245150-0.000085j
[2025-09-12 10:19:24] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -55.312893+0.000448j
[2025-09-12 10:19:39] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -55.279904+0.001218j
[2025-09-12 10:19:54] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -55.240314+0.000100j
[2025-09-12 10:20:10] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -55.116286-0.002942j
[2025-09-12 10:20:25] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -55.272769+0.003498j
[2025-09-12 10:20:25] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-12 10:20:40] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -55.278241+0.002310j
[2025-09-12 10:20:55] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -55.262339-0.000030j
[2025-09-12 10:21:11] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -55.175465-0.000161j
[2025-09-12 10:21:26] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -55.279500-0.001514j
[2025-09-12 10:21:41] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -55.180064+0.000000j
[2025-09-12 10:21:57] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -55.259627-0.001695j
[2025-09-12 10:22:12] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -55.232346+0.000262j
[2025-09-12 10:22:27] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -55.313695+0.000724j
[2025-09-12 10:22:43] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -55.348132+0.000175j
[2025-09-12 10:22:58] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -55.220613-0.001106j
[2025-09-12 10:23:13] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -55.182374+0.000858j
[2025-09-12 10:23:29] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -55.304643-0.003028j
[2025-09-12 10:23:44] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -55.139572-0.000072j
[2025-09-12 10:23:59] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -55.245772-0.002556j
[2025-09-12 10:24:14] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -55.205327+0.000092j
[2025-09-12 10:24:30] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -55.135335-0.001404j
[2025-09-12 10:24:45] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -55.137061+0.000007j
[2025-09-12 10:25:00] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -55.188268-0.002561j
[2025-09-12 10:25:16] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -55.029338+0.001605j
[2025-09-12 10:25:31] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -55.145998-0.003219j
[2025-09-12 10:25:46] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -55.120775-0.004180j
[2025-09-12 10:26:02] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -55.141952-0.000426j
[2025-09-12 10:26:17] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -55.198381+0.001214j
[2025-09-12 10:26:32] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -55.265133-0.003669j
[2025-09-12 10:26:48] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -55.156138-0.000995j
[2025-09-12 10:27:03] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -55.237935-0.000164j
[2025-09-12 10:27:18] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -55.207879-0.001751j
[2025-09-12 10:27:34] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -55.248831+0.001317j
[2025-09-12 10:27:49] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -55.202837+0.001820j
[2025-09-12 10:28:04] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -55.143149+0.019625j
[2025-09-12 10:28:20] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -55.262793+0.001217j
[2025-09-12 10:28:35] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -55.233220+0.000581j
[2025-09-12 10:28:50] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -55.233189-0.000972j
[2025-09-12 10:29:05] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -55.227696+0.001910j
[2025-09-12 10:29:21] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -55.172483+0.001538j
[2025-09-12 10:29:36] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -55.371597-0.000501j
[2025-09-12 10:29:52] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -55.180624+0.001149j
[2025-09-12 10:30:07] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -55.344216+0.003195j
[2025-09-12 10:30:22] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -55.245962-0.001889j
[2025-09-12 10:30:37] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -55.279050+0.000353j
[2025-09-12 10:30:53] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -55.374508+0.000817j
[2025-09-12 10:31:08] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -55.328571+0.000133j
[2025-09-12 10:31:23] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -55.201087+0.002427j
[2025-09-12 10:31:36] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -55.323181-0.002907j
[2025-09-12 10:31:46] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -55.316807+0.000216j
[2025-09-12 10:31:56] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -55.298874+0.003120j
[2025-09-12 10:32:07] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -55.252944-0.002164j
[2025-09-12 10:32:17] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -55.377489-0.001782j
[2025-09-12 10:32:27] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -55.374222+0.000090j
[2025-09-12 10:32:37] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -55.299666-0.000180j
[2025-09-12 10:32:53] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -55.248441-0.000222j
[2025-09-12 10:33:08] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -55.281584+0.000965j
[2025-09-12 10:33:23] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -55.416133-0.003470j
[2025-09-12 10:33:39] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -55.260104-0.002084j
[2025-09-12 10:33:54] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -55.157343-0.000870j
[2025-09-12 10:34:10] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -55.232746-0.003331j
[2025-09-12 10:34:25] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -55.209720+0.000446j
[2025-09-12 10:34:41] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -55.188394+0.001200j
[2025-09-12 10:34:56] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -55.252627+0.000279j
[2025-09-12 10:35:11] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -55.358563+0.002665j
[2025-09-12 10:35:27] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -55.362835+0.000233j
[2025-09-12 10:35:42] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -55.237084+0.000982j
[2025-09-12 10:35:58] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -55.166038-0.001065j
[2025-09-12 10:36:13] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -55.314213-0.000252j
[2025-09-12 10:36:28] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -55.258459-0.000840j
[2025-09-12 10:36:44] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -55.230410+0.000207j
[2025-09-12 10:36:59] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -55.363721+0.002045j
[2025-09-12 10:37:14] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -55.330552+0.000052j
[2025-09-12 10:37:29] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -55.322002-0.000812j
[2025-09-12 10:37:45] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -55.316344+0.002255j
[2025-09-12 10:38:00] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -55.443743+0.001180j
[2025-09-12 10:38:16] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -55.414720+0.000653j
[2025-09-12 10:38:31] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -55.268766-0.000786j
[2025-09-12 10:38:46] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -55.262151+0.001525j
[2025-09-12 10:39:02] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -55.263388-0.000874j
[2025-09-12 10:39:17] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -55.213634-0.001696j
[2025-09-12 10:39:32] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -55.322086+0.000674j
[2025-09-12 10:39:48] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -55.257037+0.001237j
[2025-09-12 10:40:03] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -55.323965-0.001733j
[2025-09-12 10:40:18] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -55.494960-0.002276j
[2025-09-12 10:40:34] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -55.341764-0.000817j
[2025-09-12 10:40:49] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -55.377965+0.000975j
[2025-09-12 10:41:04] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -55.389729-0.001747j
[2025-09-12 10:41:20] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -55.354941-0.000382j
[2025-09-12 10:41:35] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -55.190961+0.000291j
[2025-09-12 10:41:50] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -55.246835-0.000229j
[2025-09-12 10:42:05] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -55.068149-0.001931j
[2025-09-12 10:42:15] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -55.179971-0.001047j
[2025-09-12 10:42:30] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -55.168637+0.001365j
[2025-09-12 10:42:40] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -55.185400+0.002912j
[2025-09-12 10:42:54] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -55.290486+0.002078j
[2025-09-12 10:43:09] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -55.225637+0.002358j
[2025-09-12 10:43:25] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -55.242214-0.001799j
[2025-09-12 10:43:40] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -55.275915+0.001462j
[2025-09-12 10:43:55] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -55.207471+0.001710j
[2025-09-12 10:44:11] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -55.387220-0.000768j
[2025-09-12 10:44:26] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -55.253192-0.000285j
[2025-09-12 10:44:41] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -55.383306+0.001145j
[2025-09-12 10:44:57] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -55.254590+0.000188j
[2025-09-12 10:45:12] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -55.308501-0.000376j
[2025-09-12 10:45:27] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -55.384308+0.001206j
[2025-09-12 10:45:43] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -55.391145-0.000588j
[2025-09-12 10:45:58] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -55.358304-0.000481j
[2025-09-12 10:46:13] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -55.245653-0.000888j
[2025-09-12 10:46:29] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -55.282630+0.000056j
[2025-09-12 10:46:29] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-12 10:46:44] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -55.252707-0.002005j
[2025-09-12 10:46:59] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -55.283482-0.002442j
[2025-09-12 10:47:15] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -55.334515-0.000595j
[2025-09-12 10:47:30] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -55.297235+0.002672j
[2025-09-12 10:47:45] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -55.152133-0.000132j
[2025-09-12 10:48:01] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -55.344885-0.002030j
[2025-09-12 10:48:16] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -55.419442-0.002128j
[2025-09-12 10:48:31] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -55.362170+0.000275j
[2025-09-12 10:48:47] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -55.416646+0.001825j
[2025-09-12 10:49:02] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -55.389564+0.000970j
[2025-09-12 10:49:17] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -55.290629-0.000457j
[2025-09-12 10:49:33] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -55.314758-0.000659j
[2025-09-12 10:49:48] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -55.340785-0.000443j
[2025-09-12 10:50:03] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -55.321826+0.000648j
[2025-09-12 10:50:19] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -55.408202-0.001210j
[2025-09-12 10:50:34] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -55.432720+0.000754j
[2025-09-12 10:50:49] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -55.358264-0.000987j
[2025-09-12 10:51:05] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -55.413417+0.000002j
[2025-09-12 10:51:20] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -55.350314-0.001766j
[2025-09-12 10:51:35] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -55.259547-0.001562j
[2025-09-12 10:51:51] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -55.227206+0.000634j
[2025-09-12 10:52:06] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -55.289433+0.001778j
[2025-09-12 10:52:21] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -55.282081+0.000379j
[2025-09-12 10:52:37] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -55.417222+0.001820j
[2025-09-12 10:52:52] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -55.322625+0.000063j
[2025-09-12 10:53:08] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -55.285461-0.000993j
[2025-09-12 10:53:23] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -55.229024+0.000268j
[2025-09-12 10:53:38] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -55.293359-0.003091j
[2025-09-12 10:53:54] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -55.289387-0.000668j
[2025-09-12 10:54:09] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -55.326302-0.000015j
[2025-09-12 10:54:24] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -55.323974+0.000303j
[2025-09-12 10:54:40] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -55.277345-0.000128j
[2025-09-12 10:54:55] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -55.161040-0.002129j
[2025-09-12 10:55:10] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -55.142657+0.001922j
[2025-09-12 10:55:26] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -55.220808-0.002602j
[2025-09-12 10:55:41] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -55.223349+0.001797j
[2025-09-12 10:55:56] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -55.381743-0.001892j
[2025-09-12 10:56:12] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -55.174768+0.000498j
[2025-09-12 10:56:27] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -55.403913-0.000076j
[2025-09-12 10:56:42] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -55.455788+0.000613j
[2025-09-12 10:56:58] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -55.320470-0.000107j
[2025-09-12 10:57:13] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -55.350356-0.000792j
[2025-09-12 10:57:28] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -55.295177-0.003060j
[2025-09-12 10:57:44] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -55.301594-0.001004j
[2025-09-12 10:57:59] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -55.340373-0.000043j
[2025-09-12 10:58:14] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -55.357237-0.000596j
[2025-09-12 10:58:30] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -55.283747+0.002537j
[2025-09-12 10:58:45] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -55.242161+0.000498j
[2025-09-12 10:59:00] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -55.264198+0.003205j
[2025-09-12 10:59:16] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -55.326043+0.001207j
[2025-09-12 10:59:31] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -55.333444+0.008631j
[2025-09-12 10:59:46] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -55.269715+0.000242j
[2025-09-12 11:00:02] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -55.280144+0.001417j
[2025-09-12 11:00:17] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -55.341761+0.000371j
[2025-09-12 11:00:32] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -55.332746+0.001304j
[2025-09-12 11:00:47] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -55.312602+0.001004j
[2025-09-12 11:01:03] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -55.376001+0.001070j
[2025-09-12 11:01:18] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -55.305979-0.000633j
[2025-09-12 11:01:33] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -55.252449+0.001919j
[2025-09-12 11:01:49] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -55.187353+0.000161j
[2025-09-12 11:02:04] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -55.226633-0.000746j
[2025-09-12 11:02:19] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -55.318070+0.003087j
[2025-09-12 11:02:35] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -55.294134-0.001514j
[2025-09-12 11:02:50] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -55.349166-0.001295j
[2025-09-12 11:03:05] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -55.288241-0.001430j
[2025-09-12 11:03:21] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -55.279574-0.000321j
[2025-09-12 11:03:36] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -55.305817-0.001783j
[2025-09-12 11:03:51] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -55.268738+0.000389j
[2025-09-12 11:04:07] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -55.296420-0.001862j
[2025-09-12 11:04:22] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -55.258655+0.001566j
[2025-09-12 11:04:37] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -55.265019-0.000063j
[2025-09-12 11:04:52] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -55.283694-0.003589j
[2025-09-12 11:05:08] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -55.255480+0.002104j
[2025-09-12 11:05:23] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -55.261959+0.002827j
[2025-09-12 11:05:38] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -55.265355+0.002799j
[2025-09-12 11:05:54] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -55.415336+0.002207j
[2025-09-12 11:06:09] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -55.213060+0.000928j
[2025-09-12 11:06:24] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -55.337104+0.001683j
[2025-09-12 11:06:39] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -55.285730+0.000432j
[2025-09-12 11:06:55] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -55.275230-0.000339j
[2025-09-12 11:07:10] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -55.207856-0.000408j
[2025-09-12 11:07:25] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -55.246862-0.000723j
[2025-09-12 11:07:41] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -55.192418+0.003847j
[2025-09-12 11:07:56] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -55.308175-0.000623j
[2025-09-12 11:08:11] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -55.299323-0.001105j
[2025-09-12 11:08:27] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -55.308673-0.000047j
[2025-09-12 11:08:42] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -55.294351-0.002977j
[2025-09-12 11:08:57] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -55.249009-0.000137j
[2025-09-12 11:09:12] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -55.267340+0.004115j
[2025-09-12 11:09:28] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -55.202710+0.002445j
[2025-09-12 11:09:43] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -55.190737+0.000056j
[2025-09-12 11:09:58] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -55.221034-0.001483j
[2025-09-12 11:10:14] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -55.345983+0.003685j
[2025-09-12 11:10:29] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -55.386330-0.001232j
[2025-09-12 11:10:45] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -55.366920+0.001809j
[2025-09-12 11:11:00] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -55.354138+0.001210j
[2025-09-12 11:11:15] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -55.500625-0.000405j
[2025-09-12 11:11:30] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -55.399491-0.001077j
[2025-09-12 11:11:46] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -55.458950-0.001350j
[2025-09-12 11:12:01] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -55.371496-0.003549j
[2025-09-12 11:12:17] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -55.412655-0.002632j
[2025-09-12 11:12:32] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -55.346133+0.002365j
[2025-09-12 11:12:47] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -55.387048-0.002792j
[2025-09-12 11:13:03] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -55.267158-0.000905j
[2025-09-12 11:13:18] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -55.259214+0.001726j
[2025-09-12 11:13:18] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-12 11:13:33] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -55.366543+0.001121j
[2025-09-12 11:13:49] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -55.360120+0.000150j
[2025-09-12 11:14:04] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -55.412716+0.000816j
[2025-09-12 11:14:19] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -55.527091+0.000884j
[2025-09-12 11:14:35] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -55.384419-0.000972j
[2025-09-12 11:14:50] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -55.379649+0.004917j
[2025-09-12 11:15:05] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -55.365554+0.000581j
[2025-09-12 11:15:21] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -55.212227+0.000715j
[2025-09-12 11:15:36] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -55.226547-0.001465j
[2025-09-12 11:15:51] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -55.342249-0.000334j
[2025-09-12 11:16:03] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -55.222854-0.001087j
[2025-09-12 11:16:13] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -55.217681-0.001460j
[2025-09-12 11:16:23] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -55.334597-0.001064j
[2025-09-12 11:16:33] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -55.252003-0.001267j
[2025-09-12 11:16:44] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -55.319263+0.000027j
[2025-09-12 11:16:54] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -55.184169+0.002772j
[2025-09-12 11:17:04] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -55.247199+0.001833j
[2025-09-12 11:17:17] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -55.317648+0.000049j
[2025-09-12 11:17:32] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -55.270027+0.000452j
[2025-09-12 11:17:48] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -55.238479-0.000670j
[2025-09-12 11:18:03] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -55.396231+0.000149j
[2025-09-12 11:18:18] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -55.370423+0.003115j
[2025-09-12 11:18:34] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -55.304855-0.002089j
[2025-09-12 11:18:49] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -55.380571-0.000599j
[2025-09-12 11:19:04] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -55.267221-0.000356j
[2025-09-12 11:19:20] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -55.376674+0.003138j
[2025-09-12 11:19:35] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -55.463001-0.000749j
[2025-09-12 11:19:50] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -55.289348-0.001539j
[2025-09-12 11:20:06] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -55.304848-0.001817j
[2025-09-12 11:20:21] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -55.202324+0.000198j
[2025-09-12 11:20:21] RESTART #2 | Period: 600
[2025-09-12 11:20:37] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -55.107267-0.000336j
[2025-09-12 11:20:52] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -55.194523-0.001997j
[2025-09-12 11:21:07] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -55.304891-0.002561j
[2025-09-12 11:21:23] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -55.298629+0.001776j
[2025-09-12 11:21:38] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -55.259988-0.001095j
[2025-09-12 11:21:53] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -55.193618-0.001087j
[2025-09-12 11:22:09] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -55.453527-0.000660j
[2025-09-12 11:22:24] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -55.431348-0.000192j
[2025-09-12 11:22:39] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -55.395875+0.000244j
[2025-09-12 11:22:55] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -55.211496+0.000371j
[2025-09-12 11:23:10] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -55.231588-0.004093j
[2025-09-12 11:23:26] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -55.293057-0.001638j
[2025-09-12 11:23:41] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -55.379209-0.000515j
[2025-09-12 11:23:56] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -55.418540+0.002642j
[2025-09-12 11:24:12] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -55.375973+0.000263j
[2025-09-12 11:24:27] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -55.299953+0.000831j
[2025-09-12 11:24:42] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -55.297764+0.001793j
[2025-09-12 11:24:58] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -55.253720+0.000539j
[2025-09-12 11:25:13] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -55.275843-0.000605j
[2025-09-12 11:25:28] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -55.391624+0.000646j
[2025-09-12 11:25:44] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -55.304973-0.002175j
[2025-09-12 11:25:59] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -55.157397+0.002295j
[2025-09-12 11:26:15] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -55.289375+0.000251j
[2025-09-12 11:26:30] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -55.274323-0.001941j
[2025-09-12 11:26:45] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -55.185284-0.000942j
[2025-09-12 11:26:55] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -55.349203-0.001755j
[2025-09-12 11:27:09] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -55.167970+0.000093j
[2025-09-12 11:27:19] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -55.238421+0.000411j
[2025-09-12 11:27:33] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -55.284122-0.005011j
[2025-09-12 11:27:49] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -55.379408-0.001540j
[2025-09-12 11:28:04] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -55.391331-0.000972j
[2025-09-12 11:28:19] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -55.126156+0.000898j
[2025-09-12 11:28:35] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -55.224140-0.001919j
[2025-09-12 11:28:50] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -55.189724+0.001924j
[2025-09-12 11:29:05] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -55.265131-0.002291j
[2025-09-12 11:29:21] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -55.245962+0.001476j
[2025-09-12 11:29:36] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -55.294369-0.001757j
[2025-09-12 11:29:51] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -55.368033+0.003607j
[2025-09-12 11:30:06] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -55.252818+0.000982j
[2025-09-12 11:30:22] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -55.196003-0.002351j
[2025-09-12 11:30:37] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -55.396723-0.000269j
[2025-09-12 11:30:52] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -55.262478-0.001018j
[2025-09-12 11:31:08] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -55.199188+0.000209j
[2025-09-12 11:31:23] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -55.207857+0.001512j
[2025-09-12 11:31:38] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -55.235941-0.000528j
[2025-09-12 11:31:54] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -55.164529-0.001879j
[2025-09-12 11:32:09] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -55.151396-0.002399j
[2025-09-12 11:32:24] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -55.152528-0.000706j
[2025-09-12 11:32:40] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -55.181001-0.000471j
[2025-09-12 11:32:55] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -55.204596-0.001558j
[2025-09-12 11:33:10] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -55.244874-0.000234j
[2025-09-12 11:33:26] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -55.150307-0.001056j
[2025-09-12 11:33:41] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -55.183019+0.000079j
[2025-09-12 11:33:56] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -55.389919+0.001487j
[2025-09-12 11:34:12] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -55.250873-0.002300j
[2025-09-12 11:34:27] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -55.295642+0.000841j
[2025-09-12 11:34:43] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -55.201543-0.002160j
[2025-09-12 11:34:58] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -55.331345+0.000973j
[2025-09-12 11:35:13] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -55.296451-0.000568j
[2025-09-12 11:35:29] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -55.317926+0.001146j
[2025-09-12 11:35:44] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -55.311758+0.000412j
[2025-09-12 11:35:59] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -55.378234+0.000357j
[2025-09-12 11:36:14] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -55.312981-0.002738j
[2025-09-12 11:36:30] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -55.454892+0.002582j
[2025-09-12 11:36:45] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -55.312174-0.000344j
[2025-09-12 11:37:00] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -55.340958-0.001786j
[2025-09-12 11:37:16] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -55.254832+0.002155j
[2025-09-12 11:37:31] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -55.382975-0.000216j
[2025-09-12 11:37:46] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -55.410458+0.002108j
[2025-09-12 11:38:02] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -55.229909-0.001166j
[2025-09-12 11:38:17] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -55.274853+0.004104j
[2025-09-12 11:38:32] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -55.304622+0.000236j
[2025-09-12 11:38:48] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -55.222944-0.001106j
[2025-09-12 11:39:03] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -55.200116+0.000980j
[2025-09-12 11:39:18] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -55.258216-0.000475j
[2025-09-12 11:39:18] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-12 11:39:34] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -55.394656-0.001275j
[2025-09-12 11:39:49] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -55.245500-0.001606j
[2025-09-12 11:40:04] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -55.278095+0.002440j
[2025-09-12 11:40:20] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -55.331110-0.001004j
[2025-09-12 11:40:35] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -55.333946-0.001266j
[2025-09-12 11:40:50] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -55.271538-0.001167j
[2025-09-12 11:41:06] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -55.340720-0.000218j
[2025-09-12 11:41:21] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -55.323207+0.001573j
[2025-09-12 11:41:36] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -55.327231-0.001257j
[2025-09-12 11:41:52] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -55.267715+0.000338j
[2025-09-12 11:42:07] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -55.321726+0.000545j
[2025-09-12 11:42:22] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -55.276027-0.001327j
[2025-09-12 11:42:38] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -55.349430-0.000782j
[2025-09-12 11:42:53] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -55.401848-0.001196j
[2025-09-12 11:43:08] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -55.258894+0.001208j
[2025-09-12 11:43:24] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -55.309756-0.002397j
[2025-09-12 11:43:39] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -55.241321+0.000967j
[2025-09-12 11:43:54] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -55.240876-0.000902j
[2025-09-12 11:44:10] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -55.322576+0.001566j
[2025-09-12 11:44:25] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -55.309772+0.001413j
[2025-09-12 11:44:40] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -55.396428-0.001144j
[2025-09-12 11:44:56] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -55.284357-0.002525j
[2025-09-12 11:45:11] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -55.352788-0.000405j
[2025-09-12 11:45:26] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -55.285617-0.001587j
[2025-09-12 11:45:42] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -55.215557+0.000647j
[2025-09-12 11:45:57] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -55.232919-0.000164j
[2025-09-12 11:46:12] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -55.285211+0.002129j
[2025-09-12 11:46:28] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -55.245279+0.001085j
[2025-09-12 11:46:43] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -55.411884+0.000757j
[2025-09-12 11:46:58] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -55.255552-0.002572j
[2025-09-12 11:47:14] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -55.351247-0.003584j
[2025-09-12 11:47:29] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -55.413820+0.001926j
[2025-09-12 11:47:44] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -55.279953+0.002888j
[2025-09-12 11:48:00] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -55.171665-0.000308j
[2025-09-12 11:48:15] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -55.312596+0.003869j
[2025-09-12 11:48:30] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -55.265361-0.001929j
[2025-09-12 11:48:46] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -55.355089+0.002128j
[2025-09-12 11:49:01] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -55.366353-0.002621j
[2025-09-12 11:49:16] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -55.380669-0.002036j
[2025-09-12 11:49:32] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -55.360017+0.002513j
[2025-09-12 11:49:47] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -55.482904-0.000990j
[2025-09-12 11:50:02] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -55.398628-0.000823j
[2025-09-12 11:50:18] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -55.414660-0.000772j
[2025-09-12 11:50:33] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -55.352156-0.001484j
[2025-09-12 11:50:48] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -55.370693-0.000547j
[2025-09-12 11:51:04] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -55.335240-0.000224j
[2025-09-12 11:51:19] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -55.375809-0.001422j
[2025-09-12 11:51:34] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -55.148650+0.000347j
[2025-09-12 11:51:50] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -55.092496-0.000164j
[2025-09-12 11:52:05] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -55.224050-0.000322j
[2025-09-12 11:52:20] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -55.213832-0.000262j
[2025-09-12 11:52:36] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -55.324206+0.001617j
[2025-09-12 11:52:51] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -55.333586+0.000011j
[2025-09-12 11:53:06] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -55.294641+0.001917j
[2025-09-12 11:53:22] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -55.215247-0.000369j
[2025-09-12 11:53:37] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -55.301777+0.001441j
[2025-09-12 11:53:52] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -55.141114-0.000325j
[2025-09-12 11:54:08] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -55.118371-0.000697j
[2025-09-12 11:54:23] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -55.235957+0.000763j
[2025-09-12 11:54:38] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -55.362951-0.000608j
[2025-09-12 11:54:54] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -55.216164+0.000199j
[2025-09-12 11:55:09] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -55.225739-0.000642j
[2025-09-12 11:55:24] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -55.290510+0.000228j
[2025-09-12 11:55:40] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -55.394218+0.001244j
[2025-09-12 11:55:55] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -55.376044+0.000082j
[2025-09-12 11:56:10] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -55.352490-0.002381j
[2025-09-12 11:56:26] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -55.274929+0.001840j
[2025-09-12 11:56:41] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -55.289335-0.000099j
[2025-09-12 11:56:56] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -55.288209+0.001913j
[2025-09-12 11:57:12] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -55.358205+0.003372j
[2025-09-12 11:57:27] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -55.276907-0.000958j
[2025-09-12 11:57:42] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -55.376296-0.002140j
[2025-09-12 11:57:58] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -55.307469-0.000792j
[2025-09-12 11:58:13] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -55.378062+0.002333j
[2025-09-12 11:58:28] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -55.315862+0.000234j
[2025-09-12 11:58:44] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -55.286893-0.002837j
[2025-09-12 11:58:59] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -55.222435+0.002209j
[2025-09-12 11:59:14] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -55.352506-0.002310j
[2025-09-12 11:59:30] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -55.295551+0.000793j
[2025-09-12 11:59:45] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -55.143896+0.000403j
[2025-09-12 12:00:00] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -55.281115+0.001981j
[2025-09-12 12:00:15] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -55.368216+0.000825j
[2025-09-12 12:00:31] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -55.328929+0.001074j
[2025-09-12 12:00:46] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -55.246008-0.002555j
[2025-09-12 12:01:01] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -55.313563-0.002643j
[2025-09-12 12:01:17] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -55.164729-0.001627j
[2025-09-12 12:01:27] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -55.317058-0.002594j
[2025-09-12 12:01:37] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -55.336221+0.000871j
[2025-09-12 12:01:48] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -55.303959-0.002290j
[2025-09-12 12:01:58] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -55.305500+0.003179j
[2025-09-12 12:02:08] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -55.436081-0.000163j
[2025-09-12 12:02:18] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -55.396189+0.000207j
[2025-09-12 12:02:30] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -55.530916-0.002916j
[2025-09-12 12:02:45] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -55.387573-0.002108j
[2025-09-12 12:03:01] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -55.376560+0.001567j
[2025-09-12 12:03:16] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -55.350042+0.000118j
[2025-09-12 12:03:32] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -55.331800+0.001285j
[2025-09-12 12:03:47] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -55.216196+0.001144j
[2025-09-12 12:04:02] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -55.246439-0.002965j
[2025-09-12 12:04:18] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -55.306772+0.003207j
[2025-09-12 12:04:33] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -55.326485-0.000684j
[2025-09-12 12:04:48] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -55.333590-0.000025j
[2025-09-12 12:05:04] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -55.373976-0.000228j
[2025-09-12 12:05:19] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -55.197341+0.002298j
[2025-09-12 12:05:35] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -55.260813-0.000946j
[2025-09-12 12:05:35] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-12 12:05:50] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -55.321012-0.002390j
[2025-09-12 12:06:05] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -55.310940-0.000119j
[2025-09-12 12:06:21] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -55.445934+0.000682j
[2025-09-12 12:06:36] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -55.376717-0.001198j
[2025-09-12 12:06:52] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -55.287462+0.001129j
[2025-09-12 12:07:07] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -55.368570-0.000469j
[2025-09-12 12:07:22] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -55.321898+0.000499j
[2025-09-12 12:07:38] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -55.288246+0.000037j
[2025-09-12 12:07:53] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -55.395446+0.002803j
[2025-09-12 12:08:08] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -55.339156-0.000033j
[2025-09-12 12:08:24] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -55.389988-0.000817j
[2025-09-12 12:08:39] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -55.165268+0.003126j
[2025-09-12 12:08:54] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -55.229370+0.002163j
[2025-09-12 12:09:10] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -55.318060+0.003425j
[2025-09-12 12:09:25] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -55.361079-0.000492j
[2025-09-12 12:09:41] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -55.363438-0.000081j
[2025-09-12 12:09:56] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -55.270178-0.001859j
[2025-09-12 12:10:12] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -55.165643-0.001755j
[2025-09-12 12:10:27] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -55.298589+0.000302j
[2025-09-12 12:10:42] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -55.290271-0.002040j
[2025-09-12 12:10:58] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -55.294749-0.003086j
[2025-09-12 12:11:13] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -55.303598-0.001870j
[2025-09-12 12:11:29] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -55.303683-0.001347j
[2025-09-12 12:11:44] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -55.338428-0.001177j
[2025-09-12 12:11:58] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -55.331881-0.000249j
[2025-09-12 12:12:08] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -55.323390+0.000067j
[2025-09-12 12:12:22] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -55.364907-0.003830j
[2025-09-12 12:12:32] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -55.240752+0.000073j
[2025-09-12 12:12:47] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -55.226103+0.002138j
[2025-09-12 12:13:02] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -55.289738-0.001074j
[2025-09-12 12:13:18] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -55.394569-0.000195j
[2025-09-12 12:13:33] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -55.334999-0.000462j
[2025-09-12 12:13:48] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -55.217770+0.003940j
[2025-09-12 12:14:04] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -55.251439+0.001328j
[2025-09-12 12:14:19] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -55.301308-0.004161j
[2025-09-12 12:14:34] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -55.364198-0.001154j
[2025-09-12 12:14:50] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -55.386760-0.000040j
[2025-09-12 12:15:05] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -55.248266+0.000751j
[2025-09-12 12:15:20] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -55.224482+0.000156j
[2025-09-12 12:15:36] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -55.276809-0.000432j
[2025-09-12 12:15:51] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -55.274644-0.000561j
[2025-09-12 12:16:06] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -55.462467-0.001979j
[2025-09-12 12:16:22] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -55.440213+0.000241j
[2025-09-12 12:16:37] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -55.437713-0.000378j
[2025-09-12 12:16:52] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -55.268803+0.002789j
[2025-09-12 12:17:08] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -55.382026+0.000302j
[2025-09-12 12:17:23] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -55.323762-0.000109j
[2025-09-12 12:17:38] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -55.305179-0.000667j
[2025-09-12 12:17:54] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -55.384915-0.001089j
[2025-09-12 12:18:09] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -55.221900+0.001270j
[2025-09-12 12:18:24] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -55.397586-0.002423j
[2025-09-12 12:18:40] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -55.346140+0.001085j
[2025-09-12 12:18:55] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -55.375015+0.000165j
[2025-09-12 12:19:10] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -55.443245-0.000698j
[2025-09-12 12:19:26] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -55.334706+0.000731j
[2025-09-12 12:19:41] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -55.314599+0.002360j
[2025-09-12 12:19:56] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -55.358268+0.001266j
[2025-09-12 12:20:12] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -55.332769+0.000516j
[2025-09-12 12:20:27] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -55.203387+0.004087j
[2025-09-12 12:20:42] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -55.263906-0.001254j
[2025-09-12 12:20:58] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -55.286463+0.000782j
[2025-09-12 12:21:13] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -55.327845-0.000317j
[2025-09-12 12:21:28] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -55.365187+0.000382j
[2025-09-12 12:21:44] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -55.272188+0.002705j
[2025-09-12 12:21:59] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -55.157028+0.001606j
[2025-09-12 12:22:14] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -55.282943-0.000180j
[2025-09-12 12:22:30] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -55.258813+0.000465j
[2025-09-12 12:22:45] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -55.332373+0.002839j
[2025-09-12 12:23:00] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -55.300922-0.000436j
[2025-09-12 12:23:16] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -55.302458-0.001938j
[2025-09-12 12:23:31] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -55.375807+0.000190j
[2025-09-12 12:23:46] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -55.456621+0.002715j
[2025-09-12 12:24:01] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -55.458780+0.002457j
[2025-09-12 12:24:17] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -55.457403-0.001530j
[2025-09-12 12:24:32] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -55.423957+0.001566j
[2025-09-12 12:24:47] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -55.373150+0.002377j
[2025-09-12 12:25:03] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -55.290259-0.001028j
[2025-09-12 12:25:18] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -55.298226-0.000899j
[2025-09-12 12:25:33] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -55.344342-0.001800j
[2025-09-12 12:25:49] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -55.329057+0.000799j
[2025-09-12 12:26:04] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -55.424032+0.001298j
[2025-09-12 12:26:19] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -55.278270+0.000173j
[2025-09-12 12:26:35] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -55.380721-0.001109j
[2025-09-12 12:26:50] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -55.283093+0.002831j
[2025-09-12 12:27:05] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -55.251225+0.000383j
[2025-09-12 12:27:21] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -55.264226-0.000410j
[2025-09-12 12:27:36] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -55.317810+0.002975j
[2025-09-12 12:27:51] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -55.338429+0.000515j
[2025-09-12 12:28:07] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -55.246829+0.001409j
[2025-09-12 12:28:22] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -55.206830+0.000728j
[2025-09-12 12:28:37] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -55.244445+0.002036j
[2025-09-12 12:28:53] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -55.328228-0.000785j
[2025-09-12 12:29:08] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -55.370833+0.002485j
[2025-09-12 12:29:23] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -55.526211+0.004477j
[2025-09-12 12:29:38] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -55.318220-0.000182j
[2025-09-12 12:29:54] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -55.285197-0.001659j
[2025-09-12 12:30:09] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -55.158350-0.002314j
[2025-09-12 12:30:24] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -55.312302-0.001012j
[2025-09-12 12:30:40] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -55.345017-0.000988j
[2025-09-12 12:30:55] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -55.319913-0.000356j
[2025-09-12 12:31:10] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -55.361733-0.000469j
[2025-09-12 12:31:26] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -55.344687-0.002444j
[2025-09-12 12:31:41] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -55.345265+0.000486j
[2025-09-12 12:31:56] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -55.359646-0.000386j
[2025-09-12 12:32:11] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -55.297374+0.003778j
[2025-09-12 12:32:12] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-12 12:32:27] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -55.309903+0.004185j
[2025-09-12 12:32:42] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -55.363036+0.001064j
[2025-09-12 12:32:55] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -55.322076+0.000550j
[2025-09-12 12:33:06] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -55.205927-0.001925j
[2025-09-12 12:33:16] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -55.186321-0.000269j
[2025-09-12 12:33:26] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -55.253444-0.001249j
[2025-09-12 12:33:36] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -55.281577-0.000613j
[2025-09-12 12:33:46] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -55.254838-0.001561j
[2025-09-12 12:33:57] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -55.231987+0.000663j
[2025-09-12 12:34:07] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -55.230407+0.002316j
[2025-09-12 12:34:17] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -55.268971-0.001396j
[2025-09-12 12:34:27] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -55.293103+0.003362j
[2025-09-12 12:34:37] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -55.191450-0.000481j
[2025-09-12 12:34:48] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -55.278941+0.001968j
[2025-09-12 12:34:58] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -55.238620+0.003110j
[2025-09-12 12:35:08] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -55.286130+0.000189j
[2025-09-12 12:35:18] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -55.378803+0.000088j
[2025-09-12 12:35:28] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -55.416404+0.002707j
[2025-09-12 12:35:39] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -55.347940+0.001628j
[2025-09-12 12:35:49] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -55.256049-0.000502j
[2025-09-12 12:35:59] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -55.395563-0.000215j
[2025-09-12 12:36:09] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -55.160157+0.000186j
[2025-09-12 12:36:19] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -55.364741-0.001116j
[2025-09-12 12:36:30] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -55.268696-0.000999j
[2025-09-12 12:36:40] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -55.268843+0.002455j
[2025-09-12 12:36:50] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -55.224890-0.000488j
[2025-09-12 12:37:00] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -55.297051-0.000133j
[2025-09-12 12:37:10] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -55.226216+0.002284j
[2025-09-12 12:37:21] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -55.387922-0.001742j
[2025-09-12 12:37:31] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -55.333212+0.003274j
[2025-09-12 12:37:41] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -55.258025+0.000890j
[2025-09-12 12:37:51] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -55.263503+0.001379j
[2025-09-12 12:38:01] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -55.333101+0.000548j
[2025-09-12 12:38:12] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -55.281037+0.003131j
[2025-09-12 12:38:22] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -55.279952-0.000800j
[2025-09-12 12:38:32] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -55.291819+0.000207j
[2025-09-12 12:38:42] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -55.314524-0.001433j
[2025-09-12 12:38:52] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -55.279823-0.003582j
[2025-09-12 12:39:03] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -55.231680-0.000750j
[2025-09-12 12:39:13] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -55.223814+0.000105j
[2025-09-12 12:39:23] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -55.309553-0.000156j
[2025-09-12 12:39:33] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -55.405662+0.000143j
[2025-09-12 12:39:44] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -55.322168-0.001880j
[2025-09-12 12:39:54] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -55.362588+0.000491j
[2025-09-12 12:40:04] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -55.320405+0.000587j
[2025-09-12 12:40:14] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -55.441822-0.002964j
[2025-09-12 12:40:24] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -55.342026+0.000386j
[2025-09-12 12:40:34] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -55.343728-0.000818j
[2025-09-12 12:40:45] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -55.289396-0.001794j
[2025-09-12 12:40:55] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -55.339560-0.000878j
[2025-09-12 12:41:05] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -55.237931-0.001684j
[2025-09-12 12:41:15] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -55.201330+0.001718j
[2025-09-12 12:41:25] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -55.235313+0.000028j
[2025-09-12 12:41:36] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -55.168344+0.000694j
[2025-09-12 12:41:41] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -55.125417-0.002058j
[2025-09-12 12:41:46] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -55.196485+0.002499j
[2025-09-12 12:41:51] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -55.337529-0.000890j
[2025-09-12 12:41:55] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -55.252497-0.000550j
[2025-09-12 12:42:01] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -55.242478-0.001942j
[2025-09-12 12:42:06] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -55.337309-0.001559j
[2025-09-12 12:42:10] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -55.284894+0.001201j
[2025-09-12 12:42:15] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -55.231918-0.003580j
[2025-09-12 12:42:20] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -55.228969+0.000043j
[2025-09-12 12:42:30] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -55.233718+0.001242j
[2025-09-12 12:42:40] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -55.331898-0.002722j
[2025-09-12 12:42:51] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -55.303115+0.001016j
[2025-09-12 12:43:01] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -55.378202-0.000245j
[2025-09-12 12:43:11] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -55.350793+0.000201j
[2025-09-12 12:43:22] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -55.204430-0.000842j
[2025-09-12 12:43:32] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -55.188842+0.001099j
[2025-09-12 12:43:42] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -55.252313-0.000385j
[2025-09-12 12:43:52] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -55.233447-0.002923j
[2025-09-12 12:44:03] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -55.341211+0.002024j
[2025-09-12 12:44:13] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -55.188414-0.000445j
[2025-09-12 12:44:23] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -55.390584+0.001830j
[2025-09-12 12:44:34] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -55.309248+0.001743j
[2025-09-12 12:44:44] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -55.214417+0.001394j
[2025-09-12 12:44:54] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -55.177305-0.001444j
[2025-09-12 12:45:04] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -55.191057+0.001862j
[2025-09-12 12:45:15] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -55.153017-0.000563j
[2025-09-12 12:45:25] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -55.360792-0.000259j
[2025-09-12 12:45:35] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -55.239965+0.001392j
[2025-09-12 12:45:46] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -55.280265+0.000586j
[2025-09-12 12:45:56] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -55.214697-0.001859j
[2025-09-12 12:46:06] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -55.264008+0.003794j
[2025-09-12 12:46:17] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -55.367089-0.002505j
[2025-09-12 12:46:27] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -55.382314+0.000248j
[2025-09-12 12:46:37] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -55.359388-0.001424j
[2025-09-12 12:46:47] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -55.232151+0.001108j
[2025-09-12 12:46:58] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -55.310869-0.004000j
[2025-09-12 12:47:08] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -55.107367-0.000877j
[2025-09-12 12:47:18] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -55.148764-0.002784j
[2025-09-12 12:47:29] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -55.275242-0.000676j
[2025-09-12 12:47:39] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -55.238304+0.001503j
[2025-09-12 12:47:49] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -55.304692-0.000914j
[2025-09-12 12:47:59] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -55.285610+0.001496j
[2025-09-12 12:48:10] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -55.297137+0.000749j
[2025-09-12 12:48:20] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -55.303305+0.000037j
[2025-09-12 12:48:30] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -55.277181+0.000043j
[2025-09-12 12:48:41] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -55.323032-0.004275j
[2025-09-12 12:48:46] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -55.343723+0.001380j
[2025-09-12 12:48:51] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -55.479195-0.001939j
[2025-09-12 12:49:00] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -55.427815+0.000965j
[2025-09-12 12:49:05] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -55.325482+0.000683j
[2025-09-12 12:49:11] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -55.266717-0.001920j
[2025-09-12 12:49:11] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-12 12:49:21] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -55.336227-0.002831j
[2025-09-12 12:49:32] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -55.272437+0.002170j
[2025-09-12 12:49:42] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -55.212638+0.001592j
[2025-09-12 12:49:52] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -55.281195-0.000286j
[2025-09-12 12:50:02] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -55.319376+0.000755j
[2025-09-12 12:50:12] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -55.328597+0.003613j
[2025-09-12 12:50:23] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -55.278139+0.003048j
[2025-09-12 12:50:33] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -55.222867+0.000450j
[2025-09-12 12:50:43] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -55.233264-0.001959j
[2025-09-12 12:50:53] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -55.127346-0.001074j
[2025-09-12 12:51:03] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -55.273373-0.000375j
[2025-09-12 12:51:14] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -55.197030-0.000914j
[2025-09-12 12:51:24] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -55.270240-0.001664j
[2025-09-12 12:51:34] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -55.394200-0.000692j
[2025-09-12 12:51:44] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -55.316230+0.002064j
[2025-09-12 12:51:54] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -55.407622-0.000797j
[2025-09-12 12:52:05] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -55.154127-0.000299j
[2025-09-12 12:52:15] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -55.220980-0.000408j
[2025-09-12 12:52:25] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -55.211122+0.000994j
[2025-09-12 12:52:35] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -55.340467-0.000331j
[2025-09-12 12:52:46] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -55.382772+0.001130j
[2025-09-12 12:52:56] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -55.235367+0.000069j
[2025-09-12 12:53:06] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -55.285839-0.000064j
[2025-09-12 12:53:16] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -55.404946+0.002834j
[2025-09-12 12:53:26] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -55.346428+0.001075j
[2025-09-12 12:53:37] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -55.239191-0.000438j
[2025-09-12 12:53:47] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -55.377597-0.001776j
[2025-09-12 12:53:57] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -55.325772+0.002482j
[2025-09-12 12:54:07] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -55.280613-0.002007j
[2025-09-12 12:54:17] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -55.328811+0.000427j
[2025-09-12 12:54:28] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -55.323473-0.001571j
[2025-09-12 12:54:38] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -55.328220+0.002280j
[2025-09-12 12:54:48] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -55.295949-0.000196j
[2025-09-12 12:54:58] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -55.321438+0.001852j
[2025-09-12 12:55:08] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -55.255119-0.000631j
[2025-09-12 12:55:19] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -55.360709-0.000407j
[2025-09-12 12:55:29] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -55.362811-0.000052j
[2025-09-12 12:55:39] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -55.310083-0.000547j
[2025-09-12 12:55:49] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -55.272675+0.000268j
[2025-09-12 12:55:59] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -55.237150-0.000253j
[2025-09-12 12:56:10] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -55.267701-0.000257j
[2025-09-12 12:56:20] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -55.218645-0.000080j
[2025-09-12 12:56:30] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -55.153542-0.002660j
[2025-09-12 12:56:40] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -55.163844-0.001450j
[2025-09-12 12:56:51] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -55.283708-0.000971j
[2025-09-12 12:57:01] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -55.354796+0.000702j
[2025-09-12 12:57:11] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -55.329484-0.001439j
[2025-09-12 12:57:21] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -55.311382-0.000163j
[2025-09-12 12:57:31] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -55.214458-0.002228j
[2025-09-12 12:57:42] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -55.180073+0.002849j
[2025-09-12 12:57:52] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -55.196476-0.001473j
[2025-09-12 12:58:02] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -55.292685-0.001665j
[2025-09-12 12:58:12] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -55.262698+0.000578j
[2025-09-12 12:58:22] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -55.182064-0.000360j
[2025-09-12 12:58:33] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -55.187214+0.000922j
[2025-09-12 12:58:43] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -55.167412-0.000726j
[2025-09-12 12:58:53] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -55.215314-0.000154j
[2025-09-12 12:59:03] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -55.292677+0.001393j
[2025-09-12 12:59:13] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -55.294659-0.000495j
[2025-09-12 12:59:24] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -55.272823+0.001855j
[2025-09-12 12:59:34] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -55.258287+0.000076j
[2025-09-12 12:59:44] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -55.339800+0.000759j
[2025-09-12 12:59:54] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -55.283931-0.001519j
[2025-09-12 13:00:04] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -55.249516+0.000270j
[2025-09-12 13:00:15] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -55.208205+0.000320j
[2025-09-12 13:00:25] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -55.262411+0.001061j
[2025-09-12 13:00:35] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -55.376234+0.001223j
[2025-09-12 13:00:45] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -55.418599-0.002139j
[2025-09-12 13:00:56] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -55.267002+0.001597j
[2025-09-12 13:01:06] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -55.389263-0.001271j
[2025-09-12 13:01:16] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -55.463299-0.003022j
[2025-09-12 13:01:26] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -55.345368-0.000436j
[2025-09-12 13:01:36] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -55.378330-0.001301j
[2025-09-12 13:01:47] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -55.202722-0.000928j
[2025-09-12 13:01:57] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -55.322501-0.002470j
[2025-09-12 13:02:07] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -55.456692-0.001005j
[2025-09-12 13:02:17] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -55.414691+0.000600j
[2025-09-12 13:02:27] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -55.352059-0.003199j
[2025-09-12 13:02:38] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -55.334647+0.000711j
[2025-09-12 13:02:48] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -55.150892+0.001908j
[2025-09-12 13:02:58] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -55.242247-0.001274j
[2025-09-12 13:03:08] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -55.320604-0.000067j
[2025-09-12 13:03:18] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -55.158038-0.000217j
[2025-09-12 13:03:29] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -55.320384+0.001189j
[2025-09-12 13:03:39] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -55.189814-0.000419j
[2025-09-12 13:03:49] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -55.227152+0.000120j
[2025-09-12 13:03:59] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -55.257975+0.001289j
[2025-09-12 13:04:09] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -55.211513-0.000037j
[2025-09-12 13:04:20] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -55.187466+0.001130j
[2025-09-12 13:04:30] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -55.236775+0.001349j
[2025-09-12 13:04:40] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -55.329411+0.000590j
[2025-09-12 13:04:50] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -55.180960+0.000955j
[2025-09-12 13:05:01] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -55.216604-0.000955j
[2025-09-12 13:05:11] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -55.248050-0.001116j
[2025-09-12 13:05:21] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -55.263665-0.002642j
[2025-09-12 13:05:31] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -55.285698+0.003676j
[2025-09-12 13:05:41] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -55.232538-0.000308j
[2025-09-12 13:05:52] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -55.290225+0.000143j
[2025-09-12 13:06:02] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -55.340631+0.002079j
[2025-09-12 13:06:12] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -55.324627+0.002244j
[2025-09-12 13:06:22] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -55.326745-0.000431j
[2025-09-12 13:06:32] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -55.408673+0.000085j
[2025-09-12 13:06:43] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -55.341199-0.003040j
[2025-09-12 13:06:53] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -55.269065-0.001929j
[2025-09-12 13:07:03] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -55.250549+0.001893j
[2025-09-12 13:07:03] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-12 13:07:13] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -55.164942+0.001738j
[2025-09-12 13:07:23] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -55.138156+0.000191j
[2025-09-12 13:07:34] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -55.010899+0.000442j
[2025-09-12 13:07:44] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -55.262297-0.001284j
[2025-09-12 13:07:54] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -55.148557+0.000177j
[2025-09-12 13:08:04] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -55.324346-0.000036j
[2025-09-12 13:08:14] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -55.228023+0.000982j
[2025-09-12 13:08:25] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -55.293713-0.000659j
[2025-09-12 13:08:35] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -55.252727-0.000469j
[2025-09-12 13:08:45] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -55.284320-0.002106j
[2025-09-12 13:08:55] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -55.332817+0.001991j
[2025-09-12 13:09:05] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -55.248166+0.001992j
[2025-09-12 13:09:16] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -55.211733+0.004022j
[2025-09-12 13:09:26] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -55.333969+0.000146j
[2025-09-12 13:09:36] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -55.299229-0.000294j
[2025-09-12 13:09:46] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -55.274756-0.002306j
[2025-09-12 13:09:56] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -55.264959-0.000088j
[2025-09-12 13:10:07] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -55.351985+0.000182j
[2025-09-12 13:10:17] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -55.368183-0.002708j
[2025-09-12 13:10:27] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -55.359756+0.000306j
[2025-09-12 13:10:37] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -55.399876+0.000069j
[2025-09-12 13:10:48] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -55.238299+0.000641j
[2025-09-12 13:10:58] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -55.233891+0.001388j
[2025-09-12 13:11:08] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -55.249658-0.002796j
[2025-09-12 13:11:18] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -55.180612-0.003127j
[2025-09-12 13:11:28] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -55.234678+0.000064j
[2025-09-12 13:11:35] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -55.346587+0.000165j
[2025-09-12 13:11:40] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -55.276898+0.000715j
[2025-09-12 13:11:44] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -55.353821-0.000998j
[2025-09-12 13:11:49] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -55.339788-0.000024j
[2025-09-12 13:11:54] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -55.357253-0.000436j
[2025-09-12 13:11:59] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -55.207766-0.001516j
[2025-09-12 13:12:04] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -55.333359+0.000285j
[2025-09-12 13:12:09] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -55.265431-0.002817j
[2025-09-12 13:12:14] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -55.388961+0.003478j
[2025-09-12 13:12:23] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -55.317584-0.000160j
[2025-09-12 13:12:33] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -55.335371-0.003510j
[2025-09-12 13:12:43] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -55.226930+0.002474j
[2025-09-12 13:12:53] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -55.150549+0.000230j
[2025-09-12 13:13:04] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -55.299501-0.001645j
[2025-09-12 13:13:14] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -55.276023+0.000262j
[2025-09-12 13:13:24] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -55.337123-0.002071j
[2025-09-12 13:13:35] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -55.212737+0.002354j
[2025-09-12 13:13:45] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -55.247280-0.002018j
[2025-09-12 13:13:55] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -55.265103-0.000763j
[2025-09-12 13:14:06] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -55.268321-0.001376j
[2025-09-12 13:14:16] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -55.281364-0.001476j
[2025-09-12 13:14:26] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -55.284794-0.000094j
[2025-09-12 13:14:36] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -55.458201+0.000558j
[2025-09-12 13:14:47] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -55.468798-0.000723j
[2025-09-12 13:14:57] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -55.429837-0.001222j
[2025-09-12 13:15:07] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -55.481311+0.000134j
[2025-09-12 13:15:18] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -55.409535+0.003546j
[2025-09-12 13:15:28] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -55.324621+0.001252j
[2025-09-12 13:15:38] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -55.271227+0.002411j
[2025-09-12 13:15:49] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -55.313509+0.000794j
[2025-09-12 13:15:59] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -55.240205-0.000727j
[2025-09-12 13:16:09] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -55.274096+0.000255j
[2025-09-12 13:16:19] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -55.269260-0.002551j
[2025-09-12 13:16:30] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -55.181498-0.000088j
[2025-09-12 13:16:40] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -55.321216+0.001397j
[2025-09-12 13:16:50] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -55.341644+0.001086j
[2025-09-12 13:17:01] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -55.157882+0.002709j
[2025-09-12 13:17:11] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -55.290095-0.002287j
[2025-09-12 13:17:21] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -55.377707+0.000763j
[2025-09-12 13:17:32] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -55.307362-0.000915j
[2025-09-12 13:17:42] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -55.248490+0.000484j
[2025-09-12 13:17:52] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -55.393261+0.000105j
[2025-09-12 13:18:02] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -55.247144-0.000279j
[2025-09-12 13:18:13] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -55.230076+0.001053j
[2025-09-12 13:18:23] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -55.300376+0.003203j
[2025-09-12 13:18:33] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -55.242024-0.002342j
[2025-09-12 13:18:40] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -55.264403-0.000602j
[2025-09-12 13:18:45] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -55.373406+0.000744j
[2025-09-12 13:18:53] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -55.184142+0.002802j
[2025-09-12 13:18:59] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -55.280804-0.000287j
[2025-09-12 13:19:04] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -55.118239-0.001570j
[2025-09-12 13:19:14] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -55.281669+0.000597j
[2025-09-12 13:19:24] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -55.309571+0.000573j
[2025-09-12 13:19:34] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -55.277092-0.000361j
[2025-09-12 13:19:44] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -55.334324+0.001975j
[2025-09-12 13:19:55] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -55.440930+0.001219j
[2025-09-12 13:20:05] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -55.320484+0.002009j
[2025-09-12 13:20:15] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -55.274674+0.003742j
[2025-09-12 13:20:25] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -55.239116+0.001033j
[2025-09-12 13:20:36] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -55.183016+0.001044j
[2025-09-12 13:20:46] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -55.266699+0.000471j
[2025-09-12 13:20:56] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -55.146490-0.000978j
[2025-09-12 13:21:06] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -55.212789+0.000354j
[2025-09-12 13:21:16] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -55.287435-0.000684j
[2025-09-12 13:21:27] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -55.130493-0.000180j
[2025-09-12 13:21:37] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -55.178989+0.001538j
[2025-09-12 13:21:47] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -55.186108+0.001222j
[2025-09-12 13:21:57] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -55.287405+0.002305j
[2025-09-12 13:22:07] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -55.196789-0.001122j
[2025-09-12 13:22:18] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -55.177529-0.006050j
[2025-09-12 13:22:28] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -55.250393+0.001277j
[2025-09-12 13:22:38] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -55.233943-0.001199j
[2025-09-12 13:22:48] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -55.191584+0.000481j
[2025-09-12 13:22:58] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -55.237631-0.000018j
[2025-09-12 13:23:09] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -55.187663-0.002111j
[2025-09-12 13:23:19] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -55.224138+0.003095j
[2025-09-12 13:23:29] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -55.216361+0.000820j
[2025-09-12 13:23:39] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -55.193069+0.001938j
[2025-09-12 13:23:50] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -55.229365-0.001882j
[2025-09-12 13:23:50] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-12 13:23:50] ✅ Training completed | Restarts: 2
[2025-09-12 13:23:50] ============================================================
[2025-09-12 13:23:50] Training completed | Runtime: 14325.9s
[2025-09-12 13:23:54] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-12 13:23:54] ============================================================
