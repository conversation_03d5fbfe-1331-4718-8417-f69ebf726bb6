[2025-09-13 17:55:12] 使用checkpoint文件: results/L=4/J2=0.03/J1=0.10/training/checkpoints/checkpoint_iter_000210.pkl
[2025-09-13 17:55:29] ✓ 从checkpoint加载参数: 210
[2025-09-13 17:55:29]   - 能量: -55.272769+0.003498j ± 0.082347
[2025-09-13 17:55:29] ================================================================================
[2025-09-13 17:55:29] 加载量子态: L=4, J2=0.03, J1=0.10, checkpoint=checkpoint_iter_000210
[2025-09-13 17:55:29] 使用采样数目: 1048576
[2025-09-13 17:55:29] 设置样本数为: 1048576
[2025-09-13 17:55:29] 开始生成共享样本集...
[2025-09-13 17:58:30] 样本生成完成,耗时: 180.549 秒
[2025-09-13 17:58:30] ================================================================================
[2025-09-13 17:58:30] 开始计算自旋结构因子...
[2025-09-13 17:58:30] 初始化操作符缓存...
[2025-09-13 17:58:30] 预构建所有自旋相关操作符...
[2025-09-13 17:58:30] 开始计算自旋相关函数...
[2025-09-13 17:58:44] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 13.988s
[2025-09-13 17:59:01] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 17.680s
[2025-09-13 17:59:11] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.279s
[2025-09-13 17:59:20] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.393s
[2025-09-13 17:59:30] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.386s
[2025-09-13 17:59:39] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.389s
[2025-09-13 17:59:48] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.357s
[2025-09-13 17:59:58] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.398s
[2025-09-13 18:00:07] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.359s
[2025-09-13 18:00:16] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.390s
[2025-09-13 18:00:26] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.362s
[2025-09-13 18:00:35] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.327s
[2025-09-13 18:00:45] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.356s
[2025-09-13 18:00:54] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.385s
[2025-09-13 18:01:03] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.391s
[2025-09-13 18:01:13] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.359s
[2025-09-13 18:01:22] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.387s
[2025-09-13 18:01:31] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.374s
[2025-09-13 18:01:41] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.397s
[2025-09-13 18:01:50] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.387s
[2025-09-13 18:02:00] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.391s
[2025-09-13 18:02:09] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.394s
[2025-09-13 18:02:18] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.331s
[2025-09-13 18:02:28] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.380s
[2025-09-13 18:02:37] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.391s
[2025-09-13 18:02:47] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.385s
[2025-09-13 18:02:56] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.351s
[2025-09-13 18:03:05] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.388s
[2025-09-13 18:03:15] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.374s
[2025-09-13 18:03:24] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.354s
[2025-09-13 18:03:33] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.381s
[2025-09-13 18:03:43] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.384s
[2025-09-13 18:03:52] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.383s
[2025-09-13 18:04:02] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.329s
[2025-09-13 18:04:11] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.336s
[2025-09-13 18:04:20] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.370s
[2025-09-13 18:04:30] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.349s
[2025-09-13 18:04:39] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.374s
[2025-09-13 18:04:48] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.346s
[2025-09-13 18:04:58] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.358s
[2025-09-13 18:05:07] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.344s
[2025-09-13 18:05:17] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.345s
[2025-09-13 18:05:26] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.343s
[2025-09-13 18:05:35] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.376s
[2025-09-13 18:05:45] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.322s
[2025-09-13 18:05:54] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.352s
[2025-09-13 18:06:03] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.380s
[2025-09-13 18:06:13] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.343s
[2025-09-13 18:06:22] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.363s
[2025-09-13 18:06:31] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.360s
[2025-09-13 18:06:41] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.316s
[2025-09-13 18:06:50] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.357s
[2025-09-13 18:07:00] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.356s
[2025-09-13 18:07:09] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.374s
[2025-09-13 18:07:18] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.344s
[2025-09-13 18:07:28] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.357s
[2025-09-13 18:07:37] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.361s
[2025-09-13 18:07:46] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.348s
[2025-09-13 18:07:56] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.371s
[2025-09-13 18:08:05] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.363s
[2025-09-13 18:08:14] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.351s
[2025-09-13 18:08:24] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.347s
[2025-09-13 18:08:33] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.375s
[2025-09-13 18:08:43] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.359s
[2025-09-13 18:08:43] 自旋相关函数计算完成,总耗时 612.83 秒
[2025-09-13 18:08:44] 计算傅里叶变换...
[2025-09-13 18:08:46] 自旋结构因子计算完成
[2025-09-13 18:08:47] 自旋相关函数平均误差: 0.000653
