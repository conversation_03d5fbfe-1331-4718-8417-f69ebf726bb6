[2025-09-12 00:31:49] ✓ 从checkpoint恢复: results/L=4/J2=0.03/J1=0.03/training/checkpoints/final_GCNN.pkl
[2025-09-12 00:31:49]   - 迭代次数: final
[2025-09-12 00:31:49]   - 能量: -52.466196+0.001200j ± 0.085244
[2025-09-12 00:31:49]   - 时间戳: 2025-09-12T00:31:01.536779+08:00
[2025-09-12 00:32:15] ✓ 变分状态参数已从checkpoint恢复
[2025-09-12 00:32:15] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-12 00:32:15] ==================================================
[2025-09-12 00:32:15] GCNN for Shastry-Sutherland Model
[2025-09-12 00:32:15] ==================================================
[2025-09-12 00:32:15] System parameters:
[2025-09-12 00:32:15]   - System size: L=4, N=64
[2025-09-12 00:32:15]   - System parameters: J1=0.02, J2=0.03, Q=0.97
[2025-09-12 00:32:15] --------------------------------------------------
[2025-09-12 00:32:15] Model parameters:
[2025-09-12 00:32:15]   - Number of layers = 4
[2025-09-12 00:32:15]   - Number of features = 4
[2025-09-12 00:32:15]   - Total parameters = 12572
[2025-09-12 00:32:15] --------------------------------------------------
[2025-09-12 00:32:15] Training parameters:
[2025-09-12 00:32:15]   - Learning rate: 0.015
[2025-09-12 00:32:15]   - Total iterations: 1050
[2025-09-12 00:32:15]   - Annealing cycles: 3
[2025-09-12 00:32:15]   - Initial period: 150
[2025-09-12 00:32:15]   - Period multiplier: 2.0
[2025-09-12 00:32:15]   - Temperature range: 0.0-1.0
[2025-09-12 00:32:15]   - Samples: 4096
[2025-09-12 00:32:15]   - Discarded samples: 0
[2025-09-12 00:32:15]   - Chunk size: 2048
[2025-09-12 00:32:15]   - Diagonal shift: 0.2
[2025-09-12 00:32:15]   - Gradient clipping: 1.0
[2025-09-12 00:32:15]   - Checkpoint enabled: interval=105
[2025-09-12 00:32:15]   - Checkpoint directory: results/L=4/J2=0.03/J1=0.02/training/checkpoints
[2025-09-12 00:32:15] --------------------------------------------------
[2025-09-12 00:32:15] Device status:
[2025-09-12 00:32:15]   - Devices model: NVIDIA H200 NVL
[2025-09-12 00:32:15]   - Number of devices: 1
[2025-09-12 00:32:15]   - Sharding: True
[2025-09-12 00:32:15] ============================================================
[2025-09-12 00:34:11] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -52.092778+0.004693j
[2025-09-12 00:35:19] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -52.072715+0.004056j
[2025-09-12 00:35:34] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -51.978539+0.001760j
[2025-09-12 00:35:49] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -51.876277+0.003006j
[2025-09-12 00:36:04] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -51.848531+0.003087j
[2025-09-12 00:36:20] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -51.959515-0.001481j
[2025-09-12 00:36:35] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -51.935755-0.000894j
[2025-09-12 00:36:50] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -52.010592-0.000322j
[2025-09-12 00:37:05] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -51.991540+0.002330j
[2025-09-12 00:37:21] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -51.956843-0.004225j
[2025-09-12 00:37:36] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -52.144988+0.000560j
[2025-09-12 00:37:51] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -52.069227+0.000860j
[2025-09-12 00:38:06] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -52.099882+0.004200j
[2025-09-12 00:38:22] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -52.191692-0.001915j
[2025-09-12 00:38:37] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -52.141559+0.003186j
[2025-09-12 00:38:52] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -52.118533+0.001592j
[2025-09-12 00:39:07] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -52.147017-0.000809j
[2025-09-12 00:39:23] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -51.948715-0.001524j
[2025-09-12 00:39:38] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -52.033764+0.000162j
[2025-09-12 00:39:53] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -52.159074-0.002348j
[2025-09-12 00:40:08] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -52.111974+0.001106j
[2025-09-12 00:40:24] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -52.103054-0.001027j
[2025-09-12 00:40:39] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -52.044175+0.001915j
[2025-09-12 00:40:54] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -52.099958+0.002022j
[2025-09-12 00:41:10] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -52.137190-0.002847j
[2025-09-12 00:41:25] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -52.155698-0.000521j
[2025-09-12 00:41:40] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -52.145098+0.002595j
[2025-09-12 00:41:55] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -52.164399-0.000886j
[2025-09-12 00:42:11] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -52.125406+0.002337j
[2025-09-12 00:42:26] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -52.129264+0.000020j
[2025-09-12 00:42:41] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -52.209037+0.003304j
[2025-09-12 00:42:56] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -52.112616-0.000757j
[2025-09-12 00:43:12] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -52.137347+0.000144j
[2025-09-12 00:43:27] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -52.100619+0.000013j
[2025-09-12 00:43:42] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -52.061535+0.001724j
[2025-09-12 00:43:58] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -52.122495-0.005137j
[2025-09-12 00:44:13] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -52.069039+0.001786j
[2025-09-12 00:44:28] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -52.035306+0.000004j
[2025-09-12 00:44:43] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -52.147675+0.000026j
[2025-09-12 00:44:59] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -52.067707-0.001287j
[2025-09-12 00:45:14] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -52.100804+0.003484j
[2025-09-12 00:45:29] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -52.071397+0.000673j
[2025-09-12 00:45:45] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -51.922864+0.002008j
[2025-09-12 00:46:00] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -51.952335-0.003338j
[2025-09-12 00:46:15] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -52.146596+0.001037j
[2025-09-12 00:46:30] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -52.087519+0.000449j
[2025-09-12 00:46:46] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -52.225815-0.002099j
[2025-09-12 00:47:01] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -52.159133-0.003146j
[2025-09-12 00:47:16] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -52.100621-0.003759j
[2025-09-12 00:47:32] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -52.125351-0.002092j
[2025-09-12 00:47:47] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -52.145563+0.001798j
[2025-09-12 00:48:02] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -52.050834-0.000531j
[2025-09-12 00:48:18] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -52.029520-0.002617j
[2025-09-12 00:48:33] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -52.130056+0.002419j
[2025-09-12 00:48:48] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -52.066568-0.001234j
[2025-09-12 00:49:03] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -52.028370+0.001345j
[2025-09-12 00:49:19] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -52.074293-0.002829j
[2025-09-12 00:49:34] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -52.093059+0.000103j
[2025-09-12 00:49:49] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -52.066449-0.000657j
[2025-09-12 00:50:05] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -52.004225-0.000709j
[2025-09-12 00:50:20] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -51.991969+0.000216j
[2025-09-12 00:50:35] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -52.006574-0.003235j
[2025-09-12 00:50:50] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -52.024429-0.000207j
[2025-09-12 00:51:06] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -52.068645-0.000102j
[2025-09-12 00:51:21] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -52.066252+0.000051j
[2025-09-12 00:51:36] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -51.962024-0.002600j
[2025-09-12 00:51:52] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -52.182631+0.000837j
[2025-09-12 00:52:07] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -51.997239+0.001671j
[2025-09-12 00:52:22] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -52.086848-0.000413j
[2025-09-12 00:52:37] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -51.976361-0.003424j
[2025-09-12 00:52:53] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -52.083137+0.000457j
[2025-09-12 00:53:08] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -52.043721-0.002166j
[2025-09-12 00:53:23] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -52.023936+0.000746j
[2025-09-12 00:53:39] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -52.056378-0.000935j
[2025-09-12 00:53:54] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -52.148421+0.001166j
[2025-09-12 00:54:09] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -52.030475+0.001388j
[2025-09-12 00:54:24] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -52.006781-0.000915j
[2025-09-12 00:54:40] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -52.042874-0.000595j
[2025-09-12 00:54:55] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -51.975181+0.003412j
[2025-09-12 00:55:10] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -52.025091-0.001921j
[2025-09-12 00:55:26] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -51.899535+0.000745j
[2025-09-12 00:55:41] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -51.990295-0.001738j
[2025-09-12 00:55:56] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -52.020649-0.002436j
[2025-09-12 00:56:12] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -52.037320-0.001952j
[2025-09-12 00:56:27] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -52.032974-0.002408j
[2025-09-12 00:56:42] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -52.060907+0.002462j
[2025-09-12 00:56:57] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -52.113757+0.001589j
[2025-09-12 00:57:13] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -52.130988-0.000014j
[2025-09-12 00:57:28] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -52.240020+0.004454j
[2025-09-12 00:57:43] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -52.196129-0.001459j
[2025-09-12 00:57:58] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -52.241768-0.002538j
[2025-09-12 00:58:14] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -52.348428-0.000261j
[2025-09-12 00:58:29] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -52.153516-0.001470j
[2025-09-12 00:58:44] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -52.151207-0.002424j
[2025-09-12 00:59:00] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -52.260052+0.002572j
[2025-09-12 00:59:15] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -52.173558+0.001761j
[2025-09-12 00:59:30] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -52.145465+0.001238j
[2025-09-12 00:59:45] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -52.082567-0.005316j
[2025-09-12 01:00:01] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -52.036558+0.002347j
[2025-09-12 01:00:16] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -52.157953-0.002669j
[2025-09-12 01:00:31] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -52.049990+0.003096j
[2025-09-12 01:00:47] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -52.094223-0.000524j
[2025-09-12 01:01:02] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -52.083182-0.001619j
[2025-09-12 01:01:17] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -52.090822+0.001897j
[2025-09-12 01:01:32] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -52.025948+0.001372j
[2025-09-12 01:01:32] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-12 01:01:48] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -52.030183-0.001373j
[2025-09-12 01:02:03] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -51.948431+0.002078j
[2025-09-12 01:02:18] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -52.036859-0.002211j
[2025-09-12 01:02:33] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -52.060529+0.000695j
[2025-09-12 01:02:47] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -52.124227-0.002305j
[2025-09-12 01:02:57] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -52.095885-0.000802j
[2025-09-12 01:03:07] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -52.167957-0.000930j
[2025-09-12 01:03:18] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -52.136701+0.001501j
[2025-09-12 01:03:28] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -52.130858-0.002174j
[2025-09-12 01:03:38] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -52.032496-0.001386j
[2025-09-12 01:03:48] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -52.156762+0.002820j
[2025-09-12 01:04:02] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -52.047703-0.002520j
[2025-09-12 01:04:17] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -52.212708-0.003050j
[2025-09-12 01:04:32] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -52.155869+0.001959j
[2025-09-12 01:04:47] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -52.114720+0.001968j
[2025-09-12 01:05:03] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -52.195631+0.001057j
[2025-09-12 01:05:18] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -52.256793+0.001150j
[2025-09-12 01:05:33] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -52.180701+0.001310j
[2025-09-12 01:05:48] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -52.255523-0.000602j
[2025-09-12 01:06:04] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -52.171411+0.000257j
[2025-09-12 01:06:19] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -52.065740+0.000433j
[2025-09-12 01:06:34] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -51.949285+0.000716j
[2025-09-12 01:06:50] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -52.039544+0.001488j
[2025-09-12 01:07:05] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -52.091426-0.001661j
[2025-09-12 01:07:20] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -52.313068-0.002869j
[2025-09-12 01:07:35] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -52.198248-0.000688j
[2025-09-12 01:07:51] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -52.082050-0.001094j
[2025-09-12 01:08:06] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -52.172550+0.001001j
[2025-09-12 01:08:21] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -52.156065-0.000068j
[2025-09-12 01:08:37] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -52.120733-0.002614j
[2025-09-12 01:08:52] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -52.138576-0.002799j
[2025-09-12 01:09:07] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -52.094176+0.001298j
[2025-09-12 01:09:22] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -51.994954-0.000125j
[2025-09-12 01:09:38] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -52.018519-0.002718j
[2025-09-12 01:09:53] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -52.117626-0.000101j
[2025-09-12 01:10:08] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -52.052246+0.000484j
[2025-09-12 01:10:24] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -52.160377+0.001420j
[2025-09-12 01:10:39] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -52.170110-0.000374j
[2025-09-12 01:10:54] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -52.180048+0.000608j
[2025-09-12 01:11:09] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -52.255609-0.001391j
[2025-09-12 01:11:25] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -52.220876-0.000931j
[2025-09-12 01:11:40] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -52.111976-0.002389j
[2025-09-12 01:11:55] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -52.038695+0.000263j
[2025-09-12 01:12:11] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -52.065092-0.002516j
[2025-09-12 01:12:26] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -52.066869+0.002589j
[2025-09-12 01:12:26] RESTART #1 | Period: 300
[2025-09-12 01:12:41] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -52.126649-0.000278j
[2025-09-12 01:12:56] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -52.193138-0.001345j
[2025-09-12 01:13:12] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -52.172525-0.002425j
[2025-09-12 01:13:25] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -52.127947-0.002428j
[2025-09-12 01:13:36] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -52.040875-0.001018j
[2025-09-12 01:13:49] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -52.034418+0.000172j
[2025-09-12 01:14:00] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -52.081841-0.001025j
[2025-09-12 01:14:15] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -52.044677+0.001083j
[2025-09-12 01:14:30] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -52.004263+0.002316j
[2025-09-12 01:14:46] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -52.052403-0.001830j
[2025-09-12 01:15:01] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -52.144024+0.002706j
[2025-09-12 01:15:16] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -52.034524-0.001935j
[2025-09-12 01:15:32] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -52.021961-0.002452j
[2025-09-12 01:15:47] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -52.086977-0.001738j
[2025-09-12 01:16:02] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -52.011306-0.000766j
[2025-09-12 01:16:17] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -52.054660+0.000134j
[2025-09-12 01:16:32] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -52.118037+0.002774j
[2025-09-12 01:16:48] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -52.067808+0.004836j
[2025-09-12 01:17:03] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -52.003902-0.002025j
[2025-09-12 01:17:18] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -52.070548+0.003132j
[2025-09-12 01:17:33] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -52.203533+0.000922j
[2025-09-12 01:17:49] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -52.151698+0.002761j
[2025-09-12 01:18:04] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -52.075348-0.000871j
[2025-09-12 01:18:19] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -52.075116+0.001447j
[2025-09-12 01:18:34] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -52.070932-0.001294j
[2025-09-12 01:18:50] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -52.084274+0.001531j
[2025-09-12 01:19:05] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -52.020459+0.000414j
[2025-09-12 01:19:20] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -52.138379-0.001889j
[2025-09-12 01:19:35] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -52.151873-0.001535j
[2025-09-12 01:19:51] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -52.106667-0.000124j
[2025-09-12 01:20:06] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -52.008843-0.000131j
[2025-09-12 01:20:21] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -52.140316+0.002167j
[2025-09-12 01:20:36] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -52.095054-0.000705j
[2025-09-12 01:20:51] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -52.171381-0.001343j
[2025-09-12 01:21:07] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -52.242309-0.000914j
[2025-09-12 01:21:22] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -52.186530+0.002495j
[2025-09-12 01:21:37] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -52.258407-0.000651j
[2025-09-12 01:21:52] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -52.113902-0.001330j
[2025-09-12 01:22:08] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -52.109790+0.000692j
[2025-09-12 01:22:23] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -52.200866+0.001131j
[2025-09-12 01:22:38] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -52.085888-0.002480j
[2025-09-12 01:22:53] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -51.978992-0.001530j
[2025-09-12 01:23:09] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -52.142871+0.000691j
[2025-09-12 01:23:24] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -52.181677+0.000253j
[2025-09-12 01:23:39] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -52.113705-0.000733j
[2025-09-12 01:23:55] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -52.076036+0.002408j
[2025-09-12 01:24:10] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -52.194291+0.000116j
[2025-09-12 01:24:25] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -52.177581+0.001613j
[2025-09-12 01:24:40] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -52.092990-0.001676j
[2025-09-12 01:24:56] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -52.108324+0.000119j
[2025-09-12 01:25:11] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -52.083085-0.001794j
[2025-09-12 01:25:26] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -51.992831-0.001766j
[2025-09-12 01:25:41] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -52.082861-0.002211j
[2025-09-12 01:25:57] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -52.018133+0.001368j
[2025-09-12 01:26:12] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -52.052025-0.003309j
[2025-09-12 01:26:27] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -51.990307+0.003289j
[2025-09-12 01:26:42] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -52.024163-0.001144j
[2025-09-12 01:26:58] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -52.051874-0.001625j
[2025-09-12 01:27:13] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -51.948019-0.002795j
[2025-09-12 01:27:28] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -51.996491-0.001528j
[2025-09-12 01:27:28] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-12 01:27:43] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -52.039054-0.001778j
[2025-09-12 01:27:59] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -52.039888-0.002461j
[2025-09-12 01:28:14] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -52.098344+0.003355j
[2025-09-12 01:28:29] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -52.186896-0.001830j
[2025-09-12 01:28:45] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -52.162826-0.001675j
[2025-09-12 01:29:00] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -52.305323+0.003975j
[2025-09-12 01:29:15] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -52.137439-0.005057j
[2025-09-12 01:29:30] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -52.193393+0.002138j
[2025-09-12 01:29:46] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -52.285054-0.000124j
[2025-09-12 01:30:01] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -52.176428+0.002657j
[2025-09-12 01:30:16] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -52.243639-0.000386j
[2025-09-12 01:30:31] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -52.130396-0.003109j
[2025-09-12 01:30:47] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -52.175886+0.002075j
[2025-09-12 01:31:02] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -52.099514-0.003429j
[2025-09-12 01:31:17] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -52.077851+0.000062j
[2025-09-12 01:31:32] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -52.250729+0.000154j
[2025-09-12 01:31:48] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -52.072008-0.005298j
[2025-09-12 01:32:03] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -52.230579+0.003480j
[2025-09-12 01:32:18] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -52.167733-0.000801j
[2025-09-12 01:32:33] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -52.205767-0.001491j
[2025-09-12 01:32:49] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -52.082608-0.000343j
[2025-09-12 01:33:04] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -52.146242+0.000144j
[2025-09-12 01:33:19] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -52.098525-0.001979j
[2025-09-12 01:33:34] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -52.029989+0.002689j
[2025-09-12 01:33:50] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -52.208940+0.000240j
[2025-09-12 01:34:05] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -52.069327-0.008673j
[2025-09-12 01:34:20] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -52.088472+0.004854j
[2025-09-12 01:34:35] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -52.079678-0.001676j
[2025-09-12 01:34:51] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -52.049911+0.000284j
[2025-09-12 01:35:06] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -52.033113+0.001578j
[2025-09-12 01:35:21] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -52.098047+0.000965j
[2025-09-12 01:35:37] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -52.128263-0.000153j
[2025-09-12 01:35:52] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -52.060695+0.001688j
[2025-09-12 01:36:07] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -52.147362+0.001432j
[2025-09-12 01:36:22] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -52.066959+0.002999j
[2025-09-12 01:36:38] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -52.142034+0.001517j
[2025-09-12 01:36:53] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -52.097517-0.000107j
[2025-09-12 01:37:08] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -52.135887-0.000713j
[2025-09-12 01:37:23] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -52.099275+0.001674j
[2025-09-12 01:37:39] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -52.102106-0.000186j
[2025-09-12 01:37:54] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -52.187879+0.000450j
[2025-09-12 01:38:09] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -52.056229+0.000398j
[2025-09-12 01:38:25] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -52.019095-0.001546j
[2025-09-12 01:38:40] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -52.000519+0.000125j
[2025-09-12 01:38:55] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -52.061811-0.003542j
[2025-09-12 01:39:10] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -51.977283-0.000426j
[2025-09-12 01:39:25] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -52.168858-0.001186j
[2025-09-12 01:39:41] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -52.161323-0.000877j
[2025-09-12 01:39:56] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -52.137608+0.000675j
[2025-09-12 01:40:11] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -52.113726-0.000546j
[2025-09-12 01:40:27] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -52.092308-0.000489j
[2025-09-12 01:40:42] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -52.093100-0.002407j
[2025-09-12 01:40:57] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -52.186394-0.003292j
[2025-09-12 01:41:12] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -52.200275+0.000111j
[2025-09-12 01:41:28] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -52.202108+0.001428j
[2025-09-12 01:41:43] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -52.183825-0.000044j
[2025-09-12 01:41:58] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -52.081077-0.002204j
[2025-09-12 01:42:13] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -52.012788+0.000378j
[2025-09-12 01:42:29] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -52.063247-0.000721j
[2025-09-12 01:42:44] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -51.983801+0.002298j
[2025-09-12 01:42:59] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -51.937703-0.004145j
[2025-09-12 01:43:14] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -51.910999+0.000793j
[2025-09-12 01:43:30] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -52.085341-0.001750j
[2025-09-12 01:43:45] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -52.162331+0.000343j
[2025-09-12 01:44:00] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -52.061944+0.000233j
[2025-09-12 01:44:16] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -52.205653+0.000549j
[2025-09-12 01:44:31] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -52.122924-0.003299j
[2025-09-12 01:44:46] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -52.120866+0.002872j
[2025-09-12 01:45:01] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -52.047706+0.000764j
[2025-09-12 01:45:17] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -52.126539-0.001671j
[2025-09-12 01:45:32] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -52.022503+0.001871j
[2025-09-12 01:45:47] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -52.019158+0.001802j
[2025-09-12 01:46:02] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -52.085124+0.000688j
[2025-09-12 01:46:18] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -52.111120-0.000209j
[2025-09-12 01:46:33] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -52.160314-0.001038j
[2025-09-12 01:46:48] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -52.036362-0.001489j
[2025-09-12 01:47:03] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -52.004594+0.000075j
[2025-09-12 01:47:17] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -51.993434+0.001512j
[2025-09-12 01:47:28] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -52.029076+0.000352j
[2025-09-12 01:47:38] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -52.135412+0.001312j
[2025-09-12 01:47:48] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -51.921985+0.001078j
[2025-09-12 01:47:58] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -52.111939-0.001492j
[2025-09-12 01:48:08] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -52.071498+0.002254j
[2025-09-12 01:48:18] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -52.008363-0.001955j
[2025-09-12 01:48:33] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -52.124556-0.000432j
[2025-09-12 01:48:48] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -51.950672+0.001967j
[2025-09-12 01:49:03] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -51.979464+0.002777j
[2025-09-12 01:49:19] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -51.955208+0.001791j
[2025-09-12 01:49:34] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -51.929042-0.002813j
[2025-09-12 01:49:49] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -51.985467+0.001103j
[2025-09-12 01:50:04] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -51.990939-0.002238j
[2025-09-12 01:50:20] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -52.087164+0.004550j
[2025-09-12 01:50:35] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -51.920553+0.000296j
[2025-09-12 01:50:50] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -52.076039+0.000433j
[2025-09-12 01:51:06] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -52.132539-0.000351j
[2025-09-12 01:51:21] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -52.092771-0.002824j
[2025-09-12 01:51:36] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -52.051579+0.001515j
[2025-09-12 01:51:51] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -52.049441-0.000390j
[2025-09-12 01:52:07] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -51.994547+0.003615j
[2025-09-12 01:52:22] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -52.030004-0.001098j
[2025-09-12 01:52:37] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -51.987891+0.001602j
[2025-09-12 01:52:52] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -52.080847-0.000440j
[2025-09-12 01:53:08] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -52.015887-0.002497j
[2025-09-12 01:53:23] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -52.109730+0.003585j
[2025-09-12 01:53:38] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -52.172776+0.000662j
[2025-09-12 01:53:38] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-12 01:53:53] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -52.084259-0.000994j
[2025-09-12 01:54:09] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -52.043874+0.002609j
[2025-09-12 01:54:24] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -52.082766+0.001965j
[2025-09-12 01:54:39] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -52.045961-0.000608j
[2025-09-12 01:54:55] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -52.087733+0.002268j
[2025-09-12 01:55:10] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -52.251005+0.000326j
[2025-09-12 01:55:25] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -52.100977+0.000459j
[2025-09-12 01:55:40] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -52.212808-0.000820j
[2025-09-12 01:55:56] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -52.124564-0.001056j
[2025-09-12 01:56:11] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -52.162318-0.003797j
[2025-09-12 01:56:26] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -52.185732-0.003964j
[2025-09-12 01:56:41] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -52.118541+0.000832j
[2025-09-12 01:56:57] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -52.064374+0.000453j
[2025-09-12 01:57:12] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -52.154527-0.000748j
[2025-09-12 01:57:27] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -52.133197+0.000937j
[2025-09-12 01:57:43] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -52.141033+0.001602j
[2025-09-12 01:57:55] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -52.071552-0.004403j
[2025-09-12 01:58:06] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -52.156699+0.000925j
[2025-09-12 01:58:19] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -52.056515+0.001135j
[2025-09-12 01:58:30] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -52.105958-0.001404j
[2025-09-12 01:58:45] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -52.028853-0.000320j
[2025-09-12 01:59:01] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -52.023700-0.002037j
[2025-09-12 01:59:16] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -52.138186+0.002415j
[2025-09-12 01:59:31] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -51.976618-0.000771j
[2025-09-12 01:59:47] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -51.937082-0.004262j
[2025-09-12 02:00:02] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -51.934190-0.000018j
[2025-09-12 02:00:17] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -52.030271+0.004191j
[2025-09-12 02:00:32] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -52.107425+0.002858j
[2025-09-12 02:00:48] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -52.087314-0.002201j
[2025-09-12 02:01:03] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -52.234692-0.000760j
[2025-09-12 02:01:18] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -52.138466+0.001577j
[2025-09-12 02:01:33] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -52.000283-0.001291j
[2025-09-12 02:01:49] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -52.011234-0.000270j
[2025-09-12 02:02:04] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -52.083503-0.000102j
[2025-09-12 02:02:19] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -52.217877+0.001139j
[2025-09-12 02:02:34] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -52.176435+0.000164j
[2025-09-12 02:02:50] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -52.078267-0.002279j
[2025-09-12 02:03:05] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -52.187204+0.003111j
[2025-09-12 02:03:20] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -52.179033+0.003196j
[2025-09-12 02:03:35] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -52.108210+0.003891j
[2025-09-12 02:03:51] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -52.077509-0.001674j
[2025-09-12 02:04:06] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -52.097528+0.001255j
[2025-09-12 02:04:21] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -52.217116-0.000471j
[2025-09-12 02:04:36] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -52.162732+0.000777j
[2025-09-12 02:04:51] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -52.063710-0.002647j
[2025-09-12 02:05:07] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -52.044291-0.004096j
[2025-09-12 02:05:22] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -51.983486-0.000754j
[2025-09-12 02:05:37] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -51.959520+0.000087j
[2025-09-12 02:05:52] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -52.085139+0.001250j
[2025-09-12 02:06:08] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -52.031943-0.001967j
[2025-09-12 02:06:23] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -52.067970-0.001889j
[2025-09-12 02:06:38] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -52.003068+0.000720j
[2025-09-12 02:06:53] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -52.057671-0.002161j
[2025-09-12 02:07:08] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -52.050415+0.002414j
[2025-09-12 02:07:24] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -51.996150+0.001634j
[2025-09-12 02:07:39] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -52.059557+0.003341j
[2025-09-12 02:07:54] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -52.089062+0.000384j
[2025-09-12 02:08:09] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -52.148321+0.000557j
[2025-09-12 02:08:24] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -52.157067-0.001869j
[2025-09-12 02:08:40] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -52.116410-0.002004j
[2025-09-12 02:08:55] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -52.209344-0.001028j
[2025-09-12 02:09:10] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -52.040923-0.003056j
[2025-09-12 02:09:25] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -52.160445-0.000865j
[2025-09-12 02:09:40] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -52.206053-0.001541j
[2025-09-12 02:09:56] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -52.201964+0.001101j
[2025-09-12 02:10:11] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -52.209631-0.000359j
[2025-09-12 02:10:26] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -52.224942+0.001743j
[2025-09-12 02:10:41] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -52.165134+0.001965j
[2025-09-12 02:10:57] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -52.143658+0.000027j
[2025-09-12 02:11:12] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -52.107270-0.000261j
[2025-09-12 02:11:27] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -52.208458-0.003455j
[2025-09-12 02:11:42] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -52.032767+0.000217j
[2025-09-12 02:11:57] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -52.057098-0.000215j
[2025-09-12 02:12:13] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -52.077742-0.002729j
[2025-09-12 02:12:28] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -52.212029-0.003582j
[2025-09-12 02:12:43] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -52.198441-0.000282j
[2025-09-12 02:12:58] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -52.192420-0.003405j
[2025-09-12 02:13:13] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -52.230435+0.004358j
[2025-09-12 02:13:29] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -52.097506+0.002065j
[2025-09-12 02:13:44] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -52.199963-0.003824j
[2025-09-12 02:13:59] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -52.156232-0.001868j
[2025-09-12 02:14:14] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -52.101737+0.003028j
[2025-09-12 02:14:30] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -52.112892+0.004713j
[2025-09-12 02:14:45] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -52.016061-0.000200j
[2025-09-12 02:15:00] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -52.125554-0.000605j
[2025-09-12 02:15:15] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -52.087600+0.001503j
[2025-09-12 02:15:30] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -52.050814-0.000265j
[2025-09-12 02:15:46] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -52.000435+0.000960j
[2025-09-12 02:16:01] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -52.117937+0.000187j
[2025-09-12 02:16:16] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -52.082511+0.003827j
[2025-09-12 02:16:31] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -52.133473-0.000312j
[2025-09-12 02:16:47] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -52.094500-0.000619j
[2025-09-12 02:17:02] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -52.105813-0.004026j
[2025-09-12 02:17:17] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -52.043134-0.002222j
[2025-09-12 02:17:32] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -52.003059-0.000642j
[2025-09-12 02:17:48] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -52.182829-0.004808j
[2025-09-12 02:18:03] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -52.119229-0.000400j
[2025-09-12 02:18:18] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -52.218833+0.000499j
[2025-09-12 02:18:33] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -52.105280+0.001438j
[2025-09-12 02:18:49] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -52.136551-0.000336j
[2025-09-12 02:19:04] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -52.059286+0.003049j
[2025-09-12 02:19:19] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -52.144490+0.003212j
[2025-09-12 02:19:34] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -52.230272-0.001213j
[2025-09-12 02:19:50] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -52.179234-0.000113j
[2025-09-12 02:20:05] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -52.223088+0.000242j
[2025-09-12 02:20:05] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-12 02:20:20] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -52.251420+0.000328j
[2025-09-12 02:20:35] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -52.191692-0.000121j
[2025-09-12 02:20:51] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -52.141301+0.002569j
[2025-09-12 02:21:06] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -52.057389+0.001342j
[2025-09-12 02:21:21] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -52.180942+0.000673j
[2025-09-12 02:21:36] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -52.244583+0.001700j
[2025-09-12 02:21:52] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -52.152583-0.000266j
[2025-09-12 02:22:07] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -52.184938-0.001997j
[2025-09-12 02:22:22] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -52.091274-0.000295j
[2025-09-12 02:22:38] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -52.119974+0.000448j
[2025-09-12 02:22:53] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -52.201800-0.002096j
[2025-09-12 02:23:08] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -52.186781+0.003417j
[2025-09-12 02:23:24] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -52.178565+0.000111j
[2025-09-12 02:23:39] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -52.185070+0.002611j
[2025-09-12 02:23:54] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -52.290246-0.003041j
[2025-09-12 02:24:09] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -52.321919-0.000965j
[2025-09-12 02:24:25] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -52.345766+0.001109j
[2025-09-12 02:24:40] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -52.140091+0.000478j
[2025-09-12 02:24:55] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -52.137656+0.001256j
[2025-09-12 02:25:10] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -52.064400+0.000394j
[2025-09-12 02:25:26] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -52.024373-0.000460j
[2025-09-12 02:25:41] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -52.048852+0.001637j
[2025-09-12 02:25:56] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -52.074830+0.001627j
[2025-09-12 02:26:12] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -52.045825+0.002027j
[2025-09-12 02:26:27] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -51.949788-0.002194j
[2025-09-12 02:26:42] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -52.078933-0.000634j
[2025-09-12 02:26:57] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -52.102357-0.000960j
[2025-09-12 02:27:13] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -52.324733-0.002013j
[2025-09-12 02:27:28] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -52.231278+0.000710j
[2025-09-12 02:27:43] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -52.227016-0.000725j
[2025-09-12 02:27:43] RESTART #2 | Period: 600
[2025-09-12 02:27:59] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -52.153902-0.001346j
[2025-09-12 02:28:14] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -52.089716+0.001158j
[2025-09-12 02:28:29] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -52.244063+0.003224j
[2025-09-12 02:28:44] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -52.157383+0.000750j
[2025-09-12 02:29:00] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -52.149426-0.001020j
[2025-09-12 02:29:15] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -52.097041-0.001524j
[2025-09-12 02:29:30] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -52.035740-0.000162j
[2025-09-12 02:29:45] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -52.037214-0.000656j
[2025-09-12 02:30:01] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -52.054242-0.000060j
[2025-09-12 02:30:16] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -52.153194-0.002370j
[2025-09-12 02:30:31] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -52.167545-0.002251j
[2025-09-12 02:30:47] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -52.143561+0.000552j
[2025-09-12 02:31:02] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -52.118154-0.001002j
[2025-09-12 02:31:17] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -52.040895+0.001312j
[2025-09-12 02:31:32] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -52.053693-0.001310j
[2025-09-12 02:31:48] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -51.991335-0.000580j
[2025-09-12 02:32:01] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -52.030915+0.001351j
[2025-09-12 02:32:11] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -52.001236-0.000273j
[2025-09-12 02:32:21] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -52.182724+0.003459j
[2025-09-12 02:32:31] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -52.093260-0.000466j
[2025-09-12 02:32:42] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -52.071471+0.000639j
[2025-09-12 02:32:52] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -52.124300-0.004807j
[2025-09-12 02:33:02] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -52.132282-0.001913j
[2025-09-12 02:33:14] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -52.230230+0.000245j
[2025-09-12 02:33:30] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -52.200171+0.002366j
[2025-09-12 02:33:45] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -52.114270+0.001793j
[2025-09-12 02:34:00] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -52.054154-0.002931j
[2025-09-12 02:34:16] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -52.147788+0.003675j
[2025-09-12 02:34:31] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -52.173339-0.002143j
[2025-09-12 02:34:46] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -52.099205+0.000739j
[2025-09-12 02:35:01] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -52.115685-0.000103j
[2025-09-12 02:35:17] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -52.066698+0.001488j
[2025-09-12 02:35:32] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -52.133411-0.001655j
[2025-09-12 02:35:47] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -51.984596+0.001249j
[2025-09-12 02:36:03] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -52.150594+0.000217j
[2025-09-12 02:36:18] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -52.128466-0.001641j
[2025-09-12 02:36:33] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -52.186172-0.001204j
[2025-09-12 02:36:48] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -52.104782+0.001893j
[2025-09-12 02:37:04] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -52.019405+0.000516j
[2025-09-12 02:37:19] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -51.949970+0.001623j
[2025-09-12 02:37:34] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -51.994919+0.002786j
[2025-09-12 02:37:50] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -52.065995-0.000819j
[2025-09-12 02:38:05] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -52.160876-0.000550j
[2025-09-12 02:38:20] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -52.195104-0.001720j
[2025-09-12 02:38:35] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -52.164160+0.001293j
[2025-09-12 02:38:51] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -52.069450+0.003510j
[2025-09-12 02:39:06] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -52.076597+0.002258j
[2025-09-12 02:39:21] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -52.050514-0.002301j
[2025-09-12 02:39:36] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -52.083929+0.002572j
[2025-09-12 02:39:52] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -52.183788+0.001619j
[2025-09-12 02:40:07] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -52.003310+0.000964j
[2025-09-12 02:40:22] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -52.099082-0.000958j
[2025-09-12 02:40:38] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -52.040817+0.001370j
[2025-09-12 02:40:53] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -52.152974-0.000846j
[2025-09-12 02:41:08] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -52.090853+0.001764j
[2025-09-12 02:41:23] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -51.987615-0.000375j
[2025-09-12 02:41:39] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -51.963810-0.000999j
[2025-09-12 02:41:54] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -52.021633-0.001833j
[2025-09-12 02:42:09] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -52.087738-0.000506j
[2025-09-12 02:42:24] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -52.017791-0.002428j
[2025-09-12 02:42:40] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -51.935894-0.001197j
[2025-09-12 02:42:50] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -51.973737+0.001710j
[2025-09-12 02:43:03] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -52.054970+0.002796j
[2025-09-12 02:43:15] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -52.068256+0.000628j
[2025-09-12 02:43:28] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -52.092913+0.000038j
[2025-09-12 02:43:43] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -52.000633-0.000758j
[2025-09-12 02:43:58] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -52.057577-0.002155j
[2025-09-12 02:44:13] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -52.147910+0.000514j
[2025-09-12 02:44:29] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -52.088415+0.001226j
[2025-09-12 02:44:44] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -52.067440+0.002168j
[2025-09-12 02:44:59] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -52.206426-0.000322j
[2025-09-12 02:45:14] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -52.104070+0.001892j
[2025-09-12 02:45:30] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -52.059208+0.002929j
[2025-09-12 02:45:45] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -52.043902-0.001048j
[2025-09-12 02:46:00] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -52.094469+0.002092j
[2025-09-12 02:46:00] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-12 02:46:15] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -52.123410+0.000932j
[2025-09-12 02:46:30] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -52.020892-0.001792j
[2025-09-12 02:46:46] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -52.029625+0.001016j
[2025-09-12 02:47:01] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -52.121902-0.000038j
[2025-09-12 02:47:16] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -52.018812-0.000922j
[2025-09-12 02:47:31] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -52.090613-0.000439j
[2025-09-12 02:47:47] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -52.051668+0.000034j
[2025-09-12 02:48:02] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -52.113480+0.001346j
[2025-09-12 02:48:17] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -52.131684+0.002734j
[2025-09-12 02:48:32] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -52.174127+0.002968j
[2025-09-12 02:48:47] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -51.953280-0.002513j
[2025-09-12 02:49:03] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -52.111377-0.002052j
[2025-09-12 02:49:18] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -52.166739-0.001979j
[2025-09-12 02:49:33] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -52.055464-0.003595j
[2025-09-12 02:49:48] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -51.999194-0.000528j
[2025-09-12 02:50:04] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -52.054099-0.002468j
[2025-09-12 02:50:19] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -52.066257-0.001575j
[2025-09-12 02:50:34] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -51.977639-0.002446j
[2025-09-12 02:50:49] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -52.181199+0.001250j
[2025-09-12 02:51:05] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -51.959846-0.001796j
[2025-09-12 02:51:20] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -52.033179+0.000976j
[2025-09-12 02:51:35] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -52.161181+0.002077j
[2025-09-12 02:51:50] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -52.255947-0.001337j
[2025-09-12 02:52:05] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -52.147326-0.000737j
[2025-09-12 02:52:21] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -52.126707+0.001504j
[2025-09-12 02:52:36] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -52.121754-0.000606j
[2025-09-12 02:52:51] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -52.152942-0.002955j
[2025-09-12 02:53:06] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -52.074162+0.002745j
[2025-09-12 02:53:22] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -52.158754-0.000142j
[2025-09-12 02:53:37] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -52.068728+0.000579j
[2025-09-12 02:53:52] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -52.116053-0.000308j
[2025-09-12 02:54:07] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -52.106753+0.001096j
[2025-09-12 02:54:23] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -52.114214-0.001119j
[2025-09-12 02:54:38] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -52.075562+0.003156j
[2025-09-12 02:54:53] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -52.190274+0.000139j
[2025-09-12 02:55:08] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -52.195220-0.001351j
[2025-09-12 02:55:24] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -52.101652+0.000012j
[2025-09-12 02:55:39] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -52.197311-0.002715j
[2025-09-12 02:55:54] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -51.991540-0.000464j
[2025-09-12 02:56:09] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -52.079426-0.000855j
[2025-09-12 02:56:25] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -52.013416+0.000386j
[2025-09-12 02:56:40] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -51.981580+0.002197j
[2025-09-12 02:56:55] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -52.065860+0.000559j
[2025-09-12 02:57:10] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -52.093719-0.004150j
[2025-09-12 02:57:26] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -52.045776+0.001272j
[2025-09-12 02:57:41] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -52.058886+0.000840j
[2025-09-12 02:57:56] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -52.121850-0.001404j
[2025-09-12 02:58:11] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -52.159878+0.003833j
[2025-09-12 02:58:27] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -52.072400+0.001279j
[2025-09-12 02:58:42] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -52.038155+0.003177j
[2025-09-12 02:58:57] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -51.984267+0.001842j
[2025-09-12 02:59:12] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -52.084637+0.001530j
[2025-09-12 02:59:28] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -52.127075+0.002855j
[2025-09-12 02:59:43] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -52.047130-0.002376j
[2025-09-12 02:59:58] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -52.119145+0.001098j
[2025-09-12 03:00:13] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -52.146489-0.000764j
[2025-09-12 03:00:28] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -52.062506+0.002934j
[2025-09-12 03:00:44] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -52.052487+0.000949j
[2025-09-12 03:00:59] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -52.006376+0.001414j
[2025-09-12 03:01:14] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -52.022002+0.004239j
[2025-09-12 03:01:29] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -51.954218-0.000840j
[2025-09-12 03:01:45] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -52.073285-0.001932j
[2025-09-12 03:02:00] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -51.998442+0.001355j
[2025-09-12 03:02:15] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -51.986527-0.001161j
[2025-09-12 03:02:30] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -52.047330+0.002258j
[2025-09-12 03:02:46] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -52.101279+0.002202j
[2025-09-12 03:03:01] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -52.096872-0.001097j
[2025-09-12 03:03:16] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -52.063063-0.002274j
[2025-09-12 03:03:31] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -52.017343+0.000301j
[2025-09-12 03:03:47] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -52.174422+0.000100j
[2025-09-12 03:04:02] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -51.989602-0.002064j
[2025-09-12 03:04:17] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -52.141827-0.002019j
[2025-09-12 03:04:32] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -52.239860+0.000038j
[2025-09-12 03:04:47] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -52.109049+0.001576j
[2025-09-12 03:05:03] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -52.176925-0.000752j
[2025-09-12 03:05:18] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -52.117269-0.000340j
[2025-09-12 03:05:33] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -52.031099+0.001927j
[2025-09-12 03:05:48] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -52.080028-0.002205j
[2025-09-12 03:06:04] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -52.118261+0.002332j
[2025-09-12 03:06:19] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -52.131240-0.000598j
[2025-09-12 03:06:34] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -52.170885+0.000548j
[2025-09-12 03:06:49] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -52.249008+0.000697j
[2025-09-12 03:07:04] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -52.142009+0.001629j
[2025-09-12 03:07:20] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -52.074068+0.001847j
[2025-09-12 03:07:35] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -52.143531-0.002363j
[2025-09-12 03:07:50] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -52.127519+0.000066j
[2025-09-12 03:08:05] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -52.067136+0.001085j
[2025-09-12 03:08:20] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -52.050580-0.000293j
[2025-09-12 03:08:36] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -52.034957-0.000265j
[2025-09-12 03:08:51] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -52.133147-0.001787j
[2025-09-12 03:09:06] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -52.185473+0.000714j
[2025-09-12 03:09:21] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -52.106447+0.002445j
[2025-09-12 03:09:36] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -52.201784-0.001888j
[2025-09-12 03:09:52] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -52.153997-0.000769j
[2025-09-12 03:10:07] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -51.947203-0.001293j
[2025-09-12 03:10:22] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -51.981582+0.001788j
[2025-09-12 03:10:37] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -51.971058+0.002519j
[2025-09-12 03:10:53] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -51.912284+0.001261j
[2025-09-12 03:11:08] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -51.990962+0.002964j
[2025-09-12 03:11:23] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -52.046931-0.000463j
[2025-09-12 03:11:38] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -52.082278-0.002000j
[2025-09-12 03:11:53] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -52.085503-0.001545j
[2025-09-12 03:12:09] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -52.134278+0.000457j
[2025-09-12 03:12:24] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -52.042951-0.001499j
[2025-09-12 03:12:39] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -52.105785+0.000475j
[2025-09-12 03:12:39] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-12 03:12:54] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -52.178707+0.000404j
[2025-09-12 03:13:10] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -52.191402-0.001018j
[2025-09-12 03:13:25] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -52.127146-0.000128j
[2025-09-12 03:13:40] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -52.193877-0.000418j
[2025-09-12 03:13:55] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -52.276704-0.000772j
[2025-09-12 03:14:11] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -52.085351+0.001551j
[2025-09-12 03:14:26] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -52.134570-0.000672j
[2025-09-12 03:14:41] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -52.225956-0.002515j
[2025-09-12 03:14:56] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -52.220578-0.004031j
[2025-09-12 03:15:12] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -52.216239-0.000011j
[2025-09-12 03:15:27] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -52.232900-0.002633j
[2025-09-12 03:15:42] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -52.231223-0.001038j
[2025-09-12 03:15:57] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -52.159905-0.004598j
[2025-09-12 03:16:13] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -52.118469-0.003130j
[2025-09-12 03:16:28] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -52.076388+0.003168j
[2025-09-12 03:16:43] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -52.039487+0.001549j
[2025-09-12 03:16:58] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -52.054777-0.005156j
[2025-09-12 03:17:13] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -52.077627+0.000610j
[2025-09-12 03:17:25] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -51.985237-0.001345j
[2025-09-12 03:17:35] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -51.984132-0.003172j
[2025-09-12 03:17:45] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -52.099256-0.000999j
[2025-09-12 03:17:55] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -52.021252-0.000049j
[2025-09-12 03:18:06] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -52.019457-0.000696j
[2025-09-12 03:18:16] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -51.928513+0.001091j
[2025-09-12 03:18:27] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -52.121841-0.000834j
[2025-09-12 03:18:43] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -52.000518-0.000576j
[2025-09-12 03:18:58] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -52.113109+0.001120j
[2025-09-12 03:19:13] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -52.102637+0.000580j
[2025-09-12 03:19:28] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -52.009703-0.003557j
[2025-09-12 03:19:44] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -51.957626+0.002364j
[2025-09-12 03:19:59] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -51.967488+0.001248j
[2025-09-12 03:20:14] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -51.988909-0.001081j
[2025-09-12 03:20:29] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -52.106782+0.004333j
[2025-09-12 03:20:45] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -52.084086-0.000758j
[2025-09-12 03:21:00] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -52.002019-0.000205j
[2025-09-12 03:21:15] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -51.908222-0.000351j
[2025-09-12 03:21:30] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -52.095614-0.002799j
[2025-09-12 03:21:46] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -52.103637+0.001606j
[2025-09-12 03:22:01] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -52.052149-0.003190j
[2025-09-12 03:22:16] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -51.986947+0.000508j
[2025-09-12 03:22:31] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -52.063191+0.000370j
[2025-09-12 03:22:47] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -52.098890+0.002264j
[2025-09-12 03:23:02] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -52.135240+0.001853j
[2025-09-12 03:23:17] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -52.116074-0.001058j
[2025-09-12 03:23:32] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -52.069163-0.002527j
[2025-09-12 03:23:48] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -52.075529-0.001472j
[2025-09-12 03:24:03] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -51.925098-0.001293j
[2025-09-12 03:24:18] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -52.051651+0.000126j
[2025-09-12 03:24:33] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -52.202549-0.000490j
[2025-09-12 03:24:49] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -52.210525+0.000327j
[2025-09-12 03:25:04] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -52.221042+0.001361j
[2025-09-12 03:25:19] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -52.139951+0.000388j
[2025-09-12 03:25:35] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -52.061494-0.001368j
[2025-09-12 03:25:50] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -52.185106+0.000031j
[2025-09-12 03:26:05] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -52.220836+0.001085j
[2025-09-12 03:26:20] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -52.226904+0.001101j
[2025-09-12 03:26:36] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -52.142715+0.003516j
[2025-09-12 03:26:51] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -52.194382+0.000343j
[2025-09-12 03:27:06] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -52.109280+0.002141j
[2025-09-12 03:27:21] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -52.201808+0.000098j
[2025-09-12 03:27:37] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -52.124609+0.001912j
[2025-09-12 03:27:52] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -52.008871+0.000028j
[2025-09-12 03:28:03] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -51.965457+0.002921j
[2025-09-12 03:28:16] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -52.042113-0.002318j
[2025-09-12 03:28:28] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -52.156721+0.000038j
[2025-09-12 03:28:41] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -52.121189+0.002351j
[2025-09-12 03:28:56] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -52.046546+0.002144j
[2025-09-12 03:29:11] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -52.115224+0.002674j
[2025-09-12 03:29:26] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -52.107352-0.001527j
[2025-09-12 03:29:42] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -52.144647+0.000557j
[2025-09-12 03:29:57] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -52.144080+0.000207j
[2025-09-12 03:30:12] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -52.244619-0.003469j
[2025-09-12 03:30:27] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -52.107335-0.002218j
[2025-09-12 03:30:43] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -52.140829-0.001740j
[2025-09-12 03:30:58] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -52.230422-0.001255j
[2025-09-12 03:31:13] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -52.105115+0.001756j
[2025-09-12 03:31:28] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -51.987696-0.000252j
[2025-09-12 03:31:44] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -51.936473+0.001329j
[2025-09-12 03:31:59] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -52.002206+0.000413j
[2025-09-12 03:32:14] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -52.078558+0.002955j
[2025-09-12 03:32:29] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -51.959854-0.000084j
[2025-09-12 03:32:45] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -52.150590-0.003763j
[2025-09-12 03:33:00] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -52.140454-0.003128j
[2025-09-12 03:33:15] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -52.056262-0.000540j
[2025-09-12 03:33:30] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -52.046861-0.008298j
[2025-09-12 03:33:46] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -52.092977-0.002543j
[2025-09-12 03:34:01] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -52.087016-0.000709j
[2025-09-12 03:34:16] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -51.991351+0.000866j
[2025-09-12 03:34:31] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -52.051098+0.000315j
[2025-09-12 03:34:47] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -52.123957-0.000019j
[2025-09-12 03:35:02] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -52.115889-0.000551j
[2025-09-12 03:35:17] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -52.110924+0.000806j
[2025-09-12 03:35:33] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -52.202174-0.003094j
[2025-09-12 03:35:48] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -52.252556+0.001725j
[2025-09-12 03:36:03] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -52.251746+0.000299j
[2025-09-12 03:36:18] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -52.191463-0.000724j
[2025-09-12 03:36:34] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -52.127463-0.000021j
[2025-09-12 03:36:49] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -51.987531+0.002063j
[2025-09-12 03:37:04] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -52.030077-0.001059j
[2025-09-12 03:37:19] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -52.051042+0.001599j
[2025-09-12 03:37:35] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -52.136658+0.001180j
[2025-09-12 03:37:50] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -52.056889-0.000614j
[2025-09-12 03:38:05] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -52.010120-0.001366j
[2025-09-12 03:38:20] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -52.018173-0.001000j
[2025-09-12 03:38:35] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -52.219825+0.002618j
[2025-09-12 03:38:35] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-12 03:38:51] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -52.179125+0.000202j
[2025-09-12 03:39:06] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -52.064436+0.002622j
[2025-09-12 03:39:21] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -51.986710+0.000793j
[2025-09-12 03:39:37] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -51.920801-0.002105j
[2025-09-12 03:39:52] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -52.002619-0.001097j
[2025-09-12 03:40:07] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -51.990292+0.002422j
[2025-09-12 03:40:22] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -52.098752+0.000211j
[2025-09-12 03:40:38] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -52.021521+0.000360j
[2025-09-12 03:40:53] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -52.125193-0.000519j
[2025-09-12 03:41:08] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -52.056160+0.002895j
[2025-09-12 03:41:23] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -52.089995-0.002751j
[2025-09-12 03:41:39] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -52.072489-0.003771j
[2025-09-12 03:41:54] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -52.145474-0.000942j
[2025-09-12 03:42:09] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -52.207453+0.001225j
[2025-09-12 03:42:25] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -51.988817+0.003103j
[2025-09-12 03:42:40] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -52.262569+0.000517j
[2025-09-12 03:42:55] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -52.210035-0.000115j
[2025-09-12 03:43:10] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -52.185828-0.001111j
[2025-09-12 03:43:26] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -52.087917-0.000136j
[2025-09-12 03:43:41] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -52.062267-0.000469j
[2025-09-12 03:43:56] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -52.088372+0.000071j
[2025-09-12 03:44:11] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -52.223823-0.003038j
[2025-09-12 03:44:27] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -52.046596-0.001847j
[2025-09-12 03:44:42] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -51.906068-0.000088j
[2025-09-12 03:44:57] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -52.095402+0.004650j
[2025-09-12 03:45:12] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -52.026628-0.001584j
[2025-09-12 03:45:28] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -51.984875+0.000392j
[2025-09-12 03:45:43] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -51.954637-0.000599j
[2025-09-12 03:45:58] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -52.143108+0.003257j
[2025-09-12 03:46:13] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -51.932790-0.000135j
[2025-09-12 03:46:29] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -52.010413-0.000576j
[2025-09-12 03:46:44] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -52.088539-0.002805j
[2025-09-12 03:46:59] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -52.049836-0.002156j
[2025-09-12 03:47:14] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -52.066698-0.001138j
[2025-09-12 03:47:30] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -52.040734+0.000058j
[2025-09-12 03:47:45] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -51.944005+0.000888j
[2025-09-12 03:48:00] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -52.060592+0.000425j
[2025-09-12 03:48:15] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -52.099046-0.000798j
[2025-09-12 03:48:31] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -52.055855+0.000193j
[2025-09-12 03:48:46] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -52.086314+0.000699j
[2025-09-12 03:49:01] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -52.031628-0.001876j
[2025-09-12 03:49:17] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -52.005741+0.000469j
[2025-09-12 03:49:32] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -51.997344-0.001277j
[2025-09-12 03:49:47] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -51.970493+0.004284j
[2025-09-12 03:50:02] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -52.049843+0.001498j
[2025-09-12 03:50:18] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -51.953557-0.001855j
[2025-09-12 03:50:33] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -52.046155+0.004499j
[2025-09-12 03:50:48] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -52.158364+0.000852j
[2025-09-12 03:51:03] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -52.114317+0.000502j
[2025-09-12 03:51:19] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -52.152877-0.001655j
[2025-09-12 03:51:34] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -52.071367-0.006249j
[2025-09-12 03:51:49] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -52.194038+0.002246j
[2025-09-12 03:52:04] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -52.228153+0.000601j
[2025-09-12 03:52:20] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -52.199642-0.000242j
[2025-09-12 03:52:35] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -52.284978+0.000207j
[2025-09-12 03:52:50] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -52.255082+0.001538j
[2025-09-12 03:53:05] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -52.292255+0.002140j
[2025-09-12 03:53:20] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -52.226496-0.000761j
[2025-09-12 03:53:36] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -52.124800+0.000516j
[2025-09-12 03:53:51] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -52.132676-0.000445j
[2025-09-12 03:54:06] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -51.958380+0.002457j
[2025-09-12 03:54:21] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -52.157798+0.001348j
[2025-09-12 03:54:37] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -52.095713-0.002758j
[2025-09-12 03:54:52] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -52.059988+0.002401j
[2025-09-12 03:55:07] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -52.185270-0.003468j
[2025-09-12 03:55:23] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -52.120702+0.002991j
[2025-09-12 03:55:38] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -52.072803-0.000789j
[2025-09-12 03:55:53] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -52.027054-0.000192j
[2025-09-12 03:56:08] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -51.969495+0.004144j
[2025-09-12 03:56:24] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -51.995964+0.001812j
[2025-09-12 03:56:39] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -52.020986-0.002963j
[2025-09-12 03:56:54] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -52.091991+0.004092j
[2025-09-12 03:57:09] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -52.024416+0.001492j
[2025-09-12 03:57:25] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -52.007642-0.001873j
[2025-09-12 03:57:40] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -52.110790+0.001398j
[2025-09-12 03:57:55] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -52.067661+0.000251j
[2025-09-12 03:58:10] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -52.129315+0.002392j
[2025-09-12 03:58:26] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -52.147291-0.000707j
[2025-09-12 03:58:41] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -52.074509-0.000546j
[2025-09-12 03:58:56] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -52.123790+0.003865j
[2025-09-12 03:59:11] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -52.172966+0.000164j
[2025-09-12 03:59:27] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -52.065187+0.001587j
[2025-09-12 03:59:42] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -52.214937+0.001526j
[2025-09-12 03:59:57] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -52.192453-0.001098j
[2025-09-12 04:00:12] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -52.016451+0.003384j
[2025-09-12 04:00:28] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -52.106045-0.003674j
[2025-09-12 04:00:43] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -52.048313+0.003407j
[2025-09-12 04:00:58] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -52.225418-0.000314j
[2025-09-12 04:01:13] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -52.400449+0.002553j
[2025-09-12 04:01:29] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -52.113716+0.000624j
[2025-09-12 04:01:44] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -52.080261+0.002112j
[2025-09-12 04:01:55] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -52.128022+0.000215j
[2025-09-12 04:02:05] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -52.107732-0.002074j
[2025-09-12 04:02:16] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -52.132619-0.001887j
[2025-09-12 04:02:26] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -52.231570-0.001173j
[2025-09-12 04:02:36] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -52.152506-0.000700j
[2025-09-12 04:02:46] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -52.154430-0.002186j
[2025-09-12 04:02:59] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -52.074222-0.001834j
[2025-09-12 04:03:14] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -52.135889-0.000441j
[2025-09-12 04:03:29] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -52.080594-0.000884j
[2025-09-12 04:03:44] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -52.004972+0.004772j
[2025-09-12 04:04:00] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -52.061092+0.000341j
[2025-09-12 04:04:15] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -52.087963+0.000936j
[2025-09-12 04:04:30] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -52.127422+0.004221j
[2025-09-12 04:04:46] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -52.098886-0.000472j
[2025-09-12 04:04:46] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-12 04:05:01] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -52.177225-0.003199j
[2025-09-12 04:05:16] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -52.094222-0.000011j
[2025-09-12 04:05:32] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -51.913459-0.002242j
[2025-09-12 04:05:47] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -51.966295+0.001926j
[2025-09-12 04:06:02] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -52.038934-0.002526j
[2025-09-12 04:06:18] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -51.944110+0.000894j
[2025-09-12 04:06:33] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -51.961090+0.004173j
[2025-09-12 04:06:48] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -51.914481+0.003491j
[2025-09-12 04:07:04] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -52.098962+0.001251j
[2025-09-12 04:07:19] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -52.090713+0.001395j
[2025-09-12 04:07:34] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -52.094646-0.007782j
[2025-09-12 04:07:50] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -51.941612+0.001485j
[2025-09-12 04:08:05] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -51.982960-0.002589j
[2025-09-12 04:08:20] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -51.923251+0.000262j
[2025-09-12 04:08:35] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -52.048755-0.004412j
[2025-09-12 04:08:51] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -52.165247-0.004195j
[2025-09-12 04:09:06] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -52.020920-0.004009j
[2025-09-12 04:09:21] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -52.096652+0.000712j
[2025-09-12 04:09:37] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -52.102541-0.001290j
[2025-09-12 04:09:52] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -51.997099+0.003239j
[2025-09-12 04:10:07] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -51.923242-0.001343j
[2025-09-12 04:10:23] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -52.153846-0.000775j
[2025-09-12 04:10:38] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -52.027913-0.000830j
[2025-09-12 04:10:53] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -51.984575-0.001011j
[2025-09-12 04:11:09] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -52.034879-0.000163j
[2025-09-12 04:11:24] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -52.094814+0.002559j
[2025-09-12 04:11:39] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -52.158671+0.001455j
[2025-09-12 04:11:55] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -52.077701+0.001011j
[2025-09-12 04:12:10] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -52.004596+0.001094j
[2025-09-12 04:12:24] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -52.003638-0.001058j
[2025-09-12 04:12:34] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -52.076408-0.003289j
[2025-09-12 04:12:48] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -51.976381-0.002447j
[2025-09-12 04:12:58] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -52.135891+0.000579j
[2025-09-12 04:13:13] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -52.000813-0.000462j
[2025-09-12 04:13:28] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -52.060675-0.000237j
[2025-09-12 04:13:43] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -52.108413+0.000254j
[2025-09-12 04:13:59] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -52.176779-0.000478j
[2025-09-12 04:14:14] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -52.150919-0.004025j
[2025-09-12 04:14:29] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -52.220364-0.002412j
[2025-09-12 04:14:44] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -52.136509+0.005143j
[2025-09-12 04:15:00] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -52.135393-0.002038j
[2025-09-12 04:15:15] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -52.184863-0.000877j
[2025-09-12 04:15:30] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -52.040097+0.001818j
[2025-09-12 04:15:45] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -52.139964+0.000803j
[2025-09-12 04:16:01] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -52.149102-0.002870j
[2025-09-12 04:16:16] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -52.091700+0.000374j
[2025-09-12 04:16:31] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -52.070576+0.000792j
[2025-09-12 04:16:46] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -52.140449+0.001269j
[2025-09-12 04:17:02] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -52.156409-0.001735j
[2025-09-12 04:17:17] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -52.134352-0.000473j
[2025-09-12 04:17:32] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -52.135432-0.001799j
[2025-09-12 04:17:47] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -52.168305-0.003262j
[2025-09-12 04:18:03] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -52.198129-0.001419j
[2025-09-12 04:18:18] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -52.160295+0.001228j
[2025-09-12 04:18:33] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -52.149107+0.001371j
[2025-09-12 04:18:48] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -52.194520+0.002256j
[2025-09-12 04:19:03] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -52.197215+0.001837j
[2025-09-12 04:19:19] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -52.192080+0.001539j
[2025-09-12 04:19:34] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -52.133713+0.000245j
[2025-09-12 04:19:49] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -52.059102+0.000004j
[2025-09-12 04:20:04] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -52.115724+0.000230j
[2025-09-12 04:20:20] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -52.111729-0.001589j
[2025-09-12 04:20:35] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -52.138495-0.000584j
[2025-09-12 04:20:50] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -52.070116-0.000226j
[2025-09-12 04:21:05] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -52.156086+0.000487j
[2025-09-12 04:21:20] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -52.120057+0.003776j
[2025-09-12 04:21:36] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -52.019835-0.000157j
[2025-09-12 04:21:51] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -52.072983+0.000208j
[2025-09-12 04:22:06] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -52.128634-0.000756j
[2025-09-12 04:22:21] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -52.159317+0.002091j
[2025-09-12 04:22:37] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -52.218192+0.001318j
[2025-09-12 04:22:52] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -52.176741-0.000487j
[2025-09-12 04:23:07] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -52.290497+0.003594j
[2025-09-12 04:23:22] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -52.263180+0.000292j
[2025-09-12 04:23:37] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -52.217737+0.001484j
[2025-09-12 04:23:53] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -52.245341-0.000584j
[2025-09-12 04:24:08] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -52.229357-0.004548j
[2025-09-12 04:24:23] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -52.263631-0.000722j
[2025-09-12 04:24:38] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -52.369105+0.003504j
[2025-09-12 04:24:54] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -52.255911+0.001172j
[2025-09-12 04:25:09] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -52.247599+0.000534j
[2025-09-12 04:25:24] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -52.138005-0.001595j
[2025-09-12 04:25:39] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -52.036299+0.001122j
[2025-09-12 04:25:54] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -52.091155+0.002044j
[2025-09-12 04:26:10] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -52.049172+0.001524j
[2025-09-12 04:26:25] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -52.041096+0.000310j
[2025-09-12 04:26:40] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -52.039642+0.002136j
[2025-09-12 04:26:55] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -52.067145+0.002180j
[2025-09-12 04:27:11] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -52.080083-0.000260j
[2025-09-12 04:27:26] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -52.080800-0.000263j
[2025-09-12 04:27:41] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -52.229078-0.002918j
[2025-09-12 04:27:56] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -52.168883+0.002017j
[2025-09-12 04:28:12] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -52.123023+0.002142j
[2025-09-12 04:28:27] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -51.998977+0.005606j
[2025-09-12 04:28:42] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -52.170256-0.000416j
[2025-09-12 04:28:57] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -52.061899-0.003166j
[2025-09-12 04:29:13] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -52.108907-0.000761j
[2025-09-12 04:29:28] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -52.165042-0.001179j
[2025-09-12 04:29:43] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -52.086537+0.002559j
[2025-09-12 04:29:58] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -52.146868+0.002844j
[2025-09-12 04:30:13] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -51.992369-0.001054j
[2025-09-12 04:30:28] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -52.051908+0.002298j
[2025-09-12 04:30:44] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -51.937745-0.000052j
[2025-09-12 04:30:59] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -52.161602+0.000338j
[2025-09-12 04:31:14] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -52.023090+0.001075j
[2025-09-12 04:31:14] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-12 04:31:29] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -52.123722-0.002493j
[2025-09-12 04:31:45] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -52.085949+0.000060j
[2025-09-12 04:32:00] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -52.118229+0.000748j
[2025-09-12 04:32:15] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -52.166232+0.001749j
[2025-09-12 04:32:30] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -52.185049-0.000950j
[2025-09-12 04:32:46] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -52.109904-0.000569j
[2025-09-12 04:33:01] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -52.165851+0.001169j
[2025-09-12 04:33:16] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -52.135572+0.001972j
[2025-09-12 04:33:31] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -52.206817+0.001190j
[2025-09-12 04:33:47] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -52.124742+0.000404j
[2025-09-12 04:34:02] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -52.121181+0.000332j
[2025-09-12 04:34:17] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -52.032702+0.002615j
[2025-09-12 04:34:32] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -52.109670-0.000705j
[2025-09-12 04:34:48] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -52.054608+0.001616j
[2025-09-12 04:35:03] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -51.965040-0.001989j
[2025-09-12 04:35:18] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -51.967581-0.000109j
[2025-09-12 04:35:33] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -51.954344+0.001571j
[2025-09-12 04:35:48] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -52.140234+0.000995j
[2025-09-12 04:36:04] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -52.112549-0.004566j
[2025-09-12 04:36:19] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -52.067514+0.001012j
[2025-09-12 04:36:34] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -52.010664+0.003869j
[2025-09-12 04:36:50] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -51.999347-0.003125j
[2025-09-12 04:37:05] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -52.060862-0.000212j
[2025-09-12 04:37:20] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -52.000326+0.002184j
[2025-09-12 04:37:35] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -52.040158-0.001293j
[2025-09-12 04:37:51] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -52.124990-0.001247j
[2025-09-12 04:38:06] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -52.051687-0.000437j
[2025-09-12 04:38:21] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -52.112415+0.005398j
[2025-09-12 04:38:36] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -52.016562+0.000954j
[2025-09-12 04:38:52] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -51.993236+0.001390j
[2025-09-12 04:39:07] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -52.176556-0.001900j
[2025-09-12 04:39:22] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -52.265053-0.003213j
[2025-09-12 04:39:38] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -52.161049-0.000791j
[2025-09-12 04:39:53] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -52.113366-0.000388j
[2025-09-12 04:40:08] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -52.184117-0.004009j
[2025-09-12 04:40:23] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -52.106726-0.004753j
[2025-09-12 04:40:39] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -52.145167-0.003588j
[2025-09-12 04:40:54] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -52.155141+0.000148j
[2025-09-12 04:41:09] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -52.176855-0.000449j
[2025-09-12 04:41:25] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -52.176441+0.000791j
[2025-09-12 04:41:40] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -52.255317+0.002556j
[2025-09-12 04:41:55] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -52.064331-0.003127j
[2025-09-12 04:42:10] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -52.010901-0.003325j
[2025-09-12 04:42:26] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -52.059736+0.001146j
[2025-09-12 04:42:41] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -52.141904-0.001226j
[2025-09-12 04:42:56] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -51.985477+0.003189j
[2025-09-12 04:43:12] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -52.075028+0.001734j
[2025-09-12 04:43:27] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -52.094874-0.002417j
[2025-09-12 04:43:42] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -52.032890-0.000554j
[2025-09-12 04:43:57] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -51.971277+0.002655j
[2025-09-12 04:44:12] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -51.936464+0.001534j
[2025-09-12 04:44:28] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -52.116682+0.003519j
[2025-09-12 04:44:43] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -52.221541-0.001482j
[2025-09-12 04:44:58] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -52.233922+0.000942j
[2025-09-12 04:45:13] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -52.209437-0.000280j
[2025-09-12 04:45:29] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -52.133486-0.003318j
[2025-09-12 04:45:44] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -52.178603+0.001614j
[2025-09-12 04:45:59] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -52.132682+0.003510j
[2025-09-12 04:46:14] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -52.159647+0.000455j
[2025-09-12 04:46:26] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -52.145597-0.000508j
[2025-09-12 04:46:37] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -52.196252-0.002240j
[2025-09-12 04:46:47] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -52.058036+0.001997j
[2025-09-12 04:46:57] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -52.139368-0.000387j
[2025-09-12 04:47:07] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -52.045890-0.001738j
[2025-09-12 04:47:17] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -52.011347+0.003412j
[2025-09-12 04:47:27] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -51.918771+0.001294j
[2025-09-12 04:47:40] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -52.010221+0.002725j
[2025-09-12 04:47:55] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -52.116562+0.002107j
[2025-09-12 04:48:11] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -52.043328+0.001303j
[2025-09-12 04:48:26] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -52.088766+0.002534j
[2025-09-12 04:48:41] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -52.065733+0.000211j
[2025-09-12 04:48:57] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -52.078519-0.001121j
[2025-09-12 04:49:12] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -52.103310+0.004044j
[2025-09-12 04:49:27] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -52.083536-0.003185j
[2025-09-12 04:49:43] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -52.122971+0.000979j
[2025-09-12 04:49:58] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -52.006274+0.000420j
[2025-09-12 04:50:13] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -52.163984+0.003230j
[2025-09-12 04:50:28] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -52.171125-0.000772j
[2025-09-12 04:50:44] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -52.060925-0.002552j
[2025-09-12 04:50:59] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -52.136078-0.002322j
[2025-09-12 04:51:14] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -52.140048+0.001261j
[2025-09-12 04:51:30] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -52.161785-0.000785j
[2025-09-12 04:51:45] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -52.169406-0.001728j
[2025-09-12 04:52:00] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -52.180176-0.001871j
[2025-09-12 04:52:16] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -52.011441-0.002077j
[2025-09-12 04:52:31] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -52.106743-0.002404j
[2025-09-12 04:52:46] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -52.231752-0.003031j
[2025-09-12 04:53:02] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -52.091763+0.000330j
[2025-09-12 04:53:17] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -52.187927+0.001076j
[2025-09-12 04:53:32] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -52.252400-0.001817j
[2025-09-12 04:53:48] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -52.218806-0.000129j
[2025-09-12 04:54:03] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -52.207330-0.004574j
[2025-09-12 04:54:18] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -52.317459+0.002919j
[2025-09-12 04:54:34] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -52.197156-0.001058j
[2025-09-12 04:54:49] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -52.139208+0.003146j
[2025-09-12 04:55:04] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -52.198429-0.000064j
[2025-09-12 04:55:19] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -52.200677+0.001526j
[2025-09-12 04:55:35] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -52.285677-0.000774j
[2025-09-12 04:55:50] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -52.167426+0.002746j
[2025-09-12 04:56:05] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -52.101153+0.000951j
[2025-09-12 04:56:21] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -51.978277+0.000115j
[2025-09-12 04:56:36] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -52.019513+0.000790j
[2025-09-12 04:56:51] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -52.198929-0.000082j
[2025-09-12 04:57:04] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -52.149743-0.000646j
[2025-09-12 04:57:09] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -52.118717-0.001571j
[2025-09-12 04:57:09] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-12 04:57:09] ✅ Training completed | Restarts: 2
[2025-09-12 04:57:09] ============================================================
[2025-09-12 04:57:09] Training completed | Runtime: 15893.8s
[2025-09-12 04:57:11] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-12 04:57:11] ============================================================
