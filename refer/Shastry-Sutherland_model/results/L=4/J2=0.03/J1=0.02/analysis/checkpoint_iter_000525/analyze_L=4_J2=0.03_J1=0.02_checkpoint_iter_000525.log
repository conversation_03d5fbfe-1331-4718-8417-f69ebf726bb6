[2025-09-12 22:38:38] 使用checkpoint文件: results/L=4/J2=0.03/J1=0.02/training/checkpoints/checkpoint_iter_000525.pkl
[2025-09-12 22:38:56] ✓ 从checkpoint加载参数: 525
[2025-09-12 22:38:56]   - 能量: -52.094469+0.002092j ± 0.082939
[2025-09-12 22:38:56] ================================================================================
[2025-09-12 22:38:56] 加载量子态: L=4, J2=0.03, J1=0.02, checkpoint=checkpoint_iter_000525
[2025-09-12 22:38:56] 使用采样数目: 1048576
[2025-09-12 22:38:56] 设置样本数为: 1048576
[2025-09-12 22:38:56] 开始生成共享样本集...
[2025-09-12 22:41:57] 样本生成完成,耗时: 181.146 秒
[2025-09-12 22:41:57] ================================================================================
[2025-09-12 22:41:57] 开始计算自旋结构因子...
[2025-09-12 22:41:57] 初始化操作符缓存...
[2025-09-12 22:41:57] 预构建所有自旋相关操作符...
[2025-09-12 22:41:57] 开始计算自旋相关函数...
[2025-09-12 22:42:11] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 14.204s
[2025-09-12 22:42:29] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 17.787s
[2025-09-12 22:42:38] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.381s
[2025-09-12 22:42:48] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.373s
[2025-09-12 22:42:57] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.362s
[2025-09-12 22:43:07] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.386s
[2025-09-12 22:43:16] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.366s
[2025-09-12 22:43:25] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.392s
[2025-09-12 22:43:35] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.366s
[2025-09-12 22:43:44] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.385s
[2025-09-12 22:43:53] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.360s
[2025-09-12 22:44:03] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.401s
[2025-09-12 22:44:12] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.392s
[2025-09-12 22:44:22] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.396s
[2025-09-12 22:44:31] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.390s
[2025-09-12 22:44:40] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.378s
[2025-09-12 22:44:50] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.388s
[2025-09-12 22:44:59] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.385s
[2025-09-12 22:45:09] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.383s
[2025-09-12 22:45:18] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.380s
[2025-09-12 22:45:27] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.375s
[2025-09-12 22:45:37] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.386s
[2025-09-12 22:45:46] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.383s
[2025-09-12 22:45:56] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.396s
[2025-09-12 22:46:05] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.391s
[2025-09-12 22:46:14] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.389s
[2025-09-12 22:46:24] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.365s
[2025-09-12 22:46:33] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.386s
[2025-09-12 22:46:42] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.389s
[2025-09-12 22:46:52] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.362s
[2025-09-12 22:47:01] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.389s
[2025-09-12 22:47:11] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.420s
[2025-09-12 22:47:20] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.381s
[2025-09-12 22:47:29] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.395s
[2025-09-12 22:47:39] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.393s
[2025-09-12 22:47:48] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.383s
[2025-09-12 22:47:58] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.391s
[2025-09-12 22:48:07] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.376s
[2025-09-12 22:48:16] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.390s
[2025-09-12 22:48:26] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.392s
[2025-09-12 22:48:35] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.388s
[2025-09-12 22:48:45] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.363s
[2025-09-12 22:48:54] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.405s
[2025-09-12 22:49:03] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.316s
[2025-09-12 22:49:13] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.361s
[2025-09-12 22:49:22] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.362s
[2025-09-12 22:49:31] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.392s
[2025-09-12 22:49:41] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.363s
[2025-09-12 22:49:50] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.379s
[2025-09-12 22:50:00] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.380s
[2025-09-12 22:50:09] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.390s
[2025-09-12 22:50:18] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.392s
[2025-09-12 22:50:28] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.380s
[2025-09-12 22:50:37] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.447s
[2025-09-12 22:50:47] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.304s
[2025-09-12 22:50:56] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.385s
[2025-09-12 22:51:05] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.370s
[2025-09-12 22:51:15] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.368s
[2025-09-12 22:51:24] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.393s
[2025-09-12 22:51:34] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.376s
[2025-09-12 22:51:43] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.389s
[2025-09-12 22:51:52] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.370s
[2025-09-12 22:52:02] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.397s
[2025-09-12 22:52:11] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.355s
[2025-09-12 22:52:11] 自旋相关函数计算完成,总耗时 614.10 秒
[2025-09-12 22:52:13] 计算傅里叶变换...
[2025-09-12 22:52:18] 自旋结构因子计算完成
[2025-09-12 22:52:19] 自旋相关函数平均误差: 0.000664
