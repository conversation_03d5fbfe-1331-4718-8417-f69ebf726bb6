[2025-09-12 22:11:10] 使用checkpoint文件: results/L=4/J2=0.03/J1=0.02/training/checkpoints/checkpoint_iter_000315.pkl
[2025-09-12 22:11:28] ✓ 从checkpoint加载参数: 315
[2025-09-12 22:11:28]   - 能量: -52.172776+0.000662j ± 0.084303
[2025-09-12 22:11:28] ================================================================================
[2025-09-12 22:11:28] 加载量子态: L=4, J2=0.03, J1=0.02, checkpoint=checkpoint_iter_000315
[2025-09-12 22:11:28] 使用采样数目: 1048576
[2025-09-12 22:11:28] 设置样本数为: 1048576
[2025-09-12 22:11:28] 开始生成共享样本集...
[2025-09-12 22:14:29] 样本生成完成,耗时: 181.438 秒
[2025-09-12 22:14:29] ================================================================================
[2025-09-12 22:14:29] 开始计算自旋结构因子...
[2025-09-12 22:14:29] 初始化操作符缓存...
[2025-09-12 22:14:29] 预构建所有自旋相关操作符...
[2025-09-12 22:14:29] 开始计算自旋相关函数...
[2025-09-12 22:14:43] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 14.315s
[2025-09-12 22:15:01] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 17.772s
[2025-09-12 22:15:11] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.326s
[2025-09-12 22:15:20] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.361s
[2025-09-12 22:15:29] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.337s
[2025-09-12 22:15:39] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.357s
[2025-09-12 22:15:48] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.339s
[2025-09-12 22:15:57] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.345s
[2025-09-12 22:16:07] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.332s
[2025-09-12 22:16:16] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.329s
[2025-09-12 22:16:25] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.343s
[2025-09-12 22:16:35] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.357s
[2025-09-12 22:16:44] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.333s
[2025-09-12 22:16:53] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.337s
[2025-09-12 22:17:03] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.349s
[2025-09-12 22:17:12] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.336s
[2025-09-12 22:17:21] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.345s
[2025-09-12 22:17:31] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.347s
[2025-09-12 22:17:40] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.338s
[2025-09-12 22:17:49] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.346s
[2025-09-12 22:17:59] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.354s
[2025-09-12 22:18:08] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.330s
[2025-09-12 22:18:18] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.333s
[2025-09-12 22:18:27] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.352s
[2025-09-12 22:18:36] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.345s
[2025-09-12 22:18:46] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.368s
[2025-09-12 22:18:55] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.327s
[2025-09-12 22:19:04] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.355s
[2025-09-12 22:19:14] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.342s
[2025-09-12 22:19:23] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.335s
[2025-09-12 22:19:32] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.330s
[2025-09-12 22:19:42] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.328s
[2025-09-12 22:19:51] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.350s
[2025-09-12 22:20:00] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.357s
[2025-09-12 22:20:10] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.335s
[2025-09-12 22:20:19] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.347s
[2025-09-12 22:20:29] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.360s
[2025-09-12 22:20:38] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.289s
[2025-09-12 22:20:47] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.337s
[2025-09-12 22:20:57] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.357s
[2025-09-12 22:21:06] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.338s
[2025-09-12 22:21:15] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.336s
[2025-09-12 22:21:25] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.319s
[2025-09-12 22:21:34] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.355s
[2025-09-12 22:21:43] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.333s
[2025-09-12 22:21:53] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.338s
[2025-09-12 22:22:02] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.347s
[2025-09-12 22:22:11] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.360s
[2025-09-12 22:22:21] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.259s
[2025-09-12 22:22:30] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.356s
[2025-09-12 22:22:39] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.336s
[2025-09-12 22:22:49] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.343s
[2025-09-12 22:22:58] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.318s
[2025-09-12 22:23:07] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.367s
[2025-09-12 22:23:17] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.335s
[2025-09-12 22:23:26] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.359s
[2025-09-12 22:23:35] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.343s
[2025-09-12 22:23:45] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.319s
[2025-09-12 22:23:54] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.379s
[2025-09-12 22:24:03] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.341s
[2025-09-12 22:24:13] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.333s
[2025-09-12 22:24:22] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.338s
[2025-09-12 22:24:32] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.359s
[2025-09-12 22:24:41] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.325s
[2025-09-12 22:24:41] 自旋相关函数计算完成,总耗时 611.74 秒
[2025-09-12 22:24:42] 计算傅里叶变换...
[2025-09-12 22:24:44] 自旋结构因子计算完成
[2025-09-12 22:24:46] 自旋相关函数平均误差: 0.000675
