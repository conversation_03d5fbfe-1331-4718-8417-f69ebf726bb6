[2025-09-11 20:03:31] ✓ 从checkpoint恢复: results/L=4/J2=0.03/J1=0.06/training/checkpoints/final_GCNN.pkl
[2025-09-11 20:03:31]   - 迭代次数: final
[2025-09-11 20:03:31]   - 能量: -53.579904-0.002600j ± 0.081268
[2025-09-11 20:03:31]   - 时间戳: 2025-09-11T20:03:19.626797+08:00
[2025-09-11 20:03:57] ✓ 变分状态参数已从checkpoint恢复
[2025-09-11 20:03:57] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-11 20:03:57] ==================================================
[2025-09-11 20:03:57] GCNN for Shastry-Sutherland Model
[2025-09-11 20:03:57] ==================================================
[2025-09-11 20:03:57] System parameters:
[2025-09-11 20:03:57]   - System size: L=4, N=64
[2025-09-11 20:03:57]   - System parameters: J1=0.07, J2=0.03, Q=0.97
[2025-09-11 20:03:57] --------------------------------------------------
[2025-09-11 20:03:57] Model parameters:
[2025-09-11 20:03:57]   - Number of layers = 4
[2025-09-11 20:03:57]   - Number of features = 4
[2025-09-11 20:03:57]   - Total parameters = 12572
[2025-09-11 20:03:57] --------------------------------------------------
[2025-09-11 20:03:57] Training parameters:
[2025-09-11 20:03:57]   - Learning rate: 0.015
[2025-09-11 20:03:57]   - Total iterations: 1050
[2025-09-11 20:03:57]   - Annealing cycles: 3
[2025-09-11 20:03:57]   - Initial period: 150
[2025-09-11 20:03:57]   - Period multiplier: 2.0
[2025-09-11 20:03:57]   - Temperature range: 0.0-1.0
[2025-09-11 20:03:57]   - Samples: 4096
[2025-09-11 20:03:57]   - Discarded samples: 0
[2025-09-11 20:03:57]   - Chunk size: 2048
[2025-09-11 20:03:57]   - Diagonal shift: 0.2
[2025-09-11 20:03:57]   - Gradient clipping: 1.0
[2025-09-11 20:03:57]   - Checkpoint enabled: interval=105
[2025-09-11 20:03:57]   - Checkpoint directory: results/L=4/J2=0.03/J1=0.07/training/checkpoints
[2025-09-11 20:03:57] --------------------------------------------------
[2025-09-11 20:03:57] Device status:
[2025-09-11 20:03:57]   - Devices model: NVIDIA H200 NVL
[2025-09-11 20:03:57]   - Number of devices: 1
[2025-09-11 20:03:57]   - Sharding: True
[2025-09-11 20:03:57] ============================================================
[2025-09-11 20:05:50] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -54.047206+0.005617j
[2025-09-11 20:06:59] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -54.003395+0.006322j
[2025-09-11 20:07:14] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -54.039623+0.003124j
[2025-09-11 20:07:29] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -54.139089+0.003296j
[2025-09-11 20:07:45] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -54.113672+0.000221j
[2025-09-11 20:08:00] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -54.069137+0.003482j
[2025-09-11 20:08:15] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -53.992447+0.003288j
[2025-09-11 20:08:31] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -53.951310-0.001434j
[2025-09-11 20:08:46] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -54.126766-0.002189j
[2025-09-11 20:09:02] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -54.086140+0.000098j
[2025-09-11 20:09:17] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -53.974498+0.001623j
[2025-09-11 20:09:33] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -54.045359-0.002274j
[2025-09-11 20:09:48] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -54.027543+0.001114j
[2025-09-11 20:10:03] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -54.127228-0.001302j
[2025-09-11 20:10:19] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -54.176812+0.000231j
[2025-09-11 20:10:34] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -54.137700-0.001541j
[2025-09-11 20:10:50] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -54.020014-0.000110j
[2025-09-11 20:11:05] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -54.003970+0.000743j
[2025-09-11 20:11:21] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -54.017553+0.000582j
[2025-09-11 20:11:36] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -53.970602+0.003304j
[2025-09-11 20:11:52] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -53.955815-0.002955j
[2025-09-11 20:12:07] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -54.098635+0.001748j
[2025-09-11 20:12:22] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -54.112138+0.001614j
[2025-09-11 20:12:38] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -54.037958+0.000449j
[2025-09-11 20:12:53] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -54.163470-0.001926j
[2025-09-11 20:13:09] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -54.244628-0.000590j
[2025-09-11 20:13:24] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -54.149251+0.001238j
[2025-09-11 20:13:40] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -54.198324+0.000041j
[2025-09-11 20:13:55] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -54.106968-0.000245j
[2025-09-11 20:14:10] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -54.225417-0.001239j
[2025-09-11 20:14:26] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -54.145692+0.000577j
[2025-09-11 20:14:41] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -54.185602+0.003683j
[2025-09-11 20:14:56] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -54.349987-0.000808j
[2025-09-11 20:15:12] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -54.134283-0.002772j
[2025-09-11 20:15:27] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -54.112546+0.001585j
[2025-09-11 20:15:42] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -54.032485+0.004355j
[2025-09-11 20:15:58] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -53.963426+0.002671j
[2025-09-11 20:16:13] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -54.061213+0.002130j
[2025-09-11 20:16:29] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -54.185618+0.004150j
[2025-09-11 20:16:44] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -54.166508+0.001955j
[2025-09-11 20:16:59] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -54.233833+0.002269j
[2025-09-11 20:17:15] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -54.129914+0.000743j
[2025-09-11 20:17:30] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -54.235638-0.001628j
[2025-09-11 20:17:46] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -54.055347-0.001313j
[2025-09-11 20:18:01] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -54.093586+0.001193j
[2025-09-11 20:18:17] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -54.228198+0.001006j
[2025-09-11 20:18:32] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -54.113442+0.001241j
[2025-09-11 20:18:48] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -54.133197+0.000250j
[2025-09-11 20:19:03] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -54.077812-0.000011j
[2025-09-11 20:19:18] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -54.005931+0.001115j
[2025-09-11 20:19:34] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -54.050847+0.003285j
[2025-09-11 20:19:49] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -54.129097+0.000498j
[2025-09-11 20:20:05] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -54.044239+0.002050j
[2025-09-11 20:20:20] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -54.045181+0.001954j
[2025-09-11 20:20:36] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -54.111351+0.000647j
[2025-09-11 20:20:51] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -54.014937+0.003695j
[2025-09-11 20:21:07] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -53.948776-0.000934j
[2025-09-11 20:21:22] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -54.016596+0.003926j
[2025-09-11 20:21:38] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -54.021374-0.002160j
[2025-09-11 20:21:53] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -53.833289+0.000604j
[2025-09-11 20:22:08] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -53.831167-0.001582j
[2025-09-11 20:22:24] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -53.917500-0.004150j
[2025-09-11 20:22:39] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -54.039105+0.004647j
[2025-09-11 20:22:55] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -53.826021-0.003324j
[2025-09-11 20:23:10] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -53.975438+0.000443j
[2025-09-11 20:23:26] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -54.117592-0.000464j
[2025-09-11 20:23:41] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -54.200653+0.000072j
[2025-09-11 20:23:56] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -54.131658-0.000808j
[2025-09-11 20:24:12] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -54.172490-0.001636j
[2025-09-11 20:24:27] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -54.180620-0.005007j
[2025-09-11 20:24:43] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -54.151814-0.005106j
[2025-09-11 20:24:58] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -54.014032+0.001702j
[2025-09-11 20:25:13] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -54.058205-0.001397j
[2025-09-11 20:25:29] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -53.970895+0.000027j
[2025-09-11 20:25:44] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -54.020074-0.000753j
[2025-09-11 20:26:00] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -53.973026-0.003094j
[2025-09-11 20:26:15] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -54.129241+0.000687j
[2025-09-11 20:26:31] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -54.096304-0.000583j
[2025-09-11 20:26:46] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -54.200593+0.002770j
[2025-09-11 20:27:01] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -54.238963-0.001128j
[2025-09-11 20:27:17] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -54.191007-0.000963j
[2025-09-11 20:27:32] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -54.146384-0.004046j
[2025-09-11 20:27:48] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -54.221976-0.002357j
[2025-09-11 20:28:03] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -54.039252-0.000757j
[2025-09-11 20:28:18] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -54.136931+0.001877j
[2025-09-11 20:28:34] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -54.106815-0.001663j
[2025-09-11 20:28:49] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -54.162524+0.000460j
[2025-09-11 20:29:05] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -54.137708-0.000396j
[2025-09-11 20:29:20] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -54.049081-0.003562j
[2025-09-11 20:29:35] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -54.050187-0.000842j
[2025-09-11 20:29:51] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -54.165460+0.002051j
[2025-09-11 20:30:06] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -54.227113-0.002008j
[2025-09-11 20:30:22] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -54.129912+0.000073j
[2025-09-11 20:30:37] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -54.087324+0.003975j
[2025-09-11 20:30:52] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -54.126752+0.002830j
[2025-09-11 20:31:08] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -54.201024-0.000760j
[2025-09-11 20:31:23] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -54.122862-0.001447j
[2025-09-11 20:31:39] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -54.060780-0.001561j
[2025-09-11 20:31:54] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -54.166514-0.000940j
[2025-09-11 20:32:09] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -54.275253+0.000070j
[2025-09-11 20:32:25] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -54.151202-0.001930j
[2025-09-11 20:32:40] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -54.261247-0.000238j
[2025-09-11 20:32:56] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -54.019542+0.002587j
[2025-09-11 20:33:11] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -54.105622+0.000951j
[2025-09-11 20:33:26] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -54.163328-0.000421j
[2025-09-11 20:33:26] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-11 20:33:42] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -54.082152+0.002215j
[2025-09-11 20:33:57] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -54.188130-0.004627j
[2025-09-11 20:34:13] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -54.030137-0.001982j
[2025-09-11 20:34:28] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -54.020881+0.001510j
[2025-09-11 20:34:43] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -53.984837-0.001069j
[2025-09-11 20:34:59] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -54.006997-0.002250j
[2025-09-11 20:35:14] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -53.990342-0.001499j
[2025-09-11 20:35:30] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -54.123828-0.001782j
[2025-09-11 20:35:45] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -53.983362-0.002883j
[2025-09-11 20:36:00] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -53.899121-0.004309j
[2025-09-11 20:36:16] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -53.948762+0.000805j
[2025-09-11 20:36:31] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -54.069109-0.002220j
[2025-09-11 20:36:47] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -53.992198-0.002281j
[2025-09-11 20:37:02] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -54.112539-0.002316j
[2025-09-11 20:37:17] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -54.146547-0.001850j
[2025-09-11 20:37:33] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -54.148333+0.001163j
[2025-09-11 20:37:48] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -54.153826-0.002115j
[2025-09-11 20:38:04] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -54.170615-0.002692j
[2025-09-11 20:38:19] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -54.153412-0.002676j
[2025-09-11 20:38:34] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -54.107819-0.001901j
[2025-09-11 20:38:45] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -53.999407-0.004548j
[2025-09-11 20:38:55] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -54.146688+0.001495j
[2025-09-11 20:39:06] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -54.138059+0.000119j
[2025-09-11 20:39:16] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -54.033114-0.001629j
[2025-09-11 20:39:27] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -53.940517-0.000205j
[2025-09-11 20:39:37] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -54.114981+0.004346j
[2025-09-11 20:39:51] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -54.011928+0.002736j
[2025-09-11 20:40:07] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -54.165290+0.002293j
[2025-09-11 20:40:22] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -54.254596-0.000303j
[2025-09-11 20:40:38] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -54.157607-0.001891j
[2025-09-11 20:40:53] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -54.087283+0.001149j
[2025-09-11 20:41:09] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -54.056022-0.000952j
[2025-09-11 20:41:24] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -54.222500+0.000087j
[2025-09-11 20:41:40] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -54.091571-0.005611j
[2025-09-11 20:41:55] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -54.029310-0.003337j
[2025-09-11 20:42:10] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -54.227691+0.002045j
[2025-09-11 20:42:26] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -54.109933+0.000195j
[2025-09-11 20:42:41] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -54.037414+0.001873j
[2025-09-11 20:42:57] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -53.969595+0.001897j
[2025-09-11 20:43:12] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -54.105126-0.000749j
[2025-09-11 20:43:28] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -54.172392+0.001181j
[2025-09-11 20:43:43] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -54.032892-0.000429j
[2025-09-11 20:43:59] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -53.880074-0.001554j
[2025-09-11 20:44:14] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -54.051921-0.000421j
[2025-09-11 20:44:30] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -54.098015+0.000379j
[2025-09-11 20:44:30] RESTART #1 | Period: 300
[2025-09-11 20:44:45] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -54.044943+0.001057j
[2025-09-11 20:45:01] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -54.052719+0.004392j
[2025-09-11 20:45:16] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -54.123831+0.000731j
[2025-09-11 20:45:32] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -54.234643-0.000807j
[2025-09-11 20:45:47] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -54.146740-0.000296j
[2025-09-11 20:46:03] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -54.028775-0.002920j
[2025-09-11 20:46:18] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -54.112550+0.001292j
[2025-09-11 20:46:34] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -54.065085+0.002620j
[2025-09-11 20:46:49] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -54.056109-0.001995j
[2025-09-11 20:47:05] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -54.055087-0.000944j
[2025-09-11 20:47:20] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -54.178361+0.001900j
[2025-09-11 20:47:36] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -54.141021-0.001131j
[2025-09-11 20:47:51] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -54.165134-0.001142j
[2025-09-11 20:48:07] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -54.043275-0.003910j
[2025-09-11 20:48:22] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -54.012922+0.000761j
[2025-09-11 20:48:37] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -53.888572+0.000611j
[2025-09-11 20:48:53] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -53.957502+0.004011j
[2025-09-11 20:49:09] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -54.027170-0.001264j
[2025-09-11 20:49:19] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -54.060996+0.002273j
[2025-09-11 20:49:32] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -54.121685+0.000065j
[2025-09-11 20:49:44] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -54.141650-0.000114j
[2025-09-11 20:49:57] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -54.175438+0.002983j
[2025-09-11 20:50:12] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -54.097544+0.001200j
[2025-09-11 20:50:28] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -54.080924-0.000554j
[2025-09-11 20:50:43] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -54.096711-0.004768j
[2025-09-11 20:50:59] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -54.114799-0.001295j
[2025-09-11 20:51:14] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -54.129802-0.003988j
[2025-09-11 20:51:30] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -54.079223-0.000144j
[2025-09-11 20:51:45] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -53.982213+0.000075j
[2025-09-11 20:52:00] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -54.072169+0.003602j
[2025-09-11 20:52:16] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -54.100663-0.001088j
[2025-09-11 20:52:31] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -54.087890-0.004589j
[2025-09-11 20:52:47] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -54.116231-0.002701j
[2025-09-11 20:53:02] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -54.114898-0.000574j
[2025-09-11 20:53:18] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -54.094735-0.001344j
[2025-09-11 20:53:33] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -54.140091-0.003039j
[2025-09-11 20:53:48] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -54.147820-0.002622j
[2025-09-11 20:54:04] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -54.180581+0.001767j
[2025-09-11 20:54:19] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -54.070322-0.002312j
[2025-09-11 20:54:35] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -54.161045-0.000282j
[2025-09-11 20:54:50] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -54.034976+0.000928j
[2025-09-11 20:55:06] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -54.210915-0.002706j
[2025-09-11 20:55:21] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -54.121017-0.002452j
[2025-09-11 20:55:36] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -54.183621+0.001477j
[2025-09-11 20:55:52] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -54.135992-0.001767j
[2025-09-11 20:56:07] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -54.236637+0.000017j
[2025-09-11 20:56:23] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -54.124067-0.002842j
[2025-09-11 20:56:38] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -54.169893+0.002871j
[2025-09-11 20:56:54] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -54.108123-0.004663j
[2025-09-11 20:57:09] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -53.998010-0.000021j
[2025-09-11 20:57:24] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -54.097969-0.003813j
[2025-09-11 20:57:40] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -54.017304-0.001339j
[2025-09-11 20:57:55] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -54.025255+0.000539j
[2025-09-11 20:58:11] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -54.024846-0.002051j
[2025-09-11 20:58:26] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -53.997003+0.000798j
[2025-09-11 20:58:41] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -53.978386-0.001437j
[2025-09-11 20:58:57] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -54.071394+0.000639j
[2025-09-11 20:59:12] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -54.124713+0.002510j
[2025-09-11 20:59:27] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -54.069355-0.000335j
[2025-09-11 20:59:43] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -54.134290+0.001360j
[2025-09-11 20:59:43] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-11 20:59:58] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -54.120932-0.000711j
[2025-09-11 21:00:14] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -54.066944+0.001323j
[2025-09-11 21:00:29] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -54.074010-0.001843j
[2025-09-11 21:00:44] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -54.052303+0.001178j
[2025-09-11 21:01:00] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -53.990491-0.001997j
[2025-09-11 21:01:15] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -53.956066-0.000613j
[2025-09-11 21:01:31] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -54.056094+0.000055j
[2025-09-11 21:01:46] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -54.107385+0.000994j
[2025-09-11 21:02:01] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -54.108089+0.000804j
[2025-09-11 21:02:17] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -54.062618-0.003231j
[2025-09-11 21:02:32] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -54.043660+0.000635j
[2025-09-11 21:02:48] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -54.109775-0.000020j
[2025-09-11 21:03:03] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -54.012904-0.000002j
[2025-09-11 21:03:19] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -54.109096-0.000771j
[2025-09-11 21:03:34] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -54.197903+0.000894j
[2025-09-11 21:03:49] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -54.069037-0.000251j
[2025-09-11 21:04:05] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -54.043139+0.001237j
[2025-09-11 21:04:20] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -54.187028+0.002603j
[2025-09-11 21:04:36] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -54.167523-0.005649j
[2025-09-11 21:04:51] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -54.196455-0.000900j
[2025-09-11 21:05:06] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -54.265315-0.001800j
[2025-09-11 21:05:22] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -54.124680-0.005185j
[2025-09-11 21:05:37] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -54.106708-0.001039j
[2025-09-11 21:05:52] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -54.092532-0.002449j
[2025-09-11 21:06:08] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -54.199540-0.002328j
[2025-09-11 21:06:23] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -54.094208-0.001179j
[2025-09-11 21:06:39] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -54.069101-0.001147j
[2025-09-11 21:06:54] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -54.063585-0.001422j
[2025-09-11 21:07:09] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -54.054770-0.001699j
[2025-09-11 21:07:25] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -54.144399+0.002532j
[2025-09-11 21:07:40] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -54.200036+0.000940j
[2025-09-11 21:07:56] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -54.172260+0.000848j
[2025-09-11 21:08:11] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -54.135061+0.000985j
[2025-09-11 21:08:26] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -54.096640-0.002236j
[2025-09-11 21:08:42] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -53.976889-0.003526j
[2025-09-11 21:08:57] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -54.065322+0.000218j
[2025-09-11 21:09:12] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -53.917413+0.000792j
[2025-09-11 21:09:28] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -54.085747-0.000182j
[2025-09-11 21:09:43] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -53.980491+0.001779j
[2025-09-11 21:09:59] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -54.107278-0.001395j
[2025-09-11 21:10:14] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -54.065071-0.003611j
[2025-09-11 21:10:29] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -54.186233+0.000607j
[2025-09-11 21:10:45] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -54.088806-0.001262j
[2025-09-11 21:11:00] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -54.088659+0.000260j
[2025-09-11 21:11:16] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -54.168944-0.001241j
[2025-09-11 21:11:31] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -54.126334-0.002904j
[2025-09-11 21:11:46] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -54.093582+0.000612j
[2025-09-11 21:12:02] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -54.100495+0.001186j
[2025-09-11 21:12:17] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -54.134078+0.004330j
[2025-09-11 21:12:32] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -54.231050+0.000222j
[2025-09-11 21:12:48] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -54.040043+0.004369j
[2025-09-11 21:13:03] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -54.030935+0.001175j
[2025-09-11 21:13:18] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -54.033456-0.001347j
[2025-09-11 21:13:34] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -53.964392-0.000687j
[2025-09-11 21:13:49] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -54.024078+0.000012j
[2025-09-11 21:14:05] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -53.976691-0.003996j
[2025-09-11 21:14:20] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -54.080357+0.000577j
[2025-09-11 21:14:35] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -54.105767+0.000185j
[2025-09-11 21:14:51] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -54.053982+0.000052j
[2025-09-11 21:15:06] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -54.107325-0.001835j
[2025-09-11 21:15:21] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -54.149717-0.001612j
[2025-09-11 21:15:37] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -54.222459-0.002456j
[2025-09-11 21:15:52] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -54.038364+0.003929j
[2025-09-11 21:16:08] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -54.035047-0.002145j
[2025-09-11 21:16:23] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -54.043557-0.001769j
[2025-09-11 21:16:38] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -54.137293+0.003187j
[2025-09-11 21:16:54] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -54.262555+0.000606j
[2025-09-11 21:17:09] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -54.295599+0.000385j
[2025-09-11 21:17:24] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -54.192063+0.002250j
[2025-09-11 21:17:40] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -54.157988+0.000739j
[2025-09-11 21:17:55] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -54.150801+0.000379j
[2025-09-11 21:18:11] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -53.982287+0.001979j
[2025-09-11 21:18:26] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -54.033609+0.000929j
[2025-09-11 21:18:41] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -53.931970+0.003210j
[2025-09-11 21:18:57] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -53.848651+0.000135j
[2025-09-11 21:19:12] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -54.003379-0.000222j
[2025-09-11 21:19:27] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -53.940412+0.000316j
[2025-09-11 21:19:43] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -53.975279+0.003055j
[2025-09-11 21:19:58] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -54.000804-0.001293j
[2025-09-11 21:20:14] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -54.065107+0.003577j
[2025-09-11 21:20:29] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -54.072539+0.002711j
[2025-09-11 21:20:44] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -54.008884-0.003615j
[2025-09-11 21:21:00] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -54.026037-0.001112j
[2025-09-11 21:21:15] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -53.939493+0.004389j
[2025-09-11 21:21:30] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -54.021878-0.003649j
[2025-09-11 21:21:46] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -54.133797+0.001054j
[2025-09-11 21:22:01] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -54.150706-0.000323j
[2025-09-11 21:22:16] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -54.118709-0.000758j
[2025-09-11 21:22:32] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -54.093410-0.002926j
[2025-09-11 21:22:47] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -54.148054-0.000342j
[2025-09-11 21:23:02] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -54.219637+0.001939j
[2025-09-11 21:23:17] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -54.213285+0.003666j
[2025-09-11 21:23:28] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -54.140808+0.000109j
[2025-09-11 21:23:38] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -54.147453+0.002263j
[2025-09-11 21:23:48] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -54.015682+0.001997j
[2025-09-11 21:23:59] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -54.005848-0.000484j
[2025-09-11 21:24:09] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -54.046796-0.000346j
[2025-09-11 21:24:19] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -54.048016-0.000392j
[2025-09-11 21:24:33] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -54.099685+0.002903j
[2025-09-11 21:24:49] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -54.114548-0.003661j
[2025-09-11 21:25:04] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -54.073765-0.002711j
[2025-09-11 21:25:20] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -53.944908+0.003037j
[2025-09-11 21:25:35] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -53.917926-0.002119j
[2025-09-11 21:25:50] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -54.117344-0.002067j
[2025-09-11 21:26:06] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -54.078414-0.000585j
[2025-09-11 21:26:06] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-11 21:26:21] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -54.043404+0.000915j
[2025-09-11 21:26:37] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -54.070724-0.002061j
[2025-09-11 21:26:52] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -54.072378-0.000360j
[2025-09-11 21:27:07] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -54.049582-0.000809j
[2025-09-11 21:27:23] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -53.945438-0.000007j
[2025-09-11 21:27:38] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -53.899506-0.002504j
[2025-09-11 21:27:54] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -53.974450+0.001340j
[2025-09-11 21:28:09] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -53.958240-0.000805j
[2025-09-11 21:28:25] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -53.946959-0.000479j
[2025-09-11 21:28:40] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -54.090389-0.000764j
[2025-09-11 21:28:56] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -54.057338+0.001698j
[2025-09-11 21:29:11] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -54.146669-0.001282j
[2025-09-11 21:29:27] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -54.107954+0.001927j
[2025-09-11 21:29:42] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -54.137966-0.000850j
[2025-09-11 21:29:57] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -54.077883-0.002874j
[2025-09-11 21:30:13] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -54.149838-0.001755j
[2025-09-11 21:30:29] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -54.193414-0.004419j
[2025-09-11 21:30:44] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -54.246004-0.002946j
[2025-09-11 21:30:59] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -54.050114-0.000704j
[2025-09-11 21:31:15] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -53.952928-0.002789j
[2025-09-11 21:31:30] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -53.995773+0.000200j
[2025-09-11 21:31:46] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -54.061344-0.001111j
[2025-09-11 21:32:01] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -53.901416+0.000910j
[2025-09-11 21:32:17] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -54.017160+0.000180j
[2025-09-11 21:32:32] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -54.011718+0.002054j
[2025-09-11 21:32:48] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -54.014070+0.001471j
[2025-09-11 21:33:03] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -54.184574+0.002674j
[2025-09-11 21:33:19] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -54.114365+0.000980j
[2025-09-11 21:33:34] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -54.084318+0.001574j
[2025-09-11 21:33:50] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -54.103840-0.001489j
[2025-09-11 21:34:03] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -54.046671+0.000360j
[2025-09-11 21:34:14] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -54.048426-0.000527j
[2025-09-11 21:34:27] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -54.159334+0.003329j
[2025-09-11 21:34:38] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -54.254713+0.000783j
[2025-09-11 21:34:54] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -54.129704-0.003262j
[2025-09-11 21:35:09] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -54.195107+0.002740j
[2025-09-11 21:35:25] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -54.226920-0.000020j
[2025-09-11 21:35:40] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -54.214651-0.000686j
[2025-09-11 21:35:56] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -54.216472-0.000050j
[2025-09-11 21:36:11] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -54.235362+0.001366j
[2025-09-11 21:36:27] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -54.139473-0.002438j
[2025-09-11 21:36:42] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -54.082697-0.000885j
[2025-09-11 21:36:57] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -54.050553+0.000451j
[2025-09-11 21:37:13] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -54.131738+0.000453j
[2025-09-11 21:37:28] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -54.005601-0.001873j
[2025-09-11 21:37:44] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -54.092105-0.002550j
[2025-09-11 21:37:59] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -54.101060-0.000744j
[2025-09-11 21:38:14] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -54.266438+0.001184j
[2025-09-11 21:38:30] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -54.157249+0.001463j
[2025-09-11 21:38:45] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -54.223157-0.004123j
[2025-09-11 21:39:01] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -54.169121-0.000728j
[2025-09-11 21:39:16] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -54.177824+0.002025j
[2025-09-11 21:39:31] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -54.263252-0.000785j
[2025-09-11 21:39:47] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -54.173103+0.000619j
[2025-09-11 21:40:02] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -54.210031-0.002527j
[2025-09-11 21:40:18] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -54.272301+0.001836j
[2025-09-11 21:40:33] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -54.116758-0.000130j
[2025-09-11 21:40:49] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -54.064008-0.001576j
[2025-09-11 21:41:04] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -54.162764-0.000783j
[2025-09-11 21:41:20] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -54.178843-0.003692j
[2025-09-11 21:41:35] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -54.092984+0.003173j
[2025-09-11 21:41:51] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -54.251835+0.001005j
[2025-09-11 21:42:06] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -54.217768+0.002269j
[2025-09-11 21:42:21] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -54.255161+0.003535j
[2025-09-11 21:42:37] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -54.207893-0.004114j
[2025-09-11 21:42:52] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -54.132658-0.001147j
[2025-09-11 21:43:08] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -54.180431+0.000891j
[2025-09-11 21:43:23] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -53.999818-0.000334j
[2025-09-11 21:43:39] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -53.933265+0.000342j
[2025-09-11 21:43:54] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -54.082701+0.001104j
[2025-09-11 21:44:09] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -54.151327+0.000424j
[2025-09-11 21:44:25] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -54.038428-0.001551j
[2025-09-11 21:44:40] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -54.041124-0.001145j
[2025-09-11 21:44:56] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -54.096443+0.002841j
[2025-09-11 21:45:11] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -54.162745-0.001196j
[2025-09-11 21:45:26] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -54.116270+0.004155j
[2025-09-11 21:45:42] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -53.995711-0.001305j
[2025-09-11 21:45:57] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -54.107351+0.001456j
[2025-09-11 21:46:13] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -54.101939-0.000581j
[2025-09-11 21:46:28] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -54.058522-0.000887j
[2025-09-11 21:46:43] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -54.156112-0.000862j
[2025-09-11 21:46:59] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -53.968152-0.002368j
[2025-09-11 21:47:14] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -54.125571+0.002013j
[2025-09-11 21:47:30] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -54.166252-0.000336j
[2025-09-11 21:47:45] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -54.196890+0.002508j
[2025-09-11 21:48:00] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -54.310916+0.001427j
[2025-09-11 21:48:16] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -54.007402-0.000287j
[2025-09-11 21:48:31] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -54.043204+0.001970j
[2025-09-11 21:48:47] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -54.157576-0.004848j
[2025-09-11 21:49:02] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -54.154705-0.000002j
[2025-09-11 21:49:17] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -54.186800-0.003699j
[2025-09-11 21:49:33] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -54.076045-0.001887j
[2025-09-11 21:49:48] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -54.171539+0.003249j
[2025-09-11 21:50:04] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -54.076465+0.001820j
[2025-09-11 21:50:19] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -54.080916+0.000893j
[2025-09-11 21:50:35] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -54.064296+0.001234j
[2025-09-11 21:50:50] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -54.119555+0.003645j
[2025-09-11 21:51:05] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -54.007106+0.001081j
[2025-09-11 21:51:21] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -53.984567+0.001474j
[2025-09-11 21:51:36] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -54.097220-0.001975j
[2025-09-11 21:51:52] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -54.194062-0.000833j
[2025-09-11 21:52:07] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -54.110115-0.000533j
[2025-09-11 21:52:22] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -54.169812-0.005721j
[2025-09-11 21:52:38] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -54.173212-0.003083j
[2025-09-11 21:52:53] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -54.094033-0.002660j
[2025-09-11 21:52:53] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-11 21:53:09] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -54.081512-0.000984j
[2025-09-11 21:53:24] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -54.202339-0.000409j
[2025-09-11 21:53:40] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -54.098075-0.000016j
[2025-09-11 21:53:55] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -53.919689+0.001150j
[2025-09-11 21:54:11] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -54.112658+0.001037j
[2025-09-11 21:54:26] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -54.129180-0.002215j
[2025-09-11 21:54:41] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -54.046398+0.002676j
[2025-09-11 21:54:57] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -53.897203-0.001569j
[2025-09-11 21:55:12] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -54.097215-0.001709j
[2025-09-11 21:55:28] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -54.161446+0.002133j
[2025-09-11 21:55:43] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -54.065576+0.003042j
[2025-09-11 21:55:59] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -54.089665-0.001309j
[2025-09-11 21:56:14] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -54.023175-0.004382j
[2025-09-11 21:56:30] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -54.129760-0.002853j
[2025-09-11 21:56:45] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -54.039664+0.001338j
[2025-09-11 21:57:00] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -54.040498-0.002200j
[2025-09-11 21:57:16] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -54.076369-0.000611j
[2025-09-11 21:57:31] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -54.092746+0.002524j
[2025-09-11 21:57:47] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -54.132894+0.001609j
[2025-09-11 21:58:02] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -54.126401+0.001705j
[2025-09-11 21:58:18] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -54.241031-0.000242j
[2025-09-11 21:58:33] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -54.120216-0.003649j
[2025-09-11 21:58:48] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -54.037497-0.000629j
[2025-09-11 21:59:04] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -53.997261+0.000208j
[2025-09-11 21:59:19] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -54.011369+0.001550j
[2025-09-11 21:59:35] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -54.061429-0.001287j
[2025-09-11 21:59:50] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -54.060432-0.000789j
[2025-09-11 22:00:06] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -54.151873+0.007717j
[2025-09-11 22:00:21] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -54.173872-0.001973j
[2025-09-11 22:00:37] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -54.151046+0.001998j
[2025-09-11 22:00:37] RESTART #2 | Period: 600
[2025-09-11 22:00:52] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -54.079499-0.001053j
[2025-09-11 22:01:07] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -54.198210-0.000744j
[2025-09-11 22:01:23] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -54.043372+0.001765j
[2025-09-11 22:01:38] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -53.992398-0.000422j
[2025-09-11 22:01:54] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -53.985314+0.002623j
[2025-09-11 22:02:09] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -54.101453+0.001955j
[2025-09-11 22:02:25] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -54.111151+0.000535j
[2025-09-11 22:02:40] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -54.185435-0.000936j
[2025-09-11 22:02:56] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -54.117572-0.001473j
[2025-09-11 22:03:11] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -54.097498+0.000056j
[2025-09-11 22:03:27] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -54.006335+0.001964j
[2025-09-11 22:03:42] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -54.131690+0.000242j
[2025-09-11 22:03:57] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -54.036492+0.001363j
[2025-09-11 22:04:13] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -54.081462+0.001579j
[2025-09-11 22:04:28] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -54.101816+0.000849j
[2025-09-11 22:04:44] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -54.126447+0.000668j
[2025-09-11 22:04:59] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -54.032817+0.001581j
[2025-09-11 22:05:15] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -54.015389+0.000337j
[2025-09-11 22:05:30] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -54.074744-0.002791j
[2025-09-11 22:05:45] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -54.005739+0.002912j
[2025-09-11 22:06:01] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -54.036878+0.001390j
[2025-09-11 22:06:16] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -54.114937-0.000844j
[2025-09-11 22:06:32] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -54.163151-0.001376j
[2025-09-11 22:06:47] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -54.185346-0.000253j
[2025-09-11 22:07:03] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -54.004950-0.002626j
[2025-09-11 22:07:18] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -54.042122-0.001883j
[2025-09-11 22:07:34] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -54.153605-0.002361j
[2025-09-11 22:07:49] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -54.194370+0.002171j
[2025-09-11 22:08:04] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -54.116307-0.001999j
[2025-09-11 22:08:20] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -54.122140-0.000915j
[2025-09-11 22:08:30] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -54.131987-0.000194j
[2025-09-11 22:08:41] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -53.970774+0.000997j
[2025-09-11 22:08:51] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -54.045619-0.000451j
[2025-09-11 22:09:01] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -54.072732-0.004734j
[2025-09-11 22:09:12] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -54.227262-0.001347j
[2025-09-11 22:09:22] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -54.116791-0.001652j
[2025-09-11 22:09:33] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -54.047636+0.002437j
[2025-09-11 22:09:48] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -54.223798+0.001729j
[2025-09-11 22:10:04] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -54.116494-0.000884j
[2025-09-11 22:10:19] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -54.122800+0.002366j
[2025-09-11 22:10:35] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -54.169972+0.002942j
[2025-09-11 22:10:50] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -54.204615-0.001090j
[2025-09-11 22:11:06] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -54.109725-0.003159j
[2025-09-11 22:11:21] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -53.962727+0.002064j
[2025-09-11 22:11:37] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -54.173272-0.003660j
[2025-09-11 22:11:52] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -54.063127-0.000997j
[2025-09-11 22:12:07] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -54.069029+0.002209j
[2025-09-11 22:12:23] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -54.173906+0.003671j
[2025-09-11 22:12:38] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -54.252192-0.003614j
[2025-09-11 22:12:54] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -54.269601+0.002323j
[2025-09-11 22:13:10] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -54.118144-0.003652j
[2025-09-11 22:13:25] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -54.081021-0.000468j
[2025-09-11 22:13:40] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -54.139180+0.000678j
[2025-09-11 22:13:56] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -54.131734+0.002421j
[2025-09-11 22:14:11] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -54.140296+0.004414j
[2025-09-11 22:14:27] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -54.117485-0.000515j
[2025-09-11 22:14:42] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -54.098405+0.003834j
[2025-09-11 22:14:58] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -54.061474+0.001665j
[2025-09-11 22:15:13] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -54.048230+0.000878j
[2025-09-11 22:15:28] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -54.035219-0.001081j
[2025-09-11 22:15:44] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -54.084059-0.004466j
[2025-09-11 22:15:59] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -54.040041+0.003941j
[2025-09-11 22:16:15] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -54.077118-0.002531j
[2025-09-11 22:16:30] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -54.141849-0.001530j
[2025-09-11 22:16:45] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -54.020988+0.004945j
[2025-09-11 22:17:01] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -53.942538-0.001654j
[2025-09-11 22:17:16] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -53.926204+0.000816j
[2025-09-11 22:17:32] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -54.036310-0.004910j
[2025-09-11 22:17:47] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -53.915199-0.001777j
[2025-09-11 22:18:02] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -53.977093+0.000590j
[2025-09-11 22:18:18] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -53.849597-0.004231j
[2025-09-11 22:18:33] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -54.077679-0.001201j
[2025-09-11 22:18:49] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -53.988544-0.001547j
[2025-09-11 22:19:04] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -54.067254+0.002003j
[2025-09-11 22:19:15] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -54.107736-0.001253j
[2025-09-11 22:19:15] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-11 22:19:28] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -54.111532+0.000016j
[2025-09-11 22:19:40] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -53.921736+0.000146j
[2025-09-11 22:19:52] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -53.959014+0.000626j
[2025-09-11 22:20:08] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -53.903257-0.002017j
[2025-09-11 22:20:23] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -54.040668+0.001444j
[2025-09-11 22:20:39] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -54.117366-0.002176j
[2025-09-11 22:20:54] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -54.087560+0.001744j
[2025-09-11 22:21:09] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -54.115171-0.004450j
[2025-09-11 22:21:25] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -54.176140+0.002498j
[2025-09-11 22:21:40] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -54.203028-0.001437j
[2025-09-11 22:21:55] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -54.192917-0.002651j
[2025-09-11 22:22:11] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -54.233387-0.001486j
[2025-09-11 22:22:26] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -54.287209-0.000302j
[2025-09-11 22:22:42] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -54.235128+0.001617j
[2025-09-11 22:22:57] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -54.172472-0.001680j
[2025-09-11 22:23:12] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -54.264583-0.002441j
[2025-09-11 22:23:28] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -54.160765-0.000527j
[2025-09-11 22:23:43] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -54.173998+0.001083j
[2025-09-11 22:23:58] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -54.165393-0.002438j
[2025-09-11 22:24:14] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -54.044479-0.003976j
[2025-09-11 22:24:29] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -54.133753+0.000089j
[2025-09-11 22:24:45] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -54.131280+0.002590j
[2025-09-11 22:25:00] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -54.222054-0.002393j
[2025-09-11 22:25:15] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -54.147479-0.000916j
[2025-09-11 22:25:31] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -54.158094-0.002220j
[2025-09-11 22:25:46] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -54.063124+0.000342j
[2025-09-11 22:26:02] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -54.070550+0.001061j
[2025-09-11 22:26:17] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -53.973055-0.003063j
[2025-09-11 22:26:32] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -54.019558-0.002108j
[2025-09-11 22:26:48] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -54.048112+0.002535j
[2025-09-11 22:27:03] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -54.007947+0.000529j
[2025-09-11 22:27:18] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -54.071914+0.001233j
[2025-09-11 22:27:34] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -54.063658-0.000012j
[2025-09-11 22:27:49] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -54.104403-0.000488j
[2025-09-11 22:28:05] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -53.986195+0.000224j
[2025-09-11 22:28:20] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -54.034812-0.000351j
[2025-09-11 22:28:35] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -54.172958+0.002144j
[2025-09-11 22:28:51] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -53.963434-0.000416j
[2025-09-11 22:29:06] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -54.114793+0.002534j
[2025-09-11 22:29:21] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -54.104027-0.002578j
[2025-09-11 22:29:37] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -54.156501+0.000711j
[2025-09-11 22:29:52] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -53.960393-0.004950j
[2025-09-11 22:30:07] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -53.970191+0.001487j
[2025-09-11 22:30:23] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -54.045014+0.002329j
[2025-09-11 22:30:38] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -54.070220-0.000081j
[2025-09-11 22:30:54] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -53.969926+0.001050j
[2025-09-11 22:31:09] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -53.993191+0.001767j
[2025-09-11 22:31:24] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -54.161239+0.000819j
[2025-09-11 22:31:40] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -53.946306+0.000964j
[2025-09-11 22:31:55] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -53.901352+0.003445j
[2025-09-11 22:32:10] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -53.948855+0.000500j
[2025-09-11 22:32:26] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -54.062798-0.002907j
[2025-09-11 22:32:41] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -54.001091-0.001124j
[2025-09-11 22:32:57] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -54.006499-0.000768j
[2025-09-11 22:33:12] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -54.077241-0.003149j
[2025-09-11 22:33:27] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -53.993624+0.002713j
[2025-09-11 22:33:43] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -54.012522-0.000503j
[2025-09-11 22:33:58] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -53.989839+0.001757j
[2025-09-11 22:34:13] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -54.054517-0.001018j
[2025-09-11 22:34:29] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -54.108547-0.003426j
[2025-09-11 22:34:44] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -54.228650+0.000913j
[2025-09-11 22:35:00] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -54.112799-0.002145j
[2025-09-11 22:35:15] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -54.023426-0.002138j
[2025-09-11 22:35:30] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -54.059302+0.001568j
[2025-09-11 22:35:46] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -54.179099-0.004209j
[2025-09-11 22:36:01] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -53.966980-0.000276j
[2025-09-11 22:36:16] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -54.112537+0.002424j
[2025-09-11 22:36:32] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -54.105256-0.000551j
[2025-09-11 22:36:47] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -54.105650+0.001407j
[2025-09-11 22:37:03] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -54.123136+0.001936j
[2025-09-11 22:37:18] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -54.051383-0.002181j
[2025-09-11 22:37:33] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -54.019896-0.001041j
[2025-09-11 22:37:49] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -53.992686+0.005442j
[2025-09-11 22:38:04] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -53.912339-0.001925j
[2025-09-11 22:38:19] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -54.016630-0.000325j
[2025-09-11 22:38:35] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -54.055976-0.001108j
[2025-09-11 22:38:50] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -53.995102+0.003766j
[2025-09-11 22:39:05] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -54.043397-0.000646j
[2025-09-11 22:39:21] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -54.068114+0.000338j
[2025-09-11 22:39:36] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -54.133944-0.000427j
[2025-09-11 22:39:51] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -54.140361-0.001542j
[2025-09-11 22:40:07] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -54.054239-0.000595j
[2025-09-11 22:40:22] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -53.936620+0.000019j
[2025-09-11 22:40:37] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -53.969972-0.001323j
[2025-09-11 22:40:53] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -54.101900+0.000115j
[2025-09-11 22:41:08] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -54.081275-0.001065j
[2025-09-11 22:41:24] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -54.200528+0.000599j
[2025-09-11 22:41:39] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -54.096479-0.000275j
[2025-09-11 22:41:54] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -54.089058+0.001787j
[2025-09-11 22:42:10] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -54.057439+0.000105j
[2025-09-11 22:42:25] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -53.957936-0.000180j
[2025-09-11 22:42:40] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -54.074565-0.002109j
[2025-09-11 22:42:56] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -54.106625-0.000177j
[2025-09-11 22:43:11] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -54.169118-0.004977j
[2025-09-11 22:43:27] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -54.084662+0.002121j
[2025-09-11 22:43:42] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -54.102342+0.001946j
[2025-09-11 22:43:57] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -54.119091+0.000675j
[2025-09-11 22:44:13] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -54.087718+0.001947j
[2025-09-11 22:44:28] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -54.008279-0.001122j
[2025-09-11 22:44:43] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -53.974383-0.002025j
[2025-09-11 22:44:59] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -54.135056+0.002057j
[2025-09-11 22:45:14] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -54.079230-0.001256j
[2025-09-11 22:45:29] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -54.092798-0.000869j
[2025-09-11 22:45:45] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -54.207891+0.001961j
[2025-09-11 22:46:00] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -54.118324-0.000099j
[2025-09-11 22:46:00] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-11 22:46:16] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -54.081777+0.003428j
[2025-09-11 22:46:31] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -54.184875-0.000391j
[2025-09-11 22:46:46] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -54.269134+0.000875j
[2025-09-11 22:47:02] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -54.217706-0.004375j
[2025-09-11 22:47:17] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -54.156211+0.000239j
[2025-09-11 22:47:32] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -54.086566+0.001192j
[2025-09-11 22:47:48] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -54.054363-0.001870j
[2025-09-11 22:48:03] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -54.030156-0.002203j
[2025-09-11 22:48:19] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -54.046938-0.002512j
[2025-09-11 22:48:34] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -54.070136-0.003124j
[2025-09-11 22:48:49] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -54.166009+0.001369j
[2025-09-11 22:49:05] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -53.996065-0.002868j
[2025-09-11 22:49:20] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -53.977337+0.002245j
[2025-09-11 22:49:36] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -53.986296+0.000199j
[2025-09-11 22:49:51] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -53.984106-0.000230j
[2025-09-11 22:50:07] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -53.907997-0.002107j
[2025-09-11 22:50:22] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -54.088553+0.000337j
[2025-09-11 22:50:38] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -54.167422-0.001538j
[2025-09-11 22:50:53] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -54.040854-0.001513j
[2025-09-11 22:51:08] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -54.024997-0.002677j
[2025-09-11 22:51:24] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -53.967933+0.001556j
[2025-09-11 22:51:39] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -54.241159+0.001364j
[2025-09-11 22:51:55] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -53.937289+0.002485j
[2025-09-11 22:52:10] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -54.075284+0.001865j
[2025-09-11 22:52:26] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -54.155639-0.000826j
[2025-09-11 22:52:41] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -54.141035+0.001114j
[2025-09-11 22:52:57] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -54.159593+0.003061j
[2025-09-11 22:53:12] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -54.201478+0.003612j
[2025-09-11 22:53:27] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -54.134732-0.004421j
[2025-09-11 22:53:42] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -54.059624+0.003925j
[2025-09-11 22:53:53] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -54.167829+0.002529j
[2025-09-11 22:54:03] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -54.045788+0.000492j
[2025-09-11 22:54:13] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -54.101966+0.000534j
[2025-09-11 22:54:24] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -54.132294-0.001093j
[2025-09-11 22:54:34] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -54.070830+0.001341j
[2025-09-11 22:54:44] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -54.156486-0.001790j
[2025-09-11 22:54:57] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -54.105534-0.003618j
[2025-09-11 22:55:13] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -54.082249+0.000128j
[2025-09-11 22:55:28] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -54.213093+0.001939j
[2025-09-11 22:55:44] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -54.087314-0.000864j
[2025-09-11 22:55:59] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -54.172214+0.001495j
[2025-09-11 22:56:15] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -54.045355+0.000059j
[2025-09-11 22:56:30] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -54.221457+0.001966j
[2025-09-11 22:56:46] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -54.076926-0.000256j
[2025-09-11 22:57:01] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -54.029612-0.000712j
[2025-09-11 22:57:17] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -54.079277-0.001000j
[2025-09-11 22:57:32] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -54.094869-0.001323j
[2025-09-11 22:57:48] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -54.076567+0.001298j
[2025-09-11 22:58:03] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -54.079750+0.001153j
[2025-09-11 22:58:19] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -54.181888-0.000808j
[2025-09-11 22:58:34] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -54.158802-0.000705j
[2025-09-11 22:58:50] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -54.144207+0.001366j
[2025-09-11 22:59:05] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -54.000272+0.000880j
[2025-09-11 22:59:21] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -54.112937+0.000171j
[2025-09-11 22:59:36] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -54.111403-0.003010j
[2025-09-11 22:59:52] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -54.214070+0.000594j
[2025-09-11 23:00:07] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -54.209552+0.000191j
[2025-09-11 23:00:23] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -54.222199+0.001559j
[2025-09-11 23:00:38] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -54.298093-0.001451j
[2025-09-11 23:00:54] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -54.121180-0.001710j
[2025-09-11 23:01:09] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -54.169979-0.001903j
[2025-09-11 23:01:24] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -54.136265+0.000490j
[2025-09-11 23:01:40] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -54.052226-0.000167j
[2025-09-11 23:01:55] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -54.110648+0.001033j
[2025-09-11 23:02:11] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -54.201171+0.001862j
[2025-09-11 23:02:26] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -54.125224-0.000398j
[2025-09-11 23:02:42] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -54.174881+0.000716j
[2025-09-11 23:02:57] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -54.178560-0.000999j
[2025-09-11 23:03:13] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -54.143628-0.000453j
[2025-09-11 23:03:28] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -54.188306-0.003104j
[2025-09-11 23:03:44] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -53.932047+0.005274j
[2025-09-11 23:03:59] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -54.076795-0.002357j
[2025-09-11 23:04:15] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -54.078689-0.001865j
[2025-09-11 23:04:26] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -54.156938-0.000953j
[2025-09-11 23:04:39] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -54.010796+0.003607j
[2025-09-11 23:04:51] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -53.974454-0.001407j
[2025-09-11 23:05:03] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -53.969157-0.001705j
[2025-09-11 23:05:19] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -53.976888+0.000924j
[2025-09-11 23:05:34] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -54.044853+0.002487j
[2025-09-11 23:05:50] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -54.248169+0.005060j
[2025-09-11 23:06:05] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -54.141162+0.004762j
[2025-09-11 23:06:21] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -54.071762+0.001840j
[2025-09-11 23:06:36] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -54.104623+0.000853j
[2025-09-11 23:06:51] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -54.116903+0.001921j
[2025-09-11 23:07:07] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -54.131169+0.000900j
[2025-09-11 23:07:22] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -54.162774+0.000941j
[2025-09-11 23:07:38] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -54.114123-0.001081j
[2025-09-11 23:07:53] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -53.923182-0.000533j
[2025-09-11 23:08:08] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -54.123217+0.000180j
[2025-09-11 23:08:24] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -54.082967+0.000110j
[2025-09-11 23:08:39] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -54.218428+0.000647j
[2025-09-11 23:08:54] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -54.181130-0.000932j
[2025-09-11 23:09:10] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -54.136803-0.003350j
[2025-09-11 23:09:25] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -54.239739+0.002085j
[2025-09-11 23:09:41] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -54.161723+0.000509j
[2025-09-11 23:09:56] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -54.100420+0.002748j
[2025-09-11 23:10:11] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -54.058015+0.001084j
[2025-09-11 23:10:27] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -54.135350+0.000653j
[2025-09-11 23:10:42] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -54.138498+0.001002j
[2025-09-11 23:10:57] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -54.187420-0.004843j
[2025-09-11 23:11:13] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -54.208925-0.002441j
[2025-09-11 23:11:28] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -54.205903-0.000921j
[2025-09-11 23:11:44] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -54.176674-0.001808j
[2025-09-11 23:11:59] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -54.113384-0.001432j
[2025-09-11 23:12:14] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -54.037352-0.000381j
[2025-09-11 23:12:14] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-11 23:12:30] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -54.124742-0.001961j
[2025-09-11 23:12:45] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -54.083723-0.000530j
[2025-09-11 23:13:00] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -54.185326+0.001164j
[2025-09-11 23:13:16] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -54.116230-0.003807j
[2025-09-11 23:13:31] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -54.025491+0.002959j
[2025-09-11 23:13:47] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -54.062037-0.002294j
[2025-09-11 23:14:02] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -54.080449+0.000528j
[2025-09-11 23:14:17] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -54.140891+0.000969j
[2025-09-11 23:14:33] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -54.137868-0.002032j
[2025-09-11 23:14:48] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -54.087221+0.003275j
[2025-09-11 23:15:03] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -54.076459+0.000310j
[2025-09-11 23:15:19] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -54.030603+0.003246j
[2025-09-11 23:15:34] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -54.102025+0.000180j
[2025-09-11 23:15:49] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -54.084026-0.001027j
[2025-09-11 23:16:05] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -54.112188-0.004365j
[2025-09-11 23:16:20] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -54.178200+0.001991j
[2025-09-11 23:16:36] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -54.130013-0.001619j
[2025-09-11 23:16:51] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -54.095358-0.000781j
[2025-09-11 23:17:06] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -54.110065-0.001764j
[2025-09-11 23:17:22] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -54.073040-0.002963j
[2025-09-11 23:17:37] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -54.060463-0.000737j
[2025-09-11 23:17:52] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -54.150294-0.000974j
[2025-09-11 23:18:08] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -53.918430+0.000302j
[2025-09-11 23:18:23] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -54.109714-0.001560j
[2025-09-11 23:18:39] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -54.191357+0.003810j
[2025-09-11 23:18:54] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -54.146574+0.001015j
[2025-09-11 23:19:09] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -54.058388+0.000946j
[2025-09-11 23:19:25] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -54.226593+0.002802j
[2025-09-11 23:19:40] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -53.985754+0.002541j
[2025-09-11 23:19:55] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -54.135054-0.000350j
[2025-09-11 23:20:11] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -54.000061-0.000918j
[2025-09-11 23:20:26] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -54.117881+0.000602j
[2025-09-11 23:20:41] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -54.173495-0.000379j
[2025-09-11 23:20:57] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -53.957202+0.000096j
[2025-09-11 23:21:12] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -54.034644-0.000196j
[2025-09-11 23:21:28] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -54.004509+0.002096j
[2025-09-11 23:21:43] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -54.093997+0.001521j
[2025-09-11 23:21:58] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -54.146247+0.001106j
[2025-09-11 23:22:14] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -54.080345+0.000367j
[2025-09-11 23:22:29] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -54.118381-0.000899j
[2025-09-11 23:22:44] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -54.016394-0.002371j
[2025-09-11 23:23:00] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -54.171175-0.000006j
[2025-09-11 23:23:15] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -54.048233-0.001035j
[2025-09-11 23:23:31] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -54.115887-0.002172j
[2025-09-11 23:23:46] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -54.109947-0.002866j
[2025-09-11 23:24:01] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -54.139520-0.001290j
[2025-09-11 23:24:17] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -54.036021-0.001902j
[2025-09-11 23:24:32] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -54.068690-0.001183j
[2025-09-11 23:24:48] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -53.995797+0.000488j
[2025-09-11 23:25:03] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -53.870387-0.000089j
[2025-09-11 23:25:18] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -53.951726+0.002047j
[2025-09-11 23:25:34] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -54.090817-0.002693j
[2025-09-11 23:25:49] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -54.241464-0.001906j
[2025-09-11 23:26:05] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -54.131412+0.000047j
[2025-09-11 23:26:20] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -54.254098+0.000243j
[2025-09-11 23:26:35] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -53.989428-0.001245j
[2025-09-11 23:26:51] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -53.983544-0.002066j
[2025-09-11 23:27:06] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -54.147774+0.000832j
[2025-09-11 23:27:22] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -54.055014+0.002243j
[2025-09-11 23:27:37] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -54.143812+0.000778j
[2025-09-11 23:27:53] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -54.239359-0.003772j
[2025-09-11 23:28:08] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -54.112546-0.002196j
[2025-09-11 23:28:23] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -54.189447+0.001030j
[2025-09-11 23:28:39] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -54.095745-0.001397j
[2025-09-11 23:28:54] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -54.192471-0.000799j
[2025-09-11 23:29:10] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -54.187647-0.001359j
[2025-09-11 23:29:25] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -54.207776+0.001415j
[2025-09-11 23:29:40] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -54.057258-0.000296j
[2025-09-11 23:29:56] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -54.045373+0.002151j
[2025-09-11 23:30:11] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -53.996433+0.000092j
[2025-09-11 23:30:27] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -54.081343+0.000279j
[2025-09-11 23:30:42] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -54.166011+0.001003j
[2025-09-11 23:30:57] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -54.109994-0.002335j
[2025-09-11 23:31:13] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -54.106563+0.001474j
[2025-09-11 23:31:28] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -54.065528-0.000606j
[2025-09-11 23:31:44] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -53.973656+0.000253j
[2025-09-11 23:31:59] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -54.104943-0.002544j
[2025-09-11 23:32:14] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -54.073313-0.001197j
[2025-09-11 23:32:30] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -54.097602-0.001451j
[2025-09-11 23:32:45] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -54.127665+0.000227j
[2025-09-11 23:33:01] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -54.097293-0.000887j
[2025-09-11 23:33:16] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -54.038266+0.001134j
[2025-09-11 23:33:31] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -53.973530-0.000124j
[2025-09-11 23:33:47] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -53.954197+0.002894j
[2025-09-11 23:34:02] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -54.047909+0.000630j
[2025-09-11 23:34:18] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -54.040116-0.000599j
[2025-09-11 23:34:33] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -54.118464-0.001062j
[2025-09-11 23:34:48] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -54.195096-0.001214j
[2025-09-11 23:35:04] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -54.055350+0.000017j
[2025-09-11 23:35:19] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -54.002990+0.001652j
[2025-09-11 23:35:35] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -53.996370+0.004139j
[2025-09-11 23:35:50] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -53.967978-0.002469j
[2025-09-11 23:36:06] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -53.960295+0.000103j
[2025-09-11 23:36:21] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -54.135414+0.000733j
[2025-09-11 23:36:36] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -54.077740-0.000093j
[2025-09-11 23:36:52] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -53.951103-0.001946j
[2025-09-11 23:37:07] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -54.049292+0.000406j
[2025-09-11 23:37:23] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -54.068945+0.000072j
[2025-09-11 23:37:38] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -54.102772+0.001981j
[2025-09-11 23:37:53] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -54.047130+0.002591j
[2025-09-11 23:38:09] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -53.982887-0.000840j
[2025-09-11 23:38:22] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -54.159544+0.001776j
[2025-09-11 23:38:33] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -54.049954+0.000863j
[2025-09-11 23:38:43] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -54.074549+0.003127j
[2025-09-11 23:38:53] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -54.063077+0.000002j
[2025-09-11 23:38:53] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-11 23:39:04] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -54.025157-0.000959j
[2025-09-11 23:39:14] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -54.132386-0.000232j
[2025-09-11 23:39:24] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -54.131117-0.000440j
[2025-09-11 23:39:39] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -54.143122+0.000920j
[2025-09-11 23:39:54] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -54.123464-0.004046j
[2025-09-11 23:40:10] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -54.187071+0.001107j
[2025-09-11 23:40:25] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -54.061238+0.001344j
[2025-09-11 23:40:41] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -54.227949+0.000143j
[2025-09-11 23:40:56] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -54.103419+0.000500j
[2025-09-11 23:41:12] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -54.169364-0.000711j
[2025-09-11 23:41:27] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -54.256366+0.001328j
[2025-09-11 23:41:42] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -54.193816+0.002854j
[2025-09-11 23:41:58] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -54.200014+0.000104j
[2025-09-11 23:42:14] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -54.139963+0.000898j
[2025-09-11 23:42:29] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -54.095983-0.002551j
[2025-09-11 23:42:44] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -54.071522-0.002781j
[2025-09-11 23:43:00] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -54.014813+0.000645j
[2025-09-11 23:43:15] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -54.031538+0.001373j
[2025-09-11 23:43:31] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -54.093544-0.003569j
[2025-09-11 23:43:46] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -54.169706-0.000556j
[2025-09-11 23:44:02] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -54.118108+0.003367j
[2025-09-11 23:44:17] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -54.058873+0.000067j
[2025-09-11 23:44:33] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -54.132546-0.002341j
[2025-09-11 23:44:48] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -54.122749-0.001262j
[2025-09-11 23:45:03] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -54.178953+0.001824j
[2025-09-11 23:45:19] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -54.116475+0.000346j
[2025-09-11 23:45:34] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -54.138594+0.000920j
[2025-09-11 23:45:50] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -54.238123-0.003144j
[2025-09-11 23:46:05] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -54.137059+0.001012j
[2025-09-11 23:46:21] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -54.120839-0.003842j
[2025-09-11 23:46:36] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -54.087553-0.002594j
[2025-09-11 23:46:52] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -54.068559+0.000142j
[2025-09-11 23:47:07] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -54.182832+0.001052j
[2025-09-11 23:47:22] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -54.089585-0.001404j
[2025-09-11 23:47:38] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -54.046855-0.000610j
[2025-09-11 23:47:53] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -54.096361-0.003263j
[2025-09-11 23:48:09] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -54.158793+0.000015j
[2025-09-11 23:48:24] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -54.137643-0.002613j
[2025-09-11 23:48:40] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -54.215670+0.003735j
[2025-09-11 23:48:55] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -54.279549+0.000820j
[2025-09-11 23:49:07] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -54.239631+0.002659j
[2025-09-11 23:49:19] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -54.184304+0.002048j
[2025-09-11 23:49:31] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -54.142671+0.000593j
[2025-09-11 23:49:44] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -54.178622-0.001362j
[2025-09-11 23:49:59] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -54.156543-0.000075j
[2025-09-11 23:50:15] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -54.215614+0.002316j
[2025-09-11 23:50:30] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -54.032871-0.000246j
[2025-09-11 23:50:46] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -54.109548+0.002955j
[2025-09-11 23:51:01] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -54.128903+0.000418j
[2025-09-11 23:51:16] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -53.886279-0.001037j
[2025-09-11 23:51:32] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -53.911492-0.003277j
[2025-09-11 23:51:47] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -53.930609-0.003131j
[2025-09-11 23:52:03] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -53.894459+0.002858j
[2025-09-11 23:52:18] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -53.911196-0.003587j
[2025-09-11 23:52:33] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -53.879685-0.001334j
[2025-09-11 23:52:49] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -54.168375+0.001959j
[2025-09-11 23:53:04] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -54.100069+0.001130j
[2025-09-11 23:53:19] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -53.969341-0.005485j
[2025-09-11 23:53:35] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -54.017110-0.000527j
[2025-09-11 23:53:50] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -53.973214-0.000245j
[2025-09-11 23:54:06] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -53.907189-0.000649j
[2025-09-11 23:54:21] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -54.132433+0.004760j
[2025-09-11 23:54:36] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -54.077021+0.002142j
[2025-09-11 23:54:52] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -54.067379+0.001515j
[2025-09-11 23:55:07] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -54.095052+0.000681j
[2025-09-11 23:55:22] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -54.092867-0.000189j
[2025-09-11 23:55:38] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -54.066214-0.000283j
[2025-09-11 23:55:53] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -54.029717-0.003101j
[2025-09-11 23:56:09] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -53.871708-0.001107j
[2025-09-11 23:56:24] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -54.078285+0.000530j
[2025-09-11 23:56:39] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -54.083113+0.003553j
[2025-09-11 23:56:55] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -54.019851-0.001493j
[2025-09-11 23:57:10] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -53.937339-0.000729j
[2025-09-11 23:57:26] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -53.961694-0.001034j
[2025-09-11 23:57:41] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -54.093236+0.000528j
[2025-09-11 23:57:57] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -53.927900-0.001351j
[2025-09-11 23:58:12] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -53.931403+0.001233j
[2025-09-11 23:58:27] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -54.009085-0.001828j
[2025-09-11 23:58:43] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -54.078585-0.002286j
[2025-09-11 23:58:58] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -54.010536+0.000245j
[2025-09-11 23:59:14] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -53.972334-0.003169j
[2025-09-11 23:59:29] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -54.046194+0.000506j
[2025-09-11 23:59:44] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -53.993184-0.001987j
[2025-09-12 00:00:00] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -54.105222-0.001543j
[2025-09-12 00:00:15] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -54.094665+0.000288j
[2025-09-12 00:00:31] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -54.108660+0.001650j
[2025-09-12 00:00:46] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -54.065608+0.001576j
[2025-09-12 00:01:02] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -53.974311+0.000489j
[2025-09-12 00:01:17] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -54.007008-0.000965j
[2025-09-12 00:01:32] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -53.992110+0.003233j
[2025-09-12 00:01:48] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -54.000131+0.000601j
[2025-09-12 00:02:03] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -53.898165-0.002789j
[2025-09-12 00:02:19] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -54.036194-0.003170j
[2025-09-12 00:02:34] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -54.045219+0.000693j
[2025-09-12 00:02:50] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -54.050709+0.000756j
[2025-09-12 00:03:05] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -53.970574-0.000842j
[2025-09-12 00:03:21] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -53.964559+0.001777j
[2025-09-12 00:03:36] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -54.005349+0.001693j
[2025-09-12 00:03:51] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -54.086650-0.000510j
[2025-09-12 00:04:07] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -54.119044-0.003130j
[2025-09-12 00:04:22] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -54.142101-0.000531j
[2025-09-12 00:04:38] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -54.033912-0.000277j
[2025-09-12 00:04:53] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -54.052054-0.000380j
[2025-09-12 00:05:09] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -54.125853-0.001780j
[2025-09-12 00:05:24] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -54.109082-0.001208j
[2025-09-12 00:05:24] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-12 00:05:39] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -54.072901-0.000744j
[2025-09-12 00:05:55] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -54.086508+0.002157j
[2025-09-12 00:06:10] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -54.166158+0.001417j
[2025-09-12 00:06:25] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -54.201278+0.001126j
[2025-09-12 00:06:41] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -54.142786+0.000262j
[2025-09-12 00:06:56] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -54.062547+0.000749j
[2025-09-12 00:07:12] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -54.115451-0.000643j
[2025-09-12 00:07:27] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -54.102418+0.000765j
[2025-09-12 00:07:43] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -53.971841+0.004601j
[2025-09-12 00:07:58] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -54.041040-0.001228j
[2025-09-12 00:08:13] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -54.175305+0.000493j
[2025-09-12 00:08:29] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -54.155417-0.003157j
[2025-09-12 00:08:44] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -54.223528+0.001429j
[2025-09-12 00:09:00] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -54.177177-0.003433j
[2025-09-12 00:09:15] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -54.092420-0.001201j
[2025-09-12 00:09:31] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -54.006944-0.001331j
[2025-09-12 00:09:46] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -54.139326-0.001322j
[2025-09-12 00:10:01] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -54.171118+0.000767j
[2025-09-12 00:10:17] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -54.063882+0.002959j
[2025-09-12 00:10:32] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -54.080516-0.001552j
[2025-09-12 00:10:48] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -54.060873-0.000988j
[2025-09-12 00:11:03] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -54.190079+0.000810j
[2025-09-12 00:11:19] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -54.189181+0.000739j
[2025-09-12 00:11:34] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -54.156940+0.000183j
[2025-09-12 00:11:49] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -54.157902-0.000871j
[2025-09-12 00:12:05] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -54.001954+0.001371j
[2025-09-12 00:12:20] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -54.125758-0.002826j
[2025-09-12 00:12:36] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -54.052756-0.000841j
[2025-09-12 00:12:51] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -54.048663+0.001807j
[2025-09-12 00:13:07] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -54.218664-0.000576j
[2025-09-12 00:13:22] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -54.071969+0.001430j
[2025-09-12 00:13:37] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -54.197373-0.001374j
[2025-09-12 00:13:53] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -54.044357+0.001752j
[2025-09-12 00:14:08] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -53.990879+0.000558j
[2025-09-12 00:14:24] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -54.072135+0.000290j
[2025-09-12 00:14:39] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -54.066188-0.000271j
[2025-09-12 00:14:54] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -53.920602-0.000626j
[2025-09-12 00:15:10] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -54.078774-0.002136j
[2025-09-12 00:15:25] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -53.999622+0.000464j
[2025-09-12 00:15:41] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -54.064265-0.000140j
[2025-09-12 00:15:56] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -54.107045-0.002411j
[2025-09-12 00:16:12] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -54.015705+0.000254j
[2025-09-12 00:16:27] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -53.909810+0.003187j
[2025-09-12 00:16:43] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -54.072224+0.002256j
[2025-09-12 00:16:58] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -54.043666-0.001758j
[2025-09-12 00:17:13] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -54.057162-0.000764j
[2025-09-12 00:17:29] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -54.148056+0.000142j
[2025-09-12 00:17:44] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -53.929245+0.003711j
[2025-09-12 00:18:00] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -53.992583-0.001599j
[2025-09-12 00:18:15] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -54.020896+0.002297j
[2025-09-12 00:18:31] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -53.883628-0.000053j
[2025-09-12 00:18:46] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -53.922136-0.000950j
[2025-09-12 00:19:01] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -53.971927-0.001538j
[2025-09-12 00:19:17] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -54.032093+0.001632j
[2025-09-12 00:19:32] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -54.030514+0.001391j
[2025-09-12 00:19:48] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -54.134262-0.000131j
[2025-09-12 00:20:03] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -54.178563-0.003143j
[2025-09-12 00:20:19] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -54.055075-0.001518j
[2025-09-12 00:20:34] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -54.069651-0.000246j
[2025-09-12 00:20:49] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -54.061880+0.002872j
[2025-09-12 00:21:05] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -54.167339-0.000907j
[2025-09-12 00:21:20] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -54.006872+0.000298j
[2025-09-12 00:21:36] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -54.099959-0.000581j
[2025-09-12 00:21:51] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -54.002647+0.001017j
[2025-09-12 00:22:07] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -53.820413+0.000427j
[2025-09-12 00:22:22] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -53.864050+0.003066j
[2025-09-12 00:22:38] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -53.976797-0.001510j
[2025-09-12 00:22:53] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -54.066496+0.000841j
[2025-09-12 00:23:08] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -54.107115+0.002733j
[2025-09-12 00:23:24] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -54.116904-0.001826j
[2025-09-12 00:23:36] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -54.095502-0.000591j
[2025-09-12 00:23:46] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -54.032465+0.001766j
[2025-09-12 00:23:56] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -54.061824-0.002687j
[2025-09-12 00:24:07] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -53.996339+0.001479j
[2025-09-12 00:24:17] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -54.029463-0.001531j
[2025-09-12 00:24:27] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -54.139311-0.002635j
[2025-09-12 00:24:37] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -54.066984-0.003223j
[2025-09-12 00:24:52] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -54.023685+0.000831j
[2025-09-12 00:25:07] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -54.041120+0.000365j
[2025-09-12 00:25:23] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -53.946903-0.001008j
[2025-09-12 00:25:38] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -53.984811+0.000330j
[2025-09-12 00:25:54] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -54.052151+0.005530j
[2025-09-12 00:26:09] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -54.098103+0.000192j
[2025-09-12 00:26:25] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -54.085725-0.001626j
[2025-09-12 00:26:40] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -54.188046+0.002174j
[2025-09-12 00:26:56] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -54.158106-0.003982j
[2025-09-12 00:27:11] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -54.173905-0.002364j
[2025-09-12 00:27:27] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -54.165865-0.002965j
[2025-09-12 00:27:42] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -54.248845-0.002100j
[2025-09-12 00:27:58] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -54.096327-0.000597j
[2025-09-12 00:28:13] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -54.101022-0.003655j
[2025-09-12 00:28:29] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -54.117169-0.001174j
[2025-09-12 00:28:44] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -54.016034+0.000815j
[2025-09-12 00:28:59] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -53.926594+0.001014j
[2025-09-12 00:29:15] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -54.072609+0.001811j
[2025-09-12 00:29:30] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -54.031629-0.003777j
[2025-09-12 00:29:46] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -54.042317+0.000265j
[2025-09-12 00:30:01] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -54.034516-0.002277j
[2025-09-12 00:30:17] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -54.044393+0.002586j
[2025-09-12 00:30:32] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -54.211288+0.002327j
[2025-09-12 00:30:47] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -53.988924+0.003389j
[2025-09-12 00:31:02] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -54.103996-0.005094j
[2025-09-12 00:31:13] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -54.058371-0.002934j
[2025-09-12 00:31:23] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -54.050623+0.000125j
[2025-09-12 00:31:33] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -54.082578-0.001117j
[2025-09-12 00:31:33] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-12 00:31:33] ✅ Training completed | Restarts: 2
[2025-09-12 00:31:33] ============================================================
[2025-09-12 00:31:33] Training completed | Runtime: 16056.5s
[2025-09-12 00:31:37] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-12 00:31:37] ============================================================
