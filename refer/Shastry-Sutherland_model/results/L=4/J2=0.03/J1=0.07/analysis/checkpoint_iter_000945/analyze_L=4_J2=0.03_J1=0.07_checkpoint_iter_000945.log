[2025-09-13 11:56:49] 使用checkpoint文件: results/L=4/J2=0.03/J1=0.07/training/checkpoints/checkpoint_iter_000945.pkl
[2025-09-13 11:57:06] ✓ 从checkpoint加载参数: 945
[2025-09-13 11:57:06]   - 能量: -54.109082-0.001208j ± 0.080960
[2025-09-13 11:57:06] ================================================================================
[2025-09-13 11:57:06] 加载量子态: L=4, J2=0.03, J1=0.07, checkpoint=checkpoint_iter_000945
[2025-09-13 11:57:06] 使用采样数目: 1048576
[2025-09-13 11:57:06] 设置样本数为: 1048576
[2025-09-13 11:57:06] 开始生成共享样本集...
[2025-09-13 12:00:07] 样本生成完成,耗时: 181.679 秒
[2025-09-13 12:00:07] ================================================================================
[2025-09-13 12:00:07] 开始计算自旋结构因子...
[2025-09-13 12:00:07] 初始化操作符缓存...
[2025-09-13 12:00:07] 预构建所有自旋相关操作符...
[2025-09-13 12:00:08] 开始计算自旋相关函数...
[2025-09-13 12:00:22] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 14.257s
[2025-09-13 12:00:40] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 17.730s
[2025-09-13 12:00:49] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.254s
[2025-09-13 12:00:58] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.333s
[2025-09-13 12:01:08] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.334s
[2025-09-13 12:01:17] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.329s
[2025-09-13 12:01:26] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.315s
[2025-09-13 12:01:36] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.314s
[2025-09-13 12:01:45] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.318s
[2025-09-13 12:01:54] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.329s
[2025-09-13 12:02:04] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.317s
[2025-09-13 12:02:13] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.274s
[2025-09-13 12:02:22] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.353s
[2025-09-13 12:02:32] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.245s
[2025-09-13 12:02:41] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.330s
[2025-09-13 12:02:50] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.325s
[2025-09-13 12:03:00] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.334s
[2025-09-13 12:03:09] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.334s
[2025-09-13 12:03:18] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.297s
[2025-09-13 12:03:28] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.329s
[2025-09-13 12:03:37] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.332s
[2025-09-13 12:03:46] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.329s
[2025-09-13 12:03:56] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.267s
[2025-09-13 12:04:05] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.375s
[2025-09-13 12:04:14] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.249s
[2025-09-13 12:04:24] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.331s
[2025-09-13 12:04:33] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.316s
[2025-09-13 12:04:42] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.333s
[2025-09-13 12:04:51] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.268s
[2025-09-13 12:05:01] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.335s
[2025-09-13 12:05:10] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.317s
[2025-09-13 12:05:19] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.325s
[2025-09-13 12:05:29] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.327s
[2025-09-13 12:05:38] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.292s
[2025-09-13 12:05:47] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.346s
[2025-09-13 12:05:57] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.312s
[2025-09-13 12:06:06] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.327s
[2025-09-13 12:06:15] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.330s
[2025-09-13 12:06:25] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.326s
[2025-09-13 12:06:34] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.303s
[2025-09-13 12:06:43] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.327s
[2025-09-13 12:06:53] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.307s
[2025-09-13 12:07:02] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.308s
[2025-09-13 12:07:11] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.332s
[2025-09-13 12:07:21] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.298s
[2025-09-13 12:07:30] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.334s
[2025-09-13 12:07:39] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.343s
[2025-09-13 12:07:49] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.305s
[2025-09-13 12:07:58] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.329s
[2025-09-13 12:08:07] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.330s
[2025-09-13 12:08:17] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.308s
[2025-09-13 12:08:26] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.332s
[2025-09-13 12:08:35] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.325s
[2025-09-13 12:08:45] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.331s
[2025-09-13 12:08:54] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.320s
[2025-09-13 12:09:03] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.321s
[2025-09-13 12:09:13] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.329s
[2025-09-13 12:09:22] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.330s
[2025-09-13 12:09:31] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.329s
[2025-09-13 12:09:41] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.332s
[2025-09-13 12:09:50] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.326s
[2025-09-13 12:09:59] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.315s
[2025-09-13 12:10:09] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.325s
[2025-09-13 12:10:18] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.325s
[2025-09-13 12:10:18] 自旋相关函数计算完成,总耗时 610.49 秒
[2025-09-13 12:10:19] 计算傅里叶变换...
[2025-09-13 12:10:21] 自旋结构因子计算完成
[2025-09-13 12:10:23] 自旋相关函数平均误差: 0.000655
