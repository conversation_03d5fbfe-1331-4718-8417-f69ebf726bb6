[2025-09-13 11:43:02] 使用checkpoint文件: results/L=4/J2=0.03/J1=0.07/training/checkpoints/checkpoint_iter_000840.pkl
[2025-09-13 11:43:19] ✓ 从checkpoint加载参数: 840
[2025-09-13 11:43:19]   - 能量: -54.063077+0.000002j ± 0.082353
[2025-09-13 11:43:19] ================================================================================
[2025-09-13 11:43:19] 加载量子态: L=4, J2=0.03, J1=0.07, checkpoint=checkpoint_iter_000840
[2025-09-13 11:43:19] 使用采样数目: 1048576
[2025-09-13 11:43:19] 设置样本数为: 1048576
[2025-09-13 11:43:19] 开始生成共享样本集...
[2025-09-13 11:46:22] 样本生成完成,耗时: 182.964 秒
[2025-09-13 11:46:22] ================================================================================
[2025-09-13 11:46:22] 开始计算自旋结构因子...
[2025-09-13 11:46:22] 初始化操作符缓存...
[2025-09-13 11:46:22] 预构建所有自旋相关操作符...
[2025-09-13 11:46:22] 开始计算自旋相关函数...
[2025-09-13 11:46:37] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 14.554s
[2025-09-13 11:46:55] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 17.793s
[2025-09-13 11:47:04] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.329s
[2025-09-13 11:47:13] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.340s
[2025-09-13 11:47:23] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.330s
[2025-09-13 11:47:32] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.337s
[2025-09-13 11:47:41] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.322s
[2025-09-13 11:47:51] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.339s
[2025-09-13 11:48:00] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.324s
[2025-09-13 11:48:09] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.303s
[2025-09-13 11:48:19] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.350s
[2025-09-13 11:48:28] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.335s
[2025-09-13 11:48:38] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.330s
[2025-09-13 11:48:47] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.340s
[2025-09-13 11:48:56] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.332s
[2025-09-13 11:49:06] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.304s
[2025-09-13 11:49:15] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.337s
[2025-09-13 11:49:24] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.338s
[2025-09-13 11:49:34] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.331s
[2025-09-13 11:49:43] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.332s
[2025-09-13 11:49:52] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.377s
[2025-09-13 11:50:02] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.242s
[2025-09-13 11:50:11] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.333s
[2025-09-13 11:50:20] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.336s
[2025-09-13 11:50:30] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.338s
[2025-09-13 11:50:39] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.339s
[2025-09-13 11:50:48] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.344s
[2025-09-13 11:50:58] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.345s
[2025-09-13 11:51:07] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.330s
[2025-09-13 11:51:16] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.322s
[2025-09-13 11:51:26] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.298s
[2025-09-13 11:51:35] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.388s
[2025-09-13 11:51:44] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.264s
[2025-09-13 11:51:54] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.339s
[2025-09-13 11:52:03] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.313s
[2025-09-13 11:52:12] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.333s
[2025-09-13 11:52:22] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.305s
[2025-09-13 11:52:31] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.360s
[2025-09-13 11:52:40] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.323s
[2025-09-13 11:52:50] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.340s
[2025-09-13 11:52:59] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.318s
[2025-09-13 11:53:08] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.305s
[2025-09-13 11:53:18] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.311s
[2025-09-13 11:53:27] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.336s
[2025-09-13 11:53:36] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.328s
[2025-09-13 11:53:46] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.320s
[2025-09-13 11:53:55] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.338s
[2025-09-13 11:54:04] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.308s
[2025-09-13 11:54:14] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.350s
[2025-09-13 11:54:23] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.339s
[2025-09-13 11:54:32] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.314s
[2025-09-13 11:54:42] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.337s
[2025-09-13 11:54:51] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.300s
[2025-09-13 11:55:00] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.318s
[2025-09-13 11:55:10] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.342s
[2025-09-13 11:55:19] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.347s
[2025-09-13 11:55:28] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.331s
[2025-09-13 11:55:38] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.331s
[2025-09-13 11:55:47] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.322s
[2025-09-13 11:55:56] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.340s
[2025-09-13 11:56:06] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.334s
[2025-09-13 11:56:15] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.332s
[2025-09-13 11:56:24] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.343s
[2025-09-13 11:56:34] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.350s
[2025-09-13 11:56:34] 自旋相关函数计算完成,总耗时 611.28 秒
[2025-09-13 11:56:36] 计算傅里叶变换...
[2025-09-13 11:56:39] 自旋结构因子计算完成
[2025-09-13 11:56:40] 自旋相关函数平均误差: 0.000657
