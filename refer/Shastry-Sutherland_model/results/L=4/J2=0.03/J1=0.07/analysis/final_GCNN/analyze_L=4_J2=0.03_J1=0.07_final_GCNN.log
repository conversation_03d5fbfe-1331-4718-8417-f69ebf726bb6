[2025-09-13 12:24:32] 使用checkpoint文件: results/L=4/J2=0.03/J1=0.07/training/checkpoints/final_GCNN.pkl
[2025-09-13 12:24:50] ✓ 从checkpoint加载参数: final
[2025-09-13 12:24:50]   - 能量: -54.082578-0.001117j ± 0.083612
[2025-09-13 12:24:50] ================================================================================
[2025-09-13 12:24:50] 加载量子态: L=4, J2=0.03, J1=0.07, checkpoint=final_GCNN
[2025-09-13 12:24:50] 使用采样数目: 1048576
[2025-09-13 12:24:50] 设置样本数为: 1048576
[2025-09-13 12:24:50] 开始生成共享样本集...
[2025-09-13 12:27:50] 样本生成完成,耗时: 180.582 秒
[2025-09-13 12:27:50] ================================================================================
[2025-09-13 12:27:50] 开始计算自旋结构因子...
[2025-09-13 12:27:50] 初始化操作符缓存...
[2025-09-13 12:27:50] 预构建所有自旋相关操作符...
[2025-09-13 12:27:50] 开始计算自旋相关函数...
[2025-09-13 12:28:05] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 14.632s
[2025-09-13 12:28:23] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 17.774s
[2025-09-13 12:28:32] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.368s
[2025-09-13 12:28:42] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.355s
[2025-09-13 12:28:51] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.356s
[2025-09-13 12:29:00] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.369s
[2025-09-13 12:29:10] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.376s
[2025-09-13 12:29:19] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.374s
[2025-09-13 12:29:29] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.372s
[2025-09-13 12:29:38] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.381s
[2025-09-13 12:29:47] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.372s
[2025-09-13 12:29:57] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.396s
[2025-09-13 12:30:06] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.376s
[2025-09-13 12:30:15] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.378s
[2025-09-13 12:30:25] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.424s
[2025-09-13 12:30:34] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.314s
[2025-09-13 12:30:44] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.366s
[2025-09-13 12:30:53] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.362s
[2025-09-13 12:31:02] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.364s
[2025-09-13 12:31:12] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.368s
[2025-09-13 12:31:21] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.360s
[2025-09-13 12:31:30] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.373s
[2025-09-13 12:31:40] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.352s
[2025-09-13 12:31:49] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.374s
[2025-09-13 12:31:59] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.364s
[2025-09-13 12:32:08] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.391s
[2025-09-13 12:32:17] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.293s
[2025-09-13 12:32:27] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.380s
[2025-09-13 12:32:36] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.362s
[2025-09-13 12:32:45] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.358s
[2025-09-13 12:32:55] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.345s
[2025-09-13 12:33:04] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.386s
[2025-09-13 12:33:14] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.359s
[2025-09-13 12:33:23] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.374s
[2025-09-13 12:33:32] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.360s
[2025-09-13 12:33:42] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.355s
[2025-09-13 12:33:51] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.384s
[2025-09-13 12:34:00] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.320s
[2025-09-13 12:34:10] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.357s
[2025-09-13 12:34:19] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.363s
[2025-09-13 12:34:29] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.353s
[2025-09-13 12:34:38] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.329s
[2025-09-13 12:34:47] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.362s
[2025-09-13 12:34:57] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.368s
[2025-09-13 12:35:06] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.363s
[2025-09-13 12:35:15] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.358s
[2025-09-13 12:35:25] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.374s
[2025-09-13 12:35:34] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.349s
[2025-09-13 12:35:44] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.391s
[2025-09-13 12:35:53] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.371s
[2025-09-13 12:36:02] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.363s
[2025-09-13 12:36:12] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.367s
[2025-09-13 12:36:21] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.351s
[2025-09-13 12:36:30] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.363s
[2025-09-13 12:36:40] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.352s
[2025-09-13 12:36:49] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.376s
[2025-09-13 12:36:59] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.354s
[2025-09-13 12:37:08] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.378s
[2025-09-13 12:37:17] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.367s
[2025-09-13 12:37:27] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.383s
[2025-09-13 12:37:36] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.361s
[2025-09-13 12:37:45] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.357s
[2025-09-13 12:37:55] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.367s
[2025-09-13 12:38:04] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.351s
[2025-09-13 12:38:04] 自旋相关函数计算完成,总耗时 613.68 秒
[2025-09-13 12:38:05] 计算傅里叶变换...
[2025-09-13 12:38:07] 自旋结构因子计算完成
[2025-09-13 12:38:08] 自旋相关函数平均误差: 0.000661
