[2025-09-13 11:01:46] 使用checkpoint文件: results/L=4/J2=0.03/J1=0.07/training/checkpoints/checkpoint_iter_000525.pkl
[2025-09-13 11:02:03] ✓ 从checkpoint加载参数: 525
[2025-09-13 11:02:03]   - 能量: -54.107736-0.001253j ± 0.084092
[2025-09-13 11:02:03] ================================================================================
[2025-09-13 11:02:03] 加载量子态: L=4, J2=0.03, J1=0.07, checkpoint=checkpoint_iter_000525
[2025-09-13 11:02:03] 使用采样数目: 1048576
[2025-09-13 11:02:03] 设置样本数为: 1048576
[2025-09-13 11:02:03] 开始生成共享样本集...
[2025-09-13 11:05:05] 样本生成完成,耗时: 181.086 秒
[2025-09-13 11:05:05] ================================================================================
[2025-09-13 11:05:05] 开始计算自旋结构因子...
[2025-09-13 11:05:05] 初始化操作符缓存...
[2025-09-13 11:05:05] 预构建所有自旋相关操作符...
[2025-09-13 11:05:05] 开始计算自旋相关函数...
[2025-09-13 11:05:19] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 14.272s
[2025-09-13 11:05:37] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 17.993s
[2025-09-13 11:05:46] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.257s
[2025-09-13 11:05:56] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.355s
[2025-09-13 11:06:05] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.357s
[2025-09-13 11:06:14] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.349s
[2025-09-13 11:06:24] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.325s
[2025-09-13 11:06:33] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.371s
[2025-09-13 11:06:42] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.338s
[2025-09-13 11:06:52] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.343s
[2025-09-13 11:07:01] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.341s
[2025-09-13 11:07:10] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.369s
[2025-09-13 11:07:20] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.301s
[2025-09-13 11:07:29] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.345s
[2025-09-13 11:07:38] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.345s
[2025-09-13 11:07:48] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.334s
[2025-09-13 11:07:57] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.340s
[2025-09-13 11:08:06] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.334s
[2025-09-13 11:08:16] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.348s
[2025-09-13 11:08:25] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.348s
[2025-09-13 11:08:35] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.348s
[2025-09-13 11:08:44] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.346s
[2025-09-13 11:08:53] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.339s
[2025-09-13 11:09:03] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.328s
[2025-09-13 11:09:12] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.351s
[2025-09-13 11:09:21] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.360s
[2025-09-13 11:09:31] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.342s
[2025-09-13 11:09:40] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.361s
[2025-09-13 11:09:49] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.336s
[2025-09-13 11:09:59] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.349s
[2025-09-13 11:10:08] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.332s
[2025-09-13 11:10:17] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.355s
[2025-09-13 11:10:27] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.352s
[2025-09-13 11:10:36] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.373s
[2025-09-13 11:10:46] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.331s
[2025-09-13 11:10:55] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.344s
[2025-09-13 11:11:04] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.343s
[2025-09-13 11:11:14] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.355s
[2025-09-13 11:11:23] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.337s
[2025-09-13 11:11:32] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.344s
[2025-09-13 11:11:42] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.342s
[2025-09-13 11:11:51] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.339s
[2025-09-13 11:12:00] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.340s
[2025-09-13 11:12:10] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.361s
[2025-09-13 11:12:19] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.319s
[2025-09-13 11:12:28] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.336s
[2025-09-13 11:12:38] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.358s
[2025-09-13 11:12:47] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.337s
[2025-09-13 11:12:56] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.347s
[2025-09-13 11:13:06] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.350s
[2025-09-13 11:13:15] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.315s
[2025-09-13 11:13:24] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.349s
[2025-09-13 11:13:34] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.338s
[2025-09-13 11:13:43] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.350s
[2025-09-13 11:13:52] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.338s
[2025-09-13 11:14:02] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.375s
[2025-09-13 11:14:11] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.257s
[2025-09-13 11:14:20] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.340s
[2025-09-13 11:14:30] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.344s
[2025-09-13 11:14:39] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.363s
[2025-09-13 11:14:49] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.336s
[2025-09-13 11:14:58] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.319s
[2025-09-13 11:15:07] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.357s
[2025-09-13 11:15:17] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.341s
[2025-09-13 11:15:17] 自旋相关函数计算完成,总耗时 611.91 秒
[2025-09-13 11:15:18] 计算傅里叶变换...
[2025-09-13 11:15:20] 自旋结构因子计算完成
[2025-09-13 11:15:21] 自旋相关函数平均误差: 0.000662
