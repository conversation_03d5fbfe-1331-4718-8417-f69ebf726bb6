[2025-09-13 10:34:17] 使用checkpoint文件: results/L=4/J2=0.03/J1=0.07/training/checkpoints/checkpoint_iter_000315.pkl
[2025-09-13 10:34:38] ✓ 从checkpoint加载参数: 315
[2025-09-13 10:34:38]   - 能量: -54.078414-0.000585j ± 0.082354
[2025-09-13 10:34:38] ================================================================================
[2025-09-13 10:34:38] 加载量子态: L=4, J2=0.03, J1=0.07, checkpoint=checkpoint_iter_000315
[2025-09-13 10:34:38] 使用采样数目: 1048576
[2025-09-13 10:34:38] 设置样本数为: 1048576
[2025-09-13 10:34:38] 开始生成共享样本集...
[2025-09-13 10:37:38] 样本生成完成,耗时: 180.399 秒
[2025-09-13 10:37:38] ================================================================================
[2025-09-13 10:37:38] 开始计算自旋结构因子...
[2025-09-13 10:37:38] 初始化操作符缓存...
[2025-09-13 10:37:38] 预构建所有自旋相关操作符...
[2025-09-13 10:37:38] 开始计算自旋相关函数...
[2025-09-13 10:37:53] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 14.307s
[2025-09-13 10:38:10] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 17.719s
[2025-09-13 10:38:20] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.364s
[2025-09-13 10:38:29] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.379s
[2025-09-13 10:38:39] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.475s
[2025-09-13 10:38:48] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.373s
[2025-09-13 10:38:57] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.270s
[2025-09-13 10:39:07] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.381s
[2025-09-13 10:39:16] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.359s
[2025-09-13 10:39:26] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.363s
[2025-09-13 10:39:35] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.360s
[2025-09-13 10:39:44] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.357s
[2025-09-13 10:39:54] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.360s
[2025-09-13 10:40:03] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.383s
[2025-09-13 10:40:12] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.373s
[2025-09-13 10:40:22] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.338s
[2025-09-13 10:40:31] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.409s
[2025-09-13 10:40:40] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.280s
[2025-09-13 10:40:50] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.364s
[2025-09-13 10:40:59] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.362s
[2025-09-13 10:41:09] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.385s
[2025-09-13 10:41:18] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.347s
[2025-09-13 10:41:27] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.363s
[2025-09-13 10:41:37] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.396s
[2025-09-13 10:41:46] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.362s
[2025-09-13 10:41:55] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.401s
[2025-09-13 10:42:05] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.363s
[2025-09-13 10:42:14] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.337s
[2025-09-13 10:42:24] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.366s
[2025-09-13 10:42:33] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.367s
[2025-09-13 10:42:42] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.365s
[2025-09-13 10:42:52] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.379s
[2025-09-13 10:43:01] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.355s
[2025-09-13 10:43:10] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.391s
[2025-09-13 10:43:20] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.359s
[2025-09-13 10:43:29] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.389s
[2025-09-13 10:43:39] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.362s
[2025-09-13 10:43:48] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.344s
[2025-09-13 10:43:57] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.334s
[2025-09-13 10:44:07] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.398s
[2025-09-13 10:44:16] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.360s
[2025-09-13 10:44:25] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.359s
[2025-09-13 10:44:35] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.358s
[2025-09-13 10:44:44] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.382s
[2025-09-13 10:44:54] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.357s
[2025-09-13 10:45:03] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.361s
[2025-09-13 10:45:12] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.391s
[2025-09-13 10:45:22] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.350s
[2025-09-13 10:45:31] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.408s
[2025-09-13 10:45:40] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.354s
[2025-09-13 10:45:50] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.368s
[2025-09-13 10:45:59] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.376s
[2025-09-13 10:46:09] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.355s
[2025-09-13 10:46:18] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.390s
[2025-09-13 10:46:27] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.359s
[2025-09-13 10:46:37] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.390s
[2025-09-13 10:46:46] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.361s
[2025-09-13 10:46:55] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.355s
[2025-09-13 10:47:05] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.359s
[2025-09-13 10:47:14] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.406s
[2025-09-13 10:47:24] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.363s
[2025-09-13 10:47:33] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.368s
[2025-09-13 10:47:42] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.397s
[2025-09-13 10:47:52] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.367s
[2025-09-13 10:47:52] 自旋相关函数计算完成,总耗时 613.33 秒
[2025-09-13 10:47:53] 计算傅里叶变换...
[2025-09-13 10:47:55] 自旋结构因子计算完成
[2025-09-13 10:47:56] 自旋相关函数平均误差: 0.000654
