[2025-09-12 09:24:38] ✓ 从checkpoint恢复: results/L=4/J2=0.03/J1=0.01/training/checkpoints/final_GCNN.pkl
[2025-09-12 09:24:38]   - 迭代次数: final
[2025-09-12 09:24:38]   - 能量: -51.797149-0.001289j ± 0.084014
[2025-09-12 09:24:38]   - 时间戳: 2025-09-12T09:22:33.087391+08:00
[2025-09-12 09:25:03] ✓ 变分状态参数已从checkpoint恢复
[2025-09-12 09:25:03] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-12 09:25:04] ==================================================
[2025-09-12 09:25:04] GCNN for Shastry-Sutherland Model
[2025-09-12 09:25:04] ==================================================
[2025-09-12 09:25:04] System parameters:
[2025-09-12 09:25:04]   - System size: L=4, N=64
[2025-09-12 09:25:04]   - System parameters: J1=-0.0, J2=0.03, Q=0.97
[2025-09-12 09:25:04] --------------------------------------------------
[2025-09-12 09:25:04] Model parameters:
[2025-09-12 09:25:04]   - Number of layers = 4
[2025-09-12 09:25:04]   - Number of features = 4
[2025-09-12 09:25:04]   - Total parameters = 12572
[2025-09-12 09:25:04] --------------------------------------------------
[2025-09-12 09:25:04] Training parameters:
[2025-09-12 09:25:04]   - Learning rate: 0.015
[2025-09-12 09:25:04]   - Total iterations: 1050
[2025-09-12 09:25:04]   - Annealing cycles: 3
[2025-09-12 09:25:04]   - Initial period: 150
[2025-09-12 09:25:04]   - Period multiplier: 2.0
[2025-09-12 09:25:04]   - Temperature range: 0.0-1.0
[2025-09-12 09:25:04]   - Samples: 4096
[2025-09-12 09:25:04]   - Discarded samples: 0
[2025-09-12 09:25:04]   - Chunk size: 2048
[2025-09-12 09:25:04]   - Diagonal shift: 0.2
[2025-09-12 09:25:04]   - Gradient clipping: 1.0
[2025-09-12 09:25:04]   - Checkpoint enabled: interval=105
[2025-09-12 09:25:04]   - Checkpoint directory: results/L=4/J2=0.03/J1=-0.00/training/checkpoints
[2025-09-12 09:25:04] --------------------------------------------------
[2025-09-12 09:25:04] Device status:
[2025-09-12 09:25:04]   - Devices model: NVIDIA H200 NVL
[2025-09-12 09:25:04]   - Number of devices: 1
[2025-09-12 09:25:04]   - Sharding: True
[2025-09-12 09:25:04] ============================================================
[2025-09-12 09:26:48] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -51.387868-0.001179j
[2025-09-12 09:27:46] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -51.184125-0.000352j
[2025-09-12 09:27:53] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -51.301620+0.000048j
[2025-09-12 09:28:04] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -51.348537+0.003048j
[2025-09-12 09:28:15] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -51.213603+0.001312j
[2025-09-12 09:28:26] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -51.233431-0.001061j
[2025-09-12 09:28:37] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -51.220828+0.000861j
[2025-09-12 09:28:47] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -51.382701-0.000438j
[2025-09-12 09:28:58] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -51.183467+0.001017j
[2025-09-12 09:29:09] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -51.311399+0.001082j
[2025-09-12 09:29:20] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -51.243015+0.002933j
[2025-09-12 09:29:31] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -51.236699-0.000848j
[2025-09-12 09:29:41] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -51.354081+0.000925j
[2025-09-12 09:29:52] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -51.287864+0.002174j
[2025-09-12 09:30:03] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -51.171140+0.001207j
[2025-09-12 09:30:14] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -51.190050+0.001426j
[2025-09-12 09:30:25] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -51.291939+0.000327j
[2025-09-12 09:30:35] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -51.347789-0.000936j
[2025-09-12 09:30:46] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -51.498675-0.002401j
[2025-09-12 09:30:57] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -51.453888+0.000465j
[2025-09-12 09:31:08] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -51.460064+0.001320j
[2025-09-12 09:31:18] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -51.321242+0.002335j
[2025-09-12 09:31:29] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -51.282190-0.000601j
[2025-09-12 09:31:40] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -51.368319-0.001775j
[2025-09-12 09:31:51] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -51.401785+0.001572j
[2025-09-12 09:32:01] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -51.462920-0.001075j
[2025-09-12 09:32:12] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -51.447137-0.000879j
[2025-09-12 09:32:23] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -51.274473-0.000407j
[2025-09-12 09:32:34] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -51.202321-0.001725j
[2025-09-12 09:32:45] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -51.304974+0.000649j
[2025-09-12 09:32:55] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -51.387521+0.001217j
[2025-09-12 09:33:06] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -51.370814+0.000456j
[2025-09-12 09:33:17] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -51.357587+0.001239j
[2025-09-12 09:33:28] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -51.350481-0.000711j
[2025-09-12 09:33:38] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -51.255616+0.003109j
[2025-09-12 09:33:49] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -51.409359-0.000337j
[2025-09-12 09:34:00] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -51.356538+0.002086j
[2025-09-12 09:34:11] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -51.307121+0.001423j
[2025-09-12 09:34:22] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -51.356507+0.000711j
[2025-09-12 09:34:32] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -51.206145-0.001399j
[2025-09-12 09:34:43] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -51.275453+0.001590j
[2025-09-12 09:34:54] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -51.317071-0.001479j
[2025-09-12 09:35:05] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -51.383372+0.001373j
[2025-09-12 09:35:15] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -51.338062-0.002422j
[2025-09-12 09:35:26] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -51.314319+0.000581j
[2025-09-12 09:35:37] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -51.433426+0.003224j
[2025-09-12 09:35:48] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -51.394412-0.002683j
[2025-09-12 09:35:58] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -51.267168-0.002219j
[2025-09-12 09:36:09] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -51.303591+0.001897j
[2025-09-12 09:36:20] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -51.277425-0.001050j
[2025-09-12 09:36:31] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -51.348817-0.004126j
[2025-09-12 09:36:42] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -51.185276+0.000363j
[2025-09-12 09:36:52] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -51.340998-0.000485j
[2025-09-12 09:37:03] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -51.251258+0.001349j
[2025-09-12 09:37:14] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -51.502257-0.001930j
[2025-09-12 09:37:25] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -51.339035-0.001342j
[2025-09-12 09:37:35] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -51.380681+0.000901j
[2025-09-12 09:37:46] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -51.351740+0.000093j
[2025-09-12 09:37:57] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -51.423875+0.000888j
[2025-09-12 09:38:08] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -51.318926-0.001694j
[2025-09-12 09:38:18] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -51.357869-0.000894j
[2025-09-12 09:38:29] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -51.362115-0.002607j
[2025-09-12 09:38:40] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -51.445261-0.000417j
[2025-09-12 09:38:51] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -51.471289+0.001856j
[2025-09-12 09:39:02] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -51.277522+0.001408j
[2025-09-12 09:39:12] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -51.269643-0.002771j
[2025-09-12 09:39:23] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -51.336571-0.001689j
[2025-09-12 09:39:34] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -51.355520-0.001927j
[2025-09-12 09:39:44] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -51.353639+0.000477j
[2025-09-12 09:39:55] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -51.330462-0.000277j
[2025-09-12 09:40:06] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -51.385270-0.002200j
[2025-09-12 09:40:17] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -51.359677+0.003477j
[2025-09-12 09:40:27] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -51.399653+0.000847j
[2025-09-12 09:40:38] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -51.403754-0.002944j
[2025-09-12 09:40:49] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -51.255151+0.000808j
[2025-09-12 09:41:00] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -51.374812-0.002146j
[2025-09-12 09:41:11] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -51.326753+0.000633j
[2025-09-12 09:41:21] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -51.518948+0.002260j
[2025-09-12 09:41:32] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -51.395711+0.001890j
[2025-09-12 09:41:43] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -51.367597+0.001108j
[2025-09-12 09:41:54] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -51.283554+0.002033j
[2025-09-12 09:42:04] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -51.304555+0.002506j
[2025-09-12 09:42:15] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -51.340822-0.002736j
[2025-09-12 09:42:26] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -51.399944-0.000488j
[2025-09-12 09:42:37] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -51.390553-0.000648j
[2025-09-12 09:42:48] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -51.413656+0.001013j
[2025-09-12 09:42:58] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -51.438092+0.000554j
[2025-09-12 09:43:09] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -51.423760-0.004049j
[2025-09-12 09:43:20] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -51.453849+0.002379j
[2025-09-12 09:43:31] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -51.358458-0.001418j
[2025-09-12 09:43:41] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -51.396927-0.001800j
[2025-09-12 09:43:52] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -51.426470-0.002004j
[2025-09-12 09:44:03] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -51.326156-0.000834j
[2025-09-12 09:44:14] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -51.378366-0.002641j
[2025-09-12 09:44:25] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -51.424328+0.000109j
[2025-09-12 09:44:35] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -51.371343+0.003158j
[2025-09-12 09:44:46] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -51.440130+0.000325j
[2025-09-12 09:44:57] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -51.365914-0.000436j
[2025-09-12 09:45:08] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -51.514161+0.000435j
[2025-09-12 09:45:19] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -51.457908-0.000340j
[2025-09-12 09:45:29] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -51.381232-0.000429j
[2025-09-12 09:45:40] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -51.325327+0.002095j
[2025-09-12 09:45:51] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -51.311383-0.003921j
[2025-09-12 09:46:02] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -51.295679-0.000217j
[2025-09-12 09:46:12] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -51.315226+0.000807j
[2025-09-12 09:46:12] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-12 09:46:23] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -51.181484+0.005010j
[2025-09-12 09:46:34] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -51.256104-0.001171j
[2025-09-12 09:46:45] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -51.235956-0.002226j
[2025-09-12 09:46:55] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -51.276707-0.001124j
[2025-09-12 09:47:06] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -51.305860+0.001159j
[2025-09-12 09:47:15] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -51.395849-0.002073j
[2025-09-12 09:47:22] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -51.338520+0.001428j
[2025-09-12 09:47:29] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -51.376027-0.000900j
[2025-09-12 09:47:36] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -51.438293-0.000462j
[2025-09-12 09:47:44] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -51.419459+0.000844j
[2025-09-12 09:47:51] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -51.447643+0.003467j
[2025-09-12 09:47:58] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -51.498294-0.000094j
[2025-09-12 09:48:05] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -51.403589-0.000483j
[2025-09-12 09:48:12] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -51.496429-0.001137j
[2025-09-12 09:48:22] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -51.369305+0.000242j
[2025-09-12 09:48:33] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -51.420330-0.001807j
[2025-09-12 09:48:44] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -51.399655+0.000250j
[2025-09-12 09:48:55] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -51.387499+0.002674j
[2025-09-12 09:49:06] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -51.304235+0.000666j
[2025-09-12 09:49:16] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -51.260134-0.000595j
[2025-09-12 09:49:27] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -51.266810+0.000491j
[2025-09-12 09:49:38] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -51.219641-0.000362j
[2025-09-12 09:49:49] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -51.207860+0.000785j
[2025-09-12 09:50:00] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -51.160044+0.001279j
[2025-09-12 09:50:10] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -51.187075-0.001378j
[2025-09-12 09:50:21] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -51.256066-0.001724j
[2025-09-12 09:50:32] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -51.249763-0.002298j
[2025-09-12 09:50:43] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -51.329296+0.002041j
[2025-09-12 09:50:54] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -51.345796-0.001457j
[2025-09-12 09:51:04] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -51.418468+0.000563j
[2025-09-12 09:51:15] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -51.511842+0.000888j
[2025-09-12 09:51:26] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -51.377839-0.000849j
[2025-09-12 09:51:37] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -51.292667+0.001687j
[2025-09-12 09:51:47] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -51.277844-0.000218j
[2025-09-12 09:51:58] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -51.334517-0.001175j
[2025-09-12 09:52:09] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -51.314324-0.001200j
[2025-09-12 09:52:20] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -51.304408-0.000733j
[2025-09-12 09:52:30] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -51.408456-0.003808j
[2025-09-12 09:52:41] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -51.417820-0.002008j
[2025-09-12 09:52:52] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -51.423062-0.001792j
[2025-09-12 09:53:03] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -51.311611+0.004339j
[2025-09-12 09:53:14] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -51.366314-0.001340j
[2025-09-12 09:53:24] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -51.385705+0.002311j
[2025-09-12 09:53:35] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -51.355714-0.000248j
[2025-09-12 09:53:46] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -51.460612-0.002225j
[2025-09-12 09:53:46] RESTART #1 | Period: 300
[2025-09-12 09:53:57] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -51.413441-0.002607j
[2025-09-12 09:54:08] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -51.257307-0.001858j
[2025-09-12 09:54:18] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -51.296658+0.001661j
[2025-09-12 09:54:29] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -51.318676+0.000854j
[2025-09-12 09:54:40] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -51.443476-0.005720j
[2025-09-12 09:54:51] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -51.277072+0.002826j
[2025-09-12 09:55:01] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -51.323589-0.001769j
[2025-09-12 09:55:12] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -51.318412-0.001358j
[2025-09-12 09:55:23] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -51.269778-0.005422j
[2025-09-12 09:55:34] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -51.368291-0.001537j
[2025-09-12 09:55:45] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -51.480026-0.000260j
[2025-09-12 09:55:55] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -51.371148+0.001604j
[2025-09-12 09:56:06] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -51.477902+0.001409j
[2025-09-12 09:56:17] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -51.244655-0.002978j
[2025-09-12 09:56:28] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -51.381318+0.001161j
[2025-09-12 09:56:39] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -51.420017+0.000719j
[2025-09-12 09:56:49] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -51.438567-0.002210j
[2025-09-12 09:57:00] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -51.454448-0.002214j
[2025-09-12 09:57:11] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -51.333977-0.001112j
[2025-09-12 09:57:22] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -51.468956+0.000384j
[2025-09-12 09:57:33] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -51.265641+0.002565j
[2025-09-12 09:57:43] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -51.258812-0.000177j
[2025-09-12 09:57:50] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -51.389521+0.001650j
[2025-09-12 09:57:58] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -51.217970+0.003410j
[2025-09-12 09:58:08] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -51.241197+0.000288j
[2025-09-12 09:58:15] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -51.293976-0.002627j
[2025-09-12 09:58:24] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -51.302343+0.000217j
[2025-09-12 09:58:35] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -51.395008-0.000999j
[2025-09-12 09:58:46] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -51.340583+0.004208j
[2025-09-12 09:58:56] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -51.366846+0.002003j
[2025-09-12 09:59:07] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -51.340216-0.001011j
[2025-09-12 09:59:18] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -51.375863-0.003694j
[2025-09-12 09:59:29] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -51.330357-0.000817j
[2025-09-12 09:59:39] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -51.326390-0.003224j
[2025-09-12 09:59:50] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -51.291679-0.001228j
[2025-09-12 10:00:01] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -51.141757-0.000407j
[2025-09-12 10:00:12] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -51.247824+0.000555j
[2025-09-12 10:00:22] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -51.395837+0.001617j
[2025-09-12 10:00:33] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -51.262959+0.003748j
[2025-09-12 10:00:44] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -51.307005+0.000316j
[2025-09-12 10:00:55] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -51.361541-0.002291j
[2025-09-12 10:01:05] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -51.227016-0.000053j
[2025-09-12 10:01:16] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -51.218153+0.001070j
[2025-09-12 10:01:27] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -51.360394+0.000594j
[2025-09-12 10:01:38] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -51.287613+0.000329j
[2025-09-12 10:01:48] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -51.331929+0.004957j
[2025-09-12 10:01:59] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -51.321355+0.000281j
[2025-09-12 10:02:10] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -51.303212+0.002475j
[2025-09-12 10:02:20] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -51.476084-0.000980j
[2025-09-12 10:02:31] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -51.445970+0.004524j
[2025-09-12 10:02:42] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -51.444777+0.000772j
[2025-09-12 10:02:53] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -51.376170-0.000541j
[2025-09-12 10:03:04] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -51.286614-0.000366j
[2025-09-12 10:03:14] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -51.096904-0.001366j
[2025-09-12 10:03:25] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -51.208138+0.000776j
[2025-09-12 10:03:36] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -51.149890-0.000335j
[2025-09-12 10:03:46] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -51.341663-0.000210j
[2025-09-12 10:03:57] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -51.440847-0.001618j
[2025-09-12 10:04:08] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -51.400178-0.002078j
[2025-09-12 10:04:19] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -51.359758+0.001313j
[2025-09-12 10:04:19] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-12 10:04:29] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -51.288094+0.000098j
[2025-09-12 10:04:40] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -51.345470+0.000037j
[2025-09-12 10:04:51] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -51.373368-0.000042j
[2025-09-12 10:05:02] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -51.381978-0.001490j
[2025-09-12 10:05:12] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -51.280859-0.000734j
[2025-09-12 10:05:23] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -51.261156+0.001486j
[2025-09-12 10:05:34] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -51.335687-0.001048j
[2025-09-12 10:05:45] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -51.324067-0.000421j
[2025-09-12 10:05:56] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -51.270248+0.000239j
[2025-09-12 10:06:06] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -51.322268+0.000734j
[2025-09-12 10:06:17] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -51.355952-0.002088j
[2025-09-12 10:06:28] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -51.350909+0.003071j
[2025-09-12 10:06:39] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -51.361586-0.000453j
[2025-09-12 10:06:49] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -51.398951+0.000636j
[2025-09-12 10:07:00] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -51.423130-0.001384j
[2025-09-12 10:07:11] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -51.363375-0.002088j
[2025-09-12 10:07:22] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -51.403451-0.000413j
[2025-09-12 10:07:32] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -51.294529+0.000092j
[2025-09-12 10:07:43] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -51.342110+0.001469j
[2025-09-12 10:07:54] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -51.382048+0.000692j
[2025-09-12 10:08:05] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -51.426101+0.000285j
[2025-09-12 10:08:15] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -51.201050+0.000133j
[2025-09-12 10:08:26] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -51.290255+0.001794j
[2025-09-12 10:08:37] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -51.190568-0.003144j
[2025-09-12 10:08:48] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -51.270650+0.000806j
[2025-09-12 10:08:58] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -51.177509-0.002062j
[2025-09-12 10:09:09] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -51.197583-0.001617j
[2025-09-12 10:09:20] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -51.374247+0.001375j
[2025-09-12 10:09:31] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -51.320975-0.002862j
[2025-09-12 10:09:41] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -51.285720+0.000512j
[2025-09-12 10:09:52] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -51.296694+0.000927j
[2025-09-12 10:10:03] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -51.370285+0.000134j
[2025-09-12 10:10:14] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -51.245822-0.001924j
[2025-09-12 10:10:25] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -51.177522-0.000698j
[2025-09-12 10:10:35] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -51.230092-0.000572j
[2025-09-12 10:10:46] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -51.351350+0.001156j
[2025-09-12 10:10:57] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -51.245675-0.000196j
[2025-09-12 10:11:08] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -51.372578+0.001122j
[2025-09-12 10:11:18] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -51.271393+0.001319j
[2025-09-12 10:11:29] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -51.376975+0.001765j
[2025-09-12 10:11:40] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -51.272159-0.001879j
[2025-09-12 10:11:51] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -51.307844-0.002438j
[2025-09-12 10:12:01] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -51.297581+0.000388j
[2025-09-12 10:12:12] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -51.271839-0.003098j
[2025-09-12 10:12:23] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -51.373476-0.003070j
[2025-09-12 10:12:34] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -51.316240-0.003301j
[2025-09-12 10:12:45] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -51.220814+0.000169j
[2025-09-12 10:12:55] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -51.436370-0.002610j
[2025-09-12 10:13:06] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -51.334869+0.003296j
[2025-09-12 10:13:17] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -51.285125+0.001989j
[2025-09-12 10:13:28] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -51.200389-0.001406j
[2025-09-12 10:13:38] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -51.331513-0.000061j
[2025-09-12 10:13:49] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -51.353815+0.000678j
[2025-09-12 10:14:00] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -51.389113-0.000716j
[2025-09-12 10:14:11] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -51.312395+0.000654j
[2025-09-12 10:14:21] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -51.345500+0.000609j
[2025-09-12 10:14:32] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -51.403522-0.000984j
[2025-09-12 10:14:43] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -51.403576-0.004268j
[2025-09-12 10:14:54] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -51.424224+0.002159j
[2025-09-12 10:15:04] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -51.402105-0.001100j
[2025-09-12 10:15:15] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -51.211895+0.000608j
[2025-09-12 10:15:26] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -51.371409+0.000418j
[2025-09-12 10:15:37] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -51.473777+0.000108j
[2025-09-12 10:15:47] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -51.400208-0.000521j
[2025-09-12 10:15:58] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -51.354900+0.000808j
[2025-09-12 10:16:09] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -51.498381-0.002922j
[2025-09-12 10:16:20] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -51.410996+0.000889j
[2025-09-12 10:16:31] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -51.498426+0.001667j
[2025-09-12 10:16:41] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -51.519623+0.000755j
[2025-09-12 10:16:52] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -51.302315-0.002829j
[2025-09-12 10:17:03] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -51.425132+0.000890j
[2025-09-12 10:17:14] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -51.369389-0.001252j
[2025-09-12 10:17:24] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -51.414962-0.000232j
[2025-09-12 10:17:35] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -51.422964-0.000504j
[2025-09-12 10:17:46] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -51.339422+0.000552j
[2025-09-12 10:17:57] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -51.375833+0.001706j
[2025-09-12 10:18:07] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -51.271564-0.000481j
[2025-09-12 10:18:18] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -51.281571-0.001297j
[2025-09-12 10:18:29] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -51.335026+0.001758j
[2025-09-12 10:18:40] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -51.328060+0.000802j
[2025-09-12 10:18:50] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -51.293246+0.001162j
[2025-09-12 10:19:01] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -51.344937+0.000216j
[2025-09-12 10:19:12] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -51.273333-0.001313j
[2025-09-12 10:19:23] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -51.331415+0.002347j
[2025-09-12 10:19:34] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -51.407130-0.000480j
[2025-09-12 10:19:44] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -51.322634-0.000421j
[2025-09-12 10:19:55] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -51.378342+0.000984j
[2025-09-12 10:20:06] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -51.324262+0.001912j
[2025-09-12 10:20:17] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -51.291725-0.000914j
[2025-09-12 10:20:27] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -51.251682-0.003716j
[2025-09-12 10:20:38] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -51.400608+0.000775j
[2025-09-12 10:20:49] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -51.267608-0.003084j
[2025-09-12 10:21:00] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -51.377156-0.001577j
[2025-09-12 10:21:10] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -51.336840+0.000414j
[2025-09-12 10:21:21] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -51.276767+0.001379j
[2025-09-12 10:21:32] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -51.305837+0.002398j
[2025-09-12 10:21:43] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -51.288048-0.000757j
[2025-09-12 10:21:54] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -51.201484-0.000847j
[2025-09-12 10:22:04] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -51.217507-0.000756j
[2025-09-12 10:22:15] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -51.105252-0.000941j
[2025-09-12 10:22:26] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -51.123075-0.002781j
[2025-09-12 10:22:37] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -51.168060-0.000450j
[2025-09-12 10:22:47] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -51.163657-0.001677j
[2025-09-12 10:22:58] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -51.251961+0.001135j
[2025-09-12 10:23:09] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -51.121842-0.001329j
[2025-09-12 10:23:09] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-12 10:23:20] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -51.159117-0.000230j
[2025-09-12 10:23:30] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -51.225071+0.000675j
[2025-09-12 10:23:41] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -51.196997-0.000608j
[2025-09-12 10:23:52] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -51.134427+0.000955j
[2025-09-12 10:24:03] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -51.145979+0.000811j
[2025-09-12 10:24:13] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -51.165289+0.000002j
[2025-09-12 10:24:24] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -51.034758+0.001334j
[2025-09-12 10:24:35] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -51.148248+0.000764j
[2025-09-12 10:24:46] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -51.146754-0.000557j
[2025-09-12 10:24:57] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -51.249961+0.000608j
[2025-09-12 10:25:07] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -51.238171-0.002927j
[2025-09-12 10:25:18] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -51.330970+0.004192j
[2025-09-12 10:25:29] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -51.295631+0.002989j
[2025-09-12 10:25:40] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -51.133642-0.001179j
[2025-09-12 10:25:50] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -51.358438+0.000609j
[2025-09-12 10:26:01] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -51.310484+0.000141j
[2025-09-12 10:26:12] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -51.280908+0.000486j
[2025-09-12 10:26:23] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -51.461693+0.001593j
[2025-09-12 10:26:33] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -51.363683-0.001336j
[2025-09-12 10:26:44] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -51.305098+0.000664j
[2025-09-12 10:26:55] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -51.195672+0.002128j
[2025-09-12 10:27:06] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -51.302975+0.002770j
[2025-09-12 10:27:16] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -51.256184-0.001940j
[2025-09-12 10:27:27] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -51.290257-0.001741j
[2025-09-12 10:27:38] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -51.298363+0.001373j
[2025-09-12 10:27:49] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -51.307041+0.000887j
[2025-09-12 10:27:59] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -51.269101+0.003962j
[2025-09-12 10:28:10] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -51.355044+0.000657j
[2025-09-12 10:28:21] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -51.374932+0.003499j
[2025-09-12 10:28:32] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -51.381982+0.001594j
[2025-09-12 10:28:42] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -51.449320-0.000534j
[2025-09-12 10:28:53] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -51.363894+0.002039j
[2025-09-12 10:29:04] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -51.283972-0.000377j
[2025-09-12 10:29:15] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -51.275638-0.001132j
[2025-09-12 10:29:25] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -51.260296-0.002499j
[2025-09-12 10:29:36] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -51.201865+0.000218j
[2025-09-12 10:29:47] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -51.180981+0.000053j
[2025-09-12 10:29:58] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -51.388851+0.000574j
[2025-09-12 10:30:08] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -51.274598+0.001441j
[2025-09-12 10:30:19] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -51.369248-0.003762j
[2025-09-12 10:30:30] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -51.370830-0.002693j
[2025-09-12 10:30:41] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -51.309893-0.002106j
[2025-09-12 10:30:51] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -51.254944-0.001794j
[2025-09-12 10:31:02] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -51.307015-0.000157j
[2025-09-12 10:31:13] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -51.136312+0.001987j
[2025-09-12 10:31:24] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -51.231766-0.001276j
[2025-09-12 10:31:33] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -51.332275+0.000477j
[2025-09-12 10:31:40] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -51.240969+0.001037j
[2025-09-12 10:31:47] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -51.365409+0.002499j
[2025-09-12 10:31:55] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -51.340951-0.000736j
[2025-09-12 10:32:02] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -51.294392+0.002541j
[2025-09-12 10:32:09] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -51.331100+0.001420j
[2025-09-12 10:32:16] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -51.357455+0.001875j
[2025-09-12 10:32:23] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -51.262845+0.001510j
[2025-09-12 10:32:31] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -51.278021-0.000955j
[2025-09-12 10:32:38] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -51.323525+0.000208j
[2025-09-12 10:32:49] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -51.359014+0.000503j
[2025-09-12 10:33:00] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -51.367175+0.000480j
[2025-09-12 10:33:11] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -51.346013-0.000926j
[2025-09-12 10:33:22] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -51.457357+0.001267j
[2025-09-12 10:33:32] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -51.320138+0.000761j
[2025-09-12 10:33:43] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -51.327020+0.000557j
[2025-09-12 10:33:54] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -51.404845+0.000910j
[2025-09-12 10:34:05] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -51.366679-0.001395j
[2025-09-12 10:34:15] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -51.367103-0.003502j
[2025-09-12 10:34:26] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -51.364154+0.001149j
[2025-09-12 10:34:37] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -51.377578+0.000545j
[2025-09-12 10:34:48] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -51.296207-0.000395j
[2025-09-12 10:34:58] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -51.389860+0.001601j
[2025-09-12 10:35:09] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -51.353874-0.000433j
[2025-09-12 10:35:20] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -51.402887+0.002710j
[2025-09-12 10:35:31] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -51.313643-0.000309j
[2025-09-12 10:35:42] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -51.318616-0.000185j
[2025-09-12 10:35:52] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -51.287555-0.000863j
[2025-09-12 10:36:03] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -51.307642-0.000731j
[2025-09-12 10:36:14] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -51.372198-0.001248j
[2025-09-12 10:36:25] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -51.357370+0.006018j
[2025-09-12 10:36:36] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -51.282808-0.001425j
[2025-09-12 10:36:47] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -51.409947+0.000475j
[2025-09-12 10:36:58] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -51.370607+0.002829j
[2025-09-12 10:37:09] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -51.289656-0.000765j
[2025-09-12 10:37:20] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -51.183426+0.000417j
[2025-09-12 10:37:31] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -51.199827-0.002320j
[2025-09-12 10:37:41] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -51.140303+0.002190j
[2025-09-12 10:37:52] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -51.239785-0.000835j
[2025-09-12 10:38:03] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -51.224172-0.000533j
[2025-09-12 10:38:14] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -51.312959+0.004043j
[2025-09-12 10:38:25] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -51.392504-0.000503j
[2025-09-12 10:38:35] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -51.500958+0.001461j
[2025-09-12 10:38:46] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -51.432700+0.000218j
[2025-09-12 10:38:57] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -51.347005+0.000402j
[2025-09-12 10:39:08] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -51.305123-0.000383j
[2025-09-12 10:39:19] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -51.352827+0.001303j
[2025-09-12 10:39:29] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -51.391378-0.000382j
[2025-09-12 10:39:40] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -51.410626-0.000396j
[2025-09-12 10:39:51] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -51.374179-0.002098j
[2025-09-12 10:40:02] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -51.324401+0.001022j
[2025-09-12 10:40:13] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -51.363969-0.000042j
[2025-09-12 10:40:24] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -51.298594-0.000061j
[2025-09-12 10:40:34] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -51.329231+0.001291j
[2025-09-12 10:40:45] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -51.130519-0.000596j
[2025-09-12 10:40:56] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -51.205496-0.000006j
[2025-09-12 10:41:07] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -51.308201-0.000816j
[2025-09-12 10:41:17] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -51.331753-0.000912j
[2025-09-12 10:41:28] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -51.345202+0.000276j
[2025-09-12 10:41:28] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-12 10:41:39] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -51.434768-0.002393j
[2025-09-12 10:41:50] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -51.516112+0.001349j
[2025-09-12 10:42:01] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -51.375306-0.000091j
[2025-09-12 10:42:09] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -51.306214+0.004420j
[2025-09-12 10:42:16] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -51.389644+0.000792j
[2025-09-12 10:42:26] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -51.371060-0.002910j
[2025-09-12 10:42:35] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -51.379642-0.001468j
[2025-09-12 10:42:42] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -51.361218+0.000224j
[2025-09-12 10:42:53] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -51.371565+0.001375j
[2025-09-12 10:43:03] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -51.291185+0.000621j
[2025-09-12 10:43:14] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -51.422264-0.002247j
[2025-09-12 10:43:25] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -51.364358+0.000523j
[2025-09-12 10:43:36] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -51.194695+0.000909j
[2025-09-12 10:43:47] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -51.221245-0.000051j
[2025-09-12 10:43:57] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -51.216356+0.001479j
[2025-09-12 10:44:08] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -51.280790-0.001023j
[2025-09-12 10:44:19] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -51.283053-0.001279j
[2025-09-12 10:44:30] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -51.355029+0.001827j
[2025-09-12 10:44:40] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -51.281253-0.003656j
[2025-09-12 10:44:51] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -51.332553-0.002630j
[2025-09-12 10:45:02] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -51.196256-0.001121j
[2025-09-12 10:45:13] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -51.270971-0.002520j
[2025-09-12 10:45:23] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -51.256913-0.000206j
[2025-09-12 10:45:34] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -51.364050+0.002294j
[2025-09-12 10:45:45] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -51.334183-0.000835j
[2025-09-12 10:45:56] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -51.339693+0.000289j
[2025-09-12 10:46:07] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -51.223834+0.000328j
[2025-09-12 10:46:17] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -51.303501-0.001052j
[2025-09-12 10:46:28] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -51.403116+0.000527j
[2025-09-12 10:46:39] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -51.303125-0.000216j
[2025-09-12 10:46:39] RESTART #2 | Period: 600
[2025-09-12 10:46:50] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -51.283152+0.000956j
[2025-09-12 10:47:00] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -51.395270-0.000496j
[2025-09-12 10:47:11] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -51.209442-0.000057j
[2025-09-12 10:47:22] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -51.422882+0.001224j
[2025-09-12 10:47:33] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -51.436966+0.000195j
[2025-09-12 10:47:43] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -51.364671-0.001898j
[2025-09-12 10:47:54] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -51.400068+0.001937j
[2025-09-12 10:48:05] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -51.549781-0.000360j
[2025-09-12 10:48:16] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -51.514663+0.001364j
[2025-09-12 10:48:26] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -51.491802-0.001911j
[2025-09-12 10:48:37] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -51.472030+0.000977j
[2025-09-12 10:48:48] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -51.451652-0.003054j
[2025-09-12 10:48:59] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -51.341286-0.000737j
[2025-09-12 10:49:10] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -51.394707+0.001120j
[2025-09-12 10:49:20] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -51.353770-0.001284j
[2025-09-12 10:49:31] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -51.397199+0.001078j
[2025-09-12 10:49:42] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -51.265699+0.001055j
[2025-09-12 10:49:53] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -51.298220+0.001018j
[2025-09-12 10:50:03] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -51.309164-0.000081j
[2025-09-12 10:50:14] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -51.215631-0.000758j
[2025-09-12 10:50:25] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -51.302181-0.001164j
[2025-09-12 10:50:36] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -51.280264+0.000916j
[2025-09-12 10:50:46] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -51.297962+0.000160j
[2025-09-12 10:50:57] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -51.317883-0.000475j
[2025-09-12 10:51:08] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -51.299661-0.004440j
[2025-09-12 10:51:19] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -51.355061-0.002243j
[2025-09-12 10:51:29] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -51.316293-0.001651j
[2025-09-12 10:51:40] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -51.443666+0.001810j
[2025-09-12 10:51:51] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -51.234081+0.000463j
[2025-09-12 10:52:01] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -51.307288-0.000190j
[2025-09-12 10:52:12] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -51.286243-0.001688j
[2025-09-12 10:52:23] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -51.373845-0.001139j
[2025-09-12 10:52:34] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -51.353322-0.000572j
[2025-09-12 10:52:44] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -51.370839-0.000511j
[2025-09-12 10:52:55] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -51.408696-0.001013j
[2025-09-12 10:53:06] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -51.448195-0.001055j
[2025-09-12 10:53:17] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -51.316972-0.003196j
[2025-09-12 10:53:27] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -51.337168+0.001219j
[2025-09-12 10:53:38] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -51.345750+0.000780j
[2025-09-12 10:53:49] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -51.388086-0.001652j
[2025-09-12 10:54:00] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -51.320369+0.000762j
[2025-09-12 10:54:10] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -51.394129-0.001242j
[2025-09-12 10:54:21] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -51.261609-0.000010j
[2025-09-12 10:54:32] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -51.457099+0.002845j
[2025-09-12 10:54:43] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -51.250697-0.000192j
[2025-09-12 10:54:53] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -51.420631-0.003162j
[2025-09-12 10:55:04] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -51.353874-0.001951j
[2025-09-12 10:55:15] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -51.304366+0.000146j
[2025-09-12 10:55:26] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -51.269017-0.000476j
[2025-09-12 10:55:36] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -51.398599-0.002910j
[2025-09-12 10:55:47] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -51.319191+0.001099j
[2025-09-12 10:55:58] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -51.323359-0.000293j
[2025-09-12 10:56:09] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -51.312921-0.003514j
[2025-09-12 10:56:19] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -51.336822-0.000517j
[2025-09-12 10:56:30] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -51.360819-0.000927j
[2025-09-12 10:56:41] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -51.319097-0.002697j
[2025-09-12 10:56:52] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -51.284642-0.000419j
[2025-09-12 10:57:03] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -51.163939-0.001063j
[2025-09-12 10:57:13] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -51.367499+0.003142j
[2025-09-12 10:57:24] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -51.272374-0.003588j
[2025-09-12 10:57:35] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -51.191823-0.000019j
[2025-09-12 10:57:46] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -51.263098+0.001261j
[2025-09-12 10:57:56] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -51.312879+0.003053j
[2025-09-12 10:58:07] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -51.474258+0.002418j
[2025-09-12 10:58:18] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -51.283937-0.039215j
[2025-09-12 10:58:29] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -51.330406+0.001888j
[2025-09-12 10:58:39] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -51.315967-0.000924j
[2025-09-12 10:58:50] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -51.254093-0.000006j
[2025-09-12 10:59:01] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -51.175083-0.001391j
[2025-09-12 10:59:12] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -51.276170-0.001622j
[2025-09-12 10:59:23] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -51.373679-0.004238j
[2025-09-12 10:59:33] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -51.293326+0.002441j
[2025-09-12 10:59:44] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -51.125212-0.000445j
[2025-09-12 10:59:55] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -51.213345-0.002458j
[2025-09-12 11:00:06] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -51.199322-0.000965j
[2025-09-12 11:00:06] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-12 11:00:16] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -51.300049-0.002738j
[2025-09-12 11:00:27] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -51.277277+0.001620j
[2025-09-12 11:00:38] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -51.463777+0.002104j
[2025-09-12 11:00:49] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -51.426244+0.001009j
[2025-09-12 11:00:59] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -51.267313+0.003016j
[2025-09-12 11:01:10] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -51.203717+0.000624j
[2025-09-12 11:01:21] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -51.173711+0.002195j
[2025-09-12 11:01:32] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -51.212042-0.001774j
[2025-09-12 11:01:42] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -51.211124+0.000302j
[2025-09-12 11:01:53] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -51.249946-0.000521j
[2025-09-12 11:02:04] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -51.251215+0.000721j
[2025-09-12 11:02:15] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -51.388307+0.000899j
[2025-09-12 11:02:26] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -51.323366+0.000532j
[2025-09-12 11:02:36] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -51.385276+0.000148j
[2025-09-12 11:02:47] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -51.273440+0.000278j
[2025-09-12 11:02:58] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -51.324128-0.002687j
[2025-09-12 11:03:09] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -51.289420+0.000882j
[2025-09-12 11:03:19] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -51.301169-0.001660j
[2025-09-12 11:03:30] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -51.167955-0.002259j
[2025-09-12 11:03:41] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -51.426693+0.001860j
[2025-09-12 11:03:52] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -51.365686+0.002089j
[2025-09-12 11:04:02] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -51.396307+0.000851j
[2025-09-12 11:04:13] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -51.203465+0.000371j
[2025-09-12 11:04:24] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -51.305586-0.001862j
[2025-09-12 11:04:35] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -51.247963-0.000654j
[2025-09-12 11:04:46] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -51.250514-0.001724j
[2025-09-12 11:04:56] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -51.216942+0.001235j
[2025-09-12 11:05:07] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -51.275124+0.000410j
[2025-09-12 11:05:18] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -51.255021+0.004560j
[2025-09-12 11:05:29] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -51.262914-0.000299j
[2025-09-12 11:05:39] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -51.278998-0.000311j
[2025-09-12 11:05:50] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -51.314365+0.000091j
[2025-09-12 11:06:01] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -51.371366-0.001961j
[2025-09-12 11:06:12] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -51.326417+0.002344j
[2025-09-12 11:06:22] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -51.234688-0.000860j
[2025-09-12 11:06:33] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -51.379620-0.001842j
[2025-09-12 11:06:44] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -51.428129+0.006246j
[2025-09-12 11:06:55] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -51.355970-0.000673j
[2025-09-12 11:07:06] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -51.328701-0.000212j
[2025-09-12 11:07:16] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -51.458572-0.000384j
[2025-09-12 11:07:27] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -51.362337+0.000537j
[2025-09-12 11:07:38] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -51.285177+0.002646j
[2025-09-12 11:07:49] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -51.313104+0.001077j
[2025-09-12 11:07:59] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -51.442491-0.003965j
[2025-09-12 11:08:10] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -51.421786-0.000759j
[2025-09-12 11:08:21] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -51.363415-0.000416j
[2025-09-12 11:08:32] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -51.289288-0.003956j
[2025-09-12 11:08:43] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -51.215275-0.001116j
[2025-09-12 11:08:53] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -51.280404-0.001250j
[2025-09-12 11:09:04] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -51.306314-0.000856j
[2025-09-12 11:09:15] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -51.231364-0.002487j
[2025-09-12 11:09:26] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -51.256448-0.002827j
[2025-09-12 11:09:36] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -51.210440-0.001438j
[2025-09-12 11:09:47] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -51.205130+0.002133j
[2025-09-12 11:09:58] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -51.370450-0.000527j
[2025-09-12 11:10:09] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -51.338841-0.000155j
[2025-09-12 11:10:20] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -51.215136+0.001736j
[2025-09-12 11:10:30] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -51.308784-0.001338j
[2025-09-12 11:10:41] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -51.170279+0.000197j
[2025-09-12 11:10:52] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -51.276647-0.001184j
[2025-09-12 11:11:03] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -51.260583-0.000476j
[2025-09-12 11:11:13] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -51.391577-0.002254j
[2025-09-12 11:11:24] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -51.430245-0.002116j
[2025-09-12 11:11:35] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -51.449150+0.003533j
[2025-09-12 11:11:46] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -51.157540+0.001429j
[2025-09-12 11:11:56] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -51.263631+0.001285j
[2025-09-12 11:12:07] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -51.274734-0.000236j
[2025-09-12 11:12:18] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -51.379262-0.000274j
[2025-09-12 11:12:29] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -51.402793-0.001823j
[2025-09-12 11:12:39] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -51.323758+0.002991j
[2025-09-12 11:12:50] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -51.439459+0.001944j
[2025-09-12 11:13:01] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -51.485026+0.000927j
[2025-09-12 11:13:12] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -51.375098+0.001501j
[2025-09-12 11:13:22] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -51.376801-0.000438j
[2025-09-12 11:13:33] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -51.307518+0.000156j
[2025-09-12 11:13:44] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -51.424594+0.001795j
[2025-09-12 11:13:55] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -51.430698+0.000632j
[2025-09-12 11:14:06] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -51.519898+0.000653j
[2025-09-12 11:14:16] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -51.438594+0.000809j
[2025-09-12 11:14:27] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -51.384076-0.001119j
[2025-09-12 11:14:38] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -51.298303-0.001328j
[2025-09-12 11:14:49] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -51.362475-0.000089j
[2025-09-12 11:14:59] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -51.277887+0.000244j
[2025-09-12 11:15:10] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -51.380035+0.002288j
[2025-09-12 11:15:21] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -51.364214+0.004455j
[2025-09-12 11:15:32] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -51.398662+0.001818j
[2025-09-12 11:15:42] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -51.338167+0.001112j
[2025-09-12 11:15:53] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -51.280730+0.001217j
[2025-09-12 11:16:01] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -51.228840-0.000785j
[2025-09-12 11:16:08] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -51.350818+0.000214j
[2025-09-12 11:16:16] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -51.448984+0.000539j
[2025-09-12 11:16:23] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -51.371111-0.000978j
[2025-09-12 11:16:30] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -51.257894-0.000917j
[2025-09-12 11:16:37] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -51.323376-0.001546j
[2025-09-12 11:16:45] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -51.328843-0.000777j
[2025-09-12 11:16:52] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -51.353975-0.000600j
[2025-09-12 11:16:59] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -51.404805+0.000887j
[2025-09-12 11:17:06] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -51.376972-0.001557j
[2025-09-12 11:17:16] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -51.356674+0.000270j
[2025-09-12 11:17:26] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -51.437127+0.000174j
[2025-09-12 11:17:37] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -51.551501+0.004562j
[2025-09-12 11:17:48] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -51.456013-0.001188j
[2025-09-12 11:17:59] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -51.312211-0.000178j
[2025-09-12 11:18:10] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -51.334666+0.000841j
[2025-09-12 11:18:20] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -51.388308-0.002103j
[2025-09-12 11:18:20] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-12 11:18:31] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -51.330194+0.000014j
[2025-09-12 11:18:42] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -51.289429-0.001203j
[2025-09-12 11:18:53] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -51.261955+0.002060j
[2025-09-12 11:19:04] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -51.436621+0.000196j
[2025-09-12 11:19:15] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -51.368743+0.000235j
[2025-09-12 11:19:25] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -51.377780+0.002559j
[2025-09-12 11:19:36] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -51.486605+0.001218j
[2025-09-12 11:19:47] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -51.431651-0.000629j
[2025-09-12 11:19:58] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -51.232490-0.001117j
[2025-09-12 11:20:09] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -51.263835-0.000999j
[2025-09-12 11:20:19] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -51.402748-0.000111j
[2025-09-12 11:20:30] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -51.309185-0.000663j
[2025-09-12 11:20:41] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -51.352628+0.001734j
[2025-09-12 11:20:52] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -51.453000+0.000065j
[2025-09-12 11:21:03] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -51.399975+0.000494j
[2025-09-12 11:21:14] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -51.480924-0.000307j
[2025-09-12 11:21:24] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -51.390644+0.000420j
[2025-09-12 11:21:35] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -51.404201-0.002293j
[2025-09-12 11:21:46] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -51.380311-0.000356j
[2025-09-12 11:21:57] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -51.338487-0.000857j
[2025-09-12 11:22:08] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -51.392548+0.000033j
[2025-09-12 11:22:18] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -51.426728+0.001321j
[2025-09-12 11:22:29] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -51.422968+0.001621j
[2025-09-12 11:22:40] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -51.360687-0.002076j
[2025-09-12 11:22:51] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -51.366143-0.001315j
[2025-09-12 11:23:02] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -51.367636-0.001229j
[2025-09-12 11:23:12] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -51.315148-0.001957j
[2025-09-12 11:23:23] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -51.357342-0.000613j
[2025-09-12 11:23:34] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -51.351754+0.000794j
[2025-09-12 11:23:45] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -51.374216+0.000486j
[2025-09-12 11:23:56] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -51.302238-0.001268j
[2025-09-12 11:24:06] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -51.272570+0.001081j
[2025-09-12 11:24:17] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -51.328056-0.000595j
[2025-09-12 11:24:28] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -51.387885+0.003064j
[2025-09-12 11:24:39] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -51.462192-0.004902j
[2025-09-12 11:24:50] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -51.418450+0.001228j
[2025-09-12 11:25:00] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -51.292353+0.000341j
[2025-09-12 11:25:11] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -51.272880+0.001040j
[2025-09-12 11:25:22] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -51.309108+0.001291j
[2025-09-12 11:25:33] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -51.352488-0.000062j
[2025-09-12 11:25:44] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -51.389356-0.001998j
[2025-09-12 11:25:55] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -51.310935-0.002029j
[2025-09-12 11:26:05] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -51.361470+0.000551j
[2025-09-12 11:26:16] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -51.270453-0.001215j
[2025-09-12 11:26:27] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -51.344665+0.001900j
[2025-09-12 11:26:38] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -51.299575-0.001208j
[2025-09-12 11:26:47] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -51.487232-0.000747j
[2025-09-12 11:26:54] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -51.467750+0.000089j
[2025-09-12 11:27:03] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -51.291499-0.002212j
[2025-09-12 11:27:12] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -51.295562-0.000760j
[2025-09-12 11:27:20] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -51.204798+0.000023j
[2025-09-12 11:27:29] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -51.196545-0.001941j
[2025-09-12 11:27:40] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -51.247146+0.000020j
[2025-09-12 11:27:51] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -51.311830+0.000067j
[2025-09-12 11:28:01] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -51.277887+0.000262j
[2025-09-12 11:28:12] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -51.248561+0.001419j
[2025-09-12 11:28:23] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -51.235761-0.002450j
[2025-09-12 11:28:34] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -51.314986+0.001068j
[2025-09-12 11:28:45] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -51.288793+0.000308j
[2025-09-12 11:28:55] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -51.297975+0.003531j
[2025-09-12 11:29:06] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -51.274216-0.001822j
[2025-09-12 11:29:17] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -51.267923-0.003079j
[2025-09-12 11:29:28] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -51.229948+0.001304j
[2025-09-12 11:29:39] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -51.331477-0.003349j
[2025-09-12 11:29:49] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -51.370009+0.001500j
[2025-09-12 11:30:00] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -51.323024-0.000784j
[2025-09-12 11:30:11] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -51.409690+0.002260j
[2025-09-12 11:30:22] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -51.351272-0.001637j
[2025-09-12 11:30:32] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -51.434540-0.001096j
[2025-09-12 11:30:43] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -51.380642-0.000466j
[2025-09-12 11:30:54] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -51.239122-0.002527j
[2025-09-12 11:31:05] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -51.265881+0.001580j
[2025-09-12 11:31:16] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -51.293706-0.000092j
[2025-09-12 11:31:26] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -51.227218+0.000697j
[2025-09-12 11:31:37] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -51.265332+0.001460j
[2025-09-12 11:31:48] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -51.299336-0.002116j
[2025-09-12 11:31:59] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -51.343244+0.000919j
[2025-09-12 11:32:09] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -51.200842+0.003497j
[2025-09-12 11:32:20] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -51.185442+0.000668j
[2025-09-12 11:32:31] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -51.238756+0.001257j
[2025-09-12 11:32:42] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -51.275648-0.003087j
[2025-09-12 11:32:53] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -51.273128+0.000356j
[2025-09-12 11:33:03] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -51.425894+0.001793j
[2025-09-12 11:33:14] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -51.466179+0.002401j
[2025-09-12 11:33:25] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -51.440478+0.002387j
[2025-09-12 11:33:36] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -51.436046+0.001268j
[2025-09-12 11:33:46] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -51.399784-0.000096j
[2025-09-12 11:33:57] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -51.498575+0.000722j
[2025-09-12 11:34:08] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -51.486550-0.001859j
[2025-09-12 11:34:19] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -51.413764+0.001910j
[2025-09-12 11:34:29] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -51.339135+0.000469j
[2025-09-12 11:34:40] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -51.399846-0.000079j
[2025-09-12 11:34:51] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -51.290870+0.000675j
[2025-09-12 11:35:02] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -51.346643+0.001426j
[2025-09-12 11:35:13] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -51.288034-0.000711j
[2025-09-12 11:35:23] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -51.216490+0.000061j
[2025-09-12 11:35:34] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -51.309346+0.001550j
[2025-09-12 11:35:45] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -51.227295+0.000197j
[2025-09-12 11:35:56] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -51.363550+0.000388j
[2025-09-12 11:36:06] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -51.337502-0.000860j
[2025-09-12 11:36:17] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -51.480096-0.000035j
[2025-09-12 11:36:28] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -51.526127+0.000294j
[2025-09-12 11:36:39] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -51.330446+0.000176j
[2025-09-12 11:36:50] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -51.317452+0.003086j
[2025-09-12 11:37:00] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -51.308613-0.000770j
[2025-09-12 11:37:00] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-12 11:37:11] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -51.338171-0.002774j
[2025-09-12 11:37:22] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -51.391279+0.000896j
[2025-09-12 11:37:33] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -51.392394+0.001472j
[2025-09-12 11:37:44] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -51.353068-0.003237j
[2025-09-12 11:37:54] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -51.298683-0.002184j
[2025-09-12 11:38:05] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -51.309520-0.001507j
[2025-09-12 11:38:16] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -51.283243-0.004366j
[2025-09-12 11:38:27] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -51.208063+0.000434j
[2025-09-12 11:38:37] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -51.408869-0.001735j
[2025-09-12 11:38:48] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -51.378163+0.000725j
[2025-09-12 11:38:59] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -51.314291-0.001646j
[2025-09-12 11:39:10] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -51.280056+0.001333j
[2025-09-12 11:39:21] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -51.307714+0.001743j
[2025-09-12 11:39:31] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -51.393458+0.000911j
[2025-09-12 11:39:42] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -51.467279-0.001406j
[2025-09-12 11:39:53] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -51.373179-0.001081j
[2025-09-12 11:40:04] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -51.321925-0.001134j
[2025-09-12 11:40:14] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -51.339242-0.001609j
[2025-09-12 11:40:25] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -51.301483-0.001945j
[2025-09-12 11:40:36] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -51.344738-0.004576j
[2025-09-12 11:40:47] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -51.465916+0.001895j
[2025-09-12 11:40:57] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -51.469782+0.000202j
[2025-09-12 11:41:08] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -51.357295-0.000842j
[2025-09-12 11:41:19] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -51.532688+0.000488j
[2025-09-12 11:41:30] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -51.493678-0.001682j
[2025-09-12 11:41:40] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -51.524250-0.001277j
[2025-09-12 11:41:51] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -51.493115+0.001094j
[2025-09-12 11:42:02] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -51.462045+0.000106j
[2025-09-12 11:42:13] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -51.441165+0.002499j
[2025-09-12 11:42:23] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -51.504605+0.001645j
[2025-09-12 11:42:34] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -51.332038+0.000464j
[2025-09-12 11:42:45] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -51.372351+0.000736j
[2025-09-12 11:42:56] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -51.389872+0.004504j
[2025-09-12 11:43:06] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -51.317817-0.000543j
[2025-09-12 11:43:17] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -51.362482-0.000731j
[2025-09-12 11:43:28] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -51.449425+0.001865j
[2025-09-12 11:43:39] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -51.349678-0.000006j
[2025-09-12 11:43:50] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -51.334647+0.002441j
[2025-09-12 11:44:00] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -51.361247-0.002309j
[2025-09-12 11:44:11] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -51.158069-0.000880j
[2025-09-12 11:44:22] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -51.163899+0.000201j
[2025-09-12 11:44:33] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -51.118031-0.000999j
[2025-09-12 11:44:44] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -51.142868-0.002807j
[2025-09-12 11:44:54] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -51.344086-0.000227j
[2025-09-12 11:45:05] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -51.337012+0.001281j
[2025-09-12 11:45:16] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -51.352239+0.000703j
[2025-09-12 11:45:27] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -51.212256-0.002078j
[2025-09-12 11:45:37] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -51.200433+0.000742j
[2025-09-12 11:45:48] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -51.253306-0.003629j
[2025-09-12 11:45:59] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -51.243675+0.001907j
[2025-09-12 11:46:10] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -51.202179-0.002320j
[2025-09-12 11:46:21] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -51.299452-0.003620j
[2025-09-12 11:46:31] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -51.318715+0.000152j
[2025-09-12 11:46:42] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -51.129736-0.001536j
[2025-09-12 11:46:53] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -51.244324+0.002030j
[2025-09-12 11:47:04] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -51.195447-0.002104j
[2025-09-12 11:47:14] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -51.250729-0.004376j
[2025-09-12 11:47:25] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -51.293871+0.002100j
[2025-09-12 11:47:36] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -51.319091+0.001684j
[2025-09-12 11:47:47] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -51.382256+0.002546j
[2025-09-12 11:47:57] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -51.360098-0.001324j
[2025-09-12 11:48:08] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -51.389620+0.000639j
[2025-09-12 11:48:19] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -51.418622-0.000472j
[2025-09-12 11:48:30] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -51.536279+0.000499j
[2025-09-12 11:48:41] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -51.446687-0.001055j
[2025-09-12 11:48:51] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -51.399622-0.000475j
[2025-09-12 11:49:02] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -51.310576+0.000647j
[2025-09-12 11:49:13] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -51.375237-0.002040j
[2025-09-12 11:49:24] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -51.374796-0.001554j
[2025-09-12 11:49:34] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -51.539933+0.001023j
[2025-09-12 11:49:45] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -51.451053+0.000125j
[2025-09-12 11:49:56] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -51.317520+0.001733j
[2025-09-12 11:50:07] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -51.371850+0.000720j
[2025-09-12 11:50:17] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -51.370820-0.001575j
[2025-09-12 11:50:28] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -51.344189-0.001015j
[2025-09-12 11:50:39] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -51.220880+0.001568j
[2025-09-12 11:50:50] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -51.230348-0.000138j
[2025-09-12 11:51:01] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -51.324982+0.000637j
[2025-09-12 11:51:11] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -51.234159-0.000853j
[2025-09-12 11:51:22] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -51.338874-0.000631j
[2025-09-12 11:51:33] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -51.371295-0.003488j
[2025-09-12 11:51:44] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -51.366406-0.000865j
[2025-09-12 11:51:54] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -51.361648+0.001126j
[2025-09-12 11:52:05] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -51.363460-0.002433j
[2025-09-12 11:52:16] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -51.322064-0.000580j
[2025-09-12 11:52:27] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -51.317480+0.001315j
[2025-09-12 11:52:38] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -51.273086-0.000082j
[2025-09-12 11:52:48] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -51.253317+0.001089j
[2025-09-12 11:52:59] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -51.322810+0.000686j
[2025-09-12 11:53:10] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -51.344307+0.002534j
[2025-09-12 11:53:21] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -51.346404+0.001516j
[2025-09-12 11:53:32] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -51.382160+0.000209j
[2025-09-12 11:53:42] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -51.312456-0.001649j
[2025-09-12 11:53:53] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -51.417195-0.000069j
[2025-09-12 11:54:04] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -51.441328+0.001731j
[2025-09-12 11:54:15] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -51.355653-0.002011j
[2025-09-12 11:54:25] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -51.388709-0.000045j
[2025-09-12 11:54:36] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -51.393718-0.000896j
[2025-09-12 11:54:47] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -51.321922-0.001929j
[2025-09-12 11:54:58] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -51.261190-0.000279j
[2025-09-12 11:55:08] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -51.265085+0.000850j
[2025-09-12 11:55:19] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -51.251754-0.000206j
[2025-09-12 11:55:30] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -51.265991-0.001599j
[2025-09-12 11:55:41] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -51.138183-0.000755j
[2025-09-12 11:55:52] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -51.087941+0.001131j
[2025-09-12 11:55:52] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-12 11:56:02] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -51.278395+0.000452j
[2025-09-12 11:56:13] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -51.240838-0.000561j
[2025-09-12 11:56:24] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -51.400634-0.002171j
[2025-09-12 11:56:35] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -51.471456+0.002200j
[2025-09-12 11:56:45] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -51.300232+0.001781j
[2025-09-12 11:56:56] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -51.351231-0.002374j
[2025-09-12 11:57:07] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -51.401148+0.000487j
[2025-09-12 11:57:18] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -51.308854-0.002081j
[2025-09-12 11:57:29] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -51.380987-0.001599j
[2025-09-12 11:57:39] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -51.329895-0.000969j
[2025-09-12 11:57:50] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -51.312789+0.002961j
[2025-09-12 11:58:01] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -51.468365-0.000585j
[2025-09-12 11:58:12] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -51.354052+0.000372j
[2025-09-12 11:58:22] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -51.361904+0.001063j
[2025-09-12 11:58:33] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -51.295161-0.000888j
[2025-09-12 11:58:44] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -51.344262+0.000505j
[2025-09-12 11:58:55] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -51.339271-0.001676j
[2025-09-12 11:59:06] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -51.272857+0.000875j
[2025-09-12 11:59:16] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -51.282345+0.000325j
[2025-09-12 11:59:27] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -51.265660-0.003725j
[2025-09-12 11:59:38] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -51.297407+0.002186j
[2025-09-12 11:59:49] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -51.445454+0.002511j
[2025-09-12 11:59:59] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -51.476881+0.000347j
[2025-09-12 12:00:10] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -51.387507-0.001829j
[2025-09-12 12:00:21] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -51.238711+0.001627j
[2025-09-12 12:00:32] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -51.179325-0.000474j
[2025-09-12 12:00:43] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -51.284274-0.002672j
[2025-09-12 12:00:53] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -51.260346-0.000544j
[2025-09-12 12:01:04] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -51.347695-0.000997j
[2025-09-12 12:01:15] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -51.375304-0.002932j
[2025-09-12 12:01:23] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -51.245238+0.003671j
[2025-09-12 12:01:30] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -51.174939+0.001188j
[2025-09-12 12:01:38] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -51.218714+0.001570j
[2025-09-12 12:01:45] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -51.211188-0.000195j
[2025-09-12 12:01:52] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -51.281305+0.000541j
[2025-09-12 12:01:59] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -51.424794-0.001926j
[2025-09-12 12:02:06] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -51.405342-0.000876j
[2025-09-12 12:02:13] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -51.315337+0.000911j
[2025-09-12 12:02:21] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -51.390997+0.000582j
[2025-09-12 12:02:29] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -51.402941+0.000985j
[2025-09-12 12:02:40] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -51.383117+0.003016j
[2025-09-12 12:02:51] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -51.399761+0.000544j
[2025-09-12 12:03:01] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -51.420400-0.002144j
[2025-09-12 12:03:12] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -51.390054-0.001872j
[2025-09-12 12:03:23] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -51.316026+0.002095j
[2025-09-12 12:03:34] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -51.393840-0.000717j
[2025-09-12 12:03:44] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -51.412315-0.000619j
[2025-09-12 12:03:55] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -51.342952-0.000131j
[2025-09-12 12:04:06] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -51.306724-0.002403j
[2025-09-12 12:04:17] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -51.381187+0.000005j
[2025-09-12 12:04:28] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -51.306858+0.000824j
[2025-09-12 12:04:39] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -51.388111-0.000366j
[2025-09-12 12:04:49] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -51.306254+0.000309j
[2025-09-12 12:05:00] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -51.292904+0.001604j
[2025-09-12 12:05:11] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -51.367858+0.001646j
[2025-09-12 12:05:22] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -51.458386+0.000334j
[2025-09-12 12:05:32] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -51.306339-0.001074j
[2025-09-12 12:05:43] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -51.272382+0.003712j
[2025-09-12 12:05:54] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -51.259219-0.000117j
[2025-09-12 12:06:05] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -51.271324+0.002368j
[2025-09-12 12:06:16] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -51.282156+0.001850j
[2025-09-12 12:06:26] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -51.203467+0.001811j
[2025-09-12 12:06:37] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -51.303176-0.000472j
[2025-09-12 12:06:48] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -51.416374+0.000257j
[2025-09-12 12:06:59] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -51.201358+0.002376j
[2025-09-12 12:07:10] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -50.981872-0.000279j
[2025-09-12 12:07:20] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -51.156277-0.000948j
[2025-09-12 12:07:31] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -51.118735+0.002297j
[2025-09-12 12:07:42] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -51.271664-0.002367j
[2025-09-12 12:07:53] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -51.261210-0.000127j
[2025-09-12 12:08:04] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -51.366552+0.000853j
[2025-09-12 12:08:14] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -51.394988+0.000444j
[2025-09-12 12:08:25] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -51.359858+0.000830j
[2025-09-12 12:08:36] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -51.241633+0.000413j
[2025-09-12 12:08:47] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -51.301959-0.001588j
[2025-09-12 12:08:58] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -51.205993+0.001291j
[2025-09-12 12:09:08] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -51.306416-0.000230j
[2025-09-12 12:09:19] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -51.325539-0.000763j
[2025-09-12 12:09:30] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -51.459235-0.000171j
[2025-09-12 12:09:41] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -51.468815-0.001266j
[2025-09-12 12:09:51] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -51.364914-0.001796j
[2025-09-12 12:10:02] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -51.347918+0.000555j
[2025-09-12 12:10:13] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -51.329443-0.001781j
[2025-09-12 12:10:24] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -51.363775-0.000385j
[2025-09-12 12:10:34] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -51.294554-0.000414j
[2025-09-12 12:10:45] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -51.445120-0.000325j
[2025-09-12 12:10:56] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -51.410361+0.002077j
[2025-09-12 12:11:07] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -51.427369+0.000574j
[2025-09-12 12:11:18] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -51.414259-0.000245j
[2025-09-12 12:11:28] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -51.426583+0.003530j
[2025-09-12 12:11:39] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -51.370848-0.000482j
[2025-09-12 12:11:50] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -51.368415-0.004295j
[2025-09-12 12:11:59] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -51.391433+0.001609j
[2025-09-12 12:12:06] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -51.463818-0.000149j
[2025-09-12 12:12:16] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -51.301647+0.000442j
[2025-09-12 12:12:24] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -51.311279-0.001505j
[2025-09-12 12:12:31] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -51.441127-0.005022j
[2025-09-12 12:12:41] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -51.297509-0.000379j
[2025-09-12 12:12:52] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -51.334926-0.000752j
[2025-09-12 12:13:03] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -51.315175+0.000111j
[2025-09-12 12:13:14] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -51.320595+0.001800j
[2025-09-12 12:13:25] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -51.319952+0.001415j
[2025-09-12 12:13:35] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -51.511193-0.003803j
[2025-09-12 12:13:46] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -51.459925-0.000813j
[2025-09-12 12:13:57] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -51.359373+0.001734j
[2025-09-12 12:13:57] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-12 12:14:08] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -51.408140-0.003739j
[2025-09-12 12:14:18] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -51.228850+0.000859j
[2025-09-12 12:14:29] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -51.414184-0.000950j
[2025-09-12 12:14:40] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -51.354016+0.004650j
[2025-09-12 12:14:51] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -51.304777+0.001778j
[2025-09-12 12:15:01] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -51.319864-0.000762j
[2025-09-12 12:15:12] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -51.214889-0.000170j
[2025-09-12 12:15:23] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -51.270240-0.002966j
[2025-09-12 12:15:33] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -51.392104-0.003830j
[2025-09-12 12:15:44] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -51.285609-0.001598j
[2025-09-12 12:15:55] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -51.330893+0.000864j
[2025-09-12 12:16:06] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -51.352009+0.002319j
[2025-09-12 12:16:16] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -51.374233+0.000541j
[2025-09-12 12:16:27] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -51.406423+0.000669j
[2025-09-12 12:16:38] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -51.417909-0.002873j
[2025-09-12 12:16:49] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -51.285133-0.001832j
[2025-09-12 12:17:00] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -51.375553+0.002491j
[2025-09-12 12:17:10] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -51.270921-0.002454j
[2025-09-12 12:17:21] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -51.291982+0.000073j
[2025-09-12 12:17:32] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -51.166353-0.001013j
[2025-09-12 12:17:42] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -51.216499-0.000163j
[2025-09-12 12:17:53] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -51.276507-0.000782j
[2025-09-12 12:18:04] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -51.330193-0.002425j
[2025-09-12 12:18:15] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -51.272605-0.000670j
[2025-09-12 12:18:25] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -51.275018+0.000349j
[2025-09-12 12:18:36] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -51.387110+0.000010j
[2025-09-12 12:18:47] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -51.295318-0.000538j
[2025-09-12 12:18:58] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -51.405942+0.002667j
[2025-09-12 12:19:08] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -51.262985+0.002133j
[2025-09-12 12:19:19] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -51.315542-0.001297j
[2025-09-12 12:19:30] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -51.398877-0.000758j
[2025-09-12 12:19:41] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -51.290201-0.001424j
[2025-09-12 12:19:51] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -51.417935+0.001226j
[2025-09-12 12:20:02] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -51.425561+0.000857j
[2025-09-12 12:20:13] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -51.339269-0.000302j
[2025-09-12 12:20:24] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -51.277206-0.000600j
[2025-09-12 12:20:34] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -51.481407+0.003841j
[2025-09-12 12:20:45] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -51.370184+0.001727j
[2025-09-12 12:20:56] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -51.386431-0.002403j
[2025-09-12 12:21:07] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -51.406933-0.000527j
[2025-09-12 12:21:18] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -51.297330+0.000445j
[2025-09-12 12:21:28] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -51.349054-0.002322j
[2025-09-12 12:21:39] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -51.296876-0.001891j
[2025-09-12 12:21:50] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -51.290528-0.003080j
[2025-09-12 12:22:01] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -51.227763-0.002526j
[2025-09-12 12:22:11] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -51.249143-0.001667j
[2025-09-12 12:22:22] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -51.379964-0.001631j
[2025-09-12 12:22:33] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -51.319268-0.001830j
[2025-09-12 12:22:44] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -51.239416+0.002213j
[2025-09-12 12:22:55] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -51.355705-0.000889j
[2025-09-12 12:23:05] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -51.335008+0.000449j
[2025-09-12 12:23:16] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -51.371911+0.000645j
[2025-09-12 12:23:27] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -51.269586-0.002259j
[2025-09-12 12:23:38] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -51.233529+0.001051j
[2025-09-12 12:23:48] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -51.281658-0.001719j
[2025-09-12 12:23:59] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -51.269475-0.000223j
[2025-09-12 12:24:10] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -51.287932-0.000067j
[2025-09-12 12:24:21] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -51.352011+0.000840j
[2025-09-12 12:24:32] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -51.412583-0.001159j
[2025-09-12 12:24:42] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -51.336692-0.004282j
[2025-09-12 12:24:53] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -51.502214+0.001809j
[2025-09-12 12:25:04] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -51.377873-0.000776j
[2025-09-12 12:25:15] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -51.376273+0.001659j
[2025-09-12 12:25:25] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -51.396229+0.001876j
[2025-09-12 12:25:36] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -51.302934-0.001818j
[2025-09-12 12:25:47] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -51.405509+0.001479j
[2025-09-12 12:25:58] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -51.400683+0.003719j
[2025-09-12 12:26:08] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -51.423990-0.001699j
[2025-09-12 12:26:19] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -51.508082-0.000035j
[2025-09-12 12:26:30] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -51.375204+0.001006j
[2025-09-12 12:26:41] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -51.477447+0.001112j
[2025-09-12 12:26:52] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -51.475997+0.002163j
[2025-09-12 12:27:02] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -51.437838+0.001957j
[2025-09-12 12:27:13] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -51.406505-0.000215j
[2025-09-12 12:27:24] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -51.240043+0.000395j
[2025-09-12 12:27:35] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -51.371961+0.001852j
[2025-09-12 12:27:45] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -51.233211+0.000816j
[2025-09-12 12:27:56] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -51.242080+0.001479j
[2025-09-12 12:28:07] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -51.108401+0.002788j
[2025-09-12 12:28:18] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -51.244470+0.000496j
[2025-09-12 12:28:28] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -51.284022+0.000058j
[2025-09-12 12:28:39] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -51.323963+0.002001j
[2025-09-12 12:28:50] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -51.352513+0.001103j
[2025-09-12 12:29:01] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -51.312369-0.001304j
[2025-09-12 12:29:12] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -51.259202+0.001133j
[2025-09-12 12:29:22] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -51.183244-0.001614j
[2025-09-12 12:29:33] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -51.251117-0.001163j
[2025-09-12 12:29:44] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -51.313940+0.001346j
[2025-09-12 12:29:55] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -51.354587+0.002123j
[2025-09-12 12:30:06] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -51.382595+0.003798j
[2025-09-12 12:30:16] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -51.360321-0.001222j
[2025-09-12 12:30:27] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -51.497169-0.003267j
[2025-09-12 12:30:38] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -51.471708-0.002892j
[2025-09-12 12:30:49] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -51.361085-0.001551j
[2025-09-12 12:31:00] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -51.350840+0.000977j
[2025-09-12 12:31:10] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -51.319107-0.000909j
[2025-09-12 12:31:21] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -51.353947-0.002251j
[2025-09-12 12:31:32] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -51.296149+0.000883j
[2025-09-12 12:31:43] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -51.231442+0.001367j
[2025-09-12 12:31:53] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -51.337344+0.001293j
[2025-09-12 12:32:04] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -51.289902-0.000361j
[2025-09-12 12:32:15] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -51.292425+0.000922j
[2025-09-12 12:32:26] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -51.394499-0.001251j
[2025-09-12 12:32:36] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -51.443122-0.001299j
[2025-09-12 12:32:47] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -51.382395+0.002728j
[2025-09-12 12:32:47] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-12 12:32:47] ✅ Training completed | Restarts: 2
[2025-09-12 12:32:47] ============================================================
[2025-09-12 12:32:47] Training completed | Runtime: 11263.6s
[2025-09-12 12:32:51] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-12 12:32:51] ============================================================
