[2025-09-12 17:49:28] 使用checkpoint文件: results/L=4/J2=0.03/J1=0.00/training/checkpoints/checkpoint_iter_000630.pkl
[2025-09-12 17:49:45] ✓ 从checkpoint加载参数: 630
[2025-09-12 17:49:45]   - 能量: -51.388308-0.002103j ± 0.084855
[2025-09-12 17:49:45] ================================================================================
[2025-09-12 17:49:45] 加载量子态: L=4, J2=0.03, J1=0.00, checkpoint=checkpoint_iter_000630
[2025-09-12 17:49:45] 使用采样数目: 1048576
[2025-09-12 17:49:45] 设置样本数为: 1048576
[2025-09-12 17:49:45] 开始生成共享样本集...
[2025-09-12 17:52:47] 样本生成完成,耗时: 181.372 秒
[2025-09-12 17:52:47] ================================================================================
[2025-09-12 17:52:47] 开始计算自旋结构因子...
[2025-09-12 17:52:47] 初始化操作符缓存...
[2025-09-12 17:52:47] 预构建所有自旋相关操作符...
[2025-09-12 17:52:47] 开始计算自旋相关函数...
[2025-09-12 17:53:01] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 14.518s
[2025-09-12 17:53:19] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 17.766s
[2025-09-12 17:53:29] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.354s
[2025-09-12 17:53:38] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.371s
[2025-09-12 17:53:47] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.368s
[2025-09-12 17:53:57] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.361s
[2025-09-12 17:54:06] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.359s
[2025-09-12 17:54:16] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.360s
[2025-09-12 17:54:25] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.357s
[2025-09-12 17:54:34] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.374s
[2025-09-12 17:54:44] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.346s
[2025-09-12 17:54:53] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.370s
[2025-09-12 17:55:02] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.358s
[2025-09-12 17:55:12] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.392s
[2025-09-12 17:55:21] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.395s
[2025-09-12 17:55:31] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.349s
[2025-09-12 17:55:40] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.381s
[2025-09-12 17:55:49] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.385s
[2025-09-12 17:55:59] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.362s
[2025-09-12 17:56:08] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.377s
[2025-09-12 17:56:18] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.428s
[2025-09-12 17:56:27] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.302s
[2025-09-12 17:56:36] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.356s
[2025-09-12 17:56:46] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.390s
[2025-09-12 17:56:55] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.370s
[2025-09-12 17:57:04] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.384s
[2025-09-12 17:57:14] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.351s
[2025-09-12 17:57:23] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.392s
[2025-09-12 17:57:32] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.361s
[2025-09-12 17:57:42] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.359s
[2025-09-12 17:57:51] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.333s
[2025-09-12 17:58:01] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.417s
[2025-09-12 17:58:10] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.302s
[2025-09-12 17:58:19] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.391s
[2025-09-12 17:58:29] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.354s
[2025-09-12 17:58:38] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.389s
[2025-09-12 17:58:47] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.344s
[2025-09-12 17:58:57] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.409s
[2025-09-12 17:59:06] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.360s
[2025-09-12 17:59:16] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.391s
[2025-09-12 17:59:25] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.362s
[2025-09-12 17:59:34] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.383s
[2025-09-12 17:59:44] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.427s
[2025-09-12 17:59:53] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.347s
[2025-09-12 18:00:03] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.358s
[2025-09-12 18:00:12] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.352s
[2025-09-12 18:00:21] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.393s
[2025-09-12 18:00:31] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.336s
[2025-09-12 18:00:40] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.387s
[2025-09-12 18:00:49] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.379s
[2025-09-12 18:00:59] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.355s
[2025-09-12 18:01:08] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.376s
[2025-09-12 18:01:18] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.322s
[2025-09-12 18:01:27] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.388s
[2025-09-12 18:01:36] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.383s
[2025-09-12 18:01:46] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.387s
[2025-09-12 18:01:55] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.373s
[2025-09-12 18:02:05] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.367s
[2025-09-12 18:02:14] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.357s
[2025-09-12 18:02:23] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.379s
[2025-09-12 18:02:33] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.367s
[2025-09-12 18:02:42] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.363s
[2025-09-12 18:02:51] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.385s
[2025-09-12 18:03:01] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.380s
[2025-09-12 18:03:01] 自旋相关函数计算完成,总耗时 613.82 秒
[2025-09-12 18:03:03] 计算傅里叶变换...
[2025-09-12 18:03:06] 自旋结构因子计算完成
[2025-09-12 18:03:07] 自旋相关函数平均误差: 0.000683
