[2025-09-12 18:17:04] 使用checkpoint文件: results/L=4/J2=0.03/J1=0.00/training/checkpoints/checkpoint_iter_000840.pkl
[2025-09-12 18:17:21] ✓ 从checkpoint加载参数: 840
[2025-09-12 18:17:21]   - 能量: -51.087941+0.001131j ± 0.084344
[2025-09-12 18:17:21] ================================================================================
[2025-09-12 18:17:21] 加载量子态: L=4, J2=0.03, J1=0.00, checkpoint=checkpoint_iter_000840
[2025-09-12 18:17:21] 使用采样数目: 1048576
[2025-09-12 18:17:21] 设置样本数为: 1048576
[2025-09-12 18:17:21] 开始生成共享样本集...
[2025-09-12 18:20:23] 样本生成完成,耗时: 181.752 秒
[2025-09-12 18:20:23] ================================================================================
[2025-09-12 18:20:23] 开始计算自旋结构因子...
[2025-09-12 18:20:23] 初始化操作符缓存...
[2025-09-12 18:20:23] 预构建所有自旋相关操作符...
[2025-09-12 18:20:23] 开始计算自旋相关函数...
[2025-09-12 18:20:37] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 14.370s
[2025-09-12 18:20:55] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 17.768s
[2025-09-12 18:21:05] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.352s
[2025-09-12 18:21:14] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.379s
[2025-09-12 18:21:23] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.266s
[2025-09-12 18:21:33] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.314s
[2025-09-12 18:21:42] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.332s
[2025-09-12 18:21:51] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.320s
[2025-09-12 18:22:01] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.321s
[2025-09-12 18:22:10] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.336s
[2025-09-12 18:22:19] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.342s
[2025-09-12 18:22:29] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.323s
[2025-09-12 18:22:38] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.335s
[2025-09-12 18:22:47] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.341s
[2025-09-12 18:22:57] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.334s
[2025-09-12 18:23:06] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.344s
[2025-09-12 18:23:15] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.318s
[2025-09-12 18:23:25] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.329s
[2025-09-12 18:23:34] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.337s
[2025-09-12 18:23:43] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.322s
[2025-09-12 18:23:53] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.324s
[2025-09-12 18:24:02] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.331s
[2025-09-12 18:24:11] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.337s
[2025-09-12 18:24:21] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.316s
[2025-09-12 18:24:30] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.333s
[2025-09-12 18:24:39] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.333s
[2025-09-12 18:24:49] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.340s
[2025-09-12 18:24:58] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.317s
[2025-09-12 18:25:07] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.338s
[2025-09-12 18:25:17] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.339s
[2025-09-12 18:25:26] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.336s
[2025-09-12 18:25:35] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.320s
[2025-09-12 18:25:45] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.334s
[2025-09-12 18:25:54] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.337s
[2025-09-12 18:26:03] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.342s
[2025-09-12 18:26:13] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.326s
[2025-09-12 18:26:22] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.331s
[2025-09-12 18:26:31] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.345s
[2025-09-12 18:26:41] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.342s
[2025-09-12 18:26:50] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.322s
[2025-09-12 18:26:59] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.339s
[2025-09-12 18:27:09] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.301s
[2025-09-12 18:27:18] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.333s
[2025-09-12 18:27:27] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.331s
[2025-09-12 18:27:37] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.336s
[2025-09-12 18:27:46] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.340s
[2025-09-12 18:27:55] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.362s
[2025-09-12 18:28:05] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.251s
[2025-09-12 18:28:14] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.317s
[2025-09-12 18:28:23] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.331s
[2025-09-12 18:28:33] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.336s
[2025-09-12 18:28:42] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.318s
[2025-09-12 18:28:51] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.320s
[2025-09-12 18:29:01] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.314s
[2025-09-12 18:29:10] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.338s
[2025-09-12 18:29:19] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.329s
[2025-09-12 18:29:29] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.332s
[2025-09-12 18:29:38] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.360s
[2025-09-12 18:29:47] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.256s
[2025-09-12 18:29:57] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.323s
[2025-09-12 18:30:06] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.336s
[2025-09-12 18:30:15] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.334s
[2025-09-12 18:30:25] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.313s
[2025-09-12 18:30:34] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.346s
[2025-09-12 18:30:34] 自旋相关函数计算完成,总耗时 611.10 秒
[2025-09-12 18:30:35] 计算傅里叶变换...
[2025-09-12 18:30:37] 自旋结构因子计算完成
[2025-09-12 18:30:39] 自旋相关函数平均误差: 0.000671
