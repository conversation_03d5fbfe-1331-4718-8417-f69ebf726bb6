[2025-09-11 15:36:33] ✓ 从checkpoint恢复: results/L=4/J2=0.03/J1=0.05/training/checkpoints/final_GCNN.pkl
[2025-09-11 15:36:33]   - 迭代次数: final
[2025-09-11 15:36:33]   - 能量: -53.255415-0.001786j ± 0.042033
[2025-09-11 15:36:33]   - 时间戳: 2025-09-10T23:46:12.690776+08:00
[2025-09-11 15:37:02] ✓ 变分状态参数已从checkpoint恢复
[2025-09-11 15:37:02] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-11 15:37:02] ==================================================
[2025-09-11 15:37:02] GCNN for Shastry-Sutherland Model
[2025-09-11 15:37:02] ==================================================
[2025-09-11 15:37:02] System parameters:
[2025-09-11 15:37:02]   - System size: L=4, N=64
[2025-09-11 15:37:02]   - System parameters: J1=0.06, J2=0.03, Q=0.97
[2025-09-11 15:37:02] --------------------------------------------------
[2025-09-11 15:37:02] Model parameters:
[2025-09-11 15:37:02]   - Number of layers = 4
[2025-09-11 15:37:02]   - Number of features = 4
[2025-09-11 15:37:02]   - Total parameters = 12572
[2025-09-11 15:37:02] --------------------------------------------------
[2025-09-11 15:37:02] Training parameters:
[2025-09-11 15:37:02]   - Learning rate: 0.015
[2025-09-11 15:37:03]   - Total iterations: 1050
[2025-09-11 15:37:03]   - Annealing cycles: 3
[2025-09-11 15:37:03]   - Initial period: 150
[2025-09-11 15:37:03]   - Period multiplier: 2.0
[2025-09-11 15:37:03]   - Temperature range: 0.0-1.0
[2025-09-11 15:37:03]   - Samples: 4096
[2025-09-11 15:37:03]   - Discarded samples: 0
[2025-09-11 15:37:03]   - Chunk size: 2048
[2025-09-11 15:37:03]   - Diagonal shift: 0.2
[2025-09-11 15:37:03]   - Gradient clipping: 1.0
[2025-09-11 15:37:03]   - Checkpoint enabled: interval=105
[2025-09-11 15:37:03]   - Checkpoint directory: results/L=4/J2=0.03/J1=0.06/training/checkpoints
[2025-09-11 15:37:03] --------------------------------------------------
[2025-09-11 15:37:03] Device status:
[2025-09-11 15:37:03]   - Devices model: NVIDIA H200 NVL
[2025-09-11 15:37:03]   - Number of devices: 1
[2025-09-11 15:37:03]   - Sharding: True
[2025-09-11 15:37:03] ============================================================
[2025-09-11 15:38:57] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -53.722188-0.006515j
[2025-09-11 15:40:05] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -53.791005-0.002921j
[2025-09-11 15:40:21] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -53.629260-0.002363j
[2025-09-11 15:40:36] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -53.658324-0.000417j
[2025-09-11 15:40:51] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -53.706301-0.002400j
[2025-09-11 15:41:07] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -53.652538+0.001327j
[2025-09-11 15:41:19] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -53.640270+0.000160j
[2025-09-11 15:41:30] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -53.585497+0.002257j
[2025-09-11 15:41:44] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -53.557512-0.005837j
[2025-09-11 15:41:55] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -53.565320+0.002966j
[2025-09-11 15:42:10] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -53.593915-0.002009j
[2025-09-11 15:42:26] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -53.683560-0.001131j
[2025-09-11 15:42:41] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -53.520395-0.002985j
[2025-09-11 15:42:56] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -53.626733-0.000556j
[2025-09-11 15:43:12] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -53.651092-0.000766j
[2025-09-11 15:43:27] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -53.557557+0.002514j
[2025-09-11 15:43:42] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -53.593681-0.002395j
[2025-09-11 15:43:58] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -53.502471-0.000073j
[2025-09-11 15:44:13] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -53.613415-0.000429j
[2025-09-11 15:44:29] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -53.689777-0.002008j
[2025-09-11 15:44:44] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -53.696864-0.003474j
[2025-09-11 15:45:00] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -53.723382-0.001952j
[2025-09-11 15:45:15] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -53.810520-0.001032j
[2025-09-11 15:45:30] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -53.752523+0.004500j
[2025-09-11 15:45:46] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -53.807073+0.001074j
[2025-09-11 15:46:01] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -53.672382-0.002833j
[2025-09-11 15:46:17] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -53.786512+0.007173j
[2025-09-11 15:46:32] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -53.748747+0.000584j
[2025-09-11 15:46:47] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -53.849402+0.000503j
[2025-09-11 15:47:03] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -53.822535+0.000461j
[2025-09-11 15:47:18] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -53.841419-0.004068j
[2025-09-11 15:47:34] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -53.921345-0.003515j
[2025-09-11 15:47:49] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -53.747879+0.003386j
[2025-09-11 15:48:04] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -53.782494+0.002908j
[2025-09-11 15:48:20] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -53.661572-0.000063j
[2025-09-11 15:48:35] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -53.761371+0.002781j
[2025-09-11 15:48:51] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -53.647379-0.000315j
[2025-09-11 15:49:06] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -53.853578+0.006155j
[2025-09-11 15:49:21] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -53.807427-0.000687j
[2025-09-11 15:49:37] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -53.777984-0.000635j
[2025-09-11 15:49:52] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -53.750070-0.002014j
[2025-09-11 15:50:08] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -53.761185-0.000163j
[2025-09-11 15:50:23] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -53.715248-0.002950j
[2025-09-11 15:50:38] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -53.634220+0.002309j
[2025-09-11 15:50:54] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -53.554132+0.000679j
[2025-09-11 15:51:09] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -53.507119-0.002050j
[2025-09-11 15:51:25] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -53.692827-0.001873j
[2025-09-11 15:51:40] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -53.677001-0.001292j
[2025-09-11 15:51:55] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -53.812885-0.002270j
[2025-09-11 15:52:11] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -53.724850-0.000724j
[2025-09-11 15:52:26] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -53.849864-0.000120j
[2025-09-11 15:52:42] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -53.785563+0.001536j
[2025-09-11 15:52:57] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -53.673901+0.000598j
[2025-09-11 15:53:12] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -53.670920+0.003485j
[2025-09-11 15:53:28] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -53.729749+0.001002j
[2025-09-11 15:53:43] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -53.723872-0.001812j
[2025-09-11 15:53:58] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -53.698372-0.003238j
[2025-09-11 15:54:14] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -53.794080+0.001937j
[2025-09-11 15:54:29] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -53.615917-0.002045j
[2025-09-11 15:54:44] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -53.780863+0.000028j
[2025-09-11 15:55:00] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -53.756453+0.005141j
[2025-09-11 15:55:15] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -53.588383+0.003666j
[2025-09-11 15:55:30] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -53.550072-0.002357j
[2025-09-11 15:55:46] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -53.523521-0.000075j
[2025-09-11 15:56:01] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -53.618637+0.004338j
[2025-09-11 15:56:17] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -53.713150-0.001645j
[2025-09-11 15:56:32] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -53.682174+0.002052j
[2025-09-11 15:56:47] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -53.733685-0.002174j
[2025-09-11 15:57:03] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -53.682587+0.000273j
[2025-09-11 15:57:18] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -53.671571+0.001995j
[2025-09-11 15:57:33] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -53.673872-0.001061j
[2025-09-11 15:57:49] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -53.723017-0.000676j
[2025-09-11 15:58:04] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -53.712551-0.002213j
[2025-09-11 15:58:20] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -53.813521-0.002173j
[2025-09-11 15:58:35] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -53.775187+0.001413j
[2025-09-11 15:58:50] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -53.635220-0.000953j
[2025-09-11 15:59:06] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -53.679932+0.000757j
[2025-09-11 15:59:21] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -53.506937+0.001006j
[2025-09-11 15:59:37] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -53.693397-0.005530j
[2025-09-11 15:59:52] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -53.556099-0.002488j
[2025-09-11 16:00:08] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -53.604867-0.000691j
[2025-09-11 16:00:23] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -53.648547-0.000359j
[2025-09-11 16:00:38] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -53.769548+0.000545j
[2025-09-11 16:00:54] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -53.671242-0.001660j
[2025-09-11 16:01:09] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -53.628221+0.001595j
[2025-09-11 16:01:25] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -53.585853+0.003423j
[2025-09-11 16:01:40] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -53.647308-0.004658j
[2025-09-11 16:01:55] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -53.534064+0.001260j
[2025-09-11 16:02:11] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -53.709630-0.002190j
[2025-09-11 16:02:26] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -53.692967+0.000574j
[2025-09-11 16:02:42] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -53.698237-0.003336j
[2025-09-11 16:02:57] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -53.631186+0.002540j
[2025-09-11 16:03:12] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -53.674415-0.003595j
[2025-09-11 16:03:28] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -53.645085+0.003931j
[2025-09-11 16:03:43] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -53.610258-0.000705j
[2025-09-11 16:03:59] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -53.659964+0.000216j
[2025-09-11 16:04:14] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -53.567598+0.002260j
[2025-09-11 16:04:29] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -53.657297+0.000502j
[2025-09-11 16:04:45] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -53.545290+0.002088j
[2025-09-11 16:05:00] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -53.627249+0.004654j
[2025-09-11 16:05:15] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -53.581986+0.003694j
[2025-09-11 16:05:31] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -53.538477+0.002317j
[2025-09-11 16:05:46] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -53.675285+0.004632j
[2025-09-11 16:06:02] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -53.653492+0.000734j
[2025-09-11 16:06:17] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -53.634710+0.000669j
[2025-09-11 16:06:17] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-11 16:06:32] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -53.658420+0.001917j
[2025-09-11 16:06:48] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -53.576409-0.000481j
[2025-09-11 16:07:03] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -53.655735-0.001403j
[2025-09-11 16:07:19] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -53.586191-0.000033j
[2025-09-11 16:07:34] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -53.702181+0.001213j
[2025-09-11 16:07:49] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -53.686978+0.000175j
[2025-09-11 16:08:05] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -53.743439+0.001194j
[2025-09-11 16:08:20] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -53.678995-0.002713j
[2025-09-11 16:08:36] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -53.521947-0.001342j
[2025-09-11 16:08:51] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -53.497224-0.000437j
[2025-09-11 16:09:06] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -53.570433+0.004638j
[2025-09-11 16:09:22] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -53.512711+0.000790j
[2025-09-11 16:09:37] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -53.631528+0.002789j
[2025-09-11 16:09:52] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -53.711808+0.000785j
[2025-09-11 16:10:08] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -53.713420-0.000873j
[2025-09-11 16:10:23] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -53.657621-0.001876j
[2025-09-11 16:10:39] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -53.591632+0.001073j
[2025-09-11 16:10:54] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -53.719181+0.000598j
[2025-09-11 16:11:09] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -53.758856+0.001310j
[2025-09-11 16:11:25] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -53.765095-0.000192j
[2025-09-11 16:11:40] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -53.719072-0.003538j
[2025-09-11 16:11:56] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -53.739105-0.000830j
[2025-09-11 16:12:11] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -53.801859+0.000970j
[2025-09-11 16:12:26] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -53.706674+0.000468j
[2025-09-11 16:12:42] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -53.693340-0.000329j
[2025-09-11 16:12:57] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -53.672823-0.005069j
[2025-09-11 16:13:13] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -53.749366-0.002655j
[2025-09-11 16:13:28] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -53.754445+0.002761j
[2025-09-11 16:13:44] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -53.775708+0.003419j
[2025-09-11 16:13:59] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -53.582946+0.000451j
[2025-09-11 16:14:15] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -53.617384-0.000491j
[2025-09-11 16:14:30] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -53.701664+0.003281j
[2025-09-11 16:14:45] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -53.633541+0.000289j
[2025-09-11 16:15:01] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -53.654630+0.000891j
[2025-09-11 16:15:16] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -53.706581-0.000054j
[2025-09-11 16:15:32] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -53.688090+0.000036j
[2025-09-11 16:15:45] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -53.676363+0.000778j
[2025-09-11 16:15:55] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -53.705375-0.002146j
[2025-09-11 16:16:05] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -53.720699-0.002613j
[2025-09-11 16:16:15] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -53.719176+0.000987j
[2025-09-11 16:16:26] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -53.700264-0.002875j
[2025-09-11 16:16:36] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -53.744331-0.002007j
[2025-09-11 16:16:46] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -53.713708-0.001349j
[2025-09-11 16:17:01] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -53.644913-0.000245j
[2025-09-11 16:17:16] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -53.589440-0.000546j
[2025-09-11 16:17:16] RESTART #1 | Period: 300
[2025-09-11 16:17:31] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -53.635782+0.000986j
[2025-09-11 16:17:47] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -53.764924+0.003321j
[2025-09-11 16:18:02] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -53.675532-0.007505j
[2025-09-11 16:18:18] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -53.674019+0.000965j
[2025-09-11 16:18:33] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -53.588340-0.000941j
[2025-09-11 16:18:48] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -53.621863+0.001547j
[2025-09-11 16:19:04] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -53.557733-0.003888j
[2025-09-11 16:19:19] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -53.619279+0.003836j
[2025-09-11 16:19:34] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -53.542176+0.002236j
[2025-09-11 16:19:50] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -53.625166-0.001601j
[2025-09-11 16:20:05] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -53.646532+0.001714j
[2025-09-11 16:20:21] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -53.588366+0.003436j
[2025-09-11 16:20:36] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -53.641625+0.000896j
[2025-09-11 16:20:51] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -53.663332+0.001127j
[2025-09-11 16:21:07] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -53.755675-0.001403j
[2025-09-11 16:21:22] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -53.669572-0.002789j
[2025-09-11 16:21:38] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -53.690406-0.001546j
[2025-09-11 16:21:53] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -53.687730+0.001390j
[2025-09-11 16:22:09] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -53.642295+0.000715j
[2025-09-11 16:22:24] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -53.717755+0.001626j
[2025-09-11 16:22:39] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -53.776410+0.000439j
[2025-09-11 16:22:55] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -53.718077-0.004113j
[2025-09-11 16:23:10] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -53.681249-0.003747j
[2025-09-11 16:23:26] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -53.759100+0.000520j
[2025-09-11 16:23:41] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -53.774210+0.000591j
[2025-09-11 16:23:57] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -53.763481-0.002828j
[2025-09-11 16:24:12] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -53.815858-0.001735j
[2025-09-11 16:24:28] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -53.696689+0.001434j
[2025-09-11 16:24:43] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -53.588983-0.005117j
[2025-09-11 16:24:58] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -53.736340-0.000380j
[2025-09-11 16:25:14] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -53.629068-0.001856j
[2025-09-11 16:25:29] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -53.662156-0.000287j
[2025-09-11 16:25:45] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -53.728442+0.003710j
[2025-09-11 16:26:00] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -53.731094-0.002712j
[2025-09-11 16:26:16] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -53.669153-0.000292j
[2025-09-11 16:26:27] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -53.610620+0.000050j
[2025-09-11 16:26:39] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -53.588900-0.003647j
[2025-09-11 16:26:52] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -53.734919+0.001999j
[2025-09-11 16:27:04] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -53.768318+0.001243j
[2025-09-11 16:27:19] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -53.703638-0.002990j
[2025-09-11 16:27:35] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -53.716841+0.000601j
[2025-09-11 16:27:50] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -53.737854+0.005183j
[2025-09-11 16:28:05] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -53.731215+0.001295j
[2025-09-11 16:28:21] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -53.623795+0.001070j
[2025-09-11 16:28:36] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -53.807345-0.002237j
[2025-09-11 16:28:52] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -53.759328+0.000423j
[2025-09-11 16:29:07] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -53.686779-0.000981j
[2025-09-11 16:29:22] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -53.656273+0.002867j
[2025-09-11 16:29:38] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -53.705484+0.000256j
[2025-09-11 16:29:53] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -53.673572+0.006019j
[2025-09-11 16:30:08] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -53.704856+0.000141j
[2025-09-11 16:30:24] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -53.717028+0.000433j
[2025-09-11 16:30:39] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -53.610619-0.000478j
[2025-09-11 16:30:54] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -53.570313-0.001591j
[2025-09-11 16:31:10] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -53.564339-0.001072j
[2025-09-11 16:31:25] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -53.636583+0.002009j
[2025-09-11 16:31:40] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -53.619610-0.000156j
[2025-09-11 16:31:56] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -53.712152+0.002142j
[2025-09-11 16:32:11] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -53.698929-0.001606j
[2025-09-11 16:32:26] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -53.664883-0.001750j
[2025-09-11 16:32:26] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-11 16:32:42] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -53.674733-0.000488j
[2025-09-11 16:32:57] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -53.627620-0.000126j
[2025-09-11 16:33:12] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -53.703931-0.001159j
[2025-09-11 16:33:28] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -53.654200+0.003783j
[2025-09-11 16:33:43] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -53.646094+0.004811j
[2025-09-11 16:33:59] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -53.570475-0.002758j
[2025-09-11 16:34:14] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -53.727348-0.000349j
[2025-09-11 16:34:29] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -53.637891-0.001301j
[2025-09-11 16:34:45] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -53.664719-0.001952j
[2025-09-11 16:35:00] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -53.629649-0.002073j
[2025-09-11 16:35:15] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -53.714986-0.000612j
[2025-09-11 16:35:31] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -53.633117+0.004248j
[2025-09-11 16:35:46] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -53.625978+0.001371j
[2025-09-11 16:36:01] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -53.664795+0.001457j
[2025-09-11 16:36:17] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -53.650018-0.001991j
[2025-09-11 16:36:32] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -53.593506+0.000228j
[2025-09-11 16:36:48] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -53.612638-0.002521j
[2025-09-11 16:37:03] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -53.670011-0.000922j
[2025-09-11 16:37:18] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -53.593422-0.002795j
[2025-09-11 16:37:34] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -53.586661+0.004891j
[2025-09-11 16:37:49] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -53.684271+0.002336j
[2025-09-11 16:38:04] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -53.677092+0.002149j
[2025-09-11 16:38:20] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -53.609113+0.000534j
[2025-09-11 16:38:35] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -53.706794-0.002227j
[2025-09-11 16:38:51] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -53.485586-0.001720j
[2025-09-11 16:39:06] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -53.640643+0.005089j
[2025-09-11 16:39:21] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -53.681806+0.001188j
[2025-09-11 16:39:37] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -53.693539+0.000187j
[2025-09-11 16:39:52] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -53.730099+0.001006j
[2025-09-11 16:40:08] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -53.762165+0.005490j
[2025-09-11 16:40:23] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -53.716306-0.001078j
[2025-09-11 16:40:38] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -53.868936+0.000899j
[2025-09-11 16:40:54] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -53.792374+0.000782j
[2025-09-11 16:41:09] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -53.674148+0.000284j
[2025-09-11 16:41:25] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -53.702356-0.001563j
[2025-09-11 16:41:40] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -53.697184-0.000043j
[2025-09-11 16:41:55] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -53.711344+0.002518j
[2025-09-11 16:42:11] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -53.679310+0.000653j
[2025-09-11 16:42:26] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -53.705858-0.002766j
[2025-09-11 16:42:42] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -53.531423+0.000280j
[2025-09-11 16:42:57] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -53.494767+0.001876j
[2025-09-11 16:43:12] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -53.821011+0.002014j
[2025-09-11 16:43:28] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -53.654394-0.002199j
[2025-09-11 16:43:43] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -53.552636+0.001088j
[2025-09-11 16:43:59] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -53.557877-0.005445j
[2025-09-11 16:44:14] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -53.604993+0.000164j
[2025-09-11 16:44:29] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -53.615685-0.002010j
[2025-09-11 16:44:45] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -53.691530+0.001098j
[2025-09-11 16:45:00] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -53.791055-0.001213j
[2025-09-11 16:45:15] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -53.826752+0.004399j
[2025-09-11 16:45:31] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -53.734010+0.000556j
[2025-09-11 16:45:46] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -53.669766+0.003219j
[2025-09-11 16:46:01] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -53.677363-0.000724j
[2025-09-11 16:46:16] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -53.732055+0.002122j
[2025-09-11 16:46:32] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -53.645888+0.003176j
[2025-09-11 16:46:47] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -53.668444-0.003817j
[2025-09-11 16:47:02] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -53.722998+0.003790j
[2025-09-11 16:47:18] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -53.753573+0.001017j
[2025-09-11 16:47:33] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -53.718351+0.000274j
[2025-09-11 16:47:49] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -53.627691+0.003529j
[2025-09-11 16:48:04] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -53.627728+0.001909j
[2025-09-11 16:48:19] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -53.488989-0.002971j
[2025-09-11 16:48:35] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -53.449033-0.001594j
[2025-09-11 16:48:50] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -53.661649-0.001182j
[2025-09-11 16:49:05] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -53.760788+0.002291j
[2025-09-11 16:49:21] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -53.727491-0.004135j
[2025-09-11 16:49:36] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -53.670261+0.001031j
[2025-09-11 16:49:52] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -53.599840+0.000699j
[2025-09-11 16:50:07] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -53.579661+0.000104j
[2025-09-11 16:50:22] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -53.504802+0.002069j
[2025-09-11 16:50:38] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -53.736544+0.000206j
[2025-09-11 16:50:53] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -53.655531+0.003427j
[2025-09-11 16:51:08] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -53.632760+0.002830j
[2025-09-11 16:51:24] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -53.582474+0.000341j
[2025-09-11 16:51:39] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -53.779884+0.002896j
[2025-09-11 16:51:54] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -53.693241-0.000934j
[2025-09-11 16:52:10] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -53.800855+0.001928j
[2025-09-11 16:52:25] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -53.781257+0.000270j
[2025-09-11 16:52:40] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -53.737265-0.000147j
[2025-09-11 16:52:56] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -53.754207+0.001243j
[2025-09-11 16:53:11] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -53.705625+0.001273j
[2025-09-11 16:53:26] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -53.742787+0.002089j
[2025-09-11 16:53:42] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -53.641132-0.001452j
[2025-09-11 16:53:57] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -53.661809+0.002580j
[2025-09-11 16:54:13] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -53.690590+0.002789j
[2025-09-11 16:54:28] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -53.527272+0.001520j
[2025-09-11 16:54:43] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -53.520576+0.001418j
[2025-09-11 16:54:59] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -53.616427+0.004796j
[2025-09-11 16:55:14] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -53.734662+0.000162j
[2025-09-11 16:55:29] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -53.634633-0.003533j
[2025-09-11 16:55:45] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -53.665821-0.001675j
[2025-09-11 16:56:00] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -53.718576+0.001135j
[2025-09-11 16:56:16] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -53.566855-0.000588j
[2025-09-11 16:56:31] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -53.547253+0.002404j
[2025-09-11 16:56:46] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -53.635018-0.002465j
[2025-09-11 16:57:02] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -53.696381+0.000527j
[2025-09-11 16:57:17] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -53.698075+0.000030j
[2025-09-11 16:57:32] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -53.541537+0.000674j
[2025-09-11 16:57:48] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -53.674152-0.001497j
[2025-09-11 16:58:03] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -53.809614-0.000935j
[2025-09-11 16:58:18] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -53.744944-0.001876j
[2025-09-11 16:58:34] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -53.646511-0.002330j
[2025-09-11 16:58:49] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -53.590755+0.001907j
[2025-09-11 16:59:04] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -53.522665-0.000160j
[2025-09-11 16:59:20] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -53.535489-0.003944j
[2025-09-11 16:59:20] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-11 16:59:35] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -53.617638-0.002504j
[2025-09-11 16:59:50] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -53.656367-0.002998j
[2025-09-11 17:00:06] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -53.635529+0.001174j
[2025-09-11 17:00:21] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -53.639140+0.004291j
[2025-09-11 17:00:31] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -53.699597-0.005289j
[2025-09-11 17:00:41] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -53.624164-0.003346j
[2025-09-11 17:00:52] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -53.660103+0.003299j
[2025-09-11 17:01:02] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -53.664994-0.002430j
[2025-09-11 17:01:12] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -53.694907-0.000039j
[2025-09-11 17:01:23] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -53.734253-0.002521j
[2025-09-11 17:01:38] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -53.708039-0.002069j
[2025-09-11 17:01:54] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -53.710497+0.002291j
[2025-09-11 17:02:09] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -53.617133+0.001239j
[2025-09-11 17:02:24] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -53.691264-0.003523j
[2025-09-11 17:02:40] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -53.533085+0.001777j
[2025-09-11 17:02:55] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -53.636604+0.000487j
[2025-09-11 17:03:11] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -53.692991+0.004645j
[2025-09-11 17:03:26] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -53.677514+0.000680j
[2025-09-11 17:03:41] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -53.688791+0.000302j
[2025-09-11 17:03:57] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -53.748147-0.001963j
[2025-09-11 17:04:12] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -53.600092-0.000418j
[2025-09-11 17:04:28] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -53.586725+0.001087j
[2025-09-11 17:04:43] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -53.594145-0.002619j
[2025-09-11 17:04:59] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -53.808218+0.000764j
[2025-09-11 17:05:14] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -53.725301-0.002830j
[2025-09-11 17:05:30] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -53.681849-0.001156j
[2025-09-11 17:05:45] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -53.821275-0.005306j
[2025-09-11 17:06:00] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -53.713440+0.004542j
[2025-09-11 17:06:16] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -53.499109+0.002171j
[2025-09-11 17:06:31] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -53.677997-0.001009j
[2025-09-11 17:06:47] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -53.629407+0.002560j
[2025-09-11 17:07:02] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -53.494306+0.003575j
[2025-09-11 17:07:18] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -53.620485+0.000951j
[2025-09-11 17:07:33] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -53.666013+0.000052j
[2025-09-11 17:07:48] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -53.606595+0.003066j
[2025-09-11 17:08:04] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -53.568622+0.000117j
[2025-09-11 17:08:19] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -53.560921+0.001724j
[2025-09-11 17:08:35] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -53.671979+0.003757j
[2025-09-11 17:08:50] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -53.540502+0.002605j
[2025-09-11 17:09:05] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -53.576112+0.001375j
[2025-09-11 17:09:21] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -53.645628-0.000208j
[2025-09-11 17:09:36] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -53.691269+0.003935j
[2025-09-11 17:09:52] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -53.674295-0.006494j
[2025-09-11 17:10:07] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -53.743154-0.004823j
[2025-09-11 17:10:23] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -53.512983-0.003478j
[2025-09-11 17:10:38] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -53.546144+0.003177j
[2025-09-11 17:10:53] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -53.722207+0.000014j
[2025-09-11 17:11:04] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -53.725678+0.001140j
[2025-09-11 17:11:17] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -53.628369+0.000939j
[2025-09-11 17:11:29] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -53.702878-0.004983j
[2025-09-11 17:11:42] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -53.738880-0.002675j
[2025-09-11 17:11:57] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -53.692739+0.003342j
[2025-09-11 17:12:12] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -53.669598+0.004910j
[2025-09-11 17:12:28] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -53.676346+0.003093j
[2025-09-11 17:12:43] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -53.679862-0.001602j
[2025-09-11 17:12:58] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -53.773547+0.001541j
[2025-09-11 17:13:14] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -53.704897-0.004328j
[2025-09-11 17:13:29] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -53.704209-0.002301j
[2025-09-11 17:13:44] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -53.622926+0.002622j
[2025-09-11 17:14:00] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -53.666744-0.001992j
[2025-09-11 17:14:15] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -53.625222-0.000657j
[2025-09-11 17:14:30] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -53.584987+0.002242j
[2025-09-11 17:14:46] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -53.608165-0.001011j
[2025-09-11 17:15:01] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -53.701130+0.000780j
[2025-09-11 17:15:16] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -53.667960+0.004027j
[2025-09-11 17:15:32] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -53.663866+0.002450j
[2025-09-11 17:15:47] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -53.550028+0.000767j
[2025-09-11 17:16:03] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -53.645534+0.004936j
[2025-09-11 17:16:18] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -53.596366+0.002654j
[2025-09-11 17:16:33] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -53.564059+0.004135j
[2025-09-11 17:16:49] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -53.698313+0.002471j
[2025-09-11 17:17:04] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -53.599677+0.003275j
[2025-09-11 17:17:19] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -53.686378-0.014375j
[2025-09-11 17:17:35] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -53.554827-0.001444j
[2025-09-11 17:17:50] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -53.695350-0.004688j
[2025-09-11 17:18:05] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -53.632221+0.003218j
[2025-09-11 17:18:21] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -53.656073-0.003104j
[2025-09-11 17:18:36] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -53.754579+0.001815j
[2025-09-11 17:18:52] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -53.733280-0.002672j
[2025-09-11 17:19:07] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -53.624520+0.000177j
[2025-09-11 17:19:22] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -53.761175+0.002075j
[2025-09-11 17:19:38] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -53.709842+0.003194j
[2025-09-11 17:19:53] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -53.667520+0.000565j
[2025-09-11 17:20:09] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -53.689489+0.003315j
[2025-09-11 17:20:24] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -53.700720+0.000373j
[2025-09-11 17:20:39] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -53.638860+0.001349j
[2025-09-11 17:20:55] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -53.609846+0.000477j
[2025-09-11 17:21:10] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -53.616352-0.000820j
[2025-09-11 17:21:25] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -53.648336-0.000645j
[2025-09-11 17:21:41] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -53.810068-0.001876j
[2025-09-11 17:21:56] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -53.858031-0.000226j
[2025-09-11 17:22:11] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -53.709000-0.000111j
[2025-09-11 17:22:27] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -53.665775-0.000505j
[2025-09-11 17:22:42] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -53.586092-0.000671j
[2025-09-11 17:22:58] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -53.671329-0.004070j
[2025-09-11 17:23:13] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -53.697282-0.002015j
[2025-09-11 17:23:28] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -53.726080+0.001533j
[2025-09-11 17:23:44] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -53.566788+0.002638j
[2025-09-11 17:23:59] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -53.631776-0.000769j
[2025-09-11 17:24:14] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -53.662439-0.000622j
[2025-09-11 17:24:30] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -53.616471+0.001779j
[2025-09-11 17:24:45] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -53.589806+0.001649j
[2025-09-11 17:25:00] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -53.533683+0.003056j
[2025-09-11 17:25:16] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -53.697209-0.000968j
[2025-09-11 17:25:31] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -53.714912-0.001642j
[2025-09-11 17:25:31] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-11 17:25:47] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -53.679166-0.000120j
[2025-09-11 17:26:02] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -53.729305-0.000324j
[2025-09-11 17:26:17] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -53.688935-0.001257j
[2025-09-11 17:26:32] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -53.721701+0.000643j
[2025-09-11 17:26:48] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -53.700502+0.000544j
[2025-09-11 17:27:03] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -53.770528+0.000602j
[2025-09-11 17:27:18] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -53.840332+0.002019j
[2025-09-11 17:27:34] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -53.763341+0.003025j
[2025-09-11 17:27:49] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -53.801131-0.001925j
[2025-09-11 17:28:04] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -53.815127+0.001999j
[2025-09-11 17:28:20] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -53.782138+0.001506j
[2025-09-11 17:28:35] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -53.880198+0.000924j
[2025-09-11 17:28:51] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -53.797589-0.001685j
[2025-09-11 17:29:06] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -53.763337+0.003006j
[2025-09-11 17:29:21] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -53.753117+0.002622j
[2025-09-11 17:29:37] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -53.742002-0.000039j
[2025-09-11 17:29:52] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -53.709969+0.005049j
[2025-09-11 17:30:07] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -53.718288+0.000373j
[2025-09-11 17:30:23] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -53.582357+0.000789j
[2025-09-11 17:30:38] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -53.595290+0.001306j
[2025-09-11 17:30:53] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -53.639060-0.001184j
[2025-09-11 17:31:09] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -53.675142-0.000826j
[2025-09-11 17:31:24] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -53.636182+0.001999j
[2025-09-11 17:31:39] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -53.657856+0.004103j
[2025-09-11 17:31:55] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -53.587814-0.000690j
[2025-09-11 17:32:10] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -53.599049+0.000193j
[2025-09-11 17:32:25] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -53.564856-0.004211j
[2025-09-11 17:32:41] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -53.502233+0.000434j
[2025-09-11 17:32:56] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -53.694647+0.002819j
[2025-09-11 17:33:12] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -53.769060-0.004003j
[2025-09-11 17:33:12] RESTART #2 | Period: 600
[2025-09-11 17:33:27] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -53.764773-0.003125j
[2025-09-11 17:33:42] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -53.763595+0.000245j
[2025-09-11 17:33:58] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -53.746515-0.000062j
[2025-09-11 17:34:13] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -53.696690-0.000936j
[2025-09-11 17:34:28] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -53.743355-0.001961j
[2025-09-11 17:34:44] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -53.644346+0.003429j
[2025-09-11 17:34:59] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -53.536126-0.001871j
[2025-09-11 17:35:15] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -53.724615-0.005477j
[2025-09-11 17:35:30] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -53.742776-0.003079j
[2025-09-11 17:35:45] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -53.746694-0.002218j
[2025-09-11 17:36:01] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -53.763939+0.005256j
[2025-09-11 17:36:16] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -53.697093-0.001158j
[2025-09-11 17:36:31] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -53.677280+0.002536j
[2025-09-11 17:36:47] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -53.614391-0.001885j
[2025-09-11 17:37:02] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -53.571171+0.000070j
[2025-09-11 17:37:18] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -53.624749-0.000120j
[2025-09-11 17:37:33] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -53.663194+0.000704j
[2025-09-11 17:37:48] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -53.728038+0.000221j
[2025-09-11 17:38:04] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -53.713682-0.002386j
[2025-09-11 17:38:19] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -53.647708-0.002954j
[2025-09-11 17:38:34] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -53.517855-0.001369j
[2025-09-11 17:38:50] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -53.528689-0.000834j
[2025-09-11 17:39:05] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -53.515221+0.002212j
[2025-09-11 17:39:20] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -53.537404-0.001415j
[2025-09-11 17:39:36] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -53.540987-0.004236j
[2025-09-11 17:39:51] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -53.660769-0.001828j
[2025-09-11 17:40:06] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -53.694287+0.001097j
[2025-09-11 17:40:22] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -53.662722-0.000734j
[2025-09-11 17:40:37] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -53.602753-0.002026j
[2025-09-11 17:40:52] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -53.591496+0.004115j
[2025-09-11 17:41:08] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -53.634472-0.001445j
[2025-09-11 17:41:23] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -53.498529+0.000722j
[2025-09-11 17:41:39] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -53.631867-0.003332j
[2025-09-11 17:41:54] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -53.655216+0.003171j
[2025-09-11 17:42:09] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -53.560178+0.002770j
[2025-09-11 17:42:25] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -53.659928+0.000160j
[2025-09-11 17:42:40] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -53.690127+0.000869j
[2025-09-11 17:42:55] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -53.707293+0.000513j
[2025-09-11 17:43:11] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -53.735524-0.000278j
[2025-09-11 17:43:26] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -53.758790+0.004289j
[2025-09-11 17:43:41] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -53.696221+0.003947j
[2025-09-11 17:43:57] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -53.699550+0.006600j
[2025-09-11 17:44:12] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -53.671388-0.005416j
[2025-09-11 17:44:27] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -53.719637-0.004128j
[2025-09-11 17:44:43] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -53.737901+0.000003j
[2025-09-11 17:44:58] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -53.736568-0.003228j
[2025-09-11 17:45:13] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -53.665035-0.001512j
[2025-09-11 17:45:27] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -53.768298+0.005688j
[2025-09-11 17:45:37] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -53.741836-0.000711j
[2025-09-11 17:45:47] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -53.707344+0.001756j
[2025-09-11 17:45:57] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -53.648585+0.002142j
[2025-09-11 17:46:08] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -53.720697-0.000712j
[2025-09-11 17:46:18] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -53.578221-0.000066j
[2025-09-11 17:46:28] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -53.748056+0.000251j
[2025-09-11 17:46:39] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -53.635055-0.003532j
[2025-09-11 17:46:55] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -53.733571+0.001185j
[2025-09-11 17:47:10] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -53.691117-0.000956j
[2025-09-11 17:47:25] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -53.679522-0.001284j
[2025-09-11 17:47:41] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -53.589657-0.000229j
[2025-09-11 17:47:56] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -53.615959+0.000283j
[2025-09-11 17:48:12] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -53.506965+0.001322j
[2025-09-11 17:48:27] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -53.593579-0.000540j
[2025-09-11 17:48:43] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -53.592534-0.001160j
[2025-09-11 17:48:58] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -53.503770+0.002261j
[2025-09-11 17:49:13] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -53.638741+0.002808j
[2025-09-11 17:49:29] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -53.571676-0.002955j
[2025-09-11 17:49:44] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -53.646963-0.002448j
[2025-09-11 17:50:00] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -53.591878-0.000856j
[2025-09-11 17:50:15] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -53.688056-0.000454j
[2025-09-11 17:50:30] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -53.680800-0.003445j
[2025-09-11 17:50:46] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -53.714021+0.001590j
[2025-09-11 17:51:01] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -53.751750+0.000780j
[2025-09-11 17:51:17] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -53.622139-0.001308j
[2025-09-11 17:51:32] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -53.529430-0.002425j
[2025-09-11 17:51:48] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -53.591639-0.000921j
[2025-09-11 17:51:48] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-11 17:52:03] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -53.689129-0.003838j
[2025-09-11 17:52:18] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -53.649988+0.001356j
[2025-09-11 17:52:34] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -53.752259-0.000103j
[2025-09-11 17:52:49] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -53.765425+0.001647j
[2025-09-11 17:53:05] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -53.676660+0.001450j
[2025-09-11 17:53:20] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -53.563410-0.002050j
[2025-09-11 17:53:35] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -53.668904+0.001334j
[2025-09-11 17:53:51] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -53.635253+0.000708j
[2025-09-11 17:54:06] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -53.562613+0.002206j
[2025-09-11 17:54:21] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -53.496505-0.002379j
[2025-09-11 17:54:37] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -53.619768+0.001907j
[2025-09-11 17:54:52] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -53.666044-0.004113j
[2025-09-11 17:55:08] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -53.567031+0.003795j
[2025-09-11 17:55:23] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -53.706783+0.004487j
[2025-09-11 17:55:39] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -53.619054-0.000347j
[2025-09-11 17:55:54] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -53.679100-0.001715j
[2025-09-11 17:56:09] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -53.740330-0.002389j
[2025-09-11 17:56:20] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -53.772613-0.002758j
[2025-09-11 17:56:34] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -53.737528-0.003710j
[2025-09-11 17:56:45] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -53.765000+0.001318j
[2025-09-11 17:56:58] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -53.637832+0.002015j
[2025-09-11 17:57:14] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -53.684427-0.002785j
[2025-09-11 17:57:29] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -53.627179+0.001368j
[2025-09-11 17:57:44] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -53.724208-0.001556j
[2025-09-11 17:58:00] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -53.602288-0.001894j
[2025-09-11 17:58:15] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -53.548889-0.002613j
[2025-09-11 17:58:31] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -53.519978-0.004037j
[2025-09-11 17:58:46] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -53.779064-0.002408j
[2025-09-11 17:59:01] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -53.785037-0.004003j
[2025-09-11 17:59:17] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -53.698889-0.002546j
[2025-09-11 17:59:32] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -53.732330+0.000759j
[2025-09-11 17:59:48] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -53.793565-0.000623j
[2025-09-11 18:00:03] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -53.669300+0.000021j
[2025-09-11 18:00:18] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -53.696669-0.002071j
[2025-09-11 18:00:34] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -53.615622-0.000799j
[2025-09-11 18:00:49] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -53.610281-0.001288j
[2025-09-11 18:01:04] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -53.715169+0.000395j
[2025-09-11 18:01:20] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -53.829459-0.002323j
[2025-09-11 18:01:35] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -53.758983-0.000848j
[2025-09-11 18:01:51] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -53.760307+0.000189j
[2025-09-11 18:02:06] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -53.821212+0.001508j
[2025-09-11 18:02:21] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -53.784225+0.000412j
[2025-09-11 18:02:37] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -53.624660+0.000760j
[2025-09-11 18:02:52] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -53.702120-0.000392j
[2025-09-11 18:03:08] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -53.719399+0.000451j
[2025-09-11 18:03:23] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -53.648759+0.001409j
[2025-09-11 18:03:38] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -53.599355-0.002637j
[2025-09-11 18:03:54] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -53.720084+0.002864j
[2025-09-11 18:04:09] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -53.687548+0.001119j
[2025-09-11 18:04:24] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -53.627721+0.001975j
[2025-09-11 18:04:40] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -53.616666+0.000078j
[2025-09-11 18:04:55] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -53.509771+0.000660j
[2025-09-11 18:05:10] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -53.619086-0.000427j
[2025-09-11 18:05:26] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -53.658510-0.001301j
[2025-09-11 18:05:41] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -53.610544-0.003093j
[2025-09-11 18:05:56] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -53.644572+0.000254j
[2025-09-11 18:06:12] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -53.648451+0.000461j
[2025-09-11 18:06:27] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -53.729867+0.000321j
[2025-09-11 18:06:42] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -53.814937+0.002181j
[2025-09-11 18:06:58] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -53.715337+0.004060j
[2025-09-11 18:07:13] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -53.754390-0.003286j
[2025-09-11 18:07:28] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -53.579209+0.000873j
[2025-09-11 18:07:44] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -53.602459-0.001350j
[2025-09-11 18:07:59] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -53.646485+0.000233j
[2025-09-11 18:08:15] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -53.604194-0.002025j
[2025-09-11 18:08:30] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -53.712411+0.001749j
[2025-09-11 18:08:45] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -53.702376-0.000065j
[2025-09-11 18:09:01] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -53.654833+0.000517j
[2025-09-11 18:09:16] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -53.707758-0.001910j
[2025-09-11 18:09:31] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -53.693705+0.006231j
[2025-09-11 18:09:47] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -53.756752-0.000669j
[2025-09-11 18:10:02] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -53.711482+0.000927j
[2025-09-11 18:10:17] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -53.675609+0.001109j
[2025-09-11 18:10:33] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -53.732335+0.001733j
[2025-09-11 18:10:48] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -53.648973+0.000794j
[2025-09-11 18:11:03] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -53.731436-0.003590j
[2025-09-11 18:11:19] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -53.816176-0.000566j
[2025-09-11 18:11:34] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -53.652556-0.002006j
[2025-09-11 18:11:50] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -53.742889+0.005286j
[2025-09-11 18:12:05] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -53.787327-0.001968j
[2025-09-11 18:12:20] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -53.721526+0.002869j
[2025-09-11 18:12:36] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -53.584842-0.002021j
[2025-09-11 18:12:51] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -53.701707-0.000251j
[2025-09-11 18:13:06] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -53.617573+0.000570j
[2025-09-11 18:13:22] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -53.626463-0.001534j
[2025-09-11 18:13:37] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -53.690005-0.001039j
[2025-09-11 18:13:53] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -53.662499-0.007532j
[2025-09-11 18:14:08] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -53.738378-0.001718j
[2025-09-11 18:14:23] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -53.806025+0.000153j
[2025-09-11 18:14:39] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -53.778811+0.015024j
[2025-09-11 18:14:54] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -53.854332+0.001979j
[2025-09-11 18:15:09] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -53.869661-0.002550j
[2025-09-11 18:15:25] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -53.801463-0.002741j
[2025-09-11 18:15:40] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -53.626714-0.001968j
[2025-09-11 18:15:56] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -53.672949+0.002379j
[2025-09-11 18:16:11] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -53.588492-0.003605j
[2025-09-11 18:16:26] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -53.668930+0.001525j
[2025-09-11 18:16:42] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -53.626559+0.003649j
[2025-09-11 18:16:57] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -53.751893-0.002631j
[2025-09-11 18:17:13] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -53.699268+0.001957j
[2025-09-11 18:17:28] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -53.618312+0.000959j
[2025-09-11 18:17:43] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -53.583244+0.003153j
[2025-09-11 18:17:59] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -53.624159+0.000822j
[2025-09-11 18:18:14] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -53.656519-0.001494j
[2025-09-11 18:18:29] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -53.647695-0.001760j
[2025-09-11 18:18:29] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-11 18:18:45] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -53.502921+0.001068j
[2025-09-11 18:19:00] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -53.648172+0.000086j
[2025-09-11 18:19:16] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -53.584598+0.001697j
[2025-09-11 18:19:31] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -53.721869-0.002660j
[2025-09-11 18:19:46] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -53.709686-0.001160j
[2025-09-11 18:20:02] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -53.652440-0.000810j
[2025-09-11 18:20:17] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -53.767392+0.001916j
[2025-09-11 18:20:32] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -53.644280-0.001846j
[2025-09-11 18:20:48] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -53.768624-0.001307j
[2025-09-11 18:21:03] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -53.692905+0.000604j
[2025-09-11 18:21:18] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -53.622099-0.001645j
[2025-09-11 18:21:34] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -53.722705+0.003574j
[2025-09-11 18:21:49] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -53.808905+0.001958j
[2025-09-11 18:22:05] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -53.618737-0.000878j
[2025-09-11 18:22:20] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -53.539828+0.001791j
[2025-09-11 18:22:35] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -53.577695-0.002065j
[2025-09-11 18:22:51] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -53.460893+0.000444j
[2025-09-11 18:23:06] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -53.641734-0.004639j
[2025-09-11 18:23:21] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -53.716011+0.001301j
[2025-09-11 18:23:37] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -53.600950-0.000572j
[2025-09-11 18:23:52] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -53.631557+0.001231j
[2025-09-11 18:24:07] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -53.658900-0.001022j
[2025-09-11 18:24:23] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -53.558913-0.001679j
[2025-09-11 18:24:38] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -53.641197-0.000616j
[2025-09-11 18:24:54] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -53.643751-0.003008j
[2025-09-11 18:25:09] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -53.736954-0.000007j
[2025-09-11 18:25:24] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -53.624316+0.004274j
[2025-09-11 18:25:40] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -53.613591-0.000679j
[2025-09-11 18:25:55] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -53.538100+0.000690j
[2025-09-11 18:26:11] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -53.609088+0.000392j
[2025-09-11 18:26:26] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -53.628497+0.000829j
[2025-09-11 18:26:41] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -53.553497+0.005061j
[2025-09-11 18:26:57] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -53.697188+0.001342j
[2025-09-11 18:27:12] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -53.880310+0.001972j
[2025-09-11 18:27:27] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -53.911485+0.006089j
[2025-09-11 18:27:43] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -53.886777+0.000450j
[2025-09-11 18:27:58] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -53.798219+0.000240j
[2025-09-11 18:28:14] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -53.874480-0.001240j
[2025-09-11 18:28:29] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -53.819489+0.002550j
[2025-09-11 18:28:44] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -53.904845+0.000097j
[2025-09-11 18:29:00] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -53.762898+0.000142j
[2025-09-11 18:29:15] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -53.822638+0.002404j
[2025-09-11 18:29:31] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -53.801086-0.000120j
[2025-09-11 18:29:46] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -53.689349-0.002911j
[2025-09-11 18:30:01] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -53.661434+0.001946j
[2025-09-11 18:30:17] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -53.705228-0.001168j
[2025-09-11 18:30:32] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -53.692596+0.003639j
[2025-09-11 18:30:44] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -53.818440+0.000514j
[2025-09-11 18:30:54] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -53.839222-0.003584j
[2025-09-11 18:31:05] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -53.773401+0.002003j
[2025-09-11 18:31:15] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -53.744728+0.000393j
[2025-09-11 18:31:25] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -53.741655+0.001069j
[2025-09-11 18:31:36] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -53.784184+0.001169j
[2025-09-11 18:31:47] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -53.590903-0.001556j
[2025-09-11 18:32:03] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -53.590699+0.000617j
[2025-09-11 18:32:18] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -53.606250+0.002487j
[2025-09-11 18:32:34] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -53.627550+0.000063j
[2025-09-11 18:32:49] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -53.696481+0.000977j
[2025-09-11 18:33:04] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -53.769285+0.003260j
[2025-09-11 18:33:20] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -53.701825+0.000023j
[2025-09-11 18:33:35] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -53.675909+0.001968j
[2025-09-11 18:33:51] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -53.830934-0.004156j
[2025-09-11 18:34:06] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -53.739558+0.001344j
[2025-09-11 18:34:21] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -53.577259-0.000806j
[2025-09-11 18:34:37] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -53.577193-0.001897j
[2025-09-11 18:34:52] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -53.677603+0.002191j
[2025-09-11 18:35:08] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -53.717606-0.001115j
[2025-09-11 18:35:23] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -53.793911-0.003464j
[2025-09-11 18:35:38] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -53.624001+0.000767j
[2025-09-11 18:35:54] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -53.748093-0.003858j
[2025-09-11 18:36:09] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -53.718315+0.000452j
[2025-09-11 18:36:25] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -53.781129-0.000394j
[2025-09-11 18:36:40] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -53.873874-0.004343j
[2025-09-11 18:36:55] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -53.842721-0.003001j
[2025-09-11 18:37:11] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -53.921969-0.004416j
[2025-09-11 18:37:26] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -53.797615+0.000226j
[2025-09-11 18:37:42] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -53.728321-0.002969j
[2025-09-11 18:37:57] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -53.706792-0.000073j
[2025-09-11 18:38:12] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -53.597932+0.003499j
[2025-09-11 18:38:28] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -53.664800-0.002132j
[2025-09-11 18:38:43] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -53.616160+0.001547j
[2025-09-11 18:38:59] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -53.743408-0.001388j
[2025-09-11 18:39:14] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -53.710933+0.000963j
[2025-09-11 18:39:29] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -53.672487-0.004524j
[2025-09-11 18:39:45] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -53.680011-0.001456j
[2025-09-11 18:40:00] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -53.632731+0.004068j
[2025-09-11 18:40:16] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -53.738366-0.000995j
[2025-09-11 18:40:31] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -53.712663+0.000354j
[2025-09-11 18:40:47] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -53.664130+0.002985j
[2025-09-11 18:41:02] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -53.602076+0.003679j
[2025-09-11 18:41:16] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -53.628478+0.001615j
[2025-09-11 18:41:26] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -53.693429+0.003254j
[2025-09-11 18:41:40] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -53.799203-0.000044j
[2025-09-11 18:41:51] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -53.734266-0.002277j
[2025-09-11 18:42:06] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -53.765253+0.000427j
[2025-09-11 18:42:21] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -53.885807-0.001686j
[2025-09-11 18:42:37] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -53.862303-0.002070j
[2025-09-11 18:42:52] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -53.793223-0.002371j
[2025-09-11 18:43:08] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -53.768053+0.000046j
[2025-09-11 18:43:23] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -53.691576-0.001716j
[2025-09-11 18:43:38] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -53.751179-0.000209j
[2025-09-11 18:43:54] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -53.695270-0.000162j
[2025-09-11 18:44:09] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -53.635783+0.000430j
[2025-09-11 18:44:24] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -53.631198-0.001189j
[2025-09-11 18:44:40] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -53.682889+0.001162j
[2025-09-11 18:44:40] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-11 18:44:55] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -53.701931+0.000251j
[2025-09-11 18:45:11] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -53.800947-0.000247j
[2025-09-11 18:45:26] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -53.723052+0.002232j
[2025-09-11 18:45:41] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -53.670482-0.000838j
[2025-09-11 18:45:57] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -53.790401+0.001136j
[2025-09-11 18:46:12] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -53.721870-0.000493j
[2025-09-11 18:46:27] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -53.798700-0.003701j
[2025-09-11 18:46:43] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -53.738170+0.001649j
[2025-09-11 18:46:58] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -53.662822-0.001496j
[2025-09-11 18:47:13] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -53.626105-0.005374j
[2025-09-11 18:47:29] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -53.573583-0.004999j
[2025-09-11 18:47:44] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -53.657528+0.002731j
[2025-09-11 18:47:59] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -53.655859+0.008337j
[2025-09-11 18:48:15] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -53.529676+0.001358j
[2025-09-11 18:48:30] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -53.599291-0.000338j
[2025-09-11 18:48:45] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -53.716456+0.002555j
[2025-09-11 18:49:01] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -53.759685+0.000979j
[2025-09-11 18:49:16] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -53.715272+0.002756j
[2025-09-11 18:49:31] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -53.715957-0.000284j
[2025-09-11 18:49:47] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -53.687386+0.001253j
[2025-09-11 18:50:02] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -53.723748+0.003206j
[2025-09-11 18:50:17] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -53.729924-0.000170j
[2025-09-11 18:50:33] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -53.742610+0.002619j
[2025-09-11 18:50:48] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -53.621729+0.000723j
[2025-09-11 18:51:03] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -53.710677+0.000101j
[2025-09-11 18:51:19] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -53.792387+0.001536j
[2025-09-11 18:51:34] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -53.788900-0.002164j
[2025-09-11 18:51:49] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -53.804718+0.004868j
[2025-09-11 18:52:05] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -53.693991+0.000930j
[2025-09-11 18:52:20] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -53.791992-0.001730j
[2025-09-11 18:52:35] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -53.767700+0.002069j
[2025-09-11 18:52:51] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -53.692786+0.003442j
[2025-09-11 18:53:06] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -53.724774+0.002128j
[2025-09-11 18:53:22] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -53.857725-0.001313j
[2025-09-11 18:53:37] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -53.811423-0.000858j
[2025-09-11 18:53:52] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -53.779646-0.001360j
[2025-09-11 18:54:08] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -53.841829+0.002614j
[2025-09-11 18:54:23] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -53.706627-0.005075j
[2025-09-11 18:54:39] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -53.709596-0.000393j
[2025-09-11 18:54:54] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -53.771020-0.003509j
[2025-09-11 18:55:09] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -53.723160+0.002247j
[2025-09-11 18:55:25] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -53.644898-0.005388j
[2025-09-11 18:55:40] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -53.602009+0.000706j
[2025-09-11 18:55:55] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -53.648911-0.002272j
[2025-09-11 18:56:11] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -53.712986-0.001625j
[2025-09-11 18:56:26] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -53.670734+0.003797j
[2025-09-11 18:56:42] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -53.787913+0.003488j
[2025-09-11 18:56:57] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -53.709345-0.000805j
[2025-09-11 18:57:12] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -53.672558-0.000646j
[2025-09-11 18:57:28] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -53.616977-0.004845j
[2025-09-11 18:57:43] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -53.744778-0.001651j
[2025-09-11 18:57:58] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -53.799585-0.002268j
[2025-09-11 18:58:14] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -53.680333+0.001654j
[2025-09-11 18:58:29] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -53.702289-0.004268j
[2025-09-11 18:58:45] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -53.693675+0.001035j
[2025-09-11 18:59:00] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -53.744040-0.000280j
[2025-09-11 18:59:15] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -53.796304-0.003583j
[2025-09-11 18:59:31] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -53.859840+0.000550j
[2025-09-11 18:59:46] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -53.789731+0.000851j
[2025-09-11 19:00:01] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -53.722704-0.000763j
[2025-09-11 19:00:17] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -53.802557+0.001245j
[2025-09-11 19:00:32] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -53.735307-0.001628j
[2025-09-11 19:00:47] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -53.626045+0.002471j
[2025-09-11 19:01:03] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -53.716480-0.001664j
[2025-09-11 19:01:18] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -53.596364+0.001605j
[2025-09-11 19:01:33] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -53.722707-0.001374j
[2025-09-11 19:01:49] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -53.718769-0.000597j
[2025-09-11 19:02:04] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -53.696860+0.002156j
[2025-09-11 19:02:19] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -53.518231+0.000122j
[2025-09-11 19:02:35] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -53.637836-0.000103j
[2025-09-11 19:02:50] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -53.579965-0.000315j
[2025-09-11 19:03:06] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -53.559146+0.001297j
[2025-09-11 19:03:21] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -53.572228-0.001169j
[2025-09-11 19:03:36] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -53.649758-0.000198j
[2025-09-11 19:03:52] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -53.814768+0.003802j
[2025-09-11 19:04:07] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -53.755771-0.000999j
[2025-09-11 19:04:23] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -53.728342-0.001501j
[2025-09-11 19:04:38] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -53.758640-0.003026j
[2025-09-11 19:04:53] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -53.714159-0.000346j
[2025-09-11 19:05:09] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -53.680881+0.000613j
[2025-09-11 19:05:24] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -53.726444+0.003625j
[2025-09-11 19:05:40] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -53.769599-0.001705j
[2025-09-11 19:05:55] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -53.650134-0.003159j
[2025-09-11 19:06:10] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -53.592395+0.002658j
[2025-09-11 19:06:26] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -53.729780-0.001546j
[2025-09-11 19:06:41] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -53.724677-0.001526j
[2025-09-11 19:06:56] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -53.725441-0.000618j
[2025-09-11 19:07:12] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -53.747783+0.000591j
[2025-09-11 19:07:27] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -53.752303-0.000082j
[2025-09-11 19:07:43] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -53.685286-0.001552j
[2025-09-11 19:07:58] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -53.742812-0.000367j
[2025-09-11 19:08:13] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -53.755196-0.004080j
[2025-09-11 19:08:29] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -53.585871-0.003707j
[2025-09-11 19:08:44] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -53.579662-0.001268j
[2025-09-11 19:08:59] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -53.611771-0.004359j
[2025-09-11 19:09:15] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -53.667264-0.001439j
[2025-09-11 19:09:30] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -53.702943-0.001910j
[2025-09-11 19:09:45] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -53.770127+0.000772j
[2025-09-11 19:10:01] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -53.624921-0.002295j
[2025-09-11 19:10:16] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -53.627437+0.001279j
[2025-09-11 19:10:32] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -53.765546+0.003328j
[2025-09-11 19:10:47] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -53.709095+0.001004j
[2025-09-11 19:11:02] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -53.621146+0.005445j
[2025-09-11 19:11:17] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -53.570673-0.001155j
[2025-09-11 19:11:33] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -53.619409-0.002970j
[2025-09-11 19:11:33] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-11 19:11:48] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -53.736518+0.000077j
[2025-09-11 19:12:04] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -53.564010-0.002237j
[2025-09-11 19:12:19] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -53.466436+0.002460j
[2025-09-11 19:12:34] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -53.592004-0.001828j
[2025-09-11 19:12:50] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -53.731567-0.001496j
[2025-09-11 19:13:05] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -53.584155-0.001906j
[2025-09-11 19:13:20] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -53.524400-0.001157j
[2025-09-11 19:13:36] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -53.671681+0.001300j
[2025-09-11 19:13:51] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -53.645453+0.007193j
[2025-09-11 19:14:06] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -53.707985+0.001484j
[2025-09-11 19:14:22] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -53.704303-0.002001j
[2025-09-11 19:14:37] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -53.747133+0.000531j
[2025-09-11 19:14:52] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -53.727282+0.003819j
[2025-09-11 19:15:08] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -53.747848-0.000162j
[2025-09-11 19:15:19] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -53.742219+0.001447j
[2025-09-11 19:15:30] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -53.719837+0.001215j
[2025-09-11 19:15:40] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -53.661972-0.002663j
[2025-09-11 19:15:50] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -53.696758-0.001887j
[2025-09-11 19:16:01] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -53.569703+0.002216j
[2025-09-11 19:16:11] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -53.587630+0.001088j
[2025-09-11 19:16:24] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -53.767625-0.000123j
[2025-09-11 19:16:39] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -53.723413+0.001207j
[2025-09-11 19:16:55] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -53.619538-0.000004j
[2025-09-11 19:17:10] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -53.596050-0.002995j
[2025-09-11 19:17:26] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -53.496590-0.000131j
[2025-09-11 19:17:41] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -53.697048+0.000267j
[2025-09-11 19:17:56] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -53.777121-0.000029j
[2025-09-11 19:18:12] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -53.685753-0.002748j
[2025-09-11 19:18:27] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -53.726657+0.002081j
[2025-09-11 19:18:43] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -53.722439+0.001598j
[2025-09-11 19:18:58] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -53.681931+0.000043j
[2025-09-11 19:19:14] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -53.756313-0.000757j
[2025-09-11 19:19:29] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -53.746124-0.001597j
[2025-09-11 19:19:44] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -53.730016+0.002056j
[2025-09-11 19:20:00] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -53.655443+0.001635j
[2025-09-11 19:20:15] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -53.668739+0.003938j
[2025-09-11 19:20:31] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -53.692753-0.002020j
[2025-09-11 19:20:46] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -53.704715+0.004088j
[2025-09-11 19:21:02] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -53.624930-0.002723j
[2025-09-11 19:21:17] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -53.714160-0.002150j
[2025-09-11 19:21:32] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -53.593995+0.000652j
[2025-09-11 19:21:48] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -53.628389-0.000165j
[2025-09-11 19:22:03] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -53.605849-0.000400j
[2025-09-11 19:22:19] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -53.571973-0.001188j
[2025-09-11 19:22:34] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -53.677935+0.000577j
[2025-09-11 19:22:50] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -53.640595+0.001516j
[2025-09-11 19:23:05] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -53.722007-0.003223j
[2025-09-11 19:23:20] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -53.716241+0.000487j
[2025-09-11 19:23:36] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -53.582399+0.002879j
[2025-09-11 19:23:51] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -53.610756-0.001031j
[2025-09-11 19:24:07] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -53.692578-0.001003j
[2025-09-11 19:24:22] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -53.651399-0.002246j
[2025-09-11 19:24:38] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -53.605924+0.000448j
[2025-09-11 19:24:53] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -53.668006-0.005985j
[2025-09-11 19:25:08] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -53.555992-0.000929j
[2025-09-11 19:25:24] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -53.655146-0.000717j
[2025-09-11 19:25:39] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -53.689678+0.000643j
[2025-09-11 19:25:53] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -53.790436+0.001266j
[2025-09-11 19:26:03] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -53.768846+0.002191j
[2025-09-11 19:26:18] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -53.771705-0.004150j
[2025-09-11 19:26:28] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -53.726257-0.000863j
[2025-09-11 19:26:43] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -53.688232-0.000891j
[2025-09-11 19:26:58] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -53.646257-0.002352j
[2025-09-11 19:27:14] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -53.669398+0.002770j
[2025-09-11 19:27:29] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -53.711916+0.002541j
[2025-09-11 19:27:44] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -53.754656-0.003828j
[2025-09-11 19:28:00] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -53.672517-0.001908j
[2025-09-11 19:28:15] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -53.602875-0.001759j
[2025-09-11 19:28:30] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -53.599056-0.002370j
[2025-09-11 19:28:46] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -53.648673+0.002788j
[2025-09-11 19:29:01] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -53.752960+0.003347j
[2025-09-11 19:29:16] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -53.770913+0.002365j
[2025-09-11 19:29:32] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -53.717999-0.001832j
[2025-09-11 19:29:47] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -53.813680-0.002383j
[2025-09-11 19:30:02] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -53.707474-0.001097j
[2025-09-11 19:30:18] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -53.660979-0.002465j
[2025-09-11 19:30:33] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -53.815686-0.001949j
[2025-09-11 19:30:49] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -53.728076+0.000317j
[2025-09-11 19:31:04] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -53.700088+0.001065j
[2025-09-11 19:31:19] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -53.615240+0.000849j
[2025-09-11 19:31:35] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -53.636537-0.000687j
[2025-09-11 19:31:50] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -53.674989-0.006918j
[2025-09-11 19:32:06] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -53.658836-0.000470j
[2025-09-11 19:32:21] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -53.785509-0.001782j
[2025-09-11 19:32:36] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -53.689010+0.002512j
[2025-09-11 19:32:52] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -53.786218+0.002067j
[2025-09-11 19:33:07] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -53.615503+0.001279j
[2025-09-11 19:33:23] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -53.719902-0.000355j
[2025-09-11 19:33:38] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -53.612578+0.003281j
[2025-09-11 19:33:53] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -53.668021+0.002555j
[2025-09-11 19:34:09] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -53.651785-0.002636j
[2025-09-11 19:34:24] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -53.556254+0.000541j
[2025-09-11 19:34:39] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -53.528923+0.000360j
[2025-09-11 19:34:55] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -53.578715-0.000653j
[2025-09-11 19:35:10] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -53.589414-0.000294j
[2025-09-11 19:35:26] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -53.581753-0.000328j
[2025-09-11 19:35:41] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -53.716175-0.001186j
[2025-09-11 19:35:56] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -53.618290-0.002594j
[2025-09-11 19:36:12] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -53.820033-0.001665j
[2025-09-11 19:36:27] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -53.653060+0.001592j
[2025-09-11 19:36:42] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -53.720148-0.000854j
[2025-09-11 19:36:58] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -53.725504+0.002211j
[2025-09-11 19:37:13] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -53.723017+0.001432j
[2025-09-11 19:37:29] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -53.741862-0.003768j
[2025-09-11 19:37:44] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -53.763336-0.001878j
[2025-09-11 19:37:44] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-11 19:38:00] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -53.778571+0.002482j
[2025-09-11 19:38:15] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -53.761055-0.000124j
[2025-09-11 19:38:30] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -53.605411+0.001032j
[2025-09-11 19:38:46] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -53.720686-0.000902j
[2025-09-11 19:39:01] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -53.588362-0.002051j
[2025-09-11 19:39:16] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -53.663993+0.000319j
[2025-09-11 19:39:32] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -53.687230-0.001647j
[2025-09-11 19:39:47] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -53.632705+0.000133j
[2025-09-11 19:40:02] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -53.732889+0.001544j
[2025-09-11 19:40:18] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -53.820341-0.002158j
[2025-09-11 19:40:33] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -53.798059-0.004573j
[2025-09-11 19:40:49] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -53.745482+0.003956j
[2025-09-11 19:41:04] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -53.804096-0.002081j
[2025-09-11 19:41:19] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -53.811001+0.000915j
[2025-09-11 19:41:35] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -53.763533-0.001141j
[2025-09-11 19:41:50] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -53.784617+0.003071j
[2025-09-11 19:42:05] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -53.841805+0.002621j
[2025-09-11 19:42:21] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -53.830459-0.001515j
[2025-09-11 19:42:36] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -53.784216-0.000896j
[2025-09-11 19:42:51] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -53.887417-0.002243j
[2025-09-11 19:43:07] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -53.817022-0.001271j
[2025-09-11 19:43:22] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -53.726007-0.000005j
[2025-09-11 19:43:37] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -53.555701-0.001114j
[2025-09-11 19:43:53] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -53.614723+0.001336j
[2025-09-11 19:44:08] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -53.683149+0.002234j
[2025-09-11 19:44:24] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -53.617366+0.004691j
[2025-09-11 19:44:39] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -53.691131-0.001307j
[2025-09-11 19:44:55] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -53.595827-0.002760j
[2025-09-11 19:45:10] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -53.789566+0.000967j
[2025-09-11 19:45:25] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -53.798601-0.004375j
[2025-09-11 19:45:41] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -53.732090-0.002799j
[2025-09-11 19:45:56] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -53.795167+0.000380j
[2025-09-11 19:46:11] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -53.676221-0.004185j
[2025-09-11 19:46:27] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -53.696644+0.001484j
[2025-09-11 19:46:42] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -53.728678-0.003152j
[2025-09-11 19:46:58] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -53.655506+0.001026j
[2025-09-11 19:47:13] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -53.747697+0.000261j
[2025-09-11 19:47:28] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -53.628511-0.001841j
[2025-09-11 19:47:44] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -53.735142-0.006763j
[2025-09-11 19:47:59] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -53.765915+0.002690j
[2025-09-11 19:48:15] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -53.677429-0.002740j
[2025-09-11 19:48:30] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -53.586711+0.001557j
[2025-09-11 19:48:45] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -53.664963+0.000762j
[2025-09-11 19:49:01] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -53.549643-0.000412j
[2025-09-11 19:49:16] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -53.723447+0.002072j
[2025-09-11 19:49:32] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -53.883391-0.002089j
[2025-09-11 19:49:47] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -53.775452+0.000200j
[2025-09-11 19:50:02] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -53.875714-0.002434j
[2025-09-11 19:50:18] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -53.838950+0.002422j
[2025-09-11 19:50:33] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -53.851589-0.002449j
[2025-09-11 19:50:49] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -53.578880+0.002139j
[2025-09-11 19:51:04] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -53.694018-0.004712j
[2025-09-11 19:51:19] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -53.603773-0.000469j
[2025-09-11 19:51:35] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -53.641880-0.000683j
[2025-09-11 19:51:50] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -53.656264+0.001031j
[2025-09-11 19:52:05] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -53.632909+0.000249j
[2025-09-11 19:52:21] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -53.664667-0.002094j
[2025-09-11 19:52:36] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -53.622200-0.000177j
[2025-09-11 19:52:51] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -53.767195+0.001342j
[2025-09-11 19:53:07] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -53.743960+0.001770j
[2025-09-11 19:53:22] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -53.752233+0.002390j
[2025-09-11 19:53:37] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -53.702368+0.001930j
[2025-09-11 19:53:53] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -53.751323-0.002217j
[2025-09-11 19:54:08] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -53.683190+0.001436j
[2025-09-11 19:54:24] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -53.713417+0.003481j
[2025-09-11 19:54:39] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -53.645506-0.002938j
[2025-09-11 19:54:54] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -53.700368-0.002564j
[2025-09-11 19:55:10] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -53.870367-0.000749j
[2025-09-11 19:55:25] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -53.667836-0.003847j
[2025-09-11 19:55:40] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -53.704155+0.000676j
[2025-09-11 19:55:56] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -53.648926-0.000449j
[2025-09-11 19:56:11] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -53.590212-0.000655j
[2025-09-11 19:56:27] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -53.485359+0.002755j
[2025-09-11 19:56:42] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -53.635923+0.001187j
[2025-09-11 19:56:57] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -53.603704+0.003534j
[2025-09-11 19:57:13] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -53.630104+0.003110j
[2025-09-11 19:57:28] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -53.645027-0.002571j
[2025-09-11 19:57:43] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -53.761358-0.001843j
[2025-09-11 19:57:59] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -53.685612+0.000975j
[2025-09-11 19:58:14] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -53.601000+0.004705j
[2025-09-11 19:58:29] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -53.626409-0.004814j
[2025-09-11 19:58:45] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -53.433074-0.000475j
[2025-09-11 19:59:00] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -53.611036-0.002256j
[2025-09-11 19:59:15] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -53.590510+0.000403j
[2025-09-11 19:59:31] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -53.637119-0.004326j
[2025-09-11 19:59:46] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -53.723355+0.001792j
[2025-09-11 20:00:02] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -53.652616-0.001995j
[2025-09-11 20:00:17] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -53.854153+0.003807j
[2025-09-11 20:00:28] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -53.694105-0.006006j
[2025-09-11 20:00:38] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -53.593333+0.003311j
[2025-09-11 20:00:48] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -53.617525+0.000801j
[2025-09-11 20:00:59] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -53.665304-0.000415j
[2025-09-11 20:01:09] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -53.677991+0.003882j
[2025-09-11 20:01:19] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -53.725536+0.002647j
[2025-09-11 20:01:30] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -53.804554+0.001621j
[2025-09-11 20:01:42] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -53.736934-0.003569j
[2025-09-11 20:01:53] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -53.653140-0.003838j
[2025-09-11 20:02:03] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -53.550544+0.002634j
[2025-09-11 20:02:13] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -53.518774-0.001041j
[2025-09-11 20:02:23] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -53.614112+0.003865j
[2025-09-11 20:02:34] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -53.605646-0.000940j
[2025-09-11 20:02:44] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -53.577804-0.002430j
[2025-09-11 20:02:54] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -53.657807+0.001837j
[2025-09-11 20:03:05] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -53.637030-0.004977j
[2025-09-11 20:03:15] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -53.579904-0.002600j
[2025-09-11 20:03:15] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-11 20:03:15] ✅ Training completed | Restarts: 2
[2025-09-11 20:03:15] ============================================================
[2025-09-11 20:03:15] Training completed | Runtime: 15972.3s
[2025-09-11 20:03:19] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-11 20:03:19] ============================================================
