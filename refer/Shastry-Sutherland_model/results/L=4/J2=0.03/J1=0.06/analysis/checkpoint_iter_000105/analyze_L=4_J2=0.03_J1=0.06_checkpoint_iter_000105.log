[2025-09-13 07:35:14] 使用checkpoint文件: results/L=4/J2=0.03/J1=0.06/training/checkpoints/checkpoint_iter_000105.pkl
[2025-09-13 07:35:32] ✓ 从checkpoint加载参数: 105
[2025-09-13 07:35:32]   - 能量: -53.634710+0.000669j ± 0.083533
[2025-09-13 07:35:32] ================================================================================
[2025-09-13 07:35:32] 加载量子态: L=4, J2=0.03, J1=0.06, checkpoint=checkpoint_iter_000105
[2025-09-13 07:35:32] 使用采样数目: 1048576
[2025-09-13 07:35:32] 设置样本数为: 1048576
[2025-09-13 07:35:32] 开始生成共享样本集...
[2025-09-13 07:38:33] 样本生成完成,耗时: 181.047 秒
[2025-09-13 07:38:33] ================================================================================
[2025-09-13 07:38:33] 开始计算自旋结构因子...
[2025-09-13 07:38:33] 初始化操作符缓存...
[2025-09-13 07:38:33] 预构建所有自旋相关操作符...
[2025-09-13 07:38:33] 开始计算自旋相关函数...
[2025-09-13 07:38:51] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 17.580s
[2025-09-13 07:39:08] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 17.792s
[2025-09-13 07:39:18] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.333s
[2025-09-13 07:39:27] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.380s
[2025-09-13 07:39:37] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.370s
[2025-09-13 07:39:46] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.369s
[2025-09-13 07:39:55] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.322s
[2025-09-13 07:40:05] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.343s
[2025-09-13 07:40:14] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.340s
[2025-09-13 07:40:23] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.360s
[2025-09-13 07:40:33] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.348s
[2025-09-13 07:40:42] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.349s
[2025-09-13 07:40:51] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.328s
[2025-09-13 07:41:01] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.382s
[2025-09-13 07:41:10] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.371s
[2025-09-13 07:41:20] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.346s
[2025-09-13 07:41:29] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.354s
[2025-09-13 07:41:38] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.356s
[2025-09-13 07:41:48] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.286s
[2025-09-13 07:41:57] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.357s
[2025-09-13 07:42:06] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.377s
[2025-09-13 07:42:16] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.357s
[2025-09-13 07:42:25] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.349s
[2025-09-13 07:42:34] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.364s
[2025-09-13 07:42:44] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.350s
[2025-09-13 07:42:53] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.363s
[2025-09-13 07:43:02] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.344s
[2025-09-13 07:43:12] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.296s
[2025-09-13 07:43:21] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.401s
[2025-09-13 07:43:30] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.263s
[2025-09-13 07:43:40] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.346s
[2025-09-13 07:43:49] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.374s
[2025-09-13 07:43:59] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.362s
[2025-09-13 07:44:08] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.373s
[2025-09-13 07:44:17] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.328s
[2025-09-13 07:44:27] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.358s
[2025-09-13 07:44:36] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.348s
[2025-09-13 07:44:45] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.366s
[2025-09-13 07:44:55] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.289s
[2025-09-13 07:45:04] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.398s
[2025-09-13 07:45:13] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.254s
[2025-09-13 07:45:23] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.354s
[2025-09-13 07:45:32] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.345s
[2025-09-13 07:45:41] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.361s
[2025-09-13 07:45:51] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.320s
[2025-09-13 07:46:00] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.345s
[2025-09-13 07:46:10] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.384s
[2025-09-13 07:46:19] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.344s
[2025-09-13 07:46:28] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.356s
[2025-09-13 07:46:38] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.331s
[2025-09-13 07:46:47] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.364s
[2025-09-13 07:46:56] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.348s
[2025-09-13 07:47:06] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.347s
[2025-09-13 07:47:15] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.371s
[2025-09-13 07:47:24] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.346s
[2025-09-13 07:47:34] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.357s
[2025-09-13 07:47:43] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.354s
[2025-09-13 07:47:53] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.357s
[2025-09-13 07:48:02] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.354s
[2025-09-13 07:48:11] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.380s
[2025-09-13 07:48:21] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.305s
[2025-09-13 07:48:30] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.336s
[2025-09-13 07:48:39] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.379s
[2025-09-13 07:48:49] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.345s
[2025-09-13 07:48:49] 自旋相关函数计算完成,总耗时 615.67 秒
[2025-09-13 07:48:50] 计算傅里叶变换...
[2025-09-13 07:48:52] 自旋结构因子计算完成
[2025-09-13 07:48:53] 自旋相关函数平均误差: 0.000651
