[2025-09-13 08:30:23] 使用checkpoint文件: results/L=4/J2=0.03/J1=0.06/training/checkpoints/checkpoint_iter_000525.pkl
[2025-09-13 08:30:45] ✓ 从checkpoint加载参数: 525
[2025-09-13 08:30:45]   - 能量: -53.591639-0.000921j ± 0.082414
[2025-09-13 08:30:45] ================================================================================
[2025-09-13 08:30:45] 加载量子态: L=4, J2=0.03, J1=0.06, checkpoint=checkpoint_iter_000525
[2025-09-13 08:30:45] 使用采样数目: 1048576
[2025-09-13 08:30:45] 设置样本数为: 1048576
[2025-09-13 08:30:45] 开始生成共享样本集...
[2025-09-13 08:33:46] 样本生成完成,耗时: 180.914 秒
[2025-09-13 08:33:46] ================================================================================
[2025-09-13 08:33:46] 开始计算自旋结构因子...
[2025-09-13 08:33:46] 初始化操作符缓存...
[2025-09-13 08:33:46] 预构建所有自旋相关操作符...
[2025-09-13 08:33:46] 开始计算自旋相关函数...
[2025-09-13 08:34:00] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 14.272s
[2025-09-13 08:34:18] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 17.758s
[2025-09-13 08:34:27] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.363s
[2025-09-13 08:34:37] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.379s
[2025-09-13 08:34:46] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.413s
[2025-09-13 08:34:56] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.296s
[2025-09-13 08:35:05] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.358s
[2025-09-13 08:35:14] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.380s
[2025-09-13 08:35:24] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.364s
[2025-09-13 08:35:33] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.345s
[2025-09-13 08:35:42] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.365s
[2025-09-13 08:35:52] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.379s
[2025-09-13 08:36:01] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.358s
[2025-09-13 08:36:11] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.361s
[2025-09-13 08:36:20] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.367s
[2025-09-13 08:36:29] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.367s
[2025-09-13 08:36:39] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.386s
[2025-09-13 08:36:48] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.376s
[2025-09-13 08:36:57] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.367s
[2025-09-13 08:37:07] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.362s
[2025-09-13 08:37:16] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.367s
[2025-09-13 08:37:26] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.368s
[2025-09-13 08:37:35] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.350s
[2025-09-13 08:37:44] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.373s
[2025-09-13 08:37:54] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.362s
[2025-09-13 08:38:03] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.412s
[2025-09-13 08:38:13] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.400s
[2025-09-13 08:38:22] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.390s
[2025-09-13 08:38:31] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.354s
[2025-09-13 08:38:41] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.352s
[2025-09-13 08:38:50] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.364s
[2025-09-13 08:38:59] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.353s
[2025-09-13 08:39:09] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.354s
[2025-09-13 08:39:18] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.372s
[2025-09-13 08:39:27] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.351s
[2025-09-13 08:39:37] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.363s
[2025-09-13 08:39:46] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.387s
[2025-09-13 08:39:56] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.397s
[2025-09-13 08:40:05] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.358s
[2025-09-13 08:40:14] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.374s
[2025-09-13 08:40:24] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.363s
[2025-09-13 08:40:33] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.353s
[2025-09-13 08:40:42] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.335s
[2025-09-13 08:40:52] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.371s
[2025-09-13 08:41:01] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.358s
[2025-09-13 08:41:11] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.350s
[2025-09-13 08:41:20] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.374s
[2025-09-13 08:41:29] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.410s
[2025-09-13 08:41:39] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.328s
[2025-09-13 08:41:48] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.358s
[2025-09-13 08:41:57] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.363s
[2025-09-13 08:42:07] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.354s
[2025-09-13 08:42:16] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.351s
[2025-09-13 08:42:26] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.361s
[2025-09-13 08:42:35] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.356s
[2025-09-13 08:42:44] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.363s
[2025-09-13 08:42:54] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.352s
[2025-09-13 08:43:03] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.345s
[2025-09-13 08:43:12] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.399s
[2025-09-13 08:43:22] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.307s
[2025-09-13 08:43:31] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.357s
[2025-09-13 08:43:40] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.354s
[2025-09-13 08:43:50] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.369s
[2025-09-13 08:43:59] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.351s
[2025-09-13 08:43:59] 自旋相关函数计算完成,总耗时 613.16 秒
[2025-09-13 08:43:59] 计算傅里叶变换...
[2025-09-13 08:44:01] 自旋结构因子计算完成
[2025-09-13 08:44:03] 自旋相关函数平均误差: 0.000654
