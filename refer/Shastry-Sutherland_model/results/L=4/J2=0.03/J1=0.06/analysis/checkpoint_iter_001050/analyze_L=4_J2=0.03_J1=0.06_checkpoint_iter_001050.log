[2025-09-13 09:39:13] 使用checkpoint文件: results/L=4/J2=0.03/J1=0.06/training/checkpoints/checkpoint_iter_001050.pkl
[2025-09-13 09:39:30] ✓ 从checkpoint加载参数: 1050
[2025-09-13 09:39:30]   - 能量: -53.579904-0.002600j ± 0.081268
[2025-09-13 09:39:30] ================================================================================
[2025-09-13 09:39:30] 加载量子态: L=4, J2=0.03, J1=0.06, checkpoint=checkpoint_iter_001050
[2025-09-13 09:39:30] 使用采样数目: 1048576
[2025-09-13 09:39:30] 设置样本数为: 1048576
[2025-09-13 09:39:30] 开始生成共享样本集...
[2025-09-13 09:42:32] 样本生成完成,耗时: 181.890 秒
[2025-09-13 09:42:32] ================================================================================
[2025-09-13 09:42:32] 开始计算自旋结构因子...
[2025-09-13 09:42:32] 初始化操作符缓存...
[2025-09-13 09:42:32] 预构建所有自旋相关操作符...
[2025-09-13 09:42:32] 开始计算自旋相关函数...
[2025-09-13 09:42:50] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 17.144s
[2025-09-13 09:43:07] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 17.767s
[2025-09-13 09:43:17] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.342s
[2025-09-13 09:43:26] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.330s
[2025-09-13 09:43:35] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.338s
[2025-09-13 09:43:45] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.329s
[2025-09-13 09:43:54] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.346s
[2025-09-13 09:44:03] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.245s
[2025-09-13 09:44:13] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.342s
[2025-09-13 09:44:22] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.328s
[2025-09-13 09:44:31] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.346s
[2025-09-13 09:44:41] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.329s
[2025-09-13 09:44:50] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.335s
[2025-09-13 09:44:59] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.338s
[2025-09-13 09:45:09] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.336s
[2025-09-13 09:45:18] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.338s
[2025-09-13 09:45:27] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.335s
[2025-09-13 09:45:37] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.397s
[2025-09-13 09:45:46] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.266s
[2025-09-13 09:45:55] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.333s
[2025-09-13 09:46:05] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.338s
[2025-09-13 09:46:14] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.338s
[2025-09-13 09:46:23] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.342s
[2025-09-13 09:46:33] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.311s
[2025-09-13 09:46:42] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.335s
[2025-09-13 09:46:51] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.336s
[2025-09-13 09:47:01] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.342s
[2025-09-13 09:47:10] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.308s
[2025-09-13 09:47:20] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.402s
[2025-09-13 09:47:29] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.251s
[2025-09-13 09:47:38] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.338s
[2025-09-13 09:47:48] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.326s
[2025-09-13 09:47:57] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.338s
[2025-09-13 09:48:06] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.302s
[2025-09-13 09:48:16] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.335s
[2025-09-13 09:48:25] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.339s
[2025-09-13 09:48:34] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.348s
[2025-09-13 09:48:44] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.328s
[2025-09-13 09:48:53] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.331s
[2025-09-13 09:49:02] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.311s
[2025-09-13 09:49:12] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.359s
[2025-09-13 09:49:21] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.345s
[2025-09-13 09:49:30] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.343s
[2025-09-13 09:49:40] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.336s
[2025-09-13 09:49:49] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.329s
[2025-09-13 09:49:58] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.348s
[2025-09-13 09:50:08] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.330s
[2025-09-13 09:50:17] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.336s
[2025-09-13 09:50:26] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.332s
[2025-09-13 09:50:36] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.342s
[2025-09-13 09:50:45] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.328s
[2025-09-13 09:50:54] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.339s
[2025-09-13 09:51:04] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.344s
[2025-09-13 09:51:13] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.331s
[2025-09-13 09:51:22] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.342s
[2025-09-13 09:51:32] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.324s
[2025-09-13 09:51:41] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.337s
[2025-09-13 09:51:50] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.341s
[2025-09-13 09:52:00] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.336s
[2025-09-13 09:52:09] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.326s
[2025-09-13 09:52:18] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.313s
[2025-09-13 09:52:28] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.334s
[2025-09-13 09:52:37] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.333s
[2025-09-13 09:52:46] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.333s
[2025-09-13 09:52:46] 自旋相关函数计算完成,总耗时 614.01 秒
[2025-09-13 09:52:48] 计算傅里叶变换...
[2025-09-13 09:52:50] 自旋结构因子计算完成
[2025-09-13 09:52:51] 自旋相关函数平均误差: 0.000660
