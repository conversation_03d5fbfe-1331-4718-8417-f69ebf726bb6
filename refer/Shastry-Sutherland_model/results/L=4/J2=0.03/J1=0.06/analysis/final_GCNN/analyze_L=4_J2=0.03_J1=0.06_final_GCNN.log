[2025-09-13 09:52:58] 使用checkpoint文件: results/L=4/J2=0.03/J1=0.06/training/checkpoints/final_GCNN.pkl
[2025-09-13 09:53:16] ✓ 从checkpoint加载参数: final
[2025-09-13 09:53:16]   - 能量: -53.579904-0.002600j ± 0.081268
[2025-09-13 09:53:16] ================================================================================
[2025-09-13 09:53:16] 加载量子态: L=4, J2=0.03, J1=0.06, checkpoint=final_GCNN
[2025-09-13 09:53:16] 使用采样数目: 1048576
[2025-09-13 09:53:16] 设置样本数为: 1048576
[2025-09-13 09:53:16] 开始生成共享样本集...
[2025-09-13 09:56:18] 样本生成完成,耗时: 182.587 秒
[2025-09-13 09:56:19] ================================================================================
[2025-09-13 09:56:19] 开始计算自旋结构因子...
[2025-09-13 09:56:19] 初始化操作符缓存...
[2025-09-13 09:56:19] 预构建所有自旋相关操作符...
[2025-09-13 09:56:19] 开始计算自旋相关函数...
[2025-09-13 09:56:33] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 14.649s
[2025-09-13 09:56:51] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 17.727s
[2025-09-13 09:57:00] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.326s
[2025-09-13 09:57:10] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.335s
[2025-09-13 09:57:19] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.325s
[2025-09-13 09:57:28] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.330s
[2025-09-13 09:57:38] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.327s
[2025-09-13 09:57:47] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.335s
[2025-09-13 09:57:56] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.326s
[2025-09-13 09:58:06] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.302s
[2025-09-13 09:58:15] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.319s
[2025-09-13 09:58:24] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.330s
[2025-09-13 09:58:34] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.327s
[2025-09-13 09:58:43] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.337s
[2025-09-13 09:58:52] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.335s
[2025-09-13 09:59:02] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.309s
[2025-09-13 09:59:11] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.348s
[2025-09-13 09:59:20] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.341s
[2025-09-13 09:59:30] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.338s
[2025-09-13 09:59:39] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.335s
[2025-09-13 09:59:48] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.316s
[2025-09-13 09:59:58] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.314s
[2025-09-13 10:00:07] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.330s
[2025-09-13 10:00:16] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.336s
[2025-09-13 10:00:26] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.328s
[2025-09-13 10:00:35] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.335s
[2025-09-13 10:00:44] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.304s
[2025-09-13 10:00:54] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.333s
[2025-09-13 10:01:03] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.329s
[2025-09-13 10:01:12] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.329s
[2025-09-13 10:01:22] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.328s
[2025-09-13 10:01:31] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.392s
[2025-09-13 10:01:40] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.262s
[2025-09-13 10:01:50] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.341s
[2025-09-13 10:01:59] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.324s
[2025-09-13 10:02:08] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.333s
[2025-09-13 10:02:18] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.335s
[2025-09-13 10:02:27] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.328s
[2025-09-13 10:02:36] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.328s
[2025-09-13 10:02:46] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.335s
[2025-09-13 10:02:55] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.329s
[2025-09-13 10:03:04] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.285s
[2025-09-13 10:03:14] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.379s
[2025-09-13 10:03:23] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.260s
[2025-09-13 10:03:32] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.329s
[2025-09-13 10:03:42] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.323s
[2025-09-13 10:03:51] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.336s
[2025-09-13 10:04:00] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.319s
[2025-09-13 10:04:10] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.332s
[2025-09-13 10:04:19] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.330s
[2025-09-13 10:04:28] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.330s
[2025-09-13 10:04:38] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.336s
[2025-09-13 10:04:47] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.318s
[2025-09-13 10:04:56] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.322s
[2025-09-13 10:05:06] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.343s
[2025-09-13 10:05:15] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.341s
[2025-09-13 10:05:24] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.324s
[2025-09-13 10:05:34] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.330s
[2025-09-13 10:05:43] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.322s
[2025-09-13 10:05:52] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.324s
[2025-09-13 10:06:02] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.325s
[2025-09-13 10:06:11] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.323s
[2025-09-13 10:06:20] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.329s
[2025-09-13 10:06:30] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.283s
[2025-09-13 10:06:30] 自旋相关函数计算完成,总耗时 611.09 秒
[2025-09-13 10:06:32] 计算傅里叶变换...
[2025-09-13 10:06:35] 自旋结构因子计算完成
[2025-09-13 10:06:37] 自旋相关函数平均误差: 0.000656
