[2025-09-13 08:57:56] 使用checkpoint文件: results/L=4/J2=0.03/J1=0.06/training/checkpoints/checkpoint_iter_000735.pkl
[2025-09-13 08:58:13] ✓ 从checkpoint加载参数: 735
[2025-09-13 08:58:13]   - 能量: -53.682889+0.001162j ± 0.085451
[2025-09-13 08:58:13] ================================================================================
[2025-09-13 08:58:13] 加载量子态: L=4, J2=0.03, J1=0.06, checkpoint=checkpoint_iter_000735
[2025-09-13 08:58:13] 使用采样数目: 1048576
[2025-09-13 08:58:13] 设置样本数为: 1048576
[2025-09-13 08:58:13] 开始生成共享样本集...
[2025-09-13 09:01:14] 样本生成完成,耗时: 181.031 秒
[2025-09-13 09:01:14] ================================================================================
[2025-09-13 09:01:14] 开始计算自旋结构因子...
[2025-09-13 09:01:14] 初始化操作符缓存...
[2025-09-13 09:01:14] 预构建所有自旋相关操作符...
[2025-09-13 09:01:14] 开始计算自旋相关函数...
[2025-09-13 09:01:29] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 14.194s
[2025-09-13 09:01:46] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 17.800s
[2025-09-13 09:01:56] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.364s
[2025-09-13 09:02:05] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.389s
[2025-09-13 09:02:15] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.386s
[2025-09-13 09:02:24] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.355s
[2025-09-13 09:02:33] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.367s
[2025-09-13 09:02:43] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.387s
[2025-09-13 09:02:52] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.362s
[2025-09-13 09:03:01] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.355s
[2025-09-13 09:03:11] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.399s
[2025-09-13 09:03:20] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.284s
[2025-09-13 09:03:30] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.381s
[2025-09-13 09:03:39] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.394s
[2025-09-13 09:03:48] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.396s
[2025-09-13 09:03:58] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.361s
[2025-09-13 09:04:07] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.390s
[2025-09-13 09:04:17] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.392s
[2025-09-13 09:04:26] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.379s
[2025-09-13 09:04:35] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.379s
[2025-09-13 09:04:45] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.368s
[2025-09-13 09:04:54] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.391s
[2025-09-13 09:05:03] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.336s
[2025-09-13 09:05:13] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.380s
[2025-09-13 09:05:22] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.375s
[2025-09-13 09:05:32] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.387s
[2025-09-13 09:05:41] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.358s
[2025-09-13 09:05:50] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.397s
[2025-09-13 09:06:00] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.377s
[2025-09-13 09:06:09] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.377s
[2025-09-13 09:06:19] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.380s
[2025-09-13 09:06:28] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.382s
[2025-09-13 09:06:37] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.354s
[2025-09-13 09:06:47] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.405s
[2025-09-13 09:06:56] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.376s
[2025-09-13 09:07:05] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.378s
[2025-09-13 09:07:15] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.376s
[2025-09-13 09:07:24] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.387s
[2025-09-13 09:07:34] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.373s
[2025-09-13 09:07:43] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.381s
[2025-09-13 09:07:52] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.375s
[2025-09-13 09:08:02] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.374s
[2025-09-13 09:08:11] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.367s
[2025-09-13 09:08:21] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.371s
[2025-09-13 09:08:30] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.376s
[2025-09-13 09:08:39] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.376s
[2025-09-13 09:08:49] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.391s
[2025-09-13 09:08:58] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.379s
[2025-09-13 09:09:07] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.374s
[2025-09-13 09:09:17] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.365s
[2025-09-13 09:09:26] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.377s
[2025-09-13 09:09:36] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.374s
[2025-09-13 09:09:45] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.377s
[2025-09-13 09:09:54] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.380s
[2025-09-13 09:10:04] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.361s
[2025-09-13 09:10:13] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.385s
[2025-09-13 09:10:23] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.372s
[2025-09-13 09:10:32] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.369s
[2025-09-13 09:10:41] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.368s
[2025-09-13 09:10:51] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.360s
[2025-09-13 09:11:00] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.370s
[2025-09-13 09:11:09] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.369s
[2025-09-13 09:11:19] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.384s
[2025-09-13 09:11:28] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.368s
[2025-09-13 09:11:28] 自旋相关函数计算完成,总耗时 613.73 秒
[2025-09-13 09:11:29] 计算傅里叶变换...
[2025-09-13 09:11:35] 自旋结构因子计算完成
[2025-09-13 09:11:36] 自旋相关函数平均误差: 0.000667
