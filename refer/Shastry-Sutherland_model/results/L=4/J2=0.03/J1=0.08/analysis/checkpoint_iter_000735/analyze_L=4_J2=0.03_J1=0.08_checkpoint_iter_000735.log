[2025-09-13 14:00:52] 使用checkpoint文件: results/L=4/J2=0.03/J1=0.08/training/checkpoints/checkpoint_iter_000735.pkl
[2025-09-13 14:01:09] ✓ 从checkpoint加载参数: 735
[2025-09-13 14:01:09]   - 能量: -54.601325-0.003859j ± 0.082583
[2025-09-13 14:01:09] ================================================================================
[2025-09-13 14:01:09] 加载量子态: L=4, J2=0.03, J1=0.08, checkpoint=checkpoint_iter_000735
[2025-09-13 14:01:09] 使用采样数目: 1048576
[2025-09-13 14:01:09] 设置样本数为: 1048576
[2025-09-13 14:01:09] 开始生成共享样本集...
[2025-09-13 14:04:10] 样本生成完成,耗时: 180.968 秒
[2025-09-13 14:04:10] ================================================================================
[2025-09-13 14:04:10] 开始计算自旋结构因子...
[2025-09-13 14:04:10] 初始化操作符缓存...
[2025-09-13 14:04:10] 预构建所有自旋相关操作符...
[2025-09-13 14:04:10] 开始计算自旋相关函数...
[2025-09-13 14:04:25] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 14.130s
[2025-09-13 14:04:42] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 17.779s
[2025-09-13 14:04:52] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.329s
[2025-09-13 14:05:01] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.378s
[2025-09-13 14:05:10] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.367s
[2025-09-13 14:05:20] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.366s
[2025-09-13 14:05:29] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.355s
[2025-09-13 14:05:39] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.372s
[2025-09-13 14:05:48] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.356s
[2025-09-13 14:05:57] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.370s
[2025-09-13 14:06:07] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.362s
[2025-09-13 14:06:16] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.399s
[2025-09-13 14:06:25] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.347s
[2025-09-13 14:06:35] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.357s
[2025-09-13 14:06:44] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.396s
[2025-09-13 14:06:54] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.359s
[2025-09-13 14:07:03] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.367s
[2025-09-13 14:07:12] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.382s
[2025-09-13 14:07:22] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.372s
[2025-09-13 14:07:31] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.364s
[2025-09-13 14:07:40] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.397s
[2025-09-13 14:07:50] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.397s
[2025-09-13 14:07:59] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.422s
[2025-09-13 14:08:09] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.436s
[2025-09-13 14:08:18] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.290s
[2025-09-13 14:08:27] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.390s
[2025-09-13 14:08:37] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.355s
[2025-09-13 14:08:46] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.401s
[2025-09-13 14:08:56] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.362s
[2025-09-13 14:09:05] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.338s
[2025-09-13 14:09:14] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.358s
[2025-09-13 14:09:24] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.367s
[2025-09-13 14:09:33] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.370s
[2025-09-13 14:09:42] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.357s
[2025-09-13 14:09:52] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.401s
[2025-09-13 14:10:01] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.293s
[2025-09-13 14:10:10] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.364s
[2025-09-13 14:10:20] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.392s
[2025-09-13 14:10:29] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.368s
[2025-09-13 14:10:39] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.372s
[2025-09-13 14:10:48] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.372s
[2025-09-13 14:10:57] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.357s
[2025-09-13 14:11:07] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.354s
[2025-09-13 14:11:16] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.393s
[2025-09-13 14:11:25] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.369s
[2025-09-13 14:11:35] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.328s
[2025-09-13 14:11:44] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.404s
[2025-09-13 14:11:54] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.356s
[2025-09-13 14:12:03] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.375s
[2025-09-13 14:12:12] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.366s
[2025-09-13 14:12:22] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.335s
[2025-09-13 14:12:31] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.360s
[2025-09-13 14:12:40] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.356s
[2025-09-13 14:12:50] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.390s
[2025-09-13 14:12:59] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.353s
[2025-09-13 14:13:08] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.322s
[2025-09-13 14:13:18] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.343s
[2025-09-13 14:13:27] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.362s
[2025-09-13 14:13:37] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.357s
[2025-09-13 14:13:46] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.362s
[2025-09-13 14:13:55] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.353s
[2025-09-13 14:14:05] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.347s
[2025-09-13 14:14:14] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.394s
[2025-09-13 14:14:23] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.353s
[2025-09-13 14:14:23] 自旋相关函数计算完成,总耗时 613.03 秒
[2025-09-13 14:14:25] 计算傅里叶变换...
[2025-09-13 14:14:27] 自旋结构因子计算完成
[2025-09-13 14:14:28] 自旋相关函数平均误差: 0.000670
