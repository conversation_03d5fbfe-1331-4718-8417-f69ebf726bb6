[2025-09-12 00:31:49] ✓ 从checkpoint恢复: results/L=4/J2=0.03/J1=0.07/training/checkpoints/final_GCNN.pkl
[2025-09-12 00:31:49]   - 迭代次数: final
[2025-09-12 00:31:49]   - 能量: -54.082578-0.001117j ± 0.083612
[2025-09-12 00:31:49]   - 时间戳: 2025-09-12T00:31:37.957813+08:00
[2025-09-12 00:32:15] ✓ 变分状态参数已从checkpoint恢复
[2025-09-12 00:32:15] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-12 00:32:15] ==================================================
[2025-09-12 00:32:15] GCNN for Shastry-Sutherland Model
[2025-09-12 00:32:15] ==================================================
[2025-09-12 00:32:15] System parameters:
[2025-09-12 00:32:15]   - System size: L=4, N=64
[2025-09-12 00:32:15]   - System parameters: J1=0.08, J2=0.03, Q=0.97
[2025-09-12 00:32:15] --------------------------------------------------
[2025-09-12 00:32:15] Model parameters:
[2025-09-12 00:32:15]   - Number of layers = 4
[2025-09-12 00:32:15]   - Number of features = 4
[2025-09-12 00:32:15]   - Total parameters = 12572
[2025-09-12 00:32:15] --------------------------------------------------
[2025-09-12 00:32:15] Training parameters:
[2025-09-12 00:32:15]   - Learning rate: 0.015
[2025-09-12 00:32:15]   - Total iterations: 1050
[2025-09-12 00:32:15]   - Annealing cycles: 3
[2025-09-12 00:32:15]   - Initial period: 150
[2025-09-12 00:32:15]   - Period multiplier: 2.0
[2025-09-12 00:32:15]   - Temperature range: 0.0-1.0
[2025-09-12 00:32:15]   - Samples: 4096
[2025-09-12 00:32:15]   - Discarded samples: 0
[2025-09-12 00:32:15]   - Chunk size: 2048
[2025-09-12 00:32:15]   - Diagonal shift: 0.2
[2025-09-12 00:32:15]   - Gradient clipping: 1.0
[2025-09-12 00:32:15]   - Checkpoint enabled: interval=105
[2025-09-12 00:32:15]   - Checkpoint directory: results/L=4/J2=0.03/J1=0.08/training/checkpoints
[2025-09-12 00:32:15] --------------------------------------------------
[2025-09-12 00:32:15] Device status:
[2025-09-12 00:32:15]   - Devices model: NVIDIA H200 NVL
[2025-09-12 00:32:15]   - Number of devices: 1
[2025-09-12 00:32:15]   - Sharding: True
[2025-09-12 00:32:15] ============================================================
[2025-09-12 00:34:11] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -54.425454-0.002327j
[2025-09-12 00:35:19] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -54.386105+0.001991j
[2025-09-12 00:35:34] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -54.490776+0.004093j
[2025-09-12 00:35:49] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -54.460657+0.002879j
[2025-09-12 00:36:04] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -54.507376+0.001314j
[2025-09-12 00:36:20] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -54.479473-0.001401j
[2025-09-12 00:36:35] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -54.544913-0.000220j
[2025-09-12 00:36:50] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -54.513645+0.001729j
[2025-09-12 00:37:05] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -54.358209+0.001913j
[2025-09-12 00:37:21] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -54.316538+0.002470j
[2025-09-12 00:37:36] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -54.546717+0.002515j
[2025-09-12 00:37:51] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -54.423015-0.001871j
[2025-09-12 00:38:06] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -54.452598+0.003474j
[2025-09-12 00:38:21] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -54.405960-0.000440j
[2025-09-12 00:38:37] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -54.450825+0.002514j
[2025-09-12 00:38:52] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -54.530280+0.000613j
[2025-09-12 00:39:07] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -54.432074+0.000989j
[2025-09-12 00:39:22] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -54.398700-0.001480j
[2025-09-12 00:39:38] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -54.371389-0.001987j
[2025-09-12 00:39:53] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -54.447046-0.002132j
[2025-09-12 00:40:08] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -54.399365+0.002260j
[2025-09-12 00:40:23] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -54.541083+0.000623j
[2025-09-12 00:40:39] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -54.561731+0.000027j
[2025-09-12 00:40:54] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -54.567936-0.000185j
[2025-09-12 00:41:09] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -54.463711+0.002127j
[2025-09-12 00:41:24] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -54.327036-0.000328j
[2025-09-12 00:41:40] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -54.367294-0.001628j
[2025-09-12 00:41:55] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -54.414563+0.002047j
[2025-09-12 00:42:10] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -54.393936-0.001073j
[2025-09-12 00:42:25] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -54.443402-0.000820j
[2025-09-12 00:42:41] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -54.497350+0.002824j
[2025-09-12 00:42:56] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -54.479649+0.000500j
[2025-09-12 00:43:11] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -54.374812+0.001666j
[2025-09-12 00:43:26] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -54.338524+0.000464j
[2025-09-12 00:43:42] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -54.393036+0.001406j
[2025-09-12 00:43:57] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -54.405283-0.002200j
[2025-09-12 00:44:12] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -54.416483-0.001346j
[2025-09-12 00:44:27] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -54.577209+0.002612j
[2025-09-12 00:44:42] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -54.468716+0.004467j
[2025-09-12 00:44:58] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -54.428174+0.004989j
[2025-09-12 00:45:13] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -54.479158-0.001733j
[2025-09-12 00:45:28] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -54.492543-0.001114j
[2025-09-12 00:45:43] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -54.481613+0.002187j
[2025-09-12 00:45:58] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -54.426581+0.001271j
[2025-09-12 00:46:14] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -54.492800-0.003332j
[2025-09-12 00:46:29] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -54.429835-0.000109j
[2025-09-12 00:46:44] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -54.390170-0.000582j
[2025-09-12 00:46:59] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -54.337532+0.002202j
[2025-09-12 00:47:15] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -54.548703-0.001294j
[2025-09-12 00:47:30] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -54.473589-0.002091j
[2025-09-12 00:47:45] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -54.459707-0.000343j
[2025-09-12 00:48:00] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -54.470566-0.001500j
[2025-09-12 00:48:16] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -54.374996-0.000067j
[2025-09-12 00:48:31] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -54.490963+0.005739j
[2025-09-12 00:48:46] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -54.465784+0.000480j
[2025-09-12 00:49:01] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -54.448502+0.001196j
[2025-09-12 00:49:17] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -54.469698-0.001057j
[2025-09-12 00:49:32] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -54.621793+0.001915j
[2025-09-12 00:49:47] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -54.585331+0.000615j
[2025-09-12 00:50:02] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -54.543098+0.000956j
[2025-09-12 00:50:18] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -54.529994+0.003513j
[2025-09-12 00:50:33] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -54.473372+0.000469j
[2025-09-12 00:50:48] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -54.453050-0.001568j
[2025-09-12 00:51:03] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -54.464203-0.001215j
[2025-09-12 00:51:19] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -54.433882-0.003199j
[2025-09-12 00:51:34] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -54.328290-0.002362j
[2025-09-12 00:51:49] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -54.440673-0.000484j
[2025-09-12 00:52:04] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -54.519440-0.000905j
[2025-09-12 00:52:20] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -54.474977-0.003481j
[2025-09-12 00:52:35] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -54.735416+0.000823j
[2025-09-12 00:52:50] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -54.565677-0.000439j
[2025-09-12 00:53:06] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -54.611371-0.003351j
[2025-09-12 00:53:21] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -54.524781-0.000631j
[2025-09-12 00:53:36] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -54.407865+0.001844j
[2025-09-12 00:53:51] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -54.451825+0.001806j
[2025-09-12 00:54:06] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -54.579796+0.002017j
[2025-09-12 00:54:22] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -54.427874-0.003919j
[2025-09-12 00:54:37] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -54.433797-0.000917j
[2025-09-12 00:54:52] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -54.449349+0.001024j
[2025-09-12 00:55:07] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -54.414942+0.000843j
[2025-09-12 00:55:23] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -54.341058-0.004762j
[2025-09-12 00:55:38] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -54.367720-0.000580j
[2025-09-12 00:55:53] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -54.395610+0.000157j
[2025-09-12 00:56:08] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -54.310777-0.001746j
[2025-09-12 00:56:24] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -54.454820+0.000958j
[2025-09-12 00:56:39] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -54.490342+0.000542j
[2025-09-12 00:56:54] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -54.486935+0.004533j
[2025-09-12 00:57:09] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -54.559781+0.003152j
[2025-09-12 00:57:25] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -54.625838+0.002347j
[2025-09-12 00:57:40] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -54.656574+0.000272j
[2025-09-12 00:57:55] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -54.648499+0.001224j
[2025-09-12 00:58:11] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -54.574232-0.004008j
[2025-09-12 00:58:26] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -54.625113-0.002492j
[2025-09-12 00:58:41] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -54.520923+0.001281j
[2025-09-12 00:58:56] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -54.516296-0.003584j
[2025-09-12 00:59:12] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -54.472490-0.002715j
[2025-09-12 00:59:27] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -54.477795-0.001251j
[2025-09-12 00:59:42] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -54.378156-0.001795j
[2025-09-12 00:59:57] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -54.417148+0.001896j
[2025-09-12 01:00:13] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -54.464961-0.002869j
[2025-09-12 01:00:28] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -54.525436-0.000644j
[2025-09-12 01:00:43] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -54.419408+0.001320j
[2025-09-12 01:00:58] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -54.347157-0.001371j
[2025-09-12 01:01:14] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -54.433023-0.003634j
[2025-09-12 01:01:29] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -54.502510-0.001920j
[2025-09-12 01:01:29] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-12 01:01:44] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -54.487546+0.000002j
[2025-09-12 01:01:59] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -54.496042+0.001343j
[2025-09-12 01:02:14] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -54.493271+0.002687j
[2025-09-12 01:02:30] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -54.517742+0.003509j
[2025-09-12 01:02:45] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -54.363849-0.001752j
[2025-09-12 01:02:55] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -54.548936+0.002933j
[2025-09-12 01:03:05] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -54.446313+0.000131j
[2025-09-12 01:03:16] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -54.540512-0.001653j
[2025-09-12 01:03:26] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -54.570748-0.000565j
[2025-09-12 01:03:36] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -54.629387-0.000562j
[2025-09-12 01:03:46] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -54.626039-0.003712j
[2025-09-12 01:03:58] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -54.569726+0.000512j
[2025-09-12 01:04:14] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -54.435380+0.004900j
[2025-09-12 01:04:29] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -54.552072-0.004121j
[2025-09-12 01:04:44] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -54.526703-0.000924j
[2025-09-12 01:04:59] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -54.400414+0.001907j
[2025-09-12 01:05:15] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -54.527280+0.004273j
[2025-09-12 01:05:30] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -54.425393+0.000481j
[2025-09-12 01:05:45] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -54.471248+0.000534j
[2025-09-12 01:06:01] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -54.456141+0.000435j
[2025-09-12 01:06:16] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -54.497528+0.001187j
[2025-09-12 01:06:31] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -54.667777-0.000447j
[2025-09-12 01:06:46] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -54.586789-0.000468j
[2025-09-12 01:07:02] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -54.526388+0.000927j
[2025-09-12 01:07:17] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -54.492017-0.001617j
[2025-09-12 01:07:32] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -54.465447+0.000947j
[2025-09-12 01:07:48] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -54.459250+0.012656j
[2025-09-12 01:08:03] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -54.422366-0.001388j
[2025-09-12 01:08:18] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -54.421622-0.002332j
[2025-09-12 01:08:33] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -54.526553-0.001798j
[2025-09-12 01:08:49] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -54.446067+0.000301j
[2025-09-12 01:09:04] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -54.541412-0.001741j
[2025-09-12 01:09:19] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -54.572566-0.000589j
[2025-09-12 01:09:34] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -54.501238-0.001149j
[2025-09-12 01:09:50] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -54.441163-0.000834j
[2025-09-12 01:10:05] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -54.480157+0.000047j
[2025-09-12 01:10:20] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -54.414715-0.001029j
[2025-09-12 01:10:35] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -54.346115-0.001055j
[2025-09-12 01:10:51] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -54.416079-0.001003j
[2025-09-12 01:11:06] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -54.477303-0.001442j
[2025-09-12 01:11:21] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -54.420860+0.003599j
[2025-09-12 01:11:37] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -54.548592+0.000107j
[2025-09-12 01:11:52] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -54.417897+0.000425j
[2025-09-12 01:12:07] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -54.336284+0.001167j
[2025-09-12 01:12:22] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -54.436079+0.000037j
[2025-09-12 01:12:22] RESTART #1 | Period: 300
[2025-09-12 01:12:38] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -54.422177+0.000873j
[2025-09-12 01:12:53] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -54.348141-0.001193j
[2025-09-12 01:13:08] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -54.408882-0.004506j
[2025-09-12 01:13:23] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -54.435120-0.002236j
[2025-09-12 01:13:33] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -54.508244-0.001582j
[2025-09-12 01:13:47] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -54.529139+0.001813j
[2025-09-12 01:13:57] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -54.456433+0.002110j
[2025-09-12 01:14:12] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -54.569381-0.000953j
[2025-09-12 01:14:27] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -54.509964-0.001282j
[2025-09-12 01:14:42] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -54.455480-0.000758j
[2025-09-12 01:14:57] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -54.413377+0.000632j
[2025-09-12 01:15:12] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -54.503946+0.002399j
[2025-09-12 01:15:28] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -54.582770+0.000604j
[2025-09-12 01:15:43] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -54.569071+0.003217j
[2025-09-12 01:15:58] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -54.621820+0.001645j
[2025-09-12 01:16:13] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -54.546871-0.000823j
[2025-09-12 01:16:29] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -54.529338+0.000006j
[2025-09-12 01:16:44] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -54.593530+0.001809j
[2025-09-12 01:16:59] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -54.532533+0.001620j
[2025-09-12 01:17:14] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -54.513304-0.004382j
[2025-09-12 01:17:30] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -54.487661-0.000440j
[2025-09-12 01:17:45] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -54.475911+0.001392j
[2025-09-12 01:18:00] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -54.405658-0.002030j
[2025-09-12 01:18:15] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -54.339570-0.000963j
[2025-09-12 01:18:31] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -54.337548-0.001085j
[2025-09-12 01:18:46] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -54.471904+0.002710j
[2025-09-12 01:19:01] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -54.509780+0.000347j
[2025-09-12 01:19:16] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -54.422856-0.000027j
[2025-09-12 01:19:32] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -54.480091+0.001217j
[2025-09-12 01:19:47] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -54.400543+0.001574j
[2025-09-12 01:20:02] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -54.567771+0.000098j
[2025-09-12 01:20:17] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -54.392688-0.000237j
[2025-09-12 01:20:33] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -54.462306+0.002934j
[2025-09-12 01:20:48] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -54.376335+0.001021j
[2025-09-12 01:21:03] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -54.449568+0.002211j
[2025-09-12 01:21:18] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -54.288236+0.000617j
[2025-09-12 01:21:34] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -54.278725+0.000592j
[2025-09-12 01:21:49] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -54.305062-0.000904j
[2025-09-12 01:22:04] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -54.297519-0.000276j
[2025-09-12 01:22:19] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -54.363739-0.002472j
[2025-09-12 01:22:34] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -54.460467-0.000901j
[2025-09-12 01:22:50] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -54.350785+0.000586j
[2025-09-12 01:23:05] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -54.348609+0.002037j
[2025-09-12 01:23:20] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -54.328992+0.000677j
[2025-09-12 01:23:35] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -54.542623-0.001097j
[2025-09-12 01:23:51] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -54.431284+0.001608j
[2025-09-12 01:24:06] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -54.441658-0.000578j
[2025-09-12 01:24:21] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -54.448209-0.001112j
[2025-09-12 01:24:36] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -54.609281-0.000745j
[2025-09-12 01:24:52] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -54.504171-0.001481j
[2025-09-12 01:25:07] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -54.466967+0.000891j
[2025-09-12 01:25:22] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -54.465537-0.000353j
[2025-09-12 01:25:37] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -54.485190-0.001212j
[2025-09-12 01:25:53] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -54.573506-0.002067j
[2025-09-12 01:26:08] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -54.472446+0.001171j
[2025-09-12 01:26:23] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -54.362884+0.001069j
[2025-09-12 01:26:38] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -54.431579+0.000549j
[2025-09-12 01:26:53] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -54.528443+0.001844j
[2025-09-12 01:27:09] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -54.594800-0.000226j
[2025-09-12 01:27:24] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -54.593765+0.000364j
[2025-09-12 01:27:24] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-12 01:27:39] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -54.625892+0.000118j
[2025-09-12 01:27:54] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -54.632874+0.002697j
[2025-09-12 01:28:10] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -54.488005-0.000925j
[2025-09-12 01:28:25] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -54.488422-0.002156j
[2025-09-12 01:28:40] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -54.428350+0.000295j
[2025-09-12 01:28:55] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -54.362335+0.000285j
[2025-09-12 01:29:11] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -54.346453+0.000850j
[2025-09-12 01:29:26] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -54.333835+0.004092j
[2025-09-12 01:29:41] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -54.511658+0.002748j
[2025-09-12 01:29:56] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -54.441665+0.001308j
[2025-09-12 01:30:12] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -54.434832+0.000404j
[2025-09-12 01:30:27] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -54.449164+0.001495j
[2025-09-12 01:30:42] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -54.321945+0.002408j
[2025-09-12 01:30:57] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -54.440233-0.002486j
[2025-09-12 01:31:13] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -54.381410-0.002192j
[2025-09-12 01:31:28] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -54.421175+0.001493j
[2025-09-12 01:31:43] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -54.525676+0.001277j
[2025-09-12 01:31:58] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -54.434106-0.000269j
[2025-09-12 01:32:13] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -54.581123+0.005093j
[2025-09-12 01:32:29] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -54.619065+0.001763j
[2025-09-12 01:32:44] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -54.646615+0.000788j
[2025-09-12 01:32:59] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -54.427956-0.002091j
[2025-09-12 01:33:14] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -54.646219+0.001895j
[2025-09-12 01:33:30] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -54.589414+0.000360j
[2025-09-12 01:33:45] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -54.453972-0.000530j
[2025-09-12 01:34:00] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -54.443431+0.002140j
[2025-09-12 01:34:15] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -54.547267-0.000302j
[2025-09-12 01:34:31] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -54.583820-0.001985j
[2025-09-12 01:34:46] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -54.584881-0.001393j
[2025-09-12 01:35:01] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -54.623556-0.002113j
[2025-09-12 01:35:16] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -54.592728-0.001344j
[2025-09-12 01:35:32] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -54.543277+0.000434j
[2025-09-12 01:35:47] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -54.451284+0.002284j
[2025-09-12 01:36:02] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -54.476584+0.002493j
[2025-09-12 01:36:17] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -54.512633-0.003005j
[2025-09-12 01:36:32] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -54.462856-0.002201j
[2025-09-12 01:36:48] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -54.503214-0.002088j
[2025-09-12 01:37:03] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -54.316741+0.002806j
[2025-09-12 01:37:18] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -54.552795-0.000069j
[2025-09-12 01:37:33] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -54.250941-0.001366j
[2025-09-12 01:37:49] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -54.493407-0.001910j
[2025-09-12 01:38:04] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -54.576541+0.002025j
[2025-09-12 01:38:19] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -54.702825+0.000128j
[2025-09-12 01:38:34] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -54.626989+0.000752j
[2025-09-12 01:38:50] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -54.568253-0.000642j
[2025-09-12 01:39:05] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -54.480415+0.001232j
[2025-09-12 01:39:20] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -54.478568+0.002120j
[2025-09-12 01:39:35] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -54.586405-0.001228j
[2025-09-12 01:39:51] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -54.492839+0.002727j
[2025-09-12 01:40:06] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -54.448904-0.001797j
[2025-09-12 01:40:21] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -54.409191+0.000889j
[2025-09-12 01:40:36] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -54.567163-0.000431j
[2025-09-12 01:40:51] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -54.426531+0.000098j
[2025-09-12 01:41:07] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -54.419187+0.000888j
[2025-09-12 01:41:22] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -54.359549+0.002097j
[2025-09-12 01:41:37] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -54.440023-0.000189j
[2025-09-12 01:41:53] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -54.501246-0.000029j
[2025-09-12 01:42:08] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -54.430217+0.000041j
[2025-09-12 01:42:23] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -54.398055+0.002187j
[2025-09-12 01:42:38] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -54.439554+0.001968j
[2025-09-12 01:42:53] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -54.415239-0.001837j
[2025-09-12 01:43:09] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -54.440968-0.002972j
[2025-09-12 01:43:24] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -54.472023+0.000169j
[2025-09-12 01:43:39] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -54.665018+0.001613j
[2025-09-12 01:43:54] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -54.567702+0.002161j
[2025-09-12 01:44:09] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -54.571194-0.002478j
[2025-09-12 01:44:25] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -54.587705-0.002126j
[2025-09-12 01:44:40] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -54.569305+0.004538j
[2025-09-12 01:44:55] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -54.562294+0.000588j
[2025-09-12 01:45:10] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -54.475688-0.000851j
[2025-09-12 01:45:25] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -54.547151-0.000228j
[2025-09-12 01:45:41] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -54.481612-0.002107j
[2025-09-12 01:45:56] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -54.411616-0.001430j
[2025-09-12 01:46:11] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -54.463350-0.000963j
[2025-09-12 01:46:26] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -54.518790-0.002125j
[2025-09-12 01:46:42] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -54.608245-0.000389j
[2025-09-12 01:46:57] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -54.649371-0.000498j
[2025-09-12 01:47:12] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -54.538821-0.001710j
[2025-09-12 01:47:23] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -54.574513-0.008954j
[2025-09-12 01:47:33] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -54.488565+0.000380j
[2025-09-12 01:47:44] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -54.537366+0.001963j
[2025-09-12 01:47:54] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -54.421992-0.003338j
[2025-09-12 01:48:04] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -54.575153+0.001418j
[2025-09-12 01:48:14] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -54.522175-0.000270j
[2025-09-12 01:48:27] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -54.513892-0.001022j
[2025-09-12 01:48:42] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -54.520468-0.001914j
[2025-09-12 01:48:57] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -54.549380+0.002969j
[2025-09-12 01:49:12] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -54.498268-0.000022j
[2025-09-12 01:49:27] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -54.519968-0.004182j
[2025-09-12 01:49:43] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -54.553212+0.000271j
[2025-09-12 01:49:58] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -54.551425-0.003466j
[2025-09-12 01:50:13] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -54.410869+0.000996j
[2025-09-12 01:50:28] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -54.460838-0.000146j
[2025-09-12 01:50:44] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -54.436426-0.002239j
[2025-09-12 01:50:59] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -54.422098-0.001220j
[2025-09-12 01:51:14] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -54.541657-0.003125j
[2025-09-12 01:51:30] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -54.585226+0.000196j
[2025-09-12 01:51:45] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -54.484035-0.001630j
[2025-09-12 01:52:00] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -54.352901-0.003619j
[2025-09-12 01:52:16] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -54.379046-0.002258j
[2025-09-12 01:52:31] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -54.423094-0.001635j
[2025-09-12 01:52:46] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -54.423112-0.018834j
[2025-09-12 01:53:01] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -54.348617+0.000236j
[2025-09-12 01:53:17] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -54.354069-0.001182j
[2025-09-12 01:53:32] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -54.421736+0.003801j
[2025-09-12 01:53:32] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-12 01:53:47] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -54.420645+0.000986j
[2025-09-12 01:54:02] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -54.439117-0.002059j
[2025-09-12 01:54:18] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -54.414536-0.002819j
[2025-09-12 01:54:33] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -54.470003+0.000864j
[2025-09-12 01:54:48] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -54.555044-0.001314j
[2025-09-12 01:55:04] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -54.522704-0.000898j
[2025-09-12 01:55:19] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -54.320577+0.001637j
[2025-09-12 01:55:34] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -54.404017+0.000139j
[2025-09-12 01:55:49] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -54.449537+0.000746j
[2025-09-12 01:56:05] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -54.415538-0.000700j
[2025-09-12 01:56:20] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -54.376537-0.000228j
[2025-09-12 01:56:35] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -54.434679-0.001830j
[2025-09-12 01:56:50] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -54.522435-0.000440j
[2025-09-12 01:57:06] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -54.472642-0.002602j
[2025-09-12 01:57:21] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -54.422355-0.001191j
[2025-09-12 01:57:36] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -54.435138-0.002655j
[2025-09-12 01:57:51] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -54.415850-0.000750j
[2025-09-12 01:58:01] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -54.471166+0.002016j
[2025-09-12 01:58:15] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -54.443967+0.003257j
[2025-09-12 01:58:26] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -54.466357-0.001208j
[2025-09-12 01:58:40] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -54.427675-0.001500j
[2025-09-12 01:58:55] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -54.455948+0.000245j
[2025-09-12 01:59:10] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -54.421426-0.003196j
[2025-09-12 01:59:25] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -54.470631-0.001899j
[2025-09-12 01:59:40] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -54.428795-0.000966j
[2025-09-12 01:59:56] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -54.483850+0.000475j
[2025-09-12 02:00:11] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -54.435892+0.000954j
[2025-09-12 02:00:26] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -54.431789+0.001192j
[2025-09-12 02:00:41] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -54.477764+0.002287j
[2025-09-12 02:00:56] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -54.635729-0.002945j
[2025-09-12 02:01:12] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -54.653048+0.000101j
[2025-09-12 02:01:27] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -54.604932-0.001007j
[2025-09-12 02:01:42] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -54.444174-0.002446j
[2025-09-12 02:01:57] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -54.498606+0.003559j
[2025-09-12 02:02:12] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -54.580023-0.000354j
[2025-09-12 02:02:27] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -54.488461+0.003067j
[2025-09-12 02:02:43] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -54.488654-0.001130j
[2025-09-12 02:02:58] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -54.475424-0.001522j
[2025-09-12 02:03:13] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -54.480056-0.002874j
[2025-09-12 02:03:28] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -54.306533-0.002227j
[2025-09-12 02:03:43] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -54.309978+0.001384j
[2025-09-12 02:03:59] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -54.528925+0.002685j
[2025-09-12 02:04:14] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -54.528867-0.000207j
[2025-09-12 02:04:29] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -54.490610+0.001095j
[2025-09-12 02:04:44] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -54.439547-0.001545j
[2025-09-12 02:04:59] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -54.605475-0.002645j
[2025-09-12 02:05:14] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -54.444613-0.000464j
[2025-09-12 02:05:30] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -54.457070-0.001060j
[2025-09-12 02:05:45] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -54.322292-0.000568j
[2025-09-12 02:06:00] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -54.436041+0.000356j
[2025-09-12 02:06:15] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -54.591707-0.001100j
[2025-09-12 02:06:30] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -54.528347-0.000012j
[2025-09-12 02:06:46] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -54.467575+0.001762j
[2025-09-12 02:07:01] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -54.382375-0.000816j
[2025-09-12 02:07:16] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -54.368546+0.002695j
[2025-09-12 02:07:31] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -54.414752+0.002872j
[2025-09-12 02:07:46] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -54.486037+0.002415j
[2025-09-12 02:08:02] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -54.401689+0.001727j
[2025-09-12 02:08:17] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -54.484732-0.001275j
[2025-09-12 02:08:32] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -54.463135-0.000085j
[2025-09-12 02:08:47] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -54.479853+0.000515j
[2025-09-12 02:09:03] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -54.532212+0.001817j
[2025-09-12 02:09:18] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -54.501325+0.000225j
[2025-09-12 02:09:33] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -54.567681+0.004237j
[2025-09-12 02:09:48] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -54.524246+0.002255j
[2025-09-12 02:10:03] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -54.359095-0.001651j
[2025-09-12 02:10:18] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -54.418580-0.002281j
[2025-09-12 02:10:34] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -54.454076-0.001188j
[2025-09-12 02:10:49] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -54.383142-0.003235j
[2025-09-12 02:11:04] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -54.389722+0.000338j
[2025-09-12 02:11:19] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -54.486916-0.001175j
[2025-09-12 02:11:34] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -54.417035+0.001173j
[2025-09-12 02:11:50] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -54.474177-0.000800j
[2025-09-12 02:12:05] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -54.456058+0.002894j
[2025-09-12 02:12:20] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -54.519351+0.000752j
[2025-09-12 02:12:35] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -54.522996+0.003408j
[2025-09-12 02:12:50] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -54.487918+0.001581j
[2025-09-12 02:13:06] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -54.490792+0.001606j
[2025-09-12 02:13:21] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -54.588864+0.001175j
[2025-09-12 02:13:36] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -54.485218+0.000592j
[2025-09-12 02:13:51] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -54.374178+0.001183j
[2025-09-12 02:14:06] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -54.385297+0.001244j
[2025-09-12 02:14:22] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -54.347595+0.000561j
[2025-09-12 02:14:37] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -54.554509+0.002544j
[2025-09-12 02:14:52] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -54.429851+0.000265j
[2025-09-12 02:15:07] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -54.495405+0.000901j
[2025-09-12 02:15:22] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -54.531001+0.003802j
[2025-09-12 02:15:38] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -54.493594+0.001737j
[2025-09-12 02:15:53] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -54.464830+0.002910j
[2025-09-12 02:16:08] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -54.565420+0.001972j
[2025-09-12 02:16:23] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -54.595695+0.003568j
[2025-09-12 02:16:38] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -54.481007-0.002114j
[2025-09-12 02:16:54] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -54.589174+0.001122j
[2025-09-12 02:17:09] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -54.453100-0.000538j
[2025-09-12 02:17:24] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -54.552604-0.001387j
[2025-09-12 02:17:39] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -54.414120+0.000321j
[2025-09-12 02:17:54] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -54.365233+0.000159j
[2025-09-12 02:18:10] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -54.425968-0.000959j
[2025-09-12 02:18:25] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -54.484173+0.001810j
[2025-09-12 02:18:40] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -54.624833-0.002296j
[2025-09-12 02:18:55] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -54.611682-0.002566j
[2025-09-12 02:19:10] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -54.492224-0.001626j
[2025-09-12 02:19:26] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -54.470016-0.001436j
[2025-09-12 02:19:41] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -54.337995+0.003428j
[2025-09-12 02:19:56] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -54.414339+0.002328j
[2025-09-12 02:19:56] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-12 02:20:11] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -54.544817-0.000091j
[2025-09-12 02:20:26] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -54.588414+0.001906j
[2025-09-12 02:20:42] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -54.462837+0.001125j
[2025-09-12 02:20:57] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -54.416470+0.003605j
[2025-09-12 02:21:12] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -54.369748+0.000479j
[2025-09-12 02:21:27] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -54.539174+0.001325j
[2025-09-12 02:21:42] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -54.449405+0.001062j
[2025-09-12 02:21:58] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -54.410455-0.000577j
[2025-09-12 02:22:13] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -54.477966-0.002253j
[2025-09-12 02:22:28] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -54.492310-0.000278j
[2025-09-12 02:22:43] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -54.536108-0.000844j
[2025-09-12 02:22:59] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -54.477388+0.000649j
[2025-09-12 02:23:14] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -54.404440-0.000201j
[2025-09-12 02:23:29] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -54.436570-0.003481j
[2025-09-12 02:23:44] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -54.412280+0.003751j
[2025-09-12 02:23:59] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -54.435221-0.001358j
[2025-09-12 02:24:15] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -54.511134-0.002528j
[2025-09-12 02:24:30] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -54.531109+0.002039j
[2025-09-12 02:24:45] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -54.547460-0.002472j
[2025-09-12 02:25:00] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -54.620052-0.001066j
[2025-09-12 02:25:15] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -54.540457+0.000298j
[2025-09-12 02:25:31] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -54.495069-0.001686j
[2025-09-12 02:25:46] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -54.492507+0.001885j
[2025-09-12 02:26:01] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -54.450211+0.001911j
[2025-09-12 02:26:16] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -54.546197+0.003050j
[2025-09-12 02:26:32] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -54.510621-0.001195j
[2025-09-12 02:26:47] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -54.526535-0.002742j
[2025-09-12 02:27:02] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -54.459955+0.000241j
[2025-09-12 02:27:17] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -54.508460+0.000628j
[2025-09-12 02:27:32] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -54.523242+0.001644j
[2025-09-12 02:27:32] RESTART #2 | Period: 600
[2025-09-12 02:27:47] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -54.593972-0.001525j
[2025-09-12 02:28:03] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -54.638196-0.001863j
[2025-09-12 02:28:18] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -54.491265+0.002007j
[2025-09-12 02:28:33] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -54.431311+0.001953j
[2025-09-12 02:28:48] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -54.530516-0.001461j
[2025-09-12 02:29:04] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -54.556322+0.002163j
[2025-09-12 02:29:19] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -54.590008+0.000019j
[2025-09-12 02:29:34] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -54.493598-0.001435j
[2025-09-12 02:29:49] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -54.540514+0.000746j
[2025-09-12 02:30:04] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -54.470428+0.000049j
[2025-09-12 02:30:20] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -54.511689-0.000451j
[2025-09-12 02:30:35] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -54.583012-0.003321j
[2025-09-12 02:30:50] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -54.404898+0.002909j
[2025-09-12 02:31:05] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -54.349149-0.001566j
[2025-09-12 02:31:20] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -54.364651+0.003936j
[2025-09-12 02:31:36] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -54.401190+0.002617j
[2025-09-12 02:31:51] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -54.364263-0.003562j
[2025-09-12 02:32:03] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -54.410959-0.001119j
[2025-09-12 02:32:13] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -54.403307-0.000286j
[2025-09-12 02:32:23] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -54.392733-0.001805j
[2025-09-12 02:32:33] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -54.382011+0.003599j
[2025-09-12 02:32:43] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -54.499952+0.000691j
[2025-09-12 02:32:53] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -54.485844-0.002261j
[2025-09-12 02:33:03] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -54.490921-0.003466j
[2025-09-12 02:33:17] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -54.525823-0.002252j
[2025-09-12 02:33:32] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -54.432086-0.002331j
[2025-09-12 02:33:47] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -54.525682+0.002734j
[2025-09-12 02:34:02] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -54.523719-0.001224j
[2025-09-12 02:34:18] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -54.288010-0.001209j
[2025-09-12 02:34:33] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -54.553739+0.000198j
[2025-09-12 02:34:48] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -54.430479-0.000122j
[2025-09-12 02:35:03] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -54.442312-0.000513j
[2025-09-12 02:35:19] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -54.504055-0.004466j
[2025-09-12 02:35:34] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -54.548713-0.002472j
[2025-09-12 02:35:49] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -54.525105+0.002139j
[2025-09-12 02:36:04] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -54.574351+0.000451j
[2025-09-12 02:36:20] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -54.480485-0.000028j
[2025-09-12 02:36:35] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -54.545062+0.001545j
[2025-09-12 02:36:50] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -54.453895-0.001640j
[2025-09-12 02:37:05] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -54.494985+0.001094j
[2025-09-12 02:37:21] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -54.572685+0.000479j
[2025-09-12 02:37:36] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -54.503450-0.001526j
[2025-09-12 02:37:51] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -54.566917-0.001873j
[2025-09-12 02:38:07] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -54.477720+0.000161j
[2025-09-12 02:38:22] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -54.545114+0.000552j
[2025-09-12 02:38:37] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -54.479484+0.001207j
[2025-09-12 02:38:52] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -54.413373-0.000164j
[2025-09-12 02:39:08] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -54.475833-0.001056j
[2025-09-12 02:39:23] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -54.435824+0.000973j
[2025-09-12 02:39:38] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -54.476683-0.000786j
[2025-09-12 02:39:53] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -54.481025-0.003344j
[2025-09-12 02:40:09] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -54.474218-0.003748j
[2025-09-12 02:40:24] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -54.361963+0.003186j
[2025-09-12 02:40:39] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -54.394657-0.000250j
[2025-09-12 02:40:55] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -54.530874+0.002062j
[2025-09-12 02:41:10] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -54.491271-0.001836j
[2025-09-12 02:41:25] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -54.420659+0.002035j
[2025-09-12 02:41:40] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -54.479729+0.002271j
[2025-09-12 02:41:56] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -54.486828+0.001190j
[2025-09-12 02:42:11] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -54.459936+0.002268j
[2025-09-12 02:42:26] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -54.505904+0.000271j
[2025-09-12 02:42:41] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -54.572597-0.000826j
[2025-09-12 02:42:52] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -54.529957-0.002308j
[2025-09-12 02:43:05] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -54.460135-0.001396j
[2025-09-12 02:43:16] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -54.478096-0.000447j
[2025-09-12 02:43:30] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -54.587004+0.000566j
[2025-09-12 02:43:45] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -54.347272+0.000333j
[2025-09-12 02:44:00] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -54.372419+0.004240j
[2025-09-12 02:44:15] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -54.449564-0.001217j
[2025-09-12 02:44:30] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -54.557402-0.001500j
[2025-09-12 02:44:46] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -54.464728+0.001690j
[2025-09-12 02:45:01] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -54.418008+0.002102j
[2025-09-12 02:45:16] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -54.375543-0.000594j
[2025-09-12 02:45:31] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -54.376188+0.000435j
[2025-09-12 02:45:46] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -54.464179+0.001201j
[2025-09-12 02:45:46] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-12 02:46:02] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -54.464909-0.001998j
[2025-09-12 02:46:17] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -54.381508-0.000227j
[2025-09-12 02:46:32] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -54.496713+0.000668j
[2025-09-12 02:46:47] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -54.492421-0.002080j
[2025-09-12 02:47:03] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -54.606287-0.000845j
[2025-09-12 02:47:18] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -54.460276-0.001389j
[2025-09-12 02:47:33] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -54.468179+0.001618j
[2025-09-12 02:47:48] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -54.455165-0.000613j
[2025-09-12 02:48:03] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -54.491765-0.000905j
[2025-09-12 02:48:19] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -54.468917-0.000272j
[2025-09-12 02:48:34] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -54.473092-0.002534j
[2025-09-12 02:48:49] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -54.499529+0.002108j
[2025-09-12 02:49:04] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -54.575038-0.001558j
[2025-09-12 02:49:20] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -54.580695+0.000657j
[2025-09-12 02:49:35] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -54.565400+0.000146j
[2025-09-12 02:49:50] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -54.545662-0.000708j
[2025-09-12 02:50:05] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -54.456986-0.000973j
[2025-09-12 02:50:20] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -54.515270+0.001357j
[2025-09-12 02:50:36] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -54.514670+0.001128j
[2025-09-12 02:50:51] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -54.427255-0.001992j
[2025-09-12 02:51:06] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -54.573331-0.003550j
[2025-09-12 02:51:21] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -54.585671-0.002649j
[2025-09-12 02:51:37] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -54.406569+0.000963j
[2025-09-12 02:51:52] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -54.577154+0.003247j
[2025-09-12 02:52:07] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -54.596841+0.002930j
[2025-09-12 02:52:22] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -54.548721+0.000893j
[2025-09-12 02:52:38] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -54.471983-0.000030j
[2025-09-12 02:52:53] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -54.378432+0.000534j
[2025-09-12 02:53:08] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -54.386174-0.002541j
[2025-09-12 02:53:23] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -54.590989+0.000460j
[2025-09-12 02:53:38] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -54.532150+0.000116j
[2025-09-12 02:53:53] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -54.663112+0.000792j
[2025-09-12 02:54:09] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -54.600431+0.002695j
[2025-09-12 02:54:24] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -54.499884-0.002280j
[2025-09-12 02:54:39] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -54.551516+0.001678j
[2025-09-12 02:54:54] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -54.596971-0.002058j
[2025-09-12 02:55:09] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -54.671495-0.001082j
[2025-09-12 02:55:25] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -54.489815-0.000547j
[2025-09-12 02:55:40] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -54.357323+0.001107j
[2025-09-12 02:55:55] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -54.345642-0.000568j
[2025-09-12 02:56:10] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -54.488753+0.000189j
[2025-09-12 02:56:25] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -54.496906-0.001079j
[2025-09-12 02:56:40] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -54.632505+0.000819j
[2025-09-12 02:56:56] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -54.477125+0.001458j
[2025-09-12 02:57:11] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -54.590948-0.001141j
[2025-09-12 02:57:26] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -54.565296+0.001712j
[2025-09-12 02:57:41] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -54.520417+0.001677j
[2025-09-12 02:57:56] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -54.539780-0.001621j
[2025-09-12 02:58:11] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -54.682933-0.000699j
[2025-09-12 02:58:27] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -54.478976-0.001439j
[2025-09-12 02:58:42] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -54.560840+0.003657j
[2025-09-12 02:58:57] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -54.635760-0.002167j
[2025-09-12 02:59:12] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -54.482875+0.001234j
[2025-09-12 02:59:27] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -54.529542+0.002645j
[2025-09-12 02:59:43] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -54.497544-0.000888j
[2025-09-12 02:59:58] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -54.482433-0.002871j
[2025-09-12 03:00:13] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -54.448505+0.002000j
[2025-09-12 03:00:28] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -54.315079+0.000314j
[2025-09-12 03:00:44] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -54.520499+0.003081j
[2025-09-12 03:00:59] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -54.437372+0.000122j
[2025-09-12 03:01:14] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -54.390639-0.000664j
[2025-09-12 03:01:29] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -54.503494+0.002754j
[2025-09-12 03:01:44] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -54.696134-0.002750j
[2025-09-12 03:02:00] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -54.625289+0.002626j
[2025-09-12 03:02:15] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -54.636809-0.002726j
[2025-09-12 03:02:30] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -54.499690+0.001129j
[2025-09-12 03:02:45] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -54.543015-0.000316j
[2025-09-12 03:03:00] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -54.426099-0.000485j
[2025-09-12 03:03:16] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -54.500071-0.002615j
[2025-09-12 03:03:31] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -54.554454-0.000329j
[2025-09-12 03:03:46] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -54.483621+0.001013j
[2025-09-12 03:04:01] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -54.361403+0.004328j
[2025-09-12 03:04:17] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -54.468025-0.000758j
[2025-09-12 03:04:32] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -54.396272-0.006514j
[2025-09-12 03:04:47] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -54.399415+0.003535j
[2025-09-12 03:05:02] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -54.498969+0.000147j
[2025-09-12 03:05:18] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -54.363602+0.001343j
[2025-09-12 03:05:33] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -54.419533-0.000689j
[2025-09-12 03:05:48] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -54.380476-0.000056j
[2025-09-12 03:06:03] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -54.449524-0.000725j
[2025-09-12 03:06:19] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -54.425388-0.003604j
[2025-09-12 03:06:34] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -54.486678-0.002439j
[2025-09-12 03:06:49] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -54.376506-0.002021j
[2025-09-12 03:07:04] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -54.421663+0.001088j
[2025-09-12 03:07:20] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -54.446602-0.000691j
[2025-09-12 03:07:35] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -54.380647+0.002122j
[2025-09-12 03:07:50] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -54.445200+0.000723j
[2025-09-12 03:08:05] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -54.474331+0.001392j
[2025-09-12 03:08:20] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -54.524158+0.000975j
[2025-09-12 03:08:36] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -54.429376-0.001653j
[2025-09-12 03:08:51] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -54.564022+0.003136j
[2025-09-12 03:09:06] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -54.483023+0.002237j
[2025-09-12 03:09:21] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -54.461107+0.000332j
[2025-09-12 03:09:36] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -54.477837-0.000165j
[2025-09-12 03:09:52] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -54.471329-0.001036j
[2025-09-12 03:10:07] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -54.533346-0.003442j
[2025-09-12 03:10:22] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -54.475941-0.001115j
[2025-09-12 03:10:37] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -54.360054-0.002963j
[2025-09-12 03:10:52] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -54.352398-0.003759j
[2025-09-12 03:11:08] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -54.314314+0.001960j
[2025-09-12 03:11:23] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -54.344581+0.001569j
[2025-09-12 03:11:38] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -54.470856+0.002575j
[2025-09-12 03:11:53] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -54.399739-0.000846j
[2025-09-12 03:12:09] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -54.420665+0.001060j
[2025-09-12 03:12:24] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -54.573865-0.000660j
[2025-09-12 03:12:24] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-12 03:12:39] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -54.567884-0.001558j
[2025-09-12 03:12:54] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -54.342866-0.001217j
[2025-09-12 03:13:09] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -54.454661+0.001064j
[2025-09-12 03:13:25] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -54.514711-0.004059j
[2025-09-12 03:13:40] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -54.559455-0.000797j
[2025-09-12 03:13:55] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -54.598847-0.000867j
[2025-09-12 03:14:10] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -54.608362-0.002212j
[2025-09-12 03:14:26] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -54.458833+0.001408j
[2025-09-12 03:14:41] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -54.463616+0.001775j
[2025-09-12 03:14:56] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -54.697929+0.001889j
[2025-09-12 03:15:11] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -54.639064-0.001560j
[2025-09-12 03:15:26] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -54.633480-0.001200j
[2025-09-12 03:15:41] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -54.566035+0.000921j
[2025-09-12 03:15:57] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -54.635576-0.003566j
[2025-09-12 03:16:12] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -54.512540-0.006628j
[2025-09-12 03:16:27] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -54.491185-0.001905j
[2025-09-12 03:16:42] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -54.420106-0.002697j
[2025-09-12 03:16:58] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -54.627466-0.001796j
[2025-09-12 03:17:13] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -54.470729-0.001115j
[2025-09-12 03:17:24] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -54.487395+0.001631j
[2025-09-12 03:17:34] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -54.645740-0.001131j
[2025-09-12 03:17:45] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -54.611937+0.001535j
[2025-09-12 03:17:55] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -54.634603-0.000337j
[2025-09-12 03:18:05] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -54.647422-0.002258j
[2025-09-12 03:18:15] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -54.612517+0.001306j
[2025-09-12 03:18:27] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -54.585074-0.000319j
[2025-09-12 03:18:42] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -54.597375+0.000772j
[2025-09-12 03:18:58] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -54.644140+0.002916j
[2025-09-12 03:19:13] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -54.563307-0.001020j
[2025-09-12 03:19:28] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -54.548487+0.000997j
[2025-09-12 03:19:44] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -54.532546-0.002337j
[2025-09-12 03:19:59] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -54.496966-0.000779j
[2025-09-12 03:20:14] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -54.419796-0.000607j
[2025-09-12 03:20:29] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -54.569271-0.000653j
[2025-09-12 03:20:45] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -54.502830+0.001189j
[2025-09-12 03:21:00] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -54.583555-0.000526j
[2025-09-12 03:21:15] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -54.444133+0.000259j
[2025-09-12 03:21:30] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -54.511362-0.002235j
[2025-09-12 03:21:46] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -54.562141+0.000761j
[2025-09-12 03:22:01] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -54.493870-0.002124j
[2025-09-12 03:22:16] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -54.538034+0.000923j
[2025-09-12 03:22:31] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -54.539683-0.001917j
[2025-09-12 03:22:47] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -54.565655+0.001866j
[2025-09-12 03:23:02] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -54.518284+0.002797j
[2025-09-12 03:23:17] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -54.573469+0.001652j
[2025-09-12 03:23:32] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -54.588115+0.002600j
[2025-09-12 03:23:48] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -54.482613-0.000442j
[2025-09-12 03:24:03] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -54.416502-0.003794j
[2025-09-12 03:24:18] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -54.496316-0.002542j
[2025-09-12 03:24:33] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -54.513043+0.005953j
[2025-09-12 03:24:49] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -54.459164-0.001576j
[2025-09-12 03:25:04] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -54.286406-0.000313j
[2025-09-12 03:25:19] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -54.348457-0.000238j
[2025-09-12 03:25:34] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -54.581578-0.002619j
[2025-09-12 03:25:50] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -54.649949-0.001350j
[2025-09-12 03:26:05] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -54.485514-0.000653j
[2025-09-12 03:26:20] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -54.628833+0.000241j
[2025-09-12 03:26:35] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -54.524659-0.000704j
[2025-09-12 03:26:51] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -54.509370+0.001711j
[2025-09-12 03:27:06] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -54.517377-0.001724j
[2025-09-12 03:27:21] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -54.526149+0.001744j
[2025-09-12 03:27:37] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -54.505037-0.002297j
[2025-09-12 03:27:52] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -54.355889+0.003201j
[2025-09-12 03:28:03] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -54.455498+0.000326j
[2025-09-12 03:28:16] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -54.531523-0.002927j
[2025-09-12 03:28:28] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -54.456708-0.001210j
[2025-09-12 03:28:41] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -54.432236+0.000397j
[2025-09-12 03:28:56] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -54.453870+0.003329j
[2025-09-12 03:29:11] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -54.486527-0.000701j
[2025-09-12 03:29:26] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -54.542205+0.000700j
[2025-09-12 03:29:41] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -54.527893+0.000914j
[2025-09-12 03:29:57] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -54.473670+0.004382j
[2025-09-12 03:30:12] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -54.567714-0.001043j
[2025-09-12 03:30:27] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -54.446447+0.001460j
[2025-09-12 03:30:42] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -54.327060-0.000263j
[2025-09-12 03:30:57] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -54.468760-0.000332j
[2025-09-12 03:31:12] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -54.440314+0.000360j
[2025-09-12 03:31:28] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -54.489370+0.003908j
[2025-09-12 03:31:43] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -54.444486+0.003081j
[2025-09-12 03:31:58] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -54.482380-0.003872j
[2025-09-12 03:32:13] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -54.354009-0.000803j
[2025-09-12 03:32:29] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -54.340972+0.002499j
[2025-09-12 03:32:44] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -54.400726-0.000975j
[2025-09-12 03:32:59] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -54.409271-0.001558j
[2025-09-12 03:33:14] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -54.533686-0.001905j
[2025-09-12 03:33:29] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -54.458930+0.000650j
[2025-09-12 03:33:45] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -54.399385+0.002259j
[2025-09-12 03:34:00] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -54.506160-0.000481j
[2025-09-12 03:34:15] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -54.438136+0.000174j
[2025-09-12 03:34:30] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -54.551923+0.000446j
[2025-09-12 03:34:45] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -54.421425+0.001151j
[2025-09-12 03:35:00] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -54.389859+0.001602j
[2025-09-12 03:35:16] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -54.479708+0.003415j
[2025-09-12 03:35:31] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -54.448684-0.001231j
[2025-09-12 03:35:46] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -54.507832+0.001155j
[2025-09-12 03:36:01] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -54.518443-0.001763j
[2025-09-12 03:36:17] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -54.463196-0.001431j
[2025-09-12 03:36:32] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -54.420632-0.001221j
[2025-09-12 03:36:47] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -54.378457-0.001556j
[2025-09-12 03:37:02] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -54.486707-0.002776j
[2025-09-12 03:37:18] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -54.585197-0.000658j
[2025-09-12 03:37:33] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -54.573786+0.004045j
[2025-09-12 03:37:48] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -54.595613-0.003175j
[2025-09-12 03:38:03] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -54.630594-0.001902j
[2025-09-12 03:38:18] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -54.601325-0.003859j
[2025-09-12 03:38:18] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-12 03:38:34] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -54.615132-0.003426j
[2025-09-12 03:38:49] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -54.495585+0.001398j
[2025-09-12 03:39:04] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -54.535120-0.003354j
[2025-09-12 03:39:19] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -54.450681-0.000206j
[2025-09-12 03:39:35] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -54.522973-0.001316j
[2025-09-12 03:39:50] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -54.533718-0.001160j
[2025-09-12 03:40:05] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -54.602393-0.000422j
[2025-09-12 03:40:20] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -54.597885-0.002894j
[2025-09-12 03:40:35] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -54.598061-0.002150j
[2025-09-12 03:40:51] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -54.573234-0.000325j
[2025-09-12 03:41:06] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -54.400792-0.002095j
[2025-09-12 03:41:21] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -54.515036-0.002702j
[2025-09-12 03:41:36] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -54.504743+0.001008j
[2025-09-12 03:41:52] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -54.438786+0.000692j
[2025-09-12 03:42:07] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -54.415436+0.000710j
[2025-09-12 03:42:22] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -54.503828-0.000194j
[2025-09-12 03:42:37] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -54.382354+0.001968j
[2025-09-12 03:42:53] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -54.329753-0.000912j
[2025-09-12 03:43:08] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -54.493942+0.000780j
[2025-09-12 03:43:23] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -54.511690-0.001021j
[2025-09-12 03:43:38] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -54.392645+0.001373j
[2025-09-12 03:43:54] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -54.532410+0.002169j
[2025-09-12 03:44:09] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -54.388110+0.001098j
[2025-09-12 03:44:24] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -54.545096-0.001045j
[2025-09-12 03:44:39] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -54.575681-0.001100j
[2025-09-12 03:44:55] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -54.606726+0.002797j
[2025-09-12 03:45:10] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -54.488983-0.003969j
[2025-09-12 03:45:25] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -54.558187-0.000843j
[2025-09-12 03:45:40] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -54.503647-0.003613j
[2025-09-12 03:45:55] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -54.428961-0.001492j
[2025-09-12 03:46:11] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -54.467665+0.001257j
[2025-09-12 03:46:26] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -54.550829+0.000113j
[2025-09-12 03:46:41] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -54.498746-0.002997j
[2025-09-12 03:46:56] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -54.405840+0.002255j
[2025-09-12 03:47:11] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -54.466277-0.000535j
[2025-09-12 03:47:27] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -54.439896+0.000962j
[2025-09-12 03:47:42] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -54.520572-0.003319j
[2025-09-12 03:47:57] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -54.473308-0.000149j
[2025-09-12 03:48:12] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -54.451438+0.005394j
[2025-09-12 03:48:27] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -54.534326+0.000122j
[2025-09-12 03:48:43] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -54.534499-0.001644j
[2025-09-12 03:48:58] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -54.546655-0.000254j
[2025-09-12 03:49:13] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -54.617728+0.001265j
[2025-09-12 03:49:29] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -54.538273+0.002256j
[2025-09-12 03:49:44] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -54.381755+0.002595j
[2025-09-12 03:49:59] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -54.478133+0.001812j
[2025-09-12 03:50:14] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -54.493056-0.005968j
[2025-09-12 03:50:29] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -54.510620+0.000549j
[2025-09-12 03:50:45] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -54.556682-0.001665j
[2025-09-12 03:51:00] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -54.491012+0.003140j
[2025-09-12 03:51:15] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -54.393939+0.000440j
[2025-09-12 03:51:30] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -54.347812-0.001150j
[2025-09-12 03:51:46] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -54.446767+0.002527j
[2025-09-12 03:52:01] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -54.491267+0.001477j
[2025-09-12 03:52:16] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -54.546129-0.000068j
[2025-09-12 03:52:31] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -54.516009+0.001003j
[2025-09-12 03:52:46] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -54.528886-0.002024j
[2025-09-12 03:53:02] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -54.340862-0.009253j
[2025-09-12 03:53:17] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -54.449933-0.000313j
[2025-09-12 03:53:32] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -54.398592-0.000684j
[2025-09-12 03:53:47] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -54.386545-0.004261j
[2025-09-12 03:54:03] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -54.377391-0.002609j
[2025-09-12 03:54:18] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -54.454400+0.002671j
[2025-09-12 03:54:33] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -54.508160-0.002588j
[2025-09-12 03:54:48] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -54.495747+0.004233j
[2025-09-12 03:55:03] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -54.526175+0.004519j
[2025-09-12 03:55:19] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -54.468459+0.003519j
[2025-09-12 03:55:34] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -54.446120-0.001466j
[2025-09-12 03:55:49] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -54.478220-0.003510j
[2025-09-12 03:56:04] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -54.365869+0.001575j
[2025-09-12 03:56:20] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -54.372748+0.000695j
[2025-09-12 03:56:35] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -54.363844-0.001642j
[2025-09-12 03:56:50] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -54.434600+0.000826j
[2025-09-12 03:57:05] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -54.394824-0.001013j
[2025-09-12 03:57:21] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -54.414859-0.001642j
[2025-09-12 03:57:36] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -54.477565+0.000200j
[2025-09-12 03:57:51] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -54.460864-0.002255j
[2025-09-12 03:58:06] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -54.416039+0.002345j
[2025-09-12 03:58:21] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -54.546955-0.001223j
[2025-09-12 03:58:37] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -54.554211+0.002349j
[2025-09-12 03:58:52] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -54.519451-0.002025j
[2025-09-12 03:59:07] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -54.556737+0.002618j
[2025-09-12 03:59:22] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -54.452340+0.000803j
[2025-09-12 03:59:38] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -54.365577+0.001825j
[2025-09-12 03:59:53] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -54.407746-0.002070j
[2025-09-12 04:00:08] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -54.553898-0.001413j
[2025-09-12 04:00:23] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -54.472747+0.000588j
[2025-09-12 04:00:39] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -54.452153+0.000546j
[2025-09-12 04:00:54] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -54.492154-0.002002j
[2025-09-12 04:01:09] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -54.432005-0.003390j
[2025-09-12 04:01:24] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -54.511311-0.001086j
[2025-09-12 04:01:40] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -54.545168-0.003718j
[2025-09-12 04:01:52] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -54.482654-0.004646j
[2025-09-12 04:02:02] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -54.523216-0.000796j
[2025-09-12 04:02:13] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -54.636446-0.001591j
[2025-09-12 04:02:23] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -54.528847+0.004395j
[2025-09-12 04:02:33] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -54.673052-0.001059j
[2025-09-12 04:02:43] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -54.551456+0.002758j
[2025-09-12 04:02:54] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -54.456218-0.000642j
[2025-09-12 04:03:09] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -54.523623+0.002300j
[2025-09-12 04:03:25] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -54.545074-0.000736j
[2025-09-12 04:03:40] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -54.475583-0.000025j
[2025-09-12 04:03:55] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -54.513137-0.000280j
[2025-09-12 04:04:10] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -54.541585+0.000359j
[2025-09-12 04:04:26] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -54.370755-0.000172j
[2025-09-12 04:04:26] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-12 04:04:41] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -54.408894+0.003036j
[2025-09-12 04:04:56] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -54.413310+0.002024j
[2025-09-12 04:05:11] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -54.468471-0.000044j
[2025-09-12 04:05:27] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -54.466286-0.000358j
[2025-09-12 04:05:42] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -54.614335+0.002607j
[2025-09-12 04:05:57] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -54.539867+0.000085j
[2025-09-12 04:06:12] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -54.520002+0.002712j
[2025-09-12 04:06:28] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -54.282594-0.001194j
[2025-09-12 04:06:43] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -54.425373+0.000245j
[2025-09-12 04:06:58] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -54.481063-0.002304j
[2025-09-12 04:07:14] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -54.487759-0.002264j
[2025-09-12 04:07:29] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -54.535896-0.001041j
[2025-09-12 04:07:44] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -54.503756+0.002720j
[2025-09-12 04:07:59] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -54.592197+0.000874j
[2025-09-12 04:08:15] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -54.630442-0.001086j
[2025-09-12 04:08:30] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -54.681834-0.000995j
[2025-09-12 04:08:45] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -54.492346+0.002010j
[2025-09-12 04:09:01] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -54.505761+0.001936j
[2025-09-12 04:09:16] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -54.399676-0.001002j
[2025-09-12 04:09:31] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -54.459162+0.002900j
[2025-09-12 04:09:46] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -54.523979+0.002898j
[2025-09-12 04:10:02] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -54.583234-0.001506j
[2025-09-12 04:10:17] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -54.498822+0.001651j
[2025-09-12 04:10:32] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -54.618455-0.000607j
[2025-09-12 04:10:47] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -54.473158+0.000409j
[2025-09-12 04:11:03] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -54.488634+0.002118j
[2025-09-12 04:11:18] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -54.516911-0.001501j
[2025-09-12 04:11:33] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -54.481827-0.004296j
[2025-09-12 04:11:48] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -54.545273+0.000994j
[2025-09-12 04:12:04] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -54.444866-0.000992j
[2025-09-12 04:12:19] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -54.500988-0.002598j
[2025-09-12 04:12:29] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -54.481960-0.000558j
[2025-09-12 04:12:42] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -54.467224-0.000954j
[2025-09-12 04:12:54] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -54.523504+0.002009j
[2025-09-12 04:13:07] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -54.463949-0.000735j
[2025-09-12 04:13:22] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -54.570455+0.000358j
[2025-09-12 04:13:37] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -54.407990-0.001050j
[2025-09-12 04:13:52] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -54.525908-0.002149j
[2025-09-12 04:14:08] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -54.492440+0.002756j
[2025-09-12 04:14:23] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -54.346805+0.002321j
[2025-09-12 04:14:38] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -54.434080-0.000952j
[2025-09-12 04:14:53] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -54.401033+0.000010j
[2025-09-12 04:15:08] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -54.448959+0.001331j
[2025-09-12 04:15:23] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -54.544331-0.000386j
[2025-09-12 04:15:39] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -54.422974-0.000193j
[2025-09-12 04:15:54] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -54.501261+0.002529j
[2025-09-12 04:16:09] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -54.493598-0.001503j
[2025-09-12 04:16:24] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -54.374750+0.000975j
[2025-09-12 04:16:39] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -54.391325+0.002614j
[2025-09-12 04:16:55] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -54.413813-0.000796j
[2025-09-12 04:17:10] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -54.385081-0.001322j
[2025-09-12 04:17:25] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -54.475934+0.003135j
[2025-09-12 04:17:40] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -54.450795+0.000643j
[2025-09-12 04:17:55] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -54.453696-0.001638j
[2025-09-12 04:18:10] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -54.460022-0.001426j
[2025-09-12 04:18:26] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -54.572684-0.002539j
[2025-09-12 04:18:41] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -54.482002+0.001119j
[2025-09-12 04:18:56] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -54.500374+0.004834j
[2025-09-12 04:19:11] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -54.478399+0.001144j
[2025-09-12 04:19:26] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -54.407933-0.001213j
[2025-09-12 04:19:41] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -54.430442-0.000381j
[2025-09-12 04:19:57] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -54.498549+0.001051j
[2025-09-12 04:20:12] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -54.523740+0.001906j
[2025-09-12 04:20:27] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -54.457030-0.001712j
[2025-09-12 04:20:42] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -54.507848+0.004001j
[2025-09-12 04:20:57] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -54.524363+0.000660j
[2025-09-12 04:21:13] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -54.478107+0.000000j
[2025-09-12 04:21:28] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -54.410246+0.001080j
[2025-09-12 04:21:43] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -54.361679+0.002890j
[2025-09-12 04:21:58] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -54.369082-0.003935j
[2025-09-12 04:22:13] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -54.567856-0.000091j
[2025-09-12 04:22:29] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -54.490923+0.001314j
[2025-09-12 04:22:44] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -54.542633+0.002489j
[2025-09-12 04:22:59] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -54.611061-0.002423j
[2025-09-12 04:23:14] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -54.601959-0.002529j
[2025-09-12 04:23:29] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -54.666203-0.002558j
[2025-09-12 04:23:45] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -54.438716+0.001672j
[2025-09-12 04:24:00] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -54.425610-0.001637j
[2025-09-12 04:24:15] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -54.330181+0.003978j
[2025-09-12 04:24:30] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -54.466235-0.006490j
[2025-09-12 04:24:45] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -54.304667-0.003956j
[2025-09-12 04:25:01] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -54.405350+0.001542j
[2025-09-12 04:25:16] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -54.479430+0.001032j
[2025-09-12 04:25:31] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -54.420209+0.000447j
[2025-09-12 04:25:46] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -54.468232-0.003061j
[2025-09-12 04:26:02] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -54.448373-0.002356j
[2025-09-12 04:26:17] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -54.384823+0.000603j
[2025-09-12 04:26:32] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -54.515157-0.001284j
[2025-09-12 04:26:47] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -54.605054+0.000458j
[2025-09-12 04:27:02] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -54.647358+0.001764j
[2025-09-12 04:27:18] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -54.530638+0.000125j
[2025-09-12 04:27:33] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -54.458428-0.002425j
[2025-09-12 04:27:48] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -54.370530-0.000870j
[2025-09-12 04:28:03] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -54.290477+0.000567j
[2025-09-12 04:28:18] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -54.308605+0.000501j
[2025-09-12 04:28:34] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -54.422745-0.001506j
[2025-09-12 04:28:49] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -54.474369+0.003390j
[2025-09-12 04:29:04] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -54.475395+0.000859j
[2025-09-12 04:29:19] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -54.373331+0.001209j
[2025-09-12 04:29:34] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -54.416804+0.000082j
[2025-09-12 04:29:50] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -54.481632-0.000390j
[2025-09-12 04:30:05] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -54.530383-0.000158j
[2025-09-12 04:30:20] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -54.574559+0.001518j
[2025-09-12 04:30:35] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -54.688882+0.000765j
[2025-09-12 04:30:51] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -54.472151-0.001479j
[2025-09-12 04:30:51] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-12 04:31:06] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -54.408757+0.000938j
[2025-09-12 04:31:21] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -54.324946-0.003886j
[2025-09-12 04:31:36] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -54.527367+0.000343j
[2025-09-12 04:31:51] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -54.513949+0.002335j
[2025-09-12 04:32:07] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -54.545122+0.001273j
[2025-09-12 04:32:22] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -54.510842+0.000156j
[2025-09-12 04:32:37] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -54.304431+0.004079j
[2025-09-12 04:32:52] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -54.442666-0.001275j
[2025-09-12 04:33:07] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -54.371515+0.001152j
[2025-09-12 04:33:23] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -54.412280+0.001039j
[2025-09-12 04:33:38] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -54.561319+0.004076j
[2025-09-12 04:33:53] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -54.578985-0.000651j
[2025-09-12 04:34:08] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -54.438413+0.003700j
[2025-09-12 04:34:23] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -54.559004+0.000258j
[2025-09-12 04:34:39] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -54.455269-0.001777j
[2025-09-12 04:34:54] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -54.397601+0.000835j
[2025-09-12 04:35:09] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -54.498447+0.001034j
[2025-09-12 04:35:24] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -54.433348+0.001687j
[2025-09-12 04:35:39] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -54.406980+0.000844j
[2025-09-12 04:35:55] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -54.418852+0.000177j
[2025-09-12 04:36:10] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -54.448718+0.000197j
[2025-09-12 04:36:25] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -54.523507-0.001485j
[2025-09-12 04:36:40] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -54.523110-0.002082j
[2025-09-12 04:36:56] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -54.619214-0.000332j
[2025-09-12 04:37:11] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -54.318147+0.002593j
[2025-09-12 04:37:26] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -54.415007+0.001266j
[2025-09-12 04:37:41] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -54.373570-0.001333j
[2025-09-12 04:37:56] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -54.473148-0.002485j
[2025-09-12 04:38:12] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -54.462209-0.000149j
[2025-09-12 04:38:27] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -54.483835+0.000527j
[2025-09-12 04:38:42] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -54.448937+0.001287j
[2025-09-12 04:38:58] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -54.513841+0.000846j
[2025-09-12 04:39:13] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -54.544988+0.000088j
[2025-09-12 04:39:28] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -54.519676-0.000531j
[2025-09-12 04:39:43] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -54.576294-0.002756j
[2025-09-12 04:39:58] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -54.546526+0.000459j
[2025-09-12 04:40:14] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -54.550176-0.000713j
[2025-09-12 04:40:29] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -54.575787-0.000863j
[2025-09-12 04:40:44] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -54.636870+0.000618j
[2025-09-12 04:40:59] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -54.533351+0.000653j
[2025-09-12 04:41:14] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -54.473805+0.000553j
[2025-09-12 04:41:30] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -54.574091+0.000820j
[2025-09-12 04:41:45] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -54.584160+0.004413j
[2025-09-12 04:42:00] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -54.511851+0.003364j
[2025-09-12 04:42:15] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -54.578159-0.003264j
[2025-09-12 04:42:30] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -54.510129+0.002326j
[2025-09-12 04:42:46] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -54.418303+0.001726j
[2025-09-12 04:43:01] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -54.417695-0.000162j
[2025-09-12 04:43:16] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -54.438963-0.003320j
[2025-09-12 04:43:31] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -54.467980+0.002471j
[2025-09-12 04:43:47] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -54.603572-0.004974j
[2025-09-12 04:44:02] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -54.492483-0.002534j
[2025-09-12 04:44:17] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -54.374194+0.003019j
[2025-09-12 04:44:32] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -54.431207+0.000915j
[2025-09-12 04:44:48] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -54.375701+0.000765j
[2025-09-12 04:45:03] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -54.404353-0.000171j
[2025-09-12 04:45:18] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -54.399081+0.000240j
[2025-09-12 04:45:33] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -54.400178+0.000840j
[2025-09-12 04:45:49] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -54.362339+0.002565j
[2025-09-12 04:46:04] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -54.526157-0.000784j
[2025-09-12 04:46:19] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -54.562806+0.000690j
[2025-09-12 04:46:30] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -54.573952+0.000159j
[2025-09-12 04:46:40] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -54.521099-0.001785j
[2025-09-12 04:46:50] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -54.441501-0.000699j
[2025-09-12 04:47:00] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -54.499789-0.001522j
[2025-09-12 04:47:10] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -54.525133+0.001045j
[2025-09-12 04:47:20] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -54.462130-0.001411j
[2025-09-12 04:47:31] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -54.533061+0.000505j
[2025-09-12 04:47:45] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -54.657982-0.000424j
[2025-09-12 04:48:00] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -54.550158-0.002193j
[2025-09-12 04:48:15] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -54.635991-0.001254j
[2025-09-12 04:48:31] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -54.498478-0.002315j
[2025-09-12 04:48:46] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -54.521111-0.000651j
[2025-09-12 04:49:01] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -54.609442-0.000601j
[2025-09-12 04:49:16] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -54.674236+0.000988j
[2025-09-12 04:49:32] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -54.476641+0.001605j
[2025-09-12 04:49:47] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -54.374734+0.000787j
[2025-09-12 04:50:02] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -54.432524-0.002540j
[2025-09-12 04:50:17] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -54.425280-0.001063j
[2025-09-12 04:50:33] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -54.428617-0.000720j
[2025-09-12 04:50:48] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -54.512269+0.003467j
[2025-09-12 04:51:03] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -54.557914+0.002122j
[2025-09-12 04:51:18] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -54.557442-0.001970j
[2025-09-12 04:51:34] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -54.643530+0.001660j
[2025-09-12 04:51:49] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -54.534654-0.000020j
[2025-09-12 04:52:04] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -54.497805+0.002470j
[2025-09-12 04:52:20] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -54.504872+0.002729j
[2025-09-12 04:52:35] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -54.549857+0.000530j
[2025-09-12 04:52:50] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -54.581690+0.003675j
[2025-09-12 04:53:05] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -54.488761+0.003609j
[2025-09-12 04:53:21] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -54.486032+0.001221j
[2025-09-12 04:53:36] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -54.510504+0.000506j
[2025-09-12 04:53:51] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -54.448279+0.000269j
[2025-09-12 04:54:06] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -54.555608+0.000416j
[2025-09-12 04:54:21] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -54.490107-0.001035j
[2025-09-12 04:54:37] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -54.522711+0.001503j
[2025-09-12 04:54:52] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -54.483016+0.000868j
[2025-09-12 04:55:07] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -54.527764+0.001033j
[2025-09-12 04:55:23] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -54.539066+0.000056j
[2025-09-12 04:55:38] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -54.521270-0.000482j
[2025-09-12 04:55:53] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -54.603513+0.000022j
[2025-09-12 04:56:08] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -54.606569+0.000169j
[2025-09-12 04:56:24] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -54.679864-0.000251j
[2025-09-12 04:56:39] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -54.648893-0.003682j
[2025-09-12 04:56:54] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -54.513163-0.001287j
[2025-09-12 04:56:54] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-12 04:56:54] ✅ Training completed | Restarts: 2
[2025-09-12 04:56:54] ============================================================
[2025-09-12 04:56:54] Training completed | Runtime: 15878.7s
[2025-09-12 04:57:00] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-12 04:57:00] ============================================================
