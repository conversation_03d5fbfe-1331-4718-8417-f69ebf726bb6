[2025-09-12 20:21:02] 使用checkpoint文件: results/L=4/J2=0.03/J1=0.01/training/checkpoints/checkpoint_iter_000630.pkl
[2025-09-12 20:21:19] ✓ 从checkpoint加载参数: 630
[2025-09-12 20:21:19]   - 能量: -51.755386+0.003287j ± 0.082452
[2025-09-12 20:21:19] ================================================================================
[2025-09-12 20:21:19] 加载量子态: L=4, J2=0.03, J1=0.01, checkpoint=checkpoint_iter_000630
[2025-09-12 20:21:19] 使用采样数目: 1048576
[2025-09-12 20:21:19] 设置样本数为: 1048576
[2025-09-12 20:21:19] 开始生成共享样本集...
[2025-09-12 20:24:20] 样本生成完成,耗时: 180.853 秒
[2025-09-12 20:24:20] ================================================================================
[2025-09-12 20:24:20] 开始计算自旋结构因子...
[2025-09-12 20:24:20] 初始化操作符缓存...
[2025-09-12 20:24:20] 预构建所有自旋相关操作符...
[2025-09-12 20:24:20] 开始计算自旋相关函数...
[2025-09-12 20:24:34] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 14.324s
[2025-09-12 20:24:52] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 17.782s
[2025-09-12 20:25:02] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.381s
[2025-09-12 20:25:11] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.354s
[2025-09-12 20:25:20] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.362s
[2025-09-12 20:25:30] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.379s
[2025-09-12 20:25:39] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.380s
[2025-09-12 20:25:49] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.391s
[2025-09-12 20:25:58] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.376s
[2025-09-12 20:26:07] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.377s
[2025-09-12 20:26:17] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.385s
[2025-09-12 20:26:26] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.400s
[2025-09-12 20:26:35] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.386s
[2025-09-12 20:26:45] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.393s
[2025-09-12 20:26:54] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.378s
[2025-09-12 20:27:04] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.303s
[2025-09-12 20:27:13] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.383s
[2025-09-12 20:27:22] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.387s
[2025-09-12 20:27:32] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.386s
[2025-09-12 20:27:41] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.383s
[2025-09-12 20:27:51] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.371s
[2025-09-12 20:28:00] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.394s
[2025-09-12 20:28:09] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.386s
[2025-09-12 20:28:19] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.383s
[2025-09-12 20:28:28] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.359s
[2025-09-12 20:28:37] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.438s
[2025-09-12 20:28:47] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.297s
[2025-09-12 20:28:56] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.395s
[2025-09-12 20:29:06] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.368s
[2025-09-12 20:29:15] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.382s
[2025-09-12 20:29:24] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.356s
[2025-09-12 20:29:34] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.398s
[2025-09-12 20:29:43] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.379s
[2025-09-12 20:29:53] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.392s
[2025-09-12 20:30:02] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.369s
[2025-09-12 20:30:11] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.389s
[2025-09-12 20:30:21] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.437s
[2025-09-12 20:30:30] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.278s
[2025-09-12 20:30:39] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.384s
[2025-09-12 20:30:49] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.392s
[2025-09-12 20:30:58] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.387s
[2025-09-12 20:31:08] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.363s
[2025-09-12 20:31:17] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.390s
[2025-09-12 20:31:26] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.385s
[2025-09-12 20:31:36] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.388s
[2025-09-12 20:31:45] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.369s
[2025-09-12 20:31:55] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.389s
[2025-09-12 20:32:04] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.358s
[2025-09-12 20:32:13] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.383s
[2025-09-12 20:32:23] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.391s
[2025-09-12 20:32:32] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.374s
[2025-09-12 20:32:42] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.397s
[2025-09-12 20:32:51] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.365s
[2025-09-12 20:33:00] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.392s
[2025-09-12 20:33:10] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.370s
[2025-09-12 20:33:19] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.400s
[2025-09-12 20:33:28] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.374s
[2025-09-12 20:33:38] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.335s
[2025-09-12 20:33:47] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.363s
[2025-09-12 20:33:57] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.395s
[2025-09-12 20:34:06] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.377s
[2025-09-12 20:34:15] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.376s
[2025-09-12 20:34:25] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.386s
[2025-09-12 20:34:34] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.374s
[2025-09-12 20:34:34] 自旋相关函数计算完成,总耗时 614.05 秒
[2025-09-12 20:34:35] 计算傅里叶变换...
[2025-09-12 20:34:37] 自旋结构因子计算完成
[2025-09-12 20:34:38] 自旋相关函数平均误差: 0.000669
