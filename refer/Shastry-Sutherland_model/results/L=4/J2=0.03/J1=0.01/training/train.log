[2025-09-12 04:57:28] ✓ 从checkpoint恢复: results/L=4/J2=0.03/J1=0.02/training/checkpoints/final_GCNN.pkl
[2025-09-12 04:57:28]   - 迭代次数: final
[2025-09-12 04:57:28]   - 能量: -52.118717-0.001571j ± 0.085651
[2025-09-12 04:57:28]   - 时间戳: 2025-09-12T04:57:11.660741+08:00
[2025-09-12 04:57:53] ✓ 变分状态参数已从checkpoint恢复
[2025-09-12 04:57:53] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-12 04:57:53] ==================================================
[2025-09-12 04:57:53] GCNN for Shastry-Sutherland Model
[2025-09-12 04:57:53] ==================================================
[2025-09-12 04:57:53] System parameters:
[2025-09-12 04:57:53]   - System size: L=4, N=64
[2025-09-12 04:57:54]   - System parameters: J1=0.01, J2=0.03, Q=0.97
[2025-09-12 04:57:54] --------------------------------------------------
[2025-09-12 04:57:54] Model parameters:
[2025-09-12 04:57:54]   - Number of layers = 4
[2025-09-12 04:57:54]   - Number of features = 4
[2025-09-12 04:57:54]   - Total parameters = 12572
[2025-09-12 04:57:54] --------------------------------------------------
[2025-09-12 04:57:54] Training parameters:
[2025-09-12 04:57:54]   - Learning rate: 0.015
[2025-09-12 04:57:54]   - Total iterations: 1050
[2025-09-12 04:57:54]   - Annealing cycles: 3
[2025-09-12 04:57:54]   - Initial period: 150
[2025-09-12 04:57:54]   - Period multiplier: 2.0
[2025-09-12 04:57:54]   - Temperature range: 0.0-1.0
[2025-09-12 04:57:54]   - Samples: 4096
[2025-09-12 04:57:54]   - Discarded samples: 0
[2025-09-12 04:57:54]   - Chunk size: 2048
[2025-09-12 04:57:54]   - Diagonal shift: 0.2
[2025-09-12 04:57:54]   - Gradient clipping: 1.0
[2025-09-12 04:57:54]   - Checkpoint enabled: interval=105
[2025-09-12 04:57:54]   - Checkpoint directory: results/L=4/J2=0.03/J1=0.01/training/checkpoints
[2025-09-12 04:57:54] --------------------------------------------------
[2025-09-12 04:57:54] Device status:
[2025-09-12 04:57:54]   - Devices model: NVIDIA H200 NVL
[2025-09-12 04:57:54]   - Number of devices: 1
[2025-09-12 04:57:54]   - Sharding: True
[2025-09-12 04:57:54] ============================================================
[2025-09-12 04:59:49] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -51.745810+0.001474j
[2025-09-12 05:00:56] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -51.681772-0.001280j
[2025-09-12 05:01:11] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -51.732906+0.001776j
[2025-09-12 05:01:26] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -51.585496+0.001044j
[2025-09-12 05:01:42] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -51.532498+0.004628j
[2025-09-12 05:01:57] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -51.667411+0.003555j
[2025-09-12 05:02:12] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -51.562870-0.000740j
[2025-09-12 05:02:27] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -51.564540+0.001840j
[2025-09-12 05:02:43] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -51.645469+0.002050j
[2025-09-12 05:02:58] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -51.631831-0.000561j
[2025-09-12 05:03:13] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -51.564959-0.000054j
[2025-09-12 05:03:28] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -51.630557+0.003476j
[2025-09-12 05:03:43] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -51.744041-0.005134j
[2025-09-12 05:03:59] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -51.734793-0.000294j
[2025-09-12 05:04:14] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -51.789040-0.000597j
[2025-09-12 05:04:29] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -51.655555-0.001154j
[2025-09-12 05:04:44] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -51.621624-0.001263j
[2025-09-12 05:05:00] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -51.755678+0.000772j
[2025-09-12 05:05:15] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -51.673628-0.001188j
[2025-09-12 05:05:30] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -51.813072+0.001061j
[2025-09-12 05:05:45] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -51.736808+0.003633j
[2025-09-12 05:06:01] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -51.743365+0.001936j
[2025-09-12 05:06:16] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -51.830932-0.000999j
[2025-09-12 05:06:31] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -51.822592-0.003851j
[2025-09-12 05:06:46] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -51.731202-0.001590j
[2025-09-12 05:07:02] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -51.892000-0.000492j
[2025-09-12 05:07:17] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -51.776542-0.001547j
[2025-09-12 05:07:32] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -51.637436+0.004297j
[2025-09-12 05:07:47] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -51.787802-0.000774j
[2025-09-12 05:08:03] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -51.808499-0.002218j
[2025-09-12 05:08:18] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -51.687057-0.002108j
[2025-09-12 05:08:33] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -51.713033+0.002348j
[2025-09-12 05:08:48] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -51.793004-0.000200j
[2025-09-12 05:09:04] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -51.837117-0.002486j
[2025-09-12 05:09:19] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -51.703026+0.001128j
[2025-09-12 05:09:34] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -51.712221-0.000727j
[2025-09-12 05:09:49] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -51.808192-0.001120j
[2025-09-12 05:10:05] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -51.703712+0.001420j
[2025-09-12 05:10:20] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -51.757044+0.001915j
[2025-09-12 05:10:35] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -51.760531+0.001462j
[2025-09-12 05:10:50] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -51.761144+0.000733j
[2025-09-12 05:11:05] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -51.703404+0.000241j
[2025-09-12 05:11:21] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -51.797313+0.001089j
[2025-09-12 05:11:36] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -51.603537-0.000205j
[2025-09-12 05:11:51] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -51.701146-0.000415j
[2025-09-12 05:12:06] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -51.758371-0.001836j
[2025-09-12 05:12:22] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -51.757173+0.003469j
[2025-09-12 05:12:37] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -51.762206+0.003442j
[2025-09-12 05:12:52] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -51.720007-0.006916j
[2025-09-12 05:13:07] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -51.758045+0.000707j
[2025-09-12 05:13:23] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -51.806686-0.001498j
[2025-09-12 05:13:38] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -51.808836-0.001242j
[2025-09-12 05:13:53] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -51.867475-0.000497j
[2025-09-12 05:14:08] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -51.661528-0.000043j
[2025-09-12 05:14:23] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -51.702824+0.002701j
[2025-09-12 05:14:39] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -51.644693-0.000591j
[2025-09-12 05:14:54] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -51.592552-0.000812j
[2025-09-12 05:15:09] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -51.666492-0.001051j
[2025-09-12 05:15:24] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -51.644857+0.000244j
[2025-09-12 05:15:40] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -51.832337-0.001911j
[2025-09-12 05:15:55] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -51.878151-0.001280j
[2025-09-12 05:16:10] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -51.862729+0.002120j
[2025-09-12 05:16:25] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -51.878376-0.000978j
[2025-09-12 05:16:41] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -51.887114+0.001344j
[2025-09-12 05:16:56] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -51.859046+0.002672j
[2025-09-12 05:17:11] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -51.801740+0.001098j
[2025-09-12 05:17:26] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -51.672366+0.002308j
[2025-09-12 05:17:42] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -51.702041-0.000425j
[2025-09-12 05:17:57] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -51.762839+0.002990j
[2025-09-12 05:18:12] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -51.703153-0.002029j
[2025-09-12 05:18:27] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -51.751327+0.001730j
[2025-09-12 05:18:42] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -51.688460+0.000786j
[2025-09-12 05:18:58] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -51.820558+0.001439j
[2025-09-12 05:19:13] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -51.774156-0.001506j
[2025-09-12 05:19:28] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -51.784065+0.002414j
[2025-09-12 05:19:43] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -51.849789+0.000637j
[2025-09-12 05:19:59] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -51.769543-0.000982j
[2025-09-12 05:20:14] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -51.747395+0.000316j
[2025-09-12 05:20:29] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -51.696503+0.000354j
[2025-09-12 05:20:44] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -51.641314-0.001457j
[2025-09-12 05:21:00] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -51.735056+0.001260j
[2025-09-12 05:21:15] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -51.753628-0.001305j
[2025-09-12 05:21:30] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -51.904908-0.003286j
[2025-09-12 05:21:45] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -51.759028-0.004317j
[2025-09-12 05:22:01] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -51.763257-0.000220j
[2025-09-12 05:22:16] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -51.810899-0.000901j
[2025-09-12 05:22:31] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -51.777515+0.003748j
[2025-09-12 05:22:46] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -51.908695+0.000702j
[2025-09-12 05:23:02] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -51.852052-0.000566j
[2025-09-12 05:23:17] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -51.860329+0.002916j
[2025-09-12 05:23:32] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -51.698782-0.000537j
[2025-09-12 05:23:47] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -51.681030+0.002741j
[2025-09-12 05:24:03] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -51.604433-0.001622j
[2025-09-12 05:24:18] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -51.674005+0.000428j
[2025-09-12 05:24:33] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -51.783982-0.000317j
[2025-09-12 05:24:45] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -51.702170+0.002008j
[2025-09-12 05:24:55] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -51.753110+0.000305j
[2025-09-12 05:25:06] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -51.728988+0.001488j
[2025-09-12 05:25:16] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -51.906559+0.000236j
[2025-09-12 05:25:26] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -51.844202+0.001001j
[2025-09-12 05:25:36] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -51.742668+0.000905j
[2025-09-12 05:25:48] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -51.645839+0.000332j
[2025-09-12 05:26:03] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -51.525535+0.001026j
[2025-09-12 05:26:18] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -51.622694+0.001534j
[2025-09-12 05:26:34] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -51.682907+0.000473j
[2025-09-12 05:26:34] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-12 05:26:49] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -51.669676-0.000181j
[2025-09-12 05:27:04] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -51.707055+0.004369j
[2025-09-12 05:27:19] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -51.853378+0.000830j
[2025-09-12 05:27:35] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -51.834282+0.000302j
[2025-09-12 05:27:50] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -51.869171-0.000314j
[2025-09-12 05:28:05] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -51.812986-0.001001j
[2025-09-12 05:28:21] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -51.732736+0.002225j
[2025-09-12 05:28:36] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -51.811524+0.001324j
[2025-09-12 05:28:51] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -51.784338+0.000699j
[2025-09-12 05:29:06] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -51.638931-0.000114j
[2025-09-12 05:29:22] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -51.631964-0.001453j
[2025-09-12 05:29:37] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -51.707082+0.002650j
[2025-09-12 05:29:52] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -51.693488-0.000595j
[2025-09-12 05:30:08] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -51.721577+0.001288j
[2025-09-12 05:30:23] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -51.650367+0.003140j
[2025-09-12 05:30:38] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -51.507560-0.001425j
[2025-09-12 05:30:53] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -51.621426-0.003631j
[2025-09-12 05:31:08] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -51.575593+0.002216j
[2025-09-12 05:31:24] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -51.742514-0.000389j
[2025-09-12 05:31:39] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -51.767748-0.002848j
[2025-09-12 05:31:54] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -51.759241+0.000886j
[2025-09-12 05:32:10] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -51.756921-0.000782j
[2025-09-12 05:32:25] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -51.719727-0.000025j
[2025-09-12 05:32:40] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -51.759053-0.002865j
[2025-09-12 05:32:55] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -51.871028+0.000627j
[2025-09-12 05:33:11] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -51.786270-0.001950j
[2025-09-12 05:33:26] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -51.743519+0.000944j
[2025-09-12 05:33:41] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -51.780762+0.002890j
[2025-09-12 05:33:56] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -51.767880-0.004587j
[2025-09-12 05:34:12] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -51.760222+0.000157j
[2025-09-12 05:34:27] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -51.803326-0.001623j
[2025-09-12 05:34:42] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -51.845147-0.001275j
[2025-09-12 05:34:58] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -51.810705-0.000165j
[2025-09-12 05:35:13] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -51.949838+0.000934j
[2025-09-12 05:35:24] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -51.830956+0.002144j
[2025-09-12 05:35:37] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -51.778275-0.000195j
[2025-09-12 05:35:48] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -51.766625+0.000801j
[2025-09-12 05:36:01] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -51.693998+0.002493j
[2025-09-12 05:36:16] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -51.672169+0.002095j
[2025-09-12 05:36:31] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -51.764721-0.000089j
[2025-09-12 05:36:46] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -51.741217-0.001517j
[2025-09-12 05:37:02] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -51.840271+0.001159j
[2025-09-12 05:37:17] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -51.839168-0.000223j
[2025-09-12 05:37:32] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -51.814216-0.000140j
[2025-09-12 05:37:47] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -51.811947-0.002483j
[2025-09-12 05:37:47] RESTART #1 | Period: 300
[2025-09-12 05:38:03] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -51.691481+0.002255j
[2025-09-12 05:38:18] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -51.688106+0.001131j
[2025-09-12 05:38:33] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -51.726387-0.000578j
[2025-09-12 05:38:48] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -51.841209+0.003657j
[2025-09-12 05:39:04] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -51.853620+0.000348j
[2025-09-12 05:39:19] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -51.670916+0.003487j
[2025-09-12 05:39:34] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -51.691694+0.003296j
[2025-09-12 05:39:49] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -51.719782+0.007291j
[2025-09-12 05:40:05] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -51.707085+0.001204j
[2025-09-12 05:40:20] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -51.686580+0.000919j
[2025-09-12 05:40:35] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -51.828251-0.001904j
[2025-09-12 05:40:51] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -51.742054-0.001404j
[2025-09-12 05:41:06] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -51.927315-0.002859j
[2025-09-12 05:41:21] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -51.719899+0.000696j
[2025-09-12 05:41:36] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -51.657867+0.001870j
[2025-09-12 05:41:52] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -51.579514-0.001178j
[2025-09-12 05:42:07] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -51.636659-0.003250j
[2025-09-12 05:42:22] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -51.830245-0.000336j
[2025-09-12 05:42:37] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -51.714554+0.003820j
[2025-09-12 05:42:53] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -51.704921+0.000476j
[2025-09-12 05:43:08] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -51.622358-0.003390j
[2025-09-12 05:43:23] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -51.690732-0.000534j
[2025-09-12 05:43:38] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -51.690844+0.003399j
[2025-09-12 05:43:54] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -51.523808+0.001295j
[2025-09-12 05:44:09] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -51.598978-0.001183j
[2025-09-12 05:44:24] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -51.582186+0.002352j
[2025-09-12 05:44:39] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -51.636095-0.002837j
[2025-09-12 05:44:55] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -51.728360+0.001034j
[2025-09-12 05:45:10] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -51.669695-0.002429j
[2025-09-12 05:45:25] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -51.615963-0.000009j
[2025-09-12 05:45:40] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -51.710043-0.000435j
[2025-09-12 05:45:56] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -51.710820-0.001197j
[2025-09-12 05:46:11] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -51.723893+0.000521j
[2025-09-12 05:46:26] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -51.703309+0.002302j
[2025-09-12 05:46:41] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -51.646472-0.000199j
[2025-09-12 05:46:57] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -51.713486+0.002190j
[2025-09-12 05:47:12] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -51.757513-0.001116j
[2025-09-12 05:47:27] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -51.661889+0.000324j
[2025-09-12 05:47:42] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -51.917759-0.003125j
[2025-09-12 05:47:57] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -51.741718-0.002131j
[2025-09-12 05:48:13] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -51.680776-0.000261j
[2025-09-12 05:48:28] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -51.664075+0.000764j
[2025-09-12 05:48:43] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -51.734393+0.002832j
[2025-09-12 05:48:58] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -51.838683+0.003058j
[2025-09-12 05:49:13] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -51.854537+0.004007j
[2025-09-12 05:49:29] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -51.732180-0.000195j
[2025-09-12 05:49:44] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -51.678334-0.001704j
[2025-09-12 05:49:59] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -51.621725+0.001399j
[2025-09-12 05:50:14] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -51.527332+0.000837j
[2025-09-12 05:50:30] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -51.647994+0.003605j
[2025-09-12 05:50:45] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -51.665513+0.000061j
[2025-09-12 05:51:00] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -51.825499-0.000115j
[2025-09-12 05:51:15] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -51.672264+0.000093j
[2025-09-12 05:51:31] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -51.764038-0.002813j
[2025-09-12 05:51:46] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -51.673512-0.003323j
[2025-09-12 05:52:01] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -51.791628-0.002602j
[2025-09-12 05:52:16] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -51.548356+0.001414j
[2025-09-12 05:52:32] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -51.702626+0.004138j
[2025-09-12 05:52:47] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -51.594159-0.001311j
[2025-09-12 05:53:02] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -51.635503-0.000660j
[2025-09-12 05:53:02] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-12 05:53:17] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -51.751116+0.001121j
[2025-09-12 05:53:33] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -51.617675-0.002211j
[2025-09-12 05:53:48] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -51.657971+0.001245j
[2025-09-12 05:54:03] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -51.680929-0.001216j
[2025-09-12 05:54:18] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -51.547981+0.001554j
[2025-09-12 05:54:34] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -51.711029-0.005375j
[2025-09-12 05:54:49] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -51.702018-0.004747j
[2025-09-12 05:55:04] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -51.705359+0.000303j
[2025-09-12 05:55:19] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -51.808509-0.001515j
[2025-09-12 05:55:35] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -51.743825-0.000805j
[2025-09-12 05:55:50] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -51.753636-0.002021j
[2025-09-12 05:56:05] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -51.678170-0.000688j
[2025-09-12 05:56:20] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -51.681121+0.001568j
[2025-09-12 05:56:36] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -51.825948-0.001096j
[2025-09-12 05:56:51] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -51.811474+0.000367j
[2025-09-12 05:57:06] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -51.759834+0.006234j
[2025-09-12 05:57:21] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -51.786159+0.001760j
[2025-09-12 05:57:37] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -51.866067+0.000023j
[2025-09-12 05:57:52] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -51.868527-0.001021j
[2025-09-12 05:58:07] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -51.847330-0.000891j
[2025-09-12 05:58:22] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -51.749943-0.001985j
[2025-09-12 05:58:38] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -51.646425+0.002197j
[2025-09-12 05:58:53] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -51.760464+0.000060j
[2025-09-12 05:59:08] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -51.869350+0.000704j
[2025-09-12 05:59:23] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -51.735907-0.001187j
[2025-09-12 05:59:38] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -51.671564+0.003551j
[2025-09-12 05:59:54] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -51.647801+0.001116j
[2025-09-12 06:00:09] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -51.554604-0.000938j
[2025-09-12 06:00:24] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -51.647912+0.001152j
[2025-09-12 06:00:39] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -51.736999+0.000905j
[2025-09-12 06:00:55] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -51.720522+0.000190j
[2025-09-12 06:01:10] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -51.689692-0.002459j
[2025-09-12 06:01:25] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -51.541358-0.000875j
[2025-09-12 06:01:40] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -51.545012-0.001242j
[2025-09-12 06:01:56] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -51.600174-0.000873j
[2025-09-12 06:02:11] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -51.612230-0.001078j
[2025-09-12 06:02:26] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -51.599898+0.002434j
[2025-09-12 06:02:41] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -51.660913+0.000747j
[2025-09-12 06:02:56] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -51.620030+0.001216j
[2025-09-12 06:03:12] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -51.819954+0.002288j
[2025-09-12 06:03:27] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -51.740943+0.002538j
[2025-09-12 06:03:42] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -51.657852+0.003621j
[2025-09-12 06:03:57] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -51.643374+0.001521j
[2025-09-12 06:04:12] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -51.701646+0.003108j
[2025-09-12 06:04:28] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -51.912284+0.000952j
[2025-09-12 06:04:43] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -51.858845+0.000364j
[2025-09-12 06:04:58] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -51.836449+0.001407j
[2025-09-12 06:05:13] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -51.709506-0.001319j
[2025-09-12 06:05:29] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -51.648414+0.000926j
[2025-09-12 06:05:44] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -51.665154-0.004288j
[2025-09-12 06:05:59] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -51.687129-0.001561j
[2025-09-12 06:06:14] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -51.671437-0.004344j
[2025-09-12 06:06:30] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -51.599532-0.000672j
[2025-09-12 06:06:45] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -51.523746+0.003987j
[2025-09-12 06:07:00] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -51.738013+0.001012j
[2025-09-12 06:07:15] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -51.712094-0.001907j
[2025-09-12 06:07:31] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -51.675684+0.000751j
[2025-09-12 06:07:46] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -51.610477-0.000248j
[2025-09-12 06:08:01] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -51.656694+0.002749j
[2025-09-12 06:08:16] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -51.801061+0.000198j
[2025-09-12 06:08:32] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -51.660105-0.002507j
[2025-09-12 06:08:47] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -51.818312+0.000158j
[2025-09-12 06:09:02] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -51.641146-0.000095j
[2025-09-12 06:09:17] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -51.683609-0.000698j
[2025-09-12 06:09:33] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -51.708845+0.000342j
[2025-09-12 06:09:45] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -51.730992-0.000265j
[2025-09-12 06:09:55] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -51.670010+0.000702j
[2025-09-12 06:10:06] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -51.742586-0.001941j
[2025-09-12 06:10:16] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -51.759204-0.000108j
[2025-09-12 06:10:26] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -51.621214-0.002691j
[2025-09-12 06:10:36] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -51.832057-0.001915j
[2025-09-12 06:10:46] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -51.787202+0.000553j
[2025-09-12 06:11:01] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -51.826768+0.000970j
[2025-09-12 06:11:16] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -51.692882+0.001132j
[2025-09-12 06:11:32] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -51.734170+0.000416j
[2025-09-12 06:11:47] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -51.724787+0.001072j
[2025-09-12 06:12:02] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -51.733154-0.000679j
[2025-09-12 06:12:17] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -51.648832+0.001611j
[2025-09-12 06:12:33] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -51.682444-0.001537j
[2025-09-12 06:12:48] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -51.712244+0.002559j
[2025-09-12 06:13:03] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -51.724591-0.003093j
[2025-09-12 06:13:18] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -51.694882+0.000673j
[2025-09-12 06:13:34] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -51.650321-0.002529j
[2025-09-12 06:13:49] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -51.566076+0.001955j
[2025-09-12 06:14:04] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -51.710500+0.000485j
[2025-09-12 06:14:20] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -51.541415+0.002552j
[2025-09-12 06:14:35] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -51.663225-0.000797j
[2025-09-12 06:14:50] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -51.653872+0.003329j
[2025-09-12 06:15:05] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -51.666763+0.000692j
[2025-09-12 06:15:21] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -51.778360+0.001166j
[2025-09-12 06:15:36] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -51.731519+0.000601j
[2025-09-12 06:15:51] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -51.791243+0.001095j
[2025-09-12 06:16:07] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -51.756246+0.002283j
[2025-09-12 06:16:22] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -51.770474+0.001146j
[2025-09-12 06:16:37] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -51.663386+0.001470j
[2025-09-12 06:16:52] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -51.775275+0.003031j
[2025-09-12 06:17:08] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -51.799487+0.001826j
[2025-09-12 06:17:23] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -51.813499+0.000451j
[2025-09-12 06:17:38] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -51.818604+0.000247j
[2025-09-12 06:17:53] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -51.832354-0.000605j
[2025-09-12 06:18:09] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -51.821574-0.000059j
[2025-09-12 06:18:24] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -51.768392+0.000543j
[2025-09-12 06:18:39] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -51.765367+0.001849j
[2025-09-12 06:18:55] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -51.748170-0.001153j
[2025-09-12 06:19:10] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -51.823947+0.001337j
[2025-09-12 06:19:10] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-12 06:19:25] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -51.741526+0.000205j
[2025-09-12 06:19:40] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -51.727064+0.004407j
[2025-09-12 06:19:56] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -51.833064-0.003401j
[2025-09-12 06:20:11] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -51.846383-0.001016j
[2025-09-12 06:20:24] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -51.834729+0.002464j
[2025-09-12 06:20:35] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -51.772261-0.000145j
[2025-09-12 06:20:48] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -51.791990-0.001516j
[2025-09-12 06:20:59] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -51.737571-0.004809j
[2025-09-12 06:21:14] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -51.885621+0.000369j
[2025-09-12 06:21:29] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -51.904242-0.000008j
[2025-09-12 06:21:44] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -51.901416-0.000370j
[2025-09-12 06:21:59] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -51.954876+0.001635j
[2025-09-12 06:22:15] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -51.754707-0.000254j
[2025-09-12 06:22:30] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -51.794277+0.001704j
[2025-09-12 06:22:45] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -51.932655-0.001879j
[2025-09-12 06:23:01] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -51.768738-0.000908j
[2025-09-12 06:23:16] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -51.752209-0.003231j
[2025-09-12 06:23:31] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -51.905673+0.003338j
[2025-09-12 06:23:46] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -51.781534+0.002545j
[2025-09-12 06:24:02] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -51.775158+0.000999j
[2025-09-12 06:24:17] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -51.860872-0.000108j
[2025-09-12 06:24:32] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -51.853691-0.001903j
[2025-09-12 06:24:47] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -51.668504+0.000783j
[2025-09-12 06:25:03] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -51.683148+0.001489j
[2025-09-12 06:25:18] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -51.590567-0.001828j
[2025-09-12 06:25:33] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -51.691800+0.001485j
[2025-09-12 06:25:48] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -51.618856-0.001947j
[2025-09-12 06:26:03] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -51.785357-0.002197j
[2025-09-12 06:26:19] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -51.796859+0.000770j
[2025-09-12 06:26:34] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -51.696411-0.001022j
[2025-09-12 06:26:49] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -51.729332+0.001973j
[2025-09-12 06:27:04] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -51.580042-0.000375j
[2025-09-12 06:27:19] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -51.645962-0.003886j
[2025-09-12 06:27:35] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -51.666666+0.000504j
[2025-09-12 06:27:50] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -51.619496-0.000897j
[2025-09-12 06:28:05] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -51.671641+0.001262j
[2025-09-12 06:28:20] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -51.745076-0.002369j
[2025-09-12 06:28:35] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -51.692910-0.000235j
[2025-09-12 06:28:50] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -51.683824+0.002418j
[2025-09-12 06:29:06] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -51.681177+0.005009j
[2025-09-12 06:29:21] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -51.743670-0.000314j
[2025-09-12 06:29:36] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -51.762702-0.000464j
[2025-09-12 06:29:51] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -51.720889+0.000258j
[2025-09-12 06:30:07] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -51.702944+0.000250j
[2025-09-12 06:30:22] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -51.784733-0.001527j
[2025-09-12 06:30:37] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -51.806892+0.002917j
[2025-09-12 06:30:53] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -51.789993-0.000634j
[2025-09-12 06:31:08] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -51.766455-0.001423j
[2025-09-12 06:31:23] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -51.653054-0.001039j
[2025-09-12 06:31:38] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -51.848779+0.002629j
[2025-09-12 06:31:54] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -51.824409-0.001408j
[2025-09-12 06:32:09] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -51.696417+0.001119j
[2025-09-12 06:32:24] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -51.683364+0.000865j
[2025-09-12 06:32:39] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -51.688144+0.000242j
[2025-09-12 06:32:55] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -51.734206-0.001468j
[2025-09-12 06:33:10] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -51.757107-0.001593j
[2025-09-12 06:33:25] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -51.722678+0.000769j
[2025-09-12 06:33:40] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -51.799072-0.002205j
[2025-09-12 06:33:56] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -51.763725-0.001439j
[2025-09-12 06:34:11] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -51.736684+0.001814j
[2025-09-12 06:34:26] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -51.784054+0.000558j
[2025-09-12 06:34:41] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -51.815568+0.001961j
[2025-09-12 06:34:57] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -51.755803-0.000118j
[2025-09-12 06:35:12] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -51.775270+0.000403j
[2025-09-12 06:35:27] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -51.829146+0.002079j
[2025-09-12 06:35:42] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -51.857071+0.001256j
[2025-09-12 06:35:58] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -51.774802+0.001183j
[2025-09-12 06:36:13] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -51.791945+0.003584j
[2025-09-12 06:36:28] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -51.813519-0.000807j
[2025-09-12 06:36:43] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -51.800774-0.000291j
[2025-09-12 06:36:59] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -51.695009+0.000133j
[2025-09-12 06:37:14] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -51.676873-0.004421j
[2025-09-12 06:37:29] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -51.721855+0.000162j
[2025-09-12 06:37:44] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -51.757131+0.000983j
[2025-09-12 06:38:00] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -51.774967-0.000525j
[2025-09-12 06:38:15] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -51.779471+0.002599j
[2025-09-12 06:38:30] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -51.880705+0.000266j
[2025-09-12 06:38:45] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -51.915615+0.001810j
[2025-09-12 06:39:01] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -51.934555-0.000910j
[2025-09-12 06:39:16] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -51.806312+0.000091j
[2025-09-12 06:39:31] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -51.775335+0.000272j
[2025-09-12 06:39:46] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -51.669010+0.001116j
[2025-09-12 06:40:02] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -51.635056+0.001250j
[2025-09-12 06:40:17] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -51.539657-0.000584j
[2025-09-12 06:40:32] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -51.633725+0.001738j
[2025-09-12 06:40:47] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -51.606243+0.001452j
[2025-09-12 06:41:02] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -51.648704+0.001626j
[2025-09-12 06:41:18] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -51.695117-0.000133j
[2025-09-12 06:41:33] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -51.728990+0.000213j
[2025-09-12 06:41:48] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -51.643531+0.000827j
[2025-09-12 06:42:03] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -51.796831+0.002134j
[2025-09-12 06:42:19] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -51.743622-0.002632j
[2025-09-12 06:42:34] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -51.863919-0.000373j
[2025-09-12 06:42:49] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -51.835441+0.000253j
[2025-09-12 06:43:04] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -51.943442+0.001218j
[2025-09-12 06:43:20] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -51.916318-0.000853j
[2025-09-12 06:43:35] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -51.783431+0.000289j
[2025-09-12 06:43:50] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -51.665986+0.001823j
[2025-09-12 06:44:05] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -51.671835+0.000899j
[2025-09-12 06:44:21] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -51.721272-0.001455j
[2025-09-12 06:44:36] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -51.750328-0.002287j
[2025-09-12 06:44:51] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -51.710838-0.002339j
[2025-09-12 06:45:06] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -51.749154+0.000333j
[2025-09-12 06:45:22] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -51.671450-0.001393j
[2025-09-12 06:45:37] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -51.677768+0.003959j
[2025-09-12 06:45:37] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-12 06:45:52] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -51.661328+0.000955j
[2025-09-12 06:46:07] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -51.625190-0.001111j
[2025-09-12 06:46:23] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -51.729499+0.000894j
[2025-09-12 06:46:38] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -51.827528+0.000006j
[2025-09-12 06:46:53] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -51.777997-0.004327j
[2025-09-12 06:47:08] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -51.810628-0.000649j
[2025-09-12 06:47:24] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -51.776203-0.003017j
[2025-09-12 06:47:39] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -51.682832-0.000191j
[2025-09-12 06:47:54] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -51.683267+0.001011j
[2025-09-12 06:48:09] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -51.603210+0.000364j
[2025-09-12 06:48:25] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -51.708931+0.001419j
[2025-09-12 06:48:40] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -51.644933+0.001676j
[2025-09-12 06:48:55] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -51.719715+0.000631j
[2025-09-12 06:49:10] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -51.662398+0.001876j
[2025-09-12 06:49:26] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -51.699679-0.003674j
[2025-09-12 06:49:41] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -51.724745+0.002243j
[2025-09-12 06:49:56] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -51.621119-0.000685j
[2025-09-12 06:50:11] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -51.636679-0.001381j
[2025-09-12 06:50:27] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -51.659892+0.001164j
[2025-09-12 06:50:42] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -51.775526-0.001620j
[2025-09-12 06:50:57] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -51.845995+0.001690j
[2025-09-12 06:51:12] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -51.745234-0.001301j
[2025-09-12 06:51:28] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -51.607137+0.001276j
[2025-09-12 06:51:43] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -51.628933+0.001265j
[2025-09-12 06:51:58] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -51.690218-0.000774j
[2025-09-12 06:52:13] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -51.827425+0.001165j
[2025-09-12 06:52:29] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -51.738349+0.000940j
[2025-09-12 06:52:44] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -51.817808+0.002004j
[2025-09-12 06:52:59] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -51.799848+0.001055j
[2025-09-12 06:53:14] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -51.792330-0.003789j
[2025-09-12 06:53:14] RESTART #2 | Period: 600
[2025-09-12 06:53:30] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -51.690838-0.000052j
[2025-09-12 06:53:45] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -51.756171+0.001275j
[2025-09-12 06:54:00] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -51.779944+0.000602j
[2025-09-12 06:54:15] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -51.705782+0.000035j
[2025-09-12 06:54:31] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -51.730341-0.001370j
[2025-09-12 06:54:45] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -51.697996-0.000176j
[2025-09-12 06:54:55] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -51.693577+0.002715j
[2025-09-12 06:55:05] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -51.726391-0.003239j
[2025-09-12 06:55:15] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -51.764838-0.002034j
[2025-09-12 06:55:26] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -51.635616-0.002143j
[2025-09-12 06:55:36] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -51.783666+0.002505j
[2025-09-12 06:55:46] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -51.659688+0.001941j
[2025-09-12 06:55:56] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -51.624662-0.002926j
[2025-09-12 06:56:11] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -51.587780-0.001136j
[2025-09-12 06:56:26] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -51.578398+0.000301j
[2025-09-12 06:56:42] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -51.601712-0.001428j
[2025-09-12 06:56:57] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -51.768291+0.001625j
[2025-09-12 06:57:12] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -51.738684-0.000223j
[2025-09-12 06:57:27] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -51.727470+0.000160j
[2025-09-12 06:57:43] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -51.679779-0.004647j
[2025-09-12 06:57:58] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -51.702479-0.001313j
[2025-09-12 06:58:13] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -51.731883-0.001302j
[2025-09-12 06:58:28] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -51.632559+0.003541j
[2025-09-12 06:58:44] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -51.716949+0.002113j
[2025-09-12 06:58:59] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -51.684457+0.002497j
[2025-09-12 06:59:14] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -51.736368-0.000903j
[2025-09-12 06:59:29] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -51.721018-0.000925j
[2025-09-12 06:59:45] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -51.812277+0.003125j
[2025-09-12 07:00:00] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -51.678642-0.002928j
[2025-09-12 07:00:15] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -51.728906+0.000589j
[2025-09-12 07:00:30] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -51.757213-0.001170j
[2025-09-12 07:00:46] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -51.679919+0.002940j
[2025-09-12 07:01:01] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -51.765088+0.000779j
[2025-09-12 07:01:16] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -51.677851-0.001528j
[2025-09-12 07:01:31] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -51.601983+0.000863j
[2025-09-12 07:01:47] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -51.733236-0.003895j
[2025-09-12 07:02:02] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -51.725015-0.000581j
[2025-09-12 07:02:17] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -51.724075-0.000160j
[2025-09-12 07:02:32] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -51.701738-0.000235j
[2025-09-12 07:02:48] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -51.710648-0.000110j
[2025-09-12 07:03:03] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -51.654615+0.000386j
[2025-09-12 07:03:18] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -51.607831-0.002081j
[2025-09-12 07:03:33] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -51.709360+0.002687j
[2025-09-12 07:03:49] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -51.783186-0.000912j
[2025-09-12 07:04:04] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -51.764793-0.005158j
[2025-09-12 07:04:19] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -51.632215-0.004485j
[2025-09-12 07:04:35] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -51.723878+0.002590j
[2025-09-12 07:04:50] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -51.534735+0.002147j
[2025-09-12 07:05:05] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -51.738511-0.000179j
[2025-09-12 07:05:20] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -51.732179+0.001600j
[2025-09-12 07:05:32] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -51.819484-0.000542j
[2025-09-12 07:05:44] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -51.745375-0.000263j
[2025-09-12 07:05:57] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -51.735826-0.000635j
[2025-09-12 07:06:08] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -51.887597+0.000480j
[2025-09-12 07:06:24] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -51.848287-0.002572j
[2025-09-12 07:06:39] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -51.895221-0.000265j
[2025-09-12 07:06:54] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -51.888796-0.004519j
[2025-09-12 07:07:09] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -51.739498+0.002216j
[2025-09-12 07:07:24] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -51.856363+0.000463j
[2025-09-12 07:07:39] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -51.794155+0.002698j
[2025-09-12 07:07:55] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -51.951469+0.002426j
[2025-09-12 07:08:10] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -51.936599+0.002750j
[2025-09-12 07:08:25] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -51.902292-0.000662j
[2025-09-12 07:08:40] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -51.751550+0.000037j
[2025-09-12 07:08:55] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -51.738690-0.001032j
[2025-09-12 07:09:10] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -51.724377+0.000255j
[2025-09-12 07:09:26] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -51.704356+0.000142j
[2025-09-12 07:09:41] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -51.633692+0.001030j
[2025-09-12 07:09:56] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -51.788301+0.000294j
[2025-09-12 07:10:11] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -51.744783+0.002060j
[2025-09-12 07:10:26] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -51.762723+0.000990j
[2025-09-12 07:10:41] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -51.888612+0.000512j
[2025-09-12 07:10:57] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -51.697928+0.001171j
[2025-09-12 07:11:12] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -51.638079-0.001664j
[2025-09-12 07:11:27] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -51.673873-0.000046j
[2025-09-12 07:11:27] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-12 07:11:42] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -51.626763+0.000473j
[2025-09-12 07:11:57] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -51.587993-0.000702j
[2025-09-12 07:12:13] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -51.729629+0.000254j
[2025-09-12 07:12:28] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -51.608384+0.001311j
[2025-09-12 07:12:43] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -51.605996+0.001331j
[2025-09-12 07:12:58] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -51.709383+0.000836j
[2025-09-12 07:13:14] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -51.662696-0.001168j
[2025-09-12 07:13:29] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -51.577933+0.002498j
[2025-09-12 07:13:44] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -51.681950-0.001030j
[2025-09-12 07:13:59] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -51.555924-0.000094j
[2025-09-12 07:14:14] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -51.617730-0.000219j
[2025-09-12 07:14:29] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -51.790756+0.000708j
[2025-09-12 07:14:45] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -51.620131+0.001041j
[2025-09-12 07:15:00] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -51.694914+0.001364j
[2025-09-12 07:15:15] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -51.785048+0.004075j
[2025-09-12 07:15:30] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -51.679050-0.002063j
[2025-09-12 07:15:45] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -51.783606+0.001149j
[2025-09-12 07:16:01] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -51.691563-0.000271j
[2025-09-12 07:16:16] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -51.620377-0.000754j
[2025-09-12 07:16:31] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -51.734504-0.002106j
[2025-09-12 07:16:46] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -51.697296-0.000252j
[2025-09-12 07:17:02] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -51.717527-0.001072j
[2025-09-12 07:17:17] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -51.735671+0.002156j
[2025-09-12 07:17:32] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -51.880047-0.002793j
[2025-09-12 07:17:47] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -51.894261+0.003447j
[2025-09-12 07:18:02] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -51.828830-0.000234j
[2025-09-12 07:18:18] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -51.760681+0.000291j
[2025-09-12 07:18:33] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -51.722111+0.003585j
[2025-09-12 07:18:48] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -51.716088-0.003091j
[2025-09-12 07:19:03] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -51.847156+0.002751j
[2025-09-12 07:19:19] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -51.663896-0.000323j
[2025-09-12 07:19:34] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -51.627361+0.001465j
[2025-09-12 07:19:49] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -51.747282+0.000745j
[2025-09-12 07:20:04] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -51.635820+0.000517j
[2025-09-12 07:20:19] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -51.739975-0.000291j
[2025-09-12 07:20:35] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -51.677253+0.001358j
[2025-09-12 07:20:50] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -51.689551-0.002403j
[2025-09-12 07:21:05] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -51.730356-0.000788j
[2025-09-12 07:21:20] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -51.725782-0.001193j
[2025-09-12 07:21:36] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -51.676254+0.004871j
[2025-09-12 07:21:51] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -51.662736+0.001004j
[2025-09-12 07:22:06] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -51.769241+0.003948j
[2025-09-12 07:22:21] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -51.690552+0.000430j
[2025-09-12 07:22:37] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -51.727039+0.000363j
[2025-09-12 07:22:52] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -51.609163+0.003300j
[2025-09-12 07:23:07] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -51.818681-0.001478j
[2025-09-12 07:23:22] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -51.804925-0.000639j
[2025-09-12 07:23:37] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -51.700855-0.001469j
[2025-09-12 07:23:53] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -51.805143+0.000489j
[2025-09-12 07:24:08] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -51.799071+0.000704j
[2025-09-12 07:24:23] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -51.831504-0.000746j
[2025-09-12 07:24:38] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -51.860772+0.000425j
[2025-09-12 07:24:53] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -51.804268-0.000628j
[2025-09-12 07:25:09] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -51.779587-0.001729j
[2025-09-12 07:25:24] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -51.721373-0.001654j
[2025-09-12 07:25:39] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -51.692343+0.001751j
[2025-09-12 07:25:54] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -51.736391+0.001280j
[2025-09-12 07:26:09] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -51.685081+0.000159j
[2025-09-12 07:26:25] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -51.640500+0.002375j
[2025-09-12 07:26:40] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -51.713859-0.001045j
[2025-09-12 07:26:55] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -51.746657+0.001760j
[2025-09-12 07:27:10] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -51.839606-0.000110j
[2025-09-12 07:27:25] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -51.694148+0.000503j
[2025-09-12 07:27:41] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -51.679414-0.000207j
[2025-09-12 07:27:56] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -51.707435+0.000916j
[2025-09-12 07:28:11] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -51.768212+0.003387j
[2025-09-12 07:28:26] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -51.722537+0.002936j
[2025-09-12 07:28:42] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -51.861990+0.001397j
[2025-09-12 07:28:57] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -51.880560+0.004031j
[2025-09-12 07:29:12] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -51.770201-0.000634j
[2025-09-12 07:29:27] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -51.754700+0.000147j
[2025-09-12 07:29:43] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -51.745146+0.001229j
[2025-09-12 07:29:58] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -51.743093-0.000835j
[2025-09-12 07:30:13] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -51.661868-0.002251j
[2025-09-12 07:30:28] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -51.789465-0.002913j
[2025-09-12 07:30:44] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -51.738099-0.002868j
[2025-09-12 07:30:59] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -51.696111-0.002364j
[2025-09-12 07:31:14] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -51.726904-0.001079j
[2025-09-12 07:31:29] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -51.787031+0.000066j
[2025-09-12 07:31:44] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -51.696069-0.000729j
[2025-09-12 07:32:00] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -51.628052+0.003160j
[2025-09-12 07:32:15] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -51.669903-0.001879j
[2025-09-12 07:32:30] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -51.646602-0.000327j
[2025-09-12 07:32:45] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -51.780987+0.002370j
[2025-09-12 07:33:01] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -51.490964+0.001969j
[2025-09-12 07:33:16] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -51.589162-0.001444j
[2025-09-12 07:33:31] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -51.527956-0.002408j
[2025-09-12 07:33:46] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -51.728565+0.003381j
[2025-09-12 07:34:01] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -51.607667+0.002208j
[2025-09-12 07:34:17] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -51.794711-0.002206j
[2025-09-12 07:34:32] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -51.563546+0.001628j
[2025-09-12 07:34:47] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -51.601868+0.002091j
[2025-09-12 07:35:02] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -51.577416-0.001390j
[2025-09-12 07:35:18] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -51.529164+0.000943j
[2025-09-12 07:35:33] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -51.567376-0.003122j
[2025-09-12 07:35:48] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -51.539796+0.002305j
[2025-09-12 07:36:03] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -51.615350+0.001130j
[2025-09-12 07:36:19] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -51.581322-0.003155j
[2025-09-12 07:36:34] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -51.683715+0.002364j
[2025-09-12 07:36:49] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -51.688669-0.001535j
[2025-09-12 07:37:04] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -51.639979+0.001798j
[2025-09-12 07:37:19] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -51.606062-0.000999j
[2025-09-12 07:37:35] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -51.619545-0.001980j
[2025-09-12 07:37:50] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -51.842778+0.001540j
[2025-09-12 07:38:05] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -51.755386+0.003287j
[2025-09-12 07:38:05] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-12 07:38:20] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -51.753575+0.000990j
[2025-09-12 07:38:36] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -51.693723+0.003179j
[2025-09-12 07:38:51] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -51.725578-0.000766j
[2025-09-12 07:39:06] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -51.682163-0.001588j
[2025-09-12 07:39:21] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -51.664045+0.001791j
[2025-09-12 07:39:32] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -51.702085-0.000863j
[2025-09-12 07:39:42] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -51.714610-0.002208j
[2025-09-12 07:39:53] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -51.844199-0.003128j
[2025-09-12 07:40:03] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -51.827237+0.007457j
[2025-09-12 07:40:13] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -51.814950+0.002340j
[2025-09-12 07:40:23] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -51.823790+0.001034j
[2025-09-12 07:40:36] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -51.692401-0.000564j
[2025-09-12 07:40:51] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -51.699153+0.000615j
[2025-09-12 07:41:06] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -51.781895-0.001995j
[2025-09-12 07:41:22] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -51.819239-0.001166j
[2025-09-12 07:41:37] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -51.770802+0.000512j
[2025-09-12 07:41:52] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -51.731379-0.002086j
[2025-09-12 07:42:07] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -51.673480+0.000098j
[2025-09-12 07:42:23] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -51.707301-0.003371j
[2025-09-12 07:42:38] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -51.654293-0.001682j
[2025-09-12 07:42:53] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -51.715026+0.000169j
[2025-09-12 07:43:09] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -51.660938+0.002029j
[2025-09-12 07:43:24] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -51.522410-0.000557j
[2025-09-12 07:43:39] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -51.424448-0.001485j
[2025-09-12 07:43:54] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -51.487337-0.002697j
[2025-09-12 07:44:10] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -51.570067+0.001078j
[2025-09-12 07:44:25] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -51.626374-0.000082j
[2025-09-12 07:44:40] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -51.679427-0.001389j
[2025-09-12 07:44:56] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -51.627699+0.000331j
[2025-09-12 07:45:11] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -51.718058+0.003585j
[2025-09-12 07:45:26] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -51.638572+0.000425j
[2025-09-12 07:45:41] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -51.565996+0.000256j
[2025-09-12 07:45:57] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -51.702071+0.001601j
[2025-09-12 07:46:12] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -51.779916+0.000004j
[2025-09-12 07:46:27] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -51.764062-0.001954j
[2025-09-12 07:46:43] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -51.793375-0.002362j
[2025-09-12 07:46:58] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -51.717848+0.000166j
[2025-09-12 07:47:13] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -51.874459+0.002664j
[2025-09-12 07:47:29] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -51.832126-0.000169j
[2025-09-12 07:47:44] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -51.762563+0.000515j
[2025-09-12 07:47:59] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -51.774537+0.001425j
[2025-09-12 07:48:14] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -51.729601-0.005047j
[2025-09-12 07:48:30] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -51.740139+0.000880j
[2025-09-12 07:48:45] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -51.902055+0.000237j
[2025-09-12 07:49:00] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -51.685065-0.003949j
[2025-09-12 07:49:15] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -51.821196-0.001861j
[2025-09-12 07:49:31] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -51.782773-0.002931j
[2025-09-12 07:49:46] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -51.851866-0.000514j
[2025-09-12 07:50:01] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -51.841221+0.001768j
[2025-09-12 07:50:12] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -51.716828+0.001212j
[2025-09-12 07:50:25] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -51.688765+0.001501j
[2025-09-12 07:50:37] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -51.749819+0.000688j
[2025-09-12 07:50:49] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -51.800259-0.000546j
[2025-09-12 07:51:04] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -51.774471+0.001666j
[2025-09-12 07:51:19] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -51.766650-0.001067j
[2025-09-12 07:51:34] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -51.689554-0.001109j
[2025-09-12 07:51:50] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -51.757594+0.000978j
[2025-09-12 07:52:05] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -51.728574+0.001052j
[2025-09-12 07:52:20] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -51.718999-0.002143j
[2025-09-12 07:52:35] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -51.687167+0.001992j
[2025-09-12 07:52:51] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -51.597554-0.001418j
[2025-09-12 07:53:06] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -51.767837-0.002915j
[2025-09-12 07:53:21] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -51.787173+0.001524j
[2025-09-12 07:53:36] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -51.630996+0.000660j
[2025-09-12 07:53:52] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -51.825951-0.001233j
[2025-09-12 07:54:07] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -51.843350-0.001038j
[2025-09-12 07:54:22] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -51.796473-0.001191j
[2025-09-12 07:54:37] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -51.869182-0.003312j
[2025-09-12 07:54:53] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -51.888140+0.002059j
[2025-09-12 07:55:08] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -51.855593-0.000595j
[2025-09-12 07:55:23] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -51.796052+0.000293j
[2025-09-12 07:55:38] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -51.949266+0.002937j
[2025-09-12 07:55:54] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -51.634699-0.001974j
[2025-09-12 07:56:09] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -51.719292+0.000632j
[2025-09-12 07:56:24] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -51.633478+0.002452j
[2025-09-12 07:56:39] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -51.624978+0.000786j
[2025-09-12 07:56:54] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -51.890186-0.000174j
[2025-09-12 07:57:10] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -51.676731-0.001045j
[2025-09-12 07:57:25] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -51.679500+0.001003j
[2025-09-12 07:57:40] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -51.725310+0.001520j
[2025-09-12 07:57:55] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -51.625670-0.000637j
[2025-09-12 07:58:11] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -51.621657+0.001805j
[2025-09-12 07:58:26] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -51.621785-0.000341j
[2025-09-12 07:58:41] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -51.630156+0.001307j
[2025-09-12 07:58:56] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -51.658328-0.003182j
[2025-09-12 07:59:11] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -51.785610+0.000535j
[2025-09-12 07:59:27] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -51.851408-0.000206j
[2025-09-12 07:59:42] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -51.865837-0.000445j
[2025-09-12 07:59:57] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -51.633409+0.005326j
[2025-09-12 08:00:12] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -51.655440-0.000802j
[2025-09-12 08:00:27] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -51.623953+0.000472j
[2025-09-12 08:00:43] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -51.678700+0.000813j
[2025-09-12 08:00:58] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -51.871213-0.000214j
[2025-09-12 08:01:13] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -51.776031-0.000286j
[2025-09-12 08:01:28] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -51.726224+0.001053j
[2025-09-12 08:01:44] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -51.757006+0.000586j
[2025-09-12 08:01:59] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -51.750240-0.001408j
[2025-09-12 08:02:14] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -51.770671-0.001467j
[2025-09-12 08:02:29] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -51.777258-0.000619j
[2025-09-12 08:02:44] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -51.725851-0.000926j
[2025-09-12 08:03:00] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -51.640816-0.001666j
[2025-09-12 08:03:15] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -51.708308-0.001374j
[2025-09-12 08:03:30] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -51.632826-0.000463j
[2025-09-12 08:03:45] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -51.691633+0.001804j
[2025-09-12 08:04:01] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -51.708949+0.001929j
[2025-09-12 08:04:01] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-12 08:04:16] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -51.847423+0.000494j
[2025-09-12 08:04:31] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -51.782217+0.000105j
[2025-09-12 08:04:46] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -51.572071+0.000384j
[2025-09-12 08:05:01] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -51.704041+0.000914j
[2025-09-12 08:05:17] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -51.753539-0.001638j
[2025-09-12 08:05:32] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -51.775088+0.000875j
[2025-09-12 08:05:47] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -51.830744-0.000835j
[2025-09-12 08:06:02] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -51.950406+0.001973j
[2025-09-12 08:06:18] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -51.758192-0.001208j
[2025-09-12 08:06:33] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -51.708214+0.000196j
[2025-09-12 08:06:48] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -51.822243+0.001071j
[2025-09-12 08:07:03] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -51.726480+0.001288j
[2025-09-12 08:07:19] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -51.884642+0.000203j
[2025-09-12 08:07:34] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -51.759893-0.001889j
[2025-09-12 08:07:49] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -51.825108-0.002257j
[2025-09-12 08:08:04] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -51.813296+0.001006j
[2025-09-12 08:08:20] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -51.664170-0.001755j
[2025-09-12 08:08:35] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -51.686461-0.001213j
[2025-09-12 08:08:50] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -51.702698-0.000621j
[2025-09-12 08:09:05] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -51.622232-0.000643j
[2025-09-12 08:09:21] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -51.793625+0.000857j
[2025-09-12 08:09:36] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -51.794272+0.001223j
[2025-09-12 08:09:51] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -51.737937+0.001892j
[2025-09-12 08:10:06] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -51.786111+0.000453j
[2025-09-12 08:10:22] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -51.742192+0.001046j
[2025-09-12 08:10:37] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -51.831739+0.002321j
[2025-09-12 08:10:52] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -51.683278+0.000153j
[2025-09-12 08:11:07] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -51.707861+0.002016j
[2025-09-12 08:11:23] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -51.621812-0.001264j
[2025-09-12 08:11:38] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -51.588518-0.002081j
[2025-09-12 08:11:53] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -51.671695-0.001843j
[2025-09-12 08:12:08] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -51.665310+0.002537j
[2025-09-12 08:12:23] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -51.595999+0.000605j
[2025-09-12 08:12:39] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -51.658728+0.001463j
[2025-09-12 08:12:54] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -51.792156-0.017185j
[2025-09-12 08:13:09] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -51.767275+0.001615j
[2025-09-12 08:13:24] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -51.810811+0.001518j
[2025-09-12 08:13:40] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -51.584726-0.001822j
[2025-09-12 08:13:55] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -51.702260+0.003416j
[2025-09-12 08:14:10] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -51.731055+0.001399j
[2025-09-12 08:14:25] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -51.721654-0.003294j
[2025-09-12 08:14:40] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -51.718423-0.002221j
[2025-09-12 08:14:56] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -51.816292-0.000305j
[2025-09-12 08:15:11] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -51.755907+0.000985j
[2025-09-12 08:15:26] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -51.738238-0.000530j
[2025-09-12 08:15:41] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -51.695594-0.000432j
[2025-09-12 08:15:56] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -51.661771+0.003701j
[2025-09-12 08:16:12] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -51.700563+0.001228j
[2025-09-12 08:16:27] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -51.639352-0.000190j
[2025-09-12 08:16:42] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -51.719766-0.000150j
[2025-09-12 08:16:57] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -51.712642+0.000498j
[2025-09-12 08:17:13] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -51.622502-0.000267j
[2025-09-12 08:17:28] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -51.651853-0.000230j
[2025-09-12 08:17:43] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -51.754023+0.001850j
[2025-09-12 08:17:59] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -51.706993+0.000070j
[2025-09-12 08:18:14] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -51.732904-0.002260j
[2025-09-12 08:18:29] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -51.714296-0.001218j
[2025-09-12 08:18:44] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -51.715811-0.000530j
[2025-09-12 08:18:59] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -51.742477+0.000901j
[2025-09-12 08:19:15] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -51.676025+0.001438j
[2025-09-12 08:19:30] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -51.702598+0.000053j
[2025-09-12 08:19:45] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -51.649571-0.000481j
[2025-09-12 08:20:00] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -51.618921+0.003000j
[2025-09-12 08:20:16] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -51.634557+0.001309j
[2025-09-12 08:20:31] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -51.655061+0.000860j
[2025-09-12 08:20:46] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -51.808328+0.000287j
[2025-09-12 08:21:01] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -51.766911-0.001612j
[2025-09-12 08:21:17] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -51.655493+0.001708j
[2025-09-12 08:21:32] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -51.657899+0.000122j
[2025-09-12 08:21:47] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -51.804319-0.000612j
[2025-09-12 08:22:02] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -51.676838+0.000766j
[2025-09-12 08:22:18] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -51.714548+0.000906j
[2025-09-12 08:22:33] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -51.725114-0.003323j
[2025-09-12 08:22:48] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -51.679416-0.000841j
[2025-09-12 08:23:03] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -51.740720-0.000492j
[2025-09-12 08:23:19] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -51.769778+0.002076j
[2025-09-12 08:23:34] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -51.744280+0.002108j
[2025-09-12 08:23:49] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -51.609523-0.001292j
[2025-09-12 08:24:04] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -51.758121+0.000482j
[2025-09-12 08:24:20] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -51.721629+0.000882j
[2025-09-12 08:24:35] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -51.706108-0.001376j
[2025-09-12 08:24:47] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -51.842005-0.000324j
[2025-09-12 08:24:57] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -51.807225+0.001859j
[2025-09-12 08:25:08] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -51.740222+0.001483j
[2025-09-12 08:25:18] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -51.850089-0.002799j
[2025-09-12 08:25:28] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -51.770799+0.000847j
[2025-09-12 08:25:38] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -51.752992+0.002296j
[2025-09-12 08:25:49] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -51.747769-0.002135j
[2025-09-12 08:26:05] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -51.767061+0.001516j
[2025-09-12 08:26:20] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -51.753808+0.001307j
[2025-09-12 08:26:35] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -51.597474+0.001053j
[2025-09-12 08:26:50] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -51.640509-0.001655j
[2025-09-12 08:27:06] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -51.586458+0.001094j
[2025-09-12 08:27:21] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -51.718703-0.001225j
[2025-09-12 08:27:36] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -51.771693+0.000140j
[2025-09-12 08:27:51] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -51.791715-0.000083j
[2025-09-12 08:28:07] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -51.630268+0.000624j
[2025-09-12 08:28:22] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -51.603042-0.001884j
[2025-09-12 08:28:37] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -51.651045+0.001003j
[2025-09-12 08:28:53] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -51.712502-0.001216j
[2025-09-12 08:29:08] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -51.698944-0.001136j
[2025-09-12 08:29:23] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -51.727897+0.001730j
[2025-09-12 08:29:38] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -51.686303-0.003357j
[2025-09-12 08:29:54] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -51.774795-0.000457j
[2025-09-12 08:30:09] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -51.835333+0.002390j
[2025-09-12 08:30:09] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-12 08:30:24] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -51.769766-0.001530j
[2025-09-12 08:30:40] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -51.828549+0.002132j
[2025-09-12 08:30:55] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -51.773674-0.001707j
[2025-09-12 08:31:10] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -51.787190-0.001067j
[2025-09-12 08:31:25] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -51.825135+0.001885j
[2025-09-12 08:31:41] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -51.862263+0.000887j
[2025-09-12 08:31:56] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -51.707852-0.000641j
[2025-09-12 08:32:11] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -51.824468-0.000088j
[2025-09-12 08:32:26] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -51.639073+0.004938j
[2025-09-12 08:32:42] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -51.655781-0.002526j
[2025-09-12 08:32:57] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -51.649665-0.001296j
[2025-09-12 08:33:12] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -51.621250+0.001781j
[2025-09-12 08:33:28] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -51.711283-0.000119j
[2025-09-12 08:33:43] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -51.625212-0.001318j
[2025-09-12 08:33:58] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -51.706232+0.000661j
[2025-09-12 08:34:13] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -51.656004-0.001341j
[2025-09-12 08:34:29] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -51.637468+0.000032j
[2025-09-12 08:34:44] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -51.592106+0.000428j
[2025-09-12 08:34:59] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -51.603635+0.001310j
[2025-09-12 08:35:14] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -51.628479+0.000687j
[2025-09-12 08:35:26] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -51.734208+0.001636j
[2025-09-12 08:35:38] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -51.710956-0.002846j
[2025-09-12 08:35:51] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -51.597333-0.000774j
[2025-09-12 08:36:02] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -51.585604+0.004662j
[2025-09-12 08:36:18] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -51.653690+0.002790j
[2025-09-12 08:36:33] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -51.712112+0.000797j
[2025-09-12 08:36:48] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -51.718365-0.000848j
[2025-09-12 08:37:03] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -51.686217-0.000924j
[2025-09-12 08:37:19] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -51.745860+0.000993j
[2025-09-12 08:37:34] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -51.667796-0.001801j
[2025-09-12 08:37:49] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -51.753298+0.001209j
[2025-09-12 08:38:04] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -51.738774-0.001085j
[2025-09-12 08:38:19] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -51.665502+0.000334j
[2025-09-12 08:38:35] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -51.814670+0.002010j
[2025-09-12 08:38:50] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -51.731284-0.000481j
[2025-09-12 08:39:05] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -51.626082-0.000402j
[2025-09-12 08:39:20] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -51.788164+0.000573j
[2025-09-12 08:39:35] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -51.743594+0.000733j
[2025-09-12 08:39:51] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -51.791812+0.000000j
[2025-09-12 08:40:06] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -51.749040+0.000040j
[2025-09-12 08:40:21] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -51.663125+0.001713j
[2025-09-12 08:40:36] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -51.786808-0.000168j
[2025-09-12 08:40:51] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -51.627089-0.002569j
[2025-09-12 08:41:07] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -51.700714+0.004617j
[2025-09-12 08:41:22] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -51.728645+0.000105j
[2025-09-12 08:41:37] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -51.723919-0.001322j
[2025-09-12 08:41:52] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -51.718516+0.002946j
[2025-09-12 08:42:08] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -51.719589-0.001218j
[2025-09-12 08:42:23] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -51.700647+0.001305j
[2025-09-12 08:42:38] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -51.806380-0.000028j
[2025-09-12 08:42:53] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -51.705472-0.003225j
[2025-09-12 08:43:08] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -51.722525+0.000565j
[2025-09-12 08:43:24] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -51.872568+0.000526j
[2025-09-12 08:43:39] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -51.696618+0.001190j
[2025-09-12 08:43:54] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -51.828266-0.000773j
[2025-09-12 08:44:09] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -51.774918+0.002707j
[2025-09-12 08:44:24] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -51.717574-0.000329j
[2025-09-12 08:44:40] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -51.711071-0.000552j
[2025-09-12 08:44:55] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -51.778255+0.001135j
[2025-09-12 08:45:10] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -51.662250+0.002158j
[2025-09-12 08:45:25] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -51.660655-0.001473j
[2025-09-12 08:45:40] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -51.797807-0.000482j
[2025-09-12 08:45:56] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -51.807991+0.000137j
[2025-09-12 08:46:11] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -51.796583-0.001129j
[2025-09-12 08:46:26] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -51.839097-0.000857j
[2025-09-12 08:46:41] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -51.770878+0.008938j
[2025-09-12 08:46:56] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -51.731406+0.000402j
[2025-09-12 08:47:12] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -51.665936-0.000942j
[2025-09-12 08:47:27] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -51.655012+0.000228j
[2025-09-12 08:47:42] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -51.748547-0.001699j
[2025-09-12 08:47:57] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -51.825504-0.002997j
[2025-09-12 08:48:12] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -51.855444+0.001518j
[2025-09-12 08:48:28] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -51.648513+0.001800j
[2025-09-12 08:48:43] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -51.741413+0.000718j
[2025-09-12 08:48:58] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -51.675162+0.004956j
[2025-09-12 08:49:13] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -51.676699-0.001873j
[2025-09-12 08:49:28] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -51.732343+0.001617j
[2025-09-12 08:49:44] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -51.851405+0.003027j
[2025-09-12 08:49:59] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -51.805307+0.000026j
[2025-09-12 08:50:14] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -51.898447-0.000158j
[2025-09-12 08:50:29] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -51.903157-0.000159j
[2025-09-12 08:50:44] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -51.903431+0.001359j
[2025-09-12 08:51:00] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -51.863751+0.002530j
[2025-09-12 08:51:15] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -51.866555-0.001532j
[2025-09-12 08:51:30] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -51.811066-0.002410j
[2025-09-12 08:51:45] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -51.844550+0.002003j
[2025-09-12 08:52:01] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -51.863201-0.001556j
[2025-09-12 08:52:16] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -51.801040+0.001454j
[2025-09-12 08:52:31] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -51.739385-0.000122j
[2025-09-12 08:52:46] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -51.759160-0.001608j
[2025-09-12 08:53:01] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -51.777164-0.001010j
[2025-09-12 08:53:17] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -51.950546-0.002549j
[2025-09-12 08:53:32] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -51.836092+0.000443j
[2025-09-12 08:53:47] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -51.779208+0.012230j
[2025-09-12 08:54:02] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -51.849637+0.000751j
[2025-09-12 08:54:17] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -51.670959-0.001675j
[2025-09-12 08:54:33] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -51.635175+0.000022j
[2025-09-12 08:54:48] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -51.657924+0.000686j
[2025-09-12 08:55:03] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -51.715313+0.000105j
[2025-09-12 08:55:18] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -51.875680-0.000409j
[2025-09-12 08:55:33] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -51.748618+0.001628j
[2025-09-12 08:55:48] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -51.796512+0.000582j
[2025-09-12 08:56:04] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -51.779069+0.003260j
[2025-09-12 08:56:19] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -51.773904+0.000254j
[2025-09-12 08:56:34] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -51.855276+0.000605j
[2025-09-12 08:56:34] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-12 08:56:49] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -51.784278-0.000812j
[2025-09-12 08:57:05] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -51.683868-0.000242j
[2025-09-12 08:57:20] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -51.853497-0.000660j
[2025-09-12 08:57:35] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -51.703332+0.001347j
[2025-09-12 08:57:50] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -51.814860-0.001157j
[2025-09-12 08:58:05] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -51.710486+0.001514j
[2025-09-12 08:58:21] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -51.692378+0.001416j
[2025-09-12 08:58:36] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -51.782714-0.002917j
[2025-09-12 08:58:51] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -51.776338-0.000437j
[2025-09-12 08:59:06] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -51.775807-0.000935j
[2025-09-12 08:59:21] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -51.724677-0.000103j
[2025-09-12 08:59:37] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -51.656734+0.000652j
[2025-09-12 08:59:52] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -51.684494+0.000483j
[2025-09-12 09:00:07] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -51.761508-0.000545j
[2025-09-12 09:00:22] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -51.695523-0.002448j
[2025-09-12 09:00:38] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -51.634836+0.001019j
[2025-09-12 09:00:53] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -51.631366+0.001948j
[2025-09-12 09:01:08] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -51.615499+0.001885j
[2025-09-12 09:01:23] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -51.690032+0.000769j
[2025-09-12 09:01:39] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -51.698733-0.001274j
[2025-09-12 09:01:54] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -51.745999+0.000965j
[2025-09-12 09:02:09] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -51.770699+0.000010j
[2025-09-12 09:02:24] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -51.767598-0.001392j
[2025-09-12 09:02:39] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -51.772537-0.000637j
[2025-09-12 09:02:55] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -51.758962+0.002463j
[2025-09-12 09:03:10] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -51.618636+0.000233j
[2025-09-12 09:03:25] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -51.699219+0.000445j
[2025-09-12 09:03:40] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -51.759228+0.000556j
[2025-09-12 09:03:56] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -51.737245-0.002621j
[2025-09-12 09:04:11] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -51.659461-0.000673j
[2025-09-12 09:04:26] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -51.624172-0.001199j
[2025-09-12 09:04:41] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -51.826946+0.000221j
[2025-09-12 09:04:56] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -51.858872-0.001135j
[2025-09-12 09:05:12] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -51.795170-0.000853j
[2025-09-12 09:05:27] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -51.673429+0.000137j
[2025-09-12 09:05:42] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -51.650278+0.002991j
[2025-09-12 09:05:57] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -51.704706+0.000885j
[2025-09-12 09:06:12] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -51.707743+0.001678j
[2025-09-12 09:06:28] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -51.747417-0.000879j
[2025-09-12 09:06:43] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -51.685046-0.002944j
[2025-09-12 09:06:58] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -51.686859-0.000186j
[2025-09-12 09:07:13] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -51.630669-0.000507j
[2025-09-12 09:07:28] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -51.598791-0.002385j
[2025-09-12 09:07:44] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -51.688502+0.001645j
[2025-09-12 09:07:59] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -51.663518-0.002199j
[2025-09-12 09:08:14] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -51.681338-0.001064j
[2025-09-12 09:08:29] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -51.569282+0.001732j
[2025-09-12 09:08:44] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -51.700810-0.000447j
[2025-09-12 09:09:00] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -51.692778-0.001611j
[2025-09-12 09:09:15] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -51.740320-0.000473j
[2025-09-12 09:09:30] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -51.687544-0.000169j
[2025-09-12 09:09:45] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -51.739892+0.001277j
[2025-09-12 09:09:56] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -51.721522-0.002608j
[2025-09-12 09:10:06] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -51.783494-0.000828j
[2025-09-12 09:10:16] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -51.834480+0.001778j
[2025-09-12 09:10:26] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -51.769529-0.000138j
[2025-09-12 09:10:37] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -51.764682-0.002078j
[2025-09-12 09:10:47] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -51.708569+0.000745j
[2025-09-12 09:10:57] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -51.735680-0.001325j
[2025-09-12 09:11:12] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -51.790154+0.000531j
[2025-09-12 09:11:27] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -51.777946-0.000153j
[2025-09-12 09:11:42] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -51.597768+0.002664j
[2025-09-12 09:11:57] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -51.659359-0.001416j
[2025-09-12 09:12:13] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -51.731679-0.002680j
[2025-09-12 09:12:28] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -51.753619+0.001091j
[2025-09-12 09:12:43] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -51.819519+0.000242j
[2025-09-12 09:12:58] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -51.866310-0.000968j
[2025-09-12 09:13:14] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -51.733615-0.001800j
[2025-09-12 09:13:29] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -51.796692-0.001130j
[2025-09-12 09:13:44] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -51.701091+0.001582j
[2025-09-12 09:14:00] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -51.739351+0.003398j
[2025-09-12 09:14:15] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -51.669019+0.001868j
[2025-09-12 09:14:30] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -51.740128+0.001395j
[2025-09-12 09:14:46] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -51.682203-0.002671j
[2025-09-12 09:15:01] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -51.628686-0.000256j
[2025-09-12 09:15:16] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -51.710399+0.001289j
[2025-09-12 09:15:31] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -51.647462-0.000094j
[2025-09-12 09:15:47] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -51.616211-0.001887j
[2025-09-12 09:16:02] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -51.643510-0.002569j
[2025-09-12 09:16:17] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -51.552784-0.000662j
[2025-09-12 09:16:33] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -51.568454+0.002129j
[2025-09-12 09:16:48] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -51.611393-0.001653j
[2025-09-12 09:17:03] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -51.712796-0.000275j
[2025-09-12 09:17:19] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -51.547643-0.000531j
[2025-09-12 09:17:34] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -51.486696+0.000395j
[2025-09-12 09:17:49] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -51.621463-0.001711j
[2025-09-12 09:18:04] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -51.609226-0.002283j
[2025-09-12 09:18:20] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -51.687230+0.001570j
[2025-09-12 09:18:35] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -51.743812+0.002205j
[2025-09-12 09:18:50] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -51.763930+0.001392j
[2025-09-12 09:19:05] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -51.661468+0.001994j
[2025-09-12 09:19:21] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -51.719799-0.000206j
[2025-09-12 09:19:36] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -51.705280-0.001033j
[2025-09-12 09:19:51] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -51.722426-0.000903j
[2025-09-12 09:20:07] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -51.717546-0.003523j
[2025-09-12 09:20:22] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -51.676021-0.000396j
[2025-09-12 09:20:34] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -51.673736-0.000359j
[2025-09-12 09:20:46] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -51.801012+0.000817j
[2025-09-12 09:20:58] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -51.814310+0.001077j
[2025-09-12 09:21:10] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -51.823763-0.001861j
[2025-09-12 09:21:25] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -51.728787-0.000979j
[2025-09-12 09:21:41] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -51.677502-0.002039j
[2025-09-12 09:21:56] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -51.752640+0.006534j
[2025-09-12 09:22:11] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -51.708710+0.001152j
[2025-09-12 09:22:26] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -51.797149-0.001289j
[2025-09-12 09:22:26] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-12 09:22:26] ✅ Training completed | Restarts: 2
[2025-09-12 09:22:26] ============================================================
[2025-09-12 09:22:26] Training completed | Runtime: 15872.8s
[2025-09-12 09:22:33] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-12 09:22:33] ============================================================
