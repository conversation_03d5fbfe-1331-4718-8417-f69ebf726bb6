[2025-09-13 02:18:34] 使用checkpoint文件: results/L=4/J2=0.03/J1=0.03/training/checkpoints/checkpoint_iter_001050.pkl
[2025-09-13 02:18:51] ✓ 从checkpoint加载参数: 1050
[2025-09-13 02:18:51]   - 能量: -52.466196+0.001200j ± 0.085244
[2025-09-13 02:18:51] ================================================================================
[2025-09-13 02:18:51] 加载量子态: L=4, J2=0.03, J1=0.03, checkpoint=checkpoint_iter_001050
[2025-09-13 02:18:51] 使用采样数目: 1048576
[2025-09-13 02:18:51] 设置样本数为: 1048576
[2025-09-13 02:18:51] 开始生成共享样本集...
[2025-09-13 02:21:53] 样本生成完成,耗时: 181.799 秒
[2025-09-13 02:21:53] ================================================================================
[2025-09-13 02:21:53] 开始计算自旋结构因子...
[2025-09-13 02:21:53] 初始化操作符缓存...
[2025-09-13 02:21:53] 预构建所有自旋相关操作符...
[2025-09-13 02:21:53] 开始计算自旋相关函数...
[2025-09-13 02:22:07] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 14.294s
[2025-09-13 02:22:25] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 17.751s
[2025-09-13 02:22:34] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.328s
[2025-09-13 02:22:43] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.334s
[2025-09-13 02:22:53] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.338s
[2025-09-13 02:23:02] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.338s
[2025-09-13 02:23:12] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.335s
[2025-09-13 02:23:21] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.324s
[2025-09-13 02:23:30] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.327s
[2025-09-13 02:23:40] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.339s
[2025-09-13 02:23:49] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.328s
[2025-09-13 02:23:58] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.330s
[2025-09-13 02:24:08] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.355s
[2025-09-13 02:24:17] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.258s
[2025-09-13 02:24:26] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.339s
[2025-09-13 02:24:36] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.328s
[2025-09-13 02:24:45] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.341s
[2025-09-13 02:24:54] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.345s
[2025-09-13 02:25:04] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.347s
[2025-09-13 02:25:13] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.334s
[2025-09-13 02:25:22] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.350s
[2025-09-13 02:25:32] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.335s
[2025-09-13 02:25:41] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.336s
[2025-09-13 02:25:50] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.398s
[2025-09-13 02:26:00] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.257s
[2025-09-13 02:26:09] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.332s
[2025-09-13 02:26:18] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.318s
[2025-09-13 02:26:28] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.339s
[2025-09-13 02:26:37] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.320s
[2025-09-13 02:26:46] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.326s
[2025-09-13 02:26:56] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.328s
[2025-09-13 02:27:05] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.339s
[2025-09-13 02:27:14] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.337s
[2025-09-13 02:27:24] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.328s
[2025-09-13 02:27:33] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.329s
[2025-09-13 02:27:42] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.347s
[2025-09-13 02:27:52] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.338s
[2025-09-13 02:28:01] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.341s
[2025-09-13 02:28:10] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.333s
[2025-09-13 02:28:20] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.333s
[2025-09-13 02:28:29] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.332s
[2025-09-13 02:28:38] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.322s
[2025-09-13 02:28:48] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.316s
[2025-09-13 02:28:57] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.340s
[2025-09-13 02:29:06] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.347s
[2025-09-13 02:29:16] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.331s
[2025-09-13 02:29:25] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.335s
[2025-09-13 02:29:34] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.323s
[2025-09-13 02:29:44] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.336s
[2025-09-13 02:29:53] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.337s
[2025-09-13 02:30:02] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.324s
[2025-09-13 02:30:12] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.326s
[2025-09-13 02:30:21] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.332s
[2025-09-13 02:30:30] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.334s
[2025-09-13 02:30:40] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.329s
[2025-09-13 02:30:49] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.337s
[2025-09-13 02:30:58] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.327s
[2025-09-13 02:31:08] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.321s
[2025-09-13 02:31:17] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.337s
[2025-09-13 02:31:27] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.340s
[2025-09-13 02:31:36] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.341s
[2025-09-13 02:31:45] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.312s
[2025-09-13 02:31:55] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.344s
[2025-09-13 02:32:04] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.334s
[2025-09-13 02:32:04] 自旋相关函数计算完成,总耗时 611.14 秒
[2025-09-13 02:32:05] 计算傅里叶变换...
[2025-09-13 02:32:07] 自旋结构因子计算完成
[2025-09-13 02:32:09] 自旋相关函数平均误差: 0.000672
