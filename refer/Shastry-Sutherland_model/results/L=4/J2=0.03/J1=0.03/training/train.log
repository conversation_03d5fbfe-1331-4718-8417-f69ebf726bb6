[2025-09-11 20:03:31] ✓ 从checkpoint恢复: results/L=4/J2=0.03/J1=0.04/training/checkpoints/final_GCNN.pkl
[2025-09-11 20:03:31]   - 迭代次数: final
[2025-09-11 20:03:31]   - 能量: -52.767313+0.001685j ± 0.084051
[2025-09-11 20:03:31]   - 时间戳: 2025-09-11T20:01:38.247370+08:00
[2025-09-11 20:03:57] ✓ 变分状态参数已从checkpoint恢复
[2025-09-11 20:03:57] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-11 20:03:57] ==================================================
[2025-09-11 20:03:57] GCNN for Shastry-Sutherland Model
[2025-09-11 20:03:57] ==================================================
[2025-09-11 20:03:57] System parameters:
[2025-09-11 20:03:57]   - System size: L=4, N=64
[2025-09-11 20:03:57]   - System parameters: J1=0.03, J2=0.03, Q=0.97
[2025-09-11 20:03:57] --------------------------------------------------
[2025-09-11 20:03:57] Model parameters:
[2025-09-11 20:03:57]   - Number of layers = 4
[2025-09-11 20:03:57]   - Number of features = 4
[2025-09-11 20:03:57]   - Total parameters = 12572
[2025-09-11 20:03:57] --------------------------------------------------
[2025-09-11 20:03:57] Training parameters:
[2025-09-11 20:03:57]   - Learning rate: 0.015
[2025-09-11 20:03:57]   - Total iterations: 1050
[2025-09-11 20:03:57]   - Annealing cycles: 3
[2025-09-11 20:03:57]   - Initial period: 150
[2025-09-11 20:03:57]   - Period multiplier: 2.0
[2025-09-11 20:03:57]   - Temperature range: 0.0-1.0
[2025-09-11 20:03:57]   - Samples: 4096
[2025-09-11 20:03:57]   - Discarded samples: 0
[2025-09-11 20:03:57]   - Chunk size: 2048
[2025-09-11 20:03:57]   - Diagonal shift: 0.2
[2025-09-11 20:03:57]   - Gradient clipping: 1.0
[2025-09-11 20:03:57]   - Checkpoint enabled: interval=105
[2025-09-11 20:03:57]   - Checkpoint directory: results/L=4/J2=0.03/J1=0.03/training/checkpoints
[2025-09-11 20:03:57] --------------------------------------------------
[2025-09-11 20:03:57] Device status:
[2025-09-11 20:03:57]   - Devices model: NVIDIA H200 NVL
[2025-09-11 20:03:57]   - Number of devices: 1
[2025-09-11 20:03:57]   - Sharding: True
[2025-09-11 20:03:57] ============================================================
[2025-09-11 20:05:50] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -52.737590+0.000227j
[2025-09-11 20:06:58] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -52.658857-0.002155j
[2025-09-11 20:07:14] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -52.547786+0.001540j
[2025-09-11 20:07:29] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -52.472374+0.003193j
[2025-09-11 20:07:44] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -52.528697+0.001181j
[2025-09-11 20:08:00] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -52.650594+0.001615j
[2025-09-11 20:08:15] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -52.520212-0.000121j
[2025-09-11 20:08:30] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -52.533107+0.000870j
[2025-09-11 20:08:46] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -52.499570-0.003193j
[2025-09-11 20:09:01] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -52.534119-0.001376j
[2025-09-11 20:09:16] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -52.571151-0.001971j
[2025-09-11 20:09:32] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -52.472547-0.002011j
[2025-09-11 20:09:47] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -52.460371-0.000315j
[2025-09-11 20:10:02] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -52.482229-0.002822j
[2025-09-11 20:10:18] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -52.581408-0.000299j
[2025-09-11 20:10:33] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -52.543581-0.005241j
[2025-09-11 20:10:48] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -52.457951+0.000005j
[2025-09-11 20:11:04] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -52.391906-0.001760j
[2025-09-11 20:11:19] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -52.406252-0.000211j
[2025-09-11 20:11:34] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -52.459422-0.000731j
[2025-09-11 20:11:50] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -52.411746-0.000835j
[2025-09-11 20:12:05] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -52.268055+0.001702j
[2025-09-11 20:12:20] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -52.426735-0.000267j
[2025-09-11 20:12:36] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -52.448971+0.001855j
[2025-09-11 20:12:51] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -52.483643-0.001837j
[2025-09-11 20:13:06] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -52.427185-0.002872j
[2025-09-11 20:13:22] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -52.524552+0.001200j
[2025-09-11 20:13:37] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -52.414272+0.003256j
[2025-09-11 20:13:52] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -52.433422-0.004692j
[2025-09-11 20:14:08] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -52.475150+0.000013j
[2025-09-11 20:14:23] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -52.496118+0.004617j
[2025-09-11 20:14:38] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -52.373899+0.000528j
[2025-09-11 20:14:54] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -52.407833+0.000347j
[2025-09-11 20:15:09] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -52.425174-0.005769j
[2025-09-11 20:15:24] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -52.452520+0.004447j
[2025-09-11 20:15:40] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -52.458818+0.001672j
[2025-09-11 20:15:55] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -52.379064+0.002099j
[2025-09-11 20:16:11] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -52.387458-0.000139j
[2025-09-11 20:16:26] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -52.436139+0.002003j
[2025-09-11 20:16:41] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -52.356390+0.000963j
[2025-09-11 20:16:57] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -52.529988+0.002835j
[2025-09-11 20:17:12] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -52.422894-0.001194j
[2025-09-11 20:17:27] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -52.560092+0.002067j
[2025-09-11 20:17:43] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -52.615580+0.000911j
[2025-09-11 20:17:58] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -52.646353+0.000404j
[2025-09-11 20:18:14] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -52.580167+0.001166j
[2025-09-11 20:18:29] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -52.533778+0.003218j
[2025-09-11 20:18:44] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -52.479409-0.001707j
[2025-09-11 20:19:00] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -52.500889+0.000889j
[2025-09-11 20:19:15] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -52.483849+0.000610j
[2025-09-11 20:19:30] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -52.507836+0.000548j
[2025-09-11 20:19:46] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -52.461204+0.001695j
[2025-09-11 20:20:01] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -52.473286-0.001019j
[2025-09-11 20:20:16] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -52.496015+0.000135j
[2025-09-11 20:20:32] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -52.593713-0.001339j
[2025-09-11 20:20:47] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -52.672590+0.002773j
[2025-09-11 20:21:02] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -52.573833+0.002032j
[2025-09-11 20:21:18] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -52.560478+0.001543j
[2025-09-11 20:21:33] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -52.641741+0.001095j
[2025-09-11 20:21:48] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -52.610536-0.002408j
[2025-09-11 20:22:04] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -52.566615-0.000288j
[2025-09-11 20:22:19] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -52.551294+0.003031j
[2025-09-11 20:22:34] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -52.643222+0.003472j
[2025-09-11 20:22:50] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -52.600583+0.002834j
[2025-09-11 20:23:05] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -52.624586-0.003522j
[2025-09-11 20:23:20] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -52.560004+0.001336j
[2025-09-11 20:23:36] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -52.549311+0.000580j
[2025-09-11 20:23:51] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -52.443231-0.000821j
[2025-09-11 20:24:07] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -52.568504-0.002883j
[2025-09-11 20:24:22] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -52.472810-0.005697j
[2025-09-11 20:24:37] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -52.540902-0.003597j
[2025-09-11 20:24:53] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -52.573658-0.002489j
[2025-09-11 20:25:08] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -52.511793-0.000249j
[2025-09-11 20:25:23] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -52.645125-0.000334j
[2025-09-11 20:25:39] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -52.537920+0.004195j
[2025-09-11 20:25:54] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -52.418129-0.000669j
[2025-09-11 20:26:10] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -52.501473+0.001159j
[2025-09-11 20:26:25] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -52.555885-0.002093j
[2025-09-11 20:26:40] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -52.529303+0.001451j
[2025-09-11 20:26:56] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -52.621015-0.002657j
[2025-09-11 20:27:11] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -52.602162+0.000528j
[2025-09-11 20:27:26] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -52.626049-0.001047j
[2025-09-11 20:27:42] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -52.571339-0.003710j
[2025-09-11 20:27:57] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -52.600107+0.004908j
[2025-09-11 20:28:13] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -52.613055+0.002749j
[2025-09-11 20:28:28] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -52.526090+0.000000j
[2025-09-11 20:28:43] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -52.344789-0.001199j
[2025-09-11 20:28:59] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -52.372378+0.000378j
[2025-09-11 20:29:14] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -52.346204-0.001685j
[2025-09-11 20:29:29] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -52.441578-0.001116j
[2025-09-11 20:29:45] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -52.469660-0.002747j
[2025-09-11 20:30:00] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -52.494563+0.002493j
[2025-09-11 20:30:15] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -52.449767+0.002623j
[2025-09-11 20:30:31] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -52.446240+0.000559j
[2025-09-11 20:30:46] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -52.336117+0.000899j
[2025-09-11 20:31:01] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -52.425447-0.001321j
[2025-09-11 20:31:17] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -52.634532+0.001933j
[2025-09-11 20:31:32] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -52.567725-0.000584j
[2025-09-11 20:31:47] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -52.548901-0.000125j
[2025-09-11 20:32:03] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -52.479890-0.003371j
[2025-09-11 20:32:18] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -52.447202-0.001040j
[2025-09-11 20:32:34] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -52.510037+0.004302j
[2025-09-11 20:32:49] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -52.510794-0.001735j
[2025-09-11 20:33:04] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -52.631173+0.000307j
[2025-09-11 20:33:20] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -52.553123-0.002825j
[2025-09-11 20:33:20] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-11 20:33:35] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -52.506713+0.000527j
[2025-09-11 20:33:50] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -52.473159-0.002280j
[2025-09-11 20:34:06] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -52.353377-0.002334j
[2025-09-11 20:34:21] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -52.400220+0.000771j
[2025-09-11 20:34:36] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -52.479599+0.000762j
[2025-09-11 20:34:52] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -52.575506+0.000995j
[2025-09-11 20:35:07] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -52.518474+0.000253j
[2025-09-11 20:35:22] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -52.571872-0.004481j
[2025-09-11 20:35:38] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -52.561688+0.003348j
[2025-09-11 20:35:53] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -52.539882+0.001945j
[2025-09-11 20:36:08] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -52.700400-0.003506j
[2025-09-11 20:36:24] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -52.504456+0.001504j
[2025-09-11 20:36:39] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -52.533252+0.001955j
[2025-09-11 20:36:54] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -52.594842-0.000603j
[2025-09-11 20:37:10] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -52.724862-0.001110j
[2025-09-11 20:37:25] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -52.681562+0.000192j
[2025-09-11 20:37:40] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -52.558291+0.001688j
[2025-09-11 20:37:56] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -52.408450+0.001336j
[2025-09-11 20:38:11] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -52.357567+0.002494j
[2025-09-11 20:38:26] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -52.470987+0.000298j
[2025-09-11 20:38:40] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -52.402662-0.000617j
[2025-09-11 20:38:50] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -52.412202+0.000957j
[2025-09-11 20:39:00] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -52.563245-0.002608j
[2025-09-11 20:39:11] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -52.541864-0.002733j
[2025-09-11 20:39:21] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -52.477890+0.001722j
[2025-09-11 20:39:31] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -52.579990-0.003819j
[2025-09-11 20:39:43] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -52.551754-0.001737j
[2025-09-11 20:39:58] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -52.504085-0.000345j
[2025-09-11 20:40:13] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -52.537080+0.005855j
[2025-09-11 20:40:29] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -52.514549-0.002369j
[2025-09-11 20:40:44] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -52.545144-0.003401j
[2025-09-11 20:41:00] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -52.457813-0.000368j
[2025-09-11 20:41:15] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -52.541689+0.000883j
[2025-09-11 20:41:30] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -52.624941-0.003126j
[2025-09-11 20:41:46] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -52.657721-0.000712j
[2025-09-11 20:42:01] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -52.647223-0.001823j
[2025-09-11 20:42:17] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -52.512429-0.003354j
[2025-09-11 20:42:32] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -52.386288-0.003933j
[2025-09-11 20:42:47] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -52.418437-0.002651j
[2025-09-11 20:43:03] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -52.500816-0.002750j
[2025-09-11 20:43:18] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -52.514830+0.003661j
[2025-09-11 20:43:33] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -52.464449-0.001448j
[2025-09-11 20:43:49] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -52.486599+0.001270j
[2025-09-11 20:44:04] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -52.465927+0.002980j
[2025-09-11 20:44:20] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -52.525975-0.002165j
[2025-09-11 20:44:20] RESTART #1 | Period: 300
[2025-09-11 20:44:35] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -52.574488+0.000331j
[2025-09-11 20:44:50] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -52.467513-0.002923j
[2025-09-11 20:45:06] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -52.544724-0.003734j
[2025-09-11 20:45:21] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -52.441954+0.001382j
[2025-09-11 20:45:37] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -52.440041+0.002518j
[2025-09-11 20:45:52] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -52.381777-0.000375j
[2025-09-11 20:46:07] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -52.447281+0.002193j
[2025-09-11 20:46:23] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -52.519492-0.001257j
[2025-09-11 20:46:38] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -52.562974-0.000264j
[2025-09-11 20:46:54] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -52.452228-0.001070j
[2025-09-11 20:47:09] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -52.501086-0.001209j
[2025-09-11 20:47:24] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -52.548345-0.002161j
[2025-09-11 20:47:40] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -52.458095-0.001129j
[2025-09-11 20:47:55] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -52.573737+0.000793j
[2025-09-11 20:48:11] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -52.465480-0.001007j
[2025-09-11 20:48:26] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -52.657333-0.000990j
[2025-09-11 20:48:41] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -52.557188-0.002343j
[2025-09-11 20:48:57] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -52.403618-0.001643j
[2025-09-11 20:49:11] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -52.458499+0.002876j
[2025-09-11 20:49:22] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -52.392544-0.000078j
[2025-09-11 20:49:36] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -52.407604+0.000741j
[2025-09-11 20:49:46] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -52.409319+0.001272j
[2025-09-11 20:50:00] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -52.293517+0.003118j
[2025-09-11 20:50:16] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -52.414264-0.000736j
[2025-09-11 20:50:31] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -52.463472+0.000005j
[2025-09-11 20:50:47] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -52.406462+0.001240j
[2025-09-11 20:51:02] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -52.495828+0.000143j
[2025-09-11 20:51:17] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -52.417311-0.000689j
[2025-09-11 20:51:33] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -52.544415-0.004188j
[2025-09-11 20:51:48] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -52.416301-0.000896j
[2025-09-11 20:52:03] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -52.561549-0.001578j
[2025-09-11 20:52:19] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -52.435456+0.002979j
[2025-09-11 20:52:34] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -52.444970+0.002021j
[2025-09-11 20:52:49] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -52.448062-0.001803j
[2025-09-11 20:53:05] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -52.486764+0.001149j
[2025-09-11 20:53:20] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -52.489954-0.002837j
[2025-09-11 20:53:35] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -52.400218-0.001902j
[2025-09-11 20:53:51] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -52.405239+0.001476j
[2025-09-11 20:54:06] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -52.410335+0.003200j
[2025-09-11 20:54:21] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -52.523911+0.002438j
[2025-09-11 20:54:37] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -52.485952-0.001664j
[2025-09-11 20:54:52] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -52.665548-0.001137j
[2025-09-11 20:55:08] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -52.609243+0.000066j
[2025-09-11 20:55:23] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -52.699930-0.000845j
[2025-09-11 20:55:38] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -52.621436-0.002139j
[2025-09-11 20:55:54] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -52.488796-0.000313j
[2025-09-11 20:56:09] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -52.519256-0.000580j
[2025-09-11 20:56:24] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -52.561037+0.003257j
[2025-09-11 20:56:40] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -52.408812-0.000436j
[2025-09-11 20:56:55] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -52.527374-0.000354j
[2025-09-11 20:57:10] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -52.534206+0.001696j
[2025-09-11 20:57:26] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -52.509147-0.000763j
[2025-09-11 20:57:41] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -52.466354-0.000522j
[2025-09-11 20:57:56] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -52.435395-0.001780j
[2025-09-11 20:58:12] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -52.552709-0.002625j
[2025-09-11 20:58:27] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -52.455904+0.002326j
[2025-09-11 20:58:42] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -52.504014-0.001216j
[2025-09-11 20:58:58] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -52.533130-0.001213j
[2025-09-11 20:59:13] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -52.464943+0.001483j
[2025-09-11 20:59:29] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -52.439514+0.001242j
[2025-09-11 20:59:29] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-11 20:59:44] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -52.415782-0.000015j
[2025-09-11 20:59:59] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -52.492732+0.001685j
[2025-09-11 21:00:15] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -52.423335+0.002999j
[2025-09-11 21:00:30] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -52.404419+0.001881j
[2025-09-11 21:00:45] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -52.360218+0.001434j
[2025-09-11 21:01:01] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -52.403878+0.000869j
[2025-09-11 21:01:16] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -52.374580+0.001024j
[2025-09-11 21:01:31] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -52.538890+0.000039j
[2025-09-11 21:01:47] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -52.456948+0.000328j
[2025-09-11 21:02:02] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -52.477084+0.003116j
[2025-09-11 21:02:17] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -52.478500-0.000008j
[2025-09-11 21:02:33] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -52.534101+0.001395j
[2025-09-11 21:02:48] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -52.457331-0.000716j
[2025-09-11 21:03:03] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -52.478472-0.002235j
[2025-09-11 21:03:19] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -52.498791-0.003677j
[2025-09-11 21:03:34] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -52.525705-0.000061j
[2025-09-11 21:03:49] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -52.559028+0.003067j
[2025-09-11 21:04:05] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -52.434678+0.002123j
[2025-09-11 21:04:20] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -52.407716-0.002865j
[2025-09-11 21:04:35] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -52.495368-0.000081j
[2025-09-11 21:04:51] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -52.402296+0.001220j
[2025-09-11 21:05:06] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -52.464288+0.001106j
[2025-09-11 21:05:22] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -52.430795+0.003339j
[2025-09-11 21:05:37] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -52.512028+0.001275j
[2025-09-11 21:05:52] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -52.474556-0.001206j
[2025-09-11 21:06:08] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -52.390116-0.002664j
[2025-09-11 21:06:23] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -52.407468-0.000707j
[2025-09-11 21:06:38] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -52.480758+0.002317j
[2025-09-11 21:06:54] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -52.454916+0.001194j
[2025-09-11 21:07:09] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -52.511218-0.002417j
[2025-09-11 21:07:24] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -52.605989+0.002001j
[2025-09-11 21:07:40] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -52.658900-0.002243j
[2025-09-11 21:07:55] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -52.568973+0.000370j
[2025-09-11 21:08:10] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -52.569109-0.000995j
[2025-09-11 21:08:26] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -52.720169+0.000364j
[2025-09-11 21:08:41] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -52.636901-0.002374j
[2025-09-11 21:08:57] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -52.487245-0.002193j
[2025-09-11 21:09:12] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -52.496084-0.000059j
[2025-09-11 21:09:28] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -52.357844-0.000747j
[2025-09-11 21:09:43] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -52.398055+0.001562j
[2025-09-11 21:09:58] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -52.468599-0.003440j
[2025-09-11 21:10:14] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -52.532294-0.002009j
[2025-09-11 21:10:29] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -52.560400+0.001875j
[2025-09-11 21:10:44] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -52.359783+0.002555j
[2025-09-11 21:11:00] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -52.463113+0.002296j
[2025-09-11 21:11:15] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -52.426470+0.001389j
[2025-09-11 21:11:30] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -52.472080-0.000682j
[2025-09-11 21:11:46] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -52.380872+0.001258j
[2025-09-11 21:12:01] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -52.382611-0.000519j
[2025-09-11 21:12:16] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -52.486084+0.001311j
[2025-09-11 21:12:32] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -52.466052-0.002301j
[2025-09-11 21:12:47] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -52.572002-0.000872j
[2025-09-11 21:13:02] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -52.570624+0.003952j
[2025-09-11 21:13:18] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -52.550050+0.002869j
[2025-09-11 21:13:33] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -52.634874+0.001919j
[2025-09-11 21:13:49] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -52.458741+0.000760j
[2025-09-11 21:14:04] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -52.475628+0.001114j
[2025-09-11 21:14:19] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -52.544831+0.002930j
[2025-09-11 21:14:35] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -52.674313-0.003546j
[2025-09-11 21:14:50] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -52.476515+0.001169j
[2025-09-11 21:15:05] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -52.555006+0.001448j
[2025-09-11 21:15:21] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -52.454080-0.001095j
[2025-09-11 21:15:36] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -52.441432-0.000940j
[2025-09-11 21:15:51] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -52.504746+0.005054j
[2025-09-11 21:16:07] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -52.311660+0.000567j
[2025-09-11 21:16:22] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -52.398712+0.004650j
[2025-09-11 21:16:38] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -52.415070+0.000639j
[2025-09-11 21:16:53] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -52.330992-0.000453j
[2025-09-11 21:17:08] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -52.358666+0.003411j
[2025-09-11 21:17:24] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -52.426921+0.002631j
[2025-09-11 21:17:39] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -52.496673-0.001310j
[2025-09-11 21:17:55] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -52.424265+0.000398j
[2025-09-11 21:18:10] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -52.473747-0.002261j
[2025-09-11 21:18:25] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -52.505393+0.003729j
[2025-09-11 21:18:41] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -52.445116+0.004679j
[2025-09-11 21:18:56] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -52.435064-0.002584j
[2025-09-11 21:19:11] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -52.471434-0.004614j
[2025-09-11 21:19:27] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -52.318228-0.000451j
[2025-09-11 21:19:42] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -52.351312+0.001175j
[2025-09-11 21:19:58] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -52.490319-0.000067j
[2025-09-11 21:20:13] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -52.376707+0.001816j
[2025-09-11 21:20:28] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -52.333976+0.004794j
[2025-09-11 21:20:44] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -52.491235-0.000764j
[2025-09-11 21:20:59] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -52.488420+0.001291j
[2025-09-11 21:21:14] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -52.529806+0.003803j
[2025-09-11 21:21:30] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -52.447588+0.000441j
[2025-09-11 21:21:45] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -52.438690-0.001764j
[2025-09-11 21:22:00] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -52.469075+0.000884j
[2025-09-11 21:22:16] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -52.463629+0.001221j
[2025-09-11 21:22:31] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -52.492765+0.001341j
[2025-09-11 21:22:46] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -52.648309+0.000393j
[2025-09-11 21:23:02] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -52.554604+0.001082j
[2025-09-11 21:23:17] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -52.574446-0.000828j
[2025-09-11 21:23:27] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -52.621577+0.003767j
[2025-09-11 21:23:37] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -52.634194+0.003157j
[2025-09-11 21:23:48] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -52.557199-0.000851j
[2025-09-11 21:23:58] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -52.477388+0.000322j
[2025-09-11 21:24:08] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -52.386746+0.001516j
[2025-09-11 21:24:18] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -52.427080+0.001908j
[2025-09-11 21:24:32] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -52.533083+0.000391j
[2025-09-11 21:24:47] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -52.594733-0.003267j
[2025-09-11 21:25:02] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -52.554225-0.001465j
[2025-09-11 21:25:18] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -52.618122+0.003195j
[2025-09-11 21:25:33] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -52.478879-0.000638j
[2025-09-11 21:25:49] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -52.391060+0.001243j
[2025-09-11 21:25:49] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-11 21:26:04] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -52.386093+0.001727j
[2025-09-11 21:26:19] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -52.536037+0.003314j
[2025-09-11 21:26:35] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -52.537698-0.002570j
[2025-09-11 21:26:50] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -52.535400-0.000484j
[2025-09-11 21:27:06] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -52.508394+0.001176j
[2025-09-11 21:27:21] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -52.488350+0.002248j
[2025-09-11 21:27:37] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -52.518524+0.002273j
[2025-09-11 21:27:52] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -52.503585-0.001698j
[2025-09-11 21:28:07] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -52.554839-0.000601j
[2025-09-11 21:28:23] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -52.576702-0.001883j
[2025-09-11 21:28:38] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -52.553648+0.001401j
[2025-09-11 21:28:53] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -52.490962-0.000933j
[2025-09-11 21:29:09] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -52.451108+0.003772j
[2025-09-11 21:29:24] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -52.481680+0.001238j
[2025-09-11 21:29:40] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -52.547606+0.000574j
[2025-09-11 21:29:55] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -52.610171+0.001642j
[2025-09-11 21:30:11] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -52.535998+0.000911j
[2025-09-11 21:30:26] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -52.625268-0.000035j
[2025-09-11 21:30:41] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -52.702154+0.000240j
[2025-09-11 21:30:57] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -52.517636-0.000268j
[2025-09-11 21:31:12] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -52.468273+0.000453j
[2025-09-11 21:31:28] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -52.366698-0.006500j
[2025-09-11 21:31:43] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -52.497116-0.000650j
[2025-09-11 21:31:58] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -52.444075+0.000893j
[2025-09-11 21:32:14] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -52.549106-0.004379j
[2025-09-11 21:32:29] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -52.496734-0.004794j
[2025-09-11 21:32:45] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -52.588924-0.002010j
[2025-09-11 21:33:00] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -52.613330+0.002189j
[2025-09-11 21:33:15] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -52.652806+0.001294j
[2025-09-11 21:33:31] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -52.699220+0.005851j
[2025-09-11 21:33:46] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -52.588626-0.001617j
[2025-09-11 21:34:00] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -52.611666-0.000138j
[2025-09-11 21:34:10] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -52.403236+0.002497j
[2025-09-11 21:34:24] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -52.516318-0.001213j
[2025-09-11 21:34:35] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -52.470188+0.001913j
[2025-09-11 21:34:50] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -52.539424-0.002420j
[2025-09-11 21:35:05] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -52.562075+0.000130j
[2025-09-11 21:35:20] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -52.426805+0.005013j
[2025-09-11 21:35:36] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -52.398512+0.003861j
[2025-09-11 21:35:51] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -52.573272-0.000767j
[2025-09-11 21:36:06] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -52.525304+0.001651j
[2025-09-11 21:36:22] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -52.665759+0.002713j
[2025-09-11 21:36:37] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -52.490435+0.002841j
[2025-09-11 21:36:52] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -52.464504-0.002801j
[2025-09-11 21:37:08] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -52.472862-0.000474j
[2025-09-11 21:37:23] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -52.218276-0.001004j
[2025-09-11 21:37:39] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -52.372605+0.002245j
[2025-09-11 21:37:54] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -52.203782-0.000049j
[2025-09-11 21:38:09] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -52.531272+0.001863j
[2025-09-11 21:38:25] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -52.379024+0.001826j
[2025-09-11 21:38:40] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -52.365242-0.000985j
[2025-09-11 21:38:55] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -52.459004+0.001535j
[2025-09-11 21:39:11] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -52.402059-0.000917j
[2025-09-11 21:39:26] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -52.479157+0.000330j
[2025-09-11 21:39:42] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -52.677798+0.000717j
[2025-09-11 21:39:57] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -52.517893+0.000655j
[2025-09-11 21:40:12] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -52.655340+0.002969j
[2025-09-11 21:40:28] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -52.576821+0.003322j
[2025-09-11 21:40:43] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -52.575736+0.003632j
[2025-09-11 21:40:58] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -52.529995-0.000898j
[2025-09-11 21:41:14] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -52.465407-0.000520j
[2025-09-11 21:41:29] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -52.552781+0.000744j
[2025-09-11 21:41:45] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -52.537154+0.001177j
[2025-09-11 21:42:00] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -52.519150+0.002388j
[2025-09-11 21:42:15] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -52.495722+0.000901j
[2025-09-11 21:42:31] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -52.430718-0.000597j
[2025-09-11 21:42:46] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -52.536321+0.006342j
[2025-09-11 21:43:01] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -52.478967+0.000189j
[2025-09-11 21:43:17] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -52.662847-0.001860j
[2025-09-11 21:43:32] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -52.507465+0.001538j
[2025-09-11 21:43:47] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -52.572477+0.002323j
[2025-09-11 21:44:03] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -52.580991-0.002660j
[2025-09-11 21:44:18] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -52.464706+0.002720j
[2025-09-11 21:44:34] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -52.456975+0.000002j
[2025-09-11 21:44:49] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -52.527243+0.004830j
[2025-09-11 21:45:04] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -52.584407+0.001105j
[2025-09-11 21:45:20] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -52.537356+0.001541j
[2025-09-11 21:45:35] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -52.590488+0.000264j
[2025-09-11 21:45:50] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -52.580861-0.003521j
[2025-09-11 21:46:06] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -52.500650+0.001586j
[2025-09-11 21:46:21] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -52.460487-0.001123j
[2025-09-11 21:46:36] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -52.628976-0.000755j
[2025-09-11 21:46:52] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -52.460852-0.000116j
[2025-09-11 21:47:07] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -52.481887-0.000299j
[2025-09-11 21:47:22] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -52.586289-0.000156j
[2025-09-11 21:47:38] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -52.610385-0.001794j
[2025-09-11 21:47:53] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -52.770647-0.000792j
[2025-09-11 21:48:09] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -52.708184+0.000315j
[2025-09-11 21:48:24] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -52.807095-0.001238j
[2025-09-11 21:48:39] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -52.811160-0.003372j
[2025-09-11 21:48:55] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -52.598218+0.002154j
[2025-09-11 21:49:10] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -52.717966-0.000451j
[2025-09-11 21:49:25] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -52.648283-0.002159j
[2025-09-11 21:49:41] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -52.576349-0.000100j
[2025-09-11 21:49:56] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -52.648678-0.003046j
[2025-09-11 21:50:11] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -52.526333+0.003690j
[2025-09-11 21:50:27] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -52.538523-0.000775j
[2025-09-11 21:50:42] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -52.488732+0.001602j
[2025-09-11 21:50:57] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -52.585667+0.000197j
[2025-09-11 21:51:13] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -52.426238-0.001723j
[2025-09-11 21:51:28] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -52.439855+0.001680j
[2025-09-11 21:51:43] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -52.484681-0.002340j
[2025-09-11 21:51:59] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -52.542268+0.003208j
[2025-09-11 21:52:14] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -52.555263+0.001114j
[2025-09-11 21:52:29] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -52.427099+0.000501j
[2025-09-11 21:52:29] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-11 21:52:45] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -52.452083-0.000392j
[2025-09-11 21:53:00] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -52.504100+0.001585j
[2025-09-11 21:53:15] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -52.607566+0.000177j
[2025-09-11 21:53:31] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -52.555583-0.000535j
[2025-09-11 21:53:46] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -52.473580-0.003110j
[2025-09-11 21:54:01] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -52.489323+0.000025j
[2025-09-11 21:54:17] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -52.523297-0.002674j
[2025-09-11 21:54:32] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -52.600366-0.004548j
[2025-09-11 21:54:47] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -52.583435-0.000927j
[2025-09-11 21:55:03] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -52.590337-0.001227j
[2025-09-11 21:55:18] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -52.556818-0.001279j
[2025-09-11 21:55:33] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -52.495995-0.000712j
[2025-09-11 21:55:49] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -52.463914+0.003037j
[2025-09-11 21:56:04] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -52.589460+0.000309j
[2025-09-11 21:56:20] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -52.725994-0.001333j
[2025-09-11 21:56:35] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -52.715901-0.000286j
[2025-09-11 21:56:50] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -52.648997+0.001079j
[2025-09-11 21:57:06] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -52.618584+0.001399j
[2025-09-11 21:57:21] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -52.359712-0.000833j
[2025-09-11 21:57:37] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -52.387309-0.002784j
[2025-09-11 21:57:52] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -52.478087-0.000890j
[2025-09-11 21:58:07] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -52.407594-0.000523j
[2025-09-11 21:58:23] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -52.395947+0.000007j
[2025-09-11 21:58:38] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -52.336758+0.002470j
[2025-09-11 21:58:54] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -52.513469-0.000519j
[2025-09-11 21:59:09] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -52.522521-0.000382j
[2025-09-11 21:59:24] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -52.505689-0.002618j
[2025-09-11 21:59:40] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -52.609361-0.000721j
[2025-09-11 21:59:55] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -52.557277-0.001467j
[2025-09-11 22:00:10] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -52.702692-0.000969j
[2025-09-11 22:00:11] RESTART #2 | Period: 600
[2025-09-11 22:00:26] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -52.653455-0.001733j
[2025-09-11 22:00:41] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -52.590306+0.000811j
[2025-09-11 22:00:57] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -52.522828+0.002868j
[2025-09-11 22:01:12] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -52.588764+0.002267j
[2025-09-11 22:01:27] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -52.677731+0.001135j
[2025-09-11 22:01:43] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -52.704664+0.000164j
[2025-09-11 22:01:58] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -52.629996+0.000285j
[2025-09-11 22:02:14] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -52.598489+0.004972j
[2025-09-11 22:02:29] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -52.564057-0.001946j
[2025-09-11 22:02:44] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -52.528360-0.002053j
[2025-09-11 22:03:00] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -52.572287+0.001468j
[2025-09-11 22:03:15] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -52.413351+0.000073j
[2025-09-11 22:03:31] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -52.549187-0.001206j
[2025-09-11 22:03:46] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -52.438729+0.002816j
[2025-09-11 22:04:01] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -52.608656+0.003189j
[2025-09-11 22:04:17] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -52.483094+0.001001j
[2025-09-11 22:04:32] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -52.542496+0.000743j
[2025-09-11 22:04:47] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -52.581582-0.002152j
[2025-09-11 22:05:03] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -52.546514+0.001502j
[2025-09-11 22:05:18] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -52.325955-0.002525j
[2025-09-11 22:05:34] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -52.361019-0.000280j
[2025-09-11 22:05:49] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -52.273902+0.000770j
[2025-09-11 22:06:04] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -52.424780+0.000305j
[2025-09-11 22:06:20] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -52.448871+0.002057j
[2025-09-11 22:06:35] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -52.466332-0.001012j
[2025-09-11 22:06:50] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -52.557319-0.000573j
[2025-09-11 22:07:06] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -52.440139+0.002614j
[2025-09-11 22:07:21] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -52.513234-0.000741j
[2025-09-11 22:07:37] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -52.534090+0.001612j
[2025-09-11 22:07:52] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -52.601787-0.001896j
[2025-09-11 22:08:07] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -52.624335+0.001121j
[2025-09-11 22:08:22] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -52.731729+0.001706j
[2025-09-11 22:08:32] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -52.547366+0.003304j
[2025-09-11 22:08:42] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -52.555421-0.001659j
[2025-09-11 22:08:52] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -52.608609+0.001318j
[2025-09-11 22:09:03] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -52.503236-0.000053j
[2025-09-11 22:09:13] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -52.509588-0.001557j
[2025-09-11 22:09:23] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -52.517493+0.000007j
[2025-09-11 22:09:35] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -52.630612+0.003044j
[2025-09-11 22:09:50] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -52.589921-0.001026j
[2025-09-11 22:10:05] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -52.678570+0.006107j
[2025-09-11 22:10:21] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -52.673675+0.001188j
[2025-09-11 22:10:36] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -52.675617-0.002794j
[2025-09-11 22:10:52] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -52.566862+0.001322j
[2025-09-11 22:11:07] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -52.452137-0.003539j
[2025-09-11 22:11:22] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -52.563577-0.000193j
[2025-09-11 22:11:38] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -52.560231+0.004002j
[2025-09-11 22:11:53] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -52.542976+0.004032j
[2025-09-11 22:12:09] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -52.633933+0.000590j
[2025-09-11 22:12:24] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -52.445402+0.000766j
[2025-09-11 22:12:39] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -52.582262-0.001205j
[2025-09-11 22:12:55] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -52.618726+0.001526j
[2025-09-11 22:13:10] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -52.553775-0.001393j
[2025-09-11 22:13:25] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -52.516517+0.002381j
[2025-09-11 22:13:41] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -52.491996+0.000048j
[2025-09-11 22:13:56] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -52.474329-0.000327j
[2025-09-11 22:14:11] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -52.565869-0.001456j
[2025-09-11 22:14:27] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -52.554506-0.001871j
[2025-09-11 22:14:42] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -52.514844+0.000698j
[2025-09-11 22:14:58] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -52.515756+0.000257j
[2025-09-11 22:15:13] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -52.491167-0.000014j
[2025-09-11 22:15:28] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -52.518192-0.001151j
[2025-09-11 22:15:44] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -52.587360+0.000209j
[2025-09-11 22:15:59] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -52.530809+0.004331j
[2025-09-11 22:16:15] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -52.538181-0.001847j
[2025-09-11 22:16:30] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -52.700885-0.001568j
[2025-09-11 22:16:45] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -52.561939+0.000878j
[2025-09-11 22:17:01] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -52.539827+0.002249j
[2025-09-11 22:17:16] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -52.475649+0.002673j
[2025-09-11 22:17:32] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -52.385338+0.000069j
[2025-09-11 22:17:47] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -52.318947+0.000191j
[2025-09-11 22:18:02] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -52.314436+0.002381j
[2025-09-11 22:18:18] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -52.311310+0.002445j
[2025-09-11 22:18:33] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -52.371913+0.001762j
[2025-09-11 22:18:49] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -52.421518-0.000107j
[2025-09-11 22:18:49] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-11 22:19:04] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -52.411526+0.003164j
[2025-09-11 22:19:15] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -52.504271+0.001102j
[2025-09-11 22:19:28] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -52.436667-0.001283j
[2025-09-11 22:19:40] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -52.383602+0.000146j
[2025-09-11 22:19:52] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -52.350015+0.001376j
[2025-09-11 22:20:08] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -52.459777+0.003746j
[2025-09-11 22:20:23] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -52.340848-0.001942j
[2025-09-11 22:20:39] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -52.257420-0.000766j
[2025-09-11 22:20:54] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -52.382253-0.002285j
[2025-09-11 22:21:09] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -52.407229-0.003422j
[2025-09-11 22:21:25] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -52.575715-0.002154j
[2025-09-11 22:21:40] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -52.580033-0.005130j
[2025-09-11 22:21:55] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -52.623656-0.000984j
[2025-09-11 22:22:11] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -52.487145+0.002096j
[2025-09-11 22:22:26] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -52.500863+0.000261j
[2025-09-11 22:22:41] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -52.485485+0.001102j
[2025-09-11 22:22:57] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -52.488401+0.001121j
[2025-09-11 22:23:12] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -52.584517+0.001577j
[2025-09-11 22:23:27] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -52.553319-0.001605j
[2025-09-11 22:23:43] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -52.556708+0.000050j
[2025-09-11 22:23:58] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -52.539749+0.002113j
[2025-09-11 22:24:13] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -52.454337-0.001177j
[2025-09-11 22:24:29] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -52.423498-0.002263j
[2025-09-11 22:24:44] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -52.417096+0.000501j
[2025-09-11 22:25:00] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -52.489212+0.002418j
[2025-09-11 22:25:15] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -52.513258+0.001480j
[2025-09-11 22:25:30] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -52.394311-0.000574j
[2025-09-11 22:25:46] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -52.450457-0.000704j
[2025-09-11 22:26:01] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -52.580712+0.000476j
[2025-09-11 22:26:17] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -52.590827+0.000235j
[2025-09-11 22:26:32] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -52.482593+0.001535j
[2025-09-11 22:26:47] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -52.541654-0.002797j
[2025-09-11 22:27:03] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -52.467105-0.001784j
[2025-09-11 22:27:18] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -52.434645-0.000113j
[2025-09-11 22:27:33] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -52.674318+0.000892j
[2025-09-11 22:27:49] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -52.545217-0.000561j
[2025-09-11 22:28:04] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -52.483179+0.000836j
[2025-09-11 22:28:19] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -52.507398+0.002280j
[2025-09-11 22:28:35] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -52.388180-0.000008j
[2025-09-11 22:28:50] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -52.483346-0.000405j
[2025-09-11 22:29:06] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -52.414738-0.002574j
[2025-09-11 22:29:21] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -52.411962-0.001566j
[2025-09-11 22:29:36] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -52.477958-0.001756j
[2025-09-11 22:29:52] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -52.437883+0.000869j
[2025-09-11 22:30:07] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -52.508320-0.000093j
[2025-09-11 22:30:22] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -52.584530-0.002481j
[2025-09-11 22:30:38] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -52.556052+0.001082j
[2025-09-11 22:30:53] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -52.553557+0.000221j
[2025-09-11 22:31:08] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -52.604956+0.000511j
[2025-09-11 22:31:24] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -52.492775+0.002848j
[2025-09-11 22:31:39] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -52.562397-0.001053j
[2025-09-11 22:31:54] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -52.575613+0.002462j
[2025-09-11 22:32:10] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -52.568390+0.002886j
[2025-09-11 22:32:25] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -52.515329+0.001417j
[2025-09-11 22:32:41] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -52.407687-0.000768j
[2025-09-11 22:32:56] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -52.436030-0.000897j
[2025-09-11 22:33:11] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -52.451810+0.004439j
[2025-09-11 22:33:27] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -52.560536+0.000607j
[2025-09-11 22:33:42] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -52.567248+0.000502j
[2025-09-11 22:33:57] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -52.575662+0.000419j
[2025-09-11 22:34:13] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -52.515003-0.000097j
[2025-09-11 22:34:28] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -52.626904-0.001234j
[2025-09-11 22:34:44] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -52.655986+0.001852j
[2025-09-11 22:34:59] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -52.418829-0.000100j
[2025-09-11 22:35:14] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -52.508702+0.001548j
[2025-09-11 22:35:30] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -52.634665-0.004269j
[2025-09-11 22:35:45] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -52.622169-0.001351j
[2025-09-11 22:36:00] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -52.653378-0.000397j
[2025-09-11 22:36:16] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -52.542419+0.000926j
[2025-09-11 22:36:31] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -52.586177+0.002589j
[2025-09-11 22:36:46] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -52.579321-0.000956j
[2025-09-11 22:37:02] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -52.559065+0.000655j
[2025-09-11 22:37:17] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -52.483291+0.001392j
[2025-09-11 22:37:32] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -52.431040-0.000037j
[2025-09-11 22:37:48] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -52.430793+0.001575j
[2025-09-11 22:38:03] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -52.454307-0.001291j
[2025-09-11 22:38:19] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -52.587811-0.003164j
[2025-09-11 22:38:34] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -52.540568-0.002264j
[2025-09-11 22:38:49] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -52.481309-0.000538j
[2025-09-11 22:39:05] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -52.473671+0.002634j
[2025-09-11 22:39:20] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -52.645075+0.000973j
[2025-09-11 22:39:35] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -52.541232+0.000653j
[2025-09-11 22:39:51] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -52.556945+0.001362j
[2025-09-11 22:40:06] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -52.496114-0.001931j
[2025-09-11 22:40:22] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -52.584755+0.000148j
[2025-09-11 22:40:37] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -52.451931-0.000458j
[2025-09-11 22:40:52] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -52.455475-0.002895j
[2025-09-11 22:41:08] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -52.514444+0.002435j
[2025-09-11 22:41:23] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -52.401906+0.001546j
[2025-09-11 22:41:38] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -52.381657+0.000991j
[2025-09-11 22:41:54] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -52.394363+0.000433j
[2025-09-11 22:42:09] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -52.391570+0.000842j
[2025-09-11 22:42:24] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -52.398729+0.000096j
[2025-09-11 22:42:40] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -52.495162-0.001940j
[2025-09-11 22:42:55] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -52.565652-0.002769j
[2025-09-11 22:43:10] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -52.408337-0.001370j
[2025-09-11 22:43:26] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -52.395965-0.000284j
[2025-09-11 22:43:41] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -52.568016-0.000227j
[2025-09-11 22:43:57] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -52.593862-0.002189j
[2025-09-11 22:44:12] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -52.472746-0.003055j
[2025-09-11 22:44:27] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -52.477259+0.000221j
[2025-09-11 22:44:43] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -52.514744-0.001034j
[2025-09-11 22:44:58] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -52.570863-0.002424j
[2025-09-11 22:45:13] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -52.441046-0.003102j
[2025-09-11 22:45:29] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -52.584620-0.001256j
[2025-09-11 22:45:29] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-11 22:45:44] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -52.672071+0.001196j
[2025-09-11 22:46:00] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -52.481118-0.000243j
[2025-09-11 22:46:15] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -52.478097+0.001920j
[2025-09-11 22:46:30] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -52.515191+0.001691j
[2025-09-11 22:46:46] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -52.529895-0.001687j
[2025-09-11 22:47:01] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -52.559579-0.000775j
[2025-09-11 22:47:16] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -52.594957-0.003382j
[2025-09-11 22:47:32] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -52.489550+0.002708j
[2025-09-11 22:47:47] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -52.470971-0.000711j
[2025-09-11 22:48:03] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -52.415218+0.001389j
[2025-09-11 22:48:18] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -52.562110-0.000276j
[2025-09-11 22:48:33] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -52.550365-0.000793j
[2025-09-11 22:48:49] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -52.517329+0.001456j
[2025-09-11 22:49:04] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -52.516293+0.000688j
[2025-09-11 22:49:19] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -52.523362+0.002581j
[2025-09-11 22:49:35] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -52.553229+0.000626j
[2025-09-11 22:49:50] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -52.509009+0.002339j
[2025-09-11 22:50:05] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -52.458114-0.000565j
[2025-09-11 22:50:21] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -52.396291+0.004061j
[2025-09-11 22:50:36] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -52.451152+0.003192j
[2025-09-11 22:50:51] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -52.492899-0.001746j
[2025-09-11 22:51:07] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -52.503733+0.000029j
[2025-09-11 22:51:22] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -52.571232-0.005478j
[2025-09-11 22:51:37] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -52.589064-0.000979j
[2025-09-11 22:51:53] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -52.501839+0.002166j
[2025-09-11 22:52:08] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -52.736573-0.000228j
[2025-09-11 22:52:23] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -52.572715-0.002749j
[2025-09-11 22:52:39] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -52.616649+0.004296j
[2025-09-11 22:52:54] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -52.438516+0.001426j
[2025-09-11 22:53:10] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -52.491596+0.001939j
[2025-09-11 22:53:25] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -52.454776-0.001249j
[2025-09-11 22:53:40] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -52.423866-0.002059j
[2025-09-11 22:53:51] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -52.407221-0.000846j
[2025-09-11 22:54:01] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -52.488449-0.002050j
[2025-09-11 22:54:11] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -52.585502+0.000565j
[2025-09-11 22:54:22] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -52.526753-0.002219j
[2025-09-11 22:54:32] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -52.452371-0.000248j
[2025-09-11 22:54:42] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -52.562518-0.000824j
[2025-09-11 22:54:54] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -52.509038-0.002914j
[2025-09-11 22:55:10] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -52.685183-0.001431j
[2025-09-11 22:55:25] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -52.509683-0.000815j
[2025-09-11 22:55:41] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -52.426611+0.000219j
[2025-09-11 22:55:56] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -52.588691-0.001530j
[2025-09-11 22:56:11] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -52.598491+0.000672j
[2025-09-11 22:56:27] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -52.519408-0.003893j
[2025-09-11 22:56:42] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -52.636407-0.003198j
[2025-09-11 22:56:58] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -52.551818-0.000476j
[2025-09-11 22:57:13] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -52.533946-0.000027j
[2025-09-11 22:57:28] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -52.487111-0.001808j
[2025-09-11 22:57:44] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -52.448310-0.000340j
[2025-09-11 22:57:59] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -52.289352-0.003501j
[2025-09-11 22:58:15] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -52.497073+0.005039j
[2025-09-11 22:58:30] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -52.448389-0.003200j
[2025-09-11 22:58:45] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -52.561003-0.002576j
[2025-09-11 22:59:01] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -52.529113-0.000817j
[2025-09-11 22:59:16] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -52.322209-0.000764j
[2025-09-11 22:59:32] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -52.345246-0.000655j
[2025-09-11 22:59:47] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -52.389189+0.001348j
[2025-09-11 23:00:02] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -52.427028+0.000680j
[2025-09-11 23:00:18] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -52.442251-0.001223j
[2025-09-11 23:00:33] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -52.487163+0.002131j
[2025-09-11 23:00:49] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -52.643068-0.003652j
[2025-09-11 23:01:04] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -52.460185-0.001249j
[2025-09-11 23:01:19] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -52.576915+0.001163j
[2025-09-11 23:01:35] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -52.431536+0.000611j
[2025-09-11 23:01:50] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -52.356522+0.000236j
[2025-09-11 23:02:06] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -52.271627+0.002343j
[2025-09-11 23:02:21] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -52.323249-0.000436j
[2025-09-11 23:02:37] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -52.435074-0.000124j
[2025-09-11 23:02:52] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -52.337412+0.001145j
[2025-09-11 23:03:07] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -52.466171+0.003135j
[2025-09-11 23:03:23] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -52.489534-0.000601j
[2025-09-11 23:03:38] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -52.628399+0.000304j
[2025-09-11 23:03:53] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -52.536143-0.003256j
[2025-09-11 23:04:09] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -52.479160-0.000253j
[2025-09-11 23:04:22] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -52.602886-0.000554j
[2025-09-11 23:04:33] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -52.397432+0.001144j
[2025-09-11 23:04:47] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -52.535176+0.000518j
[2025-09-11 23:04:57] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -52.398942+0.000404j
[2025-09-11 23:05:12] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -52.320905-0.001609j
[2025-09-11 23:05:27] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -52.370863+0.000448j
[2025-09-11 23:05:43] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -52.395096-0.000638j
[2025-09-11 23:05:58] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -52.473989-0.000307j
[2025-09-11 23:06:13] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -52.396374+0.001941j
[2025-09-11 23:06:29] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -52.390770-0.000697j
[2025-09-11 23:06:44] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -52.511048+0.001006j
[2025-09-11 23:06:59] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -52.512531-0.000491j
[2025-09-11 23:07:15] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -52.440005-0.000210j
[2025-09-11 23:07:30] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -52.521333+0.001363j
[2025-09-11 23:07:45] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -52.453137+0.001070j
[2025-09-11 23:08:01] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -52.464234-0.001532j
[2025-09-11 23:08:16] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -52.550497-0.000373j
[2025-09-11 23:08:31] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -52.455245+0.000240j
[2025-09-11 23:08:47] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -52.539983-0.002424j
[2025-09-11 23:09:02] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -52.445855-0.002899j
[2025-09-11 23:09:17] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -52.485983-0.001662j
[2025-09-11 23:09:33] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -52.456306+0.002837j
[2025-09-11 23:09:48] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -52.412466+0.000697j
[2025-09-11 23:10:03] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -52.586553+0.001535j
[2025-09-11 23:10:19] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -52.650577-0.002017j
[2025-09-11 23:10:34] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -52.664421+0.001106j
[2025-09-11 23:10:49] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -52.471856-0.000317j
[2025-09-11 23:11:05] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -52.359348-0.001985j
[2025-09-11 23:11:20] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -52.396747-0.001002j
[2025-09-11 23:11:35] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -52.512645-0.000937j
[2025-09-11 23:11:35] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-11 23:11:51] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -52.489521+0.002136j
[2025-09-11 23:12:06] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -52.590277+0.000476j
[2025-09-11 23:12:21] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -52.544021+0.001685j
[2025-09-11 23:12:37] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -52.507217+0.000806j
[2025-09-11 23:12:52] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -52.451292+0.003334j
[2025-09-11 23:13:07] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -52.407101-0.004852j
[2025-09-11 23:13:23] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -52.376468-0.001974j
[2025-09-11 23:13:38] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -52.393290-0.001554j
[2025-09-11 23:13:53] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -52.490684+0.000605j
[2025-09-11 23:14:09] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -52.589885+0.000404j
[2025-09-11 23:14:24] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -52.630093+0.001590j
[2025-09-11 23:14:39] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -52.496222-0.002486j
[2025-09-11 23:14:55] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -52.647740-0.003034j
[2025-09-11 23:15:10] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -52.533407-0.004064j
[2025-09-11 23:15:26] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -52.580944-0.001142j
[2025-09-11 23:15:41] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -52.433980-0.000422j
[2025-09-11 23:15:56] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -52.415462-0.000563j
[2025-09-11 23:16:12] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -52.484207-0.002413j
[2025-09-11 23:16:27] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -52.383388+0.002128j
[2025-09-11 23:16:42] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -52.561027-0.001145j
[2025-09-11 23:16:58] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -52.479780-0.002364j
[2025-09-11 23:17:13] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -52.438149-0.002208j
[2025-09-11 23:17:28] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -52.483453-0.000342j
[2025-09-11 23:17:44] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -52.397363-0.000290j
[2025-09-11 23:17:59] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -52.516844+0.001277j
[2025-09-11 23:18:14] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -52.427017+0.002576j
[2025-09-11 23:18:30] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -52.522532-0.002540j
[2025-09-11 23:18:45] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -52.622127+0.003200j
[2025-09-11 23:19:00] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -52.576295+0.000341j
[2025-09-11 23:19:16] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -52.586430-0.000368j
[2025-09-11 23:19:31] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -52.546873-0.001263j
[2025-09-11 23:19:46] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -52.510886+0.001490j
[2025-09-11 23:20:02] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -52.444755-0.002376j
[2025-09-11 23:20:17] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -52.552414-0.001098j
[2025-09-11 23:20:33] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -52.551298+0.000722j
[2025-09-11 23:20:48] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -52.567915+0.004788j
[2025-09-11 23:21:03] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -52.552750+0.004319j
[2025-09-11 23:21:19] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -52.504333+0.000878j
[2025-09-11 23:21:34] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -52.525574-0.004855j
[2025-09-11 23:21:49] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -52.618171-0.000525j
[2025-09-11 23:22:05] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -52.457421-0.000762j
[2025-09-11 23:22:20] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -52.425059+0.004415j
[2025-09-11 23:22:35] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -52.496531-0.000574j
[2025-09-11 23:22:51] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -52.566129+0.000777j
[2025-09-11 23:23:06] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -52.490508+0.001462j
[2025-09-11 23:23:21] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -52.549848-0.003765j
[2025-09-11 23:23:37] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -52.494895+0.003489j
[2025-09-11 23:23:52] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -52.535198+0.005397j
[2025-09-11 23:24:07] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -52.505216-0.003668j
[2025-09-11 23:24:23] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -52.470335+0.000617j
[2025-09-11 23:24:38] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -52.474829-0.000804j
[2025-09-11 23:24:54] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -52.480920-0.004263j
[2025-09-11 23:25:09] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -52.549800+0.002606j
[2025-09-11 23:25:24] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -52.664854-0.000628j
[2025-09-11 23:25:40] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -52.750724-0.001863j
[2025-09-11 23:25:55] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -52.722078+0.000876j
[2025-09-11 23:26:10] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -52.623115+0.000656j
[2025-09-11 23:26:26] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -52.572703-0.001777j
[2025-09-11 23:26:41] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -52.555153+0.000075j
[2025-09-11 23:26:56] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -52.462943-0.000296j
[2025-09-11 23:27:12] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -52.417930-0.000401j
[2025-09-11 23:27:27] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -52.473845-0.001163j
[2025-09-11 23:27:43] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -52.503828-0.002256j
[2025-09-11 23:27:58] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -52.580683-0.002192j
[2025-09-11 23:28:13] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -52.471548-0.001744j
[2025-09-11 23:28:28] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -52.426860-0.001444j
[2025-09-11 23:28:44] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -52.321257+0.001990j
[2025-09-11 23:28:59] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -52.437013+0.000323j
[2025-09-11 23:29:14] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -52.378732+0.000201j
[2025-09-11 23:29:30] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -52.383336-0.001185j
[2025-09-11 23:29:45] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -52.444939-0.002434j
[2025-09-11 23:30:00] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -52.518914-0.000372j
[2025-09-11 23:30:16] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -52.470320-0.000835j
[2025-09-11 23:30:31] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -52.443800+0.002431j
[2025-09-11 23:30:47] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -52.561517-0.004976j
[2025-09-11 23:31:02] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -52.422447+0.001341j
[2025-09-11 23:31:17] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -52.479592+0.004296j
[2025-09-11 23:31:33] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -52.533346+0.001007j
[2025-09-11 23:31:48] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -52.549985+0.000327j
[2025-09-11 23:32:03] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -52.427977+0.000609j
[2025-09-11 23:32:19] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -52.469211+0.000394j
[2025-09-11 23:32:34] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -52.451291-0.002461j
[2025-09-11 23:32:49] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -52.477266-0.002629j
[2025-09-11 23:33:05] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -52.452783-0.000046j
[2025-09-11 23:33:20] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -52.415126+0.000227j
[2025-09-11 23:33:35] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -52.523101+0.001715j
[2025-09-11 23:33:51] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -52.541850+0.001165j
[2025-09-11 23:34:06] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -52.533631-0.001108j
[2025-09-11 23:34:21] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -52.428939-0.000844j
[2025-09-11 23:34:37] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -52.381065-0.001367j
[2025-09-11 23:34:52] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -52.485144+0.003962j
[2025-09-11 23:35:07] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -52.309526+0.000128j
[2025-09-11 23:35:23] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -52.353413-0.001304j
[2025-09-11 23:35:38] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -52.375583-0.001206j
[2025-09-11 23:35:54] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -52.424265-0.000214j
[2025-09-11 23:36:09] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -52.319144+0.006283j
[2025-09-11 23:36:24] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -52.355368-0.000738j
[2025-09-11 23:36:40] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -52.300091+0.002429j
[2025-09-11 23:36:55] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -52.405802-0.001848j
[2025-09-11 23:37:10] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -52.416466+0.000277j
[2025-09-11 23:37:26] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -52.522915-0.000729j
[2025-09-11 23:37:41] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -52.509367-0.000571j
[2025-09-11 23:37:56] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -52.527830-0.004899j
[2025-09-11 23:38:12] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -52.498740-0.000801j
[2025-09-11 23:38:24] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -52.462517+0.001180j
[2025-09-11 23:38:24] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-11 23:38:35] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -52.536930-0.002816j
[2025-09-11 23:38:45] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -52.524396+0.001383j
[2025-09-11 23:38:55] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -52.633272+0.000893j
[2025-09-11 23:39:06] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -52.548714-0.001964j
[2025-09-11 23:39:16] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -52.629695-0.002546j
[2025-09-11 23:39:26] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -52.682745+0.002390j
[2025-09-11 23:39:41] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -52.641058-0.003669j
[2025-09-11 23:39:57] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -52.649678-0.004229j
[2025-09-11 23:40:12] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -52.607320-0.000910j
[2025-09-11 23:40:28] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -52.604913-0.000211j
[2025-09-11 23:40:43] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -52.629595-0.003405j
[2025-09-11 23:40:58] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -52.511421+0.002725j
[2025-09-11 23:41:14] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -52.496735+0.004327j
[2025-09-11 23:41:29] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -52.507946+0.001778j
[2025-09-11 23:41:45] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -52.523937-0.002046j
[2025-09-11 23:42:00] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -52.506535+0.000579j
[2025-09-11 23:42:15] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -52.608653-0.001935j
[2025-09-11 23:42:31] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -52.487700+0.000283j
[2025-09-11 23:42:46] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -52.545089-0.003068j
[2025-09-11 23:43:02] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -52.488114-0.003132j
[2025-09-11 23:43:17] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -52.491048-0.001817j
[2025-09-11 23:43:33] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -52.604390+0.002257j
[2025-09-11 23:43:48] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -52.620396+0.001748j
[2025-09-11 23:44:03] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -52.494047+0.000021j
[2025-09-11 23:44:19] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -52.526676+0.003162j
[2025-09-11 23:44:34] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -52.498040-0.001202j
[2025-09-11 23:44:50] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -52.631882-0.002194j
[2025-09-11 23:45:05] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -52.576660-0.002382j
[2025-09-11 23:45:21] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -52.647164-0.001398j
[2025-09-11 23:45:36] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -52.613677+0.000552j
[2025-09-11 23:45:51] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -52.500423+0.002991j
[2025-09-11 23:46:07] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -52.585656+0.003253j
[2025-09-11 23:46:22] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -52.543053+0.000654j
[2025-09-11 23:46:38] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -52.620169+0.001069j
[2025-09-11 23:46:53] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -52.589407+0.000115j
[2025-09-11 23:47:09] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -52.517958+0.001140j
[2025-09-11 23:47:24] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -52.503447-0.000502j
[2025-09-11 23:47:39] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -52.548935-0.004648j
[2025-09-11 23:47:55] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -52.521048-0.001133j
[2025-09-11 23:48:10] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -52.547728-0.001378j
[2025-09-11 23:48:26] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -52.440331-0.004031j
[2025-09-11 23:48:41] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -52.523910+0.000903j
[2025-09-11 23:48:57] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -52.614055-0.001026j
[2025-09-11 23:49:07] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -52.552604-0.001405j
[2025-09-11 23:49:20] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -52.491883+0.001296j
[2025-09-11 23:49:32] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -52.546131-0.002412j
[2025-09-11 23:49:45] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -52.519844-0.001792j
[2025-09-11 23:50:00] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -52.578011+0.002747j
[2025-09-11 23:50:15] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -52.539708-0.002919j
[2025-09-11 23:50:31] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -52.416572-0.002444j
[2025-09-11 23:50:46] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -52.511596-0.000265j
[2025-09-11 23:51:01] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -52.616332-0.002612j
[2025-09-11 23:51:17] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -52.630458+0.001761j
[2025-09-11 23:51:32] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -52.616251+0.002365j
[2025-09-11 23:51:47] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -52.553267-0.003578j
[2025-09-11 23:52:03] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -52.586018+0.003325j
[2025-09-11 23:52:18] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -52.656921-0.003766j
[2025-09-11 23:52:33] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -52.627429-0.000619j
[2025-09-11 23:52:49] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -52.618981+0.001286j
[2025-09-11 23:53:04] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -52.552329+0.000027j
[2025-09-11 23:53:19] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -52.488133+0.000351j
[2025-09-11 23:53:35] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -52.551572-0.003149j
[2025-09-11 23:53:50] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -52.680934+0.000714j
[2025-09-11 23:54:05] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -52.557882+0.001885j
[2025-09-11 23:54:21] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -52.545060-0.001846j
[2025-09-11 23:54:36] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -52.621957+0.000599j
[2025-09-11 23:54:52] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -52.559114+0.000242j
[2025-09-11 23:55:07] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -52.500221+0.000423j
[2025-09-11 23:55:22] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -52.410472-0.002002j
[2025-09-11 23:55:37] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -52.506016-0.000979j
[2025-09-11 23:55:53] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -52.375806+0.000270j
[2025-09-11 23:56:08] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -52.350403-0.005645j
[2025-09-11 23:56:24] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -52.484828-0.000648j
[2025-09-11 23:56:39] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -52.519363-0.000246j
[2025-09-11 23:56:54] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -52.491428+0.001664j
[2025-09-11 23:57:10] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -52.536158+0.004350j
[2025-09-11 23:57:25] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -52.439513-0.001454j
[2025-09-11 23:57:40] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -52.618200-0.003107j
[2025-09-11 23:57:56] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -52.625058+0.002988j
[2025-09-11 23:58:11] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -52.671530+0.018278j
[2025-09-11 23:58:26] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -52.580229+0.002879j
[2025-09-11 23:58:42] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -52.543635-0.001737j
[2025-09-11 23:58:57] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -52.445623-0.000165j
[2025-09-11 23:59:12] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -52.508324-0.002255j
[2025-09-11 23:59:28] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -52.426489-0.000250j
[2025-09-11 23:59:43] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -52.513501+0.001955j
[2025-09-11 23:59:58] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -52.503938-0.002137j
[2025-09-12 00:00:14] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -52.544194+0.000286j
[2025-09-12 00:00:29] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -52.594416-0.002709j
[2025-09-12 00:00:44] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -52.532677+0.001847j
[2025-09-12 00:01:00] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -52.607461-0.001002j
[2025-09-12 00:01:15] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -52.634493-0.001352j
[2025-09-12 00:01:31] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -52.674187+0.001442j
[2025-09-12 00:01:46] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -52.598300-0.000681j
[2025-09-12 00:02:01] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -52.691686+0.002486j
[2025-09-12 00:02:17] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -52.541112+0.001980j
[2025-09-12 00:02:32] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -52.548535-0.001447j
[2025-09-12 00:02:47] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -52.540535+0.001090j
[2025-09-12 00:03:03] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -52.481488+0.000794j
[2025-09-12 00:03:18] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -52.406968+0.000975j
[2025-09-12 00:03:34] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -52.392580-0.002727j
[2025-09-12 00:03:49] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -52.354315-0.000078j
[2025-09-12 00:04:04] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -52.413596+0.002910j
[2025-09-12 00:04:20] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -52.482925-0.001224j
[2025-09-12 00:04:35] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -52.480953-0.001202j
[2025-09-12 00:04:35] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-12 00:04:51] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -52.460326+0.001919j
[2025-09-12 00:05:06] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -52.451758+0.000885j
[2025-09-12 00:05:21] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -52.553003+0.001230j
[2025-09-12 00:05:37] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -52.575845-0.003618j
[2025-09-12 00:05:52] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -52.358437-0.001573j
[2025-09-12 00:06:08] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -52.427735+0.002896j
[2025-09-12 00:06:23] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -52.480231-0.000575j
[2025-09-12 00:06:38] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -52.465194-0.000205j
[2025-09-12 00:06:54] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -52.493332-0.002309j
[2025-09-12 00:07:09] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -52.427934-0.000399j
[2025-09-12 00:07:24] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -52.466933+0.000096j
[2025-09-12 00:07:40] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -52.625430+0.000130j
[2025-09-12 00:07:55] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -52.528950-0.000112j
[2025-09-12 00:08:10] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -52.523097+0.003774j
[2025-09-12 00:08:26] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -52.444742-0.001981j
[2025-09-12 00:08:41] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -52.486971+0.000491j
[2025-09-12 00:08:57] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -52.557148-0.002118j
[2025-09-12 00:09:12] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -52.518178+0.003122j
[2025-09-12 00:09:27] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -52.479254+0.004222j
[2025-09-12 00:09:43] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -52.414321+0.000193j
[2025-09-12 00:09:58] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -52.401442+0.002440j
[2025-09-12 00:10:13] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -52.515293-0.002467j
[2025-09-12 00:10:29] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -52.449309+0.000457j
[2025-09-12 00:10:44] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -52.356760+0.000106j
[2025-09-12 00:11:00] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -52.399367-0.001181j
[2025-09-12 00:11:15] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -52.379955+0.003033j
[2025-09-12 00:11:30] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -52.328513+0.001628j
[2025-09-12 00:11:46] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -52.398264+0.002567j
[2025-09-12 00:12:01] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -52.498702-0.001003j
[2025-09-12 00:12:16] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -52.493675-0.001356j
[2025-09-12 00:12:32] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -52.488914-0.002043j
[2025-09-12 00:12:47] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -52.352556+0.001158j
[2025-09-12 00:13:03] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -52.547070+0.000233j
[2025-09-12 00:13:18] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -52.648452-0.001405j
[2025-09-12 00:13:33] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -52.546477-0.001038j
[2025-09-12 00:13:49] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -52.630292+0.002197j
[2025-09-12 00:14:04] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -52.358956+0.000668j
[2025-09-12 00:14:20] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -52.444451-0.000967j
[2025-09-12 00:14:35] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -52.424824-0.001274j
[2025-09-12 00:14:50] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -52.450308-0.000356j
[2025-09-12 00:15:06] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -52.462260-0.000672j
[2025-09-12 00:15:21] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -52.561079-0.000193j
[2025-09-12 00:15:36] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -52.602531+0.002243j
[2025-09-12 00:15:52] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -52.564794-0.001994j
[2025-09-12 00:16:07] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -52.388972-0.001719j
[2025-09-12 00:16:23] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -52.410094+0.001671j
[2025-09-12 00:16:38] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -52.618869-0.001095j
[2025-09-12 00:16:53] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -52.723068-0.000281j
[2025-09-12 00:17:09] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -52.579295-0.003227j
[2025-09-12 00:17:24] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -52.553187+0.002490j
[2025-09-12 00:17:39] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -52.508555+0.001040j
[2025-09-12 00:17:55] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -52.537008+0.002438j
[2025-09-12 00:18:10] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -52.471803-0.001088j
[2025-09-12 00:18:26] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -52.614671-0.002409j
[2025-09-12 00:18:41] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -52.552773-0.000576j
[2025-09-12 00:18:56] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -52.497068+0.000297j
[2025-09-12 00:19:12] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -52.369545+0.000630j
[2025-09-12 00:19:27] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -52.452335-0.000625j
[2025-09-12 00:19:43] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -52.429512-0.001132j
[2025-09-12 00:19:58] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -52.604728-0.001964j
[2025-09-12 00:20:13] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -52.576296+0.001727j
[2025-09-12 00:20:29] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -52.616367-0.000577j
[2025-09-12 00:20:44] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -52.439271+0.001416j
[2025-09-12 00:20:59] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -52.539353-0.000935j
[2025-09-12 00:21:15] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -52.504344-0.001742j
[2025-09-12 00:21:30] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -52.475568-0.002071j
[2025-09-12 00:21:45] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -52.581223+0.001490j
[2025-09-12 00:22:01] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -52.529674-0.001108j
[2025-09-12 00:22:16] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -52.559295-0.001312j
[2025-09-12 00:22:32] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -52.735808+0.000891j
[2025-09-12 00:22:47] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -52.622608+0.000421j
[2025-09-12 00:23:02] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -52.552465+0.001282j
[2025-09-12 00:23:18] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -52.611186-0.002507j
[2025-09-12 00:23:31] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -52.432150-0.001925j
[2025-09-12 00:23:42] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -52.306768+0.001557j
[2025-09-12 00:23:52] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -52.326667-0.001057j
[2025-09-12 00:24:02] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -52.287297-0.000079j
[2025-09-12 00:24:13] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -52.548348-0.002968j
[2025-09-12 00:24:23] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -52.500073-0.004215j
[2025-09-12 00:24:33] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -52.431207-0.000052j
[2025-09-12 00:24:45] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -52.567425+0.000275j
[2025-09-12 00:25:01] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -52.630271-0.002347j
[2025-09-12 00:25:16] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -52.656172+0.002112j
[2025-09-12 00:25:32] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -52.673388+0.001122j
[2025-09-12 00:25:47] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -52.493499+0.001075j
[2025-09-12 00:26:02] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -52.550962+0.001990j
[2025-09-12 00:26:18] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -52.421943-0.000054j
[2025-09-12 00:26:33] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -52.501926+0.001299j
[2025-09-12 00:26:49] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -52.531201+0.000768j
[2025-09-12 00:27:04] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -52.573917-0.000110j
[2025-09-12 00:27:19] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -52.388209+0.001134j
[2025-09-12 00:27:35] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -52.473897-0.002362j
[2025-09-12 00:27:50] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -52.474905-0.002446j
[2025-09-12 00:28:05] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -52.532231-0.000903j
[2025-09-12 00:28:21] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -52.467568-0.002455j
[2025-09-12 00:28:36] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -52.359220-0.001981j
[2025-09-12 00:28:52] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -52.422413-0.000884j
[2025-09-12 00:29:07] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -52.486800-0.000599j
[2025-09-12 00:29:22] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -52.445548-0.000624j
[2025-09-12 00:29:38] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -52.520100+0.002077j
[2025-09-12 00:29:53] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -52.498136-0.000084j
[2025-09-12 00:30:08] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -52.535486-0.003183j
[2025-09-12 00:30:24] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -52.522927-0.003122j
[2025-09-12 00:30:39] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -52.429715-0.001691j
[2025-09-12 00:30:55] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -52.466196+0.001200j
[2025-09-12 00:30:55] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-12 00:30:55] ✅ Training completed | Restarts: 2
[2025-09-12 00:30:55] ============================================================
[2025-09-12 00:30:55] Training completed | Runtime: 16017.9s
[2025-09-12 00:31:01] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-12 00:31:01] ============================================================
