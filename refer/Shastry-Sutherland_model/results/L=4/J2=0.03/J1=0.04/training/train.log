[2025-09-11 15:36:33] ✓ 从checkpoint恢复: results/L=4/J2=0.03/J1=0.05/training/checkpoints/final_GCNN.pkl
[2025-09-11 15:36:33]   - 迭代次数: final
[2025-09-11 15:36:34]   - 能量: -53.255415-0.001786j ± 0.042033
[2025-09-11 15:36:34]   - 时间戳: 2025-09-10T23:46:12.690776+08:00
[2025-09-11 15:37:02] ✓ 变分状态参数已从checkpoint恢复
[2025-09-11 15:37:02] ✓ 从final状态恢复, 重置迭代计数为0
[2025-09-11 15:37:02] ==================================================
[2025-09-11 15:37:02] GCNN for Shastry-Sutherland Model
[2025-09-11 15:37:02] ==================================================
[2025-09-11 15:37:02] System parameters:
[2025-09-11 15:37:02]   - System size: L=4, N=64
[2025-09-11 15:37:02]   - System parameters: J1=0.04, J2=0.03, Q=0.97
[2025-09-11 15:37:02] --------------------------------------------------
[2025-09-11 15:37:02] Model parameters:
[2025-09-11 15:37:02]   - Number of layers = 4
[2025-09-11 15:37:02]   - Number of features = 4
[2025-09-11 15:37:02]   - Total parameters = 12572
[2025-09-11 15:37:02] --------------------------------------------------
[2025-09-11 15:37:02] Training parameters:
[2025-09-11 15:37:02]   - Learning rate: 0.015
[2025-09-11 15:37:02]   - Total iterations: 1050
[2025-09-11 15:37:02]   - Annealing cycles: 3
[2025-09-11 15:37:02]   - Initial period: 150
[2025-09-11 15:37:02]   - Period multiplier: 2.0
[2025-09-11 15:37:02]   - Temperature range: 0.0-1.0
[2025-09-11 15:37:02]   - Samples: 4096
[2025-09-11 15:37:02]   - Discarded samples: 0
[2025-09-11 15:37:02]   - Chunk size: 2048
[2025-09-11 15:37:02]   - Diagonal shift: 0.2
[2025-09-11 15:37:02]   - Gradient clipping: 1.0
[2025-09-11 15:37:02]   - Checkpoint enabled: interval=105
[2025-09-11 15:37:02]   - Checkpoint directory: results/L=4/J2=0.03/J1=0.04/training/checkpoints
[2025-09-11 15:37:02] --------------------------------------------------
[2025-09-11 15:37:02] Device status:
[2025-09-11 15:37:02]   - Devices model: NVIDIA H200 NVL
[2025-09-11 15:37:02]   - Number of devices: 1
[2025-09-11 15:37:02]   - Sharding: True
[2025-09-11 15:37:02] ============================================================
[2025-09-11 15:38:57] [Iter 1/1050] R0[0/150], Temp: 1.0000, Energy: -53.017139+0.004066j
[2025-09-11 15:40:05] [Iter 2/1050] R0[1/150], Temp: 0.9999, Energy: -52.852878+0.007001j
[2025-09-11 15:40:20] [Iter 3/1050] R0[2/150], Temp: 0.9996, Energy: -52.935051+0.004320j
[2025-09-11 15:40:36] [Iter 4/1050] R0[3/150], Temp: 0.9990, Energy: -52.994285+0.000684j
[2025-09-11 15:40:51] [Iter 5/1050] R0[4/150], Temp: 0.9982, Energy: -53.023190+0.000880j
[2025-09-11 15:41:07] [Iter 6/1050] R0[5/150], Temp: 0.9973, Energy: -52.953182-0.001656j
[2025-09-11 15:41:19] [Iter 7/1050] R0[6/150], Temp: 0.9961, Energy: -52.922461+0.002575j
[2025-09-11 15:41:30] [Iter 8/1050] R0[7/150], Temp: 0.9946, Energy: -52.799189+0.000600j
[2025-09-11 15:41:43] [Iter 9/1050] R0[8/150], Temp: 0.9930, Energy: -52.902116-0.000514j
[2025-09-11 15:41:55] [Iter 10/1050] R0[9/150], Temp: 0.9911, Energy: -52.917386+0.006607j
[2025-09-11 15:42:10] [Iter 11/1050] R0[10/150], Temp: 0.9891, Energy: -52.876695+0.003167j
[2025-09-11 15:42:25] [Iter 12/1050] R0[11/150], Temp: 0.9868, Energy: -52.980280+0.001950j
[2025-09-11 15:42:40] [Iter 13/1050] R0[12/150], Temp: 0.9843, Energy: -53.028974+0.001527j
[2025-09-11 15:42:55] [Iter 14/1050] R0[13/150], Temp: 0.9816, Energy: -52.994748+0.000398j
[2025-09-11 15:43:11] [Iter 15/1050] R0[14/150], Temp: 0.9787, Energy: -53.000435-0.000340j
[2025-09-11 15:43:26] [Iter 16/1050] R0[15/150], Temp: 0.9755, Energy: -53.111317-0.001970j
[2025-09-11 15:43:41] [Iter 17/1050] R0[16/150], Temp: 0.9722, Energy: -52.997831-0.002823j
[2025-09-11 15:43:56] [Iter 18/1050] R0[17/150], Temp: 0.9686, Energy: -52.893901-0.003025j
[2025-09-11 15:44:12] [Iter 19/1050] R0[18/150], Temp: 0.9649, Energy: -52.894482-0.004710j
[2025-09-11 15:44:27] [Iter 20/1050] R0[19/150], Temp: 0.9609, Energy: -52.823096-0.002252j
[2025-09-11 15:44:42] [Iter 21/1050] R0[20/150], Temp: 0.9568, Energy: -52.768536+0.001457j
[2025-09-11 15:44:57] [Iter 22/1050] R0[21/150], Temp: 0.9524, Energy: -52.806325-0.007503j
[2025-09-11 15:45:12] [Iter 23/1050] R0[22/150], Temp: 0.9479, Energy: -52.728353+0.000045j
[2025-09-11 15:45:28] [Iter 24/1050] R0[23/150], Temp: 0.9431, Energy: -52.978861-0.004096j
[2025-09-11 15:45:43] [Iter 25/1050] R0[24/150], Temp: 0.9382, Energy: -52.873291+0.002891j
[2025-09-11 15:45:58] [Iter 26/1050] R0[25/150], Temp: 0.9330, Energy: -52.860695+0.001670j
[2025-09-11 15:46:13] [Iter 27/1050] R0[26/150], Temp: 0.9277, Energy: -52.906603-0.002295j
[2025-09-11 15:46:29] [Iter 28/1050] R0[27/150], Temp: 0.9222, Energy: -53.043308+0.001425j
[2025-09-11 15:46:44] [Iter 29/1050] R0[28/150], Temp: 0.9165, Energy: -52.967701+0.001351j
[2025-09-11 15:46:59] [Iter 30/1050] R0[29/150], Temp: 0.9106, Energy: -52.897397-0.002307j
[2025-09-11 15:47:14] [Iter 31/1050] R0[30/150], Temp: 0.9045, Energy: -53.006238-0.004348j
[2025-09-11 15:47:30] [Iter 32/1050] R0[31/150], Temp: 0.8983, Energy: -52.885127+0.000024j
[2025-09-11 15:47:45] [Iter 33/1050] R0[32/150], Temp: 0.8918, Energy: -52.919536-0.005044j
[2025-09-11 15:48:00] [Iter 34/1050] R0[33/150], Temp: 0.8853, Energy: -52.963295-0.002083j
[2025-09-11 15:48:15] [Iter 35/1050] R0[34/150], Temp: 0.8785, Energy: -52.894257-0.000125j
[2025-09-11 15:48:31] [Iter 36/1050] R0[35/150], Temp: 0.8716, Energy: -53.016020-0.000692j
[2025-09-11 15:48:46] [Iter 37/1050] R0[36/150], Temp: 0.8645, Energy: -52.835522+0.003153j
[2025-09-11 15:49:01] [Iter 38/1050] R0[37/150], Temp: 0.8572, Energy: -52.804645-0.001673j
[2025-09-11 15:49:16] [Iter 39/1050] R0[38/150], Temp: 0.8498, Energy: -52.904319-0.005068j
[2025-09-11 15:49:32] [Iter 40/1050] R0[39/150], Temp: 0.8423, Energy: -52.841014-0.003195j
[2025-09-11 15:49:47] [Iter 41/1050] R0[40/150], Temp: 0.8346, Energy: -52.828485-0.001031j
[2025-09-11 15:50:02] [Iter 42/1050] R0[41/150], Temp: 0.8267, Energy: -52.772328+0.000539j
[2025-09-11 15:50:17] [Iter 43/1050] R0[42/150], Temp: 0.8187, Energy: -52.811114+0.000616j
[2025-09-11 15:50:33] [Iter 44/1050] R0[43/150], Temp: 0.8106, Energy: -52.845237+0.000274j
[2025-09-11 15:50:48] [Iter 45/1050] R0[44/150], Temp: 0.8023, Energy: -52.832572-0.002660j
[2025-09-11 15:51:03] [Iter 46/1050] R0[45/150], Temp: 0.7939, Energy: -52.827558-0.002106j
[2025-09-11 15:51:18] [Iter 47/1050] R0[46/150], Temp: 0.7854, Energy: -52.698669-0.000363j
[2025-09-11 15:51:34] [Iter 48/1050] R0[47/150], Temp: 0.7767, Energy: -52.830364-0.000182j
[2025-09-11 15:51:49] [Iter 49/1050] R0[48/150], Temp: 0.7679, Energy: -52.817562-0.000263j
[2025-09-11 15:52:04] [Iter 50/1050] R0[49/150], Temp: 0.7590, Energy: -52.968693+0.001877j
[2025-09-11 15:52:19] [Iter 51/1050] R0[50/150], Temp: 0.7500, Energy: -52.829833-0.000170j
[2025-09-11 15:52:35] [Iter 52/1050] R0[51/150], Temp: 0.7409, Energy: -52.798070-0.003361j
[2025-09-11 15:52:50] [Iter 53/1050] R0[52/150], Temp: 0.7316, Energy: -52.829375-0.002535j
[2025-09-11 15:53:05] [Iter 54/1050] R0[53/150], Temp: 0.7223, Energy: -52.868972+0.003919j
[2025-09-11 15:53:20] [Iter 55/1050] R0[54/150], Temp: 0.7129, Energy: -53.020656+0.000832j
[2025-09-11 15:53:35] [Iter 56/1050] R0[55/150], Temp: 0.7034, Energy: -52.920928+0.002322j
[2025-09-11 15:53:51] [Iter 57/1050] R0[56/150], Temp: 0.6938, Energy: -52.878729+0.002675j
[2025-09-11 15:54:06] [Iter 58/1050] R0[57/150], Temp: 0.6841, Energy: -52.852489-0.000641j
[2025-09-11 15:54:21] [Iter 59/1050] R0[58/150], Temp: 0.6743, Energy: -52.925989+0.002987j
[2025-09-11 15:54:36] [Iter 60/1050] R0[59/150], Temp: 0.6644, Energy: -53.058193+0.002185j
[2025-09-11 15:54:52] [Iter 61/1050] R0[60/150], Temp: 0.6545, Energy: -53.134577+0.000352j
[2025-09-11 15:55:07] [Iter 62/1050] R0[61/150], Temp: 0.6445, Energy: -53.172210+0.000990j
[2025-09-11 15:55:22] [Iter 63/1050] R0[62/150], Temp: 0.6345, Energy: -52.975891-0.000091j
[2025-09-11 15:55:37] [Iter 64/1050] R0[63/150], Temp: 0.6243, Energy: -53.050979+0.006088j
[2025-09-11 15:55:52] [Iter 65/1050] R0[64/150], Temp: 0.6142, Energy: -52.922464-0.003745j
[2025-09-11 15:56:08] [Iter 66/1050] R0[65/150], Temp: 0.6040, Energy: -52.904540+0.002048j
[2025-09-11 15:56:23] [Iter 67/1050] R0[66/150], Temp: 0.5937, Energy: -52.852089-0.005506j
[2025-09-11 15:56:38] [Iter 68/1050] R0[67/150], Temp: 0.5834, Energy: -53.045854-0.001489j
[2025-09-11 15:56:53] [Iter 69/1050] R0[68/150], Temp: 0.5730, Energy: -52.939901-0.001707j
[2025-09-11 15:57:09] [Iter 70/1050] R0[69/150], Temp: 0.5627, Energy: -53.020279+0.001113j
[2025-09-11 15:57:24] [Iter 71/1050] R0[70/150], Temp: 0.5523, Energy: -53.006958+0.000167j
[2025-09-11 15:57:39] [Iter 72/1050] R0[71/150], Temp: 0.5418, Energy: -52.961486+0.001189j
[2025-09-11 15:57:54] [Iter 73/1050] R0[72/150], Temp: 0.5314, Energy: -52.962575+0.002230j
[2025-09-11 15:58:10] [Iter 74/1050] R0[73/150], Temp: 0.5209, Energy: -52.912287-0.000413j
[2025-09-11 15:58:25] [Iter 75/1050] R0[74/150], Temp: 0.5105, Energy: -52.918612-0.001580j
[2025-09-11 15:58:40] [Iter 76/1050] R0[75/150], Temp: 0.5000, Energy: -53.024056-0.001934j
[2025-09-11 15:58:56] [Iter 77/1050] R0[76/150], Temp: 0.4895, Energy: -53.032880+0.004222j
[2025-09-11 15:59:11] [Iter 78/1050] R0[77/150], Temp: 0.4791, Energy: -52.993593+0.001841j
[2025-09-11 15:59:26] [Iter 79/1050] R0[78/150], Temp: 0.4686, Energy: -52.892808-0.001577j
[2025-09-11 15:59:41] [Iter 80/1050] R0[79/150], Temp: 0.4582, Energy: -52.896722-0.003543j
[2025-09-11 15:59:57] [Iter 81/1050] R0[80/150], Temp: 0.4477, Energy: -52.930940+0.002526j
[2025-09-11 16:00:12] [Iter 82/1050] R0[81/150], Temp: 0.4373, Energy: -52.921380+0.001046j
[2025-09-11 16:00:27] [Iter 83/1050] R0[82/150], Temp: 0.4270, Energy: -52.917506+0.000619j
[2025-09-11 16:00:42] [Iter 84/1050] R0[83/150], Temp: 0.4166, Energy: -52.767872+0.002714j
[2025-09-11 16:00:58] [Iter 85/1050] R0[84/150], Temp: 0.4063, Energy: -52.846494+0.000624j
[2025-09-11 16:01:13] [Iter 86/1050] R0[85/150], Temp: 0.3960, Energy: -52.850925+0.001943j
[2025-09-11 16:01:28] [Iter 87/1050] R0[86/150], Temp: 0.3858, Energy: -52.968732-0.004190j
[2025-09-11 16:01:43] [Iter 88/1050] R0[87/150], Temp: 0.3757, Energy: -52.937133-0.001233j
[2025-09-11 16:01:59] [Iter 89/1050] R0[88/150], Temp: 0.3655, Energy: -52.979786+0.002707j
[2025-09-11 16:02:14] [Iter 90/1050] R0[89/150], Temp: 0.3555, Energy: -53.117325-0.000110j
[2025-09-11 16:02:29] [Iter 91/1050] R0[90/150], Temp: 0.3455, Energy: -53.106797+0.000371j
[2025-09-11 16:02:44] [Iter 92/1050] R0[91/150], Temp: 0.3356, Energy: -52.957743+0.001275j
[2025-09-11 16:03:00] [Iter 93/1050] R0[92/150], Temp: 0.3257, Energy: -52.842214-0.003323j
[2025-09-11 16:03:15] [Iter 94/1050] R0[93/150], Temp: 0.3159, Energy: -52.803026+0.000304j
[2025-09-11 16:03:30] [Iter 95/1050] R0[94/150], Temp: 0.3062, Energy: -52.979009+0.004097j
[2025-09-11 16:03:45] [Iter 96/1050] R0[95/150], Temp: 0.2966, Energy: -52.910278+0.000314j
[2025-09-11 16:04:01] [Iter 97/1050] R0[96/150], Temp: 0.2871, Energy: -53.037551+0.001002j
[2025-09-11 16:04:16] [Iter 98/1050] R0[97/150], Temp: 0.2777, Energy: -52.997465-0.002094j
[2025-09-11 16:04:31] [Iter 99/1050] R0[98/150], Temp: 0.2684, Energy: -52.980752-0.001442j
[2025-09-11 16:04:46] [Iter 100/1050] R0[99/150], Temp: 0.2591, Energy: -52.868018+0.001014j
[2025-09-11 16:05:02] [Iter 101/1050] R0[100/150], Temp: 0.2500, Energy: -52.859700+0.003537j
[2025-09-11 16:05:17] [Iter 102/1050] R0[101/150], Temp: 0.2410, Energy: -52.899487+0.001121j
[2025-09-11 16:05:32] [Iter 103/1050] R0[102/150], Temp: 0.2321, Energy: -52.816979-0.000661j
[2025-09-11 16:05:48] [Iter 104/1050] R0[103/150], Temp: 0.2233, Energy: -52.825433-0.000391j
[2025-09-11 16:06:03] [Iter 105/1050] R0[104/150], Temp: 0.2146, Energy: -52.760276+0.001015j
[2025-09-11 16:06:03] ✓ Checkpoint saved: checkpoint_iter_000105.pkl
[2025-09-11 16:06:18] [Iter 106/1050] R0[105/150], Temp: 0.2061, Energy: -52.772211-0.001434j
[2025-09-11 16:06:33] [Iter 107/1050] R0[106/150], Temp: 0.1977, Energy: -52.828590-0.002842j
[2025-09-11 16:06:48] [Iter 108/1050] R0[107/150], Temp: 0.1894, Energy: -52.890809+0.000487j
[2025-09-11 16:07:04] [Iter 109/1050] R0[108/150], Temp: 0.1813, Energy: -52.758412+0.001607j
[2025-09-11 16:07:19] [Iter 110/1050] R0[109/150], Temp: 0.1733, Energy: -52.811364+0.000625j
[2025-09-11 16:07:34] [Iter 111/1050] R0[110/150], Temp: 0.1654, Energy: -52.683370-0.000012j
[2025-09-11 16:07:49] [Iter 112/1050] R0[111/150], Temp: 0.1577, Energy: -52.910437+0.003351j
[2025-09-11 16:08:04] [Iter 113/1050] R0[112/150], Temp: 0.1502, Energy: -52.847335+0.002997j
[2025-09-11 16:08:20] [Iter 114/1050] R0[113/150], Temp: 0.1428, Energy: -52.895781+0.001207j
[2025-09-11 16:08:35] [Iter 115/1050] R0[114/150], Temp: 0.1355, Energy: -52.821484+0.002618j
[2025-09-11 16:08:50] [Iter 116/1050] R0[115/150], Temp: 0.1284, Energy: -52.844788+0.000349j
[2025-09-11 16:09:05] [Iter 117/1050] R0[116/150], Temp: 0.1215, Energy: -52.879996-0.000833j
[2025-09-11 16:09:21] [Iter 118/1050] R0[117/150], Temp: 0.1147, Energy: -52.751399-0.001224j
[2025-09-11 16:09:36] [Iter 119/1050] R0[118/150], Temp: 0.1082, Energy: -52.805848-0.001585j
[2025-09-11 16:09:51] [Iter 120/1050] R0[119/150], Temp: 0.1017, Energy: -52.824902+0.001683j
[2025-09-11 16:10:06] [Iter 121/1050] R0[120/150], Temp: 0.0955, Energy: -52.900601-0.003304j
[2025-09-11 16:10:22] [Iter 122/1050] R0[121/150], Temp: 0.0894, Energy: -52.891590-0.000451j
[2025-09-11 16:10:37] [Iter 123/1050] R0[122/150], Temp: 0.0835, Energy: -53.043684-0.003995j
[2025-09-11 16:10:52] [Iter 124/1050] R0[123/150], Temp: 0.0778, Energy: -52.998474+0.001421j
[2025-09-11 16:11:07] [Iter 125/1050] R0[124/150], Temp: 0.0723, Energy: -52.924401+0.002025j
[2025-09-11 16:11:22] [Iter 126/1050] R0[125/150], Temp: 0.0670, Energy: -52.764834-0.004672j
[2025-09-11 16:11:38] [Iter 127/1050] R0[126/150], Temp: 0.0618, Energy: -52.745738-0.000489j
[2025-09-11 16:11:53] [Iter 128/1050] R0[127/150], Temp: 0.0569, Energy: -52.712289-0.003494j
[2025-09-11 16:12:08] [Iter 129/1050] R0[128/150], Temp: 0.0521, Energy: -52.906773+0.002863j
[2025-09-11 16:12:23] [Iter 130/1050] R0[129/150], Temp: 0.0476, Energy: -52.826799+0.001485j
[2025-09-11 16:12:39] [Iter 131/1050] R0[130/150], Temp: 0.0432, Energy: -52.907961-0.006174j
[2025-09-11 16:12:54] [Iter 132/1050] R0[131/150], Temp: 0.0391, Energy: -52.774351+0.002189j
[2025-09-11 16:13:09] [Iter 133/1050] R0[132/150], Temp: 0.0351, Energy: -52.879563+0.002413j
[2025-09-11 16:13:24] [Iter 134/1050] R0[133/150], Temp: 0.0314, Energy: -52.881946-0.000264j
[2025-09-11 16:13:40] [Iter 135/1050] R0[134/150], Temp: 0.0278, Energy: -52.912214-0.004582j
[2025-09-11 16:13:55] [Iter 136/1050] R0[135/150], Temp: 0.0245, Energy: -52.864030+0.001261j
[2025-09-11 16:14:10] [Iter 137/1050] R0[136/150], Temp: 0.0213, Energy: -52.993851-0.002166j
[2025-09-11 16:14:25] [Iter 138/1050] R0[137/150], Temp: 0.0184, Energy: -52.892597+0.000667j
[2025-09-11 16:14:40] [Iter 139/1050] R0[138/150], Temp: 0.0157, Energy: -53.010511-0.000770j
[2025-09-11 16:14:56] [Iter 140/1050] R0[139/150], Temp: 0.0132, Energy: -52.881227-0.001267j
[2025-09-11 16:15:11] [Iter 141/1050] R0[140/150], Temp: 0.0109, Energy: -52.815300+0.000468j
[2025-09-11 16:15:26] [Iter 142/1050] R0[141/150], Temp: 0.0089, Energy: -52.837871-0.000201j
[2025-09-11 16:15:41] [Iter 143/1050] R0[142/150], Temp: 0.0070, Energy: -52.737688-0.001020j
[2025-09-11 16:15:51] [Iter 144/1050] R0[143/150], Temp: 0.0054, Energy: -52.694075-0.004853j
[2025-09-11 16:16:02] [Iter 145/1050] R0[144/150], Temp: 0.0039, Energy: -52.776132+0.003997j
[2025-09-11 16:16:12] [Iter 146/1050] R0[145/150], Temp: 0.0027, Energy: -52.963898+0.000473j
[2025-09-11 16:16:22] [Iter 147/1050] R0[146/150], Temp: 0.0018, Energy: -52.851588-0.000633j
[2025-09-11 16:16:32] [Iter 148/1050] R0[147/150], Temp: 0.0010, Energy: -52.883057-0.000406j
[2025-09-11 16:16:42] [Iter 149/1050] R0[148/150], Temp: 0.0004, Energy: -52.923191-0.005045j
[2025-09-11 16:16:55] [Iter 150/1050] R0[149/150], Temp: 0.0001, Energy: -52.846754+0.004736j
[2025-09-11 16:16:55] RESTART #1 | Period: 300
[2025-09-11 16:17:10] [Iter 151/1050] R1[0/300], Temp: 1.0000, Energy: -52.799755+0.002610j
[2025-09-11 16:17:25] [Iter 152/1050] R1[1/300], Temp: 1.0000, Energy: -52.800393+0.003151j
[2025-09-11 16:17:40] [Iter 153/1050] R1[2/300], Temp: 0.9999, Energy: -53.047494-0.001668j
[2025-09-11 16:17:56] [Iter 154/1050] R1[3/300], Temp: 0.9998, Energy: -52.921846+0.004706j
[2025-09-11 16:18:11] [Iter 155/1050] R1[4/300], Temp: 0.9996, Energy: -52.929205+0.000461j
[2025-09-11 16:18:26] [Iter 156/1050] R1[5/300], Temp: 0.9993, Energy: -53.002658-0.001124j
[2025-09-11 16:18:42] [Iter 157/1050] R1[6/300], Temp: 0.9990, Energy: -52.874257-0.000528j
[2025-09-11 16:18:57] [Iter 158/1050] R1[7/300], Temp: 0.9987, Energy: -52.982169+0.001992j
[2025-09-11 16:19:12] [Iter 159/1050] R1[8/300], Temp: 0.9982, Energy: -52.979699-0.003987j
[2025-09-11 16:19:28] [Iter 160/1050] R1[9/300], Temp: 0.9978, Energy: -52.805404-0.002358j
[2025-09-11 16:19:43] [Iter 161/1050] R1[10/300], Temp: 0.9973, Energy: -52.891126-0.002614j
[2025-09-11 16:19:58] [Iter 162/1050] R1[11/300], Temp: 0.9967, Energy: -52.902055-0.003289j
[2025-09-11 16:20:13] [Iter 163/1050] R1[12/300], Temp: 0.9961, Energy: -52.928866+0.000002j
[2025-09-11 16:20:29] [Iter 164/1050] R1[13/300], Temp: 0.9954, Energy: -52.884808+0.002032j
[2025-09-11 16:20:44] [Iter 165/1050] R1[14/300], Temp: 0.9946, Energy: -52.898287+0.000406j
[2025-09-11 16:20:59] [Iter 166/1050] R1[15/300], Temp: 0.9938, Energy: -52.818330-0.001193j
[2025-09-11 16:21:14] [Iter 167/1050] R1[16/300], Temp: 0.9930, Energy: -52.740076+0.001603j
[2025-09-11 16:21:30] [Iter 168/1050] R1[17/300], Temp: 0.9921, Energy: -52.724680+0.003501j
[2025-09-11 16:21:45] [Iter 169/1050] R1[18/300], Temp: 0.9911, Energy: -52.819805+0.000761j
[2025-09-11 16:22:00] [Iter 170/1050] R1[19/300], Temp: 0.9901, Energy: -52.792205+0.005332j
[2025-09-11 16:22:16] [Iter 171/1050] R1[20/300], Temp: 0.9891, Energy: -52.847206-0.000515j
[2025-09-11 16:22:31] [Iter 172/1050] R1[21/300], Temp: 0.9880, Energy: -52.854645+0.001801j
[2025-09-11 16:22:46] [Iter 173/1050] R1[22/300], Temp: 0.9868, Energy: -52.833102-0.001718j
[2025-09-11 16:23:01] [Iter 174/1050] R1[23/300], Temp: 0.9856, Energy: -52.816241-0.002012j
[2025-09-11 16:23:16] [Iter 175/1050] R1[24/300], Temp: 0.9843, Energy: -52.810428-0.006818j
[2025-09-11 16:23:32] [Iter 176/1050] R1[25/300], Temp: 0.9830, Energy: -52.860419-0.000956j
[2025-09-11 16:23:47] [Iter 177/1050] R1[26/300], Temp: 0.9816, Energy: -52.929937-0.002504j
[2025-09-11 16:24:02] [Iter 178/1050] R1[27/300], Temp: 0.9801, Energy: -52.811873+0.000264j
[2025-09-11 16:24:18] [Iter 179/1050] R1[28/300], Temp: 0.9787, Energy: -52.936524+0.002854j
[2025-09-11 16:24:33] [Iter 180/1050] R1[29/300], Temp: 0.9771, Energy: -52.839856-0.004295j
[2025-09-11 16:24:48] [Iter 181/1050] R1[30/300], Temp: 0.9755, Energy: -52.745091-0.000211j
[2025-09-11 16:25:03] [Iter 182/1050] R1[31/300], Temp: 0.9739, Energy: -52.861101-0.003284j
[2025-09-11 16:25:19] [Iter 183/1050] R1[32/300], Temp: 0.9722, Energy: -52.925927+0.001592j
[2025-09-11 16:25:34] [Iter 184/1050] R1[33/300], Temp: 0.9704, Energy: -52.977378+0.002863j
[2025-09-11 16:25:49] [Iter 185/1050] R1[34/300], Temp: 0.9686, Energy: -52.949618-0.000944j
[2025-09-11 16:26:05] [Iter 186/1050] R1[35/300], Temp: 0.9668, Energy: -52.896230-0.000743j
[2025-09-11 16:26:20] [Iter 187/1050] R1[36/300], Temp: 0.9649, Energy: -52.889545-0.000193j
[2025-09-11 16:26:30] [Iter 188/1050] R1[37/300], Temp: 0.9629, Energy: -52.951484+0.002358j
[2025-09-11 16:26:43] [Iter 189/1050] R1[38/300], Temp: 0.9609, Energy: -52.970256-0.002032j
[2025-09-11 16:26:54] [Iter 190/1050] R1[39/300], Temp: 0.9589, Energy: -53.075846-0.001737j
[2025-09-11 16:27:07] [Iter 191/1050] R1[40/300], Temp: 0.9568, Energy: -53.024598-0.003792j
[2025-09-11 16:27:23] [Iter 192/1050] R1[41/300], Temp: 0.9546, Energy: -52.877737+0.002115j
[2025-09-11 16:27:38] [Iter 193/1050] R1[42/300], Temp: 0.9524, Energy: -52.927437-0.008699j
[2025-09-11 16:27:53] [Iter 194/1050] R1[43/300], Temp: 0.9502, Energy: -52.896877+0.000785j
[2025-09-11 16:28:08] [Iter 195/1050] R1[44/300], Temp: 0.9479, Energy: -52.789558-0.001640j
[2025-09-11 16:28:24] [Iter 196/1050] R1[45/300], Temp: 0.9455, Energy: -52.895348+0.003674j
[2025-09-11 16:28:39] [Iter 197/1050] R1[46/300], Temp: 0.9431, Energy: -52.736585+0.000636j
[2025-09-11 16:28:54] [Iter 198/1050] R1[47/300], Temp: 0.9407, Energy: -52.832275+0.000800j
[2025-09-11 16:29:09] [Iter 199/1050] R1[48/300], Temp: 0.9382, Energy: -52.784115+0.000606j
[2025-09-11 16:29:24] [Iter 200/1050] R1[49/300], Temp: 0.9356, Energy: -52.811233-0.002546j
[2025-09-11 16:29:40] [Iter 201/1050] R1[50/300], Temp: 0.9330, Energy: -52.803064+0.001395j
[2025-09-11 16:29:55] [Iter 202/1050] R1[51/300], Temp: 0.9304, Energy: -52.868008-0.001224j
[2025-09-11 16:30:10] [Iter 203/1050] R1[52/300], Temp: 0.9277, Energy: -52.863612+0.000782j
[2025-09-11 16:30:25] [Iter 204/1050] R1[53/300], Temp: 0.9249, Energy: -52.740552+0.001993j
[2025-09-11 16:30:41] [Iter 205/1050] R1[54/300], Temp: 0.9222, Energy: -52.849129-0.003328j
[2025-09-11 16:30:56] [Iter 206/1050] R1[55/300], Temp: 0.9193, Energy: -52.844505-0.001245j
[2025-09-11 16:31:11] [Iter 207/1050] R1[56/300], Temp: 0.9165, Energy: -52.859042-0.000745j
[2025-09-11 16:31:26] [Iter 208/1050] R1[57/300], Temp: 0.9135, Energy: -52.816235-0.000614j
[2025-09-11 16:31:42] [Iter 209/1050] R1[58/300], Temp: 0.9106, Energy: -52.892858+0.000092j
[2025-09-11 16:31:57] [Iter 210/1050] R1[59/300], Temp: 0.9076, Energy: -52.801081-0.000583j
[2025-09-11 16:31:57] ✓ Checkpoint saved: checkpoint_iter_000210.pkl
[2025-09-11 16:32:12] [Iter 211/1050] R1[60/300], Temp: 0.9045, Energy: -52.797248+0.002196j
[2025-09-11 16:32:27] [Iter 212/1050] R1[61/300], Temp: 0.9014, Energy: -52.808744+0.005122j
[2025-09-11 16:32:43] [Iter 213/1050] R1[62/300], Temp: 0.8983, Energy: -52.767164-0.001600j
[2025-09-11 16:32:58] [Iter 214/1050] R1[63/300], Temp: 0.8951, Energy: -52.828389+0.004654j
[2025-09-11 16:33:13] [Iter 215/1050] R1[64/300], Temp: 0.8918, Energy: -52.795443+0.002314j
[2025-09-11 16:33:28] [Iter 216/1050] R1[65/300], Temp: 0.8886, Energy: -52.834187-0.001144j
[2025-09-11 16:33:43] [Iter 217/1050] R1[66/300], Temp: 0.8853, Energy: -52.925353+0.001798j
[2025-09-11 16:33:59] [Iter 218/1050] R1[67/300], Temp: 0.8819, Energy: -52.861614-0.000363j
[2025-09-11 16:34:14] [Iter 219/1050] R1[68/300], Temp: 0.8785, Energy: -52.963400+0.000211j
[2025-09-11 16:34:29] [Iter 220/1050] R1[69/300], Temp: 0.8751, Energy: -52.934451+0.001483j
[2025-09-11 16:34:44] [Iter 221/1050] R1[70/300], Temp: 0.8716, Energy: -52.851427-0.002872j
[2025-09-11 16:35:00] [Iter 222/1050] R1[71/300], Temp: 0.8680, Energy: -52.890356+0.001171j
[2025-09-11 16:35:15] [Iter 223/1050] R1[72/300], Temp: 0.8645, Energy: -52.867062-0.001652j
[2025-09-11 16:35:30] [Iter 224/1050] R1[73/300], Temp: 0.8609, Energy: -52.813085-0.001557j
[2025-09-11 16:35:45] [Iter 225/1050] R1[74/300], Temp: 0.8572, Energy: -52.955622-0.005347j
[2025-09-11 16:36:00] [Iter 226/1050] R1[75/300], Temp: 0.8536, Energy: -52.900595-0.002066j
[2025-09-11 16:36:16] [Iter 227/1050] R1[76/300], Temp: 0.8498, Energy: -52.952350-0.004699j
[2025-09-11 16:36:31] [Iter 228/1050] R1[77/300], Temp: 0.8461, Energy: -53.017117+0.002781j
[2025-09-11 16:36:46] [Iter 229/1050] R1[78/300], Temp: 0.8423, Energy: -52.919635-0.000543j
[2025-09-11 16:37:01] [Iter 230/1050] R1[79/300], Temp: 0.8384, Energy: -53.064359+0.002114j
[2025-09-11 16:37:16] [Iter 231/1050] R1[80/300], Temp: 0.8346, Energy: -53.015280-0.001305j
[2025-09-11 16:37:32] [Iter 232/1050] R1[81/300], Temp: 0.8307, Energy: -52.991261-0.002466j
[2025-09-11 16:37:47] [Iter 233/1050] R1[82/300], Temp: 0.8267, Energy: -53.052468+0.003642j
[2025-09-11 16:38:02] [Iter 234/1050] R1[83/300], Temp: 0.8227, Energy: -52.931034-0.001534j
[2025-09-11 16:38:17] [Iter 235/1050] R1[84/300], Temp: 0.8187, Energy: -52.926065+0.000639j
[2025-09-11 16:38:33] [Iter 236/1050] R1[85/300], Temp: 0.8147, Energy: -53.009193+0.007170j
[2025-09-11 16:38:48] [Iter 237/1050] R1[86/300], Temp: 0.8106, Energy: -52.983783-0.000414j
[2025-09-11 16:39:03] [Iter 238/1050] R1[87/300], Temp: 0.8065, Energy: -52.976359-0.001152j
[2025-09-11 16:39:19] [Iter 239/1050] R1[88/300], Temp: 0.8023, Energy: -52.903226+0.003249j
[2025-09-11 16:39:34] [Iter 240/1050] R1[89/300], Temp: 0.7981, Energy: -53.020981+0.004298j
[2025-09-11 16:39:49] [Iter 241/1050] R1[90/300], Temp: 0.7939, Energy: -52.894205-0.000563j
[2025-09-11 16:40:04] [Iter 242/1050] R1[91/300], Temp: 0.7896, Energy: -52.889508-0.004268j
[2025-09-11 16:40:19] [Iter 243/1050] R1[92/300], Temp: 0.7854, Energy: -52.857369+0.001786j
[2025-09-11 16:40:35] [Iter 244/1050] R1[93/300], Temp: 0.7810, Energy: -52.814437+0.000986j
[2025-09-11 16:40:50] [Iter 245/1050] R1[94/300], Temp: 0.7767, Energy: -52.930751+0.002486j
[2025-09-11 16:41:05] [Iter 246/1050] R1[95/300], Temp: 0.7723, Energy: -52.853197-0.002352j
[2025-09-11 16:41:20] [Iter 247/1050] R1[96/300], Temp: 0.7679, Energy: -52.984254-0.000333j
[2025-09-11 16:41:36] [Iter 248/1050] R1[97/300], Temp: 0.7635, Energy: -52.800063-0.002806j
[2025-09-11 16:41:51] [Iter 249/1050] R1[98/300], Temp: 0.7590, Energy: -52.950958+0.003440j
[2025-09-11 16:42:06] [Iter 250/1050] R1[99/300], Temp: 0.7545, Energy: -52.859313+0.000445j
[2025-09-11 16:42:21] [Iter 251/1050] R1[100/300], Temp: 0.7500, Energy: -53.031224+0.000693j
[2025-09-11 16:42:36] [Iter 252/1050] R1[101/300], Temp: 0.7455, Energy: -52.920961-0.001165j
[2025-09-11 16:42:52] [Iter 253/1050] R1[102/300], Temp: 0.7409, Energy: -52.814064-0.000517j
[2025-09-11 16:43:07] [Iter 254/1050] R1[103/300], Temp: 0.7363, Energy: -52.898573+0.000117j
[2025-09-11 16:43:22] [Iter 255/1050] R1[104/300], Temp: 0.7316, Energy: -52.901756+0.000957j
[2025-09-11 16:43:37] [Iter 256/1050] R1[105/300], Temp: 0.7270, Energy: -52.913616-0.002155j
[2025-09-11 16:43:52] [Iter 257/1050] R1[106/300], Temp: 0.7223, Energy: -52.880188-0.003122j
[2025-09-11 16:44:08] [Iter 258/1050] R1[107/300], Temp: 0.7176, Energy: -52.728589-0.000897j
[2025-09-11 16:44:23] [Iter 259/1050] R1[108/300], Temp: 0.7129, Energy: -52.968904+0.001881j
[2025-09-11 16:44:38] [Iter 260/1050] R1[109/300], Temp: 0.7081, Energy: -52.973192-0.000513j
[2025-09-11 16:44:53] [Iter 261/1050] R1[110/300], Temp: 0.7034, Energy: -52.941232+0.000503j
[2025-09-11 16:45:08] [Iter 262/1050] R1[111/300], Temp: 0.6986, Energy: -52.869571-0.001601j
[2025-09-11 16:45:23] [Iter 263/1050] R1[112/300], Temp: 0.6938, Energy: -52.970860+0.000278j
[2025-09-11 16:45:39] [Iter 264/1050] R1[113/300], Temp: 0.6889, Energy: -52.836028+0.002200j
[2025-09-11 16:45:54] [Iter 265/1050] R1[114/300], Temp: 0.6841, Energy: -52.911901+0.002382j
[2025-09-11 16:46:09] [Iter 266/1050] R1[115/300], Temp: 0.6792, Energy: -52.822345+0.002481j
[2025-09-11 16:46:24] [Iter 267/1050] R1[116/300], Temp: 0.6743, Energy: -52.875445+0.001732j
[2025-09-11 16:46:39] [Iter 268/1050] R1[117/300], Temp: 0.6694, Energy: -52.837057+0.001031j
[2025-09-11 16:46:54] [Iter 269/1050] R1[118/300], Temp: 0.6644, Energy: -52.843711+0.001426j
[2025-09-11 16:47:10] [Iter 270/1050] R1[119/300], Temp: 0.6595, Energy: -52.860557-0.002692j
[2025-09-11 16:47:25] [Iter 271/1050] R1[120/300], Temp: 0.6545, Energy: -52.962998+0.000805j
[2025-09-11 16:47:40] [Iter 272/1050] R1[121/300], Temp: 0.6495, Energy: -52.930405-0.000529j
[2025-09-11 16:47:55] [Iter 273/1050] R1[122/300], Temp: 0.6445, Energy: -52.953607+0.000826j
[2025-09-11 16:48:10] [Iter 274/1050] R1[123/300], Temp: 0.6395, Energy: -53.009407-0.003709j
[2025-09-11 16:48:26] [Iter 275/1050] R1[124/300], Temp: 0.6345, Energy: -52.834069+0.001278j
[2025-09-11 16:48:41] [Iter 276/1050] R1[125/300], Temp: 0.6294, Energy: -52.955650+0.001791j
[2025-09-11 16:48:56] [Iter 277/1050] R1[126/300], Temp: 0.6243, Energy: -53.014754+0.000197j
[2025-09-11 16:49:11] [Iter 278/1050] R1[127/300], Temp: 0.6193, Energy: -52.735518-0.000665j
[2025-09-11 16:49:26] [Iter 279/1050] R1[128/300], Temp: 0.6142, Energy: -52.822704+0.000091j
[2025-09-11 16:49:42] [Iter 280/1050] R1[129/300], Temp: 0.6091, Energy: -52.869433-0.004218j
[2025-09-11 16:49:57] [Iter 281/1050] R1[130/300], Temp: 0.6040, Energy: -52.966185+0.002968j
[2025-09-11 16:50:12] [Iter 282/1050] R1[131/300], Temp: 0.5988, Energy: -52.902015+0.001062j
[2025-09-11 16:50:27] [Iter 283/1050] R1[132/300], Temp: 0.5937, Energy: -52.940968+0.001851j
[2025-09-11 16:50:43] [Iter 284/1050] R1[133/300], Temp: 0.5885, Energy: -52.932753-0.004013j
[2025-09-11 16:50:58] [Iter 285/1050] R1[134/300], Temp: 0.5834, Energy: -52.942798-0.000807j
[2025-09-11 16:51:13] [Iter 286/1050] R1[135/300], Temp: 0.5782, Energy: -52.905535-0.000099j
[2025-09-11 16:51:28] [Iter 287/1050] R1[136/300], Temp: 0.5730, Energy: -52.915673+0.000885j
[2025-09-11 16:51:43] [Iter 288/1050] R1[137/300], Temp: 0.5679, Energy: -52.988256-0.000365j
[2025-09-11 16:51:59] [Iter 289/1050] R1[138/300], Temp: 0.5627, Energy: -52.915233+0.001267j
[2025-09-11 16:52:14] [Iter 290/1050] R1[139/300], Temp: 0.5575, Energy: -52.901641-0.000078j
[2025-09-11 16:52:29] [Iter 291/1050] R1[140/300], Temp: 0.5523, Energy: -52.918178+0.000246j
[2025-09-11 16:52:44] [Iter 292/1050] R1[141/300], Temp: 0.5471, Energy: -52.925486-0.004503j
[2025-09-11 16:53:00] [Iter 293/1050] R1[142/300], Temp: 0.5418, Energy: -52.927454+0.000563j
[2025-09-11 16:53:15] [Iter 294/1050] R1[143/300], Temp: 0.5366, Energy: -52.916842-0.001059j
[2025-09-11 16:53:30] [Iter 295/1050] R1[144/300], Temp: 0.5314, Energy: -52.883600-0.000975j
[2025-09-11 16:53:45] [Iter 296/1050] R1[145/300], Temp: 0.5262, Energy: -52.795359-0.000930j
[2025-09-11 16:54:01] [Iter 297/1050] R1[146/300], Temp: 0.5209, Energy: -52.902976-0.002948j
[2025-09-11 16:54:16] [Iter 298/1050] R1[147/300], Temp: 0.5157, Energy: -52.820077+0.001675j
[2025-09-11 16:54:31] [Iter 299/1050] R1[148/300], Temp: 0.5105, Energy: -52.808250+0.004845j
[2025-09-11 16:54:46] [Iter 300/1050] R1[149/300], Temp: 0.5052, Energy: -52.822743-0.001917j
[2025-09-11 16:55:02] [Iter 301/1050] R1[150/300], Temp: 0.5000, Energy: -52.801823-0.002397j
[2025-09-11 16:55:17] [Iter 302/1050] R1[151/300], Temp: 0.4948, Energy: -52.829803-0.005621j
[2025-09-11 16:55:32] [Iter 303/1050] R1[152/300], Temp: 0.4895, Energy: -52.947420+0.001200j
[2025-09-11 16:55:47] [Iter 304/1050] R1[153/300], Temp: 0.4843, Energy: -52.944140+0.004158j
[2025-09-11 16:56:02] [Iter 305/1050] R1[154/300], Temp: 0.4791, Energy: -52.823725+0.004014j
[2025-09-11 16:56:18] [Iter 306/1050] R1[155/300], Temp: 0.4738, Energy: -52.922733+0.002074j
[2025-09-11 16:56:33] [Iter 307/1050] R1[156/300], Temp: 0.4686, Energy: -52.870313+0.000801j
[2025-09-11 16:56:48] [Iter 308/1050] R1[157/300], Temp: 0.4634, Energy: -52.966668-0.002430j
[2025-09-11 16:57:03] [Iter 309/1050] R1[158/300], Temp: 0.4582, Energy: -52.984896-0.002040j
[2025-09-11 16:57:18] [Iter 310/1050] R1[159/300], Temp: 0.4529, Energy: -53.010888-0.002830j
[2025-09-11 16:57:34] [Iter 311/1050] R1[160/300], Temp: 0.4477, Energy: -52.889504+0.001942j
[2025-09-11 16:57:49] [Iter 312/1050] R1[161/300], Temp: 0.4425, Energy: -52.869965+0.001768j
[2025-09-11 16:58:04] [Iter 313/1050] R1[162/300], Temp: 0.4373, Energy: -52.843558+0.003621j
[2025-09-11 16:58:19] [Iter 314/1050] R1[163/300], Temp: 0.4321, Energy: -52.933320+0.003098j
[2025-09-11 16:58:35] [Iter 315/1050] R1[164/300], Temp: 0.4270, Energy: -52.851936-0.002000j
[2025-09-11 16:58:35] ✓ Checkpoint saved: checkpoint_iter_000315.pkl
[2025-09-11 16:58:50] [Iter 316/1050] R1[165/300], Temp: 0.4218, Energy: -52.930877-0.001806j
[2025-09-11 16:59:05] [Iter 317/1050] R1[166/300], Temp: 0.4166, Energy: -52.820560-0.003953j
[2025-09-11 16:59:20] [Iter 318/1050] R1[167/300], Temp: 0.4115, Energy: -52.735286-0.002025j
[2025-09-11 16:59:35] [Iter 319/1050] R1[168/300], Temp: 0.4063, Energy: -52.790520-0.002862j
[2025-09-11 16:59:50] [Iter 320/1050] R1[169/300], Temp: 0.4012, Energy: -52.848865-0.000815j
[2025-09-11 17:00:06] [Iter 321/1050] R1[170/300], Temp: 0.3960, Energy: -52.840902-0.002429j
[2025-09-11 17:00:21] [Iter 322/1050] R1[171/300], Temp: 0.3909, Energy: -52.893763-0.001405j
[2025-09-11 17:00:31] [Iter 323/1050] R1[172/300], Temp: 0.3858, Energy: -52.900538+0.003685j
[2025-09-11 17:00:41] [Iter 324/1050] R1[173/300], Temp: 0.3807, Energy: -52.929821-0.004756j
[2025-09-11 17:00:51] [Iter 325/1050] R1[174/300], Temp: 0.3757, Energy: -52.735995-0.001084j
[2025-09-11 17:01:02] [Iter 326/1050] R1[175/300], Temp: 0.3706, Energy: -52.859544-0.002321j
[2025-09-11 17:01:12] [Iter 327/1050] R1[176/300], Temp: 0.3655, Energy: -52.925000+0.000171j
[2025-09-11 17:01:22] [Iter 328/1050] R1[177/300], Temp: 0.3605, Energy: -52.894610-0.000794j
[2025-09-11 17:01:37] [Iter 329/1050] R1[178/300], Temp: 0.3555, Energy: -52.834358+0.004643j
[2025-09-11 17:01:52] [Iter 330/1050] R1[179/300], Temp: 0.3505, Energy: -52.778862-0.000705j
[2025-09-11 17:02:07] [Iter 331/1050] R1[180/300], Temp: 0.3455, Energy: -52.841114+0.002679j
[2025-09-11 17:02:23] [Iter 332/1050] R1[181/300], Temp: 0.3405, Energy: -52.713503-0.000358j
[2025-09-11 17:02:38] [Iter 333/1050] R1[182/300], Temp: 0.3356, Energy: -52.800418-0.001058j
[2025-09-11 17:02:53] [Iter 334/1050] R1[183/300], Temp: 0.3306, Energy: -52.931072-0.003334j
[2025-09-11 17:03:08] [Iter 335/1050] R1[184/300], Temp: 0.3257, Energy: -52.952349+0.003433j
[2025-09-11 17:03:24] [Iter 336/1050] R1[185/300], Temp: 0.3208, Energy: -53.008189+0.002901j
[2025-09-11 17:03:39] [Iter 337/1050] R1[186/300], Temp: 0.3159, Energy: -53.005284+0.002697j
[2025-09-11 17:03:54] [Iter 338/1050] R1[187/300], Temp: 0.3111, Energy: -53.078779+0.004567j
[2025-09-11 17:04:10] [Iter 339/1050] R1[188/300], Temp: 0.3062, Energy: -53.086649+0.000942j
[2025-09-11 17:04:25] [Iter 340/1050] R1[189/300], Temp: 0.3014, Energy: -53.114670-0.000918j
[2025-09-11 17:04:40] [Iter 341/1050] R1[190/300], Temp: 0.2966, Energy: -52.956771+0.001903j
[2025-09-11 17:04:55] [Iter 342/1050] R1[191/300], Temp: 0.2919, Energy: -52.888111+0.001495j
[2025-09-11 17:05:11] [Iter 343/1050] R1[192/300], Temp: 0.2871, Energy: -52.874682-0.000484j
[2025-09-11 17:05:26] [Iter 344/1050] R1[193/300], Temp: 0.2824, Energy: -52.988672+0.002073j
[2025-09-11 17:05:41] [Iter 345/1050] R1[194/300], Temp: 0.2777, Energy: -52.792168-0.001385j
[2025-09-11 17:05:56] [Iter 346/1050] R1[195/300], Temp: 0.2730, Energy: -52.784005+0.003581j
[2025-09-11 17:06:12] [Iter 347/1050] R1[196/300], Temp: 0.2684, Energy: -52.802200+0.001023j
[2025-09-11 17:06:27] [Iter 348/1050] R1[197/300], Temp: 0.2637, Energy: -52.824343+0.001652j
[2025-09-11 17:06:42] [Iter 349/1050] R1[198/300], Temp: 0.2591, Energy: -52.815997-0.002444j
[2025-09-11 17:06:58] [Iter 350/1050] R1[199/300], Temp: 0.2545, Energy: -52.896725-0.001656j
[2025-09-11 17:07:13] [Iter 351/1050] R1[200/300], Temp: 0.2500, Energy: -52.850555-0.004287j
[2025-09-11 17:07:28] [Iter 352/1050] R1[201/300], Temp: 0.2455, Energy: -52.818418-0.002766j
[2025-09-11 17:07:43] [Iter 353/1050] R1[202/300], Temp: 0.2410, Energy: -52.770131-0.000369j
[2025-09-11 17:07:59] [Iter 354/1050] R1[203/300], Temp: 0.2365, Energy: -52.866351-0.002174j
[2025-09-11 17:08:14] [Iter 355/1050] R1[204/300], Temp: 0.2321, Energy: -52.858044-0.001125j
[2025-09-11 17:08:29] [Iter 356/1050] R1[205/300], Temp: 0.2277, Energy: -52.865736-0.001711j
[2025-09-11 17:08:45] [Iter 357/1050] R1[206/300], Temp: 0.2233, Energy: -52.916726+0.003653j
[2025-09-11 17:09:00] [Iter 358/1050] R1[207/300], Temp: 0.2190, Energy: -52.820466+0.000530j
[2025-09-11 17:09:15] [Iter 359/1050] R1[208/300], Temp: 0.2146, Energy: -52.846344+0.002167j
[2025-09-11 17:09:31] [Iter 360/1050] R1[209/300], Temp: 0.2104, Energy: -52.927305+0.002186j
[2025-09-11 17:09:46] [Iter 361/1050] R1[210/300], Temp: 0.2061, Energy: -52.958550+0.001261j
[2025-09-11 17:10:01] [Iter 362/1050] R1[211/300], Temp: 0.2019, Energy: -52.889385-0.002114j
[2025-09-11 17:10:16] [Iter 363/1050] R1[212/300], Temp: 0.1977, Energy: -52.859954+0.000342j
[2025-09-11 17:10:32] [Iter 364/1050] R1[213/300], Temp: 0.1935, Energy: -52.795996+0.002986j
[2025-09-11 17:10:47] [Iter 365/1050] R1[214/300], Temp: 0.1894, Energy: -52.779205+0.001174j
[2025-09-11 17:11:00] [Iter 366/1050] R1[215/300], Temp: 0.1853, Energy: -52.821468-0.001844j
[2025-09-11 17:11:11] [Iter 367/1050] R1[216/300], Temp: 0.1813, Energy: -52.745162-0.000656j
[2025-09-11 17:11:25] [Iter 368/1050] R1[217/300], Temp: 0.1773, Energy: -52.800292-0.001838j
[2025-09-11 17:11:35] [Iter 369/1050] R1[218/300], Temp: 0.1733, Energy: -52.904062+0.003993j
[2025-09-11 17:11:50] [Iter 370/1050] R1[219/300], Temp: 0.1693, Energy: -52.945194+0.000335j
[2025-09-11 17:12:05] [Iter 371/1050] R1[220/300], Temp: 0.1654, Energy: -52.896976-0.000947j
[2025-09-11 17:12:20] [Iter 372/1050] R1[221/300], Temp: 0.1616, Energy: -52.768608+0.000812j
[2025-09-11 17:12:35] [Iter 373/1050] R1[222/300], Temp: 0.1577, Energy: -52.749962+0.003935j
[2025-09-11 17:12:51] [Iter 374/1050] R1[223/300], Temp: 0.1539, Energy: -52.803670-0.000220j
[2025-09-11 17:13:06] [Iter 375/1050] R1[224/300], Temp: 0.1502, Energy: -53.022848-0.000116j
[2025-09-11 17:13:21] [Iter 376/1050] R1[225/300], Temp: 0.1464, Energy: -52.964851+0.000652j
[2025-09-11 17:13:36] [Iter 377/1050] R1[226/300], Temp: 0.1428, Energy: -52.828404+0.000397j
[2025-09-11 17:13:52] [Iter 378/1050] R1[227/300], Temp: 0.1391, Energy: -52.727567+0.003666j
[2025-09-11 17:14:07] [Iter 379/1050] R1[228/300], Temp: 0.1355, Energy: -52.919564-0.003922j
[2025-09-11 17:14:22] [Iter 380/1050] R1[229/300], Temp: 0.1320, Energy: -52.871933-0.001587j
[2025-09-11 17:14:37] [Iter 381/1050] R1[230/300], Temp: 0.1284, Energy: -53.041306-0.003837j
[2025-09-11 17:14:52] [Iter 382/1050] R1[231/300], Temp: 0.1249, Energy: -53.036921-0.007818j
[2025-09-11 17:15:07] [Iter 383/1050] R1[232/300], Temp: 0.1215, Energy: -52.922883-0.000753j
[2025-09-11 17:15:23] [Iter 384/1050] R1[233/300], Temp: 0.1181, Energy: -52.821673+0.000794j
[2025-09-11 17:15:38] [Iter 385/1050] R1[234/300], Temp: 0.1147, Energy: -52.742872-0.000902j
[2025-09-11 17:15:53] [Iter 386/1050] R1[235/300], Temp: 0.1114, Energy: -52.802512-0.000510j
[2025-09-11 17:16:08] [Iter 387/1050] R1[236/300], Temp: 0.1082, Energy: -52.669697-0.000017j
[2025-09-11 17:16:24] [Iter 388/1050] R1[237/300], Temp: 0.1049, Energy: -52.825285-0.004934j
[2025-09-11 17:16:39] [Iter 389/1050] R1[238/300], Temp: 0.1017, Energy: -52.825315-0.001219j
[2025-09-11 17:16:54] [Iter 390/1050] R1[239/300], Temp: 0.0986, Energy: -52.975066-0.000695j
[2025-09-11 17:17:09] [Iter 391/1050] R1[240/300], Temp: 0.0955, Energy: -52.995045+0.004143j
[2025-09-11 17:17:25] [Iter 392/1050] R1[241/300], Temp: 0.0924, Energy: -53.025295+0.003200j
[2025-09-11 17:17:40] [Iter 393/1050] R1[242/300], Temp: 0.0894, Energy: -52.985424-0.005533j
[2025-09-11 17:17:55] [Iter 394/1050] R1[243/300], Temp: 0.0865, Energy: -53.053234+0.002831j
[2025-09-11 17:18:10] [Iter 395/1050] R1[244/300], Temp: 0.0835, Energy: -52.927372+0.000119j
[2025-09-11 17:18:25] [Iter 396/1050] R1[245/300], Temp: 0.0807, Energy: -52.915073-0.000362j
[2025-09-11 17:18:41] [Iter 397/1050] R1[246/300], Temp: 0.0778, Energy: -52.857921-0.000427j
[2025-09-11 17:18:56] [Iter 398/1050] R1[247/300], Temp: 0.0751, Energy: -52.949062-0.000094j
[2025-09-11 17:19:11] [Iter 399/1050] R1[248/300], Temp: 0.0723, Energy: -52.885244+0.000174j
[2025-09-11 17:19:26] [Iter 400/1050] R1[249/300], Temp: 0.0696, Energy: -52.973339-0.000908j
[2025-09-11 17:19:41] [Iter 401/1050] R1[250/300], Temp: 0.0670, Energy: -52.945233-0.003592j
[2025-09-11 17:19:57] [Iter 402/1050] R1[251/300], Temp: 0.0644, Energy: -52.776904+0.001968j
[2025-09-11 17:20:12] [Iter 403/1050] R1[252/300], Temp: 0.0618, Energy: -52.908178+0.000416j
[2025-09-11 17:20:27] [Iter 404/1050] R1[253/300], Temp: 0.0593, Energy: -52.992083-0.003255j
[2025-09-11 17:20:42] [Iter 405/1050] R1[254/300], Temp: 0.0569, Energy: -53.003313-0.000367j
[2025-09-11 17:20:57] [Iter 406/1050] R1[255/300], Temp: 0.0545, Energy: -52.888341-0.004849j
[2025-09-11 17:21:13] [Iter 407/1050] R1[256/300], Temp: 0.0521, Energy: -52.802300-0.001605j
[2025-09-11 17:21:28] [Iter 408/1050] R1[257/300], Temp: 0.0498, Energy: -52.898365+0.001517j
[2025-09-11 17:21:43] [Iter 409/1050] R1[258/300], Temp: 0.0476, Energy: -52.838243-0.003359j
[2025-09-11 17:21:58] [Iter 410/1050] R1[259/300], Temp: 0.0454, Energy: -52.789997-0.004063j
[2025-09-11 17:22:14] [Iter 411/1050] R1[260/300], Temp: 0.0432, Energy: -52.741894+0.001919j
[2025-09-11 17:22:29] [Iter 412/1050] R1[261/300], Temp: 0.0411, Energy: -53.000307-0.001756j
[2025-09-11 17:22:44] [Iter 413/1050] R1[262/300], Temp: 0.0391, Energy: -52.923347+0.001448j
[2025-09-11 17:22:59] [Iter 414/1050] R1[263/300], Temp: 0.0371, Energy: -52.862966+0.004011j
[2025-09-11 17:23:14] [Iter 415/1050] R1[264/300], Temp: 0.0351, Energy: -52.954415-0.000549j
[2025-09-11 17:23:30] [Iter 416/1050] R1[265/300], Temp: 0.0332, Energy: -52.928776-0.001087j
[2025-09-11 17:23:45] [Iter 417/1050] R1[266/300], Temp: 0.0314, Energy: -52.962036+0.001741j
[2025-09-11 17:24:00] [Iter 418/1050] R1[267/300], Temp: 0.0296, Energy: -52.951808-0.000187j
[2025-09-11 17:24:15] [Iter 419/1050] R1[268/300], Temp: 0.0278, Energy: -52.850991+0.004752j
[2025-09-11 17:24:30] [Iter 420/1050] R1[269/300], Temp: 0.0261, Energy: -52.821156+0.002242j
[2025-09-11 17:24:30] ✓ Checkpoint saved: checkpoint_iter_000420.pkl
[2025-09-11 17:24:46] [Iter 421/1050] R1[270/300], Temp: 0.0245, Energy: -52.794424+0.002301j
[2025-09-11 17:25:01] [Iter 422/1050] R1[271/300], Temp: 0.0229, Energy: -52.775398+0.002455j
[2025-09-11 17:25:16] [Iter 423/1050] R1[272/300], Temp: 0.0213, Energy: -52.727238+0.001823j
[2025-09-11 17:25:31] [Iter 424/1050] R1[273/300], Temp: 0.0199, Energy: -52.832459+0.001367j
[2025-09-11 17:25:46] [Iter 425/1050] R1[274/300], Temp: 0.0184, Energy: -52.811091+0.001352j
[2025-09-11 17:26:02] [Iter 426/1050] R1[275/300], Temp: 0.0170, Energy: -52.786367+0.003047j
[2025-09-11 17:26:17] [Iter 427/1050] R1[276/300], Temp: 0.0157, Energy: -52.811993-0.002085j
[2025-09-11 17:26:32] [Iter 428/1050] R1[277/300], Temp: 0.0144, Energy: -52.937668+0.001058j
[2025-09-11 17:26:47] [Iter 429/1050] R1[278/300], Temp: 0.0132, Energy: -52.775419+0.002309j
[2025-09-11 17:27:02] [Iter 430/1050] R1[279/300], Temp: 0.0120, Energy: -52.814338+0.003402j
[2025-09-11 17:27:18] [Iter 431/1050] R1[280/300], Temp: 0.0109, Energy: -52.868899+0.001125j
[2025-09-11 17:27:33] [Iter 432/1050] R1[281/300], Temp: 0.0099, Energy: -52.900290+0.004558j
[2025-09-11 17:27:48] [Iter 433/1050] R1[282/300], Temp: 0.0089, Energy: -52.887154+0.001035j
[2025-09-11 17:28:03] [Iter 434/1050] R1[283/300], Temp: 0.0079, Energy: -52.818173+0.003819j
[2025-09-11 17:28:18] [Iter 435/1050] R1[284/300], Temp: 0.0070, Energy: -52.889999-0.000581j
[2025-09-11 17:28:34] [Iter 436/1050] R1[285/300], Temp: 0.0062, Energy: -52.823652+0.003171j
[2025-09-11 17:28:49] [Iter 437/1050] R1[286/300], Temp: 0.0054, Energy: -52.824156-0.004128j
[2025-09-11 17:29:04] [Iter 438/1050] R1[287/300], Temp: 0.0046, Energy: -52.963212-0.001813j
[2025-09-11 17:29:19] [Iter 439/1050] R1[288/300], Temp: 0.0039, Energy: -53.012376-0.000320j
[2025-09-11 17:29:35] [Iter 440/1050] R1[289/300], Temp: 0.0033, Energy: -52.977707+0.001673j
[2025-09-11 17:29:50] [Iter 441/1050] R1[290/300], Temp: 0.0027, Energy: -53.036788+0.001303j
[2025-09-11 17:30:05] [Iter 442/1050] R1[291/300], Temp: 0.0022, Energy: -52.903981+0.001970j
[2025-09-11 17:30:20] [Iter 443/1050] R1[292/300], Temp: 0.0018, Energy: -52.824037-0.000890j
[2025-09-11 17:30:35] [Iter 444/1050] R1[293/300], Temp: 0.0013, Energy: -52.906126+0.000203j
[2025-09-11 17:30:51] [Iter 445/1050] R1[294/300], Temp: 0.0010, Energy: -52.832706+0.002942j
[2025-09-11 17:31:06] [Iter 446/1050] R1[295/300], Temp: 0.0007, Energy: -52.777828+0.001964j
[2025-09-11 17:31:21] [Iter 447/1050] R1[296/300], Temp: 0.0004, Energy: -52.797697+0.001071j
[2025-09-11 17:31:36] [Iter 448/1050] R1[297/300], Temp: 0.0002, Energy: -52.784418+0.002093j
[2025-09-11 17:31:52] [Iter 449/1050] R1[298/300], Temp: 0.0001, Energy: -52.912132+0.003627j
[2025-09-11 17:32:07] [Iter 450/1050] R1[299/300], Temp: 0.0000, Energy: -52.840510+0.003902j
[2025-09-11 17:32:07] RESTART #2 | Period: 600
[2025-09-11 17:32:22] [Iter 451/1050] R2[0/600], Temp: 1.0000, Energy: -52.838397-0.003158j
[2025-09-11 17:32:37] [Iter 452/1050] R2[1/600], Temp: 1.0000, Energy: -52.819022+0.002863j
[2025-09-11 17:32:52] [Iter 453/1050] R2[2/600], Temp: 1.0000, Energy: -52.843339-0.000361j
[2025-09-11 17:33:08] [Iter 454/1050] R2[3/600], Temp: 0.9999, Energy: -52.799664-0.002613j
[2025-09-11 17:33:23] [Iter 455/1050] R2[4/600], Temp: 0.9999, Energy: -52.901406+0.001408j
[2025-09-11 17:33:38] [Iter 456/1050] R2[5/600], Temp: 0.9998, Energy: -52.786908+0.003123j
[2025-09-11 17:33:53] [Iter 457/1050] R2[6/600], Temp: 0.9998, Energy: -52.947861-0.002530j
[2025-09-11 17:34:09] [Iter 458/1050] R2[7/600], Temp: 0.9997, Energy: -52.796181-0.000020j
[2025-09-11 17:34:24] [Iter 459/1050] R2[8/600], Temp: 0.9996, Energy: -52.937432+0.000537j
[2025-09-11 17:34:39] [Iter 460/1050] R2[9/600], Temp: 0.9994, Energy: -52.848118-0.002973j
[2025-09-11 17:34:54] [Iter 461/1050] R2[10/600], Temp: 0.9993, Energy: -52.830510+0.004900j
[2025-09-11 17:35:09] [Iter 462/1050] R2[11/600], Temp: 0.9992, Energy: -53.014207-0.002006j
[2025-09-11 17:35:25] [Iter 463/1050] R2[12/600], Temp: 0.9990, Energy: -53.014319+0.003592j
[2025-09-11 17:35:40] [Iter 464/1050] R2[13/600], Temp: 0.9988, Energy: -52.945868-0.000453j
[2025-09-11 17:35:55] [Iter 465/1050] R2[14/600], Temp: 0.9987, Energy: -52.881758+0.001708j
[2025-09-11 17:36:10] [Iter 466/1050] R2[15/600], Temp: 0.9985, Energy: -52.944954-0.004249j
[2025-09-11 17:36:26] [Iter 467/1050] R2[16/600], Temp: 0.9982, Energy: -53.032852-0.000207j
[2025-09-11 17:36:41] [Iter 468/1050] R2[17/600], Temp: 0.9980, Energy: -52.962957-0.000648j
[2025-09-11 17:36:56] [Iter 469/1050] R2[18/600], Temp: 0.9978, Energy: -52.912698+0.000352j
[2025-09-11 17:37:11] [Iter 470/1050] R2[19/600], Temp: 0.9975, Energy: -52.953884+0.004177j
[2025-09-11 17:37:26] [Iter 471/1050] R2[20/600], Temp: 0.9973, Energy: -52.975982-0.002133j
[2025-09-11 17:37:42] [Iter 472/1050] R2[21/600], Temp: 0.9970, Energy: -52.957448-0.003743j
[2025-09-11 17:37:57] [Iter 473/1050] R2[22/600], Temp: 0.9967, Energy: -52.950045-0.001760j
[2025-09-11 17:38:12] [Iter 474/1050] R2[23/600], Temp: 0.9964, Energy: -52.869725+0.000671j
[2025-09-11 17:38:27] [Iter 475/1050] R2[24/600], Temp: 0.9961, Energy: -52.913970+0.000979j
[2025-09-11 17:38:42] [Iter 476/1050] R2[25/600], Temp: 0.9957, Energy: -52.861374-0.007221j
[2025-09-11 17:38:58] [Iter 477/1050] R2[26/600], Temp: 0.9954, Energy: -53.088251+0.001972j
[2025-09-11 17:39:13] [Iter 478/1050] R2[27/600], Temp: 0.9950, Energy: -52.974131+0.001643j
[2025-09-11 17:39:28] [Iter 479/1050] R2[28/600], Temp: 0.9946, Energy: -52.926354+0.002512j
[2025-09-11 17:39:43] [Iter 480/1050] R2[29/600], Temp: 0.9942, Energy: -52.944261-0.000000j
[2025-09-11 17:39:58] [Iter 481/1050] R2[30/600], Temp: 0.9938, Energy: -52.878862-0.002366j
[2025-09-11 17:40:14] [Iter 482/1050] R2[31/600], Temp: 0.9934, Energy: -52.918607+0.000878j
[2025-09-11 17:40:29] [Iter 483/1050] R2[32/600], Temp: 0.9930, Energy: -52.973506+0.003858j
[2025-09-11 17:40:44] [Iter 484/1050] R2[33/600], Temp: 0.9926, Energy: -52.995089+0.000413j
[2025-09-11 17:40:59] [Iter 485/1050] R2[34/600], Temp: 0.9921, Energy: -52.937327+0.001607j
[2025-09-11 17:41:14] [Iter 486/1050] R2[35/600], Temp: 0.9916, Energy: -53.004347-0.004145j
[2025-09-11 17:41:30] [Iter 487/1050] R2[36/600], Temp: 0.9911, Energy: -53.038733-0.003110j
[2025-09-11 17:41:45] [Iter 488/1050] R2[37/600], Temp: 0.9906, Energy: -52.952003+0.001546j
[2025-09-11 17:42:00] [Iter 489/1050] R2[38/600], Temp: 0.9901, Energy: -52.886990-0.002148j
[2025-09-11 17:42:15] [Iter 490/1050] R2[39/600], Temp: 0.9896, Energy: -52.985678+0.004349j
[2025-09-11 17:42:31] [Iter 491/1050] R2[40/600], Temp: 0.9891, Energy: -52.940606-0.002167j
[2025-09-11 17:42:46] [Iter 492/1050] R2[41/600], Temp: 0.9885, Energy: -52.908328-0.003475j
[2025-09-11 17:43:01] [Iter 493/1050] R2[42/600], Temp: 0.9880, Energy: -52.980449+0.000345j
[2025-09-11 17:43:16] [Iter 494/1050] R2[43/600], Temp: 0.9874, Energy: -52.936808+0.004306j
[2025-09-11 17:43:31] [Iter 495/1050] R2[44/600], Temp: 0.9868, Energy: -53.005189-0.000193j
[2025-09-11 17:43:47] [Iter 496/1050] R2[45/600], Temp: 0.9862, Energy: -52.983845-0.001511j
[2025-09-11 17:44:02] [Iter 497/1050] R2[46/600], Temp: 0.9856, Energy: -53.037972+0.001655j
[2025-09-11 17:44:17] [Iter 498/1050] R2[47/600], Temp: 0.9849, Energy: -52.951010+0.004215j
[2025-09-11 17:44:32] [Iter 499/1050] R2[48/600], Temp: 0.9843, Energy: -52.969026+0.002433j
[2025-09-11 17:44:47] [Iter 500/1050] R2[49/600], Temp: 0.9836, Energy: -52.947442+0.000706j
[2025-09-11 17:45:03] [Iter 501/1050] R2[50/600], Temp: 0.9830, Energy: -52.992218-0.004524j
[2025-09-11 17:45:18] [Iter 502/1050] R2[51/600], Temp: 0.9823, Energy: -52.870664+0.001089j
[2025-09-11 17:45:29] [Iter 503/1050] R2[52/600], Temp: 0.9816, Energy: -52.805010+0.002557j
[2025-09-11 17:45:40] [Iter 504/1050] R2[53/600], Temp: 0.9809, Energy: -52.961609-0.003326j
[2025-09-11 17:45:50] [Iter 505/1050] R2[54/600], Temp: 0.9801, Energy: -52.971775+0.002391j
[2025-09-11 17:46:00] [Iter 506/1050] R2[55/600], Temp: 0.9794, Energy: -53.000170+0.001569j
[2025-09-11 17:46:10] [Iter 507/1050] R2[56/600], Temp: 0.9787, Energy: -52.967742+0.001795j
[2025-09-11 17:46:20] [Iter 508/1050] R2[57/600], Temp: 0.9779, Energy: -52.952712+0.002843j
[2025-09-11 17:46:30] [Iter 509/1050] R2[58/600], Temp: 0.9771, Energy: -52.825186-0.004169j
[2025-09-11 17:46:42] [Iter 510/1050] R2[59/600], Temp: 0.9763, Energy: -52.676865-0.001783j
[2025-09-11 17:46:57] [Iter 511/1050] R2[60/600], Temp: 0.9755, Energy: -52.762345+0.000383j
[2025-09-11 17:47:13] [Iter 512/1050] R2[61/600], Temp: 0.9747, Energy: -52.824499+0.001985j
[2025-09-11 17:47:28] [Iter 513/1050] R2[62/600], Temp: 0.9739, Energy: -52.994236-0.001307j
[2025-09-11 17:47:43] [Iter 514/1050] R2[63/600], Temp: 0.9730, Energy: -52.935906-0.000446j
[2025-09-11 17:47:59] [Iter 515/1050] R2[64/600], Temp: 0.9722, Energy: -53.004895+0.001493j
[2025-09-11 17:48:14] [Iter 516/1050] R2[65/600], Temp: 0.9713, Energy: -52.898212-0.004715j
[2025-09-11 17:48:29] [Iter 517/1050] R2[66/600], Temp: 0.9704, Energy: -52.987893+0.000869j
[2025-09-11 17:48:44] [Iter 518/1050] R2[67/600], Temp: 0.9695, Energy: -52.957749+0.001754j
[2025-09-11 17:49:00] [Iter 519/1050] R2[68/600], Temp: 0.9686, Energy: -52.913888-0.001348j
[2025-09-11 17:49:15] [Iter 520/1050] R2[69/600], Temp: 0.9677, Energy: -52.844783+0.005080j
[2025-09-11 17:49:30] [Iter 521/1050] R2[70/600], Temp: 0.9668, Energy: -52.855521+0.000339j
[2025-09-11 17:49:46] [Iter 522/1050] R2[71/600], Temp: 0.9658, Energy: -52.862366+0.000452j
[2025-09-11 17:50:01] [Iter 523/1050] R2[72/600], Temp: 0.9649, Energy: -52.827889+0.003032j
[2025-09-11 17:50:16] [Iter 524/1050] R2[73/600], Temp: 0.9639, Energy: -52.907602+0.000773j
[2025-09-11 17:50:31] [Iter 525/1050] R2[74/600], Temp: 0.9629, Energy: -52.923746+0.000586j
[2025-09-11 17:50:31] ✓ Checkpoint saved: checkpoint_iter_000525.pkl
[2025-09-11 17:50:47] [Iter 526/1050] R2[75/600], Temp: 0.9619, Energy: -52.937911+0.005064j
[2025-09-11 17:51:02] [Iter 527/1050] R2[76/600], Temp: 0.9609, Energy: -52.982854+0.002251j
[2025-09-11 17:51:17] [Iter 528/1050] R2[77/600], Temp: 0.9599, Energy: -52.878613-0.000617j
[2025-09-11 17:51:32] [Iter 529/1050] R2[78/600], Temp: 0.9589, Energy: -53.069101+0.000985j
[2025-09-11 17:51:48] [Iter 530/1050] R2[79/600], Temp: 0.9578, Energy: -52.954657+0.000321j
[2025-09-11 17:52:03] [Iter 531/1050] R2[80/600], Temp: 0.9568, Energy: -53.089041-0.001004j
[2025-09-11 17:52:18] [Iter 532/1050] R2[81/600], Temp: 0.9557, Energy: -52.963469+0.000865j
[2025-09-11 17:52:34] [Iter 533/1050] R2[82/600], Temp: 0.9546, Energy: -53.115670-0.001320j
[2025-09-11 17:52:49] [Iter 534/1050] R2[83/600], Temp: 0.9535, Energy: -52.905267+0.002985j
[2025-09-11 17:53:04] [Iter 535/1050] R2[84/600], Temp: 0.9524, Energy: -52.904752-0.000365j
[2025-09-11 17:53:19] [Iter 536/1050] R2[85/600], Temp: 0.9513, Energy: -52.960203-0.001209j
[2025-09-11 17:53:35] [Iter 537/1050] R2[86/600], Temp: 0.9502, Energy: -52.860920+0.003814j
[2025-09-11 17:53:50] [Iter 538/1050] R2[87/600], Temp: 0.9490, Energy: -52.950689-0.000469j
[2025-09-11 17:54:05] [Iter 539/1050] R2[88/600], Temp: 0.9479, Energy: -52.792419+0.001738j
[2025-09-11 17:54:21] [Iter 540/1050] R2[89/600], Temp: 0.9467, Energy: -52.870718+0.002897j
[2025-09-11 17:54:36] [Iter 541/1050] R2[90/600], Temp: 0.9455, Energy: -52.856629+0.001282j
[2025-09-11 17:54:51] [Iter 542/1050] R2[91/600], Temp: 0.9443, Energy: -52.839478-0.003457j
[2025-09-11 17:55:06] [Iter 543/1050] R2[92/600], Temp: 0.9431, Energy: -52.792281+0.002669j
[2025-09-11 17:55:22] [Iter 544/1050] R2[93/600], Temp: 0.9419, Energy: -52.786556-0.000165j
[2025-09-11 17:55:37] [Iter 545/1050] R2[94/600], Temp: 0.9407, Energy: -52.796009-0.002943j
[2025-09-11 17:55:52] [Iter 546/1050] R2[95/600], Temp: 0.9394, Energy: -52.785872-0.003298j
[2025-09-11 17:56:08] [Iter 547/1050] R2[96/600], Temp: 0.9382, Energy: -52.851006-0.008472j
[2025-09-11 17:56:19] [Iter 548/1050] R2[97/600], Temp: 0.9369, Energy: -52.930183-0.000843j
[2025-09-11 17:56:31] [Iter 549/1050] R2[98/600], Temp: 0.9356, Energy: -52.950434+0.000406j
[2025-09-11 17:56:44] [Iter 550/1050] R2[99/600], Temp: 0.9343, Energy: -52.896222-0.001019j
[2025-09-11 17:56:56] [Iter 551/1050] R2[100/600], Temp: 0.9330, Energy: -52.851073-0.006132j
[2025-09-11 17:57:11] [Iter 552/1050] R2[101/600], Temp: 0.9317, Energy: -52.842139-0.000398j
[2025-09-11 17:57:26] [Iter 553/1050] R2[102/600], Temp: 0.9304, Energy: -52.951751-0.002503j
[2025-09-11 17:57:41] [Iter 554/1050] R2[103/600], Temp: 0.9290, Energy: -52.859592-0.002837j
[2025-09-11 17:57:57] [Iter 555/1050] R2[104/600], Temp: 0.9277, Energy: -52.912643+0.003822j
[2025-09-11 17:58:12] [Iter 556/1050] R2[105/600], Temp: 0.9263, Energy: -52.828414+0.002807j
[2025-09-11 17:58:27] [Iter 557/1050] R2[106/600], Temp: 0.9249, Energy: -52.929769-0.001617j
[2025-09-11 17:58:42] [Iter 558/1050] R2[107/600], Temp: 0.9236, Energy: -53.015729+0.002192j
[2025-09-11 17:58:58] [Iter 559/1050] R2[108/600], Temp: 0.9222, Energy: -52.928389+0.002786j
[2025-09-11 17:59:13] [Iter 560/1050] R2[109/600], Temp: 0.9208, Energy: -52.953398+0.000359j
[2025-09-11 17:59:28] [Iter 561/1050] R2[110/600], Temp: 0.9193, Energy: -52.829016+0.001642j
[2025-09-11 17:59:43] [Iter 562/1050] R2[111/600], Temp: 0.9179, Energy: -52.977281+0.002756j
[2025-09-11 17:59:59] [Iter 563/1050] R2[112/600], Temp: 0.9165, Energy: -52.891680-0.002054j
[2025-09-11 18:00:14] [Iter 564/1050] R2[113/600], Temp: 0.9150, Energy: -52.874366-0.001796j
[2025-09-11 18:00:29] [Iter 565/1050] R2[114/600], Temp: 0.9135, Energy: -52.915597+0.000001j
[2025-09-11 18:00:44] [Iter 566/1050] R2[115/600], Temp: 0.9121, Energy: -52.818867+0.000141j
[2025-09-11 18:01:00] [Iter 567/1050] R2[116/600], Temp: 0.9106, Energy: -52.836148+0.005786j
[2025-09-11 18:01:15] [Iter 568/1050] R2[117/600], Temp: 0.9091, Energy: -52.810916+0.000680j
[2025-09-11 18:01:30] [Iter 569/1050] R2[118/600], Temp: 0.9076, Energy: -52.858295-0.002745j
[2025-09-11 18:01:45] [Iter 570/1050] R2[119/600], Temp: 0.9060, Energy: -52.922370+0.003485j
[2025-09-11 18:02:01] [Iter 571/1050] R2[120/600], Temp: 0.9045, Energy: -52.869972-0.000829j
[2025-09-11 18:02:16] [Iter 572/1050] R2[121/600], Temp: 0.9030, Energy: -52.775665-0.001483j
[2025-09-11 18:02:31] [Iter 573/1050] R2[122/600], Temp: 0.9014, Energy: -52.868477+0.000530j
[2025-09-11 18:02:46] [Iter 574/1050] R2[123/600], Temp: 0.8998, Energy: -52.958157+0.000349j
[2025-09-11 18:03:02] [Iter 575/1050] R2[124/600], Temp: 0.8983, Energy: -52.877023-0.004281j
[2025-09-11 18:03:17] [Iter 576/1050] R2[125/600], Temp: 0.8967, Energy: -52.792283+0.000748j
[2025-09-11 18:03:32] [Iter 577/1050] R2[126/600], Temp: 0.8951, Energy: -52.677477-0.000883j
[2025-09-11 18:03:47] [Iter 578/1050] R2[127/600], Temp: 0.8935, Energy: -52.791488-0.002219j
[2025-09-11 18:04:03] [Iter 579/1050] R2[128/600], Temp: 0.8918, Energy: -52.886068+0.002473j
[2025-09-11 18:04:18] [Iter 580/1050] R2[129/600], Temp: 0.8902, Energy: -52.910717-0.002598j
[2025-09-11 18:04:33] [Iter 581/1050] R2[130/600], Temp: 0.8886, Energy: -52.767807+0.000157j
[2025-09-11 18:04:48] [Iter 582/1050] R2[131/600], Temp: 0.8869, Energy: -52.860419-0.002717j
[2025-09-11 18:05:03] [Iter 583/1050] R2[132/600], Temp: 0.8853, Energy: -52.940732-0.002131j
[2025-09-11 18:05:19] [Iter 584/1050] R2[133/600], Temp: 0.8836, Energy: -52.895336+0.000730j
[2025-09-11 18:05:34] [Iter 585/1050] R2[134/600], Temp: 0.8819, Energy: -52.952671-0.001411j
[2025-09-11 18:05:49] [Iter 586/1050] R2[135/600], Temp: 0.8802, Energy: -52.953786-0.001786j
[2025-09-11 18:06:04] [Iter 587/1050] R2[136/600], Temp: 0.8785, Energy: -53.000717+0.000090j
[2025-09-11 18:06:20] [Iter 588/1050] R2[137/600], Temp: 0.8768, Energy: -53.043022+0.000186j
[2025-09-11 18:06:35] [Iter 589/1050] R2[138/600], Temp: 0.8751, Energy: -53.008151+0.001520j
[2025-09-11 18:06:50] [Iter 590/1050] R2[139/600], Temp: 0.8733, Energy: -52.922796-0.001496j
[2025-09-11 18:07:05] [Iter 591/1050] R2[140/600], Temp: 0.8716, Energy: -53.087191+0.001140j
[2025-09-11 18:07:20] [Iter 592/1050] R2[141/600], Temp: 0.8698, Energy: -52.971110-0.000854j
[2025-09-11 18:07:36] [Iter 593/1050] R2[142/600], Temp: 0.8680, Energy: -52.994580-0.000199j
[2025-09-11 18:07:51] [Iter 594/1050] R2[143/600], Temp: 0.8663, Energy: -52.981220+0.000444j
[2025-09-11 18:08:06] [Iter 595/1050] R2[144/600], Temp: 0.8645, Energy: -52.853673-0.002842j
[2025-09-11 18:08:21] [Iter 596/1050] R2[145/600], Temp: 0.8627, Energy: -52.933540-0.002532j
[2025-09-11 18:08:37] [Iter 597/1050] R2[146/600], Temp: 0.8609, Energy: -52.836090-0.000219j
[2025-09-11 18:08:52] [Iter 598/1050] R2[147/600], Temp: 0.8591, Energy: -52.800400-0.001494j
[2025-09-11 18:09:07] [Iter 599/1050] R2[148/600], Temp: 0.8572, Energy: -52.909078+0.003555j
[2025-09-11 18:09:22] [Iter 600/1050] R2[149/600], Temp: 0.8554, Energy: -52.885374-0.001822j
[2025-09-11 18:09:37] [Iter 601/1050] R2[150/600], Temp: 0.8536, Energy: -52.901134+0.002569j
[2025-09-11 18:09:53] [Iter 602/1050] R2[151/600], Temp: 0.8517, Energy: -52.942811-0.001294j
[2025-09-11 18:10:08] [Iter 603/1050] R2[152/600], Temp: 0.8498, Energy: -52.990553+0.002631j
[2025-09-11 18:10:23] [Iter 604/1050] R2[153/600], Temp: 0.8480, Energy: -52.916549-0.002262j
[2025-09-11 18:10:39] [Iter 605/1050] R2[154/600], Temp: 0.8461, Energy: -52.975775+0.002609j
[2025-09-11 18:10:54] [Iter 606/1050] R2[155/600], Temp: 0.8442, Energy: -52.834187-0.002504j
[2025-09-11 18:11:09] [Iter 607/1050] R2[156/600], Temp: 0.8423, Energy: -52.921471+0.000893j
[2025-09-11 18:11:24] [Iter 608/1050] R2[157/600], Temp: 0.8404, Energy: -52.993025+0.005220j
[2025-09-11 18:11:39] [Iter 609/1050] R2[158/600], Temp: 0.8384, Energy: -52.967713+0.000177j
[2025-09-11 18:11:55] [Iter 610/1050] R2[159/600], Temp: 0.8365, Energy: -52.988409+0.001364j
[2025-09-11 18:12:10] [Iter 611/1050] R2[160/600], Temp: 0.8346, Energy: -52.975003-0.003630j
[2025-09-11 18:12:25] [Iter 612/1050] R2[161/600], Temp: 0.8326, Energy: -52.827805-0.001420j
[2025-09-11 18:12:40] [Iter 613/1050] R2[162/600], Temp: 0.8307, Energy: -52.850059-0.005913j
[2025-09-11 18:12:56] [Iter 614/1050] R2[163/600], Temp: 0.8287, Energy: -52.916168-0.000455j
[2025-09-11 18:13:11] [Iter 615/1050] R2[164/600], Temp: 0.8267, Energy: -53.019792+0.005071j
[2025-09-11 18:13:26] [Iter 616/1050] R2[165/600], Temp: 0.8247, Energy: -52.819805+0.000509j
[2025-09-11 18:13:41] [Iter 617/1050] R2[166/600], Temp: 0.8227, Energy: -52.794741+0.001840j
[2025-09-11 18:13:57] [Iter 618/1050] R2[167/600], Temp: 0.8207, Energy: -52.811159+0.002952j
[2025-09-11 18:14:12] [Iter 619/1050] R2[168/600], Temp: 0.8187, Energy: -52.868568+0.002235j
[2025-09-11 18:14:27] [Iter 620/1050] R2[169/600], Temp: 0.8167, Energy: -52.837568+0.000979j
[2025-09-11 18:14:42] [Iter 621/1050] R2[170/600], Temp: 0.8147, Energy: -52.789793-0.001014j
[2025-09-11 18:14:58] [Iter 622/1050] R2[171/600], Temp: 0.8126, Energy: -52.853832-0.001106j
[2025-09-11 18:15:13] [Iter 623/1050] R2[172/600], Temp: 0.8106, Energy: -52.841952-0.000975j
[2025-09-11 18:15:28] [Iter 624/1050] R2[173/600], Temp: 0.8085, Energy: -52.771742+0.002307j
[2025-09-11 18:15:43] [Iter 625/1050] R2[174/600], Temp: 0.8065, Energy: -52.912065+0.002499j
[2025-09-11 18:15:58] [Iter 626/1050] R2[175/600], Temp: 0.8044, Energy: -52.824329-0.002524j
[2025-09-11 18:16:14] [Iter 627/1050] R2[176/600], Temp: 0.8023, Energy: -52.770226-0.004550j
[2025-09-11 18:16:29] [Iter 628/1050] R2[177/600], Temp: 0.8002, Energy: -52.837186+0.000242j
[2025-09-11 18:16:44] [Iter 629/1050] R2[178/600], Temp: 0.7981, Energy: -52.971284-0.003215j
[2025-09-11 18:16:59] [Iter 630/1050] R2[179/600], Temp: 0.7960, Energy: -52.886914+0.001227j
[2025-09-11 18:16:59] ✓ Checkpoint saved: checkpoint_iter_000630.pkl
[2025-09-11 18:17:15] [Iter 631/1050] R2[180/600], Temp: 0.7939, Energy: -52.878003+0.002353j
[2025-09-11 18:17:30] [Iter 632/1050] R2[181/600], Temp: 0.7918, Energy: -53.116035+0.001468j
[2025-09-11 18:17:45] [Iter 633/1050] R2[182/600], Temp: 0.7896, Energy: -52.871660-0.001236j
[2025-09-11 18:18:00] [Iter 634/1050] R2[183/600], Temp: 0.7875, Energy: -52.869955+0.002810j
[2025-09-11 18:18:16] [Iter 635/1050] R2[184/600], Temp: 0.7854, Energy: -52.980621+0.002521j
[2025-09-11 18:18:31] [Iter 636/1050] R2[185/600], Temp: 0.7832, Energy: -53.036652+0.003284j
[2025-09-11 18:18:46] [Iter 637/1050] R2[186/600], Temp: 0.7810, Energy: -52.922679+0.001820j
[2025-09-11 18:19:01] [Iter 638/1050] R2[187/600], Temp: 0.7789, Energy: -52.943722-0.001097j
[2025-09-11 18:19:17] [Iter 639/1050] R2[188/600], Temp: 0.7767, Energy: -52.925593+0.002868j
[2025-09-11 18:19:32] [Iter 640/1050] R2[189/600], Temp: 0.7745, Energy: -52.812647+0.003908j
[2025-09-11 18:19:47] [Iter 641/1050] R2[190/600], Temp: 0.7723, Energy: -52.908519-0.003248j
[2025-09-11 18:20:02] [Iter 642/1050] R2[191/600], Temp: 0.7701, Energy: -52.901668+0.001445j
[2025-09-11 18:20:18] [Iter 643/1050] R2[192/600], Temp: 0.7679, Energy: -52.846007+0.002820j
[2025-09-11 18:20:33] [Iter 644/1050] R2[193/600], Temp: 0.7657, Energy: -52.889821-0.000868j
[2025-09-11 18:20:48] [Iter 645/1050] R2[194/600], Temp: 0.7635, Energy: -52.772674-0.000231j
[2025-09-11 18:21:03] [Iter 646/1050] R2[195/600], Temp: 0.7612, Energy: -52.926131-0.001686j
[2025-09-11 18:21:18] [Iter 647/1050] R2[196/600], Temp: 0.7590, Energy: -52.972359-0.002079j
[2025-09-11 18:21:34] [Iter 648/1050] R2[197/600], Temp: 0.7568, Energy: -53.041259+0.001945j
[2025-09-11 18:21:49] [Iter 649/1050] R2[198/600], Temp: 0.7545, Energy: -52.904268-0.001037j
[2025-09-11 18:22:04] [Iter 650/1050] R2[199/600], Temp: 0.7523, Energy: -52.928170+0.002141j
[2025-09-11 18:22:19] [Iter 651/1050] R2[200/600], Temp: 0.7500, Energy: -53.006304-0.002079j
[2025-09-11 18:22:35] [Iter 652/1050] R2[201/600], Temp: 0.7477, Energy: -53.063912+0.001240j
[2025-09-11 18:22:50] [Iter 653/1050] R2[202/600], Temp: 0.7455, Energy: -53.044644-0.001572j
[2025-09-11 18:23:05] [Iter 654/1050] R2[203/600], Temp: 0.7432, Energy: -52.953681-0.002424j
[2025-09-11 18:23:20] [Iter 655/1050] R2[204/600], Temp: 0.7409, Energy: -52.933727+0.000537j
[2025-09-11 18:23:35] [Iter 656/1050] R2[205/600], Temp: 0.7386, Energy: -52.912980-0.000393j
[2025-09-11 18:23:51] [Iter 657/1050] R2[206/600], Temp: 0.7363, Energy: -52.851291-0.004056j
[2025-09-11 18:24:06] [Iter 658/1050] R2[207/600], Temp: 0.7340, Energy: -52.854118-0.003761j
[2025-09-11 18:24:21] [Iter 659/1050] R2[208/600], Temp: 0.7316, Energy: -52.891599-0.003517j
[2025-09-11 18:24:36] [Iter 660/1050] R2[209/600], Temp: 0.7293, Energy: -52.816014+0.000147j
[2025-09-11 18:24:52] [Iter 661/1050] R2[210/600], Temp: 0.7270, Energy: -52.773682+0.001646j
[2025-09-11 18:25:07] [Iter 662/1050] R2[211/600], Temp: 0.7247, Energy: -52.732747-0.002633j
[2025-09-11 18:25:22] [Iter 663/1050] R2[212/600], Temp: 0.7223, Energy: -52.857181-0.001511j
[2025-09-11 18:25:37] [Iter 664/1050] R2[213/600], Temp: 0.7200, Energy: -52.901381-0.001872j
[2025-09-11 18:25:53] [Iter 665/1050] R2[214/600], Temp: 0.7176, Energy: -52.879961-0.005070j
[2025-09-11 18:26:08] [Iter 666/1050] R2[215/600], Temp: 0.7153, Energy: -52.921415-0.000499j
[2025-09-11 18:26:23] [Iter 667/1050] R2[216/600], Temp: 0.7129, Energy: -52.975064-0.002555j
[2025-09-11 18:26:38] [Iter 668/1050] R2[217/600], Temp: 0.7105, Energy: -53.013328-0.000655j
[2025-09-11 18:26:54] [Iter 669/1050] R2[218/600], Temp: 0.7081, Energy: -52.855070-0.000604j
[2025-09-11 18:27:09] [Iter 670/1050] R2[219/600], Temp: 0.7058, Energy: -52.816689-0.000525j
[2025-09-11 18:27:24] [Iter 671/1050] R2[220/600], Temp: 0.7034, Energy: -52.973147-0.001014j
[2025-09-11 18:27:39] [Iter 672/1050] R2[221/600], Temp: 0.7010, Energy: -52.744436-0.004363j
[2025-09-11 18:27:55] [Iter 673/1050] R2[222/600], Temp: 0.6986, Energy: -52.837756+0.003178j
[2025-09-11 18:28:10] [Iter 674/1050] R2[223/600], Temp: 0.6962, Energy: -52.849058+0.004005j
[2025-09-11 18:28:25] [Iter 675/1050] R2[224/600], Temp: 0.6938, Energy: -52.834810-0.002859j
[2025-09-11 18:28:40] [Iter 676/1050] R2[225/600], Temp: 0.6913, Energy: -52.894308-0.003711j
[2025-09-11 18:28:56] [Iter 677/1050] R2[226/600], Temp: 0.6889, Energy: -52.739658-0.003767j
[2025-09-11 18:29:11] [Iter 678/1050] R2[227/600], Temp: 0.6865, Energy: -52.758006+0.001759j
[2025-09-11 18:29:26] [Iter 679/1050] R2[228/600], Temp: 0.6841, Energy: -52.778461+0.000827j
[2025-09-11 18:29:41] [Iter 680/1050] R2[229/600], Temp: 0.6816, Energy: -52.827609+0.001832j
[2025-09-11 18:29:56] [Iter 681/1050] R2[230/600], Temp: 0.6792, Energy: -52.955206+0.001368j
[2025-09-11 18:30:12] [Iter 682/1050] R2[231/600], Temp: 0.6767, Energy: -52.904276+0.000968j
[2025-09-11 18:30:27] [Iter 683/1050] R2[232/600], Temp: 0.6743, Energy: -52.899680-0.002598j
[2025-09-11 18:30:40] [Iter 684/1050] R2[233/600], Temp: 0.6718, Energy: -52.832154-0.000853j
[2025-09-11 18:30:51] [Iter 685/1050] R2[234/600], Temp: 0.6694, Energy: -52.857151-0.002934j
[2025-09-11 18:31:01] [Iter 686/1050] R2[235/600], Temp: 0.6669, Energy: -52.817520+0.000462j
[2025-09-11 18:31:11] [Iter 687/1050] R2[236/600], Temp: 0.6644, Energy: -52.904668+0.001655j
[2025-09-11 18:31:21] [Iter 688/1050] R2[237/600], Temp: 0.6620, Energy: -52.844758+0.001283j
[2025-09-11 18:31:31] [Iter 689/1050] R2[238/600], Temp: 0.6595, Energy: -52.939866-0.000185j
[2025-09-11 18:31:42] [Iter 690/1050] R2[239/600], Temp: 0.6570, Energy: -52.770301+0.003571j
[2025-09-11 18:31:56] [Iter 691/1050] R2[240/600], Temp: 0.6545, Energy: -52.810940+0.003499j
[2025-09-11 18:32:12] [Iter 692/1050] R2[241/600], Temp: 0.6520, Energy: -52.999141-0.002235j
[2025-09-11 18:32:27] [Iter 693/1050] R2[242/600], Temp: 0.6495, Energy: -52.843134-0.001538j
[2025-09-11 18:32:42] [Iter 694/1050] R2[243/600], Temp: 0.6470, Energy: -52.967356+0.001026j
[2025-09-11 18:32:58] [Iter 695/1050] R2[244/600], Temp: 0.6445, Energy: -52.904164-0.000582j
[2025-09-11 18:33:13] [Iter 696/1050] R2[245/600], Temp: 0.6420, Energy: -52.896954-0.006582j
[2025-09-11 18:33:28] [Iter 697/1050] R2[246/600], Temp: 0.6395, Energy: -52.887237-0.001930j
[2025-09-11 18:33:44] [Iter 698/1050] R2[247/600], Temp: 0.6370, Energy: -52.859812+0.000199j
[2025-09-11 18:33:59] [Iter 699/1050] R2[248/600], Temp: 0.6345, Energy: -52.986762-0.000300j
[2025-09-11 18:34:14] [Iter 700/1050] R2[249/600], Temp: 0.6319, Energy: -52.938744-0.001219j
[2025-09-11 18:34:29] [Iter 701/1050] R2[250/600], Temp: 0.6294, Energy: -53.062519-0.000673j
[2025-09-11 18:34:45] [Iter 702/1050] R2[251/600], Temp: 0.6269, Energy: -53.036715-0.000488j
[2025-09-11 18:35:00] [Iter 703/1050] R2[252/600], Temp: 0.6243, Energy: -52.928079+0.001601j
[2025-09-11 18:35:15] [Iter 704/1050] R2[253/600], Temp: 0.6218, Energy: -52.951961-0.003648j
[2025-09-11 18:35:31] [Iter 705/1050] R2[254/600], Temp: 0.6193, Energy: -53.016458-0.002502j
[2025-09-11 18:35:46] [Iter 706/1050] R2[255/600], Temp: 0.6167, Energy: -52.946350+0.002598j
[2025-09-11 18:36:01] [Iter 707/1050] R2[256/600], Temp: 0.6142, Energy: -52.871965+0.003810j
[2025-09-11 18:36:16] [Iter 708/1050] R2[257/600], Temp: 0.6116, Energy: -52.831853+0.002727j
[2025-09-11 18:36:32] [Iter 709/1050] R2[258/600], Temp: 0.6091, Energy: -52.841379-0.000591j
[2025-09-11 18:36:47] [Iter 710/1050] R2[259/600], Temp: 0.6065, Energy: -52.830933-0.001704j
[2025-09-11 18:37:02] [Iter 711/1050] R2[260/600], Temp: 0.6040, Energy: -52.934019+0.001147j
[2025-09-11 18:37:18] [Iter 712/1050] R2[261/600], Temp: 0.6014, Energy: -52.904273+0.000173j
[2025-09-11 18:37:33] [Iter 713/1050] R2[262/600], Temp: 0.5988, Energy: -52.988683-0.001666j
[2025-09-11 18:37:48] [Iter 714/1050] R2[263/600], Temp: 0.5963, Energy: -53.092131+0.000829j
[2025-09-11 18:38:03] [Iter 715/1050] R2[264/600], Temp: 0.5937, Energy: -52.881817+0.004232j
[2025-09-11 18:38:19] [Iter 716/1050] R2[265/600], Temp: 0.5911, Energy: -52.938276-0.000055j
[2025-09-11 18:38:34] [Iter 717/1050] R2[266/600], Temp: 0.5885, Energy: -52.810275+0.001239j
[2025-09-11 18:38:49] [Iter 718/1050] R2[267/600], Temp: 0.5860, Energy: -52.868891-0.000719j
[2025-09-11 18:39:05] [Iter 719/1050] R2[268/600], Temp: 0.5834, Energy: -52.856612-0.000466j
[2025-09-11 18:39:20] [Iter 720/1050] R2[269/600], Temp: 0.5808, Energy: -53.009253+0.000587j
[2025-09-11 18:39:35] [Iter 721/1050] R2[270/600], Temp: 0.5782, Energy: -52.897429+0.000289j
[2025-09-11 18:39:51] [Iter 722/1050] R2[271/600], Temp: 0.5756, Energy: -52.910344+0.000936j
[2025-09-11 18:40:06] [Iter 723/1050] R2[272/600], Temp: 0.5730, Energy: -52.798088+0.001100j
[2025-09-11 18:40:21] [Iter 724/1050] R2[273/600], Temp: 0.5705, Energy: -52.759960-0.000927j
[2025-09-11 18:40:36] [Iter 725/1050] R2[274/600], Temp: 0.5679, Energy: -52.772215-0.005581j
[2025-09-11 18:40:52] [Iter 726/1050] R2[275/600], Temp: 0.5653, Energy: -52.675830+0.000207j
[2025-09-11 18:41:07] [Iter 727/1050] R2[276/600], Temp: 0.5627, Energy: -52.705603-0.001822j
[2025-09-11 18:41:19] [Iter 728/1050] R2[277/600], Temp: 0.5601, Energy: -52.800632+0.003510j
[2025-09-11 18:41:30] [Iter 729/1050] R2[278/600], Temp: 0.5575, Energy: -52.926753-0.001656j
[2025-09-11 18:41:43] [Iter 730/1050] R2[279/600], Temp: 0.5549, Energy: -52.951958+0.001326j
[2025-09-11 18:41:54] [Iter 731/1050] R2[280/600], Temp: 0.5523, Energy: -53.028432+0.001933j
[2025-09-11 18:42:10] [Iter 732/1050] R2[281/600], Temp: 0.5497, Energy: -52.998308-0.001840j
[2025-09-11 18:42:25] [Iter 733/1050] R2[282/600], Temp: 0.5471, Energy: -52.974717-0.003726j
[2025-09-11 18:42:40] [Iter 734/1050] R2[283/600], Temp: 0.5444, Energy: -52.831423-0.003799j
[2025-09-11 18:42:55] [Iter 735/1050] R2[284/600], Temp: 0.5418, Energy: -52.925537-0.001162j
[2025-09-11 18:42:55] ✓ Checkpoint saved: checkpoint_iter_000735.pkl
[2025-09-11 18:43:11] [Iter 736/1050] R2[285/600], Temp: 0.5392, Energy: -52.896476-0.002337j
[2025-09-11 18:43:26] [Iter 737/1050] R2[286/600], Temp: 0.5366, Energy: -52.919448-0.001209j
[2025-09-11 18:43:41] [Iter 738/1050] R2[287/600], Temp: 0.5340, Energy: -52.914632+0.001446j
[2025-09-11 18:43:56] [Iter 739/1050] R2[288/600], Temp: 0.5314, Energy: -52.841352+0.001958j
[2025-09-11 18:44:12] [Iter 740/1050] R2[289/600], Temp: 0.5288, Energy: -53.031287+0.005102j
[2025-09-11 18:44:27] [Iter 741/1050] R2[290/600], Temp: 0.5262, Energy: -52.987573-0.005978j
[2025-09-11 18:44:42] [Iter 742/1050] R2[291/600], Temp: 0.5236, Energy: -52.988828+0.004511j
[2025-09-11 18:44:57] [Iter 743/1050] R2[292/600], Temp: 0.5209, Energy: -52.931142-0.000850j
[2025-09-11 18:45:12] [Iter 744/1050] R2[293/600], Temp: 0.5183, Energy: -52.915339+0.001521j
[2025-09-11 18:45:27] [Iter 745/1050] R2[294/600], Temp: 0.5157, Energy: -52.917302+0.000860j
[2025-09-11 18:45:43] [Iter 746/1050] R2[295/600], Temp: 0.5131, Energy: -52.939054-0.000892j
[2025-09-11 18:45:58] [Iter 747/1050] R2[296/600], Temp: 0.5105, Energy: -53.001590-0.004475j
[2025-09-11 18:46:13] [Iter 748/1050] R2[297/600], Temp: 0.5079, Energy: -52.912320+0.000389j
[2025-09-11 18:46:28] [Iter 749/1050] R2[298/600], Temp: 0.5052, Energy: -52.903496-0.002015j
[2025-09-11 18:46:43] [Iter 750/1050] R2[299/600], Temp: 0.5026, Energy: -52.935959+0.001537j
[2025-09-11 18:46:59] [Iter 751/1050] R2[300/600], Temp: 0.5000, Energy: -52.961267+0.000941j
[2025-09-11 18:47:14] [Iter 752/1050] R2[301/600], Temp: 0.4974, Energy: -52.949976-0.002717j
[2025-09-11 18:47:29] [Iter 753/1050] R2[302/600], Temp: 0.4948, Energy: -53.100117+0.001164j
[2025-09-11 18:47:44] [Iter 754/1050] R2[303/600], Temp: 0.4921, Energy: -52.958820+0.000677j
[2025-09-11 18:47:59] [Iter 755/1050] R2[304/600], Temp: 0.4895, Energy: -53.067694-0.002460j
[2025-09-11 18:48:15] [Iter 756/1050] R2[305/600], Temp: 0.4869, Energy: -52.847224+0.000106j
[2025-09-11 18:48:30] [Iter 757/1050] R2[306/600], Temp: 0.4843, Energy: -52.853813+0.000672j
[2025-09-11 18:48:45] [Iter 758/1050] R2[307/600], Temp: 0.4817, Energy: -52.907511+0.000288j
[2025-09-11 18:49:00] [Iter 759/1050] R2[308/600], Temp: 0.4791, Energy: -52.848646+0.000236j
[2025-09-11 18:49:15] [Iter 760/1050] R2[309/600], Temp: 0.4764, Energy: -52.884702-0.000186j
[2025-09-11 18:49:31] [Iter 761/1050] R2[310/600], Temp: 0.4738, Energy: -52.936164-0.002666j
[2025-09-11 18:49:46] [Iter 762/1050] R2[311/600], Temp: 0.4712, Energy: -52.983916+0.001101j
[2025-09-11 18:50:01] [Iter 763/1050] R2[312/600], Temp: 0.4686, Energy: -52.916600-0.001636j
[2025-09-11 18:50:16] [Iter 764/1050] R2[313/600], Temp: 0.4660, Energy: -53.005352+0.004989j
[2025-09-11 18:50:31] [Iter 765/1050] R2[314/600], Temp: 0.4634, Energy: -52.923933+0.000138j
[2025-09-11 18:50:47] [Iter 766/1050] R2[315/600], Temp: 0.4608, Energy: -52.936104+0.001934j
[2025-09-11 18:51:02] [Iter 767/1050] R2[316/600], Temp: 0.4582, Energy: -52.749834-0.001289j
[2025-09-11 18:51:17] [Iter 768/1050] R2[317/600], Temp: 0.4556, Energy: -52.742307+0.000098j
[2025-09-11 18:51:32] [Iter 769/1050] R2[318/600], Temp: 0.4529, Energy: -52.685949+0.002158j
[2025-09-11 18:51:47] [Iter 770/1050] R2[319/600], Temp: 0.4503, Energy: -52.794569+0.001214j
[2025-09-11 18:52:02] [Iter 771/1050] R2[320/600], Temp: 0.4477, Energy: -52.868954-0.001822j
[2025-09-11 18:52:18] [Iter 772/1050] R2[321/600], Temp: 0.4451, Energy: -52.777735-0.001743j
[2025-09-11 18:52:33] [Iter 773/1050] R2[322/600], Temp: 0.4425, Energy: -52.792077+0.000421j
[2025-09-11 18:52:48] [Iter 774/1050] R2[323/600], Temp: 0.4399, Energy: -52.817651-0.003605j
[2025-09-11 18:53:03] [Iter 775/1050] R2[324/600], Temp: 0.4373, Energy: -52.805600-0.002272j
[2025-09-11 18:53:18] [Iter 776/1050] R2[325/600], Temp: 0.4347, Energy: -52.984352-0.000654j
[2025-09-11 18:53:34] [Iter 777/1050] R2[326/600], Temp: 0.4321, Energy: -52.971577-0.000772j
[2025-09-11 18:53:49] [Iter 778/1050] R2[327/600], Temp: 0.4295, Energy: -53.119088+0.002124j
[2025-09-11 18:54:04] [Iter 779/1050] R2[328/600], Temp: 0.4270, Energy: -53.039507+0.001387j
[2025-09-11 18:54:19] [Iter 780/1050] R2[329/600], Temp: 0.4244, Energy: -52.895412+0.000649j
[2025-09-11 18:54:34] [Iter 781/1050] R2[330/600], Temp: 0.4218, Energy: -52.902915+0.002445j
[2025-09-11 18:54:50] [Iter 782/1050] R2[331/600], Temp: 0.4192, Energy: -52.779491+0.001809j
[2025-09-11 18:55:05] [Iter 783/1050] R2[332/600], Temp: 0.4166, Energy: -52.779293-0.000122j
[2025-09-11 18:55:20] [Iter 784/1050] R2[333/600], Temp: 0.4140, Energy: -52.923428+0.003582j
[2025-09-11 18:55:35] [Iter 785/1050] R2[334/600], Temp: 0.4115, Energy: -52.928080+0.003987j
[2025-09-11 18:55:50] [Iter 786/1050] R2[335/600], Temp: 0.4089, Energy: -52.916463-0.002862j
[2025-09-11 18:56:06] [Iter 787/1050] R2[336/600], Temp: 0.4063, Energy: -53.005772+0.000444j
[2025-09-11 18:56:21] [Iter 788/1050] R2[337/600], Temp: 0.4037, Energy: -52.873270-0.001396j
[2025-09-11 18:56:36] [Iter 789/1050] R2[338/600], Temp: 0.4012, Energy: -52.875717-0.001325j
[2025-09-11 18:56:51] [Iter 790/1050] R2[339/600], Temp: 0.3986, Energy: -52.885836-0.001642j
[2025-09-11 18:57:06] [Iter 791/1050] R2[340/600], Temp: 0.3960, Energy: -52.827896-0.002594j
[2025-09-11 18:57:22] [Iter 792/1050] R2[341/600], Temp: 0.3935, Energy: -52.949333-0.000695j
[2025-09-11 18:57:37] [Iter 793/1050] R2[342/600], Temp: 0.3909, Energy: -52.903585+0.000091j
[2025-09-11 18:57:52] [Iter 794/1050] R2[343/600], Temp: 0.3884, Energy: -52.748972+0.005010j
[2025-09-11 18:58:07] [Iter 795/1050] R2[344/600], Temp: 0.3858, Energy: -52.762789+0.003126j
[2025-09-11 18:58:22] [Iter 796/1050] R2[345/600], Temp: 0.3833, Energy: -52.805930+0.004629j
[2025-09-11 18:58:37] [Iter 797/1050] R2[346/600], Temp: 0.3807, Energy: -52.709554-0.003205j
[2025-09-11 18:58:53] [Iter 798/1050] R2[347/600], Temp: 0.3782, Energy: -52.897311+0.001526j
[2025-09-11 18:59:08] [Iter 799/1050] R2[348/600], Temp: 0.3757, Energy: -52.936627-0.000792j
[2025-09-11 18:59:23] [Iter 800/1050] R2[349/600], Temp: 0.3731, Energy: -52.862776+0.000459j
[2025-09-11 18:59:38] [Iter 801/1050] R2[350/600], Temp: 0.3706, Energy: -52.866339+0.003291j
[2025-09-11 18:59:53] [Iter 802/1050] R2[351/600], Temp: 0.3681, Energy: -52.842434-0.005073j
[2025-09-11 19:00:08] [Iter 803/1050] R2[352/600], Temp: 0.3655, Energy: -52.930311-0.002045j
[2025-09-11 19:00:24] [Iter 804/1050] R2[353/600], Temp: 0.3630, Energy: -52.924643-0.003713j
[2025-09-11 19:00:39] [Iter 805/1050] R2[354/600], Temp: 0.3605, Energy: -52.946796+0.000631j
[2025-09-11 19:00:54] [Iter 806/1050] R2[355/600], Temp: 0.3580, Energy: -52.904759-0.000323j
[2025-09-11 19:01:09] [Iter 807/1050] R2[356/600], Temp: 0.3555, Energy: -52.941272-0.004374j
[2025-09-11 19:01:24] [Iter 808/1050] R2[357/600], Temp: 0.3530, Energy: -52.924134+0.001563j
[2025-09-11 19:01:40] [Iter 809/1050] R2[358/600], Temp: 0.3505, Energy: -53.016603+0.001952j
[2025-09-11 19:01:55] [Iter 810/1050] R2[359/600], Temp: 0.3480, Energy: -52.913321+0.001436j
[2025-09-11 19:02:10] [Iter 811/1050] R2[360/600], Temp: 0.3455, Energy: -52.901280+0.001957j
[2025-09-11 19:02:25] [Iter 812/1050] R2[361/600], Temp: 0.3430, Energy: -52.912985-0.002190j
[2025-09-11 19:02:40] [Iter 813/1050] R2[362/600], Temp: 0.3405, Energy: -52.892907+0.002629j
[2025-09-11 19:02:56] [Iter 814/1050] R2[363/600], Temp: 0.3380, Energy: -52.816583+0.002303j
[2025-09-11 19:03:11] [Iter 815/1050] R2[364/600], Temp: 0.3356, Energy: -52.961726+0.004606j
[2025-09-11 19:03:26] [Iter 816/1050] R2[365/600], Temp: 0.3331, Energy: -52.859036-0.000084j
[2025-09-11 19:03:41] [Iter 817/1050] R2[366/600], Temp: 0.3306, Energy: -52.832960-0.002084j
[2025-09-11 19:03:57] [Iter 818/1050] R2[367/600], Temp: 0.3282, Energy: -52.860711-0.000586j
[2025-09-11 19:04:12] [Iter 819/1050] R2[368/600], Temp: 0.3257, Energy: -52.912675+0.000664j
[2025-09-11 19:04:27] [Iter 820/1050] R2[369/600], Temp: 0.3233, Energy: -52.898850+0.000984j
[2025-09-11 19:04:42] [Iter 821/1050] R2[370/600], Temp: 0.3208, Energy: -52.911781-0.000428j
[2025-09-11 19:04:57] [Iter 822/1050] R2[371/600], Temp: 0.3184, Energy: -52.925954+0.001676j
[2025-09-11 19:05:12] [Iter 823/1050] R2[372/600], Temp: 0.3159, Energy: -53.091337+0.002982j
[2025-09-11 19:05:28] [Iter 824/1050] R2[373/600], Temp: 0.3135, Energy: -52.915197-0.002002j
[2025-09-11 19:05:43] [Iter 825/1050] R2[374/600], Temp: 0.3111, Energy: -52.867469+0.001164j
[2025-09-11 19:05:58] [Iter 826/1050] R2[375/600], Temp: 0.3087, Energy: -53.032276+0.000157j
[2025-09-11 19:06:14] [Iter 827/1050] R2[376/600], Temp: 0.3062, Energy: -53.029814+0.002734j
[2025-09-11 19:06:29] [Iter 828/1050] R2[377/600], Temp: 0.3038, Energy: -52.985580+0.000289j
[2025-09-11 19:06:44] [Iter 829/1050] R2[378/600], Temp: 0.3014, Energy: -52.926409-0.004837j
[2025-09-11 19:06:59] [Iter 830/1050] R2[379/600], Temp: 0.2990, Energy: -52.828814+0.002394j
[2025-09-11 19:07:14] [Iter 831/1050] R2[380/600], Temp: 0.2966, Energy: -52.830828+0.006242j
[2025-09-11 19:07:30] [Iter 832/1050] R2[381/600], Temp: 0.2942, Energy: -52.938146+0.000766j
[2025-09-11 19:07:45] [Iter 833/1050] R2[382/600], Temp: 0.2919, Energy: -52.881019-0.002096j
[2025-09-11 19:08:00] [Iter 834/1050] R2[383/600], Temp: 0.2895, Energy: -52.925251+0.002753j
[2025-09-11 19:08:15] [Iter 835/1050] R2[384/600], Temp: 0.2871, Energy: -52.765012-0.000386j
[2025-09-11 19:08:31] [Iter 836/1050] R2[385/600], Temp: 0.2847, Energy: -52.923838-0.000185j
[2025-09-11 19:08:46] [Iter 837/1050] R2[386/600], Temp: 0.2824, Energy: -53.061602-0.001638j
[2025-09-11 19:09:01] [Iter 838/1050] R2[387/600], Temp: 0.2800, Energy: -53.023294+0.003491j
[2025-09-11 19:09:16] [Iter 839/1050] R2[388/600], Temp: 0.2777, Energy: -52.789218-0.001303j
[2025-09-11 19:09:31] [Iter 840/1050] R2[389/600], Temp: 0.2753, Energy: -52.864443+0.003642j
[2025-09-11 19:09:31] ✓ Checkpoint saved: checkpoint_iter_000840.pkl
[2025-09-11 19:09:47] [Iter 841/1050] R2[390/600], Temp: 0.2730, Energy: -52.748394+0.001432j
[2025-09-11 19:10:02] [Iter 842/1050] R2[391/600], Temp: 0.2707, Energy: -52.730761-0.001588j
[2025-09-11 19:10:17] [Iter 843/1050] R2[392/600], Temp: 0.2684, Energy: -52.812672+0.002735j
[2025-09-11 19:10:32] [Iter 844/1050] R2[393/600], Temp: 0.2660, Energy: -52.743394-0.000392j
[2025-09-11 19:10:48] [Iter 845/1050] R2[394/600], Temp: 0.2637, Energy: -52.779240-0.005146j
[2025-09-11 19:11:03] [Iter 846/1050] R2[395/600], Temp: 0.2614, Energy: -52.734030-0.000271j
[2025-09-11 19:11:18] [Iter 847/1050] R2[396/600], Temp: 0.2591, Energy: -52.912668-0.001735j
[2025-09-11 19:11:33] [Iter 848/1050] R2[397/600], Temp: 0.2568, Energy: -52.984270+0.002571j
[2025-09-11 19:11:49] [Iter 849/1050] R2[398/600], Temp: 0.2545, Energy: -53.021868+0.001951j
[2025-09-11 19:12:04] [Iter 850/1050] R2[399/600], Temp: 0.2523, Energy: -52.805955-0.000484j
[2025-09-11 19:12:19] [Iter 851/1050] R2[400/600], Temp: 0.2500, Energy: -52.847449+0.000823j
[2025-09-11 19:12:34] [Iter 852/1050] R2[401/600], Temp: 0.2477, Energy: -52.986813-0.004135j
[2025-09-11 19:12:50] [Iter 853/1050] R2[402/600], Temp: 0.2455, Energy: -52.844599-0.000793j
[2025-09-11 19:13:05] [Iter 854/1050] R2[403/600], Temp: 0.2432, Energy: -52.797374+0.000305j
[2025-09-11 19:13:20] [Iter 855/1050] R2[404/600], Temp: 0.2410, Energy: -52.897125+0.002644j
[2025-09-11 19:13:35] [Iter 856/1050] R2[405/600], Temp: 0.2388, Energy: -52.884155+0.001165j
[2025-09-11 19:13:51] [Iter 857/1050] R2[406/600], Temp: 0.2365, Energy: -52.866588+0.001277j
[2025-09-11 19:14:06] [Iter 858/1050] R2[407/600], Temp: 0.2343, Energy: -52.873801-0.001066j
[2025-09-11 19:14:21] [Iter 859/1050] R2[408/600], Temp: 0.2321, Energy: -52.835064-0.001797j
[2025-09-11 19:14:36] [Iter 860/1050] R2[409/600], Temp: 0.2299, Energy: -52.913411-0.000222j
[2025-09-11 19:14:51] [Iter 861/1050] R2[410/600], Temp: 0.2277, Energy: -52.934175-0.000228j
[2025-09-11 19:15:07] [Iter 862/1050] R2[411/600], Temp: 0.2255, Energy: -52.878503+0.000556j
[2025-09-11 19:15:19] [Iter 863/1050] R2[412/600], Temp: 0.2233, Energy: -52.938282+0.001803j
[2025-09-11 19:15:29] [Iter 864/1050] R2[413/600], Temp: 0.2211, Energy: -52.976307+0.002068j
[2025-09-11 19:15:39] [Iter 865/1050] R2[414/600], Temp: 0.2190, Energy: -52.917529+0.003202j
[2025-09-11 19:15:49] [Iter 866/1050] R2[415/600], Temp: 0.2168, Energy: -52.918997-0.000404j
[2025-09-11 19:16:00] [Iter 867/1050] R2[416/600], Temp: 0.2146, Energy: -53.021745-0.002984j
[2025-09-11 19:16:10] [Iter 868/1050] R2[417/600], Temp: 0.2125, Energy: -52.867386+0.003157j
[2025-09-11 19:16:22] [Iter 869/1050] R2[418/600], Temp: 0.2104, Energy: -52.816921+0.001269j
[2025-09-11 19:16:37] [Iter 870/1050] R2[419/600], Temp: 0.2082, Energy: -52.920893+0.006673j
[2025-09-11 19:16:53] [Iter 871/1050] R2[420/600], Temp: 0.2061, Energy: -52.852921-0.000877j
[2025-09-11 19:17:08] [Iter 872/1050] R2[421/600], Temp: 0.2040, Energy: -52.807429-0.001619j
[2025-09-11 19:17:23] [Iter 873/1050] R2[422/600], Temp: 0.2019, Energy: -52.899723+0.000247j
[2025-09-11 19:17:39] [Iter 874/1050] R2[423/600], Temp: 0.1998, Energy: -52.863737-0.000384j
[2025-09-11 19:17:54] [Iter 875/1050] R2[424/600], Temp: 0.1977, Energy: -52.941782-0.002047j
[2025-09-11 19:18:09] [Iter 876/1050] R2[425/600], Temp: 0.1956, Energy: -53.025769-0.002545j
[2025-09-11 19:18:25] [Iter 877/1050] R2[426/600], Temp: 0.1935, Energy: -53.004714+0.000977j
[2025-09-11 19:18:40] [Iter 878/1050] R2[427/600], Temp: 0.1915, Energy: -53.055202+0.004694j
[2025-09-11 19:18:55] [Iter 879/1050] R2[428/600], Temp: 0.1894, Energy: -53.094665+0.002306j
[2025-09-11 19:19:11] [Iter 880/1050] R2[429/600], Temp: 0.1874, Energy: -52.916480+0.000383j
[2025-09-11 19:19:26] [Iter 881/1050] R2[430/600], Temp: 0.1853, Energy: -52.894547+0.000536j
[2025-09-11 19:19:41] [Iter 882/1050] R2[431/600], Temp: 0.1833, Energy: -52.966156-0.001771j
[2025-09-11 19:19:56] [Iter 883/1050] R2[432/600], Temp: 0.1813, Energy: -52.816639+0.004522j
[2025-09-11 19:20:12] [Iter 884/1050] R2[433/600], Temp: 0.1793, Energy: -52.848587-0.000807j
[2025-09-11 19:20:27] [Iter 885/1050] R2[434/600], Temp: 0.1773, Energy: -52.801643+0.001846j
[2025-09-11 19:20:42] [Iter 886/1050] R2[435/600], Temp: 0.1753, Energy: -52.831050+0.002048j
[2025-09-11 19:20:58] [Iter 887/1050] R2[436/600], Temp: 0.1733, Energy: -52.819976+0.000866j
[2025-09-11 19:21:13] [Iter 888/1050] R2[437/600], Temp: 0.1713, Energy: -52.906624-0.001190j
[2025-09-11 19:21:28] [Iter 889/1050] R2[438/600], Temp: 0.1693, Energy: -52.901023+0.002097j
[2025-09-11 19:21:44] [Iter 890/1050] R2[439/600], Temp: 0.1674, Energy: -52.893441-0.001955j
[2025-09-11 19:21:59] [Iter 891/1050] R2[440/600], Temp: 0.1654, Energy: -52.830177-0.000958j
[2025-09-11 19:22:14] [Iter 892/1050] R2[441/600], Temp: 0.1635, Energy: -52.791963+0.001210j
[2025-09-11 19:22:30] [Iter 893/1050] R2[442/600], Temp: 0.1616, Energy: -52.788489+0.002041j
[2025-09-11 19:22:45] [Iter 894/1050] R2[443/600], Temp: 0.1596, Energy: -52.757316+0.000598j
[2025-09-11 19:23:00] [Iter 895/1050] R2[444/600], Temp: 0.1577, Energy: -52.842439+0.000200j
[2025-09-11 19:23:16] [Iter 896/1050] R2[445/600], Temp: 0.1558, Energy: -52.941226+0.000555j
[2025-09-11 19:23:31] [Iter 897/1050] R2[446/600], Temp: 0.1539, Energy: -52.903685+0.000462j
[2025-09-11 19:23:46] [Iter 898/1050] R2[447/600], Temp: 0.1520, Energy: -52.757723-0.000000j
[2025-09-11 19:24:01] [Iter 899/1050] R2[448/600], Temp: 0.1502, Energy: -52.849559-0.003738j
[2025-09-11 19:24:17] [Iter 900/1050] R2[449/600], Temp: 0.1483, Energy: -52.819386+0.003269j
[2025-09-11 19:24:32] [Iter 901/1050] R2[450/600], Temp: 0.1464, Energy: -52.786069-0.001155j
[2025-09-11 19:24:47] [Iter 902/1050] R2[451/600], Temp: 0.1446, Energy: -52.933926-0.002659j
[2025-09-11 19:25:03] [Iter 903/1050] R2[452/600], Temp: 0.1428, Energy: -52.926533-0.001703j
[2025-09-11 19:25:18] [Iter 904/1050] R2[453/600], Temp: 0.1409, Energy: -52.902138-0.000463j
[2025-09-11 19:25:33] [Iter 905/1050] R2[454/600], Temp: 0.1391, Energy: -52.867362-0.002573j
[2025-09-11 19:25:49] [Iter 906/1050] R2[455/600], Temp: 0.1373, Energy: -52.952252-0.000880j
[2025-09-11 19:25:59] [Iter 907/1050] R2[456/600], Temp: 0.1355, Energy: -52.868055-0.000856j
[2025-09-11 19:26:12] [Iter 908/1050] R2[457/600], Temp: 0.1337, Energy: -52.880957+0.001316j
[2025-09-11 19:26:24] [Iter 909/1050] R2[458/600], Temp: 0.1320, Energy: -52.869036+0.003613j
[2025-09-11 19:26:36] [Iter 910/1050] R2[459/600], Temp: 0.1302, Energy: -52.952717+0.002722j
[2025-09-11 19:26:52] [Iter 911/1050] R2[460/600], Temp: 0.1284, Energy: -52.917577+0.003762j
[2025-09-11 19:27:07] [Iter 912/1050] R2[461/600], Temp: 0.1267, Energy: -52.964537-0.002011j
[2025-09-11 19:27:22] [Iter 913/1050] R2[462/600], Temp: 0.1249, Energy: -52.944632+0.004742j
[2025-09-11 19:27:37] [Iter 914/1050] R2[463/600], Temp: 0.1232, Energy: -52.859219-0.001308j
[2025-09-11 19:27:53] [Iter 915/1050] R2[464/600], Temp: 0.1215, Energy: -52.885780+0.000056j
[2025-09-11 19:28:08] [Iter 916/1050] R2[465/600], Temp: 0.1198, Energy: -52.905485+0.002037j
[2025-09-11 19:28:23] [Iter 917/1050] R2[466/600], Temp: 0.1181, Energy: -53.004932+0.003620j
[2025-09-11 19:28:38] [Iter 918/1050] R2[467/600], Temp: 0.1164, Energy: -52.877194-0.004650j
[2025-09-11 19:28:54] [Iter 919/1050] R2[468/600], Temp: 0.1147, Energy: -52.990074+0.000827j
[2025-09-11 19:29:09] [Iter 920/1050] R2[469/600], Temp: 0.1131, Energy: -52.969395+0.000652j
[2025-09-11 19:29:24] [Iter 921/1050] R2[470/600], Temp: 0.1114, Energy: -52.977367-0.000229j
[2025-09-11 19:29:39] [Iter 922/1050] R2[471/600], Temp: 0.1098, Energy: -52.928661+0.001451j
[2025-09-11 19:29:54] [Iter 923/1050] R2[472/600], Temp: 0.1082, Energy: -53.023262+0.003170j
[2025-09-11 19:30:10] [Iter 924/1050] R2[473/600], Temp: 0.1065, Energy: -53.043689-0.000432j
[2025-09-11 19:30:25] [Iter 925/1050] R2[474/600], Temp: 0.1049, Energy: -52.982719-0.003956j
[2025-09-11 19:30:40] [Iter 926/1050] R2[475/600], Temp: 0.1033, Energy: -53.108327+0.002774j
[2025-09-11 19:30:55] [Iter 927/1050] R2[476/600], Temp: 0.1017, Energy: -52.992208+0.001300j
[2025-09-11 19:31:10] [Iter 928/1050] R2[477/600], Temp: 0.1002, Energy: -52.874159-0.000168j
[2025-09-11 19:31:26] [Iter 929/1050] R2[478/600], Temp: 0.0986, Energy: -52.977582-0.000821j
[2025-09-11 19:31:41] [Iter 930/1050] R2[479/600], Temp: 0.0970, Energy: -52.937730+0.001299j
[2025-09-11 19:31:56] [Iter 931/1050] R2[480/600], Temp: 0.0955, Energy: -53.058123+0.000343j
[2025-09-11 19:32:11] [Iter 932/1050] R2[481/600], Temp: 0.0940, Energy: -52.988999-0.000983j
[2025-09-11 19:32:26] [Iter 933/1050] R2[482/600], Temp: 0.0924, Energy: -52.932538-0.000792j
[2025-09-11 19:32:42] [Iter 934/1050] R2[483/600], Temp: 0.0909, Energy: -52.957709-0.000688j
[2025-09-11 19:32:57] [Iter 935/1050] R2[484/600], Temp: 0.0894, Energy: -52.806799+0.001565j
[2025-09-11 19:33:12] [Iter 936/1050] R2[485/600], Temp: 0.0879, Energy: -52.883804+0.001813j
[2025-09-11 19:33:27] [Iter 937/1050] R2[486/600], Temp: 0.0865, Energy: -52.935739+0.003590j
[2025-09-11 19:33:43] [Iter 938/1050] R2[487/600], Temp: 0.0850, Energy: -52.857578+0.000880j
[2025-09-11 19:33:58] [Iter 939/1050] R2[488/600], Temp: 0.0835, Energy: -52.771644+0.001038j
[2025-09-11 19:34:13] [Iter 940/1050] R2[489/600], Temp: 0.0821, Energy: -52.734556+0.001130j
[2025-09-11 19:34:28] [Iter 941/1050] R2[490/600], Temp: 0.0807, Energy: -52.849184-0.001479j
[2025-09-11 19:34:43] [Iter 942/1050] R2[491/600], Temp: 0.0792, Energy: -52.853488-0.003304j
[2025-09-11 19:34:59] [Iter 943/1050] R2[492/600], Temp: 0.0778, Energy: -52.746072-0.001100j
[2025-09-11 19:35:14] [Iter 944/1050] R2[493/600], Temp: 0.0764, Energy: -52.876959+0.002346j
[2025-09-11 19:35:29] [Iter 945/1050] R2[494/600], Temp: 0.0751, Energy: -52.690482-0.000683j
[2025-09-11 19:35:29] ✓ Checkpoint saved: checkpoint_iter_000945.pkl
[2025-09-11 19:35:44] [Iter 946/1050] R2[495/600], Temp: 0.0737, Energy: -52.664722+0.001997j
[2025-09-11 19:36:00] [Iter 947/1050] R2[496/600], Temp: 0.0723, Energy: -52.923148-0.000124j
[2025-09-11 19:36:15] [Iter 948/1050] R2[497/600], Temp: 0.0710, Energy: -52.953815+0.002288j
[2025-09-11 19:36:30] [Iter 949/1050] R2[498/600], Temp: 0.0696, Energy: -52.946209+0.001158j
[2025-09-11 19:36:45] [Iter 950/1050] R2[499/600], Temp: 0.0683, Energy: -52.967175-0.000053j
[2025-09-11 19:37:01] [Iter 951/1050] R2[500/600], Temp: 0.0670, Energy: -52.896888+0.000064j
[2025-09-11 19:37:16] [Iter 952/1050] R2[501/600], Temp: 0.0657, Energy: -52.956255-0.003011j
[2025-09-11 19:37:31] [Iter 953/1050] R2[502/600], Temp: 0.0644, Energy: -52.891718+0.000067j
[2025-09-11 19:37:46] [Iter 954/1050] R2[503/600], Temp: 0.0631, Energy: -52.947504+0.004457j
[2025-09-11 19:38:02] [Iter 955/1050] R2[504/600], Temp: 0.0618, Energy: -52.971666-0.001809j
[2025-09-11 19:38:17] [Iter 956/1050] R2[505/600], Temp: 0.0606, Energy: -52.864539-0.000242j
[2025-09-11 19:38:32] [Iter 957/1050] R2[506/600], Temp: 0.0593, Energy: -52.919579+0.000123j
[2025-09-11 19:38:47] [Iter 958/1050] R2[507/600], Temp: 0.0581, Energy: -52.887758+0.000018j
[2025-09-11 19:39:02] [Iter 959/1050] R2[508/600], Temp: 0.0569, Energy: -52.923228+0.001784j
[2025-09-11 19:39:18] [Iter 960/1050] R2[509/600], Temp: 0.0557, Energy: -52.844064+0.002061j
[2025-09-11 19:39:33] [Iter 961/1050] R2[510/600], Temp: 0.0545, Energy: -53.045586+0.009714j
[2025-09-11 19:39:48] [Iter 962/1050] R2[511/600], Temp: 0.0533, Energy: -52.873954-0.000578j
[2025-09-11 19:40:03] [Iter 963/1050] R2[512/600], Temp: 0.0521, Energy: -52.799122-0.002788j
[2025-09-11 19:40:18] [Iter 964/1050] R2[513/600], Temp: 0.0510, Energy: -52.818518+0.000048j
[2025-09-11 19:40:34] [Iter 965/1050] R2[514/600], Temp: 0.0498, Energy: -52.895921+0.001004j
[2025-09-11 19:40:49] [Iter 966/1050] R2[515/600], Temp: 0.0487, Energy: -52.838944+0.000358j
[2025-09-11 19:41:04] [Iter 967/1050] R2[516/600], Temp: 0.0476, Energy: -52.946609-0.001790j
[2025-09-11 19:41:19] [Iter 968/1050] R2[517/600], Temp: 0.0465, Energy: -52.831680-0.002064j
[2025-09-11 19:41:35] [Iter 969/1050] R2[518/600], Temp: 0.0454, Energy: -52.925142+0.000973j
[2025-09-11 19:41:50] [Iter 970/1050] R2[519/600], Temp: 0.0443, Energy: -52.972108-0.000227j
[2025-09-11 19:42:05] [Iter 971/1050] R2[520/600], Temp: 0.0432, Energy: -52.873517+0.004081j
[2025-09-11 19:42:20] [Iter 972/1050] R2[521/600], Temp: 0.0422, Energy: -52.889537-0.001355j
[2025-09-11 19:42:35] [Iter 973/1050] R2[522/600], Temp: 0.0411, Energy: -52.921512-0.001118j
[2025-09-11 19:42:51] [Iter 974/1050] R2[523/600], Temp: 0.0401, Energy: -52.820912-0.000131j
[2025-09-11 19:43:06] [Iter 975/1050] R2[524/600], Temp: 0.0391, Energy: -52.992322+0.000450j
[2025-09-11 19:43:21] [Iter 976/1050] R2[525/600], Temp: 0.0381, Energy: -52.859959+0.000230j
[2025-09-11 19:43:36] [Iter 977/1050] R2[526/600], Temp: 0.0371, Energy: -52.853535+0.002148j
[2025-09-11 19:43:51] [Iter 978/1050] R2[527/600], Temp: 0.0361, Energy: -52.831658+0.001299j
[2025-09-11 19:44:07] [Iter 979/1050] R2[528/600], Temp: 0.0351, Energy: -52.830711+0.004107j
[2025-09-11 19:44:22] [Iter 980/1050] R2[529/600], Temp: 0.0342, Energy: -52.888930+0.000331j
[2025-09-11 19:44:37] [Iter 981/1050] R2[530/600], Temp: 0.0332, Energy: -52.878624-0.004100j
[2025-09-11 19:44:52] [Iter 982/1050] R2[531/600], Temp: 0.0323, Energy: -52.853583-0.004073j
[2025-09-11 19:45:07] [Iter 983/1050] R2[532/600], Temp: 0.0314, Energy: -52.926640-0.003180j
[2025-09-11 19:45:22] [Iter 984/1050] R2[533/600], Temp: 0.0305, Energy: -52.869654+0.000733j
[2025-09-11 19:45:38] [Iter 985/1050] R2[534/600], Temp: 0.0296, Energy: -52.811488+0.004397j
[2025-09-11 19:45:53] [Iter 986/1050] R2[535/600], Temp: 0.0287, Energy: -52.798482+0.000338j
[2025-09-11 19:46:08] [Iter 987/1050] R2[536/600], Temp: 0.0278, Energy: -52.846909-0.001239j
[2025-09-11 19:46:23] [Iter 988/1050] R2[537/600], Temp: 0.0270, Energy: -52.816379+0.001388j
[2025-09-11 19:46:39] [Iter 989/1050] R2[538/600], Temp: 0.0261, Energy: -52.905755+0.000007j
[2025-09-11 19:46:54] [Iter 990/1050] R2[539/600], Temp: 0.0253, Energy: -52.948746+0.001547j
[2025-09-11 19:47:09] [Iter 991/1050] R2[540/600], Temp: 0.0245, Energy: -52.878223+0.001627j
[2025-09-11 19:47:24] [Iter 992/1050] R2[541/600], Temp: 0.0237, Energy: -52.766614-0.000191j
[2025-09-11 19:47:39] [Iter 993/1050] R2[542/600], Temp: 0.0229, Energy: -52.828398+0.001643j
[2025-09-11 19:47:55] [Iter 994/1050] R2[543/600], Temp: 0.0221, Energy: -52.962048+0.003206j
[2025-09-11 19:48:10] [Iter 995/1050] R2[544/600], Temp: 0.0213, Energy: -52.895138+0.002040j
[2025-09-11 19:48:25] [Iter 996/1050] R2[545/600], Temp: 0.0206, Energy: -53.077439-0.001771j
[2025-09-11 19:48:40] [Iter 997/1050] R2[546/600], Temp: 0.0199, Energy: -53.121469+0.000417j
[2025-09-11 19:48:56] [Iter 998/1050] R2[547/600], Temp: 0.0191, Energy: -52.956438-0.000972j
[2025-09-11 19:49:11] [Iter 999/1050] R2[548/600], Temp: 0.0184, Energy: -52.830127+0.002696j
[2025-09-11 19:49:26] [Iter 1000/1050] R2[549/600], Temp: 0.0177, Energy: -52.861589-0.003251j
[2025-09-11 19:49:41] [Iter 1001/1050] R2[550/600], Temp: 0.0170, Energy: -52.844422+0.002345j
[2025-09-11 19:49:56] [Iter 1002/1050] R2[551/600], Temp: 0.0164, Energy: -52.842077+0.003004j
[2025-09-11 19:50:12] [Iter 1003/1050] R2[552/600], Temp: 0.0157, Energy: -52.964339-0.001979j
[2025-09-11 19:50:27] [Iter 1004/1050] R2[553/600], Temp: 0.0151, Energy: -52.878281+0.004184j
[2025-09-11 19:50:42] [Iter 1005/1050] R2[554/600], Temp: 0.0144, Energy: -52.974425+0.002630j
[2025-09-11 19:50:57] [Iter 1006/1050] R2[555/600], Temp: 0.0138, Energy: -52.973680-0.002315j
[2025-09-11 19:51:13] [Iter 1007/1050] R2[556/600], Temp: 0.0132, Energy: -53.013413+0.010475j
[2025-09-11 19:51:28] [Iter 1008/1050] R2[557/600], Temp: 0.0126, Energy: -53.037765+0.000213j
[2025-09-11 19:51:43] [Iter 1009/1050] R2[558/600], Temp: 0.0120, Energy: -52.951353+0.000945j
[2025-09-11 19:51:58] [Iter 1010/1050] R2[559/600], Temp: 0.0115, Energy: -52.842949+0.001554j
[2025-09-11 19:52:13] [Iter 1011/1050] R2[560/600], Temp: 0.0109, Energy: -52.907883-0.002007j
[2025-09-11 19:52:29] [Iter 1012/1050] R2[561/600], Temp: 0.0104, Energy: -52.922471-0.001534j
[2025-09-11 19:52:44] [Iter 1013/1050] R2[562/600], Temp: 0.0099, Energy: -52.911021-0.000651j
[2025-09-11 19:52:59] [Iter 1014/1050] R2[563/600], Temp: 0.0094, Energy: -52.846817+0.002053j
[2025-09-11 19:53:14] [Iter 1015/1050] R2[564/600], Temp: 0.0089, Energy: -52.877967+0.003677j
[2025-09-11 19:53:29] [Iter 1016/1050] R2[565/600], Temp: 0.0084, Energy: -52.866227+0.002271j
[2025-09-11 19:53:45] [Iter 1017/1050] R2[566/600], Temp: 0.0079, Energy: -52.802369+0.000694j
[2025-09-11 19:54:00] [Iter 1018/1050] R2[567/600], Temp: 0.0074, Energy: -52.725140-0.000646j
[2025-09-11 19:54:15] [Iter 1019/1050] R2[568/600], Temp: 0.0070, Energy: -52.777820+0.000574j
[2025-09-11 19:54:30] [Iter 1020/1050] R2[569/600], Temp: 0.0066, Energy: -52.851014-0.002728j
[2025-09-11 19:54:45] [Iter 1021/1050] R2[570/600], Temp: 0.0062, Energy: -52.879293-0.000160j
[2025-09-11 19:55:01] [Iter 1022/1050] R2[571/600], Temp: 0.0058, Energy: -52.762778+0.002813j
[2025-09-11 19:55:16] [Iter 1023/1050] R2[572/600], Temp: 0.0054, Energy: -52.800549+0.000335j
[2025-09-11 19:55:31] [Iter 1024/1050] R2[573/600], Temp: 0.0050, Energy: -52.765600+0.003641j
[2025-09-11 19:55:46] [Iter 1025/1050] R2[574/600], Temp: 0.0046, Energy: -52.851772+0.000999j
[2025-09-11 19:56:01] [Iter 1026/1050] R2[575/600], Temp: 0.0043, Energy: -52.883436+0.001120j
[2025-09-11 19:56:17] [Iter 1027/1050] R2[576/600], Temp: 0.0039, Energy: -52.906451-0.001109j
[2025-09-11 19:56:32] [Iter 1028/1050] R2[577/600], Temp: 0.0036, Energy: -52.735618-0.000124j
[2025-09-11 19:56:47] [Iter 1029/1050] R2[578/600], Temp: 0.0033, Energy: -52.904080-0.001382j
[2025-09-11 19:57:02] [Iter 1030/1050] R2[579/600], Temp: 0.0030, Energy: -52.902426-0.002911j
[2025-09-11 19:57:18] [Iter 1031/1050] R2[580/600], Temp: 0.0027, Energy: -52.937149-0.002907j
[2025-09-11 19:57:33] [Iter 1032/1050] R2[581/600], Temp: 0.0025, Energy: -52.970355+0.002233j
[2025-09-11 19:57:48] [Iter 1033/1050] R2[582/600], Temp: 0.0022, Energy: -53.054243-0.002850j
[2025-09-11 19:58:03] [Iter 1034/1050] R2[583/600], Temp: 0.0020, Energy: -53.029757-0.001150j
[2025-09-11 19:58:19] [Iter 1035/1050] R2[584/600], Temp: 0.0018, Energy: -52.971411+0.001791j
[2025-09-11 19:58:34] [Iter 1036/1050] R2[585/600], Temp: 0.0015, Energy: -52.995659-0.002064j
[2025-09-11 19:58:49] [Iter 1037/1050] R2[586/600], Temp: 0.0013, Energy: -53.028887-0.004322j
[2025-09-11 19:59:04] [Iter 1038/1050] R2[587/600], Temp: 0.0012, Energy: -53.131211-0.000672j
[2025-09-11 19:59:20] [Iter 1039/1050] R2[588/600], Temp: 0.0010, Energy: -53.091399-0.001885j
[2025-09-11 19:59:35] [Iter 1040/1050] R2[589/600], Temp: 0.0008, Energy: -53.048147+0.000606j
[2025-09-11 19:59:50] [Iter 1041/1050] R2[590/600], Temp: 0.0007, Energy: -52.958690-0.000566j
[2025-09-11 20:00:05] [Iter 1042/1050] R2[591/600], Temp: 0.0006, Energy: -53.061902-0.001603j
[2025-09-11 20:00:20] [Iter 1043/1050] R2[592/600], Temp: 0.0004, Energy: -52.860482-0.000720j
[2025-09-11 20:00:30] [Iter 1044/1050] R2[593/600], Temp: 0.0003, Energy: -52.766732+0.000227j
[2025-09-11 20:00:41] [Iter 1045/1050] R2[594/600], Temp: 0.0002, Energy: -52.929296+0.000152j
[2025-09-11 20:00:51] [Iter 1046/1050] R2[595/600], Temp: 0.0002, Energy: -52.943688-0.000246j
[2025-09-11 20:01:01] [Iter 1047/1050] R2[596/600], Temp: 0.0001, Energy: -52.813864-0.000628j
[2025-09-11 20:01:11] [Iter 1048/1050] R2[597/600], Temp: 0.0001, Energy: -52.712315-0.001710j
[2025-09-11 20:01:21] [Iter 1049/1050] R2[598/600], Temp: 0.0000, Energy: -52.836457-0.000896j
[2025-09-11 20:01:31] [Iter 1050/1050] R2[599/600], Temp: 0.0000, Energy: -52.767313+0.001685j
[2025-09-11 20:01:32] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-09-11 20:01:32] ✅ Training completed | Restarts: 2
[2025-09-11 20:01:32] ============================================================
[2025-09-11 20:01:32] Training completed | Runtime: 15869.5s
[2025-09-11 20:01:38] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-11 20:01:38] ============================================================
