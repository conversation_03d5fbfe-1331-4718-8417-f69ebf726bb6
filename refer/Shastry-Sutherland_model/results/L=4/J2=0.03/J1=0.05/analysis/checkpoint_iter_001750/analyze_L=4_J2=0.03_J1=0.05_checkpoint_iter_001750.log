[2025-09-13 06:40:11] 使用checkpoint文件: results/L=4/J2=0.03/J1=0.05/training/checkpoints/checkpoint_iter_001750.pkl
[2025-09-13 06:40:28] ✓ 从checkpoint加载参数: 1750
[2025-09-13 06:40:28]   - 能量: -53.244679+0.000943j ± 0.041419
[2025-09-13 06:40:28] ================================================================================
[2025-09-13 06:40:28] 加载量子态: L=4, J2=0.03, J1=0.05, checkpoint=checkpoint_iter_001750
[2025-09-13 06:40:28] 使用采样数目: 1048576
[2025-09-13 06:40:28] 设置样本数为: 1048576
[2025-09-13 06:40:28] 开始生成共享样本集...
[2025-09-13 06:43:29] 样本生成完成,耗时: 180.986 秒
[2025-09-13 06:43:29] ================================================================================
[2025-09-13 06:43:29] 开始计算自旋结构因子...
[2025-09-13 06:43:29] 初始化操作符缓存...
[2025-09-13 06:43:29] 预构建所有自旋相关操作符...
[2025-09-13 06:43:29] 开始计算自旋相关函数...
[2025-09-13 06:43:46] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 16.795s
[2025-09-13 06:44:04] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 17.822s
[2025-09-13 06:44:13] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.345s
[2025-09-13 06:44:23] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.375s
[2025-09-13 06:44:32] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.348s
[2025-09-13 06:44:41] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.347s
[2025-09-13 06:44:51] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.329s
[2025-09-13 06:45:00] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.363s
[2025-09-13 06:45:09] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.337s
[2025-09-13 06:45:19] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.363s
[2025-09-13 06:45:28] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.346s
[2025-09-13 06:45:38] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.354s
[2025-09-13 06:45:47] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.332s
[2025-09-13 06:45:56] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.390s
[2025-09-13 06:46:06] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.377s
[2025-09-13 06:46:15] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.352s
[2025-09-13 06:46:24] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.365s
[2025-09-13 06:46:34] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.467s
[2025-09-13 06:46:43] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.247s
[2025-09-13 06:46:53] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.346s
[2025-09-13 06:47:02] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.368s
[2025-09-13 06:47:11] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.373s
[2025-09-13 06:47:21] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.337s
[2025-09-13 06:47:30] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.338s
[2025-09-13 06:47:39] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.343s
[2025-09-13 06:47:49] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.367s
[2025-09-13 06:47:58] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.338s
[2025-09-13 06:48:07] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.340s
[2025-09-13 06:48:17] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.381s
[2025-09-13 06:48:26] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.263s
[2025-09-13 06:48:35] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.345s
[2025-09-13 06:48:45] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.368s
[2025-09-13 06:48:54] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.349s
[2025-09-13 06:49:04] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.350s
[2025-09-13 06:49:13] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.366s
[2025-09-13 06:49:22] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.372s
[2025-09-13 06:49:32] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.351s
[2025-09-13 06:49:41] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.387s
[2025-09-13 06:49:50] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.315s
[2025-09-13 06:50:00] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.437s
[2025-09-13 06:50:09] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.333s
[2025-09-13 06:50:19] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.342s
[2025-09-13 06:50:28] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.340s
[2025-09-13 06:50:37] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.366s
[2025-09-13 06:50:47] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.318s
[2025-09-13 06:50:56] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.340s
[2025-09-13 06:51:05] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.385s
[2025-09-13 06:51:15] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.343s
[2025-09-13 06:51:24] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.372s
[2025-09-13 06:51:33] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.310s
[2025-09-13 06:51:43] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.345s
[2025-09-13 06:51:52] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.400s
[2025-09-13 06:52:02] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.353s
[2025-09-13 06:52:11] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.388s
[2025-09-13 06:52:20] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.351s
[2025-09-13 06:52:30] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.379s
[2025-09-13 06:52:39] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.363s
[2025-09-13 06:52:48] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.351s
[2025-09-13 06:52:58] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.366s
[2025-09-13 06:53:07] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.358s
[2025-09-13 06:53:16] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.338s
[2025-09-13 06:53:26] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.344s
[2025-09-13 06:53:35] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.397s
[2025-09-13 06:53:45] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.367s
[2025-09-13 06:53:45] 自旋相关函数计算完成,总耗时 615.35 秒
[2025-09-13 06:53:46] 计算傅里叶变换...
[2025-09-13 06:53:48] 自旋结构因子计算完成
[2025-09-13 06:53:49] 自旋相关函数平均误差: 0.000666
