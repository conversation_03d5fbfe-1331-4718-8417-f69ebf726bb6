[2025-09-09 10:36:56] ==================================================
[2025-09-09 10:36:56] GCNN for Shastry-Sutherland Model
[2025-09-09 10:36:56] ==================================================
[2025-09-09 10:36:56] System parameters:
[2025-09-09 10:36:56]   - System size: L=4, N=64
[2025-09-09 10:36:56]   - System parameters: J1=0.05, J2=0.03, Q=0.97
[2025-09-09 10:36:56] --------------------------------------------------
[2025-09-09 10:36:56] Model parameters:
[2025-09-09 10:36:56]   - Number of layers = 4
[2025-09-09 10:36:56]   - Number of features = 4
[2025-09-09 10:36:56]   - Total parameters = 12572
[2025-09-09 10:36:56] --------------------------------------------------
[2025-09-09 10:36:56] Training parameters:
[2025-09-09 10:36:56]   - Learning rate: 0.015
[2025-09-09 10:36:56]   - Total iterations: 2250
[2025-09-09 10:36:56]   - Annealing cycles: 4
[2025-09-09 10:36:56]   - Initial period: 150
[2025-09-09 10:36:56]   - Period multiplier: 2.0
[2025-09-09 10:36:56]   - Temperature range: 0.0-1.0
[2025-09-09 10:36:56]   - Samples: 16384
[2025-09-09 10:36:56]   - Discarded samples: 0
[2025-09-09 10:36:56]   - Chunk size: 2048
[2025-09-09 10:36:56]   - Diagonal shift: 0.2
[2025-09-09 10:36:56]   - Gradient clipping: 1.0
[2025-09-09 10:36:56]   - Checkpoint enabled: interval=250
[2025-09-09 10:36:56]   - Checkpoint directory: results/L=4/J2=0.03/J1=0.05/training/checkpoints
[2025-09-09 10:36:56] --------------------------------------------------
[2025-09-09 10:36:56] Device status:
[2025-09-09 10:36:56]   - Devices model: NVIDIA H200 NVL
[2025-09-09 10:36:56]   - Number of devices: 1
[2025-09-09 10:36:56]   - Sharding: True
[2025-09-09 10:36:56] ============================================================
[2025-09-09 10:38:41] [Iter 1/2250] R0[0/150], Temp: 1.0000, Energy: 1.838668-0.000021j
[2025-09-09 10:39:00] [Iter 2/2250] R0[1/150], Temp: 0.9999, Energy: 1.838853+0.000213j
[2025-09-09 10:39:30] [Iter 3/2250] R0[2/150], Temp: 0.9996, Energy: 1.838340-0.000027j
[2025-09-09 10:39:49] [Iter 4/2250] R0[3/150], Temp: 0.9990, Energy: 1.838234-0.000145j
[2025-09-09 10:40:17] [Iter 5/2250] R0[4/150], Temp: 0.9982, Energy: 1.838821+0.000269j
[2025-09-09 10:40:36] [Iter 6/2250] R0[5/150], Temp: 0.9973, Energy: 1.838776-0.000086j
[2025-09-09 10:41:32] [Iter 7/2250] R0[6/150], Temp: 0.9961, Energy: 1.838694+0.000553j
[2025-09-09 10:42:33] [Iter 8/2250] R0[7/150], Temp: 0.9946, Energy: 1.838088+0.000381j
[2025-09-09 10:43:33] [Iter 9/2250] R0[8/150], Temp: 0.9930, Energy: 1.838093+0.000386j
[2025-09-09 10:44:33] [Iter 10/2250] R0[9/150], Temp: 0.9911, Energy: 1.838293+0.000856j
[2025-09-09 10:45:34] [Iter 11/2250] R0[10/150], Temp: 0.9891, Energy: 1.838094+0.000142j
[2025-09-09 10:46:34] [Iter 12/2250] R0[11/150], Temp: 0.9868, Energy: 1.838433+0.000443j
[2025-09-09 10:47:34] [Iter 13/2250] R0[12/150], Temp: 0.9843, Energy: 1.837078-0.000139j
[2025-09-09 10:48:35] [Iter 14/2250] R0[13/150], Temp: 0.9816, Energy: 1.837918-0.000032j
[2025-09-09 10:49:35] [Iter 15/2250] R0[14/150], Temp: 0.9787, Energy: 1.837359-0.000034j
[2025-09-09 10:50:35] [Iter 16/2250] R0[15/150], Temp: 0.9755, Energy: 1.836546-0.000338j
[2025-09-09 10:51:36] [Iter 17/2250] R0[16/150], Temp: 0.9722, Energy: 1.836360-0.000151j
[2025-09-09 10:52:36] [Iter 18/2250] R0[17/150], Temp: 0.9686, Energy: 1.835775-0.001054j
[2025-09-09 10:53:36] [Iter 19/2250] R0[18/150], Temp: 0.9649, Energy: 1.836136+0.000502j
[2025-09-09 10:54:37] [Iter 20/2250] R0[19/150], Temp: 0.9609, Energy: 1.836055-0.000497j
[2025-09-09 10:55:37] [Iter 21/2250] R0[20/150], Temp: 0.9568, Energy: 1.835973+0.000458j
[2025-09-09 10:56:38] [Iter 22/2250] R0[21/150], Temp: 0.9524, Energy: 1.835833+0.000431j
[2025-09-09 10:57:38] [Iter 23/2250] R0[22/150], Temp: 0.9479, Energy: 1.837357+0.001345j
[2025-09-09 10:58:38] [Iter 24/2250] R0[23/150], Temp: 0.9431, Energy: 1.835867+0.000197j
[2025-09-09 10:59:39] [Iter 25/2250] R0[24/150], Temp: 0.9382, Energy: 1.835114+0.000096j
[2025-09-09 11:00:39] [Iter 26/2250] R0[25/150], Temp: 0.9330, Energy: 1.834280-0.000052j
[2025-09-09 11:01:40] [Iter 27/2250] R0[26/150], Temp: 0.9277, Energy: 1.834946+0.000397j
[2025-09-09 11:02:40] [Iter 28/2250] R0[27/150], Temp: 0.9222, Energy: 1.834757+0.000585j
[2025-09-09 11:03:40] [Iter 29/2250] R0[28/150], Temp: 0.9165, Energy: 1.833701+0.000285j
[2025-09-09 11:04:40] [Iter 30/2250] R0[29/150], Temp: 0.9106, Energy: 1.834591+0.000511j
[2025-09-09 11:05:41] [Iter 31/2250] R0[30/150], Temp: 0.9045, Energy: 1.832215+0.000039j
[2025-09-09 11:06:41] [Iter 32/2250] R0[31/150], Temp: 0.8983, Energy: 1.833407+0.001148j
[2025-09-09 11:07:41] [Iter 33/2250] R0[32/150], Temp: 0.8918, Energy: 1.832259+0.001097j
[2025-09-09 11:08:42] [Iter 34/2250] R0[33/150], Temp: 0.8853, Energy: 1.831489+0.000234j
[2025-09-09 11:09:42] [Iter 35/2250] R0[34/150], Temp: 0.8785, Energy: 1.829404-0.000773j
[2025-09-09 11:10:42] [Iter 36/2250] R0[35/150], Temp: 0.8716, Energy: 1.827706-0.001652j
[2025-09-09 11:11:43] [Iter 37/2250] R0[36/150], Temp: 0.8645, Energy: 1.829841+0.001222j
[2025-09-09 11:12:43] [Iter 38/2250] R0[37/150], Temp: 0.8572, Energy: 1.828944+0.000772j
[2025-09-09 11:13:43] [Iter 39/2250] R0[38/150], Temp: 0.8498, Energy: 1.827970+0.000379j
[2025-09-09 11:14:44] [Iter 40/2250] R0[39/150], Temp: 0.8423, Energy: 1.824732-0.000085j
[2025-09-09 11:15:44] [Iter 41/2250] R0[40/150], Temp: 0.8346, Energy: 1.823645-0.000388j
[2025-09-09 11:16:44] [Iter 42/2250] R0[41/150], Temp: 0.8267, Energy: 1.824470+0.001677j
[2025-09-09 11:17:48] [Iter 43/2250] R0[42/150], Temp: 0.8187, Energy: 1.819677-0.001370j
[2025-09-09 11:18:48] [Iter 44/2250] R0[43/150], Temp: 0.8106, Energy: 1.816082-0.003068j
[2025-09-09 11:19:49] [Iter 45/2250] R0[44/150], Temp: 0.8023, Energy: 1.817002-0.000318j
[2025-09-09 11:20:49] [Iter 46/2250] R0[45/150], Temp: 0.7939, Energy: 1.814695+0.000351j
[2025-09-09 11:21:49] [Iter 47/2250] R0[46/150], Temp: 0.7854, Energy: 1.815397+0.001539j
[2025-09-09 11:22:49] [Iter 48/2250] R0[47/150], Temp: 0.7767, Energy: 1.809155-0.000325j
[2025-09-09 11:23:49] [Iter 49/2250] R0[48/150], Temp: 0.7679, Energy: 1.806649-0.001374j
[2025-09-09 11:24:50] [Iter 50/2250] R0[49/150], Temp: 0.7590, Energy: 1.803914-0.000832j
[2025-09-09 11:25:50] [Iter 51/2250] R0[50/150], Temp: 0.7500, Energy: 1.799311-0.001326j
[2025-09-09 11:26:51] [Iter 52/2250] R0[51/150], Temp: 0.7409, Energy: 1.795779-0.000403j
[2025-09-09 11:27:51] [Iter 53/2250] R0[52/150], Temp: 0.7316, Energy: 1.789448-0.003368j
[2025-09-09 11:28:51] [Iter 54/2250] R0[53/150], Temp: 0.7223, Energy: 1.785831-0.001385j
[2025-09-09 11:29:52] [Iter 55/2250] R0[54/150], Temp: 0.7129, Energy: 1.785610+0.003886j
[2025-09-09 11:30:52] [Iter 56/2250] R0[55/150], Temp: 0.7034, Energy: 1.776355+0.002262j
[2025-09-09 11:31:52] [Iter 57/2250] R0[56/150], Temp: 0.6938, Energy: 1.768290+0.001240j
[2025-09-09 11:32:53] [Iter 58/2250] R0[57/150], Temp: 0.6841, Energy: 1.762528+0.002210j
[2025-09-09 11:33:53] [Iter 59/2250] R0[58/150], Temp: 0.6743, Energy: 1.746689-0.005902j
[2025-09-09 11:34:54] [Iter 60/2250] R0[59/150], Temp: 0.6644, Energy: 1.738058-0.000627j
[2025-09-09 11:35:54] [Iter 61/2250] R0[60/150], Temp: 0.6545, Energy: 1.722553-0.005149j
[2025-09-09 11:36:54] [Iter 62/2250] R0[61/150], Temp: 0.6445, Energy: 1.711166-0.001429j
[2025-09-09 11:37:55] [Iter 63/2250] R0[62/150], Temp: 0.6345, Energy: 1.695721+0.002721j
[2025-09-09 11:38:55] [Iter 64/2250] R0[63/150], Temp: 0.6243, Energy: 1.685076+0.005603j
[2025-09-09 11:39:55] [Iter 65/2250] R0[64/150], Temp: 0.6142, Energy: 1.652193-0.004266j
[2025-09-09 11:40:56] [Iter 66/2250] R0[65/150], Temp: 0.6040, Energy: 1.638771+0.000652j
[2025-09-09 11:41:56] [Iter 67/2250] R0[66/150], Temp: 0.5937, Energy: 1.606038+0.004469j
[2025-09-09 11:42:56] [Iter 68/2250] R0[67/150], Temp: 0.5834, Energy: 1.576022+0.007764j
[2025-09-09 11:43:57] [Iter 69/2250] R0[68/150], Temp: 0.5730, Energy: 1.533584+0.004325j
[2025-09-09 11:44:57] [Iter 70/2250] R0[69/150], Temp: 0.5627, Energy: 1.476162-0.011207j
[2025-09-09 11:45:57] [Iter 71/2250] R0[70/150], Temp: 0.5523, Energy: 1.423236-0.007045j
[2025-09-09 11:46:57] [Iter 72/2250] R0[71/150], Temp: 0.5418, Energy: 1.363219-0.005929j
[2025-09-09 11:47:58] [Iter 73/2250] R0[72/150], Temp: 0.5314, Energy: 1.310437+0.016024j
[2025-09-09 11:48:58] [Iter 74/2250] R0[73/150], Temp: 0.5209, Energy: 1.224970+0.010827j
[2025-09-09 11:49:58] [Iter 75/2250] R0[74/150], Temp: 0.5105, Energy: 1.122387+0.015647j
[2025-09-09 11:50:58] [Iter 76/2250] R0[75/150], Temp: 0.5000, Energy: 0.988622+0.009070j
[2025-09-09 11:51:59] [Iter 77/2250] R0[76/150], Temp: 0.4895, Energy: 0.851220+0.007439j
[2025-09-09 11:52:59] [Iter 78/2250] R0[77/150], Temp: 0.4791, Energy: 0.680754-0.010560j
[2025-09-09 11:54:00] [Iter 79/2250] R0[78/150], Temp: 0.4686, Energy: 0.459970-0.003206j
[2025-09-09 11:55:00] [Iter 80/2250] R0[79/150], Temp: 0.4582, Energy: 0.182573+0.014314j
[2025-09-09 11:56:00] [Iter 81/2250] R0[80/150], Temp: 0.4477, Energy: -0.153690-0.008122j
[2025-09-09 11:57:01] [Iter 82/2250] R0[81/150], Temp: 0.4373, Energy: -0.557068-0.041102j
[2025-09-09 11:58:01] [Iter 83/2250] R0[82/150], Temp: 0.4270, Energy: -1.008521-0.021832j
[2025-09-09 11:59:02] [Iter 84/2250] R0[83/150], Temp: 0.4166, Energy: -1.622040-0.006656j
[2025-09-09 12:00:02] [Iter 85/2250] R0[84/150], Temp: 0.4063, Energy: -2.419066+0.011947j
[2025-09-09 12:01:02] [Iter 86/2250] R0[85/150], Temp: 0.3960, Energy: -3.357261+0.030023j
[2025-09-09 12:02:03] [Iter 87/2250] R0[86/150], Temp: 0.3858, Energy: -4.551230+0.013180j
[2025-09-09 12:03:03] [Iter 88/2250] R0[87/150], Temp: 0.3757, Energy: -5.999334+0.019403j
[2025-09-09 12:04:03] [Iter 89/2250] R0[88/150], Temp: 0.3655, Energy: -7.693433-0.063048j
[2025-09-09 12:05:04] [Iter 90/2250] R0[89/150], Temp: 0.3555, Energy: -9.870716+0.072025j
[2025-09-09 12:06:04] [Iter 91/2250] R0[90/150], Temp: 0.3455, Energy: -12.319238-0.053802j
[2025-09-09 12:07:04] [Iter 92/2250] R0[91/150], Temp: 0.3356, Energy: -15.124391-0.018889j
[2025-09-09 12:08:04] [Iter 93/2250] R0[92/150], Temp: 0.3257, Energy: -18.248937+0.001635j
[2025-09-09 12:09:05] [Iter 94/2250] R0[93/150], Temp: 0.3159, Energy: -21.611240+0.019800j
[2025-09-09 12:10:05] [Iter 95/2250] R0[94/150], Temp: 0.3062, Energy: -24.758901+0.018762j
[2025-09-09 12:11:05] [Iter 96/2250] R0[95/150], Temp: 0.2966, Energy: -27.892281+0.035400j
[2025-09-09 12:12:06] [Iter 97/2250] R0[96/150], Temp: 0.2871, Energy: -30.838371-0.013625j
[2025-09-09 12:13:06] [Iter 98/2250] R0[97/150], Temp: 0.2777, Energy: -33.339160+0.027258j
[2025-09-09 12:14:06] [Iter 99/2250] R0[98/150], Temp: 0.2684, Energy: -35.606027+0.048410j
[2025-09-09 12:15:07] [Iter 100/2250] R0[99/150], Temp: 0.2591, Energy: -37.703832-0.005516j
[2025-09-09 12:16:07] [Iter 101/2250] R0[100/150], Temp: 0.2500, Energy: -39.460588+0.013179j
[2025-09-09 12:17:07] [Iter 102/2250] R0[101/150], Temp: 0.2410, Energy: -40.974727+0.017553j
[2025-09-09 12:18:07] [Iter 103/2250] R0[102/150], Temp: 0.2321, Energy: -42.198347+0.012722j
[2025-09-09 12:19:08] [Iter 104/2250] R0[103/150], Temp: 0.2233, Energy: -43.227998+0.002509j
[2025-09-09 12:20:08] [Iter 105/2250] R0[104/150], Temp: 0.2146, Energy: -44.136719-0.018029j
[2025-09-09 12:21:09] [Iter 106/2250] R0[105/150], Temp: 0.2061, Energy: -44.942637-0.009320j
[2025-09-09 12:22:09] [Iter 107/2250] R0[106/150], Temp: 0.1977, Energy: -45.716861-0.001996j
[2025-09-09 12:23:09] [Iter 108/2250] R0[107/150], Temp: 0.1894, Energy: -46.405623-0.001607j
[2025-09-09 12:24:10] [Iter 109/2250] R0[108/150], Temp: 0.1813, Energy: -46.999628+0.005346j
[2025-09-09 12:25:10] [Iter 110/2250] R0[109/150], Temp: 0.1733, Energy: -47.561855-0.005345j
[2025-09-09 12:26:10] [Iter 111/2250] R0[110/150], Temp: 0.1654, Energy: -47.999764-0.009533j
[2025-09-09 12:27:11] [Iter 112/2250] R0[111/150], Temp: 0.1577, Energy: -48.549938+0.002721j
[2025-09-09 12:28:11] [Iter 113/2250] R0[112/150], Temp: 0.1502, Energy: -48.932765+0.004398j
[2025-09-09 12:29:11] [Iter 114/2250] R0[113/150], Temp: 0.1428, Energy: -49.295564-0.016216j
[2025-09-09 12:30:12] [Iter 115/2250] R0[114/150], Temp: 0.1355, Energy: -49.672096-0.010661j
[2025-09-09 12:31:12] [Iter 116/2250] R0[115/150], Temp: 0.1284, Energy: -50.012914-0.007496j
[2025-09-09 12:32:12] [Iter 117/2250] R0[116/150], Temp: 0.1215, Energy: -50.322379-0.009579j
[2025-09-09 12:33:13] [Iter 118/2250] R0[117/150], Temp: 0.1147, Energy: -50.501476+0.003972j
[2025-09-09 12:34:13] [Iter 119/2250] R0[118/150], Temp: 0.1082, Energy: -50.643882-0.004891j
[2025-09-09 12:35:13] [Iter 120/2250] R0[119/150], Temp: 0.1017, Energy: -50.855811+0.003576j
[2025-09-09 12:36:14] [Iter 121/2250] R0[120/150], Temp: 0.0955, Energy: -50.941853+0.001939j
[2025-09-09 12:37:14] [Iter 122/2250] R0[121/150], Temp: 0.0894, Energy: -51.108736-0.003161j
[2025-09-09 12:38:14] [Iter 123/2250] R0[122/150], Temp: 0.0835, Energy: -51.273344-0.004042j
[2025-09-09 12:39:15] [Iter 124/2250] R0[123/150], Temp: 0.0778, Energy: -51.480372+0.003200j
[2025-09-09 12:40:15] [Iter 125/2250] R0[124/150], Temp: 0.0723, Energy: -51.464754-0.005972j
[2025-09-09 12:41:15] [Iter 126/2250] R0[125/150], Temp: 0.0670, Energy: -51.591802-0.001529j
[2025-09-09 12:42:16] [Iter 127/2250] R0[126/150], Temp: 0.0618, Energy: -51.765702-0.001154j
[2025-09-09 12:43:16] [Iter 128/2250] R0[127/150], Temp: 0.0569, Energy: -51.783655-0.011676j
[2025-09-09 12:44:17] [Iter 129/2250] R0[128/150], Temp: 0.0521, Energy: -51.931923+0.001550j
[2025-09-09 12:45:17] [Iter 130/2250] R0[129/150], Temp: 0.0476, Energy: -51.931534-0.002787j
[2025-09-09 12:46:18] [Iter 131/2250] R0[130/150], Temp: 0.0432, Energy: -51.981360-0.009476j
[2025-09-09 12:47:18] [Iter 132/2250] R0[131/150], Temp: 0.0391, Energy: -52.059675-0.001749j
[2025-09-09 12:48:18] [Iter 133/2250] R0[132/150], Temp: 0.0351, Energy: -52.126290-0.013131j
[2025-09-09 12:49:19] [Iter 134/2250] R0[133/150], Temp: 0.0314, Energy: -52.140729-0.001927j
[2025-09-09 12:50:19] [Iter 135/2250] R0[134/150], Temp: 0.0278, Energy: -52.202301-0.006285j
[2025-09-09 12:51:19] [Iter 136/2250] R0[135/150], Temp: 0.0245, Energy: -52.296534-0.002302j
[2025-09-09 12:52:20] [Iter 137/2250] R0[136/150], Temp: 0.0213, Energy: -52.322723+0.009528j
[2025-09-09 12:53:20] [Iter 138/2250] R0[137/150], Temp: 0.0184, Energy: -52.348819-0.002887j
[2025-09-09 12:54:21] [Iter 139/2250] R0[138/150], Temp: 0.0157, Energy: -52.322977-0.003830j
[2025-09-09 12:55:21] [Iter 140/2250] R0[139/150], Temp: 0.0132, Energy: -52.404139-0.008763j
[2025-09-09 12:56:22] [Iter 141/2250] R0[140/150], Temp: 0.0109, Energy: -52.467062-0.003428j
[2025-09-09 12:57:22] [Iter 142/2250] R0[141/150], Temp: 0.0089, Energy: -52.469684+0.007212j
[2025-09-09 12:58:23] [Iter 143/2250] R0[142/150], Temp: 0.0070, Energy: -52.474752+0.000017j
[2025-09-09 12:59:23] [Iter 144/2250] R0[143/150], Temp: 0.0054, Energy: -52.479747-0.003029j
[2025-09-09 13:00:23] [Iter 145/2250] R0[144/150], Temp: 0.0039, Energy: -52.435853-0.004543j
[2025-09-09 13:01:23] [Iter 146/2250] R0[145/150], Temp: 0.0027, Energy: -52.452029-0.002348j
[2025-09-09 13:02:24] [Iter 147/2250] R0[146/150], Temp: 0.0018, Energy: -52.580468-0.000147j
[2025-09-09 13:03:24] [Iter 148/2250] R0[147/150], Temp: 0.0010, Energy: -52.584132+0.009099j
[2025-09-09 13:04:24] [Iter 149/2250] R0[148/150], Temp: 0.0004, Energy: -52.627279-0.006512j
[2025-09-09 13:05:25] [Iter 150/2250] R0[149/150], Temp: 0.0001, Energy: -52.599682-0.008474j
[2025-09-09 13:05:25] RESTART #1 | Period: 300
[2025-09-09 13:06:25] [Iter 151/2250] R1[0/300], Temp: 1.0000, Energy: -52.678972+0.010540j
[2025-09-09 13:07:25] [Iter 152/2250] R1[1/300], Temp: 1.0000, Energy: -52.645358-0.003519j
[2025-09-09 13:08:26] [Iter 153/2250] R1[2/300], Temp: 0.9999, Energy: -52.700260-0.001724j
[2025-09-09 13:09:26] [Iter 154/2250] R1[3/300], Temp: 0.9998, Energy: -52.645474-0.004818j
[2025-09-09 13:10:26] [Iter 155/2250] R1[4/300], Temp: 0.9996, Energy: -52.678872+0.003983j
[2025-09-09 13:11:27] [Iter 156/2250] R1[5/300], Temp: 0.9993, Energy: -52.687814+0.004890j
[2025-09-09 13:12:27] [Iter 157/2250] R1[6/300], Temp: 0.9990, Energy: -52.705784+0.003283j
[2025-09-09 13:13:27] [Iter 158/2250] R1[7/300], Temp: 0.9987, Energy: -52.653983-0.006030j
[2025-09-09 13:14:28] [Iter 159/2250] R1[8/300], Temp: 0.9982, Energy: -52.704036+0.002439j
[2025-09-09 13:15:28] [Iter 160/2250] R1[9/300], Temp: 0.9978, Energy: -52.677664-0.001476j
[2025-09-09 13:16:29] [Iter 161/2250] R1[10/300], Temp: 0.9973, Energy: -52.779644+0.000789j
[2025-09-09 13:17:29] [Iter 162/2250] R1[11/300], Temp: 0.9967, Energy: -52.678665+0.003539j
[2025-09-09 13:18:29] [Iter 163/2250] R1[12/300], Temp: 0.9961, Energy: -52.790607-0.009491j
[2025-09-09 13:19:30] [Iter 164/2250] R1[13/300], Temp: 0.9954, Energy: -52.722758-0.008991j
[2025-09-09 13:20:30] [Iter 165/2250] R1[14/300], Temp: 0.9946, Energy: -52.736216-0.004187j
[2025-09-09 13:21:30] [Iter 166/2250] R1[15/300], Temp: 0.9938, Energy: -52.783307+0.000413j
[2025-09-09 13:22:31] [Iter 167/2250] R1[16/300], Temp: 0.9930, Energy: -52.766593-0.007180j
[2025-09-09 13:23:31] [Iter 168/2250] R1[17/300], Temp: 0.9921, Energy: -52.789174+0.006641j
[2025-09-09 13:24:32] [Iter 169/2250] R1[18/300], Temp: 0.9911, Energy: -52.850594+0.001622j
[2025-09-09 13:25:32] [Iter 170/2250] R1[19/300], Temp: 0.9901, Energy: -52.821316+0.004965j
[2025-09-09 13:26:32] [Iter 171/2250] R1[20/300], Temp: 0.9891, Energy: -52.742881-0.009471j
[2025-09-09 13:27:33] [Iter 172/2250] R1[21/300], Temp: 0.9880, Energy: -52.724970-0.002420j
[2025-09-09 13:28:33] [Iter 173/2250] R1[22/300], Temp: 0.9868, Energy: -52.729370+0.009208j
[2025-09-09 13:29:33] [Iter 174/2250] R1[23/300], Temp: 0.9856, Energy: -52.802378+0.001373j
[2025-09-09 13:30:34] [Iter 175/2250] R1[24/300], Temp: 0.9843, Energy: -52.775316-0.000377j
[2025-09-09 13:31:34] [Iter 176/2250] R1[25/300], Temp: 0.9830, Energy: -52.756955-0.006167j
[2025-09-09 13:32:34] [Iter 177/2250] R1[26/300], Temp: 0.9816, Energy: -52.802629-0.001508j
[2025-09-09 13:33:35] [Iter 178/2250] R1[27/300], Temp: 0.9801, Energy: -52.792356-0.004332j
[2025-09-09 13:34:35] [Iter 179/2250] R1[28/300], Temp: 0.9787, Energy: -52.750079+0.003630j
[2025-09-09 13:35:35] [Iter 180/2250] R1[29/300], Temp: 0.9771, Energy: -52.824258+0.001301j
[2025-09-09 13:36:36] [Iter 181/2250] R1[30/300], Temp: 0.9755, Energy: -52.879539-0.003511j
[2025-09-09 13:37:36] [Iter 182/2250] R1[31/300], Temp: 0.9739, Energy: -52.876418-0.002479j
[2025-09-09 13:38:36] [Iter 183/2250] R1[32/300], Temp: 0.9722, Energy: -52.884276+0.011204j
[2025-09-09 13:39:36] [Iter 184/2250] R1[33/300], Temp: 0.9704, Energy: -52.917211+0.001572j
[2025-09-09 13:40:37] [Iter 185/2250] R1[34/300], Temp: 0.9686, Energy: -52.927738+0.001158j
[2025-09-09 13:41:37] [Iter 186/2250] R1[35/300], Temp: 0.9668, Energy: -52.881809+0.008295j
[2025-09-09 13:42:38] [Iter 187/2250] R1[36/300], Temp: 0.9649, Energy: -52.910890-0.000630j
[2025-09-09 13:43:38] [Iter 188/2250] R1[37/300], Temp: 0.9629, Energy: -52.934630-0.008096j
[2025-09-09 13:44:38] [Iter 189/2250] R1[38/300], Temp: 0.9609, Energy: -52.848026-0.005453j
[2025-09-09 13:45:38] [Iter 190/2250] R1[39/300], Temp: 0.9589, Energy: -52.926034+0.005486j
[2025-09-09 13:46:39] [Iter 191/2250] R1[40/300], Temp: 0.9568, Energy: -52.944942-0.000086j
[2025-09-09 13:47:39] [Iter 192/2250] R1[41/300], Temp: 0.9546, Energy: -52.974137+0.002661j
[2025-09-09 13:48:40] [Iter 193/2250] R1[42/300], Temp: 0.9524, Energy: -52.848441+0.012475j
[2025-09-09 13:49:40] [Iter 194/2250] R1[43/300], Temp: 0.9502, Energy: -52.883393-0.006422j
[2025-09-09 13:50:41] [Iter 195/2250] R1[44/300], Temp: 0.9479, Energy: -52.919797-0.000760j
[2025-09-09 13:51:41] [Iter 196/2250] R1[45/300], Temp: 0.9455, Energy: -52.907546-0.001333j
[2025-09-09 13:52:41] [Iter 197/2250] R1[46/300], Temp: 0.9431, Energy: -52.863821-0.001205j
[2025-09-09 13:53:42] [Iter 198/2250] R1[47/300], Temp: 0.9407, Energy: -52.888590+0.005958j
[2025-09-09 13:54:42] [Iter 199/2250] R1[48/300], Temp: 0.9382, Energy: -52.872096-0.003755j
[2025-09-09 13:55:42] [Iter 200/2250] R1[49/300], Temp: 0.9356, Energy: -52.871930-0.012880j
[2025-09-09 13:56:42] [Iter 201/2250] R1[50/300], Temp: 0.9330, Energy: -52.889552+0.008060j
[2025-09-09 13:57:43] [Iter 202/2250] R1[51/300], Temp: 0.9304, Energy: -52.939955-0.005056j
[2025-09-09 13:58:43] [Iter 203/2250] R1[52/300], Temp: 0.9277, Energy: -52.921246-0.003242j
[2025-09-09 13:59:43] [Iter 204/2250] R1[53/300], Temp: 0.9249, Energy: -52.940873+0.000972j
[2025-09-09 14:00:44] [Iter 205/2250] R1[54/300], Temp: 0.9222, Energy: -52.894648+0.001390j
[2025-09-09 14:01:44] [Iter 206/2250] R1[55/300], Temp: 0.9193, Energy: -52.874564+0.013126j
[2025-09-09 14:02:44] [Iter 207/2250] R1[56/300], Temp: 0.9165, Energy: -52.943353-0.003937j
[2025-09-09 14:03:45] [Iter 208/2250] R1[57/300], Temp: 0.9135, Energy: -52.936683+0.005853j
[2025-09-09 14:04:45] [Iter 209/2250] R1[58/300], Temp: 0.9106, Energy: -52.906964-0.000764j
[2025-09-09 14:05:46] [Iter 210/2250] R1[59/300], Temp: 0.9076, Energy: -52.878447+0.003789j
[2025-09-09 14:06:46] [Iter 211/2250] R1[60/300], Temp: 0.9045, Energy: -52.871805-0.010334j
[2025-09-09 14:07:47] [Iter 212/2250] R1[61/300], Temp: 0.9014, Energy: -52.924865-0.002074j
[2025-09-09 14:08:47] [Iter 213/2250] R1[62/300], Temp: 0.8983, Energy: -52.936133-0.002409j
[2025-09-09 14:09:47] [Iter 214/2250] R1[63/300], Temp: 0.8951, Energy: -52.951490-0.002156j
[2025-09-09 14:10:48] [Iter 215/2250] R1[64/300], Temp: 0.8918, Energy: -53.058756-0.002094j
[2025-09-09 14:11:48] [Iter 216/2250] R1[65/300], Temp: 0.8886, Energy: -53.052551+0.008246j
[2025-09-09 14:12:48] [Iter 217/2250] R1[66/300], Temp: 0.8853, Energy: -53.077588-0.006797j
[2025-09-09 14:13:49] [Iter 218/2250] R1[67/300], Temp: 0.8819, Energy: -52.986302+0.004172j
[2025-09-09 14:14:49] [Iter 219/2250] R1[68/300], Temp: 0.8785, Energy: -53.033913+0.000681j
[2025-09-09 14:15:49] [Iter 220/2250] R1[69/300], Temp: 0.8751, Energy: -52.993740+0.004812j
[2025-09-09 14:16:50] [Iter 221/2250] R1[70/300], Temp: 0.8716, Energy: -53.067910-0.006678j
[2025-09-09 14:17:50] [Iter 222/2250] R1[71/300], Temp: 0.8680, Energy: -53.037412+0.001479j
[2025-09-09 14:18:51] [Iter 223/2250] R1[72/300], Temp: 0.8645, Energy: -53.020339-0.008868j
[2025-09-09 14:19:51] [Iter 224/2250] R1[73/300], Temp: 0.8609, Energy: -52.956227+0.001564j
[2025-09-09 14:20:51] [Iter 225/2250] R1[74/300], Temp: 0.8572, Energy: -53.029932+0.003865j
[2025-09-09 14:21:51] [Iter 226/2250] R1[75/300], Temp: 0.8536, Energy: -53.034541-0.001693j
[2025-09-09 14:22:52] [Iter 227/2250] R1[76/300], Temp: 0.8498, Energy: -53.033331-0.006549j
[2025-09-09 14:23:52] [Iter 228/2250] R1[77/300], Temp: 0.8461, Energy: -53.062175-0.004462j
[2025-09-09 14:24:52] [Iter 229/2250] R1[78/300], Temp: 0.8423, Energy: -53.051647+0.000071j
[2025-09-09 14:25:53] [Iter 230/2250] R1[79/300], Temp: 0.8384, Energy: -53.064634+0.000064j
[2025-09-09 14:26:53] [Iter 231/2250] R1[80/300], Temp: 0.8346, Energy: -52.953789+0.003198j
[2025-09-09 14:27:53] [Iter 232/2250] R1[81/300], Temp: 0.8307, Energy: -52.991054-0.003653j
[2025-09-09 14:28:54] [Iter 233/2250] R1[82/300], Temp: 0.8267, Energy: -53.017492+0.001957j
[2025-09-09 14:29:54] [Iter 234/2250] R1[83/300], Temp: 0.8227, Energy: -53.026052+0.000226j
[2025-09-09 14:30:55] [Iter 235/2250] R1[84/300], Temp: 0.8187, Energy: -53.014586-0.004485j
[2025-09-09 14:31:58] [Iter 236/2250] R1[85/300], Temp: 0.8147, Energy: -53.050507+0.000100j
[2025-09-09 14:32:57] [Iter 237/2250] R1[86/300], Temp: 0.8106, Energy: -52.986062+0.000381j
[2025-09-09 14:33:57] [Iter 238/2250] R1[87/300], Temp: 0.8065, Energy: -52.952047+0.004366j
[2025-09-09 14:34:58] [Iter 239/2250] R1[88/300], Temp: 0.8023, Energy: -52.981520+0.005556j
[2025-09-09 14:35:58] [Iter 240/2250] R1[89/300], Temp: 0.7981, Energy: -53.000468+0.000415j
[2025-09-09 14:36:58] [Iter 241/2250] R1[90/300], Temp: 0.7939, Energy: -53.001244+0.000450j
[2025-09-09 14:37:59] [Iter 242/2250] R1[91/300], Temp: 0.7896, Energy: -52.990120-0.000680j
[2025-09-09 14:38:59] [Iter 243/2250] R1[92/300], Temp: 0.7854, Energy: -53.021207-0.002209j
[2025-09-09 14:39:59] [Iter 244/2250] R1[93/300], Temp: 0.7810, Energy: -52.966122+0.004909j
[2025-09-09 14:41:00] [Iter 245/2250] R1[94/300], Temp: 0.7767, Energy: -52.948495-0.000578j
[2025-09-09 14:42:00] [Iter 246/2250] R1[95/300], Temp: 0.7723, Energy: -52.942391+0.006815j
[2025-09-09 14:43:01] [Iter 247/2250] R1[96/300], Temp: 0.7679, Energy: -52.975193+0.000575j
[2025-09-09 14:44:01] [Iter 248/2250] R1[97/300], Temp: 0.7635, Energy: -52.993700-0.003302j
[2025-09-09 14:45:02] [Iter 249/2250] R1[98/300], Temp: 0.7590, Energy: -53.017972-0.000369j
[2025-09-09 14:46:02] [Iter 250/2250] R1[99/300], Temp: 0.7545, Energy: -52.988058-0.002835j
[2025-09-09 14:46:02] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-09-09 14:47:02] [Iter 251/2250] R1[100/300], Temp: 0.7500, Energy: -53.004398+0.001573j
[2025-09-09 14:48:02] [Iter 252/2250] R1[101/300], Temp: 0.7455, Energy: -52.998808+0.005213j
[2025-09-09 14:49:03] [Iter 253/2250] R1[102/300], Temp: 0.7409, Energy: -53.119713-0.007249j
[2025-09-09 14:50:03] [Iter 254/2250] R1[103/300], Temp: 0.7363, Energy: -53.035210+0.006170j
[2025-09-09 14:51:03] [Iter 255/2250] R1[104/300], Temp: 0.7316, Energy: -53.062977-0.001688j
[2025-09-09 14:52:04] [Iter 256/2250] R1[105/300], Temp: 0.7270, Energy: -53.109695-0.009685j
[2025-09-09 14:53:04] [Iter 257/2250] R1[106/300], Temp: 0.7223, Energy: -53.111483-0.004043j
[2025-09-09 14:54:05] [Iter 258/2250] R1[107/300], Temp: 0.7176, Energy: -53.098806+0.000698j
[2025-09-09 14:55:05] [Iter 259/2250] R1[108/300], Temp: 0.7129, Energy: -53.092970+0.002436j
[2025-09-09 14:56:05] [Iter 260/2250] R1[109/300], Temp: 0.7081, Energy: -53.082785+0.001812j
[2025-09-09 14:57:05] [Iter 261/2250] R1[110/300], Temp: 0.7034, Energy: -53.105936-0.004794j
[2025-09-09 14:58:06] [Iter 262/2250] R1[111/300], Temp: 0.6986, Energy: -53.166705+0.002881j
[2025-09-09 14:59:06] [Iter 263/2250] R1[112/300], Temp: 0.6938, Energy: -53.109276-0.000451j
[2025-09-09 15:00:07] [Iter 264/2250] R1[113/300], Temp: 0.6889, Energy: -53.039655+0.000561j
[2025-09-09 15:01:07] [Iter 265/2250] R1[114/300], Temp: 0.6841, Energy: -53.094519-0.004404j
[2025-09-09 15:02:07] [Iter 266/2250] R1[115/300], Temp: 0.6792, Energy: -53.057396+0.007358j
[2025-09-09 15:03:08] [Iter 267/2250] R1[116/300], Temp: 0.6743, Energy: -53.075052-0.003357j
[2025-09-09 15:04:08] [Iter 268/2250] R1[117/300], Temp: 0.6694, Energy: -53.077510-0.001158j
[2025-09-09 15:05:08] [Iter 269/2250] R1[118/300], Temp: 0.6644, Energy: -53.090421+0.002017j
[2025-09-09 15:06:09] [Iter 270/2250] R1[119/300], Temp: 0.6595, Energy: -53.049147-0.001891j
[2025-09-09 15:07:09] [Iter 271/2250] R1[120/300], Temp: 0.6545, Energy: -53.165638-0.009847j
[2025-09-09 15:08:09] [Iter 272/2250] R1[121/300], Temp: 0.6495, Energy: -53.159650-0.003164j
[2025-09-09 15:09:10] [Iter 273/2250] R1[122/300], Temp: 0.6445, Energy: -53.163223+0.003767j
[2025-09-09 15:10:10] [Iter 274/2250] R1[123/300], Temp: 0.6395, Energy: -53.082215+0.004155j
[2025-09-09 15:11:10] [Iter 275/2250] R1[124/300], Temp: 0.6345, Energy: -53.060065+0.004835j
[2025-09-09 15:12:11] [Iter 276/2250] R1[125/300], Temp: 0.6294, Energy: -53.029502+0.002501j
[2025-09-09 15:13:11] [Iter 277/2250] R1[126/300], Temp: 0.6243, Energy: -53.017932-0.004354j
[2025-09-09 15:14:12] [Iter 278/2250] R1[127/300], Temp: 0.6193, Energy: -53.041778+0.000565j
[2025-09-09 15:15:12] [Iter 279/2250] R1[128/300], Temp: 0.6142, Energy: -53.109881+0.006850j
[2025-09-09 15:16:12] [Iter 280/2250] R1[129/300], Temp: 0.6091, Energy: -53.036948+0.004827j
[2025-09-09 15:17:12] [Iter 281/2250] R1[130/300], Temp: 0.6040, Energy: -53.048655+0.003382j
[2025-09-09 15:18:13] [Iter 282/2250] R1[131/300], Temp: 0.5988, Energy: -52.952512-0.001529j
[2025-09-09 15:19:13] [Iter 283/2250] R1[132/300], Temp: 0.5937, Energy: -53.026830-0.004697j
[2025-09-09 15:20:13] [Iter 284/2250] R1[133/300], Temp: 0.5885, Energy: -53.039568-0.000123j
[2025-09-09 15:21:13] [Iter 285/2250] R1[134/300], Temp: 0.5834, Energy: -53.123397+0.001039j
[2025-09-09 15:22:14] [Iter 286/2250] R1[135/300], Temp: 0.5782, Energy: -53.063611-0.004047j
[2025-09-09 15:23:14] [Iter 287/2250] R1[136/300], Temp: 0.5730, Energy: -53.095990-0.000554j
[2025-09-09 15:24:14] [Iter 288/2250] R1[137/300], Temp: 0.5679, Energy: -53.138690-0.004208j
[2025-09-09 15:25:15] [Iter 289/2250] R1[138/300], Temp: 0.5627, Energy: -53.114434-0.007268j
[2025-09-09 15:26:15] [Iter 290/2250] R1[139/300], Temp: 0.5575, Energy: -53.064377+0.000063j
[2025-09-09 15:27:16] [Iter 291/2250] R1[140/300], Temp: 0.5523, Energy: -53.093751+0.002985j
[2025-09-09 15:28:16] [Iter 292/2250] R1[141/300], Temp: 0.5471, Energy: -53.113702+0.001765j
[2025-09-09 15:29:16] [Iter 293/2250] R1[142/300], Temp: 0.5418, Energy: -53.171221+0.006258j
[2025-09-09 15:30:16] [Iter 294/2250] R1[143/300], Temp: 0.5366, Energy: -53.076508-0.000811j
[2025-09-09 15:31:17] [Iter 295/2250] R1[144/300], Temp: 0.5314, Energy: -53.123522-0.006254j
[2025-09-09 15:32:17] [Iter 296/2250] R1[145/300], Temp: 0.5262, Energy: -53.068961-0.006657j
[2025-09-09 15:33:17] [Iter 297/2250] R1[146/300], Temp: 0.5209, Energy: -53.096321+0.003894j
[2025-09-09 15:34:18] [Iter 298/2250] R1[147/300], Temp: 0.5157, Energy: -53.100526-0.006397j
[2025-09-09 15:35:18] [Iter 299/2250] R1[148/300], Temp: 0.5105, Energy: -53.098968-0.000378j
[2025-09-09 15:36:18] [Iter 300/2250] R1[149/300], Temp: 0.5052, Energy: -53.132841+0.002577j
[2025-09-09 15:37:19] [Iter 301/2250] R1[150/300], Temp: 0.5000, Energy: -53.063040-0.006889j
[2025-09-09 15:38:19] [Iter 302/2250] R1[151/300], Temp: 0.4948, Energy: -53.037387-0.004923j
[2025-09-09 15:39:19] [Iter 303/2250] R1[152/300], Temp: 0.4895, Energy: -53.060519+0.005837j
[2025-09-09 15:40:20] [Iter 304/2250] R1[153/300], Temp: 0.4843, Energy: -53.034164+0.001238j
[2025-09-09 15:41:20] [Iter 305/2250] R1[154/300], Temp: 0.4791, Energy: -53.084293+0.003982j
[2025-09-09 15:42:23] [Iter 306/2250] R1[155/300], Temp: 0.4738, Energy: -53.133023-0.004099j
[2025-09-09 15:43:24] [Iter 307/2250] R1[156/300], Temp: 0.4686, Energy: -53.147951+0.001705j
[2025-09-09 15:44:24] [Iter 308/2250] R1[157/300], Temp: 0.4634, Energy: -53.105129+0.001980j
[2025-09-09 15:45:24] [Iter 309/2250] R1[158/300], Temp: 0.4582, Energy: -53.127751+0.001173j
[2025-09-09 15:46:24] [Iter 310/2250] R1[159/300], Temp: 0.4529, Energy: -53.087217-0.001061j
[2025-09-09 15:47:25] [Iter 311/2250] R1[160/300], Temp: 0.4477, Energy: -53.041428-0.005298j
[2025-09-09 15:48:25] [Iter 312/2250] R1[161/300], Temp: 0.4425, Energy: -53.151696-0.005473j
[2025-09-09 15:49:25] [Iter 313/2250] R1[162/300], Temp: 0.4373, Energy: -53.098461+0.000181j
[2025-09-09 15:50:26] [Iter 314/2250] R1[163/300], Temp: 0.4321, Energy: -53.070088-0.002202j
[2025-09-09 15:51:26] [Iter 315/2250] R1[164/300], Temp: 0.4270, Energy: -53.119933+0.007456j
[2025-09-09 15:52:26] [Iter 316/2250] R1[165/300], Temp: 0.4218, Energy: -53.138021-0.001183j
[2025-09-09 15:53:27] [Iter 317/2250] R1[166/300], Temp: 0.4166, Energy: -53.128984+0.003908j
[2025-09-09 15:54:27] [Iter 318/2250] R1[167/300], Temp: 0.4115, Energy: -53.142283+0.000240j
[2025-09-09 15:55:27] [Iter 319/2250] R1[168/300], Temp: 0.4063, Energy: -53.112132+0.002177j
[2025-09-09 15:56:28] [Iter 320/2250] R1[169/300], Temp: 0.4012, Energy: -53.136939-0.003906j
[2025-09-09 15:57:28] [Iter 321/2250] R1[170/300], Temp: 0.3960, Energy: -53.131120-0.001151j
[2025-09-09 15:58:28] [Iter 322/2250] R1[171/300], Temp: 0.3909, Energy: -53.141758-0.007280j
[2025-09-09 15:59:29] [Iter 323/2250] R1[172/300], Temp: 0.3858, Energy: -53.063994-0.000872j
[2025-09-09 16:00:29] [Iter 324/2250] R1[173/300], Temp: 0.3807, Energy: -53.114934+0.002317j
[2025-09-09 16:01:29] [Iter 325/2250] R1[174/300], Temp: 0.3757, Energy: -53.062706+0.005368j
[2025-09-09 16:02:30] [Iter 326/2250] R1[175/300], Temp: 0.3706, Energy: -53.088312+0.000814j
[2025-09-09 16:03:30] [Iter 327/2250] R1[176/300], Temp: 0.3655, Energy: -53.128984+0.001269j
[2025-09-09 16:04:30] [Iter 328/2250] R1[177/300], Temp: 0.3605, Energy: -53.110679+0.003430j
[2025-09-09 16:05:31] [Iter 329/2250] R1[178/300], Temp: 0.3555, Energy: -53.111257-0.000797j
[2025-09-09 16:06:31] [Iter 330/2250] R1[179/300], Temp: 0.3505, Energy: -53.119546-0.003484j
[2025-09-09 16:07:31] [Iter 331/2250] R1[180/300], Temp: 0.3455, Energy: -53.128175-0.001452j
[2025-09-09 16:08:32] [Iter 332/2250] R1[181/300], Temp: 0.3405, Energy: -53.193238+0.006254j
[2025-09-09 16:09:32] [Iter 333/2250] R1[182/300], Temp: 0.3356, Energy: -53.139363+0.005094j
[2025-09-09 16:10:32] [Iter 334/2250] R1[183/300], Temp: 0.3306, Energy: -53.065516+0.002423j
[2025-09-09 16:11:33] [Iter 335/2250] R1[184/300], Temp: 0.3257, Energy: -53.095910-0.000280j
[2025-09-09 16:12:33] [Iter 336/2250] R1[185/300], Temp: 0.3208, Energy: -53.101225+0.002668j
[2025-09-09 16:13:33] [Iter 337/2250] R1[186/300], Temp: 0.3159, Energy: -53.105453-0.001287j
[2025-09-09 16:14:34] [Iter 338/2250] R1[187/300], Temp: 0.3111, Energy: -53.125325+0.006122j
[2025-09-09 16:15:34] [Iter 339/2250] R1[188/300], Temp: 0.3062, Energy: -53.163964+0.003444j
[2025-09-09 16:16:34] [Iter 340/2250] R1[189/300], Temp: 0.3014, Energy: -53.170126+0.001798j
[2025-09-09 16:17:35] [Iter 341/2250] R1[190/300], Temp: 0.2966, Energy: -53.112001-0.004284j
[2025-09-09 16:18:35] [Iter 342/2250] R1[191/300], Temp: 0.2919, Energy: -53.104058+0.000356j
[2025-09-09 16:19:35] [Iter 343/2250] R1[192/300], Temp: 0.2871, Energy: -53.138515-0.000961j
[2025-09-09 16:20:35] [Iter 344/2250] R1[193/300], Temp: 0.2824, Energy: -53.151075-0.002048j
[2025-09-09 16:21:36] [Iter 345/2250] R1[194/300], Temp: 0.2777, Energy: -53.133577+0.002339j
[2025-09-09 16:22:36] [Iter 346/2250] R1[195/300], Temp: 0.2730, Energy: -53.236286-0.001694j
[2025-09-09 16:23:37] [Iter 347/2250] R1[196/300], Temp: 0.2684, Energy: -53.221154+0.002717j
[2025-09-09 16:24:37] [Iter 348/2250] R1[197/300], Temp: 0.2637, Energy: -53.139448-0.004359j
[2025-09-09 16:25:38] [Iter 349/2250] R1[198/300], Temp: 0.2591, Energy: -53.199336+0.008496j
[2025-09-09 16:26:38] [Iter 350/2250] R1[199/300], Temp: 0.2545, Energy: -53.178935+0.002753j
[2025-09-09 16:27:38] [Iter 351/2250] R1[200/300], Temp: 0.2500, Energy: -53.142462-0.004413j
[2025-09-09 16:28:39] [Iter 352/2250] R1[201/300], Temp: 0.2455, Energy: -53.177521-0.004481j
[2025-09-09 16:29:39] [Iter 353/2250] R1[202/300], Temp: 0.2410, Energy: -53.155997-0.005014j
[2025-09-09 16:30:40] [Iter 354/2250] R1[203/300], Temp: 0.2365, Energy: -53.191882-0.000232j
[2025-09-09 16:31:40] [Iter 355/2250] R1[204/300], Temp: 0.2321, Energy: -53.219111-0.003603j
[2025-09-09 16:32:40] [Iter 356/2250] R1[205/300], Temp: 0.2277, Energy: -53.211296+0.003386j
[2025-09-09 16:33:41] [Iter 357/2250] R1[206/300], Temp: 0.2233, Energy: -53.202276+0.000061j
[2025-09-09 16:34:41] [Iter 358/2250] R1[207/300], Temp: 0.2190, Energy: -53.167286+0.002291j
[2025-09-09 16:35:41] [Iter 359/2250] R1[208/300], Temp: 0.2146, Energy: -53.176196-0.000117j
[2025-09-09 16:36:42] [Iter 360/2250] R1[209/300], Temp: 0.2104, Energy: -53.167099-0.006992j
[2025-09-09 16:37:42] [Iter 361/2250] R1[210/300], Temp: 0.2061, Energy: -53.130611+0.001284j
[2025-09-09 16:38:42] [Iter 362/2250] R1[211/300], Temp: 0.2019, Energy: -53.195458+0.002386j
[2025-09-09 16:39:42] [Iter 363/2250] R1[212/300], Temp: 0.1977, Energy: -53.209933+0.004360j
[2025-09-09 16:40:43] [Iter 364/2250] R1[213/300], Temp: 0.1935, Energy: -53.148270+0.000355j
[2025-09-09 16:41:43] [Iter 365/2250] R1[214/300], Temp: 0.1894, Energy: -53.155081-0.002912j
[2025-09-09 16:42:43] [Iter 366/2250] R1[215/300], Temp: 0.1853, Energy: -53.168834+0.001230j
[2025-09-09 16:43:44] [Iter 367/2250] R1[216/300], Temp: 0.1813, Energy: -53.072772+0.002177j
[2025-09-09 16:44:44] [Iter 368/2250] R1[217/300], Temp: 0.1773, Energy: -53.146991+0.007388j
[2025-09-09 16:45:44] [Iter 369/2250] R1[218/300], Temp: 0.1733, Energy: -53.153573-0.004725j
[2025-09-09 16:46:45] [Iter 370/2250] R1[219/300], Temp: 0.1693, Energy: -53.181990-0.001538j
[2025-09-09 16:47:45] [Iter 371/2250] R1[220/300], Temp: 0.1654, Energy: -53.154543+0.002704j
[2025-09-09 16:48:45] [Iter 372/2250] R1[221/300], Temp: 0.1616, Energy: -53.201398+0.001802j
[2025-09-09 16:49:46] [Iter 373/2250] R1[222/300], Temp: 0.1577, Energy: -53.237773-0.001701j
[2025-09-09 16:50:46] [Iter 374/2250] R1[223/300], Temp: 0.1539, Energy: -53.168241-0.004425j
[2025-09-09 16:51:47] [Iter 375/2250] R1[224/300], Temp: 0.1502, Energy: -53.173717-0.001310j
[2025-09-09 16:52:47] [Iter 376/2250] R1[225/300], Temp: 0.1464, Energy: -53.164603+0.001322j
[2025-09-09 16:53:48] [Iter 377/2250] R1[226/300], Temp: 0.1428, Energy: -53.153022+0.004680j
[2025-09-09 16:54:48] [Iter 378/2250] R1[227/300], Temp: 0.1391, Energy: -53.103757+0.000262j
[2025-09-09 16:55:48] [Iter 379/2250] R1[228/300], Temp: 0.1355, Energy: -53.136976-0.004970j
[2025-09-09 16:56:49] [Iter 380/2250] R1[229/300], Temp: 0.1320, Energy: -53.186859-0.000168j
[2025-09-09 16:57:49] [Iter 381/2250] R1[230/300], Temp: 0.1284, Energy: -53.176408-0.001586j
[2025-09-09 16:58:49] [Iter 382/2250] R1[231/300], Temp: 0.1249, Energy: -53.176070+0.001324j
[2025-09-09 16:59:50] [Iter 383/2250] R1[232/300], Temp: 0.1215, Energy: -53.185053+0.002747j
[2025-09-09 17:00:50] [Iter 384/2250] R1[233/300], Temp: 0.1181, Energy: -53.190867+0.003921j
[2025-09-09 17:01:50] [Iter 385/2250] R1[234/300], Temp: 0.1147, Energy: -53.163558-0.000256j
[2025-09-09 17:02:51] [Iter 386/2250] R1[235/300], Temp: 0.1114, Energy: -53.221367-0.004556j
[2025-09-09 17:03:51] [Iter 387/2250] R1[236/300], Temp: 0.1082, Energy: -53.174393-0.002611j
[2025-09-09 17:04:51] [Iter 388/2250] R1[237/300], Temp: 0.1049, Energy: -53.190276-0.000655j
[2025-09-09 17:05:51] [Iter 389/2250] R1[238/300], Temp: 0.1017, Energy: -53.128081-0.005398j
[2025-09-09 17:06:52] [Iter 390/2250] R1[239/300], Temp: 0.0986, Energy: -53.213776-0.002616j
[2025-09-09 17:07:52] [Iter 391/2250] R1[240/300], Temp: 0.0955, Energy: -53.201708+0.003460j
[2025-09-09 17:08:52] [Iter 392/2250] R1[241/300], Temp: 0.0924, Energy: -53.173877-0.001329j
[2025-09-09 17:09:53] [Iter 393/2250] R1[242/300], Temp: 0.0894, Energy: -53.168212-0.003096j
[2025-09-09 17:10:53] [Iter 394/2250] R1[243/300], Temp: 0.0865, Energy: -53.204674+0.008389j
[2025-09-09 17:11:54] [Iter 395/2250] R1[244/300], Temp: 0.0835, Energy: -53.188580-0.001229j
[2025-09-09 17:12:54] [Iter 396/2250] R1[245/300], Temp: 0.0807, Energy: -53.178440+0.001474j
[2025-09-09 17:13:54] [Iter 397/2250] R1[246/300], Temp: 0.0778, Energy: -53.204923+0.002647j
[2025-09-09 17:14:55] [Iter 398/2250] R1[247/300], Temp: 0.0751, Energy: -53.232733-0.001219j
[2025-09-09 17:15:55] [Iter 399/2250] R1[248/300], Temp: 0.0723, Energy: -53.126714+0.004786j
[2025-09-09 17:16:56] [Iter 400/2250] R1[249/300], Temp: 0.0696, Energy: -53.168405+0.003392j
[2025-09-09 17:17:56] [Iter 401/2250] R1[250/300], Temp: 0.0670, Energy: -53.246324+0.006794j
[2025-09-09 17:18:57] [Iter 402/2250] R1[251/300], Temp: 0.0644, Energy: -53.204369-0.001213j
[2025-09-09 17:19:57] [Iter 403/2250] R1[252/300], Temp: 0.0618, Energy: -53.200056-0.004468j
[2025-09-09 17:20:57] [Iter 404/2250] R1[253/300], Temp: 0.0593, Energy: -53.187152-0.001435j
[2025-09-09 17:21:58] [Iter 405/2250] R1[254/300], Temp: 0.0569, Energy: -53.267848+0.000441j
[2025-09-09 17:22:58] [Iter 406/2250] R1[255/300], Temp: 0.0545, Energy: -53.242734+0.001944j
[2025-09-09 17:23:58] [Iter 407/2250] R1[256/300], Temp: 0.0521, Energy: -53.219777-0.004547j
[2025-09-09 17:24:58] [Iter 408/2250] R1[257/300], Temp: 0.0498, Energy: -53.200642-0.003269j
[2025-09-09 17:25:58] [Iter 409/2250] R1[258/300], Temp: 0.0476, Energy: -53.190902+0.002249j
[2025-09-09 17:26:59] [Iter 410/2250] R1[259/300], Temp: 0.0454, Energy: -53.127421+0.002528j
[2025-09-09 17:27:59] [Iter 411/2250] R1[260/300], Temp: 0.0432, Energy: -53.139951-0.004082j
[2025-09-09 17:28:59] [Iter 412/2250] R1[261/300], Temp: 0.0411, Energy: -53.197352-0.000731j
[2025-09-09 17:30:00] [Iter 413/2250] R1[262/300], Temp: 0.0391, Energy: -53.180808-0.002462j
[2025-09-09 17:31:00] [Iter 414/2250] R1[263/300], Temp: 0.0371, Energy: -53.200374+0.004229j
[2025-09-09 17:32:00] [Iter 415/2250] R1[264/300], Temp: 0.0351, Energy: -53.222053+0.005944j
[2025-09-09 17:33:00] [Iter 416/2250] R1[265/300], Temp: 0.0332, Energy: -53.186205+0.001342j
[2025-09-09 17:34:01] [Iter 417/2250] R1[266/300], Temp: 0.0314, Energy: -53.097681-0.002059j
[2025-09-09 17:34:58] [Iter 418/2250] R1[267/300], Temp: 0.0296, Energy: -53.136828+0.002113j
[2025-09-09 17:35:57] [Iter 419/2250] R1[268/300], Temp: 0.0278, Energy: -53.100145-0.006819j
[2025-09-09 17:36:57] [Iter 420/2250] R1[269/300], Temp: 0.0261, Energy: -53.136727-0.001785j
[2025-09-09 17:37:58] [Iter 421/2250] R1[270/300], Temp: 0.0245, Energy: -53.135318+0.002880j
[2025-09-09 17:38:58] [Iter 422/2250] R1[271/300], Temp: 0.0229, Energy: -53.098459+0.001823j
[2025-09-09 17:39:58] [Iter 423/2250] R1[272/300], Temp: 0.0213, Energy: -53.218171-0.000399j
[2025-09-09 17:40:59] [Iter 424/2250] R1[273/300], Temp: 0.0199, Energy: -53.111332+0.000434j
[2025-09-09 17:41:59] [Iter 425/2250] R1[274/300], Temp: 0.0184, Energy: -53.119271-0.001088j
[2025-09-09 17:43:00] [Iter 426/2250] R1[275/300], Temp: 0.0170, Energy: -53.191679-0.004457j
[2025-09-09 17:44:00] [Iter 427/2250] R1[276/300], Temp: 0.0157, Energy: -53.153328+0.003093j
[2025-09-09 17:45:00] [Iter 428/2250] R1[277/300], Temp: 0.0144, Energy: -53.232680+0.004079j
[2025-09-09 17:46:00] [Iter 429/2250] R1[278/300], Temp: 0.0132, Energy: -53.223253+0.003835j
[2025-09-09 17:47:01] [Iter 430/2250] R1[279/300], Temp: 0.0120, Energy: -53.193672+0.000179j
[2025-09-09 17:48:01] [Iter 431/2250] R1[280/300], Temp: 0.0109, Energy: -53.155706+0.002753j
[2025-09-09 17:49:02] [Iter 432/2250] R1[281/300], Temp: 0.0099, Energy: -53.174847+0.001009j
[2025-09-09 17:50:02] [Iter 433/2250] R1[282/300], Temp: 0.0089, Energy: -53.176197-0.001314j
[2025-09-09 17:51:02] [Iter 434/2250] R1[283/300], Temp: 0.0079, Energy: -53.187957+0.000329j
[2025-09-09 17:52:02] [Iter 435/2250] R1[284/300], Temp: 0.0070, Energy: -53.214516+0.000000j
[2025-09-09 17:53:03] [Iter 436/2250] R1[285/300], Temp: 0.0062, Energy: -53.204948-0.000680j
[2025-09-09 17:54:03] [Iter 437/2250] R1[286/300], Temp: 0.0054, Energy: -53.236099-0.000549j
[2025-09-09 17:55:03] [Iter 438/2250] R1[287/300], Temp: 0.0046, Energy: -53.175775-0.001483j
[2025-09-09 17:56:04] [Iter 439/2250] R1[288/300], Temp: 0.0039, Energy: -53.226369-0.001938j
[2025-09-09 17:57:04] [Iter 440/2250] R1[289/300], Temp: 0.0033, Energy: -53.204251+0.005329j
[2025-09-09 17:58:05] [Iter 441/2250] R1[290/300], Temp: 0.0027, Energy: -53.171334-0.000547j
[2025-09-09 17:59:05] [Iter 442/2250] R1[291/300], Temp: 0.0022, Energy: -53.184641-0.001305j
[2025-09-09 18:00:06] [Iter 443/2250] R1[292/300], Temp: 0.0018, Energy: -53.274593-0.003851j
[2025-09-09 18:01:06] [Iter 444/2250] R1[293/300], Temp: 0.0013, Energy: -53.233742+0.000209j
[2025-09-09 18:02:06] [Iter 445/2250] R1[294/300], Temp: 0.0010, Energy: -53.256977-0.000477j
[2025-09-09 18:03:07] [Iter 446/2250] R1[295/300], Temp: 0.0007, Energy: -53.212547+0.003671j
[2025-09-09 18:04:07] [Iter 447/2250] R1[296/300], Temp: 0.0004, Energy: -53.181607-0.000600j
[2025-09-09 18:05:07] [Iter 448/2250] R1[297/300], Temp: 0.0002, Energy: -53.185704+0.000317j
[2025-09-09 18:06:07] [Iter 449/2250] R1[298/300], Temp: 0.0001, Energy: -53.136704+0.001854j
[2025-09-09 18:07:08] [Iter 450/2250] R1[299/300], Temp: 0.0000, Energy: -53.178732+0.003479j
[2025-09-09 18:07:08] RESTART #2 | Period: 600
[2025-09-09 18:08:08] [Iter 451/2250] R2[0/600], Temp: 1.0000, Energy: -53.141409+0.001449j
[2025-09-09 18:09:09] [Iter 452/2250] R2[1/600], Temp: 1.0000, Energy: -53.160858+0.003286j
[2025-09-09 18:10:09] [Iter 453/2250] R2[2/600], Temp: 1.0000, Energy: -53.192346+0.000237j
[2025-09-09 18:11:09] [Iter 454/2250] R2[3/600], Temp: 0.9999, Energy: -53.206531-0.001147j
[2025-09-09 18:12:10] [Iter 455/2250] R2[4/600], Temp: 0.9999, Energy: -53.239808+0.002100j
[2025-09-09 18:13:10] [Iter 456/2250] R2[5/600], Temp: 0.9998, Energy: -53.201011+0.001440j
[2025-09-09 18:14:10] [Iter 457/2250] R2[6/600], Temp: 0.9998, Energy: -53.255736-0.003352j
[2025-09-09 18:15:11] [Iter 458/2250] R2[7/600], Temp: 0.9997, Energy: -53.160598-0.000421j
[2025-09-09 18:16:11] [Iter 459/2250] R2[8/600], Temp: 0.9996, Energy: -53.212778+0.000944j
[2025-09-09 18:17:12] [Iter 460/2250] R2[9/600], Temp: 0.9994, Energy: -53.154114+0.003158j
[2025-09-09 18:18:12] [Iter 461/2250] R2[10/600], Temp: 0.9993, Energy: -53.131082+0.003084j
[2025-09-09 18:19:12] [Iter 462/2250] R2[11/600], Temp: 0.9992, Energy: -53.183195-0.001097j
[2025-09-09 18:20:12] [Iter 463/2250] R2[12/600], Temp: 0.9990, Energy: -53.236094-0.004721j
[2025-09-09 18:21:13] [Iter 464/2250] R2[13/600], Temp: 0.9988, Energy: -53.230205-0.002888j
[2025-09-09 18:22:13] [Iter 465/2250] R2[14/600], Temp: 0.9987, Energy: -53.270001+0.002710j
[2025-09-09 18:23:13] [Iter 466/2250] R2[15/600], Temp: 0.9985, Energy: -53.239637+0.002727j
[2025-09-09 18:24:12] [Iter 467/2250] R2[16/600], Temp: 0.9982, Energy: -53.190894-0.003609j
[2025-09-09 18:25:13] [Iter 468/2250] R2[17/600], Temp: 0.9980, Energy: -53.187544-0.001087j
[2025-09-09 18:26:13] [Iter 469/2250] R2[18/600], Temp: 0.9978, Energy: -53.114148+0.005417j
[2025-09-09 18:27:14] [Iter 470/2250] R2[19/600], Temp: 0.9975, Energy: -53.186319-0.000758j
[2025-09-09 18:28:14] [Iter 471/2250] R2[20/600], Temp: 0.9973, Energy: -53.123038+0.005469j
[2025-09-09 18:29:14] [Iter 472/2250] R2[21/600], Temp: 0.9970, Energy: -53.144256+0.000487j
[2025-09-09 18:30:15] [Iter 473/2250] R2[22/600], Temp: 0.9967, Energy: -53.100621-0.002358j
[2025-09-09 18:31:15] [Iter 474/2250] R2[23/600], Temp: 0.9964, Energy: -53.134039-0.002727j
[2025-09-09 18:32:15] [Iter 475/2250] R2[24/600], Temp: 0.9961, Energy: -53.148919+0.002587j
[2025-09-09 18:33:15] [Iter 476/2250] R2[25/600], Temp: 0.9957, Energy: -53.174075-0.001019j
[2025-09-09 18:34:15] [Iter 477/2250] R2[26/600], Temp: 0.9954, Energy: -53.117510+0.001416j
[2025-09-09 18:35:16] [Iter 478/2250] R2[27/600], Temp: 0.9950, Energy: -53.152397+0.000038j
[2025-09-09 18:36:16] [Iter 479/2250] R2[28/600], Temp: 0.9946, Energy: -53.115461+0.000892j
[2025-09-09 18:37:16] [Iter 480/2250] R2[29/600], Temp: 0.9942, Energy: -53.162667+0.001869j
[2025-09-09 18:38:17] [Iter 481/2250] R2[30/600], Temp: 0.9938, Energy: -53.139569+0.000121j
[2025-09-09 18:39:17] [Iter 482/2250] R2[31/600], Temp: 0.9934, Energy: -53.174385+0.002326j
[2025-09-09 18:40:17] [Iter 483/2250] R2[32/600], Temp: 0.9930, Energy: -53.198144+0.000139j
[2025-09-09 18:41:18] [Iter 484/2250] R2[33/600], Temp: 0.9926, Energy: -53.170192-0.000946j
[2025-09-09 18:42:18] [Iter 485/2250] R2[34/600], Temp: 0.9921, Energy: -53.204249+0.000051j
[2025-09-09 18:43:18] [Iter 486/2250] R2[35/600], Temp: 0.9916, Energy: -53.223842+0.001185j
[2025-09-09 18:44:19] [Iter 487/2250] R2[36/600], Temp: 0.9911, Energy: -53.169745+0.000209j
[2025-09-09 18:45:19] [Iter 488/2250] R2[37/600], Temp: 0.9906, Energy: -53.183275+0.002134j
[2025-09-09 18:46:20] [Iter 489/2250] R2[38/600], Temp: 0.9901, Energy: -53.170326-0.002775j
[2025-09-09 18:47:20] [Iter 490/2250] R2[39/600], Temp: 0.9896, Energy: -53.235062+0.001068j
[2025-09-09 18:48:20] [Iter 491/2250] R2[40/600], Temp: 0.9891, Energy: -53.152492-0.004353j
[2025-09-09 18:49:21] [Iter 492/2250] R2[41/600], Temp: 0.9885, Energy: -53.167144+0.001825j
[2025-09-09 18:50:21] [Iter 493/2250] R2[42/600], Temp: 0.9880, Energy: -53.220662+0.000562j
[2025-09-09 18:51:22] [Iter 494/2250] R2[43/600], Temp: 0.9874, Energy: -53.244926+0.001791j
[2025-09-09 18:52:22] [Iter 495/2250] R2[44/600], Temp: 0.9868, Energy: -53.227266-0.004048j
[2025-09-09 18:53:22] [Iter 496/2250] R2[45/600], Temp: 0.9862, Energy: -53.145681+0.000246j
[2025-09-09 18:54:23] [Iter 497/2250] R2[46/600], Temp: 0.9856, Energy: -53.146450-0.003459j
[2025-09-09 18:55:23] [Iter 498/2250] R2[47/600], Temp: 0.9849, Energy: -53.097451-0.002321j
[2025-09-09 18:56:23] [Iter 499/2250] R2[48/600], Temp: 0.9843, Energy: -53.207474-0.000122j
[2025-09-09 18:57:24] [Iter 500/2250] R2[49/600], Temp: 0.9836, Energy: -53.257354+0.002791j
[2025-09-09 18:57:24] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-09-09 18:58:24] [Iter 501/2250] R2[50/600], Temp: 0.9830, Energy: -53.252660+0.001082j
[2025-09-09 18:59:24] [Iter 502/2250] R2[51/600], Temp: 0.9823, Energy: -53.244611-0.004158j
[2025-09-09 19:00:24] [Iter 503/2250] R2[52/600], Temp: 0.9816, Energy: -53.200516+0.002094j
[2025-09-09 19:01:25] [Iter 504/2250] R2[53/600], Temp: 0.9809, Energy: -53.163880+0.000323j
[2025-09-09 19:02:25] [Iter 505/2250] R2[54/600], Temp: 0.9801, Energy: -53.142062-0.000715j
[2025-09-09 19:03:26] [Iter 506/2250] R2[55/600], Temp: 0.9794, Energy: -53.169399+0.005769j
[2025-09-09 19:04:26] [Iter 507/2250] R2[56/600], Temp: 0.9787, Energy: -53.193800+0.002882j
[2025-09-09 19:05:26] [Iter 508/2250] R2[57/600], Temp: 0.9779, Energy: -53.235371-0.000323j
[2025-09-09 19:06:27] [Iter 509/2250] R2[58/600], Temp: 0.9771, Energy: -53.149659-0.000916j
[2025-09-09 19:07:27] [Iter 510/2250] R2[59/600], Temp: 0.9763, Energy: -53.217522+0.001689j
[2025-09-09 19:08:28] [Iter 511/2250] R2[60/600], Temp: 0.9755, Energy: -53.179516+0.002557j
[2025-09-09 19:09:28] [Iter 512/2250] R2[61/600], Temp: 0.9747, Energy: -53.190082-0.004716j
[2025-09-09 19:10:28] [Iter 513/2250] R2[62/600], Temp: 0.9739, Energy: -53.244978+0.002171j
[2025-09-09 19:11:29] [Iter 514/2250] R2[63/600], Temp: 0.9730, Energy: -53.251347-0.002979j
[2025-09-09 19:12:29] [Iter 515/2250] R2[64/600], Temp: 0.9722, Energy: -53.229603-0.000229j
[2025-09-09 19:13:29] [Iter 516/2250] R2[65/600], Temp: 0.9713, Energy: -53.224026-0.001228j
[2025-09-09 19:14:29] [Iter 517/2250] R2[66/600], Temp: 0.9704, Energy: -53.210058+0.005945j
[2025-09-09 19:15:30] [Iter 518/2250] R2[67/600], Temp: 0.9695, Energy: -53.208595-0.002507j
[2025-09-09 19:16:30] [Iter 519/2250] R2[68/600], Temp: 0.9686, Energy: -53.247001-0.001236j
[2025-09-09 19:17:30] [Iter 520/2250] R2[69/600], Temp: 0.9677, Energy: -53.208396+0.001748j
[2025-09-09 19:18:31] [Iter 521/2250] R2[70/600], Temp: 0.9668, Energy: -53.182645+0.002673j
[2025-09-09 19:19:31] [Iter 522/2250] R2[71/600], Temp: 0.9658, Energy: -53.186695+0.000609j
[2025-09-09 19:20:31] [Iter 523/2250] R2[72/600], Temp: 0.9649, Energy: -53.173645+0.000960j
[2025-09-09 19:21:32] [Iter 524/2250] R2[73/600], Temp: 0.9639, Energy: -53.176661+0.000822j
[2025-09-09 19:22:32] [Iter 525/2250] R2[74/600], Temp: 0.9629, Energy: -53.169698-0.001805j
[2025-09-09 19:23:32] [Iter 526/2250] R2[75/600], Temp: 0.9619, Energy: -53.190991-0.003939j
[2025-09-09 19:24:33] [Iter 527/2250] R2[76/600], Temp: 0.9609, Energy: -53.285653+0.004234j
[2025-09-09 19:25:33] [Iter 528/2250] R2[77/600], Temp: 0.9599, Energy: -53.333763-0.001303j
[2025-09-09 19:26:33] [Iter 529/2250] R2[78/600], Temp: 0.9589, Energy: -53.315521+0.004973j
[2025-09-09 19:27:34] [Iter 530/2250] R2[79/600], Temp: 0.9578, Energy: -53.270993+0.004559j
[2025-09-09 19:28:34] [Iter 531/2250] R2[80/600], Temp: 0.9568, Energy: -53.177592-0.001291j
[2025-09-09 19:29:34] [Iter 532/2250] R2[81/600], Temp: 0.9557, Energy: -53.173368-0.003732j
[2025-09-09 19:30:35] [Iter 533/2250] R2[82/600], Temp: 0.9546, Energy: -53.224780+0.001347j
[2025-09-09 19:31:35] [Iter 534/2250] R2[83/600], Temp: 0.9535, Energy: -53.186197+0.000421j
[2025-09-09 19:32:35] [Iter 535/2250] R2[84/600], Temp: 0.9524, Energy: -53.215457+0.002370j
[2025-09-09 19:33:36] [Iter 536/2250] R2[85/600], Temp: 0.9513, Energy: -53.284597-0.001300j
[2025-09-09 19:34:36] [Iter 537/2250] R2[86/600], Temp: 0.9502, Energy: -53.220464-0.007423j
[2025-09-09 19:35:36] [Iter 538/2250] R2[87/600], Temp: 0.9490, Energy: -53.239105+0.001888j
[2025-09-09 19:36:36] [Iter 539/2250] R2[88/600], Temp: 0.9479, Energy: -53.209427-0.004231j
[2025-09-09 19:37:37] [Iter 540/2250] R2[89/600], Temp: 0.9467, Energy: -53.226150+0.002960j
[2025-09-09 19:38:37] [Iter 541/2250] R2[90/600], Temp: 0.9455, Energy: -53.166574+0.000061j
[2025-09-09 19:39:37] [Iter 542/2250] R2[91/600], Temp: 0.9443, Energy: -53.209597+0.001157j
[2025-09-09 19:40:38] [Iter 543/2250] R2[92/600], Temp: 0.9431, Energy: -53.199390-0.002734j
[2025-09-09 19:41:38] [Iter 544/2250] R2[93/600], Temp: 0.9419, Energy: -53.206629-0.000268j
[2025-09-09 19:42:38] [Iter 545/2250] R2[94/600], Temp: 0.9407, Energy: -53.207729+0.003299j
[2025-09-09 19:43:39] [Iter 546/2250] R2[95/600], Temp: 0.9394, Energy: -53.253671-0.000330j
[2025-09-09 19:44:39] [Iter 547/2250] R2[96/600], Temp: 0.9382, Energy: -53.218324+0.002261j
[2025-09-09 19:45:39] [Iter 548/2250] R2[97/600], Temp: 0.9369, Energy: -53.274337-0.000679j
[2025-09-09 19:46:40] [Iter 549/2250] R2[98/600], Temp: 0.9356, Energy: -53.277141-0.001445j
[2025-09-09 19:47:40] [Iter 550/2250] R2[99/600], Temp: 0.9343, Energy: -53.227923+0.000719j
[2025-09-09 19:48:40] [Iter 551/2250] R2[100/600], Temp: 0.9330, Energy: -53.277207+0.000678j
[2025-09-09 19:49:40] [Iter 552/2250] R2[101/600], Temp: 0.9317, Energy: -53.262365+0.001032j
[2025-09-09 19:50:41] [Iter 553/2250] R2[102/600], Temp: 0.9304, Energy: -53.278589-0.003025j
[2025-09-09 19:51:41] [Iter 554/2250] R2[103/600], Temp: 0.9290, Energy: -53.210375-0.000479j
[2025-09-09 19:52:42] [Iter 555/2250] R2[104/600], Temp: 0.9277, Energy: -53.167220+0.002190j
[2025-09-09 19:53:42] [Iter 556/2250] R2[105/600], Temp: 0.9263, Energy: -53.209037+0.002157j
[2025-09-09 19:54:42] [Iter 557/2250] R2[106/600], Temp: 0.9249, Energy: -53.221404-0.002030j
[2025-09-09 19:55:43] [Iter 558/2250] R2[107/600], Temp: 0.9236, Energy: -53.235126-0.001916j
[2025-09-09 19:56:43] [Iter 559/2250] R2[108/600], Temp: 0.9222, Energy: -53.241003-0.003898j
[2025-09-09 19:57:43] [Iter 560/2250] R2[109/600], Temp: 0.9208, Energy: -53.222054-0.001221j
[2025-09-09 19:58:44] [Iter 561/2250] R2[110/600], Temp: 0.9193, Energy: -53.211656-0.000310j
[2025-09-09 19:59:44] [Iter 562/2250] R2[111/600], Temp: 0.9179, Energy: -53.281109-0.001483j
[2025-09-09 20:00:44] [Iter 563/2250] R2[112/600], Temp: 0.9165, Energy: -53.191231+0.000289j
[2025-09-09 20:01:44] [Iter 564/2250] R2[113/600], Temp: 0.9150, Energy: -53.227559-0.001335j
[2025-09-09 20:02:45] [Iter 565/2250] R2[114/600], Temp: 0.9135, Energy: -53.221034-0.002569j
[2025-09-09 20:03:45] [Iter 566/2250] R2[115/600], Temp: 0.9121, Energy: -53.245445-0.003724j
[2025-09-09 20:04:45] [Iter 567/2250] R2[116/600], Temp: 0.9106, Energy: -53.223537+0.001207j
[2025-09-09 20:05:46] [Iter 568/2250] R2[117/600], Temp: 0.9091, Energy: -53.218990+0.002365j
[2025-09-09 20:06:46] [Iter 569/2250] R2[118/600], Temp: 0.9076, Energy: -53.190809-0.000734j
[2025-09-09 20:07:43] [Iter 570/2250] R2[119/600], Temp: 0.9060, Energy: -53.159152-0.001520j
[2025-09-09 20:08:44] [Iter 571/2250] R2[120/600], Temp: 0.9045, Energy: -53.194578-0.001118j
[2025-09-09 20:09:44] [Iter 572/2250] R2[121/600], Temp: 0.9030, Energy: -53.193042+0.001912j
[2025-09-09 20:10:45] [Iter 573/2250] R2[122/600], Temp: 0.9014, Energy: -53.152739-0.000225j
[2025-09-09 20:11:45] [Iter 574/2250] R2[123/600], Temp: 0.8998, Energy: -53.158014+0.001897j
[2025-09-09 20:12:46] [Iter 575/2250] R2[124/600], Temp: 0.8983, Energy: -53.222902-0.000339j
[2025-09-09 20:13:46] [Iter 576/2250] R2[125/600], Temp: 0.8967, Energy: -53.200954-0.005774j
[2025-09-09 20:14:46] [Iter 577/2250] R2[126/600], Temp: 0.8951, Energy: -53.318566+0.001336j
[2025-09-09 20:15:47] [Iter 578/2250] R2[127/600], Temp: 0.8935, Energy: -53.245453-0.002165j
[2025-09-09 20:16:47] [Iter 579/2250] R2[128/600], Temp: 0.8918, Energy: -53.299513+0.004847j
[2025-09-09 20:17:47] [Iter 580/2250] R2[129/600], Temp: 0.8902, Energy: -53.238274+0.002180j
[2025-09-09 20:18:48] [Iter 581/2250] R2[130/600], Temp: 0.8886, Energy: -53.308042+0.000313j
[2025-09-09 20:19:48] [Iter 582/2250] R2[131/600], Temp: 0.8869, Energy: -53.313887-0.000105j
[2025-09-09 20:20:48] [Iter 583/2250] R2[132/600], Temp: 0.8853, Energy: -53.253226-0.000694j
[2025-09-09 20:21:48] [Iter 584/2250] R2[133/600], Temp: 0.8836, Energy: -53.128420-0.005240j
[2025-09-09 20:22:49] [Iter 585/2250] R2[134/600], Temp: 0.8819, Energy: -53.232417-0.001678j
[2025-09-09 20:23:49] [Iter 586/2250] R2[135/600], Temp: 0.8802, Energy: -53.196534-0.002557j
[2025-09-09 20:24:49] [Iter 587/2250] R2[136/600], Temp: 0.8785, Energy: -53.226712+0.000481j
[2025-09-09 20:25:49] [Iter 588/2250] R2[137/600], Temp: 0.8768, Energy: -53.222452-0.002932j
[2025-09-09 20:26:50] [Iter 589/2250] R2[138/600], Temp: 0.8751, Energy: -53.208750-0.000981j
[2025-09-09 20:27:50] [Iter 590/2250] R2[139/600], Temp: 0.8733, Energy: -53.241189+0.000114j
[2025-09-09 20:28:50] [Iter 591/2250] R2[140/600], Temp: 0.8716, Energy: -53.271358+0.000164j
[2025-09-09 20:29:51] [Iter 592/2250] R2[141/600], Temp: 0.8698, Energy: -53.274552+0.002747j
[2025-09-09 20:30:51] [Iter 593/2250] R2[142/600], Temp: 0.8680, Energy: -53.265954-0.000761j
[2025-09-09 20:31:52] [Iter 594/2250] R2[143/600], Temp: 0.8663, Energy: -53.168990+0.001697j
[2025-09-09 20:32:52] [Iter 595/2250] R2[144/600], Temp: 0.8645, Energy: -53.268606-0.000588j
[2025-09-09 20:33:52] [Iter 596/2250] R2[145/600], Temp: 0.8627, Energy: -53.291106+0.001014j
[2025-09-09 20:34:52] [Iter 597/2250] R2[146/600], Temp: 0.8609, Energy: -53.192992-0.000463j
[2025-09-09 20:35:53] [Iter 598/2250] R2[147/600], Temp: 0.8591, Energy: -53.208610-0.003096j
[2025-09-09 20:36:53] [Iter 599/2250] R2[148/600], Temp: 0.8572, Energy: -53.207563-0.003691j
[2025-09-09 20:37:54] [Iter 600/2250] R2[149/600], Temp: 0.8554, Energy: -53.226834-0.000052j
[2025-09-09 20:38:54] [Iter 601/2250] R2[150/600], Temp: 0.8536, Energy: -53.179517+0.000223j
[2025-09-09 20:39:55] [Iter 602/2250] R2[151/600], Temp: 0.8517, Energy: -53.230344+0.002590j
[2025-09-09 20:40:55] [Iter 603/2250] R2[152/600], Temp: 0.8498, Energy: -53.227810-0.002147j
[2025-09-09 20:41:55] [Iter 604/2250] R2[153/600], Temp: 0.8480, Energy: -53.212325+0.002996j
[2025-09-09 20:42:56] [Iter 605/2250] R2[154/600], Temp: 0.8461, Energy: -53.316613+0.000473j
[2025-09-09 20:43:56] [Iter 606/2250] R2[155/600], Temp: 0.8442, Energy: -53.262704-0.001576j
[2025-09-09 20:44:56] [Iter 607/2250] R2[156/600], Temp: 0.8423, Energy: -53.265930+0.002326j
[2025-09-09 20:45:57] [Iter 608/2250] R2[157/600], Temp: 0.8404, Energy: -53.241710-0.001744j
[2025-09-09 20:46:56] [Iter 609/2250] R2[158/600], Temp: 0.8384, Energy: -53.260088+0.000237j
[2025-09-09 20:47:36] [Iter 610/2250] R2[159/600], Temp: 0.8365, Energy: -53.248327+0.001496j
[2025-09-09 20:48:16] [Iter 611/2250] R2[160/600], Temp: 0.8346, Energy: -53.175549+0.001021j
[2025-09-09 20:48:57] [Iter 612/2250] R2[161/600], Temp: 0.8326, Energy: -53.242882-0.000985j
[2025-09-09 20:49:37] [Iter 613/2250] R2[162/600], Temp: 0.8307, Energy: -53.240973-0.001247j
[2025-09-09 20:50:17] [Iter 614/2250] R2[163/600], Temp: 0.8287, Energy: -53.250406+0.001537j
[2025-09-09 20:50:57] [Iter 615/2250] R2[164/600], Temp: 0.8267, Energy: -53.255879+0.000222j
[2025-09-09 20:51:25] [Iter 616/2250] R2[165/600], Temp: 0.8247, Energy: -53.316644+0.002797j
[2025-09-09 20:51:45] [Iter 617/2250] R2[166/600], Temp: 0.8227, Energy: -53.314063+0.002182j
[2025-09-09 20:52:04] [Iter 618/2250] R2[167/600], Temp: 0.8207, Energy: -53.256379+0.001598j
[2025-09-09 20:52:24] [Iter 619/2250] R2[168/600], Temp: 0.8187, Energy: -53.252958+0.001333j
[2025-09-09 20:52:43] [Iter 620/2250] R2[169/600], Temp: 0.8167, Energy: -53.237668+0.004605j
[2025-09-09 20:53:11] [Iter 621/2250] R2[170/600], Temp: 0.8147, Energy: -53.206004-0.001118j
[2025-09-09 20:53:30] [Iter 622/2250] R2[171/600], Temp: 0.8126, Energy: -53.272361+0.001472j
[2025-09-09 20:53:50] [Iter 623/2250] R2[172/600], Temp: 0.8106, Energy: -53.197161-0.002055j
[2025-09-09 20:54:09] [Iter 624/2250] R2[173/600], Temp: 0.8085, Energy: -53.164432-0.000386j
[2025-09-09 20:54:39] [Iter 625/2250] R2[174/600], Temp: 0.8065, Energy: -53.222758+0.003410j
[2025-09-09 20:54:58] [Iter 626/2250] R2[175/600], Temp: 0.8044, Energy: -53.226046+0.001511j
[2025-09-09 20:55:26] [Iter 627/2250] R2[176/600], Temp: 0.8023, Energy: -53.210480+0.000017j
[2025-09-09 20:55:45] [Iter 628/2250] R2[177/600], Temp: 0.8002, Energy: -53.210563-0.004323j
[2025-09-09 20:56:30] [Iter 629/2250] R2[178/600], Temp: 0.7981, Energy: -53.157729-0.006466j
[2025-09-09 20:57:31] [Iter 630/2250] R2[179/600], Temp: 0.7960, Energy: -53.120199+0.001540j
[2025-09-09 20:58:31] [Iter 631/2250] R2[180/600], Temp: 0.7939, Energy: -53.169650-0.002529j
[2025-09-09 20:59:32] [Iter 632/2250] R2[181/600], Temp: 0.7918, Energy: -53.151644-0.000225j
[2025-09-09 21:00:32] [Iter 633/2250] R2[182/600], Temp: 0.7896, Energy: -53.225424+0.002689j
[2025-09-09 21:01:32] [Iter 634/2250] R2[183/600], Temp: 0.7875, Energy: -53.241441+0.003392j
[2025-09-09 21:02:32] [Iter 635/2250] R2[184/600], Temp: 0.7854, Energy: -53.262435-0.002443j
[2025-09-09 21:03:33] [Iter 636/2250] R2[185/600], Temp: 0.7832, Energy: -53.333769-0.001622j
[2025-09-09 21:04:33] [Iter 637/2250] R2[186/600], Temp: 0.7810, Energy: -53.280082+0.000536j
[2025-09-09 21:05:33] [Iter 638/2250] R2[187/600], Temp: 0.7789, Energy: -53.205880-0.001733j
[2025-09-09 21:06:34] [Iter 639/2250] R2[188/600], Temp: 0.7767, Energy: -53.208561-0.000971j
[2025-09-09 21:07:34] [Iter 640/2250] R2[189/600], Temp: 0.7745, Energy: -53.224137+0.002805j
[2025-09-09 21:08:34] [Iter 641/2250] R2[190/600], Temp: 0.7723, Energy: -53.253619-0.003449j
[2025-09-09 21:09:35] [Iter 642/2250] R2[191/600], Temp: 0.7701, Energy: -53.196861-0.000546j
[2025-09-09 21:10:35] [Iter 643/2250] R2[192/600], Temp: 0.7679, Energy: -53.203094-0.000720j
[2025-09-09 21:11:35] [Iter 644/2250] R2[193/600], Temp: 0.7657, Energy: -53.180352-0.000243j
[2025-09-09 21:12:35] [Iter 645/2250] R2[194/600], Temp: 0.7635, Energy: -53.187584-0.001288j
[2025-09-09 21:13:35] [Iter 646/2250] R2[195/600], Temp: 0.7612, Energy: -53.222286-0.002844j
[2025-09-09 21:14:36] [Iter 647/2250] R2[196/600], Temp: 0.7590, Energy: -53.240021+0.000243j
[2025-09-09 21:15:36] [Iter 648/2250] R2[197/600], Temp: 0.7568, Energy: -53.249569-0.002771j
[2025-09-09 21:16:36] [Iter 649/2250] R2[198/600], Temp: 0.7545, Energy: -53.281351-0.000831j
[2025-09-09 21:17:37] [Iter 650/2250] R2[199/600], Temp: 0.7523, Energy: -53.225128+0.000582j
[2025-09-09 21:18:37] [Iter 651/2250] R2[200/600], Temp: 0.7500, Energy: -53.290719+0.002373j
[2025-09-09 21:19:37] [Iter 652/2250] R2[201/600], Temp: 0.7477, Energy: -53.259904-0.000102j
[2025-09-09 21:20:38] [Iter 653/2250] R2[202/600], Temp: 0.7455, Energy: -53.151205-0.004469j
[2025-09-09 21:21:38] [Iter 654/2250] R2[203/600], Temp: 0.7432, Energy: -53.176101-0.000540j
[2025-09-09 21:22:38] [Iter 655/2250] R2[204/600], Temp: 0.7409, Energy: -53.122816-0.001511j
[2025-09-09 21:23:38] [Iter 656/2250] R2[205/600], Temp: 0.7386, Energy: -53.132091+0.002185j
[2025-09-09 21:24:39] [Iter 657/2250] R2[206/600], Temp: 0.7363, Energy: -53.175814-0.001097j
[2025-09-09 21:25:39] [Iter 658/2250] R2[207/600], Temp: 0.7340, Energy: -53.218330-0.001984j
[2025-09-09 21:26:39] [Iter 659/2250] R2[208/600], Temp: 0.7316, Energy: -53.245285+0.004350j
[2025-09-09 21:27:40] [Iter 660/2250] R2[209/600], Temp: 0.7293, Energy: -53.221868-0.003666j
[2025-09-09 21:28:40] [Iter 661/2250] R2[210/600], Temp: 0.7270, Energy: -53.250188-0.002501j
[2025-09-09 21:29:40] [Iter 662/2250] R2[211/600], Temp: 0.7247, Energy: -53.199003+0.000391j
[2025-09-09 21:30:41] [Iter 663/2250] R2[212/600], Temp: 0.7223, Energy: -53.260501-0.001995j
[2025-09-09 21:31:41] [Iter 664/2250] R2[213/600], Temp: 0.7200, Energy: -53.243690+0.000774j
[2025-09-09 21:32:41] [Iter 665/2250] R2[214/600], Temp: 0.7176, Energy: -53.304828-0.000989j
[2025-09-09 21:33:41] [Iter 666/2250] R2[215/600], Temp: 0.7153, Energy: -53.309816+0.004095j
[2025-09-09 21:34:42] [Iter 667/2250] R2[216/600], Temp: 0.7129, Energy: -53.270073-0.003098j
[2025-09-09 21:35:42] [Iter 668/2250] R2[217/600], Temp: 0.7105, Energy: -53.231536+0.000335j
[2025-09-09 21:36:42] [Iter 669/2250] R2[218/600], Temp: 0.7081, Energy: -53.252480+0.000846j
[2025-09-09 21:37:43] [Iter 670/2250] R2[219/600], Temp: 0.7058, Energy: -53.277271-0.000673j
[2025-09-09 21:38:43] [Iter 671/2250] R2[220/600], Temp: 0.7034, Energy: -53.283252-0.000442j
[2025-09-09 21:39:43] [Iter 672/2250] R2[221/600], Temp: 0.7010, Energy: -53.245117-0.002030j
[2025-09-09 21:40:44] [Iter 673/2250] R2[222/600], Temp: 0.6986, Energy: -53.234522-0.001326j
[2025-09-09 21:41:43] [Iter 674/2250] R2[223/600], Temp: 0.6962, Energy: -53.184454-0.000404j
[2025-09-09 21:42:44] [Iter 675/2250] R2[224/600], Temp: 0.6938, Energy: -53.153678+0.001803j
[2025-09-09 21:43:44] [Iter 676/2250] R2[225/600], Temp: 0.6913, Energy: -53.249855-0.000217j
[2025-09-09 21:44:44] [Iter 677/2250] R2[226/600], Temp: 0.6889, Energy: -53.229928+0.001939j
[2025-09-09 21:45:45] [Iter 678/2250] R2[227/600], Temp: 0.6865, Energy: -53.271218+0.001845j
[2025-09-09 21:46:45] [Iter 679/2250] R2[228/600], Temp: 0.6841, Energy: -53.346914-0.002376j
[2025-09-09 21:47:45] [Iter 680/2250] R2[229/600], Temp: 0.6816, Energy: -53.312027+0.001963j
[2025-09-09 21:48:46] [Iter 681/2250] R2[230/600], Temp: 0.6792, Energy: -53.247899+0.000265j
[2025-09-09 21:49:46] [Iter 682/2250] R2[231/600], Temp: 0.6767, Energy: -53.198928-0.001624j
[2025-09-09 21:50:46] [Iter 683/2250] R2[232/600], Temp: 0.6743, Energy: -53.213642+0.001718j
[2025-09-09 21:51:47] [Iter 684/2250] R2[233/600], Temp: 0.6718, Energy: -53.271064+0.001442j
[2025-09-09 21:52:47] [Iter 685/2250] R2[234/600], Temp: 0.6694, Energy: -53.197872+0.001251j
[2025-09-09 21:53:47] [Iter 686/2250] R2[235/600], Temp: 0.6669, Energy: -53.256914+0.001690j
[2025-09-09 21:54:48] [Iter 687/2250] R2[236/600], Temp: 0.6644, Energy: -53.220923+0.000100j
[2025-09-09 21:55:48] [Iter 688/2250] R2[237/600], Temp: 0.6620, Energy: -53.273932+0.001587j
[2025-09-09 21:56:48] [Iter 689/2250] R2[238/600], Temp: 0.6595, Energy: -53.144541+0.002020j
[2025-09-09 21:57:49] [Iter 690/2250] R2[239/600], Temp: 0.6570, Energy: -53.230489-0.003245j
[2025-09-09 21:58:49] [Iter 691/2250] R2[240/600], Temp: 0.6545, Energy: -53.203194-0.001584j
[2025-09-09 21:59:49] [Iter 692/2250] R2[241/600], Temp: 0.6520, Energy: -53.230853+0.002573j
[2025-09-09 22:00:50] [Iter 693/2250] R2[242/600], Temp: 0.6495, Energy: -53.215606+0.001284j
[2025-09-09 22:01:50] [Iter 694/2250] R2[243/600], Temp: 0.6470, Energy: -53.254102+0.002417j
[2025-09-09 22:02:50] [Iter 695/2250] R2[244/600], Temp: 0.6445, Energy: -53.230933+0.001853j
[2025-09-09 22:03:50] [Iter 696/2250] R2[245/600], Temp: 0.6420, Energy: -53.282063+0.004802j
[2025-09-09 22:04:51] [Iter 697/2250] R2[246/600], Temp: 0.6395, Energy: -53.379172+0.000023j
[2025-09-09 22:05:51] [Iter 698/2250] R2[247/600], Temp: 0.6370, Energy: -53.202614-0.002456j
[2025-09-09 22:06:51] [Iter 699/2250] R2[248/600], Temp: 0.6345, Energy: -53.223612-0.000013j
[2025-09-09 22:07:52] [Iter 700/2250] R2[249/600], Temp: 0.6319, Energy: -53.185149+0.000902j
[2025-09-09 22:08:52] [Iter 701/2250] R2[250/600], Temp: 0.6294, Energy: -53.188785-0.000444j
[2025-09-09 22:09:52] [Iter 702/2250] R2[251/600], Temp: 0.6269, Energy: -53.204200-0.001390j
[2025-09-09 22:10:52] [Iter 703/2250] R2[252/600], Temp: 0.6243, Energy: -53.202297+0.000333j
[2025-09-09 22:11:53] [Iter 704/2250] R2[253/600], Temp: 0.6218, Energy: -53.235990-0.000021j
[2025-09-09 22:12:53] [Iter 705/2250] R2[254/600], Temp: 0.6193, Energy: -53.195392+0.001537j
[2025-09-09 22:13:53] [Iter 706/2250] R2[255/600], Temp: 0.6167, Energy: -53.150696+0.000356j
[2025-09-09 22:14:53] [Iter 707/2250] R2[256/600], Temp: 0.6142, Energy: -53.218917+0.001787j
[2025-09-09 22:15:54] [Iter 708/2250] R2[257/600], Temp: 0.6116, Energy: -53.170846+0.002991j
[2025-09-09 22:16:54] [Iter 709/2250] R2[258/600], Temp: 0.6091, Energy: -53.142989-0.000608j
[2025-09-09 22:17:54] [Iter 710/2250] R2[259/600], Temp: 0.6065, Energy: -53.135860+0.000461j
[2025-09-09 22:18:55] [Iter 711/2250] R2[260/600], Temp: 0.6040, Energy: -53.235445-0.000436j
[2025-09-09 22:19:55] [Iter 712/2250] R2[261/600], Temp: 0.6014, Energy: -53.229585-0.001896j
[2025-09-09 22:20:55] [Iter 713/2250] R2[262/600], Temp: 0.5988, Energy: -53.204113+0.001526j
[2025-09-09 22:21:56] [Iter 714/2250] R2[263/600], Temp: 0.5963, Energy: -53.215707+0.001939j
[2025-09-09 22:22:56] [Iter 715/2250] R2[264/600], Temp: 0.5937, Energy: -53.240625-0.000390j
[2025-09-09 22:23:56] [Iter 716/2250] R2[265/600], Temp: 0.5911, Energy: -53.214050-0.000908j
[2025-09-09 22:24:56] [Iter 717/2250] R2[266/600], Temp: 0.5885, Energy: -53.207171-0.000290j
[2025-09-09 22:25:57] [Iter 718/2250] R2[267/600], Temp: 0.5860, Energy: -53.190517-0.000330j
[2025-09-09 22:26:57] [Iter 719/2250] R2[268/600], Temp: 0.5834, Energy: -53.182034-0.001971j
[2025-09-09 22:27:57] [Iter 720/2250] R2[269/600], Temp: 0.5808, Energy: -53.214691+0.002138j
[2025-09-09 22:28:58] [Iter 721/2250] R2[270/600], Temp: 0.5782, Energy: -53.272573+0.001083j
[2025-09-09 22:29:58] [Iter 722/2250] R2[271/600], Temp: 0.5756, Energy: -53.281207+0.000429j
[2025-09-09 22:30:58] [Iter 723/2250] R2[272/600], Temp: 0.5730, Energy: -53.248445-0.001172j
[2025-09-09 22:31:59] [Iter 724/2250] R2[273/600], Temp: 0.5705, Energy: -53.272331+0.000408j
[2025-09-09 22:32:59] [Iter 725/2250] R2[274/600], Temp: 0.5679, Energy: -53.270158+0.001243j
[2025-09-09 22:33:59] [Iter 726/2250] R2[275/600], Temp: 0.5653, Energy: -53.290386-0.002100j
[2025-09-09 22:34:59] [Iter 727/2250] R2[276/600], Temp: 0.5627, Energy: -53.259292-0.000024j
[2025-09-09 22:36:00] [Iter 728/2250] R2[277/600], Temp: 0.5601, Energy: -53.187564-0.002620j
[2025-09-09 22:37:00] [Iter 729/2250] R2[278/600], Temp: 0.5575, Energy: -53.210504-0.000422j
[2025-09-09 22:38:00] [Iter 730/2250] R2[279/600], Temp: 0.5549, Energy: -53.232962+0.003844j
[2025-09-09 22:39:00] [Iter 731/2250] R2[280/600], Temp: 0.5523, Energy: -53.218078+0.000011j
[2025-09-09 22:40:01] [Iter 732/2250] R2[281/600], Temp: 0.5497, Energy: -53.195447+0.002330j
[2025-09-09 22:41:01] [Iter 733/2250] R2[282/600], Temp: 0.5471, Energy: -53.218875+0.003037j
[2025-09-09 22:42:01] [Iter 734/2250] R2[283/600], Temp: 0.5444, Energy: -53.259543-0.000026j
[2025-09-09 22:43:02] [Iter 735/2250] R2[284/600], Temp: 0.5418, Energy: -53.265575+0.000972j
[2025-09-09 22:44:02] [Iter 736/2250] R2[285/600], Temp: 0.5392, Energy: -53.323979+0.000454j
[2025-09-09 22:45:02] [Iter 737/2250] R2[286/600], Temp: 0.5366, Energy: -53.284644-0.001913j
[2025-09-09 22:46:02] [Iter 738/2250] R2[287/600], Temp: 0.5340, Energy: -53.265724-0.003243j
[2025-09-09 22:47:03] [Iter 739/2250] R2[288/600], Temp: 0.5314, Energy: -53.302292+0.001080j
[2025-09-09 22:48:03] [Iter 740/2250] R2[289/600], Temp: 0.5288, Energy: -53.259742+0.001988j
[2025-09-09 22:49:03] [Iter 741/2250] R2[290/600], Temp: 0.5262, Energy: -53.203947-0.000774j
[2025-09-09 22:50:03] [Iter 742/2250] R2[291/600], Temp: 0.5236, Energy: -53.290465-0.002143j
[2025-09-09 22:51:04] [Iter 743/2250] R2[292/600], Temp: 0.5209, Energy: -53.288698+0.000968j
[2025-09-09 22:52:04] [Iter 744/2250] R2[293/600], Temp: 0.5183, Energy: -53.249739-0.000049j
[2025-09-09 22:53:04] [Iter 745/2250] R2[294/600], Temp: 0.5157, Energy: -53.251927-0.002525j
[2025-09-09 22:54:05] [Iter 746/2250] R2[295/600], Temp: 0.5131, Energy: -53.276511-0.001898j
[2025-09-09 22:55:05] [Iter 747/2250] R2[296/600], Temp: 0.5105, Energy: -53.261472+0.000349j
[2025-09-09 22:56:05] [Iter 748/2250] R2[297/600], Temp: 0.5079, Energy: -53.277941-0.000324j
[2025-09-09 22:57:06] [Iter 749/2250] R2[298/600], Temp: 0.5052, Energy: -53.208917+0.000632j
[2025-09-09 22:58:06] [Iter 750/2250] R2[299/600], Temp: 0.5026, Energy: -53.151717+0.003438j
[2025-09-09 22:58:06] ✓ Checkpoint saved: checkpoint_iter_000750.pkl
[2025-09-09 22:59:06] [Iter 751/2250] R2[300/600], Temp: 0.5000, Energy: -53.238648-0.000098j
[2025-09-09 23:00:06] [Iter 752/2250] R2[301/600], Temp: 0.4974, Energy: -53.237472+0.001466j
[2025-09-09 23:01:07] [Iter 753/2250] R2[302/600], Temp: 0.4948, Energy: -53.261642-0.002368j
[2025-09-09 23:02:07] [Iter 754/2250] R2[303/600], Temp: 0.4921, Energy: -53.252590+0.000589j
[2025-09-09 23:03:07] [Iter 755/2250] R2[304/600], Temp: 0.4895, Energy: -53.253632-0.003648j
[2025-09-09 23:04:08] [Iter 756/2250] R2[305/600], Temp: 0.4869, Energy: -53.290716+0.002065j
[2025-09-09 23:05:08] [Iter 757/2250] R2[306/600], Temp: 0.4843, Energy: -53.242038+0.001078j
[2025-09-09 23:06:08] [Iter 758/2250] R2[307/600], Temp: 0.4817, Energy: -53.246571-0.000910j
[2025-09-09 23:07:08] [Iter 759/2250] R2[308/600], Temp: 0.4791, Energy: -53.227698+0.003464j
[2025-09-09 23:08:09] [Iter 760/2250] R2[309/600], Temp: 0.4764, Energy: -53.169750-0.000934j
[2025-09-09 23:09:09] [Iter 761/2250] R2[310/600], Temp: 0.4738, Energy: -53.144381+0.001169j
[2025-09-09 23:10:09] [Iter 762/2250] R2[311/600], Temp: 0.4712, Energy: -53.209440-0.001479j
[2025-09-09 23:11:10] [Iter 763/2250] R2[312/600], Temp: 0.4686, Energy: -53.155287+0.000201j
[2025-09-09 23:12:11] [Iter 764/2250] R2[313/600], Temp: 0.4660, Energy: -53.199894-0.000198j
[2025-09-09 23:13:11] [Iter 765/2250] R2[314/600], Temp: 0.4634, Energy: -53.175013+0.004525j
[2025-09-09 23:14:11] [Iter 766/2250] R2[315/600], Temp: 0.4608, Energy: -53.233402+0.000551j
[2025-09-09 23:15:12] [Iter 767/2250] R2[316/600], Temp: 0.4582, Energy: -53.272478-0.004398j
[2025-09-09 23:16:12] [Iter 768/2250] R2[317/600], Temp: 0.4556, Energy: -53.243354+0.002675j
[2025-09-09 23:17:13] [Iter 769/2250] R2[318/600], Temp: 0.4529, Energy: -53.294074+0.002683j
[2025-09-09 23:18:13] [Iter 770/2250] R2[319/600], Temp: 0.4503, Energy: -53.310290+0.001068j
[2025-09-09 23:19:13] [Iter 771/2250] R2[320/600], Temp: 0.4477, Energy: -53.274836-0.000720j
[2025-09-09 23:20:13] [Iter 772/2250] R2[321/600], Temp: 0.4451, Energy: -53.290805+0.002252j
[2025-09-09 23:21:14] [Iter 773/2250] R2[322/600], Temp: 0.4425, Energy: -53.280602-0.000992j
[2025-09-09 23:22:14] [Iter 774/2250] R2[323/600], Temp: 0.4399, Energy: -53.238470-0.002564j
[2025-09-09 23:23:14] [Iter 775/2250] R2[324/600], Temp: 0.4373, Energy: -53.252356-0.000351j
[2025-09-09 23:24:15] [Iter 776/2250] R2[325/600], Temp: 0.4347, Energy: -53.285080+0.000679j
[2025-09-09 23:25:15] [Iter 777/2250] R2[326/600], Temp: 0.4321, Energy: -53.246990+0.000584j
[2025-09-09 23:26:15] [Iter 778/2250] R2[327/600], Temp: 0.4295, Energy: -53.304337-0.000094j
[2025-09-09 23:27:15] [Iter 779/2250] R2[328/600], Temp: 0.4270, Energy: -53.254074-0.002605j
[2025-09-09 23:28:16] [Iter 780/2250] R2[329/600], Temp: 0.4244, Energy: -53.235484+0.006427j
[2025-09-09 23:29:16] [Iter 781/2250] R2[330/600], Temp: 0.4218, Energy: -53.221128+0.000379j
[2025-09-09 23:30:16] [Iter 782/2250] R2[331/600], Temp: 0.4192, Energy: -53.257144-0.002477j
[2025-09-09 23:31:17] [Iter 783/2250] R2[332/600], Temp: 0.4166, Energy: -53.252346+0.004308j
[2025-09-09 23:32:17] [Iter 784/2250] R2[333/600], Temp: 0.4140, Energy: -53.241280-0.000137j
[2025-09-09 23:33:17] [Iter 785/2250] R2[334/600], Temp: 0.4115, Energy: -53.224133+0.002619j
[2025-09-09 23:34:17] [Iter 786/2250] R2[335/600], Temp: 0.4089, Energy: -53.184339+0.002332j
[2025-09-09 23:35:18] [Iter 787/2250] R2[336/600], Temp: 0.4063, Energy: -53.214854+0.003488j
[2025-09-09 23:36:18] [Iter 788/2250] R2[337/600], Temp: 0.4037, Energy: -53.181675+0.001012j
[2025-09-09 23:37:18] [Iter 789/2250] R2[338/600], Temp: 0.4012, Energy: -53.183775-0.003025j
[2025-09-09 23:38:18] [Iter 790/2250] R2[339/600], Temp: 0.3986, Energy: -53.247619+0.002773j
[2025-09-09 23:39:19] [Iter 791/2250] R2[340/600], Temp: 0.3960, Energy: -53.222049+0.001522j
[2025-09-09 23:40:19] [Iter 792/2250] R2[341/600], Temp: 0.3935, Energy: -53.240024-0.000126j
[2025-09-09 23:41:19] [Iter 793/2250] R2[342/600], Temp: 0.3909, Energy: -53.276384+0.001491j
[2025-09-09 23:42:20] [Iter 794/2250] R2[343/600], Temp: 0.3884, Energy: -53.339597-0.001370j
[2025-09-09 23:43:20] [Iter 795/2250] R2[344/600], Temp: 0.3858, Energy: -53.292796-0.002012j
[2025-09-09 23:44:20] [Iter 796/2250] R2[345/600], Temp: 0.3833, Energy: -53.264394+0.001022j
[2025-09-09 23:45:21] [Iter 797/2250] R2[346/600], Temp: 0.3807, Energy: -53.228989-0.001298j
[2025-09-09 23:46:21] [Iter 798/2250] R2[347/600], Temp: 0.3782, Energy: -53.248593+0.003149j
[2025-09-09 23:47:21] [Iter 799/2250] R2[348/600], Temp: 0.3757, Energy: -53.212744+0.002759j
[2025-09-09 23:48:21] [Iter 800/2250] R2[349/600], Temp: 0.3731, Energy: -53.209750+0.002106j
[2025-09-09 23:49:22] [Iter 801/2250] R2[350/600], Temp: 0.3706, Energy: -53.272212-0.002522j
[2025-09-09 23:50:22] [Iter 802/2250] R2[351/600], Temp: 0.3681, Energy: -53.287392-0.001407j
[2025-09-09 23:51:22] [Iter 803/2250] R2[352/600], Temp: 0.3655, Energy: -53.257119-0.000077j
[2025-09-09 23:52:22] [Iter 804/2250] R2[353/600], Temp: 0.3630, Energy: -53.247110+0.000248j
[2025-09-09 23:53:23] [Iter 805/2250] R2[354/600], Temp: 0.3605, Energy: -53.299142-0.002106j
[2025-09-09 23:54:23] [Iter 806/2250] R2[355/600], Temp: 0.3580, Energy: -53.253370-0.000263j
[2025-09-09 23:55:24] [Iter 807/2250] R2[356/600], Temp: 0.3555, Energy: -53.226034+0.001741j
[2025-09-09 23:56:24] [Iter 808/2250] R2[357/600], Temp: 0.3530, Energy: -53.223895-0.003248j
[2025-09-09 23:57:24] [Iter 809/2250] R2[358/600], Temp: 0.3505, Energy: -53.326829-0.001625j
[2025-09-09 23:58:24] [Iter 810/2250] R2[359/600], Temp: 0.3480, Energy: -53.246054-0.001131j
[2025-09-09 23:59:25] [Iter 811/2250] R2[360/600], Temp: 0.3455, Energy: -53.248564-0.003184j
[2025-09-10 00:00:25] [Iter 812/2250] R2[361/600], Temp: 0.3430, Energy: -53.230621-0.002963j
[2025-09-10 00:01:25] [Iter 813/2250] R2[362/600], Temp: 0.3405, Energy: -53.242815-0.001479j
[2025-09-10 00:02:26] [Iter 814/2250] R2[363/600], Temp: 0.3380, Energy: -53.283021-0.003477j
[2025-09-10 00:03:26] [Iter 815/2250] R2[364/600], Temp: 0.3356, Energy: -53.284599+0.001479j
[2025-09-10 00:04:26] [Iter 816/2250] R2[365/600], Temp: 0.3331, Energy: -53.254170+0.001375j
[2025-09-10 00:05:26] [Iter 817/2250] R2[366/600], Temp: 0.3306, Energy: -53.269953-0.001399j
[2025-09-10 00:06:25] [Iter 818/2250] R2[367/600], Temp: 0.3282, Energy: -53.246843-0.001317j
[2025-09-10 00:07:24] [Iter 819/2250] R2[368/600], Temp: 0.3257, Energy: -53.238343+0.000906j
[2025-09-10 00:08:24] [Iter 820/2250] R2[369/600], Temp: 0.3233, Energy: -53.267433-0.002334j
[2025-09-10 00:09:24] [Iter 821/2250] R2[370/600], Temp: 0.3208, Energy: -53.268382+0.000329j
[2025-09-10 00:10:24] [Iter 822/2250] R2[371/600], Temp: 0.3184, Energy: -53.302271-0.000214j
[2025-09-10 00:11:24] [Iter 823/2250] R2[372/600], Temp: 0.3159, Energy: -53.268606+0.001351j
[2025-09-10 00:12:25] [Iter 824/2250] R2[373/600], Temp: 0.3135, Energy: -53.176390-0.001550j
[2025-09-10 00:13:25] [Iter 825/2250] R2[374/600], Temp: 0.3111, Energy: -53.205960-0.001511j
[2025-09-10 00:14:25] [Iter 826/2250] R2[375/600], Temp: 0.3087, Energy: -53.300433+0.002792j
[2025-09-10 00:15:26] [Iter 827/2250] R2[376/600], Temp: 0.3062, Energy: -53.238816+0.000025j
[2025-09-10 00:16:26] [Iter 828/2250] R2[377/600], Temp: 0.3038, Energy: -53.240389-0.001668j
[2025-09-10 00:17:26] [Iter 829/2250] R2[378/600], Temp: 0.3014, Energy: -53.171465+0.002215j
[2025-09-10 00:18:27] [Iter 830/2250] R2[379/600], Temp: 0.2990, Energy: -53.214062+0.000507j
[2025-09-10 00:19:27] [Iter 831/2250] R2[380/600], Temp: 0.2966, Energy: -53.274707-0.000443j
[2025-09-10 00:20:27] [Iter 832/2250] R2[381/600], Temp: 0.2942, Energy: -53.240557+0.002980j
[2025-09-10 00:21:28] [Iter 833/2250] R2[382/600], Temp: 0.2919, Energy: -53.311308+0.001360j
[2025-09-10 00:22:28] [Iter 834/2250] R2[383/600], Temp: 0.2895, Energy: -53.215818+0.003691j
[2025-09-10 00:23:28] [Iter 835/2250] R2[384/600], Temp: 0.2871, Energy: -53.225901-0.003690j
[2025-09-10 00:24:28] [Iter 836/2250] R2[385/600], Temp: 0.2847, Energy: -53.211589+0.001248j
[2025-09-10 00:25:29] [Iter 837/2250] R2[386/600], Temp: 0.2824, Energy: -53.195577+0.001739j
[2025-09-10 00:26:29] [Iter 838/2250] R2[387/600], Temp: 0.2800, Energy: -53.202793-0.000808j
[2025-09-10 00:27:29] [Iter 839/2250] R2[388/600], Temp: 0.2777, Energy: -53.166673+0.000154j
[2025-09-10 00:28:29] [Iter 840/2250] R2[389/600], Temp: 0.2753, Energy: -53.206929-0.001344j
[2025-09-10 00:29:30] [Iter 841/2250] R2[390/600], Temp: 0.2730, Energy: -53.228334+0.001247j
[2025-09-10 00:30:30] [Iter 842/2250] R2[391/600], Temp: 0.2707, Energy: -53.174319+0.001829j
[2025-09-10 00:31:30] [Iter 843/2250] R2[392/600], Temp: 0.2684, Energy: -53.254064+0.000131j
[2025-09-10 00:32:31] [Iter 844/2250] R2[393/600], Temp: 0.2660, Energy: -53.243480+0.000545j
[2025-09-10 00:33:31] [Iter 845/2250] R2[394/600], Temp: 0.2637, Energy: -53.286203-0.002134j
[2025-09-10 00:34:31] [Iter 846/2250] R2[395/600], Temp: 0.2614, Energy: -53.241804-0.001111j
[2025-09-10 00:35:31] [Iter 847/2250] R2[396/600], Temp: 0.2591, Energy: -53.202126+0.000147j
[2025-09-10 00:36:32] [Iter 848/2250] R2[397/600], Temp: 0.2568, Energy: -53.182775+0.003049j
[2025-09-10 00:37:32] [Iter 849/2250] R2[398/600], Temp: 0.2545, Energy: -53.179694+0.003261j
[2025-09-10 00:38:32] [Iter 850/2250] R2[399/600], Temp: 0.2523, Energy: -53.206124+0.000069j
[2025-09-10 00:39:32] [Iter 851/2250] R2[400/600], Temp: 0.2500, Energy: -53.187732-0.001271j
[2025-09-10 00:40:33] [Iter 852/2250] R2[401/600], Temp: 0.2477, Energy: -53.220347-0.000570j
[2025-09-10 00:41:33] [Iter 853/2250] R2[402/600], Temp: 0.2455, Energy: -53.255962+0.000227j
[2025-09-10 00:42:33] [Iter 854/2250] R2[403/600], Temp: 0.2432, Energy: -53.250386-0.001148j
[2025-09-10 00:43:34] [Iter 855/2250] R2[404/600], Temp: 0.2410, Energy: -53.267313+0.001497j
[2025-09-10 00:44:34] [Iter 856/2250] R2[405/600], Temp: 0.2388, Energy: -53.281089-0.000889j
[2025-09-10 00:45:35] [Iter 857/2250] R2[406/600], Temp: 0.2365, Energy: -53.241674-0.000555j
[2025-09-10 00:46:36] [Iter 858/2250] R2[407/600], Temp: 0.2343, Energy: -53.300859+0.000611j
[2025-09-10 00:47:37] [Iter 859/2250] R2[408/600], Temp: 0.2321, Energy: -53.291159+0.000582j
[2025-09-10 00:48:37] [Iter 860/2250] R2[409/600], Temp: 0.2299, Energy: -53.332799-0.001542j
[2025-09-10 00:49:37] [Iter 861/2250] R2[410/600], Temp: 0.2277, Energy: -53.305191+0.001667j
[2025-09-10 00:50:37] [Iter 862/2250] R2[411/600], Temp: 0.2255, Energy: -53.262570+0.001521j
[2025-09-10 00:51:38] [Iter 863/2250] R2[412/600], Temp: 0.2233, Energy: -53.303897-0.000080j
[2025-09-10 00:52:38] [Iter 864/2250] R2[413/600], Temp: 0.2211, Energy: -53.298868-0.000312j
[2025-09-10 00:53:38] [Iter 865/2250] R2[414/600], Temp: 0.2190, Energy: -53.306827+0.003136j
[2025-09-10 00:54:38] [Iter 866/2250] R2[415/600], Temp: 0.2168, Energy: -53.315942-0.002337j
[2025-09-10 00:55:39] [Iter 867/2250] R2[416/600], Temp: 0.2146, Energy: -53.233321+0.001397j
[2025-09-10 00:56:39] [Iter 868/2250] R2[417/600], Temp: 0.2125, Energy: -53.194499-0.000890j
[2025-09-10 00:57:39] [Iter 869/2250] R2[418/600], Temp: 0.2104, Energy: -53.291363+0.003149j
[2025-09-10 00:58:39] [Iter 870/2250] R2[419/600], Temp: 0.2082, Energy: -53.262168-0.000828j
[2025-09-10 00:59:40] [Iter 871/2250] R2[420/600], Temp: 0.2061, Energy: -53.269154-0.001247j
[2025-09-10 01:00:40] [Iter 872/2250] R2[421/600], Temp: 0.2040, Energy: -53.278643-0.002068j
[2025-09-10 01:01:40] [Iter 873/2250] R2[422/600], Temp: 0.2019, Energy: -53.236096+0.000705j
[2025-09-10 01:02:40] [Iter 874/2250] R2[423/600], Temp: 0.1998, Energy: -53.308638+0.000806j
[2025-09-10 01:03:41] [Iter 875/2250] R2[424/600], Temp: 0.1977, Energy: -53.215365+0.002117j
[2025-09-10 01:04:41] [Iter 876/2250] R2[425/600], Temp: 0.1956, Energy: -53.247588-0.001523j
[2025-09-10 01:05:41] [Iter 877/2250] R2[426/600], Temp: 0.1935, Energy: -53.196753+0.000200j
[2025-09-10 01:06:41] [Iter 878/2250] R2[427/600], Temp: 0.1915, Energy: -53.219095+0.002214j
[2025-09-10 01:07:42] [Iter 879/2250] R2[428/600], Temp: 0.1894, Energy: -53.286619-0.003941j
[2025-09-10 01:08:42] [Iter 880/2250] R2[429/600], Temp: 0.1874, Energy: -53.317692-0.001031j
[2025-09-10 01:09:42] [Iter 881/2250] R2[430/600], Temp: 0.1853, Energy: -53.260644-0.000819j
[2025-09-10 01:10:42] [Iter 882/2250] R2[431/600], Temp: 0.1833, Energy: -53.242124-0.000056j
[2025-09-10 01:11:43] [Iter 883/2250] R2[432/600], Temp: 0.1813, Energy: -53.265353+0.000242j
[2025-09-10 01:12:43] [Iter 884/2250] R2[433/600], Temp: 0.1793, Energy: -53.297592+0.001670j
[2025-09-10 01:13:44] [Iter 885/2250] R2[434/600], Temp: 0.1773, Energy: -53.253065+0.004165j
[2025-09-10 01:14:44] [Iter 886/2250] R2[435/600], Temp: 0.1753, Energy: -53.242244+0.002925j
[2025-09-10 01:15:44] [Iter 887/2250] R2[436/600], Temp: 0.1733, Energy: -53.179554-0.001535j
[2025-09-10 01:16:44] [Iter 888/2250] R2[437/600], Temp: 0.1713, Energy: -53.205220+0.000437j
[2025-09-10 01:17:45] [Iter 889/2250] R2[438/600], Temp: 0.1693, Energy: -53.236069-0.000387j
[2025-09-10 01:18:45] [Iter 890/2250] R2[439/600], Temp: 0.1674, Energy: -53.301117-0.001375j
[2025-09-10 01:19:45] [Iter 891/2250] R2[440/600], Temp: 0.1654, Energy: -53.246248+0.000706j
[2025-09-10 01:20:45] [Iter 892/2250] R2[441/600], Temp: 0.1635, Energy: -53.242678+0.000686j
[2025-09-10 01:21:46] [Iter 893/2250] R2[442/600], Temp: 0.1616, Energy: -53.249097-0.000158j
[2025-09-10 01:22:46] [Iter 894/2250] R2[443/600], Temp: 0.1596, Energy: -53.236736-0.000610j
[2025-09-10 01:23:46] [Iter 895/2250] R2[444/600], Temp: 0.1577, Energy: -53.231282-0.000455j
[2025-09-10 01:24:46] [Iter 896/2250] R2[445/600], Temp: 0.1558, Energy: -53.289689+0.002581j
[2025-09-10 01:25:47] [Iter 897/2250] R2[446/600], Temp: 0.1539, Energy: -53.229959-0.002020j
[2025-09-10 01:26:47] [Iter 898/2250] R2[447/600], Temp: 0.1520, Energy: -53.244057-0.000504j
[2025-09-10 01:27:47] [Iter 899/2250] R2[448/600], Temp: 0.1502, Energy: -53.269228-0.001438j
[2025-09-10 01:28:48] [Iter 900/2250] R2[449/600], Temp: 0.1483, Energy: -53.317997+0.000097j
[2025-09-10 01:29:48] [Iter 901/2250] R2[450/600], Temp: 0.1464, Energy: -53.264126+0.000618j
[2025-09-10 01:30:48] [Iter 902/2250] R2[451/600], Temp: 0.1446, Energy: -53.243640-0.001770j
[2025-09-10 01:31:49] [Iter 903/2250] R2[452/600], Temp: 0.1428, Energy: -53.267799-0.002131j
[2025-09-10 01:32:49] [Iter 904/2250] R2[453/600], Temp: 0.1409, Energy: -53.151373-0.000582j
[2025-09-10 01:33:49] [Iter 905/2250] R2[454/600], Temp: 0.1391, Energy: -53.214598+0.001271j
[2025-09-10 01:34:50] [Iter 906/2250] R2[455/600], Temp: 0.1373, Energy: -53.233440+0.001046j
[2025-09-10 01:35:50] [Iter 907/2250] R2[456/600], Temp: 0.1355, Energy: -53.269154+0.002131j
[2025-09-10 01:36:50] [Iter 908/2250] R2[457/600], Temp: 0.1337, Energy: -53.303450-0.000444j
[2025-09-10 01:37:50] [Iter 909/2250] R2[458/600], Temp: 0.1320, Energy: -53.308425-0.001831j
[2025-09-10 01:38:51] [Iter 910/2250] R2[459/600], Temp: 0.1302, Energy: -53.254543-0.002327j
[2025-09-10 01:39:51] [Iter 911/2250] R2[460/600], Temp: 0.1284, Energy: -53.252611+0.001998j
[2025-09-10 01:40:51] [Iter 912/2250] R2[461/600], Temp: 0.1267, Energy: -53.218154+0.002292j
[2025-09-10 01:41:51] [Iter 913/2250] R2[462/600], Temp: 0.1249, Energy: -53.270058-0.002608j
[2025-09-10 01:42:51] [Iter 914/2250] R2[463/600], Temp: 0.1232, Energy: -53.317824+0.002595j
[2025-09-10 01:43:51] [Iter 915/2250] R2[464/600], Temp: 0.1215, Energy: -53.250715+0.001289j
[2025-09-10 01:44:52] [Iter 916/2250] R2[465/600], Temp: 0.1198, Energy: -53.292384-0.001230j
[2025-09-10 01:45:52] [Iter 917/2250] R2[466/600], Temp: 0.1181, Energy: -53.254818+0.000247j
[2025-09-10 01:46:52] [Iter 918/2250] R2[467/600], Temp: 0.1164, Energy: -53.250378+0.002346j
[2025-09-10 01:47:52] [Iter 919/2250] R2[468/600], Temp: 0.1147, Energy: -53.306639+0.002011j
[2025-09-10 01:48:53] [Iter 920/2250] R2[469/600], Temp: 0.1131, Energy: -53.320196+0.002137j
[2025-09-10 01:49:53] [Iter 921/2250] R2[470/600], Temp: 0.1114, Energy: -53.290601-0.000143j
[2025-09-10 01:50:53] [Iter 922/2250] R2[471/600], Temp: 0.1098, Energy: -53.286628+0.002256j
[2025-09-10 01:51:54] [Iter 923/2250] R2[472/600], Temp: 0.1082, Energy: -53.312063+0.000387j
[2025-09-10 01:52:54] [Iter 924/2250] R2[473/600], Temp: 0.1065, Energy: -53.247022+0.002262j
[2025-09-10 01:53:54] [Iter 925/2250] R2[474/600], Temp: 0.1049, Energy: -53.305159-0.003179j
[2025-09-10 01:54:54] [Iter 926/2250] R2[475/600], Temp: 0.1033, Energy: -53.275530-0.000735j
[2025-09-10 01:55:55] [Iter 927/2250] R2[476/600], Temp: 0.1017, Energy: -53.316641+0.000186j
[2025-09-10 01:56:55] [Iter 928/2250] R2[477/600], Temp: 0.1002, Energy: -53.318893-0.000114j
[2025-09-10 01:57:55] [Iter 929/2250] R2[478/600], Temp: 0.0986, Energy: -53.308128+0.002766j
[2025-09-10 01:58:56] [Iter 930/2250] R2[479/600], Temp: 0.0970, Energy: -53.309272+0.002612j
[2025-09-10 01:59:56] [Iter 931/2250] R2[480/600], Temp: 0.0955, Energy: -53.319612+0.000001j
[2025-09-10 02:00:56] [Iter 932/2250] R2[481/600], Temp: 0.0940, Energy: -53.269150+0.001170j
[2025-09-10 02:01:57] [Iter 933/2250] R2[482/600], Temp: 0.0924, Energy: -53.164958-0.000649j
[2025-09-10 02:02:57] [Iter 934/2250] R2[483/600], Temp: 0.0909, Energy: -53.174571-0.001680j
[2025-09-10 02:03:57] [Iter 935/2250] R2[484/600], Temp: 0.0894, Energy: -53.203979-0.001083j
[2025-09-10 02:04:58] [Iter 936/2250] R2[485/600], Temp: 0.0879, Energy: -53.202874-0.000529j
[2025-09-10 02:05:58] [Iter 937/2250] R2[486/600], Temp: 0.0865, Energy: -53.216248+0.001822j
[2025-09-10 02:06:58] [Iter 938/2250] R2[487/600], Temp: 0.0850, Energy: -53.230461+0.001487j
[2025-09-10 02:07:57] [Iter 939/2250] R2[488/600], Temp: 0.0835, Energy: -53.288897+0.002495j
[2025-09-10 02:08:58] [Iter 940/2250] R2[489/600], Temp: 0.0821, Energy: -53.323195+0.003307j
[2025-09-10 02:09:56] [Iter 941/2250] R2[490/600], Temp: 0.0807, Energy: -53.295955-0.000175j
[2025-09-10 02:10:56] [Iter 942/2250] R2[491/600], Temp: 0.0792, Energy: -53.276401-0.000525j
[2025-09-10 02:11:57] [Iter 943/2250] R2[492/600], Temp: 0.0778, Energy: -53.260508+0.000041j
[2025-09-10 02:12:57] [Iter 944/2250] R2[493/600], Temp: 0.0764, Energy: -53.249990-0.001302j
[2025-09-10 02:13:57] [Iter 945/2250] R2[494/600], Temp: 0.0751, Energy: -53.295875-0.001483j
[2025-09-10 02:14:58] [Iter 946/2250] R2[495/600], Temp: 0.0737, Energy: -53.256777-0.002437j
[2025-09-10 02:15:58] [Iter 947/2250] R2[496/600], Temp: 0.0723, Energy: -53.224791+0.001877j
[2025-09-10 02:16:58] [Iter 948/2250] R2[497/600], Temp: 0.0710, Energy: -53.303673+0.001400j
[2025-09-10 02:17:59] [Iter 949/2250] R2[498/600], Temp: 0.0696, Energy: -53.248860+0.002326j
[2025-09-10 02:18:59] [Iter 950/2250] R2[499/600], Temp: 0.0683, Energy: -53.280583-0.001827j
[2025-09-10 02:19:59] [Iter 951/2250] R2[500/600], Temp: 0.0670, Energy: -53.236837-0.000298j
[2025-09-10 02:20:59] [Iter 952/2250] R2[501/600], Temp: 0.0657, Energy: -53.269555+0.002048j
[2025-09-10 02:22:00] [Iter 953/2250] R2[502/600], Temp: 0.0644, Energy: -53.309374+0.001508j
[2025-09-10 02:23:00] [Iter 954/2250] R2[503/600], Temp: 0.0631, Energy: -53.243053-0.001007j
[2025-09-10 02:24:00] [Iter 955/2250] R2[504/600], Temp: 0.0618, Energy: -53.254352+0.000619j
[2025-09-10 02:25:01] [Iter 956/2250] R2[505/600], Temp: 0.0606, Energy: -53.265995-0.000534j
[2025-09-10 02:26:01] [Iter 957/2250] R2[506/600], Temp: 0.0593, Energy: -53.252326+0.000916j
[2025-09-10 02:27:01] [Iter 958/2250] R2[507/600], Temp: 0.0581, Energy: -53.215523+0.002384j
[2025-09-10 02:28:02] [Iter 959/2250] R2[508/600], Temp: 0.0569, Energy: -53.298712+0.000531j
[2025-09-10 02:29:02] [Iter 960/2250] R2[509/600], Temp: 0.0557, Energy: -53.328071-0.000175j
[2025-09-10 02:30:02] [Iter 961/2250] R2[510/600], Temp: 0.0545, Energy: -53.335202+0.004477j
[2025-09-10 02:31:03] [Iter 962/2250] R2[511/600], Temp: 0.0533, Energy: -53.327126-0.000120j
[2025-09-10 02:32:03] [Iter 963/2250] R2[512/600], Temp: 0.0521, Energy: -53.280799-0.002577j
[2025-09-10 02:33:03] [Iter 964/2250] R2[513/600], Temp: 0.0510, Energy: -53.313693-0.003598j
[2025-09-10 02:34:04] [Iter 965/2250] R2[514/600], Temp: 0.0498, Energy: -53.280942-0.000377j
[2025-09-10 02:35:04] [Iter 966/2250] R2[515/600], Temp: 0.0487, Energy: -53.241043+0.003281j
[2025-09-10 02:36:04] [Iter 967/2250] R2[516/600], Temp: 0.0476, Energy: -53.261084-0.001312j
[2025-09-10 02:37:04] [Iter 968/2250] R2[517/600], Temp: 0.0465, Energy: -53.210400+0.001238j
[2025-09-10 02:38:05] [Iter 969/2250] R2[518/600], Temp: 0.0454, Energy: -53.282347-0.000261j
[2025-09-10 02:39:05] [Iter 970/2250] R2[519/600], Temp: 0.0443, Energy: -53.273192-0.002906j
[2025-09-10 02:40:05] [Iter 971/2250] R2[520/600], Temp: 0.0432, Energy: -53.200527-0.001894j
[2025-09-10 02:41:05] [Iter 972/2250] R2[521/600], Temp: 0.0422, Energy: -53.247230+0.002551j
[2025-09-10 02:42:06] [Iter 973/2250] R2[522/600], Temp: 0.0411, Energy: -53.267166-0.001587j
[2025-09-10 02:43:06] [Iter 974/2250] R2[523/600], Temp: 0.0401, Energy: -53.243487+0.001467j
[2025-09-10 02:44:06] [Iter 975/2250] R2[524/600], Temp: 0.0391, Energy: -53.260864+0.001545j
[2025-09-10 02:45:07] [Iter 976/2250] R2[525/600], Temp: 0.0381, Energy: -53.269567+0.000117j
[2025-09-10 02:46:07] [Iter 977/2250] R2[526/600], Temp: 0.0371, Energy: -53.287447+0.000411j
[2025-09-10 02:47:07] [Iter 978/2250] R2[527/600], Temp: 0.0361, Energy: -53.282224+0.002486j
[2025-09-10 02:48:07] [Iter 979/2250] R2[528/600], Temp: 0.0351, Energy: -53.303206+0.001637j
[2025-09-10 02:49:08] [Iter 980/2250] R2[529/600], Temp: 0.0342, Energy: -53.242434-0.001548j
[2025-09-10 02:50:08] [Iter 981/2250] R2[530/600], Temp: 0.0332, Energy: -53.293300+0.000964j
[2025-09-10 02:51:08] [Iter 982/2250] R2[531/600], Temp: 0.0323, Energy: -53.249584-0.000988j
[2025-09-10 02:52:09] [Iter 983/2250] R2[532/600], Temp: 0.0314, Energy: -53.228008-0.002126j
[2025-09-10 02:53:09] [Iter 984/2250] R2[533/600], Temp: 0.0305, Energy: -53.258129-0.001590j
[2025-09-10 02:54:09] [Iter 985/2250] R2[534/600], Temp: 0.0296, Energy: -53.234337-0.001403j
[2025-09-10 02:55:09] [Iter 986/2250] R2[535/600], Temp: 0.0287, Energy: -53.204068-0.000675j
[2025-09-10 02:56:10] [Iter 987/2250] R2[536/600], Temp: 0.0278, Energy: -53.185071-0.004625j
[2025-09-10 02:57:10] [Iter 988/2250] R2[537/600], Temp: 0.0270, Energy: -53.281419-0.000823j
[2025-09-10 02:58:10] [Iter 989/2250] R2[538/600], Temp: 0.0261, Energy: -53.245711+0.001360j
[2025-09-10 02:59:11] [Iter 990/2250] R2[539/600], Temp: 0.0253, Energy: -53.288389+0.001421j
[2025-09-10 03:00:11] [Iter 991/2250] R2[540/600], Temp: 0.0245, Energy: -53.252245+0.002532j
[2025-09-10 03:01:11] [Iter 992/2250] R2[541/600], Temp: 0.0237, Energy: -53.233230+0.000021j
[2025-09-10 03:02:11] [Iter 993/2250] R2[542/600], Temp: 0.0229, Energy: -53.234188+0.000395j
[2025-09-10 03:03:12] [Iter 994/2250] R2[543/600], Temp: 0.0221, Energy: -53.272450+0.000020j
[2025-09-10 03:04:12] [Iter 995/2250] R2[544/600], Temp: 0.0213, Energy: -53.284049-0.000670j
[2025-09-10 03:05:12] [Iter 996/2250] R2[545/600], Temp: 0.0206, Energy: -53.304649+0.002104j
[2025-09-10 03:06:13] [Iter 997/2250] R2[546/600], Temp: 0.0199, Energy: -53.267483-0.001563j
[2025-09-10 03:07:13] [Iter 998/2250] R2[547/600], Temp: 0.0191, Energy: -53.338985-0.001584j
[2025-09-10 03:08:13] [Iter 999/2250] R2[548/600], Temp: 0.0184, Energy: -53.277861+0.000718j
[2025-09-10 03:09:13] [Iter 1000/2250] R2[549/600], Temp: 0.0177, Energy: -53.335018+0.000541j
[2025-09-10 03:09:13] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-09-10 03:10:13] [Iter 1001/2250] R2[550/600], Temp: 0.0170, Energy: -53.338485-0.001151j
[2025-09-10 03:11:14] [Iter 1002/2250] R2[551/600], Temp: 0.0164, Energy: -53.291354-0.001519j
[2025-09-10 03:12:14] [Iter 1003/2250] R2[552/600], Temp: 0.0157, Energy: -53.240793+0.000093j
[2025-09-10 03:13:14] [Iter 1004/2250] R2[553/600], Temp: 0.0151, Energy: -53.240842-0.001490j
[2025-09-10 03:14:14] [Iter 1005/2250] R2[554/600], Temp: 0.0144, Energy: -53.241840-0.001142j
[2025-09-10 03:15:15] [Iter 1006/2250] R2[555/600], Temp: 0.0138, Energy: -53.255536+0.000565j
[2025-09-10 03:16:15] [Iter 1007/2250] R2[556/600], Temp: 0.0132, Energy: -53.244728+0.000613j
[2025-09-10 03:17:15] [Iter 1008/2250] R2[557/600], Temp: 0.0126, Energy: -53.304359+0.000914j
[2025-09-10 03:18:16] [Iter 1009/2250] R2[558/600], Temp: 0.0120, Energy: -53.277280+0.000570j
[2025-09-10 03:19:16] [Iter 1010/2250] R2[559/600], Temp: 0.0115, Energy: -53.299548+0.002124j
[2025-09-10 03:20:16] [Iter 1011/2250] R2[560/600], Temp: 0.0109, Energy: -53.321712+0.000798j
[2025-09-10 03:21:16] [Iter 1012/2250] R2[561/600], Temp: 0.0104, Energy: -53.366416+0.000119j
[2025-09-10 03:22:17] [Iter 1013/2250] R2[562/600], Temp: 0.0099, Energy: -53.327834-0.001866j
[2025-09-10 03:23:17] [Iter 1014/2250] R2[563/600], Temp: 0.0094, Energy: -53.233842+0.002408j
[2025-09-10 03:24:17] [Iter 1015/2250] R2[564/600], Temp: 0.0089, Energy: -53.268857+0.000916j
[2025-09-10 03:25:18] [Iter 1016/2250] R2[565/600], Temp: 0.0084, Energy: -53.288477+0.002067j
[2025-09-10 03:26:18] [Iter 1017/2250] R2[566/600], Temp: 0.0079, Energy: -53.283972-0.001483j
[2025-09-10 03:27:18] [Iter 1018/2250] R2[567/600], Temp: 0.0074, Energy: -53.197647+0.000037j
[2025-09-10 03:28:18] [Iter 1019/2250] R2[568/600], Temp: 0.0070, Energy: -53.143262+0.000276j
[2025-09-10 03:29:19] [Iter 1020/2250] R2[569/600], Temp: 0.0066, Energy: -53.249761-0.000839j
[2025-09-10 03:30:19] [Iter 1021/2250] R2[570/600], Temp: 0.0062, Energy: -53.241654+0.004563j
[2025-09-10 03:31:19] [Iter 1022/2250] R2[571/600], Temp: 0.0058, Energy: -53.224396-0.001740j
[2025-09-10 03:32:20] [Iter 1023/2250] R2[572/600], Temp: 0.0054, Energy: -53.201804+0.000510j
[2025-09-10 03:33:20] [Iter 1024/2250] R2[573/600], Temp: 0.0050, Energy: -53.314010-0.001884j
[2025-09-10 03:34:20] [Iter 1025/2250] R2[574/600], Temp: 0.0046, Energy: -53.286290-0.000291j
[2025-09-10 03:35:21] [Iter 1026/2250] R2[575/600], Temp: 0.0043, Energy: -53.276280+0.002623j
[2025-09-10 03:36:21] [Iter 1027/2250] R2[576/600], Temp: 0.0039, Energy: -53.239651+0.000400j
[2025-09-10 03:37:21] [Iter 1028/2250] R2[577/600], Temp: 0.0036, Energy: -53.235516+0.002275j
[2025-09-10 03:38:21] [Iter 1029/2250] R2[578/600], Temp: 0.0033, Energy: -53.292374-0.000561j
[2025-09-10 03:39:22] [Iter 1030/2250] R2[579/600], Temp: 0.0030, Energy: -53.204732-0.001479j
[2025-09-10 03:40:22] [Iter 1031/2250] R2[580/600], Temp: 0.0027, Energy: -53.242380-0.000746j
[2025-09-10 03:41:21] [Iter 1032/2250] R2[581/600], Temp: 0.0025, Energy: -53.286571-0.000456j
[2025-09-10 03:42:19] [Iter 1033/2250] R2[582/600], Temp: 0.0022, Energy: -53.310477+0.000183j
[2025-09-10 03:43:19] [Iter 1034/2250] R2[583/600], Temp: 0.0020, Energy: -53.232630+0.000002j
[2025-09-10 03:44:19] [Iter 1035/2250] R2[584/600], Temp: 0.0018, Energy: -53.261432+0.000345j
[2025-09-10 03:45:20] [Iter 1036/2250] R2[585/600], Temp: 0.0015, Energy: -53.315673-0.002929j
[2025-09-10 03:46:20] [Iter 1037/2250] R2[586/600], Temp: 0.0013, Energy: -53.268701-0.001663j
[2025-09-10 03:47:20] [Iter 1038/2250] R2[587/600], Temp: 0.0012, Energy: -53.322167-0.001856j
[2025-09-10 03:48:21] [Iter 1039/2250] R2[588/600], Temp: 0.0010, Energy: -53.292119-0.003112j
[2025-09-10 03:49:21] [Iter 1040/2250] R2[589/600], Temp: 0.0008, Energy: -53.293063+0.000863j
[2025-09-10 03:50:21] [Iter 1041/2250] R2[590/600], Temp: 0.0007, Energy: -53.259626-0.001119j
[2025-09-10 03:51:21] [Iter 1042/2250] R2[591/600], Temp: 0.0006, Energy: -53.328031-0.000918j
[2025-09-10 03:52:22] [Iter 1043/2250] R2[592/600], Temp: 0.0004, Energy: -53.269419-0.001888j
[2025-09-10 03:53:22] [Iter 1044/2250] R2[593/600], Temp: 0.0003, Energy: -53.282650-0.000516j
[2025-09-10 03:54:22] [Iter 1045/2250] R2[594/600], Temp: 0.0002, Energy: -53.240216-0.002291j
[2025-09-10 03:55:23] [Iter 1046/2250] R2[595/600], Temp: 0.0002, Energy: -53.233880+0.001505j
[2025-09-10 03:56:23] [Iter 1047/2250] R2[596/600], Temp: 0.0001, Energy: -53.284300+0.000697j
[2025-09-10 03:57:23] [Iter 1048/2250] R2[597/600], Temp: 0.0001, Energy: -53.284472-0.001304j
[2025-09-10 03:58:24] [Iter 1049/2250] R2[598/600], Temp: 0.0000, Energy: -53.273623+0.000921j
[2025-09-10 03:59:24] [Iter 1050/2250] R2[599/600], Temp: 0.0000, Energy: -53.301174-0.001654j
[2025-09-10 03:59:24] RESTART #3 | Period: 1200
[2025-09-10 04:00:24] [Iter 1051/2250] R3[0/1200], Temp: 1.0000, Energy: -53.316986+0.001479j
[2025-09-10 04:01:25] [Iter 1052/2250] R3[1/1200], Temp: 1.0000, Energy: -53.324883-0.000372j
[2025-09-10 04:02:25] [Iter 1053/2250] R3[2/1200], Temp: 1.0000, Energy: -53.317528+0.001605j
[2025-09-10 04:03:25] [Iter 1054/2250] R3[3/1200], Temp: 1.0000, Energy: -53.334264+0.000903j
[2025-09-10 04:04:26] [Iter 1055/2250] R3[4/1200], Temp: 1.0000, Energy: -53.270852-0.003662j
[2025-09-10 04:05:26] [Iter 1056/2250] R3[5/1200], Temp: 1.0000, Energy: -53.256712+0.002221j
[2025-09-10 04:06:26] [Iter 1057/2250] R3[6/1200], Temp: 0.9999, Energy: -53.320216+0.000602j
[2025-09-10 04:07:27] [Iter 1058/2250] R3[7/1200], Temp: 0.9999, Energy: -53.313769+0.001359j
[2025-09-10 04:08:27] [Iter 1059/2250] R3[8/1200], Temp: 0.9999, Energy: -53.224253+0.000611j
[2025-09-10 04:09:27] [Iter 1060/2250] R3[9/1200], Temp: 0.9999, Energy: -53.300363-0.000763j
[2025-09-10 04:10:27] [Iter 1061/2250] R3[10/1200], Temp: 0.9998, Energy: -53.322315-0.001404j
[2025-09-10 04:11:28] [Iter 1062/2250] R3[11/1200], Temp: 0.9998, Energy: -53.258738-0.001930j
[2025-09-10 04:12:28] [Iter 1063/2250] R3[12/1200], Temp: 0.9998, Energy: -53.282803-0.000233j
[2025-09-10 04:13:28] [Iter 1064/2250] R3[13/1200], Temp: 0.9997, Energy: -53.300409-0.001339j
[2025-09-10 04:14:28] [Iter 1065/2250] R3[14/1200], Temp: 0.9997, Energy: -53.342301-0.000948j
[2025-09-10 04:15:29] [Iter 1066/2250] R3[15/1200], Temp: 0.9996, Energy: -53.337032-0.000309j
[2025-09-10 04:16:29] [Iter 1067/2250] R3[16/1200], Temp: 0.9996, Energy: -53.320960+0.002704j
[2025-09-10 04:17:29] [Iter 1068/2250] R3[17/1200], Temp: 0.9995, Energy: -53.322772+0.000238j
[2025-09-10 04:18:30] [Iter 1069/2250] R3[18/1200], Temp: 0.9994, Energy: -53.254755+0.001748j
[2025-09-10 04:19:30] [Iter 1070/2250] R3[19/1200], Temp: 0.9994, Energy: -53.259904-0.000639j
[2025-09-10 04:20:30] [Iter 1071/2250] R3[20/1200], Temp: 0.9993, Energy: -53.278240+0.001363j
[2025-09-10 04:21:30] [Iter 1072/2250] R3[21/1200], Temp: 0.9992, Energy: -53.237835+0.000629j
[2025-09-10 04:22:31] [Iter 1073/2250] R3[22/1200], Temp: 0.9992, Energy: -53.325916+0.003084j
[2025-09-10 04:23:31] [Iter 1074/2250] R3[23/1200], Temp: 0.9991, Energy: -53.278089-0.001016j
[2025-09-10 04:24:31] [Iter 1075/2250] R3[24/1200], Temp: 0.9990, Energy: -53.295604-0.001362j
[2025-09-10 04:25:32] [Iter 1076/2250] R3[25/1200], Temp: 0.9989, Energy: -53.277697-0.000989j
[2025-09-10 04:26:32] [Iter 1077/2250] R3[26/1200], Temp: 0.9988, Energy: -53.273672+0.001757j
[2025-09-10 04:27:32] [Iter 1078/2250] R3[27/1200], Temp: 0.9988, Energy: -53.245217+0.000797j
[2025-09-10 04:28:32] [Iter 1079/2250] R3[28/1200], Temp: 0.9987, Energy: -53.243362-0.001883j
[2025-09-10 04:29:32] [Iter 1080/2250] R3[29/1200], Temp: 0.9986, Energy: -53.255887-0.001206j
[2025-09-10 04:30:33] [Iter 1081/2250] R3[30/1200], Temp: 0.9985, Energy: -53.337669-0.002544j
[2025-09-10 04:31:33] [Iter 1082/2250] R3[31/1200], Temp: 0.9984, Energy: -53.312432+0.000721j
[2025-09-10 04:32:33] [Iter 1083/2250] R3[32/1200], Temp: 0.9982, Energy: -53.252918-0.001359j
[2025-09-10 04:33:34] [Iter 1084/2250] R3[33/1200], Temp: 0.9981, Energy: -53.266994+0.000189j
[2025-09-10 04:34:34] [Iter 1085/2250] R3[34/1200], Temp: 0.9980, Energy: -53.257988-0.001129j
[2025-09-10 04:35:34] [Iter 1086/2250] R3[35/1200], Temp: 0.9979, Energy: -53.288732+0.001628j
[2025-09-10 04:36:34] [Iter 1087/2250] R3[36/1200], Temp: 0.9978, Energy: -53.184118-0.001982j
[2025-09-10 04:37:35] [Iter 1088/2250] R3[37/1200], Temp: 0.9977, Energy: -53.185341+0.000458j
[2025-09-10 04:38:35] [Iter 1089/2250] R3[38/1200], Temp: 0.9975, Energy: -53.239341+0.001979j
[2025-09-10 04:39:35] [Iter 1090/2250] R3[39/1200], Temp: 0.9974, Energy: -53.234997-0.001295j
[2025-09-10 04:40:36] [Iter 1091/2250] R3[40/1200], Temp: 0.9973, Energy: -53.235029+0.000127j
[2025-09-10 04:41:36] [Iter 1092/2250] R3[41/1200], Temp: 0.9971, Energy: -53.229758-0.000927j
[2025-09-10 04:42:36] [Iter 1093/2250] R3[42/1200], Temp: 0.9970, Energy: -53.263958+0.000404j
[2025-09-10 04:43:36] [Iter 1094/2250] R3[43/1200], Temp: 0.9968, Energy: -53.226729-0.001749j
[2025-09-10 04:44:37] [Iter 1095/2250] R3[44/1200], Temp: 0.9967, Energy: -53.236040-0.001299j
[2025-09-10 04:45:37] [Iter 1096/2250] R3[45/1200], Temp: 0.9965, Energy: -53.271947-0.001252j
[2025-09-10 04:46:37] [Iter 1097/2250] R3[46/1200], Temp: 0.9964, Energy: -53.240022-0.000069j
[2025-09-10 04:47:37] [Iter 1098/2250] R3[47/1200], Temp: 0.9962, Energy: -53.242817-0.000200j
[2025-09-10 04:48:38] [Iter 1099/2250] R3[48/1200], Temp: 0.9961, Energy: -53.271366-0.000035j
[2025-09-10 04:49:38] [Iter 1100/2250] R3[49/1200], Temp: 0.9959, Energy: -53.278054-0.001160j
[2025-09-10 04:50:38] [Iter 1101/2250] R3[50/1200], Temp: 0.9957, Energy: -53.272674+0.000034j
[2025-09-10 04:51:38] [Iter 1102/2250] R3[51/1200], Temp: 0.9955, Energy: -53.312628+0.001608j
[2025-09-10 04:52:39] [Iter 1103/2250] R3[52/1200], Temp: 0.9954, Energy: -53.227620+0.001259j
[2025-09-10 04:53:39] [Iter 1104/2250] R3[53/1200], Temp: 0.9952, Energy: -53.238216+0.000129j
[2025-09-10 04:54:39] [Iter 1105/2250] R3[54/1200], Temp: 0.9950, Energy: -53.199010-0.000031j
[2025-09-10 04:55:40] [Iter 1106/2250] R3[55/1200], Temp: 0.9948, Energy: -53.230292-0.000337j
[2025-09-10 04:56:40] [Iter 1107/2250] R3[56/1200], Temp: 0.9946, Energy: -53.185682-0.003569j
[2025-09-10 04:57:40] [Iter 1108/2250] R3[57/1200], Temp: 0.9944, Energy: -53.232080-0.001095j
[2025-09-10 04:58:40] [Iter 1109/2250] R3[58/1200], Temp: 0.9942, Energy: -53.268853+0.000388j
[2025-09-10 04:59:41] [Iter 1110/2250] R3[59/1200], Temp: 0.9940, Energy: -53.274408-0.002492j
[2025-09-10 05:00:41] [Iter 1111/2250] R3[60/1200], Temp: 0.9938, Energy: -53.269775+0.002568j
[2025-09-10 05:01:41] [Iter 1112/2250] R3[61/1200], Temp: 0.9936, Energy: -53.289449-0.001507j
[2025-09-10 05:02:41] [Iter 1113/2250] R3[62/1200], Temp: 0.9934, Energy: -53.352985+0.001862j
[2025-09-10 05:03:42] [Iter 1114/2250] R3[63/1200], Temp: 0.9932, Energy: -53.321592-0.000965j
[2025-09-10 05:04:42] [Iter 1115/2250] R3[64/1200], Temp: 0.9930, Energy: -53.329735+0.001552j
[2025-09-10 05:05:42] [Iter 1116/2250] R3[65/1200], Temp: 0.9928, Energy: -53.353860-0.000966j
[2025-09-10 05:06:43] [Iter 1117/2250] R3[66/1200], Temp: 0.9926, Energy: -53.321412+0.002309j
[2025-09-10 05:07:43] [Iter 1118/2250] R3[67/1200], Temp: 0.9923, Energy: -53.317396-0.001123j
[2025-09-10 05:08:43] [Iter 1119/2250] R3[68/1200], Temp: 0.9921, Energy: -53.279890-0.000719j
[2025-09-10 05:09:44] [Iter 1120/2250] R3[69/1200], Temp: 0.9919, Energy: -53.290561+0.001756j
[2025-09-10 05:10:44] [Iter 1121/2250] R3[70/1200], Temp: 0.9916, Energy: -53.375897+0.000982j
[2025-09-10 05:11:44] [Iter 1122/2250] R3[71/1200], Temp: 0.9914, Energy: -53.318180-0.000137j
[2025-09-10 05:12:45] [Iter 1123/2250] R3[72/1200], Temp: 0.9911, Energy: -53.344864-0.000740j
[2025-09-10 05:13:45] [Iter 1124/2250] R3[73/1200], Temp: 0.9909, Energy: -53.302474+0.001435j
[2025-09-10 05:14:45] [Iter 1125/2250] R3[74/1200], Temp: 0.9906, Energy: -53.364761+0.002695j
[2025-09-10 05:15:45] [Iter 1126/2250] R3[75/1200], Temp: 0.9904, Energy: -53.391274-0.001466j
[2025-09-10 05:16:45] [Iter 1127/2250] R3[76/1200], Temp: 0.9901, Energy: -53.373693-0.000329j
[2025-09-10 05:17:46] [Iter 1128/2250] R3[77/1200], Temp: 0.9899, Energy: -53.237894+0.001232j
[2025-09-10 05:18:46] [Iter 1129/2250] R3[78/1200], Temp: 0.9896, Energy: -53.299567-0.003497j
[2025-09-10 05:19:43] [Iter 1130/2250] R3[79/1200], Temp: 0.9893, Energy: -53.207005+0.002137j
[2025-09-10 05:20:44] [Iter 1131/2250] R3[80/1200], Temp: 0.9891, Energy: -53.256095-0.003896j
[2025-09-10 05:21:44] [Iter 1132/2250] R3[81/1200], Temp: 0.9888, Energy: -53.208260+0.000986j
[2025-09-10 05:22:44] [Iter 1133/2250] R3[82/1200], Temp: 0.9885, Energy: -53.227124+0.001491j
[2025-09-10 05:23:45] [Iter 1134/2250] R3[83/1200], Temp: 0.9882, Energy: -53.231539-0.000308j
[2025-09-10 05:24:45] [Iter 1135/2250] R3[84/1200], Temp: 0.9880, Energy: -53.279026+0.001830j
[2025-09-10 05:25:45] [Iter 1136/2250] R3[85/1200], Temp: 0.9877, Energy: -53.240326+0.003077j
[2025-09-10 05:26:45] [Iter 1137/2250] R3[86/1200], Temp: 0.9874, Energy: -53.175433-0.000227j
[2025-09-10 05:27:46] [Iter 1138/2250] R3[87/1200], Temp: 0.9871, Energy: -53.210976+0.000955j
[2025-09-10 05:28:46] [Iter 1139/2250] R3[88/1200], Temp: 0.9868, Energy: -53.190285+0.002974j
[2025-09-10 05:29:47] [Iter 1140/2250] R3[89/1200], Temp: 0.9865, Energy: -53.230404-0.000005j
[2025-09-10 05:30:47] [Iter 1141/2250] R3[90/1200], Temp: 0.9862, Energy: -53.210004+0.001422j
[2025-09-10 05:31:47] [Iter 1142/2250] R3[91/1200], Temp: 0.9859, Energy: -53.219744-0.000647j
[2025-09-10 05:32:47] [Iter 1143/2250] R3[92/1200], Temp: 0.9856, Energy: -53.286992+0.000114j
[2025-09-10 05:33:48] [Iter 1144/2250] R3[93/1200], Temp: 0.9853, Energy: -53.276559-0.000467j
[2025-09-10 05:34:48] [Iter 1145/2250] R3[94/1200], Temp: 0.9849, Energy: -53.251677+0.003544j
[2025-09-10 05:35:48] [Iter 1146/2250] R3[95/1200], Temp: 0.9846, Energy: -53.272547+0.002278j
[2025-09-10 05:36:48] [Iter 1147/2250] R3[96/1200], Temp: 0.9843, Energy: -53.243922+0.000622j
[2025-09-10 05:37:48] [Iter 1148/2250] R3[97/1200], Temp: 0.9840, Energy: -53.257575-0.000074j
[2025-09-10 05:38:49] [Iter 1149/2250] R3[98/1200], Temp: 0.9836, Energy: -53.239495+0.000844j
[2025-09-10 05:39:49] [Iter 1150/2250] R3[99/1200], Temp: 0.9833, Energy: -53.210437-0.000315j
[2025-09-10 05:40:49] [Iter 1151/2250] R3[100/1200], Temp: 0.9830, Energy: -53.242748-0.001195j
[2025-09-10 05:41:50] [Iter 1152/2250] R3[101/1200], Temp: 0.9826, Energy: -53.193355+0.000840j
[2025-09-10 05:42:50] [Iter 1153/2250] R3[102/1200], Temp: 0.9823, Energy: -53.238951-0.001872j
[2025-09-10 05:43:50] [Iter 1154/2250] R3[103/1200], Temp: 0.9819, Energy: -53.170009-0.002512j
[2025-09-10 05:44:51] [Iter 1155/2250] R3[104/1200], Temp: 0.9816, Energy: -53.282431+0.001798j
[2025-09-10 05:45:51] [Iter 1156/2250] R3[105/1200], Temp: 0.9812, Energy: -53.236174-0.001744j
[2025-09-10 05:46:51] [Iter 1157/2250] R3[106/1200], Temp: 0.9809, Energy: -53.168541-0.002588j
[2025-09-10 05:47:51] [Iter 1158/2250] R3[107/1200], Temp: 0.9805, Energy: -53.204265+0.001542j
[2025-09-10 05:48:52] [Iter 1159/2250] R3[108/1200], Temp: 0.9801, Energy: -53.219435-0.000124j
[2025-09-10 05:49:52] [Iter 1160/2250] R3[109/1200], Temp: 0.9798, Energy: -53.268290-0.001954j
[2025-09-10 05:50:52] [Iter 1161/2250] R3[110/1200], Temp: 0.9794, Energy: -53.286260-0.000024j
[2025-09-10 05:51:52] [Iter 1162/2250] R3[111/1200], Temp: 0.9790, Energy: -53.230228+0.001147j
[2025-09-10 05:52:53] [Iter 1163/2250] R3[112/1200], Temp: 0.9787, Energy: -53.295910+0.001840j
[2025-09-10 05:53:53] [Iter 1164/2250] R3[113/1200], Temp: 0.9783, Energy: -53.306819+0.001583j
[2025-09-10 05:54:53] [Iter 1165/2250] R3[114/1200], Temp: 0.9779, Energy: -53.283621-0.000285j
[2025-09-10 05:55:53] [Iter 1166/2250] R3[115/1200], Temp: 0.9775, Energy: -53.236329+0.000025j
[2025-09-10 05:56:54] [Iter 1167/2250] R3[116/1200], Temp: 0.9771, Energy: -53.252970-0.002940j
[2025-09-10 05:57:54] [Iter 1168/2250] R3[117/1200], Temp: 0.9767, Energy: -53.227247+0.001587j
[2025-09-10 05:58:54] [Iter 1169/2250] R3[118/1200], Temp: 0.9763, Energy: -53.252968-0.000166j
[2025-09-10 05:59:55] [Iter 1170/2250] R3[119/1200], Temp: 0.9759, Energy: -53.352295+0.001286j
[2025-09-10 06:00:55] [Iter 1171/2250] R3[120/1200], Temp: 0.9755, Energy: -53.327140-0.002483j
[2025-09-10 06:01:55] [Iter 1172/2250] R3[121/1200], Temp: 0.9751, Energy: -53.351965+0.002530j
[2025-09-10 06:02:55] [Iter 1173/2250] R3[122/1200], Temp: 0.9747, Energy: -53.279350+0.000097j
[2025-09-10 06:03:56] [Iter 1174/2250] R3[123/1200], Temp: 0.9743, Energy: -53.265556+0.001170j
[2025-09-10 06:04:56] [Iter 1175/2250] R3[124/1200], Temp: 0.9739, Energy: -53.222338+0.000403j
[2025-09-10 06:05:56] [Iter 1176/2250] R3[125/1200], Temp: 0.9735, Energy: -53.285340+0.000002j
[2025-09-10 06:06:57] [Iter 1177/2250] R3[126/1200], Temp: 0.9730, Energy: -53.268987+0.000979j
[2025-09-10 06:07:57] [Iter 1178/2250] R3[127/1200], Temp: 0.9726, Energy: -53.300393-0.002965j
[2025-09-10 06:08:57] [Iter 1179/2250] R3[128/1200], Temp: 0.9722, Energy: -53.248672-0.000846j
[2025-09-10 06:09:58] [Iter 1180/2250] R3[129/1200], Temp: 0.9718, Energy: -53.281133-0.001901j
[2025-09-10 06:10:58] [Iter 1181/2250] R3[130/1200], Temp: 0.9713, Energy: -53.216081-0.001394j
[2025-09-10 06:11:58] [Iter 1182/2250] R3[131/1200], Temp: 0.9709, Energy: -53.270752-0.000811j
[2025-09-10 06:12:58] [Iter 1183/2250] R3[132/1200], Temp: 0.9704, Energy: -53.273386+0.000501j
[2025-09-10 06:13:59] [Iter 1184/2250] R3[133/1200], Temp: 0.9700, Energy: -53.266871-0.001606j
[2025-09-10 06:14:59] [Iter 1185/2250] R3[134/1200], Temp: 0.9695, Energy: -53.253535-0.001920j
[2025-09-10 06:15:59] [Iter 1186/2250] R3[135/1200], Temp: 0.9691, Energy: -53.304824+0.000469j
[2025-09-10 06:17:00] [Iter 1187/2250] R3[136/1200], Temp: 0.9686, Energy: -53.275047+0.000574j
[2025-09-10 06:18:00] [Iter 1188/2250] R3[137/1200], Temp: 0.9682, Energy: -53.208466-0.002399j
[2025-09-10 06:19:00] [Iter 1189/2250] R3[138/1200], Temp: 0.9677, Energy: -53.231400+0.002164j
[2025-09-10 06:20:00] [Iter 1190/2250] R3[139/1200], Temp: 0.9673, Energy: -53.302562+0.000505j
[2025-09-10 06:21:01] [Iter 1191/2250] R3[140/1200], Temp: 0.9668, Energy: -53.308796-0.002609j
[2025-09-10 06:22:01] [Iter 1192/2250] R3[141/1200], Temp: 0.9663, Energy: -53.271240-0.001106j
[2025-09-10 06:23:01] [Iter 1193/2250] R3[142/1200], Temp: 0.9658, Energy: -53.322318+0.002278j
[2025-09-10 06:24:02] [Iter 1194/2250] R3[143/1200], Temp: 0.9654, Energy: -53.352138+0.000108j
[2025-09-10 06:25:02] [Iter 1195/2250] R3[144/1200], Temp: 0.9649, Energy: -53.315875+0.001407j
[2025-09-10 06:26:02] [Iter 1196/2250] R3[145/1200], Temp: 0.9644, Energy: -53.253164+0.000440j
[2025-09-10 06:27:02] [Iter 1197/2250] R3[146/1200], Temp: 0.9639, Energy: -53.294819+0.000501j
[2025-09-10 06:28:03] [Iter 1198/2250] R3[147/1200], Temp: 0.9634, Energy: -53.223488-0.002672j
[2025-09-10 06:29:03] [Iter 1199/2250] R3[148/1200], Temp: 0.9629, Energy: -53.230030-0.001230j
[2025-09-10 06:30:03] [Iter 1200/2250] R3[149/1200], Temp: 0.9624, Energy: -53.332633+0.000856j
[2025-09-10 06:31:04] [Iter 1201/2250] R3[150/1200], Temp: 0.9619, Energy: -53.217874+0.000180j
[2025-09-10 06:32:04] [Iter 1202/2250] R3[151/1200], Temp: 0.9614, Energy: -53.187096-0.000125j
[2025-09-10 06:33:05] [Iter 1203/2250] R3[152/1200], Temp: 0.9609, Energy: -53.227009+0.001836j
[2025-09-10 06:34:05] [Iter 1204/2250] R3[153/1200], Temp: 0.9604, Energy: -53.283074+0.000060j
[2025-09-10 06:35:05] [Iter 1205/2250] R3[154/1200], Temp: 0.9599, Energy: -53.303225-0.001691j
[2025-09-10 06:36:06] [Iter 1206/2250] R3[155/1200], Temp: 0.9594, Energy: -53.292329-0.000923j
[2025-09-10 06:37:06] [Iter 1207/2250] R3[156/1200], Temp: 0.9589, Energy: -53.258080-0.002247j
[2025-09-10 06:38:06] [Iter 1208/2250] R3[157/1200], Temp: 0.9584, Energy: -53.278438-0.001005j
[2025-09-10 06:39:06] [Iter 1209/2250] R3[158/1200], Temp: 0.9578, Energy: -53.283067-0.000403j
[2025-09-10 06:40:07] [Iter 1210/2250] R3[159/1200], Temp: 0.9573, Energy: -53.289175+0.000755j
[2025-09-10 06:41:07] [Iter 1211/2250] R3[160/1200], Temp: 0.9568, Energy: -53.307410-0.000596j
[2025-09-10 06:42:07] [Iter 1212/2250] R3[161/1200], Temp: 0.9562, Energy: -53.320318-0.001067j
[2025-09-10 06:43:08] [Iter 1213/2250] R3[162/1200], Temp: 0.9557, Energy: -53.285880+0.002966j
[2025-09-10 06:44:08] [Iter 1214/2250] R3[163/1200], Temp: 0.9552, Energy: -53.227680+0.000771j
[2025-09-10 06:45:07] [Iter 1215/2250] R3[164/1200], Temp: 0.9546, Energy: -53.279051-0.000218j
[2025-09-10 06:46:08] [Iter 1216/2250] R3[165/1200], Temp: 0.9541, Energy: -53.269510-0.003596j
[2025-09-10 06:47:08] [Iter 1217/2250] R3[166/1200], Temp: 0.9535, Energy: -53.309559-0.000907j
[2025-09-10 06:48:09] [Iter 1218/2250] R3[167/1200], Temp: 0.9530, Energy: -53.290393-0.005854j
[2025-09-10 06:49:09] [Iter 1219/2250] R3[168/1200], Temp: 0.9524, Energy: -53.337540+0.000462j
[2025-09-10 06:50:09] [Iter 1220/2250] R3[169/1200], Temp: 0.9519, Energy: -53.325592-0.003740j
[2025-09-10 06:51:10] [Iter 1221/2250] R3[170/1200], Temp: 0.9513, Energy: -53.320928-0.000657j
[2025-09-10 06:52:10] [Iter 1222/2250] R3[171/1200], Temp: 0.9507, Energy: -53.307433+0.000545j
[2025-09-10 06:53:10] [Iter 1223/2250] R3[172/1200], Temp: 0.9502, Energy: -53.316307+0.000664j
[2025-09-10 06:54:11] [Iter 1224/2250] R3[173/1200], Temp: 0.9496, Energy: -53.268394-0.001759j
[2025-09-10 06:55:11] [Iter 1225/2250] R3[174/1200], Temp: 0.9490, Energy: -53.338005-0.001461j
[2025-09-10 06:56:12] [Iter 1226/2250] R3[175/1200], Temp: 0.9484, Energy: -53.313992-0.001155j
[2025-09-10 06:57:12] [Iter 1227/2250] R3[176/1200], Temp: 0.9479, Energy: -53.268770-0.001757j
[2025-09-10 06:58:12] [Iter 1228/2250] R3[177/1200], Temp: 0.9473, Energy: -53.285572+0.000503j
[2025-09-10 06:59:12] [Iter 1229/2250] R3[178/1200], Temp: 0.9467, Energy: -53.305596+0.001399j
[2025-09-10 07:00:13] [Iter 1230/2250] R3[179/1200], Temp: 0.9461, Energy: -53.243330-0.002264j
[2025-09-10 07:01:13] [Iter 1231/2250] R3[180/1200], Temp: 0.9455, Energy: -53.269519-0.000310j
[2025-09-10 07:02:14] [Iter 1232/2250] R3[181/1200], Temp: 0.9449, Energy: -53.292342-0.000333j
[2025-09-10 07:03:14] [Iter 1233/2250] R3[182/1200], Temp: 0.9443, Energy: -53.246625+0.002689j
[2025-09-10 07:04:04] [Iter 1234/2250] R3[183/1200], Temp: 0.9437, Energy: -53.273085+0.000069j
[2025-09-10 07:04:37] [Iter 1235/2250] R3[184/1200], Temp: 0.9431, Energy: -53.266567-0.000189j
[2025-09-10 07:04:55] [Iter 1236/2250] R3[185/1200], Temp: 0.9425, Energy: -53.251187-0.000838j
[2025-09-10 07:05:15] [Iter 1237/2250] R3[186/1200], Temp: 0.9419, Energy: -53.264853-0.000279j
[2025-09-10 07:05:34] [Iter 1238/2250] R3[187/1200], Temp: 0.9413, Energy: -53.229463+0.000519j
[2025-09-10 07:05:54] [Iter 1239/2250] R3[188/1200], Temp: 0.9407, Energy: -53.275574-0.002271j
[2025-09-10 07:06:22] [Iter 1240/2250] R3[189/1200], Temp: 0.9400, Energy: -53.275647+0.000574j
[2025-09-10 07:06:41] [Iter 1241/2250] R3[190/1200], Temp: 0.9394, Energy: -53.293585-0.001878j
[2025-09-10 07:07:00] [Iter 1242/2250] R3[191/1200], Temp: 0.9388, Energy: -53.305519-0.001351j
[2025-09-10 07:07:20] [Iter 1243/2250] R3[192/1200], Temp: 0.9382, Energy: -53.246124+0.000474j
[2025-09-10 07:07:48] [Iter 1244/2250] R3[193/1200], Temp: 0.9375, Energy: -53.278132-0.000543j
[2025-09-10 07:08:09] [Iter 1245/2250] R3[194/1200], Temp: 0.9369, Energy: -53.364750-0.001065j
[2025-09-10 07:08:35] [Iter 1246/2250] R3[195/1200], Temp: 0.9362, Energy: -53.332933+0.001762j
[2025-09-10 07:08:56] [Iter 1247/2250] R3[196/1200], Temp: 0.9356, Energy: -53.260391+0.001426j
[2025-09-10 07:09:33] [Iter 1248/2250] R3[197/1200], Temp: 0.9350, Energy: -53.285837+0.001898j
[2025-09-10 07:10:33] [Iter 1249/2250] R3[198/1200], Temp: 0.9343, Energy: -53.298481-0.000700j
[2025-09-10 07:11:33] [Iter 1250/2250] R3[199/1200], Temp: 0.9337, Energy: -53.275569-0.001278j
[2025-09-10 07:11:33] ✓ Checkpoint saved: checkpoint_iter_001250.pkl
[2025-09-10 07:12:33] [Iter 1251/2250] R3[200/1200], Temp: 0.9330, Energy: -53.245009-0.000044j
[2025-09-10 07:13:34] [Iter 1252/2250] R3[201/1200], Temp: 0.9324, Energy: -53.245489-0.000619j
[2025-09-10 07:14:34] [Iter 1253/2250] R3[202/1200], Temp: 0.9317, Energy: -53.257850+0.000525j
[2025-09-10 07:15:34] [Iter 1254/2250] R3[203/1200], Temp: 0.9310, Energy: -53.288777+0.000557j
[2025-09-10 07:16:34] [Iter 1255/2250] R3[204/1200], Temp: 0.9304, Energy: -53.271308-0.000147j
[2025-09-10 07:17:35] [Iter 1256/2250] R3[205/1200], Temp: 0.9297, Energy: -53.299023+0.002298j
[2025-09-10 07:18:35] [Iter 1257/2250] R3[206/1200], Temp: 0.9290, Energy: -53.229809-0.001026j
[2025-09-10 07:19:35] [Iter 1258/2250] R3[207/1200], Temp: 0.9284, Energy: -53.266004-0.001227j
[2025-09-10 07:20:36] [Iter 1259/2250] R3[208/1200], Temp: 0.9277, Energy: -53.321217-0.000185j
[2025-09-10 07:21:36] [Iter 1260/2250] R3[209/1200], Temp: 0.9270, Energy: -53.309745-0.002375j
[2025-09-10 07:22:36] [Iter 1261/2250] R3[210/1200], Temp: 0.9263, Energy: -53.258616+0.000287j
[2025-09-10 07:23:36] [Iter 1262/2250] R3[211/1200], Temp: 0.9256, Energy: -53.292091+0.000429j
[2025-09-10 07:24:37] [Iter 1263/2250] R3[212/1200], Temp: 0.9249, Energy: -53.313191-0.000406j
[2025-09-10 07:25:37] [Iter 1264/2250] R3[213/1200], Temp: 0.9243, Energy: -53.247525-0.001105j
[2025-09-10 07:26:37] [Iter 1265/2250] R3[214/1200], Temp: 0.9236, Energy: -53.296353+0.000458j
[2025-09-10 07:27:38] [Iter 1266/2250] R3[215/1200], Temp: 0.9229, Energy: -53.263863+0.001476j
[2025-09-10 07:28:38] [Iter 1267/2250] R3[216/1200], Temp: 0.9222, Energy: -53.265841-0.000467j
[2025-09-10 07:29:38] [Iter 1268/2250] R3[217/1200], Temp: 0.9215, Energy: -53.289900-0.001560j
[2025-09-10 07:30:39] [Iter 1269/2250] R3[218/1200], Temp: 0.9208, Energy: -53.353073-0.000343j
[2025-09-10 07:31:39] [Iter 1270/2250] R3[219/1200], Temp: 0.9200, Energy: -53.256743-0.002025j
[2025-09-10 07:32:39] [Iter 1271/2250] R3[220/1200], Temp: 0.9193, Energy: -53.281494+0.000067j
[2025-09-10 07:33:40] [Iter 1272/2250] R3[221/1200], Temp: 0.9186, Energy: -53.255156-0.000702j
[2025-09-10 07:34:40] [Iter 1273/2250] R3[222/1200], Temp: 0.9179, Energy: -53.208677-0.001259j
[2025-09-10 07:35:40] [Iter 1274/2250] R3[223/1200], Temp: 0.9172, Energy: -53.299147+0.001367j
[2025-09-10 07:36:41] [Iter 1275/2250] R3[224/1200], Temp: 0.9165, Energy: -53.274296-0.001133j
[2025-09-10 07:37:41] [Iter 1276/2250] R3[225/1200], Temp: 0.9157, Energy: -53.270455-0.002583j
[2025-09-10 07:38:41] [Iter 1277/2250] R3[226/1200], Temp: 0.9150, Energy: -53.286973-0.000763j
[2025-09-10 07:39:41] [Iter 1278/2250] R3[227/1200], Temp: 0.9143, Energy: -53.304910+0.001484j
[2025-09-10 07:40:41] [Iter 1279/2250] R3[228/1200], Temp: 0.9135, Energy: -53.347017+0.000295j
[2025-09-10 07:41:41] [Iter 1280/2250] R3[229/1200], Temp: 0.9128, Energy: -53.260829+0.001207j
[2025-09-10 07:42:42] [Iter 1281/2250] R3[230/1200], Temp: 0.9121, Energy: -53.298907+0.002219j
[2025-09-10 07:43:42] [Iter 1282/2250] R3[231/1200], Temp: 0.9113, Energy: -53.264388+0.002257j
[2025-09-10 07:44:42] [Iter 1283/2250] R3[232/1200], Temp: 0.9106, Energy: -53.281136-0.003760j
[2025-09-10 07:45:43] [Iter 1284/2250] R3[233/1200], Temp: 0.9098, Energy: -53.243881+0.003580j
[2025-09-10 07:46:43] [Iter 1285/2250] R3[234/1200], Temp: 0.9091, Energy: -53.278328+0.000703j
[2025-09-10 07:47:43] [Iter 1286/2250] R3[235/1200], Temp: 0.9083, Energy: -53.293994-0.001484j
[2025-09-10 07:48:44] [Iter 1287/2250] R3[236/1200], Temp: 0.9076, Energy: -53.275815-0.002483j
[2025-09-10 07:49:44] [Iter 1288/2250] R3[237/1200], Temp: 0.9068, Energy: -53.291612+0.005782j
[2025-09-10 07:50:44] [Iter 1289/2250] R3[238/1200], Temp: 0.9060, Energy: -53.270960-0.001584j
[2025-09-10 07:51:44] [Iter 1290/2250] R3[239/1200], Temp: 0.9053, Energy: -53.326200-0.001918j
[2025-09-10 07:52:45] [Iter 1291/2250] R3[240/1200], Temp: 0.9045, Energy: -53.257909-0.000746j
[2025-09-10 07:53:45] [Iter 1292/2250] R3[241/1200], Temp: 0.9037, Energy: -53.218720+0.001825j
[2025-09-10 07:54:46] [Iter 1293/2250] R3[242/1200], Temp: 0.9030, Energy: -53.291984-0.001164j
[2025-09-10 07:55:46] [Iter 1294/2250] R3[243/1200], Temp: 0.9022, Energy: -53.246512+0.000937j
[2025-09-10 07:56:46] [Iter 1295/2250] R3[244/1200], Temp: 0.9014, Energy: -53.357439-0.000536j
[2025-09-10 07:57:47] [Iter 1296/2250] R3[245/1200], Temp: 0.9006, Energy: -53.309398+0.000800j
[2025-09-10 07:58:47] [Iter 1297/2250] R3[246/1200], Temp: 0.8998, Energy: -53.276773-0.000620j
[2025-09-10 07:59:47] [Iter 1298/2250] R3[247/1200], Temp: 0.8991, Energy: -53.248925+0.000041j
[2025-09-10 08:00:47] [Iter 1299/2250] R3[248/1200], Temp: 0.8983, Energy: -53.276378+0.000678j
[2025-09-10 08:01:48] [Iter 1300/2250] R3[249/1200], Temp: 0.8975, Energy: -53.221944+0.001882j
[2025-09-10 08:02:48] [Iter 1301/2250] R3[250/1200], Temp: 0.8967, Energy: -53.237067+0.001232j
[2025-09-10 08:03:48] [Iter 1302/2250] R3[251/1200], Temp: 0.8959, Energy: -53.208477-0.000547j
[2025-09-10 08:04:49] [Iter 1303/2250] R3[252/1200], Temp: 0.8951, Energy: -53.270526-0.000911j
[2025-09-10 08:05:49] [Iter 1304/2250] R3[253/1200], Temp: 0.8943, Energy: -53.303660+0.003232j
[2025-09-10 08:06:50] [Iter 1305/2250] R3[254/1200], Temp: 0.8935, Energy: -53.271116+0.000962j
[2025-09-10 08:07:50] [Iter 1306/2250] R3[255/1200], Temp: 0.8927, Energy: -53.256581-0.000623j
[2025-09-10 08:08:51] [Iter 1307/2250] R3[256/1200], Temp: 0.8918, Energy: -53.257323+0.001543j
[2025-09-10 08:09:51] [Iter 1308/2250] R3[257/1200], Temp: 0.8910, Energy: -53.298500+0.000679j
[2025-09-10 08:10:52] [Iter 1309/2250] R3[258/1200], Temp: 0.8902, Energy: -53.260004-0.001544j
[2025-09-10 08:11:52] [Iter 1310/2250] R3[259/1200], Temp: 0.8894, Energy: -53.256178-0.000948j
[2025-09-10 08:12:53] [Iter 1311/2250] R3[260/1200], Temp: 0.8886, Energy: -53.236348-0.002833j
[2025-09-10 08:13:53] [Iter 1312/2250] R3[261/1200], Temp: 0.8877, Energy: -53.253230-0.001933j
[2025-09-10 08:14:53] [Iter 1313/2250] R3[262/1200], Temp: 0.8869, Energy: -53.289174+0.002193j
[2025-09-10 08:15:54] [Iter 1314/2250] R3[263/1200], Temp: 0.8861, Energy: -53.296131+0.003183j
[2025-09-10 08:16:54] [Iter 1315/2250] R3[264/1200], Temp: 0.8853, Energy: -53.283662+0.001649j
[2025-09-10 08:17:54] [Iter 1316/2250] R3[265/1200], Temp: 0.8844, Energy: -53.264336-0.001061j
[2025-09-10 08:18:55] [Iter 1317/2250] R3[266/1200], Temp: 0.8836, Energy: -53.262950-0.000853j
[2025-09-10 08:19:55] [Iter 1318/2250] R3[267/1200], Temp: 0.8827, Energy: -53.304797+0.001148j
[2025-09-10 08:20:55] [Iter 1319/2250] R3[268/1200], Temp: 0.8819, Energy: -53.264827-0.002696j
[2025-09-10 08:21:56] [Iter 1320/2250] R3[269/1200], Temp: 0.8811, Energy: -53.259507+0.002188j
[2025-09-10 08:22:56] [Iter 1321/2250] R3[270/1200], Temp: 0.8802, Energy: -53.318852-0.000051j
[2025-09-10 08:23:56] [Iter 1322/2250] R3[271/1200], Temp: 0.8794, Energy: -53.295633-0.003151j
[2025-09-10 08:24:57] [Iter 1323/2250] R3[272/1200], Temp: 0.8785, Energy: -53.340485+0.001020j
[2025-09-10 08:25:57] [Iter 1324/2250] R3[273/1200], Temp: 0.8776, Energy: -53.305462+0.001018j
[2025-09-10 08:26:58] [Iter 1325/2250] R3[274/1200], Temp: 0.8768, Energy: -53.282992+0.000410j
[2025-09-10 08:27:58] [Iter 1326/2250] R3[275/1200], Temp: 0.8759, Energy: -53.296346+0.000387j
[2025-09-10 08:28:59] [Iter 1327/2250] R3[276/1200], Temp: 0.8751, Energy: -53.305144+0.000375j
[2025-09-10 08:29:59] [Iter 1328/2250] R3[277/1200], Temp: 0.8742, Energy: -53.305858+0.000230j
[2025-09-10 08:31:00] [Iter 1329/2250] R3[278/1200], Temp: 0.8733, Energy: -53.317011-0.001377j
[2025-09-10 08:32:00] [Iter 1330/2250] R3[279/1200], Temp: 0.8724, Energy: -53.329600-0.000687j
[2025-09-10 08:33:01] [Iter 1331/2250] R3[280/1200], Temp: 0.8716, Energy: -53.290261-0.000036j
[2025-09-10 08:34:01] [Iter 1332/2250] R3[281/1200], Temp: 0.8707, Energy: -53.278618-0.001869j
[2025-09-10 08:35:01] [Iter 1333/2250] R3[282/1200], Temp: 0.8698, Energy: -53.251872-0.001697j
[2025-09-10 08:36:02] [Iter 1334/2250] R3[283/1200], Temp: 0.8689, Energy: -53.301478-0.000540j
[2025-09-10 08:37:02] [Iter 1335/2250] R3[284/1200], Temp: 0.8680, Energy: -53.295929+0.000957j
[2025-09-10 08:38:02] [Iter 1336/2250] R3[285/1200], Temp: 0.8672, Energy: -53.250185+0.000208j
[2025-09-10 08:39:03] [Iter 1337/2250] R3[286/1200], Temp: 0.8663, Energy: -53.279463-0.002087j
[2025-09-10 08:40:03] [Iter 1338/2250] R3[287/1200], Temp: 0.8654, Energy: -53.252615+0.000773j
[2025-09-10 08:41:03] [Iter 1339/2250] R3[288/1200], Temp: 0.8645, Energy: -53.279880-0.000829j
[2025-09-10 08:42:04] [Iter 1340/2250] R3[289/1200], Temp: 0.8636, Energy: -53.255969-0.001370j
[2025-09-10 08:43:04] [Iter 1341/2250] R3[290/1200], Temp: 0.8627, Energy: -53.245870-0.000716j
[2025-09-10 08:44:04] [Iter 1342/2250] R3[291/1200], Temp: 0.8618, Energy: -53.271076-0.003070j
[2025-09-10 08:45:05] [Iter 1343/2250] R3[292/1200], Temp: 0.8609, Energy: -53.222564-0.001545j
[2025-09-10 08:46:05] [Iter 1344/2250] R3[293/1200], Temp: 0.8600, Energy: -53.263868+0.002224j
[2025-09-10 08:47:05] [Iter 1345/2250] R3[294/1200], Temp: 0.8591, Energy: -53.263753-0.002284j
[2025-09-10 08:48:05] [Iter 1346/2250] R3[295/1200], Temp: 0.8582, Energy: -53.254973-0.000858j
[2025-09-10 08:49:05] [Iter 1347/2250] R3[296/1200], Temp: 0.8572, Energy: -53.209969-0.000949j
[2025-09-10 08:50:06] [Iter 1348/2250] R3[297/1200], Temp: 0.8563, Energy: -53.192338-0.001419j
[2025-09-10 08:51:06] [Iter 1349/2250] R3[298/1200], Temp: 0.8554, Energy: -53.223860-0.000848j
[2025-09-10 08:52:06] [Iter 1350/2250] R3[299/1200], Temp: 0.8545, Energy: -53.271572-0.000209j
[2025-09-10 08:53:06] [Iter 1351/2250] R3[300/1200], Temp: 0.8536, Energy: -53.257086+0.001876j
[2025-09-10 08:54:06] [Iter 1352/2250] R3[301/1200], Temp: 0.8526, Energy: -53.223287-0.000197j
[2025-09-10 08:55:07] [Iter 1353/2250] R3[302/1200], Temp: 0.8517, Energy: -53.242517-0.000331j
[2025-09-10 08:56:07] [Iter 1354/2250] R3[303/1200], Temp: 0.8508, Energy: -53.210933+0.003899j
[2025-09-10 08:57:07] [Iter 1355/2250] R3[304/1200], Temp: 0.8498, Energy: -53.231365-0.000457j
[2025-09-10 08:58:07] [Iter 1356/2250] R3[305/1200], Temp: 0.8489, Energy: -53.248000-0.000911j
[2025-09-10 08:59:08] [Iter 1357/2250] R3[306/1200], Temp: 0.8480, Energy: -53.180936-0.000354j
[2025-09-10 09:00:08] [Iter 1358/2250] R3[307/1200], Temp: 0.8470, Energy: -53.235334-0.001170j
[2025-09-10 09:01:08] [Iter 1359/2250] R3[308/1200], Temp: 0.8461, Energy: -53.287975-0.000350j
[2025-09-10 09:02:08] [Iter 1360/2250] R3[309/1200], Temp: 0.8451, Energy: -53.249765+0.000419j
[2025-09-10 09:03:08] [Iter 1361/2250] R3[310/1200], Temp: 0.8442, Energy: -53.280713+0.002081j
[2025-09-10 09:04:09] [Iter 1362/2250] R3[311/1200], Temp: 0.8432, Energy: -53.284862+0.000394j
[2025-09-10 09:05:09] [Iter 1363/2250] R3[312/1200], Temp: 0.8423, Energy: -53.324688+0.000467j
[2025-09-10 09:06:10] [Iter 1364/2250] R3[313/1200], Temp: 0.8413, Energy: -53.333588-0.000971j
[2025-09-10 09:07:10] [Iter 1365/2250] R3[314/1200], Temp: 0.8404, Energy: -53.347910-0.000829j
[2025-09-10 09:08:10] [Iter 1366/2250] R3[315/1200], Temp: 0.8394, Energy: -53.328972+0.000909j
[2025-09-10 09:09:11] [Iter 1367/2250] R3[316/1200], Temp: 0.8384, Energy: -53.341342-0.000369j
[2025-09-10 09:10:11] [Iter 1368/2250] R3[317/1200], Temp: 0.8375, Energy: -53.279388+0.000077j
[2025-09-10 09:11:12] [Iter 1369/2250] R3[318/1200], Temp: 0.8365, Energy: -53.270718-0.001438j
[2025-09-10 09:12:12] [Iter 1370/2250] R3[319/1200], Temp: 0.8355, Energy: -53.295686+0.000490j
[2025-09-10 09:13:12] [Iter 1371/2250] R3[320/1200], Temp: 0.8346, Energy: -53.289273-0.000577j
[2025-09-10 09:14:12] [Iter 1372/2250] R3[321/1200], Temp: 0.8336, Energy: -53.284095-0.000769j
[2025-09-10 09:15:13] [Iter 1373/2250] R3[322/1200], Temp: 0.8326, Energy: -53.243514+0.000899j
[2025-09-10 09:16:13] [Iter 1374/2250] R3[323/1200], Temp: 0.8316, Energy: -53.330552-0.002670j
[2025-09-10 09:17:14] [Iter 1375/2250] R3[324/1200], Temp: 0.8307, Energy: -53.296684+0.001858j
[2025-09-10 09:18:14] [Iter 1376/2250] R3[325/1200], Temp: 0.8297, Energy: -53.233945-0.000748j
[2025-09-10 09:19:14] [Iter 1377/2250] R3[326/1200], Temp: 0.8287, Energy: -53.268560+0.001977j
[2025-09-10 09:20:15] [Iter 1378/2250] R3[327/1200], Temp: 0.8277, Energy: -53.240442-0.000025j
[2025-09-10 09:21:15] [Iter 1379/2250] R3[328/1200], Temp: 0.8267, Energy: -53.253528+0.001700j
[2025-09-10 09:22:15] [Iter 1380/2250] R3[329/1200], Temp: 0.8257, Energy: -53.293050-0.001403j
[2025-09-10 09:23:16] [Iter 1381/2250] R3[330/1200], Temp: 0.8247, Energy: -53.279420-0.000239j
[2025-09-10 09:24:16] [Iter 1382/2250] R3[331/1200], Temp: 0.8237, Energy: -53.283766+0.001735j
[2025-09-10 09:25:16] [Iter 1383/2250] R3[332/1200], Temp: 0.8227, Energy: -53.268129+0.002216j
[2025-09-10 09:26:17] [Iter 1384/2250] R3[333/1200], Temp: 0.8217, Energy: -53.320679+0.001168j
[2025-09-10 09:27:17] [Iter 1385/2250] R3[334/1200], Temp: 0.8207, Energy: -53.281597+0.000730j
[2025-09-10 09:28:18] [Iter 1386/2250] R3[335/1200], Temp: 0.8197, Energy: -53.340333-0.001291j
[2025-09-10 09:29:18] [Iter 1387/2250] R3[336/1200], Temp: 0.8187, Energy: -53.367001-0.000548j
[2025-09-10 09:30:18] [Iter 1388/2250] R3[337/1200], Temp: 0.8177, Energy: -53.367636+0.000669j
[2025-09-10 09:31:19] [Iter 1389/2250] R3[338/1200], Temp: 0.8167, Energy: -53.341036+0.000685j
[2025-09-10 09:32:19] [Iter 1390/2250] R3[339/1200], Temp: 0.8157, Energy: -53.317486-0.001424j
[2025-09-10 09:33:20] [Iter 1391/2250] R3[340/1200], Temp: 0.8147, Energy: -53.316065+0.001268j
[2025-09-10 09:34:20] [Iter 1392/2250] R3[341/1200], Temp: 0.8136, Energy: -53.310023-0.001089j
[2025-09-10 09:35:20] [Iter 1393/2250] R3[342/1200], Temp: 0.8126, Energy: -53.262943-0.000637j
[2025-09-10 09:36:21] [Iter 1394/2250] R3[343/1200], Temp: 0.8116, Energy: -53.286623-0.000304j
[2025-09-10 09:37:21] [Iter 1395/2250] R3[344/1200], Temp: 0.8106, Energy: -53.286762+0.000291j
[2025-09-10 09:38:21] [Iter 1396/2250] R3[345/1200], Temp: 0.8095, Energy: -53.295769-0.000064j
[2025-09-10 09:39:21] [Iter 1397/2250] R3[346/1200], Temp: 0.8085, Energy: -53.297126-0.001473j
[2025-09-10 09:40:21] [Iter 1398/2250] R3[347/1200], Temp: 0.8075, Energy: -53.222096-0.001010j
[2025-09-10 09:41:22] [Iter 1399/2250] R3[348/1200], Temp: 0.8065, Energy: -53.229321-0.000502j
[2025-09-10 09:42:22] [Iter 1400/2250] R3[349/1200], Temp: 0.8054, Energy: -53.277878-0.000273j
[2025-09-10 09:43:22] [Iter 1401/2250] R3[350/1200], Temp: 0.8044, Energy: -53.295416+0.003026j
[2025-09-10 09:44:23] [Iter 1402/2250] R3[351/1200], Temp: 0.8033, Energy: -53.208069-0.000252j
[2025-09-10 09:45:23] [Iter 1403/2250] R3[352/1200], Temp: 0.8023, Energy: -53.269631+0.001592j
[2025-09-10 09:46:23] [Iter 1404/2250] R3[353/1200], Temp: 0.8013, Energy: -53.203116+0.000341j
[2025-09-10 09:47:24] [Iter 1405/2250] R3[354/1200], Temp: 0.8002, Energy: -53.257445+0.000207j
[2025-09-10 09:48:24] [Iter 1406/2250] R3[355/1200], Temp: 0.7992, Energy: -53.284085+0.000904j
[2025-09-10 09:49:24] [Iter 1407/2250] R3[356/1200], Temp: 0.7981, Energy: -53.301911-0.001430j
[2025-09-10 09:50:25] [Iter 1408/2250] R3[357/1200], Temp: 0.7971, Energy: -53.367563+0.000402j
[2025-09-10 09:51:25] [Iter 1409/2250] R3[358/1200], Temp: 0.7960, Energy: -53.363171-0.000231j
[2025-09-10 09:52:25] [Iter 1410/2250] R3[359/1200], Temp: 0.7950, Energy: -53.322812-0.001617j
[2025-09-10 09:53:26] [Iter 1411/2250] R3[360/1200], Temp: 0.7939, Energy: -53.367366+0.000589j
[2025-09-10 09:54:26] [Iter 1412/2250] R3[361/1200], Temp: 0.7928, Energy: -53.346603+0.001410j
[2025-09-10 09:55:26] [Iter 1413/2250] R3[362/1200], Temp: 0.7918, Energy: -53.320164+0.001696j
[2025-09-10 09:56:27] [Iter 1414/2250] R3[363/1200], Temp: 0.7907, Energy: -53.335986+0.000082j
[2025-09-10 09:57:28] [Iter 1415/2250] R3[364/1200], Temp: 0.7896, Energy: -53.300267-0.001290j
[2025-09-10 09:58:28] [Iter 1416/2250] R3[365/1200], Temp: 0.7886, Energy: -53.280875+0.001780j
[2025-09-10 09:59:28] [Iter 1417/2250] R3[366/1200], Temp: 0.7875, Energy: -53.325158-0.000002j
[2025-09-10 10:00:29] [Iter 1418/2250] R3[367/1200], Temp: 0.7864, Energy: -53.260399-0.000271j
[2025-09-10 10:01:29] [Iter 1419/2250] R3[368/1200], Temp: 0.7854, Energy: -53.265017+0.001541j
[2025-09-10 10:02:29] [Iter 1420/2250] R3[369/1200], Temp: 0.7843, Energy: -53.338781+0.001081j
[2025-09-10 10:03:30] [Iter 1421/2250] R3[370/1200], Temp: 0.7832, Energy: -53.337080+0.002175j
[2025-09-10 10:04:30] [Iter 1422/2250] R3[371/1200], Temp: 0.7821, Energy: -53.280794-0.001398j
[2025-09-10 10:05:30] [Iter 1423/2250] R3[372/1200], Temp: 0.7810, Energy: -53.286368-0.002037j
[2025-09-10 10:06:31] [Iter 1424/2250] R3[373/1200], Temp: 0.7800, Energy: -53.293730+0.000630j
[2025-09-10 10:07:31] [Iter 1425/2250] R3[374/1200], Temp: 0.7789, Energy: -53.316069+0.001478j
[2025-09-10 10:08:31] [Iter 1426/2250] R3[375/1200], Temp: 0.7778, Energy: -53.276641-0.000658j
[2025-09-10 10:09:31] [Iter 1427/2250] R3[376/1200], Temp: 0.7767, Energy: -53.224423-0.002657j
[2025-09-10 10:10:32] [Iter 1428/2250] R3[377/1200], Temp: 0.7756, Energy: -53.235354+0.001046j
[2025-09-10 10:11:32] [Iter 1429/2250] R3[378/1200], Temp: 0.7745, Energy: -53.322951-0.000551j
[2025-09-10 10:12:33] [Iter 1430/2250] R3[379/1200], Temp: 0.7734, Energy: -53.291397+0.002266j
[2025-09-10 10:13:33] [Iter 1431/2250] R3[380/1200], Temp: 0.7723, Energy: -53.276065-0.000296j
[2025-09-10 10:14:33] [Iter 1432/2250] R3[381/1200], Temp: 0.7712, Energy: -53.297535+0.000259j
[2025-09-10 10:15:33] [Iter 1433/2250] R3[382/1200], Temp: 0.7701, Energy: -53.306198+0.000315j
[2025-09-10 10:16:34] [Iter 1434/2250] R3[383/1200], Temp: 0.7690, Energy: -53.311060-0.000560j
[2025-09-10 10:17:34] [Iter 1435/2250] R3[384/1200], Temp: 0.7679, Energy: -53.303212-0.002117j
[2025-09-10 10:18:35] [Iter 1436/2250] R3[385/1200], Temp: 0.7668, Energy: -53.272494-0.003779j
[2025-09-10 10:19:35] [Iter 1437/2250] R3[386/1200], Temp: 0.7657, Energy: -53.337016-0.001685j
[2025-09-10 10:20:35] [Iter 1438/2250] R3[387/1200], Temp: 0.7646, Energy: -53.294541+0.000040j
[2025-09-10 10:21:35] [Iter 1439/2250] R3[388/1200], Temp: 0.7635, Energy: -53.321051-0.001092j
[2025-09-10 10:22:36] [Iter 1440/2250] R3[389/1200], Temp: 0.7624, Energy: -53.306244-0.001607j
[2025-09-10 10:23:36] [Iter 1441/2250] R3[390/1200], Temp: 0.7612, Energy: -53.299075+0.002374j
[2025-09-10 10:24:37] [Iter 1442/2250] R3[391/1200], Temp: 0.7601, Energy: -53.280919-0.001522j
[2025-09-10 10:25:37] [Iter 1443/2250] R3[392/1200], Temp: 0.7590, Energy: -53.256292+0.000813j
[2025-09-10 10:26:37] [Iter 1444/2250] R3[393/1200], Temp: 0.7579, Energy: -53.255003+0.001072j
[2025-09-10 10:27:38] [Iter 1445/2250] R3[394/1200], Temp: 0.7568, Energy: -53.255775+0.000883j
[2025-09-10 10:28:38] [Iter 1446/2250] R3[395/1200], Temp: 0.7556, Energy: -53.243639+0.001465j
[2025-09-10 10:29:38] [Iter 1447/2250] R3[396/1200], Temp: 0.7545, Energy: -53.309533-0.000594j
[2025-09-10 10:30:39] [Iter 1448/2250] R3[397/1200], Temp: 0.7534, Energy: -53.288585-0.002794j
[2025-09-10 10:31:39] [Iter 1449/2250] R3[398/1200], Temp: 0.7523, Energy: -53.307496+0.001352j
[2025-09-10 10:32:39] [Iter 1450/2250] R3[399/1200], Temp: 0.7511, Energy: -53.246103+0.000023j
[2025-09-10 10:33:40] [Iter 1451/2250] R3[400/1200], Temp: 0.7500, Energy: -53.236297+0.001223j
[2025-09-10 10:34:40] [Iter 1452/2250] R3[401/1200], Temp: 0.7489, Energy: -53.246632-0.001219j
[2025-09-10 10:35:41] [Iter 1453/2250] R3[402/1200], Temp: 0.7477, Energy: -53.313444-0.001543j
[2025-09-10 10:36:41] [Iter 1454/2250] R3[403/1200], Temp: 0.7466, Energy: -53.344497-0.000502j
[2025-09-10 10:37:41] [Iter 1455/2250] R3[404/1200], Temp: 0.7455, Energy: -53.273062+0.000757j
[2025-09-10 10:38:42] [Iter 1456/2250] R3[405/1200], Temp: 0.7443, Energy: -53.242352-0.000465j
[2025-09-10 10:39:42] [Iter 1457/2250] R3[406/1200], Temp: 0.7432, Energy: -53.304099+0.000012j
[2025-09-10 10:40:42] [Iter 1458/2250] R3[407/1200], Temp: 0.7420, Energy: -53.292991+0.002476j
[2025-09-10 10:41:43] [Iter 1459/2250] R3[408/1200], Temp: 0.7409, Energy: -53.336858+0.000859j
[2025-09-10 10:42:43] [Iter 1460/2250] R3[409/1200], Temp: 0.7397, Energy: -53.308396+0.000082j
[2025-09-10 10:43:44] [Iter 1461/2250] R3[410/1200], Temp: 0.7386, Energy: -53.318911+0.000381j
[2025-09-10 10:44:44] [Iter 1462/2250] R3[411/1200], Temp: 0.7374, Energy: -53.278036+0.001137j
[2025-09-10 10:45:45] [Iter 1463/2250] R3[412/1200], Temp: 0.7363, Energy: -53.322653+0.001138j
[2025-09-10 10:46:45] [Iter 1464/2250] R3[413/1200], Temp: 0.7351, Energy: -53.259395+0.001916j
[2025-09-10 10:47:46] [Iter 1465/2250] R3[414/1200], Temp: 0.7340, Energy: -53.263470+0.000562j
[2025-09-10 10:48:46] [Iter 1466/2250] R3[415/1200], Temp: 0.7328, Energy: -53.288816-0.000719j
[2025-09-10 10:49:46] [Iter 1467/2250] R3[416/1200], Temp: 0.7316, Energy: -53.262297+0.002392j
[2025-09-10 10:50:47] [Iter 1468/2250] R3[417/1200], Temp: 0.7305, Energy: -53.257983-0.000155j
[2025-09-10 10:51:47] [Iter 1469/2250] R3[418/1200], Temp: 0.7293, Energy: -53.279821-0.000491j
[2025-09-10 10:52:48] [Iter 1470/2250] R3[419/1200], Temp: 0.7282, Energy: -53.291407-0.001504j
[2025-09-10 10:53:48] [Iter 1471/2250] R3[420/1200], Temp: 0.7270, Energy: -53.278718+0.000061j
[2025-09-10 10:54:48] [Iter 1472/2250] R3[421/1200], Temp: 0.7258, Energy: -53.295583-0.000024j
[2025-09-10 10:55:48] [Iter 1473/2250] R3[422/1200], Temp: 0.7247, Energy: -53.245776-0.000452j
[2025-09-10 10:56:49] [Iter 1474/2250] R3[423/1200], Temp: 0.7235, Energy: -53.249281-0.000757j
[2025-09-10 10:57:49] [Iter 1475/2250] R3[424/1200], Temp: 0.7223, Energy: -53.279944+0.001341j
[2025-09-10 10:58:49] [Iter 1476/2250] R3[425/1200], Temp: 0.7211, Energy: -53.368336-0.001408j
[2025-09-10 10:59:49] [Iter 1477/2250] R3[426/1200], Temp: 0.7200, Energy: -53.306930+0.002470j
[2025-09-10 11:00:49] [Iter 1478/2250] R3[427/1200], Temp: 0.7188, Energy: -53.283416-0.000432j
[2025-09-10 11:01:50] [Iter 1479/2250] R3[428/1200], Temp: 0.7176, Energy: -53.293927+0.001171j
[2025-09-10 11:02:50] [Iter 1480/2250] R3[429/1200], Temp: 0.7164, Energy: -53.325153+0.000895j
[2025-09-10 11:03:50] [Iter 1481/2250] R3[430/1200], Temp: 0.7153, Energy: -53.229279+0.001284j
[2025-09-10 11:04:50] [Iter 1482/2250] R3[431/1200], Temp: 0.7141, Energy: -53.217965+0.000901j
[2025-09-10 11:05:51] [Iter 1483/2250] R3[432/1200], Temp: 0.7129, Energy: -53.185449-0.001578j
[2025-09-10 11:06:51] [Iter 1484/2250] R3[433/1200], Temp: 0.7117, Energy: -53.224573-0.000193j
[2025-09-10 11:07:52] [Iter 1485/2250] R3[434/1200], Temp: 0.7105, Energy: -53.225055-0.002221j
[2025-09-10 11:08:52] [Iter 1486/2250] R3[435/1200], Temp: 0.7093, Energy: -53.327766-0.002921j
[2025-09-10 11:09:52] [Iter 1487/2250] R3[436/1200], Temp: 0.7081, Energy: -53.371448+0.002172j
[2025-09-10 11:10:53] [Iter 1488/2250] R3[437/1200], Temp: 0.7069, Energy: -53.287144-0.000823j
[2025-09-10 11:11:53] [Iter 1489/2250] R3[438/1200], Temp: 0.7058, Energy: -53.316629-0.001256j
[2025-09-10 11:12:53] [Iter 1490/2250] R3[439/1200], Temp: 0.7046, Energy: -53.312737-0.004045j
[2025-09-10 11:13:54] [Iter 1491/2250] R3[440/1200], Temp: 0.7034, Energy: -53.254651-0.000023j
[2025-09-10 11:14:54] [Iter 1492/2250] R3[441/1200], Temp: 0.7022, Energy: -53.306401-0.000644j
[2025-09-10 11:15:55] [Iter 1493/2250] R3[442/1200], Temp: 0.7010, Energy: -53.259756-0.000078j
[2025-09-10 11:16:55] [Iter 1494/2250] R3[443/1200], Temp: 0.6998, Energy: -53.354544-0.000681j
[2025-09-10 11:17:56] [Iter 1495/2250] R3[444/1200], Temp: 0.6986, Energy: -53.259905-0.002966j
[2025-09-10 11:18:56] [Iter 1496/2250] R3[445/1200], Temp: 0.6974, Energy: -53.345979-0.001503j
[2025-09-10 11:19:56] [Iter 1497/2250] R3[446/1200], Temp: 0.6962, Energy: -53.353212+0.000092j
[2025-09-10 11:20:57] [Iter 1498/2250] R3[447/1200], Temp: 0.6950, Energy: -53.310423-0.000292j
[2025-09-10 11:21:57] [Iter 1499/2250] R3[448/1200], Temp: 0.6938, Energy: -53.272724-0.000457j
[2025-09-10 11:22:57] [Iter 1500/2250] R3[449/1200], Temp: 0.6926, Energy: -53.249014+0.003007j
[2025-09-10 11:22:57] ✓ Checkpoint saved: checkpoint_iter_001500.pkl
[2025-09-10 11:23:58] [Iter 1501/2250] R3[450/1200], Temp: 0.6913, Energy: -53.263831-0.004345j
[2025-09-10 11:24:58] [Iter 1502/2250] R3[451/1200], Temp: 0.6901, Energy: -53.235722-0.001097j
[2025-09-10 11:25:58] [Iter 1503/2250] R3[452/1200], Temp: 0.6889, Energy: -53.229636+0.000664j
[2025-09-10 11:26:58] [Iter 1504/2250] R3[453/1200], Temp: 0.6877, Energy: -53.304089-0.000173j
[2025-09-10 11:27:59] [Iter 1505/2250] R3[454/1200], Temp: 0.6865, Energy: -53.331214+0.002692j
[2025-09-10 11:28:59] [Iter 1506/2250] R3[455/1200], Temp: 0.6853, Energy: -53.324576+0.000934j
[2025-09-10 11:29:59] [Iter 1507/2250] R3[456/1200], Temp: 0.6841, Energy: -53.316428+0.000393j
[2025-09-10 11:31:00] [Iter 1508/2250] R3[457/1200], Temp: 0.6828, Energy: -53.283215-0.001061j
[2025-09-10 11:32:00] [Iter 1509/2250] R3[458/1200], Temp: 0.6816, Energy: -53.288757+0.001368j
[2025-09-10 11:33:01] [Iter 1510/2250] R3[459/1200], Temp: 0.6804, Energy: -53.293497-0.000623j
[2025-09-10 11:34:01] [Iter 1511/2250] R3[460/1200], Temp: 0.6792, Energy: -53.245688+0.000983j
[2025-09-10 11:35:01] [Iter 1512/2250] R3[461/1200], Temp: 0.6780, Energy: -53.297835-0.001902j
[2025-09-10 11:36:04] [Iter 1513/2250] R3[462/1200], Temp: 0.6767, Energy: -53.287071-0.001117j
[2025-09-10 11:37:04] [Iter 1514/2250] R3[463/1200], Temp: 0.6755, Energy: -53.281892+0.000326j
[2025-09-10 11:38:04] [Iter 1515/2250] R3[464/1200], Temp: 0.6743, Energy: -53.324411+0.000481j
[2025-09-10 11:39:05] [Iter 1516/2250] R3[465/1200], Temp: 0.6731, Energy: -53.319118-0.000888j
[2025-09-10 11:40:05] [Iter 1517/2250] R3[466/1200], Temp: 0.6718, Energy: -53.366156-0.000463j
[2025-09-10 11:41:05] [Iter 1518/2250] R3[467/1200], Temp: 0.6706, Energy: -53.295083+0.000884j
[2025-09-10 11:42:06] [Iter 1519/2250] R3[468/1200], Temp: 0.6694, Energy: -53.256659-0.000497j
[2025-09-10 11:43:06] [Iter 1520/2250] R3[469/1200], Temp: 0.6681, Energy: -53.307021+0.000522j
[2025-09-10 11:44:06] [Iter 1521/2250] R3[470/1200], Temp: 0.6669, Energy: -53.206007+0.000297j
[2025-09-10 11:45:07] [Iter 1522/2250] R3[471/1200], Temp: 0.6657, Energy: -53.243884+0.000275j
[2025-09-10 11:46:07] [Iter 1523/2250] R3[472/1200], Temp: 0.6644, Energy: -53.256454-0.000168j
[2025-09-10 11:47:07] [Iter 1524/2250] R3[473/1200], Temp: 0.6632, Energy: -53.292699+0.000164j
[2025-09-10 11:48:08] [Iter 1525/2250] R3[474/1200], Temp: 0.6620, Energy: -53.279486+0.001729j
[2025-09-10 11:49:08] [Iter 1526/2250] R3[475/1200], Temp: 0.6607, Energy: -53.308535-0.002651j
[2025-09-10 11:50:08] [Iter 1527/2250] R3[476/1200], Temp: 0.6595, Energy: -53.310403+0.000465j
[2025-09-10 11:51:09] [Iter 1528/2250] R3[477/1200], Temp: 0.6582, Energy: -53.265440-0.000358j
[2025-09-10 11:52:09] [Iter 1529/2250] R3[478/1200], Temp: 0.6570, Energy: -53.290944-0.001171j
[2025-09-10 11:53:10] [Iter 1530/2250] R3[479/1200], Temp: 0.6558, Energy: -53.306374+0.001371j
[2025-09-10 11:54:10] [Iter 1531/2250] R3[480/1200], Temp: 0.6545, Energy: -53.281499+0.001434j
[2025-09-10 11:55:11] [Iter 1532/2250] R3[481/1200], Temp: 0.6533, Energy: -53.253626+0.002386j
[2025-09-10 11:56:11] [Iter 1533/2250] R3[482/1200], Temp: 0.6520, Energy: -53.209852+0.001524j
[2025-09-10 11:57:11] [Iter 1534/2250] R3[483/1200], Temp: 0.6508, Energy: -53.292151+0.002213j
[2025-09-10 11:58:11] [Iter 1535/2250] R3[484/1200], Temp: 0.6495, Energy: -53.301010-0.000715j
[2025-09-10 11:59:12] [Iter 1536/2250] R3[485/1200], Temp: 0.6483, Energy: -53.238140-0.001152j
[2025-09-10 12:00:12] [Iter 1537/2250] R3[486/1200], Temp: 0.6470, Energy: -53.338485-0.000393j
[2025-09-10 12:01:12] [Iter 1538/2250] R3[487/1200], Temp: 0.6458, Energy: -53.302594-0.000831j
[2025-09-10 12:02:13] [Iter 1539/2250] R3[488/1200], Temp: 0.6445, Energy: -53.259569-0.000493j
[2025-09-10 12:03:13] [Iter 1540/2250] R3[489/1200], Temp: 0.6433, Energy: -53.270641-0.000723j
[2025-09-10 12:04:13] [Iter 1541/2250] R3[490/1200], Temp: 0.6420, Energy: -53.271519+0.000864j
[2025-09-10 12:05:14] [Iter 1542/2250] R3[491/1200], Temp: 0.6408, Energy: -53.326425+0.000273j
[2025-09-10 12:06:14] [Iter 1543/2250] R3[492/1200], Temp: 0.6395, Energy: -53.292042-0.000019j
[2025-09-10 12:07:15] [Iter 1544/2250] R3[493/1200], Temp: 0.6382, Energy: -53.276937+0.002060j
[2025-09-10 12:08:15] [Iter 1545/2250] R3[494/1200], Temp: 0.6370, Energy: -53.297824-0.001322j
[2025-09-10 12:09:16] [Iter 1546/2250] R3[495/1200], Temp: 0.6357, Energy: -53.239831+0.000544j
[2025-09-10 12:10:16] [Iter 1547/2250] R3[496/1200], Temp: 0.6345, Energy: -53.300286+0.001027j
[2025-09-10 12:11:16] [Iter 1548/2250] R3[497/1200], Temp: 0.6332, Energy: -53.231050+0.002098j
[2025-09-10 12:12:17] [Iter 1549/2250] R3[498/1200], Temp: 0.6319, Energy: -53.273348+0.001844j
[2025-09-10 12:13:17] [Iter 1550/2250] R3[499/1200], Temp: 0.6307, Energy: -53.300998-0.000763j
[2025-09-10 12:14:17] [Iter 1551/2250] R3[500/1200], Temp: 0.6294, Energy: -53.347932+0.001926j
[2025-09-10 12:15:18] [Iter 1552/2250] R3[501/1200], Temp: 0.6281, Energy: -53.294840-0.000141j
[2025-09-10 12:16:18] [Iter 1553/2250] R3[502/1200], Temp: 0.6269, Energy: -53.289659-0.000209j
[2025-09-10 12:17:19] [Iter 1554/2250] R3[503/1200], Temp: 0.6256, Energy: -53.285626-0.000034j
[2025-09-10 12:18:19] [Iter 1555/2250] R3[504/1200], Temp: 0.6243, Energy: -53.335229+0.001186j
[2025-09-10 12:19:20] [Iter 1556/2250] R3[505/1200], Temp: 0.6231, Energy: -53.290126+0.002856j
[2025-09-10 12:20:20] [Iter 1557/2250] R3[506/1200], Temp: 0.6218, Energy: -53.334612+0.000906j
[2025-09-10 12:21:20] [Iter 1558/2250] R3[507/1200], Temp: 0.6205, Energy: -53.300557-0.000687j
[2025-09-10 12:22:21] [Iter 1559/2250] R3[508/1200], Temp: 0.6193, Energy: -53.293489+0.000842j
[2025-09-10 12:23:21] [Iter 1560/2250] R3[509/1200], Temp: 0.6180, Energy: -53.305710-0.001852j
[2025-09-10 12:24:21] [Iter 1561/2250] R3[510/1200], Temp: 0.6167, Energy: -53.296573+0.002239j
[2025-09-10 12:25:21] [Iter 1562/2250] R3[511/1200], Temp: 0.6154, Energy: -53.327338-0.001186j
[2025-09-10 12:26:22] [Iter 1563/2250] R3[512/1200], Temp: 0.6142, Energy: -53.310757+0.001001j
[2025-09-10 12:27:22] [Iter 1564/2250] R3[513/1200], Temp: 0.6129, Energy: -53.264835+0.000244j
[2025-09-10 12:28:22] [Iter 1565/2250] R3[514/1200], Temp: 0.6116, Energy: -53.267504+0.000553j
[2025-09-10 12:29:22] [Iter 1566/2250] R3[515/1200], Temp: 0.6103, Energy: -53.287713-0.003768j
[2025-09-10 12:30:23] [Iter 1567/2250] R3[516/1200], Temp: 0.6091, Energy: -53.297395+0.001761j
[2025-09-10 12:31:23] [Iter 1568/2250] R3[517/1200], Temp: 0.6078, Energy: -53.294950-0.002429j
[2025-09-10 12:32:23] [Iter 1569/2250] R3[518/1200], Temp: 0.6065, Energy: -53.238198+0.001972j
[2025-09-10 12:33:23] [Iter 1570/2250] R3[519/1200], Temp: 0.6052, Energy: -53.242632-0.000814j
[2025-09-10 12:34:24] [Iter 1571/2250] R3[520/1200], Temp: 0.6040, Energy: -53.201952+0.001159j
[2025-09-10 12:35:24] [Iter 1572/2250] R3[521/1200], Temp: 0.6027, Energy: -53.189052+0.000258j
[2025-09-10 12:36:25] [Iter 1573/2250] R3[522/1200], Temp: 0.6014, Energy: -53.189520+0.001787j
[2025-09-10 12:37:25] [Iter 1574/2250] R3[523/1200], Temp: 0.6001, Energy: -53.267732+0.000883j
[2025-09-10 12:38:25] [Iter 1575/2250] R3[524/1200], Temp: 0.5988, Energy: -53.229286-0.001284j
[2025-09-10 12:39:26] [Iter 1576/2250] R3[525/1200], Temp: 0.5975, Energy: -53.283810+0.000477j
[2025-09-10 12:40:26] [Iter 1577/2250] R3[526/1200], Temp: 0.5963, Energy: -53.299564-0.002499j
[2025-09-10 12:41:27] [Iter 1578/2250] R3[527/1200], Temp: 0.5950, Energy: -53.295791+0.000700j
[2025-09-10 12:42:27] [Iter 1579/2250] R3[528/1200], Temp: 0.5937, Energy: -53.298527-0.000634j
[2025-09-10 12:43:27] [Iter 1580/2250] R3[529/1200], Temp: 0.5924, Energy: -53.287992-0.000918j
[2025-09-10 12:44:28] [Iter 1581/2250] R3[530/1200], Temp: 0.5911, Energy: -53.236310-0.000470j
[2025-09-10 12:45:28] [Iter 1582/2250] R3[531/1200], Temp: 0.5898, Energy: -53.288251-0.000926j
[2025-09-10 12:46:28] [Iter 1583/2250] R3[532/1200], Temp: 0.5885, Energy: -53.236491-0.000619j
[2025-09-10 12:47:29] [Iter 1584/2250] R3[533/1200], Temp: 0.5873, Energy: -53.276291+0.002633j
[2025-09-10 12:48:29] [Iter 1585/2250] R3[534/1200], Temp: 0.5860, Energy: -53.280190+0.000580j
[2025-09-10 12:49:30] [Iter 1586/2250] R3[535/1200], Temp: 0.5847, Energy: -53.300967+0.000608j
[2025-09-10 12:50:30] [Iter 1587/2250] R3[536/1200], Temp: 0.5834, Energy: -53.278433+0.000245j
[2025-09-10 12:51:30] [Iter 1588/2250] R3[537/1200], Temp: 0.5821, Energy: -53.245336-0.001567j
[2025-09-10 12:52:31] [Iter 1589/2250] R3[538/1200], Temp: 0.5808, Energy: -53.304350-0.000314j
[2025-09-10 12:53:31] [Iter 1590/2250] R3[539/1200], Temp: 0.5795, Energy: -53.295554-0.001381j
[2025-09-10 12:54:31] [Iter 1591/2250] R3[540/1200], Temp: 0.5782, Energy: -53.232810+0.000723j
[2025-09-10 12:55:32] [Iter 1592/2250] R3[541/1200], Temp: 0.5769, Energy: -53.217922-0.001041j
[2025-09-10 12:56:32] [Iter 1593/2250] R3[542/1200], Temp: 0.5756, Energy: -53.253096+0.000061j
[2025-09-10 12:57:32] [Iter 1594/2250] R3[543/1200], Temp: 0.5743, Energy: -53.307261+0.001686j
[2025-09-10 12:58:33] [Iter 1595/2250] R3[544/1200], Temp: 0.5730, Energy: -53.243517-0.000162j
[2025-09-10 12:59:33] [Iter 1596/2250] R3[545/1200], Temp: 0.5717, Energy: -53.254270-0.000608j
[2025-09-10 13:00:33] [Iter 1597/2250] R3[546/1200], Temp: 0.5705, Energy: -53.284333-0.002091j
[2025-09-10 13:01:34] [Iter 1598/2250] R3[547/1200], Temp: 0.5692, Energy: -53.274170-0.000502j
[2025-09-10 13:02:34] [Iter 1599/2250] R3[548/1200], Temp: 0.5679, Energy: -53.374984+0.000705j
[2025-09-10 13:03:34] [Iter 1600/2250] R3[549/1200], Temp: 0.5666, Energy: -53.375616+0.001122j
[2025-09-10 13:04:34] [Iter 1601/2250] R3[550/1200], Temp: 0.5653, Energy: -53.314436-0.000312j
[2025-09-10 13:05:35] [Iter 1602/2250] R3[551/1200], Temp: 0.5640, Energy: -53.306494-0.000655j
[2025-09-10 13:06:35] [Iter 1603/2250] R3[552/1200], Temp: 0.5627, Energy: -53.359009+0.002332j
[2025-09-10 13:07:35] [Iter 1604/2250] R3[553/1200], Temp: 0.5614, Energy: -53.357337+0.002383j
[2025-09-10 13:08:35] [Iter 1605/2250] R3[554/1200], Temp: 0.5601, Energy: -53.371402+0.000347j
[2025-09-10 13:09:36] [Iter 1606/2250] R3[555/1200], Temp: 0.5588, Energy: -53.374145+0.000498j
[2025-09-10 13:10:36] [Iter 1607/2250] R3[556/1200], Temp: 0.5575, Energy: -53.328414+0.001801j
[2025-09-10 13:11:36] [Iter 1608/2250] R3[557/1200], Temp: 0.5562, Energy: -53.339846+0.001896j
[2025-09-10 13:12:36] [Iter 1609/2250] R3[558/1200], Temp: 0.5549, Energy: -53.280785+0.000231j
[2025-09-10 13:13:37] [Iter 1610/2250] R3[559/1200], Temp: 0.5536, Energy: -53.265868+0.000687j
[2025-09-10 13:14:37] [Iter 1611/2250] R3[560/1200], Temp: 0.5523, Energy: -53.243915-0.000793j
[2025-09-10 13:15:38] [Iter 1612/2250] R3[561/1200], Temp: 0.5510, Energy: -53.347064+0.000406j
[2025-09-10 13:16:38] [Iter 1613/2250] R3[562/1200], Temp: 0.5497, Energy: -53.338795+0.001039j
[2025-09-10 13:17:38] [Iter 1614/2250] R3[563/1200], Temp: 0.5484, Energy: -53.340392-0.000449j
[2025-09-10 13:18:39] [Iter 1615/2250] R3[564/1200], Temp: 0.5471, Energy: -53.322267-0.001795j
[2025-09-10 13:19:39] [Iter 1616/2250] R3[565/1200], Temp: 0.5458, Energy: -53.286074+0.002002j
[2025-09-10 13:20:40] [Iter 1617/2250] R3[566/1200], Temp: 0.5444, Energy: -53.337863+0.000797j
[2025-09-10 13:21:40] [Iter 1618/2250] R3[567/1200], Temp: 0.5431, Energy: -53.305562-0.001741j
[2025-09-10 13:22:41] [Iter 1619/2250] R3[568/1200], Temp: 0.5418, Energy: -53.336649-0.003288j
[2025-09-10 13:23:41] [Iter 1620/2250] R3[569/1200], Temp: 0.5405, Energy: -53.348785-0.000676j
[2025-09-10 13:24:42] [Iter 1621/2250] R3[570/1200], Temp: 0.5392, Energy: -53.234850+0.000453j
[2025-09-10 13:25:42] [Iter 1622/2250] R3[571/1200], Temp: 0.5379, Energy: -53.299464+0.000524j
[2025-09-10 13:26:43] [Iter 1623/2250] R3[572/1200], Temp: 0.5366, Energy: -53.341065+0.002264j
[2025-09-10 13:27:43] [Iter 1624/2250] R3[573/1200], Temp: 0.5353, Energy: -53.276137-0.000140j
[2025-09-10 13:28:44] [Iter 1625/2250] R3[574/1200], Temp: 0.5340, Energy: -53.238754+0.000724j
[2025-09-10 13:29:44] [Iter 1626/2250] R3[575/1200], Temp: 0.5327, Energy: -53.266534+0.000207j
[2025-09-10 13:30:44] [Iter 1627/2250] R3[576/1200], Temp: 0.5314, Energy: -53.276498+0.002877j
[2025-09-10 13:31:45] [Iter 1628/2250] R3[577/1200], Temp: 0.5301, Energy: -53.376900-0.001733j
[2025-09-10 13:32:45] [Iter 1629/2250] R3[578/1200], Temp: 0.5288, Energy: -53.396429+0.002218j
[2025-09-10 13:33:46] [Iter 1630/2250] R3[579/1200], Temp: 0.5275, Energy: -53.313018-0.003042j
[2025-09-10 13:34:46] [Iter 1631/2250] R3[580/1200], Temp: 0.5262, Energy: -53.384455+0.000258j
[2025-09-10 13:35:46] [Iter 1632/2250] R3[581/1200], Temp: 0.5249, Energy: -53.356540+0.000416j
[2025-09-10 13:36:47] [Iter 1633/2250] R3[582/1200], Temp: 0.5236, Energy: -53.284231-0.001101j
[2025-09-10 13:37:47] [Iter 1634/2250] R3[583/1200], Temp: 0.5222, Energy: -53.335349+0.001470j
[2025-09-10 13:38:48] [Iter 1635/2250] R3[584/1200], Temp: 0.5209, Energy: -53.278414+0.001599j
[2025-09-10 13:39:48] [Iter 1636/2250] R3[585/1200], Temp: 0.5196, Energy: -53.279574-0.000169j
[2025-09-10 13:40:48] [Iter 1637/2250] R3[586/1200], Temp: 0.5183, Energy: -53.271777+0.000104j
[2025-09-10 13:41:49] [Iter 1638/2250] R3[587/1200], Temp: 0.5170, Energy: -53.264705-0.002736j
[2025-09-10 13:42:49] [Iter 1639/2250] R3[588/1200], Temp: 0.5157, Energy: -53.263933-0.000674j
[2025-09-10 13:43:50] [Iter 1640/2250] R3[589/1200], Temp: 0.5144, Energy: -53.260195-0.001637j
[2025-09-10 13:44:50] [Iter 1641/2250] R3[590/1200], Temp: 0.5131, Energy: -53.269931-0.000036j
[2025-09-10 13:45:50] [Iter 1642/2250] R3[591/1200], Temp: 0.5118, Energy: -53.283795-0.003955j
[2025-09-10 13:46:51] [Iter 1643/2250] R3[592/1200], Temp: 0.5105, Energy: -53.340062+0.001773j
[2025-09-10 13:47:51] [Iter 1644/2250] R3[593/1200], Temp: 0.5092, Energy: -53.307245+0.001172j
[2025-09-10 13:48:52] [Iter 1645/2250] R3[594/1200], Temp: 0.5079, Energy: -53.247987+0.001382j
[2025-09-10 13:49:52] [Iter 1646/2250] R3[595/1200], Temp: 0.5065, Energy: -53.310977+0.000600j
[2025-09-10 13:50:53] [Iter 1647/2250] R3[596/1200], Temp: 0.5052, Energy: -53.359122+0.000157j
[2025-09-10 13:51:53] [Iter 1648/2250] R3[597/1200], Temp: 0.5039, Energy: -53.337516+0.001929j
[2025-09-10 13:52:53] [Iter 1649/2250] R3[598/1200], Temp: 0.5026, Energy: -53.320067+0.000488j
[2025-09-10 13:53:54] [Iter 1650/2250] R3[599/1200], Temp: 0.5013, Energy: -53.281040+0.000059j
[2025-09-10 13:54:54] [Iter 1651/2250] R3[600/1200], Temp: 0.5000, Energy: -53.241377-0.001729j
[2025-09-10 13:55:55] [Iter 1652/2250] R3[601/1200], Temp: 0.4987, Energy: -53.215477-0.001848j
[2025-09-10 13:56:55] [Iter 1653/2250] R3[602/1200], Temp: 0.4974, Energy: -53.238810-0.002037j
[2025-09-10 13:57:55] [Iter 1654/2250] R3[603/1200], Temp: 0.4961, Energy: -53.280159+0.000714j
[2025-09-10 13:58:56] [Iter 1655/2250] R3[604/1200], Temp: 0.4948, Energy: -53.254250-0.000928j
[2025-09-10 13:59:56] [Iter 1656/2250] R3[605/1200], Temp: 0.4935, Energy: -53.330214-0.000907j
[2025-09-10 14:00:56] [Iter 1657/2250] R3[606/1200], Temp: 0.4921, Energy: -53.335841-0.001187j
[2025-09-10 14:01:57] [Iter 1658/2250] R3[607/1200], Temp: 0.4908, Energy: -53.300809-0.000708j
[2025-09-10 14:02:57] [Iter 1659/2250] R3[608/1200], Temp: 0.4895, Energy: -53.239419+0.001919j
[2025-09-10 14:03:58] [Iter 1660/2250] R3[609/1200], Temp: 0.4882, Energy: -53.242405-0.001298j
[2025-09-10 14:04:58] [Iter 1661/2250] R3[610/1200], Temp: 0.4869, Energy: -53.223158+0.000424j
[2025-09-10 14:05:58] [Iter 1662/2250] R3[611/1200], Temp: 0.4856, Energy: -53.282054-0.000660j
[2025-09-10 14:06:59] [Iter 1663/2250] R3[612/1200], Temp: 0.4843, Energy: -53.265119+0.000755j
[2025-09-10 14:07:59] [Iter 1664/2250] R3[613/1200], Temp: 0.4830, Energy: -53.239033+0.001905j
[2025-09-10 14:09:00] [Iter 1665/2250] R3[614/1200], Temp: 0.4817, Energy: -53.297256-0.000849j
[2025-09-10 14:10:00] [Iter 1666/2250] R3[615/1200], Temp: 0.4804, Energy: -53.295489-0.000637j
[2025-09-10 14:11:01] [Iter 1667/2250] R3[616/1200], Temp: 0.4791, Energy: -53.269677-0.000372j
[2025-09-10 14:12:01] [Iter 1668/2250] R3[617/1200], Temp: 0.4778, Energy: -53.300882+0.001582j
[2025-09-10 14:13:01] [Iter 1669/2250] R3[618/1200], Temp: 0.4764, Energy: -53.258723+0.002672j
[2025-09-10 14:14:02] [Iter 1670/2250] R3[619/1200], Temp: 0.4751, Energy: -53.316526-0.005093j
[2025-09-10 14:15:02] [Iter 1671/2250] R3[620/1200], Temp: 0.4738, Energy: -53.283515-0.001268j
[2025-09-10 14:16:03] [Iter 1672/2250] R3[621/1200], Temp: 0.4725, Energy: -53.301683-0.003981j
[2025-09-10 14:17:03] [Iter 1673/2250] R3[622/1200], Temp: 0.4712, Energy: -53.358614+0.000257j
[2025-09-10 14:18:03] [Iter 1674/2250] R3[623/1200], Temp: 0.4699, Energy: -53.260117-0.000244j
[2025-09-10 14:19:04] [Iter 1675/2250] R3[624/1200], Temp: 0.4686, Energy: -53.239918+0.000837j
[2025-09-10 14:20:04] [Iter 1676/2250] R3[625/1200], Temp: 0.4673, Energy: -53.314854-0.001168j
[2025-09-10 14:21:04] [Iter 1677/2250] R3[626/1200], Temp: 0.4660, Energy: -53.264490-0.000376j
[2025-09-10 14:22:05] [Iter 1678/2250] R3[627/1200], Temp: 0.4647, Energy: -53.331633+0.000769j
[2025-09-10 14:23:05] [Iter 1679/2250] R3[628/1200], Temp: 0.4634, Energy: -53.400222-0.001326j
[2025-09-10 14:24:05] [Iter 1680/2250] R3[629/1200], Temp: 0.4621, Energy: -53.314373-0.001963j
[2025-09-10 14:25:05] [Iter 1681/2250] R3[630/1200], Temp: 0.4608, Energy: -53.320127-0.000511j
[2025-09-10 14:26:06] [Iter 1682/2250] R3[631/1200], Temp: 0.4595, Energy: -53.296827-0.003695j
[2025-09-10 14:27:06] [Iter 1683/2250] R3[632/1200], Temp: 0.4582, Energy: -53.302149+0.001396j
[2025-09-10 14:28:06] [Iter 1684/2250] R3[633/1200], Temp: 0.4569, Energy: -53.365129+0.000522j
[2025-09-10 14:29:06] [Iter 1685/2250] R3[634/1200], Temp: 0.4556, Energy: -53.271548+0.001483j
[2025-09-10 14:30:07] [Iter 1686/2250] R3[635/1200], Temp: 0.4542, Energy: -53.239135-0.000369j
[2025-09-10 14:31:07] [Iter 1687/2250] R3[636/1200], Temp: 0.4529, Energy: -53.256783+0.000110j
[2025-09-10 14:32:07] [Iter 1688/2250] R3[637/1200], Temp: 0.4516, Energy: -53.270345-0.002426j
[2025-09-10 14:33:08] [Iter 1689/2250] R3[638/1200], Temp: 0.4503, Energy: -53.252217+0.001240j
[2025-09-10 14:34:08] [Iter 1690/2250] R3[639/1200], Temp: 0.4490, Energy: -53.349682-0.000796j
[2025-09-10 14:35:08] [Iter 1691/2250] R3[640/1200], Temp: 0.4477, Energy: -53.363950+0.001720j
[2025-09-10 14:36:09] [Iter 1692/2250] R3[641/1200], Temp: 0.4464, Energy: -53.355082-0.000665j
[2025-09-10 14:37:09] [Iter 1693/2250] R3[642/1200], Temp: 0.4451, Energy: -53.316598+0.001007j
[2025-09-10 14:38:09] [Iter 1694/2250] R3[643/1200], Temp: 0.4438, Energy: -53.271667+0.000264j
[2025-09-10 14:39:10] [Iter 1695/2250] R3[644/1200], Temp: 0.4425, Energy: -53.339867+0.000172j
[2025-09-10 14:40:10] [Iter 1696/2250] R3[645/1200], Temp: 0.4412, Energy: -53.300196-0.000888j
[2025-09-10 14:41:10] [Iter 1697/2250] R3[646/1200], Temp: 0.4399, Energy: -53.310906-0.001146j
[2025-09-10 14:42:11] [Iter 1698/2250] R3[647/1200], Temp: 0.4386, Energy: -53.336004-0.000713j
[2025-09-10 14:43:11] [Iter 1699/2250] R3[648/1200], Temp: 0.4373, Energy: -53.260870-0.000757j
[2025-09-10 14:44:12] [Iter 1700/2250] R3[649/1200], Temp: 0.4360, Energy: -53.321748+0.000433j
[2025-09-10 14:45:12] [Iter 1701/2250] R3[650/1200], Temp: 0.4347, Energy: -53.291963-0.000140j
[2025-09-10 14:46:13] [Iter 1702/2250] R3[651/1200], Temp: 0.4334, Energy: -53.337521+0.000046j
[2025-09-10 14:47:13] [Iter 1703/2250] R3[652/1200], Temp: 0.4321, Energy: -53.295348-0.001038j
[2025-09-10 14:48:14] [Iter 1704/2250] R3[653/1200], Temp: 0.4308, Energy: -53.323512-0.000960j
[2025-09-10 14:49:14] [Iter 1705/2250] R3[654/1200], Temp: 0.4295, Energy: -53.349490-0.002010j
[2025-09-10 14:50:14] [Iter 1706/2250] R3[655/1200], Temp: 0.4283, Energy: -53.361290-0.000828j
[2025-09-10 14:51:14] [Iter 1707/2250] R3[656/1200], Temp: 0.4270, Energy: -53.319394-0.003426j
[2025-09-10 14:52:15] [Iter 1708/2250] R3[657/1200], Temp: 0.4257, Energy: -53.299253-0.000712j
[2025-09-10 14:53:15] [Iter 1709/2250] R3[658/1200], Temp: 0.4244, Energy: -53.281072-0.000245j
[2025-09-10 14:54:15] [Iter 1710/2250] R3[659/1200], Temp: 0.4231, Energy: -53.252794+0.000758j
[2025-09-10 14:55:15] [Iter 1711/2250] R3[660/1200], Temp: 0.4218, Energy: -53.238581-0.000248j
[2025-09-10 14:56:16] [Iter 1712/2250] R3[661/1200], Temp: 0.4205, Energy: -53.268758-0.000802j
[2025-09-10 14:57:16] [Iter 1713/2250] R3[662/1200], Temp: 0.4192, Energy: -53.194827+0.002488j
[2025-09-10 14:58:17] [Iter 1714/2250] R3[663/1200], Temp: 0.4179, Energy: -53.206880-0.001303j
[2025-09-10 14:59:17] [Iter 1715/2250] R3[664/1200], Temp: 0.4166, Energy: -53.246154-0.000194j
[2025-09-10 15:00:17] [Iter 1716/2250] R3[665/1200], Temp: 0.4153, Energy: -53.271924-0.002153j
[2025-09-10 15:01:18] [Iter 1717/2250] R3[666/1200], Temp: 0.4140, Energy: -53.287386+0.001313j
[2025-09-10 15:02:18] [Iter 1718/2250] R3[667/1200], Temp: 0.4127, Energy: -53.261145+0.004721j
[2025-09-10 15:03:19] [Iter 1719/2250] R3[668/1200], Temp: 0.4115, Energy: -53.271799-0.001057j
[2025-09-10 15:04:19] [Iter 1720/2250] R3[669/1200], Temp: 0.4102, Energy: -53.261916-0.000069j
[2025-09-10 15:05:19] [Iter 1721/2250] R3[670/1200], Temp: 0.4089, Energy: -53.291903-0.000549j
[2025-09-10 15:06:20] [Iter 1722/2250] R3[671/1200], Temp: 0.4076, Energy: -53.298315+0.000602j
[2025-09-10 15:07:20] [Iter 1723/2250] R3[672/1200], Temp: 0.4063, Energy: -53.297035-0.000297j
[2025-09-10 15:08:21] [Iter 1724/2250] R3[673/1200], Temp: 0.4050, Energy: -53.252208-0.001171j
[2025-09-10 15:09:21] [Iter 1725/2250] R3[674/1200], Temp: 0.4037, Energy: -53.241249+0.001923j
[2025-09-10 15:10:21] [Iter 1726/2250] R3[675/1200], Temp: 0.4025, Energy: -53.231586-0.001722j
[2025-09-10 15:11:22] [Iter 1727/2250] R3[676/1200], Temp: 0.4012, Energy: -53.226752+0.001627j
[2025-09-10 15:12:22] [Iter 1728/2250] R3[677/1200], Temp: 0.3999, Energy: -53.255963+0.001540j
[2025-09-10 15:13:22] [Iter 1729/2250] R3[678/1200], Temp: 0.3986, Energy: -53.200229+0.000215j
[2025-09-10 15:14:23] [Iter 1730/2250] R3[679/1200], Temp: 0.3973, Energy: -53.274509-0.000214j
[2025-09-10 15:15:23] [Iter 1731/2250] R3[680/1200], Temp: 0.3960, Energy: -53.273182+0.000942j
[2025-09-10 15:16:23] [Iter 1732/2250] R3[681/1200], Temp: 0.3948, Energy: -53.258493+0.001107j
[2025-09-10 15:17:24] [Iter 1733/2250] R3[682/1200], Temp: 0.3935, Energy: -53.273483+0.001065j
[2025-09-10 15:18:24] [Iter 1734/2250] R3[683/1200], Temp: 0.3922, Energy: -53.309583+0.000563j
[2025-09-10 15:19:25] [Iter 1735/2250] R3[684/1200], Temp: 0.3909, Energy: -53.299874+0.000914j
[2025-09-10 15:20:25] [Iter 1736/2250] R3[685/1200], Temp: 0.3897, Energy: -53.283205-0.000966j
[2025-09-10 15:21:26] [Iter 1737/2250] R3[686/1200], Temp: 0.3884, Energy: -53.242288+0.001305j
[2025-09-10 15:22:26] [Iter 1738/2250] R3[687/1200], Temp: 0.3871, Energy: -53.285843+0.000262j
[2025-09-10 15:23:26] [Iter 1739/2250] R3[688/1200], Temp: 0.3858, Energy: -53.271477-0.001748j
[2025-09-10 15:24:26] [Iter 1740/2250] R3[689/1200], Temp: 0.3846, Energy: -53.304005-0.000192j
[2025-09-10 15:25:27] [Iter 1741/2250] R3[690/1200], Temp: 0.3833, Energy: -53.332248-0.000190j
[2025-09-10 15:26:27] [Iter 1742/2250] R3[691/1200], Temp: 0.3820, Energy: -53.312654-0.000669j
[2025-09-10 15:27:27] [Iter 1743/2250] R3[692/1200], Temp: 0.3807, Energy: -53.278972+0.001940j
[2025-09-10 15:28:28] [Iter 1744/2250] R3[693/1200], Temp: 0.3795, Energy: -53.282230+0.000727j
[2025-09-10 15:29:28] [Iter 1745/2250] R3[694/1200], Temp: 0.3782, Energy: -53.316178-0.001138j
[2025-09-10 15:30:28] [Iter 1746/2250] R3[695/1200], Temp: 0.3769, Energy: -53.301401+0.003673j
[2025-09-10 15:31:29] [Iter 1747/2250] R3[696/1200], Temp: 0.3757, Energy: -53.332002-0.001242j
[2025-09-10 15:32:29] [Iter 1748/2250] R3[697/1200], Temp: 0.3744, Energy: -53.254778+0.001216j
[2025-09-10 15:33:29] [Iter 1749/2250] R3[698/1200], Temp: 0.3731, Energy: -53.320215-0.001093j
[2025-09-10 15:34:29] [Iter 1750/2250] R3[699/1200], Temp: 0.3719, Energy: -53.244679+0.000943j
[2025-09-10 15:34:29] ✓ Checkpoint saved: checkpoint_iter_001750.pkl
[2025-09-10 15:35:29] [Iter 1751/2250] R3[700/1200], Temp: 0.3706, Energy: -53.300787-0.001278j
[2025-09-10 15:36:30] [Iter 1752/2250] R3[701/1200], Temp: 0.3693, Energy: -53.294748-0.000204j
[2025-09-10 15:37:30] [Iter 1753/2250] R3[702/1200], Temp: 0.3681, Energy: -53.209973-0.001650j
[2025-09-10 15:38:30] [Iter 1754/2250] R3[703/1200], Temp: 0.3668, Energy: -53.244193-0.001608j
[2025-09-10 15:39:30] [Iter 1755/2250] R3[704/1200], Temp: 0.3655, Energy: -53.329284+0.000502j
[2025-09-10 15:40:31] [Iter 1756/2250] R3[705/1200], Temp: 0.3643, Energy: -53.278352+0.000139j
[2025-09-10 15:41:31] [Iter 1757/2250] R3[706/1200], Temp: 0.3630, Energy: -53.370687-0.000574j
[2025-09-10 15:42:31] [Iter 1758/2250] R3[707/1200], Temp: 0.3618, Energy: -53.376385+0.000357j
[2025-09-10 15:43:32] [Iter 1759/2250] R3[708/1200], Temp: 0.3605, Energy: -53.320966-0.000382j
[2025-09-10 15:44:32] [Iter 1760/2250] R3[709/1200], Temp: 0.3592, Energy: -53.281205-0.000877j
[2025-09-10 15:45:32] [Iter 1761/2250] R3[710/1200], Temp: 0.3580, Energy: -53.288539+0.001797j
[2025-09-10 15:46:32] [Iter 1762/2250] R3[711/1200], Temp: 0.3567, Energy: -53.254738+0.000144j
[2025-09-10 15:47:33] [Iter 1763/2250] R3[712/1200], Temp: 0.3555, Energy: -53.241659+0.003307j
[2025-09-10 15:48:33] [Iter 1764/2250] R3[713/1200], Temp: 0.3542, Energy: -53.320771+0.000499j
[2025-09-10 15:49:33] [Iter 1765/2250] R3[714/1200], Temp: 0.3530, Energy: -53.312163+0.003140j
[2025-09-10 15:50:33] [Iter 1766/2250] R3[715/1200], Temp: 0.3517, Energy: -53.255737+0.000509j
[2025-09-10 15:51:34] [Iter 1767/2250] R3[716/1200], Temp: 0.3505, Energy: -53.271124+0.002283j
[2025-09-10 15:52:34] [Iter 1768/2250] R3[717/1200], Temp: 0.3492, Energy: -53.241598-0.000112j
[2025-09-10 15:53:34] [Iter 1769/2250] R3[718/1200], Temp: 0.3480, Energy: -53.252036+0.000538j
[2025-09-10 15:54:35] [Iter 1770/2250] R3[719/1200], Temp: 0.3467, Energy: -53.255110-0.000145j
[2025-09-10 15:55:35] [Iter 1771/2250] R3[720/1200], Temp: 0.3455, Energy: -53.148297-0.001166j
[2025-09-10 15:56:35] [Iter 1772/2250] R3[721/1200], Temp: 0.3442, Energy: -53.224236-0.000813j
[2025-09-10 15:57:36] [Iter 1773/2250] R3[722/1200], Temp: 0.3430, Energy: -53.257346-0.001123j
[2025-09-10 15:58:36] [Iter 1774/2250] R3[723/1200], Temp: 0.3418, Energy: -53.204973+0.000504j
[2025-09-10 15:59:36] [Iter 1775/2250] R3[724/1200], Temp: 0.3405, Energy: -53.258857+0.001174j
[2025-09-10 16:00:36] [Iter 1776/2250] R3[725/1200], Temp: 0.3393, Energy: -53.221839+0.002628j
[2025-09-10 16:01:36] [Iter 1777/2250] R3[726/1200], Temp: 0.3380, Energy: -53.288842-0.001993j
[2025-09-10 16:02:37] [Iter 1778/2250] R3[727/1200], Temp: 0.3368, Energy: -53.295945+0.000057j
[2025-09-10 16:03:37] [Iter 1779/2250] R3[728/1200], Temp: 0.3356, Energy: -53.303785+0.000983j
[2025-09-10 16:04:37] [Iter 1780/2250] R3[729/1200], Temp: 0.3343, Energy: -53.323713-0.000772j
[2025-09-10 16:05:37] [Iter 1781/2250] R3[730/1200], Temp: 0.3331, Energy: -53.274490+0.000166j
[2025-09-10 16:06:37] [Iter 1782/2250] R3[731/1200], Temp: 0.3319, Energy: -53.265798+0.001859j
[2025-09-10 16:07:38] [Iter 1783/2250] R3[732/1200], Temp: 0.3306, Energy: -53.299347-0.000090j
[2025-09-10 16:08:38] [Iter 1784/2250] R3[733/1200], Temp: 0.3294, Energy: -53.273430+0.001650j
[2025-09-10 16:09:38] [Iter 1785/2250] R3[734/1200], Temp: 0.3282, Energy: -53.343188+0.000559j
[2025-09-10 16:10:38] [Iter 1786/2250] R3[735/1200], Temp: 0.3269, Energy: -53.339775+0.000159j
[2025-09-10 16:11:39] [Iter 1787/2250] R3[736/1200], Temp: 0.3257, Energy: -53.346900+0.000959j
[2025-09-10 16:12:39] [Iter 1788/2250] R3[737/1200], Temp: 0.3245, Energy: -53.317943-0.001094j
[2025-09-10 16:13:39] [Iter 1789/2250] R3[738/1200], Temp: 0.3233, Energy: -53.310454+0.000076j
[2025-09-10 16:14:39] [Iter 1790/2250] R3[739/1200], Temp: 0.3220, Energy: -53.339314-0.000724j
[2025-09-10 16:15:40] [Iter 1791/2250] R3[740/1200], Temp: 0.3208, Energy: -53.309136+0.000173j
[2025-09-10 16:16:40] [Iter 1792/2250] R3[741/1200], Temp: 0.3196, Energy: -53.321222+0.001012j
[2025-09-10 16:17:40] [Iter 1793/2250] R3[742/1200], Temp: 0.3184, Energy: -53.279122-0.002295j
[2025-09-10 16:18:41] [Iter 1794/2250] R3[743/1200], Temp: 0.3172, Energy: -53.249628-0.000191j
[2025-09-10 16:19:41] [Iter 1795/2250] R3[744/1200], Temp: 0.3159, Energy: -53.331532-0.000036j
[2025-09-10 16:20:41] [Iter 1796/2250] R3[745/1200], Temp: 0.3147, Energy: -53.251924-0.001413j
[2025-09-10 16:21:42] [Iter 1797/2250] R3[746/1200], Temp: 0.3135, Energy: -53.218139-0.000086j
[2025-09-10 16:22:42] [Iter 1798/2250] R3[747/1200], Temp: 0.3123, Energy: -53.262941-0.000071j
[2025-09-10 16:23:42] [Iter 1799/2250] R3[748/1200], Temp: 0.3111, Energy: -53.246168+0.000962j
[2025-09-10 16:24:43] [Iter 1800/2250] R3[749/1200], Temp: 0.3099, Energy: -53.244764-0.001907j
[2025-09-10 16:25:43] [Iter 1801/2250] R3[750/1200], Temp: 0.3087, Energy: -53.349369+0.000038j
[2025-09-10 16:26:43] [Iter 1802/2250] R3[751/1200], Temp: 0.3074, Energy: -53.354928-0.000951j
[2025-09-10 16:27:44] [Iter 1803/2250] R3[752/1200], Temp: 0.3062, Energy: -53.292171+0.000260j
[2025-09-10 16:28:44] [Iter 1804/2250] R3[753/1200], Temp: 0.3050, Energy: -53.331982+0.000504j
[2025-09-10 16:29:44] [Iter 1805/2250] R3[754/1200], Temp: 0.3038, Energy: -53.295455-0.000213j
[2025-09-10 16:30:45] [Iter 1806/2250] R3[755/1200], Temp: 0.3026, Energy: -53.271414+0.000362j
[2025-09-10 16:31:45] [Iter 1807/2250] R3[756/1200], Temp: 0.3014, Energy: -53.228655+0.001001j
[2025-09-10 16:32:46] [Iter 1808/2250] R3[757/1200], Temp: 0.3002, Energy: -53.257984+0.001827j
[2025-09-10 16:33:46] [Iter 1809/2250] R3[758/1200], Temp: 0.2990, Energy: -53.296251+0.002131j
[2025-09-10 16:34:46] [Iter 1810/2250] R3[759/1200], Temp: 0.2978, Energy: -53.309523-0.001106j
[2025-09-10 16:35:47] [Iter 1811/2250] R3[760/1200], Temp: 0.2966, Energy: -53.324788+0.001253j
[2025-09-10 16:36:47] [Iter 1812/2250] R3[761/1200], Temp: 0.2954, Energy: -53.264063+0.000516j
[2025-09-10 16:37:47] [Iter 1813/2250] R3[762/1200], Temp: 0.2942, Energy: -53.258787-0.000069j
[2025-09-10 16:38:48] [Iter 1814/2250] R3[763/1200], Temp: 0.2931, Energy: -53.245456-0.003754j
[2025-09-10 16:39:48] [Iter 1815/2250] R3[764/1200], Temp: 0.2919, Energy: -53.288805-0.000973j
[2025-09-10 16:40:49] [Iter 1816/2250] R3[765/1200], Temp: 0.2907, Energy: -53.351215+0.000390j
[2025-09-10 16:41:49] [Iter 1817/2250] R3[766/1200], Temp: 0.2895, Energy: -53.300113+0.002400j
[2025-09-10 16:42:49] [Iter 1818/2250] R3[767/1200], Temp: 0.2883, Energy: -53.306327+0.001869j
[2025-09-10 16:43:49] [Iter 1819/2250] R3[768/1200], Temp: 0.2871, Energy: -53.268721+0.002449j
[2025-09-10 16:44:50] [Iter 1820/2250] R3[769/1200], Temp: 0.2859, Energy: -53.279899-0.000454j
[2025-09-10 16:45:50] [Iter 1821/2250] R3[770/1200], Temp: 0.2847, Energy: -53.282376-0.000922j
[2025-09-10 16:46:51] [Iter 1822/2250] R3[771/1200], Temp: 0.2836, Energy: -53.284053+0.000949j
[2025-09-10 16:47:51] [Iter 1823/2250] R3[772/1200], Temp: 0.2824, Energy: -53.238045-0.001308j
[2025-09-10 16:48:51] [Iter 1824/2250] R3[773/1200], Temp: 0.2812, Energy: -53.253077+0.001577j
[2025-09-10 16:49:52] [Iter 1825/2250] R3[774/1200], Temp: 0.2800, Energy: -53.240823+0.000312j
[2025-09-10 16:50:52] [Iter 1826/2250] R3[775/1200], Temp: 0.2789, Energy: -53.262395+0.000632j
[2025-09-10 16:51:52] [Iter 1827/2250] R3[776/1200], Temp: 0.2777, Energy: -53.257558-0.001398j
[2025-09-10 16:52:52] [Iter 1828/2250] R3[777/1200], Temp: 0.2765, Energy: -53.272772+0.000312j
[2025-09-10 16:53:53] [Iter 1829/2250] R3[778/1200], Temp: 0.2753, Energy: -53.306083-0.001166j
[2025-09-10 16:54:53] [Iter 1830/2250] R3[779/1200], Temp: 0.2742, Energy: -53.211103+0.001228j
[2025-09-10 16:55:54] [Iter 1831/2250] R3[780/1200], Temp: 0.2730, Energy: -53.227368-0.000365j
[2025-09-10 16:56:54] [Iter 1832/2250] R3[781/1200], Temp: 0.2718, Energy: -53.280203+0.000553j
[2025-09-10 16:57:55] [Iter 1833/2250] R3[782/1200], Temp: 0.2707, Energy: -53.273642-0.002366j
[2025-09-10 16:58:55] [Iter 1834/2250] R3[783/1200], Temp: 0.2695, Energy: -53.280271+0.003107j
[2025-09-10 16:59:55] [Iter 1835/2250] R3[784/1200], Temp: 0.2684, Energy: -53.249019-0.000583j
[2025-09-10 17:00:56] [Iter 1836/2250] R3[785/1200], Temp: 0.2672, Energy: -53.267062+0.000111j
[2025-09-10 17:01:56] [Iter 1837/2250] R3[786/1200], Temp: 0.2660, Energy: -53.248631+0.000543j
[2025-09-10 17:02:57] [Iter 1838/2250] R3[787/1200], Temp: 0.2649, Energy: -53.207934-0.001329j
[2025-09-10 17:03:57] [Iter 1839/2250] R3[788/1200], Temp: 0.2637, Energy: -53.197764-0.000967j
[2025-09-10 17:04:57] [Iter 1840/2250] R3[789/1200], Temp: 0.2626, Energy: -53.174601+0.001357j
[2025-09-10 17:05:57] [Iter 1841/2250] R3[790/1200], Temp: 0.2614, Energy: -53.196727-0.000614j
[2025-09-10 17:06:58] [Iter 1842/2250] R3[791/1200], Temp: 0.2603, Energy: -53.232405+0.003621j
[2025-09-10 17:07:58] [Iter 1843/2250] R3[792/1200], Temp: 0.2591, Energy: -53.260847+0.001037j
[2025-09-10 17:08:58] [Iter 1844/2250] R3[793/1200], Temp: 0.2580, Energy: -53.236720+0.000047j
[2025-09-10 17:09:59] [Iter 1845/2250] R3[794/1200], Temp: 0.2568, Energy: -53.211851-0.000350j
[2025-09-10 17:10:59] [Iter 1846/2250] R3[795/1200], Temp: 0.2557, Energy: -53.225184+0.002178j
[2025-09-10 17:11:59] [Iter 1847/2250] R3[796/1200], Temp: 0.2545, Energy: -53.270829-0.000592j
[2025-09-10 17:13:00] [Iter 1848/2250] R3[797/1200], Temp: 0.2534, Energy: -53.306573-0.000859j
[2025-09-10 17:14:00] [Iter 1849/2250] R3[798/1200], Temp: 0.2523, Energy: -53.310966-0.001051j
[2025-09-10 17:14:47] [Iter 1850/2250] R3[799/1200], Temp: 0.2511, Energy: -53.299699-0.002561j
[2025-09-10 17:15:27] [Iter 1851/2250] R3[800/1200], Temp: 0.2500, Energy: -53.336725+0.000747j
[2025-09-10 17:16:07] [Iter 1852/2250] R3[801/1200], Temp: 0.2489, Energy: -53.359555-0.002565j
[2025-09-10 17:16:48] [Iter 1853/2250] R3[802/1200], Temp: 0.2477, Energy: -53.322037-0.000187j
[2025-09-10 17:17:28] [Iter 1854/2250] R3[803/1200], Temp: 0.2466, Energy: -53.306651-0.001194j
[2025-09-10 17:18:08] [Iter 1855/2250] R3[804/1200], Temp: 0.2455, Energy: -53.321649+0.000412j
[2025-09-10 17:18:48] [Iter 1856/2250] R3[805/1200], Temp: 0.2444, Energy: -53.275386+0.000489j
[2025-09-10 17:19:28] [Iter 1857/2250] R3[806/1200], Temp: 0.2432, Energy: -53.298495+0.001813j
[2025-09-10 17:20:09] [Iter 1858/2250] R3[807/1200], Temp: 0.2421, Energy: -53.298233+0.000126j
[2025-09-10 17:20:43] [Iter 1859/2250] R3[808/1200], Temp: 0.2410, Energy: -53.292741-0.000566j
[2025-09-10 17:21:02] [Iter 1860/2250] R3[809/1200], Temp: 0.2399, Energy: -53.293984-0.000123j
[2025-09-10 17:21:21] [Iter 1861/2250] R3[810/1200], Temp: 0.2388, Energy: -53.266479-0.000148j
[2025-09-10 17:21:40] [Iter 1862/2250] R3[811/1200], Temp: 0.2376, Energy: -53.244072-0.001617j
[2025-09-10 17:22:00] [Iter 1863/2250] R3[812/1200], Temp: 0.2365, Energy: -53.292574-0.000068j
[2025-09-10 17:22:27] [Iter 1864/2250] R3[813/1200], Temp: 0.2354, Energy: -53.340742-0.001564j
[2025-09-10 17:22:47] [Iter 1865/2250] R3[814/1200], Temp: 0.2343, Energy: -53.273671+0.001511j
[2025-09-10 17:23:06] [Iter 1866/2250] R3[815/1200], Temp: 0.2332, Energy: -53.284606+0.001127j
[2025-09-10 17:23:26] [Iter 1867/2250] R3[816/1200], Temp: 0.2321, Energy: -53.273624-0.003333j
[2025-09-10 17:23:52] [Iter 1868/2250] R3[817/1200], Temp: 0.2310, Energy: -53.322812-0.001280j
[2025-09-10 17:24:15] [Iter 1869/2250] R3[818/1200], Temp: 0.2299, Energy: -53.369201-0.000184j
[2025-09-10 17:24:40] [Iter 1870/2250] R3[819/1200], Temp: 0.2288, Energy: -53.346946+0.000361j
[2025-09-10 17:25:02] [Iter 1871/2250] R3[820/1200], Temp: 0.2277, Energy: -53.411198-0.000185j
[2025-09-10 17:25:36] [Iter 1872/2250] R3[821/1200], Temp: 0.2266, Energy: -53.340960+0.001326j
[2025-09-10 17:26:37] [Iter 1873/2250] R3[822/1200], Temp: 0.2255, Energy: -53.304787+0.000260j
[2025-09-10 17:27:37] [Iter 1874/2250] R3[823/1200], Temp: 0.2244, Energy: -53.244840+0.001522j
[2025-09-10 17:28:37] [Iter 1875/2250] R3[824/1200], Temp: 0.2233, Energy: -53.269871-0.000124j
[2025-09-10 17:29:38] [Iter 1876/2250] R3[825/1200], Temp: 0.2222, Energy: -53.306900-0.002071j
[2025-09-10 17:30:38] [Iter 1877/2250] R3[826/1200], Temp: 0.2211, Energy: -53.242575-0.000764j
[2025-09-10 17:31:39] [Iter 1878/2250] R3[827/1200], Temp: 0.2200, Energy: -53.327277-0.000029j
[2025-09-10 17:32:39] [Iter 1879/2250] R3[828/1200], Temp: 0.2190, Energy: -53.344825-0.000671j
[2025-09-10 17:33:39] [Iter 1880/2250] R3[829/1200], Temp: 0.2179, Energy: -53.331801+0.000674j
[2025-09-10 17:34:40] [Iter 1881/2250] R3[830/1200], Temp: 0.2168, Energy: -53.364084+0.001416j
[2025-09-10 17:35:40] [Iter 1882/2250] R3[831/1200], Temp: 0.2157, Energy: -53.334523-0.000059j
[2025-09-10 17:36:40] [Iter 1883/2250] R3[832/1200], Temp: 0.2146, Energy: -53.314328+0.001348j
[2025-09-10 17:37:41] [Iter 1884/2250] R3[833/1200], Temp: 0.2136, Energy: -53.255828-0.000417j
[2025-09-10 17:38:41] [Iter 1885/2250] R3[834/1200], Temp: 0.2125, Energy: -53.256771-0.002208j
[2025-09-10 17:39:42] [Iter 1886/2250] R3[835/1200], Temp: 0.2114, Energy: -53.250484-0.000539j
[2025-09-10 17:40:42] [Iter 1887/2250] R3[836/1200], Temp: 0.2104, Energy: -53.201878+0.002154j
[2025-09-10 17:41:42] [Iter 1888/2250] R3[837/1200], Temp: 0.2093, Energy: -53.256608+0.002938j
[2025-09-10 17:42:43] [Iter 1889/2250] R3[838/1200], Temp: 0.2082, Energy: -53.268488-0.001745j
[2025-09-10 17:43:43] [Iter 1890/2250] R3[839/1200], Temp: 0.2072, Energy: -53.196335-0.001626j
[2025-09-10 17:44:43] [Iter 1891/2250] R3[840/1200], Temp: 0.2061, Energy: -53.286026+0.001005j
[2025-09-10 17:45:43] [Iter 1892/2250] R3[841/1200], Temp: 0.2050, Energy: -53.332402-0.001629j
[2025-09-10 17:46:44] [Iter 1893/2250] R3[842/1200], Temp: 0.2040, Energy: -53.338105+0.002666j
[2025-09-10 17:47:44] [Iter 1894/2250] R3[843/1200], Temp: 0.2029, Energy: -53.315869-0.000283j
[2025-09-10 17:48:44] [Iter 1895/2250] R3[844/1200], Temp: 0.2019, Energy: -53.284585-0.000227j
[2025-09-10 17:49:44] [Iter 1896/2250] R3[845/1200], Temp: 0.2008, Energy: -53.259649+0.000598j
[2025-09-10 17:50:45] [Iter 1897/2250] R3[846/1200], Temp: 0.1998, Energy: -53.327436+0.000916j
[2025-09-10 17:51:45] [Iter 1898/2250] R3[847/1200], Temp: 0.1987, Energy: -53.265259-0.000127j
[2025-09-10 17:52:45] [Iter 1899/2250] R3[848/1200], Temp: 0.1977, Energy: -53.218799-0.002315j
[2025-09-10 17:53:46] [Iter 1900/2250] R3[849/1200], Temp: 0.1967, Energy: -53.303129+0.000644j
[2025-09-10 17:54:46] [Iter 1901/2250] R3[850/1200], Temp: 0.1956, Energy: -53.323910-0.001173j
[2025-09-10 17:55:46] [Iter 1902/2250] R3[851/1200], Temp: 0.1946, Energy: -53.327493-0.001717j
[2025-09-10 17:56:46] [Iter 1903/2250] R3[852/1200], Temp: 0.1935, Energy: -53.272438+0.001662j
[2025-09-10 17:57:47] [Iter 1904/2250] R3[853/1200], Temp: 0.1925, Energy: -53.281539-0.001254j
[2025-09-10 17:58:47] [Iter 1905/2250] R3[854/1200], Temp: 0.1915, Energy: -53.269046-0.001217j
[2025-09-10 17:59:47] [Iter 1906/2250] R3[855/1200], Temp: 0.1905, Energy: -53.324561+0.001241j
[2025-09-10 18:00:47] [Iter 1907/2250] R3[856/1200], Temp: 0.1894, Energy: -53.325406-0.000921j
[2025-09-10 18:01:48] [Iter 1908/2250] R3[857/1200], Temp: 0.1884, Energy: -53.327463-0.000068j
[2025-09-10 18:02:48] [Iter 1909/2250] R3[858/1200], Temp: 0.1874, Energy: -53.352269+0.002430j
[2025-09-10 18:03:49] [Iter 1910/2250] R3[859/1200], Temp: 0.1864, Energy: -53.347366+0.001670j
[2025-09-10 18:04:49] [Iter 1911/2250] R3[860/1200], Temp: 0.1853, Energy: -53.401977+0.002393j
[2025-09-10 18:05:49] [Iter 1912/2250] R3[861/1200], Temp: 0.1843, Energy: -53.388571+0.000581j
[2025-09-10 18:06:50] [Iter 1913/2250] R3[862/1200], Temp: 0.1833, Energy: -53.385771-0.000369j
[2025-09-10 18:07:50] [Iter 1914/2250] R3[863/1200], Temp: 0.1823, Energy: -53.374668-0.001597j
[2025-09-10 18:08:50] [Iter 1915/2250] R3[864/1200], Temp: 0.1813, Energy: -53.327262-0.000938j
[2025-09-10 18:09:50] [Iter 1916/2250] R3[865/1200], Temp: 0.1803, Energy: -53.298839-0.000395j
[2025-09-10 18:10:51] [Iter 1917/2250] R3[866/1200], Temp: 0.1793, Energy: -53.325570-0.001464j
[2025-09-10 18:11:51] [Iter 1918/2250] R3[867/1200], Temp: 0.1783, Energy: -53.369001-0.002438j
[2025-09-10 18:12:51] [Iter 1919/2250] R3[868/1200], Temp: 0.1773, Energy: -53.370312+0.000733j
[2025-09-10 18:13:52] [Iter 1920/2250] R3[869/1200], Temp: 0.1763, Energy: -53.364396+0.000292j
[2025-09-10 18:14:52] [Iter 1921/2250] R3[870/1200], Temp: 0.1753, Energy: -53.269932-0.000051j
[2025-09-10 18:15:52] [Iter 1922/2250] R3[871/1200], Temp: 0.1743, Energy: -53.277911-0.001735j
[2025-09-10 18:16:52] [Iter 1923/2250] R3[872/1200], Temp: 0.1733, Energy: -53.239754-0.001927j
[2025-09-10 18:17:53] [Iter 1924/2250] R3[873/1200], Temp: 0.1723, Energy: -53.273236-0.000140j
[2025-09-10 18:18:53] [Iter 1925/2250] R3[874/1200], Temp: 0.1713, Energy: -53.283590-0.001393j
[2025-09-10 18:19:53] [Iter 1926/2250] R3[875/1200], Temp: 0.1703, Energy: -53.283618-0.001020j
[2025-09-10 18:20:53] [Iter 1927/2250] R3[876/1200], Temp: 0.1693, Energy: -53.334285-0.002106j
[2025-09-10 18:21:54] [Iter 1928/2250] R3[877/1200], Temp: 0.1684, Energy: -53.295122-0.000257j
[2025-09-10 18:22:54] [Iter 1929/2250] R3[878/1200], Temp: 0.1674, Energy: -53.257693-0.000380j
[2025-09-10 18:23:54] [Iter 1930/2250] R3[879/1200], Temp: 0.1664, Energy: -53.320412-0.000852j
[2025-09-10 18:24:55] [Iter 1931/2250] R3[880/1200], Temp: 0.1654, Energy: -53.289559-0.000706j
[2025-09-10 18:25:55] [Iter 1932/2250] R3[881/1200], Temp: 0.1645, Energy: -53.331243+0.001762j
[2025-09-10 18:26:55] [Iter 1933/2250] R3[882/1200], Temp: 0.1635, Energy: -53.306780+0.002770j
[2025-09-10 18:27:56] [Iter 1934/2250] R3[883/1200], Temp: 0.1625, Energy: -53.298902-0.000975j
[2025-09-10 18:28:56] [Iter 1935/2250] R3[884/1200], Temp: 0.1616, Energy: -53.334872+0.000623j
[2025-09-10 18:29:57] [Iter 1936/2250] R3[885/1200], Temp: 0.1606, Energy: -53.349364+0.002330j
[2025-09-10 18:30:57] [Iter 1937/2250] R3[886/1200], Temp: 0.1596, Energy: -53.347456-0.002495j
[2025-09-10 18:31:57] [Iter 1938/2250] R3[887/1200], Temp: 0.1587, Energy: -53.306969-0.000249j
[2025-09-10 18:32:57] [Iter 1939/2250] R3[888/1200], Temp: 0.1577, Energy: -53.302464+0.001149j
[2025-09-10 18:33:58] [Iter 1940/2250] R3[889/1200], Temp: 0.1568, Energy: -53.296502+0.000404j
[2025-09-10 18:34:58] [Iter 1941/2250] R3[890/1200], Temp: 0.1558, Energy: -53.249141-0.000458j
[2025-09-10 18:35:58] [Iter 1942/2250] R3[891/1200], Temp: 0.1549, Energy: -53.210075+0.001621j
[2025-09-10 18:36:59] [Iter 1943/2250] R3[892/1200], Temp: 0.1539, Energy: -53.248691+0.001188j
[2025-09-10 18:37:59] [Iter 1944/2250] R3[893/1200], Temp: 0.1530, Energy: -53.316169-0.002363j
[2025-09-10 18:39:00] [Iter 1945/2250] R3[894/1200], Temp: 0.1520, Energy: -53.306475-0.000799j
[2025-09-10 18:40:00] [Iter 1946/2250] R3[895/1200], Temp: 0.1511, Energy: -53.321851-0.000870j
[2025-09-10 18:41:00] [Iter 1947/2250] R3[896/1200], Temp: 0.1502, Energy: -53.356624+0.000841j
[2025-09-10 18:42:00] [Iter 1948/2250] R3[897/1200], Temp: 0.1492, Energy: -53.346122-0.000839j
[2025-09-10 18:43:01] [Iter 1949/2250] R3[898/1200], Temp: 0.1483, Energy: -53.297521+0.000245j
[2025-09-10 18:44:01] [Iter 1950/2250] R3[899/1200], Temp: 0.1474, Energy: -53.284884+0.000142j
[2025-09-10 18:45:01] [Iter 1951/2250] R3[900/1200], Temp: 0.1464, Energy: -53.216590+0.001974j
[2025-09-10 18:46:01] [Iter 1952/2250] R3[901/1200], Temp: 0.1455, Energy: -53.263546+0.001134j
[2025-09-10 18:47:01] [Iter 1953/2250] R3[902/1200], Temp: 0.1446, Energy: -53.309909+0.001291j
[2025-09-10 18:48:02] [Iter 1954/2250] R3[903/1200], Temp: 0.1437, Energy: -53.326794+0.001206j
[2025-09-10 18:49:02] [Iter 1955/2250] R3[904/1200], Temp: 0.1428, Energy: -53.254178+0.001727j
[2025-09-10 18:50:02] [Iter 1956/2250] R3[905/1200], Temp: 0.1418, Energy: -53.260107-0.000039j
[2025-09-10 18:51:03] [Iter 1957/2250] R3[906/1200], Temp: 0.1409, Energy: -53.283767+0.000574j
[2025-09-10 18:52:03] [Iter 1958/2250] R3[907/1200], Temp: 0.1400, Energy: -53.340724+0.000565j
[2025-09-10 18:53:03] [Iter 1959/2250] R3[908/1200], Temp: 0.1391, Energy: -53.338769+0.002269j
[2025-09-10 18:54:04] [Iter 1960/2250] R3[909/1200], Temp: 0.1382, Energy: -53.274413-0.000359j
[2025-09-10 18:55:04] [Iter 1961/2250] R3[910/1200], Temp: 0.1373, Energy: -53.378354+0.000625j
[2025-09-10 18:56:04] [Iter 1962/2250] R3[911/1200], Temp: 0.1364, Energy: -53.368133-0.000702j
[2025-09-10 18:57:05] [Iter 1963/2250] R3[912/1200], Temp: 0.1355, Energy: -53.386823-0.001344j
[2025-09-10 18:58:05] [Iter 1964/2250] R3[913/1200], Temp: 0.1346, Energy: -53.297614+0.000799j
[2025-09-10 18:59:06] [Iter 1965/2250] R3[914/1200], Temp: 0.1337, Energy: -53.328900+0.001334j
[2025-09-10 19:00:06] [Iter 1966/2250] R3[915/1200], Temp: 0.1328, Energy: -53.296637+0.000790j
[2025-09-10 19:01:07] [Iter 1967/2250] R3[916/1200], Temp: 0.1320, Energy: -53.335529+0.001992j
[2025-09-10 19:02:07] [Iter 1968/2250] R3[917/1200], Temp: 0.1311, Energy: -53.297618-0.001523j
[2025-09-10 19:03:07] [Iter 1969/2250] R3[918/1200], Temp: 0.1302, Energy: -53.321665-0.000649j
[2025-09-10 19:04:08] [Iter 1970/2250] R3[919/1200], Temp: 0.1293, Energy: -53.283239+0.003747j
[2025-09-10 19:05:08] [Iter 1971/2250] R3[920/1200], Temp: 0.1284, Energy: -53.254458+0.000310j
[2025-09-10 19:06:09] [Iter 1972/2250] R3[921/1200], Temp: 0.1276, Energy: -53.225307+0.000346j
[2025-09-10 19:07:09] [Iter 1973/2250] R3[922/1200], Temp: 0.1267, Energy: -53.250198+0.000474j
[2025-09-10 19:08:09] [Iter 1974/2250] R3[923/1200], Temp: 0.1258, Energy: -53.283189-0.002982j
[2025-09-10 19:09:10] [Iter 1975/2250] R3[924/1200], Temp: 0.1249, Energy: -53.293540+0.001886j
[2025-09-10 19:10:10] [Iter 1976/2250] R3[925/1200], Temp: 0.1241, Energy: -53.334257-0.001245j
[2025-09-10 19:11:11] [Iter 1977/2250] R3[926/1200], Temp: 0.1232, Energy: -53.273791-0.000376j
[2025-09-10 19:12:11] [Iter 1978/2250] R3[927/1200], Temp: 0.1224, Energy: -53.287196+0.000044j
[2025-09-10 19:13:11] [Iter 1979/2250] R3[928/1200], Temp: 0.1215, Energy: -53.198729+0.000350j
[2025-09-10 19:14:12] [Iter 1980/2250] R3[929/1200], Temp: 0.1206, Energy: -53.237626-0.000983j
[2025-09-10 19:15:12] [Iter 1981/2250] R3[930/1200], Temp: 0.1198, Energy: -53.280880-0.001294j
[2025-09-10 19:16:12] [Iter 1982/2250] R3[931/1200], Temp: 0.1189, Energy: -53.268263+0.006684j
[2025-09-10 19:17:13] [Iter 1983/2250] R3[932/1200], Temp: 0.1181, Energy: -53.325685-0.000480j
[2025-09-10 19:18:13] [Iter 1984/2250] R3[933/1200], Temp: 0.1173, Energy: -53.295938-0.000823j
[2025-09-10 19:19:14] [Iter 1985/2250] R3[934/1200], Temp: 0.1164, Energy: -53.284211+0.002092j
[2025-09-10 19:20:14] [Iter 1986/2250] R3[935/1200], Temp: 0.1156, Energy: -53.291213-0.000639j
[2025-09-10 19:21:14] [Iter 1987/2250] R3[936/1200], Temp: 0.1147, Energy: -53.222226-0.000034j
[2025-09-10 19:22:14] [Iter 1988/2250] R3[937/1200], Temp: 0.1139, Energy: -53.253627-0.001742j
[2025-09-10 19:23:15] [Iter 1989/2250] R3[938/1200], Temp: 0.1131, Energy: -53.286161-0.000164j
[2025-09-10 19:24:15] [Iter 1990/2250] R3[939/1200], Temp: 0.1123, Energy: -53.275907-0.000977j
[2025-09-10 19:25:15] [Iter 1991/2250] R3[940/1200], Temp: 0.1114, Energy: -53.230332-0.001179j
[2025-09-10 19:26:16] [Iter 1992/2250] R3[941/1200], Temp: 0.1106, Energy: -53.298908+0.000706j
[2025-09-10 19:27:16] [Iter 1993/2250] R3[942/1200], Temp: 0.1098, Energy: -53.304429+0.000940j
[2025-09-10 19:28:17] [Iter 1994/2250] R3[943/1200], Temp: 0.1090, Energy: -53.293000+0.000239j
[2025-09-10 19:29:17] [Iter 1995/2250] R3[944/1200], Temp: 0.1082, Energy: -53.296809+0.000580j
[2025-09-10 19:30:17] [Iter 1996/2250] R3[945/1200], Temp: 0.1073, Energy: -53.294852+0.000669j
[2025-09-10 19:31:18] [Iter 1997/2250] R3[946/1200], Temp: 0.1065, Energy: -53.291009+0.000373j
[2025-09-10 19:32:18] [Iter 1998/2250] R3[947/1200], Temp: 0.1057, Energy: -53.357762-0.000887j
[2025-09-10 19:33:19] [Iter 1999/2250] R3[948/1200], Temp: 0.1049, Energy: -53.278859-0.001199j
[2025-09-10 19:34:19] [Iter 2000/2250] R3[949/1200], Temp: 0.1041, Energy: -53.266092-0.001540j
[2025-09-10 19:34:19] ✓ Checkpoint saved: checkpoint_iter_002000.pkl
[2025-09-10 19:35:20] [Iter 2001/2250] R3[950/1200], Temp: 0.1033, Energy: -53.275692+0.001039j
[2025-09-10 19:36:20] [Iter 2002/2250] R3[951/1200], Temp: 0.1025, Energy: -53.301987-0.001723j
[2025-09-10 19:37:20] [Iter 2003/2250] R3[952/1200], Temp: 0.1017, Energy: -53.285248+0.002146j
[2025-09-10 19:38:21] [Iter 2004/2250] R3[953/1200], Temp: 0.1009, Energy: -53.296715+0.000183j
[2025-09-10 19:39:21] [Iter 2005/2250] R3[954/1200], Temp: 0.1002, Energy: -53.240544-0.000068j
[2025-09-10 19:40:21] [Iter 2006/2250] R3[955/1200], Temp: 0.0994, Energy: -53.294081+0.001284j
[2025-09-10 19:41:21] [Iter 2007/2250] R3[956/1200], Temp: 0.0986, Energy: -53.323629+0.001810j
[2025-09-10 19:42:22] [Iter 2008/2250] R3[957/1200], Temp: 0.0978, Energy: -53.354656+0.000687j
[2025-09-10 19:43:22] [Iter 2009/2250] R3[958/1200], Temp: 0.0970, Energy: -53.341543-0.000007j
[2025-09-10 19:44:22] [Iter 2010/2250] R3[959/1200], Temp: 0.0963, Energy: -53.315775+0.001406j
[2025-09-10 19:45:23] [Iter 2011/2250] R3[960/1200], Temp: 0.0955, Energy: -53.241402+0.000516j
[2025-09-10 19:46:23] [Iter 2012/2250] R3[961/1200], Temp: 0.0947, Energy: -53.323041+0.000717j
[2025-09-10 19:47:24] [Iter 2013/2250] R3[962/1200], Temp: 0.0940, Energy: -53.326122+0.001073j
[2025-09-10 19:48:24] [Iter 2014/2250] R3[963/1200], Temp: 0.0932, Energy: -53.298222+0.000365j
[2025-09-10 19:49:25] [Iter 2015/2250] R3[964/1200], Temp: 0.0924, Energy: -53.331755+0.000187j
[2025-09-10 19:50:25] [Iter 2016/2250] R3[965/1200], Temp: 0.0917, Energy: -53.336203+0.000041j
[2025-09-10 19:51:26] [Iter 2017/2250] R3[966/1200], Temp: 0.0909, Energy: -53.333656+0.000380j
[2025-09-10 19:52:26] [Iter 2018/2250] R3[967/1200], Temp: 0.0902, Energy: -53.337544+0.000463j
[2025-09-10 19:53:26] [Iter 2019/2250] R3[968/1200], Temp: 0.0894, Energy: -53.316700+0.001482j
[2025-09-10 19:54:27] [Iter 2020/2250] R3[969/1200], Temp: 0.0887, Energy: -53.348252+0.001135j
[2025-09-10 19:55:27] [Iter 2021/2250] R3[970/1200], Temp: 0.0879, Energy: -53.328927+0.000838j
[2025-09-10 19:56:27] [Iter 2022/2250] R3[971/1200], Temp: 0.0872, Energy: -53.274377-0.000332j
[2025-09-10 19:57:28] [Iter 2023/2250] R3[972/1200], Temp: 0.0865, Energy: -53.324215+0.002656j
[2025-09-10 19:58:28] [Iter 2024/2250] R3[973/1200], Temp: 0.0857, Energy: -53.321250-0.001493j
[2025-09-10 19:59:28] [Iter 2025/2250] R3[974/1200], Temp: 0.0850, Energy: -53.278752+0.000787j
[2025-09-10 20:00:28] [Iter 2026/2250] R3[975/1200], Temp: 0.0843, Energy: -53.276418-0.000456j
[2025-09-10 20:01:29] [Iter 2027/2250] R3[976/1200], Temp: 0.0835, Energy: -53.301719-0.001196j
[2025-09-10 20:02:29] [Iter 2028/2250] R3[977/1200], Temp: 0.0828, Energy: -53.374205+0.002280j
[2025-09-10 20:03:29] [Iter 2029/2250] R3[978/1200], Temp: 0.0821, Energy: -53.357554-0.000582j
[2025-09-10 20:04:29] [Iter 2030/2250] R3[979/1200], Temp: 0.0814, Energy: -53.391908+0.000797j
[2025-09-10 20:05:30] [Iter 2031/2250] R3[980/1200], Temp: 0.0807, Energy: -53.312007+0.002417j
[2025-09-10 20:06:30] [Iter 2032/2250] R3[981/1200], Temp: 0.0800, Energy: -53.294716-0.000636j
[2025-09-10 20:07:31] [Iter 2033/2250] R3[982/1200], Temp: 0.0792, Energy: -53.298734-0.000164j
[2025-09-10 20:08:31] [Iter 2034/2250] R3[983/1200], Temp: 0.0785, Energy: -53.358286+0.000034j
[2025-09-10 20:09:31] [Iter 2035/2250] R3[984/1200], Temp: 0.0778, Energy: -53.312256+0.000073j
[2025-09-10 20:10:32] [Iter 2036/2250] R3[985/1200], Temp: 0.0771, Energy: -53.371461-0.000108j
[2025-09-10 20:11:32] [Iter 2037/2250] R3[986/1200], Temp: 0.0764, Energy: -53.298085+0.000062j
[2025-09-10 20:12:33] [Iter 2038/2250] R3[987/1200], Temp: 0.0757, Energy: -53.354785+0.001232j
[2025-09-10 20:13:33] [Iter 2039/2250] R3[988/1200], Temp: 0.0751, Energy: -53.292883+0.000757j
[2025-09-10 20:14:33] [Iter 2040/2250] R3[989/1200], Temp: 0.0744, Energy: -53.294117-0.001435j
[2025-09-10 20:15:34] [Iter 2041/2250] R3[990/1200], Temp: 0.0737, Energy: -53.326291+0.000107j
[2025-09-10 20:16:34] [Iter 2042/2250] R3[991/1200], Temp: 0.0730, Energy: -53.365849-0.002658j
[2025-09-10 20:17:34] [Iter 2043/2250] R3[992/1200], Temp: 0.0723, Energy: -53.371638+0.002240j
[2025-09-10 20:18:35] [Iter 2044/2250] R3[993/1200], Temp: 0.0716, Energy: -53.383444+0.001393j
[2025-09-10 20:19:35] [Iter 2045/2250] R3[994/1200], Temp: 0.0710, Energy: -53.334973-0.002176j
[2025-09-10 20:20:35] [Iter 2046/2250] R3[995/1200], Temp: 0.0703, Energy: -53.365774+0.002734j
[2025-09-10 20:21:36] [Iter 2047/2250] R3[996/1200], Temp: 0.0696, Energy: -53.311870-0.002315j
[2025-09-10 20:22:36] [Iter 2048/2250] R3[997/1200], Temp: 0.0690, Energy: -53.267750+0.000100j
[2025-09-10 20:23:36] [Iter 2049/2250] R3[998/1200], Temp: 0.0683, Energy: -53.300169-0.001010j
[2025-09-10 20:24:37] [Iter 2050/2250] R3[999/1200], Temp: 0.0676, Energy: -53.355801+0.002100j
[2025-09-10 20:25:37] [Iter 2051/2250] R3[1000/1200], Temp: 0.0670, Energy: -53.353611+0.001317j
[2025-09-10 20:26:38] [Iter 2052/2250] R3[1001/1200], Temp: 0.0663, Energy: -53.368891-0.000338j
[2025-09-10 20:27:38] [Iter 2053/2250] R3[1002/1200], Temp: 0.0657, Energy: -53.289423+0.000092j
[2025-09-10 20:28:39] [Iter 2054/2250] R3[1003/1200], Temp: 0.0650, Energy: -53.232399+0.000938j
[2025-09-10 20:29:39] [Iter 2055/2250] R3[1004/1200], Temp: 0.0644, Energy: -53.297797+0.000268j
[2025-09-10 20:30:39] [Iter 2056/2250] R3[1005/1200], Temp: 0.0638, Energy: -53.257430-0.000791j
[2025-09-10 20:31:39] [Iter 2057/2250] R3[1006/1200], Temp: 0.0631, Energy: -53.274487-0.000126j
[2025-09-10 20:32:40] [Iter 2058/2250] R3[1007/1200], Temp: 0.0625, Energy: -53.315883+0.000422j
[2025-09-10 20:33:40] [Iter 2059/2250] R3[1008/1200], Temp: 0.0618, Energy: -53.316787-0.000347j
[2025-09-10 20:34:40] [Iter 2060/2250] R3[1009/1200], Temp: 0.0612, Energy: -53.323417+0.000592j
[2025-09-10 20:35:41] [Iter 2061/2250] R3[1010/1200], Temp: 0.0606, Energy: -53.261436+0.000235j
[2025-09-10 20:36:41] [Iter 2062/2250] R3[1011/1200], Temp: 0.0600, Energy: -53.271851+0.000649j
[2025-09-10 20:37:42] [Iter 2063/2250] R3[1012/1200], Temp: 0.0593, Energy: -53.245704-0.000950j
[2025-09-10 20:38:42] [Iter 2064/2250] R3[1013/1200], Temp: 0.0587, Energy: -53.224797-0.000056j
[2025-09-10 20:39:43] [Iter 2065/2250] R3[1014/1200], Temp: 0.0581, Energy: -53.243698-0.000474j
[2025-09-10 20:40:43] [Iter 2066/2250] R3[1015/1200], Temp: 0.0575, Energy: -53.297918-0.000162j
[2025-09-10 20:41:43] [Iter 2067/2250] R3[1016/1200], Temp: 0.0569, Energy: -53.249049+0.000234j
[2025-09-10 20:42:44] [Iter 2068/2250] R3[1017/1200], Temp: 0.0563, Energy: -53.349124-0.002718j
[2025-09-10 20:43:44] [Iter 2069/2250] R3[1018/1200], Temp: 0.0557, Energy: -53.290791-0.000683j
[2025-09-10 20:44:44] [Iter 2070/2250] R3[1019/1200], Temp: 0.0551, Energy: -53.263672+0.000624j
[2025-09-10 20:45:45] [Iter 2071/2250] R3[1020/1200], Temp: 0.0545, Energy: -53.226109-0.002135j
[2025-09-10 20:46:45] [Iter 2072/2250] R3[1021/1200], Temp: 0.0539, Energy: -53.218884-0.001044j
[2025-09-10 20:47:45] [Iter 2073/2250] R3[1022/1200], Temp: 0.0533, Energy: -53.179351+0.000512j
[2025-09-10 20:48:46] [Iter 2074/2250] R3[1023/1200], Temp: 0.0527, Energy: -53.200455-0.000378j
[2025-09-10 20:49:46] [Iter 2075/2250] R3[1024/1200], Temp: 0.0521, Energy: -53.272668-0.001938j
[2025-09-10 20:50:47] [Iter 2076/2250] R3[1025/1200], Temp: 0.0516, Energy: -53.264742+0.000438j
[2025-09-10 20:51:47] [Iter 2077/2250] R3[1026/1200], Temp: 0.0510, Energy: -53.261647+0.000476j
[2025-09-10 20:52:47] [Iter 2078/2250] R3[1027/1200], Temp: 0.0504, Energy: -53.255376-0.001223j
[2025-09-10 20:53:47] [Iter 2079/2250] R3[1028/1200], Temp: 0.0498, Energy: -53.292012+0.000955j
[2025-09-10 20:54:48] [Iter 2080/2250] R3[1029/1200], Temp: 0.0493, Energy: -53.259206+0.000749j
[2025-09-10 20:55:48] [Iter 2081/2250] R3[1030/1200], Temp: 0.0487, Energy: -53.195258+0.001821j
[2025-09-10 20:56:49] [Iter 2082/2250] R3[1031/1200], Temp: 0.0481, Energy: -53.285691-0.001263j
[2025-09-10 20:57:49] [Iter 2083/2250] R3[1032/1200], Temp: 0.0476, Energy: -53.260194-0.002811j
[2025-09-10 20:58:49] [Iter 2084/2250] R3[1033/1200], Temp: 0.0470, Energy: -53.307014-0.000114j
[2025-09-10 20:59:50] [Iter 2085/2250] R3[1034/1200], Temp: 0.0465, Energy: -53.308403-0.000886j
[2025-09-10 21:00:51] [Iter 2086/2250] R3[1035/1200], Temp: 0.0459, Energy: -53.295542+0.000414j
[2025-09-10 21:01:51] [Iter 2087/2250] R3[1036/1200], Temp: 0.0454, Energy: -53.322114+0.001198j
[2025-09-10 21:02:51] [Iter 2088/2250] R3[1037/1200], Temp: 0.0448, Energy: -53.283623-0.001906j
[2025-09-10 21:03:51] [Iter 2089/2250] R3[1038/1200], Temp: 0.0443, Energy: -53.236404-0.000061j
[2025-09-10 21:04:52] [Iter 2090/2250] R3[1039/1200], Temp: 0.0438, Energy: -53.258918+0.000763j
[2025-09-10 21:05:52] [Iter 2091/2250] R3[1040/1200], Temp: 0.0432, Energy: -53.343335-0.001001j
[2025-09-10 21:06:52] [Iter 2092/2250] R3[1041/1200], Temp: 0.0427, Energy: -53.270277-0.000593j
[2025-09-10 21:07:52] [Iter 2093/2250] R3[1042/1200], Temp: 0.0422, Energy: -53.291385+0.000093j
[2025-09-10 21:08:53] [Iter 2094/2250] R3[1043/1200], Temp: 0.0416, Energy: -53.282251+0.000750j
[2025-09-10 21:09:53] [Iter 2095/2250] R3[1044/1200], Temp: 0.0411, Energy: -53.296258+0.002444j
[2025-09-10 21:10:54] [Iter 2096/2250] R3[1045/1200], Temp: 0.0406, Energy: -53.315305-0.000909j
[2025-09-10 21:11:54] [Iter 2097/2250] R3[1046/1200], Temp: 0.0401, Energy: -53.288301+0.000531j
[2025-09-10 21:12:54] [Iter 2098/2250] R3[1047/1200], Temp: 0.0396, Energy: -53.244483-0.001301j
[2025-09-10 21:13:55] [Iter 2099/2250] R3[1048/1200], Temp: 0.0391, Energy: -53.301798-0.002168j
[2025-09-10 21:14:55] [Iter 2100/2250] R3[1049/1200], Temp: 0.0386, Energy: -53.343426+0.001333j
[2025-09-10 21:15:55] [Iter 2101/2250] R3[1050/1200], Temp: 0.0381, Energy: -53.246376-0.000184j
[2025-09-10 21:16:56] [Iter 2102/2250] R3[1051/1200], Temp: 0.0376, Energy: -53.260521-0.001355j
[2025-09-10 21:17:56] [Iter 2103/2250] R3[1052/1200], Temp: 0.0371, Energy: -53.316498-0.001504j
[2025-09-10 21:18:56] [Iter 2104/2250] R3[1053/1200], Temp: 0.0366, Energy: -53.285260-0.002297j
[2025-09-10 21:19:57] [Iter 2105/2250] R3[1054/1200], Temp: 0.0361, Energy: -53.302758-0.000837j
[2025-09-10 21:20:57] [Iter 2106/2250] R3[1055/1200], Temp: 0.0356, Energy: -53.321729+0.000088j
[2025-09-10 21:21:57] [Iter 2107/2250] R3[1056/1200], Temp: 0.0351, Energy: -53.257036-0.000721j
[2025-09-10 21:22:58] [Iter 2108/2250] R3[1057/1200], Temp: 0.0346, Energy: -53.247885-0.000437j
[2025-09-10 21:23:58] [Iter 2109/2250] R3[1058/1200], Temp: 0.0342, Energy: -53.254407+0.001641j
[2025-09-10 21:24:59] [Iter 2110/2250] R3[1059/1200], Temp: 0.0337, Energy: -53.257846-0.000681j
[2025-09-10 21:25:59] [Iter 2111/2250] R3[1060/1200], Temp: 0.0332, Energy: -53.216600-0.000697j
[2025-09-10 21:26:59] [Iter 2112/2250] R3[1061/1200], Temp: 0.0327, Energy: -53.193393-0.000419j
[2025-09-10 21:27:59] [Iter 2113/2250] R3[1062/1200], Temp: 0.0323, Energy: -53.244217-0.001402j
[2025-09-10 21:29:00] [Iter 2114/2250] R3[1063/1200], Temp: 0.0318, Energy: -53.270018-0.000227j
[2025-09-10 21:30:00] [Iter 2115/2250] R3[1064/1200], Temp: 0.0314, Energy: -53.252002+0.000837j
[2025-09-10 21:31:01] [Iter 2116/2250] R3[1065/1200], Temp: 0.0309, Energy: -53.336761+0.000141j
[2025-09-10 21:32:01] [Iter 2117/2250] R3[1066/1200], Temp: 0.0305, Energy: -53.315194+0.005900j
[2025-09-10 21:33:01] [Iter 2118/2250] R3[1067/1200], Temp: 0.0300, Energy: -53.314569+0.001213j
[2025-09-10 21:34:02] [Iter 2119/2250] R3[1068/1200], Temp: 0.0296, Energy: -53.256827+0.001706j
[2025-09-10 21:35:02] [Iter 2120/2250] R3[1069/1200], Temp: 0.0291, Energy: -53.303672+0.001102j
[2025-09-10 21:36:03] [Iter 2121/2250] R3[1070/1200], Temp: 0.0287, Energy: -53.300815+0.001796j
[2025-09-10 21:37:03] [Iter 2122/2250] R3[1071/1200], Temp: 0.0282, Energy: -53.264549-0.001019j
[2025-09-10 21:38:03] [Iter 2123/2250] R3[1072/1200], Temp: 0.0278, Energy: -53.313052+0.000388j
[2025-09-10 21:39:03] [Iter 2124/2250] R3[1073/1200], Temp: 0.0274, Energy: -53.286107+0.002297j
[2025-09-10 21:40:04] [Iter 2125/2250] R3[1074/1200], Temp: 0.0270, Energy: -53.246235+0.000390j
[2025-09-10 21:41:04] [Iter 2126/2250] R3[1075/1200], Temp: 0.0265, Energy: -53.196073+0.000269j
[2025-09-10 21:42:04] [Iter 2127/2250] R3[1076/1200], Temp: 0.0261, Energy: -53.240244+0.001170j
[2025-09-10 21:43:04] [Iter 2128/2250] R3[1077/1200], Temp: 0.0257, Energy: -53.273824+0.000314j
[2025-09-10 21:44:05] [Iter 2129/2250] R3[1078/1200], Temp: 0.0253, Energy: -53.297382-0.000793j
[2025-09-10 21:45:05] [Iter 2130/2250] R3[1079/1200], Temp: 0.0249, Energy: -53.236276-0.000574j
[2025-09-10 21:46:06] [Iter 2131/2250] R3[1080/1200], Temp: 0.0245, Energy: -53.245908+0.000419j
[2025-09-10 21:47:06] [Iter 2132/2250] R3[1081/1200], Temp: 0.0241, Energy: -53.299001+0.001354j
[2025-09-10 21:48:06] [Iter 2133/2250] R3[1082/1200], Temp: 0.0237, Energy: -53.285482+0.000183j
[2025-09-10 21:49:07] [Iter 2134/2250] R3[1083/1200], Temp: 0.0233, Energy: -53.285723+0.000226j
[2025-09-10 21:50:07] [Iter 2135/2250] R3[1084/1200], Temp: 0.0229, Energy: -53.276957+0.000362j
[2025-09-10 21:51:07] [Iter 2136/2250] R3[1085/1200], Temp: 0.0225, Energy: -53.260360+0.000100j
[2025-09-10 21:52:08] [Iter 2137/2250] R3[1086/1200], Temp: 0.0221, Energy: -53.295754-0.000654j
[2025-09-10 21:53:08] [Iter 2138/2250] R3[1087/1200], Temp: 0.0217, Energy: -53.244062-0.000764j
[2025-09-10 21:54:08] [Iter 2139/2250] R3[1088/1200], Temp: 0.0213, Energy: -53.226065-0.002006j
[2025-09-10 21:55:09] [Iter 2140/2250] R3[1089/1200], Temp: 0.0210, Energy: -53.296617+0.002556j
[2025-09-10 21:56:09] [Iter 2141/2250] R3[1090/1200], Temp: 0.0206, Energy: -53.316854-0.001088j
[2025-09-10 21:57:10] [Iter 2142/2250] R3[1091/1200], Temp: 0.0202, Energy: -53.223747-0.003285j
[2025-09-10 21:58:10] [Iter 2143/2250] R3[1092/1200], Temp: 0.0199, Energy: -53.258478-0.000778j
[2025-09-10 21:59:10] [Iter 2144/2250] R3[1093/1200], Temp: 0.0195, Energy: -53.323845-0.000073j
[2025-09-10 22:00:11] [Iter 2145/2250] R3[1094/1200], Temp: 0.0191, Energy: -53.286173-0.000360j
[2025-09-10 22:01:11] [Iter 2146/2250] R3[1095/1200], Temp: 0.0188, Energy: -53.267957-0.000019j
[2025-09-10 22:02:12] [Iter 2147/2250] R3[1096/1200], Temp: 0.0184, Energy: -53.238479-0.002046j
[2025-09-10 22:03:12] [Iter 2148/2250] R3[1097/1200], Temp: 0.0181, Energy: -53.304021+0.001276j
[2025-09-10 22:04:12] [Iter 2149/2250] R3[1098/1200], Temp: 0.0177, Energy: -53.294454-0.000850j
[2025-09-10 22:05:12] [Iter 2150/2250] R3[1099/1200], Temp: 0.0174, Energy: -53.308847+0.001055j
[2025-09-10 22:06:13] [Iter 2151/2250] R3[1100/1200], Temp: 0.0170, Energy: -53.270139+0.000156j
[2025-09-10 22:07:13] [Iter 2152/2250] R3[1101/1200], Temp: 0.0167, Energy: -53.250902-0.000921j
[2025-09-10 22:08:13] [Iter 2153/2250] R3[1102/1200], Temp: 0.0164, Energy: -53.219145+0.000941j
[2025-09-10 22:09:14] [Iter 2154/2250] R3[1103/1200], Temp: 0.0160, Energy: -53.276445+0.002074j
[2025-09-10 22:10:14] [Iter 2155/2250] R3[1104/1200], Temp: 0.0157, Energy: -53.238831-0.001240j
[2025-09-10 22:11:15] [Iter 2156/2250] R3[1105/1200], Temp: 0.0154, Energy: -53.327739-0.000278j
[2025-09-10 22:12:15] [Iter 2157/2250] R3[1106/1200], Temp: 0.0151, Energy: -53.316647-0.001620j
[2025-09-10 22:13:15] [Iter 2158/2250] R3[1107/1200], Temp: 0.0147, Energy: -53.331779-0.001191j
[2025-09-10 22:14:16] [Iter 2159/2250] R3[1108/1200], Temp: 0.0144, Energy: -53.284596+0.000550j
[2025-09-10 22:15:16] [Iter 2160/2250] R3[1109/1200], Temp: 0.0141, Energy: -53.297046-0.000133j
[2025-09-10 22:16:17] [Iter 2161/2250] R3[1110/1200], Temp: 0.0138, Energy: -53.320123+0.000122j
[2025-09-10 22:17:17] [Iter 2162/2250] R3[1111/1200], Temp: 0.0135, Energy: -53.265056+0.001283j
[2025-09-10 22:18:17] [Iter 2163/2250] R3[1112/1200], Temp: 0.0132, Energy: -53.258306+0.000548j
[2025-09-10 22:19:18] [Iter 2164/2250] R3[1113/1200], Temp: 0.0129, Energy: -53.334785-0.000128j
[2025-09-10 22:20:18] [Iter 2165/2250] R3[1114/1200], Temp: 0.0126, Energy: -53.284492-0.001069j
[2025-09-10 22:21:18] [Iter 2166/2250] R3[1115/1200], Temp: 0.0123, Energy: -53.290230-0.000125j
[2025-09-10 22:22:19] [Iter 2167/2250] R3[1116/1200], Temp: 0.0120, Energy: -53.298073-0.000621j
[2025-09-10 22:23:19] [Iter 2168/2250] R3[1117/1200], Temp: 0.0118, Energy: -53.294150+0.000684j
[2025-09-10 22:24:19] [Iter 2169/2250] R3[1118/1200], Temp: 0.0115, Energy: -53.291463+0.000519j
[2025-09-10 22:25:19] [Iter 2170/2250] R3[1119/1200], Temp: 0.0112, Energy: -53.289211+0.002023j
[2025-09-10 22:26:20] [Iter 2171/2250] R3[1120/1200], Temp: 0.0109, Energy: -53.267732-0.000029j
[2025-09-10 22:27:20] [Iter 2172/2250] R3[1121/1200], Temp: 0.0107, Energy: -53.287216+0.000280j
[2025-09-10 22:28:20] [Iter 2173/2250] R3[1122/1200], Temp: 0.0104, Energy: -53.296531-0.002370j
[2025-09-10 22:29:20] [Iter 2174/2250] R3[1123/1200], Temp: 0.0101, Energy: -53.277958+0.002714j
[2025-09-10 22:30:21] [Iter 2175/2250] R3[1124/1200], Temp: 0.0099, Energy: -53.309336-0.001938j
[2025-09-10 22:31:21] [Iter 2176/2250] R3[1125/1200], Temp: 0.0096, Energy: -53.288817+0.000107j
[2025-09-10 22:32:21] [Iter 2177/2250] R3[1126/1200], Temp: 0.0094, Energy: -53.301556+0.001676j
[2025-09-10 22:33:22] [Iter 2178/2250] R3[1127/1200], Temp: 0.0091, Energy: -53.281600-0.002074j
[2025-09-10 22:34:22] [Iter 2179/2250] R3[1128/1200], Temp: 0.0089, Energy: -53.268863-0.001630j
[2025-09-10 22:35:23] [Iter 2180/2250] R3[1129/1200], Temp: 0.0086, Energy: -53.308040-0.001037j
[2025-09-10 22:36:23] [Iter 2181/2250] R3[1130/1200], Temp: 0.0084, Energy: -53.330620-0.000876j
[2025-09-10 22:37:23] [Iter 2182/2250] R3[1131/1200], Temp: 0.0081, Energy: -53.318131-0.000807j
[2025-09-10 22:38:24] [Iter 2183/2250] R3[1132/1200], Temp: 0.0079, Energy: -53.229775+0.001018j
[2025-09-10 22:39:24] [Iter 2184/2250] R3[1133/1200], Temp: 0.0077, Energy: -53.271668-0.001108j
[2025-09-10 22:40:25] [Iter 2185/2250] R3[1134/1200], Temp: 0.0074, Energy: -53.330987+0.000978j
[2025-09-10 22:41:25] [Iter 2186/2250] R3[1135/1200], Temp: 0.0072, Energy: -53.325802-0.001699j
[2025-09-10 22:42:25] [Iter 2187/2250] R3[1136/1200], Temp: 0.0070, Energy: -53.216789-0.000540j
[2025-09-10 22:43:26] [Iter 2188/2250] R3[1137/1200], Temp: 0.0068, Energy: -53.275051-0.001014j
[2025-09-10 22:44:26] [Iter 2189/2250] R3[1138/1200], Temp: 0.0066, Energy: -53.313762+0.001358j
[2025-09-10 22:45:26] [Iter 2190/2250] R3[1139/1200], Temp: 0.0064, Energy: -53.375978+0.001253j
[2025-09-10 22:46:26] [Iter 2191/2250] R3[1140/1200], Temp: 0.0062, Energy: -53.274619+0.000142j
[2025-09-10 22:47:27] [Iter 2192/2250] R3[1141/1200], Temp: 0.0060, Energy: -53.336311+0.000227j
[2025-09-10 22:48:27] [Iter 2193/2250] R3[1142/1200], Temp: 0.0058, Energy: -53.284229-0.000201j
[2025-09-10 22:49:27] [Iter 2194/2250] R3[1143/1200], Temp: 0.0056, Energy: -53.278849-0.000942j
[2025-09-10 22:50:28] [Iter 2195/2250] R3[1144/1200], Temp: 0.0054, Energy: -53.282161-0.000235j
[2025-09-10 22:51:28] [Iter 2196/2250] R3[1145/1200], Temp: 0.0052, Energy: -53.286160+0.001953j
[2025-09-10 22:52:28] [Iter 2197/2250] R3[1146/1200], Temp: 0.0050, Energy: -53.334430-0.000030j
[2025-09-10 22:53:29] [Iter 2198/2250] R3[1147/1200], Temp: 0.0048, Energy: -53.339760+0.000121j
[2025-09-10 22:54:29] [Iter 2199/2250] R3[1148/1200], Temp: 0.0046, Energy: -53.335213-0.001082j
[2025-09-10 22:55:29] [Iter 2200/2250] R3[1149/1200], Temp: 0.0045, Energy: -53.288762+0.000178j
[2025-09-10 22:56:30] [Iter 2201/2250] R3[1150/1200], Temp: 0.0043, Energy: -53.258285+0.000774j
[2025-09-10 22:57:30] [Iter 2202/2250] R3[1151/1200], Temp: 0.0041, Energy: -53.286038+0.000578j
[2025-09-10 22:58:31] [Iter 2203/2250] R3[1152/1200], Temp: 0.0039, Energy: -53.283997+0.001024j
[2025-09-10 22:59:31] [Iter 2204/2250] R3[1153/1200], Temp: 0.0038, Energy: -53.290982+0.000922j
[2025-09-10 23:00:31] [Iter 2205/2250] R3[1154/1200], Temp: 0.0036, Energy: -53.306572+0.002415j
[2025-09-10 23:01:32] [Iter 2206/2250] R3[1155/1200], Temp: 0.0035, Energy: -53.341477-0.000932j
[2025-09-10 23:02:32] [Iter 2207/2250] R3[1156/1200], Temp: 0.0033, Energy: -53.312002+0.001017j
[2025-09-10 23:03:32] [Iter 2208/2250] R3[1157/1200], Temp: 0.0032, Energy: -53.283974-0.002176j
[2025-09-10 23:04:33] [Iter 2209/2250] R3[1158/1200], Temp: 0.0030, Energy: -53.355695-0.000253j
[2025-09-10 23:05:33] [Iter 2210/2250] R3[1159/1200], Temp: 0.0029, Energy: -53.304109+0.000028j
[2025-09-10 23:06:34] [Iter 2211/2250] R3[1160/1200], Temp: 0.0027, Energy: -53.286115-0.000638j
[2025-09-10 23:07:34] [Iter 2212/2250] R3[1161/1200], Temp: 0.0026, Energy: -53.297970-0.000021j
[2025-09-10 23:08:34] [Iter 2213/2250] R3[1162/1200], Temp: 0.0025, Energy: -53.222982-0.000917j
[2025-09-10 23:09:35] [Iter 2214/2250] R3[1163/1200], Temp: 0.0023, Energy: -53.259352+0.001172j
[2025-09-10 23:10:35] [Iter 2215/2250] R3[1164/1200], Temp: 0.0022, Energy: -53.272824-0.000560j
[2025-09-10 23:11:35] [Iter 2216/2250] R3[1165/1200], Temp: 0.0021, Energy: -53.308905+0.001984j
[2025-09-10 23:12:36] [Iter 2217/2250] R3[1166/1200], Temp: 0.0020, Energy: -53.301581+0.000853j
[2025-09-10 23:13:36] [Iter 2218/2250] R3[1167/1200], Temp: 0.0019, Energy: -53.245960-0.001006j
[2025-09-10 23:14:36] [Iter 2219/2250] R3[1168/1200], Temp: 0.0018, Energy: -53.257372-0.000159j
[2025-09-10 23:15:37] [Iter 2220/2250] R3[1169/1200], Temp: 0.0016, Energy: -53.345708-0.000100j
[2025-09-10 23:16:37] [Iter 2221/2250] R3[1170/1200], Temp: 0.0015, Energy: -53.342219-0.000526j
[2025-09-10 23:17:37] [Iter 2222/2250] R3[1171/1200], Temp: 0.0014, Energy: -53.259112+0.000881j
[2025-09-10 23:18:38] [Iter 2223/2250] R3[1172/1200], Temp: 0.0013, Energy: -53.278849-0.000664j
[2025-09-10 23:19:38] [Iter 2224/2250] R3[1173/1200], Temp: 0.0012, Energy: -53.297757-0.000469j
[2025-09-10 23:20:38] [Iter 2225/2250] R3[1174/1200], Temp: 0.0012, Energy: -53.301886+0.000033j
[2025-09-10 23:21:39] [Iter 2226/2250] R3[1175/1200], Temp: 0.0011, Energy: -53.267447-0.000769j
[2025-09-10 23:22:39] [Iter 2227/2250] R3[1176/1200], Temp: 0.0010, Energy: -53.239707+0.000173j
[2025-09-10 23:23:39] [Iter 2228/2250] R3[1177/1200], Temp: 0.0009, Energy: -53.311212-0.001703j
[2025-09-10 23:24:40] [Iter 2229/2250] R3[1178/1200], Temp: 0.0008, Energy: -53.308398-0.002559j
[2025-09-10 23:25:40] [Iter 2230/2250] R3[1179/1200], Temp: 0.0008, Energy: -53.293027-0.000026j
[2025-09-10 23:26:40] [Iter 2231/2250] R3[1180/1200], Temp: 0.0007, Energy: -53.284321-0.000058j
[2025-09-10 23:27:40] [Iter 2232/2250] R3[1181/1200], Temp: 0.0006, Energy: -53.269205-0.001420j
[2025-09-10 23:28:41] [Iter 2233/2250] R3[1182/1200], Temp: 0.0006, Energy: -53.304056-0.000698j
[2025-09-10 23:29:41] [Iter 2234/2250] R3[1183/1200], Temp: 0.0005, Energy: -53.345764-0.000856j
[2025-09-10 23:30:41] [Iter 2235/2250] R3[1184/1200], Temp: 0.0004, Energy: -53.283139+0.001052j
[2025-09-10 23:31:42] [Iter 2236/2250] R3[1185/1200], Temp: 0.0004, Energy: -53.230534+0.000391j
[2025-09-10 23:32:42] [Iter 2237/2250] R3[1186/1200], Temp: 0.0003, Energy: -53.244555-0.001452j
[2025-09-10 23:33:42] [Iter 2238/2250] R3[1187/1200], Temp: 0.0003, Energy: -53.346846+0.000327j
[2025-09-10 23:34:43] [Iter 2239/2250] R3[1188/1200], Temp: 0.0002, Energy: -53.352714+0.000051j
[2025-09-10 23:35:43] [Iter 2240/2250] R3[1189/1200], Temp: 0.0002, Energy: -53.255011+0.000364j
[2025-09-10 23:36:44] [Iter 2241/2250] R3[1190/1200], Temp: 0.0002, Energy: -53.297212+0.000301j
[2025-09-10 23:37:44] [Iter 2242/2250] R3[1191/1200], Temp: 0.0001, Energy: -53.270605+0.000660j
[2025-09-10 23:38:44] [Iter 2243/2250] R3[1192/1200], Temp: 0.0001, Energy: -53.334163+0.000947j
[2025-09-10 23:39:45] [Iter 2244/2250] R3[1193/1200], Temp: 0.0001, Energy: -53.393670-0.000580j
[2025-09-10 23:40:45] [Iter 2245/2250] R3[1194/1200], Temp: 0.0001, Energy: -53.402896-0.001477j
[2025-09-10 23:41:46] [Iter 2246/2250] R3[1195/1200], Temp: 0.0000, Energy: -53.289909+0.000242j
[2025-09-10 23:42:46] [Iter 2247/2250] R3[1196/1200], Temp: 0.0000, Energy: -53.257492+0.001043j
[2025-09-10 23:43:46] [Iter 2248/2250] R3[1197/1200], Temp: 0.0000, Energy: -53.216705+0.002535j
[2025-09-10 23:44:47] [Iter 2249/2250] R3[1198/1200], Temp: 0.0000, Energy: -53.219267+0.001422j
[2025-09-10 23:45:47] [Iter 2250/2250] R3[1199/1200], Temp: 0.0000, Energy: -53.255415-0.001786j
[2025-09-10 23:45:47] ✓ Checkpoint saved: checkpoint_iter_002250.pkl
[2025-09-10 23:45:47] ✅ Training completed | Restarts: 3
[2025-09-10 23:45:47] ============================================================
[2025-09-10 23:45:47] Training completed | Runtime: 133730.7s
[2025-09-10 23:46:12] ✓ Final state saved: checkpoints/final_GCNN.pkl
[2025-09-10 23:46:12] ============================================================
