import numpy as np
import netket as nk
import os
import jax
import jax.numpy as jnp
from functools import partial
from jax import vmap
from src.utils.logging import log_message

# =========================================
# 通用工具函数
# =========================================

def create_k_mesh(lattice):
    """
    创建k点网格,根据晶格尺寸设置点数,使用π/L*n的方式生成k点, 范围从-π到π

    参数:
    - lattice: 晶格对象,从中获取Lx和Ly

    返回:
    - k_points_x: x方向的k点
    - k_points_y: y方向的k点
    - kx, ky: 网格化的k点
    """
    # 从晶格获取尺寸
    Lx, Ly = lattice.extent

    # 生成k点,范围为[-π, π],确保包含端点
    k_points_x = np.linspace(-np.pi, np.pi, 2 * Lx + 1, endpoint=True)
    k_points_y = np.linspace(-np.pi, np.pi, 2 * Ly + 1, endpoint=True)
    kx, ky = np.meshgrid(k_points_x, k_points_y)

    return k_points_x, k_points_y, kx, ky

def infer_log_file(save_dir, L):
    """自动推断日志文件名"""
    temp_dir_parts = save_dir.split('/')
    for i, current_dir_part in enumerate(temp_dir_parts):
        if current_dir_part.startswith('J2='):
            if i + 1 < len(temp_dir_parts) and temp_dir_parts[i+1].startswith('J1='):
                temp_J2_str = current_dir_part.split('=')[1]
                temp_J1_str = temp_dir_parts[i+1].split('=')[1]
                return os.path.join(os.path.dirname(save_dir), 
                                  f"analyze_L={L}_J2={temp_J2_str}_J1={temp_J1_str}.log")
    return None

def create_spin_operators(hilbert, n_sites):
    """创建所有位点的自旋操作符"""
    spin_ops_components = []
    for i in range(n_sites):
        sx_i = nk.operator.spin.sigmax(hilbert, i) * 0.5
        sy_i = nk.operator.spin.sigmay(hilbert, i) * 0.5
        sz_i = nk.operator.spin.sigmaz(hilbert, i) * 0.5
        spin_ops_components.append((sx_i.to_jax_operator(), sy_i.to_jax_operator(), sz_i.to_jax_operator()))
    return spin_ops_components

def create_data_structure(correlation_data, k_points_x, k_points_y, 
                         structure_factor_complex, lattice, 
                         calculation_type, reference_info=None):
    """
    创建优化的数据结构
    
    参数:
    - correlation_data: 原始相关函数数据列表
    - k_points_x, k_points_y: k点坐标
    - structure_factor_complex: 复数结构因子
    - lattice: 晶格对象
    - calculation_type: 计算类型 ('spin', 'dimer', 'diag_dimer', 'plaquette')
    - reference_info: 参考点信息
    
    返回:
    - 优化的数据结构字典
    """
    import time
    
    # 提取相关函数数据
    n_pairs = len(correlation_data)
    positions = np.zeros((n_pairs, 2))
    values = np.zeros(n_pairs, dtype=complex)
    errors = np.zeros(n_pairs, dtype=complex)
    variances = np.zeros(n_pairs, dtype=complex)
    pair_indices = np.zeros((n_pairs, 2), dtype=int)
    
    # 根据计算类型提取不同的索引信息
    if calculation_type == 'spin':
        for i, data in enumerate(correlation_data):
            positions[i] = [data['r_x'], data['r_y']]
            values[i] = data['corr_full_real'] + 1j * data['corr_full_imag']
            errors[i] = data['error'] + 1j * data['error_imag']
            variances[i] = data['variance'] + 1j * data['variance_imag']
            pair_indices[i] = [data['i'], data['j']]
    
    elif calculation_type in ['dimer', 'diag_dimer']:
        directions = np.array([data['direction'] for data in correlation_data])
        for i, data in enumerate(correlation_data):
            positions[i] = [data['r_x'], data['r_y']]
            values[i] = data['corr_full_real'] + 1j * data['corr_full_imag']
            errors[i] = data['error'] + 1j * data['error_imag']
            variances[i] = data['variance'] + 1j * data['variance_imag']
            pair_indices[i] = [data['dimer_i_idx'], data['dimer_j_idx']]
    
    elif calculation_type == 'plaquette':
        for i, data in enumerate(correlation_data):
            positions[i] = [data['r_x'], data['r_y']]
            values[i] = data['corr_full_real'] + 1j * data['corr_full_imag']
            errors[i] = data['error'] + 1j * data['error_imag']
            variances[i] = data['variance'] + 1j * data['variance_imag']
            pair_indices[i] = [data['plaq_i_idx'], data['plaq_j_idx']]
    
    # 创建k点网格
    kx_grid, ky_grid = np.meshgrid(k_points_x, k_points_y)
    k_points_grid = np.stack([kx_grid, ky_grid], axis=2)
    
    # 构建优化的数据结构
    optimized_data = {
        'metadata': {
            'lattice_info': {
                'Lx': lattice.extent[0],
                'Ly': lattice.extent[1], 
                'N_sites': lattice.n_nodes
            },
            'k_grid': {
                'kx': k_points_x,
                'ky': k_points_y,
                'shape': structure_factor_complex.shape
            },
            'calculation_type': calculation_type,
            'reference_info': reference_info or {},
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        },
        'correlations': {
            'positions': positions,
            'values': values,
            'errors': errors,
            'variances': variances,
            'pair_indices': pair_indices
        },
        'structure_factor': {
            'values': structure_factor_complex,
            'k_points': k_points_grid,
            'normalization': 1.0 / lattice.n_nodes
        }
    }
    
    # 为二聚体类型添加方向信息
    if calculation_type in ['dimer', 'diag_dimer']:
        optimized_data['correlations']['directions'] = directions
    
    return optimized_data

def save_optimized_data(data, save_path):
    """保存优化的数据结构"""
    np.save(save_path, data)
    
def load_optimized_data(load_path):
    """加载优化的数据结构"""
    return np.load(load_path, allow_pickle=True).item()

# =========================================
# 共享采样优化模块
# =========================================

def generate_shared_samples(vqs, n_samples=None, log_file=None):
    """
    在量子态加载后立即生成一次共享样本,供后续所有结构因子计算使用
    """
    import time
    
    if n_samples is not None:
        original_n_samples = vqs.n_samples
        vqs.n_samples = n_samples
        log_message(log_file, f"设置样本数为: {n_samples}")
    
    log_message(log_file, f"开始生成共享样本集...")
    sampling_start = time.time()
    vqs.sample()  # 生成样本集
    sampling_time = time.time() - sampling_start
    log_message(log_file, f"样本生成完成,耗时: {sampling_time:.3f} 秒")
    
    return original_n_samples if n_samples is not None else None

def batch_expect_with_existing_samples(vqs, operators, log_file=None):
    """
    使用已有的共享样本批量计算所有算符的期望值,不重新生成样本
    """
    import time
    
    total_operators = len(operators)
    log_message(log_file, f"使用已有共享样本计算 {total_operators} 个算符的期望值...")
    
    # 现在所有算符都将使用相同的样本集进行计算
    log_message(log_file, f"开始计算 {total_operators} 个算符的期望值(使用共享样本)...")
    results = []
    expectation_start = time.time()
    
    for i, operator in enumerate(operators):
        op_start = time.time()
        stats = vqs.expect(operator)
        op_time = time.time() - op_start
        results.append(stats)
        
        # 每个算符都报告进度
        elapsed = time.time() - expectation_start
        avg_time_per_op = elapsed / (i + 1)
        remaining_ops = total_operators - (i + 1)
        eta = remaining_ops * avg_time_per_op
        log_message(log_file, 
            f"算符计算进度: {i+1}/{total_operators} "
            f"({(i+1)/total_operators*100:.1f}%), "
            f"当前算符耗时: {op_time:.3f}s, "
            f"平均耗时: {avg_time_per_op:.3f}s, "
            f"预计剩余时间: {eta:.1f}s")

    total_expectation_time = time.time() - expectation_start
    log_message(log_file, 
        f"所有算符期望值计算完成,总耗时: {total_expectation_time:.3f} 秒, "
        f"平均每算符: {total_expectation_time/total_operators:.3f} 秒")

    return results

class OperatorCache:
    """操作符缓存类,避免重复创建相同的操作符"""
    def __init__(self, hilbert):
        self.hilbert = hilbert
        self._spin_ops = {}
        self._dimer_ops = {}
        self._exchange_ops = {}
        
    def get_spin_operators(self, site_idx):
        """获取指定位点的自旋操作符(缓存版本)"""
        if site_idx not in self._spin_ops:
            sx = nk.operator.spin.sigmax(self.hilbert, site_idx) * 0.5
            sy = nk.operator.spin.sigmay(self.hilbert, site_idx) * 0.5
            sz = nk.operator.spin.sigmaz(self.hilbert, site_idx) * 0.5
            self._spin_ops[site_idx] = (
                sx.to_jax_operator(),
                sy.to_jax_operator(), 
                sz.to_jax_operator()
            )
        return self._spin_ops[site_idx]
    
    def get_dimer_operator(self, site1, site2):
        """获取二聚体操作符 S_i·S_j(缓存版本)"""
        key = tuple(sorted([site1, site2]))
        if key not in self._dimer_ops:
            sx1, sy1, sz1 = self.get_spin_operators(site1)
            sx2, sy2, sz2 = self.get_spin_operators(site2)
            dimer_op = sx1 @ sx2 + sy1 @ sy2 + sz1 @ sz2
            self._dimer_ops[key] = dimer_op
        return self._dimer_ops[key]
    
    def get_exchange_operator(self, site1, site2):
        """获取交换算符 S_{i,j} = 1/2 + 2S_i·S_j(缓存版本)"""
        key = tuple(sorted([site1, site2]))
        if key not in self._exchange_ops:
            dimer_op = self.get_dimer_operator(site1, site2)
            constant_op = nk.operator.LocalOperator(self.hilbert, constant=0.5).to_jax_operator()
            exchange_op = constant_op + 2.0 * dimer_op
            self._exchange_ops[key] = exchange_op
        return self._exchange_ops[key]

def batch_expect_with_shared_samples(vqs, operators, n_samples=None, log_file=None):
    """
    使用NetKet的样本重用机制来批量计算所有算符的期望值
    基于NetKet官方文档:"Notice that if you call multiple times expect, the same set of samples will be used"
    """
    import time
    
    total_operators = len(operators)
    log_message(log_file, f"准备批量计算 {total_operators} 个算符的期望值...")
    
    if n_samples is not None:
        original_n_samples = vqs.n_samples
        vqs.n_samples = n_samples
        log_message(log_file, f"临时设置样本数为: {n_samples}")

    try:
        # 强制生成新的样本集(只需要一次)
        log_message(log_file, f"开始生成共享样本集...")
        sampling_start = time.time()
        vqs.sample()
        sampling_time = time.time() - sampling_start
        log_message(log_file, f"样本生成完成,耗时: {sampling_time:.3f} 秒")

        # 现在所有算符都将使用相同的样本集进行计算
        log_message(log_file, f"开始计算 {total_operators} 个算符的期望值(使用共享样本)...")
        results = []
        expectation_start = time.time()
        
        for i, operator in enumerate(operators):
            op_start = time.time()
            stats = vqs.expect(operator)
            op_time = time.time() - op_start
            results.append(stats)
            
            # 每个算符都报告进度
            elapsed = time.time() - expectation_start
            avg_time_per_op = elapsed / (i + 1)
            remaining_ops = total_operators - (i + 1)
            eta = remaining_ops * avg_time_per_op
            log_message(log_file, 
                f"算符计算进度: {i+1}/{total_operators} "
                f"({(i+1)/total_operators*100:.1f}%), "
                f"当前算符耗时: {op_time:.3f}s, "
                f"平均耗时: {avg_time_per_op:.3f}s, "
                f"预计剩余时间: {eta:.1f}s")

        total_expectation_time = time.time() - expectation_start
        log_message(log_file, 
            f"所有算符期望值计算完成,总耗时: {total_expectation_time:.3f} 秒, "
            f"平均每算符: {total_expectation_time/total_operators:.3f} 秒")

        return results

    finally:
        if n_samples is not None:
            vqs.n_samples = original_n_samples
            log_message(log_file, f"恢复原始样本数: {original_n_samples}")

def compute_structure_factor_fourier(correlation_data, k_points_x, k_points_y, log_file=None):
    """
    通用的傅里叶变换函数计算结构因子
    """
    log_message(log_file, "计算傅里叶变换...")
    
    kx_grid, ky_grid = np.meshgrid(k_points_x, k_points_y)
    n_ky, n_kx = kx_grid.shape
    
    # 提取相关数据用于向量化计算
    r_values = np.array([[data['r_x'], data['r_y']] for data in correlation_data])
    corr_full_values = np.array([data['corr_full_real'] + 1j * data['corr_full_imag'] 
                                for data in correlation_data])

    # 使用已经创建的k网格
    k_grid = np.stack([kx_grid.flatten(), ky_grid.flatten()], axis=1)

    # 将数据转换为JAX数组
    r_values_jax = jnp.array(r_values)
    corr_full_values_jax = jnp.array(corr_full_values)
    k_grid_jax = jnp.array(k_grid)

    # 定义计算单个k点的结构因子的函数
    def compute_sf_for_k(k_vec):
        phases = jnp.exp(1j * jnp.dot(r_values_jax, k_vec))
        return jnp.sum(corr_full_values_jax * phases)

    # 向量化函数以并行计算所有k点
    compute_sf_vmap = vmap(compute_sf_for_k)

    # 并行计算所有k点的结构因子
    sf_values = compute_sf_vmap(k_grid_jax)

    # 将结果重塑为2D网格
    sf_values_2d = sf_values.reshape(n_ky, n_kx)
    
    return np.array(sf_values_2d)

# =========================================
# 自旋结构因子
# =========================================

def calculate_spin_structure_factor(vqs, lattice, L, save_dir, log_file=None):
    """
    计算自旋结构因子,使用共享采样优化版本
    一次生成样本,批量计算所有算符期望值
    """
    if log_file is None:
        log_file = infer_log_file(save_dir, L)

    log_message(log_file, "开始计算自旋结构因子...")

    N = lattice.n_nodes
    k_points_x, k_points_y, kx_grid, ky_grid = create_k_mesh(lattice)

    # 初始化操作符缓存
    log_message(log_file, "初始化操作符缓存...")
    op_cache = OperatorCache(vqs.hilbert)

    # 计算位移向量
    positions = np.array([lattice.positions[i] for i in range(N)])
    r_vectors = np.zeros((N, N, 2))
    for i in range(N):
        r_vectors[i] = positions - positions[i]

    # 预构建所有自旋点积操作符 S_0·S_j
    log_message(log_file, "预构建所有自旋相关操作符...")
    ops_S0_dot_Sj_list = []
    for j in range(N):
        spin_dot_op = op_cache.get_dimer_operator(0, j)
        ops_S0_dot_Sj_list.append(spin_dot_op)

    # 使用已有共享样本逐个计算自旋相关函数
    import time
    start_time = time.time()
    correlation_results = []
    
    log_message(log_file, f"开始计算自旋相关函数...")
    for j in range(N):
        op_start = time.time()
        stats = vqs.expect(ops_S0_dot_Sj_list[j])
        op_time = time.time() - op_start
        correlation_results.append(stats)
        
        # 显示当前算符信息和进度
        log_message(log_file, 
            f"自旋算符进度: {j+1}/{N}, 当前算符: S_0 · S_{j}, 耗时: {op_time:.3f}s")
    
    end_time = time.time()
    log_message(log_file, f"自旋相关函数计算完成,总耗时 {end_time - start_time:.2f} 秒")

    # 处理结果
    correlation_data = []
    for j, corr_obj in enumerate(correlation_results):
        r_0j = r_vectors[0, j]
        corr_mean = corr_obj.mean

        # 处理误差和方差
        if j == 0: 
            error_real = error_imag = variance_real = variance_imag = 0.0
        else:
            error_real = corr_obj.error_of_mean.real
            error_imag = corr_obj.error_of_mean.imag
            variance_real = corr_obj.variance.real
            variance_imag = corr_obj.variance.imag

        correlation_data.append({
            'i': 0, 'j': j,
            'r_x': r_0j[0], 'r_y': r_0j[1],
            'corr_full_real': corr_mean.real,
            'corr_full_imag': corr_mean.imag,
            'error': error_real, 
            'error_imag': error_imag, 
            'variance': variance_real, 
            'variance_imag': variance_imag
        })

    # 计算结构因子
    spin_sf = compute_structure_factor_fourier(correlation_data, k_points_x, k_points_y, log_file)
    spin_sf /= N  # 归一化

    # 保存数据
    optimized_data = create_data_structure(
        correlation_data=correlation_data,
        k_points_x=k_points_x,
        k_points_y=k_points_y,
        structure_factor_complex=spin_sf,
        lattice=lattice,
        calculation_type='spin',
        reference_info={'reference_site': 0}
    )
    save_optimized_data(optimized_data, os.path.join(save_dir, "spin_data.npy"))

    log_message(log_file, "自旋结构因子计算完成")
    return (k_points_x, k_points_y), (spin_sf.real, spin_sf.imag)

# =========================================
# 二聚体结构因子
# =========================================

def identify_dimers(lattice, log_file=None):
    """识别x和y方向的二聚体"""
    dimers_x = []  # x方向的二聚体
    dimers_y = []  # y方向的二聚体
    dimer_positions_x = []  # 存储每个x方向二聚体的中心位置
    dimer_positions_y = []  # 存储每个y方向二聚体的中心位置

    # 预先获取所有边
    edges = list(lattice.edges())

    # 获取晶格尺寸
    Lx, Ly = lattice.extent

    for x_coord in range(Lx):
        for y_coord in range(Ly):
            for unit_idx in range(4):
                site_i = 4 * (y_coord + x_coord * Ly) + unit_idx

                for edge in edges:
                    if edge[0] == site_i or edge[1] == site_i:
                        site_j = edge[1] if edge[0] == site_i else edge[0]
                        pos_i = lattice.positions[site_i]
                        pos_j = lattice.positions[site_j]

                        # x方向二聚体（水平方向）
                        if abs(pos_j[0] - pos_i[0]) > 0 and abs(pos_j[1] - pos_i[1]) < 0.1:
                            if site_i < site_j:  # 避免重复添加
                                dimers_x.append((site_i, site_j))
                                dimer_center = 0.5 * (np.array(pos_i) + np.array(pos_j))
                                dimer_positions_x.append(dimer_center)
                        # y方向二聚体（垂直方向）  
                        elif abs(pos_j[1] - pos_i[1]) > 0 and abs(pos_j[0] - pos_i[0]) < 0.1:
                            if site_i < site_j:  # 避免重复添加
                                dimers_y.append((site_i, site_j))
                                dimer_center = 0.5 * (np.array(pos_i) + np.array(pos_j))
                                dimer_positions_y.append(dimer_center)

    # 记录找到的二聚体
    log_message(log_file, f"找到 {len(dimers_x)} 个x方向二聚体和 {len(dimers_y)} 个y方向二聚体")

    return dimers_x + dimers_y, dimer_positions_x + dimer_positions_y, len(dimers_x), len(dimers_y)

def calculate_dimer_structure_factor(vqs, lattice, L, save_dir, log_file=None):
    """
    计算二聚体-二聚体结构因子,使用共享采样优化版本
    """
    if log_file is None:
        log_file = infer_log_file(save_dir, L)
    
    log_message(log_file, "开始计算二聚体结构因子...")

    N = lattice.n_nodes
    k_points_x, k_points_y, kx_grid, ky_grid = create_k_mesh(lattice)

    # 识别二聚体
    log_message(log_file, "识别x和y方向的二聚体...")
    dimers, dimer_positions, n_dimers_x, n_dimers_y = identify_dimers(lattice, log_file)
    
    if len(dimers) == 0:
        log_message(log_file, "警告: 没有找到二聚体！")
        return (k_points_x, k_points_y), (np.zeros(kx_grid.shape), np.zeros(kx_grid.shape))

    n_dimers = len(dimers)

    # 预计算二聚体操作符
    log_message(log_file, "预计算二聚体操作符...")
    spin_ops_components = create_spin_operators(vqs.hilbert, lattice.n_nodes)

    dimer_ops_list = []
    for i, (i1, i2) in enumerate(dimers):
        sx_i1, sy_i1, sz_i1 = spin_ops_components[i1]
        sx_i2, sy_i2, sz_i2 = spin_ops_components[i2]
        S_i1_dot_S_i2 = sx_i1 @ sx_i2 + sy_i1 @ sy_i2 + sz_i1 @ sz_i2
        dimer_ops_list.append(S_i1_dot_S_i2)

    # 计算位移向量
    dimer_positions_np = np.array(dimer_positions)
    r_vectors = np.zeros((n_dimers, n_dimers, 2))
    for i in range(n_dimers):
        r_vectors[i] = dimer_positions_np - dimer_positions_np[i]

    # 批量构建所有需要计算的操作符
    all_operators = []
    operator_info = []
    
    # x方向二聚体
    if n_dimers_x > 0:
        ref_dimer_idx_x = 0
        op_D_ref_x = dimer_ops_list[ref_dimer_idx_x]
        dimer_ref_tuple_x = dimers[ref_dimer_idx_x]
        for j_local_idx in range(n_dimers_x):
            j_global_idx = j_local_idx
            op_Dj = dimer_ops_list[j_global_idx]
            combined_op = op_D_ref_x @ op_Dj
            all_operators.append(combined_op)
            operator_info.append({
                'type': 'x_dimer',
                'ref_idx': ref_dimer_idx_x,
                'j_idx': j_global_idx,
                'ref_tuple': dimer_ref_tuple_x,
                'j_tuple': dimers[j_global_idx]
            })

    # y方向二聚体
    if n_dimers_y > 0:
        ref_dimer_idx_y = n_dimers_x
        op_D_ref_y = dimer_ops_list[ref_dimer_idx_y]
        dimer_ref_tuple_y = dimers[ref_dimer_idx_y]

        for j_local_idx in range(n_dimers_y):
            j_global_idx = n_dimers_x + j_local_idx
            op_Dj = dimer_ops_list[j_global_idx]
            combined_op = op_D_ref_y @ op_Dj
            all_operators.append(combined_op)
            operator_info.append({
                'type': 'y_dimer',
                'ref_idx': ref_dimer_idx_y,
                'j_idx': j_global_idx,
                'ref_tuple': dimer_ref_tuple_y,
                'j_tuple': dimers[j_global_idx]
            })

    # 分别计算x方向和y方向二聚体
    import time
    correlation_results = []
    
    # 计算x方向二聚体
    if n_dimers_x > 0:
        x_operators = all_operators[:n_dimers_x]
        x_operator_info = operator_info[:n_dimers_x]
        
        log_message(log_file, f"使用x-二聚体 {dimer_ref_tuple_x} (全局索引 {ref_dimer_idx_x}) 作为参考点, 计算x方向连通二聚体相关函数...")
        start_time = time.time()
        
        for i, (operator, info) in enumerate(zip(x_operators, x_operator_info)):
            op_start = time.time()
            stats = vqs.expect(operator)
            op_time = time.time() - op_start
            correlation_results.append(stats)
            
            # 显示当前算符信息和进度
            log_message(log_file, 
                f"x方向算符进度: {i+1}/{n_dimers_x}, 当前算符: D_{info['ref_tuple']} * D_{info['j_tuple']}, 耗时: {op_time:.3f}s")
        
        x_time = time.time() - start_time
        log_message(log_file, f"x方向二聚体相关函数计算完成,耗时: {x_time:.2f} 秒")
        log_message(log_file, "-"*80)
    
    # 计算y方向二聚体
    if n_dimers_y > 0:
        y_operators = all_operators[n_dimers_x:]
        y_operator_info = operator_info[n_dimers_x:]
        
        log_message(log_file, f"使用y-二聚体 {dimer_ref_tuple_y} (全局索引 {ref_dimer_idx_y}) 作为参考点, 计算y方向连通二聚体相关函数...")
        start_time = time.time()
        
        for i, (operator, info) in enumerate(zip(y_operators, y_operator_info)):
            op_start = time.time()
            stats = vqs.expect(operator)
            op_time = time.time() - op_start
            correlation_results.append(stats)
            
            # 显示当前算符信息和进度
            log_message(log_file, 
                f"y方向算符进度: {i+1}/{n_dimers_y}, 当前算符: D_{info['ref_tuple']} * D_{info['j_tuple']}, 耗时: {op_time:.3f}s")
        
        y_time = time.time() - start_time
        log_message(log_file, f"y方向二聚体相关函数计算完成,耗时: {y_time:.2f} 秒")

    # 处理结果
    dimer_correlation_data = []
    for idx, (corr_obj, info) in enumerate(zip(correlation_results, operator_info)):
        r_ref_j = r_vectors[info['ref_idx'], info['j_idx']]
        corr_full = corr_obj.mean
        direction = 'x' if info['type'] == 'x_dimer' else 'y'

        dimer_correlation_data.append({
            'dimer_i_idx': info['ref_idx'],
            'dimer_j_idx': info['j_idx'],
            'dimer_i_sites': info['ref_tuple'],
            'dimer_j_sites': info['j_tuple'],
            'r_x': r_ref_j[0],
            'r_y': r_ref_j[1],
            'corr_full_real': corr_full.real,
            'corr_full_imag': corr_full.imag,
            'error': corr_obj.error_of_mean.real,
            'error_imag': corr_obj.error_of_mean.imag,
            'variance': corr_obj.variance.real,
            'variance_imag': corr_obj.variance.imag,
            'direction': direction
        })

    if not dimer_correlation_data:
        log_message(log_file, "没有足够的数据点进行傅里叶变换。")
        return (k_points_x, k_points_y), (np.zeros(kx_grid.shape), np.zeros(kx_grid.shape))

    # 计算结构因子
    dimer_sf_complex = compute_structure_factor_fourier(dimer_correlation_data, k_points_x, k_points_y, log_file)
    dimer_sf_complex /= N

    # 保存数据
    reference_info = {}
    if n_dimers_x > 0:
        reference_info['x_reference'] = {'dimer_idx': 0, 'sites': dimers[0]}
    if n_dimers_y > 0:
        reference_info['y_reference'] = {'dimer_idx': n_dimers_x, 'sites': dimers[n_dimers_x]}

    optimized_data = create_data_structure(
        correlation_data=dimer_correlation_data,
        k_points_x=k_points_x,
        k_points_y=k_points_y,
        structure_factor_complex=dimer_sf_complex,
        lattice=lattice,
        calculation_type='dimer',
        reference_info=reference_info
    )
    save_optimized_data(optimized_data, os.path.join(save_dir, "dimer_data.npy"))

    log_message(log_file, "二聚体结构因子计算完成")
    return (k_points_x, k_points_y), (dimer_sf_complex.real, dimer_sf_complex.imag)

# =========================================
# 对角二聚体结构因子
# =========================================

def identify_diagonal_dimers(lattice):
    """识别对角二聚体"""
    N = lattice.n_nodes
    Lx, Ly = lattice.extent
    
    diag_dimers_nwse = []  # 西北-东南方向
    diag_dimers_swne = []  # 西南-东北方向
    diag_dimer_positions_nwse = []
    diag_dimer_positions_swne = []

    for i in range(N):
        pos_i = np.array(lattice.positions[i])
        
        for j in range(N):
            if i == j:
                continue
                
            pos_j = np.array(lattice.positions[j])
            displacement = pos_j - pos_i
            
            # 考虑周期性边界条件
            displacement_pbc = displacement.copy()
            if displacement[0] > Lx:
                displacement_pbc[0] -= 2 * Lx
            elif displacement[0] < -Lx:
                displacement_pbc[0] += 2 * Lx
                
            if displacement[1] > Ly:
                displacement_pbc[1] -= 2 * Ly
            elif displacement[1] < -Ly:
                displacement_pbc[1] += 2 * Ly
            
            # 西北-东南方向:[1, -1]
            if (abs(displacement_pbc[0] - 1.0) < 0.1 and 
                abs(displacement_pbc[1] + 1.0) < 0.1):
                if (i, j) not in diag_dimers_nwse and (j, i) not in diag_dimers_nwse:
                    diag_dimers_nwse.append((i, j))
                    dimer_center = 0.5 * (pos_i + pos_j)
                    diag_dimer_positions_nwse.append(dimer_center)
            
            # 西南-东北方向:[1, 1]
            elif (abs(displacement_pbc[0] - 1.0) < 0.1 and 
                  abs(displacement_pbc[1] - 1.0) < 0.1):
                if (i, j) not in diag_dimers_swne and (j, i) not in diag_dimers_swne:
                    diag_dimers_swne.append((i, j))
                    dimer_center = 0.5 * (pos_i + pos_j)
                    diag_dimer_positions_swne.append(dimer_center)

    dimers = diag_dimers_nwse + diag_dimers_swne
    dimer_positions = diag_dimer_positions_nwse + diag_dimer_positions_swne
    
    return dimers, dimer_positions, len(diag_dimers_nwse), len(diag_dimers_swne)

def calculate_diag_dimer_structure_factor(vqs, lattice, L, save_dir, log_file=None):
    """
    计算对角二聚体结构因子,使用共享采样优化版本
    """
    if log_file is None:
        log_file = infer_log_file(save_dir, L)

    log_message(log_file, "开始计算对角二聚体结构因子...")

    N = lattice.n_nodes
    k_points_x, k_points_y, kx_grid, ky_grid = create_k_mesh(lattice)

    # 识别对角二聚体
    log_message(log_file, "识别所有对角二聚体...")
    dimers, dimer_positions, n_dimers_nwse, n_dimers_swne = identify_diagonal_dimers(lattice)
    
    if len(dimers) == 0:
        log_message(log_file, "警告: 没有找到对角二聚体！")
        return (k_points_x, k_points_y), (np.zeros(kx_grid.shape), np.zeros(kx_grid.shape))

    n_dimers = len(dimers)
    log_message(log_file, f"总共找到 {n_dimers_nwse} 个西北-东南方向对角二聚体和 {n_dimers_swne} 个西南-东北方向对角二聚体")

    # 预计算对角二聚体操作符
    log_message(log_file, "预计算对角二聚体操作符...")
    spin_ops_components = create_spin_operators(vqs.hilbert, lattice.n_nodes)

    dimer_ops_list = []
    for i, (i1, i2) in enumerate(dimers):
        sx_i1, sy_i1, sz_i1 = spin_ops_components[i1]
        sx_i2, sy_i2, sz_i2 = spin_ops_components[i2]
        S_i1_dot_S_i2 = sx_i1 @ sx_i2 + sy_i1 @ sy_i2 + sz_i1 @ sz_i2
        dimer_ops_list.append(S_i1_dot_S_i2)

    # 计算位移向量
    dimer_positions_np = np.array(dimer_positions)
    r_vectors = np.zeros((n_dimers, n_dimers, 2))
    for i in range(n_dimers):
        r_vectors[i] = dimer_positions_np - dimer_positions_np[i]

    # 批量构建所有需要计算的操作符
    all_operators = []
    operator_info = []

    # 西北-东南方向对角二聚体
    if n_dimers_nwse > 0:
        ref_dimer_idx_nwse = 0
        op_D_ref_nwse = dimer_ops_list[ref_dimer_idx_nwse]
        dimer_ref_tuple_nwse = dimers[ref_dimer_idx_nwse]

        for j_local_idx in range(n_dimers_nwse):
            j_global_idx = j_local_idx
            op_Dj = dimer_ops_list[j_global_idx]
            combined_op = op_D_ref_nwse @ op_Dj
            all_operators.append(combined_op)
            operator_info.append({
                'type': 'diag_nwse',
                'ref_idx': ref_dimer_idx_nwse,
                'j_idx': j_global_idx,
                'ref_tuple': dimer_ref_tuple_nwse,
                'j_tuple': dimers[j_global_idx]
            })

    # 西南-东北方向对角二聚体
    if n_dimers_swne > 0:
        ref_dimer_idx_swne = n_dimers_nwse
        op_D_ref_swne = dimer_ops_list[ref_dimer_idx_swne]
        dimer_ref_tuple_swne = dimers[ref_dimer_idx_swne]

        for j_local_idx in range(n_dimers_swne):
            j_global_idx = n_dimers_nwse + j_local_idx
            op_Dj = dimer_ops_list[j_global_idx]
            combined_op = op_D_ref_swne @ op_Dj
            all_operators.append(combined_op)
            operator_info.append({
                'type': 'diag_swne',
                'ref_idx': ref_dimer_idx_swne,
                'j_idx': j_global_idx,
                'ref_tuple': dimer_ref_tuple_swne,
                'j_tuple': dimers[j_global_idx]
            })

    # 分别计算不同方向的对角二聚体
    import time
    correlation_results = []
    
    # 计算西北-东南方向对角二聚体
    if n_dimers_nwse > 0:
        nwse_operators = all_operators[:n_dimers_nwse]
        nwse_operator_info = operator_info[:n_dimers_nwse]
        
        log_message(log_file, f"开始计算西北-东南方向对角二聚体相关函数 ({n_dimers_nwse} 个算符)...")
        start_time = time.time()
        
        for i, (operator, info) in enumerate(zip(nwse_operators, nwse_operator_info)):
            op_start = time.time()
            stats = vqs.expect(operator)
            op_time = time.time() - op_start
            correlation_results.append(stats)
            
            # 显示当前算符信息和进度
            log_message(log_file, 
                f"NW-SE对角算符进度: {i+1}/{n_dimers_nwse}, 当前算符: D_{info['ref_tuple']} * D_{info['j_tuple']}, 耗时: {op_time:.3f}s")
        
        nwse_time = time.time() - start_time
        log_message(log_file, f"西北-东南方向对角二聚体相关函数计算完成,耗时: {nwse_time:.2f} 秒")
        log_message(log_file, "================================================================================")
    
    # 计算西南-东北方向对角二聚体
    if n_dimers_swne > 0:
        swne_operators = all_operators[n_dimers_nwse:]
        swne_operator_info = operator_info[n_dimers_nwse:]
        
        log_message(log_file, f"开始计算西南-东北方向对角二聚体相关函数 ({n_dimers_swne} 个算符)...")
        start_time = time.time()
        
        for i, (operator, info) in enumerate(zip(swne_operators, swne_operator_info)):
            op_start = time.time()
            stats = vqs.expect(operator)
            op_time = time.time() - op_start
            correlation_results.append(stats)
            
            # 显示当前算符信息和进度
            log_message(log_file, 
                f"SW-NE对角算符进度: {i+1}/{n_dimers_swne}, 当前算符: D_{info['ref_tuple']} * D_{info['j_tuple']}, 耗时: {op_time:.3f}s")
        
        swne_time = time.time() - start_time
        log_message(log_file, f"西南-东北方向对角二聚体相关函数计算完成,耗时: {swne_time:.2f} 秒")

    # 处理结果
    diag_dimer_correlation_data = []
    for idx, (corr_obj, info) in enumerate(zip(correlation_results, operator_info)):
        r_ref_j = r_vectors[info['ref_idx'], info['j_idx']]
        corr_full = corr_obj.mean
        direction = 'diag_nwse' if info['type'] == 'diag_nwse' else 'diag_swne'

        diag_dimer_correlation_data.append({
            'dimer_i_idx': info['ref_idx'],
            'dimer_j_idx': info['j_idx'],
            'dimer_i_sites': info['ref_tuple'],
            'dimer_j_sites': info['j_tuple'],
            'r_x': r_ref_j[0],
            'r_y': r_ref_j[1],
            'corr_full_real': corr_full.real,
            'corr_full_imag': corr_full.imag,
            'error': corr_obj.error_of_mean.real,
            'error_imag': corr_obj.error_of_mean.imag,
            'variance': corr_obj.variance.real,
            'variance_imag': corr_obj.variance.imag,
            'direction': direction
        })

    if not diag_dimer_correlation_data:
        log_message(log_file, "没有足够的数据点进行傅里叶变换。")
        return (k_points_x, k_points_y), (np.zeros(kx_grid.shape), np.zeros(kx_grid.shape))

    # 计算结构因子
    diag_dimer_sf_complex = compute_structure_factor_fourier(diag_dimer_correlation_data, k_points_x, k_points_y, log_file)
    diag_dimer_sf_complex /= N

    # 保存数据
    reference_info = {}
    if n_dimers_nwse > 0:
        reference_info['nwse_reference'] = {'dimer_idx': 0, 'sites': dimers[0]}
    if n_dimers_swne > 0:
        reference_info['swne_reference'] = {'dimer_idx': n_dimers_nwse, 'sites': dimers[n_dimers_nwse]}

    optimized_data = create_data_structure(
        correlation_data=diag_dimer_correlation_data,
        k_points_x=k_points_x,
        k_points_y=k_points_y,
        structure_factor_complex=diag_dimer_sf_complex,
        lattice=lattice,
        calculation_type='diag_dimer',
        reference_info=reference_info
    )
    save_optimized_data(optimized_data, os.path.join(save_dir, "diag_dimer_data.npy"))

    log_message(log_file, "对角二聚体结构因子计算完成")
    return (k_points_x, k_points_y), (diag_dimer_sf_complex.real, diag_dimer_sf_complex.imag)

# =========================================
# 简盘结构因子
# =========================================

def construct_plaquette_permutation(hilbert, plaq_sites):
    """
    构建简盘循环置换操作符,使用自旋交换算符
    P = S_{1,2} S_{2,3} S_{3,4} 实现循环置换 (1,2,3,4) -> (4,1,2,3)
    P^-1 = S_{1,4} S_{4,3} S_{3,2} 实现逆循环置换 (1,2,3,4) -> (2,3,4,1)
    """
    a, b, c, d = plaq_sites

    # 构建各个位点的自旋算符
    S_a = [nk.operator.spin.sigmax(hilbert, a) * 0.5,
           nk.operator.spin.sigmay(hilbert, a) * 0.5,
           nk.operator.spin.sigmaz(hilbert, a) * 0.5]
    S_b = [nk.operator.spin.sigmax(hilbert, b) * 0.5,
           nk.operator.spin.sigmay(hilbert, b) * 0.5,
           nk.operator.spin.sigmaz(hilbert, b) * 0.5]
    S_c = [nk.operator.spin.sigmax(hilbert, c) * 0.5,
           nk.operator.spin.sigmay(hilbert, c) * 0.5,
           nk.operator.spin.sigmaz(hilbert, c) * 0.5]
    S_d = [nk.operator.spin.sigmax(hilbert, d) * 0.5,
           nk.operator.spin.sigmay(hilbert, d) * 0.5,
           nk.operator.spin.sigmaz(hilbert, d) * 0.5]

    # 转换为JAX操作符
    S_a = [op.to_jax_operator() for op in S_a]
    S_b = [op.to_jax_operator() for op in S_b]
    S_c = [op.to_jax_operator() for op in S_c]
    S_d = [op.to_jax_operator() for op in S_d]

    # 构建交换算符 S_{i,j} = (1/2 + 2S_i·S_j)
    def exchange_op(S_i, S_j):
        SiSj = S_i[0] @ S_j[0] + S_i[1] @ S_j[1] + S_i[2] @ S_j[2]
        constant_op = nk.operator.LocalOperator(hilbert, constant=0.5).to_jax_operator()
        return constant_op + 2.0 * SiSj

    # 构建循环置换
    S_ab = exchange_op(S_a, S_b)
    S_bc = exchange_op(S_b, S_c)
    S_cd = exchange_op(S_c, S_d)
    S_ad = exchange_op(S_a, S_d)
    S_dc = exchange_op(S_d, S_c)
    S_cb = exchange_op(S_c, S_b)

    P = S_ab @ S_bc @ S_cd
    P_inv = S_ad @ S_dc @ S_cb

    return P, P_inv

def identify_plaquettes(lattice):
    """识别所有4节点简盘"""
    plaquettes = []
    plaquette_positions = []
    
    N = lattice.n_nodes
    positions = [np.array(lattice.positions[i]) for i in range(N)]
    Lx, Ly = lattice.extent
    
    # 每个格点作为简盘的左下角,寻找右边、上边、右上角的格点
    for i in range(N):
        pos_i = positions[i]
        
        # 寻找邻居(考虑周期性边界条件)
        def find_neighbor(ref_pos, dx_target, dy_target):
            for j in range(N):
                if j == i:
                    continue
                pos_j = positions[j]
                
                # 计算位移,考虑周期性边界条件
                dx = pos_j[0] - ref_pos[0]
                dy = pos_j[1] - ref_pos[1]
                
                # 处理周期性边界
                if dx > Lx:
                    dx -= 2 * Lx
                elif dx < -Lx:
                    dx += 2 * Lx
                
                if dy > Ly:
                    dy -= 2 * Ly
                elif dy < -Ly:
                    dy += 2 * Ly
                
                if abs(dx - dx_target) < 0.1 and abs(dy - dy_target) < 0.1:
                    return j
            return None
        
        right_neighbor = find_neighbor(pos_i, 1.0, 0.0)
        up_neighbor = find_neighbor(pos_i, 0.0, 1.0)
        right_up_neighbor = find_neighbor(pos_i, 1.0, 1.0)
        
        # 如果找到了所有三个邻居,就组成一个plaquette
        if (right_neighbor is not None and 
            up_neighbor is not None and 
            right_up_neighbor is not None):
            
            plaq = [i, right_neighbor, right_up_neighbor, up_neighbor]  # 逆时针顺序
            plaquettes.append(plaq)
            
            # 计算plaquette中心位置
            plaq_positions = [positions[node] for node in plaq]
            center_x = np.mean([pos[0] for pos in plaq_positions])
            center_y = np.mean([pos[1] for pos in plaq_positions])
            plaquette_positions.append(np.array([center_x, center_y]))

    return plaquettes, plaquette_positions

def calculate_plaquette_structure_factor(vqs, lattice, L, save_dir, log_file=None):
    """
    计算简盘结构因子,使用共享采样优化版本
    """
    if log_file is None:
        log_file = infer_log_file(save_dir, L)

    log_message(log_file, "开始计算简盘结构因子...")

    k_points_x, k_points_y, kx_grid, ky_grid = create_k_mesh(lattice)

    # 识别所有可能的4节点plaquette
    log_message(log_file, "识别所有可能的4节点简盘...")
    plaquettes, plaquette_positions = identify_plaquettes(lattice)
    
    log_message(log_file, f"找到 {len(plaquettes)} 个有效的4节点简盘")
    
    if len(plaquettes) == 0:
        log_message(log_file, "警告: 没有找到有效的简盘！")
        return (k_points_x, k_points_y), (np.zeros(kx_grid.shape), np.zeros(kx_grid.shape))

    # 预计算简盘操作符
    log_message(log_file, "预计算简盘操作符 P_k 和 P_k^-1...")
    plaquette_P_ops = []
    plaquette_Pinv_ops = []
    
    for i, plaq_sites in enumerate(plaquettes):
        P, P_inv = construct_plaquette_permutation(vqs.hilbert, plaq_sites)
        plaquette_P_ops.append(P)
        plaquette_Pinv_ops.append(P_inv)

    # 计算位移向量
    plaquette_positions_np = np.array(plaquette_positions)
    n_plaq = len(plaquettes)
    r_vectors = np.zeros((n_plaq, n_plaq, 2))
    for i in range(n_plaq):
        r_vectors[i] = plaquette_positions_np - plaquette_positions_np[i]

    reference_plaq_idx = 0
    P_ref = plaquette_P_ops[reference_plaq_idx]
    Pinv_ref = plaquette_Pinv_ops[reference_plaq_idx]
    O_0_ref = P_ref + Pinv_ref

    # 批量构建所有需要计算的操作符
    all_operators = []
    log_message(log_file, "准备简盘相关函数计算...")
    for j_idx in range(n_plaq):
        P_j = plaquette_P_ops[j_idx]
        Pinv_j = plaquette_Pinv_ops[j_idx]
        O_r_j = P_j + Pinv_j
        operator_to_expect = O_r_j @ O_0_ref
        all_operators.append(operator_to_expect)

    # 使用已有共享样本逐个计算简盘相关函数
    import time
    start_time = time.time()
    correlation_results = []
    
    log_message(log_file, f"开始计算简盘相关函数 ({n_plaq} 个算符)...")
    for j_idx in range(n_plaq):
        op_start = time.time()
        stats = vqs.expect(all_operators[j_idx])
        op_time = time.time() - op_start
        correlation_results.append(stats)
        
        # 显示当前算符信息和进度
        log_message(log_file, 
            f"简盘算符进度: {j_idx+1}/{n_plaq}, 当前算符: P_{plaquettes[reference_plaq_idx]} * P_{plaquettes[j_idx]}, "
            f"计算简盘{plaquettes[j_idx]}(spins: {plaquettes[j_idx]})与参考简盘{plaquettes[reference_plaq_idx]}(spins: {plaquettes[reference_plaq_idx]})的相关函数, 耗时: {op_time:.3f}s")
    
    end_time = time.time()
    log_message(log_file, f"简盘相关函数计算完成,总耗时 {end_time - start_time:.2f} 秒")

    # 处理结果
    plaquette_correlation_data = []
    for j_idx, corr_obj in enumerate(correlation_results):
        r_ref_j = r_vectors[reference_plaq_idx, j_idx]
        corr_full_raw_unconnected = corr_obj.mean
        
        # C(r) = (1/4) * <[P_r + P_r^-1][P_0 + P_0^-1]> 
        corr_full_scaled = 0.25 * corr_full_raw_unconnected
        
        # 缩放误差和方差
        error_scaled_real = 0.25 * corr_obj.error_of_mean.real
        error_scaled_imag = 0.25 * corr_obj.error_of_mean.imag
        variance_scaled_real = (0.25**2) * corr_obj.variance.real
        variance_scaled_imag = (0.25**2) * corr_obj.variance.imag

        plaquette_correlation_data.append({
            'plaq_i_idx': reference_plaq_idx, 'plaq_j_idx': j_idx,
            'r_x': r_ref_j[0], 'r_y': r_ref_j[1],
            'corr_full_real': corr_full_scaled.real,
            'corr_full_imag': corr_full_scaled.imag,
            'error': error_scaled_real, 
            'error_imag': error_scaled_imag,
            'variance': variance_scaled_real,
            'variance_imag': variance_scaled_imag
        })

    # 计算结构因子
    plaq_sf_complex = compute_structure_factor_fourier(plaquette_correlation_data, k_points_x, k_points_y, log_file)
    N_sites = lattice.n_nodes
    plaq_sf_complex /= N_sites

    # 保存数据
    reference_info = {
        'reference_plaquette': {
            'plaq_idx': reference_plaq_idx,
            'sites': plaquettes[reference_plaq_idx]
        },
        'total_plaquettes': len(plaquettes)
    }
    
    optimized_data = create_data_structure(
        correlation_data=plaquette_correlation_data,
        k_points_x=k_points_x,
        k_points_y=k_points_y,
        structure_factor_complex=plaq_sf_complex,
        lattice=lattice,
        calculation_type='plaquette',
        reference_info=reference_info
    )
    save_optimized_data(optimized_data, os.path.join(save_dir, "plaquette_data.npy"))

    log_message(log_file, "简盘结构因子计算完成")
    return (k_points_x, k_points_y), (plaq_sf_complex.real, plaq_sf_complex.imag)




