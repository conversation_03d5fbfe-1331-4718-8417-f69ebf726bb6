This chapter presents the numerical results obtained from neural network quantum states (NQS) calculations for the extended Shastry-Sutherland model. Two limiting cases are systematically investigated to validate the computational approach, analyze the effectiveness of parameter transfer learning, and provide quantitative comparison with literature results to establish the reliability of the method.

\section{Selection of Physical System Parameters}

To validate the feasibility of the NQS method for studying the extended Shastry-Sutherland model, two limiting cases were selected that correspond to well-studied physical systems in the literature:

\begin{itemize}
\item \textbf{$J' = 0.0$ limit (Pure J-Q model)}: This corresponds to the two-dimensional Heisenberg model with four-spin interactions, which has been extensively studied for deconfined quantum criticality. The absence of diagonal dimer interactions allows direct comparison with established quantum Monte Carlo and other numerical results.

\item \textbf{$J' = J$ limit (Pure Shastry-Sutherland model)}: This represents the original Shastry-Sutherland model where nearest-neighbor and diagonal dimer interactions have equal strength. This case enables comparison with recent transformer-based neural network studies and provides insights into spin liquid phases.
\end{itemize}

These two limits provide complementary perspectives on quantum frustration: the $J' = 0.0$ case emphasizes quantum fluctuations and criticality, while the $J' = J$ case highlights geometric frustration and unconventional ground states. By establishing the accuracy of the method in these well-characterized regimes, the analysis can be extended to intermediate parameter regions where novel physics may emerge.

The GCNN architecture employs 4 layers with 4 features per layer, resulting in approximately 12,572 trainable parameters for the 4×4×4 system. This architecture provides a balance between computational efficiency and representation power for capturing the complex many-body correlations in frustrated quantum spin systems.

\section{Initial Training Results and Their Limitations}

Before implementing the parameter transfer learning strategy, neural network quantum states calculations exhibited significant instabilities when training each parameter point independently from random initialization. This approach failed to exploit the continuous evolution of quantum ground states across parameter space, leading to several critical issues:

\begin{itemize}
\item \textbf{Energy fluctuations}: Although energy appeared to converge within 1000 iterations, significant instabilities emerged before the convergence phase.

\item \textbf{Inconsistent order parameter values}: Large variations in calculated order parameters, resulting in non-physical discontinuities across parameter space.

\item \textbf{Poor parameter-space continuity}: Independent training at each parameter point failed to preserve the natural smoothness expected from quantum ground state evolution.
\end{itemize}

Figure \ref{fig:energy_before_ft} demonstrates these problems through energy convergence analysis before implementing parameter transfer learning. While apparent convergence is achieved, the irregular fluctuations before the convergence phase reveal the limitations of independent training.

\begin{figure}[H]
    \centering
    \begin{subfigure}[b]{0.48\textwidth}
        \centering
        \includegraphics[width=\textwidth]{energy_analysis_L4_J2_1.00_before.png}
        \caption{Energy analysis for $L=4$, $J'=J$ system}
    \end{subfigure}
    \hfill
    \begin{subfigure}[b]{0.48\textwidth}
        \centering
        \includegraphics[width=\textwidth]{energy_analysis_L5_J2_1.00_before.png}
        \caption{Energy analysis for $L=5$, $J'=J$ system}
    \end{subfigure}
    \caption{Energy convergence analysis without parameter transfer learning for $J'=J$ systems. The top panels show energy evolution during training, the middle panels reveal the stable phase, and the bottom panels display scattered energy-parameter relationships. The results show a seemingly good energy convergence.}
    \label{fig:energy_before_ft}
\end{figure}

 

\section{Parameter Transfer Learning Methodology}

The fundamental insight behind the parameter transfer learning strategy is that quantum ground states evolve continuously across parameter space, and this physical continuity should be exploited in the training process. The methodology addresses the limitations of independent training through the following systematic approach:

\subsection{Core Transfer Learning Strategy}

\begin{itemize}
\item \textbf{Seed point initialization}: We select a central parameter point in each $J$ range and train the neural network quantum state to full convergence. This establishes a baseline wave function approximation that captures the essential many-body correlations of the system.

\item \textbf{Bidirectional parameter evolution}: From the converged seed point, we systematically transfer learned parameters to neighboring $J$ values in both directions. Each new parameter point uses the converged model from the previous point as initialization, creating a smooth evolutionary path across parameter space.

\item \textbf{Adaptive fine-tuning}: At each new parameter point, we perform targeted fine-tuning that preserves the learned wave function structure while adapting to the modified Hamiltonian. This approach requires significantly fewer iterations than training from random initialization.

\item \textbf{Continuity preservation}: The transfer learning process maintains the natural smoothness of quantum ground state evolution, eliminating artificial discontinuities that arise from independent training approaches.
\end{itemize}

\subsection{Physical Motivation}

The effectiveness of parameter transfer learning stems from fundamental quantum mechanical principles:

\begin{itemize}
\item \textbf{Adiabatic evolution}: As Hamiltonian parameters change slowly, the ground state evolves continuously without sudden transitions (except at true critical points). This ensures that the wave function at parameter $J$ provides a starting approximation for nearby parameters $J \pm \delta J$.

\item \textbf{Correlation structure preservation}: The underlying many-body correlation patterns evolve smoothly across parameter space. Transfer learning preserves these learned correlations while adapting to parameter changes, avoiding the need to rediscover fundamental structures at each point.

\item \textbf{Optimization landscape}: Starting from a physically relevant initial condition simplifies the optimization landscape, reducing the risk of converging to spurious local minima that can occur with random initialization.
\end{itemize}

This methodology addresses the irregularities observed previously, transforming chaotic, non-physical behavior into smooth, continuous evolution that reflects the underlying quantum mechanics.

\subsection{Statistical Analysis and Error Control}

To ensure the reliability of results, we implemented systematic error control and finite-size analysis:

\begin{itemize}
\item \textbf{Statistical ensemble}: Each parameter point uses 10 independent training checkpoints for robust statistical analysis and error estimation. These multiple independent runs provide the statistical basis for error bars in all subsequent analyses.

\item \textbf{Convergence validation}: Quality control identifies and excludes insufficiently converged training runs, ensuring reliable order parameter calculations.

\item \textbf{Finite-size scaling}: Systematic differences between $L=4$ and $L=5$ systems follow expected finite-size scaling behavior, with smaller systems showing systematically higher order parameters due to reduced quantum fluctuations.
\end{itemize}

\section{Improved Results with Parameter Transfer Learning}

\subsection{Energy Convergence Analysis}

The implementation of parameter transfer learning improved the quality and reliability of calculations. Figure \ref{fig:energy_analysis_after_ft} demonstrates the enhanced energy convergence behavior achieved through the transfer learning approach for both limiting cases.

\begin{figure}[H]
    \centering
    \begin{subfigure}[b]{0.48\textwidth}
        \centering
        \includegraphics[width=\textwidth]{energy_analysis_L4_J2_0.00.png}
        \caption{Energy analysis for $L=4$, $J'=0$ system}
    \end{subfigure}
    \hfill
    \begin{subfigure}[b]{0.48\textwidth}
        \centering
        \includegraphics[width=\textwidth]{energy_analysis_L4_J2_1.00.png}
        \caption{Energy analysis for $L=4$, $J'=J$ system}
    \end{subfigure}

    \begin{subfigure}[b]{0.48\textwidth}
        \centering
        \includegraphics[width=\textwidth]{energy_analysis_L5_J2_0.00.png}
        \caption{Energy analysis for $L=5$, $J'=0$ system}
    \end{subfigure}
    \hfill
    \begin{subfigure}[b]{0.48\textwidth}
        \centering
        \includegraphics[width=\textwidth]{energy_analysis_L5_J2_1.00.png}
        \caption{Energy analysis for $L=5$, $J'=J$ system}
    \end{subfigure}
    \caption{Energy convergence analysis after implementing parameter transfer learning. The top panels show smooth energy evolution during training, the middle panels demonstrate stable post-convergence behavior, and the bottom panels reveal linear relationships between average energy and $J$ parameter with high-quality fitting ($R^2 \approx 0.9999$).}
    \label{fig:energy_analysis_after_ft}
\end{figure}

\subsection{Comparison with Literature Results}

\subsubsection{J-Q Model Validation ($J' = 0.0$)}

For the $J' = 0.0$ limit, the system corresponds to the two-dimensional Heisenberg model with four-spin interactions studied in the context of deconfined quantum criticality\cite{sandvik2007evidence}. The antiferromagnetic order parameter is calculated as:
\begin{align}
M_{\text{AF}}^2 
= S(\pi,\pi) 
= \frac{1}{N}\sum_{\mathbf r} e^{i\pi r_x + i\pi r_y} \, \langle \vec{S}_0 \cdot \vec{S}_{\mathbf r} \rangle.
\end{align}

Figure \ref{fig:jq_model_comparison} presents a comparison between the GCNN-based results and established quantum Monte Carlo studies for the J-Q model.

\begin{figure}[H]
    \centering
    \begin{subfigure}[b]{0.48\textwidth}
        \centering
        \includegraphics[width=\textwidth]{af_order_parameter_vs_J1_J2_0.00.png}
        \caption{GCNN results: AF order parameter vs $J$}
        \label{fig:jq_our_af}
    \end{subfigure}
    \hfill
    \begin{subfigure}[b]{0.48\textwidth}
        \centering
        \includegraphics[width=\textwidth]{paperJQ.png}
        \caption{Literature QMC results: finite-size scaling}
        \label{fig:jq_literature}
    \end{subfigure}
    \caption{J-Q Model validation through comparison with quantum Monte Carlo studies. Left: Antiferromagnetic order parameter calculations show smooth, monotonic evolution with $J$ for both $L=4$ and $L=5$ systems. Right: Literature finite-size scaling results demonstrating the expected scaling behavior for deconfined quantum criticality. The quantitative agreement validates the parameter transfer learning methodology and confirms the accuracy of the GCNN approach in capturing critical quantum fluctuations.}
    \label{fig:jq_model_comparison}
\end{figure}

The comparison reveals quantitative agreement between the method and established quantum Monte Carlo results:

\begin{itemize}
\item \textbf{Quantitative agreement}: Within the statistical error range, for the cases of $J=0$ or $J=0.1$, the antiferromagnetic order parameters for $L=4$ or $L=5$ are consistent with the literature.

\item \textbf{Consistent parameter dependence}: Both approaches show the characteristic increase of antiferromagnetic order with increasing $J$, reflecting the enhancement of magnetic correlations in the J-Q model.

\item \textbf{Finite-size scaling agreement}: Results for $L=4$ and $L=5$ systems exhibit the expected finite-size scaling behavior, with smaller systems showing systematically higher order parameters due to reduced quantum fluctuations.

\item \textbf{Statistical reliability}: The smooth curves achieved through parameter transfer learning demonstrate improved statistical control compared to independent training approaches, enabling reliable phase diagram analysis.
\end{itemize}

\subsubsection{Shastry-Sutherland Model Validation ($J' = J$)}

For the $J' = J$ limit, comparison is made with recent transformer-based neural network calculations that investigated spin liquid phases in the Shastry-Sutherland model\cite{viteritti2024transformer}. The higher order parameter values in this regime reflect the suppression of quantum fluctuations by diagonal dimer interactions, consistent with the understanding of geometric frustration effects.

Figure \ref{fig:ss_model_comparison} presents a comparison between the results and transformer-based calculations for both antiferromagnetic order parameters and magnetization behavior.

\begin{figure}[H]
    \centering
    \begin{subfigure}[b]{0.48\textwidth}
        \centering
        \includegraphics[width=\textwidth]{af_order_parameter_vs_J1_J2_1.00.png}
        \caption{GCNN results: AF order parameter vs $J$}
        \label{fig:ss_our_af}
    \end{subfigure}
    \hfill
    \begin{subfigure}[b]{0.48\textwidth}
        \centering
        \includegraphics[width=\textwidth]{paperSS.png}
        \caption{Literature transformer results: system-size dependence}
        \label{fig:ss_literature_mag}
    \end{subfigure}
    \caption{Shastry-Sutherland Model antiferromagnetic order parameter validation. Left: Results showing strong monotonic increase with $J$, reflecting enhanced magnetic ordering in the presence of diagonal dimer interactions. Right: Transformer-based calculations demonstrating system-size dependence and parameter evolution.}
    \label{fig:ss_model_comparison}
\end{figure}


The detailed comparison reveals agreement between the GCNN-based approach and transformer-based calculations:

\begin{itemize}
\item \textbf{Order parameters agreement}: Within the statistical error range, for the cases of $J=0.80$, $J=0.82$ or $J=0.84$, the antiferromagnetic order parameters for $L=4$ or $L=5$ are consistent with the literature.

\item \textbf{Parameter dependence agreement}: Both approaches show similar monotonic increase of the antiferromagnetic order parameter with increasing $J$, indicating strengthening of magnetic correlations in the Shastry-Sutherland model.

\item \textbf{Finite-size scaling validation}: The systematic differences between different system sizes are consistent across both methodologies, with smaller systems generally showing higher order parameters due to reduced quantum fluctuations.

\item \textbf{Smooth evolution confirmation}: The smooth parameter dependence achieved through the transfer learning approach matches the results from transformer calculations, confirming that the methodology captures the underlying quantum mechanical evolution.

\item \textbf{Architecture independence}: The agreement between GCNN and transformer architectures demonstrates that the physical results are robust to the specific neural network implementation, validating the approach of neural network quantum states for frustrated systems.
\end{itemize}

\subsubsection{Quantitative Agreement and Method Validation}

The comprehensive comparison with established literature demonstrates several key validation points that confirm the reliability of the neural network quantum states approach:

\begin{itemize}
\item \textbf{J-Q model consistency}: The antiferromagnetic order parameter calculations show quantitative agreement with quantum Monte Carlo results from studies of deconfined quantum criticality. The smooth monotonic evolution and correct finite-size scaling behavior validate the GCNN architecture's ability to capture critical quantum fluctuations without sign problems.

\item \textbf{Shastry-Sutherland model agreement}: The comparison with transformer-based neural network studies reveals consistency in the antiferromagnetic order parameters. Results fall within the ranges reported in literature, demonstrating that different neural network architectures converge to the same physical results.

\item \textbf{Methodological robustness}: The agreement between GCNN and transformer approaches confirms that the physical conclusions are architecture-independent and reflect genuine quantum mechanical properties rather than computational artifacts.

\item \textbf{Parameter transfer learning validation}: The smooth, continuous evolution of order parameters achieved through the transfer learning approach matches the results from independent studies, confirming that this methodology preserves physical continuity while improving computational efficiency.

\item \textbf{Finite-size scaling accuracy}: Both limiting cases exhibit the expected finite-size scaling behavior, with systematic differences between $L=4$ and $L=5$ systems that are consistent with literature results and theoretical expectations.

\item \textbf{Sign-problem-free advantages}: Unlike quantum Monte Carlo approaches that face sign problems in frustrated systems, the method provides stable, high-precision results across the entire parameter space, including strongly frustrated regions where traditional methods encounter difficulties.
\end{itemize}

These validation results establish a foundation for extending the investigation to intermediate parameter regions ($0 < J' < J$) where novel quantum phases and exotic phase transitions may emerge. The demonstrated accuracy in both limiting cases provides confidence that the methodology can reliably map the complete phase diagram of the extended Shastry-Sutherland model.


\section{Conclusions}

This chapter demonstrates the application of neural network quantum states to the extended Shastry-Sutherland model through systematic investigation of two limiting cases and the development of a parameter transfer learning methodology.

\subsection{Key Achievements}

\begin{itemize}
\item \textbf{Method validation}: Established the reliability of the NQS approach by achieving quantitative agreement with literature results for both the J-Q model ($J'=0.0$) and pure Shastry-Sutherland model ($J'=J$) limits.

\item \textbf{Transfer learning development}: Developed a parameter transfer learning strategy that transforms chaotic, non-physical training results into smooth, continuous evolution of order parameters, addressing the challenge of training instability.

\item \textbf{Physical insight}: Revealed the systematic evolution of antiferromagnetic order across both limiting cases, with the $J'=J$ regime showing stronger magnetic order due to suppressed quantum fluctuations from diagonal dimer interactions.

\item \textbf{Computational progress}: Demonstrated that GCNN-based NQS methods can provide sign-problem-free, high-precision calculations across strongly frustrated parameter regions where traditional approaches face difficulties.
\end{itemize}

The systematic validation of the methodology in these well-characterized limiting cases establishes a foundation for extending the investigation to intermediate parameter regions where novel quantum phases and deconfined quantum criticality may emerge. The smooth parameter evolution achieved through transfer learning opens possibilities for detailed phase diagram analysis and the exploration of unconventional quantum states in frustrated magnetic systems.