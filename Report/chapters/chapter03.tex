This chapter presents the computational methodology employed to study the extended Shastry-Sutherland model. We begin with an overview of the sign problem that plagues conventional quantum Monte Carlo methods in frustrated systems, then introduce variational Monte Carlo techniques and neural network quantum states as a solution. We detail the specific neural network architectures used and describe the training optimization strategies that enable high-precision ground state calculations.

\section{The Sign Problem and Variational Monte Carlo}

\subsection{Quantum Monte Carlo and the Sign Problem}

Quantum Monte Carlo (QMC) methods are among the most powerful numerical techniques for solving quantum many-body systems. These methods are based on statistical sampling of the possible configurations of a system to calculate expectation values of physical observables. For a quantum many-body system, the ground state energy can be calculated using the variational principle:
\begin{align}
    E_0 \leq E_V[\Psi_T] = \frac{\langle\Psi_T|\hat{H}|\Psi_T\rangle}{\langle\Psi_T|\Psi_T\rangle}
\end{align}
where $\Psi_T$ is a trial wave function. For a system of $N$ spin-1/2 particles, we can represent this wave function in the computational basis $\{|S\rangle\}$, where $|S\rangle = |S_1, S_2, \ldots, S_N\rangle$ and $S_i = \pm 1$ represents spin up or down.

The energy expectation value can be rewritten as a sum over all configurations:
\begin{align}
    E_V[\Psi_T] = \frac{\sum_S \Psi_T^*(S) \langle S|\hat{H}|\Psi_T\rangle}{\sum_S |\Psi_T(S)|^2}
\end{align}
We can define the \textbf{local energy} for a configuration $S$ as:
\begin{align}
    E_L(S) = \frac{\langle S|\hat{H}|\Psi_T\rangle}{\Psi_T(S)}
\end{align}
If $\Psi_T(S)$ is real and non-negative for all $S$, we can interpret $P(S) = \frac{\Psi_T(S)^2}{\sum_{S'} \Psi_T(S')^2}$ as a probability distribution. The energy expectation value then becomes a classical statistical average:
\begin{align}
    E_V[\Psi_T] = \sum_S P(S) E_L(S)
\end{align}
This allows us to use Monte Carlo methods for importance sampling:
\begin{enumerate}
    \item Generate configuration samples $\{S_1, S_2, \ldots, S_M\}$ according to the probability distribution $P(S)$.
    \item Calculate the local energies $E_L(S_i)$ for these samples.
    \item Estimate the energy expectation value: $E_V[\Psi_T] \approx \frac{1}{M}\sum_{i=1}^M E_L(S_i)$.
\end{enumerate}

However, for many systems of interest, such as frustrated quantum systems or fermionic systems, the ground-state wave function must have amplitudes $\Psi_T(S)$ that are complex or take negative values. This prevents $P(S)$ from being interpreted as a probability distribution and leads to the infamous \textbf{sign problem}.

To see this mathematically, let's rewrite the expectation value by sampling from the distribution of the amplitudes' magnitudes, $P(S) = \frac{|\Psi_T(S)|^2}{\sum_{S'} |\Psi_T(S')|^2}$. The energy becomes:
\begin{align}
    E_V[\Psi_T] = \frac{\sum_S |\Psi_T(S)|^2 \frac{\Psi_T^*(S)}{\Psi_T(S)} \frac{\langle S|\hat{H}|\Psi_T\rangle}{\Psi_T(S)}}{\sum_S |\Psi_T(S)|^2} = \sum_S P(S) \left[ \frac{\Psi_T^*(S)}{\Psi_T(S)} E_L(S) \right]
\end{align}
The term $\sigma(S) = \frac{\Psi_T^*(S)}{\Psi_T(S)}$ is the "sign" or complex phase of the contribution from configuration $S$. The expectation value is now an average of a new observable, $\sigma(S) E_L(S)$, over the positive definite distribution $P(S)$.

The Monte Carlo estimate becomes:
\begin{align}
    E_V[\Psi_T] = \frac{\langle \sigma(S) E_L(S) \rangle_{P(S)}}{\langle \sigma(S) \rangle_{P(S)}}
\end{align}
where the numerator is $\frac{1}{M}\sum_{i=1}^M \sigma(S_i) E_L(S_i)$ and the denominator is the average sign, $\langle \sigma \rangle = \frac{1}{M}\sum_{i=1}^M \sigma(S_i)$.

The sign problem arises because the average sign $\langle \sigma \rangle$ can be very close to zero. This happens when positive and negative (or complex phase) contributions from different configurations nearly cancel each other out. The statistical error of the energy estimate is inversely proportional to $\langle \sigma \rangle$. As the system size ($N$) or inverse temperature ($\beta$) increases, the average sign often decays exponentially:
\begin{align}
    \langle \sigma \rangle \propto e^{-\alpha N}
\end{align}
This means the number of samples $M$ required to achieve a given accuracy grows exponentially, rendering conventional QMC methods intractable for large systems with a severe sign problem. This necessitates alternative approaches, such as fixed-node/fixed-phase QMC or developing new methods to mitigate the sign problem.


\subsection{Variational Monte Carlo Approach}

Variational Monte Carlo (VMC) provides a framework for circumventing the sign problem by using variational wave functions that are designed to be positive definite. The key insight is to parametrize the trial wave function $\Psi_T(\mathbf{S}; \boldsymbol{\theta})$ with a set of variational parameters $\boldsymbol{\theta}$ and optimize these parameters to minimize the energy:

\begin{align}
\boldsymbol{\theta}^* = \arg\min_{\boldsymbol{\theta}} E[\Psi_T(\mathbf{S}; \boldsymbol{\theta})]
\end{align}

The optimization is typically performed using gradient-based methods, where the gradient of the energy with respect to the parameters is computed using the identity:

\begin{align}
\frac{\partial E}{\partial \theta_i} = 2\text{Re}\left[\langle E_L \frac{\partial \ln \Psi_T}{\partial \theta_i}\rangle - \langle E_L\rangle \left\langle \frac{\partial \ln \Psi_T}{\partial \theta_i}\right\rangle\right]
\end{align}

This approach transforms the quantum many-body problem into an optimization problem in parameter space, which can be solved using modern machine learning techniques.

\section{Neural Network Quantum States}

\subsection{Basic Principles}

Neural Network Quantum States (NQS), introduced by Carleo and Troyer \cite{carleo2017solving}, represent a revolutionary approach to quantum many-body problems. The key idea is to use artificial neural networks as universal function approximators to represent quantum wave functions:

\begin{align}
\Psi_T(\mathbf{S}) = \mathcal{N}(\mathbf{S}; \boldsymbol{\theta})
\end{align}

where $\mathcal{N}(\mathbf{S}; \boldsymbol{\theta})$ is a neural network that takes a spin configuration $\mathbf{S}$ as input and outputs the corresponding wave function amplitude.

The advantages of NQS include:

\begin{itemize}
\item \textbf{Universal Approximation}: Neural networks can, in principle, approximate any continuous function to arbitrary precision with sufficient parameters.
\item \textbf{Scalability}: Modern deep learning frameworks enable efficient training of networks with millions of parameters.
\item \textbf{Symmetry Incorporation}: Network architectures can be designed to respect the symmetries of the physical system.
\item \textbf{Sign Problem Mitigation}: By construction, NQS can represent complex wave functions while maintaining positive definite sampling probabilities.
\end{itemize}

\subsection{Graph Convolutional Neural Networks for Quantum States}

For the Shastry-Sutherland model, we employ Graph Convolutional Neural Networks (GCNN) as our primary NQS architecture. GCNNs are particularly well-suited for quantum spin systems because they naturally incorporate the lattice structure and local interactions.

The GCNN architecture consists of several key components:

\begin{enumerate}
\item \textbf{Input Layer}: The spin configuration $\mathbf{S} = \{S_1, S_2, \ldots, S_N\}$ is represented as node features on the lattice graph.

\item \textbf{Graph Convolutional Layers}: Each layer performs message passing between neighboring spins:
\begin{align}
h_i^{(l+1)} = \sigma\left(W^{(l)} h_i^{(l)} + \sum_{j \in \mathcal{N}(i)} U^{(l)} h_j^{(l)}\right)
\end{align}
where $h_i^{(l)}$ is the feature vector of spin $i$ at layer $l$, $\mathcal{N}(i)$ denotes the neighbors of spin $i$, and $W^{(l)}$, $U^{(l)}$ are learnable weight matrices.

\item \textbf{Symmetry Enforcement}: The network architecture is designed to respect the lattice symmetries including four-fold rotations and reflections through equivariant layers and symmetric aggregation operations, ensuring that the wave function transforms correctly under the symmetry group of the Shastry-Sutherland lattice.

\item \textbf{Output Layer}: The final layer produces a complex-valued output representing the wave function amplitude:
\begin{align}
\Psi_T(\mathbf{S}) = \exp\left(\sum_i \alpha_i h_i^{(L)} + i\sum_i \beta_i h_i^{(L)}\right)
\end{align}
where $\alpha_i$ and $\beta_i$ are learnable parameters controlling the magnitude and phase of the wave function.
\end{enumerate}

\subsection{Transformer-Based Architectures}

In addition to GCNNs, we explore transformer-based architectures for quantum states, motivated by their success in capturing long-range correlations in natural language processing and computer vision. The transformer architecture for quantum states includes:

\begin{enumerate}
\item \textbf{Embedding Layer}: Spin configurations are mapped to high-dimensional feature vectors through learned embeddings.

\item \textbf{Multi-Head Self-Attention}: The attention mechanism allows each spin to interact with all other spins, potentially capturing long-range quantum correlations:
\begin{align}
\text{Attention}(Q, K, V) = \text{softmax}\left(\frac{QK^T}{\sqrt{d_k}}\right)V
\end{align}
where $Q$, $K$, and $V$ are query, key, and value matrices derived from the input features.

\item \textbf{Position Encoding}: Spatial information is incorporated through learned positional encodings that respect the lattice structure.

\item \textbf{Feed-Forward Networks}: Each attention layer is followed by position-wise feed-forward networks for additional non-linear processing.
\end{enumerate}

Recent work by Viteritti et al. \cite{viteritti2024transformer} has demonstrated the effectiveness of transformer architectures for frustrated magnetic systems, particularly in the Shastry-Sutherland model. Their results show that transformer-based neural networks can capture quantum spin liquid phases with high accuracy and provide evidence for the emergence of a spin-liquid phase in the intermediate coupling regime. The transformer architecture's ability to capture long-range correlations through self-attention mechanisms makes it particularly suitable for studying critical phases and quantum spin liquids.

\section{Training Optimization Strategies}

\subsection{Cosine Annealing Learning Rate Schedule}

Effective training of NQS requires careful optimization strategies. We employ a cosine annealing learning rate schedule that periodically reduces and restores the learning rate:

\begin{align}
\eta(t) = \eta_{\min} + \frac{1}{2}(\eta_{\max} - \eta_{\min})\left(1 + \cos\left(\frac{T_{\text{cur}}}{T_{\max}}\pi\right)\right)
\end{align}

where $\eta_{\max}$ and $\eta_{\min}$ are the maximum and minimum learning rates, $T_{\text{cur}}$ is the current iteration within a cycle, and $T_{\max}$ is the cycle length.

This schedule provides several benefits:
\begin{itemize}
\item \textbf{Exploration}: High learning rates enable exploration of the parameter space
\item \textbf{Refinement}: Low learning rates allow fine-tuning of solutions
\item \textbf{Escape from Local Minima}: Periodic increases in learning rate help escape poor local minima
\item \textbf{Multiple Solutions}: Different cycles can converge to different local minima, providing ensemble estimates
\end{itemize}

\subsection{Fine-Tuning and Checkpointing}

Our training protocol includes several stages:

\begin{enumerate}
\item \textbf{Initial Training}: Train the network for a specified number of iterations using the cosine annealing schedule.

\item \textbf{Checkpointing}: Save network parameters at regular intervals to enable analysis of convergence and selection of optimal solutions.

\item \textbf{Fine-Tuning}: Select the best checkpoint based on energy criteria and perform additional training with reduced learning rates for final optimization.

\item \textbf{Ensemble Analysis}: Compare results from multiple training runs to assess statistical reliability and identify systematic errors.
\end{enumerate}

\subsection{Gradient Clipping and Regularization}

To ensure stable training, we implement several regularization techniques:

\begin{itemize}
\item \textbf{Gradient Clipping}: Limit the magnitude of gradients to prevent explosive updates:
\begin{align}
\mathbf{g} \leftarrow \mathbf{g} \cdot \min\left(1, \frac{\tau}{|\mathbf{g}|}\right)
\end{align}
where $\tau$ is the clipping threshold.

\item \textbf{Diagonal Shift}: Add a small diagonal term to the Fisher information matrix to improve numerical stability in natural gradient optimization.

\item \textbf{Parameter Initialization}: Use careful initialization schemes that respect the symmetries of the problem and promote stable training dynamics.
\end{itemize}

\section{Implementation Details}

\subsection{NetKet Framework}

Our implementation is based on the NetKet library \cite{netket2022}, a comprehensive framework for neural network quantum states. NetKet provides:

\begin{itemize}
\item Efficient implementations of various neural network architectures
\item Optimized sampling algorithms for quantum systems
\item Built-in support for symmetries and conservation laws
\item Integration with JAX for high-performance automatic differentiation
\end{itemize}

\subsection{Computational Resources and Scaling}

The calculations are performed on high-performance computing clusters equipped with modern GPU accelerators. Key computational parameters include:

\begin{itemize}
\item \textbf{System Sizes}: Various lattice configurations ranging from small clusters to extended systems
\item \textbf{Sampling}: 16,384 to 1,048,576 Monte Carlo samples per iteration depending on system size and required precision
\item \textbf{Network Parameters}: 10,000 to 100,000 parameters depending on architecture and system complexity
\end{itemize}

\subsection{Methodological Considerations and Limitations}

While neural network quantum states provide a powerful framework for studying frustrated quantum systems, several important limitations must be acknowledged:

\begin{itemize}
\item \textbf{Finite-Size Effects}: All calculations are performed on finite lattices, requiring careful extrapolation to the thermodynamic limit and analysis of finite-size scaling behavior.

\item \textbf{Variational Bias}: The choice of neural network architecture introduces variational bias, potentially missing important physical correlations not captured by the chosen ansatz.

\item \textbf{Optimization Challenges}: The non-convex optimization landscape can lead to convergence to local minima, requiring multiple independent runs and careful analysis of solution stability.

\item \textbf{Computational Scaling}: While more efficient than conventional QMC for sign-problem systems, the computational cost still scales significantly with system size and required precision.
\end{itemize}

The methodology presented in this chapter enables systematic investigation of the extended Shastry-Sutherland model across the proposed phase diagram, providing access to parameter regimes that were previously inaccessible due to the sign problem. The following chapter presents the results obtained using these computational techniques.